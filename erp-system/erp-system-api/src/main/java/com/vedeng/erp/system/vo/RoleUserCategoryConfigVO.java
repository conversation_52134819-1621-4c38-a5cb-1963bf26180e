package com.vedeng.erp.system.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 人员与商品分类配置VO
 * <AUTHOR>
 */
@Data
public class RoleUserCategoryConfigVO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 人员用户ID
     */
    private Integer userId;

    /**
     * 人员用户名称
     */
    private String userName;

    /**
     * 人员头像URL
     */
    private String userAvatar;

    /**
     * 人员部门列表
     * 通过接口获取，不存储在数据库
     */
    private List<Department> userDepartments;

    /**
     * 人员职位列表
     * 通过接口获取，不存储在数据库
     */
    private List<Position> userPositions;

    /**
     * 人员在职状态（0-在职，1-离职）
     * 通过接口获取，不存储在数据库
     */
    private Integer userStatus;

    /**
     * 人员在职状态文本
     */
    private String userStatusText;

    /**
     * 商品分类ID
     */
    private Integer categoryId;

    /**
     * 商品分类名称
     */
    private String categoryName;

    /**
     * 商品分类IDS
     */
    private String categoryIds;

    private Integer categoryCount;


    /**
     * 部门内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Department {
        private Integer departmentId;
        private String departmentName;
    }
    
    /**
     * 职位内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Position {
        private Integer positionId;
        private String positionName;
    }
} 