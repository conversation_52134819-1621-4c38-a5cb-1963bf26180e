<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>商品搜索</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="skuSearch" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="radio">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline ">
     <label class="layui-form-label">A</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="union" name="SKU_NAME,MATERIAL_CODE,SKU_NO,MODEL,SPEC,REGISTRATION_NUMBER" placeholder="可搜索订货号/商品名称/制造商型号/注册证号/规格/物料编号" style="" alias="" jdbctype="VARCHAR" data="" datatype="" oper=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">商品档位</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="GOODS_POSITION_NO" placeholder="" style="" alias="A" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;无档位&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;独家产品&quot;,&quot;K&quot;:&quot;5&quot;},{&quot;V&quot;:&quot;代理产品&quot;,&quot;K&quot;:&quot;4&quot;},{&quot;V&quot;:&quot;经销产品&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;代销产品&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;未签约&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper="="></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">注册证号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="30" name="REGISTRATION_NUMBER" placeholder="" style="" alias="D" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">规格</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="SPEC" placeholder="" style="" alias="A" jdbctype="VARCHAR" data="" datatype="" oper="LIKE">
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">制造商型号</label>
     <div class="layui-input-inline"><input class=" layui-input list-search-item " type="text" name="MODEL" placeholder="" style="" alias="A" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></input>
     </div>
    </div>

    <div class=" layui-inline ">
     <label class="layui-form-label">订货号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="30" name="SKU_NO" placeholder="请输入订货号" style="" alias="A" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">物料编号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="MATERIAL_CODE" placeholder="" style="" alias="A" jdbctype="VARCHAR" data="" datatype="" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">商品等级</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="GOODS_LEVEL_NO" placeholder="" style="" alias="A" jdbctype="NUMBER" data="select LEVEL_NAME AS `V`,ID AS `K` FROM V_GOODS_LEVEL" datatype="KVSQLCACHE" oper="="></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">商品名称</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="hidden" name="SKU_NAME" placeholder="" style="" alias="A" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">品牌</label>
     <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="BRAND_ID" placeholder="" style="" alias="E" jdbctype="NUMBER" data="SELECT BRAND_ID AS `K`,BRAND_NAME AS `V`  FROM T_BRAND WHERE COMPANY_ID=1 AND IS_DELETE=0" datatype="KVSQLCACHE" oper="="></select>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">单位</label>
     <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="BASE_UNIT_ID" placeholder="" style="" alias="A" jdbctype="NUMBER" data="select UNIT_ID K,UNIT_NAME V from T_UNIT   WHERE IS_DEL=0 " datatype="KVSQLCACHE" oper="=">
     </select></div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">贝登分类</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="search-category" name="BASE_CATEGORY_ID" placeholder="" style="" alias="CB" jdbctype="NUMBER" data="" datatype="" oper="in"></object>
     </div>
    </div>

   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton">
       <button item_name="SKU,SKU_NAME,ASSMA" item_id="SKU-SKU_NAME-ASSMA"  url="ez_callback" opentype="" windowname="" style type="tableselectradio">选择</button>
    </div>
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column">
      <th type="rowbutton" id="rowbutton">

      </th>
      <th item_name="SKU" name="SKU" body="td-text" order="1" url="" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">订货号</th>
      <th item_name="SKU_NAME" name="SKU_NAME" body="td-text"   windowname="" datatype="" data="" style="min-width:300px" head="18">商品名称</th>
      <th item_name="BRAND_NAME" name="BRAND_NAME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="min-width:150px" head="18">品牌名称</th>

      <th item_name="GOODS_LEVEL_NO" name="GOODS_LEVEL_NO" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select LEVEL_NAME AS `V`,ID AS `K` FROM V_GOODS_LEVEL" style="" head="18">商品等级</th>
      <th item_name="GOODS_POSITION_NO" name="GOODS_POSITION_NO" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;无档位&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;独家产品&quot;,&quot;K&quot;:&quot;5&quot;},{&quot;V&quot;:&quot;代理产品&quot;,&quot;K&quot;:&quot;4&quot;},{&quot;V&quot;:&quot;经销产品&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;代销产品&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;未签约&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" head="18">商品档位</th>
      <th item_name="SPU_TYPE" name="SPU_TYPE" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;设备&quot;,&quot;K&quot;:316},{&quot;V&quot;:&quot;耗材&quot;,&quot;K&quot;:317},{&quot;V&quot;:&quot;试剂&quot;,&quot;K&quot;:318},{&quot;V&quot;:&quot;配件&quot;,&quot;K&quot;:1008}]" style="" head="18">商品类型</th>
      <th item_name="STOCK_NUM" name="STOCK_NUM" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">库存量</th>
      <th item_name="UNIT_NAME" name="UNIT_NAME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">单位</th>
      <th item_name="ASSMA" name="ASSMA" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">归属人</th>
        <th item_name="CHECK_STATUS" name="CHECK_STATUS" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待完善&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;3&quot;}]" style="" head="18">审核状态</th>
      </tr>
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="ORDER BY SPU_TYPE ASC, SALE_NUM DESC, A.AVAILABLE_STOCK_NUM DESC, CHECK_PRICE DESC" groupby="">String sql="SELECT A.SPEC,
       A.MODEL,
       A.MATERIAL_CODE,
       A.SKU_ID                                                                                     ID,
       A.SKU_ID                                                                                     SKU_ID,
       A.SPU_ID,
       (CASE B.SPU_TYPE WHEN 0 THEN 2002 WHEN 319 THEN 2000 WHEN 653 THEN 2001 ELSE B.SPU_TYPE END) SPU_TYPE,
       A.TERMINAL_PRICE,
       A.DISTRIBUTION_PRICE,
       IF(A.TERMINAL_PRICE &gt; 0, 1, 0)                                                               CHECK_PRICE,
       A.SKU_NO                                                                                     SKU,
       A.ONE_YEAR_SALE_NUM                 as                                                       SALE_NUM,
       A.AVAILABLE_STOCK_NUM,
       A.STOCK_NUM,
       IF(AVAILABLE_STOCK_NUM &gt; 0, 1, 0)                                                            AVAILABLE_STOCK,
       A.SKU_NO                            AS                                                       'STOCK_BATCH',
       A.SKU_NO                            AS                                                       'STOCK_BATCH1',
       C.FIRST_ENGAGE_ID,
       A.SKU_NAME,
       E.BRAND_NAME,
       D.REGISTRATION_NUMBER,

       IFNULL(A.AVGPRICE, '-')             AS                                                       'AVGPRICE',
       CONCAT(F.USERNAME, '&amp;', G.USERNAME) AS                                                       'ASSNAME',
       SALE.USERNAME,
       A.CHECK_STATUS * 1                                                                           CHECK_STATUS,
       CONCAT(F.USERNAME ,'&', G.USERNAME  )                       AS       'ASSMA',
       CB.BASE_CATEGORY_ID                                                                          'C3',
       DB.BASE_CATEGORY_ID                                                                          'C2',
       EB.BASE_CATEGORY_ID                                                                          'C1',
       CONCAT(EB.BASE_CATEGORY_NAME, '/', DB.BASE_CATEGORY_NAME, '/', CB.BASE_CATEGORY_NAME)        catename,
       UU.UNIT_NAME,
       A.TAX_CATEGORY_NO,
       B.BRAND_ID,
       LV.LEVEL_NAME,
       POS.POSITION_NAME,
       A.GOODS_LEVEL_NO,
       A.GOODS_POSITION_NO,
      A.PURCHASE_TIME,A.BASE_UNIT_ID
FROM V_CORE_SKU A
         LEFT JOIN V_GOODS_LEVEL LV ON A.GOODS_LEVEL_NO = LV.ID
         LEFT JOIN V_GOODS_POSITION POS ON A.GOODS_POSITION_NO = POS.ID
         LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
         LEFT JOIN T_FIRST_ENGAGE C ON C.FIRST_ENGAGE_ID = B.FIRST_ENGAGE_ID AND C.IS_DELETED=0
         LEFT JOIN T_REGISTRATION_NUMBER D ON C.REGISTRATION_NUMBER_ID = D.REGISTRATION_NUMBER_ID
         LEFT JOIN T_BRAND E ON B.BRAND_ID = E.BRAND_ID
         LEFT JOIN T_USER F ON B.ASSIGNMENT_MANAGER_ID = F.USER_ID
         LEFT JOIN T_USER G ON B.ASSIGNMENT_ASSISTANT_ID = G.USER_ID
         LEFT JOIN V_BASE_CATEGORY CB ON B.CATEGORY_ID = CB.BASE_CATEGORY_ID and CB.IS_DELETED=0
         LEFT JOIN V_BASE_CATEGORY DB ON CB.PARENT_ID = DB.BASE_CATEGORY_ID AND DB.IS_DELETED=0
         LEFT JOIN V_BASE_CATEGORY EB ON DB.PARENT_ID = EB.BASE_CATEGORY_ID AND EB.IS_DELETED=0
         LEFT JOIN T_USER SALE ON SALE.USER_ID = A.LATEST_VALID_ORDER_USER
         LEFT JOIN T_UNIT UU ON UU.UNIT_ID = A.BASE_UNIT_ID

where A.STATUS = 1
  AND B.STATUS = 1";


list=search(sql);

return list;</pre> count: <pre id="count" class="layui-code">SELECT count(1)
FROM V_CORE_SKU A
         LEFT JOIN V_GOODS_LEVEL LV ON A.GOODS_LEVEL_NO = LV.ID
         LEFT JOIN V_GOODS_POSITION POS ON A.GOODS_POSITION_NO = POS.ID
         LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
         LEFT JOIN T_FIRST_ENGAGE C ON C.FIRST_ENGAGE_ID = B.FIRST_ENGAGE_ID
         LEFT JOIN T_REGISTRATION_NUMBER D ON C.REGISTRATION_NUMBER_ID = D.REGISTRATION_NUMBER_ID
         LEFT JOIN T_BRAND E ON B.BRAND_ID = E.BRAND_ID
         LEFT JOIN T_USER F ON B.ASSIGNMENT_MANAGER_ID = F.USER_ID
         LEFT JOIN T_USER G ON B.ASSIGNMENT_ASSISTANT_ID = G.USER_ID
         LEFT JOIN V_BASE_CATEGORY CB ON B.CATEGORY_ID = CB.BASE_CATEGORY_ID
         LEFT JOIN V_BASE_CATEGORY DB ON CB.PARENT_ID = DB.BASE_CATEGORY_ID
         LEFT JOIN V_BASE_CATEGORY EB ON DB.PARENT_ID = EB.BASE_CATEGORY_ID
         LEFT JOIN T_USER SALE ON SALE.USER_ID = A.LATEST_VALID_ORDER_USER
         LEFT JOIN T_UNIT UU ON UU.UNIT_ID = A.BASE_UNIT_ID
where A.STATUS = 1
  AND B.STATUS = 1</pre></td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot">

  </div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>