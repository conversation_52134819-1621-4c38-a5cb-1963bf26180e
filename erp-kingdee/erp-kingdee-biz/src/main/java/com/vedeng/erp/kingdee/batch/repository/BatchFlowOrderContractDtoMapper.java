package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderContractDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 流转单合同Mapper
 */
public interface BatchFlowOrderContractDtoMapper {


    List<BatchFlowOrderContractDto> queryFlowOrderBuyContractFile(@Param("validTimeBegin") Long validTimeBegin,
                                                                  @Param("validTimeEnd") Long validTimeEnd);

    List<BatchFlowOrderContractDto> queryFlowOrderSaleContractFile(@Param("validTimeBegin") Long validTimeBegin,
                                                                   @Param("validTimeEnd") Long validTimeEnd);

}
