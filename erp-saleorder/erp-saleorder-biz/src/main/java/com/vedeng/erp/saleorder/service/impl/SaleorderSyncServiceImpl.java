package com.vedeng.erp.saleorder.service.impl;

import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.erp.saleorder.api.SaleorderSyncService;
import com.vedeng.erp.saleorder.enums.PCOrderStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleorderSyncEnum;
import com.vedeng.erp.system.common.constants.ThirdRequestLogConstant;
import com.vedeng.erp.system.dto.ThirdRequestLogDto;
import com.vedeng.erp.system.service.ThirdRequestLogApiService;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.OrderData;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.trader.model.WebAccount;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.vedeng.common.constant.OrderConstant.ORDER_TYPE_BD;

/**
 * @Author: daniel
 * @Date: 2021/12/17 13 50
 * @Description:
 */
@Service("saleorderSyncService")
public class SaleorderSyncServiceImpl implements SaleorderSyncService {

    private static final Logger logger = LoggerFactory.getLogger(SaleorderSyncServiceImpl.class);

    @Value("${mjx_url}")
    private String mjxUrl;
    //最底层service 不要再引入service
    private String PC_ORDER_SYNC_URL = "/order/updateOrderConfirmFinish";
    private String UPDATEORDERCONFIRMFINISH = "UPDATEORDERCONFIRMFINISH";
    @Autowired
    private WebAccountMapper webAccountMapper;

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Autowired
    private RedisUtils redisUtils;

//    @Value("${jd.upload.tradeId}")
//    private Integer jdTraderId;

    @Value("${jd.upload.tradeIdList:194723}")
    private String jdTraderIds;

    @Autowired
    private ThirdRequestLogApiService thirdRequestLogApiService;

    /**
     * @param saleorderId 订单id
     * @param syncStatus  同步的订单状态
     * @param biz         业务触发点
     * @date ******** 重构代码，调用统一到一个地方，同时加锁
     */
    @Override
    public void syncSaleorderStatus2Mjx(Integer saleorderId, PCOrderStatusEnum syncStatus, SaleorderSyncEnum biz) {
        long start = System.currentTimeMillis();
        String key = UPDATEORDERCONFIRMFINISH + saleorderId;
        boolean lock = redisUtils.tryGetDistributedLock(key, UUID.randomUUID().toString(), 5000);
        try {
            Saleorder saleorder = saleorderMapper.getSaleOrderBaseInfo(saleorderId);

            String[] idsArray = jdTraderIds.split(",");
            List<Integer> traderIdList = Arrays.stream(idsArray)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            if (saleorder == null || syncStatus == null || traderIdList.contains(saleorder.getTraderId())) {
                logger.info("syncSaleorderStatus2Mjx {} saleorder == null||syncStatus==null", saleorderId);
                return;
            }

            if (!ORDER_TYPE_BD.equals(saleorder.getOrderType()) && !saleorder.virtualBdOrder() && !lock) {
                logger.info("syncSaleorderStatus2Mjx {} order: {} lock: {} ", saleorderId, JsonUtils.convertObjectToJsonStr(saleorder), lock);
                return;
            }
            logger.info("syncSaleorderStatus2Mjx {} start ：{} {} {}", saleorder.getSaleorderId(), JsonUtils.convertObjectToJsonStr(saleorder), syncStatus, biz);
            String webAccountMobile = null;
            if (saleorder.getOrderType() == 0) {
                webAccountMobile = saleorder.getTraderContactMobile();
            } else if (saleorder.getOrderType() == 1) {
                webAccountMobile = saleorder.getCreateMobile();
            }
            if (StringUtils.isEmpty(webAccountMobile)) {
                logger.info("syncSaleorderStatus2Mjx {} StringUtils.isEmpty(webAccountMobile) ", saleorderId);
                return;
            }
            WebAccount webAccount = webAccountMapper.getWenAccountInfoByMobile(webAccountMobile);
            if (webAccount == null) {
                logger.info("syncSaleorderStatus2Mjx {} webAccount==null", saleorderId);
                return;
            }
            OrderData orderData = new OrderData();
            orderData.setTraderId(saleorder.getTraderId());
            orderData.setOrderNo(saleorder.getSaleorderNo());
            orderData.setCompanyId(saleorder.getCompanyId());
            orderData.setAccountId(webAccount.getWebAccountId());
            orderData.setSsoAccountId(webAccount.getSsoAccountId());
            orderData.setOrderStatus(syncStatus.status());
            if (SaleorderSyncEnum.CANCEL_BD_TIMEOUT.equals(biz)) {
                orderData.setCancelType(OrderConstant.CANCEL_BD_TIMEOUT);
            }
            if (SaleorderSyncEnum.CANCEL_BD_HAND.equals(biz)) {
                orderData.setCancelType(OrderConstant.CANCEL_BD_HAND);
            }

            String url = mjxUrl + PC_ORDER_SYNC_URL + "?biz=" + biz.name();
            String json = JsonUtils.translateToJson(orderData);
            logger.info("syncSaleorderStatus2Mjx {}  ::request {}", saleorderId, json);
            JSONObject result = NewHttpClientUtils.httpPost(url, json);
            logger.info("syncSaleorderStatus2Mjx {} ::response {}", saleorderId, result);

            // NewHttpClientUtils.httpPost 的返回值如果为null，则表示调用远程接口的返回值不为200或者请求发生异常,或者返回体不为success
            if (Objects.isNull(result) || !result.getBoolean(ThirdRequestLogConstant.RESPONSE_SUCCESS)) {
                saveRequestFailLog(url, json, saleorder.getSaleorderNo());
            }
        } catch (Exception e) {
            logger.error("syncSaleorderStatus2Mjx {} error", saleorderId, e);
        } finally {
            String value = redisUtils.get(key);
            redisUtils.releaseDistributedLock(key, value);
            logger.info("syncSaleorderStatus2Mjx {} ::time {}", saleorderId, System.currentTimeMillis() - start);
        }
    }

    /**
     * 调用失败记录日志
     *
     * @param url  请求url
     * @param json 请求入参
     */
    private void saveRequestFailLog(String url, String json, String businessNo) {
        ThirdRequestLogDto thirdRequestLogDto = new ThirdRequestLogDto();
        thirdRequestLogDto.setServerName(ThirdRequestLogConstant.SERVER_NAME_MJX);
        thirdRequestLogDto.setErpMethodName(ThirdRequestLogConstant.ERP_METHOD_NAME_1);
        thirdRequestLogDto.setUrl(url);
        thirdRequestLogDto.setRequestType(ThirdRequestLogConstant.REQUEST_TYPE_POST);
        thirdRequestLogDto.setRequestParam(json);
        thirdRequestLogDto.setResponse(ThirdRequestLogConstant.RESPONSE_FAIL);
        thirdRequestLogDto.setAddTime(new Date());
        thirdRequestLogDto.setBusinessNo(businessNo);
        thirdRequestLogApiService.saveLog(thirdRequestLogDto);
        logger.error("第三方请求失败，日志已记录。订单号:{},请求报文:{}", businessNo, com.alibaba.fastjson.JSONObject.toJSONString(thirdRequestLogDto));
    }

}
