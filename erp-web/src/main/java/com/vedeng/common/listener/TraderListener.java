package com.vedeng.common.listener;

import com.alibaba.fastjson.JSON;
import com.google.common.eventbus.Subscribe;
import com.vedeng.common.core.config.IObserver;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.listenerEvent.TraderBillPeriodEvent;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodOverdueManageDetailTypeEnum;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.order.service.OrderAccountPeriodService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户相关业务监听器
 * @date 2023/10/20 16:56
 */
@Component
@Slf4j
public class TraderListener implements IObserver {


    @Autowired
    OrderAccountPeriodService orderAccountPeriodService;


    /**
     * 订单生成账期逾期编码
     *
     * @param traderBillPeriodEvent TraderBillPeriodEvent
     */
    @Subscribe
    @Transactional(rollbackFor = Throwable.class)
    public void openBlueInvoiceCreateBillPeriod(TraderBillPeriodEvent traderBillPeriodEvent) {
        log.info("销售订单开票处理订单生成账期逾期编码 -> {}", JSON.toJSONString(traderBillPeriodEvent));

        try {
            orderAccountPeriodService.dealCustomerBillPeriodManagement(
                    traderBillPeriodEvent.getSaleOrderId(),
                    CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_INVOICE.getCode(),
                    traderBillPeriodEvent.getInvoiceId(),
                    traderBillPeriodEvent.getAmount());
        } catch (CustomerBillPeriodException e) {
            log.error("销售订单开票处理订单生成账期逾期编码 error", e);
            throw new ServiceException("销售订单开票处理订单生成账期逾期编码异常," + e.getMessage());
        }

    }
}
