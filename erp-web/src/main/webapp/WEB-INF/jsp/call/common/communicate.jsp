<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<style>
	.container{
		/*display: flex;*/

	}
	#com-content{
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
<div class="content-item">
	<div class="title">近期沟通</div>
	<table class="table table-td-border1">
		<tbody>
			<tr class="table-header">
				<td style="width:80px;">时间</td>
				<td style="width:280px;">沟通内容</td>
				<td style="width: 80px;">人员</td>
			</tr>
			<c:choose>
				<c:when test="${not empty traderCustomer.communicateRecordList }">
					<c:forEach items="${traderCustomer.communicateRecordList }" var="comm">
					<tr>
						<td  ><date:date value ="${comm.addTime} "/></td>
						<td data="${comm.communicateRecordId}">
							<div class="container">
								<ul class="communicatecontent ml0">
									<c:if test="${not empty comm.tag }">
										<c:forEach items="${comm.tag}" var="tg" varStatus="loop">
                                            <c:if test="${loop.index < 4}">
											    <li class="bluetag" title="${tg.tagName}">${tg.tagName}</li>
                                            </c:if>
										</c:forEach>
                                        <c:if test="${fn:length(comm.tag) >4}">
                                            <li class="bluetag">...</li>
                                        </c:if>
									</c:if>
								</ul>
								<div id="com-content" style="float: left" title="${comm.contentSuffix}">${comm.contentSuffix}</div>
							</div>
						</td>
						<td>${comm.creatorName }</td>
					</tr>
					</c:forEach>
				</c:when>
				<c:otherwise>
				<tr>
					<td colspan="3">暂无记录</td>
				</tr>
				</c:otherwise>
			</c:choose>
		</tbody>
	</table>

	<c:if test="${improveLabels}">
	    <c:if test="${isClick}">
		<div class="tcenter mb15 mt-5">
			<a class="title-click nobor addtitle" tabTitle='{"link":"/trader/customer/editbaseinfo.do?traderCustomerId=${traderCustomer.traderCustomerId}","title":"完善客户标签"}'>完善客户标签</a>

		</div>
		</c:if>
	</c:if>

</div>

