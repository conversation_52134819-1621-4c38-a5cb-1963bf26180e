package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;

/**
 * KING_DEE_SALES_VAT_SPECIAL_INVOICE
 * <AUTHOR>
@Getter
@Setter
@Table(name = "KING_DEE_SALES_VAT_SPECIAL_INVOICE")
public class KingDeeSalesVatSpecialInvoiceEntity extends BaseEntity {
    /**
     * id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 单据内码
     */
    private String fid;

    /**
     * 单据号
     */
    private String fBillNo;

    /**
     * 业务日期
     */
    private String fdate;

    /**
     * 贝登单据头ID
     */
    private String fQzokBddjtid;

    /**
     * 发票号
     */
    private String finvoiceno;

    /**
     * 发票日期
     */
    private String finvoicedate;

    /**
     * 发票代码
     */
    private String fQzokFpdm;

    /**
     * 开票方式
     */
    private String fbillingway;

    /**
     * 客户
     */
    private String fcustomerid;

    /**
     * 单据状态
     */
    private String fdocumentstatus;

    /**
     * 发票类型
     */
    private String fBillTypeId;

    /**
     * 结算组织
     */
    private String fsettleorgid;

    /**
     * 作废状态
     */
    private String fCancelStatus;

    /**
     * 按含税方式
     */
    @Column(name = "FISTAX")
    private String fIsTax;

    /**
     * 红蓝字标识
     */
    @Column(name = "FRedBlue")
    private Integer fRedBlue;

    /**
     * FSALESICENTRY
     */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fsalesicentry;

}