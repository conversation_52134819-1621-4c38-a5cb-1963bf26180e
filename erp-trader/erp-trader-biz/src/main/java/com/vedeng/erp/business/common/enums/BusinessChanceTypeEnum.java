package com.vedeng.erp.business.common.enums;

import lombok.Getter;

@Getter
public enum BusinessChanceTypeEnum {

    SMALL(391,"总机"),

    BIG(392,"销售"),

    SYNTHESIS(394,"自有商城"),
    
    AED(1933,"贝登微信"),

    EMERGENCY(4110,"线索转商机"),

    VISIT(6012,"拜访"),
    ;
    
    
    
    private final Integer code;


    private final String title;

    BusinessChanceTypeEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }


    public static String getTitleByCode(Integer code) {
        for (BusinessChanceTypeEnum businessChanceLevelEnum : BusinessChanceTypeEnum.values()) {
            if (businessChanceLevelEnum.getCode().equals(code)) {
                return businessChanceLevelEnum.getTitle();
            }
        }
        return "";
    }

}
