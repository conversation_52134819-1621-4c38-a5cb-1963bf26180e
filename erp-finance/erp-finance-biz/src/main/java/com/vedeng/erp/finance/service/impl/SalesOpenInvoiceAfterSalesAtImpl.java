package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.aftersale.dto.AfterSalesWithDetailDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceOpenResponseDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.service.AbstractSalesOpenInvoice;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceApplyService;
import com.vedeng.erp.finance.service.InvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 售后安调开票
 */
@Service
@Slf4j
public class SalesOpenInvoiceAfterSalesAtImpl extends AbstractSalesOpenInvoice {

    @Autowired
    private InvoiceApplyService invoiceApplyService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private AfterSalesApiService afterSalesApiService;

    @Autowired
    private InvoiceApiService invoiceApiService;

    /**
     * 初始化交付信息
     * @param invoiceDto
     */
    @Override
    protected void initHandOverInfo(InvoiceDto invoiceDto) {
        Integer afterSalesId = invoiceDto.getRelatedId();
        // 获取售后单收票人手机号
        AfterSalesWithDetailDto mobile = afterSalesApiService.findAfterSalesDetailByAfterSalesId(afterSalesId);
        log.info("交付手机号：{}",mobile);
        invoiceDto.setInvoiceTraderContactMobile(mobile.getInvoiceTraderContactMobile());
        invoiceDto.setInvoiceTraderContactName(mobile.getInvoiceTraderContactName());
        // 微信通知人
        InvoiceApplyDto invoiceApply = invoiceApplyService.getInvoiceApply(invoiceDto.getInvoiceApplyId());
        invoiceDto.setWxAppUserId(CollUtil.newArrayList(invoiceApply.getCreator()));
        // 邮箱交付信息
        InvoiceDto invoiceByApply = invoiceApiService.getAtInvoiceByApply(invoiceDto.getInvoiceApplyId(),invoiceDto.getInvoiceNo());
        if (Objects.nonNull(invoiceByApply)){
            invoiceDto.setInvoiceTraderContactEmail(invoiceByApply.getInvoiceTraderContactEmail());
            invoiceDto.setOpenInvoiceTime(invoiceByApply.getOpenInvoiceTime());
            invoiceDto.setOrderNo(invoiceByApply.getOrderNo());
        }
    }

    @Override
    @Transactional
    protected InvoiceDto successOpenInvoice(InvoiceApplyDto invoiceApplyDto, SaleInvoiceOpenResponseDto saleInvoiceOpenResponseDto) {
        // 新增发票
        InvoiceDto invoice = super.assembleCreateInvoice(invoiceApplyDto, saleInvoiceOpenResponseDto);
        invoiceService.createInvoice(invoice);
        // 更新售后单明细表开票状态
        afterSalesApiService.tryUpdateInvoiceStatus(invoiceApplyDto);
        // 更新申请单审核状态
        invoiceApplyService.auditPassInvoiceApply(invoiceApplyDto.getInvoiceApplyId());

        // 操作类型 改为手动
        if (ErpConstant.INVOICE_CREATE_TYPE_MANUAL.equals(invoiceApplyDto.getCreateType())) {
            invoiceApplyService.updateManualCreateType(invoiceApplyDto.getInvoiceApplyId());
        }

        return invoice;
    }

}
