package com.vedeng.goods.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;

import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.department.dao.DepartmentsHospitalGenerateMapper;
import com.vedeng.department.model.DepartmentsHospitalGenerate;
import com.vedeng.department.model.DepartmentsHospitalGenerateExample;
import com.vedeng.department.service.DepartmentsWithCompatibilityService;
import com.vedeng.goods.command.SpuAddCommand;
import com.vedeng.goods.dao.*;
import com.vedeng.goods.model.*;
import com.vedeng.goods.model.dto.MoveCategoryDto;
import com.vedeng.goods.model.vo.*;
import com.vedeng.goods.service.BaseCategoryService;
import com.vedeng.goods.service.InspectionItemApiService;
import com.vedeng.goods.utils.NamingUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service("basecategoryService")
public class BaseCategoryServiceImpl implements BaseCategoryService {

    private Logger logger= LoggerFactory.getLogger(BaseCategoryServiceImpl.class);
	@Autowired
	private BaseCategoryMapper baseCategoryMapper;

	@Autowired
	private BaseAttributeMapper baseAttributeMapper;

	@Autowired
	private BaseAttributeValueMapper baseAttributeValueMapper;

	@Autowired
	private CategoryAttrValueMappingMapper categoryAttrValueMappingMapper;

	@Autowired
	private CategoryMigrationMapper categoryMigrationMapper;

	@Resource
	private DepartmentsHospitalGenerateMapper departmentsHospitalGenerateMapper;

	@Resource
	private DepartmentsWithCompatibilityService departmentsWithCompatibilityService;

	@Resource
	private CategoryDepartmentMapper categoryDepartmentMapper;

	@Autowired
	private InspectionItemApiService inspectionItemApiService;

	@Value("${client_id}")
	protected String clientId;

	@Value("${client_key}")
	protected String clientKey;

	@Value("${operate_url}")
	protected String operateUrl;

	@Value("${is_sync_category_2_op}")
	protected Integer isSyncCategory2Op;

	/**
	 * @description 分类信息
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/9
	 */
	@Override
	public BaseCategoryVo getBaseCategoryByParam(Integer baseCategoryId) {

		return baseCategoryMapper.selectByPrimaryKey(baseCategoryId);
	}

	/**
	 * @description 保存分类信息
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/23
	 */
	@Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public ResultInfo saveBaseCategory(BaseCategoryVo baseCategoryVo, User operator) {
		Integer result = 0;
		Integer userId = 0;
		Date nowDate = new Date();
		
		//转换品名举例
		if (StringUtils.isNotEmpty(baseCategoryVo.getBaseCategoryExampleProduct())) {
			String[] split = baseCategoryVo.getBaseCategoryExampleProduct().split(",");
			if(split.length > 1){
				baseCategoryVo.setBaseCategoryExampleProduct(NamingUtils.join(ErpConst.Symbol.COMMA_IN_CN, split));
			}
		}
		
		//判断是新增还是更新
		if (baseCategoryVo.getBaseCategoryId() == null) {//新增分类信息
			userId = baseCategoryVo.getCreator();
			baseCategoryVo.setAddTime(nowDate);
			baseCategoryVo.setModTime(nowDate);
			//保存
			result = baseCategoryMapper.insertSelective(baseCategoryVo);
			//更新treenodes和ParentId
			if (result > 0){
				if (baseCategoryVo.getBaseCategoryLevel().equals(CommonConstants.CATEGORY_LEVEL_1)){
					baseCategoryVo.setTreenodes(String.valueOf(baseCategoryVo.getBaseCategoryId()));
				}else{
					baseCategoryVo.setTreenodes(baseCategoryVo.getTreenodes()+','+baseCategoryVo.getBaseCategoryId());
				}
				baseCategoryMapper.updateByPrimaryKeySelective(baseCategoryVo);
			}
		}else{//更新分类信息

			if (CommonConstants.CATEGORY_LEVEL_3.equals(baseCategoryVo.getBaseCategoryLevel())){

				/**若该分类下存在被推送到指南针平台的商品，则同步更新指南针平台（OP）相应商品的三级分类名  */


				baseCategoryVo.setIsDeleted(CommonConstants.IS_DELETE_0);
				List<BaseCategoryVo> thirdCategoryList = baseCategoryMapper.getthirdCategoryListById(baseCategoryVo);
				if (CollectionUtils.isEmpty(thirdCategoryList) || thirdCategoryList.get(0) == null ||
						(thirdCategoryList != null && thirdCategoryList.size() == 1 && thirdCategoryList.get(0) != null
								&& thirdCategoryList.get(0).getBaseCategoryId() == null)){
					thirdCategoryList = null;
				}else{
//					BaseCategoryVo baseCategoryVoExsit = baseCategoryMapper.selectByPrimaryKey(baseCategoryVo.getBaseCategoryId());
					for (BaseCategoryVo baseCategoryVoTemp : thirdCategoryList){
						Integer coreProductNum = baseCategoryVoTemp.getCoreProductNum() == null ? 0 : baseCategoryVoTemp.getCoreProductNum();
						Integer temporaryProductNum = baseCategoryVoTemp.getTemporaryProductNum() == null ? 0 : baseCategoryVoTemp.getTemporaryProductNum();
						Integer otherProductNum = baseCategoryVoTemp.getOtherProductNum() == null ? 0 : baseCategoryVoTemp.getOtherProductNum();
						if (coreProductNum > 0 || temporaryProductNum > 0
								|| otherProductNum > 0){
//							if (! baseCategoryVoExsit.getBaseCategoryType().equals(baseCategoryVo.getBaseCategoryType())
//									&& ! baseCategoryVoExsit.getBaseCategoryType().equals(0)){
//								return new ResultInfo(CommonConstants.FAIL_CODE, "分类下存在商品，暂不可修改分类类型！");
//							}
						}
					}
				}
			}
			pushUpdateCategory(baseCategoryVo,clientId,clientKey,operateUrl,isSyncCategory2Op);
			userId = baseCategoryVo.getUpdater();
			//更新
			baseCategoryVo.setModTime(nowDate);
			result = baseCategoryMapper.updateByPrimaryKeySelective(baseCategoryVo);
		}
		//判断该保存的分类信息是否属于三级分类
		if (result > 0 && CommonConstants.CATEGORY_LEVEL_3.equals(baseCategoryVo.getBaseCategoryLevel())){
			//如果是三级分类，则保存引用的属性及属性值关联信息
			String[] baseAttributeId = baseCategoryVo.getBaseAttributeId();
			String[] baseAttributeValueIds = baseCategoryVo.getBaseAttributeValueIds();

			//先删除原关联信息
			List<BaseCategoryVo> baseCategoryVoList = new ArrayList<>();
			baseCategoryVoList.add(baseCategoryVo);
			Map<String,Object> map = new HashMap<>();
			map.put("isDeleted",CommonConstants.IS_DELETE_1);
			map.put("updater",userId);
			map.put("modTime",nowDate);
			map.put("list",baseCategoryVoList);
			categoryAttrValueMappingMapper.deleteCategoryAttrValueMappingByCategoryIds(map);
			List<CategoryAttrValueMapping> categoryAttrValueMappingList = new ArrayList<>();
			//解析属性以及属性值
			for (int i = 0 ; i<baseAttributeId.length;i++){
				if (baseAttributeId[i] !=null && !"".equals(baseAttributeId[i]) && baseAttributeValueIds[i] != null && !"".equals(baseAttributeValueIds[i])){
					String[] baseAttributeValueId = baseAttributeValueIds[i].split("@");
					for (int j = 0 ; j < baseAttributeValueId.length;j++){
						CategoryAttrValueMapping categoryAttrValueMapping = new CategoryAttrValueMapping();
						categoryAttrValueMapping.setBaseAttributeId(Integer.valueOf(baseAttributeId[i]));
						categoryAttrValueMapping.setBaseAttributeValueId(Integer.valueOf(baseAttributeValueId[j]));
						categoryAttrValueMapping.setCreator(userId);
						categoryAttrValueMapping.setUpdater(userId);
						categoryAttrValueMapping.setAddTime(nowDate);
						categoryAttrValueMapping.setModTime(nowDate);
						categoryAttrValueMapping.setIsDeleted(CommonConstants.IS_DELETE_0);
						categoryAttrValueMapping.setBaseCategoryId(baseCategoryVo.getBaseCategoryId());
						categoryAttrValueMappingList.add(categoryAttrValueMapping);
					}
				}
			}
			if (CollectionUtils.isNotEmpty(categoryAttrValueMappingList)){
				//批量保存关联信息
				categoryAttrValueMappingMapper.insertCategoryAttrValueMappingBatch(categoryAttrValueMappingList);
			}
            //保存三级分类医院科室信息
            this.saveCategoryDepartment(baseCategoryVo.getBaseCategoryId(), baseCategoryVo.getHospitalDepartmentIds(), operator);

			// 保存三级分类检查项目信息
			inspectionItemApiService.saveCategoryInspection(baseCategoryVo.getBaseCategoryId(), baseCategoryVo.getInspectionItemIds());
        }
        return ResultInfo.success(baseCategoryVo.getBaseCategoryId());
	}

	@Override
	public List<BaseCategoryVo> getCategoryListPage(BaseCategoryVo baseCategoryVo, Page page) {
		Map<String,Object> map = new HashMap<>();
		map.put("baseCategoryVo",baseCategoryVo);
		map.put("page",page);
		List<BaseCategoryVo> list = null;
		if (CommonConstants.CATEGORY_LEVEL_1.equals(baseCategoryVo.getBaseCategoryLevel())){
			list = baseCategoryMapper.getFirstCategoryListPage(map);
		}
		if (CommonConstants.CATEGORY_LEVEL_2.equals(baseCategoryVo.getBaseCategoryLevel())){
			list = baseCategoryMapper.getSecondCategoryListForNameQuery(map);
		}
		if (CommonConstants.CATEGORY_LEVEL_3.equals(baseCategoryVo.getBaseCategoryLevel())){
			list = baseCategoryMapper.getThirdCategoryListForNameQuery(map);
		}
		return list;
	}

	/**
	 * @description 获取属性信息
	 * <AUTHOR>
	 * @param
	 * @date 2019/5/17
	 */
	@Override
	public List<BaseAttribute> getAttributeInfo(Integer baseCategoryId) {

		return baseAttributeMapper.getAttributeInfoByCategory(baseCategoryId);
	}

	@Override
	public List<BaseCategoryVo> getBaseCategoryListPageByAttr(Integer attrId, Page page) {
		Map<String,Object> paramMap = new HashMap<>();
		paramMap.put("attrId",attrId);
		paramMap.put("isDeleted",CommonConstants.IS_DELETE_0);
		paramMap.put("page",page);
		return baseCategoryMapper.getBaseCategoryListPage(paramMap);
	}

	@Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public Integer deleteCategory(List<BaseCategoryVo> firstCategoryVoList,
			List<BaseCategoryVo> secondCategoryVoList, List<BaseCategoryVo> thirdBasegoryVoList,User user) {
		Integer result = 0;
		Date nowDate = new Date();
		Map<String,Object> map = new HashMap<>();
		map.put("isDeleted",CommonConstants.IS_DELETE_1);
		map.put("modTime",nowDate);
		map.put("updater",user.getUserId());
		//删除三级分类
		if (CollectionUtils.isNotEmpty(thirdBasegoryVoList)){
			map.put("list",thirdBasegoryVoList);
			result = categoryAttrValueMappingMapper.deleteCategoryAttrValueMappingByCategoryIds(map);
			result = baseCategoryMapper.deleteCategory(map);
		}
		//删除二级分类
		if (CollectionUtils.isNotEmpty(secondCategoryVoList)){
			map.put("list",secondCategoryVoList);
			result = baseCategoryMapper.deleteCategory(map);
		}
		//删除一级分类
		if (CollectionUtils.isNotEmpty(firstCategoryVoList)){
			map.put("list",firstCategoryVoList);
			result = baseCategoryMapper.deleteCategory(map);
		}
		return result;
	}

	@Override
	public List<CategoryAttrValueMappingVo> getCategoryAttrValueMappingVoList(List<BaseCategoryVo> list) {
		Map<String,Object> map = new HashMap<>();
		map.put("isDeleted",CommonConstants.IS_DELETE_0);
		map.put("list",list);
		return categoryAttrValueMappingMapper.getCategoryAttrValueMappingVoList(map);
	}

    @Override
    public List<BaseAttributeVo> doAttrAndValueToString(List<BaseAttributeVo> list, List<BaseAttributeValueVo> baseAttributeValueVoList) {
		if (CollectionUtils.isNotEmpty(list)){
			for (BaseAttributeVo baseAttributeVo : list){
				if (CollectionUtils.isNotEmpty(baseAttributeValueVoList)){
					for (BaseAttributeValueVo baseAttributeValueVo : baseAttributeValueVoList){
						if (baseAttributeVo.getBaseAttributeId().equals(baseAttributeValueVo.getBaseAttributeId())){
							if (baseAttributeVo.getBaseAttributeValueIds() == null || "".equals(baseAttributeVo.getBaseAttributeValueIds())){
								baseAttributeVo.setBaseAttributeValueIds(String.valueOf(baseAttributeValueVo.getBaseAttributeValueId()));
							}else{
								baseAttributeVo.setBaseAttributeValueIds(baseAttributeVo.getBaseAttributeValueIds()+"@"+baseAttributeValueVo.getBaseAttributeValueId());
							}
						}
					}
				}
			}
		}
		return list;
    }

	@Override
	public String doAttrAndValueToJson(List<BaseAttributeVo> list, List<BaseAttributeValueVo> baseAttributeValueVoList) {
		String attrAndValueJson = "[";
		if (CollectionUtils.isNotEmpty(list)){
			for (BaseAttributeVo baseAttributeVo : list){
				attrAndValueJson = attrAndValueJson + "{\"attrName\":\""
						+baseAttributeVo.getBaseAttributeName()+"\",\"attrId\":\""
						+baseAttributeVo.getBaseAttributeId()+ "\",\"attrValue\":[";
				String attrAndValueJsonTemp = "";
				if (CollectionUtils.isNotEmpty(baseAttributeValueVoList)){
					for (BaseAttributeValueVo baseAttributeValueVo : baseAttributeValueVoList){
						if (baseAttributeVo.getBaseAttributeId().equals(baseAttributeValueVo.getBaseAttributeId())){
							attrAndValueJsonTemp = attrAndValueJsonTemp +"{\"attrValueName\":\""+baseAttributeValueVo.getAttrValue();
							if (CommonConstants.STATUS_1.equals(baseAttributeVo.getIsUnit())) {
								attrAndValueJsonTemp = attrAndValueJsonTemp +" "+baseAttributeValueVo.getUnitName();
							}
							attrAndValueJsonTemp = attrAndValueJsonTemp +"\",\"attrValueId\":\""+baseAttributeValueVo.getBaseAttributeValueId()+"\"},";
						}
					}
				}
				attrAndValueJson =  "".equals(attrAndValueJsonTemp) ?  attrAndValueJson +"]}," : attrAndValueJson + attrAndValueJsonTemp.substring(0,attrAndValueJsonTemp.length()-1) + "]},";
			}
		}
		attrAndValueJson = attrAndValueJson.length() == 1 ? attrAndValueJson + "]" : attrAndValueJson.substring(0,attrAndValueJson.length()-1) + "]";
		return attrAndValueJson;
	}

	@Override
	public List<BaseAttributeVo> doAttrAndValueToList(BaseCategoryVo baseCategoryVo) {
		String[] baseAttributeId = baseCategoryVo.getBaseAttributeId();
		String[] baseAttributeValueIds = baseCategoryVo.getBaseAttributeValueIds();
		if (baseAttributeId.length>0 || baseAttributeValueIds.length>0){
			List<BaseAttributeVo> baseAttributeVoList = new ArrayList<>();
			for (int i = 0 ; i<baseAttributeId.length;i++){
				BaseAttributeVo baseAttributeVo = new BaseAttributeVo();
				if (baseAttributeId[i]==null || "".equals(baseAttributeId[i])){
					baseAttributeVo.setBaseAttributeId(null);
				}else{
					baseAttributeVo.setBaseAttributeId(Integer.valueOf(baseAttributeId[i]));
				}
				if (baseAttributeValueIds.length>0 ){
					if (baseAttributeValueIds[i]!=null&& !"".equals(baseAttributeValueIds[i])){
						baseAttributeVo.setBaseAttributeValueIds(baseAttributeValueIds[i]);
					}else{
						baseAttributeVo.setBaseAttributeValueIds(null);
					}
				}else{
					baseAttributeVo.setBaseAttributeValueIds(null);
				}

				baseAttributeVoList.add(baseAttributeVo);
			}
			return baseAttributeVoList;
		}
		return null;
	}

	@Override
	public ResultInfo checkCategoryField(BaseCategoryVo baseCategoryVo) {
		if (baseCategoryVo == null){
			return new ResultInfo(CommonConstants.FAIL_CODE, "分类信息填写不完整，无法提交");
		}else{
			if (baseCategoryVo.getBaseCategoryName()==null || "".equals(baseCategoryVo.getBaseCategoryName())){
				return new ResultInfo(CommonConstants.FAIL_CODE, "分类名称填写不完整，无法提交");
			}
			if (CommonConstants.CATEGORY_LEVEL_3.equals(baseCategoryVo.getBaseCategoryLevel())){
//				if (baseCategoryVo.getBaseCategoryType()==null || "".equals(baseCategoryVo.getBaseCategoryType())){
//					return new ResultInfo(CommonConstants.FAIL_CODE, "分类类型为空，无法提交");
//				}
				String[] baseAttributeId = baseCategoryVo.getBaseAttributeId();
				String[] baseAttributeValueIds = baseCategoryVo.getBaseAttributeValueIds();
				List<Integer> baseAttributeIdList = new ArrayList<>();
				List<Integer> baseAttributeValueIdList = new ArrayList<>();
				/*if (baseAttributeId.length==0 && baseAttributeValueIds.length == 0){
					return new ResultInfo(CommonConstants.FAIL_CODE, "属性与属性值少于一条，无法提交");
				}*/
				if (baseAttributeId.length!=0 && baseAttributeValueIds.length == 0){
					return new ResultInfo(CommonConstants.FAIL_CODE, "属性与属性值填写不完整，无法提交");
				}
				for (int i = 0 ; i < baseAttributeId.length ; i ++){
					if(baseAttributeId[i] !=null && !"".equals(baseAttributeId[i])){
						if (baseAttributeValueIds[i] == null || "".equals(baseAttributeValueIds[i])){
							return new ResultInfo(CommonConstants.FAIL_CODE, "属性与属性值填写不完整，无法提交");
						}
						for (int j = i+1 ; j < baseAttributeId.length ; j ++){
							if (baseAttributeId[i].equals(baseAttributeId[j])){
								return new ResultInfo(CommonConstants.FAIL_CODE, "属性存在重复，无法提交");
							}
						}
						baseAttributeIdList.add(Integer.valueOf(baseAttributeId[i]));
						String[] baseAttributeValueIdArray = baseAttributeValueIds[i].split("@");
						for (int m = 0;m<baseAttributeValueIdArray.length;m++){
							baseAttributeValueIdList.add(Integer.valueOf(baseAttributeValueIdArray[m]));
						}
					}
				}
				if (CollectionUtils.isNotEmpty(baseAttributeIdList)){
					Map<String,Object> map = new HashMap<>();
					map.put("isDeleted",CommonConstants.IS_DELETE_1);//已删除
					map.put("list",baseAttributeIdList);
					if (baseAttributeMapper.getDeletedAttrNumByIds(map)>0){
						return new ResultInfo(CommonConstants.FAIL_CODE, "存在已删除的属性，无法提交");
					}
				}
				if (CollectionUtils.isNotEmpty(baseAttributeValueIdList)) {
					Map<String,Object> map = new HashMap<>();
					map.put("isDeleted",CommonConstants.IS_DELETE_1);//已删除
					map.put("list",baseAttributeValueIdList);
					if (baseAttributeValueMapper.getDeletedAttrValueNumByIds(map) > 0) {
						return new ResultInfo(CommonConstants.FAIL_CODE, "存在已删除的属性值，无法提交");
					}
				}
			}
			/**检查欲保存的分类信息是否已经存在
			 * 检查规则：1、一级或二级分类与库中所有未删除的分类都不可重复
			 *          2、三级分类与库中所有未删除的相同分类类型不可重复
			 */
			if (baseCategoryMapper.checkRepeatCategory(baseCategoryVo)>0){
				return new ResultInfo(CommonConstants.FAIL_CODE, "分类与已有内容重复，无法提交");
			}
		}
		return new ResultInfo(CommonConstants.SUCCESS_CODE, "校验通过");
	}

	@Override
	public String getOrganizedCategoryNameById(Integer thirdCategoryId) {

		return baseCategoryMapper.getOrganizedCategoryNameById(thirdCategoryId, ErpConst.Symbol.HYPHEN);
	}

	@Override
	public List<CategoryAttrValueMapping> getCategoryAttrValueMappingVoList(Integer thirdCategoryId) {
		Map<String,Object> map = new HashMap<>();
		map.put("isDeleted",CommonConstants.IS_DELETE_0);
		map.put("baseCategoryId",thirdCategoryId);
		return categoryAttrValueMappingMapper.getCategoryAttrValueMappingList(map);
	}

	@Override
	public List<BaseCategoryVo> getCategoryListByKeyWords(String keyWords) {
		//先查询出满足该关键词的商品ID
		List<Integer> idList = baseCategoryMapper.getCategoryIdByKeyWords(keyWords);
		return baseCategoryMapper.getCategoryListByKeyWords(keyWords,idList);
	}

	@Override
	public List<BaseCategoryVo> getCategoryListByIds(List<BaseCategoryVo> list,Integer level) {
		Map<String,Object> map = new HashMap<>();
		map.put("level",level);
		map.put("isDeleted",CommonConstants.IS_DELETE_0);
		map.put("list",list);
		return baseCategoryMapper.getCategoryListByIds(map);
	}

	@Override
	public List<BaseCategoryVo> getthirdCategoryListById(BaseCategoryVo baseCategoryVo) {
		baseCategoryVo.setIsDeleted(CommonConstants.IS_DELETE_0);
		return baseCategoryMapper.getthirdCategoryListById(baseCategoryVo);
	}
	@Override
	public List<BaseCategoryVo> getCategoryListByParentId(Integer parentId) {
		//baseCategoryVo.setIsDeleted(CommonConstants.IS_DELETE_0);
		return baseCategoryMapper.getCategoryListByParentId(parentId);
	}

	@Override
	public List<BaseCategory> getFirstCategory() {
		return baseCategoryMapper.getFirstCategory();
	}

	@Override
	public List<BaseCategory> getSecondCategory(Integer baseCategoryId) {
		return baseCategoryMapper.getSecondCategory(baseCategoryId);
	}

	@Override
	public BaseCategory getBaseCategoryInfoById(Integer categoryId) {
		return baseCategoryMapper.getBaseCategoryInfoById(categoryId);
	}

	@Override
	public void moveCatrgory(Integer baseCatrgoryId, Integer parentId) {
		baseCategoryMapper.moveCatrgory(baseCatrgoryId,parentId);
	}

	@Override
	public List<MoveCategoryDto> getMoveCategoryDtoByCategoryId(Integer categoryId) {
		return baseCategoryMapper.getMoveCategoryDtoByCategoryId(categoryId);
	}

	@Override
	public void insertCategoryMigration(CategoryMigration categoryMigration) {
		categoryMigrationMapper.insertCategoryMigration(categoryMigration);
	}

	@Override
	public List<FirstAndSecondCategoryInfo> getFirstAndSecondCategoryListByKeyWords(String keyWords) {
		List<Integer> idList = baseCategoryMapper.getCategoryIdByKeyWords(keyWords);
		return baseCategoryMapper.getFirstAndSecondCategoryListByKeyWords(keyWords,idList);
	}

	@Override
	public List<CategoryMigration> getCtegoryMigretionlistPage(CategoryMigrationVo categoryMigrationVo, Page page) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("categoryMigrationVo", categoryMigrationVo);
		map.put("page", page);
		return categoryMigrationMapper.getCtegoryMigretionlistPage(map);
	}


	@Override
	public int modifyCategory2Op(CategoryToOpVo categoryToOpVo) {
	    if(categoryToOpVo==null||categoryToOpVo.getCategoryLevel()==null){
	        logger.info("通知op分类修改，参数为空");
	        return GoodsConstants.ZERO;
        }else{
	    	String url=operateUrl+"";
	        logger.info("通知op分类修改，参数为：{}", JSONObject.toJSONString(categoryToOpVo));
			com.alibaba.fastjson.TypeReference<RestfulResult> typeReference = new com.alibaba.fastjson.TypeReference<RestfulResult>() {
			};
			RestfulResult restfulResult = HttpRestClientUtil.restPost(url, typeReference, null, categoryToOpVo);
			if(restfulResult==null||!restfulResult.isSuccess()){
				logger.info("分类修改同步到op失败");
			}else {
				logger.info("分类修改同步到op成功");
			}
        }
		return 0;
	}

	@Override
	public int migrateCategory2Op(CategoryToOpVo categoryToOpVo) {
		if(ErpConst.ZERO.equals(isSyncCategory2Op)){
			return 1;
		}
        if(categoryToOpVo==null){
            logger.info("通知op分类迁移，参数为空");
            return GoodsConstants.ZERO;
        }else{
        	try {
				String url = operateUrl + "/operationclassify/fullPushCategory";
				logger.info("通知op分类迁移，参数为：{}", JSONObject.toJSONString(categoryToOpVo));
				List<CategoryToOpVo> categoryList = new ArrayList<>();
				categoryList.add(categoryToOpVo);
				com.alibaba.fastjson.TypeReference<RestfulResult> typeReference = new com.alibaba.fastjson.TypeReference<RestfulResult>() {
				};
				RestfulResult restfulResult = HttpRestClientUtil.restPost(url, typeReference, null, categoryList);
				if (restfulResult == null || !restfulResult.isSuccess()) {
					logger.info("分类迁移同步到op失败");
					return GoodsConstants.ZERO;
				} else {
					logger.info("分类迁移同步到op成功");
					return GoodsConstants.ONE;
				}
			}catch (Exception ex){
				logger.info("分类迁移同步到op出错",ex);
				return GoodsConstants.ZERO;
			}
        }
	}

	@Override
	public List<CategoryToOpVo> getAllLevelCategoryByIdList(List<Integer> categoryIds) {
		return baseCategoryMapper.getCategoryLevelAllByIdList(categoryIds);
	}

    @Override
    public String getAllLevelCategoryNameById(Integer categoryId) {
        return baseCategoryMapper.getAllLevelCategoryNameById(categoryId);
    }



    @Override
    @Transactional(readOnly = true, rollbackFor = Exception.class)
    public List<CategoryDepartmentDo> listCategoryDepartmentDo(Integer thirdLevelCategoryId, Integer deleted) {
        if (thirdLevelCategoryId == null) {
            return Collections.emptyList();
        }
        return categoryDepartmentMapper.listByCategoryId(thirdLevelCategoryId, deleted);
    }

    @Transactional(readOnly = true, rollbackFor = Exception.class)
    @Override
    public List<CategoryDepartmentVo> listCategoryDepartmentVo(Integer thirdLevelCategoryId) {
        DepartmentsHospitalGenerateExample condition = new DepartmentsHospitalGenerateExample();
        condition.createCriteria().andIsDeleteEqualTo(CommonConstants.IS_DELETE_0);
		condition.setOrderByClause("sort");
        List<DepartmentsHospitalGenerate> notDeletedDepartmentList = departmentsHospitalGenerateMapper.selectByExample(condition);

        List<Integer> departmentIdListToUse = this.listCategoryDepartmentDo(thirdLevelCategoryId, CommonConstants.IS_DELETE_0).stream()
                .map(CategoryDepartmentDo::getDepartmentId).collect(Collectors.toList());

        List<CategoryDepartmentVo> categoryDepartmentVoList = new ArrayList<>(notDeletedDepartmentList.size());
        notDeletedDepartmentList.forEach(item -> {
            CategoryDepartmentVo categoryDepartmentVo = new CategoryDepartmentVo();
            categoryDepartmentVo.setDepartmentId(item.getDepartmentId());
            categoryDepartmentVo.setDepartmentName(item.getDepartmentName());
            //如果分类id不为空做勾选判断
            if(thirdLevelCategoryId != null){
				categoryDepartmentVo.setSelected(departmentIdListToUse.contains(item.getDepartmentId()));
			}
            categoryDepartmentVoList.add(categoryDepartmentVo);
        });
        return categoryDepartmentVoList;
    }


    private void  saveCategoryDepartment(Integer thirdLevelCategoryId, Integer[] departmentIds, User operator) {
        if (thirdLevelCategoryId == null) {
            return;
        }

		Set<Integer> departmentIdsToSave = new HashSet<>(Arrays.asList(ArrayUtils.isNotEmpty(departmentIds)?departmentIds : new Integer[0]));

		DepartmentsHospitalGenerateExample example = new DepartmentsHospitalGenerateExample();
        example.createCriteria().andIsDeleteEqualTo(CommonConstants.IS_DELETE_0);
        List<DepartmentsHospitalGenerate> nonDeletedDepartmentList = departmentsHospitalGenerateMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(nonDeletedDepartmentList)) {
            logger.error("【数据异常】保存三级分类关联的科室时，从DB查询医院科室信息为空");
            return;
        }
        List<Integer> nonDeletedDepartmentIdList = nonDeletedDepartmentList.stream().map(DepartmentsHospitalGenerate::getDepartmentId).collect(Collectors.toList());

        List<CategoryDepartmentDo> categoryDepartmentDoList = this.listCategoryDepartmentDo(thirdLevelCategoryId, null);
        
		Set<CategoryDepartmentDo> categoryDepartmentSetToDelete = Collections.newSetFromMap(new IdentityHashMap<>());
        Map<Integer,CategoryDepartmentDo> categoryDepartmentMap = new HashMap<>(categoryDepartmentDoList.size());
        categoryDepartmentDoList.forEach(e->{
			if (CommonConstants.IS_DELETE_0.equals(e.getIsDeleted())) {
				categoryDepartmentSetToDelete.add(e);
			}
        	categoryDepartmentMap.put(e.getDepartmentId(), e);
        });

		List<CategoryDepartmentDo> categoryDepartmentDoListToAdd = new LinkedList<>();
        List<CategoryDepartmentDo> categoryDepartmentDoListToUpdate = new LinkedList<>();


		if(departmentIdsToSave.isEmpty()){
			categoryDepartmentSetToDelete.addAll(categoryDepartmentDoList);
		}else {
			Iterator<Integer> iterator = departmentIdsToSave.iterator();
			while (iterator.hasNext()) {
				Integer departmentIdToSave = iterator.next();
				if (!nonDeletedDepartmentIdList.contains(departmentIdToSave)) {
					iterator.remove();
					continue;
				}

				CategoryDepartmentDo categoryDepartmentDoToUse = categoryDepartmentMap.get(departmentIdToSave);

				if (categoryDepartmentDoToUse != null) {
					//先将已有可科室信息置为删除状态
					categoryDepartmentSetToDelete.remove(categoryDepartmentDoToUse);

					CategoryDepartmentDo categoryDepartmentDo = new CategoryDepartmentDo();
					categoryDepartmentDo.setId(categoryDepartmentDoToUse.getId());
					categoryDepartmentDo.setDepartmentId(categoryDepartmentDoToUse.getDepartmentId());
					categoryDepartmentDo.setCategoryId(thirdLevelCategoryId);
					categoryDepartmentDo.setIsDeleted(CommonConstants.IS_DELETE_0);
					categoryDepartmentDo.setCreator(categoryDepartmentDoToUse.getCreator());
					categoryDepartmentDo.setUpdater(operator.getUserId());
					categoryDepartmentDo.setAddTime(categoryDepartmentDoToUse.getAddTime());
					categoryDepartmentDo.setModTime(new Date());
					categoryDepartmentDoListToUpdate.add(categoryDepartmentDo);
				} else {
					CategoryDepartmentDo categoryDepartmentDo = new CategoryDepartmentDo();
					categoryDepartmentDo.setDepartmentId(departmentIdToSave);
					categoryDepartmentDo.setCategoryId(thirdLevelCategoryId);
					categoryDepartmentDo.setIsDeleted(CommonConstants.IS_DELETE_0);
					categoryDepartmentDo.setCreator(operator.getUserId());
					categoryDepartmentDo.setUpdater(operator.getUserId());
					categoryDepartmentDo.setAddTime(new Date());
					categoryDepartmentDo.setModTime(new Date());
					categoryDepartmentDoListToAdd.add(categoryDepartmentDo);
				}
			}
		}

		//删除过期的科室信息
		if (CollectionUtils.isNotEmpty(categoryDepartmentSetToDelete)) {
			categoryDepartmentSetToDelete.forEach(e->e.setIsDeleted(CommonConstants.IS_DELETE_1));
			categoryDepartmentMapper.batchUpdate(new ArrayList<>(categoryDepartmentSetToDelete));
		}

        if (!categoryDepartmentDoListToAdd.isEmpty()) {
            categoryDepartmentMapper.batchInsert(categoryDepartmentDoListToAdd);
        }

        if (!categoryDepartmentDoListToUpdate.isEmpty()) {
            categoryDepartmentMapper.batchUpdate(categoryDepartmentDoListToUpdate);
        }

        departmentsWithCompatibilityService.saveDepartment(thirdLevelCategoryId, departmentIdsToSave, operator);
    }

	@Override
	public String getThirdCategoryFullPath(Integer thirdCategoryId, String delimiter) {
		if (thirdCategoryId == null) {
			return StringUtils.EMPTY;
		}
		return baseCategoryMapper.getOrganizedCategoryNameById(thirdCategoryId,StringUtils.isNotEmpty(delimiter)?delimiter:ErpConst.Symbol.COMMA);
	}

	@Override
	public List<SpuAddCommand> getValidSpuInfoByCategoryId(Integer baseCategoryId) {
		if (baseCategoryId == null || baseCategoryId <= 0){
			return null;
		}
		return baseCategoryMapper.getValidSpuInfoByCategoryId(baseCategoryId);
	}

	@Override
	public String getDepartmentByBaseCategoryId(Integer baseCategoryId) {
		if (baseCategoryId == null || baseCategoryId <= 0){
			return null;
		}
		return baseCategoryMapper.getDepartmentByBaseCategoryId(baseCategoryId);
	}

	public ResultInfo pushUpdateCategory(BaseCategoryVo baseCategoryVo, String clientId, String clientKey, String operateUrl,Integer isPush) {
		if(ErpConst.ZERO.equals(isPush)){
			return new ResultInfo();
		}
		try {
			logger.info("修改分类推送到op");
			//查询该在OP平台中分类下是否有商品
			// 定义反序列化 数据格式
//            final TypeReference<ResultInfo4Op> TypeRef4Query = new TypeReference<ResultInfo4Op>() {
//            };
//            String queryUrl = operateUrl + ErpConst.GET_GOODSNUM_URL + baseCategoryVo.getBaseCategoryId();
//            Object resultInfo = HttpClientUtils4Op.get(queryUrl, null, clientId, clientKey, TypeRef4Query);
//            ResultInfo4Op resultInfo4Op = (ResultInfo4Op) resultInfo;
//            int goodsNum = resultInfo4Op == null ? 0 : Integer.parseInt(resultInfo4Op.getData().toString());
//
//            //如果存在商品则向OP系统中同步分类名
//            if (goodsNum > 0) {

//                String erpCatNameConcat = "&erpCatNameConcat=" + baseCategoryVo.getFirstLevelCategoryName() + "@" + baseCategoryVo.getSecondLevelCategoryName() + "@" + baseCategoryVo.getBaseCategoryName();
			String url = operateUrl + ErpConst.SAVE_CATEGORY_URL_NEW   ;
			//  String url = operateUrl + ErpConst.SAVE_CATEGORY_URL + baseCategoryVo.getBaseCategoryId() + "&erpCategoryName=" + baseCategoryVo.getBaseCategoryName() ;

			Map<String, String> paramMap = new HashMap<String, String>();
			paramMap.put("erpCategoryName",baseCategoryVo.getBaseCategoryName());
			paramMap.put("erpcategoryId",baseCategoryVo.getBaseCategoryId()+"");
			HttpClientUtils.postOp(url, paramMap   );
//            if("-1".equals(object.toString())){
//                throw new NumberFormatException();
//            }
//            }
		} catch (Exception e) {
			logger.error("商品分类信息推送异常：", e);
		}
		return new ResultInfo();
	}

}
