package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseReceiptEntity;
import java.util.List;

import com.vedeng.erp.kingdee.dto.KingDeePurchaseReceiptQueryDto;
import org.apache.ibatis.annotations.Param;

/**
 * @description: 
 * @author: yana.jiang
 * @date: 2022/11/10
 */
public interface KingDeePurchaseReceiptMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeePurchaseReceiptEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeePurchaseReceiptEntity record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    KingDeePurchaseReceiptEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeePurchaseReceiptEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeePurchaseReceiptEntity record);

    int updateBatchSelective(List<KingDeePurchaseReceiptEntity> list);

    int batchInsert(@Param("list") List<KingDeePurchaseReceiptEntity> list);


    List<KingDeePurchaseReceiptEntity> findAll(KingDeePurchaseReceiptQueryDto kingDeePurchaseReceiptEntity);

}