package com.vedeng.track.log.bigdata;

import com.alibaba.fastjson.JSON;
import com.vedeng.track.server.bigdata.param.EventTraceReqDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/11/16
 */
public class BigdataTraceInErp {

    /**
     * 该日志在log.config中进行了独立的配置。参见apollo
     */
    private static Logger logger = LoggerFactory.getLogger(BigdataTraceInErp.class);

    public static void eventLogSubmit(EventTraceReqDto reqDto) {
        reqDto.setEventType("eventTrack");
        logger.info(JSON.toJSONString(reqDto));
    }


}
