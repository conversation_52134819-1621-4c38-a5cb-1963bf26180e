package com.vedeng.flash.dao;

import com.vedeng.flash.dto.PrepareStockLogDto;
import com.vedeng.flash.dto.RegularOperateLogDto;
import com.vedeng.flash.model.PrepareStockLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface PrepareStockLogMapper {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PREPARE_STOCK_LOG
     *
     * @mbggenerated Fri May 21 17:09:58 CST 2021
     */
    int deleteByPrimaryKey(Integer safeStockId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PREPARE_STOCK_LOG
     *
     * @mbggenerated Fri May 21 17:09:58 CST 2021
     */
    int insert(PrepareStockLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PREPARE_STOCK_LOG
     *
     * @mbggenerated Fri May 21 17:09:58 CST 2021
     */
    int insertSelective(PrepareStockLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PREPARE_STOCK_LOG
     *
     * @mbggenerated Fri May 21 17:09:58 CST 2021
     */
    PrepareStockLog selectByPrimaryKey(Integer safeStockId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PREPARE_STOCK_LOG
     *
     * @mbggenerated Fri May 21 17:09:58 CST 2021
     */
    int updateByPrimaryKeySelective(PrepareStockLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PREPARE_STOCK_LOG
     *
     * @mbggenerated Fri May 21 17:09:58 CST 2021
     */
    int updateByPrimaryKey(PrepareStockLog record);

    /**
     * 根据备货计划Id查询安全日志
     * @param regularId
     * @return
     */
    List<PrepareStockLogDto> getStockLogListByRegularId(Integer regularId);

    List<RegularOperateLogDto> getPrepareLogListByRegularIdListPage(Map<String, Object> map);

    List<RegularOperateLogDto> getDeleteLogListByRegularId(@Param(value = "regularId") Integer regularId);

    List<RegularOperateLogDto> getDoNotPrepareLogListByRegularId(@Param(value = "regularId") Integer regularId);

    List<RegularOperateLogDto> getRepalceLogListByRegularId(@Param(value = "regularId")Integer regularId);

    List<RegularOperateLogDto> getConversionLogListByRegularId(@Param(value = "regularId")Integer regularId);
}
