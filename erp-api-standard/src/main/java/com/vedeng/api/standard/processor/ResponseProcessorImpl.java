package com.vedeng.api.standard.processor;

import com.vedeng.api.standard.core.ApiRequest;
import com.vedeng.api.standard.core.exception.BusinessException;
import com.vedeng.api.standard.internal.InternalCallResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.regex.Pattern;

/**
 * 响应处理器实现类
 * 提供标准的响应后处理功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
@Component
public class ResponseProcessorImpl implements ResponseProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(ResponseProcessorImpl.class);
    
    // 敏感数据匹配模式
    private static final Pattern PASSWORD_PATTERN = Pattern.compile("password|pwd|passwd", Pattern.CASE_INSENSITIVE);
    private static final Pattern PHONE_PATTERN = Pattern.compile("phone|mobile|tel", Pattern.CASE_INSENSITIVE);
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("idcard|idno|identity", Pattern.CASE_INSENSITIVE);
    
    @Override
    public Object postProcess(ApiRequest apiRequest, Object result) {
        try {
            //// 0. 首先统一处理 InternalCallResult（最优先处理）
            //if (result instanceof InternalCallResult) {
            //   InternalCallResult callResult = (InternalCallResult) result;
            //
            //    if (!callResult.isSuccess()) {
            //        // InternalCallResult 失败处理
            //        String errorMessage = callResult.getErrorMessage();
            //        if (errorMessage == null || errorMessage.trim().isEmpty()) {
            //            errorMessage = "内部调用失败";
            //        }
            //
            //        String businessCode = callResult.getBusinessCode();
            //        logger.warn("InternalCallResult执行失败: requestId={}, error={}, businessCode={}",
            //            apiRequest.getRequestId(), errorMessage, businessCode);
            //
            //        // 根据业务错误码选择异常类型，让Controller的异常处理机制统一处理
            //        if (businessCode != null && !businessCode.trim().isEmpty()) {
            //            throw new BusinessException(businessCode, errorMessage);
            //        } else {
            //            throw new RuntimeException(errorMessage);
            //        }
            //    }
            //
            //    // InternalCallResult 成功处理：提取业务数据
            //    Object businessData = callResult.getData();
            //    logger.debug("InternalCallResult执行成功: requestId={}, hasData={}",
            //        apiRequest.getRequestId(), businessData != null);
            //
            //    // 继续对提取的业务数据进行后续处理
            //    result = businessData;
            //}
            
            // 1. 记录响应日志
            logResponse(apiRequest, result);
            
            // 2. 处理敏感数据
            Object sanitizedResult = sanitizeSensitiveData(result);
            
            // 3. 可以在这里添加其他后处理逻辑
            // 例如：数据格式转换、缓存处理、统计信息收集等
            
            return sanitizedResult;
            
        } catch (Exception e) {
            // 如果是我们主动抛出的异常，继续抛出
            if (e instanceof BusinessException ||
                e instanceof RuntimeException) {
                throw e;
            }
            
            logger.warn("响应后处理失败: requestId={}", apiRequest.getRequestId(), e);
            // 如果后处理失败，返回原始结果
            return result;
        }
    }
    
    @Override
    public void logResponse(ApiRequest apiRequest, Object result) {
        try {
            String resultType = result != null ? result.getClass().getSimpleName() : "null";
            
            logger.info("API响应: requestId={}, module={}, action={}, resultType={}", 
                apiRequest.getRequestId(),
                apiRequest.getModule(),
                apiRequest.getAction(),
                resultType);
            
            // 记录响应数据的基本信息（不记录具体内容）
            if (result instanceof Map) {
                Map<?, ?> resultMap = (Map<?, ?>) result;
                logger.debug("响应数据大小: {} 个字段", resultMap.size());
            }
            
        } catch (Exception e) {
            logger.warn("记录响应日志失败: requestId={}", apiRequest.getRequestId(), e);
        }
    }
    
    @Override
    public Object sanitizeSensitiveData(Object result) {
        if (result == null) {
            return null;
        }
        
        try {
            if (result instanceof Map) {
                return sanitizeMap((Map<?, ?>) result);
            } else if (result instanceof String) {
                return sanitizeString((String) result);
            } else {
                // 对于其他类型，可以根据需要扩展处理逻辑
                return result;
            }
        } catch (Exception e) {
            logger.warn("敏感数据处理失败", e);
            return result;
        }
    }
    
    /**
     * 处理Map类型的敏感数据
     */
    @SuppressWarnings("unchecked")
    private Object sanitizeMap(Map<?, ?> map) {
        if (map == null || map.isEmpty()) {
            return map;
        }
        
        // 创建新的Map来避免修改原始数据
        Map<Object, Object> sanitizedMap = new java.util.HashMap<>();
        
        for (Map.Entry<?, ?> entry : map.entrySet()) {
            Object key = entry.getKey();
            Object value = entry.getValue();
            
            if (key instanceof String) {
                String keyStr = (String) key;
                
                // 检查是否为敏感字段
                if (isSensitiveField(keyStr)) {
                    sanitizedMap.put(key, maskSensitiveValue(value));
                } else if (value instanceof Map) {
                    // 递归处理嵌套Map
                    sanitizedMap.put(key, sanitizeMap((Map<?, ?>) value));
                } else {
                    sanitizedMap.put(key, value);
                }
            } else {
                sanitizedMap.put(key, value);
            }
        }
        
        return sanitizedMap;
    }
    
    /**
     * 处理String类型的敏感数据
     */
    private String sanitizeString(String str) {
        if (str == null || str.trim().isEmpty()) {
            return str;
        }
        
        // 可以在这里添加字符串中敏感信息的处理逻辑
        // 例如：替换身份证号、手机号等
        
        return str;
    }
    
    /**
     * 判断是否为敏感字段
     */
    private boolean isSensitiveField(String fieldName) {
        if (fieldName == null || fieldName.trim().isEmpty()) {
            return false;
        }
        
        String lowerFieldName = fieldName.toLowerCase();
        
        // 检查密码相关字段
        if (PASSWORD_PATTERN.matcher(lowerFieldName).find()) {
            return true;
        }
        
        // 检查手机号相关字段
        if (PHONE_PATTERN.matcher(lowerFieldName).find()) {
            return true;
        }
        
        // 检查身份证相关字段
        if (ID_CARD_PATTERN.matcher(lowerFieldName).find()) {
            return true;
        }
        
        // 可以添加更多敏感字段的判断逻辑
        
        return false;
    }
    
    /**
     * 对敏感值进行脱敏处理
     */
    private Object maskSensitiveValue(Object value) {
        if (value == null) {
            return null;
        }
        
        if (value instanceof String) {
            String strValue = (String) value;
            
            if (strValue.length() <= 4) {
                return "****";
            } else if (strValue.length() <= 8) {
                return strValue.substring(0, 2) + "****" + strValue.substring(strValue.length() - 2);
            } else {
                return strValue.substring(0, 3) + "****" + strValue.substring(strValue.length() - 3);
            }
        }
        
        // 对于非字符串类型，直接返回脱敏标识
        return "****";
    }
}
