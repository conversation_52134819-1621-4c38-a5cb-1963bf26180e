package com.vedeng.order.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.constant.RemarkComponentTypeEnum;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.TreeUtil;
import com.vedeng.order.dao.RemarkComponentMapper;
import com.vedeng.order.model.ComponentRelation;
import com.vedeng.order.model.RemarkComponent;
import com.vedeng.order.model.RemarkComponentTree;
import com.vedeng.order.model.dto.RemarkComponentDefaultDto;
import com.vedeng.order.model.query.LabelQuery;
import com.vedeng.order.model.vo.SkuVo;
import com.vedeng.order.service.RemarkComponentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:  备注组件业务处理
 * @Author:       davis
 * @Date:         2021/4/14 下午5:30
 * @Version:      1.0
 */
@Slf4j
@Service("remarkComponentService")
public class RemarkComponentServiceImpl extends BaseServiceimpl implements RemarkComponentService {

    @Autowired
    private RemarkComponentMapper remarkComponentMapper;

    @Value("${component_remark_default}")
    private String componentRemarkDefault;

    @Override
    public List<RemarkComponentTree> getInitComponent(LabelQuery labelQuery) {
        List<RemarkComponent> componentList = remarkComponentMapper.getInitComponent(labelQuery);
        // 组装树形结构
        List<RemarkComponentTree> componentTreeList = getComponentTree(componentList, labelQuery);
        return componentTreeList;
    }

    @Override
    public String getComponentHtml(LabelQuery labelQuery) {
        List<ComponentRelation> componentRelationList = remarkComponentMapper.getComponentRelationList(labelQuery);
        Map<Integer, StringBuffer> map = new TreeMap<>(Comparator.naturalOrder());
        StringBuffer sb;

        if (CollectionUtils.isEmpty(componentRelationList)) {
            return "";
        }

        for (ComponentRelation componentRelation : componentRelationList) {
            int type = componentRelation.getType();
            int componentId = componentRelation.getComponentId();
            sb = map.get(componentId);
            if (sb == null) {
                sb = new StringBuffer();
                sb.append("<div class='pre-item'>\n" +
                        "<div class='item-label'>").append(RemarkComponentTypeEnum.codeToMessage(type)).append("：</div>\n" +
                        "<div class='item-field'>").append(componentRelation.getName())
                        .append("【产品：").append(componentRelation.getSkuNo());
                if (componentRelation.getIsDate() == 0) {
                    sb.append(" 延期至：").append(DateUtil.convertString(componentRelation.getTime(), "yyyy-MM-dd"));
                }
                if (componentRelation.getIsReason() == 0) {
                    sb.append(" 原因：").append(componentRelation.getReason());
                }
                sb.append("】\n</div>").append("\n</div>");
                if(componentId == 16 || componentId == 17){ // 专向发货 - T_REMARK_COMPONENT（ 是、否）
                    sb = new StringBuffer();
                    sb.append("<div class='pre-item'>\n" +
                            "<div class='item-label'>").append(RemarkComponentTypeEnum.codeToMessage(type)).append("：</div>\n" +
                            "<div class='item-field'>").append(componentRelation.getName());
                    sb.append("\n</div>").append("\n</div>");
                }
                map.put(componentId, sb);
            } else {
                String[] str = sb.toString().split("】");
                sb = new StringBuffer();
                sb.append(str[0]).append("、").append(componentRelation.getSkuNo());
                if(str.length>=2){
                    sb.append(str[1]);
                }
                map.put(componentId, sb);
            }
        }
        String componentHtml = "";

        if (CollectionUtils.isEmpty(map)) {
             return "";
        }

        Set<Map.Entry<Integer, StringBuffer>> entrySet = map.entrySet();
        if (!CollectionUtils.isEmpty(entrySet)) {
            for (Map.Entry<Integer, StringBuffer> entry : entrySet) {
                componentHtml += entry.getValue().toString();
            }
        }
        return componentHtml;
    }

    private List<RemarkComponentTree> getComponentTree(List<RemarkComponent> componentList, LabelQuery labelQuery) {
        List<RemarkComponentTree> componentTreeList = new ArrayList<>();
        RemarkComponentTree remarkComponentTree;
        if (CollectionUtils.isEmpty(componentList)) {
            return componentTreeList;
        }
        for (RemarkComponent remarkComponent : componentList) {
            List<SkuVo> skuList = new ArrayList<>();
            remarkComponentTree = new RemarkComponentTree();
            remarkComponentTree.setId(remarkComponent.getComponentId());
            remarkComponentTree.setParentId(remarkComponent.getParentId());
            remarkComponentTree.setLabel(remarkComponent.getName());
            if (remarkComponent.getParentId() == 0) {
                componentTreeList.add(remarkComponentTree);
                continue;
            }
            labelQuery.setComponentId(remarkComponent.getComponentId());
            // 获取该场景下是否已经配置过内部备注
            remarkComponentTree.setDisabled(false);
            remarkComponentTree.setNeedDate(remarkComponent.getIsDate() == 0);
            remarkComponentTree.setNeedReason(remarkComponent.getIsReason() == 0);

            if (CollectionUtils.isEmpty(labelQuery.getSkuList())) {
                continue;
            }

            for (SkuVo sku : labelQuery.getSkuList()) {
                SkuVo skuVo = new SkuVo();
                BeanUtils.copyProperties(sku, skuVo);
                labelQuery.setSkuNo(skuVo.getSkuNo());
                labelQuery.setSkuName(skuVo.getSkuName());
                if (labelQuery.getIsAll() == 1) {
                    ComponentRelation componentRelation = remarkComponentMapper.getComponentRelation(labelQuery);
                    if (componentRelation != null) {
                        skuVo.setChoose(true);
                        remarkComponentTree.setSelected(true);
                        remarkComponentTree.setDate(DateUtil.convertString(componentRelation.getTime(), "yyyy-MM-dd"));
                        remarkComponentTree.setReason(componentRelation.getReason());
                    } else {
                        skuVo.setChoose(false);
                    }
                } else {
                    skuVo.setChoose(false);
                }
                skuList.add(skuVo);
            }
            remarkComponentTree.setSkuList(skuList);
            componentTreeList.add(remarkComponentTree);
        }

        setComponentDefaultValue(componentTreeList);


        return TreeUtil.build(componentTreeList, 0);
    }

    /**
     * 初始化内部备注默认值
     *
     * @param componentTreeList
     */
    private void setComponentDefaultValue(List<RemarkComponentTree> componentTreeList) {
        List<RemarkComponentDefaultDto> remarkComponentDefaultList = JSONObject.parseArray(componentRemarkDefault, RemarkComponentDefaultDto.class);
        if (CollectionUtils.isEmpty(componentTreeList) || CollectionUtils.isEmpty(remarkComponentDefaultList)) {
         return;
        }

        remarkComponentDefaultList.forEach(defaultValue -> {
            if (!CollectionUtils.isEmpty(componentTreeList.stream().filter(item -> defaultValue.getParentId().equals(item.getParentId()) &&
                    item.getSelected() != null  && item.getSelected()).collect(Collectors.toList()))){
                return;
            }
            componentTreeList.stream().filter(item -> defaultValue.getParentId().equals(item.getParentId()) && defaultValue.getDefaultId().equals(item.getId()))
                    .forEach(item -> {
                        item.setSelected(true);
                        if (CollectionUtils.isEmpty(item.getSkuList())){
                            return;
                        }
                        item.getSkuList().forEach(item1->item1.setChoose(true));
                    });
        });
    }

    @Override
    public List<Map<String, Object>> findComponentList(Map map) {
        return remarkComponentMapper.findComponentList(map) ;
    }
}
