package com.vedeng.erp.saleorder.dto;

import lombok.Data;

import java.util.List;

/**
 * 报价单信息
 */
@Data
public class QuoteInfoDto {
    /**
     * 报价单号
     */
    private Integer quoteorderId;

    /**
     * 报价单号
     */
    private String quoteorderNo;

    /**
     *交易者ID
     */
    private Integer traderId;

    /**
     * 交易者名称
     */
    private String traderName;

    /**
     * 客户地区
     */
    private String area;

    /**
     * 客户性质
     */
    private Integer customerNature;
    /**
     * 终端客户ID
     */
    private Integer terminalTraderId;

    /**
     * 终端名称
     */
    private String terminalTraderName;

    /**
     * 销售区域ID
     */
    private Integer salesAreaId;

    /**
     * 销售区域
     */
    private String salesArea;

    /**
     * 跟单状态
     */
    private Integer followOrderStatus;
    /**
     * 报价单商品信息
     */
    private List<QuoteGoodsInfoDto> quoteGoodsInfoDtos;
    /**
     * 报价单归属用户
     */
    private Integer userId;
}
