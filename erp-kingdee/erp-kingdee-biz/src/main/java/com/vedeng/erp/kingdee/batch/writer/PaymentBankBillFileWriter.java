package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.domain.command.KingDeeFileCommand;
import com.vedeng.erp.kingdee.mapstruct.KingDeeFileCommandConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePayBillMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeReceiveBillMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoRet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName PaymentBankBillFileWriter.java
 * @Description TODO 推送回单信息
 * @createTime 2023年06月05日 11:17:00
 */
@Slf4j
@Service
public class PaymentBankBillFileWriter extends BaseWriter<KingDeeFileCommand> {
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeePayBillMapper kingDeePayBillMapper;

    @Override
    public void doWrite(KingDeeFileCommand item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("收款单-推送金蝶附件信息{},", JSON.toJSONString(item));
        if (item == null){
            return;
        }
        RepoRet fileResult = kingDeeBaseApi.attachmentUpload(item);
        if (fileResult == null) {
            log.info("收款单-回单上传金蝶失败");
            throw new ServiceException("收款单-回单上传失败");
        } else {
            if (fileResult.getResult().getResponseStatus().isIsSuccess()) {
                //收到成功保存附件信息
                log.info("收款单-回单保存成功{},", JSON.toJSONString(fileResult));
                //更新附件是否上传成功信息
                kingDeePayBillMapper.updatePushStatus(Integer.parseInt(stepContext.get("kingDeePayBillId").toString()));
            } else {
                log.info("收款单-回单上传金蝶失败,金蝶反馈{},", JSON.toJSONString(fileResult));
                throw new ServiceException("收款单-回单上传失败");
            }
        }
    }
}
