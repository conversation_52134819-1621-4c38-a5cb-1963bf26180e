package com.smallhospital.model.vo;

import com.smallhospital.model.ElContract;
import com.smallhospital.model.ElContractSku;

import java.util.List;

public class ELContractVO extends ElContract {

    private Integer contractId;
    private String traderName;

    private String signDateStr;

    private String contractValidityDateStartStr;

    private String contractValidityDateEndStr;

    private String ownerName;

    public Integer getContractId() {
        return contractId;
    }

    public void setContractId(Integer contractId) {
        this.contractId = contractId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getSignDateStr() {
        return signDateStr;
    }

    public void setSignDateStr(String signDateStr) {
        this.signDateStr = signDateStr;
    }

    public String getContractValidityDateStartStr() {
        return contractValidityDateStartStr;
    }

    public void setContractValidityDateStartStr(String contractValidityDateStartStr) {
        this.contractValidityDateStartStr = contractValidityDateStartStr;
    }

    public String getContractValidityDateEndStr() {
        return contractValidityDateEndStr;
    }

    public void setContractValidityDateEndStr(String contractValidityDateEndStr) {
        this.contractValidityDateEndStr = contractValidityDateEndStr;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }
}
