package com.vedeng.erp.buyorder.service;

import com.vedeng.erp.buyorder.dto.PurchaseDeliveryBatchDetailDto;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 直发采购批次信息接口api
 */
public interface PurchaseDeliveryBatchDetailService {
    /**
     * @param purchaseDeliveryDirectBatchDetailId
     * @return
     * <AUTHOR>
     * @desc 根据批次详情id查询物流明细id
     */
    Integer queryExpressDetailIdById(Integer purchaseDeliveryDirectBatchDetailId);

    /**
     * @param purchaseDeliveryDirectBatchDetailId
     * @param num
     * @return
     * <AUTHOR>
     */
    int updateOutNumById(Integer purchaseDeliveryDirectBatchDetailId, Integer num);

    /**
     * @param purchaseDeliveryDirectBatchDetailId
     * @return
     * <AUTHOR>
     * @desc 根据批次详情id查询已出库数量
     */
    PurchaseDeliveryBatchDetailDto queryInfoByDetailId(Integer purchaseDeliveryDirectBatchDetailId);

    /**
     * 更新MWS直发入库数量
     * @param purchaseDeliveryDirectBatchDetailId
     * @param num
     * @return
     */
    int updateArrivalCountByDetailId(Integer purchaseDeliveryDirectBatchDetailId, Integer num);

    /**
     * @param expressDetailId
     * @return
     * <AUTHOR>
     * @desc 根据快递单详情id查询批次信息
     */
    List<PurchaseDeliveryBatchDetailDto> queryInfoByExpressDetailId(Integer expressDetailId);

    /**
     * <AUTHOR>
     * @desc 根据明细id查询主表id
     * @param purchaseDeliveryDirectBatchDetailId
     * @return
     */
    Integer queryMainIdByDetailId(Integer purchaseDeliveryDirectBatchDetailId);

    /**
     * <AUTHOR>
     * @desc 根据主表id查询所有同行单明细信息
     * @param purchaseDeliveryDirectBatchInfoId
     * @return
     */
    List<PurchaseDeliveryBatchDetailDto> queryInfoByMainId(Integer purchaseDeliveryDirectBatchInfoId);
}
