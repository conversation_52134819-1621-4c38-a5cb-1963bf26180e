function getZhanQiContentForLineWrap(divId,saleOrderId){
    // $("[name='zhanqi"+divId+"']").html(
    //     "<style>.tip-hover:hover+.customernameshow{display: block;}</style><span title='账期订单：需合同回传并审批通过后，方可发货。' style='cursor: default;margin-left: -25px;'><i class='iconredsigh ml4 contorlIcon' style='margin-left: 0px;vertical-align: -1px;'></i>未发货<font color='red'>(账期发货限制)</font></span><i class='iconbluemouth tip-hover'></i><div class='pos_abs customernameshow mouthControlPos' style='left:calc(100% + 10px)'>账期订单：需合同回传并审批通过后，方可发货。</div>"
    // );
    // return ;
    $.ajax({
        type: "GET",
        url: "/orderstream/saleorder/periodOrderCanDeliver.do?saleOrderId=" + saleOrderId,
        dataType:'json',
        success: function(data){
            if (data.code === 0){
                if(data.data == true){
                    $("[name='zhanqi"+divId+"']").html(
                        "<style>.tip-hover:hover+.customernameshow{display: block;}</style><span class='tip-hover'style='cursor: default;margin-left: -25px;'><i class='iconredsigh ml4 contorlIcon' style='margin-left: 0px;vertical-align: -1px;'></i>未发货<font color='red'>(账期发货限制)</font></span> <div class='pos_abs customernameshow mouthControlPos' style='left:calc(100% + 10px);color:black;'>账期订单：需合同回传并审批通过后，方可发货。</div>"
                    );
                }
            }

        },
        error:function(data){
            console.log('error');
        }
    });
}

$(function () {
    $('.J-line-wrap').each(function () {
        var $lineWrap = $(this);
        var lineData = $lineWrap.data('json');

        const currentTime = Date.now();

        var showZhanqiFahuo = false;
        var showZhanQiFahuoSaleOrderId = false;
        var saleorderIdElement = document.getElementById('saleorderId');
        if (saleorderIdElement && saleorderIdElement.type === 'hidden') {
            showZhanQiFahuoSaleOrderId = true;
        }

        $.each(lineData, function (i, item) {
            var status = item.status || 0;
            var statusCurrent = false;
            if(status == 2 && item.label=='待发货'){
                statusCurrent = true;
                showZhanqiFahuo = true;
            }
            var statusClass = ['', 't-line-done', 't-line-doing', 't-line-async'][status];
            var jumpDom = item.jump ? 'data-jump="' + item.jump + '"' : '';
            var tipDom = item.tip ?
                ('<div class="t-line-item-tip"'+(statusCurrent?
                    ' name="zhanqi'+currentTime+'"':'')+'>' + item.tip + '</div>') :
                '';
            var lockDom = item.lock ? '<div class="t-line-lock"><i class="lock-icon"></i><div class="t-line-lock-txt">'+ item.lock +'</div></div>' : '';

            $lineWrap.append(
                '<div class="t-line-item ' + statusClass + '" '+ jumpDom +'>' +
                '<div class="t-line-item-icon">' + (i+1) + '</div>' +
                '<div class="t-line-item-cnt">' +
                '<div class="t-line-item-txt">' + item.label + '</div>' +
                tipDom +
                lockDom +
                '</div>' +
                '</div>'
            )


            if(item.fail ===1){
                $lineWrap.addClass('red-line');
            }
        })

        $('[data-jump]', $lineWrap).click(function () {
            var id = $(this).data('jump');
            console.log(id);
            var top = $(id).offset().top;

            $('html,body').animate({ scrollTop: top - 50 }, 300);
        })

        if(showZhanqiFahuo && showZhanQiFahuoSaleOrderId){//页面存在saleOrderId这个元素且符合待发货的条件,调用后台接口进行检查
            var saleOrderId = $("input[name='saleorderId']").val();
            getZhanQiContentForLineWrap(currentTime,saleOrderId);

        }


    })

})