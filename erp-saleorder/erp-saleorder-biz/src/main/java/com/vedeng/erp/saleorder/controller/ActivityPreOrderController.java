package com.vedeng.erp.saleorder.controller;

import com.ezadmin.web.EzResult;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.erp.saleorder.dao.ActivityPreOrderMapper;
import com.vedeng.erp.saleorder.domain.entity.ActivityPreOrderEntity;
import com.vedeng.erp.saleorder.service.ActivityPreOrderService;
import com.vedeng.order.model.Saleorder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;

/**
 * @title: ActivityPreOrderController
 * <AUTHOR>
 * @Date: 2022/12/23 16:30
 */
@Controller
@RequestMapping("/orderstream/activityPreSaleorder")
@ExceptionController
@Slf4j
public class ActivityPreOrderController extends BaseController {

    @Autowired
    private ActivityPreOrderService activityPreOrderService;

    @NoNeedAccessAuthorization
    @RequestMapping("/close")
    @ResponseBody
    public EzResult close(HttpServletRequest request,@RequestParam("_CHECKD_IDS") String checkIds,
                         ModelAndView mv)  {
        log.info("开始关闭商机{}",checkIds);
        try {
            String[] preId = StringUtils.split(checkIds, ",");
            if (ArrayUtils.isNotEmpty(preId)) {
                activityPreOrderService.closeActivityPreOrder(preId);
            }
            return EzResult.instance();
        }catch (Exception e){
            logger.error("",e);
            return EzResult.instance().fail().setMessage("关闭失败");
        }
    }
    @NoNeedAccessAuthorization
    @RequestMapping("/checkActiveNum")
    @ResponseBody
    public EzResult checkActiveNum(HttpServletRequest request,Integer traderId,Integer activityPreOrderId,
                          ModelAndView mv)  {

        try {
            ActivityPreOrderEntity activityPreOrder= activityPreOrderService.queryById(activityPreOrderId);

            if(activityPreOrder==null||activityPreOrder.getOrderStatus()== ErpConst.FOUR){
                return EzResult.instance().fail();
            }




            return EzResult.instance();
        }catch (Exception e){
            logger.error("",e);
            return EzResult.instance().fail().setMessage("关闭失败");
        }
    }


    @NoNeedAccessAuthorization
    @RequestMapping("/toOrder")
    public String toOrder(HttpServletRequest request,@Param("activityPreOrderId") Integer activityPreOrderId,
                         ModelAndView mv){
        log.info("开始商机转订单{}",activityPreOrderId);

        //  ez列表里面，如果关联了订单，跳转到： /orderstream/saleorder/edit.do?saleorderId=xxxx ,
        //        如果没有关联订单跳转到：/orderstream/saleorder/addOrder.do?activityPreOrderId=xxx
        //二次判断是否可以转订单，商机状态必须为进行中，
        String result="";
        ActivityPreOrderEntity activityPreOrder= activityPreOrderService.queryById(activityPreOrderId);

        if(activityPreOrder==null||activityPreOrder.getOrderStatus()== ErpConst.FOUR){
                return "common/404";
        }
        if(activityPreOrder.getSaleorderId()!=null&&activityPreOrder.getSaleorderId()>0){
            result="/orderstream/saleorder/edit.do?saleorderId="+activityPreOrder.getSaleorderId();
        }
        else{
            result="/ezadmin/form/form-createOrder?activityPreOrderId="+activityPreOrder.getActivityPreOrderId();
        }
        return "redirect:"+result;
    }

}
