package com.vedeng.erp.system.service;

import com.vedeng.erp.system.domain.entity.FlowOrderDetailEntity;
public interface FlowOrderDetailService{

    int deleteByPrimaryKey(Long flowOrderDetailId);

    int insert(FlowOrderDetailEntity record);

    int insertSelective(FlowOrderDetailEntity record);

    FlowOrderDetailEntity selectByPrimaryKey(Long flowOrderDetailId);

    int updateByPrimaryKeySelective(FlowOrderDetailEntity record);

    int updateByPrimaryKey(FlowOrderDetailEntity record);

}
