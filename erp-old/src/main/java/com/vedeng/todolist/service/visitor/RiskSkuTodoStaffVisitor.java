package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.model.User;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.dto.RiskSkuTodoDto;
import com.vedeng.todolist.dto.SupplyChainStaffTodoDtoBuild;
import com.vedeng.todolist.model.TodoList;
import com.wms.service.context.ThreadLocalContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class RiskSkuTodoStaffVisitor implements TodoVisitor<SupplyChainStaffTodoDtoBuild>{

    @Resource
    private TodoListMapper todoListMapper;

    @Override
    public void visitor(SupplyChainStaffTodoDtoBuild supplyChainStaffTodoDtoBuild) {

        User user = ThreadLocalContext.get("userInfo");

        List<TodoList> skuTodoList = todoListMapper.getUnHandledRiskCheckSkuTodoListByUserList(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA.getBuzSceneId(),Arrays.asList(user.getUserId()));

        if(CollectionUtils.isEmpty(skuTodoList)){
            return;
        }

        //前台数据的封装
        RiskSkuTodoDto riskCheckSkuTodo = new RiskSkuTodoDto();

        Set<Integer> noRepeatSet = skuTodoList.parallelStream().map(TodoList::getBuzId).collect(Collectors.toSet());
        riskCheckSkuTodo.setSkuTodoListCount(noRepeatSet.size());

        Integer riskCheckSkuTodoListGroupByOrder = skuTodoList.parallelStream().map(TodoList::getBuzExtra).filter(StringUtils::isNotBlank).collect(Collectors.toSet()).size();
        riskCheckSkuTodo.setSkuTodoListCountGroupByOrder(riskCheckSkuTodoListGroupByOrder);

        skuTodoList.stream().forEach(todo-> {
            todo.setProcessTime(todo.getStatus() == 1 ? todo.getUpdateTime() - todo.getAddTime() : System.currentTimeMillis() - todo.getAddTime());
        });

        //总处理时长
        Long totalProcessTime = skuTodoList.stream().mapToLong(TodoList::getProcessTime).sum();

        //总的订单数
        int totalOrderSum = skuTodoList.size();

        //平均时间
        String avgTime = new BigDecimal(totalProcessTime / (totalOrderSum * 1000 * 60 * 60.0)).setScale(1, BigDecimal.ROUND_HALF_UP).toPlainString();

        //最大时间
        String maxTime = skuTodoList.stream().map(TodoList::getProcessTime).max(Long::compareTo)
                                             .map(p-> new BigDecimal(p /(1000 * 60 * 60.0)).setScale(1, BigDecimal.ROUND_HALF_UP).toPlainString()).get();

        riskCheckSkuTodo.setAvgWaitTime(avgTime);
        riskCheckSkuTodo.setMaxWaitTime(maxTime);

        supplyChainStaffTodoDtoBuild.get().setRiskSkuTodoDto(riskCheckSkuTodo);
    }
}
