package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeSalesVatSpecialInvoiceCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSalesVatSpecialInvoiceEntity;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatSpecialInvoiceDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSalesVatSpecialInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSalesVatSpecialInvoiceConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeSalesVatSpecialInvoiceRepository;
import com.vedeng.erp.kingdee.service.KingDeeSalesVatSpecialInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeeSalesVatSpecialInvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/7 14:28
 */
@Service
@Slf4j
public class KingDeeSalesVatSpecialInvoiceServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeSalesVatSpecialInvoiceEntity,
        KingDeeSalesVatSpecialInvoiceDto,
        KingDeeSalesVatSpecialInvoiceCommand,
        KingDeeSalesVatSpecialInvoiceRepository,
        KingDeeSalesVatSpecialInvoiceConvertor,
        KingDeeSalesVatSpecialInvoiceCommandConvertor>
        implements KingDeeSalesVatSpecialInvoiceService, KingDeeSalesVatSpecialInvoiceApiService {
}