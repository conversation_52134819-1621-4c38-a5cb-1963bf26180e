package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 费用应收单
 * @date
 */
@NoArgsConstructor
@Data
@Getter
@Setter
public class KingDeeReceiveFeeCommand {

    /**
     * 单据内码 0：表示新增，非0：云星空系统单据FID值，表示修改
     */
    private String fid;

    /**
     * 单据日期 格式yyyy-MM-dd
     */
    private String fdate;
    /**
     * 贝登单据头ID 贝登单据头ID号（预留）
     */
    private String f_Qzok_Bddjtid;

    /**
     * 业务类型 默认FY
     */
    private String fbusinesstype;

    /**
     * 立账类型  默认填写1
     */
    private String fSetAccountType;
    /**
     * fEntityDetail
     */
    private List<FEntityDetail> fEntityDetail = new ArrayList<>();

    /**
     * 单据类型 填单据类型编码，默认"YSD01_SYS"
     */
    private KingDeeNumberCommand fBillTypeID = new KingDeeNumberCommand();

    /**
     * 客户 填写客户编码
     */
    private KingDeeNumberCommand fcustomerid = new KingDeeNumberCommand();

    /**
     * 结算组织 填写组织编码
     */
    private KingDeeNumberCommand fsettleorgid = new KingDeeNumberCommand();


    /**
     * FEntityDetail
     */
    @NoArgsConstructor
    @Data
    public static class FEntityDetail {

        /**
         * 计价数量 填写数量（退货负数）
         */
        private String fPriceQty;
        /**
         * 含税单价 填写单价
         */
        private String fTaxPrice;
        /**
         * 税率%
         */
        private String fEntryTaxRate;
        /**
         * 贝登单据行ID
         */
        private String f_Qzok_Bddjhid;

        //Fnumber
        /**
         * 费用项目编码 填写金蝶的费用项目编码
         */
        private KingDeeNumberCommand fcostid = new KingDeeNumberCommand();

    }

}
