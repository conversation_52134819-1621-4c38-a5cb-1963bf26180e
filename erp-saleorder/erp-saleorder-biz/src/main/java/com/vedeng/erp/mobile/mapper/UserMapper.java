package com.vedeng.erp.mobile.mapper;

import com.vedeng.erp.mobile.domain.Position;
import com.vedeng.erp.mobile.domain.User;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import javax.inject.Named;

//@Named("mobileUserMapper")
@Repository("mobileUserMapper")
public interface UserMapper {
    int deleteByPrimaryKey(Integer userId);

    int insert(User record);

    int insertOrUpdate(User record);

    int insertOrUpdateSelective(User record);

    int insertSelective(User record);

    User selectByPrimaryKey(Integer userId);

    int updateByPrimaryKeySelective(User record);

    int updateByPrimaryKey(User record);

    int updateBatch(List<User> list);

    int updateBatchSelective(List<User> list);

    int batchInsert(@Param("list") List<User> list);

    /**
     * 获取用户的USER_ORG_IDS(所有子部门)
     */
    List<Integer> getOrgIdsByUsersOrgIds(Integer userId);

    /**
     * 查询用户职位
     */
    List<Position> getPositionByUserId(Integer userId);

    List<User> getUserByPositTypes(User subUser);

    /**
     * 批量查询用户
     */
    List<User> getUserByUserIds(List<Integer> userId);
}