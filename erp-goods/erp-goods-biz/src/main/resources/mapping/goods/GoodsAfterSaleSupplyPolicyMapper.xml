<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.GoodsAfterSaleSupplyPolicyMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.AfterSaleSupplyPolicy">
    <!--@mbg.generated-->
    <!--@Table T_AFTER_SALE_SUPPLY_POLICY-->
    <id column="SUPPLY_POLICY_ID" jdbcType="BIGINT" property="supplyPolicyId" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="TRADER_ID" jdbcType="BIGINT" property="traderId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="SERVICE_PROVIDER_TYPE" jdbcType="INTEGER" property="serviceProviderType" />
    <result column="INSTALL_POLICY_INSTALL_TYPE" jdbcType="INTEGER" property="installPolicyInstallType" />
    <result column="INSTALL_POLICY_INSTALL_AREA" jdbcType="VARCHAR" property="installPolicyInstallArea" />
    <result column="INSTALL_POLICY_INSTALL_FEE" jdbcType="DECIMAL" property="installPolicyInstallFee" />
    <result column="INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION" jdbcType="INTEGER" property="installPolicyHaveInstallationQualification" />
    <result column="INSTALL_POLICY_FREE_REMOTE_INSTALL" jdbcType="INTEGER" property="installPolicyFreeRemoteInstall" />
    <result column="INSTALL_POLICY_RESPONSE_TIME" jdbcType="VARCHAR" property="installPolicyResponseTime" />
    <result column="INSTALL_POLICY_VISIT_TIME" jdbcType="VARCHAR" property="installPolicyVisitTime" />
    <result column="INSTALL_POLICY_INSTALL_TIME" jdbcType="VARCHAR" property="installPolicyInstallTime" />
    <result column="TECHNICAL_DIRECT_SUPPLY_MAINTAIN" jdbcType="INTEGER" property="technicalDirectSupplyMaintain" />
    <result column="TECHNICAL_DIRECT_RESPONSE_TIME" jdbcType="VARCHAR" property="technicalDirectResponseTime" />
    <result column="TECHNICAL_DIRECT_EFFECT_TIME" jdbcType="VARCHAR" property="technicalDirectEffectTime" />
    <result column="GUARANTEE_POLICY_IS_GUARANTEE" jdbcType="INTEGER" property="guaranteePolicyIsGuarantee" />
    <result column="GUARANTEE_POLICY_GUARANTEE_TYPE" jdbcType="VARCHAR" property="guaranteePolicyGuaranteeType" />
    <result column="GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD" jdbcType="VARCHAR" property="guaranteePolicyHostGuaranteePeriod" />
    <result column="GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD" jdbcType="VARCHAR" property="guaranteePolicyPartsGuaranteePeriod" />
    <result column="GUARANTEE_POLICY_CYCLE_CALTYPE" jdbcType="INTEGER" property="guaranteePolicyCycleCaltype" />
    <result column="GUARANTEE_POLICY_AREA" jdbcType="INTEGER" property="guaranteePolicyArea" />
    <result column="GUARANTEE_POLICY_AREA_COMMENT" jdbcType="VARCHAR" property="guaranteePolicyAreaComment" />
    <result column="GUARANTEE_POLICY_RESPONSE_TIME" jdbcType="VARCHAR" property="guaranteePolicyResponseTime" />
    <result column="GUARANTEE_POLICY_VISIT_TIME" jdbcType="VARCHAR" property="guaranteePolicyVisitTime" />
    <result column="GUARANTEE_POLICY_REPAIRE_TIME" jdbcType="VARCHAR" property="guaranteePolicyRepaireTime" />
    <result column="GUARANTEE_POLICY_REPAIRE_COMMENT" jdbcType="VARCHAR" property="guaranteePolicyRepaireComment" />
    <result column="RETURN_POLICY_SUPPORT_RETURN" jdbcType="INTEGER" property="returnPolicySupportReturn" />
    <result column="RETURN_POLICY_CONDITION" jdbcType="VARCHAR" property="returnPolicyCondition" />
    <result column="RETURN_POLICY_NEED_IDENTIFY" jdbcType="INTEGER" property="returnPolicyNeedIdentify" />
    <result column="RETURN_POLICY_IDENTIFY_TYPE" jdbcType="VARCHAR" property="returnPolicyIdentifyType" />
    <result column="RETURN_POLICY_RETURN_PERIOD" jdbcType="VARCHAR" property="returnPolicyReturnPeriod" />
    <result column="RETURN_POLICY_CYCLE_CALTYP" jdbcType="INTEGER" property="returnPolicyCycleCaltyp" />
    <result column="RETURN_POLICY_PACKAGING_REQUIREMENTS" jdbcType="VARCHAR" property="returnPolicyPackagingRequirements" />
    <result column="RETURN_POLICY_RETURN_COMMENTS" jdbcType="VARCHAR" property="returnPolicyReturnComments" />
    <result column="EXCHANGE_POLICY_SUPPORT_CHANGE" jdbcType="INTEGER" property="exchangePolicySupportChange" />
    <result column="EXCHANGE_POLICY_EXCHANGE_CONTITION" jdbcType="VARCHAR" property="exchangePolicyExchangeContition" />
    <result column="EXCHANGE_POLICY_NEED_IDENTIFY" jdbcType="INTEGER" property="exchangePolicyNeedIdentify" />
    <result column="EXCHANGE_POLICY_IDENTIFY_TYPE" jdbcType="VARCHAR" property="exchangePolicyIdentifyType" />
    <result column="EXCHANGE_POLICY_EXCHANGE_PERIOD" jdbcType="VARCHAR" property="exchangePolicyExchangePeriod" />
    <result column="EXCHANGE_POLICY_CYCLE_CALTYP" jdbcType="INTEGER" property="exchangePolicyCycleCaltyp" />
    <result column="EXCHANGE_POLICY_PACKAGING_REQUIREMENTS" jdbcType="VARCHAR" property="exchangePolicyPackagingRequirements" />
    <result column="EXCHANGE_POLICY_EXCHANGE_COMMENTS" jdbcType="VARCHAR" property="exchangePolicyExchangeComments" />
    <result column="PAROLE_POLICY_SUPPORT_REPAIR" jdbcType="INTEGER" property="parolePolicySupportRepair" />
    <result column="PAROLE_POLICY_SUPPORT_RENOVATION" jdbcType="INTEGER" property="parolePolicySupportRenovation" />
    <result column="PAROLE_POLICY_SUPPLY_BOX" jdbcType="INTEGER" property="parolePolicySupplyBox" />
    <result column="PAROLE_POLICY_SUPPLY_ATTACHMENT" jdbcType="INTEGER" property="parolePolicySupplyAttachment" />
    <result column="OVERDUE_POLICY_SUPPLY_BACKUP" jdbcType="VARCHAR" property="overduePolicySupplyBackup" />
    <result column="OVERDUE_POLICY_DETAIL" jdbcType="VARCHAR" property="overduePolicyDetail" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="UPDATOR" jdbcType="VARCHAR" property="updator" />
    <result column="ADD_TIME" jdbcType="VARCHAR" property="addTime" />
    <result column="MOD_TIME" jdbcType="VARCHAR" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SUPPLY_POLICY_ID, SKU_NO, TRADER_ID, TRADER_NAME, SERVICE_PROVIDER_TYPE, INSTALL_POLICY_INSTALL_TYPE, 
    INSTALL_POLICY_INSTALL_AREA, INSTALL_POLICY_INSTALL_FEE, INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION, 
    INSTALL_POLICY_FREE_REMOTE_INSTALL, INSTALL_POLICY_RESPONSE_TIME, INSTALL_POLICY_VISIT_TIME, 
    INSTALL_POLICY_INSTALL_TIME, TECHNICAL_DIRECT_SUPPLY_MAINTAIN, TECHNICAL_DIRECT_RESPONSE_TIME, 
    TECHNICAL_DIRECT_EFFECT_TIME, GUARANTEE_POLICY_IS_GUARANTEE, GUARANTEE_POLICY_GUARANTEE_TYPE, 
    GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD, GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD, 
    GUARANTEE_POLICY_CYCLE_CALTYPE, GUARANTEE_POLICY_AREA, GUARANTEE_POLICY_AREA_COMMENT, 
    GUARANTEE_POLICY_RESPONSE_TIME, GUARANTEE_POLICY_VISIT_TIME, GUARANTEE_POLICY_REPAIRE_TIME, 
    GUARANTEE_POLICY_REPAIRE_COMMENT, RETURN_POLICY_SUPPORT_RETURN, RETURN_POLICY_CONDITION, 
    RETURN_POLICY_NEED_IDENTIFY, RETURN_POLICY_IDENTIFY_TYPE, RETURN_POLICY_RETURN_PERIOD, 
    RETURN_POLICY_CYCLE_CALTYP, RETURN_POLICY_PACKAGING_REQUIREMENTS, RETURN_POLICY_RETURN_COMMENTS, 
    EXCHANGE_POLICY_SUPPORT_CHANGE, EXCHANGE_POLICY_EXCHANGE_CONTITION, EXCHANGE_POLICY_NEED_IDENTIFY, 
    EXCHANGE_POLICY_IDENTIFY_TYPE, EXCHANGE_POLICY_EXCHANGE_PERIOD, EXCHANGE_POLICY_CYCLE_CALTYP, 
    EXCHANGE_POLICY_PACKAGING_REQUIREMENTS, EXCHANGE_POLICY_EXCHANGE_COMMENTS, PAROLE_POLICY_SUPPORT_REPAIR, 
    PAROLE_POLICY_SUPPORT_RENOVATION, PAROLE_POLICY_SUPPLY_BOX, PAROLE_POLICY_SUPPLY_ATTACHMENT, 
    OVERDUE_POLICY_SUPPLY_BACKUP, OVERDUE_POLICY_DETAIL, CREATOR, UPDATOR, ADD_TIME, 
    MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_SUPPLY_POLICY
    where SUPPLY_POLICY_ID = #{supplyPolicyId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_AFTER_SALE_SUPPLY_POLICY
    where SUPPLY_POLICY_ID = #{supplyPolicyId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="SUPPLY_POLICY_ID" keyProperty="supplyPolicyId" parameterType="com.vedeng.goods.domain.entity.AfterSaleSupplyPolicy" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALE_SUPPLY_POLICY (SKU_NO, TRADER_ID, TRADER_NAME, 
      SERVICE_PROVIDER_TYPE, INSTALL_POLICY_INSTALL_TYPE, 
      INSTALL_POLICY_INSTALL_AREA, INSTALL_POLICY_INSTALL_FEE, 
      INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION, INSTALL_POLICY_FREE_REMOTE_INSTALL, 
      INSTALL_POLICY_RESPONSE_TIME, INSTALL_POLICY_VISIT_TIME, 
      INSTALL_POLICY_INSTALL_TIME, TECHNICAL_DIRECT_SUPPLY_MAINTAIN, 
      TECHNICAL_DIRECT_RESPONSE_TIME, TECHNICAL_DIRECT_EFFECT_TIME, 
      GUARANTEE_POLICY_IS_GUARANTEE, GUARANTEE_POLICY_GUARANTEE_TYPE, 
      GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD, GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD, 
      GUARANTEE_POLICY_CYCLE_CALTYPE, GUARANTEE_POLICY_AREA, 
      GUARANTEE_POLICY_AREA_COMMENT, GUARANTEE_POLICY_RESPONSE_TIME, 
      GUARANTEE_POLICY_VISIT_TIME, GUARANTEE_POLICY_REPAIRE_TIME, 
      GUARANTEE_POLICY_REPAIRE_COMMENT, RETURN_POLICY_SUPPORT_RETURN, 
      RETURN_POLICY_CONDITION, RETURN_POLICY_NEED_IDENTIFY, 
      RETURN_POLICY_IDENTIFY_TYPE, RETURN_POLICY_RETURN_PERIOD, 
      RETURN_POLICY_CYCLE_CALTYP, RETURN_POLICY_PACKAGING_REQUIREMENTS, 
      RETURN_POLICY_RETURN_COMMENTS, EXCHANGE_POLICY_SUPPORT_CHANGE, 
      EXCHANGE_POLICY_EXCHANGE_CONTITION, EXCHANGE_POLICY_NEED_IDENTIFY, 
      EXCHANGE_POLICY_IDENTIFY_TYPE, EXCHANGE_POLICY_EXCHANGE_PERIOD, 
      EXCHANGE_POLICY_CYCLE_CALTYP, EXCHANGE_POLICY_PACKAGING_REQUIREMENTS, 
      EXCHANGE_POLICY_EXCHANGE_COMMENTS, PAROLE_POLICY_SUPPORT_REPAIR, 
      PAROLE_POLICY_SUPPORT_RENOVATION, PAROLE_POLICY_SUPPLY_BOX, 
      PAROLE_POLICY_SUPPLY_ATTACHMENT, OVERDUE_POLICY_SUPPLY_BACKUP, 
      OVERDUE_POLICY_DETAIL, CREATOR, UPDATOR, 
      ADD_TIME, MOD_TIME)
    values (#{skuNo,jdbcType=VARCHAR}, #{traderId,jdbcType=BIGINT}, #{traderName,jdbcType=VARCHAR}, 
      #{serviceProviderType,jdbcType=INTEGER}, #{installPolicyInstallType,jdbcType=INTEGER}, 
      #{installPolicyInstallArea,jdbcType=VARCHAR}, #{installPolicyInstallFee,jdbcType=DECIMAL}, 
      #{installPolicyHaveInstallationQualification,jdbcType=INTEGER}, #{installPolicyFreeRemoteInstall,jdbcType=INTEGER}, 
      #{installPolicyResponseTime,jdbcType=VARCHAR}, #{installPolicyVisitTime,jdbcType=VARCHAR}, 
      #{installPolicyInstallTime,jdbcType=VARCHAR}, #{technicalDirectSupplyMaintain,jdbcType=INTEGER}, 
      #{technicalDirectResponseTime,jdbcType=VARCHAR}, #{technicalDirectEffectTime,jdbcType=VARCHAR}, 
      #{guaranteePolicyIsGuarantee,jdbcType=INTEGER}, #{guaranteePolicyGuaranteeType,jdbcType=VARCHAR}, 
      #{guaranteePolicyHostGuaranteePeriod,jdbcType=VARCHAR}, #{guaranteePolicyPartsGuaranteePeriod,jdbcType=VARCHAR}, 
      #{guaranteePolicyCycleCaltype,jdbcType=INTEGER}, #{guaranteePolicyArea,jdbcType=INTEGER}, 
      #{guaranteePolicyAreaComment,jdbcType=VARCHAR}, #{guaranteePolicyResponseTime,jdbcType=VARCHAR}, 
      #{guaranteePolicyVisitTime,jdbcType=VARCHAR}, #{guaranteePolicyRepaireTime,jdbcType=VARCHAR}, 
      #{guaranteePolicyRepaireComment,jdbcType=VARCHAR}, #{returnPolicySupportReturn,jdbcType=INTEGER}, 
      #{returnPolicyCondition,jdbcType=VARCHAR}, #{returnPolicyNeedIdentify,jdbcType=INTEGER}, 
      #{returnPolicyIdentifyType,jdbcType=VARCHAR}, #{returnPolicyReturnPeriod,jdbcType=VARCHAR}, 
      #{returnPolicyCycleCaltyp,jdbcType=INTEGER}, #{returnPolicyPackagingRequirements,jdbcType=VARCHAR}, 
      #{returnPolicyReturnComments,jdbcType=VARCHAR}, #{exchangePolicySupportChange,jdbcType=INTEGER}, 
      #{exchangePolicyExchangeContition,jdbcType=VARCHAR}, #{exchangePolicyNeedIdentify,jdbcType=INTEGER}, 
      #{exchangePolicyIdentifyType,jdbcType=VARCHAR}, #{exchangePolicyExchangePeriod,jdbcType=VARCHAR}, 
      #{exchangePolicyCycleCaltyp,jdbcType=INTEGER}, #{exchangePolicyPackagingRequirements,jdbcType=VARCHAR}, 
      #{exchangePolicyExchangeComments,jdbcType=VARCHAR}, #{parolePolicySupportRepair,jdbcType=INTEGER}, 
      #{parolePolicySupportRenovation,jdbcType=INTEGER}, #{parolePolicySupplyBox,jdbcType=INTEGER}, 
      #{parolePolicySupplyAttachment,jdbcType=INTEGER}, #{overduePolicySupplyBackup,jdbcType=VARCHAR}, 
      #{overduePolicyDetail,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{updator,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=VARCHAR}, #{modTime,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="SUPPLY_POLICY_ID" keyProperty="supplyPolicyId" parameterType="com.vedeng.goods.domain.entity.AfterSaleSupplyPolicy" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALE_SUPPLY_POLICY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="serviceProviderType != null">
        SERVICE_PROVIDER_TYPE,
      </if>
      <if test="installPolicyInstallType != null">
        INSTALL_POLICY_INSTALL_TYPE,
      </if>
      <if test="installPolicyInstallArea != null">
        INSTALL_POLICY_INSTALL_AREA,
      </if>
      <if test="installPolicyInstallFee != null">
        INSTALL_POLICY_INSTALL_FEE,
      </if>
      <if test="installPolicyHaveInstallationQualification != null">
        INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION,
      </if>
      <if test="installPolicyFreeRemoteInstall != null">
        INSTALL_POLICY_FREE_REMOTE_INSTALL,
      </if>
      <if test="installPolicyResponseTime != null">
        INSTALL_POLICY_RESPONSE_TIME,
      </if>
      <if test="installPolicyVisitTime != null">
        INSTALL_POLICY_VISIT_TIME,
      </if>
      <if test="installPolicyInstallTime != null">
        INSTALL_POLICY_INSTALL_TIME,
      </if>
      <if test="technicalDirectSupplyMaintain != null">
        TECHNICAL_DIRECT_SUPPLY_MAINTAIN,
      </if>
      <if test="technicalDirectResponseTime != null">
        TECHNICAL_DIRECT_RESPONSE_TIME,
      </if>
      <if test="technicalDirectEffectTime != null">
        TECHNICAL_DIRECT_EFFECT_TIME,
      </if>
      <if test="guaranteePolicyIsGuarantee != null">
        GUARANTEE_POLICY_IS_GUARANTEE,
      </if>
      <if test="guaranteePolicyGuaranteeType != null">
        GUARANTEE_POLICY_GUARANTEE_TYPE,
      </if>
      <if test="guaranteePolicyHostGuaranteePeriod != null">
        GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD,
      </if>
      <if test="guaranteePolicyPartsGuaranteePeriod != null">
        GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD,
      </if>
      <if test="guaranteePolicyCycleCaltype != null">
        GUARANTEE_POLICY_CYCLE_CALTYPE,
      </if>
      <if test="guaranteePolicyArea != null">
        GUARANTEE_POLICY_AREA,
      </if>
      <if test="guaranteePolicyAreaComment != null">
        GUARANTEE_POLICY_AREA_COMMENT,
      </if>
      <if test="guaranteePolicyResponseTime != null">
        GUARANTEE_POLICY_RESPONSE_TIME,
      </if>
      <if test="guaranteePolicyVisitTime != null">
        GUARANTEE_POLICY_VISIT_TIME,
      </if>
      <if test="guaranteePolicyRepaireTime != null">
        GUARANTEE_POLICY_REPAIRE_TIME,
      </if>
      <if test="guaranteePolicyRepaireComment != null">
        GUARANTEE_POLICY_REPAIRE_COMMENT,
      </if>
      <if test="returnPolicySupportReturn != null">
        RETURN_POLICY_SUPPORT_RETURN,
      </if>
      <if test="returnPolicyCondition != null">
        RETURN_POLICY_CONDITION,
      </if>
      <if test="returnPolicyNeedIdentify != null">
        RETURN_POLICY_NEED_IDENTIFY,
      </if>
      <if test="returnPolicyIdentifyType != null">
        RETURN_POLICY_IDENTIFY_TYPE,
      </if>
      <if test="returnPolicyReturnPeriod != null">
        RETURN_POLICY_RETURN_PERIOD,
      </if>
      <if test="returnPolicyCycleCaltyp != null">
        RETURN_POLICY_CYCLE_CALTYP,
      </if>
      <if test="returnPolicyPackagingRequirements != null">
        RETURN_POLICY_PACKAGING_REQUIREMENTS,
      </if>
      <if test="returnPolicyReturnComments != null">
        RETURN_POLICY_RETURN_COMMENTS,
      </if>
      <if test="exchangePolicySupportChange != null">
        EXCHANGE_POLICY_SUPPORT_CHANGE,
      </if>
      <if test="exchangePolicyExchangeContition != null">
        EXCHANGE_POLICY_EXCHANGE_CONTITION,
      </if>
      <if test="exchangePolicyNeedIdentify != null">
        EXCHANGE_POLICY_NEED_IDENTIFY,
      </if>
      <if test="exchangePolicyIdentifyType != null">
        EXCHANGE_POLICY_IDENTIFY_TYPE,
      </if>
      <if test="exchangePolicyExchangePeriod != null">
        EXCHANGE_POLICY_EXCHANGE_PERIOD,
      </if>
      <if test="exchangePolicyCycleCaltyp != null">
        EXCHANGE_POLICY_CYCLE_CALTYP,
      </if>
      <if test="exchangePolicyPackagingRequirements != null">
        EXCHANGE_POLICY_PACKAGING_REQUIREMENTS,
      </if>
      <if test="exchangePolicyExchangeComments != null">
        EXCHANGE_POLICY_EXCHANGE_COMMENTS,
      </if>
      <if test="parolePolicySupportRepair != null">
        PAROLE_POLICY_SUPPORT_REPAIR,
      </if>
      <if test="parolePolicySupportRenovation != null">
        PAROLE_POLICY_SUPPORT_RENOVATION,
      </if>
      <if test="parolePolicySupplyBox != null">
        PAROLE_POLICY_SUPPLY_BOX,
      </if>
      <if test="parolePolicySupplyAttachment != null">
        PAROLE_POLICY_SUPPLY_ATTACHMENT,
      </if>
      <if test="overduePolicySupplyBackup != null">
        OVERDUE_POLICY_SUPPLY_BACKUP,
      </if>
      <if test="overduePolicyDetail != null">
        OVERDUE_POLICY_DETAIL,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updator != null">
        UPDATOR,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=BIGINT},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="serviceProviderType != null">
        #{serviceProviderType,jdbcType=INTEGER},
      </if>
      <if test="installPolicyInstallType != null">
        #{installPolicyInstallType,jdbcType=INTEGER},
      </if>
      <if test="installPolicyInstallArea != null">
        #{installPolicyInstallArea,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallFee != null">
        #{installPolicyInstallFee,jdbcType=DECIMAL},
      </if>
      <if test="installPolicyHaveInstallationQualification != null">
        #{installPolicyHaveInstallationQualification,jdbcType=INTEGER},
      </if>
      <if test="installPolicyFreeRemoteInstall != null">
        #{installPolicyFreeRemoteInstall,jdbcType=INTEGER},
      </if>
      <if test="installPolicyResponseTime != null">
        #{installPolicyResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyVisitTime != null">
        #{installPolicyVisitTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallTime != null">
        #{installPolicyInstallTime,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectSupplyMaintain != null">
        #{technicalDirectSupplyMaintain,jdbcType=INTEGER},
      </if>
      <if test="technicalDirectResponseTime != null">
        #{technicalDirectResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectEffectTime != null">
        #{technicalDirectEffectTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyIsGuarantee != null">
        #{guaranteePolicyIsGuarantee,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyGuaranteeType != null">
        #{guaranteePolicyGuaranteeType,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyHostGuaranteePeriod != null">
        #{guaranteePolicyHostGuaranteePeriod,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyPartsGuaranteePeriod != null">
        #{guaranteePolicyPartsGuaranteePeriod,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyCycleCaltype != null">
        #{guaranteePolicyCycleCaltype,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyArea != null">
        #{guaranteePolicyArea,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyAreaComment != null">
        #{guaranteePolicyAreaComment,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyResponseTime != null">
        #{guaranteePolicyResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyVisitTime != null">
        #{guaranteePolicyVisitTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyRepaireTime != null">
        #{guaranteePolicyRepaireTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyRepaireComment != null">
        #{guaranteePolicyRepaireComment,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicySupportReturn != null">
        #{returnPolicySupportReturn,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyCondition != null">
        #{returnPolicyCondition,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyNeedIdentify != null">
        #{returnPolicyNeedIdentify,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyIdentifyType != null">
        #{returnPolicyIdentifyType,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyReturnPeriod != null">
        #{returnPolicyReturnPeriod,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyCycleCaltyp != null">
        #{returnPolicyCycleCaltyp,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyPackagingRequirements != null">
        #{returnPolicyPackagingRequirements,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyReturnComments != null">
        #{returnPolicyReturnComments,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicySupportChange != null">
        #{exchangePolicySupportChange,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyExchangeContition != null">
        #{exchangePolicyExchangeContition,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyNeedIdentify != null">
        #{exchangePolicyNeedIdentify,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyIdentifyType != null">
        #{exchangePolicyIdentifyType,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyExchangePeriod != null">
        #{exchangePolicyExchangePeriod,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyCycleCaltyp != null">
        #{exchangePolicyCycleCaltyp,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyPackagingRequirements != null">
        #{exchangePolicyPackagingRequirements,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyExchangeComments != null">
        #{exchangePolicyExchangeComments,jdbcType=VARCHAR},
      </if>
      <if test="parolePolicySupportRepair != null">
        #{parolePolicySupportRepair,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupportRenovation != null">
        #{parolePolicySupportRenovation,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupplyBox != null">
        #{parolePolicySupplyBox,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupplyAttachment != null">
        #{parolePolicySupplyAttachment,jdbcType=INTEGER},
      </if>
      <if test="overduePolicySupplyBackup != null">
        #{overduePolicySupplyBackup,jdbcType=VARCHAR},
      </if>
      <if test="overduePolicyDetail != null">
        #{overduePolicyDetail,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.domain.entity.AfterSaleSupplyPolicy">
    <!--@mbg.generated-->
    update T_AFTER_SALE_SUPPLY_POLICY
    <set>
      <if test="skuNo != null">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=BIGINT},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="serviceProviderType != null">
        SERVICE_PROVIDER_TYPE = #{serviceProviderType,jdbcType=INTEGER},
      </if>
      <if test="installPolicyInstallType != null">
        INSTALL_POLICY_INSTALL_TYPE = #{installPolicyInstallType,jdbcType=INTEGER},
      </if>
      <if test="installPolicyInstallArea != null">
        INSTALL_POLICY_INSTALL_AREA = #{installPolicyInstallArea,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallFee != null">
        INSTALL_POLICY_INSTALL_FEE = #{installPolicyInstallFee,jdbcType=DECIMAL},
      </if>
      <if test="installPolicyHaveInstallationQualification != null">
        INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION = #{installPolicyHaveInstallationQualification,jdbcType=INTEGER},
      </if>
      <if test="installPolicyFreeRemoteInstall != null">
        INSTALL_POLICY_FREE_REMOTE_INSTALL = #{installPolicyFreeRemoteInstall,jdbcType=INTEGER},
      </if>
      <if test="installPolicyResponseTime != null">
        INSTALL_POLICY_RESPONSE_TIME = #{installPolicyResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyVisitTime != null">
        INSTALL_POLICY_VISIT_TIME = #{installPolicyVisitTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallTime != null">
        INSTALL_POLICY_INSTALL_TIME = #{installPolicyInstallTime,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectSupplyMaintain != null">
        TECHNICAL_DIRECT_SUPPLY_MAINTAIN = #{technicalDirectSupplyMaintain,jdbcType=INTEGER},
      </if>
      <if test="technicalDirectResponseTime != null">
        TECHNICAL_DIRECT_RESPONSE_TIME = #{technicalDirectResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectEffectTime != null">
        TECHNICAL_DIRECT_EFFECT_TIME = #{technicalDirectEffectTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyIsGuarantee != null">
        GUARANTEE_POLICY_IS_GUARANTEE = #{guaranteePolicyIsGuarantee,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyGuaranteeType != null">
        GUARANTEE_POLICY_GUARANTEE_TYPE = #{guaranteePolicyGuaranteeType,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyHostGuaranteePeriod != null">
        GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD = #{guaranteePolicyHostGuaranteePeriod,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyPartsGuaranteePeriod != null">
        GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD = #{guaranteePolicyPartsGuaranteePeriod,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyCycleCaltype != null">
        GUARANTEE_POLICY_CYCLE_CALTYPE = #{guaranteePolicyCycleCaltype,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyArea != null">
        GUARANTEE_POLICY_AREA = #{guaranteePolicyArea,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyAreaComment != null">
        GUARANTEE_POLICY_AREA_COMMENT = #{guaranteePolicyAreaComment,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyResponseTime != null">
        GUARANTEE_POLICY_RESPONSE_TIME = #{guaranteePolicyResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyVisitTime != null">
        GUARANTEE_POLICY_VISIT_TIME = #{guaranteePolicyVisitTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyRepaireTime != null">
        GUARANTEE_POLICY_REPAIRE_TIME = #{guaranteePolicyRepaireTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyRepaireComment != null">
        GUARANTEE_POLICY_REPAIRE_COMMENT = #{guaranteePolicyRepaireComment,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicySupportReturn != null">
        RETURN_POLICY_SUPPORT_RETURN = #{returnPolicySupportReturn,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyCondition != null">
        RETURN_POLICY_CONDITION = #{returnPolicyCondition,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyNeedIdentify != null">
        RETURN_POLICY_NEED_IDENTIFY = #{returnPolicyNeedIdentify,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyIdentifyType != null">
        RETURN_POLICY_IDENTIFY_TYPE = #{returnPolicyIdentifyType,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyReturnPeriod != null">
        RETURN_POLICY_RETURN_PERIOD = #{returnPolicyReturnPeriod,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyCycleCaltyp != null">
        RETURN_POLICY_CYCLE_CALTYP = #{returnPolicyCycleCaltyp,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyPackagingRequirements != null">
        RETURN_POLICY_PACKAGING_REQUIREMENTS = #{returnPolicyPackagingRequirements,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyReturnComments != null">
        RETURN_POLICY_RETURN_COMMENTS = #{returnPolicyReturnComments,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicySupportChange != null">
        EXCHANGE_POLICY_SUPPORT_CHANGE = #{exchangePolicySupportChange,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyExchangeContition != null">
        EXCHANGE_POLICY_EXCHANGE_CONTITION = #{exchangePolicyExchangeContition,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyNeedIdentify != null">
        EXCHANGE_POLICY_NEED_IDENTIFY = #{exchangePolicyNeedIdentify,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyIdentifyType != null">
        EXCHANGE_POLICY_IDENTIFY_TYPE = #{exchangePolicyIdentifyType,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyExchangePeriod != null">
        EXCHANGE_POLICY_EXCHANGE_PERIOD = #{exchangePolicyExchangePeriod,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyCycleCaltyp != null">
        EXCHANGE_POLICY_CYCLE_CALTYP = #{exchangePolicyCycleCaltyp,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyPackagingRequirements != null">
        EXCHANGE_POLICY_PACKAGING_REQUIREMENTS = #{exchangePolicyPackagingRequirements,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyExchangeComments != null">
        EXCHANGE_POLICY_EXCHANGE_COMMENTS = #{exchangePolicyExchangeComments,jdbcType=VARCHAR},
      </if>
      <if test="parolePolicySupportRepair != null">
        PAROLE_POLICY_SUPPORT_REPAIR = #{parolePolicySupportRepair,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupportRenovation != null">
        PAROLE_POLICY_SUPPORT_RENOVATION = #{parolePolicySupportRenovation,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupplyBox != null">
        PAROLE_POLICY_SUPPLY_BOX = #{parolePolicySupplyBox,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupplyAttachment != null">
        PAROLE_POLICY_SUPPLY_ATTACHMENT = #{parolePolicySupplyAttachment,jdbcType=INTEGER},
      </if>
      <if test="overduePolicySupplyBackup != null">
        OVERDUE_POLICY_SUPPLY_BACKUP = #{overduePolicySupplyBackup,jdbcType=VARCHAR},
      </if>
      <if test="overduePolicyDetail != null">
        OVERDUE_POLICY_DETAIL = #{overduePolicyDetail,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updator != null">
        UPDATOR = #{updator,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=VARCHAR},
      </if>
    </set>
    where SUPPLY_POLICY_ID = #{supplyPolicyId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.domain.entity.AfterSaleSupplyPolicy">
    <!--@mbg.generated-->
    update T_AFTER_SALE_SUPPLY_POLICY
    set SKU_NO = #{skuNo,jdbcType=VARCHAR},
      TRADER_ID = #{traderId,jdbcType=BIGINT},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      SERVICE_PROVIDER_TYPE = #{serviceProviderType,jdbcType=INTEGER},
      INSTALL_POLICY_INSTALL_TYPE = #{installPolicyInstallType,jdbcType=INTEGER},
      INSTALL_POLICY_INSTALL_AREA = #{installPolicyInstallArea,jdbcType=VARCHAR},
      INSTALL_POLICY_INSTALL_FEE = #{installPolicyInstallFee,jdbcType=DECIMAL},
      INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION = #{installPolicyHaveInstallationQualification,jdbcType=INTEGER},
      INSTALL_POLICY_FREE_REMOTE_INSTALL = #{installPolicyFreeRemoteInstall,jdbcType=INTEGER},
      INSTALL_POLICY_RESPONSE_TIME = #{installPolicyResponseTime,jdbcType=VARCHAR},
      INSTALL_POLICY_VISIT_TIME = #{installPolicyVisitTime,jdbcType=VARCHAR},
      INSTALL_POLICY_INSTALL_TIME = #{installPolicyInstallTime,jdbcType=VARCHAR},
      TECHNICAL_DIRECT_SUPPLY_MAINTAIN = #{technicalDirectSupplyMaintain,jdbcType=INTEGER},
      TECHNICAL_DIRECT_RESPONSE_TIME = #{technicalDirectResponseTime,jdbcType=VARCHAR},
      TECHNICAL_DIRECT_EFFECT_TIME = #{technicalDirectEffectTime,jdbcType=VARCHAR},
      GUARANTEE_POLICY_IS_GUARANTEE = #{guaranteePolicyIsGuarantee,jdbcType=INTEGER},
      GUARANTEE_POLICY_GUARANTEE_TYPE = #{guaranteePolicyGuaranteeType,jdbcType=VARCHAR},
      GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD = #{guaranteePolicyHostGuaranteePeriod,jdbcType=VARCHAR},
      GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD = #{guaranteePolicyPartsGuaranteePeriod,jdbcType=VARCHAR},
      GUARANTEE_POLICY_CYCLE_CALTYPE = #{guaranteePolicyCycleCaltype,jdbcType=INTEGER},
      GUARANTEE_POLICY_AREA = #{guaranteePolicyArea,jdbcType=INTEGER},
      GUARANTEE_POLICY_AREA_COMMENT = #{guaranteePolicyAreaComment,jdbcType=VARCHAR},
      GUARANTEE_POLICY_RESPONSE_TIME = #{guaranteePolicyResponseTime,jdbcType=VARCHAR},
      GUARANTEE_POLICY_VISIT_TIME = #{guaranteePolicyVisitTime,jdbcType=VARCHAR},
      GUARANTEE_POLICY_REPAIRE_TIME = #{guaranteePolicyRepaireTime,jdbcType=VARCHAR},
      GUARANTEE_POLICY_REPAIRE_COMMENT = #{guaranteePolicyRepaireComment,jdbcType=VARCHAR},
      RETURN_POLICY_SUPPORT_RETURN = #{returnPolicySupportReturn,jdbcType=INTEGER},
      RETURN_POLICY_CONDITION = #{returnPolicyCondition,jdbcType=VARCHAR},
      RETURN_POLICY_NEED_IDENTIFY = #{returnPolicyNeedIdentify,jdbcType=INTEGER},
      RETURN_POLICY_IDENTIFY_TYPE = #{returnPolicyIdentifyType,jdbcType=VARCHAR},
      RETURN_POLICY_RETURN_PERIOD = #{returnPolicyReturnPeriod,jdbcType=VARCHAR},
      RETURN_POLICY_CYCLE_CALTYP = #{returnPolicyCycleCaltyp,jdbcType=INTEGER},
      RETURN_POLICY_PACKAGING_REQUIREMENTS = #{returnPolicyPackagingRequirements,jdbcType=VARCHAR},
      RETURN_POLICY_RETURN_COMMENTS = #{returnPolicyReturnComments,jdbcType=VARCHAR},
      EXCHANGE_POLICY_SUPPORT_CHANGE = #{exchangePolicySupportChange,jdbcType=INTEGER},
      EXCHANGE_POLICY_EXCHANGE_CONTITION = #{exchangePolicyExchangeContition,jdbcType=VARCHAR},
      EXCHANGE_POLICY_NEED_IDENTIFY = #{exchangePolicyNeedIdentify,jdbcType=INTEGER},
      EXCHANGE_POLICY_IDENTIFY_TYPE = #{exchangePolicyIdentifyType,jdbcType=VARCHAR},
      EXCHANGE_POLICY_EXCHANGE_PERIOD = #{exchangePolicyExchangePeriod,jdbcType=VARCHAR},
      EXCHANGE_POLICY_CYCLE_CALTYP = #{exchangePolicyCycleCaltyp,jdbcType=INTEGER},
      EXCHANGE_POLICY_PACKAGING_REQUIREMENTS = #{exchangePolicyPackagingRequirements,jdbcType=VARCHAR},
      EXCHANGE_POLICY_EXCHANGE_COMMENTS = #{exchangePolicyExchangeComments,jdbcType=VARCHAR},
      PAROLE_POLICY_SUPPORT_REPAIR = #{parolePolicySupportRepair,jdbcType=INTEGER},
      PAROLE_POLICY_SUPPORT_RENOVATION = #{parolePolicySupportRenovation,jdbcType=INTEGER},
      PAROLE_POLICY_SUPPLY_BOX = #{parolePolicySupplyBox,jdbcType=INTEGER},
      PAROLE_POLICY_SUPPLY_ATTACHMENT = #{parolePolicySupplyAttachment,jdbcType=INTEGER},
      OVERDUE_POLICY_SUPPLY_BACKUP = #{overduePolicySupplyBackup,jdbcType=VARCHAR},
      OVERDUE_POLICY_DETAIL = #{overduePolicyDetail,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      UPDATOR = #{updator,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=VARCHAR}
    where SUPPLY_POLICY_ID = #{supplyPolicyId,jdbcType=BIGINT}
  </update>

<!--auto generated by MybatisCodeHelper on 2024-05-20-->
  <select id="findBySkuNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_AFTER_SALE_SUPPLY_POLICY
    where SKU_NO=#{skuNo,jdbcType=VARCHAR} and TRADER_ID = #{traderId,jdbcType=BIGINT}
  </select>
</mapper>