@import (less) '../common/mixin.css';

.record-wrap {
    display: flex;
    align-items: center;

    .vd-ui_icon {
        font-size: 16px;
        margin-right: 5px;
        color: #09f;
        cursor: pointer;
        line-height: 1;

        &:hover {
            color: #f60;
        }
    }
}

.leads-iframe-wrap {
    .leads-iframe {
        width: 100%;
        height: calc(100vh - 5px);
    }
}

// 新建商机
.businesschance-add-container {
    padding-top: 60px;

    .form-wrap {
        padding: 10px 20px 20px 20px;
    
        .form-top-tip {
            max-width: 1240px;
            min-width: 1160px;
            margin: 0 auto;
            margin-bottom: 10px;
        }
    }

    .top-featurn {
        width: 100%;
        height: 60px;
        position: fixed;
        top: 50px;
        left: 0;
        z-index: 40;

        .inner-header {
            padding: 0 15px;
            height: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #FFFFFF;
            border-bottom: solid 1px #E1E5E8;

            .inner-header-title {
                font-size: 20px;
                font-weight: 700;
            }
        }
    }

    .next-data {
        margin-top: 5px;
        margin-left: 6px;
    }

    .price-dw {
        display: inline-block;
        padding: 6px 10px;
    }
}

// 商机详情 ↓↓↓
.businesschance-detail-container {
    position: relative;
    padding-top: 60px;
    padding-right: 360px;

    .visible-xs {
        display: none;
    }

    .chanceDetail-header-wrap {
        width: 100%;
        min-width: 760px;
        height: 60px;
        background: #fff;
        border-bottom: solid 1px #E1E5E8;
        position: fixed;
        top: 50px;
        z-index: 9;

        .header-main {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;

            .header-content {
                flex: 1;
                min-width: 0;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .header-left {
                    display: flex;
                    align-items: center;
                    padding: 0 15px;

                    .title {
                        font-size: 20px;
                        font-weight: 700;
                        color: #333333;
                    }
                }
                .header-right {
                    display: flex;
                    align-items: center;
                    padding-right: 10px;

                    .vd-ui-button {
                        margin-right: 10px;
                    }
                }
            }

            .header-aside-wrap {

                // 大屏头
                .header-md-aside {
                    position: relative;
                    width: 360px;
                    display: flex;

                    &::before {
                        content: "";
                        height: 72px;
                        width: 1px;
                        background: #E1E5E8;
                        position: absolute;
                        left: 0;
                    }
                }

                .vd-ui-title-tip-wrap {
                    flex: 1;
                }

                .header-xs-aside {
                    display: none;
                }

                .h-a-item {
                    height: 60px;
                    position: relative;
                    flex: 1;
                    background-size: 24px 24px;
                    background-position: center;
                    background-repeat: no-repeat;
                    cursor: pointer;

                    &::before {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        left: 0;
                        top: 0;
                        content: "";
                        z-index: 1;
                        background-size: 24px 24px;
                        background-position: center;
                        background-repeat: no-repeat;
                        opacity: 0;
                    }

                    &.msg {
                        background-image: url('../../image/icon/msg.svg');

                        &::before {
                            background-image: url('../../image/icon/msg-active.svg');
                        }
                    }
                    &.time {
                        background-image: url('../../image/icon/time.svg');

                        &::before {
                            background-image: url('../../image/icon/time-active.svg');
                        }
                    }
                    &.record {
                        background-image: url('../../image/icon/record.svg');

                        &::before {
                            background-image: url('../../image/icon/record-active.svg');
                        }
                    }
                    &.user {
                        background-image: url('../../image/icon/user.svg');

                        &::before {
                            background-image: url('../../image/icon/user-active.svg');
                        }
                    }

                    

                    &.active, &:hover {
                        background-image: none;

                        &::before {
                            opacity: 1;
                        }   

                        // &.msg {
                        //     background: url('../../image/icon/msg-active.svg') no-repeat;
                        // }
                        // &.time {
                        //     background: url('../../image/icon/time-active.svg') no-repeat;
                        // }
                        // &.record {
                        //     background: url('../../image/icon/record-active.svg') no-repeat;
                        // }
                        // &.user {
                        //     background: url('../../image/icon/user-active.svg') no-repeat;
                        // }
                    }

                    &.active {
                        &::after {
                            content: "";
                            display: block;
                            width: 30px;
                            height: 2px;
                            background: #09f;
                            position: absolute;
                            bottom: 0;
                            left: 50%;
                            transform: translateX(-50%);
                        }
                    }
                }
            }
        }
    }

    .deatil-page-top-bg {
        height: 150px;
        background: linear-gradient(to bottom, #ddeefa 0%, #f5f7fa 100%);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1;

        &.stage5 {
            background: linear-gradient(to bottom, #e0f2e4 0%, #f5f7fa 100%);
        }
        
        &.stage6 {
            background: linear-gradient(to bottom, #f2dfe4 0%, #f5f7fa 100%);
        }
    }

    .detail-page-wrap {
        padding: 0 20px 20px;
        position: relative;

        .chance-step {
            height: 90px;
            max-width: 1240px;
            min-width: 960px;
            margin: 0 auto;

            > ul {
                position: relative;
                z-index: 2;
                display: flex;
                padding: 30px 0 20px;

                > li {
                    position: relative;
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    > .icon {
                        width: 17px;
                        height: 17px;
                        border-radius: 50%;
                        overflow: hidden;
                        margin-bottom: 5px;
                        background: #999;
                        position: relative;
                        z-index: 2;
                    }
                    > p {
                        font-size: 12px;
                        color: #999;
                    }

                    &::before {
                        content: "";
                        position: absolute;
                        width: 50%;
                        left: 0;
                        top: 8px;
                        border-bottom: dotted 1px #c9ced1;
                    }
                    &::after {
                        content: "";
                        position: absolute;
                        width: 50%;
                        right: 0;
                        top: 8px;
                        border-bottom: dotted 1px #c9ced1;
                    }
                    &:first-child {
                        &::before {
                            display: none;
                        }
                    }
                    &:last-child {
                        &::after {
                            display: none;
                        }
                    }

                    &.active {
                        .icon {
                            background: #09f;
                        }
                        > p {
                            color: #09f;
                        }
                    }
                    &.success {
                        .icon {
                            background: #13BF13;
                        }
                        > p {
                            color: #13BF13;
                        }
                    }
                    &.close {
                        .icon {
                            background: #E64545;
                        }
                        > p {
                            color: #E64545;
                        }
                    }
                }
            }
        }

        .main {
            position: relative;
            z-index: 2;

            .show-font {
                margin-top: 10px;
                &:first-child {
                    margin-top: 0;
                }
            }
        }

        .right-aside {
            width: 360px;
            background: #fff;
            position: fixed;
            right: 0;
            top: 50px;
            z-index: 3;
            min-height: 500px;
            bottom: 0;
            padding-top: 60px;

            .right-aside-inner {
                height: 100%;
                border-left: solid 1px #E1E5E8;
            }
        }
    }

    .related-order-card {
        position: relative;
        max-width: 1240px;
        min-width: 960px;
        margin: 0 auto;
        background: #fff;
        margin-bottom: 20px;

        .title {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            font-size: 16px;
            font-weight: 700;
        }
    }

    .card-title-ai {
        position: absolute;
        top: 15px;
        right: 20px;
    }

    .flex {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .quote-title {
        padding: 0 20px;
        .left {
            .title {
                display: flex;
                align-items: center;
                padding: 15px 0;
                font-size: 16px;
                font-weight: 700;
            }

            .status {
                padding: 0 10px; 
                height: 27px;
                border-radius: 3px;
                font-size: 12px;
                font-weight: 400;
                line-height: 27px;
                margin-left: 10px;

                &.no {
                    background-color: #F5F7FA;
                    color: #999;
                }
                &.s0 {
                    background-color: #ffede0;
                    color: #FF6600;
                }
                &.s1 {
                    background-color: #E3F7E3;
                    color: #13BF13;
                }
            }
        }
        .right {
            .opt {
                display: flex;
                align-items: center;

                .btn {
                    position: relative;
                    color: #09f;
                    padding-right: 10px;
                    margin-right: 10px;
                    cursor: pointer;

                    &::after {
                        content: "";
                        height: 15px;
                        border-right: solid 1px #ededed;
                        position: absolute;
                        right: 0;
                        top: 3px;
                    }

                    &:hover {
                        color: #f60;
                    }

                    &:last-child {
                        margin-right: 0;
                        padding-right: 0;

                        &::after {
                            display: none;
                        }
                    }
                }
            }
        }
    }

    .quoteorder-table {
        border-left: solid 1px #E1E5E8;
        border-top: solid 1px #E1E5E8;

        .q-tr {
            display: flex;

            .q-td {
                flex: 1;
                border-right: solid 1px #ededed;
                border-bottom: solid 1px #ededed;

                .label {
                    padding: 0 10px;
                    height: 35px;
                    background: #FAFBFC;
                    line-height: 36px;
                    text-align: center;
                    white-space: nowrap;
                    border-bottom: solid 1px #ededed;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    color: #999;

                    .tip {
                        position: relative;

                        > i {
                            font-size: 16px;
                            color: #999;
                            cursor: pointer;
                            margin-left: 5px;
                            &:hover + span {
                                display: block;
                            }
                        }

                        > span {
                            display: none;
                            position: relative;
                            font-size: 14px;
                            line-height: 21px;
                            padding: 8px 15px;
                            border-radius: 3px;
                            background: #333;
                            color: #fff;
                            position: absolute;
                            top: -41px;
                            left: -30px;

                            &::after {
                                content: "";
                                position: absolute;
                                left: 37px;
                                bottom: -9px;
                                width: 0;
                                height: 0;
                                border-top: solid 9px #333333;
                                border-left: solid 6px transparent;
                                border-right: solid 6px transparent;
                            }
                        }

                        &.right {
                            > span {
                                left: auto;
                                right: -30px;

                                &::after {
                                    left: auto;
                                    right: 32px;
                                }
                            }
                        }
                    }
                }
    
                .value {
                    padding: 8px 10px;
                    text-align: center;
                    font-size: 14px;
                    color: #333;

                    &.price {
                        color: #E64545;
                        font-weight: 700;
                    }
                }
            }
        }
    }
    .null-baojia {
        padding: 20px 0 40px;
        display: flex;
        flex-direction: column;
        align-items: center;

        > i {
            font-size: 32px;
            color: #09F;
        }
        > p {
            color: #999;
            margin-top: 10px;
        }
    }

    // 客户名称
    .detail-tyc {
        word-break: break-all;
        position: relative;
        padding-right: 21px;
        display: inline-block;

        .company {
            font-size: 12px;
            color: #333;

            &.blue {
                color: #09f;
                cursor: pointer;

                &:hover {
                    color: #f60;
                }
            }
        }

        .icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: url('../../image/tyc.png') no-repeat;
            background-size: 100%;
            margin-left: 5px;
            cursor: pointer;
            position: absolute;
            top: 1px;
            right: 0;
        }
    }

    // 高亮号码
    .highlight {
        color: #09f;
        cursor: pointer;
    
        .vd-ui_icon {
            font-size: 16px;
            margin-right: 1px;
            cursor: pointer;
            line-height: 1;
        }
    
        &:hover {
            color: #f60;
        }
    }

    .no-bg {
        background: none;

        .icon-collect2 {
            color: #FF6600;
        }
    }
}

.call-span {
    .icon-call2 {
        line-height: 1;
        vertical-align: -2px;
        font-size: 16px;
    }

    &.highlight {
        color: #09f;

        &:hover {
            color: #f60;
        }
    }
}

.td-tyc-wrap {
    display: flex;
    align-items: center;

    .text-line-1 {
        flex: 1;
    }

    .tyc-icon {
        width: 16px;
        height: 16px;
        background-image: url(../../image/tyc.png);
        background-size: 100% 100%;
        margin-left: 5px;
        cursor: pointer;
    }
}

.change-type-inner {
    display: flex;

    .vd-ui-tip-wrap {
        margin-left: 20px;
        width: 17px;
    }
}

.form-wrap {
    .form-chance-block-placeholder {
        padding-bottom: 15px;
    }
    
    .form-chance-block {
        background: rgba(255, 237, 224, 0.5);
        padding: 15px 0;
        margin-bottom: 15px;
        position: relative;

        .form-chance-tip {
            position: absolute;
            display: flex;
            align-items: center;
            color: #f60;
            right: 10px;
            top: 10px;

            .vd-ui_icon {
                line-height: 1;
                margin-right: 5px;
                font-size: 16px;
            }

            .icon-yes1 {
                display: none;
            }
        }

        &.active {
            background: rgba(227, 247, 227, 0.5);
            
            .form-chance-tip {
                color: #13BF13;
            }

            .icon-yes1 {
                display: block;
            }

            .icon-info1 {
                display: none;
            }
        }
    }
}

@media screen and (max-width: 1366px) {
    .businesschance-detail-container {
        padding-right: 0;

        .hidden-xs {
            display: none;
        }
        .chanceDetail-header-wrap {
            .header-main {
                .header-aside-wrap {
                    .header-md-aside {
                        display: none;
                    }
                    // 小屏头
                    .header-xs-aside {
                        position: relative;
                        width: 192px;
                        display: flex;
                    }
                }
            }
        }

    }
}

.order-type {
    .vd-ui-input-error {
        margin-bottom: 10px!important;
    }

    .red {
        color: #e64545;
    }
}
