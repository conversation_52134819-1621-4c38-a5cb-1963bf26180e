package com.vedeng.trader.group.order;

import com.vedeng.common.util.DateUtil;
import com.vedeng.order.dao.BussinessChanceMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.trader.group.BaseBehavior;
import com.vedeng.trader.model.Period;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.BitSet;
import java.util.List;

public abstract class OrderBaseBehavior extends BaseBehavior {

    // 运行时注入Mapper
    private SaleorderMapper saleorderMapper=(SaleorderMapper) context.getBean(SaleorderMapper.class);
    public BitSet getOrderData(){
        BitSet bitSet=new BitSet();
        Period period= DateUtil.getStartAndEndPointForDays(getPeriod());
        List<Integer> traderIds=saleorderMapper.getTraderIdOrderedInPeriod(period);
        if (CollectionUtils.isNotEmpty(traderIds)) {
            for(Integer id:traderIds){
                if(id==null){
                    continue;
                }
                bitSet.set(id);
            }
        }
        return bitSet;
    }
}
