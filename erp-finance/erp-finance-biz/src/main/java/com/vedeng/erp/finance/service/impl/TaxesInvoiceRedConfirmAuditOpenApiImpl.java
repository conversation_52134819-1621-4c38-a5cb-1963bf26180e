package com.vedeng.erp.finance.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceRedConfirmAuditResponseDto;
import com.vedeng.erp.finance.service.AbstractTaxesOpenApiHandler;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.infrastructure.taxes.domain.TaxesOpenApiResult;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.util.Objects;

import static com.vedeng.infrastructure.taxes.common.constant.TaxesConstant.Y;

/**
 * 红字确认单审核
 */
@Service
public class TaxesInvoiceRedConfirmAuditOpenApiImpl extends AbstractTaxesOpenApiHandler {

    /**
     * 后置处理
     * @param taxesParam
     * @param executeResult
     * @return
     */
    @Override
    protected ITaxesResult afterExecute(ITaxesParam taxesParam, ITaxesResult executeResult) {
        TaxesOpenApiResult openapi = (TaxesOpenApiResult) executeResult;
        String decodeStr = new String(Base64Utils.decodeFromString(openapi.getData()));
        SaleInvoiceRedConfirmAuditResponseDto taxesResult = JSONObject.parseObject(decodeStr, SaleInvoiceRedConfirmAuditResponseDto.class);
        if (Objects.isNull(taxesResult)){
            taxesResult = new SaleInvoiceRedConfirmAuditResponseDto();
        }
        TaxesOpenApiResult.ReturnInfo return_info = openapi.getReturn_info();
        String message = super.getMessage(return_info, taxesResult.getMessage());
        taxesResult.setReturnCode(return_info.getReturn_code());
        taxesResult.setReturnMessage(message);
        if (TaxesReturnCodeEnum.SUCCESS.getCode().equals(return_info.getReturn_code()) && Y.equals(taxesResult.getCode())){
            taxesResult.setIsSuccess(Boolean.TRUE);
        }

        return taxesResult;
    }
}
