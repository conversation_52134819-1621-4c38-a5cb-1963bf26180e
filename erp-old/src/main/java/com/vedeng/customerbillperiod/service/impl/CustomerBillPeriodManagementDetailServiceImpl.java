package com.vedeng.customerbillperiod.service.impl;

import com.vedeng.common.annotation.MethodLock;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodOverdueManageDetailTypeEnum;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodSettlementTypeEnum;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodUseTypeEnum;
import com.vedeng.customerbillperiod.dao.CustomerBillPeriodOverdueManagementDetailMapper;
import com.vedeng.customerbillperiod.dao.CustomerBillPeriodRiskManagementDetailMapper;
import com.vedeng.customerbillperiod.dao.CustomerBillPeriodUseDetailMapper;
import com.vedeng.customerbillperiod.dto.GenerateCustomerBillPeriodManagementDetailDto;
import com.vedeng.customerbillperiod.dto.RollbackCustomerBillPeriodManagementDetailDto;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodOverdueManagementDetail;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodRiskManagementDetail;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail;
import com.vedeng.customerbillperiod.model.vo.CustomerBillPeriodOverdueManagementDetailVo;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodManagementCodeCommonService;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodManagementDetailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: daniel
 * @Date: 2021/7/26 15 11
 * @Description:
 */
@Service
public class CustomerBillPeriodManagementDetailServiceImpl implements CustomerBillPeriodManagementDetailService {

    private static final Logger logger = LoggerFactory.getLogger(CustomerBillPeriodManagementDetailServiceImpl.class);

    @Resource
    private CustomerBillPeriodUseDetailMapper customerBillPeriodUseDetailMapper;

    @Resource
    private CustomerBillPeriodOverdueManagementDetailMapper customerBillPeriodOverdueManagementDetailMapper;

    @Resource
    private CustomerBillPeriodRiskManagementDetailMapper customerBillPeriodRiskManagementDetailMapper;

    @Autowired
    private CustomerBillPeriodManagementCodeCommonService customerBillPeriodManagementCodeCommonService;


    @MethodLock(className = GenerateCustomerBillPeriodManagementDetailDto.class, field = "relatedId", time = 10000)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateCustomerBillPeriodManagementDetail(GenerateCustomerBillPeriodManagementDetailDto managementDetailDto) throws CustomerBillPeriodException {
        logger.info("生成账期编码操作：{}",managementDetailDto.toString());

        List<CustomerBillPeriodUseDetail> useDetailList =
                customerBillPeriodUseDetailMapper.getCustomerBillPeriodUseDetailListByOrderId(managementDetailDto.getCompanyId(),
                        managementDetailDto.getCustomerId(), managementDetailDto.getOrderId());
        if (CollectionUtils.isEmpty(useDetailList)){
            logger.info("当前无待还账期金额，无法生成账期管理编码");
            return;
        }

        //生成逾期管理编码
        if (useDetailList.get(0).getSettlementType().equals(managementDetailDto.getType())){
            generateOverdueManageDetail(useDetailList.get(0).getSettlementType(),managementDetailDto);
        }

        //如果是订单发货，则生成风险管理编码
        if (CustomerBillPeriodSettlementTypeEnum.ORDER_DELIVERY.getCode().equals(managementDetailDto.getType())){
            generateRiskManageDetail(managementDetailDto);
        }

    }


    private void generateOverdueManageDetail(Integer settlementType, GenerateCustomerBillPeriodManagementDetailDto managementDetailDto){
        //获取未生成账期逾期编码的账期使用记录
        List<CustomerBillPeriodUseDetail> useDetailListOfUnOverdueManaged =
                customerBillPeriodUseDetailMapper.getCustomerBillPeriodUseDetailListOfUnOverdueManagedByOrderId(managementDetailDto.getCompanyId(),
                        managementDetailDto.getCustomerId(),managementDetailDto.getOrderId()).stream()
                        .filter(item -> item.getUnreturnedAmount().compareTo(BigDecimal.ZERO) > 0)
                        .collect(Collectors.toList());

        BigDecimal overdueAmount = managementDetailDto.getAmount();

        //逾期管理编码
        String billPeriodOverdueManagementCode =
                customerBillPeriodManagementCodeCommonService.customerBillPeriodManagementCodeGenerator(managementDetailDto.getCompanyId(), managementDetailDto.getCustomerId(),
                        managementDetailDto.getOrderNo(),managementDetailDto.getOrderId(), settlementType);
        for (CustomerBillPeriodUseDetail item : useDetailListOfUnOverdueManaged){
            CustomerBillPeriodOverdueManagementDetail overdueManagementDetail = new CustomerBillPeriodOverdueManagementDetail();
            overdueManagementDetail.setBillPeriodOverdueManagementCode(billPeriodOverdueManagementCode);
            overdueManagementDetail.setCompanyId(item.getCompanyId());
            overdueManagementDetail.setCustomerId(item.getCustomerId());
            overdueManagementDetail.setBillPeriodUseDetailId(item.getBillPeriodUseDetailId());
            overdueManagementDetail.setType(item.getSettlementType());
            overdueManagementDetail.setRelatedId(managementDetailDto.getRelatedId());
            overdueManagementDetail.setParentManagementDetailId(0L);
            overdueManagementDetail.setSettlementPeriod(item.getSettlementPeriod());
            overdueManagementDetail.setOverdueAmount(BigDecimal.ZERO);
            overdueManagementDetail.setOverdueDays(0);
            overdueManagementDetail.setAddTime(managementDetailDto.getAddTime());
            BigDecimal amount = overdueAmount.compareTo(item.getUnreturnedAmount()) >=0 ? item.getUnreturnedAmount() : overdueAmount;
            overdueManagementDetail.setAmount(amount);
            overdueManagementDetail.setUnreturnedAmount(amount);
            overdueAmount = overdueAmount.subtract(amount);
            customerBillPeriodOverdueManagementDetailMapper.insertSelective(overdueManagementDetail);
            logger.info("生成账期的逾期管理编码记录：{}",overdueManagementDetail.toString());

            if (overdueAmount.compareTo(BigDecimal.ZERO) <= 0){
                break;
            }
            billPeriodOverdueManagementCode = customerBillPeriodManagementCodeCommonService.billPeriodManagementCodeIncrease(billPeriodOverdueManagementCode);
        }
    }

    private void generateRiskManageDetail(GenerateCustomerBillPeriodManagementDetailDto managementDetailDto){
        //获取未生成风险管理的账期使用记录
        List<CustomerBillPeriodUseDetail> useDetailListOfUnRiskManaged =
                customerBillPeriodUseDetailMapper.getCustomerBillPeriodUseDetailListOfUnRiskManagedByOrderId(managementDetailDto.getCompanyId()
                        , managementDetailDto.getCustomerId(), managementDetailDto.getOrderId()).stream()
                        .filter(item -> item.getUnreturnedAmount().compareTo(BigDecimal.ZERO) > 0)
                        .collect(Collectors.toList());

        BigDecimal riskAmount = managementDetailDto.getAmount();
        //风险管理编码
        String billPeriodRiskManagementCode =
                customerBillPeriodManagementCodeCommonService.customerBillPeriodManagementCodeGenerator(managementDetailDto.getCompanyId(), managementDetailDto.getCustomerId(),
                        managementDetailDto.getOrderNo(),managementDetailDto.getOrderId(), 0);
        for (CustomerBillPeriodUseDetail item : useDetailListOfUnRiskManaged){
            CustomerBillPeriodRiskManagementDetail riskManagementDetail = new CustomerBillPeriodRiskManagementDetail();
            riskManagementDetail.setBillPeriodRiskManagementCode(billPeriodRiskManagementCode);
            riskManagementDetail.setCompanyId(item.getCompanyId());
            riskManagementDetail.setCustomerId(item.getCustomerId());
            riskManagementDetail.setBillPeriodUseDetailId(item.getBillPeriodUseDetailId());
            BigDecimal amountItem = riskAmount.compareTo(item.getUnreturnedAmount()) >=0 ? item.getUnreturnedAmount() : riskAmount;
            riskManagementDetail.setAmount(amountItem);
            riskManagementDetail.setUnreturnedAmount(amountItem);
            riskManagementDetail.setType(CustomerBillPeriodSettlementTypeEnum.ORDER_DELIVERY.getCode());
            riskManagementDetail.setRelatedId(managementDetailDto.getRelatedId());
            riskManagementDetail.setParentManagementDetailId(0L);
            riskManagementDetail.setSettlementPeriod(item.getSettlementPeriod());
            riskManagementDetail.setAddTime(managementDetailDto.getAddTime());
            riskAmount = riskAmount.subtract(amountItem);
            customerBillPeriodRiskManagementDetailMapper.insertSelective(riskManagementDetail);
            logger.info("生成了账期的风险管理编码记录：{}",riskManagementDetail.toString());

            if (riskAmount.compareTo(BigDecimal.ZERO) <= 0){
                break;
            }
            billPeriodRiskManagementCode = customerBillPeriodManagementCodeCommonService.billPeriodManagementCodeIncrease(billPeriodRiskManagementCode);
        }
    }


    @MethodLock(className = RollbackCustomerBillPeriodManagementDetailDto.class, field = "relatedId", time = 10000)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rollbackCustomerBillPeriodManagementDetail(RollbackCustomerBillPeriodManagementDetailDto rollbackCustomerBillPeriodManagementDetailDto) throws CustomerBillPeriodException {
        customerBillPeriodManagementCodeCommonService.generateNegativeBillPeriodManagementCode(rollbackCustomerBillPeriodManagementDetailDto);
    }



    @Override
    public List<CustomerBillPeriodOverdueManagementDetailVo> getCustomerBillPeriodOverDueInfoByOverdueDay(Integer dayNum) {
        return customerBillPeriodOverdueManagementDetailMapper.getCustomerBillPeriodOverDueInfoByOverdueDay(dayNum);
    }

    @Override
    public void dealOverDuedcustomerBill(List<CustomerBillPeriodOverdueManagementDetailVo> overduedList) {
        CustomerBillPeriodOverdueManagementDetail update = new CustomerBillPeriodOverdueManagementDetail();
        for (CustomerBillPeriodOverdueManagementDetailVo detail : overduedList) {
            update.setOverdueAmount(null);

            update.setBillPeriodOverdueManagementDetailId(detail.getBillPeriodOverdueManagementDetailId());
            update.setOverdueDays(detail.getDayNum());
            if(detail.getOverdueAmount().compareTo(BigDecimal.ZERO) == 0){
                update.setOverdueAmount(detail.getUnreturnedAmount());
            }
            update.setModTime(System.currentTimeMillis());
            customerBillPeriodOverdueManagementDetailMapper.updateByPrimaryKeySelective(update);
        }
    }
}
