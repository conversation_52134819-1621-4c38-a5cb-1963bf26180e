package com.vedeng.erp.trader.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.common.dto.RSalesJBusinessOrderDto;
import com.vedeng.erp.trader.domain.entity.RSalesJBusinessOrderEntity;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description dto转entity
 * @date 2022/7/12 10:45
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface RSalesJBusinessOrderConvertor extends BaseMapStruct<RSalesJBusinessOrderEntity, RSalesJBusinessOrderDto> {
}
