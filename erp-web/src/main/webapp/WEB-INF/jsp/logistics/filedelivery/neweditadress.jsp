<!DOCTYPE html>
<html lang="en">
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增寄送地址" scope="application" />
 <%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags"%>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://com.vedeng.common.util/definetags" prefix="define"%>
<%@taglib prefix="sf" uri="http://www.springframework.org/tags/form"%>

<%@ page trimDirectiveWhitespaces="true" %>

<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<head>
<c:set var="path" value="<%=basePath%>" scope="application" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="renderer" content="webkit|ie-comp|ie-stand">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
 <title>${title}</title>
<link data-n-head="ssr" rel="icon" type="image/x-icon" href="<%=basePath%>static/favicon.ico">

<link  rel="stylesheet" href='<%= basePath %>webjars/layuidist/css/layui.css?rnd=${resourceVersionKey}'></link>
<link  rel="stylesheet" href='<%= basePath %>webjars/topezadmin/layui/css/ezform.css?rnd=${resourceVersionKey}'></link>
<script src="<%= basePath %>webjars/layuidist/layui.js?rnd=${resourceVersionKey}"></script>
<script src="<%= basePath %>webjars/jquery/1.12.4/jquery.min.js?rnd=${resourceVersionKey}"></script>
</head>
<body>
<div class="layui-fluid" style="margin-top: 20px;">
    <div class="layui-col-space10 layui-row"  >
        <div class="layui-col-md12  ">

            <form class="layui-form   " style="margin-bottom: 58px"  >
                <div class="layui-form-item"  >
                    <label class="layui-form-label"> <span class="layui-badge-dot" ></span>收件单位类型 :</label>
                    <div class="layui-input-block">
                        <input type="radio" lay-filter="traderType" name="traderType" value="1" title="客户" <c:if test="${deliveryVO.traderType eq 1}">checked </c:if>>
                        <input type="radio" lay-filter="traderType" name="traderType" value="2" title="供应商" <c:if test="${deliveryVO.traderType eq 2}">checked </c:if>>
                        <input type="radio" lay-filter="traderType" name="traderType" value="3" title="其他" <c:if test="${deliveryVO.traderType eq 3}">checked </c:if>>
                    </div>
                </div>
                <div class="layui-form-item"  >
                    <label class="layui-form-label"> <span class="layui-badge-dot" ></span>收件单位 :</label>
                    <div class="layui-input-block">
                        <div     url="${url}"   name="traderNameId" value=""></div>
                    </div>
                </div>

                <div class="layui-row layui-panel formtopfix formtopfixbuttons" style="margin-left: -16px;"  >
                    <div style="margin: 10px 0px 0px 10px;float:left" class="
                    layui-col-md9 layui-col-md-offset3 layui-btn-container" id="submitButtonContainer">
                        <button   class="print layui-btn layui-bg-blue " >打印面单</button>
                        <button   class="submit layui-btn layui-bg-blue " >提交</button>
                        <button   class="approve layui-btn layui-bg-blue " >通过</button>
                        <button   class="reject layui-btn layui-bg-blue " >驳回</button>
                        <button   class="history layui-btn layui-bg-blue " >审批信息</button>
                        <button   class="close layui-btn layui-bg-blue " >关闭寄送</button>
                     </div>
                </div>
            </form>
        </div>
    </div>
</div>


<script>


    // Usage
    layui.use(function(){
        let layer = layui.layer;
        let form=layui.form;
        let table=layui.table;

    });

</script>
</body>
</html>