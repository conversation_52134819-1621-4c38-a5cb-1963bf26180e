<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.finance.dao.HxInvoiceDetailMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.finance.model.HxInvoiceDetail">
        <id column="HX_INVOICE_DETAIL_ID" jdbcType="INTEGER" property="hxInvoiceDetailId" />
        <result column="HX_INVOICE_ID" jdbcType="INTEGER" property="hxInvoiceId" />
        <result column="NUMBER" jdbcType="VARCHAR" property="number" />
        <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
        <result column="SPECIFICATION" jdbcType="VARCHAR" property="specification" />
        <result column="UNIT" jdbcType="VARCHAR" property="unit" />
        <result column="QUANTITY" jdbcType="DOUBLE" property="quantity" />
        <result column="PRICE" jdbcType="DECIMAL" property="price" />
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
        <result column="TAX_AMOUNT" jdbcType="DECIMAL" property="taxAmount" />
    </resultMap>
    <sql id="Base_Column_List">
        HX_INVOICE_DETAIL_ID, HX_INVOICE_ID, `NUMBER`, GOODS_NAME, SPECIFICATION, UNIT, QUANTITY,
    PRICE, AMOUNT, TAX_AMOUNT
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_HX_INVOICE_DETAIL
        where HX_INVOICE_DETAIL_ID = #{hxInvoiceDetailId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from T_HX_INVOICE_DETAIL
        where HX_INVOICE_DETAIL_ID = #{hxInvoiceDetailId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="HX_INVOICE_DETAIL_ID" keyProperty="hxInvoiceDetailId" parameterType="com.vedeng.finance.model.HxInvoiceDetail" useGeneratedKeys="true">
        insert into T_HX_INVOICE_DETAIL (HX_INVOICE_ID, `NUMBER`, GOODS_NAME,
                                         SPECIFICATION, UNIT, QUANTITY,
                                         PRICE, AMOUNT, TAX_AMOUNT
        )
        values (#{hxInvoiceId,jdbcType=INTEGER}, #{number,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR},
                #{specification,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, #{quantity,jdbcType=DOUBLE},
                #{price,jdbcType=DECIMAL}, #{amount,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL}
               )
    </insert>
    <insert id="insertSelective" keyColumn="HX_INVOICE_DETAIL_ID" keyProperty="hxInvoiceDetailId" parameterType="com.vedeng.finance.model.HxInvoiceDetail" useGeneratedKeys="true">
        insert into T_HX_INVOICE_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hxInvoiceId != null">
                HX_INVOICE_ID,
            </if>
            <if test="number != null">
                `NUMBER`,
            </if>
            <if test="goodsName != null">
                GOODS_NAME,
            </if>
            <if test="specification != null">
                SPECIFICATION,
            </if>
            <if test="unit != null">
                UNIT,
            </if>
            <if test="quantity != null">
                QUANTITY,
            </if>
            <if test="price != null">
                PRICE,
            </if>
            <if test="amount != null">
                AMOUNT,
            </if>
            <if test="taxAmount != null">
                TAX_AMOUNT,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hxInvoiceId != null">
                #{hxInvoiceId,jdbcType=INTEGER},
            </if>
            <if test="number != null">
                #{number,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="specification != null">
                #{specification,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                #{unit,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=DOUBLE},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="taxAmount != null">
                #{taxAmount,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.finance.model.HxInvoiceDetail">
        update T_HX_INVOICE_DETAIL
        <set>
            <if test="hxInvoiceId != null">
                HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER},
            </if>
            <if test="number != null">
                `NUMBER` = #{number,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="specification != null">
                SPECIFICATION = #{specification,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                UNIT = #{unit,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                QUANTITY = #{quantity,jdbcType=DOUBLE},
            </if>
            <if test="price != null">
                PRICE = #{price,jdbcType=DECIMAL},
            </if>
            <if test="amount != null">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="taxAmount != null">
                TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL},
            </if>
        </set>
        where HX_INVOICE_DETAIL_ID = #{hxInvoiceDetailId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.finance.model.HxInvoiceDetail">
        update T_HX_INVOICE_DETAIL
        set HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER},
            `NUMBER` = #{number,jdbcType=VARCHAR},
            GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
            SPECIFICATION = #{specification,jdbcType=VARCHAR},
            UNIT = #{unit,jdbcType=VARCHAR},
            QUANTITY = #{quantity,jdbcType=DOUBLE},
            PRICE = #{price,jdbcType=DECIMAL},
            AMOUNT = #{amount,jdbcType=DECIMAL},
            TAX_AMOUNT = #{taxAmount,jdbcType=DECIMAL}
        where HX_INVOICE_DETAIL_ID = #{hxInvoiceDetailId,jdbcType=INTEGER}
    </update>
</mapper>