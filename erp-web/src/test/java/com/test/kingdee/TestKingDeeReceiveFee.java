package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeeReceiveFeeCommand;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeReceiveFeeMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveFeeCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveFeeConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * 应付单：费用应付单
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeReceiveFee {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeReceiveFeeCommandConvertor commandConvertor;

    @Autowired
    private KingDeeReceiveFeeConvertor kingDeeReceiveFeeConvertor;

    @Autowired
    private KingDeeReceiveFeeMapper kingDeeReceiveFeeMapper;


    @Test
    public void kingDeeReceiveFee() {
        //一级dto
        KingDeeReceiveFeeDto kingDeeReceiveFeeDto = new KingDeeReceiveFeeDto();
        kingDeeReceiveFeeDto.setFid("0");
        kingDeeReceiveFeeDto.setFBillTypeID("YSD02_SYS");
        kingDeeReceiveFeeDto.setFdate("2022-09-17");
        kingDeeReceiveFeeDto.setFQzokBddjtid("贝登erp对应的单据头ID");
        kingDeeReceiveFeeDto.setFcustomerid("KH0001");
        kingDeeReceiveFeeDto.setFbusinesstype("FY");
        kingDeeReceiveFeeDto.setFsettleorgid("101");
        kingDeeReceiveFeeDto.setFSetAccountType("1");

        List<KingDeeReceiveFeeDetailDto> kingDeeReceiveFeeDetailDtoList = new ArrayList<>();
        KingDeeReceiveFeeDetailDto KingDeeReceiveFeeDetailDto = new KingDeeReceiveFeeDetailDto();
        KingDeeReceiveFeeDetailDto.setFcostid("008");
        KingDeeReceiveFeeDetailDto.setFPriceQty("1.0");
        KingDeeReceiveFeeDetailDto.setFTaxPrice("106.0");
        KingDeeReceiveFeeDetailDto.setFEntryTaxRate("6.00");
        KingDeeReceiveFeeDetailDto.setFQzokBddjhid("贝登单据行ID");

        kingDeeReceiveFeeDetailDtoList.add(KingDeeReceiveFeeDetailDto);
        kingDeeReceiveFeeDto.setFEntityDetail(kingDeeReceiveFeeDetailDtoList);

        KingDeeReceiveFeeCommand command = commandConvertor.toCommand(kingDeeReceiveFeeDto);

        System.out.println("入参："+JSON.toJSONString(command));
        RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, kingDeeReceiveFeeDto.getFormId()));
        ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();
        System.out.println("金蝶返回："+JSON.toJSONString(successEntities));
        if (CollUtil.isNotEmpty(successEntities)) {
            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
            System.out.println("数据库入参："+JSON.toJSONString(kingDeeReceiveFeeConvertor.toEntity(kingDeeReceiveFeeDto)));
            kingDeeReceiveFeeMapper.insertSelective(kingDeeReceiveFeeConvertor.toEntity(kingDeeReceiveFeeDto));
        }
    }
}
