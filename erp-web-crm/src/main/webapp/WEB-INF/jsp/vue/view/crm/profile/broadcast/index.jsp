<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>营销中心荣誉榜</title>
    <link rel="icon" href="${pageContext.request.contextPath}/static/image/leaderboard.ico">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,viewport-fit=cover">
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/broadcast/card.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/broadcast/broadcast.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>
<body>
    <div class="page-wrap" id="page">
        <div class="page-main" v-if="!isloading">
            <div class="rank-container">
                <div class="fixed-nav" ref="nav">
                    <div class="m-nav" :class="{'fixed': navFixed}">
                        <div class="scroll-bar" ref="scroll-dom">
                            <div class="nav-list">
                                <div 
                                    class="nav-item J-nav-item"
                                    :class="{'active': index == navIndex}"
                                    v-for="(item, index) in Navs" :key="index"
                                    @click="scrollTo(index)"
                                >{{ item }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="rank-wrap">
                    <div class="rank-card-wrap">
                        <div class="rank-card J-rank-card" ref="card1">
                            <rank-card 
                                title="今日 到款榜单" 
                                filter
                                :data="dayList"
                            ></rank-card>
                        </div>
                        <div class="rank-card J-rank-card" ref="card2">
                            <rank-card title="月度 到款榜单" filter :data="monthList" :need-percent="true"></rank-card>
                        </div>
                        <div class="rank-card J-rank-card" ref="card3">
                            <rank-card title="月度 AED榜单" filter :data="aedMonthList" price-key="warehouseNum"></rank-card>
                        </div>
                        <div class="rank-card J-rank-card" ref="card4">
                            <rank-card title="月度 自有品牌榜单" filter :data="customMonthList" price-key="warehouseAmount"></rank-card>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="/static/js/common/jquery.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/better-scroll.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/components/business/broadcast-card.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/pages/broadcast/index.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
