package com.vedeng.erptoxfjx.xfjx.domain;

import java.util.Date;

/**
 * t_buyorder_to_saleorder_sync
 */
public class XfjxBuyorderToSaleorderSync {
    /**
     * <pre>
     * 主键
     * 表字段 : t_buyorder_to_saleorder_sync.ID
     * </pre>
     * 
     */
    private Integer id;

    /**
     * <pre>
     * 同步方向（1：erp到医购优选，2：医购优选到erp）
     * 表字段 : t_buyorder_to_saleorder_sync.SYNC_DIRECT_TO
     * </pre>
     * 
     */
    private Integer syncDirectTo;

    /**
     * <pre>
     * 采购单号
     * 表字段 : t_buyorder_to_saleorder_sync.BUYORDER_NO
     * </pre>
     * 
     */
    private String buyorderNo;

    /**
     * <pre>
     * 同步状态（0：同步失败；1：同步成功；2：同步中）
     * 表字段 : t_buyorder_to_saleorder_sync.SYNC_STATUS
     * </pre>
     * 
     */
    private Integer syncStatus;

    /**
     * <pre>
     * 开始时间
     * 表字段 : t_buyorder_to_saleorder_sync.ADD_TIME
     * </pre>
     * 
     */
    private Date addTime;

    /**
     * <pre>
     * 更新时间
     * 表字段 : t_buyorder_to_saleorder_sync.UPDATE_TIME
     * </pre>
     * 
     */
    private Date updateTime;

    /**
     * <pre>
     * 贝登ERP销售订单号
     * 表字段 : t_buyorder_to_saleorder_sync.BD_SALEORDER_NO
     * </pre>
     * 
     */
    private String bdSaleorderNo;

    /**
     * 主键
     * @return ID 主键
     */
    public Integer getId() {
        return id;
    }

    /**
     * 主键
     * @param id 主键
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 同步方向（1：erp到医购优选，2：医购优选到erp）
     * @return SYNC_DIRECT_TO 同步方向（1：erp到医购优选，2：医购优选到erp）
     */
    public Integer getSyncDirectTo() {
        return syncDirectTo;
    }

    /**
     * 同步方向（1：erp到医购优选，2：医购优选到erp）
     * @param syncDirectTo 同步方向（1：erp到医购优选，2：医购优选到erp）
     */
    public void setSyncDirectTo(Integer syncDirectTo) {
        this.syncDirectTo = syncDirectTo;
    }

    /**
     * 采购单号
     * @return BUYORDER_NO 采购单号
     */
    public String getBuyorderNo() {
        return buyorderNo;
    }

    /**
     * 采购单号
     * @param buyorderNo 采购单号
     */
    public void setBuyorderNo(String buyorderNo) {
        this.buyorderNo = buyorderNo == null ? null : buyorderNo.trim();
    }

    /**
     * 同步状态（0：同步失败；1：同步成功；2：同步中）
     * @return SYNC_STATUS 同步状态（0：同步失败；1：同步成功；2：同步中）
     */
    public Integer getSyncStatus() {
        return syncStatus;
    }

    /**
     * 同步状态（0：同步失败；1：同步成功；2：同步中）
     * @param syncStatus 同步状态（0：同步失败；1：同步成功；2：同步中）
     */
    public void setSyncStatus(Integer syncStatus) {
        this.syncStatus = syncStatus;
    }

    /**
     * 开始时间
     * @return ADD_TIME 开始时间
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * 开始时间
     * @param addTime 开始时间
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * 更新时间
     * @return UPDATE_TIME 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 更新时间
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 贝登ERP销售订单号
     * @return BD_SALEORDER_NO 贝登ERP销售订单号
     */
    public String getBdSaleorderNo() {
        return bdSaleorderNo;
    }

    /**
     * 贝登ERP销售订单号
     * @param bdSaleorderNo 贝登ERP销售订单号
     */
    public void setBdSaleorderNo(String bdSaleorderNo) {
        this.bdSaleorderNo = bdSaleorderNo == null ? null : bdSaleorderNo.trim();
    }
}