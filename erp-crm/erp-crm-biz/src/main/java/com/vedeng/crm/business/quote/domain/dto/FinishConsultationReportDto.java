package com.vedeng.crm.business.quote.domain.dto;

import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 完成咨询
 */
@Data
public class FinishConsultationReportDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报价单id
     */
    @NotNull(message = "报价单id不能为空", groups = DefaultGroup.class)
    private Integer quoteorderId;

}
