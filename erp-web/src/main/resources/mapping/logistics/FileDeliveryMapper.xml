<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.logistics.dao.FileDeliveryMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.FileDelivery" >
    <id column="FILE_DELIVERY_ID" property="fileDeliveryId" jdbcType="INTEGER" />
    <result column="FILE_DELIVERY_NO" property="fileDeliveryNo" jdbcType="VARCHAR" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="SEND_TYPE" property="sendType" jdbcType="INTEGER" />
    <result column="VERIFY_STATUS" property="verifyStatus" jdbcType="BIT" />
    <result column="DELIVERY_STATUS" property="deliveryStatus" jdbcType="BIT" />
    <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT" />
    <result column="DELIVERY_USER_ID" property="deliveryUserId" jdbcType="INTEGER" />
    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    <result column="TRADER_TYPE" property="traderType" jdbcType="BIT" />
    <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR" />
    <result column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER" />
    <result column="TRADER_CONTACT_NAME" property="traderContactName" jdbcType="VARCHAR" />
    <result column="MOBILE" property="mobile" jdbcType="VARCHAR" />
    <result column="TELEPHONE" property="telephone" jdbcType="VARCHAR" />
    <result column="TRADER_ADDRESS_ID" property="traderAddressId" jdbcType="INTEGER" />
    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
    <result column="AREA" property="area" jdbcType="VARCHAR" />
    <result column="CONTENT" property="content" jdbcType="VARCHAR" />
    <result column="IS_CLOSED" property="isClosed" jdbcType="INTEGER" />
    <result column="CLOSED_COMMENTS" property="closedComments" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="DELIEVRY_TYPE" property="delievryType" jdbcType="INTEGER" />
    <result column="DELIVERY_DEPT" property="deliveryDept" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    FILE_DELIVERY_ID,FILE_DELIVERY_NO, COMPANY_ID, SEND_TYPE, VERIFY_STATUS, DELIVERY_STATUS, DELIVERY_TIME, DELIVERY_USER_ID, 
    TRADER_ID, TRADER_TYPE, TRADER_NAME, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, MOBILE, 
    TELEPHONE, TRADER_ADDRESS_ID, ADDRESS, CONTENT, IS_CLOSED , CLOSED_COMMENTS , ADD_TIME, CREATOR, MOD_TIME, UPDATER, AREA
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_FILE_DELIVERY
    where FILE_DELIVERY_ID = #{fileDeliveryId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_FILE_DELIVERY
    where FILE_DELIVERY_ID = #{fileDeliveryId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.logistics.model.FileDelivery" >
    insert into T_FILE_DELIVERY (FILE_DELIVERY_ID, SEND_TYPE, VERIFY_STATUS, 
      DELIVERY_STATUS, DELIVERY_TIME, DELIVERY_USER_ID, 
      TRADER_ID, TRADER_TYPE, TRADER_NAME, 
      TRADER_CONTACT_ID, TRADER_CONTACT_NAME, MOBILE, 
      TELEPHONE, TRADER_ADDRESS_ID, ADDRESS, 
      CONTENT, ADD_TIME, CREATOR, AREA,
      MOD_TIME, UPDATER,COMPANY_ID)
    values (#{fileDeliveryId,jdbcType=INTEGER}, #{sendType,jdbcType=INTEGER}, #{verifyStatus,jdbcType=BIT}, 
      #{deliveryStatus,jdbcType=BIT}, #{deliveryTime,jdbcType=BIGINT}, #{deliveryUserId,jdbcType=INTEGER}, 
      #{traderId,jdbcType=INTEGER}, #{traderType,jdbcType=BIT}, #{traderName,jdbcType=VARCHAR}, 
      #{traderContactId,jdbcType=INTEGER}, #{traderContactName,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{telephone,jdbcType=VARCHAR}, #{traderAddressId,jdbcType=INTEGER}, #{address,jdbcType=VARCHAR}, 
      #{content,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER},#{area,jdbcType=VARCHAR}
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER},#{companyId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.logistics.model.FileDelivery" useGeneratedKeys="true" keyProperty="fileDeliveryId">
    insert into T_FILE_DELIVERY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="fileDeliveryId != null" >
        FILE_DELIVERY_ID,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="sendType != null" >
        SEND_TYPE,
      </if>
      <if test="verifyStatus != null" >
        VERIFY_STATUS,
      </if>
      <if test="deliveryStatus != null" >
        DELIVERY_STATUS,
      </if>
      <if test="deliveryTime != null" >
        DELIVERY_TIME,
      </if>
      <if test="deliveryUserId != null" >
        DELIVERY_USER_ID,
      </if>
      <if test="traderId != null" >
        TRADER_ID,
      </if>
      <if test="traderType != null" >
        TRADER_TYPE,
      </if>
      <if test="traderName != null" >
        TRADER_NAME,
      </if>
      <if test="traderContactId != null" >
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null" >
        TRADER_CONTACT_NAME,
      </if>
      <if test="mobile != null" >
        MOBILE,
      </if>
      <if test="telephone != null" >
        TELEPHONE,
      </if>
      <if test="traderAddressId != null" >
        TRADER_ADDRESS_ID,
      </if>
      <if test="address != null" >
        ADDRESS,
      </if>
      <if test="area != null" >
        AREA,
      </if>
      <if test="content != null" >
        CONTENT,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="delievryType != null" >
        DELIEVRY_TYPE,
      </if>
      <if test="deliveryDept != null" >
        DELIVERY_DEPT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="fileDeliveryId != null" >
        #{fileDeliveryId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="sendType != null" >
        #{sendType,jdbcType=INTEGER},
      </if>
      <if test="verifyStatus != null" >
        #{verifyStatus,jdbcType=BIT},
      </if>
      <if test="deliveryStatus != null" >
        #{deliveryStatus,jdbcType=BIT},
      </if>
      <if test="deliveryTime != null" >
        #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryUserId != null" >
        #{deliveryUserId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null" >
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null" >
        #{traderType,jdbcType=BIT},
      </if>
      <if test="traderName != null" >
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null" >
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null" >
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null" >
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null" >
        #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="area != null" >
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="delievryType != null" >
        #{delievryType,jdbcType=INTEGER},
      </if>
      <if test="deliveryDept != null" >
        #{deliveryDept,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.logistics.model.FileDelivery" >
    update T_FILE_DELIVERY
    <set >
    	<if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="sendType != null" >
        SEND_TYPE = #{sendType,jdbcType=INTEGER},
      </if>
      <if test="verifyStatus != null" >
        VERIFY_STATUS = #{verifyStatus,jdbcType=BIT},
      </if>
      <if test="deliveryStatus != null" >
        DELIVERY_STATUS = #{deliveryStatus,jdbcType=BIT},
      </if>
      <if test="deliveryTime != null" >
        DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryUserId != null" >
        DELIVERY_USER_ID = #{deliveryUserId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null" >
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null" >
        TRADER_TYPE = #{traderType,jdbcType=BIT},
      </if>
      <if test="fileDeliveryNo != null" >
        FILE_DELIVERY_NO = #{fileDeliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="traderName != null" >
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null" >
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null" >
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null" >
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        TELEPHONE = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null" >
        TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="address != null" >
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="area != null" >
        AREA = #{area,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="delievryType != null" >
        DELIEVRY_TYPE = #{delievryType,jdbcType=INTEGER},
      </if>
      <if test="deliveryDept != null" >
        DELIVERY_DEPT = #{deliveryDept,jdbcType=INTEGER},
      </if>
    </set>
    where FILE_DELIVERY_ID = #{fileDeliveryId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.logistics.model.FileDelivery" >
    update T_FILE_DELIVERY
    set SEND_TYPE = #{sendType,jdbcType=INTEGER},
      VERIFY_STATUS = #{verifyStatus,jdbcType=BIT},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      DELIVERY_STATUS = #{deliveryStatus,jdbcType=BIT},
      DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      DELIVERY_USER_ID = #{deliveryUserId,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_TYPE = #{traderType,jdbcType=BIT},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      TELEPHONE = #{telephone,jdbcType=VARCHAR},
      TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      ADDRESS = #{address,jdbcType=VARCHAR},
      ARERA = #{area,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where FILE_DELIVERY_ID = #{fileDeliveryId,jdbcType=INTEGER}
  </update>
  <!-- 获取文件寄送列表 （分页） -->
  <select id="getFileDeliverylistpage" parameterType="Map" resultType="com.vedeng.logistics.model.FileDelivery">
	  	select a.*,b.USERNAME as DELIVERY_USERNAME,c.USERNAME as CREATOR_USERNAME,
	  		e.LOGISTICS_ID,e.LOGISTICS_NO, d.EXPRESS_DETAIL_ID,
			d.BUSINESS_TYPE,
			d.RELATED_ID,
			d.NUM,
			d.AMOUNT,
			f.NAME AS LOGISTICS_NAME
	    from T_FILE_DELIVERY a
	    left join T_USER b on a.DELIVERY_USER_ID = b.USER_ID
	    left join T_USER c on a.CREATOR = c.USER_ID
	    left join T_EXPRESS_DETAIL d on a.FILE_DELIVERY_ID = d.RELATED_ID and d.BUSINESS_TYPE =498
	    left join T_EXPRESS e on d.EXPRESS_ID = e.EXPRESS_ID AND e.IS_ENABLE = 1
	    LEFT JOIN T_LOGISTICS f ON f.LOGISTICS_ID = e.LOGISTICS_ID AND f.IS_ENABLE = 1
	    where 1=1
		<if test="fileDelivery.companyId!=null">
			and a.COMPANY_ID=#{fileDelivery.companyId,jdbcType=INTEGER}
		</if>
		<if test="fileDelivery.sendType!=null and fileDelivery.sendType!='-1'">
			and a.SEND_TYPE=#{fileDelivery.sendType,jdbcType=INTEGER}
		</if>

        <if test="fileDelivery.isClosed!=null and fileDelivery.isClosed!='-1'">
          and a.IS_CLOSED=#{fileDelivery.isClosed,jdbcType=INTEGER}
        </if>

		<if test="fileDelivery.verifyStatus!=null and fileDelivery.verifyStatus!='-1'">
			and a.VERIFY_STATUS=#{fileDelivery.verifyStatus,jdbcType=INTEGER}
		</if>
		<if test="fileDelivery.deliveryStatus!=null and fileDelivery.deliveryStatus!='-1'">
			and a.DELIVERY_STATUS=#{fileDelivery.deliveryStatus,jdbcType=INTEGER}
		</if>
		<if test="fileDelivery.deliveryUserId!=null and fileDelivery.deliveryUserId!='-1'">
			and a.DELIVERY_USER_ID=#{fileDelivery.deliveryUserId,jdbcType=INTEGER}
		</if>
		
		<if test="fileDelivery.fileDeliveryNo!=null and fileDelivery.fileDeliveryNo!=''">
			and a.FILE_DELIVERY_NO like CONCAT('%',#{fileDelivery.fileDeliveryNo,jdbcType=VARCHAR},'%' )
		</if>
		
		<if test="fileDelivery.logisticsId!=null and fileDelivery.logisticsId!='-1'">
			and e.LOGISTICS_ID=#{fileDelivery.logisticsId,jdbcType=INTEGER}
		</if>
		<if test="fileDelivery.deliveryDept!=null and fileDelivery.deliveryDept!=''">
			and a.DELIVERY_DEPT=#{fileDelivery.deliveryDept,jdbcType=INTEGER}
		</if>
		<if test="fileDelivery.logisticsNo!=null and fileDelivery.logisticsNo!=''">
			and e.LOGISTICS_NO like CONCAT('%',#{fileDelivery.logisticsNo,jdbcType=VARCHAR},'%' )
		</if>
		
		<if test="fileDelivery.type != null and fileDelivery.type != ''">
				<choose>
					<when test="fileDelivery.type == 1 or fileDelivery.type == '1'">
						<if test="fileDelivery.beginTime != null and fileDelivery.beginTime != ''">
							AND a.ADD_TIME >= #{fileDelivery.beginTime,jdbcType=INTEGER}
						</if>
						<if test="fileDelivery.endTime != null and fileDelivery.endTime != ''">
							AND a.ADD_TIME <![CDATA[ <= ]]> #{fileDelivery.endTime,jdbcType=INTEGER}
						</if>
					</when>
					<when test="fileDelivery.type == 2 or fileDelivery.type == '2'">
						<if test="fileDelivery.beginTime != null and fileDelivery.beginTime != ''">
							AND a.DELIVERY_TIME >= #{fileDelivery.beginTime,jdbcType=INTEGER}
						</if>
						<if test="fileDelivery.endTime != null and fileDelivery.endTime != ''">
							AND a.DELIVERY_TIME <![CDATA[ <= ]]> #{fileDelivery.endTime,jdbcType=INTEGER}
						</if>
					</when>
					<otherwise></otherwise>
				</choose>
		</if>
		<if test="fileDelivery.traderType!=null and fileDelivery.traderType!='-1'">
			and a.TRADER_TYPE=#{fileDelivery.traderType,jdbcType=INTEGER}
		</if>
		<if test="fileDelivery.traderName!=null and fileDelivery.traderName!=''">
			and a.TRADER_NAME like CONCAT('%',#{fileDelivery.traderName},'%' )
		</if>
		<if test="fileDelivery.creator!=null and fileDelivery.creator!='-1'">
			and a.CREATOR=#{fileDelivery.creator,jdbcType=INTEGER}
		</if>
		<if test="creatorIds.size()>0">
			and a.CREATOR in
			<foreach collection="creatorIds" item="creatorId" index="index"
	            open="(" close=")" separator=",">
	            #{creatorId}
	        </foreach>
		</if>
		<choose>
			<when test="fileDelivery.positionType == 314">
			  order by a.IS_CLOSED ASC ,a.DELIVERY_DEPT !=0 desc, a.FILE_DELIVERY_ID desc
			</when>
			<when test="fileDelivery.positionType == 313">
			  order by a.IS_CLOSED ASC ,a.DELIVERY_DEPT DESC, a.FILE_DELIVERY_ID desc
			</when>
			<otherwise>
			   order by  
	  		   a.FILE_DELIVERY_ID desc
			</otherwise>
		</choose>
  </select>
  <!-- 根据收件人名称查询文件 -->
  <select id="getFileDeliveryListByUName" resultMap="BaseResultMap" parameterType="com.vedeng.authorization.model.User" >
    select 
    <include refid="Base_Column_List" />
    from T_FILE_DELIVERY
    where TRADER_CONTACT_NAME = #{username,jdbcType=VARCHAR}
    <if test="companyId!=null">
		and COMPANY_ID=#{companyId,jdbcType=INTEGER}
	</if>
  </select>

    <update id="updateDeliveryCloseStatus" parameterType="com.vedeng.logistics.model.FileDelivery" >
        update T_FILE_DELIVERY
        <set >
            <if test="isClosed != null" >
              IS_CLOSED = #{isClosed,jdbcType=INTEGER},
            </if>

            <if test="closedComments != null" >
              CLOSED_COMMENTS = #{closedComments,jdbcType=VARCHAR},
            </if>

            <if test="modTime != null" >
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null" >
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        where FILE_DELIVERY_ID = #{fileDeliveryId,jdbcType=INTEGER}
    </update>
</mapper>