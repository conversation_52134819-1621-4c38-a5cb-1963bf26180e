package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 出入库日志主表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchWarehouseGoodsOutInDto extends BatchBaseDto {
    /**
     * 主键
     */
    private Long warehouseGoodsOutInId;

    /**
     * ERP出入库单号（自动生成）
     */
    private String outInNo;

    /**
     * WMS出入库单号
     */
    private String wmsNo;

    /**
     * 关联单号
     */
    private String relateNo;

    /**
     * 出入库类型
     * 1入库 2出库 3销售换货入库 4销售换货出库 5销售退货入库 6采购退货出库 7采购换货出库 8采购换货入库 9外借入库 10外借出库 11调整盘盈入库 12盘盈入库 13报废出库 14领用出库 15 调整盘亏 16 盘亏出库 17采购赠品入库
     */
    private Integer outInType;

    /**
     * 收发货方
     */
    private String outInCompany;

    /**
     * 出入库时间
     */
    private Date outInTime;

    /**
     * 来源 ERP WMS
     */
    private String source;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新备注
     */
    private String updateRemark;

    /**
     * 最大时间
     */
    private Date beginTime;

    /**
     * 最小时间
     */
    private Date endTime;

    /**
     * 类型集合
     */
    private List<Integer> outInTypeList;

    /**
     * 开票税率
     */
    private String rate;

    /**
     * 发票类型
     */
    private String invoiceTypeName;


    private List<BatchWarehouseGoodsOutInItemDto> batchWarehouseGoodsOutInItemDtos = new ArrayList<>();


    /**
     * 是否虚拟 0否 1是
     */
    private Integer isVirtual;

    /**
     * 销售单归属erp销售部门
     */
    private String orgName;

    /**
     * 销售单关联的 客户id
     */
    private Integer traderCustomerId;

    /**
     * 限制的新增时间
     */
    private Long findGiftAndHaveBlueInvoiceTime;

    /**
     * 调整单主键id
     */
    private Integer saleSettlementAdjustmentId;

    /**
     * 是否整单赠品
     * 默认否
     */
    private Integer wholeGiftSaleOrder = 0 ;
}