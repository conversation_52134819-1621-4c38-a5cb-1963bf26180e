package com.vedeng.erp.trader.service;

import com.vedeng.authorization.model.User;
import com.vedeng.erp.trader.domain.dto.TraderFinanceDetail;
import com.vedeng.erp.trader.domain.entity.TraderCustomerFinance;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * @description:
 * @author: yana.jiang
 * @date: 2022/11/28
 */
public interface TraderFinancialService {
    /**
     * 根据traderCustomerFinanceId 查客户回显使用
     * @param traderCustomerFinanceId
     * @return
     */
    TraderFinanceDetail getTraderFinancialById(Integer traderCustomerFinanceId);

    /**
     * 根据根据traderCustomerFinanceId 修改 客户财务专用
     * @param traderFinance
     * @param user
     */
    void updateTraderCustomerFinance(TraderCustomerFinance traderFinance, User user);

    /**
     * 批量导入客户
     * @param file
     * @param user
     */
    void importTraderCustomerFinancial(MultipartFile file, User user) throws Exception;

    /**
     * 数据同步
     * @param startDate
     * @param endDate
     */
	void syncTrader(Long startDate, Long endDate);
    
    
}
