package com.vedeng.erp.aftersale.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/12/15 9:17
 **/
@Setter
@Getter
public class YxbSnDto {
    /**
     * yxb 单号
     */
    private String orderNo;

    /**
     * erp 单号
     */
    private String erpOrderNo;

    /**
     * 验收方式，1 电话回访 2 纸单验收 3 短信通知
     */
    private Integer checkType;

    /**
     * 验收结论：4085,客户设备已安调 4086,客户设备未安调不需要安调 4087,超期免除义务 4088,客户设备未安调已协助报售后 4089,客户设备未安调后续再跟进
     */
    private Integer checkConclusion;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkDate;

    /**
     * 附件
     */
    private List<String> fileList;

    private List<SnDetail> detail;


    @Setter
    @Getter
    public static class SnDetail {

        /**
         * sku
         */
        private String sku;

        /**
         * sn
         */
        private String sn;

        private String msg;

    }

}
