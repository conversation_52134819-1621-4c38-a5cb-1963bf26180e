package com.vedeng.finance.dto;

import lombok.Data;

import java.util.List;

/**
 * 航信进项发票传输类
 * <AUTHOR>
 * @date created in 2020/5/18 13:57
 */
@Data
public class HxIncomeInvoiceDTO {

    /**
     * 发票种类：s专票，c普票
     */
    private String fpzl;

    /**
     * 发票状态，0正常，1失控，2作废，3红冲，4异常
     */
    private String fpzt;

    /**
     * 发票代码
     */
    private String fpdm;

    /**
     * 发票号码
     */
    private String fphm;

    /**
     * 销方税号
     */
    private String xfsh;

    /**
     * 销方名称
     */
    private String xfmc;

    /**
     * 销方地址电话
     */
    private String xfdzdh;

    /**
     * 销方开户行及账号
     */
    private String xfyhzh;

    /**
     * 购方税号
     */
    private String gfsh;

    /**
     * 购方名称
     */
    private String gfmc;

    /**
     * 购方地址电话
     */
    private String gfdzdh;

    /**
     * 购方开户行及账号
     */
    private String gfyhzh;

    /**
     * 开票日期
     */
    private String kprq;

    /**
     * 发票金额
     */
    private String je;

    /**
     * 发票税额
     */
    private String se;

    /**
     * 价税合计
     */
    private String jshj;

    /**
     * 备注
     */
    private String bz;

    /**
     * 开票人
     */
    private String kpr;

    /**
     * 复核人
     */
    private String fhr;

    /**
     * 收款人
     */
    private String skr;

    private List<HxIncomeInvoiceDetailDTO> detailList;
}
