package com.vedeng.erp.quote.mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.vedeng.erp.quote.domain.AuthorizationApplyEntity;

public interface AuthorizationApplyMapper {
    /**
     * delete by primary key
     * @param authorizationApplyId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer authorizationApplyId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(AuthorizationApplyEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(AuthorizationApplyEntity record);

    /**
     * select by primary key
     * @param authorizationApplyId primary key
     * @return object by primary key
     */
    AuthorizationApplyEntity selectByPrimaryKey(Integer authorizationApplyId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(AuthorizationApplyEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(AuthorizationApplyEntity record);

    AuthorizationApplyEntity findByAuthorizationApplyId(@Param("authorizationApplyId")Integer authorizationApplyId);


}