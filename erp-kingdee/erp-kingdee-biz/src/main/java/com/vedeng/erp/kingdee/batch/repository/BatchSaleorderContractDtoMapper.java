package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesDto;
import com.vedeng.erp.kingdee.batch.dto.BatchSaleorderContractDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BatchSaleorderContractDtoMapper {

    List<BatchSaleorderContractDto> querySaleorderContract(BatchSaleorderContractDto dto);
    List<BatchSaleorderContractDto> querySaleorderContractFile(BatchSaleorderContractDto dto);
    List<BatchSaleorderContractDto> queryConfirmation(BatchSaleorderContractDto dto);
    List<BatchSaleorderContractDto> onlineQueryConfirmation(BatchSaleorderContractDto dto);
    List<BatchSaleorderContractDto> queryConfirmationFile(BatchSaleorderContractDto dto);
    List<BatchSaleorderContractDto> queryOnlineConfirmationFile(BatchSaleorderContractDto dto);
}
