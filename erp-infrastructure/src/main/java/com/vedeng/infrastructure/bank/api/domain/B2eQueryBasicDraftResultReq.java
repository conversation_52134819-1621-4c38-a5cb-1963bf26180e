package com.vedeng.infrastructure.bank.api.domain;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 融资申请结果查询请求
 * @date 2024/10/14 14:48
 */
@Data
public class B2eQueryBasicDraftResultReq {

    /**
     * 客户技术请求流水号
     */
    private String trnId;

    /**
     * 借据号/银承协议号
     */
    private String creditCode;
    /**
     * 融资申请编号
     */
    private String applicationNo;
    /**
     * 出账申请起始日期格式 yyyy-MM-dd
     */
    private String startDate;
    /**
     * 出账申请终止日期格式 yyyy-MM-dd
     */
    private String endDate;
    /**
     * 最小放款金额
     */
    private String minAmount;
    /**
     * 最大放款金额
     */
    private String maxAmount;
    /**
     * 担保方式
     * 不输入查所有担保方式
     * 00-无担保
     * 01-100%保证金
     * 02-理财产品质押
     * 03-电子存单质押
     * 04-票据池质押
     */
    private String assureWay;
    /**
     * 融资申请状态
     * A-所有
     * S-审批通过
     * E-审批拒绝
     * R-审批中
     */
    private String status;
    /**
     * 当前页码(从 1 开始，不传默认为 1)
     */
    private String pageNo;
    /**
     * 每页数据条数(默认 10 条，最大每页
     * 100 条)
     */
    private String pageSize;

}
