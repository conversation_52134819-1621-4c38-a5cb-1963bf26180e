<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>查看SPU</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/spu/spu_view.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/manage.css" />
</head>
<body>
    <div class="detail-wrap">
        <div class="detail-title">查看SPU：${command.showName}
        </div>
<c:if test="${firstEngage != null and firstEngage.status != null and firstEngage.status != 3}">
    <div class="vd-tip tip-red">
    <i class="vd-tip-icon vd-icon icon-info2"></i>
    <div class="vd-tip-cnt">当前首营信息没有审核通过，请暂停对该SPU进行审核。<br>请通知提交人对首营信息进行编辑后，重新提交审核。</div>
    </div>
</c:if>
    <div class="tab-nav">
        <a class="tab-item current" href="#">基本信息</a>
<%--        <c:if test="${command.spuLevel!=2 && showType ne 'op'}">&lt;%&ndash;showType ne 'op'运营后台连接跳转无需这些操作&ndash;%&gt;--%>
<%--            <c:if test="${empty command.operateInfoId or command.operateInfoId==0 and command.checkStatus==3}">--%>
<%--                <a class="tab-item"   href="/vgoods/operate/openOperate.do?spuId=${command.spuId}">运营信息</a>--%>
<%--            </c:if>--%>
<%--            <c:if test="${not empty command.operateInfoId and command.operateInfoId>0}">--%>
<%--                <a class="tab-item"   href="/vgoods/operate/viewOperate.do?spuId=${command.spuId}">运营信息</a>--%>
<%--            </c:if>--%>
<%--        </c:if>--%>
        </div>
            <div class="detail-option-wrap">
                <c:if test="${showType ne 'op'}"><%--showType ne 'op'运营后台连接跳转无需这些操作--%>
                    <div class="option-btns">
                        <%--<c:if test="${command.spuLevel==2 && command.hasEditTempAuth && command.checkStatus != 1}">--%>
                            <%--<a class="btn btn-small btn-blue" href="/goods/vgoods/addTempSpu.do?spuId=${command.spuId}"> 编辑</a>--%>
                        <%--</c:if>--%>
                        <c:if test="${ command.hasEditAuth && command.checkStatus != 1}">
                            <a class="btn btn-small btn-blue" href="/goods/vgoods/addSpu.do?spuId=${command.spuId}"> 编辑</a>
                        </c:if>
                        <c:if test="${command.hasEditAuth && command.checkStatus == 5}">
                            <a class="btn btn-small btn-blue J-spu-submitVerify" href="javascript:void(0);" data-id="${command.spuId}">提交审核</a>
                        </c:if>
                        <c:if test="${command.checkStatus == 1}">
                            <c:choose>
                                <c:when test="${command.hasCheckAuth}">
                                    <a class="btn btn-small btn-blue J-spu-verify" href="javascript:void(0);" data-id="${command.spuId}">审核通过</a>
                                    <a class="btn btn-small btn-blue J-spu-noverify" data-id="${command.spuId}" href="javascript:void(0);">审核不通过</a>
                                </c:when>
                                <c:otherwise>
                                    <a class="btn btn-small" style="color: grey">已申请审核</a>
                                </c:otherwise>
                            </c:choose>
                        </c:if>
                        <c:if test="${command.spuLevel==2 && command.hasEditAuth }">
                            <a class="btn btn-small btn-blue J-change-spu" data-href="./changeTempToCoreSpu.do?spuId=${command.spuId}"> 转为普通商品</a>
                        </c:if>
                    </div>
                </c:if>
            </div>


    <div class="detail-block">
        <div class="block-title">分类信息</div>
        <div class="detail-table">
            <div class="table-item item-col">
                <div class="table-th">分类归属：</div>
                <div class="table-td">
                    ${cateName}
                </div>
            </div>
        </div>
    </div>

    <div class="detail-block">
        <div class="block-title">等级及档位</div>
        <div class="detail-block-tip vd-tip tip-blue">
            <i class="vd-tip-icon vd-icon icon-info2"></i>
            <div class="vd-tip-cnt">SPU的商品等级、商品档位修改后，对已经创建的SKU无影响，只会作为新增SKU时的参考依据。</div>
        </div>
        <div class="detail-table">
            <div class="table-item">
                <div class="table-th">商品等级：</div>
                <div class="table-td">
                    <c:if test="${command.goodsLevelVo ne null}">
                        ${command.goodsLevelVo.levelName}
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">描述：</div>
                <div class="table-td">
                    <c:if test="${command.goodsLevelVo ne null}">
                        ${command.goodsLevelVo.description}
                    </c:if>
                    <c:if test="${command.goodsLevelVo eq null}">
                        无
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">商品档位</div>
                <div class="table-td">
                    <c:if test="${command.goodsPositionVo ne null}">
                        ${command.goodsPositionVo.positionName}
                    </c:if>
                    <c:if test="${command.goodsPositionVo eq null}">
                        无档位
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">描述：</div>
                <div class="table-td">
                    <c:if test="${command.goodsPositionVo ne null}">
                        ${command.goodsPositionVo.description}
                    </c:if>
                </div>
            </div>
        </div>
    </div>

    <div class="detail-block block-nohidden">
            <div class="block-title">SKU信息</div>
            <div class="detail-block-tip vd-tip tip-blue">
                <i class="vd-tip-icon vd-icon icon-info2"></i>
                <div class="vd-tip-cnt">待办事项未完成，产品无法推送到前台！目前已添加<span class="J-sku-num">0</span>条，最多可添加200条。</div>
            </div>
            <div class="detail-block-option">
                <c:if test="${command.spuLevel!=2  && command.hasEditAuth && showType ne 'op'}">
                    <a href="javascript:void(0);" tabtitle={"num":"vgoodsaddsku${command.spuId}","link":"/goods/vgoods/addSku.do?spuId=${command.spuId}","title":"新增SKU"} class="btn btn-small">新增SKU</a>
                </c:if>
                <c:if test="${command.spuLevel==2 && command.hasEditTempAuth && showType ne 'op'}">
                    <a class="btn btn-small J-sku-edit" data-spuid="${command.spuId}" data-spuname="${command.showName}" data-type="${command.skuType}">新增SKU</a>
                </c:if>
            </div>
            <div class="detail-table J-sku-list" data-spuid="${command.spuId}" data-spulv="${command.spuLevel}" data-spuname="${command.showName}">
                <table class="table table-base">
                    <colgroup>
                        <col width="">
                        <col width="">
                        <col width="">
                        <col width="">
                        <col width="">
                        <col width="">
                        <col width="">
                    </colgroup>
                    <tbody>
                    <tr class="J-th-item">
                        <th>订货号</th>
                        <th>商品名称</th>
                        <th>规格</th>
                        <th>制造商型号</th>
                        <th>审核状态</th>
                        <th>待办事项</th>
                        <th>操作</th>
                    </tr>
                    <tr class="J-tr-nodata" style="display: none;">
                        <td class="list-no-data" colspan="6">
                            <i class="vd-icon icon-info1"></i>没有匹配数据
                        </td>
                    </tr>
                    </tbody>
                </table>
                <div class="J-pager pager-list-wrap"></div>
            </div>
        </div>

    <div class="detail-block">
        <div class="block-title">基本信息</div>
        <div class="detail-table">
            <div class="table-item">
                <div class="table-th">商品品牌：</div>
                <div class="table-td">
                    ${brand.brandName}
                </div>
            </div>

            <div class="table-item">
                <div class="table-th">商品类型：</div>
                <div class="table-td">
                    <c:forEach var="spuType" items="${command.spuTypeList}" varStatus="status">
                        <c:if test="${command.spuType == spuType.sysOptionDefinitionId }"> ${spuType.title } </c:if>
                    </c:forEach>
                </div>
            </div>
<%--            <div class="table-item">--%>
<%--                <div class="table-th">税收编码：</div>--%>
<%--                <div class="table-td">--%>
<%--                    ${command.taxClassificationCode}--%>
<%--                </div>--%>
<%--            </div>--%>
<%--            <div class="table-item">--%>
<%--                <div class="table-th">税收编码简称：</div>--%>
<%--                <div class="table-td">--%>
<%--                    ${command.taxCodeSimpleName}--%>
<%--                </div>--%>
<%--            </div>--%>
            <div class="table-item">
                <div class="table-th">
                    <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                    是否在《医疗器械分类目录》：
                </div>
                <div class="table-td">
                    ${command.medicalInstrumentCatalogIncluded eq 1? '是':'否'}
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">
                    <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                    是否有注册证/备案凭证：
                </div>
                <div class="table-td">
                    ${command.hasRegistrationCert? '有':'无'}
                </div>
            </div>

            <div class="table-item">
                <div class="table-th">非医疗器械一级分类：</div>
                <div class="table-td">
                    <c:choose>
                        <c:when test="${command.noMedicalFirstType eq 1}">科研产品</c:when>
                        <c:when test="${command.noMedicalFirstType eq 2}">医疗器械附件产品</c:when>
                        <c:when test="${command.noMedicalFirstType eq 3}">其他非医疗器械</c:when>
                    </c:choose>

                </div>
            </div>

            <div class="table-item">
                <div class="table-th">非医疗器械二级分类：</div>
                <div class="table-td">
                    <c:choose>
                        <c:when test="${command.noMedicalSecondType eq 1}">科研配件</c:when>
                        <c:when test="${command.noMedicalSecondType eq 2}">实验耗材</c:when>
                        <c:when test="${command.noMedicalSecondType eq 3}">仪器设备</c:when>
                    </c:choose>
                </div>
            </div>



            <c:if test="${command.spuType eq 316 and command.hasRegistrationCert}">
                <div class="table-item">
                    <div class="table-th">
                        是否为大型医疗设备：
                    </div>
                    <div class="table-td">
                            ${command.secondLevelSpuType eq 3161? '是':'否'}

                    </div>
                </div>
            </c:if>
            <%--Note:"是否有注册证/备案凭证”为“有”时显示--%>
           <c:if test="${command.hasRegistrationCert}">
                <div class="table-item">
                    <div class="table-th">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                        产品名称（注册证/备案凭证）：
                    </div>
                    <div class="table-td">
                        ${command.spuName}

                    </div>
                </div>
           </c:if>
            <%--Note:"是否有注册证/备案凭证”为“有”时显示--%>
            <c:if test="${command.hasRegistrationCert}">
                <div class="table-item">
                    <div class="table-th">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                        规格、型号（注册证/备案凭证）：
                    </div>
                    <div class="table-td">
                            ${command.specsModel}
                    </div>
                </div>
            </c:if>
            <div class="table-item">
                <div class="table-th">spu名称：</div>
                <div class="table-td">
                ${command.showName}
                </div>
            </div>
<%--<c:if test="${command.spuLevel!=2}">--%>
            <div class="table-item">
                <div class="table-th">注册商标：</div>
                <div class="table-td">
                ${command.registrationIcon}
                </div>
            </div>
            <%--<div class="table-item">--%>
                <%--<div class="table-th">wiki链接：</div>--%>
                <%--<div class="table-td">--%>
                    <%--<a href="${command.wikiHref}" target="_blank">${command.wikiHref}</a>--%>
                <%--</div>--%>
            <%--</div>--%>
<%--</c:if>--%>
            <div class="table-item">
                <div class="table-th">存储条件（温度）：</div>
                <div class="table-td">
                    <c:if test="${ command.goodsStorageConditionVo.storageConditionTemperature ==1 }"> 常温0-30℃ </c:if>
                    <c:if test="${ command.goodsStorageConditionVo.storageConditionTemperature ==2 }"> 阴凉0-20℃ </c:if>
                    <c:if test="${ command.goodsStorageConditionVo.storageConditionTemperature ==3 }"> 冷藏2-10℃   </c:if>
                    <c:if test="${ command.goodsStorageConditionVo.storageConditionTemperature ==4 }">
                        <c:if test="${not empty command.goodsStorageConditionVo.storageConditionTemperatureLowerValue and not empty command.goodsStorageConditionVo.storageConditionTemperatureUpperValue}">
                            其他温度<fmt:formatNumber value="${command.goodsStorageConditionVo.storageConditionTemperatureLowerValue}" type="number"/>℃
                            -
                            <fmt:formatNumber value="${command.goodsStorageConditionVo.storageConditionTemperatureUpperValue}" type="number"/>℃
                        </c:if>
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">启用状态：</div>
                <div class="table-td">
                    <c:choose>
                        <c:when test="${command.status==1}">
                            <span>已启用</span>
                        </c:when>
                        <c:when test="${command.status==0}">
                            <span>已禁用</span>
                        </c:when>
                        <c:otherwise>
                            <span>审核中</span>
                        </c:otherwise>
                    </c:choose>
                </div>
            </div>
            <div class="table-item">
            <div class="table-th">存储条件(湿度）：</div>
                <div class="table-td">
                    <c:if test="${not empty command.goodsStorageConditionVo.storageConditionHumidityLowerValue and not empty command.goodsStorageConditionVo.storageConditionHumidityUpperValue}">
                        <fmt:formatNumber value="${command.goodsStorageConditionVo.storageConditionHumidityLowerValue}" type="number"/>%
                        -
                        <fmt:formatNumber value="${command.goodsStorageConditionVo.storageConditionHumidityUpperValue}" type="number"/>%
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">禁用原因：</div>
                <div class="table-td">
                    <c:if test="${command.status==0}">
                        ${command.disabledReason}
                    </c:if>
                </div>
            </div>
            <div class="table-item item-col">
                <div class="table-th">存储条件（其他）：</div>
                <div class="table-td">
                    <c:forEach items="${command.goodsStorageConditionVo.storageConditionOthersArray}" var="item">
                        <c:if test="${fn:contains(item, '1') }"> 通风 </c:if>
                        <c:if test="${fn:contains(item, '2')}"> 干燥 </c:if>
                        <c:if test="${fn:contains(item, '3') }"> 避光 </c:if>
                        <c:if test="${fn:contains(item, '4') }"> 防潮 </c:if>
                        <c:if test="${fn:contains(item, '5') }"> 避热 </c:if>
                        <c:if test="${fn:contains(item, '6')}"> 密封 </c:if>
                        <c:if test="${fn:contains(item, '7') }"> 密闭 </c:if>
                        <c:if test="${fn:contains(item, '8') }"> 严封 </c:if>
                        <c:if test="${fn:contains(item, '9') }"> 遮光 </c:if>
                    </c:forEach>

                </div>
            </div>

        </div>
    </div>
    <div class="detail-block" <c:if test="${!command.hasRegistrationCert}"> style="display:none" </c:if>  >
        <div class="block-title">
            注册证/备案信息
            <c:if test="${command.hasCheckAuth && showType ne 'op' && firstEngage.status == 1}" >
                  <a href="javascript:void(0);" class="btn btn-blue btn-small btn-title J-first-pass" data-id="${firstEngage.firstEngageId}">审核通过</a>
                  <a href="javascript:void(0);" class="btn btn-blue btn-small btn-title J-first-refuse" data-id="${firstEngage.firstEngageId}">审核不通过</a>
            </c:if>
        </div>
        <div class="detail-table">
            <div class="table-item">
                <div class="table-th">注册证号/备案凭证号：</div>
                <div class="table-td">

<a href="javascript:void(0);" tabTitle='{"num":"firstengage${firstEngage.firstEngageId}","link":"/firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${firstEngage.firstEngageId}","title":"查看首营"}' >${firstEngage.registrationNumber}</a>


                </div>
            </div>
            <div class="table-item">
                <div class="table-th">证件有效期：</div>
                <div class="table-td">
                    ${firstEngage.effectStartDate} 至 ${firstEngage.effectEndDate}
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">管理类别：</div>
                <div class="table-td">
                    ${firstEngage.manageCategoryLevelShow}
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">注册人/备案人名称：</div>
                <div class="table-td">
                    ${firstEngage.productCompanyChineseName}
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">旧国标分类：</div>
                <div class="table-td">
                    ${firstEngage.oldStandardCategoryName}
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">新国标分类：</div>
                <div class="table-td">
                    ${firstEngage.newStandardCategoryName}
                </div>
            </div>
            <div class="table-item item-col">
                <div class="table-th">审核状态：</div>
                <div class="table-td">
                    <c:if test="${firstEngage.status==2}">
                        <span class="title-status status-red">审核不通过</span>
                    </c:if>
                    <c:if test="${firstEngage.status==1}">
                        <span class="title-status status-yellow">审核中</span>
                    </c:if>
                    <c:if test="${firstEngage.status==3}">
                        <span class="title-status status-green">审核通过</span>
                    </c:if>
                    <c:if test="${firstEngage.status==5}">
                        <span class="title-status status-green">待提交审核</span>
                    </c:if>
                </div>
            </div>
        </div>

    </div>

    <c:if test="${command.spuLevel!=2}">
    <div class="detail-block">
        <div class="block-title">应用信息</div>
        <div class="detail-table">
            <div class="table-item  ">
                <div class="table-th">科室：</div>
                <div class="table-td J-spring-filter">
                <c:forEach var="departmentsHospital" items="${command.departmentsHospitalList}" varStatus="status">
                     <c:if test="${departmentsHospital.selected}"> ${departmentsHospital.departmentName} 、 </c:if>
                </c:forEach>
                </div>
            </div>
            <div class="table-item  ">
                <div class="table-th">收费项目：</div>
                <div class="table-td">
                        ${command.hospitalTagsShow}
                </div>
            </div>
            <div class="table-item  ">
                <div class="table-th">检查项目：</div>
                <div class="table-td">
                    <c:forEach var="inspectionItemVo" items="${command.inspectionItemVos}" varStatus="status">
                        <c:if test="${inspectionItemVo.selected}"> ${inspectionItemVo.name} 、 </c:if>
                    </c:forEach>
                </div>
            </div>
        </div>
    </div>
    <div class="detail-block">
        <div class="block-title">属性信息</div>
        <div class="detail-table">
            <div class="table-item item-col">
                <div class="table-th">选择带入分类属性：</div>
                <div class="table-td J-spring-filter">
    <c:forEach var="baseAttribute" items="${command.baseAttributes}" varStatus="status">
         <c:if test="${baseAttribute.selected}"> ${baseAttribute.baseAttributeName} 、 </c:if>
    </c:forEach>
                </div>
            </div>
            <div class="table-item item-col">
                <div class="table-th">选择主属性：</div>
                <div class="table-td J-spring-filter">
                    <c:forEach var="primaryAttribute" items="${command.primaryAttributes}" varStatus="status">
                        <c:if test="${primaryAttribute.isPrimary eq 1}"> ${primaryAttribute.baseAttributeName} 、 </c:if>
                    </c:forEach>
                </div>
            </div>
        </div>
    </div>

        <div class="detail-block">
            <div class="block-title">产品资料</div>
            <div class="detail-table">
                <div class="table-item item-col">
                    <div class="table-th">wiki链接：</div>
                    <div class="table-td">
                        <a href="${command.wikiHref}" target="_blank">${command.wikiHref}</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="detail-block">
            <div class="block-title">产品归属</div>
            <div class="detail-table">
                <div class="table-item">
                    <div class="table-th">产品经理：</div>
                    <div class="table-td">
                      ${command.assignmentManagerName}
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">产品助理：</div>
                    <div class="table-td">
                      ${command.assignmentAssistantName}
                    </div>
                </div>
            </div>
        </div>



        <div class="detail-block">
            <div class="block-title">
                禁用审核记录
            </div>
            <div class="status-title">
                <div class="status-item">操作时间</div>
                <div class="status-item">操作人</div>
                <div class="status-item">操作事项</div>
                <div class="status-item">审核状态</div>
                <div class="status-item" style="width:300px">备注</div>
            </div>
            <div class="status-list">
                <c:choose>
                    <c:when test="${not empty disableGoodsVerifyRecord}">
                        <c:forEach items="${disableGoodsVerifyRecord}" var="log" varStatus="index">
                            <c:if test="${not empty  log.operate}">
                                <c:if test="${index.count==6}">
                                    <div class="status-more J-optional-more">
                                </c:if>
                                <div class="status-cnt">
                                    <div class="status-item">
                                        <fmt:formatDate value="${log.operateTime}" pattern="yyyy-MM-dd HH:mm:ss" />
                                    </div>
                                    <div class="status-item" style="word-wrap: break-word;">${log.operaterName}</div>
                                    <div class="status-item">${log.operate}</div>
                                    <div class="status-item">
                                        <c:choose>
                                            <c:when test="${log.operate == '审核不通过'}">审核不通过</c:when>
                                            <c:when test="${log.operate == '审核通过'}">审核通过</c:when>
                                            <c:otherwise>审核中</c:otherwise>
                                        </c:choose>
                                    </div>
                                    <div class="status-item" style="width:300px">${log.comment}</div>
                                </div>
                                <c:if test="${disableGoodsVerifyRecord.size()== index.count && index.count>5 }">
                                    </div>
                                </c:if>
                            </c:if>
                        </c:forEach>
                    </c:when>
                    <c:otherwise>
                        <div class="status-cnt">
                            <div class="status-item"></div>
                            <div class="status-item"></div>
                            <div class="status-item" style="text-align: center;">暂无审核记录！</div>
                        </div>
                    </c:otherwise>
                </c:choose>
            </div>
            <c:if test="${disableGoodsVerifyRecord.size() > 5 }">
                <div class="detail-optional J-toggle-show">
                    <span class="toggle-txt J-more">展开更多<i class="vd-icon icon-down"></i></span>
                    <span class="toggle-txt J-less" style="display: none;">收起<i class="vd-icon icon-up"></i></span>
                </div>
            </c:if>

            <c:if test="${command.status == 2}">
                <div style="text-align: center">
                   <c:if test="${ not empty disableGoodsTask && isDisableGoodsCandidate}">
                       <a class="btn btn-small btn-blue J-sku-disableOption" data-dlg='0' data-href="/goods/vgoods/complementTask.do" data-params='{"taskId":${disableGoodsTask.id},"pass": true, "relatedId":${command.spuId},"goodsType": 1 }'>审核通过</a>
                       &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp;
                       <a class="btn btn-small btn-red J-sku-disableOption" data-href="/goods/vgoods/complementTask.do" data-params='{"taskId":${disableGoodsTask.id},"pass": false, "relatedId":${command.spuId},"goodsType": 1 }'>审核不通过</a>
                   </c:if>
                </div>
            </c:if>


        </div>

            <div class="detail-block">
                <div class="block-title">审核记录
                    <c:if test="${command.checkStatus==0}">
                        <span class="title-status status-yellow">待完善</span>
                    </c:if>
                    <c:if test="${command.checkStatus==2}">
                        <span class="title-status status-red">审核不通过</span>
                    </c:if>
                    <c:if test="${command.checkStatus==1}">
                        <span class="title-status status-yellow">审核中</span>
                    </c:if>
                    <c:if test="${command.checkStatus==3}">
                        <span class="title-status status-green">审核通过</span>
                    </c:if>
                    <c:if test="${command.checkStatus==5}">
                        <span class="title-status status-green">待提交审核</span>
                    </c:if>
                </div>
                <div class="status-title">
                    <div class="status-item">操作时间</div>
                    <div class="status-item">操作人</div>
                    <div class="status-item">操作事项</div>
                    <div class="status-item">审核状态</div>
                    <div class="status-item">备注</div>
                </div>
                <div class="status-list">
                    <c:choose>
                        <c:when test="${not empty newLogCheckList}">
                            <c:forEach items="${newLogCheckList}" var="log" varStatus="index">
                                <c:if test="${index.count==6}">
                                    <div class="status-more J-optional-more">
                                </c:if>
                                <div class="status-cnt">
                                    <div class="status-item">
                                        <fmt:formatDate value="${log.addTime}" pattern="yyyy-MM-dd HH:mm:ss" />
                                    </div>
                                    <div class="status-item">${log.creatorName}</div>
                                    <div class="status-item">${log.logStatusName}</div>
                                    <div class="status-item">
                                        <c:choose>
                                            <c:when test="${log.logStatus == 1}">审核中</c:when>
                                            <c:when test="${log.logStatus == 2}">审核不通过</c:when>
                                            <c:when test="${log.logStatus == 3}">审核通过</c:when>
                                        </c:choose>
                                    </div>
                                    <div class="status-item">${log.logMessage}</div>
                                </div>
                                <c:if test="${newLogCheckList.size()== index.count && index.count>5 }">
                                    </div>
                                </c:if>
                            </c:forEach>
                        </c:when>
                        <c:otherwise>
                            <div class="status-cnt">
                                <div class="status-item"></div>
                                <div class="status-item"></div>
                                <div class="status-item" style="text-align: center;">暂无审核记录！</div>
                            </div>
                        </c:otherwise>
                    </c:choose>
                </div>
                <c:if test="${newLogCheckList.size() > 5 }">
                    <div class="detail-optional J-toggle-show">
                        <span class="toggle-txt J-more">展开更多<i class="vd-icon icon-down"></i></span>
                        <span class="toggle-txt J-less" style="display: none;">收起<i class="vd-icon icon-up"></i></span>
                    </div>
                </c:if>
            </div>

            <div class="detail-block">
                <div class="block-title">审核记录（旧）
                    <c:if test="${command.checkStatus==0}">
                        <span class="title-status status-yellow">待完善</span>
                    </c:if>
                    <c:if test="${command.checkStatus==2}">
                        <span class="title-status status-red">审核不通过</span>
                    </c:if>
                    <c:if test="${command.checkStatus==1}">
                        <span class="title-status status-yellow">审核中</span>
                    </c:if>
                    <c:if test="${command.checkStatus==3}">
                        <span class="title-status status-green">审核通过</span>
                    </c:if>
                    <c:if test="${command.checkStatus==5}">
                        <span class="title-status status-green">待提交审核</span>
                    </c:if>

                </div>
                <div class="status-title">
                    <div class="status-item">操作时间</div>
                    <div class="status-item">操作人</div>
                    <div class="status-item">操作事项</div>
                    <div class="status-item">审核状态</div>
                    <div class="status-item">备注</div>
                </div>
                <div class="status-list">
                    <c:choose>
                        <c:when test="${not empty oldLogCheckList}">
                            <c:forEach items="${oldLogCheckList}" var="log" varStatus="index">
                                <c:if test="${index.count==6}">
                                    <div class="status-more J-optional-more">
                                </c:if>
                                <div class="status-cnt">
                                    <div class="status-item">
                                        <fmt:formatDate value="${log.addTime}" pattern="yyyy-MM-dd HH:mm:ss" />
                                    </div>
                                    <div class="status-item">${log.creatorName}</div>
                                    <div class="status-item">${log.logStatusName}</div>
                                    <div class="status-item">
                                        <c:choose>
                                            <c:when test="${log.logStatus == 1}">审核中</c:when>
                                            <c:when test="${log.logStatus == 2}">审核不通过</c:when>
                                            <c:when test="${log.logStatus == 3}">审核通过</c:when>
                                        </c:choose>
                                    </div>
                                    <div class="status-item">${log.logMessage}</div>
                                </div>
                                <c:if test="${oldLogCheckList.size()== index.count && index.count>5 }">
                                    </div>
                                </c:if>
                            </c:forEach>
                        </c:when>
                        <c:otherwise>
                            <div class="status-cnt">
                                <div class="status-item"></div>
                                <div class="status-item"></div>
                                <div class="status-item" style="text-align: center;">暂无审核记录！</div>
                            </div>
                        </c:otherwise>
                    </c:choose>
                </div>
                <c:if test="${oldLogCheckList.size() > 5 }">
                    <div class="detail-optional J-toggle-show">
                        <span class="toggle-txt J-more">展开更多<i class="vd-icon icon-down"></i></span>
                        <span class="toggle-txt J-less" style="display: none;">收起<i class="vd-icon icon-up"></i></span>
                    </div>
                </c:if>
            </div>

</c:if>
    </div>
    <script type="text/tmpl" class="J-dlg-tmpl">
        <div class="del-wrap">
            <div class="del-tip">
                <i class="vd-icon icon-caution2"></i><span class="J-dlg-tip"></span>
            </div>
            <form class="J-dlg-form">
                <div class="del-cnt base-form">
                    <textarea name="content" id="" cols="30" rows="10" class="input-textarea J-dlg-cnt" placeholder="必填：请填写删除原因，最少10个字，最多300个字"></textarea>
                </div>
            </form>
        </div>
    </script>
    <script type="text/tmpl" class="J-sku-tmpl">
        <div class="edit-sku-wrap base-form form-span-6">
            <form class="J-sku-form" style="display: none;">
                <div class="form-item">
                    <div class="form-label">SPU：</div>    
                    <div class="form-fields form-label-txt">{{=spuName}}</div>    
                </div>    
                <div class="form-item">
                    <div class="form-label">
                        <span class="must">*</span>
                        {{if(type == 1){ }}
                            制造商型号： 
                        {{ }else{ }}
                            规格：    
                        {{ } }}
                    </div>    
                    <div class="form-fields">
                        <div class="form-col col-10">
                            <input class="input-text J-cnt" name="content" type="text"> 
                        </div>
                    </div>    
                </div>  
            </form>  
            <div class="dlg-loading J-sku-loading">
                <img src="${pageContext.request.contextPath}/static/new/img/loading.gif" alt="">
            </div>
        </div>
    </script>
    <script type="text/tmpl" class="J-sku-list-tmpl">
        {{ $.each(list, function(i, item){ }}
            <tr class="J-tr-item">
                <td>{{=item.skuNo}}</td>
                <td>
                    <div class="line-clamp2">
                        <a tabtitle={"num":"vgoodsviewsku{{=item.skuId}}","link":"/goods/vgoods/viewSku.do?skuId={{=item.skuId}}&spuId={{=item.spuId}}&pageType=0","title":"查看SKU"} href="javascript:void(0);" class="">{{=item.showName}}</a>
                    </div>
                </td>
                <td>{{=item.spec || '--'}}</td>
                <td>{{=item.model || '--'}}</td>
                <td>
                     {{ if(item.checkStatus==2){ }}
                        <span class="status-red">审核不通过</span>
                    {{ }else if(item.checkStatus==1){ }}
                        <span class="status-yellow">审核中</span>
                    {{ }else if(item.checkStatus==3){ }}
                        <span class="status-green">审核通过</span>
                    {{ }else if(item.checkStatus==0){ }}
                    <span class="status-yellow">待完善</span>
                    {{ }else if(item.checkStatus==5){ }}
                    <span class="status-yellow">待提交审核</span>
                    {{ } }}
                </td>
                <td >
                    {{ if(item.hasTodoItemsCount > 0){ }}
                      <img  src="${pageContext.request.contextPath}/static/images/alarm.png" width="25px" class="option-select-item J-view-sku-todoList" data-id="{{=item.skuId}}">
                    {{ } else { }}
                        <img src="${pageContext.request.contextPath}/static/images/tick.png" width="25px">
                    {{ } }}
                </td>
                <td>
                        <div class="option-select-wrap J-option-select-wrap">
                            <c:if test="${showType ne 'op'}" >
                                {{ if(spulv != 2){ }}
                                <div class="option-select-btn" tabtitle={"num":"vgoodseditsku{{=item.skuId}}","link":"/goods/vgoods/addSku.do?skuId={{=item.skuId}}&spuId={{=item.spuId}}","title":"编辑SKU"}>编辑</div>
                                {{ } }}
                                {{ if(spulv == 2){ }}
                                <div class="option-select-btn J-sku-edit" data-type="${command.skuType}" data-spuid="{{=item.spuId}}" data-spuname="{{=spuName}}" data-skuid="{{=item.skuId}}">编辑</div>
                                {{ } }}
                                <div class="option-select-icon J-option-select-icon">
                                    <i class="vd-icon icon-down"></i>
                                </div>
                                <div class="option-select-list">
                                    <c:if test="${command.spuLevel!=2 && command.hasEditAuth  }" >
                                    {{ if(item.goodsLevelNo !=5){ }}
                                    <div class="option-select-item" tabtitle={"num":"vgoodscopysku{{=item.skuId}}","link":"/goods/vgoods/copySku.do?skuId={{=item.skuId}}&spuId={{=item.spuId}}","title":"复制SKU"}>复制SKU </div>
                                    {{ } }}
                                    </c:if>
                                    <div class="option-select-item J-del-sku" data-spuid="{{=item.spuId}}" data-id="{{=item.skuId}}">删除</div>
                                </div>
                            </c:if>
                        </div>
                </td>
            </tr>
        {{ }) }}
    </script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/pager.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/pages/goods/vgoods/spu_view.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../../common/footer.jsp"%>