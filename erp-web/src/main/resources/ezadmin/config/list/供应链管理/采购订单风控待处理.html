<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>采购订单风控待处理</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="bdJlVSQ2Zfk" datasource="erp-reportdatasource" fixednumber="2" fixednumberright="0" append_column_url="" append_row_url="" empty_show="" firstcol="">
  <div id="appendHead"></div>
  <div class="layui-fluid">
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
     <li item_name=""><a href="" item_name="">待处理</a></li>
     <li item_name="已处理"><a href="/ezadmin/list/list-bdJlVSQ2Zfk2" item_name="已处理">已处理</a></li>
    </ul>
   </div>
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">订单号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="RISK_CHECK_BUZ_EXTRA" placeholder="" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">供应商名称</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">创建人</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="USERNAME" placeholder="" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">被风控时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="17" name="ADD_TIME" placeholder="" style="" alias="a" jdbctype="DATE" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">被风控备注</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="20" name="COMMENT1" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;客户资质未审核通过&quot;,&quot;K&quot;:&quot;客户资质未审核通过&quot;},{&quot;V&quot;:&quot;商品信息不完整&quot;,&quot;K&quot;:&quot;商品信息不完整&quot;},{&quot;V&quot;:&quot;客户信息不完整&quot;,&quot;K&quot;:&quot;客户信息不完整&quot;},{&quot;V&quot;:&quot;供应商信息不完整&quot;,&quot;K&quot;:&quot;供应商信息不完整&quot;}]" datatype="JSON" oper="" validate_rules="" validate_messages=""></object>
     </div>
    </div>
   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton">
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="RISK_CHECK_BUZ_EXTRA" name="RISK_CHECK_BUZ_EXTRA" body="td-link" order="0" url="/order/buyorder/viewBuyorder.do?buyorderId=${BUYORDER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单号</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" order="0" url="/trader/supplier/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">供应商名称</th>
      <th item_name="USERNAME" name="USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">创建人</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">被风控时间</th>
      <th item_name="COMMENT1" name="COMMENT1" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">被风控备注</th>
      <th item_name="OPERATION" name="OPERATION" body="td-link" order="0" url="/order/buyorder/viewBuyorder.do?buyorderId=${BUYORDER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">操作</th>
     </tr>
    </thead>
    <tbody>
     <tr>
      <td> express:</td>
      <td colspan="100"><pre id="express" class="layui-code" orderby="" groupby="">select
a.RISK_CHECK_BUZ_EXTRA,

a.RISK_CHECK_BUZ_EXTRA BUZ_EXTRA2,
	a.BUYORDER_ID,
	a.TRADER_ID,
	a.TRADER_NAME,
	a.CREATOR,
	a.USERNAME,
	a.ADD_TIME,
	a.COMMENTS,
	a.COMMENT1,
	a.OPERATION
from (
SELECT
	L.RISK_CHECK_BUZ_EXTRA,
	L.RISK_CHECK_BUZ_EXTRA BUZ_EXTRA2,
	B.BUYORDER_ID,
	T.TRADER_ID,
	T.TRADER_NAME,
	B.CREATOR,
	U.USERNAME,
	FROM_UNIXTIME( L.ADD_TIME / 1000,'%Y-%m-%d %H:%i:%s' ) ADD_TIME,
	GROUP_CONCAT( DISTINCT CASE L.RISK_CHECK_BUZ_TYPE WHEN 4 THEN '客户资质未审核通过' WHEN 5 THEN '客户信息不完整' WHEN 6 THEN '供应商信息不完整' WHEN 7 THEN '商品信息不完整' END ) COMMENTS,
	GROUP_CONCAT( DISTINCT CASE L.RISK_CHECK_BUZ_TYPE WHEN 4 THEN '客户资质未审核通过' WHEN 5 THEN '客户信息不完整' WHEN 6 THEN '供应商信息不完整' WHEN 7 THEN '商品信息不完整' END ) COMMENT1,
	'解除风控' OPERATION
FROM
	T_RISK_CHECK_LOG L
	LEFT JOIN T_BUYORDER B ON L.RISK_CHECK_BUZ_EXTRA = B.BUYORDER_NO
	AND L.RISK_CHECK_BUZ_PROPERTY = 'buyorder'
	LEFT JOIN T_TRADER T ON B.TRADER_ID = T.TRADER_ID
	JOIN T_USER U ON B.CREATOR = U.USER_ID
GROUP BY
	L.RISK_CHECK_BUZ_EXTRA
HAVING
	SUM( RISK_CHECK_TRIGGER_STATUS ) &lt; COUNT( * )
	) a
where 1=1</pre></td>
     </tr>
     <tr>
      <td> count:</td>
      <td colspan="100"><pre id="count" class="layui-code"></pre></td>
     </tr>
    </tbody>
   </table>
  </div>
  <div id="appendFoot"></div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
 </body>
</html>