package com.vedeng.todolist.service.impl;

import com.vedeng.common.util.DateUtil;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.model.TodoList;
import com.vedeng.todolist.service.ITodoInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: daniel
 * @Date: 2020/12/11 10 42
 * @Description:
 */
@Service
public class MaintainDataSpu implements ITodoInstance {

    private static final Logger logger = LoggerFactory.getLogger(MaintainDataSpu.class);

    @Resource
    private TodoListMapper todoListMapper;

    @Override
    public synchronized void add(Integer buzId, String buzExtra, String comment, String buzProperty) {
        logger.info("待办事项---分级分档维护SPU，buzId:{}，buzExtra：{}生成待办事项",buzId,buzExtra);
        if (todoListMapper.selectUnHandledByBuzTypeAndBuzId(TodoListBuzSceneEnum.MAINTAIN_DATA_SPU.getBuzSceneId(), buzId).size() > 0) {
            todoListMapper.deleteByBuzTypeAndBuzId(TodoListBuzSceneEnum.MAINTAIN_DATA_SPU.getBuzSceneId(),buzId);
        }
        TodoList instance = new TodoList(TodoListBuzSceneEnum.MAINTAIN_DATA_SPU,buzId,buzExtra,buzProperty,comment, DateUtil.sysTimeMillis(),2);
        todoListMapper.insertSelective(instance);
    }

    @Override
    public void finish(Integer buzId) {
        todoListMapper.updateStatusByBuzTypeAndBuzId(TodoListBuzSceneEnum.MAINTAIN_DATA_SPU.getBuzSceneId(),buzId,1,
                                                    getCurrentRequestUser().getUserId(),
                                                    System.currentTimeMillis());
        logger.info("待办事项---分级分档维护SPU，buzId;{}更新为已完成",buzId);
    }
}
