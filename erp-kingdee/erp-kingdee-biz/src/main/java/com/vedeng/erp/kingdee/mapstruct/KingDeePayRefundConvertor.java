package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayRefundBillEntity;
import com.vedeng.erp.kingdee.dto.KingDeePayRefundBillDto;
import com.vedeng.erp.kingdee.dto.KingDeePayRefundEntryDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.kingdee.mapstruct
 * @Date 2023/8/24 15:44
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeePayRefundConvertor extends BaseMapStruct<KingDeePayRefundBillEntity, KingDeePayRefundBillDto> {

    @Override
    @Mapping(target = "FRefundBillEntry", source = "frefundbillentry", qualifiedByName = "payRefundToJsonArray")
    KingDeePayRefundBillEntity toEntity(KingDeePayRefundBillDto dto);

    @Override
    @Mapping(target = "frefundbillentry", source = "FRefundBillEntry", qualifiedByName = "payRefundJsonArrayToList")
    KingDeePayRefundBillDto toDto(KingDeePayRefundBillEntity entity);

    @Named("payRefundJsonArrayToList")
    default List<KingDeePayRefundEntryDto> payRefundJsonArrayToEntryList(JSONArray payRefundEntryDto) {
        if (CollUtil.isEmpty(payRefundEntryDto)) {
            return Collections.emptyList();
        }
        return payRefundEntryDto.toJavaList(KingDeePayRefundEntryDto.class);
    }

    @Named("payRefundToJsonArray")
    default JSONArray payRefundListToJsonArray(List<KingDeePayRefundEntryDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
}
