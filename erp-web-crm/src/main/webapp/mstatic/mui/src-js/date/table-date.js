const getDateTimestamp = function(time) {
    if (typeof time === 'number' || typeof time === 'string') {
        return util_date.clearTime(new Date(time)).getTime();
    } else if (time instanceof Date) {
        return util_date.clearTime(time).getTime();
    } else {
        return NaN;
    }
};

// 日期表格
Vue.component('ui-date-table', {
    template: `
        <table
            cellspacing="0"
            cellpadding="0"
            class="vd-ui-date-table "
            @click="handleClick"
            @mousemove="handleMouseMove"
            :class="{'is-week-mode': selectionMode === 'week'}"
        >
            <tbody>
                <tr>
                    <th v-for="(week, key) in WEEKS" :key="key">{{ WEEKS_SHOW[week] }}</th>
                </tr>
                <tr
                    class="vd-ui-date-table__row"
                    v-for="(row, key) in rows"
                    :key="key">
                    <td
                        v-for="(cell, key) in row"
                        :class="getCellClasses(cell)"
                        :key="key">
                        <div>
                            <span>{{ cell.text }}</span>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    `,
    props: {
        firstDayOfWeek: {
            type: Number,
            default: 1,
            validator: val => val >= 1 && val <= 7
        },
        value: {},
        defaultValue: {
            validator(val) {
                return val === null || util_date.isDate(val) || (Array.isArray(val) && val.every(util_date.isDate));
            }
        },
        date: {},
        // week  dates
        selectionMode: {
            default: 'day'
        },
        disabledDate: {},
        cellClassName: {},
        minDate: {},
        maxDate: {},
        // 范围
        rangeState: {
            default () {
                return {
                    endDate: null,
                    selecting: false
                };
            }
        },
        min: '',
        max: '',
    },
    data () {
        return {
            WEEKS_: ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'],
            WEEKS_SHOW: {
                sun: '日',
                mon: '一',
                tue: '二',
                wed: '三',
                thu: '四',
                fri: '五',
                sat: '六',
            },
            tableRows: [ [], [], [], [], [], [] ],
            lastRow: null,
            lastColumn: null,
        }
    },
    watch: {
        'rangeState.endDate'(newVal) {
            this.markRange(this.minDate, newVal);
        },
        minDate(newVal, oldVal) {
            if (getDateTimestamp(newVal) !== getDateTimestamp(oldVal)) {
                this.markRange(this.minDate, this.maxDate);
            }
        },

        maxDate(newVal, oldVal) {
            if (getDateTimestamp(newVal) !== getDateTimestamp(oldVal)) {
                this.markRange(this.minDate, this.maxDate);
            }
        }
    },
    mounted() {
        console.log(this.maxDate)
    },
    computed: {
        offsetDay () {
            const week = this.firstDayOfWeek;
            // 周日为界限，左右偏移的天数，3217654 例如周一就是 -1，目的是调整前两行日期的位置
            return week > 3 ? 7 - week : -week;
        },
        WEEKS () {
            const week = this.firstDayOfWeek;
            return this.WEEKS_.concat(this.WEEKS_).slice(week, week + 7);
        },
        year () {
            return this.date.getFullYear();
        },
        month () {
            return this.date.getMonth();
        },

        startDate () {
            return util_date.getStartDateOfMonth(this.year, this.month);
        },

        // 面板日期数据
        rows () {
            try {
                const date = new Date(this.year, this.month, 1);
                let day = util_date.getFirstDayOfMonth(date); // day of first day   获取每月第一天
                const dateCountOfMonth = util_date.getDayCountOfMonth(date.getFullYear(), date.getMonth());   // 当月有几天
                const dateCountOfLastMonth = util_date.getDayCountOfMonth(date.getFullYear(), (date.getMonth() === 0 ? 11 : date.getMonth() - 1));  // 上个月有多少天

                day = (day === 0 ? 7 : day);
                const offset = this.offsetDay;
                const rows = this.tableRows;
                let count = 1;

                const startDate = this.startDate;
                const disabledDate = this.disabledDate;
                const cellClassName = this.cellClassName;
                const selectedDate = [];
                const now = getDateTimestamp(new Date());

                for (let i = 0; i < 6; i++) {
                    const row = rows[i];

                    for (let j = 0; j < 7; j++) {
                        let cell = row[j];
                        if (!cell) {
                            cell = { row: i, column: j, type: 'normal', inRange: false, start: false, end: false };
                        }

                        cell.type = 'normal';

                        const index = i * 7 + j;

                        const time = util_date.nextDate(startDate, index - offset).getTime();

                        cell.inRange = time >= getDateTimestamp(this.minDate) && time <= getDateTimestamp(this.maxDate);
                        cell.start = this.minDate && time === getDateTimestamp(this.minDate);
                        cell.end = this.maxDate && time === getDateTimestamp(this.maxDate);

                        const isToday = time === now;

                        if (isToday) {
                            cell.type = 'today';
                        }

                        if (i >= 0 && i <= 1) {
                            const numberOfDaysFromPreviousMonth = day + offset < 0 ? 7 + day + offset : day + offset;

                            if (j + i * 7 >= numberOfDaysFromPreviousMonth) {
                                cell.text = count++;
                            } else {
                                cell.text = dateCountOfLastMonth - (numberOfDaysFromPreviousMonth - j % 7) + 1 + i * 7;
                                cell.type = 'prev-month';
                            }
                        } else {
                            if (count <= dateCountOfMonth) {
                                cell.text = count++;
                            } else {
                                cell.text = count++ - dateCountOfMonth;
                                cell.type = 'next-month';
                            }
                        }

                        let cellDate = new Date(time);
                        cell.disabled = typeof disabledDate === 'function' && disabledDate(cellDate);

                        if((this.min && time < getDateTimestamp(this.min)) || (this.max && time > getDateTimestamp(this.max))) {
                            cell.disabled = true;
                        }

                        cell.selected = util_date.arrayFind(selectedDate, date => date.getTime() === cellDate.getTime());
                        cell.customClass = typeof cellClassName === 'function' && cellClassName(cellDate);
                        this.$set(row, j, cell);
                    }
                }
                return rows;
            } catch (err) {
                console.log('table数据 rowsErr', err);
            }
        }
    },
    methods: {
        cellMatchesDate(cell, date) {
            const value = new Date(date);
            return (this.year === value.getFullYear() && this.month === value.getMonth() && Number(cell.text) === value.getDate());
        },

        getCellClasses (cell) {
            const selectionMode = this.selectionMode;
            const defaultValue = this.defaultValue ? Array.isArray(this.defaultValue) ? this.defaultValue : [this.defaultValue] : [];

            let classes = [];
            if ((cell.type === 'normal' || cell.type === 'today') && !cell.disabled) {
                classes.push('available');
                if (cell.type === 'today') {
                    classes.push('today');
                }
            } else {
                classes.push(cell.type);
            }

            if (cell.type === 'normal' && defaultValue.some(date => this.cellMatchesDate(cell, date))) {
                classes.push('default');
            }

            if (selectionMode === 'day' && (cell.type === 'normal' || cell.type === 'today') && this.cellMatchesDate(cell, this.value)) {
                classes.push('current');
            }

            if (cell.inRange && ((cell.type === 'normal' || cell.type === 'today') || this.selectionMode === 'week')) {
                classes.push('in-range');

                if (cell.start) {
                    classes.push('start-date');
                }

                if (cell.end) {
                    classes.push('end-date');
                }
            }

            if (cell.disabled) {
                classes.push('disabled');
            }

            if (cell.selected) {
                classes.push('selected');
            }

            if (cell.customClass) {
                classes.push(cell.customClass);
            }

            return classes.join(' ');
        },

        getDateOfCell (row, column) {
            const offsetFromStart = row * 7 + (column - (this.showWeekNumber ? 1 : 0)) - this.offsetDay;
            return util_date.nextDate(this.startDate, offsetFromStart);
        },

        markRange (minDate, maxDate) {
            minDate = getDateTimestamp(minDate);
            maxDate = getDateTimestamp(maxDate) || minDate;
            [minDate, maxDate] = [Math.min(minDate, maxDate), Math.max(minDate, maxDate)];

            const startDate = this.startDate;
            const rows = this.rows;
            for (let i = 0, k = rows.length; i < k; i++) {
                const row = rows[i];
                for (let j = 0, l = row.length; j < l; j++) {
                    if (this.showWeekNumber && j === 0) continue;

                    const cell = row[j];
                    const index = i * 7 + j + (this.showWeekNumber ? -1 : 0);
                    const time = util_date.nextDate(startDate, index - this.offsetDay).getTime();

                    cell.inRange = minDate && time >= minDate && time <= maxDate;
                    cell.start = minDate && time === minDate;
                    cell.end = maxDate && time === maxDate;
                }
            }
        },

        // 鼠标移动，针对范围选择
        handleMouseMove (event) {
            if (!this.rangeState.selecting) return;   // 只针对range范围类型

            let target = event.target;
            if (target.tagName === 'SPAN') {
                target = target.parentNode.parentNode;
            }
            if (target.tagName === 'DIV') {
                target = target.parentNode;
            }
            if (target.tagName !== 'TD') return;

            const row = target.parentNode.rowIndex - 1;
            const column = target.cellIndex;

            // can not select disabled date
            if (this.rows[row][column].disabled) return;

            // only update rangeState when mouse moves to a new cell
            // this avoids frequent Date object creation and improves performance
            if (row !== this.lastRow || column !== this.lastColumn) {
                this.lastRow = row;
                this.lastColumn = column;
                this.$emit('changerange', {
                    minDate: this.minDate,
                    maxDate: this.maxDate,
                    rangeState: {
                        selecting: true,
                        endDate: this.getDateOfCell(row, column)
                    }
                });
            }
        },

        // 点击每个日期
        handleClick (event) {
            try {
                let target = event.target;
                if (target.tagName === 'SPAN') {
                    target = target.parentNode.parentNode;
                }
                if (target.tagName === 'DIV') {
                    target = target.parentNode;
                }

                if (target.tagName !== 'TD') return;   // 如果点击的不是每一个小格子 td，不处理

                const row = target.parentNode.rowIndex - 1;
                const column = this.selectionMode === 'week' ? 1 : target.cellIndex;
                const cell = this.rows[row][column];

                if (cell.disabled || cell.type === 'week') return;

                const newDate = this.getDateOfCell(row, column);

                if (this.selectionMode === 'range') {
                    // 范围选择点击
                    if (!this.rangeState.selecting) {
                        this.$emit('pick', { minDate: newDate, maxDate: null });
                        this.rangeState.selecting = true;
                    } else {
                        if (newDate >= this.minDate) {
                            this.$emit('pick', { minDate: this.minDate, maxDate: newDate });
                        } else {
                            this.$emit('pick', { minDate: newDate, maxDate: this.minDate });
                        }
                        this.rangeState.selecting = false;
                    }
                } else if (this.selectionMode === 'day') {
                    this.$emit('pick', newDate);
                }
            } catch (err) {
                console.log('表格内数据点击 handleClick ', err);
            }
        },
    }
})