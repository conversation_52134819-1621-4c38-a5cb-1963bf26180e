package com.vedeng.api.standard.validation.rules;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.api.standard.adapter.peerlist.dto.PeerListCreateRequest;
import com.vedeng.api.standard.adapter.peerlist.dto.PeerListDetailRequest;
import com.vedeng.api.standard.validation.ValidationContextKeys;
import com.vedeng.api.standard.validation.ValidationResult;
import com.vedeng.api.standard.validation.ValidationRule;
import com.vedeng.erp.buyorder.domain.vo.PurchaseDeliveryDirectBatchInfoVo;
import com.vedeng.erp.buyorder.dto.BuyOrderApiDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.logistics.dao.ExpressDetailMapper;
import com.vedeng.logistics.model.SyncExpressDetailDto;
import com.vedeng.logistics.service.ExpressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class AddPeerListEnableRule implements ValidationRule<PeerListCreateRequest> {
    
    @Autowired
    private ExpressService expressService;
    @Autowired
    private BuyorderApiService buyorderApiService;
    
    @Autowired
    private ExpressDetailMapper expressDetailMapper;
    @Override
    public ValidationResult validate(PeerListCreateRequest request, Map<String, Object> context) {
        List<PeerListDetailRequest> detailList = request.getList();
        // 根据采购订单号获取销售订单ID
        BuyOrderApiDto dto = buyorderApiService.getBuyorderByBuyorderNo(request.getBuyOrderNo());
        log.info("采购订单ID：{}",dto.getBuyorderId());
        log.info("销售订单明细：{}", JSON.toJSON(detailList));
        // 采购
        // 查询出所有快递明细
        for (PeerListDetailRequest peerListDetailRequest : detailList) {
            // 查询expressDetailId
            SyncExpressDetailDto detailDto = expressService.getDetail(dto.getBuyorderId(),peerListDetailRequest.getLogisticsNo(),peerListDetailRequest.getSku());
            peerListDetailRequest.setExpressDetailId(detailDto.getExpressDetailId());
            peerListDetailRequest.setOldNum(detailDto.getNum());
        }

        PurchaseDeliveryDirectBatchInfoVo result = BeanUtil.toBean(request, PurchaseDeliveryDirectBatchInfoVo.class);
        result.setBuyorderId(dto.getBuyorderId());
        context.put(ValidationContextKeys.PURCHASE_DELIVERY_DIRECT_BATCH_INFO_VO, result);

        return ValidationResult.success();
    }
}
