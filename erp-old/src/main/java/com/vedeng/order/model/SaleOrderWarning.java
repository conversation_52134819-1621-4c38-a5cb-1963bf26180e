package com.vedeng.order.model;

import lombok.Data;

/**
 * @Description:  待采购订单预警历史记录表
 * @Author:       davis
 * @Date:         2021/4/20 下午2:56
 * @Version:      1.0
 */
@Data
public class SaleOrderWarning {

    /**
     * 主键ID
     */
    private Integer warningId;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 订单ID
     */
    private Integer saleorderId;

    /**
     * 订单明细ID
     */
    private Integer saleorderGoodsId;

    /**
     * 时效状态
     */
    private Integer aging;

    /**
     * 预警时间
     */
    private Long warnTime;

    /**
     * 预警等级
     */
    private Integer warnLevel;

    /**
     * 逾期次数
     */
    private Integer overDue;

    /**
     * 是否删除
     */
    private Integer isDelete;

}
