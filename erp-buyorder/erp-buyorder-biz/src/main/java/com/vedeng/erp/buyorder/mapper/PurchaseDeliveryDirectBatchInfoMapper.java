package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchInfo;
import com.vedeng.erp.buyorder.domain.entity.TPurchaseDeliveryDirectBatchInfoExample;
import java.util.List;

import com.vedeng.erp.buyorder.domain.vo.PurchaseDeliveryDirectBatchInfoVo;
import com.vedeng.erp.buyorder.dto.PurchaseDeliveryBatchInfoDto;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

public interface PurchaseDeliveryDirectBatchInfoMapper {
    long countByExample(TPurchaseDeliveryDirectBatchInfoExample example);

    int deleteByExample(TPurchaseDeliveryDirectBatchInfoExample example);

    int deleteByPrimaryKey(Integer purchaseDeliveryDirectBatchInfoId);

    int insert(PurchaseDeliveryDirectBatchInfo record);

    int insertSelective(PurchaseDeliveryDirectBatchInfo record);

    List<PurchaseDeliveryDirectBatchInfo> selectByExample(TPurchaseDeliveryDirectBatchInfoExample example);

    PurchaseDeliveryDirectBatchInfo selectByPrimaryKey(Integer purchaseDeliveryDirectBatchInfoId);

    int updateByExampleSelective(@Param("record") PurchaseDeliveryDirectBatchInfo record, @Param("example") TPurchaseDeliveryDirectBatchInfoExample example);

    int updateByExample(@Param("record") PurchaseDeliveryDirectBatchInfo record, @Param("example") TPurchaseDeliveryDirectBatchInfoExample example);

    int updateByPrimaryKeySelective(PurchaseDeliveryDirectBatchInfo record);

    int updateByPrimaryKey(PurchaseDeliveryDirectBatchInfo record);

    /**
     * <AUTHOR>
     * @desc 根据采购单id查询关联的同行单信息
     * @param buyorderId
     * @return
     */
    List<PurchaseDeliveryBatchInfoDto> queryPurchaseDeliveryInfoByBuyorderId(Integer buyorderId);
}