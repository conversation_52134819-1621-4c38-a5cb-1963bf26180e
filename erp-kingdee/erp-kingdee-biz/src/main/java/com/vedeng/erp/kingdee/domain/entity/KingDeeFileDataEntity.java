package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * KING_DEE_FILE_DATA
 *
 * <AUTHOR>
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_FILE_DATA")
public class KingDeeFileDataEntity extends BaseEntity {
    /**
     * id
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 金蝶业务表单
     */
    private String formId;

    /**
     * 金蝶fid 如 对应入库的那金蝶id
     */
    private String fId;

    /**
     * erp单据id 如 对应入库单id
     */
    private String erpId;

    /**
     * 附件url 全路径
     */
    private String url;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 创建者id
     */
    private Integer creator;

    /**
     * 修改者id
     */
    private Integer updater;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 修改者名称
     */
    private String updaterName;

    private static final long serialVersionUID = 1L;
}