package com.vedeng.kpi.model.VO;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

public class KpiDailyCountVo {

    private static final long serialVersionUID = 5007642176650957740L;


    /**
     * 销售ID
     */
    private Integer userId;

    /**
     * 公司ID
     */
    private Integer companyId;

    /**
     * 小组ID
     */
    private Integer teamId;

    /**
     * 团队(部门)ID
     */
    private Integer groupId;

    /**
     * 本月累计业绩金额
     */
    private BigDecimal kpiAmount;

    /**
     * 本月累计业绩完成度
     */
    private BigDecimal kpiAmountProgress;

    /**
     * 业绩得分
     */
    private BigDecimal kpiAmountScore;

    /**
     * 已合作客户数量
     */
    private Integer coCustomerNum;

    /**
     * 已流失客户数量
     */
    private Integer lostCustomerNum;

    /**
     * 客户数量得分
     */
    private BigDecimal customerScore;

    /**
     * 新增BD客户
     */
    private Integer bdNewCustomerNum;

    /**
     * 流失BD客户
     */
    private Integer bdLostCustomerNum;

    /**
     * BD客户数量得分
     */
    private BigDecimal bdCustomerScore;

    /**
     * 新成交询价成功数量
     */
    private Integer chanceSuccessNum;

    /**
     * 失败询价数量
     */
    private Integer chanceFailNum;

    /**
     * 商机转化率
     */
    private BigDecimal chanceTransProportion;

    private BigDecimal chanceScore;

    private Integer amountSort;

    private Integer customerSort;

    private Integer bdCustomerSort;

    private Integer chanceSort;

    private BigDecimal totalScore;

    private Integer totalSort;

    private BigDecimal amountTarget;

    private String userName;

    private Integer yesterdaySort;

    private Integer lastmonthSort;

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Integer getTeamId() {
        return teamId;
    }

    public void setTeamId(Integer teamId) {
        this.teamId = teamId;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public BigDecimal getKpiAmount() {
        return kpiAmount;
    }

    public void setKpiAmount(BigDecimal kpiAmount) {
        this.kpiAmount = kpiAmount;
    }

    public BigDecimal getKpiAmountProgress() {
        return kpiAmountProgress;
    }

    public void setKpiAmountProgress(BigDecimal kpiAmountProgress) {
        this.kpiAmountProgress = kpiAmountProgress;
    }

    public BigDecimal getKpiAmountScore() {
        return kpiAmountScore;
    }

    public void setKpiAmountScore(BigDecimal kpiAmountScore) {
        this.kpiAmountScore = kpiAmountScore;
    }

    public Integer getCoCustomerNum() {
        return coCustomerNum;
    }

    public void setCoCustomerNum(Integer coCustomerNum) {
        this.coCustomerNum = coCustomerNum;
    }

    public Integer getLostCustomerNum() {
        return lostCustomerNum;
    }

    public void setLostCustomerNum(Integer lostCustomerNum) {
        this.lostCustomerNum = lostCustomerNum;
    }

    public BigDecimal getCustomerScore() {
        return customerScore;
    }

    public void setCustomerScore(BigDecimal customerScore) {
        this.customerScore = customerScore;
    }

    public Integer getBdNewCustomerNum() {
        return bdNewCustomerNum;
    }

    public void setBdNewCustomerNum(Integer bdNewCustomerNum) {
        this.bdNewCustomerNum = bdNewCustomerNum;
    }

    public Integer getBdLostCustomerNum() {
        return bdLostCustomerNum;
    }

    public void setBdLostCustomerNum(Integer bdLostCustomerNum) {
        this.bdLostCustomerNum = bdLostCustomerNum;
    }

    public BigDecimal getBdCustomerScore() {
        return bdCustomerScore;
    }

    public void setBdCustomerScore(BigDecimal bdCustomerScore) {
        this.bdCustomerScore = bdCustomerScore;
    }

    public Integer getChanceSuccessNum() {
        return chanceSuccessNum;
    }

    public void setChanceSuccessNum(Integer chanceSuccessNum) {
        this.chanceSuccessNum = chanceSuccessNum;
    }

    public Integer getChanceFailNum() {
        return chanceFailNum;
    }

    public void setChanceFailNum(Integer chanceFailNum) {
        this.chanceFailNum = chanceFailNum;
    }

    public BigDecimal getChanceTransProportion() {
        return chanceTransProportion;
    }

    public void setChanceTransProportion(BigDecimal chanceTransProportion) {
        this.chanceTransProportion = chanceTransProportion;
    }

    public BigDecimal getChanceScore() {
        return chanceScore;
    }

    public void setChanceScore(BigDecimal chanceScore) {
        this.chanceScore = chanceScore;
    }

    public Integer getAmountSort() {
        return amountSort;
    }

    public void setAmountSort(Integer amountSort) {
        this.amountSort = amountSort;
    }

    public Integer getCustomerSort() {
        return customerSort;
    }

    public void setCustomerSort(Integer customerSort) {
        this.customerSort = customerSort;
    }

    public Integer getBdCustomerSort() {
        return bdCustomerSort;
    }

    public void setBdCustomerSort(Integer bdCustomerSort) {
        this.bdCustomerSort = bdCustomerSort;
    }

    public Integer getChanceSort() {
        return chanceSort;
    }

    public void setChanceSort(Integer chanceSort) {
        this.chanceSort = chanceSort;
    }

    public BigDecimal getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(BigDecimal totalScore) {
        this.totalScore = totalScore;
    }

    public Integer getTotalSort() {
        return totalSort;
    }

    public void setTotalSort(Integer totalSort) {
        this.totalSort = totalSort;
    }

    public BigDecimal getAmountTarget() {
        return amountTarget;
    }

    public void setAmountTarget(BigDecimal amountTarget) {
        this.amountTarget = amountTarget;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getYesterdaySort() {
        return yesterdaySort;
    }

    public void setYesterdaySort(Integer yesterdaySort) {
        this.yesterdaySort = yesterdaySort;
    }

    public Integer getLastmonthSort() {
        return lastmonthSort;
    }

    public void setLastmonthSort(Integer lastmonthSort) {
        this.lastmonthSort = lastmonthSort;
    }
}
