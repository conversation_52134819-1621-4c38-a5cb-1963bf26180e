package com.vedeng.crm.web.api;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.redis.utils.RedisUtil;

import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.infrastructure.wxapp.utils.WxHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 企微
 * @date 2024/7/12 14:54
 */
@ExceptionController
@RestController
@RequestMapping("/crm/wx")
@Slf4j
public class WxApi {

    public static final String HTTP = "http://";
    public static final String HTTPS = "https://";
    @Value("${cas.client.serviceUrl}")
    private String casClientServiceUrl;
    @Value("${cas.client.prefixUrl}")
    protected String casClientPrefixUrl;

    /**
     * 获取企业微信JS-SDK使用权限签名
     *
     * @param url url
     * @return R
     * @throws NoSuchAlgorithmException
     */
    @RequestMapping(value = "/getWeiXinPermissionsValidationConfig")
    @NoNeedAccessAuthorization
    public R<?> getWeiXinPermissionsValidationConfig(String url) throws NoSuchAlgorithmException {
        // 获取jsapi_ticket
        String ticket = WxHelper.getJsApiTicket("");
        long timestamp = DateUtil.currentSeconds();
        //随机字符串
        String nonceStr = RandomUtil.randomString(16);
        // 获取JS-SDK使用权限签名
        String signature = WxHelper.getJSSDKSignature(ticket, nonceStr, timestamp, url);
        casClientServiceUrl = removeHttpPrefix(casClientServiceUrl);
        casClientPrefixUrl = removeHttpPrefix(casClientPrefixUrl);
        WxHelper.WxConfig wxConfig = new WxHelper.WxConfig(WxHelper.APP_ID, WxHelper.CRM_PC_AGENT_ID, timestamp, nonceStr, signature, casClientPrefixUrl, casClientServiceUrl);
        return R.success(wxConfig);
    }

    @RequestMapping(value = "/getWeiXinPermissionsValidationConfigForAgent")
    @NoNeedAccessAuthorization
    public R<?> getWeiXinPermissionsValidationConfigForAgent(String url) throws NoSuchAlgorithmException {
        // 获取jsapi_ticket
        String ticket = WxHelper.getJsApiTicket("agent_config");
        long timestamp = DateUtil.currentSeconds();
        //随机字符串
        String nonceStr = RandomUtil.randomString(16);
        // 获取JS-SDK使用权限签名
        String signature = WxHelper.getJSSDKSignature(ticket, nonceStr, timestamp, url);
        casClientServiceUrl = removeHttpPrefix(casClientServiceUrl);
        casClientPrefixUrl = removeHttpPrefix(casClientPrefixUrl);
        WxHelper.WxConfig wxConfig = new WxHelper.WxConfig(WxHelper.APP_ID, WxHelper.CRM_PC_AGENT_ID, timestamp, nonceStr, signature, casClientPrefixUrl, casClientServiceUrl);
        return R.success(wxConfig);
    }

    public static String removeHttpPrefix(String url) {
        if (url.startsWith("http://") || url.startsWith("https://")) {
            url = url.replaceFirst("http://|https://", "");
        }
        if (url.endsWith("/")) {
            url = url.substring(0, url.length() - 1);
        }
        return url;
    }

    /**
     * 企微OAuth2 登录
     * @param code  前端组装OAuth2链接，由用户点击（或者静默授权，无需用户点击）同意后，微信端返回code码，前端将code交由后端进行换取用户信息
     * @param request
     * @return
     */
    @RequestMapping("/getUserIdByCode")
    public ModelAndView getUserIdByCode(@RequestParam("code") String code, HttpServletRequest request, HttpServletResponse response) {
        log.info("获取入参code：{}",code);
        try {
            String jobNumber = "";
            if("Y".equals(mock)){
                jobNumber = code;
            }else{
                jobNumber = WxHelper.getUserInfo(code);
            }
            //获取用户信息
            UserDto userDto = userApiService.getUserBaseInfoByJobNumber(jobNumber);
//            if(userDto == null){
//                return R.error("未获取到工号为"+jobNumber+"的员工信息，请联系"+noPowerContract);
//            }
            HttpSession session = request.getSession();
            // 使用新的当前登录用户对象,初始化当前登录用户信息
            CurrentUser currentUser = CurrentUser.builder().id(userDto.getUserId())
                    .number(userDto.getNumber())
                    .headPic(userDto.getAliasHeadPicture())
                    .actualName(userDto.getRealName())
                    .username(userDto.getUsername())
                    .isAdmin(userDto.getIsAdmin())
                    .companyId(userDto.getCompanyId())
                    .positType(userDto.getPositType())
                    .isDisabled(userDto.getIsDisabled())
                    .positionTypes(userDto.getPositionTypes())
                    .orgId(userDto.getOrgId())
                    .orgName(userDto.getOrgName())
                    .parentId(userDto.getParentId())
                    .build();
            session.setAttribute(ErpConstant.CURRENT_USER, currentUser);

            String lxcrmLoginToken = UUID.randomUUID().toString();//生成一个登录认证的token


            //写入一个loginToken到cookie中
            RedisUtil.StringOps.set(dbType+"lxcrm-login:"+lxcrmLoginToken, JSON.toJSONString(currentUser));
            RedisUtil.KeyOps.expire(dbType+"lxcrm-login:"+lxcrmLoginToken,30*24*3600, TimeUnit.SECONDS);//30天*24小时*3600秒*1000毫秒

            //将loginToken写入到Cookie返回前端
            Cookie cookie = new Cookie("lxcrmLoginToken", lxcrmLoginToken);
            cookie.setPath("/");
            cookie.setHttpOnly(true);
//            cookie.setSecure(true);
            cookie.setMaxAge(360*24*3600*1000);
            response.addCookie(cookie);
            log.info("获取到的用户信息：{}",JSON.toJSONString(currentUser));
            return new ModelAndView("redirect:/crm/visitRecord/m/index");
//            return R.success(currentUser);
        }catch(Exception e) {
            log.error("登录异常",e);
            return new ModelAndView("redirect:/error");
//            return R.error("登录异常，请重试；如果问题仍然无法解决，请联系"+noPowerContract);
        }
    }


    /**
     * 获取当前登录用户的信息
     * @param request
     * @return
     */
    @RequestMapping("/getUserInfo")
    public R<CurrentUser> getUserInfo(HttpServletRequest request) {
        HttpSession session = request.getSession();
        Object obj = session.getAttribute(ErpConstant.CURRENT_USER);
        if (obj != null ) {
            CurrentUser currentUser = (CurrentUser) session.getAttribute(ErpConstant.CURRENT_USER);

            return  R.success(currentUser);
        }else{
            String lxcrmLoginToken = getLoginTokenFromSession(request);
            if (lxcrmLoginToken != null) {
                String loginUserStr = RedisUtil.StringOps.get(dbType+"lxcrm-login:"+lxcrmLoginToken);
                if(StringUtils.isNotBlank(loginUserStr)){
                    RedisUtil.KeyOps.expire(dbType+"lxcrm-login:"+lxcrmLoginToken,30*24*3600, TimeUnit.SECONDS);//30天*24小时*3600秒*1000毫秒
                    CurrentUser currentUser = JSONObject.parseObject(loginUserStr, CurrentUser.class);
                    session.setAttribute(ErpConstant.CURRENT_USER, currentUser);
                    return  R.success(currentUser);
                }
            }
        }
        return  R.error("未登录");
    }

    private String getLoginTokenFromSession(HttpServletRequest request) {
        String lxcrmLoginToken = "";
        //读取cookie，取loginToken这个参数
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if("lxcrmLoginToken".equals(cookie.getName())){
                    lxcrmLoginToken = cookie.getValue();
                    break;
                }
            }
        }
        return lxcrmLoginToken;

    }

    @Autowired
    private UserApiService userApiService;

    @Value("${redis_dbtype}")
    protected String dbType;

    @Value("${noPowerContract:Aadi}")
    private String noPowerContract;

    @Value("${mockLxcrmLogin:Y}")
    private String mock;













}
