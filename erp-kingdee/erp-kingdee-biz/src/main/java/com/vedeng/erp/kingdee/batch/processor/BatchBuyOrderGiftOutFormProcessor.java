package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.utils.FileInfoUtils;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchAttachmentDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.repository.BatchAttachmentDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import com.vedeng.erp.kingdee.service.KingDeeStorageInService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 采购售后换货入库单附件
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchBuyOrderGiftOutFormProcessor extends BaseProcessor<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto> {

    public static final String ZERO = "0";

    @Value("${oss_http}")
    private String ossHttp;

    @Autowired
    private BatchAttachmentDtoMapper batchAttachmentDtoMapper;

    @Autowired
    private KingDeeStorageInService kingDeeStorageInService;

    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;

    @Override
    public KingDeeFileDataDto doProcess(BatchWarehouseGoodsOutInDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("开始处理采购赠品出库附件：{}", JSON.toJSONString(dto));
        BatchAttachmentDto attachmentDto = BatchAttachmentDto.builder()
                .attachmentType(462)
                .attachmentFunction(4213)
                .relatedId(dto.getWarehouseGoodsOutInId().intValue())
                .build();
        BatchAttachmentDto batchAttachmentDto = batchAttachmentDtoMapper.purchaseInfindByQuery(attachmentDto);
        if (Objects.isNull(batchAttachmentDto) || StrUtil.isEmpty(batchAttachmentDto.getUri()) || StrUtil.isEmpty(batchAttachmentDto.getDomain())) {
            log.info("未查询到采购赠品出库附件: {}", JSON.toJSONString(dto));
            return null;
        }
        String fileUrl = ossHttp + batchAttachmentDto.getDomain() + batchAttachmentDto.getUri();

        KingDeeFileDataDto query = KingDeeFileDataDto.builder()
                .formId(KingDeeFormConstant.STK_MISCELLANEOUS)
                .erpId(dto.getWarehouseGoodsOutInId().toString())
                .url(fileUrl)
                .build();
        List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(query);
        if (!CollectionUtils.isEmpty(existFile)) {
            log.info("当前附件已经推送过金蝶，{}", JSON.toJSONString(query));
            return null;
        }


        KingDeeStorageInDto storageInDto = new KingDeeStorageInDto();
        storageInDto.setFBillNo(dto.getOutInNo());
        kingDeeStorageInService.query(storageInDto);
        if (storageInDto.getFId() == null || ZERO.equals(storageInDto.getFId())) {
            log.info("采购采购赠品出库未推送金蝶，无法推送附件：{}", dto.getOutInNo());
            return null;
        }

        String suffix = FileInfoUtils.getSuffix(fileUrl);
        String name = StrUtil.isEmpty(batchAttachmentDto.getName()) ? "file" + suffix : batchAttachmentDto.getName() + suffix;
        return KingDeeFileDataDto.builder()
                .fileName(name)
                .aliasFileName(batchAttachmentDto.getName())
                .billNo(dto.getOutInNo())
                .formId(storageInDto.getFormId())
                .isLast(true)
                .fId(storageInDto.getFId())
                .url(fileUrl)
                .erpId(dto.getWarehouseGoodsOutInId().toString())
                .businessId(storageInDto.getFormId() + dto.getWarehouseGoodsOutInId().toString())
                .build();
    }
}
