package com.vedeng.trader.group.deal;

import com.vedeng.common.util.DateUtil;
import com.vedeng.order.dao.BussinessChanceMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.trader.group.BaseBehavior;
import com.vedeng.trader.group.model.SaleorderSum;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;
import java.util.BitSet;
import java.util.List;

/**
 * <b>Description:</b>成交金额行为<br>
 * @Note
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/5/25
 */
public class DealAmountBehavior extends BaseBehavior {

    private BigDecimal lowAmount;
    private BigDecimal highAmount;
    // 运行时注入Mapper
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private SaleorderMapper saleorderMapper=(SaleorderMapper) context.getBean(SaleorderMapper.class);
    @Override
    public BitSet calculate() {
        BitSet res=getBaseTraderSet();
        SaleorderSum query=new SaleorderSum();
        query.setPeriod(DateUtil.getStartAndEndPointForDays(getPeriod()));
        query.setHighAmount(highAmount);
        query.setLowAmount(lowAmount);
        List<Integer> traderIds=saleorderMapper.getTraderIdsByAmount(query);
        BitSet amountSet=new BitSet();
        addBitSet(amountSet,traderIds);
        res.and(amountSet);
        return res;
    }

    public BigDecimal getLowAmount() {
        return lowAmount;
    }

    public void setLowAmount(BigDecimal lowAmount) {
        this.lowAmount = lowAmount;
    }

    public BigDecimal getHighAmount() {
        return highAmount;
    }

    public void setHighAmount(BigDecimal highAmount) {
        this.highAmount = highAmount;
    }
}
