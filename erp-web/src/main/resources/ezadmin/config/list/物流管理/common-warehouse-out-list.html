<!doctype html>
<html lang="en">
<head>
 <meta charset="UTF-8">
 <title>出库单列表</title>
 <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="common-warehouse-out-list" datasource="erp-datasourcetarget" fixednumber="" fixednumberright="" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
<div id="appendHead"></div>
<div class="layui-fluid">
 <div class="layui-tab">
  <ul class="layui-tab-title" id="tab">
  </ul>
 </div>
 <form class="layui-form" id="search">
  <div class=" layui-inline "><label class="layui-form-label">出库单号</label>
   <div class="layui-input-inline">
    <input class=" layui-input list-search-item " type="text" name="OUT_IN_NO" placeholder="请输入出库单号" style="" alias="" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="{&quot;maxlength&quot;:&quot;50&quot;}" validate_messages="{&quot;maxlength&quot;:&quot;长度不能超过{0}&quot;}">
   </div>
  </div>
  <div class=" layui-inline "><label class="layui-form-label">收货方</label>
   <div class="layui-input-inline">
    <input class=" layui-input list-search-item " type="text" name="OUT_IN_COMPANY" placeholder="请输入收货方名称" style="" alias="" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="{&quot;maxlength&quot;:&quot;50&quot;}" validate_messages="{&quot;maxlength&quot;:&quot;长度不能超过{0}&quot;}">
   </div>
  </div>
  <div class=" layui-inline "><label class="layui-form-label">WMS出库单号</label>
   <div class="layui-input-inline">
    <input class=" layui-input list-search-item " type="text" name="WMS_NO" placeholder="请输入WMS出库单号" style="" alias="" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="{&quot;maxlength&quot;:&quot;50&quot;}" validate_messages="{&quot;maxlength&quot;:&quot;长度不能超过{0}&quot;}">
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">关联单号</label>
   <div class="layui-input-inline">
    <input class=" layui-input list-search-item " type="text" name="RELATE_NO" placeholder="请输入关联单号" style="" alias="" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="{&quot;maxlength&quot;:&quot;50&quot;}" validate_messages="{&quot;maxlength&quot;:&quot;长度不能超过{0}&quot;}">
   </div>
  </div>
  <div class=" layui-inline "><label class="layui-form-label">出库时间</label>
   <div class="layui-input-inline">
    <object class=" layui-input list-search-item " type="daterange" name="OUT_IN_TIME" placeholder style="" alias="" data="" datatype="" oper="BETWEEN" jdbctype="DATETIME"></object>
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">出库单类型</label>
   <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="OUT_IN_TYPE" placeholder="全部" style="" alias="" jdbctype="" data="[{
	&quot;V&quot;: &quot;销售出库&quot;,
	&quot;K&quot;: &quot;2&quot;
}, {
	&quot;V&quot;: &quot;销售换货出库&quot;,
	&quot;K&quot;: &quot;4&quot;
}, {
	&quot;V&quot;: &quot;采购售后退货出库&quot;,
	&quot;K&quot;: &quot;6&quot;
}, {
	&quot;V&quot;: &quot;采购售后换货出库&quot;,
	&quot;K&quot;: &quot;7&quot;
}, {
	&quot;V&quot;: &quot;外借出库&quot;,
	&quot;K&quot;: &quot;10&quot;
}, {
	&quot;V&quot;: &quot;报废出库&quot;,
	&quot;K&quot;: &quot;13&quot;
}, {
	&quot;V&quot;: &quot;领用出库&quot;,
	&quot;K&quot;: &quot;14&quot;
}, {
	&quot;V&quot;: &quot;盘亏出库&quot;,
	&quot;K&quot;: &quot;16&quot;
},
{
	&quot;V&quot;: &quot;样品出库&quot;,
	&quot;K&quot;: &quot;18&quot;
},
{
	&quot;V&quot;: &quot;库存转换出库&quot;,
	&quot;K&quot;: &quot;19&quot;
}]" datatype="JSON" oper="EQ"></select>
   </div>
  </div>
     <div class=" layui-inline ">
         <label class="layui-form-label">数据来源</label>
         <div class="layui-input-inline">
             <select class=" layui-input list-search-item " type="search" name="SOURCE" jdbctype="VARCHAR"
                     placeholder="全部"
                     data='[{"K":"ERP","V":"ERP"},{"K":"WMS","V":"WMS"}]'
                     datatype="json"></select>
         </div>
     </div>
 </form>
 <hr class="layui-border-blue">
 <div class="btn-group   bd-highlight" id="tableButton">
 </div>
 <table id="table" class="layui-table" style=" width:100%">
  <thead>
  <tr id="column">
   <th type="rowbutton" id="rowbutton"></th>
   <th item_name="OUT_IN_NO" name="OUT_IN_NO" body="td-link" order="0" url="/warehouse/warehousesout/detail.do?outInNo=${OUT_IN_NO}&outInType=${OUT_IN_TYPE_CODE}" opentype="PARENT" windowname="" datatype="" data="" style="" head="th">ERP出库单号</th>
   <th item_name="RELATE_NO" name="RELATE_NO" body="td-link" order="" url="/warehouse/warehousesout/relatedDetail.do?relatedNo=${RELATE_NO}&outInType=${OUT_IN_TYPE_CODE}" opentype="PARENT" windowname="" datatype="" data="" style="" head="th">关联单号</th>
   <th item_name="WMS_NO" name="WMS_NO" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">WMS出库单号</th>
   <th item_name="OUT_IN_TYPE" name="OUT_IN_TYPE" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">出库单类型</th>
   <th item_name="OUT_IN_COMPANY" name="OUT_IN_COMPANY" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">收货方</th>
   <th item_name="OUT_IN_TIME" name="OUT_IN_TIME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">出库时间</th>
   <th item_name="SOURCE" name="SOURCE" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" head="th">数据来源</th>
  </tr>
  </thead>
  <tbody>
  <tr>
   <td> express:</td>
   <td colspan="100"><pre id="express" class="layui-code" orderby="ORDER BY ADD_TIME DESC" groupby="">StringBuilder sql=new StringBuilder();
sql.append("SELECT OUT_IN_NO , WMS_NO , RELATE_NO,IS_VIRTUAL,
    CASE WHEN OUT_IN_TYPE = '2' THEN '销售出库' WHEN OUT_IN_TYPE = '4' THEN '销售换货出库' WHEN OUT_IN_TYPE = '6' THEN '采购售后退货出库' WHEN OUT_IN_TYPE = '7' THEN '采购售后换货出库' WHEN OUT_IN_TYPE = '10' THEN '外借出库' WHEN OUT_IN_TYPE = '13' THEN '报废出库' WHEN OUT_IN_TYPE = '14' THEN '领用出库' WHEN OUT_IN_TYPE = '16' THEN '盘亏出库' WHEN OUT_IN_TYPE = '18' THEN '样品出库' WHEN OUT_IN_TYPE = '19' THEN '库存转换出库' END AS OUT_IN_TYPE,
    OUT_IN_TYPE as OUT_IN_TYPE_CODE, OUT_IN_COMPANY, DATE_FORMAT(OUT_IN_TIME, '%Y-%m-%d %H:%i:%s') as OUT_IN_TIME, SOURCE,UPDATE_REMARK
    FROM T_WAREHOUSE_GOODS_OUT_IN WHERE OUT_IN_TYPE IN (2, 4, 6, 7, 10, 13, 14, 16,18,19) AND IS_DELETE = 0");
return search(sql);</pre></td>
  </tr>
  <tr>
   <td> count:</td>
   <td colspan="100"><pre id="count" class="layui-code"></pre></td>
  </tr>
  </tbody>
 </table>
</div>

<div id="appendFoot">
    <script type="text/javascript">
        $(function(){
            //console.log($("#mytable tbody [item_name='RELATE_NO']").length);
            $("#mytable tbody tr").each(function(index,obj){
                    var UPDATE_REMARK = $(obj).find("[item_name='UPDATE_REMARK']")[0].value;
                    if(UPDATE_REMARK != undefined && '21-22其他' == UPDATE_REMARK) {
                        var content = $(obj).find("[item_name='RELATE_NO']").text();
                        var objtd =  $(obj).find("[item_name='RELATE_NO']:visible");
                        objtd.text(content);
                    }
                }
            )
        })
    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
 layui.use(function () {

 })
</script>
</body>
</html>