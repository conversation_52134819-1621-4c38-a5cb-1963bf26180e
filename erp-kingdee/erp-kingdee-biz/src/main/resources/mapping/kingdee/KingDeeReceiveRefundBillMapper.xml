<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeReceiveRefundBillMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveRefundBill">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_RECEIVE_REFUND_BILL-->
    <id column="KING_DEE_RECEIVE_REFUND_BILL_ID" jdbcType="INTEGER" property="kingDeeReceiveRefundBillId" />
    <result column="F_ID" jdbcType="VARCHAR" property="fId" />
    <result column="F_Bill_No" jdbcType="VARCHAR" property="fBillNo" />
    <result column="F_Bill_Type_ID" jdbcType="VARCHAR" property="fBillTypeId" />
    <result column="F_DATE" jdbcType="VARCHAR" property="fDate" />
    <result column="F_CONTACT_UNIT_TYPE" jdbcType="VARCHAR" property="fContactUnitType" />
    <result column="F_CONTACT_UNIT" jdbcType="VARCHAR" property="fContactUnit" />
    <result column="F_RECT_UNIT_TYPE" jdbcType="VARCHAR" property="fRectUnitType" />
    <result column="F_RECT_UNIT" jdbcType="VARCHAR" property="fRectUnit" />
    <result column="F_SETTLE_ORG_ID" jdbcType="VARCHAR" property="fSettleOrgId" />
    <result column="F_SALE_ORG_ID" jdbcType="VARCHAR" property="fSaleOrgId" />
    <result column="F_PAY_ORG_ID" jdbcType="VARCHAR" property="fPayOrgId" />
    <result column="F_BUSINESS_TYPE" jdbcType="VARCHAR" property="fBusinessType" />
    <result column="F_QZOK_LSH" jdbcType="VARCHAR" property="fQzokLsh" />
    <result column="F_QZOK_BDDJTID" jdbcType="VARCHAR" property="fQzokBddjtid" />
    <result column="F_REFUND_BILL_ENTRY" jdbcType="VARCHAR" property="fRefundBillEntry" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    KING_DEE_RECEIVE_REFUND_BILL_ID, F_ID, F_Bill_No, F_Bill_Type_ID, F_DATE, F_CONTACT_UNIT_TYPE, 
    F_CONTACT_UNIT, F_RECT_UNIT_TYPE, F_RECT_UNIT, F_SETTLE_ORG_ID, F_SALE_ORG_ID, F_PAY_ORG_ID, 
    F_BUSINESS_TYPE, F_QZOK_LSH, F_QZOK_BDDJTID, F_REFUND_BILL_ENTRY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_RECEIVE_REFUND_BILL
    where KING_DEE_RECEIVE_REFUND_BILL_ID = #{kingDeeReceiveRefundBillId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_RECEIVE_REFUND_BILL
    where KING_DEE_RECEIVE_REFUND_BILL_ID = #{kingDeeReceiveRefundBillId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="KING_DEE_RECEIVE_REFUND_BILL_ID" keyProperty="kingDeeReceiveRefundBillId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveRefundBill" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_RECEIVE_REFUND_BILL (F_ID, F_Bill_No, F_Bill_Type_ID, 
      F_DATE, F_CONTACT_UNIT_TYPE, F_CONTACT_UNIT, 
      F_RECT_UNIT_TYPE, F_RECT_UNIT, F_SETTLE_ORG_ID, 
      F_SALE_ORG_ID, F_PAY_ORG_ID, F_BUSINESS_TYPE, 
      F_QZOK_LSH, F_QZOK_BDDJTID, F_REFUND_BILL_ENTRY
      )
    values (#{fId,jdbcType=VARCHAR}, #{fBillNo,jdbcType=VARCHAR}, #{fBillTypeId,jdbcType=VARCHAR}, 
      #{fDate,jdbcType=VARCHAR}, #{fContactUnitType,jdbcType=VARCHAR}, #{fContactUnit,jdbcType=VARCHAR}, 
      #{fRectUnitType,jdbcType=VARCHAR}, #{fRectUnit,jdbcType=VARCHAR}, #{fSettleOrgId,jdbcType=VARCHAR}, 
      #{fSaleOrgId,jdbcType=VARCHAR}, #{fPayOrgId,jdbcType=VARCHAR}, #{fBusinessType,jdbcType=VARCHAR}, 
      #{fQzokLsh,jdbcType=VARCHAR}, #{fQzokBddjtid,jdbcType=VARCHAR}, #{fRefundBillEntry,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="KING_DEE_RECEIVE_REFUND_BILL_ID" keyProperty="kingDeeReceiveRefundBillId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveRefundBill" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_RECEIVE_REFUND_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fId != null">
        F_ID,
      </if>
      <if test="fBillNo != null">
        F_Bill_No,
      </if>
      <if test="fBillTypeId != null">
        F_Bill_Type_ID,
      </if>
      <if test="fDate != null">
        F_DATE,
      </if>
      <if test="fContactUnitType != null">
        F_CONTACT_UNIT_TYPE,
      </if>
      <if test="fContactUnit != null">
        F_CONTACT_UNIT,
      </if>
      <if test="fRectUnitType != null">
        F_RECT_UNIT_TYPE,
      </if>
      <if test="fRectUnit != null">
        F_RECT_UNIT,
      </if>
      <if test="fSettleOrgId != null">
        F_SETTLE_ORG_ID,
      </if>
      <if test="fSaleOrgId != null">
        F_SALE_ORG_ID,
      </if>
      <if test="fPayOrgId != null">
        F_PAY_ORG_ID,
      </if>
      <if test="fBusinessType != null">
        F_BUSINESS_TYPE,
      </if>
      <if test="fQzokLsh != null">
        F_QZOK_LSH,
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID,
      </if>
      <if test="fRefundBillEntry != null">
        F_REFUND_BILL_ENTRY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fId != null">
        #{fId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnitType != null">
        #{fContactUnitType,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnit != null">
        #{fContactUnit,jdbcType=VARCHAR},
      </if>
      <if test="fRectUnitType != null">
        #{fRectUnitType,jdbcType=VARCHAR},
      </if>
      <if test="fRectUnit != null">
        #{fRectUnit,jdbcType=VARCHAR},
      </if>
      <if test="fSettleOrgId != null">
        #{fSettleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fSaleOrgId != null">
        #{fSaleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fPayOrgId != null">
        #{fPayOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fBusinessType != null">
        #{fBusinessType,jdbcType=VARCHAR},
      </if>
      <if test="fQzokLsh != null">
        #{fQzokLsh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="fRefundBillEntry != null">
        #{fRefundBillEntry,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveRefundBill">
    <!--@mbg.generated-->
    update KING_DEE_RECEIVE_REFUND_BILL
    <set>
      <if test="fId != null">
        F_ID = #{fId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        F_Bill_No = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        F_Bill_Type_ID = #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        F_DATE = #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnitType != null">
        F_CONTACT_UNIT_TYPE = #{fContactUnitType,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnit != null">
        F_CONTACT_UNIT = #{fContactUnit,jdbcType=VARCHAR},
      </if>
      <if test="fRectUnitType != null">
        F_RECT_UNIT_TYPE = #{fRectUnitType,jdbcType=VARCHAR},
      </if>
      <if test="fRectUnit != null">
        F_RECT_UNIT = #{fRectUnit,jdbcType=VARCHAR},
      </if>
      <if test="fSettleOrgId != null">
        F_SETTLE_ORG_ID = #{fSettleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fSaleOrgId != null">
        F_SALE_ORG_ID = #{fSaleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fPayOrgId != null">
        F_PAY_ORG_ID = #{fPayOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fBusinessType != null">
        F_BUSINESS_TYPE = #{fBusinessType,jdbcType=VARCHAR},
      </if>
      <if test="fQzokLsh != null">
        F_QZOK_LSH = #{fQzokLsh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="fRefundBillEntry != null">
        F_REFUND_BILL_ENTRY = #{fRefundBillEntry,jdbcType=VARCHAR},
      </if>
    </set>
    where KING_DEE_RECEIVE_REFUND_BILL_ID = #{kingDeeReceiveRefundBillId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveRefundBill">
    <!--@mbg.generated-->
    update KING_DEE_RECEIVE_REFUND_BILL
    set F_ID = #{fId,jdbcType=VARCHAR},
      F_Bill_No = #{fBillNo,jdbcType=VARCHAR},
      F_Bill_Type_ID = #{fBillTypeId,jdbcType=VARCHAR},
      F_DATE = #{fDate,jdbcType=VARCHAR},
      F_CONTACT_UNIT_TYPE = #{fContactUnitType,jdbcType=VARCHAR},
      F_CONTACT_UNIT = #{fContactUnit,jdbcType=VARCHAR},
      F_RECT_UNIT_TYPE = #{fRectUnitType,jdbcType=VARCHAR},
      F_RECT_UNIT = #{fRectUnit,jdbcType=VARCHAR},
      F_SETTLE_ORG_ID = #{fSettleOrgId,jdbcType=VARCHAR},
      F_SALE_ORG_ID = #{fSaleOrgId,jdbcType=VARCHAR},
      F_PAY_ORG_ID = #{fPayOrgId,jdbcType=VARCHAR},
      F_BUSINESS_TYPE = #{fBusinessType,jdbcType=VARCHAR},
      F_QZOK_LSH = #{fQzokLsh,jdbcType=VARCHAR},
      F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      F_REFUND_BILL_ENTRY = #{fRefundBillEntry,jdbcType=VARCHAR}
    where KING_DEE_RECEIVE_REFUND_BILL_ID = #{kingDeeReceiveRefundBillId,jdbcType=INTEGER}
  </update>

  <update id="updateLshByFBillNo">
    update KING_DEE_RECEIVE_REFUND_BILL
    set F_QZOK_LSH = #{lsh,jdbcType=VARCHAR}
    where F_Bill_No = #{fBillNo,jdbcType=VARCHAR}
      and IS_DELETE = 0
    </update>
</mapper>