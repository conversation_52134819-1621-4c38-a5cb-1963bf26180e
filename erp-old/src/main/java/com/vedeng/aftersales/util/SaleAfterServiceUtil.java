
package com.vedeng.aftersales.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.order.model.SaleorderGoods;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <b>Description: 售后业务工具方法</b><br> 
 * <b>Author: Franlin.wu</b> 
 * 
 * <br><b>Date: 2018年12月15日 下午1:54:58 </b> 
 *
 */
public class SaleAfterServiceUtil
{
	/**
	 * 记录日志
	 */
	private static final Logger LOG = LoggerFactory.getLogger(SaleAfterServiceUtil.class);
	
	/**
	 * 
	 * <b>Description: 【存在优惠券】计算单个sku的退货退款金额</b><br> 
	 * A退款金额 = 当前sku退货数量*(当前sku的总金额-当前sku的已退金额)/(当前sku的总数量-当前sku已退数量)
	 * @param nowReturnNum    当前salederGoodsId退货数量
	 * @param skuTotalNum     当前salederGoodsId的总数量
	 * @param alReturnNum     当前salederGoodsId已退数量
	 * @param skuTotalAmount  当前salederGoodsId的总金额
	 * @param alReturnAmount  当前salederGoodsId的已退金额
	 * @return
	 * <b>Author: Franlin.wu</b>  
	 * <br><b>Date: 2018年12月15日 下午2:00:25 </b>
	 */
	public static BigDecimal subRefundAmount(BigDecimal nowReturnNum, BigDecimal skuTotalNum, BigDecimal alReturnNum, BigDecimal skuTotalAmount,
											 BigDecimal alReturnAmount, SaleorderGoods saleorderGoods)
	{
		LOG.info("当前salederGoodsId退货数量:{}, 当前salederGoodsId的总数量:{}, 当前salederGoodsId已退数量:{}, 当前salederGoodsId的总金额:{}, 当前salederGoodsId的已退金额:{} ", nowReturnNum, skuTotalNum, alReturnNum, skuTotalAmount, alReturnAmount);
		// 默认当前sku退款金额为0
		BigDecimal skuRefundAmount = BigDecimal.ZERO;
		// 不满足
		boolean flag = null == nowReturnNum || null == skuTotalNum || null == alReturnNum || null == skuTotalAmount || null == alReturnAmount || skuTotalNum.compareTo(alReturnNum) <= 0;
		// 参数存在null
		if(!flag)
		{
			//sku此次是否全部退货
			boolean allReturnNow = nowReturnNum.compareTo(skuTotalNum.subtract(alReturnNum)) >= 0;
			if (saleorderGoods.getIsCoupons() == 1 && saleorderGoods.getMaxSkuRefundAmount().compareTo(BigDecimal.ZERO) > 0 && allReturnNow){
				//当订单商品使用了优惠券，使用saleorderGoods的MAX_SKU_REFUND_AMOUNT来兜底精确度
				skuRefundAmount = saleorderGoods.getMaxSkuRefundAmount().subtract(alReturnAmount);
			} else {
				// 计算公式： A退款金额 = 当前salederGoodsId退货数量*(当前salederGoodsId的总金额-当前salederGoodsId的已退金额)/(当前salederGoodsId的总数量-当前sku已退数量)
				skuRefundAmount = BigDecimalCommonUtil.divide(skuTotalAmount.subtract(alReturnAmount).multiply(nowReturnNum), skuTotalNum.subtract(alReturnNum));
			}
		}
		return skuRefundAmount;
	}
	
}

