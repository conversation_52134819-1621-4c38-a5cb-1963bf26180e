<%@ page contentType="text/html; charset=UTF-8" language="java" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="上传同行单" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js"></script>
<script type="text/javascript" src="<%= basePath %>static/js/orderstream/buyorder/upload_direct_file.js?rnd=${resourceVersionKey}"></script>

<style>
    .add-detail .border-blue {
        color: #3384ef;
    }

    .add-detail .border-blue:hover {
        color: #fff;
    }
</style>

<div class="addElement">
    <div class="add-main">
        <form method="post" id="myform">
            <div class="mb8 c_1">
                <div class="f_left">
                    <input type="file" class="upload_file" id="file_1" name="lwfile"
                           style="display: none;" onchange="uploadFile(this,1)"/>
                    <input type="text" class="input-middle" id="name_1" readonly="readonly"
                           placeholder="请上传随货同行单" name="fileName" onclick="file_1.click();"
                           value="${bus.name}">
                    <input type="hidden" id="yybusUri" name="fileUri" value="${bus.uri}">
                    <div class="font-red " id="yy-message" style="display: none;">请上传随货同行单</div>
                </div>
                <label class="bt-bg-style bt-middle bg-light-blue ml10" id="busUpload"
                       onclick="return $('#file_1').click();">浏览</label>
                <!-- 上传成功出现 -->
                <c:choose>
                    <c:when test="${!empty bus.uri}">
                        <i class="iconsuccesss ml7" id="img_icon_4"></i>
                        <a href="http://${bus.domain}${bus.uri}" target="_blank"
                           class="font-blue cursor-pointer mr5 ml10 mt4"
                           id="img_view_1">查看</a>
                        <span class="font-red cursor-pointer mt4" onclick="del(1)"
                              id="img_del_1">删除</span>
                    </c:when>
                    <c:otherwise>
                        <i class="iconsuccesss ml7 none" id="img_icon_4"></i>
                        <a href="" target="_blank"
                           class="font-blue cursor-pointer mr5 ml10 mt4 none"
                           id="img_view_1">查看</a>
                        <span class="font-red cursor-pointer mt4 none" onclick="del(1)"
                              id="img_del_1">删除</span>
                    </c:otherwise>
                </c:choose>
                <div class="clear"></div>
            </div>
            <div class="clear" id="conadd1">
                <span class="bt-border-style bt-small border-blue mt8"
                      onclick="con_add(1,'请上传随货同行单');">继续添加</span>
            </div>

            <div class="add-detail">
                <span>1、支持格式：jpg、png、jpeg</span><br>
                <span>2、上传文件不得超过20M</span>
            </div>
            <input type="hidden" name="relatedId" value="${purchaseDeliveryDirectBatchInfoId}">
            <input type="hidden" name="formToken" value="${formToken}"/>
            <button class="bt-bg-style bg-light-blue bt-small" style="margin-top: 15px" onclick="uploadDirectInfo()">保存</button>
        </form>
    </div>
</div>