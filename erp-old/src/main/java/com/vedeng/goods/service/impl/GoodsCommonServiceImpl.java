package com.vedeng.goods.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.Action;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.common.util.NumberUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.dao.*;
import com.vedeng.goods.enums.GoodsSignContractModeEnum;
import com.vedeng.goods.manager.GoodsExtValidManager;
import com.vedeng.goods.manager.constants.GoodsValidConstants;
import com.vedeng.goods.manager.extension.GoodsExtValidModuleEnum;
import com.vedeng.goods.manager.extension.GoodsTodoResultVo;
import com.vedeng.goods.manager.extension.GoodsValidResultVo;
import com.vedeng.goods.manager.rule.GoodsExtValidElement;
import com.vedeng.goods.manager.rule.GoodsValidTypeEnum;
import com.vedeng.goods.manager.rule.GoodsValidatedRule;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.CoreSpu;
import com.vedeng.goods.model.entity.GoodsLevelDo;
import com.vedeng.goods.model.entity.GoodsPositionDo;
import com.vedeng.goods.model.vo.*;
import com.vedeng.goods.service.GoodsCommonService;
import com.vedeng.goods.supprot.BaseGoodsDo;
import com.vedeng.goods.utils.GoodsUtils;
import com.vedeng.system.service.ActionService;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.constant.TodoRelationGoodsInfoEnum;
import com.vedeng.todolist.service.ITodoInstance;
import com.vedeng.todolist.service.TodoListInstanceFactory;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Service
public class GoodsCommonServiceImpl implements GoodsCommonService {

    private final static Logger LOGGER = LoggerFactory.getLogger(GoodsCommonServiceImpl.class);

    private final static Comparator<BaseGoodsDo> DEFAULT_COMPARATOR = Comparator.comparingInt(BaseGoodsDo::getOrdinal);

    @Resource
    private GoodsLevelMapper goodsLevelMapper;
    @Resource
    private GoodsPositionMapper goodsPositionMapper;
    @Resource
    private GoodsExtValidManager goodsExtValidManager;
    @Resource
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Autowired
    private TodoListInstanceFactory todoListInstanceFactory;

    @Resource
    private ActionService actionService;

    @Value("${erp_url}")
    private String erpDomain;

    @Resource
    private CoreSpuMapper coreSpuMapper;
    @Resource
    private CoreOperateInfoGenerateExtendMapper coreOperateInfoGenerateExtendMapper;

    private final AtomicReference<String> goodsDeliveryTimeUrlHolder = new AtomicReference<>();
    @Value("${redis_dbtype}")
    protected String dbType;// 开发redis，测试redis

    @Override
    public List<GoodsLevelVo> listAllGoodsLevel(boolean additional) {
        List<GoodsLevelDo> goodsLevelDos = goodsLevelMapper.selectAll();
        if (CollectionUtils.isEmpty(goodsLevelDos)) {
            throw new IllegalStateException("goods level is empty.");
        }

        //sort by ordinal no.
        goodsLevelDos.sort(DEFAULT_COMPARATOR);

        List<GoodsLevelVo> resultList = new ArrayList<>(goodsLevelDos.size());

        for (GoodsLevelDo goodsLevelDo : goodsLevelDos) {
            GoodsLevelVo goodsLevelVo = createGoodsLevelVo(goodsLevelDo);
            if (additional) {
                GoodsValidatedRule validatedRule = getValidatedRule(GoodsValidTypeEnum.SKU.getType(), goodsLevelDo.getId());
                if (validatedRule != null) {
                    List<String> todoItem = new LinkedList<>();
                    List<GoodsExtValidElement> additionalModules = validatedRule.getAdditionalModules();
                    additionalModules.forEach(addition -> {
                        GoodsExtValidModuleEnum goodsExtValidModuleEnum = GoodsExtValidModuleEnum.getByServiceId(addition.getServiceId());
                        if (addition.getRequired() && goodsExtValidModuleEnum != null && goodsExtValidModuleEnum.getStatus().equals(GoodsValidConstants.ENABLE)) {
                            todoItem.add(addition.getServiceName());
                        }
                    });
                    goodsLevelVo.setTodoItems(todoItem);
                    goodsLevelVo.setValidatedRule(validatedRule);
                }
            }

            resultList.add(goodsLevelVo);
        }

        return resultList;
    }

    @Override
    public List<GoodsPositionVo> listAllGoodsPosition() {
        List<GoodsPositionDo> goodsPositionDos = goodsPositionMapper.selectAll();

        if (CollectionUtils.isEmpty(goodsPositionDos)) {
            throw new IllegalStateException("goods position is empty.");
        }

        //VDERP-15068 修改商品单位排序规则
        goodsPositionDos.sort(Comparator.comparing(GoodsPositionDo::getPriority).reversed());

        List<GoodsPositionVo> resultList = new ArrayList<>(goodsPositionDos.size());

        for (GoodsPositionDo goodsPositionDo : goodsPositionDos) {
            resultList.add(createGoodsPositionVo(goodsPositionDo));
        }

        return resultList;
    }


    @Override
    public String getGoodsLevelName(Integer goodsLevelNo) {
        if (goodsLevelNo==null) {
            return null;
        }

        String goodsLevelName;
        Optional<GoodsLevelDo> goodsLevelIfMatch = goodsLevelMapper.selectAll().stream().filter(level -> Objects.equals(goodsLevelNo, level.getId())).findFirst();
        if(goodsLevelIfMatch.isPresent()){
            goodsLevelName=goodsLevelIfMatch.get().getLevelName();
        }else {
            goodsLevelName = DEFAULT_GOODS_LEVEL_NAME;
        }

        return goodsLevelName;
    }

    @Override
    public String getGoodsPositionName(Integer goodsPositionNo) {
        if (goodsPositionNo == null) {
            return null;
        }

        String goodsPositionName;
        Optional<GoodsPositionDo> goodsPositionIfMatch = goodsPositionMapper.selectAll().stream().filter(position -> Objects.equals(goodsPositionNo, position.getId())).findFirst();
        if(goodsPositionIfMatch.isPresent()){
            goodsPositionName= goodsPositionIfMatch.get().getPositionName();
        } else {
            goodsPositionName = DEFAULT_GOODS_POSITION_NAME;
        }
        return goodsPositionName;
    }

    @Override
    public GoodsPositionDo getGoodsPosition(Integer goodsPositionNo) {
        if (!NumberUtil.isPositive(goodsPositionNo)) {
            return null;
        }
        return goodsPositionMapper.selectByPrimaryKey(goodsPositionNo);
    }

    @Override
    public GoodsLevelDo getGoodsLevel(Integer goodsLevelNo) {
        if (!NumberUtil.isPositive(goodsLevelNo)) {
            return null;
        }
        return goodsLevelMapper.selectByPrimaryKey(goodsLevelNo);
    }

    @Override
    public GoodsLevelVo getGoodsLevelVo(Integer goodsLevelNo) {
        GoodsLevelDo goodsLevelDo = getGoodsLevel(goodsLevelNo);

        if (goodsLevelDo == null || CommonConstants.IS_DELETE_1.equals(goodsLevelDo.getIsDeleted())) {
            return null;
        }
        return createGoodsLevelVo(goodsLevelDo);
    }

    @Override
    public GoodsPositionVo getGoodsPositionVo(Integer goodsPositionNo) {
        GoodsPositionDo goodsPositionDo = getGoodsPosition(goodsPositionNo);
        if (goodsPositionDo == null || CommonConstants.IS_DELETE_1.equals(goodsPositionDo.getIsDeleted())) {
            return null;
        }
        return createGoodsPositionVo(goodsPositionDo);
    }

    @Override
    public GoodsValidatedRule getValidatedRule(Integer goodsValidType, Integer goodsLevelNo) {
        //做一道缓存
        String key=dbType + ErpConst.KEY_PREFIX_GOODSVALIDATEDRULE + goodsValidType+"_"+goodsLevelNo;
        if (JedisUtils.exists(key)) {
            String goodsValidatedRule = JedisUtils.get(key);
            if (StringUtil.isNotBlank(goodsValidatedRule)) {
                return JSON.parseObject(goodsValidatedRule, GoodsValidatedRule.class);
            }else{
                throw new IllegalStateException();
            }
        }else{
            GoodsValidTypeEnum validType = GoodsValidTypeEnum.getByType(goodsValidType);
            if (validType == null) {
                throw new IllegalStateException();
            }
            GoodsLevelDo goodsLevelQuery = goodsLevelMapper.selectByPrimaryKeyWithBlob(goodsLevelNo);
            String jsonRule=null;
            if(GoodsValidTypeEnum.SPU.getType().equals(goodsValidType)){
                jsonRule= goodsLevelQuery.getSpuValidationRules();
            }else  if(GoodsValidTypeEnum.SKU.getType().equals(goodsValidType)){
                jsonRule= goodsLevelQuery.getSkuValidationRules();
            }
            if (StringUtil.isBlank(jsonRule) ) {
                return null;
            }
            GoodsValidatedRule goodsValidatedRule= JSON.parseObject(jsonRule,GoodsValidatedRule.class);
            JedisUtils.set(key,jsonRule, 60 * 60 * 24 * 3);
            return goodsValidatedRule;
        }
    }

    @Override
    public List<GoodsValidatedRule> getAllValidatedRule(Integer goodsValidType ) {
        List<GoodsLevelDo> goodsLevelDos = goodsLevelMapper.selectAll();
        if (CollectionUtils.isEmpty(goodsLevelDos)) {
            throw new IllegalStateException("goods level is empty.");
        }
        //sort by ordinal no.
        goodsLevelDos.sort(DEFAULT_COMPARATOR);

        List<GoodsValidatedRule> resultList = new ArrayList<>();

        for (GoodsLevelDo goodsLevelDo : goodsLevelDos) {
            GoodsValidatedRule validatedRule = getValidatedRule(goodsValidType, goodsLevelDo.getId());
            resultList.add(validatedRule);
        }
        return resultList;
    }


    @Override
    public int countTodoItems(Integer skuId) {
        return goodsExtValidManager.countTodoItems(skuId);
    }

    @Override
    public GoodsValidResultVo checkGoodsWhileSync(Integer skuId) {
        return goodsExtValidManager.checkGoodsWhileSync(skuId);
    }

    @Override
    public List<GoodsTodoItemVo> listSkuTodoItems(Integer skuId, boolean notDoneOnly) {
        if (skuId == null || skuId <= 0) {
            return null;
        }

        //代办SKU事项
        List<GoodsTodoResultVo> goodsTodoResultVos = goodsExtValidManager.getTodoItems(skuId);
        if (CollectionUtils.isEmpty(goodsTodoResultVos)) {
            return Collections.emptyList();
        }

        List<GoodsTodoItemVo> resultList = new LinkedList<>();
        for (GoodsTodoResultVo goodsTodoResultVo : goodsTodoResultVos) {
            if (notDoneOnly && goodsTodoResultVo.getDone()) {
                continue;
            }

            GoodsExtValidModuleEnum goodsExtValidModuleEnum = GoodsExtValidModuleEnum.getByServiceId(goodsTodoResultVo.getServiceId());
            if (goodsExtValidModuleEnum == null) {
                continue;
            }

            String targetUrl=null;
            if (!goodsTodoResultVo.getDone()) {
                targetUrl = determineTargetUrl(goodsExtValidModuleEnum, skuId);
            }

            GoodsTodoItemVo goodsTodoItemVo = new GoodsTodoItemVo();
            goodsTodoItemVo.setServiceId(goodsTodoResultVo.getServiceId());
            goodsTodoItemVo.setServiceName(goodsTodoResultVo.getServiceName());
            goodsTodoItemVo.setDone(goodsTodoResultVo.getDone());
            goodsTodoItemVo.setViewName(goodsExtValidModuleEnum.getName());
            goodsTodoItemVo.setUrl(targetUrl);
            resultList.add(goodsTodoItemVo);
        }

        return resultList;
    }


    private String determineTargetUrl(GoodsExtValidModuleEnum goodsExtValidModuleEnum, Integer skuId) {
        if (goodsExtValidModuleEnum == null) {
            return null;
        }

        final String skuNo = GoodsUtils.createGoodsNo(skuId);

        String goodsDeliveryTimeUrl = goodsDeliveryTimeUrlHolder.get();
        if (goodsDeliveryTimeUrl == null) {
            Action actionQuery = actionService.getByActionDesc("预计可发货时间");
            String moduleName = actionQuery.getModuleName();
            goodsDeliveryTimeUrlHolder.set(moduleName);
            goodsDeliveryTimeUrl = moduleName;
        }

        EnumMap<GoodsExtValidModuleEnum, String> goodsExtValidModuleMap = new EnumMap<>(GoodsExtValidModuleEnum.class);
        goodsExtValidModuleMap.put(GoodsExtValidModuleEnum.HAS_PRICING, erpDomain + "price/basePriceMaintain/index.do?includeSkuNosStr="+skuNo);
        goodsExtValidModuleMap.put(GoodsExtValidModuleEnum.EXTERNAL_AFTER_SALES, erpDomain + "aftersale/serviceStandard/detail.do?skuNo=" + skuNo);
        goodsExtValidModuleMap.put(GoodsExtValidModuleEnum.INTERNAL_AFTER_SALES, erpDomain + "aftersale/serviceStandard/detail.do?skuNo=" + skuNo);
        goodsExtValidModuleMap.put(GoodsExtValidModuleEnum.HAS_REPORT, erpDomain + "goods/vgoods/addSku.do?skuId=" + skuId);
        goodsExtValidModuleMap.put(GoodsExtValidModuleEnum.HAS_OPERATION, erpDomain + "vgoods/operate/viewOperate.do?skuId=" + skuId);
        goodsExtValidModuleMap.put(GoodsExtValidModuleEnum.HAS_DELIVERY_TIME, goodsDeliveryTimeUrl + "&SKU_NO=" + skuNo);

        return goodsExtValidModuleMap.get(goodsExtValidModuleEnum);
    }


    private final static List<Integer> TODO_ITEM_ID_LIST = Arrays.asList(
            TodoListBuzSceneEnum.MAINTAIN_DATA_SKU.getBuzSceneId(),
            TodoListBuzSceneEnum.MAINTAIN_DATA_DELIVERY_TIME.getBuzSceneId(),
            TodoListBuzSceneEnum.MAINTAIN_DATA_PRICE.getBuzSceneId(),
            TodoListBuzSceneEnum.MAINTAIN_DATA_OPERATION_INFO.getBuzSceneId(),
            TodoListBuzSceneEnum.MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY.getBuzSceneId(),
            TodoListBuzSceneEnum.MAINTAIN_DATA_REPORT_INFO.getBuzSceneId(),
            TodoListBuzSceneEnum.MAINTAIN_DATA_SIGN_CONTRACT_MODE.getBuzSceneId(),
            TodoListBuzSceneEnum.MAINTAIN_DATA_AFTER_SALE_POLICY.getBuzSceneId()
    );

    private final static Integer EMPTY_GOODS_POSITION_NO = 0;

    @Override
    public GoodsTodoSummaryVo statGoodsLevelAndPosition() {
        GoodsTodoSummaryVo goodsTodoSummaryVo = new GoodsTodoSummaryVo();

        List<GoodsLevelDo> goodsLevelDos = goodsLevelMapper.selectAll();
        for (GoodsLevelDo goodsLevelDo : goodsLevelDos) {
            if (goodsLevelDo == null || CommonConstants.IS_DELETE_1.equals(goodsLevelDo.getIsDeleted())) {
                continue;
            }

            List<Integer> countList = goodsLevelMapper.countGoodsByGoodsLevelNo(goodsLevelDo.getId(), TODO_ITEM_ID_LIST);
            GoodsTodoSummaryVo.GoodsLevelTodoVo goodsLevelTodoVo = new GoodsTodoSummaryVo.GoodsLevelTodoVo();
            goodsLevelTodoVo.setGoodsLevelNo(goodsLevelDo.getId());
            goodsLevelTodoVo.setGoodsLevelName(goodsLevelDo.getLevelName());
            goodsLevelTodoVo.setUniqueIdentifier(goodsLevelDo.getUniqueIdentifier() + "级");
            goodsLevelTodoVo.setTotalCount(countList.get(0));
            goodsLevelTodoVo.setTodoCount(countList.get(1));

            goodsTodoSummaryVo.addGoodsLevelTodoVo(goodsLevelTodoVo);
        }

        List<Integer> goodsLevelNoList = goodsLevelDos.stream().map(GoodsLevelDo::getId).collect(Collectors.toList());

        List<GoodsPositionDo> goodsPositionDos = goodsPositionMapper.selectAll();

        //VDERP-15068 修改商品单位排序规则
        goodsPositionDos.sort(Comparator.comparing(GoodsPositionDo::getPriority).reversed());

        GoodsPositionDo defaultGoodsPositionDo = getDefaultGoodsPositionDo();
        goodsPositionDos.add(defaultGoodsPositionDo);
        for (GoodsPositionDo goodsPositionDo : goodsPositionDos) {
            if (goodsPositionDo == null || CommonConstants.IS_DELETE_1.equals(goodsPositionDo.getIsDeleted())) {
                continue;
            }
            List<Integer> countList = goodsLevelMapper.countGoodsByPositionNo(goodsPositionDo.getId(), goodsLevelNoList);
            GoodsTodoSummaryVo.GoodsPositionTodoVo goodsPositionTodoVo = new GoodsTodoSummaryVo.GoodsPositionTodoVo();
            goodsPositionTodoVo.setGoodsPositionNo(goodsPositionDo.getId());
            goodsPositionTodoVo.setGoodsPositionName(goodsPositionDo.getPositionName());
            if (goodsPositionDo.getId().equals(EMPTY_GOODS_POSITION_NO)) {
                goodsPositionTodoVo.setShowName("--");
            } else {
                goodsPositionTodoVo.setShowName(goodsPositionDo.getId() + "档");
            }
            goodsPositionTodoVo.setTotalCount(countList.get(0));
            goodsPositionTodoVo.setLevelACount(countList.get(1));
            goodsPositionTodoVo.setLevelBCount(countList.get(2));
            goodsPositionTodoVo.setLevelCCount(countList.get(3));
            goodsPositionTodoVo.setLevelDCount(countList.get(4));
            goodsPositionTodoVo.setLevelECount(countList.get(5));

            goodsTodoSummaryVo.addGoodsPositionTodoVo(goodsPositionTodoVo);
        }

        return goodsTodoSummaryVo;
    }

    private GoodsPositionDo getDefaultGoodsPositionDo() {
        GoodsPositionDo goodsPositionDo = new GoodsPositionDo();
        goodsPositionDo.setId(EMPTY_GOODS_POSITION_NO);
        goodsPositionDo.setPositionName("--");
        return goodsPositionDo;
    }

    @Override
    public ResultInfo checkGoodsInfo4Push(Integer skuId) {
        if (skuId == null || skuId <= 0) {
            return null;
        }
        CoreSkuGenerate skuGenerate= coreSkuGenerateMapper.selectByPrimaryKey(skuId);
        if(skuGenerate==null){
            return new ResultInfo(-1, "SKU不存在。");
        }

        GoodsLevelVo goodsLevelVo = goodsLevelMapper.getGoodsLevelVoBySkuId(skuId);
        if (goodsLevelVo == null) {
            return new ResultInfo(-1, "该SKU未初始化分档信息");
        }
        if (StringUtil.isNotBlank(goodsLevelVo.getUniqueIdentifier()) && ("D".equals(goodsLevelVo.getUniqueIdentifier())||"E".equals(goodsLevelVo.getUniqueIdentifier()))) {
            return new ResultInfo(-1, "商品所处等级限制推送！");
        }
        OperateSkuVo operateSkuVo = coreOperateInfoGenerateExtendMapper.selectSkuOperateBySpuId(skuId);
        if(operateSkuVo == null){
            return new ResultInfo(-1, "该SKU的商品主图或者图文详情维护不完整，请维护后再推送。");
        }

        if(skuGenerate==null||skuGenerate.getOperateInfoId()==null||skuGenerate.getOperateInfoId()==0){
            return new ResultInfo(-1, "该SKU的商品主图或者图文详情维护不完整，请维护后再推送。");
        } else if (operateSkuVo.getSeoKeywords() == null) {
           return new ResultInfo(-1, "SEO信息缺失，请补充完整再推送。");
        }

        GoodsValidResultVo todoItems = checkGoodsWhileSync(skuId);
        if (todoItems == null || todoItems.getFailCount() == 0) {
            return null;
        }
        LOGGER.info("SKU分档信息校验不通过 failItems:{}", JSON.toJSONString(todoItems.getFailItems()));
        StringBuffer failItemsStr = new StringBuffer();
        todoItems.getFailItems().forEach(todoItem -> {
            Integer todoSceneId = TodoRelationGoodsInfoEnum.getTodoSceneIdByBuzId(todoItem.getServiceId()).getTodoSceneId();
            ITodoInstance todoInstance = todoListInstanceFactory.createTodoInstance(TodoListBuzSceneEnum.getTodoListBuzSceneEnumById(todoSceneId));
            LOGGER.info("SKU分档检查添加推送待办信息 todoSceneId:{},skuId:{},errorMessage:{}", todoSceneId, skuId, todoItem.getServiceId());
            todoInstance.add(skuId, "V" + skuId, "", "skuNo");
            failItemsStr.append(todoItem.getServiceName())
                    .append("、");
        });
        try {
            failItemsStr.deleteCharAt(failItemsStr.length() - 1);
            sendMessage(skuId, skuGenerate.getSpuId(), failItemsStr);
        } catch (Exception e) {
            LOGGER.error("发送工作台消息至商品归属人发生异常", e);
        }
        return new ResultInfo(-2, "", todoItems.getFailItems());
    }

    /**
     * 发送工作台消息至商品归属人
     *
     * @param skuId
     * @param spuId
     * @param failItemStr
     */
    private void sendMessage(Integer skuId, Integer spuId, StringBuffer failItemStr) {
        if (skuId == null || skuId == 0){
            return;
        }
        HashSet<Integer> userIdsSet = new HashSet<>();
        CoreSpu spuInfo = coreSpuMapper.getSpuBySku("V" + skuId);
        if (spuInfo == null){
            return;
        }
        if (spuInfo.getAssignmentManagerId() != null && spuInfo.getAssignmentManagerId() > 0){
            userIdsSet.add(spuInfo.getAssignmentManagerId());
        }
        if (spuInfo.getAssignmentAssistantId() != null && spuInfo.getAssignmentAssistantId() > 0){
            userIdsSet.add(spuInfo.getAssignmentAssistantId());
        }
        if (CollectionUtils.isEmpty(userIdsSet)){
            LOGGER.info("当前SKU不存在归属经理和归属助理，无需发送工作台消息 skuId:{}", skuId);
            return;
        }

        HashMap<String, String> params = new HashMap<>(4);
        params.put("skuNo", "V" + skuId);
        params.put("items", failItemStr.toString());
        LOGGER.info("SKU推送分级分档校验不通过发送站内信 userIds:{},params:{}",
                userIdsSet.toString(), params.toString());
        MessageUtil.sendMessage(160, new ArrayList<>(userIdsSet), params,
                "/goods/vgoods/viewSku.do?skuId=" + skuId + "&spuId=" + spuId + "&&pageType=0");
    }

    @Override
    public int updateSynchronizationStatusBySkuIds(List<Integer> skuIds, Integer status) {
        if (CollectionUtils.isEmpty(skuIds) || status == null) {
            return 0;
        }
        return coreSkuGenerateMapper.updateSynchronizationStatusBySkuIds(skuIds, status);
    }



    //~================================================================================= Private methods


    private GoodsLevelVo createGoodsLevelVo(GoodsLevelDo goodsLevelDo){
        GoodsLevelVo goodsLevelVo = new GoodsLevelVo();
        goodsLevelVo.setId(goodsLevelDo.getId());
        goodsLevelVo.setUniqueIdentifier(goodsLevelDo.getUniqueIdentifier());
        goodsLevelVo.setLevelName(goodsLevelDo.getLevelName());
        goodsLevelVo.setDescription(goodsLevelDo.getDescription());
        return goodsLevelVo;
    }


    private GoodsPositionVo createGoodsPositionVo(GoodsPositionDo goodsPositionDo){
        GoodsPositionVo goodsPositionVo = new GoodsPositionVo();
        goodsPositionVo.setId(goodsPositionDo.getId());
        goodsPositionVo.setPositionName(goodsPositionDo.getPositionName());
        goodsPositionVo.setDescription(goodsPositionDo.getDescription());
        GoodsSignContractModeEnum goodsSignContractModeEnum = GoodsSignContractModeEnum.getByCode(goodsPositionDo.getSignContractMode());
        goodsPositionVo.setSignContractModeRule(goodsSignContractModeEnum.getRule());
        return goodsPositionVo;
    }

}
