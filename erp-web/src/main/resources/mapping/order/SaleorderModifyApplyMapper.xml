<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.SaleorderModifyApplyMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.SaleorderModifyApply" >
    <id column="SALEORDER_MODIFY_APPLY_ID" property="saleorderModifyApplyId" jdbcType="INTEGER" />
    <result column="SALEORDER_MODIFY_APPLY_NO" property="saleorderModifyApplyNo" jdbcType="VARCHAR" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="SALEORDER_ID" property="saleorderId" jdbcType="INTEGER" />
    <result column="VALID_STATUS" property="validStatus" jdbcType="BIT" />
    <result column="VALID_TIME" property="validTime" jdbcType="BIGINT" />
    <result column="TAKE_TRADER_ID" property="takeTraderId" jdbcType="INTEGER" />
    <result column="TAKE_TRADER_NAME" property="takeTraderName" jdbcType="VARCHAR" />
    <result column="TAKE_TRADER_CONTACT_ID" property="takeTraderContactId" jdbcType="INTEGER" />
    <result column="TAKE_TRADER_CONTACT_NAME" property="takeTraderContactName" jdbcType="VARCHAR" />
    <result column="TAKE_TRADER_CONTACT_MOBILE" property="takeTraderContactMobile" jdbcType="VARCHAR" />
    <result column="TAKE_TRADER_CONTACT_TELEPHONE" property="takeTraderContactTelephone" jdbcType="VARCHAR" />
    <result column="TAKE_TRADER_ADDRESS_ID" property="takeTraderAddressId" jdbcType="INTEGER" />
    <result column="TAKE_TRADER_AREA" property="takeTraderArea" jdbcType="VARCHAR" />
    <result column="TAKE_TRADER_ADDRESS" property="takeTraderAddress" jdbcType="VARCHAR" />
    <result column="LOGISTICS_COMMENTS" property="logisticsComments" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_ID" property="invoiceTraderId" jdbcType="INTEGER" />
    <result column="INVOICE_TRADER_NAME" property="invoiceTraderName" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_CONTACT_ID" property="invoiceTraderContactId" jdbcType="INTEGER" />
    <result column="INVOICE_TRADER_CONTACT_NAME" property="invoiceTraderContactName" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_CONTACT_MOBILE" property="invoiceTraderContactMobile" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_CONTACT_TELEPHONE" property="invoiceTraderContactTelephone" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_ADDRESS_ID" property="invoiceTraderAddressId" jdbcType="INTEGER" />
    <result column="INVOICE_TRADER_AREA" property="invoiceTraderArea" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_ADDRESS" property="invoiceTraderAddress" jdbcType="VARCHAR" />
    <result column="INVOICE_COMMENTS" property="invoiceComments" jdbcType="VARCHAR" />
    <result column="OLD_TAKE_TRADER_CONTACT_ID" property="oldTakeTraderContactId" jdbcType="INTEGER" />
    <result column="OLD_TAKE_TRADER_CONTACT_NAME" property="oldTakeTraderContactName" jdbcType="VARCHAR" />
    <result column="OLD_TAKE_TRADER_CONTACT_MOBILE" property="oldTakeTraderContactMobile" jdbcType="VARCHAR" />
    <result column="OLD_TAKE_TRADER_CONTACT_TELEPHONE" property="oldTakeTraderContactTelephone" jdbcType="VARCHAR" />
    <result column="OLD_TAKE_TRADER_ADDRESS_ID" property="oldTakeTraderAddressId" jdbcType="INTEGER" />
    <result column="OLD_TAKE_TRADER_AREA" property="oldTakeTraderArea" jdbcType="VARCHAR" />
    <result column="OLD_TAKE_TRADER_ADDRESS" property="oldTakeTraderAddress" jdbcType="VARCHAR" />
    <result column="OLD_LOGISTICS_COMMENTS" property="oldLogisticsComments" jdbcType="VARCHAR" />
    <result column="OLD_INVOICE_TRADER_CONTACT_ID" property="oldInvoiceTraderContactId" jdbcType="INTEGER" />
    <result column="OLD_INVOICE_TRADER_CONTACT_NAME" property="oldInvoiceTraderContactName" jdbcType="VARCHAR" />
    <result column="OLD_INVOICE_TRADER_CONTACT_MOBILE" property="oldInvoiceTraderContactMobile" jdbcType="VARCHAR" />
    <result column="OLD_INVOICE_TRADER_CONTACT_TELEPHONE" property="oldInvoiceTraderContactTelephone" jdbcType="VARCHAR" />
    <result column="OLD_INVOICE_TRADER_ADDRESS_ID" property="oldInvoiceTraderAddressId" jdbcType="INTEGER" />
    <result column="OLD_INVOICE_TRADER_AREA" property="oldInvoiceTraderArea" jdbcType="VARCHAR" />
    <result column="OLD_INVOICE_TRADER_ADDRESS" property="oldInvoiceTraderAddress" jdbcType="VARCHAR" />
    <result column="OLD_INVOICE_COMMENTS" property="oldInvoiceComments" jdbcType="VARCHAR" />
    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER" />
    <result column="IS_SEND_INVOICE" property="isSendInvoice" jdbcType="BIT" />
    <result column="OLD_INVOICE_TYPE" property="oldInvoiceType" jdbcType="INTEGER" />
    <result column="OLD_IS_SEND_INVOICE" property="oldIsSendInvoice" jdbcType="BIT" />
    <result column="IS_DELAY_INVOICE" property="isDelayInvoice" jdbcType="BIT" />
    <result column="OLD_IS_DELAY_INVOICE" property="oldIsDelayInvoice" jdbcType="BIT" />
    <result column="INVOICE_METHOD" property="invoiceMethod" jdbcType="BIT" />
    <result column="OLD_INVOICE_METHOD" property="oldInvoiceMethod" jdbcType="BIT" />
    <result column="IS_WMSCANCEL" property="isWmsCancel" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />

    <result column="DELIVERY_METHOD" property="deliveryMethod" jdbcType="INTEGER" />
    <result column="DELIVERY_TYPE" property="deliveryType" jdbcType="INTEGER"/>
    <result column="OLD_DELIVERY_METHOD" property="deliveryMethod" jdbcType="INTEGER" />
    <result column="OLD_DELIVERY_TYPE" property="deliveryType" jdbcType="INTEGER"/>
    <result column="IS_SAME_ADDRESS" property="isSameAddress" jdbcType="BIT"/>
    <result column="OLD_IS_SAME_ADDRESS" property="oldIsSameAddress" jdbcType="BIT"/>
    <result column="INVOICE_SEND_NODE" property="invoiceSendNode" jdbcType="BIT"/>
    <result column="OLD_INVOICE_SEND_NODE" property="oldInvoiceSendNode" jdbcType="BIT"/>
    <result column="INVOICE_EMAIL" property="invoiceEmail" jdbcType="VARCHAR" />
    <result column="OLD_INVOICE_EMAIL" property="oldInvoiceEmail" jdbcType="VARCHAR" />
    <result column="TAKE_TRADER_AREA_ID" property="takeTraderAreaId" jdbcType="INTEGER" />
    <result column="OLD_TAKE_TRADER_AREA_ID" property="oldTakeTraderAreaId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    SALEORDER_MODIFY_APPLY_ID, SALEORDER_MODIFY_APPLY_NO, COMPANY_ID, SALEORDER_ID, VALID_STATUS, 
    VALID_TIME, TAKE_TRADER_ID, TAKE_TRADER_NAME, TAKE_TRADER_CONTACT_ID, TAKE_TRADER_CONTACT_NAME, 
    TAKE_TRADER_CONTACT_MOBILE, TAKE_TRADER_CONTACT_TELEPHONE, TAKE_TRADER_ADDRESS_ID, 
    TAKE_TRADER_AREA, TAKE_TRADER_ADDRESS, LOGISTICS_COMMENTS, INVOICE_TRADER_ID, INVOICE_TRADER_NAME, 
    INVOICE_TRADER_CONTACT_ID, INVOICE_TRADER_CONTACT_NAME, INVOICE_TRADER_CONTACT_MOBILE, 
    INVOICE_TRADER_CONTACT_TELEPHONE, INVOICE_TRADER_ADDRESS_ID, INVOICE_TRADER_AREA, 
    INVOICE_TRADER_ADDRESS, INVOICE_COMMENTS, OLD_TAKE_TRADER_CONTACT_ID, OLD_TAKE_TRADER_CONTACT_NAME, 
    OLD_TAKE_TRADER_CONTACT_MOBILE, OLD_TAKE_TRADER_CONTACT_TELEPHONE, OLD_TAKE_TRADER_ADDRESS_ID, 
    OLD_TAKE_TRADER_AREA, OLD_TAKE_TRADER_ADDRESS, OLD_LOGISTICS_COMMENTS, OLD_INVOICE_TRADER_CONTACT_ID, 
    OLD_INVOICE_TRADER_CONTACT_NAME, OLD_INVOICE_TRADER_CONTACT_MOBILE, OLD_INVOICE_TRADER_CONTACT_TELEPHONE, 
    OLD_INVOICE_TRADER_ADDRESS_ID, OLD_INVOICE_TRADER_AREA, OLD_INVOICE_TRADER_ADDRESS, 
    OLD_INVOICE_COMMENTS, ADD_TIME, CREATOR, INVOICE_TYPE, IS_SEND_INVOICE, OLD_INVOICE_TYPE, OLD_IS_SEND_INVOICE, INVOICE_METHOD,
    OLD_INVOICE_METHOD,DELIVERY_METHOD,DELIVERY_TYPE,OLD_DELIVERY_METHOD,OLD_DELIVERY_TYPE,IS_WMSCANCEL,
IS_SAME_ADDRESS,OLD_IS_SAME_ADDRESS,INVOICE_SEND_NODE,OLD_INVOICE_SEND_NODE,INVOICE_EMAIL,OLD_INVOICE_EMAIL,TAKE_TRADER_AREA_ID,OLD_TAKE_TRADER_AREA_ID
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_SALEORDER_MODIFY_APPLY
    where SALEORDER_MODIFY_APPLY_ID = #{saleorderModifyApplyId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_SALEORDER_MODIFY_APPLY
    where SALEORDER_MODIFY_APPLY_ID = #{saleorderModifyApplyId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.order.model.SaleorderModifyApply" >
    insert into T_SALEORDER_MODIFY_APPLY (SALEORDER_MODIFY_APPLY_ID, SALEORDER_MODIFY_APPLY_NO, 
      COMPANY_ID, SALEORDER_ID, VALID_STATUS, 
      VALID_TIME, TAKE_TRADER_ID, TAKE_TRADER_NAME, 
      TAKE_TRADER_CONTACT_ID, TAKE_TRADER_CONTACT_NAME, 
      TAKE_TRADER_CONTACT_MOBILE, TAKE_TRADER_CONTACT_TELEPHONE, 
      TAKE_TRADER_ADDRESS_ID, TAKE_TRADER_AREA, TAKE_TRADER_ADDRESS, 
      LOGISTICS_COMMENTS, INVOICE_TRADER_ID, INVOICE_TRADER_NAME, 
      INVOICE_TRADER_CONTACT_ID, INVOICE_TRADER_CONTACT_NAME, 
      INVOICE_TRADER_CONTACT_MOBILE, INVOICE_TRADER_CONTACT_TELEPHONE, 
      INVOICE_TRADER_ADDRESS_ID, INVOICE_TRADER_AREA, 
      INVOICE_TRADER_ADDRESS, INVOICE_COMMENTS, 
      OLD_TAKE_TRADER_CONTACT_ID, OLD_TAKE_TRADER_CONTACT_NAME, 
      OLD_TAKE_TRADER_CONTACT_MOBILE, OLD_TAKE_TRADER_CONTACT_TELEPHONE, 
      OLD_TAKE_TRADER_ADDRESS_ID, OLD_TAKE_TRADER_AREA, 
      OLD_TAKE_TRADER_ADDRESS, OLD_LOGISTICS_COMMENTS, 
      OLD_INVOICE_TRADER_CONTACT_ID, OLD_INVOICE_TRADER_CONTACT_NAME, 
      OLD_INVOICE_TRADER_CONTACT_MOBILE, OLD_INVOICE_TRADER_CONTACT_TELEPHONE, 
      OLD_INVOICE_TRADER_ADDRESS_ID, OLD_INVOICE_TRADER_AREA, 
      OLD_INVOICE_TRADER_ADDRESS, OLD_INVOICE_COMMENTS, 
      ADD_TIME, CREATOR,DELIVERY_METHOD,DELIVERY_TYPE,OLD_DELIVERY_METHOD,OLD_DELIVERY_TYPE,IS_WMSCANCEL,TAKE_TRADER_AREA_ID,OLD_TAKE_TRADER_AREA_ID)
    values (#{saleorderModifyApplyId,jdbcType=INTEGER}, #{saleorderModifyApplyNo,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=INTEGER}, #{saleorderId,jdbcType=INTEGER}, #{validStatus,jdbcType=BIT}, 
      #{validTime,jdbcType=BIGINT}, #{takeTraderId,jdbcType=INTEGER}, #{takeTraderName,jdbcType=VARCHAR}, 
      #{takeTraderContactId,jdbcType=INTEGER}, #{takeTraderContactName,jdbcType=VARCHAR}, 
      #{takeTraderContactMobile,jdbcType=VARCHAR}, #{takeTraderContactTelephone,jdbcType=VARCHAR}, 
      #{takeTraderAddressId,jdbcType=INTEGER}, #{takeTraderArea,jdbcType=VARCHAR}, #{takeTraderAddress,jdbcType=VARCHAR}, 
      #{logisticsComments,jdbcType=VARCHAR}, #{invoiceTraderId,jdbcType=INTEGER}, #{invoiceTraderName,jdbcType=VARCHAR}, 
      #{invoiceTraderContactId,jdbcType=INTEGER}, #{invoiceTraderContactName,jdbcType=VARCHAR}, 
      #{invoiceTraderContactMobile,jdbcType=VARCHAR}, #{invoiceTraderContactTelephone,jdbcType=VARCHAR}, 
      #{invoiceTraderAddressId,jdbcType=INTEGER}, #{invoiceTraderArea,jdbcType=VARCHAR}, 
      #{invoiceTraderAddress,jdbcType=VARCHAR}, #{invoiceComments,jdbcType=VARCHAR}, 
      #{oldTakeTraderContactId,jdbcType=INTEGER}, #{oldTakeTraderContactName,jdbcType=VARCHAR}, 
      #{oldTakeTraderContactMobile,jdbcType=VARCHAR}, #{oldTakeTraderContactTelephone,jdbcType=VARCHAR}, 
      #{oldTakeTraderAddressId,jdbcType=INTEGER}, #{oldTakeTraderArea,jdbcType=VARCHAR}, 
      #{oldTakeTraderAddress,jdbcType=VARCHAR}, #{oldLogisticsComments,jdbcType=VARCHAR}, 
      #{oldInvoiceTraderContactId,jdbcType=INTEGER}, #{oldInvoiceTraderContactName,jdbcType=VARCHAR}, 
      #{oldInvoiceTraderContactMobile,jdbcType=VARCHAR}, #{oldInvoiceTraderContactTelephone,jdbcType=VARCHAR}, 
      #{oldInvoiceTraderAddressId,jdbcType=INTEGER}, #{oldInvoiceTraderArea,jdbcType=VARCHAR}, 
      #{oldInvoiceTraderAddress,jdbcType=VARCHAR}, #{oldInvoiceComments,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER},#{deliveryMethod,jdbcType=INTEGER}, #{deliveryType,jdbcType=INTEGER},#{oldDeliveryMethod,jdbcType=INTEGER}, #{oldDeliveryType,jdbcType=INTEGER}, #{isWmsCancel,jdbcType=VARCHAR},#{takeTraderAreaId,jdbcType=INTEGER},#{oldTakeTraderAreaId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.order.model.SaleorderModifyApply" useGeneratedKeys="true" keyProperty="saleorderModifyApplyId">
    insert into T_SALEORDER_MODIFY_APPLY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="saleorderModifyApplyId != null" >
        SALEORDER_MODIFY_APPLY_ID,
      </if>
      <if test="saleorderModifyApplyNo != null" >
        SALEORDER_MODIFY_APPLY_NO,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="saleorderId != null" >
        SALEORDER_ID,
      </if>
      <if test="validStatus != null" >
        VALID_STATUS,
      </if>
      <if test="validTime != null" >
        VALID_TIME,
      </if>
      <if test="takeTraderId != null" >
        TAKE_TRADER_ID,
      </if>
      <if test="takeTraderName != null" >
        TAKE_TRADER_NAME,
      </if>
      <if test="takeTraderContactId != null" >
        TAKE_TRADER_CONTACT_ID,
      </if>
      <if test="takeTraderContactName != null" >
        TAKE_TRADER_CONTACT_NAME,
      </if>
      <if test="takeTraderContactMobile != null" >
        TAKE_TRADER_CONTACT_MOBILE,
      </if>
      <if test="takeTraderContactTelephone != null" >
        TAKE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="takeTraderAddressId != null" >
        TAKE_TRADER_ADDRESS_ID,
      </if>
      <if test="takeTraderArea != null" >
        TAKE_TRADER_AREA,
      </if>
      <if test="takeTraderAddress != null" >
        TAKE_TRADER_ADDRESS,
      </if>
      <if test="logisticsComments != null" >
        LOGISTICS_COMMENTS,
      </if>
      <if test="invoiceTraderId != null" >
        INVOICE_TRADER_ID,
      </if>
      <if test="invoiceTraderName != null" >
        INVOICE_TRADER_NAME,
      </if>
      <if test="invoiceTraderContactId != null" >
        INVOICE_TRADER_CONTACT_ID,
      </if>
      <if test="invoiceTraderContactName != null" >
        INVOICE_TRADER_CONTACT_NAME,
      </if>
      <if test="invoiceTraderContactMobile != null" >
        INVOICE_TRADER_CONTACT_MOBILE,
      </if>
      <if test="invoiceTraderContactTelephone != null" >
        INVOICE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="invoiceTraderAddressId != null" >
        INVOICE_TRADER_ADDRESS_ID,
      </if>
      <if test="invoiceTraderArea != null" >
        INVOICE_TRADER_AREA,
      </if>
      <if test="invoiceTraderAddress != null" >
        INVOICE_TRADER_ADDRESS,
      </if>
      <if test="invoiceComments != null" >
        INVOICE_COMMENTS,
      </if>
      <if test="oldTakeTraderContactId != null" >
        OLD_TAKE_TRADER_CONTACT_ID,
      </if>
      <if test="oldTakeTraderContactName != null" >
        OLD_TAKE_TRADER_CONTACT_NAME,
      </if>
      <if test="oldTakeTraderContactMobile != null" >
        OLD_TAKE_TRADER_CONTACT_MOBILE,
      </if>
      <if test="oldTakeTraderContactTelephone != null" >
        OLD_TAKE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="oldTakeTraderAddressId != null" >
        OLD_TAKE_TRADER_ADDRESS_ID,
      </if>
      <if test="oldTakeTraderArea != null" >
        OLD_TAKE_TRADER_AREA,
      </if>
      <if test="oldTakeTraderAddress != null" >
        OLD_TAKE_TRADER_ADDRESS,
      </if>
      <if test="oldLogisticsComments != null" >
        OLD_LOGISTICS_COMMENTS,
      </if>
      <if test="oldInvoiceTraderContactId != null" >
        OLD_INVOICE_TRADER_CONTACT_ID,
      </if>
      <if test="oldInvoiceTraderContactName != null" >
        OLD_INVOICE_TRADER_CONTACT_NAME,
      </if>
      <if test="oldInvoiceTraderContactMobile != null" >
        OLD_INVOICE_TRADER_CONTACT_MOBILE,
      </if>
      <if test="oldInvoiceTraderContactTelephone != null" >
        OLD_INVOICE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="oldInvoiceTraderAddressId != null" >
        OLD_INVOICE_TRADER_ADDRESS_ID,
      </if>
      <if test="oldInvoiceTraderArea != null" >
        OLD_INVOICE_TRADER_AREA,
      </if>
      <if test="oldInvoiceTraderAddress != null" >
        OLD_INVOICE_TRADER_ADDRESS,
      </if>
      <if test="oldInvoiceComments != null" >
        OLD_INVOICE_COMMENTS,
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE,
      </if>
      <if test="isSendInvoice != null" >
        IS_SEND_INVOICE,
      </if>
      <if test="oldInvoiceType != null" >
        OLD_INVOICE_TYPE,
      </if>
      <if test="oldIsSendInvoice != null" >
        OLD_IS_SEND_INVOICE,
      </if>
      <if test="isDelayInvoice != null" >
        IS_DELAY_INVOICE,
      </if>
      <if test="oldIsDelayInvoice != null" >
        OLD_IS_DELAY_INVOICE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="invoiceMethod != null" >
        INVOICE_METHOD,
      </if>
      <if test="oldInvoiceMethod != null" >
        OLD_INVOICE_METHOD,
      </if>
      <if test="isWmsCancel != null" >
        IS_WMSCANCEL,
      </if>
      <if test="deliveryType != null" >
        DELIVERY_TYPE,
      </if>
      <if test="deliveryMethod != null" >
        DELIVERY_METHOD,
      </if>
      <if test="oldDeliveryType != null" >
        OLD_DELIVERY_TYPE,
      </if>
      <if test="oldDeliveryMethod != null" >
        OLD_DELIVERY_METHOD,
      </if>
      <if test="deliveryClaim != null" >
        DELIVERY_CLAIM,
      </if>
      <if test="oldDeliveryClaim != null" >
        OLD_DELIVERY_CLAIM,
      </if>
      <if test="deliveryDelayTime != null" >
        DELIVERY_DELAY_TIME,
      </if>
      <if test="oldDeliveryDelayTime != null" >
        OLD_DELIVERY_DELAY_TIME,
      </if>
      <if test="modifyType != null" >
        MODIFY_TYPE,
      </if>
      <if test="relatedId != null" >
        RELATED_ID
      </if>
      <if test="isSameAddress != null" >
        IS_SAME_ADDRESS,
      </if>
      <if test="oldIsSameAddress != null" >
        OLD_IS_SAME_ADDRESS,
      </if>
      <if test="oldInvoiceSendNode != null" >
        OLD_INVOICE_SEND_NODE,
      </if>
      <if test="invoiceSendNode != null" >
        INVOICE_SEND_NODE,
      </if>
      <if test="invoiceEmail != null" >
        INVOICE_EMAIL,
      </if>
      <if test="oldInvoiceEmail != null" >
        OLD_INVOICE_EMAIL,
      </if>
      <if test="takeTraderAreaId != null" >
        TAKE_TRADER_AREA_ID,
      </if>
      <if test="oldTakeTraderAreaId != null" >
        OLD_TAKE_TRADER_AREA_ID,
      </if>
      <if test="onlinePersonId != null" >
        ONLINE_PERSON_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="saleorderModifyApplyId != null" >
        #{saleorderModifyApplyId,jdbcType=INTEGER},
      </if>
      <if test="saleorderModifyApplyNo != null" >
        #{saleorderModifyApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="saleorderId != null" >
        #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null" >
        #{validStatus,jdbcType=BIT},
      </if>
      <if test="validTime != null" >
        #{validTime,jdbcType=BIGINT},
      </if>
      <if test="takeTraderId != null" >
        #{takeTraderId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderName != null" >
        #{takeTraderName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactId != null" >
        #{takeTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderContactName != null" >
        #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactMobile != null" >
        #{takeTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null" >
        #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddressId != null" >
        #{takeTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderArea != null" >
        #{takeTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddress != null" >
        #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="logisticsComments != null" >
        #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderId != null" >
        #{invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderName != null" >
        #{invoiceTraderName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactId != null" >
        #{invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderContactName != null" >
        #{invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactMobile != null" >
        #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactTelephone != null" >
        #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddressId != null" >
        #{invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderArea != null" >
        #{invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddress != null" >
        #{invoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="invoiceComments != null" >
        #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="oldTakeTraderContactId != null" >
        #{oldTakeTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="oldTakeTraderContactName != null" >
        #{oldTakeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="oldTakeTraderContactMobile != null" >
        #{oldTakeTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="oldTakeTraderContactTelephone != null" >
        #{oldTakeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="oldTakeTraderAddressId != null" >
        #{oldTakeTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="oldTakeTraderArea != null" >
        #{oldTakeTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="oldTakeTraderAddress != null" >
        #{oldTakeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="oldLogisticsComments != null" >
        #{oldLogisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="oldInvoiceTraderContactId != null" >
        #{oldInvoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="oldInvoiceTraderContactName != null" >
        #{oldInvoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="oldInvoiceTraderContactMobile != null" >
        #{oldInvoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="oldInvoiceTraderContactTelephone != null" >
        #{oldInvoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="oldInvoiceTraderAddressId != null" >
        #{oldInvoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="oldInvoiceTraderArea != null" >
        #{oldInvoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="oldInvoiceTraderAddress != null" >
        #{oldInvoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="oldInvoiceComments != null" >
        #{oldInvoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null" >
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="isSendInvoice != null" >
        #{isSendInvoice,jdbcType=BIT},
      </if>
      <if test="oldInvoiceType != null" >
        #{oldInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="oldIsSendInvoice != null" >
        #{oldIsSendInvoice,jdbcType=BIT},
      </if>
      <if test="isDelayInvoice != null" >
        #{isDelayInvoice,jdbcType=BIT},
      </if>
      <if test="oldIsDelayInvoice != null" >
        #{oldIsDelayInvoice,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="invoiceMethod != null" >
        #{invoiceMethod,jdbcType=BIT},
      </if>
      <if test="oldInvoiceMethod != null" >
        #{oldInvoiceMethod,jdbcType=BIT},
      </if>
      <if test="isWmsCancel != null" >
        #{isWmsCancel,jdbcType=BIT},
      </if>
      <if test="deliveryType != null" >
        #{deliveryType,jdbcType=INTEGER},
      </if>
      <if test="deliveryMethod != null" >
        #{deliveryMethod,jdbcType=INTEGER},
      </if>
      <if test="oldDeliveryType != null" >
        #{oldDeliveryType,jdbcType=INTEGER},
      </if>
      <if test="oldDeliveryMethod != null" >
        #{oldDeliveryMethod,jdbcType=INTEGER},
      </if>
      <if test="deliveryClaim != null" >
        #{deliveryClaim,jdbcType=BIT},
      </if>
      <if test="oldDeliveryClaim != null" >
        #{oldDeliveryClaim,jdbcType=BIT},
      </if>
      <if test="deliveryDelayTime != null" >
        #{deliveryDelayTime,jdbcType=BIGINT},
      </if>
      <if test="oldDeliveryDelayTime != null" >
        #{oldDeliveryDelayTime,jdbcType=BIGINT},
      </if>
      <if test="modifyType != null" >
        #{modifyType,jdbcType=BIGINT},
      </if>
      <if test="relatedId != null" >
        #{relatedId,jdbcType=INTEGER}
      </if>
      <if test="isSameAddress != null" >
        #{isSameAddress,jdbcType=INTEGER},
      </if>
      <if test="oldIsSameAddress != null" >
        #{oldIsSameAddress,jdbcType=INTEGER},
      </if>
      <if test="oldInvoiceSendNode != null" >
        #{oldInvoiceSendNode,jdbcType=INTEGER},
      </if>
      <if test="invoiceSendNode != null" >
        #{invoiceSendNode,jdbcType=INTEGER},
      </if>
      <if test="invoiceEmail != null" >
        #{invoiceEmail,jdbcType=VARCHAR},
      </if>
      <if test="oldInvoiceEmail != null" >
        #{oldInvoiceEmail,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAreaId != null" >
        #{takeTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="oldTakeTraderAreaId != null" >
        #{oldTakeTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="onlinePersonId != null" >
        #{onlinePersonId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.SaleorderModifyApply" >
    update T_SALEORDER_MODIFY_APPLY
    <set >
      <if test="saleorderModifyApplyNo != null" >
        SALEORDER_MODIFY_APPLY_NO = #{saleorderModifyApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="saleorderId != null" >
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null" >
        VALID_STATUS = #{validStatus,jdbcType=BIT},
      </if>
      <if test="validTime != null" >
        VALID_TIME = #{validTime,jdbcType=BIGINT},
      </if>
      <if test="takeTraderId != null" >
        TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderName != null" >
        TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactId != null" >
        TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderContactName != null" >
        TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactMobile != null" >
        TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null" >
        TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddressId != null" >
        TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderArea != null" >
        TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddress != null" >
        TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="logisticsComments != null" >
        LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderId != null" >
        INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderName != null" >
        INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactId != null" >
        INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderContactName != null" >
        INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactMobile != null" >
        INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactTelephone != null" >
        INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddressId != null" >
        INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderArea != null" >
        INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddress != null" >
        INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="invoiceComments != null" >
        INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="oldTakeTraderContactId != null" >
        OLD_TAKE_TRADER_CONTACT_ID = #{oldTakeTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="oldTakeTraderContactName != null" >
        OLD_TAKE_TRADER_CONTACT_NAME = #{oldTakeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="oldTakeTraderContactMobile != null" >
        OLD_TAKE_TRADER_CONTACT_MOBILE = #{oldTakeTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="oldTakeTraderContactTelephone != null" >
        OLD_TAKE_TRADER_CONTACT_TELEPHONE = #{oldTakeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="oldTakeTraderAddressId != null" >
        OLD_TAKE_TRADER_ADDRESS_ID = #{oldTakeTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="oldTakeTraderArea != null" >
        OLD_TAKE_TRADER_AREA = #{oldTakeTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="oldTakeTraderAddress != null" >
        OLD_TAKE_TRADER_ADDRESS = #{oldTakeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="oldLogisticsComments != null" >
        OLD_LOGISTICS_COMMENTS = #{oldLogisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="oldInvoiceTraderContactId != null" >
        OLD_INVOICE_TRADER_CONTACT_ID = #{oldInvoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="oldInvoiceTraderContactName != null" >
        OLD_INVOICE_TRADER_CONTACT_NAME = #{oldInvoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="oldInvoiceTraderContactMobile != null" >
        OLD_INVOICE_TRADER_CONTACT_MOBILE = #{oldInvoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="oldInvoiceTraderContactTelephone != null" >
        OLD_INVOICE_TRADER_CONTACT_TELEPHONE = #{oldInvoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="oldInvoiceTraderAddressId != null" >
        OLD_INVOICE_TRADER_ADDRESS_ID = #{oldInvoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="oldInvoiceTraderArea != null" >
        OLD_INVOICE_TRADER_AREA = #{oldInvoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="oldInvoiceTraderAddress != null" >
        OLD_INVOICE_TRADER_ADDRESS = #{oldInvoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="oldInvoiceComments != null" >
        OLD_INVOICE_COMMENTS = #{oldInvoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="isSendInvoice != null" >
        OLD_INVOICE_TRADER_ADDRESS_ID = #{isSendInvoice,jdbcType=BIT},
      </if>
      <if test="oldInvoiceType != null" >
        OLD_INVOICE_TYPE = #{oldInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="oldIsSendInvoice != null" >
        OLD_INVOICE_TRADER_ADDRESS_ID = #{oldIsSendInvoice,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="invoiceMethod != null" >
        INVOICE_METHOD = #{invoiceMethod,jdbcType=BIT},
      </if>
      <if test="oldInvoiceMethod != null" >
        OLD_INVOICE_METHOD = #{oldInvoiceMethod,jdbcType=BIT},
      </if>
      <if test="isWmsCancel != null" >
        IS_WMSCANCEL = #{isWmsCancel,jdbcType=BIT},
      </if>
      <if test="deliveryType != null" >
        DELIVERY_TYPE= #{deliveryType,jdbcType=BIT},
      </if>
      <if test="deliveryMethod != null" >
        DELIVERY_METHOD= #{deliveryMethod,jdbcType=BIT},
      </if>
      <if test="oldDeliveryType != null" >
        OLD_DELIVERY_TYPE= #{oldDeliveryType,jdbcType=BIT},
      </if>
      <if test="oldDeliveryMethod != null" >
        OLD_DELIVERY_METHOD= #{oldDeliveryMethod,jdbcType=BIT},
      </if>
      <if test="deliveryClaim != null" >
        DELIVERY_CLAIM = #{deliveryClaim,jdbcType=BIT},
      </if>
      <if test="oldDeliveryClaim != null" >
        OLD_DELIVERY_CLAIM = #{oldDeliveryMethod,jdbcType=BIT},
      </if>
      <if test="deliveryDelayTime != null" >
        DELIVERY_DELAY_TIME = #{deliveryDelayTime,jdbcType=BIGINT},
      </if>
      <if test="oldDeliveryDelayTime != null" >
        OLD_DELIVERY_DELAY_TIME = #{oldDeliveryDelayTime,jdbcType=BIGINT},
      </if>
      <if test="isSameAddress != null" >
        IS_SAME_ADDRESS = #{isSameAddress,jdbcType=INTEGER},
      </if>
      <if test="oldIsSameAddress != null" >
        OLD_IS_SAME_ADDRESS = #{oldIsSameAddress,jdbcType=INTEGER},
      </if>
      <if test="oldInvoiceSendNode != null" >
        OLD_INVOICE_SEND_NODE = #{oldInvoiceSendNode,jdbcType=INTEGER},
      </if>
      <if test="invoiceSendNode != null" >
        INVOICE_SEND_NODE = #{invoiceSendNode,jdbcType=INTEGER},
      </if>
      <if test="oldInvoiceEmail != null" >
        OLD_INVOICE_EMAIL = #{oldInvoiceEmail,jdbcType=VARCHAR},
      </if>
      <if test="invoiceEmail != null" >
        INVOICE_EMAIL = #{invoiceEmail,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAreaId != null" >
        TAKE_TRADER_AREA_ID = #{takeTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="oldTakeTraderAreaId != null" >
        OLD_TAKE_TRADER_AREA_ID = #{oldTakeTraderAreaId,jdbcType=INTEGER},
      </if>
    </set>
    where SALEORDER_MODIFY_APPLY_ID = #{saleorderModifyApplyId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.order.model.SaleorderModifyApply" >
    update T_SALEORDER_MODIFY_APPLY
    set SALEORDER_MODIFY_APPLY_NO = #{saleorderModifyApplyNo,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      VALID_STATUS = #{validStatus,jdbcType=BIT},
      VALID_TIME = #{validTime,jdbcType=BIGINT},
      TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
      TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
      TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
      TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
      TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
      INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
      INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
      INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
      INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
      INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      OLD_TAKE_TRADER_CONTACT_ID = #{oldTakeTraderContactId,jdbcType=INTEGER},
      OLD_TAKE_TRADER_CONTACT_NAME = #{oldTakeTraderContactName,jdbcType=VARCHAR},
      OLD_TAKE_TRADER_CONTACT_MOBILE = #{oldTakeTraderContactMobile,jdbcType=VARCHAR},
      OLD_TAKE_TRADER_CONTACT_TELEPHONE = #{oldTakeTraderContactTelephone,jdbcType=VARCHAR},
      OLD_TAKE_TRADER_ADDRESS_ID = #{oldTakeTraderAddressId,jdbcType=INTEGER},
      OLD_TAKE_TRADER_AREA = #{oldTakeTraderArea,jdbcType=VARCHAR},
      OLD_TAKE_TRADER_ADDRESS = #{oldTakeTraderAddress,jdbcType=VARCHAR},
      OLD_LOGISTICS_COMMENTS = #{oldLogisticsComments,jdbcType=VARCHAR},
      OLD_INVOICE_TRADER_CONTACT_ID = #{oldInvoiceTraderContactId,jdbcType=INTEGER},
      OLD_INVOICE_TRADER_CONTACT_NAME = #{oldInvoiceTraderContactName,jdbcType=VARCHAR},
      OLD_INVOICE_TRADER_CONTACT_MOBILE = #{oldInvoiceTraderContactMobile,jdbcType=VARCHAR},
      OLD_INVOICE_TRADER_CONTACT_TELEPHONE = #{oldInvoiceTraderContactTelephone,jdbcType=VARCHAR},
      OLD_INVOICE_TRADER_ADDRESS_ID = #{oldInvoiceTraderAddressId,jdbcType=INTEGER},
      OLD_INVOICE_TRADER_AREA = #{oldInvoiceTraderArea,jdbcType=VARCHAR},
      OLD_INVOICE_TRADER_ADDRESS = #{oldInvoiceTraderAddress,jdbcType=VARCHAR},
      OLD_INVOICE_COMMENTS = #{oldInvoiceComments,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      DELIVERY_TYPE= #{deliveryType,jdbcType=BIT},
      DELIVERY_METHOD= #{deliveryMethod,jdbcType=BIT},
      OLD_DELIVERY_TYPE= #{oldDeliveryType,jdbcType=BIT},
      OLD_DELIVERY_METHOD= #{oldDeliveryMethod,jdbcType=BIT},
      TAKE_TRADER_AREA_ID = #{takeTraderAreaId,jdbcType=INTEGER},
      OLD_TAKE_TRADER_AREA_ID = #{oldTakeTraderAreaId,jdbcType=INTEGER}
    where SALEORDER_MODIFY_APPLY_ID = #{saleorderModifyApplyId,jdbcType=INTEGER}
  </update>

  <select id="getSaleorderModifyApply" resultMap="BaseResultMap" parameterType="java.lang.Integer">
    select
      b.*
    from T_SALEORDER_MODIFY_APPLY b
           LEFT JOIN T_VERIFIES_INFO e ON b.SALEORDER_MODIFY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_SALEORDER_MODIFY_APPLY'
    where b.SALEORDER_ID = #{saleOrderId,jdbcType=INTEGER} and RELATED_ID = #{buyOrderModifyApplyId,jdbcType=INTEGER}
    limit 1
  </select>

  <select id="getSaleorderModifyApplyInfo" resultType="com.vedeng.order.model.SaleorderModifyApply" parameterType="java.lang.Integer">
    SELECT a.*, d.VERIFY_USERNAME,d.STATUS as VERIFY_STATUS FROM T_SALEORDER_MODIFY_APPLY a
    LEFT JOIN T_VERIFIES_INFO d on a.SALEORDER_MODIFY_APPLY_ID = d.RELATE_TABLE_KEY and d.RELATE_TABLE="T_SALEORDER_MODIFY_APPLY"
    WHERE a.SALEORDER_MODIFY_APPLY_ID = #{saleorderModifyApplyId,jdbcType=INTEGER}
  </select>

  <select id="getSaleorderModifyApplyBySaleOrderId" resultType="com.vedeng.order.model.SaleorderModifyApply" parameterType="java.lang.Integer">
    select
      b.*
    from T_SALEORDER_MODIFY_APPLY b
           LEFT JOIN T_VERIFIES_INFO e ON b.SALEORDER_MODIFY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_SALEORDER_MODIFY_APPLY'
    where b.SALEORDER_ID = #{saleOrderId,jdbcType=INTEGER} and e.status = #{status,jdbcType=INTEGER}
      limit 1
  </select>

</mapper>