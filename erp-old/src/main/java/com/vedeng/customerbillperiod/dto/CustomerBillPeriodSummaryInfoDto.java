package com.vedeng.customerbillperiod.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: daniel
 * @Date: 2021/7/22 15 29
 * @Description: 客户账期简要信息
 */
@Data
public class CustomerBillPeriodSummaryInfoDto {

    /**
     * 账期类型，@CustomerBillPeriodTypeEnum
     */
    private Integer billPeriodType;

    /**
     * 有效账期的申请总额度
     */
    private BigDecimal totalAmount;

    /**
     * 账期有效期开始时间
     */
    private Long billPeriodStart;

    /**
     * 账期有效期截止时间
     */
    private Long billPeriodEnd;

    /**
     * 最近一次更新时间
     */
    private Long lastModTime;

    /**
     * 有效账期数
     */
    private Integer countOfValid;

    /**
     * 剩余可用额度
     */
    private BigDecimal availableAmount;

    /**
     * 未归还账期总额度
     */
    private BigDecimal unreturnedAmount;

    /**
     * 逾期金额
     */
    private BigDecimal overDueAmount;

    /**
     * 使用次数
     */
    private Integer countOfUsed;

    /**
     * 逾期次数
     */
    private Integer countOfOverDue;
}
