package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.common.util.DateUtil;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.dto.RiskSkuTodoDto;
import com.vedeng.todolist.dto.SupplyChainAdminTodoDtoBuild;
import com.vedeng.todolist.model.TodoList;
import com.wms.service.context.ThreadLocalContext;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 *  管理者页面-> sku的待办项
 */
@Service
public class RiskSkuTodoAdminVisitor implements TodoVisitor<SupplyChainAdminTodoDtoBuild>{

    @Resource
    private TodoListMapper todoListMapper;

    @Resource
    private UserMapper userMapper;

    private Map<Integer,List<Integer>> userIdByOrgaIdMap = new HashedMap();

    private long firstDayTimestamp;

    @Override
    public void visitor(SupplyChainAdminTodoDtoBuild todoDtoBuild) {

        firstDayTimestamp = DateUtil.getFirstDayofMonth();

        List<Organization> orgaList =  ThreadLocalContext.get("orgaList");

        Set<Integer> userIdSet = new HashSet<>();

        orgaList.stream().forEach(organization -> {
            List<Integer> userIdList = userMapper.getUserIdListByOrgId(organization.getOrgId());
            userIdSet.addAll(userIdList);
            userIdByOrgaIdMap.put(organization.getOrgId(),userIdList);
        });

        List<Integer> userIdAllList = new ArrayList<>(userIdSet);

        Map<Organization, RiskSkuTodoDto> skuTodoMap = new LinkedHashMap();

        //获取所有未处理代办
        List<TodoList> unHandlerSkuTodoList = todoListMapper.getUnHandledRiskCheckSkuTodoListByUserList(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA.getBuzSceneId(), userIdAllList);

        //获取当月处理的待办
        List<TodoList> handlerSkuTodoList = todoListMapper.getHandledRiskCheckSkuTodoListByUserList(TodoListBuzSceneEnum.RISK_CHECK_SKU_DATA.getBuzSceneId(), userIdAllList,firstDayTimestamp);

        orgaList.stream().forEach(organization -> {

            //查出属于当前部门的代办
            List<TodoList> unHandlerTodoList = unHandlerSkuTodoList.stream()
                                                                   .filter(skuTodo -> skuTodoBelongToOrga(skuTodo,organization))
                                                                   .collect(Collectors.toList());

            //查出当月已处理的待办
            List<TodoList> handlerTodoList = handlerSkuTodoList.stream()
                                                               .filter(skuTodo -> skuTodoBelongToOrga(skuTodo,organization))
                                                               .collect(Collectors.toList());

            RiskSkuTodoDto riskSkuTodoDto = handlerTodoList(organization.getOrgId(),unHandlerTodoList,handlerTodoList);

            if(riskSkuTodoDto.getSkuTodoListCount() == 0){
                return;
            }

            skuTodoMap.put(organization,riskSkuTodoDto);

        });

        todoDtoBuild.get().setSkuTodoMap(skuTodoMap);
    }


    /**
     * 判断一个sku的代办 是否属于某个组织机构
     * @param skuTodo
     * @param organization
     * @return
     */
    private boolean skuTodoBelongToOrga(TodoList skuTodo,Organization organization){
        return organization.getOrgId().equals(skuTodo.getAssistantOrgId()) || organization.getOrgId().equals(skuTodo.getManagerOrgId());
    }


    /**
     * 处理一个部门的代办
     * @param orgaId
     * @param unHandlerTodoList
     * @param handlerTodoList
     * @return
     */
    private RiskSkuTodoDto handlerTodoList(Integer orgaId,List<TodoList> unHandlerTodoList,List<TodoList> handlerTodoList) {

        RiskSkuTodoDto riskSkuTodoDto = new RiskSkuTodoDto();

        Set<Integer> noRepeatSet = unHandlerTodoList.parallelStream().map(TodoList::getBuzId).collect(Collectors.toSet());
        riskSkuTodoDto.setSkuTodoListCount(noRepeatSet.size());

        Integer riskCheckSkuTodoListGroupByOrder = unHandlerTodoList.parallelStream().map(TodoList::getBuzExtra).filter(StringUtils::isNotBlank).collect(Collectors.toSet()).size();
        riskSkuTodoDto.setSkuTodoListCountGroupByOrder(riskCheckSkuTodoListGroupByOrder);

        //本月已处理
        Integer handlerCurrentMonth = handlerTodoList.parallelStream().map(TodoList::getBuzId).collect(Collectors.toSet()).size();
        riskSkuTodoDto.setHandlerCurrentMonth(handlerCurrentMonth);

        //当月新增的已处理的代办
        int handlerCurrentMonthAnd = handlerTodoList.stream().filter(skuTodo -> skuTodo.getAddTime() > firstDayTimestamp).map(TodoList::getBuzId).collect(Collectors.toSet()).size();

        //当月新增的未处理的代办
        Integer unhandlerCurrentMonth = unHandlerTodoList.stream().filter(skuTodo -> skuTodo.getAddTime() > firstDayTimestamp).map(TodoList::getBuzId).collect(Collectors.toSet()).size();

        //本月新增代办
        riskSkuTodoDto.setAddCurrentMonth(unhandlerCurrentMonth + handlerCurrentMonthAnd);

        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(0);

        int allTodoListCurrentMonth = riskSkuTodoDto.getSkuTodoListCount() + handlerCurrentMonth;
        riskSkuTodoDto.setPercent(allTodoListCurrentMonth > 0? numberFormat.format((float)handlerCurrentMonth/(float)(allTodoListCurrentMonth) * 100) : "0");

        String userIdList = userIdByOrgaIdMap.get(orgaId).stream().map(String::valueOf).collect(Collectors.joining(","));
        riskSkuTodoDto.setUserIdList(userIdList);

        return riskSkuTodoDto;
    }

}
