
function delBrand(brandId){
	checkLogin();
	if(brandId > 0){
		layer.confirm("您是否确认删除？", {
			  btn: ['确定','取消'] //按钮
			}, function(){
				$.ajax({
					type: "POST",
					url: "./delBrand.do",
					data: {'brandId':brandId},
					dataType:'json',
					success: function(data){
						//refreshNowPageList(data);
						if(data.code == 0){
							self.location.reload();
						}else{
							layer.alert(data.message);
						}
						
					},
					error:function(data){
						if(data.status ==1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
						}
					}
				});
			});
	}
}

function exportBrand(){
	checkLogin();
	location.href = page_url + '/report/supply/exportBrandList.do?' + $("#search").serialize();
}