$(function(){
    $("select[name='province']").change(function(){
        checkLogin();
        var regionId = $(this).val();
        var $this = $(this);
        if(regionId > 0){
            $.ajax({
                type : "POST",
                url : page_url+"/system/region/getregion.do",
                data :{'regionId':regionId},
                dataType : 'json',
                success : function(data) {
                    $option = "<option value='0'>请选择</option>";
                    $.each(data.listData,function(i,n){
                        $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
                    });
                    $("select[name='city'] option:gt(0)").remove();
                    $("select[name='zone'] option:gt(0)").remove();

                    if($("#city").data('city')){
                        $("select[name='city']").html($option);
                        $("select[name='zone']").html("<option value='0'>请选择</option>");
                        $("#city").val($("#city").data('city')).trigger("change");
                        $("#city").data('city', '');
                    }else{
                        $("#zone").val("0").trigger("change");
                        $("#city").val("0").trigger("change");

                        $("select[name='city']").html($option);
                        $("select[name='zone']").html("<option value='0'>请选择</option>");
                    }


                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }else if(regionId==0){
            $("select[name='city'] option:gt(0)").remove();
            $("select[name='zone'] option:gt(0)").remove();
        }
    });

    $("select[name='city']").change(function(){
        checkLogin();
        var regionId = $(this).val();
        var $this = $(this);
        if(regionId > 0){
            $.ajax({
                type : "POST",
                url : page_url+"/system/region/getregion.do",
                data :{'regionId':regionId},
                dataType : 'json',
                success : function(data) {
                    $option = "<option value='0'>请选择</option>";
                    $.each(data.listData,function(i,n){
                        $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
                    });
                    $("select[name='zone'] option:gt(0)").remove();

                    $("select[name='zone']").html($option);

                    if($("select[name='zone']").data('zone')){
                        $("select[name='zone']").val($("select[name='zone']").data('zone'));
                        $("select[name='zone']").data('zone', '')
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }
    });
});
