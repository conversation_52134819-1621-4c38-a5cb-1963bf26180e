<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="编辑财务信息" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/customer/edit_finance.js?rnd=${resourceVersionKey}"></script>
<script src="/webjars/ezadmin/plugins/cascader/xm-select.js"></script>

<div class="formpublic">
    <div class="form-list form-tips8">
        <form method="post" action="" id="myform">
            <ul>
                <li>
                    <div class="form-tips">
                        <span>*</span>
                        <lable>银行帐号</lable>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" onkeyup="this.value=this.value.replace(/[^\d]/g,'') "
                                   onafterpaste="this.value=this.value.replace(/[^\d]/g,'')"
                                   placeholder="账号只允许输入阿拉伯数字" class="input-largest errobor" name="bankAccount"
                                   id="bankAccount" value="${traderFinance.bankAccount}" style="height: 36px"
                                   <c:if test="${isEdit}">readonly</c:if>/>
                        </div>
                    </div>
                </li>

                <li style="margin-top: 20px">
                    <div class="form-tips">
                        <span>*</span>
                        <lable>开户银行</lable>
                    </div>
                    <div class="f_left">
                        <div class="">
                            <div id="mx-select" name="bankName"></div>
                            <input type="hidden" name="bank" id="bank" value="${traderFinance.bank}">
                        </div>
                    </div>
                </li>

                <li style="margin-top: 20px">
                    <div class="form-tips">
                        <span>*</span>
                        <lable>开户行支付联行号</lable>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" class="input-largest" style="border: 0" name="bankCode" id="bankCode"
                                   readonly value="${traderFinance.bankCode}"/>
                        </div>
                    </div>
                </li>
                <li style="margin-top: 20px">
                    <div class="form-tips">

                        <lable>备注</lable>
                    </div>
                    <div class="f_left">
                        <div class="form-blanks">
                            <input type="text" class="input-largest errobor" name="comments"
                                   id="comments" value="${traderFinance.comments}" style="height: 36px"/>
                        </div>
                    </div>
                </li>
            </ul>
            <input type="hidden" name="traderFinanceId"
                   value="${traderFinance.traderFinanceId}"/> <input type="hidden"
                                                                     name="traderId" value="${traderFinance.traderId}"/>
            <input
                    type="hidden" name="traderType" id="traderType" value="2"/> <input
                type="hidden" name="beforeParams" value='${beforeParams}'>
            <div class="add-tijiao tcenter">
                <button type="submit" id="submit">提交</button>
                <button type="button" class="dele" id="close-layer">取消</button>
            </div>
        </form>

        <c:if test="${traderFinance.traderFinanceId eq null}">
			<span style="margin-top: 50px;color: #00ccff">
			温馨提示: <br>
			<div>1、如果搜索无所需结果，请线下联系上级主管，通知财务相关人员将此银行相关联行号信息补充维护！</div>
			<div>2、已交易成功的账户不可进行编辑！</div>
		    </span>
        </c:if>

        <c:if test="${traderFinance.traderFinanceId ne null}">
            <span style="margin-top: 50px; color: #00ccff">温馨提示:&nbsp;&nbsp;&nbsp;&nbsp;已交易成功的账户不可进行编辑！</span>
        </c:if>

    </div>
</div>
</body>

</html>

<script>
    var isEdit = '${isEdit}' === 'true'


    var sel = xmSelect.render({
        el: '#mx-select',
        tips: '输入选择',
        searchTips: '请输入',
        style: {
            marginLeft: '0',
            width: '398px'
        },
        disabled: isEdit,
        clickClose: true,
        radio: true,
        height: '450px',
        name: 'bankName',
        autoRow: true,
        filterable: true,
        paging: true,
        pageSize: 10,
        remoteSearch: true,
        model: {
            icon: 'hidden',
            label: {
                type: 'text'
            }
        },

        remoteMethod: function (val, cb, show) {
            //这里如果val为空, 则不触发搜索
            if (!val) {
                return cb([]);
            }
            $.ajax({
                type: "POST",
                url: "/bank/getBankByBankName.do",
                data: {'bankName': val},
                dataType: 'json',
                success: function (data) {
                    var arr = [];
                    if (data.data.length > 0) {
                        data.data.forEach(function (item) {
                            var i = {
                                name: item.bankName,
                                value: item.bankNo
                            };
                            arr.push(i)
                        });
                    }
                    cb(arr);
                },
                error: function (data) {
                    cb([]);
                }
            });
        },
        on: function (data) {
            // 联行号
            if (data.arr.length > 0) {
                $('#bankCode').val(data.arr[0].value);
                $('#bank').val(data.arr[0].name);
            }

        }
    });

    $(function () {
        var name = '${traderFinance.bank}';
        var account = '${traderFinance.bankCode}';
        if (name != '' && account != '') {
            sel.setValue([
                {name: name, value: account}
            ])
        }
    })
</script>

<style>
    xm-select {
        border: 1px solid #ccc;
    }

    .xm-icon-sousuo {
        max-height: 1px !important;
    }
</style>