package com.vedeng.erp.kingdee.task.batch;


import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.SaleOrderAfterSaleExchangeBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * sqxyx
 * 销售换货出入库推金蝶
 */
@JobHandler(value = "SaleOrderAfterSaleExchangeBatchTask")
@Component
public class SaleOrderAfterSaleExchangeBatchTask extends AbstractJobHandler {

    @Autowired
    private SaleOrderAfterSaleExchangeBatchJob batchJob;

    @Autowired
    private JobLauncher jobLauncher;
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("====================销售换货售后流程开始==================");
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = batchJob.saleOrderAfterSaleExchangeFlowJob();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("====================销售换货售后流程结束==================");
        return SUCCESS;
    }
}
