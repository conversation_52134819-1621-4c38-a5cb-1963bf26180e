package com.vedeng.goods.mapper;

import com.vedeng.goods.domain.entity.BaseCategory;
import com.vedeng.goods.dto.CategoryFrontDto;
import com.vedeng.goods.dto.CategoryQueryDto;
import com.vedeng.goods.dto.CategoryResultDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【V_BASE_CATEGORY(商品分类信息表)】的数据库操作Mapper
* @createDate 2022-03-11 11:00:54
* @Entity com.vedeng.erp.business.domain.entity.BaseCategory
*/
@Repository("tokeBaseCategoryMapper")
public interface BaseCategoryMapper {

    int deleteByPrimaryKey(Long id);

    int insert(BaseCategory record);

    int insertSelective(BaseCategory record);

    BaseCategory selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(BaseCategory record);

    int updateByPrimaryKey(BaseCategory record);

    /**
     * 寻找以及分类分类
     * @return
     */
    List<BaseCategory> findLevelCategory(@Param("isDelete") Integer isDelete, @Param("level") Integer level);

    /**
     * 查询分类
     * @param baseCategoryLevel
     * @return
     */
    List<CategoryFrontDto> selectByBaseCategoryLevel(@Param("baseCategoryLevel")Integer baseCategoryLevel);

    /**
     * 根据父id查询
     * @param parentId
     * @return
     */
    List<CategoryFrontDto> selectByParentId(@Param("parentId")Integer parentId);

    /**
     * 根据id查
     * @param baseCategoryIds
     * @return
     */
    List<BaseCategory> findByBaseCategoryIds(@Param("lists")List<Integer> baseCategoryIds);

    /**
     * 根据父级id查询
     *
     * @param parentIdList parentIdList
     * @return List<BaseCategory>
     */
    List<BaseCategory> getByParentIdList(@Param("parentIdList") List<Integer> parentIdList);
    /**
     * 根据id查 父级
     * @param baseCategoryIds
     * @return
     */
    List<BaseCategory> findParentByBaseCategoryIds(@Param("lists")List<Integer> baseCategoryIds);

    /**
     * 根据关键字查询三级分类
     * @param categoryQueryDto
     * @return
     */
    List<CategoryResultDto> queryThreeCategoryByKeyword(CategoryQueryDto categoryQueryDto);

    /**
     * 根据完整的三级分类名称获取分类ID
     * @param fullCategoryPath
     * @return
     */
    Integer getCategoryIdByFullPath(@Param("fullCategoryPath") String fullCategoryPath);

    /**
     * 根据分类ID获取完整路径名称
     *
     * @param categoryId 分类ID
     * @return 完整路径名称
     */
    String getAllLevelCategoryNameById(@Param("categoryId") Integer categoryId);

}
