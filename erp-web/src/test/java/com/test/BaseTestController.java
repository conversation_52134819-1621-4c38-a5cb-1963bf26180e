package com.test;

import com.vedeng.erp.business.web.api.BusinessLeadsApi;
import com.vedeng.system.controller.LoginController;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.mgt.SecurityManager;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.DefaultMockMvcBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import javax.servlet.Filter;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 基础测试Controller类
 * @date 2023/10/7 15:35
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml", "classpath:spring-mvc.xml"})
@Transactional(value = "transactionManager", rollbackFor = Throwable.class)
@WebAppConfiguration
public class BaseTestController {


    @Autowired
    private WebApplicationContext webApplicationContext;
    @Autowired
    BusinessLeadsApi businessLeadsApi;
    @Autowired
    LoginController loginController;

    public MockMvc mockMvc;
    public MockHttpSession session;


    @Before
    public void setUp() throws Exception {
        SecurityUtils.setSecurityManager((SecurityManager) webApplicationContext.getBean("legacySecurityManager"));
        DefaultMockMvcBuilder builder = MockMvcBuilders.webAppContextSetup(webApplicationContext);
        builder.addFilters((Filter) webApplicationContext.getBean("legacyShiroFilter"));

        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        this.session = doLogin();
    }

    /**
     * 获取用户登录的Session
     *
     * @return MockHttpSession
     * @throws Exception 异常
     */
    private MockHttpSession doLogin() throws Exception {
        MvcResult mvcResult = mockMvc.perform(post("/dologin.do")
                .param("username", "Caesar.lv")
                .param("password", "123456"))
                //.andExpect(view().name("redirect:/index.do"))
                .andReturn();
        session = (MockHttpSession) mvcResult.getRequest().getSession();
        return session;
    }


}
