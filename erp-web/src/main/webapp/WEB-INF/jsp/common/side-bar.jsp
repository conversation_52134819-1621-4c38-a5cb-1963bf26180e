<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html lang="en">

<head>
<meta charset="UTF-8">
<title>贝登CRM</title>
	<link rel="stylesheet"
		  href="${pageContext.request.contextPath}/static/fonts/font-awesome-4.7.0/css/font-awesome.min.css" media="all">

	<link rel="stylesheet"
	href="${pageContext.request.contextPath}/static/css/frame.css">
<link rel="stylesheet"
	href="${pageContext.request.contextPath}/static/css/general.css?rnd=${resourceVersionKey}">
<link rel="stylesheet"
	href="${pageContext.request.contextPath}/static/css/manage.css">
<link rel="stylesheet"
	href="${pageContext.request.contextPath}/static/css/nanoscroller.css">
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/jquery.min.js"></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/jquery.nanoscroller.js"></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>
<script type="text/javascript"
	src='${pageContext.request.contextPath}/static/js/frame.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript"
	src='${pageContext.request.contextPath}/static/js/form.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript"
	src='${pageContext.request.contextPath}/static/js/side-bar.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/common.js?rnd=${resourceVersionKey}"></script>
	<script type="text/javascript">
		function addNewFlag(obj){
			$(obj).removeClass("iconnew");
			$.ajax({
    			url:page_url + '/order/saleorder/saveReadNewFlag.do',
    			type:"POST",
    			dataType : "json",
    			async: false,
    			success:function(data)
    			{
    				if(data.code==0){
    					
    				}else{
    					
    				}
    			},
    			error:function(data){
    				if(data.status ==1001){
    					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
    				}
    			}
    		}); 
		}
	</script>
</head>
<style>
	.side-nav-content .oper{
		float:left;
		text-align: center;
		line-height:34px;
		height:25px;
		width: 20%;
	}
	.side-nav-content .oper i{
		color:white;
		font-size: medium;
		background:none;
	}
	h4{
		cursor: pointer;
	}
	.custom-icons{
		position: fixed;
		width: 100%;
		background: #3d464d;
		z-index: 11;
	}
</style>
<body class="sidebarbody">
	<div class="nano has-scrollbar">
		<div class="frame-body container content">
			<div class="side-bar">
				<div class="side-nav-content">
					<div style="height: 42px" >
						<c:if test="${empty requestScope.logoUrl}">
						<img src="${pageContext.request.contextPath}/static/images/sidebarlogo1.jpg" style="position: fixed; top: 0px; left: -4px; z-index: 22;">
						</c:if>
						<c:if test="${not empty requestScope.logoUrl}">
							<img src="${requestScope.logoUrl}" style="position: fixed; top: 0px; left: -4px; z-index: 22;">
						</c:if>

					</div>
					<div class="custom-icons" >
						<ul>
							<li   class="oper"><i class="folder fa fa-minus-square-o"></i>
							  </li>
							<li class="oper  "><a target="_blank" href="http://faq.ivedeng.com/feedback/add">
								<i class="fa fa-edit "></i>
							</a></li>
<%--							<li class="oper  "><i class="fa fa-search  "></i> </li>--%>
<%--							<li class="oper  "> <i class=" "></i></li>--%>
						</ul>
					</div>
					<ul class="side-nav" id="side-nav" style="padding-top: 25px;">
						<c:forEach items="${menuList}" var="list1" varStatus="status1">
							<c:set var="actionCount" value="0"></c:set>
							<li id="actiongroup${status1.index}">
								<h4 class="open">
									<i class="${list1.iconStyle}"></i>${list1.name}
									<ii class="fa fa-caret-up"></ii>
								</h4>
								<c:if test="${not empty list1.children}">
									<ul>
										<c:forEach items="${list1.children}" var="list2" varStatus="status2">
												<li>
													<c:if test="${list2.name eq '产品检索'}">
														<a href="javascript:void(0);" link="./${list2.link}" id="${status1.count}${status2.count}"
														   <c:if test="${isClick eq 0 && haveAd eq 1}">class="iconnew" onclick="addNewFlag(this);"</c:if>  >${list2.name}</a>
													</c:if>
													<c:if test="${list2.name ne '产品检索'}">

														<a href="javascript:void(0);" link="${list2.link}" id="${status1.count}${status2.count}">${list2.name}</a>
													</c:if>
												</li>
												<c:set var="actionCount" value="1"></c:set>
										</c:forEach>
									</ul>
								</c:if>
							</li>
							<c:if test="${actionCount eq 0}">
								<script>
									$(document).ready(function(){
										var index = ${status1.index};
										$("#actiongroup"+index).remove();
									});
								</script>
							</c:if>
						</c:forEach>
					</ul>
				</div>
			</div>
		</div>
		<div class="sidebar-bottom">
			<div class="users">
				<div class="fixedInformation">
					<div class="userinfor">
						<p class="pl7">
							<i class="iconusername"></i>
							${user.username}
						</p>
						<c:forEach items="${position }" var="posit">
							<c:if test="${posit.orgId eq user.orgId}">
								<p class="pl33" title="${posit.orgName }">${posit.orgName }</p>
								<p class="pl33" title="${posit.positionName }">${posit.positionName }</p>
							</c:if>
						</c:forEach>
					</div>
					<ul>
						<c:if test="${position.size() > 1 }">
						<li class="pop-new-data-left"
							layerParams='{"width":"800","height":"280","title":"切换职位","link":"${pageContext.request.contextPath}/changeorg.do"}'>
							<i class="iconuserdepartment"></i> 切换职位
						</li>
						</c:if>
						<li onclick="logout('${requestScope.lxcrmUrl}');"><i class="iconuserposition"></i>退出系统</li>
					</ul>
				</div>
				<div class="iconuserarrow">
					<i></i>
				</div>
			</div>
			<ul>
				<li class="pos_rel">
					<a href="javascript:void(0);" link="${pageContext.request.contextPath}/system/message/index.do" onclick="_click();"
					title="消息列表" name="消息列表"><i class="iconunews" ></i><i class="newstips" id="msg" style="display: none;"></i></a></li>
				<li class="J-im-icon"><a href="javascript:void(0);" link="${pageContext.request.contextPath}/im/base/index.do" title="IM聊天"
					name="IM_chat" id="im-trigger"><i class="iconChat"></i><i class="newstips" id="im-msg" style="display: none;"></i></a></li>
				<li><a href="javascript:void(0);"
					link="${pageContext.request.contextPath}/system/user/myinfo.do?userId=${user.userId}"
					title="个人设置" name="个人设置"><i class="iconsetting"></i></a></li>
				<li><i class="iconuser"></i></li>
			</ul>
			<input type="hidden" id="userId" value="${ sessionScope.curr_user.userId}"/>
		</div>
	</div>
	<script type="text/javascript">
var userId = $("#userId").val();
var url = "http://"+curWwwPath.substring(7, pos) + projectName+"/system/message/getMessageCount.do";

//查询当前未处理的信息
$.ajax({
	async : false,
	url : url,
	data : {
		"userId" : userId
	},
	type : "POST",
	dataType : "json",
	success : function(data) {
		if(data.code == 0){
		   if(data.data>0){
		    	 $("#msg").show();
		     }else{
		    	 $("#msg").hide();
		     } 
		}else{
			layer.alert(data.message, { icon : 2});
		}
	}
});
function _click(){
	//$(".messageContainer").empty();
	$(window.parent.document).contents().find('.messageContainer').empty();
}
$(function () {

	$(".side-bar").on("click","h4",function(){
		var ii =$(this).find("ii");
		if(ii.hasClass("fa-caret-up")){
			$(this).removeClass("open");
			$(this).next().hide()
			ii.removeClass("fa-caret-up")
			ii.addClass(" fa-caret-down")
		}else{

			$(this).addClass("open");
			$(this).next().show()
			ii.removeClass("fa-caret-down")
			ii.addClass(" fa-caret-up")
		}
	})

    $(".folder").click(function(){
			if($(this).hasClass("fa-plus-square-o")){
				$("h4").each(function(){
					if(!$(this).hasClass("open")){
						$(this).click()
					}
				})
				$(this).removeClass("fa-plus-square-o")
				$(this).addClass("  fa-minus-square-o")
			}else{
				$("h4").each(function(){
					if($(this).hasClass("open")){
						$(this).click()
					}
				})
				$(this).removeClass("fa-minus-square-o")
				$(this).addClass("fa-plus-square-o")
			}
    })

})
</script>
</body>

</html>
