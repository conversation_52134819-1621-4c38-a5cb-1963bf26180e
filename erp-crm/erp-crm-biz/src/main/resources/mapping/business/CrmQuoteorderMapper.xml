<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.crm.business.quote.mapper.CrmQuoteorderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.crm.business.quote.domain.entity.CrmQuoteOrderEntity">
    <!--@mbg.generated-->
    <!--@Table T_QUOTEORDER-->
    <id column="QUOTEORDER_ID" jdbcType="INTEGER" property="quoteorderId" />
    <result column="BUSSINESS_CHANCE_ID" jdbcType="INTEGER" property="bussinessChanceId" />
    <result column="QUOTEORDER_NO" jdbcType="VARCHAR" property="quoteorderNo" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="SOURCE" jdbcType="INTEGER" property="source" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="AREA" jdbcType="VARCHAR" property="area" />
    <result column="CUSTOMER_TYPE" jdbcType="INTEGER" property="customerType" />
    <result column="CUSTOMER_NATURE" jdbcType="INTEGER" property="customerNature" />
    <result column="IS_NEW_CUSTOMER" jdbcType="INTEGER" property="isNewCustomer" />
    <result column="CUSTOMER_LEVEL" jdbcType="VARCHAR" property="customerLevel" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="TELEPHONE" jdbcType="VARCHAR" property="telephone" />
    <result column="TRADER_ADDRESS_ID" jdbcType="INTEGER" property="traderAddressId" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="IS_POLICYMAKER" jdbcType="INTEGER" property="isPolicymaker" />
    <result column="PURCHASING_TYPE" jdbcType="INTEGER" property="purchasingType" />
    <result column="PAYMENT_TERM" jdbcType="INTEGER" property="paymentTerm" />
    <result column="PURCHASING_TIME" jdbcType="INTEGER" property="purchasingTime" />
    <result column="PROJECT_PROGRESS" jdbcType="VARCHAR" property="projectProgress" />
    <result column="FOLLOW_ORDER_STATUS" jdbcType="INTEGER" property="followOrderStatus" />
    <result column="FOLLOW_ORDER_STATUS_COMMENTS" jdbcType="VARCHAR" property="followOrderStatusComments" />
    <result column="FOLLOW_ORDER_TIME" jdbcType="BIGINT" property="followOrderTime" />
    <result column="SALES_AREA_ID" jdbcType="INTEGER" property="salesAreaId" />
    <result column="SALES_AREA" jdbcType="VARCHAR" property="salesArea" />
    <result column="TERMINAL_TRADER_ID" jdbcType="INTEGER" property="terminalTraderId" />
    <result column="TERMINAL_TRADER_NAME" jdbcType="VARCHAR" property="terminalTraderName" />
    <result column="TERMINAL_TRADER_TYPE" jdbcType="INTEGER" property="terminalTraderType" />
    <result column="PAYMENT_TYPE" jdbcType="INTEGER" property="paymentType" />
    <result column="PREPAID_AMOUNT" jdbcType="DECIMAL" property="prepaidAmount" />
    <result column="ACCOUNT_PERIOD_AMOUNT" jdbcType="DECIMAL" property="accountPeriodAmount" />
    <result column="PERIOD_DAY" jdbcType="INTEGER" property="periodDay" />
    <result column="LOGISTICS_COLLECTION" jdbcType="INTEGER" property="logisticsCollection" />
    <result column="RETAINAGE_AMOUNT" jdbcType="DECIMAL" property="retainageAmount" />
    <result column="RETAINAGE_AMOUNT_MONTH" jdbcType="INTEGER" property="retainageAmountMonth" />
    <result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus" />
    <result column="VALID_TIME" jdbcType="BIGINT" property="validTime" />
    <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount" />
    <result column="PERIOD" jdbcType="INTEGER" property="period" />
    <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType" />
    <result column="FREIGHT_DESCRIPTION" jdbcType="INTEGER" property="freightDescription" />
    <result column="ADDITIONAL_CLAUSE" jdbcType="VARCHAR" property="additionalClause" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="IS_SEND" jdbcType="INTEGER" property="isSend" />
    <result column="SEND_TIME" jdbcType="BIGINT" property="sendTime" />
    <result column="IS_REPLAY" jdbcType="INTEGER" property="isReplay" />
    <result column="REPLAY_TIME" jdbcType="BIGINT" property="replayTime" />
    <result column="REPLAY_USER_ID" jdbcType="INTEGER" property="replayUserId" />
    <result column="HAVE_COMMUNICATE" jdbcType="INTEGER" property="haveCommunicate" />
    <result column="CONSULT_STATUS" jdbcType="INTEGER" property="consultStatus" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CLOSE_REASON_ID" jdbcType="INTEGER" property="closeReasonId" />
    <result column="CLOSE_REASON_COMMENT" jdbcType="VARCHAR" property="closeReasonComment" />
    <result column="QUOTED_ALARM_MODE" jdbcType="INTEGER" property="quotedAlarmMode" />
    <result column="SALESMAN_ALARM_LEVEL" jdbcType="INTEGER" property="salesmanAlarmLevel" />
    <result column="PURCHASER_ALARM_LEVEL" jdbcType="INTEGER" property="purchaserAlarmLevel" />
    <result column="LINK_BD_STATUS" jdbcType="INTEGER" property="linkBdStatus" />
    <result column="TERMINAL_TYPE" jdbcType="INTEGER" property="terminalType" />
    <result column="ONLINE_SHARE_TIME" jdbcType="TIMESTAMP" property="onlineShareTime" />
    <result column="NEEDS_DESC" jdbcType="VARCHAR" property="needsDesc" />
    <result column="BUILD_CHAT_USER_IDS" jdbcType="VARCHAR" property="buildChatUserIds" />
    
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    QUOTEORDER_ID, BUSSINESS_CHANCE_ID, QUOTEORDER_NO, COMPANY_ID, `SOURCE`, ORG_ID,
    USER_ID, TRADER_ID, TRADER_NAME, AREA, CUSTOMER_TYPE, CUSTOMER_NATURE, IS_NEW_CUSTOMER,
    CUSTOMER_LEVEL, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, MOBILE, TELEPHONE, TRADER_ADDRESS_ID,
    ADDRESS, IS_POLICYMAKER, PURCHASING_TYPE, PAYMENT_TERM, PURCHASING_TIME, PROJECT_PROGRESS,
    FOLLOW_ORDER_STATUS, FOLLOW_ORDER_STATUS_COMMENTS, FOLLOW_ORDER_TIME, SALES_AREA_ID,
    SALES_AREA, TERMINAL_TRADER_ID, TERMINAL_TRADER_NAME, TERMINAL_TRADER_TYPE, PAYMENT_TYPE,
    PREPAID_AMOUNT, ACCOUNT_PERIOD_AMOUNT, PERIOD_DAY, LOGISTICS_COLLECTION, RETAINAGE_AMOUNT,
    RETAINAGE_AMOUNT_MONTH, VALID_STATUS, VALID_TIME, TOTAL_AMOUNT, PERIOD, INVOICE_TYPE,
    FREIGHT_DESCRIPTION, ADDITIONAL_CLAUSE, COMMENTS, IS_SEND, SEND_TIME, IS_REPLAY,
    REPLAY_TIME, REPLAY_USER_ID, HAVE_COMMUNICATE, CONSULT_STATUS, ADD_TIME, CREATOR,
    MOD_TIME, UPDATER, CLOSE_REASON_ID, CLOSE_REASON_COMMENT, QUOTED_ALARM_MODE, SALESMAN_ALARM_LEVEL,
    PURCHASER_ALARM_LEVEL, LINK_BD_STATUS, TERMINAL_TYPE, ONLINE_SHARE_TIME,IS_BUILD_CHAT,NEEDS_DESC,BUILD_CHAT_USER_IDS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_QUOTEORDER
    where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
  </select>

  <select id="selectByBusinessChanceId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_QUOTEORDER
    where BUSSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER}
  </select>

  <select id="selectQuoteOrderGoodsExportVo" resultType="com.vedeng.crm.business.quote.domain.dto.QuoteOrderGoodsExportVo">
    SELECT 
	    c.QUOTEORDER_NEEDS_ID NEED_ID,
	    a.QUOTEORDER_GOODS_ID GOODS_ID,
	    1 AS queryType,
	    d.SKU_ID,
	    c.PRODUCT_NEEDS,
	    c.NUM_NEEDS,
	    c.DISTRIBUTE_BUDGET,
	    c.TERMINAL_BUDGET,
	    c.EXTRA_NEEDS,
	    a.SKU AS skuNo,
	    a.GOODS_NAME AS skuName,
	    a.BRAND_NAME,
	    a.MODEL,
	    d.TECHNICAL_PARAMETER,
	    a.PRICE AS salePrice,
	    a.DELIVERY_CYCLE AS expectDeliveryTime,
	    a.NUM,
	    a.REPORT_STATUS,
	    a.REGISTRATION_NUMBER,
	    a.UNIT_NAME,
	    a.IMG_URL AS picture,
	    a.PARAM_CONTENT AS mainParameter
	FROM
	    T_QUOTEORDER_NEEDS c
	        LEFT JOIN
	    T_R_QUOTEORDER_NEEDS_J_GOODS b ON b.QUOTEORDER_NEEDS_ID = c.QUOTEORDER_NEEDS_ID
	        LEFT JOIN
	    T_QUOTEORDER_GOODS a ON a.QUOTEORDER_GOODS_ID = b.QUOTEORDER_GOODS_ID
	        LEFT JOIN
	    V_CORE_SKU d ON a.SKU = d.SKU_NO AND a.IS_TEMP = 0
	        AND a.IS_DELETE = 0
	WHERE
	    c.QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER} 
	    and c.IS_DELETE = 0 
    	and (a.IS_DELETE is null or a.IS_DELETE = 0)
    	and (b.IS_DELETE is null or b.IS_DELETE = 0)
	        
	UNION ALL 
	
	SELECT 
	    '' NEED_ID,
	    a.QUOTEORDER_GOODS_ID GOODS_ID,
	    2 AS queryType,
	    d.SKU_ID,
	    '' PRODUCT_NEEDS,
	    '' NUM_NEEDS,
	    '' DISTRIBUTE_BUDGET,
	    '' TERMINAL_BUDGET,
	    '' EXTRA_NEEDS,
	    a.SKU AS skuNo,
	    a.GOODS_NAME AS skuName,
	    a.BRAND_NAME,
	    a.MODEL,
	    d.TECHNICAL_PARAMETER,
	    a.PRICE AS salePrice,
	    a.DELIVERY_CYCLE AS expectDeliveryTime,
	    a.NUM,
	    a.REPORT_STATUS,
	    a.REGISTRATION_NUMBER,
	    a.UNIT_NAME,
	    a.IMG_URL AS picture,
	    a.PARAM_CONTENT AS mainParameter
	FROM
	    T_QUOTEORDER_GOODS a
	        LEFT JOIN
	    V_CORE_SKU d ON a.SKU = d.SKU_NO
	WHERE
	    a.QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER} 
	        AND a.IS_TEMP = 0
	        AND a.IS_DELETE = 0
	        AND a.QUOTEORDER_GOODS_ID NOT IN (SELECT 
	            QUOTEORDER_GOODS_ID
	        FROM
	            T_R_QUOTEORDER_NEEDS_J_GOODS
	        WHERE
	            IS_DELETE = 0)
	ORDER BY queryType ASC , NEED_ID ASC , GOODS_ID ASC

  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_QUOTEORDER
    where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" keyColumn="QUOTEORDER_ID" keyProperty="quoteorderId" parameterType="com.vedeng.crm.business.quote.domain.entity.CrmQuoteOrderEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_QUOTEORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bussinessChanceId != null">
        BUSSINESS_CHANCE_ID,
      </if>
      <if test="quoteorderNo != null">
        QUOTEORDER_NO,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="area != null">
        AREA,
      </if>
      <if test="customerType != null">
        CUSTOMER_TYPE,
      </if>
      <if test="customerNature != null">
        CUSTOMER_NATURE,
      </if>
      <if test="isNewCustomer != null">
        IS_NEW_CUSTOMER,
      </if>
      <if test="customerLevel != null">
        CUSTOMER_LEVEL,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="telephone != null">
        TELEPHONE,
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="isPolicymaker != null">
        IS_POLICYMAKER,
      </if>
      <if test="purchasingType != null">
        PURCHASING_TYPE,
      </if>
      <if test="paymentTerm != null">
        PAYMENT_TERM,
      </if>
      <if test="purchasingTime != null">
        PURCHASING_TIME,
      </if>
      <if test="projectProgress != null">
        PROJECT_PROGRESS,
      </if>
      <if test="followOrderStatus != null">
        FOLLOW_ORDER_STATUS,
      </if>
      <if test="followOrderStatusComments != null">
        FOLLOW_ORDER_STATUS_COMMENTS,
      </if>
      <if test="followOrderTime != null">
        FOLLOW_ORDER_TIME,
      </if>
      <if test="salesAreaId != null">
        SALES_AREA_ID,
      </if>
      <if test="salesArea != null">
        SALES_AREA,
      </if>
      <if test="terminalTraderId != null">
        TERMINAL_TRADER_ID,
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME,
      </if>
      <if test="terminalTraderType != null">
        TERMINAL_TRADER_TYPE,
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE,
      </if>
      <if test="prepaidAmount != null">
        PREPAID_AMOUNT,
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT,
      </if>
      <if test="periodDay != null">
        PERIOD_DAY,
      </if>
      <if test="logisticsCollection != null">
        LOGISTICS_COLLECTION,
      </if>
      <if test="retainageAmount != null">
        RETAINAGE_AMOUNT,
      </if>
      <if test="retainageAmountMonth != null">
        RETAINAGE_AMOUNT_MONTH,
      </if>
      <if test="validStatus != null">
        VALID_STATUS,
      </if>
      <if test="validTime != null">
        VALID_TIME,
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT,
      </if>
      <if test="period != null">
        PERIOD,
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE,
      </if>
      <if test="freightDescription != null">
        FREIGHT_DESCRIPTION,
      </if>
      <if test="additionalClause != null">
        ADDITIONAL_CLAUSE,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="isSend != null">
        IS_SEND,
      </if>
      <if test="sendTime != null">
        SEND_TIME,
      </if>
      <if test="isReplay != null">
        IS_REPLAY,
      </if>
      <if test="replayTime != null">
        REPLAY_TIME,
      </if>
      <if test="replayUserId != null">
        REPLAY_USER_ID,
      </if>
      <if test="haveCommunicate != null">
        HAVE_COMMUNICATE,
      </if>
      <if test="consultStatus != null">
        CONSULT_STATUS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="closeReasonId != null">
        CLOSE_REASON_ID,
      </if>
      <if test="closeReasonComment != null">
        CLOSE_REASON_COMMENT,
      </if>
      <if test="quotedAlarmMode != null">
        QUOTED_ALARM_MODE,
      </if>
      <if test="salesmanAlarmLevel != null">
        SALESMAN_ALARM_LEVEL,
      </if>
      <if test="purchaserAlarmLevel != null">
        PURCHASER_ALARM_LEVEL,
      </if>
      <if test="linkBdStatus != null">
        LINK_BD_STATUS,
      </if>
      <if test="terminalType != null">
        TERMINAL_TYPE,
      </if>
      <if test="onlineShareTime != null">
        ONLINE_SHARE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bussinessChanceId != null">
        #{bussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="quoteorderNo != null">
        #{quoteorderNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="customerType != null">
        #{customerType,jdbcType=INTEGER},
      </if>
      <if test="customerNature != null">
        #{customerNature,jdbcType=INTEGER},
      </if>
      <if test="isNewCustomer != null">
        #{isNewCustomer,jdbcType=INTEGER},
      </if>
      <if test="customerLevel != null">
        #{customerLevel,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null">
        #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="isPolicymaker != null">
        #{isPolicymaker,jdbcType=INTEGER},
      </if>
      <if test="purchasingType != null">
        #{purchasingType,jdbcType=INTEGER},
      </if>
      <if test="paymentTerm != null">
        #{paymentTerm,jdbcType=INTEGER},
      </if>
      <if test="purchasingTime != null">
        #{purchasingTime,jdbcType=INTEGER},
      </if>
      <if test="projectProgress != null">
        #{projectProgress,jdbcType=VARCHAR},
      </if>
      <if test="followOrderStatus != null">
        #{followOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="followOrderStatusComments != null">
        #{followOrderStatusComments,jdbcType=VARCHAR},
      </if>
      <if test="followOrderTime != null">
        #{followOrderTime,jdbcType=BIGINT},
      </if>
      <if test="salesAreaId != null">
        #{salesAreaId,jdbcType=INTEGER},
      </if>
      <if test="salesArea != null">
        #{salesArea,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null">
        #{terminalTraderId,jdbcType=INTEGER},
      </if>
      <if test="terminalTraderName != null">
        #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderType != null">
        #{terminalTraderType,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="prepaidAmount != null">
        #{prepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodAmount != null">
        #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null">
        #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="logisticsCollection != null">
        #{logisticsCollection,jdbcType=INTEGER},
      </if>
      <if test="retainageAmount != null">
        #{retainageAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmountMonth != null">
        #{retainageAmountMonth,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        #{validStatus,jdbcType=INTEGER},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=BIGINT},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="period != null">
        #{period,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="freightDescription != null">
        #{freightDescription,jdbcType=INTEGER},
      </if>
      <if test="additionalClause != null">
        #{additionalClause,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isSend != null">
        #{isSend,jdbcType=INTEGER},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=BIGINT},
      </if>
      <if test="isReplay != null">
        #{isReplay,jdbcType=INTEGER},
      </if>
      <if test="replayTime != null">
        #{replayTime,jdbcType=BIGINT},
      </if>
      <if test="replayUserId != null">
        #{replayUserId,jdbcType=INTEGER},
      </if>
      <if test="haveCommunicate != null">
        #{haveCommunicate,jdbcType=INTEGER},
      </if>
      <if test="consultStatus != null">
        #{consultStatus,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="closeReasonId != null">
        #{closeReasonId,jdbcType=INTEGER},
      </if>
      <if test="closeReasonComment != null">
        #{closeReasonComment,jdbcType=VARCHAR},
      </if>
      <if test="quotedAlarmMode != null">
        #{quotedAlarmMode,jdbcType=INTEGER},
      </if>
      <if test="salesmanAlarmLevel != null">
        #{salesmanAlarmLevel,jdbcType=INTEGER},
      </if>
      <if test="purchaserAlarmLevel != null">
        #{purchaserAlarmLevel,jdbcType=INTEGER},
      </if>
      <if test="linkBdStatus != null">
        #{linkBdStatus,jdbcType=INTEGER},
      </if>
      <if test="terminalType != null">
        #{terminalType,jdbcType=INTEGER},
      </if>
      <if test="onlineShareTime != null">
        #{onlineShareTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.crm.business.quote.domain.entity.CrmQuoteOrderEntity">
    <!--@mbg.generated-->
    update T_QUOTEORDER
    <set>
      <if test="bussinessChanceId != null">
        BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER},
      </if>
      <if test="quoteorderNo != null">
        QUOTEORDER_NO = #{quoteorderNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        AREA = #{area,jdbcType=VARCHAR},
      </if>
      <if test="customerType != null">
        CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
      </if>
      <if test="customerNature != null">
        CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
      </if>
      <if test="isNewCustomer != null">
        IS_NEW_CUSTOMER = #{isNewCustomer,jdbcType=INTEGER},
      </if>
      <if test="customerLevel != null">
        CUSTOMER_LEVEL = #{customerLevel,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        TELEPHONE = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="isPolicymaker != null">
        IS_POLICYMAKER = #{isPolicymaker,jdbcType=INTEGER},
      </if>
      <if test="purchasingType != null">
        PURCHASING_TYPE = #{purchasingType,jdbcType=INTEGER},
      </if>
      <if test="paymentTerm != null">
        PAYMENT_TERM = #{paymentTerm,jdbcType=INTEGER},
      </if>
      <if test="purchasingTime != null">
        PURCHASING_TIME = #{purchasingTime,jdbcType=INTEGER},
      </if>
      <if test="projectProgress != null">
        PROJECT_PROGRESS = #{projectProgress,jdbcType=VARCHAR},
      </if>
      <if test="followOrderStatus != null">
        FOLLOW_ORDER_STATUS = #{followOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="followOrderStatusComments != null">
        FOLLOW_ORDER_STATUS_COMMENTS = #{followOrderStatusComments,jdbcType=VARCHAR},
      </if>
      <if test="followOrderTime != null">
        FOLLOW_ORDER_TIME = #{followOrderTime,jdbcType=BIGINT},
      </if>
      <if test="salesAreaId != null">
        SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER},
      </if>
      <if test="salesArea != null">
        SALES_AREA = #{salesArea,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null">
        TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER},
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderType != null">
        TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="prepaidAmount != null">
        PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null">
        PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="logisticsCollection != null">
        LOGISTICS_COLLECTION = #{logisticsCollection,jdbcType=INTEGER},
      </if>
      <if test="retainageAmount != null">
        RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmountMonth != null">
        RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        VALID_STATUS = #{validStatus,jdbcType=INTEGER},
      </if>
      <if test="validTime != null">
        VALID_TIME = #{validTime,jdbcType=BIGINT},
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="period != null">
        PERIOD = #{period,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="freightDescription != null">
        FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
      </if>
      <if test="additionalClause != null">
        ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isSend != null">
        IS_SEND = #{isSend,jdbcType=INTEGER},
      </if>
      <if test="sendTime != null">
        SEND_TIME = #{sendTime,jdbcType=BIGINT},
      </if>
      <if test="isReplay != null">
        IS_REPLAY = #{isReplay,jdbcType=INTEGER},
      </if>
      <if test="replayTime != null">
        REPLAY_TIME = #{replayTime,jdbcType=BIGINT},
      </if>
      <if test="replayUserId != null">
        REPLAY_USER_ID = #{replayUserId,jdbcType=INTEGER},
      </if>
      <if test="haveCommunicate != null">
        HAVE_COMMUNICATE = #{haveCommunicate,jdbcType=INTEGER},
      </if>
      <if test="consultStatus != null">
        CONSULT_STATUS = #{consultStatus,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="closeReasonId != null">
        CLOSE_REASON_ID = #{closeReasonId,jdbcType=INTEGER},
      </if>
      <if test="closeReasonComment != null">
        CLOSE_REASON_COMMENT = #{closeReasonComment,jdbcType=VARCHAR},
      </if>
      <if test="quotedAlarmMode != null">
        QUOTED_ALARM_MODE = #{quotedAlarmMode,jdbcType=INTEGER},
      </if>
      <if test="salesmanAlarmLevel != null">
        SALESMAN_ALARM_LEVEL = #{salesmanAlarmLevel,jdbcType=INTEGER},
      </if>
      <if test="purchaserAlarmLevel != null">
        PURCHASER_ALARM_LEVEL = #{purchaserAlarmLevel,jdbcType=INTEGER},
      </if>
      <if test="linkBdStatus != null">
        LINK_BD_STATUS = #{linkBdStatus,jdbcType=INTEGER},
      </if>
      <if test="terminalType != null">
        TERMINAL_TYPE = #{terminalType,jdbcType=INTEGER},
      </if>
      <if test="onlineShareTime != null">
        ONLINE_SHARE_TIME = #{onlineShareTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isBuildChat != null">
        IS_BUILD_CHAT = #{isBuildChat,jdbcType=INTEGER},
      </if>
      <if test="needsDesc != null">
        NEEDS_DESC = #{needsDesc,jdbcType=VARCHAR},
      </if>
      <if test="buildChatUserIds != null and buildChatUserIds != ''">
        BUILD_CHAT_USER_IDS = #{buildChatUserIds,jdbcType=VARCHAR},
      </if>
    </set>
    where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.crm.business.quote.domain.entity.CrmQuoteOrderEntity">
    <!--@mbg.generated-->
    update T_QUOTEORDER
    set BUSSINESS_CHANCE_ID = #{bussinessChanceId,jdbcType=INTEGER},
      QUOTEORDER_NO = #{quoteorderNo,jdbcType=VARCHAR},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      `SOURCE` = #{source,jdbcType=INTEGER},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      USER_ID = #{userId,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      AREA = #{area,jdbcType=VARCHAR},
      CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
      CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
      IS_NEW_CUSTOMER = #{isNewCustomer,jdbcType=INTEGER},
      CUSTOMER_LEVEL = #{customerLevel,jdbcType=VARCHAR},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      TELEPHONE = #{telephone,jdbcType=VARCHAR},
      TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      ADDRESS = #{address,jdbcType=VARCHAR},
      IS_POLICYMAKER = #{isPolicymaker,jdbcType=INTEGER},
      PURCHASING_TYPE = #{purchasingType,jdbcType=INTEGER},
      PAYMENT_TERM = #{paymentTerm,jdbcType=INTEGER},
      PURCHASING_TIME = #{purchasingTime,jdbcType=INTEGER},
      PROJECT_PROGRESS = #{projectProgress,jdbcType=VARCHAR},
      FOLLOW_ORDER_STATUS = #{followOrderStatus,jdbcType=INTEGER},
      FOLLOW_ORDER_STATUS_COMMENTS = #{followOrderStatusComments,jdbcType=VARCHAR},
      FOLLOW_ORDER_TIME = #{followOrderTime,jdbcType=BIGINT},
      SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER},
      SALES_AREA = #{salesArea,jdbcType=VARCHAR},
      TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER},
      TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER},
      PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
      ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      LOGISTICS_COLLECTION = #{logisticsCollection,jdbcType=INTEGER},
      RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
      RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
      VALID_STATUS = #{validStatus,jdbcType=INTEGER},
      VALID_TIME = #{validTime,jdbcType=BIGINT},
      TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      PERIOD = #{period,jdbcType=INTEGER},
      INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
      ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      IS_SEND = #{isSend,jdbcType=INTEGER},
      SEND_TIME = #{sendTime,jdbcType=BIGINT},
      IS_REPLAY = #{isReplay,jdbcType=INTEGER},
      REPLAY_TIME = #{replayTime,jdbcType=BIGINT},
      REPLAY_USER_ID = #{replayUserId,jdbcType=INTEGER},
      HAVE_COMMUNICATE = #{haveCommunicate,jdbcType=INTEGER},
      CONSULT_STATUS = #{consultStatus,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      CLOSE_REASON_ID = #{closeReasonId,jdbcType=INTEGER},
      CLOSE_REASON_COMMENT = #{closeReasonComment,jdbcType=VARCHAR},
      QUOTED_ALARM_MODE = #{quotedAlarmMode,jdbcType=INTEGER},
      SALESMAN_ALARM_LEVEL = #{salesmanAlarmLevel,jdbcType=INTEGER},
      PURCHASER_ALARM_LEVEL = #{purchaserAlarmLevel,jdbcType=INTEGER},
      LINK_BD_STATUS = #{linkBdStatus,jdbcType=INTEGER},
      TERMINAL_TYPE = #{terminalType,jdbcType=INTEGER},
      ONLINE_SHARE_TIME = #{onlineShareTime,jdbcType=TIMESTAMP}
    where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2024-07-30-->
  <select id="findByBussinessChanceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_QUOTEORDER
        where BUSSINESS_CHANCE_ID=#{bussinessChanceId,jdbcType=INTEGER}
    </select>

  <select id="getEffectOrders" resultMap="BaseResultMap">
    select
    QUOTEORDER_ID,QUOTEORDER_NO
    from
    T_QUOTEORDER
    where
    TRADER_ID = #{traderId,jdbcType=INTEGER}
    and
    VALID_STATUS = 1
    order by
    ADD_TIME desc
    limit 1
  </select>


  <select id="getAuthorizationSum" resultType="int">
    select count(QUOTEORDER_ID) from T_AUTHORIZATION_APPLY  where QUOTEORDER_ID=#{quoteorderId,jdbcType=INTEGER} and APPLY_STATUS = #{applyStatus,jdbcType=INTEGER}
  </select>

  <select id="selectBusinessChanceByQuoteorderId"
          resultType="com.vedeng.crm.business.business.domain.entity.CrmBusinessChanceEntity">
    select
    b.BUSSINESS_CHANCE_ID,
    b.BUSSINESS_CHANCE_NO
    from
    T_QUOTEORDER a
    left join T_BUSSINESS_CHANCE b
    on a.BUSSINESS_CHANCE_ID = b.BUSSINESS_CHANCE_ID
    where a.QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
  </select>

  <select id="getQuoteShardInfoById" resultType="com.vedeng.crm.business.quote.domain.dto.CrmQuoteShareDto">
    SELECT
      A.QUOTEORDER_NO,
      A.QUOTEORDER_ID,
      T.TRADER_NAME,
      CHANCE.CHECK_TRADER_CONTACT_NAME  TRADER_CONTACT_NAME,
      CHANCE.CHECK_TRADER_CONTACT_MOBILE MOBILE,
      A.ONLINE_SHARE_TIME,
      SYS.TITLE freightDescriptionName,
      SUM(QG.PRICE * QG.NUM)  quoteMoney,
      COUNT(DISTINCT  QG.SKU) goodsCount,
      SUM(QG.NUM) totalNum
    FROM
      T_QUOTEORDER A
    LEFT JOIN T_BUSSINESS_CHANCE  CHANCE ON A.BUSSINESS_CHANCE_ID = CHANCE.BUSSINESS_CHANCE_ID
        LEFT JOIN T_TRADER T ON A.TRADER_ID=T.TRADER_ID
        LEFT JOIN T_R_TRADER_J_USER TU ON A.TRADER_ID=TU.TRADER_ID AND TU.TRADER_TYPE=1
        LEFT JOIN T_SYS_OPTION_DEFINITION SYS ON SYS.SYS_OPTION_DEFINITION_ID = A.FREIGHT_DESCRIPTION
        LEFT JOIN T_QUOTEORDER_GOODS QG ON QG.QUOTEORDER_ID=A.QUOTEORDER_ID AND QG.GOODS_ID > 0 AND QG.IS_DELETE=0
    WHERE
      A.QUOTEORDER_ID = #{quoteorderId}
    GROUP BY A.QUOTEORDER_ID
  </select>

  <select id="checkQuoteOrderSkuIfCheckStatus3" resultType="map">
    SELECT
    QG.SKU AS "skuNo",
    SKU.CHECK_STATUS AS "checkStatus"
    FROM
    T_QUOTEORDER A
    LEFT JOIN T_QUOTEORDER_GOODS QG ON QG.QUOTEORDER_ID=A.QUOTEORDER_ID AND QG.GOODS_ID > 0 AND QG.IS_DELETE=0
    LEFT JOIN V_CORE_SKU SKU ON SKU.SKU_NO=QG.SKU
    WHERE
    A.QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
    AND CHECK_STATUS NOT IN (3)
  </select>

  <select id="getQuoteWithNoSkuInfoById" resultType="java.lang.Integer">
    <![CDATA[
    SELECT
      count(1)
    FROM
      T_QUOTEORDER A
        LEFT JOIN T_QUOTEORDER_GOODS QG ON QG.QUOTEORDER_ID=A.QUOTEORDER_ID  AND QG.IS_DELETE=0
    WHERE
      A.QUOTEORDER_ID = #{quoteorderId} AND QG.GOODS_ID <=0
    ]]>
  </select>
  
  <update id="updateQuoteOrderForBuildUserIds" parameterType="com.vedeng.crm.business.quote.domain.entity.CrmQuoteOrderEntity">
    update T_QUOTEORDER
    <set>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isBuildChat != null">
        IS_BUILD_CHAT = #{isBuildChat,jdbcType=INTEGER},
      </if>
      <if test="buildChatUserIds != null and buildChatUserIds != '' ">
        BUILD_CHAT_USER_IDS = #{buildChatUserIds,jdbcType=VARCHAR},
      </if>
      <if test="buildChatUserIds == null or buildChatUserIds == '' ">
        BUILD_CHAT_USER_IDS = null,
      </if>
    </set>
    where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
  </update>


</mapper>
