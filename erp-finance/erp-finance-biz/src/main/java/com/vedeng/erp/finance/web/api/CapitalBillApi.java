package com.vedeng.erp.finance.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.finance.dto.CapitalBillDetailDto;
import com.vedeng.erp.finance.service.CapitalBillApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 交易流水
 * @date 2022/8/27 13:26
 **/
@ExceptionController
@RestController
@Slf4j
@RequestMapping("/capitalBill/api")
public class CapitalBillApi  {

    @Autowired
    private CapitalBillApiService capitalBillApiService;




    /**
     * 查询流水申请
     * @param capitalBillDetailDto
     * @return
     */
    @RequestMapping("/capitalBillData")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> capitalBillData(CapitalBillDetailDto capitalBillDetailDto,@RequestParam(required = false,defaultValue = "") String operationType) {

        return R.success(capitalBillApiService.getCapitalBillData(capitalBillDetailDto,operationType));
    }
}
