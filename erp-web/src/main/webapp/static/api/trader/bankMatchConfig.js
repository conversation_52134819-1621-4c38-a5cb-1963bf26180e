function page(data) {
    return axios({
        url: '/bankMatchConfig/page.do',
        method: 'post',
        data: data
    })
}

function detail(data) {
    return axios({
        url: '/bankMatchConfig/detail.do',
        method: 'post',
        params: data
    })
}

function save(data) {
    return axios({
        url: '/bankMatchConfig/save.do',
        method: 'post',
        data: data
    })
}

function update(data) {
    return axios({
        url: '/bankMatchConfig/update.do',
        method: 'post',
        data: data
    })
}

function deleteById(data) {
    return axios({
        url: '/bankMatchConfig/delete.do',
        method: 'post',
        params: data
    })
}