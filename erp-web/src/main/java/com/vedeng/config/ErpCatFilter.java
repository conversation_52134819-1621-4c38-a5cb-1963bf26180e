package com.vedeng.config;

import com.dianping.cat.servlet.CatFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/3/7
 */
public class ErpCatFilter extends CatFilter {

//    private Set<String> excludeUrls;
//    private Set<String> excludePrefixes = Stream.of("/static","/js") .collect(Collectors.toCollection(HashSet::new));
//
//
//    @Override
//    private void doFilter(ServletRequest var1, ServletResponse var2, Filter<PERSON>hain var3) throws IOException, ServletException;
}
