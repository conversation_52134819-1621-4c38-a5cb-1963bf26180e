package com.vedeng.infrastructure.kingdee.common.sdk.entity;


import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Data
public class AppCfg {

    @Value("${kingdee.serverUrl}")
    private String serverUrl;

    @Value("${kingdee.dCID}")
    private String dCID;

    @Value("${kingdee.lCID}")
    private int lCID = 2052;

    @Value("${kingdee.userName}")
    private String userName;

    private String pwd;

    @Value("${kingdee.appId}")
    String appId;

    @Value("${kingdee.appSecret}")
    String appSecret;

    @Value("${kingdee.orgNum}")
    String orgNum;

    String secSmartKet;

    @Value("${kingdee.connectTimeout}")
    int connectTimeout;

    @Value("${kingdee.requestTimeout}")
    int requestTimeout;

    @Value("${kingdee.stockTimeout}")
    int stockTimeout;

    String proxy;


    //@Bean
    //public AppCfg appCfg() {
    //    AppCfg appCfg = new AppCfg();
    //    appCfg.setServerUrl(serverUrl);
    //    appCfg.setdCID(dCID);
    //    appCfg.setlCID(lCID);
    //    appCfg.setUserName(userName);
    //    appCfg.setAppId(appId);
    //    appCfg.setAppSecret(appSecret);
    //    appCfg.setOrgNum(orgNum);
    //    appCfg.setConnectTimeout(connectTimeout);
    //    appCfg.setRequestTimeout(requestTimeout);
    //    appCfg.setStockTimeout(stockTimeout);
    //    return appCfg;
    //}


    public AppCfg setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
        return this;
    }


    public AppCfg setdCID(String dCID) {
        this.dCID = dCID;
        return this;
    }


    public AppCfg setlCID(int lCID) {
        this.lCID = lCID;
        return this;
    }


    public AppCfg setUserName(String userName) {
        this.userName = userName;
        return this;
    }


    public AppCfg setPwd(String pwd) {
        this.pwd = pwd;
        return this;
    }


    public AppCfg setAppId(String appId) {
        this.appId = appId;
        return this;
    }


    public AppCfg setAppSecret(String appSecret) {
        this.appSecret = appSecret;
        return this;
    }


}
