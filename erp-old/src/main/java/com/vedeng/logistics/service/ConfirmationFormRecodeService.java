package com.vedeng.logistics.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.service.BaseService;
import com.vedeng.logistics.model.ConfirmationFormRecode;
import com.vedeng.order.model.Saleorder;

import javax.servlet.http.HttpServletRequest;

/**
 *
 * <b>Description:</b><br> 物流回传确认单
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> erp
 * <br><b>PackageName:</b> com.vedeng.logistics.service
 * <br><b>ClassName:</b> BusinessWarehouseOutService
 * <br><b>Date:</b> 2022年11月9日 下午2:11:46
 */
public interface ConfirmationFormRecodeService extends BaseService {

    /**
     * 删除确认单
     * @param confirmationId
     */
    void deleteConfirmationById(Integer confirmationId);

    /**
     * 确认单审核流，确认或驳回
     */
    ResultInfo confirmationApprovedOrRejected(HttpServletRequest request, String taskId, String comment, Boolean pass);

    /**
     * 上传确认单
     * @param curr_user
     * @param confirmationFormRecode
     * @param request
     * @return
     */
    Boolean uploadConfirmationForm(CurrentUser curr_user, ConfirmationFormRecode confirmationFormRecode, HttpServletRequest request);

    /**
     * 自动上传确认单
     * @param confirmationFormRecode
     * @return
     */
    Boolean uploadConfirmationFormForAutoConfirmOrder(ConfirmationFormRecode confirmationFormRecode);

    Boolean confirmationOrderAudit(HttpServletRequest request, long timeMillis, Integer saleOrderId);

    Boolean confirmationApplicationReview(HttpServletRequest request, Integer batchId, Saleorder saleorder);

}
