.layer-content {
    font-size: 12px;
    line-height: 1.5;
    background-color: #f3f4f8;
    color: #555;
    text-align: left;
}
.layer-content .li-text {
    vertical-align: middle;
    margin-top: 3px;
}
.layer-content input[type=text], .layer-content select {
    padding: 2px 6px;
    line-height: 1.5;
}
.layer-content select {
    padding: 0;
}
.layer-content select {
    width: auto;
}
.layer-content textarea {
    height: 80px;
    width: 400px;
    line-height: 1.5;
    padding: 5px;
    font-size: 14px;
}
.layer-content input[type=radio] {
    vertical-align: middle;
    margin-right: 3px;
}
.layer-content input[type=radio] {
    padding: 0;
    border: none;
    height: 20px;
}
.layer-content input[type=text] {
    width: 200px;
}
.layer-content input[class*="input-xxs"] {
    width: 30px;
}
.layer-content input[class*="input-xs"] {
    width: 50px;
}
.layer-content input[class*="input-s"] {
    width: 70px;
}
.layer-content input[class*="input-m"] {
    width: 100px;
}
.layer-content input[class*="input-l"] {
    width: 150px;
}
.layer-content input[class*="input-xl"] {
    width: 200px;
}
.layer-content input[class*="input-xxl"] {
    width: 300px;
}
.pos1 {
    float: left;
}
.pos2 {
    float: right;
}
.table {
    width: 100%;
    padding: 6px;
    border-collapse: collapse;
    font-size: 12px;
}
.table tbody {
    padding: 10px 0;
}
.table td {
    border: none;
}
.layer-content .content-box {
    padding: 0 0 10px 10px;
}
.layer-content .clear {
    clear: both;
    height: 0;
    line-height: 0;
    padding: 0;
    font-size: 0;
}
.layer-content ul {}
.layer-content ul li {
    text-align: left;
}
.layer-content .title-bar {
    background-color: #09f;
    width: 100%;
}
.layer-content .title-bar li {
    float: left;
}
.layer-content .title-bar .layer-logo {
    float: left;
    height: 45px;
    width: 180px;
    background-image: url("../images/inner-logo.png");
    background-repeat: no-repeat;
    background-position: center 2px;
}
.layer-content .title-bar .button-bar {
    float: right;
    width: 100%;
}
.layer-content .title-bar .button-bar2 {
    width: 620px;
    margin-top: 2px;
}
.layer-content .button-bar .button {
   /* padding: 5px 13px*/
    display: inline-block;
    _zoom: 1;
    *display: inline;
    _display: inline;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    
}
.layer-content .button-bar .button, .layer-content .button-bar .button a {
    color: white;
}

.layer-content .button-bar .button7 {
    background-color: #e9f6fe;
    color: #09f;
}
.layer-content .button-bar .button-between1 {
    margin-left: 10px;
}
.layer-content .button-bar .button1-pos {
    float: left;
    margin: 10px 0;
}
.layer-content .button-bar .button2-pos {
    float: right;
    color: white;
    margin: 10px 0;
}
.layer-content .menu-bar {
    float: left;
    width: 180px;
}
.layer-content .menu-bar td {
    padding: 0;
}
.layer-content .menu-bar .menu-item {
    width: 100%;
    margin: 10px;
  
    padding: 5px 0;
    background-color: white;
}
.layer-content .menu-bar .menu-item .title {
  
    line-height: 2;
    font-weight: bold;
    text-align: left;
    padding: 0 10px;
}
.layer-content .menu-bar ul.quee, .layer-content .menu-bar div.quee {
    overflow-y: auto;
    overflow-x: hidden;
    height: 165px;
}
.layer-content .menu-bar ul li {
    padding: 3px 10px;
}
.layer-content .menu-bar ul.list li:hover {
    background-color: #eaf7ff;
    color: #09f;
}
.layer-content .menu-bar ul li icon {
    display: inline-block;
    padding: 3px;
    line-height: normal;
    min-width: 10px;
    text-align: center;
    vertical-align: text-bottom;
    margin-right: 4px;
    font-size: 10px;
    border-radius: 5px;
}
.layer-content .menu-bar ul li .icon1 {
    background-color: #09F;
    color: white;
}
.layer-content .menu-bar ul li .icon2 {
    background-color: #EEE;
}
.layer-content .menu-bar .column {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    cursor: pointer;
}
.layer-content .menu-content .table tr:hover {
    background-color: #eaf7ff;
    color: #09f;
}
.layer-content .menu-bar .user {
    padding-left: 5px;
    width: 45px;
}
.layer-content .menu-bar .team {
    width: 85px;
}
.layer-content .menu-bar .callicon {
    margin-right: 0;
    padding-right: 5px;
}
.layer-content .menu-bar .callicon img {
    vertical-align: middle;
}
.layer-content .content-colum {
    float: right;
    width: 620px;
    height: 480px;
    overflow-y: auto;
    overflow-x: hidden;
}
.layer-content .content-colum2 {
    width: 100%;
    float: none;
}
.layer-content .content-colum .current-info {
  
    margin-top: 10px;
    margin-right: 10px;
    background-color: #fefbea;
    padding: 5px;
}
.layer-content .button-bar .current-info .callin, .layer-content .content-colum .current-info .callin {
    vertical-align: top;
    padding-left: 10px;
}
.layer-content .button-bar .current-info .callnum, .layer-content .content-colum .current-info .callnum {
    font-size: 18px;
    color: white;
    ma
}
.layer-content .content-colum .current-info .callnum {
    color: #666;
}
.layer-content .button-bar .current-info ul li, .layer-content .content-colum .current-info ul li {
    float: left;
    margin-left: 5px;
    vertical-align: middle;
}
.layer-content .button-bar .current-info ul.area, .layer-content .content-colum .current-info ul.area {
    line-height: 38px;
    margin-right: 10px;
    color: white;
}
.layer-content .content-colum .current-info ul.area {
    color: #666;
}
.layer-content .content-colum .content-item {
    margin: 10px 10px 0 0;
    background-color: white;
}
.layer-content .content-colum .content-item .title-main {
    border: 1px solid #E6E6E6;
    border-bottom: none;
    background-color: #f6f6f6;
    padding: 5px 10px;
    text-align: center;
    font-weight: bold;
    line-height: 2;
}
.layer-content .content-colum .content-item .title {
    border: 1px solid #E6E6E6;
    border-bottom: none;
    padding: 5px 10px;
    font-weight: bold;
}
.layer-content .content-colum .content-item .title-inner {
    border-top: none;
}
.layer-content .content-colum .content-item .td-title {
    border-top: 1px #e6e6e6 solid;
    border-bottom: 1px #e6e6e6 solid;
}
.layer-content .content-colum .table-td-border1 td {
    padding: 5px;
    text-align: center;
    border: 1px solid #E6E6E6;
    line-height: 1.5;
}
.layer-content .content-colum .table-td-border1 .table-header, .layer-content .content-colum .table-td-border1 .date-column {
    white-space: nowrap;
}
.layer-content .content-colum .table-td-border1 td.value {
    color: #09F;
}
.layer-content .content-colum .table-td-border2 {
    border: 1px solid #E6E6E6;
    padding: 10px;
}
.layer-content .content-colum .table-td-border2 td {
    border: none;
    text-align: left;
}
.layer-content .content-colum .table-td-border2 td.td-name {
    width: 100px;
    text-align: right;
}
.layer-content .content-colum .table-td-border2 ul li {
    float: left;
    margin-left: 5px;
    margin-right: 15px;
}
.layer-content.call-panel {
    font-size: 12px;
    background-color: white;
    behavior: url(/PIE.htc);
    box-sizing: content-box;
    border-right:1px solid #ddd;
}
.layer-content.call-panel {
    font-size: 12px;
    margin-top: 0px;
}
.call-panel .call-queue {
    border-right: 1px #DDD solid;
    width: 257px;
    float: left;
}
.call-panel .panel-content {
    float: left;
    width: calc(100% - 257px);
}
.call-panel .title-bar {
    background-color: white;
}
.call-panel input[type=text], .call-panel select {
    padding: 1px 6px;
    height: 24px;
    line-height: 1;
}
.call-panel select {
    padding: 0;
    height: inherit;
    line-height: normal;
}
.call-panel .button-bar .button.icon {
    padding-left: 28px;
    background-repeat: no-repeat;
    background-position: 5px;
}
.call-panel .button-bar .only-text {
    padding-top: 1px;
}
.call-panel .button-bar .button1-pos {
    margin: 6px 0 0 8px;
}
.call-panel .button-bar .button2-pos {
    color: white;
    margin: 6px 0;
}
.call-panel .button-bar .button.icon1 {
    background-image: url(../images/panel-call.png);
}
.call-panel .button-bar .button.icon2 {
    background-image: url(../images/panel-consultation.png);
}
.call-panel .button-bar .button.icon3 {
    background-image: url(../images/panel-end.png);
}
.call-panel .button-bar .button.icon4 {
    background-image: url(../images/panel-headset.png);
}
.call-panel .button-bar .button.icon5 {
    background-image: url(../images/panel-keep.png);
}
.call-panel .button-bar .button.icon6 {
    background-image: url(../images/panel-transfer.png);
}
.call-panel .button-bar .button.icon7 {
    background-image: url(../images/panel-complete.png);
}
.call-panel .button-bar .button.icon8 {
    background-image: url(../images/panel-cancel.png);
}
.call-panel .button-bar .button.icon6-1 {
    background-image: url(../images/panel-transfer-disabled.png);
}
.call-panel .panel-message {
    padding: 5px 15px 5px 8px;
    background-color: #e9f6fe;
    margin-top: 6px;
    height: 41px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 3px;
}
.panel-message li{
    margin-right: 14px;
}
.call-panel .value {
    color: #09f;
}
.call-panel .value2 {
    color: #ff6600;
}
.call-panel .table {
    font-size: 12px;
}
.call-panel .call-queue .call-queue-list-title {
    text-align: center;
    float: left;
    width: 20px;
    height: 75px;
    line-height: 1;
    padding-top: 11px;
    background-color: #e9f6fe;
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
    color: #3384ef;
}
.call-panel .call-queue .call-queue-list {
    float: right;
    width: 236px;
    overflow: auto;
    overflow-x: hidden;
    display: block;
    height: 75px;
    padding-top: 3px;
   color: #3384ef;
   padding-top:3px;
}
.call-panel .call-queue .call-queue-list li {
    padding: 1px 10px;
}

.agent-list {
    width: 600px;
    background-color: white;
}
.agent-list .title-bar {
    box-sizing: content-box;
    background-color: #f6f6f6;
    padding: 6px 15px;
    width: auto;
    border-bottom: 1px #DDD solid;
}
.agent-list .title-bar .title-text {
    font-size: 14px;
    font-weight: bold;
}
.agent-list .menu-bar {
    width: 85px;
    background-color: #e9f6fe;
    height: 400px;
}
.agent-list .menu-bar .menu-item {
    padding: 0;
    margin: 0;
    border: none;
    background-color: transparent;
}
.agent-list .menu-bar .menu-item li {
    padding-left: 3px;
    text-align: center;
    line-height: 1.8;
}
.agent-list .menu-bar .menu-item li.selected {
    border-left: 3px #09f solid;
    background-color: #c7e9fe;
    color: #09F;
    padding-left: 0;
    background-image: url(../images/menu-selected.png);
    background-repeat: no-repeat;
    background-position: right;
}
.agent-list .menu-bar .menu-item li:hover {
    cursor: pointer;
}
.agent-list .agent-content-box {
    float: right;
}
.agent-list .agent-content-box .content-list {
    width: 495px;
    height: 325px;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 10px;
}
.agent-list .agent-content-box .table-td-border1 td {
    padding: 5px;
    text-align: center;
    border: 1px solid #E6E6E6;
    line-height: 1.5;
}
.agent-list .agent-content-box .table-td-border1 .table-header td {
    font-weight: bold;
}
.agent-list .agent-content-box .table-td-border1 .table-header, .agent-list .agent-content-box .table-td-border1 .date-column {
    white-space: nowrap;
}
.agent-list .agent-content-box .table-td-border1 td.value {
    color: #09F;
}
.agent-list .agent-content-box .table-td-border1 tbody tr:hover {
    background-color: #c7e9fe;
    color: #09f;
    cursor: pointer;
}
.agent-list .agent-content-box .table-td-border1 tbody tr.selected {
    background-color: #09f;
    color: white;
}
.agent-list .agent-content-box .table-td-border2 {
    border: 1px solid #E6E6E6;
    padding: 10px;
}
.agent-list .agent-content-box .table-td-border2 td {
    border: none;
    text-align: left;
}
.agent-list .agent-content-box .table-td-border2 td.td-name {
    width: 100px;
    text-align: right;
}
.agent-list .agent-content-box .table-td-border2 ul li {
    float: left;
    margin-left: 5px;
    margin-right: 15px;
}
.agent-list .content-bottom {
    margin: 8px 10px;
    border-top: 1px solid #ddd;
}
.agent-list .button-bar .search-content {
    height: 18px;
    border-right: none;
}
.agent-list .button-bar .search-button {
    height: 20px;
    position: absolute;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
.agent-list .button-bar .float-pix {
    margin-left: 70px;
}
.agent-list .button-bar .float-pix-text {
    margin-top: 2px;
}
.agent-list .value2 {
    color: #f60;
}
