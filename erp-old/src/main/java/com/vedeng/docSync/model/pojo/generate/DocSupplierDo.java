package com.vedeng.docSync.model.pojo.generate;

import java.io.Serializable;
import lombok.Data;

/**
 * T_DOC_SUPPLIER
 * <AUTHOR>
@Data
public class DocSupplierDo implements Serializable {
    private Integer id;

    /**
     * 生产厂商ID
     */
    private Integer supplierId;

    /**
     * 生产厂商名称
     */
    private String supplierName;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 是否禁用
     */
    private Integer disable;

    /**
     * 是否删除
     */
    private Integer isDelete;

    private static final long serialVersionUID = 1L;
}