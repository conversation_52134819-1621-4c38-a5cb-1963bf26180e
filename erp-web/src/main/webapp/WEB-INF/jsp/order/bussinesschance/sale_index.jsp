<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="商机列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<c:if test="${not empty method && method == 'bussinesschance' }">
	<%@ include file="../../trader/customer/customer_tag.jsp"%>
</c:if>
<style>
.customer{
	margin-left: 10px;
}

</style>
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<div class="customer">
    <ul>
        <li>
            <c:choose>
			   <c:when test="${not empty method && method == 'bussinesschance'}">
			         <a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=-1&isRest=1&traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"
			   </c:when>
			   <c:otherwise>
			         <a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=-1&isRest=1"
			   </c:otherwise>
			</c:choose>
            <c:if test="${ (empty bussinessChanceVo.status)  or (bussinessChanceVo.status=='-1') }">class="customer-color"</c:if>>全部</a>
        </li>
        <li>
            <c:choose>
			   <c:when test="${not empty method && method == 'bussinesschance'}">
			         <a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=0&isRest=1&traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"
			   </c:when>
			   <c:otherwise>
			         <a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=0&isRest=1"
			   </c:otherwise>
			</c:choose>
            <c:if test="${bussinessChanceVo.status=='0' }">class="customer-color"</c:if>>未处理</a>
        </li>
		<li>
            <c:choose>
			   <c:when test="${not empty method && method == 'bussinesschance'}">
			         <a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=6&isRest=1&traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"
			   </c:when>
			   <c:otherwise>
			         <a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=6&isRest=1"
			   </c:otherwise>
			</c:choose>
            <c:if test="${bussinessChanceVo.status=='6' }">class="customer-color"</c:if>>处理中</a>
        </li>
		<li>
			<c:choose>
			<c:when test="${not empty method && method == 'bussinesschance'}">
				<a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=1&isRest=1&traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"
			</c:when>
			<c:otherwise>
			<a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=1&isRest=1"
			</c:otherwise>
			</c:choose>
			   <c:if test="${bussinessChanceVo.status=='1' }">class="customer-color"</c:if>>报价中</a>
		</li>
        <li>
            <c:choose>
			   <c:when test="${not empty method && method == 'bussinesschance'}">
			         <a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=2&isRest=1&traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"
			   </c:when>
			   <c:otherwise>
			         <a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=2&isRest=1"
			   </c:otherwise>
			</c:choose>
            <c:if test="${bussinessChanceVo.status=='2' }">class="customer-color"</c:if>>已报价</a>
        </li>

        <li>
            <c:choose>
			   <c:when test="${not empty method && method == 'bussinesschance'}">
			         <a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=3&isRest=1&traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"
			   </c:when>
			   <c:otherwise>
			         <a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=3&isRest=1"
			   </c:otherwise>
			</c:choose>
            <c:if test="${bussinessChanceVo.status=='3' }">class="customer-color"</c:if>>已订单</a>
        </li>
		<li>
            <c:choose>
			   <c:when test="${not empty method && method == 'bussinesschance'}">
			         <a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=7&isRest=1&traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"
			   </c:when>
			   <c:otherwise>
			         <a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=7&isRest=1"
			   </c:otherwise>
			</c:choose>
            <c:if test="${bussinessChanceVo.status=='7' }">class="customer-color"</c:if>>已成单</a>
        </li>
        <li>
            <c:choose>
			   <c:when test="${not empty method && method == 'bussinesschance'}">
			         <a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=4&isRest=1&traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"
			   </c:when>
			   <c:otherwise>
			         <a href="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do?status=4&isRest=1"
			   </c:otherwise>
			</c:choose>
            <c:if test="${bussinessChanceVo.status=='4' }">class="customer-color"</c:if>>已关闭</a>
        </li>
    </ul>
</div>
<div class="searchfunc" <c:if test="${not empty method}">style="margin-top: -10px;"</c:if>>
	<form method="post"
		action="${pageContext.request.contextPath}/order/bussinesschance/saleindex.do"
		id="search">
		<ul>
			<li><label class="searchItem infor_name">商机编号</label> <input type="text"
				class="input-middle" name="bussinessChanceNo" id="bussinessChanceNo"
				value="${bussinessChanceVo.bussinessChanceNo }" /></li>


			<li>
				<label class="infor_name">商机类型</label>
						<select class="wid16 selector" name="newType" id="newType">
							<option value="0">全部</option>
							<c:if test="${not empty typeList }">
								<c:forEach items="${typeList }" var="prov">
									<option value="${prov.sysOptionDefinitionId }" <c:if test="${bussinessChanceVo.newType==prov.sysOptionDefinitionId}">selected="selected"</c:if>>${prov.title }</option>
								</c:forEach>
							</c:if>
						</select>
					</li>
					<li>
						<label class="infor_name">询价行为</label>
						<select class="wid16 selector" name="inquiry" id="inquiry">
							<option value="0">全部</option>
							<c:if test="${not empty inquiryList }">
								<c:forEach items="${inquiryList }" var="cy">
									<option value="${cy.sysOptionDefinitionId }" <c:if test="${bussinessChanceVo.inquiry==cy.sysOptionDefinitionId}">selected="selected"</c:if>>${cy.title }</option>
								</c:forEach>
							</c:if>
						</select>
					</li>

			<li>
				<label class="infor_name" style="width: 208px">渠道类型-渠道名称</label>
				<ul class="inputfloat f_left">
					<li>
						<select class="wid16 selector" name="newSource" id="newSource" >
							<option value="0">全部</option>
							<c:if test="${not empty newSourceList }">
								<c:forEach items="${newSourceList }" var="zo">
									<option value="${zo.sysOptionDefinitionId }" <c:if test="${bussinessChanceVo.newSource==zo.sysOptionDefinitionId}">selected="selected"</c:if>>${zo.title }</option>
								</c:forEach>
							</c:if>
						</select>
					</li>
					<li>
						<select class="wid16 selector" name="newCommunication" id="newCommunication" >
							<option value="0">全部</option>
							<c:if test="${not empty newCommunicationList }">
								<c:forEach items="${newCommunicationList }" var="zo">
									<option value="${zo.sysOptionDefinitionId }" <c:if test="${bussinessChanceVo.newCommunication==zo.sysOptionDefinitionId}">selected="selected"</c:if>>${zo.title }</option>
								</c:forEach>
							</c:if>
						</select>
					</li>
				</ul>
			</li>

			<li>
				<label class="searchItem infor_name">咨询入口</label>
				<select class="input-middle" name="entrances" style="">
					<option value="0">全部</option>
					<c:forEach items="${entrancesList }" var="entrances">
						<option value="${entrances.sysOptionDefinitionId }"
								<c:if test="${entrances.sysOptionDefinitionId == bussinessChanceVo.entrances}">selected="selected"</c:if>>${entrances.title }</option>
					</c:forEach>
				</select>
			</li>

		</ul>
		<div style="margin-top: -7px;display: inline-block;">
			<ul>

				<li><label class="searchItem infor_name">分配结果</label>
					<select class="input-middle" name="assignStatus" style="    width: 114px;">
						<option value="-1">全部</option>
						<option value="0"
								<c:if test="${bussinessChanceVo.assignStatus == 0}">selected="selected"</c:if>>未分配</option>
						<option value="1"
								<c:if test="${bussinessChanceVo.assignStatus == 1}">selected="selected"</c:if>>已分配</option>
					</select>
				</li>

				<li>
					<label class="searchItem infor_name">功能</label>
					<select class="input-middle" name="functions">
						<option value="0">全部</option>
						<c:forEach items="${functionsList }" var="functions">
							<option value="${functions.sysOptionDefinitionId }"
									<c:if test="${functions.sysOptionDefinitionId == bussinessChanceVo.functions}">selected="selected"</c:if>>${functions.title }</option>
						</c:forEach>
					</select>
				</li>

				<li><label class="searchItem infor_name">客户分类</label>
					<select class="input-middle" name="traderCategory">
						<option value="-1">全部</option>
						<option value="1"
								<c:if test="${bussinessChanceVo.isNew == 1}">selected="selected"</c:if>>新商机
						</option>

					</select>
				</li>

				<li><label class="searchItem infor_name">商机等级</label> <select
						class="input-middle" name="bussinessLevel">
					<option value="0">全部</option>
					<c:forEach items="${bussinessLevelList }" var="bussinessLevel">
						<option value="${bussinessLevel.sysOptionDefinitionId }"
								<c:if test="${bussinessLevel.sysOptionDefinitionId == bussinessChanceVo.bussinessLevel}">selected="selected"</c:if>>${bussinessLevel.title }</option>
					</c:forEach>
				</select></li>

				<li><label class="searchItem infor_name">商机阶段</label> <select
						class="input-middle" name="bussinessStage">
					<option value="">全部</option>
					<c:forEach items="${bussinessStageList }" var="bussinessStage">
						<option value="${bussinessStage.sysOptionDefinitionId }"
								<c:if test="${bussinessStage.sysOptionDefinitionId == bussinessChanceVo.bussinessStage}">selected="selected"</c:if>>${bussinessStage.title }</option>
					</c:forEach>
				</select></li>
				<li><label class="searchItem infor_name">成单几率</label> <select
						class="input-middle" name="orderRate">
					<option value="">全部</option>
					<c:forEach items="${orderRateList }" var="orderRate">
						<option value="${orderRate.sysOptionDefinitionId }"
								<c:if test="${orderRate.sysOptionDefinitionId == bussinessChanceVo.orderRate}">selected="selected"</c:if>>${orderRate.title }</option>
					</c:forEach>
				</select></li>
				<li><label class="searchItem infor_name">已成单未结款</label> <select
						class="input-middle" name="isPayment">
					<option value="">全部</option>
					<option value="1" <c:if test="${bussinessChanceVo.isPayment==1}">selected="selected"</c:if>>是</option>
					<option value="2" <c:if test="${bussinessChanceVo.isPayment==2}">selected="selected"</c:if>>否</option>
				</select></li>
				<li><label class="searchItem infor_name">产品名称</label> <input type="text"
																			 class="input-middle" name="goodsName" id="goodsName"
																			 value="${bussinessChanceVo.goodsName }" /></li>
				<c:if test="${empty method}">
					<li><label class="searchItem infor_name">客户名称</label> <input type="text"
																				 class="input-middle" name="traderName" id="traderName"
																				 value="${bussinessChanceVo.traderName }" /></li>
				</c:if>
				<li><label class="searchItem infor_name">联系方式</label> <input type="text"
																			 class="input-middle" name="traderContactName" id="traderContactName"
																			 value="${bussinessChanceVo.traderContactName }"
																			 placeholder="联系人/手机号/电话/其他联系方式" /></li>
				<li><label class="searchItem infor_name">客户性质</label> <select
						class="input-middle" name="customerNature">
					<option value="">全部</option>
					<option value="465"
							<c:if test="${bussinessChanceVo.customerNature == 465}">selected="selected"</c:if>>分销</option>
					<option value="466"
							<c:if test="${bussinessChanceVo.customerNature == 466}">selected="selected"</c:if>>终端</option>
				</select></li>
				<li><label class="searchItem infor_name">询价产品</label> <input type="text"
																			 class="input-middle" name="content" id="content"
																			 value="${bussinessChanceVo.content }" /></li>
				<li><label class="searchItem infor_name">报价单号</label> <input type="text"
																			 class="input-middle" name="quoteorderNo" id="quoteorderNo"
																			 value="${bussinessChanceVo.quoteorderNo }" /></li>
				<li><label class="searchItem infor_name">订单号</label> <input type="text"
																			class="input-middle" name="saleorderNo" id="saleorderNo"
																			value="${bussinessChanceVo.saleorderNo }" /></li>
				<c:if test="${empty method}">
					<li><label class="searchItem infor_name">归属销售</label> <select
							class="input-middle" name="userId">
						<option value="0">全部</option>
						<c:if test="${not empty userList }">
							<c:forEach items="${userList }" var="user">
								<option value="${user.userId }"
										<c:if test="${bussinessChanceVo.userId eq user.userId }">selected="selected"</c:if>>${user.username }</option>
							</c:forEach>
						</c:if>
					</select></li>
				</c:if>
				<li>
					<label class="searchItem infor_name">预计成单时间</label>
					<input class="Wdate f_left input-smaller96 m0" type="text"
						   placeholder="请选择日期"
						   onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'cdendtime\')}'})" autocomplete="off"
						   name="cdstarttime" id="cdstarttime"
						   value="${bussinessChanceVo.cdstarttime }">
					<div class="f_left ml1 mr1 mt4">-</div> <input
						class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期"
						onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'cdstarttime\')}'})" autocomplete="off"
						name="cdendtime" id="cdendtime" value="${bussinessChanceVo.cdendtime }">
				</li>
				<li>
					<div class="infor_name specialinfor">
						<select name="timeType">
							<option value="1"
									<c:if test="${bussinessChanceVo.timeType == 1 }">selected="selected"</c:if>>创建时间</option>
							<option value="2"
									<c:if test="${bussinessChanceVo.timeType == 2 }">selected="selected"</c:if>>分配时间</option>
							<option value="3"
									<c:if test="${bussinessChanceVo.timeType == 3 }">selected="selected"</c:if>>报价时间</option>
							<option value="4"
									<c:if test="${bussinessChanceVo.timeType == 4 }">selected="selected"</c:if>>订单时间</option>
							<option value="5"
									<c:if test="${bussinessChanceVo.timeType == 5 }">selected="selected"</c:if>>关闭时间</option>
						</select>
					</div> <input class="Wdate f_left input-smaller96 m0" type="text"
								  placeholder="请选择日期"
								  onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endtime\')}'})" autocomplete="off"
								  name="starttime" id="starttime"
								  value="${bussinessChanceVo.starttime }">
					<div class="f_left ml1 mr1 mt4">-</div> <input
						class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期"
						onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'starttime\')}'})" autocomplete="off"
						name="endtime" id="endtime" value="${bussinessChanceVo.endtime }">
				</li>
				<li>
					<label class="searchItem infor_name">下次沟通时间</label>
					<input class="Wdate f_left input-smaller96 m0" type="text"
						   placeholder="请选择日期"
						   onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'nextEndTime\')}'})" autocomplete="off"
						   name="nextStartTime" id="nextStartTime"
						   value="${bussinessChanceVo.nextStartTime }">
					<div class="f_left ml1 mr1 mt4">-</div> <input
						class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期"
						onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'nextStartTime\')}'})" autocomplete="off"
						name="nextEndTime" id="nextEndTime" value="${bussinessChanceVo.nextEndTime }">
				</li>
				<c:if test="${empty method}">
					<li><label class="searchItem infor_name">地区</label> <select
							name="province">
						<option value="0">请选择</option>
						<c:if test="${not empty provinceList }">
							<c:forEach items="${provinceList }" var="province">
								<option value="${province.regionId }"
										<c:if test="${ not empty bussinessChanceVo &&  province.regionId == bussinessChanceVo.province }">selected="selected"</c:if>>${province.regionName }</option>
							</c:forEach>
						</c:if>
					</select> <select name="city">
						<option value="0">请选择</option>
						<c:if test="${not empty cityList }">
							<c:forEach items="${cityList }" var="city">
								<option value="${city.regionId }"
										<c:if test="${ not empty bussinessChanceVo &&  city.regionId == bussinessChanceVo.city }">selected="selected"</c:if>>${city.regionName }</option>
							</c:forEach>
						</c:if>
					</select> <select name="zone">
						<option value="0">请选择</option>
						<c:if test="${not empty zoneList }">
							<c:forEach items="${zoneList }" var="zone">
								<option value="${zone.regionId }"
										<c:if test="${ not empty bussinessChanceVo &&  zone.regionId == bussinessChanceVo.zone }">selected="selected"</c:if>>${zone.regionName }</option>
							</c:forEach>
						</c:if>
					</select></li>

				</c:if>
				<li><label class="infor_name" style="width:130px;">是否是【合集】商机</label>
					<select name="mergeStatus">
						<option value="">全部</option>
						<option value="2" <c:if test="${bussinessChanceVo.mergeStatus == 2 }">selected="selected"</c:if>>是</option>
						<option value="0" <c:if test="${bussinessChanceVo.mergeStatus == 0 }">selected="selected"</c:if>>否</option>
					</select>
				</li>

				<li><label class="infor_name searchItem" style="width:130px;">是否已【关联订单】</label>
					<select name="isLinkBd">
						<option value="">全部</option>
						<option value="1" <c:if test="${bussinessChanceVo.isLinkBd == 1 }">selected="selected"</c:if>>是</option>
						<option value="0" <c:if test="${bussinessChanceVo.isLinkBd == 0 }">selected="selected"</c:if>>否</option>
					</select>
				</li>
				<div id="old-li" style="display: none">
					<li><label class="searchItem infor_name">商机类型（原）</label> <select
							class="input-middle" name="type">
						<option value="0">全部</option>
						<c:forEach items="${typeList }" var="type">
							<option value="${type.sysOptionDefinitionId }"
									<c:if test="${type.sysOptionDefinitionId == bussinessChanceVo.type}">selected="selected"</c:if>>${type.title }</option>
						</c:forEach>
					</select></li>
					<li><label class="searchItem infor_name" style="width: 142px;">渠道类型（原 商机来源）</label> <select
							class="input-middle" name="source">
						<option value="0">全部</option>
						<c:forEach items="${sourceList }" var="type">
							<option value="${type.sysOptionDefinitionId }"
									<c:if test="${type.sysOptionDefinitionId == bussinessChanceVo.source}">selected="selected"</c:if>>${type.title }</option>
						</c:forEach>
					</select></li>
					<%--<li><label class="infor_name" style="width: 147px;">渠道名称（原 询价方式）</label>
						<select name="communication">
							<option value="">全部</option>
							<c:forEach items="${communicationList}" var="c">
								<option value="${c.sysOptionDefinitionId }"
										<c:if test="${c.sysOptionDefinitionId == bussinessChanceVo.communication}">selected="selected"</c:if>>${c.title }</option>
							</c:forEach>
						</select>
					</li>--%>
				</div>
				<li>
					<a id="show-a" onclick="showOld(true)">展开</a>
					<a id="unshow-a" style="display:none;" onclick="showOld(false)">收起</a>
				</li>
			</ul>
		</div>
		<input type="hidden" id="status" name="status" value="${bussinessChanceVo.status }"/>
		<div class="tcenter">
			<c:if test="${not empty traderCustomer.traderId }">
				<input type="hidden" name="traderId"
					value="${traderCustomer.traderId }">
				<input type="hidden" name="traderCustomerId"
					value="${traderCustomer.traderCustomerId }">
			</c:if>
			<span class="bt-small bg-light-blue bt-bg-style mr20 "
				onclick="search();" id="searchSpan">搜索</span> <span
				class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>
<%--			<c:choose>--%>
<%--				<c:when test="${not empty method && method == 'bussinesschance'}">--%>
<%--					<c:choose>--%>
<%--						<c:when test="${customerInfoByTraderCustomer.isEnable == 1  && ((customerInfoByTraderCustomer.verifyStatus != null && customerInfoByTraderCustomer.verifyStatus != 0 )|| customerInfoByTraderCustomer.verifyStatus == null)}">--%>
<%--							<c:if test="${isSaleFlag==0}">--%>
<%--								<span class="bt-small bg-light-blue bt-bg-style mr20 addtitle"--%>
<%--																	  tabTitle='{"num":"businesschanceservice<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"./order/bussinesschance/newAddBussinesChance.do?traderId=${traderCustomer.traderId}","title":"新增商机"}'>新增商机</span>--%>
<%--							</c:if>--%>

<%--							<c:if test="${isSaleFlag==1}">--%>
<%--								<span class="bt-small bg-light-blue bt-bg-style mr20 addtitle"--%>
<%--																	  tabTitle='{"num":"businesschanceservice<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/businessChance/edit.do?traderId=${traderCustomer.traderId}","title":"新增商机"}'>新增商机</span>--%>
<%--							</c:if>--%>

<%--						</c:when>--%>
<%--						<c:otherwise>--%>
<%--								<span class="bt-small bg-light-blue bt-bg-style mr20 "--%>
<%--									  onclick="toAddBussinessChance();" >新增商机</span>--%>
<%--						</c:otherwise>--%>
<%--					</c:choose>--%>
<%--				</c:when>--%>
<%--				<c:otherwise>--%>
<%--					<c:if test="${isSaleFlag==0}">--%>
<%--						<span class="bt-small bg-light-blue bt-bg-style mr20 addtitle"--%>
<%--							  tabTitle='{"num":"businesschanceservice<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"./order/bussinesschance/newAddBussinesChance.do?traderId=${traderCustomer.traderId}","title":"新增商机"}'>新增商机</span>--%>
<%--					</c:if>--%>

<%--					<c:if test="${isSaleFlag==1}">--%>
<%--						<span class="bt-small bg-light-blue bt-bg-style mr20 addtitle"--%>
<%--							  tabTitle='{"num":"businesschanceservice<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/businessChance/edit.do?traderId=${traderCustomer.traderId}","title":"新增商机"}'>新增商机</span>--%>
<%--					</c:if>--%>
<%--				</c:otherwise>--%>
<%--			</c:choose>--%>
			<%--<c:if test="${not empty method && method == 'bussinesschance' }">
				<c:if test="${customerInfoByTraderCustomer.isEnable == 1  && ((customerInfoByTraderCustomer.verifyStatus != null && customerInfoByTraderCustomer.verifyStatus != 0 )|| customerInfoByTraderCustomer.verifyStatus == null)}">
					<span class="bt-small bg-light-blue bt-bg-style mr20 addtitle"
						tabTitle='{"num":"businesschanceservice<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"./order/bussinesschance/addSalesBussinessChance.do?traderId=${traderCustomer.traderId }&traderCustomerId=${traderCustomer.traderCustomerId }","title":"新增商机"}'>新增商机</span>
				</c:if>
			</c:if>--%>
		</div>
	</form>
</div>
<div class="content">
	<div class="title-click" id="listConfig">
		列表配置
	</div>

	<div class="fixdiv">
		<div class="superdiv" style="width: 3000px">
			<table
				class="table table-bordered table-striped table-condensed table-centered" style="table-layout: fixed;">
				<thead>
					<tr>
						<th class="th-config wid10">商机编号</th>
						<th class="th-config wid9">商机类型</th>
						<th class="th-config wid9">询价行为</th>
						<th class="th-config wid9">渠道类型</th>
						<%--<th class="th-config wid9">渠道名称</th>--%>
						<th class="th-config wid8">咨询入口</th>
						<th class="th-config wid8">功能</th>
						<th class="th-config wid9">商机等级</th>
						<th class="th-config wid13">预计金额</th>
						<th class="th-config wid13">报价金额</th>
						<th class="th-config wid9">成单几率</th>
						<th class="th-config wid10">分配销售部门</th>
						<th class="th-config wid10">分配销售</th>
						<th class="th-config wid15" >客户名称</th>
						<th class="th-config wid7">客户性质</th>
						<th class="th-config wid12">手机号</th>
						<th class="th-config wid18 ">询价产品</th>
						<th class="th-config wid18">产品备注</th>
						<th class="th-config wid10">商机阶段</th>
						<th class="th-config wid7">商机状态</th>
						<th class="th-config wid8">报价单号</th>
						<th class="th-config wid8">订单号</th>
						<th class="th-config wid10">分配时间</th>
						<th class="th-config wid12">商机初次查看时间</th>
						<th class="th-config wid12">预计成单时间</th>
						<th class="th-config wid12">下次沟通时间</th>
						<th class="th-config wid20">下次沟通内容</th>
						<th class="th-config wid10">响应时长（分）</th>
						<th class="th-config wid12">关闭原因</th>
						<th class="th-config wid8">附件</th>
						<th class="th-config wid12">沟通内容</th>
						<th class="th-config wid12">是否已【关联订单】</th>
					</tr>
				</thead>
				<tbody>
					<c:if test="${not empty bussinessChanceList}">
						<c:forEach items="${bussinessChanceList }" var="bussinessChance">
							<tr>
								<td>
									<div class="font-blue" flag="${bussinessChance.flag}" bussinessChanceNo="${bussinessChance.bussinessChanceNo}">
									<c:if test="${bussinessChance.flag eq 1}">
									<span style="color: red;font-size: 16px">！</span>
									</c:if>
										<c:if test="${bussinessChance.mergeStatus==2}">
											<font color="red">[合]</font>
										</c:if>
										<c:if test="${isSaleFlag==0}">
											<a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"view<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${bussinessChance.bussinessChanceId}&traderId=${bussinessChance.traderId }",
												"title":"销售商机详情"}'>${bussinessChance.bussinessChanceNo }${bussinessChance.verifyStatus == 0 && fn:contains(bussinessChance.verifyUsername, curr_user.username) ?"<font color='red'>[审]</font>":""}</a>
										</c:if>
										<c:if test="${isSaleFlag==1}">
											<a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"view<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											    "link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${bussinessChance.bussinessChanceId}&traderId=${bussinessChance.traderId }",
												"title":"销售商机详情"}'>${bussinessChance.bussinessChanceNo }${bussinessChance.verifyStatus == 0 && fn:contains(bussinessChance.verifyUsername, curr_user.username) ?"<font color='red'>[审]</font>":""}</a>
										</c:if>
									</div>
								</td>
								<td>${bussinessChance.typeName }</td>
								<td>${bussinessChance.inquiryName }</td>
								<!-- 2018-8-22 新增(商机来源)-->
								<td>${bussinessChance.sourceName }</td>
								<%--<td>${bussinessChance.communicationName }</td>--%>

								<td>${bussinessChance.entranceName}</td>
								<td>${bussinessChance.functionName}</td>

								<td>
								  <c:forEach items="${bussinessLevelList }" var="bussinessLevel">
									<c:if test="${bussinessLevel.sysOptionDefinitionId == bussinessChance.bussinessLevel}">${bussinessLevel.title }</c:if>
								  </c:forEach>
								</td>
								<td>${bussinessChance.amount }</td>
								<td>
									<fmt:formatNumber value="${bussinessChance.quoteorderTotalAmount}" type="numer" pattern="0.00" maxFractionDigits="2"/>
								</td>
								<td>
									<c:forEach items="${orderRateList }" var="orderRate">
									<c:if test="${orderRate.sysOptionDefinitionId == bussinessChance.orderRate}">${orderRate.title }</c:if>
								  </c:forEach>
								</td>
								<td>${bussinessChance.saleDeptName }</td>
								<td>${bussinessChance.saleUser }</td>
								<td>
									<c:choose>
										<c:when test="${bussinessChance.traderId != null and bussinessChance.traderId > 0}">
											<span class="font-blue">
												<a class="addtitle" href="javascript:void(0);"
												   tabTitle='{"num":"viewcustomer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
														"link":"./trader/customer/baseinfo.do?traderId=${bussinessChance.traderId}",
														"title":"客户信息"}'>
													<c:if test="${not empty traderGroupMap && not empty traderGroupMap[bussinessChance.traderId]}">
														<span style="color: red">【${traderGroupMap[bussinessChance.traderId].traderGroupName}】</span>
													</c:if>
													${bussinessChance.traderName}
												</a>

											</span>
										</c:when>
										<c:otherwise>
											${bussinessChance.traderName}
										</c:otherwise>
									</c:choose>
								</td>
								<td>
									<c:choose>
										<c:when test="${bussinessChance.customerNature != null and bussinessChance.customerNature == 465}">
											分销
										</c:when>
										<c:when test="${bussinessChance.customerNature != null and bussinessChance.customerNature == 466}">
											终端
										</c:when>
									</c:choose>
								</td>
								<td>
									<c:choose>
										<c:when test="${bussinessChance.checkMobile != null}">
											<i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${bussinessChance.checkMobile}',${bussinessChance.traderId},1,1,${bussinessChance.bussinessChanceId},${bussinessChance.traderContactId});"></i>
											${bussinessChance.checkMobile}
										</c:when>
										<c:when test="${bussinessChance.mobile != null}">
											<i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${bussinessChance.mobile}',0,1,1,${bussinessChance.bussinessChanceId},0);"></i>
											${bussinessChance.mobile}
										</c:when>
									</c:choose>
								</td>
								<td style="word-wrap:break-word;">
								<c:if test="${fn:length(bussinessChance.content)>50 }">
									${fn:substring(bussinessChance.content, 0, 50)}...
								</c:if>
								<c:if test="${fn:length(bussinessChance.content)<=50 }">
									${bussinessChance.content }
								</c:if>
								</td>
								<td>
									<c:choose>
										<c:when test="${bussinessChance.productCommentsSale != null}">${bussinessChance.productCommentsSale}</c:when>
										<c:otherwise>${bussinessChance.productComments}</c:otherwise>
									</c:choose>
								</td>
								<td>
									<c:choose>
										<c:when test="${bussinessChance.bussinessStage == 944}">
											预算阶段
										</c:when>
										<c:when test="${bussinessChance.bussinessStage == 945}">
											方案制定
										</c:when>
										<c:when test="${bussinessChance.bussinessStage == 946}">
											比价阶段
										</c:when>
										<c:when test="${bussinessChance.bussinessStage == 947}">
											合同签署
										</c:when>
									</c:choose>
								</td>
								<td>
									<c:choose>
											<c:when test="${bussinessChance.status eq '0'}">
					                    		<span class="warning-color1">未处理</span>
					                    	</c:when>
											<c:when test="${bussinessChance.status eq '1'}">
				                    	<span class="success-color1">报价中</span>
				                    	</c:when>
											<c:when test="${bussinessChance.status eq '2'}">
				                    	<span class="success-color1">已报价</span>
				                    	</c:when>
											<c:when test="${bussinessChance.status eq '3'}">
				                    	<span class="success-color1">已订单</span>
				                    	</c:when>
										<c:when test="${bussinessChance.status eq '4'}">
				                    	已关闭
				                    	</c:when>
										<c:when test="${bussinessChance.status eq '5'}">
											<span class="warning-color1">未分配</span>
										</c:when>
										<c:when test="${bussinessChance.status eq '6'}">
											<span class="warning-color1">处理中</span>
										</c:when>
										<c:when test="${bussinessChance.status eq '7'}">
											<span class="success-color1">已成单</span>
										</c:when>
									</c:choose>
								</td>
								<td>
									<c:choose>
										<c:when test="${bussinessChance.quoteValidStatus eq 0}">
											<a class="addtitle"
												tabtitle="{&quot;num&quot;:&quot;viewQuote2<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>&quot;,&quot;link&quot;:&quot;./order/quote/getQuoteDetail.do?quoteorderId=${bussinessChance.quoteorderId}&viewType=2&quot;,&quot;title&quot;:&quot;编辑报价&quot;}">${bussinessChance.quoteorderNo}</a>
										</c:when>
										<c:otherwise>
											<a class="addtitle"
												tabtitle="{&quot;num&quot;:&quot;viewQuote3<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>&quot;,&quot;link&quot;:&quot;./order/quote/getQuoteDetail.do?quoteorderId=${bussinessChance.quoteorderId}&viewType=3&quot;,&quot;title&quot;:&quot;报价信息&quot;}">${bussinessChance.quoteorderNo}</a>
										</c:otherwise>
									</c:choose>
									<%-- <a class="addtitle"
										tabtitle="{&quot;num&quot;:&quot;viewQuote3<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>&quot;,&quot;link&quot;:&quot;./order/quote/getQuoteDetail.do?quoteorderId=${bussinessChance.quoteorderId}&viewType=3&quot;,&quot;title&quot;:&quot;报价信息&quot;}">${bussinessChance.quoteorderNo }</a> --%>
								</td>
								<td>
									<a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
										"link":"${pageContext.request.contextPath}/order/saleorder/view.do?saleorderId=${bussinessChance.saleorderId}","title":"订单信息"}'>${bussinessChance.saleorderNo}</a>
								</td>
								<td><date:date value="${bussinessChance.assignTime} " /></td>
								<td>
									<c:choose>
										<c:when test="${bussinessChance.type == 392}">
											<date:date value="${bussinessChance.addTime} " />
										</c:when>
										<c:otherwise>
											<date:date value="${bussinessChance.firstViewTime} " />
										</c:otherwise>
									</c:choose>
								</td>
								<td>
									<date:date value="${bussinessChance.orderTime} " />
								</td>
								<td><date:date value="${bussinessChance.nextContactDate}"/></td>
								<td style="white-space:nowrap;overflow:hidden;text-overflow: ellipsis" onmousemove="removeClassType(this)">${bussinessChance.nextContactContent}</td>
								<td><c:if
										test="${bussinessChance.firstViewTime > 0 && bussinessChance.assignTime > 0}">
										<fmt:formatNumber type="number" maxFractionDigits="1"
											value="${((bussinessChance.firstViewTime-bussinessChance.assignTime)/1000)/60}" />
									</c:if></td>
								<td>${bussinessChance.closeReason }</td>
								<td>
									<c:if test="${not empty bussinessChance.attachmentDomain and not empty bussinessChance.attachmentUri}">
										<a href="http://${bussinessChance.attachmentDomain}${bussinessChance.attachmentUri}" target="_blank">
											附件
										</a>
									</c:if>
								</td>
								<td>
									<ul class="communicatecontent ml0">
										<c:if test="${not empty bussinessChance.tags }">
											<c:forEach items="${bussinessChance.tags }" var="tag">
												<li class="bluetag" title="${tag.tagName}">${tag.tagName}</li>
											</c:forEach>
										</c:if>
										<c:if test="${bussinessChance.contactContent != null and !bussinessChance.contactContent.equals('1')}">
                                            <li>${bussinessChance.contactContent}</li>
                                        </c:if>
									</ul>
								</td>
								<td>
									<c:if test="${bussinessChance.isLinkBd==0}">否</c:if>
									<c:if test="${bussinessChance.isLinkBd==1}">是</c:if>
								</td>
							</tr>
						</c:forEach>
					</c:if>
					<c:if test="${empty bussinessChanceList }">
						<!-- 查询无结果弹出 -->
						<tr>
							<td colspan="14">查询无结果！</td>
						</tr>
					</c:if>
				</tbody>
			</table>

		</div>
	</div>
	<div>
		<tags:page page="${page}" />
	</div>
	<div class="clear"></div>
				<div class="fixtablelastline">
					【商机预计金额之和：<fmt:formatNumber type="number" value="${totalAmount}" pattern="0.00" maxFractionDigits="2" /> 元】
				</div>
	<input name="searchListHidden" value="" hidden="hidden">
	<input name="columnListHidden" value="" hidden="hidden">
</div>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/order/bussinesschance/service_index.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/region/index.js?rnd=${resourceVersionKey}"></script>
<script>

	$(document).ready = getListConfigInfo();

	function removeClassType($this) {
		$($this).attr("title",$this.innerText);
	}


	$('#listConfig').on('click',function () {
		layui.use('layer', function () {
			var layer = layui.layer;
			var searchList = [];
			var columnList = [];

			var searchListHidden = $("input[name='searchListHidden']").val();
			var columnListHidden = $("input[name='columnListHidden']").val();

			$('label.searchItem').each(function (index,data) {
				searchList.push(data.innerHTML)
			})
			$('th.th-config').each(function (index,data) {
				columnList.push(data.innerHTML)
			});
			layer.open({
				title: '列表配置',
				type: 2,
				content: '/system/list/page.do?listId=saleindex&columnList='+ columnList + '&searchList='+ searchList + '&columnListHidden=' + columnListHidden + '&searchListHidden=' + searchListHidden,
				area: ['600px', '400px'],
				btnAlign: 'c',
				btn: ['确认','取消'],
				yes: function (index) {
					var data = window["layui-layer-iframe" + index].getListConfigureValue();
					var res = saveListConfig(data);
					if (res < 0){
						layer.alert('保存列表配置信息失败')
					}
					hiddenElement(data);
					layer.close(index);
					window.location.reload();
				},
				btn2: function () {
					layer.close(index);
				}
			})
		})
	})

	function getListConfigInfo() {
		var listConfig = {
			search: [],
			columnList: []
		};
		$.ajax({
			async: false,
			type: 'GET',
			url: '/system/list/configure.do?listName=businessChanceSaleIndex',
			dataType: 'json',
			success: function (data) {
				if (data.code === 0){
					if (data.data != null && data.data.searchList != null){
						listConfig.search = data.data.searchList.split(',');
					}
					if (data.data != null && data.data.columnList)
						listConfig.columnList = data.data.columnList.split(',');
				} else {
					layer.alert('获取列表配置信息失败')
				}
			},
			error: function (error) {
				console.log(error)
				layer.alert('获取列表配置信息失败')
			}
		})
		$("input[name='searchListHidden']").val(listConfig.search.join(','));
		$("input[name='columnListHidden']").val(listConfig.columnList.join(','));
		hiddenElement(listConfig);
	}


	function hiddenElement(data) {
		$('label.searchItem').each(function (index,dom) {
			if ($.inArray(dom.innerHTML,data.search) >= 0){
				$(dom).parent().hide();
			}
		})

		$('th.th-config').each(function (index,dom) {
			if ($.inArray(dom.innerHTML,data.columnList) >= 0){
				$('.table tr').find('td:eq('+index+')').hide();
				$('.table tr').find('th:eq('+index+')').hide();
			}
		})
	}


	function saveListConfig(data) {
		var listConfig = {};
		listConfig.listName = 'businessChanceSaleIndex';
		listConfig.columnList = data.columnList.join(',');
		listConfig.searchList = data.search.join(',');
		$.ajax({
			async: false,
			type: 'POST',
			url: '/system/list/save.do',
			data: listConfig,
			dataType: 'json',
			success: function (data) {
				return data.code;
			},
			error: function (error) {
				return -1;
			}
		})
	}

    function toAddBussinessChance() {
        layer.alert('此客户正处于审核中，请尽快完成审核！');
    }


</script>
<input type="hidden" id="rest" value="${bussinessChanceVo.isRest }"/>
<%@ include file="../../common/footer.jsp"%>
