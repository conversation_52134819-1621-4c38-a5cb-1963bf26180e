package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.PaymentBankBillNewJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 已结算付款银行流水推送金蝶定时任务
 */
@JobHandler("PaymentBankBillNewBatchTask")
@Component
@Slf4j
public class PaymentBankBillNewBatchTask extends AbstractJobHandler {

    @Autowired
    private PaymentBankBillNewJob paymentBankBillNewJob;

    @Autowired
    private JobLauncher jobLauncher;
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        XxlJobLogger.log("====================已结算付款银行流水推送金蝶开始==================");
        JobParameters pushPaymentBankBillNewJobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job pushPaymentBankBillNewJob = paymentBankBillNewJob.pushPaymentBankBillNewJob();
        jobLauncher.run(pushPaymentBankBillNewJob, pushPaymentBankBillNewJobParameters);
        XxlJobLogger.log("====================已结算付款银行流水推送金蝶结束==================");

        return SUCCESS;
    }
}
