package com.vedeng.erp.buyorder.task.buyorder.statusSync;

import com.newtask.data.AbstractDataByTimeSync;
import com.newtask.data.dao.BuyorderDataMapper;
import com.newtask.data.dto.BuyorderDataDto;
import com.vedeng.erp.buyorder.common.utils.NewBuyOrderUtils;
import com.vedeng.order.model.vo.BuyorderVo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description com.newtask.data.buyorder.statusSync
 * @Date 2021/10/19 15:29
 */
@Component
public class BuyorderSubStatusSync extends AbstractDataByTimeSync {

    @Resource
    BuyorderDataMapper buyorderDataMapper;

    @Override
    public List<Map<String, Object>> loadBizByTimeData(Long startTime, Long endTime) {
        List<Map<String, Object>> datalist = new ArrayList<>();
        List<BuyorderVo> buyorderList = buyorderDataMapper.getBuyorderByTime(startTime, endTime);
        if (CollectionUtils.isNotEmpty(buyorderList)) {
            buyorderList.forEach(item -> {
                //订单主状态
                Map<String, Object> dataMap = new HashMap<>(2);
                dataMap.put("orderId", item.getBuyorderId());
                dataMap.put("subStatus", NewBuyOrderUtils.calcStatusAndSubStatusOfBuyOrder(item));
                datalist.add(dataMap);
            });
        }
        return datalist;
    }


    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        long nowTime = System.currentTimeMillis();
        List<BuyorderDataDto> buyorderDataDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(item -> {
                BuyorderDataDto buyorderDataDto = new BuyorderDataDto();
                Integer orderId = Integer.parseInt(item.get("orderId").toString());
                makeExist(orderId, nowTime);
                buyorderDataDto.setBuyorderId(orderId);
                buyorderDataDto.setSubStatus(item.get("subStatus").toString());
                buyorderDataDtos.add(buyorderDataDto);
            });
            buyorderDataMapper.updateOrderSubStatus(buyorderDataDtos, nowTime);
        }
    }

    @Override
    public void makeExist(Integer id, Long nowTime) {
        int buyorderDataId = buyorderDataMapper.getBuyorderDataById(id);
        if (buyorderDataId <= 0) {
            buyorderDataMapper.insertBuyorderData(id, nowTime);
        }
    }
}
