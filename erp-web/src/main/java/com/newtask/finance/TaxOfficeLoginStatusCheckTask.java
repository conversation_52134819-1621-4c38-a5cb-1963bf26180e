package com.newtask.finance;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.enums.InvoiceApplyCheckRuleEnum;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceLimitRequestDto;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceLimitResponseDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.dto.OpenInvoiceResultDto;
import com.vedeng.erp.finance.enums.CheckChainEnum;
import com.vedeng.erp.finance.enums.SalesOpenInvoiceTypeEnum;
import com.vedeng.erp.finance.enums.TaxesInterfaceCodeEnum;
import com.vedeng.erp.finance.service.FullyDigitalInvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import com.vedeng.erp.finance.service.InvoiceCheckApiService;
import com.vedeng.erp.finance.service.TaxesOpenApiService;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.erp.system.service.UserWorkApiService;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.vedeng.infrastructure.taxes.utils.TaxesUtil.taxesConfig;

/**
 * 税务局登录状态检查任务
 */
@JobHandler("TaxOfficeLoginStatusCheckTask")
@Component
@Slf4j
public class TaxOfficeLoginStatusCheckTask extends AbstractJobHandler {

    @Autowired
    private TaxesOpenApiService taxesOpenApiService;

    @Autowired
    private UserWorkApiService userWorkApiService;

    /**
     * 税务局登录状态检查结果发送人id集合
     */
    @Value("${taxOfficeLoginStatusCheckUserIds}")
    private String taxOfficeLoginStatusCheckUserIds;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        if (StrUtil.isBlank(taxOfficeLoginStatusCheckUserIds)) {
            log.info("税务局登录状态检查结果发送人id集合为空");
            return ReturnT.FAIL;
        }
        List<Integer> userIds = JSON.parseArray(taxOfficeLoginStatusCheckUserIds, Integer.class);
        if (CollUtil.isEmpty(userIds)) {
            log.info("税务局登录状态检查结果发送人id集合为空");
            return ReturnT.FAIL;
        }
        SaleInvoiceLimitResponseDto responseDto = null;
        String returnCode = null;
        String returnMessage = "";
        try {
            responseDto = (SaleInvoiceLimitResponseDto) taxesOpenApiService.openapi(new SaleInvoiceLimitRequestDto(), TaxesInterfaceCodeEnum.LIMIT);
            returnCode = responseDto.getReturnCode();
            returnMessage = responseDto.getReturnMessage();
        } catch (Exception e) {
            log.error("税务局登录状态检查失败,调用接口失败", e);
            return ReturnT.FAIL;
        }
        if (!TaxesReturnCodeEnum.SUCCESS.getCode().equals(returnCode) && ErpConstant.EXCLUDED_LOG_CONTENT_LIST.stream().anyMatch(returnMessage::contains)) {
            log.info("税务局登录状态检查失败,发送通知,returnCode:{}", returnCode);
            String msg = StrUtil.format(taxesConfig.getTAX_OFFICE_LOGIN_STATUS_CHECK_TEMPLATE(),taxesConfig.taxNo);
            for (Integer userId : userIds) {
                if (!ErpConstant.ONE.equals(userId) && !ErpConstant.TWO.equals(userId)) {
                    try {
                        userWorkApiService.sendInvoiceMsg(userId, msg);
                    } catch (Exception e) {
                        log.info("税务局登录状态检查失败,发送通知失败：{}", userId, e);
                    }
                }
            }
        }
        return ReturnT.SUCCESS;
    }
}
