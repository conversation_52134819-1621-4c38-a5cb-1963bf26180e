<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchSupplierFinanceDtoMapper">
    <update id="updateKingDeePushStatus">
      UPDATE T_TRADER_SUPPLIER_FINANCE
      SET IS_PUSH = 1, PUSH_TIME = NOW(), MOD_TIME = MOD_TIME
      WHERE TRADER_SUPPLIER_FINANCE_ID = #{traderSupplierFinanceId,jdbcType=INTEGER}
    </update>


    <select id="findByAll" resultType="com.vedeng.erp.kingdee.batch.dto.BatchSupplierFinanceDto">
        SELECT
        ttsf.TRADER_SUPPLIER_FINANCE_ID,
        tts.TRADER_SUPPLIER_ID ,
        tt.TRADER_NAME ,
        CASE
        ttsf.TRADER_TYPE
        WHEN 1 THEN '01'
        WHEN 2 THEN '02'
        ELSE ''
        END traderTypeName
        FROM
        T_TRADER_SUPPLIER_FINANCE ttsf
        LEFT JOIN T_TRADER_SUPPLIER tts ON
        ttsf.TRADER_ID = tts.TRADER_ID
        LEFT JOIN T_TRADER tt ON
        tts.TRADER_ID = tt.TRADER_ID
        WHERE
        ttsf.IS_DELETE = 0
        AND ttsf.IS_PUSH = 0
        and  tt.IS_ENABLE = 1
        and tts.IS_ENABLE = 1
        <if test="beginTime != null">
            AND ttsf.MOD_TIME <![CDATA[>=]]> #{beginTime,jdbcType=DATE}
        </if>
        <if test="endTime != null">
            AND ttsf.MOD_TIME <![CDATA[<=]]> #{endTime,jdbcType=DATE}
        </if>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="getSupplierIdByTraderId" resultType="com.vedeng.erp.kingdee.batch.dto.BatchSupplierFinanceDto">
        select TRADER_SUPPLIER_ID traderSupplierId
        from T_TRADER_SUPPLIER
        where TRADER_ID = #{traderId}
    </select>

    <select id="querySupplierCompensate" resultType="com.vedeng.erp.kingdee.batch.dto.BatchSupplierFinanceDto">
        select TTS.TRADER_SUPPLIER_ID
        from T_TRADER_SUPPLIER TTS
                 left join T_VERIFIES_INFO TVI
                           on TTS.TRADER_SUPPLIER_ID = TVI.RELATE_TABLE_KEY and TVI.RELATE_TABLE = 'T_TRADER_SUPPLIER' and
                              TVI.VERIFIES_TYPE = 619
                 left join KING_DEE_SUPPLIER KDS on TTS.TRADER_SUPPLIER_ID = KDS.F_NUMBER
        where TTS.IS_ENABLE = 1
          and KDS.ID is null
          and TVI.STATUS = 1
        <if test="beginTimestamp != null">
            and TTS.ADD_TIME <![CDATA[>=]]> #{beginTimestamp,jdbcType=BIGINT}
        </if>
        <if test="endTimestamp != null">
            and TTS.ADD_TIME <![CDATA[<=]]> #{endTimestamp,jdbcType=BIGINT}
        </if>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>
</mapper>