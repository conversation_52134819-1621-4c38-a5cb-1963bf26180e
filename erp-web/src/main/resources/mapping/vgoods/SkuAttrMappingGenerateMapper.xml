<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.SkuAttrMappingGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.SkuAttrMappingGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <id column="SKU_ATTR_ID" jdbcType="INTEGER" property="skuAttrId" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="BASE_ATTRIBUTE_ID" jdbcType="INTEGER" property="baseAttributeId" />
    <result column="STATUS" jdbcType="TINYINT" property="status" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="BASE_ATTRIBUTE_VALUE_ID" jdbcType="INTEGER" property="baseAttributeValueId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    SKU_ATTR_ID, SKU_ID, BASE_ATTRIBUTE_ID, `STATUS`, UPDATER, CREATOR, ADD_TIME, MOD_TIME, 
    BASE_ATTRIBUTE_VALUE_ID
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.goods.model.SkuAttrMappingGenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from V_SKU_ATTR_MAPPING
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from V_SKU_ATTR_MAPPING
    where SKU_ATTR_ID = #{skuAttrId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from V_SKU_ATTR_MAPPING
    where SKU_ATTR_ID = #{skuAttrId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.goods.model.SkuAttrMappingGenerateExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from V_SKU_ATTR_MAPPING
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.SkuAttrMappingGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="skuAttrId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into V_SKU_ATTR_MAPPING (SKU_ID, BASE_ATTRIBUTE_ID, `STATUS`, 
      UPDATER, CREATOR, ADD_TIME, 
      MOD_TIME, BASE_ATTRIBUTE_VALUE_ID)
    values (#{skuId,jdbcType=INTEGER}, #{baseAttributeId,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, 
      #{updater,jdbcType=INTEGER}, #{creator,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modTime,jdbcType=TIMESTAMP}, #{baseAttributeValueId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.SkuAttrMappingGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="skuAttrId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into V_SKU_ATTR_MAPPING
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="baseAttributeId != null">
        BASE_ATTRIBUTE_ID,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="baseAttributeValueId != null">
        BASE_ATTRIBUTE_VALUE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="baseAttributeId != null">
        #{baseAttributeId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="baseAttributeValueId != null">
        #{baseAttributeValueId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.goods.model.SkuAttrMappingGenerateExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select count(*) from V_SKU_ATTR_MAPPING
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update V_SKU_ATTR_MAPPING
    <set>
      <if test="record.skuAttrId != null">
        SKU_ATTR_ID = #{record.skuAttrId,jdbcType=INTEGER},
      </if>
      <if test="record.skuId != null">
        SKU_ID = #{record.skuId,jdbcType=INTEGER},
      </if>
      <if test="record.baseAttributeId != null">
        BASE_ATTRIBUTE_ID = #{record.baseAttributeId,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        `STATUS` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.baseAttributeValueId != null">
        BASE_ATTRIBUTE_VALUE_ID = #{record.baseAttributeValueId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update V_SKU_ATTR_MAPPING
    set SKU_ATTR_ID = #{record.skuAttrId,jdbcType=INTEGER},
      SKU_ID = #{record.skuId,jdbcType=INTEGER},
      BASE_ATTRIBUTE_ID = #{record.baseAttributeId,jdbcType=INTEGER},
      `STATUS` = #{record.status,jdbcType=TINYINT},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      BASE_ATTRIBUTE_VALUE_ID = #{record.baseAttributeValueId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.SkuAttrMappingGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update V_SKU_ATTR_MAPPING
    <set>
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="baseAttributeId != null">
        BASE_ATTRIBUTE_ID = #{baseAttributeId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=TINYINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="baseAttributeValueId != null">
        BASE_ATTRIBUTE_VALUE_ID = #{baseAttributeValueId,jdbcType=INTEGER},
      </if>
    </set>
    where SKU_ATTR_ID = #{skuAttrId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.SkuAttrMappingGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update V_SKU_ATTR_MAPPING
    set SKU_ID = #{skuId,jdbcType=INTEGER},
      BASE_ATTRIBUTE_ID = #{baseAttributeId,jdbcType=INTEGER},
      `STATUS` = #{status,jdbcType=TINYINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR = #{creator,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      BASE_ATTRIBUTE_VALUE_ID = #{baseAttributeValueId,jdbcType=INTEGER}
    where SKU_ATTR_ID = #{skuAttrId,jdbcType=INTEGER}
  </update>
</mapper>