<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>删除拜访记录</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="deleteVisitRecord" datasource="erp-datasourcetarget" success_url="reload">
<div id="APPEND_HEAD" class="append">

</div>
<div class="layui-container">
    <form id="inputForm" method="post" class="layui-form">

       <!-- <div class="layui-form-item ">
            <label class="layui-form-label">提问内容</label>
            <div class="layui-input-inline form-group">
                <input    lay-verify="required"
                          type="text"    class="layui-input form-item"
                          value=""  name="QUESTION" item_desc="提问内容" maxlength="256">
            </div>
        </div>
        <div class="layui-form-item ">
            <label class="layui-form-label">问题来源</label>
            <div class="layui-input-inline form-group">
                <input  readonly="readonly"
                        type="text"   class="layui-input form-item"
                        value=""  name="DOC_TYPE" item_desc="问题来源">
            </div>
        </div>
        <div class="layui-form-item "> <label class="layui-form-label">答案</label>
            <div class="layui-input-inline form-group">
                <object class="form-item" name="ANSWER" oninput="updateWordCount();" item_desc="答案"type="textarea" value="" item_name="ANSWER" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc=""  valid_rule="" valid_msg="" item_max_upload=""></object>
                <small id="DESC_ANSWER" class="form-text text-muted">字数</small>
            </div>
        </div>-->




    </form>
    <form id="configForm" class="layui-form">
        <div class="layui-card" >
            <div class="layui-card-header">
                表单数据配置
            </div>
            <div class="layui-card-body">
            </div>
        </div>
        <div class="layui-form-item "><label class="layui-form-label">初始表达式</label>
            <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">list=select("SELECT
        * from KING_DEE_EVENT_MSG where KING_DEE_EVENT_MSG_ID= ${ID}");
return list.get(0);</pre>
            </div>
        </div>
        <div class="layui-form-item "> <label class="layui-form-label">提交表达式</label>
            <div class="layui-input-block form-group">
              <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">if(null==ID||"".equals(ID)){
                    sessionUserId=$$("EZ_SESSION_USER_ID_KEY");
                    insert("UPDATE KING_DEE_EVENT_MSG SET MOD_TIME= NOW() WHERE 1=2");
                     return 0;
                }else{
                    update("UPDATE KING_DEE_EVENT_MSG SET MOD_TIME= NOW() WHERE 1=2");
                    return ID;
                }
              </pre>
            </div>
        </div>
        <div class="layui-form-item "> <label class="layui-form-label">删除表达式</label>
            <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type="">
          <!--if(null!=ID||IS_DELETE ==0){-->
          <!--update("UPDATE T_ASK_QUESTION_MANAGEMENT  SET IS_DELETE = 1, UPDATER = #{EZ_SESSION_USER_ID_KEY}, MOD_TIME = NOW() WHERE ID = #{ID}");-->
          <!--}else{-->
          <!--update(" UPDATE T_ASK_QUESTION_MANAGEMENT SET IS_DELETE = 0, UPDATER = #{EZ_SESSION_USER_ID_KEY}, MOD_TIME = NOW() WHERE ID = #{ID}");-->
          <!--}-->
          update(" UPDATE T_VISIT_RECORD SET IS_DELETE =1, MOD_USER_ID = #{EZ_SESSION_USER_ID_KEY}, MOD_TIME = NOW() WHERE ID = #{ID} ");
          return ID;
      </pre>
            </div>
        </div>

    </form>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
<div id="APPEND_FOOT" class="append">
    <script>
        var id=  $("#ID").val();
        if(id == undefined || id == ""){
            $("div[item_name='DOC_TYPE']").hide();
        }
        function updateCharCount(obj){
            //var textarea = document.getElementsByName('ANSWER');
            var wordCount = document.getElementById('DESC_ANSWER');
            // 使用正则表达式匹配非空白字符序列作为“词”的定义
            var words = $(obj).val().match(/\S+/g);
            // 更新词的数量
            var current = (words!=undefined && words[0] )? words[0].length : 0;
            wordCount.textContent = current +"/2000";
        }

        $(function(){
            updateCharCount($('textarea[name="ANSWER"]')[0]);
            $('textarea[name="ANSWER"]').attr("maxlength",200);
            $('input[name="QUESTION"]').attr("maxlength",256);
            $('textarea[name="ANSWER"]').on('input', function(e) {
                updateCharCount(this);
            });
        });
    </script>
</div>
</body>
</html>
