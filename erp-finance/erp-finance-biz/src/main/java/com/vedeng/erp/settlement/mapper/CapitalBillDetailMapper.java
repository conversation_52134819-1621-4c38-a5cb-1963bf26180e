package com.vedeng.erp.settlement.mapper;

import com.vedeng.erp.settlement.domain.entity.CapitalBillDetailEntity;

import java.math.BigDecimal;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/8/27 13:20
 **/
@Named("newCapitalBillDetailMapper")
public interface CapitalBillDetailMapper {
    /**
     * delete by primary key
     * @param capitalBillDetailId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer capitalBillDetailId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(CapitalBillDetailEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(CapitalBillDetailEntity record);

    /**
     * select by primary key
     * @param capitalBillDetailId primary key
     * @return object by primary key
     */
    CapitalBillDetailEntity selectByPrimaryKey(Integer capitalBillDetailId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(CapitalBillDetailEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(CapitalBillDetailEntity record);

    int batchInsert(@Param("list") List<CapitalBillDetailEntity> list);

    BigDecimal getAfterSalesFlByBuyOrderId(@Param("buyOrderId") Integer buyOrderId);

    /**
     * 采购单支付金额（不含返利）
     * @param buyOrderId
     * @return
     */
    BigDecimal getBuyOrderHavePayMoney(@Param("buyOrderId") Integer buyOrderId);

    /**
     * 采购单售后金额（不含返利）
     * @param buyOrderId
     * @return
     */
    BigDecimal getBuyOrderAfterSalesHavePayMoney(@Param("buyOrderId") Integer buyOrderId);
}