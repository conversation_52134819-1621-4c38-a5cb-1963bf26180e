// 手机联想 联系人
Vue.component('ui-phone-related', {
    template: `
        <div class="vd-ui-phone-related">
            <div class="phone-related-show-wrap" @click="openErpSearch">
                <ui-placeholder v-if="!phoneValue">{{ placeholder }}</ui-placeholder>
                <div v-else class="phone-related-show" :class="{'pb': mobileStatus == 1 || mobileStatus == 2}">
                    <div class="phone-value">{{ phoneValue }}</div>
                    <ui-tip v-if="mobileStatus == 1" type="success">该手机号已注册贝登商城</ui-tip>
                    <ui-tip v-if="mobileStatus == 2">该手机号未注册贝登商城</ui-tip>
                </div>
            </div>

            <!-- 手机联想弹层 -->
            <crm-slide-dialog ref="slideDialog" title="手机" @hidn="handlerCancel">
                <div class="phone-related-panel">
                    <div class="slide-dialog-input-wrap">
                        <ui-input
                            v-model="dialogInput"
                            v-bind="$attrs"
                            type="tel"
                            maxlength="11"
                            @blur="handlerBlur"
                            @focus="handlerFocus"
                            @input.native="handlerInput"
                            @change="handlerChange"
                            border
                            clear
                            autocomplete="off"
                        />
                    </div>

                    <div class="phone-related-ul" @click.stop>
                        <template v-if="axiosSearchKey">
                            <div class="erp-loading" v-if="loading">
                                <i class="vd-ui_icon icon-loading" ref="loading"></i>
                                <span>加载中...</span>
                            </div>
                            <div class="related-list" v-else-if="relateList.length">
                                <template v-for="(item, index) in relateList">
                                    <div 
                                        :key="index"
                                        class="pr-item" :class="{'active': item.traderContactId == tempChoose.traderContactId}"
                                        v-if="item.name && item.mobile"
                                        @click.stop="chooseThis(item)"
                                    >
                                        <div class="name text-line-1">{{item.name}}</div>
                                        <div class="mobile" v-html="lightName(item.mobile)"></div>
                                    </div>
                                </template>
                            </div>
                            <div class="erp-load-empty" v-else>
                                <img src="/mstatic/image/empty/search-empty.svg"/>
                                <p>抱歉，没有找到符合条件的结果，</p>
                                <p>建议您换个关键词</p>
                            </div>
                        </template>
                    </div>

                    <div class="slide-dialog-default-footer">
                        <div class="btn-cancel-flex">
                            <vd-ui-button @click="handlerCancel">取消</vd-ui-button>
                        </div>
                        <div class="btn-confirm-flex">
                            <vd-ui-button type="primary" @click="handlerConfirm">确定</vd-ui-button>
                        </div>
                    </div>
                </div>
            </crm-slide-dialog>
        </div>
    `,

    props: {
        value: {
            type: String,
            default: ''
        },
        // 已建档客户id： 有id才联想，否则只做简单输入
        traderId: {
            type: [String, Number],
            default: ''
        },
        // 是否精确匹配:  true:精确[输入满11位才查询]  false:非精确
        accurateMatch: {
            type: Boolean,
            default: false,
        },
        maxlength: {
            type: Number,
            default: 11
        },
        placeholder: {
            type: String,
            default: ''
        },
        errorMsg: {
            type: String
        },
    },
    data() {
        return {
            phoneValue: '',
            mobileStatus: 0, // 手机号是否注册过贝登商城 1注册过 0未
            choosePhone: {}, // 选中项

            // 联想
            dialogInput: '',
            loading: false,
            axiosSearchKey: '', // 记录接口搜索词·高亮  有此值，代表已经触发过搜索
            timer: null,
            relateList: [], // 搜索联想
            tempChoose: {}, // 临时选中项
        }
    },
    watch: {
        value: {
            handler (newV) {
                this.phoneValue = newV;
                this.checkMobileStatus();
            },
            immediate: true
        },
    },
    computed: {
        lightName () {
            return (name) => {
                if (!this.axiosSearchKey) return name;
                const regExp = new RegExp(this.axiosSearchKey, 'g');
                name = name.replace(regExp, `<font color='#FF6600'">${this.axiosSearchKey}</font>`);
                return name;
            }
        },
        tags () {
            let arr = this.companyInfo.tags.split(';') || [];
            return arr
        },
        category_Big_Middle_Small () {
            let arr = [];
            this.companyInfo.category && arr.push(this.companyInfo.category); 
            this.companyInfo.categoryBig && arr.push(this.companyInfo.categoryBig); 
            this.companyInfo.categoryMiddle && arr.push(this.companyInfo.categoryMiddle); 
            this.companyInfo.categorySmall && arr.push(this.companyInfo.categorySmall);
            return arr.join(' / ')
        }
    },
    mounted() {
    },
    methods: {
        handlerBlur () {
            this.$emit('blur');
        },
        // 输入停顿300毫秒后搜素
        handlerInput (event) {
            let val = event.target.value.trim();
            this.handlerSearch(val);
        },
        handlerChange () {
            let val = this.dialogInput.trim();
            this.handlerSearch(val);
        },
        handlerFocus (event) {
            let val = event.target.value.trim();
            this.handlerSearch(val);
        },
        handlerSearch (val) {
            val = val.trim();
            if (!val) {
                this.axiosSearchKey = '';
                this.relateList = [];
                this.loading = false;
                this.timer = null;
                this.tempChoose = {};
                return;
            };
            if (val == this.axiosSearchKey) return;

            this.timer && clearTimeout(this.timer);
            if (this.traderId) {
                this.timer = setTimeout(()=> {
                    this.getRelatelist(val);
                }, 300)
            }
        },

        openErpSearch () {
            if (this.phoneValue) {
                this.dialogInput = this.phoneValue;
                this.tempChoose = JSON.parse(JSON.stringify(this.choosePhone));
                this.getRelatelist(this.phoneValue);
            }

            this.$refs.slideDialog.show();
        },

        getRelatelist (val) {
            this.loading = true;
            this.axiosSearchKey = val;

            let query = `?traderId=${this.traderId}&mobile=${val}&pageSize=100&accurateMatch=${this.accurateMatch? 1: 0}`;
            this.$axios.post(`/crm/traderContact/m/page${query}`).then(({data}) => {
                this.loading = false;
                if (data.success) {
                    this.relateList = data.data.list || [];
                    console.log('phone related:', this.relateList);
                }
            }).catch(err=> {
                this.loading = false;
            })
        },

        // 选择已建档客户
        async chooseThis (item) {
            this.tempChoose = item;
        },

        // 确定
        handlerConfirm () {
            let mobile = this.tempChoose.mobile || this.dialogInput || '';
            if (!mobile) {
                this.$message({
                    type: 'error',
                    message: '请输入手机号',
                })
                return;
            } else if (mobile.length !== 11) {
                this.$message({
                    type: 'error',
                    message: '请输入11位手机号码',
                })
                return;
            }

            this.$emit("input", mobile);
            this.checkMobileStatus();
            this.choosePhone = JSON.parse(JSON.stringify(this.tempChoose));

            let emitData = Object.assign({}, this.choosePhone, {
                choosed: true,
                traderId: this.choosePhone.traderId || '',
                mobile: this.choosePhone.mobile || '',
                traderContactId: this.choosePhone.traderContactId || '',
                traderContactName: this.choosePhone.name || '',
            });
            this.$emit('change', emitData);

            setTimeout(()=> {
                this.handlerCancel();
            }, 30);
        },
        // 取消
        handlerCancel () {
            this.$refs.slideDialog.hide();

            this.dialogInput = '';
            this.axiosSearchKey = '';
            this.relateList = [];
            this.loading = false;
            this.timer = null;
            this.tempChoose = {};
        },


        // 手机号是否注册过贝登
        checkMobileStatus() {
            if(this.phoneValue) {
                this.$axios.post('/crm/visitrecord/profile/checkMobileExists?mobile=' + this.phoneValue).then(({ data }) => {
                    if (data.success) {
                        this.mobileStatus = data.data ? 1 : 2
                    }
                })
            } else {
                this.mobileStatus = 0
            }
        },
    }
})