package com.vedeng.crm.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.crm.feign.terminal.TerminalApiService;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.dto.TerminalQueryDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 终端
 */
@ExceptionController
@RestController
@RequestMapping("/terminal/public")
@Slf4j
public class TerminalQueryApi {

    @Autowired
    private TerminalApiService terminalApiService;

    /**
     * 查询终端信息
     *
     * @return
     */
    @RequestMapping(value = "/search", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<PageInfo<OrderTerminalDto>> findThreeCategory(@RequestBody TerminalQueryDto terminalQueryDto) {
        return terminalApiService.search(terminalQueryDto);
    }

}
