package com.vedeng.temporal.notification;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.vedeng.infrastructure.wxrobot.dto.WxMsgDto;
import com.vedeng.infrastructure.wxrobot.service.WxRobotService;
import com.vedeng.temporal.config.TemporalProperties;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * Temporal工作流通知服务（简化版）
 * 统一处理各类工作流失败的通知发送，采用同步方式发送
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 3.0
 * @since 2025-01-31
 */
@Service
@Slf4j
public class TemporalNotificationService {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Autowired
    private WxRobotService wxRobotService;
    
    @Autowired
    private TemporalProperties temporalProperties;
    
    /**
     * 发送业务失败通知
     */
    public void sendBusinessFailureNotification(NotificationContext context) {
        sendNotification(context, NotificationLevel.BUSINESS, 
            context.getBusinessId(),
            context.getBusinessType(),
            context.getTargetCompany(),
            context.getOperationName(),
            context.getErrorMessage(),
            LocalDateTime.now().format(DATE_FORMATTER),
            context.getDuration() != null ? context.getDuration() : 0);
    }
    
    /**
     * 发送技术异常通知
     */
    public void sendTechnicalErrorNotification(NotificationContext context) {
        sendNotification(context, NotificationLevel.TECHNICAL,
            context.getBusinessId(),
            context.getExceptionType(),
            context.getErrorMessage(),
            context.getOperationName(),
            LocalDateTime.now().format(DATE_FORMATTER),
            context.getDuration() != null ? context.getDuration() : 0);
    }
    
    /**
     * 发送工作流失败通知
     */
    public void sendWorkflowFailureNotification(NotificationContext context) {
        sendNotification(context, NotificationLevel.WORKFLOW,
            context.getBusinessId(), // 作为工作流ID
            context.getBusinessType(),
            context.getSourceCompany() + "→" + context.getTargetCompany(),
            context.getTargetCompany(),
            context.getErrorMessage(),
            LocalDateTime.now().format(DATE_FORMATTER),
            context.getOperationName());
    }
    
    /**
     * 统一发送通知方法
     */
    private void sendNotification(NotificationContext context, NotificationLevel level, Object... params) {
        String robotKey = temporalProperties.getNotification().getRobotWebhook();
        if (StrUtil.isBlank(robotKey)) {
            log.warn("机器人Key未配置，无法发送通知");
            return;
        }
        
        String message = StrUtil.format(level.getTemplate(), params);
        WxMsgDto wxMsgDto = new WxMsgDto().initWxMsgDto(message);
        
        // 直接同步发送通知
        try {
            wxRobotService.send(robotKey, wxMsgDto);
            log.info("发送{}级别通知成功，业务ID: {}, 操作: {}", 
                level.getDescription(), context.getBusinessId(), context.getOperationName());
        } catch (Exception e) {
            log.error("发送{}级别通知失败，业务ID: {}, 操作: {}", 
                level.getDescription(), context.getBusinessId(), context.getOperationName(), e);
        }
    }
}
