<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="修改拜访人" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/core.js?rnd=${resourceVersionKey}"></script>
<style type="text/css">

</style>
<div class="content" style="padding-bottom: 20px;">
    <div class="parts">
        <div class="title-container">
            <div class="table-title">拜访计划</div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smallest">归属销售</td>
                <td class="table-middle"><div id="saleUserId" value="${userList}"></div></td>
            </tr>
            </tbody>
        </table>
    </div>

    </div>
</c:if>
	<%--<div class="pb50 font-grey9">
		友情提醒： <br />
		1、如果该客户同时也是供应商，则显示供应商相关数据，否则供应信息为空；采购次数和采购金额指我司向其采购订单的次数和金额。
	</div>--%>
</div>
<%@ include file="../../common/footer.jsp" %>
