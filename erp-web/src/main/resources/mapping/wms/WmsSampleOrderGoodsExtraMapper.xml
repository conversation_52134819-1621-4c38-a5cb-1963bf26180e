<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wms.dao.WmsSampleOrderGoodsExtraMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.wms.dto.WmsSampleOrderGoodsExtra">
    <!--@mbg.generated-->
    <!--@Table T_WMS_SAMPLE_ORDER_GOODS_EXTRA-->
    <id column="SAMPLE_ORDER_GOODS_EXTRA_ID" jdbcType="BIGINT" property="sampleOrderGoodsExtraId" />
    <result column="WMS_OUTPUT_ORDER_GOODS_ID" jdbcType="BIGINT" property="wmsOutputOrderGoodsId" />
    <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="TOTAL_PRICE" jdbcType="DECIMAL" property="totalPrice" />
    <result column="PURCHASE_PRICE" jdbcType="DECIMAL" property="purchasePrice" />
    <result column="TOTAL_PURCHASE_PRICE" jdbcType="DECIMAL" property="totalPurchasePrice" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SAMPLE_ORDER_GOODS_EXTRA_ID, WMS_OUTPUT_ORDER_GOODS_ID, UNIT_NAME, PRICE, TOTAL_PRICE,
    PURCHASE_PRICE, TOTAL_PURCHASE_PRICE, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME,
    MOD_TIME, UPDATER, UPDATER_NAME, REMARK, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_WMS_SAMPLE_ORDER_GOODS_EXTRA
    where SAMPLE_ORDER_GOODS_EXTRA_ID = #{sampleOrderGoodsExtraId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_WMS_SAMPLE_ORDER_GOODS_EXTRA
    where SAMPLE_ORDER_GOODS_EXTRA_ID = #{sampleOrderGoodsExtraId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="SAMPLE_ORDER_GOODS_EXTRA_ID" keyProperty="sampleOrderGoodsExtraId" parameterType="com.vedeng.erp.wms.dto.WmsSampleOrderGoodsExtra" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_SAMPLE_ORDER_GOODS_EXTRA (WMS_OUTPUT_ORDER_GOODS_ID, UNIT_NAME, PRICE,
    TOTAL_PRICE, PURCHASE_PRICE, TOTAL_PURCHASE_PRICE,
    IS_DELETE, ADD_TIME, CREATOR,
    CREATOR_NAME, MOD_TIME, UPDATER,
    UPDATER_NAME, REMARK, UPDATE_REMARK
    )
    values (#{wmsOutputOrderGoodsId,jdbcType=BIGINT}, #{unitName,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL},
    #{totalPrice,jdbcType=DECIMAL}, #{purchasePrice,jdbcType=DECIMAL}, #{totalPurchasePrice,jdbcType=DECIMAL},
    #{isDelete,jdbcType=BOOLEAN}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
    #{creatorName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER},
    #{updaterName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" keyColumn="SAMPLE_ORDER_GOODS_EXTRA_ID" keyProperty="sampleOrderGoodsExtraId" parameterType="com.vedeng.erp.wms.dto.WmsSampleOrderGoodsExtra" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_SAMPLE_ORDER_GOODS_EXTRA
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="wmsOutputOrderGoodsId != null">
        WMS_OUTPUT_ORDER_GOODS_ID,
      </if>
      <if test="unitName != null">
        UNIT_NAME,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="totalPrice != null">
        TOTAL_PRICE,
      </if>
      <if test="purchasePrice != null">
        PURCHASE_PRICE,
      </if>
      <if test="totalPurchasePrice != null">
        TOTAL_PURCHASE_PRICE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="wmsOutputOrderGoodsId != null">
        #{wmsOutputOrderGoodsId,jdbcType=BIGINT},
      </if>
      <if test="unitName != null">
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchasePrice != null">
        #{purchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="totalPurchasePrice != null">
        #{totalPurchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.wms.dto.WmsSampleOrderGoodsExtra">
    <!--@mbg.generated-->
    update T_WMS_SAMPLE_ORDER_GOODS_EXTRA
    <set>
      <if test="wmsOutputOrderGoodsId != null">
        WMS_OUTPUT_ORDER_GOODS_ID = #{wmsOutputOrderGoodsId,jdbcType=BIGINT},
      </if>
      <if test="unitName != null">
        UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="totalPrice != null">
        TOTAL_PRICE = #{totalPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchasePrice != null">
        PURCHASE_PRICE = #{purchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="totalPurchasePrice != null">
        TOTAL_PURCHASE_PRICE = #{totalPurchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where SAMPLE_ORDER_GOODS_EXTRA_ID = #{sampleOrderGoodsExtraId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.wms.dto.WmsSampleOrderGoodsExtra">
    <!--@mbg.generated-->
    update T_WMS_SAMPLE_ORDER_GOODS_EXTRA
    set WMS_OUTPUT_ORDER_GOODS_ID = #{wmsOutputOrderGoodsId,jdbcType=BIGINT},
    UNIT_NAME = #{unitName,jdbcType=VARCHAR},
    PRICE = #{price,jdbcType=DECIMAL},
    TOTAL_PRICE = #{totalPrice,jdbcType=DECIMAL},
    PURCHASE_PRICE = #{purchasePrice,jdbcType=DECIMAL},
    TOTAL_PURCHASE_PRICE = #{totalPurchasePrice,jdbcType=DECIMAL},
    IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
    ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
    CREATOR = #{creator,jdbcType=INTEGER},
    CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
    MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    UPDATER = #{updater,jdbcType=INTEGER},
    UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    REMARK = #{remark,jdbcType=VARCHAR},
    UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where SAMPLE_ORDER_GOODS_EXTRA_ID = #{sampleOrderGoodsExtraId,jdbcType=BIGINT}
  </update>


  <!--auto generated by MybatisCodeHelper on 2023-02-22-->
  <select id="selectByWmsOutputOrderGoodsId" resultMap="BaseResultMap">
    select
      SAMPLE_ORDER_GOODS_EXTRA_ID, WMS_OUTPUT_ORDER_GOODS_ID, UNIT_NAME, Round(PRICE,2) PRICE, TOTAL_PRICE,
      Round(PURCHASE_PRICE,2) PURCHASE_PRICE, TOTAL_PURCHASE_PRICE, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME,
      MOD_TIME, UPDATER, UPDATER_NAME, REMARK, UPDATE_REMARK
    from T_WMS_SAMPLE_ORDER_GOODS_EXTRA
    where WMS_OUTPUT_ORDER_GOODS_ID=#{wmsOutputOrderGoodsId,jdbcType=BIGINT}
      and IS_DELETE = 0 limit 1
  </select>

</mapper>