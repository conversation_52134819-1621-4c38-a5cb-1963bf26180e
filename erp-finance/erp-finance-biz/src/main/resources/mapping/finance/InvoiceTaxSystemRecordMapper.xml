<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.InvoiceTaxSystemRecordMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.InvoiceTaxSystemRecordEntity">
        <!--@mbg.generated-->
        <!--@Table T_INVOICE_TAX_SYSTEM_RECORD-->
        <id column="INVOICE_TAX_SYSTEM_RECORD_ID" jdbcType="BIGINT" property="invoiceTaxSystemRecordId"/>
        <result column="BUSINESS_ID" jdbcType="VARCHAR" property="businessId"/>
        <result column="INTERFACE_TYPE" jdbcType="VARCHAR" property="interfaceType"/>
        <result column="RUNNING_STATUS" jdbcType="VARCHAR" property="runningStatus"/>
        <result column="RETRY_NUM" jdbcType="INTEGER" property="retryNum"/>
        <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete"/>
        <result column="URL_PDF" jdbcType="VARCHAR" property="urlPdf"/>
        <result column="URL_XML" jdbcType="VARCHAR" property="urlXml"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark"/>
        <result column="BODY_ORG" jdbcType="LONGVARCHAR" property="bodyOrg"/>
        <result column="RESULT" jdbcType="LONGVARCHAR" property="result"/>
        <result column="INVOICE_NO" jdbcType="VARCHAR" property="invoiceNo" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        INVOICE_TAX_SYSTEM_RECORD_ID,
        BUSINESS_ID,
        INTERFACE_TYPE,
        RUNNING_STATUS,
        RETRY_NUM,
        IS_DELETE,
        URL_PDF,
        URL_XML,
        ADD_TIME,
        MOD_TIME,
        CREATOR,
        CREATOR_NAME,
        UPDATER,
        UPDATER_NAME,
        UPDATE_REMARK,
        BODY_ORG,
        `RESULT`,
        INVOICE_NO
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE_TAX_SYSTEM_RECORD
        where INVOICE_TAX_SYSTEM_RECORD_ID = #{invoiceTaxSystemRecordId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from T_INVOICE_TAX_SYSTEM_RECORD
        where INVOICE_TAX_SYSTEM_RECORD_ID = #{invoiceTaxSystemRecordId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceTaxSystemRecordEntity">
        <!--@mbg.generated-->
        insert into T_INVOICE_TAX_SYSTEM_RECORD (INVOICE_TAX_SYSTEM_RECORD_ID, BUSINESS_ID,
                                                 INTERFACE_TYPE, RUNNING_STATUS, RETRY_NUM,
                                                 IS_DELETE, URL_PDF, URL_XML,
                                                 ADD_TIME, MOD_TIME, CREATOR,
                                                 CREATOR_NAME, UPDATER, UPDATER_NAME,
                                                 UPDATE_REMARK, BODY_ORG, `RESULT`,INVOICE_NO)
        values (#{invoiceTaxSystemRecordId,jdbcType=BIGINT}, #{businessId,jdbcType=VARCHAR},
                #{interfaceType,jdbcType=VARCHAR}, #{runningStatus,jdbcType=VARCHAR}, #{retryNum,jdbcType=INTEGER},
                #{isDelete,jdbcType=BOOLEAN}, #{urlPdf,jdbcType=VARCHAR}, #{urlXml,jdbcType=VARCHAR},
                #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
                #{creatorName,jdbcType=VARCHAR}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR},
                #{updateRemark,jdbcType=VARCHAR}, #{bodyOrg,jdbcType=LONGVARCHAR}, #{result,jdbcType=LONGVARCHAR},
                #{invoiceNo,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceTaxSystemRecordEntity">
        <!--@mbg.generated-->
        insert into T_INVOICE_TAX_SYSTEM_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceTaxSystemRecordId != null">
                INVOICE_TAX_SYSTEM_RECORD_ID,
            </if>
            <if test="businessId != null">
                BUSINESS_ID,
            </if>
            <if test="interfaceType != null">
                INTERFACE_TYPE,
            </if>
            <if test="runningStatus != null">
                RUNNING_STATUS,
            </if>
            <if test="retryNum != null">
                RETRY_NUM,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="urlPdf != null">
                URL_PDF,
            </if>
            <if test="urlXml != null">
                URL_XML,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="creatorName != null">
                CREATOR_NAME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="updaterName != null">
                UPDATER_NAME,
            </if>
            <if test="updateRemark != null">
                UPDATE_REMARK,
            </if>
            <if test="bodyOrg != null">
                BODY_ORG,
            </if>
            <if test="result != null">
                `RESULT`,
            </if>
            <if test="invoiceNo != null">
                INVOICE_NO,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceTaxSystemRecordId != null">
                #{invoiceTaxSystemRecordId,jdbcType=BIGINT},
            </if>
            <if test="businessId != null">
                #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="interfaceType != null">
                #{interfaceType,jdbcType=VARCHAR},
            </if>
            <if test="runningStatus != null">
                #{runningStatus,jdbcType=VARCHAR},
            </if>
            <if test="retryNum != null">
                #{retryNum,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BOOLEAN},
            </if>
            <if test="urlPdf != null">
                #{urlPdf,jdbcType=VARCHAR},
            </if>
            <if test="urlXml != null">
                #{urlXml,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateRemark != null">
                #{updateRemark,jdbcType=VARCHAR},
            </if>
            <if test="bodyOrg != null">
                #{bodyOrg,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null">
                #{result,jdbcType=LONGVARCHAR},
            </if>
            <if test="invoiceNo != null">
                #{invoiceNo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.vedeng.erp.finance.domain.entity.InvoiceTaxSystemRecordEntity">
        <!--@mbg.generated-->
        update T_INVOICE_TAX_SYSTEM_RECORD
        <set>
            <if test="businessId != null">
                BUSINESS_ID = #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="interfaceType != null">
                INTERFACE_TYPE = #{interfaceType,jdbcType=VARCHAR},
            </if>
            <if test="runningStatus != null">
                RUNNING_STATUS = #{runningStatus,jdbcType=VARCHAR},
            </if>
            <if test="retryNum != null">
                RETRY_NUM = #{retryNum,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
            </if>
            <if test="urlPdf != null">
                URL_PDF = #{urlPdf,jdbcType=VARCHAR},
            </if>
            <if test="urlXml != null">
                URL_XML = #{urlXml,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateRemark != null">
                UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
            </if>
            <if test="bodyOrg != null">
                BODY_ORG = #{bodyOrg,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null">
                `RESULT` = #{result,jdbcType=LONGVARCHAR},
            </if>
            <if test="invoiceNo != null">
                INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
            </if>
        </set>
        where INVOICE_TAX_SYSTEM_RECORD_ID = #{invoiceTaxSystemRecordId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceTaxSystemRecordEntity">
        <!--@mbg.generated-->
        update T_INVOICE_TAX_SYSTEM_RECORD
        set BUSINESS_ID    = #{businessId,jdbcType=VARCHAR},
            INTERFACE_TYPE = #{interfaceType,jdbcType=VARCHAR},
            RUNNING_STATUS = #{runningStatus,jdbcType=VARCHAR},
            RETRY_NUM      = #{retryNum,jdbcType=INTEGER},
            IS_DELETE      = #{isDelete,jdbcType=BOOLEAN},
            URL_PDF        = #{urlPdf,jdbcType=VARCHAR},
            URL_XML        = #{urlXml,jdbcType=VARCHAR},
            ADD_TIME       = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME       = #{modTime,jdbcType=TIMESTAMP},
            CREATOR        = #{creator,jdbcType=INTEGER},
            CREATOR_NAME   = #{creatorName,jdbcType=VARCHAR},
            UPDATER        = #{updater,jdbcType=INTEGER},
            UPDATER_NAME   = #{updaterName,jdbcType=VARCHAR},
            UPDATE_REMARK  = #{updateRemark,jdbcType=VARCHAR},
            BODY_ORG       = #{bodyOrg,jdbcType=LONGVARCHAR},
            `RESULT`       = #{result,jdbcType=LONGVARCHAR},
            INVOICE_NO     = #{invoiceNo,jdbcType=VARCHAR}
        where INVOICE_TAX_SYSTEM_RECORD_ID = #{invoiceTaxSystemRecordId,jdbcType=BIGINT}
    </update>

    <!--auto generated by MybatisCodeHelper on 2023-09-22-->
    <select id="findByBusinessIdAndInterfaceType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE_TAX_SYSTEM_RECORD
        where BUSINESS_ID = #{businessId,jdbcType=VARCHAR}
          and INTERFACE_TYPE = #{interfaceType,jdbcType=VARCHAR}
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-09-25-->
    <select id="queryDownPdfError" resultType="com.vedeng.erp.finance.dto.InvoiceTaxSystemRecordDto">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE_TAX_SYSTEM_RECORD
        where INTERFACE_TYPE = 'SALE_DOWN'
          and URL_PDF = ''
          and IS_DELETE = 0
          and RUNNING_STATUS = 'FAIL'
    </select>


    <select id="queryDownXmlNeedDown" resultType="com.vedeng.erp.finance.dto.InvoiceTaxSystemRecordDto">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE_TAX_SYSTEM_RECORD
        where INTERFACE_TYPE = 'SALE_DOWN'
          and URL_PDF != ''
          and URL_XML = ''
          and IS_DELETE = 0
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-09-25-->
    <select id="selectByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE_TAX_SYSTEM_RECORD
        <where>
            <if test="invoiceTaxSystemRecordId != null">
                and INVOICE_TAX_SYSTEM_RECORD_ID = #{invoiceTaxSystemRecordId,jdbcType=BIGINT}
            </if>
            <if test="businessId != null and businessId != ''">
                and BUSINESS_ID = #{businessId,jdbcType=VARCHAR}
            </if>
            <if test="interfaceType != null and interfaceType != ''">
                and INTERFACE_TYPE = #{interfaceType,jdbcType=VARCHAR}
            </if>
            <if test="runningStatus != null and runningStatus != ''">
                and RUNNING_STATUS = #{runningStatus,jdbcType=VARCHAR}
            </if>
            <if test="retryNum != null">
                and RETRY_NUM = #{retryNum,jdbcType=INTEGER}
            </if>
            <if test="isDelete != null">
                and IS_DELETE = #{isDelete,jdbcType=BOOLEAN}
            </if>
            <if test="urlPdf != null and urlPdf != ''">
                and URL_PDF = #{urlPdf,jdbcType=VARCHAR}
            </if>
            <if test="urlXml != null and urlXml != ''">
                and URL_XML = #{urlXml,jdbcType=VARCHAR}
            </if>
            <if test="updateRemark != null and updateRemark != ''">
                and UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
            </if>
            <if test="bodyOrg != null and bodyOrg != ''">
                and BODY_ORG = #{bodyOrg,jdbcType=LONGVARCHAR}
            </if>
            <if test="result != null and result != ''">
                and `RESULT` = #{result,jdbcType=LONGVARCHAR}
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                and INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
            </if>
            <if test="addTime != null">
                and ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modTime != null">
                and MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
            </if>
            <if test="creator != null">
                and CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="updater != null">
                and UPDATER = #{updater,jdbcType=INTEGER}
            </if>
            <if test="creatorName != null and creatorName != ''">
                and CREATOR_NAME = #{creatorName,jdbcType=VARCHAR}
            </if>
            <if test="updaterName != null and updaterName != ''">
                and UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <update id="updateAndRetryNumAdd" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceTaxSystemRecordEntity">
        <selectKey resultType="Integer" order="AFTER" keyProperty="retryNum">
            select TITSR.RETRY_NUM
            FROM T_INVOICE_TAX_SYSTEM_RECORD TITSR
            WHERE TITSR.INVOICE_TAX_SYSTEM_RECORD_ID = #{invoiceTaxSystemRecordId,jdbcType=BIGINT}
        </selectKey>

        update T_INVOICE_TAX_SYSTEM_RECORD
        <set>
            <if test="businessId != null">
                BUSINESS_ID = #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="interfaceType != null">
                INTERFACE_TYPE = #{interfaceType,jdbcType=VARCHAR},
            </if>
            <if test="runningStatus != null">
                RUNNING_STATUS = #{runningStatus,jdbcType=VARCHAR},
            </if>
            RETRY_NUM = RETRY_NUM + 1,
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
            </if>
            <if test="urlPdf != null">
                URL_PDF = #{urlPdf,jdbcType=VARCHAR},
            </if>
            <if test="urlXml != null">
                URL_XML = #{urlXml,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateRemark != null">
                UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
            </if>
            <if test="bodyOrg != null">
                BODY_ORG = #{bodyOrg,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null">
                `RESULT` = #{result,jdbcType=LONGVARCHAR},
            </if>
            <if test="invoiceNo != null">
                INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
            </if>
        </set>
        where INVOICE_TAX_SYSTEM_RECORD_ID = #{invoiceTaxSystemRecordId,jdbcType=BIGINT}
    </update>



    <insert id="insertOrUpdateSelective"
            parameterType="com.vedeng.erp.finance.domain.entity.InvoiceTaxSystemRecordEntity">
        <!--@mbg.generated-->
        insert into T_INVOICE_TAX_SYSTEM_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceTaxSystemRecordId != null">
                INVOICE_TAX_SYSTEM_RECORD_ID,
            </if>
            <if test="businessId != null and businessId != ''">
                BUSINESS_ID,
            </if>
            <if test="interfaceType != null and interfaceType != ''">
                INTERFACE_TYPE,
            </if>
            <if test="runningStatus != null and runningStatus != ''">
                RUNNING_STATUS,
            </if>
            <if test="retryNum != null">
                RETRY_NUM,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="urlPdf != null and urlPdf != ''">
                URL_PDF,
            </if>
            <if test="urlXml != null and urlXml != ''">
                URL_XML,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME,
            </if>
            <if test="updateRemark != null and updateRemark != ''">
                UPDATE_REMARK,
            </if>
            <if test="bodyOrg != null and bodyOrg != ''">
                BODY_ORG,
            </if>
            <if test="result != null and result != ''">
                `RESULT`,
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                INVOICE_NO,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceTaxSystemRecordId != null">
                #{invoiceTaxSystemRecordId,jdbcType=BIGINT},
            </if>
            <if test="businessId != null and businessId != ''">
                #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="interfaceType != null and interfaceType != ''">
                #{interfaceType,jdbcType=VARCHAR},
            </if>
            <if test="runningStatus != null and runningStatus != ''">
                #{runningStatus,jdbcType=VARCHAR},
            </if>
            <if test="retryNum != null">
                #{retryNum,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BOOLEAN},
            </if>
            <if test="urlPdf != null and urlPdf != ''">
                #{urlPdf,jdbcType=VARCHAR},
            </if>
            <if test="urlXml != null and urlXml != ''">
                #{urlXml,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null and updaterName != ''">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateRemark != null and updateRemark != ''">
                #{updateRemark,jdbcType=VARCHAR},
            </if>
            <if test="bodyOrg != null and bodyOrg != ''">
                #{bodyOrg,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null and result != ''">
                #{result,jdbcType=LONGVARCHAR},
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                #{invoiceNo,jdbcType=VARCHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="invoiceTaxSystemRecordId != null">
                INVOICE_TAX_SYSTEM_RECORD_ID = #{invoiceTaxSystemRecordId,jdbcType=BIGINT},
            </if>
            <if test="businessId != null and businessId != ''">
                BUSINESS_ID = #{businessId,jdbcType=VARCHAR},
            </if>
            <if test="interfaceType != null and interfaceType != ''">
                INTERFACE_TYPE = #{interfaceType,jdbcType=VARCHAR},
            </if>
            <if test="runningStatus != null and runningStatus != ''">
                RUNNING_STATUS = #{runningStatus,jdbcType=VARCHAR},
            </if>
            <if test="retryNum != null">
                RETRY_NUM = #{retryNum,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
            </if>
            <if test="urlPdf != null and urlPdf != ''">
                URL_PDF = #{urlPdf,jdbcType=VARCHAR},
            </if>
            <if test="urlXml != null and urlXml != ''">
                URL_XML = #{urlXml,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateRemark != null and updateRemark != ''">
                UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
            </if>
            <if test="bodyOrg != null and bodyOrg != ''">
                BODY_ORG = #{bodyOrg,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null and result != ''">
                `RESULT` = #{result,jdbcType=LONGVARCHAR},
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getPdfUrlsByInvoiceNos" resultType="java.lang.String">
        select URL_PDF
        from T_INVOICE_TAX_SYSTEM_RECORD where URL_PDF != ''
                                           and IS_DELETE = 0
                                           and INTERFACE_TYPE = 'SALE_DOWN'
                                           and INVOICE_NO in
        <foreach collection="collection" item="i" open="(" close=")" separator=",">
            #{i,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getXmlUrlsByInvoiceNos" resultType="java.lang.String">
        select URL_XML
        from T_INVOICE_TAX_SYSTEM_RECORD where URL_XML != ''
                                           and IS_DELETE = 0
                                           and INTERFACE_TYPE = 'SALE_DOWN'
                                           and INVOICE_NO in
        <foreach collection="collection" item="i" open="(" close=")" separator=",">
            #{i,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getByInvoiceNo" resultType="com.vedeng.erp.finance.domain.entity.InvoiceTaxSystemRecordEntity">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE_TAX_SYSTEM_RECORD
        where INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
        and INTERFACE_TYPE = #{interfaceType,jdbcType=VARCHAR}
    </select>
</mapper>