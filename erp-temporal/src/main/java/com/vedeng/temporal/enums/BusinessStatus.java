package com.vedeng.temporal.enums;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 业务状态枚举
 * 标准化所有业务流程的状态定义
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-02
 */
public enum BusinessStatus {
    
    /**
     * 待处理 - 业务刚创建，等待处理
     */
    PENDING("待处理"),
    
    /**
     * 处理中 - 业务正在处理过程中
     */
    PROCESSING("处理中"),
    
    /**
     * 已审核 - 业务已通过审核
     */
    APPROVED("已审核"),
    
    /**
     * 已拒绝 - 业务审核被拒绝
     */
    REJECTED("已拒绝"),
    
    /**
     * 已完成 - 业务流程完全完成
     */
    COMPLETED("已完成"),
    
    /**
     * 失败 - 业务处理失败
     */
    FAILED("失败");
    
    private final String description;
    
    BusinessStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 判断是否为终态状态
     * 终态状态表示业务流程已结束，不会再发生变化
     *
     * @return true 如果是终态状态
     */
    @JsonIgnore
    public boolean isTerminal() {
        return this == APPROVED || this == COMPLETED ||
               this == REJECTED || this == FAILED;
    }

    /**
     * 判断是否为成功状态
     *
     * @return true 如果是成功状态
     */
    @JsonIgnore
    public boolean isSuccess() {
        return this == APPROVED || this == COMPLETED;
    }

    /**
     * 判断是否为失败状态
     *
     * @return true 如果是失败状态
     */
    @JsonIgnore
    public boolean isFailure() {
        return this == REJECTED || this == FAILED;
    }
    
    /**
     * 从字符串解析业务状态
     * 
     * @param statusStr 状态字符串
     * @return 对应的业务状态，如果无法识别则返回 PENDING
     */
    public static BusinessStatus fromString(String statusStr) {
        if (statusStr == null || statusStr.trim().isEmpty()) {
            return PENDING;
        }
        
        String upperStatus = statusStr.trim().toUpperCase();
        
        switch (upperStatus) {
            case "APPROVED":
            case "已审核":
            case "AUDIT_PASS":
            case "审核通过":
                return APPROVED;
                
            case "COMPLETED":
            case "已完成":
            case "FINISHED":
            case "完成":
                return COMPLETED;
                
            case "REJECTED":
            case "已拒绝":
            case "AUDIT_REJECT":
            case "审核拒绝":
                return REJECTED;
                
            case "FAILED":
            case "失败":
            case "ERROR":
            case "错误":
                return FAILED;
                
            case "PROCESSING":
            case "处理中":
            case "IN_PROGRESS":
            case "进行中":
                return PROCESSING;
                
            case "PENDING":
            case "待处理":
            case "WAITING":
            case "等待中":
            default:
                return PENDING;
        }
    }
    
    @Override
    public String toString() {
        return this.name() + "(" + this.description + ")";
    }
}
