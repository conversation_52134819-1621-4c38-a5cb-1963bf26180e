package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.processor.BatchAfterSaleSettlementAdjustmentProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchSaleSettlementAdjustmentProcessor;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.domain.command.KingDeeSaleOutStockCommand;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleSettlementAdjustmentEntityDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleOutStockCommandConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: Suqin
 * @date: 2023/1/1313:03
 **/
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeSaleAdjustment {

    @Autowired
    BatchSaleSettlementAdjustmentItemDtoMapper batchSaleSettlementAdjustmentItemDtoMapper;
    @Autowired
    BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;
    @Autowired
    BatchSaleSettlementAdjustmentProcessor batchSaleSettlementAdjustmentProcessor;
    @Autowired
    BatchSaleorderDtoMapper batchSaleorderDtoMapper;
    @Autowired
    BatchSaleSettlementAdjustmentDtoMapper batchSaleSettlementAdjustmentDtoMapper;
    @Autowired
    BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;
    @Autowired
    BatchAfterSaleSettlementAdjustmentProcessor batchAfterSaleSettlementAdjustmentProcessor;
    @Autowired
    KingDeeSaleOutStockCommandConvertor kingDeeSaleOutStockCommandConvertor;
    @Autowired
    KingDeeBaseApi kingDeeBaseApi;

    @Test
    public void saleTest() throws Exception {
        String beginTime = "2023-02-15 11:24:00";
        String endTime = "2023-02-15 11:24:00";

        BatchWarehouseGoodsOutInDto queryOutOrder = BatchWarehouseGoodsOutInDto
                .builder()
                .isVirtual(0)
                .isDelete(0)
                // 销售出库
                .outInType(2)
                .beginTime(StringUtils.isBlank(beginTime) ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(StringUtils.isBlank(endTime) ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .build();

        List<BatchWarehouseGoodsOutInDto> byAll = batchWarehouseGoodsOutInDtoMapper.findByAll(queryOutOrder);
        if (CollUtil.isEmpty(byAll)){
            return;
        }
        BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto = byAll.get(0);

        BatchSaleSettlementAdjustmentAndItemDto item = batchSaleSettlementAdjustmentProcessor.process(batchWarehouseGoodsOutInDto);

        BatchSaleSettlementAdjustmentDto batchSaleSettlementAdjustmentDto = item.getBatchSaleSettlementAdjustmentDto();
        List<BatchSaleSettlementAdjustmentItemDto> batchSaleSettlementAdjustmentItemDtoList = item.getBatchSaleSettlementAdjustmentItemDtoList();
        BatchSaleorderDto saleOrder = batchSaleorderDtoMapper.selectByPrimaryKey(batchSaleSettlementAdjustmentDto.getSaleorderId());

        batchSaleSettlementAdjustmentDtoMapper.insertSelective(batchSaleSettlementAdjustmentDto);
        log.info("销售正向产生调整单落表：{}", JSON.toJSONString(batchSaleSettlementAdjustmentDto));
        int adjustmentId = batchSaleSettlementAdjustmentDto.getSaleSettlementAdjustmentId();

        batchSaleSettlementAdjustmentItemDtoList.forEach(a -> {
            a.setSaleSettlementAdjustmentId(adjustmentId);
            log.info("销售正向产生调整单明细落表：{}", JSON.toJSONString(a));
            batchSaleSettlementAdjustmentItemDtoMapper.insertSelective(a);

            // 税率
            BigDecimal rate = new BigDecimal(batchSaleorderDtoMapper.getTaxRateByInvoiceType(saleOrder.getInvoiceType()));
            // 调整价税合计：含税调整金额（即含税调整单价*入库该行的数量）
            BigDecimal tzjshj = a.getAdjustmentAmount().multiply(new BigDecimal(String.valueOf(a.getAdjustmentAmount()))).setScale(2, RoundingMode.HALF_UP);
            // 调整金额（不含税）：调整价税合计/(1+ 税率)
            BigDecimal tzje = tzjshj.divide(rate.add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
            // 调整税额：调整价税合计- 调整金额
            BigDecimal tzse = tzjshj.subtract(tzje);

            KingDeeSaleSettlementAdjustmentEntityDto entity = new KingDeeSaleSettlementAdjustmentEntityDto();
            entity.setFQzokYsddh(batchSaleSettlementAdjustmentDto.getSaleorderNo());
            entity.setFQzokGsywdh(batchSaleSettlementAdjustmentDto.getSaleorderNo());
            entity.setFGzokYwlx("销售");
            entity.setFQzokSpdm(a.getSku());
            entity.setFQzokSpmc(a.getSkuName());
            entity.setFQzokPch(a.getBatchNumber());
            entity.setFQzokSn(a.getBarcodeFactory());
            entity.setFQzokTzje(tzje);
            entity.setFQzokTzse(tzse);
            entity.setFQzokTzjshj(tzjshj);
        });
    }

    @Test
    public void afterSale() throws Exception {
        String beginTime = "2023-02-15 11:24:00";
        String endTime = "2023-02-15 11:24:00";

        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Long begin;
        Long end;
        try {
            begin = StringUtils.isBlank(beginTime) ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : sf.parse(beginTime).getTime();
            end = StringUtils.isBlank(endTime) ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : sf.parse(endTime).getTime();
        } catch (ParseException e) {
            log.error("时间转化异常：{}，{}", beginTime, endTime);
            throw new RuntimeException("时间转化异常", e);
        }

        BatchAfterSalesDto queryAdjustment = BatchAfterSalesDto
                .builder()
                .type(539)
                .atferSalesStatus(2)
                .beginTime(begin)
                .endTime(end)
                .build();
        List<BatchAfterSalesDto> saleBackBySaleorderId = batchAfterSalesDtoMapper.findSaleBackBySaleorderId(queryAdjustment);
        BatchAfterSalesDto afterSalesDto = saleBackBySaleorderId.get(0);

        BatchSaleSettlementAdjustmentAndItemDto item = batchAfterSaleSettlementAdjustmentProcessor.process(afterSalesDto);



        BatchSaleSettlementAdjustmentDto adjustmentDto = item.getBatchSaleSettlementAdjustmentDto();
        List<BatchSaleSettlementAdjustmentItemDto> adjustmentItemDtoList = item.getBatchSaleSettlementAdjustmentItemDtoList();

        batchSaleSettlementAdjustmentDtoMapper.insertSelective(adjustmentDto);
        log.info("销售售后产生调整单落表：{}", JSON.toJSONString(adjustmentDto));
        // 主键
        int adjustmentId = adjustmentDto.getSaleSettlementAdjustmentId();

        adjustmentItemDtoList.forEach(a -> {
            a.setSaleSettlementAdjustmentId(adjustmentId);
            log.info("销售售后产生调整单明细落表：{}", JSON.toJSONString(a));
            batchSaleSettlementAdjustmentItemDtoMapper.insertSelective(a);
        });
    }

    @Test
    public void testOut(){
        KingDeeSaleOutStockDto kingDeeSaleOutStockDto = new KingDeeSaleOutStockDto();
        kingDeeSaleOutStockDto.setFid("0");
        kingDeeSaleOutStockDto.setFBillNo("SUQIN_TEST_OUT7");
        kingDeeSaleOutStockDto.setF_qzok_bddjtid("341");
        kingDeeSaleOutStockDto.setF_qzok_gsbm(" ");
        kingDeeSaleOutStockDto.setFDate("2023-02-15 11:24:00");
        kingDeeSaleOutStockDto.setFCustomerID("36881");

        List<KingDeeSaleOutStockDetailDto> detailDtoList = new ArrayList<>();
        KingDeeSaleOutStockDetailDto temp1 = new KingDeeSaleOutStockDetailDto();
        temp1.setFMaterialID("V501985");
        temp1.setFRealQty(BigDecimal.valueOf(5));
        temp1.setFIsFree(true);
        temp1.setFTaxPrice(BigDecimal.valueOf(0));
        temp1.setFEntryTaxRate(BigDecimal.valueOf(13));
        temp1.setF_QZOK_SFZF("普发");
        temp1.setF_QZOK_YSDDH("BFZ014893823020230215");
        temp1.setF_QZOK_GSYWDH("BFZ014893823020230215");
        temp1.setF_QZOK_YWLX("销售订单");
        temp1.setF_QZOK_PCH("");
        temp1.setF_QZOK_XLH("1");
        temp1.setF_QZOK_SFAT2("是");
        temp1.setF_QZOK_BDDJHID("570");
        detailDtoList.add(temp1);

        KingDeeSaleOutStockDetailDto temp2 = new KingDeeSaleOutStockDetailDto();
        temp2.setFMaterialID("V514104");
        temp2.setFRealQty(BigDecimal.valueOf(5));
        temp2.setFIsFree(false);
        temp2.setFTaxPrice(BigDecimal.valueOf(1));
        temp2.setFEntryTaxRate(BigDecimal.valueOf(13));
        temp2.setF_QZOK_SFZF("普发");
        temp2.setF_QZOK_YSDDH("BFZ014893823020230215");
        temp2.setF_QZOK_GSYWDH("BFZ014893823020230215");
        temp2.setF_QZOK_YWLX("销售订单");
        temp2.setF_QZOK_PCH("");
        temp2.setF_QZOK_XLH("1");
        temp2.setF_QZOK_SFAT2("是");
        temp2.setF_QZOK_BDDJHID("573");
        detailDtoList.add(temp2);

        kingDeeSaleOutStockDto.setFEntity(detailDtoList);

        ArrayList<String> needReturnFields = new ArrayList<>();
        needReturnFields.add("fEntity.fEntryId");
        needReturnFields.add("fEntity.F_QZOK_BDDJHID");

        KingDeeSaleOutStockCommand saleOutStockCommand = kingDeeSaleOutStockCommandConvertor.toCommand(kingDeeSaleOutStockDto);
        log.info("销售实物商品出库单推送：{}", JSON.toJSONString(kingDeeSaleOutStockDto));
        kingDeeBaseApi.save(new SaveExtCommand<>(saleOutStockCommand, kingDeeSaleOutStockDto.getFormId(), needReturnFields));
    }

    @Test
    public void testSalePush(){
        BatchSaleSettlementAdjustmentDto queryAdjustment = new BatchSaleSettlementAdjustmentDto();
        queryAdjustment.setAdjustmentType(1);
        List<BatchSaleSettlementAdjustmentDto> adjustments = batchSaleSettlementAdjustmentDtoMapper.findByAll(queryAdjustment);
        System.out.println(adjustments);

    }
}
