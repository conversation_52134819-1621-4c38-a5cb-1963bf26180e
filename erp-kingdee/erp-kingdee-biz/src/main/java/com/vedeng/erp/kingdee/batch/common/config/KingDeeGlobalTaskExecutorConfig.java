package com.vedeng.erp.kingdee.batch.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/1 15:07
 **/
@Configuration
public class KingDeeGlobalTaskExecutorConfig {

    @Value("${kingDee_CorePoolSize}")
    private Integer corePoolSize;

    @Value("${kingDee_MaxPoolSize}")
    private Integer maxPoolSize;

    @Value("${kingDee_QueueCapacity}")
    private Integer queueCapacity;


    @Bean
    public TaskExecutor kingDeeGlobalTaskExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(corePoolSize);
        taskExecutor.setMaxPoolSize(maxPoolSize);
        taskExecutor.setQueueCapacity(queueCapacity);
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        return taskExecutor;
    }
}
