package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.kingdee.batch.common.enums.CostActEnum;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchExpressCostDto;
import com.vedeng.erp.kingdee.domain.entity.KingDeeExpressCostEntity;
import com.vedeng.erp.kingdee.dto.KingDeeExpressCostDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeExpressCostMapper;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class ExpressCostPushUpdateProcessor extends BaseProcessor<BatchExpressCostDto,KingDeeExpressCostDto> {

    @Autowired
    private KingDeeExpressCostMapper kingDeeExpressCostMapper;

    @Override
    public KingDeeExpressCostDto doProcess(BatchExpressCostDto batchExpressCostDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("[update]ExpressCostPushUpdateProcessor.doProcess，input：{}", JSONObject.toJSONString(batchExpressCostDto));
        List<KingDeeExpressCostEntity> kingDeeExpressCostEntities = kingDeeExpressCostMapper.selectByBddjbh(batchExpressCostDto.getUniqueKey());
        log.info("[update]ExpressCostPushUpdateProcessor.doProcess，selectByBddjbh：{}", JSONObject.toJSONString(kingDeeExpressCostEntities));
        if (CollectionUtil.isEmpty(kingDeeExpressCostEntities)){
            StringBuilder sb = new StringBuilder();
            sb.append("根据快递成本唯一键未查询到信息,唯一键：").append(batchExpressCostDto.getUniqueKey());
            log.info(sb.toString());
            throw new ServiceException(sb.toString());
        }
        if (kingDeeExpressCostEntities.size() != 1){
            StringBuilder sb = new StringBuilder();
            sb.append("根据快递成本唯一键查询到多条数据，唯一键：").append(batchExpressCostDto.getUniqueKey());
            throw new ServiceException(sb.toString());
        }
        String fid = kingDeeExpressCostEntities.get(0).getFid();

        return KingDeeExpressCostDto.builder()
                .fid(fid)
                .fQzokYsddh(batchExpressCostDto.getSaleorderNo())
                .fQzokGsywdh(batchExpressCostDto.getSaleorderNo())
                .fQzokCrkdh(batchExpressCostDto.getOutInNo())
                .fQzokKddh(batchExpressCostDto.getLogisticsNo())
                .fQzokWlbm(batchExpressCostDto.getSku())
                .fQzokXlh(batchExpressCostDto.getBarcodeFactory())
                .fQzokPch(batchExpressCostDto.getBatchNumber())
                .fQzokFhsl(Objects.isNull(batchExpressCostDto.getShareNum()) ? null : String.valueOf(batchExpressCostDto.getShareNum()))
                .fQzokCb(Objects.isNull(batchExpressCostDto.getShareExpressAmountNoTax()) ? null : String.valueOf(batchExpressCostDto.getShareExpressAmountNoTax()))
                .fQzokSjr(batchExpressCostDto.getTakeTraderContactName())
                .fQzokDh(batchExpressCostDto.getTakeTraderContactTelephone())
                .fQzokDz(batchExpressCostDto.getTakeTraderAddress())
                .fQzokBddjbh(batchExpressCostDto.getUniqueKey())
                .fQzokSfsc(CostActEnum.isDelete(batchExpressCostDto.getCostActFlag()))
                .fQzokSfjrcb("Y")
                .FQzokWlgs(batchExpressCostDto.getLogistics())
                .FQzokSfzp(Objects.nonNull(batchExpressCostDto.getIsGift()) && KingDeeConstant.ONE.equals(batchExpressCostDto.getIsGift()) ? 1 : 0)
                .build();
    }
}
