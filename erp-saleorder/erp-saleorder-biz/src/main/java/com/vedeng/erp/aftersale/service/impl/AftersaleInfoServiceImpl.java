package com.vedeng.erp.aftersale.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.rabbitmq.MsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.aftersales.dao.AfterSalesInvoiceMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesInvoice;
import com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.service.AuthService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.base.ResultInfo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.aftersale.domain.dto.AfterSalesDetailDto;
import com.vedeng.erp.aftersale.domain.dto.PushAfterSalesAttachmentDto;
import com.vedeng.erp.aftersale.domain.dto.PushAfterSalesDto;
import com.vedeng.erp.aftersale.domain.dto.PushAfterSalesGoodsDto;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesDetailEntity;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesEntity;
import com.vedeng.erp.aftersale.dto.*;
import com.vedeng.erp.aftersale.mapper.*;
import com.vedeng.erp.aftersale.mapstruct.AfterSaleConvertor;
import com.vedeng.erp.aftersale.mapstruct.AfterSaleDetailApiConvertor;
import com.vedeng.erp.aftersale.mapstruct.AfterSalesDetailConvertor;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.buyorder.service.BuyorderInfoSyncService;
import com.vedeng.erp.common.constants.AfterSalesConstants;
import com.vedeng.erp.finance.api.CustomerAccountApiService;
import com.vedeng.erp.finance.constants.FinanceConstant;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.PayApplyDto;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.saleorder.dto.AfterSalesGoodsDto;
import com.vedeng.erp.saleorder.dto.OrderFinanceInfoDto;
import com.vedeng.erp.system.dto.BankInfoDto;
import com.vedeng.erp.system.dto.RegionDto;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.service.BankApiService;
import com.vedeng.erp.system.service.RegionApiService;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.erp.trader.api.CustomerAccountApi;
import com.vedeng.erp.trader.dto.CustomerBankAccountApiDto;
import com.vedeng.erp.trader.dto.CustomerBankAccountQueryReqDto;
import com.vedeng.erp.trader.dto.CustomerBankAccountReqDto;
import com.vedeng.finance.model.Invoice;
import com.vedeng.orderstream.aftersales.constant.AfterSalesProcessEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.saleorder.service.impl
 * @Date 2022/1/10 19:52
 */
@Slf4j
@Service
public class AftersaleInfoServiceImpl implements AfterSalesApiService {

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private AfterSalesInvoiceMapper afterSalesInvoiceMapper;

    @Resource
    private BuyorderInfoSyncService buyorderInfoSyncService;

    @Autowired
    private AfterSaleAuditInfoMapper afterSaleAuditInfoMapper;

    @Autowired
    private AfterSalesBizMapper afterSalesBizMapper;

    @Autowired
    private AfterSaleConvertor afterSaleConvertor;

    @Autowired
    private AfterSalesDetailConvertor afterSalesDetailConvertor;

    @Autowired
    private InvoiceApiService invoiceApiService;
    @Autowired
    private AfterSalesDetailMapper afterSalesDetailMapper;

    @Autowired
    private AfterSaleDetailApiConvertor afterSaleDetailApiConvertor;

    @Autowired
    private RegionApiService regionApiService;

    @Autowired
    MsgProducer msgProducer;

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    @Autowired
    private TrackStrategyFactory trackStrategyFactory;

    @Autowired
    private AuthService authService;

    @Autowired
    private CustomerAccountApi customerAccountApi;

    @Autowired
    private PayApplyApiService payApplyApiService;

    @Autowired
    private BankApiService bankApiService;

    @Autowired
    private CustomerAccountApiService customerAccountApiService;

    @Value("${oss_http}")
    private String ossHttp;


    @Override
    public List<String> getCannotReturnSkuByAfterSalesId(Integer afterSalesId) {
        return afterSaleAuditInfoMapper.getCannotReturnSkuByAfterSalesId(afterSalesId);
    }

    @Override
    public Integer getAfterSalesNumBySaleorderGoodsId(Integer saleorderGoodsId) {
        return afterSalesMapper.getAfterSalesNumBySaleorderGoodsId(saleorderGoodsId);
    }

    /**
     * 查看售后单详情
     *
     * @param afterSalesId
     * @return
     */
    @Override
    public AfterSalesWithDetailDto findAfterSalesDetailByAfterSalesId(Integer afterSalesId) {
        AfterSalesDetailDto afterSalesDetailDto = afterSalesBizMapper.findAfterSalesDetailByAfterSalesId(afterSalesId);
        AfterSalesWithDetailDto convert = afterSalesDetailConvertor.toDto(afterSalesDetailDto);
        return convert;
    }

    @Override
    public AfterSalesDetailApiDto getAfterSalesDetailByAfterSalesId(Integer afterSalesId) {
        return afterSaleDetailApiConvertor.toDto(afterSalesDetailMapper.findByAfterSalesId(afterSalesId));
    }

    @Override
    public List<AfterSalesGoodsDto> findGoodsByAfterSalesIdAndGoodsType(Integer afterSalesId, Integer goodsType) {
        return afterSalesMapper.getByAfterSalesIdAndGoodsType(afterSalesId, goodsType);
    }

    @Override
    public OrderFinanceInfoDto getAftersaleFinanceInfo(Integer afterSalesId) {
        return afterSalesBizMapper.getAftersaleFinanceInfo(afterSalesId);
    }

    @Override
    public void saveMakeOutInvoiceStatus(Integer afterSalesId, Integer status) {
        afterSalesMapper.saveMakeOutInvoiceStatus(afterSalesId, status);
    }

    @Override
    public List<AfterSalesDto> getOngoingAfterSalesByOrderId(Integer saleOrderId) {
        List<AfterSalesEntity> afterSalesEntityList = afterSalesBizMapper.getOngoingAfterSalesByOrderId(saleOrderId);
        return afterSaleConvertor.toDto(afterSalesEntityList);
    }

    @Override
    public List<AfterSalesDto> getCompletedAfterSalesByOrderId(Integer saleOrderId) {
        List<AfterSalesEntity> afterSalesEntityList = afterSalesBizMapper.getCompletedAfterSalesByOrderId(saleOrderId);
        return afterSaleConvertor.toDto(afterSalesEntityList);
    }

    @Override
    public AfterSalesDto queryInfoByNo(String afterSalesNo) {
        AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(afterSalesNo);
        AfterSalesDto afterSalesDto = new AfterSalesDto();
        afterSalesDto.setAfterSalesId(afterSales.getAfterSalesId());
        afterSalesDto.setOrderId(afterSales.getOrderId());
        return afterSalesDto;
    }

    @Override
    public void updateAfterSalesInvoiceStatus(Integer afterSalesId, Integer invoiceId) {
        Invoice invoice = new Invoice();
        invoice.setInvoiceId(invoiceId);
        invoice.setAfterSalesId(afterSalesId);
        AfterSalesInvoice afterSalesInvoice = afterSalesInvoiceMapper.selectOneByRelatedId(afterSalesId,invoiceId);
        log.info("根据售后单id{},发票id{},查询到的售后单退票信息{},",afterSalesId,invoiceId, JSON.toJSONString(afterSalesInvoice));
        if(afterSalesInvoice != null) {
            afterSalesInvoice.setStatus(ErpConst.ONE);
            afterSalesInvoiceMapper.update(afterSalesInvoice);
        }else {
            log.error("未查询到冲销的退票发票信息");
        }
        //计算售后单退票状态
        buyorderInfoSyncService.calculateAndUpdateInvoiceRefundStatus(afterSalesId);
    }

    @Override
    public AfterSalesDto queryInfoById(Integer afterSalesId) {
        AfterSalesDto afterSalesDto = afterSalesMapper.queryAftersalesInfoById(afterSalesId);
        if(SysOptionConstant.ID_548.equals(afterSalesDto.getType())){
            //采购仅退票查询发票信息
            List<ReturnBuyorderInvoiceDto> returnBuyorderInvoiceDtoList = afterSalesInvoiceMapper.queryAfterBuyorderInvoice(afterSalesId);
            afterSalesDto.setReturnBuyorderInvoiceDto(returnBuyorderInvoiceDtoList);
        }
        return afterSalesDto;
    }

    @Override
    public int updateAfterStatusById(Integer afterSalesId, Integer afterStatus) {
        AfterSales afterSales = new AfterSales();
        afterSales.setAfterSalesId(afterSalesId);
        afterSales.setAtferSalesStatus(afterStatus);
        return afterSalesMapper.updateByPrimaryKeySelective(afterSales);
    }

    @Override
    public List<Map<String, Object>> getCommunicateAfterSaleInfo(List<Integer> afterSaleIdList) {
        return afterSalesMapper.getCommunicateAfterSaleInfo(afterSaleIdList);
    }

    @Override
    public List<AfterSalesDto> findByOrderIdGetSaleOrderAfterSale(Integer saleOrderId) {
        return afterSaleConvertor.toDto(afterSalesBizMapper.findByOrderIdGetSaleOrderAfterSale(saleOrderId));
    }

    /**
     * 根据售后单id查询售后商品信息
     *
     * @param afterSalesId 售后单id
     * @return List<AfterSalesGoodsDto>
     */
    @Override
    public List<AfterSalesGoodsDto> getAfterSalesGoodsByAfterSalesId(Integer afterSalesId) {
        return afterSalesMapper.getByAfterSalesId(afterSalesId);
    }

    @Override
    public List<AfterSalesDto> findUnderwayThOrTpByOrderIds(List<Integer> relatedIdList) {
        return afterSaleConvertor.toDto(afterSalesBizMapper.findUnderwayThOrTpByOrderIds(relatedIdList));
    }

    /**
     * 根据售后单id查询信息
     *
     * @param afterSalesId afterSalesId
     * @return AfterSalesDto
     */
    @Override
    public AfterSalesDto getAfterSalesById(Integer afterSalesId) {
        return afterSaleConvertor.toDto(afterSalesBizMapper.selectByPrimaryKey(afterSalesId));
    }

    @Override
    public AfterSalesDto getAfterSalesByNo(String afterSalesNo) {
        return afterSaleConvertor.toDto(afterSalesBizMapper.selectByAfterSalesNo(afterSalesNo));
    }

    /**
     * 尝试更新开票状态
     * @param invoiceApplyDto
     */
    @Override
    public void tryUpdateInvoiceStatus(InvoiceApplyDto invoiceApplyDto) {
        // 查询售后需开票的金额（服务费金额）
        Integer afterSalesId = invoiceApplyDto.getRelatedId();
        AfterSalesDetailDto afterSalesDetailDto = afterSalesBizMapper.findAfterSalesDetailByAfterSalesId(afterSalesId);
        BigDecimal serviceAmount = Optional.ofNullable(afterSalesDetailDto).map(AfterSalesDetailDto::getServiceAmount).orElse(BigDecimal.ZERO);

        // 历史已开票金额
        BigDecimal openedAmount = invoiceApiService.calAmount(invoiceApplyDto.getRelatedId());

        Integer invoiceStatus = getInvoiceStatus(openedAmount,serviceAmount);
        log.info("开票状态计算结果：{}",invoiceStatus);

        if (Objects.nonNull(invoiceStatus)) {
            // 更新T_AFTER_SALES_DETAIL表开票状态
            AfterSalesDetailEntity updateAfterDetailEntity = new AfterSalesDetailEntity();
            updateAfterDetailEntity.setAfterSalesId(afterSalesId);
            updateAfterDetailEntity.setInvoiceStatus(invoiceStatus);
            updateAfterDetailEntity.setInvoiceTime(DateUtil.sysTimeMillis());
            afterSalesBizMapper.updateDetailByAfterSalesId(updateAfterDetailEntity);
            log.info("T_AFTER_SALES_DETAIL开票状态更新完成");

            // 更新T_AFTER_SALES表开票状态
            AfterSalesDetailEntity updateAfterEntity = new AfterSalesDetailEntity();
            updateAfterEntity.setAfterSalesId(afterSalesId);
            updateAfterEntity.setInvoiceMakeoutStatus(invoiceStatus);
            updateAfterEntity.setModeTime(DateUtil.sysTimeMillis());
            afterSalesBizMapper.updateByAfterSalesId(updateAfterEntity);
            log.info("T_AFTER_SALES开票状态更新完成");
        }
    }

    /**
     * 计算开票状态
     * @param serviceAmount 服务费金额
     * @return 开票状态
     */
    private Integer getInvoiceStatus(BigDecimal openedAmount, BigDecimal serviceAmount) {
        log.info("开始计算明细表开票状态：serviceAmount:{},openedAmount:{}",serviceAmount ,openedAmount);
        // 计算开票状态 0未开票 1部分开票 2全部开票
        if (serviceAmount.compareTo(openedAmount) == 0){
            return 2;
        }
        if (serviceAmount.compareTo(openedAmount) > 0){
            return 1;
        }
        log.error("特殊场景：计算开票时已开票金额超过了服务费金额，为全部开票");
        return 2;
    }

    /**
     * 计算开票状态
     * @param serviceAmount 服务费金额
     * @param amount 已开票金额
     * @return 开票状态
     */
    private Integer getMakeOutInvoiceStatus(BigDecimal serviceAmount, BigDecimal amount,Integer afterType) {
        log.info("开始计算主表开票状态：amount:{},serviceAmount:{}", amount, serviceAmount);
        if (Arrays.asList(
                AfterSalesProcessEnum.AFTERSALES_TH.getCode(),
                AfterSalesProcessEnum.AFTERSALES_HH.getCode()).contains(afterType)){
            if(Objects.isNull(amount)){
                // copy:存在开票申请，不存在开票记录，开票状态为未开票
                return ErpConst.ONE;
            }
            // copy:存在开票申请，存在开票记录，开票状态为全部开票
            return ErpConst.TWO;
        }


        if (Arrays.asList(
                AfterSalesProcessEnum.AFTERASALES_THIRD_AT.getCode(),
                AfterSalesProcessEnum.AFTERASALES_THIRD_WX.getCode(),
                AfterSalesProcessEnum.AFTERSALES_ATY.getCode(),
                AfterSalesProcessEnum.AFTERSALES_ATN.getCode(),
                AfterSalesProcessEnum.AFTERSALES_WX.getCode(),
                // 销售售后安调 已弃用
                AfterSalesProcessEnum.AFTERSALES_AT.getCode()).contains(afterType)){
            // 计算开票状态 0未开票 1部分开票 2全部开票
            if (BigDecimal.ZERO.compareTo(serviceAmount) == 0){
                return ErpConst.ZERO;
            }
            if (serviceAmount.compareTo(BigDecimal.ZERO) > 0 && amount.compareTo(BigDecimal.ZERO) == 0 ){
                return ErpConst.ONE;
            }
            if (serviceAmount.compareTo(BigDecimal.ZERO) > 0 && amount.compareTo(BigDecimal.ZERO) != 0 ){
                return ErpConst.TWO;
            }
        }
        return null;
    }

    @Override
    public AfterSaleSkuInfoDto getGoodsNameModelAndSpecByAfterSalesGoodsId(Integer detailGoodsId) {
        return afterSalesDetailMapper.getGoodsNameModelAndSpecByAfterSalesGoodsId(detailGoodsId);
    }

    @Override
    public List<AfterSalesGoodsDto> getAfterSalesNum(Integer saleOrderId) {
        return afterSalesDetailMapper.getAfterSalesNum(saleOrderId);
    }

    @Override
    public List<AfterSalesDto> getAfterSaleListBySaleId(Integer saleorderId) {
        return afterSalesMapper.getAfterSaleListBySaleId(saleorderId);
    }

    @Override
    public Integer queryAfterSaleInProgress(Integer saleOrderId) {
        return afterSalesMapper.queryAfterSaleInProgress(saleOrderId);
    }

    @Override
    public int getPurchaseAfterSalesGoodsNum(Integer saleorderGoodsId) {
        return afterSalesMapper.getPurchaseAfterSalesGoodsNum(saleorderGoodsId);
    }

    @Override
    public BigDecimal getPurchaseAfterSalesGoodsAmount(Integer saleorderGoodsId) {
        return afterSalesMapper.getPurchaseAfterSalesGoodsAmount(saleorderGoodsId);
    }

    @Override
    public void pushAfterSalesToFrontMall(Integer afterSalesId) {
        if (Objects.isNull(afterSalesId)) {
            log.info("推送售后单到前台商城,售后单id为空");
            return;
        }
        AfterSalesEntity afterSales = afterSalesBizMapper.selectByPrimaryKey(afterSalesId);
        if (Objects.isNull(afterSales) || !ErpConstant.SALE.equals(afterSales.getSubjectType())) {
            log.info("推送售后单到前台商城,该售后单并非销售售后单,无需推送,afterSalesId:{}", afterSalesId);
            return;
        }
        PushAfterSalesDto pushAfterSalesDto = afterSalesBizMapper.getPushInfo(afterSalesId);
        if (Objects.isNull(pushAfterSalesDto)) {
            log.info("推送售后单到前台商城,未查询到售后单信息,afterSalesId:{}", afterSalesId);
            return;
        }
        // 封装收货联系人信息
        Integer areaId = pushAfterSalesDto.getAreaId();
        List<RegionDto> regionDtoList =  regionApiService.getAllRegion();
        PushAfterSalesDto.ContactData contactData = new PushAfterSalesDto.ContactData();
        contactData.setContactUserName(pushAfterSalesDto.getContactUserName());
        contactData.setContactMobile(pushAfterSalesDto.getContactMobile());
        contactData.setContactAddress(pushAfterSalesDto.getContactAddress());
        if (Objects.nonNull(areaId) && CollUtil.isNotEmpty(regionDtoList)) {
            Map<Integer, RegionDto> regionDtoMap = regionDtoList.stream().collect(Collectors.toMap(RegionDto::getRegionId, Function.identity()));
            this.setContactDataInfo(contactData, areaId, regionDtoMap);
        }
        pushAfterSalesDto.setContactData(contactData);
        // 封装附件信息
        List<PushAfterSalesAttachmentDto> attachments = afterSalesBizMapper.getAfterSalesAttachmentById(pushAfterSalesDto.getAfterSalesId(),pushAfterSalesDto.getAfterSalesTypeId());
        if(CollUtil.isNotEmpty(attachments)){
            attachments.forEach(e -> e.setAttachmentsUrl(ossHttp + e.getAttachmentsUrl()));
        }
        pushAfterSalesDto.setAttachments(attachments);
        // 封装商品信息
        List<PushAfterSalesGoodsDto> pushGoodsList =  afterSalesBizMapper.getPushGoodsByAfterSalesId(pushAfterSalesDto.getAfterSalesId());
        pushGoodsList.forEach(goods -> goods.setGiftFlag(ErpConstant.ONE.equals(goods.getIsGift()) ? "Y" : "N"));
        pushAfterSalesDto.setShOrderGoodsList(pushGoodsList);
        // 仅退票和丢票有发票信息
        if (AfterSalesProcessEnum.AFTERSALES_TP.getCode().equals(pushAfterSalesDto.getAfterSalesTypeId()) || AfterSalesProcessEnum.LOST_TICKET.getCode().equals(pushAfterSalesDto.getAfterSalesTypeId())) {
            List<AfterSalesInvoiceVo> afterSalesInvoiceVos = afterSalesInvoiceMapper.getPushInvoiceByAfterSalesId(pushAfterSalesDto.getAfterSalesId());
            if (CollUtil.isNotEmpty(afterSalesInvoiceVos)) {
                List<Integer> invoiceDtoList = afterSalesInvoiceVos.stream().map(AfterSalesInvoiceVo::getInvoiceId).collect(Collectors.toList());
                pushAfterSalesDto.setInvoiceList(invoiceDtoList);
            }
        }
        try {
            log.info("推送售后单到前台商城,发送消息至mq,推送信息:{}", JSONUtil.toJsonStr(pushAfterSalesDto));
            msgProducer.sendMsg(RabbitConfig.PUSH_AFTERSALES_TO_FRONTMALL_EXCHANGE, RabbitConfig.PUSH_AFTERSALES_TO_FRONTMALL_ROUTINGKEY, JSONUtil.toJsonStr(pushAfterSalesDto));
        } catch (Exception e) {
            log.error("推送售后单到前台商城,发送消息至mq异常,推送信息:{}", JSONUtil.toJsonStr(pushAfterSalesDto), e);
            throw new ServiceException("推送售后单到前台商城,发送消息至mq异常");
        }
    }

    @Override
    public void trackCompleteClose(Integer afterSalesId) {
        try {
            AfterSalesEntity afterSales = afterSalesBizMapper.selectByPrimaryKey(afterSalesId);
            if (Objects.isNull(afterSales)) {
                log.info("ERP客户档案时间轴,售后完结,未查询到售后单信息,afterSalesId:{}", afterSalesId);
                return;
            }
            Integer subjectType = afterSales.getSubjectType();
            Integer type = afterSales.getType();
            Integer afterSalesStatus = afterSales.getAtferSalesStatus();
            if (!FinanceConstant.ID_535.equals(subjectType) && !FinanceConstant.ID_537.equals(subjectType)) {
                log.info("ERP客户档案时间轴,售后完结,非销售以及第三方售后,afterSalesId:{}", afterSalesId);
                return;
            }
            if (!ErpConstant.TWO.equals(afterSalesStatus)) {
                log.info("ERP客户档案时间轴,售后完结,售后单状态非完结,afterSalesId:{}", afterSalesId);
                return;
            }
            AfterSalesDetailEntity afterSalesDetail = afterSalesDetailMapper.findByAfterSalesId(afterSalesId);
            if (Objects.isNull(afterSalesDetail)) {
                log.info("ERP客户档案时间轴,售后完结,未查询到售后单详情信息,afterSalesId:{}", afterSalesId);
                return;
            }
            // 开始添加埋点信息
            Map<String, Object> trackParams = new HashMap<>();
            // 客户id
            trackParams.put("traderId", afterSalesDetail.getTraderId());
            // 创建人信息
            User user = authService.getUserById(afterSales.getAtferSalesStatusUser());
            trackParams.put("track_user", user);
            // 订单号
            trackParams.put("orderNo", afterSales.getOrderNo());
            // 售后类型
            SysOptionDefinitionDto sysOptionDefinition = sysOptionDefinitionApiService.getOptionDefinitionById(type);
            trackParams.put("afterSaleTypeName", Objects.nonNull(sysOptionDefinition) ? sysOptionDefinition.getTitle() : "");
            TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.AFTER_SALE_ORDER_COMPLETE);
            TrackParamsData trackParamsData = new TrackParamsData();
            trackParamsData.setTrackParams(trackParams);
            trackParamsData.setTrackTime(new Date());
            trackParamsData.setTrackResult(ResultInfo.success());
            trackParamsData.setEventTrackingEnum(EventTrackingEnum.AFTER_SALE_ORDER_COMPLETE);
            trackStrategy.track(trackParamsData);
        } catch (Exception e) {
            log.error("埋点：{}，失败，不影响正常业务",EventTrackingEnum.AFTER_SALE_ORDER_COMPLETE.getArchivedName(),e);
        }
    }

    @Override
    public void handleCustomerBankAccount(Integer payApplyId, Integer afterSalesId) {
        log.info("售后退货退款付款申请通过,处理客户银行账号信息,afterSalesId:{},payApplyId:{}", afterSalesId, payApplyId);
        AfterSalesEntity afterSales = afterSalesBizMapper.selectByPrimaryKey(afterSalesId);
        if (Objects.isNull(afterSales)) {
            log.info("售后退货退款付款申请通过,处理客户银行账号信息,未查询到售后单信息,afterSalesId:{}", afterSalesId);
            return;
        }
        Integer subjectType = afterSales.getSubjectType();
        Integer type = afterSales.getType();
        if (!ErpConstant.SALE.equals(subjectType)) {
            log.info("售后退货退款付款申请通过,处理客户银行账号信息,非销售售后,subjectType:{}", subjectType);
            return;
        }
        if (!FinanceConstant.TH.equals(type) && !FinanceConstant.TK.equals(type)) {
            log.info("售后退货退款付款申请通过,处理客户银行账号信息,非退货退款,type:{}", type);
        }
        AfterSalesDetailEntity afterSalesDetail = afterSalesDetailMapper.findByAfterSalesId(afterSalesId);
        if (Objects.isNull(afterSalesDetail)) {
            log.info("售后退货退款付款申请通过,处理客户银行账号信息,未查询到售后单详情信息,afterSalesId:{}", afterSalesId);
            return;
        }
        Integer refund = afterSalesDetail.getRefund();
        Integer traderId = afterSalesDetail.getTraderId();
        if (!ErpConstant.TWO.equals(refund)) {
            log.info("售后退货退款付款申请通过,处理客户银行账号信息,款项退还!=退给客户,refund:{}", refund);
            return;
        }
        PayApplyDto payApplyDto = payApplyApiService.queryInfoByPayApplyId(payApplyId);
        if (Objects.isNull(payApplyDto)) {
            log.info("售后退货退款付款申请通过,处理客户银行账号信息,未查询到付款申请信息,afterSalesId:{}", afterSalesId);
            return;
        }
        log.info("售后退货退款付款申请通过,付款申请信息,payApplyDto:{}", JSON.toJSONString(payApplyDto));
        Integer traderMode = payApplyDto.getTraderMode();
        String bankAccount = payApplyDto.getBankAccount();
        String traderName = payApplyDto.getTraderName();
        String bankCode = payApplyDto.getBankCode();
        Integer bankId = 0;
        if (StrUtil.isNotBlank(bankCode)) {
            BankInfoDto bankInfoDto = bankApiService.findByBankNoAndIsDel(bankCode, ErpConstant.ZERO);
            bankId = Objects.isNull(bankInfoDto) ? 0 : bankInfoDto.getBankId();
        }
        log.info("售后退货退款付款申请通过,处理客户银行账号信息,银行信息,bankId:{}", bankId);
        CustomerBankAccountQueryReqDto queryReqDto = new CustomerBankAccountQueryReqDto();
        queryReqDto.setTraderId(traderId);
        queryReqDto.setAccountType(getAccountType(traderMode));
        queryReqDto.setAccountNo(bankAccount);
        List<CustomerBankAccountApiDto> queryDto = customerAccountApi.query(queryReqDto);
        log.info("售后退货退款付款申请通过,查询客户账户信息,查询结果,queryDto:{}", JSON.toJSONString(queryDto));
        if (CollUtil.isEmpty(queryDto)) {
            // 插入
            CustomerBankAccountReqDto insertDto = new CustomerBankAccountReqDto();
            insertDto.setTraderId(traderId);
            insertDto.setAccountType(getAccountType(traderMode));
            insertDto.setAccountNo(bankAccount);
            insertDto.setAccountName(traderName);
            insertDto.setBankId(bankId);
            insertDto.setIsVerify(ErpConstant.ZERO);
            insertDto.setLastUseTime(new Date());
            log.info("售后退货退款付款申请通过,插入客户账户信息,insertDto:{}", JSON.toJSONString(insertDto));
            customerAccountApi.insertOrUpdateCustomerAccount(insertDto);
        } else {
            // 更新
            CustomerBankAccountReqDto updateDto = new CustomerBankAccountReqDto();
            updateDto.setCustomerBankAccountId(queryDto.get(0).getCustomerBankAccountId());
            updateDto.setLastUseTime(new Date());
            if (ErpConstant.ZERO.equals(queryDto.get(0).getIsVerify())) {
                updateDto.setBankId(bankId);
            }
            log.info("售后退货退款付款申请通过,更新客户账户信息,updateDto:{}", JSON.toJSONString(updateDto));
            customerAccountApi.insertOrUpdateCustomerAccount(updateDto);
        }
        if (bankId > 0) {
            log.info("售后退货退款付款申请通过,处理客户银行账号信息,更新客户银行别名,payApplyId:{},bankId:{}", payApplyId, bankId);
            customerAccountApiService.tryCreateBankAlias(payApplyId, bankId);
        }
    }

    private Integer getAccountType(Integer traderMode) {
        // 支付宝
        if (SysOptionConstant.ID_520.equals(traderMode)) {
            return ErpConstant.THREE;
        }
        // 银行
        if (SysOptionConstant.ID_521.equals(traderMode)) {
            return ErpConstant.ONE;
        }
        // 微信
        if (SysOptionConstant.ID_522.equals(traderMode)) {
            return ErpConstant.TWO;
        }
        return null;
    }

    private void setContactDataInfo(PushAfterSalesDto.ContactData contactData, Integer areaId, Map<Integer, RegionDto> regionDtoMap) {
        if (AfterSalesConstants.CHINA_REGION_ID.contains(areaId)) {
            return;
        }
        RegionDto regionDto = regionDtoMap.get(areaId);
        if (Objects.nonNull(regionDto) && Objects.nonNull(regionDto.getRegionType())) {
            switch (regionDto.getRegionType()) {
                case AfterSalesConstants.PROVINCE_TYPE:
                    contactData.setContactProvince(regionDto.getRegionId());
                    break;
                case AfterSalesConstants.CITY_TYPE:
                    contactData.setContactCity(regionDto.getRegionId());
                    break;
                case AfterSalesConstants.AREA_TYPE:
                    contactData.setContactArea(regionDto.getRegionId());
                    break;
            }
            setContactDataInfo(contactData, regionDto.getParentId(), regionDtoMap);
        }
    }
}
