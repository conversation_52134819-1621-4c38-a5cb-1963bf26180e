package com.vedeng.system.rabbitmq;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.vedeng.common.constant.BelongPlatformEnum;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.common.util.DateUtil;
import com.vedeng.soap.ApiSoap;
import com.vedeng.trader.dao.RTraderJUserMapper;
import com.vedeng.trader.dao.TraderContactGenerateMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderContactGenerate;
import com.vedeng.trader.model.WebAccount;
import com.vedeng.trader.service.TraderContactService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * 注册用户同步
 * @Author: daniel
 * @Date: 2021/2/22 15 04
 * @Description:
 */
@Component("accountRegisterQueueConsumer")
public class AccountRegisterQueueConsumer extends AbstractMessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(AccountRegisterQueueConsumer.class);

    @Autowired
    private ApiSoap apiSoap;

    @Autowired
    private RTraderJUserMapper traderJUserMapper;

    @Autowired
    private TraderMapper traderMapper;

    @Autowired
    private TraderContactGenerateMapper traderContactGenerateMapper;

    @Autowired
    @Qualifier("webAccountMapper")
    private WebAccountMapper webAccountMapper;

    @Resource
    private TraderContactService traderContactService;

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("注册用户同步的消息，消息体：{}",messageBody);
        WebAccount account = JSONObject.parseObject(messageBody,WebAccount.class);
        if (StringUtils.isBlank(account.getMobile())){
            LOGGER.error("注册用户手机号异常，注册用户信息：{}",JSONObject.toJSONString(account));
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
        }
        try {
            if (apiSoap.getMobileCount(account) > 0){
                //如果手机号存在 启用这个用户
                WebAccount webAccountQuery = webAccountMapper.getWenAccountInfoByMobile(account.getMobile());
                TraderContactGenerate contactToUpdate = null;
                if (webAccountQuery.getTraderContactId()!=null && webAccountQuery.getTraderContactId()>0) {
                    contactToUpdate = traderContactService.selectByPrimaryKey(webAccountQuery.getTraderContactId());
                }
                if (contactToUpdate == null) {
                    contactToUpdate = traderContactService.getByTraderIdAndMobileNo(webAccountQuery.getTraderId(), webAccountQuery.getMobile());
                }

                //如果是禁用 更新为启用
                if(contactToUpdate!=null && Objects.equals(contactToUpdate.getIsEnable(), CommonConstants.DISABLE)){
                    LOGGER.info("启用用户同步的消息开始，消息体：{}",messageBody);
                    //if found one, then to update.
//            contactToUpdate.setTraderId(traderUnboundDto.getTraderId());
                    contactToUpdate.setTraderId(account.getTraderId());
                    contactToUpdate.setIsEnable(CommonConstants.ENABLE);
                    contactToUpdate.setUpdater(2); //njadmin
                    contactToUpdate.setModTime(System.currentTimeMillis());
                    traderContactService.updateByPrimaryKeySelective(contactToUpdate);
                }
                LOGGER.info("启用用户开始更新T_WEB_ACCOUNT表，消息体：{}",messageBody);
                webAccountQuery.setTraderId(account.getTraderId());
                //启用注册用户关联客户信息，并贝登会员置为"会员"
                webAccountQuery.setIsVedengMember(CommonConstants.STATUS_1);
                webAccountQuery.setModTime(new Date());
                webAccountMapper.updateisVedengJoin(webAccountQuery);
                webAccountQuery.setVedengMemberTime(new Date());
                //更新贝登会员开通时间
                webAccountMapper.updateOpenVedengMemberTime(webAccountQuery);
                //
                if (BelongPlatformEnum.JC.getBelong().equals(account.getRegisterPlatform()) &&
                        account.getTraderId() != null && account.getTraderId() > 0) {
                    Trader update = new Trader();
                    update.setTraderId(account.getTraderId());
                    update.setBelongPlatform(BelongPlatformEnum.JC.getBelong());
                    traderMapper.updateBelongPlatformOfTrader(update);
                }
            } else {
                WebAccount toRegisterAccount = new WebAccount();
                toRegisterAccount.setSsoAccountId(account.getSsoAccountId());
                toRegisterAccount.setTraderId(account.getTraderId());
                toRegisterAccount.setIsEnable(1);
                toRegisterAccount.setFrom(account.getFrom());
                toRegisterAccount.setMobile(account.getMobile());
                toRegisterAccount.setName(account.getName());
                toRegisterAccount.setAddTime(DateUtil.sysTimeMillis());
                toRegisterAccount.setRegisterPlatform(account.getRegisterPlatform());
                toRegisterAccount.setBelongPlatform(account.getRegisterPlatform());
                if (account.getTraderId() != null && account.getTraderId() > 0) {
                    toRegisterAccount.setCompanyName(traderMapper.getTraderByTraderId(account.getTraderId()).getTraderName());
                    Optional.ofNullable(traderJUserMapper.getUserByTraderId(account.getTraderId()))
                            .ifPresent(var -> toRegisterAccount.setUserId(var.getUserId()));
                }
                apiSoap.insert(toRegisterAccount);

                //如果用户是集采平台用户，那么自动将该用户添加到归属客户的联系人
                if (BelongPlatformEnum.JC.getBelong().equals(toRegisterAccount.getBelongPlatform()) &&
                        toRegisterAccount.getTraderId() != null && toRegisterAccount.getTraderId() > 0) {
                    TraderContactGenerate traderContact = new TraderContactGenerate();
                    traderContact.setTraderId(toRegisterAccount.getTraderId());
                    traderContact.setTraderType(1);
                    traderContact.setIsEnable(1);
                    traderContact.setName(toRegisterAccount.getName());
                    traderContact.setMobile(toRegisterAccount.getMobile());
                    traderContact.setAddTime(DateUtil.sysTimeMillis());
                    traderContact.setCreator(2);
                    traderContactGenerateMapper.insert(traderContact);
                    Trader update = new Trader();
                    update.setTraderId(toRegisterAccount.getTraderId());
                    update.setBelongPlatform(BelongPlatformEnum.JC.getBelong());
                    traderMapper.updateBelongPlatformOfTrader(update);

                }
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
        } catch (Exception e){
            LOGGER.error("同步注册用户失败，注册用户：{}，错误信息：",messageBody,e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,false);
            } catch (IOException ex) {
                LOGGER.error("注册用户同步消费失败，将消息返回给rabbitmq错误：",ex);
            }
        }
    }
}
