package com.vedeng.common.validator;

import com.baidu.unbiz.fluentvalidator.ValidatorContext;
import com.baidu.unbiz.fluentvalidator.ValidatorHandler;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class NumberValidatorHandler extends ValidatorHandler<Number> {

    private String errorMessage;

    public NumberValidatorHandler(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    @Override
    public boolean validate(ValidatorContext context, Number number) {
        if (number == null) {
            context.addErrorMsg(errorMessage);
            return false;
        }

        String numberStr = number.toString();

        boolean valid = true;
        try {
            if (number instanceof Integer) {
                Integer.parseInt(numberStr);
            } else if (number instanceof Long) {
                Long.parseLong(numberStr);
            } else if (number instanceof Float) {
                Float.parseFloat(numberStr);
            } else if (number instanceof Double) {
                Double.parseDouble(numberStr);
            } else if (number instanceof BigDecimal) {
                new BigDecimal(numberStr);
            }
        } catch (Exception e) {
            valid = false;
            context.addErrorMsg(errorMessage);
        }

        return valid;
    }

}
