package com.vedeng.crm.web.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/12/24
 */
public class EmptyStrController {

    @NoNeedAccessAuthorization
    @GetMapping(value = "/pixel", produces = MediaType.IMAGE_PNG_VALUE)
    public byte[] getPixelImage() throws IOException {
        // 创建一个1x1像素的BufferedImage
        BufferedImage image = new BufferedImage(1, 1, BufferedImage.TYPE_INT_RGB);

        // 设置像素颜色为黑色
        image.setRGB(0, 0, Color.BLACK.getRGB());

        // 将图像写入ByteArrayOutputStream
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", baos);

        // 返回字节数组
        return baos.toByteArray();
    }



}
