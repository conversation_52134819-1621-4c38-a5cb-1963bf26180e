package com.vedeng.crm.task.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.domain.DataDictionaryDto;
import com.vedeng.common.core.enums.JumpErpTitleEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.crm.business.business.domain.entity.CrmBusinessChanceEntity;
import com.vedeng.crm.business.quote.domain.dto.SendMessageDto;
import com.vedeng.crm.business.quote.service.CrmQuoteOrderService;
import com.vedeng.crm.common.domain.DeadlineRole;
import com.vedeng.crm.common.enums.BusinessSupervisionTypeEnum;
import com.vedeng.crm.common.enums.TaskTypeEnum;
import com.vedeng.crm.common.service.JumpService;
import com.vedeng.crm.shard.service.ShareService;
import com.vedeng.crm.task.domain.dto.*;
import com.vedeng.crm.task.domain.entity.TaskEntity;
import com.vedeng.crm.task.domain.entity.TaskItemEntity;
import com.vedeng.crm.task.domain.entity.WeixingMessageEntity;
import com.vedeng.crm.task.domain.vo.MyTaskVo;
import com.vedeng.crm.task.domain.vo.TaskGroupSub;
import com.vedeng.crm.task.mapper.TaskItemMapper;
import com.vedeng.crm.task.mapper.TaskMapper;
import com.vedeng.crm.task.mapper.WeixingMessageMapper;
import com.vedeng.crm.task.mapstruct.TaskConvertor;
import com.vedeng.crm.task.service.TaskService;
import com.vedeng.crm.visitrecord.api.CrmVisitApiService;
import com.vedeng.crm.visitrecord.api.CrmVisitDataApiService;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;
import com.vedeng.crm.visitrecord.domain.vo.VisitTongxingUserVo;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.service.BusinessLeadsService;
import com.vedeng.erp.common.dto.RSalesJBusinessOrderDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.dto.BizTraderApiDto;
import com.vedeng.erp.trader.dto.BusinessChanceApiDto;
import com.vedeng.erp.trader.service.BusinessChanceApiService;
import com.vedeng.erp.trader.service.TraderApiService;
import com.vedeng.infrastructure.feign.message.MessageApiService;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.infrastructure.message.dto.MessageDto;
import com.vedeng.uac.api.dto.UserInfoDto;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TaskServiceImpl implements TaskService {

    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private TaskConvertor taskConvertor;
    @Autowired
    private BusinessChanceApiService businessChanceApiService;
    @Autowired
    private TaskItemMapper taskItemMapper;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    private UacWxUserInfoApiService uacWxUserInfoApiService;
    @Autowired
    private CrmQuoteOrderService crmQuoteOrderService;
    @Autowired
    private ShareService shareService;
    @Autowired
    private WeixingMessageMapper weixingMessageMapper;
    @Autowired
    TraderApiService traderApiService;

    @Value(value = "${crm.task.deadlineRole}")
    private String deadlineRole;
    @Value(value = "${crm.task.taskType.departmentId}")
    private Integer taskTypeDepartmentId;

    private static final String DEADLINE_REMINDER = "<div class=\"gray\">%s</div> <div class=\"normal\">提醒：商机%s%s任务即将截止，请您关注</div><div class=\"highlight\">若截止时间内未能完成任务，将会发送超时提醒至主管处</div>";
    private static final String TIMEOUT_REMINDER = "<div class=\"gray\">%s</div> <div class=\"normal\">超时预警：商机%s%s任务已经超时，请您关注</div>";
    private static final String MY_TODO_TIMEOUT_STATISTIC = "<div class=\"gray\">%s</div> <div class=\"normal\">您的团队还有%s条派送的任务未跟进，请您关注；</div>";
    private static final String MY_INITIATIVE_TIMEOUT_STATISTIC = "<div class=\"gray\">%s</div> <div class=\"normal\">您的团队还有%s条发起的任务未跟进，请您关注；</div>";


    //再次跟进	初次提醒	销售
    private static final String BUSINESS_CHANCE_FIRST_REMINDER = "<div class=\"gray\">%s</div> <div class=\"normal\">通知：今日您还有%s条商机需要跟进，请您关注</div>";

    private static final String BUSINESS_CHANCE_TREAM_REMINDER = "<div class=\"gray\">%s</div> <div class=\"normal\">通知：今日您的团队还有%s条商机需要跟进，请您关注</div>";

    // 任务发起
    private static final String START_TASK_NOTIFICATION_TEMPLATE = "<div class=\"gray\">%s</div><div class=\"normal\">%s任务通知</div><div class=\"normal\">发起人：%s</div><div class=\"normal\">截止时间：%s</div><div class=\"highlight\">若截止时间内未能完成任务，会将发送超时提醒至主管处</div>";

    /**
     * 任务完成或者关闭的提醒
     */
    private static final String TASK_COMPLETED_MESSAGE_TEMPLATE = "<div class=\"gray\">%s</div><div class=\"normal\">%s您发起的%s任务，%s%s，原因：%s</div>";

    @Autowired
    private JumpService jumpService;


    @Autowired
    private BusinessLeadsService businessLeadsService;


    // http://qa.lxcrm.ivedeng.com
    @Value("${lxcrmUrl}")
    private String lxcrmUrl;


    @Override
    public void alertBussinessChanceProcessTask(String check) {
        List<TaskGroupSub> taskGroupSubList = taskMapper.findBussinessChanceProcessTask();
        //判断taskGroupSubList是否为空，为空时返回
        if (CollectionUtil.isEmpty(taskGroupSubList)) {
            log.info("商机跟进提醒时，无数据");
            return;
        }
        String today = getNowDay();
        String now = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");

        Map<Integer, TaskGroupSub> taskGroupSubMap = new HashMap<>();
        for (TaskGroupSub taskGroupSub : taskGroupSubList) {
            taskGroupSubMap.put(taskGroupSub.getUserId(), taskGroupSub);
        }
        // 再次跟进	初次提醒	销售自己
        for (TaskGroupSub taskGroupSub : taskGroupSubList) {
            if ("Y".equals(check)) {
                log.info("check是否发送商机提醒");
                WeixingMessageEntity weixingMessageEntity = checkHasAlertMessageToday(taskGroupSub.getUserId(), ErpConstant.FIVE);
                if (weixingMessageEntity != null && taskGroupSub.getTaskCount() <= weixingMessageEntity.getMessageCount()) {
                    log.info("已经发送过提醒,且当前该人员的任务提醒数小于等于当天已提醒数，跳过");
                    continue;
                }
            }

            if (taskGroupSub.getTaskCount() <= 0) {
                log.info("check是否发送商机提醒时，未发现有数量要推送:{},{},{}", new Object[]{taskGroupSub.getUserId(), taskGroupSub.getUsername(), taskGroupSub.getTaskCount()});
                continue;
            }
            //超时预警：商机编号+任务类型（含二级）任务已经超时，请您关注；
            SendMessageDto sendMessageDto = new SendMessageDto();
            sendMessageDto.setUrl(jumpService.getjumpUrl(lxcrmUrl + "/crm/task/profile/index?initTaskType=5&initStartDeadLine=2024-09-01&initEndDeadLine=" + today, JumpErpTitleEnum.TASK_LIST));
            sendMessageDto.setUserNumber(taskGroupSub.getNumber());
            sendMessageDto.setFormat(String.format(BUSINESS_CHANCE_FIRST_REMINDER, now, taskGroupSub.getTaskCount()));
            sendCardMsg(sendMessageDto, "任务提醒");
            saveWeixinMessage(taskGroupSub.getUserId(), "商机跟进", "02", ErpConstant.FIVE, 0, new Object[]{now, taskGroupSub.getTaskCount()}, sendMessageDto.getFormat(), taskGroupSub.getTaskCount());
        }
        //再次跟进	初次提醒	销售主管
        List<UserDto> parentUserList = userApiService.searchIsParentFromUac();
        if (CollectionUtil.isNotEmpty(parentUserList)) {
            //将parentUserList遍历一遍，并取下属的集合
            for (UserDto userDto : parentUserList) {
                List<UserDto> childUserList = userApiService.queryUserSubFromUac(userDto.getUserId());
                if (CollectionUtil.isNotEmpty(childUserList)) {
                    //将childUserList转换为List<Integer>
                    List<Integer> childUserIdList = childUserList.stream().map(UserDto::getUserId).collect(Collectors.toList());
                    //遍历childUserIdList，在taskGroupSubMap 这个Map中，逐个按Integer 获取统计所有TaskGroupSub的taskCount
                    Integer parentTaskCount = 0;
                    for (Integer childUserId : childUserIdList) {
                        TaskGroupSub taskGroupSub = taskGroupSubMap.get(childUserId);
                        if (taskGroupSub == null) {
                            continue;
                        }
                        parentTaskCount = parentTaskCount + taskGroupSub.getTaskCount();
                    }

                    if (parentTaskCount <= 0) {
                        log.info("check是否发送商机提醒时，未发现有数量要推送:{},{},{}", new Object[]{userDto.getUserId(), userDto.getUsername(), parentTaskCount});
                        continue;
                    }

                    if ("Y".equals(check)) {
                        log.info("check是否发送商机提醒,{},{}", new Object[]{userDto.getUserId(), parentTaskCount});
                        WeixingMessageEntity weixingMessageEntity = checkHasAlertMessageToday(userDto.getUserId(), ErpConstant.FIVE);
                        if (weixingMessageEntity != null && parentTaskCount <= weixingMessageEntity.getMessageCount()) {
                            log.info("已经发送过提醒,且当前该人员的任务提醒数小于等于当天已提醒数，跳过,{},{}", new Object[]{userDto.getUserId(), parentTaskCount});
                            continue;
                        }
                    }
                    //超时预警：商机编号+任务类型（含二级）任务已经超时，请您关注；
                    SendMessageDto sendMessageDto = new SendMessageDto();
                    sendMessageDto.setUrl(jumpService.getjumpUrl(lxcrmUrl + "/crm/task/profile/index?initTaskType=5&initStartDeadLine=2024-09-01&initEndDeadLine=" + today, JumpErpTitleEnum.BUSSINESS_CHANCE_DETAIL));
                    sendMessageDto.setUserNumber(userDto.getNumber());
                    sendMessageDto.setFormat(String.format(BUSINESS_CHANCE_TREAM_REMINDER, now, parentTaskCount));
                    sendCardMsg(sendMessageDto, "任务提醒");
                    saveWeixinMessage(userDto.getUserId(), "商机跟进", "02", ErpConstant.FIVE, 0, new Object[]{now, parentTaskCount}, sendMessageDto.getFormat(), parentTaskCount);
                }
            }
        }


    }

    @Override
    public List<DataDictionaryDto> cascadeGetTaskType() {
        List<DataDictionaryDto> dataDictionaryDtoList = Stream.of(TaskTypeEnum.values())
                .map(taskType -> new DataDictionaryDto(taskType.getValue(), taskType.getLabel()))
                .collect(Collectors.toList());

        List<DataDictionaryDto> child = Stream.of(BusinessSupervisionTypeEnum.values())
                .map(taskType -> new DataDictionaryDto(taskType.getValue(), taskType.getLabel()))
                .collect(Collectors.toList());

        dataDictionaryDtoList.forEach(dataDictionaryDto -> {
            if (dataDictionaryDto.getValue().equals(TaskTypeEnum.BUSINESS_SUPERVISION.getValue())) {
                dataDictionaryDto.setChildren(child);
            }
        });
        return dataDictionaryDtoList;
    }

    @Override
    public List<TaskItemDto> getAgainTodoTask(Integer bizId, Integer bizType) {
        return taskItemMapper.findByBizIdAndBizTypeAndMainTaskType(bizId, bizType);
    }

    public WeixingMessageEntity checkHasAlertMessageToday(Integer userId, Integer mainTaskType) {
        return weixingMessageMapper.selectByUserIdAndType(userId, mainTaskType);
    }

    public void saveWeixinMessage(Integer userId, String bizType, String bizTypeEnum,
                                  Integer mainTaskType, Integer subTaskType, Object[] params, String messageContent, Integer messageCount) {
        WeixingMessageEntity record = new WeixingMessageEntity();
        record.setAddTime(new Date());
        record.setModTime(new Date());
        record.setCreator(1);
        record.setUpdater(1);
        record.setCreatorName("");
        record.setUpdaterName("1");
        record.setIsDelete(false);
        //将record剩下的值按参数填充上
        record.setBizType(bizType);
        record.setBizTypeEnum(bizTypeEnum);
        record.setMainTaskType(mainTaskType);
        record.setSubTaskType(subTaskType);
        record.setMessageParams(JSON.toJSONString(params));
        record.setMessageContent(messageContent);
        record.setMessageUserId(userId);
        record.setMessageCount(messageCount);
        weixingMessageMapper.insertSelective(record);
    }


    @Override
    public void alertBussinessChanceTask() {
        List<TaskReminder> timeoutList = taskItemMapper.findAlertBussinessChanceTask();
        if (CollectionUtil.isEmpty(timeoutList)) {
            log.info("未获取到超时数据");
            return;
        }

        for (TaskReminder taskReminder : timeoutList) {
            //超时提醒每个商机督导的信息
            // 获取当前日期
            LocalDate currentDate = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
            String formattedDate = currentDate.format(formatter);
            String now = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
            // BUSSINESS_CHANCE_TITLE 标题
            /**
             * 任务类型
             * 1.初步产品方案
             * 2.单品询价
             * 3.综合询价
             * 4.商机督导
             * 5.再次跟进
             */
            Integer mainTaskType = taskReminder.getMainTaskType();
            /**
             * 商机督导-督导类型
             */
            Integer subTaskType = taskReminder.getSubTaskType();

            String mainTaskTypeName = TaskTypeEnum.getTaskTypeEnum(mainTaskType.toString()).getLabel();
            String subTaskTypeName = "";

            //超时预警：商机编号+任务类型（含二级）任务已经超时，请您关注；
            SendMessageDto sendMessageDto = new SendMessageDto();
            sendMessageDto.setType(TaskTypeEnum.getTaskTypeLabel(taskReminder.getMainTaskType().toString()));
            RestfulResult<UserInfoDto> user = uacWxUserInfoApiService.getUser(taskReminder.getTaskUser(), 1);
            if (user.isSuccess()) {
                Integer parentUserId = user.getData().getParentUserId();
                UserDto parentUser = userApiService.getUserBaseInfo(parentUserId);
                if (Objects.nonNull(parentUser)) {
                    sendMessageDto.setUserNumber(parentUser.getNumber());
                }
            }
            if (ErpConstant.ONE.equals(taskReminder.getBizType())) {
                sendMessageDto.setBusinessNo(taskReminder.getBizNo());
            } else if (ErpConstant.TWO.equals(taskReminder.getBizType())) {
                sendMessageDto.setBusinessNo(taskReminder.getBizNo());
            } else if (ErpConstant.THREE.equals(taskReminder.getBizType())) {
                String businessChanceNo = crmQuoteOrderService.selectBusinessChanceByQuoteorderId(taskReminder.getBizId());
                sendMessageDto.setBusinessNo(businessChanceNo);
            }
            sendMessageDto.setUrl(jumpService.getjumpUrl(lxcrmUrl + "/crm/businessChance/profile/detail?id=" + taskReminder.getBizId() + "&taskItemId=" + taskReminder.getTaskItemId(), JumpErpTitleEnum.TASK_LIST)); // sendMessageDto.setUserNumber("1338");
            sendMessageDto.setFormat(String.format(TIMEOUT_REMINDER, now, sendMessageDto.getBusinessNo(), mainTaskTypeName + subTaskTypeName));
            sendCardMsg(sendMessageDto, "超时提醒");
            taskItemMapper.updateIsTimeoutReminderByTaskItemId(ErpConstant.ONE, taskReminder.getTaskItemId());

        }


    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateTaskForVisit(TaskDto taskDto){
        CurrentUser currentUser = null;
        if(taskDto.getApiUserId() !=null){
            currentUser = new CurrentUser();
            currentUser.setId(taskDto.getApiUserId());
            currentUser.setUsername(taskDto.getApiUserName());
        }else {
            currentUser = CurrentUser.getCurrentUser();
        }
        Assert.notNull(taskDto.getBizId());
        Assert.notNull(taskDto.getBizType());
        Assert.notNull(taskDto.getMainTaskType());


//        TaskEntity taskEntity = taskConvertor.toEntity(taskDto);
//        taskEntity.setIsDelete(ErpConstant.F);
//        taskEntity.setAddTime(new Date());
//        taskEntity.setModTime(new Date());
//        taskEntity.setCreator(currentUser.getId());
//        taskEntity.setUpdater(currentUser.getId());
//        taskEntity.setCreatorName(currentUser.getUsername());
//        taskEntity.setUpdaterName(currentUser.getUsername());
//        taskMapper.insertSelective(taskEntity);

        TaskEntity taskEntity  = taskMapper.findTaskEntityForVisit(taskDto.getBizId(), taskDto.getBizType());
        if(taskEntity == null){
            log.info("历史计划无任务");
            return ;
        }

        taskEntity.setModTime(new Date());
        taskEntity.setUpdater(currentUser.getId());
        taskEntity.setUpdaterName(currentUser.getUsername());
        taskEntity.setDeadline(taskDto.getDeadline());
        taskEntity.setIsDelete(ErpConstant.F);
        taskEntity.setTaskContent(taskDto.getTaskContent());
        taskMapper.updateByPrimaryKeySelective(taskEntity);
        //只查找未完成的任务
        List<TaskItemEntity> taskItemList = taskItemMapper.findAllByTaskIdNotDone(taskEntity.getTaskId());
        //遍历taskItemList ，取taskUser与  taskDto.getTodoUserList()对比，存在的不变，不存在的删除
        CurrentUser finalCurrentUser = currentUser;
        taskItemList.forEach(taskItemEntity -> {
            if(!taskDto.getTodoUserList().contains(taskItemEntity.getTaskUser())){
                //删除 taskItemMapper.deleteByPrimaryKey(taskItemEntity.getTaskItemId());
//                taskItemEntity.setIsDelete(1);//标记为删除
                taskItemEntity.setDoneStatus(2);
                taskItemEntity.setDoneRemark("被去除该计划的拜访人");
                taskItemEntity.setUpdater(finalCurrentUser.getId());
                taskItemEntity.setUpdaterName(finalCurrentUser.getUsername());
                taskItemEntity.setDoneUser(finalCurrentUser.getId());
                taskItemEntity.setDoneUserName(finalCurrentUser.getUsername());
                taskItemMapper.updateByPrimaryKeySelective(taskItemEntity);
            }
        });


        List<Integer> taskUserIds = CollectionUtil.isEmpty(taskItemList)?new ArrayList<>()
                :(taskItemList.stream().map(TaskItemEntity::getTaskUser).collect(Collectors.toList())) ;

        taskDto.getTodoUserList().forEach(newUserId -> {
            if(!taskUserIds.contains(newUserId)){
                TaskItemEntity taskItemEntity = new TaskItemEntity();//初始化一个对象，将各个字段赋值
                taskItemEntity.setTaskId(taskEntity.getTaskId());
                taskItemEntity.setTaskUser(newUserId);
                UserDto userDto = userApiService.getUserBaseInfo(newUserId);
                taskItemEntity.setTaskUserName(userDto==null?"":userDto.getUsername());
                taskItemEntity.setDoneStatus(0);
                taskItemEntity.setIsDeadlineReminder(0);
                taskItemEntity.setIsTimeoutReminder(0);
                taskItemEntity.setIsDelete(ErpConstant.F);
                taskItemMapper.insertSelective(taskItemEntity);

            }
        });

    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void save(TaskDto taskDto) {
        CurrentUser currentUser = null;
        if(taskDto.getApiUserId() !=null){
            currentUser = new CurrentUser();
            currentUser.setId(taskDto.getApiUserId());
            currentUser.setUsername(taskDto.getApiUserName());
        }else {
            currentUser = CurrentUser.getCurrentUser();
        }
        Assert.notNull(taskDto.getBizId());
        Assert.notNull(taskDto.getBizType());
        Assert.notNull(taskDto.getMainTaskType());

        // 查看当前跟进记录的业务（商机或线索）是否已经存在待办任务，将之前的待办任务全部自动变成已处理
        if (TaskTypeEnum.FOLLOW_UP_AGAIN.getValue().equals(taskDto.getMainTaskType().toString())) {
            this.handlePreviousTodoTasks(taskDto);
        }

        if (taskDto.getCommitTime() == null) {
            taskDto.setCommitTime(new Date());
        }

        // 当业务类型是商机
        if (this.isBusinessChance(taskDto.getBizType())) {
            this.processBusinessChanceTask(taskDto);

            CurrentUser finalCurrentUser = currentUser;
            taskDto.getTodoUserList().forEach(userId -> this.shareBusinessForUser(taskDto, userId, finalCurrentUser));

        }

        // 当业务类型是线索
        if (this.isBusinessLeads(taskDto.getBizType())) {
            this.processBusinesLeadsTask(taskDto);

            //CurrentUser finalCurrentUser = currentUser;
            //taskDto.getTodoUserList().forEach(userId -> this.shareBusinessForUser(taskDto, userId, finalCurrentUser));

        }

        // 获取截止时间规则
        List<DeadlineRole> deadlineRoles = JSON.parseArray(this.deadlineRole, DeadlineRole.class);
        DeadlineRole deadlineRole = deadlineRoles.stream().filter(d -> d.getTaskType().equals(taskDto.getMainTaskType()))
                .findFirst().orElseThrow(() -> new ServiceException("不支持的任务类型"));

        // 基于任务类型计算 截止时间
        switch (TaskTypeEnum.getTaskTypeEnum(taskDto.getMainTaskType().toString())) {
            case INITIAL_PRODUCT_PLAN:
            case COMPREHENSIVE_INQUIRY:
            case SINGLE_ITEM_INQUIRY:
                taskDto.setDeadline(DateUtil.offsetHour(taskDto.getCommitTime(), deadlineRole.getFeedbackTime()));
                break;
            case BUSINESS_SUPERVISION:
            case FOLLOW_UP_AGAIN:
            case COLLABORATIVE_QUOTATION:
                Assert.notNull(taskDto.getDeadline(), () -> new ServiceException("截止时间不能为空"));
                break;
            case VISIT_FOLLOW:
                break;
            default:
                throw new ServiceException("不支持的任务类型");
        }


        TaskEntity taskEntity = taskConvertor.toEntity(taskDto);
        taskEntity.setIsDelete(ErpConstant.F);
        taskEntity.setAddTime(new Date());
        taskEntity.setModTime(new Date());
        taskEntity.setCreator(currentUser.getId());
        taskEntity.setUpdater(currentUser.getId());
        taskEntity.setCreatorName(currentUser.getUsername());
        taskEntity.setUpdaterName(currentUser.getUsername());
        taskMapper.insertSelective(taskEntity);
        CurrentUser finalCurrentUser = currentUser;
        // 基于待办人list去生成各自的task
        taskDto.getTodoUserList().forEach(userId -> {
            Long taskItemId = this.createTodoTask(taskEntity.getTaskId(), userId, finalCurrentUser);
            this.sendCreateTaskMsg(taskDto, userId, taskItemId);
        });



    }


    @Override
    public void handlePreviousTodoTasks(TaskDto taskDto) {
        List<TaskItemDto> againTodoTask = getAgainTodoTask(taskDto.getBizId(), taskDto.getBizType());
        if (CollectionUtils.isNotEmpty(againTodoTask)) {
            log.info("CRM跟进记录，待办任务，自动待处理待办任务:{}", JSON.toJSONString(againTodoTask));
            againTodoTask.forEach(task -> {
                TaskHandleDto taskHandleDto = new TaskHandleDto();
                taskHandleDto.setTaskId(task.getTaskId());
                taskHandleDto.setTaskItemId(task.getTaskItemId());
                taskHandleDto.setDoneStatus(ErpConstant.ONE);
                taskHandleDto.setDoneRemark("系统已处理");
                taskHandleDto.setApiUserId(taskDto.getApiUserId());
                taskHandleDto.setApiUserName(taskDto.getApiUserName());
                try {
                    this.handle(taskHandleDto);
                } catch (Exception e) {
                    log.warn("待办任务处理失败，taskItemId:{}", task.getTaskItemId(), e);
                }
            });
        }
    }

    /**
     * 发送创建任务消息
     *
     * @param taskDto taskDto
     * @param userId  userId
     */
    private void sendCreateTaskMsg(TaskDto taskDto, Integer userId, Long taskItemId) {
        if (TaskTypeEnum.INITIAL_PRODUCT_PLAN.getValue().equals(taskDto.getMainTaskType().toString())
                || TaskTypeEnum.BUSINESS_SUPERVISION.getValue().equals(taskDto.getMainTaskType().toString())
                || TaskTypeEnum.COLLABORATIVE_QUOTATION.getValue().equals(taskDto.getMainTaskType().toString())) {
            WxCpMessage wxCpMessage = new WxCpMessage();
            RestfulResult<UserInfoDto> user = uacWxUserInfoApiService.getUser(userId, 1);
            if (!user.isSuccess()) {
                log.error("获取用户信息失败，userId:{}", userId);
                return;
            }
            UserInfoDto userData = user.getData();
            wxCpMessage.setToUser(userData.getJobNumber());
            String text = taskDto.getBizNo() + TaskTypeEnum.getTaskTypeLabel(taskDto.getMainTaskType().toString());
            String content = String.format(START_TASK_NOTIFICATION_TEMPLATE, DateUtil.formatDateTime(new Date()), text, userData.getDisplayName(), DateUtil.formatDateTime(taskDto.getDeadline()));
            wxCpMessage.setDescription(content);
            wxCpMessage.setTitle("任务提醒");
            wxCpMessage.setMsgType("textcard");
            wxCpMessage.setBtnTxt("详情");
            wxCpMessage.setUrl(jumpService.getjumpUrl(lxcrmUrl + "/crm/businessChance/profile/detail?id=" + taskDto.getBizId() + "&taskItemId=" + taskItemId, JumpErpTitleEnum.TASK_LIST));
            try {
                log.info("发送消息：{}", JSON.toJSONString(wxCpMessage));
                RestfulResult<Void> voidRestfulResult = uacWxUserInfoApiService.sendToUser(wxCpMessage);
                if (!voidRestfulResult.isSuccess()) {
                    log.error("发送消息失败，{}", voidRestfulResult.getMessage());
                }
            } catch (Exception e) {
                log.error("发送消息失败", e);
            }
        }
    }


    private void shareBusinessForUser(TaskDto taskDto, Integer userId, CurrentUser currentUser) {
        if (TaskTypeEnum.SINGLE_ITEM_INQUIRY.getValue().equals(taskDto.getMainTaskType().toString())
                || TaskTypeEnum.COMPREHENSIVE_INQUIRY.getValue().equals(taskDto.getMainTaskType().toString())) {
            RSalesJBusinessOrderDto rSalesJBusinessOrderDto = createRSalesJBusinessOrderDto(taskDto, userId);
            shareService.shareBusiness(rSalesJBusinessOrderDto, currentUser);
        }
    }

    private RSalesJBusinessOrderDto createRSalesJBusinessOrderDto(TaskDto taskDto, Integer userId) {
        RSalesJBusinessOrderDto rSalesJBusinessOrderDto = new RSalesJBusinessOrderDto();
        rSalesJBusinessOrderDto.setBusinessType(ErpConstant.ONE);
        rSalesJBusinessOrderDto.setBusinessId(taskDto.getBizId());
        rSalesJBusinessOrderDto.setBusinessNo(taskDto.getBizNo());
        rSalesJBusinessOrderDto.setSaleUserId(userId);

        UserDto user = userApiService.getUserBaseInfo(userId);
        rSalesJBusinessOrderDto.setSaleUserName(user.getUsername());

        Date currentDate = new Date();
        rSalesJBusinessOrderDto.setAddTime(currentDate);
        rSalesJBusinessOrderDto.setModTime(currentDate);

        return rSalesJBusinessOrderDto;
    }

    /**
     * 根据商机阶段获取有效的督导类型
     *
     * @param stage 商机阶段
     * @return 有效的督导类型列表
     */
    private List<BusinessSupervisionTypeEnum> getValidSupervisionTypes(int stage) {
        switch (stage) {
            case 2:
                return BusinessSupervisionTypeEnum.getEnumsByType("OpportunityValidation");
            case 3:
            case 4:
                return BusinessSupervisionTypeEnum.getEnumsByType("ProductSolution");
            default:
                return BusinessSupervisionTypeEnum.getEnumsByType("Other");
        }
    }

    private boolean isBusinessChance(Integer bizType) {
        return bizType == 1;
    }

    private boolean isBusinessLeads(Integer bizType) {
        return bizType == 2;
    }

    private void processBusinessChanceTask(TaskDto taskDto) {
        BusinessChanceApiDto businessChance = businessChanceApiService.getBusinessChance(taskDto.getBizId());
        Assert.notNull(businessChance.getBussinessChanceNo(), "商机不存在");
        taskDto.setBizNo(businessChance.getBussinessChanceNo());

        if (isBusinessSupervisionTask(taskDto.getMainTaskType())) {
            validateBusinessSupervisionTask(taskDto, businessChance.getStage());
        }
    }

    private void processBusinesLeadsTask(TaskDto taskDto) {
        BusinessLeadsDto leadsDto = businessLeadsService.getOne(taskDto.getBizId());
        Assert.notNull(leadsDto.getLeadsNo(), "线索不存在");
        taskDto.setBizNo(leadsDto.getLeadsNo());

//        if (isBusinessSupervisionTask(taskDto.getMainTaskType())) {//判断是否商机督导
//            validateBusinessSupervisionTask(taskDto, businessChance.getStage());
//        }
    }


    private boolean isBusinessSupervisionTask(Integer mainTaskType) {
        return TaskTypeEnum.BUSINESS_SUPERVISION.getValue().equals(mainTaskType.toString());
    }

    /**
     * 验证业务监管任务的子任务类型是否符合当前业务阶段的可选类型范围
     *
     * @param taskDto       包含任务信息的数据传输对象，特别是子任务类型的列表
     * @param businessStage 业务当前所处的阶段，用于确定哪些子任务类型是有效的
     * @throws ServiceException 当任务类型不存在或当前业务阶段不允许当前子任务类型时抛出服务异常
     */
    private void validateBusinessSupervisionTask(TaskDto taskDto, Integer businessStage) {
        if (StrUtil.isNotEmpty(taskDto.getSubTaskType())) {
            String type = Arrays.stream(taskDto.getSubTaskType().split(",")).findFirst().orElseThrow(() -> new ServiceException("任务类型不存在"));
            BusinessSupervisionTypeEnum currentSupervisionType = BusinessSupervisionTypeEnum.getBusinessSupervisionTypeEnum(type);
            List<BusinessSupervisionTypeEnum> validSupervisionTypes = getValidSupervisionTypes(businessStage);

            if (!validSupervisionTypes.contains(currentSupervisionType)) {
                throw new ServiceException("SUB_TASK_TYPE_NOT_MATCH");
            }
        }
    }


    @Override
    public List<DataDictionaryDto> getTaskType(Integer businessId, Integer type) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        List<TaskTypeEnum> taskTypeEnums = new ArrayList<>();
        if(type == 2){//线索
            BusinessLeadsDto dto = businessLeadsService.getOne(businessId );

            taskTypeEnums.add(TaskTypeEnum.INITIAL_PRODUCT_PLAN);

            // 任务类型（销售主管视角）
            RestfulResult<UserInfoDto> user = uacWxUserInfoApiService.getUser(dto.getBelongerId(), 1);
            if (user.isSuccess()) {
                if (currentUser.getId().equals(user.getData().getParentUserId())) {
                    taskTypeEnums.clear();
                    taskTypeEnums.add(TaskTypeEnum.BUSINESS_SUPERVISION);
                }
            }
            return taskTypeEnums.stream()
                    .map(enumValue -> new DataDictionaryDto(enumValue.getValue(), enumValue.getLabel()))
                    .collect(Collectors.toList());


        }else if(type == 1){//商机
            BusinessChanceApiDto businessChance = businessChanceApiService.getBusinessChance(businessId);
            taskTypeEnums.add(TaskTypeEnum.INITIAL_PRODUCT_PLAN);

            // 任务类型（销售主管视角）
            RestfulResult<UserInfoDto> user = uacWxUserInfoApiService.getUser(businessChance.getSaleId(), 1);
            if (user.isSuccess()) {
                if (currentUser.getId().equals(user.getData().getParentUserId())) {
                    taskTypeEnums.clear();
                    taskTypeEnums.add(TaskTypeEnum.BUSINESS_SUPERVISION);
                }
            }
            // 任务类型（供应链人员视角）
            RestfulResult<List<UserInfoDto>> userListByDepartmentId = uacWxUserInfoApiService.getUserListByDepartmentId(taskTypeDepartmentId);
            if (userListByDepartmentId.isSuccess()) {
                List<UserInfoDto> userInfoDtoList = userListByDepartmentId.getData();
                if (userInfoDtoList != null) {
                    for (UserInfoDto userInfoDto : userInfoDtoList) {
                        if (currentUser.getId().equals(userInfoDto.getId())) {
                            taskTypeEnums.clear();
                            taskTypeEnums.add(TaskTypeEnum.COLLABORATIVE_QUOTATION);
                        }
                    }
                }
            }


            return taskTypeEnums.stream()
                    .map(enumValue -> new DataDictionaryDto(enumValue.getValue(), enumValue.getLabel()))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();



    }

    @Override
    public List<DeadlineRole.Department> getCandidateUser() {
        List<DeadlineRole> deadlineRoles = JSON.parseArray(this.deadlineRole, DeadlineRole.class);
        log.info("deadlineRole:{}", JSON.toJSONString(deadlineRoles));

        CurrentUser currentUser = CurrentUser.getCurrentUser();
        String taskType = TaskTypeEnum.INITIAL_PRODUCT_PLAN.getValue();

        // 任务类型（供应链人员视角）
        RestfulResult<List<UserInfoDto>> userListByDepartmentId = uacWxUserInfoApiService.getUserListByDepartmentId(taskTypeDepartmentId);
        if (userListByDepartmentId.isSuccess()) {
            List<UserInfoDto> userInfoDtoList = userListByDepartmentId.getData();
            boolean match = userInfoDtoList.stream().anyMatch(userInfoDto -> currentUser.getId().equals(userInfoDto.getId()));
            if (match) {
                taskType = TaskTypeEnum.COLLABORATIVE_QUOTATION.getValue();
            }
        }

        String finalTaskType = taskType;
        DeadlineRole deadlineRole = deadlineRoles.stream().filter(d -> d.getTaskType().toString().equals(finalTaskType))
                .findFirst().orElseThrow(() -> new ServiceException("不支持的任务类型"));

        deadlineRole.getDepartmentList().forEach(this::populateUserList);
        return deadlineRole.getDepartmentList();
    }


    private void populateUserList(DeadlineRole.Department department) {
        RestfulResult<List<UserInfoDto>> userListByDepartmentId = uacWxUserInfoApiService.getUserByDepartment(department.getDepartmentId());
        if (userListByDepartmentId.isSuccess()) {
            List<UserInfoDto> userInfoDtoList = userListByDepartmentId.getData();
            if (userInfoDtoList != null) {
                userInfoDtoList.forEach(userInfoDto -> {
                    TaskUserDto taskUserDto = new TaskUserDto();
                    taskUserDto.setUserId(userInfoDto.getId());
                    taskUserDto.setUserName(userInfoDto.getDisplayName());
                    taskUserDto.setAliasHeadPicture(userInfoDto.getAliasHeadPicture());
                    department.getUserList().add(taskUserDto);
                });
            }
        }
        // 递归处理下级部门
        for (DeadlineRole.Department child : department.getChildDepartment()) {
            this.populateUserList(child);
        }
    }

    @Autowired
    private CrmVisitDataApiService crmVisitDataApiService;

    @Override
    public PageInfo<MyTaskVo> taskPage(PageParam<TaskQueryDto> taskQueryDto) {
        TaskQueryDto param = taskQueryDto.getParam();
        Assert.notNull(param.getListType(), () -> new ServiceException("列表类型不能为空"));

        if (CollUtil.isEmpty(param.getBizList())) {

            CurrentUser currentUser = CurrentUser.getCurrentUser();
            RestfulResult<List<UserInfoDto>> allSubUser = uacWxUserInfoApiService.getAllSubUser(currentUser.getId());
            List<Integer> userIdList = new ArrayList<>();
            if (allSubUser.isSuccess()) {
                List<UserInfoDto> userInfoDtoList = allSubUser.getData();
                if (userInfoDtoList != null) {
                    userIdList = userInfoDtoList.stream().map(UserInfoDto::getId).collect(Collectors.toList());
                }
            }

            if (param.getListType().equals(1) && CollUtil.isEmpty(param.getTodoUserIdList())) {
                param.setTodoUserIdList(userIdList);
            }

            if (param.getListType().equals(2) && CollUtil.isEmpty(param.getCreatorIdList())) {
                param.setCreatorIdList(userIdList);
            }
            if (StrUtil.isEmpty(taskQueryDto.getOrderBy())) {
                taskQueryDto.setOrderBy("COMMIT_TIME desc");
            }
        } else {
            List<TaskQueryDto.Biz> visitTaskQueryDtoList = new ArrayList<>();
            for(TaskQueryDto.Biz biz:param.getBizList()){
                if(biz.getBizType().equals(1)){//商机
                    Integer bizId=  biz.getBizId();
                    List<VisitRecordVo> voList  = crmVisitDataApiService.selectVisitRecordByRelateId(bizId,2);
                    if(CollectionUtils.isNotEmpty(voList)){
                        for(VisitRecordVo vo:voList){
                            TaskQueryDto.Biz bizTemp = new TaskQueryDto.Biz();
                            bizTemp.setBizId(vo.getId());
                            bizTemp.setBizType(7);
                            visitTaskQueryDtoList.add(bizTemp);
                        }
                    }
                }else if(biz.getBizType().equals(2)){//线索
                    Integer bizId=  biz.getBizId();
                    List<VisitRecordVo> voList  = crmVisitDataApiService.selectVisitRecordByRelateId(bizId,1);
                    if(CollectionUtils.isNotEmpty(voList)){
                        for(VisitRecordVo vo:voList){
                            TaskQueryDto.Biz bizTemp = new TaskQueryDto.Biz();
                            bizTemp.setBizId(vo.getId());
                            bizTemp.setBizType(7);
                            visitTaskQueryDtoList.add(bizTemp);
                        }
                    }
                }
            }
            visitTaskQueryDtoList.addAll(param.getBizList());
            param.setBizList(visitTaskQueryDtoList);

            if (StrUtil.isEmpty(taskQueryDto.getOrderBy())) {
                taskQueryDto.setOrderBy("DONE_STATUS asc,DEADLINE desc");
            }

        }


        PageInfo<MyTaskVo> pageInfo = PageHelper.startPage(taskQueryDto).doSelectPageInfo(() -> taskMapper.findMyTask(param));

        List<MyTaskVo> tasks = pageInfo.getList();

        if (CollUtil.isNotEmpty(tasks)) {
            tasks.forEach(d -> {
                this.populateTaskDetails(d);
                this.getSubTaskInfo(d);
            });
            this.populateBizTrader(tasks);
        }

        return pageInfo;
    }

    /**
     * 填充业务客户信息
     */
    private void populateBizTrader(List<MyTaskVo> tasks) {
        Map<Integer, List<Integer>> bizIdsMap = tasks.stream()
                .collect(Collectors.groupingBy(MyTaskVo::getBizType, Collectors.mapping(MyTaskVo::getBizId, Collectors.toList())));

        bizIdsMap.forEach((bizType, ids) -> {
            List<BizTraderApiDto> traders = traderApiService.getBizTrader(ids, bizType);
            traders.stream()
                    .filter(d -> d.getTraderId() != null && d.getTraderId() != 0)
                    .forEach(dto -> traderApiService.getTraderLink(dto));
            Map<Integer, BizTraderApiDto> traderMap = traders.stream()
                    .collect(Collectors.toMap(BizTraderApiDto::getBizId, trader -> trader));

            tasks.stream()
                    .filter(t -> t.getBizType().equals(bizType))
                    .forEach(t -> {
                        BizTraderApiDto trader = traderMap.get(t.getBizId());
                        if (trader != null) {
                            t.setTraderCustomerName(trader.getTraderName());
                            t.setTraderCustomerId(trader.getTraderId());
                            t.setTraderNameInnerLink(trader.getTraderNameInnerLink());
                            t.setTraderNameLink(trader.getTraderNameLink());
                        }
                    });
        });
    }

    @Override
    public int getTaskCount(Integer bizId, Integer todoUserId, Integer bizType) {
        return taskMapper.getTaskCount(bizId, todoUserId, bizType);
    }

    @Override
    public List<MyTaskVo> getMyTask(TaskQueryDto dto) {
        return taskMapper.findMyTask(dto);
    }

    @Override
    public MyTaskVo detail(Long taskId, Long taskItemId) {
        TaskQueryDto query = new TaskQueryDto();
        query.setTaskId(taskId);
        query.setTaskItemId(taskItemId);
        query.setListType(1);
        if (taskId != null) {
            query.setListType(2);
        }
        List<MyTaskVo> myTask = taskMapper.findMyTask(query);
        if (CollUtil.isEmpty(myTask)) {
            throw new ServiceException("任务不存在");
        }
        this.populateBizTrader(myTask);
        return myTask.stream()
                .findFirst()
                .map(d -> {
                    this.populateTaskDetails(d);
                    this.getSubTaskInfo(d);
                    return d;
                }).orElseThrow(() -> new ServiceException("任务不存在"));
    }

    private void getSubTaskInfo(MyTaskVo d) {
        // 当查询主任务详情时,获取所有的子任务详情
        if (d.getTaskId() != null) {
            TaskQueryDto myQuery = new TaskQueryDto();
            myQuery.setListType(1);
            myQuery.setTaskId(d.getTaskId());
            List<MyTaskVo> taskVoList = taskMapper.findMyTask(myQuery);
            taskVoList.forEach(this::populateTaskDetails);
            d.setSubTaskList(taskVoList);
        }
    }

    /**
     * 根据任务相关信息填充任务详情对象。
     * 此方法主要用于根据任务的子任务类型和主任务类型，设置任务的类型标签，并根据不同的主任务类型，
     * 设置任务的申请用户、处理用户和完成用户。对于业务监管类型的任务，还会拼接具体的业务监管类型标签。
     */
    private void populateTaskDetails(MyTaskVo taskVo) {
        // Set task type with possible business supervision type
        // 获取主任务类型枚举
        TaskTypeEnum mainTaskTypeEnum = TaskTypeEnum.getTaskTypeEnum(taskVo.getMainTaskType().toString());
        String taskTypeLabel = mainTaskTypeEnum.getLabel();
        taskVo.setMainTaskTypeLabel(taskTypeLabel);

        // 处理子任务类型标签
        if (TaskTypeEnum.BUSINESS_SUPERVISION.getValue().equals(taskVo.getMainTaskType().toString())) {
            if (StrUtil.isNotBlank(taskVo.getSubTaskType())) {
                String[] split = taskVo.getSubTaskType().split(",");
                if (ArrayUtil.isNotEmpty(split)) {
                    String subTaskTypeLabel = Arrays.stream(split)
                            .map(s -> BusinessSupervisionTypeEnum.getBusinessSupervisionTypeEnum(s).getLabel())
                            .collect(Collectors.joining("、"));
                    taskTypeLabel += "-" + subTaskTypeLabel;
                }
            }
        }

        taskVo.setTaskType(taskTypeLabel);

        // Set user information
        taskVo.setApplyUser(getTaskUser(taskVo.getCreatorId()));
        taskVo.setTodoUser(getTaskUser(taskVo.getTaskUserId()));
        taskVo.setDoneUser(getTaskUser(taskVo.getDoneUserId()));

        // Set overtime status
        taskVo.setIsOverTime(this.setTaskOverTimeStatus(taskVo) ? ErpConstant.T : ErpConstant.F);


        // Set handling permissions
        if (taskVo.getTaskUserId() != null && taskVo.getDoneStatus() == ErpConstant.F) {
            CurrentUser currentUser = CurrentUser.getCurrentUser();
            boolean canHandle = taskVo.getTaskUserId().equals(currentUser.getId()) ||
                    Optional.ofNullable(uacWxUserInfoApiService.getUserByUserId(taskVo.getTaskUserId()))
                            .filter(RestfulResult::isSuccess)
                            .map(RestfulResult::getData)
                            .map(UserInfoDto::getParentUserId)
                            .filter(parentId -> parentId.equals(currentUser.getId()))
                            .isPresent();

            taskVo.setCanHandle(canHandle ? ErpConstant.T : ErpConstant.F);
        }
    }


    /**
     * 判断任务是否超时
     *
     * @param task 任务对象
     * @return true 表示超时，false 表示未超时
     */
    private boolean isOverTime(MyTaskVo task) {
        // 如果处理时间不为空且处理时间小于等于截止日期，则任务没有超时
        if (task.getDoneTime() != null && !task.getDoneTime().after(task.getDeadline())) {
            return false;
        }
        // 判断任务的截止日期是否在当前时间之前
        return task.getDeadline().before(new Date());
    }

    /**
     * 设置任务超时状态
     *
     * @param taskVo 任务对象
     * @return true 表示任务或子任务超时，false 表示未超时
     */
    public boolean setTaskOverTimeStatus(MyTaskVo taskVo) {
        // 判断主任务是否超时
        boolean isMainTaskOverTime = isOverTime(taskVo);

        // 判断子任务是否超时
        boolean isSubTaskOverTime = false;
        if (CollUtil.isNotEmpty(taskVo.getSubTaskList())) {
            isSubTaskOverTime = taskVo.getSubTaskList().stream()
                    .peek(t -> t.setIsOverTime(isOverTime(t) ? 1 : 0)) // 设置子任务的超时状态
                    .anyMatch(taskItem -> taskItem.getIsOverTime() == 1); // 检查是否有子任务超时
        }

        // 综合判断：主任务或子任务超时，则整个任务超时
        return isMainTaskOverTime || isSubTaskOverTime;
    }

    /**
     * 需求：任务完成后需要增加消息提醒，包含ERP站内消息及企业微信提醒。
     * http://jira.ivedeng.com/browse/CRM-691
     * 如果任务的处理人，并非发起人，则给发起人发一条消息
     */
    private void checkIsNeedSendMessage(Long taskItemId){
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        TaskItemEntity taskItemEntity = taskItemMapper.selectByPrimaryKey(taskItemId);
        if(taskItemEntity == null){
            return ;
        }
        TaskEntity taskEntity = taskMapper.selectByPrimaryKey(taskItemEntity.getTaskId());
        if(taskEntity == null){
            return ;
        }
        if(taskEntity.getBizType().equals(Integer.parseInt(TaskTypeEnum.VISIT_FOLLOW.getValue()))){
            log.info("拜访任务完成，不发通知");
            return;
        }
        if(taskEntity.getCreator() != null && taskEntity.getCreator() >1 && !taskEntity.getCreator().equals(currentUser.getId())){
            //  `BIZ_TYPE` tinyint(1) NOT NULL DEFAULT '0' COMMENT '业务类型，1：商机 2：线索 3：报价',
            Integer bizType=  taskEntity.getBizType();//1和3均存的是商机的ID。2为线索的

            //#业务编号#您发起的#任务类型#任务，#处理人# #处理方式#，原因：#处理描述#
            String bizNo = taskEntity.getBizNo();
            String taskType = TaskTypeEnum.getTaskTypeLabel(taskEntity.getMainTaskType().toString());
            String doneUser = taskItemEntity.getDoneUserName();
            //  `DONE_STATUS` tinyint(1) NOT NULL DEFAULT '0' COMMENT '处理状态：0待处理 1已处理 2关闭',
            String doneStatusName = "";
            if (taskItemEntity.getDoneStatus() == 1) {
                doneStatusName = "已处理";
            } else if (taskItemEntity.getDoneStatus() == 2) {
                doneStatusName = "已关闭";
            } else {
                doneStatusName = "待处理";
            }
            String doneRemark = StringUtils.isNotBlank(taskItemEntity.getDoneRemark())?taskItemEntity.getDoneRemark():"未填写";

            WxCpMessage wxCpMessage = new WxCpMessage();
            RestfulResult<UserInfoDto> user = uacWxUserInfoApiService.getUser(taskEntity.getCreator(), 1);
            if (!user.isSuccess()) {
                log.error("获取用户信息失败，不发送任务处理完成消息，userId:{}", taskEntity.getCreator());
                return;
            }
            UserInfoDto userData = user.getData();
            wxCpMessage.setToUser(userData.getJobNumber());
             //    private static final String TASK_COMPLETED_MESSAGE_TEMPLATE = "<div class=\"gray\">%s</div><div class=\"normal\">%s您发起的%s任务，%s%s，原因：%s</div>";
            String content = String.format(TASK_COMPLETED_MESSAGE_TEMPLATE, DateUtil.formatDateTime(new Date()), bizNo,taskType,doneUser,doneStatusName,doneRemark);
            wxCpMessage.setDescription(content);
            wxCpMessage.setTitle("任务完成提醒");
            wxCpMessage.setMsgType("textcard");
            wxCpMessage.setBtnTxt("详情");
            if(bizType !=null && bizType.equals(2)){//2为线索
                wxCpMessage.setUrl(jumpService.getjumpUrl(lxcrmUrl + "/crm/businessLeads/profile/detail?id=" + taskEntity.getBizId() + "&taskItemId=" + taskItemId, JumpErpTitleEnum.TASK_LIST));
            }else{//1和3均为商机
                wxCpMessage.setUrl(jumpService.getjumpUrl(lxcrmUrl + "/crm/businessChance/profile/detail?id=" + taskEntity.getBizId() + "&taskItemId=" + taskItemId, JumpErpTitleEnum.TASK_LIST));
            }
            try {
                log.info("任务完成时，给发起人发送消息：{}", JSON.toJSONString(wxCpMessage));
                RestfulResult<Void> voidRestfulResult = uacWxUserInfoApiService.sendToUser(wxCpMessage);
                if (!voidRestfulResult.isSuccess()) {
                    log.error("任务完成时，给发起人发送消息失败，{}", voidRestfulResult.getMessage());
                }
            } catch (Exception e) {
                log.error("任务完成时，给发起人发送消息失败", e);
            }

            //发送ERP气泡消息
            String url = (bizType !=null && bizType.equals(2))
                    ?(lxcrmUrl + "/crm/businessLeads/profile/detail?id=" + taskEntity.getBizId() + "&taskItemId=" + taskItemId)  //线索
                    :(lxcrmUrl + "/crm/businessChance/profile/detail?id=" + taskEntity.getBizId() + "&taskItemId=" + taskItemId) ; //商机
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("bizNo", bizNo);
            paramMap.put("taskType", taskType);
            paramMap.put("doneUser", doneUser);
            paramMap.put("doneStatusName", doneStatusName);
            paramMap.put("doneRemark", doneRemark);

            log.info("任务完成时给线索发起人发消息start,业务ID:{}", taskItemId);
            sendMessage(6011, Collections.singletonList(taskEntity.getCreator()), paramMap, url);
            log.info("任务完成时给线索发起人发消息end,业务ID:{}", taskItemId);


        }

    }

    @Override
    public void handle(TaskHandleDto taskHandle) {
        CurrentUser currentUser = null;
        if(taskHandle.getApiUserId() !=null){//兼容API接口传过来的数据，如果传了用户信息，则不取当前session的用户信息
            currentUser = new CurrentUser();
            currentUser.setId(taskHandle.getApiUserId());
            currentUser.setUsername(taskHandle.getApiUserName());
        }else {
            currentUser = CurrentUser.getCurrentUser();
        }
        TaskItemEntity taskItemEntity = taskItemMapper.selectByPrimaryKey(taskHandle.getTaskItemId());
        if (taskItemEntity == null) {
            throw new ServiceException("任务不存在");
        }
        if (taskItemEntity.getDoneStatus() != 0) {
            throw new ServiceException("任务已处理");
        }
        taskItemEntity.setDoneStatus(taskHandle.getDoneStatus());
        taskItemEntity.setDoneRemark(taskHandle.getDoneRemark());
        taskItemEntity.setDoneTime(new Date());
        taskItemEntity.setDoneUser(currentUser.getId());
        taskItemEntity.setDoneUserName(currentUser.getUsername());

        taskItemEntity.setModTime(new Date());
        taskItemEntity.setUpdater(currentUser.getId());
        taskItemEntity.setUpdaterName(currentUser.getUsername());

        taskItemMapper.updateByPrimaryKeySelective(taskItemEntity);


        // 基于子任务更新主任务的状态
        // 若所有任务都处于已处理(包含关闭状态)，则状态变为已处理；若所有任务都为关闭，则状态为关闭；若有其一为待处理，则为处理中；
        TaskEntity task = new TaskEntity();
        task.setTaskId(taskItemEntity.getTaskId());
        task.setModTime(new Date());
        task.setUpdater(currentUser.getId());
        task.setUpdaterName(currentUser.getUsername());

        List<TaskItemEntity> taskItemList = taskItemMapper.findAllByTaskId(taskItemEntity.getTaskId());

        if (taskItemList.stream().anyMatch(taskItem -> taskItem.getDoneStatus() == 0)) {
            //如果有一个子任务是待处理（状态为0），则主任务是处理中（状态为0）。
            task.setDoneStatus(0);
        } else if (taskItemList.stream().allMatch(taskItem -> taskItem.getDoneStatus() == 1)) {
            //如果所有子任务都是已处理（状态为1），则主任务是已处理（状态为1）。
            task.setDoneStatus(1);
        } else if (taskItemList.stream().allMatch(taskItem -> taskItem.getDoneStatus() == 2)) {
            //如果所有子任务都是已关闭（状态为2），则主任务是已关闭（状态为2）。
            task.setDoneStatus(2);
        } else {
            // 如果子任务中既有已处理（状态为1）又有已关闭（状态为2），则主任务是已处理（状态为1）。
            task.setDoneStatus(1);
        }

        taskMapper.updateByPrimaryKeySelective(task);
        checkIsNeedSendMessage(taskHandle.getTaskItemId());
    }


    @Override
    public List<DataDictionaryDto> getSupervisionType(Integer businessId) {
        List<BusinessSupervisionTypeEnum> opportunityValidation = new ArrayList<>();
        BusinessChanceApiDto businessChance = businessChanceApiService.getBusinessChance(businessId);
        if (businessChance != null) {
            // 根据阶段返回不同的督导类型
            switch (businessChance.getStage()) {
                case 2:
                    // 商机验证阶段(2商机验证)
                    opportunityValidation = BusinessSupervisionTypeEnum.getEnumsByType("OpportunityValidation");
                    break;
                case 3:
                case 4:
                    // 产品验证阶段 (3.初级方案&4.最终方案)
                    opportunityValidation = BusinessSupervisionTypeEnum.getEnumsByType("ProductSolution");
                    break;
                default:
                    opportunityValidation = BusinessSupervisionTypeEnum.getEnumsByType("Other");
                    break;
            }
        }
        return opportunityValidation.stream()
                .map(enumValue -> new DataDictionaryDto(enumValue.getValue(), enumValue.getLabel()))
                .collect(Collectors.toList());
    }


    @Override
    public List<TaskUserDto> getCreator() {
        List<TaskUserDto> taskUserDtoList = new ArrayList<>();
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        RestfulResult<List<UserInfoDto>> allSubUser = uacWxUserInfoApiService.getAllSubUser(currentUser.getId());
        if (allSubUser.isSuccess()) {
            List<UserInfoDto> userInfoDtoList = allSubUser.getData();
            if (userInfoDtoList != null) {
                userInfoDtoList.forEach(userInfoDto -> {
                    TaskUserDto taskUserDto = new TaskUserDto();
                    taskUserDto.setUserId(userInfoDto.getId());
                    taskUserDto.setUserName(userInfoDto.getDisplayName());
                    taskUserDto.setAliasHeadPicture(userInfoDto.getAliasHeadPicture());
                    taskUserDtoList.add(taskUserDto);
                });
            }
        }
        return taskUserDtoList;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void deadlineReminder() {
        List<DeadlineRole> deadlineRoles = JSON.parseArray(this.deadlineRole, DeadlineRole.class);
        Map<Integer, Integer> deadlineRolesMap = deadlineRoles.stream().collect(Collectors.toMap(DeadlineRole::getTaskType, DeadlineRole::getWarningTime, (v1, v2) -> v1));
        Integer productSchemeWarningTime = deadlineRolesMap.get(Integer.valueOf(TaskTypeEnum.INITIAL_PRODUCT_PLAN.getValue()));
        Integer itemInquiryWarningTime = deadlineRolesMap.get(Integer.valueOf(TaskTypeEnum.SINGLE_ITEM_INQUIRY.getValue()));
        Integer generalInquiryWarningTime = deadlineRolesMap.get(Integer.valueOf(TaskTypeEnum.COMPREHENSIVE_INQUIRY.getValue()));
        Integer quoteWarningTime = deadlineRolesMap.get(Integer.valueOf(TaskTypeEnum.COLLABORATIVE_QUOTATION.getValue()));
        log.info("初步产品方案、单品询价、综合询价-临期提醒,productSchemeWarningTime:{},itemInquiryWarningTime:{},generalInquiryWarningTime:{}", productSchemeWarningTime, itemInquiryWarningTime, generalInquiryWarningTime);
        List<TaskReminder> taskReminderList = taskItemMapper.findDeadlineReminder(productSchemeWarningTime, itemInquiryWarningTime, generalInquiryWarningTime, quoteWarningTime);
        log.info("初步产品方案、单品询价、综合询价-临期提醒,临期提醒任务:{}", JSON.toJSONString(taskReminderList));
        if (CollUtil.isEmpty(taskReminderList)) {
            log.info("初步产品方案、单品询价、综合询价-临期提醒,临期提醒任务为空");
            return;
        }
        String now = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        for (TaskReminder taskReminder : taskReminderList) {
            log.info("初步产品方案、单品询价、综合询价-临期提醒,临期提醒任务:{}", JSON.toJSONString(taskReminder));
            try {
                SendMessageDto sendMessageDto = new SendMessageDto();
                sendMessageDto.setType(TaskTypeEnum.getTaskTypeLabel(taskReminder.getMainTaskType().toString()));
                sendMessageDto.setUserNumber(taskReminder.getNumber());
                if (ErpConstant.ONE.equals(taskReminder.getBizType())) {
                    sendMessageDto.setBusinessNo(taskReminder.getBizNo());
                }
                if (ErpConstant.THREE.equals(taskReminder.getBizType())) {
                    CrmBusinessChanceEntity crmBusinessChanceEntity = crmQuoteOrderService.selectBusinessChanceEntityByQuoteorderId(taskReminder.getBizId());
                    sendMessageDto.setBusinessNo(crmBusinessChanceEntity.getBussinessChanceNo());
                    taskReminder.setBizId(crmBusinessChanceEntity.getBussinessChanceId());
                }
                sendMessageDto.setUrl(jumpService.getjumpUrl(lxcrmUrl + "/crm/businessChance/profile/detail?id=" + taskReminder.getBizId() + "&taskItemId=" + taskReminder.getTaskItemId(), JumpErpTitleEnum.TASK_LIST));
                sendMessageDto.setFormat(String.format(DEADLINE_REMINDER, now, sendMessageDto.getBusinessNo(), sendMessageDto.getType()));
                sendCardMsg(sendMessageDto, "任务临期提醒");
                taskItemMapper.updateIsDeadlineReminderByTaskItemId(ErpConstant.ONE, taskReminder.getTaskItemId());
            } catch (Exception e) {
                log.info("初步产品方案、单品询价、综合询价-临期提醒,发送卡片消息异常", e);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void timeoutReminder() {
        List<TaskReminder> taskReminderList = taskItemMapper.findTimeoutReminder();
        //20240929 新增协同报价类型的任务
        log.info("初步产品方案、单品询价、综合询价-超时提醒,超时提醒任务:{}", JSON.toJSONString(taskReminderList));
        if (CollUtil.isEmpty(taskReminderList)) {
            log.info("初步产品方案、单品询价、综合询价-超时提醒,超时提醒任务为空");
            return;
        }
        String now = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        for (TaskReminder taskReminder : taskReminderList) {
            log.info("初步产品方案、单品询价、综合询价-超时提醒,超时提醒任务:{}", JSON.toJSONString(taskReminder));
            try {
                SendMessageDto sendMessageDto = new SendMessageDto();
                sendMessageDto.setType(TaskTypeEnum.getTaskTypeLabel(taskReminder.getMainTaskType().toString()));
                RestfulResult<UserInfoDto> user = uacWxUserInfoApiService.getUser(taskReminder.getTaskUser(), 1);
                if (user.isSuccess()) {
                    Integer parentUserId = user.getData().getParentUserId();
                    UserDto parentUser = userApiService.getUserBaseInfo(parentUserId);
                    if (Objects.nonNull(parentUser)) {
                        sendMessageDto.setUserNumber(parentUser.getNumber());
                    }
                }
                if (ErpConstant.ONE.equals(taskReminder.getBizType())) {
                    sendMessageDto.setBusinessNo(taskReminder.getBizNo());
                }
//                if (ErpConstant.THREE.equals(taskReminder.getBizType())) {
//                    String businessChanceNo = crmQuoteOrderService.selectBusinessChanceByQuoteorderId(taskReminder.getBizId());
//                    sendMessageDto.setBusinessNo(businessChanceNo);
//                }
                if (ErpConstant.THREE.equals(taskReminder.getBizType())) {
                    CrmBusinessChanceEntity crmBusinessChanceEntity = crmQuoteOrderService.selectBusinessChanceEntityByQuoteorderId(taskReminder.getBizId());
                    sendMessageDto.setBusinessNo(crmBusinessChanceEntity.getBussinessChanceNo());
                    taskReminder.setBizId(crmBusinessChanceEntity.getBussinessChanceId());//此场景原值存的是报价单的ID，跳转链接需要传商机的ID
                }
                sendMessageDto.setUrl(jumpService.getjumpUrl(lxcrmUrl + "/crm/businessChance/profile/detail?id=" + taskReminder.getBizId() + "&taskItemId=" + taskReminder.getTaskItemId(), JumpErpTitleEnum.TASK_LIST));
                sendMessageDto.setFormat(String.format(TIMEOUT_REMINDER, now, sendMessageDto.getBusinessNo(), sendMessageDto.getType()));
                sendCardMsg(sendMessageDto, "任务超时提醒");
                taskItemMapper.updateIsTimeoutReminderByTaskItemId(ErpConstant.ONE, taskReminder.getTaskItemId());
            } catch (Exception e) {
                log.info("初步产品方案、单品询价、综合询价-超时提醒,发送卡片消息异常", e);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void myTodoTimeoutStatistic() {
        String today = getNowDay();

        // 查询所有拥有下属的人
        List<UserDto> parentFromUac = userApiService.searchIsParentFromUac();
        log.info("我的待办任务超时统计,parentFromUac:{}", JSON.toJSONString(parentFromUac));
        String now = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        // 遍历每个有下属的人，统计其下属的待办任务
        for (UserDto userDto : parentFromUac) {
            List<UserDto> subUserList = userApiService.queryUserSubFromUac(userDto.getUserId());
            if (CollUtil.isEmpty(subUserList)) {
                log.info("该用户没有下属,userDto:{}", JSON.toJSONString(userDto));
                continue;
            }
            List<Integer> subUserIds = subUserList.stream().map(UserDto::getUserId).collect(Collectors.toList());
            int count = taskItemMapper.countMyTodoTimeout(subUserIds);
            if (count <= 0) {
                log.info("该用户没有待办任务,subUserIds:{}", JSON.toJSONString(subUserIds));
                continue;
            }
            log.info("该用户有待办任务,userDto:{},subUserIds:{},count:{}", JSON.toJSONString(userDto), JSON.toJSONString(subUserIds), count);
            try {
                SendMessageDto sendMessageDto = new SendMessageDto();
                sendMessageDto.setUserNumber(userDto.getNumber());
                sendMessageDto.setUrl(jumpService.getjumpUrl(lxcrmUrl + "/crm/task/profile/index?initStartDeadLine=2024-09-01&initEndDeadLine=" + today, JumpErpTitleEnum.TASK_LIST));
                sendMessageDto.setFormat(String.format(MY_TODO_TIMEOUT_STATISTIC, now, count));
                sendCardMsg(sendMessageDto, "任务超时提醒");
            } catch (Exception e) {
                log.info("我的待办任务超时统计,发送卡片消息异常", e);
            }
        }
    }

    public static String getNowDay() {
        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = currentDate.format(formatter);
        return formattedDate;
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void myInitiativeTimeoutStatistic() {
        String today = getNowDay();
        // 查询所有拥有下属的人
        List<UserDto> parentFromUac = userApiService.searchIsParentFromUac();
        log.info("我的发起任务超时统计,parentFromUac:{}", JSON.toJSONString(parentFromUac));
        String now = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        // 遍历每个有下属的人，统计其下属的发起任务
        for (UserDto userDto : parentFromUac) {
            List<UserDto> subUserList = userApiService.queryUserSubFromUac(userDto.getUserId());
            if (CollUtil.isEmpty(subUserList)) {
                log.info("该用户没有下属,userDto:{}", JSON.toJSONString(userDto));
                continue;
            }
            List<Integer> subUserIds = subUserList.stream().map(UserDto::getUserId).collect(Collectors.toList());
            int count = taskItemMapper.countMyInitiativeTimeout(subUserIds);
            if (count <= 0) {
                log.info("该用户没有发起任务,subUserIds:{}", JSON.toJSONString(subUserIds));
                continue;
            }
            log.info("该用户有发起任务,userDto:{},subUserIds:{},count:{}", JSON.toJSONString(userDto), JSON.toJSONString(subUserIds), count);
            try {
                SendMessageDto sendMessageDto = new SendMessageDto();
                sendMessageDto.setUserNumber(userDto.getNumber());
                sendMessageDto.setUrl(jumpService.getjumpUrl(lxcrmUrl + "/crm/task/profile/index?list=2&initEndDeadLine=" + today, JumpErpTitleEnum.TASK_LIST));
                sendMessageDto.setFormat(String.format(MY_INITIATIVE_TIMEOUT_STATISTIC, now, count));
                sendCardMsg(sendMessageDto, "任务超时提醒");
            } catch (Exception e) {
                log.info("我的发起任务超时统计,发送卡片消息异常", e);
            }
        }
    }

    public void sendCardMsg(SendMessageDto sendMessageDto, String title) {
        WxCpMessage wxCpMessage = new WxCpMessage();
        wxCpMessage.setToUser(sendMessageDto.getUserNumber());
        wxCpMessage.setMsgType("textcard");
        wxCpMessage.setTitle(StringUtils.isNotBlank(title) ? title : "提醒");
        wxCpMessage.setDescription(sendMessageDto.getFormat());
        wxCpMessage.setUrl(sendMessageDto.getUrl());
        wxCpMessage.setBtnTxt("详情");
        log.info("CRM发送卡片消息入参：{}", JSON.toJSON(wxCpMessage));
        try{
            uacWxUserInfoApiService.sendToUser(wxCpMessage);
        }catch (Exception e){
            log.error("卡片消息推送失败",e);
        }

    }

    /**
     * 创建待办任务
     *
     * @param taskId   taskId
     * @param taskUser taskUser
     */
    private Long createTodoTask(Long taskId, Integer taskUser, CurrentUser currentUser) {
        TaskItemEntity taskItemEntity = new TaskItemEntity();
        taskItemEntity.setTaskId(taskId);
        taskItemEntity.setTaskUser(taskUser);

        UserDto userById = userApiService.getUserBaseInfo(taskUser);
        taskItemEntity.setTaskUserName(userById.getUsername());

        taskItemEntity.setIsDelete(ErpConstant.F);
        taskItemEntity.setDoneStatus(0);

        taskItemEntity.setAddTime(new Date());
        taskItemEntity.setModTime(new Date());
        taskItemEntity.setCreator(currentUser.getId());
        taskItemEntity.setUpdater(currentUser.getId());
        taskItemEntity.setCreatorName(currentUser.getUsername());
        taskItemEntity.setUpdaterName(currentUser.getUsername());

        taskItemMapper.insertSelective(taskItemEntity);

        return taskItemEntity.getTaskItemId();
    }

    private TaskUserDto getTaskUser(Integer userId) {
        UserDto user = userApiService.getUserBaseInfo(userId);
        return TaskUserDto.builder().userId(userId).userName(user.getUsername()).aliasHeadPicture(user.getAliasHeadPicture()).build();
    }


    @Override
    public Integer queryTaskForBussinessChance(Integer bussinessChanceId) {
        List<Integer> taskTypeList = new ArrayList<>();
        taskTypeList.addAll(Arrays.asList(new Integer[]{2, 3, 4}));
        return taskMapper.getTaskForBussinessChance(bussinessChanceId, taskTypeList);
    }

    @Autowired
    private MessageApiService messageApiService;

    public void sendMessage(Integer messageTemplateId,List<Integer> userIds,Map<String, String> params,String url,String... str){
        MessageDto messageDto = new MessageDto();
        messageDto.setUserIds(userIds);
        messageDto.setMessageTemplateId(messageTemplateId);
        messageDto.setParams(params);
        messageDto.setUrl(url);
        try{
            messageApiService.send(messageDto);
        }catch (Exception e){
            log.error("推送消息到erp失败",e);
        }
    }

    @Override
    public void handleTaskCompleteForLeadsClose(Integer bizId){
        List<TaskItemDto> againTodoTask = taskItemMapper.findByBizIdAndBizTypeForCloseBiz(bizId,Arrays.asList(2));
        if (CollectionUtils.isNotEmpty(againTodoTask)) {
            log.info("线索关闭，自动待处理待办任务:{}", JSON.toJSONString(againTodoTask));
            againTodoTask.forEach(task -> {
                TaskHandleDto taskHandleDto = new TaskHandleDto();
                taskHandleDto.setTaskId(task.getTaskId());
                taskHandleDto.setTaskItemId(task.getTaskItemId());
                taskHandleDto.setDoneStatus(ErpConstant.TWO);
                taskHandleDto.setDoneRemark("线索关闭，自动关闭任务");

                try {
                    this.handle(taskHandleDto);
                } catch (Exception e) {
                    log.warn("待办任务处理失败，taskItemId:{}", task.getTaskItemId(), e);
                }
            });
        }
    }

    @Override
    public void handleTaskCompleteForBusincessChance(Integer bizId) {

        handleTaskCompleteForBusincessChance(bizId,ErpConstant.TWO,"商机关闭，自动关闭任务");
    }
    @Override
    public void handleTaskCompleteForBusincessChance(Integer bizId,Integer doneStatus,String doneRemark) {
        List<TaskItemDto> againTodoTask = taskItemMapper.findByBizIdAndBizTypeForCloseBiz(bizId,Arrays.asList(1,3));
        if (CollectionUtils.isNotEmpty(againTodoTask)) {
            log.info("商机关闭，自动待处理待办任务:{} {}", JSON.toJSONString(againTodoTask),doneRemark);
            againTodoTask.forEach(task -> {
                TaskHandleDto taskHandleDto = new TaskHandleDto();
                taskHandleDto.setTaskId(task.getTaskId());
                taskHandleDto.setTaskItemId(task.getTaskItemId());
                taskHandleDto.setDoneStatus(doneStatus);
                taskHandleDto.setDoneRemark(doneRemark);
                try {
                    this.handle(taskHandleDto);
                } catch (Exception e) {
                    log.warn("待办任务处理失败，taskItemId:{}", task.getTaskItemId(), e);
                }
            });
        }
    }


    @Override
    public void handleTaskCompleteForBusincessChance(TaskDto taskDto) {
        List<TaskItemDto> againTodoTask = taskItemMapper.findByBizIdAndBizTypeForCloseBiz(taskDto.getBizId(),Arrays.asList(1,3));
        if (CollectionUtils.isNotEmpty(againTodoTask)) {
            log.info("商机关闭，自动待处理待办任务:{}", JSON.toJSONString(againTodoTask));
            againTodoTask.forEach(task -> {
                TaskHandleDto taskHandleDto = new TaskHandleDto();
                taskHandleDto.setTaskId(task.getTaskId());
                taskHandleDto.setTaskItemId(task.getTaskItemId());
                taskHandleDto.setDoneStatus(ErpConstant.TWO);
                taskHandleDto.setDoneRemark("商机关闭，自动关闭任务");
                taskHandleDto.setApiUserId(taskDto.getApiUserId());
                taskHandleDto.setApiUserName(taskDto.getApiUserName());
                try {
                    this.handle(taskHandleDto);
                } catch (Exception e) {
                    log.warn("待办任务处理失败，taskItemId:{}", task.getTaskItemId(), e);
                }
            });
        }
    }


}

