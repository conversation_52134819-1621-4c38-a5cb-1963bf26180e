package com.vedeng.erp.business.dto;

import lombok.Data;

import java.util.List;

/**
 * {
 *                     id: 1,
 *                     label: '一级 1',
 *                     children: [{
 *                         id: 4,
 *                         label: '二级 1-1',
 *                         children: [{
 *                             id: 9,
 *                             label: '三级 1-1-1'
 *                         }, {
 *                             id: 10,
 *                             label: '三级 1-1-2'
 *                         }]
 *                     }]
 *                 }
 */
@Data
public class NodeDto {

    private int id;

    private String label;

    private List<NodeDto> children;
}
