$(function(){
    $("#addAfteCapitalPayForm").submit(function(){
        var traderMode = $("#traderModeRad:checked").val();
        if(traderMode == undefined || traderMode == 0 || traderMode.length == 0){
            warnTips("traderMode","请选择交易方式");//文本框ID和提示用语
            return false;
        }
        var comments = $("#comments").val();
        if(comments!="" && comments.length>512){
            warn2Tips("comments","交易备注长度应该在0-512个字符之间");
            return false;
        }
        $("#traderMode").val($("#traderModeRad:checked").val());
        checkLogin();
        $.ajax({
            url:page_url+'/order/afterSalesCommon/saveAddAfterCapitalBill.do',
            data:$('#addAfteCapitalPayForm').serialize(),
            type:"POST",
            dataType : "json",
            success:function(data){
                if (data.code == 0) {
//					$("#close-layer").click();
                    /*layer.alert(data.message, { icon: 1 },
                        function (index) {
                            layer.close(index);
                        }
                    );*/
                    if($("#refreshParent") && $("#refreshParent").val() == 1){
                        parent.vm.queryList();
                        $("#close-layer").click();
                    }else {
                        parent.location.reload();
                    }
                } else {
                    layer.alert(data.message, { icon: 2 },
                        function (index) {
                            layer.close(index);
                        }
                    );
                }
            },error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
        return false;
    })
});
