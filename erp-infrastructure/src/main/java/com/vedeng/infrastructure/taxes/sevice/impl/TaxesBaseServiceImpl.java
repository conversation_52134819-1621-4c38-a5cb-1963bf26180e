package com.vedeng.infrastructure.taxes.sevice.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.infrastructure.taxes.domain.TaxesCommonApiResult;
import com.vedeng.infrastructure.taxes.domain.TaxesOpenApiResult;
import com.vedeng.infrastructure.taxes.sevice.ITaxesBaseService;
import com.vedeng.infrastructure.taxes.sevice.ITaxesToolService;
import com.vedeng.infrastructure.taxes.utils.TaxesHttpClientUtils;
import com.vedeng.infrastructure.taxes.utils.TaxesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

/**
 * 税金系统接口
 */
@Service
@Slf4j
public class TaxesBaseServiceImpl implements ITaxesBaseService, ITaxesToolService {

    /**
     * 销项接口
     * @param taxesParam
     * @param interfaceCode
     * @return
     */
    @Override
    public ITaxesResult openapi(ITaxesParam taxesParam, String interfaceCode) {
        TaxesOpenApiResult openApiResult;
        try{
            String jsonStr = saleAssemble(interfaceCode, JSONObject.toJSONString(taxesParam));
            String taxesUrl = TaxesUtil.taxesConfig.taxesUrl + TaxesUtil.taxesConfig.saleUrl;
            String resultStr = post(interfaceCode, jsonStr, taxesUrl);
            Gson gson = new Gson();
            openApiResult = gson.fromJson(resultStr, TaxesOpenApiResult.class);
        }catch (ServiceException se){
            openApiResult = new TaxesOpenApiResult(se);
        }
        return openApiResult;
    }

    /**
     * 公共接口
     * @param taxesParam
     * @param interfaceCode
     * @return
     */
    @Override
    public ITaxesResult commonapi(ITaxesParam taxesParam, String interfaceCode) {
        TaxesCommonApiResult commonApiResult;
        try{
            String url = TaxesUtil.taxesConfig.taxesUrl + TaxesUtil.taxesConfig.commonUrl;
            String jsonStr = JSONObject.toJSONString(taxesParam);
            String resultStr = post(interfaceCode, jsonStr, url);
            Gson gson = new Gson();
            commonApiResult = gson.fromJson(resultStr, TaxesCommonApiResult.class);
        }catch (ServiceException se){
            commonApiResult = new TaxesCommonApiResult(se);
        }
        return commonApiResult;
    }

    /**
     * commonApi接口数据封装
     * @param interfaceCode
     * @param dataJson
     * @return
     */
    private String commonAssemble(String interfaceCode, String dataJson) {
        // 准备数据
        JSONObject paramsJson = new JSONObject(true);
        paramsJson.put("requestcode",  interfaceCode);
        paramsJson.put("data", dataJson);
        return paramsJson.toString();
    }

    /**
     * 销项票接口数据封装
     * @param interfaceCode
     * @param dataJson
     * @return
     */
    private String saleAssemble(String interfaceCode, String dataJson) {
        // 准备数据
        JSONObject globalInfo = new JSONObject(true);
        globalInfo.put("application_code",  TaxesUtil.taxesConfig.appCode);
        globalInfo.put("interface_code", interfaceCode);
        globalInfo.put("version", TaxesUtil.taxesConfig.taxVersion);
        globalInfo.put("app_abbr", "");
        globalInfo.put("app_id", TaxesUtil.taxesConfig.appId);
        globalInfo.put("data_id", "");

        JSONObject json = new JSONObject(true);
        json.put("global_info",globalInfo);
        json.put("data", Base64Utils.encodeToString(dataJson.getBytes()));
        json.put("data_sign", "");
        String dataSign = TaxesUtil.getTaxesSign(json.toJSONString());
        json.put("data_sign", dataSign);
        return json.toString();
    }


    /**
     * 重试3次,策略 2s 3s 4.5s
     * @param interfaceCode
     * @param jsonStr
     * @param url
     * @return
     */
//    @Retryable(value = ServiceException.class,maxAttempts = 3,backoff = @Backoff(delay = 2000,multiplier = 1.5))
    @Override
    public String post(String interfaceCode, String jsonStr, String url) {
        return TaxesHttpClientUtils.post(interfaceCode,jsonStr,url);
    }

    /**
     * 红票申请接口
     * @param taxesParamJsonStr
     * @param interfaceCode
     * @return
     */
    @Override
    public ITaxesResult openapi(String taxesParamJsonStr, String interfaceCode) {
        TaxesOpenApiResult openApiResult;
        try{
            String jsonStr = saleAssemble(interfaceCode, taxesParamJsonStr);
            String taxesUrl = TaxesUtil.taxesConfig.taxesUrl + TaxesUtil.taxesConfig.saleUrl;
            String resultStr = post(interfaceCode, jsonStr, taxesUrl);
            Gson gson = new Gson();
            openApiResult = gson.fromJson(resultStr, TaxesOpenApiResult.class);
        }catch (ServiceException se){
            openApiResult = new TaxesOpenApiResult(se);
        }
        return openApiResult;
    }
}
