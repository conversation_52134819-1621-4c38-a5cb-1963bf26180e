package com.vedeng.erp.kingdee.batch.chain.context;

import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/01/15 12:11
 * @description
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SaleBackOutInBindContext {

    // SN未匹配List
    List<BatchWarehouseGoodsOutInItemDto> snList ;
    // 批次号未匹配List
    List<BatchWarehouseGoodsOutInItemDto> batchList;
    // SN和批次号未匹配List
    List<BatchWarehouseGoodsOutInItemDto> snAndBatchList;
    // 第二次SN未匹配List
    List<BatchWarehouseGoodsOutInItemDto> snList2;
    // 第二次批次号未匹配List
    List<BatchWarehouseGoodsOutInItemDto> batchList2;
    // 第二次SN和批次号未匹配List
    List<BatchWarehouseGoodsOutInItemDto> snAndBatchList2;

    // 第三次SN和批次号未匹配List
    List<BatchWarehouseGoodsOutInItemDto> snAndBatchList3;

    // 匹配后剩余未匹配SN的出库商品，第二次匹配时需清空
    List<BatchWarehouseGoodsOutInItemDto> remainSnOutGoods;
    // 匹配后剩余未匹配批次号的出库商品，第二次匹配时需清空
    List<BatchWarehouseGoodsOutInItemDto> remainBatchOutGoods;
    // 匹配后剩余未匹配SN和批次号的出库商品
    List<BatchWarehouseGoodsOutInItemDto> remainSnBatchOutGoods;

    // 上一次遍历的入库单单号
    String outInNo;
    // 所有的入库单
    List<BatchWarehouseGoodsOutInDto> inOrders;

    // 销售订单有效时间
    Long validTime;
}
