package com.vedeng.erp.trader.feign;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.onedataapi.api.archived.TraderArchivedApi;
import com.vedeng.onedataapi.api.archived.req.TraderArchivedReqDto;
import com.vedeng.onedataapi.api.archived.res.TraderArchivedItemResDto;
import com.vedeng.onedataapi.api.archived.res.TraderArchivedResDto;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 大数据服务查询客户档案
 */
@FeignApi(serverName = "onedataapi")
public interface OneDataTraderEventApi extends TraderArchivedApi {

	/**
     * 筛选项列举
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /archived/filterItems")
    @Override
    RestfulResult<TraderArchivedItemResDto> filterItems();
	
    /**
     * 获取客户档案
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /archived/traderArchived")
    @Override
    RestfulResult<TraderArchivedResDto> traderArchived(@RequestBody TraderArchivedReqDto traderArchivedReqDto);

}
