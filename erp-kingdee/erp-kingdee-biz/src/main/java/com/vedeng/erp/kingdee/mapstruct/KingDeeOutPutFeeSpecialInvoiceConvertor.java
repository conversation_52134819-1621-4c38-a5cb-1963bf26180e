package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeOutPutFeeSpecialInvoiceEntity;
import com.vedeng.erp.kingdee.dto.OutPutFeeSpecialInvoiceDetailDto;
import com.vedeng.erp.kingdee.dto.OutPutFeeSpecialInvoiceDto;
import org.mapstruct.*;

import java.util.List;

/**
 * 金蝶 销项专票 dto 转 entity
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeOutPutFeeSpecialInvoiceConvertor extends BaseMapStruct<KingDeeOutPutFeeSpecialInvoiceEntity,OutPutFeeSpecialInvoiceDto> {
    /**
     * KingDeeOutPutFeeSpecialInvoiceEntity
     *
     * @param dto OutPutFeeSpecialInvoiceDto
     * @return KingDeeOutPutFeeSpecialInvoiceEntity
     */
    @Override
    @Mapping(target = "fsaleexinventry", source = "FSALEEXINVENTRY", qualifiedByName = "listToString")
    KingDeeOutPutFeeSpecialInvoiceEntity toEntity(OutPutFeeSpecialInvoiceDto dto);

    /**
     * dto 原List 转 JSON
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("listToString")
    default String listToString(List<OutPutFeeSpecialInvoiceDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSON.toJSONString(source);
    }
}
