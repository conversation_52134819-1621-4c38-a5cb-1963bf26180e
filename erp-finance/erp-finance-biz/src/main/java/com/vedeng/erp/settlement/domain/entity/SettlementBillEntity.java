package com.vedeng.erp.settlement.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 财务结算单
 */
@Getter
@Setter
public class SettlementBillEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer settleBillId;

    /**
     * 结算单号
     */
    private String settleBillNo;

    /**
     * 交易人ID
     */
    private Integer traderId;

    /**
     * 结算主体类型1.客户 2.供应商
     */
    private Integer traderSubjectType;

    /**
     * 结算主体id  (客户或供应商id)
     */
    private Integer traderSubjectId;

    /**
     * 结算业务来源类型
     * saleOrder.销售单
     * buyOrder.采购单
     * buyOrderExpense.采购费用单
     * buyOrderAfterSale.采购售后单
     * buyOrderExpenseAfterSale.采购费用售后单
     * buyOrderRebateChargeApply.采购返利结算收款申请
     */
    private String sourceType;

    /**
     * 结算业务来源编号
     */
    private String businessSourceTypeNo;

    /**
     * 结算业务来源id
     */
    private Integer businessSourceTypeId;

    /**
     * 结算总金额
     */
    private BigDecimal settleAmount;

    /**
     * 已结算金额（含信用，返利，实付）
     */
    private BigDecimal alreadySettleAmount;

    /**
     * 账期周期
     */
    private Integer accountPeriod;

    /**
     * 账期金额
     */
    private BigDecimal accountPeriodAmount;

    /**
     * 账期已还金额
     */
    private BigDecimal alreadyRepaidAmount;

    /**
     * 交易笔数（根据业务单号统计）
     */
    private Integer transactionCount;

    /**
     * 结算状态
     * 0：未结算
     * 1：部分结算
     * 2：已结算
     */
    private Integer settlementStatus;

    /**
     * 是否删除
     */
    private Integer isDelete;
}