package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseVatSpecialInvoiceEntity;
import com.vedeng.erp.kingdee.dto.PurchaseVatSpecialInvoiceDetailDto;
import com.vedeng.erp.kingdee.dto.PurchaseVatSpecialInvoiceDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * 金蝶 采购专票 dto 转 实体
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeePurchaseVatSpecialInvoiceConvertor extends BaseMapStruct<KingDeePurchaseVatSpecialInvoiceEntity,PurchaseVatSpecialInvoiceDto> {
    /**
     * KingDeePurchaseVatSpecialInvoiceEntity
     *
     * @param dto PurchaseVatSpecialInvoiceDto
     * @return KingDeePurchaseVatSpecialInvoiceEntity
     */
    @Mapping(target = "FBillTypeId", source = "FBillTypeID")
    @Mapping(target = "fpurchaseicentry", source = "FPURCHASEICENTRY", qualifiedByName = "listToString")
    @Override
    KingDeePurchaseVatSpecialInvoiceEntity toEntity(PurchaseVatSpecialInvoiceDto dto);


    @Mapping(target = "FBillTypeID", source = "FBillTypeId")
    @Mapping(target = "FPURCHASEICENTRY", source = "fpurchaseicentry", qualifiedByName = "stringToList")
    @Override
    PurchaseVatSpecialInvoiceDto toDto(KingDeePurchaseVatSpecialInvoiceEntity entity);

    /**
     * dto 原List 转 JSON
     *
     * @param source 对象
     * @return String String
     */
    @Named("listToString")
    default String listToString(List<PurchaseVatSpecialInvoiceDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSON.toJSONString(source);
    }

    /**
     * entity 中string 转 原对象
     *
     * @param data string
     * @return List<KingDeePayBillDto> dto中的对象
     */
    @Named("stringToList")
    default List<PurchaseVatSpecialInvoiceDetailDto> entryJsonArrayToList(String data) {
        if (StrUtil.isEmpty(data)) {
            return Collections.emptyList();
        }
        return JSONArray.parseArray(data,PurchaseVatSpecialInvoiceDetailDto.class);
    }
}
