package com.vedeng.erp.finance.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class MatchSaleOrderDto {
    private Long saleorderId;           // 销售订单ID
    private String saleorderNo;         // 销售订单编号
    private BigDecimal totalAmount;     // 总金额(已扣除退款金额)
    private BigDecimal prepaidAmount;   // 预付款金额
    private Integer haveAccountPeriod;  // 是否有账期
    private Integer paymentStatus;      // 付款状态
    private BigDecimal accountPeriodAmount; // 账期金额
    private Long traderId;              // 交易方ID
    private String traderName;          // 交易方名称
    private BigDecimal retainageAmount;  // 保留金额/质保金
    private String traderContactName;   // 交易方联系人名称
    private Integer validStatus;        // 有效状态
    private Integer isPayment;          // 是否已付款
    private String validTime;             // 生效时间
    private Integer status;             // 订单状态
    private BigDecimal receivedAmount;  // 已收金额
}
