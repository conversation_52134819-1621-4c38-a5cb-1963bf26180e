package com.vedeng.erp.business.service.api;

import com.vedeng.erp.business.domain.entity.BussinessChanceEntity;
import com.vedeng.erp.business.mapper.BussinessChanceMapper;
import com.vedeng.erp.trader.dto.BusinessChanceApiDto;
import com.vedeng.erp.trader.service.BusinessChanceApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: BusinessChanceApiService
 * @date 2024/7/30 12:52
 */
@Service
public class BusinessChanceApiServiceImpl implements BusinessChanceApiService {

    @Autowired
    BussinessChanceMapper bussinessChanceMapper;



    @Override
    public BusinessChanceApiDto getBusinessChance(Integer businessChanceId) {
        BusinessChanceApiDto businessChanceDto = new BusinessChanceApiDto();
        BussinessChanceEntity bussinessChanceEntity = bussinessChanceMapper.selectByPrimaryKey(businessChanceId);
        if (bussinessChanceEntity != null) {
            businessChanceDto.setBussinessChanceId(bussinessChanceEntity.getBussinessChanceId());
            businessChanceDto.setTerminalTraderName(bussinessChanceEntity.getTerminalTraderName());
            businessChanceDto.setTerminalTraderType(bussinessChanceEntity.getTerminalTraderType());
            businessChanceDto.setStage(bussinessChanceEntity.getStage());
            businessChanceDto.setBussinessChanceNo(bussinessChanceEntity.getBussinessChanceNo());
            businessChanceDto.setSaleId(bussinessChanceEntity.getUserId());
        }
        return businessChanceDto;
    }

}
