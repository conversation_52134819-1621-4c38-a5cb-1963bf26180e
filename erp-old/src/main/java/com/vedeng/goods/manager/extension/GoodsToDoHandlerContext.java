package com.vedeng.goods.manager.extension;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class GoodsToDoHandlerContext {

    public static final String GOODS_ID = "GOODS_ID";

    private final static ThreadLocal<Map<String, Object>> ATTRIBUTES = ThreadLocal.withInitial(LinkedHashMap::new);

    private final static GoodsToDoHandlerContext INSTANCE = new GoodsToDoHandlerContext();

    private GoodsToDoHandlerContext() {
    }

    public static GoodsToDoHandlerContext getInstance() {
        return INSTANCE;
    }


    public Integer getGoodsId() {
        return (Integer) getAttributes().get(GOODS_ID);
    }

    public void setGoodsId(Integer goodsId) {
        setAttribute(GOODS_ID, goodsId);
    }

    private void setAttribute(String name, Object value) {
        Map<String, Object> attributes = getAttributes();
        attributes.put(name, value);
    }

    private Map<String, Object> getAttributes() {
        return ATTRIBUTES.get();
    }

    public void remove() {
        ATTRIBUTES.remove();
    }
}
