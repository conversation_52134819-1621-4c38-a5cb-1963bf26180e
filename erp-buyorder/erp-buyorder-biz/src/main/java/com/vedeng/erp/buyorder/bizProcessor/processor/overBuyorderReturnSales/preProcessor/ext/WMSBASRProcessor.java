package com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.preProcessor.ext;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.erp.buyorder.bizProcessor.dto.BizDto;
import com.vedeng.erp.buyorder.bizProcessor.exception.PreProcessorException;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.preProcessor.OverBuyorderAfterSaleReturnOrderPreProcessor;
import com.wms.service.listenner.PurchaseReturnConfirmListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WMSBASRProcessor extends OverBuyorderAfterSaleReturnOrderPreProcessor {

    @Autowired
    PurchaseReturnConfirmListener purchaseReturnConfirmListener;

    @Override
    public void doProcess(BizDto bizDto) throws PreProcessorException {
        log.info("处理采购退货前置wms下传处理逻辑,售后单:{}", JSON.toJSONString(bizDto));
        try {
            AfterSalesVo afterSalesVo = (AfterSalesVo) bizDto.getBizData();
            purchaseReturnConfirmListener.onActionHappen(afterSalesVo.getAfterSalesId());
        } catch (Exception e) {
            log.error("WMSBASRProcessor 处理报错,业务信息:{},错误:{}", JSON.toJSONString(bizDto),e);
            throw new PreProcessorException("WMSBASRProcessor处理报错  wms下发报错");
        }
    }
}
