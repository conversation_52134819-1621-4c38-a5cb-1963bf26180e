<%--
  Created by IntelliJ IDEA.
  User: Simple
  Date: 2021/10/19
  Time: 13:40
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="终端列表" scope="application"/>
<%@ include file="../../common/common.jsp" %>

<%--<link rel="stylesheet" href="<%=basePath%>static/css/orderstream/pagination.css?rnd=${resourceVersionKey}"/>--%>
<%--<script type="text/javascript"--%>
<%--        src='<%= basePath %>static/js/orderstream/saleorder/pagination.js?rnd=${resourceVersionKey}'></script>--%>

<body>
    <div class="body-terminal">
        <div class="header-terminal">
            <table class="table table-bordered table-striped table-condensed table-centered"
                   style="width: 60%; float: left; margin-left: 30px; margin-top: 5px;">
                <thead>
                    <tr>
                        <th>客户名称</th>
                        <th>地区</th>
                        <th>归属销售</th>
                        <th>选择</th>
                    </tr>
                </thead>
                <tbody>
                    <c:if test="${not empty terminalList}">
                        <c:forEach items="${terminalList}" var="list" varStatus="status">
                            <tr>
                                <td>${list.traderName}</td>
                                <td>${list.address}</td>
                                <td>${list.personal}</td>
                                <td style="text-align:center">
                                    <a href="javaScript:void(0)"
                                       onclick="selectTerminal('${list.areaId}','${list.address}','${list.traderId}','${list.traderName}','${list.customerType}','${list.customerTypeStr}','${list.areaIds}')">选择</a>
                                </td>
                            </tr>
                        </c:forEach>
                    </c:if>
                    <c:if test="${empty terminalList}">
                        <tr>
                            <td colspan="4">
                                ${list.traderName}查询无结果！请尝试使用其他关键词搜索。
                            </td>
                        </tr>
                    </c:if>
                </tbody>
            </table>
        </div>
        <div class="footer-terminal" style="clear: both">
            <div id="pages">
                <tags:page page="${page}" />
            </div>
        </div>
    </div>
    <input id="terminalPageIndex" hidden value="${page ne null?page.pageNo:1}">
    <input id="terminalPageSize" hidden value="${page ne null?page.pageSize:0}">
    <input id="terminalPageCount" hidden value="${page ne null?page.totalPage:0}">
    <input id="terminalTotal" hidden value="${page ne null?page.totalRecord:0}">
</body>

<script>
    /* salesAreaId销售区域ID；salesArea销售区域；terminalTraderId终端ID，terminalTraderName终端名称，terminalTraderType终端类型*/
    function selectTerminal(salesAreaId,salesArea,terminalTraderId,terminalTraderName,terminalTraderType,terminalTraderTypeStr,areaIds){
        checkLogin();

        parent.$("#updateTerminalInfo").find("#terminalTraderNameDiv").html(terminalTraderName);

        parent.$("#quotePayMoneForm").find("#terminalTraderId").val(terminalTraderId);
        parent.$("#quotePayMoneForm").find("#terminalTraderName").val(terminalTraderName);
        parent.$("#quotePayMoneForm").find("#terminalTraderType").val(terminalTraderType);
        if (areaIds){
            parent.changeArea(areaIds);
        }

        parent.$("#editForm").find("#terminalTraderId").val(terminalTraderId);
        parent.$("#editForm").find("#terminalTraderName").val(terminalTraderName);
        parent.$("#editForm").find("#terminalTraderType").val(terminalTraderType);

        parent.$("#updateTerminalInfo").find("#terminalNameDetail").show();
        parent.$("#updateTerminalInfo").find("#terminalNameCheck").hide();

        if(parent.layer!=undefined){
            parent.layer.close(index);
        }

        parent.$("#terminal-info-div").hide();

    }


</script>

