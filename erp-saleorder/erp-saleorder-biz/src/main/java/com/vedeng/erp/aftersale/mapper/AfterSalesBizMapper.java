package com.vedeng.erp.aftersale.mapper;

import com.vedeng.erp.aftersale.domain.dto.*;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesDetailEntity;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesEntity;
import com.vedeng.erp.mobile.dto.AfterSalesListResultDto;
import com.vedeng.erp.saleorder.dto.OrderFinanceInfoDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/5 16:09
 **/
public interface AfterSalesBizMapper {
    /**
     * delete by primary key
     * @param afterSalesId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer afterSalesId);


    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(AfterSalesEntity record);

    /**
     * select by primary key
     * @param afterSalesId primary key
     * @return object by primary key
     */
    AfterSalesEntity selectByPrimaryKey(Integer afterSalesId);

    AfterSalesEntity selectByAfterSalesNo(String afterSalesNo);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(AfterSalesEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(AfterSalesEntity record);


    List<AfterSalesListResultDto> getAfterSalesListByOrderIdAndSubjectType(@Param("orderIds") List<Integer> orderIds, @Param("subjectType") Integer subjectType);


    /**
     * 获取销售售后订单
     * @param saleOrderId 销售订单id
     * @return 销售售后订单
     */
    List<AfterSalesEntity> findByOrderIdGetSaleOrderAfterSale(@Param("orderId")Integer saleOrderId);

    /**
     * 获取订单实际金额
     * @param saleOrderId 订单id
     * @return SaleOrderAfterSaleAmountDto
     */
    List<SaleOrderGoodsAmountDto> getSaleOrderAmountDto(@Param("saleOrderId")Integer saleOrderId);

    /**
     * 根据关联id集合查询售后单信息
     * @param orderIds
     * @return
     */
    List<AfterSalesEntity> findUnderwayThOrTpByOrderIds(@Param("orderIds") List<Integer> orderIds);

    /**
     * 根据售后单id查询售后单详情
     * @param afterSalesId
     * @return
     */
    AfterSalesDetailDto findAfterSalesDetailByAfterSalesId(@Param("afterSalesId")Integer afterSalesId);

    /**
     * 根据售后单id更新售后单详情  明细表
     * @param record
     * @return
     */
    int updateDetailByAfterSalesId(AfterSalesDetailEntity record);

    /**
     * 根据售后单id更新售后单详情  主表
     * @param record
     * @return
     */
    int updateByAfterSalesId(AfterSalesDetailEntity record);

    OrderFinanceInfoDto getAftersaleFinanceInfo(Integer afterSalesId);

    List<AfterSalesEntity> getOngoingAfterSalesByOrderId(@Param("saleOrderId") Integer saleOrderId);

    List<AfterSalesEntity> getCompletedAfterSalesByOrderId(@Param("saleOrderId") Integer saleOrderId);

    int updateClientStatusByAfterSalesId(@Param("updatedClientStatus") Integer updatedClientStatus, @Param("afterSalesId") Integer afterSalesId);

    PushAfterSalesDto getPushInfo(@Param("afterSalesId") Integer afterSalesId);

    List<PushAfterSalesAttachmentDto> getAfterSalesAttachmentById(@Param("afterSalesId") Integer afterSalesId, @Param("afterSalesTypeId") Integer afterSalesTypeId);

    List<PushAfterSalesGoodsDto> getPushGoodsByAfterSalesId(@Param("afterSalesId") Integer afterSalesId);
}