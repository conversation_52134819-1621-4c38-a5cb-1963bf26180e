package com.vedeng.goods.model;

import java.util.ArrayList;
import java.util.List;

public class BrandGenerateExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public BrandGenerateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andBrandIdIsNull() {
            addCriterion("BRAND_ID is null");
            return (Criteria) this;
        }

        public Criteria andBrandIdIsNotNull() {
            addCriterion("BRAND_ID is not null");
            return (Criteria) this;
        }

        public Criteria andBrandIdEqualTo(Integer value) {
            addCriterion("BRAND_ID =", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdNotEqualTo(Integer value) {
            addCriterion("BRAND_ID <>", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdGreaterThan(Integer value) {
            addCriterion("BRAND_ID >", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("BRAND_ID >=", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdLessThan(Integer value) {
            addCriterion("BRAND_ID <", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdLessThanOrEqualTo(Integer value) {
            addCriterion("BRAND_ID <=", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdIn(List<Integer> values) {
            addCriterion("BRAND_ID in", values, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdNotIn(List<Integer> values) {
            addCriterion("BRAND_ID not in", values, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdBetween(Integer value1, Integer value2) {
            addCriterion("BRAND_ID between", value1, value2, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdNotBetween(Integer value1, Integer value2) {
            addCriterion("BRAND_ID not between", value1, value2, "brandId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("COMPANY_ID is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("COMPANY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Integer value) {
            addCriterion("COMPANY_ID =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Integer value) {
            addCriterion("COMPANY_ID <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Integer value) {
            addCriterion("COMPANY_ID >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("COMPANY_ID >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Integer value) {
            addCriterion("COMPANY_ID <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("COMPANY_ID <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Integer> values) {
            addCriterion("COMPANY_ID in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Integer> values) {
            addCriterion("COMPANY_ID not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("COMPANY_ID between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("COMPANY_ID not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andBrandNatureIsNull() {
            addCriterion("BRAND_NATURE is null");
            return (Criteria) this;
        }

        public Criteria andBrandNatureIsNotNull() {
            addCriterion("BRAND_NATURE is not null");
            return (Criteria) this;
        }

        public Criteria andBrandNatureEqualTo(Integer value) {
            addCriterion("BRAND_NATURE =", value, "brandNature");
            return (Criteria) this;
        }

        public Criteria andBrandNatureNotEqualTo(Integer value) {
            addCriterion("BRAND_NATURE <>", value, "brandNature");
            return (Criteria) this;
        }

        public Criteria andBrandNatureGreaterThan(Integer value) {
            addCriterion("BRAND_NATURE >", value, "brandNature");
            return (Criteria) this;
        }

        public Criteria andBrandNatureGreaterThanOrEqualTo(Integer value) {
            addCriterion("BRAND_NATURE >=", value, "brandNature");
            return (Criteria) this;
        }

        public Criteria andBrandNatureLessThan(Integer value) {
            addCriterion("BRAND_NATURE <", value, "brandNature");
            return (Criteria) this;
        }

        public Criteria andBrandNatureLessThanOrEqualTo(Integer value) {
            addCriterion("BRAND_NATURE <=", value, "brandNature");
            return (Criteria) this;
        }

        public Criteria andBrandNatureIn(List<Integer> values) {
            addCriterion("BRAND_NATURE in", values, "brandNature");
            return (Criteria) this;
        }

        public Criteria andBrandNatureNotIn(List<Integer> values) {
            addCriterion("BRAND_NATURE not in", values, "brandNature");
            return (Criteria) this;
        }

        public Criteria andBrandNatureBetween(Integer value1, Integer value2) {
            addCriterion("BRAND_NATURE between", value1, value2, "brandNature");
            return (Criteria) this;
        }

        public Criteria andBrandNatureNotBetween(Integer value1, Integer value2) {
            addCriterion("BRAND_NATURE not between", value1, value2, "brandNature");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNull() {
            addCriterion("BRAND_NAME is null");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNotNull() {
            addCriterion("BRAND_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andBrandNameEqualTo(String value) {
            addCriterion("BRAND_NAME =", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotEqualTo(String value) {
            addCriterion("BRAND_NAME <>", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThan(String value) {
            addCriterion("BRAND_NAME >", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThanOrEqualTo(String value) {
            addCriterion("BRAND_NAME >=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThan(String value) {
            addCriterion("BRAND_NAME <", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThanOrEqualTo(String value) {
            addCriterion("BRAND_NAME <=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLike(String value) {
            addCriterion("BRAND_NAME like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotLike(String value) {
            addCriterion("BRAND_NAME not like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameIn(List<String> values) {
            addCriterion("BRAND_NAME in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotIn(List<String> values) {
            addCriterion("BRAND_NAME not in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameBetween(String value1, String value2) {
            addCriterion("BRAND_NAME between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotBetween(String value1, String value2) {
            addCriterion("BRAND_NAME not between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameEnIsNull() {
            addCriterion("BRAND_NAME_EN is null");
            return (Criteria) this;
        }

        public Criteria andBrandNameEnIsNotNull() {
            addCriterion("BRAND_NAME_EN is not null");
            return (Criteria) this;
        }

        public Criteria andBrandNameEnEqualTo(String value) {
            addCriterion("BRAND_NAME_EN =", value, "brandNameEn");
            return (Criteria) this;
        }

        public Criteria andBrandNameEnNotEqualTo(String value) {
            addCriterion("BRAND_NAME_EN <>", value, "brandNameEn");
            return (Criteria) this;
        }

        public Criteria andBrandNameEnGreaterThan(String value) {
            addCriterion("BRAND_NAME_EN >", value, "brandNameEn");
            return (Criteria) this;
        }

        public Criteria andBrandNameEnGreaterThanOrEqualTo(String value) {
            addCriterion("BRAND_NAME_EN >=", value, "brandNameEn");
            return (Criteria) this;
        }

        public Criteria andBrandNameEnLessThan(String value) {
            addCriterion("BRAND_NAME_EN <", value, "brandNameEn");
            return (Criteria) this;
        }

        public Criteria andBrandNameEnLessThanOrEqualTo(String value) {
            addCriterion("BRAND_NAME_EN <=", value, "brandNameEn");
            return (Criteria) this;
        }

        public Criteria andBrandNameEnLike(String value) {
            addCriterion("BRAND_NAME_EN like", value, "brandNameEn");
            return (Criteria) this;
        }

        public Criteria andBrandNameEnNotLike(String value) {
            addCriterion("BRAND_NAME_EN not like", value, "brandNameEn");
            return (Criteria) this;
        }

        public Criteria andBrandNameEnIn(List<String> values) {
            addCriterion("BRAND_NAME_EN in", values, "brandNameEn");
            return (Criteria) this;
        }

        public Criteria andBrandNameEnNotIn(List<String> values) {
            addCriterion("BRAND_NAME_EN not in", values, "brandNameEn");
            return (Criteria) this;
        }

        public Criteria andBrandNameEnBetween(String value1, String value2) {
            addCriterion("BRAND_NAME_EN between", value1, value2, "brandNameEn");
            return (Criteria) this;
        }

        public Criteria andBrandNameEnNotBetween(String value1, String value2) {
            addCriterion("BRAND_NAME_EN not between", value1, value2, "brandNameEn");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNull() {
            addCriterion("MANUFACTURER is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIsNotNull() {
            addCriterion("MANUFACTURER is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerEqualTo(String value) {
            addCriterion("MANUFACTURER =", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotEqualTo(String value) {
            addCriterion("MANUFACTURER <>", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThan(String value) {
            addCriterion("MANUFACTURER >", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerGreaterThanOrEqualTo(String value) {
            addCriterion("MANUFACTURER >=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThan(String value) {
            addCriterion("MANUFACTURER <", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLessThanOrEqualTo(String value) {
            addCriterion("MANUFACTURER <=", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerLike(String value) {
            addCriterion("MANUFACTURER like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotLike(String value) {
            addCriterion("MANUFACTURER not like", value, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerIn(List<String> values) {
            addCriterion("MANUFACTURER in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotIn(List<String> values) {
            addCriterion("MANUFACTURER not in", values, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerBetween(String value1, String value2) {
            addCriterion("MANUFACTURER between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andManufacturerNotBetween(String value1, String value2) {
            addCriterion("MANUFACTURER not between", value1, value2, "manufacturer");
            return (Criteria) this;
        }

        public Criteria andBrandWebsiteIsNull() {
            addCriterion("BRAND_WEBSITE is null");
            return (Criteria) this;
        }

        public Criteria andBrandWebsiteIsNotNull() {
            addCriterion("BRAND_WEBSITE is not null");
            return (Criteria) this;
        }

        public Criteria andBrandWebsiteEqualTo(String value) {
            addCriterion("BRAND_WEBSITE =", value, "brandWebsite");
            return (Criteria) this;
        }

        public Criteria andBrandWebsiteNotEqualTo(String value) {
            addCriterion("BRAND_WEBSITE <>", value, "brandWebsite");
            return (Criteria) this;
        }

        public Criteria andBrandWebsiteGreaterThan(String value) {
            addCriterion("BRAND_WEBSITE >", value, "brandWebsite");
            return (Criteria) this;
        }

        public Criteria andBrandWebsiteGreaterThanOrEqualTo(String value) {
            addCriterion("BRAND_WEBSITE >=", value, "brandWebsite");
            return (Criteria) this;
        }

        public Criteria andBrandWebsiteLessThan(String value) {
            addCriterion("BRAND_WEBSITE <", value, "brandWebsite");
            return (Criteria) this;
        }

        public Criteria andBrandWebsiteLessThanOrEqualTo(String value) {
            addCriterion("BRAND_WEBSITE <=", value, "brandWebsite");
            return (Criteria) this;
        }

        public Criteria andBrandWebsiteLike(String value) {
            addCriterion("BRAND_WEBSITE like", value, "brandWebsite");
            return (Criteria) this;
        }

        public Criteria andBrandWebsiteNotLike(String value) {
            addCriterion("BRAND_WEBSITE not like", value, "brandWebsite");
            return (Criteria) this;
        }

        public Criteria andBrandWebsiteIn(List<String> values) {
            addCriterion("BRAND_WEBSITE in", values, "brandWebsite");
            return (Criteria) this;
        }

        public Criteria andBrandWebsiteNotIn(List<String> values) {
            addCriterion("BRAND_WEBSITE not in", values, "brandWebsite");
            return (Criteria) this;
        }

        public Criteria andBrandWebsiteBetween(String value1, String value2) {
            addCriterion("BRAND_WEBSITE between", value1, value2, "brandWebsite");
            return (Criteria) this;
        }

        public Criteria andBrandWebsiteNotBetween(String value1, String value2) {
            addCriterion("BRAND_WEBSITE not between", value1, value2, "brandWebsite");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNull() {
            addCriterion("`OWNER` is null");
            return (Criteria) this;
        }

        public Criteria andOwnerIsNotNull() {
            addCriterion("`OWNER` is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerEqualTo(String value) {
            addCriterion("`OWNER` =", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotEqualTo(String value) {
            addCriterion("`OWNER` <>", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThan(String value) {
            addCriterion("`OWNER` >", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerGreaterThanOrEqualTo(String value) {
            addCriterion("`OWNER` >=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThan(String value) {
            addCriterion("`OWNER` <", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLessThanOrEqualTo(String value) {
            addCriterion("`OWNER` <=", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerLike(String value) {
            addCriterion("`OWNER` like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotLike(String value) {
            addCriterion("`OWNER` not like", value, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerIn(List<String> values) {
            addCriterion("`OWNER` in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotIn(List<String> values) {
            addCriterion("`OWNER` not in", values, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerBetween(String value1, String value2) {
            addCriterion("`OWNER` between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andOwnerNotBetween(String value1, String value2) {
            addCriterion("`OWNER` not between", value1, value2, "owner");
            return (Criteria) this;
        }

        public Criteria andLogoDomainIsNull() {
            addCriterion("LOGO_DOMAIN is null");
            return (Criteria) this;
        }

        public Criteria andLogoDomainIsNotNull() {
            addCriterion("LOGO_DOMAIN is not null");
            return (Criteria) this;
        }

        public Criteria andLogoDomainEqualTo(String value) {
            addCriterion("LOGO_DOMAIN =", value, "logoDomain");
            return (Criteria) this;
        }

        public Criteria andLogoDomainNotEqualTo(String value) {
            addCriterion("LOGO_DOMAIN <>", value, "logoDomain");
            return (Criteria) this;
        }

        public Criteria andLogoDomainGreaterThan(String value) {
            addCriterion("LOGO_DOMAIN >", value, "logoDomain");
            return (Criteria) this;
        }

        public Criteria andLogoDomainGreaterThanOrEqualTo(String value) {
            addCriterion("LOGO_DOMAIN >=", value, "logoDomain");
            return (Criteria) this;
        }

        public Criteria andLogoDomainLessThan(String value) {
            addCriterion("LOGO_DOMAIN <", value, "logoDomain");
            return (Criteria) this;
        }

        public Criteria andLogoDomainLessThanOrEqualTo(String value) {
            addCriterion("LOGO_DOMAIN <=", value, "logoDomain");
            return (Criteria) this;
        }

        public Criteria andLogoDomainLike(String value) {
            addCriterion("LOGO_DOMAIN like", value, "logoDomain");
            return (Criteria) this;
        }

        public Criteria andLogoDomainNotLike(String value) {
            addCriterion("LOGO_DOMAIN not like", value, "logoDomain");
            return (Criteria) this;
        }

        public Criteria andLogoDomainIn(List<String> values) {
            addCriterion("LOGO_DOMAIN in", values, "logoDomain");
            return (Criteria) this;
        }

        public Criteria andLogoDomainNotIn(List<String> values) {
            addCriterion("LOGO_DOMAIN not in", values, "logoDomain");
            return (Criteria) this;
        }

        public Criteria andLogoDomainBetween(String value1, String value2) {
            addCriterion("LOGO_DOMAIN between", value1, value2, "logoDomain");
            return (Criteria) this;
        }

        public Criteria andLogoDomainNotBetween(String value1, String value2) {
            addCriterion("LOGO_DOMAIN not between", value1, value2, "logoDomain");
            return (Criteria) this;
        }

        public Criteria andLogoUriIsNull() {
            addCriterion("LOGO_URI is null");
            return (Criteria) this;
        }

        public Criteria andLogoUriIsNotNull() {
            addCriterion("LOGO_URI is not null");
            return (Criteria) this;
        }

        public Criteria andLogoUriEqualTo(String value) {
            addCriterion("LOGO_URI =", value, "logoUri");
            return (Criteria) this;
        }

        public Criteria andLogoUriNotEqualTo(String value) {
            addCriterion("LOGO_URI <>", value, "logoUri");
            return (Criteria) this;
        }

        public Criteria andLogoUriGreaterThan(String value) {
            addCriterion("LOGO_URI >", value, "logoUri");
            return (Criteria) this;
        }

        public Criteria andLogoUriGreaterThanOrEqualTo(String value) {
            addCriterion("LOGO_URI >=", value, "logoUri");
            return (Criteria) this;
        }

        public Criteria andLogoUriLessThan(String value) {
            addCriterion("LOGO_URI <", value, "logoUri");
            return (Criteria) this;
        }

        public Criteria andLogoUriLessThanOrEqualTo(String value) {
            addCriterion("LOGO_URI <=", value, "logoUri");
            return (Criteria) this;
        }

        public Criteria andLogoUriLike(String value) {
            addCriterion("LOGO_URI like", value, "logoUri");
            return (Criteria) this;
        }

        public Criteria andLogoUriNotLike(String value) {
            addCriterion("LOGO_URI not like", value, "logoUri");
            return (Criteria) this;
        }

        public Criteria andLogoUriIn(List<String> values) {
            addCriterion("LOGO_URI in", values, "logoUri");
            return (Criteria) this;
        }

        public Criteria andLogoUriNotIn(List<String> values) {
            addCriterion("LOGO_URI not in", values, "logoUri");
            return (Criteria) this;
        }

        public Criteria andLogoUriBetween(String value1, String value2) {
            addCriterion("LOGO_URI between", value1, value2, "logoUri");
            return (Criteria) this;
        }

        public Criteria andLogoUriNotBetween(String value1, String value2) {
            addCriterion("LOGO_URI not between", value1, value2, "logoUri");
            return (Criteria) this;
        }

        public Criteria andSortIsNull() {
            addCriterion("SORT is null");
            return (Criteria) this;
        }

        public Criteria andSortIsNotNull() {
            addCriterion("SORT is not null");
            return (Criteria) this;
        }

        public Criteria andSortEqualTo(Integer value) {
            addCriterion("SORT =", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotEqualTo(Integer value) {
            addCriterion("SORT <>", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThan(Integer value) {
            addCriterion("SORT >", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortGreaterThanOrEqualTo(Integer value) {
            addCriterion("SORT >=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThan(Integer value) {
            addCriterion("SORT <", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortLessThanOrEqualTo(Integer value) {
            addCriterion("SORT <=", value, "sort");
            return (Criteria) this;
        }

        public Criteria andSortIn(List<Integer> values) {
            addCriterion("SORT in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotIn(List<Integer> values) {
            addCriterion("SORT not in", values, "sort");
            return (Criteria) this;
        }

        public Criteria andSortBetween(Integer value1, Integer value2) {
            addCriterion("SORT between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andSortNotBetween(Integer value1, Integer value2) {
            addCriterion("SORT not between", value1, value2, "sort");
            return (Criteria) this;
        }

        public Criteria andInitialCnIsNull() {
            addCriterion("INITIAL_CN is null");
            return (Criteria) this;
        }

        public Criteria andInitialCnIsNotNull() {
            addCriterion("INITIAL_CN is not null");
            return (Criteria) this;
        }

        public Criteria andInitialCnEqualTo(String value) {
            addCriterion("INITIAL_CN =", value, "initialCn");
            return (Criteria) this;
        }

        public Criteria andInitialCnNotEqualTo(String value) {
            addCriterion("INITIAL_CN <>", value, "initialCn");
            return (Criteria) this;
        }

        public Criteria andInitialCnGreaterThan(String value) {
            addCriterion("INITIAL_CN >", value, "initialCn");
            return (Criteria) this;
        }

        public Criteria andInitialCnGreaterThanOrEqualTo(String value) {
            addCriterion("INITIAL_CN >=", value, "initialCn");
            return (Criteria) this;
        }

        public Criteria andInitialCnLessThan(String value) {
            addCriterion("INITIAL_CN <", value, "initialCn");
            return (Criteria) this;
        }

        public Criteria andInitialCnLessThanOrEqualTo(String value) {
            addCriterion("INITIAL_CN <=", value, "initialCn");
            return (Criteria) this;
        }

        public Criteria andInitialCnLike(String value) {
            addCriterion("INITIAL_CN like", value, "initialCn");
            return (Criteria) this;
        }

        public Criteria andInitialCnNotLike(String value) {
            addCriterion("INITIAL_CN not like", value, "initialCn");
            return (Criteria) this;
        }

        public Criteria andInitialCnIn(List<String> values) {
            addCriterion("INITIAL_CN in", values, "initialCn");
            return (Criteria) this;
        }

        public Criteria andInitialCnNotIn(List<String> values) {
            addCriterion("INITIAL_CN not in", values, "initialCn");
            return (Criteria) this;
        }

        public Criteria andInitialCnBetween(String value1, String value2) {
            addCriterion("INITIAL_CN between", value1, value2, "initialCn");
            return (Criteria) this;
        }

        public Criteria andInitialCnNotBetween(String value1, String value2) {
            addCriterion("INITIAL_CN not between", value1, value2, "initialCn");
            return (Criteria) this;
        }

        public Criteria andInitialEnIsNull() {
            addCriterion("INITIAL_EN is null");
            return (Criteria) this;
        }

        public Criteria andInitialEnIsNotNull() {
            addCriterion("INITIAL_EN is not null");
            return (Criteria) this;
        }

        public Criteria andInitialEnEqualTo(String value) {
            addCriterion("INITIAL_EN =", value, "initialEn");
            return (Criteria) this;
        }

        public Criteria andInitialEnNotEqualTo(String value) {
            addCriterion("INITIAL_EN <>", value, "initialEn");
            return (Criteria) this;
        }

        public Criteria andInitialEnGreaterThan(String value) {
            addCriterion("INITIAL_EN >", value, "initialEn");
            return (Criteria) this;
        }

        public Criteria andInitialEnGreaterThanOrEqualTo(String value) {
            addCriterion("INITIAL_EN >=", value, "initialEn");
            return (Criteria) this;
        }

        public Criteria andInitialEnLessThan(String value) {
            addCriterion("INITIAL_EN <", value, "initialEn");
            return (Criteria) this;
        }

        public Criteria andInitialEnLessThanOrEqualTo(String value) {
            addCriterion("INITIAL_EN <=", value, "initialEn");
            return (Criteria) this;
        }

        public Criteria andInitialEnLike(String value) {
            addCriterion("INITIAL_EN like", value, "initialEn");
            return (Criteria) this;
        }

        public Criteria andInitialEnNotLike(String value) {
            addCriterion("INITIAL_EN not like", value, "initialEn");
            return (Criteria) this;
        }

        public Criteria andInitialEnIn(List<String> values) {
            addCriterion("INITIAL_EN in", values, "initialEn");
            return (Criteria) this;
        }

        public Criteria andInitialEnNotIn(List<String> values) {
            addCriterion("INITIAL_EN not in", values, "initialEn");
            return (Criteria) this;
        }

        public Criteria andInitialEnBetween(String value1, String value2) {
            addCriterion("INITIAL_EN between", value1, value2, "initialEn");
            return (Criteria) this;
        }

        public Criteria andInitialEnNotBetween(String value1, String value2) {
            addCriterion("INITIAL_EN not between", value1, value2, "initialEn");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("`SOURCE` is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("`SOURCE` is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(Integer value) {
            addCriterion("`SOURCE` =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(Integer value) {
            addCriterion("`SOURCE` <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(Integer value) {
            addCriterion("`SOURCE` >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("`SOURCE` >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(Integer value) {
            addCriterion("`SOURCE` <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(Integer value) {
            addCriterion("`SOURCE` <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<Integer> values) {
            addCriterion("`SOURCE` in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<Integer> values) {
            addCriterion("`SOURCE` not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(Integer value1, Integer value2) {
            addCriterion("`SOURCE` between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("`SOURCE` not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Long value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Long value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Long value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Long value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Long> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Long> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Long value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Long value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Long value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Long value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Long> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Long> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("IS_DELETE is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("IS_DELETE is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Integer value) {
            addCriterion("IS_DELETE =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Integer value) {
            addCriterion("IS_DELETE <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Integer value) {
            addCriterion("IS_DELETE >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Integer value) {
            addCriterion("IS_DELETE >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Integer value) {
            addCriterion("IS_DELETE <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Integer value) {
            addCriterion("IS_DELETE <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Integer> values) {
            addCriterion("IS_DELETE in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Integer> values) {
            addCriterion("IS_DELETE not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Integer value1, Integer value2) {
            addCriterion("IS_DELETE between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Integer value1, Integer value2) {
            addCriterion("IS_DELETE not between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andCommentsIsNull() {
            addCriterion("COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andCommentsIsNotNull() {
            addCriterion("COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andCommentsEqualTo(String value) {
            addCriterion("COMMENTS =", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotEqualTo(String value) {
            addCriterion("COMMENTS <>", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsGreaterThan(String value) {
            addCriterion("COMMENTS >", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("COMMENTS >=", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsLessThan(String value) {
            addCriterion("COMMENTS <", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsLessThanOrEqualTo(String value) {
            addCriterion("COMMENTS <=", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsLike(String value) {
            addCriterion("COMMENTS like", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotLike(String value) {
            addCriterion("COMMENTS not like", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsIn(List<String> values) {
            addCriterion("COMMENTS in", values, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotIn(List<String> values) {
            addCriterion("COMMENTS not in", values, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsBetween(String value1, String value2) {
            addCriterion("COMMENTS between", value1, value2, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotBetween(String value1, String value2) {
            addCriterion("COMMENTS not between", value1, value2, "comments");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_BRAND
     *
     * @mbggenerated do_not_delete_during_merge Thu Jun 27 19:06:50 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_BRAND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}