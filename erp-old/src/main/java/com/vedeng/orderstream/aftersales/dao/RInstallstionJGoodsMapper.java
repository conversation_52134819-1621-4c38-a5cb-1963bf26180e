package com.vedeng.orderstream.aftersales.dao;

import com.vedeng.orderstream.aftersales.model.RInstallstionJGoods;
import com.vedeng.orderstream.aftersales.model.RInstallstionJGoodsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

@Named("rInstallstionJGoodsMapper")
public interface RInstallstionJGoodsMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    int countByExample(RInstallstionJGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    int deleteByExample(RInstallstionJGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    int deleteByPrimaryKey(Integer rInstallstionJGoodsId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    int insert(RInstallstionJGoods record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    int insertSelective(RInstallstionJGoods record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    List<RInstallstionJGoods> selectByExample(RInstallstionJGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    RInstallstionJGoods selectByPrimaryKey(Integer rInstallstionJGoodsId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    int updateByExampleSelective(@Param("record") RInstallstionJGoods record, @Param("example") RInstallstionJGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    int updateByExample(@Param("record") RInstallstionJGoods record, @Param("example") RInstallstionJGoodsExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    int updateByPrimaryKeySelective(RInstallstionJGoods record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_R_INSTALLSTION_J_GOODS
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    int updateByPrimaryKey(RInstallstionJGoods record);


    int delRInstallstionJGoods(RInstallstionJGoods rin);

    /**
     *  <b>Description:</b><br>  根据条件查询安调与产品的关系列表
     * @param rInstallstionJGoods
     * @return
     */
    List<com.vedeng.aftersales.model.RInstallstionJGoods> getRInstallstionJGoodsList(com.vedeng.aftersales.model.RInstallstionJGoods rInstallstionJGoods);
}