package com.vedeng.crm.business.quote.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.crm.business.quote.domain.dto.QuoteGoodsRequestDto;
import com.vedeng.crm.business.quote.domain.dto.QuoteGoodsUpdateRequestDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface QuoteGoodsUpdateConvertor extends BaseMapStruct<QuoteGoodsRequestDto, QuoteGoodsUpdateRequestDto> {

}
