package com.vedeng.finance.model.vo;

import com.vedeng.finance.model.PayApplyDetail;

import java.math.BigDecimal;

/**
 * <AUTHOR> simgo.wang
 * @date : 2021/11/9 10:14
 * @desc :
 */
public class PayApplyDetailVo extends PayApplyDetail {

    /**
     * @Fields serialVersionUID : TODO
     */
    private static final long serialVersionUID = 1L;

    private BigDecimal totalAmountSum;//申请付款的已申请总额

    private BigDecimal applicationSum;//申请付款的已申请数量

    private Integer payType;//付款类型

    private Integer relatedId;

    public Integer getRelatedId() {
        return relatedId;
    }

    public void setRelatedId(Integer relatedId) {
        this.relatedId = relatedId;
    }

    public BigDecimal getTotalAmountSum() {
        return totalAmountSum;
    }

    public void setTotalAmountSum(BigDecimal totalAmountSum) {
        this.totalAmountSum = totalAmountSum;
    }

    public BigDecimal getApplicationSum() {
        return applicationSum;
    }

    public void setApplicationSum(BigDecimal applicationSum) {
        this.applicationSum = applicationSum;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

}