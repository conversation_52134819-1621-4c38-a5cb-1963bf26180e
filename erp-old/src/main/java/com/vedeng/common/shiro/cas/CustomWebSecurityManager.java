package com.vedeng.common.shiro.cas;

import com.ezadmin.common.utils.IpUtils;
import com.vedeng.common.shiro.cas.service.UacAccountFacadeService;

import com.vedeng.uac.api.dto.AccountSignOnLogDto;
import io.buji.pac4j.subject.Pac4jPrincipal;
import io.buji.pac4j.token.Pac4jToken;
import org.apache.batik.bridge.UserAgent;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.subject.support.WebDelegatingSubject;
import org.apache.shiro.web.util.WebUtils;
import org.pac4j.core.profile.CommonProfile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class CustomWebSecurityManager extends DefaultWebSecurityManager {

    private final static Logger LOGGER = LoggerFactory.getLogger(CustomWebSecurityManager.class);

    private static final int ONE_YEAR_IN_SECOND = (int) TimeUnit.DAYS.toSeconds(365);

    @Value("${cas.client.platformNo}")
    private Integer platformNo;

    @Autowired
    private UacAccountFacadeService uacAccountFacadeService;

    @Override
    protected void onFailedLogin(AuthenticationToken token, AuthenticationException ae, Subject subject) {
        super.onFailedLogin(token, ae, subject);

        try {
            subject.logout();
        } catch (Exception e) {
            //ignore
        }
    }

    @Override
    protected void onSuccessfulLogin(AuthenticationToken token, AuthenticationInfo info, Subject subject) {
        super.onSuccessfulLogin(token, info, subject);

        // todo 临时兼容方案发布sid cookie
        if (subject instanceof WebDelegatingSubject) {
            final Pac4jToken pac4jToken = (Pac4jToken) token;
            CommonProfile currentProfile = pac4jToken.getProfiles().values().iterator().next();
            try {
                issueCrossingEzCookieIfNecessary(currentProfile, (WebDelegatingSubject) subject);
            } catch (Exception e) {
                LOGGER.error("登录成功后发布cookie失败 - cookieName:{}, profile:{}",  currentProfile);
            }
        }

        // uac登录日志存储
        try {
            insertAccountSignOnLog(subject);
        } catch (Exception e) {
            LOGGER.error("UAC登录日志存储失败", e);
        }

    }


    private void issueCrossingEzCookieIfNecessary(CommonProfile profile, WebDelegatingSubject webDelegatingSubject) {
        if (profile == null) {
            return;
        }

        HttpServletRequest httpServletRequest = WebUtils.toHttp(webDelegatingSubject.getServletRequest());
        HttpServletResponse httpServletResponse = WebUtils.toHttp(webDelegatingSubject.getServletResponse());

        String userId = profile.getAttribute("userId", String.class);
        String username = profile.getUsername();
        String usernameForCookie = null;
        if(containsChinese(username)){
            usernameForCookie = "user-"+userId;
        }else{
            usernameForCookie = profile.getUsername();
        }
        addCookie("lastLoginName", usernameForCookie, ONE_YEAR_IN_SECOND,httpServletRequest, httpServletResponse);

    }

    public static boolean containsChinese(String str) {
        // 使用正则表达式匹配中文字符
        return str.matches(".*[\u4e00-\u9fa5].*");
    }


    // UAC记录登录日志
    private void insertAccountSignOnLog(Subject subject) {
        AccountSignOnLogDto accountSignOnLogDto = new AccountSignOnLogDto();
        HttpServletRequest httpServletRequest = WebUtils.toHttp(((WebDelegatingSubject) subject).getServletRequest());
        String ip = IpUtils.getRealIp(httpServletRequest);
        accountSignOnLogDto.setIp(ip);
        // 获取用户账号
        HttpSession session = httpServletRequest.getSession();
        String accountName = subject.getPrincipals().oneByType(Pac4jPrincipal.class).getProfile().getUsername();
        accountSignOnLogDto.setAccountName(accountName);
        // 获取userAgent
        String userAgent = httpServletRequest.getHeader("user-agent");
        accountSignOnLogDto.setUserAgent(userAgent);
        accountSignOnLogDto.setIsError(0);
        accountSignOnLogDto.setCreatedTime(new Date());
        accountSignOnLogDto.setPlatformNo(platformNo);
        accountSignOnLogDto.setRemark("ERP登录日志");
        uacAccountFacadeService.insertAccountSignOnLog(accountSignOnLogDto);

        LOGGER.info("ERP登录日志成功日志 - username: {}, ip: {}, agent: {}" ,accountName, ip, userAgent);
    }

    private void addCookie(String key, String value, Integer expire,HttpServletRequest httpServletRequest,HttpServletResponse httpServletResponse) {
        Cookie cookie = new Cookie(key, value);
        String domain="ivedeng.com";
        if (!httpServletRequest.getServerName().contains("ivedeng.com")) {
            domain = httpServletRequest.getServerName();
        }
        cookie.setDomain(domain);
        cookie.setPath("/");
        cookie.setMaxAge(expire);
        httpServletResponse.addCookie(cookie);
    }
}
