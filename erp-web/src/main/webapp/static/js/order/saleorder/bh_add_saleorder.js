$(function(){
	$("#prepareReaseonType").change(function(){
		if($(this).val() == "4264"){
			$("#prepareCommentsDiv").show();
		}else {
			$("#prepareCommentsDiv").hide();
		}
	})

	$("#prepareReaseonType").trigger('change');
});


function checkAddBhSaleorderForm(){
	checkLogin();
	clearErroeMes();//清除錯誤提示信息

	let reaseonType = $("#prepareReaseonType").val();
	if(reaseonType == "-1"){
		warnTips("prepareCommentsErr","请选择申请原因");
		return false;
	}

	if(reaseonType == "4264"){
		var prepareComments = $("#prepareComments").val();
		if(prepareComments.trim() === ''){
			warnTips("prepareCommentsErr","请填写申请原因");
			return false;
		}
		if(prepareComments.length > 256 ){
			warnTips("prepareCommentsErr","申请原因不允许超过256个字");
			return false;
		}
	}


	var marketingPlan = $("#marketingPlan").val();
	if(marketingPlan!="" && marketingPlan.length>256){
		warnTips("marketingPlan","后期营销计划长度应该在0-256个字符之间");
		return false;
	}

	return true;
}

function updateReaseon(obj){
	checkLogin();
	let val = $(obj).val();
	if(val=="4264"){
		$("#prepareCommentsDiv").show();
	}else{
		$("#prepareCommentsDiv").hide();
	}
	warnTips("prepareCommentsErr","");
}

