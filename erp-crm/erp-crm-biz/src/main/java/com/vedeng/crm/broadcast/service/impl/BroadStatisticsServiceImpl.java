package com.vedeng.crm.broadcast.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.vedeng.common.core.base.R;
import com.vedeng.crm.broadcast.service.BroadStatisticsService;
import com.vedeng.crm.feign.order.BusinessToOrderApiService;
import com.vedeng.erp.broadcast.StatisticDataDto;

/**
 * 到款排行榜服务
 * @ClassName:  BroadStatisticsServiceImpl   
 * @author: <PERSON><PERSON>yang
 * @date:   2025年7月21日 下午2:48:34    
 * @Copyright:
 */
@Service
public class BroadStatisticsServiceImpl implements BroadStatisticsService {
	
	@Autowired
	private BusinessToOrderApiService businessToOrderApiService;

	public StatisticDataDto getBraodCastStatisticData() {
		R<StatisticDataDto>  statisticDataDtoResult = businessToOrderApiService.getBraodCastStatisticData();
		if(statisticDataDtoResult.getSuccess()) {
			return statisticDataDtoResult.getData();
		}
		return null;
	}
}
