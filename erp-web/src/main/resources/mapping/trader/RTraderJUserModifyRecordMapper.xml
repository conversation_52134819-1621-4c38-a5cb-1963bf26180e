<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.RTraderJUserModifyRecordMapper" >
    <insert id="insert" parameterType="com.vedeng.trader.model.RTraderJUserModifyRecord" >
        insert into T_R_TRADER_J_USER_MODIFY_RECORD
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="traderType != null" >
                TRADER_TYPE,
            </if>
            <if test="userId != null" >
                USER_ID,
            </if>
            <if test="oldUserId != null" >
                OLD_USER_ID,
            </if>
            <if test="traderId != null" >
                TRADER_ID,
            </if>
            <if test="creator != null" >
                CREATOR,
            </if>
            <if test="startTime != null" >
                START_TIME,
            </if>
            <if test="endTime != null" >
                END_TIME,
            </if>
            <if test="isDelete != null" >
                IS_DELETED,
            </if>
            <if test="updater != null" >
                UPDATER,
            </if>
            <if test="modTime != null" >
                MOD_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="traderType != null" >
                #{traderType,jdbcType=BIT},
            </if>
            <if test="userId != null" >
                #{userId,jdbcType=INTEGER},
            </if>
            <if test="oldUserId != null" >
                #{oldUserId,jdbcType=INTEGER},
            </if>
            <if test="traderId != null" >
                #{traderId,jdbcType=INTEGER},
            </if>
            <if test="creator != null" >
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="startTime != null" >
                #{startTime,jdbcType=BIGINT},
            </if>
            <if test="endTime != null" >
                #{endTime,jdbcType=BIGINT},
            </if>
            <if test="isDelete != null" >
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="updater != null" >
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="modTime != null" >
                #{modTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <insert id="saveRTraderJUserModifyRecordBatch">
        insert into T_R_TRADER_J_USER_MODIFY_RECORD
        (
            TRADER_TYPE,
            USER_ID,
            OLD_USER_ID,
            TRADER_ID,
            CREATOR,
            START_TIME
        )
        VALUES
        <foreach collection ="list" item="rTraderJUserModifyRecord" index="index" separator =",">
            (
                #{rTraderJUserModifyRecord.traderType,jdbcType=INTEGER},
                #{rTraderJUserModifyRecord.userId,jdbcType=INTEGER},
                #{rTraderJUserModifyRecord.oldUserId,jdbcType=INTEGER},
                #{rTraderJUserModifyRecord.traderId,jdbcType=INTEGER},
                #{rTraderJUserModifyRecord.creator,jdbcType=INTEGER},
                #{rTraderJUserModifyRecord.startTime,jdbcType=BIGINT}
            )
        </foreach >
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.trader.model.RTraderJUserModifyRecord">
        update T_R_TRADER_J_USER_MODIFY_RECORD
        <set>
            <if test="traderType != null" >
                TRADER_TYPE = #{traderType,jdbcType=BIT},
            </if>
            <if test="userId != null" >
                USER_ID = #{userId,jdbcType=INTEGER},
            </if>
            <if test="oldUserId != null" >
                OLD_USER_ID = #{oldUserId,jdbcType=INTEGER},
            </if>
            <if test="traderId != null" >
                TRADER_ID = #{traderId,jdbcType=INTEGER},
            </if>
            <if test="creator != null" >
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="startTime != null" >
                START_TIME = #{startTime,jdbcType=BIGINT},
            </if>
            <if test="endTime != null" >
                END_TIME = #{endTime,jdbcType=BIGINT},
            </if>
            <if test="isDelete != null" >
                IS_DELETED = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="updater != null" >
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="modTime != null" >
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
        </set>
        where TRADER_OWNER_MODIFY_RECORD_ID = #{traderOwnerModifyRecordId,jdbcType=BIGINT}
    </update>

    <update id="updateEndTimeByTraderId">
        update T_R_TRADER_J_USER_MODIFY_RECORD
        <set>
            END_TIME = #{endTime,jdbcType=BIGINT},
            <if test="updater != null" >
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            MOD_TIME = #{endTime,jdbcType=BIGINT},
        </set>
        where TRADER_ID = #{traderId,jdbcType=BIGINT} AND END_TIME = 4102329600000 <!-- 过期日期为2099年，即目前生效的数据 -->
    </update>

    <update id="updateEndTimeByUserId">
        update T_R_TRADER_J_USER_MODIFY_RECORD
        <set>
            END_TIME = #{endTime,jdbcType=BIGINT},
            <if test="updater != null" >
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            MOD_TIME = #{endTime,jdbcType=BIGINT},
        </set>
        where END_TIME = 4102329600000 <!-- 过期日期为2099年，即目前生效的数据 -->
            AND TRADER_OWNER_MODIFY_RECORD_ID IN
            <foreach collection ="list" item="rTraderJUserModifyRecord" index="index" separator =","  open="(" close=")">
                    #{rTraderJUserModifyRecord.traderOwnerModifyRecordId,jdbcType=INTEGER}
            </foreach >
    </update>

    <select id="getRTraderJUserModifyRecordByUserId"
            resultType="com.vedeng.trader.model.RTraderJUserModifyRecord">
        SELECT
            *
        FROM T_R_TRADER_J_USER_MODIFY_RECORD
        WHERE USER_ID = #{userId,jdbcType=INTEGER}
            <if test="traderType != null" >
                AND TRADER_TYPE =#{traderType,jdbcType=INTEGER}
            </if>
            AND END_TIME = 4102329600000 <!-- 过期日期为2099年，即目前生效的数据 -->
    </select>
</mapper>
