package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.domain.entity.TraderCustomerTagChangeRecordEntity;
import com.vedeng.erp.trader.dto.TraderCustomerMarketingPrincipalDto;
import com.vedeng.erp.trader.dto.TraderCustomerMarketingTerminalDto;
import com.vedeng.erp.trader.dto.TraderCustomerTagChangeRecordDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 修改记录接口
 * @date 2023/8/17 15:42
 **/
public interface TraderCustomerTagChangeRecordService {

    /**
     * 计算经营模块不同
     * @param saveOrUpdate 
     * @param oldData
     * @param newData
     */
    void calcTraderCustomerMarketingPrincipalDiffAndSave(Integer saveOrUpdate, TraderCustomerMarketingPrincipalDto oldData, TraderCustomerMarketingPrincipalDto newData);


    /**
     * 计算终端标签不同
     * @param save 
     * @param oldData
     * @param newData
     * @param source 0 用户 1 系统
     */
    void calcTraderCustomerMarketingTerminalDiffAndSave(Integer save, List<TraderCustomerMarketingTerminalDto> oldData, List<TraderCustomerMarketingTerminalDto> newData,Integer source);


    /**
     * 查询客户改动记录
     * @param traderCustomerId 客户id
     * @return List<List<TraderCustomerTagChangeRecordDto>>
     */
    List<List<TraderCustomerTagChangeRecordDto>>  queryByTraderCustomerId(Integer traderCustomerId);


    /**
     * 批量插入数据
     */
    void batchInsertTraderCustomerTagChangeRecordEntity(List<TraderCustomerTagChangeRecordDto> records);
}
