<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>合同回传审核待处理</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="contractreturnreview" datasource="erp-datasourcetarget" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div>
  <div class="layui-fluid">
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
    </ul>
   </div>
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">送达时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="" style="" alias="FROM_UNIXTIME(round(ta.latest_add_time / 1000,0),'%Y-%m-%d')" jdbctype="DATE" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class="layui-inline ">
     <label>归属部门</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="xm-select" name="ORG_ID" placeholder="" style="" alias="O" jdbctype="NUMBER" data="
SELECT
	O.ORG_ID K,
	O.ORG_NAME V
FROM
	T_SALEORDER S
JOIN T_SALEORDER_DATA L ON
	L.SALEORDER_ID  = S.SALEORDER_ID
	AND S.COMPANY_ID = 1
	AND L.CONTRACT_VERIFY_STATUS = 0
JOIN T_TRADER T ON
	S.TRADER_ID = T.TRADER_ID
JOIN T_R_TRADER_J_USER TU ON
	T.TRADER_ID = TU.TRADER_ID
JOIN T_USER U ON
	TU.USER_ID = U.USER_ID
JOIN T_R_USER_POSIT UP ON
	U.USER_ID = UP.USER_ID
JOIN T_POSITION P ON
	UP.POSITION_ID = P.POSITION_ID
JOIN T_ORGANIZATION O ON
	P.ORG_ID = O.ORG_ID
WHERE 1=1
	AND S.`STATUS` != 4
GROUP BY O.ORG_ID " datatype="KVSQLCACHE" oper="IN"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">客户名称</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="TRADER_NAME" placeholder style="" alias="T" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" validate_rules="{&quot;required&quot;: false, &quot;maxlength&quot;: 50}" validate_messages="{&quot;required&quot;: false, &quot;maxlength&quot;: &quot;长度不能超过50个字符&quot;}">
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">订单号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="SALEORDER_NO" placeholder="" style="" alias="S" jdbctype="" data="" datatype="" oper="LIKE"></object>
     </div>
    </div>
   <div class=" layui-inline "><label class="layui-form-label">订单是否生效</label>
       <div class="layui-input-inline">
           <object class=" layui-input list-search-item " type="21" name="VALID_STATUS" placeholder="默认全部" style="text-overflow: clip ;" alias="S" jdbctype="" data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></object>
       </div>
   </div>
   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton">
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="SALEORDER_NO" name="SALEORDER_NO" body="td-link-color" order="0" url="/order/saleorder/view.do?saleorderId=${SALEORDER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">订单号</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link-color" order="0" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">客户名称</th>
      <th item_name="USERNAME" name="USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">归属销售</th>
      <th item_name="VALID_STATUS" name="VALID_STATUS" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">订单是否生效</th>
      <th item_name="ORG_NAME" name="ORG_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">归属部门</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">送达时间</th>
      <th item_name="OPERATION" name="OPERATION" body="td-link-color" order="0" url="/order/saleorder/view.do?saleorderId=${SALEORDER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">操作</th>
     </tr>
    </thead>
    <tbody>
     <tr>
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="ORDER BY 	ta.latest_add_time ASC" groupby="GROUP BY  S.SALEORDER_ID">SELECT
	S.SALEORDER_ID,
	S.SALEORDER_NO,
	T.TRADER_ID,
	T.TRADER_NAME,
	CASE
		S.CUSTOMER_NATURE
		WHEN 465 THEN
		'分销'
		WHEN 466 THEN
		'终端'
	END CUSTOMER_NATURE,
    CASE
		S.VALID_STATUS
		WHEN 1 THEN
		'是'
		WHEN 0 THEN
		'否'
	END VALID_STATUS,
	U.USERNAME,
	U.USER_ID,
	O.ORG_ID,
	O.ORG_NAME,
    FROM_UNIXTIME(round(ta.latest_add_time / 1000,0),'%Y-%m-%d %H:%i:%s') as ADD_TIME,
	CASE
		S.ORDER_TYPE WHEN 5 THEN '/order/hc/hcOrderDetailsPage.do?saleorderId='
		ELSE '/order/saleorder/view.do?saleorderId='
	END URI,
	'去审核' OPERATION
FROM
	T_SALEORDER S
JOIN T_SALEORDER_DATA L ON
	L.SALEORDER_ID  = S.SALEORDER_ID
	AND S.COMPANY_ID = 1
	AND L.CONTRACT_VERIFY_STATUS = 0
JOIN
(
SELECT
  RELATED_ID,
  MAX(ADD_TIME) AS latest_add_time
FROM
  T_ATTACHMENT
WHERE
  ATTACHMENT_FUNCTION = 492
  AND IS_DELETED = 0
  AND ADD_TIME > 1640966400000
GROUP BY
  RELATED_ID
HAVING
  latest_add_time = MAX(ADD_TIME)
) ta ON
	ta.RELATED_ID = S.SALEORDER_ID
JOIN T_TRADER T ON
	S.TRADER_ID = T.TRADER_ID
JOIN T_R_TRADER_J_USER TU ON
	T.TRADER_ID = TU.TRADER_ID
JOIN T_USER U ON
	TU.USER_ID = U.USER_ID
JOIN T_R_USER_POSIT UP ON
	U.USER_ID = UP.USER_ID
JOIN T_POSITION P ON
	UP.POSITION_ID = P.POSITION_ID
JOIN T_ORGANIZATION O ON
	P.ORG_ID = O.ORG_ID
WHERE 1=1
	AND S.`STATUS` != 4
    AND ta.latest_add_time > 1640966400000

      </pre> count: <pre id="count" class="layui-code"></pre></td>
     </tr>
    </tbody>
   </table>
  </div>
  <div id="appendFoot"></div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
 </body>
</html>
