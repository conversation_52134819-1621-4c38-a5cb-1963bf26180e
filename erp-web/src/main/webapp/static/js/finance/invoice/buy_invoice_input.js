
var invoiceType = 0.00;
function changeInvoiceType(obj){
	var val = $(obj).find("option:selected").val();
	$("#buyorderInfo").find("input[type='checkbox'][name='selectInvoiceName']").each(function(){
		if($(this).is(":checked")){
//			if(val!=$(this).val()){
				$(this).prop("checked",false);
//			}
		}
	});
	invoiceType = $(obj).find("option:selected").attr("id");
	$("#invoiceForm").find("#ratio").val(invoiceType);
	$("#buyorderInfo").find("input[name='update_num_pice']").click();
	
	$("#selectNum").html(0.00);
	$("#selectPrice").html(0.00);
	$("#invoiceForm").find("#amount").val(0.00);
	$(".J-amount").html(0.00);
	checkAmountLeft();
}

function updateInvoice(price,arrivalNum,inNum,buyNum,goodsNum){
	invoiceType = Number($("#taxRateFlag").val()).toFixed(2)*1;
//	$("#buyorderInfo").find("#invoice_num"+buyNum+"_"+goodsNum).val((arrivalNum - inNum).toFixed(2));
//	$("#buyorderInfo").find("#invoice_totle_amount"+buyNum+"_"+goodsNum).val(((arrivalNum - inNum)*Number(price)).toFixed(2));
	
	var num = $("#buyorderInfo").find("#invoice_num"+buyNum+"_"+goodsNum).val();
	var totalPrice = $("#buyorderInfo").find("#invoice_totle_amount"+buyNum+"_"+goodsNum).val();
	var str = $("#taxRateFlag").val();
	if(str == 429 || str == 682 || str == 684 || str == 686 || str == 688 || str == 972){
//		$("#buyorderInfo").find("#invoice_price"+buyNum+"_"+goodsNum).html((totalPrice/num/(1+invoiceType)).toFixed(8));
		$("#buyorderInfo").find("#invoice_price"+buyNum+"_"+goodsNum).html((totalPrice/num).toFixed(8));
		$("#buyorderInfo").find("#invoice_no_tax"+buyNum+"_"+goodsNum).html((totalPrice/(1+invoiceType)).toFixed(8));
		$("#buyorderInfo").find("#invoice_tax"+buyNum+"_"+goodsNum).html( (totalPrice -(totalPrice/(1+invoiceType))).toFixed(8));
	}else{
//		$("#buyorderInfo").find("#invoice_price"+buyNum+"_"+goodsNum).html((totalPrice/num/(1+invoiceType)).toFixed(8));
		$("#buyorderInfo").find("#invoice_price"+buyNum+"_"+goodsNum).html((totalPrice/num).toFixed(8));
		$("#buyorderInfo").find("#invoice_no_tax"+buyNum+"_"+goodsNum).html((totalPrice/(1+invoiceType)).toFixed(8));
		$("#buyorderInfo").find("#invoice_tax"+buyNum+"_"+goodsNum).html(0.00);
	}
}











// 单选框  参数：（this, 采购价, 本次录票数量, 本次录票总额, 已录票数量, 采购订单的index_商品的index）
function selectBuyOrder(obj,price,maxNum,maxPrice,inNum,index, buyOrderIndex){
	var id = $(obj).attr("id");
	
	// 本次录票数量 必须大于 0
	if(Number(maxNum) == 0){
		layer.alert("已入库数量小于等于已录票数量，请验证！");
		$("#"+id).prop("checked",false);
		$('#checkbox_all' + buyOrderIndex).prop('checked', false);
		return false;
	}

	var addAmountFlag = true;
	
	// 获取发票税额
	var	invoiceType = Number($("#taxRateFlag").val()).toFixed(10)*1;
	// 判断录票数量和录票总价不为0
	if(Number($(obj).val()) != 0 && Number($("#invoice_totle_amount"+index).val()) != 0){
		var num = $("#invoice_num"+index).val();
		var totalPrice = $("#invoice_totle_amount"+index).val();
		if(Number(num) > 0){
			$("#invoice_price"+index).html((totalPrice/num).toFixed(9));
			$("#invoice_no_tax"+index).html((totalPrice/(1+invoiceType)).toFixed(9));
			$("#invoice_tax"+index).html((totalPrice - (totalPrice/(1+invoiceType))).toFixed(9));
		}
		// 路票数量要大于0
		else{
			layer.alert("录票数量要大于0，请验证！");
			$("#"+id).prop("checked",false);
			$('#checkbox_all' + buyOrderIndex).prop('checked', false);
			return false;
		}
	}
	
	// 当前选择的金额
	var selectPrice = Number($("#invoice_totle_amount" + id).val());
	// 已选择金额
	var totalAmount = Number($("#amount").val());
	if($(obj).is(':checked')){
		//验证数量----------------------------------------------------------------------------------------
		var str = $("#invoiceForm").find("[name='invoiceColor']").val();
		if(str == "2-1"){//蓝字有效
			//验证数字是否合法
			var reg = /^(([1-9][0-9]*)|(([0]\.\d{1,10}|[1-9][0-9]*\.\d{1,10})))$/;
			if(!reg.test($("#invoice_num"+id).val())){
				addAmountFlag = false;
				$("#invoice_num"+id).addClass("errorbor");
				layer.alert("只允许输入大于零的数字，小数点后只允许保留八位", {
					icon : 2
				}, function(lay) {
					$("#"+id).prop("checked",false);
					$('#checkbox_all' + buyOrderIndex).prop('checked', false);
					$("#invoice_num"+id).focus();
					layer.close(lay);
				});
				return false;
			}
			//本次录票数量超出最大限制
			var invoiceNum = Number($("#invoice_num"+id).val());
			if(invoiceNum > Number(maxNum)){
				addAmountFlag = false;
				$("#invoice_num"+id).addClass("errorbor");
				layer.alert("（本次录票数量+已录票数量）不得大于已入库总数！", {
					icon : 2
				}, function(lay) {
					$("#"+id).prop("checked",false);
					$("#invoice_num"+id).focus();
					$('#checkbox_all' + buyOrderIndex).prop('checked', false);
					layer.close(lay);
				});
				return false;
			}
		}else{//非蓝字有效
			var invoiceNum = Number($("#invoice_num"+id).val());
			//验证数字是否合法
			var reg = /^(\-?)(([1-9][0-9]*)|(([0]\.\d{1,10}|[1-9][0-9]*\.\d{1,10})))$/;
			if(!reg.test($("#invoice_num"+id).val())){
				addAmountFlag = false;
				$("#invoice_num"+id).addClass("errorbor");
				layer.alert("只允许输入数字，小数点后只允许保留八位", {
					icon : 2
				}, function(lay) {
					$("#"+id).prop("checked",false);
					$("#invoice_num"+id).focus();
					$('#checkbox_all' + buyOrderIndex).prop('checked', false);
					layer.close(lay);
				});
				return false;
			}
			//非蓝字有效，只允许输入负数
			if(invoiceNum >= 0){
				addAmountFlag = false;
				$("#invoice_num"+id).addClass("errorbor");
				layer.alert("只允许输入负数，小数点后只允许保留八位", {
					icon : 2
				}, function(lay) {
					$("#"+id).prop("checked",false);
					$("#invoice_num"+id).focus();
					$('#checkbox_all' + buyOrderIndex).prop('checked', false);
					layer.close(lay);
				});
				return false;
			}
			//无已录入信息，不允许录入选择非蓝字有效
			if(inNum==0 && invoiceNum < 0){
				addAmountFlag = false;
				$("#invoice_num"+id).addClass("errorbor");
				layer.alert("未检测到已录票信息，请验证！", {
					icon : 2
				}, function(lay) {
					$("#"+id).prop("checked",false);
					$("#invoice_num"+id).focus();
					$('#checkbox_all' + buyOrderIndex).prop('checked', false);
					layer.close(lay);
				});
				return false;
			}
			//本次录票额（负数）绝对值大于已录入数量
			if(invoiceNum + Number(maxNum) < 0){
				addAmountFlag = false;
				$("#invoice_num"+id).addClass("errorbor");
				layer.alert("（本次录票数量+已录票数量）不得小于零！", {
					icon : 2
				}, function(lay) {
					$("#"+id).prop("checked",false);
					$("#invoice_num"+id).focus();
					$('#checkbox_all' + buyOrderIndex).prop('checked', false);
					layer.close(lay);
				});
				return false;
			}
		}
		
		//验证金额-------------------------------------------------------------------------------------
		var invoiceTotleAmount = Number($("#invoice_totle_amount"+id).val());
		var str = $("#invoiceForm").find("[name='invoiceColor']").val();
		if(str == "2-1"){
			var reg = /^(([1-9][0-9]*)|(([0]\.\d{1,10}|[1-9][0-9]*\.\d{1,10})))$/;
			if(!reg.test($("#invoice_totle_amount"+id).val())){
				addAmountFlag = false;
				$("#invoice_totle_amount"+id).addClass("errorbor");
				layer.alert("只允许输入大于零的数字，小数点后只允许保留八位", {
					icon : 2
				}, function(lay) {
					$("#"+id).prop("checked",false);
					$("#invoice_totle_amount"+id).focus();
					$('#checkbox_all' + buyOrderIndex).prop('checked', false);
					layer.close(lay);
				});
				return false;
			}

			var standardPrice = Number($('#standard_price' + id).val()).toFixed(2);

			var deleteRebatePrice = Number($('#deleteRebatePrice' + id).val()).toFixed(2);
			if (deleteRebatePrice != -1) {
				var maxInvoiceAmount = (Number($('#max_num' + id).val()) * deleteRebatePrice).toFixed(2);
				if (invoiceTotleAmount > (Number(maxInvoiceAmount) + 1)) {
					layer.alert($('#rebateWarnMessage').val() + (Number(maxInvoiceAmount)).toString());
					$("#invoice_totle_amount" + id).addClass("errorbor");
					return false;
				}
			}
			if(invoiceTotleAmount > (Number(standardPrice) + 1) || invoiceTotleAmount < (Number(standardPrice) - 1) ){
				$("#invoice_totle_amount"+id).addClass("errorbor");
				layer.alert($('#warnMessage').val());
			}
		}else{
			if(inNum==0 && Number($("#invoice_totle_amount"+id)) < 0){
				addAmountFlag = false;
				$("#invoice_num"+id).addClass("errorbor");
				layer.alert("未检测到已录票信息，请验证！", {
					icon : 2
				}, function(lay) {
					$("#"+id).prop("checked",false);
					$("#invoice_num"+id).focus();
					$('#checkbox_all' + buyOrderIndex).prop('checked', false);
					layer.close(lay);
				});
				return false;
			}
			var reg = /^(\-?)(([1-9][0-9]*)|(([0]\.\d{1,10}|[1-9][0-9]*\.\d{1,10})))$/;
			if(!reg.test($("#invoice_totle_amount"+id).val())){
				addAmountFlag = false;
				$("#invoice_totle_amount"+id).addClass("errorbor");
				layer.alert("只允许输入数字，小数点后只允许保留八位", {
					icon : 2
				}, function(lay) {
					$("#"+id).prop("checked",false);
					$("#invoice_totle_amount"+id).focus();
					$('#checkbox_all' + buyOrderIndex).prop('checked', false);
					layer.close(lay);
				});
				return false;
			}
			if(invoiceTotleAmount >= 0){
				addAmountFlag = false;
				$("#invoice_totle_amount"+id).addClass("errorbor");
				layer.alert("只允许负数数字，小数点后只允许保留八位", {
					icon : 2
				}, function(lay) {
					$("#"+id).prop("checked",false);
					$("#invoice_totle_amount"+id).focus();
					$('#checkbox_all' + buyOrderIndex).prop('checked', false);
					layer.close(lay);
				});
				return false;
			}
			if(invoiceTotleAmount + maxPrice < 0){
				addAmountFlag = false;
				$("#invoice_totle_amount"+id).addClass("errorbor");
				layer.alert("本次录票总额绝对值不允许低于已录金额", {
					icon : 2
				}, function(lay) {
					$("#"+id).prop("checked",false);
					$("#invoice_totle_amount"+id).focus();
					$('#checkbox_all' + buyOrderIndex).prop('checked', false);
					layer.close(lay);
				});
				return false;
			}
		}
		
		//---------------------------------------------------------------------------------------------------
		var traderId = $(obj).attr("class");
		$("#buyorderInfo").find("input[name='selectInvoiceName']:checked").each(function(i){
			if(traderId!=$(this).attr("class")){
				addAmountFlag = false;
				layer.alert("所选数据中供应商不同，请重新确认！", {
					icon : 2
				}, function(lay) {
					$("#"+id).prop("checked",false);
					$('#checkbox_all' + buyOrderIndex).prop('checked', false);
					layer.close(lay);
				});
				return false;
			}
		})

		if (addAmountFlag){
			$("#amount").val((totalAmount + selectPrice).toFixed(2));
			$(".J-amount").html((totalAmount + selectPrice).toFixed(2));
			checkAmountLeft();
		}
		$("#selectPrice").html((totalAmount + selectPrice).toFixed(2));
		$("#selectNum").html((Number($("#selectNum").html()) + Number($("#invoice_num"+$(obj).attr("id")).val())).toFixed(2));
	}else{
		$("#amount").val((totalAmount -selectPrice).toFixed(2));
		$(".J-amount").html((totalAmount - selectPrice).toFixed(2));
		checkAmountLeft();
		$("#selectPrice").html((totalAmount - selectPrice).toFixed(2));
		
		$("#selectNum").html((Number($("#selectNum").html()) - Number($("#invoice_num"+id).val())).toFixed(2));
	}

	// var needChangeAllCheckBox = true;
	var thisCheckStatus = true;
	var all_single_checkbox = $(obj).parents(".order_info_body").find(".single_check_box");
	console.log(all_single_checkbox);
	if (all_single_checkbox.length > 0){
		all_single_checkbox.each(function (i){
			var thisCheckObj = $(all_single_checkbox[i]).find("[id]");
			var thisSelectCheckboxStatus = $(thisCheckObj).is(':checked');
			console.log('thisSelectCheckboxStatus' + thisSelectCheckboxStatus);
			if (!thisSelectCheckboxStatus){
				thisCheckStatus = false;
			}
		})
	} else {
		thisCheckStatus = false;
	}

	$('#checkbox_all' + buyOrderIndex).prop('checked', thisCheckStatus)
}


// 多选框
function selectAll(param){
	var addAmountFlag = true;
	// 查找当前多选框下面的所有单选框（应该是个数组）
	var all_single_checkbox = $(param).parents(".order_info_thead").siblings(".order_info_body").find(".single_check_box");
	// 如果存在单选框
	if(all_single_checkbox && all_single_checkbox.length > 0){
		// 循环单选框
		for (var i = 0; i < all_single_checkbox.length; i++) {
			// 获取单选框
			var input_obj = $(all_single_checkbox[i]).find("[id]");
			// 采购价
			var price = $(input_obj).attr("goodsPrice");
			// 本次录票数量
			var maxNum = $(input_obj).attr("maxNum");
			// 本次录票总额
			var maxPrice = $(input_obj).attr("maxPrice");
			// 已录票数量
			var inNum = $(input_obj).attr("inNum");
			// 采购订单的index_商品的index
			var index = $(input_obj).attr("id");
			// id
			var id = $(input_obj).attr("id");
			
			// 如果是全选
			if($(param).is(':checked')){
				
				// 如果当前单选按钮被选中了
				if($(input_obj).is(':checked')){
					continue;
				}
				
				// 本次录票数量 必须大于 0
				if(Number(maxNum) == 0){
					$("#invoice_num"+id).addClass("errorbor");
					continue;
				}
				// 获取发票税额
				var	invoiceType = Number($("#taxRateFlag").val()).toFixed(10)*1;
				// 判断录票数量和录票总价不为0
				if(Number($(input_obj).val()) != 0 && Number($("#invoice_totle_amount"+index).val()) != 0){
					var num = $("#invoice_num"+index).val();
					var totalPrice = $("#invoice_totle_amount"+index).val();
					
					// 如果录票数量不大于0，不勾选
					if(Number(num) <= 0){
						$("#invoice_num"+id).addClass("errorbor");
						continue;
					}

					// 修改录票单价
					$("#invoice_price"+index).html((totalPrice/num).toFixed(9));
					$("#invoice_no_tax"+index).html((totalPrice/(1+invoiceType)).toFixed(9));
					$("#invoice_tax"+index).html((totalPrice - (totalPrice/(1+invoiceType))).toFixed(9));
				}
				
				// 选中当前单选框
				$("#"+id).prop("checked", true);
				
				// 当前选择的金额
				var selectPrice = Number($("#invoice_totle_amount" + id).val());
				// 已选择金额
				var totalAmount = Number($("#amount").val());
				// 如果当前被选中了
				if($(input_obj).is(':checked')){
					
					// 验证数量----------------------------------------------------------------------------------------
					var str = $("#invoiceForm").find("[name='invoiceColor']").val();
					if(str == "2-1"){//蓝字有效
						// 验证数字是否合法
						var reg = /^(([1-9][0-9]*)|(([0]\.\d{1,10}|[1-9][0-9]*\.\d{1,10})))$/;
						if(!reg.test($("#invoice_num"+id).val())){
							$("#invoice_num"+id).addClass("errorbor");
							layer.alert("只允许输入大于零的数字，小数点后只允许保留八位", {
								icon : 2
							}, function(lay) {
								all_single_checkbox.each(function (i){
									$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
								})
								$("#invoice_num"+id).focus();
								addAmountFlag = false;
								$(param).prop('checked', false);
								layer.close(lay);
							});
							return false;
						}
						
						//本次录票数量超出最大限制
						var invoiceNum = Number($("#invoice_num"+id).val());
						if(invoiceNum > Number(maxNum)){//
							$("#invoice_num"+id).addClass("errorbor");
							layer.alert("（本次录票数量+已录票数量）不得大于已入库总数！", {
								icon : 2
							}, function(lay) {
								all_single_checkbox.each(function (i){
									$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
								})
								$("#invoice_num"+id).focus();
								addAmountFlag = false;
								$(param).prop('checked', false);
								layer.close(lay);
							});
							return false;
						}
					}
					
					else{//非蓝字有效
						var invoiceNum = Number($("#invoice_num"+id).val());
						//验证数字是否合法
						var reg = /^(\-?)(([1-9][0-9]*)|(([0]\.\d{1,10}|[1-9][0-9]*\.\d{1,10})))$/;
						if(!reg.test($("#invoice_num"+id).val())){
							$("#invoice_num"+id).addClass("errorbor");
							layer.alert("只允许输入数字，小数点后只允许保留八位", {
								icon : 2
							}, function(lay) {
								all_single_checkbox.each(function (i){
									$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
								})
								$("#invoice_num"+id).focus();
								addAmountFlag = false;
								$(param).prop('checked', false);
								layer.close(lay);
							});
							return false;
						}
						
						//非蓝字有效，只允许输入负数
						if(invoiceNum >= 0){
							$("#invoice_num"+id).addClass("errorbor");
							layer.alert("只允许输入负数，小数点后只允许保留八位", {
								icon : 2
							}, function(lay) {
								all_single_checkbox.each(function (i){
									$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
								})
								$("#invoice_num"+id).focus();
								addAmountFlag = false;
								$(param).prop('checked', false);
								layer.close(lay);
							});
							return false;
						}
						
						//无已录入信息，不允许录入选择非蓝字有效
						if(inNum==0 && invoiceNum < 0){
							$("#invoice_num"+id).addClass("errorbor");
							layer.alert("未检测到已录票信息，请验证！", {
								icon : 2
							}, function(lay) {
								all_single_checkbox.each(function (i){
									$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
								})
								$("#invoice_num"+id).focus();
								addAmountFlag = false;
								$(param).prop('checked', false);
								layer.close(lay);
							});
							return false;
						}
						
						//本次录票额（负数）绝对值大于已录入数量
						if(invoiceNum + Number(maxNum) < 0){//
							$("#invoice_num"+id).addClass("errorbor");
							layer.alert("（本次录票数量+已录票数量）不得小于零！", {
								icon : 2
							}, function(lay) {
								all_single_checkbox.each(function (i){
									$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
								})
								$("#invoice_num"+id).focus();
								addAmountFlag = false;
								$(param).prop('checked', false);
								layer.close(lay);
							});
							return false;
						}
					}
					
					
					//验证金额-------------------------------------------------------------------------------------
					var invoiceTotleAmount = Number($("#invoice_totle_amount"+id).val());
					var str = $("#invoiceForm").find("[name='invoiceColor']").val();
					if(str == "2-1"){
						var reg = /^(([1-9][0-9]*)|(([0]\.\d{1,10}|[1-9][0-9]*\.\d{1,10})))$/;
						if(!reg.test($("#invoice_totle_amount"+id).val())){
							$("#invoice_totle_amount"+id).addClass("errorbor");
							layer.alert("只允许输入大于零的数字，小数点后只允许保留八位", {
								icon : 2
							}, function(lay) {
								all_single_checkbox.each(function (i){
									$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
								})
								$("#invoice_totle_amount"+id).focus();
								addAmountFlag = false;
								$(param).prop('checked', false);
								layer.close(lay);
							});
							return false;
						}

						var standardPrice = Number($('#standard_price' + id).val()).toFixed(2);

						var deleteRebatePrice = Number($('#deleteRebatePrice' + id).val()).toFixed(2);
						if (deleteRebatePrice != -1) {
							var maxInvoiceAmount = (Number($('#max_num' + id).val()) * deleteRebatePrice).toFixed(2);
							if (Number(invoiceTotleAmount) > (Number(maxInvoiceAmount) + 1)) {
								layer.alert($('#rebateWarnMessage').val() + (Number(maxInvoiceAmount)).toString());
								$("#invoice_totle_amount" + id).addClass("errorbor");
								return false;
							}
						}

						if(Number(invoiceTotleAmount) > (Number(standardPrice) + 1) || Number(invoiceTotleAmount) < (Number(standardPrice) - 1)){
							$("#invoice_totle_amount"+id).addClass("errorbor");
                            layer.alert($('#warnMessage').val());
						}
					}else{
						if(inNum==0 && Number($("#invoice_totle_amount"+id)) < 0){
							$("#invoice_num"+id).addClass("errorbor");
							layer.alert("未检测到已录票信息，请验证！", {
								icon : 2
							}, function(lay) {
								all_single_checkbox.each(function (i){
									$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
								})
								$("#invoice_num"+id).focus();
								addAmountFlag = false;
								$(param).prop('checked', false);
								layer.close(lay);
							});
							return false;
						}
						var reg = /^(\-?)(([1-9][0-9]*)|(([0]\.\d{1,10}|[1-9][0-9]*\.\d{1,10})))$/;
						if(!reg.test($("#invoice_totle_amount"+id).val())){
							$("#invoice_totle_amount"+id).addClass("errorbor");
							layer.alert("只允许输入数字，小数点后只允许保留八位", {
								icon : 2
							}, function(lay) {
								all_single_checkbox.each(function (i){
									$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
								})
								$("#invoice_totle_amount"+id).focus();
								addAmountFlag = false;
								$(param).prop('checked', false);
								layer.close(lay);
							});
							return false;
						}
						if(invoiceTotleAmount >= 0){
							$("#invoice_totle_amount"+id).addClass("errorbor");
							layer.alert("只允许负数数字，小数点后只允许保留八位", {
								icon : 2
							}, function(lay) {
								all_single_checkbox.each(function (i){
									$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
								})
								$("#invoice_totle_amount"+id).focus();
								addAmountFlag = false;
								$(param).prop('checked', false);
								layer.close(lay);
							});
							return false;
						}
						if(invoiceTotleAmount + maxPrice < 0){
							$("#invoice_totle_amount"+id).addClass("errorbor");
							layer.alert("本次录票总额绝对值不允许低于已录金额", {
								icon : 2
							}, function(lay) {
								all_single_checkbox.each(function (i){
									$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
								})
								$("#invoice_totle_amount"+id).focus();
								addAmountFlag = false;
								$(param).prop('checked', false);
								layer.close(lay);
							});
							return false;
						}
					}
					
					//---------------------------------------------------------------------------------------------------
					var traderId = $(input_obj).attr("class");
					$("#buyorderInfo").find("input[name='selectInvoiceName']:checked").each(function(i){
						if(traderId!=$(this).attr("class")){
							addAmountFlag = false;
							layer.alert("所选数据中供应商不同，请重新确认！", {
								icon : 2
							}, function(lay) {
								all_single_checkbox.each(function (i){
									$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
								})
								addAmountFlag = false;
								$(param).prop('checked', false);
								layer.close(lay);
							});
							return false;
						}
					})

					if (addAmountFlag){
						$("#amount").val((totalAmount + selectPrice).toFixed(2));
						$(".J-amount").html((totalAmount + selectPrice).toFixed(2));
					}

					checkAmountLeft();
					$("#selectPrice").html((totalAmount + selectPrice).toFixed(2));
					$("#selectNum").html((Number($("#selectNum").html()) + Number($("#invoice_num"+$(input_obj).attr("id")).val())).toFixed(2));
				}
			}
			
			// 如果多选框不是选中状态
			else{
				all_single_checkbox.each(function (i){
					$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
				})
				$(param).prop('checked', false);
				// 当前选择的金额
				var selectPrice = Number($("#invoice_totle_amount" + id).val());
				// 已选择金额
				var totalAmount = Number($("#amount").val());
				$("#amount").val((totalAmount - selectPrice).toFixed(2));
				$(".J-amount").html((totalAmount - selectPrice).toFixed(2));
				$("#selectPrice").html(0);
				$("#selectNum").html((Number(0)));
			}
		}
	}
	checkAmountLeft();
}



































function checkAmountLeft(){
	var total = Number($('.J-amount-total').html());
	var amount = Number($('.J-amount').html());
	var recordAmount = Number($('.J-record-amount').val());
	var left = total - amount - recordAmount;
	
	$('.J-amount-left').html(left.toFixed(2))
	if (left > 0) {
		$('.J-amount-left').addClass('orange').removeClass('red');
	} else if (left == 0) {
		$('.J-amount-left').removeClass('red orange');
	} else if (left < 0) {
		$('.J-amount-left').addClass('red').removeClass('orange');
	}
};

$(function(){
	checkAmountLeft();
	
	var checkHeaderFixed = function(){
		if ($(window).scrollTop() > 0) {
			$('.J-header').addClass('fixed');
		} else {
			$('.J-header').removeClass('fixed');
		}
	};

	checkHeaderFixed();

	$(window).on('scroll', function(){
		checkHeaderFixed();
	})

	$('.J-show-pic').click(function(){
		var picUrl = $('.J-pic-url').val().trim();

		if (picUrl) {
			window.open(picUrl);
			return;
		}

		$.ajax({
			url: '/finance/invoice/downLoadAndSaveInvoiceHref.do',
			data: {
				invoiceId: $('[name=hxInvoiceId]').val(),
				invoiceType: '1',
				invoiceCode: $('#invoiceCode').val(),
				invoiceNum: $('#invoiceNo').val()
			},
			type: 'post',
			dataType: "json",
			success: function (res) {
				if (res.code == -1) {
					layer.alert(res.message, function () {
						layer.closeAll();
					});
				} else {
					window.open(res.data)
				}
			},
			error: function (data) {
				if (data.status == 1001) {
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		})
	})
})

function invoiceNumChange(obj,price,maxNum, rebatePrice){
	$(obj).removeClass("errorbor");
	/*if($("#"+id).is(":checked")){
		return false;
	}*/
	var reg;
	var str = $("#invoiceForm").find("[name='invoiceColor']").val();
	if(str == "2-1"){//蓝字有效
		reg = /^(([1-9][0-9]*)|(([0]\.\d{1,10}|[1-9][0-9]*\.\d{1,10})))$/;
	}else{
		reg = /^(\-?)(([1-9][0-9]*)|(([0]\.\d{1,10}|[1-9][0-9]*\.\d{1,10})))$/;
	}
	if((!reg.test($(obj).val())) || $(obj).val().length > 15){
		$(obj).val("0.00");
		$(obj).addClass("errorbor");
		return false;
	}
	if(Number($(obj).val()) == 0 || Number($(obj).val()) > maxNum){
		$(obj).val("0.00");
		$(obj).addClass("errorbor");
		return false;
	}
	
//	判断本次录票总额是否为0
	if(Number($("#invoice_totle_amount"+index).val()) == 0){
		$("#invoice_totle_amount"+index).val("0.00");
		$("#invoice_totle_amount"+index).addClass("errorbor");
		return false;
	}
	
	invoiceType = Number($("#taxRateFlag").val()).toFixed(10)*1;
	var invoiceNum = Number($(obj).val());
	var index = $(obj).attr("alt");
//	$("#invoice_price"+index).html((price/(1+invoiceType)).toFixed(8));
//	$("#invoice_totle_amount"+index).val((price*invoiceNum).toFixed(8));


	var standardPrice = Number(($('#goods_price' + index).val() - Number(rebatePrice)) * $(obj).val()).toFixed(2);
	$("#standard_price" + index).val(standardPrice);
	$('#invoice_totle_amount' + index).val(standardPrice);

	//判断录票数量和录票总价不为0
	if(Number($(obj).val()) != 0 && Number($("#invoice_totle_amount"+index).val()) != 0){
		var num = $(obj).val();
		var totalPrice = $("#invoice_totle_amount"+index).val();
		//修改录票单价
		$("#invoice_price"+index).html((totalPrice/num).toFixed(9));
		$("#invoice_no_tax"+index).html((totalPrice/(1+invoiceType)).toFixed(9));
		$("#invoice_tax"+index).html((totalPrice - (totalPrice/(1+invoiceType))).toFixed(9));
	}
	
	
	
	var selectNum = 0.00;
	var selectPrice = 0.00;
	$("#buyorderInfo").find("input[name='selectInvoiceName']").each(function(){
		if($(this).is(':checked')){
			if($(this).attr("id") && null != $(this).attr("id")){
				selectNum += Number($("#invoice_num"+$(this).attr("id")).val());
				selectPrice += Number($("#invoice_totle_amount"+$(this).attr("id")).val());
			}
		}
	});
	$("#selectNum").html(selectNum.toFixed(10));
	$("#selectPrice").html(selectPrice.toFixed(9));
	$("#invoiceForm").find("#amount").val(selectPrice.toFixed(2));
	$(".J-amount").html(selectPrice.toFixed(2));
	checkAmountLeft();
}
function invoiceTotleAmountChange(obj,maxPrice,price,arrivalNum, invoicedNum){
	$(obj).removeClass("errorbor");
	var reg;
	var str = $("#invoiceForm").find("[name='invoiceColor']").val();
	if(str == "2-1"){//蓝字有效
		reg = /^(([1-9][0-9]*)|(([0]\.\d{1,9}|[1-9][0-9]*\.\d{1,9})))$/;
	}else{
		reg = /^(\-?)(([1-9][0-9]*)|(([0]\.\d{1,9}|[1-9][0-9]*\.\d{1,9})))$/;
	}
	if(!reg.test($(obj).val())  || $(obj).val().length > 15){
		$(obj).val("0.00");
		$(obj).addClass("errorbor");
		return false;
	}
    var myIndex = $(obj).attr("alt");
	var standardPrice = Number($('#standard_price' + myIndex).val()).toFixed(2);

	var invoice_num = Number($('#invoice_num' + myIndex).val()).toFixed(2);
	if (Number(arrivalNum) > 0) {
		var maxInvoiceAmount = (standardPrice/ invoice_num)* (Number(arrivalNum) - Number(invoicedNum));
		if (Number($(obj).val()) == 0 || Number($(obj).val()) > (Number(maxInvoiceAmount) + 1)) {
			layer.alert($('#rebateWarnMessage').val() + (Number(maxInvoiceAmount)).toString());
			$(obj).addClass("errorbor");
			return false;
		}
	}

	if(Number($(obj).val()) == 0 || Number($(obj).val()) > (Number(standardPrice) + 1) || Number($(obj).val())  < (Number(standardPrice)  - 1)){
		layer.alert($('#warnMessage').val());
		$(obj).addClass("errorbor");
	}
	//判断本次录票数量是否为0
	if(Number($("#invoice_num"+index).val()) == 0){
		$("#invoice_num"+index).val("0.00");
		$("#invoice_num"+index).addClass("errorbor");
		return false;
	}
	
	invoiceType = Number($("#taxRateFlag").val()).toFixed(10)*1;
	var invoiceTotleAmount = Number($(obj).val());
	var index = $(obj).attr("alt");
//	$("#invoice_price"+index).html((price/(1+invoiceType)).toFixed(8));
//	$("#invoice_num"+index).val((invoiceTotleAmount/price).toFixed(8));
	
	var str = $("#taxRateFlag").val();

	//判断录票数量和录票总价不为0
	if(Number($(obj).val()) != 0 && Number($("#invoice_totle_amount"+index).val()) != 0){
		var totalPrice = $(obj).val();
		var num = $("#invoice_num"+index).val();
		//修改录票单价
		$("#invoice_price"+index).html((totalPrice/num).toFixed(9));
		$("#invoice_no_tax"+index).html((totalPrice/(1+invoiceType)).toFixed(9));
		$("#invoice_tax"+index).html((totalPrice - (totalPrice/(1+invoiceType))).toFixed(9));
	}
	
	
	var selectNum = 0.00;
	var selectPrice = 0.00;
	$("#buyorderInfo").find("input[name='selectInvoiceName']").each(function(){
		if($(this).is(':checked')){
			if($(this).attr("id") && null != $(this).attr("id")){
				selectNum += Number($("#invoice_num"+$(this).attr("id")).val());
				selectPrice += Number($("#invoice_totle_amount"+$(this).attr("id")).val());
			}
		}
	});
	$("#selectNum").html(selectNum.toFixed(10));
	$("#selectPrice").html(selectPrice.toFixed(9));
	$("#invoiceForm").find("#amount").val(selectPrice.toFixed(2));
	$('.J-amount').html(selectPrice.toFixed(2))
	checkAmountLeft();
}
function vailInvoiceNo(obj){
	clear2ErroeMes();
	var invoiceNo = $(obj).val().trim();
	if(invoiceNo.length == 0){
		warn2Tips("invoiceNo","发票号不允许为空");
		return false;
	}else{
		var reg = /^\d{8}$/;
		if(!reg.test(invoiceNo)){
			warn2Tips("invoiceNo","请输入正确的8位数字发票号");
			return false;
		}
	}
}

function vailInvoiceCode(obj){
	clear2ErroeMes();
	var invoiceCode = $(obj).val().trim();
	if(invoiceCode.length == 0){
		warn2Tips("invoiceCode","发票代码不允许为空");
		return false;
	} else {
		var reg = /^[0-9]*$/;
		if (!reg.test(invoiceCode)) {
			warn2Tips("invoiceCode","请输入数字");
			return false;
		}
	}
}

function updateInvoiceColor(){
	var str = $("#invoiceForm").find("[name='invoiceColor']").val();
	if(str == "2-1"){
		$("#invoiceForm").find("#colorType").val("2");//蓝票
		$("#invoiceForm").find("#isEnable").val("1");//有效
	}else if(str == "2-0"){
		$("#invoiceForm").find("#colorType").val("2");
		$("#invoiceForm").find("#isEnable").val("0");//失效
	}else if(str == "1-1"){
		$("#invoiceForm").find("#colorType").val("1");//红票
		$("#invoiceForm").find("#isEnable").val("1");
	}
}

function addInvoice(){
	checkLogin();
	clear2ErroeMes();

	var id = "";
	var invoice;var flag = true;
	var relatedIdArr = [];var invoiceNumArr = [];var invoicePriceArr = [];var invoiceTotalAmountArr = [];var buyorderGoodsIdArr = [];
	var goodsTypeArr = [];
	if($("#buyorderInfo").find("input[name='selectInvoiceName']:checked").length>0){
		$("#buyorderInfo").find("input[name='selectInvoiceName']:checked").each(function(i){
			id = $(this).attr("id");
			goodsTypeArr.push($("#goodsType"+id).val());
			//采购单号
			relatedIdArr.push($("#relatedId"+id).val());
			//产品ID
			buyorderGoodsIdArr.push($("#buyorderInfo").find("#buyorderGoodsId"+id).val());
			//录票单价
			invoicePriceArr.push($("#invoice_price"+id).html());
			//本次录票数量----------------------------------------------------------------------
			invoice = $("#invoice_num"+id).val().trim();
			//已勾选项，不允许为空
			if(invoice.length == 0 || invoice == 0 || invoice.length > 15){
				$("#invoice_num"+id).addClass("errorbor");
				layer.alert("只允许输入数字，小数点后只允许保留八位，最大允许15位", {
					icon : 2
				}, function(lay) {
					$("#invoice_num"+id).focus();
					layer.close(lay);
				});
				flag = false;
				return flag;
			}
			var str = $("#invoiceForm").find("[name='invoiceColor']").val();
			if(str == "2-1"){//蓝字有效
				//验证数字是否合法
				var reg = /^(([1-9][0-9]*)|(([0]\.\d{1,10}|[1-9][0-9]*\.\d{1,10})))$/;
				if(!reg.test($("#invoice_num"+id).val())){
					$("#invoice_num"+id).addClass("errorbor");
					layer.alert("只允许输入大于零的数字，小数点后只允许保留八位", {
						icon : 2
					}, function(lay) {
						$("#invoice_num"+id).focus();
						layer.close(lay);
					});
					flag = false;
					return flag;
				}
				//本次录票数量超出最大限制
				if(Number($("#invoice_num"+id).val()) > Number($("#max_num"+id).val())){//
					$("#invoice_num"+id).addClass("errorbor");
					layer.alert("（本次录票数量+已录票数量）不得大于已入库总数！", {
						icon : 2
					}, function(lay) {
						$("#invoice_num"+id).focus();
						layer.close(lay);
					});
					flag = false;
					return flag;
				}
			}else{//非蓝字有效
				//验证数字是否合法
				var reg = /^(\-?)(([1-9][0-9]*)|(([0]\.\d{1,10}|[1-9][0-9]*\.\d{1,10})))$/;
				if(!reg.test(invoice)){
					$("#invoice_num"+id).addClass("errorbor");
					layer.alert("只允许输入数字，小数点后只允许保留八位", {
						icon : 2
					}, function(lay) {
						$("#invoice_num"+id).focus();
						layer.close(lay);
					});
					flag = false;
					return flag;
				}
				//非蓝字有效，只允许输入负数
				if(Number(invoice) >= 0){
					$("#invoice_num"+id).addClass("errorbor");
					layer.alert("只允许输入负数，小数点后只允许保留八位！", {
						icon : 2
					}, function(lay) {
						all_single_checkbox.each(function (i){
							$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
						})
						$("#invoice_num"+id).focus();
						layer.close(lay);
					});
					return false;
				}
				//无已录入信息，不允许录入选择非蓝字有效
				if(Number($("#inNum"+id).val())==0 && Number(invoice) < 0){
					$("#invoice_num"+id).addClass("errorbor");
					layer.alert("未检测到已录票信息，请验证！", {
						icon : 2
					}, function(lay) {
						all_single_checkbox.each(function (i){
							$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
						})
						$("#invoice_num"+id).focus();
						layer.close(lay);
					});
					return false;
				}
				//本次录票数量（负数）绝对值大于已录入数量
				if(Number(invoice) + Number($("#max_num"+id).val()) < 0){
					$("#invoice_num"+id).addClass("errorbor");
					layer.alert("（本次录票数量+已录票数量）不得小于零！", {
						icon : 2
					}, function(lay) {
						$("#invoice_num"+id).focus();
						layer.close(lay);
					});
					flag = false;
					return flag;
				}
			}
			invoiceNumArr.push(invoice);
			//本次录票总金额------------------------------------------------------------------------------
			invoice = $("#invoice_totle_amount"+id).val();
			var str = $("#invoiceForm").find("[name='invoiceColor']").val();
			//已勾选项，不允许为空
			if(invoice.length == 0 || invoice == 0 || invoice.length > 15){
				$("#invoice_totle_amount"+id).addClass("errorbor");
				layer.alert("只允许输入数字，小数点后只允许保留八位，最大允许15位", {
					icon : 2
				}, function(lay) {
					$("#invoice_totle_amount"+id).focus();
					layer.close(lay);
				});
				flag = false;
				return flag;
			}
			if(str == "2-1"){//蓝字有效
				//验证数字是否合法
				var reg = /^(([1-9][0-9]*)|(([0]\.\d{1,10}|[1-9][0-9]*\.\d{1,10})))$/;
				if(!reg.test(invoice)){
					$("#invoice_totle_amount"+id).addClass("errorbor");
					layer.alert("只允许输入大于零的数字，小数点后只允许保留八位", {
						icon : 2
					}, function(lay) {
						$("#invoice_totle_amount"+id).focus();
						layer.close(lay);
					});
					flag = false;
					return flag;
				}

				//录入金额大于最大限制
				var standardPrice = Number($('#standard_price' + id).val()).toFixed(2);

				var deleteRebatePrice = Number($('#deleteRebatePrice' + id).val()).toFixed(2);
				if (deleteRebatePrice != -1) {
					var maxInvoiceAmount = (Number($('#max_num' + id).val()) * deleteRebatePrice).toFixed(2);
					if (Number(invoice) > (Number(maxInvoiceAmount) + 1)) {
						alert($('#rebateWarnMessage').val() + (Number(maxInvoiceAmount)).toString());
						$("#invoice_totle_amount" + id).addClass("errorbor");
						return false;
					}
				}

				if(Number(invoice) > (Number(standardPrice) + 1) || Number(invoice) < (Number(standardPrice) - 1)){
					$("#invoice_totle_amount"+id).addClass("errorbor");
					alert($('#warnMessage').val());
				}
			}else{
				//验证数字是否合法
				var reg = /^(\-?)(([1-9][0-9]*)|(([0]\.\d{1,10}|[1-9][0-9]*\.\d{1,10})))$/;
				if(!reg.test(invoice)){
					$("#invoice_totle_amount"+id).addClass("errorbor");
					layer.alert("只允许输入数字，小数点后只允许保留八位", {
						icon : 2
					}, function(lay) {
						$("#invoice_totle_amount"+id).focus();
						layer.close(lay);
					});
					flag = false;
					return flag;
				}
				//非蓝字有效，只允许输入负数
				if(invoice >= 0){
					$("#invoice_num"+id).addClass("errorbor");
					layer.alert("只允许输入负数，小数点后只允许保留八位！", {
						icon : 2
					}, function(lay) {
						all_single_checkbox.each(function (i){
							$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
						})
						$("#invoice_num"+id).focus();
						layer.close(lay);
					});
					return false;
				}
				//无已录入信息，不允许录入选择非蓝字有效
				if(Number($("#inNum"+id).val())==0 && Number(invoice) < 0){
					$("#invoice_num"+id).addClass("errorbor");
					layer.alert("未检测到已录票信息，请验证！", {
						icon : 2
					}, function(lay) {
						all_single_checkbox.each(function (i){
							$($(all_single_checkbox[i]).find("[id]")).prop("checked",false);
						})
						$("#invoice_num"+id).focus();
						layer.close(lay);
					});
					return false;
				}
				//本次录票额（负数）绝对值大于已录入金额
				if(Number(invoice) + Number($("#max_price"+id).val()) < 0){
					$("#invoice_totle_amount"+id).addClass("errorbor");
					layer.alert("本次录票总额绝对值不允许低于已录金额", {
						icon : 2
					}, function(lay) {
						$("#invoice_totle_amount"+id).focus();
						layer.close(lay);
					});
					return false;
				}
			}
			invoiceTotalAmountArr.push(invoice);
		});
		if(flag==false){
			return false;
		}
	}else{
		layer.alert("录票总件数/录票总额为0，不允许提交！", {icon : 2});
		return false;
	}
	if(relatedIdArr.length <= 0 || $("#invoiceForm").find("#amount").val()==0){
		layer.alert("参数错误", {icon : 2});
		return false;
	}
	
	$("#invoiceForm #hideValue").html("");
	$("#invoiceForm #hideValue").append("<input type='hidden' name='ratio' value='"+$("#taxRateFlag").val()+"'>");
	$("#invoiceForm #hideValue").append("<input type='hidden' name='relatedIdArr' value='"+relatedIdArr+"'>");
	$("#invoiceForm #hideValue").append("<input type='hidden' name='detailGoodsIdArr' value='"+buyorderGoodsIdArr+"'>");
	$("#invoiceForm #hideValue").append("<input type='hidden' name='invoiceNumArr' value='"+invoiceNumArr+"'>");
	$("#invoiceForm #hideValue").append("<input type='hidden' name='invoicePriceArr' value='"+invoicePriceArr+"'>");
	$("#invoiceForm #hideValue").append("<input type='hidden' name='invoiceTotalAmountArr' value='"+invoiceTotalAmountArr+"'>");
	$("#invoiceForm #hideValue").append("<input type='hidden' name='goodsTypeArr' value='"+goodsTypeArr+"'>");

	$.ajax({
		async:false,
		url:'./saveBuyOrderInvoiceNew.do',
		data:$("#invoiceForm").serialize(),
		type:"POST",
		dataType : "json",
		success:function(data){
			refreshNowPageList(data);
		},error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	})
}