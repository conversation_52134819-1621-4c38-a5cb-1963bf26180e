package com.newtask.ddi;


import com.newtask.ddi.dao.DdiBuyorderExtMapper;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.order.model.GeSaleOrder;
import com.wms.dao.VWmsInSncodeOrderExtMapper;
import com.wms.model.ddi.DdiBuyorderExtDto;
import com.wms.model.ddi.VWmsInSncodeOrderExtDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Component
@JobHandler(value = "DdiBuyOrderTask")
public class DdiBuyOrderTask extends AbstractJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(DdiBuyOrderTask.class);

    @Autowired
    private VWmsInSncodeOrderExtMapper vWmsInsncodeOrderExtMapper;


    @Autowired
    private DdiBuyorderExtMapper ddiBuyorderExtMapper;


    @Value("${GE_TRADER_SKU}")
    private String geTraderSku;

    @Transactional
    public ReturnT<String> doExecute(String param) throws Exception {


        LOGGER.info("DDI采购订单数据采集定时任务开始执行" + param);
        XxlJobLogger.log("DDI采购订单数据采集定时任务开始执行" + param);

        //默认取开始时间位昨天的零点到23：59
        Date yesterdayDate = DateUtil.getPreviousDayByDateTime(new Date());
        Timestamp startTime = DateUtil.getDayStartTime(yesterdayDate);
        Timestamp endTime = DateUtil.getDayEndTime(yesterdayDate);

        if (!StringUtils.isBlank(param)) {
            String[] params = param.split(",");
            String startTimeStr = params[0];
            String endTimeStr = params[1];

            long startTimeLong = DateUtil.convertLong(startTimeStr, DateUtil.DATE_FORMAT);
            long endTimeLong = DateUtil.convertLong(endTimeStr, DateUtil.DATE_FORMAT);

            startTime = DateUtil.getDayStartTime(new Date(startTimeLong));
            endTime = DateUtil.getDayEndTime(new Date(endTimeLong));
        }

        GeSaleOrder geSaleOrder = JsonUtils.readValue(geTraderSku, GeSaleOrder.class);

        List<String> skuList = geSaleOrder.getSkuList();

        List<VWmsInSncodeOrderExtDto> VWmsInSncodeOrderExtDtoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(skuList)) {
            VWmsInSncodeOrderExtDtoList = vWmsInsncodeOrderExtMapper.getAlltodayOrder(skuList, startTime, endTime);
        }
        try {   //获取昨日所有相关数据
            for (VWmsInSncodeOrderExtDto VWmsInSncodeOrderExtDto : VWmsInSncodeOrderExtDtoList) {


                List<DdiBuyorderExtDto> ddiBuyorderExtDtoList = vWmsInsncodeOrderExtMapper.getSingleOrder(VWmsInSncodeOrderExtDto);

                if (!CollectionUtils.isEmpty(ddiBuyorderExtDtoList)) {
                    for (DdiBuyorderExtDto ddiBuyorderExtDto : ddiBuyorderExtDtoList) {
                        // GE产品销售数量不用去订单里面取，取1
                        ddiBuyorderExtDto.setNum(1);
                        ddiBuyorderExtMapper.insertSingle(ddiBuyorderExtDto);
                    }
                }

            }

        } catch (Exception e) {
            LOGGER.error("DDI采购订单数据采集失败,错误原因:{}", e);
            return FAIL;
        }

        LOGGER.info("DDI采购订单数据采集定时任务结束" + param);
        XxlJobLogger.log("DDI采购订单数据采集定时任务结束 " + param);
        return SUCCESS;
    }
}
