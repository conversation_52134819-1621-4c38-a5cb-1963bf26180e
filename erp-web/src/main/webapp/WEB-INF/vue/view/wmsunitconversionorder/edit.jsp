<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>


<div id="app" style="display: none;">
    <el-form ref="form" :model="wmsUnitConversionOrderDto"
             :label-position="labelPosition" label-width="80px">
        <el-card class="box-card">
            <div slot="header">
                <span>库存转换基本信息</span>
            </div>
            <el-row>
                <el-form-item label="申请原因:" :required="true">
                    <el-input v-model="wmsUnitConversionOrderDto.reason" placeholder="请填写申请原因"
                              style="width: 500px"></el-input>
                </el-form-item>
            </el-row>
            <el-row>
                <el-form-item label="申请类型:" :required="true">
                    <template>
                        <el-select v-model="wmsUnitConversionOrderDto.orderType" placeholder="请选择" style="width: 500px">
                            <el-option
                                    v-for="item in orderTypeOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                            </el-option>
                        </el-select>
                    </template>
                </el-form-item>
            </el-row>

            <el-row>
                <el-form-item label="备注:" :required="true">
                    <el-input v-model="wmsUnitConversionOrderDto.comments" placeholder="请填写备注"
                              style="width: 500px"></el-input>
                </el-form-item>
            </el-row>
        </el-card>

        <el-card class="box-card">
            <div slot="header">
                <span>库存转换产品信息</span>
                <el-button style="float: right; padding: 3px 0" type="text" @click="addLine()">新增</el-button>
            </div>

            <template>
                <el-table :data="wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList" border
                          key="wmsUnitConversionOrderItemDtoList"
                          :header-cell-style="{'text-align':'center'}"
                          :cell-style="{'text-align':'center'}">
                    <el-table-column
                            label="序号"
                            type="index"
                            width="50"
                            :resizable="false">
                    </el-table-column>
                    <el-table-column
                            label="需转换订货号"
                            min-width="20%"
                            :resizable="false">
                        <template slot-scope="scope">
                            <el-form-item label-width="0">
                                <el-input v-model="scope.row.sourceSkuNo" style="width: 100px;"
                                          :disabled="true"></el-input>
                                <el-button style="padding: 3px 0" type="primary " @click="addSku(scope.$index, 1)">添加
                                </el-button>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="需转换商品名称"
                            min-width="30%"
                            :resizable="false">
                        <template slot-scope="scope">
                            <el-link type="primary" :underline="false" @click="viewSkuInfo(scope.row.sourceSkuId)">
                                {{scope.row.sourceGoodsName }}
                            </el-link>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="sourceUnitName"
                            label="需转换单位"
                            min-width="8%"
                            :resizable="false">
                    </el-table-column>
                    <el-table-column
                            label="成本单价"
                            min-width="15%"
                            :resizable="false">
                        <template slot-scope="scope">
                            <el-form-item label-width="0">
                                <el-input v-model="scope.row.sourcePrice"
                                          @input="handleSourcePrice(scope.row)"
                                          style="width: 100%;">
                                </el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="stockNum"
                            label="库存数量"
                            min-width="10%"
                            :resizable="false">
                    </el-table-column>
                    <el-table-column
                            label="需转换数量"
                            min-width="10%"
                            :resizable="false">
                        <template slot-scope="scope">
                            <el-form-item label-width="0">
                                <el-input v-model="scope.row.sourceNum"
                                          @input="handleSourceNum(scope.row)"
                                          style="width: 100%;">
                                </el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="目标订货号"
                            min-width="20%"
                            :resizable="false">
                        <template slot-scope="scope">
                            <el-form-item label-width="0">
                                <el-input v-model="scope.row.targetSkuNo" :disabled="true"
                                          style="width: 100px;">
                                </el-input>
                                <el-button style="padding: 3px 0" type="primary " @click="addSku(scope.$index, 2)">添加
                                </el-button>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="目标商品名称"
                            min-width="30%"
                            :resizable="false">
                        <template slot-scope="scope">
                            <el-link type="primary" :underline="false" @click="viewSkuInfo(scope.row.targetSkuId)">
                                {{scope.row.targetGoodsName }}
                            </el-link>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="targetUnitName"
                            label="目标单位"
                            min-width="8%"
                            :resizable="false">
                    </el-table-column>
                    <el-table-column
                            label="转换后成本单价"
                            min-width="15%"
                            :resizable="false">
                        <template slot-scope="scope">
                            <el-form-item label-width="0">
                                <el-input v-model="scope.row.targetPrice"
                                          @input="handleTargetPrice(scope.row)"
                                          style="width: 100%;">
                                </el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="转换后数量"
                            min-width="10%"
                            :resizable="false">
                        <template slot-scope="scope">
                            <el-form-item label-width="0">
                                <el-input v-model="scope.row.targetNum"
                                          @input="handleTargetNum(scope.row)"
                                          style="width: 100%;">
                                </el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="税率"
                            min-width="20%"
                            :resizable="false">
                        <template slot-scope="scope">
                            <el-form-item label-width="0">
                                <el-select v-model="scope.row.taxRate" placeholder="请选择">
                                    <el-option
                                            v-for="item in taxRateOptions"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                            fixed="right"
                            label="操作"
                            min-width="8%"
                            :resizable="false">
                        <template slot-scope="scope">
                            <el-button @click="handleDelete(scope.$index)" type="text" size="small" style="color: red">
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
        </el-card>

        <el-form-item style="text-align: center; margin-top: 20px">
            <el-button type="primary" size="small" @click="submitForm('form')">提交并审核</el-button>
        </el-form-item>
    </el-form>
</div>

<script src="${pageContext.request.contextPath}/static/api/wmsunitconversionorder/wmsUnitConversionOrder.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    const wmsUnitConversionOrderId = '${wmsUnitConversionOrderId}';

    new Vue({
        el: '#app',
        data() {
            return {
                taxRateOptions: [
                    {value: 1, label: '0%'},
                    {value: 2, label: '1%'},
                    {value: 3, label: '3%'},
                    {value: 4, label: '5%'},
                    {value: 5, label: '6%'},
                    {value: 6, label: '9%'},
                    {value: 7, label: '13%'}
                ],

                orderTypeOptions: [
                    {value: 1, label: '单位转换'},
                    {value: 2, label: '主机配件'},
                    {value: 3, label: '组合产品'}
                ],
                labelPosition: 'right',
                wmsUnitConversionOrderDto: {
                    reason: '',
                    orderType: null,
                    comments: '',
                    wmsUnitConversionOrderItemDtoList: []
                },
                // 区分入口是 source还是target
                source: 0
            }
        },

        mounted() {
            loadingApp();
        },

        created() {
            if (wmsUnitConversionOrderId) {
                // 编辑
                viewWmsUnitConversionOrderDetail({"wmsUnitConversionOrderId": wmsUnitConversionOrderId}).then(res => {
                    this.wmsUnitConversionOrderDto = res.data.data;
                });
            }
        },

        methods: {
            addLine() {
                let line = {
                    taxRate: 7,
                    sourceSkuNo: '',
                    targetSkuNo: ''
                };
                this.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList.push(line)
            },

            handleDelete(index) {
                this.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList.splice(index, 1);
            },

            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },

            // 查看商品信息
            viewSkuInfo(id) {
                openTab("商品信息", '/goods/goods/viewbaseinfo.do?goodsId=' + id);
            },

            handleSourcePrice(row) {
                row.sourcePrice = this.limitSourcePrice(row.sourcePrice);
            },

            limitSourcePrice(returnNum) {
                let str = returnNum;
                let len1 = str.substr(0, 1);
                let len2 = str.substr(1, 1);
                //如果第一位是0，第二位不是点，就用数字把点替换掉
                if (str.length > 1 && len1 == 0 && len2 != '.') {
                    str = str.substr(1, 1);
                }
                //第一位不能是.
                if (len1 == '.') {
                    str = '';
                }
                //限制只能输入一个小数点
                if (str.indexOf('.') != -1) {
                    let str_ = str.substr(str.indexOf('.') + 1);
                    if (str_.indexOf('.') != -1) {
                        str = str.substr(0, str.indexOf('.') + str_.indexOf('.') + 1);
                    }
                }
                //正则替换
                str = str.replace(/[^\d\.]+/g, ''); // 保留数字和小数点
                str = str.replace(/(\.\d{2})\d*/, '$1'); // 小数点后只能输两位
                return str;
            },

            handleSourceNum(row) {
                row.sourceNum = this.limitSourceNum(row.sourceNum);
            },

            limitSourceNum(returnNum) {
                if (returnNum != undefined) {
                    returnNum = returnNum.replace(/[^0-9]/g, '');// 只能输入数字
                    returnNum = returnNum.replace(/^(0+)|[^\d]+/g, '');// 第一位0开头，0后面为数字，则过滤掉，取后面的数字
                }
                return returnNum;
            },

            handleTargetPrice(row) {
                row.targetPrice = this.limitTargetPrice(row.targetPrice);
            },

            limitTargetPrice(returnNum) {
                let str = returnNum;
                let len1 = str.substr(0, 1);
                let len2 = str.substr(1, 1);
                //如果第一位是0，第二位不是点，就用数字把点替换掉
                if (str.length > 1 && len1 == 0 && len2 != '.') {
                    str = str.substr(1, 1);
                }
                //第一位不能是.
                if (len1 == '.') {
                    str = '';
                }
                //限制只能输入一个小数点
                if (str.indexOf('.') != -1) {
                    let str_ = str.substr(str.indexOf('.') + 1);
                    if (str_.indexOf('.') != -1) {
                        str = str.substr(0, str.indexOf('.') + str_.indexOf('.') + 1);
                    }
                }
                //正则替换
                str = str.replace(/[^\d\.]+/g, ''); // 保留数字和小数点
                str = str.replace(/(\.\d{2})\d*/, '$1'); // 小数点后只能输两位
                return str;
            },

            handleTargetNum(row) {
                row.targetNum = this.limitTargetNum(row.targetNum);
            },

            limitTargetNum(returnNum) {
                if (returnNum != undefined) {
                    returnNum = returnNum.replace(/[^0-9]/g, '');// 只能输入数字
                    returnNum = returnNum.replace(/^(0+)|[^\d]+/g, '');// 第一位0开头，0后面为数字，则过滤掉，取后面的数字
                }
                return returnNum;
            },

            addSku(index, source) {
                let that = this;
                layer.open({
                    type: 2,
                    shadeClose: false,
                    area: ['80%', '80%'],
                    title: '添加商品',
                    content: '/ezadmin/list/list-unitConversionSkuAdd',
                    btn: ['确定', '取消'],
                    yes: function (i, layero) {
                        let iframeWin = window[layero.find('iframe')[0]['name']];
                        let ids = iframeWin.getJsonCheckIds();
                        let lines = JSON.parse(iframeWin.getJsonCheckIdAndNames())[0];

                        if (ids == '[]') {
                            alert('请选择');
                        } else {
                            let chosenSkuList = [];
                            that.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList.forEach(sourceItem => {
                                chosenSkuList.push(sourceItem.sourceSkuId);
                                chosenSkuList.push(sourceItem.targetSkuId);
                            });
                            let stockNum = 0;
                            getStockNum({"skuNo": lines.SKU_NO}).then(res=> {
                                stockNum = res.data.data;
                                if (chosenSkuList.indexOf(lines.SKU_ID) == -1) {
                                    if (source === 1) {
                                        that.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList[index].sourceSkuId = lines.SKU_ID;
                                        that.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList[index].sourceSkuNo = lines.SKU_NO;
                                        that.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList[index].sourceGoodsName = lines.SKU_NAME;
                                        that.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList[index].sourceUnitId = lines.UNIT_ID;
                                        that.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList[index].sourceUnitName = lines.UNIT_NAME;
                                        that.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList[index].stockNum = stockNum
                                    }
                                    if (source === 2) {
                                        that.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList[index].targetSkuId = lines.SKU_ID;
                                        that.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList[index].targetSkuNo = lines.SKU_NO;
                                        that.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList[index].targetGoodsName = lines.SKU_NAME;
                                        that.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList[index].targetUnitId = lines.UNIT_ID;
                                        that.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList[index].targetUnitName = lines.UNIT_NAME;
                                    }
                                    layer.close(i)
                                } else {
                                    layer.alert("该商品已在列表，不允许重复添加！");
                                    return false;
                                }
                            });

                        }
                    }
                });
            },

            submitForm(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        let checkPass = true;
                        if (this.wmsUnitConversionOrderDto.reason == null || this.wmsUnitConversionOrderDto.reason === "") {
                            this.$alert('申请原因为空，不允许提交！', '信息', {
                                confirmButtonText: '确定'
                            });
                            checkPass = false;
                            return false
                        } else {
                            if (this.wmsUnitConversionOrderDto.reason.length > 255) {
                                this.$alert('申请原因最多输入255个字符，不允许提交！', '信息', {
                                    confirmButtonText: '确定'
                                });
                                checkPass = false;
                                return false
                            }
                        }

                        if (this.wmsUnitConversionOrderDto.orderType == null ) {
                            this.$alert('申请类型为空，不允许提交！', '信息', {
                                confirmButtonText: '确定'
                            });
                            checkPass = false;
                            return false
                        }

                        if (this.wmsUnitConversionOrderDto.comments == null || this.wmsUnitConversionOrderDto.comments === "") {
                            this.$alert('备注为空，不允许提交！', '信息', {
                                confirmButtonText: '确定'
                            });
                            checkPass = false;
                            return false
                        } else {
                            if (this.wmsUnitConversionOrderDto.comments.length > 255) {
                                this.$alert('备注最多输入255个字符，不允许提交！', '信息', {
                                    confirmButtonText: '确定'
                                });
                                checkPass = false;
                                return false
                            }
                        }

                        if (this.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList.length == 0) {
                            this.$alert('请选择库存转换产品信息！', '信息', {
                                confirmButtonText: '确定'
                            });
                            checkPass = false;
                            return false
                        }

                        // 校验 成本单价*需转换数量=转换后成本单价*转换后数量
                        this.wmsUnitConversionOrderDto.wmsUnitConversionOrderItemDtoList.forEach(item => {
                            if (item.sourceSkuNo == null || item.sourceSkuNo === "") {
                                this.$alert('需转换订货号为空，不允许提交！', '信息', {
                                    confirmButtonText: '确定'
                                });
                                checkPass = false;
                                return false
                            }
                            
                            if (item.sourcePrice == null || item.sourcePrice === "") {
                                this.$alert('成本单价为空，不允许提交！', '信息', {
                                    confirmButtonText: '确定'
                                });
                                checkPass = false;
                                return false
                            } else {
                                if (item.sourcePrice == 0) {
                                    // 不可以用=== 注意！
                                    this.$alert('成本单价为0，不允许提交！', '信息', {
                                        confirmButtonText: '确定'
                                    });
                                    checkPass = false;
                                    return false
                                }
                            }

                            if (item.sourceNum == null || item.sourceNum === "") {
                                this.$alert('需转换数量为空，不允许提交！', '信息', {
                                    confirmButtonText: '确定'
                                });
                                checkPass = false;
                                return false
                            } else {
                                if (item.sourceNum == 0) {
                                    this.$alert('需转换数量为0，不允许提交！', '信息', {
                                        confirmButtonText: '确定'
                                    });
                                    checkPass = false;
                                    return false
                                }
                            }

                            if (item.targetSkuNo == null || item.targetSkuNo === "") {
                                this.$alert('目标订货号为空，不允许提交！', '信息', {
                                    confirmButtonText: '确定'
                                });
                                checkPass = false;
                                return false
                            }

                            if (item.targetPrice == null || item.targetPrice === "") {
                                this.$alert('转换后成本单价为空，不允许提交！', '信息', {
                                    confirmButtonText: '确定'
                                });
                                checkPass = false;
                                return false
                            } else {
                                if (item.targetPrice == 0) {
                                    this.$alert('转换后成本单价为0，不允许提交！', '信息', {
                                        confirmButtonText: '确定'
                                    });
                                    checkPass = false;
                                    return false
                                }
                            }

                            if (item.targetNum == null || item.targetNum === "") {
                                this.$alert('转换后数量为空，不允许提交！', '信息', {
                                    confirmButtonText: '确定'
                                });
                                checkPass = false;
                                return false
                            } else {
                                if (item.targetNum == 0) {
                                    this.$alert('转换后数量为0，不允许提交！', '信息', {
                                        confirmButtonText: '确定'
                                    });
                                    checkPass = false;
                                    return false
                                }
                            }

                            if ((Number(item.sourcePrice) * Number(item.sourceNum)).toFixed(2) != (Number(item.targetPrice) * Number(item.targetNum)).toFixed(2)) {
                                this.$alert('转换前后成本价不一致，不允许提交！', '信息', {
                                    confirmButtonText: '确定'
                                });
                                checkPass = false;
                                return false
                            }
                        });

                        if (checkPass) {
                            if (wmsUnitConversionOrderId) {
                                // 更新
                                saveEditWmsUnitConversionOrder(this.wmsUnitConversionOrderDto).then(res => {
                                    if (res.data.success) {
                                        openTab("单位转换单详情", '/wmsUnitConversion/detail.do?wmsUnitConversionOrderId=' + wmsUnitConversionOrderId);
                                        this.closeThis()
                                    } else {
                                        this.$message({
                                            message: res.data.message,
                                            type: 'error',
                                        });
                                    }
                                })
                            } else {
                                // 新增
                                saveAddWmsUnitConversionOrder(this.wmsUnitConversionOrderDto).then(res => {
                                    if (res.data.success) {
                                        openTab("单位转换单详情", '/wmsUnitConversion/detail.do?wmsUnitConversionOrderId=' + res.data.data);
                                        this.closeThis()
                                    } else {
                                        this.$message({
                                            message: res.data.message,
                                            type: 'error',
                                        });
                                    }
                                })
                            }
                        }
                    } else {
                        return false;
                    }
                });
            }
        },
    });
</script>

<style>
    .el-card__header {
        height: 34px;
        background-color: #c5ddfb;
        box-sizing: border-box;
        padding-left: 15px;
        font-weight: 700;
        padding-top: 9px;
        padding-bottom: 31px;
        color: #303133;
    }

    .el-form-item__label {
        padding-right: 10px;
    }

</style>