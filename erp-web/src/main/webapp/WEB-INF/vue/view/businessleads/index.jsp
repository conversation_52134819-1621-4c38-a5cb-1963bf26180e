<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<div id="app" style="display: none;">

    <vxe-grid ref='xGrid' v-bind="gridOptions" @toolbar-button-click="toolbarButtonClickEvent">

        <%--    表单搜索    --%>
        <template #form_leads_no="{ data }">
            <vxe-input v-model="data.leadsNo" placeholder="请输入" clearable></vxe-input>
        </template>

        <template #form_type="{ data }">
            <vxe-select v-model="data.clueType" clearable placeholder="全部">
                <vxe-option value="" label="全部"></vxe-option>
                <vxe-option value="391" label="总机询价"></vxe-option>
                <vxe-option value="394" label="自主询价"></vxe-option>
            </vxe-select>
        </template>

        <template #form_status="{ data }">
            <vxe-select v-model="data.status" clearable placeholder="全部">
                <vxe-option value="" label="全部"></vxe-option>
                <vxe-option value="1" label="有效线索"></vxe-option>
                <vxe-option value="0" label="无效线索"></vxe-option>
            </vxe-select>
        </template>

        <template #form_follow_status="{ data }">
            <vxe-select v-model="data.followStatusList" clearable placeholder="全部" multiple>
                <vxe-option value="1" label="未处理"></vxe-option>
                <vxe-option value="2" label="跟进中"></vxe-option>
                <vxe-option value="3" label="已关闭"></vxe-option>
                <vxe-option value="4" label="已商机"></vxe-option>
            </vxe-select>
        </template>

        <template #form_contract_way="{ data }">
            <vxe-input v-model.trim="data.contactWay" placeholder="联系人、手机号、电话、其他联系方式" clearable></vxe-input>
        </template>

        <template #form_trader_name="{ data }">
            <el-select
                    v-model="data.traderName"
                    filterable
                    clearable
                    remote
                    reserve-keyword
                    placeholder="请输入"
                    :remote-method="remoteMethod"
                    :loading="loading"
                    @blur="selectBlur($event)"
                    style="width: 100%;">
                <el-option
                        v-for="item in traderOptions"
                        :key="item.traderId"
                        :label="item.traderName"
                        :value="item.traderId"
                        :disabled="!item.belong"
                        @click.native="clickTrader(item)">
                    <span style="float: left">{{ item.traderName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.saleName }}</span>
                </el-option>
            </el-select>
        </template>

        <template #form_goods_info="{ data }">
            <vxe-input v-model="data.goodsInfo" placeholder="请输入产品名称" clearable></vxe-input>
        </template>

        <template #form_area="{ data }">
            <el-cascader
                    style="width: 100%; height: 36px"
                    v-model="data.areaIdList"
                    ref="cascader"
                    :options="areaOptions"
                    :props="{multiple: true }"
                    collapse-tags
                    @change="getCheckedNodes"
                    clearable></el-cascader>
        </template>

        <template #form_time="{ data }">
            <el-select v-model="timeType" size="small"  style="width:23%;">
                <el-option
                        v-for="item in items"
                        :key="item.type"
                        :label="item.nameTime"
                        :value="item.type"/>
            </el-select>
            <el-date-picker size="small"  style="width: 76%;"
                            v-model="data.time"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']">
            </el-date-picker>
        </template>

        <template #form_next_communication="{ data }">
            <el-date-picker size="small" style="width: 100%;"
                            v-model="data.nextCommunicationDates"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']">
            </el-date-picker>
        </template>

        <template #form_communicate="{ data }">
            <vxe-input v-model="data.communicateContent" placeholder="请输入" clearable></vxe-input>
        </template>

        <template #form_belonger="{ data }">

            <el-select v-model="data.belongerIdList" filterable multiple placeholder="请选择" prop="belongerId"
                       style="width: 100%;">
                <el-option
                        v-for="item in belongerOptions"
                        :key="item.userId"
                        :label="item.username"
                        :value="item.userId">
                </el-option>
            </el-select>

        </template>

        <template #form_entrances="{ data }">

            <el-select v-model="data.entrances" filterable clearable placeholder="请选择" prop="entrances"
                       style="width: 100%;">
                <el-option
                        v-for="item in entrancesOptions"
                        :key="item.sysOptionDefinitionId"
                        :label="item.title"
                        :value="item.sysOptionDefinitionId">
                </el-option>
            </el-select>

        </template>

        <template #form_functions="{ data }">

            <el-select v-model="data.functions" filterable clearable placeholder="请选择" prop="functions"
                       style="width: 100%;">
                <el-option
                        v-for="item in functionsOptions"
                        :key="item.sysOptionDefinitionId"
                        :label="item.title"
                        :value="item.sysOptionDefinitionId">
                </el-option>
            </el-select>

        </template>

<%--        <template #form_tag="{ data }">--%>
<%--            <el-select v-model="data.tagIdList" placeholder="请选择" multiple clearable  style="width: 100%;">--%>
<%--                <el-option v-for="tag in tagList" :key="tag.id" :value="tag.id" :label="tag.name"></el-option>--%>
<%--            </el-select>--%>
<%--        </template>--%>

<%--        <template #form_quick_search="{ data }">--%>
<%--            <el-select v-model="data.quickSearch" clearable placeholder="全部"  style="width: 100%;">--%>
<%--                <el-option value="" label="全部"></el-option>--%>
<%--                <el-option value="1" label="3天未跟进的线索"></el-option>--%>
<%--            </el-select>--%>
<%--        </template>--%>
            
        <template #form_inquiry="{ data }">
            <vxe-select v-model="data.inquiry" clearable placeholder="全部">
                <vxe-option value="" label="全部"></vxe-option>
                <vxe-option value="4001" label="即时通讯"></vxe-option>
                <vxe-option value="4002" label="电话"></vxe-option>
                <vxe-option value="4003" label="留言"></vxe-option>
            </vxe-select>
        </template>
            
        <template #form_assign_or_not="{ data }">
            <vxe-select v-model="data.assignOrNot" clearable placeholder="全部">
                <vxe-option value="-1" label="全部"></vxe-option>
                <vxe-option value="1" label="否"></vxe-option>
                <vxe-option value="2" label="是"></vxe-option>
            </vxe-select>
        </template>

        <template #form_content="{ data }">
            <vxe-input v-model="data.content" placeholder="请输入" clearable></vxe-input>
        </template>
            
        <template #form_channel="{ data }">
            <el-cascader
                    style="width: 100%; height: 36px"
                    v-model="data.channelIdList"
                    ref="cascaderChannel"
                    :options="channelOptions"
                    :props="{multiple: true }"
                    collapse-tags
                    @change="getChannelCheckedNodes"
                    clearable></el-cascader>
        </template>
            
        <%--     表格数据   --%>
        <template #leads_no="{ row }">
            <el-link type="primary" @click="toDetail(row)">
                <span v-if="row.mergeStatus == 2" style="color: red;">[合]</span> {{ row.leadsNo}}
            </el-link>
        </template>

        <template #phone="{ row }">
            <span v-if="row.phone"><i class="el-icon-phone" style="color: #409eff;"
                                      @click="call(row,row.phone)"></i>{{ row.phone}}</span>
        </template>

        <template #telephone="{ row }">
            <span v-if="row.telephone"> <i class="el-icon-phone" style="color: #409eff;"
                                           @click="call(row,row.telephone)"></i>{{ row.telephone }}</span>
        </template>

        <template #trader_name="{ row }">
            <span v-if="row.traderId == 0">{{row.traderName}}</span>
            <el-link type="primary" v-if="row.traderId != 0" @click="toTrader(row)">{{row.traderName}}</el-link>
        </template>

        <template #goods_info="{ row }">
            <span>{{ getGoodsInfo(row.goodsInfo)}}
                <br>
                {{getGoodsInfoNext(row.goodsInfo)}}
                </span>
        </template>

        <template #address="{ row }">
            <span>{{getAddress(row)}}</span>
        </template>

        <template #status_columns="{ row }">
            <span v-if="row.status == null"></span>
            <span v-if="row.status === 0">无效线索</span>
            <span v-if="row.status === 1">有效线索</span>
        </template>

        <template #clue_type="{ row }">
            <span v-if="row.clueType == null"></span>
            <span v-if="row.clueType === 391">总机询价</span>
            <span v-if="row.clueType === 394">自主询价</span>
        </template>

        <template #inquiry="{ row }">
            <span v-if="row.inquiry == null"></span>
            <span v-if="row.inquiry === 4001">即时通讯</span>
            <span v-if="row.inquiry === 4002">电话</span>
            <span v-if="row.inquiry === 4003">留言</span>
        </template>

        <template #tags="{ row }">
            <el-tag v-for="item in row.tags"
                    :key="item.id"
                    :style="{color: item.cssClass,borderColor:item.cssClass}"
                    style="margin-right: 5px"
                    effect="plain">
                {{ item.name }}
            </el-tag>
        </template>

        <template #communication_record="{ row }">
            <span v-if="row.communicateRecordDto==null">-</span>
            <i v-else class="el-icon-document-copy" style="color: #409eff;" @click="getCommunicationRecord(row)"></i>
            <span v-if="row.communicateRecordDto!=null && row.communicateRecordDto.nextContactDate!=null ">下次沟通时间：{{parseTime(row.communicateRecordDto.nextContactDate, '{y}-{m}-{d}')}}</span>
        </template>

        <template #follow_status="{ row }">
            <span :style="{color: buildFollowStatus(row).color}">{{ buildFollowStatus(row).value }}</span>
        </template>

        <template #operate="{ row }">
            <vxe-button @click="unTop(row)" v-if="row.customDataOperDto" size="small" type="text" status="info">
                取消置顶
            </vxe-button>
            <vxe-button @click="top(row)" v-else size="small" type="text" status="primary">置顶</vxe-button>
        </template>

    </vxe-grid>

<%--    <el-dialog title="自定义线索标签" :visible.sync="outerVisible" width="60%" center>--%>
<%--        <el-form :model="tagForm">--%>
<%--            <el-row>--%>
<%--                <el-col :span="5">--%>
<%--                    <el-form-item label="默认线索标签:" label-width="150px">--%>
<%--                    </el-form-item>--%>
<%--                </el-col>--%>
<%--                <el-col :span="8">--%>
<%--                    <span>暂无默认线索标签</span>--%>
<%--                </el-col>--%>
<%--            </el-row>--%>

<%--            <template v-for="(tagItem, index) in tagForm">--%>
<%--                <el-row>--%>
<%--                    <el-col :span="5">--%>
<%--                        <el-form-item label-width="150px">--%>
<%--                        <span slot="label">--%>
<%--                           <span v-if="index === 0">自定义线索标签：</span>--%>
<%--                           <span v-else>&nbsp;</span>--%>
<%--                        </span>--%>
<%--                            <el-button type="primary" icon="el-icon-plus" circle size="small"--%>
<%--                                       v-if="index === tagForm.length-1" @click="addTagItem"></el-button>--%>
<%--                        </el-form-item>--%>
<%--                    </el-col>--%>
<%--                    <el-col :span="8">--%>
<%--                        <el-input v-model="tagItem.name" maxlength="5"></el-input>--%>
<%--                    </el-col>--%>
<%--                    <el-col :span="2">--%>
<%--                        <el-color-picker v-model="tagItem.cssClass" size="medium"--%>
<%--                                         :predefine="predefineColors"></el-color-picker>--%>
<%--                    </el-col>--%>
<%--                    <el-col :span="4">--%>
<%--                        <div class="grid-content bg-purple"></div>--%>
<%--                    </el-col>--%>
<%--                    <el-col :span="4">--%>
<%--                        <span class="demonstration">样式参考:</span>--%>
<%--                        <el-tag v-if="tagItem.name" :style="{color: tagItem.cssClass,borderColor:tagItem.cssClass}"--%>
<%--                                effect="plain">{{tagItem.name}}--%>
<%--                        </el-tag>--%>
<%--                    </el-col>--%>
<%--                    <el-col :span="2">--%>
<%--                        <el-button type="danger" icon="el-icon-delete" circle size="small" v-if="tagForm.length >= 1"--%>
<%--                                   @click="deleteTag(index)"></el-button>--%>
<%--                    </el-col>--%>
<%--                </el-row>--%>
<%--            </template>--%>

<%--        </el-form>--%>


<%--        <div slot="footer" class="dialog-footer">--%>
<%--            <el-button @click="outerVisible = false">取消</el-button>--%>
<%--            <el-button type="primary" @click="saveTag">确定</el-button>--%>
<%--        </div>--%>
<%--    </el-dialog>--%>

    <el-dialog title="分配线索" :visible.sync="assignDialog" width="60%" center>
        <el-form>
            <el-form-item label="新归属人:" label-width="100px" style="height: 200px">
                <el-select v-model="assignUserId" placeholder="请输入并选择归属人" prop="assignUserId" filterable
                           style="width: 50%;">
                    <el-option
                            v-for="item in belongerOptions"
                            :key="item.userId"
                            :label="item.username"
                            :value="item.userId">
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>

        <div slot="footer" class="dialog-footer">
            <el-button @click="assignDialog = false">取消</el-button>
            <el-button type="primary" @click="assignUser">确定</el-button>
        </div>
    </el-dialog>

    <el-dialog title="沟通记录" :visible.sync="communicationRecordDialog" width="60%" center top="20px"
               :append-to-body="true" :destroy-on-close="true">
        <div style="margin: 10px">
            <el-row>
                <el-button type="text" @click="addCommunicateRecord">+ 新增沟通记录</el-button>
            </el-row>

            <el-row v-for="item in communicateRecordList" :key="item.communicateRecordId">
                <div style="border: 1px solid #d7dae2;border-radius: 0px">
                    <el-row>
                        <div style="margin: 10px;">
                            <el-col :span="12">
                                <div>
                                    {{ item.creatorName }}
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div style="float: right">
                                    {{parseTime(item.begintime, '{y}-{m}-{d} {h}:{i}')}}
                                </div>
                            </el-col>
                        </div>
                    </el-row>

                    <div>
                        <div style="margin: 20px">
                            <div>
                                <el-descriptions :colon="false">
                                    <el-descriptions-item>
                                        {{ item.communicateGoalName}}{{ item.communicateModeName}}{{ item.contactContent}}{{ item.contentSuffix}}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </div>
                            <div>
                                <el-descriptions>
                                    <el-descriptions-item label="下次沟通时间"
                                                          v-if="item.nextContactDate != null">
                                        {{parseTime(item.nextContactDate, '{y}-{m}-{d}')}}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </div>
                            <div>
                                <el-descriptions>
                                    <el-descriptions-item label="下次沟通内容"
                                                          v-if="item.nextContactContent != null">
                                        {{ item.nextContactContent }}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </div>
                        </div>
                    </div>

                    <el-row>
                        <div style="margin: 20px">
                            <el-divider></el-divider>
                            <el-col :span="1">
                                <div v-if="item.coid !=null">
                                    <img @click="playrecord(item.coidUri)"
                                         src="${pageContext.request.contextPath}/static/vue/images/trumpet.png">
                                </div>
                            </el-col>
                            <el-col :span="10">
                                <div>
                                    <p v-if="item.traderContactId != null">
                                        {{ item.contactName }}&nbsp;{{ item.contactDepartment
                                        }}&nbsp;{{ item.contactPosition }}
                                    </p>
                                    <p v-else style="height: 30px">
                                        {{ item.contact }}
                                    </p>
                                </div>
                            </el-col>
                        </div>
                    </el-row>
                </div>
            </el-row>


            <el-row v-if="communicateRecordTotalNum > 3">
                <div style="float: right">
                    <el-button v-if="showMoreFlag" type="text" @click="getMoreCommunicateRecord">查看更多跟进情况
                    </el-button>
                </div>

            </el-row>
        </div>
    </el-dialog>

    <el-dialog title="新增沟通记录" :visible.sync="dialogFormVisible" top="20px"
               :close-on-click-modal="false" :close-on-press-escape="false">
        <el-form :model="communicateRecordDto" ref="communicateRecordFrom" :rules="rules">
            <div v-show="businessLeads.traderId!= 0">
                <el-form-item label="客户名称:" label-width="150px">
                    {{businessLeads.traderName}}
                </el-form-item>

                <el-form-item label="联系人:" label-width="150px" prop="traderContactNameView" :rules="this.businessLeads.traderId !== 0 ? rules.traderContactNameView: [{required: false}]">
                    <el-select
                            v-model="communicateRecordDto.traderContactNameView"
                            filterable
                            clearable
                            reserve-keyword
                            placeholder="请选择联系人"
                            style="width: 300px;"
                            @clear="traderContactClearComm"
                    >
                        <el-option
                                v-for="item in traderConcatDatas"
                                :key="item.traderContactId"
                                :label="item.name"
                                :value="item.traderContactId"
                                @click.native="traderConcatSelectComm(item)"
                        >
                            <span style="float: left">{{ item.name + ' ' + item.mobile + ' ' + item.telephone }}</span>
                        </el-option>

                    </el-select>
                    <span class="title-click  bg-light-bule pop-new-data bt-small mr10 bt-bg-style J-add-prod"
                          id="concat" onclick="addTraderContract()"
                          style="margin-left:24px;color: #3384ef;padding-right: 10px;cursor: pointer;height: 34px;line-height: 34px;"
                          :layerParams="'{&quot;width&quot;:&quot;50%&quot;,&quot;height&quot;:&quot;75%&quot;,&quot;title&quot;:&quot;添加联系人&quot;,&quot;link&quot;:&quot;/orderstream/saleorder/addContact.do?indexId=3&amp;traderId='+businessLeads.traderId+'&quot;}'"><span
                            v-show="businessLeads.traderId!=null">添加联系人</span></span>

                </el-form-item>

            </div>

            <div v-show="businessLeads.traderId== 0">
                <el-form-item label="联系人:" label-width="150px" prop="contact" :rules="businessLeads.traderId === 0 ? rules.contact: [{required: false}]">
                    <el-input v-model="communicateRecordDto.contact" placeholder="请输入联系人"
                              style="width: 300px;"></el-input>
                </el-form-item>

                <el-form-item label="联系电话:" label-width="150px" prop="contactMob" :rules="businessLeads.traderId === 0 ? rules.contactMob: [{required: false}]">
                    <el-input v-model="communicateRecordDto.contactMob" placeholder="请输入联系电话"
                              style="width: 300px;"></el-input>
                </el-form-item>
            </div>

            <el-form-item label="沟通时间:" label-width="150px" prop="time">
                <el-date-picker
                        v-model="communicateRecordDto.time"
                        type="datetimerange"
                        value-format="timestamp"
                        range-separator="至"
                        start-placeholder="沟通开始时间"
                        end-placeholder="沟通结束时间">
                </el-date-picker>
            </el-form-item>

            <el-form-item label="沟通内容:" label-width="150px" prop="contentSuffix">
                <el-input
                        v-model="communicateRecordDto.contentSuffix"
                        type="textarea"
                        placeholder="沟通内容最多输入200个汉字"
                        style="width: 500px"
                        maxlength="200"
                        rows="6"
                >
                </el-input>
            </el-form-item>

            <el-form-item label="下次沟通时间:" label-width="150px" prop="nextContactDate">
                <el-date-picker
                        v-model="communicateRecordDto.nextContactDate"
                        type="date"
                        :disabled="communicateRecordDto.noneNextDate"
                        placeholder="选择日期时间"
                        default-time="00:00:00">
                </el-date-picker>
                <el-checkbox v-model="communicateRecordDto.noneNextDate"
                             @change="communicateRecordNextBind">暂无下次沟通记录
                </el-checkbox>
            </el-form-item>

            <el-form-item label="下次沟通内容:" label-width="150px" prop="nextContactContent">
                <el-input
                        v-model="communicateRecordDto.nextContactContent"
                        type="textarea"
                        placeholder=""
                        style="width: 500px"
                        maxlength="200"
                        rows="6"
                >
                </el-input>
            </el-form-item>

            <el-form-item label="备注:" label-width="150px" prop="comments">
                <el-input
                        v-model="communicateRecordDto.comments"
                        type="textarea"
                        placeholder=""
                        style="width: 500px"
                        maxlength="200"
                        rows="3"
                >
                </el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: center">
            <el-button @click="cancelCommunicateRecord('communicateRecordFrom')">取 消</el-button>
            <el-button type="primary" @click="submitCommunicateRecord('communicateRecordFrom')">确 定</el-button>
        </div>
    </el-dialog>

</div>

<script src="${pageContext.request.contextPath}/static/api/trader/businessLeads.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/communicateRecord.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/traderContact.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomer.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/system/customTag.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/system/region.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/system/user.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">

    let vm0 = null
    const sendThis0 = (_this) => {
        vm0 = _this;
    }

    new Vue({
        el: '#app',

        data() {
            var dateTime = new Date().getTime();
            var dateTimeEnd = new Date(dateTime + 1000 * 60 * 2).getTime();
            return {
                rules: {
                    'nextContactDate': [
                        {required: true, message: '请输入下次沟通时间', trigger: 'blur'}
                    ],
                    'time': [
                        {type: 'array', required: true, message: '请输入沟通时间', trigger: ['blur', 'change']}
                    ],
                    'contentSuffix': [
                        {required: true, message: '请输入沟通内容', trigger: 'blur'}
                    ],
                    'contact': [
                        {required: true, trigger: 'blur' ,validator: this.validateName},
                    ],
                    'contactMob': [
                        {required: true, trigger: 'blur', validator: this.validatePhone},
                    ],
                    traderContactNameView: [{required: true, message: '请选择联系人', trigger: ['change', 'blur']}],
                },
                predefineColors: [
                    '#ff4500',
                    '#ff8c00',
                    '#ffd700',
                    '#90ee90',
                    '#00ced1',
                    '#1e90ff',
                    '#c71585',
                ],
                tagForm: [{
                    id: null,
                    name: null,
                    cssClass: '#409EFF',
                    type: 1
                }],
                showMoreFlag: true,
                //新增沟通记录
                communicateRecordDto: {
                    traderContactNameView: '',
                    relatedId: '',
                    traderId: '',
                    companyId: 1,
                    traderContactId: '',
                    contact: null,
                    time: [dateTime, dateTimeEnd],
                    begintime: null,
                    endtime: null,
                    noneNextDate: false,
                    isLfasr: 0,
                    contactMob: null,
                    contentSuffix: null,
                    communicateType: 4109,
                    nextContactContent: '',
                    comments: '',
                    nextContactDate: ''
                },
                businessLeads: {
                    leadsNo: '',
                    status: '',
                    invalidReason: '',
                    contact: '',
                    phone: '',
                    telephone: '',
                    traderName: '',
                    province: '',
                    city: '',
                    county: '',
                    address: '',
                    belonger: '',
                    followStatus: '',
                    goodsInfo: '',
                    tagIdList: [],
                    remark: '',
                    followPicList: [],
                    closeReason: '',
                    mergeStatus:0
                },
                dialogFormVisible: false,
                communicateRecordList: [],
                //查看更多
                communicateRecordTotalNum: '',
                communicationRecordDialog: false,
                outerVisible: false,
                innerVisible: false,
                belongerOptions: "",
                nameTime: "",
                assignDialog: false,
                assignUserId: '',
                timeType: "addTime",
                type: "",
                items: [{type: 'addTime', nameTime: '创建时间'},
                    {type: 'turnBusinessChanceTime', nameTime: '转商机时间'},
                    {type: 'closeTime', nameTime: '关闭时间'},
                    {type: 'assignTime', nameTime: '分配时间'}],
                loading: false,
                traderConcatDatas: [],
                //客户
                traderOptions: [],
                //咨询入口
                entrancesOptions: [],
                //功能
                functionsOptions: [],
                //地区三级联动
                areaOptions: [],
                //渠道二级联动
                channelOptions: [],
                areaCheck: [],
                channelCheck: [],
                //标签
                tagList: [],
                gridOptions: {
                    height: '800px',
                    align: 'center',
                    border: true,
                    showHeaderOverflow: true,
                    keepSource: true,
                    id: 'business_leads_index',
                    rowId: 'id',
                    rowConfig: {
                        isHover: true
                    },
                    columnConfig: {
                        resizable: true
                    },
                    customConfig: {
                        storage: true,
                    },
                    pagerConfig: {
                        pageSize: 50,
                        pageSizes: [10, 50, 100, 200]
                    },

                    formConfig: {
                        titleWidth: 100,
                        titleAlign: 'right',
                        items: [
                            {
                                field: 'leadsNo',
                                title: '线索编号',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_leads_no'}
                            },
                            
                            {
                                field: 'clueType', 
                                title: '线索类型', 
                                span: 6, 
                                itemRender: {}, 
                                slots: {default: 'form_type'}
                            },

                            {
                                field: 'channelIdList',
                                title: '渠道',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_channel'}
                            },

                            {
                                field: 'inquiry',
                                title: '询价行为',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_inquiry'}
                            },

                            {
                                field: 'entrances',
                                title: '咨询入口',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_entrances'}
                            },


                            {
                                field: 'functions',
                                title: '功能',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_functions'}
                            },

                            {
                                field: 'assignOrNot',
                                title: '是否分配销售',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_assign_or_not'}
                            },

                            {
                                field: 'content',
                                title: '三级分类',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_content'}
                            },

                            {
                                field: 'status',
                                title: '线索分级',
                                folding: true,
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_status'}
                            },

                            {
                                field: 'followStatusList',
                                title: '跟进状态',
                                folding: true,
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_follow_status'}
                            },
                            {
                                field: 'contactWay',
                                title: '联系方式',
                                folding: true,
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_contract_way'}
                            },
                            {
                                field: 'traderName',
                                title: '客户名称',
                                folding: true,
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_trader_name'}
                            },
                            {
                                field: 'goodsInfo',
                                title: '产品信息',
                                folding: true,
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_goods_info'}
                            },
                            {
                                field: 'areaIdList',
                                title: '地区',
                                folding: true,
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_area'}
                            },
                            {
                                field: 'time',
                                span: 6,
                                folding: true,
                                itemRender: {},
                                slots: {default: 'form_time'}
                            },
                            {
                                field: 'nextCommunicationDates',
                                title: '下次沟通时间',
                                folding: true,
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_next_communication'}
                            },
                            {
                                field: 'communicateContent',
                                title: '沟通记录',
                                folding: true,
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_communicate'}
                            },
                            {
                                field: 'belongerIdList',
                                title: '线索归属人',
                                folding: true,
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_belonger'}
                            },
                            // {
                            //     field: 'tagIdList',
                            //     title: '线索标签',
                            //     folding: true,
                            //     span: 6,
                            //     itemRender: {},
                            //     slots: {default: 'form_tag'}
                            // },
                            // {
                            //     field: 'quickSearch',
                            //     title: '快速检索',
                            //     folding: true,
                            //     span: 6,
                            //     itemRender: {},
                            //     slots: {default: 'form_quick_search'}
                            // },

                            {
                                span: 24,
                                align: 'center',
                                collapseNode: true,
                                itemRender: {
                                    name: '$buttons',
                                    children: [{
                                        props: {
                                            type: 'submit',
                                            content: '搜索',
                                            status: 'primary'
                                        }
                                    }, {props: {type: 'reset', content: '重置'}}]
                                }
                            }
                        ]
                    },
                    toolbarConfig: {
                        buttons: [
                            {code: 'add', name: '新增线索', status: 'warning'},
                            {code: 'init', name: '初始化线索', status: 'primary'},
                            {code: 'assign', name: '分配线索', status: 'primary'},
                            // {code: 'openTag', name: '自定义线索标签', status: 'primary'}
                        ],
                        // tools: [
                        //     {code: 'add', icon: 'vxe-icon--funnel ', circle: true},
                        // ],
                        custom: true
                    },
                    proxyConfig: {
                        seq: true, // 启用动态序号代理，每一页的序号会根据当前页数变化
                        form: true, // 启用表单代理，当点击表单提交按钮时会自动触发 reload 行为
                        props: {
                            result: 'list', // 配置响应结果列表字段
                            total: 'total' // 配置响应结果总页数字段
                        },
                        ajax: {
                            // 当点击工具栏查询按钮或者手动提交指令 query或reload 时会被触发
                            query: ({page, sorts, filters, form}) => {
                                const queryParams = Object.assign({}, form);
                                if (queryParams.time != null && queryParams.time.length > 0) {
                                    queryParams.searchTime = this.timeType
                                    queryParams.startTime = queryParams.time[0]
                                    queryParams.endTime = queryParams.time[1]
                                }
                                if (queryParams.nextCommunicationDates != null && queryParams.nextCommunicationDates.length > 0) {
                                    queryParams.nextCommunicationDateStart = queryParams.nextCommunicationDates[0]
                                    queryParams.nextCommunicationDateEnd = queryParams.nextCommunicationDates[1]
                                }

                                if (queryParams.areaIdList != null && this.areaCheck) {
                                    console.log(this.areaCheck)
                                    let provinceIds = []
                                    let cityIds = []
                                    let countyIds = []
                                    if (this.areaCheck.length > 0) {
                                        this.areaCheck.forEach(node => {
                                            if (node.level === 1) {
                                                provinceIds.push(node.value)
                                            }
                                            if (node.level === 2) {
                                                cityIds.push(node.value)
                                            }
                                            if (node.level === 3) {
                                                countyIds.push(node.value)
                                            }
                                        })
                                        queryParams.provinceIdList = provinceIds
                                        queryParams.cityIdList = cityIds
                                        queryParams.countyIdList = countyIds
                                    }
                                }

                                if (queryParams.channelIdList != null && this.channelCheck) {
                                    console.log(this.channelCheck)
                                    let sourceIdList = []
                                    let communicationIdList = []
                                    if (this.channelCheck.length > 0) {
                                        this.channelCheck.forEach(node => {
                                            if (node.level === 1) {
                                                sourceIdList.push(node.value)
                                            }
                                            if (node.level === 2) {
                                                communicationIdList.push(node.value)
                                            }
                                        })
                                        queryParams.sourceIdList = sourceIdList
                                        queryParams.communicationIdList = communicationIdList
                                    }
                                }

                                let pageParams = {
                                    pageNum: page.currentPage,
                                    pageSize: page.pageSize,
                                    param: queryParams
                                }
                                return axios({
                                    url: '/businessLeads/page.do',
                                    method: 'post',
                                    data: pageParams
                                }).then(response => response.data.data);
                            }
                        }
                    },
                    columns: [
                        {type: 'checkbox', width: 50},
                        {
                            field: 'leadsNo',
                            title: '线索编号',
                            slots: {default: 'leads_no'},
                            width: 100
                        },
                        {
                            field: 'status',
                            title: '线索分级',
                            slots: {default: 'status_columns'},
                            width: 100
                        },
                        // {
                        //     field: 'tags',
                        //     title: '线索标签',
                        //     slots: {default: 'tags'},
                        //     width: 200
                        // },
                        {
                            field: 'clueType',
                            title: '线索类型',
                            slots: {default: 'clue_type'},
                            width: 100,
                        },
                        {
                            field: 'inquiry',
                            title: '询价行为',
                            slots: {default: 'inquiry'},
                            width: 100,
                        },
                        {
                            field: 'sourceName',
                            title: '渠道类型',
                            width: 200,
                        },
                        {
                            field: 'communicationName',
                            title: '渠道名称',
                            width: 200,
                        },
                        {
                            field: 'content',
                            title: '三级分类',
                            width: 200,
                        },
                        {
                            field: 'entrancesName',
                            title: '咨询入口',
                            width: 200,
                        },
                        {
                            field: 'functionsName',
                            title: '功能',
                            width: 200,
                        },
                        {
                            field: 'contact',
                            title: '客户联系人',
                            width: 200
                        },
                        {
                            field: 'phone',
                            title: '手机',
                            slots: {default: 'phone'},
                            width: 150,
                        },
                        {
                            field: 'telephone',
                            title: '电话',
                            slots: {default: 'telephone'},
                            width: 150,
                        },
                        {
                            field: 'otherContactInfo',
                            title: '其他联系方式',
                            width: 150,
                        },
                        {
                            field: 'traderName',
                            title: '客户名称',
                            slots: {default: 'trader_name'},
                            width: 200,
                        },
                        {
                            field: 'goodsInfo',
                            width: 230,
                            title: '产品信息',
                            type: 'html',
                            showOverflow: true,
                            slots: {default: 'goods_info'},
                        },
                        {
                            field: 'address',
                            title: '地区',
                            width: 180,
                            slots: {default: 'address'},
                        },
                        {
                            field: 'communicationRecord',
                            title: '沟通记录',
                            width: 250,
                            slots: {default: 'communication_record'},
                        },
                        {
                            field: 'firstFollowTime',
                            title: '首次跟进时间',
                            width: 160,
                            formatter: this.formatDate
                        },
                        {
                            field: 'addTime',
                            title: '创建时间',
                            width: 160,
                            formatter: this.formatDate
                        },
                        {
                            field: 'followStatus',
                            title: '跟进状态',
                            width: 80,
                            slots: {default: 'follow_status'},
                        },
                        {
                            field: 'belonger',
                            width: 120,
                            title: '线索归属人',
                        },
                        {
                            field: 'assignTime',
                            title: '分配时间',
                            width: 160,
                            formatter: this.formatDate
                        },
                        {
                            field: 'creatorName',
                            width: 120,
                            title: '创建人',
                        },
                        {field: 'operate', title: '操作', width: 100, slots: {default: 'operate'}, fixed: 'right'}
                    ],
                    checkboxConfig: {
                        reserve: true,
                        highlight: true,
                        range: true
                    },
                },
            }
        },

        created() {
            getAllNotDisabledUserList().then(res => {
                this.belongerOptions = res.data.data;
            });
            
            getSysOptionDefinitionByParentId(1404).then(res => {
                this.entrancesOptions = res.data.data;
            });

            getSysOptionDefinitionByParentId(1405).then(res => {
                this.functionsOptions = res.data.data;
            });

            getCascaderRegionOptions().then(res => {
                this.areaOptions = res.data.data;
            });

            getCascaderChannelOptions().then(res => {
                this.channelOptions = res.data.data;
            });

            this.getTags()

            sendThis0(this);
        },

        mounted() {
            loadingApp()
        },

        methods: {
            formatDate({cellValue}) {
                return XEUtils.toDateString(cellValue, 'yyyy-MM-dd HH:mm')
            },

            addTagItem() {
                if (this.tagForm.length < 5) {
                    this.tagForm.push({
                        id: null,
                        name: null,
                        cssClass: '#409EFF',
                        type: 1
                    })
                }
            },

            deleteTag(index) {
                let tagId = this.tagForm[index].id
                if (tagId == null) {
                    if (this.tagForm.length > 1) {
                        this.tagForm.splice(index, 1)
                    } else {
                        this.tagForm[0].name = null
                        this.tagForm[0].cssClass = '#409EFF'
                    }
                    return
                }

                this.$confirm('删除标签，曾使用过此标签的线索，标签值也同时被删除，请谨慎操作', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    if (this.tagForm.length > 1) {
                        this.tagForm.splice(index, 1)
                    } else {
                        this.tagForm[0].name = null
                        this.tagForm[0].cssClass = '#409EFF'
                        this.tagForm[0].id = null
                    }

                    removeTag({"id": tagId}).then(res => {
                        this.$message({
                            showClose: true,
                            message: '删除成功',
                            type: 'success'
                        });
                    })
                }).catch(() => {
                    this.$message({
                        showClose: true,
                        type: 'info',
                        message: '已取消'
                    });
                });
            },

            saveTag() {
                if (this.tagForm.length === 1) {
                    let tag = this.tagForm[0]
                    this.persistentTag(tag)
                }

                if (this.tagForm.length > 1) {
                    this.$confirm('修改标签，曾使用此标签的线索，标签值也同时被修改', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.tagForm.forEach(tag => {
                            this.persistentTag(tag)
                        })
                    }).catch(() => {
                        this.$message({
                            type: 'info',
                            message: '已取消'
                        });
                    });
                }
            },

            persistentTag(tag) {
                if (tag.id == null) {
                    addTag(tag).then(res => {
                        if (res.data.code !== 0) {
                            this.$message({
                                showClose: true,
                                message: res.data.message,
                                type: 'error'
                            });
                        } else {
                            this.$message({
                                showClose: true,
                                message: tag.name + '新增成功',
                                type: 'success'
                            });
                            this.outerVisible = false
                        }
                    })
                } else {
                    updateTag(tag).then(res => {
                        if (res.data.code !== 0) {
                            this.$message({
                                showClose: true,
                                message: res.data.message,
                                type: 'error'
                            });
                        } else {
                            this.$message({
                                showClose: true,
                                message: tag.name + '修改成功',
                                type: 'success'
                            });
                            this.outerVisible = false
                        }
                    })
                }

            },


            toolbarButtonClickEvent({code}) {
                switch (code) {
                    case 'add':
                        // openTab("新增线索", "/businessLeads/edit/add.do")
                        var title = "新增线索";
                        var link = "/businessLeads/edit/add.do";
                        var timestamp = new Date().getTime().toString();
                        window.parent.postMessage({
                            from:'ez',
                            name: title,
                            url:link,
                            id:timestamp
                        }, '*');
                        break
                    case 'init':
                        layer.open({
                            title: '初始化线索',
                            type: 2,
                            shade: 0.2,
                            maxmin: true,
                            shadeClose: true,
                            area: ['50%', '90%'],
                            content: '/initialization/upLoadFile.do?type=1',
                            moveOut: true
                        });
                        break
                    case 'assign':
                        this.assignDialog = true
                        break
                    case 'openTag':
                        this.outerVisible = true
                        this.getInitTags()
                        break
                }
            },

            top(row) {
                toTop({"id": row.id}).then(res => {
                    if (res.data.code === 0) {
                        this.$message({
                            showClose: true,
                            message: '置顶成功',
                            type: 'success'
                        });
                    }
                })
                location.reload();
            },

            unTop(row) {
                toUnTop({"id": row.id}).then(res => {
                    if (res.data.code === 0) {
                        this.$message({
                            showClose: true,
                            message: '取消置顶成功',
                            type: 'success'
                        });
                        location.reload();
                    }
                })
            },

            toDetail(row) {
                openTab("线索详情", '/businessLeads/details.do?id=' + row.id)
            },

            //  phone traderId,traderType,callType,orderId,traderContactId
            call(row, phone) {
                updateLeadsFirstFollowStatus({"id": row.id});
                callout(phone, row.traderId, 1, 7, row.id, row.traderContactId)

            },

            toTrader(row) {
                if (row.traderId === 0) {
                    return
                }
                openTab("客户信息", '/trader/customer/baseinfo.do?traderId=' + row.traderId);
            },

            // 打开沟通记录
            getCommunicationRecord(row) {
                this.communicationRecordDialog = true
                this.showMoreFlag = true
                //获取沟通记录
                getCommunicateRecord(
                    {
                        "param": {"communicateType": 4109, "relatedId": row.id},
                        "orderBy": 'COMMUNICATE_RECORD_ID desc',
                        "pageSize": 3
                    }
                ).then(res => {
                    this.communicateRecordList = res.data.data.list;
                    this.communicateRecordTotalNum = res.data.data.total;
                });
                this.businessLeads = row
            },

            getMoreCommunicateRecord() {
                this.showMoreFlag = false;
                //获取更多沟通记录
                getCommunicateRecord(
                    {
                        "param": {"communicateType": 4109, "relatedId": this.businessLeads.id},
                        "orderBy": 'COMMUNICATE_RECORD_ID desc',
                        "pageSize": 1000
                    }
                ).then(res => {
                    this.communicateRecordList = res.data.data.list;
                });

            },

            //播放录音
            playrecord(url) {
                if (url != '') {
                    debugger
                    layer.open({
                        type: 2,
                        shadeClose: false, //点击遮罩关闭
                        area: ['360px', '80px'],
                        title: false,
                        content: ['/system/call/getrecordpaly.do?url=' + url],
                        success: function (layero, index) {
                            //layer.iframeAuto(index);
                        },
                        error: function (data) {
                            if (data.status == 1001) {
                                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                            }
                        }
                    });
                }
            },

            buildFollowStatus(row) {
                let followStatus = {
                    'value': '',
                    'color': '',
                }

                switch (row.followStatus) {
                    case 1:
                        followStatus.value = '未处理'
                        followStatus.color = '#f61c1c'
                        break;
                    case 2:
                        followStatus.value = '跟进中'
                        followStatus.color = '#16780d'
                        break;
                    case 3:
                        followStatus.value = '已关闭'
                        followStatus.color = '#c2c2c2'
                        break;
                    case 4:
                        followStatus.value = '已商机'
                        followStatus.color = '#d58c0f'
                        break;
                    default:
                }
                return followStatus
            },

            //客户名称远程搜索
            remoteMethod(query) {
                this.loading = true;
                if (query) {
                    getTraderRemoteList({"name": query})
                        .then(res => {
                            this.loading = false;
                            this.traderOptions = res.data.data
                        }).catch(res => {
                        this.traderOptions = [];
                    })
                } else {
                    this.traderOptions = [];
                }
            },

            selectBlur(e) {
                const $table = this.$refs.xGrid
                $table.formData.traderName = e.target.value;
            },

            clickTrader(item) {
                const $table = this.$refs.xGrid
                $table.formData.traderName = item.traderName;
            },

            getCheckedNodes() {
                this.areaCheck = this.$refs['cascader'].getCheckedNodes()
            },

            getChannelCheckedNodes() {
                this.channelCheck = this.$refs['cascaderChannel'].getCheckedNodes()
            },

            getTags() {
                getBusinessTags({"type": "1"}).then(res => {
                    this.tagList = res.data.data
                })
            },

            getInitTags() {
                getBusinessTagsToMap({"type": "1"}).then(res => {
                    let belongerTag = res.data.data.belonger
                    if (belongerTag && belongerTag.length > 0) {
                        this.tagForm = []
                        this.tagForm = belongerTag
                    }
                })
            },

            traderContactClearComm() {
                this.communicateRecordDto.traderContactNameView = null;
                this.communicateRecordDto.traderContactId = null;
                this.communicateRecordDto.contact = null;
                this.communicateRecordDto.contactMob = null;
            },

            assignUser() {
                const $grid = this.$refs.xGrid
                let checkboxRecords = $grid.getCheckboxRecords()
                let ids = []
                checkboxRecords.forEach(c => ids.push(c.id))

                assign({"ids": ids, "userId": this.assignUserId}).then(res => {
                    if (res.data.code === 0) {
                        this.$message({
                            showClose: true,
                            message: res.data.message,
                            type: 'success'
                        });
                        this.assignDialog = false
                    } else {
                        this.$message({
                            showClose: true,
                            message: res.data.message,
                            type: 'error'
                        });
                    }
                })

            },

            communicateRecordNextBind() {
                if (this.communicateRecordDto.noneNextDate) {
                    this.communicateRecordDto.nextContactDate = null;
                    this.rules = {
                        ...this.rules,
                        'nextContactDate': [{
                            required: false,
                            message: '',
                            trigger: 'change'
                        }]
                    };
                } else {
                    this.communicateRecordDto.nextContactDate = null;
                    this.rules = {
                        ...this.rules,
                        'nextContactDate': [{
                            required: true,
                            message: '请输入下次沟通时间',
                            trigger: 'change'
                        }]
                    };
                }
            },
            //新增沟通记录页面====begin
            addCommunicateRecord() {
                this.dialogFormVisible = true;
                this.communicateRecordDto.relatedId = this.businessLeads.id
                if (this.businessLeads.traderId !== 0) {
                    this.communicateRecordDto.traderId = this.businessLeads.traderId
                    getTraderContactDat(
                        {
                            "param": {"traderId": this.businessLeads.traderId},
                            "orderBy": 'TRADER_CONTACT_ID desc',
                            "pageSize": 1000
                        }
                    ).then(res => {
                        this.traderConcatDatas = res.data.data.list
                        console.log(this.traderConcatDatas)
                    });
                } else {
                    var array = []
                    if (this.businessLeads.contact != null
                        && this.businessLeads.contact !== ''
                        && this.businessLeads.phone != null
                        && this.businessLeads.phone !== '') {
                        let mob = {
                            traderContactId: 1,
                            name: this.businessLeads.contact,
                            mobile: this.businessLeads.phone,
                        };
                        array.push(mob)
                    }
                    if (this.businessLeads.contact != null
                        && this.businessLeads.contact !== ''
                        && this.businessLeads.telephone != null
                        && this.businessLeads.telephone !== '') {
                        let data = {
                            traderContactId: 2,
                            name: this.businessLeads.contact,
                            mobile: this.businessLeads.telephone
                        };
                        array.push(data)
                    }
                    this.traderConcatDatas = array;
                    this.communicateRecordDto.contact = this.businessLeads.contact;
                    this.communicateRecordDto.contactMob = this.businessLeads.phone;
                    console.log(this.traderConcatDatas)
                }

            },

            submitCommunicateRecord(form) {
                debugger
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        if (this.communicateRecordDto.noneNextDate) {
                            this.communicateRecordDto.noneNextDate = 1;
                        } else {
                            this.communicateRecordDto.noneNextDate = 0;
                        }
                        if (this.communicateRecordDto.time.length > 1) {
                            this.communicateRecordDto.begintime = this.communicateRecordDto.time[0]
                            this.communicateRecordDto.endtime = this.communicateRecordDto.time[1]
                        }
                        this.communicateRecordDto.relatedId = this.businessLeads.id;
                        debugger
                        if(this.businessLeads.traderId == 0){
                            this.communicateRecordDto.traderContactNameView = this.communicateRecordDto.contact + " " + this.communicateRecordDto.contactMob
                        }
                        addCommunicateRecord(this.communicateRecordDto).then(res => {
                            this.dialogFormVisible = false;
                            updateLeadsFirstFollowStatus({"id": this.businessLeads.id});
                            this.traderContactClearComm();
                            setTimeout(function () {
                                location.reload()
                            }, 500);
                            // location.reload();
                        }).catch((e) => {
                            console.log(e)
                        });
                    }
                })
            },

            cancelCommunicateRecord(form) {
                this.dialogFormVisible = false
                this.$refs[form].resetFields();
            },

            traderConcatSelectComm(item) {
                if (this.businessLeads.traderId !== 0) {
                    this.communicateRecordDto.traderContactNameView = item.name + " " + item.mobile + " " + item.telephone
                    this.communicateRecordDto.traderContactId = item.traderContactId

                } else {
                    this.communicateRecordDto.traderContactNameView = item.name + " " + item.mobile
                    this.communicateRecordDto.contact = item.name
                    this.communicateRecordDto.contactMob = item.mobile
                }
            },

            validateName(rule, value, callback) {
                debugger
                if (this.businessLeads.traderId !== 0) {
                    callback();
                    return;
                }
                if (value ==="" || value === undefined || value === null || value.length === 0) {
                    callback(new Error('请输入联系人'));
                }else if(value.length > 20)  {
                    callback(new Error('联系人姓名不能超过20个字符'));
                }else {
                    callback();
                }
            },
            validatePhone(rule, value, callback) {
                debugger
                if (this.businessLeads.traderId !== 0) {
                    callback();
                    return;
                }
                if (value === '') {
                    callback(new Error('请输入联系电话'));
                    retrun;
                }
                var phoneReg = /(^1[3|4|5|6|7|8|9]\d{9}$)|(^09\d{8}$)/;
                if (!phoneReg.test(value)) {
                    callback(new Error('请输入正确的电话号码'));
                } else {
                    callback();
                }
            },

            getAddress(row) {
                return row.province + ((row.city == null || row.city === '') ? '' : '-')
                    + row.city + ((row.county == null || row.county === '') ? '' : '-') + row.county;
            },

            getGoodsInfo(data) {
                if (data.length > 0) {
                    return data.slice(0, 14)
                }
            },

            getGoodsInfoNext(data) {
                if (data.length > 14) {
                    return data.slice(14)
                }
            },
        }

    });

    function updateTrader() {
        debugger
        let data = {
            "param": {"traderId": vm0.businessLeads.traderId},
            "orderBy": 'TRADER_CONTACT_ID desc',
            "pageSize": 1000
        }
        getTraderContactDat(data).then(res => {
            vm0.traderConcatDatas = res.data.data.list;
            if (vm0.traderConcatDatas.length > 0) {
                vm0.communicateRecordDto.traderContactNameView = vm0.traderConcatDatas[0].name + " " + vm0.traderConcatDatas[0].mobile + " " + vm0.traderConcatDatas[0].telephone
                vm0.communicateRecordDto.traderContactId = vm0.traderConcatDatas[0].traderContactId
            }
        });
    }
</script>

<style>
    .el-row {
        margin-bottom: 10px;
    }

    .switchStyle .el-switch__label {
        position: absolute;
        display: none;
        color: #fff;
    }

    .switchStyle .el-switch__label--left {
        z-index: 9;
        left: 24px;
    }

    .switchStyle .el-switch__label--right {
        z-index: 9;
        left: -4px;
    }

    .switchStyle .el-switch__label.is-active {
        display: block;
    }

    .switchStyle.el-switch .el-switch__core,
    .el-switch .el-switch__label {
        width: 60px !important;
    }

    .el-descriptions-item__label.has-colon {
        width: 130px;
        justify-content: flex-end;
    }

    .cell {
        text-align: center;
    }


    .primaryButton {
        color: #FFF;
        background-color: #409EFF;
        border-color: #409EFF;
        font-size: 12px;
        border-radius: 3px;
    }

    .el-cascader__tags .el-tag .el-icon-close {
        -webkit-box-flex: 0;
        -ms-flex: none;
        flex: none;
        background-color: #C0C4CC;
        color: #FFF;
        display: none;
    }

    /* 直接针对el-input的基础类进行样式覆盖 */
    .el-input,
    .el-input__inner {
        height: 32px !important;
        line-height: 32px; !important;
    }
    
</style>
