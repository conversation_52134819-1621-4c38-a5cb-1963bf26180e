package com.vedeng.erp.buyorder.domain.entity;

import java.util.Date;

public class GeBusinessChance {
    /**   GE_BUSSINESS_CHANCE_ID **/
    private Integer geBussinessChanceId;

    /** GE商机单号  GE_BUSSINESS_CHANCE_NO **/
    private String geBussinessChanceNo;

    /** 报价单ID  QUOTEORDER_ID **/
    private Integer quoteorderId;

    /** 报价单号  QUOTEORDER_NO **/
    private String quoteorderNo;

    /** 报单状态 1可跟进 2不可跟进  STATUS **/
    private Integer status;

    /** 经销商（客户）ID  TRADER_ID **/
    private Integer traderId;

    /** 经销商（客户）名称  TRADER_NAME **/
    private String traderName;

    /** 终端客户ID  TERMINAL_TRADER_ID **/
    private Integer terminalTraderId;

    /** 终端名称  TERMINAL_TRADER_NAME **/
    private String terminalTraderName;

    /** 终端销售区域ID  SALES_AREA_ID **/
    private Integer salesAreaId;

    /** 终端销售区域  SALES_AREA **/
    private String salesArea;

    /** 商品ID  GOODS_ID **/
    private Integer goodsId;

    /** 唯一编码  SKU **/
    private String sku;

    /** 商品名称  GOODS_NAME **/
    private String goodsName;

    /** 医院性质 1公立 2非公  HOSTPITAL_TYPE **/
    private Integer hospitalType;

    /** 商机来源 字典  GE_BUSINESS_CHANCE_SOURCE **/
    private Integer geBusinessChanceSource;

    /** 联系详细地址  ADDRESS **/
    private String address;

    /** 是否维护GE商机内容 0否 1是  IS_DEFEND **/
    private Integer isDefend;

    /** 创建时间  ADD_TIME **/
    private Date addTime;

    /** 更新时间  MOD_TIME **/
    private Date modTime;

    /** 创建人  CREATOR **/
    private Integer creator;

    /** 更新人  UPDATER **/
    private Integer updater;

    /** 创建人真实姓名  CREATOR_NAME **/
    private String creatorName;

    /** 修改人真实姓名  UPDATER_NAME **/
    private String updaterName;

    /** 是否删除 0否 1是  IS_DELETE **/
    private Integer isDelete;

    /**     GE_BUSSINESS_CHANCE_ID   **/
    public Integer getGeBussinessChanceId() {
        return geBussinessChanceId;
    }

    /**     GE_BUSSINESS_CHANCE_ID   **/
    public void setGeBussinessChanceId(Integer geBussinessChanceId) {
        this.geBussinessChanceId = geBussinessChanceId;
    }

    /**   GE商机单号  GE_BUSSINESS_CHANCE_NO   **/
    public String getGeBussinessChanceNo() {
        return geBussinessChanceNo;
    }

    /**   GE商机单号  GE_BUSSINESS_CHANCE_NO   **/
    public void setGeBussinessChanceNo(String geBussinessChanceNo) {
        this.geBussinessChanceNo = geBussinessChanceNo == null ? null : geBussinessChanceNo.trim();
    }

    /**   报价单ID  QUOTEORDER_ID   **/
    public Integer getQuoteorderId() {
        return quoteorderId;
    }

    /**   报价单ID  QUOTEORDER_ID   **/
    public void setQuoteorderId(Integer quoteorderId) {
        this.quoteorderId = quoteorderId;
    }

    /**   报价单号  QUOTEORDER_NO   **/
    public String getQuoteorderNo() {
        return quoteorderNo;
    }

    /**   报价单号  QUOTEORDER_NO   **/
    public void setQuoteorderNo(String quoteorderNo) {
        this.quoteorderNo = quoteorderNo == null ? null : quoteorderNo.trim();
    }

    /**   报单状态 1可跟进 2不可跟进  STATUS   **/
    public Integer getStatus() {
        return status;
    }

    /**   报单状态 1可跟进 2不可跟进  STATUS   **/
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**   经销商（客户）ID  TRADER_ID   **/
    public Integer getTraderId() {
        return traderId;
    }

    /**   经销商（客户）ID  TRADER_ID   **/
    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    /**   经销商（客户）名称  TRADER_NAME   **/
    public String getTraderName() {
        return traderName;
    }

    /**   经销商（客户）名称  TRADER_NAME   **/
    public void setTraderName(String traderName) {
        this.traderName = traderName == null ? null : traderName.trim();
    }

    /**   终端客户ID  TERMINAL_TRADER_ID   **/
    public Integer getTerminalTraderId() {
        return terminalTraderId;
    }

    /**   终端客户ID  TERMINAL_TRADER_ID   **/
    public void setTerminalTraderId(Integer terminalTraderId) {
        this.terminalTraderId = terminalTraderId;
    }

    /**   终端名称  TERMINAL_TRADER_NAME   **/
    public String getTerminalTraderName() {
        return terminalTraderName;
    }

    /**   终端名称  TERMINAL_TRADER_NAME   **/
    public void setTerminalTraderName(String terminalTraderName) {
        this.terminalTraderName = terminalTraderName == null ? null : terminalTraderName.trim();
    }

    /**   终端销售区域ID  SALES_AREA_ID   **/
    public Integer getSalesAreaId() {
        return salesAreaId;
    }

    /**   终端销售区域ID  SALES_AREA_ID   **/
    public void setSalesAreaId(Integer salesAreaId) {
        this.salesAreaId = salesAreaId;
    }

    /**   终端销售区域  SALES_AREA   **/
    public String getSalesArea() {
        return salesArea;
    }

    /**   终端销售区域  SALES_AREA   **/
    public void setSalesArea(String salesArea) {
        this.salesArea = salesArea == null ? null : salesArea.trim();
    }

    /**   商品ID  GOODS_ID   **/
    public Integer getGoodsId() {
        return goodsId;
    }

    /**   商品ID  GOODS_ID   **/
    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    /**   唯一编码  SKU   **/
    public String getSku() {
        return sku;
    }

    /**   唯一编码  SKU   **/
    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    /**   商品名称  GOODS_NAME   **/
    public String getGoodsName() {
        return goodsName;
    }

    /**   商品名称  GOODS_NAME   **/
    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName == null ? null : goodsName.trim();
    }

    /**   医院性质 1公立 2非公  HOSTPITAL_TYPE   **/
    public Integer getHospitalType() {
        return hospitalType;
    }

    /**   医院性质 1公立 2非公  HOSTPITAL_TYPE   **/
    public void setHospitalType(Integer hospitalType) {
        this.hospitalType = hospitalType;
    }

    /**   商机来源 字典  GE_BUSINESS_CHANCE_SOURCE   **/
    public Integer getGeBusinessChanceSource() {
        return geBusinessChanceSource;
    }

    /**   商机来源 字典  GE_BUSINESS_CHANCE_SOURCE   **/
    public void setGeBusinessChanceSource(Integer geBusinessChanceSource) {
        this.geBusinessChanceSource = geBusinessChanceSource;
    }

    /**   联系详细地址  ADDRESS   **/
    public String getAddress() {
        return address;
    }

    /**   联系详细地址  ADDRESS   **/
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    /**   是否维护GE商机内容 0否 1是  IS_DEFEND   **/
    public Integer getIsDefend() {
        return isDefend;
    }

    /**   是否维护GE商机内容 0否 1是  IS_DEFEND   **/
    public void setIsDefend(Integer isDefend) {
        this.isDefend = isDefend;
    }

    /**   创建时间  ADD_TIME   **/
    public Date getAddTime() {
        return addTime;
    }

    /**   创建时间  ADD_TIME   **/
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**   更新时间  MOD_TIME   **/
    public Date getModTime() {
        return modTime;
    }

    /**   更新时间  MOD_TIME   **/
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**   创建人  CREATOR   **/
    public Integer getCreator() {
        return creator;
    }

    /**   创建人  CREATOR   **/
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**   更新人  UPDATER   **/
    public Integer getUpdater() {
        return updater;
    }

    /**   更新人  UPDATER   **/
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**   创建人真实姓名  CREATOR_NAME   **/
    public String getCreatorName() {
        return creatorName;
    }

    /**   创建人真实姓名  CREATOR_NAME   **/
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName == null ? null : creatorName.trim();
    }

    /**   修改人真实姓名  UPDATER_NAME   **/
    public String getUpdaterName() {
        return updaterName;
    }

    /**   修改人真实姓名  UPDATER_NAME   **/
    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName == null ? null : updaterName.trim();
    }

    /**   是否删除 0否 1是  IS_DELETE   **/
    public Integer getIsDelete() {
        return isDelete;
    }

    /**   是否删除 0否 1是  IS_DELETE   **/
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }
}