package com.vedeng.erp.trader.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/16 15:04
 **/
@Setter
@Getter
public class TraderDealerFrontDto {

    private Integer traderId;

    private Integer traderCustomerId;

    private Principal principal;

    private List<Terminal> terminal;


    @Setter
    @Getter
    @NoArgsConstructor
    public static class Principal {

        private List<String> skuTypeChecked;

        private String skuScope;

        private List<List<Integer>> skuCategoryChecked;

        private String salesType;

        private List<Integer> agencyBrandChecked;

        private List<String> governmentRelationChecked;

        private String otherAgencyBrand;

        private List<List<Integer>> agencySkuChecked;

        private String otherAgencySku;

        /**
         * 核心资源 - 产品型渠道商 - 有无代理商品或品牌 [1:有代理商品或品牌；0:无代理商品或品牌]
         */
        private String customerHasAgency;

        /**
         * 是否展示 核心资源 - 产品型渠道商 - 有无代理商品或品牌 [true:展示；false:不展示]
         */
        private boolean showCustomerAgency;

        /**
         * 其他政府关系
         */
        private String otherGovernmentRelation;

        /**
         * 客户有效性 1 有效 0 无效
         */
        private Integer effectiveness;

        /**
         * 客户是否为产品型渠道客户 true or false
         */
        @JsonProperty("isProductCustomer")
        private boolean isProductCustomer;

        /**
         * 客户是否为客户型渠道客户 true or false
         */
        @JsonProperty("isChannelCustomer")
        private boolean isChannelCustomer;
    }

    @Setter
    @Getter
    @NoArgsConstructor
    public static class Terminal {

        private String traderCustomerMarketingType;
        private List<String> institutionNatureChecked;
        private List<String> institutionTypeChecked;
        private List<String> institutionTypeChildChecked;
        private List<String> institutionLevelChecked;
        private String otherInstitutionType;
        private List<String> terminalDataChecked;


    }


}

