package com.vedeng.docSync.model.pojo.generate;

import java.io.Serializable;
import lombok.Data;

/**
 * T_DOC_FILE
 * <AUTHOR>
@Data
public class DocFileDo implements Serializable {
    private Integer fileId;

    /**
     * 文件链接域名
     */
    private String domain;

    /**
     * 文件链接地址
     */
    private String uri;

    /**
     * 文件后缀
     */
    private String suffix;

    /**
     * 文件MD5值
     */
    private String md5;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 是否删除
     */
    private Integer isDelete;

    private String fileName;

    private static final long serialVersionUID = 1L;
}