.record-wrap {
  display: flex;
  align-items: center;
}
.record-wrap .vd-ui_icon {
  font-size: 16px;
  margin-right: 5px;
  color: #09f;
  cursor: pointer;
  line-height: 1;
}
.record-wrap .vd-ui_icon:hover {
  color: #f60;
}
.leads-iframe-wrap .leads-iframe {
  width: 100%;
  height: calc(100vh - 5px);
}
.businesschance-add-container {
  padding-top: 60px;
}
.businesschance-add-container .form-wrap {
  padding: 10px 20px 20px 20px;
}
.businesschance-add-container .form-wrap .form-top-tip {
  max-width: 1240px;
  min-width: 1160px;
  margin: 0 auto;
  margin-bottom: 10px;
}
.businesschance-add-container .top-featurn {
  width: 100%;
  height: 60px;
  position: fixed;
  top: 50px;
  left: 0;
  z-index: 40;
}
.businesschance-add-container .top-featurn .inner-header {
  padding: 0 15px;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #FFFFFF;
  border-bottom: solid 1px #E1E5E8;
}
.businesschance-add-container .top-featurn .inner-header .inner-header-title {
  font-size: 20px;
  font-weight: 700;
}
.businesschance-add-container .next-data {
  margin-top: 5px;
  margin-left: 6px;
}
.businesschance-add-container .price-dw {
  display: inline-block;
  padding: 6px 10px;
}
.businesschance-detail-container {
  position: relative;
  padding-top: 60px;
  padding-right: 360px;
}
.businesschance-detail-container .visible-xs {
  display: none;
}
.businesschance-detail-container .chanceDetail-header-wrap {
  width: 100%;
  min-width: 760px;
  height: 60px;
  background: #fff;
  border-bottom: solid 1px #E1E5E8;
  position: fixed;
  top: 50px;
  z-index: 9;
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-content {
  flex: 1;
  min-width: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-content .header-left {
  display: flex;
  align-items: center;
  padding: 0 15px;
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-content .header-left .title {
  font-size: 20px;
  font-weight: 700;
  color: #333333;
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-content .header-right {
  display: flex;
  align-items: center;
  padding-right: 10px;
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-content .header-right .vd-ui-button {
  margin-right: 10px;
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .header-md-aside {
  position: relative;
  width: 360px;
  display: flex;
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .header-md-aside::before {
  content: "";
  height: 72px;
  width: 1px;
  background: #E1E5E8;
  position: absolute;
  left: 0;
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .vd-ui-title-tip-wrap {
  flex: 1;
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .header-xs-aside {
  display: none;
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item {
  height: 60px;
  position: relative;
  flex: 1;
  background-size: 24px 24px;
  background-position: center;
  background-repeat: no-repeat;
  cursor: pointer;
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item::before {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  content: "";
  z-index: 1;
  background-size: 24px 24px;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.msg {
  background-image: url('../../image/icon/msg.svg');
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.msg::before {
  background-image: url('../../image/icon/msg-active.svg');
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.time {
  background-image: url('../../image/icon/time.svg');
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.time::before {
  background-image: url('../../image/icon/time-active.svg');
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.record {
  background-image: url('../../image/icon/record.svg');
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.record::before {
  background-image: url('../../image/icon/record-active.svg');
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.user {
  background-image: url('../../image/icon/user.svg');
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.user::before {
  background-image: url('../../image/icon/user-active.svg');
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.active,
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item:hover {
  background-image: none;
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.active::before,
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item:hover::before {
  opacity: 1;
}
.businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .h-a-item.active::after {
  content: "";
  display: block;
  width: 30px;
  height: 2px;
  background: #09f;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}
.businesschance-detail-container .deatil-page-top-bg {
  height: 150px;
  background: linear-gradient(to bottom, #ddeefa 0%, #f5f7fa 100%);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
}
.businesschance-detail-container .deatil-page-top-bg.stage5 {
  background: linear-gradient(to bottom, #e0f2e4 0%, #f5f7fa 100%);
}
.businesschance-detail-container .deatil-page-top-bg.stage6 {
  background: linear-gradient(to bottom, #f2dfe4 0%, #f5f7fa 100%);
}
.businesschance-detail-container .detail-page-wrap {
  padding: 0 20px 20px;
  position: relative;
}
.businesschance-detail-container .detail-page-wrap .chance-step {
  height: 90px;
  max-width: 1240px;
  min-width: 960px;
  margin: 0 auto;
}
.businesschance-detail-container .detail-page-wrap .chance-step > ul {
  position: relative;
  z-index: 2;
  display: flex;
  padding: 30px 0 20px;
}
.businesschance-detail-container .detail-page-wrap .chance-step > ul > li {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.businesschance-detail-container .detail-page-wrap .chance-step > ul > li > .icon {
  width: 17px;
  height: 17px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 5px;
  background: #999;
  position: relative;
  z-index: 2;
}
.businesschance-detail-container .detail-page-wrap .chance-step > ul > li > p {
  font-size: 12px;
  color: #999;
}
.businesschance-detail-container .detail-page-wrap .chance-step > ul > li::before {
  content: "";
  position: absolute;
  width: 50%;
  left: 0;
  top: 8px;
  border-bottom: dotted 1px #c9ced1;
}
.businesschance-detail-container .detail-page-wrap .chance-step > ul > li::after {
  content: "";
  position: absolute;
  width: 50%;
  right: 0;
  top: 8px;
  border-bottom: dotted 1px #c9ced1;
}
.businesschance-detail-container .detail-page-wrap .chance-step > ul > li:first-child::before {
  display: none;
}
.businesschance-detail-container .detail-page-wrap .chance-step > ul > li:last-child::after {
  display: none;
}
.businesschance-detail-container .detail-page-wrap .chance-step > ul > li.active .icon {
  background: #09f;
}
.businesschance-detail-container .detail-page-wrap .chance-step > ul > li.active > p {
  color: #09f;
}
.businesschance-detail-container .detail-page-wrap .chance-step > ul > li.success .icon {
  background: #13BF13;
}
.businesschance-detail-container .detail-page-wrap .chance-step > ul > li.success > p {
  color: #13BF13;
}
.businesschance-detail-container .detail-page-wrap .chance-step > ul > li.close .icon {
  background: #E64545;
}
.businesschance-detail-container .detail-page-wrap .chance-step > ul > li.close > p {
  color: #E64545;
}
.businesschance-detail-container .detail-page-wrap .main {
  position: relative;
  z-index: 2;
}
.businesschance-detail-container .detail-page-wrap .main .show-font {
  margin-top: 10px;
}
.businesschance-detail-container .detail-page-wrap .main .show-font:first-child {
  margin-top: 0;
}
.businesschance-detail-container .detail-page-wrap .right-aside {
  width: 360px;
  background: #fff;
  position: fixed;
  right: 0;
  top: 50px;
  z-index: 3;
  min-height: 500px;
  bottom: 0;
  padding-top: 60px;
}
.businesschance-detail-container .detail-page-wrap .right-aside .right-aside-inner {
  height: 100%;
  border-left: solid 1px #E1E5E8;
}
.businesschance-detail-container .related-order-card {
  position: relative;
  max-width: 1240px;
  min-width: 960px;
  margin: 0 auto;
  background: #fff;
  margin-bottom: 20px;
}
.businesschance-detail-container .related-order-card .title {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  font-size: 16px;
  font-weight: 700;
}
.businesschance-detail-container .card-title-ai {
  position: absolute;
  top: 15px;
  right: 20px;
}
.businesschance-detail-container .flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.businesschance-detail-container .quote-title {
  padding: 0 20px;
}
.businesschance-detail-container .quote-title .left .title {
  display: flex;
  align-items: center;
  padding: 15px 0;
  font-size: 16px;
  font-weight: 700;
}
.businesschance-detail-container .quote-title .left .status {
  padding: 0 10px;
  height: 27px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: 400;
  line-height: 27px;
  margin-left: 10px;
}
.businesschance-detail-container .quote-title .left .status.no {
  background-color: #F5F7FA;
  color: #999;
}
.businesschance-detail-container .quote-title .left .status.s0 {
  background-color: #ffede0;
  color: #FF6600;
}
.businesschance-detail-container .quote-title .left .status.s1 {
  background-color: #E3F7E3;
  color: #13BF13;
}
.businesschance-detail-container .quote-title .right .opt {
  display: flex;
  align-items: center;
}
.businesschance-detail-container .quote-title .right .opt .btn {
  position: relative;
  color: #09f;
  padding-right: 10px;
  margin-right: 10px;
  cursor: pointer;
}
.businesschance-detail-container .quote-title .right .opt .btn::after {
  content: "";
  height: 15px;
  border-right: solid 1px #ededed;
  position: absolute;
  right: 0;
  top: 3px;
}
.businesschance-detail-container .quote-title .right .opt .btn:hover {
  color: #f60;
}
.businesschance-detail-container .quote-title .right .opt .btn:last-child {
  margin-right: 0;
  padding-right: 0;
}
.businesschance-detail-container .quote-title .right .opt .btn:last-child::after {
  display: none;
}
.businesschance-detail-container .quoteorder-table {
  border-left: solid 1px #E1E5E8;
  border-top: solid 1px #E1E5E8;
}
.businesschance-detail-container .quoteorder-table .q-tr {
  display: flex;
}
.businesschance-detail-container .quoteorder-table .q-tr .q-td {
  flex: 1;
  border-right: solid 1px #ededed;
  border-bottom: solid 1px #ededed;
}
.businesschance-detail-container .quoteorder-table .q-tr .q-td .label {
  padding: 0 10px;
  height: 35px;
  background: #FAFBFC;
  line-height: 36px;
  text-align: center;
  white-space: nowrap;
  border-bottom: solid 1px #ededed;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
}
.businesschance-detail-container .quoteorder-table .q-tr .q-td .label .tip {
  position: relative;
}
.businesschance-detail-container .quoteorder-table .q-tr .q-td .label .tip > i {
  font-size: 16px;
  color: #999;
  cursor: pointer;
  margin-left: 5px;
}
.businesschance-detail-container .quoteorder-table .q-tr .q-td .label .tip > i:hover + span {
  display: block;
}
.businesschance-detail-container .quoteorder-table .q-tr .q-td .label .tip > span {
  display: none;
  position: relative;
  font-size: 14px;
  line-height: 21px;
  padding: 8px 15px;
  border-radius: 3px;
  background: #333;
  color: #fff;
  position: absolute;
  top: -41px;
  left: -30px;
}
.businesschance-detail-container .quoteorder-table .q-tr .q-td .label .tip > span::after {
  content: "";
  position: absolute;
  left: 37px;
  bottom: -9px;
  width: 0;
  height: 0;
  border-top: solid 9px #333333;
  border-left: solid 6px transparent;
  border-right: solid 6px transparent;
}
.businesschance-detail-container .quoteorder-table .q-tr .q-td .label .tip.right > span {
  left: auto;
  right: -30px;
}
.businesschance-detail-container .quoteorder-table .q-tr .q-td .label .tip.right > span::after {
  left: auto;
  right: 32px;
}
.businesschance-detail-container .quoteorder-table .q-tr .q-td .value {
  padding: 8px 10px;
  text-align: center;
  font-size: 14px;
  color: #333;
}
.businesschance-detail-container .quoteorder-table .q-tr .q-td .value.price {
  color: #E64545;
  font-weight: 700;
}
.businesschance-detail-container .null-baojia {
  padding: 20px 0 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.businesschance-detail-container .null-baojia > i {
  font-size: 32px;
  color: #09F;
}
.businesschance-detail-container .null-baojia > p {
  color: #999;
  margin-top: 10px;
}
.businesschance-detail-container .detail-tyc {
  word-break: break-all;
  position: relative;
  padding-right: 21px;
  display: inline-block;
}
.businesschance-detail-container .detail-tyc .company {
  font-size: 12px;
  color: #333;
}
.businesschance-detail-container .detail-tyc .company.blue {
  color: #09f;
  cursor: pointer;
}
.businesschance-detail-container .detail-tyc .company.blue:hover {
  color: #f60;
}
.businesschance-detail-container .detail-tyc .icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url('../../image/tyc.png') no-repeat;
  background-size: 100%;
  margin-left: 5px;
  cursor: pointer;
  position: absolute;
  top: 1px;
  right: 0;
}
.businesschance-detail-container .highlight {
  color: #09f;
  cursor: pointer;
}
.businesschance-detail-container .highlight .vd-ui_icon {
  font-size: 16px;
  margin-right: 1px;
  cursor: pointer;
  line-height: 1;
}
.businesschance-detail-container .highlight:hover {
  color: #f60;
}
.businesschance-detail-container .no-bg {
  background: none;
}
.businesschance-detail-container .no-bg .icon-collect2 {
  color: #FF6600;
}
.call-span .icon-call2 {
  line-height: 1;
  vertical-align: -2px;
  font-size: 16px;
}
.call-span.highlight {
  color: #09f;
}
.call-span.highlight:hover {
  color: #f60;
}
.td-tyc-wrap {
  display: flex;
  align-items: center;
}
.td-tyc-wrap .text-line-1 {
  flex: 1;
}
.td-tyc-wrap .tyc-icon {
  width: 16px;
  height: 16px;
  background-image: url(../../image/tyc.png);
  background-size: 100% 100%;
  margin-left: 5px;
  cursor: pointer;
}
.change-type-inner {
  display: flex;
}
.change-type-inner .vd-ui-tip-wrap {
  margin-left: 20px;
  width: 17px;
}
.form-wrap .form-chance-block-placeholder {
  padding-bottom: 15px;
}
.form-wrap .form-chance-block {
  background: rgba(255, 237, 224, 0.5);
  padding: 15px 0;
  margin-bottom: 15px;
  position: relative;
}
.form-wrap .form-chance-block .form-chance-tip {
  position: absolute;
  display: flex;
  align-items: center;
  color: #f60;
  right: 10px;
  top: 10px;
}
.form-wrap .form-chance-block .form-chance-tip .vd-ui_icon {
  line-height: 1;
  margin-right: 5px;
  font-size: 16px;
}
.form-wrap .form-chance-block .form-chance-tip .icon-yes1 {
  display: none;
}
.form-wrap .form-chance-block.active {
  background: rgba(227, 247, 227, 0.5);
}
.form-wrap .form-chance-block.active .form-chance-tip {
  color: #13BF13;
}
.form-wrap .form-chance-block.active .icon-yes1 {
  display: block;
}
.form-wrap .form-chance-block.active .icon-info1 {
  display: none;
}
@media screen and (max-width: 1366px) {
  .businesschance-detail-container {
    padding-right: 0;
  }
  .businesschance-detail-container .hidden-xs {
    display: none;
  }
  .businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .header-md-aside {
    display: none;
  }
  .businesschance-detail-container .chanceDetail-header-wrap .header-main .header-aside-wrap .header-xs-aside {
    position: relative;
    width: 192px;
    display: flex;
  }
}
.order-type .vd-ui-input-error {
  margin-bottom: 10px!important;
}
.order-type .red {
  color: #e64545;
}
