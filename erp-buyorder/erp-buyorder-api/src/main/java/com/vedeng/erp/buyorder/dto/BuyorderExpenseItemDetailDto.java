package com.vedeng.erp.buyorder.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description 采购费用明细详情
 * <AUTHOR>
 * @date 2022/10/17 15:18
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BuyorderExpenseItemDetailDto extends BaseDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Integer buyorderExpenseItemDetailId;

    /**
     * 采购明细表id
     */
    private Integer buyorderExpenseItemId;

    /**
     * 唯一编码
     */
    private String sku;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 商品型号
     */
    private String model;

    /**
     * 单位中文名
     */
    private String unitName;

    /**
     * 单价
     */
    private BigDecimal price;



    /**
     * 货币单位ID
     */
    private Integer currencyUnitId;

    /**
     * 虚拟商品所属费用类别ID
     */
    private Integer expenseCategoryId;

    /**
     * 虚拟商品费用类别名称
     */
    private String expenseCategoryName;

    /**
     * 虚拟商品是否有库存管理
     */
    private Integer haveStockManage;

    /**
     * 采购费用商品备注
     */
    private String insideComments;

    /**
     * 商品备注 直属费用单，关联的采购单申请修改时用到这个字段
     */
    private String oldInsideComments;

    /**
     * 是否删除0否1是
     */
    private Integer isDelete;

    /**
     * @组合对象@ 产品归属经理
     */
    private String assignmentManager;





}