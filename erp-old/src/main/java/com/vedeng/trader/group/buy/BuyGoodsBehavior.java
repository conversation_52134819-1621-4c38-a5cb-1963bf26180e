package com.vedeng.trader.group.buy;

import com.vedeng.trader.group.GoodsBehavior;

import java.util.BitSet;

/**
 * <b>Description:</b>购买商品的行为<br>
 * @Note
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/5/25
 */
public class BuyGoodsBehavior extends BuyBaseBehavior {
    @Override
    public BitSet calculate() {
        BitSet res=getBaseTraderSet();
        BitSet buyset=getBuyGoods();
        res.and(buyset);
        return res;
    }
}
