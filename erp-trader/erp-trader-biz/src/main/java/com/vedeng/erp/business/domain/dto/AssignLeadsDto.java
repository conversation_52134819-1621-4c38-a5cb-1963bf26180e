package com.vedeng.erp.business.domain.dto;

import com.vedeng.common.core.utils.validator.group.AddGroup;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 分配线索
 * @date 2022/7/25 19:56
 */
@Data
public class AssignLeadsDto {

    /**
     * 线索id
     */
    @NotEmpty(message = "分配线索不能为空", groups = {AddGroup.class})
    private List<Integer> ids;

    /**
     * 归属人
     */
    @NotNull(message = "归属人不能为空", groups = {AddGroup.class})
    private Integer userId;

}
