package com.vedeng.infrastructure.kingdee.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeEventMsgDto;
import com.vedeng.infrastructure.kingdee.domain.entity.KingDeeEventMsgEntity;
import com.vedeng.infrastructure.kingdee.enums.KingDeeMsgStatusEnums;
import com.vedeng.infrastructure.kingdee.mapper.KingDeeEventMsgMapper;
import com.vedeng.infrastructure.kingdee.mapstruct.KingDeeEventMsgConvertor;
import com.vedeng.infrastructure.kingdee.service.KingDeeEventMsgService;
import com.vedeng.infrastructure.wxrobot.constants.WxRobotMsgTemple;
import com.vedeng.infrastructure.wxrobot.dto.WxMsgDto;
import com.vedeng.infrastructure.wxrobot.service.WxRobotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶事件消息接口
 * @date 2022/8/27 12:40
 */
@Service
@Slf4j
public class KingDeeEventMsgServiceImpl implements KingDeeEventMsgService {

    @Autowired
    private KingDeeEventMsgMapper kingDeeEventMsgMapper;
    @Autowired
    private KingDeeEventMsgConvertor kingDeeEventMsgConvertor;
    @Autowired
    private WxRobotService wxRobotService;


    @Value("${kingdee.wxRootNum}")
    public String wxRootNum;

    @Value("${kingdee.retryNum}")
    private Integer retryNum;

    @Value("${kingdee.openSend}")
    private Boolean openSend;

    @Override
    public void create(KingDeeEventMsgDto kingDeeEventMsgDto) {
        kingDeeEventMsgDto.setMessageStatus(KingDeeMsgStatusEnums.SEND.getStatus());
        KingDeeEventMsgEntity kingDeeEventMsgEntity = kingDeeEventMsgConvertor.toEntity(kingDeeEventMsgDto);
        kingDeeEventMsgMapper.insertSelective(kingDeeEventMsgEntity);
        kingDeeEventMsgDto.setKingDeeEventMsgId(kingDeeEventMsgEntity.getKingDeeEventMsgId());
    }

    @Override
    public void msgConsume(KingDeeEventMsgDto kingDeeEventMsgDto) {
        KingDeeEventMsgEntity msgEntity = kingDeeEventMsgMapper.selectByPrimaryKey(kingDeeEventMsgDto.getKingDeeEventMsgId());
        msgEntity.setMessageStatus(KingDeeMsgStatusEnums.CONSUME.getStatus());
        msgEntity.setErrorMsg("");
        kingDeeEventMsgMapper.insertIntoBackUpTable(msgEntity);
        log.info("消息备份成功删除原表，{}", JSON.toJSONString(msgEntity));
        kingDeeEventMsgMapper.deleteByPrimaryKey(kingDeeEventMsgDto.getKingDeeEventMsgId());
    }

    @Override
    public void msgNoHandleByLtId(KingDeeEventMsgDto kingDeeEventMsgDto) {
        List<KingDeeEventMsgDto> noConsumeMsgById = this.getNoConsumeMsgLtId(kingDeeEventMsgDto.getBusinessId(), kingDeeEventMsgDto.getEventType(), kingDeeEventMsgDto.getKingDeeEventMsgId());
        if (CollectionUtils.isEmpty(noConsumeMsgById)) {
            return;
        }
        noConsumeMsgById.forEach(item -> {
            item.setMessageStatus(KingDeeMsgStatusEnums.NO_HANDLE.getStatus());
            kingDeeEventMsgMapper.insertIntoBackUpTable(kingDeeEventMsgConvertor.toEntity(item));
            log.info("updateNotHandleByIds:{}", JSON.toJSONString(item));
            kingDeeEventMsgMapper.deleteByPrimaryKey(item.getKingDeeEventMsgId());
        });
    }

    @Override
    public void msgRetry(KingDeeEventMsgDto kingDeeEventMsgDto) {
        if (kingDeeEventMsgDto != null) {
            kingDeeEventMsgDto.setMessageStatus(KingDeeMsgStatusEnums.RETRY.getStatus());
            kingDeeEventMsgMapper.updateByPrimaryKeySelective(kingDeeEventMsgConvertor.toEntity(kingDeeEventMsgDto));
        }
    }

    @Override
    public void msgRetryNumAdd(KingDeeEventMsgDto kingDeeEventMsgDto) {
        if (kingDeeEventMsgDto != null && kingDeeEventMsgDto.getKingDeeEventMsgId() != null) {
            kingDeeEventMsgMapper.addRetryNum(kingDeeEventMsgDto.getKingDeeEventMsgId());
        }
    }

    @Override
    public void sendMsg(KingDeeEventMsgDto kingDeeEventMsgDto) {
        if (kingDeeEventMsgDto != null) {
            String format = StrUtil.format(WxRobotMsgTemple.KING_DEE_ERROR, retryNum,
                    kingDeeEventMsgDto.getKingDeeEventMsgId(),
                    kingDeeEventMsgDto.getEventType(),
                    kingDeeEventMsgDto.getBody(),
                    kingDeeEventMsgDto.getErrorMsg());
            WxMsgDto wxMsg = new WxMsgDto().initWxMsgDto(format);
            wxRobotService.send(wxRootNum, wxMsg);

            KingDeeEventMsgEntity update = new KingDeeEventMsgEntity();
            update.setKingDeeEventMsgId(kingDeeEventMsgDto.getKingDeeEventMsgId());
            update.setSendMsg(1);
            kingDeeEventMsgMapper.updateByPrimaryKeySelective(update);
        }
    }

    @Override
    public List<KingDeeEventMsgDto> getRetryMsg(Integer id) {
        return kingDeeEventMsgConvertor.toDto(kingDeeEventMsgMapper.findByMessageStatus(KingDeeMsgStatusEnums.RETRY.getStatus(), retryNum,id,null));
    }

    @Override
    public List<KingDeeEventMsgDto> getRetryMsg(List<String> eventTypeList,Integer retryNum) {
        return kingDeeEventMsgConvertor.toDto(kingDeeEventMsgMapper.findByMessageStatus(KingDeeMsgStatusEnums.RETRY.getStatus(), retryNum,null,eventTypeList));
    }


    @Override
    public List<KingDeeEventMsgDto> getNeedSendMsg() {
        return kingDeeEventMsgConvertor.toDto(kingDeeEventMsgMapper.findNeedSendMsg(KingDeeMsgStatusEnums.RETRY.getStatus(), retryNum,openSend));
    }

    @Override
    public void msgNoHandle(KingDeeEventMsgDto kingDeeEventMsgDto) {
        kingDeeEventMsgDto.setMessageStatus(KingDeeMsgStatusEnums.NO_HANDLE.getStatus());
        kingDeeEventMsgMapper.updateByPrimaryKeySelective(kingDeeEventMsgConvertor.toEntity(kingDeeEventMsgDto));
    }

    @Override
    public List<KingDeeEventMsgDto> getNoConsumeMsgById(String businessId, String eventType) {
        return kingDeeEventMsgConvertor.toDto(kingDeeEventMsgMapper.findAllByBusinessIdAndEventType(businessId, eventType));
    }

    @Override
    public List<KingDeeEventMsgDto> getNoConsumeMsgLtId(String businessId, String eventType, Integer msgId) {
        return kingDeeEventMsgConvertor.toDto(kingDeeEventMsgMapper.findLtByBusinessIdAndEventType(businessId, eventType, msgId));
    }

}
