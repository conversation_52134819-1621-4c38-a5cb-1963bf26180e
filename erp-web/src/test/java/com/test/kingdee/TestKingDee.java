package com.test.kingdee;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.kingdee.domain.command.KingDeeCustomerCommand;
import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeInternalProcurementQueryResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePayExpensesQueryResultDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SaveResult;
import com.vedeng.infrastructure.kingdee.common.K3CloudApi;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.*;


@Slf4j
public class TestKingDee {

    @Test
    public void kingDee() throws Exception {
        K3CloudApi api = new K3CloudApi();
        KingDeePayBill payBillDto = new KingDeePayBill();
        payBillDto.setFid(0);
        payBillDto.setFBillNo("2323232");
        payBillDto.setFdate("2022-08-25");
        payBillDto.setFCONTACTUNITTYPE("BD_Supplier");

        payBillDto.getFCONTACTUNIT().setFNumber("000");
        payBillDto.getFRECTUNIT().setFNumber("000");
        payBillDto.getFSETTLEORGID().setFNumber("100");
        payBillDto.getFPAYORGID().setFNumber("100");

        payBillDto.setFremark("测试付款");

        payBillDto.setFexchangerate("1.0");

        KingDeePayBill.FPAYBILLENTRY fpaybillentry = new KingDeePayBill.FPAYBILLENTRY();
        fpaybillentry.getFSETTLETYPEID().setFNumber("JSFS04_SYS");
        fpaybillentry.getFaccountid().setFNumber("************");
        fpaybillentry.setFsettleno("12125");
        fpaybillentry.setFpaytotalamountfor("500");
        payBillDto.getFpaybillentry().add(fpaybillentry);


        KingDeePayBill.FPAYBILLSRCENTRY fpaybillsrcentry = new KingDeePayBill.FPAYBILLSRCENTRY();
        fpaybillsrcentry.setFafttaxtotalamount("500");
        payBillDto.getFpaybillsrcentry().add(fpaybillsrcentry);

        SaveResult sRet = api.save("AP_PAYBILL", new SaveExtCommand<>(payBillDto, "AP_PAYBILL"));


        if (sRet.isSuccessfully()) {
            Gson gson = new Gson();
            System.out.println(sRet.getResult().getResponseStatus().isIsSuccess());
            System.out.println(String.format("%s", gson.toJson(sRet.getResult().getResponseStatus().getSuccessEntitys())));
        } else {
            Gson gson = new Gson();
            System.out.println(sRet.getResult().getResponseStatus().isIsSuccess());
            System.out.println(String.format("%s", gson.toJson(sRet.getResult().getResponseStatus().getErrors())));
        }
    }


    @Test
    public void kingDeeSaveSupplier() throws Exception {
        K3CloudApi api = new K3CloudApi();


        KingDeeOrg dto = new KingDeeOrg();
        dto.setFName("lewis做大做强再创辉煌供应商");
        dto.setFNumber("test2223");
        dto.setFSupplierId("0");
        dto.getFBaseInfo().setFSupplyClassify("CG");
        dto.getFCreateOrgId().setFNumber("123456");
        dto.getFCreateOrgId().setFNumber("100");
        dto.getFUseOrgId().setFNumber("100");


        SaveResult sRet = api.save("BD_Supplier", new SaveExtCommand<>(dto, "BD_Supplier"));

        if (sRet.isSuccessfully()) {
            Gson gson = new Gson();
            System.out.println(sRet.getResult().getResponseStatus().isIsSuccess());
            System.out.println(String.format("%s", gson.toJson(sRet.getResult().getResponseStatus().getSuccessEntitys())));
        } else {
            Gson gson = new Gson();
            System.out.println(sRet.getResult().getResponseStatus().isIsSuccess());
            System.out.println(String.format("%s", gson.toJson(sRet.getResult().getResponseStatus().getErrors())));
        }
    }

    @Test
    public void kingDeeSaveCustomer() throws Exception {
        K3CloudApi api = new K3CloudApi();


        KingDeeCustomerCommand dto = new KingDeeCustomerCommand();
        dto.setFName("lewis做大做强再创辉煌供应商adasdads");
        dto.setFNumber("test22234");
        dto.setFCUSTID("0");
        dto.getFCreateOrgId().setFNumber("123456");
        dto.getFCreateOrgId().setFNumber("100");
        dto.getFUseOrgId().setFNumber("100");
//        dto.getFTaxRate().setFNumber("asdasdad");
//        dto.getFTRADINGCURRID().setFNumber("FTRADINGCURRID");
        SaveResult sRet = api.save("BD_Customer", new SaveExtCommand<>(dto, "BD_Customer"));

        if (sRet.isSuccessfully()) {
            Gson gson = new Gson();
            System.out.println(sRet.getResult().getResponseStatus().isIsSuccess());
            System.out.println(String.format("%s", gson.toJson(sRet.getResult().getResponseStatus().getSuccessEntitys())));
        } else {
            Gson gson = new Gson();
            System.out.println(sRet.getResult().getResponseStatus().isIsSuccess());
            System.out.println(String.format("%s", gson.toJson(sRet.getResult().getResponseStatus().getErrors())));
        }
    }


    @Test
    public void kingDeeQuery() {
        KingDeeBaseApi kingDeeBaseApi = new KingDeeBaseApi();
        //KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        //queryParam.setFormId(KingDeeFormConstant.PAY_EXPENSES);
        //List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        //queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBillNo").value("RK2212151000002").build());
        //queryParam.setFilterString(queryFilterDtos);
        //List<KingDeePurchaseReceiptQueryResultDto> query = kingDeeBaseApi.query(queryParam, KingDeePurchaseReceiptQueryResultDto.class);
        //log.info("==结果=={}", JSON.toJSONString(query));
        //KingDeePayExpensesQueryResultDto kingDeePayExpensesQueryResultDto = new KingDeePayExpensesQueryResultDto();
        //KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        //queryParam.setFormId("AP_Payable");
        //List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        //queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("F_QZOK_BDDJTID")
        //        .value("22305560").build());
        //queryParam.setFilterString(queryFilterDtos);
        //List<KingDeePayExpensesQueryResultDto> query =  kingDeeBaseApi.query(queryParam, KingDeePayExpensesQueryResultDto.class);
        //log.info("==结果=={}", JSON.toJSONString(query));

        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.INTERNAL_PROCUREMENT);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("F_QZOK_BDDJTID").value("117").build());
        queryParam.setFilterString(queryFilterDtos);
        log.info("金蝶采购入库单查询入参：{}", JSON.toJSONString(queryParam));
        List<KingDeeInternalProcurementQueryResultDto> query =  kingDeeBaseApi.query(queryParam, KingDeeInternalProcurementQueryResultDto.class);

        System.out.println(JSON.toJSONString(query));

    }

    public static List<Map<String, Object>> checkQuery(List<List<Object>> result, KingDeeQueryExtParam queryParam) {
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        String jsonString = JSON.toJSONString(result);
        if (jsonString.contains("ErrorCode")) {
            throw new ServiceException("金蝶查询接口异常");
        }
        List<String> strings = StrUtil.splitTrim(queryParam.getFieldKeys().toLowerCase(Locale.ROOT), ",");
        List<Map<String, Object>> parseData = new ArrayList<>();
        for (List<Object> data : result) {
            Map<String, Object> parseItem = new HashMap<>();
            for (int i = 0; i < strings.size(); i++) {
                parseItem.put(strings.get(i), data.get(i));
            }
            parseData.add(parseItem);
        }
        return parseData;
    }

    @Test
    public void aVoid() {
//        LocalDateTime todayStart = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
//        LocalDateTime todayEnd = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
//        long begin = todayStart.toInstant(ZoneOffset.of("+8")).toEpochMilli();
//        long end = todayEnd.toInstant(ZoneOffset.of("+8")).toEpochMilli();
//        System.out.println(begin);
//        System.out.println(end);

        KingDeePayBillDto kingDeePayBillDto = new KingDeePayBillDto(KingDeeBizEnums.savePayBill, ErpConst.ONE);
        log.info("test:{}", JSON.toJSONString(kingDeePayBillDto));

    }

}
