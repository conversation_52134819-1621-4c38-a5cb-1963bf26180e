package com.vedeng.erp.finance.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.finance.constants.FinanceConstant;
import com.vedeng.erp.finance.dto.PayApplyCreateBillDto;
import com.vedeng.erp.finance.service.AbstractPayApply;
import com.vedeng.erp.trader.dto.TraderCustomerDto;
import com.vedeng.erp.trader.service.TraderCustomerApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 售后单付款申请
 */
@Slf4j
@Service
public class AfterSalesPayApplyService extends AbstractPayApply {

    @Autowired
    private AfterSalesApiService AfterSalesApiService;

    @Autowired
    private TraderCustomerApiService traderCustomerApiService;

    // 售后类型：第三方维修585，第三方安调550，销售订单维修584，销售安调（附加服务）4091，销售安调（合同安调）4090 退货539 退款543
    public static final List<Integer> AFTER_SALES_TYPE_LIST = Arrays.asList(585, 550, 584, 4091, 4090, 539,543);


    @Override
    public void createBillBaseCheckValid(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException {
        AfterSalesDto dto = AfterSalesApiService.getAfterSalesById(payApplyCreateBillDto.getRelatedId());
        log.info("售后单信息：{}", JSON.toJSON(dto));
        if (Objects.isNull(dto)){
            throw new ServiceException("未找到对应的售后单" + payApplyCreateBillDto.getRelatedId());
        }
        //1. 单据进行中：归属业务单据的单据状态 = 进行中
        if (dto.getAtferSalesStatus() != 1){
            throw new ServiceException("归属业务单据的单据状态不是进行中，当前状态：" +dto.getAtferSalesStatus());
        }
        //2. 单据已生效：归属业务单据的生效状态 = 已生效
        if (dto.getValidStatus() != 1){
            throw new ServiceException("归属业务单据的生效状态不是已生效，当前状态：" +dto.getValidStatus());
        }
        //3. 单据未完成付款：归属业务单据的付款状态 ！= 全部付款
        if (Objects.equals(dto.getAmountPayStatus(),3)){
            throw new ServiceException("归属业务单据的付款状态是全部付款" );
        }
    }

    @Override
    public void createBillBusinessCheck(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException {
        super.businessCheck(payApplyCreateBillDto);
        AfterSalesDto dto = AfterSalesApiService.getAfterSalesById(payApplyCreateBillDto.getRelatedId());
        boolean contains = AFTER_SALES_TYPE_LIST.contains(dto.getType());
        if (!contains){
            throw new ServiceException("售后单类型不是：" + AFTER_SALES_TYPE_LIST + ",当前类型：" + dto.getType());
        }
    }

    @Override
    public void otherProcess(PayApplyCreateBillDto payApplyCreateBillDto) {
        AfterSalesDto dto = AfterSalesApiService.getAfterSalesById(payApplyCreateBillDto.getRelatedId());
        Integer type = dto.getType();
        if (FinanceConstant.TH.equals(type) || FinanceConstant.TK.equals(type)) {
            log.info("售后退货退款自动制单扣减余额,payApplyCreateBillDto:{}", JSON.toJSONString(payApplyCreateBillDto));
            TraderCustomerDto traderCustomerDto = traderCustomerApiService.getTraderByPayApply(payApplyCreateBillDto.getPayApplyId());
            if (Objects.isNull(traderCustomerDto)) {
                log.info("售后单id:{}，未找到对应的客户信息", payApplyCreateBillDto.getRelatedId());
                return;
            }
//            if (payApplyCreateBillDto.getAmount().compareTo(traderCustomerDto.getAmount()) > 0) {
//                throw new ServiceException("退款金额大于账户余额，无法退款");
//            }
            log.info("售后订单id:{}，在财务制单环节，余额不扣减", payApplyCreateBillDto.getRelatedId());
//            log.info("售后订单id:{}，在财务制单环节，余额扣减金额：{}", payApplyCreateBillDto.getRelatedId(), payApplyCreateBillDto.getAmount());
//            traderCustomerApiService.updateTraderAmount(traderCustomerDto.getTraderId(), payApplyCreateBillDto.getAmount().multiply(new BigDecimal(-1)));
        }
    }


    @Override
    public Integer choseBank(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException {
        boolean withinWorkingHours = payRuleCheck();
        // 民生
        Integer payVedengBankId = ErpConstant.SEVEN;
        if (withinWorkingHours){
            // 周一到周五，销售售后付款：付款银行 = 中国银行
            payVedengBankId = ErpConstant.ONE;
        }
        log.info("选择银行{},withinWorkingHours:{}",payVedengBankId,withinWorkingHours);

        return payVedengBankId;

    }
}
