package com.vedeng.order.service.impl;

import com.google.common.base.Objects;
import com.vedeng.activiti.constants.ProcessConstants;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.erp.buyorder.dto.BuyOrderModifyApply;
import com.vedeng.erp.buyorder.dto.Buyorder2SaleorderGoodsDto;
import com.vedeng.erp.buyorder.dto.BuyorderModifyApplyGoods;
import com.vedeng.erp.buyorder.dto.ModifyOrderMessageDto;
import com.vedeng.erp.saleorder.api.OrderInfoSyncService;
import com.vedeng.order.dao.*;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.RBuyorderSaleorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderModifyApply;
import com.vedeng.order.model.vo.BuyorderModifyApplyVo;
import com.vedeng.order.service.BuyOrderModifyApplyService;
import com.vedeng.order.service.SaleorderService;
import com.wms.constant.CancelReasonConstant;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.service.CancelTypeService;
import com.wms.service.util.GlobalThreadPool;
import com.wms.service.LogicalSaleorderChooseService;
import com.wms.service.listenner.PurchaseChangeAuditFinishListener;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Service
public class BuyOrderModifyApplyServiceImpl implements BuyOrderModifyApplyService {

    private final static Logger LOGGER = LoggerFactory.getLogger(BuyOrderModifyApplyServiceImpl.class);

    @Autowired
    private PurchaseChangeAuditFinishListener purchaseChangeAuditFinishListener;
    @Autowired
    private OrderInfoSyncService orderInfoSyncService;
    @Autowired
    private LogicalSaleorderChooseService logicalSaleorderChooseService;
    @Resource
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;
    @Resource
    private BuyorderMapper buyorderMapper;
    @Resource
    private SaleorderMapper saleorderMapper;
    @Resource
    private BuyorderModifyApplyGoodsMapper buyorderModifyApplyGoodsMapper;
    @Resource
    private BuyOrderModifyApplyMapper buyOrderModifyApplyMapper;
    @Resource
    private RBuyorderSaleorderMapper buyorderSaleorderMapper;

    @Resource
    private SaleorderService saleorderService;

    @Autowired
    private CancelTypeService cancelTypeService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processBuyOrderModifyApplyFinished(Map<String, Object> variables, boolean audited,User user) {
        if (MapUtils.isEmpty(variables)) {
            throw new IllegalArgumentException();
        }

        Integer applyId = MapUtils.getInteger(variables, "relateTableKey");

        BuyorderModifyApplyVo buyOrderModifyApplyInfo = (BuyorderModifyApplyVo) variables.get("buyorderModifyApplyInfo");

        boolean isChangeDeliveryWay = Boolean.TRUE.equals(variables.get("isChangeDeliveryWay"));
        boolean isChangeLogisticsComment = Boolean.TRUE.equals(variables.get("isChangeLogisticsComment"));
        boolean deliverySelfOrWmsSwitch = Boolean.TRUE.equals(variables.get("deliverySelfOrWmsSwitch"));

        boolean enableSendBuOrderRequest = isChangeDeliveryWay || isChangeLogisticsComment;

        //原始的采购单
        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyOrderModifyApplyInfo.getBuyorderId());

        //采购单关联的销售单数量不为1，不可修改发货方式
        List<Integer> saleOrderIdList = buyorderMapper.getSaleorderIdListByBuyorderId(buyorder.getBuyorderId());
        if (saleOrderIdList.size() == 1 && isChangeDeliveryWay) {
            Saleorder saleOrderQuery = saleorderMapper.selectByPrimaryKey(saleOrderIdList.get(0));
            if (saleOrderQuery != null) {
                Integer validStatus = audited ? ProcessConstants.CheckStatus.CHECKED.getStatus() : ProcessConstants.CheckStatus.REJECTED.getStatus();
                //采购单状态信息发生改变通知销售
                syncModifyApplyInfoToSalesSide(saleOrderQuery.getSaleorderId(), applyId, validStatus,null);
                if (deliverySelfOrWmsSwitch) {
                    //直发 ->>> 普法
                    sendSaleOrderOutStorageRequest(saleOrderQuery.getSaleorderNo(),user);
                }else {
                    //普法 ->>> 直发
                    //如果提交申请的时候 有取消销售订单WMS接口，需进行WMS相关的操作
                    rePutSaleOrderOutput(applyId,saleOrderQuery.getSaleorderId(),user);
                }
            }
        }

        //如果提交申请的时候 有取消同步WMS接口，需进行WMS相关的操作
        if (ErpConst.ONE.equals(buyOrderModifyApplyInfo.getSynWmsCancel())) {
            //提交任务到消息监听处理线程池
            GlobalThreadPool.submitMessage(() -> {
                try {
                    String uuid = UUID.randomUUID().toString().replace("-", "");
                    LOGGER.info("开始执行purchaseChangeAuditFinishListener.onActionHappen,uuid:{}",uuid);
                    Thread.sleep(5000L);
                    purchaseChangeAuditFinishListener.onActionHappen(buyOrderModifyApplyInfo, audited);
                    LOGGER.info("结束执行purchaseChangeAuditFinishListener.onActionHappen,uuid:{}",uuid);

                } catch (Exception e) {
                    LOGGER.error("purchaseAuditFinishListener->onActionHappen:", e);
                }
            });
        }
    }

    public void rePutSaleOrderOutput(Integer applyId,Integer saleOrderId,User user){
        SaleorderModifyApply saleorderModifyApply = new SaleorderModifyApply();
        saleorderModifyApply.setRelatedId(applyId);
        saleorderModifyApply.setSaleorderId(saleOrderId);
        SaleorderModifyApply saleorderModifyApplyInfo = saleorderService.getSaleorderModifyApplyInfoByRelatedIdAndOrderId(saleorderModifyApply);
        //iswmscancel为1时需要补偿wms出库单任务
        if(saleorderModifyApplyInfo.getIsWmsCancel() != null && saleorderModifyApplyInfo.getIsWmsCancel().equals(1)){
            try{
                Saleorder saleorder = new Saleorder();
                saleorder.setSaleorderId(saleOrderId);
                logicalSaleorderChooseService.chooseLogicalSaleorder(saleorder,user);
            }catch (Exception e){
                LOGGER.error("销售单下发WMS失败{}",saleOrderId,e);
            }
        }
    }


    @Override
    public void syncModifyApplyInfoToSalesSide(Integer saleOrderId, Integer applyId, Integer validStatus,Boolean isWmsCancel) {
        BuyOrderModifyApply buyOrderModifyApply = buyOrderModifyApplyMapper.selectSingleById(applyId);

        ModifyOrderMessageDto modifyOrderMessageDto = new ModifyOrderMessageDto();
        modifyOrderMessageDto.setSaleorderId(saleOrderId);
        modifyOrderMessageDto.setCreatorId(buyOrderModifyApply.getCreator());
        modifyOrderMessageDto.setBuyorderModifyApplyId(applyId);
        modifyOrderMessageDto.setValidStatus(validStatus);
        modifyOrderMessageDto.setIsWmsCancel(isWmsCancel);

        List<RBuyorderSaleorder> rBuyOrderSaleOrderList = buyorderSaleorderMapper.getRBuyorderSaleorderListByParam(buyOrderModifyApply.getBuyorderId());

        List<Buyorder2SaleorderGoodsDto> buyorder2SaleorderGoodsDtos = new LinkedList<>();
        for (BuyorderModifyApplyGoods buyorderModifyApplyGood : buyorderModifyApplyGoodsMapper.selectAllByApplyId(applyId)) {
            RBuyorderSaleorder rBuyorderSaleorder = rBuyOrderSaleOrderList.stream().filter(e -> Objects.equal(buyorderModifyApplyGood.getBuyorderGoodsId(), e.getBuyorderGoodsId())).findFirst().orElse(null);
            if (rBuyorderSaleorder == null) {
                continue;
            }

            Buyorder2SaleorderGoodsDto buyorder2SaleorderGoodsDto = new Buyorder2SaleorderGoodsDto();
            buyorder2SaleorderGoodsDto.setSaleorderGoodsId(rBuyorderSaleorder.getSaleorderGoodsId());
            buyorder2SaleorderGoodsDto.setDirectOrginalValue(buyOrderModifyApply.getOldDeliveryDirect());
            buyorder2SaleorderGoodsDto.setDirectNowValue(buyOrderModifyApply.getDeliveryDirect());
            buyorder2SaleorderGoodsDto.setDeliveryDirectChangeReason(buyOrderModifyApply.getDeliveryDirectChangeReason());
            buyorder2SaleorderGoodsDtos.add(buyorder2SaleorderGoodsDto);
        }

        if (!buyorder2SaleorderGoodsDtos.isEmpty()) {
            modifyOrderMessageDto.setBuyorder2SaleorderGoodsDtoList(buyorder2SaleorderGoodsDtos);
        }

        LOGGER.info("【采购单信息变更】采购单修改流程完成告知销售端，info: {}", modifyOrderMessageDto);

        try {
            orderInfoSyncService.editBuyOrderInfoSync(modifyOrderMessageDto);
        } catch (Exception e) {
            LOGGER.error("采购单修改流程完成调用销售端接口发生错误", e);
        }
    }

    @Override
    public Integer judgeIsNewBuyOrderModifyApply(Integer buyOrderModifyApplyId) throws InterruptedException {
        BuyOrderModifyApply buyOrderModifyApply = buyOrderModifyApplyMapper.selectSingleById(buyOrderModifyApplyId);
        if(buyOrderModifyApply == null){
            throw new InterruptedException("QueryResult 对应采购修改单不存在");
        }
        return Integer.valueOf(1).equals(buyOrderModifyApply.getIsNew())?Integer.valueOf(1):Integer.valueOf(0);
    }


    @Override
    public boolean cancelOutStorageRequestOfSaleOrder(String saleOrderNo) {

        return cancelTypeService.cancelOutSaleOutMethod(saleOrderNo, CancelReasonConstant.BUY_EDIT_ORDER);
    }


    private boolean sendSaleOrderOutStorageRequest(String saleOrderNo,User user) {
        boolean success = false;
        try {
            doSendSaleOrderOutStorageRequest(saleOrderNo,user);
            success = true;
        } catch (Exception e) {
            LOGGER.error("销售单下发WMS失败{}",saleOrderNo,e);
        }
        return success;
    }


    private void doSendSaleOrderOutStorageRequest(String saleOrderNo,User user)
            throws Exception {

        Saleorder order = new Saleorder();
        order.setSaleorderNo(saleOrderNo);
        order = saleorderMapper.getSaleorderByOrderNo(saleOrderNo);
        if (order == null) {
            return;
        }

//        User user = new User();
//        user.setUserId(ErpConst.NJ_ADMIN_ID);
//        user.setUsername(ErpConst.NJ_ADMIN_NAME);

        logicalSaleorderChooseService.chooseLogicalSaleorder(order, user);
    }
}
