package com.vedeng.finance.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.Company;
import com.vedeng.authorization.model.User;
import com.vedeng.billsync.dao.BankBillExtMapper;
import com.vedeng.billsync.task.model.entity.generate.BankBillDo;
import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.finance.dto.BankBillIgnoreRecordVo;
import com.vedeng.erp.finance.service.PayVedengBankApiService;
import com.vedeng.erp.kingdee.dto.result.KingDeeCustomerResultDto;
import com.vedeng.erp.kingdee.enums.KingdeeIgnoreBillTypeEnums;
import com.vedeng.erp.trader.dto.TraderBelongDto;
import com.vedeng.erp.trader.service.TraderCustomerApiService;
import com.vedeng.finance.dao.CapitalBillDetailMapper;
import com.vedeng.finance.dto.BankBillForPageDto;
import com.vedeng.finance.model.*;
import com.vedeng.finance.model.vo.SettlementMessageVo;
import com.vedeng.finance.service.*;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.OrderCommonService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.soap.service.VedengSoapService;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.CompanyService;
import com.vedeng.system.service.UserService;
import com.wms.service.other.LogicalSaleorderChooseServiceImpl;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <b>Description:</b><br>
 * 付款申请管理
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.finance.controller <br>
 * <b>ClassName:</b> PayApplyController <br>
 * <b>Date:</b> 2017年9月8日 下午2:47:24
 */
@Controller
@RequestMapping("/finance/bankbill")
public class BankBillController extends BaseController {

    @Autowired
    @Qualifier("bankBillService")
    private BankBillService bankBillService;
    @Resource
    private CapitalBillDetailMapper capitalBillDetailMapper;
    @Autowired
    @Qualifier("saleorderService")
    private SaleorderService saleorderService;

    @Autowired
    @Qualifier("companyService")
    private CompanyService companyService;

    @Autowired
    @Qualifier("capitalBillService")
    private CapitalBillService capitalBillService;

    @Autowired
    private BankBillExtMapper bankBillExtMapper;
    @Autowired
    @Qualifier("vedengSoapService")
    private VedengSoapService vedengSoapService;

    @Autowired
    @Qualifier("userService")
    private UserService userService;

    @Autowired
    @Qualifier("payApplyService")
    PayApplyService payApplyService;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired // 自动装载
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
    @Autowired
    private RedisUtils redisUtils;

    private final static String REDIS_KEY = "ERP:BATCH:SETTLEMENT:";

    @Autowired
    private LogicalSaleorderChooseServiceImpl logicalSaleorderChooseServiceImpl;
    @Resource
    private OrderCommonService orderCommonService;

    @Value("${batch_payment_customer}")
    private String batch_payment_customer;

    @Autowired
    KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    PayVedengBankApiService payVedengBankApiService;

    @Autowired
    private BankAndCapitalService bankAndCapitalService;

    /**
     * 银行流水列表
     */
    @ResponseBody
    @RequestMapping(value = "bankBillList")
    @NoNeedAccessAuthorization
    public ResultInfo<BankBillForPageDto> bankBillList(HttpServletRequest request, BankBill bankBill,
                                                       @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                                       @RequestParam(required = false) Integer pageSize,
                                                       @RequestParam(required = false, defaultValue = "0") Integer syncBankFlag,
                                                       @RequestParam(required = false, defaultValue = "1") Integer bankType) {
        log.info("新银行流水列表查询入参，bankBill:{}", JSON.toJSONString(bankBill));
        ResultInfo<BankBillForPageDto> resultInfo = ResultInfo.success();
        Page page = getPageTag(request, pageNo, pageSize);
        bankBill.setCompanyId(1);
        bankBill.setBankTag(bankType);
        bankBill.setSyncBankFlag(syncBankFlag);
        try {
            Map<String, Object> map = bankBillService.getBankBillListPage(bankBill, page);
            BankBillForPageDto bankBillForPageDto = new BankBillForPageDto();
            if (Objects.nonNull(map)) {
                List<BankBill> list = (List<BankBill>) map.get("list");
                bankBillForPageDto.setBankBillList(list);
            }
            bankBillForPageDto.setPage((Page) map.get("page"));
            resultInfo.setData(bankBillForPageDto);
            return resultInfo;
        } catch (Exception e) {
            logger.error("新银行流水列表查询异常", e);
            return ResultInfo.error("银行流水查询失败，请刷新页面后重试。");
        }
    }

    /**
     * <b>Description:</b><br>
     * 建行银行流水列表
     *
     * @param request
     * @param bankBill
     * @param session
     * @param pageNo
     * @param pageSize
     * @param searchBeginTime
     * @param searchEndTime
     * @return
     * @throws ParseException
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2017年9月15日 下午5:07:18
     */
    @ResponseBody
    @RequestMapping(value = "index")
    public ModelAndView index(HttpServletRequest request, BankBill bankBill, HttpSession session,
                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                              @RequestParam(required = false) Integer pageSize, @RequestParam(required = false, defaultValue = "1") Integer syncBankFlag,
                              @RequestParam(required = false, value = "beginTime") String searchBeginTime,
                              @RequestParam(required = false, value = "endTime") String searchEndTime,
                              @RequestParam(required = false, defaultValue = "1") Integer bankType) throws ParseException {
        ModelAndView mv = new ModelAndView();
        Page page = getPageTag(request, pageNo, pageSize);

        // 如果有搜索 时间就按照搜索时间，如果第一次进来设置默认时间为当前时间前一天
        if (null != searchEndTime && searchEndTime != "") {
            bankBill.setSearchEndtime(searchEndTime);
        } else {
            searchEndTime = DateUtil.convertString(DateUtil.sysTimeMillis(), DateUtil.DATE_FORMAT);
            bankBill.setSearchEndtime(searchEndTime);
        }

        if (null != searchBeginTime && searchBeginTime != "") {
            bankBill.setSearchBegintime(searchBeginTime);
        } else {
            searchBeginTime = DateUtil.convertString(DateUtil.sysTimeMillis(), DateUtil.DATE_FORMAT);
            bankBill.setSearchBegintime(searchBeginTime);
        }

        User user = (User) session.getAttribute(Consts.SESSION_USER);
        bankBill.setCompanyId(1);


        // changed by Randy.Xu   .Desc: begin
        //VDERP-3590 页面显示更改
        bankBill.setBankTag(bankType);
        bankBill.setCompanyId(1);
        // changed by Randy.Xu   .Desc: end


        try {
            bankBill.setSyncBankFlag(syncBankFlag);
            Map<String, Object> map = bankBillService.getBankBillListPage(bankBill, page);
            if (map != null) {
                mv.addObject("list", map.get("list"));
                mv.addObject("page", map.get("page"));
                mv.addObject("getAmount", map.get("getAmount"));
                mv.addObject("payAmount", map.get("payAmount"));
                mv.addObject("orderNum", map.get("orderNum"));
                mv.addObject("orderAmount", map.get("orderAmount"));
                mv.addObject("matchAmount", map.get("matchAmount"));
                mv.addObject("syncBankFlag", bankBill.getSyncBankFlag());
            }
        } catch (Exception e) {
            logger.error("bank bill:", e);
        }

        mv.addObject("beginTime", searchBeginTime);
        mv.addObject("endTime", searchEndTime);
        //获取当前日期
        Date date = new Date();
        String nowDate = DateUtil.DatePreMonth(date, 0, null);
        mv.addObject("nowDate", nowDate);

        //获取类型
        mv.addObject("bankType", bankType);

        mv.setViewName("finance/bankBill/index");
        return mv;
    }

    @Autowired
    private TraderCustomerApiService traderCustomerApiService;

    /**
     * <b>Description:</b><br>
     * 订单结款列表
     *
     * @param request
     * @param bankBill
     * @param session
     * @param pageNo
     * @param pageSize
     * @param searchBeginTime
     * @param searchEndTime
     * @return
     * @throws ParseException
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2017年9月15日 下午5:07:34
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "bankBillMatchList")
    public ModelAndView bankBillMatchList(HttpServletRequest request, BankBill bankBill, HttpSession session,
                                          @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                          @RequestParam(required = false) Integer pageSize, @RequestParam(required = false, defaultValue = "0") Integer syncBankFlag,
                                          @RequestParam(required = false, value = "beginTime") String searchBeginTime,
                                          @RequestParam(required = false, value = "endTime") String searchEndTime,
                                          @RequestParam(required = false, defaultValue = "1") Integer bankType) throws ParseException {
        ModelAndView mv = new ModelAndView();
        //默认100条
        if (pageSize == null) {
            pageSize = 100;
        }
        Page page = getPageTag(request, pageNo, pageSize);
        // 如果有搜索时间就按照搜索时间，如果第一次进来设置默认时间为当前时间前一天
        if (null != searchEndTime && searchEndTime != "") {
            bankBill.setSearchEndtime(searchEndTime);
        } else {
            searchEndTime = DateUtil.convertString(DateUtil.sysTimeMillis(), DateUtil.DATE_FORMAT);
            bankBill.setSearchEndtime(searchEndTime);
        }

        if (null != searchBeginTime && searchBeginTime != "") {
            bankBill.setSearchBegintime(searchBeginTime);
        } else {
            searchBeginTime = DateUtil.getDateStrBeforeDays(ErpConst.DAY_PERIOD_THIRTY);
            bankBill.setSearchBegintime(searchBeginTime);
        }

        User user = (User) session.getAttribute(Consts.SESSION_USER);
        bankBill.setCompanyId(user.getCompanyId());
        // changed by Randy.Xu   .Desc: begin
        //VDERP-3590 添加支付宝和微信的结款信息
        bankBill.setBankTag(bankType);
        mv.addObject("bankType", bankType);
        if (bankBill.getBankTag().equals(4) || bankBill.getBankTag().equals(5)) {
            bankBill.setFlag1(1);
        }
        // changed by Randy.Xu   .Desc: end


        bankBill.setSyncBankFlag(syncBankFlag);
        Map<String, Object> map;
        try {
            map = bankBillService.getBankBillMatchListPage(bankBill, page, true);
            // 补充订单剩余账期额度数据
            List<Integer> saleorderIdList = new ArrayList<>();
            //List<BankBill> bankBillInfo = (List<BankBill>) map.get("list");

            //暂时不清楚这里为什么这样处理
            List<BankBill> bankBillInfo = new ArrayList<>();
            if (map.containsKey("list") && CollectionUtils.isNotEmpty((List<BankBill>) map.get("list"))) {
                bankBillInfo = (List<BankBill>) map.get("list");
                Map<String, List<TraderBelongDto>> belongMap = new HashMap<>();
                if (CollUtil.isNotEmpty(bankBillInfo)) {
                    List<String> name = bankBillInfo.stream().map(BankBill::getAccName1).collect(Collectors.toList());
                    List<TraderBelongDto> traderBelongList = traderCustomerApiService.getTraderBelongInfo(name);
                    if (CollUtil.isNotEmpty(traderBelongList)) {
                        belongMap = traderBelongList.stream().distinct().collect(Collectors.groupingBy(TraderBelongDto::getTraderName, Collectors.toList()));
                        if (MapUtils.isEmpty(belongMap)) {
                            belongMap = new HashMap<>();
                        }
                    }
                }
                for (BankBill bankBill1 : bankBillInfo) {

                    if(CollUtil.isNotEmpty(bankBill1.getCapitalBillDetailList())){
                        bankBill1.getCapitalBillDetailList().forEach(d -> {
                            Integer traderId = d.getSaleorder().getTraderId();
                            List<TraderBelongDto> traderBelongInfoById = traderCustomerApiService.getTraderBelongInfoById(traderId);
                            log.info("traderBelongInfoById{}",JSON.toJSONString(traderBelongInfoById));
                            if (CollUtil.isNotEmpty(traderBelongInfoById)) {
                                TraderBelongDto first = CollUtil.getFirst(traderBelongInfoById);
                                d.getSaleorder().setOptUserName(first.getUserName());
                            }
                        });
                    }


                    List<TraderBelongDto> traderBelongDtos = belongMap.get(bankBill1.getAccName1());
                    if (CollUtil.isNotEmpty(traderBelongDtos)) {
                        List<String> userNameList = traderBelongDtos.stream().map(TraderBelongDto::getUserName).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(userNameList)) {
                            if (userNameList.size() == 1) {
                                bankBill1.setBelongUser(userNameList.get(0));
                            } else if (userNameList.size() > 1) {
                                bankBill1.setBelongUser("查询到多个客户");
                            }
                        } else {
                            String userName = tryAcquireUserName(bankBill1.getAccName1());
                            bankBill1.setBelongUser(userName);
                        }
                    } else {
                        String userName = tryAcquireUserName(bankBill1.getAccName1());
                        bankBill1.setBelongUser(userName);
                    }


                    //batch_payment_customer逗号分隔为字符串数组，只要数组中有一个和bankBill1.getAccName1()相等，就显示批量付款

                    if (!StringUtils.isEmpty(batch_payment_customer)) {
                        String[] split = batch_payment_customer.split(",");
                        for (String string : split) {
                            if (string.equals(bankBill1.getAccName1())) {
                                bankBill1.setIsShowBatchPay(1);
                                break;
                            }
                        }
                    } else {
                        bankBill1.setIsShowBatchPay(0);
                    }
                }
                mv.addObject("list", bankBillInfo);
            } else {
                mv.addObject("list", bankBillInfo);
            }
            //
            mv.addObject("page", map.get("page"));

            mv.addObject("getAmount", map.get("getAmount"));
            mv.addObject("payAmount", map.get("payAmount"));
            mv.addObject("orderNum", map.get("orderNum"));
            mv.addObject("orderAmount", map.get("orderAmount"));
            mv.addObject("matchAmount", map.get("matchAmount"));
            mv.addObject("syncBankFlag", bankBill.getSyncBankFlag());
            log.info("bankBillInfo{}", JSON.toJSON(bankBillInfo));
        } catch (Exception e) {
            logger.error("bankBillMatchList:", e);
        }
        mv.addObject("beginTime", searchBeginTime);
        mv.addObject("endTime", searchEndTime);
        //获取当前日期
        Date date = new Date();
        String nowDate = DateUtil.DatePreMonth(date, 0, null);
        mv.addObject("nowDate", nowDate);
        mv.setViewName("finance/bankBill/list_bankBillMatch");
        return mv;
    }

    /**
     * 将名称格式化后查询
     *
     * @param traderName
     * @return
     */
    private String tryAcquireUserName(String traderName) {
        try {
            if (traderName.contains("（") || traderName.contains("）") || traderName.contains("(") || traderName.contains(")")) {
                String zh = traderName.replace("(", "（").replace(")", "）");
                String eg = traderName.replace("（", "(").replace("）", ")");
                List<String> list = new ArrayList<>();
                list.add(zh);
                list.add(eg);
                List<TraderBelongDto> traderBelongList = traderCustomerApiService.getTraderBelongInfo(list);
                if (CollUtil.isNotEmpty(traderBelongList)) {
                    List<String> userNameList = traderBelongList.stream().map(TraderBelongDto::getUserName).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(userNameList)) {
                        if (userNameList.size() == 1) {
                            return userNameList.get(0);
                        } else if (userNameList.size() > 1) {
                            return "查询到多个客户";
                        }
                    } else {
                        return "未查询到客户";
                    }
                }
                return "未查询到客户";
            }
            return "未查询到客户";
        } catch (Exception e) {
            logger.info("tryAcquireUserName:", e);
            return "未查询到客户";
        }
    }


    @ExcludeAuthorization
    @RequestMapping(value = "/getbankBillMatchListDetail")
    public ModelAndView getbankBillMatchListDetail(BankBill bankBill,
                                                   @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                                   @RequestParam(required = false) Integer pageSize,
                                                   HttpServletRequest request,
                                                   HttpSession session,
                                                   @RequestParam(required = false, defaultValue = "0") Integer syncBankFlag,
                                                   @RequestParam(required = false, defaultValue = "1") Integer bankType) throws Exception {
        ModelAndView mv = new ModelAndView();

        if (pageSize == null) {
            pageSize = 10;
        }
        Page page = getPageTag(request, pageNo, pageSize);

        User user = (User) session.getAttribute(Consts.SESSION_USER);
        bankBill.setCompanyId(user.getCompanyId());
        // changed by Randy.Xu   .Desc: begin
        //VDERP-3590 添加支付宝和微信的结款信息
        bankBill.setBankTag(bankType);
        if (bankBill.getBankTag().equals(4) || bankBill.getBankTag().equals(5)) {
            bankBill.setFlag1(1);
        }
        bankBill.setSyncBankFlag(syncBankFlag);
        Map<String, Object> map;
        try {
            map = bankBillService.getBankBillMatchListPage(bankBill, page, false);
            List<BankBill> bankBillInfo = new ArrayList<>();
            if (map.containsKey("list") && CollectionUtils.isNotEmpty((List<BankBill>) map.get("list"))) {
                bankBillInfo = (List<BankBill>) map.get("list");
                bankBillInfo.forEach(bankBill1 -> {
                    if (!StringUtils.isEmpty(batch_payment_customer)) {
                        String[] split = batch_payment_customer.split(",");
                        for (String string : split) {
                            if (string.equals(bankBill1.getAccName1())) {
                                bankBill1.setIsShowBatchPay(1);
                                break;
                            }
                        }
                    } else {
                        bankBill1.setIsShowBatchPay(0);
                    }
                });
                mv.addObject("list", bankBillInfo);
                mv.setViewName("finance/bankBill/detail_bankBillMatch");
            } else {
                mv.addObject("list", new ArrayList<>());
                mv.setViewName("finance/bankBill/detail_bankBillMatch");
            }
        } catch (Exception e) {
            logger.error("bankBillMatchList:", e);
            mv.addObject("list", new ArrayList<>());
            mv.setViewName("finance/bankBill/detail_bankBillMatch");
        }
        return mv;
    }


    /**
     * <b>Description:</b><br>
     * 修改银行流水
     *
     * @param bankBill
     * @return
     * @throws Exception
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2017年9月26日 下午3:12:40
     */
    @ResponseBody
    @RequestMapping(value = "/editBankBill")
    public ResultInfo editBankBill(BankBill bankBill) throws Exception {
        ResultInfo result = new ResultInfo();
        result = bankBillService.editBankBill(bankBill);
        return result;
    }

    /**
     * <b>Description:</b><br>忽略匹配
     *
     * @param session
     * @param type    1.结款 2.付款
     * @return
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2017年9月26日 下午4:25:30
     */
    @ResponseBody
    @RequestMapping(value = "/addIgnore")
    public ModelAndView addIgnore(HttpSession session, Integer bankBillId, Integer type, @RequestParam(required = false) Integer bankTag) {
        ModelAndView mv = new ModelAndView();
        BankBillDo bankBillDo = bankBillExtMapper.getBankBillById(bankBillId);
        //删除原有逻辑，VDERP-15303不再区分结款、付款、支付宝等
        List<SysOptionDefinition> ignoreList = getSysOptionDefinitionList(SysOptionConstant.ID_4286);
        mv.addObject("ignoreList", JSONUtil.parseArray(ignoreList));
        mv.addObject("name", bankBillDo.getAccName1());
        mv.addObject("bankBillId", bankBillId);
        mv.setViewName("vue/view/finance/bank_bill_ignore");
        return mv;
    }

    /**
     * 变更'忽略原因'匹配'往来单位类型'
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/changeIgnoreReason")
    @ExcludeAuthorization
    public ResultInfo changeIgnoreReason(Integer ignoreReasonId) {
        Integer unitTypeId = KingdeeIgnoreBillTypeEnums.matchUnitTypeById(ignoreReasonId);
        return ResultInfo.success(unitTypeId);
    }

    @ResponseBody
    @RequestMapping("/getUnitNameList")
    @ExcludeAuthorization
    public ResultInfo getUnitNameList(String name, Integer unitTypeId, Integer bankBillId) {
        //根据名称和类型查询金蝶往来单位
        List<KingDeeCustomerResultDto> kingDeeUnitNameList = bankBillService.getKingDeeUnitNameList(name, unitTypeId, bankBillId);
        return ResultInfo.success(kingDeeUnitNameList);
    }

    @ResponseBody
    @RequestMapping("/saveIgnoreRecord")
    @ExcludeAuthorization
    public ResultInfo saveIgnoreRecord(HttpSession session, @RequestBody BankBillIgnoreRecordVo ignoreRecordVo) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        if (bankBillService.saveIgnoreRecord(user, ignoreRecordVo)) {
            return ResultInfo.success();
        }
        return ResultInfo.error();
    }


    /**
     * 订单结款时校验销售订单的剩余结款额
     *
     * @param amount
     * @param saleOrderId
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/checkSaleOrderResidueAmount", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo checkSaleOrderResidueAmount(BigDecimal amount, Integer saleOrderId) {
        log.info("自动结款提交校验checkSaleOrderResidueAmount start, saleOrderId:{}", saleOrderId);
        if (saleOrderId == null) {
            log.info("订单结款时，销售单号为空");
            return ResultInfo.error("订单结款时，销售单号为空");
        }
        if (amount == null || amount.compareTo(BigDecimal.ZERO) < 0) {
            log.info("订单结款时，本次拟结款金额小于0");
            return ResultInfo.error("订单结款时，本次拟结款金额小于0");
        }
        Saleorder saleorder = saleorderService.getsaleorderbySaleorderId(saleOrderId);
        if (Objects.isNull(saleorder)) {
            log.error("订单结款时获取订单信息失败 - saleOrderId:{}", saleOrderId);
            return ResultInfo.error("订单结款时获取订单信息失败");
        }
        // 若该订单的付款计划选择的是（先货后款，预付%0）,则查询当前订单有无信用支付记录
        if (OrderConstant.PREPAY_0_PERCENT.equals(saleorder.getPaymentType()) &&
                capitalBillService.getSaleOrderCreditPaymentRecord(saleOrderId) <= 0) {
            log.info("订单结款时，该订单为账期订单，但是目前销售未点击信用支付，无法结款！");
            return new ResultInfo(-2, "该订单为账期订单，但是目前销售未点击信用支付，无法结款！此时结款将会引发支付流水缺失，请联系销售先点击信用支付或者将订单修改为先款后货的非账期订单！");
        }
        Map<String, BigDecimal> saleOrderMoneyMap = saleorderService.getSaleorderDataInfo(saleOrderId);
        if (MapUtils.isEmpty(saleOrderMoneyMap)) {
            log.error("订单结款时资金数据信息失败 - saleOrderId:{}", saleOrderId);
            return ResultInfo.error("查询订单金额信息失败");
        }
        BigDecimal realAmount = saleOrderMoneyMap.get("realAmount");
        BigDecimal receivedAmount = saleOrderMoneyMap.get("receivedAmount");
        BigDecimal residueAmount = realAmount.subtract(receivedAmount);
        if (residueAmount == null) {
            logger.error("订单结款时，销售订单【saleorderId=" + saleOrderId + "】剩余结款为空");
            return new ResultInfo(-1, "订单款金额为空");
        }
        if (amount.compareTo(residueAmount) > 0) {
            logger.info("订单结款时，本次拟结款金额超出订单的需结款金额");
            return new ResultInfo(-1, "本次拟结款金额超出订单的需结款金额");
        }

        return ResultInfo.success();
    }

    /**
     * <b>Description:</b><br>
     * 银企直连结款，添加资金流水（只针对销售订单）
     *
     * @param session
     * @param capitalBill
     * @return
     * @Note <b>Author:</b> Michael <br>
     * <b>Date:</b> 2017年9月27日 下午4:15:03
     */
    @FormToken(remove = true, save = true)
    @ResponseBody
    @RequestMapping(value = "/addCapitalBill")
    public ResultInfo addCapitalBill(HttpServletRequest request, HttpSession session, CapitalBill capitalBill, Integer saleorderId,
                                     BigDecimal receivedAmount) {
        if (saleorderId == null) {
            return ResultInfo.error("订单结款时，销售单号为空");
        }
        if (capitalBill.getAmount() == null || capitalBill.getAmount().compareTo(BigDecimal.ZERO) < 0) {
            return ResultInfo.error("订单结款时，本次拟结款金额小于0");
        }


        try {
            String key = REDIS_KEY + capitalBill.getBankBillId();
            if (RedissonLockUtils.tryLock(key)) {
                log.info("结款加锁成功, key = [{}]", key);
                try {

                    // 获取订单信息
                    Saleorder saleorder = new Saleorder();
                    saleorder.setSaleorderId(saleorderId);
                    Saleorder saleorderInfo = saleorderService.getBaseSaleorderInfo(saleorder);

                    //add by Tomcat.Hui ******** 如果订单状态位待确认，则返回
                    if (null != saleorderInfo) {
                        if (saleorderInfo.getStatus().equals(4) && !saleorderInfo.getOrderType().equals(1)) {
                            return new ResultInfo<>(-1, "该订单待用户确认，请联系销售处理");
                        }

                        if (saleorderInfo.getStatus().equals(0) || saleorderInfo.getStatus().equals(3)) {
                            return new ResultInfo(-1, "订单处于待确认或已关闭状态,无法结款");
                        }

                    }
                    //add by Tomcat.Hui ******** 如果订单状态位待确认，则返回 end

                    User user = (User) session.getAttribute(Consts.SESSION_USER);
                    // 归属销售
                    User belongUser = new User();
                    if (saleorderInfo.getTraderId() != null) {
                        belongUser = userService.getUserByTraderId(saleorderInfo.getTraderId(), 1);// 1客户，2供应商
                        if (belongUser != null && belongUser.getUserId() != null) {
                            belongUser = userService.getUserById(belongUser.getUserId());
                        }
                    }

                    Company companyInfo = companyService.getCompanyByCompangId(user.getCompanyId());
                    // 获取银行流水信息
                    BankBill bankBill = bankBillService.getBankBillById(capitalBill.getBankBillId());
                    if (bankBill.getMatchedAmount().compareTo(BigDecimal.ZERO) < 0) {
                        return new ResultInfo(-1, "已匹配总额为负值");
                    }

                    //校验是否超出银行流水的剩余结款金额
                    BigDecimal amt = bankBill.getAmt();
                    BigDecimal matchedAmount = bankBill.getMatchedAmount();
                    BigDecimal residueAmountFromBankBill = amt.subtract(matchedAmount);
                    if (capitalBill.getAmount().compareTo(residueAmountFromBankBill) > 0) {
                        return new ResultInfo(-1, "超出流水的\"剩余结款金额\"，无法操作");
                    }

                    // 获取订单已付款金额
                    //	Saleorder s = capitalBillService.getSaleorderCapitalById(saleorderId);
                    // 资金流水赋值
                    if (user != null) {
                        capitalBill.setCreator(user.getUserId());
                        capitalBill.setAddTime(DateUtil.sysTimeMillis());
                        capitalBill.setCompanyId(user.getCompanyId());
                    }
                    // 根据借款金额判断是付预付款，还是付账期
                    BigDecimal totalAmount = saleorderInfo.getTotalAmount();

                    ResultInfo<?> result = new ResultInfo<>();
                    // 订单总额 ！= 订单已付款金额+剩余账期还款金额
                    if (totalAmount != receivedAmount) {
                        // 剩余预付款金额 订单预付款金额-(订单已付款金额+剩余账期还款金额)
                        BigDecimal residue = saleorderInfo.getPrepaidAmount().subtract(receivedAmount);
                        // 如果剩余预付款金额大于订单借款金额
                        // changed by Randy.Xu   .Desc: begin
                        // 交易方式银行
                        if (null != bankBill.getBankTag() && bankBill.getBankTag().equals(4)) {
                            capitalBill.setTraderMode(520);
                        } else {
                            capitalBill.setTraderMode(521);
                        }
                        // changed by Randy.Xu   .Desc: end
                        capitalBill.setTranFlow(bankBill.getTranFlow());
                        capitalBill.setCurrencyUnitId(1);
                        capitalBill.setTraderTime(DateUtil.sysTimeMillis());
                        capitalBill.setTraderType(1);
                        capitalBill.setPayerBankAccount(bankBill.getAccno2());
                        capitalBill.setPayerBankName(bankBill.getCadbankNm());
                        capitalBill.setPayer(bankBill.getAccName1());
                        capitalBill.setPayee(companyInfo == null ? "" : companyInfo.getCompanyName());

                        List<CapitalBillDetail> capitalBillDetails = new ArrayList<>();
                        CapitalBillDetail capitalBillDetail = new CapitalBillDetail();

                        capitalBillDetail.setOrderType(1);
                        capitalBillDetail.setOrderNo(saleorderInfo.getSaleorderNo());
                        capitalBillDetail.setRelatedId(saleorderInfo.getSaleorderId());
                        capitalBillDetail.setTraderType(1);
                        capitalBillDetail.setTraderId(saleorderInfo.getTraderId());
                        capitalBillDetail.setUserId(saleorderInfo.getUserId());
                        capitalBillDetail.setBussinessType(526);//交易类型订单收款
                        capitalBillDetail.setAmount(capitalBill.getAmount());
                        if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
                            capitalBillDetail.setOrgName(belongUser.getOrgName());
                            capitalBillDetail.setOrgId(belongUser.getOrgId());
                        }
                        // 订单收款
                        capitalBillDetails.add(capitalBillDetail);
                        capitalBill.setCapitalBillDetails(capitalBillDetails);

                        CapitalBillDetail capitalBillDetailInfo = new CapitalBillDetail();
                        capitalBillDetailInfo.setOrderType(1);
                        capitalBillDetailInfo.setOrderNo(saleorderInfo.getSaleorderNo());
                        capitalBillDetailInfo.setRelatedId(saleorderInfo.getSaleorderId());
                        capitalBillDetailInfo.setTraderType(1);
                        capitalBillDetailInfo.setTraderId(saleorderInfo.getTraderId());
                        capitalBillDetailInfo.setUserId(saleorderInfo.getUserId());
                        capitalBillDetailInfo.setBussinessType(526);//交易类型订单收款
                        capitalBillDetailInfo.setAmount(capitalBill.getAmount());
                        if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
                            capitalBillDetailInfo.setOrgName(belongUser.getOrgName());
                            capitalBillDetailInfo.setOrgId(belongUser.getOrgId());
                        }
                        //VDERP-1327  订单结款自动生成流水记录
                        String payer = capitalBill.getPayer();
                        capitalBill.setCapitalBillDetail(capitalBillDetailInfo);
                        result = capitalBillService.saveAddCapitalBill(capitalBill);
                        //VDERP-1327  订单结款自动生成流水记录
                        if (!StringUtils.isEmpty(payer) && result.getCode() == 0) {
                            if (payer.equals(ErpConst.TAOBAO) || payer.equals(ErpConst.WEIXIN)) {
                                ResultInfo resultInfo = capitalBillService.saveSecondCapitalBill(saleorderInfo, capitalBill);
                            }
                        }
                        //VDERP-1327  订单结款自动生成流水记录
                    }

                    //调用库存服务
                    if (result.getCode() == 0) {
                        logicalSaleorderChooseServiceImpl.chooseLogicalSaleorder(saleorderInfo, user);
                        //VDERP-2263   订单售后采购改动通知
                        orderCommonService.updateSaleOrderDataUpdateTime(saleorderId, null, OrderDataUpdateConstant.SALE_ORDER_PAY);
                        String formToken = (String) request.getAttribute("formToken");
                        result.setMessage(result.getMessage() + "::" + formToken);
                    }

                    if (result.getCode() == 0 && Objects.nonNull(belongUser) && !StringUtils.isEmpty(belongUser.getUserId())) {
                        log.info("发送结款信息:{}", saleorderInfo.getSaleorderNo());
                        SettlementMessageVo settlementMessageVo = new SettlementMessageVo();
                        settlementMessageVo.setUserId(belongUser.getUserId());
                        settlementMessageVo.setSaleOrderNo(saleorderInfo.getSaleorderNo());
                        settlementMessageVo.setTraderName(saleorderInfo.getTraderName());
                        settlementMessageVo.setActualAmount(saleorderInfo.getTotalAmount());
                        settlementMessageVo.setCurrentAmount(capitalBill.getAmount());

                        BigDecimal totalAmount1 = saleorderInfo.getTotalAmount();
                        BigDecimal realPayAmount = saleorderInfo.getRealPayAmount();
                        BigDecimal realPayAmount1 = realPayAmount.add(capitalBill.getAmount());
                        settlementMessageVo.setRemainAmount(totalAmount1.subtract(realPayAmount1));
                        financeSendMessageService.sendMessage(settlementMessageVo);
                    } else {
                        log.info("发送结款信息失败：{}", saleorderInfo.getSaleorderNo());
                    }
                    return result;
                } finally {
                    RedissonLockUtils.unlock(key);
                    log.info("结款释放锁成功, key = [{}]", key);
                }
            } else {
                log.error("结款获取锁失败,超出等待时长, key = [{}]", key);
                throw new Exception("结款获取锁失败,请稍后重试");
            }

        } catch (Exception e) {
            log.error("订单结款时发生错误", e);
            return ResultInfo.error("订单结款时系统内部发生错误");
        }
    }

    @Resource
    private FinanceSendMessageService financeSendMessageService;

    @FormToken(save = true)
    @RequestMapping(value = "/getManualMatchInfo")
    public ModelAndView getManualMatchInfo(HttpServletRequest request, Integer bankBillId, String search,
                                           @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                           @RequestParam(required = false, defaultValue = "10") Integer pageSize, HttpSession session,
                                           @RequestParam(required = false) Integer bankTag) {
        ModelAndView mv = new ModelAndView();

        Page page = getPageTag(request, pageNo, pageSize);
        // 获取银行流水信息
        BankBill bankBill = bankBillService.getBankBillById(bankBillId);
        Map<String, Object> map;
        if (null != search) {
            // 搜索订单
            Saleorder saleorder = new Saleorder();
            saleorder.setSearch(search);
            saleorder.setValidStatus(1);
            saleorder.setStatus(-2);
            if (null != bankTag && (bankTag == 4 || bankTag == 5)) {
                saleorder.setIsHandMatch(1);
                bankBill.setBankTag(bankTag);
            }
            map = saleorderService.getSaleorderListPage(request, saleorder, page, 0);
            List<Saleorder> saleorderList = (List<Saleorder>) map.get("saleorderList");
            if (null != saleorderList) {
                for (Saleorder s : saleorderList) {

                    Saleorder saleorderInfo = capitalBillService.getSaleorderCapitalById(s.getSaleorderId());
                    // 获取订单已付款金额
                    if (null != saleorderInfo && null != saleorderInfo.getReceivedAmount()) {
                        s.setReceivedAmount(saleorderInfo.getReceivedAmount());
                    }
                    if (null != saleorderInfo && null != saleorderInfo.getRealAmount()) {
                        s.setRealAmount(saleorderInfo.getRealAmount());
                    }
                }
            }
            // 如果有查到订单的话
            if (null != saleorderList && !saleorderList.isEmpty()) {
                List<Integer> saleorderIdList = new ArrayList<>();
                for (Saleorder s : saleorderList) {
                    saleorderIdList.add(s.getSaleorderId());
                }
                //List<SaleorderData> saleorderDataList = capitalBillService.getCapitalListBySaleorderId(saleorderIdList);
            }
            mv.addObject("search", search);
            mv.addObject("saleorderList", saleorderList);
            mv.addObject("page", map.get("page"));
        }

        mv.addObject("bankBill", bankBill);
        mv.setViewName("finance/bankBill/list_manualMatch");
        return mv;
    }

    /**
     * 银行流水发送至金蝶
     * <b>Description:</b><br>
     *
     * @param request
     * @return
     * @Note <b>Author:</b> Bill
     * <br><b>Date:</b> 2018年5月28日 下午7:12:42
     */
    @ResponseBody
    @RequestMapping(value = "/sendbankbilllist")
    public synchronized ResultInfo sendBankBillList(HttpServletRequest request, HttpSession session, BankBill bankBill) {
        logger.info("银行流水发送至金蝶....start");
        String isExecuting = redisUtils.get(dbType + "sendbankbilllist-lock");
        if (StringUtils.isEmpty(isExecuting)) {
            String time = System.nanoTime() + "";
            redisUtils.set(dbType + "sendbankbilllist-lock", time, 43200);
            try {
                Page page = getPageTag(request, 1, 1000);
                User user = (User) session.getAttribute(Consts.SESSION_USER);
                bankBill.setCompanyId(user.getCompanyId());
                bankBill.setUserIdNow(user.getUserId());
                ResultInfo resultInfo = bankBillService.sendBankBillList(bankBill, page, session);
                return resultInfo;
            } catch (Exception e) {
                logger.error("sendbankbilllist:", e);
            } finally {
                logger.info("银行流水发送至金蝶....end 已执行");
                redisUtils.del(dbType + "sendbankbilllist-lock");
            }
        } else {
            logger.info("银行流水发送至金蝶....end 重复操作未执行");
        }
        return new ResultInfo();
    }

    /**
     * <b>Description:</b><br> 南京银行流水列表
     *
     * @param request
     * @param bankBill
     * @param session
     * @param pageNo
     * @param pageSize
     * @param searchBeginTime
     * @param searchEndTime
     * @return
     * @throws ParseException
     * @Note <b>Author:</b> Administrator
     * <br><b>Date:</b> 2018年7月5日 下午1:43:43
     */
    @ResponseBody
    @RequestMapping(value = "njindex")
    public ModelAndView njindex(HttpServletRequest request, BankBill bankBill, HttpSession session,
                                @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                @RequestParam(required = false) Integer pageSize,
                                @RequestParam(required = false, value = "beginTime") String searchBeginTime,
                                @RequestParam(required = false, value = "endTime") String searchEndTime) throws ParseException {
        ModelAndView mv = new ModelAndView();
        Page page = getPageTag(request, pageNo, pageSize);
        // 如果有搜索时间就按照搜索时间，如果第一次进来设置默认时间为当前时间前一天
        if (null != searchEndTime && searchEndTime != "") {
            bankBill.setSearchEndtime(searchEndTime);
        } else {
            searchEndTime = DateUtil.convertString(DateUtil.sysTimeMillis(), DateUtil.DATE_FORMAT);
            bankBill.setSearchEndtime(searchEndTime);
        }

        if (null != searchBeginTime && searchBeginTime != "") {
            bankBill.setSearchBegintime(searchBeginTime);
        } else {
            searchBeginTime = DateUtil.convertString(DateUtil.sysTimeMillis(), DateUtil.DATE_FORMAT);
            bankBill.setSearchBegintime(searchBeginTime);
        }

        User user = (User) session.getAttribute(Consts.SESSION_USER);
        bankBill.setCompanyId(user.getCompanyId());

        //南京银行
        bankBill.setBankTag(2);
        //bankBill.setSyncBankFlag(1);
        try {
            Map<String, Object> map = bankBillService.getBankBillListPage(bankBill, page);
            if (map != null) {
                //匹配项目
                List<SysOptionDefinition> macthObjectList = getSysOptionDefinitionList(856);
                mv.addObject("macthObjectList", macthObjectList);
                mv.addObject("list", map.get("list"));
                mv.addObject("page", map.get("page"));
                mv.addObject("getAmount", map.get("getAmount"));
                mv.addObject("payAmount", map.get("payAmount"));
                mv.addObject("orderNum", map.get("orderNum"));
                mv.addObject("orderAmount", map.get("orderAmount"));
                mv.addObject("matchAmount", map.get("matchAmount"));
            }
        } catch (Exception e) {
            logger.error("njindex:", e);
        }

        mv.addObject("beginTime", searchBeginTime);
        mv.addObject("endTime", searchEndTime);
        //获取当前日期
        Date date = new Date();
        String nowDate = DateUtil.DatePreMonth(date, 0, null);
        mv.addObject("nowDate", nowDate);
        mv.addObject("syncBankFlag", bankBill.getSyncBankFlag());
        mv.setViewName("finance/bankBill/njindex");
        return mv;

    }

    /**
     * <b>Description:</b><br> 订单付款匹配列表
     *
     * @param request
     * @param bankBill
     * @param session
     * @param pageNo
     * @param pageSize
     * @param searchBeginTime
     * @param searchEndTime
     * @return
     * @throws ParseException
     * @Note <b>Author:</b> Michael
     * <br><b>Date:</b> 2018年8月6日 上午9:46:08
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "bankBillPayMatchList")
    public ModelAndView bankBillPayMatchList(HttpServletRequest request, BankBill bankBill, HttpSession session,
                                             @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                             @RequestParam(required = false) Integer pageSize,
                                             @RequestParam(required = false, value = "beginTime") String searchBeginTime,
                                             @RequestParam(required = false, value = "endTime") String searchEndTime,
                                             @RequestParam(required = false, defaultValue = "3") Integer bankType) throws ParseException {
        ModelAndView mv = new ModelAndView();
        //默认100条
        if (pageSize == null) {
            pageSize = 100;
        }
        Page page = getPageTag(request, pageNo, pageSize);
        // 如果有搜索时间就按照搜索时间，如果第一次进来设置默认时间为当前时间前一天
        if (null != searchEndTime && searchEndTime != "") {
            bankBill.setSearchEndtime(searchEndTime);
        } else {
            searchEndTime = DateUtil.convertString(DateUtil.sysTimeMillis(), DateUtil.DATE_FORMAT);
            bankBill.setSearchEndtime(searchEndTime);
        }

        if (null != searchBeginTime && searchBeginTime != "") {
            bankBill.setSearchBegintime(searchBeginTime);
        } else {
            searchBeginTime = DateUtil.convertString(DateUtil.sysTimeMillis(), DateUtil.DATE_FORMAT);
            bankBill.setSearchBegintime(searchBeginTime);
        }

        User user = (User) session.getAttribute(Consts.SESSION_USER);
        bankBill.setCompanyId(user.getCompanyId());
        //南京银行流水
//		if(bankBill.getBankTag() == null){
//			bankBill.setBankTag(2);
//		}

        // changed by Randy.Xu   .Desc: begin
        //VDERP-3590 添加支付宝和微信的付款流水信息
        if (bankBill.getBankTag() == null) {
            bankBill.setBankTag(bankType);
        }
        if (bankBill.getBankTag().equals(4) || bankBill.getBankTag().equals(5)) {
            bankBill.setFlag1(0);
        }
        bankBill.setCompanyId(1);
        // changed by Randy.Xu   .Desc: end

        Map<String, Object> map;
        try {
            map = bankBillService.getBankBillPayMatchListPage(bankBill, page);
            // 补充订单剩余账期额度数据
            List<Integer> saleorderIdList = new ArrayList<>();
            List<BankBill> bankBillInfo = (List<BankBill>) map.get("list");
            if (bankBillInfo != null) {
                // 获取订单审核信息
                TaskService taskService = processEngine.getTaskService(); // 任务相关service
                for (int i = 0; i < bankBillInfo.size(); i++) {
                    if (bankBillInfo.get(i).getCapitalBillDetailList() != null) {
                        for (int j = 0; j < bankBillInfo.get(i).getCapitalBillDetailList().size(); j++) {
                            // 获取当前活动节点
                            Task taskInfoPay = taskService.createTaskQuery().processInstanceBusinessKey("paymentVerify_" + bankBillInfo.get(i).getCapitalBillDetailList().get(j).getPayApply().getPayApplyId())
                                    .singleResult();
                            if (null != taskInfoPay) {
                                bankBillInfo.get(i).getCapitalBillDetailList().get(j).getPayApply().setTaskInfoPayId(taskInfoPay.getId());
                            }
                        }
                    }
                }
            }

            mv.addObject("list", bankBillInfo);
            mv.addObject("page", map.get("page"));

            mv.addObject("getAmount", map.get("getAmount"));
            mv.addObject("payAmount", map.get("payAmount"));
            mv.addObject("orderNum", map.get("orderNum"));
            mv.addObject("orderAmount", map.get("orderAmount"));
            mv.addObject("matchAmount", map.get("matchAmount"));
        } catch (Exception e) {
            logger.error("bankBillPayMatchList:", e);
        }
        mv.addObject("beginTime", searchBeginTime);
        mv.addObject("endTime", searchEndTime);
        // add by Randy.Xu 2020/12/21 10:25 .Desc: . begin
        // VDERP-3590 添加支付宝和微信的付款流水信息
        mv.addObject("bankType", bankType);
        // add by Randy.Xu 2020/12/21 10:25 .Desc: . end

        //获取当前日期
        Date date = new Date();
        String nowDate = DateUtil.DatePreMonth(date, 0, null);
        mv.addObject("nowDate", nowDate);
        mv.setViewName("finance/bankBill/list_bankBillPayMatch");
        return mv;
    }

    /**
     * <b>Description:</b><br> 订单付款手动匹配
     *
     * @param request
     * @param bankBillId
     * @param search
     * @param pageNo
     * @param pageSize
     * @param session
     * @return
     * @Note <b>Author:</b> Michael
     * <br><b>Date:</b> 2018年8月23日 上午10:16:16
     */
    @FormToken(save = true)
    @RequestMapping(value = "/getManualMatchPayInfo")
    public ModelAndView getManualMatchPayInfo(HttpServletRequest request, Integer bankBillId, String search,
                                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                              @RequestParam(required = false, defaultValue = "10") Integer pageSize, HttpSession session,
                                              @RequestParam(required = false) Integer bankTag) {
        ModelAndView mv = new ModelAndView();
        Page page = getPageTag(request, pageNo, pageSize);
        // 获取银行流水信息
        BankBill bankBill = bankBillService.getBankBillById(bankBillId);
        Map<String, Object> map;
        if (null != search) {
            // 搜索订单
            PayApply payApply = new PayApply();
            payApply.setSearch(search.trim());
            payApply.setValidStatus(0);
            payApply.setIsBill(1);
            if (null != bankTag && (bankTag.equals(4) || bankTag.equals(5))) {
                payApply.setIsHandMatch(1);
                bankBill.setBankTag(bankTag);
            }
            map = payApplyService.getPayApplyListPage(request, payApply, page);
            List<PayApply> payApplyList = (List<PayApply>) map.get("payApplyList");
            if (payApplyList != null) {
                // 获取订单审核信息
                TaskService taskService = processEngine.getTaskService(); // 任务相关service
                for (int i = 0; i < payApplyList.size(); i++) {
                    if (payApplyList.get(i).getPayApplyId() != null) {
                        // 获取当前活动节点
                        Task taskInfoPay = taskService.createTaskQuery().processInstanceBusinessKey("paymentVerify_" + payApplyList.get(i).getPayApplyId())
                                .singleResult();
                        payApplyList.get(i).setTaskInfoPayId(taskInfoPay.getId());
                    }
                }
            }
            mv.addObject("search", search);
            mv.addObject("payApplyList", payApplyList);
            mv.addObject("page", map.get("page"));
        }

        mv.addObject("bankBill", bankBill);
        mv.setViewName("finance/bankBill/list_manualMatchPay");
        return mv;
    }


    /**
     * 发送付款记录到金蝶
     * <b>Description:</b><br>
     *
     * @param request
     * @param session
     * @return
     * @throws Exception
     * @Note <b>Author:</b> Bill
     * <br><b>Date:</b> 2018年8月3日 上午11:23:33
     */
    @ResponseBody
    @RequestMapping(value = "/sendpaybilltokindlee")
    public ResultInfo sendPayBillToKindlee(HttpServletRequest request, HttpSession session, BankBill bankBill) throws Exception {
        Page page = getPageTag(request, 1, 1000);
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        bankBill.setCompanyId(user.getCompanyId());
        bankBill.setUserIdNow(user.getUserId());
        logger.info("付款流水发送至金蝶.....开始");
        ResultInfo resultInfo = bankBillService.sendPayBillToKindlee(bankBill, page, session);
        logger.info("付款流水发送至金蝶.....结束");
        return resultInfo;
    }


    /**
     * <b>Description:</b><br> 中国银行流水列表
     *
     * @param request
     * @param bankBill
     * @param session
     * @param pageNo
     * @param pageSize
     * @param searchBeginTime
     * @param searchEndTime
     * @return
     * @throws ParseException
     * @Note <b>Author:</b> Michael
     * <br><b>Date:</b> 2018年9月18日 下午1:43:43
     */
    @ResponseBody
    @RequestMapping(value = "chinaIndex")
    public ModelAndView chinaIndex(HttpServletRequest request, BankBill bankBill, HttpSession session,
                                   @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                   @RequestParam(required = false) Integer pageSize,
                                   @RequestParam(required = false, value = "beginTime") String searchBeginTime,
                                   @RequestParam(required = false, value = "endTime") String searchEndTime
    ) throws ParseException {
        ModelAndView mv = new ModelAndView();
        Page page = getPageTag(request, pageNo, pageSize);
        // 如果有搜索时间就按照搜索时间，如果第一次进来设置默认时间为当前时间前一天
        if (null != searchEndTime && searchEndTime != "") {
            bankBill.setSearchEndtime(searchEndTime);
        } else {
            searchEndTime = DateUtil.convertString(DateUtil.sysTimeMillis(), DateUtil.DATE_FORMAT);
            bankBill.setSearchEndtime(searchEndTime);
        }

        if (null != searchBeginTime && searchBeginTime != "") {
            bankBill.setSearchBegintime(searchBeginTime);
        } else {
            searchBeginTime = DateUtil.convertString(DateUtil.sysTimeMillis(), DateUtil.DATE_FORMAT);
            bankBill.setSearchBegintime(searchBeginTime);
        }

        User user = (User) session.getAttribute(Consts.SESSION_USER);
        bankBill.setCompanyId(user.getCompanyId());

        //中国银行
        bankBill.setBankTag(3);
        //实时拉取 前台传递
        //bankBill.setSyncBankFlag(syncBankFlag);
        try {
            Map<String, Object> map = bankBillService.getBankBillListPage(bankBill, page);
            if (map != null) {
                //匹配项目
                List<SysOptionDefinition> macthObjectList = getSysOptionDefinitionList(856);
                mv.addObject("macthObjectList", macthObjectList);
                mv.addObject("list", map.get("list"));
                mv.addObject("page", map.get("page"));
                mv.addObject("getAmount", map.get("getAmount"));
                mv.addObject("payAmount", map.get("payAmount"));
                mv.addObject("orderNum", map.get("orderNum"));
                mv.addObject("orderAmount", map.get("orderAmount"));
                mv.addObject("matchAmount", map.get("matchAmount"));
            }
        } catch (Exception e) {
            logger.error("chinaIndex:", e);
        }

        mv.addObject("beginTime", searchBeginTime);
        mv.addObject("endTime", searchEndTime);
        mv.addObject("syncBankFlag", bankBill.getSyncBankFlag());
        //获取当前日期
        Date date = new Date();
        String nowDate = DateUtil.DatePreMonth(date, 0, null);
        mv.addObject("nowDate", nowDate);
        mv.setViewName("finance/bankBill/chinaIndex");
        return mv;
    }

    /**
     * 民生银行流水列表
     *
     * @param request
     * @param bankBill
     * @param session
     * @param pageNo
     * @param pageSize
     * @param searchBeginTime
     * @param searchEndTime
     * @return
     * <AUTHOR>
     */

    @ResponseBody
    @RequestMapping(value = "/msyhIndex")
    public ModelAndView msyhIndex(HttpServletRequest request, BankBill bankBill, HttpSession session,
                                  @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                  @RequestParam(required = false) Integer pageSize,
                                  @RequestParam(required = false, value = "beginTime") String searchBeginTime,
                                  @RequestParam(required = false, value = "endTime") String searchEndTime) {
        ModelAndView mv = new ModelAndView();
        Page page = getPageTag(request, pageNo, pageSize);
        // 如果有搜索时间就按照搜索时间，如果第一次进来设置默认时间为当前时间前一天
        if (null != searchEndTime && searchEndTime != "") {
            bankBill.setSearchEndtime(searchEndTime);
        } else {
            searchEndTime = DateUtil.convertString(DateUtil.sysTimeMillis(), DateUtil.DATE_FORMAT);
            bankBill.setSearchEndtime(searchEndTime);
        }

        if (null != searchBeginTime && searchBeginTime != "") {
            bankBill.setSearchBegintime(searchBeginTime);
        } else {
            searchBeginTime = DateUtil.convertString(DateUtil.sysTimeMillis(), DateUtil.DATE_FORMAT);
            bankBill.setSearchBegintime(searchBeginTime);
        }

        User user = (User) session.getAttribute(Consts.SESSION_USER);
        bankBill.setCompanyId(user.getCompanyId());

        //民生银行
        bankBill.setBankTag(7);
        //
        bankBill.setSyncBankFlag(ErpConst.ZERO);
        try {
            Map<String, Object> map = bankBillService.getBankBillListPage(bankBill, page);
            if (map != null) {
                //匹配项目
                List<SysOptionDefinition> macthObjectList = getSysOptionDefinitionList(856);
                mv.addObject("macthObjectList", macthObjectList);
                mv.addObject("list", map.get("list"));
                mv.addObject("page", map.get("page"));
                mv.addObject("getAmount", map.get("getAmount"));
                mv.addObject("payAmount", map.get("payAmount"));
                mv.addObject("orderNum", map.get("orderNum"));
                mv.addObject("orderAmount", map.get("orderAmount"));
                mv.addObject("matchAmount", map.get("matchAmount"));
            }
        } catch (Exception e) {
            logger.error("msyhIndex:", e);
        }

        mv.addObject("beginTime", searchBeginTime);
        mv.addObject("endTime", searchEndTime);
        mv.addObject("syncBankFlag", bankBill.getSyncBankFlag());
        //获取当前日期
        Date date = new Date();
        String nowDate = DateUtil.DatePreMonth(date, 0, null);
        mv.addObject("nowDate", nowDate);
        mv.setViewName("finance/bankBill/msyhIndex");
        return mv;
    }

    /**
     * 中国银行流水推送金蝶
     * <b>Description:</b>
     *
     * @param request
     * @param session
     * @param bankBill
     * @return
     * @throws Exception ResultInfo
     * @Note <b>Author：</b> bill.bo
     * <b>Date:</b> 2018年10月18日 下午2:45:16
     */
    @ResponseBody
    @RequestMapping(value = "/sendchpaybilltokindlee")
    public ResultInfo<T> sendChainaBankBillToKindlee(HttpServletRequest request, HttpSession session, BankBill bankBill) throws Exception {
        Page page = getPageTag(request, 1, 1000);
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        bankBill.setCompanyId(user.getCompanyId());
        bankBill.setUserIdNow(user.getUserId());
        ResultInfo<T> resultInfo = bankBillService.sendChPayBillToKindlee(bankBill, page, session);

        return resultInfo;
    }

    /**
     * 批量结款导表格
     *
     * @param request
     * @return
     * <AUTHOR>
     */
    @ResponseBody
    @RequestMapping(value = "/bankBillBatchInit")
    public ModelAndView batchUpdateGoodsInit(HttpServletRequest request, Integer bankBillId, String bankAccName) {

        ModelAndView mv = new ModelAndView();
        mv.addObject("bankBillId", bankBillId);
        mv.addObject("bankAccName", bankAccName);
        mv.setViewName("finance/bankBill/batchBankBill");
        return mv;
    }


    @ResponseBody
    @RequestMapping(value = "/bankBillBatchInitAndConfirm")
    @ExcludeAuthorization
    public ModelAndView bankBillBatchInitAndConfirm(HttpServletRequest request, Integer bankBillId, String bankAccName) {

        ModelAndView mv = new ModelAndView();
        mv.addObject("bankBillId", bankBillId);
        mv.addObject("bankAccName", bankAccName);
        mv.setViewName("finance/bankBill/importData");
        return mv;
    }


    /**
     * 批量結款
     *
     * @param request
     * @param lwfile
     * @return
     * <AUTHOR>
     */
    @MethodLock(className = CapitalBill.class, field = "bankBillId", time = 1800000)
    @ResponseBody
    @RequestMapping("bankBillBatch")
    @SystemControllerLog(operationType = "import", desc = "导入批量结款数据")
    public ResultInfo<?> saveUplodeTaxCategoryNo(HttpServletRequest request, CapitalBill capitalBill, String bankAccName,
                                                 @RequestParam("lwfile") MultipartFile lwfile) {
        ResultInfo<?> resultInfo = new ResultInfo<>();
        String bankAccName1 = null;
        try {
            bankAccName1 = URLDecoder.decode(bankAccName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            logger.error("字符串转换：", e);
        }

        // 获取银行流水信息
        BankBill bankBill = bankBillService.getBankBillById(capitalBill.getBankBillId());
        //获取剩余款额
        BigDecimal finalamt = bankBill.getAmt().subtract(bankBill.getMatchedAmount());

        BigDecimal billCount = BigDecimal.valueOf(0.00);

        InputStream inputStream = null;
        try {
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            // 临时文件存放地址
            String path = request.getSession().getServletContext().getRealPath("/upload/goods");
            FileInfo fileInfo = ftpUtilService.fileUploadServe(path, lwfile);
            if (fileInfo.getCode() == 0) {
                // 获取excel路径
                inputStream = new FileInputStream(new File(fileInfo.getFilePath()));
                Workbook workbook = WorkbookFactory.create(inputStream);
                // 获取第一面sheet
                Sheet sheet = workbook.getSheetAt(0);
                // 起始行
                int startRowNum = sheet.getFirstRowNum() + 1;
                int endRowNum = sheet.getLastRowNum();// 结束行
                Row rowBEcell = sheet.getRow(sheet.getFirstRowNum());//获取第一行的单元个数
                int startCellNum = rowBEcell.getFirstCellNum();// 起始列
                int endCellNum = rowBEcell.getLastCellNum() - 1;//结束列
                //创建调用接口的集合
                List<BatchBillInfo> useAddCapitalBill = new ArrayList<>();
                BatchBillInfo batchBillInfo = null;
                CapitalBill capitalBillBatch = null;
                Integer saleOrderIdBatch = null;

                for (int rowNum = startRowNum; rowNum <= endRowNum; rowNum++) {// 循环行数
                    Row row = sheet.getRow(rowNum);
                    //创建接口需要的条件
                    batchBillInfo = new BatchBillInfo();
                    capitalBillBatch = new CapitalBill();
                    capitalBillBatch.setBankBillId(capitalBill.getBankBillId());
                    saleOrderIdBatch = 0;
                    // 获取excel的值
                    //此处本来是cellNum = startCellNum，改为cellNum = 0
                    for (int cellNum = 0; cellNum <= endCellNum; cellNum++) {// 循环列数（下表从0开始）
                        Cell cell = row.getCell(cellNum);

                        if (cellNum == 0) {// 第一列数据cellNum==startCellNum
                            // 若excel中无内容，而且没有空格，cell为空--默认3，空白
                            if (cell == null || cell.getCellType() == CellType.BLANK) {
                                resultInfo.setMessage("行" + (rowNum + 1) + "中数据错误或缺失，请返回修改！");
                                throw new Exception("行" + (rowNum + 1) + "中数据错误或缺失，请返回修改！");
                            } else {
                                //传纯数字直接报不存在订单
                                if (cell.getCellType() == CellType.NUMERIC) {
                                    resultInfo.setMessage("行" + (rowNum + 1) + "中订单不存在或不满足条件！");
                                    throw new Exception("行" + (rowNum + 1) + "中订单不存在或不满足条件！");
                                } else {
                                    //判断订单状态
                                    String saleorderNo = cell.getStringCellValue();
                                    Saleorder getsaleId = saleorderService.getsaleorderId(saleorderNo);
                                    if (getsaleId == null) {
                                        resultInfo.setMessage("行" + (rowNum + 1) + "中订单不存在或不满足条件！");
                                        throw new Exception("行" + (rowNum + 1) + "中订单不存在或不满足条件！");
                                    }
                                    // 获取订单信息
                                    Saleorder saleorder = new Saleorder();
                                    saleorder.setSaleorderId(getsaleId.getSaleorderId());
                                    Saleorder saleorderInfo = saleorderService.getBaseSaleorderInfo(saleorder);
                                    Integer validStatus = saleorderInfo.getValidStatus();
                                    Integer status = saleorderInfo.getStatus();
                                    if (saleorderInfo == null || validStatus == 0 || status == 3) {
                                        resultInfo.setMessage("行" + (rowNum + 1) + "中订单不存在或不满足条件！");
                                        throw new Exception("行" + (rowNum + 1) + "中订单不存在或不满足条件！");
                                    }
                                    saleOrderIdBatch = getsaleId.getSaleorderId();
                                }
                            }
                        }

                        if (cellNum == 1) {// 第二列数据cellNum==(startCellNum+1)
                            // 若excel中无内容，而且没有空格，cell为空--默认3，空白

                            if (cell == null || cell.getCellType() == CellType.BLANK) {
                                resultInfo.setMessage("行" + (rowNum + 1) + "中数据错误或缺失，请返回修改！");
                                throw new Exception("行" + (rowNum + 1) + "中数据错误或缺失，请返回修改！");
                            }
                            if (cell.getCellType() != CellType.NUMERIC) {
                                resultInfo.setMessage("行" + (rowNum + 1) + "中数据错误或缺失，请返回修改！");
                                throw new Exception("行" + (rowNum + 1) + "中数据错误或缺失，请返回修改！");
                            }
                            BigDecimal bigDecimal = BigDecimal.valueOf(cell.getNumericCellValue());
                            bigDecimal = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP);
                            capitalBillBatch.setAmount(bigDecimal);
                            billCount = bigDecimal.add(billCount);
                        }
                        //第三列数据
                        if (cellNum == 2) {// 第三列数据cellNum==(startCellNum+2)
                            // 若excel中无内容的话，而且没有空格，cell为空--默认3，空白
                            if (cell == null || cell.getCellType() == CellType.BLANK) {
                                resultInfo.setMessage("行" + (rowNum + 1) + "中数据错误或缺失，请返回修改！");
                                throw new Exception("行" + (rowNum + 1) + "中数据错误或缺失，请返回修改！");
                            }
                            if (cell.getCellType() == CellType.NUMERIC) {
                                cell.getNumericCellValue();
                                DecimalFormat decimalFormat = new DecimalFormat("#########################################################.##############################");

                                capitalBillBatch.setPayer(decimalFormat.format(cell.getNumericCellValue()));
                            } else {
                                capitalBillBatch.setPayer(cell.getStringCellValue());
                            }
                        }
                        //第四列数据
                        if (cellNum == 3) {
                            if (cell == null || cell.getCellType() == CellType.BLANK) {
                            } else {
                                if (cell.getCellType() == CellType.NUMERIC) {

                                    DecimalFormat decimalFormat = new DecimalFormat("#########################################################.##############################");
                                    capitalBillBatch.setComments(decimalFormat.format(cell.getNumericCellValue()));
                                } else {
                                    capitalBillBatch.setComments(cell.getStringCellValue());
                                }
                            }
                        }
                        capitalBillBatch.setTraderSubject(1);
                    }
                    batchBillInfo.setCapitalBill(capitalBillBatch);
                    batchBillInfo.setSaleOrderId(saleOrderIdBatch);
                    useAddCapitalBill.add(batchBillInfo);
                }
                if (billCount.compareTo(finalamt) == 1) {
                    resultInfo.setMessage("本次拟结款金额不得大于剩余结款金额！");
                    throw new Exception("本次拟结款金额不得大于剩余结款金额！");
                }
                //调用接口
                for (BatchBillInfo batch : useAddCapitalBill) {
                    batch.setBankAccName(bankAccName1);
                    bankAndCapitalService.saveAliPayBillToCapitalBill(batch);
                }
            }
        } catch (Exception e) {
            logger.error("bankBillBatch:", e);
            return resultInfo;
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                logger.error("关闭批量结款流出现异常:", e);
            }
        }
        resultInfo.setCode(0);
        resultInfo.setMessage("操作成功");
        return resultInfo;
    }


    /**
     * 批量結款
     *
     * @param request
     * @param
     * @return
     * <AUTHOR>
     */
    @ResponseBody
    @ExcludeAuthorization
    @RequestMapping("bankBillBatchAndConfirm")
    @SystemControllerLog(operationType = "import", desc = "导入批量结款数据")
    public ResultInfo<?> bankBillBatchAndConfirm(HttpServletRequest request, CapitalBill capitalBill, String bankAccName,
                                                 @RequestParam("file") MultipartFile file) {

        Integer bankBillId = capitalBill.getBankBillId();

        ResultInfo<?> resultInfo = new ResultInfo<>();
        String bankAccName1 = null;
        try {
            bankAccName1 = URLDecoder.decode(bankAccName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            logger.error("字符串转换：", e);
        }


        int num = 0;
        //2019.12.9

        /*System.out.println(capitalBill.getBankBillId());*/
        // 获取银行流水信息
        BankBill bankBill = bankBillService.getBankBillById(capitalBill.getBankBillId());
        List<Saleorder> matchInfo = bankBillExtMapper.getMatchInfo(bankBill.getAmt(), bankBill.getAccName1(), bankBill.getBankBillId());
        List<Saleorder> matchInfoByOrderNo = bankBillExtMapper.getMatchInfoByOrderNo(bankBill.getDet());
        matchInfo.addAll(matchInfoByOrderNo);
        List<String> saleorderNos = matchInfo.stream().map(Saleorder::getSaleorderNo).collect(Collectors.toList());


        //获取剩余款额
        /*double finalamt=bankBill.getAmt().doubleValue()-bankBill.getMatchedAmount().doubleValue();*/
        BigDecimal finalamt = bankBill.getAmt().subtract(bankBill.getMatchedAmount());

        /*double billCount=0;*/
        BigDecimal billCount = BigDecimal.valueOf(0.00);

        InputStream inputStream = null;
        try {
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            // 临时文件存放地址
            String path = request.getSession().getServletContext().getRealPath("/upload/goods");
            FileInfo fileInfo = ftpUtilService.fileUploadServe(path, file);
            if (fileInfo.getCode() == 0) {
                /*List<Goods> list = new ArrayList<>();*/
                // 获取excel路径
                inputStream = new FileInputStream(new File(fileInfo.getFilePath()));
                Workbook workbook = WorkbookFactory.create(inputStream);
                // 获取第一面sheet
                Sheet sheet = workbook.getSheetAt(0);
                // 起始行
                int startRowNum = sheet.getFirstRowNum() + 1;
                int endRowNum = sheet.getLastRowNum();// 结束行
                Row rowBEcell = sheet.getRow(sheet.getFirstRowNum());//获取第一行的单元个数
                int startCellNum = rowBEcell.getFirstCellNum();// 起始列
                int endCellNum = rowBEcell.getLastCellNum() - 1;//结束列
                //创建调用接口的集合
                List<BatchBillInfo> useAddCapitalBill = new ArrayList<>();
                BatchBillInfo batchBillInfo = null;
                CapitalBill capitalBillBatch = null;
                Integer saleOrderIdBatch = null;

                for (int rowNum = startRowNum; rowNum <= endRowNum; rowNum++) {// 循环行数
                    Row row = sheet.getRow(rowNum);
                    //创建接口需要的条件
                    batchBillInfo = new BatchBillInfo();
                    capitalBillBatch = new CapitalBill();
                    capitalBillBatch.setBankBillId(capitalBill.getBankBillId());
                    saleOrderIdBatch = 0;
					/*int startCellNum = row.getFirstCellNum();// 起始列
					int endCellNum = row.getLastCellNum() - 1;// 结束列*/
                    // 获取excel的值
                    //此处本来是cellNum = startCellNum，改为cellNum = 0
                    for (int cellNum = 0; cellNum <= endCellNum; cellNum++) {// 循环列数（下表从0开始）
                        Cell cell = row.getCell(cellNum);

                        if (cellNum == 0) {// 第一列数据cellNum==startCellNum
                            // 若excel中无内容，而且没有空格，cell为空--默认3，空白
                            if (cell == null || cell.getCellType() == CellType.BLANK) {
                                resultInfo.setMessage("第" + (rowNum + 1) + "行" + "必填项存在空值！");
                                throw new Exception("第" + (rowNum + 1) + "行" + "必填项存在空值！");
                            } else {
                                //传纯数字直接报不存在订单
                                if (cell.getCellType() == CellType.NUMERIC) {
                                    resultInfo.setMessage("第" + (rowNum + 1) + "行" + "中订单不存在或不满足条件！");
                                    throw new Exception("第" + (rowNum + 1) + "行" + "中订单不存在或不满足条件！");
                                } else {

                                    //判断订单状态
                                    String saleorderNo = cell.getStringCellValue();
                                    if (!saleorderNos.contains(saleorderNo)) {
                                        resultInfo.setMessage("第" + (rowNum + 1) + "行" + "订单号无法完成匹配，请从批量结款中剔除，如确需要匹配结款，此订单请采用手动匹配功能完成结款");
                                        throw new Exception("第" + (rowNum + 1) + "行" + "订单号无法完成匹配，请从批量结款中剔除，如确需要匹配结款，此订单请采用手动匹配功能完成结款");
                                    }
                                    Saleorder getsaleId = saleorderService.getsaleorderId(saleorderNo);
                                    if (getsaleId == null) {
                                        resultInfo.setMessage("第" + (rowNum + 1) + "行" + "中订单不存在或不结款满足条件！");
                                        throw new Exception("第" + (rowNum + 1) + "行" + "中订单不存在或不结款满足条件！");
                                    }
                                    // 获取订单信息
                                    Saleorder saleorder = new Saleorder();
                                    saleorder.setSaleorderId(getsaleId.getSaleorderId());
                                    Saleorder saleorderInfo = saleorderService.getBaseSaleorderInfo(saleorder);
                                    Integer validStatus = saleorderInfo.getValidStatus();
                                    Integer status = saleorderInfo.getStatus();
                                    if (saleorderInfo == null || validStatus == 0 || status == 3) {
                                        resultInfo.setMessage("第" + (rowNum + 1) + "行" + "中订单不存在或不满足结款条件！");
                                        throw new Exception("第" + (rowNum + 1) + "行" + "中订单不存在或不满足结款条件！");
                                    }
                                    saleOrderIdBatch = getsaleId.getSaleorderId();
                                }
                            }
                        }

                        if (cellNum == 1) {// 第二列数据cellNum==(startCellNum+1)
                            // 若excel中无内容，而且没有空格，cell为空--默认3，空白

                            if (cell == null || cell.getCellType() == CellType.BLANK) {
                                resultInfo.setMessage("第" + (rowNum + 1) + "行" + "必填项存在空值！");
                                throw new Exception("第" + (rowNum + 1) + "行" + "必填项存在空值！");
                            }
                            if (cell.getCellType() != CellType.NUMERIC) {
                                resultInfo.setMessage("第" + (rowNum + 1) + "行" + "金额字段必须为大于0的纯数字，小数位不超过2位！");
                                throw new Exception("第" + (rowNum + 1) + "行" + "金额字段必须为大于0的纯数字，小数位不超过2位！");
                            }
                            //
                            BigDecimal bigDecimal = BigDecimal.valueOf(cell.getNumericCellValue());
                            //bigDecimal如果小数超过两位报错或者为负数
                            if (bigDecimal.compareTo(BigDecimal.ZERO) <= 0) {
                                resultInfo.setMessage("第" + (rowNum + 1) + "行" + "金额字段必须为大于0的纯数字，小数位不超过2位！");
                                throw new Exception("第" + (rowNum + 1) + "行" + "金额字段必须为大于0的纯数字，小数位不超过2位！");
                            }
                            if (bigDecimal.scale() > 2) {
                                resultInfo.setMessage("第" + (rowNum + 1) + "行" + "金额字段必须为大于0的纯数字，小数位不超过2位！");
                                throw new Exception("第" + (rowNum + 1) + "行" + "金额字段必须为大于0的纯数字，小数位不超过2位！");
                            }
                            bigDecimal = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP);
                            capitalBillBatch.setAmount(bigDecimal);
                            billCount = bigDecimal.add(billCount);
                        }
                        //第三列数据
                        if (cellNum == 2) {// 第三列数据cellNum==(startCellNum+2)
                            // 若excel中无内容的话，而且没有空格，cell为空--默认3，空白
                            if (cell == null || cell.getCellType() == CellType.BLANK) {
                                resultInfo.setMessage("第" + (rowNum + 1) + "行" + "必填项存在空值！");
                                throw new Exception("第" + (rowNum + 1) + "行" + "必填项存在空值！");
                            }
                            if (cell.getCellType() == CellType.NUMERIC) {
                                cell.getNumericCellValue();
                                DecimalFormat decimalFormat = new DecimalFormat("#########################################################.##############################");

                                capitalBillBatch.setPayer(decimalFormat.format(cell.getNumericCellValue()));
                            } else {
                                capitalBillBatch.setPayer(cell.getStringCellValue());
                            }
                        }
                        //第四列数据
                        if (cellNum == 3) {
                            if (cell == null || cell.getCellType() == CellType.BLANK) {
                                resultInfo.setMessage("第" + (rowNum + 1) + "行" + "必填项存在空值！");
                                throw new Exception("第" + (rowNum + 1) + "行" + "必填项存在空值！");
                            } else {
                                if (cell.getCellType() == CellType.NUMERIC) {
                                    resultInfo.setMessage("第" + (rowNum + 1) + "行" + "只能填写对公或者对私！");
                                    throw new Exception("第" + (rowNum + 1) + "行" + "只能填写对公或者对私！");
                                } else {
                                    String traderSubjectStr = cell.getStringCellValue();
                                    if (traderSubjectStr.equals("对公")) {
                                        capitalBillBatch.setTraderSubject(1);
                                    } else if (traderSubjectStr.equals("对私")) {
                                        capitalBillBatch.setTraderSubject(2);
                                    } else {
                                        resultInfo.setMessage("第" + (rowNum + 1) + "行" + "只能填写对公或者对私！");
                                        throw new Exception("第" + (rowNum + 1) + "行" + "只能填写对公或者对私！");
                                    }
                                }
                            }
                        }
                        //第五列数据
                        if (cellNum == 4) {
                            if (cell == null || cell.getCellType() == CellType.BLANK) {
                            } else {
                                if (cell.getCellType() == CellType.NUMERIC) {

                                    DecimalFormat decimalFormat = new DecimalFormat("#########################################################.##############################");
                                    capitalBillBatch.setComments(decimalFormat.format(cell.getNumericCellValue()));
                                } else {
                                    capitalBillBatch.setComments(cell.getStringCellValue());
                                }
                            }
                        }
                    }
                    batchBillInfo.setCapitalBill(capitalBillBatch);
                    batchBillInfo.setSaleOrderId(saleOrderIdBatch);
                    useAddCapitalBill.add(batchBillInfo);
                }
                if (billCount.compareTo(finalamt) == 1) {
                    resultInfo.setMessage("当前批量结款的总金额超出当前流水剩余结款金额，请核对修改后重新上传！");
                    throw new Exception("当前批量结款的总金额超出当前流水剩余结款金额，请核对修改后重新上传！");
                }
                num = useAddCapitalBill.size();
                //调用接口
                for (BatchBillInfo batch : useAddCapitalBill) {
                    //确认 支付宝或者微信
                    if (bankBill.getAccName1().equals("支付宝（中国）网络技术有限公司") || bankBill.getAccName1().equals("财付通支付科技有限公司")) {

                    } else {
                        //批量确认  非支付宝微信
                        //结款


                        String key = REDIS_KEY + bankBillId;
                        if (RedissonLockUtils.tryLock(key)) {
                            log.info("结款加锁成功, key = [{}]", key);
                            try {
                                ResultInfo resultInfo1 = this.addCapitalBillBatch(user, batch.getCapitalBill(), batch.getSaleOrderId(), bankAccName1);
                            } finally {
                                RedissonLockUtils.unlock(key);
                                log.info("结款释放锁成功, key = [{}]", key);
                            }
                        } else {
                            log.error("结款获取锁失败,超出等待时长, key = [{}]", key);
                            throw new Exception("结款获取锁失败,请稍后重试");
                        }

                    }


                }
            }
        } catch (Exception e) {
            logger.error("bankBillBatch:", e);
            return resultInfo;
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                logger.error("关闭批量结款流出现异常:", e);
            }
        }
        resultInfo.setCode(0);
        resultInfo.setMessage("本次成功导入了" + num + "条数据");
        return resultInfo;
    }


    private List<CapitalBillDetail> matchAliPayBill(String tranFlow) {
        return capitalBillDetailMapper.getMatchAliPayInfo(tranFlow);
    }


    //原先的结款接口
    public ResultInfo addCapitalBillBatch(User user, CapitalBill capitalBill, Integer saleorderId, String bankAccName) {
        // 获取订单信息
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(saleorderId);
        Saleorder saleorderInfo = saleorderService.getBaseSaleorderInfo(saleorder);
        // 归属销售
        User belongUser = new User();
        if (saleorderInfo.getTraderId() != null) {
            belongUser = userService.getUserByTraderId(saleorderInfo.getTraderId(), 1);// 1客户，2供应商
            if (belongUser != null && belongUser.getUserId() != null) {
                belongUser = userService.getUserById(belongUser.getUserId());
            }
        }
        Company companyInfo = companyService.getCompanyByCompangId(user.getCompanyId());
        // 获取银行流水信息
        BankBill bankBill = bankBillService.getBankBillById(capitalBill.getBankBillId());
        // 获取订单已付款金额
        // 资金流水赋值
        if (user != null) {
            capitalBill.setCreator(user.getUserId());
            capitalBill.setAddTime(DateUtil.sysTimeMillis());
            capitalBill.setCompanyId(user.getCompanyId());
        }
        // 根据借款金额判断是付预付款，还是付账期
        BigDecimal totalAmount = saleorderInfo.getTotalAmount();

        ResultInfo<?> result = new ResultInfo<>();
        // 交易方式银行
        capitalBill.setTraderMode(521);
        capitalBill.setTranFlow(bankBill.getTranFlow());
        capitalBill.setCurrencyUnitId(1);
        capitalBill.setTraderTime(DateUtil.sysTimeMillis());
        capitalBill.setTraderType(1);
        capitalBill.setPayerBankAccount(bankBill.getAccno2());
        capitalBill.setPayerBankName(bankBill.getCadbankNm());
        capitalBill.setPayee(companyInfo == null ? "" : companyInfo.getCompanyName());

        List<CapitalBillDetail> capitalBillDetails = new ArrayList<>();
        CapitalBillDetail capitalBillDetail = new CapitalBillDetail();

        capitalBillDetail.setOrderType(1);
        capitalBillDetail.setOrderNo(saleorderInfo.getSaleorderNo());
        capitalBillDetail.setRelatedId(saleorderInfo.getSaleorderId());
        capitalBillDetail.setTraderType(1);
        capitalBillDetail.setTraderId(saleorderInfo.getTraderId());
        capitalBillDetail.setUserId(saleorderInfo.getUserId());
        capitalBillDetail.setBussinessType(526);//交易类型订单收款
        capitalBillDetail.setAmount(capitalBill.getAmount());
        if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
            capitalBillDetail.setOrgName(belongUser.getOrgName());
            capitalBillDetail.setOrgId(belongUser.getOrgId());
        }
        // 订单收款
        capitalBillDetails.add(capitalBillDetail);
        capitalBill.setCapitalBillDetails(capitalBillDetails);

        CapitalBillDetail capitalBillDetailInfo = new CapitalBillDetail();
        capitalBillDetailInfo.setOrderType(1);
        capitalBillDetailInfo.setOrderNo(saleorderInfo.getSaleorderNo());
        capitalBillDetailInfo.setRelatedId(saleorderInfo.getSaleorderId());
        capitalBillDetailInfo.setTraderType(1);
        capitalBillDetailInfo.setTraderId(saleorderInfo.getTraderId());
        capitalBillDetailInfo.setUserId(saleorderInfo.getUserId());
        capitalBillDetailInfo.setBussinessType(526);//交易类型订单收款
        capitalBillDetailInfo.setAmount(capitalBill.getAmount());
        if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
            capitalBillDetailInfo.setOrgName(belongUser.getOrgName());
            capitalBillDetailInfo.setOrgId(belongUser.getOrgId());
        }
        String payer = bankAccName;
//	  //VDERP-1327  订单结款自动生成流水记录
        capitalBill.setCapitalBillDetail(capitalBillDetailInfo);
        result = capitalBillService.saveAddCapitalBill(capitalBill);
        //VDERP-1327  订单结款自动生成流水记录
        if (!StringUtils.isEmpty(payer) && result.getCode() == 0) {
            if (payer.equals(ErpConst.TAOBAO) || payer.equals(ErpConst.WEIXIN)) {
                //对私方式交易为支付宝或微信
                capitalBill.setPayer(payer);
                ResultInfo resultInfo = capitalBillService.saveSecondCapitalBill(saleorderInfo, capitalBill);
            }
        }
        //VDERP-1327  订单结款自动生成流水记录
        try {
            //调用库存服务
            int i = warehouseStockService.updateOccupyStockService(saleorder, 0);
            logicalSaleorderChooseServiceImpl.chooseLogicalSaleorder(saleorder, user);
        } catch (Exception e) {
            logger.error("addCapitalBillBatch stock error:", e);
        }
        if (user.getCompanyId() == 1 && result.getCode() == 0) {
            vedengSoapService.orderSyncWeb(saleorderId);
        }
        if (result.getCode() == 0) {
            warehouseStockService.updateOccupyStockService(saleorderInfo, 0);
        }
        return result;


    }

    /**
     * 交通银行流水列表
     *
     * @param request
     * @param bankBill
     * @param session
     * @param pageNo
     * @param pageSize
     * @param searchBeginTime
     * @param searchEndTime
     * @return
     * <AUTHOR>
     */

    @ResponseBody
    @RequestMapping(value = "/jtIndex")
    public ModelAndView jtIndex(HttpServletRequest request, BankBill bankBill, HttpSession session,
                                @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                @RequestParam(required = false) Integer pageSize,
                                @RequestParam(required = false, value = "beginTime") String searchBeginTime,
                                @RequestParam(required = false, value = "endTime") String searchEndTime) {
        ModelAndView mv = new ModelAndView();
        Page page = getPageTag(request, pageNo, pageSize);
        // 如果有搜索时间就按照搜索时间，如果第一次进来设置默认时间为当前时间前一天
        if (null != searchEndTime && searchEndTime != "") {
            bankBill.setSearchEndtime(searchEndTime);
        } else {
            searchEndTime = DateUtil.convertString(DateUtil.sysTimeMillis(), DateUtil.DATE_FORMAT);
            bankBill.setSearchEndtime(searchEndTime);
        }

        if (null != searchBeginTime && searchBeginTime != "") {
            bankBill.setSearchBegintime(searchBeginTime);
        } else {
            searchBeginTime = DateUtil.convertString(DateUtil.sysTimeMillis(), DateUtil.DATE_FORMAT);
            bankBill.setSearchBegintime(searchBeginTime);
        }

        User user = (User) session.getAttribute(Consts.SESSION_USER);
        bankBill.setCompanyId(user.getCompanyId());

        //交通银行
        bankBill.setBankTag(6);
        //
        //bankBill.setSyncBankFlag(ErpConst.ZERO);
        try {
            Map<String, Object> map = bankBillService.getBankBillListPage(bankBill, page);
            if (map != null) {
                //匹配项目
                List<SysOptionDefinition> macthObjectList = getSysOptionDefinitionList(856);
                mv.addObject("macthObjectList", macthObjectList);
                mv.addObject("list", map.get("list"));
                mv.addObject("page", map.get("page"));
                mv.addObject("getAmount", map.get("getAmount"));
                mv.addObject("payAmount", map.get("payAmount"));
                mv.addObject("orderNum", map.get("orderNum"));
                mv.addObject("orderAmount", map.get("orderAmount"));
                mv.addObject("matchAmount", map.get("matchAmount"));
            }
        } catch (Exception e) {
            logger.error("jtIndex:", e);
        }

        mv.addObject("beginTime", searchBeginTime);
        mv.addObject("endTime", searchEndTime);
        mv.addObject("syncBankFlag", bankBill.getSyncBankFlag());
        //获取当前日期
        Date date = new Date();
        String nowDate = DateUtil.DatePreMonth(date, 0, null);
        mv.addObject("nowDate", nowDate);
        mv.setViewName("finance/bankBill/jtIndex");
        return mv;
    }


}
