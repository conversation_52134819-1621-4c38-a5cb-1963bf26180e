<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.settlement.mapper.PayApplyMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.settlement.domain.entity.PayApplyEntity">
        <!--@mbg.generated-->
        <!--@Table T_PAY_APPLY-->
        <id column="PAY_APPLY_ID" jdbcType="INTEGER" property="payApplyId"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="PAY_TYPE" jdbcType="INTEGER" property="payType"/>
        <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId"/>
        <result column="TRADER_SUBJECT" jdbcType="TINYINT" property="traderSubject"/>
        <result column="TRADER_MODE" jdbcType="INTEGER" property="traderMode"/>
        <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName"/>
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount"/>
        <result column="CURRENCY_UNIT_ID" jdbcType="INTEGER" property="currencyUnitId"/>
        <result column="BANK" jdbcType="VARCHAR" property="bank"/>
        <result column="BANK_ACCOUNT" jdbcType="VARCHAR" property="bankAccount"/>
        <result column="BANK_CODE" jdbcType="VARCHAR" property="bankCode"/>
        <result column="BANK_REMARK" jdbcType="VARCHAR" property="bankRemark"/>
        <result column="COMMENTS" jdbcType="VARCHAR" property="comments"/>
        <result column="VALID_STATUS" jdbcType="TINYINT" property="validStatus"/>
        <result column="VALID_TIME" jdbcType="BIGINT" property="validTime"/>
        <result column="VALID_COMMENTS" jdbcType="VARCHAR" property="validComments"/>
        <result column="VALID_USER_ID" jdbcType="INTEGER" property="validUserId"/>
        <result column="IS_BILL" jdbcType="TINYINT" property="isBill"/>
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="PAY_STATUS" jdbcType="INTEGER" property="payStatus"/>
        <result column="PAY_BANKTYPE_ID" jdbcType="INTEGER" property="payBanktypeId"/>
        <result column="PAY_BANKTYPE_NAME" jdbcType="VARCHAR" property="payBanktypeName"/>
        <result column="ACCOUNT_TYPE" jdbcType="INTEGER" property="accountType"/>
    </resultMap>

    <resultMap id="pay_apply_dto" type="com.vedeng.erp.finance.dto.PayApplyDto">
        <!--@mbg.generated-->
        <!--@Table T_PAY_APPLY-->
        <id column="PAY_APPLY_ID" jdbcType="INTEGER" property="payApplyId"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="PAY_TYPE" jdbcType="INTEGER" property="payType"/>
        <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId"/>
        <result column="TRADER_SUBJECT" jdbcType="TINYINT" property="traderSubject"/>
        <result column="TRADER_MODE" jdbcType="INTEGER" property="traderMode"/>
        <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName"/>
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount"/>
        <result column="CURRENCY_UNIT_ID" jdbcType="INTEGER" property="currencyUnitId"/>
        <result column="BANK" jdbcType="VARCHAR" property="bank"/>
        <result column="BANK_ACCOUNT" jdbcType="VARCHAR" property="bankAccount"/>
        <result column="BANK_CODE" jdbcType="VARCHAR" property="bankCode"/>
        <result column="COMMENTS" jdbcType="VARCHAR" property="comments"/>
        <result column="VALID_STATUS" jdbcType="TINYINT" property="validStatus"/>
        <result column="VALID_TIME" jdbcType="BIGINT" property="validTime"/>
        <result column="VALID_COMMENTS" jdbcType="VARCHAR" property="validComments"/>
        <result column="VALID_USER_ID" jdbcType="INTEGER" property="validUserId"/>
        <result column="IS_BILL" jdbcType="TINYINT" property="isBill"/>
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="PAY_STATUS" jdbcType="INTEGER" property="payStatus"/>
        <result column="PAY_BANKTYPE_ID" jdbcType="INTEGER" property="payBanktypeId"/>
        <result column="PAY_BANKTYPE_NAME" jdbcType="VARCHAR" property="payBanktypeName"/>
        <result column="ACCOUNT_TYPE" jdbcType="INTEGER" property="accountType"/>
        <collection property="payApplyDetailDtos" ofType="com.vedeng.erp.finance.dto.PayApplyDetailDto">
            <id column="PAY_APPLY_DETAIL_ID" jdbcType="INTEGER" property="payApplyDetailId"/>
            <result column="PAY_APPLY_ID" jdbcType="INTEGER" property="payApplyId"/>
            <result column="DETAILGOODS_ID" jdbcType="INTEGER" property="detailgoodsId"/>
            <result column="PRICE" jdbcType="DECIMAL" property="price"/>
            <result column="NUM" jdbcType="DECIMAL" property="num"/>
            <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount"/>
        </collection>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        PAY_APPLY_ID,
        COMPANY_ID,
        PAY_TYPE,
        RELATED_ID,
        TRADER_SUBJECT,
        TRADER_MODE,
        TRADER_NAME,
        AMOUNT,
        CURRENCY_UNIT_ID,
        BANK,
        BANK_ACCOUNT,
        BANK_CODE,
        COMMENTS,
        VALID_STATUS,
        VALID_TIME,
        VALID_COMMENTS,
        VALID_USER_ID,
        IS_BILL,
        ADD_TIME,
        CREATOR,
        MOD_TIME,
        UPDATER,
        PAY_STATUS,
        PAY_BANKTYPE_ID,
        PAY_BANKTYPE_NAME,
        ACCOUNT_TYPE,
        BANK_REMARK
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_PAY_APPLY
        where PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_PAY_APPLY
        where PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="PAY_APPLY_ID" keyProperty="payApplyId"
            parameterType="com.vedeng.erp.settlement.domain.entity.PayApplyEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_PAY_APPLY (COMPANY_ID, PAY_TYPE, RELATED_ID,
                                 TRADER_SUBJECT, TRADER_MODE, TRADER_NAME,
                                 AMOUNT, CURRENCY_UNIT_ID, BANK,
                                 BANK_ACCOUNT, BANK_CODE, COMMENTS,
                                 VALID_STATUS, VALID_TIME, VALID_COMMENTS,
                                 VALID_USER_ID, IS_BILL, ADD_TIME,
                                 CREATOR, MOD_TIME, UPDATER,
                                 PAY_STATUS, PAY_BANKTYPE_ID, PAY_BANKTYPE_NAME)
        values (#{companyId,jdbcType=INTEGER}, #{payType,jdbcType=INTEGER}, #{relatedId,jdbcType=INTEGER},
                #{traderSubject,jdbcType=TINYINT}, #{traderMode,jdbcType=INTEGER}, #{traderName,jdbcType=VARCHAR},
                #{amount,jdbcType=DECIMAL}, #{currencyUnitId,jdbcType=INTEGER}, #{bank,jdbcType=VARCHAR},
                #{bankAccount,jdbcType=VARCHAR}, #{bankCode,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR},
                #{validStatus,jdbcType=TINYINT}, #{validTime,jdbcType=BIGINT}, #{validComments,jdbcType=VARCHAR},
                #{validUserId,jdbcType=INTEGER}, #{isBill,jdbcType=TINYINT}, #{addTime,jdbcType=BIGINT},
                #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER},
                #{payStatus,jdbcType=INTEGER}, #{payBanktypeId,jdbcType=INTEGER}, #{payBanktypeName,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="PAY_APPLY_ID" keyProperty="payApplyId"
            parameterType="com.vedeng.erp.settlement.domain.entity.PayApplyEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_PAY_APPLY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="payType != null">
                PAY_TYPE,
            </if>
            <if test="relatedId != null">
                RELATED_ID,
            </if>
            <if test="traderSubject != null">
                TRADER_SUBJECT,
            </if>
            <if test="traderMode != null">
                TRADER_MODE,
            </if>
            <if test="traderName != null">
                TRADER_NAME,
            </if>
            <if test="amount != null">
                AMOUNT,
            </if>
            <if test="currencyUnitId != null">
                CURRENCY_UNIT_ID,
            </if>
            <if test="bank != null">
                BANK,
            </if>
            <if test="bankAccount != null">
                BANK_ACCOUNT,
            </if>
            <if test="bankCode != null">
                BANK_CODE,
            </if>
            <if test="comments != null">
                COMMENTS,
            </if>
            <if test="validStatus != null">
                VALID_STATUS,
            </if>
            <if test="validTime != null">
                VALID_TIME,
            </if>
            <if test="validComments != null">
                VALID_COMMENTS,
            </if>
            <if test="validUserId != null">
                VALID_USER_ID,
            </if>
            <if test="isBill != null">
                IS_BILL,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="payStatus != null">
                PAY_STATUS,
            </if>
            <if test="payBanktypeId != null">
                PAY_BANKTYPE_ID,
            </if>
            <if test="payBanktypeName != null">
                PAY_BANKTYPE_NAME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="payType != null">
                #{payType,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="traderSubject != null">
                #{traderSubject,jdbcType=TINYINT},
            </if>
            <if test="traderMode != null">
                #{traderMode,jdbcType=INTEGER},
            </if>
            <if test="traderName != null">
                #{traderName,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="currencyUnitId != null">
                #{currencyUnitId,jdbcType=INTEGER},
            </if>
            <if test="bank != null">
                #{bank,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null">
                #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null">
                #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="validStatus != null">
                #{validStatus,jdbcType=TINYINT},
            </if>
            <if test="validTime != null">
                #{validTime,jdbcType=BIGINT},
            </if>
            <if test="validComments != null">
                #{validComments,jdbcType=VARCHAR},
            </if>
            <if test="validUserId != null">
                #{validUserId,jdbcType=INTEGER},
            </if>
            <if test="isBill != null">
                #{isBill,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="payStatus != null">
                #{payStatus,jdbcType=INTEGER},
            </if>
            <if test="payBanktypeId != null">
                #{payBanktypeId,jdbcType=INTEGER},
            </if>
            <if test="payBanktypeName != null">
                #{payBanktypeName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.settlement.domain.entity.PayApplyEntity">
        <!--@mbg.generated-->
        update T_PAY_APPLY
        <set>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="payType != null">
                PAY_TYPE = #{payType,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                RELATED_ID = #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="traderSubject != null">
                TRADER_SUBJECT = #{traderSubject,jdbcType=TINYINT},
            </if>
            <if test="traderMode != null">
                TRADER_MODE = #{traderMode,jdbcType=INTEGER},
            </if>
            <if test="traderName != null">
                TRADER_NAME = #{traderName,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="currencyUnitId != null">
                CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
            </if>
            <if test="bank != null">
                BANK = #{bank,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null">
                BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null">
                BANK_CODE = #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="comments != null">
                COMMENTS = #{comments,jdbcType=VARCHAR},
            </if>
            <if test="validStatus != null">
                VALID_STATUS = #{validStatus,jdbcType=TINYINT},
            </if>
            <if test="validTime != null">
                VALID_TIME = #{validTime,jdbcType=BIGINT},
            </if>
            <if test="validComments != null">
                VALID_COMMENTS = #{validComments,jdbcType=VARCHAR},
            </if>
            <if test="validUserId != null">
                VALID_USER_ID = #{validUserId,jdbcType=INTEGER},
            </if>
            <if test="isBill != null">
                IS_BILL = #{isBill,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="payStatus != null">
                PAY_STATUS = #{payStatus,jdbcType=INTEGER},
            </if>
            <if test="payBanktypeId != null">
                PAY_BANKTYPE_ID = #{payBanktypeId,jdbcType=INTEGER},
            </if>
            <if test="payBanktypeName != null">
                PAY_BANKTYPE_NAME = #{payBanktypeName,jdbcType=VARCHAR},
            </if>
        </set>
        where PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.settlement.domain.entity.PayApplyEntity">
        <!--@mbg.generated-->
        update T_PAY_APPLY
        set COMPANY_ID        = #{companyId,jdbcType=INTEGER},
            PAY_TYPE          = #{payType,jdbcType=INTEGER},
            RELATED_ID        = #{relatedId,jdbcType=INTEGER},
            TRADER_SUBJECT    = #{traderSubject,jdbcType=TINYINT},
            TRADER_MODE       = #{traderMode,jdbcType=INTEGER},
            TRADER_NAME       = #{traderName,jdbcType=VARCHAR},
            AMOUNT            = #{amount,jdbcType=DECIMAL},
            CURRENCY_UNIT_ID  = #{currencyUnitId,jdbcType=INTEGER},
            BANK              = #{bank,jdbcType=VARCHAR},
            BANK_ACCOUNT      = #{bankAccount,jdbcType=VARCHAR},
            BANK_CODE         = #{bankCode,jdbcType=VARCHAR},
            COMMENTS          = #{comments,jdbcType=VARCHAR},
            VALID_STATUS      = #{validStatus,jdbcType=TINYINT},
            VALID_TIME        = #{validTime,jdbcType=BIGINT},
            VALID_COMMENTS    = #{validComments,jdbcType=VARCHAR},
            VALID_USER_ID     = #{validUserId,jdbcType=INTEGER},
            IS_BILL           = #{isBill,jdbcType=TINYINT},
            ADD_TIME          = #{addTime,jdbcType=BIGINT},
            CREATOR           = #{creator,jdbcType=INTEGER},
            MOD_TIME          = #{modTime,jdbcType=BIGINT},
            UPDATER           = #{updater,jdbcType=INTEGER},
            PAY_STATUS        = #{payStatus,jdbcType=INTEGER},
            PAY_BANKTYPE_ID   = #{payBanktypeId,jdbcType=INTEGER},
            PAY_BANKTYPE_NAME = #{payBanktypeName,jdbcType=VARCHAR}
        where PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER}
    </update>
    <insert id="batchInsert" keyColumn="PAY_APPLY_ID" keyProperty="payApplyId" parameterType="map"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_PAY_APPLY
        (COMPANY_ID, PAY_TYPE, RELATED_ID, TRADER_SUBJECT, TRADER_MODE, TRADER_NAME, AMOUNT,
         CURRENCY_UNIT_ID, BANK, BANK_ACCOUNT, BANK_CODE, COMMENTS, VALID_STATUS, VALID_TIME,
         VALID_COMMENTS, VALID_USER_ID, IS_BILL, ADD_TIME, CREATOR, MOD_TIME, UPDATER, PAY_STATUS,
         PAY_BANKTYPE_ID, PAY_BANKTYPE_NAME)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.companyId,jdbcType=INTEGER}, #{item.payType,jdbcType=INTEGER}, #{item.relatedId,jdbcType=INTEGER},
             #{item.traderSubject,jdbcType=TINYINT}, #{item.traderMode,jdbcType=INTEGER},
             #{item.traderName,jdbcType=VARCHAR},
             #{item.amount,jdbcType=DECIMAL}, #{item.currencyUnitId,jdbcType=INTEGER}, #{item.bank,jdbcType=VARCHAR},
             #{item.bankAccount,jdbcType=VARCHAR}, #{item.bankCode,jdbcType=VARCHAR}, #{item.comments,jdbcType=VARCHAR},
             #{item.validStatus,jdbcType=TINYINT}, #{item.validTime,jdbcType=BIGINT},
             #{item.validComments,jdbcType=VARCHAR},
             #{item.validUserId,jdbcType=INTEGER}, #{item.isBill,jdbcType=TINYINT}, #{item.addTime,jdbcType=BIGINT},
             #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=BIGINT}, #{item.updater,jdbcType=INTEGER},
             #{item.payStatus,jdbcType=INTEGER}, #{item.payBanktypeId,jdbcType=INTEGER},
             #{item.payBanktypeName,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="selectByPayTypeAndRelatedIdAndOtherParam" parameterType="com.vedeng.erp.finance.dto.PayApplyDto"
            resultMap="pay_apply_dto">
        select TPAD.PAY_APPLY_DETAIL_ID,
               TPAD.PAY_APPLY_ID,
               TPAD.DETAILGOODS_ID,
               TPAD.PRICE,
               TPAD.NUM,
               TPAD.TOTAL_AMOUNT,
               a.PAY_APPLY_ID,
               COMPANY_ID,
               PAY_TYPE,
               RELATED_ID,
               TRADER_SUBJECT,
               TRADER_MODE,
               TRADER_NAME,
               AMOUNT,
               CURRENCY_UNIT_ID,
               BANK,
               BANK_ACCOUNT,
               BANK_CODE,
               COMMENTS,
               VALID_STATUS,
               VALID_TIME,
               VALID_COMMENTS,
               VALID_USER_ID,
               IS_BILL,
               ADD_TIME,
               CREATOR,
               MOD_TIME,
               UPDATER,
               PAY_STATUS,
               PAY_BANKTYPE_ID,
               PAY_BANKTYPE_NAME
        from T_PAY_APPLY a
                 left join T_PAY_APPLY_DETAIL TPAD on a.PAY_APPLY_ID = TPAD.PAY_APPLY_ID
        where a.PAY_TYPE = #{payType,jdbcType=INTEGER}
          and a.RELATED_ID = #{relatedId,jdbcType=INTEGER}
        <if test="companyId != null and companyId != ''">
            <!-- 订单状态 -->
            AND a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
        </if>
        <if test="validStatus != null">
            AND a.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
        </if>
        order by a.PAY_APPLY_ID desc;
    </select>

    <select id="queryInfoByPayApplyId" resultType="com.vedeng.erp.finance.dto.PayApplyDto">
        SELECT *
        FROM T_PAY_APPLY
        WHERE PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER}
    </select>

    <select id="getPayApplyMaxRecord" resultMap="pay_apply_dto">
        SELECT *
        FROM T_PAY_APPLY
        WHERE RELATED_ID = #{relatedId,jdbcType=INTEGER}
          AND PAY_TYPE = 518
          AND ADD_TIME = (SELECT MAX(ADD_TIME) ss
                          FROM T_PAY_APPLY
                          WHERE RELATED_ID = #{relatedId,jdbcType=INTEGER}
                            and VALID_STATUS = 1)
    </select>

    <select id="getTraderSupplierIdByTraderName" resultType="java.lang.Integer">
        select tts.TRADER_SUPPLIER_ID
        from T_TRADER_SUPPLIER tts
                 left join T_TRADER tr on tr.TRADER_ID = tts.TRADER_ID
        where tr.TRADER_NAME = #{traderName,jdbcType=VARCHAR}
          and tts.IS_ENABLE = 1
        limit 1
    </select>

    <!--auto generated by MybatisCodeHelper on 2023-11-10-->
    <update id="updateAccountTypeByPayApplyId">
        update T_PAY_APPLY
        set ACCOUNT_TYPE=#{updatedAccountType,jdbcType=INTEGER}
        where PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER}
    </update>

    <!--auto generated by MybatisCodeHelper on 2024-04-24-->
    <select id="queryByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_PAY_APPLY
        <where>
            <if test="payApplyId != null">
                and PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER}
            </if>
            <if test="companyId != null">
                and COMPANY_ID = #{companyId,jdbcType=INTEGER}
            </if>
            <if test="payType != null">
                and PAY_TYPE = #{payType,jdbcType=INTEGER}
            </if>
            <if test="relatedId != null">
                and RELATED_ID = #{relatedId,jdbcType=INTEGER}
            </if>
            <if test="traderSubject != null">
                and TRADER_SUBJECT = #{traderSubject,jdbcType=TINYINT}
            </if>
            <if test="traderMode != null">
                and TRADER_MODE = #{traderMode,jdbcType=INTEGER}
            </if>
            <if test="traderName != null">
                and TRADER_NAME = #{traderName,jdbcType=VARCHAR}
            </if>
            <if test="amount != null">
                and AMOUNT = #{amount,jdbcType=DECIMAL}
            </if>
            <if test="currencyUnitId != null">
                and CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER}
            </if>
            <if test="bank != null">
                and BANK = #{bank,jdbcType=VARCHAR}
            </if>
            <if test="bankAccount != null">
                and BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR}
            </if>
            <if test="bankCode != null">
                and BANK_CODE = #{bankCode,jdbcType=VARCHAR}
            </if>
            <if test="comments != null">
                and COMMENTS = #{comments,jdbcType=VARCHAR}
            </if>
            <if test="validStatus != null">
                and VALID_STATUS = #{validStatus,jdbcType=TINYINT}
            </if>
            <if test="validTime != null">
                and VALID_TIME = #{validTime,jdbcType=BIGINT}
            </if>
            <if test="validComments != null">
                and VALID_COMMENTS = #{validComments,jdbcType=VARCHAR}
            </if>
            <if test="validUserId != null">
                and VALID_USER_ID = #{validUserId,jdbcType=INTEGER}
            </if>
            <if test="isBill != null">
                and IS_BILL = #{isBill,jdbcType=TINYINT}
            </if>
            <if test="addTime != null">
                and ADD_TIME = #{addTime,jdbcType=BIGINT}
            </if>
            <if test="beginAddTime != null">
                and ADD_TIME > #{beginAddTime,jdbcType=BIGINT}
            </if>
            <if test="creator != null">
                and CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="modTime != null">
                and MOD_TIME = #{modTime,jdbcType=BIGINT}
            </if>
            <if test="updater != null">
                and UPDATER = #{updater,jdbcType=INTEGER}
            </if>
            <if test="payStatus != null">
                and PAY_STATUS = #{payStatus,jdbcType=INTEGER}
            </if>
            <if test="payBanktypeId != null">
                and PAY_BANKTYPE_ID = #{payBanktypeId,jdbcType=INTEGER}
            </if>
            <if test="payBanktypeName != null">
                and PAY_BANKTYPE_NAME = #{payBanktypeName,jdbcType=VARCHAR}
            </if>
            <if test="accountType != null">
                and ACCOUNT_TYPE = #{accountType,jdbcType=INTEGER}
            </if>
            limit 1000
        </where>
    </select>

    <update id="updateAutoBill">
        update T_PAY_APPLY
        set AUTO_BILL = #{autoBill,jdbcType=INTEGER}
        where PAY_APPLY_ID = #{payApplyId,jdbcType=INTEGER}
    </update>

    <select id="getVerifyInfoUserNames" resultType="java.lang.String">
        select VERIFY_USERNAME
        from T_VERIFIES_INFO
        where RELATE_TABLE_KEY = #{payApplyId,jdbcType=INTEGER}
          and RELATE_TABLE = 'T_PAY_APPLY'
          and T_VERIFIES_INFO.VERIFIES_TYPE = 644
        limit 1;
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-08-26-->
    <select id="getByPayTypeAndRelatedIdLast" resultType="com.vedeng.erp.finance.dto.PayApplyDto">
        select
        <include refid="Base_Column_List"/>
        from T_PAY_APPLY
        where PAY_TYPE = #{payType,jdbcType=INTEGER}
          and RELATED_ID = #{relatedId,jdbcType=INTEGER}
        order by PAY_APPLY_ID desc
        limit 1
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-09-05-->
    <select id="findByPayTypeAndRelatedId" resultType="com.vedeng.erp.finance.dto.PayApplyDto">
        select
        <include refid="Base_Column_List"/>
        from T_PAY_APPLY
        where PAY_TYPE = #{payType,jdbcType=INTEGER}
          and RELATED_ID = #{relatedId,jdbcType=INTEGER}
          and VALID_STATUS = 1
          and IS_BILL = 1
    </select>

    <select id="getListByPayTypeAndRelatedId" resultMap="pay_apply_dto">
        select
        <include refid="Base_Column_List"/>
        from T_PAY_APPLY
        where PAY_TYPE = #{payType,jdbcType=INTEGER}
          and RELATED_ID = #{relatedId,jdbcType=INTEGER}
    </select>

    <select id="queryPayApplyByIds" resultMap="pay_apply_dto">
        select
        <include refid="Base_Column_List"/>
        from T_PAY_APPLY
        where PAY_APPLY_ID in
        <foreach collection="payApplyIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-10-16-->
    <select id="findByRelatedIdAndPayType" resultType="com.vedeng.erp.finance.dto.PayApplyDto">
        select
        <include refid="Base_Column_List"/>
        from T_PAY_APPLY
        where RELATED_ID = #{relatedId,jdbcType=INTEGER}
          and PAY_TYPE = #{payType,jdbcType=INTEGER}
          and VALID_STATUS = 0
          and IS_BILL = 0
          and PAY_STATUS = 0
    </select>

    <select id="findAllByValidStatusAndPayStatus" resultType="com.vedeng.erp.finance.dto.PayApplyDto">
        select *
        from T_PAY_APPLY TPA
                 left join T_MINSHENG_BANK_TRANSFER_RECORD TMBTR on TMBTR.PAY_APPLY_ID = TPA.PAY_APPLY_ID
        where
            TPA.VALID_STATUS = 0
          and TPA.PAY_STATUS = 0
          and TMBTR.PAY_APPLY_ID is not null
          and TMBTR.RESPONSE_CODE = 0
    </select>
</mapper>
