package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.eventbus.EventBus;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.QRCode;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.listenerEvent.TraderBillPeriodEvent;
import com.vedeng.common.core.utils.QRCodeUtils;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.erp.finance.common.exception.InvoiceException;
import com.vedeng.erp.finance.constants.FinanceConstant;
import com.vedeng.erp.finance.domain.dto.*;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.enums.InvoiceSystemInterfaceTypeEnum;
import com.vedeng.erp.finance.enums.NotifyTypeEnum;
import com.vedeng.erp.finance.enums.TaxesInterfaceCodeEnum;
import com.vedeng.erp.finance.enums.TaxesMappingEnum;
import com.vedeng.erp.finance.mapstruct.InvoiceRedConfirmationApiConvertor;
import com.vedeng.erp.finance.notify.manager.NotifyServiceManager;
import com.vedeng.erp.finance.service.*;
import com.vedeng.erp.saleorder.service.JcWeChatApiService;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.erp.trader.dto.TraderFinanceDto;
import com.vedeng.erp.trader.service.TraderFinanceApiService;
import com.vedeng.infrastructure.taxes.common.constant.TaxesConstant;
import com.vedeng.infrastructure.taxes.config.TaxesConfig;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import com.vedeng.infrastructure.wxrobot.constants.WxRobotMsgTemple;
import com.vedeng.infrastructure.wxrobot.dto.WxMsgDto;
import com.vedeng.infrastructure.wxrobot.service.WxRobotService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 财务数电发票
 * @date 2023/9/20 13:03
 */
@Service
@Slf4j
public class FullyDigitalInvoiceServiceImpl implements FullyDigitalInvoiceService {

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;
    @Autowired
    private InvoiceService invoiceService;
    @Autowired
    private AfterSalesInvoiceService afterSalesInvoiceService;
    @Autowired
    private TaxesOpenApiService taxesOpenApiService;
    @Autowired
    private InvoiceApplyService invoiceApplyService;
    @Autowired
    private SaleOrderApiService saleOrderApiService;
    @Autowired
    private TraderFinanceApiService traderFinanceApiService;
    @Autowired
    private TaxClassificationService taxClassificationService;
    @Autowired
    private InvoiceTaxSystemRecordService invoiceTaxSystemRecordService;
    @Autowired
    private InvoiceApiService invoiceApiService;
    @Autowired
    private WxRobotService wxRobotService;
    @Autowired
    private TaxesConfig taxesConfig;
    @Autowired
    private JcWeChatApiService jcWeChatApiService;
    @Autowired
    @Qualifier(value = "eventBusCenter")
    private EventBus eventBusCenter;
    @Autowired
    InvoiceRedConfirmationApiConvertor invoiceRedConfirmationApiConvertor;

    @Autowired
    private NotifyServiceManager notifyServiceManager;

    /**
     * wx机器人群号
     */
    @Value("${invoice_notice_robot_num}")
    public String invoiceNoticeRobotNum;

    private final static long WAIT_TIME = 10;
    private final static long LOCK_SECONDS = 20;
    private final static String REDIS_KEY = "ERP:TAX:OPENINVOICE:";

    @Override
    public boolean isEnableGlobal() {
        return Convert.toBool(sysOptionDefinitionApiService.getOptionDefinitionById(FinanceConstant.FULLY_DIGITALIZE_INVOICE).getStatus(), false);
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public OpenInvoiceResultDto openBlueInvoice(InvoiceApplyDto invoiceApplyDto) {
        log.info("数电发票销项票开票:，开票申请{}", JSON.toJSONString(invoiceApplyDto));
        try {

            // 初始化发票相关信息
            invoiceApplyDto.initInvoiceInfo();

            // 初始化发票金额信息
            invoiceApplyDto.initInvoiceAmount();

            String key = REDIS_KEY + invoiceApplyDto.getInvoiceApplyId();
            if (RedissonLockUtils.tryLock(key, WAIT_TIME, LOCK_SECONDS, TimeUnit.SECONDS)) {
                log.info("数电发票销项票开票:加锁成功, key = [{}]", key);

                try {
                    // 校验开票
                    this.checkMakeOutInvoice(invoiceApplyDto);

                    // 执行调用税金系统接口
                    SaleInvoiceOpenResponseDto saleInvoiceOpenResponseDto = this.invokeSaleOrderInvoiceApi(invoiceApplyDto);

                    if (saleInvoiceOpenResponseDto.getIsSuccess()) {
                        // 开票申请审核成功
                        invoiceApplyService.auditPassInvoiceApply(invoiceApplyDto.getInvoiceApplyId());

                        // 新增销项票发票
                        InvoiceDto invoice = this.createBlueInvoice(invoiceApplyDto, saleInvoiceOpenResponseDto);

                        // 更新销售订单的发票状态
                        saleOrderApiService.updateSaleOrderInvoiceStatus(invoice.getRelatedId());

                        // 销售订单开票处理订单生成账期逾期编码
                        TraderBillPeriodEvent traderBillPeriodEvent = TraderBillPeriodEvent
                                .builder()
                                .invoiceId(invoice.getInvoiceId())
                                .saleOrderId(invoice.getRelatedId())
                                .build();
                        eventBusCenter.post(traderBillPeriodEvent);

                        // 售后单关联发票信息
                        afterSalesInvoiceService.saveAfterSaleInvoice(invoice);

                        // JC订单发送微信消息
                        jcWeChatApiService.sendMsg(invoice.getRelatedId());

                        // 短信交付
                        notifyServiceManager.notify(NotifyTypeEnum.SMS,invoice);
                        // 邮件交付
                        notifyServiceManager.notify(NotifyTypeEnum.MAIL,invoice);

                        return new OpenInvoiceResultDto().success();
                    }
                    if (!saleInvoiceOpenResponseDto.getIsSuccess()) {
                        throw new InvoiceException(saleInvoiceOpenResponseDto);
                    }
                }
                finally {
                    RedissonLockUtils.unlock(key);
                    log.info("数电发票销项票开票:释放锁成功, key = [{}]", key);
                }

            }
            else {
                log.error("数电发票销项票开票:获取锁失败,超出等待时长, key = [{}]", key);
                throw new ServiceException("数电发票销项票开票:获取锁失败,超出等待时长");
            }

        }

        catch (InvoiceException e) {
            log.info("数电发票销项票开票:已知异常", e);
            this.openBlueInvoiceFail(invoiceApplyDto, e);
            return new OpenInvoiceResultDto().fail(e.getMessage());

        }

        catch (ServiceException e) {
            log.info("数电发票销项票开票:业务异常", e);
            throw e;

        }

        catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("数电发票销项票开票:系统未知异常", e);
            throw new ServiceException("数电发票销项票开票:系统未知异常", e);
        }

        catch (Exception e) {
            log.error("数电发票销项票开票:系统未知异常", e);
            throw new ServiceException("数电发票销项票开票:系统未知异常", e);
        }
        return new OpenInvoiceResultDto().fail();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void openRedInvoice(InvoiceRedConfirmationDto invoiceRedConfirmationDto) {
        InvoiceRedConfirmationApiDto invoiceRedConfirmationApiDto = invoiceRedConfirmationApiConvertor.toReverse(invoiceRedConfirmationDto);
        eventBusCenter.post(invoiceRedConfirmationApiDto);
    }


    /**
     * 是否可开票
     *
     * @param invoiceApplyDto 发票申请
     * @return 是否需要执行后续开票任务 false:不执行 true:执行
     */
    private boolean whetherCanOpenBlueInvoice(InvoiceApplyDto invoiceApplyDto) {
        // 返回校验开票申请单是否已经执行
        InvoiceApplyDto invoiceApply = invoiceApplyService.getInvoiceApply(invoiceApplyDto.getInvoiceApplyId());
        // 需要失败重试的税金任务记录表,无成功的表示需要重试
        InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto = this.getInvoiceTaxSystemRecordDto(invoiceApplyDto, InvoiceSystemInterfaceTypeEnum.SALE_OPEN,
                FinanceConstant.SUCCESS);
        return ErpConstant.WAIT_AUDIT.equals(invoiceApply.getValidStatus()) && invoiceTaxSystemRecordDto == null;
    }

    /**
     * 根据发票申请获取税金任务记录
     *
     * @param invoiceApplyDto 发票申请
     * @return InvoiceTaxSystemRecordDto
     */
    private InvoiceTaxSystemRecordDto getInvoiceTaxSystemRecordDto(InvoiceApplyDto invoiceApplyDto,
                                                                   InvoiceSystemInterfaceTypeEnum invoiceSystemInterfaceTypeEnum,
                                                                   String isSuccess) {
        InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto = new InvoiceTaxSystemRecordDto();
        invoiceTaxSystemRecordDto.setBusinessId(invoiceApplyDto.getInvoiceApplyId().toString());
        invoiceTaxSystemRecordDto.setInterfaceType(invoiceSystemInterfaceTypeEnum.getType());
        invoiceTaxSystemRecordDto.setRunningStatus(isSuccess);
        List<InvoiceTaxSystemRecordDto> invoiceTaxSystemRecordDtoList = invoiceTaxSystemRecordService
                .findInvoiceTaxSystemRecordDto(invoiceTaxSystemRecordDto);
        return CollUtil.getFirst(invoiceTaxSystemRecordDtoList);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void checkMakeOutInvoice(InvoiceApplyDto invoiceApplyDto) {
        if (invoiceApplyDto == null || invoiceApplyDto.getInvoiceApplyId() == null) {
            log.error("发票申请或者发票申请id不能为空");
            throw new ServiceException("发票申请或者发票申请id不能为空!");
        }

        // 是否可开票
        String errorMsg = StrUtil.format("数电发票销项票开票:当前发票申请已开票，无需重新开票,发票申请id：{}", invoiceApplyDto.getInvoiceApplyId());
        List<InvoiceDto> invoiceList = invoiceService.findByInvoiceApply(invoiceApplyDto.getInvoiceApplyId());
        if (CollUtil.isNotEmpty(invoiceList)) {
            throw new InvoiceException(errorMsg);
        }
        boolean whether = this.whetherCanOpenBlueInvoice(invoiceApplyDto);
        if (!whether) {
            throw new InvoiceException(errorMsg);
        }

        if (CollUtil.isEmpty(invoiceApplyDto.getInvoiceApplyDetailDtoList())) {
            throw new InvoiceException("订单实际待开票金额为零!");
        }

        invoiceApplyDto.getInvoiceApplyDetailDtoList().forEach(dto -> {
            if (StrUtil.isEmpty(dto.getTaxCategoryNo())) {
                throw new InvoiceException(StrUtil.format("商品名称：{},编号:{},税收分类编码为空！", dto.getSkuName(), dto.getSkuNo()));
            }
            // 获取税收分类编码信息
            TaxcodeClassificationDto taxClassificationDto = taxClassificationService.findByCode(dto.getTaxCategoryNo());
            if (taxClassificationDto == null) {
                throw new InvoiceException(StrUtil.format("税收分类编码{},未找到!", dto.getTaxCategoryNo()));
            }
            dto.setTaxCategorySimpleName(taxClassificationDto.getClassificationAbbreviation());
        });

        // 购方信息check
        List<TraderFinanceDto> traderFinanceDtoList = traderFinanceApiService.findByTraderIdAndTraderType(invoiceApplyDto.getTraderId(),
                ErpConstant.ONE);
        if (CollUtil.isEmpty(traderFinanceDtoList)) {
            log.error("无购方信息,财务申请单{},客户id{}", invoiceApplyDto.getInvoiceApplyId(), invoiceApplyDto.getTraderId());
            throw new InvoiceException(StrUtil.format("财务申请单{},客户id{},无购方信息，需处理", invoiceApplyDto.getInvoiceApplyId(),
                    invoiceApplyDto.getTraderId()));
        }
        if (traderFinanceDtoList.size() > 1) {
            log.error("财务申请单{},客户id{},购方信息大于1条", invoiceApplyDto.getInvoiceApplyId(), invoiceApplyDto.getTraderId());
        }
        invoiceApplyDto.setTraderFinanceDto(CollUtil.getFirst(traderFinanceDtoList));
    }

    /**
     * 组装税金系统开发发票接口参数
     *
     * @param invoiceApplyDto invoiceApplyDto
     * @return SaleInvoiceOpenRequestDto
     */
    @Transactional(rollbackFor = Throwable.class)
    public SaleInvoiceOpenRequestDto assembleInvoice(InvoiceApplyDto invoiceApplyDto) {
        log.info("组装税金系统发票接口参数:发票申请:{}", JSON.toJSONString(invoiceApplyDto));

        List<InvoiceApplyDetailDto> detailDtoList = invoiceApplyDto.getInvoiceApplyDetailDtoList();

        SaleInvoiceOpenRequestDto saleInvoiceOpenRequestDto = new SaleInvoiceOpenRequestDto();
        SaleInvoiceOpenRequestDto.DataInfo dataInfo = new SaleInvoiceOpenRequestDto.DataInfo();
        saleInvoiceOpenRequestDto.setData(dataInfo);
        dataInfo.setOrderNo(invoiceApplyDto.getInvoiceApplyId().toString());
        dataInfo.setFppzDm(invoiceApplyDto.getInvoiceTypeCode());

        TraderFinanceDto traderFinanceDto = invoiceApplyDto.getTraderFinanceDto();

        dataInfo.setGmfnsrsbh(traderFinanceDto.getTaxNum());
        dataInfo.setGmfmc(traderFinanceDto.getTraderName());
        dataInfo.setGmfdz(traderFinanceDto.getRegAddress());
        dataInfo.setGmflxdh(traderFinanceDto.getRegTel());
        dataInfo.setGmfkhh(traderFinanceDto.getBank());
        dataInfo.setGmfyhzh(traderFinanceDto.getBankAccount());
        dataInfo.setSpsl(detailDtoList.size());
        dataInfo.setHsbz("1");
        dataInfo.setSfzsgmfyhzh("N");
        dataInfo.setSfzsxsfyhzh("N");
        dataInfo.setBz(invoiceApplyDto.getOrderNo());

        List<SaleInvoiceOpenRequestDto.Mxzb> mxzbList = new ArrayList<>();
        dataInfo.setMxzbList(mxzbList);
        detailDtoList.forEach(d -> {
            // 明细
            SaleInvoiceOpenRequestDto.Mxzb mxzb = new SaleInvoiceOpenRequestDto.Mxzb();
            mxzb.setXh(d.getInvoiceApplyDetailId());
            mxzb.setFphxzDm("0");
            mxzb.setXmmc(d.getSkuName());
            mxzb.setSpfwjc(d.getTaxCategorySimpleName());
            mxzb.setSphfwssflhbbm(d.getTaxCategoryNo());
            mxzb.setHwhyslwfwmc(StrUtil.format("*{}*{}", mxzb.getSpfwjc(), mxzb.getXmmc()));
            String ggxhTpl = "{}{}";
            if (StrUtil.isNotBlank(d.getModel()) && StrUtil.isNotBlank(d.getSpec())) {
                ggxhTpl = "{}/{}";
            }
            mxzb.setGgxh(StrUtil.format(ggxhTpl, StrUtil.nullToEmpty(d.getModel()), StrUtil.nullToEmpty(d.getSpec())));
            mxzb.setDw(d.getUnitName());
            mxzb.setSpsl(this.formatNumberToString(d.getNum())); // 数量
            mxzb.setHsdj(this.formatNumberToDecimal(d.getPrice()));
            mxzb.setHsje(d.getTotalAmount());
            mxzb.setSlv(invoiceApplyDto.getTax().toString());
            BigDecimal taxAmount = mxzb.getHsje().multiply(new BigDecimal(mxzb.getSlv())).divide(BigDecimal.ONE.add(new BigDecimal(mxzb.getSlv())), 2, RoundingMode.HALF_UP);
            mxzb.setSe(taxAmount);
            mxzb.setBhsje(mxzb.getHsje().subtract(mxzb.getSe()));
            mxzb.setJe(mxzb.getBhsje());
            mxzb.setBhsdj(this.formatNumberToDecimal(mxzb.getBhsje().divide(d.getNum(), 8, RoundingMode.HALF_UP))); // 不含税单价
            mxzb.setDj(this.formatNumberToDecimal(mxzb.getBhsdj()));  // 单价
            mxzbList.add(mxzb);
        });

        dataInfo.setHjje(mxzbList.stream().map(SaleInvoiceOpenRequestDto.Mxzb::getJe).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        dataInfo.setHjse(mxzbList.stream().map(SaleInvoiceOpenRequestDto.Mxzb::getSe).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        dataInfo.setJshj(mxzbList.stream().map(SaleInvoiceOpenRequestDto.Mxzb::getHsje).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).toString());
        dataInfo.setJehj(0);

        return saleInvoiceOpenRequestDto;
    }
    
    private BigDecimal formatNumberToDecimal(BigDecimal formatNum) {
        if (Objects.isNull(formatNum)){
            return null;
        }
        String formatStr = formatNum.stripTrailingZeros().toPlainString();
        BigDecimal format = new BigDecimal(formatStr);
        return format;
    }

    private String formatNumberToString(BigDecimal formatNum) {
        if (Objects.isNull(formatNum)){
            return null;
        }
        String formatStr = formatNum.stripTrailingZeros().toPlainString();
        return formatStr;
    }


    /**
     * 新增销售蓝票
     *
     * @param invoiceApplyDto            开票申请
     * @param saleInvoiceOpenResponseDto 税金开票返回结果
     * @return 发票
     */
    @Transactional(rollbackFor = Throwable.class)
    public InvoiceDto createBlueInvoice(InvoiceApplyDto invoiceApplyDto, SaleInvoiceOpenResponseDto
            saleInvoiceOpenResponseDto) {
        InvoiceDto invoice = new InvoiceDto();

        invoice.setCompanyId(invoiceApplyDto.getCompanyId());
        invoice.setInvoiceProperty(FinanceConstant.FULLY_DIGITALIZE);
        invoice.setInvoiceCode(FinanceConstant.INVOICE_CODE);
        invoice.setInvoiceNo(saleInvoiceOpenResponseDto.getFphm());
        invoice.setType(invoiceApplyDto.getType());
        invoice.setTag(FinanceConstant.OPEN);
        invoice.setRelatedId(invoiceApplyDto.getRelatedId());
        invoice.setInvoiceType(invoiceApplyDto.getInvoiceType());
        invoice.setRatio(invoiceApplyDto.getTax());
        invoice.setColorType(FinanceConstant.BLUE);
        invoice.setIsEnable(ErpConstant.T);
        invoice.setAmount(invoiceApplyDto.getTotalAmount());
        invoice.setValidStatus(ErpConstant.AUDIT_PASS);
        invoice.setAddTime(DateUtil.current());
        invoice.setCreator(invoiceApplyDto.getUpdater());
        invoice.setInvoiceApplyId(invoiceApplyDto.getInvoiceApplyId());
        invoice.setOpenInvoiceTime(DateUtil.parseDateTime(saleInvoiceOpenResponseDto.getKprq()));
        List<InvoiceDetailDto> invoiceDetailDtoList = new ArrayList<>();
        invoice.setInvoiceDetailDtos(invoiceDetailDtoList);

        invoiceApplyDto.getInvoiceApplyDetailDtoList().forEach(d -> {
            InvoiceDetailDto detailDto = new InvoiceDetailDto();
            detailDto.setDetailgoodsId(d.getDetailgoodsId());
            detailDto.setPrice(d.getPrice());
            detailDto.setNum(d.getNum());
            detailDto.setTotalAmount(d.getTotalAmount());
            invoiceDetailDtoList.add(detailDto);
        });

        invoiceService.createInvoice(invoice);

        // 获取收票人手机号
        String saleOrderInvoiceTraderContactTelephone = saleOrderApiService.getSaleOrderInvoiceTraderContactMobile(invoice.getRelatedId());
        invoice.setInvoiceTraderContactMobile(saleOrderInvoiceTraderContactTelephone);

        return invoice;
    }


    /**
     * 调用税金系统接口
     * 1。检查税金系统接口税金记录表是否已有该开票记录
     * 2. 成功 新增成功的 组装税金系统接口税金记录表 - 销项票下载 和 销项票开具
     *
     * @param invoiceApplyDto 发票申请
     * @return SaleInvoiceOpenResponseDto
     */
    @Transactional(rollbackFor = Throwable.class)
    public SaleInvoiceOpenResponseDto invokeSaleOrderInvoiceApi(InvoiceApplyDto invoiceApplyDto) {

        log.info("数电发票开蓝票:调用税金系统接口 参数{}", JSON.toJSONString(invoiceApplyDto));

        String orderNo = invoiceApplyService.getSaleOrderNoByInvoiceApplyId(invoiceApplyDto.getInvoiceApplyId());
        invoiceApplyDto.setOrderNo(StrUtil.isEmpty(orderNo)?"":orderNo);
        // 组装税金系统发票接口参数
        SaleInvoiceOpenRequestDto saleInvoiceOpenRequestDto = this.assembleInvoice(invoiceApplyDto);
        SaleInvoiceOpenResponseDto saleInvoiceOpenResponseDto = (SaleInvoiceOpenResponseDto) taxesOpenApiService.openapi(saleInvoiceOpenRequestDto,
                TaxesInterfaceCodeEnum.OPEN);
        log.info("数电发票开蓝票:调用税金系统接口 返回值{}", JSON.toJSONString(saleInvoiceOpenResponseDto));

        if (saleInvoiceOpenResponseDto.getIsSuccess()) {
            // 存在成功但是发票号无的情况，当无发票号认定为失败
            if (StrUtil.isEmpty(saleInvoiceOpenResponseDto.getFphm())) {
                throw new InvoiceException(saleInvoiceOpenResponseDto);
            }

            this.openBlueInvoiceInvokeSuccess(saleInvoiceOpenRequestDto, saleInvoiceOpenResponseDto);
            return saleInvoiceOpenResponseDto;
        }
        return saleInvoiceOpenResponseDto;
    }

    /**
     * 成功
     * 组装税金系统接口税金记录表 - 销项票下载 和 销项票开具
     *
     * @param saleInvoiceOpenRequestDto  入参
     * @param saleInvoiceOpenResponseDto 出餐
     */
    @Transactional(rollbackFor = Throwable.class)
    public void openBlueInvoiceInvokeSuccess(SaleInvoiceOpenRequestDto saleInvoiceOpenRequestDto,
                                             SaleInvoiceOpenResponseDto saleInvoiceOpenResponseDto) {
        // 获取税金系统接口调用记录表 是否有数据 当有数据 无论是否失败，更新为成功状态
        InvoiceTaxSystemRecordDto whetherExist = new InvoiceTaxSystemRecordDto();
        whetherExist.setBusinessId(saleInvoiceOpenRequestDto.getData().getOrderNo());
        whetherExist.setInterfaceType(InvoiceSystemInterfaceTypeEnum.SALE_OPEN.getType());
        InvoiceTaxSystemRecordDto exist = invoiceTaxSystemRecordService.isExist(whetherExist);
        if (exist != null) {
            exist.setBodyOrg(JSON.toJSONString(saleInvoiceOpenRequestDto));
            exist.setResult(JSON.toJSONString(saleInvoiceOpenResponseDto));
            invoiceTaxSystemRecordService.success(exist);
            // 调用销项票下载
            this.invokeBlueInvoiceInvoiceDown(saleInvoiceOpenResponseDto, saleInvoiceOpenRequestDto.getData().getOrderNo());
            return;
        }
        // 生成税金系统接口税金记录表 - 销项票开具
        InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto = invoiceTaxSystemRecordService.fromTaxesApiInfoInitInvoiceTaxSystemRecord(saleInvoiceOpenRequestDto,
                saleInvoiceOpenResponseDto);
        invoiceTaxSystemRecordDto.setBusinessId(saleInvoiceOpenRequestDto.getData().getOrderNo());
        invoiceTaxSystemRecordService.success(invoiceTaxSystemRecordDto);

        // 调用销项票下载
        this.invokeBlueInvoiceInvoiceDown(saleInvoiceOpenResponseDto, saleInvoiceOpenRequestDto.getData().getOrderNo());
    }


    /**
     * 失败
     * 1. 则新增一条失败的 组装税金系统接口税金记录表 - 销项票开具 记录
     * 2. 企业微信群告警
     *
     * @param invoiceApplyDto  开票申请
     * @param invoiceException 异常信息
     */
    @Transactional(rollbackFor = Throwable.class)
    public void openBlueInvoiceFail(InvoiceApplyDto invoiceApplyDto, InvoiceException invoiceException) {
        TaxesReturnInfo taxesReturnInfo = invoiceException.getTaxesReturnInfo();
        SaleInvoiceOpenResponseDto saleInvoiceOpenResponseDto = new SaleInvoiceOpenResponseDto();
        saleInvoiceOpenResponseDto.setReturnMessage(taxesReturnInfo.getReturnMessage());
        saleInvoiceOpenResponseDto.setReturnCode(taxesReturnInfo.getReturnCode());
        // 组装税金系统接口税金记录表 - 销项票开具
        InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto = invoiceTaxSystemRecordService.fromTaxesApiInfoInitInvoiceTaxSystemRecord(
                null,
                saleInvoiceOpenResponseDto);

        invoiceTaxSystemRecordDto.setBusinessId(invoiceApplyDto.getInvoiceApplyId().toString());

        // 判断当前是否存在
        InvoiceTaxSystemRecordDto old = this.getInvoiceTaxSystemRecordDto(invoiceApplyDto, InvoiceSystemInterfaceTypeEnum.SALE_OPEN,
                null);
        if (old != null) {
            invoiceTaxSystemRecordDto.setInvoiceTaxSystemRecordId(old.getInvoiceTaxSystemRecordId());
        }
        invoiceTaxSystemRecordService.fail(invoiceTaxSystemRecordDto);
        // 接口返回失败 企业微信群告警
        this.retryOut(invoiceTaxSystemRecordDto,
                StrUtil.format(WxRobotMsgTemple.OPEN_INVOICE_FAIL_ERROR,
                        invoiceApplyDto.getInvoiceApplyId().toString(),
                        saleInvoiceOpenResponseDto.getReturnMessage()));
    }

    /**
     * 生成税金系统接口税金记录表并调用接口 - 销项票下载
     * 根据 saleInvoiceOpenResponseDto 构造成功失败记录
     *
     * @param saleInvoiceOpenResponseDto 出参
     * @param businessId                 开票申请id
     */
    @Transactional(rollbackFor = Throwable.class)
    public void invokeBlueInvoiceInvoiceDown(SaleInvoiceOpenResponseDto saleInvoiceOpenResponseDto,
                                             String businessId) {
        DownloadInvoice downloadInvoice = new DownloadInvoice();
        downloadInvoice.setInvoiceNo(saleInvoiceOpenResponseDto.getFphm());
        downloadInvoice.setInvoiceDate(saleInvoiceOpenResponseDto.getKprq());
        downloadInvoice.setInvoiceApplyId(businessId);
        this.downloadInvoice(downloadInvoice);
    }





    /**
     * 重试次数超出，发送消息
     *
     * @param invoiceTaxSystemRecordDto 税金任务记录
     * @param msg                       错误消息
     */
    @Override
    public void retryOut(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto, String msg) {
        if (invoiceTaxSystemRecordDto.getRetryNum() > taxesConfig.retryNum) {
            this.wxErrorNotice(msg);
        }
    }

    /**
     * 企业微信群告警
     *
     * @param msg 错误消息
     */
    private void wxErrorNotice(String msg) {
        WxMsgDto wxMsgDto = new WxMsgDto().initWxMsgDto(msg);
        wxRobotService.send(invoiceNoticeRobotNum, wxMsgDto);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void downloadInvoice(DownloadInvoice downloadInvoice) {

        log.info("downloadInvoice 入参:{}", JSON.toJSONString(downloadInvoice));
        InvoiceTaxSystemRecordDto dto = new InvoiceTaxSystemRecordDto();
        dto.setBusinessId(downloadInvoice.getInvoiceApplyId());
        dto.setInterfaceType(TaxesMappingEnum.getInterfaceType(SaleInvoiceDownResponseDto.class));

        if (!downloadInvoice.getSkipCheckExtend()) {
            InvoiceTaxSystemRecordDto exist = invoiceTaxSystemRecordService.isExist(dto);

            if (Objects.nonNull(exist)) {
                return;
            }
        }

        // 组装税金系统接口税金记录表 - 销项票下载
        SaleInvoiceDownRequestDto saleInvoiceDownRequestDto = new SaleInvoiceDownRequestDto();
        saleInvoiceDownRequestDto.setFphm(downloadInvoice.getInvoiceNo());
        saleInvoiceDownRequestDto.setKprq(downloadInvoice.getInvoiceDate());
        saleInvoiceDownRequestDto.setFiletype(TaxesConstant.PDF);
        SaleInvoiceDownResponseDto saleInvoiceDownResponseDto = new SaleInvoiceDownResponseDto();
        saleInvoiceDownResponseDto.setReturnCode(TaxesReturnCodeEnum.FAIL.getCode());

        dto.setIsDelete(ErpConstant.F);
        dto.setRunningStatus(FinanceConstant.FAIL);
        dto.setRetryNum(0);
        dto.setBodyOrg(JSON.toJSONString(saleInvoiceDownRequestDto));
        dto.setResult(JSON.toJSONString(saleInvoiceDownResponseDto));
        dto.setInvoiceNo(downloadInvoice.getInvoiceNo());
        // 插入税金系统接口税金记录表
        log.info("downloadInvoice init:{}", JSON.toJSONString(dto));
        invoiceTaxSystemRecordService.init(dto);

    }

    @Override
    public void errorRetryDownInvoice(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto, String type) {

        log.info("errorRetryDownInvoice:入参:{},type:{}", JSON.toJSONString(invoiceTaxSystemRecordDto), type);
        if (TaxesConstant.PDF.equals(type)) {
            doPdf(invoiceTaxSystemRecordDto);
        }
        if (TaxesConstant.XML.equals(type)) {
            doXml(invoiceTaxSystemRecordDto);
        }

    }

    @Override
    public String getBlueInvoiceOpenDate(String invoiceNo) {
        SaleInvoiceBlueInfoRequestDto saleInvoiceBlueInfoRequestDto = new SaleInvoiceBlueInfoRequestDto();
        saleInvoiceBlueInfoRequestDto.setFphm(invoiceNo);
        SaleInvoiceBlueInfoResponseDto saleInvoiceBlueInfoResponseDto = (SaleInvoiceBlueInfoResponseDto) taxesOpenApiService.openapi(saleInvoiceBlueInfoRequestDto,
                TaxesInterfaceCodeEnum.BLUE_INFO);
        if (saleInvoiceBlueInfoResponseDto.getIsSuccess()) {
            return saleInvoiceBlueInfoResponseDto.getKprq();
        }
        return null;
    }

    @Transactional(rollbackFor = Throwable.class)
    public void doPdf(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto) {
        // 接口
        SaleInvoiceDownDefinedRequestDto saleInvoiceDownRequestDto = JSON.parseObject(invoiceTaxSystemRecordDto.getBodyOrg(), SaleInvoiceDownDefinedRequestDto.class);
        saleInvoiceDownRequestDto.setFiletype(TaxesConstant.PDF);
        log.info("调用发票下载接口:{}", JSON.toJSONString(saleInvoiceDownRequestDto));

        // 税金要求下载前 先调用下这个接口
        SaleInvoiceLimitRequestDto requestDto = new SaleInvoiceLimitRequestDto();
        taxesOpenApiService.openapi(requestDto, TaxesInterfaceCodeEnum.LIMIT);

        InvoiceDto byInvoiceNo = invoiceService.findByInvoiceNo(saleInvoiceDownRequestDto.getFphm());
        if (Objects.isNull(byInvoiceNo)) {
            log.error("未查到开票申请对应发票信息,入参：{}",JSON.toJSONString(invoiceTaxSystemRecordDto));
            throw new ServiceException("发票:"+saleInvoiceDownRequestDto.getFphm()+",未查询到订单信息");
        }
        String fileName = getFileName(byInvoiceNo);
        saleInvoiceDownRequestDto.setFileName(fileName);
        SaleInvoiceDownResponseDto result = (SaleInvoiceDownResponseDto) taxesOpenApiService.openapi(saleInvoiceDownRequestDto, TaxesInterfaceCodeEnum.DOWN);
        log.info("调用发票下载接口返回结果:{}", JSON.toJSONString(result));
        // 是否success
        FullyDigitalInvoiceServiceImpl bean = SpringUtil.getBean(FullyDigitalInvoiceServiceImpl.class);
        if (result.getIsSuccess()) {
            try {
                bean.doDownloadPdfSuccess(saleInvoiceDownRequestDto, result, invoiceTaxSystemRecordDto.getInvoiceTaxSystemRecordId());
            } catch (Exception e) {
                log.warn("下载后更新发票异常", e);
                bean.doDownloadPdfError(saleInvoiceDownRequestDto, result, invoiceTaxSystemRecordDto.getInvoiceTaxSystemRecordId());
            }
        } else {
            bean.doDownloadPdfError(saleInvoiceDownRequestDto, result, invoiceTaxSystemRecordDto.getInvoiceTaxSystemRecordId());
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    public void doXml(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto) {
        // 接口
        SaleInvoiceDownDefinedRequestDto saleInvoiceDownRequestDto = JSON.parseObject(invoiceTaxSystemRecordDto.getBodyOrg(), SaleInvoiceDownDefinedRequestDto.class);
        saleInvoiceDownRequestDto.setFiletype(TaxesConstant.XML);
        log.info("调用发票下载接口:{}", JSON.toJSONString(saleInvoiceDownRequestDto));

        InvoiceDto byInvoiceNo = invoiceService.findByInvoiceNo(saleInvoiceDownRequestDto.getFphm());
        if (Objects.isNull(byInvoiceNo)) {
            log.error("未查到开票申请对应发票信息,入参：{}",JSON.toJSONString(invoiceTaxSystemRecordDto));
            throw new ServiceException("发票:"+saleInvoiceDownRequestDto.getFphm()+",未查询到订单信息");
        }
        String fileName = getFileName(byInvoiceNo);
        saleInvoiceDownRequestDto.setFileName(fileName);
        SaleInvoiceDownResponseDto result = (SaleInvoiceDownResponseDto) taxesOpenApiService.openapi(saleInvoiceDownRequestDto, TaxesInterfaceCodeEnum.DOWN);
        log.info("调用发票下载接口返回结果:{}", JSON.toJSONString(result));
        // 是否success
        FullyDigitalInvoiceServiceImpl bean = SpringUtil.getBean(FullyDigitalInvoiceServiceImpl.class);
        if (result.getIsSuccess()) {
            try {
                bean.doDownloadXmlSuccess(saleInvoiceDownRequestDto, result, invoiceTaxSystemRecordDto.getInvoiceTaxSystemRecordId());
            } catch (Exception e) {
                log.warn("下载后更新发票异常", e);
                bean.doDownloadXmlError(saleInvoiceDownRequestDto, result, invoiceTaxSystemRecordDto.getInvoiceTaxSystemRecordId());
            }
        } else {
            bean.doDownloadXmlError(saleInvoiceDownRequestDto, result, invoiceTaxSystemRecordDto.getInvoiceTaxSystemRecordId());
        }
        try {
            Thread.sleep(5000);//航信xml下载接口需要间隔5秒
        }catch (Exception e){
            log.error("xml发票下载异常",e);
        }

    }

    /**
     * 获取文件名
     * @param invoiceDto
     * @return
     */
    private String getFileName(InvoiceDto invoiceDto){
        String addTime  =DateFormatUtils.format(invoiceDto.getAddTime(), DatePattern.PURE_DATETIME_PATTERN);
        String bizNo = StrUtil.isNotBlank(invoiceDto.getAfterSalesNo()) ? invoiceDto.getAfterSalesNo() : invoiceDto.getOrderNo();
        String invoiceNo = invoiceDto.getInvoiceNo();
        StringBuilder sb = new StringBuilder();
        sb.append(addTime).append(StrUtil.UNDERLINE).append(bizNo).append(StrUtil.UNDERLINE).append(invoiceNo);
        String fileName = sb.toString();
        return fileName;
    }

    @Transactional(rollbackFor = Throwable.class)
    public void doDownloadPdfError(SaleInvoiceDownRequestDto res, SaleInvoiceDownResponseDto result, Long invoiceTaxSystemRecordId) {
        doDownInvoiceError(res, result, invoiceTaxSystemRecordId);
    }

    /**
     * 下载发票异常
     *
     * @param res                      请求
     * @param result                   返回结果
     * @param invoiceTaxSystemRecordId id
     */
    private void doDownInvoiceError(SaleInvoiceDownRequestDto res, SaleInvoiceDownResponseDto result, Long invoiceTaxSystemRecordId) {
        InvoiceTaxSystemRecordDto dto = invoiceTaxSystemRecordService.fromTaxesApiInfoInitInvoiceTaxSystemRecord(res, result);
        dto.setInvoiceTaxSystemRecordId(invoiceTaxSystemRecordId);
        invoiceTaxSystemRecordService.fail(dto);

        String format = StrUtil.format(WxRobotMsgTemple.DOWN_INVOICE_ERROR, res.getFiletype(),
                invoiceTaxSystemRecordId,
                res.getFphm(),
                JSON.toJSONString(result));
        this.retryOut(dto, format);
    }


    @Transactional(rollbackFor = Throwable.class)
    public void doDownloadPdfSuccess(SaleInvoiceDownRequestDto res, SaleInvoiceDownResponseDto result, Long invoiceTaxSystemRecordId) {
        InvoiceTaxSystemRecordDto dto = invoiceTaxSystemRecordService.fromTaxesApiInfoInitInvoiceTaxSystemRecord(res, result);
        dto.setInvoiceTaxSystemRecordId(invoiceTaxSystemRecordId);
        dto.setUrlPdf(result.getOssUrl());
        invoiceTaxSystemRecordService.success(dto);

        InvoiceDto invoiceDto = new InvoiceDto();
        invoiceDto.setInvoiceHref(result.getOssUrl());
        invoiceDto.setInvoiceNo(res.getFphm());
        invoiceDto.setOssFileUrl(result.getOssUrl());
        if (StrUtil.isNotEmpty(result.getOssUrl())) {
            String ossUrl = result.getOssUrl();
            List<String> split = StrUtil.split(ossUrl, "=");
            if (split.size() > 1) {
                invoiceDto.setResourceId(split.get(split.size() - 1));
            }
        }

        invoiceApiService.updateDownloadData(invoiceDto);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void doDownloadXmlSuccess(SaleInvoiceDownRequestDto res, SaleInvoiceDownResponseDto result, Long invoiceTaxSystemRecordId) {
        InvoiceTaxSystemRecordDto dto = invoiceTaxSystemRecordService.fromTaxesApiInfoInitInvoiceTaxSystemRecord(res, result);
        dto.setInvoiceTaxSystemRecordId(invoiceTaxSystemRecordId);
        dto.setUrlXml(result.getOssUrl());
        invoiceTaxSystemRecordService.success(dto);
    }

    @Transactional(rollbackFor = Throwable.class)
    public void doDownloadXmlError(SaleInvoiceDownRequestDto res, SaleInvoiceDownResponseDto result, Long invoiceTaxSystemRecordId) {
        doDownInvoiceError(res, result, invoiceTaxSystemRecordId);
    }
    private Boolean checkIsRedInvoice(Integer invoiceId) {
        InvoiceDto invoiceBaseInfoByInvoiceId = invoiceApiService.findbyInvoiceId(invoiceId);
        //红字为1,数电发票为3
        if(FinanceConstant.FULLY_DIGITALIZE.equals(invoiceBaseInfoByInvoiceId.getInvoiceProperty())&&FinanceConstant.RED.equals(invoiceBaseInfoByInvoiceId.getColorType())){
            return true;
        }
        return false;
    }

    @Override
    public String getInvoiceQrCode(Integer invoiceId) {
        //校验是否为数电红字发票
        Boolean invoiceBoolean = this.checkIsRedInvoice(invoiceId);
        if (invoiceBoolean){
            return "-101";
        }
        BufferedImage image = null;
        String base64String = null;
        try {
            InvoiceDto invoiceDto = invoiceApiService.findbyInvoiceId(invoiceId);
            if (invoiceDto != null) {
                String invoiceNo = invoiceDto.getInvoiceNo();
                String openInvoiceTime = DateFormatUtils.format(invoiceDto.getOpenInvoiceTime(), "yyyy-MM-dd HH:mm:ss");
                SaleInvoiceQrRequestDto requestDto = new SaleInvoiceQrRequestDto();
                requestDto.setFphm(invoiceNo);
                requestDto.setKprq(openInvoiceTime);
                requestDto.setNsrsbh(taxesConfig.taxNo);
                log.info("数电发票二维码交付功能外部接口入参, requestDto: {}", JSON.toJSONString(requestDto));
                SaleInvoiceQrResponseDto openapi = (SaleInvoiceQrResponseDto) taxesOpenApiService.openapi(requestDto, TaxesInterfaceCodeEnum.QR_CODE);
                log.info("数电发票二维码交付功能外部接口出参, openapi: {}", JSON.toJSONString(openapi));
                String data = openapi.getData();
                if (StringUtil.isBlank(data) || !data.contains("http")) {
                    log.info("未查询到发票信息, requestDto: {}, openapi: {}", JSON.toJSONString(requestDto),JSON.toJSONString(openapi));
                    return "-102";
                }
                QRCode qrCode = new QRCode();
                qrCode.setContent(openapi.getData());
                // 生成二维码图片流
                image = QRCodeUtils.createImage(qrCode.getContent(), "", true);
                log.info("数电发票二维码交付功能成功, 发票ID: {}", invoiceId);
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(image, "png", baos);
                baos.flush();
                byte[] imageInByte = baos.toByteArray();
                baos.close();
                base64String = Base64.getEncoder().encodeToString(imageInByte);
            }
        } catch (Exception e) {
            log.error("数电发票二维码交付功能失败, 发票ID: {}", invoiceId, e);
            return "-103";
        } finally {
            if (image != null) {
                image.flush();
            }
        }

        return base64String;
    }
}
