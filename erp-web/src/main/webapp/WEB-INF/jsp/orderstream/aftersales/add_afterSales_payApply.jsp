<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增付款申请" scope="application" />
<%@ include file="../../common/common.jsp"%>

<script type="text/javascript" >
    $(function(){
        $("#myform").submit(function(){
            checkLogin();
            var payApplyTotalAmount = $("#payApplyTotalAmount").val();

            var regPos = /^\d+(\.\d+)?$/; //非负浮点数
            //负浮点数
            var regNeg = /^(-(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)))$/;

            if(!regPos.test(payApplyTotalAmount) && !regNeg.test(payApplyTotalAmount)) {
                warnTips("amountError"," 请输入输入数字字符，保留小数点后两位");
                return false;
            }

            if(payApplyTotalAmount <= 0){
                warnErrorTips("payApplyTotalAmount","payApplyTotalAmount","申请付款金额不可小于等于0");//文本框ID和提示用语
                return false;
            }
            if (parseFloat($("#payApplyTotalAmount").val()) > parseFloat($('#canApplyAmount').val())){
                warnErrorTips("payApplyTotalAmount","payApplyTotalAmount","申请付款金额不可大于可申请金额");//文本框ID和提示用语
                return false;
            }
            var comments = $("#comments").val();
            if(comments.length > 200){
                warnErrorTips("commentsError","commentsError","付款备注不得超过200字符");//文本框ID和提示用语
                return false;
            }
            $.ajax({
                url:page_url+'/order/afterSalesCommon/saveApplyPayment.do',
                data:$('#myform').serialize(),
                type:"POST",
                dataType : "json",
                async: false,
                success:function(data)
                {
                    if(data.code==0){
                        window.parent.location.reload();
                    }else{
                        layer.alert(data.message);
                    }

                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
            return false;
        })
    })
</script>

<div class="form-list form-tips8">
    <form method="post" id="myform" >
        <ul>
             <li>
                 <div class="form-tips">
                     <lable>业务类型：</lable>
                 </div>
                 <div class="f_left ">
                     <div class="form-blanks">
                         <ul>
                             <li>
                                 退款
                             </li>
                         </ul>
                     </div>
                 </div>
             </li>

             <li id="li0">
               <div class="form-tips">
                   <lable>交易方式：</lable>
               </div>
               <div class="f_left ">
                   <div class="form-blanks">
                       <ul>
                            <li>
                                <c:if test="${afterSales.traderMode eq 521}">
                                    <label>银行</label>
                                </c:if>
                                <c:if test="${afterSales.traderMode eq 520}">
                                    <label>支付宝</label>
                                </c:if>
                            </li>
                       </ul>    
                   </div>
               </div>
           </li>
             <li id="li1">
               <div class="form-tips">
                   <span>*</span>
                   <lable>交易主体</lable>
               </div>
               <div class="f_left ">
                   <div class="form-blanks">
                       <ul>
                            <li>
                                <c:if test="${afterSales.traderSubject eq 1}">
                                    <label>对公</label>
                                </c:if>
                                <c:if test="${afterSales.traderSubject eq 2}">
                                    <label>对私</label>
                                </c:if>
                            </li>
                       </ul>    
                   </div>
               </div>
           </li>
			<li id="li2">
               <div class="form-tips">
                   <lable>交易名称</lable>
               </div>
               <div class="f_left ">
                   <div class="form-blanks">
                       <label>${afterSales.payee}</label>
                   </div>
               </div>
            </li>
            <li id="li3">
               <div class="form-tips">
                   <lable>开户银行</lable>
               </div>
               <div class="f_left ">
                   <div class="form-blanks">
                       <label>${afterSales.bank}</label>
                   </div>
               </div>
            </li>
            <li id="li4">
               <div class="form-tips">
                   <span>*</span>
                   <lable>银行/支付宝账号 </lable>
               </div>
               <div class="f_left ">
                   <div class="form-blanks">
                       <label>${afterSales.bankAccount}</label>
                   </div>
               </div>
            </li>
            <li id="li5">
               <div class="form-tips">
                   <lable>开户行支付联行号</lable>
               </div>
               <div class="f_left ">
                   <div class="form-blanks">
                       <label>${afterSales.bankCode}</label>
                   </div>
               </div>
            </li>
            <li id="li6">
                <div class="form-tips">
                    <span>*</span>
                    <lable>申请付款金额</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input  name="payApplyTotalAmount" id="payApplyTotalAmount" value="${canApplyAmount}">
                        <label style="margin-top: 5px">/${canApplyAmount}</label>
                    </div>
                    <div id="amountError"></div>
                </div>
            </li>
            <li id="li7">
                <div class="form-tips">
                    <lable>备注</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input  name="comments" id="comments">
                    </div>
                    <div id="commentsError"></div>
                </div>
            </li>

        </ul>
            <div class="add-tijiao tcenter">
            	<input type="hidden" name="beforeParams" value='${beforeParams}'/>
            	<input type="hidden" name="relatedId" value="${afterSales.afterSalesId}" />
            	<input type="hidden" name="traderMode" value="${afterSales.traderMode}" />
            	<input type="hidden" name="afterSalesDetailId" value="${afterSales.afterSalesDetailId}" />
                <input type="hidden" name="refundAmountStatus" value="${afterSales.refundAmountStatus}" />
                <input type="hidden" name="refundTwo" id="refundTwo" value="${afterSales.refund}">
                <input type="hidden" name="canApplyAmount" id="canApplyAmount" value="${canApplyAmount}">
                <input type="hidden" name="formToken" value="${formToken}"/>
                <button type="submit" id="sub">提交</button>
                <button type="button" class="dele" id="close-layer">取消</button>
            </div>
   </form>
</div>
<%@ include file="../../common/footer.jsp"%>