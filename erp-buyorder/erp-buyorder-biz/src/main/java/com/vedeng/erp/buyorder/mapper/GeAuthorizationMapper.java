package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.GeAuthorization;

import java.util.List;

public interface GeAuthorizationMapper {
    int deleteByPrimaryKey(Integer authorizationId);

    int insert(GeAuthorization record);

    int insertSelective(GeAuthorization record);

    GeAuthorization selectByPrimaryKey(Integer authorizationId);

    int updateByPrimaryKeySelective(GeAuthorization record);

    int updateByPrimaryKey(GeAuthorization record);

    List<GeAuthorization> countGeBussinessChanceHaveNoCloseGeAuthorization(GeAuthorization geAuthorization);
}