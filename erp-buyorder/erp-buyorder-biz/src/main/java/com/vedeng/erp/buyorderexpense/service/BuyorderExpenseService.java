package com.vedeng.erp.buyorderexpense.service;

import com.vedeng.erp.aftersale.domain.entity.ExpenseReturnEarlyWarnEntity;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 采购费用单接口
 * @date 2022/8/22 15:10
 **/
public interface BuyorderExpenseService {

    /**
     * 采购费用单 详情
     * @param buyorderExpenseId 采购单id
     * @return BuyorderExpenseDto 页面对象
     */
    BuyorderExpenseDto viewDetail(Integer buyorderExpenseId);

    /**
     * 编辑页面根据ID查询费用订单信息
     *
     * @param buyorderExpenseId buyorderExpenseId
     * @return BusinessLeadsDto
     */
    BuyorderExpenseDto getBuyorderExpenseEditInfo(Integer buyorderExpenseId);

    /**
     * @description: 采购费用单合同
     * @return:
     * @author: Strange
     * @date: 2022/8/24
     **/
    BuyorderExpenseDto printDetail(Integer buyorderExpenseId);

    /**
     * 根据采购单id查询对应的采购虚拟商品集合
     *
     * @param buyOrderId 采购单id
     * @return List<BuyorderExpenseItemDto>
     */
    List<BuyorderExpenseItemDto> getByBuyOrderId(Integer buyOrderId);

    /**
     * 根据采购单id查询对应的采购费用订单信息集合（包括采购单生效前和生效后创建的费用单）
     *
     * @param buyOrderId 采购单id
     * @return 采购费用单集合
     */
    List<BuyorderExpenseDto> getBuyOrderExpenseDtoListByBuyOrderId(Integer buyOrderId);

    /**
     * 判断费用单是否可以售后
     * @param buyorderExpenseId 费用单id
     * @return boolean
     */
    boolean checkExpenseCanAfterSales(Integer buyorderExpenseId);


    /**
     * 退货预警采购费用订单分摊情况
     * @param expenseReturnEarlyWarn 退货预警
     * @return List<Integer> 触发预警的 采购费用单列表
     */
    List<Integer> returnAlertBuyOrderExpenseGoodsShare(ExpenseReturnEarlyWarnEntity expenseReturnEarlyWarn);


    /**
     * 解除采购费用订单退货预警
     * @param buyorderExpenseId 采购费用单ID
     */
    void freeReturnAlertBuyOrderExpenseGoods(Integer buyorderExpenseId);

    /**
     * 直接解除费用订单部分预警
     * @param expenseReturnEarlyWarn
     */
    void freePartReturnAlertBuyOrderExpenseGoods(ExpenseReturnEarlyWarnEntity expenseReturnEarlyWarn);

    /**
     * 扣减后 计算费用订单判断是否需要解除预警
     * @param expenseReturnEarlyWarn
     */
    void freePartReturnAlertBuyOrderExpenseGoodsByCompute(ExpenseReturnEarlyWarnEntity expenseReturnEarlyWarn);



    /**
     * 根据待采购订单信息采购费用单数据
     *
     * @param saleOrderGoodsIdList 销售单明细id
     * @return
     */
    BuyorderExpenseDto byPreBuyorderGetDetail(List<Integer> saleOrderGoodsIdList);
}
