<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>商品信息(财务专用)</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<script></script>
<body id="sku-list-finance" datasource="erp-datasourcetarget" fixednumber="" fixednumberright="" append_column_url="" append_row_url="" empty_show="" firstcol="">
<div id="appendHead"></div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline "><label class="layui-form-label">商品名称</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="SHOW_NAME" placeholder style alias="K" jdbctype data datatype oper="LIKE" validate_rules validate_messages>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">订货号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="SKU_NO" placeholder style alias="F" jdbctype data datatype oper="EQ" validate_rules validate_messages>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">商品类别</label>
            <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="SPU_TYPE" placeholder="默认全部" style="" alias="P" jdbctype="" data="SELECT SYS_OPTION_DEFINITION_ID AS K, TITLE AS V FROM T_SYS_OPTION_DEFINITION WHERE STATUS = 1 AND PARENT_ID = 315 AND SYS_OPTION_DEFINITION_ID NOT IN (319, 653) " datatype="KVSQL" oper="EQ" validate_rules="" validate_messages=""></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">归属(经理)</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="ASSIGNMENT_MANAGER_ID" placeholder="默认全部" style alias="P" jdbctype data="select distinct U.USER_ID AS K,LOWER(U.USERNAME) AS V from T_USER U LEFT JOIN T_R_USER_ROLE RB ON U.USER_ID=RB.USER_ID
	LEFT JOIN T_ROLE R ON RB.ROLE_ID=R.ROLE_ID
	WHERE R.ROLE_NAME IN ('产品专员','产品主管','产品总监')
	AND U.IS_DISABLED='0'" datatype="KVSQLCACHE" oper="EQ" validate_rules validate_messages>
                </select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">是否医疗器械</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="IS_MEDICAL_EQUIPMENT" placeholder="默认全部" style="" alias="F" jdbctype=""
                        data='[{"V":"是","K":"1"},{"V":"否","K":"0"}]'
                        datatype="json" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">医疗器械细分类</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="MEDICAL_EQUIPMENT_TYPE" placeholder style alias="F" jdbctype data datatype oper="LIKE" validate_rules validate_messages>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">医疗器械用途</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="MEDICAL_EQUIPMENT_USE" placeholder style alias="F" jdbctype data datatype oper="LIKE" validate_rules validate_messages>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">医疗器械产线</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="MEDICAL_EQUIPMENT_LINE" placeholder style alias="F" jdbctype data datatype oper="LIKE" validate_rules validate_messages>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">创建时间</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="daterange" name="SKU_ADD_TIME" placeholder style="" alias="F" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">推送金蝶时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="PUSH_TIME" placeholder style="" alias="F" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">是否推送金蝶</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="IS_PUSH" placeholder="默认全部" style="" alias="F" jdbctype=""
                        data='[{"V":"是","K":"1"},{"V":"否","K":"0"}]'
                        datatype="json" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">是否安调</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="IS_INSTALLATION" placeholder="默认全部" style="" alias="F" jdbctype=""
                        data='[{"V":"是","K":"1"},{"V":"否","K":"0"}]'
                        datatype="json" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">审核状态</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="AUDIT_STATUS" placeholder="默认全部" style="" alias="F" jdbctype=""
                        data='[{"V":"待提交","K":"0"},{"V":"审核中","K":"1"},{"V":"审核通过","K":"2"},{"V":"已驳回","K":"3"}]'
                        datatype="json" oper=""></select>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
        <button type="table" class="layui-btn" url="/goods/finance/upLoadFile.do" opentype="MODEL" windowname="批量修改商品信息" area="600px,400px">批量处理</button>
        <button type="table" class="layui-btn" url="/ezadmin/list/export-sku-list-finance" opentype="_BLANK_PARAM" windowname="导出">导出</button>
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th type="rowbutton" id="rowbutton">
                <button item_name="修改"
                        name="edit"
                        url="/goods/finance/edit.do?goodsFinanceId=${GOODS_FINANCE_ID}"
                        opentype="MODEL"
                        windowname="商品信息(财务专用)修改"
                        encrypt_list_id="sku-list-finance"
                        item_label="修改"
                        open_type="MODEL"
                        window_name="商品信息(财务专用)修改"
                        plugin_code="button-single"
                        type="single"></button></th>
            <th type="rowbutton" id="rowbutton">
                <button item_name="安调维护"
                        name="audit"
                        url="/goods/finance/audit.do?goodsFinanceId=${GOODS_FINANCE_ID}"
                        opentype="MODEL"
                        windowname="安调维护"
                        encrypt_list_id="sku-list-finance"
                        item_label="安调维护"
                        plugin_code="button-single"
                        area="800px,800px"
                        type="single"></button>
            </th>
            <th item_name="SKU_NO" name="SKU_NO" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" style="text-align: center">订货号</th>
            <th item_name="SHOW_NAME" name="SHOW_NAME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">产品名称</th>
            <th item_name="UNIT_NAME" name="UNIT_NAME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">商品单位</th>
            <th item_name="TITLE" name="TITLE" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">商品类别</th>
            <th item_name="USERNAME" name="USERNAME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">归属(经理)</th>
            <th item_name="IS_MEDICAL_EQUIPMENT" name="IS_MEDICAL_EQUIPMENT" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">是否医疗器械</th>
            <th item_name="MEDICAL_EQUIPMENT_TYPE" name="MEDICAL_EQUIPMENT_TYPE" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">医疗器械细分类</th>
            <th item_name="MEDICAL_EQUIPMENT_USE" name="MEDICAL_EQUIPMENT_USE" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">医疗器械用途</th>
            <th item_name="MEDICAL_EQUIPMENT_LINE" name="MEDICAL_EQUIPMENT_LINE" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">医疗器械产线</th>
            <th item_name="SKU_ADD_TIME" name="SKU_ADD_TIME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">创建时间</th>
            <th item_name="PUSH_TIME" name="PUSH_TIME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">推送金蝶时间</th>
            <th item_name="IS_PUSH" name="IS_PUSH" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" style="text-align: center">是否推送金蝶</th>
            <th item_name="IS_INSTALLATION" name="IS_INSTALLATION" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" style="text-align: center">是否安调</th>
            <th item_name="AUDIT_STATUS" name="AUDIT_STATUS" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" style="text-align: center">审核状态</th>

        </tr>
        </thead>
        <tbody>
        <tr>
            <td> express:</td>
            <td colspan="100"><pre id="express" class="layui-code" orderby=" ORDER BY F.SKU_ADD_TIME DESC " groupby="">StringBuilder sql=new StringBuilder();
sql.append("
    SELECT
        F.GOODS_FINANCE_ID,
        F.SKU_NO,
        CASE
            WHEN F.IS_MEDICAL_EQUIPMENT = 0 THEN '否'
            WHEN F.IS_MEDICAL_EQUIPMENT = 1 THEN '是'
            ELSE '-'
        END AS IS_MEDICAL_EQUIPMENT,
        CASE
            WHEN F.AUDIT_STATUS = 0 THEN '待提交'
            WHEN F.AUDIT_STATUS = 1 THEN '审核中'
            WHEN F.AUDIT_STATUS = 2 THEN '审核通过'
            WHEN F.AUDIT_STATUS = 3 THEN '已驳回'
            ELSE '-'
        END AS AUDIT_STATUS,
        IF(F.MEDICAL_EQUIPMENT_TYPE = '','-',MEDICAL_EQUIPMENT_TYPE) AS MEDICAL_EQUIPMENT_TYPE,
        IF(F.MEDICAL_EQUIPMENT_USE = '','-',MEDICAL_EQUIPMENT_USE) AS MEDICAL_EQUIPMENT_USE,
        IF(F.MEDICAL_EQUIPMENT_LINE = '','-',MEDICAL_EQUIPMENT_LINE) AS MEDICAL_EQUIPMENT_LINE,
        IF(F.IS_PUSH = 1,'是', '否') AS IS_PUSH,

        CASE
            WHEN F.IS_INSTALLATION = 0 THEN '否'
            WHEN F.IS_INSTALLATION = 1 THEN '是'
            ELSE '-'
        END AS IS_INSTALLATION,

        IF(F.IS_PUSH = 1,DATE_FORMAT(F.PUSH_TIME,'%Y-%m-%d %H:%i:%s'),'-') AS PUSH_TIME,
        DATE_FORMAT(F.ADD_TIME,'%Y-%m-%d %H:%i:%s') AS ADD_TIME,
        DATE_FORMAT(F.MOD_TIME,'%Y-%m-%d %H:%i:%s') AS MOD_TIME,
        DATE_FORMAT(F.SKU_ADD_TIME,'%Y-%m-%d %H:%i:%s') AS SKU_ADD_TIME,
        K.SHOW_NAME,
        T.UNIT_NAME,
        S1.TITLE,
        P.ASSIGNMENT_MANAGER_ID,
        U.USERNAME
    FROM
        T_GOODS_FINANCE F
    LEFT JOIN
        V_CORE_SKU K
            ON F.SKU_ID = K.SKU_ID
    LEFT JOIN
        T_UNIT T
            ON K.BASE_UNIT_ID = T.UNIT_ID
    LEFT JOIN
        V_CORE_SPU P
            ON K.SPU_ID = P.SPU_ID
    LEFT JOIN
        T_SYS_OPTION_DEFINITION S1
            ON P.SPU_TYPE = S1.SYS_OPTION_DEFINITION_ID
    LEFT JOIN
        T_USER U
            ON P.ASSIGNMENT_MANAGER_ID = U.USER_ID
    WHERE
        F.IS_DELETE = 0");
return search(sql);</pre></td>
        </tr>
        <tr>
            <td> count:</td>
            <td colspan="100"><pre id="count" class="layui-code"></pre></td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot"></div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>