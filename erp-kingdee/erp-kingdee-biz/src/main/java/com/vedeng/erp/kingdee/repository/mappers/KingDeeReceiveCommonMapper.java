package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveCommonEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface KingDeeReceiveCommonMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeReceiveCommonEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeReceiveCommonEntity record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    KingDeeReceiveCommonEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeReceiveCommonEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeReceiveCommonEntity record);

    int updateBatchSelective(List<KingDeeReceiveCommonEntity> list);

    int batchInsert(@Param("list") List<KingDeeReceiveCommonEntity> list);

    /**
     * 根据发票id查询已推送过的 标准应收单信息
     *
     * @param invoiceId invoiceId
     * @return KingDeeReceiveCommonEntity
     */
    KingDeeReceiveCommonEntity selectByInvoiceId(@Param("invoiceId") String invoiceId);
}