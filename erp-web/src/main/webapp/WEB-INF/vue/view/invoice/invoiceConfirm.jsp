<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<!-- 引入样式 -->
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/element-ui/css/index.css">
<!-- 引入脚本 -->
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/vue.js"></script>
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/ele-index.js"></script>

<div id="app">
    <p class="p-class">申请开票需要满足以下条件：</p>

    <p class="p-class">1  &ensp;&ensp;回传合同审核状态为审核通过;</p>

    <p class="warn-class" >{{contractVerifyInfo}}</p>

    <p class="p-class">2 &ensp;&ensp;确认单审核状态为审核通过;</p>

    <p class="warn-class" v-if="secondWarn">确认单非审核通过。</p>

    <p class="warn-class" v-else>确认单已审核通过。</p>

    <p style="display: block;text-align: left;padding-top: 40px;padding-left: 34px">
        <el-button type="primary" @click="selectInvoiceItems" :disabled="invoiceConfirm">继续申请开票</el-button>
        <el-button @click="reInitCommun()">关 闭</el-button>
    </p>
    <span class="warn-msg" v-show="invoiceConfirm">
        该功能仅对部分职位开放权限，请联系上级进行咨询。
    </span>
</div>

<script type="text/javascript">
    const sendThis0 = (_this) => {
        vm0 = _this;
    };
    let url3 = '/finance/invoice/selectInvoiceItems.do?relatedId=${relatedId}&comment=${comment}&isAuto=${isAuto}&invoiceType=${invoiceType}&isAdvance=0&editFlag=true'
    const vm = new Vue({
        el: '#app',
        data() {
            return {
                firstWarn: !${firstWarn},
                secondWarn: !${secondWarn},
                invoiceConfirm: !${invoiceConfirm},
                contractVerifyInfo: '${contractVerifyInfo}'
            }},
        methods: {
            selectInvoiceItems() {
                axios({
                    url: this.url3,
                    method: 'get',
                })
                parent.layer.closeAll()
                parent.layer.open({
                    title: '申请开票',
                    type: 2,
                    shadeClose: false,
                    area: ['80%','720px'],
                    content: encodeURI(url3),
                });
            },

            reInitCommun() {
                layer.closeAll();
                parent.layer.closeAll();
            },

        }
    });
</script>

<style>
    .warn-msg {
        font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
        font-size: 14px;
        color: #909399;
        padding-top: 20px;
        padding-left: 34px;
    }
    .warn-class {
        font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
        font-size: 14px;
        color: #E6A23C;
        padding-left: 34px;
    }
    .p-class {
        padding-top: 20px;
        padding-left: 15px;
        font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
        font-size: 14px;
    }
    .el-row {
        margin-bottom: 10px;
    }

    .el-descriptions {
        background-color: #e5f1ff;
        margin-bottom: 20px;
    }

    .el-descriptions__title {
        margin-left: 10px;
        margin-top: 10px;
    }

    .el-descriptions__header {
        margin-bottom: 10px;
    }

    .title-style {
        background-color: #e5f1ff;
        box-sizing: border-box;
        padding-left: 10px;
        font-size: 16px;
        font-weight: 700;
        color: #303133;
    }

    .el-descriptions-item__cell.el-descriptions-item__label.is-bordered-label {
        text-align: center;
        width: 10%;
    }

    .cell {
        text-align: center;
    }

    .el-descriptions-item__cell.el-descriptions-item__content {
        text-align: center !important;
    }

    .my-content {
        width: 40%;
    }

    .el-tag{
        white-space: normal;
        height:auto;
    }


</style>