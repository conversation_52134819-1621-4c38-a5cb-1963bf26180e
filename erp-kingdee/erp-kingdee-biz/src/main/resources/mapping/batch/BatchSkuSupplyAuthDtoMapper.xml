<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchSkuSupplyAuthDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchSkuSupplyAuthDto">
    <!--@mbg.generated-->
    <!--@Table T_SKU_SUPPLY_AUTH-->
    <id column="SKU_SUPPLY_AUTH_ID" jdbcType="INTEGER" property="skuSupplyAuthId" />
    <result column="TRADER_SUPPLY_ID" jdbcType="INTEGER" property="traderSupplyId" />
    <result column="BRAND_IDS" jdbcType="VARCHAR" property="brandIds" />
    <result column="VALID_START_TIME" jdbcType="DATE" property="validStartTime" />
    <result column="VALID_END_TIME" jdbcType="DATE" property="validEndTime" />
    <result column="AUTH_TYPE" jdbcType="INTEGER" property="authType" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>

<!--auto generated by MybatisCodeHelper on 2022-11-30-->
  <select id="findByTraderSupplyIdAndValidStartTimeBeforeAndValidEndTimeAfter" resultType="java.lang.String">
    select
    case when TSSA.AUTH_TYPE = 1 then '独家代理合作模式'
    when TSSA.AUTH_TYPE = 2 then '授权经销合作模式'
    when TSSA.AUTH_TYPE = 3 then '零星采购模式'
    when TSSA.AUTH_TYPE = 4 then '授权经销产品'
    when TSSA.AUTH_TYPE = 5 then '非授权经销产品' end AS TYPE
    from T_SKU_SUPPLY_AUTH TSSA left join T_SKU_SUPPLY_AUTH_DETAIL TSSAD on TSSAD.SKU_SUPPLY_AUTH_ID = TSSA.SKU_SUPPLY_AUTH_ID
    where TSSA.TRADER_SUPPLY_ID=#{traderSupplyId,jdbcType=INTEGER} and TSSA.VALID_START_TIME <![CDATA[<=]]>
    FROM_UNIXTIME(#{time}/1000,'%Y-%m-%d') and TSSA.VALID_END_TIME <![CDATA[>=]]> FROM_UNIXTIME(#{time}/1000,'%Y-%m-%d')
    and TSSA.IS_DELETE = 0
    and TSSAD.IS_DELETE = 0
    and TSSAD.SKU_NO = #{sku}
    limit 1
  </select>
</mapper>