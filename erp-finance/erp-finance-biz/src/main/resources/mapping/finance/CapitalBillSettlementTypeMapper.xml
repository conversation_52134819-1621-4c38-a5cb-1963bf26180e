<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.CapitalBillSettlementTypeMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.CapitalBillSettlementTypeEntity">
    <!--@mbg.generated-->
    <!--@Table T_CAPITAL_BILL_SETTLEMENT_TYPE-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="CAPITAL_BILL_ID" jdbcType="INTEGER" property="capitalBillId" />
    <result column="TYPE" jdbcType="INTEGER" property="type" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CAPITAL_BILL_ID, `TYPE`, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER,
    UPDATER_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_CAPITAL_BILL_SETTLEMENT_TYPE
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_CAPITAL_BILL_SETTLEMENT_TYPE
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.finance.domain.entity.CapitalBillSettlementTypeEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CAPITAL_BILL_SETTLEMENT_TYPE (CAPITAL_BILL_ID, `TYPE`, ADD_TIME,
    MOD_TIME, CREATOR, CREATOR_NAME,
    UPDATER, UPDATER_NAME)
    values (#{capitalBillId,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
    #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR},
    #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.finance.domain.entity.CapitalBillSettlementTypeEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CAPITAL_BILL_SETTLEMENT_TYPE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="capitalBillId != null">
        CAPITAL_BILL_ID,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="capitalBillId != null">
        #{capitalBillId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.CapitalBillSettlementTypeEntity">
    <!--@mbg.generated-->
    update T_CAPITAL_BILL_SETTLEMENT_TYPE
    <set>
      <if test="capitalBillId != null">
        CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.CapitalBillSettlementTypeEntity">
    <!--@mbg.generated-->
    update T_CAPITAL_BILL_SETTLEMENT_TYPE
    set CAPITAL_BILL_ID = #{capitalBillId,jdbcType=INTEGER},
    `TYPE` = #{type,jdbcType=INTEGER},
    ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
    MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    CREATOR = #{creator,jdbcType=INTEGER},
    CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
    UPDATER = #{updater,jdbcType=INTEGER},
    UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
</mapper>