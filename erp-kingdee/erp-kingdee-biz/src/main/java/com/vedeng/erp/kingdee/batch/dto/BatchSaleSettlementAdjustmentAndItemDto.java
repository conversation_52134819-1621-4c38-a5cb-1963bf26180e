package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.dto.KingDeeSaleSettlementAdjustmentDto;
import lombok.*;

import java.util.List;

/**
 * @description: 销售调整单及调整单明细
 * @author: <PERSON>qin
 * @date: 2023/2/22 13:22
 **/
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BatchSaleSettlementAdjustmentAndItemDto {
    /**
     * 调整单
     */
    BatchSaleSettlementAdjustmentDto batchSaleSettlementAdjustmentDto;

    /**
     * 调整单明细
     */
    List<BatchSaleSettlementAdjustmentItemDto> BatchSaleSettlementAdjustmentItemDtoList;

    /**
     * 推送金蝶调整单
     */
    KingDeeSaleSettlementAdjustmentDto kingDeeSaleSettlementAdjustmentDto;
}
