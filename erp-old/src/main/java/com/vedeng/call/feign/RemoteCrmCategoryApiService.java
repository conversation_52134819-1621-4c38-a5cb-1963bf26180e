package com.vedeng.call.feign;

import com.vedeng.common.core.base.R;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import feign.Headers;
import feign.RequestLine;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: crm关键词分类匹配接口
 * @date 2024/9/30 10:51
 */
@FeignApi(serverName = ServerConstants.CRM_SERVER)
public interface RemoteCrmCategoryApiService {


    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /crm/category/match")
    R<CategoryMatchDto> matchCategories(@RequestBody String input);


    @Data
    public static class CategoryMatchDto {

        private List<String> keywords;

        private List<CategoryMatch> matchedCategories;


        @Data
        public static class CategoryMatch {
            private String category;
            private Integer subCategoryId;

        }
    }
}
