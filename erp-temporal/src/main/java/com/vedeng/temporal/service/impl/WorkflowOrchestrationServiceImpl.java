package com.vedeng.temporal.service.impl;

import com.vedeng.temporal.constants.BusinessConstants;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.service.WorkflowOrchestrationService;
import com.vedeng.temporal.workflow.MultiCompanyBusinessWorkflow;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.time.Duration;

/**
 * 工作流编排服务实现类
 * 专门负责Temporal工作流的启动、管理和控制
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-03
 */
@Service
@Slf4j
public class WorkflowOrchestrationServiceImpl implements WorkflowOrchestrationService {

    @Autowired
    private WorkflowClient workflowClient;

    @Value("${temporal.taskQueue.multiCompany:" + BusinessConstants.WorkflowConstants.DEFAULT_TASK_QUEUE + "}")
    private String taskQueue;

    @Override
    public CompanyBusinessResponse startMultiCompanyWorkflow(CompanyBusinessRequest request) {
        try {
            log.info(BusinessConstants.LogConstants.WORKFLOW_START, request.getBusinessId());

            // 生成工作流ID（移除UUID确保幂等性，增加flowOrderNo提高辨识度）
            String flowOrderNo = extractFlowOrderNo(request.getBusinessData());
            String workflowId = BusinessConstants.WorkflowConstants.WORKFLOW_ID_PREFIX +
                    request.getBusinessId() + "-" + flowOrderNo;

            // 配置工作流选项
            WorkflowOptions options = WorkflowOptions.newBuilder()
                    .setWorkflowId(workflowId)
                    .setTaskQueue(taskQueue)
                    .setWorkflowExecutionTimeout(Duration.ofHours(BusinessConstants.WorkflowConstants.DEFAULT_WORKFLOW_TIMEOUT_HOURS))
                    .setWorkflowTaskTimeout(Duration.ofMinutes(BusinessConstants.WorkflowConstants.DEFAULT_WORKFLOW_TASK_TIMEOUT_MINUTES))
                    .build();

            // 创建工作流存根
            MultiCompanyBusinessWorkflow workflow = workflowClient.newWorkflowStub(
                    MultiCompanyBusinessWorkflow.class, options);

            // 异步启动工作流
            try {
                WorkflowClient.start(workflow::executeMultiCompanyBusiness, request);
                log.info(BusinessConstants.LogConstants.WORKFLOW_START_SUCCESS, workflowId);
                
                return CompanyBusinessResponse.builder()
                        .success(true)
                        .message("工作流启动成功")
                        .generatedDocumentId(workflowId)
                        .processTimestamp(System.currentTimeMillis())
                        .build();
                        
            } catch (RuntimeException e) {
                // 检查是否为工作流已存在的异常
                if (e.getMessage() != null && e.getMessage().contains("already exists")) {
                    // 工作流已存在，这是正常情况，不是错误
                    log.info("业务ID {} 的工作流已在运行中，跳过重复启动: {}", request.getBusinessId(), workflowId);
                    return CompanyBusinessResponse.builder()
                            .success(true)
                            .message("工作流已在运行中")
                            .generatedDocumentId(workflowId)
                            .processTimestamp(System.currentTimeMillis())
                            .build();
                } else {
                    // 其他运行时异常重新抛出
                    throw e;
                }
            }

        } catch (Exception e) {
            log.error("启动多公司业务工作流异常", e);
            return CompanyBusinessResponse.failure("启动工作流异常",
                    BusinessConstants.ErrorCode.WORKFLOW_START_ERROR, e);
        }
    }

    /**
     * 从业务数据中解析流转单编号
     * @param businessData 业务数据JSON字符串
     * @return 流转单编号，解析失败时返回"UNKNOWN"
     */
    private String extractFlowOrderNo(String businessData) {
        try {
            if (businessData == null || businessData.trim().isEmpty()) {
                log.warn("业务数据为空，使用默认flowOrderNo");
                return "UNKNOWN";
            }
            
            JSONObject jsonObject = JSON.parseObject(businessData);
            String flowOrderNo = jsonObject.getString("flowOrderNo");
            
            if (flowOrderNo == null || flowOrderNo.trim().isEmpty()) {
                log.warn("业务数据中未找到flowOrderNo，使用默认值");
                return "UNKNOWN";
            }
            
            return flowOrderNo.trim();
            
        } catch (Exception e) {
            log.warn("解析flowOrderNo失败，使用默认值: {}", e.getMessage());
            return "UNKNOWN";
        }
    }
}
