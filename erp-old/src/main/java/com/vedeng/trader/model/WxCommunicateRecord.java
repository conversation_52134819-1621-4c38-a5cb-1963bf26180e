package com.vedeng.trader.model;

import com.vedeng.wechat.api.dto.WxCustomerFollowUserDto;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class WxCommunicateRecord extends CommunicateRecord {
    private String jobNumber;//员工的工号

    private String externalUserId;//微信的externalUserId

    private String talkId;//聊天记录的ID，用来判断重复性检查

    private String avatarUrl;//客户的头像

    private Integer talkType;//3 呼出 4呼入

    private WxCustomerFollowUserDto wxCustomerFollowUserDto;

    //生成getter和setter方法
    public String getJobNumber() {
        return jobNumber;
    }
    public void setJobNumber(String jobNumber) {
        this.jobNumber = jobNumber;

    }

    public String getExternalUserId() {
        return externalUserId;
    }
    public void setExternalUserId(String externalUserId) {
        this.externalUserId = externalUserId;
    }


    public String getTalkId() {
        return talkId;
    }
    public void setTalkId(String talkId) {
        this.talkId = talkId;
    }


    public String getAvatarUrl() {
        return avatarUrl;
    }
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public Integer getTalkType() {
        return talkType;
    }
    public void setTalkType(Integer talkType) {
        this.talkType = talkType;
    }

    public WxCustomerFollowUserDto getWxCustomerFollowUserDto() {
        return wxCustomerFollowUserDto;
    }
    public void setWxCustomerFollowUserDto(WxCustomerFollowUserDto wxCustomerFollowUserDto) {
        this.wxCustomerFollowUserDto = wxCustomerFollowUserDto;
    }


}
