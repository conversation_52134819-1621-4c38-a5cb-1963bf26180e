$(function(){
	//联系人下拉搜索初始化
	$(".contact").select2();
	// 联系地址下拉搜索初始化
	$(".address").select2();
});
function search() {
	checkLogin();
	if($("#searchTraderName").val()==undefined || $("#searchTraderName").val()==""){
		warnTips("searchError","查询条件不允许为空");//文本框ID和提示用语
		return false;
	}

	$("#search").submit();
}

function reSearch(){
	$("#searchTraderName").val("")
	$("#desc_div").hide();
	$("#updateTerminalInfo").show();
}

function addSubmit(){
	checkLogin();
	//科研购不需要校验联系人及地址, 已移除
	var belongPlatform = $("#belongPlatform").val();
	if(belongPlatform == 6){
		//集采订单无需校验地址
		if($("#trader_contact_1").val() ==undefined || $("#trader_contact_1").val().length == 0 || $("#trader_contact_1").val() == '0'){
			warnTips("traderContactIdMsg","未选择联系人");
			return false;
		}else {
			delWarnTips("traderContactIdMsg");
		}
	}

	var $form = $("#addForm");
	$("form :input").parents('li').find('.warning').remove();

	// if ($("select[name='traderContactId']").val() == 0) {
	// 	warnTips("traderContactIdMsg","联系人不允许为空");
	// 	return false;
	// }
	// if ($("select[name='traderAddressId']").val() == 0) {
	// 	warnTips("traderAddressIdMsg","联系地址不允许为空");
	// 	return false;
	// }
	var traderContactIdText = $("select[name='traderContactId']").find("option:selected").text();
	var traderContactIdTextArr = traderContactIdText.split('/');
	$("input[name='traderContactName']").val(traderContactIdTextArr[0]);
	$("input[name='traderContactTelephone']").val(traderContactIdTextArr[1]);
	$("input[name='traderContactMobile']").val(traderContactIdTextArr[2]);
	var traderAddressIdText = $("select[name='traderAddressId']").find("option:selected").text();
	var traderAddressIdTextArr = traderAddressIdText.split('/');
	$("input[name='traderArea']").val(traderAddressIdTextArr[0]);
	$("input[name='traderAddress']").val(traderAddressIdTextArr[1]);
	$form.submit();
	//return false;
}

function selectCustomer(indexId,traderId,traderName,traderCustomerId,customerType,customerNature, belongPlatForm,traderType){
	checkLogin();
	if ($("#customer_type_nature_div").length > 0) {
		var customerTypeStr = '';
		if (customerType == '426') {
			customerTypeStr = '科研医疗';
		} else if (customerType == '427') {
			customerTypeStr = '临床医疗';
		}
		
		var customerNatureStr = '';
		if (customerNature == '465') {
			customerNatureStr = '分销';
		} else if (customerNature == '466') {
			customerNatureStr = '终端';
		}
		// if (customerTypeStr == '' || customerNatureStr == '') {
		// 	layer.alert("客户类型和客户性质不能为空");
		// 	return false;
		// }
		if(belongPlatForm == 6){
			//集采订单展示总院分院
			if(traderType == 0){
				customerNatureStr = customerNatureStr + ' ' + '集团 总部';
			}else {
				customerNatureStr = customerNatureStr + ' ' + '集团 分院';
			}
		}
		$("#customer_type_nature_div").html(customerTypeStr + ' ' + customerNatureStr);
	}
	$("#trader_name_span_" + indexId).val(traderName);
	$("#trader_id_" + indexId).val(traderId);
	$("#trader_name_" + indexId).val(traderName);
	// $("#add_contact_" + indexId, window.parent.document).attr("layerParams", '{"width":"430px","height":"220px","title":"添加联系人","link":"/order/jc/addContact.do?traderId='+traderId+'&traderCustomerId='+traderCustomerId+'"}');
	// $("#add_address_" + indexId, window.parent.document).attr("layerParams",'{"width":"600px","height":"200px","title":"添加地址","link":"/order/jc/addAddress.do?traderId='+traderId+'"}');
	if ($("#desc_div").length > 0) {
		$("#desc_div").show();
		$("#updateTerminalInfo").hide();
		$("#customer_type_" + indexId).val(customerType);
		$("#customer_nature_" + indexId).val(customerNature);
	}

	if ($("#desc_div").length > 0) {
		$("#desc_div").show();
		$("#updateTerminalInfo").hide();
		$("#customer_type_").val(customerType);
		$("#customer_nature_").val(customerNature);
	}
	debugger;
	if(belongPlatForm == 6){
		//根据客户ID获取联系人列表&地址列表
		$.ajax({
			async:false,
			url: page_url + '/order/jc/getCustomerContactAndAddress.do',
			data:{"traderId":traderId},
			type:"POST",
			dataType : "json",
			success:function(data){
				if (data.code !== 0) {
					console.log("获取客户下联系人列表信息失败，message: " + data.message);
					return
				}

				var contactStr = '<option value="0">请选择联系人</option>';
				for(var i = 0; i< data.param.contactList.length; i++) {
					var isSelected = data.param.contactList[i].isDefault == 1 ? 'selected = "selected"' : '';
					contactStr += '<option value="' + data.param.contactList[i].traderContactId + '" ' + isSelected + '>' + data.param.contactList[i].name + '/' + data.param.contactList[i].telephone + '/' + data.param.contactList[i].mobile + '</option>';
				}
				$("#trader_contact_" + indexId).html(contactStr);
				$("#trader_contact_" + indexId).trigger('change');
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}else {
		//根据客户ID获取联系人列表&地址列表
		$.ajax({
			async:false,
			url: page_url + '/orderstream/saleorder/getCustomerContactAndAddress.do',
			data:{"traderId":traderId},
			type:"POST",
			dataType : "json",
			success:function(data){
				if (data.code !== 0) {
					console.log("获取客户下联系人列表信息失败，message: " + data.message);
					return
				}

				var contactStr = '<option value="0">请选择联系人</option>';
				for(var i = 0; i< data.param.contactList.length; i++) {
					var isSelected = data.param.contactList[i].isDefault == 1 ? 'selected = "selected"' : '';
					contactStr += '<option value="' + data.param.contactList[i].traderContactId + '" ' + isSelected + '>' + data.param.contactList[i].name + '/' + data.param.contactList[i].telephone + '/' + data.param.contactList[i].mobile + '</option>';
				}
				$("#trader_contact_" + indexId).html(contactStr);
				$("#trader_contact_" + indexId).trigger('change');

				var addressStr = '<option value="0">请选择联系地址</option>';
				for(var i = 0; i< data.param.addressList.length; i++) {
					var isSelected = data.param.addressList[i].traderAddress.isDefault == 1 ? 'selected = "selected"' : '';
					addressStr += '<option value="' + data.param.addressList[i].traderAddress.traderAddressId + '" ' + isSelected + '>' + data.param.addressList[i].area + '/' + data.param.addressList[i].traderAddress.address + '</option>';
				}
				$("#address_" + indexId).html(addressStr);
				$("#address_" + indexId).trigger('change');
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}

	//根据客户信息判断创建订单为何种类型start
	judgeOrderType(belongPlatForm);
	//根据客户信息判断创建订单为何种类型end
	if(parent.layer!=undefined){
		parent.layer.close(index);
	}
}
//订单流升级--判断订单类型方法
function judgeOrderType(belongPlatForm){
	if(belongPlatForm == 6){
		//集采客户--ordertype=8集采线下订单
		$("#addContactPart").hide();
		$("#traderAddressInfo").hide();
	}else if(belongPlatForm == 2){
		//医械购客户--ordertype=9线下直销订单
		$("#positionInfo").hide();
		$("#rangeInfo").hide();
	}else{
		//VS订单
		$("#positionInfo").hide();
		$("#rangeInfo").hide();
	}
	$("#belongPlatform").val(belongPlatForm);
}

function addContact(){
	checkLogin();
	var traderId = $("#trader_id_1").val();
	var indexId = $("#indexId_1").val();
	var customerNature = $("#customer_nature_1").val();
	var open = layer.open({
		type: 2,
		title: '新增联系人',
		shadeClose: false,
		area : ['800px', '620px'],
		content: "/orderstream/saleorder/addContact.do?traderId="+traderId + "&indexId=" + indexId + "&customerNature=" + customerNature,

		success: function(layero, index){

		}
	});
}

function addAddress(){
	checkLogin();
	var traderId = $("#trader_id_1").val()
	var indexId = $("#indexId_1").val()
	var open = layer.open({
		type: 2,
		title: '新增地址',
		shadeClose: false,
		area : ['800px', '600px'],
		content: "/orderstream/saleorder/addAddress.do?traderId="+traderId + "&indexId=" + indexId,
		success: function(layero, index){

		}
	});
}

function switchInput(){
	checkLogin();
	$(".searchfunc").hide();
	$("#selectTerminalInfo").hide();
	$("#InputTerminalInfo").show();
}

function switchSelect(){
	checkLogin();
	$("#selectTerminalInfo").show();
	$(".searchfunc").show();
	$("#InputTerminalInfo").hide();
}
$(function(){
	var checkType = function(){
		if($('.J-type:checked').val() == '1'){
			$('.J-block1').show();
			$('.J-block2').hide();
		}else{
			$('.J-block2').show();
			$('.J-block1').hide();
		}
	}
	
	checkType();
	
	$('.J-type').change(function(){
		checkType();
	})
})




function selectlendOut(traderId, traderName,traderType,callbackFuntion){

	window.parent && window.parent.hanlderSelect && window.parent.hanlderSelect(traderId, traderName,traderType);

	debugger;
	//如果有回调函数就调用
	if(callbackFuntion != null && callbackFuntion != ''){
		eval("window.parent."+callbackFuntion+"('"+traderId+"','"+traderName+"','"+traderType+"')");
	}

	if(parent.layer!=undefined){
		parent.layer.close(index);
	}
}

$(function(){
	if($('#block_2').val()=='2') $('#radio_2').attr("checked",true);

	$('.J-search-btn').click(function(){
		if(!$.trim($(".J-searchTraderName").val())){
			$('.J-error').show();//文本框ID和提示用语
			return false;
		}
		
		var searchURL = '';
		
		if($('.J-type:checked').val() == 1){
			searchURL = '/trader/customer/searchCustomerList.do';
		}else{
			searchURL = '/order/buyorder/getSupplierByName.do';
		}

		$("#search").attr("action",page_url + searchURL);
		$("#search").submit();

		/*var link = page_url + searchURL + '?&searchTraderName=' + $('.J-searchTraderName').val() + '&lendOut=1&callbackFuntion='+$("#callbackFuntion").val();
		window.location.href = link;*/
//		$.ajax({
//			async:false,
//			url: page_url + '/trader/customer/searchCustomerList.do',
//			data:{
//				"searchTraderName":$('.J-searchTraderName').val(),
//				"lendOut":1
//			},
//			type:"POST",
//			dataType : "json",
//			success:function(data){
//				
//			},
//			error:function(data){
//				if(data.status ==1001){
//					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
//				}
//			}
//		});
	})
	
	$('.J-searchTraderName').on('keyup', function(e){
		if(e.keyCode == 13||e.keyCode == 108){
			e.preventDefault();
			$('.J-search-btn').click();
			return false;
		}
	})
	
	$('.J-add-search_customer-search').submit(function(e){
		e.preventDefault();
		return false;
	})
})

function triggerContactInfoChanged() {
	var belongPlatForm = $("#belongPlatform").val();
	if(belongPlatForm == 6) {
		//集采线下客户展示
		var contactId = $('#contact_1').find('option:selected').val();
		if(contactId==undefined||contactId==""||contactId==null){
			contactId = $('#trader_contact_1').find('option:selected').val();
		}
		if (contactId == 0 || contactId == '') {
			$("#contact_position_1").html('');
			$("#allowed_goods_types_1").html('');
			return
		}

		//根据客户ID获取联系人列表&地址列表
		$.ajax({
			url: page_url + '/order/jc/getContactInfo.do',
			data: {"contactId": contactId},
			type: "get",
			dataType: "json",
			success: function (data) {
				if (data.code !== 0 && data.data !== null) {
					console.log("获取客户下联系人列表信息失败，message: " + data.message);
					return
				}
				//处理集采客户 -- 需显示集团标示信息
				if (data.data.groupLevel == 1) {
					$('.group-label.parent').show();
				} else {
					$('.group-label.child').show();
				}
				//处理集采客户
				$("#contact_position_1").html(data.data.positions);
				$("#allowed_goods_types_1").html(data.data.allowedGoodsTypes);
			}
		});
	}
}





