package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.model.User;
import com.vedeng.flash.constant.FlashConstant;
import com.vedeng.flash.dao.EarlyWarningTaskMapper;
import com.vedeng.flash.model.EarlyWarningTask;
import com.vedeng.todolist.dto.EarlyWarningGoodsTaskToDoDto;
import com.vedeng.todolist.dto.SupplyChainStaffTodoDtoBuild;
import com.wms.service.context.ThreadLocalContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class EarlyWarningGoodsTaskStaffVisitor implements TodoVisitor<SupplyChainStaffTodoDtoBuild> {

    @Autowired
    private EarlyWarningTaskMapper earlyWarningTaskMapper;

    @Override
    public void visitor(SupplyChainStaffTodoDtoBuild todoDtoBuild) {
        User user = ThreadLocalContext.get("userInfo");
        List<Integer> userIdList = Collections.singletonList(user.getUserId());
        List<EarlyWarningTask> earlyWarningTaskList = earlyWarningTaskMapper.getEarlyWarningGoodsTaskListByuserIds(userIdList);
        EarlyWarningGoodsTaskToDoDto earlyWarningGoodsTaskToDoDto = new EarlyWarningGoodsTaskToDoDto();
        if (CollectionUtils.isNotEmpty(earlyWarningTaskList)) {
            long adventCount = earlyWarningTaskList.stream().filter(e -> FlashConstant.TASK_STATUS_ADVENT.equals(e.getTaskStatus())).count();
            long overdueCount = earlyWarningTaskList.stream().filter(e -> !(FlashConstant.TASK_STATUS_ADVENT.equals(e.getTaskStatus()) || FlashConstant.TASK_STATUS_INIT.equals(e.getTaskStatus()))).count();
            earlyWarningGoodsTaskToDoDto.setAdventExpeditingTaskNum((int) adventCount);
            earlyWarningGoodsTaskToDoDto.setOverdueExpeditingTaskNum((int) overdueCount);
            earlyWarningGoodsTaskToDoDto.setTotalExpeditingTaskNum((int) (adventCount + overdueCount));
        }
        todoDtoBuild.get().setEarlyWarningGoodsTaskToDoDto(earlyWarningGoodsTaskToDoDto);
    }

}
