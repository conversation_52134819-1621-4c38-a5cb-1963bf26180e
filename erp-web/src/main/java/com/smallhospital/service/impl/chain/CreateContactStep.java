package com.smallhospital.service.impl.chain;

import com.smallhospital.dto.ELOrderDto;
import com.vedeng.authorization.dao.RegionMapper;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.util.DateUtil;
import com.vedeng.trader.dao.TraderAddressMapper;
import com.vedeng.trader.dao.TraderContactGenerateMapper;
import com.vedeng.trader.model.TraderAddress;
import com.vedeng.trader.model.TraderContactGenerate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2020/12/15 19:35
 */
@Service
public class CreateContactStep extends AbstractCreateOrderStep{


    @Autowired
    private TraderAddressMapper traderAddressMapper;

    @Autowired
    private RegionMapper regionMapper;

    @Autowired
    private TraderContactGenerateMapper traderContactGenerateMapper;

    @Override
    protected void doDealWith(ELOrderDto orderDto) throws Exception {
        if (!StringUtils.isBlank(orderDto.getDeliveryAddressArea())){
            //校验收货地址是否存在
            String[] addressArr = orderDto.getDeliveryAddressArea().split(",");
            List<String> traderAddressIdStr = regionMapper.getRegionIdStringByMinRegionName(addressArr[2]);
            String areaStr = this.getArea(traderAddressIdStr);
            String areaId = areaStr.split(",")[2];
            vailTraderAddress(orderDto.getPurchaserId(),areaStr,areaId,orderDto.getDeliveryAddress());
        }
        if (!StringUtils.isBlank(orderDto.getInvoiceAddressArea())){
            //校验收票地址是否存在
            String[] addressStr = orderDto.getInvoiceAddressArea().split(",");
            List<String> areaIdStr = regionMapper.getRegionIdStringByMinRegionName(addressStr[2]);
            String areaInfo = this.getArea(areaIdStr);
            String id = areaInfo.split(",")[2];
            vailTraderAddress(orderDto.getPurchaserId(),areaInfo,id,orderDto.getInvoiceAddress());
        }
        //校验收货联系人是否存在
        vailTraderContact(orderDto.getPurchaserId(),orderDto.getDeliveryMan(),orderDto.getDeliveryPhone());
        //校验收票联系人是否存在
        vailTraderContact(orderDto.getPurchaserId(),orderDto.getInvoiceMan(),orderDto.getInvoicePhone());
    }

    private String getArea(List<String> addressIdStr){
        String areaStr = null;
        if (addressIdStr.size() == 1){
            areaStr = addressIdStr.get(0);
        }else if(addressIdStr.size() >= 2){
            areaStr = addressIdStr.get(1);
        }
        return areaStr;
    }

    private void vailTraderAddress(Integer traderId, String areaIdStr, String areaId, String address){
        TraderAddress traderAddress = traderAddressMapper.getAddressInfoByAddress(traderId, CommonConstants.TRADER_TYPE_1, areaIdStr,Integer.valueOf(areaId),address);
        if (traderAddress == null || traderAddress.getTraderAddressId() == null) {
            traderAddress = new TraderAddress();
            traderAddress.setTraderId(traderId);
            traderAddress.setTraderType(CommonConstants.TRADER_TYPE_1);
            traderAddress.setIsEnable(CommonConstants.ENABLE);
            traderAddress.setAreaId(Integer.valueOf(areaId));
            traderAddress.setAreaIds(areaIdStr);
            traderAddress.setAddress(address);
            traderAddress.setAddTime(DateUtil.gainNowDate());
            traderAddress.setModTime(DateUtil.gainNowDate());
            traderAddressMapper.insertSelective(traderAddress);
        }
    }

    private void vailTraderContact(Integer traderId, String name,String mobile){
        TraderContactGenerate contactInfo = traderContactGenerateMapper.getContactInfo(traderId, CommonConstants.TRADER_TYPE_1, name, null, mobile);
        if (contactInfo == null || contactInfo.getTraderContactId() == null) {
            contactInfo = new TraderContactGenerate();
            contactInfo.setTraderId(traderId);
            contactInfo.setTraderType(CommonConstants.TRADER_TYPE_1);
            contactInfo.setIsEnable(CommonConstants.ENABLE);
            contactInfo.setName(name);
            contactInfo.setMobile(mobile);
            contactInfo.setAddTime(DateUtil.gainNowDate());
            contactInfo.setModTime(DateUtil.gainNowDate());
            traderContactGenerateMapper.insertSelective(contactInfo);
        }
    }
}
