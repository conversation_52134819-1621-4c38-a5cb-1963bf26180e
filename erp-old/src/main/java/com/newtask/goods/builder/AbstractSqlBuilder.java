package com.newtask.goods.builder;

import com.newtask.goods.DBTableType;
import com.newtask.goods.SqlCommandType;

/**
 * <AUTHOR> [<EMAIL>]
 */
public abstract class AbstractSqlBuilder implements SQLStatementBuilder {

    private SqlCommandType sqlCommand;
    private DBTableType dbTable;

    public SqlCommandType getSqlCommand() {
        return sqlCommand;
    }

    public AbstractSqlBuilder setSqlCommand(SqlCommandType sqlCommand) {
        this.sqlCommand = sqlCommand;
        return this;
    }

    public DBTableType getDbTable() {
        return dbTable;
    }

    public AbstractSqlBuilder setDbTable(DBTableType dbTable) {
        this.dbTable = dbTable;
        return this;
    }

}
