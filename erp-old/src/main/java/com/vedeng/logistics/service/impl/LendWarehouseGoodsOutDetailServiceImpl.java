package com.vedeng.logistics.service.impl;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.model.LendOut;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutVo;
import com.wms.model.po.WmsOutputOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @description: 外借出库
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/16 15:33
 **/
@Service
@Slf4j
public class LendWarehouseGoodsOutDetailServiceImpl extends BaseWarehouseGoodsOutDetailService {

    /**
     * wms出库单类型-外借出库
     */
    private final Integer WMS_OUTPUT_ORDER_TYPE_LEND = 1;

    /**
     * 外借单详情页
     */
    private final String DETAIL_URL_LEND_OUT = "/wms/commodityLendOut/detail.do?lendOutId=";


    @Override
    public WarehouseGoodsOutVo detailWarehouseGoodsOut(String outInNo) {
        WarehouseGoodsOutVo warehouseGoodsOutVo = commonDetailWarehouseGoodsOut(outInNo, WarehouseGoodsOutEnum.LEND_WAREHOUSE_OUT.getErpCode());
        if(warehouseGoodsOutVo != null){
            String wmsLendOutOrderNo = warehouseGoodsOutVo.getRelateNo();
            if (StringUtils.isNotBlank(wmsLendOutOrderNo)) {
                WmsOutputOrder wmsLendOutOrder = wmsOutputOrderMapper.getWmsOutputOrderByOrderNoAndType(wmsLendOutOrderNo, WMS_OUTPUT_ORDER_TYPE_LEND);
                if(Objects.nonNull(wmsLendOutOrder)){
                    warehouseGoodsOutVo.setBelongUserName(wmsLendOutOrder.getCreator());
                    if(StringUtils.isNotBlank(wmsLendOutOrder.getCreator())){
                        User lendOutCreator = userMapper.getUserByUserName(wmsLendOutOrder.getCreator());
                        if(lendOutCreator != null && StringUtils.isNotBlank(lendOutCreator.getOrgName())){
                            warehouseGoodsOutVo.setBelongUserOrgName(lendOutCreator.getOrgName());
                        }
                    }
                }
            }
        }
        return warehouseGoodsOutVo;
    }

    @Override
    public List<WarehouseGoodsOutLogVo> relatedNoWarehouseGoodsOutLogList(String relatedNo) {
        List<WarehouseGoodsOutLogVo> warehouseGoodsOutLogVoList = warehouseGoodsOutInMapper.selectWarehouseGoodsOutLogListByRelatedNo(WarehouseGoodsOutEnum.LEND_WAREHOUSE_OUT.getErpCode(), relatedNo);
        return CollectionUtils.isNotEmpty(warehouseGoodsOutLogVoList) ? warehouseGoodsOutLogVoList : new ArrayList<>();
    }

    @Override
    public String relatedNoDetailRedirect(String relatedNo) {
        WmsOutputOrder wmsOutputOrder = wmsOutputOrderMapper.getWmsOutputOrderByOrderNoAndType(relatedNo, WMS_OUTPUT_ORDER_TYPE_LEND);
        String url = "";
        if (wmsOutputOrder != null) {
            url = REDIRECT_URL_PREFIX + DETAIL_URL_LEND_OUT + wmsOutputOrder.getId();
        }
        return url;
    }
}
