package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶费用应付单明细 dto  金蝶入参
 * @date
 */
@Getter
@Setter
public class KingDeePayExpensesCommand {


    /**
     * 单据内码
     */
    private String FID;
    /**
     * fBillNo
     */
    private String FBillNo;
    /**
     * 单据日期
     */
    private String FDATE;
    /**
     * 贝登单据头ID
     */
    private String F_QZOK_BDDJTID;
    /**
     * 是否价外税
     */
    private boolean FISPRICEEXCLUDETAX;
    /**
     * 业务类型
     */
    private String FBUSINESSTYPE;
    /**
     * 是否含税单价录入
     */
    private boolean FISTAX;
    /**
     * 立账类型
     */
    private String FSetAccountType;

    //FNumber对象
    /**
     * 单据类型
     */
    private KingDeeNumberCommand FBillTypeID = new KingDeeNumberCommand();
    /**
     * 供应商
     */
    private KingDeeNumberCommand FSUPPLIERID = new KingDeeNumberCommand();
    /**
     * 币别
     */
    private KingDeeNumberCommand FCURRENCYID = new KingDeeNumberCommand();
    /**
     * 结算组织
     */
    private KingDeeNumberCommand FSETTLEORGID = new KingDeeNumberCommand();
    /**
     * 付款组织
     */
    private KingDeeNumberCommand FPAYORGID = new KingDeeNumberCommand();

    /**
     * 内部属性
     */
    private List<KingDeePayExpensesEntityCommand> fEntityDetail;

    /**
     * 应付单明细
     */
    @Data
    public static class   KingDeePayExpensesEntityCommand{
        /**
         * 计价数量
         */
        private String FPriceQty;
        /**
         * 含税单价
         */
        private String FTaxPrice;
        /**
         * 税率%
         */
        private String FEntryTaxRate;
        /**
         * 是否计入采购成本
         */
        private String FINCLUDECOST;

        /**
         * 贝登单据行ID
         */
        private String F_QZOK_BDDJHID;
        /**
         * 对应贝登的sku
         */
        private KingDeeNumberCommand F_QZOK_WLBM = new KingDeeNumberCommand();

        //FNumber对象
        /**
         * 费用项目
         */
        private KingDeeNumberCommand FCOSTID = new KingDeeNumberCommand();

        /**
         * 费用承担部门
         */
        private KingDeeNumberCommand FCOSTDEPARTMENTID = new KingDeeNumberCommand();

        /**
         * 归属销售单号
         */
        private String F_QZOK_GSXSDH;
    }

}
