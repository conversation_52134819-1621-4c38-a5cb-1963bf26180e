package com.vedeng.erp.settlement.mapper;

import com.vedeng.erp.settlement.domain.entity.SettlementBillItemEntity;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface SettlementBillItemMapper {
    int deleteByPrimaryKey(Integer settleItemBillId);

    int insert(SettlementBillItemEntity record);

    int insertSelective(SettlementBillItemEntity record);

    SettlementBillItemEntity selectByPrimaryKey(Integer settleItemBillId);

    int updateByPrimaryKeySelective(SettlementBillItemEntity record);

    int updateByPrimaryKey(SettlementBillItemEntity record);

    int updateBatchSelective(List<SettlementBillItemEntity> list);

    int batchInsert(List<SettlementBillItemEntity> list);

    List<SettlementBillItemEntity> findBySettleBillId(@Param("settleBillId") Integer settleBillId);

    List<SettlementBillItemEntity> findByBusinessIdAndBusinessType(@Param("businessId") Integer businessId, @Param("businessType") String businessType);

    int deleteBySettleItemBillIdIn(@Param("settleItemBillIdCollection") Collection<Integer> settleItemBillIdCollection);
}