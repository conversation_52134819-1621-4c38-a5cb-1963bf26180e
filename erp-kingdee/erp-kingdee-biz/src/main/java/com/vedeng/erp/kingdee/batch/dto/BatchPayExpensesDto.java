package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.dto.InPutFeePlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.InPutFeeSpecialInvoiceDto;
import com.vedeng.erp.kingdee.dto.KingDeePayExpensesDto;
import lombok.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 应付单 发票 组合对象
 * @date 2022/12/6 10:34
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchPayExpensesDto {

    /**
     * 金蝶应付单
     */
    private KingDeePayExpensesDto kingDeePayExpensesDto;

    /**
     * 金蝶普票
     */
    private InPutFeePlainInvoiceDto inPutFeePlainInvoiceDto;

    /**
     * 金蝶专票
     */
    private InPutFeeSpecialInvoiceDto inPutFeeSpecialInvoiceDto;
}
