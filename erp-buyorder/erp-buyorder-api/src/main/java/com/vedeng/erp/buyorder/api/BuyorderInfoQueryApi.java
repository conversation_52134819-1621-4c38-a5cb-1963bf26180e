package com.vedeng.erp.buyorder.api;


import com.vedeng.erp.buyorder.dto.BuyOrderDto;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.buyorder.service
 * @Date 2022/1/11 17:47
 */
@RequestMapping("/buyorder")
public interface BuyorderInfoQueryApi {

    /**
     * 根据SKU_ID获取商品在途信息0
     */
    @RequestMapping(value = "/listGoodsOnWayInfo")
    List<BuyOrderDto> listGoodsOnwayInfo(Integer skuId);
}
