package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.GeAuthorizationVerify;

import java.util.List;

public interface GeAuthorizationVerifyMapper {
    int deleteByPrimaryKey(Integer authorizationVerifyId);

    int insert(GeAuthorizationVerify record);

    int insertSelective(GeAuthorizationVerify record);

    GeAuthorizationVerify selectByPrimaryKey(Integer authorizationVerifyId);

    int updateByPrimaryKeySelective(GeAuthorizationVerify record);

    int updateByPrimaryKey(GeAuthorizationVerify record);

    List<GeAuthorizationVerify> selectByAuthorizationId(Integer authorizationId);
}