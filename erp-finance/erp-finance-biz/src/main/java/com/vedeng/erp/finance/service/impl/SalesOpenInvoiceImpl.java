package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.eventbus.EventBus;
import com.vedeng.common.core.base.ResultInfo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.listenerEvent.TraderBillPeriodEvent;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceOpenResponseDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.service.*;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.JcWeChatApiService;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 销项开票
 */
@Slf4j
@Service
public class SalesOpenInvoiceImpl extends AbstractSalesOpenInvoice {

    @Autowired
    private InvoiceApplyService invoiceApplyService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private SaleOrderApiService saleOrderApiService;

    @Autowired
    private AfterSalesInvoiceService afterSalesInvoiceService;

    @Autowired
    private JcWeChatApiService jcWeChatApiService;

    @Autowired
    private InvoiceApiService invoiceApiService;

    @Autowired
    @Qualifier(value = "eventBusCenter")
    private EventBus eventBusCenter;


    @Autowired
    private TrackStrategyFactory trackStrategyFactory;

    /**
     * 初始化交付信息
     *
     * @param invoiceDto
     */
    @Override
    protected void initHandOverInfo(InvoiceDto invoiceDto) {
        Integer saleOrderId = invoiceDto.getRelatedId();
        // 获取收票人手机号
        String saleOrderInvoiceTraderContactTelephone = saleOrderApiService.getSaleOrderInvoiceTraderContactMobile(saleOrderId);
        invoiceDto.setInvoiceTraderContactMobile(saleOrderInvoiceTraderContactTelephone);
        // 联系人
        String traderContactName = saleOrderApiService.getSaleOrderInvoiceTraderContactName(saleOrderId);
        invoiceDto.setInvoiceTraderContactName(traderContactName);

        // 微信通知人
        Set<Integer> wx = new HashSet<>();
        InvoiceApplyDto invoiceApply = invoiceApplyService.getInvoiceApply(invoiceDto.getInvoiceApplyId());
        Integer creator = invoiceApply.getCreator();
        if (Objects.nonNull(creator)) {
            wx.add(creator);
        }
        SaleorderInfoDto bySaleOrderId = saleOrderApiService.getBySaleOrderId(saleOrderId);
        Integer userId = bySaleOrderId.getUserId();
        if (Objects.nonNull(userId)) {
            wx.add(userId);
        }
        ArrayList<Integer> integers = CollUtil.newArrayList(wx);
        invoiceDto.setWxAppUserId(integers);
        // 邮箱交付信息
        InvoiceDto invoiceByApply = invoiceApiService.getSaleOrderInvoiceByApply(invoiceDto.getInvoiceApplyId(), invoiceDto.getInvoiceNo());
        if (Objects.nonNull(invoiceByApply)) {
            invoiceDto.setEmail(invoiceByApply.getEmail());
            invoiceDto.setInvoiceTraderContactEmail(invoiceByApply.getInvoiceTraderContactEmail());
            invoiceDto.setOpenInvoiceTime(invoiceByApply.getOpenInvoiceTime());
            invoiceDto.setOrderNo(invoiceByApply.getOrderNo());
        }
    }

    /**
     * 开票成功后置业务处理
     *
     * @param invoiceApplyDto
     * @param saleInvoiceOpenResponseDto
     * @return
     */
    @Override
    @Transactional
    public InvoiceDto successOpenInvoice(InvoiceApplyDto invoiceApplyDto, SaleInvoiceOpenResponseDto saleInvoiceOpenResponseDto) {
        log.info("开票完成后续处理开始");
        // 开票申请审核成功
        invoiceApplyService.auditPassInvoiceApply(invoiceApplyDto.getInvoiceApplyId());

        // 操作类型 改为手动
        if (ErpConstant.INVOICE_CREATE_TYPE_MANUAL.equals(invoiceApplyDto.getCreateType())) {
            log.info("修改开票类型");
            invoiceApplyService.updateManualCreateType(invoiceApplyDto.getInvoiceApplyId());
        }

        // 新增销项票发票
        InvoiceDto invoice = super.assembleCreateInvoice(invoiceApplyDto, saleInvoiceOpenResponseDto);
        invoiceService.createInvoice(invoice);

        // 更新销售订单的发票状态
        saleOrderApiService.updateSaleOrderInvoiceStatus(invoice.getRelatedId());

        // 销售订单开票处理订单生成账期逾期编码
        TraderBillPeriodEvent traderBillPeriodEvent = TraderBillPeriodEvent
                .builder()
                .invoiceId(invoice.getInvoiceId())
                .saleOrderId(invoice.getRelatedId())
                .amount(invoice.getAmount())
                .build();
        eventBusCenter.post(traderBillPeriodEvent);

        // 售后单关联发票信息（售后退票）
        afterSalesInvoiceService.saveAfterSaleInvoice(invoice);

        // JC订单发送微信消息
        jcWeChatApiService.sendMsg(invoice.getRelatedId());
        log.info("开票完成后续处理结束");

        // 埋点
        addTrackApplyAuthorization(invoiceApplyDto.getErpOrderNo(),invoiceApplyDto.getTraderId());

        return invoice;
    }

    private void addTrackApplyAuthorization( String orderNo, Integer traderId) {
        EventTrackingEnum eventTrackingEnum = EventTrackingEnum.SALE_ORDER_INVOICE;
        try {
            TrackParamsData trackParamsData = new TrackParamsData();
            Map<String, Object> trackParams = new HashMap<>();
            trackParams.put("traderId",traderId);
            trackParams.put("orderNo", orderNo);
            TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(eventTrackingEnum);
            trackParamsData.setEventTrackingEnum(eventTrackingEnum);
            trackParamsData.setTrackParams(trackParams);
            trackParamsData.setTrackResult(ResultInfo.success());
            trackStrategy.track(trackParamsData);
        }catch(Exception e) {
            log.error("埋点：{}，失败，不影响正常业务",eventTrackingEnum.getArchivedName(),e);
        }
    }

}
