function openModel(url, name) {
    layer.open({
        title: name,
        type: 2,
        shade: 0.2,
        maxmin: true,
        shadeClose: true,
        area: ['90%', '90%'],
        content: url,
        moveOut: true
    });
}

function openModelParam(url, name,width,height) {
    layer.open({
        title: name,
        type: 2,
        shade: 0.2,
        maxmin: true,
        shadeClose: true,
        area: [width, height],
        content: url,
        moveOut: true
    });
}

/** 时间转换 **/
function parseTime(time, pattern) {
    if (arguments.length === 0 || !time) {
        return null
    }
    const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
    let date
    if (typeof time === 'object') {
        date = time
    } else {
        if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
            time = parseInt(time)
        } else if (typeof time === 'string') {
            time = time.replace(new RegExp(/-/gm), '/').replace('T', ' ').replace(new RegExp(/\.[\d]{3}/gm), '');
        }
        if ((typeof time === 'number') && (time.toString().length === 10)) {
            time = time * 1000
        }
        date = new Date(time)
    }
    const formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay()
    }
    const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
        let value = formatObj[key]
        // Note: getDay() returns 0 on Sunday
        if (key === 'a') {
            return ['日', '一', '二', '三', '四', '五', '六'][value]
        }
        if (result.length > 0 && value < 10) {
            value = '0' + value
        }
        return value || 0
    })
    return time_str
}

//用字符串替换占位符
function getReplaceStr(str, obj) {
    for (let key in obj) {
        str = str.replace(new RegExp('\\{\\{' + key + '\\}\\}', 'g'), obj[key])
    }
    return str
}

// 解决 vue 加载闪屏问题
function loadingApp(){
    document.getElementById("app").style.display = ''
}

//获取当月第一天的日期
function getNowMonthFirst(){
    let y = new Date().getFullYear(); //获取年份
    let m = new Date().getMonth(); //获取月份
    return new Date(y, m, 1);
}

//获取当月第一天的日期 23.59.59
function getNowMonthLastTime(){
    let y = new Date().getFullYear(); //获取年份
    let m = new Date().getMonth(); //获取月份
    return new Date(y, m, 1,23,59,59);
}

//获取当月最后一天的日期
function getNowMonthLast(){
    let y = new Date().getFullYear(); //获取年份
    let m = new Date().getMonth() + 1; //获取月份
    return new Date(y, m, 0,23,59,59);
}

//获取上几个月第一天的日期
function getLastMonthFirst(last){
    let y = new Date().getFullYear(); //获取年份
    let m = new Date().getMonth()-last; //获取月份
    let d = new Date().getDate(); //获取日
    return new Date(y, m, d); //获取当月最后一日
}

//获取今天最后时间
function getNowDateLastTime(){
    let y = new Date().getFullYear(); //获取年份
    let m = new Date().getMonth(); //获取月份
    let d = new Date().getDate(); //获取日
    return new Date(y, m, d,23,59,59);
}

//用字符串替换占位符
function fixed(str) {
    return Number(str).toFixed(2);
}