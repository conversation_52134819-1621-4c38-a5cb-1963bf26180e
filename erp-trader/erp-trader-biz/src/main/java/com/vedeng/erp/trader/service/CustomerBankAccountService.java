package com.vedeng.erp.trader.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.domain.dto.CustomerBankAccountDto;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface CustomerBankAccountService {
    
    PageInfo<CustomerBankAccountDto> list(PageParam<CustomerBankAccountDto> customerBankAccountDtoPageParam);

    void delete(CustomerBankAccountDto customerBankAccountDto);

    void update(CustomerBankAccountDto customerBankAccountDto);

    List<CustomerBankAccountDto> upload(MultipartFile file) throws Exception;
}
