package com.vedeng.goods.dao;

import com.vedeng.goods.model.entity.GoodsLevelDo;
import com.vedeng.goods.model.vo.GoodsLevelVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GoodsLevelMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(GoodsLevelDo record);

    GoodsLevelDo selectByPrimaryKey(Integer id);

    List<GoodsLevelDo> selectAll();

    int updateByPrimaryKey(GoodsLevelDo record);

    List<Integer> countGoodsByGoodsLevelNo(@Param("goodsLevelNo") Integer goodsLevelNo, @Param("todoTypeList") List<Integer> todoTypeList);

    List<Integer> countGoodsByPositionNo(@Param("goodsPositionNo") Integer goodsPositionNo, @Param("goodsLevelNoList")List<Integer> goodsLevelNoList);

    /**
     * 检索SKU商品分级信息
     *
     * @param skuId
     * @return
     */
    GoodsLevelVo getGoodsLevelVoBySkuId(Integer skuId);

    /**
     * 根据商品分级ID查询商品分级信息
     * @param id
     * @return 包含两个配置项的dto
     */
    GoodsLevelDo selectByPrimaryKeyWithBlob(Integer id);
}