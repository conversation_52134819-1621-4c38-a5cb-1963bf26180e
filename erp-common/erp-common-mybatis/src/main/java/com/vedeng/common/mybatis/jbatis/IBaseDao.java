package com.vedeng.common.mybatis.jbatis;

import java.io.Serializable;
import java.util.List;

/**
 * <p>Description: baseDao接口</p>
 *
 * T  实体类泛型
 * Pk 实体类主键类型
 * <AUTHOR>
 * @version V1.0
 */
public interface IBaseDao<T, PK extends Serializable> {


    /**
     * 插入单条信息
     * <p>注：插入实体类时，请保证信息完整，否则会异常推出</p>
     *
     * @param object object
     * @return long
     */
    PK create(T object);

    /**
     * 编辑单条信息
     * <p>注：编辑时保证主键和更改信息 不为空</p>
     *
     * @param object object
     * @return int
     */
    int update(T object);

    /**
     * 删除单条信息
     * <p>注：object为空 将异常退出，这样做是为了保证 不会在误操作的情况下 删除全表</p>
     *
     * @param object object
     * @return int
     */
    int remove(T object);

    /**
     * 统计信息条数
     * <p>注：分表是分库统计加和</p>
     *
     * @param object object
     * @return int
     */
    int count(T object);

    /**
     * 批量添加多条信息
     * <p>注：list不能为空，且bean内信息完整</p>
     *
     * @param list object
     * @return boolean
     */
    boolean createOfBatch(List<T> list);


    /**
     * 批量更新多条信息
     * <p>注：因为没使用常规update更新，批量更新时请确定bean存在且内信息完整</p>
     *
     * @param list object
     * @return int
     */
    int updateOfBatch(List<T> list);

    /**
     * 批量删除多条信息
     * <p>注：list为空 将异常退出，这样做是为了保证 不会在误操作的情况下 删除全表</p>
     *
     * @param list object
     * @return int
     */
    int removeOfBatch(List<T> list);

    /**
     * 获取多条信息
     * <p>注：T为空时将返回表内所有数据</p>
     *
     * @param object object
     * @return list
     */
    List<T> queryByObject(T object);

    /**
     * 批量获取多条信息
     * <p>注：T为空时将返回表内所有数据</p>
     *
     * @param list object
     * @return list
     */
    List<T> queryByList(List<T> list);

    /**
     * <p>Description:带参数的查询 </p>
     *
     * @param object  实体类
     * @param orderby 排序  username desc
     * @param offset  limit
     * @param limit   limit
     * @return ist<T>
     */
    List<T> queryListByCriteria(T object, String orderby, Integer offset, Integer limit);

    /**
     * <p>Description:带复杂参数的查询 </p>
     *
     * @param list    list
     * @param orderby 排序
     * @param offset  limit
     * @param limit   limit
     * @return ist<T>
     */
    List<T> queryListByBatchCriteria(List<T> list, String orderby, Integer offset, Integer limit);

    /**
     * 根据主键值查找一条记录
     *
     * @param id id
     * @return T
     * @throws T
     */
    T findById(PK id);

    /**
     * 根据主键删除一条记录
     *
     * @param id id
     * @return boolean
     */
    boolean removeById(PK id);


}
