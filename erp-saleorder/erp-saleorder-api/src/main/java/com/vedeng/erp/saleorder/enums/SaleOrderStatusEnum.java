package com.vedeng.erp.saleorder.enums;

import com.vedeng.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单表主状态
 *
 * 订单状态：0待确认（默认）、1进行中、2已完结、3已关闭 4待用户确认
 */
@Slf4j
public enum SaleOrderStatusEnum {

    WAIT_CONFIRM(0, "待确认"),
    IN_PROGRESS(1, "进行中"),
    FINISHED(2, "已完结"),
    CLOSE(3, "已关闭"),
    TO_BE_CONFIRMED(4, "待用户确认")
    ;
    private Integer code;

    private String desc;

    SaleOrderStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String getDesc(Integer code){
        for(SaleOrderStatusEnum v : values()){
            if(v.getCode().equals(code)){
                return v.getDesc();
            }
        }
        log.info("没有对应的订单状态枚举值");
        throw new ServiceException("没有对应的订单状态枚举值");
    }
}
