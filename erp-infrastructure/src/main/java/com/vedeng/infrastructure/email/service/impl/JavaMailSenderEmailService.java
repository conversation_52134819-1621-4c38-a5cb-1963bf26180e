package com.vedeng.infrastructure.email.service.impl;

import com.vedeng.infrastructure.email.service.MailSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

/**
 * <AUTHOR>
 * @description
 * @date 2023/10/8 10:34
 **/
@Component
@Slf4j
public class JavaMailSenderEmailService implements MailSender {


    @Autowired
    private JavaMailSender invoiceJavaMailSender;

    @Override
    public void sendMailText(String to, String subject, String content) {
        if(StringUtils.isBlank(to)){
            log.info("邮箱收件人地址为空，不需要发送");
            return;
        }
        JavaMailSenderImpl invoiceJavaMailSender = (JavaMailSenderImpl) this.invoiceJavaMailSender;
        String username = invoiceJavaMailSender.getUsername();
        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom(username);
        message.setTo(to);
        message.setSubject(subject);
        message.setText(content);
        log.info("发送开票邮件：{}",to);
        invoiceJavaMailSender.send(message);
    }

    @Override
    public void sendMailHtml(String to, String subject, String content) {
        if(StringUtils.isBlank(to)){
            log.info("邮箱收件人地址为空，不需要发送");
            return;
        }

        JavaMailSenderImpl invoiceJavaMailSender = (JavaMailSenderImpl) this.invoiceJavaMailSender;
        String username = invoiceJavaMailSender.getUsername();
        MimeMessage mimeMessage = invoiceJavaMailSender.createMimeMessage();
        MimeMessageHelper mimeMessageHelper = null;
        try {
            mimeMessageHelper = new MimeMessageHelper(mimeMessage, true, "UTF-8");
            mimeMessage.setFrom(username);
            mimeMessageHelper.setTo(to);
            mimeMessageHelper.setSubject(subject);
            mimeMessageHelper.setText(content,true);
            log.info("发送开票邮件：{}",to);
            this.invoiceJavaMailSender.send(mimeMessage);
            log.info("发送开票邮件：{}，成功",to);
        } catch (Exception e) {
            log.error("发送开票邮件异常",e);
        }

    }
}
