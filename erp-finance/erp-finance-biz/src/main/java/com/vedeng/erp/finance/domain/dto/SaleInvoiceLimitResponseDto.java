package com.vedeng.erp.finance.domain.dto;

import lombok.Data;

/**
 * 销项开票余额返回值dto
 */
@Data
public class SaleInvoiceLimitResponseDto extends TaxesReturnInfo {

    private static final long serialVersionUID = -4855237883483350703L;

    /**
     * 可用授信额度
     */
    private String sysxed;

    /**
     * 已使用授信额度
     */
    private String ysysxed;

    /**
     * 总授信额度
     */
    private String zsxed;

    /**
     * 当前可用纸票数量
     */
    private String syzzfpzs;

    /**
     * 纸票已开具张数
     */
    private String ysyfpzs;

    /**
     * 发票总张数
     */
    private String zfpzs;

    /**
     * 本月开具蓝票张数
     */
    private String bykjlpzs;

    /**
     * 发票合计金额(蓝字发票开具金额)
     */
    private String fphjje;

    /**
     * 发票额减税额
     */
    private String fpejse;

    /**
     * 申报标志
     */
    private String sbbz;
}
