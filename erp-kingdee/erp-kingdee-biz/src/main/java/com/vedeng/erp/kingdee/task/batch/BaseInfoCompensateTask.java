package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.BaseInfoCompensateBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 基础数据补偿任务开始
 * @date 2024/2/23 10:28
 */
@JobHandler(value = "BaseInfoCompensateTask")
@Component
public class BaseInfoCompensateTask  extends AbstractJobHandler {

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private BaseInfoCompensateBatchJob baseInfoCompensateBatchJob;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        XxlJobLogger.log("==================基础数据补偿任务开始====================");
        JobParameters compensateJobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job baseInfoCompensateFlowJob = baseInfoCompensateBatchJob.baseInfoCompensateFlowJob();
        jobLauncher.run(baseInfoCompensateFlowJob, compensateJobParameters);
        XxlJobLogger.log("==================基础数据补偿任务结束====================");

        return SUCCESS;
    }
}
