package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderDto;
import com.vedeng.erp.kingdee.batch.processor.BatchInternalProcurementProcessor;
import com.vedeng.erp.kingdee.batch.tasklet.FlowOrderContractSignTasklet;
import com.vedeng.erp.kingdee.batch.tasklet.FlowOrderToErpTasklet;
import com.vedeng.erp.kingdee.batch.writer.BatchInternalProcurementWriter;
import com.vedeng.erp.kingdee.dto.KingDeeInternalProcurementDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 流转单流程
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class FlowOrderBatchJob extends BaseJob {


    @Autowired
    private BatchInternalProcurementProcessor batchInternalProcurementProcessor;
    @Autowired
    private BatchInternalProcurementWriter batchInternalProcurementWriter;
    @Autowired
    private FlowOrderToErpTasklet flowOrderToErpTasklet;
    @Autowired
    private FlowOrderContractSignTasklet flowOrderContractSignTasklet;


    public Job flowOrderJob() {
        return jobBuilderFactory.get("flowOrderJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(flowOrder())
                .next(flowOrderToErp())
                .next(flowOrderContractSign())
                .build();
    }

    private Step flowOrder() {
        return stepBuilderFactory.get("流转单推送")
                .<BatchFlowOrderDto, KingDeeInternalProcurementDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(flowOrderReader(null, null))
                .processor(batchInternalProcurementProcessor)
                .writer(batchInternalProcurementWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }


    private Step flowOrderToErp() {
        return stepBuilderFactory.get("流转单回传erp")
                .tasklet(flowOrderToErpTasklet)
                .build();
    }

    private Step flowOrderContractSign() {
        return stepBuilderFactory.get("流转单合同电子签章")
                .tasklet(flowOrderContractSignTasklet)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchFlowOrderDto> flowOrderReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        BatchFlowOrderDto flowOrderDto = BatchFlowOrderDto
                .builder()
                .beginTime(beginTime == null ? null : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? null : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchFlowOrderDto.class.getSimpleName(), flowOrderDto);
    }

}

