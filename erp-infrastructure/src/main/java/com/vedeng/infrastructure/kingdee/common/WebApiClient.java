package com.vedeng.infrastructure.kingdee.common;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.internal.bind.ObjectTypeAdapter;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.*;
import com.vedeng.infrastructure.kingdee.common.sdk.utils.CfgUtil;
import com.vedeng.infrastructure.kingdee.common.sdk.utils.MapTypeAdapter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/* loaded from: k3cloud-webapi-sdk7.9.2.jar:com/kingdee/bos/webapi/sdk/WebApiClient.class */
@Slf4j
public class WebApiClient {
    protected IdentifyInfo identify;
    K3CloudCookieStore cookier;
    int connectTimeout;
    int requestTimeout;
    int stockTimeout;

    public WebApiClient() {
        this("");
    }

    public WebApiClient(String serverUrl) {
        this(new IdentifyInfo().setServerUrl(serverUrl));
    }

    public WebApiClient(String serverUrl, int timeout) {
        this(new IdentifyInfo().setServerUrl(serverUrl));
        this.requestTimeout = timeout;
    }

    public WebApiClient(IdentifyInfo identify) {
        this.connectTimeout = 120;
        this.requestTimeout = 120;
        this.stockTimeout = 180;
        if (identify.getAppId() == null || identify.getAppId().length() <= 0 || identify.getAppSecret() == null || identify.getAppSecret().length() <= 0) {
            this.identify = new IdentifyInfo();
            AppCfg cfg = CfgUtil.getAppDefaultCfg();
            if (cfg != null) {
                this.identify.setAppId(cfg.getAppId());
                this.identify.setAppSecret(cfg.getAppSecret());
                this.identify.setdCID(cfg.getDCID());
                this.identify.setLcid(cfg.getLCID());
                this.identify.setOrgNum(cfg.getOrgNum());
                this.identify.setUserName(cfg.getUserName());
                if (this.identify.getServerUrl() == null || this.identify.getServerUrl().length() == 0) {
                    this.identify.setServerUrl(cfg.getServerUrl());
                    if (this.identify.getServerUrl() == null || this.identify.getServerUrl().length() == 0) {
                        this.identify.setServerUrl("https://api.kingdee.com/galaxyapi/");
                    }
                }
                if (cfg.getConnectTimeout() > 0) {
                    this.connectTimeout = cfg.getConnectTimeout();
                }
                if (cfg.getRequestTimeout() > 0) {
                    this.requestTimeout = cfg.getRequestTimeout();
                }
                if (cfg.getStockTimeout() > 0) {
                    this.stockTimeout = cfg.getStockTimeout();
                    return;
                }
                return;
            }
            return;
        }
        this.identify = identify;
    }

    protected void onPreExecute(String serviceMethod) {
    }

    protected void preExecute() {
        if (this.cookier == null) {
            this.cookier = new K3CloudCookieStore();
        }
    }

    protected void afterExecute(String json, K3CloudCookieStore cookier) {
        if (cookier != null) {
            this.cookier = cookier;
        }
    }

    Gson builderGson() {
        return new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
    }

    RequestBodyObject genrateBody(Object[] parameters, InvokeMode type) {
        RequestBodyObject json;
        if (type == InvokeMode.Query) {
            json = new QueryRequestBodyObject(parameters);
        } else {
            json = new RequestBodyObject(parameters);
        }
        return json;
    }

    public String execute(String serviceName, Object[] parameters) throws Exception {
        try {
            String json = this.executeJson(serviceName, parameters, InvokeMode.Syn);
            if (json.startsWith("response_error:")) {
                KDException ex = KDException.parse(json);
                if (ex == null) {
                    throw new Exception(json);
                } else {
                    throw new Exception(ex.getMessage());
                }
            } else {
                return json;
            }
        } catch (Exception var5) {
            log.error("【execute】 处理异常",var5);
            throw var5;
        }
    }

    public <T> List<T> execute(String serviceName, Object[] parameters, Type type) {
        try {
            String json = this.executeJson(serviceName, parameters, InvokeMode.Syn);
            Gson gson = this.builderGson();
            return gson.fromJson(json, type);
        } catch (Exception var6) {
            var6.printStackTrace();
            return null;
        }
    }

    public <T> T execute(String serviceName, Object[] parameters, Class<T> cls) {
        try {
            String json = this.executeJson(serviceName, parameters, InvokeMode.Syn);
            if (json.startsWith("response_error:")) {
                KDException ex = KDException.parse(json);
                if (ex == null) {
                    throw new Exception(json);
                } else {
                    throw new Exception(ex.getMessage());
                }
            } else {
                Gson gson = buildGson();
                return gson.fromJson(json, cls);
            }
        } catch (Exception var6) {
            var6.getStackTrace();
            return null;
        }
    }

    public static Gson buildGson() {
        Gson gson = (new GsonBuilder()).create();

        try {
            Field factories = Gson.class.getDeclaredField("factories");
            factories.setAccessible(true);
            Object o = factories.get(gson);
            Class<?>[] declaredClasses = Collections.class.getDeclaredClasses();
            Class[] var4 = declaredClasses;
            int var5 = declaredClasses.length;

            for (int var6 = 0; var6 < var5; ++var6) {
                Class c = var4[var6];
                if ("java.util.Collections$UnmodifiableList".equals(c.getName())) {
                    Field listField = c.getDeclaredField("list");
                    listField.setAccessible(true);
                    List<TypeAdapterFactory> list = (List) listField.get(o);
                    int i = list.indexOf(ObjectTypeAdapter.FACTORY);
                    list.set(i, MapTypeAdapter.FACTORY);
                    break;
                }
            }
        } catch (Exception var11) {
            var11.printStackTrace();
        }

        return gson;
    }

    String executeByQuery(String serviceName, Object[] parameters) throws Exception {
        try {
            String json = doExecuteJson(serviceName, parameters, InvokeMode.Query);
            if (json.startsWith("response_error:")) {
                KDException ex = KDException.parse(json);
                if (ex == null) {
                    throw new Exception(json);
                }
                throw new Exception(ex.getMessage());
            }
            Gson gson = new Gson();
            QueryResultInfo taskInfo = gson.fromJson(json, QueryResultInfo.class);
            if (taskInfo.getStatus() == 2) {
                return gson.toJson(taskInfo.getResult());
            }
            return gson.toJson(queryTaskResult(serviceName, new QueryTaskParam(taskInfo.getTaskId(), false), 5));
        } catch (Exception e) {
            e.getStackTrace();
            throw e;
        }
    }

    Object queryTaskResult(String serviceName, QueryTaskParam param, int failTime) throws Exception {
        try {
            int index = serviceName.lastIndexOf(".");
            String qService = serviceName.substring(0, index) + "." + "QueryAsyncResult";
            String json = this.doExecuteJson(qService, new Object[]{param}, InvokeMode.Syn);
            if (json.startsWith("response_error:")) {
                if (failTime > 0) {
                    return this.queryTaskResult(serviceName, param, failTime - 1);
                } else {
                    KDException ex = KDException.parse(json);
                    if (ex == null) {
                        throw new Exception(json);
                    } else {
                        throw new Exception(ex.getMessage());
                    }
                }
            } else {
                Gson gson = new Gson();
                QueryResultInfo taskInfo = gson.fromJson(json, QueryResultInfo.class);
                return taskInfo.getStatus() == 2 ? taskInfo.getResult() : this.queryTaskResult(serviceName, param, 5);
            }
        } catch (Exception var9) {
            if (failTime > 0) {
                return this.queryTaskResult(serviceName, param, failTime - 1);
            } else {
                throw var9;
            }
        }
    }

    String executeJson(String serviceName, Object[] parameters, InvokeMode type) throws Exception {
        if (type == InvokeMode.Query) {
            return executeByQuery(serviceName, parameters);
        }
        if (type == InvokeMode.Syn) {
            return doExecuteJson(serviceName, parameters, type);
        }
        throw new Exception("Not suppoer yet,for InvokeMode:" + type.toString());
    }

    String doExecuteJson(String serviceName, Object[] parameters, InvokeMode type) throws Exception {
        ApiRequester req;
        preExecute();
        onPreExecute(serviceName);
        String url = this.identify.getServerUrl();
        if (url == "" || url == null) {
            url = "https://api.kingdee.com/galaxy/";
        } else if (!url.endsWith("/")) {
            url = url + "/";
        }
        String url2 = url + serviceName + ".common.kdsvc";
        log.info("request_url " + url2);
        if (type == InvokeMode.Query) {
            req = new ApiQueryRequester(url2);
        } else {
            req = new ApiRequester(url2);
        }
        req.setConnectTimeout(this.connectTimeout);
        req.setConnectionRequrestTimeout(this.requestTimeout);
        req.setSocketTimeout(this.stockTimeout);
        req.setIdentify(this.identify);
        req.setCookieStore(this.cookier);
        try {
            String json = req.postJson(genrateBody(parameters, type));
            afterExecute(json, req.getCookieStore());
            return json;
        } catch (Exception e) {
            afterExecute(null, req.getCookieStore());
            e.getStackTrace();
            throw e;
        }
    }

    protected <T> List<T> loadDataList(String fieldKeys, Class type, String json) throws InstantiationException, IllegalAccessException, InvocationTargetException {
        Object v;
        ArrayList arrayList = new ArrayList();
        List<ArrayList<Object>> rows = (List) new Gson().fromJson(json, arrayList.getClass());
        Method[] pes = type.getMethods();
        String[] fields = fieldKeys.split(",");
        Method[] setPes = getMethodsByFields(pes, "set", fields);
        Method[] getPes = getMethodsByFields(pes, "get", fields);
        for (List<Object> darray : rows) {
            Object newInstance = type.newInstance();
            for (int i = 0; i < fields.length; i++) {
                if (!(setPes[i] == null || getPes[i] == null || (v = convertToDest(getPes[i].getReturnType(), darray.get(i))) == null)) {
                    setPes[i].invoke(newInstance, v);
                }
            }
            arrayList.add(newInstance);
        }
        return arrayList;
    }

    String loadErrorMsg(String json) {
        if (!json.startsWith("[[") || !json.endsWith("]]")) {
            return String.format("fail for ExecuteBillQuery:%s", json);
        }
        return ((RepoRet) new Gson().fromJson(json.substring(2, json.length() - 2), RepoRet.class)).getResult().getResponseStatus().getErrors().get(0).getMessage();
    }

    Method[] getMethodsByFields(Method[] pes, String pre, String[] fields) {
        Method[] rets = new Method[fields.length];
        for (int i = 0; i < fields.length; i++) {
            for (int j = 0; j < pes.length; j++) {
                if (pes[j].getName().toLowerCase().equals(pre + fields[i].toLowerCase())) {
                    rets[i] = pes[j];
                }
            }
        }
        return rets;
    }

    Method getMethodFromT(Method[] pes, String pname) {
        for (int i = 0; i < pes.length; i++) {
            if (pes[i].getName().toLowerCase().equals(pname.toLowerCase())) {
                return pes[i];
            }
        }
        return null;
    }

    Object convertToDest(Class<?> clazz, Object val) {
        if (val == null) {
            return null;
        }
        if (clazz.getName().equals(String.class.getName())) {
            return val.toString();
        }
        if (clazz.getName().equals(Integer.TYPE.getName()) || clazz.getName().equals(Short.TYPE.getName()) || clazz.getName().equals(Long.TYPE.getName())) {
            String v = val.toString();
            if (v.toString().indexOf(".") > -1) {
                v = v.substring(0, v.toString().indexOf("."));
            }
            return new Integer(v.toString());
        } else if (clazz.getName().equals(BigDecimal.class.getName())) {
            return new BigDecimal(val.toString());
        } else {
            if (!clazz.getName().equals(Date.class.getName())) {
                return val;
            }
            try {
                return new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss").parse(val.toString());
            } catch (ParseException e) {
                log.error("【convertToDest】处理异常",e);
                return null;
            }
        }
    }
}
