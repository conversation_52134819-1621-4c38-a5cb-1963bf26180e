package com.vedeng.erp.settlement.mapper;

import com.vedeng.erp.settlement.domain.entity.BankBillIgnoreRecordEntity;
import com.vedeng.erp.finance.dto.BankBillIgnoreRecordDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BankBillIgnoreRecordMapper {
    /**
     * delete by primary key
     *
     * @param bankBillIgnoreInfoId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer bankBillIgnoreInfoId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(BankBillIgnoreRecordEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(BankBillIgnoreRecordDto record);

    /**
     * select by primary key
     *
     * @param bankBillIgnoreInfoId primary key
     * @return object by primary key
     */
    BankBillIgnoreRecordEntity selectByPrimaryKey(Integer bankBillIgnoreInfoId);

    /**
     * select by bankBill key
     *
     * @return object by bankBill key
     */
    List<BankBillIgnoreRecordEntity> selectByBankBillId(@Param("bankBillId") Integer bankBillId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BankBillIgnoreRecordEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BankBillIgnoreRecordEntity record);
}