package com.vedeng.common.util;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Objects;

/**
 * <b>Description:</b><br> 全局获取request
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> erp
 * <br><b>PackageName:</b> com.vedeng.common.util
 * <br><b>ClassName:</b> RequestUtils
 * <br><b>Date:</b> 2017年5月3日 下午4:46:22
 */
public class RequestUtils {

	private final static String XML_HTTP_REQUEST = "XMLHttpRequest";

	/**
	 * <b>Description:</b><br> 获取request
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年5月3日 下午3:35:20
	 */
	public static HttpServletRequest getRequest(){
		HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
		return request;
	}


	public static void bindToRequestScope(String key, Object value) {
		RequestContextHolder.getRequestAttributes().setAttribute(key, value, RequestAttributes.SCOPE_REQUEST);
	}

	public static void unbindToRequestScope(String key) {
		RequestContextHolder.getRequestAttributes().removeAttribute(key, RequestAttributes.SCOPE_REQUEST);
	}

	public static boolean hasAttribute(String key){
		return RequestContextHolder.getRequestAttributes().getAttribute(key, RequestAttributes.SCOPE_REQUEST) != null;
	}


	public static String getRequestUri(){
        final HttpServletRequest request = getRequest();
        if (request==null) {
           throw new IllegalArgumentException("请求未进入到springMVC容器无法获取属性信息");
        }
       return getRequest().getRequestURI();
	}


	public static boolean isAjaxRequest(HttpServletRequest request) {
		Objects.requireNonNull(request, "request is null");
		return XML_HTTP_REQUEST.equals(request.getHeader("X-Requested-With"));
	}
}
