package com.vedeng.flash.service.message;

import com.vedeng.flash.dto.SendMessageDto;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.vo.BuyorderVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ExpeditionTaskOverdueMessageSender extends AbstractMessageSender {

    @Resource
    BuyorderMapper buyorderMapper;

    @Resource
    BuyorderGoodsMapper buyorderGoodsMapper;

    @Override
    protected String getUrl(SendMessageDto sendMessageDto) {
        String url = "";
        Integer buyorderGoodsId = sendMessageDto.getEarlyWarningTask().getRelateBusinessId();
        BuyorderGoods buyorderGoods = buyorderGoodsMapper.selectByPrimaryKey(sendMessageDto.getEarlyWarningTask().getRelateBusinessId());
        BuyorderVo buyorderVoById = buyorderMapper.getBuyorderVoById(buyorderGoods.getBuyorderId());
        if(buyorderVoById.getValidStatus() != null && buyorderVoById.getValidStatus() == 1) {
            url = "/order/buyorder/viewBuyordersh.do?buyorderId=" + buyorderVoById.getBuyorderId();
        }else{
            url = "/order/buyorder/viewBuyorder.do?buyorderId=" + buyorderVoById.getBuyorderId();
        };
        return url;
    }

    @Override
    protected List<Integer> getMessageUserIdList(SendMessageDto sendMessageDto) {
        String taskDealer = sendMessageDto.getEarlyWarningTask().getTaskDealer();
        List<Integer> userList = Collections.singletonList(Integer.valueOf(taskDealer));
        return userList;
    }

    @Override
    protected Integer getMessageTemplateId() {
        return 179;
    }

    @Override
    protected Map getTemplateVariableMap(SendMessageDto sendMessageDto) {
        Map<String,String> params = new HashMap<>();
        BuyorderGoods buyorderGoods = buyorderGoodsMapper.selectByPrimaryKey(sendMessageDto.getEarlyWarningTask().getRelateBusinessId());
        params.put("orderNo",sendMessageDto.getEarlyWarningTask().getBusinessExtra1());
        params.put("sku",buyorderGoods.getSku());
        return params;
    }
}
