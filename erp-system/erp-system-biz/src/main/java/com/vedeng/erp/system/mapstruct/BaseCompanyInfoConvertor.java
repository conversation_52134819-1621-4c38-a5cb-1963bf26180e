package com.vedeng.erp.system.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.system.domain.entity.BaseCompanyInfoEntity;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BaseCompanyInfoConvertor extends BaseMapStruct<BaseCompanyInfoEntity, BaseCompanyInfoDto> {
}
