package com.newtask.trader;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingPrincipalEntity;
import com.vedeng.erp.trader.dto.DwhCustomerTagCpmAddDfDto;
import com.vedeng.erp.trader.dto.TraderCustomerBussinessAreaDto;
import com.vedeng.erp.trader.mapper.TraderCustomerMarketingPrincipalMapper;
import com.vedeng.erp.trader.service.DwhCustomerTagCpmAddDfService;
import com.vedeng.erp.trader.service.TraderCustomerBussinessAreaService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/1 9:02
 **/
@Component
@JobHandler(value = "DwhCustomerTagCpmAddDfUpdateTask")
@Slf4j
public class DwhCustomerTagCpmAddDfUpdateTask extends AbstractJobHandler {

    @Autowired
    private DwhCustomerTagCpmAddDfService dwhCustomerTagCpmAddDfService;

    @Autowired
    private TraderCustomerMarketingPrincipalMapper traderCustomerMarketingPrincipalMapper;

    @Autowired
    private TraderCustomerBussinessAreaService traderCustomerBussinessAreaService;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("DwhCustomerTagCpmAddDfUpdateTask-JOB, START");
        log.info("大数据 DwhCustomerTagCpmAddDfUpdateTask start param:{}", param);
        DwhCustomerTagCpmAddDfDto query = new DwhCustomerTagCpmAddDfDto();
        Integer pageNum = 1;
        Integer pageSize = 1000;
        PageParam<DwhCustomerTagCpmAddDfDto> page = new PageParam<>();
        page.setParam(query);
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        if (StrUtil.isNotEmpty(param)) {
            query.setTraderId(Integer.valueOf(param));
        }

        while (true) {
            log.info("分页执行进度 {},{}", pageSize, pageNum);
            page.setPageNum(pageNum);
            PageInfo<DwhCustomerTagCpmAddDfDto> list = dwhCustomerTagCpmAddDfService.page(page);
            List<DwhCustomerTagCpmAddDfDto> data = list.getList();
            if (CollUtil.isEmpty(data)) {
                break;
            }
            List<TraderCustomerMarketingPrincipalEntity> updateList = new LinkedList<>();
            List<TraderCustomerMarketingPrincipalEntity> insertList = new LinkedList<>();
            List<TraderCustomerBussinessAreaDto> updateArea = new LinkedList<>();
            data.forEach(x->{
                TraderCustomerMarketingPrincipalEntity entity = traderCustomerMarketingPrincipalMapper.findByTraderIdAndTraderCustomerId(x.getTraderId(), x.getTraderCustomerId());
                if (entity != null) {
                    if (StrUtil.isNotEmpty(x.getSkuTypes())) {
                        entity.setSkuType(x.getSkuTypes().replace(';', ','));
                    }
                    if (StrUtil.isNotEmpty(x.getRelations())) {
                        entity.setGovernmentRelation(x.getRelations().replace(';', ','));
                    }
                    updateList.add(entity);
                } else {
                    TraderCustomerMarketingPrincipalEntity entityNew = new TraderCustomerMarketingPrincipalEntity().init();
                    entityNew.setTraderId(x.getTraderId());
                    entityNew.setTraderCustomerId(x.getTraderCustomerId());
                    if (StrUtil.isNotEmpty(x.getSkuTypes())) {
                        entityNew.setSkuType(x.getSkuTypes().replace(';', ','));
                    }
                    if (StrUtil.isNotEmpty(x.getRelations())) {
                        entityNew.setGovernmentRelation(x.getRelations().replace(';', ','));
                    }
                    insertList.add(entityNew);
                }
                if (StrUtil.isNotEmpty(x.getAreaIds())) {

                    List<String> split = StrUtil.split(x.getAreaIds(), ";");
                    if (CollUtil.isNotEmpty(split)) {
                        split.forEach(e->{
                            TraderCustomerBussinessAreaDto areaDto = new TraderCustomerBussinessAreaDto();
                            areaDto.setAreaIds(e);
                            List<String> stringList = StrUtil.split(e, StrUtil.COMMA);
                            String s = stringList.get(stringList.size() - 1);
                            if(StrUtil.isNotEmpty(s)){
                                areaDto.setAreaId(Integer.valueOf(s));
                            }
                            areaDto.setTraderCustomerId(x.getTraderCustomerId());
                            updateArea.add(areaDto);
                        });

                    }
                }

            });

            if (CollUtil.isNotEmpty(updateList)) {
                traderCustomerMarketingPrincipalMapper.updateBatchSelective(updateList);
            }

            if (CollUtil.isNotEmpty(insertList)) {
                traderCustomerMarketingPrincipalMapper.batchInsert(insertList);
            }

            if (CollUtil.isNotEmpty(updateArea)) {
                traderCustomerBussinessAreaService.batchUpdate(updateArea);
            }


            if (!list.isHasNextPage()) {
                break;
            }

            pageNum=list.getNextPage();
        }


        XxlJobLogger.log("DwhCustomerTagCpmAddDfUpdateTask-JOB, END");
        log.info("大数据 DwhCustomerTagCpmAddDfUpdateTask end");

        return ReturnT.SUCCESS;
    }
}
