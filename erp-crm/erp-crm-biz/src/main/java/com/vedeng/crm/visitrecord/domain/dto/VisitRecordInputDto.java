package com.vedeng.crm.visitrecord.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/2/28
 */
@Data
public class VisitRecordInputDto {

    /**
     * 创建下次拜访计划时的上一次拜访计划ID
     */
    private Integer preId;

    private Integer id;

    /** 拜访计划编号 */
    private String visitRecordNo;

    /** 交易者ID */
    private Integer traderId;
    /** 客户来源方式 (1erp2终端库3天眼查) */
    private Integer customerFrom;
    /** 客户名称 */
    private String customerName;
    /** 客户类型 (ERP客户时自动带入且不能修改，其他必填。465分销466终端) */
    private Integer customerNature;

    /** 拜访内容-联系人信息-姓名 */
    private String contactName;

    /** 拜访内容-联系人信息-手机 */
    private String contactMobile;

    /** 拜访内容-联系人信息-电话 */
    private String contactTele;

    /** 拜访内容-联系人信息-职位 */
    private String contactPosition;

    /** 其它联系方式 */
    private String otherContact;

    /** 无联系人 (Y是N否) */
    private String noContract;


    /** 省份编码 */
    private Integer provinceCode;

    /** 省份名称 */
    private String provinceName;

    /** 城市编码 */
    private Integer cityCode;

    /** 城市名称 */
    private String cityName;

    /** 区编码 */
    private Integer areaCode;

    /** 区名称 */
    private String areaName;
    /** 拜访地址 */
    private String visitAddress;

    /** 拜访人 */
    private Integer visitorId;
    /**
     * 拜访人工号
     */
    private String visitorJobNumber;

    /** 拜访人用户名 */
    private String visitorName;

    /** 计划拜访时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planVisitDate;

    /** 拜访目标 (A新客开发B商机跟进C老客客情维护D签约会员E产品推广，以逗号隔开) */
    private String visitTarget;

    private String visitTargetStr;

    private RelateBizData relateBizData;

    /**
     * M端是根据企业微信Js组件获取的人，人的信息是工号
     */
    private List<String> tongxingJobNumbers;

    private List<Integer> tongxingIds;

    /** 备注 */
    private String remark;

    @Data
    @NoArgsConstructor
    public static class RelateBizData{
        /** 关联单据类型 (0默认商机线索类型 1线索2商机) */
        private Integer relateType;

        /**
         * 线索或商机的ID
         */
        private Integer bizId;

        /**
         * 线索或商机编号
         */
        private String bizNo;



    }

}
