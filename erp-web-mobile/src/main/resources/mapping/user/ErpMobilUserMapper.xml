<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.mobile.system.mapper.ErpMobilUserMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.mobile.system.model.User">
        <id property="userDetailId" column="USER_DETAIL_ID" jdbcType="INTEGER"/>
        <result property="userId" column="USER_ID" jdbcType="VARCHAR"/>
        <result property="mobile" column="MOBILE" jdbcType="VARCHAR"/>
        <result property="telephone" column="TELEPHONE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        USER_DETAIL_ID,USER_ID,MOBILE,TELEPHONE
    </sql>

    <select id="getMobileByUserId" resultType="java.lang.String" parameterType="java.lang.Integer">
        SELECT
            MOBILE
        FROM
            T_USER_DETAIL
        WHERE
            USER_ID = #{userId,jdbcType=INTEGER}
    </select>

    <select id="userLoginInfo" resultType="com.vedeng.uac.api.dto.UserLoginInfo">
        SELECT
            a.USER_ID as userId,
            a.USERNAME as userName,
            a.PASSWORD as passWord,
            a.SALT as salt,
            a.SYSTEM as systems
        FROM
            T_USER a
        LEFT JOIN T_USER_DETAIL b ON a.USER_ID = b.USER_ID
        WHERE
            a.USERNAME =#{userName,jdbcType=VARCHAR}
          AND a.COMPANY_ID = 1
          AND a.IS_DISABLED=0
    </select>
</mapper>
