<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="维护金蝶费用" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<%@ include file="../../component/remarkComponent.jsp" %>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script src="/webjars/ezadmin/plugins/cascader/xm-select.js"></script>

<body>
<script>


    function deleteInterfaceParam(obj) {
        $(obj).parent().parent().remove()
    }

    function addNewRow() {
        var oriHtml = $("#updateTbody").html()

        var newHtml = "\n <tr class=\"add\">" +
            "<td class=\"add\"><input name=\"addCategoryKingDeeNo\" style='width: 100%;' type='text' onchange=\"changeValue(this)\"/></td>" +
            "<td class=\"add\"><input name=\"addCategoryKingDeeName\" style='width: 100%;' type='text' onchange=\"changeValue(this)\" /></td>" +
            "<td><a class=\"\" href=\"javascript:void(0);\" onClick=\"deleteInterfaceParam(this)\" style=\"text-align:center; color: #3384ef;\" >删除</a></td>" +
            "</tr> \n";

        var genHtml = newHtml + oriHtml

        $("#updateTbody").html(genHtml)


    }

    layui.use('form', function () {

    })





    function changeValue(obj){
        $(obj).attr("value",$(obj).val());
    }

    function save(){
        var kingDeeCategoryList = []
        if (check()){
            return false
        }
        $('table tr.add').each(function () {
            var kingDeeCategory = {}
            $(this).find("td.add").each( function (){
                var item = $(this).find("input");
                var itemName = item.attr("name");
                var itemValue = item.val();
                if(itemName == 'addCategoryKingDeeNo'){
                    kingDeeCategory["costCategoryKingDeeNo"] = itemValue
                }
                if (itemName == 'addCategoryKingDeeName'){
                    kingDeeCategory["costCategoryKingDeeName"] = itemValue
                }
            })
            kingDeeCategoryList.push(kingDeeCategory)
        })
        console.log(kingDeeCategoryList)



        $.ajax({
            url: '/costCategory/addKingDeeCategory.do',
            type: 'post',
            dataType: 'json',
            data: "list="+encodeURIComponent(JSON.stringify(kingDeeCategoryList)),
            success: function (data) {
                debugger
                if (data.code === 0) {
                    window.parent.location.reload();
                    layer.close(index);
                } else {
                    layer.alert(data.message);
                }
            }
        })
    }

    function check(){
        var out = false
        $('table tr.add').find("td.add").each(function(){
            var item = $(this).find("input");
            var name = item.attr("name");
            var value = item.val().trim();
            if(!value){
                if (name == 'addCategoryKingDeeNo'){
                    layer.alert("金蝶费用编号未填写")
                }else {
                    layer.alert("金蝶费用名称未填写")
                }
                out = true
                return true
            }
            if (value.length > 10){
                if (name == 'addCategoryKingDeeNo'){
                    layer.alert("费用编号不能大于10个字")
                }else {
                    layer.alert("费用名称不能大于10个字")
                }
                out = true
                return true
            }
        })
        return out;
    }

</script>

<div class="content">
    <div class="formtitle"></div>
    <div id="desc_div">
        <form method="post" id="editForm">
            <ul class="editplan">
                <div>
                    <li>
                        <div class="title-container title-container-blue">
                            <div class="title-click nobor"
                                 layerParams='{"width":"700px","height":"480px","title":"新增"}'
                                 onclick="addNewRow()">添加
                            </div>
                        </div>
                        <table class="table table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:40%"><font style="color: red">*</font>金蝶费用编号</th>
                                <th style="width:50%"><font style="color: red">*</font>金蝶费用名称</th>
                                <th style="width:10%">操作</th>
                            </tr>
                            </thead>
                            <tbody id="updateTbody">
                            <c:forEach items="${kingDeeCategory}" var="list" varStatus="status">
                                <tr>
                                    <td><xmp>${list.costCategoryKingDeeNo}</xmp></td>
                                    <td><xmp>${list.costCategoryKingDeeName}</xmp></td>
                                    <td></td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </li>
                </div>
                <div style="margin-left: 10px">
                    <font style="color: #00b7ee">不可与现有的金蝶费用编号重名</font>
                </div>
            </ul>
            <div class="add-tijiao tcenter mt10">
                <button class="bt-bg-style bt-small bg-light-green" type="button" id="submit" onclick="save()">提交</button>
                <button class="bt-bg-style bt-small bg-light-red" id="close-layer" type="button">取消</button>
            </div>
        </form>
    </div>
</div>
</body>
