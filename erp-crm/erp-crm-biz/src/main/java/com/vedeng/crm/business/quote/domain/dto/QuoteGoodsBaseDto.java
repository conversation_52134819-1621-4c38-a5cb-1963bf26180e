package com.vedeng.crm.business.quote.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class QuoteGoodsBaseDto implements Serializable {

    private static final long serialVersionUID = 1856064443848720550L;

    /**
     * 商品ID
     */
    private Integer goodsId;

    /**
     * 唯一编码
     */
    private String sku;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 商品型号
     */
    private String model;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 原数量
     */
    private Integer oldNum;

    /**
     * 中文名
     */
    private String unitName;

    /**
     * 货期
     */
    private String deliveryCycle;

    /**
     * 报备结果0不需要报备 1等待报备 2成功 3失败
     */
    private Integer reportStatus;

    /**
     * 报备原因
     */
    private String reportComments;

}
