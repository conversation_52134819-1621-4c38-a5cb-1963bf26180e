package com.vedeng.erp.saleorder.buzlogic.create;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.saleorder.buzlogic.create.impl.ZxfEditSaleOrderBuzLogic;
import com.vedeng.erp.saleorder.service.BaseSaleOrderService;
import com.vedeng.order.model.Saleorder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: thor
 * @Date: 2021/10/8 13 56
 * @Description:
 */
@Service
public class EditSaleOrderBuzLogic {
    Logger logger= LoggerFactory.getLogger(EditSaleOrderBuzLogic.class);

    private List<String> sequence = Arrays.asList("saveEditSaleOrderInfo","sendMessage");

    public List<String> getSequence() {
        return sequence;
    }

    public void setSequence(List<String> sequence) {
        this.sequence = sequence;
    }

    @Autowired
    private BaseSaleOrderService baseSaleOrderService;

    public ResultInfo saveEditSaleOrderInfo(Saleorder saleorder){
        if(ErpConst.ZERO.equals(saleorder.getIsSendInvoice())){
            saleorder.setIsSameAddress(ErpConst.ZERO);
        }
        baseSaleOrderService.saveEditSaleOrderInfo(saleorder);
        return new ResultInfo(0,"保存编辑销售单信息成功");
    }

    public void sendMessage(){

    }

    public ResultInfo run(Saleorder saleorder){
        ResultInfo result = new ResultInfo(0,"操作成功");
        for (String methodName : getSequence()){
            switch (methodName) {
                case "saveEditSaleOrderInfo":
                    if(ErpConst.ZERO.equals(result.getCode())){
                        result = this.saveEditSaleOrderInfo(saleorder);
                        logger.info("编辑销售单{},返回信息{},", saleorder.getSaleorderId(),result.getMessage());
                    }
                    break;
                case "sendMessage":
                    this.sendMessage();
                    break;
                default:
                    break;
            }
        }
        return result;
    }
}
