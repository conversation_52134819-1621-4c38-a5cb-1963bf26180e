let uiDialogVue = Vue.component('ui-dialog', {
    template: `
        <transition name="dialog-fade">
            <div class="mui-dialog-container" v-show="isShow">
                <div class="nui-custom-dislog-center">
                    <transition name="dialog-move">
                        <div class="mui-dialog-wrapper" v-show="isShow" :class="[type]">
                            <div class="mui-dialog-content">
                                <div class="mui-dialog-icon" v-if="type">
                                    <i class="vd-ui_icon" :class='icon'></i>
                                </div>
                                <div class="mui-dialog-title" v-if="title" v-html="title"></div>
                                <div class="mui-dialog-middle" v-if="message" v-html="message"></div>
                            </div>

                            <div class="mui-dialog-button-choice">
                                <div
                                    class='mui-dialog-button'
                                    v-for='(item, index) in buttons'
                                    :class='item.btnClass'
                                    @click="handleButton(item)"
                                    :key="index"
                                >{{ item.txt }}</div>
                            </div>
                        </div>
                    </transition>

                </div>
            </div>
        </transition>
    `,
    data() {
        return {
            isShow: false,
            type: '',
            title: '',
            message: '',
            buttons: [],
            defaultClass: '',  // 按钮默认class
            iconType: {
                'success': 'icon-yes2',
                'error': 'icon-error2',
                'warn': 'icon-caution2',
                'info': 'icon-info2'
            }
            // btnClass： disabled:置灰色  confirm:蓝色  delete:红色  cancel:默认色
        }
    },
    computed: {
        icon() {
            return this.iconType[this.type];
        }
    },
    methods: {
        show() {
            this.isShow = true;
        },
        hide() {
            this.isShow = false;
        },
        handleButton(item) {
            this.isShow = false;
            if (item.callback) {
                item.callback();
            }
        },
        deleteButton() {
            this.isShow = false;
            this.handleDelete && this.handleDelete();
        }
    }
})

let installDialogComponents = () => {
    const VdPop = Vue.extend(uiDialogVue);
    let vm = null;

    const showDialog = ({type, title, message, ...opt}) => {
        return new Promise((resolve, reject) => {
            if (!vm) {
                vm = new VdPop();
                vm.$mount();
                document.body.appendChild(vm.$el);
            } else {
                vm.hide();
            }

            vm.type = type || '';
            vm.title = title || '';
            vm.message = message || '';
            vm.buttons = opt.buttons || vm.buttons;
            vm.handleDelete = opt.handleDelete || vm.handleDelete;
            vm.defaultClass = opt.defaultClass || vm.defaultClass;

            vm.show();
        });
    }

    const fn = (type, opt) => {
        return Promise.resolve(showDialog(type, opt));
    };

    showDialog.success = fn.bind(null, 'success');
    showDialog.error = fn.bind(null, 'error');
    showDialog.warn = fn.bind(null, 'warn');
    showDialog.info = fn.bind(null, 'info');

    Vue.prototype.$dialog = showDialog;
}

installDialogComponents();