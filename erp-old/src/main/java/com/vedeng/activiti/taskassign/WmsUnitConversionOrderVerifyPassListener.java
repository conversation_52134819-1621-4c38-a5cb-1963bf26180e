package com.vedeng.activiti.taskassign;

import com.pricecenter.constant.VerifyStatusEnum;
import com.wms.unitconversionorder.service.WmsUnitConversionOrderService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

/**
 * 单位转换单审核完成事件监听器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/22 11:50
 */
public class WmsUnitConversionOrderVerifyPassListener implements ExecutionListener {

    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private WmsUnitConversionOrderService wmsUnitConversionOrderService = (WmsUnitConversionOrderService) context.getBean("wmsUnitConversionOrderServiceImpl");

    @Override
    public void notify(DelegateExecution execution) throws Exception {
        String businessKey = execution.getVariable("businessKey").toString();
        Integer receiveOutOrderId = Integer.valueOf(businessKey.split("_")[1]);
        boolean pass = Boolean.valueOf(execution.getVariable("pass").toString());
        wmsUnitConversionOrderService.audit(receiveOutOrderId, pass ? VerifyStatusEnum.Approved.getValue() : VerifyStatusEnum.Reject.getValue());

        if (pass) {
            wmsUnitConversionOrderService.putWmsTask(receiveOutOrderId);
        }
    }
}