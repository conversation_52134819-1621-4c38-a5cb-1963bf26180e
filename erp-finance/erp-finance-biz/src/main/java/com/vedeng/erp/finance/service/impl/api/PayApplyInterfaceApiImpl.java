package com.vedeng.erp.finance.service.impl.api;

import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.finance.service.PayApplyAutoPayApi;
import com.vedeng.erp.finance.dto.PayApplyCheckDto;
import com.vedeng.erp.finance.dto.PayApplyCreateBillDto;
import com.vedeng.erp.finance.facade.PayApplyFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class PayApplyInterfaceApiImpl implements PayApplyAutoPayApi {

    @Autowired
    private PayApplyFacade payApplyFacade;

    /**
     * 付款申请校验
     * @param payApplyCheckDto
     * @throws ServiceException
     */
    @Override
    public void payApplyRuleCheck(PayApplyCheckDto payApplyCheckDto) throws ServiceException{
        payApplyFacade.payApplyRuleCheck(payApplyCheckDto);
    }

    @Override
    public List<PayApplyCreateBillDto> findPayApply(Integer payApplyId) {
        // 筛选付款数据
        PayApplyCreateBillDto payApplyDto = new PayApplyCreateBillDto();
        // 待审核
        payApplyDto.setValidStatus(0);
        // 未制单
        payApplyDto.setIsBill(0);
        // 未付款
        payApplyDto.setPayStatus(0);
        // 2024-01-01 00:00:00
        payApplyDto.setBeginAddTime(1704038400000L);
        // 公司id
        payApplyDto.setCompanyId(1);
        if (Objects.nonNull(payApplyId)){
            payApplyDto.setPayApplyId(payApplyId);
        }
        List<PayApplyCreateBillDto> payApplyByDto = payApplyFacade.getPayApplyByDto(payApplyDto);
        return payApplyByDto;
    }

    /**
     * 是否满足自动制单
     * @param payApplyCreateBillDto
     * @throws ServiceException
     */
    @Override
    public void createBillRuleCheck(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException {
        payApplyFacade.createBillRuleCheck(payApplyCreateBillDto);
    }
}
