<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/vd-ui/common.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/vd-ui/ui.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/flowOrder/add.css?rnd=${resourceVersionKey}">

<div id="page-container" class="wrap">
    <template v-if="!pageloading">
        <div class="container-l" :class="{hide: !isShowLeft}">
            <div class="l-title">采购单号：{{ baseBusinessNo }}</div>
            <div class="l-steps">
                <div class="step-list">
                    <div class="step-item" v-for="(item, index) in supplierShowList">
                        <div class="step-l">
                            <div class="step-l-txt">{{ supplierData.length-index }}级</div>
                            <div class="step-gap">
                                <i class="el-icon-top"></i>
                            </div>
                        </div>
                        <div class="step-r">
                            <div class="step-company">
                                <div class="text-line-1" :title="item.traderName || ''">{{ item.traderName || '-' }}</div></div>
                            <div class="step-rate">
                                <div class="rate-item">加价率：{{ totalAddRate[index] || '-' }}</div>
                                <div class="rate-item">毛利率：{{ totalProfitRate[index] || '-' }}</div>
                            </div>
                            <div class="step-gap">
                                <i class="el-icon-top"></i>
                            </div>
                        </div>
                    </div>
                    <div class="step-item">
                        <div class="step-l">
                            <div class="step-l-txt">0级</div>
                        </div>
                        <div class="step-r">
                            <div class="step-company">{{ sourceErpName }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="l-trigger-show" @click="isShowLeft = !isShowLeft" :title="isShowLeft ? '收起' : '展开'">
                <i class="el-icon-arrow-left"></i>
            </div>
        </div>
        <div class="container-r" :class="{'left-hide': !isShowLeft}">
            <div class="content-block">
                <div class="block-title">
                    供应商信息
                    <div class="block-title-options">
                        <div class="option-item" @click="addSupplier" :class="{disabled: supplierData.length >= 9}">
                            <i class="el-icon-plus"></i>
                            <div class="item-txt">添加上游</div>
                        </div>
                    </div>
                </div>
                <div class="block-table">
                    <el-table :data="supplierData" highlight-current-row :key="supplierTableKey" size="mini" border>
                        <el-table-column label="节点" width="45" type="index"></el-table-column>
                        <el-table-column label="供应商" min-width="180" label-class-name="must">
                            <template slot-scope="scope">
                                <ui-select :errorable="submitFlag && !scope.row.traderName" :remote="true" size="small" :disabled="scope.$index === 0" :default-label="scope.row.traderName || ''" placeholder="请选择供应商" v-model="scope.row.traderId" :remote-info="{
                                    url: '/flowOrder/getTrader.do?traderType=2',
                                    paramsMethod: 'get',
                                    paramsKey: 'keywords'
                                }" @change="supplierChange(scope.$index, $event)" :remote-data-parse="parseSupplierList" list-width="260px">
                                    <template v-slot:option="{ item }">
                                        <div class="supplier-option-item" :title="!item.riskPass ? '风控不通过' : ''">
                                            <div class="item-label text-line-1" :title="item.label">{{ item.label }}</div>
                                            <div class="item-icon" :class="{disabled: item.disabled}"></div>
                                        </div>
                                    </template>
                                </ui-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="开票" width="45">
                            <template slot-scope="scope">
                                <el-checkbox v-model="scope.row.isInvoice" @change="isInvoiceChange(scope.$index)"></el-checkbox>
                            </template>
                        </el-table-column>
                        <el-table-column label="联系人" width="185" label-class-name="must">
                            <template slot-scope="scope">
                                <ui-select :errorable="submitFlag && !scope.row.traderContactName" :remote="true" size="small" :disabled="!scope.row.traderId || scope.$index === 0" :default-label="scope.row.traderContactName ? scope.row.traderContactName + ' ' + scope.row.traderContactPhone : ''" placeholder="请选择联系人" v-model="scope.row.traderContactId" :remote-info="{
                                    url: '/flowOrder/getTraderContact.do?traderType=2&traderId=' + scope.row.traderId,
                                    paramsMethod: 'get',
                                    paramsKey: 'keywords',
                                }" @change="contactChange(scope.$index, $event)" list-width="200px" :remote-data-parse="parseContactList" :search-default-all="true">
                                </ui-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="联系地址" width="250" label-class-name="must">
                            <template slot-scope="scope">
                                <ui-select :errorable="submitFlag && !scope.row.traderContactAddress" :remote="true" size="small" :disabled="!scope.row.traderId || scope.$index === 0" :default-label="scope.row.traderContactAddress || ''" placeholder="请选择联系地址" v-model="scope.row.traderAddressId" :remote-info="{
                                    url: '/flowOrder/getTraderAddress.do?traderType=2&traderId=' + scope.row.traderId,
                                    paramsMethod: 'get',
                                    paramsKey: 'keywords',
                                }" @change="traderAddressChange(scope.$index, $event)" list-width="320px" :remote-data-parse="parseAddressList" :search-default-all="true">
                                </ui-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="收货人" width="185" label-class-name="must">
                            <template slot-scope="scope">
                                <ui-select :errorable="submitFlag && !scope.row.receiverName" :remote="true" size="small" :disabled="!scope.row.traderId || scope.$index === 0" :default-label="scope.row.receiverName ? scope.row.receiverName + ' ' + scope.row.receiverPhone : ''" placeholder="请选择收货人" v-model="scope.row.receiverTraderContactId" :remote-info="{
                                    url: '/flowOrder/getTraderContact.do?traderType=2&traderId=' + scope.row.traderId,
                                    paramsMethod: 'get',
                                    paramsKey: 'keywords',
                                }" @change="receiverChange(scope.$index, $event)" list-width="200px" :remote-data-parse="parseContactList" :search-default-all="true">
                                </ui-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="收货地址" width="250" label-class-name="must">
                            <template slot-scope="scope">
                                <ui-select :errorable="submitFlag && !scope.row.receiverAddress" :remote="true" is-right="1" class="ui-select-right" size="small" :disabled="!scope.row.traderId || scope.$index === 0" :default-label="scope.row.receiverAddress || ''" placeholder="请选择收货地址" v-model="scope.row.receiverAddressId" :remote-info="{
                                    url: '/flowOrder/getTraderAddress.do?traderType=2&traderId=' + scope.row.traderId,
                                    paramsMethod: 'get',
                                    paramsKey: 'keywords',
                                }" @change="receiverAddressChange(scope.$index, $event)" list-width="320px" :remote-data-parse="parseAddressList" :search-default-all="true">
                                </ui-select>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column label="收票人" width="160">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.contact" size="mini" filterable remote placeholder="请输入" :remote-method="remoteMethod" :loading="supplierLoading">
                                    <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="收票地址" width="180">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.contact" size="mini" filterable remote placeholder="请输入" :remote-method="remoteMethod" :loading="supplierLoading">
                                    <el-option v-for="item in supplierOptions" :key="item.value" :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </template>
                        </el-table-column> -->
                        <el-table-column label="操作" width="70" fixed="right">
                            <template slot-scope="scope">
                                <div class="option-btn option-red" v-if="scope.$index !== 0" @click="deleteSupplier(scope.$index)">删除</div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="form-error-wrap" v-if="supplierError">
                    <i class="el-icon-error"></i>
                    <div class="error-label">请完善必填字段</div>
                </div>
            </div>
            <div class="content-block">
                <div class="block-title">
                    商品信息
                </div>
                <div class="block-table" v-if="isShowGoodsTable">
                    <el-table :data="goodsData" highlight-current-row ref="goodsTable" :summary-method="getGoodsSummary" show-summary max-height="360" size="mini" border style="width: 100%">
                        <el-table-column label="订货号" width="80" prop="skuNo" fixed="left"></el-table-column>
                        <el-table-column label="产品名称" min-width="220">
                            <template slot-scope="scope">
                                <div class="text-line-1" :title="scope.row.productName">{{ scope.row.productName }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="品牌" width="100">
                            <template slot-scope="scope">
                                <div class="text-line-1" :title="scope.row.brand">{{ scope.row.brand }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="规格/型号" width="100">
                            <template slot-scope="scope">
                                <div class="text-line-1" :title="scope.row.model">{{ scope.row.model }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="数量" width="70" prop="quantity"></el-table-column>
                        <el-table-column label="单位" width="90" prop="unit"></el-table-column>
                        <!-- <el-table-column label="单位" width="90">
                            <template slot-scope="scope">
                                {{scope.row}}
                            </template>
                        </el-table-column> -->
                        <template v-if="supplierData.length >= 1">
                            <template v-for="(item, index) in supplierShowList">
                                <template v-if="supplierData.length - index - 1 < supplierData.length - 1">
                                    <el-table-column align="center" width="105" :label="(supplierData.length - index) + '-' + (supplierData.length - index - 1) + '加价率'" :prop="index + 1" label-class-name="must">
                                        <template slot="header" slot-scope="scope">
                                            <div class="rate-header-wrap">
                                                <div class="header-txt">
                                                    {{(supplierData.length - index) + '-' + (supplierData.length - index - 1) + '加价率'}}
                                                </div>
                                                <i class="el-icon-edit" @click="showRateDialog(supplierData.length - index)"></i>
                                            </div>
                                        </template>
                                        <template slot-scope="scope">
                                            <div class="rate-wrap">
                                                <el-input-number v-model="scope.row.flowNodeOrderDetailPriceDtoList[supplierData.length - index].markupRate" size="mini" @change="goodsPriceChange(supplierData.length-index, scope.$index, 1)" :controls="false" :precision="2" max="9999.99"></el-input-number>%
                                            </div>
                                        </template>
                                    </el-table-column>
                                </template>
                                <el-table-column align="center" width="120" :label="(supplierData.length - index - 1) + '-' + (supplierData.length - index) + '采购价'" :prop="index + 1" label-class-name="must">
                                    <template slot-scope="scope">
                                        <div :class="{error: submitFlag && scope.row.flowNodeOrderDetailPriceDtoList[supplierData.length - index - 1].price == 0}">
                                            <el-input-number v-model="scope.row.flowNodeOrderDetailPriceDtoList[supplierData.length - index - 1].price" size="mini" :controls="false" :precision="2" max="9999999999.99" min="0" @change="goodsPriceChange(supplierData.length-index-1, scope.$index, 0)"></el-input-number>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column align="center" width="90" :label="(supplierData.length - index) + '级毛利率'" :prop="index + 1">
                                    <template slot-scope="scope">
                                        <template v-if="index === 0">-</template>
                                        <template v-else>
                                            {{ scope.row.flowNodeOrderDetailPriceDtoList[supplierData.length - index - 1].grossProfitRate || scope.row.flowNodeOrderDetailPriceDtoList[supplierData.length - index - 1].grossProfitRate === 0 ? scope.row.flowNodeOrderDetailPriceDtoList[supplierData.length - index - 1].grossProfitRate + '%' : '-' }}
                                        </template>
                                    </template>
                                </el-table-column>
                            </template>
                            <template v-for="(item, index) in supplierShowList">
                                <el-table-column align="center" :prop="index + 1" width="105" :label="(supplierData.length - index - 1) + '-' + (supplierData.length - index) + '采购金额'">
                                    <template slot-scope="scope">
                                        {{ calcGoodsItemLevelTotal(supplierData.length - index - 1, scope.row) }}
                                    </template>
                                </el-table-column>
                            </template>
                        </template>
                    </el-table>
                </div>
                <div class="form-error-wrap" v-if="goodsError">
                    <i class="el-icon-error"></i>
                    <div class="error-label">请完善必填字段，采购价不能为0</div>
                </div>
            </div>
            <div class="content-block">
                <div class="block-title">
                    款票信息
                </div>
                <div class="block-table">
                    <el-table :data="supplierData" highlight-current-row size="mini" border style="width: 100%">
                        <el-table-column label="节点" width="60">
                            <template slot-scope="scope">
                                {{ scope.$index }}-{{ scope.$index + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column label="付款方式" min-width="185">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.paymentMethod" :disabled="scope.$index === 0" placeholder="请选择" size="mini" @change="handlerPaymethodChange(scope.$index)">
                                    <el-option
                                      v-for="item in payTypeList"
                                      :key="item.value"
                                      :label="item.label"
                                      :value="item.value">
                                    </el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="付款金额" width="110px">
                            <template slot-scope="scope">
                                <el-input-number :disabled="scope.row.paymentMethod == 0 || scope.$index === 0" v-model="scope.row.amount" size="mini" :controls="false" :precision="2" max="9999999999.99" min="0" @change="payPriceChange(scope.$index, 'amount')"></el-input-number>
                            </template>
                        </el-table-column>
                        <el-table-column label="账期支付" width="110px">
                            <template slot-scope="scope">
                                <el-input-number :disabled="scope.row.paymentMethod == 0 || scope.$index === 0" v-model="scope.row.creditPayment" size="mini" :controls="false" :precision="2" max="9999999999.99" min="0" @change="payPriceChange(scope.$index, 'creditPayment')"></el-input-number>
                            </template>
                        </el-table-column>
                        <el-table-column label="尾款" width="110px">
                            <template slot-scope="scope">
                                <el-input-number :disabled="scope.row.paymentMethod == 0 || scope.$index === 0" v-model="scope.row.balance" size="mini" :controls="false" :precision="2" max="9999999999.99" min="0" @change="payPriceChange(scope.$index, 'balance')"></el-input-number>
                            </template>
                        </el-table-column>
                        <el-table-column label="尾款期限（月）" width="110px">
                            <template slot-scope="scope">
                                <el-input-number :disabled="scope.row.paymentMethod == 0 || scope.$index === 0" v-model="scope.row.balanceDueDate" size="mini" :controls="false" :precision="0" max="9999999999" min="0"></el-input-number>
                            </template>
                        </el-table-column>
                        <el-table-column label="发票类型" min-width="220">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.invoiceType" :disabled="scope.$index === 0" placeholder="请选择" size="mini">
                                    <el-option
                                      v-for="item in invoiceTypeList"
                                      :key="item.value"
                                      :label="item.label"
                                      :value="item.value">
                                    </el-option>
                                </el-select>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
            <div class="container-r-footer">
                <el-button type="primary" size="small" @click="submit">保存</el-button>
            </div>
        </div>
    </template>
    <el-dialog
        title="编辑"
        :visible.sync="rateDialogVisible"
        :close-on-click-modal="false"
        custom-class="dialog-form-wrap"
        width="480px"
    >
        <el-form label-width="120px">
            <el-form-item label="加价率" required :class="{'is-error': isRateformError}">
                <div class="rate-wrap">
                    <el-input-number @blur="validRateDialogForm" v-model="rateDialogFormValue" size="mini" :controls="false" :precision="2" max="9999.99"></el-input-number>%
                </div>
                <div class="el-form-item__error" v-if="isRateformError">{{ isRateformError }}</div>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="multiAddRate" size="mini">确 定</el-button>
            <el-button @click="rateDialogVisible = false" size="mini">取 消</el-button>
        </div>
    </el-dialog>
</div>

<script src="${pageContext.request.contextPath}/static/vue/vd-ui/ui.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/flowOrder/purchase_add.js?rnd=${resourceVersionKey}"></script>
