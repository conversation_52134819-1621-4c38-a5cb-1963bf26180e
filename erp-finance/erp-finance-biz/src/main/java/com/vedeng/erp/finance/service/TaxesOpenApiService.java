package com.vedeng.erp.finance.service;

import com.vedeng.erp.finance.domain.dto.*;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.erp.finance.enums.TaxesInterfaceCodeEnum;

/**
 * 税金系统统一入口
 */
public interface TaxesOpenApiService<T extends ITaxesParam,R extends ITaxesResult> {

    /**
     *
     * 销项开票
     * @see SaleInvoiceOpenRequestDto 入参
     * @see SaleInvoiceOpenResponseDto 出参
     *
     * 销项票下载
     * @see SaleInvoiceDownRequestDto 入参
     * @see SaleInvoiceDownResponseDto 出参
     *
     * 开票额度
     * @see SaleInvoiceLimitRequestDto 入参
     * @see SaleInvoiceLimitResponseDto 出参
     *
     * 二维码交付
     * @see SaleInvoiceQrRequestDto 入参
     * @see SaleInvoiceQrResponseDto 出参
     */
    R openapi(T t, TaxesInterfaceCodeEnum interfaceCodeEnum);
}
