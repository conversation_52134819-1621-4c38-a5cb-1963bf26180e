package com.vedeng.api.standard.adapter.saleorder.converter;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vedeng.api.standard.adapter.saleorder.dto.SaleOrderCreateRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 销售订单数据转换器
 * 负责在不同数据格式之间进行转换
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
@Component
public class SaleorderDataConverter {

    private static final Logger logger = LoggerFactory.getLogger(SaleorderDataConverter.class);

    private final ObjectMapper objectMapper;

    public SaleorderDataConverter() {
        this.objectMapper = new ObjectMapper();
        // 配置忽略未知属性，避免上游携带额外字段时转换失败
        this.objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }
    
    /**
     * 通用转换方法
     * 
     * @param source 源数据
     * @param targetClass 目标类型
     * @param <T> 目标类型泛型
     * @return 转换后的对象
     */
    public <T> T convert(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        
        try {
            if (targetClass.isAssignableFrom(source.getClass())) {
                // 如果源对象已经是目标类型，直接返回
                return targetClass.cast(source);
            }
            
            if (source instanceof Map) {
                // 从Map转换
                return convertFromMap((Map<?, ?>) source, targetClass);
            } else {
                // 使用Jackson进行转换
                return objectMapper.convertValue(source, targetClass);
            }
            
        } catch (Exception e) {
            logger.error("数据转换失败: source={}, targetClass={}", 
                source.getClass().getSimpleName(), targetClass.getSimpleName(), e);
            throw new RuntimeException("数据转换失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从Map转换为指定类型
     * 优化：使用策略模式，支持更多类型的专门转换
     */
    @SuppressWarnings("unchecked")
    private <T> T convertFromMap(Map<?, ?> sourceMap, Class<T> targetClass) {
        if (targetClass == SaleOrderCreateRequest.class) {
            return (T) convertToSaleOrderRequest(sourceMap);
        } else {
            // 使用Jackson进行通用转换
            return objectMapper.convertValue(sourceMap, targetClass);
        }
    }

    /**
     * 转换为采购单通用请求
     * 基于统一的 BuyOrderRequest 结构
     */
    private SaleOrderCreateRequest convertToSaleOrderRequest(Map<?, ?> sourceMap) {
        String jsonStr = JSON.toJSONString(sourceMap);
        return JSON.parseObject(jsonStr, SaleOrderCreateRequest.class);
    }


}
