<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="进项票（财务）" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
<style type="text/css">
    .layui-tab-brief > .layui-tab-title .layui-this {
        color: #3384ef;
    }

    .layui-tab-brief > .layui-tab-title .layui-this:after {
        border-bottom: 2px solid #3384ef;
    }
</style>
<div class="layui-tab layui-tab-brief" lay-filter="test" style="height: 100%;margin: 0;padding: 10px 0;">
    <ul class="layui-tab-title">
        <li class="layui-this">全部</li>
        <li>待认领</li>
        <li>费用票</li>
        <li>异常票</li>
        <li>负数票</li>
        <li>无效票</li>
    </ul>
    <div class="layui-tab-content" style="min-height: calc(100% - 40px);display: flex;">
        <iframe style="width: 100%;" frameborder="no" src="./hx_invoice.do?id=0"></iframe>
    </div>
    <span id="viewTraderSpan" onclick="viewTraderInfo(traderId)" style="color: #438DEF" class="addtitle"
          href="javascript:void(0);"><c:out
            value="" escapeXml="true"/></span>
    <span id="viewBuyOrderInfoSpan" style="color: #438DEF" class="addtitle"
          href="javascript:void(0);"><c:out
            value="" escapeXml="true"/></span>
</div>
<script src="/webjars/ezadmin/plugins/layui/layui.js" charset="utf-8"></script>
<script>
    layui.use('element', function () {
        var element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块

        //触发事件
        var active = {
            tabChange: function () {
                //切换到指定Tab项
                element.tabChange('demo', '22'); //切换到：用户管理
            }
        };

        element.on('tab(test)', function (data) {
            $(".layui-tab-content").find("iframe").attr("src", './hx_invoice.do?id=' + data.index);
        });

    });

    /**
     * 查看客户详情信息
     * @param traderId
     */
    function viewTraderInfo(traderId) {
        $('#viewTraderSpan').attr('tabTitle', '{"num":"viewtrader", "link":"/trader/supplier/baseinfo.do?traderId=' + traderId + '","title":"供应商信息"}');
        $('#viewTraderSpan').click();
    }

    /**
     * 导出费用票信息
     */
    function exportCostListInfo() {
        //$('#exportCostListSpan').attr('tabTitle', '{"num":"exportCostList", "link":"http://ezadmin.ivedeng.com/ezlist/list/list.html?pageId=180","title":"导出费用票信息"}');
        //$('#exportCostListSpan').click();
    }

    /**
     * 查看采购订单信息
     */
    function viewBuyOrderInfo(buyOrderId,type) {
        if (type == 503){
            $('#viewBuyOrderInfoSpan').attr('tabTitle', '{"num":"viewbuyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>", "link":"/order/buyorder/viewBuyorder.do?buyorderId=' + buyOrderId + '","title":"订单信息"}');
        } else if (type == 4126){
            $('#viewBuyOrderInfoSpan').attr('tabTitle', '{"num":"viewExpenseOrder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>", "link":"/buyorderExpense/details.do?buyorderExpenseId=' + buyOrderId + '","title":"费用订单详情"}');
        } else {
            $('#viewBuyOrderInfoSpan').attr('tabTitle', '{"num":"viewAfterOrder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>", "link":"/aftersales/order/viewAfterSalesDetail.do?traderType=2&afterSalesId=' + buyOrderId + '","title":"订单信息"}');
        }
        $('#viewBuyOrderInfoSpan').click();
    }
</script>
<%@ include file="../../common/footer.jsp" %>