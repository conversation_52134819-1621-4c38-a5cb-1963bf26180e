package com.newtask.filemove.theadpool;

import com.newtask.filemove.dto.FileMoveDto;
import com.vedeng.common.model.FileInfo;
import com.vedeng.filemove.dao.FileMoveDao;
import com.vedeng.trader.model.TraderCertificate;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.util.List;

/**
 * 客户资质定时任务
 */
public class TraderCertificateFileMoveTask extends FileMoveTask {

    @Resource
    private FileMoveDao fileMoveDao;

    public TraderCertificateFileMoveTask(List<FileMoveDto> fileMoveList) {
        super(fileMoveList);
        // 运行时注入service
        WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
        fileMoveDao = (FileMoveDao) context.getBean("fileMoveDao");
    }

    @Override
    protected void updateErrorInfo(FileMoveDto fileMoveDto) {
        TraderCertificate toUpdateCertificate = new TraderCertificate();
        toUpdateCertificate.setTraderCertificateId(fileMoveDto.getId());
        toUpdateCertificate.setSynSuccess(2);
        fileMoveDao.updateTraderCertificate(toUpdateCertificate);
    }

    @Override
    protected void updateOriginalFileInfo(FileMoveDto fileMoveDto, FileInfo fileInfo) {
        //更新uri、domain、resourceId
        TraderCertificate toUpdateCertificate = new TraderCertificate();
        toUpdateCertificate.setTraderCertificateId(fileMoveDto.getId());
        toUpdateCertificate.setDomain(fileInfo.getHttpUrl());
        toUpdateCertificate.setUri(fileInfo.getFilePath());
        toUpdateCertificate.setOriginalFilepath(fileMoveDto.getDomain() + fileMoveDto.getUri());
        toUpdateCertificate.setSynSuccess(1);
        toUpdateCertificate.setOssResourceId(fileInfo.getOssResourceId());

        if(fileInfo.getPrefix() != null){
            toUpdateCertificate.setCostTime(Long.valueOf(fileInfo.getPrefix()));
        }

        fileMoveDao.updateTraderCertificate(toUpdateCertificate);
    }
}
