package com.vedeng.im.rabbitmq;

import com.rabbitmq.client.Channel;
import com.vedeng.common.constant.TraderConstants;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.crm.api.dto.label.RTraderJLabelDTO;
import com.vedeng.trader.dao.RTraderLabelJTraderMapper;
import com.vedeng.trader.model.RTraderLabelJTrader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Component
public class TraderLabelConsumer extends AbstractMessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(TraderLabelConsumer.class);
    @Autowired
    private RTraderLabelJTraderMapper rTraderLabelJTraderMapper;
    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("客户中心推送客户标签关系 {}",messageBody);
        try{
            RTraderJLabelDTO rTraderJLabelDTO= JsonUtils.readValue(messageBody,RTraderJLabelDTO.class);
            if(rTraderJLabelDTO==null
                    ||rTraderJLabelDTO.getOperateType()==null
                    ||rTraderJLabelDTO.getTraderId()==null
                    || CollectionUtils.isEmpty(rTraderJLabelDTO.getLabelIdList())){
                channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
                return;
            }
            rTraderLabelJTraderMapper.deleteByTraderIdAndLabelIds(rTraderJLabelDTO.getTraderId(),rTraderJLabelDTO.getLabelIdList());
            if(TraderConstants.OPERATE_ADD.equals(rTraderJLabelDTO.getOperateType())){
                for(Integer labelId:rTraderJLabelDTO.getLabelIdList()){
                    if(labelId==null){
                        continue;
                    }
                    RTraderLabelJTrader rj=new RTraderLabelJTrader();
                    rj.setTraderLabelId(labelId);
                    rj.setTraderId(rTraderJLabelDTO.getTraderId());
                    rTraderLabelJTraderMapper.insertSelective(rj);
                }
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
        }catch (Exception ex){
            LOGGER.error("CRM推送客户标签报错",ex);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,false);
            } catch (IOException e) {
                LOGGER.error("CRM消息确认报错，将消息返回给rabbitmq错误：",e);
            }
        }
    }
}
