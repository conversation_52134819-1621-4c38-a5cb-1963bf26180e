package com.vedeng.erp.saleorder.model.po;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 京东商品
    */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VJdGoods {
    /**
    * ID
    */
    private Integer id;

    /**
    * 京东商品ID
    */
    private Long jdGoodsId;

    /**
    * 贝登订货号
    */
    private String vdSkuNo;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 创建者
    */
    private Integer creator;

    /**
    * 修改者
    */
    private Integer updater;

    /**
    * 是否禁用
    */
    private Integer deleteFlag;
}