package com.vedeng.erp.settlement.mapper;

import com.vedeng.erp.finance.dto.PayApplyCreateBillDto;
import com.vedeng.erp.settlement.domain.entity.PayApplyEntity;
import java.util.List;

import com.vedeng.erp.finance.dto.PayApplyDto;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

@Named("newPayApplyMapper")
public interface PayApplyMapper {
    /**
     * delete by primary key
     * @param payApplyId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer payApplyId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(PayApplyEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(PayApplyEntity record);

    /**
     * select by primary key
     * @param payApplyId primary key
     * @return object by primary key
     */
    PayApplyEntity selectByPrimaryKey(Integer payApplyId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(PayApplyEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(PayApplyEntity record);

    int batchInsert(@Param("list") List<PayApplyEntity> list);


    /**
     * 根据入参查询
     *
     * @param payApplyDto
     * @return
     */
    List<PayApplyDto> selectByPayTypeAndRelatedIdAndOtherParam(PayApplyDto payApplyDto);

    /**
     * <AUTHOR>
     * @desc 根据付款申请id查询付款申请
     * @param payApplyId
     * @return
     */
    PayApplyDto queryInfoByPayApplyId(Integer payApplyId);

    PayApplyDto getPayApplyMaxRecord(Integer relatedId);

    Integer getTraderSupplierIdByTraderName(String traderName);

    /**
     * 更新往来单位类型(0-客户,1-供应商)
     * @param updatedAccountType
     * @param payApplyId
     * @return
     */
    int updateAccountTypeByPayApplyId(@Param("updatedAccountType")Integer updatedAccountType,@Param("payApplyId")Integer payApplyId);

    List<PayApplyEntity> queryByAll(PayApplyCreateBillDto dto);

    void updateAutoBill(@Param("payApplyId") Integer payApplyId, @Param("autoBill") Integer autoBill);

    // 获取当前审批人T_VERIFIES_INFO
    String getVerifyInfoUserNames(@Param("payApplyId") Integer payApplyId);

    PayApplyDto getByPayTypeAndRelatedIdLast(@Param("payType") Integer payType, @Param("relatedId") Integer relatedId);

    /**
     * 获取已付款的付款申请
     * @param payType
     * @param relatedId
     * @return
     */
    List<PayApplyDto> findByPayTypeAndRelatedId(@Param("payType")Integer payType,@Param("relatedId")Integer relatedId);

    /**
     * 根据付款类型和付款单id查询付款申请列表
     * @param payType
     * @param relatedId
     * @return
     */
    List<PayApplyDto> getListByPayTypeAndRelatedId(@Param("payType")Integer payType,@Param("relatedId")Integer relatedId);

    List<PayApplyDto> queryPayApplyByIds(@Param("payApplyIds") List<Integer> payApplyIds);

    PayApplyDto findByRelatedIdAndPayType(@Param("relatedId")Integer relatedId,@Param("payType")Integer payType);


    /**
     * 根据付款申请状态查询付款申请
     * @return
     */
    List<PayApplyDto> findAllByValidStatusAndPayStatus();
}
