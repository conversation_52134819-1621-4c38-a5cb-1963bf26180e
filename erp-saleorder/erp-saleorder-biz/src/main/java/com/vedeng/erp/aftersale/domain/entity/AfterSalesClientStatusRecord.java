package com.vedeng.erp.aftersale.domain.entity;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户端售后状态记录表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AfterSalesClientStatusRecord {
    /**
    * 主键
    */
    private Long clientStatusRecordId;

    /**
    * 售后主表ID
    */
    private Integer afterSalesId;

    /**
    * 推送状态：1下派工程师 2工程师完成预约 3工程师已完工
    */
    private Integer pushClientStatus;

    /**
    * 推送时间
    */
    private Date pushTime;

    /**
    * 推送内容
    */
    private String message;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 添加人ID
    */
    private Integer creator;

    /**
    * 添加人名称
    */
    private String creatorName;

    /**
    * 更新人ID
    */
    private Integer updater;

    /**
    * 更新人名称
    */
    private String updaterName;

    /**
    * 更新备注
    */
    private String updateRemark;
}