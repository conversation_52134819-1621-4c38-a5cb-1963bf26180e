
Vue.component('ui-radio-group', {
    template: `
        <div class="vd-ui-radio-group">
            <template v-for="(item, index) in boxList">
                <ui-radio
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :tip="item.tip"
                    :checked.sync="item.checked"
                    :disabled="disabled || item.disabled"
                    :clearable="clearable"
                    @change="handlerChange(index, $event)"
                ></ui-radio>
            </template>
            <div class="vd-ui-input-error" v-if="errorable">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,

    watch: {
        list: {
            handler () {
                this.setList();
            },
            deep: true,
            immediate: true
        },
        value (newV) {
            this.setList();
        },
    },

    data() {
        return {
            boxList: [],
        };
    },
    props: {
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        value: "",
        clearable: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
    },
    mounted() {
        this.$form.setValidEl(this);
        this.setList();
    },
    methods: {
        setList() {
            this.boxList = JSON.parse(JSON.stringify(this.list));

            this.boxList.forEach((item) => {
                if (item.value == this.value) {
                    item.checked = true;
                }
            });
        },
        handlerChange(index, data) {
            let valueItem = null;
            this.boxList.forEach((item, i) => {
                if(index === i) {
                    this.$set(this.boxList[i], "checked", data);
                } else {
                    this.$set(this.boxList[i], "checked", false);
                }
                if (item.checked) {
                    valueItem = item;
                }
            });

            let value = valueItem ? valueItem.value : '';

            if (value !== this.value) {
                this.checkValid(value);
                this.$emit("update:value", value);
                this.$emit("change", valueItem);
            }
        },
        checkValid(newValue) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, newValue, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        }
    }
})
