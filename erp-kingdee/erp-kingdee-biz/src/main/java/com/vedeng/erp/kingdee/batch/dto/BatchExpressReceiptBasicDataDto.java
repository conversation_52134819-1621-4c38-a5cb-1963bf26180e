package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 快递签收基础数据表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BatchExpressReceiptBasicDataDto extends BatchBaseDto {
    /**
     * 主键ID
     */
    private Integer expressReceiptBasicDataId;

    /**
     * 基础数据唯一键
     */
    private String uniqueKey;

    /**
     * 销售单号
     */
    private String saleorderNo;

    /**
     * 出库单号
     */
    private String outInNo;

    /**
     * 是否费用单（0不是，1是）
     */
    private Integer isExpense;

    /**
     * 承运商
     */
    private String logistics;

    /**
     * 快递主键ID
     */
    private Integer expressId;

    /**
     * 收件地址
     */
    private String takeTraderAddress;

    /**
     * 收件人
     */
    private String takeTraderContactName;

    /**
     * 收件人电话
     */
    private String takeTraderContactTelephone;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 数据类型（ 0实物普发，1实物直发，2纯虚拟商品销售订单，3混虚拟商品销售订单）
     */
    private Integer dataType;

    /**
     * 发货数量
     */
    private Integer num;

    /**
     * 订货号
     */
    private String sku;

    /**
     * 批次号
     */
    private String batchNumber;

    /**
     * SN码
     */
    private String barcodeFactory;

    /**
     * 订单首次付款时间
     */
    private Date firstTraderTime;

    /**
     * 签收时间
     */
    private Date arrivalTime;

    /**
     * 是否赠品（0否，1是）
     */
    private Integer isGift;

    /**
     * WMS单号
     */
    private String wmsNo;

    /**
     * 原基础数据唯一键
     */
    private String oldUniqueKey;

    /**
     * 签收计算标记（1新增，2更新，4删除）
     */
    private Integer arrivalActFlag;

    /**
     * 计算时间
     */
    private Date etlTime;

    /**
     * 是否推送金蝶（0未推送，1已推送，2无需推送）
     */
    private Integer isPushKingdee;

    /**
     * 备注
     */
    private String remark;



    /**
     * 最大时间
     */
    private Date beginTime;

    /**
     * 最小时间
     */
    private Date endTime;

    /**
     * 签收计算标记集合
     */
    private List<Integer> arrivalActFlagList;
}