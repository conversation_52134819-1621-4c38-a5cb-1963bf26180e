package com.vedeng.flash.controller;

import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.dao.RoleMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.constant.goods.LogTypeEnum;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.aftersale.service.BuyorderExpenseItemApiService;
import com.vedeng.erp.buyorder.dto.BuyOrderRebateApplyDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyOrderRebateApplyApiService;
import com.vedeng.flash.constant.FlashConstant;
import com.vedeng.flash.dto.ProcessResultDto;
import com.vedeng.flash.dto.ReviewQueryDto;
import com.vedeng.flash.dto.vo.ReviewAndMessageVO;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.dao.FirstEngageGenerateMapper;
import com.vedeng.goods.dao.LogCheckGenerateMapper;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.goods.model.CoreSpu;
import com.vedeng.goods.model.LogCheckGenerate;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderModifyApply;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderModifyApply;
import com.vedeng.order.model.po.OrderReviewProcessPo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.model.vo.FirstEngageInfoDto;
import com.vedeng.order.service.OrderReviewProcessService;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.model.Trader;
import com.wms.service.context.ThreadLocalContext;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.*;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.runtime.ProcessInstanceQuery;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping("review/workbench")
public class ReviewAndMessageController {

    private static final Logger logger = LoggerFactory.getLogger(ReviewAndMessageController.class);

    //查看类型集合(手写版)
    //V_LOG_CHECK(1-spu,2-sku,3-注册证)
    private static final List<String> selfSpeciesList = Arrays.asList("注册证审核", "SPU审核", "SKU审核");

    //查看类型集合(activity版)
    private static final List<String> activitySpeciesList = Arrays.asList("售后审核", "订单审核", "备货审核", "供应商审核", "订单修改","合同回传","供应商返利结算");
    public static final int ONE = 1;

    @Autowired
    UserService userService;

    @Autowired
    OrgService orgService;

    @Autowired
    OrganizationMapper organizationMapper;

    @Resource
    LogCheckGenerateMapper logCheckGenerateMapper;

    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Resource
    private CoreSpuMapper coreSpuMapper;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Resource
    private FirstEngageGenerateMapper firstEngageGenerateMapper;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private TraderCustomerMapper traderCustomerMapper;

    @Autowired
    private OrderReviewProcessService orderReviewProcessService;

    @Autowired
    private BuyorderExpenseItemApiService buyorderExpenseItemApiService;

    @Autowired
    private BuyOrderRebateApplyApiService buyOrderRebateApplyApiService;

    /**
     * 供应链工作台审批页面
     *
     * @param reviewQueryDto
     * @param request
     * @param session
     * @param pageNo
     * @param pageSize
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/5/25 17:27.
     * @author: Randy.Xu.
     * @return: org.springframework.web.servlet.ModelAndView.
     * @throws: .
     */
    @RequestMapping(value = "remindReview")
    public ModelAndView getRemindReview(ReviewQueryDto reviewQueryDto, HttpServletRequest request, HttpSession session,
                                        @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                        @RequestParam(required = false) Integer pageSize) {
        String path = request.getRequestURL().toString();
        path = path + "?";
        if (reviewQueryDto.getCheckSpecies() != null && !"".equals(reviewQueryDto.getCheckSpecies())) {
            path = path + "checkSpecies=" + reviewQueryDto.getCheckSpecies() + "&";
        }
        if (reviewQueryDto.getSubmitEndTime() != null && !"".equals(reviewQueryDto.getSubmitEndTime())) {
                path = path + "submitEndTime=" + reviewQueryDto.getSubmitEndTime() + "&";
        }
        if (reviewQueryDto.getSubmitStartTime() != null && !"".equals(reviewQueryDto.getSubmitStartTime())) {
            path = path + "submitStartTime=" + reviewQueryDto.getSubmitStartTime() + "&";
        }
        if (reviewQueryDto.getTabStatus() != null) {
            path = path + "tabStatus=" + reviewQueryDto.getTabStatus() + "&";
        }
        path = path.substring(0,path.length()-1);

        ModelAndView mav = new ModelAndView();
        mav.setViewName("flash/workbench/review_page");
        User currentUser = (User) session.getAttribute(ErpConst.CURR_USER);

        ThreadLocalContext.put("userInfo", currentUser);

        List<String> businessKeys = orderReviewProcessService.getAllOrderReviewProcess().stream().map(OrderReviewProcessPo::getBusinessKey).collect(Collectors.toList());

        // 结果集对象初始化
        List<LogCheckGenerate> UnfinishLogCheckGenerateList = new ArrayList<>();
        List<LogCheckGenerate> UnfinishLogCheckGenerateListForSubmit = new ArrayList<>();
        List<HistoricProcessInstance> processInstanceList = new ArrayList<>();
        //最终结果级
        List<ReviewAndMessageVO> reviewAndMessageVOList = new ArrayList<>();
        Page page = Page.newBuilder(pageNo, pageSize, path);
        // 创建历史实例查询
        HistoricProcessInstanceQuery historyProcessQueryForFilter = processEngine.getHistoryService().createHistoricProcessInstanceQuery();
        TaskService taskService = processEngine.getTaskService();


        ProcessInstanceQuery processQuery = processEngine.getRuntimeService().createProcessInstanceQuery();

        // 任务相关service
        // 创建历史实例查询
        HistoricActivityInstanceQuery historyAIQuery = processEngine.getHistoryService()
                .createHistoricActivityInstanceQuery();
        TaskQuery taskInstanceQuery = taskService.createTaskQuery();


        List<String> speciesList = new ArrayList<>();
        speciesList.addAll(selfSpeciesList);
        speciesList.addAll(activitySpeciesList);
        mav.addObject("speciesList", speciesList);
        Set<String> processDefinitionKeySet = new HashSet<>();
        List<Integer> selfReviewTypeList = new ArrayList<>();
        List<Integer> selfReviewTypeListForSubmit = new ArrayList<>();
        //查看类型过滤
        if (reviewQueryDto.getCheckSpecies() == null || reviewQueryDto.getCheckSpecies() == "") {
            for (String species : speciesList) {
                if (selfSpeciesList.contains(species)) {
                    dealWithSelfSpecies(species, currentUser, selfReviewTypeList);
                    dealWithSelfSpeciesForSubmit(species,selfReviewTypeListForSubmit);
                }
                if (activitySpeciesList.contains(species)) {
                    dealWithactivitySpecies(species, processDefinitionKeySet);
                }
            }

        } else {
            if (selfSpeciesList.contains(reviewQueryDto.getCheckSpecies())) {
                dealWithSelfSpecies(reviewQueryDto.getCheckSpecies(), currentUser, selfReviewTypeList);
                dealWithSelfSpeciesForSubmit(reviewQueryDto.getCheckSpecies(),selfReviewTypeListForSubmit);
            }
            if (activitySpeciesList.contains(reviewQueryDto.getCheckSpecies())) {
                dealWithactivitySpecies(reviewQueryDto.getCheckSpecies(), processDefinitionKeySet);
            }
        }


        //自有审核流
        if (CollectionUtils.isNotEmpty(selfReviewTypeList)) {
            UnfinishLogCheckGenerateList = logCheckGenerateMapper.getAllUnfinishProcess(selfReviewTypeList, reviewQueryDto);

        }
        if (CollectionUtils.isNotEmpty(selfReviewTypeListForSubmit)) {
            UnfinishLogCheckGenerateListForSubmit = logCheckGenerateMapper.getAllUnfinishProcess(selfReviewTypeListForSubmit, reviewQueryDto);

        }

        //待审核
        if (reviewQueryDto.getTabStatus() == null || FlashConstant.REMINED_REVIEW.equals(reviewQueryDto.getTabStatus())) {
            taskInstanceQuery.taskCandidateOrAssigned(currentUser.getUsername());
            ProcessResultDto activityProcess = getActivityProcessForRemind(processQuery, taskInstanceQuery, processDefinitionKeySet, reviewQueryDto,historyProcessQueryForFilter);
            if (CollectionUtils.isNotEmpty(UnfinishLogCheckGenerateList)) {
                for (LogCheckGenerate logCheckGenerate : UnfinishLogCheckGenerateList) {
                    ReviewAndMessageVO reviewAndMessageVO = new ReviewAndMessageVO();
                    resolveSelfTypeForStatus(logCheckGenerate, reviewAndMessageVO, FlashConstant.REMINED_REVIEW);
                    reviewAndMessageVOList.add(reviewAndMessageVO);
                }
            }
            if (CollectionUtils.isNotEmpty(activityProcess.getHistoricProcessInstanceList())) {
                for (HistoricProcessInstance historicProcessInstance : activityProcess.getHistoricProcessInstanceList()) {
                    if (CollectionUtils.isNotEmpty(businessKeys) && businessKeys.contains(historicProcessInstance.getBusinessKey())){
                        continue;
                    }
                    ReviewAndMessageVO reviewAndMessageVO = new ReviewAndMessageVO();
                    try {
                        resolveActivityTypeForStatus(historicProcessInstance, reviewAndMessageVO, FlashConstant.REMINED_REVIEW, activityProcess, taskService);
                    } catch (Exception e) {
                        logger.error("resolveActivityTypeForStatus error",e);
                    }
                    reviewAndMessageVOList.add(reviewAndMessageVO);
                }
            }
        }

        //发起的在途
        if (FlashConstant.SUBMMIT_UNFINISH.equals(reviewQueryDto.getTabStatus())) {
            //发起者过滤
            if (CollectionUtils.isNotEmpty(UnfinishLogCheckGenerateListForSubmit)) {
                UnfinishLogCheckGenerateListForSubmit = UnfinishLogCheckGenerateListForSubmit.stream().filter(e -> e.getCreator().equals(currentUser.getUserId())).collect(Collectors.toList());
            }
            ProcessResultDto activityProcess = getActivityProcessForSubmitUnfinish(processQuery, taskInstanceQuery, processDefinitionKeySet,reviewQueryDto,historyProcessQueryForFilter,currentUser,historyAIQuery);
            //处理
            if (CollectionUtils.isNotEmpty(UnfinishLogCheckGenerateListForSubmit)) {
                for (LogCheckGenerate logCheckGenerate : UnfinishLogCheckGenerateListForSubmit) {
                    ReviewAndMessageVO reviewAndMessageVO = new ReviewAndMessageVO();
                    resolveSelfTypeForStatus(logCheckGenerate, reviewAndMessageVO, FlashConstant.SUBMMIT_UNFINISH);
                    reviewAndMessageVOList.add(reviewAndMessageVO);
                }
            }
            if (CollectionUtils.isNotEmpty(activityProcess.getHistoricProcessInstanceList())) {
                for (HistoricProcessInstance historicProcessInstance : activityProcess.getHistoricProcessInstanceList()) {
                    if (CollectionUtils.isNotEmpty(businessKeys) && businessKeys.contains(historicProcessInstance.getBusinessKey())){
                        continue;
                    }
                    ReviewAndMessageVO reviewAndMessageVO = new ReviewAndMessageVO();
                    try {
                        resolveActivityTypeForStatus(historicProcessInstance, reviewAndMessageVO, FlashConstant.SUBMMIT_UNFINISH, activityProcess, taskService);
                        reviewAndMessageVOList.add(reviewAndMessageVO);
                    } catch (Exception e) {
                        String message = e.getMessage();
                        logger.error("resolveActivityTypeForStatus error",e);
                    }
                }
            }

        }
        if (CollectionUtils.isEmpty(reviewAndMessageVOList)) {
            mav.addObject("review", reviewQueryDto);
            return mav;
        }
        reviewAndMessageVOList = reviewAndMessageVOList.stream().sorted(Comparator.comparing(ReviewAndMessageVO::getSubmitTimeSort, Comparator.nullsLast(Long::compareTo))).collect(Collectors.toList());
        // 分页对象

        int dataTotal = reviewAndMessageVOList.size();
        page.setTotalRecord(dataTotal);
        page.setTotalPage((int) Math.ceil(((double) dataTotal) / page.getPageSize()));
        Integer start = (page.getPageNo() - 1) * page.getPageSize();
        reviewAndMessageVOList = reviewAndMessageVOList.subList(start, (start + page.getPageSize()) > dataTotal ? dataTotal : (start + page.getPageSize()));
        // 根据批量实例ID查找



        mav.addObject("reviewList", reviewAndMessageVOList);
        mav.addObject("review", reviewQueryDto);
        mav.addObject("page", page);
        return mav;
    }

    private void dealWithSelfSpeciesForSubmit(String species, List<Integer> selfReviewTypeListForSubmit) {
        if ("SPU审核".equals(species)) {
            selfReviewTypeListForSubmit.add(LogTypeEnum.SPU.getLogType());
        }
        if ("SKU审核".equals(species) ) {
            selfReviewTypeListForSubmit.add(LogTypeEnum.SKU.getLogType());
        }
        if ("注册证审核".equals(species) ) {
            selfReviewTypeListForSubmit.add(LogTypeEnum.FIRSTENGAGE.getLogType());
        }

    }


    //获得待审核
    private ProcessResultDto getActivityProcessForRemind(ProcessInstanceQuery processQuery, TaskQuery taskInstanceQuery, Set<String> processDefinitionKeySet,ReviewQueryDto reviewQueryDto,
                                                HistoricProcessInstanceQuery historyProcessQueryForFilter) {
        ProcessResultDto processResultDto = new ProcessResultDto();
        // 用户任务
        List<Task> taskInstanceList = taskInstanceQuery.list();
        Set<String> processInstanceIds = new HashSet<>();
        for (Task taskInstance : taskInstanceList) {
            processInstanceIds.add(taskInstance.getProcessInstanceId());
        }

        if (CollectionUtils.isNotEmpty(processDefinitionKeySet) && CollectionUtils.isNotEmpty(processInstanceIds)) {
            processQuery.processDefinitionKeys(processDefinitionKeySet);
            processQuery.processInstanceIds(processInstanceIds);
            List<ProcessInstance> processInstanceList = processQuery.list();
            processResultDto.setProcessInstanceList(processInstanceList);
        } else {
            processResultDto.setProcessInstanceList(new ArrayList<>());
        }
        if(CollectionUtils.isNotEmpty(processResultDto.getProcessInstanceList())){
            Set<String> instanceIds = new HashSet<>();
            for (ProcessInstance processInstance : processResultDto.getProcessInstanceList()) {
                instanceIds.add(processInstance.getProcessInstanceId());
            }
            historyProcessQueryForFilter.processInstanceIds(instanceIds);
            List<HistoricProcessInstance> list = historyProcessQueryForFilter.list();
            if(CollectionUtils.isNotEmpty(list)){
                list = list.stream().filter(o->filterTime(o,reviewQueryDto)).collect(Collectors.toList());
            }
            processResultDto.setHistoricProcessInstanceList(list);
            processResultDto.setHistoricProcessInstanceList(list);
        }
        processResultDto.setTaskInstanceList(taskInstanceList);


        return processResultDto;
    }

    //获得提交未审核
    private ProcessResultDto getActivityProcessForSubmitUnfinish(ProcessInstanceQuery processQuery, TaskQuery taskInstanceQuery, Set<String> processDefinitionKeySet,ReviewQueryDto reviewQueryDto,
                                                         HistoricProcessInstanceQuery historyProcessQueryForFilter,User currentUser,HistoricActivityInstanceQuery historyAIQuery) {
        ProcessResultDto processResultDto = new ProcessResultDto();
        // 用户任务
        List<Task> taskInstanceList = taskInstanceQuery.list();
        Set<String> processInstanceIds = new HashSet<>();
        for (Task taskInstance : taskInstanceList) {
            processInstanceIds.add(taskInstance.getProcessInstanceId());
        }

        if (CollectionUtils.isNotEmpty(processDefinitionKeySet) && CollectionUtils.isNotEmpty(processInstanceIds)) {
            processQuery.processDefinitionKeys(processDefinitionKeySet);
            processQuery.processInstanceIds(processInstanceIds);
            List<ProcessInstance> processInstanceList = processQuery.list();
            processResultDto.setProcessInstanceList(processInstanceList);
        } else {
            processResultDto.setProcessInstanceList(new ArrayList<>());
        }



        if(CollectionUtils.isNotEmpty(processResultDto.getProcessInstanceList())){
            processResultDto.setProcessInstanceList(processResultDto.getProcessInstanceList().stream().filter(e->filterStarter(currentUser,e,historyAIQuery)).collect(Collectors.toList()));
            if(CollectionUtils.isNotEmpty(processResultDto.getProcessInstanceList())){
                Set<String> instanceIds = new HashSet<>();
                for (ProcessInstance processInstance : processResultDto.getProcessInstanceList()) {
                    instanceIds.add(processInstance.getProcessInstanceId());
                }
                historyProcessQueryForFilter.processInstanceIds(instanceIds);
                List<HistoricProcessInstance> list = historyProcessQueryForFilter.list();
                if(CollectionUtils.isNotEmpty(list)){
                   list = list.stream().filter(o->filterTime(o,reviewQueryDto)).collect(Collectors.toList());
                }
                processResultDto.setHistoricProcessInstanceList(list);
            }
        }
        processResultDto.setTaskInstanceList(taskInstanceList);


        return processResultDto;
    }

    private boolean filterTime(HistoricProcessInstance o, ReviewQueryDto reviewQueryDto) {
        //提交时间过滤
        Boolean startFlag = true;
        Boolean endFlag = true;
        if (reviewQueryDto.getSubmitStartTime() != null && reviewQueryDto.getSubmitStartTime() != "") {
            startFlag = o.getStartTime().compareTo(DateUtil.StringToDate(reviewQueryDto.getSubmitStartTime())) > 0 ? true:false;

        }
        if (reviewQueryDto.getSubmitEndTime() != null && reviewQueryDto.getSubmitEndTime() != "") {
            endFlag = o.getStartTime().compareTo(DateUtil.StringToDate(reviewQueryDto.getSubmitEndTime())) < 0 ? true:false;
        }
        return startFlag && endFlag;
    }

    /**
     * 处理activity审核流
     *
     * @param species
     * @param
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/5/25 17:24.
     * @author: Randy.Xu.
     * @return: void.
     * @throws: .
     */
    private void dealWithactivitySpecies(String species, Set<String> processDefinitionKeySet) {
        if ("售后审核".equals(species)) {
            processDefinitionKeySet.add("afterSalesVerify");
            processDefinitionKeySet.add("overBuyorderAfterSalesVerify");
        }
        if ("订单审核".equals(species)) {
            processDefinitionKeySet.add("buyorderVerify");
            processDefinitionKeySet.add("buyorderExpenseVerify");
            processDefinitionKeySet.add("buyorderVerify_HC");
            processDefinitionKeySet.add("earlyBuyorderVerify");
        }
        if ("备货审核".equals(species)) {
            processDefinitionKeySet.add("bhSaleorderVerify");
        }
        if ("供应商审核".equals(species)) {
            processDefinitionKeySet.add("traderCustomerVerify");
        }
        if ("订单修改".equals(species)) {
            processDefinitionKeySet.add("editSaleorderVerify");
            processDefinitionKeySet.add("editBuyorderVerify");
            processDefinitionKeySet.add("saleorderModifyAudit");
        }
        if ("合同回传".equals(species)){
            processDefinitionKeySet.add("purchaseContractVerify");
        }
        if ("供应商返利结算".equals(species)){
            processDefinitionKeySet.add("rebateChargeVerify");
        }
    }

    /**
     * 处理手写的审核流
     *
     * @param species
     * @param currentUser
     * @param selfReviewTypeList
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/5/25 17:23.
     * @author: Randy.Xu.
     * @return: void.
     * @throws: .
     */
    private void dealWithSelfSpecies(String species, User currentUser, List<Integer> selfReviewTypeList) {
        if ("SPU审核".equals(species) && currentUser.hasRole(GoodsConstants.GOODS_EDIT_ROLE)) {
            selfReviewTypeList.add(LogTypeEnum.SPU.getLogType());
        }
        if ("SKU审核".equals(species) && currentUser.hasRole(GoodsConstants.GOODS_EDIT_ROLE)) {
            selfReviewTypeList.add(LogTypeEnum.SKU.getLogType());
        }
        if ("注册证审核".equals(species) && currentUser.hasRole(GoodsConstants.GOODS_CHECK_ROLE)) {
            selfReviewTypeList.add(LogTypeEnum.FIRSTENGAGE.getLogType());
        }
    }

    private void resolveSelfTypeForStatus(LogCheckGenerate logCheckGenerate, ReviewAndMessageVO reviewAndMessageVO, Integer status) {
        if (logCheckGenerate.getLogType().equals(LogTypeEnum.SPU.getLogType())) {
            reviewAndMessageVO.setCheckSpecies("SPU审核");
            reviewAndMessageVO.setCheckUrl("./goods/vgoods/viewSpu.do?spuId=" + logCheckGenerate.getLogBizId());
            reviewAndMessageVO.setProcessNo("E" + logCheckGenerate.getLogBizId());
            CoreSpu spu = coreSpuMapper.getSpuinfoById(logCheckGenerate.getLogBizId());
            if (FlashConstant.REMINED_REVIEW.equals(status)) {
                reviewAndMessageVO.setRemindMessage("SPU信息【" + spu.getSpuNo() + "】需要您审核");
                reviewAndMessageVO.setSubmitUser(logCheckGenerate.getCreatorName());
            } else if (FlashConstant.SUBMMIT_UNFINISH.equals(status)) {
                reviewAndMessageVO.setRemindMessage("SPU信息【" + spu.getSpuNo() + "】已提交审核");
                String[] split = GoodsConstants.GOODS_EDIT_ROLE.split(";");
                List<String> roleList = Arrays.asList(split);
                List<User> userList = roleMapper.getUserInfoByRoleNameList(roleList);
                if (CollectionUtils.isNotEmpty(userList)) {
                    List<String> collect = userList.stream().map(User::getUsername).collect(Collectors.toList());
                    String reviewUserList = String.join(",", collect);
                    reviewAndMessageVO.setReviewUser(reviewUserList);
                }
            }
            reviewAndMessageVO.setSubmitTimeSort(logCheckGenerate.getAddTime().getTime());
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(logCheckGenerate.getAddTime().getTime(), DateUtil.TIME_FORMAT));
        }
        if (logCheckGenerate.getLogType().equals(LogTypeEnum.SKU.getLogType())) {
            reviewAndMessageVO.setCheckSpecies("SKU审核");
            reviewAndMessageVO.setCheckUrl("./goods/vgoods/viewSku.do?pageType=0&skuId=" + logCheckGenerate.getLogBizId());
            reviewAndMessageVO.setProcessNo("E" + logCheckGenerate.getLogBizId());
            CoreSku sku = coreSkuMapper.getSkuinfoById(logCheckGenerate.getLogBizId());
            if(Objects.isNull(sku)){
                sku = new CoreSku();
            }
            if (FlashConstant.REMINED_REVIEW.equals(status)) {
                reviewAndMessageVO.setRemindMessage("SkU信息【" + sku.getSkuNo() + "】需要您审核");
                reviewAndMessageVO.setSubmitUser(logCheckGenerate.getCreatorName());
            } else if (FlashConstant.SUBMMIT_UNFINISH.equals(status)) {
                reviewAndMessageVO.setRemindMessage("SkU信息【" + sku.getSkuNo() + "】已提交审核");
                String[] split = GoodsConstants.GOODS_EDIT_ROLE.split(";");
                List<String> roleList = Arrays.asList(split);
                List<User> userList = roleMapper.getUserInfoByRoleNameList(roleList);
                if (CollectionUtils.isNotEmpty(userList)) {
                    List<String> collect = userList.stream().map(User::getUsername).collect(Collectors.toList());
                    String reviewUserList = String.join(",", collect);
                    reviewAndMessageVO.setReviewUser(reviewUserList);
                }
            }
            reviewAndMessageVO.setSubmitTimeSort(logCheckGenerate.getAddTime().getTime());
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(logCheckGenerate.getAddTime().getTime(), DateUtil.TIME_FORMAT));
        }
        if (logCheckGenerate.getLogType().equals(LogTypeEnum.FIRSTENGAGE.getLogType())) {
            reviewAndMessageVO.setCheckSpecies("注册证审核");
            reviewAndMessageVO.setCheckUrl("./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=" + logCheckGenerate.getLogBizId());
            reviewAndMessageVO.setProcessNo("E" + logCheckGenerate.getLogBizId());
            FirstEngageInfoDto infoByFirstEngageId = firstEngageGenerateMapper.getInfoByFirstEngageId(logCheckGenerate.getLogBizId());
            if (FlashConstant.REMINED_REVIEW.equals(status)) {
                reviewAndMessageVO.setRemindMessage("注册证信息【" + infoByFirstEngageId.getRegistrationNumber() + "】需要您审核");
                reviewAndMessageVO.setSubmitUser(logCheckGenerate.getCreatorName());
            } else if (FlashConstant.SUBMMIT_UNFINISH.equals(status)) {
                reviewAndMessageVO.setRemindMessage("注册证信息【" + infoByFirstEngageId.getRegistrationNumber() + "】已提交审核");
                String[] split = GoodsConstants.GOODS_CHECK_ROLE.split(";");
                List<String> roleList = Arrays.asList(split);
                List<User> userList = roleMapper.getUserInfoByRoleNameList(roleList);
                if (CollectionUtils.isNotEmpty(userList)) {
                    List<String> collect = userList.stream().map(User::getUsername).collect(Collectors.toList());
                    String reviewUserList = String.join(",", collect);
                    reviewAndMessageVO.setReviewUser(reviewUserList);
                }
            }
            reviewAndMessageVO.setSubmitTimeSort(logCheckGenerate.getAddTime().getTime());
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(logCheckGenerate.getAddTime().getTime(), DateUtil.TIME_FORMAT));

        }

    }

    private void resolveActivityTypeForStatus(HistoricProcessInstance historicProcessInstance, ReviewAndMessageVO reviewAndMessageVO, Integer type, ProcessResultDto processResultDto, TaskService taskService) {
        if ("buyorderExpenseVerify".equals(historicProcessInstance.getProcessDefinitionKey())) {
            //采购费用订单
            String businessKey = historicProcessInstance.getBusinessKey();
            //id:buyorderExpenseId
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            BuyorderExpenseDto buyOrderExpense = buyorderExpenseItemApiService.getBuyOrderExpenseById(Integer.valueOf(id));
            String buyOrderExpenseNo = buyOrderExpense.getBuyorderExpenseNo();
            //非直属采购费用订单
            if (StringUtils.isNotBlank(buyOrderExpenseNo) && ONE == buyOrderExpense.getOrderType()) {
                //类型
                reviewAndMessageVO.setCheckSpecies("订单审核");
                //详情地址
                reviewAndMessageVO.setCheckUrl("./buyorderExpense/details.do?buyorderExpenseId=" + id);
                //提交时间：取流程提交时间 精确到时分秒
                reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
               //提交人取流程创建人
                reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
                //代办内容
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    //待审批
                    reviewAndMessageVO.setRemindMessage("采购费用单【" + buyOrderExpenseNo + "】需要您审核");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    //我的申请
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("采购费用单【" + buyOrderExpenseNo + "】已提交审核");
                }
                //提交人
                reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
                reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
                //流程单号
                reviewAndMessageVO.setProcessNo("E" + id);
            }
        }
        if ("afterSalesVerify".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            reviewAndMessageVO.setCheckSpecies("售后审核");
            reviewAndMessageVO.setCheckUrl("./aftersales/order/viewAfterSalesDetail.do?afterSalesId=" + id + "&traderType=2");
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            AfterSales afterSalesById = afterSalesMapper.getAfterSalesById(Integer.valueOf(id));
            if (afterSalesById != null) {
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("售后申请【" + afterSalesById.getAfterSalesNo() + "】需要您审核");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("售后申请【" + afterSalesById.getAfterSalesNo() + "】已提交审核");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }
        if ("overBuyorderAfterSalesVerify".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            reviewAndMessageVO.setCheckSpecies("售后审核");
            reviewAndMessageVO.setCheckUrl("./order/newBuyorder/viewAfterSalesDetail.do?afterSalesId=" + id + "&traderType=2");
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            AfterSales afterSalesById = afterSalesMapper.getAfterSalesById(Integer.valueOf(id));
            if (afterSalesById != null) {
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("售后申请【" + afterSalesById.getAfterSalesNo() + "】需要您审核");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("售后申请【" + afterSalesById.getAfterSalesNo() + "】已提交审核");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }
        if ("buyorderVerify".equals(historicProcessInstance.getProcessDefinitionKey()) || "buyorderVerify_HC".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.lastIndexOf("_") + 1);
            reviewAndMessageVO.setCheckSpecies("订单审核");
            reviewAndMessageVO.setCheckUrl("./order/buyorder/viewBuyorder.do?buyorderId=" + id);
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            BuyorderVo buyorderVo = buyorderMapper.getBuyorderVoById(Integer.valueOf(id));
            if (buyorderVo != null) {
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("采购单【" + buyorderVo.getBuyorderNo() + "】需要您审核");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("采购单【" + buyorderVo.getBuyorderNo() + "】已提交审核");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }
        if ("earlyBuyorderVerify".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            reviewAndMessageVO.setCheckSpecies("订单审核");
            reviewAndMessageVO.setCheckUrl("./order/saleorder/view.do?saleorderId=" + id);
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            Saleorder saleOrderById = saleorderMapper.getSaleOrderById(Integer.valueOf(id));
            if (saleOrderById != null) {
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("销售单【" + saleOrderById.getSaleorderNo() + "】提前采购需要您审核");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("销售单【" + saleOrderById.getSaleorderNo() + "】提前采购已提交审核");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }
        if ("bhSaleorderVerify".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            reviewAndMessageVO.setCheckSpecies("备货审核");
            reviewAndMessageVO.setCheckUrl("./order/saleorder/viewBhSaleorder.do?saleorderId=" + id);
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            Saleorder saleOrderById = saleorderMapper.getSaleOrderById(Integer.valueOf(id));
            if (saleOrderById != null) {
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("备货单【" + saleOrderById.getSaleorderNo() + "】需要您审核");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("备货单【" + saleOrderById.getSaleorderNo() + "】已提交审核");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }
        if ("traderCustomerVerify".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            reviewAndMessageVO.setCheckSpecies("供应商审核");
            reviewAndMessageVO.setCheckUrl("./trader/customer/baseinfo.do?traderId=" + id);
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            Trader traderByTraderId = traderCustomerMapper.getTraderByTraderCustomerId(Integer.valueOf(id));
            if (traderByTraderId != null) {
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("【" + traderByTraderId.getTraderName() + "】需要您审核");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("【" + traderByTraderId.getTraderName() + "】已提交审核");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }
        if ("editSaleorderVerify".equals(historicProcessInstance.getProcessDefinitionKey()) || "saleorderModifyAudit".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            reviewAndMessageVO.setCheckSpecies("订单修改");
            reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            SaleorderModifyApply saleorderModifyApply = saleorderMapper.getSaleorderModifyApplyBySaleorderModifyId(Integer.valueOf(id));
            if (saleorderModifyApply != null) {
                reviewAndMessageVO.setCheckUrl("./order/saleorder/viewModifyApply.do?saleorderModifyApplyId="
                        + saleorderModifyApply.getSaleorderModifyApplyId() + "&saleorderId="
                        + saleorderModifyApply.getSaleorderId());
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("销售单修改【" + saleorderModifyApply.getSaleorderModifyApplyNo() + "】需要您审核");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("销售单修改【" + saleorderModifyApply.getSaleorderModifyApplyNo() + "】已提交审核");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }
        if ("editBuyorderVerify".equals(historicProcessInstance.getProcessDefinitionKey()) || "saleorderModifyAudit".equals(historicProcessInstance.getProcessDefinitionKey())) {
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            reviewAndMessageVO.setCheckSpecies("订单修改");
            BuyorderModifyApply buyorderModifyApply = buyorderMapper.getBuyModifyInfoByBuyorderModifyId(Integer.valueOf(id));
            if (buyorderModifyApply != null) {
                reviewAndMessageVO.setCheckUrl("./order/buyorder/viewModifyApply.do?buyorderModifyApplyId="
                        + buyorderModifyApply.getBuyorderModifyApplyId());
                reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
                reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
//                SaleorderModifyApply saleorderModifyApply = saleorderMapper.getSaleorderModifyApplyBySaleorderModifyId(Integer.valueOf(id));

                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("采购单修改【" + buyorderModifyApply.getBuyorderModifyApplyNo() + "】需要您审核");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("采购单修改【" + buyorderModifyApply.getBuyorderModifyApplyNo() + "】已提交审核");
                }
            }
            reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
            reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
            reviewAndMessageVO.setProcessNo("E" + id);
        }
        if ("purchaseContractVerify".equals(historicProcessInstance.getProcessDefinitionKey())){
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            reviewAndMessageVO.setCheckSpecies("合同回传");
            Buyorder buyorder = buyorderMapper.selectByPrimaryKey(Integer.valueOf(id));
            if (buyorder != null){
                reviewAndMessageVO.setCheckUrl("./order/newBuyorder/newViewBuyOrderDetail.do?buyorderId="+buyorder.getBuyorderId());
                reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
                reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
                reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
                reviewAndMessageVO.setProcessNo("C" + DateUtil.DateToString(new Date(),"yyyyMMdd")+buyorder.getBuyorderId());
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("采购订单【"+buyorder.getBuyorderNo()+"】合同回传需要你审批");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("采购订单【"+buyorder.getBuyorderNo()+"】合同回传已提交审核");
                }
            }
        }
        if ("rebateChargeVerify".equals(historicProcessInstance.getProcessDefinitionKey())){
            String businessKey = historicProcessInstance.getBusinessKey();
            String id = businessKey.substring(businessKey.indexOf("_") + 1);
            reviewAndMessageVO.setCheckSpecies("供应商返利结算");
            BuyOrderRebateApplyDto buyOrderRebateApplyDto = buyOrderRebateApplyApiService.selectById(id);
            if (buyOrderRebateApplyDto != null){
                reviewAndMessageVO.setCheckUrl("./buyOrder/rebateChargeApply/detail.do?buyOrderRebateChargeId="+buyOrderRebateApplyDto.getBuyOrderRebateChargeId());
                reviewAndMessageVO.setSubmitTime(DateUtil.convertString(historicProcessInstance.getStartTime().getTime(), DateUtil.TIME_FORMAT));
                reviewAndMessageVO.setSubmitUser(historicProcessInstance.getStartUserId());
                reviewAndMessageVO.setSubmitTimeSort(historicProcessInstance.getStartTime().getTime());
                reviewAndMessageVO.setProcessNo(buyOrderRebateApplyDto.getBuyOrderRebateChargeNo());
                if (FlashConstant.REMINED_REVIEW.equals(type)) {
                    reviewAndMessageVO.setRemindMessage("供应商返利结算单【"+buyOrderRebateApplyDto.getBuyOrderRebateChargeNo()+"】需要你审批");
                } else if (FlashConstant.SUBMMIT_UNFINISH.equals(type)) {
                    initReviewUser(historicProcessInstance, reviewAndMessageVO, processResultDto, taskService);
                    reviewAndMessageVO.setRemindMessage("供应商返利结算单【"+buyOrderRebateApplyDto.getBuyOrderRebateChargeNo()+"】已提交审核");
                }
            }
        }
    }

    private void initReviewUser(HistoricProcessInstance historicProcessInstance, ReviewAndMessageVO reviewAndMessageVO, ProcessResultDto processResultDto, TaskService taskService) {
        try {
            List<HistoricTaskInstance> historicTaskInstanceList = processResultDto.getHistoricTaskInstanceList();
            if (CollectionUtils.isNotEmpty(historicTaskInstanceList)) {
                HistoricTaskInstance historicTaskInstance = historicTaskInstanceList.stream().filter(e -> e.getProcessInstanceId().equals(historicProcessInstance.getId())).findFirst().orElse(null);
                if (historicTaskInstance != null) {
                    List<IdentityLink> identityLinksForTask = taskService.getIdentityLinksForTask(historicTaskInstance.getId());
                    if (CollectionUtils.isNotEmpty(identityLinksForTask)) {
                        List<String> collect = identityLinksForTask.stream().map(e -> e.getUserId()).collect(Collectors.toList());
                        String reviewUser = String.join(",", collect);
                        reviewAndMessageVO.setReviewUser(reviewUser);
                    }
                }
            }
        } catch (Exception e) {
            logger.info("1" + e.getMessage());
        }
    }

    private boolean filterStarter(User user, ProcessInstance e, HistoricActivityInstanceQuery historyAIQuery) {
        String processDefinitionId = e.getProcessInstanceId();
        historyAIQuery.processInstanceId(processDefinitionId);
        historyAIQuery.activityName("申请人");
        List<HistoricActivityInstance> list = historyAIQuery.list();
        try {
            if(CollectionUtils.isNotEmpty(list)){
                if(list.stream().anyMatch(o->o.getAssignee().equals(user.getUsername()))){
                    return true;
                }
            }
        } catch (Exception e1) {
            return false;
        }
        return false;
    }
}
