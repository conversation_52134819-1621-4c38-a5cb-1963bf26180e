package com.vedeng.common.shiro.cas.config;

import com.vedeng.common.shiro.cas.CasClientConstants;
import com.vedeng.common.shiro.cas.properties.CasClientProperties;
import com.vedeng.common.shiro.cas.service.UacAccountFacadeService;
import com.vedeng.common.shiro.cas.service.UacAccountFacadeServiceImpl;
import io.buji.pac4j.context.ShiroSessionStore;
import org.jasig.cas.client.configuration.ConfigurationKeys;
import org.jasig.cas.client.session.SingleSignOutFilter;
import org.jasig.cas.client.session.SingleSignOutHttpSessionListener;
import org.pac4j.cas.client.CasClient;
import org.pac4j.core.client.Clients;
import org.pac4j.core.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.servlet.DispatcherType;
import javax.servlet.http.HttpSessionListener;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class CasClientConfiguration {
    @Autowired
    private CasClientProperties casClientProperties;

    @Autowired
    private CasClient casClient;


    @Bean
    protected Config casConfig() {
        Clients clients = new Clients(CasClientConstants.DEFAULT_CALLBACK_URL);
        clients.setClients(casClient);
        Config config = new Config();
        config.setClients(clients);
        config.setSessionStore(ShiroSessionStore.INSTANCE);
        return config;
    }


    @Bean
    public UacAccountFacadeService uacAccountFacadeService() {
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(Math.toIntExact(TimeUnit.SECONDS.toMillis(10)));
        factory.setReadTimeout(Math.toIntExact(TimeUnit.SECONDS.toMillis(30)));
        RestTemplate internalRestTemplate = new RestTemplate(factory);
        return new UacAccountFacadeServiceImpl(internalRestTemplate);
    }


    @Bean
    public HttpSessionListener singleSignOutSessionListener() {
        return new SingleSignOutHttpSessionListener();
    }

    @Bean
    public FilterRegistrationBean singleSignOutFilter() {
        FilterRegistrationBean filterFilterRegistrationBean = new FilterRegistrationBean();
        filterFilterRegistrationBean.setFilter(new SingleSignOutFilter());
        filterFilterRegistrationBean.setOrder(0);
        filterFilterRegistrationBean.setInitParameters(Collections.singletonMap(ConfigurationKeys.CAS_SERVER_URL_PREFIX.getName(),
                casClientProperties.getPrefixUrl()));
        filterFilterRegistrationBean.setDispatcherTypes(DispatcherType.REQUEST);
        return filterFilterRegistrationBean;
    }

}
