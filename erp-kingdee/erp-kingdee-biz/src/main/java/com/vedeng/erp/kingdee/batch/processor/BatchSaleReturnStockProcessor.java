package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSaleOutStockEntity;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.dto.result.KingDeeSaleOutStockQueryResultDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleOutStockConvertor;
import com.vedeng.erp.kingdee.service.KingDeeSaleOutStockService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 销售退货入库 处理类
 * @date 2023/1/7 13:00
 */

@Service
@Slf4j
public class BatchSaleReturnStockProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeSaleReturnStockDto> {

    private static String SEPARATER = "-";
    @Autowired
    private BatchSaleorderDtoMapper batchSaleorderDtoMapper;

    @Autowired
    private BatchSaleorderGoodsDtoMapper batchSaleorderGoodsDtoMapper;

    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;


    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private BatchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper;

    @Autowired
    private KingDeeSaleOutStockConvertor kingDeeSaleOutStockConvertor;

    @Autowired
    private KingDeeSaleOutStockService kingDeeSaleOutStockService;

    @Override
    public KingDeeSaleReturnStockDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        log.info("SaleBackProcessorService.process:{},是否为虚拟入库单:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto), batchWarehouseGoodsOutInDto.getIsVirtual());

        // 判断数据是否已存在
        log.info("查询是否推送金蝶销售售后退货入库单");
        KingDeeSaleReturnStockDto queryDto = new KingDeeSaleReturnStockDto();
        queryDto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        boolean old = kingDeeBaseApi.isExist(queryDto);
        if (old) {
            log.info("销售售后退货入库单,数据已存在:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }

        // 查询售后单信息
        BatchAfterSalesDto batchAfterSalesDto = batchAfterSalesDtoMapper.findSaleByAfterSalesNoAndSubjectType(batchWarehouseGoodsOutInDto.getRelateNo(), 535);
        if (Objects.isNull(batchAfterSalesDto)) {
            throw new KingDeeException("未查到此销售售后" + batchWarehouseGoodsOutInDto.getRelateNo() + "信息");
        }

        // 查询客户 信息 销售单
        BatchSaleorderDto batchSaleorderDto = batchSaleorderDtoMapper.selectBySaleorderNo(batchAfterSalesDto.getOrderNo());
        if (Objects.isNull(batchSaleorderDto)) {
            throw new KingDeeException("未查到此售后的销售单" + batchAfterSalesDto.getOrderNo() + "信息");
        }

        // 税率
        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        String taxRate = StrUtil.isEmpty(batchWarehouseGoodsOutInDto.getRate()) ? "0.00" : decimalFormat.format(new BigDecimal(batchWarehouseGoodsOutInDto.getRate()).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));

        // 封装金蝶主体信息
        KingDeeSaleReturnStockDto dto = new KingDeeSaleReturnStockDto();
        dto.setFid("0");
        dto.setFBillTypeID("XSTHD01_SYS");
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        dto.setFQzokBddjtid(batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString());
        dto.setFDate(DateUtil.formatDate(batchWarehouseGoodsOutInDto.getOutInTime()));
        dto.setFSaleOrgId(KingDeeConstant.ORG_ID.toString());
        dto.setFStockOrgId(KingDeeConstant.ORG_ID.toString());
        dto.setFRetcustId(batchSaleorderDto.getTraderCustomerId().toString());

        List<KingDeeSaleReturnStockDetailDto> FEntityDetail = new ArrayList<>();
        // 查询出入库明细表信息
        List<BatchWarehouseGoodsOutInItemDto> byOutInNo = batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo());
        if (CollUtil.isEmpty(byOutInNo)) {
            log.warn("未能查到销售售后退货入库单子单信息:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }
        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(byOutInNo);
        // 封装销售售后单关联的商品信息map
        Map<Integer, BatchAfterSalesGoodsDto> batchAfterSalesGoods2Map = new HashMap<>();
        if (KingDeeConstant.ONE.equals(batchWarehouseGoodsOutInDto.getIsVirtual())) {
            List<BatchSaleorderGoodsDto> batchSaleorderGoodsDtos = batchSaleorderGoodsDtoMapper.findBySaleorderId(batchAfterSalesDto.getOrderId());
            if (CollUtil.isEmpty(batchSaleorderGoodsDtos)) {
                log.warn("虚拟出入库单，未能查到销售售后退货入库单关联的商品信息:{}", JSON.toJSONString(batchAfterSalesDto));
                throw new KingDeeException("虚拟出入库单，未能查到销售后退货入库单关联的商品信息");
            }
            List<BatchAfterSalesGoodsDto> batchAfterSalesGoodsDtoList = batchSaleorderGoodsDtos.stream().map(batchSaleorderGoodsDto -> {
                BatchAfterSalesGoodsDto batchAfterSalesGoodsDto = new BatchAfterSalesGoodsDto();
                batchAfterSalesGoodsDto.setAfterSalesGoodsId(batchSaleorderGoodsDto.getSaleorderGoodsId());
                batchAfterSalesGoodsDto.setSku(batchSaleorderGoodsDto.getSku());
                batchAfterSalesGoodsDto.setPrice(batchSaleorderGoodsDto.getPrice());
                batchAfterSalesGoodsDto.setDeliveryDirect(batchSaleorderGoodsDto.getDeliveryDirect());
                return batchAfterSalesGoodsDto;
            }).collect(Collectors.toList());
            batchAfterSalesGoods2Map = batchAfterSalesGoodsDtoList.stream().collect(Collectors.toMap(BatchAfterSalesGoodsDto::getAfterSalesGoodsId, c -> c, (k1, k2) -> k1));
        } else {
            List<BatchAfterSalesGoodsDto> batchAfterSalesGoodsDtoList = batchAfterSalesDto.getBatchAfterSalesGoodsDtoList();
            if (CollUtil.isEmpty(batchAfterSalesGoodsDtoList)) {
                log.warn("非虚拟出入库单，未能查到销售售后退货入库单关联的商品信息:{}", JSON.toJSONString(batchAfterSalesDto));
                throw new KingDeeException("非虚拟出入库单，未能查到销售后退货入库单关联的商品信息");
            }
            batchAfterSalesGoods2Map = batchAfterSalesGoodsDtoList.stream().collect(Collectors.toMap(BatchAfterSalesGoodsDto::getAfterSalesGoodsId, c -> c, (k1, k2) -> k1));
        }
        log.info("封装销售售后单关联的商品信息map为:{}", JSON.toJSONString(batchAfterSalesGoods2Map));
        // 遍历出入库明细表信息
        for (BatchWarehouseGoodsOutInItemDto outInItemDto : byOutInNo) {
            BatchAfterSalesGoodsDto batchAfterSalesGoodsDto = batchAfterSalesGoods2Map.get(outInItemDto.getRelatedId());
            if (Objects.isNull(batchAfterSalesGoodsDto)) {
                log.error("未查到当前销售售后退货入库单原始商品信息:{}", JSON.toJSONString(outInItemDto));
                throw new KingDeeException("未查到当前销售售后退货入库单原始商品信息");
            }

            //VDERP-14008 入库单拆分
            List<BatchRWarehouseGoodsOutJWarehouseGoodsInDto> rOutJInDtoList = batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.findByWarehouseGoodsInIdAndWarehouseGoodsInItemIdAndRelationType(
                    batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId(), outInItemDto.getWarehouseGoodsOutInDetailId(), 1);

            boolean isEmpty = CollectionUtils.isEmpty(rOutJInDtoList);
            // 判断退货行数量是否等于关联的出库单行总数
            boolean isNumMismatch = !isEmpty && !(outInItemDto.getNum().compareTo(
                    rOutJInDtoList.stream()
                            .map(BatchRWarehouseGoodsOutJWarehouseGoodsInDto::getNum)
                            .reduce(BigDecimal.ZERO, BigDecimal::add)) == 0);

            if (isEmpty || isNumMismatch) {
                log.info("销售退货入库单关联出入库关系表，无关联或部分关联:入库单明细={}，关联关系={}", JSON.toJSONString(outInItemDto), JSON.toJSONString(rOutJInDtoList));
                this.buildIrrelevantFEntityData(batchAfterSalesDto, taxRate, FEntityDetail, outInItemDto, batchAfterSalesGoodsDto);
            } else {
                log.info("销售退货入库单关联出入库关系表，全部关联:入库单明细={}，关联关系={}", JSON.toJSONString(outInItemDto), JSON.toJSONString(rOutJInDtoList));
                this.buildFEntityData(batchAfterSalesDto, taxRate, FEntityDetail, outInItemDto, batchAfterSalesGoodsDto, rOutJInDtoList);
            }

        }

        dto.setFEntity(FEntityDetail);

        return dto;
    }

    private void buildFEntityData(BatchAfterSalesDto batchAfterSalesDto, String taxRate, List<KingDeeSaleReturnStockDetailDto> FEntityDetail, BatchWarehouseGoodsOutInItemDto outInItemDto, BatchAfterSalesGoodsDto batchAfterSalesGoodsDto, List<BatchRWarehouseGoodsOutJWarehouseGoodsInDto> rOutJInDtoList) {

        for (BatchRWarehouseGoodsOutJWarehouseGoodsInDto r : rOutJInDtoList) {
            // 封装FEntity数据
            KingDeeSaleReturnStockDetailDto kingDeeSaleReturnStockDetailDto = this.commonBuildFEntityData(batchAfterSalesDto, taxRate, outInItemDto, batchAfterSalesGoodsDto, r.getNum());
            kingDeeSaleReturnStockDetailDto.setFQzokBddjhid(outInItemDto.getWarehouseGoodsOutInDetailId() + SEPARATER + r.getWarehouseGoodsOutItemId());
            kingDeeSaleReturnStockDetailDto.setFSrcBillTypeID("SAL_OUTSTOCK");

            // 关联原始出库单
            List<KingDeeSaleReturnStockDetailLink> fentityLink = new ArrayList<>();

            BatchWarehouseGoodsOutInItemDto batchWarehouseGoodsOutInItemDto = batchWarehouseGoodsOutInItemDtoMapper.selectByPrimaryKey(r.getWarehouseGoodsOutItemId());
            if (Objects.isNull(batchWarehouseGoodsOutInItemDto)) {
                log.error("未能查到销售售后退货入库单关联的出库明细信息:出库单明细id= {}", r.getWarehouseGoodsOutItemId());
                throw new KingDeeException("未能查到销售售后退货入库单关联的出库明细信息");
            }

            log.info("调用查询金蝶接口-销售退货入库单关联原始销售出库单查询金蝶：fBillNo={}", batchWarehouseGoodsOutInItemDto.getOutInNo());
            List<KingDeeSaleOutStockQueryResultDto> query = kingDeeSaleOutStockService.getKingDeeSaleOutStock(batchWarehouseGoodsOutInItemDto.getOutInNo());
            log.info("销售退货入库单关联原始销售出库单查询金蝶结果：{}", JSON.toJSONString(query));
            if (CollUtil.isEmpty(query)) {
                log.error("调用金蝶查询-未能查到销售退货入库单关联原始销售出库单信息:fBillNo= {}", batchWarehouseGoodsOutInItemDto.getOutInNo());
                throw new KingDeeException("调用金蝶查询-未能查到销售退货入库单关联原始销售出库单信息");
            }
            KingDeeSaleOutStockEntity kingDeeSaleOutStockEntity = new KingDeeSaleOutStockEntity();
            kingDeeSaleOutStockEntity.setFid(CollUtil.getFirst(query).getFID());
            List<KingDeeSaleOutStockDetailDto> source = query.stream().map(kingDeeSaleOutStockQueryResultDto -> {
                KingDeeSaleOutStockDetailDto kingDeeSaleOutStockDetailDto = new KingDeeSaleOutStockDetailDto();
                kingDeeSaleOutStockDetailDto.setFEntryId(kingDeeSaleOutStockQueryResultDto.getFEntity_FENTRYID());
                kingDeeSaleOutStockDetailDto.setF_QZOK_BDDJHID(kingDeeSaleOutStockQueryResultDto.getF_QZOK_BDDJHID());
                kingDeeSaleOutStockDetailDto.setFRealQty(new BigDecimal(kingDeeSaleOutStockQueryResultDto.getFRealQty()));
                return kingDeeSaleOutStockDetailDto;
            }).collect(Collectors.toList());
            kingDeeSaleOutStockEntity.setFEntity(JSONArray.parseArray(JSON.toJSONString(source)));

            KingDeeSaleOutStockDto kingDeeSaleOutStockDto = kingDeeSaleOutStockConvertor.toDto(kingDeeSaleOutStockEntity);
            String fid = kingDeeSaleOutStockDto.getFid();
            List<KingDeeSaleOutStockDetailDto> fEntity = kingDeeSaleOutStockDto.getFEntity();
            Map<String, KingDeeSaleOutStockDetailDto> kingDeeSaleOutStockDetails2Map = fEntity.stream().collect(Collectors.toMap(KingDeeSaleOutStockDetailDto::getF_QZOK_BDDJHID, c -> c, (k1, k2) -> k1));
            KingDeeSaleOutStockDetailDto kingDeeSaleOutStockDetailDto = kingDeeSaleOutStockDetails2Map.get(r.getWarehouseGoodsOutItemId().toString());
            if (Objects.isNull(kingDeeSaleOutStockDetailDto)) {
                log.error("从kingDeeSaleOutStockDetails2Map未能查到KingDeeSaleOutStockDetailDto信息:F_QZOK_BDDJHID= {},kingDeeSaleOutStockDetails2Map={}", r.getWarehouseGoodsOutItemId(), JSON.toJSONString(kingDeeSaleOutStockDetails2Map));
                throw new KingDeeException("从kingDeeSaleOutStockDetails2Map未能查到KingDeeSaleOutStockDetailDto信息");
            }
            if (Objects.isNull(kingDeeSaleOutStockDetailDto.getFEntryId())) {
                log.error("查询到的销售退货入库单关联的原始销售出库单的fEntity中fEntryId为空：{}", JSON.toJSONString(kingDeeSaleOutStockDto));
                throw new KingDeeException("查询到的销售退货入库单关联的原始销售出库单的fEntity中fEntryId为空");
            }
            // 封装fentityLink数据
            KingDeeSaleReturnStockDetailLink kingDeeSaleReturnStockDetailLink = new KingDeeSaleReturnStockDetailLink();
            // 关联的原销售出库单的剩余可退数量
            BigDecimal unboundOutInAmount = batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.getUnboundOutInAmountByItemId(r.getWarehouseGoodsOutItemId(), true);
            kingDeeSaleReturnStockDetailLink.setFLinkId(0);
            kingDeeSaleReturnStockDetailLink.setFentityLinkFruleid("OutStock-SalReturnStock");
            kingDeeSaleReturnStockDetailLink.setFentityLinkFflowlineid(0);
            kingDeeSaleReturnStockDetailLink.setFentityLinkFstableid(0);
            kingDeeSaleReturnStockDetailLink.setFentityLinkFstablename("T_SAL_OUTSTOCKENTRY");
            kingDeeSaleReturnStockDetailLink.setFentityLinkFsbillid(fid);
            kingDeeSaleReturnStockDetailLink.setFentityLinkFsid(kingDeeSaleOutStockDetailDto.getFEntryId());
            kingDeeSaleReturnStockDetailLink.setFentityLinkFbaseunitqtyold(unboundOutInAmount);
            kingDeeSaleReturnStockDetailLink.setFentityLinkFbaseunitqty(r.getNum());
            kingDeeSaleReturnStockDetailLink.setFentityLinkFsalbaseqtyold(unboundOutInAmount);
            kingDeeSaleReturnStockDetailLink.setFentityLinkFsalbaseqty(r.getNum());
            fentityLink.add(kingDeeSaleReturnStockDetailLink);

            kingDeeSaleReturnStockDetailDto.setFentityLink(fentityLink);
            FEntityDetail.add(kingDeeSaleReturnStockDetailDto);
        }
    }

    private void buildIrrelevantFEntityData(BatchAfterSalesDto batchAfterSalesDto, String taxRate, List<KingDeeSaleReturnStockDetailDto> FEntityDetail, BatchWarehouseGoodsOutInItemDto outInItemDto, BatchAfterSalesGoodsDto batchAfterSalesGoodsDto) {
        KingDeeSaleReturnStockDetailDto kingDeeSaleReturnStockDetailDto = this.commonBuildFEntityData(batchAfterSalesDto, taxRate, outInItemDto, batchAfterSalesGoodsDto, outInItemDto.getNum());
        kingDeeSaleReturnStockDetailDto.setFQzokBddjhid(outInItemDto.getWarehouseGoodsOutInDetailId().toString());
        FEntityDetail.add(kingDeeSaleReturnStockDetailDto);
    }

    private KingDeeSaleReturnStockDetailDto commonBuildFEntityData(BatchAfterSalesDto batchAfterSalesDto, String taxRate, BatchWarehouseGoodsOutInItemDto outInItemDto, BatchAfterSalesGoodsDto batchAfterSalesGoodsDto, BigDecimal num) {
        KingDeeSaleReturnStockDetailDto kingDeeSaleReturnStockDetailDto = new KingDeeSaleReturnStockDetailDto();
        kingDeeSaleReturnStockDetailDto.setFmaterialid(batchAfterSalesGoodsDto.getSku());
        kingDeeSaleReturnStockDetailDto.setFRealQty(num.abs());
        kingDeeSaleReturnStockDetailDto.setFstockid("ck9999");
        kingDeeSaleReturnStockDetailDto.setFtaxprice(batchAfterSalesGoodsDto.getPrice());
        kingDeeSaleReturnStockDetailDto.setFentrytaxrate(new BigDecimal(taxRate));
        kingDeeSaleReturnStockDetailDto.setFQzokYsddh(batchAfterSalesDto.getOrderNo());
        kingDeeSaleReturnStockDetailDto.setFQzokGsywdh(batchAfterSalesDto.getAfterSalesNo());
        kingDeeSaleReturnStockDetailDto.setFQzokYwlx("销售退货");
        kingDeeSaleReturnStockDetailDto.setFQzokPch(outInItemDto.getBatchNumber());
        kingDeeSaleReturnStockDetailDto.setFQzokXlh(outInItemDto.getBarcodeFactory());
        kingDeeSaleReturnStockDetailDto.setFQzokSfzf(batchAfterSalesGoodsDto.getDeliveryDirect().equals(0) ? "否" : "是");
        return kingDeeSaleReturnStockDetailDto;
    }

}
