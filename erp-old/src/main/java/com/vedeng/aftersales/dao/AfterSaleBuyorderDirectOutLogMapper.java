package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.model.AfterSaleBuyorderDirectOutLog;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;

import java.util.List;

public interface AfterSaleBuyorderDirectOutLogMapper {
    int deleteByPrimaryKey(Integer afterSaleBuyorderDirectOutLogId);

    int insert(AfterSaleBuyorderDirectOutLog record);

    int insertSelective(AfterSaleBuyorderDirectOutLog record);

    AfterSaleBuyorderDirectOutLog selectByPrimaryKey(Integer afterSaleBuyorderDirectOutLogId);

    int updateByPrimaryKeySelective(AfterSaleBuyorderDirectOutLog record);

    int updateByPrimaryKey(AfterSaleBuyorderDirectOutLog record);

    List<AfterSaleBuyorderDirectOutLog> getDirectReturnOutList(AfterSalesVo afterSalesVo);

    Integer getTotalNumByAfterSalesGoodsId(AfterSalesGoodsVo afterSalesGoodsVo);

    int delectById(Integer afterSaleBuyorderDirectOutLogId);

    List<AfterSaleBuyorderDirectOutLog> getBuyOrderAfterSaleLog(AfterSalesGoodsVo afterSalesGoodsVo);

    AfterSaleBuyorderDirectOutLog getSimiliarLog(AfterSaleBuyorderDirectOutLog saleBuyorderDirectOutLog);
}