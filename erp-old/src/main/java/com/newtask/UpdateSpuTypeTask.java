package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.model.CoreSpu;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/16 14:07
 * @describe 更新前一天的V_CORE_SKU表中的SPU_TYPE字段
 */
@Slf4j
@Component
@JobHandler(value = "updateSpuTypeTask")
public class UpdateSpuTypeTask extends AbstractJobHandler {

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Resource
    private CoreSpuMapper coreSpuMapper;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        log.info("更新V_CORE_SKU表中的SPU_TYPE字段开始,入参:{}",param);
        XxlJobLogger.log("更新V_CORE_SKU表中的SPU_TYPE字段开始,入参:{}",param);

        // 获取当前系统时间
        String nowDate = DateUtil.getNowDate("yyyy-MM-dd");

        List<CoreSpu> coreSpuList = coreSpuMapper.listSpuByModTime(nowDate);
        log.info("产品类型 coreSpuList --> {}", coreSpuList);

        coreSkuMapper.updateSpuTypeBySpuId(coreSpuList, nowDate);

        log.info("更新V_CORE_SKU表中的SPU_TYPE字段结束,入参:{}",param);
        XxlJobLogger.log("更新V_CORE_SKU表中的SPU_TYPE字段结束,入参:{}",param);

        return SUCCESS;
    }
}
