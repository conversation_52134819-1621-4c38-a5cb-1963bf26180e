<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderWorkareaMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderWorkareaEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_WORKAREA-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="AREA_ID" jdbcType="INTEGER" property="areaId" />
    <result column="AREA_NAME_ALL" jdbcType="VARCHAR" property="areaNameAll" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, TRADER_ID, AREA_ID, AREA_NAME_ALL, MOD_TIME, ADD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_WORKAREA
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER_WORKAREA
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.trader.domain.entity.TraderWorkareaEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_WORKAREA (TRADER_ID, AREA_ID, AREA_NAME_ALL, 
      MOD_TIME, ADD_TIME)
    values (#{traderId,jdbcType=INTEGER}, #{areaId,jdbcType=INTEGER}, #{areaNameAll,jdbcType=VARCHAR}, 
      #{modTime,jdbcType=TIMESTAMP}, #{addTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.trader.domain.entity.TraderWorkareaEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_WORKAREA
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="areaId != null">
        AREA_ID,
      </if>
      <if test="areaNameAll != null">
        AREA_NAME_ALL,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaNameAll != null">
        #{areaNameAll,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderWorkareaEntity">
    <!--@mbg.generated-->
    update T_TRADER_WORKAREA
    <set>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="areaId != null">
        AREA_ID = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaNameAll != null">
        AREA_NAME_ALL = #{areaNameAll,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderWorkareaEntity">
    <!--@mbg.generated-->
    update T_TRADER_WORKAREA
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      AREA_ID = #{areaId,jdbcType=INTEGER},
      AREA_NAME_ALL = #{areaNameAll,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2024-03-29-->
  <select id="selectByTraderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_TRADER_WORKAREA
        where TRADER_ID=#{traderId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2024-03-29-->
  <delete id="deleteByTraderId">
    delete from T_TRADER_WORKAREA
    where TRADER_ID=#{traderId,jdbcType=INTEGER}
  </delete>
</mapper>