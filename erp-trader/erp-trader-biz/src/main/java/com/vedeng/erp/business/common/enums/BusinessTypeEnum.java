package com.vedeng.erp.business.common.enums;

import lombok.Getter;

@Getter
public enum BusinessTypeEnum {

    SMALL(5701,"小产品"),

    BIG(5702,"大单品"),

    SYNTHESIS(5703,"综合项目"),
    
    AED(5704,"AED"),

    EMERGENCY(5705,"应急"),
    
    ;
    
    
    
    private final Integer code;


    private final String title;

    BusinessTypeEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }


    public static String getTitleByCode(Integer code) {
        for (BusinessTypeEnum businessChanceLevelEnum : BusinessTypeEnum.values()) {
            if (businessChanceLevelEnum.getCode().equals(code)) {
                return businessChanceLevelEnum.getTitle();
            }
        }
        return "";
    }

}
