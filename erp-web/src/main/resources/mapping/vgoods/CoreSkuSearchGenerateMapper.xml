<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.CoreSkuSearchGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.CoreSkuSearchGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    <id column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SPU_ID" jdbcType="INTEGER" property="spuId" />
    <result column="CHECK_STATUS" jdbcType="TINYINT" property="checkStatus" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="SPEC" jdbcType="VARCHAR" property="spec" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName" />
    <result column="SHOW_NAME" jdbcType="VARCHAR" property="showName" />
    <result column="MATERIAL_CODE" jdbcType="VARCHAR" property="materialCode" />
    <result column="SUPPLY_MODEL" jdbcType="VARCHAR" property="supplyModel" />
    <result column="IS_STOCKUP" jdbcType="VARCHAR" property="isStockup" />
    <result column="WIKI_HREF" jdbcType="VARCHAR" property="wikiHref" />
    <result column="TECHNICAL_PARAMETER" jdbcType="VARCHAR" property="technicalParameter" />
    <result column="PERFORMANCE_PARAMETER" jdbcType="VARCHAR" property="performanceParameter" />
    <result column="SPEC_PARAMETER" jdbcType="VARCHAR" property="specParameter" />
    <result column="BASE_UNIT_ID" jdbcType="INTEGER" property="baseUnitId" />
    <result column="MIN_ORDER" jdbcType="DECIMAL" property="minOrder" />
    <result column="GOODS_LENGTH" jdbcType="DECIMAL" property="goodsLength" />
    <result column="GOODS_WIDTH" jdbcType="DECIMAL" property="goodsWidth" />
    <result column="GOODS_HEIGHT" jdbcType="DECIMAL" property="goodsHeight" />
    <result column="PACKAGE_LENGTH" jdbcType="DECIMAL" property="packageLength" />
    <result column="PACKAGE_WIDTH" jdbcType="DECIMAL" property="packageWidth" />
    <result column="PACKAGE_HEIGHT" jdbcType="DECIMAL" property="packageHeight" />
    <result column="NET_WEIGHT" jdbcType="DECIMAL" property="netWeight" />
    <result column="GROSS_WEIGHT" jdbcType="DECIMAL" property="grossWeight" />
    <result column="UNIT_ID" jdbcType="INTEGER" property="unitId" />
    <result column="CHANGE_NUM" jdbcType="DECIMAL" property="changeNum" />
    <result column="PACKING_LIST" jdbcType="VARCHAR" property="packingList" />
    <result column="AFTER_SALE_CONTENT" jdbcType="VARCHAR" property="afterSaleContent" />
    <result column="QA_YEARS" jdbcType="VARCHAR" property="qaYears" />
    <!--存储条件标准化 since ERP_LV_2020_67-->
    <result column="STORAGE_CONDITION_ONE" jdbcType="TINYINT" property="storageConditionOne" />
    <result column="STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE" jdbcType="FLOAT" property="storageConditionOneLowerValue" />
    <result column="STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE" jdbcType="FLOAT" property="storageConditionOneUpperValue" />
    <result column="STORAGE_CONDITION_HUMIDITY_LOWER_VALUE" jdbcType="FLOAT" property="storageConditionHumidityLowerValue" />
    <result column="STORAGE_CONDITION_HUMIDITY_UPPER_VALUE" jdbcType="FLOAT" property="storageConditionHumidityUpperValue" />
    <result column="STORAGE_CONDITION_TWO" jdbcType="VARCHAR" property="storageConditionTwo" />

    <result column="EFFECTIVE_DAY_UNIT" jdbcType="TINYINT" property="effectiveDayUnit" />
    <result column="EFFECTIVE_DAYS" jdbcType="VARCHAR" property="effectiveDays" />
    <result column="QA_RULE" jdbcType="VARCHAR" property="qaRule" />
    <result column="QA_OUT_PRICE" jdbcType="DECIMAL" property="qaOutPrice" />
    <result column="QA_RESPONSE_TIME" jdbcType="DECIMAL" property="qaResponseTime" />
    <result column="HAS_BACKUP_MACHINE" jdbcType="VARCHAR" property="hasBackupMachine" />
    <result column="SUPPLIER_EXTEND_GUARANTEE_PRICE" jdbcType="DECIMAL" property="supplierExtendGuaranteePrice" />
    <result column="CORE_PARTS_PRICE_FID" jdbcType="INTEGER" property="corePartsPriceFid" />
    <result column="RETURN_GOODS_CONDITIONS" jdbcType="TINYINT" property="returnGoodsConditions" />
    <result column="FREIGHT_INTRODUCTIONS" jdbcType="VARCHAR" property="freightIntroductions" />
    <result column="EXCHANGE_GOODS_CONDITIONS" jdbcType="VARCHAR" property="exchangeGoodsConditions" />
    <result column="EXCHANGE_GOODS_METHOD" jdbcType="VARCHAR" property="exchangeGoodsMethod" />
    <result column="GOODS_COMMENTS" jdbcType="VARCHAR" property="goodsComments" />
    <result column="STATUS" jdbcType="TINYINT" property="status" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CHECK_TIME" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="CHECKER" jdbcType="INTEGER" property="checker" />
    <result column="OPERATE_INFO_ID" jdbcType="INTEGER" property="operateInfoId" />
    <result column="DELETE_REASON" jdbcType="VARCHAR" property="deleteReason" />
    <result column="LAST_CHECK_REASON" jdbcType="VARCHAR" property="lastCheckReason" />
    <result column="TAX_CATEGORY_NO" jdbcType="VARCHAR" property="taxCategoryNo" />
    <result column="JX_MARKET_PRICE" jdbcType="DECIMAL" property="jxMarketPrice" />
    <result column="JX_SALE_PRICE" jdbcType="DECIMAL" property="jxSalePrice" />
    <result column="JX_FLAG" jdbcType="INTEGER" property="jxFlag" />
    <result column="SOURCE" jdbcType="TINYINT" property="source" />
    <result column="GOODS_LEVEL_NO" jdbcType="INTEGER" property="goodsLevelNo" />
    <result column="GOODS_POSITION_NO" jdbcType="INTEGER" property="goodsPositionNo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    SKU_ID, SPU_ID, CHECK_STATUS, MODEL, SPEC, SKU_NO, SKU_NAME, SHOW_NAME, MATERIAL_CODE, 
    SUPPLY_MODEL, IS_STOCKUP, WIKI_HREF, TECHNICAL_PARAMETER, PERFORMANCE_PARAMETER, 
    SPEC_PARAMETER, BASE_UNIT_ID, MIN_ORDER, GOODS_LENGTH, GOODS_WIDTH, GOODS_HEIGHT, 
    PACKAGE_LENGTH, PACKAGE_WIDTH, PACKAGE_HEIGHT, NET_WEIGHT, GROSS_WEIGHT, UNIT_ID, 
    CHANGE_NUM, PACKING_LIST, AFTER_SALE_CONTENT, QA_YEARS,
    <include refid="GoodsStorageConditionColumn"/>,
    EFFECTIVE_DAY_UNIT, EFFECTIVE_DAYS, QA_RULE, QA_OUT_PRICE, QA_RESPONSE_TIME, HAS_BACKUP_MACHINE, 
    SUPPLIER_EXTEND_GUARANTEE_PRICE, CORE_PARTS_PRICE_FID, RETURN_GOODS_CONDITIONS, FREIGHT_INTRODUCTIONS, 
    EXCHANGE_GOODS_CONDITIONS, EXCHANGE_GOODS_METHOD, GOODS_COMMENTS, `STATUS`, ADD_TIME, 
    CREATOR, MOD_TIME, UPDATER, CHECK_TIME, CHECKER, OPERATE_INFO_ID, DELETE_REASON, 
    LAST_CHECK_REASON, TAX_CATEGORY_NO, JX_MARKET_PRICE, JX_SALE_PRICE, JX_FLAG, `SOURCE`, GOODS_LEVEL_NO,
    GOODS_POSITION_NO
  </sql>
  <sql id="GoodsStorageConditionColumn">
    STORAGE_CONDITION_ONE, STORAGE_CONDITION_ONE_LOWER_VALUE, STORAGE_CONDITION_ONE_UPPER_VALUE,
    STORAGE_CONDITION_HUMIDITY_LOWER_VALUE, STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,
    STORAGE_CONDITION_TWO
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.goods.model.CoreSkuSearchGenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from V_CORE_SKU_SEARCH
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from V_CORE_SKU_SEARCH
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    delete from V_CORE_SKU_SEARCH
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.goods.model.CoreSkuSearchGenerateExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    delete from V_CORE_SKU_SEARCH
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.CoreSkuSearchGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    <selectKey keyProperty="skuId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into V_CORE_SKU_SEARCH (SPU_ID, CHECK_STATUS, MODEL, 
      SPEC, SKU_NO, SKU_NAME, 
      SHOW_NAME, MATERIAL_CODE, SUPPLY_MODEL, 
      IS_STOCKUP, WIKI_HREF, TECHNICAL_PARAMETER, 
      PERFORMANCE_PARAMETER, SPEC_PARAMETER, BASE_UNIT_ID, 
      MIN_ORDER, GOODS_LENGTH, GOODS_WIDTH, 
      GOODS_HEIGHT, PACKAGE_LENGTH, PACKAGE_WIDTH, 
      PACKAGE_HEIGHT, NET_WEIGHT, GROSS_WEIGHT, 
      UNIT_ID, CHANGE_NUM, PACKING_LIST, 
      AFTER_SALE_CONTENT, QA_YEARS, STORAGE_CONDITION_ONE, 
      STORAGE_CONDITION_TWO, EFFECTIVE_DAY_UNIT, 
      EFFECTIVE_DAYS, QA_RULE, QA_OUT_PRICE, 
      QA_RESPONSE_TIME, HAS_BACKUP_MACHINE, SUPPLIER_EXTEND_GUARANTEE_PRICE, 
      CORE_PARTS_PRICE_FID, RETURN_GOODS_CONDITIONS, 
      FREIGHT_INTRODUCTIONS, EXCHANGE_GOODS_CONDITIONS, 
      EXCHANGE_GOODS_METHOD, GOODS_COMMENTS, `STATUS`, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, CHECK_TIME, CHECKER, 
      OPERATE_INFO_ID, DELETE_REASON, LAST_CHECK_REASON, 
      TAX_CATEGORY_NO, JX_MARKET_PRICE, JX_SALE_PRICE, 
      JX_FLAG, `SOURCE`)
    values (#{spuId,jdbcType=INTEGER}, #{checkStatus,jdbcType=TINYINT}, #{model,jdbcType=VARCHAR}, 
      #{spec,jdbcType=VARCHAR}, #{skuNo,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, 
      #{showName,jdbcType=VARCHAR}, #{materialCode,jdbcType=VARCHAR}, #{supplyModel,jdbcType=VARCHAR}, 
      #{isStockup,jdbcType=VARCHAR}, #{wikiHref,jdbcType=VARCHAR}, #{technicalParameter,jdbcType=VARCHAR}, 
      #{performanceParameter,jdbcType=VARCHAR}, #{specParameter,jdbcType=VARCHAR}, #{baseUnitId,jdbcType=INTEGER}, 
      #{minOrder,jdbcType=DECIMAL}, #{goodsLength,jdbcType=DECIMAL}, #{goodsWidth,jdbcType=DECIMAL}, 
      #{goodsHeight,jdbcType=DECIMAL}, #{packageLength,jdbcType=DECIMAL}, #{packageWidth,jdbcType=DECIMAL}, 
      #{packageHeight,jdbcType=DECIMAL}, #{netWeight,jdbcType=DECIMAL}, #{grossWeight,jdbcType=DECIMAL}, 
      #{unitId,jdbcType=INTEGER}, #{changeNum,jdbcType=DECIMAL}, #{packingList,jdbcType=VARCHAR}, 
      #{afterSaleContent,jdbcType=VARCHAR}, #{qaYears,jdbcType=VARCHAR}, #{storageConditionOne,jdbcType=TINYINT}, 
      #{storageConditionTwo,jdbcType=VARCHAR}, #{effectiveDayUnit,jdbcType=TINYINT}, 
      #{effectiveDays,jdbcType=VARCHAR}, #{qaRule,jdbcType=VARCHAR}, #{qaOutPrice,jdbcType=DECIMAL}, 
      #{qaResponseTime,jdbcType=DECIMAL}, #{hasBackupMachine,jdbcType=VARCHAR}, #{supplierExtendGuaranteePrice,jdbcType=DECIMAL}, 
      #{corePartsPriceFid,jdbcType=INTEGER}, #{returnGoodsConditions,jdbcType=TINYINT}, 
      #{freightIntroductions,jdbcType=VARCHAR}, #{exchangeGoodsConditions,jdbcType=VARCHAR}, 
      #{exchangeGoodsMethod,jdbcType=VARCHAR}, #{goodsComments,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER}, #{checkTime,jdbcType=TIMESTAMP}, #{checker,jdbcType=INTEGER}, 
      #{operateInfoId,jdbcType=INTEGER}, #{deleteReason,jdbcType=VARCHAR}, #{lastCheckReason,jdbcType=VARCHAR}, 
      #{taxCategoryNo,jdbcType=VARCHAR}, #{jxMarketPrice,jdbcType=DECIMAL}, #{jxSalePrice,jdbcType=DECIMAL}, 
      #{jxFlag,jdbcType=INTEGER}, #{source,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.CoreSkuSearchGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    <selectKey keyProperty="skuId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into V_CORE_SKU_SEARCH
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="spuId != null">
        SPU_ID,
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS,
      </if>
      <if test="model != null">
        MODEL,
      </if>
      <if test="spec != null">
        SPEC,
      </if>
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="skuName != null">
        SKU_NAME,
      </if>
      <if test="showName != null">
        SHOW_NAME,
      </if>
      <if test="materialCode != null">
        MATERIAL_CODE,
      </if>
      <if test="supplyModel != null">
        SUPPLY_MODEL,
      </if>
      <if test="isStockup != null">
        IS_STOCKUP,
      </if>
      <if test="wikiHref != null">
        WIKI_HREF,
      </if>
      <if test="technicalParameter != null">
        TECHNICAL_PARAMETER,
      </if>
      <if test="performanceParameter != null">
        PERFORMANCE_PARAMETER,
      </if>
      <if test="specParameter != null">
        SPEC_PARAMETER,
      </if>
      <if test="baseUnitId != null">
        BASE_UNIT_ID,
      </if>
      <if test="minOrder != null">
        MIN_ORDER,
      </if>
      <if test="goodsLength != null">
        GOODS_LENGTH,
      </if>
      <if test="goodsWidth != null">
        GOODS_WIDTH,
      </if>
      <if test="goodsHeight != null">
        GOODS_HEIGHT,
      </if>
      <if test="packageLength != null">
        PACKAGE_LENGTH,
      </if>
      <if test="packageWidth != null">
        PACKAGE_WIDTH,
      </if>
      <if test="packageHeight != null">
        PACKAGE_HEIGHT,
      </if>
      <if test="netWeight != null">
        NET_WEIGHT,
      </if>
      <if test="grossWeight != null">
        GROSS_WEIGHT,
      </if>
      <if test="unitId != null">
        UNIT_ID,
      </if>
      <if test="changeNum != null">
        CHANGE_NUM,
      </if>
      <if test="packingList != null">
        PACKING_LIST,
      </if>
      <if test="afterSaleContent != null">
        AFTER_SALE_CONTENT,
      </if>
      <if test="qaYears != null">
        QA_YEARS,
      </if>
      <if test="storageConditionOne != null">
        STORAGE_CONDITION_ONE,
      </if>
      <if test="storageConditionTwo != null">
        STORAGE_CONDITION_TWO,
      </if>
      <if test="effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT,
      </if>
      <if test="effectiveDays != null">
        EFFECTIVE_DAYS,
      </if>
      <if test="qaRule != null">
        QA_RULE,
      </if>
      <if test="qaOutPrice != null">
        QA_OUT_PRICE,
      </if>
      <if test="qaResponseTime != null">
        QA_RESPONSE_TIME,
      </if>
      <if test="hasBackupMachine != null">
        HAS_BACKUP_MACHINE,
      </if>
      <if test="supplierExtendGuaranteePrice != null">
        SUPPLIER_EXTEND_GUARANTEE_PRICE,
      </if>
      <if test="corePartsPriceFid != null">
        CORE_PARTS_PRICE_FID,
      </if>
      <if test="returnGoodsConditions != null">
        RETURN_GOODS_CONDITIONS,
      </if>
      <if test="freightIntroductions != null">
        FREIGHT_INTRODUCTIONS,
      </if>
      <if test="exchangeGoodsConditions != null">
        EXCHANGE_GOODS_CONDITIONS,
      </if>
      <if test="exchangeGoodsMethod != null">
        EXCHANGE_GOODS_METHOD,
      </if>
      <if test="goodsComments != null">
        GOODS_COMMENTS,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="checkTime != null">
        CHECK_TIME,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID,
      </if>
      <if test="deleteReason != null">
        DELETE_REASON,
      </if>
      <if test="lastCheckReason != null">
        LAST_CHECK_REASON,
      </if>
      <if test="taxCategoryNo != null">
        TAX_CATEGORY_NO,
      </if>
      <if test="jxMarketPrice != null">
        JX_MARKET_PRICE,
      </if>
      <if test="jxSalePrice != null">
        JX_SALE_PRICE,
      </if>
      <if test="jxFlag != null">
        JX_FLAG,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="spuId != null">
        #{spuId,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="spec != null">
        #{spec,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null">
        #{showName,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="supplyModel != null">
        #{supplyModel,jdbcType=VARCHAR},
      </if>
      <if test="isStockup != null">
        #{isStockup,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null">
        #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameter != null">
        #{technicalParameter,jdbcType=VARCHAR},
      </if>
      <if test="performanceParameter != null">
        #{performanceParameter,jdbcType=VARCHAR},
      </if>
      <if test="specParameter != null">
        #{specParameter,jdbcType=VARCHAR},
      </if>
      <if test="baseUnitId != null">
        #{baseUnitId,jdbcType=INTEGER},
      </if>
      <if test="minOrder != null">
        #{minOrder,jdbcType=DECIMAL},
      </if>
      <if test="goodsLength != null">
        #{goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="goodsWidth != null">
        #{goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="goodsHeight != null">
        #{goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="packageLength != null">
        #{packageLength,jdbcType=DECIMAL},
      </if>
      <if test="packageWidth != null">
        #{packageWidth,jdbcType=DECIMAL},
      </if>
      <if test="packageHeight != null">
        #{packageHeight,jdbcType=DECIMAL},
      </if>
      <if test="netWeight != null">
        #{netWeight,jdbcType=DECIMAL},
      </if>
      <if test="grossWeight != null">
        #{grossWeight,jdbcType=DECIMAL},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="changeNum != null">
        #{changeNum,jdbcType=DECIMAL},
      </if>
      <if test="packingList != null">
        #{packingList,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleContent != null">
        #{afterSaleContent,jdbcType=VARCHAR},
      </if>
      <if test="qaYears != null">
        #{qaYears,jdbcType=VARCHAR},
      </if>
      <if test="storageConditionOne != null">
        #{storageConditionOne,jdbcType=TINYINT},
      </if>
      <if test="storageConditionTwo != null">
        #{storageConditionTwo,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDayUnit != null">
        #{effectiveDayUnit,jdbcType=TINYINT},
      </if>
      <if test="effectiveDays != null">
        #{effectiveDays,jdbcType=VARCHAR},
      </if>
      <if test="qaRule != null">
        #{qaRule,jdbcType=VARCHAR},
      </if>
      <if test="qaOutPrice != null">
        #{qaOutPrice,jdbcType=DECIMAL},
      </if>
      <if test="qaResponseTime != null">
        #{qaResponseTime,jdbcType=DECIMAL},
      </if>
      <if test="hasBackupMachine != null">
        #{hasBackupMachine,jdbcType=VARCHAR},
      </if>
      <if test="supplierExtendGuaranteePrice != null">
        #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      </if>
      <if test="corePartsPriceFid != null">
        #{corePartsPriceFid,jdbcType=INTEGER},
      </if>
      <if test="returnGoodsConditions != null">
        #{returnGoodsConditions,jdbcType=TINYINT},
      </if>
      <if test="freightIntroductions != null">
        #{freightIntroductions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsConditions != null">
        #{exchangeGoodsConditions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsMethod != null">
        #{exchangeGoodsMethod,jdbcType=VARCHAR},
      </if>
      <if test="goodsComments != null">
        #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=INTEGER},
      </if>
      <if test="operateInfoId != null">
        #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null">
        #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null">
        #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null">
        #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="jxMarketPrice != null">
        #{jxMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="jxSalePrice != null">
        #{jxSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="jxFlag != null">
        #{jxFlag,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.goods.model.CoreSkuSearchGenerateExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    select count(*) from V_CORE_SKU_SEARCH
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    update V_CORE_SKU_SEARCH
    <set>
      <if test="record.skuId != null">
        SKU_ID = #{record.skuId,jdbcType=INTEGER},
      </if>
      <if test="record.spuId != null">
        SPU_ID = #{record.spuId,jdbcType=INTEGER},
      </if>
      <if test="record.checkStatus != null">
        CHECK_STATUS = #{record.checkStatus,jdbcType=TINYINT},
      </if>
      <if test="record.model != null">
        MODEL = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.spec != null">
        SPEC = #{record.spec,jdbcType=VARCHAR},
      </if>
      <if test="record.skuNo != null">
        SKU_NO = #{record.skuNo,jdbcType=VARCHAR},
      </if>
      <if test="record.skuName != null">
        SKU_NAME = #{record.skuName,jdbcType=VARCHAR},
      </if>
      <if test="record.showName != null">
        SHOW_NAME = #{record.showName,jdbcType=VARCHAR},
      </if>
      <if test="record.materialCode != null">
        MATERIAL_CODE = #{record.materialCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplyModel != null">
        SUPPLY_MODEL = #{record.supplyModel,jdbcType=VARCHAR},
      </if>
      <if test="record.isStockup != null">
        IS_STOCKUP = #{record.isStockup,jdbcType=VARCHAR},
      </if>
      <if test="record.wikiHref != null">
        WIKI_HREF = #{record.wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="record.technicalParameter != null">
        TECHNICAL_PARAMETER = #{record.technicalParameter,jdbcType=VARCHAR},
      </if>
      <if test="record.performanceParameter != null">
        PERFORMANCE_PARAMETER = #{record.performanceParameter,jdbcType=VARCHAR},
      </if>
      <if test="record.specParameter != null">
        SPEC_PARAMETER = #{record.specParameter,jdbcType=VARCHAR},
      </if>
      <if test="record.baseUnitId != null">
        BASE_UNIT_ID = #{record.baseUnitId,jdbcType=INTEGER},
      </if>
      <if test="record.minOrder != null">
        MIN_ORDER = #{record.minOrder,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsLength != null">
        GOODS_LENGTH = #{record.goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsWidth != null">
        GOODS_WIDTH = #{record.goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="record.goodsHeight != null">
        GOODS_HEIGHT = #{record.goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="record.packageLength != null">
        PACKAGE_LENGTH = #{record.packageLength,jdbcType=DECIMAL},
      </if>
      <if test="record.packageWidth != null">
        PACKAGE_WIDTH = #{record.packageWidth,jdbcType=DECIMAL},
      </if>
      <if test="record.packageHeight != null">
        PACKAGE_HEIGHT = #{record.packageHeight,jdbcType=DECIMAL},
      </if>
      <if test="record.netWeight != null">
        NET_WEIGHT = #{record.netWeight,jdbcType=DECIMAL},
      </if>
      <if test="record.grossWeight != null">
        GROSS_WEIGHT = #{record.grossWeight,jdbcType=DECIMAL},
      </if>
      <if test="record.unitId != null">
        UNIT_ID = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.changeNum != null">
        CHANGE_NUM = #{record.changeNum,jdbcType=DECIMAL},
      </if>
      <if test="record.packingList != null">
        PACKING_LIST = #{record.packingList,jdbcType=VARCHAR},
      </if>
      <if test="record.afterSaleContent != null">
        AFTER_SALE_CONTENT = #{record.afterSaleContent,jdbcType=VARCHAR},
      </if>
      <if test="record.qaYears != null">
        QA_YEARS = #{record.qaYears,jdbcType=VARCHAR},
      </if>
      <if test="record.storageConditionOne != null">
        STORAGE_CONDITION_ONE = #{record.storageConditionOne,jdbcType=TINYINT},
      </if>
      <if test="record.storageConditionTwo != null">
        STORAGE_CONDITION_TWO = #{record.storageConditionTwo,jdbcType=VARCHAR},
      </if>
      <if test="record.effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT = #{record.effectiveDayUnit,jdbcType=TINYINT},
      </if>
      <if test="record.effectiveDays != null">
        EFFECTIVE_DAYS = #{record.effectiveDays,jdbcType=VARCHAR},
      </if>
      <if test="record.qaRule != null">
        QA_RULE = #{record.qaRule,jdbcType=VARCHAR},
      </if>
      <if test="record.qaOutPrice != null">
        QA_OUT_PRICE = #{record.qaOutPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.qaResponseTime != null">
        QA_RESPONSE_TIME = #{record.qaResponseTime,jdbcType=DECIMAL},
      </if>
      <if test="record.hasBackupMachine != null">
        HAS_BACKUP_MACHINE = #{record.hasBackupMachine,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierExtendGuaranteePrice != null">
        SUPPLIER_EXTEND_GUARANTEE_PRICE = #{record.supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.corePartsPriceFid != null">
        CORE_PARTS_PRICE_FID = #{record.corePartsPriceFid,jdbcType=INTEGER},
      </if>
      <if test="record.returnGoodsConditions != null">
        RETURN_GOODS_CONDITIONS = #{record.returnGoodsConditions,jdbcType=TINYINT},
      </if>
      <if test="record.freightIntroductions != null">
        FREIGHT_INTRODUCTIONS = #{record.freightIntroductions,jdbcType=VARCHAR},
      </if>
      <if test="record.exchangeGoodsConditions != null">
        EXCHANGE_GOODS_CONDITIONS = #{record.exchangeGoodsConditions,jdbcType=VARCHAR},
      </if>
      <if test="record.exchangeGoodsMethod != null">
        EXCHANGE_GOODS_METHOD = #{record.exchangeGoodsMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsComments != null">
        GOODS_COMMENTS = #{record.goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `STATUS` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.checkTime != null">
        CHECK_TIME = #{record.checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.checker != null">
        CHECKER = #{record.checker,jdbcType=INTEGER},
      </if>
      <if test="record.operateInfoId != null">
        OPERATE_INFO_ID = #{record.operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="record.deleteReason != null">
        DELETE_REASON = #{record.deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="record.lastCheckReason != null">
        LAST_CHECK_REASON = #{record.lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="record.taxCategoryNo != null">
        TAX_CATEGORY_NO = #{record.taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.jxMarketPrice != null">
        JX_MARKET_PRICE = #{record.jxMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.jxSalePrice != null">
        JX_SALE_PRICE = #{record.jxSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.jxFlag != null">
        JX_FLAG = #{record.jxFlag,jdbcType=INTEGER},
      </if>
      <if test="record.source != null">
        `SOURCE` = #{record.source,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    update V_CORE_SKU_SEARCH
    set SKU_ID = #{record.skuId,jdbcType=INTEGER},
      SPU_ID = #{record.spuId,jdbcType=INTEGER},
      CHECK_STATUS = #{record.checkStatus,jdbcType=TINYINT},
      MODEL = #{record.model,jdbcType=VARCHAR},
      SPEC = #{record.spec,jdbcType=VARCHAR},
      SKU_NO = #{record.skuNo,jdbcType=VARCHAR},
      SKU_NAME = #{record.skuName,jdbcType=VARCHAR},
      SHOW_NAME = #{record.showName,jdbcType=VARCHAR},
      MATERIAL_CODE = #{record.materialCode,jdbcType=VARCHAR},
      SUPPLY_MODEL = #{record.supplyModel,jdbcType=VARCHAR},
      IS_STOCKUP = #{record.isStockup,jdbcType=VARCHAR},
      WIKI_HREF = #{record.wikiHref,jdbcType=VARCHAR},
      TECHNICAL_PARAMETER = #{record.technicalParameter,jdbcType=VARCHAR},
      PERFORMANCE_PARAMETER = #{record.performanceParameter,jdbcType=VARCHAR},
      SPEC_PARAMETER = #{record.specParameter,jdbcType=VARCHAR},
      BASE_UNIT_ID = #{record.baseUnitId,jdbcType=INTEGER},
      MIN_ORDER = #{record.minOrder,jdbcType=DECIMAL},
      GOODS_LENGTH = #{record.goodsLength,jdbcType=DECIMAL},
      GOODS_WIDTH = #{record.goodsWidth,jdbcType=DECIMAL},
      GOODS_HEIGHT = #{record.goodsHeight,jdbcType=DECIMAL},
      PACKAGE_LENGTH = #{record.packageLength,jdbcType=DECIMAL},
      PACKAGE_WIDTH = #{record.packageWidth,jdbcType=DECIMAL},
      PACKAGE_HEIGHT = #{record.packageHeight,jdbcType=DECIMAL},
      NET_WEIGHT = #{record.netWeight,jdbcType=DECIMAL},
      GROSS_WEIGHT = #{record.grossWeight,jdbcType=DECIMAL},
      UNIT_ID = #{record.unitId,jdbcType=INTEGER},
      CHANGE_NUM = #{record.changeNum,jdbcType=DECIMAL},
      PACKING_LIST = #{record.packingList,jdbcType=VARCHAR},
      AFTER_SALE_CONTENT = #{record.afterSaleContent,jdbcType=VARCHAR},
      QA_YEARS = #{record.qaYears,jdbcType=VARCHAR},
      STORAGE_CONDITION_ONE = #{record.storageConditionOne,jdbcType=TINYINT},
      STORAGE_CONDITION_TWO = #{record.storageConditionTwo,jdbcType=VARCHAR},
      EFFECTIVE_DAY_UNIT = #{record.effectiveDayUnit,jdbcType=TINYINT},
      EFFECTIVE_DAYS = #{record.effectiveDays,jdbcType=VARCHAR},
      QA_RULE = #{record.qaRule,jdbcType=VARCHAR},
      QA_OUT_PRICE = #{record.qaOutPrice,jdbcType=DECIMAL},
      QA_RESPONSE_TIME = #{record.qaResponseTime,jdbcType=DECIMAL},
      HAS_BACKUP_MACHINE = #{record.hasBackupMachine,jdbcType=VARCHAR},
      SUPPLIER_EXTEND_GUARANTEE_PRICE = #{record.supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      CORE_PARTS_PRICE_FID = #{record.corePartsPriceFid,jdbcType=INTEGER},
      RETURN_GOODS_CONDITIONS = #{record.returnGoodsConditions,jdbcType=TINYINT},
      FREIGHT_INTRODUCTIONS = #{record.freightIntroductions,jdbcType=VARCHAR},
      EXCHANGE_GOODS_CONDITIONS = #{record.exchangeGoodsConditions,jdbcType=VARCHAR},
      EXCHANGE_GOODS_METHOD = #{record.exchangeGoodsMethod,jdbcType=VARCHAR},
      GOODS_COMMENTS = #{record.goodsComments,jdbcType=VARCHAR},
      `STATUS` = #{record.status,jdbcType=TINYINT},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      CHECK_TIME = #{record.checkTime,jdbcType=TIMESTAMP},
      CHECKER = #{record.checker,jdbcType=INTEGER},
      OPERATE_INFO_ID = #{record.operateInfoId,jdbcType=INTEGER},
      DELETE_REASON = #{record.deleteReason,jdbcType=VARCHAR},
      LAST_CHECK_REASON = #{record.lastCheckReason,jdbcType=VARCHAR},
      TAX_CATEGORY_NO = #{record.taxCategoryNo,jdbcType=VARCHAR},
      JX_MARKET_PRICE = #{record.jxMarketPrice,jdbcType=DECIMAL},
      JX_SALE_PRICE = #{record.jxSalePrice,jdbcType=DECIMAL},
      JX_FLAG = #{record.jxFlag,jdbcType=INTEGER},
      `SOURCE` = #{record.source,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.CoreSkuSearchGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    update V_CORE_SKU_SEARCH
    <set>
      <if test="spuId != null">
        SPU_ID = #{spuId,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS = #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="model != null">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="spec != null">
        SPEC = #{spec,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null">
        SHOW_NAME = #{showName,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        MATERIAL_CODE = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="supplyModel != null">
        SUPPLY_MODEL = #{supplyModel,jdbcType=VARCHAR},
      </if>
      <if test="isStockup != null">
        IS_STOCKUP = #{isStockup,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null">
        WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameter != null">
        TECHNICAL_PARAMETER = #{technicalParameter,jdbcType=VARCHAR},
      </if>
      <if test="performanceParameter != null">
        PERFORMANCE_PARAMETER = #{performanceParameter,jdbcType=VARCHAR},
      </if>
      <if test="specParameter != null">
        SPEC_PARAMETER = #{specParameter,jdbcType=VARCHAR},
      </if>
      <if test="baseUnitId != null">
        BASE_UNIT_ID = #{baseUnitId,jdbcType=INTEGER},
      </if>
      <if test="minOrder != null">
        MIN_ORDER = #{minOrder,jdbcType=DECIMAL},
      </if>
      <if test="goodsLength != null">
        GOODS_LENGTH = #{goodsLength,jdbcType=DECIMAL},
      </if>
      <if test="goodsWidth != null">
        GOODS_WIDTH = #{goodsWidth,jdbcType=DECIMAL},
      </if>
      <if test="goodsHeight != null">
        GOODS_HEIGHT = #{goodsHeight,jdbcType=DECIMAL},
      </if>
      <if test="packageLength != null">
        PACKAGE_LENGTH = #{packageLength,jdbcType=DECIMAL},
      </if>
      <if test="packageWidth != null">
        PACKAGE_WIDTH = #{packageWidth,jdbcType=DECIMAL},
      </if>
      <if test="packageHeight != null">
        PACKAGE_HEIGHT = #{packageHeight,jdbcType=DECIMAL},
      </if>
      <if test="netWeight != null">
        NET_WEIGHT = #{netWeight,jdbcType=DECIMAL},
      </if>
      <if test="grossWeight != null">
        GROSS_WEIGHT = #{grossWeight,jdbcType=DECIMAL},
      </if>
      <if test="unitId != null">
        UNIT_ID = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="changeNum != null">
        CHANGE_NUM = #{changeNum,jdbcType=DECIMAL},
      </if>
      <if test="packingList != null">
        PACKING_LIST = #{packingList,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleContent != null">
        AFTER_SALE_CONTENT = #{afterSaleContent,jdbcType=VARCHAR},
      </if>
      <if test="qaYears != null">
        QA_YEARS = #{qaYears,jdbcType=VARCHAR},
      </if>
      <if test="storageConditionOne != null">
        STORAGE_CONDITION_ONE = #{storageConditionOne,jdbcType=TINYINT},
      </if>
      <if test="storageConditionOne == 4">
        STORAGE_CONDITION_ONE_LOWER_VALUE = #{storageConditionOneLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionOne == 4">
        STORAGE_CONDITION_ONE_UPPER_VALUE = #{storageConditionOneUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionTwo != null">
        STORAGE_CONDITION_TWO = #{storageConditionTwo,jdbcType=VARCHAR},
      </if>
      <if test="effectiveDayUnit != null">
        EFFECTIVE_DAY_UNIT = #{effectiveDayUnit,jdbcType=TINYINT},
      </if>
      <if test="effectiveDays != null">
        EFFECTIVE_DAYS = #{effectiveDays,jdbcType=VARCHAR},
      </if>
      <if test="qaRule != null">
        QA_RULE = #{qaRule,jdbcType=VARCHAR},
      </if>
      <if test="qaOutPrice != null">
        QA_OUT_PRICE = #{qaOutPrice,jdbcType=DECIMAL},
      </if>
      <if test="qaResponseTime != null">
        QA_RESPONSE_TIME = #{qaResponseTime,jdbcType=DECIMAL},
      </if>
      <if test="hasBackupMachine != null">
        HAS_BACKUP_MACHINE = #{hasBackupMachine,jdbcType=VARCHAR},
      </if>
      <if test="supplierExtendGuaranteePrice != null">
        SUPPLIER_EXTEND_GUARANTEE_PRICE = #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      </if>
      <if test="corePartsPriceFid != null">
        CORE_PARTS_PRICE_FID = #{corePartsPriceFid,jdbcType=INTEGER},
      </if>
      <if test="returnGoodsConditions != null">
        RETURN_GOODS_CONDITIONS = #{returnGoodsConditions,jdbcType=TINYINT},
      </if>
      <if test="freightIntroductions != null">
        FREIGHT_INTRODUCTIONS = #{freightIntroductions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsConditions != null">
        EXCHANGE_GOODS_CONDITIONS = #{exchangeGoodsConditions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeGoodsMethod != null">
        EXCHANGE_GOODS_METHOD = #{exchangeGoodsMethod,jdbcType=VARCHAR},
      </if>
      <if test="goodsComments != null">
        GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        CHECKER = #{checker,jdbcType=INTEGER},
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null">
        DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null">
        LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="taxCategoryNo != null">
        TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      </if>
      <if test="jxMarketPrice != null">
        JX_MARKET_PRICE = #{jxMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="jxSalePrice != null">
        JX_SALE_PRICE = #{jxSalePrice,jdbcType=DECIMAL},
      </if>
      <if test="jxFlag != null">
        JX_FLAG = #{jxFlag,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=TINYINT},
      </if>

      <if test="midPackageNum != null">
        MID_PACKAGE_NUM = #{midPackageNum,jdbcType=INTEGER},
      </if>
      <if test="boxPackageNum != null">
        BOX_PACKAGE_NUM = #{boxPackageNum,jdbcType=INTEGER},
      </if>
      <if test="goodsLevelNo!=null ">
        GOODS_LEVEL_NO=#{goodsLevelNo,jdbcType=INTEGER},
      </if>
      <if test="goodsPositionNo!=null ">
        GOODS_POSITION_NO=#{goodsPositionNo,jdbcType=INTEGER},
      </if>
      <if test="orgIdList != null">
        ORG_ID_LIST = #{orgIdList,jdbcType=VARCHAR},
      </if>
      <if test="isAvailableSale != null">
        IS_AVAILABLE_SALE = #{isAvailableSale,jdbcType=INTEGER},
      </if>
      <if test="pushedOrgIdList != null">
        PUSHED_ORG_ID_LIST = #{pushedOrgIdList,jdbcType=VARCHAR},
      </if>
      <if test="configurationList != null">
        CONFIGURATION_LIST = #{configurationList,jdbcType=VARCHAR},
      </if>
    </set>
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.CoreSkuSearchGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    update V_CORE_SKU_SEARCH
    set SPU_ID = #{spuId,jdbcType=INTEGER},
      CHECK_STATUS = #{checkStatus,jdbcType=TINYINT},
      MODEL = #{model,jdbcType=VARCHAR},
      SPEC = #{spec,jdbcType=VARCHAR},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      SHOW_NAME = #{showName,jdbcType=VARCHAR},
      MATERIAL_CODE = #{materialCode,jdbcType=VARCHAR},
      SUPPLY_MODEL = #{supplyModel,jdbcType=VARCHAR},
      IS_STOCKUP = #{isStockup,jdbcType=VARCHAR},
      WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      TECHNICAL_PARAMETER = #{technicalParameter,jdbcType=VARCHAR},
      PERFORMANCE_PARAMETER = #{performanceParameter,jdbcType=VARCHAR},
      SPEC_PARAMETER = #{specParameter,jdbcType=VARCHAR},
      BASE_UNIT_ID = #{baseUnitId,jdbcType=INTEGER},
      MIN_ORDER = #{minOrder,jdbcType=DECIMAL},
      GOODS_LENGTH = #{goodsLength,jdbcType=DECIMAL},
      GOODS_WIDTH = #{goodsWidth,jdbcType=DECIMAL},
      GOODS_HEIGHT = #{goodsHeight,jdbcType=DECIMAL},
      PACKAGE_LENGTH = #{packageLength,jdbcType=DECIMAL},
      PACKAGE_WIDTH = #{packageWidth,jdbcType=DECIMAL},
      PACKAGE_HEIGHT = #{packageHeight,jdbcType=DECIMAL},
      NET_WEIGHT = #{netWeight,jdbcType=DECIMAL},
      GROSS_WEIGHT = #{grossWeight,jdbcType=DECIMAL},
      UNIT_ID = #{unitId,jdbcType=INTEGER},
      CHANGE_NUM = #{changeNum,jdbcType=DECIMAL},
      PACKING_LIST = #{packingList,jdbcType=VARCHAR},
      AFTER_SALE_CONTENT = #{afterSaleContent,jdbcType=VARCHAR},
      QA_YEARS = #{qaYears,jdbcType=VARCHAR},
      STORAGE_CONDITION_ONE = #{storageConditionOne,jdbcType=TINYINT},
      STORAGE_CONDITION_TWO = #{storageConditionTwo,jdbcType=VARCHAR},
      EFFECTIVE_DAY_UNIT = #{effectiveDayUnit,jdbcType=TINYINT},
      EFFECTIVE_DAYS = #{effectiveDays,jdbcType=VARCHAR},
      QA_RULE = #{qaRule,jdbcType=VARCHAR},
      QA_OUT_PRICE = #{qaOutPrice,jdbcType=DECIMAL},
      QA_RESPONSE_TIME = #{qaResponseTime,jdbcType=DECIMAL},
      HAS_BACKUP_MACHINE = #{hasBackupMachine,jdbcType=VARCHAR},
      SUPPLIER_EXTEND_GUARANTEE_PRICE = #{supplierExtendGuaranteePrice,jdbcType=DECIMAL},
      CORE_PARTS_PRICE_FID = #{corePartsPriceFid,jdbcType=INTEGER},
      RETURN_GOODS_CONDITIONS = #{returnGoodsConditions,jdbcType=TINYINT},
      FREIGHT_INTRODUCTIONS = #{freightIntroductions,jdbcType=VARCHAR},
      EXCHANGE_GOODS_CONDITIONS = #{exchangeGoodsConditions,jdbcType=VARCHAR},
      EXCHANGE_GOODS_METHOD = #{exchangeGoodsMethod,jdbcType=VARCHAR},
      GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      `STATUS` = #{status,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      CHECKER = #{checker,jdbcType=INTEGER},
      OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      TAX_CATEGORY_NO = #{taxCategoryNo,jdbcType=VARCHAR},
      JX_MARKET_PRICE = #{jxMarketPrice,jdbcType=DECIMAL},
      JX_SALE_PRICE = #{jxSalePrice,jdbcType=DECIMAL},
      JX_FLAG = #{jxFlag,jdbcType=INTEGER},
      `SOURCE` = #{source,jdbcType=TINYINT}
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </update>

  <!-- add by Tomcat.Hui 2020/5/20 3:39 下午 .Desc: VDERP-2492 修改手动填报预计可发货时间触发通知XXX. start -->
  <update id="updateModTime" parameterType="map">
    update V_CORE_SKU_SEARCH
    set MOD_TIME = #{sku.modTime}
    WHERE SKU_NO IN
    <foreach collection="list" item="sku" index="index" separator="," open="(" close=")">
      #{sku.skuNo}
    </foreach>
  </update>
  <!-- add by Tomcat.Hui 2020/5/20 3:39 下午 .Desc: VDERP-2492 修改手动填报预计可发货时间触发通知XXX. end -->


  <update id="updateSkuSearchPurchaseTime" parameterType="map">
    UPDATE V_CORE_SKU_SEARCH
    SET PURCHASE_TIME =
    <foreach collection="list" item="sku" index="index" open="CASE SKU_NO" close="END">
      WHEN #{sku.skuNo} THEN #{sku.purchaseTime}
    </foreach>
    , PURCHASE_TIME_UPDATE_TIME =
    <foreach collection="list" item="sku" index="index" open="CASE SKU_NO" close="END">
      WHEN #{sku.skuNo} THEN #{sku.purchaseTimeUpdateTime}
    </foreach>
    WHERE SKU_NO IN
    <foreach collection="list" item="sku" index="index" separator="," open="(" close=")">
      #{sku.skuNo}
    </foreach>
  </update>
</mapper>