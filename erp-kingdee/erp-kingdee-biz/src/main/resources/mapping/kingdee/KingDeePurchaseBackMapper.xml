<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseBackMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseBackEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_PURCHASE_BACK-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="F_ID" jdbcType="VARCHAR" property="fId" />
    <result column="F_BILL_NO" jdbcType="VARCHAR" property="fBillNo" />
    <result column="F_QZOK_BDDJT_ID" jdbcType="VARCHAR" property="fQzokBddjtId" />
    <result column="F_DATE" jdbcType="VARCHAR" property="fDate" />
    <result column="F_MR_TYPE" jdbcType="VARCHAR" property="fMrType" />
    <result column="F_MR_MODE" jdbcType="VARCHAR" property="fMrMode" />
    <result column="F_BUSINESS_TYPE" jdbcType="VARCHAR" property="fBusinessType" />
    <result column="F_BILL_TYPE_ID" jdbcType="VARCHAR" property="fBillTypeId" />
    <result column="F_STOCK_ORG_ID" jdbcType="VARCHAR" property="fStockOrgId" />
    <result column="F_SUPPLIER_ID" jdbcType="VARCHAR" property="fSupplierId" />
    <result column="FPURMRBENTRY"
            javaType="com.vedeng.erp.kingdee.dto.KingDeePurchaseBackDetailDto"
            property="fpurmrbentry"
            typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>

  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, F_ID, F_BILL_NO, 
    F_QZOK_BDDJT_ID, F_DATE, F_MR_TYPE, F_MR_MODE, F_BUSINESS_TYPE, F_BILL_TYPE_ID, F_STOCK_ORG_ID,
    F_SUPPLIER_ID, FPURMRBENTRY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_PURCHASE_BACK
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from KING_DEE_PURCHASE_BACK
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseBackEntity">
    <!--@mbg.generated-->
    insert into KING_DEE_PURCHASE_BACK (ID, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, CREATOR_NAME, 
      UPDATER_NAME, F_ID, F_BILL_NO, 
      F_QZOK_BDDJT_ID, F_DATE, F_MR_TYPE, 
      F_MR_MODE, F_BILL_TYPE_ID, F_STOCK_ORG_ID, 
      F_SUPPLIER_ID, FPURMRBENTRY,F_BUSINESS_TYPE)
    values (#{id,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updaterName,jdbcType=VARCHAR}, #{fId,jdbcType=VARCHAR}, #{fBillNo,jdbcType=VARCHAR}, 
      #{fQzokBddjtId,jdbcType=VARCHAR}, #{fDate,jdbcType=VARCHAR}, #{fMrType,jdbcType=VARCHAR}, 
      #{fMrMode,jdbcType=VARCHAR}, #{fBillTypeId,jdbcType=VARCHAR}, #{fStockOrgId,jdbcType=VARCHAR}, 
      #{fSupplierId,jdbcType=VARCHAR}, #{fpurmrbentry,jdbcType=VARCHAR},#{fBusinessType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseBackEntity">
    <!--@mbg.generated-->
    insert into KING_DEE_PURCHASE_BACK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fId != null">
        F_ID,
      </if>
      <if test="fBillNo != null">
        F_BILL_NO,
      </if>
      <if test="fQzokBddjtId != null">
        F_QZOK_BDDJT_ID,
      </if>
      <if test="fDate != null">
        F_DATE,
      </if>
      <if test="fMrType != null">
        F_MR_TYPE,
      </if>
      <if test="fMrMode != null">
        F_MR_MODE,
      </if>
      <if test="fBusinessType != null">
        F_BUSINESS_TYPE,
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID,
      </if>
      <if test="fStockOrgId != null">
        F_STOCK_ORG_ID,
      </if>
      <if test="fSupplierId != null">
        F_SUPPLIER_ID,
      </if>
      <if test="fpurmrbentry != null">
        FPURMRBENTRY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fId != null">
        #{fId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtId != null">
        #{fQzokBddjtId,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fMrType != null">
        #{fMrType,jdbcType=VARCHAR},
      </if>
      <if test="fMrMode != null">
        #{fMrMode,jdbcType=VARCHAR},
      </if>
      <if test="fBusinessType != null">
        #{fBusinessType,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fStockOrgId != null">
        #{fStockOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fSupplierId != null">
        #{fSupplierId,jdbcType=VARCHAR},
      </if>
      <if test="fpurmrbentry != null">
        #{fpurmrbentry,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseBackEntity">
    <!--@mbg.generated-->
    update KING_DEE_PURCHASE_BACK
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fId != null">
        F_ID = #{fId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtId != null">
        F_QZOK_BDDJT_ID = #{fQzokBddjtId,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        F_DATE = #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fMrType != null">
        F_MR_TYPE = #{fMrType,jdbcType=VARCHAR},
      </if>
      <if test="fMrMode != null">
        F_MR_MODE = #{fMrMode,jdbcType=VARCHAR},
      </if>
      <if test="fBusinessType != null">
        F_BUSINESS_TYPE = #{fBusinessType,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fStockOrgId != null">
        F_STOCK_ORG_ID = #{fStockOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fSupplierId != null">
        F_SUPPLIER_ID = #{fSupplierId,jdbcType=VARCHAR},
      </if>
      <if test="fpurmrbentry != null">
        FPURMRBENTRY = #{fpurmrbentry,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseBackEntity">
    <!--@mbg.generated-->
    update KING_DEE_PURCHASE_BACK
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      F_ID = #{fId,jdbcType=VARCHAR},
      F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      F_QZOK_BDDJT_ID = #{fQzokBddjtId,jdbcType=VARCHAR},
      F_DATE = #{fDate,jdbcType=VARCHAR},
      F_MR_TYPE = #{fMrType,jdbcType=VARCHAR},
      F_MR_MODE = #{fMrMode,jdbcType=VARCHAR},
      F_BUSINESS_TYPE = #{fBusinessType,jdbcType=VARCHAR},
      F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      F_STOCK_ORG_ID = #{fStockOrgId,jdbcType=VARCHAR},
      F_SUPPLIER_ID = #{fSupplierId,jdbcType=VARCHAR},
      FPURMRBENTRY = #{fpurmrbentry,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update KING_DEE_PURCHASE_BACK
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fId != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.fId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_BILL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fBillNo != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.fBillNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_BDDJT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokBddjtId != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.fQzokBddjtId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_DATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fDate != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.fDate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_MR_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fMrType != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.fMrType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_MR_MODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fMrMode != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.fMrMode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_BILL_TYPE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fBillTypeId != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.fBillTypeId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_STOCK_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fStockOrgId != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.fStockOrgId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_SUPPLIER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fSupplierId != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.fSupplierId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FPURMRBENTRY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fpurmrbentry != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.fpurmrbentry,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=VARCHAR}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into KING_DEE_PURCHASE_BACK
    (ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, F_ID, F_BILL_NO, 
      F_QZOK_BDDJT_ID, F_DATE, F_MR_TYPE, F_MR_MODE, F_BILL_TYPE_ID, F_STOCK_ORG_ID, 
      F_SUPPLIER_ID, FPURMRBENTRY,F_BUSINESS_TYPE)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.updaterName,jdbcType=VARCHAR}, #{item.fId,jdbcType=VARCHAR}, #{item.fBillNo,jdbcType=VARCHAR}, 
        #{item.fQzokBddjtId,jdbcType=VARCHAR}, #{item.fDate,jdbcType=VARCHAR}, #{item.fMrType,jdbcType=VARCHAR}, 
        #{item.fMrMode,jdbcType=VARCHAR}, #{item.fBillTypeId,jdbcType=VARCHAR}, #{item.fStockOrgId,jdbcType=VARCHAR}, 
        #{item.fSupplierId,jdbcType=VARCHAR}, #{item.fpurmrbentry,jdbcType=VARCHAR},#{item.FBusinessType,jdbcType=VARCHAR})
    </foreach>
  </insert>



  <select id="findByAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from KING_DEE_PURCHASE_BACK
    <where>
      <if test="fId != null">
        and F_ID=#{fId,jdbcType=VARCHAR}
      </if>
      <if test="fBillNo != null">
        and F_BILL_NO=#{fBillNo,jdbcType=VARCHAR}
      </if>
      <if test="fQzokBddjtId != null">
        and F_QZOK_BDDJT_ID=#{fQzokBddjtId,jdbcType=VARCHAR}
      </if>
      <if test="fDate != null">
        and F_DATE=#{fDate,jdbcType=VARCHAR}
      </if>
      <if test="fMrType != null">
        and F_MR_TYPE=#{fMrType,jdbcType=VARCHAR}
      </if>
      <if test="fMrMode != null">
        and F_MR_MODE=#{fMrMode,jdbcType=VARCHAR}
      </if>
      <if test="fBillTypeId != null">
        and F_BILL_TYPE_ID=#{fBillTypeId,jdbcType=VARCHAR}
      </if>
      <if test="fStockOrgId != null">
        and F_STOCK_ORG_ID=#{fStockOrgId,jdbcType=VARCHAR}
      </if>
      <if test="fSupplierId != null">
        and F_SUPPLIER_ID=#{fSupplierId,jdbcType=VARCHAR}
      </if>
      <if test="fpurmrbentry != null">
        and FPURMRBENTRY=#{fpurmrbentry,jdbcType=VARCHAR}
      </if>
      <if test="addTime != null">
        and ADD_TIME=#{addTime,jdbcType=TIMESTAMP}
      </if>
      <if test="modTime != null">
        and MOD_TIME=#{modTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creator != null">
        and CREATOR=#{creator,jdbcType=INTEGER}
      </if>
      <if test="updater != null">
        and UPDATER=#{updater,jdbcType=INTEGER}
      </if>
      <if test="creatorName != null">
        and CREATOR_NAME=#{creatorName,jdbcType=VARCHAR}
      </if>
      <if test="updaterName != null">
        and UPDATER_NAME=#{updaterName,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>