<%--
  Created by IntelliJ IDEA.
  User: admin
  Date: 2021/5/28
  Time: 14:51
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="导出定品列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<html>
<head>
    <link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
    <script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
</head>
<body>
<div class="sku-opr">
    <form id="export_stock">
        <ul class="lastResult">
            <li>
                <div class="infor_name label-c">
                    <span>*</span><label class="info">请选择月份：</label>
                </div>
                <div class="f_left label-c">
                    <input class="input-c label-c f_left input-smaller96 m0" type="text"
                           placeholder="请选择日期"
                           <%--onClick="WdatePicker({dateFmt:'yyyy-MM',maxDate:'%y-%M'})"--%>
                           name="exportMonth" id="exportMonth"
                           <%--readonly="readonly"--%>
                           value="" lay-key ="5">
                    <script>
                        //年月选择器
                        layui.use(['laydate'], function() {
                            var laydate=layui.laydate;
                            let render = laydate.render({
                                elem: '#exportMonth'
                                , type: 'month'
                                , value: new Date()
                                , show: false
                                , trigger: 'click'
                                , max: 0
                            });
                        })


                    </script>
                </div>
            </li>
            <li style="margin: 0;">
                <div class="infor_name label-c">
                </div>
                <div class="f_left month_error" style="display:none;">
                    <span style="color:red;">请选择日期!</span>
                </div>
            </li>
        </ul>
    </form>
</div>
</body>
</html>
<script>
    function callbackdata4() {
        let exportMonth = $('#exportMonth').val();
        if (!exportMonth) {
            $('.month_error').show();
            return false;
        }else{
            $('.month_error').hide();
        }
        let flag = false;
        /*let load = layer.msg('处理中', {
            icon: 16
            ,shade: 0.1
        });*/
        location.href = page_url + '/ordering-pool/goods/exportOrderingList.do?' + $('#exportMonth').serialize();

        /*$.ajax({
            async:false,
            url:page_url + '/ordering-pool/goods/exportOrderingList.do',
            data:{
                exportMonth:exportMonth
            },
            type:"GET",
            success:function(data){
                // console.log(data);
                layer.close(load);
                flag = true;
                // window.parent.search();
            },
            error:function(data){
                layer.close(load);
                if(data.status == 1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                }
                if(data.status == 400){
                    layer.open({
                        title: '提示：'
                        ,content: '导出失败，请尝试重新导出！'
                        ,btnAlign: 'c'
                    });
                }
                if(data.status == 500){
                    layer.open({
                        title: '在线调试'
                        ,content: '导出失败，请尝试重新导出！'
                        ,btnAlign: 'c'
                    });
                }
            }
        });*/
        return flag;

    }
</script>
<style>
    .sku-opr {
        padding: 20px;
        font-size: 15px;
    }

    .sku-opr ul li {
        margin-top: 20px;
    }

    .label-c {
        height: 40px;
        line-height: 40px;
    }

    .input-c {
        width: 200px;
        border-color: darkgray;
    }

    .submit-c {
        width: 90%;
        margin: 150px auto 0px;
    }

    .bg-cancle-blue {
        background: white;
        color: #1b76ed;
        border: 1px solid #1b76ed;
    }

    .layui-laydate {
        left: 40% !important;
        top: 35px !important;
    }

    i{
        background: none;
    }
</style>
<%@ include file="../../common/footer.jsp"%>
