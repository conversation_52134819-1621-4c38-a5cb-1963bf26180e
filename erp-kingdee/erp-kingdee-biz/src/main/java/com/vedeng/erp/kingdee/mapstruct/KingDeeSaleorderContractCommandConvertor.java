package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeSaleorderContractCommand;
import com.vedeng.erp.kingdee.dto.KingDeeSaleorderContractDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface KingDeeSaleorderContractCommandConvertor extends BaseCommandMapStruct<KingDeeSaleorderContractCommand, KingDeeSaleorderContractDto> {

    @Mapping(target = "FID", source = "FId")
    @Mapping(target = "f_QZOK_OrgId.FNumber", source = "FQzokOrgid")
    @Mapping(target = "f_QZOK_HTH", source = "FQzokHth")
    @Mapping(target = "f_QZOK_HTRQ", source = "FQzokHtrq")
    @Mapping(target = "f_QZOK_HTJE", source = "FQzokHtje")
    @Mapping(target = "f_QZOK_SLL", source = "FQzokSll")
    @Mapping(target = "f_QZOK_DDH", source = "FQzokDdh")
    @Mapping(target = "FBillNo", source = "FBillNo")
    KingDeeSaleorderContractCommand toCommand(KingDeeSaleorderContractDto dto);
}
