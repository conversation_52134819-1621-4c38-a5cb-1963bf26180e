package com.vedeng.logistics.model;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class WarehouseGoodsOperateDataVo {

    private BigDecimal totalPrice;
    private String chineseTotal;
    private BigDecimal expressPrice;
    private BigDecimal realTotalPrice;
    private BigDecimal couponsPrice;
    boolean hcPrintOutflag;
    boolean  jcPrintOutExpress;
    private Long currTime;


    private List<WarehouseGoodsOperateLog> woList;

    public WarehouseGoodsOperateDataVo(List<WarehouseGoodsOperateLog> woList) {
        this.woList = woList;
    }

    public WarehouseGoodsOperateDataVo(BigDecimal totalPrice, String chineseTotal, List<WarehouseGoodsOperateLog> woList) {
        this.totalPrice = totalPrice;
        this.chineseTotal = chineseTotal;
        this.woList = woList;
    }

    public WarehouseGoodsOperateDataVo() {
    }
}
