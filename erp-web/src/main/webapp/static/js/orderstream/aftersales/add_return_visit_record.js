$(function () {
    $("#submit").click(function () {
        checkLogin();
        $(".warning").remove();
        $("input").removeClass("errorbor");

        var customerName = $("input[name='customerName']").val();
        if (customerName === undefined || customerName === '') {
            warnTips("customerNameError", "客户姓名不允许为空");
            return false;
        } else if (customerName.length > 100) {
            warnTips("customerNameError", "客户姓名最多输入100字符，请检查后提交");
            return false;
        }

        var customerMobile = $("input[name='customerMobile']").val();
        if (customerMobile === undefined || customerMobile === '') {
            warnTips("customerMobileError", "手机号码不允许为空");
            return false;
        } else if (!(/^1[3456789]\d{9}$/.test(customerMobile))) {
            warnTips("customerMobileError", "手机号码有误，请检查后提交");
            return false;
        }

        var soundRecordId = $("input[name='soundRecordId']").val();
        if (!(soundRecordId === undefined || soundRecordId === '') && !(/(^[1-9]\d*$)/.test(soundRecordId))) {
            warnTips("soundRecordIdError", "录音ID只能输入数字");
            return false;
        } else if (soundRecordId.length > 7) {
            warnTips("soundRecordIdError", "录音ID最多可输入7位数字");
            return false;
        }

        var serviceResponseScore = $("input[name='serviceResponseScore']").val();
        if (serviceResponseScore === undefined || serviceResponseScore === '') {
            warnTips("serviceResponseScoreError", "请输入服务响应分值");
            return false;
        } else if (!(/^([0-9]{1,2}|100)/.test(serviceResponseScore))) {
            warnTips("serviceResponseScoreError", "请输入0-100之间的整数数值");
            return false;
        } else if (!(Number(0) <= Number(serviceResponseScore) && Number(serviceResponseScore) <= Number(100))) {
            warnTips("serviceResponseScoreError", "请输入0-100之间的整数数值");
            return false;
        }

        var serviceAttitudeScore = $("input[name='serviceAttitudeScore']").val();
        if (serviceAttitudeScore === undefined || serviceAttitudeScore === '') {
            warnTips("serviceAttitudeScoreError", "请输入服务态度分值");
            return false;
        } else if (!(/^([0-9]{1,2}|100)/.test(serviceAttitudeScore))) {
            warnTips("serviceAttitudeScoreError", "请输入0-100之间的整数数值");
            return false;
        } else if (!(Number(0) <= Number(serviceAttitudeScore) && Number(serviceAttitudeScore) <= Number(100))) {
            warnTips("serviceAttitudeScoreError", "请输入0-100之间的整数数值");
            return false;
        }

        var serviceCapabilityScore = $("input[name='serviceCapabilityScore']").val();
        if (serviceCapabilityScore === undefined || serviceCapabilityScore === '') {
            warnTips("serviceCapabilityScoreError", "请输入服务能力分值");
            return false;
        } else if (!(/^([0-9]{1,2}|100)/.test(serviceCapabilityScore))) {
            warnTips("serviceCapabilityScoreError", "请输入0-100之间的整数数值");
            return false;
        } else if (!(Number(0) <= Number(serviceCapabilityScore) && Number(serviceCapabilityScore) <= Number(100))) {
            warnTips("serviceCapabilityScoreError", "请输入0-100之间的整数数值");
            return false;
        }

        debugger;
        var comments = $("#comments").val();
        if (comments.length > 256) {
            warnTips("commentsError", "最多输入256个字符");
            return false;
        }

        $.ajax({
            type: "POST",
            url: page_url + "/order/aftersalesUpgrade/saveReturnVisitRecord.do",
            data: $('#addReturnVisitRecord').serialize(),
            dataType: 'json',
            async: false,
            success: function (data) {
                if (data.code == 0) {
                    window.parent.location.reload();
                } else {
                    layer.alert(data.message, {icon: 2},
                        function (index) {
                            layer.close(index);
                            return false;
                        }
                    );
                }

            }, error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });

    });
});


function calculateTotalScore() {
    var serviceResponseScore = $("input[name='serviceResponseScore']").val();
    var serviceAttitudeScore = $("input[name='serviceAttitudeScore']").val();
    var serviceCapabilityScore = $("input[name='serviceCapabilityScore']").val();
    var isComplaint = $("select[name='isComplaint']").val();
    var isRecommend = $("select[name='isRecommend']").val();

    if (serviceResponseScore !== undefined && serviceResponseScore !== '' && serviceAttitudeScore !== undefined && serviceAttitudeScore !== '' && serviceCapabilityScore !== undefined && serviceCapabilityScore !== '') {
        if (Number(isComplaint) === 1 || Number(isRecommend) === 1) {
            $("#totalScore").prop("value", "0.0");
            $("#result").prop("value", "不满意");
        } else {
            var totalScore = Number(serviceResponseScore) * Number(0.2) + Number(serviceAttitudeScore) * Number(0.4) + Number(serviceCapabilityScore) * Number(0.4);
            $("#totalScore").prop("value", totalScore.toFixed(1));
            if (totalScore >= 80) {
                $("#result").prop("value", "满意");
            } else {
                $("#result").prop("value", "不满意");
            }
        }
    }
}
