<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.CustomTagMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.CustomTagEntity">
        <!--@mbg.generated-->
        <!--@Table T_CUSTOM_TAG-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="TYPE" jdbcType="INTEGER" property="type"/>
        <result column="CSS_CLASS" jdbcType="VARCHAR" property="cssClass"/>
        <result column="BELONGER_ID" jdbcType="INTEGER" property="belongerId"/>
        <result column="DEFAULT_FLAG" jdbcType="TINYINT" property="defaultFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, `NAME`, `TYPE`, CSS_CLASS, BELONGER_ID,
        DEFAULT_FLAG
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOM_TAG
        where ID = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_CUSTOM_TAG
        where ID = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.erp.system.domain.entity.CustomTagEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_CUSTOM_TAG (ADD_TIME, MOD_TIME, CREATOR,
                                  UPDATER, `NAME`, `TYPE`,
                                  CSS_CLASS, BELONGER_ID, DEFAULT_FLAG)
        values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
                #{updater,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER},
                #{cssClass,jdbcType=VARCHAR}, #{belongerId,jdbcType=INTEGER}, #{defaultFlag,jdbcType=TINYINT})
    </insert>
    <insert id="insertSelective" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.erp.system.domain.entity.CustomTagEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_CUSTOM_TAG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="name != null">
                `NAME`,
            </if>
            <if test="type != null">
                `TYPE`,
            </if>
            <if test="cssClass != null">
                CSS_CLASS,
            </if>
            <if test="belongerId != null">
                BELONGER_ID,
            </if>
            <if test="defaultFlag != null">
                DEFAULT_FLAG,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="cssClass != null">
                #{cssClass,jdbcType=VARCHAR},
            </if>
            <if test="belongerId != null">
                #{belongerId,jdbcType=INTEGER},
            </if>
            <if test="defaultFlag != null">
                #{defaultFlag,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.CustomTagEntity">
        <!--@mbg.generated-->
        update T_CUSTOM_TAG
        <set>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="name != null">
                `NAME` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                `TYPE` = #{type,jdbcType=INTEGER},
            </if>
            <if test="cssClass != null">
                CSS_CLASS = #{cssClass,jdbcType=VARCHAR},
            </if>
            <if test="belongerId != null">
                BELONGER_ID = #{belongerId,jdbcType=INTEGER},
            </if>
            <if test="defaultFlag != null">
                DEFAULT_FLAG = #{defaultFlag,jdbcType=TINYINT},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.CustomTagEntity">
        <!--@mbg.generated-->
        update T_CUSTOM_TAG
        set ADD_TIME     = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME     = #{modTime,jdbcType=TIMESTAMP},
            CREATOR      = #{creator,jdbcType=INTEGER},
            UPDATER      = #{updater,jdbcType=INTEGER},
            `NAME`       = #{name,jdbcType=VARCHAR},
            `TYPE`       = #{type,jdbcType=INTEGER},
            CSS_CLASS    = #{cssClass,jdbcType=VARCHAR},
            BELONGER_ID  = #{belongerId,jdbcType=INTEGER},
            DEFAULT_FLAG = #{defaultFlag,jdbcType=TINYINT}
        where ID = #{id,jdbcType=INTEGER}
    </update>


    <!--auto generated by MybatisCodeHelper on 2022-07-18-->
    <select id="findByTypeAndBelongerId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOM_TAG
        where `TYPE` = #{type,jdbcType=INTEGER}
          and BELONGER_ID = #{belongerId,jdbcType=INTEGER}
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-07-18-->
    <select id="findByTypeAndDefaultFlag" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOM_TAG
        where `TYPE` = #{type,jdbcType=VARCHAR}
          and DEFAULT_FLAG = #{defaultFlag,jdbcType=TINYINT}
    </select>

<!--auto generated by MybatisCodeHelper on 2022-07-27-->
    <select id="findByIdInAndBelongerId" resultType="com.vedeng.erp.system.dto.CustomTagDto">
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOM_TAG
        where ID in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and BELONGER_ID=#{belongerId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2022-07-28-->
    <select id="findByIdInAndDefaultFlag" resultType="com.vedeng.erp.system.dto.CustomTagDto">
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOM_TAG
        where ID in
        <foreach item="item" index="index" collection="idCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and DEFAULT_FLAG=#{defaultFlag,jdbcType=TINYINT}
    </select>

<!--auto generated by MybatisCodeHelper on 2022-07-29-->
    <select id="findByNameAndBelongerIdAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOM_TAG
        where `NAME`=#{name,jdbcType=VARCHAR} and BELONGER_ID=#{belongerId,jdbcType=INTEGER} and
        `TYPE`=#{type,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2022-07-30-->
    <select id="findByTypeAndDefaultFlagAndName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOM_TAG
        where `TYPE`=#{type,jdbcType=INTEGER} and DEFAULT_FLAG=#{defaultFlag,jdbcType=TINYINT} and
        `NAME`=#{name,jdbcType=VARCHAR}
    </select>
    
<!--auto generated by MybatisCodeHelper on 2024-07-24-->
    <select id="findByTypeAndDefaultFlagAndNameLike" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOM_TAG
        where `TYPE`=#{type,jdbcType=INTEGER} and DEFAULT_FLAG=#{defaultFlag,jdbcType=TINYINT} 
        <if test="likeName != null and likeName != ''">
            and `NAME` like concat('%',#{likeName,jdbcType=VARCHAR},'%')
        </if>
    </select>
    
    <select id="findByIdIn" resultType="com.vedeng.erp.system.dto.CustomTagDto">
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOM_TAG
        where ID in
        <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>
    
<!--auto generated by MybatisCodeHelper on 2024-08-22-->
    <select id="findByType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOM_TAG
        where `TYPE`=#{type,jdbcType=INTEGER}
    </select>
</mapper>
