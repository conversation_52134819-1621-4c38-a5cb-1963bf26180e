package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationItemEntity;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/10/19 11:11
 **/
@Repository
public interface InvoiceRedConfirmationItemMapper {
    int deleteByPrimaryKey(Integer invoiceRedConfirmationItemId);

    int insert(InvoiceRedConfirmationItemEntity record);

    int insertSelective(InvoiceRedConfirmationItemEntity record);

    InvoiceRedConfirmationItemEntity selectByPrimaryKey(Integer invoiceRedConfirmationItemId);

    int updateByPrimaryKeySelective(InvoiceRedConfirmationItemEntity record);

    int updateByPrimaryKey(InvoiceRedConfirmationItemEntity record);

    int updateBatchSelective(List<InvoiceRedConfirmationItemEntity> list);

    int insertOrUpdateSelective(InvoiceRedConfirmationItemEntity record);

    /**
     * 根据确认单id查询确认单明细集合
     *
     * @param invoiceRedConfirmationId 确认单主表id
     * @return List<InvoiceRedConfirmationItemEntity>
     */
    List<InvoiceRedConfirmationItemEntity> getByInvoiceRedConfirmationId(Integer invoiceRedConfirmationId);
}