<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="参考同类商品" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/tag.css?rnd=${resourceVersionKey}">
<div class="content">
    <div  id="desc_div">
        <ul class="payplan">
            <li>
                <div class="parts">
                    <table class="table  table-bordered table-striped table-condensed table-centered">
                        <tbody>

                            <tr>
                                <td>售后总对接人</td>
                                <td>${traderSupplierVo.afterSaleManager} &nbsp;&nbsp; ${traderSupplierVo.serviceTelephone}</td>
                            </tr>

                            <tr>
                                <td>安装服务联系人</td>
                                <td>${traderSupplierVo.installServiceContactName} &nbsp;&nbsp; ${traderSupplierVo.installServiceContactWay}</td>
                            </tr>

                            <tr>
                                <td>技术支持对接人</td>
                                <td>${traderSupplierVo.technicalDirectContactName} &nbsp;&nbsp; ${traderSupplierVo.technicalDirectContactWay}</td>
                            </tr>

                            <tr>
                                <td>维修服务联系人</td>
                                <td>${traderSupplierVo.maintenanceContactName} &nbsp;&nbsp; ${traderSupplierVo.maintenanceContactWay}</td>
                            </tr>

                            <tr>
                                <td>退货联系人</td>
                                <td>${traderSupplierVo.exchangeContactName} &nbsp;&nbsp; ${traderSupplierVo.exchangeContactWay}</td>
                            </tr>

                            <tr>
                                <td>其他对接人</td>
                                <td>${traderSupplierVo.otherContactName} &nbsp;&nbsp; ${traderSupplierVo.otherContactWay}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </li>
        </ul>
    </div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/price/index.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>
