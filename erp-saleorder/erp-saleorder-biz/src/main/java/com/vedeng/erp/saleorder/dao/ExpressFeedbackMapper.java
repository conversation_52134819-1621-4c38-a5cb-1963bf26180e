package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.saleorder.model.po.ExpressFeedback;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface ExpressFeedbackMapper {


    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int deleteByPrimaryKey(Integer expressFeedbackId);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insert(ExpressFeedback row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insertSelective(ExpressFeedback row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    ExpressFeedback selectByPrimaryKey(Integer expressFeedbackId);

    /**
     * 查询未执行的和执行失败次数小于5的数据
     * @return
     */
    List<ExpressFeedback> selectByIsSuccess(@Param("beginTime")String beginTime, @Param("endTime")String endTime);


    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKeySelective(ExpressFeedback row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKey(ExpressFeedback row);
}
