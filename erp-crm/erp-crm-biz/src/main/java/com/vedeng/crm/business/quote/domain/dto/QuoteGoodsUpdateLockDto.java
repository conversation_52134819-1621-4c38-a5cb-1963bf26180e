package com.vedeng.crm.business.quote.domain.dto;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class QuoteGoodsUpdateLockDto {

    /**
     * 报价单商品id
     */
    private Integer quoteorderGoodsId;

    /**
     * sku
     */
    private String skuNo;

    private String oldSkuNo;

    /**
     * 货期
     */
    private String deliveryCycle;

    private String oldDeliveryCycle;

    /**
     * 价格
     */
    private BigDecimal price;

    private BigDecimal oldPrice;

    /**
     * 报备状态
     */
    private Integer reportStatus;

    private Integer oldReportStatus;

    /**
     * 报备备注
     */
    private String reportComments;


    /**
     * 数量
     */
    private Integer num;

    private Integer oldNum;

    /**
     * 修改时间
     */
    private Long modTime;

    /**
     * 修改者id
     */
    private Integer updater;

}
