package com.wms.service.chain.step;

import com.alibaba.fastjson.JSON;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.model.Buyorder;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.LogicalEnum;
import com.wms.dto.PutSaleOrderGoodsDto;
import com.wms.dto.StockCalculateDto;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.service.stockcalculate.StockinfoCaculateInterface;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 同步库存信息处理器
 */
@Service
public class PurchaseReturnOutStockInfoHandler extends AbstractHandlerStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseReturnOutStockInfoHandler.class);

    @Autowired
    @Qualifier("purchaseExgCaculateImpl")
    private StockinfoCaculateInterface stockinfoCaculateInterface;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        List<PutSaleOrderGoodsDto> outputOrderGoodsList = context.get("outputOrderGoodsList");
        Buyorder buyOrder = context.get("buyOrder");

        List<StockCalculateDto> stockCalculateList  = new ArrayList<>();

        outputOrderGoodsList.stream().forEach(outputOrder -> {
            StockCalculateDto stockCalculateDto = new StockCalculateDto();
            stockCalculateDto.setSku(outputOrder.getSku());
            stockCalculateDto.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(outputOrder.getLotAtt08()));
            stockCalculateDto.setStockNum(outputOrder.getQtyOrdered());
            stockCalculateList.add(stockCalculateDto);
        });

        //采购退货的库存计算接口
        List<WarehouseDto> warehouseList = stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);

        StockInfoDto stockInfoDto = new StockInfoDto();
        stockInfoDto.setRelatedNo(buyOrder.getBuyorderNo());
        stockInfoDto.setWarehouseStockList(warehouseList);

        LOGGER.info("采购单:"+buyOrder.getBuyorderNo()+"，退货出库下发库存服务========================:" + JSON.toJSONString(stockInfoDto));

        //更新库存服务
        warehouseStockService.updateStockInfo(stockInfoDto);

    }
}
