<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.InvoiceRedConfirmationMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationEntity">
    <!--@mbg.generated-->
    <!--@Table T_INVOICE_RED_CONFIRMATION-->
    <id column="INVOICE_RED_CONFIRMATION_ID" jdbcType="INTEGER" property="invoiceRedConfirmationId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="AFTER_SALE_BUSINESS_ORDER_TYPE" jdbcType="INTEGER" property="afterSaleBusinessOrderType" />
    <result column="BUSINESS_ORDER_ID" jdbcType="INTEGER" property="businessOrderId" />
    <result column="BUSINESS_ORDER_NO" jdbcType="VARCHAR" property="businessOrderNo" />
    <result column="AFTER_SALE_BUSINESS_ORDER_ID" jdbcType="INTEGER" property="afterSaleBusinessOrderId" />
    <result column="AFTER_SALE_BUSINESS_ORDER_NO" jdbcType="VARCHAR" property="afterSaleBusinessOrderNo" />
    <result column="BLUE_INVOICE_NO" jdbcType="VARCHAR" property="blueInvoiceNo" />
    <result column="BLUE_INVOICE_ID" jdbcType="INTEGER" property="blueInvoiceId" />
    <result column="RED_CONFIRMATION_STATUS" jdbcType="INTEGER" property="redConfirmationStatus" />
    <result column="RED_CONFIRMATION_SCOPE" jdbcType="INTEGER" property="redConfirmationScope" />
    <result column="SELLER" jdbcType="VARCHAR" property="seller" />
    <result column="SELLER_TAX_NUMBER" jdbcType="VARCHAR" property="sellerTaxNumber" />
    <result column="BUYER" jdbcType="VARCHAR" property="buyer" />
    <result column="BUYER_TAX_NUMBER" jdbcType="VARCHAR" property="buyerTaxNumber" />
    <result column="APPLY_TIME" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="APPLY_AMOUNT" jdbcType="DECIMAL" property="applyAmount" />
    <result column="APPLY_TAX_AMOUNT" jdbcType="DECIMAL" property="applyTaxAmount" />
    <result column="APPLY_PRICE_PLUS_TAXES" jdbcType="DECIMAL" property="applyPricePlusTaxes" />
    <result column="APPLY_REASON" jdbcType="VARCHAR" property="applyReason" />
    <result column="INPUT_PERSON_TYPE" jdbcType="INTEGER" property="inputPersonType" />
    <result column="UUID" jdbcType="VARCHAR" property="uuid" />
    <result column="INVOICE_RED_CONFIRMATION_NO" jdbcType="VARCHAR" property="invoiceRedConfirmationNo" />
    <result column="TAXES_RED_CONFIRMATION_STATUS" jdbcType="VARCHAR" property="taxesRedConfirmationStatus" />
    <result column="CONFIRM_TIME" jdbcType="TIMESTAMP" property="confirmTime" />
    <result column="CONFIRM_PERSON_TYPE" jdbcType="INTEGER" property="confirmPersonType" />
    <result column="SELLER_BUYER_TYPE" jdbcType="INTEGER" property="sellerBuyerType" />
    <result column="VALUE_ADDED_TAX_STATUS" jdbcType="VARCHAR" property="valueAddedTaxStatus" />
    <result column="CONSUMPTION_TAX_STATUS" jdbcType="VARCHAR" property="consumptionTaxStatus" />
    <result column="ENTER_ACCOUNT_STATUS" jdbcType="VARCHAR" property="enterAccountStatus" />
    <result column="ALREADY_RED_INVOICE_FLAG" jdbcType="VARCHAR" property="alreadyRedInvoiceFlag" />
    <result column="RED_INVOICE_NO" jdbcType="VARCHAR" property="redInvoiceNo" />
    <result column="RED_INVOICE_ID" jdbcType="INTEGER" property="redInvoiceId" />
    <result column="OPEN_RED_INVOICE_TIME" jdbcType="TIMESTAMP" property="openRedInvoiceTime" />
    <result column="API_DATA_UPDATE_TIME" jdbcType="TIMESTAMP" property="apiDataUpdateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    INVOICE_RED_CONFIRMATION_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, AFTER_SALE_BUSINESS_ORDER_TYPE, BUSINESS_ORDER_ID, BUSINESS_ORDER_NO,
    AFTER_SALE_BUSINESS_ORDER_ID, AFTER_SALE_BUSINESS_ORDER_NO, BLUE_INVOICE_NO, BLUE_INVOICE_ID,
    RED_CONFIRMATION_STATUS, RED_CONFIRMATION_SCOPE, SELLER, SELLER_TAX_NUMBER, BUYER,
    BUYER_TAX_NUMBER, APPLY_TIME, APPLY_AMOUNT, APPLY_TAX_AMOUNT, APPLY_PRICE_PLUS_TAXES,
    APPLY_REASON, INPUT_PERSON_TYPE, UUID, INVOICE_RED_CONFIRMATION_NO, TAXES_RED_CONFIRMATION_STATUS,
    CONFIRM_TIME, CONFIRM_PERSON_TYPE, SELLER_BUYER_TYPE, VALUE_ADDED_TAX_STATUS, CONSUMPTION_TAX_STATUS,
    ENTER_ACCOUNT_STATUS, ALREADY_RED_INVOICE_FLAG, RED_INVOICE_NO, RED_INVOICE_ID, OPEN_RED_INVOICE_TIME,
    API_DATA_UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_INVOICE_RED_CONFIRMATION
    where INVOICE_RED_CONFIRMATION_ID = #{invoiceRedConfirmationId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_INVOICE_RED_CONFIRMATION
    where INVOICE_RED_CONFIRMATION_ID = #{invoiceRedConfirmationId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="INVOICE_RED_CONFIRMATION_ID" keyProperty="invoiceRedConfirmationId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_RED_CONFIRMATION (ADD_TIME, MOD_TIME, CREATOR,
    UPDATER, CREATOR_NAME, UPDATER_NAME,
    AFTER_SALE_BUSINESS_ORDER_TYPE, BUSINESS_ORDER_ID,
    BUSINESS_ORDER_NO, AFTER_SALE_BUSINESS_ORDER_ID,
    AFTER_SALE_BUSINESS_ORDER_NO, BLUE_INVOICE_NO,
    BLUE_INVOICE_ID, RED_CONFIRMATION_STATUS, RED_CONFIRMATION_SCOPE,
    SELLER, SELLER_TAX_NUMBER, BUYER,
    BUYER_TAX_NUMBER, APPLY_TIME, APPLY_AMOUNT,
    APPLY_TAX_AMOUNT, APPLY_PRICE_PLUS_TAXES, APPLY_REASON,
    INPUT_PERSON_TYPE, UUID, INVOICE_RED_CONFIRMATION_NO,
    TAXES_RED_CONFIRMATION_STATUS, CONFIRM_TIME,
    CONFIRM_PERSON_TYPE, SELLER_BUYER_TYPE, VALUE_ADDED_TAX_STATUS,
    CONSUMPTION_TAX_STATUS, ENTER_ACCOUNT_STATUS,
    ALREADY_RED_INVOICE_FLAG, RED_INVOICE_NO, RED_INVOICE_ID,
    OPEN_RED_INVOICE_TIME, API_DATA_UPDATE_TIME
    )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
    #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR},
    #{afterSaleBusinessOrderType,jdbcType=INTEGER}, #{businessOrderId,jdbcType=INTEGER},
    #{businessOrderNo,jdbcType=VARCHAR}, #{afterSaleBusinessOrderId,jdbcType=INTEGER},
    #{afterSaleBusinessOrderNo,jdbcType=VARCHAR}, #{blueInvoiceNo,jdbcType=VARCHAR},
    #{blueInvoiceId,jdbcType=INTEGER}, #{redConfirmationStatus,jdbcType=INTEGER}, #{redConfirmationScope,jdbcType=INTEGER},
    #{seller,jdbcType=VARCHAR}, #{sellerTaxNumber,jdbcType=VARCHAR}, #{buyer,jdbcType=VARCHAR},
    #{buyerTaxNumber,jdbcType=VARCHAR}, #{applyTime,jdbcType=TIMESTAMP}, #{applyAmount,jdbcType=DECIMAL},
    #{applyTaxAmount,jdbcType=DECIMAL}, #{applyPricePlusTaxes,jdbcType=DECIMAL}, #{applyReason,jdbcType=VARCHAR},
    #{inputPersonType,jdbcType=INTEGER}, #{uuid,jdbcType=VARCHAR}, #{invoiceRedConfirmationNo,jdbcType=VARCHAR},
    #{taxesRedConfirmationStatus,jdbcType=VARCHAR}, #{confirmTime,jdbcType=TIMESTAMP},
    #{confirmPersonType,jdbcType=INTEGER}, #{sellerBuyerType,jdbcType=INTEGER}, #{valueAddedTaxStatus,jdbcType=VARCHAR},
    #{consumptionTaxStatus,jdbcType=VARCHAR}, #{enterAccountStatus,jdbcType=VARCHAR},
    #{alreadyRedInvoiceFlag,jdbcType=VARCHAR}, #{redInvoiceNo,jdbcType=VARCHAR}, #{redInvoiceId,jdbcType=INTEGER},
    #{openRedInvoiceTime,jdbcType=TIMESTAMP}, #{apiDataUpdateTime,jdbcType=TIMESTAMP}
    )
  </insert>
  <insert id="insertSelective" keyColumn="INVOICE_RED_CONFIRMATION_ID" keyProperty="invoiceRedConfirmationId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_RED_CONFIRMATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="afterSaleBusinessOrderType != null">
        AFTER_SALE_BUSINESS_ORDER_TYPE,
      </if>
      <if test="businessOrderId != null">
        BUSINESS_ORDER_ID,
      </if>
      <if test="businessOrderNo != null">
        BUSINESS_ORDER_NO,
      </if>
      <if test="afterSaleBusinessOrderId != null">
        AFTER_SALE_BUSINESS_ORDER_ID,
      </if>
      <if test="afterSaleBusinessOrderNo != null">
        AFTER_SALE_BUSINESS_ORDER_NO,
      </if>
      <if test="blueInvoiceNo != null">
        BLUE_INVOICE_NO,
      </if>
      <if test="blueInvoiceId != null">
        BLUE_INVOICE_ID,
      </if>
      <if test="redConfirmationStatus != null">
        RED_CONFIRMATION_STATUS,
      </if>
      <if test="redConfirmationScope != null">
        RED_CONFIRMATION_SCOPE,
      </if>
      <if test="seller != null">
        SELLER,
      </if>
      <if test="sellerTaxNumber != null">
        SELLER_TAX_NUMBER,
      </if>
      <if test="buyer != null">
        BUYER,
      </if>
      <if test="buyerTaxNumber != null">
        BUYER_TAX_NUMBER,
      </if>
      <if test="applyTime != null">
        APPLY_TIME,
      </if>
      <if test="applyAmount != null">
        APPLY_AMOUNT,
      </if>
      <if test="applyTaxAmount != null">
        APPLY_TAX_AMOUNT,
      </if>
      <if test="applyPricePlusTaxes != null">
        APPLY_PRICE_PLUS_TAXES,
      </if>
      <if test="applyReason != null">
        APPLY_REASON,
      </if>
      <if test="inputPersonType != null">
        INPUT_PERSON_TYPE,
      </if>
      <if test="uuid != null">
        UUID,
      </if>
      <if test="invoiceRedConfirmationNo != null">
        INVOICE_RED_CONFIRMATION_NO,
      </if>
      <if test="taxesRedConfirmationStatus != null">
        TAXES_RED_CONFIRMATION_STATUS,
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME,
      </if>
      <if test="confirmPersonType != null">
        CONFIRM_PERSON_TYPE,
      </if>
      <if test="sellerBuyerType != null">
        SELLER_BUYER_TYPE,
      </if>
      <if test="valueAddedTaxStatus != null">
        VALUE_ADDED_TAX_STATUS,
      </if>
      <if test="consumptionTaxStatus != null">
        CONSUMPTION_TAX_STATUS,
      </if>
      <if test="enterAccountStatus != null">
        ENTER_ACCOUNT_STATUS,
      </if>
      <if test="alreadyRedInvoiceFlag != null">
        ALREADY_RED_INVOICE_FLAG,
      </if>
      <if test="redInvoiceNo != null">
        RED_INVOICE_NO,
      </if>
      <if test="redInvoiceId != null">
        RED_INVOICE_ID,
      </if>
      <if test="openRedInvoiceTime != null">
        OPEN_RED_INVOICE_TIME,
      </if>
      <if test="apiDataUpdateTime != null">
        API_DATA_UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleBusinessOrderType != null">
        #{afterSaleBusinessOrderType,jdbcType=INTEGER},
      </if>
      <if test="businessOrderId != null">
        #{businessOrderId,jdbcType=INTEGER},
      </if>
      <if test="businessOrderNo != null">
        #{businessOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleBusinessOrderId != null">
        #{afterSaleBusinessOrderId,jdbcType=INTEGER},
      </if>
      <if test="afterSaleBusinessOrderNo != null">
        #{afterSaleBusinessOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="blueInvoiceNo != null">
        #{blueInvoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="blueInvoiceId != null">
        #{blueInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="redConfirmationStatus != null">
        #{redConfirmationStatus,jdbcType=INTEGER},
      </if>
      <if test="redConfirmationScope != null">
        #{redConfirmationScope,jdbcType=INTEGER},
      </if>
      <if test="seller != null">
        #{seller,jdbcType=VARCHAR},
      </if>
      <if test="sellerTaxNumber != null">
        #{sellerTaxNumber,jdbcType=VARCHAR},
      </if>
      <if test="buyer != null">
        #{buyer,jdbcType=VARCHAR},
      </if>
      <if test="buyerTaxNumber != null">
        #{buyerTaxNumber,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyAmount != null">
        #{applyAmount,jdbcType=DECIMAL},
      </if>
      <if test="applyTaxAmount != null">
        #{applyTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="applyPricePlusTaxes != null">
        #{applyPricePlusTaxes,jdbcType=DECIMAL},
      </if>
      <if test="applyReason != null">
        #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="inputPersonType != null">
        #{inputPersonType,jdbcType=INTEGER},
      </if>
      <if test="uuid != null">
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="invoiceRedConfirmationNo != null">
        #{invoiceRedConfirmationNo,jdbcType=VARCHAR},
      </if>
      <if test="taxesRedConfirmationStatus != null">
        #{taxesRedConfirmationStatus,jdbcType=VARCHAR},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmPersonType != null">
        #{confirmPersonType,jdbcType=INTEGER},
      </if>
      <if test="sellerBuyerType != null">
        #{sellerBuyerType,jdbcType=INTEGER},
      </if>
      <if test="valueAddedTaxStatus != null">
        #{valueAddedTaxStatus,jdbcType=VARCHAR},
      </if>
      <if test="consumptionTaxStatus != null">
        #{consumptionTaxStatus,jdbcType=VARCHAR},
      </if>
      <if test="enterAccountStatus != null">
        #{enterAccountStatus,jdbcType=VARCHAR},
      </if>
      <if test="alreadyRedInvoiceFlag != null">
        #{alreadyRedInvoiceFlag,jdbcType=VARCHAR},
      </if>
      <if test="redInvoiceNo != null">
        #{redInvoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="redInvoiceId != null">
        #{redInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="openRedInvoiceTime != null">
        #{openRedInvoiceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="apiDataUpdateTime != null">
        #{apiDataUpdateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationEntity">
    <!--@mbg.generated-->
    update T_INVOICE_RED_CONFIRMATION
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleBusinessOrderType != null">
        AFTER_SALE_BUSINESS_ORDER_TYPE = #{afterSaleBusinessOrderType,jdbcType=INTEGER},
      </if>
      <if test="businessOrderId != null">
        BUSINESS_ORDER_ID = #{businessOrderId,jdbcType=INTEGER},
      </if>
      <if test="businessOrderNo != null">
        BUSINESS_ORDER_NO = #{businessOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleBusinessOrderId != null">
        AFTER_SALE_BUSINESS_ORDER_ID = #{afterSaleBusinessOrderId,jdbcType=INTEGER},
      </if>
      <if test="afterSaleBusinessOrderNo != null">
        AFTER_SALE_BUSINESS_ORDER_NO = #{afterSaleBusinessOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="blueInvoiceNo != null">
        BLUE_INVOICE_NO = #{blueInvoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="blueInvoiceId != null">
        BLUE_INVOICE_ID = #{blueInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="redConfirmationStatus != null">
        RED_CONFIRMATION_STATUS = #{redConfirmationStatus,jdbcType=INTEGER},
      </if>
      <if test="redConfirmationScope != null">
        RED_CONFIRMATION_SCOPE = #{redConfirmationScope,jdbcType=INTEGER},
      </if>
      <if test="seller != null">
        SELLER = #{seller,jdbcType=VARCHAR},
      </if>
      <if test="sellerTaxNumber != null">
        SELLER_TAX_NUMBER = #{sellerTaxNumber,jdbcType=VARCHAR},
      </if>
      <if test="buyer != null">
        BUYER = #{buyer,jdbcType=VARCHAR},
      </if>
      <if test="buyerTaxNumber != null">
        BUYER_TAX_NUMBER = #{buyerTaxNumber,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        APPLY_TIME = #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyAmount != null">
        APPLY_AMOUNT = #{applyAmount,jdbcType=DECIMAL},
      </if>
      <if test="applyTaxAmount != null">
        APPLY_TAX_AMOUNT = #{applyTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="applyPricePlusTaxes != null">
        APPLY_PRICE_PLUS_TAXES = #{applyPricePlusTaxes,jdbcType=DECIMAL},
      </if>
      <if test="applyReason != null">
        APPLY_REASON = #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="inputPersonType != null">
        INPUT_PERSON_TYPE = #{inputPersonType,jdbcType=INTEGER},
      </if>
      <if test="uuid != null">
        UUID = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="invoiceRedConfirmationNo != null">
        INVOICE_RED_CONFIRMATION_NO = #{invoiceRedConfirmationNo,jdbcType=VARCHAR},
      </if>
      <if test="taxesRedConfirmationStatus != null">
        TAXES_RED_CONFIRMATION_STATUS = #{taxesRedConfirmationStatus,jdbcType=VARCHAR},
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmPersonType != null">
        CONFIRM_PERSON_TYPE = #{confirmPersonType,jdbcType=INTEGER},
      </if>
      <if test="sellerBuyerType != null">
        SELLER_BUYER_TYPE = #{sellerBuyerType,jdbcType=INTEGER},
      </if>
      <if test="valueAddedTaxStatus != null">
        VALUE_ADDED_TAX_STATUS = #{valueAddedTaxStatus,jdbcType=VARCHAR},
      </if>
      <if test="consumptionTaxStatus != null">
        CONSUMPTION_TAX_STATUS = #{consumptionTaxStatus,jdbcType=VARCHAR},
      </if>
      <if test="enterAccountStatus != null">
        ENTER_ACCOUNT_STATUS = #{enterAccountStatus,jdbcType=VARCHAR},
      </if>
      <if test="alreadyRedInvoiceFlag != null">
        ALREADY_RED_INVOICE_FLAG = #{alreadyRedInvoiceFlag,jdbcType=VARCHAR},
      </if>
      <if test="redInvoiceNo != null">
        RED_INVOICE_NO = #{redInvoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="redInvoiceId != null">
        RED_INVOICE_ID = #{redInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="openRedInvoiceTime != null">
        OPEN_RED_INVOICE_TIME = #{openRedInvoiceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="apiDataUpdateTime != null">
        API_DATA_UPDATE_TIME = #{apiDataUpdateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where INVOICE_RED_CONFIRMATION_ID = #{invoiceRedConfirmationId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationEntity">
    <!--@mbg.generated-->
    update T_INVOICE_RED_CONFIRMATION
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
    MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    CREATOR = #{creator,jdbcType=INTEGER},
    UPDATER = #{updater,jdbcType=INTEGER},
    CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
    UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    AFTER_SALE_BUSINESS_ORDER_TYPE = #{afterSaleBusinessOrderType,jdbcType=INTEGER},
    BUSINESS_ORDER_ID = #{businessOrderId,jdbcType=INTEGER},
    BUSINESS_ORDER_NO = #{businessOrderNo,jdbcType=VARCHAR},
    AFTER_SALE_BUSINESS_ORDER_ID = #{afterSaleBusinessOrderId,jdbcType=INTEGER},
    AFTER_SALE_BUSINESS_ORDER_NO = #{afterSaleBusinessOrderNo,jdbcType=VARCHAR},
    BLUE_INVOICE_NO = #{blueInvoiceNo,jdbcType=VARCHAR},
    BLUE_INVOICE_ID = #{blueInvoiceId,jdbcType=INTEGER},
    RED_CONFIRMATION_STATUS = #{redConfirmationStatus,jdbcType=INTEGER},
    RED_CONFIRMATION_SCOPE = #{redConfirmationScope,jdbcType=INTEGER},
    SELLER = #{seller,jdbcType=VARCHAR},
    SELLER_TAX_NUMBER = #{sellerTaxNumber,jdbcType=VARCHAR},
    BUYER = #{buyer,jdbcType=VARCHAR},
    BUYER_TAX_NUMBER = #{buyerTaxNumber,jdbcType=VARCHAR},
    APPLY_TIME = #{applyTime,jdbcType=TIMESTAMP},
    APPLY_AMOUNT = #{applyAmount,jdbcType=DECIMAL},
    APPLY_TAX_AMOUNT = #{applyTaxAmount,jdbcType=DECIMAL},
    APPLY_PRICE_PLUS_TAXES = #{applyPricePlusTaxes,jdbcType=DECIMAL},
    APPLY_REASON = #{applyReason,jdbcType=VARCHAR},
    INPUT_PERSON_TYPE = #{inputPersonType,jdbcType=INTEGER},
    UUID = #{uuid,jdbcType=VARCHAR},
    INVOICE_RED_CONFIRMATION_NO = #{invoiceRedConfirmationNo,jdbcType=VARCHAR},
    TAXES_RED_CONFIRMATION_STATUS = #{taxesRedConfirmationStatus,jdbcType=VARCHAR},
    CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
    CONFIRM_PERSON_TYPE = #{confirmPersonType,jdbcType=INTEGER},
    SELLER_BUYER_TYPE = #{sellerBuyerType,jdbcType=INTEGER},
    VALUE_ADDED_TAX_STATUS = #{valueAddedTaxStatus,jdbcType=VARCHAR},
    CONSUMPTION_TAX_STATUS = #{consumptionTaxStatus,jdbcType=VARCHAR},
    ENTER_ACCOUNT_STATUS = #{enterAccountStatus,jdbcType=VARCHAR},
    ALREADY_RED_INVOICE_FLAG = #{alreadyRedInvoiceFlag,jdbcType=VARCHAR},
    RED_INVOICE_NO = #{redInvoiceNo,jdbcType=VARCHAR},
    RED_INVOICE_ID = #{redInvoiceId,jdbcType=INTEGER},
    OPEN_RED_INVOICE_TIME = #{openRedInvoiceTime,jdbcType=TIMESTAMP},
    API_DATA_UPDATE_TIME = #{apiDataUpdateTime,jdbcType=TIMESTAMP}
    where INVOICE_RED_CONFIRMATION_ID = #{invoiceRedConfirmationId,jdbcType=INTEGER}
  </update>

  <select id="findByAll" resultType="com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationDto">
    select
    TIRC.ADD_TIME, TIRC.MOD_TIME, TIRC.CREATOR, TIRC.UPDATER, TIRC.CREATOR_NAME, TIRC.UPDATER_NAME, TIRC.INVOICE_RED_CONFIRMATION_ID,
    TIRC.AFTER_SALE_BUSINESS_ORDER_TYPE, BUSINESS_ORDER_ID, BUSINESS_ORDER_NO, AFTER_SALE_BUSINESS_ORDER_ID,
    AFTER_SALE_BUSINESS_ORDER_NO, BLUE_INVOICE_NO, BLUE_INVOICE_ID, RED_CONFIRMATION_STATUS, RED_CONFIRMATION_SCOPE,
    SELLER, SELLER_TAX_NUMBER, BUYER, BUYER_TAX_NUMBER, APPLY_TIME, APPLY_AMOUNT, APPLY_TAX_AMOUNT,
    APPLY_PRICE_PLUS_TAXES, APPLY_REASON, INPUT_PERSON_TYPE, TIRC.UUID, TIRC.INVOICE_RED_CONFIRMATION_NO,
    TAXES_RED_CONFIRMATION_STATUS, CONFIRM_TIME, CONFIRM_PERSON_TYPE, SELLER_BUYER_TYPE, VALUE_ADDED_TAX_STATUS,
    CONSUMPTION_TAX_STATUS, ENTER_ACCOUNT_STATUS, ALREADY_RED_INVOICE_FLAG, RED_INVOICE_NO, RED_INVOICE_ID,
    OPEN_RED_INVOICE_TIME, API_DATA_UPDATE_TIME,SUM(IFNULL(TIRCI.THIS_TIME_SURPLUS_AMOUNT,0)) surplusAmount,
    SUM(IFNULL(TIRCI.THIS_TIME_SURPLUS_UAX_AMOUNT,0)) surplusUaxAmount,SUM(IFNULL(TIRCI.THIS_TIME_SURPLUS_PRICE_PLUS_TAXES,0)) surplusPricePlusTaxes,TSOD.TITLE afterSaleTitle,
    TIMESTAMPDIFF(HOUR, NOW() - INTERVAL 3 DAY, APPLY_TIME) as remainingTime

    from T_INVOICE_RED_CONFIRMATION TIRC left join T_INVOICE_RED_CONFIRMATION_ITEM TIRCI
    on TIRC.INVOICE_RED_CONFIRMATION_ID = TIRCI.INVOICE_RED_CONFIRMATION_ID
    left join T_AFTER_SALES TAS  on TAS.AFTER_SALES_ID = TIRC.AFTER_SALE_BUSINESS_ORDER_ID
    left join T_SYS_OPTION_DEFINITION TSOD  on TSOD.SYS_OPTION_DEFINITION_ID = TAS.TYPE
    <where>
      <if test="invoiceRedConfirmationNo !=null and invoiceRedConfirmationNo!=''">
        and TIRC.INVOICE_RED_CONFIRMATION_NO like CONCAT('%',#{invoiceRedConfirmationNo,jdbcType=VARCHAR},'%')
      </if>
      <if test="afterSaleBusinessOrderNo !=null and afterSaleBusinessOrderNo!=''">
        and TIRC.AFTER_SALE_BUSINESS_ORDER_NO like CONCAT('%',#{afterSaleBusinessOrderNo,jdbcType=VARCHAR},'%')
      </if>
      <if test="businessOrderNo !=null and businessOrderNo!=''">
        and TIRC.BUSINESS_ORDER_NO like CONCAT('%',#{businessOrderNo,jdbcType=VARCHAR},'%')
      </if>
      <if test="redConfirmationStatus !=null ">
        and TIRC.RED_CONFIRMATION_STATUS = #{redConfirmationStatus,jdbcType=INTEGER}
      </if>
      <if test="buyer !=null and buyer!=''">
        and TIRC.BUYER like CONCAT('%',#{buyer,jdbcType=VARCHAR},'%')
      </if>
      <if test="taxesRedConfirmationStatus !=null and taxesRedConfirmationStatus!=''and taxesRedConfirmationStatus!='00'" >
        and TIRC.TAXES_RED_CONFIRMATION_STATUS = #{taxesRedConfirmationStatus}
      </if>
      <if test="redConfirmationStatusList !=null and redConfirmationStatusList.size()>0">
        and TIRC.RED_CONFIRMATION_STATUS in
        <foreach collection="redConfirmationStatusList" item="item" separator="," close=")" open="(">
          #{item,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="blueInvoiceNo !=null and blueInvoiceNo!=''" >
        and TIRC.BLUE_INVOICE_NO like CONCAT('%',#{blueInvoiceNo,jdbcType=VARCHAR},'%')
      </if>
      <if test="redInvoiceNo !=null and redInvoiceNo!=''" >
        and TIRC.RED_INVOICE_NO like CONCAT('%',#{redInvoiceNo,jdbcType=VARCHAR},'%')
      </if>
      <if test="applyReason !=null and applyReason!='' and applyReason!='00'" >
        and TIRC.APPLY_REASON =#{applyReason,jdbcType=VARCHAR}
      </if>
      <if test="redConfirmationScope !=null and redConfirmationScope!=-1" >
        and TIRC.RED_CONFIRMATION_SCOPE =#{redConfirmationScope,jdbcType=INTEGER}
      </if>
      <if test="inputPersonType !=null  and inputPersonType!=-1 " >
        and TIRC.INPUT_PERSON_TYPE =#{inputPersonType,jdbcType=INTEGER}
      </if>
      <if test="beginTime !=null and endTime!=null">
        and TIRC.APPLY_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP} and TIRC.APPLY_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
      </if>
      <if test="remainingTimeOfVoid != null and remainingTimeOfVoid == 1 and remainingTimeOfVoid != -1">
        and APPLY_TIME <![CDATA[<=]]> NOW() - INTERVAL 2 day
      </if>
      <if test="remainingTimeOfVoid != null and remainingTimeOfVoid == 2 and remainingTimeOfVoid != -1">
       and APPLY_TIME <![CDATA[>=]]> NOW() - INTERVAL 2 day
        and APPLY_TIME <![CDATA[<=]]> NOW() - INTERVAL 1 day
      </if>
      <if test="remainingTimeOfVoid != null and remainingTimeOfVoid == 3 and remainingTimeOfVoid != -1">
        and APPLY_TIME <![CDATA[>=]]>  NOW() - INTERVAL 1 day
        and APPLY_TIME <![CDATA[<=]]> NOW()
      </if>

    </where>
    group by TIRC.INVOICE_RED_CONFIRMATION_ID
    order by TIRC.INVOICE_RED_CONFIRMATION_ID desc
  </select>

<!--auto generated by MybatisCodeHelper on 2023-10-19-->
  <select id="initCheckByAfterSaleBusinessOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_INVOICE_RED_CONFIRMATION
    where
    RED_CONFIRMATION_STATUS not in (3,4)
    and AFTER_SALE_BUSINESS_ORDER_NO=#{afterSaleBusinessOrderNo,jdbcType=VARCHAR}
    and BLUE_INVOICE_NO=#{blueInvoiceNo,jdbcType=VARCHAR}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-10-23-->
  <select id="findRedConfirmationStatus" resultType="com.vedeng.erp.finance.dto.RedConfirmationStatusStatistics">
        select RED_CONFIRMATION_STATUS, count(1) num
        from T_INVOICE_RED_CONFIRMATION
        group by RED_CONFIRMATION_STATUS
    </select>

  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_INVOICE_RED_CONFIRMATION
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="AFTER_SALE_BUSINESS_ORDER_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterSaleBusinessOrderType != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.afterSaleBusinessOrderType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_ORDER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessOrderId != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.businessOrderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_ORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessOrderNo != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.businessOrderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="AFTER_SALE_BUSINESS_ORDER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterSaleBusinessOrderId != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.afterSaleBusinessOrderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="AFTER_SALE_BUSINESS_ORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterSaleBusinessOrderNo != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.afterSaleBusinessOrderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BLUE_INVOICE_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.blueInvoiceNo != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.blueInvoiceNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BLUE_INVOICE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.blueInvoiceId != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.blueInvoiceId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="RED_CONFIRMATION_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.redConfirmationStatus != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.redConfirmationStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="RED_CONFIRMATION_SCOPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.redConfirmationScope != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.redConfirmationScope,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SELLER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.seller != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.seller,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SELLER_TAX_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sellerTaxNumber != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.sellerTaxNumber,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUYER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.buyer != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.buyer,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUYER_TAX_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.buyerTaxNumber != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.buyerTaxNumber,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="APPLY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.applyTime != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.applyTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="APPLY_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.applyAmount != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.applyAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="APPLY_TAX_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.applyTaxAmount != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.applyTaxAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="APPLY_PRICE_PLUS_TAXES = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.applyPricePlusTaxes != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.applyPricePlusTaxes,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="APPLY_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.applyReason != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.applyReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INPUT_PERSON_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.inputPersonType != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.inputPersonType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UUID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.uuid != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.uuid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_RED_CONFIRMATION_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceRedConfirmationNo != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.invoiceRedConfirmationNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAXES_RED_CONFIRMATION_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.taxesRedConfirmationStatus != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.taxesRedConfirmationStatus,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONFIRM_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.confirmTime != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.confirmTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONFIRM_PERSON_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.confirmPersonType != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.confirmPersonType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SELLER_BUYER_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sellerBuyerType != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.sellerBuyerType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="VALUE_ADDED_TAX_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.valueAddedTaxStatus != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.valueAddedTaxStatus,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONSUMPTION_TAX_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.consumptionTaxStatus != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.consumptionTaxStatus,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ENTER_ACCOUNT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.enterAccountStatus != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.enterAccountStatus,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ALREADY_RED_INVOICE_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.alreadyRedInvoiceFlag != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.alreadyRedInvoiceFlag,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="RED_INVOICE_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.redInvoiceNo != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.redInvoiceNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="RED_INVOICE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.redInvoiceId != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.redInvoiceId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="OPEN_RED_INVOICE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.openRedInvoiceTime != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.openRedInvoiceTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="API_DATA_UPDATE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.apiDataUpdateTime != null">
            when INVOICE_RED_CONFIRMATION_ID = #{item.invoiceRedConfirmationId,jdbcType=INTEGER} then #{item.apiDataUpdateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where INVOICE_RED_CONFIRMATION_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.invoiceRedConfirmationId,jdbcType=INTEGER}
    </foreach>
  </update>

<!--auto generated by MybatisCodeHelper on 2023-10-25-->
  <select id="findUuid" resultType="java.lang.String">
    select UUID
    from T_INVOICE_RED_CONFIRMATION
    where UUID != ''
      and UUID is not null
  </select>
</mapper>