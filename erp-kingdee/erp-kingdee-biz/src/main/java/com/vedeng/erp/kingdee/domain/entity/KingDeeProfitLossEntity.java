package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description: 盈亏单
 * @author: yana.jiang
 * @date: 2022/11/10
 */
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_PROFIT_LOSS")
public class KingDeeProfitLossEntity extends BaseEntity {
    /**
    * id
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
    * 单据内码0：表示新增 非0：云星空系统单据FID值，表示修改
    */
    private String fId;

    /**
    * 单据类型填单据类型编码，默认：PK01_SYS
    */
    private String fBillTypeId;

    /**
    * fBillNo
    */
    private String fBillNo;

    /**
    * 库存组织写组织编码
    */
    private String fStockOrgId;

    /**
    * 贝登单据头ID
    */
    private String fQzokBddjtId;

    /**
    * 货主类型
    */
    private String fOwnerTypeIdHead;

    /**
    * 单据日期
    */
    private String fDate;

    /**
    * fDeptId
    */
    private String fDeptId;

    /**
    * fBillEntry
    */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fBillEntry;
}