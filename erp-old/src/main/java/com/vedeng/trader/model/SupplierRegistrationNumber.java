package com.vedeng.trader.model;

public class SupplierRegistrationNumber {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SUPPLIER_REGISTRATION_NUMBER.SUPPLIER_REGISTRATION_NUMBER_ID
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    private Integer supplierRegistrationNumberId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SUPPLIER_REGISTRATION_NUMBER.TRADER_ID
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    private Integer traderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SUPPLIER_REGISTRATION_NUMBER.REGISTRATION_NUMBER_ID
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    private Integer registrationNumberId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SUPPLIER_REGISTRATION_NUMBER.CHECK_STATUS
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    private Byte checkStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SUPPLIER_REGISTRATION_NUMBER.CHECK_USER_ID
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    private Integer checkUserId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SUPPLIER_REGISTRATION_NUMBER.CHECK_TIME
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    private Long checkTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SUPPLIER_REGISTRATION_NUMBER.IS_DELETE
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    private Boolean isDelete;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SUPPLIER_REGISTRATION_NUMBER.ADD_TIME
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    private Long addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SUPPLIER_REGISTRATION_NUMBER.UPDATE_TIME
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    private Long updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SUPPLIER_REGISTRATION_NUMBER.CREATOR
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SUPPLIER_REGISTRATION_NUMBER.UPDATOR
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    private Integer updator;


    private String checkTimeStr;

    public String getCheckTimeStr() {
        return checkTimeStr;
    }

    public void setCheckTimeStr(String checkTimeStr) {
        this.checkTimeStr = checkTimeStr;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.SUPPLIER_REGISTRATION_NUMBER_ID
     *
     * @return the value of T_SUPPLIER_REGISTRATION_NUMBER.SUPPLIER_REGISTRATION_NUMBER_ID
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public Integer getSupplierRegistrationNumberId() {
        return supplierRegistrationNumberId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.SUPPLIER_REGISTRATION_NUMBER_ID
     *
     * @param supplierRegistrationNumberId the value for T_SUPPLIER_REGISTRATION_NUMBER.SUPPLIER_REGISTRATION_NUMBER_ID
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public void setSupplierRegistrationNumberId(Integer supplierRegistrationNumberId) {
        this.supplierRegistrationNumberId = supplierRegistrationNumberId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.TRADER_ID
     *
     * @return the value of T_SUPPLIER_REGISTRATION_NUMBER.TRADER_ID
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public Integer getTraderId() {
        return traderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.TRADER_ID
     *
     * @param traderId the value for T_SUPPLIER_REGISTRATION_NUMBER.TRADER_ID
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.REGISTRATION_NUMBER_ID
     *
     * @return the value of T_SUPPLIER_REGISTRATION_NUMBER.REGISTRATION_NUMBER_ID
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public Integer getRegistrationNumberId() {
        return registrationNumberId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.REGISTRATION_NUMBER_ID
     *
     * @param registrationNumberId the value for T_SUPPLIER_REGISTRATION_NUMBER.REGISTRATION_NUMBER_ID
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public void setRegistrationNumberId(Integer registrationNumberId) {
        this.registrationNumberId = registrationNumberId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.CHECK_STATUS
     *
     * @return the value of T_SUPPLIER_REGISTRATION_NUMBER.CHECK_STATUS
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public Byte getCheckStatus() {
        return checkStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.CHECK_STATUS
     *
     * @param checkStatus the value for T_SUPPLIER_REGISTRATION_NUMBER.CHECK_STATUS
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public void setCheckStatus(Byte checkStatus) {
        this.checkStatus = checkStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.CHECK_USER_ID
     *
     * @return the value of T_SUPPLIER_REGISTRATION_NUMBER.CHECK_USER_ID
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public Integer getCheckUserId() {
        return checkUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.CHECK_USER_ID
     *
     * @param checkUserId the value for T_SUPPLIER_REGISTRATION_NUMBER.CHECK_USER_ID
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public void setCheckUserId(Integer checkUserId) {
        this.checkUserId = checkUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.CHECK_TIME
     *
     * @return the value of T_SUPPLIER_REGISTRATION_NUMBER.CHECK_TIME
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public Long getCheckTime() {
        return checkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.CHECK_TIME
     *
     * @param checkTime the value for T_SUPPLIER_REGISTRATION_NUMBER.CHECK_TIME
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public void setCheckTime(Long checkTime) {
        this.checkTime = checkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.IS_DELETE
     *
     * @return the value of T_SUPPLIER_REGISTRATION_NUMBER.IS_DELETE
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public Boolean getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.IS_DELETE
     *
     * @param isDelete the value for T_SUPPLIER_REGISTRATION_NUMBER.IS_DELETE
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public void setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.ADD_TIME
     *
     * @return the value of T_SUPPLIER_REGISTRATION_NUMBER.ADD_TIME
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.ADD_TIME
     *
     * @param addTime the value for T_SUPPLIER_REGISTRATION_NUMBER.ADD_TIME
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.UPDATE_TIME
     *
     * @return the value of T_SUPPLIER_REGISTRATION_NUMBER.UPDATE_TIME
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.UPDATE_TIME
     *
     * @param updateTime the value for T_SUPPLIER_REGISTRATION_NUMBER.UPDATE_TIME
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.CREATOR
     *
     * @return the value of T_SUPPLIER_REGISTRATION_NUMBER.CREATOR
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.CREATOR
     *
     * @param creator the value for T_SUPPLIER_REGISTRATION_NUMBER.CREATOR
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.UPDATOR
     *
     * @return the value of T_SUPPLIER_REGISTRATION_NUMBER.UPDATOR
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public Integer getUpdator() {
        return updator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SUPPLIER_REGISTRATION_NUMBER.UPDATOR
     *
     * @param updator the value for T_SUPPLIER_REGISTRATION_NUMBER.UPDATOR
     *
     * @mbggenerated Mon Nov 30 18:50:07 CST 2020
     */
    public void setUpdator(Integer updator) {
        this.updator = updator;
    }
}