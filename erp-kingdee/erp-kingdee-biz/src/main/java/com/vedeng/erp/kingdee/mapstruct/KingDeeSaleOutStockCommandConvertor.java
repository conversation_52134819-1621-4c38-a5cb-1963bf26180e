package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeSaleOutStockCommand;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.kingdee.mapstruct
 * @Date 2023/1/7 15:57
 */
@Mapper(componentModel = "spring")
public interface KingDeeSaleOutStockCommandConvertor extends BaseCommandMapStruct<KingDeeSaleOutStockCommand, KingDeeSaleOutStockDto> {

    /**
     * dto转化为金蝶进口入参
     *
     * @param dto KingDeeSaleOutStockDto
     * @return KingDeeSaleOutStockCommand
     */
    @Mapping(target = "fid", source = "fid")
    @Mapping(target = "FBillTypeID.FNumber", source = "FBillTypeID")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "f_QZOK_BDDJTID", source = "f_qzok_bddjtid")
    @Mapping(target = "f_QZOK_GSBM", source = "f_qzok_gsbm")
    @Mapping(target = "FDate", source = "FDate")
    @Mapping(target = "FSaleOrgId.FNumber", source = "FSaleOrgId")
    @Mapping(target = "FStockOrgId.FNumber", source = "FStockOrgId")
    @Mapping(target = "FCustomerID.FNumber", source = "FCustomerID")
    @Override
    KingDeeSaleOutStockCommand toCommand(KingDeeSaleOutStockDto dto);

    /**
     * 内部子dto转化
     *
     * @param dto KingDeeSaleOutStockDetailDto
     * @return KingDeeSaleOutStockCommand.FEntity
     */
    @Mapping(target = "FMaterialID.FNumber", source = "FMaterialID")
    @Mapping(target = "FRealQty", source = "FRealQty")
    @Mapping(target = "FIsFree", source = "FIsFree")
    @Mapping(target = "FTaxPrice", source = "FTaxPrice")
    @Mapping(target = "FEntryTaxRate", source = "FEntryTaxRate")
    @Mapping(target = "FStockID.FNumber", source = "FStockID")
    @Mapping(target = "f_QZOK_SFZF", source = "f_QZOK_SFZF")
    @Mapping(target = "f_QZOK_YSDDH", source = "f_QZOK_YSDDH")
    @Mapping(target = "f_QZOK_GSYWDH", source = "f_QZOK_GSYWDH")
    @Mapping(target = "f_QZOK_YWLX", source = "f_QZOK_YWLX")
    @Mapping(target = "f_QZOK_PCH", source = "f_QZOK_PCH")
    @Mapping(target = "f_QZOK_XLH", source = "f_QZOK_XLH")
    @Mapping(target = "f_QZOK_SFAT2", source = "f_QZOK_SFAT2")
    @Mapping(target = "f_QZOK_BDDJHID", source = "f_QZOK_BDDJHID")
    @Mapping(target = "FENTRYID", source = "FEntryId")
    KingDeeSaleOutStockCommand.FEntity toCommand(KingDeeSaleOutStockDetailDto dto);

}
