package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.finance.dto.BankBillIgnoreRecordDto;
import com.vedeng.erp.finance.service.BankBillIgnoreRecordApiService;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.batch.repository.BatchBankBillDtoMapper;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayVedengBankDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillEntryDto;
import com.vedeng.erp.kingdee.enums.*;
import com.vedeng.erp.kingdee.repository.KingDeePayVedengBankRepository;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 银行忽略流水（收款单）
 */
@Service
@Slf4j
public class IgnoreBankReceiveBillProcessor implements ItemProcessor<BatchBankBillDto, KingDeeReceiveBillDto> {

    @Resource
    private KingDeePayVedengBankRepository kingDeePayVedengBankRepository;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private BankBillIgnoreRecordApiService bankBillIgnoreRecordApiService;

    @Autowired
    private BatchBankBillDtoMapper batchBankBillDtoMapper;


    /**
     * 销售收款
     */
    private static final String FPURPOSEID_SALE = "SFKYT01_SYS";

    /**
     * 其他收入
     */
    private static final String FPURPOSEID_OTHER_INCOME = "SFKYT07_SYS";

    /**
     * 交易类型
     */
    private static final String FQZOKJYLX = "收入";

    @Override
    public KingDeeReceiveBillDto process(BatchBankBillDto batchBankBillDto) throws Exception {
        log.info("【金蝶收款单】金蝶处理银行忽略收款流水信息{},", JSON.toJSONString(batchBankBillDto));
        BankBillIgnoreRecordDto ignoreRecordDto = bankBillIgnoreRecordApiService.selectByBankBillId(batchBankBillDto.getBankBillId());
        if (Objects.isNull(ignoreRecordDto)){
            log.info("【金蝶收款单】bankBillId:{} 未查询到忽略项配置",batchBankBillDto.getBankBillId());
            return null;
        }
        Integer bankTag = batchBankBillDto.getBankTag();
        // 往来单位类型
        String contactUnitType = ignoreRecordDto.getContactUnitType();
        // 往来单位编号
        String contactUnitNo = ignoreRecordDto.getContactUnitNo();
        // 交易主体
        String tradeSubject = ignoreRecordDto.getTradeSubject();
        //忽略银行流水处理
        KingDeeReceiveBillDto kingdeeReceiveBillDto = new KingDeeReceiveBillDto();
        //单据编号
        kingdeeReceiveBillDto.setFBillNo(KingDeeConstant.IGNORE + batchBankBillDto.getBankBillId());
        //单据日期
        kingdeeReceiveBillDto.setFDate(DateUtil.formatDateTime(batchBankBillDto.getRealTrandatetime()));// VDERP-16088  款对接金蝶业务日期规则调整-精确到时分秒
        kingdeeReceiveBillDto.setFQzokPzgsywdh(batchBankBillDto.getOrderNo());
        //流水号
        kingdeeReceiveBillDto.setFQzokLsh(batchBankBillDto.getTranFlow());
        //erp银行流水id
        kingdeeReceiveBillDto.setBankBillId(batchBankBillDto.getBankBillId());
        kingdeeReceiveBillDto.setFId(ErpConstant.ZERO.toString());
        if (KingDeeFormConstant.BD_SUPPLIER.contains(contactUnitType)){
            // 供应商的付款退款单不走这边拦截掉，他走的是IgnoredPayRefundBillProcessor
            return null;
        }
        // 业务类型
        String fBillTypeId = KingDeeFormConstant.BD_CUSTOMER.contains(contactUnitType)?
                KingDeeReceiveBillTypeEnum.SALE_RECEIVE.getCode() : KingDeeReceiveBillTypeEnum.OTHER_RECEIVE.getCode();
        kingdeeReceiveBillDto.setFBillTypeId(fBillTypeId);
        //往来单位类型
        kingdeeReceiveBillDto.setFContactUnitType(contactUnitType);
        //往来单位
        kingdeeReceiveBillDto.setFContactUnit(contactUnitNo);
        if (StringUtils.isEmpty(kingdeeReceiveBillDto.getFContactUnit())) {
            log.error("【金蝶收款单】银行忽略收款流水推送失败！未查询到往来单位信息bankBillId:{}", batchBankBillDto.getBankBillId());
            return null;
        }
        //交易主体
        kingdeeReceiveBillDto.setFQzokJyzt(tradeSubject);
        //交易类型
        kingdeeReceiveBillDto.setFQzokJylx(FQZOKJYLX);
        //交易方式
        String fQzokJyfs = KingDeeTraderModeEnum.BANK_PAY.getDesc();
        // 结算方式
        String fSettletypeid = KingDeePayBillSettleTypeEnum.TELEGRAPHIC_SETTLETYPE.getCode();
        if (bankTag == KingDeeBankTagEnum.ALI_PAY.getCode()){
            // 支付宝
            fQzokJyfs = KingDeeTraderModeEnum.ALI_PAY.getDesc();
            fSettletypeid = KingDeePayBillSettleTypeEnum.ALIPAY_SETTLETYPE.getCode();
        }else if (bankTag == KingDeeBankTagEnum.WE_CHAT_PAY.getCode()){
            // 微信
            fQzokJyfs = KingDeeTraderModeEnum.WE_CHAT_PAY.getDesc();
            fSettletypeid = KingDeePayBillSettleTypeEnum.WECHAT_SETTLETYPE.getCode();
        }
        //交易方式
        kingdeeReceiveBillDto.setFQzokJyfs(fQzokJyfs);

        List<KingDeeReceiveBillEntryDto> kingdeeReceiveBillEntryDtos = new ArrayList<>();
        KingDeeReceiveBillEntryDto kingdeeReceiveBillEntryDto = new KingDeeReceiveBillEntryDto();
        //结算方式
        KingDeePayVedengBankDto kingDeePayVedengBankDto = queryBankInfo(bankTag);
        if(Objects.isNull(kingDeePayVedengBankDto)){
            log.error("【金蝶收款单】银行忽略收款流水推送失败!流水未查询到我方银行信息{},",bankTag);
            return null;
        }
        // 交易方式
        kingdeeReceiveBillEntryDto.setFSettleTypeId(fSettletypeid);

        // 用途
        String purPoseid = FPURPOSEID_SALE;
        if (KingDeeReceiveBillTypeEnum.OTHER_RECEIVE.getCode().equals(fBillTypeId)){
            purPoseid = FPURPOSEID_OTHER_INCOME;
        }
        kingdeeReceiveBillEntryDto.setFPurposeId(purPoseid);
        // 我方银行账号
        kingdeeReceiveBillEntryDto.setFAccountId(kingDeePayVedengBankDto.getPayBankNo());
        if (Objects.isNull(batchBankBillDto.getAmt())){
            log.error("【金蝶收款单】查询到银行流水总额为空，{}",JSON.toJSONString(batchBankBillDto));
            return null;
        }
        kingdeeReceiveBillEntryDto.setFRecTotalAmountFor(batchBankBillDto.getAmt().subtract(batchBankBillDto.getMatchedAmount()).toString());
        kingdeeReceiveBillEntryDto.setFQzokBddjhid("");
        kingdeeReceiveBillEntryDto.setFQzokYsddh("");
        kingdeeReceiveBillEntryDto.setFQzokGsywdh("");
        kingdeeReceiveBillEntryDto.setFQzokYwlx("其他业务收款");
        kingdeeReceiveBillEntryDto.setFQzokSkyw(KingdeeIgnoreBillTypeEnums.matchCodeById(batchBankBillDto.getMatchedObject()));
        // 手续费
        kingdeeReceiveBillEntryDto.setFHandlingChargeFor("0");

        kingdeeReceiveBillEntryDtos.add(kingdeeReceiveBillEntryDto);
        kingdeeReceiveBillDto.setFReceiveBillEntry(kingdeeReceiveBillEntryDtos);
        boolean isExist = kingDeeBaseApi.isExist(kingdeeReceiveBillDto);
        if (isExist){
            log.info("【金蝶收款单】金蝶处理银行忽略收款流水信息{},已经推送过金蝶", JSON.toJSONString(kingdeeReceiveBillDto));
            return null;
        }
        return kingdeeReceiveBillDto;
    }

    /**
     * 获取手续费
     * @param batchBankBillDto
     * @return
     */
    public String getFee(BatchBankBillDto batchBankBillDto) {
        BatchBankBillDto fee = null;
        if (batchBankBillDto.getBankTag() == 4 && !StringUtils.isEmpty(batchBankBillDto.getCapitalSearchFlow())) {
            fee = batchBankBillDtoMapper.getAliFeeByCapitalSearchFlow(batchBankBillDto.getCapitalSearchFlow());
            log.info("{}金蝶处理银行忽略收款流水信息,获取到ali费用信息：{}",batchBankBillDto.getCapitalSearchFlow(),fee);
        }
        if (batchBankBillDto.getBankTag() == 5) {
            String feeTranFlow = batchBankBillDto.getTranFlow() + "_fee";
            fee = batchBankBillDtoMapper.getWeChatFeeBankBillByTranFlow(feeTranFlow);
            log.info("{}金蝶处理银行忽略收款流水信息,获取到wechat费用信息：{}",feeTranFlow,fee);
        }
        return Objects.isNull(fee) ? null : fee.getAmt().toString();
    }


    /**
     * 根据流水类型查询银行信息
     * @param bankType
     * @return
     */
    private KingDeePayVedengBankDto queryBankInfo(Integer bankType){
        KingDeePayVedengBankDto kingDeePayVedengBankDto = new KingDeePayVedengBankDto();
        switch (bankType){
            case 1:
                //建设银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.SIX);
                break;
            case 2:
                //南京银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.TWO);
                break;
            case 3:
                //中国银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.ONE);
                break;
            case 4:
                //支付宝
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.FOUR);
                break;
            case 5:
                //微信
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.FIVE);
                break;
            case 6:
                //交通银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.THREE);
                break;
            case 7:
                //民生银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.SEVEN);
                break;
            default:
                log.info("银行流水类型未查对应银行编码");
                return null;
        }
        return kingDeePayVedengBankDto;
    }
}
