package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchVirtualWarehouseLogDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.processor.*;
import com.vedeng.erp.kingdee.batch.tasklet.PushInvoiceVoucherTasklet;
import com.vedeng.erp.kingdee.batch.tasklet.SaleOrderBlueInvoiceWarehouseOutTasklet;
import com.vedeng.erp.kingdee.batch.tasklet.SaleOrderInvoiceWarehouseOutInTasklet;
import com.vedeng.erp.kingdee.batch.writer.*;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDto;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatPlainAndSpecialInvoiceDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.item.support.CompositeItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * 销售单实物商品正向流程
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/6 11:20
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class SaleOrderBatchJob extends BaseJob {

    @Autowired
    private BatchSaleOutStockProcessor batchSaleOutStockProcessor;
    @Autowired
    private BatchSaleOutStockWriter batchSaleOutStockWriter;
    @Autowired
    private BatchSaleOrderReceiveProcessor batchSaleOrderReceiveProcessor;
    @Autowired
    private BatchSaleOrderReceiveWriter batchSaleOrderReceiveWriter;
    @Autowired
    private BatchSaleOrderBlueInvoiceProcessor batchSaleOrderBlueInvoiceProcessor;
    @Autowired
    private BatchSaleOrderBlueInvoiceWriter batchSaleOrderBlueInvoiceWriter;
    @Autowired
    private CommonFileDataWriter commonFileDataWriter;
    @Autowired
    private PushInvoiceVoucherTasklet pushInvoiceVoucherTasklet;
    @Autowired
    private BatchVirtualWarehouseLogProcessor batchVirtualWarehouseLogProcessorService;
    @Autowired
    private BatchVirtualWarehouseLogWriter batchVirtualWarehouseLogWriterService;
    @Autowired
    private BatchSaleOutAcceptanceFormProcessor batchSaleOutAcceptanceFormProcessor;
    @Autowired
    private BatchHistorySaleBlueInvoiceCompositeProcessor batchHistorySaleBlueInvoiceCompositeProcessor;
    @Autowired
    private SaleOrderBlueInvoiceWarehouseOutTasklet saleOrderBlueInvoiceWarehouseOutTasklet;
    @Autowired
    private SaleOrderInvoiceWarehouseOutInTasklet saleOrderInvoiceWarehouseOutInTasklet;
    @Autowired
    private BatchGiftSaleOrderReceiveProcessor batchGiftSaleOrderReceiveProcessor;

    @Value("${findGiftAndHaveBlueInvoiceTime}")
    private Long findGiftAndHaveBlueInvoiceTime;

    public Job saleOrderPhysicalGoodsFlowJob() {
        return jobBuilderFactory.get("saleOrderPhysicalGoodsBatchJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(invoiceVirture())
                .next(bindBlueInvoiceAndWarehouseInRelation())
                .next(bindRedInvoiceAndWarehouseInRelation())
                .next(warehouseOutPush())
                .next(acceptanceOutForm())
                .next(receivableOrderPush())
                .next(blueInvoicePush())
                .next(saveInvoiceVoucher())
                .build();
    }

    public Job saleOrderOnlyOutJob() {
        return jobBuilderFactory.get("销售仅推送出库")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(warehouseOutPush())
                .build();
    }

    public Job saleGiftPay() {
        return jobBuilderFactory.get("销售赠品应付单")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(giftPay())
                .next(wholeGiftPay())
                .build();
    }

    private Step invoiceVirture() {
        return stepBuilderFactory.get("红票生成虚拟出入库单")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, BatchVirtualWarehouseLogDto>chunk(1)
                // 捕捉到异常就重试,重试3次还是异常,JOB就停止并标志失败
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleorderInvoiceVirtureReader(null, null))
                .processor(batchVirtualWarehouseLogProcessorService)
                .writer(batchVirtualWarehouseLogWriterService)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 出库单推送
     *
     * @return Step
     */
    private Step warehouseOutPush() {
        return stepBuilderFactory.get("销售出库单")
                .<BatchWarehouseGoodsOutInDto, KingDeeSaleOutStockDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchWarehouseOutDtoItemReader(null, null))
                .processor(batchSaleOutStockProcessor)
                .writer(batchSaleOutStockWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 出库验收报告推送
     *
     * @return Step
     */
    private Step acceptanceOutForm() {
        return stepBuilderFactory.get("销售出库验收报告")
                .<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchWarehouseOutDtoItemReader(null, null))
                .processor(batchSaleOutAcceptanceFormProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 销售退货红票和入库单关系绑定
     */
    private Step bindRedInvoiceAndWarehouseInRelation() {
        return stepBuilderFactory.get("销售退货红票和入库单关系绑定")
                .tasklet(saleOrderInvoiceWarehouseOutInTasklet)
                .build();
    }

    /**
     * 销售蓝票和出库单关系绑定
     */
    private Step bindBlueInvoiceAndWarehouseInRelation() {
        return stepBuilderFactory.get("销售蓝票和出库单关系绑定")
                .tasklet(saleOrderBlueInvoiceWarehouseOutTasklet)
                .build();
    }

    /**
     * 标准应收单推送
     *
     * @return Step
     */
    private Step receivableOrderPush() {
        return stepBuilderFactory.get("销售标准应收单")
                .<BatchInvoiceDto, KingDeeReceiveCommonDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchSaleOrderReceiveDtoItemReader(null, null))
                .processor(compositeItemExcelHistoryReceiveCommonProcessor())
                .writer(batchSaleOrderReceiveWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 蓝票推送（包含普票和专票）
     *
     * @return Step
     */
    private Step blueInvoicePush() {
        return stepBuilderFactory.get("销售蓝票")
                .<BatchInvoiceDto, KingDeeSalesVatPlainAndSpecialInvoiceDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchSaleOrderReceiveDtoItemReader(null, null))
                .processor(compositeItemExcelHistoryBlueInvoiceProcessor())
                .writer(batchSaleOrderBlueInvoiceWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 发票存入金蝶凭证信息表
     */
    private Step saveInvoiceVoucher() {
        return stepBuilderFactory.get("销售蓝票存入金蝶凭证信息表")
                .tasklet(pushInvoiceVoucherTasklet)
                .build();
    }

    /**
     * 赠品应付单推送
     * @return
     */
    private Step giftPay() {
        return stepBuilderFactory.get("赠品应付单推送")
                .<BatchWarehouseGoodsOutInDto, KingDeeReceiveCommonDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(giftPayItemReader())
                .processor(batchGiftSaleOrderReceiveProcessor)
                .writer(batchSaleOrderReceiveWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 整单赠品应付单推送
     * @return
     */
    private Step  wholeGiftPay() {
        return stepBuilderFactory.get("整单赠品应付单推送")
                .<BatchWarehouseGoodsOutInDto, KingDeeReceiveCommonDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(wholeGiftPayItemReader())
                .processor(batchGiftSaleOrderReceiveProcessor)
                .writer(batchSaleOrderReceiveWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }



    /**
     * 21-22历史蓝票Excel处理及生成标准应收单
     *
     * @return
     */
    @Bean
    public CompositeItemProcessor<BatchInvoiceDto, KingDeeReceiveCommonDto> compositeItemExcelHistoryReceiveCommonProcessor() {
        CompositeItemProcessor<BatchInvoiceDto, KingDeeReceiveCommonDto> compositeItemProcessor = new CompositeItemProcessor<>();
        compositeItemProcessor.setDelegates(Arrays.asList(batchHistorySaleBlueInvoiceCompositeProcessor, batchSaleOrderReceiveProcessor));
        return compositeItemProcessor;
    }

    /**
     * 21-22历史蓝票Excel处理及生成实物商品蓝票
     *
     * @return
     */
    @Bean
    public CompositeItemProcessor<BatchInvoiceDto, KingDeeSalesVatPlainAndSpecialInvoiceDto> compositeItemExcelHistoryBlueInvoiceProcessor() {
        CompositeItemProcessor<BatchInvoiceDto, KingDeeSalesVatPlainAndSpecialInvoiceDto> compositeItemProcessor = new CompositeItemProcessor<>();
        compositeItemProcessor.setDelegates(Arrays.asList(batchHistorySaleBlueInvoiceCompositeProcessor, batchSaleOrderBlueInvoiceProcessor));
        return compositeItemProcessor;
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleorderInvoiceVirtureReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto invoiceDto = BatchInvoiceDto
                .builder()
                .colorType(ErpConstant.ONE)
                .isEnable(ErpConstant.ONE)
                .tag(ErpConstant.ONE)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "queryAllNeedVirturalInvoice", invoiceDto);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchWarehouseOutDtoItemReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchWarehouseGoodsOutInDto batchSaleOutStockDto = BatchWarehouseGoodsOutInDto.builder()
                .outInType(2)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), "findSaleOrderGoodsBatch", batchSaleOutStockDto);
    }



    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> batchSaleOrderReceiveDtoItemReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                .type(505)
                // 通过时间是当天的
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "findSaleOrderBlueInvoiceBatch", batchInvoiceDto);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> giftPayItemReader() {
        BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto = BatchWarehouseGoodsOutInDto
                .builder()
                .findGiftAndHaveBlueInvoiceTime(findGiftAndHaveBlueInvoiceTime)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), "findSaleOrderGiftGoodsAndHaveInvoice", batchWarehouseGoodsOutInDto);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto>  wholeGiftPayItemReader() {
        BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto = BatchWarehouseGoodsOutInDto
                .builder()
                .findGiftAndHaveBlueInvoiceTime(findGiftAndHaveBlueInvoiceTime)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), "findWholeSaleOrderGiftGoodsAndHaveInvoice", batchWarehouseGoodsOutInDto);
    }
}