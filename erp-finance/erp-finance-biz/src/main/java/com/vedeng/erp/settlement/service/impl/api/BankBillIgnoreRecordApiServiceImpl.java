package com.vedeng.erp.settlement.service.impl.api;

import com.vedeng.erp.settlement.domain.entity.BankBillIgnoreRecordEntity;
import com.vedeng.erp.finance.dto.BankBillIgnoreRecordDto;
import com.vedeng.erp.settlement.mapper.BankBillIgnoreRecordMapper;
import com.vedeng.erp.finance.service.BankBillIgnoreRecordApiService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class BankBillIgnoreRecordApiServiceImpl implements BankBillIgnoreRecordApiService {

    @Resource
    private BankBillIgnoreRecordMapper bankBillIgnoreRecordMapper;

    @Override
    public BankBillIgnoreRecordDto selectByBankBillId(Integer bankBillId) {
        List<BankBillIgnoreRecordEntity> ig = bankBillIgnoreRecordMapper.selectByBankBillId(bankBillId);
        if (CollectionUtils.isEmpty(ig)){
            return null;
        }
        BankBillIgnoreRecordDto dto = new BankBillIgnoreRecordDto();
        BeanUtils.copyProperties(ig.get(0),dto);
        return dto;
    }

    @Override
    public int insertSelective(BankBillIgnoreRecordDto bankBillIgnoreRecordDto) {
        return bankBillIgnoreRecordMapper.insertSelective(bankBillIgnoreRecordDto);
    }
}
