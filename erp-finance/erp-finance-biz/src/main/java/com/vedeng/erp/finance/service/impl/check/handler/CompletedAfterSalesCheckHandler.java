package com.vedeng.erp.finance.service.impl.check.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.finance.service.AbstractCheckHandler;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.enums.CheckHandlerEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 已完结售后单校验
 */
@Service
@Slf4j
public class CompletedAfterSalesCheckHandler extends AbstractCheckHandler {

    @Autowired
    private AfterSalesApiService afterSalesApiService;

    @Override
    public void handleSalesCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("已完结售后单校验-销售,invoiceCheckRequestDto:{}", invoiceCheckRequestDto);
        List<AfterSalesDto> afterSalesDtoList = afterSalesApiService.getCompletedAfterSalesByOrderId(invoiceCheckRequestDto.getRelatedId());
        if (CollUtil.isNotEmpty(afterSalesDtoList)) {
            String afterSalesNos = afterSalesDtoList.stream().map(AfterSalesDto::getAfterSalesNo).filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
            CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
            String promptText = StrUtil.format(checkHandlerEnum.getPromptText(), afterSalesNos);
            buildResult(invoiceCheckResultDto, promptText);
        }
    }

    @Override
    public void handleAfterCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
    }
}
