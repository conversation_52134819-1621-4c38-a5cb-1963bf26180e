<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="saleorder" type="com.vedeng.order.model.Saleorder" required="true" %>
<%@attribute name="afterSalesList" type="java.util.List" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>


<div class="parts content1" liname="售后列表" id="售后列表">
    <div class="title-container">
        <div class="table-title nobor">售后列表</div>
        <c:if test="${saleorder.status != 3 and saleorder.status != 0}">
            <c:if test="${saleorder.deliveryDirect eq 1}">
                <div class="title-click nobor" onclick="confirmMsgNew(${saleorder.saleorderId});">新增售后</div>
                <span style="display:none;"><div class="title-click nobor addtitle2" id="addAfter"></div></span>
            </c:if>
            <c:if test="${saleorder.deliveryDirect eq 0}">
                <div class="title-click nobor addtitle" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
									"link":"./order/aftersalesUpgrade/addAfterSalesPage.do?flag=th&&saleorderId=${saleorder.saleorderId}","title":"售后详情"}'>新增售后</div>
            </c:if>

        </c:if>

    </div>
    <table class="table table-bordered table-striped table-condensed table-centered">
        <thead>
        <tr>
            <th class="wid15">售后单号</th>
            <th class="wid15">售后类型</th>
            <th class="wid20">创建时间</th>
            <th class="wid10">创建人</th>
            <th class="wid10">订单状态</th>
        </tr>
        </thead>
        <tbody>
        <c:forEach items="${afterSalesList}" var="aftersales" varStatus="status">
            <tr>
                <td>
	                        	<span class="font-blue addtitle"
                                      tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											"link":"./order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=${aftersales.afterSalesId}&traderType=1","title":"售后详情"}'>${aftersales.afterSalesNo}</span>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${aftersales.type eq 539}">销售退货</c:when>
                        <c:when test="${aftersales.type eq 540}">销售换货</c:when>
                        <c:when test="${aftersales.type eq 541}">销售安调</c:when>
                        <c:when test="${aftersales.type eq 584}">销售维修</c:when>
                        <c:when test="${aftersales.type eq 542}">销售退票</c:when>
                        <c:when test="${aftersales.type eq 1135}">销售丢票</c:when>
                        <c:when test="${aftersales.type eq 543}">销售退款</c:when>
                        <c:when test="${aftersales.type eq 544}">销售订单技术咨询</c:when>
                        <c:when test="${aftersales.type eq 545}">销售订单其他</c:when>
                        <c:when test="${aftersales.type eq 3321}">销售订单未履约赔付</c:when>
                        <c:when test="${aftersales.type eq 3322}">销售订单送货投诉</c:when>
                        <c:when test="${aftersales.type eq 3323}">销售订单安装投诉</c:when>
                        <c:when test="${aftersales.type eq 3324}">销售订单维修投诉</c:when>
                        <c:when test="${aftersales.type eq 4090}">销售安调（合同安调）</c:when>
                        <c:when test="${aftersales.type eq 4091}">销售安调（附加服务）</c:when>
                    </c:choose>
                </td>
                <td><date:date value ="${aftersales.addTime}"/></td>
                <td>${aftersales.creatorName}</td>
                <td>
                    <c:if test="${aftersales.atferSalesStatus eq 0}">待确认</c:if>
                    <c:if test="${aftersales.atferSalesStatus eq 1}">进行中</c:if>
                    <c:if test="${aftersales.atferSalesStatus eq 2}">已完结</c:if>
                    <c:if test="${aftersales.atferSalesStatus eq 3}">已关闭</c:if>
                </td>
            </tr>
        </c:forEach>
        <c:if test="${empty afterSalesList}">
            <tr>
                <td colspan="5">查询无结果！</td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>
