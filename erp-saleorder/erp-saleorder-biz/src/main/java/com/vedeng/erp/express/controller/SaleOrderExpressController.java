package com.vedeng.erp.express.controller;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.express.dto.ExpressCommunicationBindDto;
import com.vedeng.erp.express.dto.SaleOrderCommunicationDto;
import com.vedeng.erp.express.dto.SaleOrderExpressDto;
import com.vedeng.erp.express.service.SaleOrderExpressApiService;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@ExceptionController
@RestController
@Slf4j
@RequestMapping("/saleOrderExpress")
public class SaleOrderExpressController {

    @Autowired
    private SaleOrderExpressApiService saleOrderExpressApiService;

    @Autowired
    private SaleOrderApiService saleOrderApiService;

    /**
     * 获取直发快递信息
     * @param saleOrderId
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/querySaleDirect")
    @NoNeedAccessAuthorization
    public ResultInfo<List<SaleOrderExpressDto>> getSaleDirectExpress(@RequestParam Integer saleOrderId){
        List<SaleOrderExpressDto> result = saleOrderExpressApiService.querySaleDirectExpress(saleOrderId);
        return ResultInfo.success(result);
    }

    /**
     * 获取销售订单信息
     * @param saleOrderId
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/querySaleInfo")
    @NoNeedAccessAuthorization
    public ResultInfo<SaleorderInfoDto> getSaleInfo(@RequestParam Integer saleOrderId){
        SaleorderInfoDto saleorder = saleOrderApiService.getBySaleOrderId(saleOrderId);
        log.info("获取销售订单信息:{}", JSONObject.toJSONString(saleorder));
        return ResultInfo.success(saleorder);
    }

    /**
     * 绑定并签收
     * @param list
     * @return
     * @throws Exception
     */
    @RequestMapping("/bindCommunicationIdAndSignFor")
    @NoNeedAccessAuthorization
    @ResponseBody
    public ResultInfo<List<SaleOrderExpressDto>> bindCommunicationIdAndSignFor(@RequestBody List<ExpressCommunicationBindDto> list){
        log.info("快递绑定通话并签收:{}", JSONObject.toJSONString(list));
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        saleOrderExpressApiService.bindCommunicationIdAndSignFor(list,currentUser.getId());
        return ResultInfo.success();
    }

    /**
     * 绑定通话记录
     * @param expressCommunicationBindDto
     * @return
     * @throws Exception
     */
    @RequestMapping("/bindCommunicationId")
    @NoNeedAccessAuthorization
    public ResultInfo<List<SaleOrderExpressDto>> bindCommunicationId(@RequestBody ExpressCommunicationBindDto expressCommunicationBindDto) throws Exception{
        log.info("快递仅绑定通话:{}", JSONObject.toJSONString(expressCommunicationBindDto));
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        saleOrderExpressApiService.bindCommunicationId(expressCommunicationBindDto,currentUser.getId());
        return ResultInfo.success();
    }

    /**
     * 获取快递绑定通话记录
     * @param expressId
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/getExpressCommunicationId")
    @NoNeedAccessAuthorization
    public ResultInfo<List<Integer>> getExpressCommunicationId(@RequestParam Integer expressId){
        List<Integer> result = saleOrderExpressApiService.getExpressCommunicationId(expressId);
        return ResultInfo.success(result);
    }

    /**
     * 根据通话记录id获取通话记录
     */
    @RequestMapping(value = "/getCommunicationRecord")
    @NoNeedAccessAuthorization
    public ResultInfo<SaleOrderCommunicationDto> getCommunicationRecord(@RequestParam Integer communicateRecordId){
        SaleOrderCommunicationDto result = saleOrderExpressApiService.getSaleOrderCommunication(communicateRecordId);
        return ResultInfo.success(result);
    }
}
