package com.vedeng.goods.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/14 13:53
 */
@Getter
@Setter
public class SaleOrderDataDto {

    /**
     * 销售日期
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date saleDate;

    /**
     * 订单号
     */
    private String saleOrderNo;

    /**
     * 销售单价
     */
    private BigDecimal price;
}