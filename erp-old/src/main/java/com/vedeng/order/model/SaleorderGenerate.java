package com.vedeng.order.model;

import java.math.BigDecimal;
import java.util.Date;

public class SaleorderGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.SALEORDER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer saleorderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.QUOTEORDER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer quoteorderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.PARENT_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer parentId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.SALEORDER_NO
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String saleorderNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.M_SALEORDER_NO
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String mSaleorderNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.ORDER_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer orderType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.COMPANY_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer companyId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.SOURCE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer source;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.CREATOR_ORG_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer creatorOrgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.CREATOR_ORG_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String creatorOrgName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.ORG_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer orgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.ORG_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String orgName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.USER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.VALID_ORG_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer validOrgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.VALID_ORG_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String validOrgName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.VALID_USER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer validUserId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.VALID_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte validStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.VALID_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long validTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.END_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long endTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.PURCHASE_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte purchaseStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.LOCKED_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte lockedStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer invoiceStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long invoiceTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.PAYMENT_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer paymentStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.PAYMENT_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long paymentTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.DELIVERY_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer deliveryStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.DELIVERY_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long deliveryTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.IS_CUSTOMER_ARRIVAL
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte isCustomerArrival;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.ARRIVAL_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte arrivalStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.ARRIVAL_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long arrivalTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.SERVICE_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte serviceStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.HAVE_ACCOUNT_PERIOD
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte haveAccountPeriod;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.IS_PAYMENT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte isPayment;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TOTAL_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private BigDecimal totalAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TRADER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer traderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.CUSTOMER_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer customerType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.CUSTOMER_NATURE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer customerNature;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TRADER_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String traderName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TRADER_CONTACT_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer traderContactId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TRADER_CONTACT_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String traderContactName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TRADER_CONTACT_MOBILE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String traderContactMobile;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TRADER_CONTACT_TELEPHONE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String traderContactTelephone;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TRADER_ADDRESS_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer traderAddressId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TRADER_AREA_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer traderAreaId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TRADER_AREA
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String traderArea;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TRADER_ADDRESS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String traderAddress;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TRADER_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String traderComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TAKE_TRADER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer takeTraderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TAKE_TRADER_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String takeTraderName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TAKE_TRADER_CONTACT_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer takeTraderContactId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TAKE_TRADER_CONTACT_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String takeTraderContactName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TAKE_TRADER_CONTACT_MOBILE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String takeTraderContactMobile;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TAKE_TRADER_CONTACT_TELEPHONE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String takeTraderContactTelephone;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TAKE_TRADER_ADDRESS_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer takeTraderAddressId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TAKE_TRADER_AREA_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer takeTraderAreaId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TAKE_TRADER_AREA
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String takeTraderArea;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TAKE_TRADER_ADDRESS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String takeTraderAddress;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.IS_SEND_INVOICE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte isSendInvoice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_TRADER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer invoiceTraderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_TRADER_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String invoiceTraderName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_TRADER_CONTACT_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer invoiceTraderContactId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_TRADER_CONTACT_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String invoiceTraderContactName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_TRADER_CONTACT_MOBILE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String invoiceTraderContactMobile;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_TRADER_CONTACT_TELEPHONE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String invoiceTraderContactTelephone;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_TRADER_ADDRESS_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer invoiceTraderAddressId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_TRADER_AREA_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer invoiceTraderAreaId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_TRADER_AREA
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String invoiceTraderArea;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_TRADER_ADDRESS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String invoiceTraderAddress;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.SALES_AREA_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer salesAreaId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.SALES_AREA
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String salesArea;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TERMINAL_TRADER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer terminalTraderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TERMINAL_TRADER_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String terminalTraderName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.TERMINAL_TRADER_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer terminalTraderType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer invoiceType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.FREIGHT_DESCRIPTION
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer freightDescription;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.DELIVERY_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer deliveryType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.LOGISTICS_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer logisticsId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.PAYMENT_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer paymentType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.PREPAID_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private BigDecimal prepaidAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.ACCOUNT_PERIOD_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private BigDecimal accountPeriodAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.PERIOD_DAY
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer periodDay;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.LOGISTICS_COLLECTION
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte logisticsCollection;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.RETAINAGE_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private BigDecimal retainageAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.RETAINAGE_AMOUNT_MONTH
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer retainageAmountMonth;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.PAYMENT_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String paymentComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.ADDITIONAL_CLAUSE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String additionalClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.LOGISTICS_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String logisticsComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.FINANCE_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String financeComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String comments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String invoiceComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.DELIVERY_DIRECT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer deliveryDirect;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.SUPPLIER_CLAUSE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String supplierClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.HAVE_ADVANCE_PURCHASE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte haveAdvancePurchase;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.ADVANCE_PURCHASE_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte advancePurchaseStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.ADVANCE_PURCHASE_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String advancePurchaseComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.ADVANCE_PURCHASE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long advancePurchaseTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.IS_URGENT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte isUrgent;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.URGENT_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private BigDecimal urgentAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.HAVE_COMMUNICATE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte haveCommunicate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.PREPARE_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String prepareComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.MARKETING_PLAN
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String marketingPlan;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.STATUS_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer statusComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.SYNC_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte syncStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.LOGISTICS_API_SYNC
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte logisticsApiSync;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.LOGISTICS_WXSEND_SYNC
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte logisticsWxsendSync;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.SATISFY_INVOICE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long satisfyInvoiceTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.SATISFY_DELIVERY_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long satisfyDeliveryTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.IS_SALES_PERFORMANCE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte isSalesPerformance;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.SALES_PERFORMANCE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long salesPerformanceTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.SALES_PERFORMANCE_MOD_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long salesPerformanceModTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.IS_DELAY_INVOICE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte isDelayInvoice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_METHOD
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte invoiceMethod;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.LOCKED_REASON
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String lockedReason;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.COST_USER_IDS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String costUserIds;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.OWNER_USER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer ownerUserId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.INVOICE_EMAIL
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String invoiceEmail;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.PAYMENT_MODE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte paymentMode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.PAY_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte payType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.IS_APPLY_INVOICE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte isApplyInvoice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.APPLY_INVOICE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long applyInvoiceTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.ADD_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.CREATOR
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.MOD_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long modTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.UPDATER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.ADK_SALEORDER_NO
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String adkSaleorderNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.CREATE_MOBILE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String createMobile;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.BDTRADER_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String bdtraderComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.CLOSE_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String closeComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.BD_MOBILE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long bdMobileTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.WEB_TAKE_DELIVERY_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Long webTakeDeliveryTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.ACTION_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer actionId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.IS_COUPONS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte isCoupons;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.EL_SALEORDRE_NO
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private String elSaleordreNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.COUPONMONEY
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private BigDecimal couponmoney;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.ORIGINAL_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private BigDecimal originalAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.IS_PRINTOUT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte isPrintout;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.OUT_IS_FLAG
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Byte outIsFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.UPDATE_DATA_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Date updateDataTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.REAL_PAY_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private BigDecimal realPayAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.REAL_RETURN_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private BigDecimal realReturnAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.REAL_TOTAL_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private BigDecimal realTotalAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_SALEORDER.SEND_TO_PC
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    private Integer sendToPc;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.SALEORDER_ID
     *
     * @return the value of T_SALEORDER.SALEORDER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getSaleorderId() {
        return saleorderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.SALEORDER_ID
     *
     * @param saleorderId the value for T_SALEORDER.SALEORDER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setSaleorderId(Integer saleorderId) {
        this.saleorderId = saleorderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.QUOTEORDER_ID
     *
     * @return the value of T_SALEORDER.QUOTEORDER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getQuoteorderId() {
        return quoteorderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.QUOTEORDER_ID
     *
     * @param quoteorderId the value for T_SALEORDER.QUOTEORDER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setQuoteorderId(Integer quoteorderId) {
        this.quoteorderId = quoteorderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.PARENT_ID
     *
     * @return the value of T_SALEORDER.PARENT_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getParentId() {
        return parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.PARENT_ID
     *
     * @param parentId the value for T_SALEORDER.PARENT_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.SALEORDER_NO
     *
     * @return the value of T_SALEORDER.SALEORDER_NO
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getSaleorderNo() {
        return saleorderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.SALEORDER_NO
     *
     * @param saleorderNo the value for T_SALEORDER.SALEORDER_NO
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setSaleorderNo(String saleorderNo) {
        this.saleorderNo = saleorderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.M_SALEORDER_NO
     *
     * @return the value of T_SALEORDER.M_SALEORDER_NO
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getmSaleorderNo() {
        return mSaleorderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.M_SALEORDER_NO
     *
     * @param mSaleorderNo the value for T_SALEORDER.M_SALEORDER_NO
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setmSaleorderNo(String mSaleorderNo) {
        this.mSaleorderNo = mSaleorderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.ORDER_TYPE
     *
     * @return the value of T_SALEORDER.ORDER_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getOrderType() {
        return orderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.ORDER_TYPE
     *
     * @param orderType the value for T_SALEORDER.ORDER_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.COMPANY_ID
     *
     * @return the value of T_SALEORDER.COMPANY_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.COMPANY_ID
     *
     * @param companyId the value for T_SALEORDER.COMPANY_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.SOURCE
     *
     * @return the value of T_SALEORDER.SOURCE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getSource() {
        return source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.SOURCE
     *
     * @param source the value for T_SALEORDER.SOURCE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.CREATOR_ORG_ID
     *
     * @return the value of T_SALEORDER.CREATOR_ORG_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getCreatorOrgId() {
        return creatorOrgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.CREATOR_ORG_ID
     *
     * @param creatorOrgId the value for T_SALEORDER.CREATOR_ORG_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setCreatorOrgId(Integer creatorOrgId) {
        this.creatorOrgId = creatorOrgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.CREATOR_ORG_NAME
     *
     * @return the value of T_SALEORDER.CREATOR_ORG_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getCreatorOrgName() {
        return creatorOrgName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.CREATOR_ORG_NAME
     *
     * @param creatorOrgName the value for T_SALEORDER.CREATOR_ORG_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setCreatorOrgName(String creatorOrgName) {
        this.creatorOrgName = creatorOrgName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.ORG_ID
     *
     * @return the value of T_SALEORDER.ORG_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getOrgId() {
        return orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.ORG_ID
     *
     * @param orgId the value for T_SALEORDER.ORG_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.ORG_NAME
     *
     * @return the value of T_SALEORDER.ORG_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.ORG_NAME
     *
     * @param orgName the value for T_SALEORDER.ORG_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.USER_ID
     *
     * @return the value of T_SALEORDER.USER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.USER_ID
     *
     * @param userId the value for T_SALEORDER.USER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.VALID_ORG_ID
     *
     * @return the value of T_SALEORDER.VALID_ORG_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getValidOrgId() {
        return validOrgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.VALID_ORG_ID
     *
     * @param validOrgId the value for T_SALEORDER.VALID_ORG_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setValidOrgId(Integer validOrgId) {
        this.validOrgId = validOrgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.VALID_ORG_NAME
     *
     * @return the value of T_SALEORDER.VALID_ORG_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getValidOrgName() {
        return validOrgName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.VALID_ORG_NAME
     *
     * @param validOrgName the value for T_SALEORDER.VALID_ORG_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setValidOrgName(String validOrgName) {
        this.validOrgName = validOrgName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.VALID_USER_ID
     *
     * @return the value of T_SALEORDER.VALID_USER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getValidUserId() {
        return validUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.VALID_USER_ID
     *
     * @param validUserId the value for T_SALEORDER.VALID_USER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setValidUserId(Integer validUserId) {
        this.validUserId = validUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.VALID_STATUS
     *
     * @return the value of T_SALEORDER.VALID_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getValidStatus() {
        return validStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.VALID_STATUS
     *
     * @param validStatus the value for T_SALEORDER.VALID_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setValidStatus(Byte validStatus) {
        this.validStatus = validStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.VALID_TIME
     *
     * @return the value of T_SALEORDER.VALID_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getValidTime() {
        return validTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.VALID_TIME
     *
     * @param validTime the value for T_SALEORDER.VALID_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setValidTime(Long validTime) {
        this.validTime = validTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.END_TIME
     *
     * @return the value of T_SALEORDER.END_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getEndTime() {
        return endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.END_TIME
     *
     * @param endTime the value for T_SALEORDER.END_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.STATUS
     *
     * @return the value of T_SALEORDER.STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.STATUS
     *
     * @param status the value for T_SALEORDER.STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.PURCHASE_STATUS
     *
     * @return the value of T_SALEORDER.PURCHASE_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getPurchaseStatus() {
        return purchaseStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.PURCHASE_STATUS
     *
     * @param purchaseStatus the value for T_SALEORDER.PURCHASE_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setPurchaseStatus(Byte purchaseStatus) {
        this.purchaseStatus = purchaseStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.LOCKED_STATUS
     *
     * @return the value of T_SALEORDER.LOCKED_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getLockedStatus() {
        return lockedStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.LOCKED_STATUS
     *
     * @param lockedStatus the value for T_SALEORDER.LOCKED_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setLockedStatus(Byte lockedStatus) {
        this.lockedStatus = lockedStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_STATUS
     *
     * @return the value of T_SALEORDER.INVOICE_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getInvoiceStatus() {
        return invoiceStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_STATUS
     *
     * @param invoiceStatus the value for T_SALEORDER.INVOICE_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceStatus(Integer invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_TIME
     *
     * @return the value of T_SALEORDER.INVOICE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getInvoiceTime() {
        return invoiceTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_TIME
     *
     * @param invoiceTime the value for T_SALEORDER.INVOICE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceTime(Long invoiceTime) {
        this.invoiceTime = invoiceTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.PAYMENT_STATUS
     *
     * @return the value of T_SALEORDER.PAYMENT_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getPaymentStatus() {
        return paymentStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.PAYMENT_STATUS
     *
     * @param paymentStatus the value for T_SALEORDER.PAYMENT_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setPaymentStatus(Integer paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.PAYMENT_TIME
     *
     * @return the value of T_SALEORDER.PAYMENT_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getPaymentTime() {
        return paymentTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.PAYMENT_TIME
     *
     * @param paymentTime the value for T_SALEORDER.PAYMENT_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setPaymentTime(Long paymentTime) {
        this.paymentTime = paymentTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.DELIVERY_STATUS
     *
     * @return the value of T_SALEORDER.DELIVERY_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.DELIVERY_STATUS
     *
     * @param deliveryStatus the value for T_SALEORDER.DELIVERY_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.DELIVERY_TIME
     *
     * @return the value of T_SALEORDER.DELIVERY_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getDeliveryTime() {
        return deliveryTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.DELIVERY_TIME
     *
     * @param deliveryTime the value for T_SALEORDER.DELIVERY_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setDeliveryTime(Long deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.IS_CUSTOMER_ARRIVAL
     *
     * @return the value of T_SALEORDER.IS_CUSTOMER_ARRIVAL
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getIsCustomerArrival() {
        return isCustomerArrival;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.IS_CUSTOMER_ARRIVAL
     *
     * @param isCustomerArrival the value for T_SALEORDER.IS_CUSTOMER_ARRIVAL
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setIsCustomerArrival(Byte isCustomerArrival) {
        this.isCustomerArrival = isCustomerArrival;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.ARRIVAL_STATUS
     *
     * @return the value of T_SALEORDER.ARRIVAL_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getArrivalStatus() {
        return arrivalStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.ARRIVAL_STATUS
     *
     * @param arrivalStatus the value for T_SALEORDER.ARRIVAL_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setArrivalStatus(Byte arrivalStatus) {
        this.arrivalStatus = arrivalStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.ARRIVAL_TIME
     *
     * @return the value of T_SALEORDER.ARRIVAL_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getArrivalTime() {
        return arrivalTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.ARRIVAL_TIME
     *
     * @param arrivalTime the value for T_SALEORDER.ARRIVAL_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setArrivalTime(Long arrivalTime) {
        this.arrivalTime = arrivalTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.SERVICE_STATUS
     *
     * @return the value of T_SALEORDER.SERVICE_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getServiceStatus() {
        return serviceStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.SERVICE_STATUS
     *
     * @param serviceStatus the value for T_SALEORDER.SERVICE_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setServiceStatus(Byte serviceStatus) {
        this.serviceStatus = serviceStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.HAVE_ACCOUNT_PERIOD
     *
     * @return the value of T_SALEORDER.HAVE_ACCOUNT_PERIOD
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getHaveAccountPeriod() {
        return haveAccountPeriod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.HAVE_ACCOUNT_PERIOD
     *
     * @param haveAccountPeriod the value for T_SALEORDER.HAVE_ACCOUNT_PERIOD
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setHaveAccountPeriod(Byte haveAccountPeriod) {
        this.haveAccountPeriod = haveAccountPeriod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.IS_PAYMENT
     *
     * @return the value of T_SALEORDER.IS_PAYMENT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getIsPayment() {
        return isPayment;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.IS_PAYMENT
     *
     * @param isPayment the value for T_SALEORDER.IS_PAYMENT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setIsPayment(Byte isPayment) {
        this.isPayment = isPayment;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TOTAL_AMOUNT
     *
     * @return the value of T_SALEORDER.TOTAL_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TOTAL_AMOUNT
     *
     * @param totalAmount the value for T_SALEORDER.TOTAL_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TRADER_ID
     *
     * @return the value of T_SALEORDER.TRADER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getTraderId() {
        return traderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TRADER_ID
     *
     * @param traderId the value for T_SALEORDER.TRADER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.CUSTOMER_TYPE
     *
     * @return the value of T_SALEORDER.CUSTOMER_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getCustomerType() {
        return customerType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.CUSTOMER_TYPE
     *
     * @param customerType the value for T_SALEORDER.CUSTOMER_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.CUSTOMER_NATURE
     *
     * @return the value of T_SALEORDER.CUSTOMER_NATURE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getCustomerNature() {
        return customerNature;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.CUSTOMER_NATURE
     *
     * @param customerNature the value for T_SALEORDER.CUSTOMER_NATURE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setCustomerNature(Integer customerNature) {
        this.customerNature = customerNature;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TRADER_NAME
     *
     * @return the value of T_SALEORDER.TRADER_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getTraderName() {
        return traderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TRADER_NAME
     *
     * @param traderName the value for T_SALEORDER.TRADER_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TRADER_CONTACT_ID
     *
     * @return the value of T_SALEORDER.TRADER_CONTACT_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getTraderContactId() {
        return traderContactId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TRADER_CONTACT_ID
     *
     * @param traderContactId the value for T_SALEORDER.TRADER_CONTACT_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTraderContactId(Integer traderContactId) {
        this.traderContactId = traderContactId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TRADER_CONTACT_NAME
     *
     * @return the value of T_SALEORDER.TRADER_CONTACT_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getTraderContactName() {
        return traderContactName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TRADER_CONTACT_NAME
     *
     * @param traderContactName the value for T_SALEORDER.TRADER_CONTACT_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTraderContactName(String traderContactName) {
        this.traderContactName = traderContactName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TRADER_CONTACT_MOBILE
     *
     * @return the value of T_SALEORDER.TRADER_CONTACT_MOBILE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getTraderContactMobile() {
        return traderContactMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TRADER_CONTACT_MOBILE
     *
     * @param traderContactMobile the value for T_SALEORDER.TRADER_CONTACT_MOBILE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTraderContactMobile(String traderContactMobile) {
        this.traderContactMobile = traderContactMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TRADER_CONTACT_TELEPHONE
     *
     * @return the value of T_SALEORDER.TRADER_CONTACT_TELEPHONE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getTraderContactTelephone() {
        return traderContactTelephone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TRADER_CONTACT_TELEPHONE
     *
     * @param traderContactTelephone the value for T_SALEORDER.TRADER_CONTACT_TELEPHONE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTraderContactTelephone(String traderContactTelephone) {
        this.traderContactTelephone = traderContactTelephone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TRADER_ADDRESS_ID
     *
     * @return the value of T_SALEORDER.TRADER_ADDRESS_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getTraderAddressId() {
        return traderAddressId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TRADER_ADDRESS_ID
     *
     * @param traderAddressId the value for T_SALEORDER.TRADER_ADDRESS_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTraderAddressId(Integer traderAddressId) {
        this.traderAddressId = traderAddressId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TRADER_AREA_ID
     *
     * @return the value of T_SALEORDER.TRADER_AREA_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getTraderAreaId() {
        return traderAreaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TRADER_AREA_ID
     *
     * @param traderAreaId the value for T_SALEORDER.TRADER_AREA_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTraderAreaId(Integer traderAreaId) {
        this.traderAreaId = traderAreaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TRADER_AREA
     *
     * @return the value of T_SALEORDER.TRADER_AREA
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getTraderArea() {
        return traderArea;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TRADER_AREA
     *
     * @param traderArea the value for T_SALEORDER.TRADER_AREA
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTraderArea(String traderArea) {
        this.traderArea = traderArea;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TRADER_ADDRESS
     *
     * @return the value of T_SALEORDER.TRADER_ADDRESS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getTraderAddress() {
        return traderAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TRADER_ADDRESS
     *
     * @param traderAddress the value for T_SALEORDER.TRADER_ADDRESS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTraderAddress(String traderAddress) {
        this.traderAddress = traderAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TRADER_COMMENTS
     *
     * @return the value of T_SALEORDER.TRADER_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getTraderComments() {
        return traderComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TRADER_COMMENTS
     *
     * @param traderComments the value for T_SALEORDER.TRADER_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTraderComments(String traderComments) {
        this.traderComments = traderComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TAKE_TRADER_ID
     *
     * @return the value of T_SALEORDER.TAKE_TRADER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getTakeTraderId() {
        return takeTraderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TAKE_TRADER_ID
     *
     * @param takeTraderId the value for T_SALEORDER.TAKE_TRADER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTakeTraderId(Integer takeTraderId) {
        this.takeTraderId = takeTraderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TAKE_TRADER_NAME
     *
     * @return the value of T_SALEORDER.TAKE_TRADER_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getTakeTraderName() {
        return takeTraderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TAKE_TRADER_NAME
     *
     * @param takeTraderName the value for T_SALEORDER.TAKE_TRADER_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTakeTraderName(String takeTraderName) {
        this.takeTraderName = takeTraderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TAKE_TRADER_CONTACT_ID
     *
     * @return the value of T_SALEORDER.TAKE_TRADER_CONTACT_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getTakeTraderContactId() {
        return takeTraderContactId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TAKE_TRADER_CONTACT_ID
     *
     * @param takeTraderContactId the value for T_SALEORDER.TAKE_TRADER_CONTACT_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTakeTraderContactId(Integer takeTraderContactId) {
        this.takeTraderContactId = takeTraderContactId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TAKE_TRADER_CONTACT_NAME
     *
     * @return the value of T_SALEORDER.TAKE_TRADER_CONTACT_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getTakeTraderContactName() {
        return takeTraderContactName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TAKE_TRADER_CONTACT_NAME
     *
     * @param takeTraderContactName the value for T_SALEORDER.TAKE_TRADER_CONTACT_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTakeTraderContactName(String takeTraderContactName) {
        this.takeTraderContactName = takeTraderContactName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TAKE_TRADER_CONTACT_MOBILE
     *
     * @return the value of T_SALEORDER.TAKE_TRADER_CONTACT_MOBILE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getTakeTraderContactMobile() {
        return takeTraderContactMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TAKE_TRADER_CONTACT_MOBILE
     *
     * @param takeTraderContactMobile the value for T_SALEORDER.TAKE_TRADER_CONTACT_MOBILE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTakeTraderContactMobile(String takeTraderContactMobile) {
        this.takeTraderContactMobile = takeTraderContactMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TAKE_TRADER_CONTACT_TELEPHONE
     *
     * @return the value of T_SALEORDER.TAKE_TRADER_CONTACT_TELEPHONE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getTakeTraderContactTelephone() {
        return takeTraderContactTelephone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TAKE_TRADER_CONTACT_TELEPHONE
     *
     * @param takeTraderContactTelephone the value for T_SALEORDER.TAKE_TRADER_CONTACT_TELEPHONE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTakeTraderContactTelephone(String takeTraderContactTelephone) {
        this.takeTraderContactTelephone = takeTraderContactTelephone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TAKE_TRADER_ADDRESS_ID
     *
     * @return the value of T_SALEORDER.TAKE_TRADER_ADDRESS_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getTakeTraderAddressId() {
        return takeTraderAddressId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TAKE_TRADER_ADDRESS_ID
     *
     * @param takeTraderAddressId the value for T_SALEORDER.TAKE_TRADER_ADDRESS_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTakeTraderAddressId(Integer takeTraderAddressId) {
        this.takeTraderAddressId = takeTraderAddressId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TAKE_TRADER_AREA_ID
     *
     * @return the value of T_SALEORDER.TAKE_TRADER_AREA_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getTakeTraderAreaId() {
        return takeTraderAreaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TAKE_TRADER_AREA_ID
     *
     * @param takeTraderAreaId the value for T_SALEORDER.TAKE_TRADER_AREA_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTakeTraderAreaId(Integer takeTraderAreaId) {
        this.takeTraderAreaId = takeTraderAreaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TAKE_TRADER_AREA
     *
     * @return the value of T_SALEORDER.TAKE_TRADER_AREA
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getTakeTraderArea() {
        return takeTraderArea;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TAKE_TRADER_AREA
     *
     * @param takeTraderArea the value for T_SALEORDER.TAKE_TRADER_AREA
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTakeTraderArea(String takeTraderArea) {
        this.takeTraderArea = takeTraderArea;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TAKE_TRADER_ADDRESS
     *
     * @return the value of T_SALEORDER.TAKE_TRADER_ADDRESS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getTakeTraderAddress() {
        return takeTraderAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TAKE_TRADER_ADDRESS
     *
     * @param takeTraderAddress the value for T_SALEORDER.TAKE_TRADER_ADDRESS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTakeTraderAddress(String takeTraderAddress) {
        this.takeTraderAddress = takeTraderAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.IS_SEND_INVOICE
     *
     * @return the value of T_SALEORDER.IS_SEND_INVOICE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getIsSendInvoice() {
        return isSendInvoice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.IS_SEND_INVOICE
     *
     * @param isSendInvoice the value for T_SALEORDER.IS_SEND_INVOICE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setIsSendInvoice(Byte isSendInvoice) {
        this.isSendInvoice = isSendInvoice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_TRADER_ID
     *
     * @return the value of T_SALEORDER.INVOICE_TRADER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getInvoiceTraderId() {
        return invoiceTraderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_TRADER_ID
     *
     * @param invoiceTraderId the value for T_SALEORDER.INVOICE_TRADER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceTraderId(Integer invoiceTraderId) {
        this.invoiceTraderId = invoiceTraderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_TRADER_NAME
     *
     * @return the value of T_SALEORDER.INVOICE_TRADER_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getInvoiceTraderName() {
        return invoiceTraderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_TRADER_NAME
     *
     * @param invoiceTraderName the value for T_SALEORDER.INVOICE_TRADER_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceTraderName(String invoiceTraderName) {
        this.invoiceTraderName = invoiceTraderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_TRADER_CONTACT_ID
     *
     * @return the value of T_SALEORDER.INVOICE_TRADER_CONTACT_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getInvoiceTraderContactId() {
        return invoiceTraderContactId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_TRADER_CONTACT_ID
     *
     * @param invoiceTraderContactId the value for T_SALEORDER.INVOICE_TRADER_CONTACT_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceTraderContactId(Integer invoiceTraderContactId) {
        this.invoiceTraderContactId = invoiceTraderContactId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_TRADER_CONTACT_NAME
     *
     * @return the value of T_SALEORDER.INVOICE_TRADER_CONTACT_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getInvoiceTraderContactName() {
        return invoiceTraderContactName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_TRADER_CONTACT_NAME
     *
     * @param invoiceTraderContactName the value for T_SALEORDER.INVOICE_TRADER_CONTACT_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceTraderContactName(String invoiceTraderContactName) {
        this.invoiceTraderContactName = invoiceTraderContactName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_TRADER_CONTACT_MOBILE
     *
     * @return the value of T_SALEORDER.INVOICE_TRADER_CONTACT_MOBILE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getInvoiceTraderContactMobile() {
        return invoiceTraderContactMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_TRADER_CONTACT_MOBILE
     *
     * @param invoiceTraderContactMobile the value for T_SALEORDER.INVOICE_TRADER_CONTACT_MOBILE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceTraderContactMobile(String invoiceTraderContactMobile) {
        this.invoiceTraderContactMobile = invoiceTraderContactMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_TRADER_CONTACT_TELEPHONE
     *
     * @return the value of T_SALEORDER.INVOICE_TRADER_CONTACT_TELEPHONE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getInvoiceTraderContactTelephone() {
        return invoiceTraderContactTelephone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_TRADER_CONTACT_TELEPHONE
     *
     * @param invoiceTraderContactTelephone the value for T_SALEORDER.INVOICE_TRADER_CONTACT_TELEPHONE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceTraderContactTelephone(String invoiceTraderContactTelephone) {
        this.invoiceTraderContactTelephone = invoiceTraderContactTelephone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_TRADER_ADDRESS_ID
     *
     * @return the value of T_SALEORDER.INVOICE_TRADER_ADDRESS_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getInvoiceTraderAddressId() {
        return invoiceTraderAddressId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_TRADER_ADDRESS_ID
     *
     * @param invoiceTraderAddressId the value for T_SALEORDER.INVOICE_TRADER_ADDRESS_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceTraderAddressId(Integer invoiceTraderAddressId) {
        this.invoiceTraderAddressId = invoiceTraderAddressId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_TRADER_AREA_ID
     *
     * @return the value of T_SALEORDER.INVOICE_TRADER_AREA_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getInvoiceTraderAreaId() {
        return invoiceTraderAreaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_TRADER_AREA_ID
     *
     * @param invoiceTraderAreaId the value for T_SALEORDER.INVOICE_TRADER_AREA_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceTraderAreaId(Integer invoiceTraderAreaId) {
        this.invoiceTraderAreaId = invoiceTraderAreaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_TRADER_AREA
     *
     * @return the value of T_SALEORDER.INVOICE_TRADER_AREA
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getInvoiceTraderArea() {
        return invoiceTraderArea;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_TRADER_AREA
     *
     * @param invoiceTraderArea the value for T_SALEORDER.INVOICE_TRADER_AREA
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceTraderArea(String invoiceTraderArea) {
        this.invoiceTraderArea = invoiceTraderArea;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_TRADER_ADDRESS
     *
     * @return the value of T_SALEORDER.INVOICE_TRADER_ADDRESS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getInvoiceTraderAddress() {
        return invoiceTraderAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_TRADER_ADDRESS
     *
     * @param invoiceTraderAddress the value for T_SALEORDER.INVOICE_TRADER_ADDRESS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceTraderAddress(String invoiceTraderAddress) {
        this.invoiceTraderAddress = invoiceTraderAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.SALES_AREA_ID
     *
     * @return the value of T_SALEORDER.SALES_AREA_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getSalesAreaId() {
        return salesAreaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.SALES_AREA_ID
     *
     * @param salesAreaId the value for T_SALEORDER.SALES_AREA_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setSalesAreaId(Integer salesAreaId) {
        this.salesAreaId = salesAreaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.SALES_AREA
     *
     * @return the value of T_SALEORDER.SALES_AREA
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getSalesArea() {
        return salesArea;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.SALES_AREA
     *
     * @param salesArea the value for T_SALEORDER.SALES_AREA
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setSalesArea(String salesArea) {
        this.salesArea = salesArea;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TERMINAL_TRADER_ID
     *
     * @return the value of T_SALEORDER.TERMINAL_TRADER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getTerminalTraderId() {
        return terminalTraderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TERMINAL_TRADER_ID
     *
     * @param terminalTraderId the value for T_SALEORDER.TERMINAL_TRADER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTerminalTraderId(Integer terminalTraderId) {
        this.terminalTraderId = terminalTraderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TERMINAL_TRADER_NAME
     *
     * @return the value of T_SALEORDER.TERMINAL_TRADER_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getTerminalTraderName() {
        return terminalTraderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TERMINAL_TRADER_NAME
     *
     * @param terminalTraderName the value for T_SALEORDER.TERMINAL_TRADER_NAME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTerminalTraderName(String terminalTraderName) {
        this.terminalTraderName = terminalTraderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.TERMINAL_TRADER_TYPE
     *
     * @return the value of T_SALEORDER.TERMINAL_TRADER_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getTerminalTraderType() {
        return terminalTraderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.TERMINAL_TRADER_TYPE
     *
     * @param terminalTraderType the value for T_SALEORDER.TERMINAL_TRADER_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setTerminalTraderType(Integer terminalTraderType) {
        this.terminalTraderType = terminalTraderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_TYPE
     *
     * @return the value of T_SALEORDER.INVOICE_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getInvoiceType() {
        return invoiceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_TYPE
     *
     * @param invoiceType the value for T_SALEORDER.INVOICE_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.FREIGHT_DESCRIPTION
     *
     * @return the value of T_SALEORDER.FREIGHT_DESCRIPTION
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getFreightDescription() {
        return freightDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.FREIGHT_DESCRIPTION
     *
     * @param freightDescription the value for T_SALEORDER.FREIGHT_DESCRIPTION
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setFreightDescription(Integer freightDescription) {
        this.freightDescription = freightDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.DELIVERY_TYPE
     *
     * @return the value of T_SALEORDER.DELIVERY_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getDeliveryType() {
        return deliveryType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.DELIVERY_TYPE
     *
     * @param deliveryType the value for T_SALEORDER.DELIVERY_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setDeliveryType(Integer deliveryType) {
        this.deliveryType = deliveryType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.LOGISTICS_ID
     *
     * @return the value of T_SALEORDER.LOGISTICS_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getLogisticsId() {
        return logisticsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.LOGISTICS_ID
     *
     * @param logisticsId the value for T_SALEORDER.LOGISTICS_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setLogisticsId(Integer logisticsId) {
        this.logisticsId = logisticsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.PAYMENT_TYPE
     *
     * @return the value of T_SALEORDER.PAYMENT_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getPaymentType() {
        return paymentType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.PAYMENT_TYPE
     *
     * @param paymentType the value for T_SALEORDER.PAYMENT_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.PREPAID_AMOUNT
     *
     * @return the value of T_SALEORDER.PREPAID_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public BigDecimal getPrepaidAmount() {
        return prepaidAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.PREPAID_AMOUNT
     *
     * @param prepaidAmount the value for T_SALEORDER.PREPAID_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setPrepaidAmount(BigDecimal prepaidAmount) {
        this.prepaidAmount = prepaidAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.ACCOUNT_PERIOD_AMOUNT
     *
     * @return the value of T_SALEORDER.ACCOUNT_PERIOD_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public BigDecimal getAccountPeriodAmount() {
        return accountPeriodAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.ACCOUNT_PERIOD_AMOUNT
     *
     * @param accountPeriodAmount the value for T_SALEORDER.ACCOUNT_PERIOD_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setAccountPeriodAmount(BigDecimal accountPeriodAmount) {
        this.accountPeriodAmount = accountPeriodAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.PERIOD_DAY
     *
     * @return the value of T_SALEORDER.PERIOD_DAY
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getPeriodDay() {
        return periodDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.PERIOD_DAY
     *
     * @param periodDay the value for T_SALEORDER.PERIOD_DAY
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setPeriodDay(Integer periodDay) {
        this.periodDay = periodDay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.LOGISTICS_COLLECTION
     *
     * @return the value of T_SALEORDER.LOGISTICS_COLLECTION
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getLogisticsCollection() {
        return logisticsCollection;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.LOGISTICS_COLLECTION
     *
     * @param logisticsCollection the value for T_SALEORDER.LOGISTICS_COLLECTION
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setLogisticsCollection(Byte logisticsCollection) {
        this.logisticsCollection = logisticsCollection;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.RETAINAGE_AMOUNT
     *
     * @return the value of T_SALEORDER.RETAINAGE_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public BigDecimal getRetainageAmount() {
        return retainageAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.RETAINAGE_AMOUNT
     *
     * @param retainageAmount the value for T_SALEORDER.RETAINAGE_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setRetainageAmount(BigDecimal retainageAmount) {
        this.retainageAmount = retainageAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.RETAINAGE_AMOUNT_MONTH
     *
     * @return the value of T_SALEORDER.RETAINAGE_AMOUNT_MONTH
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getRetainageAmountMonth() {
        return retainageAmountMonth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.RETAINAGE_AMOUNT_MONTH
     *
     * @param retainageAmountMonth the value for T_SALEORDER.RETAINAGE_AMOUNT_MONTH
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setRetainageAmountMonth(Integer retainageAmountMonth) {
        this.retainageAmountMonth = retainageAmountMonth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.PAYMENT_COMMENTS
     *
     * @return the value of T_SALEORDER.PAYMENT_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getPaymentComments() {
        return paymentComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.PAYMENT_COMMENTS
     *
     * @param paymentComments the value for T_SALEORDER.PAYMENT_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setPaymentComments(String paymentComments) {
        this.paymentComments = paymentComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.ADDITIONAL_CLAUSE
     *
     * @return the value of T_SALEORDER.ADDITIONAL_CLAUSE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getAdditionalClause() {
        return additionalClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.ADDITIONAL_CLAUSE
     *
     * @param additionalClause the value for T_SALEORDER.ADDITIONAL_CLAUSE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setAdditionalClause(String additionalClause) {
        this.additionalClause = additionalClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.LOGISTICS_COMMENTS
     *
     * @return the value of T_SALEORDER.LOGISTICS_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getLogisticsComments() {
        return logisticsComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.LOGISTICS_COMMENTS
     *
     * @param logisticsComments the value for T_SALEORDER.LOGISTICS_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setLogisticsComments(String logisticsComments) {
        this.logisticsComments = logisticsComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.FINANCE_COMMENTS
     *
     * @return the value of T_SALEORDER.FINANCE_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getFinanceComments() {
        return financeComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.FINANCE_COMMENTS
     *
     * @param financeComments the value for T_SALEORDER.FINANCE_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setFinanceComments(String financeComments) {
        this.financeComments = financeComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.COMMENTS
     *
     * @return the value of T_SALEORDER.COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getComments() {
        return comments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.COMMENTS
     *
     * @param comments the value for T_SALEORDER.COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setComments(String comments) {
        this.comments = comments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_COMMENTS
     *
     * @return the value of T_SALEORDER.INVOICE_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getInvoiceComments() {
        return invoiceComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_COMMENTS
     *
     * @param invoiceComments the value for T_SALEORDER.INVOICE_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceComments(String invoiceComments) {
        this.invoiceComments = invoiceComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.DELIVERY_DIRECT
     *
     * @return the value of T_SALEORDER.DELIVERY_DIRECT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getDeliveryDirect() {
        return deliveryDirect;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.DELIVERY_DIRECT
     *
     * @param deliveryDirect the value for T_SALEORDER.DELIVERY_DIRECT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setDeliveryDirect(Integer deliveryDirect) {
        this.deliveryDirect = deliveryDirect;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.SUPPLIER_CLAUSE
     *
     * @return the value of T_SALEORDER.SUPPLIER_CLAUSE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getSupplierClause() {
        return supplierClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.SUPPLIER_CLAUSE
     *
     * @param supplierClause the value for T_SALEORDER.SUPPLIER_CLAUSE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setSupplierClause(String supplierClause) {
        this.supplierClause = supplierClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.HAVE_ADVANCE_PURCHASE
     *
     * @return the value of T_SALEORDER.HAVE_ADVANCE_PURCHASE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getHaveAdvancePurchase() {
        return haveAdvancePurchase;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.HAVE_ADVANCE_PURCHASE
     *
     * @param haveAdvancePurchase the value for T_SALEORDER.HAVE_ADVANCE_PURCHASE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setHaveAdvancePurchase(Byte haveAdvancePurchase) {
        this.haveAdvancePurchase = haveAdvancePurchase;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.ADVANCE_PURCHASE_STATUS
     *
     * @return the value of T_SALEORDER.ADVANCE_PURCHASE_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getAdvancePurchaseStatus() {
        return advancePurchaseStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.ADVANCE_PURCHASE_STATUS
     *
     * @param advancePurchaseStatus the value for T_SALEORDER.ADVANCE_PURCHASE_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setAdvancePurchaseStatus(Byte advancePurchaseStatus) {
        this.advancePurchaseStatus = advancePurchaseStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.ADVANCE_PURCHASE_COMMENTS
     *
     * @return the value of T_SALEORDER.ADVANCE_PURCHASE_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getAdvancePurchaseComments() {
        return advancePurchaseComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.ADVANCE_PURCHASE_COMMENTS
     *
     * @param advancePurchaseComments the value for T_SALEORDER.ADVANCE_PURCHASE_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setAdvancePurchaseComments(String advancePurchaseComments) {
        this.advancePurchaseComments = advancePurchaseComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.ADVANCE_PURCHASE_TIME
     *
     * @return the value of T_SALEORDER.ADVANCE_PURCHASE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getAdvancePurchaseTime() {
        return advancePurchaseTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.ADVANCE_PURCHASE_TIME
     *
     * @param advancePurchaseTime the value for T_SALEORDER.ADVANCE_PURCHASE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setAdvancePurchaseTime(Long advancePurchaseTime) {
        this.advancePurchaseTime = advancePurchaseTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.IS_URGENT
     *
     * @return the value of T_SALEORDER.IS_URGENT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getIsUrgent() {
        return isUrgent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.IS_URGENT
     *
     * @param isUrgent the value for T_SALEORDER.IS_URGENT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setIsUrgent(Byte isUrgent) {
        this.isUrgent = isUrgent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.URGENT_AMOUNT
     *
     * @return the value of T_SALEORDER.URGENT_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public BigDecimal getUrgentAmount() {
        return urgentAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.URGENT_AMOUNT
     *
     * @param urgentAmount the value for T_SALEORDER.URGENT_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setUrgentAmount(BigDecimal urgentAmount) {
        this.urgentAmount = urgentAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.HAVE_COMMUNICATE
     *
     * @return the value of T_SALEORDER.HAVE_COMMUNICATE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getHaveCommunicate() {
        return haveCommunicate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.HAVE_COMMUNICATE
     *
     * @param haveCommunicate the value for T_SALEORDER.HAVE_COMMUNICATE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setHaveCommunicate(Byte haveCommunicate) {
        this.haveCommunicate = haveCommunicate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.PREPARE_COMMENTS
     *
     * @return the value of T_SALEORDER.PREPARE_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getPrepareComments() {
        return prepareComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.PREPARE_COMMENTS
     *
     * @param prepareComments the value for T_SALEORDER.PREPARE_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setPrepareComments(String prepareComments) {
        this.prepareComments = prepareComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.MARKETING_PLAN
     *
     * @return the value of T_SALEORDER.MARKETING_PLAN
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getMarketingPlan() {
        return marketingPlan;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.MARKETING_PLAN
     *
     * @param marketingPlan the value for T_SALEORDER.MARKETING_PLAN
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setMarketingPlan(String marketingPlan) {
        this.marketingPlan = marketingPlan;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.STATUS_COMMENTS
     *
     * @return the value of T_SALEORDER.STATUS_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getStatusComments() {
        return statusComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.STATUS_COMMENTS
     *
     * @param statusComments the value for T_SALEORDER.STATUS_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setStatusComments(Integer statusComments) {
        this.statusComments = statusComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.SYNC_STATUS
     *
     * @return the value of T_SALEORDER.SYNC_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getSyncStatus() {
        return syncStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.SYNC_STATUS
     *
     * @param syncStatus the value for T_SALEORDER.SYNC_STATUS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setSyncStatus(Byte syncStatus) {
        this.syncStatus = syncStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.LOGISTICS_API_SYNC
     *
     * @return the value of T_SALEORDER.LOGISTICS_API_SYNC
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getLogisticsApiSync() {
        return logisticsApiSync;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.LOGISTICS_API_SYNC
     *
     * @param logisticsApiSync the value for T_SALEORDER.LOGISTICS_API_SYNC
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setLogisticsApiSync(Byte logisticsApiSync) {
        this.logisticsApiSync = logisticsApiSync;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.LOGISTICS_WXSEND_SYNC
     *
     * @return the value of T_SALEORDER.LOGISTICS_WXSEND_SYNC
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getLogisticsWxsendSync() {
        return logisticsWxsendSync;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.LOGISTICS_WXSEND_SYNC
     *
     * @param logisticsWxsendSync the value for T_SALEORDER.LOGISTICS_WXSEND_SYNC
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setLogisticsWxsendSync(Byte logisticsWxsendSync) {
        this.logisticsWxsendSync = logisticsWxsendSync;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.SATISFY_INVOICE_TIME
     *
     * @return the value of T_SALEORDER.SATISFY_INVOICE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getSatisfyInvoiceTime() {
        return satisfyInvoiceTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.SATISFY_INVOICE_TIME
     *
     * @param satisfyInvoiceTime the value for T_SALEORDER.SATISFY_INVOICE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setSatisfyInvoiceTime(Long satisfyInvoiceTime) {
        this.satisfyInvoiceTime = satisfyInvoiceTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.SATISFY_DELIVERY_TIME
     *
     * @return the value of T_SALEORDER.SATISFY_DELIVERY_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getSatisfyDeliveryTime() {
        return satisfyDeliveryTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.SATISFY_DELIVERY_TIME
     *
     * @param satisfyDeliveryTime the value for T_SALEORDER.SATISFY_DELIVERY_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setSatisfyDeliveryTime(Long satisfyDeliveryTime) {
        this.satisfyDeliveryTime = satisfyDeliveryTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.IS_SALES_PERFORMANCE
     *
     * @return the value of T_SALEORDER.IS_SALES_PERFORMANCE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getIsSalesPerformance() {
        return isSalesPerformance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.IS_SALES_PERFORMANCE
     *
     * @param isSalesPerformance the value for T_SALEORDER.IS_SALES_PERFORMANCE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setIsSalesPerformance(Byte isSalesPerformance) {
        this.isSalesPerformance = isSalesPerformance;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.SALES_PERFORMANCE_TIME
     *
     * @return the value of T_SALEORDER.SALES_PERFORMANCE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getSalesPerformanceTime() {
        return salesPerformanceTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.SALES_PERFORMANCE_TIME
     *
     * @param salesPerformanceTime the value for T_SALEORDER.SALES_PERFORMANCE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setSalesPerformanceTime(Long salesPerformanceTime) {
        this.salesPerformanceTime = salesPerformanceTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.SALES_PERFORMANCE_MOD_TIME
     *
     * @return the value of T_SALEORDER.SALES_PERFORMANCE_MOD_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getSalesPerformanceModTime() {
        return salesPerformanceModTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.SALES_PERFORMANCE_MOD_TIME
     *
     * @param salesPerformanceModTime the value for T_SALEORDER.SALES_PERFORMANCE_MOD_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setSalesPerformanceModTime(Long salesPerformanceModTime) {
        this.salesPerformanceModTime = salesPerformanceModTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.IS_DELAY_INVOICE
     *
     * @return the value of T_SALEORDER.IS_DELAY_INVOICE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getIsDelayInvoice() {
        return isDelayInvoice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.IS_DELAY_INVOICE
     *
     * @param isDelayInvoice the value for T_SALEORDER.IS_DELAY_INVOICE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setIsDelayInvoice(Byte isDelayInvoice) {
        this.isDelayInvoice = isDelayInvoice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_METHOD
     *
     * @return the value of T_SALEORDER.INVOICE_METHOD
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getInvoiceMethod() {
        return invoiceMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_METHOD
     *
     * @param invoiceMethod the value for T_SALEORDER.INVOICE_METHOD
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceMethod(Byte invoiceMethod) {
        this.invoiceMethod = invoiceMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.LOCKED_REASON
     *
     * @return the value of T_SALEORDER.LOCKED_REASON
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getLockedReason() {
        return lockedReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.LOCKED_REASON
     *
     * @param lockedReason the value for T_SALEORDER.LOCKED_REASON
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setLockedReason(String lockedReason) {
        this.lockedReason = lockedReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.COST_USER_IDS
     *
     * @return the value of T_SALEORDER.COST_USER_IDS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getCostUserIds() {
        return costUserIds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.COST_USER_IDS
     *
     * @param costUserIds the value for T_SALEORDER.COST_USER_IDS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setCostUserIds(String costUserIds) {
        this.costUserIds = costUserIds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.OWNER_USER_ID
     *
     * @return the value of T_SALEORDER.OWNER_USER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getOwnerUserId() {
        return ownerUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.OWNER_USER_ID
     *
     * @param ownerUserId the value for T_SALEORDER.OWNER_USER_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setOwnerUserId(Integer ownerUserId) {
        this.ownerUserId = ownerUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.INVOICE_EMAIL
     *
     * @return the value of T_SALEORDER.INVOICE_EMAIL
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getInvoiceEmail() {
        return invoiceEmail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.INVOICE_EMAIL
     *
     * @param invoiceEmail the value for T_SALEORDER.INVOICE_EMAIL
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setInvoiceEmail(String invoiceEmail) {
        this.invoiceEmail = invoiceEmail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.PAYMENT_MODE
     *
     * @return the value of T_SALEORDER.PAYMENT_MODE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getPaymentMode() {
        return paymentMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.PAYMENT_MODE
     *
     * @param paymentMode the value for T_SALEORDER.PAYMENT_MODE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setPaymentMode(Byte paymentMode) {
        this.paymentMode = paymentMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.PAY_TYPE
     *
     * @return the value of T_SALEORDER.PAY_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getPayType() {
        return payType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.PAY_TYPE
     *
     * @param payType the value for T_SALEORDER.PAY_TYPE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setPayType(Byte payType) {
        this.payType = payType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.IS_APPLY_INVOICE
     *
     * @return the value of T_SALEORDER.IS_APPLY_INVOICE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getIsApplyInvoice() {
        return isApplyInvoice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.IS_APPLY_INVOICE
     *
     * @param isApplyInvoice the value for T_SALEORDER.IS_APPLY_INVOICE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setIsApplyInvoice(Byte isApplyInvoice) {
        this.isApplyInvoice = isApplyInvoice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.APPLY_INVOICE_TIME
     *
     * @return the value of T_SALEORDER.APPLY_INVOICE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getApplyInvoiceTime() {
        return applyInvoiceTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.APPLY_INVOICE_TIME
     *
     * @param applyInvoiceTime the value for T_SALEORDER.APPLY_INVOICE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setApplyInvoiceTime(Long applyInvoiceTime) {
        this.applyInvoiceTime = applyInvoiceTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.ADD_TIME
     *
     * @return the value of T_SALEORDER.ADD_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.ADD_TIME
     *
     * @param addTime the value for T_SALEORDER.ADD_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.CREATOR
     *
     * @return the value of T_SALEORDER.CREATOR
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.CREATOR
     *
     * @param creator the value for T_SALEORDER.CREATOR
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.MOD_TIME
     *
     * @return the value of T_SALEORDER.MOD_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.MOD_TIME
     *
     * @param modTime the value for T_SALEORDER.MOD_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.UPDATER
     *
     * @return the value of T_SALEORDER.UPDATER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.UPDATER
     *
     * @param updater the value for T_SALEORDER.UPDATER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.ADK_SALEORDER_NO
     *
     * @return the value of T_SALEORDER.ADK_SALEORDER_NO
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getAdkSaleorderNo() {
        return adkSaleorderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.ADK_SALEORDER_NO
     *
     * @param adkSaleorderNo the value for T_SALEORDER.ADK_SALEORDER_NO
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setAdkSaleorderNo(String adkSaleorderNo) {
        this.adkSaleorderNo = adkSaleorderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.CREATE_MOBILE
     *
     * @return the value of T_SALEORDER.CREATE_MOBILE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getCreateMobile() {
        return createMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.CREATE_MOBILE
     *
     * @param createMobile the value for T_SALEORDER.CREATE_MOBILE
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setCreateMobile(String createMobile) {
        this.createMobile = createMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.BDTRADER_COMMENTS
     *
     * @return the value of T_SALEORDER.BDTRADER_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getBdtraderComments() {
        return bdtraderComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.BDTRADER_COMMENTS
     *
     * @param bdtraderComments the value for T_SALEORDER.BDTRADER_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setBdtraderComments(String bdtraderComments) {
        this.bdtraderComments = bdtraderComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.CLOSE_COMMENTS
     *
     * @return the value of T_SALEORDER.CLOSE_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getCloseComments() {
        return closeComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.CLOSE_COMMENTS
     *
     * @param closeComments the value for T_SALEORDER.CLOSE_COMMENTS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setCloseComments(String closeComments) {
        this.closeComments = closeComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.BD_MOBILE_TIME
     *
     * @return the value of T_SALEORDER.BD_MOBILE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getBdMobileTime() {
        return bdMobileTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.BD_MOBILE_TIME
     *
     * @param bdMobileTime the value for T_SALEORDER.BD_MOBILE_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setBdMobileTime(Long bdMobileTime) {
        this.bdMobileTime = bdMobileTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.WEB_TAKE_DELIVERY_TIME
     *
     * @return the value of T_SALEORDER.WEB_TAKE_DELIVERY_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Long getWebTakeDeliveryTime() {
        return webTakeDeliveryTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.WEB_TAKE_DELIVERY_TIME
     *
     * @param webTakeDeliveryTime the value for T_SALEORDER.WEB_TAKE_DELIVERY_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setWebTakeDeliveryTime(Long webTakeDeliveryTime) {
        this.webTakeDeliveryTime = webTakeDeliveryTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.ACTION_ID
     *
     * @return the value of T_SALEORDER.ACTION_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getActionId() {
        return actionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.ACTION_ID
     *
     * @param actionId the value for T_SALEORDER.ACTION_ID
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setActionId(Integer actionId) {
        this.actionId = actionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.IS_COUPONS
     *
     * @return the value of T_SALEORDER.IS_COUPONS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getIsCoupons() {
        return isCoupons;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.IS_COUPONS
     *
     * @param isCoupons the value for T_SALEORDER.IS_COUPONS
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setIsCoupons(Byte isCoupons) {
        this.isCoupons = isCoupons;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.EL_SALEORDRE_NO
     *
     * @return the value of T_SALEORDER.EL_SALEORDRE_NO
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getElSaleordreNo() {
        return elSaleordreNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.EL_SALEORDRE_NO
     *
     * @param elSaleordreNo the value for T_SALEORDER.EL_SALEORDRE_NO
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setElSaleordreNo(String elSaleordreNo) {
        this.elSaleordreNo = elSaleordreNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.COUPONMONEY
     *
     * @return the value of T_SALEORDER.COUPONMONEY
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public BigDecimal getCouponmoney() {
        return couponmoney;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.COUPONMONEY
     *
     * @param couponmoney the value for T_SALEORDER.COUPONMONEY
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setCouponmoney(BigDecimal couponmoney) {
        this.couponmoney = couponmoney;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.ORIGINAL_AMOUNT
     *
     * @return the value of T_SALEORDER.ORIGINAL_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public BigDecimal getOriginalAmount() {
        return originalAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.ORIGINAL_AMOUNT
     *
     * @param originalAmount the value for T_SALEORDER.ORIGINAL_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setOriginalAmount(BigDecimal originalAmount) {
        this.originalAmount = originalAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.IS_PRINTOUT
     *
     * @return the value of T_SALEORDER.IS_PRINTOUT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getIsPrintout() {
        return isPrintout;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.IS_PRINTOUT
     *
     * @param isPrintout the value for T_SALEORDER.IS_PRINTOUT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setIsPrintout(Byte isPrintout) {
        this.isPrintout = isPrintout;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.OUT_IS_FLAG
     *
     * @return the value of T_SALEORDER.OUT_IS_FLAG
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Byte getOutIsFlag() {
        return outIsFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.OUT_IS_FLAG
     *
     * @param outIsFlag the value for T_SALEORDER.OUT_IS_FLAG
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setOutIsFlag(Byte outIsFlag) {
        this.outIsFlag = outIsFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.UPDATE_DATA_TIME
     *
     * @return the value of T_SALEORDER.UPDATE_DATA_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Date getUpdateDataTime() {
        return updateDataTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.UPDATE_DATA_TIME
     *
     * @param updateDataTime the value for T_SALEORDER.UPDATE_DATA_TIME
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setUpdateDataTime(Date updateDataTime) {
        this.updateDataTime = updateDataTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.REAL_PAY_AMOUNT
     *
     * @return the value of T_SALEORDER.REAL_PAY_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public BigDecimal getRealPayAmount() {
        return realPayAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.REAL_PAY_AMOUNT
     *
     * @param realPayAmount the value for T_SALEORDER.REAL_PAY_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setRealPayAmount(BigDecimal realPayAmount) {
        this.realPayAmount = realPayAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.REAL_RETURN_AMOUNT
     *
     * @return the value of T_SALEORDER.REAL_RETURN_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public BigDecimal getRealReturnAmount() {
        return realReturnAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.REAL_RETURN_AMOUNT
     *
     * @param realReturnAmount the value for T_SALEORDER.REAL_RETURN_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setRealReturnAmount(BigDecimal realReturnAmount) {
        this.realReturnAmount = realReturnAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.REAL_TOTAL_AMOUNT
     *
     * @return the value of T_SALEORDER.REAL_TOTAL_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public BigDecimal getRealTotalAmount() {
        return realTotalAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.REAL_TOTAL_AMOUNT
     *
     * @param realTotalAmount the value for T_SALEORDER.REAL_TOTAL_AMOUNT
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setRealTotalAmount(BigDecimal realTotalAmount) {
        this.realTotalAmount = realTotalAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_SALEORDER.SEND_TO_PC
     *
     * @return the value of T_SALEORDER.SEND_TO_PC
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Integer getSendToPc() {
        return sendToPc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_SALEORDER.SEND_TO_PC
     *
     * @param sendToPc the value for T_SALEORDER.SEND_TO_PC
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setSendToPc(Integer sendToPc) {
        this.sendToPc = sendToPc;
    }
}