package com.vedeng.crm.trader.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.dto.TraderContactDto;
import com.vedeng.erp.trader.service.TraderContactService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/25
 */
@ExceptionController
@RestController
@RequestMapping(value = {"/crm/traderContact/profile","/crm/traderContact/m"})
@Slf4j
public class TraderContactApi {


    @Autowired
    private TraderContactService traderContactService;


    /**
     *  分页查询
     *
     * @param traderId
     * @param name
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "/page")
    @NoNeedAccessAuthorization
    public R<?> page(@RequestParam(required=true) Integer traderId,@RequestParam (required = false) String name,@RequestParam (required = false) String mobile,@RequestParam (required = true)  Integer pageSize ,@RequestParam (required = false,defaultValue = "1")  Integer accurateMatch   ) {
        PageParam<TraderContactDto> recordDtoPageParam = new PageParam<>();
        TraderContactDto traderContactDto = new TraderContactDto();
        traderContactDto.setTraderId(traderId);
        traderContactDto.setName(name);
        traderContactDto.setMobile(mobile);
        recordDtoPageParam.setPageSize(pageSize);
        recordDtoPageParam.setParam(traderContactDto);
        if( accurateMatch == 1){
            return R.success(traderContactService.searchByTraderIdAndMobileAccurateMatch(recordDtoPageParam));
        }else{
            return R.success(traderContactService.searchByTraderIdAndMobile(recordDtoPageParam));
        }

    }

    //    @RequestMapping(value = "/page", method = RequestMethod.POST)
//    @NoNeedAccessAuthorization
//    public R<?> page(@RequestBody PageParam<TraderContactDto> recordDtoPageParam) {
//        return R.success(traderContactService.erpPage(recordDtoPageParam));
//    }


}
