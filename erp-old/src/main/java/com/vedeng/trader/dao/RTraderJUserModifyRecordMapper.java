package com.vedeng.trader.dao;

import com.vedeng.trader.model.RTraderJUserModifyRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO交易者归属变更记录Mapper接口
 * @date 2021/8/4 10:36
 */
public interface RTraderJUserModifyRecordMapper {

    int insert(RTraderJUserModifyRecord rTraderJUserModifyRecord);

    int updateByPrimaryKeySelective(RTraderJUserModifyRecord rTraderJUserModifyRecord);

    int updateEndTimeByTraderId(RTraderJUserModifyRecord rTraderJUserModifyRecord);

    int updateEndTimeByUserId(@Param(value = "list") List<RTraderJUserModifyRecord> list,
                              @Param(value = "endTime") Long endTime,
                              @Param(value = "updater") Integer updater);

    List<RTraderJUserModifyRecord> getRTraderJUserModifyRecordByUserId(@Param(value = "userId") Integer userId,@Param(value = "traderType") Integer traderType);

    int saveRTraderJUserModifyRecordBatch(List<RTraderJUserModifyRecord> list);

}
