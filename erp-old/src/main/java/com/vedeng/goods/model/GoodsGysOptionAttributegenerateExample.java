package com.vedeng.goods.model;

import java.util.ArrayList;
import java.util.List;

public class GoodsGysOptionAttributegenerateExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public GoodsGysOptionAttributegenerateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andGoodsSysOptionAttributeIdIsNull() {
            addCriterion("GOODS_SYS_OPTION_ATTRIBUTE_ID is null");
            return (Criteria) this;
        }

        public Criteria andGoodsSysOptionAttributeIdIsNotNull() {
            addCriterion("GOODS_SYS_OPTION_ATTRIBUTE_ID is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsSysOptionAttributeIdEqualTo(Integer value) {
            addCriterion("GOODS_SYS_OPTION_ATTRIBUTE_ID =", value, "goodsSysOptionAttributeId");
            return (Criteria) this;
        }

        public Criteria andGoodsSysOptionAttributeIdNotEqualTo(Integer value) {
            addCriterion("GOODS_SYS_OPTION_ATTRIBUTE_ID <>", value, "goodsSysOptionAttributeId");
            return (Criteria) this;
        }

        public Criteria andGoodsSysOptionAttributeIdGreaterThan(Integer value) {
            addCriterion("GOODS_SYS_OPTION_ATTRIBUTE_ID >", value, "goodsSysOptionAttributeId");
            return (Criteria) this;
        }

        public Criteria andGoodsSysOptionAttributeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("GOODS_SYS_OPTION_ATTRIBUTE_ID >=", value, "goodsSysOptionAttributeId");
            return (Criteria) this;
        }

        public Criteria andGoodsSysOptionAttributeIdLessThan(Integer value) {
            addCriterion("GOODS_SYS_OPTION_ATTRIBUTE_ID <", value, "goodsSysOptionAttributeId");
            return (Criteria) this;
        }

        public Criteria andGoodsSysOptionAttributeIdLessThanOrEqualTo(Integer value) {
            addCriterion("GOODS_SYS_OPTION_ATTRIBUTE_ID <=", value, "goodsSysOptionAttributeId");
            return (Criteria) this;
        }

        public Criteria andGoodsSysOptionAttributeIdIn(List<Integer> values) {
            addCriterion("GOODS_SYS_OPTION_ATTRIBUTE_ID in", values, "goodsSysOptionAttributeId");
            return (Criteria) this;
        }

        public Criteria andGoodsSysOptionAttributeIdNotIn(List<Integer> values) {
            addCriterion("GOODS_SYS_OPTION_ATTRIBUTE_ID not in", values, "goodsSysOptionAttributeId");
            return (Criteria) this;
        }

        public Criteria andGoodsSysOptionAttributeIdBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_SYS_OPTION_ATTRIBUTE_ID between", value1, value2, "goodsSysOptionAttributeId");
            return (Criteria) this;
        }

        public Criteria andGoodsSysOptionAttributeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_SYS_OPTION_ATTRIBUTE_ID not between", value1, value2, "goodsSysOptionAttributeId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIsNull() {
            addCriterion("GOODS_ID is null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIsNotNull() {
            addCriterion("GOODS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsIdEqualTo(Integer value) {
            addCriterion("GOODS_ID =", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotEqualTo(Integer value) {
            addCriterion("GOODS_ID <>", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThan(Integer value) {
            addCriterion("GOODS_ID >", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("GOODS_ID >=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThan(Integer value) {
            addCriterion("GOODS_ID <", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdLessThanOrEqualTo(Integer value) {
            addCriterion("GOODS_ID <=", value, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdIn(List<Integer> values) {
            addCriterion("GOODS_ID in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotIn(List<Integer> values) {
            addCriterion("GOODS_ID not in", values, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_ID between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andGoodsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("GOODS_ID not between", value1, value2, "goodsId");
            return (Criteria) this;
        }

        public Criteria andAttributeTypeIsNull() {
            addCriterion("ATTRIBUTE_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andAttributeTypeIsNotNull() {
            addCriterion("ATTRIBUTE_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andAttributeTypeEqualTo(Integer value) {
            addCriterion("ATTRIBUTE_TYPE =", value, "attributeType");
            return (Criteria) this;
        }

        public Criteria andAttributeTypeNotEqualTo(Integer value) {
            addCriterion("ATTRIBUTE_TYPE <>", value, "attributeType");
            return (Criteria) this;
        }

        public Criteria andAttributeTypeGreaterThan(Integer value) {
            addCriterion("ATTRIBUTE_TYPE >", value, "attributeType");
            return (Criteria) this;
        }

        public Criteria andAttributeTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("ATTRIBUTE_TYPE >=", value, "attributeType");
            return (Criteria) this;
        }

        public Criteria andAttributeTypeLessThan(Integer value) {
            addCriterion("ATTRIBUTE_TYPE <", value, "attributeType");
            return (Criteria) this;
        }

        public Criteria andAttributeTypeLessThanOrEqualTo(Integer value) {
            addCriterion("ATTRIBUTE_TYPE <=", value, "attributeType");
            return (Criteria) this;
        }

        public Criteria andAttributeTypeIn(List<Integer> values) {
            addCriterion("ATTRIBUTE_TYPE in", values, "attributeType");
            return (Criteria) this;
        }

        public Criteria andAttributeTypeNotIn(List<Integer> values) {
            addCriterion("ATTRIBUTE_TYPE not in", values, "attributeType");
            return (Criteria) this;
        }

        public Criteria andAttributeTypeBetween(Integer value1, Integer value2) {
            addCriterion("ATTRIBUTE_TYPE between", value1, value2, "attributeType");
            return (Criteria) this;
        }

        public Criteria andAttributeTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("ATTRIBUTE_TYPE not between", value1, value2, "attributeType");
            return (Criteria) this;
        }

        public Criteria andAttributeIdIsNull() {
            addCriterion("ATTRIBUTE_ID is null");
            return (Criteria) this;
        }

        public Criteria andAttributeIdIsNotNull() {
            addCriterion("ATTRIBUTE_ID is not null");
            return (Criteria) this;
        }

        public Criteria andAttributeIdEqualTo(Integer value) {
            addCriterion("ATTRIBUTE_ID =", value, "attributeId");
            return (Criteria) this;
        }

        public Criteria andAttributeIdNotEqualTo(Integer value) {
            addCriterion("ATTRIBUTE_ID <>", value, "attributeId");
            return (Criteria) this;
        }

        public Criteria andAttributeIdGreaterThan(Integer value) {
            addCriterion("ATTRIBUTE_ID >", value, "attributeId");
            return (Criteria) this;
        }

        public Criteria andAttributeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ATTRIBUTE_ID >=", value, "attributeId");
            return (Criteria) this;
        }

        public Criteria andAttributeIdLessThan(Integer value) {
            addCriterion("ATTRIBUTE_ID <", value, "attributeId");
            return (Criteria) this;
        }

        public Criteria andAttributeIdLessThanOrEqualTo(Integer value) {
            addCriterion("ATTRIBUTE_ID <=", value, "attributeId");
            return (Criteria) this;
        }

        public Criteria andAttributeIdIn(List<Integer> values) {
            addCriterion("ATTRIBUTE_ID in", values, "attributeId");
            return (Criteria) this;
        }

        public Criteria andAttributeIdNotIn(List<Integer> values) {
            addCriterion("ATTRIBUTE_ID not in", values, "attributeId");
            return (Criteria) this;
        }

        public Criteria andAttributeIdBetween(Integer value1, Integer value2) {
            addCriterion("ATTRIBUTE_ID between", value1, value2, "attributeId");
            return (Criteria) this;
        }

        public Criteria andAttributeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ATTRIBUTE_ID not between", value1, value2, "attributeId");
            return (Criteria) this;
        }

        public Criteria andAttributeOtherIsNull() {
            addCriterion("ATTRIBUTE_OTHER is null");
            return (Criteria) this;
        }

        public Criteria andAttributeOtherIsNotNull() {
            addCriterion("ATTRIBUTE_OTHER is not null");
            return (Criteria) this;
        }

        public Criteria andAttributeOtherEqualTo(String value) {
            addCriterion("ATTRIBUTE_OTHER =", value, "attributeOther");
            return (Criteria) this;
        }

        public Criteria andAttributeOtherNotEqualTo(String value) {
            addCriterion("ATTRIBUTE_OTHER <>", value, "attributeOther");
            return (Criteria) this;
        }

        public Criteria andAttributeOtherGreaterThan(String value) {
            addCriterion("ATTRIBUTE_OTHER >", value, "attributeOther");
            return (Criteria) this;
        }

        public Criteria andAttributeOtherGreaterThanOrEqualTo(String value) {
            addCriterion("ATTRIBUTE_OTHER >=", value, "attributeOther");
            return (Criteria) this;
        }

        public Criteria andAttributeOtherLessThan(String value) {
            addCriterion("ATTRIBUTE_OTHER <", value, "attributeOther");
            return (Criteria) this;
        }

        public Criteria andAttributeOtherLessThanOrEqualTo(String value) {
            addCriterion("ATTRIBUTE_OTHER <=", value, "attributeOther");
            return (Criteria) this;
        }

        public Criteria andAttributeOtherLike(String value) {
            addCriterion("ATTRIBUTE_OTHER like", value, "attributeOther");
            return (Criteria) this;
        }

        public Criteria andAttributeOtherNotLike(String value) {
            addCriterion("ATTRIBUTE_OTHER not like", value, "attributeOther");
            return (Criteria) this;
        }

        public Criteria andAttributeOtherIn(List<String> values) {
            addCriterion("ATTRIBUTE_OTHER in", values, "attributeOther");
            return (Criteria) this;
        }

        public Criteria andAttributeOtherNotIn(List<String> values) {
            addCriterion("ATTRIBUTE_OTHER not in", values, "attributeOther");
            return (Criteria) this;
        }

        public Criteria andAttributeOtherBetween(String value1, String value2) {
            addCriterion("ATTRIBUTE_OTHER between", value1, value2, "attributeOther");
            return (Criteria) this;
        }

        public Criteria andAttributeOtherNotBetween(String value1, String value2) {
            addCriterion("ATTRIBUTE_OTHER not between", value1, value2, "attributeOther");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated do_not_delete_during_merge Thu Jun 27 19:06:50 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_GOODS_SYS_OPTION_ATTRIBUTE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}