package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchRExpressWarehouseGoodsOutInDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BatchRExpressWarehouseGoodsOutInDtoMapper {
    int deleteByPrimaryKey(Integer rExpressWarehouseGoodsOutInId);

    int insert(BatchRExpressWarehouseGoodsOutInDto record);

    int insertOrUpdate(BatchRExpressWarehouseGoodsOutInDto record);

    int insertOrUpdateSelective(BatchRExpressWarehouseGoodsOutInDto record);

    int insertSelective(BatchRExpressWarehouseGoodsOutInDto record);

    BatchRExpressWarehouseGoodsOutInDto selectByPrimaryKey(Integer rExpressWarehouseGoodsOutInId);

    int updateByPrimaryKeySelective(BatchRExpressWarehouseGoodsOutInDto record);

    int updateByPrimaryKey(BatchRExpressWarehouseGoodsOutInDto record);

    int updateBatch(List<BatchRExpressWarehouseGoodsOutInDto> list);

    int updateBatchSelective(List<BatchRExpressWarehouseGoodsOutInDto> list);

    int batchInsert(@Param("list") List<BatchRExpressWarehouseGoodsOutInDto> list);

    List<BatchRExpressWarehouseGoodsOutInDto> findByExpressIdAndIsDelete(@Param("expressId") Integer expressId, @Param("isDelete") Integer isDelete);


}