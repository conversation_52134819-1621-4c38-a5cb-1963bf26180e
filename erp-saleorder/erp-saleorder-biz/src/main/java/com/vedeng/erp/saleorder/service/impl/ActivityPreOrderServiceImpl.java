package com.vedeng.erp.saleorder.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.rabbitmq.MsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.erp.saleorder.dao.ActivityPreOrderMapper;
import com.vedeng.erp.saleorder.dao.extend.ActivityPreOrderExtendMapper;
import com.vedeng.erp.saleorder.domain.dto.KYGActivityPreOrderProducerDto;
import com.vedeng.erp.saleorder.domain.entity.ActivityPreOrderEntity;
import com.vedeng.erp.saleorder.dto.ActivityPreOrderDto;
import com.vedeng.erp.saleorder.mapstruct.ActivityPreOrderConvertor;
import com.vedeng.erp.saleorder.rebbitmq.producer.KYGActivityPreOrderProducer;
import com.vedeng.erp.saleorder.service.ActivityPreOrderApiService;
import com.vedeng.erp.saleorder.service.ActivityPreOrderService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.LogicalEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.rmi.ServerException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 活动预处理订单。后续可转真实订单
 * @date 2022/12/22 13:48
 */
@Service
@Slf4j
public class ActivityPreOrderServiceImpl implements ActivityPreOrderService, ActivityPreOrderApiService {

    @Autowired
    ActivityPreOrderMapper activityPreOrderMapper;
    @Autowired
    ActivityPreOrderExtendMapper activityPreOrderExtendMapper;
    @Autowired
    MsgProducer msgProducer;
    @Autowired
    KYGActivityPreOrderProducer kygActivityPreOrderProducer;
    @Autowired
    SaleorderService saleorderService;
    @Autowired
    ActivityPreOrderConvertor activityPreOrderConvertor;

    @Override
    public ActivityPreOrderEntity getActivityPreOrderByOrderNo(String orderNo) {
        return activityPreOrderMapper.findByOrderNo(orderNo);
    }

    @Override
    public ActivityPreOrderDto getActivityPreOrderBySaleorderId(Integer saleorderId) {
        return activityPreOrderMapper.findBySaleorderId(saleorderId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void closeActivityPreOrder(String[] ids) throws IOException {
        if (ArrayUtils.isEmpty(ids)) {
            return;
        }
        StockInfoDto stockInfoDto = new StockInfoDto();
        //消费消息时 做分支处理
        stockInfoDto.setRelatedNo("ActivityPreOrder");
        List<WarehouseDto> warehouseStockList = new ArrayList<>();
        Set<Integer> integers = Arrays.stream(ids).map(Integer::valueOf).collect(Collectors.toSet());
        List<ActivityPreOrderEntity> activityPreOrderList = activityPreOrderMapper.findByActivityPreOrderIdIn(integers);
        if(CollUtil.isEmpty(activityPreOrderList)){
            return;
        }
        activityPreOrderList.forEach(entity -> {
            entity.setOrderStatus(ErpConst.FOUR);
            WarehouseDto dto = new WarehouseDto();
            dto.setActionId(entity.getActiveId());
            dto.setSku(entity.getSku());
            dto.setOccupyNum(entity.getNum());
            warehouseStockList.add(dto);
        });

        activityPreOrderMapper.updateBatchSelective(activityPreOrderList);

        if (CollectionUtils.isNotEmpty(warehouseStockList)) {
            stockInfoDto.setWarehouseStockList(warehouseStockList);
            String key = stockInfoDto.getRelatedNo() + "," + "update" + "#" + System.currentTimeMillis() + "#" + UUID.randomUUID().toString();
            stockInfoDto.setMqOnlyKey(key);
            String jsonRequest = JsonUtils.translateToJson(stockInfoDto);
            //释放库存
            log.info("start closeActivityPreOrder release stock {} {}:", key, jsonRequest);
            try {
                msgProducer.sendMsg(RabbitConfig.STOCK_SERVICE_EXCHANGE, RabbitConfig.STOCK_SERVICE_STOCKNUM_ROUTINGKEY, jsonRequest);
            } catch (Exception e) {
                log.error("error closeActivityPreOrder release stock {}:", key, e);
                throw new ServerException("更新库存,发送消息异常");
            }
        }

        if (CollUtil.isNotEmpty(activityPreOrderList)) {
            activityPreOrderList.forEach(l -> kygActivityPreOrderProducer.senMsg(new KYGActivityPreOrderProducerDto().close(l.getOrderNo())));
        }
    }


    @Override
    public ActivityPreOrderEntity queryById(Integer activityPreOrderId) {
        return activityPreOrderMapper.selectByPrimaryKey(activityPreOrderId);
    }

    @Override
    public ActivityPreOrderDto getActivityPreOrderDtoById(Integer activityPreOrderId) {
        return activityPreOrderMapper.findByActivityPreOrderId(activityPreOrderId);
    }

    @Override
    public void update(ActivityPreOrderDto dto) {
        ActivityPreOrderEntity entity = activityPreOrderConvertor.toEntity(dto);
        activityPreOrderMapper.updateByPrimaryKeySelective(entity);
        if (entity.getSaleorderId() != null) {
            if (entity.getOrderStatus() == 2) {
                log.info("科研购活动订单创建。订单号：{}", entity.getSaleorderId());
                Saleorder saleorder = saleorderService.getSaleOrderById(dto.getSaleorderId());
                kygActivityPreOrderProducer.senMsg(new KYGActivityPreOrderProducerDto().create(saleorder.getTraderId(), entity.getOrderNo(), saleorder.getSaleorderNo()));
            }
            if (entity.getOrderStatus() == 3) {
                log.info("科研购活动订单付款。订单号：{}", entity.getSaleorderId());
                Saleorder saleorder = saleorderService.getSaleOrderById(dto.getSaleorderId());
                kygActivityPreOrderProducer.senMsg(new KYGActivityPreOrderProducerDto().pay(saleorder.getTraderId(), entity.getOrderNo(), saleorder.getSaleorderNo()));
            }
        }
    }

    @Override
    public Integer findExistNumByTraderIdAndActionId(Integer actionId, Integer traderId, Integer skuId) {
        return activityPreOrderMapper.findExistNumByTraderIdAndActionId(traderId, actionId, skuId);
    }

}