package com.vedeng.flash.dto.temp;

import lombok.*;

import java.util.List;

/**
 * @program: erp.vedeng.com
 * @description: 分类
 * @author: Pusan
 * @create: 2021-06-02 09:43
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class InlineCategoryTemp {

    /** 类型iD */
    private Integer categoryId;
    private String categoryName;

    /** 下级类型 */
    List<InlineCategoryTemp> childrenCategoryList;
}
