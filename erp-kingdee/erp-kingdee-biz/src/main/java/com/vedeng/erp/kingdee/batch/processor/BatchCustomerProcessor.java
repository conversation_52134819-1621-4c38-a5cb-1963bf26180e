package com.vedeng.erp.kingdee.batch.processor;

import com.vedeng.erp.kingdee.batch.dto.BatchCustomerFinanceDto;
import com.vedeng.erp.kingdee.domain.entity.KingDeeCustomerEntity;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeCustomerMapper;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/5 11:03
 */
@Service
@Slf4j
public class BatchCustomerProcessor implements ItemProcessor<BatchCustomerFinanceDto, KingDeeCustomerDto> {

    @Autowired
    private KingDeeCustomerMapper kingDeeCustomerMapper;

    @Override
    public KingDeeCustomerDto process(BatchCustomerFinanceDto batchCustomerFinanceDto) throws Exception {
        KingDeeCustomerEntity kingDeeCustomer = kingDeeCustomerMapper.selectByFNumber(batchCustomerFinanceDto.getTraderCustomerId());
        if (kingDeeCustomer == null) {
            log.error("审计客户信息未推送过金蝶，无法更新，customer信息：{}", batchCustomerFinanceDto.getTraderCustomerId());
            return null;
        }
        KingDeeCustomerDto kingDeeCustomerDto = new KingDeeCustomerDto(KingDeeBizEnums.updateCustomer,batchCustomerFinanceDto.getTraderCustomerId(), batchCustomerFinanceDto.getTraderName());
        kingDeeCustomerDto.setTraderCustomerFinanceId(batchCustomerFinanceDto.getTraderCustomerFinanceId());
        kingDeeCustomerDto.setFUseOrgId(null);
        kingDeeCustomerDto.setFCreateOrgId(null);
        kingDeeCustomerDto.setId(kingDeeCustomer.getId());
        kingDeeCustomerDto.setFCustId(kingDeeCustomer.getFCustId());
        kingDeeCustomerDto.setFQzokKhxztext(batchCustomerFinanceDto.getCustomerNatureName());
        kingDeeCustomerDto.setFQzokZdkhfltext(batchCustomerFinanceDto.getCustomerClassName());
        kingDeeCustomerDto.setFQzokSsjt(batchCustomerFinanceDto.getGroupName());
        kingDeeCustomerDto.setFQzokYljgfltext(batchCustomerFinanceDto.getCustomerThirdTypeName());
        kingDeeCustomerDto.setFQzokKhxfltext(batchCustomerFinanceDto.getCustomerSecondTypeName());
        kingDeeCustomerDto.setFQzokYydjtext(batchCustomerFinanceDto.getHospitalLevelName());
        kingDeeCustomerDto.setFQzokSyyygt(batchCustomerFinanceDto.getHospitalName());

        return kingDeeCustomerDto;
    }
}