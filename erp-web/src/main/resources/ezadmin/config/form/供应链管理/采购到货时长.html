<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>采购到货时长</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="oLNICEVGvuM" datasource="erp-datasourcetarget" success_url="">
  <div id="APPEND_HEAD" class="append"></div>
  <div class="layui-container">
   <form id="inputForm" method="post" class="layui-form">
    <div class="layui-card" group_name="EZ_DEFAULT_GROUP">
     <div class="layui-card-header">
       EZ_DEFAULT_GROUP 
     </div>
     <div class="layui-card-body">
      <div class="layui-form-item ">
       <label class="layui-form-label">SKU_NO</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="SKU_NO" type="span" value="" item_name="SKU_NO" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
      <div class="layui-form-item "><label class="layui-form-label">商品名称</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="SKU_NAME" type="span" value="" item_name="SKU_NAME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
      <div class="layui-form-item ">
       <label class="layui-form-label">PURCHASE_TIME</label>
       <div class="layui-input-block form-group">
       </div>
      </div>
      <div class="layui-form-item "><label class="layui-form-label">采购到货时长（工作日）</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="PURCHASE_TIME" type="text" value="" item_name="PURCHASE_TIME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="required|number" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>

         <div class="layui-form-item "><label class="layui-form-label">发货方式</label>
             <div class="layui-input-block form-group"><select class="form-item" name="IS_DIRECT" type="search" value="" item_name="IS_DIRECT" placeholder="" style="" alias="" jdbctype="" data="[{&quot;V&quot;:&quot;普发&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;直发&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" lay_verify="required" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload=""></select>
             </div>
         </div>
     </div>
    </div>
   </form>
   <form id="configForm" class="layui-form">
    <div class="layui-card" group_name="表单数据配置">
     <div class="layui-card-header">
       表单数据配置 
     </div>
     <div class="layui-card-body">
     </div>
    </div>
    <div class="layui-form-item "><label class="layui-form-label">初始表达式</label>
     <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">list=select("select
            t.sku_id ID,t.sku_no as SKU_NO,
            t.sku_name as SKU_NAME,
            PURCHASE_TIME,IS_DIRECT
        from
            V_CORE_SKU t  
         where SKU_ID=${ID}");
return list.get(0)</pre>
     </div>
    </div>
    <div class="layui-form-item ">
     <label class="layui-form-label">提交表达式</label>
     <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">if(isNotBlank("PURCHASE_TIME")){
update("update V_CORE_SKU SET PURCHASE_TIME = ${PURCHASE_TIME},PURCHASE_TIME_UPDATE_TIME =now()  
 WHERE SKU_ID = ${ID}");
}else{
update("update V_CORE_SKU SET PURCHASE_TIME =null,PURCHASE_TIME_UPDATE_TIME =now()  
 WHERE SKU_ID = ${ID}");
}
          if(isNotBlank("IS_DIRECT")){
          update("update V_CORE_SKU SET IS_DIRECT =${IS_DIRECT}  WHERE SKU_ID = ${ID}");
          }
update("update V_CORE_SKU_SEARCH SET MOD_TIME =now() WHERE SKU_ID = ${ID}");</pre>
     </div>
    </div>
    <div class="layui-form-item ">
     <label class="layui-form-label">删除表达式</label>
     <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type=""></pre>
     </div>
    </div>
    <div class="layui-form-item "><label class="layui-form-label">分组表达式</label>
     <div class="layui-input-block form-group">
      <pre class="form-item layui-code" name="GROUP_DATA" type=""></pre>
     </div>
    </div>
   </form>
  </div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
  <div id="APPEND_FOOT" class="append">
  </div>
 </body>
</html>