package com.vedeng.goods.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.goods.domain.entity.SkuSceneCategoryEntity;
import com.vedeng.goods.dto.SkuSceneCategoryDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;


/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SkuSceneCategoryConvertor extends BaseMapStruct<SkuSceneCategoryEntity, SkuSceneCategoryDto> {
}
