Vue.component('ui-search-item', {
    template: `<div class="search-item" :class="{hidden: !isShow}" v-show="settingShow">
        <div class="search-label">{{label}}：</div>
        <div class="search-content">
            <slot></slot>
        </div>
    </div>`,
    name: 'ui-search-item',
    props: {
        label: {
            type: String,
            default: '标签'
        },
        lock: {
            type: Boolean,
            default: false
        },
        isShow: {
            type: Boolean,
            default: true
        },
        settingShow: {
            type: Boolean,
            default: true
        }
    }
})

Vue.component('ui-search-container', {
    template: `<div class="ui-list-wrap">
        <div class="vd-ui-search-wrap" :class="layout_hidden_value ? 'no-left' : ''">
            <div class="vd-ui-search-tab" v-if="tabList && tabList.length">
                <ui-tab :active="currentTabId || tabList[0].id" ref="searchTab" :tabList="tabList" @customOption="customOption" @change="handlerTabChange"></ui-tab>
            </div>
            <div class="vd-ui-search-container" :class="{hidden: isCalcingSearch}">
                <div class="vd-ui-search-list" ref="searchList" :style="'max-height:' + maxHeight + 'px;'" :class="{'show-line': isShowLine}">
                    <template v-if="!isLoadingSearch">
                        <slot name="filter-list"></slot>
                    </template>
                </div>
                <div class="vd-ui-search-btns">
                    <div class="search-btn-inner">
                        <ui-button type="primary" @click="listSearch">搜索</ui-button>
                        <ui-button @click="handlerReset">重置</ui-button>
                        <div class="vd-ui-search-toggle" @click="toggleSearchShow" v-if="needToggle">
                            <template v-if="!isShowLine">
                                展开全部<i class="vd-ui_icon icon-down"></i>
                            </template>
                            <template v-else>
                                收起<i class="vd-ui_icon icon-up"></i>
                            </template>
                        </div>
                        <div class="vd-ui-search-option">
                            <div class="search-option-item" @click="addToCustom" v-if="needCustom">
                                <i class="vd-ui_icon icon-add"></i>添加为自定义筛选
                            </div> 
                            <div class="search-option-item item-setting" @click="showSetting('QUERY')" v-if="needSetting">
                                <i class="vd-ui_icon icon-setting"></i>设置
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="vd-ui-list-container" v-if="!isFirst">
            <div class="vd-ui-list-inner" :class="{'no-border-top': !listOption}">
                <div class="list-container-top" v-if="listOption">
                    <div class="list-container-top-buttons">
                        <slot name="list-button"></slot>
                    </div>
                    <div class="list-select-num" v-if="listSelectData && listSelectData.length">已选{{ listSelectData.length }}项</div>
                </div>
                <div class="list-container-content">
                    <div class="list-container-setting" @click="showSetting('RESULT')" v-if="needSetting">
                        <i class="vd-ui_icon icon-setting"></i>
                    </div>
                    <div class="list-container-table" v-if="!isloading">
                        <ui-table ref="listTable" border :oneline="true" @tableItemEdit="tableItemEdit" @tablescroll="tableScroll" :headers="showHeaders" :list="list" :right-fixed="true" :can-choose="canChoose" @selectchange="handlerListSelect" @handlersort="listSort" :left-fixed-number="canChoose ? leftFixedNumber + 1 : leftFixedNumber" :fixed-top="fixedTop" @call="handlerCallNumber" :listSort="isListSort" :editValid="editValid" :editValidMsg="editValidMsg">
                            <template v-for="(slot, name) in $scopedSlots" :keys="name" #[name]="{ row }">
                                <slot :name="name" :row="row" v-if="name !== 'list-button' && name !== 'filter-list'"></slot>
                            </template>jjjjj
                        </ui-table>
                    </div>
                    <div class="list-container-pager">
                        <div class="list-container-total">共 {{totalCount}} 条</div>
                        <ui-pagination
                            :total="totalCount"
                            :pageSize="pageSize"
                            :currentPage="pageNo"
                            :jump="true"
                            v-if="!refreshPager"
                            @change="handlerPageChange"
                        ></ui-pagination>
                        <ui-select
                            :data="pageOptionList"
                            v-model="pageSize"
                            shadows
                            width="108px"
                            @change="handlerPagesizeChange"
                        ></ui-select>
                    </div>
                </div>
            </div>
        </div>
        <ui-dialog
            :visible.sync="isShowSettingDialog"
            :title="settingDialogTitle"
            width="960px"
        >
            <div class="search-setting-dlg">
                <div class="search-setting-top">
                    <div class="setting-select-all">
                        <ui-checkbox
                            :checked.sync="isSettingSelectAll"
                            label="全选"
                            @change="handlerSettingAllChange"
                        ></ui-checkbox>
                    </div>
                     <div class="setting-refresh" @click="resetSettingDialog">
                         <i class="vd-ui_icon icon-rotate"></i>恢复系统默认
                     </div>
                </div>
                <draggable v-model="tempSettingList" class="dlg-setting-list" ghost-class="placehodler" v-if="isShowSettingDialog" @sort="handlerSettingSort">
                    <div class="dlg-setting-item" v-for="(item, index) in tempSettingList" :key="index">
                        <ui-checkbox
                            :checked.sync="item.isShow"
                            :disabled="item.disabled"
                            :label="item.label"
                            @change="checkSettingSelectAll"
                        ></ui-checkbox>
                    </div>
                </draggable>
                <!--<div class="dlg-setting-list sortable" v-if="settingType === 'QUERY'">
                    <div class="dlg-setting-item" v-for="(item, index) in tempSettingList" :key="index">
                        <ui-checkbox
                            :checked.sync="item.isShow"
                            :disabled="item.disabled"
                            :label="item.label"
                            @change="checkSettingSelectAll"
                        ></ui-checkbox>
                    </div>
                </div>-->
            </div>
            <template slot="footer">
                <ui-button @click="handlerSettingChange" type="primary">确定</ui-button>
                <ui-button @click="isShowSettingDialog=false" class="close">取消</ui-button>
            </template>
        </ui-dialog>
        <ui-dialog
            :visible.sync="isShowCustomDialog"
            title="自定义筛选"
            width="720px"
        >
            <div class="form-wrap label-width-2" v-if="isShowCustomDialog">
                <ui-form-item label="标题名称" :must="true">
                    <div class="ui-col-4">
                        <ui-input v-model="customName" maxlength="7" valid="customSearchForm_customName"></ui-input>
                    </div>
                    <div class="form-tip">- 建议标题设置简约、直接、准确，最多可输入7个字</div>
                </ui-form-item>
            </div>
            <template slot="footer">
                <div class="dlg-form-footer">
                    <ui-button @click="saveCustomSearch" type="primary">保存</ui-button>
                    <ui-button @click="isShowCustomDialog=false" class="close">取消</ui-button>
                </div>
            </template>
        </ui-dialog>
    </div>`,
    props: {
        line: {
            type: Number,
            default: 2
        },
        listName: {
            type: String,
            default: ''
        },
        needCustom: {
            type: Boolean,
            default: true
        },
        needSetting: {
            type: Boolean,
            default: true
        },
        defaultTab: {
            type: Array,
            default() {
                return []
            }
        },
        list: {
            type: Array,
            default: () => {
                return [];
            },
        },
        headers: {
            type: Array,
            default: () => {
                return [];
            },
        },
        url: {
            type: String,
            default: ''
        },
        leftFixedNumber: {
            type: Number,
            default: 0
        },
        searchParams: {
            type: Object,
            default: () => {
                return {};
            },
        },
        defaultSearchParams: {
            type: Object,
            default: () => {
                return {};
            },
        },
        searchDefaultParams: {
            type: Object,
            default: () => {
                return {};
            },
        },
        isListSort: {
            type: Boolean,
            default: false
        },
        canChoose: {
            type: Boolean,
            default: true
        },
        listOption: {
            type: Boolean,
            default: true
        },
        currentTabId: {
            type: String,
            default: ''
        },
        editValid: {
            type: Function,
            default: null
        },
        editValidMsg: {
            type: String,
            default: ''
        },
        customReset: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            maxHeight: '',
            isShowLine: false,
            needToggle: false,
            settingList: {}, //筛选设置
            tableSettingList: {},
            isShowSettingDialog: false,
            isSettingSelectAll: true,
            tempSettingList: {},
            isShowCustomDialog: false,
            customName: '',
            tabList: [],
            settingDialogTitle: '',
            settingType: '', //QUERY 查询条件 RESULT查询结果
            showHeaders: [],
            pageOptionList: [
                { label: '50条/页', value: '50' },
                { label: '100条/页', value: '100' },
                { label: '200条/页', value: '200' }
            ],
            pageSize: 50,
            listSelectData: [],
            fixedTop: layout_hidden_value ? 0 : 50, //表头悬浮高度，根据有没有头部判断
            pageNo: 1,
            totalCount: 0,
            prevSearchParams: {}, //搜索参数
            isFirst: true, //第一次请求不加载列表，防止默认出现空列表
            refreshPager: false, //更新分页器,
            conditionList: [],
            isLoadingSearch: false,
            isCalcingSearch: false,
            customEditId: '',
            querySettingId: '',
            resultSettingId: '',
            sortValue: '',
            isloading: false,
        }
    },
    computed: {

    },
    created() {
        this.maxHeight = this.line * 43;
        this.showHeaders = JSON.parse(JSON.stringify(this.headers));
    },
    mounted() {
        window.addEventListener('resize', this.checkNeedToggle)
        this.initTab();
        this.getSettingList();
        this.listSearch();
    },
    methods: {
        initTab() {
            this.tabList = this.defaultTab.concat([])
        },
        toggleSearchShow() {
            this.isShowLine = !this.isShowLine;
            this.checkSearchItemStatus();

            this.$nextTick(() => {
                let event = new Event('scroll');
                window.dispatchEvent(event);
            })
        },
        checkNeedToggle() {
            let scrollH = this.$refs.searchList.scrollHeight;

            if (scrollH > this.maxHeight + 10) {
                this.needToggle = true;
            } else {
                this.isShowLine = false;
                this.needToggle = false;
            }

            this.checkSearchItemStatus();
        },
        checkSearchItemStatus() {
            this.$children.forEach(item => {
                if (item.$options.name == 'ui-search-item') {
                    if (item.$el.offsetTop > 43 * (this.line - 1) + 10 && !this.isShowLine) {
                        item.isShow = false;
                    } else {
                        item.isShow = true
                    }
                }
            })

            this.$nextTick(() => {
                this.isCalcingSearch = false;
            })
        },
        getSettingList() {
            this.$children.forEach((searchItem, index) => {
                if (searchItem.$options.name === 'ui-search-item' && searchItem.label) {
                    this.settingList[searchItem.label] = {
                        isShow: true,
                        disabled: searchItem.lock ? true : false,
                        sort: index
                    }
                }
            });

            this.checkNeedToggle();

            this.headers.forEach((item, index) => {
                if (item.key !== 'option' && !item.hidden) {
                    this.tableSettingList[item.label] = {
                        isShow: true,
                        disabled: item.lock || item.key === 'option' ? true : false,
                        sort: index
                    }
                }
            })

            //获取后台存储设置
            this.$axios.post('/crm/common/profile/searchList', {
                searchFromEnum: this.listName
            }).then(({ data }) => {
                if (data.success) {
                    let customList = data.data.conditionList || [];

                    customList.forEach(item => {
                        this.tabList.push({
                            label: item.searchName,
                            id: item.id,
                            isCustom: true,
                            customData: JSON.parse(item.searchContent)
                        })
                    })

                    this.conditionList = customList;

                    let querySettingData = data.data.queryMap || {};
                    this.querySettingId = querySettingData.id || '';

                    if (querySettingData.searchContent) {
                        try {
                            let settingList = JSON.parse(querySettingData.searchContent);
                            if (settingList && settingList.length) {
                                settingList.forEach(item => {
                                    if (this.settingList[item.label]) {
                                        if (!item.isShow) {
                                            this.settingList[item.label].isShow = item.isShow;
                                        }
                                        this.settingList[item.label].sort = (item.sort || item.sort === 0) ? item.sort : this.tableSettingList[item.label].sort;
                                    }
                                });
                            }

                            console.log(this.settingList)

                            this.checkSettingShow();
                        } catch (error) {
                            console.error(error)
                        }
                    }

                    let resultSettingData = data.data.resultMap || {};
                    this.resultSettingId = resultSettingData.id || '';

                    if (resultSettingData.searchContent) {
                        try {
                            let settingList = JSON.parse(resultSettingData.searchContent);
                            if (settingList && settingList.length) {
                                settingList.forEach(item => {
                                    if (this.tableSettingList[item.label]) {
                                        this.tableSettingList[item.label].isShow = item.isShow;
                                        this.tableSettingList[item.label].sort = (item.sort || item.sort === 0) ? item.sort : this.tableSettingList[item.label].sort;
                                    }
                                });
                            }

                            this.checkTableSettingShow();
                        } catch (error) {
                            console.error(error)
                        }
                    }
                }
            });
        },
        showSetting(type) {
            this.isShowSettingDialog = true;
            this.settingType = type;
            let tempSettingList = [];
            if (type === 'QUERY') {
                this.settingDialogTitle = '自定义筛选字段';
                for (let item in this.settingList) {
                    tempSettingList.push({
                        label: item,
                        ...this.settingList[item]
                    })
                }
            }
            if (type === 'RESULT') {
                this.settingDialogTitle = '自定义显示列';
                for (let item in this.tableSettingList) {
                    tempSettingList.push({
                        label: item,
                        ...this.tableSettingList[item]
                    })
                }
            }

            tempSettingList.sort((a, b) => {
                return a.sort - b.sort;
            })
            this.tempSettingList = tempSettingList;

            this.$nextTick(() => {
                this.checkSettingSelectAll();
            })
        },
        handlerSettingAllChange() {
            for (let item in this.tempSettingList) {
                if (!this.tempSettingList[item].disabled) {
                    this.tempSettingList[item].isShow = this.isSettingSelectAll;
                }
            }
        },
        checkSettingSelectAll() {
            let flag = true;
            for (let item in this.tempSettingList) {
                if (!this.tempSettingList[item].isShow) {
                    flag = false;
                }
            }
            this.isSettingSelectAll = flag;
        },
        handlerSettingChange() {
            // for (let item in this.tempSettingList) {
            //     if (!this.tempSettingList[item].isShow) {
            //         hiddenList[item] = 1;
            //     }
            // }

            this.isShowSettingDialog = false;

            let id = "";
            if (this.settingType === 'QUERY') {
                id = this.querySettingId || '';
                this.tempSettingList.forEach((item, index) => {
                    for (let key in this.settingList) {
                        if (item.label == key) {
                            this.settingList[key].sort = item.sort;
                            this.settingList[key].isShow = item.isShow;
                        }
                    }
                })
            } else if (this.settingType === 'RESULT') {
                id = this.resultSettingId || '';

                this.tempSettingList.forEach((item, index) => {
                    for (let key in this.tableSettingList) {
                        if (item.label == key) {
                            this.tableSettingList[key].sort = item.sort;
                            this.tableSettingList[key].isShow = item.isShow;
                        }
                    }
                })
            }

            //将修改的设置存储到后台
            this.$axios.post('/crm/common/profile/saveSearchConfig', {
                searchFromEnum: this.listName,
                searchType: this.settingType,
                searchContent: JSON.stringify(this.tempSettingList),
                id: id
            }).then(({ data }) => {
                if (data.success) {
                    if (this.settingType === 'QUERY') {
                        this.checkSettingShow();
                    }

                    if (this.settingType === 'RESULT') {
                        this.checkTableSettingShow();
                    }
                } else {
                    this.$message.warn(data.message);
                }
            })
        },
        //恢复系统默认
        resetSettingDialog() {
            let setting = [];

            if (this.settingType === 'RESULT') {
                this.headers.forEach((item, index) => {
                    if (item.key !== 'option' || item.hidden) {
                        setting.push({
                            label: item.label,
                            isShow: true,
                            disabled: item.lock ? true : false,
                            sort: index
                        })
                    }
                })
            }

            if (this.settingType === 'QUERY') {
                let num = 0;
                this.$children.forEach((searchItem, index) => {
                    if (searchItem.$options.name === 'ui-search-item' && searchItem.label) {
                        setting.push({
                            isShow: true,
                            disabled: searchItem.lock ? true : false,
                            sort: num,
                            label: searchItem.label
                        })
                        num++;
                    }
                })
            }

            this.tempSettingList = setting;
            this.checkSettingSelectAll();
        },
        checkSettingShow() {
            let list = [];
            let num = 0;

            this.$children.forEach((searchItem, index) => {
                if (searchItem.$options.name && searchItem.label) {
                    if (this.settingList[searchItem.label] && !this.settingList[searchItem.label].isShow) {
                        searchItem.settingShow = false;
                    } else {
                        searchItem.settingShow = true;
                    }

                    list.push({
                        el: searchItem.$el,
                        sort: (this.settingList[searchItem.label].sort || this.settingList[searchItem.label].sort === 0) ? this.settingList[searchItem.label].sort : num
                    })

                    num++;
                }
            });

            list.sort((a, b) => {
                return a.sort - b.sort;
            })

            list.forEach(item => {
                this.$refs.searchList.append(item.el);
            })

            this.$nextTick(() => {
                this.checkNeedToggle();
            })
        },
        checkTableSettingShow() {
            let newHeaders = [];
            let option = [];
            this.headers.forEach((item, index) => {
                if(item.hidden) {
                    newHeaders.push({
                        sort: index,
                        ...item
                    });
                } else if (this.tableSettingList[item.label] && this.tableSettingList[item.label].isShow) {
                    newHeaders.push({
                        sort: this.tableSettingList[item.label].sort,
                        ...item
                    });
                }

                if (item.key === 'option') {
                    option = item;
                }
            })

            newHeaders.sort((a, b) => {
                return a.sort - b.sort;
            })
            newHeaders = newHeaders.concat(option);

            this.showHeaders = newHeaders;

            this.$nextTick(() => {
                this.$refs.listTable && this.$refs.listTable.initTable();
            })
        },
        addToCustom() {
            let num = 0;
            this.tabList.forEach(item => {
                if (item.isCustom) {
                    num++;
                }
            })
            if (num >= 5) {
                this.$message.warn('最多支持创建5个固定查询');
                return;
            }

            this.customEditId = '';
            this.showCustomDialog();
        },
        showCustomDialog() {
            this.isShowCustomDialog = true;
            this.customName = "";

            this.$form.rules({
                customName: {
                    required: '请填写标题名称'
                },
            }, 'customSearchForm', this)
        },
        saveCustomSearch() {
            if (this.$form.validForm('customSearchForm')) {
                this.$axios.post('/crm/common/profile/saveSearchConfig', {
                    searchName: this.customName,
                    searchFromEnum: this.listName,
                    searchType: "LIST",
                    searchContent: JSON.stringify(this.searchParams),
                    id: this.customEditId || ''
                }).then(({ data }) => {
                    if (data.success) {
                        this.$message.success(this.customEditId ? '操作成功' : '新增成功')

                        if (this.customEditId) {
                            this.tabList.forEach(item => {
                                if (item.id == this.customEditId) {
                                    item.label = this.customName;
                                }
                            })
                        } else {
                            this.tabList.push({
                                label: this.customName,
                                id: data.data,
                                isCustom: true,
                                customData: this.searchParams
                            })
                        }


                        this.isShowCustomDialog = false;
                    } else {
                        this.$message.warn(data.message);
                    }
                })
            }
        },
        deleteConfirm() {
            let _this = this;
            this.$popup.warn({
                message: '删除后将无法恢复，您确定需要删除吗？',
                buttons: [{
                    txt: '删除',
                    btnClass: 'delete',
                    callback() {
                        _this.$axios.post('/crm/common/profile/deleteSearchConfig', {
                            id: _this.customEditId
                        }).then(({ data }) => {
                            if (data.success) {
                                _this.tabList.forEach((item, index) => {
                                    if (item.id == _this.customEditId) {
                                        _this.tabList.splice(index, 1);
                                        if (_this.$refs.searchTab.current == item.id) {
                                            _this.$refs.searchTab.handlerChangeCurrent(_this.tabList[0]);
                                        }
                                    }
                                })

                                _this.$message.success('删除成功');
                            } else {
                                _this.$message.warn(data.message);
                            }
                        })
                    }
                },
                {
                    txt: '取消',
                }]
            })
        },
        customOption(params) {
            this.customEditId = params.item.id;
            if (params.type === 'rename') {
                this.showCustomDialog();
                this.customName = params.item.label;
            }

            if (params.type === 'delete') {
                this.deleteConfirm();
            }
        },
        handlerTabChange(id, item) {
            document.body.click()
            console.log('-----------')

            setTimeout(() => {
                if (item.isCustom) {
                    this.isLoadingSearch = true;
                    this.isCalcingSearch = true;
                    this.searchParams = JSON.parse(JSON.stringify(item.customData));
                    this.$parent.searchParams = JSON.parse(JSON.stringify(item.customData));
                } else if (item.id === 'all') {
                    this.isLoadingSearch = true;
                    this.isCalcingSearch = true;
                    this.clearParams();
                    console.log('all')
                    this.searchParams = JSON.parse(JSON.stringify(Object.assign({} ,this.searchParams, this.searchDefaultParams)));
                    this.$parent.searchParams = JSON.parse(JSON.stringify(Object.assign({} ,this.searchParams, this.searchDefaultParams)));
                    this.isLoadingSearch = true;
                } else {
                    this.$emit('listtabchange', item)
                    return;
                }

                setTimeout(() => {
                    this.isLoadingSearch = false;
                    this.listSearch();
                    this.$nextTick(() => {
                        setTimeout(() => {
                            this.checkSearchItemStatus();
                            this.checkSettingShow();
                        })
                    })
                })
            })
        },
        clearParams() {
            for (let item in this.searchParams) {
                if (typeof this.searchParams[item] === 'string' || typeof this.searchParams[item] === 'number') {
                    this.searchParams[item] = '';
                } else if (Array.isArray(this.searchParams[item])) {
                    this.searchParams[item] = [];
                } else if (typeof this.searchParams[item] === 'object') {
                    this.searchParams[item] = {};
                }
            }
        },
        handlerReset() {
            if(this.customReset) {
                this.$emit('reset');
            } else {
                this.clearParams();
                this.$emit('afterreset');

                this.isLoadingSearch = true;
    
                setTimeout(() => {
                    this.isLoadingSearch = false;
                    this.listSearch();
                    this.$nextTick(() => {
                        setTimeout(() => {
                            this.checkSearchItemStatus();
                            this.checkSettingShow();
                        })
                    })
                })
    
                this.$emit('reset');
            }
        },
        handlerListSelect(data) {
            this.listSelectData = data;
        },
        getSelectedData(key) {
            let ids = [];

            if (key) {
                this.listSelectData.forEach(item => {
                    ids.push(item[key]);
                })

                return ids;
            } else {
                return this.listSelectData;
            }
        },
        listSort(data) {
            if (this.isListSort) {
                let item = data.item;
                let key = item.sortKey ? item.sortKey : data.key;
                let orderPrase = {
                    up: key + ' asc',
                    down: key + ' desc',
                };

                this.sortValue = orderPrase[data.sort] || '';
                this.listSearch({isSort: 1});
                this.$emit('handlersort', data)
            }
        },
        handlerPageChange(num) {
            this.pageNo = num;

            let isSort = this.sortValue ? 1 : 0;

            this.listSearch({ isPage: 1, isSort })
        },
        handlerPagesizeChange() {
            this.pageNo = 1;
            this.listSearch()
        },
        refresh() {
            this.listSearch({ isRefresh: 1 });
        },
        listSearch(searchData) {
            GLOBAL.showGlobalLoading();

            let { isPage, isSort, isRefresh } = searchData || {};

            let params = {};

            if (!(isPage || isSort || isRefresh)) {
                this.prevSearchParams = JSON.parse(JSON.stringify(this.searchParams));
                this.pageNo = 1;
                this.sortValue = "";
                params = this.searchParams;
            } else {
                params = this.prevSearchParams;
            }

            for (let item in params) {
                if (typeof params[item] === 'string') {
                    params[item] = params[item].trim();
                }
            }

            this.$axios.post(this.url, {
                pageNum: this.pageNo,
                pageSize: this.pageSize,
                orderBy: this.sortValue,
                param: {
                    ...params,
                    ...this.defaultSearchParams,
                }
            }).then(({ data }) => {
                GLOBAL.hideGlobalLoading();
                if(this.$refs.listTable) {
                    if (!(isSort || isRefresh)) {
                        this.$refs.listTable.clearSort();
                    }
    
                    if(!isRefresh) {
                        this.$refs.listTable.clearFilter();
                    }
                }

                if (data.success) {
                    let list = data.data.list;
                    this.isloading = true;
                    
                    list.forEach(item => {
                        this.headers.forEach(headerItem => {
                            if (headerItem.parse) {
                                item[headerItem.key] = headerItem.parse.data[item[headerItem.parse.key]];
                            }
                        })
                    })
                    this.list = list;
                    this.totalCount = data.data.total;
                    this.isFirst = false;


                    //更新分页器
                    this.refreshPager = true;
                    setTimeout(() => {
                        this.isloading = false;
                        this.refreshPager = false;
                        this.$emit('search', list)
                    }, 100)
                } else {
                    this.$message.warn(data.message)
                }
            })
        },
        tableItemEdit(data) {
            console.log(data)
            this.$emit('edit', data)
        },
        tableScroll() {
            this.$emit('scroll')
        },
        handlerCallNumber(data) {
            this.$emit('call', data)
        },
        handlerSettingSort(data) {
            this.tempSettingList.forEach((item, index) => {
                item.sort = index
            })
        }
    }
})