$(function () {
    // $('.sku-unit-select').chosen({
    //     search_contains: true,
    //     no_results_text: "没有找到结果！"
    // });

    if ($('#isNeedReport').val() == 0){
        $('#isAuthorizedItem').hide();
        $('#authorizationItem').hide();
    }

    if ($('#isAuthorized').val() == 0){
        $('#authorizationItem').hide();
    }




    $("#effectiveDays").keyup(function () {
        var effectiveDays = $("#effectiveDays").val();
        //  if(){

        //  }
        $.getJSON("/goods/vgoods/static/generateWarnDays.do?day=" + effectiveDays,
            function (d) {
                if (d.code == 0) {
                    console.log(d.data)
                    $("#nearTermWarnDays").val(d.data.nearTermWarnDays);
                    $("#overNearTermWarnDays").val(d.data.overNearTermWarnDays);
                    $("#nearTermWarnDaysSpan").html(d.data.nearTermWarnDays+'天');
                    $("#overNearTermWarnDaysSpan").html(d.data.overNearTermWarnDays+'天');
                }else{
                  //  layer.alert("计算效期日期异常")
                }
            })
    })



    $("input[name=effectiveDays]").bind('input propertychange', function () {
        var days = parseInt($("input[name=effectiveDays]").val());
        if (days >= 1095) {
            $("input[name=nearTermWarnDays]").val(365);
            $("input[name=overNearTermWarnDays]").val(120);
        } else if (days < 1095 && days >= 365) {
            $("input[name=nearTermWarnDays]").val(180);
            $("input[name=overNearTermWarnDays]").val(90);
        } else if (days < 365 && days >= 180) {
            $("input[name=nearTermWarnDays]").val(120);
            $("input[name=overNearTermWarnDays]").val(60);
        } else if (days < 180 && days >= 90) {
            $("input[name=nearTermWarnDays]").val(60);
            $("input[name=overNearTermWarnDays]").val(30);
        } else if (days < 90 && days >= 30) {
            $("input[name=nearTermWarnDays]").val(20);
            $("input[name=overNearTermWarnDays]").val(10);
        } else if (days < 30 && days >= 20) {
            $("input[name=nearTermWarnDays]").val(10);
            $("input[name=overNearTermWarnDays]").val(5);
        } else {
            $("input[name=nearTermWarnDays]").val(7);
            $("input[name=overNearTermWarnDays]").val(5);
        }
    });

    //定期维护
    if ($('#regularMaintainType').val() == '2' || $('#regularMaintainType').val() == '1') {
        $('#regularMaintainReasonDiv').show();
        $('#regularMaintainReason').removeAttr("disabled")
    }

    //是否启用多级包装
    if ($('input:radio[name=isEnableMultistagePackage]:checked').val() == '1') {
        $('#midPackageNumDiv').show();
        $('#boxPackageNumDiv').show();
    }
    ;
    //是否套件
    if ($('input:radio[name=isKit]:checked').val() == '1') {
        $('#kitDescDiv').show();
        $('#isSameSnCodeDiv').show();
    }
    ;
    //是否套件
    if ($('input:radio[name=isEnableValidityPeriod]:checked').val() == '1') {
        $('#isEnableValidityPeriodDiv').show();
    }
    ;
    //是否允许退货
    if ($('input:radio[name=returnGoodsConditions]:checked').val() == '1') {
        $('#freightIntroductions').show();
    }
    ;





    var resetShouquanNames = function () {
            $('.J-shouquan-item').each(function(i){
                $(this).find('.J-shouquan-input-1').attr('name', 'skuAuthorizationDtos[' + i + '].regionsStr');
                $(this).find('.J-shouquan-input-2').attr('name', 'skuAuthorizationDtos[' + i + '].snowFlakeId');
                $(this).find('.J-shouquan-input-3').attr('name', 'skuAuthorizationDtos[' + i + '].terminalTypesStr');
            });

        if($('.J-shouquan-item').length > 1){
            $('.J-shouquan-item .J-sort-del').show();
        }else{
            $('.J-shouquan-item .J-sort-del').hide();
        }
    };

    resetShouquanNames();

    //筛选多选
    var getMultiData = function ($item) {
        var data = [];
        $item.siblings('select').find('option').each(function () {
            data.push({
                label: $.trim($(this).html()),
                value: $(this).val()
            })
        });
        return data;
    };

    $('.J-muiti-select').each(function () {
        var data = getMultiData($(this));

        $(this).siblings('select,.select').remove();

        //初始化品牌信息
        new SuggestSelect({
            placeholder: '请选择',
            wrap: $(this),
            data: data,
            multi: true,
            multiAll: true,
            input: $(this).siblings('.J-value'),
        })
    });

    $('.authorization_add').click(function () {
        $(this).parent().before($('.J-shouquan-tmpl').html());

        $('.J-shouquan-item').last().find('.J-muiti-select').each(function () {
            var data = getMultiData($(this));

            $(this).siblings('select,.select').remove();

            //初始化品牌信息
            new SuggestSelect({
                placeholder: '请选择',
                wrap: $(this),
                data: data,
                multi: true,
                multiAll: true,
                input: $(this).siblings('.J-value'),
            })
        });

        resetShouquanNames();
    });

    $(document).on('click', '.authorization-del', function(){
        $(this).parent().remove();
        resetShouquanNames();
    })

    //校验
    $.validator.addMethod('decimal2', function (value, element, params) {
        return !value || /^[0-9]+(\.[0-9]{1,2})?$/.test(value);
    }, '数字保留小数点后两位');

    $.validator.addMethod('decimal3', function (value, element, params) {
        return !value || /^[0-9]+(\.[0-9]{1,3})?$/.test(value);
    }, '数字保留小数点后三位');

    $.validator.addMethod('decimal1', function (value, element, params) {
        return !value || /^[0-9]+(\.[0-9]{1})?$/.test(value);
    }, '数字保留小数点后一位');

    $.validator.addMethod('number', function (value, element, params) {
        return !value || /^\d+(\.\d+)?$/.test(value);
    }, '必须是数字');

    $.validator.addMethod('boxMidRule', function (value, element, params) {
        //debugger;
        var boxPackageNum = $("input[name=boxPackageNum]").val();
        var midPackageNum = $("input[name=midPackageNum]").val();
        if ('' == boxPackageNum || '' == midPackageNum) {
            return true;
        }
        if (parseInt(midPackageNum) > parseInt(boxPackageNum)) {
            return false;
        }
        return true;
    }, '箱包装数量应该≥中包装数量');

    $.validator.addMethod('lwhBt', function (value, element, params) {
        //debugger;
        var l = $("input[name=packageLength]").val();
        var w = $("input[name=packageWidth]").val();
        var h = $("input[name=packageHeight]").val();
        if ('' == l || '' == w || '' == h) {
            return false;
        }
        return true;
    }, '请填写箱包装体积');


    $.validator.addMethod('needOne', function (value, element, params) {
        var hasNameOrValue = false;

        var flag = false;
        $('.J-tech-params .J-sort-item').each(function () {
            var $name = $(this).find('.J-sort-name');
            var $value = $(this).find('.J-sort-value');

            if($.trim($name.val()) || $.trim($value.val())){
                hasNameOrValue = true;
            }

            if ($.trim($name.val()) && $.trim($value.val())) {
                flag = true;
            }
        })

        //如果必填检验关闭，且输入值为空，不进行校验
        if(!hasNameOrValue && validatorRequiredConditionDisable(this, element)){
            return true
        }

        return flag;
    }, '请填写技术参数');


    $.validator.addMethod('needOneConf', function (value, element, params) {
        var hasNameOrValue = false;

        var flag = true;
        $('.J-conf-params .J-conf-item').each(function () {
            var $name = $(this).find('.J-conf-name');
            var $value = $(this).find('.J-conf-value');

            if($.trim($name.val()) || $.trim($value.val())){
                hasNameOrValue = true;
            }

            if ((!$.trim($name.val()) || !$.trim($value.val()))) {
                if($.trim($name.val()) || $.trim($value.val())){
                    flag = false;
                }
            }
        })

        if(hasNameOrValue === false){
            return true
        }

        return flag;
    }, '请填写配置清单');


    $.validator.addMethod('checkAuth', function (value, element, params) {
        return checkSkuAuthorization();
    }, '');

    var canSubmit = true;

    $('.J-form').validate({
        errorWrap: true,
        rules: {
            goodsLevelNo:{
                required: true,
            },
            goodsPositionNo:{
                required: true,
            },
            goodsBarcode: {
                required: true,
                digits: true,
                maxlength: 255
            },
            isNeedTestReprot: {
                required: true
            },
            isKit: {
                required: true
            },
            kitDesc: {
                maxlength: 128,
                required: function () {
                    return $('input:radio[name=isKit]:checked').length == 1 && $('input:radio[name=isKit]:checked').val() == '1';
                }
            },
            isSameSnCode: {
                required: function () {
                    return $('input:radio[name=isKit]:checked').length == 1 && $('input:radio[name=isKit]:checked').val() == '1';
                }
            },
            isFactorySnCode: {
                required: true
            },
            isManageVedengCode: {
                required: true
            },
            isEnableFactoryBatchnum: {
                required: true
            },
            isInstallable: {
                required: true
            },
            isEnableMultistagePackage: {
                required: true
            },
            midPackageNum: {
                //boxMidRule:true,
                digits: true,
                maxlength: 11
            },
            boxPackageNum: {
                boxMidRule: true,
                maxlength: 11,
                digits: true,
                required: function () {
                    return $('input:radio[name=isEnableMultistagePackage]:checked').length == 1 && $('input:radio[name=isEnableMultistagePackage]:checked').val() == '1';
                }
            },
            isEnableValidityPeriod: {
                required: true
            },
            effectiveDays: {
                required: function () {
                    return $('input:radio[name=isEnableValidityPeriod]:checked').length == 1 && $('input:radio[name=isEnableValidityPeriod]:checked').val() == '1';
                },
                maxlength: 10,
                digits: true
            },
            nearTermWarnDays: {
                required: function () {
                    return $('input:radio[name=isEnableValidityPeriod]:checked').length == 1 && $('input:radio[name=isEnableValidityPeriod]:checked').val() == '1';
                }
            },
            overNearTermWarnDays: {
                required: function () {
                    return $('input:radio[name=isEnableValidityPeriod]:checked').length == 1 && $('input:radio[name=isEnableValidityPeriod]:checked').val() == '1';
                }
            },
            model: {
                required: function () {
                    return $('.J-sku-type').val() == '1';
                },
            },
            spec: {
                required: function () {
                    return $('.J-sku-type').val() != '1';
                },
            },
            skuName: {
                required: true
            },
            materialCode: {
                required: true,
                maxlength: 50
            },
            supplyModel: {
                required: true,
                maxlength: 50
            },
            paramsValid: {
                required: false,
                needOne: true
            },
            configurationValid: {
                required: false,
                needOneConf: true
            },
            configurationName: {
                maxlength: 16
            },
            configurationQuantity: {
                digits: true
            },
            packageLength: {
                required: true,
                decimal2: true,
                maxlength: 11
            },
            packageWidth: {
                required: true,
                decimal2: true,
                maxlength: 11
            },
            packageHeight: {
                required: true,
                decimal2: true,
                maxlength: 11
            },
            grossWeight: {
                required: true,
                decimal3: true,
                maxlength: 11
            },
            goodsLength: {
                required: true,
                decimal2: true,
                maxlength: 11
            },
            goodsWidth: {
                required: true,
                decimal2: true,
                maxlength: 11
            },
            goodsHeight: {
                required: true,
                decimal2: true,
                maxlength: 11
            },
            baseUnitId: {
                required: true,
                maxlength: 100
            },
            minUnitId: {
                required: true,
                maxlength: 100
            },
            packingList: {
                required: true,
                maxlength: 200
            },
            unitId: {
                required: true
            },
            changeNum: {
                required: true,
                digits: true,
                maxlength: 16
            },
            minOrder: {
                required: true,
                digits: true,
                min: 1,
                max: 9999,
                maxlength: 20
            },
            storageConditionTemperature: {
                required: true
            },
            storageConditionTemperatureLowerValue: {
                required: true,
                range: [-100,100],
                checkTem: true,
            },
            storageConditionTemperatureUpperValue: {
                required: true,
                range:[-100,100],
                checkTem: true,
            },
            storageConditionHumidityLowerValue: {
                required: true,
                digits: true,
                range: [0,100],
                checkHum: true,
            },
            storageConditionHumidityUpperValue: {
                required: true,
                digits: true,
                range: [0,100],
                checkHum: true
            },
            storageConditionOthersArray:{
                required: true
            },
            isBadGoods:{
              required: true
            },
            isNeedReport: {
                required: true,
                checkAuth: true
            },
            wikiHref:{
                required: true
            },
            regularMaintainType: {
                required: true
            },
            taxCategoryNo: {
                required: true
            }
        },
        messages: {
            goodsBarcode: {
                required: '请输入商品条形码（69码)',
                digits: '请输入整数'
            },
            isNeedTestReprot: {
                required: '请选择是否必须检测报告'
            },
            isKit: {
                required: '请选择是否套套件'
            },
            kitDesc: {
                required: '请输入有哪几个套件'
            },
            isSameSnCode: {
                required: '请选择套件中子件的SN码是否必须一致'
            },
            isFactorySnCode: {
                required: '请选择是否厂家赋SN码'
            },
            isManageVedengCode: {
                required: '请选择是否管理贝登追溯码'
            },
            isEnableFactoryBatchnum: {
                required: '请选择是否启用厂家批号'
            },
            isInstallable: {
                required: '请选择产品是否可安装'
            },
            isEnableMultistagePackage: {
                required: '请选择是否启用多级包装'
            },
            isEnableValidityPeriod: {
                required: '请选择是否启用效期'
            },
            midPackageNum: {
                digits: '请输入整数'
            },
            boxPackageNum: {
                required: '请填写箱包装数量',
                digits: '请输入整数'
            },
            packageLength: {
                required: "请输入长"
            },
            packageWidth: {
                required: "请输入宽"
            },
            packageHeight: {
                required: "请输入高"
            },
            grossWeight: {
                required: '请输入毛重',
            },
            packingList:{
                required: '请输入包装清单',
            },
            effectiveDays: {
                required: '请填写有效期',
                digits: '请输入整数'
            },
            model: {
                required: '请填写制造商型号'
            },
            spec: {
                required: '请填写规格'
            },
            skuName: {
                required: '请填写商品名称'
            },
            baseUnitId: {
                required: '请选择SKU商品单位'
            },
            minUnitId: {
                required: '请选择SKU商品单位'
            },
            unitId: {
                required: '请选择商品最小单位'
            },
            changeNum: {
                required: '请填写内含最小商品数量',
                digits: '请输入整数'
            },
            storageConditionTemperature: {
                required: '请选择存储条件(温度)'
            },
            storageConditionTemperatureLowerValue: {
                required: '请填写温度范围',
                range: '请正确填写，温度范围-100-100',
            },
            storageConditionTemperatureUpperValue: {
                required: '请填写温度范围',
                range: '请正确填写，温度范围-100-100',
            },
            storageConditionHumidityLowerValue:{
                required: '请填写湿度范围',
                digits: '请输入正确填写，湿度范围0-100（正整数）',
                range: '请输入正确填写，湿度范围0-100（正整数）',
            },
            storageConditionHumidityUpperValue:{
                required: '请填写湿度范围',
                digits: '请输入正确填写，湿度范围0-100（正整数）',
                range: '请输入正确填写，湿度范围0-100（正整数）',
            },
            storageConditionOthersArray:{
                required: '请选择存储条件其他',
            },
            minOrder: {
                required: '请填写最小起订量',
                digits: '请输入整数',
                min: '最小起订量为正整数',
                max: '最小起订量不得超过9999',
                maxlength: '最小起订量不得超过9999'
            },
            isBadGoods:{
                required: '请选择是否异形品'
            },
            materialCode:{
                required: '请输入物料编号'
            },
            supplyModel:{
                required: '请输入供应商型号'
            },
            isNeedReport: {
                required: '请选择是否需要报备'
            },
            wikiHref:{
                required: '请输入Wiki链接'
            },
            regularMaintainType: {
                required: '请选择定期维护类型'
            },
            configurationQuantity: {
                digits: '配置数量必填正整数'
            },
            taxCategoryNo: {
                required: '请选择税收分类编码'
            }
        },
        groups: {
            storageConditionHumidity: "storageConditionHumidityLowerValue storageConditionHumidityUpperValue",
            storageConditionTemperature: "storageConditionTemperatureLowerValue  storageConditionTemperatureUpperValue",
        },
        submitHandler: function (form) {
            if (canSubmit) {
                canSubmit = false;
                window.localStorage.setItem('addSkuSuccess', 'true');
                $(".defaultZero").each(function () {
                    if ($(this).val() == '') {
                        $(this).val(0);
                    }
                })
                // if (checkMinOrder() && checkSkuAuthorization()) {
                    form.submit();
                // } else {
                    canSubmit = true;
                // }
            }
        },
        invalidHandler: function(form, validator) {
            //校验不通过时，展开折叠的面板
            var errorMap = validator.errorMap;

            $.map(errorMap, function (value, key) {
                var targetNode =$("[name='"+ key +"']");
                while (targetNode.length > 0) {
                    if(targetNode.hasClass('form-block')){
                        break
                    }
                    targetNode = targetNode.parent();
                }

                var toggle = targetNode.find('.J-toggle-show');
                if (toggle.length > 0 && toggle.find('.J-less').css('display')==='none') {
                   toggle.trigger('click');
                }

            });

            return false;
        }
    });

    window.localStorage.setItem('addSkuSuccess', '')

    $('.J-tech-params').on('blur', '.J-sort-name,.J-sort-value', function () {
        var $label = $('label[for=paramsValid]');

        if ($label.length && $label.css('display') !== 'none') {
            $('[name=paramsValid]').valid();
        }
    })

    // //选择初始化
   //  Select.use('select');
    // //debugger;
    //  $('#unitHidden').find('.select').hide();
    //   $('#minUnitHidden').find('.select').hide();
    //默认带入商品名
    $('.J-model').change(function () {
        if (!$.trim($('.J-prod-name').val())) {
            $('.J-prod-name').val($.trim($('.J-spu-name').data('html')) + $(this).val());
        }
    })

    //上传
    var timeout = null;
    $('.J-upload').each(function (i) {
        var _this = this;
        new Upload({
            limit: 10,
            dragable: true,
            url: GLOBAL.IMGUPLOADURL,
            wrapper: $(this),
            type: 'file',
            uploadName: 'upload' + i,
            list: JSON.parse($(this).siblings('.J-upload-data').val() || '[]'),
            filters: {
                mime_types: [
                    {title: "Image files", extensions: "jpg,png,jpeg,bmp,pdf"}
                ],
                max_file_size: '5MB'
            },
            onchange: function () {
                $(_this).find('.J-upload-item').each(function (idx, elem) {
                    var data = $(this).data('item');
                    $(this).find('.J-item-name').remove();
                    var targetElement=$(_this).attr("type")+"["+idx+"]";
                    $(this).append('<input type="hidden" class="J-item-name" name="'+targetElement+'.relativePath"  value="' + data.filePath + '" >');
                    $(this).append('<input type="hidden" class="J-item-name" name="'+targetElement+'.displayName"  value="' + data.fileName + '" >');
                })
                if (i == 0) {
                    $(_this).find('[name^=upload]').valid();
                }
            },
            onError: function (error) {
                var errorMsg = {
                    TYPE: '上传图片格式为：JPG、PNG、JEPG、BMP、PDF格式',
                    SIZE: '文件大小不超过5M'
                }
                if (error) {
                    var $error = $(_this).siblings('.J-upload-error');
                    $error.show().find('label').html(errorMsg[error]).show();
                    timeout && clearTimeout(timeout);
                    timeout = setTimeout(function () {
                        $error.hide();
                    }, 3000)
                }
            }
        });
    })


    //排序序号
    var setNum = function ($wrap) {
        $('.J-sort-num', $wrap).each(function (i) {
            $(this).val(i + 1);
        })
    };

    $('.J-sort-wrap').each(function () {
        setNum($(this));
    })

    var sortInput = function (params) {
        var $wrap = $(params.wrap);

        var flag = true;
        var checkVal = function (el) {
            var $this = $(el);

            if (flag) {
                setTimeout(function () {
                    var val = $.trim($this.val());
                    if (val && !(/^\d*$/.test(val))) {
                        val = val.replace(/[^\d]+/g, '');
                        $this.val(val);
                    }
                }, 10)
            }
        };

        $wrap.on('compositionstart', '.J-sort-num', function () {
            flag = false;
        })

        $wrap.on('compositionend', '.J-sort-num', function () {
            flag = true;
        })

        $wrap.on('keyup', '.J-sort-num', function () {
            checkVal(this);
        });

        $wrap.on('change', '.J-sort-num', function () {
            checkVal(this);
            var _this = this;

            setTimeout(function () {
                var $items = $('.J-sort-num', $wrap);
                var len = $items.length;
                var val = parseInt($(_this).val());

                if (val === 0 || val) {
                    val = val > len ? len : (val < 1 ? 1 : val);

                    var $parent = $(_this).parents('.J-sort-item:first');
                    var index = $parent.index();

                    setTimeout(function () {
                        var $sort = $parent.clone(true);

                        if (val <= index) {
                            $('.J-sort-item', $wrap).eq(val - 1).before($sort);
                        } else {
                            $('.J-sort-item', $wrap).eq(val - 1).after($sort);
                        }

                        $parent.remove();
                        setNum($wrap);
                    })
                } else {
                    setNum($wrap);
                }

            }, 110);
        });

    };

    $('.J-sort-wrap').each(function () {
        sortInput({
            wrap: $(this)
        })
    })

    //检查添加或者删除是否显示
    var checkItemNum = function ($wrap) {
        var len = $('.J-sort-item', $wrap).length;
        var confLen = $('.J-conf-item', $wrap).length;
        var min = 1;
        var max = 30;

        if (len >= max) {
            $('.J-sort-add-option', $wrap).hide();
        } else {
            $('.J-sort-add-option', $wrap).show();
        }

        if (len <= min) {
            $('.J-sort-del', $wrap).hide();
        } else {
            $('.J-sort-del', $wrap).show();
        }

        if (confLen < min) {
            $('.J-conf-del', $wrap).hide();
        } else {
            $('.J-conf-del', $wrap).show();
        }
    };

    //获取name
    var paramsNames = [];

    $('.J-sort-wrap').each(function () {
        var $name = $(this).find('.J-sort-name').eq(0);
        var $value = $(this).find('.J-sort-value').eq(0);

        paramsNames.push({
            itemName: $name.attr('name'),
            itemValue: $value.attr('name')
        });

    })

     //sku类型为器械时才需要维护参数信息，避免js堆栈异常
     if($('#ex1').length){
        new Sortable(ex1, {
            animation: 150,
            ghostClass: "sortable-drag",  // drop placeholder的css类名
            chosenClass: "sortable-drag",
            onEnd: function (evt) {
                //debugger;
                var $wrap = $(evt.item).parents('.J-sort-wrap:first');

                //$(this).parents('.J-sort-item:first').remove();
                checkItemNum($wrap);
                setNum($wrap);
                console.log("拖拽结束")
            },
        });
    }


    //参数添加
    var paramsTmpl = template($('.J-sort-tmpl').html());

     // 配置信息添加
    var paramsTmpl2 = template($('.J-conf-tmpl').html());

    $('.J-conf-wrap').each(function () {
        setNum($(this));
    })

    var configurationNames = [];

    $('.J-conf-wrap').each(function () {
        var $name = $(this).find('.J-conf-name').eq(0);
        var $value = $(this).find('.J-conf-value').eq(0);

        configurationNames.push({
            confName: $name.attr('name'),
            confValue: $value.attr('name')
        });

    })

    // 配置信息添加
    var addParamsItem2 = function ($wrap, params) {
        var defaults = {
            name: '',
            value: '',
            confName: '',
            confValue: ''
        };
        var index = $wrap.data('index') || 0;
        var confName = configurationNames[index];

        var data = $.extend({}, defaults, params, confName);
        var $list = $('#ex2', $wrap);

        $list.append(paramsTmpl2(data));

        setNum($wrap);
        checkItemNum($wrap);
    };

    // 新增配置信息
    $('.J-sort-add-conf').click(function () {
        var $wrap = $(this).parents('.J-conf-wrap:first');
        addParamsItem2($wrap)
    });

    var addParamsItem = function ($wrap, params) {
        var defaults = {
            name: '',
            value: '',
            itemName: '',
            itemValue: ''
        };
        var index = $wrap.data('index') || 0;
        var paramsName = paramsNames[index];

        var data = $.extend({}, defaults, params, paramsName);
        var $list = $('#ex1', $wrap);

        $list.append(paramsTmpl(data));

        setNum($wrap);
        checkItemNum($wrap);
    };

    $('.J-sort-add').click(function () {
        var $wrap = $(this).parents('.J-sort-wrap:first');
        addParamsItem($wrap)
    });

    $("#isNS").click(function(){
        if (this.value == 1) {
            $("#isManage").attr('value', 1);
        }
        layui.use(['form'], function() {
            var form = layui.form;
        form.render();})
    });

    $("#isNotNS").click(function(){
        if (this.value == 0) {
            $("#isManage").attr('value', 0);
        }
        layui.use(['form'], function() {
            var form = layui.form;
            form.render();})
    });



    //参数导入
    $('.J-sort-import').click(function () {
        var $wrap = $(this).parents('.J-sort-wrap:first');
        new artDialog({
            title: '复制文本导入参数',
            content: $('.J-import-tmpl').html(),
            init: function () { },
            width: 680,
            button: [{
                name: '导入',
                highlight: true,
                callback: function () {
                    var cnt = $.trim($('.J-import-cnt').val());

                    if (cnt) {
                        var params = cnt.split(/[;；]/);

                        $.each(params, function (i, item) {
                            if ($.trim(item)) {
                                var itemArr = item.split(/[:：]/);
                                var needInput = true;

                                $wrap.find('.J-sort-item').each(function () {
                                    if (needInput) {
                                        var $name = $(this).find('.J-sort-name');
                                        var $value = $(this).find('.J-sort-value');

                                        if (!($.trim($name.val())) && !($.trim($value.val()))) {
                                            $name.val(itemArr[0]);
                                            $value.val(itemArr[1] || '');
                                            needInput = false;
                                        }
                                    }
                                })

                                if ($wrap.find('.J-sort-item').length < 30 && needInput) {
                                    addParamsItem($wrap, {
                                        name: itemArr[0],
                                        value: itemArr[1] || ''
                                    })
                                }

                                var $label = $('label[for=paramsValid]');

                                if ($label.length && $label.css('display') !== 'none') {
                                    $('[name=paramsValid]').valid();
                                }
                            }
                        })
                    }
                }
            }, {
                name: '取消'
            }],
        })
    })


    // 配置导入
    $('.J-conf-import').click(function () {
        var $wrap = $(this).parents('.J-conf-wrap:first');
        new artDialog({
            title: '复制文本导入配置',
            content: $('.J-import-tmpl-conf').html(),
            init: function () { },
            width: 680,
            button: [{
                name: '导入',
                highlight: true,
                callback: function () {
                    var cnt = $.trim($('.J-import-cnt').val());

                    if (cnt) {
                        var params = cnt.split(/[;；]/);

                        $.each(params, function (i, item) {
                            if ($.trim(item)) {
                                var itemArr = item.split(/[:：]/);
                                var needInput = true;

                                $wrap.find('.J-conf-item').each(function () {
                                    if (needInput) {
                                        var $name = $(this).find('.J-conf-name');
                                        var $value = $(this).find('.J-conf-value');

                                        if (!($.trim($name.val())) && !($.trim($value.val()))) {
                                            $name.val(itemArr[0]);
                                            $value.val(itemArr[1] || '');
                                            needInput = false;
                                        }
                                    }
                                })

                                if ($wrap.find('.J-conf-item').length < 30 && needInput) {
                                    addParamsItem2($wrap, {
                                        name: itemArr[0],
                                        value: itemArr[1] || ''
                                    })
                                }

                                var $label = $('label[for=configurationValid]');

                                if ($label.length && $label.css('display') !== 'none') {
                                    $('[name=configurationValid]').valid();
                                }
                            }
                        })
                    }
                }
            }, {
                name: '取消'
            }],
        })
    })

    // 删除配置
    $(document).on('click', '.J-conf-del', function () {
        var $wrap = $(this).parents('.J-conf-wrap:first');

        $(this).parents('.J-conf-item:first').remove();
        checkItemNum($wrap);
        setNum($wrap);
    })

    $('.J-conf-wrap').each(function () {
        checkItemNum($(this));
    })

    //删除参数
    $(document).on('click', '.J-sort-del', function () {
        var $wrap = $(this).parents('.J-sort-wrap:first');

        $(this).parents('.J-sort-item:first').remove();
        checkItemNum($wrap);
        setNum($wrap);
    })

    $('.J-sort-wrap').each(function () {
        checkItemNum($(this));
    })

    //单位同步
    // $('.J-sku-unit').change(function () {
    //     debugger
    //     if ($(this).val()) {
    //         $('.J-sku-unit-value').html('（' + $.trim($(this).find('option:selected').html()) + '）');
    //     } else {
    //         $('.J-sku-unit-value').html('');
    //     }
    // })
    //
    // $('.J-sku-unit').trigger('change');
    //
    // $('.J-base-sku-unit').change(function () {
    //     debugger
    //     if ($(this).val()){
    //         $('.sku-unit-value').html('（' + $.trim($(this).find('option:selected').html()) + '）');
    //     } else {
    //         $('.sku-unit-value').html('');
    //     }
    // });
    //
    // $('.J-base-sku-unit').trigger('change');

    //增加关闭提示
    GLOBAL.addtip();








    loadGoodsValidationRule();



   // let spanText = $('.J-sku-unit-value').html('（' + $("#unitListsName span:eq(0)").text() + '）');

    //  sss
});
/**
 * 校验属性值
 */
function validateFuc() {
    if ($('.attr-required-icon').length > 0) {
        for (var i = 0; i < $('.attr-required-icon').length; i++) {
            $("input[name='baseAttributeValueId["+i+"]']").rules('add', {
                required:true,
                messages:{
                    required:'请选择'+$("input[name='baseAttributeValueId["+i+"]']").attr("content1")
                }
            });
        }
    }
};
/**
 * 获取验证SKU字段规则
 */
//编辑页面切换等级时，异步加载规则
function loadGoodsValidationRule() {
    var legacyPropertyNameMap =new Map;
    legacyPropertyNameMap.set('technicalParameter', 'paramsValid');


    var goodsLevelNo = parseInt($('select[name=goodsLevelNo]').val());

    if(isNaN(goodsLevelNo) || goodsLevelNo<=0) {
        return
    }

    $('span.must').each(function () {
        $(this).show();
    });

    $.ajax({
        url: page_url + '/goods/vgoods/goodsValidatedRule.do',
        data: {
            targetValidType: 2,
            goodsLevelNo: goodsLevelNo
        },
        dataType: 'json',
        success: function (res) {
            if(res !== null && res.code===0){
                //    debugger
                //动态生成页面校验规则
                var ruleInJson = res.data;

                var basicItems = ruleInJson.basicItems;

                next:
                    for (var i=0; i < basicItems.length; i++) {
                        var elem = basicItems[i];
                        var required = elem.required;

                        //商品属性
                        if (elem.propertyName === 'baseAttributeValueId'){
                            if (!required) {
                                $('.attr-required-icon').hide();
                            }
                            if (required && $('.attr-required-icon').length > 0) {
                                validateFuc();
                            }
                        }

                        var propertyName = elem.propertyName;
                        if (propertyName == null || propertyName === '') {
                            continue
                        }
                        if (propertyName === 'technicalParameter') {
                            if ($('[name=paramsValid]').length == 0) {
                                //耗材没有技术参数
                                continue
                            }
                            propertyName = legacyPropertyNameMap.get(propertyName);
                        }

                        var target = $("[name=" + propertyName + "]");
                        if (target.length === 0) {
                            console.log("not match property name:"+ propertyName);
                            continue
                        }

                        if (!required) {
                            var removeFlag = target.rules('remove', 'required');
                            if (removeFlag.require) {
                                continue next;
                            }

                            var parentNode = target.parent();
                            while (parentNode.length > 0) {
                                if (parentNode.hasClass('form-fields')) {
                                    break
                                }
                                parentNode=parentNode.parent()
                            }

                            if (!parentNode.length) {
                                continue
                            }

                            var targetSpan = parentNode.prev().find('.must')
                            if( targetSpan.length > 0) {
                                targetSpan.hide();
                            }

                        } else {
                            if (propertyName === 'paramsValid') {
                                //技术参数比较特殊先这样处理吧
                                debugger
                                target.rules('add',{
                                    required : false,
                                    messages : {
                                        required: elem.message
                                    }
                                });
                            } else {
                                target.rules('add',{
                                    required : true,
                                    messages : {
                                        required: elem.message
                                    }
                                });
                            }
                        }
                    }

            } else {
                artDialog.alert('加载商品校验规则失败！');
            }
        },
        error:function(res){
            artDialog.alert("系统异常,请联系管理员", '系统错误');
            console.log("error message:" + res)
        }
    });
}


/**
 * 校验最小起订量是否合法
 * 1.最大为9999;
 * 2.必须为正整数;
 * @returns {boolean}
 */
// function checkMinOrder() {
//     debugger
//     var minOrder = $('#minOrder').val();
//     if (minOrder == undefined){
//         return true;
//     }
//     if (parseFloat(minOrder) > 9999){
//         $('#minOrderMsg').html('最小起订量不得超过9999');
//         return false;
//     } else {
//         $('#minOrderMsg').html('');
//     }
//     var type="^[0-9]*[1-9][0-9]*$";
//     var r=new RegExp(type);
//     var flag=r.test(minOrder);
//     if(!flag){
//         $('#minOrderMsg').html('最小起订量为正整数');
//         return false;
//     } else {
//         $('#minOrderMsg').html('');
//         return true;
//     }
// }

/**
 * 检查sku报备信息
 * 1.区域和类型必须匹配一边不为空；
 * 2.不同的item之间多个区域不能重复；
 * 3.不允许存在空的item;
 */
function checkSkuAuthorization() {
    var isNeedReport = $("input[name='isNeedReport']:checked").val();
    if (isNeedReport != 0 && isNeedReport != 1){
        $('#isNeedReportMsg').html('是否需报备不能为空');
        return false;
    } else if (isNeedReport == 0){
        $('#isNeedReportMsg').html('');
        return true;
    }else if (isNeedReport == 1) {
        $('#isNeedReportMsg').html('');
        var isAuthorized = $("input[name='isAuthorized']:checked").val();
        if (isAuthorized != 0 && isAuthorized != 1){
            $('#isAuthorizedMsg').html('是否获得授权不能为空');
            return false;
        } else if (isAuthorized == 0){
            $('#isAuthorizedMsg').html('');
            return true;
        } else if (isAuthorized == 1){
            $('#isAuthorizedMsg').html('');

            var checkRequired = 1;
            var regionsFlag = [];

            var regionsStrs = $('.J-shouquan-input-1');
            if (regionsStrs.length == 0){
                checkRequired= 0;
            }
            for (var index = 0; index < regionsStrs.length; index++) {
                var regionStrr = regionsStrs[index].value;
                var regionStrs = regionStrr.split('@');
                regionsFlag.push.apply(regionsFlag,regionStrs);

                if (regionStrr == null || regionStrr == undefined || regionStrr == ''){
                    checkRequired = 0;
                }
            }
            $('#skuAuthorizationMsg').html('');

            var terminalTypeStr = $('.J-shouquan-input-3');
            if (terminalTypeStr.length == 0){
                checkRequired= 0;
            }
            for (var index = 0; index < terminalTypeStr.length; index++) {
                var terminalTypeStrr = terminalTypeStr[index].value;
                if (terminalTypeStrr == null || terminalTypeStrr == undefined || terminalTypeStrr == ''){
                    checkRequired = 0;
                }
            }
            if (checkRequired == 0){
                $('#skuAuthorizationMsg').html('授权范围不允许为空');
                return false;
            } else {
                $('#skuAuthorizationMsg').html('');
            }

            var isRegionsRepeat = isRepeat(regionsFlag);
            if (isRegionsRepeat) {
                $('#skuAuthorizationMsg').html('授权区域不能重复');
                return false;
            } else {
                $('#skuAuthorizationMsg').html('');
            }
            return true;
        }
    }
}

/**
 * 检测数组元素是否重复
 * @param arr
 * @returns {boolean}
 */
function isRepeat(arr) {
    var hash = {};
    for(var i in arr) {
        if(hash[arr[i]]) {
            return true;
        }
        hash[arr[i]] = true;
    }
    return false;
}

/**
 * SKU报备信息的动态展示
 * isNeedReport-是否需要报备
 * isAuthorized-是否获取授权
 * authorizationItem-授权范围
 *
 */
function changeauthorization() {
    var isNeedReport = $("input[name='isNeedReport']:checked").val();
    if (isNeedReport == 0){
        $('#authorizationItem').hide();//授权范围隐藏
        $('#isAuthorizedItem').hide();//是否获取授权
        return;
    } else {
        $('#isAuthorizedItem').show();
    }
    var isAuthorized = $("input[name='isAuthorized']:checked").val();
    if (isAuthorized == 0){
        $('#authorizationItem').hide();
    } else {
        $('#authorizationItem').show();
    }
}

// /**
//  * 校验属性值
//  */
// function   validateFuc(){
//     if ($('.attr-required-icon').length > 0) {
//         for (var i = 0; i < $('.attr-required-icon').length; i++) {
//             $("input[name='baseAttributeValueId["+i+"]']").rules('add', {
//                 required:true,
//                 messages:{
//                     required:'请选择'+$("input[name='baseAttributeValueId["+i+"]']").attr("content1")
//                 }
//             });
//         }
//     }
//
//     // $("[flag]").each(function (i,e) {
//     //     debugger
//     //     $("input[name='baseAttributeValueId["+i+"]']").rules('add', {
//     //         required:true,
//     //         messages:{
//     //             required:'请选择'+$("input[name='baseAttributeValueId["+i+"]']").attr("content1")
//     //         }
//     //     });
//     // });
// };

/**
 * 关闭效期提示
 */
function closeExpireDateTips(){
    $('.expire-date-tips').css('display','none')
}

//展开收起
$('.J-toggle-show').click(function () {
    var $optionalWrap=$(this).parent().find('.J-optional-more')
    var isShow = $optionalWrap.hasClass('show');
    var $less = $(this).find('.J-less');
    var $more = $(this).find('.J-more');

    if (isShow) {
        $optionalWrap.removeClass('show').slideUp(200);
        $less.hide();
        $more.show();
    } else {
        $optionalWrap.addClass('show').slideDown(200);
        $less.show();
        $more.hide();
    }
});


//关闭当前页面
$('.close-spu-edit').click(function () {
    if(parentWindow.find('.title .active .glyphicon').length > 0){
        var closeTab = parentWindow.find('.title .active .glyphicon').get(0);
        $(closeTab).trigger('click');
    }
});

layui.use('form', function(){
    var form = layui.form;

    form.render('select');

    form.on('select(lay-baseUnitId)', function(data){

        if (data.value){
            $('.sku-unit-value').html('（' + data.othis.find(".layui-this").html() + '）');
        } else {
            $('.sku-unit-value').html('');
        }
    });
    form.on('select(lay-unitId)', function(data){

        if (data.value){
            $('.J-sku-unit-value').html('（' +data.othis.find(".layui-this").html() + '）');
        } else {
            $('.J-sku-unit-value').html('');
        }
    });

    form.on('radio(isEnableValidityPeriod)', function(data){
        console.log(data.elem); //得到radio原始DOM对象
        console.log(data.value); //被点击的radio的value值
        if (data.value == '1') {
            $('#isEnableValidityPeriodDiv').show();
        } else {
            $('#isEnableValidityPeriodDiv').hide();
            if(!($("#effectiveDays")!=null && $("#effectiveDays") >0)){
                $("#effectiveDays").val(0);
            }
            if(!($("#nearTermWarnDays")!=null && $("#nearTermWarnDays") >0)){
                $("#nearTermWarnDays").val(0);
                $("#nearTermWarnDaysSpan").html('0天');
            }
            if(!($("#overNearTermWarnDays")!=null && $("#overNearTermWarnDays") >0)){
                $("#overNearTermWarnDays").val(0);
                $("#overNearTermWarnDaysSpan").html('0天');
            }
            // $("input[name='effectiveDays']").val('0');
            // $("input[name='nearTermWarnDays']").val('0');
            // $("input[name='overNearTermWarnDays']").val('0');
        }
    });

    //是否套件
    form.on('radio(isKit)', function(data){
        if (data.value == '1') {
            $('#isSameSnCodeDiv').show();
            $('#kitDescDiv').show();
        } else {
            $('#isSameSnCodeDiv').hide();
            $('#kitDescDiv').hide();
            $('#kitDesc').val('');

            $("input[name='isSameSnCode']").removeAttr('checked');
        }
    });
    form.on('select(goodsLevelNo)', function(data){
        loadGoodsValidationRule();
    });
    form.on('select(regularMaintainType)', function(data){

            if (data.value == '1' || data.value == '2') {
                $('#regularMaintainReasonDiv').show();
                $('#regularMaintainReason').removeAttr("disabled")
            } else {
                $('#regularMaintainReasonDiv').hide();
                $('#regularMaintainReason').attr("disabled",true)
            }

    });
  //  radio
    //是否启用多级包装
    form.on('radio(isEnableMultistagePackage)', function(data){
   // $('input[name=isEnableMultistagePackage]').change(function () {
        if (data.value == '1') {
            $('#midPackageNumDiv').show();
            $('#boxPackageNumDiv').show();
        } else {
            $('#midPackageNumDiv').hide();
            $('#boxPackageNumDiv').hide();
            $("#midPackageNum").val('0');
            $("#boxPackageNum").val('0');
        }
    });

    form.on('radio(storageConditionTemperature)', function(data){
        if(data.value==='4'&& data.elem.checked){
            $('#lessTemperature').attr('disabled', false)
            $('#greatTemperature').attr('disabled', false)
        }else {
            $('#lessTemperature').attr('disabled', true)
            $('#greatTemperature').attr('disabled', true)
        }
    });


});

//是否允许退货
$('input[name=returnGoodsConditions]').change(function () {
    if (this.value == '1') {
        $('#freightIntroductions').show();
    } else {
        $('#freightIntroductions').hide();
        $("textarea[name='freightIntroductions']").val('');
    }
});
//是否启用效期
// $('input[name=isEnableValidityPeriod]').change(function () {
//
// });