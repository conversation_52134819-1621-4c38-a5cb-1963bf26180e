<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ include file="../../common/common.jsp"%>

<div class="parts">
    <div class="title-container">
        <div class="table-title nobor">账期申请记录</div>

    </div>
    <table class="table table-bordered table-striped table-condensed table-centered">
        <thead>
        <tr>
            <th class="table-smallest">申请人</th>
            <th class="table-smallest">申请日期</th>
            <th class="table-smallest">账期类型</th>
            <th class="table-smallest">申请类型</th>
            <th class="table-smallest">账期原额度</th>
            <th class="table-smallest">账期原有效期</th>
            <th class="table-smallest">本次额度申请（元）</th>
            <th class="table-smallest">本次结算周期（天）</th>
            <th class="table-smallest">本次申请的有效期</th>
            <th class="table-smallest">审核状态</th>
            <th class="table-smallest"> 操作</th>
        </tr>
        </thead>
        <tbody>
        <c:if test="${not empty customerBillPeriodApplyList}">
            <c:forEach items="${customerBillPeriodApplyList}" var="bill">
                <tr>
                    <td>
                        <c:forEach var="creatorList" items="${creatorName}" varStatus="creatorNum">
                            <c:if test="${creatorList.userId eq bill.creator}">${creatorList.username}</c:if>
                        </c:forEach>
                    </td>
                    <td><date:date value="${bill.addTime}" /></td>
                    <td>
                        <c:choose>
                            <c:when test="${empty bill.billPeriodType}">-</c:when>
                            <c:when test="${bill.billPeriodType == 1}">正式账期</c:when>
                            <c:when test="${bill.billPeriodType == 2}">临时账期</c:when>
                            <c:when test="${bill.billPeriodType == 3}">订单账期</c:when>
                        </c:choose>
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${empty bill.operateType}">-</c:when>
                            <c:when test="${bill.operateType == 1}">新增</c:when>
                            <c:when test="${bill.operateType == 2}">调整</c:when>
                        </c:choose>
                    </td>
                    <td><c:if test="${empty bill.beforeApplyAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${bill.beforeApplyAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:choose>
                        <c:when test="${empty bill.beforeBillPeriodEnd || bill.billPeriodType==3}">
                            -
                        </c:when>
                        <c:when test="${not empty bill.beforeBillPeriodEnd && bill.billPeriodType == 3}">-</c:when>
                        <c:otherwise>
                            <date:date value="${bill.beforeBillPeriodEnd}" format="yyyy-MM-dd" />
                        </c:otherwise>
                    </c:choose>
                    </td>
                    <td>${bill.applyAmount}</td>
                    <td>${bill.settlementPeriod}</td>
                    <td>
                        <c:choose>
                            <c:when test="${empty bill.billPeriodEnd}">-</c:when>
                            <c:when test="${not empty bill.billPeriodType && bill.billPeriodType == 3}">-</c:when>
                            <c:otherwise><date:date value="${bill.billPeriodEnd }" format="yyyy-MM-dd" /></c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${bill.checkStatus eq 0}">待审核</c:when>
                            <c:when test="${bill.checkStatus eq 1}">审核通过</c:when>
                            <c:when test="${bill.checkStatus eq 2}">审核不通过</c:when>
                        </c:choose>
                    </td>
                    <td>  <%--url待修改--%>
                        <a href="javascript:void(0);"
                           class="addtitle" tabtitle='{"num":"finance_accountperiod_applyAudit_${bill.billPeriodApplyId}",
										   "link":"./finance/accountperiod/getAccountPeriodApply.do?billPeriodApplyId=${bill.billPeriodApplyId}&traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&billPeriodType=${bill.billPeriodType}&traderAccountPeriodApplyId=${bill.billPeriodApplyId}&traderType=1",
										   "title":"账期申请审核"}'>查看</a>
                    </td>
                </tr>
            </c:forEach>
        </c:if>

        <c:if test="${empty customerBillPeriodApplyList }">
            <tr>
                <td colspan="11">查询无结果！</td>
            </tr>
        </c:if>
        </tbody>
    </table>

    </div>
    <tags:page page="${page}" optpage ='n'/>
<%@ include file="../../common/footer.jsp"%>