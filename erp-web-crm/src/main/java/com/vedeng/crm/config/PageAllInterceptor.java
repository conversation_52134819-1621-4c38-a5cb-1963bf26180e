package com.vedeng.crm.config;

import com.vedeng.core.trace.constant.MdcConstant;
import org.slf4j.MDC;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/9/25
 */
public class PageAllInterceptor  implements HandlerInterceptor {


    private String erpHost;
    
    private String companyConfig;

    public PageAllInterceptor(String erpHost, String companyConfig) {
        this.erpHost = erpHost;
        this.companyConfig = companyConfig;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String traceId = MDC.get(MdcConstant.TRANCE_ID);
        request.setAttribute("traceId",traceId);
        request.setAttribute("erpHost", erpHost);
        request.setAttribute("companyConfig", companyConfig);
        if(request.getRequestURI().equals("/notBoundPlatform")){
            response.sendRedirect("/crm/noPermission");
            return false;
        }
        return true;

    }

}
