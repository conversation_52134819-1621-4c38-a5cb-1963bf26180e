package com.newtask.stop;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.trader.dao.TraderCertificateMapper;
import com.vedeng.trader.model.TraderCertificate;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * @Description:  同步厂家资质文件后缀名
 * @Author:       Davis
 * @Date:         2021/7/29 9:20
 * @Version:      v1.0
 */
@Component
@JobHandler(value="syncTraderSupplierTask")
@Deprecated
public class SyncTraderSupplierTask extends AbstractJobHandler {
	
	private Logger logger = LoggerFactory.getLogger(SyncTraderSupplierTask.class);

	@Autowired
	private TraderCertificateMapper traderCertificateMapper;

	//每个线程处理的数据量
	private CountDownLatch threadsSignal;

	private static final int count = 1000;

	private static final int threadSize = 200;

	@Value("${file_service_url}")
	private String fileServiceUrl;

	private static final String FILE_URL = "http://*************:9702/";

	private static final String PATH = "/file/querySuffix";
	
	private static final String SYNC_FROM_ERP = "erp";

	//定义线程池数量为10,每个线程处理100条数据
	private static ExecutorService execPool;

	@Override
	public ReturnT<String> doExecute(String param) throws Exception {
		XxlJobLogger.log("开始同步文件后缀名----");
		logger.info("开始同步文件后缀名----");
		
		execPool = new ThreadPoolExecutor(5, 10,
				60L, TimeUnit.MILLISECONDS,
				new LinkedBlockingQueue<Runnable>(1024 * 100),
				new ThreadFactoryBuilder()
						.setNameFormat("TraderCertificate-sync-%d")
						.build(),
				new ThreadPoolExecutor.AbortPolicy());

		ReturnT<String> result = syncTraderMethod(param);
		
		XxlJobLogger.log("结束同步文件后缀名----");
		logger.info("结束q同步文件后缀名----");
		return result;
	}

	public ReturnT<String> syncTraderMethod(String param) {
		if (StringUtil.isEmpty(param)) {
			logger.info("参数不能为空");
			return ReturnT.FAIL;
		}

		String[] params = param.split(",");

		if (params.length < 2) {
			XxlJobLogger.log("同步文件参数格式不对----");
			logger.info("同步文件参数格式不对");
			return ReturnT.FAIL;
		}
		
		Long num = Long.parseLong(params[0]);

		XxlJobLogger.log("同步文件参数：{}", num);
		logger.info("同步文件参数：{}", num);
		try {
			for (int i = 0; i < num; i = i + count) {
				logger.info("同步开始序号：{}", i);
				List<TraderCertificate> traderCertificateList = traderCertificateMapper.getTraderCertificateList(0, Integer.valueOf(count));
				if (CollectionUtils.isEmpty(traderCertificateList)) {
					XxlJobLogger.log("文件集合为空----");
					logger.info("文件集合为空----");
					break;
				}
				dealData(traderCertificateList, params[1]);
			}
		} catch (Exception e) {
			logger.error("同步出现异常");
		} finally {
			execPool.shutdownNow();
		}
		XxlJobLogger.log("开始同步数据----");
		return SUCCESS;
	}

	private void dealData(List<TraderCertificate> traderCertificateList, String syncType) {
		int attachmentSize = traderCertificateList.size();
		try {
			if (attachmentSize <= threadSize) {
				threadsSignal = new CountDownLatch(1);
				execPool.submit(new SyncThread(traderCertificateList, syncType));
				threadsSignal.await(1, TimeUnit.MINUTES);
			} else {
				List<List<TraderCertificate>> fileList = createList(traderCertificateList, threadSize);
				threadsSignal = new CountDownLatch(fileList.size());
				for (List<TraderCertificate> files : fileList) {
					execPool.submit(new SyncThread(files, syncType));
				}
				threadsSignal.await(1, TimeUnit.MINUTES);
			}
		} catch (InterruptedException e) {
			logger.error(e.toString() + " 错误所在行数：" + e.getStackTrace()[0].getLineNumber());
		}
	}

	/**
	 * 数据拆分
	 * @param count
	 * @return
	 */
	public static List<List<TraderCertificate>>  createList(List<TraderCertificate> list, int count) {
		int listSize = list.size();
		int step = count;
		List<List<TraderCertificate>> listArr = new ArrayList<>();
		for (int i = 0; i < list.size(); i+=count) {
			if (i+count > listSize) {
				step = listSize - i;
			}
			List<TraderCertificate> newList = list.subList(i, i+step);
			listArr.add(newList);
		}
		return listArr;
	}

	class SyncThread implements Callable {

		private List<TraderCertificate> traderCertificateList;
		
		private String syncType;

		public SyncThread(List<TraderCertificate> list, String syncType) {
			this.traderCertificateList = list;
			this.syncType = syncType;
		}

		@Override
		public Object call() throws Exception {
			for (TraderCertificate traderCertificate : traderCertificateList) {
				if (StringUtil.isEmpty(traderCertificate.getUri())) {
					continue;
				}
				List<String> resourceIds = new ArrayList<>();
				String uri = traderCertificate.getUri();
				try {
					if (uri.indexOf("resourceId") != -1) {
						String resourceId = traderCertificate.getUri().substring(traderCertificate.getUri().indexOf("=")+1);
						if (SYNC_FROM_ERP.equals(syncType)) {
							String suffix = traderCertificateMapper.selectSuffixFromFile(resourceId);
							traderCertificate.setSuffix(suffix);
						} else {
							resourceIds.add(resourceId);
							Map<String, Object> jsonMap = new HashMap<>();
							jsonMap.put("resourceIds", resourceIds);
							String requestJson = JsonUtils.translateToJson(jsonMap);
							JSONObject resultJsonObj = NewHttpClientUtils.httpPost(fileServiceUrl + PATH, requestJson);
							if (resultJsonObj == null){
								continue;
							}
							Boolean isSuccess = (Boolean) resultJsonObj.get("success");
							if (!isSuccess){
								continue;
							}
							List<Map<String, Object>> data = resultJsonObj.getJSONArray("data");
							if (CollectionUtils.isEmpty(data)) {
								continue;
							}
							String suffix = (String) data.get(0).get("suffix");
							traderCertificate.setSuffix(suffix);
						}
						
					} else {
						//logger.info("TraderCertificate###FTP文件获取文件信息：" + uri);
						String type = uri.substring(uri.indexOf(".")+1);
						traderCertificate.setSuffix(type);
					}
					traderCertificateMapper.updateTraderCertificateSuffix(traderCertificate);
				} catch (Exception e) {
					logger.info("TraderCertificate###同步文件出错：" + uri);
					continue;
				}
				//logger.info("TraderCertificate###更新后缀成功：" + uri);
			}
			threadsSignal.countDown();
			return null;
		}
	}

}
