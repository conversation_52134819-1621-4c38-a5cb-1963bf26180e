package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.mobile.dto.SaleOrderDetailInfoDto;
import com.vedeng.erp.mobile.dto.SaleOrderListQueryDto;
import com.vedeng.erp.mobile.dto.SaleOrderListResultDto;
import com.vedeng.erp.saleorder.domain.entity.SaleorderEntity;
import com.vedeng.erp.saleorder.dto.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.saleorder.dao
 * @Date 2022/12/16 10:36
 */
@Repository("newSaleOrderMapper")
public interface SaleOrderMapper {

    /**
     * 根据skuId查询最近的十个销售单信息
     *
     * @param goodsId goodsId
     * @return List<SaleorderInfoDto>
     */
    List<SaleorderInfoDto> getSkuFairValueSaleOrderList(@Param("goodsId") Integer goodsId);

    /**
     * 更新销售单收发货状态
     * @param saleorderInfoDto
     */
    void updateSaleorderStatus(SaleorderInfoDto saleorderInfoDto);


    /**
     * 更新销售单确认单审核状态
     * @param saleorderInfoDto
     */
    int updateConfirmationFormAudit(SaleorderInfoDto saleorderInfoDto);

    /**
     * 查询沟通记录关联的销售单信息（id 单号）
     *
     * @param list 销售单id集合
     * @return id 单号
     */
    List<Map<String, Object>> getCommunicateSaleOrderInfo(@Param("list") List<Integer> list);

    /**
     * 根据客户名查询是否有生效订单
     * @param traderName
     * @return
     */
    SaleorderInfoDto selectByTraderName(@Param("traderName") String traderName);

    /**
     * 根据客户名称查询客户最近一次已生效的订单
     * @param traderName
     * @return
     */
    SaleorderInfoDto selectLastSaleOrderByTraderName(@Param("traderName") String traderName);


    int updateByPrimaryKeySelective(SaleorderEntity record);
    List<SaleOrderListResultDto> getSaleOrderListPage(SaleOrderListQueryDto saleOrderListQueryDto);


    SaleOrderDetailInfoDto getBySaleOrderId(Integer saleOrderId);

    BigDecimal getRealTotalAmountOfSaleOrder(Integer saleOrderId);

    List<SaleOrderInvoiceDto> findSaleOrderInvoiceListBySaleorderId(@Param("saleorderId")Integer saleorderId);



    SaleorderEntity findBySaleorderId(@Param("saleorderId")Integer saleorderId);


    String findInvoiceTraderContactMobileBySaleorderId(@Param("saleorderId")Integer saleorderId);

    /**
     * 根据销售售后单id查询关联的销售单信息
     *
     * @param afterSalesId 售后单id
     * @return SaleorderEntity
     */
    SaleorderEntity getByAfterSaleId(@Param("afterSalesId")Integer afterSalesId);

    List<SaleorderEntity> getSaleorderInfoBySaleorderIds(@Param("saleorderIds") List<Integer> saleorderIds);

    OrderFinanceInfoDto getSaleorderFinanceInfo(Integer saleorderId);

    List<Integer> querySaleOrderIdBySettlement(SaleOrderQueryDto param);

    Integer getContractVerifyStatusBySaleOrderId(@Param("saleorderId") Integer saleorderId);

    String getSaleOrderInvoiceTraderContactName(Integer saleOrderId);

    List<SaleorderInfoDto> getSaleorderCanInvoiceListPage(@Param("saleorderNo") String saleorderNo);

    List<Integer> getBelongB2bSaleOrderIdBySaleOrderIds(@Param("orderIds") List<Integer> orderIds);

    /**
     * 根据订单号获取订单信息
     * @param orderNo
     * @return
     */
	SaleorderInfoDto getByAfterSaleNo(@Param("saleorderNo") String saleorderNo);

    List<SaleOrderGoodsImageDto> getSaleOrderGoodsImage(@Param("skuNoList") List<String> skuNoList);


    /**
     * getSaleorderContractStatus
     * @param saleOrderId
     * @return
     */
    Boolean getSaleorderContractStatus(@Param("saleOrderId") int saleOrderId);
}
