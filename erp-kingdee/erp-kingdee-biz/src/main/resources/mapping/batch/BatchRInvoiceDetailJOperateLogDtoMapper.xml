<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchRInvoiceDetailJOperateLogDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceDetailJOperateLogDto">
    <!--@mbg.generated-->
    <!--@Table T_R_INVOICE_DETAIL_J_OPERATE_LOG-->
    <id column="R_INVOICE_DETAIL_J_OPERATE_LOG_ID" jdbcType="INTEGER" property="rInvoiceDetailJOperateLogId" />
    <result column="INVOICE_DETAIL_ID" jdbcType="INTEGER" property="invoiceDetailId" />
    <result column="OPERATE_LOG_ID" jdbcType="INTEGER" property="operateLogId" />
    <result column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId" />
    <result column="DETAILGOODS_ID" jdbcType="INTEGER" property="detailGoodsId" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="NUM" jdbcType="DECIMAL" property="num" />
    <result column="OPERATE_TYPE" jdbcType="TINYINT" property="operateType" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
    <resultMap id="BaseAndOutInNo" type="com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceDetailJOperateLogDto">
        <!--@mbg.generated-->
        <!--@Table T_R_INVOICE_DETAIL_J_OPERATE_LOG-->
        <id column="R_INVOICE_DETAIL_J_OPERATE_LOG_ID" jdbcType="INTEGER" property="rInvoiceDetailJOperateLogId" />
        <result column="INVOICE_DETAIL_ID" jdbcType="INTEGER" property="invoiceDetailId" />
        <result column="OPERATE_LOG_ID" jdbcType="INTEGER" property="operateLogId" />
        <result column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId" />
        <result column="DETAILGOODS_ID" jdbcType="INTEGER" property="detailGoodsId" />
        <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
        <result column="SKU" jdbcType="VARCHAR" property="sku" />
        <result column="NUM" jdbcType="DECIMAL" property="num" />
        <result column="OPERATE_TYPE" jdbcType="TINYINT" property="operateType" />
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
        <result column="CREATOR" jdbcType="INTEGER" property="creator" />
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
        <result column="UPDATER" jdbcType="INTEGER" property="updater" />
        <result column="OUT_IN_NO" jdbcType="VARCHAR" property="outInNo"/>
    </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    R_INVOICE_DETAIL_J_OPERATE_LOG_ID, INVOICE_DETAIL_ID, OPERATE_LOG_ID, INVOICE_ID, 
    DETAILGOODS_ID, GOODS_ID, SKU, NUM,  OPERATE_TYPE, IS_DELETE, ADD_TIME, CREATOR,
    MOD_TIME, UPDATER
  </sql>
  <select id="findByAll" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_R_INVOICE_DETAIL_J_OPERATE_LOG
        <where>
            <if test="rInvoiceDetailJOperateLogId != null">
                and R_INVOICE_DETAIL_J_OPERATE_LOG_ID=#{rInvoiceDetailJOperateLogId,jdbcType=INTEGER}
            </if>
            <if test="invoiceDetailId != null">
                and INVOICE_DETAIL_ID=#{invoiceDetailId,jdbcType=INTEGER}
            </if>
            <if test="operateLogId != null">
                and OPERATE_LOG_ID=#{operateLogId,jdbcType=INTEGER}
            </if>
            <if test="invoiceId != null">
                and INVOICE_ID=#{invoiceId,jdbcType=INTEGER}
            </if>
            <if test="detailGoodsId != null">
                and DETAILGOODS_ID=#{detailGoodsId,jdbcType=INTEGER}
            </if>
            <if test="goodsId != null">
                and GOODS_ID=#{goodsId,jdbcType=INTEGER}
            </if>
            <if test="sku != null and sku != ''">
                and SKU=#{sku,jdbcType=VARCHAR}
            </if>
            <if test="num != null">
                and NUM=#{num,jdbcType=DECIMAL}
            </if>
            <if test="operateType != null">
                and OPERATE_TYPE=#{operateType,jdbcType=TINYINT}
            </if>
            <if test="isDelete != null">
                and IS_DELETE=#{isDelete,jdbcType=INTEGER}
            </if>
            <if test="addTime != null">
                and ADD_TIME=#{addTime,jdbcType=TIMESTAMP}
            </if>
            <if test="creator != null">
                and CREATOR=#{creator,jdbcType=INTEGER}
            </if>
            <if test="modTime != null">
                and MOD_TIME=#{modTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                and UPDATER=#{updater,jdbcType=INTEGER}
            </if>
        </where>
    </select>

<!--auto generated by MybatisCodeHelper on 2022-12-06-->
  <select id="findByInvoiceIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_R_INVOICE_DETAIL_J_OPERATE_LOG
        where INVOICE_ID in
      <foreach collection="ids" item="item" close=")" open="(" separator=",">
          #{item}
      </foreach>
      and IS_DELETE = 0
    </select>

<!--auto generated by MybatisCodeHelper on 2022-12-09-->
  <select id="findByInvoiceId" resultMap="BaseAndOutInNo">
      select
      TRIDJOL.R_INVOICE_DETAIL_J_OPERATE_LOG_ID, TRIDJOL.INVOICE_DETAIL_ID, TRIDJOL.OPERATE_LOG_ID, TRIDJOL.INVOICE_ID,
      TRIDJOL.DETAILGOODS_ID, TRIDJOL.GOODS_ID, TRIDJOL.SKU, TRIDJOL.NUM, TRIDJOL.OPERATE_TYPE, TRIDJOL.IS_DELETE,
      TRIDJOL.ADD_TIME, TRIDJOL.CREATOR, TRIDJOL.MOD_TIME, TRIDJOL.UPDATER,TWGOII.OUT_IN_NO
      from T_R_INVOICE_DETAIL_J_OPERATE_LOG TRIDJOL
      left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TRIDJOL.OPERATE_LOG_ID = TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID
      where INVOICE_ID=#{invoiceId,jdbcType=INTEGER}
      and TWGOII.IS_DELETE = 0
    </select>

    <insert id="insertSelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceDetailJOperateLogDto" >
        <!--          -->
        insert into T_R_INVOICE_DETAIL_J_OPERATE_LOG
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="rInvoiceDetailJOperateLogId != null" >
                R_INVOICE_DETAIL_J_OPERATE_LOG_ID,
            </if>
            <if test="invoiceDetailId != null" >
                INVOICE_DETAIL_ID,
            </if>
            <if test="operateLogId != null" >
                OPERATE_LOG_ID,
            </if>
            <if test="invoiceId != null" >
                INVOICE_ID,
            </if>
            <if test="detailGoodsId != null" >
                DETAILGOODS_ID,
            </if>
            <if test="goodsId != null" >
                GOODS_ID,
            </if>
            <if test="sku != null" >
                SKU,
            </if>
            <if test="num != null" >
                NUM,
            </if>
            <if test="operateType != null" >
                OPERATE_TYPE,
            </if>
            <if test="isDelete != null" >
                IS_DELETE,
            </if>
            <if test="addTime != null" >
                ADD_TIME,
            </if>
            <if test="creator != null" >
                CREATOR,
            </if>
            <if test="modTime != null" >
                MOD_TIME,
            </if>
            <if test="updater != null" >
                UPDATER,
            </if>
            <if test="sourceType != null" >
                SOURCE_TYPE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="rInvoiceDetailJOperateLogId != null" >
                #{rInvoiceDetailJOperateLogId,jdbcType=INTEGER},
            </if>
            <if test="invoiceDetailId != null" >
                #{invoiceDetailId,jdbcType=INTEGER},
            </if>
            <if test="operateLogId != null" >
                #{operateLogId,jdbcType=INTEGER},
            </if>
            <if test="invoiceId != null" >
                #{invoiceId,jdbcType=INTEGER},
            </if>
            <if test="detailGoodsId != null" >
                #{detailGoodsId,jdbcType=INTEGER},
            </if>
            <if test="goodsId != null" >
                #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="sku != null" >
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="num != null" >
                #{num,jdbcType=DECIMAL},
            </if>
            <if test="operateType != null" >
                #{operateType,jdbcType=TINYINT},
            </if>
            <if test="isDelete != null" >
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="addTime != null" >
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null" >
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null" >
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null" >
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="sourceType != null" >
                #{sourceType,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceDetailJOperateLogDto" >
        <!--          -->
        update T_R_INVOICE_DETAIL_J_OPERATE_LOG
        <set >
            <if test="invoiceDetailId != null" >
                INVOICE_DETAIL_ID = #{invoiceDetailId,jdbcType=INTEGER},
            </if>
            <if test="operateLogId != null" >
                OPERATE_LOG_ID = #{operateLogId,jdbcType=INTEGER},
            </if>
            <if test="invoiceId != null" >
                INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
            </if>
            <if test="detailGoodsId != null" >
                DETAILGOODS_ID = #{detailGoodsId,jdbcType=INTEGER},
            </if>
            <if test="goodsId != null" >
                GOODS_ID = #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="sku != null" >
                SKU = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="num != null" >
                NUM = #{num,jdbcType=DECIMAL},
            </if>
            <if test="operateType != null" >
                OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
            </if>
            <if test="isDelete != null" >
                IS_DELETE = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="addTime != null" >
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null" >
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null" >
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null" >
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="sourceType != null" >
                SOURCE_TYPE = #{sourceType,jdbcType=INTEGER},
            </if>
        </set>
        where R_INVOICE_DETAIL_J_OPERATE_LOG_ID = #{rInvoiceDetailJOperateLogId,jdbcType=INTEGER}
    </update>


    <select id="getValidWarehousingLogByRelatedId" resultType="com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceDetailJOperateLogDto">
        SELECT
        TWGOII.RELATED_ID,
        TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID operateLogId,
        TWGOII.GOODS_ID,
        IFNULL(ABS(TWGOII.NUM) - SUM(ABS(IFNULL( TRIDJOL.NUM, 0 ))),0) CAN_RELATION_NUM
        FROM
        T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII
        LEFT JOIN T_R_INVOICE_DETAIL_J_OPERATE_LOG TRIDJOL ON TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID = TRIDJOL.OPERATE_LOG_ID
        AND TRIDJOL.OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
        AND TRIDJOL.IS_DELETE = 0
        LEFT JOIN T_WAREHOUSE_GOODS_OUT_IN TWGOI on TWGOII.OUT_IN_NO = TWGOI.OUT_IN_NO
        WHERE
        TWGOI.IS_VIRTUAL =  #{isVirtual,jdbcType=INTEGER}
        AND TWGOII.RELATED_ID =  #{detailGoodsId,jdbcType=INTEGER}
        AND TWGOII.OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
        AND TWGOI.RELATE_NO =#{afterSaleNo,jdbcType=VARCHAR}
        AND TWGOII.IS_DELETE=0
        GROUP BY
        TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID
        HAVING
        CAN_RELATION_NUM > 0
    </select>

    <select id="getValidWarehousingLogByRelatedIds" resultType="com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceDetailJOperateLogDto">
        SELECT
        T1.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID operateLogId,
        T3.WAREHOUSE_GOODS_OUT_IN_ID,
        T1.RELATED_ID,
        T1.GOODS_ID,
        ABS(T1.NUM) - SUM(ABS(IFNULL( T2.NUM, 0 ))) CAN_RELATION_NUM
        FROM
        T_WAREHOUSE_GOODS_OUT_IN_ITEM T1
        LEFT JOIN T_R_INVOICE_DETAIL_J_OPERATE_LOG T2 ON T1.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID = T2.OPERATE_LOG_ID
        LEFT JOIN T_WAREHOUSE_GOODS_OUT_IN T3 ON T1.OUT_IN_NO = T3.OUT_IN_NO
        AND T2.OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
        AND T2.IS_DELETE = 0
        WHERE
        T1.OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
        AND T1.IS_DELETE = 0
        AND T1.RELATED_ID IN
        <foreach collection="detailGoodsIdList" item="detailGoodsId" index="index" open="(" close=")" separator=",">
            #{detailGoodsId,jdbcType=INTEGER}
        </foreach>
        GROUP BY
        T1.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID
        HAVING
        CAN_RELATION_NUM > 0
    </select>

<!--auto generated by MybatisCodeHelper on 2023-01-31-->
  <select id="findByInvoiceIdAndOperateType" resultMap="BaseAndOutInNo">
      select
      TRIDJOL.R_INVOICE_DETAIL_J_OPERATE_LOG_ID, TRIDJOL.INVOICE_DETAIL_ID, TRIDJOL.OPERATE_LOG_ID, TRIDJOL.INVOICE_ID,
      TRIDJOL.DETAILGOODS_ID, TRIDJOL.GOODS_ID, TRIDJOL.SKU, TRIDJOL.NUM, TRIDJOL.OPERATE_TYPE, TRIDJOL.IS_DELETE,
      TRIDJOL.ADD_TIME, TRIDJOL.CREATOR, TRIDJOL.MOD_TIME, TRIDJOL.UPDATER,TWGOII.OUT_IN_NO
      from T_R_INVOICE_DETAIL_J_OPERATE_LOG TRIDJOL
      left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TRIDJOL.OPERATE_LOG_ID = TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID
      where INVOICE_ID=#{invoiceId,jdbcType=INTEGER}
      and TWGOII.IS_DELETE = 0
      and TRIDJOL.OPERATE_TYPE=#{operateType,jdbcType=TINYINT}
    </select>

    <select id="getSumNumByOperateLogId" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(tridjol.NUM), 0)
        FROM
            T_R_INVOICE_DETAIL_J_OPERATE_LOG tridjol
        WHERE
            tridjol.IS_DELETE = 0
            AND tridjol.OPERATE_TYPE = 2
            AND tridjol.SOURCE_TYPE = 0
            AND tridjol.OPERATE_LOG_ID = #{operateLogId,jdbcType=INTEGER}
    </select>


    <select id="getAfterGoodsIdByCondition" resultType="java.lang.Integer">
        SELECT ASG.AFTER_SALES_GOODS_ID
        FROM T_SALEORDER_GOODS TSG
        LEFT JOIN T_SALEORDER TS ON TSG.SALEORDER_ID = TS.SALEORDER_ID
        LEFT JOIN T_AFTER_SALES TAS ON TS.SALEORDER_ID = TAS.ORDER_ID
        LEFT JOIN T_AFTER_SALES_GOODS ASG ON TAS.AFTER_SALES_ID = ASG.AFTER_SALES_ID
        AND ASG.ORDER_DETAIL_ID = TSG.SALEORDER_GOODS_ID
        WHERE TSG.SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
        AND TAS.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
        LIMIT 1
    </select>
    <select id="findByInvoiceDetailIdAndOperateType"
            resultType="com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceDetailJOperateLogDto">
        select
        <include refid="Base_Column_List"/>
        from T_R_INVOICE_DETAIL_J_OPERATE_LOG
        where INVOICE_DETAIL_ID = #{invoiceDetailId,jdbcType=INTEGER}
        and IS_DELETE = 0
        and OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
    </select>
    <select id="findWarehouseOutByInvoiceId" resultType="java.util.Date">
        select
            twgoi.OUT_IN_TIME
        from
            T_R_INVOICE_DETAIL_J_OPERATE_LOG tridjol
                LEFT JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM twgoii ON
                tridjol.OPERATE_LOG_ID = twgoii.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID
                LEFT JOIN T_WAREHOUSE_GOODS_OUT_IN twgoi ON
                twgoii.OUT_IN_NO = twgoi.OUT_IN_NO
        where
            tridjol.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
          and tridjol.IS_DELETE = 0
          and tridjol.OPERATE_TYPE = #{operateType,jdbcType=TINYINT}
          AND twgoii.IS_DELETE = 0
        ORDER BY
            twgoi.OUT_IN_TIME DESC
        LIMIT 1
    </select>



<!--auto generated by MybatisCodeHelper on 2023-06-19-->
  <select id="findByOperateLogId" resultMap="BaseAndOutInNo">
        select
        <include refid="Base_Column_List"/>
        from T_R_INVOICE_DETAIL_J_OPERATE_LOG
        where OPERATE_LOG_ID=#{operateLogId,jdbcType=INTEGER}
      and IS_DELETE = 0
      order by INVOICE_ID desc,INVOICE_DETAIL_ID desc
    </select>


    <select id="getInvoiceDetailOccupyNum" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(tridjol.NUM), 0)
        FROM
            T_R_INVOICE_DETAIL_J_OPERATE_LOG tridjol
        WHERE
            tridjol.IS_DELETE = 0
          AND tridjol.OPERATE_TYPE = 2
          AND tridjol.SOURCE_TYPE = 0
          AND tridjol.INVOICE_DETAIL_ID = #{invoiceDetailId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-07-14-->
  <delete id="deleteByRInvoiceDetailJOperateLogId">
        delete from T_R_INVOICE_DETAIL_J_OPERATE_LOG
        where R_INVOICE_DETAIL_J_OPERATE_LOG_ID in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </delete>
</mapper>