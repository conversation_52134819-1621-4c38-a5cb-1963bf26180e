package com.vedeng.trader.service;

import com.vedeng.trader.model.TraderContactGenerate;
import com.vedeng.trader.model.vo.TraderContactVo;

import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public interface TraderContactService {

    /**
     * 通过客户编号和手机号码获取联系人信息
     *
     * @param traderId
     * @param mobileNo
     * @return
     */
    TraderContactGenerate getByTraderIdAndMobileNo(Integer traderId, String mobileNo);

    boolean updateByPrimaryKeySelective(TraderContactGenerate contactToUpdate);

    TraderContactGenerate selectByPrimaryKey(Integer traderContactId);


    TraderContactGenerate insertSelective(TraderContactGenerate traderContact);

    /**
     * 查询客户的联系方式集合，并且联系方式的mobile是已关联到该客户的注册账号
     * @param traderId 客户id
     * @return 联系方式
     */
    List<TraderContactVo> getTraderContactInWebAccountByTraderId(Integer traderId);

}
