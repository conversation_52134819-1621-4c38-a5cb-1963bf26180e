package com.vedeng.erp.wms.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 物流附件
 */
@Getter
@Setter
public class LogisticsInfoFileEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer logisticsInfoFileId;

    /**
     * 业务单号
     */
    private String orderNo;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 物流公司ID
     */
    private Integer logisticsId;

    /**
     * 快递ID
     */
    private Integer expressId;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 附件地址
     */
    private String url;
}