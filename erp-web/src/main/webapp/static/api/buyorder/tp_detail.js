function viewData(data) {
    return axios({
        url: '/after/newBuyorder/getTpDetail.do',
        method: 'post',
        params:{afterSalesId:data}
    })
}
function viewOrderData(data){
    return axios({
        url: '/after/newBuyorder/getBuyorderInfo.do',
        method: 'post',
        params:{buyorderNo:data}
    })
}

function auditRecordData(data) {
    return axios({
        url: '/old/afterBuyorder/getBuyorderAfterCheckStatus.do',
        method: 'post',
        params: {afterSalesId:data}
    })
}

function closeAfterSalesTp(data){
    return axios({
        url: '/after/newBuyorder/closeTpAfter.do',
        method: 'post',
        params: data
    })
}

