$(function(){
    $("#submit").click(function(){
        debugger
        checkLogin();
        // if($("#productComments").val()==""){
        //     $("#productComments1").addClass("errorbor");
        //     return false;
        // }else{
        //     $("#productComments").removeClass("errorbor");
        // }
        if($("#productComments").val().length>300){
            $('#productComments1').css("display","");
            $("#productComments").addClass("errorbor");
            return false;
        }else{
            $('#productComments1').css("display","none");
            $("#productComments").removeClass("errorbor");
        }

        var traderId =$("#traderId").val();
        var posturl = '/order/bussinesschance/saveEditCommentsChance.do';
        $.ajax({
            url:page_url+posturl,
            data:$('#myform').serialize(),
            type:"POST",
            dataType : "json",
            async: false,
            success:function(data)
            {
                if(data.code==0){
                    window.close();
                    var bussinessChance=data.data;
                    window.parent.location.href=page_url+"/order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=" + bussinessChance.bussinessChanceId + "&traderId="
								+ traderId;
                }else{
                    layer.alert(data.message);
                }

            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });

//		var nameReg = /^[a-zA-Z0-9\u4e00-\u9fa5\.\(\)\,]{1,32}$/;
//		if($('#goodsBrand').val()!='' && !nameReg.test($('#goodsBrand').val())){
//			warnTips("goodsBrand","产品品牌不允许使用特殊字符");
//			return false;
//		}else{
//			delWarnTips("goodsBrand");
//		}
    })

    //取消按钮 返回上一级页面
    // $("#close-layer").click(function(){
    //     window.history.go(-1);
    // })
    // function submitAction() {
    //     window.history.go(-1);
    // }
});


