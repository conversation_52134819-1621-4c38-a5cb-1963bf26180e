package com.wms.dto;

public class WmsRequestDto {

    String  method;

    String  client_customerid;

    String	client_db;

    //ASNRT
    String	messageid;

    //Token 号
    String	apptoken;

    //验签KEY
    String	appkey;

    //验签值
    String	sign;

    //时间,格式 YYYY-MM-DD HH:MM:SS
    String	timestamp;

    //JSON格式业务数据
    String	data;

    public String getMethod() {
        return method;
    }

    public String getClient_customerid() {
        return client_customerid;
    }

    public String getClient_db() {
        return client_db;
    }

    public String getMessageid() {
        return messageid;
    }

    public String getApptoken() {
        return apptoken;
    }

    public String getAppkey() {
        return appkey;
    }

    public String getSign() {
        return sign;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public String getData() {
        return data;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public void setClient_customerid(String client_customerid) {
        this.client_customerid = client_customerid;
    }

    public void setClient_db(String client_db) {
        this.client_db = client_db;
    }

    public void setMessageid(String messageid) {
        this.messageid = messageid;
    }

    public void setApptoken(String apptoken) {
        this.apptoken = apptoken;
    }

    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public void setData(String data) {
        this.data = data;
    }
}
