package com.vedeng.erp.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.system.domain.TUserWorkdate;
import com.vedeng.erp.system.domain.TUserWorkdetail;
import com.vedeng.erp.system.dto.BussinessChanceMessageVo;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.dto.UserWorkDetail;
import com.vedeng.erp.system.mapper.TUserWorkdateMapper;
import com.vedeng.erp.system.mapper.TUserWorkdetailMapper;
import com.vedeng.erp.system.mapper.UserMapper;
import com.vedeng.erp.system.service.UserWorkApiService;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.uac.api.dto.MessageSendDto;
import com.vedeng.uac.api.dto.UserDTO;
import com.vedeng.uac.api.dto.WxUserDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;

@Slf4j
@Service
public class UserWorkApiServiceImpl implements UserWorkApiService {

    @Autowired
    private TUserWorkdetailMapper userWorkdetailMapper;

    @Autowired
    private TUserWorkdateMapper userWorkdateMapper;

    @Autowired
    private UserMapper userMapper;

    public static String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Value(value="${jobNumberList:[]}")
    private String jobNumberList;

    @Value("${invoiceWxMsgOpen}")
    private Boolean invoiceWxMsgOpen;
    @Autowired
    private UacWxUserInfoApiService uacWxUserInfoApiService;

    @Transactional
    @Override
    public boolean handUserWorkDetail(List<UserWorkDetail> userWorkDetailList) {
        if(CollectionUtil.isEmpty(userWorkDetailList)){
            return false;
        }

        //jobNumberList为[{"woquJobNumber":"0963","wxJobNumber":"qy01fe81290eeb0db09c3194f895"},{"woquJobNumber":"1009","wxJobNumber":"qy01f881d90e720db09c3ef8338e"},{"woquJobNumber":"0957","wxJobNumber":"qy01f8818c0ec90db19c50035c14"},{"woquJobNumber":"1021","wxJobNumber":"qy01f781bb0e8a0db09c3502d3e6"},{"woquJobNumber":"0967","wxJobNumber":"qy01f281c70e080db19c612cb787"},{"woquJobNumber":"0951","wxJobNumber":"qy01df81ef0ed90db09c4ac588d5"},{"woquJobNumber":"0984","wxJobNumber":"qy01d381020ea40db19cfe9771a3"},{"woquJobNumber":"0979","wxJobNumber":"qy01cb81580ea60db39cc4b09f65"},{"woquJobNumber":"1018","wxJobNumber":"qy01c581030e5e0db09c4b33af0f"},{"woquJobNumber":"1011","wxJobNumber":"qy01c581020eaa0db09cd4d453f4"},{"woquJobNumber":"0940","wxJobNumber":"qy01ad81c60ee20db29c80838c81"},{"woquJobNumber":"1046","wxJobNumber":"qy01a281ed0e240db39c5d5fa158"},{"woquJobNumber":"0959","wxJobNumber":"qy01a1810f0e4b0db39c4c024008"},{"woquJobNumber":"0965","wxJobNumber":"qy0189817b0e210db19c5ade2c18"},{"woquJobNumber":"1023","wxJobNumber":"qy018781280e7b0db39cb20c5706"},{"woquJobNumber":"0949","wxJobNumber":"qy018381450e800db09c9988b01a"},{"woquJobNumber":"1028","wxJobNumber":"qy017e81780e3e0db09ce9bb2294"},{"woquJobNumber":"1043","wxJobNumber":"qy017b81fe0eb10db19cb789fd1c"},{"woquJobNumber":"0952","wxJobNumber":"qy015981760e0f0db19ca7754657"},{"woquJobNumber":"0973","wxJobNumber":"qy015681230e9d0db29cd25b6fbb"},{"woquJobNumber":"1042","wxJobNumber":"qy0150812e0ecc0db09cc3311d47"},{"woquJobNumber":"1029","wxJobNumber":"qy014381050e7c0db09c0344160a"},{"woquJobNumber":"0986","wxJobNumber":"qy013f81300e2b0db19c6978411d"},{"woquJobNumber":"1001","wxJobNumber":"qy013b810d0edd0db09c56a65516"},{"woquJobNumber":"1027","wxJobNumber":"qy012881bb0e570db09c96438a1c"},{"woquJobNumber":"1038","wxJobNumber":"qy012881550e830db09c896b5218"},{"woquJobNumber":"1032","wxJobNumber":"qy012881190e7c0db09c37dde023"},{"woquJobNumber":"1047","wxJobNumber":"qy012581de0e560db29cecfed2ec"},{"woquJobNumber":"1045","wxJobNumber":"qy012381660e1c0db19c9cb09de5"},{"woquJobNumber":"0980","wxJobNumber":"qy011081d60e010db19ce40438ea"},{"woquJobNumber":"0997","wxJobNumber":"qy010c81670e0d0db29c7eb8f1d7"}]
        //将jobNumberList转换为map,key为woquJobNumber,value为wxJobNumber
        Map<String,String> jobNumberMap = new HashMap<>();
        if(StringUtils.isNotBlank(jobNumberList)){
            JSONArray jsonArray = JSONArray.parseArray(jobNumberList);
            for(int i=0;i<jsonArray.size();i++){
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String woquJobNumber = jsonObject.getString("woquJobNumber");
                String wxJobNumber = jsonObject.getString("wxJobNumber");
                jobNumberMap.put(woquJobNumber,wxJobNumber);
            }
        }

        for(UserWorkDetail user:userWorkDetailList){
            log.info("请假信息:{}",new Object[]{JSONObject.toJSONString(user)});
            if(ObjectUtil.isEmpty(user.getUserId()) || ObjectUtil.isEmpty(user.getStartTime()) || ObjectUtil.isEmpty(user.getEndTime())){
                log.info("请假信息不处理，没有开始或结束时间:{}",new Object[]{JSONObject.toJSONString(user)});
                continue;
            }
            if(CollectionUtil.isNotEmpty(jobNumberMap)){
                String userId = user.getUserId();
                String handoverUserId = user.getHandoverUserId();
                String newUserId = jobNumberMap.get(userId);
                if(StringUtils.isNotBlank(newUserId)){
                    user.setUserId(newUserId);
                }
                String newHandOverUserId = jobNumberMap.get(handoverUserId);
                if(StringUtils.isNotBlank(newHandOverUserId)){
                    user.setHandoverUserId(newHandOverUserId);
                }
            }


            String md5 = DigestUtils.md5Hex(user.getUserId()+"_"+user.getStartTime()+"_"+user.getEndTime()+"_"+user.getWorkType());

            TUserWorkdetail old = userWorkdetailMapper.selectByUniqueId(md5);
            if(old !=null){
                log.info("该条请假信息已存在:{}",new Object[]{JSONObject.toJSONString(old)});
                continue;
            }

            TUserWorkdetail record = new TUserWorkdetail();
            record.setUniqueId(md5);
            record.setJobNumber(user.getUserId());
            Date startDateTime = converDateTime(user.getStartTime());
            Date endDateTime = converDateTime(user.getEndTime());
            if(startDateTime == null || endDateTime == null){
                log.error("发现异常时间请假数据，此条跳过");
                continue;
            }
            record.setStartTime(startDateTime);
            record.setEndTime(endDateTime);
            record.setHandOverJobNumber(user.getHandoverUserId());
            record.setHandOverName(user.getHandoverUserName());
            record.setWorktype(user.getWorkType());
            record.setAddTime(new java.util.Date());
            record.setModTime(new Date());
            userWorkdetailMapper.insertSelective(record);
        }
        log.info("handUserWorkDetail");
        return true;
    }

    static java.util.Date converDateTime(String datetime){
        try{
            SimpleDateFormat sf = new SimpleDateFormat(TIME_FORMAT);
            return sf.parse(datetime);
        }catch (Exception e){
            log.error("解析休假时间失败"+datetime,e);
            return null;
        }

    }




    @Override
    public Integer getUserBusiness2OtherSaleUser(Integer saleUserId, String date) {

        List<Integer> useridList = new ArrayList<>();
        useridList.add(saleUserId);
        RestfulResult<List<WxUserDto>> resultLiZhi = uacWxUserInfoApiService.getWxUserListByUserId(useridList);
        if(resultLiZhi.isSuccess()){
            if(CollectionUtil.size(resultLiZhi.getData())>0){
                log.info("分配商机时，员工已离职:{}",saleUserId);
                return 0;
            }
        }

        UserDTO userDTO = new UserDTO();
        userDTO.setId(saleUserId);
        RestfulResult<WxUserDto>  result =  uacWxUserInfoApiService.getByAccountId(saleUserId);


        String jobNumber = result.getData().getJobNumber();

        TUserWorkdetail result1 = userWorkdetailMapper.selectByUserIdAndDate(jobNumber,date,1);//0请假1销假
        if(result1 != null){//如果有销假的数据，直接认为该人当天上班
            return saleUserId;
        }
        TUserWorkdetail result0 = userWorkdetailMapper.selectByUserIdAndDate(jobNumber,date,0);//0请假1销假
        if(result0 ==null ){
            return  saleUserId;
        }
        String jiaojieJobNumber = result0.getHandOverJobNumber();//

//        RestfulResult<WxUserDto> restfulResult = uacWxUserInfoApiService.getWxUserByJobNumber(jiaojieJobNumber);
        RestfulResult<UserDTO> restfulResult = uacWxUserInfoApiService.getUserInfoByJobNumber(jiaojieJobNumber);
        if(restfulResult.isSuccess()){
            return restfulResult.getData().getAccountId();
        }
        return saleUserId;
    }

    @Value("${defaultWorkTime:[\"9:15-11:50\",\"12:50-17:45\"]}")
    private String defaultWorkTime;//以上默认值有冒号，用双冒号代替

    @Override
    public boolean isWorkTime() {
        return false;
//        log.info("商机分配时间，检查当前是否是工作时间:{}",defaultWorkTime);
//        LocalTime currentTime = LocalTime.now();//改为动态配置
////        LocalTime startTime1 = LocalTime.of(9, 15);
////        LocalTime endTime1 = LocalTime.of(11, 50);
////        LocalTime startTime2 = LocalTime.of(16, 50);
////        LocalTime endTime2 = LocalTime.of(17, 45);
//        boolean isWithinRange = false;
//        List<String> workTimeArray = JSONArray.parseArray(defaultWorkTime,String.class);
//        for(String workTime:workTimeArray){
//            String[] wtArray = workTime.split("-");
//            String startT = wtArray[0];
//            String endT = wtArray[1];
//            LocalTime startTime = LocalTime.of(Integer.parseInt(startT.split(":")[0]), Integer.parseInt(startT.split(":")[1]));
//            LocalTime endTime = LocalTime.of(Integer.parseInt(endT.split(":")[0]), Integer.parseInt(endT.split(":")[1]));
//            if(currentTime.isAfter(startTime) && currentTime.isBefore(endTime)){
//                isWithinRange = true;
//                break;
//            }
//        }
//        if(!isWithinRange ){//当前时间是否在工作时间范围内。工作时间（工作日）：上午9点15分-11点50分，下午12点50分-17点45分
//            return false;//如果不在以上时间内，直接不用查表了。一律算到休息时间。
//        }
//        LocalDate currentDate = LocalDate.now();
//        // 获取当前日期对应的星期几
//        DayOfWeek dayOfWeek = currentDate.getDayOfWeek();
//        // 输出星期几的名称
//        //System.out.println("当前是星期" + dayOfWeek.getValue());
//        TUserWorkdate userWorkdate = userWorkdateMapper.selectByNow();
//        if(dayOfWeek.getValue()>=1 && dayOfWeek.getValue() <=5){
//            //周一至周五。此时还要判断是否是假期
//            if(userWorkdate ==null || 0!=userWorkdate.getWorkType()){//0假期1工作日  假期数据为空或者本身这条不是假期
//                return true;
//            }
//        }else{
//            //周六或周日。此时还要判断是否是加班
//            if(userWorkdate !=null && 1==userWorkdate.getWorkType()){//0假期1工作日 如果今天是周六或周日，但表里维护了今天上班，则认为今天上班。
//                return true;
//            }
//        }
//
//        return false;
    }

    @Value("${messageTemplate}")
    private String messageTemplate;

    @Value("${messageTemplateForXs}")
    private String messageTemplateForXs;

    @Override
    public boolean sendMsgForXs(Integer userId, BussinessChanceMessageVo messageVo) {
        log.info("开始企微消息推送线索：{},{}",new Object[]{userId,JSONObject.toJSONString(messageVo)});
        if(userId == null || messageVo == null){
            log.error("消息发送失败,参数不能为空");
            return false;
        }
        String content = StrUtil.format(
                messageTemplateForXs,
                messageVo.getBussinessNo(),
                messageVo.getSendTime(),
                messageVo.getCustomerName(),
                messageVo.getMobile(),
                messageVo.getRemark());

        MessageSendDto messageSendDto = new MessageSendDto();
        messageSendDto.setUserId(userId);
        messageSendDto.setMsgContent(content);

        RestfulResult<WxUserDto> result = uacWxUserInfoApiService.sendMsgForLxCrm(messageSendDto );
        if(result.isSuccess()){
            log.info("消息发送成功:{}",userId);
            return true;
        }
        log.warn("消息发送失败:{},{}",userId,JSONObject.toJSONString(result));
        return false;
    }

    @Override
    public boolean sendMsg(Integer userId, BussinessChanceMessageVo messageVo) {
        log.info("开始企微消息推送：{},{}",new Object[]{userId,JSONObject.toJSONString(messageVo)});
        if(userId == null || messageVo == null){
            log.error("消息发送失败,参数不能为空");
            return false;
        }
        String content = StrUtil.format(
                messageTemplate,
                messageVo.getBussinessNo(),
                messageVo.getSendTime(),
                messageVo.getCustomerName(),
                messageVo.getMobile(),
                messageVo.getRemark());

        MessageSendDto messageSendDto = new MessageSendDto();
        messageSendDto.setUserId(userId);
        messageSendDto.setMsgContent(content);

        RestfulResult<WxUserDto> result = uacWxUserInfoApiService.sendMsg(messageSendDto );
        if(result.isSuccess()){
            log.info("消息发送成功:{}",userId);
            return true;
        }
        log.warn("消息发送失败:{},{}",userId,JSONObject.toJSONString(result));
        return false;
    }

    @Override
    public boolean sendInvoiceMsg(Integer userId, String msg) {
        log.info("开始企微消息推送：{},{}",userId,msg);

        if (!invoiceWxMsgOpen) {
            log.info("企业微信开关关闭");
            return true;
        }

        if(userId == null || StrUtil.isEmpty(msg)){
            log.error("消息发送失败,参数不能为空");
            return false;
        }
        UserDto userDto = userMapper.getUserByUserId(userId);
        if (Objects.isNull(userDto) || !ErpConstant.ZERO.equals(userDto.getIsDisabled()) ) {
            log.info("员工不存在或者已离职,userDto:{}", JSON.toJSONString(userDto));
            return false;
        }
        String content = msg;

        MessageSendDto messageSendDto = new MessageSendDto();
        messageSendDto.setUserId(userId);
        messageSendDto.setMsgContent(content);

        RestfulResult<WxUserDto> result = uacWxUserInfoApiService.sendMsg(messageSendDto );
        if(result.isSuccess()){
            log.info("消息发送成功:{}",userId);
            return true;
        }
        log.warn("消息发送失败:{},{}",userId,JSONObject.toJSONString(result));
        return false;
    }

    @Override
    public boolean sendMsg(Integer userId, String msg) {
        log.info("开始企微消息推送：{},{}",userId,msg);

        if(userId == null || StrUtil.isEmpty(msg)){
            log.error("消息发送失败,参数不能为空");
            return false;
        }
        UserDto userDto = userMapper.getUserByUserId(userId);
        if (Objects.isNull(userDto) || !ErpConstant.ZERO.equals(userDto.getIsDisabled()) ) {
            log.info("员工不存在或者已离职,userDto:{}", JSON.toJSONString(userDto));
            return false;
        }

        MessageSendDto messageSendDto = new MessageSendDto();
        messageSendDto.setUserId(userId);
        messageSendDto.setMsgContent(msg);


        RestfulResult<WxUserDto> result = uacWxUserInfoApiService.sendMsg(messageSendDto );
        if(result.isSuccess()){
            log.info("消息发送成功:{}",userId);
            return true;
        }
        log.warn("消息发送失败:{},{}",userId,JSONObject.toJSONString(result));
        return false;
    }



    @Override
    public UserDto getUserByUserId(Integer userId) {
        return userMapper.getUserByUserId(userId);
    }

//    public static void main(String[] args) {
//        LocalDate currentDate = LocalDate.now();
//        // 获取当前日期对应的星期几
//        DayOfWeek dayOfWeek = currentDate.getDayOfWeek();
//        // 输出星期几的名称
//        System.out.println("当前是星期" + dayOfWeek.getValue());
//    }
}
