package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchMaterialFinanceDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/5 13:09
 */
@Repository
public interface BatchMaterialFinanceDtoMapper {

    /**
     * 查询审计物料信息
     *
     * @param queryMaterialFinanceDto BatchMaterialFinanceDto
     * @return List<BatchMaterialFinanceDto>
     */
    List<BatchMaterialFinanceDto> findByAll(BatchMaterialFinanceDto queryMaterialFinanceDto);

    /**
     * 更新审计商品推送金蝶信息
     *
     * @param goodsFinanceId goodsFinanceId
     */
    void updateKingDeePushStatus(@Param("goodsFinanceId") Long goodsFinanceId);
}