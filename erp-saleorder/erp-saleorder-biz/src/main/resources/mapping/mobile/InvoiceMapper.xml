<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.mobile.mapper.InvoiceMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.mobile.domain.Invoice">
    <!--@mbg.generated-->
    <!--@Table T_INVOICE-->
    <id column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="INVOICE_CODE" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="INVOICE_FLOW" jdbcType="VARCHAR" property="invoiceFlow" />
    <result column="INVOICE_PROPERTY" jdbcType="BOOLEAN" property="invoiceProperty" />
    <result column="INVOICE_HREF" jdbcType="VARCHAR" property="invoiceHref" />
    <result column="TYPE" jdbcType="INTEGER" property="type" />
    <result column="TAG" jdbcType="BOOLEAN" property="tag" />
    <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
    <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
    <result column="INVOICE_NO" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType" />
    <result column="RATIO" jdbcType="DECIMAL" property="ratio" />
    <result column="COLOR_TYPE" jdbcType="BOOLEAN" property="colorType" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="IS_ENABLE" jdbcType="BOOLEAN" property="isEnable" />
    <result column="VALID_USERID" jdbcType="INTEGER" property="validUserid" />
    <result column="VALID_STATUS" jdbcType="BOOLEAN" property="validStatus" />
    <result column="VALID_TIME" jdbcType="BIGINT" property="validTime" />
    <result column="VALID_COMMENTS" jdbcType="VARCHAR" property="validComments" />
    <result column="INVOICE_PRINT_STATUS" jdbcType="BOOLEAN" property="invoicePrintStatus" />
    <result column="INVOICE_CANCEL_STATUS" jdbcType="BOOLEAN" property="invoiceCancelStatus" />
    <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId" />
    <result column="IS_AUTH" jdbcType="BOOLEAN" property="isAuth" />
    <result column="IS_MONTH_AUTH" jdbcType="BOOLEAN" property="isMonthAuth" />
    <result column="AUTH_TIME" jdbcType="BIGINT" property="authTime" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="INVOICE_APPLY_ID" jdbcType="INTEGER" property="invoiceApplyId" />
    <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="AFTER_EXPRESS_ID" jdbcType="INTEGER" property="afterExpressId" />
    <result column="OSS_FILE_URL" jdbcType="VARCHAR" property="ossFileUrl" />
    <result column="RESOURCE_ID" jdbcType="VARCHAR" property="resourceId" />
    <result column="INVOICE_FROM" jdbcType="TINYINT" property="invoiceFrom" />
    <result column="HX_INVOICE_ID" jdbcType="INTEGER" property="hxInvoiceId" />
    <result column="AUTH_MODE" jdbcType="BOOLEAN" property="authMode" />
    <result column="AUTH_FAIL_REASON" jdbcType="VARCHAR" property="authFailReason" />
    <result column="IS_AUTHING" jdbcType="BOOLEAN" property="isAuthing" />
    <result column="COLOR_COMPLEMENT_TYPE" jdbcType="BOOLEAN" property="colorComplementType" />
    <result column="IS_AFTER_BUYORDER_ONLY" jdbcType="BOOLEAN" property="isAfterBuyorderOnly" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    INVOICE_ID, COMPANY_ID, INVOICE_CODE, INVOICE_FLOW, INVOICE_PROPERTY, INVOICE_HREF, 
    `TYPE`, TAG, RELATED_ID, AFTER_SALES_ID, INVOICE_NO, INVOICE_TYPE, RATIO, COLOR_TYPE, 
    AMOUNT, IS_ENABLE, VALID_USERID, VALID_STATUS, VALID_TIME, VALID_COMMENTS, INVOICE_PRINT_STATUS, 
    INVOICE_CANCEL_STATUS, EXPRESS_ID, IS_AUTH, IS_MONTH_AUTH, AUTH_TIME, ADD_TIME, CREATOR, 
    MOD_TIME, UPDATER, INVOICE_APPLY_ID, SEND_TIME, AFTER_EXPRESS_ID, OSS_FILE_URL, RESOURCE_ID, 
    INVOICE_FROM, HX_INVOICE_ID, AUTH_MODE, AUTH_FAIL_REASON, IS_AUTHING, COLOR_COMPLEMENT_TYPE, 
    IS_AFTER_BUYORDER_ONLY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_INVOICE
    where INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_INVOICE
    where INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="INVOICE_ID" keyProperty="invoiceId" parameterType="com.vedeng.erp.mobile.domain.Invoice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE (COMPANY_ID, INVOICE_CODE, INVOICE_FLOW, 
      INVOICE_PROPERTY, INVOICE_HREF, `TYPE`, 
      TAG, RELATED_ID, AFTER_SALES_ID, 
      INVOICE_NO, INVOICE_TYPE, RATIO, 
      COLOR_TYPE, AMOUNT, IS_ENABLE, 
      VALID_USERID, VALID_STATUS, VALID_TIME, 
      VALID_COMMENTS, INVOICE_PRINT_STATUS, INVOICE_CANCEL_STATUS, 
      EXPRESS_ID, IS_AUTH, IS_MONTH_AUTH, 
      AUTH_TIME, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, INVOICE_APPLY_ID, 
      SEND_TIME, AFTER_EXPRESS_ID, OSS_FILE_URL, 
      RESOURCE_ID, INVOICE_FROM, HX_INVOICE_ID, 
      AUTH_MODE, AUTH_FAIL_REASON, IS_AUTHING, 
      COLOR_COMPLEMENT_TYPE, IS_AFTER_BUYORDER_ONLY
      )
    values (#{companyId,jdbcType=INTEGER}, #{invoiceCode,jdbcType=VARCHAR}, #{invoiceFlow,jdbcType=VARCHAR}, 
      #{invoiceProperty,jdbcType=BOOLEAN}, #{invoiceHref,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{tag,jdbcType=BOOLEAN}, #{relatedId,jdbcType=INTEGER}, #{afterSalesId,jdbcType=INTEGER}, 
      #{invoiceNo,jdbcType=VARCHAR}, #{invoiceType,jdbcType=INTEGER}, #{ratio,jdbcType=DECIMAL}, 
      #{colorType,jdbcType=BOOLEAN}, #{amount,jdbcType=DECIMAL}, #{isEnable,jdbcType=BOOLEAN}, 
      #{validUserid,jdbcType=INTEGER}, #{validStatus,jdbcType=BOOLEAN}, #{validTime,jdbcType=BIGINT}, 
      #{validComments,jdbcType=VARCHAR}, #{invoicePrintStatus,jdbcType=BOOLEAN}, #{invoiceCancelStatus,jdbcType=BOOLEAN}, 
      #{expressId,jdbcType=INTEGER}, #{isAuth,jdbcType=BOOLEAN}, #{isMonthAuth,jdbcType=BOOLEAN}, 
      #{authTime,jdbcType=BIGINT}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{invoiceApplyId,jdbcType=INTEGER}, 
      #{sendTime,jdbcType=TIMESTAMP}, #{afterExpressId,jdbcType=INTEGER}, #{ossFileUrl,jdbcType=VARCHAR}, 
      #{resourceId,jdbcType=VARCHAR}, #{invoiceFrom,jdbcType=TINYINT}, #{hxInvoiceId,jdbcType=INTEGER}, 
      #{authMode,jdbcType=BOOLEAN}, #{authFailReason,jdbcType=VARCHAR}, #{isAuthing,jdbcType=BOOLEAN}, 
      #{colorComplementType,jdbcType=BOOLEAN}, #{isAfterBuyorderOnly,jdbcType=BOOLEAN}
      )
  </insert>
  <insert id="insertSelective" keyColumn="INVOICE_ID" keyProperty="invoiceId" parameterType="com.vedeng.erp.mobile.domain.Invoice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="invoiceCode != null and invoiceCode != ''">
        INVOICE_CODE,
      </if>
      <if test="invoiceFlow != null and invoiceFlow != ''">
        INVOICE_FLOW,
      </if>
      <if test="invoiceProperty != null">
        INVOICE_PROPERTY,
      </if>
      <if test="invoiceHref != null and invoiceHref != ''">
        INVOICE_HREF,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="tag != null">
        TAG,
      </if>
      <if test="relatedId != null">
        RELATED_ID,
      </if>
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="invoiceNo != null and invoiceNo != ''">
        INVOICE_NO,
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE,
      </if>
      <if test="ratio != null">
        RATIO,
      </if>
      <if test="colorType != null">
        COLOR_TYPE,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="validUserid != null">
        VALID_USERID,
      </if>
      <if test="validStatus != null">
        VALID_STATUS,
      </if>
      <if test="validTime != null">
        VALID_TIME,
      </if>
      <if test="validComments != null and validComments != ''">
        VALID_COMMENTS,
      </if>
      <if test="invoicePrintStatus != null">
        INVOICE_PRINT_STATUS,
      </if>
      <if test="invoiceCancelStatus != null">
        INVOICE_CANCEL_STATUS,
      </if>
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      <if test="isAuth != null">
        IS_AUTH,
      </if>
      <if test="isMonthAuth != null">
        IS_MONTH_AUTH,
      </if>
      <if test="authTime != null">
        AUTH_TIME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="invoiceApplyId != null">
        INVOICE_APPLY_ID,
      </if>
      <if test="sendTime != null">
        SEND_TIME,
      </if>
      <if test="afterExpressId != null">
        AFTER_EXPRESS_ID,
      </if>
      <if test="ossFileUrl != null and ossFileUrl != ''">
        OSS_FILE_URL,
      </if>
      <if test="resourceId != null and resourceId != ''">
        RESOURCE_ID,
      </if>
      <if test="invoiceFrom != null">
        INVOICE_FROM,
      </if>
      <if test="hxInvoiceId != null">
        HX_INVOICE_ID,
      </if>
      <if test="authMode != null">
        AUTH_MODE,
      </if>
      <if test="authFailReason != null and authFailReason != ''">
        AUTH_FAIL_REASON,
      </if>
      <if test="isAuthing != null">
        IS_AUTHING,
      </if>
      <if test="colorComplementType != null">
        COLOR_COMPLEMENT_TYPE,
      </if>
      <if test="isAfterBuyorderOnly != null">
        IS_AFTER_BUYORDER_ONLY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="invoiceCode != null and invoiceCode != ''">
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceFlow != null and invoiceFlow != ''">
        #{invoiceFlow,jdbcType=VARCHAR},
      </if>
      <if test="invoiceProperty != null">
        #{invoiceProperty,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceHref != null and invoiceHref != ''">
        #{invoiceHref,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="tag != null">
        #{tag,jdbcType=BOOLEAN},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="invoiceNo != null and invoiceNo != ''">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="ratio != null">
        #{ratio,jdbcType=DECIMAL},
      </if>
      <if test="colorType != null">
        #{colorType,jdbcType=BOOLEAN},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=BOOLEAN},
      </if>
      <if test="validUserid != null">
        #{validUserid,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        #{validStatus,jdbcType=BOOLEAN},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=BIGINT},
      </if>
      <if test="validComments != null and validComments != ''">
        #{validComments,jdbcType=VARCHAR},
      </if>
      <if test="invoicePrintStatus != null">
        #{invoicePrintStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceCancelStatus != null">
        #{invoiceCancelStatus,jdbcType=BOOLEAN},
      </if>
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      <if test="isAuth != null">
        #{isAuth,jdbcType=BOOLEAN},
      </if>
      <if test="isMonthAuth != null">
        #{isMonthAuth,jdbcType=BOOLEAN},
      </if>
      <if test="authTime != null">
        #{authTime,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="invoiceApplyId != null">
        #{invoiceApplyId,jdbcType=INTEGER},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="afterExpressId != null">
        #{afterExpressId,jdbcType=INTEGER},
      </if>
      <if test="ossFileUrl != null and ossFileUrl != ''">
        #{ossFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null and resourceId != ''">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="invoiceFrom != null">
        #{invoiceFrom,jdbcType=TINYINT},
      </if>
      <if test="hxInvoiceId != null">
        #{hxInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="authMode != null">
        #{authMode,jdbcType=BOOLEAN},
      </if>
      <if test="authFailReason != null and authFailReason != ''">
        #{authFailReason,jdbcType=VARCHAR},
      </if>
      <if test="isAuthing != null">
        #{isAuthing,jdbcType=BOOLEAN},
      </if>
      <if test="colorComplementType != null">
        #{colorComplementType,jdbcType=BOOLEAN},
      </if>
      <if test="isAfterBuyorderOnly != null">
        #{isAfterBuyorderOnly,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.mobile.domain.Invoice">
    <!--@mbg.generated-->
    update T_INVOICE
    <set>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="invoiceCode != null and invoiceCode != ''">
        INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceFlow != null and invoiceFlow != ''">
        INVOICE_FLOW = #{invoiceFlow,jdbcType=VARCHAR},
      </if>
      <if test="invoiceProperty != null">
        INVOICE_PROPERTY = #{invoiceProperty,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceHref != null and invoiceHref != ''">
        INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=INTEGER},
      </if>
      <if test="tag != null">
        TAG = #{tag,jdbcType=BOOLEAN},
      </if>
      <if test="relatedId != null">
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="invoiceNo != null and invoiceNo != ''">
        INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="ratio != null">
        RATIO = #{ratio,jdbcType=DECIMAL},
      </if>
      <if test="colorType != null">
        COLOR_TYPE = #{colorType,jdbcType=BOOLEAN},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=BOOLEAN},
      </if>
      <if test="validUserid != null">
        VALID_USERID = #{validUserid,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
      </if>
      <if test="validTime != null">
        VALID_TIME = #{validTime,jdbcType=BIGINT},
      </if>
      <if test="validComments != null and validComments != ''">
        VALID_COMMENTS = #{validComments,jdbcType=VARCHAR},
      </if>
      <if test="invoicePrintStatus != null">
        INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceCancelStatus != null">
        INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=BOOLEAN},
      </if>
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      <if test="isAuth != null">
        IS_AUTH = #{isAuth,jdbcType=BOOLEAN},
      </if>
      <if test="isMonthAuth != null">
        IS_MONTH_AUTH = #{isMonthAuth,jdbcType=BOOLEAN},
      </if>
      <if test="authTime != null">
        AUTH_TIME = #{authTime,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="invoiceApplyId != null">
        INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
      </if>
      <if test="sendTime != null">
        SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="afterExpressId != null">
        AFTER_EXPRESS_ID = #{afterExpressId,jdbcType=INTEGER},
      </if>
      <if test="ossFileUrl != null and ossFileUrl != ''">
        OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null and resourceId != ''">
        RESOURCE_ID = #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="invoiceFrom != null">
        INVOICE_FROM = #{invoiceFrom,jdbcType=TINYINT},
      </if>
      <if test="hxInvoiceId != null">
        HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="authMode != null">
        AUTH_MODE = #{authMode,jdbcType=BOOLEAN},
      </if>
      <if test="authFailReason != null and authFailReason != ''">
        AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR},
      </if>
      <if test="isAuthing != null">
        IS_AUTHING = #{isAuthing,jdbcType=BOOLEAN},
      </if>
      <if test="colorComplementType != null">
        COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=BOOLEAN},
      </if>
      <if test="isAfterBuyorderOnly != null">
        IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly,jdbcType=BOOLEAN},
      </if>
    </set>
    where INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.mobile.domain.Invoice">
    <!--@mbg.generated-->
    update T_INVOICE
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      INVOICE_FLOW = #{invoiceFlow,jdbcType=VARCHAR},
      INVOICE_PROPERTY = #{invoiceProperty,jdbcType=BOOLEAN},
      INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR},
      `TYPE` = #{type,jdbcType=INTEGER},
      TAG = #{tag,jdbcType=BOOLEAN},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      RATIO = #{ratio,jdbcType=DECIMAL},
      COLOR_TYPE = #{colorType,jdbcType=BOOLEAN},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      IS_ENABLE = #{isEnable,jdbcType=BOOLEAN},
      VALID_USERID = #{validUserid,jdbcType=INTEGER},
      VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
      VALID_TIME = #{validTime,jdbcType=BIGINT},
      VALID_COMMENTS = #{validComments,jdbcType=VARCHAR},
      INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=BOOLEAN},
      INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=BOOLEAN},
      EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      IS_AUTH = #{isAuth,jdbcType=BOOLEAN},
      IS_MONTH_AUTH = #{isMonthAuth,jdbcType=BOOLEAN},
      AUTH_TIME = #{authTime,jdbcType=BIGINT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
      SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
      AFTER_EXPRESS_ID = #{afterExpressId,jdbcType=INTEGER},
      OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR},
      RESOURCE_ID = #{resourceId,jdbcType=VARCHAR},
      INVOICE_FROM = #{invoiceFrom,jdbcType=TINYINT},
      HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER},
      AUTH_MODE = #{authMode,jdbcType=BOOLEAN},
      AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR},
      IS_AUTHING = #{isAuthing,jdbcType=BOOLEAN},
      COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=BOOLEAN},
      IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly,jdbcType=BOOLEAN}
    where INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_INVOICE
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="COMPANY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.companyId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INVOICE_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_FLOW = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceFlow,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_PROPERTY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceProperty,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="INVOICE_HREF = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceHref,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`TYPE` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.type,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.tag,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="RELATED_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.relatedId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="AFTER_SALES_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.afterSalesId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INVOICE_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="RATIO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.ratio,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="COLOR_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.colorType,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.amount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="IS_ENABLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.isEnable,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="VALID_USERID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.validUserid,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="VALID_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.validStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="VALID_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.validTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="VALID_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.validComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_PRINT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoicePrintStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="INVOICE_CANCEL_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceCancelStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="EXPRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.expressId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_AUTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.isAuth,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_MONTH_AUTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.isMonthAuth,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="AUTH_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.authTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INVOICE_APPLY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceApplyId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SEND_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.sendTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="AFTER_EXPRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.afterExpressId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="OSS_FILE_URL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.ossFileUrl,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="RESOURCE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.resourceId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_FROM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceFrom,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="HX_INVOICE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.hxInvoiceId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="AUTH_MODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.authMode,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="AUTH_FAIL_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.authFailReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_AUTHING = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.isAuthing,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="COLOR_COMPLEMENT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.colorComplementType,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_AFTER_BUYORDER_ONLY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.isAfterBuyorderOnly,jdbcType=BOOLEAN}
        </foreach>
      </trim>
    </trim>
    where INVOICE_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.invoiceId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_INVOICE
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="COMPANY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.companyId != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.companyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceCode != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_FLOW = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceFlow != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceFlow,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_PROPERTY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceProperty != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceProperty,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_HREF = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceHref != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceHref,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`TYPE` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.type != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.type,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tag != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.tag,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="RELATED_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.relatedId != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.relatedId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="AFTER_SALES_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterSalesId != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.afterSalesId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceNo != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceType != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="RATIO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ratio != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.ratio,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="COLOR_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.colorType != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.colorType,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.amount != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.amount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_ENABLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isEnable != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.isEnable,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="VALID_USERID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.validUserid != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.validUserid,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="VALID_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.validStatus != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.validStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="VALID_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.validTime != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.validTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="VALID_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.validComments != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.validComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_PRINT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoicePrintStatus != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoicePrintStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_CANCEL_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceCancelStatus != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceCancelStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="EXPRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.expressId != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.expressId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_AUTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isAuth != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.isAuth,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_MONTH_AUTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isMonthAuth != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.isMonthAuth,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="AUTH_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.authTime != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.authTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_APPLY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceApplyId != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceApplyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SEND_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sendTime != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.sendTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="AFTER_EXPRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterExpressId != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.afterExpressId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="OSS_FILE_URL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ossFileUrl != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.ossFileUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="RESOURCE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.resourceId != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.resourceId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_FROM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceFrom != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.invoiceFrom,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="HX_INVOICE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hxInvoiceId != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.hxInvoiceId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="AUTH_MODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.authMode != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.authMode,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="AUTH_FAIL_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.authFailReason != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.authFailReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_AUTHING = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isAuthing != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.isAuthing,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="COLOR_COMPLEMENT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.colorComplementType != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.colorComplementType,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_AFTER_BUYORDER_ONLY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isAfterBuyorderOnly != null">
            when INVOICE_ID = #{item.invoiceId,jdbcType=INTEGER} then #{item.isAfterBuyorderOnly,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
    </trim>
    where INVOICE_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.invoiceId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="INVOICE_ID" keyProperty="invoiceId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE
    (COMPANY_ID, INVOICE_CODE, INVOICE_FLOW, INVOICE_PROPERTY, INVOICE_HREF, `TYPE`, 
      TAG, RELATED_ID, AFTER_SALES_ID, INVOICE_NO, INVOICE_TYPE, RATIO, COLOR_TYPE, AMOUNT, 
      IS_ENABLE, VALID_USERID, VALID_STATUS, VALID_TIME, VALID_COMMENTS, INVOICE_PRINT_STATUS, 
      INVOICE_CANCEL_STATUS, EXPRESS_ID, IS_AUTH, IS_MONTH_AUTH, AUTH_TIME, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER, INVOICE_APPLY_ID, SEND_TIME, AFTER_EXPRESS_ID, OSS_FILE_URL, 
      RESOURCE_ID, INVOICE_FROM, HX_INVOICE_ID, AUTH_MODE, AUTH_FAIL_REASON, IS_AUTHING, 
      COLOR_COMPLEMENT_TYPE, IS_AFTER_BUYORDER_ONLY)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.companyId,jdbcType=INTEGER}, #{item.invoiceCode,jdbcType=VARCHAR}, #{item.invoiceFlow,jdbcType=VARCHAR}, 
        #{item.invoiceProperty,jdbcType=BOOLEAN}, #{item.invoiceHref,jdbcType=VARCHAR}, 
        #{item.type,jdbcType=INTEGER}, #{item.tag,jdbcType=BOOLEAN}, #{item.relatedId,jdbcType=INTEGER}, 
        #{item.afterSalesId,jdbcType=INTEGER}, #{item.invoiceNo,jdbcType=VARCHAR}, #{item.invoiceType,jdbcType=INTEGER}, 
        #{item.ratio,jdbcType=DECIMAL}, #{item.colorType,jdbcType=BOOLEAN}, #{item.amount,jdbcType=DECIMAL}, 
        #{item.isEnable,jdbcType=BOOLEAN}, #{item.validUserid,jdbcType=INTEGER}, #{item.validStatus,jdbcType=BOOLEAN}, 
        #{item.validTime,jdbcType=BIGINT}, #{item.validComments,jdbcType=VARCHAR}, #{item.invoicePrintStatus,jdbcType=BOOLEAN}, 
        #{item.invoiceCancelStatus,jdbcType=BOOLEAN}, #{item.expressId,jdbcType=INTEGER}, 
        #{item.isAuth,jdbcType=BOOLEAN}, #{item.isMonthAuth,jdbcType=BOOLEAN}, #{item.authTime,jdbcType=BIGINT}, 
        #{item.addTime,jdbcType=BIGINT}, #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=BIGINT}, 
        #{item.updater,jdbcType=INTEGER}, #{item.invoiceApplyId,jdbcType=INTEGER}, #{item.sendTime,jdbcType=TIMESTAMP}, 
        #{item.afterExpressId,jdbcType=INTEGER}, #{item.ossFileUrl,jdbcType=VARCHAR}, #{item.resourceId,jdbcType=VARCHAR}, 
        #{item.invoiceFrom,jdbcType=TINYINT}, #{item.hxInvoiceId,jdbcType=INTEGER}, #{item.authMode,jdbcType=BOOLEAN}, 
        #{item.authFailReason,jdbcType=VARCHAR}, #{item.isAuthing,jdbcType=BOOLEAN}, #{item.colorComplementType,jdbcType=BOOLEAN}, 
        #{item.isAfterBuyorderOnly,jdbcType=BOOLEAN})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="INVOICE_ID" keyProperty="invoiceId" parameterType="com.vedeng.erp.mobile.domain.Invoice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        INVOICE_ID,
      </if>
      COMPANY_ID,
      INVOICE_CODE,
      INVOICE_FLOW,
      INVOICE_PROPERTY,
      INVOICE_HREF,
      `TYPE`,
      TAG,
      RELATED_ID,
      AFTER_SALES_ID,
      INVOICE_NO,
      INVOICE_TYPE,
      RATIO,
      COLOR_TYPE,
      AMOUNT,
      IS_ENABLE,
      VALID_USERID,
      VALID_STATUS,
      VALID_TIME,
      VALID_COMMENTS,
      INVOICE_PRINT_STATUS,
      INVOICE_CANCEL_STATUS,
      EXPRESS_ID,
      IS_AUTH,
      IS_MONTH_AUTH,
      AUTH_TIME,
      ADD_TIME,
      CREATOR,
      MOD_TIME,
      UPDATER,
      INVOICE_APPLY_ID,
      SEND_TIME,
      AFTER_EXPRESS_ID,
      OSS_FILE_URL,
      RESOURCE_ID,
      INVOICE_FROM,
      HX_INVOICE_ID,
      AUTH_MODE,
      AUTH_FAIL_REASON,
      IS_AUTHING,
      COLOR_COMPLEMENT_TYPE,
      IS_AFTER_BUYORDER_ONLY,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      #{companyId,jdbcType=INTEGER},
      #{invoiceCode,jdbcType=VARCHAR},
      #{invoiceFlow,jdbcType=VARCHAR},
      #{invoiceProperty,jdbcType=BOOLEAN},
      #{invoiceHref,jdbcType=VARCHAR},
      #{type,jdbcType=INTEGER},
      #{tag,jdbcType=BOOLEAN},
      #{relatedId,jdbcType=INTEGER},
      #{afterSalesId,jdbcType=INTEGER},
      #{invoiceNo,jdbcType=VARCHAR},
      #{invoiceType,jdbcType=INTEGER},
      #{ratio,jdbcType=DECIMAL},
      #{colorType,jdbcType=BOOLEAN},
      #{amount,jdbcType=DECIMAL},
      #{isEnable,jdbcType=BOOLEAN},
      #{validUserid,jdbcType=INTEGER},
      #{validStatus,jdbcType=BOOLEAN},
      #{validTime,jdbcType=BIGINT},
      #{validComments,jdbcType=VARCHAR},
      #{invoicePrintStatus,jdbcType=BOOLEAN},
      #{invoiceCancelStatus,jdbcType=BOOLEAN},
      #{expressId,jdbcType=INTEGER},
      #{isAuth,jdbcType=BOOLEAN},
      #{isMonthAuth,jdbcType=BOOLEAN},
      #{authTime,jdbcType=BIGINT},
      #{addTime,jdbcType=BIGINT},
      #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=BIGINT},
      #{updater,jdbcType=INTEGER},
      #{invoiceApplyId,jdbcType=INTEGER},
      #{sendTime,jdbcType=TIMESTAMP},
      #{afterExpressId,jdbcType=INTEGER},
      #{ossFileUrl,jdbcType=VARCHAR},
      #{resourceId,jdbcType=VARCHAR},
      #{invoiceFrom,jdbcType=TINYINT},
      #{hxInvoiceId,jdbcType=INTEGER},
      #{authMode,jdbcType=BOOLEAN},
      #{authFailReason,jdbcType=VARCHAR},
      #{isAuthing,jdbcType=BOOLEAN},
      #{colorComplementType,jdbcType=BOOLEAN},
      #{isAfterBuyorderOnly,jdbcType=BOOLEAN},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="invoiceId != null">
        INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      </if>
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      INVOICE_FLOW = #{invoiceFlow,jdbcType=VARCHAR},
      INVOICE_PROPERTY = #{invoiceProperty,jdbcType=BOOLEAN},
      INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR},
      `TYPE` = #{type,jdbcType=INTEGER},
      TAG = #{tag,jdbcType=BOOLEAN},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      RATIO = #{ratio,jdbcType=DECIMAL},
      COLOR_TYPE = #{colorType,jdbcType=BOOLEAN},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      IS_ENABLE = #{isEnable,jdbcType=BOOLEAN},
      VALID_USERID = #{validUserid,jdbcType=INTEGER},
      VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
      VALID_TIME = #{validTime,jdbcType=BIGINT},
      VALID_COMMENTS = #{validComments,jdbcType=VARCHAR},
      INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=BOOLEAN},
      INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=BOOLEAN},
      EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      IS_AUTH = #{isAuth,jdbcType=BOOLEAN},
      IS_MONTH_AUTH = #{isMonthAuth,jdbcType=BOOLEAN},
      AUTH_TIME = #{authTime,jdbcType=BIGINT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
      SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
      AFTER_EXPRESS_ID = #{afterExpressId,jdbcType=INTEGER},
      OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR},
      RESOURCE_ID = #{resourceId,jdbcType=VARCHAR},
      INVOICE_FROM = #{invoiceFrom,jdbcType=TINYINT},
      HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER},
      AUTH_MODE = #{authMode,jdbcType=BOOLEAN},
      AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR},
      IS_AUTHING = #{isAuthing,jdbcType=BOOLEAN},
      COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=BOOLEAN},
      IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly,jdbcType=BOOLEAN},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="INVOICE_ID" keyProperty="invoiceId" parameterType="com.vedeng.erp.mobile.domain.Invoice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        INVOICE_ID,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="invoiceCode != null and invoiceCode != ''">
        INVOICE_CODE,
      </if>
      <if test="invoiceFlow != null and invoiceFlow != ''">
        INVOICE_FLOW,
      </if>
      <if test="invoiceProperty != null">
        INVOICE_PROPERTY,
      </if>
      <if test="invoiceHref != null and invoiceHref != ''">
        INVOICE_HREF,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="tag != null">
        TAG,
      </if>
      <if test="relatedId != null">
        RELATED_ID,
      </if>
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="invoiceNo != null and invoiceNo != ''">
        INVOICE_NO,
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE,
      </if>
      <if test="ratio != null">
        RATIO,
      </if>
      <if test="colorType != null">
        COLOR_TYPE,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="validUserid != null">
        VALID_USERID,
      </if>
      <if test="validStatus != null">
        VALID_STATUS,
      </if>
      <if test="validTime != null">
        VALID_TIME,
      </if>
      <if test="validComments != null and validComments != ''">
        VALID_COMMENTS,
      </if>
      <if test="invoicePrintStatus != null">
        INVOICE_PRINT_STATUS,
      </if>
      <if test="invoiceCancelStatus != null">
        INVOICE_CANCEL_STATUS,
      </if>
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      <if test="isAuth != null">
        IS_AUTH,
      </if>
      <if test="isMonthAuth != null">
        IS_MONTH_AUTH,
      </if>
      <if test="authTime != null">
        AUTH_TIME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="invoiceApplyId != null">
        INVOICE_APPLY_ID,
      </if>
      <if test="sendTime != null">
        SEND_TIME,
      </if>
      <if test="afterExpressId != null">
        AFTER_EXPRESS_ID,
      </if>
      <if test="ossFileUrl != null and ossFileUrl != ''">
        OSS_FILE_URL,
      </if>
      <if test="resourceId != null and resourceId != ''">
        RESOURCE_ID,
      </if>
      <if test="invoiceFrom != null">
        INVOICE_FROM,
      </if>
      <if test="hxInvoiceId != null">
        HX_INVOICE_ID,
      </if>
      <if test="authMode != null">
        AUTH_MODE,
      </if>
      <if test="authFailReason != null and authFailReason != ''">
        AUTH_FAIL_REASON,
      </if>
      <if test="isAuthing != null">
        IS_AUTHING,
      </if>
      <if test="colorComplementType != null">
        COLOR_COMPLEMENT_TYPE,
      </if>
      <if test="isAfterBuyorderOnly != null">
        IS_AFTER_BUYORDER_ONLY,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceId != null">
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="invoiceCode != null and invoiceCode != ''">
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceFlow != null and invoiceFlow != ''">
        #{invoiceFlow,jdbcType=VARCHAR},
      </if>
      <if test="invoiceProperty != null">
        #{invoiceProperty,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceHref != null and invoiceHref != ''">
        #{invoiceHref,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="tag != null">
        #{tag,jdbcType=BOOLEAN},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="invoiceNo != null and invoiceNo != ''">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="ratio != null">
        #{ratio,jdbcType=DECIMAL},
      </if>
      <if test="colorType != null">
        #{colorType,jdbcType=BOOLEAN},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=BOOLEAN},
      </if>
      <if test="validUserid != null">
        #{validUserid,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        #{validStatus,jdbcType=BOOLEAN},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=BIGINT},
      </if>
      <if test="validComments != null and validComments != ''">
        #{validComments,jdbcType=VARCHAR},
      </if>
      <if test="invoicePrintStatus != null">
        #{invoicePrintStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceCancelStatus != null">
        #{invoiceCancelStatus,jdbcType=BOOLEAN},
      </if>
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      <if test="isAuth != null">
        #{isAuth,jdbcType=BOOLEAN},
      </if>
      <if test="isMonthAuth != null">
        #{isMonthAuth,jdbcType=BOOLEAN},
      </if>
      <if test="authTime != null">
        #{authTime,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="invoiceApplyId != null">
        #{invoiceApplyId,jdbcType=INTEGER},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="afterExpressId != null">
        #{afterExpressId,jdbcType=INTEGER},
      </if>
      <if test="ossFileUrl != null and ossFileUrl != ''">
        #{ossFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null and resourceId != ''">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="invoiceFrom != null">
        #{invoiceFrom,jdbcType=TINYINT},
      </if>
      <if test="hxInvoiceId != null">
        #{hxInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="authMode != null">
        #{authMode,jdbcType=BOOLEAN},
      </if>
      <if test="authFailReason != null and authFailReason != ''">
        #{authFailReason,jdbcType=VARCHAR},
      </if>
      <if test="isAuthing != null">
        #{isAuthing,jdbcType=BOOLEAN},
      </if>
      <if test="colorComplementType != null">
        #{colorComplementType,jdbcType=BOOLEAN},
      </if>
      <if test="isAfterBuyorderOnly != null">
        #{isAfterBuyorderOnly,jdbcType=BOOLEAN},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="invoiceId != null">
        INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="invoiceCode != null and invoiceCode != ''">
        INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceFlow != null and invoiceFlow != ''">
        INVOICE_FLOW = #{invoiceFlow,jdbcType=VARCHAR},
      </if>
      <if test="invoiceProperty != null">
        INVOICE_PROPERTY = #{invoiceProperty,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceHref != null and invoiceHref != ''">
        INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=INTEGER},
      </if>
      <if test="tag != null">
        TAG = #{tag,jdbcType=BOOLEAN},
      </if>
      <if test="relatedId != null">
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="invoiceNo != null and invoiceNo != ''">
        INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="ratio != null">
        RATIO = #{ratio,jdbcType=DECIMAL},
      </if>
      <if test="colorType != null">
        COLOR_TYPE = #{colorType,jdbcType=BOOLEAN},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=BOOLEAN},
      </if>
      <if test="validUserid != null">
        VALID_USERID = #{validUserid,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
      </if>
      <if test="validTime != null">
        VALID_TIME = #{validTime,jdbcType=BIGINT},
      </if>
      <if test="validComments != null and validComments != ''">
        VALID_COMMENTS = #{validComments,jdbcType=VARCHAR},
      </if>
      <if test="invoicePrintStatus != null">
        INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceCancelStatus != null">
        INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=BOOLEAN},
      </if>
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      <if test="isAuth != null">
        IS_AUTH = #{isAuth,jdbcType=BOOLEAN},
      </if>
      <if test="isMonthAuth != null">
        IS_MONTH_AUTH = #{isMonthAuth,jdbcType=BOOLEAN},
      </if>
      <if test="authTime != null">
        AUTH_TIME = #{authTime,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="invoiceApplyId != null">
        INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
      </if>
      <if test="sendTime != null">
        SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="afterExpressId != null">
        AFTER_EXPRESS_ID = #{afterExpressId,jdbcType=INTEGER},
      </if>
      <if test="ossFileUrl != null and ossFileUrl != ''">
        OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null and resourceId != ''">
        RESOURCE_ID = #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="invoiceFrom != null">
        INVOICE_FROM = #{invoiceFrom,jdbcType=TINYINT},
      </if>
      <if test="hxInvoiceId != null">
        HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="authMode != null">
        AUTH_MODE = #{authMode,jdbcType=BOOLEAN},
      </if>
      <if test="authFailReason != null and authFailReason != ''">
        AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR},
      </if>
      <if test="isAuthing != null">
        IS_AUTHING = #{isAuthing,jdbcType=BOOLEAN},
      </if>
      <if test="colorComplementType != null">
        COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=BOOLEAN},
      </if>
      <if test="isAfterBuyorderOnly != null">
        IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>

  <resultMap id="invoiceDtoResult" type="com.vedeng.erp.mobile.dto.SaleOrderInvoiceInfoDto" extends="BaseResultMap">
  </resultMap>
  
<!--auto generated by MybatisCodeHelper on 2023-09-14-->
  <select id="getByRelatedIdAndType" resultMap="invoiceDtoResult">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE
        where `TYPE`=#{type,jdbcType=INTEGER}
        and RELATED_ID in
        <foreach collection="relatedIds" item="relatedId" open="(" separator="," close=")">
            #{relatedId,jdbcType=INTEGER}
        </foreach>
    </select>
</mapper>