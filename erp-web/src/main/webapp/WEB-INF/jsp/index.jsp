<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE>
<html>
<head>
<meta charset="UTF-8">
<title id="test">贝登ERP管理系统</title>
	<link data-n-head="ssr" rel="icon" type="image/x-icon" href="${pageContext.request.contextPath}/static/favicon.ico">
<link rel="stylesheet"
	href="${pageContext.request.contextPath}/static/bootstrap/css/bootstrap.min.css">
<link rel="stylesheet"
	href="${pageContext.request.contextPath}/static/css/content.css">
<link rel="stylesheet"
	href="${pageContext.request.contextPath}/static/css/general.css?rnd=${resourceVersionKey}">
<link rel="stylesheet"
	href="${pageContext.request.contextPath}/static/css/manage.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/front-page.css">
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/jquery.min.js"></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/closable-tab.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/main_page.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/index.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/mylayer.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/bootstrap/js/bootstrap.min.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/jquery-min-draggable.js"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/call/call.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/superSlide.2.1.1.js?rnd=${resourceVersionKey}" type="text/javascript"></script>
<script src="${pageContext.request.contextPath}/static/js/sales_juhe.js?rnd=${resourceVersionKey}" type="text/javascript"></script>
<script type='text/javascript'>
	if (top.location != self.location)
	{
		top.location=self.location;
	}
	//检测登录状态，给弹出框页面使用//add by john
	function checkLogin(){
		$.ajax({
			url: '/checkSession.do',
			dataType: "json",
			type:"POST",
			async: false,
			success: function (data) {
				if (data.code==0) {
				} else {
					layer.confirm("登录超时，请重新登录", {
						btn : [ '关闭' ]
					}, function() {
						top.location.href=page_url+'/login.do';
					});
				}
			}
		});
	}


</script>
	<style>
		.row .title{
			position: fixed;
			top: 0;
			left: 145px;
			width: calc(100% - 145px);
			z-index: 1111;
		}
		.row .tab-content {
			position: relative;
			width: calc(100% - 145px) !important;
			height: 832px;
			top: 35px;
			position: fixed;
			left: 145px;
		}

		.tab-content>.tab-pane {
			display: block;
			opacity: 0;
			z-index: 0;
			position: absolute;
			left: 0;
			top: 0;
			height: 100%;
		}

		.tab-content>.active {
			opacity: 1;
			z-index: 1;
		}

		.ai-fixed {
			width: 0;
			height: 0;
			background: #09f;
			position: absolute;
			z-index: 99999;
			background: url("${pageContext.request.contextPath}/static/images/ai-zhushou.gif");
			background-size: 100% 100%;
			border-radius: 50%;
			cursor: pointer;
			top: 10px;
			right: 40px;
			overflow: hidden;
		}

		.ai-fixed .glyphicon {
			cursor: pointer;
			position: absolute;
			top: -5px;
			left: -5px;
		}

		.ai-fixed .glyphicon:hover {
			color: #f60;
		}

		.ai-fixed.fadein {
			width: 70px;
			height: 70px;
			right: 16px;
			overflow: visible;
			top: calc(50% - 40px);
		}

		.ai-fixed.animate {
			transition: all .5s ease;
		}

		.ai-fixed-text {
			width: 30px;
			height: 30px;
			background: url("${pageContext.request.contextPath}/static/images/ai-zhushou.gif");
			background-size: 100% 100%;
			border-radius: 50%;
			cursor: pointer;
			display: none;
		}

		.ai-fixed.beta::before {
			content: "";
			width: 38px;
			height: 16px;
			background: url("${pageContext.request.contextPath}/static/images/beta.png");
			background-size: 100% 100%;
			position: absolute;
			right: 0;
			top: 0;
		}

		.ai-iframe-wrap {
			position: fixed;
			top: 50px;
			right: 40px;
			width: 600px;
			height: 85vh;
			max-height: 600px;
			min-height: 400px;
			z-index: 11111;
			box-shadow: 0 3px 10px rgba(0,0,0,.1)
		}
		
		.ai-iframe-wrap iframe {
			height: 100%;
			width: 100%;
		}

		.ai-iframe-wrap .glyphicon {
			cursor: pointer;
			position: absolute;
			font-size: 18px;
			color: #666;
			top: -5px;
			right: -5px;
		}

		.ai-iframe-wrap .glyphicon:hover {
			color: #f60;
		}

	</style>


</head>

<body>
	<div id="hidden-side-bar" class="hidden-side-bar side-actived">
		<i class='sideleft'></i>
	</div>
	<div class="window_change">
		<div id="aiTipsForVoice" style="display:none;position: absolute;right: 30px;width: 200px;height:80px;top: 42px;z-index: 10003;border-radius: 5px;background: #e0f3ff;">
			<div style="
				position: absolute;
				border: 8px solid transparent;
				border-bottom-color: #ff9700;
				right: 10px;
				top: -16px;
			"></div>
			<div class="messageTip"> <i class="iconmessages" style="background:url('${pageContext.request.contextPath}/static/images/laba.gif') no-repeat;"></i> <span class="messageTip-title">语音助手</span>
				<i class="iconmessagecha" onclick="$('#aiTipsForVoice').hide();"></i></div>
			<span style="margin: 5px;font-size:12px;cursor: pointer" onclick="openLastVoice('${pageContext.request.contextPath}/ai/communicateInfo.do?selectedTab=assistant');$('#aiTipsForVoice').hide();">通话小助手已为您解析完最近通话并整理完待办事项</span>
			<a href="javascript:void(0);" onclick="openLastVoice('${pageContext.request.contextPath}/ai/communicateInfo.do?selectedTab=assistant');$('#aiTipsForVoice').hide();" style="color: #438DEF;position: absolute;right:5px;bottom: 5px;font-size: 12px;">查看</a>
		</div>
		<div id="positionForLoadCrm" style="display: none;width:1px;height:1px;">

		</div>
		<div class="ai-fixed beta J-ai-wrap">
			<span class="glyphicon glyphicon-remove J-ai-fadeout"></span>
		</div>
		<div class="side-bar-frameset-box left-box" style="position: relative">
			<iframe class="side-bar-frame "
				src="${pageContext.request.contextPath}/menu.do" id="side-bar-frame"
				name="side-bar-frame " noresize scrolling="no ">
				您的浏览器不支持框架，请升级浏览器以便正常访问 </iframe>
		</div>
		<!-- 注意：如果Callcenter存在， 那么把 " main-frameset-box " 里面的 ' right-box0 ' 删掉，如果不存在就要把 right-box0 加上去-->
		<div
			class="main-frameset-box right-box <c:if test="${empty user.userDetail.ccNumber }">right-box0</c:if>"
			style="overflow-y: hidden;">
			<div class="main-frame ">
				<div class="closable-tab container closable-tab-page pos_rel "
					id="container ">
					<div class="same t_left ">
						<i class="iconleft "></i>
					</div>
					<div class='same t_right'>
						<i class="iconright "></i>
					</div>
					<div class="row ">
						<!-- 此处是相关代码 -->
						<div class="title " id="title ">
							<ul class="nav nav-tabs " role="tablist ">
							</ul>
							<div style="float: right;height:35px;width: 350px;padding-right: 20px;display: flex;align-items: center">
								<c:if test="${crm ne null}">
									<div style="display:inline-block;height:30px;font-weight: bolder;line-height: 30px">
										<a target="_blank" href="${crm}?from=erp">
											<font color="#438DEF">客户中心</font>
										</a>
									</div>
								</c:if>
								<%--<c:if test="${ssoForCrmJumpUrl}">
									<iframe src="${ssoForCrmJumpUrl}" width="10px" height="10px"> </iframe>
								</c:if>--%>
								<div style="margin-left:20px;display:inline-block;flex:1;height:30px;font-weight: bolder;line-height: 30px">
									使用时遇到问题?
									<a target="_blank" href="http://faq.ivedeng.com/feedback/add?from=erp">
										<font color="#438DEF">点此反馈</font>
									</a>
								</div>
								<div class="ai-fixed-text J-ai-trigger" title="ERP贝壳助手"></div>
							</div>


						</div>
						<div class="tab-content " style="width: 100%; height: 620px"
							id="tab-content">
						</div>
						<!-- 相关代码结束 -->
					</div>
				</div>
			</div>
		</div>
		<c:if test="${not empty user.userDetail.ccNumber }">
			<div class="top-bar-frameset-box bottom-box"
				style="position: relative;">
				<iframe class="top-bar-frame " id="callcenter"
					src="${pageContext.request.contextPath}/system/call/index.do"
					name="top-bar-frame " scrolling="no " noresize>
					您的浏览器不支持框架，请升级浏览器以便正常访问 </iframe>
				<input type="hidden" id="call_traderId" value="0" /> <input
					type="hidden" id="call_traderType" value="0" /> <input
					type="hidden" id="call_callType" value="0" /> <input type="hidden"
					id="call_orderId" value="0" /> <input type="hidden"
					id="call_traderContactId" value="0" /> <input type="hidden"
					id='monitorAgent' value="" /> <input type="hidden" id='callFailedId'
					value="" /> <input type="hidden" id='call_screen'
					value="1" />
			</div>
			<div id="hidden-top-bar" class="hidden-top-bar1 ">
				<i class="bottomdown"></i>
			</div>
		</c:if>
	</div>
	<input type="hidden" id="userId" value="${ sessionScope.curr_user.userId}"/>
	<input type="hidden" id="userName" value="${ sessionScope.curr_user.username}"/>
	<input type="hidden" id="checkCurrentUserLoginState" value="1"/>
	<input type="hidden" id="positType" value="${ sessionScope.curr_user.positType}"/>
	<input type="hidden" id="websocketUrl" value="${ wsUrl}"/>
	<input type="hidden" id="imUrl" value="${ imUrl}"/>
	<div class="messageContainer-wrap">
		<div id="imMessageContainer" class="imMessageContainer" style="display: none;">
			<div class="messageTip" title="【您有新消息，请查看】">
                <i class="iconmessages"></i> <span class="messageTip-title">【您有新消息，请查看】</span> <i
                    class="iconmessagecha J-close-message"></i>
            </div>
		</div>
		<div id="messageContainer" class="messageContainer">

		</div>

		<div class="msg-always-wrap" style="display: none;position: static;">
			【商机管理】未读<span class="msg-always-num" id="msgNum"></span>条
		</div>
	</div>
	<c:if test="${count==0}">
		<input type="hidden" id="noticeId" value="${notice.noticeId}">
		<div class="version-attention">
            <div class="version-ct">
              <div class="version-box">
                 <div class="version-tit1">${notice.title}</div>
				 <div style="padding:10px 100px;">
				     ${notice.content}
				 </div>
			  </div>
              <div class="have-read"><span>我已阅读</span></div>
            </div>
        </div>
    </c:if>
	<c:if test="${sessionScope.curr_user.positType eq 310 && not empty list}">
		<div class="front-page-slide" <c:if test='${count==0}'>style="display: none;"</c:if>>
             <div class="index-page">
                <div class="index-slide-banner">
                    <div class="front-page-close"><i class="icon icon-page-close"></i></div>
                    <div class="bd">
                        <ul>
	                        <c:forEach items="${list}" var="ad">
	                            <li class="img-box" style=" float: left;">
	                                <div class="container">
	                                    <span>
	                                    	<img src="${ad.url}"/> 
	                                    </span>
	                                </div>
	                            </li>
                            </c:forEach>
                        </ul>
                    </div>
                     <div class="container hd-container">
                        <ul class="hd">
                        </ul>
                    </div>
                </div>
                <div class="clear">
                </div>
            </div>
        </div>
	</c:if>
	<form id="tongjiForm" target="_blank" method="post" action="http://*************:8080/webroot/decision/view/report?viewlet=%25E6%2595%25B0%25E5%25AD%2597%25E5%258C%2596%25E8%2590%25A5%25E9%2594%2580%25E9%2583%25A8%252F%25E6%25B2%259F%25E9%2580%259A%25E8%25AE%25B0%25E5%25BD%2595%25E6%25AF%258F%25E6%2597%25A5%25E6%258A%25A5%25E8%25A1%25A8-%25E8%25B4%259D%25E5%25A3%25B3%25E5%258A%25A9%25E7%2590%2586.cpt&ref_t=design&ref_c=c74722f3-77ac-4ccb-bb6d-8c55302ea1e9">
		<input type="hidden" name="userId" value="${user.userId}"/>
	</form>
</body>
<script type="text/javascript">
	var callIndex;//呼叫弹层面板
	var callAgent;//坐席列表弹层
	var callComm;//新增联系面板
	var callBussincessChance;//商机列表
	//关闭坐席列表
	function closeAgent() {
		layer.close(callAgent);
	}
	function closeComm() {
		layer.close(callComm);
	}
	function closeBussincessChance() {
		layer.close(callBussincessChance);
	}

	function closeScreenAll() {
		layer.confirm("确定关闭弹屏？", {
			btn : [ '确定', '取消' ]
		//按钮
		}, function() {
			layer.closeAll();
		}, function() {
		});
	}

    function hideMsg() {
        $('.side-bar-frameset-box').css('z-index', 10000);
    }

    function showMsg() {
        $('.side-bar-frameset-box').css('z-index', 1);
    }
	function bindEvent(element, eventName, eventHandler) {
		if (element.addEventListener){
			element.addEventListener(eventName, eventHandler, false);
		} else if (element.attachEvent) {
			element.attachEvent('on' + eventName, eventHandler);
		}
	}
	// Listen to message from child window
	bindEvent(window, 'message', function (e) {
		if(e.data.pageType == 'ai'){ //AI助手来源
			if(e.data.name == 'openLastVoice'){
				openLastVoice(e.data.url);//ai助手，打开小助手页面
				return;
			}
			if(e.data.name == 'tongjiFormClick'){
				$('#tongjiForm').submit();//ai助手，打开大数据的通话报表页面
				return;
			}
		}
		if(e.data.pageType == 'crm'){ //CRM
			/**
			 * 呼出电话前数据处理
			 * @param phone 被叫号码
			 * @param traderId 交易者id
			 * @param traderType 交易者类型 1客户2供应商
			 * @param callType 呼出订单类型 7线索1商机2销售订单3报价4售后5采购订单//没有就传 0
			 * @param orderId 订单id 没有就传 0
			 * @param traderContactId 联系人ID 没有就传 0
			 * @returns
			 */
			if(e.data.name == 'call'){//phone,traderId,traderType,callType,orderId,traderContactId
				//callout('18651669603',5842,1,2,206319,7993);
				callout(e.data.phone,e.data.traderId,e.data.traderType,e.data.callType,e.data.orderId,e.data.traderContactId);
				return;
			}
			if(e.data.name == 'host'){
				// 回传消息到子页面
				e.source.postMessage({
					type: 'response',
					requestId: e.data.requestId || '',
					host: window.location.host,
				}, e.origin);
				return;
			}

		}
		var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random()*1000);
		if(e.data.id!=undefined&&e.data.id!=''){
			var uniqueName = e.data.id.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
			id=uniqueName;
		}
		var item = {
			'id': id,
			'name': e.data.name,
			'url': e.data.url, 'closable': true
		};

		if('olderp'==e.data.from){
			return;
		}
		if(!e.data.url){//解决豆包触发message方法，一直打开http://erp.ivedeng.com/undefined页面
			return ;
		}
		closableTab.addTab(item);
		closableTab.resizeMove();
	});
	// $(window).bind('beforeunload',function(){
	// 	$.get("/call/static/beforeunload.do")
	// 	// 加一段同步代码阻塞一下，不然刷新会发不出去异步请求
	// 	let now = new Date()
	// 	while (new Date() - now < 100) { }
	// 	return '您输入的内容尚未保存，确定离开此页面吗？';
	// });

	function checkCurrentUserLoginState() {
		if($("#checkCurrentUserLoginState").val() == 0){
			return ;
		}
		// 这里是你想要执行的代码
		//console.log('执行了 myFunction');
		$.ajax({
			type: "GET",
			url: "/checkSession.do?t=checkCurrentUserLoginState",
			dataType: 'json',
			success: function (data) {
				var checkCurrentUserLoginState = true;
				if (data.code == 0 && data.data.userId == $("#userId").val()) {
					checkCurrentUserLoginState = true;
				} else {
					checkCurrentUserLoginState = false;
				}
				if(!checkCurrentUserLoginState){
					$("#checkCurrentUserLoginState").val(0);
					var message = '当前登录信息已失效，请重新登录再试。';
					layer.alert(message, {
						title: '提示',
						closeBtn: 0,
						icon: 0,
						btn: ['确认'],
						yes: function() {
							// 点击确认按钮后的回调函数
							location.reload(); // 刷新当前浏览器地址
						}
					});
					$('.layui-layer-shade').css({height: '100%'});
				}
			},
			error: function (data) {
				if (data.status == 1001) {
					//checkCurrentUserLoginState = false;
					$("#checkCurrentUserLoginState").val(0);
					layer.alert('当前登录信息已失效，请重新登录再试。', {
						title: '提示',
						closeBtn: 0,
						icon: 0,
						btn: ['确认'],
						yes: function() {
							// 点击确认按钮后的回调函数
							location.reload(); // 刷新当前浏览器地址
						}
					});
					$('.layui-layer-shade').css({height: '100%'});
				}
			}
		})
	}

	$(document).ready(function (){
		if (navigator.userAgent.toLowerCase().indexOf('chrome') < 0){
			layer.confirm("请使用Chrome最新浏览器访问ERP系统！", {
				icon: 7,
				title: '提示',
				btn : [ '点击下载', '知道了' ]
				//按钮
			}, function() {
				window.open('https://www.google.cn/chrome/')
				layer.closeAll();
			}, function() {
				layer.closeAll();
			});
		}

		//setInterval(checkCurrentUserLoginState, 5000); // 每5秒执行一次 检查登录用户状态


		var userIds = '${aiUserIds}';
		var userId = '${sessionScope.curr_user.userId}';

		if( (userIds && userId && userIds.split(',').indexOf(userId.toString()) !== -1 ) || userIds =='ALL'){
			//ai图标
			$('.J-ai-fadeout').click(function (e) {
				e.stopPropagation();
				$('.J-ai-wrap').removeClass('fadein');;
				localStorage.setItem('ai-icon-hide', 1)
				setTimeout(function () {
					$('.J-ai-trigger').fadeIn();
				}, 500)
			});

			$('.J-ai-wrap').click(function () {
				$(this).fadeOut();

				if($('.J-ai-iframe-wrap').length){
					$('.J-ai-iframe-wrap').fadeIn();
				} else {
					var width=$(window).width();
					var height=$(window).height();

					$('body').append(`<div class="ai-iframe-wrap J-ai-iframe-wrap">
						<span class="glyphicon glyphicon-remove-circle J-ai-iframe-close"></span>
						<iframe id="aiWindowIframe" src="${pageContext.request.contextPath}/jumpAi.do?w=` + width + `&h=` + height + `"></iframe>
					</div>`);
				}
			});

			$(document).on('click', '.J-ai-iframe-close', function () {
				$('.J-ai-wrap').fadeIn();

				$('.J-ai-iframe-wrap').fadeOut();
			})

			$('.J-ai-trigger').click(function () {
				$('.J-ai-wrap').addClass('fadein');
				$('.J-ai-trigger').fadeOut();
				localStorage.removeItem('ai-icon-hide')
			})

			if(localStorage.getItem('ai-icon-hide') != 1){
				$('.J-ai-wrap').addClass('fadein');
			} else {
				$('.J-ai-trigger').fadeIn();
			}

			setTimeout(function () {
				$('.J-ai-wrap').addClass('animate');
			}, 500)
		}
		var targetUrl = "${param.target}";
		var ct = "${param.ct}";
		var targetTitle = "${param.title}";
		var targetTitleValue = null;
		if(targetUrl && targetUrl.length>0){
			if(ct && ct.length >0 ){ //优先取ct
				var titleJson = '${titleJson}';//[{'code':'1','value':'任务列表'},{'code':'2','value':'线索列表'}]
				//定义一个值，解析titleJson这个对象，按code取到targetTitle为key对应的值
				var targetTitleValue = JSON.parse(titleJson).filter(function (item) {
					return item.code == ct;
				})[0]?.value;
			}
			targetTitleValue  = targetTitleValue || targetTitle;

			var uniqueName = targetUrl.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
			setTimeout(function () {
				window.postMessage({
					from:'jump',
					name: targetTitleValue.length>0?targetTitleValue:"打开",
					url:targetUrl,
					id:"tab-"+uniqueName
				}, '*');
			},1000);
		}
	})

	function showAiHelpme(communicateRecordId) {
		var index = layer.myopen({
			type: 2,
			shadeClose: false, //点击遮罩关闭
			closeBtn: 1,
			//area: 'auto',
			area: ['820px', '530px'],
			title: "贝壳助理语音识别，录音ID："+communicateRecordId,
			content: '${pageContext.request.contextPath}/system/call/getrecordplayForAi.do?communicateRecordId='+communicateRecordId,
			success: function (layero, index) {
				//layer.iframeAuto(index);
			}
		});
	}

	function getAiMessage(searchWord){
		var timestamp = new Date().getTime();
		var randomNum = Math.floor(Math.random() * 1000);
		var result = String(timestamp) + String(randomNum);
		var iframe = document.getElementById('aiWindowIframe');
		var jsonString = '{\n' +
				'        "_id": "'+result+'",\n' +
				'        "type": "text",\n' +
				'        "content": {\n' +
				'            "text": "'+searchWord+'"\n' +
				'        },\n' +
				'        "position": "right"\n' +
				'    }';
		var jsonObj = JSON.parse(jsonString);
		return jsonObj;
	}
	var targetOrigin = "*";
	function openAiWindow(searchWord) {
		var aiFrame = document.getElementById('aiWindowIframe');
		if(aiFrame == null || aiFrame == undefined){//判断当前页面是否已经加载过。如果未加载，则需要加载后，再调用 postMessage。
			$(".J-ai-wrap").click();
			setTimeout(function () {
				aiFrame = document.getElementById('aiWindowIframe');
				aiFrame.contentWindow.postMessage(getAiMessage(searchWord),targetOrigin);
			}, 1000)
		}else{
			$(".J-ai-wrap").click();
			aiFrame.contentWindow.postMessage(getAiMessage(searchWord),targetOrigin);
		}
	}

	function openLastVoice(selectedTab) {
		var id = selectedTab.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
		console.log(id);
		var item = {
			'id': id,
			'name': '贝壳助理-通话小助手',
			'url': selectedTab,
			'closable': true
		};

		closableTab.addTab(item);
		closableTab.resizeMove();
		<%--var index2 = layer.myopen({--%>
		<%--	type: 2,--%>
		<%--	shadeClose: false, //点击遮罩关闭--%>
		<%--	closeBtn: 1,--%>
		<%--	area: ['70%', '80%'],--%>
		<%--	title: "贝壳助理语音识别",--%>
		<%--	content: '${pageContext.request.contextPath}/ai/communicateInfo.do?selectedTab='+selectedTab,--%>
		<%--	success: function (layero, index) {--%>
		<%--		//layer.iframeAuto(index);--%>
		<%--	}--%>
		<%--});--%>
	}
</script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/changecareerlayer.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
		//当页面加载完成时执行以下js
		$(function(){
			// 获取当前时间戳
			var timestamp = new Date().getTime();
			var lxcrmUrl = '${requestScope.lxcrmUrl}';
			if(lxcrmUrl && lxcrmUrl != ''){
				var script = document.createElement('script');
				script.type = 'text/javascript';
				script.src = lxcrmUrl+"/crm/other/profile/getContentForLoginLxcrm?t="+timestamp;
				// 获取目标 div 元素
				var targetDiv = document.getElementById('positionForLoadCrm');
				// 将 script 元素追加到目标 div 中
				targetDiv.appendChild(script);
			}
		});
</script>
</html>