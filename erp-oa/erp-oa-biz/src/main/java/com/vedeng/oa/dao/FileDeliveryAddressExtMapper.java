package com.vedeng.oa.dao;

import com.vedeng.oa.model.FileDeliveryAddress;

import java.util.List;
import org.springframework.stereotype.Repository;

/**
 * 文件寄送地址管理拓展
 * <AUTHOR>
 *
 */
@Repository
public interface FileDeliveryAddressExtMapper extends FileDeliveryAddressMapper{

	/**
	 * 批量新增
	 * @param fileDeliveryAddressList
	 * @return
	 */
	int batchInsert(List<FileDeliveryAddress> fileDeliveryAddressList);
    
}