package com.vedeng.infrastructure.kingdee.domain.command;

import cn.hutool.core.collection.ListUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 操作扩展Command
 * @date 2022/11/4 11:15
 */
public class OperateExtCommand extends BaseCommand {

    private final String Ids;

    private final String CreateOrgId;

    private final List<String> Numbers;


    public OperateExtCommand(String formId) {
        super(formId);
        Ids = "";
        CreateOrgId = "";
        Numbers = ListUtil.empty();
    }

    public OperateExtCommand(String formId, String ids, String createOrgId, List<String> numbers) {
        super(formId);
        Ids = ids;
        CreateOrgId = createOrgId;
        Numbers = numbers;
    }

    public String getIds() {
        return Ids;
    }

    public String getCreateOrgId() {
        return CreateOrgId;
    }


    public List<String> getNumbers() {
        return Numbers;
    }


}
