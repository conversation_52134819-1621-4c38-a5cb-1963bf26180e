package com.vedeng.system.model;

public class RegionGenerate {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_REGION.REGION_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private Integer regionId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_REGION.PARENT_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private Integer parentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_REGION.REGION_NAME
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private String regionName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_REGION.REGION_TYPE
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private Integer regionType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_REGION.AGENCY_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    private Integer agencyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_REGION.REGION_ID
     *
     * @return the value of T_REGION.REGION_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public Integer getRegionId() {
        return regionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_REGION.REGION_ID
     *
     * @param regionId the value for T_REGION.REGION_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_REGION.PARENT_ID
     *
     * @return the value of T_REGION.PARENT_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public Integer getParentId() {
        return parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_REGION.PARENT_ID
     *
     * @param parentId the value for T_REGION.PARENT_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_REGION.REGION_NAME
     *
     * @return the value of T_REGION.REGION_NAME
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public String getRegionName() {
        return regionName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_REGION.REGION_NAME
     *
     * @param regionName the value for T_REGION.REGION_NAME
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_REGION.REGION_TYPE
     *
     * @return the value of T_REGION.REGION_TYPE
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public Integer getRegionType() {
        return regionType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_REGION.REGION_TYPE
     *
     * @param regionType the value for T_REGION.REGION_TYPE
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setRegionType(Integer regionType) {
        this.regionType = regionType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_REGION.AGENCY_ID
     *
     * @return the value of T_REGION.AGENCY_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public Integer getAgencyId() {
        return agencyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_REGION.AGENCY_ID
     *
     * @param agencyId the value for T_REGION.AGENCY_ID
     *
     * @mbg.generated Mon Apr 22 20:59:19 CST 2019
     */
    public void setAgencyId(Integer agencyId) {
        this.agencyId = agencyId;
    }
}