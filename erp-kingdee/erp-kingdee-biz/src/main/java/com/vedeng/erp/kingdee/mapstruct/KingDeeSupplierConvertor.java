package com.vedeng.erp.kingdee.mapstruct;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierBankDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import org.mapstruct.*;

import java.util.List;

/**
 * dto 转 entity
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeSupplierConvertor extends BaseMapStruct<KingDeeSupplierEntity, KingDeeSupplierDto> {


    @Override
    @Mapping(target = "FNumber", source = "FNumber")
    @Mapping(target = "FSupplierId", source = "FSupplierId")
    @Mapping(target = "FCreateOrgId", source = "FCreateOrgId")
    @Mapping(target = "FUseOrgId", source = "FUseOrgId")
    @Mapping(target = "FName", source = "FName")
    @Mapping(target = "FBaseInfo", source = "FBaseInfo")
    @Mapping(target = "fsupplierclassify", source = "FSupplierClassify")
    @Mapping(target = "fbankinfo", source = "FBankInfo", qualifiedByName = "entryListToStr")
    KingDeeSupplierEntity toEntity(KingDeeSupplierDto dto);


    /**
     * dto 原对象中 转 Str
     *
     * @param source 对象
     * @return Str
     */
    @Named("entryListToStr")
    default String entryListToStr(List<KingDeeSupplierBankDto> source) {
        if (source == null) {
            return null;
        }
        return JSON.toJSONString(source);
    }
}
