package com.wms.dao;

import com.vedeng.erp.wms.dto.WmsSampleOrderGoodsExtra;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WmsSampleOrderGoodsExtraMapper {
    int deleteByPrimaryKey(Long sampleOrderGoodsExtraId);

    int insert(WmsSampleOrderGoodsExtra record);

    int insertSelective(WmsSampleOrderGoodsExtra record);

    WmsSampleOrderGoodsExtra selectByPrimaryKey(Long sampleOrderGoodsExtraId);

    int updateByPrimaryKeySelective(WmsSampleOrderGoodsExtra record);

    int updateByPrimaryKey(WmsSampleOrderGoodsExtra record);

    WmsSampleOrderGoodsExtra selectByWmsOutputOrderGoodsId(@Param("wmsOutputOrderGoodsId")Long wmsOutputOrderGoodsId);

}