package com.vedeng.common.core.base;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 异常基类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseException extends RuntimeException {

    private static final long serialVersionUID = -5953362580741979713L;

    private final Integer code;
    private final String message;
    private Object data;

    public BaseException(BaseResponseCode code) {
        super(code.getMsg());
        this.code = code.getCode();
        this.message = code.getMsg();
    }

    public BaseException(String message) {
        super(message);
        this.code = BaseResponseCode.OPERATION_ERROR.getCode();
        this.message = message;
    }

    public BaseException(BaseResponseCode status, Object data) {
        this(status);
        this.data = data;
    }

    public BaseException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public BaseException(Integer code, String message, Object data) {
        this(code, message);
        this.data = data;
    }

    public BaseException(String message,Throwable cause) {
        super(message,cause);
        this.code = BaseResponseCode.OPERATION_ERROR.getCode();
        this.message = message;
    }
}
