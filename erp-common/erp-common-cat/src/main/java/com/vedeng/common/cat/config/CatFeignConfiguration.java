package com.vedeng.common.cat.config;

import com.dianping.cat.Cat;
import com.vedeng.common.cat.constants.CatConstantsExt;
import feign.RequestInterceptor;
import feign.RequestTemplate;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2024-08-23
 * @Description: TODO
 * @Version: 1.0
 */
public class CatFeignConfiguration implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
            CatContextImpl catContext = new CatContextImpl();
            Cat.logRemoteCallClient(catContext, Cat.getManager().getDomain());
            requestTemplate.header(CatConstantsExt.CAT_HTTP_HEADER_ROOT_MESSAGE_ID, catContext.getProperty(Cat.Context.ROOT));
            requestTemplate.header(CatConstantsExt.CAT_HTTP_HEADER_PARENT_MESSAGE_ID, catContext.getProperty(Cat.Context.PARENT));
            requestTemplate.header(CatConstantsExt.CAT_HTTP_HEADER_CHILD_MESSAGE_ID, catContext.getProperty(Cat.Context.CHILD));
    }
}