package com.vedeng.erp.kingdee.batch.writer;

import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.BatchReceiveFeeDto;
import com.vedeng.erp.kingdee.dto.OutPutFeePlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.OutPutFeeSpecialInvoiceDto;
import com.vedeng.erp.kingdee.service.KingDeeOutPutFeePlainInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeeOutPutFeeSpecialInvoiceApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2023−02-02 下午1:43
 * @description
 */
@Service
@Slf4j
public class AfterSaleOutPutFeeInvoiceWriter extends BaseWriter<BatchReceiveFeeDto> {

    @Autowired
    private KingDeeOutPutFeePlainInvoiceApiService kingDeeOutPutFeePlainInvoiceApiService;
    @Autowired
    private KingDeeOutPutFeeSpecialInvoiceApiService kingDeeOutPutFeeSpecialInvoiceApiService;

    @Override
    public void doWrite(BatchReceiveFeeDto batchReceiveFeeDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        //销项费用普票 plainDto
        OutPutFeePlainInvoiceDto plainDto = batchReceiveFeeDto.getOutPutFeePlainInvoiceDto();
        if (null != plainDto){
            plainDto.setKingDeeBizEnums(KingDeeBizEnums.saveExpensePlainInvoice);
            kingDeeOutPutFeePlainInvoiceApiService.register(plainDto,true);
        }
        //销项费用专票 specialDto
        OutPutFeeSpecialInvoiceDto specialDto = batchReceiveFeeDto.getOutPutFeeSpecialInvoiceDto();
        if (null != specialDto){
            specialDto.setKingDeeBizEnums(KingDeeBizEnums.saveExpenseSpecialInvoice);
            kingDeeOutPutFeeSpecialInvoiceApiService.register(specialDto,true);
        }
    }
}
