<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCommunicateFeedbackMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderCommunicateFeedbackEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_COMMUNICATE_FEEDBACK-->
    <id column="TRADER_COMMUNICATE_FEEDBACK_ID" jdbcType="BIGINT" property="traderCommunicateFeedbackId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="FEEDBACK" jdbcType="VARCHAR" property="feedback" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_COMMUNICATE_FEEDBACK_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, 
    UPDATER_NAME, TRADER_CUSTOMER_ID, TRADER_ID, FEEDBACK
  </sql>
  <insert id="insertSelective" keyColumn="TRADER_COMMUNICATE_FEEDBACK_ID" keyProperty="traderCommunicateFeedbackId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCommunicateFeedbackEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_COMMUNICATE_FEEDBACK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="feedback != null and feedback != ''">
        FEEDBACK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderCustomerId != null">
        #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="feedback != null and feedback != ''">
        #{feedback,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>