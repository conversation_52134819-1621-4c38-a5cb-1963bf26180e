<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="新增关联商品" scope="application"/>
<%@ include file="../../common/common.jsp" %>

<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/supplier/add_related_sku.js?rnd=${resourceVersionKey}"></script>
<div class="formpublic">
    <form action="${pageContext.request.contextPath}/trader/tradersupplier/addRelatedSku.do" id="addRelatedSku"
          method="post">
        <input type="hidden" name="skuSupplyAuthId" value="${skuSupplyAuthId}">
        <input type="hidden" name="userId" value="${userId}">
        <input type="hidden" name="isInit" id="isInit" value="0">
        <div>
            <span>
                <ul>
                    <li>
                        <span>
                            <label class="infor_name">产品名称</label>
                            <input type="text" class="input-middle" name="skuName" id="skuName" placeholder="请输入产品名称"
                                   value="${relatedSkuQuery.skuName}">
                        </span>
                        <span>
                            <label class="infor_name">订货号</label>
                            <input type="text" class="input-middle" name="skuNo" id="skuNo" placeholder="请输入订货号"
                                   value="${relatedSkuQuery.skuNo}">
                        </span>
                    </li>
                    <li>
                        <span>
                            <label class="infor_name">品牌</label>
                            <input type="text" class="input-middle" name="brandName" id="brandName" placeholder="请输入品牌"
                                   value="${relatedSkuQuery.brandName}">
                        </span>
                        <span>
                            <label class="infor_name">规格/型号</label>
                            <input type="text" class="input-middle" name="specModel" id="specModel" placeholder="请输入规格/型号"
                                   value="${relatedSkuQuery.specModel}">
                        </span>
                    </li>
                </ul>
            </span>
            <span>
                <span class="bg-light-blue bt-bg-style bt-small" id="search">搜索</span>
            </span>
        </div>
    </form>

    <div>
        <table class="table table-bordered table-striped table-condensed table-centered" id="cus">
            <thead>
            <tr>
                <th class="table-smallest8">
                    <input type="checkbox" name="checkAllSku" onclick="selectAll(this)"/>
                </th>
                <th class="wid5">订货号</th>
                <th class="wid13">产品名称</th>
                <th class="wid8">品牌</th>
                <th class="wid8">规格/型号</th>
            </tr>
            </thead>
            <tbody class="employeestate">
            <c:if test="${not empty skuList}">
                <c:forEach items="${skuList}" var="sku" varStatus="status">
                    <tr>
                        <td>
                            <input type="checkbox" name="checkOne" value="${sku.skuId}">
                            <input type="hidden" value="${sku.spuType}">
                        </td>
                        <td>${sku.skuNo}</td>
                        <td>${sku.skuName}</td>
                        <td>${sku.brandName}</td>
                        <td><c:choose>
                                <c:when test="${sku.spuType eq 316 or sku.spuType eq 1008}">${sku.model}</c:when>
                                <c:otherwise>${sku.spec}</c:otherwise>
                            </c:choose></td>
                    </tr>
                </c:forEach>
            </c:if>
            </tbody>
        </table>
        <c:if test="${empty skuList}">
            <!-- 查询无结果弹出 -->
            <div class="noresult">请点击搜索按钮查询商品信息！</div>
        </c:if>
        <tags:page page="${page}"/>
    </div>
</div>

<div class="add-tijiao" style="margin-top: 50px">
    <button type="button" class="bg-light-blue" onclick="saveRelatedSku()">提交</button>
</div>
