package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.SerialNumberTraceDto;
import com.vedeng.erp.kingdee.batch.processor.SerialNumberTraceProcessor;
import com.vedeng.erp.kingdee.batch.writer.SerialNumberTraceWriter;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockUpdateDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2023-06-28
 * @Description: 换货安调sn更新金蝶job
 * @Version: 1.0
 */
@Slf4j
@Configuration
public class SerialNumberTraceBatchJob extends BaseJob {
    @Autowired
    private SerialNumberTraceWriter serialNumberTraceWriter;
    @Autowired
    private SerialNumberTraceProcessor serialNumberTraceProcessor;

    public Job serialNumberTraceSnBatchJob() {
        return jobBuilderFactory.get("serialNumberTraceBatchJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(serialNumberTraceSnStep())
                .build();
    }

    private Step serialNumberTraceSnStep() {
        return stepBuilderFactory.get("换货安调sn更新销售出库单")
                .<SerialNumberTraceDto, KingDeeSaleOutStockUpdateDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(serialNumberTraceReader(null, null))
                .processor(serialNumberTraceProcessor)
                .writer(serialNumberTraceWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<SerialNumberTraceDto> serialNumberTraceReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        SerialNumberTraceDto serialNumberTraceDto = SerialNumberTraceDto.builder()
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, SerialNumberTraceDto.class.getSimpleName(), serialNumberTraceDto);
    }
}
