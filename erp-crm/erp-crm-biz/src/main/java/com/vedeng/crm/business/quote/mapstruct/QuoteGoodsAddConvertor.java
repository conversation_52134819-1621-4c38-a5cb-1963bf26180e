package com.vedeng.crm.business.quote.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.crm.business.quote.domain.dto.CrmCoreSkuInfoDto;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface QuoteGoodsAddConvertor extends BaseMapStruct<CrmCoreSkuInfoDto, CrmQuoteorderGoods> {

    @Mapping(target = "sku", source = "skuNo")
    @Mapping(target = "goodsName", source = "skuName")
    @Mapping(target = "model", source = "modelOrSpec")
    @Mapping(target = "deliveryCycle", source = "expectDeliveryTime")
    @Mapping(target = "price", source = "salePrice")
    @Mapping(target = "goodsId", source = "skuId")
    CrmQuoteorderGoods toDto(CrmCoreSkuInfoDto skuInfoDto);

}
