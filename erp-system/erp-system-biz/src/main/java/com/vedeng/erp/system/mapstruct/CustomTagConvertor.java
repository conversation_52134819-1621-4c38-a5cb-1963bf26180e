package com.vedeng.erp.system.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.system.dto.CustomTagDto;
import com.vedeng.erp.system.domain.entity.CustomTagEntity;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: dto entity转换类
 * @date 2022/7/9 13:21
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CustomTagConvertor extends BaseMapStruct<CustomTagEntity, CustomTagDto> {
}
