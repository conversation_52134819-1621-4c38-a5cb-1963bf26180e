package com.vedeng.api.standard.approval;

import java.util.List;
import java.util.Map;

/**
 * 审核结果封装
 * 包含审核执行的结果和详细信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-26
 */
public class ApprovalResult {
    
    private boolean success;
    private String message;
    private Object data;
    private List<ApprovalStepDetail> stepDetails;
    private int totalSteps;
    private boolean workflowCompleted;
    private String errorCode;
    private Map<String, Object> additionalInfo;
    
    public ApprovalResult() {
        this.success = true;
        this.workflowCompleted = false;
    }
    
    public ApprovalResult(boolean success, String message) {
        this();
        this.success = success;
        this.message = message;
    }
    
    public static ApprovalResult success(String message, Object data) {
        ApprovalResult result = new ApprovalResult(true, message);
        result.setData(data);
        return result;
    }
    
    public static ApprovalResult failure(String message, String errorCode) {
        ApprovalResult result = new ApprovalResult(false, message);
        result.setErrorCode(errorCode);
        return result;
    }
    
    // Getters and Setters
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    
    public Object getData() { return data; }
    public void setData(Object data) { this.data = data; }
    
    public List<ApprovalStepDetail> getStepDetails() { return stepDetails; }
    public void setStepDetails(List<ApprovalStepDetail> stepDetails) { this.stepDetails = stepDetails; }
    
    public int getTotalSteps() { return totalSteps; }
    public void setTotalSteps(int totalSteps) { this.totalSteps = totalSteps; }
    
    public boolean isWorkflowCompleted() { return workflowCompleted; }
    public void setWorkflowCompleted(boolean workflowCompleted) { this.workflowCompleted = workflowCompleted; }
    
    public String getErrorCode() { return errorCode; }
    public void setErrorCode(String errorCode) { this.errorCode = errorCode; }
    
    public Map<String, Object> getAdditionalInfo() { return additionalInfo; }
    public void setAdditionalInfo(Map<String, Object> additionalInfo) { this.additionalInfo = additionalInfo; }
    
    @Override
    public String toString() {
        return "ApprovalResult{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", stepDetails=" + stepDetails +
                ", totalSteps=" + totalSteps +
                ", workflowCompleted=" + workflowCompleted +
                ", errorCode='" + errorCode + '\'' +
                ", additionalInfo=" + additionalInfo +
                '}';
    }
}
