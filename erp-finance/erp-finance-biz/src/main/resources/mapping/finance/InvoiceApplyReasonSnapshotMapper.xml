<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.InvoiceApplyReasonSnapshotMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.InvoiceApplyReasonSnapshotEntity">
    <!--@mbg.generated-->
    <!--@Table T_INVOICE_APPLY_REASON_SNAPSHOT-->
    <id column="INVOICE_APPLY_REASON_ID" jdbcType="BIGINT" property="invoiceApplyReasonId" />
    <result column="INVOICE_APPLY_ID" jdbcType="INTEGER" property="invoiceApplyId" />
    <result column="RULE_CODE" jdbcType="VARCHAR" property="ruleCode" />
    <result column="RULE_NAME" jdbcType="VARCHAR" property="ruleName" />
    <result column="RULE_CONTENT" jdbcType="VARCHAR" property="ruleContent" />
    <result column="PROMPT_TEXT" jdbcType="VARCHAR" property="promptText" />
    <result column="APPLY_REASON" jdbcType="VARCHAR" property="applyReason" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    INVOICE_APPLY_REASON_ID, INVOICE_APPLY_ID, RULE_CODE, RULE_NAME, RULE_CONTENT, PROMPT_TEXT, 
    APPLY_REASON, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, 
    UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_INVOICE_APPLY_REASON_SNAPSHOT
    where INVOICE_APPLY_REASON_ID = #{invoiceApplyReasonId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_INVOICE_APPLY_REASON_SNAPSHOT
    where INVOICE_APPLY_REASON_ID = #{invoiceApplyReasonId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="INVOICE_APPLY_REASON_ID" keyProperty="invoiceApplyReasonId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyReasonSnapshotEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_APPLY_REASON_SNAPSHOT (INVOICE_APPLY_ID, RULE_CODE, RULE_NAME, 
      RULE_CONTENT, PROMPT_TEXT, APPLY_REASON, 
      IS_DELETE, ADD_TIME, MOD_TIME, 
      CREATOR, CREATOR_NAME, UPDATER, 
      UPDATER_NAME, UPDATE_REMARK)
    values (#{invoiceApplyId,jdbcType=INTEGER}, #{ruleCode,jdbcType=VARCHAR}, #{ruleName,jdbcType=VARCHAR}, 
      #{ruleContent,jdbcType=VARCHAR}, #{promptText,jdbcType=VARCHAR}, #{applyReason,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=BOOLEAN}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updater,jdbcType=INTEGER}, 
      #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="INVOICE_APPLY_REASON_ID" keyProperty="invoiceApplyReasonId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyReasonSnapshotEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_APPLY_REASON_SNAPSHOT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceApplyId != null">
        INVOICE_APPLY_ID,
      </if>
      <if test="ruleCode != null and ruleCode != ''">
        RULE_CODE,
      </if>
      <if test="ruleName != null and ruleName != ''">
        RULE_NAME,
      </if>
      <if test="ruleContent != null and ruleContent != ''">
        RULE_CONTENT,
      </if>
      <if test="promptText != null and promptText != ''">
        PROMPT_TEXT,
      </if>
      <if test="applyReason != null and applyReason != ''">
        APPLY_REASON,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="invoiceApplyId != null">
        #{invoiceApplyId,jdbcType=INTEGER},
      </if>
      <if test="ruleCode != null and ruleCode != ''">
        #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null and ruleName != ''">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleContent != null and ruleContent != ''">
        #{ruleContent,jdbcType=VARCHAR},
      </if>
      <if test="promptText != null and promptText != ''">
        #{promptText,jdbcType=VARCHAR},
      </if>
      <if test="applyReason != null and applyReason != ''">
        #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyReasonSnapshotEntity">
    <!--@mbg.generated-->
    update T_INVOICE_APPLY_REASON_SNAPSHOT
    <set>
      <if test="invoiceApplyId != null">
        INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
      </if>
      <if test="ruleCode != null and ruleCode != ''">
        RULE_CODE = #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null and ruleName != ''">
        RULE_NAME = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleContent != null and ruleContent != ''">
        RULE_CONTENT = #{ruleContent,jdbcType=VARCHAR},
      </if>
      <if test="promptText != null and promptText != ''">
        PROMPT_TEXT = #{promptText,jdbcType=VARCHAR},
      </if>
      <if test="applyReason != null and applyReason != ''">
        APPLY_REASON = #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where INVOICE_APPLY_REASON_ID = #{invoiceApplyReasonId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyReasonSnapshotEntity">
    <!--@mbg.generated-->
    update T_INVOICE_APPLY_REASON_SNAPSHOT
    set INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
      RULE_CODE = #{ruleCode,jdbcType=VARCHAR},
      RULE_NAME = #{ruleName,jdbcType=VARCHAR},
      RULE_CONTENT = #{ruleContent,jdbcType=VARCHAR},
      PROMPT_TEXT = #{promptText,jdbcType=VARCHAR},
      APPLY_REASON = #{applyReason,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where INVOICE_APPLY_REASON_ID = #{invoiceApplyReasonId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_INVOICE_APPLY_REASON_SNAPSHOT
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="INVOICE_APPLY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.invoiceApplyId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="RULE_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.ruleCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="RULE_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.ruleName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="RULE_CONTENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.ruleContent,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PROMPT_TEXT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.promptText,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="APPLY_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.applyReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where INVOICE_APPLY_REASON_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.invoiceApplyReasonId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_INVOICE_APPLY_REASON_SNAPSHOT
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="INVOICE_APPLY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceApplyId != null">
            when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.invoiceApplyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="RULE_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ruleCode != null">
            when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.ruleCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="RULE_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ruleName != null">
            when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.ruleName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="RULE_CONTENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ruleContent != null">
            when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.ruleContent,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PROMPT_TEXT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.promptText != null">
            when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.promptText,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="APPLY_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.applyReason != null">
            when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.applyReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateRemark != null">
            when INVOICE_APPLY_REASON_ID = #{item.invoiceApplyReasonId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where INVOICE_APPLY_REASON_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.invoiceApplyReasonId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="INVOICE_APPLY_REASON_ID" keyProperty="invoiceApplyReasonId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_APPLY_REASON_SNAPSHOT
    (INVOICE_APPLY_ID, RULE_CODE, RULE_NAME, RULE_CONTENT, PROMPT_TEXT, APPLY_REASON, 
      IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.invoiceApplyId,jdbcType=INTEGER}, #{item.ruleCode,jdbcType=VARCHAR}, #{item.ruleName,jdbcType=VARCHAR}, 
        #{item.ruleContent,jdbcType=VARCHAR}, #{item.promptText,jdbcType=VARCHAR}, #{item.applyReason,jdbcType=VARCHAR}, 
        #{item.isDelete,jdbcType=BOOLEAN}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updater,jdbcType=INTEGER}, 
        #{item.updaterName,jdbcType=VARCHAR}, #{item.updateRemark,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="INVOICE_APPLY_REASON_ID" keyProperty="invoiceApplyReasonId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyReasonSnapshotEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_APPLY_REASON_SNAPSHOT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceApplyReasonId != null">
        INVOICE_APPLY_REASON_ID,
      </if>
      INVOICE_APPLY_ID,
      RULE_CODE,
      RULE_NAME,
      RULE_CONTENT,
      PROMPT_TEXT,
      APPLY_REASON,
      IS_DELETE,
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      CREATOR_NAME,
      UPDATER,
      UPDATER_NAME,
      UPDATE_REMARK,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceApplyReasonId != null">
        #{invoiceApplyReasonId,jdbcType=BIGINT},
      </if>
      #{invoiceApplyId,jdbcType=INTEGER},
      #{ruleCode,jdbcType=VARCHAR},
      #{ruleName,jdbcType=VARCHAR},
      #{ruleContent,jdbcType=VARCHAR},
      #{promptText,jdbcType=VARCHAR},
      #{applyReason,jdbcType=VARCHAR},
      #{isDelete,jdbcType=BOOLEAN},
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updater,jdbcType=INTEGER},
      #{updaterName,jdbcType=VARCHAR},
      #{updateRemark,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="invoiceApplyReasonId != null">
        INVOICE_APPLY_REASON_ID = #{invoiceApplyReasonId,jdbcType=BIGINT},
      </if>
      INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
      RULE_CODE = #{ruleCode,jdbcType=VARCHAR},
      RULE_NAME = #{ruleName,jdbcType=VARCHAR},
      RULE_CONTENT = #{ruleContent,jdbcType=VARCHAR},
      PROMPT_TEXT = #{promptText,jdbcType=VARCHAR},
      APPLY_REASON = #{applyReason,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="INVOICE_APPLY_REASON_ID" keyProperty="invoiceApplyReasonId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyReasonSnapshotEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_APPLY_REASON_SNAPSHOT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceApplyReasonId != null">
        INVOICE_APPLY_REASON_ID,
      </if>
      <if test="invoiceApplyId != null">
        INVOICE_APPLY_ID,
      </if>
      <if test="ruleCode != null and ruleCode != ''">
        RULE_CODE,
      </if>
      <if test="ruleName != null and ruleName != ''">
        RULE_NAME,
      </if>
      <if test="ruleContent != null and ruleContent != ''">
        RULE_CONTENT,
      </if>
      <if test="promptText != null and promptText != ''">
        PROMPT_TEXT,
      </if>
      <if test="applyReason != null and applyReason != ''">
        APPLY_REASON,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceApplyReasonId != null">
        #{invoiceApplyReasonId,jdbcType=BIGINT},
      </if>
      <if test="invoiceApplyId != null">
        #{invoiceApplyId,jdbcType=INTEGER},
      </if>
      <if test="ruleCode != null and ruleCode != ''">
        #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null and ruleName != ''">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleContent != null and ruleContent != ''">
        #{ruleContent,jdbcType=VARCHAR},
      </if>
      <if test="promptText != null and promptText != ''">
        #{promptText,jdbcType=VARCHAR},
      </if>
      <if test="applyReason != null and applyReason != ''">
        #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="invoiceApplyReasonId != null">
        INVOICE_APPLY_REASON_ID = #{invoiceApplyReasonId,jdbcType=BIGINT},
      </if>
      <if test="invoiceApplyId != null">
        INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
      </if>
      <if test="ruleCode != null and ruleCode != ''">
        RULE_CODE = #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null and ruleName != ''">
        RULE_NAME = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleContent != null and ruleContent != ''">
        RULE_CONTENT = #{ruleContent,jdbcType=VARCHAR},
      </if>
      <if test="promptText != null and promptText != ''">
        PROMPT_TEXT = #{promptText,jdbcType=VARCHAR},
      </if>
      <if test="applyReason != null and applyReason != ''">
        APPLY_REASON = #{applyReason,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>