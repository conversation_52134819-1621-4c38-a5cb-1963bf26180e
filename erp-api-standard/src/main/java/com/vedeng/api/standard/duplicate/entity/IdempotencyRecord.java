package com.vedeng.api.standard.duplicate.entity;

import com.vedeng.api.standard.duplicate.enums.IdempotencyStatus;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 幂等性记录实体类
 * 对应数据库表：t_idempotency_record
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IdempotencyRecord extends BaseEntity {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 幂等性键
     * 格式：{businessType}_{companyCode}_{flowOrderId}[_{contentHash}]
     */
    private String idempotencyKey;
    
    /**
     * 流程订单ID（来自Temporal工作流）
     */
    private String flowOrderId;
    
    /**
     * 公司代码
     */
    private String companyCode;
    
    /**
     * 业务类型
     * 值：PURCHASE_ORDER, SALES_ORDER, INVOICE等
     */
    private String businessType;
    
    /**
     * 业务内容hash（可选，用于精确去重）
     */
    private String businessContentHash;
    
    /**
     * 请求数据JSON格式
     */
    private String requestData;
    
    /**
     * 响应数据JSON格式
     */
    private String responseData;
    
    /**
     * 处理状态
     * 0-处理中 1-成功 2-失败
     */
    private Integer status;
    
    
    /**
     * 生成的业务单据ID
     */
    private String businessDocumentId;
    
    /**
     * 生成的业务单据编号
     */
    private String businessDocumentNo;
    
    /**
     * 扩展信息（JSON格式）
     */
    private String extraInfo;
    
    /**
     * 创建时间
     */
    private Date createdTime;
    
    /**
     * 更新时间
     */
    private Date updatedTime;
    
    /**
     * 是否逻辑删除
     * 0-未删除 1-已删除
     */
    private Integer isDeleted;

    
    /**
     * 获取状态枚举
     * 
     * @return 状态枚举
     */
    public IdempotencyStatus getStatusEnum() {
        return status != null ? IdempotencyStatus.fromCode(status) : null;
    }
    
    /**
     * 设置状态枚举
     * 
     * @param statusEnum 状态枚举
     */
    public void setStatusEnum(IdempotencyStatus statusEnum) {
        this.status = statusEnum != null ? statusEnum.getCode() : null;
    }
    
    /**
     * 判断是否为成功状态
     * 
     * @return 是否为成功状态
     */
    public boolean isSuccess() {
        return IdempotencyStatus.SUCCESS.getCode() == (status != null ? status : -1);
    }
    
    /**
     * 判断是否为失败状态
     * 
     * @return 是否为失败状态
     */
    public boolean isFailed() {
        return IdempotencyStatus.FAILED.getCode() == (status != null ? status : -1);
    }
    
    /**
     * 判断是否为处理中状态
     * 
     * @return 是否为处理中状态
     */
    public boolean isProcessing() {
        return IdempotencyStatus.PROCESSING.getCode() == (status != null ? status : -1);
    }
    
    /**
     * 判断是否为终态（成功或失败）
     * 
     * @return 是否为终态
     */
    public boolean isTerminal() {
        return isSuccess() || isFailed();
    }
    
    /**
     * 判断是否已逻辑删除
     * 
     * @return 是否已逻辑删除
     */
    public boolean isLogicallyDeleted() {
        return Integer.valueOf(1).equals(isDeleted);
    }
    
    /**
     * 设置为逻辑删除状态
     */
    public void markAsDeleted() {
        this.isDeleted = 1;
        this.updatedTime = new Date();
    }
    
    /**
     * 设置为未删除状态
     */
    public void markAsNotDeleted() {
        this.isDeleted = 0;
    }
    
    
    @Override
    public String toString() {
        return String.format("IdempotencyRecord{id=%d, idempotencyKey='%s', businessType='%s', " +
                "companyCode='%s', flowOrderId='%s', status=%d}", 
                id, idempotencyKey, businessType, companyCode, flowOrderId, status);
    }
}
