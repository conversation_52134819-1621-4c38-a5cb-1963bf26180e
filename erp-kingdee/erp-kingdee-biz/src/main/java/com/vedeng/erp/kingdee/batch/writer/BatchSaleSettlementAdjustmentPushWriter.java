package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.BatchSaleSettlementAdjustmentAndItemDto;
import com.vedeng.erp.kingdee.batch.dto.BatchSaleSettlementAdjustmentDto;
import com.vedeng.erp.kingdee.batch.repository.BatchSaleSettlementAdjustmentDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeSaleSettlementAdjustmentDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleSettlementAdjustmentCommandConvertor;
import com.vedeng.erp.kingdee.service.KingDeeSaleSettlementAdjustmentApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @description: 金蝶调整单推送
 * @author: Suqin
 * @date: 2023/2/25 13:18
 **/
@Slf4j
@Service
public class BatchSaleSettlementAdjustmentPushWriter extends BaseWriter<BatchSaleSettlementAdjustmentAndItemDto> {

    @Autowired
    KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    KingDeeSaleSettlementAdjustmentCommandConvertor kingDeeSaleSettlementAdjustmentCommandConvertor;

    @Autowired
    BatchSaleSettlementAdjustmentDtoMapper batchSaleSettlementAdjustmentDtoMapper;

    @Autowired
    KingDeeSaleSettlementAdjustmentApiService kingDeeSaleSettlementAdjustmentApiService;

    @Override
    public void doWrite(BatchSaleSettlementAdjustmentAndItemDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        if (Objects.isNull(item)){
            return;
        }
        KingDeeSaleSettlementAdjustmentDto model = item.getKingDeeSaleSettlementAdjustmentDto();
        model.setKingDeeBizEnums(KingDeeBizEnums.saveSaleSettlementAdjustment);
        log.info("销售/售后 调整单推送金蝶参数：{}", JSON.toJSONString(model));
        kingDeeSaleSettlementAdjustmentApiService.register(model,true);
        // 修改推送状态、出库信息
        BatchSaleSettlementAdjustmentDto update = item.getBatchSaleSettlementAdjustmentDto();
        if (Objects.nonNull(update)) {
            batchSaleSettlementAdjustmentDtoMapper.updateByPrimaryKeySelective(update);
        }
    }
}
