<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="跟进记录详情页" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<style>
    .lineline{
        width: 100px;!important;
    }
    .myinput{
        cursor: not-allowed !important
    }
</style>
<div class="layui-form">
    <div class="layui-form-item layui-form-text">
        <label class="layui-form-label lineline" >跟进记录:</label>
        <div class="layui-input-block">
            <textarea  id="followUpComment" class="layui-textarea" maxlength="60">${task.followUpComment}</textarea>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label lineline">跟进人:</label>
        <div class="layui-input-block">
            <label  type="text" disabled="disabled" autocomplete="off"  class="layui-form-label"  value="">${task.followUpPerson}</label>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label lineline">跟进时间:</label>
        <div class="layui-input-block">
            <label type="text" disabled="disabled"  autocomplete="off" style="width: auto" class="layui-form-label" value="${task.followUpTime}">${task.followUpTime}</label>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block" style="text-align: center">
            <button class="layui-btn" lay-submit lay-filter="*" onclick=save()>确定</button>
        </div>
    </div>
    <input type="hidden" value="${task.earlyWarningTaskId}" id="earlyWarningTaskId">
    <input type="hidden" value="${task.followUpNum}" id="followUpNum">
</div>
<script src="${pageContext.request.contextPath}/static/js/flash/expediting/followUpDetail.js?rnd=${resourceVersionKey}">
<%@ include file="../../common/footer.jsp" %>