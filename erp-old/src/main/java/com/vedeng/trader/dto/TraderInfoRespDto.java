package com.vedeng.trader.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class TraderInfoRespDto implements Serializable {

    private static final long serialVersionUID = 5779701285913667375L;

    private Integer traderId;

    private Integer traderCustomerId;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 归属平台  1贝登医疗，2医械购，3科研购，4集团业务部，5其他，6集采
     */
    private Integer belongPlatform;

    /**
     * 是否是关联客户
     */
    private Boolean isRelate;

    /**
     * 来源 share-分享客户 belong-归属客户
     */
    private String fromWhere;

    /**
     * 创建时间
     */
    private String addTime;

    /**
     * 客户等级
     */
    private String traderLevel;

    /**
     * 经营终端类型
     */
    private String businessTerminalName;

    /**
     * 经营商品类型
     */
    private String businessSkuTypeName;

    /**
     * 经验模式
     */
    private String businessModal;
}