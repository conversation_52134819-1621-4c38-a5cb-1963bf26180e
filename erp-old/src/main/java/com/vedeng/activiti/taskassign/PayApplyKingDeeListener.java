package com.vedeng.activiti.taskassign;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesDetailMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.finance.dto.PayVedengBankDto;
import com.vedeng.erp.finance.service.PayVedengBankApiService;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.enums.*;
import com.vedeng.erp.kingdee.service.KingDeePayBillApiService;
import com.vedeng.erp.kingdee.service.KingDeeReceiveRefundBillApiService;
import com.vedeng.erp.kingdee.service.KingDeeWorkflowApiService;
import com.vedeng.finance.dao.PayApplyMapper;
import com.vedeng.finance.model.PayApply;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.orderstream.aftersales.constant.AfterSalesProcessEnum;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.dao.TraderSupplierMapper;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.model.TraderSupplier;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 付款申请通过推送金蝶处理mq
 */
public class PayApplyKingDeeListener implements ExecutionListener {

    private static Logger logger = LoggerFactory.getLogger(PayApplyKingDeeListener.class);

    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();

    private PayApplyMapper payApplyMapper = context.getBean("payApplyMapper", PayApplyMapper.class);
    private BuyorderMapper buyorderMapper = context.getBean("buyorderMapper", BuyorderMapper.class);
    private TraderSupplierMapper traderSupplierMapper = context.getBean("traderSupplierMapper", TraderSupplierMapper.class);
    private AfterSalesMapper afterSalesMapper = context.getBean("afterSalesMapper", AfterSalesMapper.class);
    private AfterSalesDetailMapper afterSalesDetailMapper = context.getBean("afterSalesDetailMapper", AfterSalesDetailMapper.class);
    private TraderCustomerMapper traderCustomerMapper = context.getBean("traderCustomerMapper", TraderCustomerMapper.class);
    private BuyorderExpenseApiService buyorderExpenseApiService = (BuyorderExpenseApiService) context.getBean("buyorderExpenseServiceImpl");
    private PayVedengBankApiService payVedengBankApiService = (PayVedengBankApiService) context.getBean("payVedengBankApiServiceImpl");
    private KingDeePayBillApiService kingDeePayBillService = (KingDeePayBillApiService) context.getBean("kingDeePayBillServiceImpl");
    private KingDeeWorkflowApiService kingDeeWorkflowApiService = (KingDeeWorkflowApiService) context.getBean("kingDeeWorkflowServiceImpl");

    private KingDeeReceiveRefundBillApiService kingDeeReceiveRefundBillApiService = (KingDeeReceiveRefundBillApiService) context.getBean("kingDeeReceiveRefundBill2ServiceImpl");

    private static final String BD_SUPPLIER = "BD_Supplier"; //付款单
    private static final String BD_CUSTOMER = "BD_Customer"; //收款退款单

    @Override
    public void notify(DelegateExecution delegateExecution) {
        Integer payApplyId = Integer.parseInt(delegateExecution.getVariable("relateTableKey").toString());
        logger.info("财务制单完成，开始推送金蝶，付款申请ID{},", payApplyId);
        //查询财务制单通过的付款申请信息
        PayApply payApply = payApplyMapper.selectByPrimaryKey(payApplyId);
        logger.info("开始判断付款申请对应金蝶付款单还是收款退款单,payApplyId:{}",payApplyId);
        String type = kingDeePayBillService.choosePayObject(payApply.getPayType(),payApply.getRelatedId());
        logger.info("判断付款申请对应金蝶付款单还是收款退款单,payType:{},返回结果:{}",payApply.getPayType(),type);
        // 银行回单备注
        String bankRemark = payApply.getBankRemark();
        // 获取配置
        Integer autoAudit = payApplyMapper.queryAutoPayConfig(1);

        //推送付款单
        if (BD_SUPPLIER.equals(type)){
            KingDeePayBillDto kingDeePayBillDto;
            KingDeePayBillEntryDto kingDeePayBillEntryDto = new KingDeePayBillEntryDto();
            List<String> ignoreTradeNameList = payVedengBankApiService.getIgnoreTraderName();

            if (CollUtil.isNotEmpty(ignoreTradeNameList)){
                logger.info("获取付款申请需要排出的供应商集合:{},当前供应商名称:{}", JSONUtil.toJsonStr(ignoreTradeNameList),payApply.getTraderName());
                if (ignoreTradeNameList.contains(payApply.getTraderName())){
                    logger.info("付款申请供应商排除命中,traderName：{}，无需推送金蝶付款",payApply.getTraderName());
                    return;
                }
            }
            if (KingDeeConstant.ID_517.equals(payApply.getPayType())) {
                kingDeePayBillDto = new KingDeePayBillDto(KingDeeBizEnums.savePayBill, ErpConst.ONE);
                //采购单申请付款
                Buyorder buyorder = buyorderMapper.selectByPrimaryKey(payApply.getRelatedId());
                TraderSupplier traderSupplier = traderSupplierMapper.getSuplierInfoByTraderId(buyorder.getTraderId());
                kingDeePayBillDto.setTraderSupplierId(traderSupplier.getTraderSupplierId());
                kingDeePayBillDto.setFBillTypeId(KingDeePayBillTypeEnums.BUYORDER_PAY.getCode());

                kingDeePayBillDto.setFQzokCgddh(buyorder.getBuyorderNo());
                kingDeePayBillDto.setFQzokPzgsywdh(buyorder.getBuyorderNo());

                kingDeePayBillEntryDto.setFQzokYsddh(buyorder.getBuyorderNo());

                kingDeePayBillEntryDto.setFQzokGsywdh(buyorder.getBuyorderNo());

                kingDeePayBillEntryDto.setFQzokYwlx(KingDeeBaseEnums.BUY_PAY.getName());

                kingDeePayBillEntryDto.setFcomment(buyorder.getBuyorderNo() + (StringUtils.isNotBlank(bankRemark) ? "/"+bankRemark : ""));

                kingDeePayBillEntryDto.setFpurposeid(KingDeePayBillUseTypeEnums.BUY_PAY.getCode());
            } else if (KingDeeConstant.ID_518.equals(payApply.getPayType())) {
                //销售售后单申请付款
                AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(payApply.getRelatedId());
                AfterSalesDetail afterSalesDetail = afterSalesDetailMapper.selectadtbyid(afterSales);

                kingDeePayBillEntryDto.setFQzokYsddh(afterSales.getOrderNo());

                kingDeePayBillEntryDto.setFQzokGsywdh(afterSales.getAfterSalesNo());
                //VDERP-12445【功能】【erp】安调维修付款申请，金蝶系统付款对象取值修改为ERP付款申请内支付对象start
                if (AfterSalesProcessEnum.AFTERSALES_ATN.getCode().equals(afterSales.getType())
                        || AfterSalesProcessEnum.AFTERSALES_ATY.getCode().equals(afterSales.getType())
                        || AfterSalesProcessEnum.AFTERSALES_AT.getCode().equals(afterSales.getType())
                        || AfterSalesProcessEnum.AFTERSALES_WX.getCode().equals(afterSales.getType())
                        || AfterSalesProcessEnum.AFTERASALES_THIRD_AT.getCode().equals(afterSales.getType())
                        || AfterSalesProcessEnum.AFTERASALES_THIRD_WX.getCode().equals(afterSales.getType())) {
                    //安调维修付款给供应商（工程师）--付款类型为采购
                    kingDeePayBillDto = new KingDeePayBillDto(KingDeeBizEnums.savePayBill, ErpConst.ONE);
                    TraderSupplier traderSupplier = traderSupplierMapper.getTraderSupplierByTraderName(payApply.getTraderName());
                    if (traderSupplier != null) {
                        kingDeePayBillDto.setTraderSupplierId(traderSupplier.getTraderSupplierId());
                        // 业务类型--售后工程师付款
                        kingDeePayBillEntryDto.setFQzokYwlx(KingDeeBaseEnums.SALE_AFTER_ENGINEER_PAY.getName());
                    } else {
                        logger.error("安调维修售后单{},查询供应商信息为空,", afterSales.getAfterSalesNo());
                        throw new ServiceException("安调维修售后单查询供应商信息为空");
                    }
                    kingDeePayBillDto.setFBillTypeId(KingDeePayBillTypeEnums.BUYORDER_PAY.getCode());

                    kingDeePayBillEntryDto.setFpurposeid(KingDeePayBillUseTypeEnums.BUY_PAY.getCode());
                } else {
                    kingDeePayBillDto = new KingDeePayBillDto(KingDeeBizEnums.savePayBill, ErpConst.TWO);
                    TraderCustomer traderCustomer = traderCustomerMapper.getTraderCustomerByTraderId(afterSalesDetail.getTraderId());
                    //业务类型--销售售后退货/退款付款
                    if (AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSales.getType())) {
                        kingDeePayBillEntryDto.setFQzokYwlx(KingDeeBaseEnums.SALE_AFTER_BACK_GOOD_PAY.getName());
                    } else if (AfterSalesProcessEnum.AFTERSALES_TK.getCode().equals(afterSales.getType())) {
                        kingDeePayBillEntryDto.setFQzokYwlx(KingDeeBaseEnums.SALE_AFTER_BACK_REFUND_PAY.getName());
                    } else if (AfterSalesProcessEnum.THIRD_AMOUNT_RETURN.getCode().equals(afterSales.getType())) {
                        kingDeePayBillEntryDto.setFQzokYwlx(KingDeeBaseEnums.THIRD_AFTER_BACK_REFUND_PAY.getName());
                    } else {
                        logger.error("销售售后单{},类型为{},非付款业务类型", afterSales.getAfterSalesId(), afterSales.getType());
                        throw new ServiceException("错误的销售售后付款类型");
                    }
                    kingDeePayBillDto.setTraderCustomerId(traderCustomer.getTraderCustomerId());
                    kingDeePayBillDto.setFBillTypeId(KingDeePayBillTypeEnums.OTHER_PAY.getCode());

                    kingDeePayBillEntryDto.setFpurposeid(KingDeePayBillUseTypeEnums.OTHER_PAY.getCode());
                }
                //VDERP-12445【功能】【erp】安调维修付款申请，金蝶系统付款对象取值修改为ERP付款申请内支付对象end
                kingDeePayBillDto.setFQzokCgddh(afterSales.getAfterSalesNo());
                kingDeePayBillDto.setFQzokPzgsywdh(afterSales.getAfterSalesNo());
                kingDeePayBillEntryDto.setFcomment(afterSales.getAfterSalesNo()+ (StringUtils.isNotBlank(bankRemark) ? "/"+bankRemark : ""));
            } else if (KingDeeConstant.ID_4125.equals(payApply.getPayType())) {
                kingDeePayBillDto = new KingDeePayBillDto(KingDeeBizEnums.savePayBill, ErpConst.ONE);
                //采购费用单申请付款
                BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.getOrderMainData(payApply.getRelatedId());
                TraderSupplier traderSupplier = traderSupplierMapper.getSuplierInfoByTraderId(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderId());
                kingDeePayBillDto.setTraderSupplierId(traderSupplier.getTraderSupplierId());
                kingDeePayBillDto.setFBillTypeId(KingDeePayBillTypeEnums.BUYORDER_PAY.getCode());
                kingDeePayBillDto.setFQzokCgddh(buyorderExpenseDto.getBuyorderExpenseNo());
                kingDeePayBillDto.setFQzokPzgsywdh(buyorderExpenseDto.getBuyorderExpenseNo());
                kingDeePayBillEntryDto.setFQzokYsddh(buyorderExpenseDto.getBuyorderExpenseNo());
                kingDeePayBillEntryDto.setFQzokGsywdh(buyorderExpenseDto.getBuyorderExpenseNo());
                kingDeePayBillEntryDto.setFQzokYwlx(KingDeeBaseEnums.BUY_EXPENSE_PAY.getName());
                kingDeePayBillEntryDto.setFcomment(buyorderExpenseDto.getBuyorderExpenseNo()+ (StringUtils.isNotBlank(bankRemark) ? "/"+bankRemark : ""));
                kingDeePayBillEntryDto.setFpurposeid(KingDeePayBillUseTypeEnums.BUY_PAY.getCode());
            } else {
                logger.error("未知付款申请类型,付款申请信息{},", JSON.toJSONString(kingDeePayBillEntryDto));
                throw new ServiceException("未知付款申请类型，无法推送金蝶");
            }

            kingDeePayBillDto.setKingDeeBizEnums(KingDeeBizEnums.savePayBill);

            kingDeePayBillDto.setFBillNo(payApply.getPayApplyId().toString());

            String payApplyDate = DateUtil.convertString(DateUtil.sysTimeMillis(), "yyyy-MM-dd");
//            kingDeePayBillDto.setFDate(payApplyDate);
            kingDeePayBillDto.setFDate(cn.hutool.core.date.DateUtil.formatDateTime(new Date()));// VDERP-16088  款对接金蝶业务日期规则调整-精确到时分秒

            kingDeePayBillDto.setFRemark(payApply.getValidComments());

            if (ErpConst.ONE.equals(payApply.getTraderSubject())) {
                kingDeePayBillDto.setFQzokJyzt("对公");
                kingDeePayBillEntryDto.setFRecType(ErpConst.ZERO.toString());
                kingDeePayBillEntryDto.setFRuZhangType(ErpConst.ONE.toString());
            } else if (ErpConst.TWO.equals(payApply.getTraderSubject())) {
                kingDeePayBillDto.setFQzokJyzt("对私");
                kingDeePayBillEntryDto.setFRecType(ErpConst.ONE.toString());
                kingDeePayBillEntryDto.setFRuZhangType(ErpConst.ZERO.toString());
            }

            kingDeePayBillDto.setFQzokJylx("支出");

            if (SysOptionConstant.ID_521.equals(payApply.getTraderMode())) {
                //付款方式为银行的付款申请单，推送金蝶
                kingDeePayBillEntryDto.setFsettletypeid(KingDeePayBillSettleTypeEnum.TELEGRAPHIC_SETTLETYPE.getCode());
            } else {
                logger.info("付款申请ID{},交易方式不为银行，无需推送金蝶付款", payApply.getPayApplyId());
                return;
            }

            kingDeePayBillEntryDto.setFpaytotalamountfor(payApply.getAmount());
            kingDeePayBillEntryDto.setFcostid("");

            kingDeePayBillEntryDto.setFexpensedeptidE("");
            // 我方银行账号
            if (payApply.getPayBankTypeId() != null) {
                PayVedengBankDto payVedengBankDto = payVedengBankApiService.queryInfoByPayVedengBankId(payApply.getPayBankTypeId());
                kingDeePayBillEntryDto.setFaccountid(payVedengBankDto.getPayBankNo());
            } else {
                logger.error("付款申请ID{},我方银行账号Id为空，发起付款MQ失败", payApply.getPayApplyId());
                throw new ServiceException("付款我方银行账号信息为空");
            }

            kingDeePayBillEntryDto.setFsettleno("");
            kingDeePayBillEntryDto.setFpostdate(payApplyDate);
            kingDeePayBillEntryDto.setFOppositeBankAccount(payApply.getBankAccount());
            kingDeePayBillEntryDto.setFOppositeCcountName(payApply.getTraderName());
            kingDeePayBillEntryDto.setFOppositeBankName(payApply.getBank());
            kingDeePayBillEntryDto.setFCnaps(payApply.getBankCode());
            // 设置自动提交银行
            kingDeePayBillEntryDto.setFQzokZdtjyh(Objects.equals(autoAudit,1) ? "1" : "2");
            List<KingDeePayBillEntryDto> kingDeePayBillEntryDtoList = new ArrayList<>();
            kingDeePayBillEntryDtoList.add(kingDeePayBillEntryDto);
            kingDeePayBillDto.setFPayBillEntry(kingDeePayBillEntryDtoList);

            KingDeePayBillSrcEntryDto kingDeePayBillSrcEntryDto = new KingDeePayBillSrcEntryDto();
            KingDeePayBillSrcEntryLinkDto kingDeePayBillSrcEntryLinkDto = new KingDeePayBillSrcEntryLinkDto();
            List<KingDeePayBillSrcEntryLinkDto> kingDeePayBillSrcEntryLinkDtoList = new ArrayList<>();
            kingDeePayBillSrcEntryLinkDtoList.add(kingDeePayBillSrcEntryLinkDto);
            kingDeePayBillSrcEntryDto.setFpaybillsrcentryLink(kingDeePayBillSrcEntryLinkDtoList);

            List<KingDeePayBillSrcEntryDto> kingDeePayBillSrcEntryDtoList = new ArrayList<>();
            kingDeePayBillSrcEntryDtoList.add(kingDeePayBillSrcEntryDto);
            kingDeePayBillDto.setFPayBillSrcEntry(kingDeePayBillSrcEntryDtoList);
            //发送mq消息
            // 设置自动审核
            kingDeePayBillDto.setIsAutoSubmitAndAudit(Objects.equals(autoAudit,1) ? Boolean.TRUE : Boolean.FALSE);
            logger.info("推送金蝶付款单：{}", JSON.toJSONString(kingDeePayBillDto));
            kingDeePayBillService.register(kingDeePayBillDto);
//            if (Objects.equals(autoAudit,1)){
//                // 自动提交
//                KingDeeWorkflowDto kingDeeWorkflowSubmitDto = new KingDeeWorkflowDto(KingDeeBizEnums.workFlowSubmit, kingDeePayBillDto.getFBillNo(), KingDeeBizEnums.savePayBill.getFormId());
//                logger.info("金蝶付款单自动提交：{}",JSON.toJSON(kingDeeWorkflowSubmitDto));
//                kingDeeWorkflowApiService.register(kingDeeWorkflowSubmitDto);
//                // 自动审核
//                KingDeeWorkflowDto kingDeeWorkflowAuditDto = new KingDeeWorkflowDto(KingDeeBizEnums.workFlowAudit, kingDeePayBillDto.getFBillNo(), KingDeeBizEnums.savePayBill.getFormId());
//                logger.info("金蝶付款单自动审核：{}",JSON.toJSON(kingDeeWorkflowAuditDto));
//                kingDeeWorkflowApiService.register(kingDeeWorkflowAuditDto);
//            }
        }
        //推送收款退款单
        if(BD_CUSTOMER.equals(type)) {
            AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(payApply.getRelatedId());//.getAfterSalesNo()
            KingDeeReceiveRefundBillDto receiveRefundBillDto = new KingDeeReceiveRefundBillDto();
            KingDeeReceiveRefundEntryDto receiveRefundEntryDto = new KingDeeReceiveRefundEntryDto();
            receiveRefundBillDto.setFID(String.valueOf(ErpConst.ZERO));
            receiveRefundBillDto.setFBillNo(payApply.getPayApplyId().toString());
//            receiveRefundBillDto.setFDATE(DateUtil.convertString(DateUtil.sysTimeMillis(), "yyyy-MM-dd"));
            receiveRefundBillDto.setFDATE(cn.hutool.core.date.DateUtil.formatDateTime(new Date()));// VDERP-16088  款对接金蝶业务日期规则调整-精确到时分秒
            receiveRefundBillDto.setFCONTACTUNITTYPE(BD_CUSTOMER);
            Integer traderCustomerId = afterSalesMapper.getTraderCustomerIdByAfterSalesId(payApply.getRelatedId());
            receiveRefundBillDto.setFCONTACTUNIT(traderCustomerId.toString());
            receiveRefundBillDto.setFRECTUNITTYPE(BD_CUSTOMER);
            receiveRefundBillDto.setFRECTUNIT(traderCustomerId.toString());
            receiveRefundBillDto.setF_QZOK_LSH("");
            receiveRefundBillDto.setF_QZOK_BDDJTID(payApply.getPayApplyId().toString());
            receiveRefundBillDto.setFQzokPzgsywdh(afterSales.getAfterSalesNo());//VDERP-16089 - 款对接金蝶增加入参：归属业务单号
            // 设置自动提交银行
            receiveRefundBillDto.setFQzokZdtjyh(Objects.equals(autoAudit,1) ? "1" : "2");
            //明细
            if (SysOptionConstant.ID_521.equals(payApply.getTraderMode())) {
                //付款方式为银行的付款申请单，推送金蝶
                receiveRefundEntryDto.setFSETTLETYPEID(KingDeeBankTagEnum.BANK_PAY.getfSettleTypeId());
            } else {
                logger.info("付款申请推送收款退款单,付款申请ID{},交易方式不为银行，无需推送金蝶付款", payApply.getPayApplyId());
                return;
            }
            receiveRefundEntryDto.setFRECTOTALAMOUNTFOR(payApply.getAmount());
            receiveRefundEntryDto.setFREFUNDAMOUNTFOR(payApply.getAmount());

            // 我方银行账号
            if (payApply.getPayBankTypeId() != null) {
                PayVedengBankDto payVedengBankDto = payVedengBankApiService.queryInfoByPayVedengBankId(payApply.getPayBankTypeId());
                receiveRefundEntryDto.setFACCOUNTID(payVedengBankDto.getPayBankNo());
            } else {
                logger.error("付款申请ID{},我方银行账号Id为空", payApply.getPayApplyId());
                throw new ServiceException("收款退款单推送金蝶,我方银行账号信息为空");
            }
            receiveRefundEntryDto.setFOPPOSITEBANKACCOUNT(payApply.getBankAccount());
            receiveRefundEntryDto.setFOPPOSITECCOUNTNAME(payApply.getTraderName());
            receiveRefundEntryDto.setFOPPOSITEBANKNAME(payApply.getBank());
            receiveRefundEntryDto.setFCNAPS(payApply.getBankCode());
            receiveRefundEntryDto.setF_QZOK_YSDDH("");
            receiveRefundEntryDto.setF_QZOK_GSYWDH(afterSales.getAfterSalesNo());
            receiveRefundEntryDto.setF_QZOK_BDDJHID("");
            receiveRefundEntryDto.setF_QZOK_YWLX("销售售后付款");
            receiveRefundEntryDto.setFRecType(ErpConst.ONE.toString().equals(payApply.getTraderSubject().toString()) ? "0" : "1");
            receiveRefundEntryDto.setFNote(receiveRefundEntryDto.getF_QZOK_GSYWDH()+ (StringUtils.isNotBlank(bankRemark) ? "/"+bankRemark : ""));
            receiveRefundBillDto.getFREFUNDBILLENTRY().add(receiveRefundEntryDto);

            //推送金蝶
            receiveRefundBillDto.setKingDeeBizEnums(KingDeeBizEnums.saveReceiveRefundBill2);
            logger.info("收款退款单推送金蝶：{}",JSON.toJSON(receiveRefundBillDto));
            // 设置自动审核
            receiveRefundBillDto.setIsAutoSubmitAndAudit(Objects.equals(autoAudit,1) ? Boolean.TRUE : Boolean.FALSE);
            kingDeeReceiveRefundBillApiService.register(receiveRefundBillDto,true);
//            if (Objects.equals(autoAudit,1)){
//                // 自动提交
//                KingDeeWorkflowDto kingDeeWorkflowSubmitDto = new KingDeeWorkflowDto(KingDeeBizEnums.workFlowSubmit, receiveRefundBillDto.getFBillNo(), KingDeeBizEnums.saveReceiveRefundBill2.getFormId());
//                logger.info("金蝶付款单自动提交：{}",JSON.toJSON(kingDeeWorkflowSubmitDto));
//                kingDeeWorkflowApiService.register(kingDeeWorkflowSubmitDto,true);
//                // 自动审核
//                KingDeeWorkflowDto kingDeeWorkflowAuditDto = new KingDeeWorkflowDto(KingDeeBizEnums.workFlowAudit, receiveRefundBillDto.getFBillNo(), KingDeeBizEnums.saveReceiveRefundBill2.getFormId());
//                logger.info("金蝶付款单自动审核：{}",JSON.toJSON(kingDeeWorkflowAuditDto));
//                kingDeeWorkflowApiService.register(kingDeeWorkflowAuditDto,true);
//            }
        }
    }
}
