package com.vedeng.crm.business.quote.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 */
@Data
public class CrmQuoteOrderDto {

    /**
     * 报价单ID
     */
    private Integer quoteorderId;

    /**
     * 商机ID
     */
    private Integer bussinessChanceId;

    /**
     * 报价单号
     */
    private String quoteorderNo;

    /**
     * ERP公司ID(T_COMPANY)
     */
    private Integer companyId;

    /**
     * 来源0ERP 1web 2wap
     */
    private Integer source;

    /**
     * 部门ID
     */
    private Integer orgId;

    /**
     * 归属ERP用户ID
     */
    private Integer userId;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 交易者名称
     */
    private String traderName;

    /**
     * 客户地区
     */
    private String area;

    /**
     * 客户类型 426科研医疗 427临床医疗 (字典库)
     */
    private Integer customerType;

    /**
     * 客户性质 465分销 466终端 （字典库）
     */
    private Integer customerNature;

    /**
     * 是否是新客户 0否 1是
     */
    private Integer isNewCustomer;

    /**
     * 客户等级
     */
    private String customerLevel;

    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 联系人
     */
    private String traderContactName;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 联系地址ID
     */
    private Integer traderAddressId;

    /**
     * 联系详细地址(含省市区)
     */
    private String address;

    /**
     * 是否采购关键人 0否 1是
     */
    private Integer isPolicymaker;

    /**
     * 采购类型 字典库
     */
    private Integer purchasingType;

    /**
     * 付款条件 字典库
     */
    private Integer paymentTerm;

    /**
     * 采购时间 字典库
     */
    private Integer purchasingTime;

    /**
     * 项目进展情况
     */
    private String projectProgress;

    /**
     * 跟单状态0跟单中 1成单 2失单
     */
    private Integer followOrderStatus;

    /**
     * 跟单备注（失单原因）
     */
    private String followOrderStatusComments;

    /**
     * 成单失单时间
     */
    private Long followOrderTime;

    /**
     * 销售区域ID
     */
    private Integer salesAreaId;

    /**
     * 销售区域
     */
    private String salesArea;

    /**
     * 终端客户ID
     */
    private Integer terminalTraderId;

    /**
     * 终端名称
     */
    private String terminalTraderName;

    /**
     * 终端类型 字典
     */
    private Integer terminalTraderType;

    /**
     * 付款方式 字典库
     */
    private Integer paymentType;

    /**
     * 预付金额
     */
    private BigDecimal prepaidAmount;

    /**
     * 账期支付金额
     */
    private BigDecimal accountPeriodAmount;

    /**
     * 账期天数
     */
    private Integer periodDay;

    /**
     * 物流代收0否 1是
     */
    private Integer logisticsCollection;

    /**
     * 尾款
     */
    private BigDecimal retainageAmount;

    /**
     * 尾款期限(月)
     */
    private Integer retainageAmountMonth;

    /**
     * 是否生效 0否 1是
     */
    private Integer validStatus;

    /**
     * 生效时间
     */
    private Long validTime;

    /**
     * 报价总额
     */
    private BigDecimal totalAmount;

    /**
     * 有效天数
     */
    private Integer period;

    /**
     * 发票类型 字典表
     */
    private Integer invoiceType;

    /**
     * 运费说明 字典表
     */
    private Integer freightDescription;

    /**
     * 附加条款
     */
    private String additionalClause;

    /**
     * 备注
     */
    private String comments;

    /**
     * 是否发生咨询：0未 1已发送
     */
    private Integer isSend;

    /**
     * 发送咨询时间(最后一次)
     */
    private Long sendTime;

    /**
     * 是否回复：0否 1是
     */
    private Integer isReplay;

    /**
     * 最后一次回复时间
     */
    private Long replayTime;

    /**
     * 最后一次回复人
     */
    private Integer replayUserId;

    /**
     * 有沟通记录0无 1有
     */
    private Integer haveCommunicate;

    /**
     * 咨询答复状态0无 1供应链未处理，主管未处理；2供应链部分处理，主管未处理；3供应链已全部处理，主管未处理；4供应链未处理，主管已处理；5供应链部分处理，主管已处理；6供应链全部处理，主管已处理
     */
    private Integer consultStatus;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 关闭原因字典表id
     */
    private Integer closeReasonId;

    /**
     * 关闭原因描述
     */
    private String closeReasonComment;

    /**
     * 报价预警模式,0：解除预警，1:销售端触发预警，2:采购端触发预警
     */
    private Integer quotedAlarmMode;

    /**
     * 销售端报价预警等级，0:无预警，1:一级预警，2:二级预警
     */
    private Integer salesmanAlarmLevel;

    /**
     * 采购端报价预警等级，0:无预警，1:一级预警，2:二级预警，3：三级预警
     */
    private Integer purchaserAlarmLevel;

    /**
     * 关联bd订单状态,0待关联,1审核中,2关联成功,3关联失败
     */
    private Integer linkBdStatus;

    /**
     * 授权类型ID（字典表）
     */
    private Integer terminalType;

    /**
     * 分享时间
     */
    private Date onlineShareTime;

    /**
     * 是否构建群聊 1是 0否
     */
    private Integer isBuildChat;

    /**
     * 需求描述
     */
    private String needsDesc;

    /**
     * 群名
     */
    private String chatName;

    /**
     * 授权书信息
     */
    private QuoteApplyDto quoteApplyDto;
    /**
     * 附件的数量
     */
    private Integer fileCount;
    
    /**
     * 建群勾选的用户ID列表，逗号分隔
     */
    private String buildChatUserIds;
}
