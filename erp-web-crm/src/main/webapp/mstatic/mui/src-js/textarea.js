Vue.component('ui-textarea', {
    template: `
        <div class="vd-ui-textarea-wrap">
            <textarea
                class="vd-ui-textarea" 
                ref="textarea"
                v-bind="$attrs"
                v-model="value" 
                :readonly="readonly"
                :disabled="disabled"
                :placeholder="placeholder" 
                :class="{'border': border}" 
                :style="{'height': height}"
                :maxlength="maxlength"
                @input="handlerInput"
                @change="handlerChange"
                @focus="handlerFocus"
                @blur="handlerBlur"
                @keydown="handlerKeydown"
                @keyup="handlerKeyup"
            />

            <span
                v-if="showWordLimit && maxlength"
                class="vd-ui-textarea-count"
                :class="{
                    'upper-limit': value.length == maxlength
                }"
            >
                {{ value.length }}/{{ maxlength }}
            </span>
        </div>
    `,
    props: {
        value: {
            type: String,
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        readonly: {
            type: Boolean,
            default: false,
        },
        showWordLimit: {
            type: Boolean,
            default: false,
        },
        clear: {
            type: Boolean,
            default: false
        },

        placeholder: String,
        maxlength: String,
        border: {
            type: Boolean,
            default: false
        },
        height: {
            type: String,
            default: '74px'
        },
        maxheight: {
            type: String
        },
        // 高度自适应
        heightAuto: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            onCompressing: false
        };
    },
    watch: {
        value (newV) {
            this.computedHeight();
        }
    },
    mounted() {
    },
    methods: {
        handlerInput(e) {
            this.$emit('input', e.target.value);
        },
        handlerChange(e) {
            this.$emit('change', e);
        },

        handlerFocus(e) {
            this.$emit('focus', e);
        },
        handlerBlur(e) {
            setTimeout(() => {
                this.$emit('blur', e);
            }, 100)
        },
        handlerKeydown(e) {
            this.$emit('keydown', e);
        },
        handlerKeyup(e) {
            this.$emit('keyup', e);
        },
        handlerClear() {
            this.value = "";
            this.handlerInput();
        },

        // 高度自适应
        computedHeight() {
            if (this.heightAuto) {
                let Element = this.$refs.textarea;
                if (this.value) {
                    let scrollHeight = this.$refs.textarea.scrollHeight;
                    if (scrollHeight > this.maxheight) {
                        console.log('大于 maxheight', scrollHeight, this.maxheight);
                        Element.style.height = this.maxheight
                    } else {
                        console.log('小于 maxheight', scrollHeight, this.maxheight);
                        Element.style.height = scrollHeight + 'px' || this.height || '';
                    }
                } else {
                    Element.style.height = this.height || '';
                }
            }
        }
    }
})