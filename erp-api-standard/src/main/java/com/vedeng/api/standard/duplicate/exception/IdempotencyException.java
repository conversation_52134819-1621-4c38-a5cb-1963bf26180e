package com.vedeng.api.standard.duplicate.exception;

import com.vedeng.api.standard.duplicate.entity.IdempotencyRecord;
import com.vedeng.api.standard.duplicate.enums.IdempotencyStatus;

/**
 * 幂等性异常类
 * 当幂等性处理过程中发生异常时抛出
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-15
 */
public class IdempotencyException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 幂等性键
     */
    private final String idempotencyKey;
    
    /**
     * 当前状态
     */
    private final IdempotencyStatus currentStatus;
    
    /**
     * 已存在的数据
     */
    private final Object existingData;
    
    /**
     * 错误代码
     */
    private final String errorCode;
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     */
    public IdempotencyException(String message) {
        super(message);
        this.idempotencyKey = null;
        this.currentStatus = null;
        this.existingData = null;
        this.errorCode = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param cause 原因异常
     */
    public IdempotencyException(String message, Throwable cause) {
        super(message, cause);
        this.idempotencyKey = null;
        this.currentStatus = null;
        this.existingData = null;
        this.errorCode = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param idempotencyKey 幂等性键
     * @param currentStatus 当前状态
     */
    public IdempotencyException(String message, String idempotencyKey, IdempotencyStatus currentStatus) {
        super(message);
        this.idempotencyKey = idempotencyKey;
        this.currentStatus = currentStatus;
        this.existingData = null;
        this.errorCode = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param idempotencyKey 幂等性键
     * @param currentStatus 当前状态
     * @param existingData 已存在的数据
     */
    public IdempotencyException(String message, String idempotencyKey, 
                               IdempotencyStatus currentStatus, Object existingData) {
        super(message);
        this.idempotencyKey = idempotencyKey;
        this.currentStatus = currentStatus;
        this.existingData = existingData;
        this.errorCode = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param idempotencyKey 幂等性键
     * @param currentStatus 当前状态
     * @param existingData 已存在的数据
     * @param errorCode 错误代码
     */
    public IdempotencyException(String message, String idempotencyKey, 
                               IdempotencyStatus currentStatus, Object existingData, String errorCode) {
        super(message);
        this.idempotencyKey = idempotencyKey;
        this.currentStatus = currentStatus;
        this.existingData = existingData;
        this.errorCode = errorCode;
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param cause 原因异常
     * @param idempotencyKey 幂等性键
     * @param currentStatus 当前状态
     */
    public IdempotencyException(String message, Throwable cause, String idempotencyKey, 
                               IdempotencyStatus currentStatus) {
        super(message, cause);
        this.idempotencyKey = idempotencyKey;
        this.currentStatus = currentStatus;
        this.existingData = null;
        this.errorCode = null;
    }
    
    public String getIdempotencyKey() {
        return idempotencyKey;
    }
    
    public IdempotencyStatus getCurrentStatus() {
        return currentStatus;
    }
    
    public Object getExistingData() {
        return existingData;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    /**
     * 是否有幂等性键
     * 
     * @return 是否有幂等性键
     */
    public boolean hasIdempotencyKey() {
        return idempotencyKey != null && !idempotencyKey.trim().isEmpty();
    }
    
    /**
     * 是否有已存在的数据
     * 
     * @return 是否有已存在的数据
     */
    public boolean hasExistingData() {
        return existingData != null;
    }
    
    /**
     * 获取已存在的幂等性记录
     * 
     * @return 已存在的幂等性记录，如果不是IdempotencyRecord类型则返回null
     */
    public IdempotencyRecord getExistingRecord() {
        return existingData instanceof IdempotencyRecord ? (IdempotencyRecord) existingData : null;
    }
    
    /**
     * 创建键生成异常
     * 
     * @param message 异常消息
     * @param cause 原因异常
     * @return 幂等性异常
     */
    public static IdempotencyException keyGenerationError(String message, Throwable cause) {
        return new IdempotencyException("幂等性键生成失败: " + message, cause);
    }
    
    /**
     * 创建记录查询异常
     * 
     * @param idempotencyKey 幂等性键
     * @param cause 原因异常
     * @return 幂等性异常
     */
    public static IdempotencyException recordQueryError(String idempotencyKey, Throwable cause) {
        return new IdempotencyException("查询幂等性记录失败: " + idempotencyKey, cause, 
                idempotencyKey, null);
    }
    
    /**
     * 创建记录创建异常
     * 
     * @param idempotencyKey 幂等性键
     * @param cause 原因异常
     * @return 幂等性异常
     */
    public static IdempotencyException recordCreationError(String idempotencyKey, Throwable cause) {
        return new IdempotencyException("创建幂等性记录失败: " + idempotencyKey, cause, 
                idempotencyKey, IdempotencyStatus.PROCESSING);
    }
    
    /**
     * 创建记录更新异常
     * 
     * @param idempotencyKey 幂等性键
     * @param targetStatus 目标状态
     * @param cause 原因异常
     * @return 幂等性异常
     */
    public static IdempotencyException recordUpdateError(String idempotencyKey, 
                                                        IdempotencyStatus targetStatus, Throwable cause) {
        return new IdempotencyException("更新幂等性记录失败: " + idempotencyKey, cause, 
                idempotencyKey, targetStatus);
    }
    
    @Override
    public String toString() {
        return String.format("IdempotencyException{message='%s', idempotencyKey='%s', " +
                "currentStatus=%s, hasExistingData=%s, errorCode='%s'}", 
                getMessage(), idempotencyKey, currentStatus, hasExistingData(), errorCode);
    }
}
