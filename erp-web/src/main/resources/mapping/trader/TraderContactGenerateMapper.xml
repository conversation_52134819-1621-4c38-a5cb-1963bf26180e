<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.trader.dao.TraderContactGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.trader.model.TraderContactGenerate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 23 10:53:21 CST 2019.
    -->
    <id column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_TYPE" jdbcType="TINYINT" property="traderType" />
    <result column="IS_ENABLE" jdbcType="TINYINT" property="isEnable" />
    <result column="SEX" jdbcType="TINYINT" property="sex" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="DEPARTMENT" jdbcType="VARCHAR" property="department" />
    <result column="POSITION" jdbcType="VARCHAR" property="position" />
    <result column="TELEPHONE" jdbcType="VARCHAR" property="telephone" />
    <result column="FAX" jdbcType="VARCHAR" property="fax" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="MOBILE2" jdbcType="VARCHAR" property="mobile2" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="QQ" jdbcType="VARCHAR" property="qq" />
    <result column="WEIXIN" jdbcType="VARCHAR" property="weixin" />
    <result column="IS_ON_JOB" jdbcType="TINYINT" property="isOnJob" />
    <result column="IS_DEFAULT" jdbcType="TINYINT" property="isDefault" />
    <result column="BIRTHDAY" jdbcType="DATE" property="birthday" />
    <result column="IS_MARRIED" jdbcType="TINYINT" property="isMarried" />
    <result column="HAVE_CHILDREN" jdbcType="TINYINT" property="haveChildren" />
    <result column="EDUCATION" jdbcType="INTEGER" property="education" />
    <result column="CHARACTER" jdbcType="VARCHAR" property="character" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 23 10:53:21 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 23 10:53:21 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 23 10:53:21 CST 2019.
    -->
    TRADER_CONTACT_ID, TRADER_ID, TRADER_TYPE, IS_ENABLE, SEX, `NAME`, DEPARTMENT, `POSITION`, 
    TELEPHONE, FAX, MOBILE, MOBILE2, EMAIL, QQ, WEIXIN, IS_ON_JOB, IS_DEFAULT, BIRTHDAY, 
    IS_MARRIED, HAVE_CHILDREN, EDUCATION, `CHARACTER`, COMMENTS, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.trader.model.TraderContactGenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 23 10:53:21 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_TRADER_CONTACT
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 23 10:53:21 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_CONTACT
    where TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 23 10:53:21 CST 2019.
    -->
    delete from T_TRADER_CONTACT
    where TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.trader.model.TraderContactGenerateExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 23 10:53:21 CST 2019.
    -->
    delete from T_TRADER_CONTACT
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.trader.model.TraderContactGenerate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 23 10:53:21 CST 2019.
    -->
    insert into T_TRADER_CONTACT (TRADER_CONTACT_ID, TRADER_ID, TRADER_TYPE, 
      IS_ENABLE, SEX, `NAME`, 
      DEPARTMENT, `POSITION`, TELEPHONE, 
      FAX, MOBILE, MOBILE2, 
      EMAIL, QQ, WEIXIN, 
      IS_ON_JOB, IS_DEFAULT, BIRTHDAY, 
      IS_MARRIED, HAVE_CHILDREN, EDUCATION, 
      `CHARACTER`, COMMENTS, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER
      )
    values (#{traderContactId,jdbcType=INTEGER}, #{traderId,jdbcType=INTEGER}, #{traderType,jdbcType=TINYINT}, 
      #{isEnable,jdbcType=TINYINT}, #{sex,jdbcType=TINYINT}, #{name,jdbcType=VARCHAR}, 
      #{department,jdbcType=VARCHAR}, #{position,jdbcType=VARCHAR}, #{telephone,jdbcType=VARCHAR}, 
      #{fax,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{mobile2,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{qq,jdbcType=VARCHAR}, #{weixin,jdbcType=VARCHAR}, 
      #{isOnJob,jdbcType=TINYINT}, #{isDefault,jdbcType=TINYINT}, #{birthday,jdbcType=DATE}, 
      #{isMarried,jdbcType=TINYINT}, #{haveChildren,jdbcType=TINYINT}, #{education,jdbcType=INTEGER}, 
      #{character,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.trader.model.TraderContactGenerate" keyProperty="traderContactId" useGeneratedKeys="true">
    insert into T_TRADER_CONTACT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderType != null">
        TRADER_TYPE,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="sex != null">
        SEX,
      </if>
      <if test="name != null">
        `NAME`,
      </if>
      <if test="department != null">
        DEPARTMENT,
      </if>
      <if test="position != null">
        `POSITION`,
      </if>
      <if test="telephone != null">
        TELEPHONE,
      </if>
      <if test="fax != null">
        FAX,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="mobile2 != null">
        MOBILE2,
      </if>
      <if test="email != null">
        EMAIL,
      </if>
      <if test="qq != null">
        QQ,
      </if>
      <if test="weixin != null">
        WEIXIN,
      </if>
      <if test="isOnJob != null">
        IS_ON_JOB,
      </if>
      <if test="isDefault != null">
        IS_DEFAULT,
      </if>
      <if test="birthday != null">
        BIRTHDAY,
      </if>
      <if test="isMarried != null">
        IS_MARRIED,
      </if>
      <if test="haveChildren != null">
        HAVE_CHILDREN,
      </if>
      <if test="education != null">
        EDUCATION,
      </if>
      <if test="character != null">
        `CHARACTER`,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null">
        #{traderType,jdbcType=TINYINT},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=TINYINT},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        #{department,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="fax != null">
        #{fax,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="mobile2 != null">
        #{mobile2,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="qq != null">
        #{qq,jdbcType=VARCHAR},
      </if>
      <if test="weixin != null">
        #{weixin,jdbcType=VARCHAR},
      </if>
      <if test="isOnJob != null">
        #{isOnJob,jdbcType=TINYINT},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=DATE},
      </if>
      <if test="isMarried != null">
        #{isMarried,jdbcType=TINYINT},
      </if>
      <if test="haveChildren != null">
        #{haveChildren,jdbcType=TINYINT},
      </if>
      <if test="education != null">
        #{education,jdbcType=INTEGER},
      </if>
      <if test="character != null">
        #{character,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.trader.model.TraderContactGenerateExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 23 10:53:21 CST 2019.
    -->
    select count(*) from T_TRADER_CONTACT
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 23 10:53:21 CST 2019.
    -->
    update T_TRADER_CONTACT
    <set>
      <if test="record.traderContactId != null">
        TRADER_CONTACT_ID = #{record.traderContactId,jdbcType=INTEGER},
      </if>
      <if test="record.traderId != null">
        TRADER_ID = #{record.traderId,jdbcType=INTEGER},
      </if>
      <if test="record.traderType != null">
        TRADER_TYPE = #{record.traderType,jdbcType=TINYINT},
      </if>
      <if test="record.isEnable != null">
        IS_ENABLE = #{record.isEnable,jdbcType=TINYINT},
      </if>
      <if test="record.sex != null">
        SEX = #{record.sex,jdbcType=TINYINT},
      </if>
      <if test="record.name != null">
        `NAME` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.department != null">
        DEPARTMENT = #{record.department,jdbcType=VARCHAR},
      </if>
      <if test="record.position != null">
        `POSITION` = #{record.position,jdbcType=VARCHAR},
      </if>
      <if test="record.telephone != null">
        TELEPHONE = #{record.telephone,jdbcType=VARCHAR},
      </if>
      <if test="record.fax != null">
        FAX = #{record.fax,jdbcType=VARCHAR},
      </if>
      <if test="record.mobile != null">
        MOBILE = #{record.mobile,jdbcType=VARCHAR},
      </if>
      <if test="record.mobile2 != null">
        MOBILE2 = #{record.mobile2,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null">
        EMAIL = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.qq != null">
        QQ = #{record.qq,jdbcType=VARCHAR},
      </if>
      <if test="record.weixin != null">
        WEIXIN = #{record.weixin,jdbcType=VARCHAR},
      </if>
      <if test="record.isOnJob != null">
        IS_ON_JOB = #{record.isOnJob,jdbcType=TINYINT},
      </if>
      <if test="record.isDefault != null">
        IS_DEFAULT = #{record.isDefault,jdbcType=TINYINT},
      </if>
      <if test="record.birthday != null">
        BIRTHDAY = #{record.birthday,jdbcType=DATE},
      </if>
      <if test="record.isMarried != null">
        IS_MARRIED = #{record.isMarried,jdbcType=TINYINT},
      </if>
      <if test="record.haveChildren != null">
        HAVE_CHILDREN = #{record.haveChildren,jdbcType=TINYINT},
      </if>
      <if test="record.education != null">
        EDUCATION = #{record.education,jdbcType=INTEGER},
      </if>
      <if test="record.character != null">
        `CHARACTER` = #{record.character,jdbcType=VARCHAR},
      </if>
      <if test="record.comments != null">
        COMMENTS = #{record.comments,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 23 10:53:21 CST 2019.
    -->
    update T_TRADER_CONTACT
    set TRADER_CONTACT_ID = #{record.traderContactId,jdbcType=INTEGER},
      TRADER_ID = #{record.traderId,jdbcType=INTEGER},
      TRADER_TYPE = #{record.traderType,jdbcType=TINYINT},
      IS_ENABLE = #{record.isEnable,jdbcType=TINYINT},
      SEX = #{record.sex,jdbcType=TINYINT},
      `NAME` = #{record.name,jdbcType=VARCHAR},
      DEPARTMENT = #{record.department,jdbcType=VARCHAR},
      `POSITION` = #{record.position,jdbcType=VARCHAR},
      TELEPHONE = #{record.telephone,jdbcType=VARCHAR},
      FAX = #{record.fax,jdbcType=VARCHAR},
      MOBILE = #{record.mobile,jdbcType=VARCHAR},
      MOBILE2 = #{record.mobile2,jdbcType=VARCHAR},
      EMAIL = #{record.email,jdbcType=VARCHAR},
      QQ = #{record.qq,jdbcType=VARCHAR},
      WEIXIN = #{record.weixin,jdbcType=VARCHAR},
      IS_ON_JOB = #{record.isOnJob,jdbcType=TINYINT},
      IS_DEFAULT = #{record.isDefault,jdbcType=TINYINT},
      BIRTHDAY = #{record.birthday,jdbcType=DATE},
      IS_MARRIED = #{record.isMarried,jdbcType=TINYINT},
      HAVE_CHILDREN = #{record.haveChildren,jdbcType=TINYINT},
      EDUCATION = #{record.education,jdbcType=INTEGER},
      `CHARACTER` = #{record.character,jdbcType=VARCHAR},
      COMMENTS = #{record.comments,jdbcType=VARCHAR},
      ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      UPDATER = #{record.updater,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.trader.model.TraderContactGenerate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 23 10:53:21 CST 2019.
    -->
    update T_TRADER_CONTACT
    <set>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null">
        TRADER_TYPE = #{traderType,jdbcType=TINYINT},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=TINYINT},
      </if>
      <if test="sex != null">
        SEX = #{sex,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        `NAME` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        DEPARTMENT = #{department,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        `POSITION` = #{position,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        TELEPHONE = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="fax != null">
        FAX = #{fax,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="mobile2 != null">
        MOBILE2 = #{mobile2,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        EMAIL = #{email,jdbcType=VARCHAR},
      </if>
      <if test="qq != null">
        QQ = #{qq,jdbcType=VARCHAR},
      </if>
      <if test="weixin != null">
        WEIXIN = #{weixin,jdbcType=VARCHAR},
      </if>
      <if test="isOnJob != null">
        IS_ON_JOB = #{isOnJob,jdbcType=TINYINT},
      </if>
      <if test="isDefault != null">
        IS_DEFAULT = #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="birthday != null">
        BIRTHDAY = #{birthday,jdbcType=DATE},
      </if>
      <if test="isMarried != null">
        IS_MARRIED = #{isMarried,jdbcType=TINYINT},
      </if>
      <if test="haveChildren != null">
        HAVE_CHILDREN = #{haveChildren,jdbcType=TINYINT},
      </if>
      <if test="education != null">
        EDUCATION = #{education,jdbcType=INTEGER},
      </if>
      <if test="character != null">
        `CHARACTER` = #{character,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.trader.model.TraderContactGenerate">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 23 10:53:21 CST 2019.
    -->
    update T_TRADER_CONTACT
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_TYPE = #{traderType,jdbcType=TINYINT},
      IS_ENABLE = #{isEnable,jdbcType=TINYINT},
      SEX = #{sex,jdbcType=TINYINT},
      `NAME` = #{name,jdbcType=VARCHAR},
      DEPARTMENT = #{department,jdbcType=VARCHAR},
      `POSITION` = #{position,jdbcType=VARCHAR},
      TELEPHONE = #{telephone,jdbcType=VARCHAR},
      FAX = #{fax,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      MOBILE2 = #{mobile2,jdbcType=VARCHAR},
      EMAIL = #{email,jdbcType=VARCHAR},
      QQ = #{qq,jdbcType=VARCHAR},
      WEIXIN = #{weixin,jdbcType=VARCHAR},
      IS_ON_JOB = #{isOnJob,jdbcType=TINYINT},
      IS_DEFAULT = #{isDefault,jdbcType=TINYINT},
      BIRTHDAY = #{birthday,jdbcType=DATE},
      IS_MARRIED = #{isMarried,jdbcType=TINYINT},
      HAVE_CHILDREN = #{haveChildren,jdbcType=TINYINT},
      EDUCATION = #{education,jdbcType=INTEGER},
      `CHARACTER` = #{character,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER}
  </update>

  <select id="getContactInfo" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" />
    FROM T_TRADER_CONTACT A
    WHERE     A.TRADER_ID = #{traderId,jdbcType=INTEGER}
          AND A.TRADER_TYPE = #{traderType,jdbcType=TINYINT}
          AND A.IS_ENABLE = 1
          AND A.NAME = #{name,jdbcType=VARCHAR}
           <if test="telephone != null">
            AND A.TELEPHONE = #{telephone,jdbcType=VARCHAR}
          </if> 
          AND A.MOBILE = #{mobile,jdbcType=VARCHAR}
    LIMIT 1
  </select>

  <select id="getTraderDefaultContact" parameterType="java.lang.Integer" resultType="com.vedeng.trader.model.TraderContactGenerate">
    SELECT
      <include refid="Base_Column_List" />
    FROM T_TRADER_CONTACT A
    WHERE IS_DEFAULT = 1 AND A.IS_ENABLE = 1 and A.TRADER_TYPE = 1
    <if test="traderId != null">
      AND A.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </if>
    limit 1
  </select>
  <select id="getByTraderIdAndMobileNo" parameterType="map" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
    from
      T_TRADER_CONTACT
    where
      TRADER_ID = #{traderId ,jdbcType=INTEGER}
      and MOBILE like CONCAT('%',#{mobileNo,jdbcType=VARCHAR},'%' )
      and IS_ENABLE = #{enable ,jdbcType=TINYINT}
  </select>

<!--  <select id="getTraderContact" parameterType="java.lang.Integer" resultType="com.smallhospital.dto.ElTraderInfo">-->
<!--    SELECT-->
<!--    A.NAME AS contact,-->
<!--    A.MOBILE AS contactNumber,-->
<!--    A.IS_DEFAULT AS isDefault-->
<!--    FROM T_TRADER_CONTACT A-->
<!--    WHERE A.IS_ENABLE = 1 and A.TRADER_TYPE = 1-->
<!--    <if test="traderId != null">-->
<!--      AND A.TRADER_ID = #{traderId,jdbcType=INTEGER}-->
<!--    </if>-->
<!--  </select>-->

  <select id="getTraderIdsByPhone" resultType="java.lang.Integer">
    SELECT
        TRADER_ID
    FROM
        `T_TRADER_CONTACT`
    WHERE
        MOBILE = #{mobileNo,jdbcType=VARCHAR}
        AND IS_ENABLE = 1
        AND TRADER_TYPE = 1
    GROUP BY
        TRADER_ID
  </select>

  <select id="getTraderContactList"  resultType="com.vedeng.trader.model.vo.TraderContactVo">
    select
    tc.TRADER_CONTACT_ID, tc.TRADER_ID, tc.TRADER_TYPE, tc.IS_ENABLE, tc.SEX, tc.NAME, tc.DEPARTMENT, tc.POSITION,
    tc.TELEPHONE, tc.FAX, tc.MOBILE, tc.MOBILE2, tc.EMAIL, tc.QQ, tc.WEIXIN, tc.IS_ON_JOB, tc.IS_DEFAULT, tc.BIRTHDAY,
    tc.IS_MARRIED, tc.HAVE_CHILDREN, tc.EDUCATION, tc.`CHARACTER`, tc.COMMENTS, tc.ADD_TIME, tc.CREATOR, tc.MOD_TIME,
    tc.UPDATER, COALESCE(wa.IS_VEDENG_JX,wa1.IS_VEDENG_JX,- 1) AS IS_VEDENG_JX,
    COALESCE(wa.IS_VEDENG_MEMBER,wa1.IS_VEDENG_MEMBER,- 1) AS IS_VEDENG_MEMBER,tc.IS_TOP
    from T_TRADER_CONTACT tc
	LEFT JOIN T_WEB_ACCOUNT wa ON tc.MOBILE = wa.MOBILE
      LEFT JOIN T_WEB_ACCOUNT wa1 ON tc.MOBILE2 = wa1.MOBILE
    where
        tc.TRADER_ID = #{traderId,jdbcType=INTEGER}
        and tc.TRADER_TYPE = 1
    GROUP BY tc.IS_TOP,tc.TRADER_CONTACT_ID
    ORDER BY tc.IS_TOP DESC
  </select>
  <select id="getTraderContactListInWebAccountByTraderId" resultType="com.vedeng.trader.model.TraderContactGenerate">
        select c.*
            from T_TRADER_CONTACT c
         join T_WEB_ACCOUNT a on c.MOBILE = a.MOBILE and c.TRADER_ID = a.TRADER_ID and a.IS_ENABLE = 1 and c.IS_ENABLE = 1
            where c.TRADER_ID = #{traderId,jdbcType=INTEGER}
  </select>

  <update id="updateContactStick">
		update T_TRADER_CONTACT
		set IS_TOP = #{isTop,jdbcType=BIT}
		where TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER}
	</update>

  <select id="getIsVedengMemberByTraderMobile" resultType="java.lang.Integer">
    SELECT
      COALESCE(IS_VEDENG_MEMBER,- 1) AS IS_VEDENG_MEMBER
    FROM
      T_WEB_ACCOUNT
    WHERE
      IS_ENABLE = 1
      AND MOBILE = #{mobile,jdbcType=VARCHAR}
    GROUP BY
      MOBILE
  </select>
</mapper>