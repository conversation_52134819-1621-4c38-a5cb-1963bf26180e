package com.vedeng.mobile.system.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 地区信息表
 *
 * <AUTHOR>
 * @TableName T_REGION
 */
@Data
public class Region implements Serializable {


    private Long regionId;

    private Long parentId;

    /**
     * 地区名
     */
    private String regionName;

    /**
     * 地区类型
     */
    private Integer regionType;

    /**
     * 办事处的id,这里有一个bug,同一个省不能有多个办事处,该字段只记录最新的那个办事处的id
     */
    private Integer agencyId;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 20200728最新数据
     */
    private String regionFullName;

    /**
     *
     */
    private String regionCode;

    /**
     *
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}