package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeProfitLossCommand;
import com.vedeng.erp.kingdee.dto.KingDeeProfitLossDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeProfitLossDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * @description:
 * @author: yana.jiang
 * @date: 2022/11/10
 */
@Mapper(componentModel = "spring")
public interface KingDeeProfitLossCommandConvertor extends BaseCommandMapStruct<KingDeeProfitLossCommand, KingDeeProfitLossDto> {

    @Mapping(target = "FId", source = "FId")
    @Mapping(target = "FBillTypeId.FNumber", source = "FBillTypeId")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "FStockOrgId.FNumber", source = "FStockOrgId")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtId")
    @Mapping(target = "FOwnerTypeIdHead", source = "FOwnerTypeIdHead")
    @Mapping(target = "FDate", source = "FDate")
    @Mapping(target = "FDeptId.FNumber", source = "FDeptId")
    KingDeeProfitLossCommand toCommand(KingDeeProfitLossDto dto);

    @Mapping(target = "FMaterialId.FNumber", source = "FMaterialId")
    @Mapping(target = "FStockId.FNumber", source = "FStockId")
    @Mapping(target = "FStockStatusId.FNumber", source = "FStockStatusId")
    @Mapping(target = "FBaseLossQty", source = "FBaseLossQty")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "f_QZOK_PCH", source = "FQzokPch")
    @Mapping(target = "f_QZOK_XLH", source = "FQzokXlh")
    @Mapping(target = "f_QZOK_SQLX", source = "FQzokSqlx")
    @Mapping(target = "f_QZOK_SFZF", source = "FQzokSfzf")
    KingDeeProfitLossCommand.KingDeeProfitLossDetailCommand toCommand(KingDeeProfitLossDetailDto dto);
}
