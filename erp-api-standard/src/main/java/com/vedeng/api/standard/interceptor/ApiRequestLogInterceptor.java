package com.vedeng.api.standard.interceptor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

/**
 * API请求日志拦截器
 * 记录API请求的详细信息和性能指标
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
public class ApiRequestLogInterceptor implements HandlerInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(ApiRequestLogInterceptor.class);
    
    private static final String REQUEST_START_TIME = "requestStartTime";
    private static final String REQUEST_ID = "requestId";
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 记录请求开始时间
        long startTime = System.currentTimeMillis();
        request.setAttribute(REQUEST_START_TIME, startTime);
        
        // 生成或获取请求ID
        String requestId = getOrGenerateRequestId(request);
        request.setAttribute(REQUEST_ID, requestId);
        
        // 记录请求开始日志
        logRequestStart(request, requestId);
        
        return true;
    }
    
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 在这里可以记录Controller处理完成的信息
        // 通常不需要特殊处理
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 计算请求处理时间
        Long startTime = (Long) request.getAttribute(REQUEST_START_TIME);
        if (startTime != null) {
            long executionTime = System.currentTimeMillis() - startTime;
            
            String requestId = (String) request.getAttribute(REQUEST_ID);
            
            // 记录请求完成日志
            logRequestCompletion(request, response, requestId, executionTime, ex);
        }
    }
    
    /**
     * 获取或生成请求ID
     */
    private String getOrGenerateRequestId(HttpServletRequest request) {
        // 首先尝试从请求头获取
        String requestId = request.getHeader("X-Request-ID");
        if (requestId != null && !requestId.trim().isEmpty()) {
            return requestId;
        }
        
        // 尝试从请求参数获取
        requestId = request.getParameter("requestId");
        if (requestId != null && !requestId.trim().isEmpty()) {
            return requestId;
        }
        
        // 生成新的请求ID
        return UUID.randomUUID().toString();
    }
    
    /**
     * 记录请求开始日志
     */
    private void logRequestStart(HttpServletRequest request, String requestId) {
        try {
            String method = request.getMethod();
            String uri = request.getRequestURI();
            String queryString = request.getQueryString();
            String clientIp = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            
            StringBuilder logMessage = new StringBuilder();
            logMessage.append("API请求开始: ");
            logMessage.append("requestId=").append(requestId);
            logMessage.append(", method=").append(method);
            logMessage.append(", uri=").append(uri);
            
            if (queryString != null && !queryString.isEmpty()) {
                logMessage.append(", queryString=").append(queryString);
            }
            
            logMessage.append(", clientIp=").append(clientIp);
            
            if (userAgent != null && !userAgent.isEmpty()) {
                logMessage.append(", userAgent=").append(userAgent.length() > 100 ? userAgent.substring(0, 100) + "..." : userAgent);
            }
            
            logger.info(logMessage.toString());
            
        } catch (Exception e) {
            logger.warn("记录请求开始日志失败: requestId={}", requestId, e);
        }
    }
    
    /**
     * 记录请求完成日志
     */
    private void logRequestCompletion(HttpServletRequest request, HttpServletResponse response, 
                                     String requestId, long executionTime, Exception ex) {
        try {
            String method = request.getMethod();
            String uri = request.getRequestURI();
            int status = response.getStatus();
            
            StringBuilder logMessage = new StringBuilder();
            logMessage.append("API请求完成: ");
            logMessage.append("requestId=").append(requestId);
            logMessage.append(", method=").append(method);
            logMessage.append(", uri=").append(uri);
            logMessage.append(", status=").append(status);
            logMessage.append(", executionTime=").append(executionTime).append("ms");
            
            if (ex != null) {
                logMessage.append(", exception=").append(ex.getClass().getSimpleName());
                logMessage.append(", exceptionMessage=").append(ex.getMessage());
                logger.error(logMessage.toString(), ex);
            } else {
                logger.info(logMessage.toString());
            }
            
            // 记录性能警告
            if (executionTime > 5000) {
                logger.warn("API请求执行时间过长: requestId={}, executionTime={}ms", requestId, executionTime);
            }
            
        } catch (Exception e) {
            logger.warn("记录请求完成日志失败: requestId={}", requestId, e);
        }
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        String remoteAddr = request.getRemoteAddr();
        return remoteAddr != null ? remoteAddr : "unknown";
    }
}
