package com.vedeng.price.service;

import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.price.api.price.dto.price.PriceInfoResponseDto;
import com.vedeng.price.dto.GoodSalePrice;

import java.util.List;
import java.util.Map;

public interface PriceInfoDealWithService {

    Map<String, String> dealWithGoodsSalePrice(Integer traderId, List<GoodSalePrice> goodSalePrices);

    Map<String,String> dealWithGoodsSalePrice(Integer traderId,List<GoodSalePrice>  goodSalePriceList, List<PriceInfoResponseDto> priceInfoResponseDtos);

    void dealWithCostPrice(List<SaleorderGoods> saleorderGoodList, List<PriceInfoResponseDto> priceInfoResponseDtos);

}
