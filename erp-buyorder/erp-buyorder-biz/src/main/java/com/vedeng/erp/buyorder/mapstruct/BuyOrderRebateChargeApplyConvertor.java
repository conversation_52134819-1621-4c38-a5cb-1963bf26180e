package com.vedeng.erp.buyorder.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.buyorder.domain.entity.BuyOrderRebateChargeApplyEntity;
import com.vedeng.erp.buyorder.dto.BuyOrderRebateChargeApplyDto;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.buyorder.mapstruct
 * @Date 2023/11/24 10:43
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, builder = @Builder(disableBuilder = true))
public interface BuyOrderRebateChargeApplyConvertor extends BaseMapStruct<BuyOrderRebateChargeApplyEntity, BuyOrderRebateChargeApplyDto> {
}
