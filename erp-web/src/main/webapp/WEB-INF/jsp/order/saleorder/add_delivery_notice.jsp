<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增发货通知" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/add_delivery_notice.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%= basePath %>static/js/region/index.js?rnd=${resourceVersionKey}'></script>
<div class="content mt10 ">
    <form action="${pageContext.request.contextPath}/order/saleorder/saveDeliveryNotice.do" method="post" id="editForm">
        <%--<c:if test="${invoiceModifyflag ne 1}">--%>
            <div class="parts">
                <div class="title-container">
                    <div class="table-title nobor">产品信息</div>
                </div>
                <table class="table  table-bordered table-striped table-condensed table-centered">
                    <thead>
                    <tr>
                            <th style="width:50px">序号</th>
                            <th style="width:70px">产品名称</th>
                            <th style="width:80px">品牌</th>
                            <th style="width:70px">型号</th>
                            <th style="width:80px">单价</th>
                            <th style="width:70px">本次发货数量/剩余数量</th>
                            <th style="width:35px">单位</th>
                    </tr>
                    </thead>
                    <tbody>
                    <c:set var="num" value="0"></c:set>
                    <c:set var="count" value="0"></c:set>
                    <c:set var="totleMoney" value="0.00"></c:set>
                    <c:set var="id_str" value=""></c:set>
                    <c:forEach var="list" items="${saleorderGoodsList}" varStatus="staut">
                        <c:if test="${list.isDelete eq 0}">
                            <%--<c:set var="count" value="${count+1}"></c:set>
                            <c:set var="num" value="${num + list.num}"></c:set>
                            <c:set var="totleMoney" value="${totleMoney + (list.price * list.num)}"></c:set>
                            <c:set var="id_str" value="${id_str}_${list.saleorderGoodsId}"></c:set>--%>
                            <tr>
                                <c:set var="lwNum" value="0"></c:set>
                                <td>
                                    <%--<c:if test="${(empty list.buyNum) or (list.buyNum eq 0)}">
                                        <c:set var="lwNum" value="${lwNum + 1}"></c:set>
                                        <input type="checkbox" name="goodsCheckName" onclick="goodsCheckClick(this);" value="${list.saleorderGoodsId}" class="f_left" autocomplete="off"/>
                                    </c:if>--%>
                                    <%--<span class="f_left">--%>${staut.count}<%--</span>--%>
                                        <input type="hidden" name="thisTimeDeliveryNum" >

                                </td>
                                <td class="text-left">
                                    <div class="customername pos_rel">
                                        <c:choose>
                                            <c:when test="${list.isDelete eq 1}">
                                                <span>${newSkuInfosMap[list.sku].SHOW_NAME}<br/></span>
                                                <span>${newSkuInfosMap[list.sku].SKU_NO} <br>${newSkuInfosMap[list.sku].MATERIAL_CODE}</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="font-blue"><a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${list.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${list.goodsId}","title":"产品信息"}'>${newSkuInfosMap[list.sku].SHOW_NAME}</a>&nbsp;<%--<i class="iconbluemouth contorlIcon"></i>--%><br/></span>
                                                <span>${newSkuInfosMap[list.sku].SKU_NO} <br>${newSkuInfosMap[list.sku].MATERIAL_CODE}</span>

                                                <c:set var="skuNo" value="${list.sku}"></c:set>
                                                <%--<%@ include file="../../common/new_sku_common_tip.jsp" %>--%>
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </td>
                                <td>${newSkuInfosMap[list.sku].BRAND_NAME}</td>
                                <td>${newSkuInfosMap[list.sku].MODEL}</td>
                                <td>${list.price}</td>
                                <td class="thisTimeDeliveryNumTd"><input alt1="${list.saleorderGoodsId}" alt2="${list.goodsId}" style="width: 20%">/<span>${list.num}</span></td>
                                <td>${newSkuInfosMap[list.sku].UNIT_NAME}</td>
                            </tr>
                        </c:if>
                    </c:forEach>
                    <c:if test="${empty saleorderGoodsList}">
                        <tr>
                            <td colspan="7">暂无产品信息！</td>
                        </tr>
                    </c:if>
<%--                    <tr style="background: #eaf2fd;">
                        <td colspan="7" class="text-left">
                            <c:if test="${lwNum > 0}">
                                <input type="checkbox" name="goodsCheckAllName" id="goodsCheckAllName" onclick="goodsCheckAllClick(this);" autocomplete="off"/><span>全选</span>
                                <span style="display: none;"> <!-- 弹框 -->
										<div class="title-click nobor  pop-new-data" id="saleGoodsDeliveryDirect"></div>
									</span>
                                <a onclick="updateSaleGoodsInit(${saleorder.saleorderId});">批量修改</a>
                                &nbsp;&nbsp;
                            </c:if>
                            总件数<span class="font-red">${num}</span>， 总金额
                            <span class="font-red"><fmt:formatNumber type="number" value="${totleMoney}" pattern="0.00" maxFractionDigits="2" /></span>
                            <input type="hidden" id="goodsTotleMoney"  value="${totleMoney}">
                        </td>
                    </tr>--%>
                    </tbody>
                </table>
            </div>
        <%--</c:if>--%>
        <div class="parts content1 ">
            <div class="formtitle mt10">
                收货信息
            </div>
            <ul class="payplan">
                <li>
                    <div class="infor_name infor_name72">
                        <span>*</span>
                        <label>收货客户</label>
                    </div>
                    <c:choose>
                        <c:when test="${orderType eq 5 }">
                            <div class="f_left  customername pos_rel">
		                            <span class="font-blue">
		                            <a class="addtitle" href="javascript:void(0);"
                                       tabTitle='{"num":"viewcustomer${customer.traderCustomerId}",
												"link":"./trader/customer/baseinfo.do?traderCustomerId=${customer.traderCustomerId}&traderId=${saleorder.traderId}",
												"title":"客户信息"}'>
                                            ${customer.name}</a></span>
                            </div>
                        </c:when>
                        <c:otherwise>
                            <div class="f_left inputfloat">
                                <span class=" mr10 mt3" id="trader_name_span_1">${saleorder.takeTraderName}</span>
                                <input type="hidden" name="takeTraderId" id="trader_id_1" value="${saleorder.takeTraderId}">
                                <input type="hidden" name="takeTraderName" id="trader_name_1" value="${saleorder.takeTraderName}">
                                <span class="bt-bg-style bt-small bg-light-blue pop-new-data" layerParams='{"width":"800px","height":"300px","title":"编辑收货信息","link":"${pageContext.request.contextPath}/trader/customer/searchCustomerList.do?indexId=1&searchTraderName="}'>重新搜索</span>
                            </div>
                        </c:otherwise>
                    </c:choose>
                </li>
                <c:if test="${orderType ne 5 }">
                    <li>
                        <div class="infor_name infor_name72">
                            <span>*</span>
                            <label>收货联系人</label>
                        </div>
                        <div class="f_left inputfloat">
                            <select class="input-xx" id="contact_1" name="takeTraderContactId">
                                <option value="0">请选择</option>
                                <c:if test="${not empty takeTraderContactList}">
                                    <c:forEach items="${takeTraderContactList}" var="list" varStatus="status">
                                        <option value="${list.traderContactId}" <c:if test="${list.traderContactId eq saleorder.takeTraderContactId}">selected="selected"</c:if>>
                                                ${list.name}/${list.telephone}/${list.mobile}
                                        </option>
                                    </c:forEach>
                                </c:if>
                            </select>
                            <input type="hidden" name="takeTraderContactName">
                            <input type="hidden" name="takeTraderContactTelephone">
                            <input type="hidden" name="takeTraderContactMobile">
                            <span class="mt4 pop-new-data font-blue" id="add_contact_1" layerParams='{"width":"430px","height":"220px","title":"添加联系人","link":"./addContact.do?traderId=${saleorder.takeTraderId}&traderCustomerId=${takeTraderCustomerInfo.traderCustomerId}&indexId=1"}'>添加联系人</span>
                            <div id="takeTraderContactIdMsg" style="clear:both"></div>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name infor_name72">
                            <span>*</span>
                            <label>收货地址</label>
                        </div>
                        <div class="f_left inputfloat">
                            <select class="input-xx" id="address_1" name="takeTraderAddressId">
                                <option value="0">请选择</option>
                                <c:if test="${not empty takeTraderAddressList}">
                                    <c:forEach items="${takeTraderAddressList}" var="list" varStatus="status">
                                        <option value="${list.traderAddress.traderAddressId}" <c:if test="${list.traderAddress.traderAddressId eq saleorder.takeTraderAddressId}">selected="selected"</c:if>>${list.area}/${list.traderAddress.address}</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                            <input type="hidden" name="takeTraderArea">
                            <input type="hidden" name="takeTraderAddress">
                            <span class="mt4 pop-new-data font-blue" id="add_address_1" layerParams='{"width":"600px","height":"200px","title":"添加地址","link":"./addAddress.do?traderId=${saleorder.takeTraderId}&indexId=1"}'>添加地址</span>
                            <div id="takeTraderAddressIdMsg" style="clear:both"></div>
                        </div>
                    </li>
                </c:if>
                <!-- 耗材订单 -->
                <c:if test="${orderType eq 5 }">
                    <li>
                        <div class="infor_name infor_name72 mt0">
                            <span>*</span>
                            <label>收货联系人</label>
                        </div>
                        <div class="f_left">
                            <input maxLength="64" type="text" id="takeTraderContactName_5" name="takeTraderContactName" value="${saleorder.takeTraderContactName}" placeholder="请输入收货联系人" />
                            <div id="5_takeTraderContactName" style="clear:both"></div>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name infor_name72 mt0">
                            <span>*</span>
                            <label>收货手机号</label>
                        </div>
                        <div class="f_left">
                            <input maxLength="11" type="text" id="takeTraderContactMobile_5" name="takeTraderContactMobile" value="${saleorder.takeTraderContactMobile}" placeholder="请输入收货手机号" />
                            <div id="5_takeTraderContactMobile" style="clear:both"></div>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name infor_name72 mt0">
                            <span>*</span>
                            <label>收货地址</label>
                        </div>
                        <div class="f_left">
                            <select  id="takeTraderAddressId-province" onchange="changeArea('takeTraderAddressId', 'takeTraderArea_5', 1)" >
                                <option value="0">请选择</option>
                                <c:if test="${not empty provinceList }">
                                    <c:forEach items="${provinceList }" var="prov">
                                        <option value="${prov.regionId }" <c:if test="${takeTraderAddressIdProvince eq prov.regionId }">selected="selected"</c:if> >${prov.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                            <select  id="takeTraderAddressId-city" onchange="changeArea('takeTraderAddressId', 'takeTraderArea_5', 2)">
                                <option value="0">请选择</option>
                                <c:if test="${not empty takeTraderAddressCityList }">
                                    <c:forEach items="${takeTraderAddressCityList }" var="cy">
                                        <option value="${cy.regionId }" <c:if test="${takeTraderAddressIdCity eq cy.regionId }">selected="selected"</c:if> >${cy.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                            <select  id="takeTraderAddressId-zone" onchange="changeArea('takeTraderAddressId', 'takeTraderArea_5', 3)">
                                <option value="0">请选择</option>
                                <c:if test="${not empty takeTraderAddressZoneList }">
                                    <c:forEach items="${takeTraderAddressZoneList }" var="zo">
                                        <option value="${zo.regionId }" <c:if test="${takeTraderAddressIdZone eq zo.regionId }">selected="selected"</c:if> >${zo.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                            <input type="hidden" id="takeTraderArea_5" name="takeTraderArea" value="${saleorder.takeTraderArea}" />
                            <input type="hidden" id="takeTraderAddressId" name="takeTraderAreaId" value="${takeTraderAddressIdZone}" />
                            <input maxLength="256" class="input-xx" id="takeTraderAddress_5" type="text" name="takeTraderAddress" value="${saleorder.takeTraderAddress}" placeholder="请输入收货地址" />
                            <div id="5_takeTraderAddress" style="clear:both"></div>
                        </div>
                    </li>
                </c:if>

                <li>
                    <div class="infor_name infor_name72">
                        <span>*</span>
                        <label>发货方式</label>
                    </div>
                    <div class="f_left inputfloat">
                        <select class="input-middle" name="deliveryType" id="deliveryTypeSelect">
                            <option value="-1">请选择</option>
                            <c:forEach var="list" items="${deliveryTypes}">
                                <option value="${list.sysOptionDefinitionId}" <c:if test="${saleorder.deliveryType == list.sysOptionDefinitionId}">selected="selected"</c:if> >${list.title}</option>
                            </c:forEach>
                        </select>
                    </div>
                    <div id="deliveryTypeMsg" style="clear:both;"></div>
                </li>

                <c:if test="${saleorder.orderType != 5}">
                    <c:choose>
                        <c:when test="${saleorder.isPrintout == -1}">
                            <li id="is_print_li">
                                <div>
                                    <div style="height: 25px">
                                        <span style="color: red;text-indent: 15px">  *</span>
                                        <label style="width: 150px">是否打印随货出库单</label>
                                        <select  id="is_print" style="height: auto" onchange="changeIsPrintout()" >
                                            <option value="-1" selected = selected>请选择</option>
                                            <option value="1">是</option>
                                            <option value="2">否</option>
                                        </select>
                                    </div>
                                    <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                    <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">

                                    <div id="isPrintoutMsg" style="clear:both;"></div>
                                </div>
                                <input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
                            </li>
                        </c:when>

                        <c:when test="${saleorder.isPrintout == 0}">
                            <li id="is_print_li">
                                <div>
                                    <div style="height: 25px">
                                        <span style="color: red;text-indent: 15px">  *</span>
                                        <label style="width: 150px">是否打印随货出库单</label>
                                        <select  id="is_print" style="height: auto" onchange="changeIsPrintout()" >
                                            <option value="-1">请选择</option>
                                            <option value="1">是</option>
                                            <option value="2" selected = selected>否</option>
                                        </select>
                                    </div>
                                    <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                    <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">

                                    <div id="isPrintoutMsg" style="clear:both;"></div>
                                </div>
                                <input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
                            </li>
                        </c:when>
                        <c:when test="${saleorder.isPrintout == 1}">
                            <li id="is_print_li">
                                <div>
                                    <div style="height: 25px">
                                        <span style="color: red;text-indent: 15px">  *</span>
                                        <label style="width: 150px">是否打印随货出库单</label>
                                        <select  id="is_print" style="height: auto" onchange="changeIsPrintout()" >
                                            <option value="-1">请选择</option>
                                            <option value="1" selected = selected>是</option>
                                            <option value="2">否</option>
                                        </select>
                                    </div>
                                    <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                    <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">

                                    <div id="isPrintoutMsg" style="clear:both;"></div>
                                </div>
                                <input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
                            </li>

                            <li id='is_price_li'>
                                <div style="height: 25px">
                                    <span style="color: red;text-indent: 15px">*</span>
                                    <label style="width: 150px">随货出库单是否自带价格</label>
                                    <select  id='is_price' name = "isPrintout" style='height: auto' onchange='changeIsPrice()' >
                                        <option value="0">请选择</option>
                                        <option value="1" selected = selected>是</option>
                                        <option value="2">否</option>
                                    </select>
                                </div>
                                <div id="isPriceMsg" style="clear:both;"></div>
                            </li>
                        </c:when>

                        <c:when test="${saleorder.isPrintout == 2}">
                            <li id="is_print_li">
                                <div>
                                    <div style="height: 25px">
                                        <span style="color: red;text-indent: 15px">  *</span>
                                        <label style="width: 150px">是否打印随货出库单</label>
                                        <select  id="is_print" style="height: auto" onchange="changeIsPrintout()" >
                                            <option value="-1">请选择</option>
                                            <option value="1" selected = selected>是</option>
                                            <option value="2">否</option>
                                        </select>
                                    </div>
                                    <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                    <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">

                                    <div id="isPrintoutMsg" style="clear:both;"></div>
                                </div>
                                <input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
                            </li>

                            <li id='is_price_li'>
                                <div style="height: 25px">
                                    <span style="color: red;text-indent: 15px">*</span>
                                    <label style="width: 150px">随货出库单是否自带价格</label>
                                    <select  id='is_price' name = "isPrintout" style='height: auto' onchange='changeIsPrice()' >
                                        <option value="0">请选择</option>
                                        <option value="1">是</option>
                                        <option value="2" selected = selected>否</option>
                                    </select>
                                </div>
                                <div id="isPriceMsg" style="clear:both;"></div>
                            </li>
                        </c:when>

                        <c:when test="${saleorder.isPrintout == 3}">
                            <li id="is_print_li">
                                <div>
                                    <div style="height: 25px">
                                        <span style="color: red;text-indent: 15px">  *</span>
                                        <label style="width: 150px">是否打印随货出库单</label>
                                        <select  id="is_print" style="height: auto" onchange="changeIsPrintout()" >
                                            <option value="-1">请选择</option>
                                            <option value="1" selected = selected>是</option>
                                            <option value="2">否</option>
                                        </select>
                                    </div>
                                    <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                    <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">

                                    <div id="isPrintoutMsg" style="clear:both;"></div>
                                </div>
                                <input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
                            </li>
                        </c:when>
                        <c:otherwise>
                            <li id="is_print_li">
                                <div>
                                    <div style="height: 25px">
                                        <span style="color: red;text-indent: 15px">  *</span>
                                        <label style="width: 150px">是否打印随货出库单</label>
                                        <select  id="is_print" style="height: auto" onchange="changeIsPrintout()" >
                                            <option value="-1" selected = selected>请选择</option>
                                            <option value="1">是</option>
                                            <option value="2">否</option>
                                        </select>
                                    </div>
                                    <input type="hidden" id="is_scientificDept" value="${isScientificDep}">
                                    <input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">

                                    <div id="isPrintoutMsg" style="clear:both;"></div>
                                </div>
                                <input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
                            </li>
                        </c:otherwise>
                    </c:choose>
                </c:if>

                <li>
                    <div class="infor_name infor_name72">
                        <label>指定物流公司</label>
                    </div>
                    <div class="f_left inputfloat">
                        <select class="input-middle" name="logisticsId">
                            <option value="0">请选择</option>
                            <c:forEach var="list" items="${logisticsList}">
                                <c:if test="${list.isEnable == 1}">
                                    <option value="${list.logisticsId}" <c:if test="${saleorder.logisticsId == list.logisticsId}">selected="selected"</c:if> >${list.name}</option>
                                </c:if>
                            </c:forEach>
                        </select>
                    </div>
                </li>
                <li>
                    <div class="infor_name infor_name72">
                        <label>运费说明</label>
                    </div>
                    <div class="f_left inputfloat">
                        <select class="input-middle" name="freightDescription">
                            <option value="0">请选择</option>
                            <c:forEach var="list" items="${freightDescriptions}">
                                <option value="${list.sysOptionDefinitionId}" <c:if test="${saleorder.freightDescription == list.sysOptionDefinitionId}">selected="selected"</c:if> >${list.title}</option>
                            </c:forEach>
                        </select>
                    </div>
                </li>
                <li>
                    <div class="infor_name infor_name72">
                        <label>物流备注</label>
                    </div>
                    <div class="f_left inputfloat">
                        <input type="text" name="logisticsComments" id="logisticsComments" value="${saleorder.logisticsComments}" placeholder="对内使用，适用于向物流部同事告知发货要求，默认同步客户信息中物流备注" class="input-xx" />
                    </div>
                </li>
            </ul>
        </div>


        <div class="add-tijiao" style="margin-bottom:50px;">
            <input type="hidden" name="saleorderId" value="${saleorder.saleorderId}">
            <input type="hidden" id="id_str" name="id_str" value="${id_str}">
            <input type="hidden" name="formToken" value="${formToken}"/>
            <button type="button" class="bt-bg-style bg-deep-green" onclick="editSubmit(${saleorder.orderType});">提交</button>
        </div>
    </form>
</div>
<%@ include file="../../common/footer.jsp"%>