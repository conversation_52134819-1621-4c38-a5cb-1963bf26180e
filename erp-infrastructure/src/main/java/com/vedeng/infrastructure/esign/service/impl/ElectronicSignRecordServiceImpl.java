package com.vedeng.infrastructure.esign.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.infrastructure.esign.domain.dto.ElectronicSignRecordDto;
import com.vedeng.infrastructure.esign.domain.entity.ElectronicSignRecordEntity;
import com.vedeng.infrastructure.esign.enums.EsignStatusEnums;
import com.vedeng.infrastructure.esign.mapper.ElectronicSignRecordMapper;
import com.vedeng.infrastructure.esign.mapstruct.ElectronicSignRecordConvertor;
import com.vedeng.infrastructure.esign.service.ElectronicSignRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class ElectronicSignRecordServiceImpl implements ElectronicSignRecordService {

    @Autowired
    ElectronicSignRecordMapper electronicSignRecordMapper;
    @Autowired
    ElectronicSignRecordConvertor electronicSignRecordConvertor;


    @Override
    public ElectronicSignRecordEntity selectByPrimaryKey(Integer electronicSignRecordId) {
        return electronicSignRecordMapper.selectByPrimaryKey(electronicSignRecordId);
    }

    @Override
    public void sendSuccess(ElectronicSignRecordDto electronicSignRecordDto) {
        electronicSignRecordDto.setSignStatus(EsignStatusEnums.SEND_SUCCESS.getStatus());
        this.create(electronicSignRecordDto);
    }

    @Override
    public void sendFail(ElectronicSignRecordDto electronicSignRecordDto) {
        electronicSignRecordDto.setSignStatus(EsignStatusEnums.SEND_FAIL.getStatus());
        String[] split = StrUtil.split(electronicSignRecordDto.getErrorMsg(), 2000);
        electronicSignRecordDto.setErrorMsg(split[0]);
        this.create(electronicSignRecordDto);
    }

    @Override
    public void signSuccess(String businessId, Integer businessType) {
        List<ElectronicSignRecordEntity> byBusinessIdAndBusinessType = electronicSignRecordMapper.findByBusinessIdAndBusinessType(businessId, businessType);
        if (CollUtil.isNotEmpty(byBusinessIdAndBusinessType)) {
            byBusinessIdAndBusinessType.forEach(e -> {
                e.setSignStatus(EsignStatusEnums.SIGN_SUCCESS.getStatus());
                e.setErrorMsg("");
                this.update(e);
            });
        }

    }

    @Override
    public void signFail(String businessId, Integer businessType, String errorMsg) {
        List<ElectronicSignRecordEntity> byBusinessIdAndBusinessType = electronicSignRecordMapper.findByBusinessIdAndBusinessType(businessId, businessType);
        if (CollUtil.isNotEmpty(byBusinessIdAndBusinessType)) {
            byBusinessIdAndBusinessType.forEach(e -> {
                e.setSignStatus(EsignStatusEnums.SIGN_FAIL.getStatus());
                String[] split = StrUtil.split(errorMsg, 2000);
                e.setErrorMsg(split[0]);
                this.update(e);
            });
        }
    }

    @Override
    public void msgRetryNumAdd(ElectronicSignRecordEntity electronicSignRecordEntity) {
        electronicSignRecordMapper.addRetryNum(electronicSignRecordEntity.getElectronicSignRecordId());
    }

    @Override
    public List<ElectronicSignRecordEntity> getFail(Integer businessType) {
        return electronicSignRecordMapper.findBySignStatusIn(CollUtil.newArrayList(EsignStatusEnums.SEND_FAIL.getStatus(),
                EsignStatusEnums.SIGN_FAIL.getStatus()), businessType);
    }

    @Override
    public  List<ElectronicSignRecordEntity> getElectronicSignRecord(String businessId, Integer businessType) {
        return electronicSignRecordMapper.findByBusinessIdAndBusinessType(businessId, businessType);
    }

    private void create(ElectronicSignRecordDto electronicSignRecordDto) {
        if (electronicSignRecordDto.getBusinessType() != 5) {
            return;
        }
        ElectronicSignRecordEntity electronicSignRecordEntity = electronicSignRecordConvertor.toEntity(electronicSignRecordDto);
        electronicSignRecordMapper.insertSelective(electronicSignRecordEntity);
    }

    private void update(ElectronicSignRecordEntity electronicSignRecordEntity) {
        if (electronicSignRecordEntity.getBusinessType() != 5) {
            return;
        }
        electronicSignRecordMapper.updateByPrimaryKeySelective(electronicSignRecordEntity);
    }


}
