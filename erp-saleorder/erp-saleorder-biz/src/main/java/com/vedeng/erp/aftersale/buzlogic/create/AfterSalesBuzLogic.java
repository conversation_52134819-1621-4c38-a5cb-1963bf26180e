package com.vedeng.erp.aftersale.buzlogic.create;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.authorization.model.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <b>Description:</b><br>
 * 订单流升级-模块化执行方法
 *
 * @param
 * @return
 * @Note <b>Author:</b> Thor <br>
 *       <b>Date:</b> 2021/10/9 17:03
 */
@Service
public class AfterSalesBuzLogic {
    @Autowired
    private AfterSalesService afterSalesService;

    private AfterSalesVo afterSalesVo;
    private List<String> sequence;;
    private User user;

    public List<String> getSequence() {
        return sequence;
    }

    public void setSequence(List<String> sequence) {
        this.sequence = sequence;
    }

    public AfterSalesVo getAfterSalesVo() {
        return afterSalesVo;
    }

    public User getUser() {
        return user;
    }

    public void setAfterSalesService(AfterSalesService afterSalesService) {
        this.afterSalesService = afterSalesService;
    }
    /**
     * <b>Description:</b><br>
     * 初始化参数
     *
     * @param sequence, afterSalesVo, user
     * @return void
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/10/11 14:51
     */
    public void init(List<String> sequence,AfterSalesVo afterSalesVo,User user){
        this.sequence = sequence;
        this.afterSalesVo = afterSalesVo;
        this.user = user;
    }
    /**
     * <b>Description:</b><br>
     * 保存售后单
     *
     * @param
     * @return void
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/10/9 17:04
     */
    public ResultInfo saveAfterSale(AfterSalesVo afterSalesVo,User user){
        ResultInfo resultInfo = new ResultInfo(0,"操作成功");
        resultInfo = afterSalesService.saveAddAfterSales(afterSalesVo, user);
        return  resultInfo;
    }

    public ResultInfo run(List<String> sequence, AfterSalesVo afterSalesVo, User user){
        init(sequence,afterSalesVo,user);
        ResultInfo resultInfo = new ResultInfo(0,"操作成功");
        for (String methodName : sequence){
            switch (methodName) {
                case "saveAfterSale":
                    if(resultInfo.getCode().equals(0)) {
                        resultInfo = this.saveAfterSale(afterSalesVo,user);
                    }
                    break;
                default:
                    break;
            }
        }
        return resultInfo;
    }
}
