package com.vedeng.activiti.taskassign;

import com.vedeng.aftersales.service.AfterSaleServiceStandardService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

/**
 * 售后标准申请审核监听器
 */
public class AfterSaleStadardVerifyFinishListener implements ExecutionListener {

    // 运行时注入service
    public static WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();

    private AfterSaleServiceStandardService afterSaleServiceStandardService;

    public AfterSaleStadardVerifyFinishListener(){
        afterSaleServiceStandardService = (AfterSaleServiceStandardService) context.getBean(AfterSaleServiceStandardService.class);
    }


    @Override
    public void notify(DelegateExecution execution) throws Exception {

        String businessKey = execution.getVariable("businessKey").toString();
        Long serviceStandardApplyId =  Long.valueOf(businessKey.split("_")[1]);
        boolean pass = Boolean.valueOf(execution.getVariable("pass").toString()) ;

        afterSaleServiceStandardService.verifyFinish(serviceStandardApplyId,pass);
    }
}
