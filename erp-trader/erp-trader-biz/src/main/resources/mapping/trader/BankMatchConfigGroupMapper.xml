<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.BankMatchConfigGroupMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.BankMatchConfigGroupEntity">
    <!--@mbg.generated-->
    <!--@Table T_BANK_MATCH_CONFIG_GROUP-->
    <id column="GROUP_ID" jdbcType="BIGINT" property="groupId" />
    <result column="GROUP_NAME" jdbcType="VARCHAR" property="groupName" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    GROUP_ID, GROUP_NAME, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER,
    UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_BANK_MATCH_CONFIG_GROUP
    where GROUP_ID = #{groupId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_BANK_MATCH_CONFIG_GROUP
    where GROUP_ID = #{groupId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="GROUP_ID" keyProperty="groupId" parameterType="com.vedeng.erp.trader.domain.entity.BankMatchConfigGroupEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BANK_MATCH_CONFIG_GROUP (GROUP_NAME, IS_DELETE, ADD_TIME,
      MOD_TIME, CREATOR, CREATOR_NAME,
      UPDATER, UPDATER_NAME, UPDATE_REMARK
      )
    values (#{groupName,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR},
      #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="GROUP_ID" keyProperty="groupId" parameterType="com.vedeng.erp.trader.domain.entity.BankMatchConfigGroupEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BANK_MATCH_CONFIG_GROUP
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groupName != null">
        GROUP_NAME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.BankMatchConfigGroupEntity">
    <!--@mbg.generated-->
    update T_BANK_MATCH_CONFIG_GROUP
    <set>
      <if test="groupName != null">
        GROUP_NAME = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where GROUP_ID = #{groupId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.BankMatchConfigGroupEntity">
    <!--@mbg.generated-->
    update T_BANK_MATCH_CONFIG_GROUP
    set GROUP_NAME = #{groupName,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where GROUP_ID = #{groupId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_BANK_MATCH_CONFIG_GROUP
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="GROUP_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.groupName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where GROUP_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.groupId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_BANK_MATCH_CONFIG_GROUP
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="GROUP_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.groupName != null">
            when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.groupName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateRemark != null">
            when GROUP_ID = #{item.groupId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where GROUP_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.groupId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="GROUP_ID" keyProperty="groupId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BANK_MATCH_CONFIG_GROUP
    (GROUP_NAME, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME,
      UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.groupName,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP},
        #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR},
        #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR}, #{item.updateRemark,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>

  <select id="findAll" resultMap="BaseResultMap">
    select * from T_BANK_MATCH_CONFIG_GROUP where IS_DELETE = 0
  </select>

<!--auto generated by MybatisCodeHelper on 2024-08-26-->
  <delete id="deleteByGroupId">
    update T_BANK_MATCH_CONFIG_GROUP
    set IS_DELETE = 1
    where GROUP_ID = #{groupId,jdbcType=BIGINT};
  </delete>

<!--auto generated by MybatisCodeHelper on 2024-08-26-->
  <select id="findByAll" resultType="com.vedeng.erp.trader.domain.dto.BankMatchConfigGroupDto">
    select
    <include refid="Base_Column_List"/>
    from T_BANK_MATCH_CONFIG_GROUP
    <where>
      <if test="groupId != null">
        and GROUP_ID=#{groupId,jdbcType=BIGINT}
      </if>
      <if test="groupName != null and groupName != ''">
        and GROUP_NAME like CONCAT('%',#{groupName,jdbcType=VARCHAR},'%')
      </if>
      <if test="isDelete != null">
        and IS_DELETE=#{isDelete,jdbcType=INTEGER}
      </if>
      <if test="addTime != null">
        and ADD_TIME=#{addTime,jdbcType=TIMESTAMP}
      </if>
      <if test="modTime != null">
        and MOD_TIME=#{modTime,jdbcType=TIMESTAMP}
      </if>
      <if test="creator != null">
        and CREATOR=#{creator,jdbcType=INTEGER}
      </if>
      <if test="updater != null">
        and UPDATER=#{updater,jdbcType=INTEGER}
      </if>
      <if test="creatorName != null and creatorName != ''">
        and CREATOR_NAME=#{creatorName,jdbcType=VARCHAR}
      </if>
      <if test="updaterName != null and updaterName != ''">
        and UPDATER_NAME=#{updaterName,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

<!--auto generated by MybatisCodeHelper on 2024-08-26-->
  <select id="findAllByGroupName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_BANK_MATCH_CONFIG_GROUP
    where GROUP_NAME=#{groupName,jdbcType=VARCHAR}
    and IS_DELETE = 0
  </select>
</mapper>
