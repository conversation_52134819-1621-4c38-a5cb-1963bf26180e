package com.vedeng.common.constant;

import lombok.Getter;

@Getter
public enum AfterSaleStatusEnum {
    WAIT_CONFIRM(0, "待确认"),
    UNDER_WAY(1, "进行中"),
    FINISHED(2, "已完结"),
    CLOSED(3, "已关闭")
    ;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String describe;


    AfterSaleStatusEnum(Integer status, String describe) {
        this.status = status;
        this.describe = describe;
    }
}
