package com.vedeng.erp.kingdee.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @description 金蝶蓝票以及应付单反审核
 * @date 2022/12/6 15:00
 **/
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class KingDeeInvoiceRollBackDto {


    /**
     * 冲销红票id
     */
    private Integer invoiceId;

    private List<KingDeeInvoiceAndId> originalInvoice;


    @Setter
    @Getter
    @NoArgsConstructor
    public static class KingDeeInvoiceAndId {

        private boolean special = false;

        /**
         * 金蝶票id
         */
        private String fid;

        /**
         * 金蝶应付单id
         */
        private String payId;

        /**
         * 票id
         */
        private Integer id;

    }

}
