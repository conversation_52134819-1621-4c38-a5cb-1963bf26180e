<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchVirtualInvoiceDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchVirtualInvoiceDto">
    <!--@mbg.generated-->
    <!--@Table T_VIRTUAL_INVOICE-->
    <id column="VIRTUAL_INVOICE_ID" jdbcType="INTEGER" property="virtualInvoiceId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UUID" jdbcType="VARCHAR" property="uuid" />
    <result column="INVOICE_CODE" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="INVOICE_PROPERTY" jdbcType="INTEGER" property="invoiceProperty" />
    <result column="INVOICE_NO" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType" />
    <result column="RATIO" jdbcType="DECIMAL" property="ratio" />
    <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType" />
    <result column="BUSINESS_ORDER_ID" jdbcType="INTEGER" property="businessOrderId" />
    <result column="BUSINESS_ORDER_NO" jdbcType="VARCHAR" property="businessOrderNo" />
    <result column="COLOR_TYPE" jdbcType="INTEGER" property="colorType" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="OPEN_INVOICE_TIME" jdbcType="TIMESTAMP" property="openInvoiceTime" />
    <result column="SOURCE_INVOICE_ID" jdbcType="INTEGER" property="sourceInvoiceId" />
  </resultMap>
  <resultMap id="BaseResultMapAndTraderSupper" type="com.vedeng.erp.kingdee.batch.dto.BatchVirtualInvoiceDto">
    <!--@mbg.generated-->
    <!--@Table T_VIRTUAL_INVOICE-->
    <id column="VIRTUAL_INVOICE_ID" jdbcType="INTEGER" property="virtualInvoiceId" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UUID" jdbcType="VARCHAR" property="uuid" />
    <result column="INVOICE_CODE" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="INVOICE_PROPERTY" jdbcType="INTEGER" property="invoiceProperty" />
    <result column="INVOICE_NO" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType" />
    <result column="RATIO" jdbcType="DECIMAL" property="ratio" />
    <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType" />
    <result column="BUSINESS_ORDER_ID" jdbcType="INTEGER" property="businessOrderId" />
    <result column="BUSINESS_ORDER_NO" jdbcType="VARCHAR" property="businessOrderNo" />
    <result column="COLOR_TYPE" jdbcType="INTEGER" property="colorType" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="OPEN_INVOICE_TIME" jdbcType="TIMESTAMP" property="openInvoiceTime" />
    <result column="SOURCE_INVOICE_ID" jdbcType="INTEGER" property="sourceInvoiceId" />
    <result column="TRADER_SUPPLIER_ID" jdbcType="INTEGER" property="traderSupplierId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, VIRTUAL_INVOICE_ID, 
    UUID, INVOICE_CODE, INVOICE_PROPERTY, INVOICE_NO, INVOICE_TYPE, RATIO, BUSINESS_TYPE, 
    BUSINESS_ORDER_ID, BUSINESS_ORDER_NO, COLOR_TYPE, AMOUNT, OPEN_INVOICE_TIME, SOURCE_INVOICE_ID
  </sql>
  <insert id="insert" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchVirtualInvoiceDto">
    <!--@mbg.generated-->
    insert into T_VIRTUAL_INVOICE (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      VIRTUAL_INVOICE_ID, UUID, INVOICE_CODE, 
      INVOICE_PROPERTY, INVOICE_NO, INVOICE_TYPE, 
      RATIO, BUSINESS_TYPE, BUSINESS_ORDER_ID, 
      BUSINESS_ORDER_NO, COLOR_TYPE, AMOUNT, 
      OPEN_INVOICE_TIME, SOURCE_INVOICE_ID)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{virtualInvoiceId,jdbcType=INTEGER}, #{uuid,jdbcType=VARCHAR}, #{invoiceCode,jdbcType=VARCHAR}, 
      #{invoiceProperty,jdbcType=INTEGER}, #{invoiceNo,jdbcType=VARCHAR}, #{invoiceType,jdbcType=INTEGER}, 
      #{ratio,jdbcType=DECIMAL}, #{businessType,jdbcType=INTEGER}, #{businessOrderId,jdbcType=INTEGER}, 
      #{businessOrderNo,jdbcType=VARCHAR}, #{colorType,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, 
      #{openInvoiceTime,jdbcType=TIMESTAMP}, #{sourceInvoiceId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchVirtualInvoiceDto">
    <!--@mbg.generated-->
    insert into T_VIRTUAL_INVOICE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="virtualInvoiceId != null">
        VIRTUAL_INVOICE_ID,
      </if>
      <if test="uuid != null">
        UUID,
      </if>
      <if test="invoiceCode != null">
        INVOICE_CODE,
      </if>
      <if test="invoiceProperty != null">
        INVOICE_PROPERTY,
      </if>
      <if test="invoiceNo != null">
        INVOICE_NO,
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE,
      </if>
      <if test="ratio != null">
        RATIO,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="businessOrderId != null">
        BUSINESS_ORDER_ID,
      </if>
      <if test="businessOrderNo != null">
        BUSINESS_ORDER_NO,
      </if>
      <if test="colorType != null">
        COLOR_TYPE,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="openInvoiceTime != null">
        OPEN_INVOICE_TIME,
      </if>
      <if test="sourceInvoiceId != null">
        SOURCE_INVOICE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="virtualInvoiceId != null">
        #{virtualInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="uuid != null">
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="invoiceCode != null">
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceProperty != null">
        #{invoiceProperty,jdbcType=INTEGER},
      </if>
      <if test="invoiceNo != null">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="ratio != null">
        #{ratio,jdbcType=DECIMAL},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="businessOrderId != null">
        #{businessOrderId,jdbcType=INTEGER},
      </if>
      <if test="businessOrderNo != null">
        #{businessOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="colorType != null">
        #{colorType,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="openInvoiceTime != null">
        #{openInvoiceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceInvoiceId != null">
        #{sourceInvoiceId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-11-29-->
  <select id="selectByInvoiceNoCodeAndSourceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_VIRTUAL_INVOICE
    where INVOICE_NO=#{invoiceNo,jdbcType=VARCHAR} and INVOICE_CODE=#{invoiceCode,jdbcType=VARCHAR} and
    SOURCE_INVOICE_ID=#{sourceInvoiceId,jdbcType=INTEGER}
    and COLOR_TYPE=#{colorType,jdbcType=INTEGER}
    And UUID =''
    order by VIRTUAL_INVOICE_ID desc limit 1

  </select>

<!--auto generated by MybatisCodeHelper on 2023-11-29-->
  <update id="updateByVirtualInvoiceId">
    update T_VIRTUAL_INVOICE
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="virtualInvoiceId != null">
        VIRTUAL_INVOICE_ID = #{virtualInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="uuid != null">
        UUID = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="invoiceCode != null">
        INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceProperty != null">
        INVOICE_PROPERTY = #{invoiceProperty,jdbcType=INTEGER},
      </if>
      <if test="invoiceNo != null">
        INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="ratio != null">
        RATIO = #{ratio,jdbcType=DECIMAL},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="businessOrderId != null">
        BUSINESS_ORDER_ID = #{businessOrderId,jdbcType=INTEGER},
      </if>
      <if test="businessOrderNo != null">
        BUSINESS_ORDER_NO = #{businessOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="colorType != null">
        COLOR_TYPE = #{colorType,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="openInvoiceTime != null">
        OPEN_INVOICE_TIME = #{openInvoiceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceInvoiceId != null">
        SOURCE_INVOICE_ID = #{sourceInvoiceId,jdbcType=INTEGER},
      </if>
    </set>
    where VIRTUAL_INVOICE_ID=#{virtualInvoiceId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-11-30-->
  <select id="selectBySourceInvoiceIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_VIRTUAL_INVOICE
    where SOURCE_INVOICE_ID in
    <foreach collection="list" separator="," item="item" close=")" open="(">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>


  <select id="selectRedCountByInvoiceNo" resultType="java.lang.Integer">
    select COUNT(1) from T_VIRTUAL_INVOICE
    where COLOR_TYPE = 1 and BUSINESS_TYPE = 2 and INVOICE_NO like concat('%',#{invoiceNo,jdbcType=VARCHAR},'%')
    and BUSINESS_ORDER_ID in
    <foreach collection="list" item="item" separator="," close=")" open="(">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2023-12-02-->
  <select id="findByAll" resultMap="BaseResultMapAndTraderSupper">
    select
     CREATOR_NAME, UPDATER_NAME, VIRTUAL_INVOICE_ID,
    UUID, INVOICE_CODE, INVOICE_PROPERTY, INVOICE_NO, TVI.INVOICE_TYPE, RATIO, BUSINESS_TYPE,
    BUSINESS_ORDER_ID, BUSINESS_ORDER_NO, COLOR_TYPE, TVI.AMOUNT, OPEN_INVOICE_TIME, SOURCE_INVOICE_ID,
    TTS.TRADER_SUPPLIER_ID
    from T_VIRTUAL_INVOICE TVI left join T_BUYORDER TB on TVI.BUSINESS_ORDER_ID = TB.BUYORDER_ID
    left join T_TRADER_SUPPLIER TTS on TB.TRADER_ID = TTS.TRADER_ID
    <where>
      <if test="virtualInvoiceId != null">
        and VIRTUAL_INVOICE_ID=#{virtualInvoiceId,jdbcType=INTEGER}
      </if>
      <if test="uuid != null">
        and UUID=#{uuid,jdbcType=VARCHAR}
      </if>
      <if test="invoiceCode != null">
        and INVOICE_CODE=#{invoiceCode,jdbcType=VARCHAR}
      </if>
      <if test="invoiceProperty != null">
        and INVOICE_PROPERTY=#{invoiceProperty,jdbcType=INTEGER}
      </if>
      <if test="invoiceNo != null">
        and INVOICE_NO=#{invoiceNo,jdbcType=VARCHAR}
      </if>
      <if test="invoiceType != null">
        and TVI.INVOICE_TYPE=#{invoiceType,jdbcType=INTEGER}
      </if>
      <if test="ratio != null">
        and RATIO=#{ratio,jdbcType=DECIMAL}
      </if>
      <if test="businessType != null">
        and BUSINESS_TYPE=#{businessType,jdbcType=INTEGER}
      </if>
      <if test="businessOrderId != null">
        and BUSINESS_ORDER_ID=#{businessOrderId,jdbcType=INTEGER}
      </if>
      <if test="businessOrderNo != null">
        and BUSINESS_ORDER_NO=#{businessOrderNo,jdbcType=VARCHAR}
      </if>
      <if test="colorType != null">
        and COLOR_TYPE=#{colorType,jdbcType=INTEGER}
      </if>
      <if test="amount != null">
        and TVI.AMOUNT=#{amount,jdbcType=DECIMAL}
      </if>
      <if test="openInvoiceTime != null">
        and OPEN_INVOICE_TIME=#{openInvoiceTime,jdbcType=TIMESTAMP}
      </if>
      <if test="sourceInvoiceId != null">
        and SOURCE_INVOICE_ID=#{sourceInvoiceId,jdbcType=INTEGER}
      </if>
      <if test="beginTime != null">
        and TVI.ADD_TIME  <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null">
        and TVI.ADD_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    limit #{_pagesize} OFFSET #{_skiprows}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-12-12-->
  <delete id="deleteByVirtualInvoiceId">
    delete from T_VIRTUAL_INVOICE
    where VIRTUAL_INVOICE_ID=#{virtualInvoiceId,jdbcType=INTEGER}
  </delete>

<!--auto generated by MybatisCodeHelper on 2023-12-12-->
  <select id="selectByVirtualInvoiceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_VIRTUAL_INVOICE
    where VIRTUAL_INVOICE_ID=#{virtualInvoiceId,jdbcType=INTEGER}
  </select>

  <select id="findRedByAll" resultMap="BaseResultMapAndTraderSupper">
    select
    CREATOR_NAME, UPDATER_NAME, VIRTUAL_INVOICE_ID,
    UUID, INVOICE_CODE, INVOICE_PROPERTY, INVOICE_NO, TVI.INVOICE_TYPE, RATIO, BUSINESS_TYPE,
    BUSINESS_ORDER_ID, BUSINESS_ORDER_NO, COLOR_TYPE, TVI.AMOUNT, OPEN_INVOICE_TIME, SOURCE_INVOICE_ID,
    TTS.TRADER_SUPPLIER_ID
    from T_VIRTUAL_INVOICE TVI left join T_AFTER_SALES TAS on TAS.AFTER_SALES_ID = TVI.BUSINESS_ORDER_ID
    left join T_BUYORDER TB on TAS.ORDER_ID = TB.BUYORDER_ID
    left join T_TRADER_SUPPLIER TTS on TB.TRADER_ID = TTS.TRADER_ID
    <where>
      <if test="virtualInvoiceId != null">
        and VIRTUAL_INVOICE_ID=#{virtualInvoiceId,jdbcType=INTEGER}
      </if>
      <if test="uuid != null">
        and UUID=#{uuid,jdbcType=VARCHAR}
      </if>
      <if test="invoiceCode != null">
        and INVOICE_CODE=#{invoiceCode,jdbcType=VARCHAR}
      </if>
      <if test="invoiceProperty != null">
        and INVOICE_PROPERTY=#{invoiceProperty,jdbcType=INTEGER}
      </if>
      <if test="invoiceNo != null">
        and INVOICE_NO=#{invoiceNo,jdbcType=VARCHAR}
      </if>
      <if test="invoiceType != null">
        and TVI.INVOICE_TYPE=#{invoiceType,jdbcType=INTEGER}
      </if>
      <if test="ratio != null">
        and RATIO=#{ratio,jdbcType=DECIMAL}
      </if>
      <if test="businessType != null">
        and BUSINESS_TYPE=#{businessType,jdbcType=INTEGER}
      </if>
      <if test="businessOrderId != null">
        and BUSINESS_ORDER_ID=#{businessOrderId,jdbcType=INTEGER}
      </if>
      <if test="businessOrderNo != null">
        and BUSINESS_ORDER_NO=#{businessOrderNo,jdbcType=VARCHAR}
      </if>
      <if test="colorType != null">
        and COLOR_TYPE=#{colorType,jdbcType=INTEGER}
      </if>
      <if test="amount != null">
        and TVI.AMOUNT=#{amount,jdbcType=DECIMAL}
      </if>
      <if test="openInvoiceTime != null">
        and OPEN_INVOICE_TIME=#{openInvoiceTime,jdbcType=TIMESTAMP}
      </if>
      <if test="sourceInvoiceId != null">
        and SOURCE_INVOICE_ID=#{sourceInvoiceId,jdbcType=INTEGER}
      </if>
      <if test="beginTime != null">
        and TVI.ADD_TIME  <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null">
        and TVI.ADD_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    limit #{_pagesize} OFFSET #{_skiprows}
  </select>
</mapper>