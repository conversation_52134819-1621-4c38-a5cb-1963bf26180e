package com.vedeng.data.controller;

import cn.hutool.core.util.ObjectUtil;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.erp.saleorder.api.SaleorderSyncService;
import com.vedeng.erp.saleorder.enums.PCOrderStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleorderSyncEnum;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleorderService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

/**
 * 接口重处理
 */
@Controller
@RequestMapping("/data/profile")
@Slf4j
public class SaleOrderSendMjxController {


    @Autowired
    private SaleorderSyncService saleorderSyncService;
    @Autowired
    private SaleorderService saleorderService;

    @ResponseBody
    @RequestMapping(value="sendMjx")
    @NoNeedAccessAuthorization
    public ResultInfo sendMjx(HttpServletRequest request){
        String orderNo = request.getParameter("orderNo");
        String status = request.getParameter("status");
        if(ObjectUtil.isEmpty(orderNo) ||ObjectUtil.isEmpty(status) ){
            return ResultInfo.error("订单号或状态不能为空");
        }
        Saleorder saleorder = saleorderService.getBySaleOrderNo(orderNo);
        if(ObjectUtil.isEmpty(saleorder)){
            return ResultInfo.error("订单未找到");
        }
        saleorderSyncService.syncSaleorderStatus2Mjx(saleorder.getSaleorderId(), PCOrderStatusEnum.get(Integer.parseInt(status)), SaleorderSyncEnum.CONFIRM_ARRIVAL);
        return ResultInfo.success("成功执行推送，请检查确认：syncSaleorderStatus2Mjx "+orderNo);
    }

    @ResponseBody
    @RequestMapping(value="syncOrder")
    @NoNeedAccessAuthorization
    public ResultInfo syncOrder(HttpServletRequest request){
        String saleOrderIdStr = request.getParameter("saleOrderId");
        if(ObjectUtil.isEmpty(saleOrderIdStr)){
            return ResultInfo.error("销售订单号不能为空");
        }
        Integer saleOrderId = Integer.valueOf(saleOrderIdStr);
        Saleorder saleorder = saleorderService.getSaleOrderById(saleOrderId);
        if(ObjectUtil.isEmpty(saleorder)){
            return ResultInfo.error("订单未找到");
        }
        try {
            JSONObject result2 = saleorderService.updateVedengJX(saleOrderId);
        }catch (Exception e){
            return ResultInfo.error("同步失败");
        }
        return ResultInfo.success("成功执行推送，请检查确认：updateVedengJX "+saleOrderIdStr);
    }



}
