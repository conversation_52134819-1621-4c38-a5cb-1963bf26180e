<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.saleorder.dao.ActivityPreOrderMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.saleorder.domain.entity.ActivityPreOrderEntity">
        <!--@mbg.generated-->
        <!--@Table T_ACTIVITY_PRE_ORDER-->
        <id column="ACTIVITY_PRE_ORDER_ID" jdbcType="INTEGER" property="activityPreOrderId" />
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
        <result column="CREATOR" jdbcType="INTEGER" property="creator" />
        <result column="UPDATER" jdbcType="INTEGER" property="updater" />
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
        <result column="SALEORDER_ID" jdbcType="INTEGER" property="saleorderId" />
        <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
        <result column="COMPANY_NAME" jdbcType="VARCHAR" property="companyName" />
        <result column="SKU" jdbcType="VARCHAR" property="sku" />
        <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
        <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
        <result column="CONTACT_PERSON" jdbcType="VARCHAR" property="contactPerson" />
        <result column="CONTACT_PHONE" jdbcType="VARCHAR" property="contactPhone" />
        <result column="BELONG_SALES_ID" jdbcType="VARCHAR" property="belongSalesId" />
        <result column="BELONG_SALES" jdbcType="VARCHAR" property="belongSales" />
        <result column="ORDER_STATUS" jdbcType="INTEGER" property="orderStatus" />
        <result column="QUOTA" jdbcType="INTEGER" property="quota" />
        <result column="TAG" jdbcType="INTEGER" property="tag" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />
        <result column="NUM" jdbcType="INTEGER" property="num" />
        <result column="ACTIVE_ID" jdbcType="INTEGER" property="activeId" />
        <result column="PRICE" jdbcType="DECIMAL" property="price" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ACTIVITY_PRE_ORDER_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME,
        SALEORDER_ID, ORDER_NO, COMPANY_NAME, SKU, GOODS_ID, GOODS_NAME, CONTACT_PERSON,
        CONTACT_PHONE, BELONG_SALES_ID, BELONG_SALES, ORDER_STATUS, QUOTA, TAG, REMARK, NUM,
        ACTIVE_ID, PRICE
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from T_ACTIVITY_PRE_ORDER
        where ACTIVITY_PRE_ORDER_ID = #{activityPreOrderId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete from T_ACTIVITY_PRE_ORDER
        where ACTIVITY_PRE_ORDER_ID = #{activityPreOrderId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="ACTIVITY_PRE_ORDER_ID" keyProperty="activityPreOrderId" parameterType="com.vedeng.erp.saleorder.domain.entity.ActivityPreOrderEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_ACTIVITY_PRE_ORDER (ADD_TIME, MOD_TIME, CREATOR,
        UPDATER, CREATOR_NAME, UPDATER_NAME,
        SALEORDER_ID, ORDER_NO, COMPANY_NAME,
        SKU, GOODS_ID, GOODS_NAME,
        CONTACT_PERSON, CONTACT_PHONE, BELONG_SALES_ID,
        BELONG_SALES, ORDER_STATUS, QUOTA,
        TAG, REMARK, NUM, ACTIVE_ID,
        PRICE)
        values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
        #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR},
        #{saleorderId,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR},
        #{sku,jdbcType=VARCHAR}, #{goodsId,jdbcType=INTEGER}, #{goodsName,jdbcType=VARCHAR},
        #{contactPerson,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR}, #{belongSalesId,jdbcType=VARCHAR},
        #{belongSales,jdbcType=VARCHAR}, #{orderStatus,jdbcType=INTEGER}, #{quota,jdbcType=INTEGER},
        #{tag,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER}, #{activeId,jdbcType=INTEGER},
        #{price,jdbcType=DECIMAL})
    </insert>
    <insert id="insertSelective" keyColumn="ACTIVITY_PRE_ORDER_ID" keyProperty="activityPreOrderId" parameterType="com.vedeng.erp.saleorder.domain.entity.ActivityPreOrderEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_ACTIVITY_PRE_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME,
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME,
            </if>
            <if test="saleorderId != null">
                SALEORDER_ID,
            </if>
            <if test="orderNo != null and orderNo != ''">
                ORDER_NO,
            </if>
            <if test="companyName != null and companyName != ''">
                COMPANY_NAME,
            </if>
            <if test="sku != null and sku != ''">
                SKU,
            </if>
            <if test="goodsId != null">
                GOODS_ID,
            </if>
            <if test="goodsName != null and goodsName != ''">
                GOODS_NAME,
            </if>
            <if test="contactPerson != null and contactPerson != ''">
                CONTACT_PERSON,
            </if>
            <if test="contactPhone != null and contactPhone != ''">
                CONTACT_PHONE,
            </if>
            <if test="belongSalesId != null and belongSalesId != ''">
                BELONG_SALES_ID,
            </if>
            <if test="belongSales != null and belongSales != ''">
                BELONG_SALES,
            </if>
            <if test="orderStatus != null">
                ORDER_STATUS,
            </if>
            <if test="quota != null">
                QUOTA,
            </if>
            <if test="tag != null">
                TAG,
            </if>
            <if test="remark != null and remark != ''">
                REMARK,
            </if>
            <if test="num != null">
                NUM,
            </if>
            <if test="activeId != null">
                ACTIVE_ID,
            </if>
            <if test="price != null">
                PRICE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null and updaterName != ''">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="saleorderId != null">
                #{saleorderId,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null and orderNo != ''">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null and companyName != ''">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="sku != null and sku != ''">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="goodsId != null">
                #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="goodsName != null and goodsName != ''">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="contactPerson != null and contactPerson != ''">
                #{contactPerson,jdbcType=VARCHAR},
            </if>
            <if test="contactPhone != null and contactPhone != ''">
                #{contactPhone,jdbcType=VARCHAR},
            </if>
            <if test="belongSalesId != null and belongSalesId != ''">
                #{belongSalesId,jdbcType=VARCHAR},
            </if>
            <if test="belongSales != null and belongSales != ''">
                #{belongSales,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null">
                #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="quota != null">
                #{quota,jdbcType=INTEGER},
            </if>
            <if test="tag != null">
                #{tag,jdbcType=INTEGER},
            </if>
            <if test="remark != null and remark != ''">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                #{num,jdbcType=INTEGER},
            </if>
            <if test="activeId != null">
                #{activeId,jdbcType=INTEGER},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.saleorder.domain.entity.ActivityPreOrderEntity">
        <!--@mbg.generated-->
        update T_ACTIVITY_PRE_ORDER
        <set>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="saleorderId != null">
                SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null and orderNo != ''">
                ORDER_NO = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null and companyName != ''">
                COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="sku != null and sku != ''">
                SKU = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="goodsId != null">
                GOODS_ID = #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="goodsName != null and goodsName != ''">
                GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="contactPerson != null and contactPerson != ''">
                CONTACT_PERSON = #{contactPerson,jdbcType=VARCHAR},
            </if>
            <if test="contactPhone != null and contactPhone != ''">
                CONTACT_PHONE = #{contactPhone,jdbcType=VARCHAR},
            </if>
            <if test="belongSalesId != null and belongSalesId != ''">
                BELONG_SALES_ID = #{belongSalesId,jdbcType=VARCHAR},
            </if>
            <if test="belongSales != null and belongSales != ''">
                BELONG_SALES = #{belongSales,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null">
                ORDER_STATUS = #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="quota != null">
                QUOTA = #{quota,jdbcType=INTEGER},
            </if>
            <if test="tag != null">
                TAG = #{tag,jdbcType=INTEGER},
            </if>
            <if test="remark != null and remark != ''">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                NUM = #{num,jdbcType=INTEGER},
            </if>
            <if test="activeId != null">
                ACTIVE_ID = #{activeId,jdbcType=INTEGER},
            </if>
            <if test="price != null">
                PRICE = #{price,jdbcType=DECIMAL},
            </if>
        </set>
        where ACTIVITY_PRE_ORDER_ID = #{activityPreOrderId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.saleorder.domain.entity.ActivityPreOrderEntity">
        <!--@mbg.generated-->
        update T_ACTIVITY_PRE_ORDER
        set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
        CREATOR = #{creator,jdbcType=INTEGER},
        UPDATER = #{updater,jdbcType=INTEGER},
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
        COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
        SKU = #{sku,jdbcType=VARCHAR},
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
        CONTACT_PERSON = #{contactPerson,jdbcType=VARCHAR},
        CONTACT_PHONE = #{contactPhone,jdbcType=VARCHAR},
        BELONG_SALES_ID = #{belongSalesId,jdbcType=VARCHAR},
        BELONG_SALES = #{belongSales,jdbcType=VARCHAR},
        ORDER_STATUS = #{orderStatus,jdbcType=INTEGER},
        QUOTA = #{quota,jdbcType=INTEGER},
        TAG = #{tag,jdbcType=INTEGER},
        REMARK = #{remark,jdbcType=VARCHAR},
        NUM = #{num,jdbcType=INTEGER},
        ACTIVE_ID = #{activeId,jdbcType=INTEGER},
        PRICE = #{price,jdbcType=DECIMAL}
        where ACTIVITY_PRE_ORDER_ID = #{activityPreOrderId,jdbcType=INTEGER}
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update T_ACTIVITY_PRE_ORDER
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="ADD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.addTime != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="MOD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.modTime != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CREATOR = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.creator != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UPDATER = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updater != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CREATOR_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.creatorName != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UPDATER_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterName != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SALEORDER_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.saleorderId != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.saleorderId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ORDER_NO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderNo != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.orderNo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="COMPANY_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.companyName != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.companyName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SKU = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sku != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.sku,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="GOODS_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.goodsId != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.goodsId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="GOODS_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.goodsName != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.goodsName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CONTACT_PERSON = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contactPerson != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.contactPerson,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CONTACT_PHONE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contactPhone != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.contactPhone,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="BELONG_SALES_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.belongSalesId != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.belongSalesId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="BELONG_SALES = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.belongSales != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.belongSales,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ORDER_STATUS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderStatus != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.orderStatus,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="QUOTA = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.quota != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.quota,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="TAG = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tag != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.tag,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="REMARK = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.remark != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.remark,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="NUM = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.num != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ACTIVE_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.activeId != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.activeId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="PRICE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.price != null">
                        when ACTIVITY_PRE_ORDER_ID = #{item.activityPreOrderId,jdbcType=INTEGER} then #{item.price,jdbcType=DECIMAL}
                    </if>
                </foreach>
            </trim>
        </trim>
        where ACTIVITY_PRE_ORDER_ID in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.activityPreOrderId,jdbcType=INTEGER}
        </foreach>
    </update>

    <!--auto generated by MybatisCodeHelper on 2022-12-24-->
    <select id="findByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_ACTIVITY_PRE_ORDER
        where ORDER_NO = #{orderNo,jdbcType=VARCHAR}
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-12-25-->
    <select id="findBySaleorderId" resultType="com.vedeng.erp.saleorder.dto.ActivityPreOrderDto">
        select
        <include refid="Base_Column_List"/>
        from T_ACTIVITY_PRE_ORDER
        where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-12-25-->
    <select id="findByActivityPreOrderId" resultType="com.vedeng.erp.saleorder.dto.ActivityPreOrderDto">
        select
        <include refid="Base_Column_List"/>
        from T_ACTIVITY_PRE_ORDER
        where ACTIVITY_PRE_ORDER_ID = #{activityPreOrderId,jdbcType=INTEGER}
    </select>

    <select id="findExistNumByTraderIdAndActionId" resultType="int">
        SELECT SUM(B.NUM) FROM T_SALEORDER A LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID=B.SALEORDER_ID
        WHERE B.IS_ACTION_GOODS=1 AND A.ACTION_ID=#{actionId} AND B.IS_DELETE=0
        AND A.TRADER_ID=#{traderId} and B.GOODS_ID=#{skuId} and A.STATUS &lt; 3
    </select>

<!--auto generated by MybatisCodeHelper on 2022-12-28-->
    <select id="findByActivityPreOrderIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_ACTIVITY_PRE_ORDER
        where ACTIVITY_PRE_ORDER_ID in
        <foreach item="item" index="index" collection="activityPreOrderIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>
</mapper>