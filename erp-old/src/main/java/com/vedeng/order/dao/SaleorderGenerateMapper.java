package com.vedeng.order.dao;

import com.vedeng.order.model.SaleorderGenerate;
import com.vedeng.order.model.SaleorderGenerateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SaleorderGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    int countByExample(SaleorderGenerateExample example);


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    int insert(SaleorderGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    int insertSelective(SaleorderGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    List<SaleorderGenerate> selectByExample(SaleorderGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    SaleorderGenerate selectByPrimaryKey(Integer saleorderId);


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    int updateByPrimaryKeySelective(SaleorderGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    int updateByPrimaryKey(SaleorderGenerate record);
}