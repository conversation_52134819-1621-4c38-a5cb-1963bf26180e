package com.vedeng.goods.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/** 商品财务信息
 * @description:
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/29 10:53
 **/
@Data
public class GoodsFinanceInfoDto implements Serializable {

    /**
     * 主键
     */
    private Long goodsFinanceId;

    /**
     * 订货号
     */
    @ExcelProperty(value = "*订货号", index = 0)
    private String skuNo;

    /**
     * 商品名称
     */
    private String showName;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 商品类别 字典表
     */
    private Integer spuType;

    /**
     * 商品类别
     */
    private String spuTypeValue;

    /**
     * 是否医疗器械 1是 0否
     */
    private Integer isMedicalEquipment;

    @ExcelProperty(value = "*是否医疗器械（填:是/否）", index = 1)
    private String medicalEquipment;

    /**
     * 医疗器械细分类
     */
    @ExcelProperty(value = "*医疗器械细分类", index = 2)
    private String medicalEquipmentType;

    /**
     * 医疗器械用途
     */
    @ExcelProperty(value = "*医疗器械用途", index = 3)
    private String medicalEquipmentUse;

    /**
     * 医疗器械产线
     */
    @ExcelProperty(value = "*医疗器械产线", index = 4)
    private String medicalEquipmentLine;

    /**
     * 是否推送金蝶 0否 1是
     */
    private Integer isPush;

    /**
     * 推送时间
     */
    private String pushTime;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新备注
     */
    private String updateRemark;

    /**
     * 创建时间
     */
    private String addTime;

    /**
     * 修改时间
     */
    private String modTime;

    /**
     * 创建者id
     */
    private Integer creator;

    /**
     * 修改者id
     */
    private Integer updater;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 修改者名称
     */
    private String updaterName;

    /**
     * 商品财务信息STR
     */
    private String skuAddTimeStr;

    /**
     * 审核状态:0待提交1审核中2审核通过3已驳回
     */
    private Integer auditStatus;

    /**
     * 是否安调
     */
    private Integer isInstallation;

    /**
     * 审核是否安调 0否 1是
     */
    private Integer auditInstallation;

    /**
     * 审批发起时间
     */
    private Date applyTime;

    /**
     * 发起人ID
     */
    private Integer applyUserId;

    /**
     * 发起人名称
     */
    private String applyUsername;

    /**
     * 审批时间
     */
    private Date auditTime;

    /**
     * 审批人ID
     */
    private Integer auditUserId;

    /**
     * 审批人名称
     */
    private String auditUsername;

    /**
     * 审批备注
     */
    private String auditRemark;

    /**
     * 审批按钮
     */
    private boolean auditButton = false;

    /**
     * 审核信息
     */
    private List<AuditInfoDto> auditInfoDtoList = new ArrayList<>();


    @Data
    public static class AuditInfoDto {

        /**
         * 操作人
         */
        private String user;

        /**
         * 操作时间
         */
        private Date time;

        /**
         * 操作事项
         */
        private String oper;

        /**
         * 备注
         */
        private String remark;

    }
}
