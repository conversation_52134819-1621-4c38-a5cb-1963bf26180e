package com.vedeng.call.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.xml.namespace.QName;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.common.constants.Contant;
import com.google.gson.JsonObject;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.call.dto.AliPhoneApiResultDto;
import com.vedeng.call.dto.AliPhoneApiResultEntityDto;
import com.vedeng.call.dto.CallStaticDto;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.HttpClientUtil;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.erp.market.api.MarketPlanApiService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.constant.AiConstant;
import com.vedeng.erp.trader.dto.TagDto;
import com.vedeng.erp.trader.service.CommunicateRecordApiService;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.phoneticWriting.dao.CommentMapper;
import com.vedeng.phoneticWriting.dao.ModificationRecordMapper;
import com.vedeng.phoneticWriting.dao.PhoneticWritingMapper;
import com.vedeng.phoneticWriting.model.Comment;
import com.vedeng.phoneticWriting.model.ModificationRecord;
import com.vedeng.phoneticWriting.model.PhoneticWriting;
import com.vedeng.system.model.Tag;
import com.vedeng.trader.dao.TraderContactGenerateMapper;
import com.vedeng.trader.dao.TraderContactMapper;
import com.vedeng.trader.dto.TraderInfoReqDto;
import com.vedeng.trader.dto.TraderInfoRespDto;
import com.vedeng.trader.model.WxCommunicateRecord;
import com.vedeng.uac.api.dto.UserDTO;
import com.vedeng.wechat.api.dto.WxCustomerFollowUserDto;
import eu.bitwalker.useragentutils.UserAgent;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.axis2.addressing.EndpointReference;
import org.apache.axis2.client.Options;
import org.apache.axis2.rpc.client.RPCServiceClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.call.dao.CallFailedMapper;
import com.vedeng.call.model.CallFailed;
import com.vedeng.call.model.CallOut;
import com.vedeng.call.service.CallService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.common.util.XmlExercise;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Quoteorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.BussinessChanceVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.system.dao.QCellCoreMapper;
import com.vedeng.system.model.QCellCore;
import com.vedeng.trader.dao.CommunicateRecordMapper;
import com.vedeng.trader.model.CommunicateRecord;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import com.vedeng.trader.service.TraderCustomerService;

import net.sf.json.JSONObject;

@Service("callService")
public class CallServiceImpl extends BaseServiceimpl implements CallService {
	/**
	 * @Fields callServerIp : TODO 呼叫中心服务器地址
	 */
	public static Logger logger = LoggerFactory.getLogger(CallServiceImpl.class);

	@Value("${call_server_ip}")
	private String callServerIp;

	@Value("${call_url}")
	private String callUrl;

	@Value("${call_namespace}")
	private String callNamespace;

	@Autowired
	@Qualifier("qCellCoreMapper")
	private QCellCoreMapper qCellCoreMapper;

	@Autowired
	@Qualifier("communicateRecordMapper")
	private CommunicateRecordMapper communicateRecordMapper;

	@Autowired
	@Qualifier("userMapper")
	private UserMapper userMapper;

	@Autowired
	@Qualifier("traderCustomerService")
	private TraderCustomerService traderCustomerService;
	
	@Autowired
	@Qualifier("callFailedMapper")
	private CallFailedMapper callFailedMapper;

	@Autowired
	@Qualifier("modificationRecordMapper")
	private ModificationRecordMapper modificationRecordMapper;

	@Autowired
	@Qualifier("phoneticWritingMapper")
	private PhoneticWritingMapper phoneticWritingMapper;

	@Autowired
	@Qualifier("commentMapper")
	private CommentMapper commentMapper;

	@Resource
	private TraderContactGenerateMapper traderContactGenerateMapper;

	@Value("${phone_prefix_array}")
	private String phonePrefixArray;

	/**
	 * 阿里手机归属地接口的token
	 */
	@Value("${ali_mobile_belong_api_token}")
	private String aliMobileBelongApiToken;

	@Autowired
	private RedisUtils redisUtils;


	@Autowired
	private MarketPlanApiService marketPlanApiService;


	@Override
	public void pullVoiceMp3(String coid) {
		List<String> coidList = new ArrayList<>();
		coidList.add(coid);
		List<Map<String,Object>> resultList = getRecordListByCoid(coidList);
		if(CollectionUtils.isNotEmpty(resultList)) {
			logger.info("实时拉取语音文件时，成功拉取到了");

			Map dataMap = (Map) resultList.get(0);
			if(null != dataMap.get("URL") && null != dataMap.get("FILELEN")){
				CommunicateRecord nc = new CommunicateRecord();
				nc.setCoid(coid);
				nc.setSyncStatus(ErpConst.ONE);
				nc.setCoidLength(Integer.parseInt(dataMap.get("FILELEN").toString()));
				String url = dataMap.get("URL").toString();
				nc.setCoidUri(org.apache.commons.lang3.StringUtils.isNotBlank(url) ? url.replaceAll(" ", "+") : "");
				communicateRecordMapper.updateByCoid(nc);
				CommunicateRecord communicateRecord = communicateRecordMapper.getLastCommunicateByCoid(coid);
				sendAiRequest(communicateRecord, AiConstant.getEnumByCommunicateType(communicateRecord.getCommunicateType()).getCode());
			}
		}else{
			logger.info("实时拉取语音文件时，未成功拉取到");
		}
	}

	@Autowired
	private UserApiService userApiService;

	@Autowired
	private TraderContactMapper traderContactMapper;

	@Value("${wechatServiceUrl:http://localhost:9980/getCustomerInfo.html}")
	private String wechatServiceUrl;

	@Autowired
	private SaleorderMapper saleorderMapper;

	@Autowired
	private UacWxUserInfoApiService uacWxUserInfoApiService;

	TraderInfoRespDto getBelongTrader(Integer userId,List<String> phoneList){
		if(userId == null || CollectionUtils.isEmpty(phoneList)) {
			logger.warn("客户或联系人ID为空");
			return null;
		}
		TraderInfoReqDto traderInfoReqDto = new TraderInfoReqDto();
		traderInfoReqDto.setUserId(userId);
		traderInfoReqDto.setPhoneList(phoneList);
		List<TraderInfoRespDto> traderInfoRespDtoList  = traderContactMapper.getBelongTrader(traderInfoReqDto);

		if(CollectionUtils.isNotEmpty(traderInfoRespDtoList)  ) {
			if(CollectionUtils.size(traderInfoRespDtoList) == 1) {
				//如果刚好匹配到一个客户，就直接返回
				return traderInfoRespDtoList.get(0);
			}else {// (CollectionUtils.size(traderInfoRespDtoList) > 1)
				//如果通过手机号码匹配到了多个客户，将traderInfoRespDtoList转换成traderIdList
				List<Integer> traderIdList = traderInfoRespDtoList.stream().map(TraderInfoRespDto::getTraderId).collect(Collectors.toList());
				Integer traderId = saleorderMapper.queryNewestTraderIdBySaleOrder(traderIdList);
				if (traderId != null) {//直接匹配traderInfoRespDtoList中traderId与此相等的那条返回
					TraderInfoRespDto matchedTrader = traderInfoRespDtoList.stream()
							.filter(dto ->traderId.equals(dto.getTraderId()))
							.findFirst()
							.orElse(null);
					return matchedTrader;
				}else{// 手机号无交易记录
                    // 一定有值，直接get没问题
                    return traderInfoRespDtoList.stream()
                            .max(Comparator.comparing(TraderInfoRespDto::getTraderId))
                            .orElse(null);
				}
			}
		}else{
			logger.info("getBelongTrader 根据手机号获取联系人及对应的客户未找到数据,{},{}",userId,phoneList);
			return null;
		}

	}



	@Override
	public boolean pullVoiceWxMp3(WxCommunicateRecord communicateRecord) throws Exception{
		String jobNumber = communicateRecord.getJobNumber();
//		String externalUserId = communicateRecord.getExternalUserId();
		String talkId = communicateRecord.getTalkId();
		//根据coidId为talkId查询CommunicateRecord的表里，是否已经处理过了
		String coidId = "QYWX_"+talkId;//沟通记录ID，企业微信的通话ID由	QYWX_+talkId组成
		CommunicateRecord communicateRecord1 = communicateRecordMapper.getCommunicateRecordByJobNumberAndTalkId(coidId);
		if(null != communicateRecord1) {
			logger.info("这条语音沟通记录已经处理过了");
			return true;
		}
		WxCustomerFollowUserDto wxCustomerFollowUserDto = communicateRecord.getWxCustomerFollowUserDto();
		//获取手机号
		List<String> phoneList = new ArrayList<>();//通过调用we-chat来获取手机号
		String avatar = wxCustomerFollowUserDto.getAvatar()!=null?wxCustomerFollowUserDto.getAvatar():""; //联系人的头像
		String nickname = wxCustomerFollowUserDto.getNickName()!=null?wxCustomerFollowUserDto.getNickName():"";//昵称
		String remark = wxCustomerFollowUserDto.getRemark() != null?wxCustomerFollowUserDto.getRemark():"-"; //销售备注的联系人的名称
		String jsonPhoneArray = ( wxCustomerFollowUserDto.getRemarkMobiles() !=null && !wxCustomerFollowUserDto.getRemarkMobiles().isEmpty())
				?wxCustomerFollowUserDto.getRemarkMobiles():null;
		if(StringUtils.isNotBlank(jsonPhoneArray)) {
			JSONArray jsonArray = JSONArray.parseArray(jsonPhoneArray);
			if(null != jsonArray && !jsonArray.isEmpty()) {
				for(int i=0;i<jsonArray.size();i++) {
					phoneList.add(jsonArray.get(i).toString());
				}
			}
		}
		String phone = CollectionUtils.isEmpty(phoneList)?"":phoneList.get(0);
		if(CollectionUtils.isEmpty(phoneList)){
			logger.info("未获取到手机号");
		}
		//获取用户信息
//		UserDto userDto = userApiService.getUserBaseInfoByJobNumber(jobNumber);
		RestfulResult<UserDTO> restfulResult =  uacWxUserInfoApiService.getUserInfoByJobNumber(jobNumber);
		UserDTO userDto = restfulResult.getData();
		if(userDto == null){
			logger.warn("未获取到用户信息");
			throw  new ServiceException("未获取到员工信息:"+jobNumber);
		}
		CommunicateRecord record = new CommunicateRecord();
		TraderInfoRespDto belongTrader = getBelongTrader(userDto.getId(),phoneList);
		if(belongTrader == null) {
			logger.warn("未找到客户信息");
			record.setTraderId(0);
			record.setTraderName("");
		}else{
			record.setTraderContactId(null);//获取联系人ID
			record.setTraderId(belongTrader.getTraderId());		 //获取联系人信息的客户ID
			record.setTraderName(belongTrader.getTraderName());//客户名称

		}
		record.setCoid(coidId);
		record.setFollowUpType(5902);// 企业微信

		record.setCoidLength(communicateRecord.getCoidLength()<=0?null:communicateRecord.getCoidLength());
		record.setCoidUri(communicateRecord.getCoidUri());

		record.setAddTime(System.currentTimeMillis());
		record.setCreator(userDto.getAccountId());
		record.setCreatorName(userDto.getDisplayName());
		record.setPhone(phone);
		record.setContactMob(phone);
		record.setCoidType(communicateRecord.getTalkType());//表示企业微信沟通3 呼出 4呼入
		record.setCompanyId(1);
		record.setBegintime(communicateRecord.getBegintime());
		record.setEndtime(communicateRecord.getEndtime());
		record.setSyncStatus(ErpConstant.ONE);
		record.setTraderType(ErpConstant.ONE);

		record.setCommunicateType(ErpConstant.ZERO);//这个值不能为null
		record.setTtNumber("企业微信");
		record.setAvatarUrl(avatar);
		record.setContact(nickname+"（备注名："+remark+"）");
		communicateRecordMapper.insertWxVoice(record);
		if(record.getTraderId() != null && record.getTraderId()>0) {
//			工作台-精准营销活动，需要更新活动-销售任务中的是否已沟通更新为已沟通。-Kerwin VDERP-15559
			marketPlanApiService.updateMarketPlanTraderSendMsg(record.getTraderId());
		}
		if(communicateRecord.getCoidLength()>0){
			CommunicateRecord nc = new CommunicateRecord();
			nc.setCommunicateRecordId(record.getCommunicateRecordId());
			nc.setSyncStatus(ErpConst.ONE);
			nc.setCoidLength(communicateRecord.getCoidLength());
			nc.setCoidUri(communicateRecord.getCoidUri());
			sendAiRequest(nc, AiConstant.getEnumByCommunicateType(null).getCode());
		}
		return true;
	}

	@Override
	public boolean sendAiRequest(CommunicateRecord communicateRecord, String senceCode) {
		logger.info("get coiduri from callcenter:{},{}",new Object[]{communicateRecord.getCommunicateRecordId(),communicateRecord.getCoidUri()});
		com.alibaba.fastjson.JSONObject json = new com.alibaba.fastjson.JSONObject();
		json.put("communicateRecordId",communicateRecord.getCommunicateRecordId());
		json.put("coidLength",communicateRecord.getCoidLength());
		json.put("coidUri",communicateRecord.getCoidUri());
		json.put("senceCode",senceCode);
		if(org.apache.commons.lang3.StringUtils.isNotBlank(voiceUrl)) {
			com.alibaba.fastjson.JSONObject jsonResult = HttpClientUtil.httpPost(voiceUrl, json.toJSONString());
			if (jsonResult == null || !jsonResult.getBoolean("success")) {
				return false;
			}
			if(jsonResult.get("data").toString().indexOf("语音转写中，请稍后再试")> -1){
				return false;
			}
			return true;
		}
		return true;
	}

	@Override
	public HashMap callInit() {
		HashMap callParams = new HashMap<>();

		// 服务器地址
		callParams.put("callServerIp", callServerIp);

		return callParams;
	}

	@Override
	public QCellCore getQCellCoreByPhone(String phone) {
		String sphone=getShortPhone(phone);
//		if(StringUtils.startsWith(phone,"09") ){
//			return qCellCoreMapper.getQCellCoreByPhone(phone.substring(2));
//		}
		return qCellCoreMapper.getQCellCoreByPhone(phone);
	}

	@Override
	public QCellCore saveQCellCoreInfoByQueryAliApi(String phone) {
		QCellCore cellCore = new QCellCore();
		String phoneInfoQueryApi = "http://aiphone.market.alicloudapi.com/ai_mobile_number_belong_to_china/v1?MOBILE_NUMBER=" + phone;
		Map<String,String> headers = new HashMap<>();
		headers.put("Authorization","APPCODE " + aliMobileBelongApiToken);
		com.alibaba.fastjson.TypeReference<AliPhoneApiResultDto> typeReference = new com.alibaba.fastjson.TypeReference<AliPhoneApiResultDto>() {};
		try {
			AliPhoneApiResultDto result = HttpRestClientUtil.get(phoneInfoQueryApi,typeReference,headers,null);
			if (result != null && result.getEntity() != null){
				AliPhoneApiResultEntityDto entityDto = result.getEntity();
				cellCore.setPhone(entityDto.getMobileNumberPrefix());
				cellCore.setProvince(entityDto.getProvince());
				cellCore.setCity(entityDto.getCity());
				cellCore.setCode(entityDto.getAreaCode());
				cellCore.setOperatpr(entityDto.getIsp());
				cellCore.setZip(entityDto.getPostCode());
				qCellCoreMapper.saveQCellCore(cellCore);
			}
		} catch (Exception e){
			logger.error("保存手机区号信息：{}失败，e：",cellCore,e);
		}
		return cellCore;
	}



	@Override
	public TraderSupplierVo getSupplierInfoByPhone(CallOut callOut) {
		TraderSupplierVo traderSupplier = new TraderSupplierVo();
		traderSupplier.setPhone(callOut.getPhone());

		// 历史沟通记录查询  全路径查询
		CommunicateRecord record = communicateRecordMapper.getSupplierCommunicateByPhone(callOut.getPhone());


		//截取查询
		String spone=getShortPhone(callOut.getPhone());
		Optional<String> checkNeedShortPhoneOption=checkNeedShortPhone(callOut.getPhone());
		if(record==null){
			if(checkNeedShortPhoneOption.isPresent()){
				record = communicateRecordMapper.getSupplierCommunicateByPhone(spone);
				traderSupplier.setPhone(spone);
			}
		}

		try {
			logger.info("根据电话 查询供应商、订单信息解析号码池start callOut:{}", JSON.toJSONString(callOut));
			if (record == null && callOut != null && callOut.getPhone() != null){
				if (checkNeedShortPhoneOption.isPresent()){
					record = communicateRecordMapper.getSupplierCommunicateByPhone(spone);
					traderSupplier.setPhone(spone);
				}
			}
		} catch (Exception e) {
			logger.error("根据电话 查询供应商、订单信息解析号码池前缀异常 callOut:{}", JSON.toJSONString(callOut));
		}

		if (null != callOut.getTraderId() && callOut.getTraderId() > 0) {
			traderSupplier.setTraderId(callOut.getTraderId());
		} else {
			if (null != record) {
				traderSupplier.setLastCommunicateType(record.getCommunicateType());
				traderSupplier.setLastRelatedId(record.getRelatedId());
			}
		}

		// 指定联系人
		if (null != callOut.getTraderContactId() && callOut.getTraderContactId() > 0) {
			traderSupplier.setTraderContactId(callOut.getTraderContactId());
		}

		// 接口调用
		String url = httpUrl + "tradersupplier/getsupplierinfobyphone.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderSupplier, clientId, clientKey,
					TypeRef2);
			JSONObject json = JSONObject.fromObject(result2.getData());
			// 对象包含另一个对象集合时用以下方法转化
			TraderSupplierVo res = JsonUtils.readValue(json.toString(), TraderSupplierVo.class);

			if(null == res){
				return null;
			}
			// 等级
//			if (JedisUtils.exists(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_OBJECT + res.getGrade())) {
//				JSONObject jsonObject = JSONObject
//						.fromObject(JedisUtils.get(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_OBJECT + res.getGrade()));
//				SysOptionDefinition sod = (SysOptionDefinition) JSONObject.toBean(jsonObject,
//						SysOptionDefinition.class);
//				res.setGradeStr(sod.getTitle());
//			}
			res.setGradeStr(getSysOptionDefinitionById(res.getGrade()).getTitle());
			if (null != record) {
				res.setLastCommuncateTime(record.getLastCommunicateTime());
			}

			// 沟通记录
			CommunicateRecord comm = new CommunicateRecord();
			comm.setTraderType(ErpConst.TWO);
			comm.setPhone(callOut.getPhone());
			List<CommunicateRecord> communicateRecordList = communicateRecordMapper.getCommunicateTraderByPhone(comm);

			// 订单销售人员查询
			List<Integer> userIds = new ArrayList<>();
			if (null != res.getBuyorderList()) {
				for (BuyorderVo b : res.getBuyorderList()) {
					userIds.add(b.getUserId());
				}
			}
			if (null != communicateRecordList) {
				for (CommunicateRecord c : communicateRecordList) {
					userIds.add(c.getCreator());
				}
			}

			if (userIds.size() > 0) {
				List<User> userList = userMapper.getUserByUserIds(userIds);
				// 信息补充
				if (null != res.getBuyorderList()) {
					for (BuyorderVo b : res.getBuyorderList()) {
						for (User u : userList) {
							if (b.getUserId().equals(u.getUserId())) {
								b.setBuyPerson(u.getUsername());
							}
						}
					}
				}

				if (null != communicateRecordList) {
					for (CommunicateRecord c : communicateRecordList) {
						for (User u : userList) {
							if (c.getCreator().equals(u.getUserId())) {
								c.setCreatorName(u.getUsername());
							}
						}
					}
				}
			}

			res.setCommunicateRecordList(communicateRecordList);
			return res;
		} catch (IOException e) {
			return null;
		}
	}

	@Autowired
	private CommunicateRecordApiService communicateRecordApiService;

	@Override
	public TraderCustomerVo getCustomerInfoByPhone(CallOut callOut) {
		TraderCustomerVo traderCustomer = new TraderCustomerVo();
		traderCustomer.setPhone(callOut.getPhone());


		Optional<String> optional = checkNeedShortPhone(callOut.getPhone());
		String spone=getShortPhone(callOut.getPhone());

		if (callOut.getPhone() != null){

			if (optional.isPresent()){
				traderCustomer.setPhone(spone);
				callOut.setPhone(spone);
			}
		}

		// 历史沟通记录查询
		CommunicateRecord record = communicateRecordMapper.getCustomerCommunicateByPhone(callOut.getPhone());
		if(optional.isPresent()){
			record = communicateRecordMapper.getCustomerCommunicateByPhone(spone);
			traderCustomer.setPhone(spone);
		}
		if (null != callOut.getTraderId() && callOut.getTraderId() > 0) {
			traderCustomer.setTraderId(callOut.getTraderId());
		} else {
			if (null != record) {
				traderCustomer.setLastCommunicateType(record.getCommunicateType());
				traderCustomer.setLastRelatedId(record.getRelatedId());
			}
		}

		// 指定联系人
		if (null != callOut.getTraderContactId() && callOut.getTraderContactId() > 0) {
			traderCustomer.setTraderContactId(callOut.getTraderContactId());
		}

		// 接口调用
		String url = httpUrl + "tradercustomer/getcustomerinfobyphone.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {

			ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderCustomer, clientId, clientKey,
					TypeRef2);
			if ( result2 == null) {
				return null;
			}
			JSONObject json = JSONObject.fromObject(result2.getData());
			// 对象包含另一个对象集合时用以下方法转化
			TraderCustomerVo res = JsonUtils.readValue(json.toString(), TraderCustomerVo.class);
			if(null == res){
				return null;
			}
			// 客户类型
			res.setCustomerTypeStr(getSysOptionDefinitionById(res.getCustomerType()).getTitle());
			res.setCustomerPropertyStr(getSysOptionDefinitionById(res.getCustomerNature()).getTitle());
			if (null != record) {
				res.setLastCommuncateTime(record.getLastCommunicateTime());
			}

			// 沟通记录
			CommunicateRecord comm = new CommunicateRecord();
			comm.setTraderType(ErpConst.ONE);
			comm.setPhone(callOut.getPhone());
			List<CommunicateRecord> communicateRecordList = communicateRecordMapper.getCommunicateTraderByPhone(comm);

			// 商机、报价、订单销售人员查询
			List<Integer> userIds = new ArrayList<>();
			if (null != res.getBussinessChanceList()) {
				for (BussinessChanceVo b : res.getBussinessChanceList()) {
					userIds.add(b.getUserId());
				}
			}
			if (null != res.getQuoteorderList()) {
				for (Quoteorder q : res.getQuoteorderList()) {
					userIds.add(q.getUserId());
				}
			}
			if (null != res.getSaleorderList()) {
				for (Saleorder s : res.getSaleorderList()) {
					userIds.add(s.getUserId());
				}
			}
			if (null != communicateRecordList) {
				for (CommunicateRecord c : communicateRecordList) {
					List<TagDto> tagDtos = communicateRecordApiService.getTag(c.getCommunicateRecordId());
					if(CollectionUtils.isNotEmpty(tagDtos)){
						List<Tag> tagList = tagDtos.stream()
							.map(tagDto -> {
								Tag tag = new Tag();
								tag.setTagId(tagDto.getTagId());
								tag.setTagName(tagDto.getTagName());
								// 设置其他字段...
								return tag;
							})
							.collect(Collectors.toList());
						c.setTag(tagList);
					}
					userIds.add(c.getCreator());
				}
			}

			if (userIds.size() > 0) {
				List<User> userList = userMapper.getUserByUserIds(userIds);
				// 信息补充
				if (null != res.getBussinessChanceList()) {
					for (BussinessChanceVo b : res.getBussinessChanceList()) {
						for (User u : userList) {
							if (b.getUserId().equals(u.getUserId())) {
								b.setSalerName(u.getUsername());
							}
						}
					}
				}
				if (null != res.getQuoteorderList()) {
					for (Quoteorder q : res.getQuoteorderList()) {
						for (User u : userList) {
							if (q.getUserId().equals(u.getUserId())) {
								q.setSalesName(u.getUsername());
							}
						}

						// 沟通次数
						List<Integer> keyList = new ArrayList<>();
						keyList.add(q.getQuoteorderId());
						List<CommunicateRecord> communicateRecord = communicateRecordMapper
								.getCommunicateRecord(keyList, SysOptionConstant.ID_245.toString());
						q.setCommunicateNum(communicateRecord.size());

					}
				}
				if (null != res.getSaleorderList()) {
					for (Saleorder s : res.getSaleorderList()) {
						for (User u : userList) {
							if (s.getUserId().equals(u.getUserId())) {
								s.setSalesName(u.getUsername());
							}
						}
					}
				}
				if (null != communicateRecordList) {
					for (CommunicateRecord c : communicateRecordList) {
						for (User u : userList) {
							if (c.getCreator().equals(u.getUserId())) {
								c.setCreatorName(u.getUsername());
							}
						}
					}
				}
			}
			res.setCommunicateRecordList(communicateRecordList);
			return res;
		} catch (Exception e) {
			logger.error("通话记录弹框获取客户信息失败"+ JSON.toJSONString(callOut),e);
			return null;
		}
	}



	@Override
	public List<?> getOrderList(CommunicateRecord communicateRecord) {
		String url = httpUrl + "call/getordersbycommunicatetype.htm";

		List<?> list = null;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, communicateRecord, clientId, clientKey,
					TypeRef2);
			switch (communicateRecord.getCommunicateType()) {
			case 244:// 商机
				list = (List<BussinessChanceVo>) result.getData();
				break;
			case 245:// 报价
				list = (List<Quoteorder>) result.getData();
				break;
			case 246:// 销售订单
				list = (List<Saleorder>) result.getData();
				break;
			case 247:// 采购订单
				list = (List<Buyorder>) result.getData();
				break;
			case 248:// 售后订单

				break;
			}
			return list;
		} catch (IOException e) {
			return null;
		}
	}

	@Override
	public Boolean saveCommunicate(CommunicateRecord communicateRecord, HttpServletRequest request, HttpSession session)
			throws Exception {
		User user = (User) session.getAttribute(ErpConst.CURR_USER);
		Long time = DateUtil.sysTimeMillis();

		Boolean res = false;
		CommunicateRecord record = null;
		communicateRecord.setCompanyId(user.getCompanyId());

		// 查询沟通记录是否存在
		if (null != communicateRecord.getCoid() && communicateRecord.getCoid() != "") {
			record = communicateRecordMapper.getCommunicateByCoidAndUserId(communicateRecord.getCoid(),
					user.getUserId());
		}

		if (null != record) {// 编辑
			communicateRecord.setCommunicateRecordId(record.getCommunicateRecordId());
			res = traderCustomerService.saveEditCommunicate(communicateRecord, request, session);
		} else {// 新增
			res = traderCustomerService.saveAddCommunicate(communicateRecord, request, session);
			if (StringUtils.isNotBlank(communicateRecord.getCoid())){
				redisUtils.sadd(ErpConst.RECORD_USER_KEY, user.getUserId());
			}
		}

		return res;
	}

	@Override
	public Trader getTraderByTraderId(Integer traderId) {
		String url = httpUrl + "trader/gettraderbytraderid.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Trader>> TypeRef2 = new TypeReference<ResultInfo<Trader>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, traderId, clientId, clientKey, TypeRef2);
			Trader trader = (Trader) result.getData();
			return trader;
		} catch (IOException e) {
			return null;
		}
	}

	@Override
	public BussinessChanceVo getBussincessChance(CallOut callOut) {
		String url = httpUrl + "call/getbussincesschance.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<BussinessChanceVo>> TypeRef2 = new TypeReference<ResultInfo<BussinessChanceVo>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, callOut, clientId, clientKey, TypeRef2);
			BussinessChanceVo bussinessChance = (BussinessChanceVo) result.getData();

			if (null == bussinessChance) {
				return null;
			}
			if (null != bussinessChance.getCreator() && bussinessChance.getCreator() > 0) {
				bussinessChance
						.setCreatorName(userMapper.selectByPrimaryKey(bussinessChance.getCreator()).getUsername());
			}
			// 商机来源
//			if (JedisUtils.exists(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_OBJECT + bussinessChance.getSource())) {
//				JSONObject jsonObject = JSONObject.fromObject(
//						JedisUtils.get(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_OBJECT + bussinessChance.getSource()));
//				SysOptionDefinition sod = (SysOptionDefinition) JSONObject.toBean(jsonObject,
//						SysOptionDefinition.class);
//				bussinessChance.setSourceName(sod.getTitle());
//			}
			bussinessChance.setSourceName(getSysOptionDefinitionById(bussinessChance.getSource()).getTitle());
			// 询价方式
//			if (JedisUtils.exists(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_OBJECT + bussinessChance.getCommunication())) {
//				JSONObject jsonObject = JSONObject.fromObject(JedisUtils
//						.get(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_OBJECT + bussinessChance.getCommunication()));
//				SysOptionDefinition sod = (SysOptionDefinition) JSONObject.toBean(jsonObject,
//						SysOptionDefinition.class);
//				bussinessChance.setCommunicationName(sod.getTitle());
//			}
			bussinessChance.setCommunicationName(getSysOptionDefinitionById(bussinessChance.getCommunication()).getTitle());
			// 商品分类
//			if (JedisUtils.exists(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_OBJECT + bussinessChance.getGoodsCategory())) {
//				JSONObject jsonObject = JSONObject.fromObject(JedisUtils
//						.get(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_OBJECT + bussinessChance.getGoodsCategory()));
//				SysOptionDefinition sod = (SysOptionDefinition) JSONObject.toBean(jsonObject,
//						SysOptionDefinition.class);
//				bussinessChance.setGoodsCategoryName(sod.getTitle());
//			}
			bussinessChance.setGoodsCategoryName(getSysOptionDefinitionById(bussinessChance.getGoodsCategory()).getTitle());
			// 地区
			if (bussinessChance.getAreaId() != null && bussinessChance.getAreaId() != 0) {
				bussinessChance.setAreas(getAddressByAreaId(bussinessChance.getAreaId()));
			}

			return bussinessChance;
		} catch (IOException e) {
			return null;
		}
	}

	@Override
	public Trader getTraderByPhone(CallOut callOut) {
		String url = httpUrl + "call/gettraderbyphone.htm";
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<Trader>> TypeRef2 = new TypeReference<ResultInfo<Trader>>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, callOut, clientId, clientKey, TypeRef2);
			Trader trader = (Trader) result.getData();
			return trader;
		} catch (IOException e) {
			return null;
		}
	}

	@Override
	public String getIsInit(String number) {
		String login = null; // 返回登录状态
		try {
			// 需要传递的参数封装成xml格式
			Map map = new HashMap<>();
			map.put("employeeID", number);

			XmlExercise xmlExercise = new XmlExercise();
			String xmlStr = xmlExercise.mapToXml(map, "data");
			
			// 调用接口
			RPCServiceClient ser = new RPCServiceClient();
			Options options = ser.getOptions();

			// 指定调用WebService的URL
			EndpointReference targetEPR = new EndpointReference(callNamespace);
			options.setTo(targetEPR);
			// options.setAction("命名空间/WS 方法名");
			options.setAction("getEmployeeLoginInfo");

			// 指定sfexpressService方法的参数值
			Object[] opAddEntryArgs = new Object[] { xmlStr };
			// 指定sfexpressService方法返回值的数据类型的Class对象
			Class[] classes = new Class[] { String.class };
			// 指定要调用的sfexpressService方法及WSDL文件的命名空间
			QName opAddEntry = new QName(callUrl, "getEmployeeLoginInfo");
			// 调用sfexpressService方法并输出该方法的返回值
			Object[] str = ser.invokeBlocking(opAddEntry, opAddEntryArgs, classes);

			// 接口返回xml字符串
			String xmlString = str[0].toString();
			
			Map result = xmlExercise.xmlToMap(xmlString);
			
			if(null != result && result.get("code").equals("0")){
				Map infoData = (Map) result.get("data");
				//Map infoData = (Map) resultMap.get(0);
				login = infoData.get("type").toString();
			}
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return login;
	}

	@Override
	public Map<String,Object> getStatistics(String starttime, String endtime) {
		Map<String,Object> resultMap = new HashMap<String, Object>();
		try {
			// 需要传递的参数封装成xml格式
			Map map = new HashMap<>();
			map.put("starttime", starttime);
			map.put("endtime", endtime);
			
			XmlExercise xmlExercise = new XmlExercise();
			String xmlStr = xmlExercise.mapToXml(map, "data");
			
			// 调用接口
			RPCServiceClient ser = new RPCServiceClient();
			Options options = ser.getOptions();

			// 指定调用WebService的URL
			EndpointReference targetEPR = new EndpointReference(callNamespace);
			options.setTo(targetEPR);
			// options.setAction("命名空间/WS 方法名");
			options.setAction("getStatistics");

			// 指定sfexpressService方法的参数值
			Object[] opAddEntryArgs = new Object[] { xmlStr };
			// 指定sfexpressService方法返回值的数据类型的Class对象
			Class[] classes = new Class[] { String.class };
			// 指定要调用的sfexpressService方法及WSDL文件的命名空间
			QName opAddEntry = new QName(callUrl, "getStatistics");
			// 调用sfexpressService方法并输出该方法的返回值
			Object[] str = ser.invokeBlocking(opAddEntry, opAddEntryArgs, classes);

			// 接口返回xml字符串
			String xmlString = str[0].toString();
			
			Map result = xmlExercise.xmlToMap(xmlString);
			
			if(null != result && result.get("code").equals("0")){
				JSONObject jsonObject = JSONObject.fromObject(result.get("data"));
				resultMap = (Map<String, Object>) JSONObject.toBean(jsonObject, Map.class);
			}
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return resultMap;
	}

	private List<CallStaticDto> getCallStaticDtoList(String starttime, String endtime, List<String> numberList) {
		if(StringUtils.isEmpty(starttime) || StringUtils.isEmpty(endtime) || numberList == null || numberList.isEmpty()){
			return new ArrayList<>();
		}
		return communicateRecordMapper.getCallStaticList(starttime, endtime, numberList);
	}

	@Override
	public List getUserDetail(String starttime, String endtime, List<String> numberList) {
		List resultMap = new ArrayList<>();
		try {
			// 需要传递的参数封装成xml格式
			Map map = new HashMap<>();
			map.put("starttime", starttime);
			map.put("endtime", endtime);
			map.put("employeeID", numberList);
			
			XmlExercise xmlExercise = new XmlExercise();
			String xmlStr = xmlExercise.mapToXml(map, "data");
			
			// 调用接口
			RPCServiceClient ser = new RPCServiceClient();
			Options options = ser.getOptions();

			// 指定调用WebService的URL
			EndpointReference targetEPR = new EndpointReference(callNamespace);
			options.setTo(targetEPR);
			// options.setAction("命名空间/WS 方法名");
			options.setAction("getUserDetail");

			// 指定sfexpressService方法的参数值
			Object[] opAddEntryArgs = new Object[] { xmlStr };
			// 指定sfexpressService方法返回值的数据类型的Class对象
			Class[] classes = new Class[] { String.class };
			// 指定要调用的sfexpressService方法及WSDL文件的命名空间
			QName opAddEntry = new QName(callUrl, "getUserDetail");
			// 调用sfexpressService方法并输出该方法的返回值
			Object[] str = ser.invokeBlocking(opAddEntry, opAddEntryArgs, classes);

			// 接口返回xml字符串
			String xmlString = str[0].toString();
			
			Map result = xmlExercise.xmlToMapList(xmlString);
			
			if(null != result && result.get("code").equals("0") && result.get("data") instanceof List){
				resultMap = (List) result.get("data");
				List<CallStaticDto> callStaticDtos2 = getCallStaticDtoList(starttime,endtime,numberList);
				mergeList(resultMap,callStaticDtos2);
				return resultMap;

//				List<CallStaticDto> callStaticDtos = new ArrayList<>();
//				for(Object o : resultMap){
//					String json = JSON.toJSONString(o);
//					CallStaticDto user = JSON.parseObject(json, CallStaticDto.class);
//					callStaticDtos.add(user);
//				}				//补充企业微信语音通话的数据
//				List<CallStaticDto> callStaticDtos2 = getCallStaticDtoList(starttime,endtime,numberList);
//				return mergeCallStaticDtos(callStaticDtos,callStaticDtos2);
			}
		} catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
		}
		return resultMap;
	}

	private void mergeList(List voipList, List<CallStaticDto> list2) {
		if(null != voipList && null != list2 && list2.size() > 0){
			Map<String, CallStaticDto> resultMap = list2.stream()
					.collect(Collectors.toMap(
							CallStaticDto::getName,
							dto -> dto,
							(existing, replacement) -> existing  // 处理重复键
					));

			for (Object voip : voipList) {
				Map<String, Object> voipMap = (Map<String, Object>) voip;
				String name = voipMap.get("Name") != null ? voipMap.get("Name").toString() : null;
				if (name == null) {
					continue;  // 跳过name为空的记录
				}
				
				CallStaticDto dto = resultMap.get(name);
				if(null != dto) {  // 如果List2中包含这个对象开始合并其值
					// 合并callIn
					mergeIntegerValue(voipMap, "callIn", dto.getCallIn());
					
					// 合并callSucc
					mergeIntegerValue(voipMap, "callSucc", dto.getCallSucc());
					
					// 合并callOut
					mergeIntegerValue(voipMap, "callOut", dto.getCallOut());
					
					// 合并callOutSucc
					mergeIntegerValue(voipMap, "callOutSucc", dto.getCallOutSucc());
					
					// 合并callTimeSucc
					mergeIntegerValue(voipMap, "callTimeSucc", dto.getCallTimeSucc());
					
					// 合并callOutTimeSucc
					mergeIntegerValue(voipMap, "callOutTimeSucc", dto.getCallOutTimeSucc());
					
					// 合并callTimeOneSucc
					mergeIntegerValue(voipMap, "callTimeOneSucc", dto.getCallTimeOneSucc());
					
					// 合并callOutTimeOneSucc
					mergeIntegerValue(voipMap, "callOutTimeOneSucc", dto.getCallOutTimeOneSucc());
					
					// 合并callTime (BigDecimal类型)
					mergeBigDecimalValue(voipMap, "callTime", dto.getCallTime());
				}
			}
		}
	}

	/**
	 * 合并Integer类型的值
	 */
	private void mergeIntegerValue(Map<String, Object> voipMap, String key, int value) {
		Object currentValue = voipMap.get(key);
		if (currentValue == null) {
			voipMap.put(key, value);
		} else {
			try {
				int currentInt = Integer.parseInt(currentValue.toString());
				voipMap.put(key, currentInt + value);
			} catch (NumberFormatException e) {
				// 如果转换失败，直接使用新值
				voipMap.put(key, value);
			}
		}
	}

	/**
	 * 合并BigDecimal类型的值
	 */
	private void mergeBigDecimalValue(Map<String, Object> voipMap, String key, BigDecimal value) {
		if (value == null) {
			return;
		}
		
		Object currentValue = voipMap.get(key);
		if (currentValue == null) {
			voipMap.put(key, value);
		} else {
			try {
				BigDecimal currentDecimal = new BigDecimal(currentValue.toString());
				voipMap.put(key, currentDecimal.add(value));
			} catch (NumberFormatException e) {
				// 如果转换失败，直接使用新值
				voipMap.put(key, value);
			}
		}
	}

	@Override
	public List<CallFailed> queryCallFailedlistPage(CallFailed callFailed, Page page) {
		
		if(null != callFailed.getStarttime()){
			callFailed.setStarttimeLong(DateUtil.convertLong(callFailed.getStarttime(), "yyyy-MM-dd"));
		}
		if(null != callFailed.getEndtime()){
			callFailed.setEndtimeLong(DateUtil.convertLong(callFailed.getEndtime()+" 23:59:59", "yyyy-MM-dd HH:mm:ss"));
		}
		
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("callFailed", callFailed);
		map.put("page", page);
		
		List<CallFailed> list = callFailedMapper.queryCallFailedlistPage(map);
		
		//信息补充
		if(list.size() > 0){
			List<String> coids = new ArrayList<>();
			
			for(CallFailed c : list){
				if(c.getCoid() != null && !c.getCoid().equals("")){
					coids.add(c.getCoid());
				}
				
				String regEx = "1[34578]{1}\\d{9}$";
				// 编译正则表达式
				Pattern pattern = Pattern.compile(regEx);
				Matcher matcher = pattern.matcher(c.getCallerNumber());
				if (matcher.matches()) {
					// 电话
					String phoneStr = c.getCallerNumber().substring(0, 7);
					QCellCore qCellCoreByPhone = this.getQCellCoreByPhone(phoneStr);
					if(null != qCellCoreByPhone){
						c.setPhoneArea(qCellCoreByPhone.getProvince()+qCellCoreByPhone.getCity());
					}
				} else if (c.getCallerNumber().length() == 8) {
					c.setPhoneArea("江苏南京");
				}else{
					String code = c.getCallerNumber().substring(0, 4);
					QCellCore qCellCoreByCode = this.getQCellCoreByCode(code);
					if(null != qCellCoreByCode){
						c.setPhoneArea(qCellCoreByCode.getProvince()+qCellCoreByCode.getCity());
					}else{
						String code2 = c.getCallerNumber().substring(0, 3);
						QCellCore qCellCoreByCode2 = this.getQCellCoreByCode(code2);
						if(null != qCellCoreByCode2){
							c.setPhoneArea(qCellCoreByCode2.getProvince()+qCellCoreByCode2.getCity());
						}
					}
				}
			}
			
			//有录音信息
			if(coids.size() > 0){
				List resultList = new ArrayList<>();
				try {
					// 需要传递的参数封装成xml格式
					Map coidMap = new HashMap<>();
					map.put("coid", coids);
					
					XmlExercise xmlExercise = new XmlExercise();
					String xmlStr = xmlExercise.mapToXml(map, "data");
					
					// 调用接口
					RPCServiceClient ser = new RPCServiceClient();
					Options options = ser.getOptions();

					// 指定调用WebService的URL
					EndpointReference targetEPR = new EndpointReference(callNamespace);
					options.setTo(targetEPR);
					// options.setAction("命名空间/WS 方法名");
					options.setAction("getRecordByCoid");

					// 指定sfexpressService方法的参数值
					Object[] opAddEntryArgs = new Object[] { xmlStr };
					// 指定sfexpressService方法返回值的数据类型的Class对象
					Class[] classes = new Class[] { String.class };
					// 指定要调用的sfexpressService方法及WSDL文件的命名空间
					QName opAddEntry = new QName(callUrl, "getRecordByCoid");
					// 调用sfexpressService方法并输出该方法的返回值
					Object[] str = ser.invokeBlocking(opAddEntry, opAddEntryArgs, classes);

					// 接口返回xml字符串
					String xmlString = str[0].toString();
					
					Map result = xmlExercise.xmlToMapList(xmlString);
					
					if(null != result && result.get("code").equals("0")){
						resultList = (List) result.get("data");
						
						if(resultList.size() > 0){
							for(CallFailed c : list){
								if(null != c.getCoid() && !c.getCoid().equals("")){
									for(int i=0;i<resultList.size();i++){
										Map dataMap = new HashMap<>();
										dataMap = (Map) resultList.get(i);
										
										if(dataMap.get("COID").toString().equals(c.getCoid())){
											c.setUrl(dataMap.get("URL").toString());
											c.setFilelen(Integer.parseInt(dataMap.get("FILELEN").toString()));
										}
									}
								}
							}
						}
					}
				} catch (Exception e) {
					logger.error(Contant.ERROR_MSG, e);
				}
			}
		}
		return list;
	}

	@Override
	public List<CallFailed> getCallFailedDialBackUser() {
		return callFailedMapper.getCallFailedDialBackUser();
	}

	@Override
	public QCellCore getQCellCoreByCode(String code) {
		return qCellCoreMapper.getQCellCoreByCode(code);
	}

	@Override
	public Boolean editCallFailedCoid(CallFailed callFailed, HttpSession session) {
		User user = (User) session.getAttribute(ErpConst.CURR_USER);
		callFailed.setIsDialBack(ErpConst.ONE);
		callFailed.setDialBackUserId(user.getUserId());
		Integer op = callFailedMapper.updateByPrimaryKey(callFailed);
		if(op > 0){
			return true;
		}
		return false;
	}

	@Override
	public List<CommunicateRecord> queryRecordlistPage(CommunicateRecord communicateRecord, Page page,HttpSession session) {
		//客户名称
		if(null != communicateRecord.getTraderName() && !communicateRecord.getTraderName().equals("")){
			String url = httpUrl + "trader/gettradeidsbytradername.htm";
			Trader trader = new Trader();
			trader.setTraderName(communicateRecord.getTraderName());
			User user = (User) session.getAttribute(ErpConst.CURR_USER);
			trader.setCompanyId(user.getCompanyId());
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<Integer>>> TypeRef2 = new TypeReference<ResultInfo<List<Integer>>>() {
			};
			try {
				ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, trader, clientId, clientKey, TypeRef2);
				List<Integer> traderIds = (List<Integer>) result.getData();
				if(null != traderIds){
					communicateRecord.setTraderIds(traderIds);
				}
			} catch (IOException e) {
				logger.error("queryRecordlistPage",e);
				return null;
			}
		}
		
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("communicateRecord", communicateRecord);
		map.put("page", page);
		
		List<CommunicateRecord> list = communicateRecordMapper.queryCallRecordListPage(map);
		if(list.size() > 0){
			List<String> coids = new ArrayList<>();
			List<Integer> traderIds = new ArrayList<>();
			for(CommunicateRecord c : list){
				if(StringUtil.isNotBlank(c.getCoid()) && !c.getSyncStatus().equals(1)){
					coids.add(c.getCoid());
				}
				if(c.getTraderId() > 0){
					traderIds.add(c.getTraderId());
				}
				String sphone=getShortPhone(c.getPhone());
				c.setPhone(sphone);
				//归属地设置
				qzone(c, sphone);

				//归属人
				if(c.getTraderId() > 0 && c.getTraderType() > 0){
					User sale = userMapper.getUserByTraderId(c.getTraderId(), c.getTraderType());
					if (null != sale && null != sale.getUsername()) {
						c.setOwnerUsername(sale.getUsername());
					}
				}
			}
			//客户名称
			customerName(list, traderIds);
		}
		return list;
	}

	@Value("${voice.url:}")
	private String voiceUrl;

	@Override
	public List<Map<String,Object>> getRecordListByCoid(List<String> coidList) {
		try {
			// 需要传递的参数封装成xml格式
			Map<String, Object> coidMap = new HashMap<>();
			coidMap.put("coid", coidList);

			XmlExercise xmlExercise = new XmlExercise();
			String xmlStr = xmlExercise.mapToXml(coidMap, "data");

			// 调用接口
			RPCServiceClient ser = new RPCServiceClient();
			Options options = ser.getOptions();

			// 指定调用WebService的URL
			EndpointReference targetEPR = new EndpointReference(callNamespace);
			options.setTo(targetEPR);
			// options.setAction("命名空间/WS 方法名");
			options.setAction("getRecordByCoid");

			// 指定sfexpressService方法的参数值
			Object[] opAddEntryArgs = new Object[] { xmlStr };
			// 指定sfexpressService方法返回值的数据类型的Class对象
			Class[] classes = new Class[] { String.class };
			// 指定要调用的sfexpressService方法及WSDL文件的命名空间
			QName opAddEntry = new QName(callUrl, "getRecordByCoid");
			// 调用sfexpressService方法并输出该方法的返回值
			Object[] str = ser.invokeBlocking(opAddEntry, opAddEntryArgs, classes);

			// 接口返回xml字符串
			String xmlString = str[0].toString();

			Map<String, Object> result = xmlExercise.xmlToMapList(xmlString);
			//logger.info("沟通记录webservice接口返回result：" + JSONObject.fromObject(result));
			if(null != result && result.get("code").equals("0")) {
				List<Map<String,Object>> resultList = (List<Map<String,Object>>) result.get("data");
				return resultList;
			}

		} catch (Exception e){
				logger.error("getRecordListByCoid",e);
		}
		return new ArrayList<>();
	}

	/**
	 * 截取前缀
	 * @param orginPhone 0315366165999
	 * @return  15366165999
	 */
	private String getShortPhone(String orginPhone) {
		if(orginPhone==null){
			return "";
		}
		List<String> phonePrefixArrayList = JSON.parseArray(phonePrefixArray, String.class);
		String phoneNumber = orginPhone;
		Optional<String> optional = phonePrefixArrayList.stream().filter(item -> phoneNumber.startsWith(item)).findFirst();
		if (optional.isPresent()){
			orginPhone=orginPhone.substring(2);
		}
		return orginPhone;
	}
	private Optional<String> checkNeedShortPhone(String orginPhone) {
		List<String> phonePrefixArrayList = JSON.parseArray(phonePrefixArray, String.class);
		String phoneNumber = orginPhone;
		Optional<String> optional = phonePrefixArrayList.stream().filter(item -> phoneNumber.startsWith(item)).findFirst();
		return optional;
	}

	private void customerName(List<CommunicateRecord> list, List<Integer> traderIds) {
		if(traderIds.size() > 0){
			String url = httpUrl + "trader/gettraderbytraderids.htm";
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<List<Trader>>> TypeRef2 = new TypeReference<ResultInfo<List<Trader>>>() {
			};
			try {
				ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, traderIds, clientId, clientKey, TypeRef2);
				List<Trader> traderList = (List<Trader>) result.getData();
				if(null != traderList){
					for(Trader t : traderList){
						for(CommunicateRecord c : list){
							if(t.getTraderId().equals(c.getTraderId())){
								c.setTraderName(t.getTraderName());
							}
						}
					}
				}
			} catch (IOException e) {
				logger.error("queryRecordlistPage",e);
			}
		}
	}

	private void commentCall(CommunicateRecord c) {
		//查询录音点评
		List<Comment> commentList =  commentMapper.getComList(c);
		c.setCommentList(commentList);
		//判断是否转译完成
		PhoneticWriting phoneticWriting = phoneticWritingMapper.getPhoneticWriting(c.getCommunicateRecordId());
		if(phoneticWriting != null){
			if(StringUtils.isNotBlank(phoneticWriting.getOriginalContent())){
c.setIsTranslation(1);
			}else {
				c.setIsTranslation(0);
			}
		}else{
			c.setIsTranslation(0);
		}
	}

	private void qzone(CommunicateRecord c, String sphone) {
		//号码归属地
		String regEx = "1[345789]{1}\\d{9}$";
		// 编译正则表达式
		Pattern pattern = Pattern.compile(regEx);
		Matcher matcher = pattern.matcher(sphone);
		if (matcher.matches()) {
			// 电话

			String phoneStr = sphone.substring(0, 7);
			QCellCore qCellCoreByPhone = this.getQCellCoreByPhone(phoneStr);
			if(null != qCellCoreByPhone){
				c.setPhoneArea(qCellCoreByPhone.getProvince()+qCellCoreByPhone.getCity());
			}
		} else if (c.getPhone().length() == 8) {
			c.setPhoneArea("江苏南京");
		}else{
			QCellCore qCellCoreByCode = null;
			if(null != c.getPhone() && c.getPhone().length() > 4){
				String code = c.getPhone().substring(0, 4);
				qCellCoreByCode = this.getQCellCoreByCode(code);
			}
			if(null != qCellCoreByCode){
				c.setPhoneArea(qCellCoreByCode.getProvince()+qCellCoreByCode.getCity());
			}else{
				if(null != c.getPhone() && c.getPhone().length() > 3){
					String code2 = c.getPhone().substring(0, 3);
					QCellCore qCellCoreByCode2 = this.getQCellCoreByCode(code2);
					if(null != qCellCoreByCode2){
						c.setPhoneArea(qCellCoreByCode2.getProvince()+qCellCoreByCode2.getCity());
					}
				}
			}
		}
	}

	@Override
	public List<User> getRecordUser() {
		return communicateRecordMapper.getRecordUser();
	}

	@Override
	public List<CommunicateRecord> selectRecordlistPage(CommunicateRecord communicateRecord, Page page,
			HttpSession session) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("communicateRecord", communicateRecord);
		map.put("page", page);
		
		List<CommunicateRecord> list = communicateRecordMapper.selectCallRecordListPage(map);
		
		return list;
	}

	@Override
	public ResultInfo<?> getRecordCoidURIByCommunicateRecordId(Integer communicateRecordId) {
		String uri = "";
		uri = communicateRecordMapper.getRecordCoidURIByCommunicateRecordId(communicateRecordId);
		if (StringUtil.isNotBlank(uri)) {
			return new ResultInfo(0, "查询成功",uri);
		}else {
			return new ResultInfo(1, "该录音文件没有上传",uri);
		}
		
	}

	@Override
	public PhoneticWriting getPhoneticWriting(CommunicateRecord communicateRecord) {
		return phoneticWritingMapper.getPhoneticWriting(communicateRecord.getCommunicateRecordId());
	}

	@Override
	public List<ModificationRecord> getMrList(CommunicateRecord communicateRecord) {
		return modificationRecordMapper.getMrInfoList(communicateRecord);
	}

	@Override
	public List<Comment> getComList(CommunicateRecord communicateRecord) {
		List<Comment> commentList = commentMapper.getComList(communicateRecord);
		for(int i=0;i < commentList.size();i++){
			// 查询点评人
			commentList.get(i).setUserName(getUserNameByUserId(commentList.get(i).getCreator()));
		}
		return commentList;
	}

	@Override
	public CommunicateRecord getCommunicateRecordById(CommunicateRecord communicateRecord) {
		return communicateRecordMapper.getCommunicate(communicateRecord);
	}

	@Override
	public List<Integer> getTraderIdsByPhone(String phone) {
		if(StringUtil.isBlank(phone)){
			return null;
		}
		try {
			if(org.apache.commons.lang3.StringUtils.isNotBlank(phone)&&phone.length()>12){
				List<String> phonePrefixArrayList = JSON.parseArray(phonePrefixArray, String.class);
				String phoneNumber =phone;
				Optional<String> optional = phonePrefixArrayList.stream().filter(item -> phoneNumber.startsWith(item)).findFirst();
				if (optional.isPresent()){
					phone=phone.substring(2);
				}
			}
		} catch (Exception e) {
			logger.error("根据来电获取交易者信息解析号码池前缀异常 callOut:{}", JSON.toJSONString(phone));
		}
		phone=StringUtil.cleanPhone(phone);

		return traderContactGenerateMapper.getTraderIdsByPhone(phone);
	}

	/**
	 * 通过客户id查询归属销售
	 * @param traderId
	 * @return
	 */
	@Override
	public Integer getuserIdByTraderId(Integer traderId) {
		return userMapper.getuserIdByTraderId(traderId);

	}

	@Override
	public Integer getCallCenterVersionByBrowserInfo(HttpServletRequest request) {
		String agent = request.getHeader("User-Agent");
		UserAgent userAgent = UserAgent.parseUserAgentString(agent);
		if (userAgent.getBrowser().getName().toLowerCase().contains("firefox")){
			return 0;
		}
		return 1;
	}

	@PostConstruct
	private void recordUserInit() {
		List<User> recordUser = getRecordUser();
		if (CollectionUtils.isEmpty(recordUser)) {
			return;
		}
		redisUtils.sadd(ErpConst.RECORD_USER_KEY,
				recordUser.stream()
						.map(User::getUserId)
						.distinct()
						.map(String::valueOf).toArray(String[]::new));
	}

	@Override
	public List<User> getRecordUserNew() {
//		Set<String> recordUserSet = redisUtils.smembers(ErpConst.RECORD_USER_KEY);
//		if (CollectionUtils.isEmpty(recordUserSet)){
//			return null;
//		}
		List<Integer> recordUserSet = communicateRecordMapper.getCommunicateRecordAllCreator();

		return userMapper.getUserBaseInfoByUserIds(recordUserSet.stream()
				.distinct().collect(Collectors.toList()));
	}
}
