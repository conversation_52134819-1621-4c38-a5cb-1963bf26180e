package com.vedeng.temporal.polling.universal.executor;

import java.util.Map;

/**
 * 本地查询执行器接口
 * 
 * 定义了执行本地数据查询的标准接口，主要用于数据库查询、缓存查询等本地数据访问。
 * 该接口专门处理本地数据访问相关的逻辑，包括数据库连接、查询优化、事务处理等。
 * 
 * 职责范围：
 * - 执行数据库查询操作
 * - 处理数据库连接和事务
 * - 实现查询结果缓存
 * - 处理数据访问异常
 * - 优化查询性能
 * 
 * @param <T> 查询返回的数据类型
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-24
 */
public interface LocalQueryExecutor<T> {
    
    /**
     * 执行本地查询
     * 
     * 通过本地数据访问（如数据库、缓存等）获取数据。
     * 实现类应该包含适当的连接池管理、查询优化和异常处理逻辑。
     * 
     * @param context 查询上下文，包含数据库配置、查询参数等信息
     * @return 查询结果数据
     * @throws RuntimeException 当数据库异常、查询异常或数据解析异常时抛出
     */
    T executeLocalQuery(Map<String, Object> context) throws Exception;
    
    /**
     * 清理查询缓存
     * 
     * 用于清理与特定查询相关的缓存数据，确保数据一致性。
     * 
     * @param context 查询上下文
     */
    default void clearQueryCache(Map<String, Object> context) {
        // 默认实现：什么都不做
        // 子类可以根据需要实现具体的缓存清理逻辑
    }
    
    /**
     * 获取执行器名称
     * 用于日志记录和监控
     * 
     * @return 执行器名称
     */
    default String getExecutorName() {
        return this.getClass().getSimpleName();
    }
}