<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="title" value="编辑订单税率" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script src="/webjars/ezadmin/plugins/cascader/xm-select.js"></script>
<link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/static/layui/css/layui.css">
<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/edit_order_ratio.js?rnd=${resourceVersionKey}'></script>
<style>
    ul>li {
        padding-top: 20px;
    }
</style>
<div class="formpublic">
    <form method="post" action="<%=basePath%>order/saleorder/saveOrderRatioEdit.do" id="editOrderRatioEdit">
        <input type="hidden" name="formToken" id="formToken" value="${formToken}"/>
        <ul>

            <li>
                <div class="form-tips">
                    <lable>发票形式:</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <select name="invoiceProperty" id="invoiceProperty">
                            <option value="1" <c:if test="${invoiceProperty == 1}">selected</c:if>>纸质发票</option>
                            <option value="2" <c:if test="${invoiceProperty == 2}">selected</c:if>>电子发票</option>
                            <option value="3" <c:if test="${invoiceProperty == 3}">selected</c:if>>数电发票</option>
                        </select>
                    </div>
                </div>
            </li>
            <li>
                <div class="infor_name infor_name96">
                    <label>发票寄送:</label>
                </div>
                <div class="f_left inputfloat">
                    <c:if test="${sendInvoice == 1}"><span style="margin-top: 4px;">寄送</span></c:if>
                    <c:if test="${sendInvoice == 0}"><span style="margin-top: 4px;">不寄送</span></c:if>
                </div>
            </li>
            <li id="invoiceContactLi">
                <div class="infor_name infor_name96">
                    <label>收票联系人：</label>
                </div>
                <div class="f_left">
                  <span style="margin-top: 4px;">${invoiceTraderContactName}</span>
                </div>
            </li>
            <li id="invoiceContactAddressLi">
                <div class="infor_name infor_name96">
                    <label>收票地址：</label>
                </div>
                <div class="f_left">
                    <div>
                       <span style="margin-top: 4px;">${customerContactAddressValue}</span>
                    </div>
                </div>
            </li>
        </ul>
        <br>
        <div class="add-tijiao tcenter">
            <input type="hidden" name="orderId" value='${orderId}'>
            <input type="hidden" name="invoiceApplyId" id="invoiceApplyId" value='${invoiceApplyId}'>
            <input type="hidden" value="${saleorder.orderType}" id="orderType" name="orderType">
            <button type="button" class="bg-light-blue" onclick="saveRatioEdit()">确定</button>
            <button class="dele" type="button" id="close-layer">取消</button>
        </div>
    </form>
</div>
<script type="text/javascript">
    var customerContact;
    var customerContactAddress;



</script>
<%@ include file="../../common/footer.jsp" %>