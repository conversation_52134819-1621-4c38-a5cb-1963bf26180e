package com.vedeng.order.service.validator.impl;

import com.vedeng.common.util.JsonUtils;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.firstengage.model.RegistrationNumber;
import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.service.validator.QualifyAutoAudtioValidator;
import com.vedeng.order.service.validator.dto.QualifyAutoAudtioException;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.vo.TraderCertificateVo;
import com.vedeng.trader.model.vo.TraderMedicalCategoryVo;
import com.wms.service.context.ThreadLocalContext;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public abstract class AbstractQualifyAutoAudtioValidator implements QualifyAutoAudtioValidator {

    public static Logger logger = LoggerFactory.getLogger(AbstractQualifyAutoAudtioValidator.class);

    //---------------------------------------以下是SKU相关信息--------------------------------------
    protected CoreSpuGenerate getSpuInfo(String skuNo) {

        return ThreadLocalContext.get(QualifyAutoAudtioUtil.getSpuInfoKey(skuNo));
    }

    protected RegistrationNumber getRegisterCertificateInfo(String skuNo) {

        return ThreadLocalContext.get(QualifyAutoAudtioUtil.getRegisterCertificateInfoKey(skuNo));
    }

    protected boolean hasRegistrationCert(String skuNo){

        return ThreadLocalContext.get(QualifyAutoAudtioUtil.hasRegistrationCertKey(skuNo));
    }

    //---------------------------------------以下是供应商相关信息------------------------------------
    protected Trader getTradeInfo(BuyorderGoodsVo buyOrderGoods) {
        return ThreadLocalContext.get(QualifyAutoAudtioUtil.getTraderInfoKey());
    }

    /**
     * 营业执照是否已经上传
     * @param traderId
     * @return
     */
    protected boolean isBusinessLicenseUpload(Integer traderId) {
        return ThreadLocalContext.get(QualifyAutoAudtioUtil.getBusinessLicenseUploadKey(traderId));
    }

    /**
     * 营业执照是否在有效期
     * @param traderId
     * @return
     */
    protected boolean isBusinessLicenseInValidityPeriod(Integer traderId) {
        return ThreadLocalContext.get(QualifyAutoAudtioUtil.getBusinessValidKey(traderId));
    }

    /**
     * 营业执照是否含有医疗机械
     * @param traderId
     * @return
     */
    protected boolean isBusinessMedical(Integer traderId) {
        return ThreadLocalContext.get(QualifyAutoAudtioUtil.getBusinessMedicalKey(traderId));
    }

    /**
     * 第二级分类是否上传
     * @param traderId
     * @return
     */
    protected boolean isSecondCategoryUpload(Integer traderId) {
        return ThreadLocalContext.get(QualifyAutoAudtioUtil.getSecondCategoryUploadKey(traderId));
    }

    /**
     * 第二级分类是否匹配
     * @param traderId
     * @param skuNo
     * @return
     */
    protected boolean isSecondCategoryMatch(Integer traderId, String skuNo) {


        List<TraderMedicalCategoryVo> oldSecondCategoryList = ThreadLocalContext.get(QualifyAutoAudtioUtil.getOldSecondCategoryListKey(traderId));

        List<TraderMedicalCategoryVo> newSecondCategoryList = ThreadLocalContext.get(QualifyAutoAudtioUtil.getNewSecondCategoryListKey(traderId));

        //获取首营信息
        FirstEngage firstEngage = ThreadLocalContext.get(QualifyAutoAudtioUtil.getFirstEngageInfoKey(skuNo));

        List<Integer> oldSecondCategoryIdList = Optional.ofNullable(oldSecondCategoryList)
                .map(list -> list.stream().map(e->e.getMedicalCategoryId()).collect(Collectors.toList()))
                .orElseGet(ArrayList::new);

        List<Integer> newSecondCategoryIdList = Optional.ofNullable(newSecondCategoryList)
                .map(list -> list.stream().map(e->e.getMedicalCategoryId()).collect(Collectors.toList()))
                .orElseGet(ArrayList::new);


        if(newSecondCategoryIdList.contains(firstEngage.getNewStandardCategoryId())){
            return true;
        }

        if (oldSecondCategoryIdList.contains(firstEngage.getOldStandardCategoryId())) {
            return true;
        }

        return false;
    }

    /**
     * 产品注册证是否有效
     * @param skuNo
     * @return
     */
    protected boolean isProductRegisteValid(String skuNo) {

        RegistrationNumber registrationNumber = ThreadLocalContext.get(QualifyAutoAudtioUtil.getRegisterCertificateInfoKey(skuNo));

        return registrationNumber.getEffectiveDate() == null || registrationNumber.getEffectiveDate() >= System.currentTimeMillis();

    }

    /**
     * 第三级分类是否上传
     * @param traderId
     * @return
     */
    protected boolean isThirdCategoryUpload(Integer traderId) {

        return ThreadLocalContext.get(QualifyAutoAudtioUtil.getThreeMedicalLicenseUploadKey(traderId));

    }

    /**
     * 第三级分类是否在有效期
     * @param traderId
     * @return
     */
    protected boolean isThirdCategoryInValidityPeriod(Integer traderId) {

        List<TraderCertificateVo> threeMedicalList = ThreadLocalContext.get(QualifyAutoAudtioUtil.getThreeMedicalLicenseListKey(traderId));

        boolean isValid = threeMedicalList.stream().anyMatch(business -> business.getEndtime() == null || business.getEndtime() == 0
                || business.getEndtime() >= System.currentTimeMillis());

        return isValid;
    }

    /**
     * 第三级分类是否匹配
     * @param traderId
     * @param skuNo
     * @return
     */
    protected boolean isThirdCategoryMatch(Integer traderId, String skuNo) {

        List<TraderMedicalCategoryVo> oldThirdCategoryList = ThreadLocalContext.get(QualifyAutoAudtioUtil.getOldThirdCategoryListKey(traderId));

        List<TraderMedicalCategoryVo> newThirdCategoryList = ThreadLocalContext.get(QualifyAutoAudtioUtil.getNewThirdCategoryListKey(traderId));

        //获取首营信息
        FirstEngage firstEngage = ThreadLocalContext.get(QualifyAutoAudtioUtil.getFirstEngageInfoKey(skuNo));

        try {
            logger.info("采购自动审核 {} {} {}",traderId,skuNo, JsonUtils.translateToJson(firstEngage));
        } catch (IOException e) {
           //
        }

        List<Integer> oldThirdCategoryIdList = Optional.ofNullable(oldThirdCategoryList)
                .map(list -> list.stream().map(e->e.getMedicalCategoryId()).collect(Collectors.toList()))
                .orElseGet(ArrayList::new);

        List<Integer> newThirdCategoryIdList = Optional.ofNullable(newThirdCategoryList)
                .map(list -> list.stream().map(e->e.getMedicalCategoryId()).collect(Collectors.toList()))
                .orElseGet(ArrayList::new);

        if(newThirdCategoryIdList.contains(firstEngage.getNewStandardCategoryId())){
            return true;
        }

        if (oldThirdCategoryIdList.contains(firstEngage.getOldStandardCategoryId())) {
            return true;
        }

        return false;
    }

    /**
     * 供应商名称和产品注册证名称是否匹配
     * @param skuNo
     * @return
     */
    protected boolean isRegisterNameMatchTraderName(String skuNo) {

        //构建供应商信息
        Trader trader = ThreadLocalContext.get(QualifyAutoAudtioUtil.getTraderInfoKey());

        try {
            logger.info("skuNo->>:" +skuNo+",traderInfo->>" +JsonUtils.translateToJson(trader));
        } catch (IOException e) {
            //
        }

        RegistrationNumber registrationNumber = ThreadLocalContext.get(QualifyAutoAudtioUtil.getRegisterCertificateInfoKey(skuNo));


        try {
            logger.info("registrationNumberInfo->>" + JsonUtils.translateToJson(registrationNumber));
        } catch (IOException e) {
           //
        }

        return trader.getTraderName().equals(registrationNumber.getProductCompany().getProductCompanyChineseName());
    }

    /**
     * 一级分类是否上传
     * @param traderId
     * @return
     */
    protected boolean isFirstCategoryUpload(Integer traderId) {

        List<TraderCertificateVo> firstCategoryCertificateList = ThreadLocalContext.get(QualifyAutoAudtioUtil.getFirstCategoryListKey(traderId));

        return CollectionUtils.isNotEmpty(firstCategoryCertificateList);


    }

    protected boolean getProductPermissionUpload(Integer traderId) {

        List<TraderCertificateVo> productPermissionList = ThreadLocalContext.get(QualifyAutoAudtioUtil.getProductPermissionListKey(traderId));

        return CollectionUtils.isNotEmpty(productPermissionList);
    }

    protected boolean getProductPermissionInValidityPeriod(Integer traderId) {

        List<TraderCertificateVo> productPermissionList = ThreadLocalContext.get(QualifyAutoAudtioUtil.getProductPermissionListKey(traderId));

        boolean isValid = productPermissionList.stream().anyMatch(business -> business.getEndtime() == null || business.getEndtime() == 0
                || business.getEndtime() >= System.currentTimeMillis());

        return isValid;
    }

    protected boolean getProductRegistrationUpload(Integer traderId) {

        List<TraderCertificateVo> productRegistrationList = ThreadLocalContext.get(QualifyAutoAudtioUtil.getProductRegistrationListKey(traderId));

        return CollectionUtils.isNotEmpty(productRegistrationList);

    }

    protected boolean getProductRegistrationInValidityPeriod(Integer traderId) {

        List<TraderCertificateVo> productRegistrationList = ThreadLocalContext.get(QualifyAutoAudtioUtil.getProductRegistrationListKey(traderId));

        boolean isValid = productRegistrationList.stream().anyMatch(business -> business.getEndtime() == null || business.getEndtime() == 0
                ||business.getEndtime() >= System.currentTimeMillis());

        return isValid;

    }

    /**
     * 默认的isMatch方法，可被子类重写
     * @param traderId 交易员ID
     * @param skuNo 商品编号
     * @return 是否匹配
     */
    @Override
    public boolean isMatch(Integer traderId, String skuNo) {
        // 默认实现，子类可以重写此方法
        CoreSpuGenerate coreSpuGenerate = getSpuInfo(skuNo);
        // 这里的匹配逻辑应该与原来的isMatch逻辑相似
        // 但由于没有BuyorderGoodsVo，所以基于traderId和skuNo重新实现
        // 默认返回false，需要子类覆盖实现具体的匹配逻辑
        return false;
    }

    /**
     * 默认的validator方法，可被子类重写
     * @param traderId 交易员ID
     * @param skuNo 商品编号
     * @return 验证通过信息
     * @throws QualifyAutoAudtioException 验证失败时抛出此异常
     */
    @Override
    public String validator(Integer traderId, String skuNo) throws QualifyAutoAudtioException {
        // 默认实现，子类可以重写此方法
        // 具体的验证逻辑应该与原来的validator逻辑相似
        // 但由于没有BuyorderGoodsVo，所以基于traderId和skuNo重新实现
        throw new QualifyAutoAudtioException("未实现基于traderId和skuNo的验证逻辑");
    }
}
