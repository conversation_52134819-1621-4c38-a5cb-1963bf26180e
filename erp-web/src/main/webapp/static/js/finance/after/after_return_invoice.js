$(function(){
	if($("#currentMonthInvoice").val()!=1){
		var returnNum = "",returnId = "",amount = 0, indx = "";
		$("#afterGoodsListId").find("input[type='hidden'][name='hideReturnNum']").each(function(i){//循环售后退货(票)数量
			returnNum = $(this).val();returnId = $(this).attr("id");
			var indx = $(this).attr("id");
			var price = Number($("#price_"+indx).val());
			amount = (Number(amount) + Number(returnNum*price)).toFixed(2);
		})
		//$("#afterReturnInvoiceAmount").html(amount);
	}else{
		var returnNum = "",returnId = "",amount = 0, indx = "";
		$("#afterGoodsListId").find("input[name='hideReturnInvoiceAmount']").each(function(i){//循环售后退货(票)数量
			amount = (Number(amount) + Number($(this).val())).toFixed(2);
		})
		//$("#afterReturnInvoiceAmount").html(amount);
		
	}
	changeRetuenInvoice();
})

function chooseBuyorderAfterReturnFullElectronicInvoice() {
	var checkboxValue = $("input[type='checkbox'][name='fullElectronicInvoice'].mt5").prop("checked");
	if (checkboxValue) {
		$("#invoiceTypeText").text("红字有效");
		$("#invoiceTypeText").css("color", "red");
		$("#colorType").val(1);
		$("#isEnable").val(1);
		$("#invoiceProperty").val(3);
		$("#in_invoiceCode").val("0000000000");
		$("#in_invoiceCode").attr("readonly","readonly");
		$("#in_invoiceCode").css("background-color","#c5c2c2");

		$("#in_invoiceNo").val("");
		$("#in_invoiceNo").removeAttr("readonly");
		$("#in_invoiceNo").css("background-color","#FFFFFF");
	} else {
		// 取消选中时，恢复原来的逻辑
		$("#in_invoiceCode").val("");
		$("#in_invoiceCode").removeAttr("readonly");
		$("#in_invoiceCode").css("background-color","#FFFFFF");
		var currentMonthInvoice = $("#currentMonthInvoice").val();
		if (currentMonthInvoice == 1) {
			$("#invoiceTypeText").text("蓝字作废");
			$("#invoiceTypeText").css("color", "blue");
			$("#colorType").val(2);
			$("#isEnable").val(0);
			$("#invoiceProperty").val(1);

			$("#in_invoiceCode").val($('#originInvoiceCode').val());
			$("#in_invoiceNo").val($('#originInvoiceNo').val());
			$("#in_invoiceNo").attr("readonly","readonly");
			$("#in_invoiceNo").css("background-color","#c5c2c2");
		}
		if (currentMonthInvoice == 0) {
			$("#in_invoiceCode").val("");
			$("#in_invoiceCode").removeAttr("readonly");
			$("#in_invoiceCode").css("background-color","#FFFFFF");

			$("#invoiceTypeText").text("红字有效");
			$("#invoiceTypeText").css("color", "red");
			$("#colorType").val(1);
			$("#isEnable").val(1);
			$("#invoiceProperty").val(1);
		}
	}
}

function checkReturnInvoice(obj,index){
	if(index == 0){
		//部分退票
		if($(obj).is(":checked")){//选中
			// var returnNum = "",returnId = "",amount = 0, indx = "";
			// $("#afterGoodsListId").find("input[type='hidden'][name='hideReturnNum']").each(function(i){//循环售后退货(票)数量
			// 	returnNum = $(this).val();returnId = $(this).attr("id");
			// 	$("#afterGoodsListId").find("input[type='hidden'][name='hideInvoiceNum']").each(function(j){//循环财务已开发票
			// 		if(i == j){
			// 			if(Number(returnNum) <= Number($(this).val())){ //退货(票)数量小于等于已开票数量
			// 				$("#spanInvoiceNum"+$(this).attr("id")).html(Number(returnNum).toFixed(2));
			// 			}else{
			// 				$("#spanInvoiceNum"+$(this).attr("id")).html(Number($(this).val()).toFixed(2));
			// 			}
			// 		}
			// 	})
			// 		var indx = $(this).attr("id");
			// 		var price = Number($("#price_"+indx).val());
			// 		amount = (Number(amount) + Number(returnNum*price)).toFixed(2);
			// })
		}
	}else{
		//全部退票
		if($(obj).is(":checked")){//选中
			// var hiddenAmount = $("#afterReturnInvoiceAmountHidden").val();
			// $("#afterGoodsListId").find("input[type='hidden'][name='hideInvoiceNum']").each(function(j){//循环财务已开发票
			// 	$("#spanInvoiceNum"+$(this).attr("id")).html(Number($(this).val()).toFixed(2));
			// })
		}
	}
	changeRetuenInvoice();
}

function vailInvoiceCode(obj){
	clear2ErroeMes();
	var invoiceCode = $(obj).val().trim();
	if(invoiceCode.length == 0){
		warn2Tips("invoiceCode","发票代码不允许为空");
		return false;
	} else {
		var reg = /^[0-9]*$/;
		if (!reg.test(invoiceCode)) {
			warn2Tips("invoiceCode","请输入数字");
			return false;
		}
	}
}
function vailInvoiceNo(obj){
	clear2ErroeMes();
	var invoiceNo = $(obj).val().trim();
	if(invoiceNo.length == 0){
		warn2Tips("in_invoiceNo","发票号不允许为空");
		return false;
	}else{
		var checkboxValue = $("input[type='checkbox'][name='fullElectronicInvoice'].mt5").prop("checked");
		if (checkboxValue) {
			// 全电发票no为20位
			var reg = /^\d{20}$/;
			if(!reg.test(invoiceNo)){
				warn2Tips("in_invoiceNo","请输入正确的20位数字发票号");
				return false;
			}
		} else {
			// 原有逻辑为8
			var reg = /^\d{8}$/;
			if(!reg.test(invoiceNo)){
				warn2Tips("in_invoiceNo","请输入正确的8位数字发票号");
				return false;
			}
		}
	}
}

// 重复点击 置灰
function lockClickInvoice(){
	$('#addAfterReturnInvoiceButton').attr('disabled',true)
}

function checkReturnInvoiceCaiG(){
	// 针对采购
	if($("#afterType").val() == 546){
		let returnInvoiceAmount = $("#returnInvoiceAmount").text() * 1;
		if (returnInvoiceAmount <=0) {
			return false;
		}

		let checkboxList = $("input:checked[id^=chooseGoods_]");
		for (let i = 0; i < checkboxList.length; i++) {
			let dom = checkboxList[i];
			let index = $(dom).val();
			let amount = $('#hideReturnInvoiceAmount_' + index).val() * 1;
			let invoiceNum = $("#hideReturnInvoiceNum_" + index).val() * 1;

			if(amount <=0 || invoiceNum <= 0) {
				return false;
			}
		}
		return true;
	} else {
		return true;
	}

}

function addAfterReturnInvoice(){
	checkLogin();
	var type = $('#type').val();
	var checkboxValue = $("input[type='checkbox'][name='fullElectronicInvoice'].mt5").prop("checked");
	if($("#currentMonthInvoice").val() == "0" || checkboxValue){//非当月发票
		clear2ErroeMes();
		var invoiceCode = $("#addAfterCapitalBillForm #in_invoiceCode").val().trim();
		if(invoiceCode.length == 0){
			warn2Tips("invoiceCode","发票代码不允许为空");
			return false;
		} else {
			var reg = /^[0-9]*$/;
			if (!reg.test(invoiceCode)) {
				warn2Tips("invoiceCode","请输入数字");
				return false;
			}
		}
		var invoiceNo = $("#addAfterCapitalBillForm #in_invoiceNo").val().trim();
		if(invoiceNo.length == 0){
			warn2Tips("in_invoiceNo","发票号不允许为空");
			return false;
		}else{
			var checkboxValue = $("input[type='checkbox'][name='fullElectronicInvoice'].mt5").prop("checked");
			if (checkboxValue) {
				// 全电发票no为20位
				var reg = /^\d{20}$/;
				if(!reg.test(invoiceNo)){
					warn2Tips("in_invoiceNo","请输入正确的20位数字发票号");
					return false;
				}
			} else {
				// 原有逻辑为8
				var reg = /^\d{8}$/;
				if(!reg.test(invoiceNo)){
					warn2Tips("in_invoiceNo","请输入正确的8位数字发票号");
					return false;
				}
			}
		}
		$("#invoiceNo").val(invoiceNo);
		$("#invoiceCode").val(invoiceCode);
		$("#returnInvoiceForm").find("#dynamicParameter").html("");//清空参数，防止提交失败后再次提交参数重复
		var invoiceNumArr = [];
		var invoiceAmountArr = [];
		var invoicePriceArr = [];
		var detailGoodsIdArr = [];
		//此次退票数量(红字发票退票数量改成退货数量) 2018-9-27
		var returnInvoiceAll = $("input[type='radio'][name='returnInvoiceCheck']:checked").val();
		if(returnInvoiceAll ==1){
			//全部商品退票
			$("#afterGoodsListId").find("input[name='hideReturnInvoiceNum']").each(function(i){
					invoiceNumArr.push($(this).val().trim());
			});
			$("#afterGoodsListId").find("input[name='hideReturnInvoiceAmount']").each(function(i){
					invoiceAmountArr.push($(this).val().trim());
			});
			//退票产品单价
			$("#addAfterCapitalBillForm").find("input[name='orderPrice']").each(function(n){
					invoicePriceArr.push($(this).val().trim());
			});
			//售后单-销售单产品详细表ID
			$("#addAfterCapitalBillForm").find("input[name='detailGoodsId']").each(function(j){
					detailGoodsIdArr.push($(this).val().trim());
			});

		}else{
			//仅退货部分商品退票--提交选中的商品退票
			var chooseGoods = [];
			var test = $("input[name='chooseGoods']:checked");
			$("input[name='chooseGoods']:checked").each(function (){
				chooseGoods.push($(this).val());
			})
			var invoicePriceArr = [];
			var detailGoodsIdArr = [];
			for(var index = 0; index < chooseGoods.length; index++){
				invoiceNumArr.push($('#hideReturnInvoiceNum_' + chooseGoods[index]).val().trim());
				invoicePriceArr.push($('#price_' + chooseGoods[index]).val().trim());
				detailGoodsIdArr.push($('#detailGoodsId_' + chooseGoods[index]).val().trim());
				invoiceAmountArr.push($('#hideReturnInvoiceAmount_' + chooseGoods[index]).val().trim());
			}

			// $("#afterGoodsListId").find("input[name='hideReturnInvoiceNum']").each(function(i){
			// 	if($("#afterGoodsListId").find("input[name='hideReturnInvoiceNum'][id='"+i+"']").val() != 0){
			// 		invoiceNumArr.push($(this).val().trim());
			// 	}
			// });
			//退票产品单价
			// $("#addAfterCapitalBillForm").find("input[name='orderPrice']").each(function(n){
			// 	if($("#afterGoodsListId").find("input[name='orderPrice'][id='"+n+"']").val() != 0){
			// 		invoicePriceArr.push($(this).val().trim());
			// 	}
			// });
			//售后单-销售单产品详细表ID
			// $("#addAfterCapitalBillForm").find("input[name='detailGoodsId']").each(function(j){
			// 	if($("#afterGoodsListId").find("input[name='detailGoodsId'][id='"+j+"']").val() != 0){
			// 		detailGoodsIdArr.push($(this).val().trim());
			// 	}
			// });
			//采购部分退票的微调
			// if (type == 503){
            //     $("#afterGoodsListId").find("input[name='hideReturnInvoiceAmount']").each(function(i){
            //         invoiceAmountArr.push($(this).val().trim());
            //     });
            // }
		}
		
		if(invoiceNumArr.length != invoicePriceArr.length || invoicePriceArr.length != detailGoodsIdArr.length){
			
			layer.alert("参数获取错误，请刷新当前页重试或联系管理员！", {
				icon : 2
			}, function(lay) {
				layer.close(lay);
			});
		}
		
		$("#returnInvoiceForm").find("#dynamicParameter").html(
				
				"<input name='invoiceNumArr' type='hidden' value='"+invoiceNumArr+"'/>" +
						"<input name='invoicePriceArr' type='hidden' value='"+invoicePriceArr+"'/>" +
						((returnInvoiceAll == 1 && type == 505) || type == 503 ? ("<input name='invoiceAmountArr' type='hidden' value='"+invoiceAmountArr+"'/>"):"")+
								"<input name='detailGoodsIdArr' type='hidden' value='"+detailGoodsIdArr+"'/>");
	}

	if (!checkReturnInvoiceCaiG()) {
		layer.alert("请勾选商品并正确填写退票数和退票金额！");
		return ;
	}

	if (checkReturnInvoiceAmount() == false){
	    return;
    }

	lockClickInvoice();

	// $("#returnInvoiceForm").submit();
	if($("#afterType").val() == 548){
		$.ajax({
			async: false,
			url: '/after/newBuyorder/saveTpReturnInvoice.do',
			data: $("#returnInvoiceForm").serialize(),
			type: "POST",
			dataType: "json",
			success: function (data) {
				layer.alert(data.message);
			}, error: function (data) {
				if (data.status == 1001) {
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}else {
		$.ajax({
			async: false,
			url: './saveAfterReturnInvoice.do',
			data: $("#returnInvoiceForm").serialize(),
			type: "POST",
			dataType: "json",
			success: function (data) {
				// layer.confirm(data.message,{
				// 	btn: ['确定']
				// }, function () {
				// 	refreshNowPageList(data);
				// });
				layer.alert(data.message);
			}, error: function (data) {
				if (data.status == 1001) {
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}
}

function changeRetuenInvoice() {
	var returnInvoiceCheck = $("input[name='returnInvoiceCheck']:checked").val();
	var returnInvoiceAmounts = $("[id^='hideReturnInvoiceAmount_']");
	var returnInvoiceNums = $("[id^='hideReturnInvoiceNum_']");
	if(returnInvoiceAmounts.length != returnInvoiceNums.length){
		layer.alert("商品退票信息有误，请核验");
	}else {
		if (returnInvoiceCheck == 1) {
			//全部商品退票，不可修改
			for (var index = 0; index < returnInvoiceAmounts.length; index++) {
				var canRecordAmount = parseFloat(returnInvoiceAmounts[index].getAttribute('canRecordAmount'));
				var canRecordNum = parseFloat(returnInvoiceAmounts[index].getAttribute('canRecordNum'));
				$('#' + returnInvoiceAmounts[index].id).attr('disabled', true);
				$('#' + returnInvoiceNums[index].id).attr('disabled', true);
				$('#chooseGoods_' + index).attr('disabled',true);
				$('#chooseGoods_' + index).prop('checked',true);
				var returnAmount = canRecordAmount;
				// console.log('全部退票商品退票金额:' + returnAmount)
				$('#' + returnInvoiceAmounts[index].id).val(returnAmount);
				$('#' + returnInvoiceNums[index].id).val(canRecordNum);
				$('#hideReturnInvoiceAmount_' + index).attr('valueFlag', returnAmount);
			}
		}else {
			//仅退货商品部分退票
			for(var index = 0; index < returnInvoiceAmounts.length; index++){
				$('#' + returnInvoiceAmounts[index].id).attr('disabled', false);
				$('#' + returnInvoiceNums[index].id).attr('disabled', false);
				$('#chooseGoods_' + index).attr('disabled',false);

				if($("#afterType").val() != 546){
					$('#chooseGoods_' + index).prop('checked',false);
				} else {
					calReturnInvoiceAmount();
				}
			}
			//采购仅退票特殊考虑，不展示checkbox，所以默认选中
			if($("#afterType").val() == 548){
				for(var index = 0; index < returnInvoiceAmounts.length; index++){
					$('#chooseGoods_' + index).prop('checked',true);
				}
			}
		}
	}
}

/**
 * 修改退票金额
 */
function checkReturnInvoiceAmount() {
    //全部退票跳过校验
    var returnInvoiceCheck = $("input[name='returnInvoiceCheck']:checked").val();
    if (returnInvoiceCheck == 1){
        return true;
    }
	var  totalAmount = 0.00;
	var returnInvoiceAmounts = $("[id^='hideReturnInvoiceAmount_']");
	var returnInvoiceNums = $("[id^='hideReturnInvoiceNum_']");
	var errorNumMsg = $('#afterType').val() == 546 ? "退票数量超过可退票上限，请确认相关业务单据数" : "退票数量不得超过该发票该sku的剩余收票数量";
	reg = /^-?\d+\.?\d{0,2}$/


	for (var index = 0; index < returnInvoiceAmounts.length; index++) {
		// var valueFlag = parseFloat(returnInvoiceAmounts[index].getAttribute('valueFlag'));
		var selfValue = parseFloat(returnInvoiceAmounts[index].value);
		var selfNum = parseFloat(returnInvoiceNums[index].value);
		// debugger;
		// if (valueFlag + 1 < selfValue || valueFlag - 1 > selfValue){
		// 	layer.alert('退票金额不得超过“退票数量*单价”的正负1元范围', function () {
		// 		layer.closeAll();
		// 		return false;
		// 	});
        //     return false;
		// }
		// console.log('两位小数校验:' + reg.test(selfValue));
		var canRecordAmount = parseFloat(returnInvoiceAmounts[index].getAttribute('canRecordAmount'));
		var canRecordNum = parseFloat(returnInvoiceAmounts[index].getAttribute('canRecordNum'));
		console.log('canRecordAmount' + canRecordAmount);
		console.log('canRecordNum' + canRecordNum);
		if(selfNum > canRecordNum){
			layer.alert(errorNumMsg, function () {
				layer.closeAll();
				return false;
			});
			return false;
		}

		if (selfValue > canRecordAmount) {
			layer.alert('退票金额不得超过该发票该sku的剩余收票金额', function () {
				layer.closeAll();
				return false;
			});
            return false;
		}

		if (! reg.test(selfValue)){
			layer.alert('只能填写2位小数', function () {
				layer.closeAll();
				return false;
			});
            return false;
		}
		totalAmount += selfValue;
	}

	if (totalAmount == 0.00){
		layer.alert('退票金额合计不得为0', function () {
			layer.closeAll();
			return false;
		})
		return true;
	}
	return true;
}


/**
 * 控制只能输入小数点后2位
 * @param obj
 */
function clearNoNum(obj) {
	obj.value = obj.value.replace(/[^\d.]/g, "");
	obj.value = obj.value.replace(/\.{2,}/g, ".");
	obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
	obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
	if (obj.value.indexOf(".") < 0 && obj.value != "") {
		 obj.value = parseFloat(obj.value);
	}
}

/**
 * 退票金额或数量修改，调整单价
 */
function invoiceChange(price,index){
	//退票总金额
	var amount = $('#hideReturnInvoiceAmount_' + index).val();
	var num = $('#hideReturnInvoiceNum_' + index).val();
	if(num != 0) {
		var price = (amount / num).toFixed(2);
		$("#price_" + index).val(price);
		$("#showPrice_" + index).html(price);
	}else {
		layer.alert("退票数量为0有误");
		return;
	}
	calReturnInvoiceAmount();
}

function checkboxChange(){
	calReturnInvoiceAmount();
}

// 计算退票金额
function calReturnInvoiceAmount(){
	if($("#afterType").val() == 546) {
		let checkboxList = $("input:checked[id^=chooseGoods_]");
		console.log(checkboxList);
		let totalReturnInvoiceAmount = 0;
		checkboxList.each(function (i,dom){
			let index = $(dom).val();
			let amount = $('#hideReturnInvoiceAmount_' + index).val() * 1;
			totalReturnInvoiceAmount += amount;
		})
		if (0 === totalReturnInvoiceAmount) {
			totalReturnInvoiceAmount = "0.00";
		}
		$("#returnInvoiceAmount").text(totalReturnInvoiceAmount);
	}
}

