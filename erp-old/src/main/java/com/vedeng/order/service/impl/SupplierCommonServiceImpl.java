package com.vedeng.order.service.impl;

import com.vedeng.order.service.SupplierCommonService;
import com.vedeng.trader.dao.TraderCertificateMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.dao.TraderSupplierMapper;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.model.vo.TraderCertificateVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("supplierCommonService")
public class SupplierCommonServiceImpl implements SupplierCommonService {


    @Resource
    protected TraderMapper traderMapper;

    @Resource
    protected TraderSupplierMapper traderSupplierMapper;

    @Resource
    protected TraderCertificateMapper traderCertificateMapper;


    public Trader getTrader(Integer traderId) {
        return traderMapper.getTraderByTraderId(traderId);
    }

    public TraderSupplier getTraderSupplier(Integer traderId) {
        return traderSupplierMapper.getSuplierInfoByTraderId(traderId);
    }

    public List<TraderCertificateVo> getTraderCertificateVos(TraderCertificateVo traderCertificate) {
        return traderCertificateMapper.getListTraderCertificateVo(traderCertificate);
    }

}
