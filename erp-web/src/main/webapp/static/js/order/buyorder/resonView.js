$(function() {
    $("#determine").click(function(){
        checkLogin();
        var reson =$("#reson").val();
        if(reson != '') {
            $.ajax({
                async:false,
                url:'./changeComponent.do',
                data:{
                    "saleorderGoodsIds":$("#saleorderGoodsIds").val(),
                    "componentId":$("#componentId").val(),
                    "reson":reson
                },
                type:"POST",
                dataType : "json",
                success:function(data){
                    if(data.code==0){
                        layer.alert(data.message,
                            { icon: 1 },
                            function () {
                                parent.location.reload();
                            }
                        );
                    }else{
                        layer.alert(data.message);
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            })
        }else {
            layer.alert("请输入原因");
        }
    });
    $("#cancel").click(function(){
        parent.location.reload();
    });
});


