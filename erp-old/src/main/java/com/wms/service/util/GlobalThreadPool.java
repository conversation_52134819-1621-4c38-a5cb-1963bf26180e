package com.wms.service.util;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 定义全局线程池对象
 */
public class GlobalThreadPool {

    private static ExecutorService messageLinstensExecute = new ThreadPoolExecutor(4, 8,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(1024),
            new ThreadFactoryBuilder().setNameFormat("erpMessage-Linsten").build(),
            new ThreadPoolExecutor.AbortPolicy());


    public static void submitMessage(Runnable message){
        messageLinstensExecute.submit(message);
    }

}
