package com.vedeng.order.model.pi;

import java.math.BigDecimal;

public class SaleorderLog {
	
    private Integer saleorderLogId;

    //机型
    private String model;

    //销售客户ID
    private Integer traderCustomerId;

    //销售客户名称
    private String traderName;

    //序列号
    private String barcodeFactory;

    //发货日期
    private Long deliveryTime;

    //批发价格
    private BigDecimal price;

    //销售区域
    private String saleArea;

    private Long addTime;
    
    private Integer goodsType;
    
    //物料编码
    private String materialCode;

    //返回信息类型，S为成功，E为报错
    private String status;

    //返回信息内容
    private String message;

    private Integer currentCount;//当前第几次查询
    
    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }

    public String getMaterialCode() {
		return materialCode;
	}

	public void setMaterialCode(String materialCode) {
		this.materialCode = materialCode;
	}

	public Integer getCurrentCount() {
		return currentCount;
	}

	public void setCurrentCount(Integer currentCount) {
		this.currentCount = currentCount;
	}

	public Integer getSaleorderLogId() {
        return saleorderLogId;
    }

    public void setSaleorderLogId(Integer saleorderLogId) {
        this.saleorderLogId = saleorderLogId;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public Integer getTraderCustomerId() {
        return traderCustomerId;
    }

    public void setTraderCustomerId(Integer traderCustomerId) {
        this.traderCustomerId = traderCustomerId;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName == null ? null : traderName.trim();
    }

    public String getBarcodeFactory() {
        return barcodeFactory;
    }

    public void setBarcodeFactory(String barcodeFactory) {
        this.barcodeFactory = barcodeFactory == null ? null : barcodeFactory.trim();
    }

    public Long getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Long deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getSaleArea() {
        return saleArea;
    }

    public void setSaleArea(String saleArea) {
        this.saleArea = saleArea == null ? null : saleArea.trim();
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message == null ? null : message.trim();
    }
}
