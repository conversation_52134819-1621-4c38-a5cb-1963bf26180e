package com.newtask.finance;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.billsync.task.model.entity.BankBillExtDo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.finance.enums.BankTagEnum;
import com.vedeng.erp.kingdee.service.KingDeePayBillApiService;
import com.vedeng.finance.service.BankBillService;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 从金蝶获取部分银行回单附件转存到oss
 *
 * <AUTHOR>
 */
@JobHandler("GetBankFileFromKingDeeTask")
@Component
@Slf4j
public class GetBankFileFromKingDeeTask extends AbstractJobHandler {

    @Autowired
    private BankBillService bankBillService;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Autowired
    private KingDeePayBillApiService kingDeePayBillApiService;

    /**
     *  中行，民生，交通
     */
    private static final List<Integer> BANK_TAG_LIST = Arrays.asList(BankTagEnum.BANK_3.getCode(),BankTagEnum.BANK_7.getCode(),BankTagEnum.BANK_6.getCode());

    /**
     * 借贷标志(0-借 转出 ,1-贷 转入)
     */
    private static final Integer PAY = ErpConstant.ZERO;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        // 组装入参
        JSONObject jsonObject = JSON.parseObject(param);
        String beginDate = jsonObject.getString("beginDate");
        String endDate = jsonObject.getString("endDate");
        log.info("执行开始");
        Integer minId = 0;
        for (;;){
            // 查询需要下载的银行流水
            List<BankBillExtDo> bankBillExtDos = bankBillService.queryBankBillOfNoReceiptUrl(beginDate, endDate, BANK_TAG_LIST, minId);
            if (CollectionUtils.isEmpty(bankBillExtDos)){
                log.info("Task获取银行回单附件结束");
                break;
            }
            minId = bankBillExtDos.get(bankBillExtDos.size()-1).getBankBillId();
            log.info("Task获取银行回单附件,总条数：{}",bankBillExtDos.size());
            int i = 0;
            for (BankBillExtDo bankBillExtDo : bankBillExtDos) {
                i++;
                log.info("当前进度：{}/{}",i,bankBillExtDos.size());
                String tranFlowOrg = bankBillExtDo.getTranFlow();
                if (StrUtil.isBlank(tranFlowOrg)){
                    log.info("银行流水号为空，跳过");
                    continue;
                }
                String newTranFlowOrg = tranFlowOrg.contains("_") ? tranFlowOrg.split("_")[0] : tranFlowOrg;
                BigDecimal amt = bankBillExtDo.getAmt();
                String tranFlow = newTranFlowOrg+"_"+amt.toPlainString();
                Integer bankBillId = bankBillExtDo.getBankBillId();
                log.info("开始获取银行回单附件,流水号:{},bankBillId:{}",tranFlow,bankBillId);
                String base64File = kingDeePayBillApiService.queryKingDeeReturnUrl(tranFlow);
                if (StringUtil.isEmpty(base64File)){
                    log.info("未获取到银行回单附件，流水号:{},bankBillId:{}",tranFlow,bankBillId);
                    continue;
                }
                // 调用金蝶获取附件
                String fileName = tranFlow;
                String suffix = "pdf";
                // 上传oss
                String url = downloadFile(base64File, fileName, suffix);
                // 更新附件地址
                if (StringUtil.isEmpty(url)){
                    log.info("未上传到oss,流水号:{}，bankBillId：{}",tranFlow,bankBillId);
                    continue;
                }
                bankBillService.updataBankBillReceiptUrl(bankBillId,url);
                log.info("结束获取到银行回单附件，流水号:{},bankBillId:{},url:{}",tranFlow,bankBillId,url);
            }
        }
        return ReturnT.SUCCESS;
    }

    public String downloadFile(String base64File, String fileName, String suffix) {
        InputStream inputStream = null;
        try {
            // 下载文件base64
            byte[] bytes = Base64.decode(base64File);
            inputStream = new ByteArrayInputStream(bytes);
            // 上传SSO
            return ossUtilsService.upload2OssForInputStream(suffix, fileName, inputStream);
        } catch (Exception e) {
            log.error("金蝶回单下载发票异常");
            return null;
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error("税金下载发票文件流关闭失败");
                return null;
            }
        }
    }
}
