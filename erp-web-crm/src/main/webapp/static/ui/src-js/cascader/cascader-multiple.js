
Vue.component('ui-cascader-multiple', {
    template: `
        <div class="menu-wrap">
            <ul 
                v-if="list.length"
                class="vd-ui-cascader-node multiple"
                @click.stop
                :style="styles"
                @mousedown.prevent
            >
                <li
                    v-for="(item, index) in list" 
                    :key="index"
                    @click="openL1(item)"
                    :class="{'selected': tempChoose[0] && tempChoose[0].value == item.value }"
                    :datatt="JSON.stringify(item)"
                >
                    <div class="ui-cascader-checkbox-wrap"
                        :class="{'active': item.checked || item.indeterminate}"
                        @click.stop="clickCheckbox(item, 1)"
                    >
                        <i class="vd-ui_icon icon-selected2" v-if="item.checked"></i>
                        <i class="vd-ui_icon icon-deduct" v-else-if="item.indeterminate"></i>
                    </div>
                    
                    <p>{{item.label}}</p>
                    <i class="vd-ui_icon icon-app-right icon-right" v-if="item.children && item.children.length"></i>
                </li>
            </ul>

            <ul 
                v-if="level2List.length"
                class="vd-ui-cascader-node multiple"
                @click.stop
                :style="styles"
                @mousedown.prevent
            >
                <li
                    v-for="(item, index) in level2List" 
                    :key="index"
                    @click="openL2(item)"
                    :class="{'selected': tempChoose[1] && tempChoose[1].value == item.value }"
                >
                    <div class="ui-cascader-checkbox-wrap"
                        :class="{'active': item.checked || item.indeterminate}"
                        @click.stop="clickCheckbox(item, 2)"

                    >
                        <i class="vd-ui_icon icon-selected2" v-if="item.checked"></i>
                        <i class="vd-ui_icon icon-deduct" v-else-if="item.indeterminate"></i>
                    </div>
                    <p>{{item.label}}</p>
                    <i class="vd-ui_icon icon-app-right icon-right" v-if="item.children && item.children.length"></i>
                </li>
            </ul>

            <ul 
                v-if="level3List.length"
                class="vd-ui-cascader-node multiple"
                @click.stop
                :style="styles"
                @mousedown.prevent
            >
                <li
                    v-for="(item, index) in level3List" 
                    :key="index"
                    @click="openL3(item)"
                    :class="{'selected': tempChoose[2] && tempChoose[2].value == item.value }"
                >
                    <div class="ui-cascader-checkbox-wrap"
                        :class="{'active': item.checked}"
                        @click.stop="clickCheckbox(item, 3)"
                    >
                        <i class="vd-ui_icon icon-selected2" v-if="item.checked"></i>
                    </div>
                    <p>{{item.label}}</p>
                </li>
            </ul>
        </div>
    `,

    props: {
        data: {
            type:Array,
            default:()=> {
                return []
            }
        },
        selectObj: {
            type: Array
        },
        styles: {
            type: Object,
            default: ()=> {
                return {}
            }
        },
        multiple: {
            type: Boolean,
            default: false
        },
        multipleLabel: {
            type: Array
        }
    },
    data () {
        return {
            list: [], // 全部数据
            level2List: [],
            level3List: [],
            tempChoose: [], // 选中项记录
        }
    },
    computed: {
    },
    watch: {
        data: {
            handler () {
                console.log('组件内 data', this.data);
            },
            deep: true,
        }
    },
    mounted() {
        this.list = this.data;
    },
    methods: {
        openL1 (val) {
            this.tempChoose = [val];
            this.level2List = val.children || [];
            this.level3List = [];
            this.$emit('open', 1)
        },
        openL2 (val) {
            this.tempChoose = [this.tempChoose[0], val];
            this.level3List = val.children || [];
            this.$emit('open', 2)
        },
        openL3 (val) {
            this.tempChoose = [this.tempChoose[0], this.tempChoose[1], val];
            this.$emit('open', 3)
        },


        clickCheckbox (item, ind) {
            ind == 1 && this.openL1(item);
            ind == 2 && this.openL2(item);
            ind == 3 && this.openL3(item);
            this.changeCheckbox(item, ind);
        },
        // 修改复选框的值
        changeCheckbox (item, ind) {
            let childs = item.children || [];
            if (item.indeterminate) {
                item.indeterminate = false;
                item.checked = true;
                childs.length && this.changeChildCheckbox(childs, true); // 子集全部选中
            } else if (item.checked) {
                item.indeterminate = false;
                item.checked = false;
                childs.length && this.changeChildCheckbox(childs, false); // 子集全部取消选中
            } else {
                item.indeterminate = false;
                item.checked = true;
                childs.length && this.changeChildCheckbox(childs, true); // 子集全部选中
            }

            this.$emit('pick', this.list);
        },

        // 子集: 全选/全不选
        changeChildCheckbox (data, bool) {
            data.forEach(item=> {
                this.$set(item, 'checked', bool);
                this.$set(item, 'indeterminate', false);

                if (item.children && item.children.length) {
                    this.changeChildCheckbox(item.children, bool);
                }
            })
        },
    }
})