package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeInventoryProfitDto;
import com.vedeng.erp.kingdee.service.KingDeeInventoryProfitApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 盘盈入库单
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchInventoryProfitWriter extends BaseWriter<KingDeeInventoryProfitDto> {


    @Autowired
    private KingDeeInventoryProfitApiService kingDeeInventoryProfitApiService;
    @Override
    public void doWrite(KingDeeInventoryProfitDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("BatchInventoryProfitWriter#doWrite,入参：{}", JSON.toJSONString(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.saveInventoryProfit);
        kingDeeInventoryProfitApiService.register(dto,true);
    }

}
