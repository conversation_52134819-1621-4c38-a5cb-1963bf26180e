package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.flash.dao.RegularSnapshotMapper;
import com.vedeng.flash.model.RegularSnapshot;
import com.vedeng.flash.service.prepare.PrepareStockService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Description:  同步定品池快照数据
 * @Author:       davis
 * @Date:         2021/5/25 上午10:12
 * @Version:      1.0
 */
@Slf4j
@Component
@JobHandler(value = "syncRegularSkuSnapshotTask")
public class SyncRegularSkuSnapshotTask extends AbstractJobHandler {

    @Autowired
    private PrepareStockService prepareStockService;

    @Autowired
    private RegularSnapshotMapper regularSnapshotMapper;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        log.info("同步定品池快照数据开始,时间:{}",param);
        XxlJobLogger.log("同步定品池快照数据开始,时间:{}",param);

        List<RegularSnapshot> regularSnapshotList = prepareStockService.syncRegularSkuSnapshot();

        if (CollectionUtils.isEmpty(regularSnapshotList)) {
            log.info("没有需要同步的数据");
            XxlJobLogger.log("没有需要同步的数据");
            return SUCCESS;
        }

        regularSnapshotMapper.insertBatch(regularSnapshotList);

        log.info("同步定品池快照数据结束,时间:{}",param);
        XxlJobLogger.log("同步定品池快照数据结束,时间:{}",param);
        return SUCCESS;
    }
}



