<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="warehouseList" language="groovy" printOrder="Horizontal" pageWidth="750" pageHeight="615" orientation="Landscape" columnWidth="750" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isTitleNewPage="true" whenResourceMissingType="Empty" isIgnorePagination="true" uuid="a0d85773-bf30-4acd-a5bf-8309f6787e74">
	<property name="ireport.zoom" value="1.331000000000025"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<style name="style_detail_left" hAlign="Left" vAlign="Middle" isBlankWhenNull="true" fontName="宋体" fontSize="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H"/>
	<style name="style_detail_center" hAlign="Center" vAlign="Middle" fontName="SansSerif" fontSize="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H"/>
	<style name="style_title_name" hAlign="Center" vAlign="Middle" fontName="宋体" fontSize="13" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H"/>
	<style name="style_title_value" hAlign="Left" vAlign="Middle" isBlankWhenNull="true" fontName="宋体" fontSize="13" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H"/>
	<style name="style_detail_right" hAlign="Right" vAlign="Middle" fontName="宋体" fontSize="11" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H"/>
	<style name="style_title_center" backcolor="#00FF00" hAlign="Center" vAlign="Middle" fontName="SansSerif" fontSize="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-H"/>
	<field name="wareHouseName" class="java.lang.String"/>
	<field name="storageroomName" class="java.lang.String"/>
	<field name="storageAreaName" class="java.lang.String"/>
	<field name="storageRackName" class="java.lang.String"/>
	<field name="storageLocationName" class="java.lang.String"/>
	<columnHeader>
		<band height="25" splitType="Stretch">
			<frame>
				<reportElement key="frame" style="style_detail_center" mode="Opaque" x="0" y="0" width="750" height="25" backcolor="#99CCFF" uuid="9b51a743-dc57-43aa-9065-3e3010633bd8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement key="staticText" style="style_detail_center" x="0" y="0" width="150" height="25" forecolor="#000000" backcolor="#0099FF" uuid="0c067fa4-bdc9-439e-9cca-b4be4c5cf6b1"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[仓库]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText" style="style_detail_center" x="150" y="0" width="150" height="25" backcolor="#0099FF" uuid="87c27b41-80d3-43c1-8d7c-9a0db3c563d7"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[库房]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText" style="style_detail_center" x="300" y="0" width="150" height="25" backcolor="#0099FF" uuid="0ed497f0-8aba-4615-ac5a-8bc2f75806bc"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[货区]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText" style="style_detail_center" x="450" y="0" width="150" height="25" backcolor="#0099FF" uuid="e2a6bbbb-0e1f-422a-942f-99f2f232f4b1"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[货架]]></text>
				</staticText>
				<staticText>
					<reportElement key="staticText" style="style_detail_center" x="600" y="0" width="150" height="25" backcolor="#0099FF" uuid="b5f0b8ec-2b75-4506-8aca-aa727b2712ef"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement>
						<font fontName="宋体" size="12"/>
					</textElement>
					<text><![CDATA[库位]]></text>
				</staticText>
			</frame>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<frame>
				<reportElement key="frame" mode="Opaque" x="0" y="0" width="750" height="20" backcolor="#FFFFFF" uuid="91d6476c-ac79-41de-91f0-e993c1d9a334"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField" style="style_detail_left" stretchType="RelativeToTallestObject" x="0" y="0" width="150" height="20" uuid="4a7a8d90-f169-4f56-b924-5cc1fcad251a"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{wareHouseName}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField" style="style_detail_left" stretchType="RelativeToTallestObject" x="150" y="0" width="150" height="20" uuid="b84aa0d0-04fe-481a-8ae2-9e87d754a8b3"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{storageroomName}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField" style="style_detail_left" stretchType="RelativeToTallestObject" x="300" y="0" width="150" height="20" uuid="2c780098-284b-4c2f-8766-fa42be49b3fa"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{storageAreaName}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField" style="style_detail_left" stretchType="RelativeToTallestObject" x="450" y="0" width="150" height="20" uuid="9f231d21-1f83-4cb6-8e1c-43e1a26651bd"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{storageRackName}]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement key="textField" style="style_detail_left" stretchType="RelativeToTallestObject" x="600" y="0" width="150" height="20" uuid="f9854777-193a-42b7-93e6-0e421aa569c5"/>
					<box>
						<pen lineWidth="0.5"/>
						<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center">
						<font fontName="宋体" size="11"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{storageLocationName}]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</detail>
</jasperReport>
