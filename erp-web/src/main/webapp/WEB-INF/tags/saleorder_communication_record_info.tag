<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="saleorder" type="com.vedeng.order.model.Saleorder" required="true" %>
<%@attribute name="communicateList" type="java.util.List" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>


<div class="parts"  liname="沟通记录" id="沟通记录">
    <div class="title-container title-container-blue">
        <div class="table-title nobor">
            沟通记录
        </div>
        <c:if test="${saleorder.status != 3}">
            <div class="title-click nobor  pop-new-data"
                 layerParams='{"width":"850px","height":"560px","title":"新增沟通记录","link":"/order/saleorder/addComrecord.do?saleorderId=${saleorder.saleorderId}&traderId=${saleorder.traderId}&traderContactMobile=${saleorder.traderContactMobile}"}'>
                新增
            </div>
        </c:if>
    </div>
    <table class="table">
        <thead>
        <tr>
            <th class="wid9">沟通时间</th>
            <th class="wid8">录音</th>
            <th class="wid9">联系人</th>
            <th class="wid11">联系方式</th>
            <th class="wid9">沟通方式</th>
            <th class="wid9">沟通目的</th>
            <th>沟通内容（AI分析整理）</th>
            <th class="wid11">操作人</th>
            <th class="wid9">下次联系日期</th>
            <th>下次沟通内容</th>
            <th>备注</th>
            <!-- <th>创建时间</th> -->
            <th class="wid9">操作</th>
        </tr>
        </thead>
        <tbody>

        <c:forEach var="list" items="${communicateList}" varStatus="status">
            <tr>
                <td><date:date value ="${list.begintime}" format="yyyy-MM-dd"/><br>
                    <date:date value ="${list.begintime}" format="HH:mm"/>~<date:date value ="${list.endtime}" format="HH:mm"/>
                </td>
                <td><c:if test="${not empty list.coidUri }">${list.communicateRecordId}</c:if></td>
                <td>${list.contactName}</td>
                <td>${list.phone}</td>
                <td>${list.communicateModeName}</td>
                <td>${list.communicateGoalName}</td>
                <td>
                    <ul class="communicatecontent ml0">
                        <c:if test="${not empty list.tag }">
                            <c:forEach items="${list.tag }" var="tag">
                                <li class="bluetag" title="${tag.tagName}">${tag.tagName}</li>
                            </c:forEach>
                        </c:if>
                    </ul>
                    <div style="float: left">${list.contentSuffix}</div>
                </td>
                <td>${list.user.username}</td>
                <c:choose>
                    <c:when test="${list.isDone == 0 }">
                        <td class="font-red">${list.nextContactDate }</td>
                    </c:when>
                    <c:otherwise>
                        <td>${list.nextContactDate }</td>
                    </c:otherwise>
                </c:choose>
                <td>${list.nextContactContent}</td>
                <td>${list.comments}</td>
                    <%-- <td><date:date value ="${list.addTime}"/></td> --%>
                <td class="caozuo">
                    <c:if test="${not empty list.coidUri}">
								<span class="caozuo-blue"
                                      onclick="javascript:window.top.showAiHelpme(${list.communicateRecordId});">
												查看
										</span>
                    </c:if>
                    <c:if test="${saleorder.status != 3}">
                        <span class="border-blue pop-new-data" layerParams='{"width":"850px","height":"560px","title":"编辑沟通记录","link":"/order/saleorder/editCommunicate.do?communicateRecordId=${list.communicateRecordId}&traderId=${saleorder.traderId}&saleorderId=${saleorder.saleorderId}"}'>编辑</span>
                    </c:if>
                </td>
            </tr>
        </c:forEach>
        <c:if test="${empty communicateList}">
            <!-- 查询无结果弹出 -->
            <tr>
                <td colspan="12">暂无沟通记录。</td>
            </tr>
        </c:if>
        </tbody>
    </table>
    <div class="clear"></div>
</div>