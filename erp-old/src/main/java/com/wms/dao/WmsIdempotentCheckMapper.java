package com.wms.dao;

import com.wms.model.po.WmsIdempotentCheck;
import org.apache.ibatis.annotations.Param;

public interface WmsIdempotentCheckMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(WmsIdempotentCheck record);

    WmsIdempotentCheck selectByPrimaryKey(Long id);

    WmsIdempotentCheck selectByBusinessKey(@Param("businessKey") String businessKey);

    int updateByPrimaryKeySelective(WmsIdempotentCheck record);

}