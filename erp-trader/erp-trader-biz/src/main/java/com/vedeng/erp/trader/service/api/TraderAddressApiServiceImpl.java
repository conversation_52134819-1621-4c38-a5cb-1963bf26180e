package com.vedeng.erp.trader.service.api;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.system.service.RegionApiService;
import com.vedeng.erp.trader.dto.TraderAddressDto;
import com.vedeng.erp.trader.mapper.TraderAddressMapper;
import com.vedeng.erp.trader.service.TraderAddressApiService;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/10/20 19:15
 **/
@Service
@Slf4j
public class TraderAddressApiServiceImpl implements TraderAddressApiService {


    @Autowired
    private TraderAddressMapper traderAddressMapper;

    @Autowired
    private RegionApiService regionApiService;

    @Override
    public TraderAddressDto findTraderAddressById(Integer traderAddressId){
        return traderAddressMapper.findTraderAddressById(traderAddressId);
    }


    @Override
    public List<TraderAddressDto> list(TraderAddressDto query) {

        if (Objects.isNull(query.getTraderId())) {
            return Collections.emptyList();
        }
        List<TraderAddressDto> all = traderAddressMapper.findAll(query);
        all.forEach(c -> {
            if (Objects.nonNull(c.getAreaId())) {
                String area = Optional.ofNullable(regionApiService.getThisRegionToParentRegion(c.getAreaId())).orElse("");
                c.setArea(area);
            }
        });
        return all;
    }

    @Override
    public List<TraderAddressDto> getLatestTransactionAddress(Integer traderId) {
        // 查询最近交易过的2个客户联系地址
        List<Integer> latestTransactionAddress = traderAddressMapper.getLatestTransactionAddress(traderId);
        // 若交易过的地址数量小于2，则随机取满两条
        if (latestTransactionAddress.size() < 2) {
            TraderAddressDto query = new TraderAddressDto();
            query.setTraderId(traderId);
            List<TraderAddressDto> randomAddress = traderAddressMapper.findAll(query);
            latestTransactionAddress.addAll(randomAddress.stream().filter(item -> !latestTransactionAddress.contains(item.getTraderAddressId())).map(TraderAddressDto::getTraderAddressId).limit(2).collect(Collectors.toList()));
        }

        // 按照入参id的顺序展示结果集
        if (CollectionUtils.isNotEmpty(latestTransactionAddress)) {
            List<TraderAddressDto> traderAddressDtoList = traderAddressMapper.getByIdList(latestTransactionAddress);
            Map<Integer, TraderAddressDto> traderAddressDtoMap = traderAddressDtoList.stream()
                    .collect(Collectors.toMap(TraderAddressDto::getTraderAddressId, Function.identity()));
            return latestTransactionAddress.stream()
                    .map(traderAddressDtoMap::get)
                    .filter(Objects::nonNull)
                    .limit(2)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<TraderAddressDto> findByTraderIdAndTraderTypeAndAddressLike(Integer traderId,
                                                                            Integer traderType,
                                                                            String keywords) {
        List<TraderAddressDto> traderAddressDtos = traderAddressMapper.findByTraderIdAndTraderTypeAndAddressLike(traderId, traderType, keywords);
        if (CollUtil.isNotEmpty(traderAddressDtos)) {
            traderAddressDtos.forEach(t -> {
                if (Objects.nonNull(t.getAreaId())) {
                    String area = Optional.ofNullable(regionApiService.getThisRegionToParentRegionP(t.getAreaId())).orElse("");
                    t.setArea(area);
                }
            });
        }
        return traderAddressDtos;
    }
}
