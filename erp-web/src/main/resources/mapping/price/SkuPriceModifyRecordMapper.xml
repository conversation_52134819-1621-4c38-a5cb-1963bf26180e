<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.price.dao.SkuPriceModifyRecordMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.price.model.SkuPriceModifyRecord" >
     
    <id column="SKU_PRICE_MODIFY_RECORD_ID" property="skuPriceModifyRecordId" jdbcType="BIGINT" />
    <result column=" SKU_ID" property="skuId" jdbcType="BIGINT" />
    <result column="PRICE_MOD_TIME" property="priceModTime" jdbcType="BIGINT" />
    <result column="AFTER_MOD_MARKET_PRICE" property="afterModMarketPrice" jdbcType="DECIMAL" />
    <result column="BEFORE_MOD_MARKET_PRICE" property="beforeModMarketPrice" jdbcType="DECIMAL" />
    <result column="AFTER_MOD_TERMINAL_PRICE" property="afterModTerminalPrice" jdbcType="DECIMAL" />
    <result column="BEFORE_MOD_TERMINAL_PRICE" property="beforeModTerminalPrice" jdbcType="DECIMAL" />
    <result column="AFTER_MOD_DISTRIBUTION_PRICE" property="afterModDistributionPrice" jdbcType="DECIMAL" />
    <result column="BEFORE_MOD_DISTRIBUTION_PRICE" property="beforeModDistributionPrice" jdbcType="DECIMAL" />
    <result column="AFTER_MOD_PURCHASE_COSTS" property="afterModPurchaseCosts" jdbcType="DECIMAL" />
    <result column="BEFORE_MOD_PURCHASE_COSTS" property="beforeModPurchaseCosts" jdbcType="DECIMAL" />
    <result column="MOD_PRICE_TYPE" property="modPriceType" jdbcType="BIT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    SKU_PRICE_MODIFY_RECORD_ID,  SKU_ID, PRICE_MOD_TIME, AFTER_MOD_MARKET_PRICE, BEFORE_MOD_MARKET_PRICE, 
    AFTER_MOD_TERMINAL_PRICE, BEFORE_MOD_TERMINAL_PRICE, AFTER_MOD_DISTRIBUTION_PRICE, 
    BEFORE_MOD_DISTRIBUTION_PRICE, AFTER_MOD_PURCHASE_COSTS, BEFORE_MOD_PURCHASE_COSTS, 
    MOD_PRICE_TYPE, CREATOR, ADD_TIME, UPDATER, UPDATE_TIME
  </sql>
  
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from T_SKU_PRICE_MODIFY_RECORD
    where SKU_PRICE_MODIFY_RECORD_ID = #{skuPriceModifyRecordId,jdbcType=BIGINT}
  </select>
  <select id="findByListPage" resultType="com.pricecenter.dto.SkuPriceModifyRecordIndexDto">
    SELECT A.* FROM (
      SELECT
          A.SKU_PRICE_MODIFY_RECORD_ID,
          A.SKU_ID,
          B.SKU_NO,
          B.SKU_NAME,
          D.BRAND_NAME,
          E.UNIT_NAME,
          B.MODEL,
          A.MOD_PRICE_TYPE,
          FROM_UNIXTIME(A.PRICE_MOD_TIME/1000,'%Y-%m-%d %H:%i:%s') as MODIFY_TIME_STRING,
          IFNULL(A.AFTER_MOD_MARKET_PRICE,0) AS AFTER_MOD_MARKET_PRICE,
          IFNULL(A.BEFORE_MOD_MARKET_PRICE,0) AS BEFORE_MOD_MARKET_PRICE,
          IFNULL(A.AFTER_MOD_TERMINAL_PRICE,0) AS AFTER_MOD_TERMINAL_PRICE,
          IFNULL(A.BEFORE_MOD_TERMINAL_PRICE,0) AS BEFORE_MOD_TERMINAL_PRICE,
          IFNULL(A.AFTER_MOD_DISTRIBUTION_PRICE,0) AS AFTER_MOD_DISTRIBUTION_PRICE,
          IFNULL(A.BEFORE_MOD_DISTRIBUTION_PRICE,0) AS BEFORE_MOD_DISTRIBUTION_PRICE,
          IFNULL(A.AFTER_MOD_GROUP_PRICE,0) AS AFTER_MOD_GROUP_PRICE,
          IFNULL(A.BEFORE_MOD_GROUP_PRICE,0) AS BEFORE_MOD_GROUP_PRICE,
          IFNULL(A.AFTER_MOD_PURCHASE_COSTS,0) AS AFTER_MOD_PURCHASE_COSTS,
          IFNULL(A.BEFORE_MOD_PURCHASE_COSTS,0) AS BEFORE_MOD_PURCHASE_COSTS,

          IFNULL(A.AFTER_MOD_ELECTRONIC_COMMERCE_PRICE,0) AS AFTER_MOD_ELECTRONIC_COMMERCE_PRICE,
          IFNULL(A.BEFORE_MOD_ELECTRONIC_COMMERCE_PRICE,0) AS BEFORE_MOD_ELECTRONIC_COMMERCE_PRICE,
          IFNULL(A.AFTER_MOD_RESEARCH_TERMINAL_PRICE,0) AS AFTER_MOD_RESEARCH_TERMINAL_PRICE,
          IFNULL(A.BEFORE_MOD_RESEARCH_TERMINAL_PRICE,0) AS BEFORE_MOD_RESEARCH_TERMINAL_PRICE,
          CASE
              WHEN (I.AFFECT_ORDER_COUNT <![CDATA[>=]]> 0 ) THEN 1
          ELSE 0
          END AS IS_HAVE_UN_DEAL_AFFECT_ORDER
          FROM
            T_SKU_PRICE_MODIFY_RECORD AS A
          LEFT JOIN V_CORE_SKU AS B ON A.SKU_ID = B.SKU_ID
          LEFT JOIN T_GOODS AS C ON B.SKU_NO = C.SKU
          LEFT JOIN T_BRAND AS D ON D.BRAND_ID = C.BRAND_ID
          LEFT JOIN T_UNIT AS E ON E.UNIT_ID = B.UNIT_ID
          <if test="skuPriceModifyRecordSearchDto.holderNameSearchString != null and skuPriceModifyRecordSearchDto.holderNameSearchString != ''" >
              LEFT JOIN V_CORE_SPU AS F ON B.SPU_ID = F.SPU_ID
              LEFT JOIN T_USER AS G ON G.USER_ID = F.ASSIGNMENT_MANAGER_ID
              LEFT JOIN T_USER AS H ON H.USER_ID = F.ASSIGNMENT_ASSISTANT_ID
          </if>
          LEFT JOIN (
              SELECT
                I.SKU_PRICE_MODIFY_RECORD_ID,COUNT(*) AS AFFECT_ORDER_COUNT
              FROM
                T_PRICE_CHANGE_AFFECT_ORDER AS I
                LEFT JOIN T_SALEORDER AS K ON K.SALEORDER_ID = I.ORDER_ID AND I.ORDER_TYPE = 1 AND K.USER_ID = #{skuPriceModifyRecordSearchDto.currentUserId,jdbcType=INTEGER}
                LEFT JOIN T_QUOTEORDER AS L ON L.QUOTEORDER_ID = I.ORDER_ID AND I.ORDER_TYPE = 2 AND L.USER_ID = #{skuPriceModifyRecordSearchDto.currentUserId,jdbcType=INTEGER}
              WHERE I.IS_DEALED = 0
                AND (K.SALEORDER_ID IS NOT NULL OR L.QUOTEORDER_ID IS NOT NULL)
              GROUP BY I.SKU_PRICE_MODIFY_RECORD_ID
          ) AS I ON I.SKU_PRICE_MODIFY_RECORD_ID = A.SKU_PRICE_MODIFY_RECORD_ID
          WHERE
            1=1
          <if test="skuPriceModifyRecordSearchDto.skuNoSearchString != null and skuPriceModifyRecordSearchDto.skuNoSearchString != ''" >
              AND (B.SKU_NO LIKE CONCAT('%',#{skuPriceModifyRecordSearchDto.skuNoSearchString,jdbcType=VARCHAR},'%'))
          </if>
          <if test="skuPriceModifyRecordSearchDto.skuNameSearchString != null and skuPriceModifyRecordSearchDto.skuNameSearchString != ''" >
              AND (B.SKU_NAME LIKE CONCAT('%',#{skuPriceModifyRecordSearchDto.skuNameSearchString,jdbcType=VARCHAR},'%'))
          </if>
          <if test="skuPriceModifyRecordSearchDto.brandNameSearchString != null and skuPriceModifyRecordSearchDto.brandNameSearchString != ''" >
              AND (D.BRAND_NAME LIKE CONCAT('%',#{skuPriceModifyRecordSearchDto.brandNameSearchString,jdbcType=VARCHAR},'%'))
          </if>
          <if test="skuPriceModifyRecordSearchDto.holderNameSearchString != null and skuPriceModifyRecordSearchDto.holderNameSearchString != ''" >
              AND (G.USERNAME LIKE CONCAT('%',#{skuPriceModifyRecordSearchDto.holderNameSearchString,jdbcType=VARCHAR},'%'))
              OR (H.USERNAME LIKE CONCAT('%',#{skuPriceModifyRecordSearchDto.holderNameSearchString,jdbcType=VARCHAR},'%'))
          </if>
          <if test="skuPriceModifyRecordSearchDto.skuPriceModifyType != null and skuPriceModifyRecordSearchDto.skuPriceModifyType != 0" >
              AND  (A.MOD_PRICE_TYPE = #{skuPriceModifyRecordSearchDto.skuPriceModifyType,jdbcType=BIT})
          </if>

          <if test="skuPriceModifyRecordSearchDto.startTimeStr != null and skuPriceModifyRecordSearchDto.startTimeStr != ''" >
              AND  (A.PRICE_MOD_TIME <![CDATA[>=]]> unix_timestamp(#{skuPriceModifyRecordSearchDto.startTimeStr,jdbcType=VARCHAR})*1000)
          </if>
          <if test="skuPriceModifyRecordSearchDto.endTimeStr != null and skuPriceModifyRecordSearchDto.endTimeStr != ''" >
              AND  (A.PRICE_MOD_TIME <![CDATA[<=]]> unix_timestamp(#{skuPriceModifyRecordSearchDto.endTimeStr,jdbcType=VARCHAR})*1000)
          </if>
          <if test="skuPriceModifyRecordSearchDto.isMarketPriceChange == 1 " >
              AND  A.AFTER_MOD_MARKET_PRICE != A.BEFORE_MOD_MARKET_PRICE
          </if>
          <if test="skuPriceModifyRecordSearchDto.isTerminalPriceChange == 1 " >
              AND  A.AFTER_MOD_TERMINAL_PRICE != A.BEFORE_MOD_TERMINAL_PRICE
          </if>
          <if test="skuPriceModifyRecordSearchDto.isDistributionPriceChange == 1 " >
              AND  A.AFTER_MOD_DISTRIBUTION_PRICE != A.BEFORE_MOD_DISTRIBUTION_PRICE
          </if>
          <if test="skuPriceModifyRecordSearchDto.isPurchaseCostsChange == 1 " >
              AND  A.AFTER_MOD_PURCHASE_COSTS != A.BEFORE_MOD_PURCHASE_COSTS
          </if>
          <if test="skuPriceModifyRecordSearchDto.isGroupPriceChange == 1 " >
              AND  A.AFTER_MOD_GROUP_PRICE != A.BEFORE_MOD_GROUP_PRICE
          </if>
          GROUP BY A.SKU_PRICE_MODIFY_RECORD_ID
          ORDER BY A.PRICE_MOD_TIME DESC
    ) AS A
    WHERE
        1=1
      <if test="skuPriceModifyRecordSearchDto.isHaveAffectOrder != 2 and skuPriceModifyRecordSearchDto.isHaveAffectOrder != null" >
          AND  A.IS_HAVE_UN_DEAL_AFFECT_ORDER = #{skuPriceModifyRecordSearchDto.isHaveAffectOrder,jdbcType=INTEGER}
      </if>

  </select>
  <select id="getLastestPriceChangeRecord" resultType="com.pricecenter.dto.SkuPriceModifyRecordIndexDto">
    SELECT
        A.MOD_PRICE_TYPE,
        IFNULL(A.AFTER_MOD_MARKET_PRICE,0) AS AFTER_MOD_MARKET_PRICE,
        IFNULL(A.BEFORE_MOD_MARKET_PRICE,0) AS BEFORE_MOD_MARKET_PRICE,
        IFNULL(A.AFTER_MOD_TERMINAL_PRICE,0) AS AFTER_MOD_TERMINAL_PRICE,
        IFNULL(A.BEFORE_MOD_TERMINAL_PRICE,0) AS BEFORE_MOD_TERMINAL_PRICE,
        IFNULL(A.AFTER_MOD_DISTRIBUTION_PRICE,0) AS AFTER_MOD_DISTRIBUTION_PRICE,
        IFNULL(A.BEFORE_MOD_DISTRIBUTION_PRICE,0) AS BEFORE_MOD_DISTRIBUTION_PRICE,
        IFNULL(A.AFTER_MOD_GROUP_PRICE,0) AS AFTER_MOD_GROUP_PRICE,
        IFNULL(A.BEFORE_MOD_GROUP_PRICE,0) AS BEFORE_MOD_GROUP_PRICE,
        IFNULL(A.AFTER_MOD_PURCHASE_COSTS,0) AS AFTER_MOD_PURCHASE_COSTS,
        IFNULL(A.BEFORE_MOD_PURCHASE_COSTS,0) AS BEFORE_MOD_PURCHASE_COSTS,
      IFNULL(A.AFTER_MOD_ELECTRONIC_COMMERCE_PRICE,0) AS AFTER_MOD_ELECTRONIC_COMMERCE_PRICE,
      IFNULL(A.BEFORE_MOD_ELECTRONIC_COMMERCE_PRICE,0) AS BEFORE_MOD_ELECTRONIC_COMMERCE_PRICE,
      IFNULL(A.AFTER_MOD_RESEARCH_TERMINAL_PRICE,0) AS AFTER_MOD_RESEARCH_TERMINAL_PRICE,
      IFNULL(A.BEFORE_MOD_RESEARCH_TERMINAL_PRICE,0) AS BEFORE_MOD_RESEARCH_TERMINAL_PRICE
    FROM
        T_SKU_PRICE_MODIFY_RECORD AS A
    WHERE
        A.SKU_ID = #{skuId,jdbcType=BIGINT}
        AND A.PRICE_MOD_TIME BETWEEN unix_timestamp(now())*1000 - 14*24*60*60*1000 AND unix_timestamp(now())*1000
    ORDER BY A.PRICE_MOD_TIME DESC
    LIMIT 1
  </select>
  <select id="getYestedayPriceChangeInfo" resultType="com.vedeng.price.model.SkuPriceModifyRecord">
    SELECT
        A.SKU_PRICE_MODIFY_RECORD_ID,
        A.SKU_ID
    FROM
        T_SKU_PRICE_MODIFY_RECORD AS A
        INNER JOIN(
            SELECT
                A.SKU_ID,
                MAX(A.PRICE_MOD_TIME) AS PRICE_MOD_TIME
            FROM
                T_SKU_PRICE_MODIFY_RECORD AS A
            WHERE
                A.PRICE_MOD_TIME BETWEEN UNIX_TIMESTAMP(CAST(SYSDATE()AS DATE) - INTERVAL 1 DAY)*1000 AND UNIX_TIMESTAMP(CAST(SYSDATE()AS DATE))*1000
            GROUP BY A.SKU_ID
    ) AS B ON A.SKU_ID = B.SKU_ID AND A.PRICE_MOD_TIME = B.PRICE_MOD_TIME
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from T_SKU_PRICE_MODIFY_RECORD
    where SKU_PRICE_MODIFY_RECORD_ID = #{skuPriceModifyRecordId,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="com.vedeng.price.model.SkuPriceModifyRecord" >
    insert into T_SKU_PRICE_MODIFY_RECORD (SKU_PRICE_MODIFY_RECORD_ID, SKU_ID, PRICE_MOD_TIME,
      AFTER_MOD_MARKET_PRICE, BEFORE_MOD_MARKET_PRICE, 
      AFTER_MOD_TERMINAL_PRICE, BEFORE_MOD_TERMINAL_PRICE, 
      AFTER_MOD_DISTRIBUTION_PRICE, BEFORE_MOD_DISTRIBUTION_PRICE, 
      AFTER_MOD_PURCHASE_COSTS, BEFORE_MOD_PURCHASE_COSTS, 
      MOD_PRICE_TYPE, CREATOR, ADD_TIME, 
      UPDATER, UPDATE_TIME)
    values (#{skuPriceModifyRecordId,jdbcType=BIGINT}, #{skuId,jdbcType=BIGINT}, #{priceModTime,jdbcType=BIGINT},
      #{afterModMarketPrice,jdbcType=DECIMAL}, #{beforeModMarketPrice,jdbcType=DECIMAL}, 
      #{afterModTerminalPrice,jdbcType=DECIMAL}, #{beforeModTerminalPrice,jdbcType=DECIMAL}, 
      #{afterModDistributionPrice,jdbcType=DECIMAL}, #{beforeModDistributionPrice,jdbcType=DECIMAL}, 
      #{afterModPurchaseCosts,jdbcType=DECIMAL}, #{beforeModPurchaseCosts,jdbcType=DECIMAL}, 
      #{modPriceType,jdbcType=BIT}, #{creator,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER}, #{updateTime,jdbcType=BIGINT})
  </insert>

  <insert id="insertSelective" parameterType="com.vedeng.price.model.SkuPriceModifyRecord" >
    insert into T_SKU_PRICE_MODIFY_RECORD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="skuPriceModifyRecordId != null" >
        SKU_PRICE_MODIFY_RECORD_ID,
      </if>
      <if test="skuId != null" >
         SKU_ID,
      </if>
      <if test="priceModTime != null" >
        PRICE_MOD_TIME,
      </if>
      <if test="afterModMarketPrice != null" >
        AFTER_MOD_MARKET_PRICE,
      </if>
      <if test="beforeModMarketPrice != null" >
        BEFORE_MOD_MARKET_PRICE,
      </if>
      <if test="afterModTerminalPrice != null" >
        AFTER_MOD_TERMINAL_PRICE,
      </if>
      <if test="beforeModTerminalPrice != null" >
        BEFORE_MOD_TERMINAL_PRICE,
      </if>
      <if test="afterModDistributionPrice != null" >
        AFTER_MOD_DISTRIBUTION_PRICE,
      </if>
      <if test="beforeModDistributionPrice != null" >
        BEFORE_MOD_DISTRIBUTION_PRICE,
      </if>
      <if test="afterModGroupPrice != null" >
        AFTER_MOD_GROUP_PRICE,
      </if>
      <if test="beforeModGroupPrice != null" >
        BEFORE_MOD_GROUP_PRICE,
      </if>
      <if test="afterModPurchaseCosts != null" >
        AFTER_MOD_PURCHASE_COSTS,
      </if>
      <if test="beforeModPurchaseCosts != null" >
        BEFORE_MOD_PURCHASE_COSTS,
      </if>

        <if test="afterModElectronicCommercePrice != null" >
            AFTER_MOD_ELECTRONIC_COMMERCE_PRICE,
        </if>
        <if test="beforeModElectronicCommercePrice != null" >
            BEFORE_MOD_ELECTRONIC_COMMERCE_PRICE,
        </if>
        <if test="afterModResearchTerminalPrice != null" >
            AFTER_MOD_RESEARCH_TERMINAL_PRICE,
        </if>
        <if test="beforeModResearchTerminalPrice != null" >
            BEFORE_MOD_RESEARCH_TERMINAL_PRICE,
        </if>

      <if test="modPriceType != null" >
        MOD_PRICE_TYPE,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="skuPriceModifyRecordId != null" >
        #{skuPriceModifyRecordId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null" >
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="priceModTime != null" >
        #{priceModTime,jdbcType=BIGINT},
      </if>
      <if test="afterModMarketPrice != null" >
        #{afterModMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="beforeModMarketPrice != null" >
        #{beforeModMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="afterModTerminalPrice != null" >
        #{afterModTerminalPrice,jdbcType=DECIMAL},
      </if>
      <if test="beforeModTerminalPrice != null" >
        #{beforeModTerminalPrice,jdbcType=DECIMAL},
      </if>
      <if test="afterModDistributionPrice != null" >
        #{afterModDistributionPrice,jdbcType=DECIMAL},
      </if>
      <if test="beforeModDistributionPrice != null" >
        #{beforeModDistributionPrice,jdbcType=DECIMAL},
      </if>
      <if test="afterModGroupPrice != null" >
        #{afterModGroupPrice,jdbcType=DECIMAL},
      </if>
      <if test="beforeModGroupPrice != null" >
        #{beforeModGroupPrice,jdbcType=DECIMAL},
      </if>
      <if test="afterModPurchaseCosts != null" >
        #{afterModPurchaseCosts,jdbcType=DECIMAL},
      </if>
      <if test="beforeModPurchaseCosts != null" >
        #{beforeModPurchaseCosts,jdbcType=DECIMAL},
      </if>

        <if test="afterModElectronicCommercePrice != null" >
            #{afterModElectronicCommercePrice,jdbcType=DECIMAL},
        </if>
        <if test="beforeModElectronicCommercePrice != null" >
            #{beforeModElectronicCommercePrice,jdbcType=DECIMAL},
        </if>
        <if test="afterModResearchTerminalPrice != null" >
            #{afterModResearchTerminalPrice,jdbcType=DECIMAL},
        </if>
        <if test="beforeModResearchTerminalPrice != null" >
            #{beforeModResearchTerminalPrice,jdbcType=DECIMAL},
        </if>

      <if test="modPriceType != null" >
        #{modPriceType,jdbcType=BIT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.price.model.SkuPriceModifyRecord" >
    update T_SKU_PRICE_MODIFY_RECORD
    <set >
      <if test="skuId != null" >
         SKU_ID = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="priceModTime != null" >
        PRICE_MOD_TIME = #{priceModTime,jdbcType=BIGINT},
      </if>
      <if test="afterModMarketPrice != null" >
        AFTER_MOD_MARKET_PRICE = #{afterModMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="beforeModMarketPrice != null" >
        BEFORE_MOD_MARKET_PRICE = #{beforeModMarketPrice,jdbcType=DECIMAL},
      </if>
      <if test="afterModTerminalPrice != null" >
        AFTER_MOD_TERMINAL_PRICE = #{afterModTerminalPrice,jdbcType=DECIMAL},
      </if>
      <if test="beforeModTerminalPrice != null" >
        BEFORE_MOD_TERMINAL_PRICE = #{beforeModTerminalPrice,jdbcType=DECIMAL},
      </if>
      <if test="afterModDistributionPrice != null" >
        AFTER_MOD_DISTRIBUTION_PRICE = #{afterModDistributionPrice,jdbcType=DECIMAL},
      </if>
      <if test="beforeModDistributionPrice != null" >
        BEFORE_MOD_DISTRIBUTION_PRICE = #{beforeModDistributionPrice,jdbcType=DECIMAL},
      </if>
      <if test="afterModPurchaseCosts != null" >
        AFTER_MOD_PURCHASE_COSTS = #{afterModPurchaseCosts,jdbcType=DECIMAL},
      </if>
      <if test="beforeModPurchaseCosts != null" >
        BEFORE_MOD_PURCHASE_COSTS = #{beforeModPurchaseCosts,jdbcType=DECIMAL},
      </if>
      <if test="modPriceType != null" >
        MOD_PRICE_TYPE = #{modPriceType,jdbcType=BIT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where SKU_PRICE_MODIFY_RECORD_ID = #{skuPriceModifyRecordId,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.vedeng.price.model.SkuPriceModifyRecord" >
    update T_SKU_PRICE_MODIFY_RECORD
    set  SKU_ID = #{skuId,jdbcType=BIGINT},
      PRICE_MOD_TIME = #{priceModTime,jdbcType=BIGINT},
      AFTER_MOD_MARKET_PRICE = #{afterModMarketPrice,jdbcType=DECIMAL},
      BEFORE_MOD_MARKET_PRICE = #{beforeModMarketPrice,jdbcType=DECIMAL},
      AFTER_MOD_TERMINAL_PRICE = #{afterModTerminalPrice,jdbcType=DECIMAL},
      BEFORE_MOD_TERMINAL_PRICE = #{beforeModTerminalPrice,jdbcType=DECIMAL},
      AFTER_MOD_DISTRIBUTION_PRICE = #{afterModDistributionPrice,jdbcType=DECIMAL},
      BEFORE_MOD_DISTRIBUTION_PRICE = #{beforeModDistributionPrice,jdbcType=DECIMAL},
      AFTER_MOD_PURCHASE_COSTS = #{afterModPurchaseCosts,jdbcType=DECIMAL},
      BEFORE_MOD_PURCHASE_COSTS = #{beforeModPurchaseCosts,jdbcType=DECIMAL},
      MOD_PRICE_TYPE = #{modPriceType,jdbcType=BIT},
      CREATOR = #{creator,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATE_TIME = #{updateTime,jdbcType=BIGINT}
    where SKU_PRICE_MODIFY_RECORD_ID = #{skuPriceModifyRecordId,jdbcType=BIGINT}
  </update>

</mapper>