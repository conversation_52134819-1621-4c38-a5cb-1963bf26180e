package com.vedeng.erp.trader.service.impl;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.vedeng.erp.trader.domain.entity.DwhTraderTagCpmErpEntity;
import com.vedeng.erp.trader.mapper.DwhTraderTagCpmErpMapper;
import com.vedeng.erp.trader.service.DwhTraderTagCpmErpService;

import java.util.List;

@Service
public class DwhTraderTagCpmErpServiceImpl implements DwhTraderTagCpmErpService{

    @Resource
    private DwhTraderTagCpmErpMapper dwhTraderTagCpmErpMapper;

    @Override
    public int insert(DwhTraderTagCpmErpEntity record) {
        return dwhTraderTagCpmErpMapper.insert(record);
    }

    @Override
    public int insertSelective(DwhTraderTagCpmErpEntity record) {
        return dwhTraderTagCpmErpMapper.insertSelective(record);
    }

    @Override
    public List<DwhTraderTagCpmErpEntity> findAll(DwhTraderTagCpmErpEntity record) {
        return dwhTraderTagCpmErpMapper.findByAll(record);
    }

}
