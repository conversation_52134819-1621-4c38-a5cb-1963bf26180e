<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="入库详情页" scope="application" />
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp"%>
<div class="content">
    <div class="formtitle"></div>
    <div  id="desc_div">
        <form method="post" id="addForm" action="${pageContext.request.contextPath}/wms/commodityLendOut/addLendOut.do">
            <ul class="payplan">

                <div class="parts">

                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">
                            基本信息
                        </div>
                    </div>

                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <tbody>
                        <tr>
                            <td class="table-smaller">入库单号</td>
                            <td>${wmsInputOrder.orderNo}</td>
                            <td class="table-smaller">入库状态</td>
                            <td>
                                <c:choose>
                                    <c:when test="${wmsInputOrder.arrivalStatus eq 0}">
                                        未收货
                                    </c:when>
                                    <c:when test="${wmsInputOrder.arrivalStatus eq 1}">
                                        部分收货
                                    </c:when>
                                    <c:when test="${wmsInputOrder.arrivalStatus eq 2}">
                                        全部收货
                                    </c:when>
                                </c:choose>
                            </td>
                        </tr>
                        <tr>
                            <td>申请入库日期</td>
                            <td><fmt:formatDate value ="${wmsInputOrder.applyIntime}"/></td>
                            <td>申请人</td>
                            <td>${wmsInputOrder.applyer}</td>
                        </tr>
                        <tr>
                            <td>申请部门</td>
                            <td>${wmsInputOrder.applyerDepartment}</td>
                            <td>备注</td>
                            <td>${wmsInputOrder.remark}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">供应商信息</div>
                        </div>
                        <table style="width: 100%">
                            <tr style="width: 100%">
                                <td colspan="7" style="text-align: center;width: 100%">暂无供应商信息</td>
                            </tr>
                        </table>
                    </div>

                </li>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">产品信息</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered" >
                            <thead>
                            <tr>
                                <th style="width:80px">序号</th>
                                <th style="width:80px">订货号</th>
                                <th style="width:80px">产品名称</th>
                                <th style="width:80px">品牌</th>
                                <th style="width:80px">型号</th>
                                <th style="width:80px">需入库数量</th>
                                <th style="width:80px">入库数量</th>
                            </tr>
                            </thead>
                            <tbody id="thisTimeUpdateTbody">
                            <c:forEach var="ordergoods" items="${wmsInputOrderGoodsList}" varStatus="staut">
                                <tr>
                                    <td>${staut.count}</td>
                                    <td>${ordergoods.skuNo}</td>
                                    <td>${ordergoods.showName}</td>
                                    <td>${ordergoods.brandName}</td>
                                    <td>${ordergoods.model}</td>
                                    <td>${ordergoods.inputNum}</td>
                                    <td>${ordergoods.arrivalNum}</td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </li>

                <div class="parts">
                    <div class="title-container">
                        <div class="table-title nobor">入库记录</div>
                    </div>
                    <form method="post" id="inSearchc" action="<%= basePath %>logistics/warehousein/printInOrder.do">
                        <table class="table">
                            <thead>
                            <tr>
                                <th class="wid5">序号</th>
                                <th>订货号</th>
                                <th>产品名称</th>
                                <th>品牌</th>
                                <th>型号</th>
                                <th>实际收货数量</th>
                                <th>单位</th>
                                <th>贝登批次码</th>
                                <th>生产日期</th>
                                <th>有效期至</th>
                                <th class="wid15">入库时间</th>
                                <th>厂家批次号</th>
                                <th>灭菌批号</th>
                                <th>注册证号</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="wlist" items="${wlog}" varStatus="num">
                                <tr>
                                    <td>${num.count}</td>
                                    <td>${wlist.sku}</td>
                                    <td class="text-left">
                                        <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${wlist.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${wlist.goodsId}","title":"产品信息"}'>${wlist.goodsName}</a>
                                    </td>
                                    <td>${wlist.brandName}</td>
                                    <td>${wlist.model}</td>
                                        <%--实际收货数量--%>
                                    <td>${wlist.num}</td>
                                    <td>${wlist.unitName}</td>
                                    <td>${wlist.vedengBatchNumer}</td>
                                        <%--生产日期--%>
                                    <td><date:date value ="${wlist.productDate}" format="yyyy-MM-dd"/></td>
                                    <td><date:date value ="${wlist.expirationDate}" format="yyyy-MM-dd"/></td>
                                        <%--<td>${wlist.operator}</td>--%>
                                    <td><date:date value ="${wlist.addTime}" format="yyyy-MM-dd"/></td>
                                    <td>${wlist.batchNumber}</td>
                                    <td>${wlist.sterilizationBatchNo}</td>
                                        <%--注册证号--%>
                                    <td>
                                        <a class="addtitle"
                                           href="javascript:void(0);"
                                           tabTitle='{ "link":"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${wlist.firstEngageId}","title":"注册证号详情页"}'>${wlist.registrationNumber}</a>
                                    </td>
                                </tr>
                            </c:forEach>
                            <c:if test="${!empty wlog}">
                                <tr>
                                    <td colspan="14" class="allchosetr text-left">
                                        <!-- 总数量 -->
                                        <c:set var="allnum" value="0"></c:set>
                                        <c:set var="allwarehouseinnum" value="0"></c:set>
                                        <c:forEach var="ordergoods" items="${wmsInputOrderGoodsList}">
                                            <c:set var="allnum" value="${allnum + ordergoods.inputNum}"></c:set>
                                            <c:set var="allwarehouseinnum" value="${allwarehouseinnum + ordergoods.arrivalNum}"></c:set>
                                        </c:forEach>

                                        已入库/商品总数:<span class="warning-color1">${allwarehouseinnum}/${allnum}</span>
                                    </td>
                                </tr>
                            </c:if>
                            <c:if test="${empty wlog}">
                                <tr>
                                    <td colspan="14">暂无入库记录</td>
                                </tr>
                            </c:if>
                            </tbody>
                        </table>

                    </form>
                </div>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">物流信息</div>
                        </div>
                        <table style="width: 100%">
                            <tr style="width: 100%">
                                <td colspan="7" style="text-align: center;width: 100%">暂无物流信息记录</td>
                            </tr>
                        </table>
                    </div>
                </li>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">入库附件</div>
                        </div>
                        <table style="width: 100%">
                            <tr style="width: 100%">
                                <td colspan="7" style="text-align: center;width: 100%">暂无入库附件记录</td>
                            </tr>
                        </table>
                    </div>

                </li>
            </ul>
        </form>
    </div>
</div>
<%@ include file="../../common/footer.jsp"%>