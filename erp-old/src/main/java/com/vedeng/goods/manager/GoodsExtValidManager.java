package com.vedeng.goods.manager;

import com.ctrip.framework.apollo.ConfigService;
import com.vedeng.common.util.NumberUtil;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.manager.extension.GoodsToDoHandlerChain;
import com.vedeng.goods.manager.extension.GoodsTodoResultVo;
import com.vedeng.goods.manager.extension.GoodsValidResultVo;
import com.vedeng.goods.manager.extension.ToDoHandlerChainBuilder;
import com.vedeng.goods.manager.extension.handler.GoodsTodoHandler;
import com.vedeng.goods.manager.rule.GoodsExtValidElement;
import com.vedeng.goods.manager.rule.GoodsValidTypeEnum;
import com.vedeng.goods.manager.rule.GoodsValidatedRule;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.todolist.service.ITodoInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Component
public class GoodsExtValidManager implements InitializingBean, GoodsInfoChangeListener {

    private final static Logger LOGGER = LoggerFactory.getLogger(GoodsExtValidManager.class);

    private final GoodsValidationRuleLoader goodsValidationRuleLoader = GoodsValidationRuleLoader.getInstance();

    @Resource
    private CoreSkuGenerateMapper coreSkuGenerateMapper;
    @Resource
    private List<ITodoInstance> todoInstances;
    @Resource
    private List<GoodsTodoHandler> goodsTodoHandlers;

    private String erpUrl;


    @Override
    public void onGoodsInfoChange(Integer goodsId) {
        if (!NumberUtil.isPositive(goodsId)) {
            return;
        }

        CoreSkuGenerate coreSkuQuery = coreSkuGenerateMapper.selectByPrimaryKey(goodsId);
        if (coreSkuQuery == null) {
            LOGGER.error("保存SKU成功后查询SKU信息失败 - goodsId:{}", goodsId);
            throw new IllegalStateException();
        }
        GoodsToDoHandlerChain goodsToDoHandlerChain = buildGoodsToDoHandlerChain(coreSkuQuery.getGoodsLevelNo());
        goodsToDoHandlerChain.handle(goodsId);
    }

    public int countTodoItems(Integer skuId) {
        List<GoodsTodoResultVo> todoItems = getTodoItems(skuId);
        return (int) todoItems.stream().filter(item -> !Boolean.TRUE.equals(item.getDone())).count();
    }


    public GoodsValidResultVo checkGoodsWhileSync(Integer skuId) {
        CoreSkuGenerate coreSkuQuery = coreSkuGenerateMapper.selectByPrimaryKey(skuId);
        if (coreSkuQuery == null) {
            throw new IllegalStateException();
        }
        GoodsToDoHandlerChain goodsToDoHandlerChain = buildGoodsToDoHandlerChain(coreSkuQuery.getGoodsLevelNo());
        return goodsToDoHandlerChain.checkTodoItems(skuId);
    }


    public List<GoodsTodoResultVo> getTodoItems(Integer skuId) {
        CoreSkuGenerate coreSkuQuery = coreSkuGenerateMapper.selectByPrimaryKey(skuId);
        if (coreSkuQuery == null) {
            throw new IllegalStateException();
        }
        GoodsToDoHandlerChain goodsToDoHandlerChain = buildGoodsToDoHandlerChain(coreSkuQuery.getGoodsLevelNo());
        return goodsToDoHandlerChain.hasTodoItems(skuId);
    }


    private GoodsToDoHandlerChain buildGoodsToDoHandlerChain(Integer goodsLevelNo) {
        List<GoodsExtValidElement> goodsExtValidElements = getGoodsExtValidRule(goodsLevelNo);
        return ToDoHandlerChainBuilder.newBuilder()
                .setGoodsExtValidElements(goodsExtValidElements)
                .addTodoInstance(todoInstances)
                .addGoodsTodoHandlers(goodsTodoHandlers)
                .build();
    }


    private List<GoodsExtValidElement> getGoodsExtValidRule(Integer goodsLevelNo) {
        GoodsValidatedRule validatedRule = this.getGoodsValidatedRule(GoodsValidTypeEnum.SKU.getType(), goodsLevelNo);
        if (validatedRule == null) {
            throw new IllegalStateException("fail to load the valid rule");
        }

        List<GoodsExtValidElement> goodsExtValidElements = validatedRule.getAdditionalModules();
        if (goodsExtValidElements == null || goodsExtValidElements.isEmpty()) {
            return goodsExtValidElements;
        }
        return goodsExtValidElements;
    }


    public GoodsValidatedRule getGoodsValidatedRule(Integer goodsValidTypeKey, Integer goodsLevelNo) {
        return goodsValidationRuleLoader.loadFromApolloConfig(goodsValidTypeKey, goodsLevelNo);
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        erpUrl = ConfigService.getAppConfig().getProperty("erp_url", "");
        if (erpUrl.length() == 0) {
            throw new IllegalStateException("Can not get property [erp_url] from apollo config");
        }
    }
}
