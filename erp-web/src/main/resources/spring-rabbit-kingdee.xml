<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:apollo="http://www.ctrip.com/schema/apollo" xmlns:rabbit="http://www.springframework.org/schema/rabbit"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.ctrip.com/schema/apollo http://www.ctrip.com/schema/apollo.xsd
		http://www.springframework.org/schema/rabbit http://www.springframework.org/schema/rabbit/spring-rabbit.xsd">

    <!--rabbit连接-->
    <rabbit:connection-factory
            id="kingDeeConnectionFactory"
            addresses="${kingdee.rabbitmq.address}"
            username="${kingdee.rabbitmq.username}"
            password="${kingdee.rabbitmq.password}"
            virtual-host="${kingdee.rabbitmq.virtualHost}"
            channel-cache-size="100"
            publisher-returns="true"
            publisher-confirms="true"/>

    <!-- 定义mq管理 -->
    <rabbit:admin connection-factory="kingDeeConnectionFactory"/>


    <bean id="jackson2JsonMessageConverter"
          class="org.springframework.amqp.support.converter.Jackson2JsonMessageConverter" />

    <rabbit:template id="kingDeeRabbitTemplate"
                     connection-factory="kingDeeConnectionFactory" message-converter="jackson2JsonMessageConverter"/>

    <rabbit:queue name="kingDeeEventQueue" durable="true" auto-delete="false" exclusive="false"/>
    <rabbit:direct-exchange name="kingDeeEventQueueExchange" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="kingDeeEventQueue" key="kingDeeEventQueueRoutingKey"/>
        </rabbit:bindings>
    </rabbit:direct-exchange>

    <rabbit:listener-container connection-factory="kingDeeConnectionFactory" acknowledge="manual"
                               failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener ref="kingDeeMqConsumer" queues="kingDeeEventQueue"/>
    </rabbit:listener-container>


</beans>
