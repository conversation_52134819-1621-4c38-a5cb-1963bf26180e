package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWmsOutputOrderDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWmsOutputOrderGoodsDto;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWmsOutputOrderDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeProfitLossDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeProfitLossDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 盘亏出库单
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchProfitLossProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeProfitLossDto> {
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private BatchWmsOutputOrderDtoMapper batchWmsOutputOrderDtoMapper;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;


    @Override
    public KingDeeProfitLossDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        KingDeeProfitLossDto dto = new KingDeeProfitLossDto();
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());

        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(dto);
        if(old){
            log.info("盘亏出库单,数据已存在:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }

        log.info("盘亏出库单,BatchWarehouseOutInProcessorService.process:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
        // erp原始单据 盘亏出库单
        BatchWmsOutputOrderDto wmsInputOrderDto = batchWmsOutputOrderDtoMapper.findByOrderNo(batchWarehouseGoodsOutInDto.getRelateNo());
        if (wmsInputOrderDto == null) {
            return null;
        }

        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo()));

        Map<Integer, BatchWmsOutputOrderGoodsDto> map = wmsInputOrderDto.getBatchWmsOutputOrderGoodsDtos().stream()
                .collect(Collectors.toMap(BatchWmsOutputOrderGoodsDto::getGoodsId, c -> c, (k1, k2) -> k1));

        // 主表
        dto.setFId("0");
        dto.setFDate(DateUtil.formatDateTime(batchWarehouseGoodsOutInDto.getOutInTime()));
        dto.setFQzokBddjtId(batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString());

        // 详细
        List<KingDeeProfitLossDetailDto> detailList = new ArrayList<>();
        if (CollUtil.isEmpty(batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos())) {
            return null;
        }
        batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos().forEach(d -> {
            KingDeeProfitLossDetailDto detailDto = new KingDeeProfitLossDetailDto();
            BatchWmsOutputOrderGoodsDto wmsInputOrderGoodsDto = map.get(d.getGoodsId());
            detailDto.setFMaterialId(wmsInputOrderGoodsDto.getSkuNo());
            detailDto.setFBaseLossQty(d.getNum().abs().toString());
            detailDto.setFQzokYsddh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokGsywdh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokPch(d.getVedengBatchNumber());
            detailDto.setFQzokXlh(d.getBarcodeFactory());
            detailDto.setFQzokSfzf("否");
            detailList.add(detailDto);
        });
        dto.setFBillEntry(detailList);
        return dto;
    }


}
