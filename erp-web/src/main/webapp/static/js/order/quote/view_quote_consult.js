function replyQuoteConsult(quoteorderId,consultStatus){
	checkLogin();
	
	/*var updateStr = "";
	//循环验证哪一行数据需要保存
	$("input[name^='goodsCategoryUser_']").each(function(i){
		if($(this).val() == "y"){
			updateStr += i;
		}
	});*/
	
	var updateStr = "";
	//循环验证哪一行数据有改动
	// $("input[name^='referenceCostPrice_']").each(function(i){
	// 	if($(this).val() != $(this).attr("alt")){
	// 		updateStr += i;
	// 	}
	// });
	$("input[name^='referencePrice_']").each(function(i){
		if($(this).val() != $(this).attr("alt")){
			updateStr += i;
		}
	});
	$("input[name^='referenceDeliveryCycle_']").each(function(i){
		if($(this).val() != $(this).attr("alt")){
			updateStr += i;
		}
	});
	$("input[name='reportStatus']").each(function(i){
		if($(this).val() != $(this).attr("alt")){
			updateStr += i;
		}
	});
	$("input[name^='reportComments_']").each(function(i){
		if($(this).val() != $(this).attr("alt")){
			updateStr += i;
		}
	});
	$("textarea[name^='consultOther_']").each(function(i){
		if($(this).val() != $(this).attr("data-origin")){
			updateStr += i;
		}
	});
	
	var flag = true;

	//参考价格（产品部门报价）

	var referencePriceArr = new Array();
	$("input[name^='referencePrice_']").each(function(i){
		if(updateStr.indexOf(i) != -1){
			if ($(this).val() != $(this).attr("alt")){
				referencePriceArr[referencePriceArr.length] = $(this).val();
				if(!vailNum(this)){
					flag = false;
					return flag;
				}
			} else {
				referencePriceArr[referencePriceArr.length] = "";
			}

		}
	});
	if(flag==false){
		return false;
	}
	
	//参考货期（产品部门回复）
	var referenceDeliveryCycleArr = new Array();
	$("input[name^='referenceDeliveryCycle_']").each(function(i){
		if(updateStr.indexOf(i) != -1){
			if ($(this).val() != $(this).attr("alt")){
				referenceDeliveryCycleArr[referenceDeliveryCycleArr.length] = $(this).val();
				if(!vailStr(this,32)){
					flag = false;
					return flag;
				}
			} else {
				referenceDeliveryCycleArr[referenceDeliveryCycleArr.length] = "";
			}
		}
	});
	if(flag==false){
		return false;
	}
	
	//报备结果 -2无效，-1报备失败，0无需报备，1报备成功
	var reportStatusArr = new Array();
	$("input[name='reportStatus']").each(function(i){
		if(updateStr.indexOf(i) != -1){
			if ($(this).val() != $(this).attr("alt")){
				reportStatusArr[reportStatusArr.length] = $(this).val();
			} else {
				reportStatusArr[reportStatusArr.length] = -2;
			}
		}
	});
	
	//报备原因
	var reportCommentsArr = new Array();
	$("input[name^='reportComments_']").each(function(i){
		if(updateStr.indexOf(i) != -1){
			if ($(this).val() != $(this).attr("alt")){
				reportCommentsArr[reportCommentsArr.length] = $(this).val();
				if(!vailStr(this,64)){
					flag = false;
					return flag;
				}
			} else {
				reportCommentsArr[reportCommentsArr.length] = "";
			}
		}
	});
	if(flag==false){
		return false;
	}

	var consultOtherArr = new Array();
	$("textarea[name^='consultOther_']").each(function (i) {
		if(updateStr.indexOf(i) != -1 ){
			if ($(this).val() != $(this).attr("data-origin")){
				consultOtherArr[consultOtherArr.length] = $(this).val();
				if(!vailStr(this,256)){
					flag = false;
					return flag;
				}
			} else {
				consultOtherArr[consultOtherArr.length] = $(this).val();
			}
		}
	})

	if(flag==false){
		return false;
	}

	var quoteorderConsultIdArr = new Array();
	$("input[name='quoteorderConsultId']").each(function(i){
		if(updateStr.indexOf(i) != -1){
			quoteorderConsultIdArr[quoteorderConsultIdArr.length] = $(this).val();
		}
	});

	var contentEmpty = referencePriceArr.length == 0 && referenceDeliveryCycleArr.length == 0 && reportStatusArr.length == 0 && consultOtherArr.length == 0;
	if (quoteorderConsultIdArr.length == 0 || contentEmpty){
		layer.alert("请填写相应的回复信息")
		return false;
	}
	debugger
	for (let i = 0; i < reportStatusArr.length; i++) {
		if (reportStatusArr[i] == 3 && reportCommentsArr[i].length == 0){
			layer.alert("请填写报备失败原因");
			return false;
		}
	}

	waitWindowNew('no');
	$.ajax({
		async : false,
		url : './saveReplyQuoteConsult.do',
		data : {"quoteorderId":quoteorderId,"beforeParams":$("input[name='beforeParams']").val(),"formToken":$("input[name='formToken']").val(),
				"referencePriceArr":JSON.stringify(referencePriceArr),
				"referenceDeliveryCycleArr":JSON.stringify(referenceDeliveryCycleArr),"reportStatusArr":JSON.stringify(reportStatusArr),
				"reportCommentsArr":JSON.stringify(reportCommentsArr),
				"consultOtherArr":JSON.stringify(consultOtherArr),
				"quoteorderConsultIdArr":JSON.stringify(quoteorderConsultIdArr),
				"consultStatus":consultStatus},
		type : "POST",
		dataType : "json",
		success : function(data) {
			if (data.code == 0){
				layer.close()
				window.location.reload();
			} else {
				layer.alert(data.message);
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	})
}

function editConsultStatus(quoteorderId,consultStatus){
	checkLogin();
	layer.confirm("您是否确认该操作？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			$.ajax({
				type: "POST",
				url: "./editConsultStatus.do",
				data: {'quoteorderId':quoteorderId,'consultStatus':consultStatus,"beforeParams":$("input[name='beforeParams']").val(),"formToken":$("input[name='formToken']").val()},
				dataType:'json',
				success: function(data){
					layer.alert(data.message, {
						icon : (data.code == 0 ? 1 : 2)
					}, function() {
						if (parent.layer != undefined) {
							parent.layer.close(index);
						}
						this.window.location.reload();
					});
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		}, function(){
	});
}

function changeReport(num,index){
	checkLogin();
	$("#reportStatus"+index).val(num);
}

function vailNum(obj){
	checkLogin();
	if($(obj).val().length>14){
		layer.alert("价格长度最大不允许14个字符。", {
			icon : 2
		}, function(index) {
			$(obj).addClass("errorbor");
			layer.close(index);
			$(obj).focus();
		});
		return false;
	} else {
		return true;
	}
	// var isNum = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
	// if($(obj).val() != undefined && $(obj).val()!="" && !isNum.test($(obj).val())){
	// 	layer.alert("价格输入格式错误！仅允许使用数字，最多精确到小数点后两位。", {
	// 		icon : 2
	// 	}, function(index) {
	// 		$(obj).addClass("errorbor");
	// 		layer.close(index);
	// 		$(obj).focus();
	// 	});
	// 	return false;
	// }else{
	// 	$(obj).removeClass("errorbor");
	// 	return true;
	// }
}

function vailStr(obj,num){
	checkLogin();
	var str = $(obj).val().trim();
	if(str.length!=0 && str.length>num){
		layer.alert("内容长度应该在0-" + num + "个字符之间。", {
			icon : 2,
			yes : function(index) {
				$(obj).addClass("errorbor");
				layer.close(index);
				$(obj).focus();
			}
		});
		return false;
	}else{
		$(obj).removeClass("errorbor");
		return true;
	}
}