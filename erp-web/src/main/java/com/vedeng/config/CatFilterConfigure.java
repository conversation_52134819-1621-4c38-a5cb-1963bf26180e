package com.vedeng.config;

import com.vedeng.common.cat.config.CatFeignConfiguration;
import com.vedeng.common.cat.filter.CatContextServletFilter;
import feign.RequestInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/3/7
 */
@Configuration
public class CatFilterConfigure {

    private static final String CAT_EXCLUDE_URL_NAME = "exclude";
    /**
     * CAT监控放行URL的列表
     * 支持前缀*通配和具体URL，多个放行URL以英文分号间隔
     */
    @Value("${cat.excludeUrl}")
    private String catExcludeUrl;

//    @Bean
//    public FilterRegistrationBean  catFilter() {
//        // 创建 CatFilter 对象
//        CatFilter filter = new CatFilter();
//        // 创建 FilterRegistrationBean 对象
//        FilterRegistrationBean registration = new FilterRegistrationBean();
//        registration.setFilter(filter);
//        registration.addUrlPatterns("/*"); // 匹配所有 URL
//        registration.setName("cat-filter");
//        registration.setOrder(1);
//        return registration;
//    }

    @Bean
    public FilterRegistrationBean catChainFilter() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        CatContextServletFilter filter = new CatContextServletFilter();
        // 放行静态资源，注意：CatFilter只支持前缀过滤和具体URL
        registration.addInitParameter(CAT_EXCLUDE_URL_NAME, catExcludeUrl);
        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        registration.setName("cat-chain-filter");
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE + 1);
        return registration;
    }

    @Bean
    public RequestInterceptor requestInterceptor() {
        return new CatFeignConfiguration();
    }

}
