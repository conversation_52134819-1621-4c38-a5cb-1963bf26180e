package com.vedeng.erp.kingdee.common.base;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 基础dto，command相互转换方法
 * @date 2022/3/4 14:41
 */
public interface BaseCommandMapStruct<C, D> {

    /**
     * DTO转Command
     *
     * @param dto
     * @return
     */
    C toCommand(D dto);

    /**
     * DTO集合转Entity集合
     *
     * @param dtoList
     * @return
     */
    List<C> toCommand(List<D> dtoList);

}