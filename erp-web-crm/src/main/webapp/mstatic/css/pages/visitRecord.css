.visit-list-item {
  background: #fff;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 5px;
}
.visit-list-item:last-child {
  margin-bottom: 0;
}
.visit-list-item .visit-title {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
.visit-list-item .visit-title .title-icon {
  width: 20px;
  height: 20px;
  background-size: 100% 100%;
  margin-right: 5px;
}
.visit-list-item .visit-title .title-icon.lv-S {
  background-image: url('/mstatic/image/traderLevel/S.svg');
}
.visit-list-item .visit-title .title-icon.lv-S1 {
  background-image: url('/mstatic/image/traderLevel/S1.svg');
}
.visit-list-item .visit-title .title-icon.lv-S2 {
  background-image: url('/mstatic/image/traderLevel/S2.svg');
}
.visit-list-item .visit-title .title-icon.lv-A1 {
  background-image: url('/mstatic/image/traderLevel/A1.svg');
}
.visit-list-item .visit-title .title-icon.lv-A2 {
  background-image: url('/mstatic/image/traderLevel/A2.svg');
}
.visit-list-item .visit-title .title-icon.lv-A3 {
  background-image: url('/mstatic/image/traderLevel/A3.svg');
}
.visit-list-item .visit-title .title-icon.lv-B1 {
  background-image: url('/mstatic/image/traderLevel/B1.svg');
}
.visit-list-item .visit-title .title-icon.lv-B2 {
  background-image: url('/mstatic/image/traderLevel/B2.svg');
}
.visit-list-item .visit-title .title-icon.lv-B3 {
  background-image: url('/mstatic/image/traderLevel/B3.svg');
}
.visit-list-item .visit-title .title-icon.lv-C1 {
  background-image: url('/mstatic/image/traderLevel/C1.svg');
}
.visit-list-item .visit-title .title-icon.lv-C2 {
  background-image: url('/mstatic/image/traderLevel/C2.svg');
}
.visit-list-item .visit-title .title-icon.lv-C3 {
  background-image: url('/mstatic/image/traderLevel/C3.svg');
}
.visit-list-item .visit-title .title-icon.lv-D {
  background-image: url('/mstatic/image/traderLevel/D.svg');
}
.visit-list-item .visit-title .title-txt {
  flex: 1;
  font-size: 14px;
  font-weight: 700;
}
.visit-list-item .visit-title .title-status {
  line-height: 22px;
  padding: 0 5px;
  border-radius: 3px;
}
.visit-list-item .visit-title .title-status.status-1 {
  background: #FFEDE0;
  color: #f60;
}
.visit-list-item .visit-title .title-status.status-2 {
  background: #E0F3FF;
  color: #09f;
}
.visit-list-item .visit-title .title-status.status-3 {
  background: #E3F7E3;
  color: #13BF13;
}
.visit-list-item .visit-title .title-status.status-4 {
  background: #E3EAF0;
  color: #1A4D80;
}
.visit-list-item .visit-tip {
  display: flex;
  color: #999;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 10px;
  border-bottom: solid 1px #F5F7FA;
}
.visit-list-item .visit-info {
  padding-top: 10px;
}
.visit-list-item .visit-info .info-item {
  display: flex;
  margin-bottom: 5px;
}
.visit-list-item .visit-info .info-item:last-child {
  margin-bottom: 0;
}
.visit-list-item .visit-info .info-item .info-label {
  width: 60px;
  color: #999;
}
.visit-list-item .visit-info .info-item .info-txt {
  flex: 1;
  word-break: break-all;
}
.card-address-btn {
  display: flex;
  align-items: center;
  color: #09f;
}
.card-address-btn .icon-address {
  font-size: 16px;
  margin-right: 5px;
}
.slide-panel {
  max-height: calc(100vh - 45px - 53px);
}
.slide-panel .slide-wrap {
  max-height: calc(100vh - 45px - 53px - 53px);
}
.slide-panel .slide-wrap.form {
  padding: 10px;
  background: #F5F7FA;
}
/* 新建拜访 */
.business-page-wrap {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.businessNo-content {
  padding-right: 10px;
}
.businessNo-content.pb {
  padding-bottom: 10px;
}
.other-contact-list .other-contact-item-wrap {
  border-bottom: solid 1px #F5F7FA;
}
.other-contact-list .other-contact-item-wrap .other-contact-item {
  display: flex;
  align-items: center;
}
.other-contact-list .other-contact-item-wrap .other-contact-item .contact-input {
  flex: 1;
  min-width: 0;
}
.other-contact-list .other-contact-item-wrap .other-contact-item .contact-del {
  width: 38px;
  height: 38px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.other-contact-list .other-contact-item-wrap .other-contact-item .contact-del .icon-delete {
  font-size: 16px;
  color: #666;
}
.address-detail {
  position: relative;
  padding: 0 0 10px;
}
.address-detail .ad-tip {
  margin-top: 5px;
  padding: 0 10px;
}
.address-detail .count {
  position: absolute;
  top: -23px;
  right: 10px;
  font-size: 12px;
  color: #999;
}
.textarea-count {
  position: absolute;
  top: -23px;
  right: 10px;
  font-size: 12px;
  color: #999;
}
.businessNo-check {
  padding-left: 10px;
}
.businessNo-check .info-item {
  font-size: 12px;
  color: #666;
}
.businessNo-check /deep/ .form-tip {
  margin-top: 10px;
}
.featurn-box {
  height: 53px;
  display: flex;
  align-items: center;
  padding-right: 10px;
}
.featurn-box.single {
  padding: 10px;
}
.featurn-box .more {
  width: 53px;
  height: 53px;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.featurn-box .more .icon1 {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  border: solid 2px #666;
  font-size: 16px;
  color: #666;
}
/* 拜访详情 */
.business-detail-container {
  padding-top: 40px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.business-detail-container .detail-page-wrap {
  position: relative;
  z-index: 2;
  padding-bottom: 53px;
}
.business-detail-container .operation-pagr-wrap {
  position: relative;
  z-index: 2;
  height: calc(100vh - 40px);
}
.business-detail-container .page-top-bg {
  height: 121px;
  background: linear-gradient(to bottom, #ddeefa 0%, #f5f7fa 100%);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
}
.business-detail-container .page-top-bg.finish {
  background: linear-gradient(to bottom, #e0f2e4 0%, #f5f7fa 100%);
}
.business-detail-container .page-top-bg.close {
  background: linear-gradient(to bottom, #E3EAF0 0%, #f5f7fa 100%);
}
.business-detail-container .page-top-bg.wait {
  background: linear-gradient(to bottom, #ffede1 0%, #f5f7fa 100%);
}
.business-detail-container .page-nav {
  width: 100vw;
  height: 40px;
  overflow: hidden;
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
}
.business-detail-container .page-nav .nav-item {
  flex: 1;
  min-width: 0;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.business-detail-container .page-nav .nav-item .nav {
  position: relative;
  font-size: 14px;
  font-weight: 400;
  color: #000;
  line-height: 40px;
}
.business-detail-container .page-nav .nav-item .nav.active {
  color: #09f;
  font-weight: 700;
}
.business-detail-container .page-nav .nav-item .nav.active::after {
  content: "";
  border-bottom: solid 3px #09f;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
}
.business-detail-container .page-nav::after {
  content: "";
  height: 121px;
  background: linear-gradient(to bottom, #ddeefa 0%, #f5f7fa 100%);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
}
.business-detail-container .page-nav.finish::after {
  background: linear-gradient(to bottom, #e0f2e4 0%, #f5f7fa 100%);
}
.business-detail-container .page-nav.close::after {
  background: linear-gradient(to bottom, #E3EAF0 0%, #f5f7fa 100%);
}
.business-detail-container .page-nav.wait::after {
  background: linear-gradient(to bottom, #ffede1 0%, #f5f7fa 100%);
}
.page-btns .btn {
  font-size: 14px;
  color: #000;
  padding: 10px 0;
  text-align: center;
  border-bottom: solid 1px #F5F7FA;
}
.page-btns .btn:last-child {
  border-bottom: none;
}
.page-btns .btn.red {
  color: #e64545;
}
.fixed-btn {
  position: fixed;
  bottom: 50px;
  right: 10px;
  z-index: 99;
}
.fixed-btn.maxTop {
  bottom: 103px;
}
.fixed-btn .to-list {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  background: #fff;
  box-shadow: 1px 6px 13px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}
.fixed-btn .to-list > .icon-home {
  font-size: 24px;
  color: #666;
}
.close-reason {
  margin: 0 10px;
  border-radius: 5px;
  overflow: hidden;
}
.visitRecord-num {
  padding-right: 44px;
}
.copy {
  font-size: 12px;
  color: #09f;
  line-height: 38px;
  width: 44px;
  height: 38px;
  text-align: center;
  display: block;
  position: absolute;
  right: 0;
  top: 0;
}
.price-light {
  color: #E64545;
}
.dedtail-chance-step {
  margin-bottom: 10px;
}
.visit-status {
  display: inline-block;
  height: 22px;
  line-height: 22px;
  padding: 0 5px;
  border-radius: 3px;
  color: #FF6600;
  background-color: rgba(255, 102, 0, 0.1);
  font-size: 12px;
  margin-top: 1px;
}
.visit-status.status1 {
  background: #ffede1;
  color: #F60;
}
.visit-status.status2 {
  background: #e1f3ff;
  color: #09F;
}
.visit-status.status3 {
  background: #E3F7E3;
  color: #13BF13;
}
.visit-status.status4 {
  background: #E3EAF0;
  color: #1A4D80;
}
.detail-trader-name .form-fields {
  padding-right: 0;
}
.user-label-list {
  margin-bottom: -10px;
  display: flex;
  flex-wrap: wrap;
}
.user-label-list .user-label-wrap {
  margin-right: 20px;
  margin-bottom: 10px;
}
.card-wrap .form-card {
  margin-bottom: 5px;
}
.card-wrap .form-card:last-child {
  margin-bottom: 0;
}
.card-pic-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -5px;
}
.card-pic-list .pic {
  border-radius: 3px;
  overflow: hidden;
  margin-right: 5px;
  margin-bottom: 5px;
}
.card-pic-list .pic.s60 {
  width: 60px;
  height: 60px;
}
.card-pic-list .pic > img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.communicate-list {
  background: #fff;
  border-radius: 5px;
  overflow: hidden;
  padding: 10px;
}
.communicate-list .communicate-item {
  margin-bottom: 10px;
}
.communicate-list .communicate-item:last-child {
  margin-bottom: 0;
}
.communicate-list .communicate-item .c-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.communicate-list .communicate-item .c-top .c-user {
  flex: 1;
  min-width: 0;
}
.communicate-list .communicate-item .c-top .c-time {
  width: 130px;
  flex-shrink: 0;
  text-align: right;
}
.communicate-list .communicate-item .c-content {
  color: #000;
  margin-top: 5px;
}
.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
  background: #fff;
  border-radius: 5px;
}
.empty-data > i {
  font-size: 32px;
  color: #09F;
}
.empty-data p {
  margin-top: 10px;
}
.close-radio {
  display: flex;
  flex-wrap: wrap;
}
.close-radio .vd-ui-radio-item {
  flex: 0 0 50%;
  margin: 0 0 20px;
}
.close-radio .vd-ui-radio-item:nth-child(2n-1) {
  padding-right: 5px;
}
.close-radio .vd-ui-radio-item:nth-child(2n) {
  padding-left: 5px;
}
/* 操作记录 */
.operation-records {
  height: 100%;
  padding: 10px 0 5px;
  box-sizing: border-box;
  overflow-y: auto;
}
.operation-records .list {
  padding: 0 10px 0 30px;
}
.operation-records .list .item {
  position: relative;
  padding: 10px;
  background: #fff;
  border-radius: 3px;
  margin-bottom: 5px;
}
.operation-records .list .item::before {
  content: "";
  display: block;
  width: 11px;
  height: 11px;
  border-radius: 50%;
  overflow: hidden;
  background: #E1E5E8;
  position: absolute;
  left: -21px;
  top: 15px;
  z-index: 2;
}
.operation-records .list .item::after {
  content: "";
  display: block;
  border-left: dashed 1px #E1E5E8;
  position: absolute;
  left: -16px;
  top: 24px;
  bottom: -24px;
  z-index: 1;
}
.operation-records .list .item:first-child::before {
  background: #09f;
}
.operation-records .list .item:last-child {
  border-bottom: none;
}
.operation-records .list .item:last-child::after {
  display: none;
}
.operation-records .list .item .row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}
.operation-records .list .item .row .creator {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}
.operation-records .list .item .row .creator .vd-ui-user-wrap {
  width: 100%;
}
.operation-records .list .item .row .time {
  flex-shrink: 0;
  white-space: nowrap;
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-left: 10px;
}
.operation-records .list .item .content {
  color: #000;
}
.page-null-data {
  padding: 100px;
  text-align: center;
}
.page-null-data > i {
  font-size: 32px;
  color: #09f;
}
.page-null-data > p {
  font-size: 14px;
  color: #999;
  margin-top: 10px;
}
/* 添加拜访计划 */
.trader-detail {
  margin-top: 5px;
}
.price-dw {
  display: block;
  width: 32px;
  line-height: 36px;
  text-align: center;
  background: #fff;
  position: absolute;
  right: 0;
  top: 0;
}
