package com.vedeng.logistics.strategy;

import com.vedeng.logistics.service.WarehouseGoodsOutDetailService;
import com.vedeng.logistics.service.WarehouseGoodsOutService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @description: 出库详情 工厂类
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/15 10:42
 **/
@Component
public class WarehouseGoodsOutDetailStrategyFactory {

    @Autowired
    Map<String, WarehouseGoodsOutDetailService> warehouseGoodsOutDetailServiceMap = new ConcurrentHashMap<>();

    public WarehouseGoodsOutDetailService getWarehouseGoodsOutDetailService(String serviceName){
        return warehouseGoodsOutDetailServiceMap.get(serviceName);
    }
}
