package com.vedeng.goods.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.goods.dto.CategoryQueryDto;
import com.vedeng.goods.dto.CategoryResultDto;
import com.vedeng.goods.service.BaseCategoryApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/18
 */
@ExceptionController
@RestController
@RequestMapping("/api/goods")
public class CategoryApi {

    @Autowired
    private BaseCategoryApiService baseCategoryApiService;

    @NoNeedAccessAuthorization
    @RequestMapping(value="/findThreeCategory")
    public R<?> findThreeCategory(@RequestBody  CategoryQueryDto categoryQueryDto){
        PageInfo<CategoryResultDto> pageInfo =  baseCategoryApiService.getCategorybyKeyword(categoryQueryDto);
        return R.success(pageInfo);
    }




}
