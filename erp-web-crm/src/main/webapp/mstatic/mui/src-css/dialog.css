.dialog-fade-enter-active {
  transition: all 0.15s ease-out;
}
.dialog-fade-leave-active {
  transition: all 0.15s ease-in;
}
.dialog-fade-enter,
.dialog-fade-leave-to {
  opacity: 0;
}
.dialog-move-enter-active {
  transition: all 0.15s ease-out;
}
.dialog-move-leave-active {
  transition: all 0.15s ease-in;
}
.dialog-move-enter,
.dialog-move-leave-to {
  opacity: 0;
  transform: translate3d(0, -30px, 0);
}
.mui-dialog-container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 2019;
  background-color: rgba(0, 0, 0, 0.6);
  text-align: center;
}
.mui-dialog-container::after {
  content: "";
  display: inline-block;
  height: 100%;
  width: 0;
  vertical-align: middle;
}
.mui-dialog-container .nui-custom-dislog-center {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.mui-dialog-wrapper {
  position: relative;
  border-radius: 12px;
  background-color: #fff;
  width: 300px;
  font-weight: 400;
  line-height: 1.5;
  overflow: hidden;
}
.mui-dialog-wrapper.success {
  background: linear-gradient(180deg, #dbffdb 0%, #ffffff 100%) no-repeat top #fff;
  background-size: 100% 62px;
}
.mui-dialog-wrapper.error {
  background: linear-gradient(180deg, #ffe5e5 0%, #ffffff 100%) no-repeat top #fff;
  background-size: 100% 62px;
}
.mui-dialog-wrapper.warn {
  background: linear-gradient(180deg, #ffede0 0%, #ffffff 100%) no-repeat top #fff;
  background-size: 100% 62px;
}
.mui-dialog-wrapper.info {
  background: linear-gradient(180deg, #c3e7ff 0%, #ffffff 100%) no-repeat top #fff;
  background-size: 100% 62px;
}
.mui-dialog-wrapper .mui-dialog-content {
  padding: 30px 20px;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-icon {
  margin-bottom: 20px;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-icon > i {
  font-size: 32px;
  height: 32px;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-icon > i.icon-info2 {
  color: #0099FF;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-icon > i.icon-yes2 {
  color: #13bf13;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-icon > i.icon-caution2 {
  color: #ff6600;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-icon > i.icon-error2 {
  color: #e64545;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-title {
  font-size: 16px;
  color: #000;
  font-weight: 700;
  margin-bottom: 20px;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-middle {
  font-size: 14px;
  color: #000;
}
.mui-dialog-wrapper .mui-dialog-button-choice {
  height: 50px;
  display: flex;
  border: none;
  border-top: solid 1px #E1E5E8;
  font-size: 14px;
}
.mui-dialog-wrapper .mui-dialog-button-choice .mui-dialog-button {
  flex: 1;
  min-width: 0;
  color: #000;
  line-height: 50px;
  border-right: solid 1px #E1E5E8;
  cursor: pointer;
}
.mui-dialog-wrapper .mui-dialog-button-choice .mui-dialog-button:last-child {
  border-right: none;
}
.mui-dialog-wrapper .mui-dialog-button-choice .mui-dialog-button.disabled {
  color: #CED2D9;
}
.mui-dialog-wrapper .mui-dialog-button-choice .mui-dialog-button.confirm {
  color: #09f;
}
.mui-dialog-wrapper .mui-dialog-button-choice .mui-dialog-button.delete {
  color: #E64545;
}
.mui-dialog-wrapper .mui-dialog-button-choice .mui-dialog-button.cancel {
  color: #333;
}
.msg-fork {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  cursor: pointer;
}
.msg-fork:hover {
  color: #333333;
}
.msg-fork:hover .icon-delete {
  color: #333333;
}
.msg-fork .icon-delete {
  font-size: 24px;
  color: #CCCCCC;
}
.msg-fork .icon-delete:hover {
  color: #333333;
}
.slide-dialog-default-footer {
  height: 53px;
  padding: 10px;
  background: #fff;
  border-top: solid 1px #EBEFF2;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 99;
}
.slide-dialog-default-footer .btn-cancel-flex {
  flex: 17;
  margin-right: 10px;
}
.slide-dialog-default-footer .btn-confirm-flex {
  flex: 25;
}
