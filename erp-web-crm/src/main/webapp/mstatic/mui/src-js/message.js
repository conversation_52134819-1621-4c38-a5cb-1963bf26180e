let messageVue = Vue.component('ui-message', {
    template: `<transition name='globalToast'>
        <div class='ui-global-toast' v-if='isShow'>
            <div class="ui-global-toast-inner" :class="'ui-message-' + type">
                <i class='vd-ui_icon' :class='icon'></i>
                <span v-html="message"></span>
            </div>
        </div>
    </transition>`,
    data() {
        return {
            isShow: false,
            type: 'success',
            message: '暂无提示弹框类型',
            duration: 3000,
            iconType: {
                'success': 'icon-yes2',
                'error': 'icon-error2',
                'warn': 'icon-caution2',
                'info': 'icon-info2'
            },
        }
    },
    computed: {
        icon(){
            return this.iconType[this.type];
        }
    },
    methods: {
        show() {
            this.isShow = true;
        },
        hide() {
            this.isShow = false;
        }
    }
})

let installMessageComponents = () => {
    const VdMsg = Vue.extend(messageVue);
        let vm = null;
        
        const showMessage = ({message, type, duration, callback}) => {
            //console.log(type,opt);
            return new Promise((resolve, reject) => {
                if(!vm){
                    vm = new VdMsg();
                    vm.$mount();
                    document.body.appendChild(vm.$el);
                }else{
                    vm.hide();
                    clearTimeout(showMessage.hideTimer,showMessage.showTimer);
                }
                
                vm.message = message || vm.message;
                vm.type = type || vm.type;
                vm.duration = duration || vm.duration;
    
                showMessage.showTimer = setTimeout(() => {
                    vm.show();
                });
                
                showMessage.hideTimer = setTimeout(() => {
                    vm.hide();
                    clearTimeout(showMessage.hideTimer,showMessage.showTimer);
                    callback && callback();
                    resolve();
                },vm.duration);
            });
        }
        
        const fn = (type,message,duration,callback) => {
            return Promise.resolve(showMessage({
                type,
                message,
                duration,
                callback
            }));
        };
    
        // const fn = (type,opt) => {
        //     return Promise.resolve(showMessage(type,opt));
        // };
        
        showMessage.success = fn.bind(null,'success');
        showMessage.error = fn.bind(null,'error')
        showMessage.warn = fn.bind(null,'warn')
        showMessage.info = fn.bind(null,'info')
    
        Vue.prototype.$message = showMessage;
}

installMessageComponents();