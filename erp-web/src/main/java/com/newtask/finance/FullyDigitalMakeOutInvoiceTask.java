package com.newtask.finance;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.constant.InvoiceApplyMethodEnum;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.enums.InvoiceApplyCheckRuleEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.finance.common.exception.InvoiceException;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.dto.OpenInvoiceResultDto;
import com.vedeng.erp.finance.enums.CheckChainEnum;
import com.vedeng.erp.finance.enums.SalesOpenInvoiceTypeEnum;
import com.vedeng.erp.finance.service.FullyDigitalInvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import com.vedeng.erp.finance.service.InvoiceCheckApiService;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 数电发票-开票定时任务
 * @date 2023/9/20 11:50
 */
@JobHandler("FullyDigitalMakeOutInvoiceTask")
@Component
@Slf4j
public class FullyDigitalMakeOutInvoiceTask extends AbstractJobHandler {

    @Autowired
    private InvoiceApplyApiService invoiceApplyApiService;
    @Autowired
    private FullyDigitalInvoiceApiService fullyDigitalInvoiceApiService;

    @Autowired
    private InvoiceCheckApiService invoiceCheckApiService;

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    // 排除的日志
    @Value("#{'${excluded.log.content.list}'.split(',')}")
    private List<String> excludedLogContentList;

    @Override
    public ReturnT<String> doExecute(String saleOrderNo) throws Exception {
        // 是否开启数电发票
        Boolean salesAutoInvoiceFlag = sysOptionDefinitionApiService.getInvoiceApplyButtonShow(InvoiceApplyCheckRuleEnum.SALES_ORDER_AUTO_INVOICE);
        if (!salesAutoInvoiceFlag) {
            log.info("财务未开启自动开关，执行结束");
            return ReturnT.SUCCESS;
        }

        boolean lock = RedissonLockUtils.tryLock(ErpConstant.INVOICING_OPEN_TASK,0,2, TimeUnit.HOURS);

        if (!lock) {
            log.info("定时任务加锁失败，触发开票,存在处理中的开票业务");
            return ReturnT.SUCCESS;
        }

        try {
            int pageSize = 1000;
            int pageNum = 1;
            // 拉取需要开票的开票申请
            while (true) {
                log.info("分页执行进度 {},{}", pageSize, pageNum);
                Page<Object> page = new Page<>(pageNum, pageSize);
                PageInfo<InvoiceApplyDto> invoiceApplyDtoPageInfo;
                if (StringUtils.isNotBlank(saleOrderNo)){
                    log.info("根据订单号 {} 获取待开票申请",saleOrderNo);
                    invoiceApplyDtoPageInfo = invoiceApplyApiService.getWaitInvoiceApply(page,saleOrderNo);
                }else {
                    invoiceApplyDtoPageInfo = invoiceApplyApiService.getWaitInvoiceApply(page,null);
                }

                // 调用开票
                invoiceApplyDtoPageInfo.getList().forEach(invoiceApplyDto -> {
                    Integer invoiceMethod = invoiceApplyDto.getApplyMethod();
                    if (!InvoiceApplyMethodEnum.TICKET_AND_FREIGH.getApplyMethodCode().equals(invoiceMethod)){
                        // 非票货同行时继续校验开票申请
                        InvoiceCheckRequestDto invoiceCheckRequestDto = getInvoiceCheckRequestDto(invoiceApplyDto);
                        InvoiceCheckResultDto invoiceCheckResultDto = invoiceCheckApiService.openCheck(invoiceCheckRequestDto);

                        if (!invoiceCheckResultDto.getSuccess()) {
                            return;
                        }
                    }

                    OpenInvoiceResultDto openInvoiceResultDto = fullyDigitalInvoiceApiService.openSaleInvoice(invoiceApplyDto, SalesOpenInvoiceTypeEnum.SALE_OPEN_INVOICE);
                    if (!openInvoiceResultDto.isSuccess()) {
                        String msg = openInvoiceResultDto.getMsg();
                        if (excludedLogContentList.stream().noneMatch(msg::contains)) {
                            log.error("数电发票开票定时任务异常{}", msg);
                        } else {
                            log.info("数电发票开票定时任务异常:{}", msg);
                        }
                    }
                });

                if (!invoiceApplyDtoPageInfo.isHasNextPage()) {
                    break;
                }

                pageNum = invoiceApplyDtoPageInfo.getNextPage();
                log.info("分页查询进度 {},{}", pageSize, pageNum);
            }
        } catch (InvoiceException e) {
            log.warn("数电发票开票定时任务InvoiceException异常:",e);
        } catch (ServiceException e) {
            log.warn("数电发票开票定时任务ServiceException异常:",e);
        } catch (Exception e) {
            log.error("数电发票开票定时任务异常:",e);
        } finally {
            RedissonLockUtils.unlock(ErpConstant.INVOICING_OPEN_TASK);
        }

        return ReturnT.SUCCESS;
    }

    private static InvoiceCheckRequestDto getInvoiceCheckRequestDto(InvoiceApplyDto invoiceApply) {
        InvoiceCheckRequestDto invoiceCheckRequestDto = new InvoiceCheckRequestDto();
        invoiceCheckRequestDto.setCheckChainEnum(CheckChainEnum.INVOICE_OPEN_SALES);
        invoiceCheckRequestDto.setInvoiceApplyId(invoiceApply.getInvoiceApplyId());
        invoiceCheckRequestDto.setInvoiceProperty(invoiceApply.getInvoiceProperty());
        invoiceCheckRequestDto.setType(invoiceApply.getType());
        invoiceCheckRequestDto.setRelatedId(invoiceApply.getRelatedId());
        invoiceCheckRequestDto.setInvoiceInfoType(invoiceApply.getInvoiceInfoType());
        invoiceCheckRequestDto.setInvoiceMessage(invoiceApply.getInvoiceMessage());
        invoiceCheckRequestDto.setDetailList(invoiceApply.getInvoiceApplyDetailDtoList().stream().map(invoiceApplyDetailDto -> {
            InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto invoiceCheckRequestDetailDto = new InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto();
            invoiceCheckRequestDetailDto.setInvoiceApplyDetailId(invoiceApplyDetailDto.getInvoiceApplyDetailId());
            invoiceCheckRequestDetailDto.setDetailGoodsId(invoiceApplyDetailDto.getDetailgoodsId());
            invoiceCheckRequestDetailDto.setPrice(invoiceApplyDetailDto.getPrice());
            invoiceCheckRequestDetailDto.setNum(invoiceApplyDetailDto.getNum());
            invoiceCheckRequestDetailDto.setTotalAmount(invoiceApplyDetailDto.getTotalAmount());
            return invoiceCheckRequestDetailDto;
        }).collect(Collectors.toList()));
        return invoiceCheckRequestDto;
    }
}
