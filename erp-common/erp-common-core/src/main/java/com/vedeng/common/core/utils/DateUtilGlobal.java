package com.vedeng.common.core.utils;

import com.common.constants.Contant;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/1/7
 */
public class DateUtilGlobal {

    public static Logger logger = LoggerFactory.getLogger(DateUtilGlobal.class);

    // 长日期格式
    public static String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 获取当前系统的日期
     *
     * @return
     */
    public static long sysTimeMillis() {
        return System.currentTimeMillis();
    }
    public static Date StringToDate(String time, String format) {
        Date res;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        try {
            res = simpleDateFormat.parse(time);
            return res;
        } catch (ParseException e) {
            logger.info("StringToDate ERROR，{}",time);
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    /**
     * 获取几天后的时间戳
     * <p>
     * Title: getDateAfter
     * </p>
     * <p>
     * Description:
     * </p>
     *
     * @param d
     * @param day
     * @return
     * <AUTHOR>
     * @date 2019年4月8日
     */
    public static long getDateAfter(Date d, int day) {
        Calendar now = Calendar.getInstance();
        now.setTime(d);
        now.set(Calendar.DATE, now.get(Calendar.DATE) + day);

        Date time = now.getTime();
        return time.getTime();
    }

    /**
     * 将长整型数字转换为日期格式的字符串
     *
     * @param time
     * @param format
     * @return
     */
    public static String convertString(long time, String format) {
        if (time > 0L) {
            if (StringUtils.isBlank(format)) {
                format = TIME_FORMAT;
            }
            SimpleDateFormat sf = new SimpleDateFormat(format);
            Date date = new Date(time);
            return sf.format(date);
        }
        return "";
    }

}
