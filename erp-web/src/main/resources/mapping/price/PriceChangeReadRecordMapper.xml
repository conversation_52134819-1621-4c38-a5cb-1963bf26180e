<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.price.dao.PriceChangeReadRecordMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.price.model.PriceChangeReadRecord" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 24 15:38:49 CST 2021.
    -->
    <id column="PRICE_CHANGE_READ_RECORED_ID" property="priceChangeReadRecoredId" jdbcType="BIGINT" />
    <result column="PRICE_CHANGE_DATE" property="priceChangeDate" jdbcType="VARCHAR" />
    <result column="READ_TIME" property="readTime" jdbcType="BIGINT" />
    <result column="READER_ID" property="readerId" jdbcType="BIGINT" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 24 15:38:49 CST 2021.
    -->
    delete from T_PRICE_CHANGE_READ_RECORED
    where PRICE_CHANGE_READ_RECORED_ID = #{priceChangeReadRecoredId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.vedeng.price.model.PriceChangeReadRecord" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 24 15:38:49 CST 2021.
    -->
    insert into T_PRICE_CHANGE_READ_RECORED (PRICE_CHANGE_READ_RECORED_ID, PRICE_CHANGE_DATE, 
      READ_TIME, READER_ID)
    values (#{priceChangeReadRecoredId,jdbcType=BIGINT}, #{priceChangeDate,jdbcType=VARCHAR}, 
      #{readTime,jdbcType=BIGINT}, #{readerId,jdbcType=BIGINT})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.price.model.PriceChangeReadRecord" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 24 15:38:49 CST 2021.
    -->
    update T_PRICE_CHANGE_READ_RECORED
    set PRICE_CHANGE_DATE = #{priceChangeDate,jdbcType=VARCHAR},
      READ_TIME = #{readTime,jdbcType=BIGINT},
      READER_ID = #{readerId,jdbcType=BIGINT}
    where PRICE_CHANGE_READ_RECORED_ID = #{priceChangeReadRecoredId,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 24 15:38:49 CST 2021.
    -->
    select PRICE_CHANGE_READ_RECORED_ID, PRICE_CHANGE_DATE, READ_TIME, READER_ID
    from T_PRICE_CHANGE_READ_RECORED
    where PRICE_CHANGE_READ_RECORED_ID = #{priceChangeReadRecoredId,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 24 15:38:49 CST 2021.
    -->
    select PRICE_CHANGE_READ_RECORED_ID, PRICE_CHANGE_DATE, READ_TIME, READER_ID
    from T_PRICE_CHANGE_READ_RECORED
  </select>
    <select id="getLatestReadRecordByUserId" resultType="com.vedeng.price.model.PriceChangeReadRecord">
      SELECT
          PRICE_CHANGE_READ_RECORED_ID,
          PRICE_CHANGE_DATE,
          READ_TIME,
          READER_ID
      FROM
          T_PRICE_CHANGE_READ_RECORED
          WHERE READER_ID = #{userId,jdbcType=INTEGER}
          ORDER BY READ_TIME DESC
          LIMIT 1
    </select>
</mapper>