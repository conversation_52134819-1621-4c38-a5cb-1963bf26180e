package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.BuyOrderRebateChargeApplyEntity;
import com.vedeng.erp.buyorder.dto.BuyOrderRebateChargeApplyDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface BuyorderRebateChargeApplyMapper {

    /**
     * delete by primary key
     *
     * @param buyOrderRebateChargeId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer buyOrderRebateChargeId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(BuyOrderRebateChargeApplyEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(BuyOrderRebateChargeApplyEntity record);

    /**
     * select by primary key
     *
     * @param buyOrderRebateChargeId primary key
     * @return object by primary key
     */
    BuyOrderRebateChargeApplyEntity selectByPrimaryKey(Integer buyOrderRebateChargeId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BuyOrderRebateChargeApplyEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BuyOrderRebateChargeApplyEntity record);

    /**
     * 根据条件查询 采购返利结算收款申请
     *
     * @param pageParam 查询参数
     * @return BuyOrderRebateChargeApplyDto
     */
    List<BuyOrderRebateChargeApplyDto> findByAll(BuyOrderRebateChargeApplyDto pageParam);

    /**
     * 查询 采购返利结算收款申请 详情信息
     *
     * @param buyOrderRebateChargeId 主键id
     * @return BuyOrderRebateChargeApplyDto
     */
    BuyOrderRebateChargeApplyDto findById(@Param("buyOrderRebateChargeId") Integer buyOrderRebateChargeId);

    /**
     * 更新 返利结算申请单 单据状态
     * @param rebateChargeId ID
     * @param status 状态
     */
    void updateStatus(@Param("rebateChargeId") Integer rebateChargeId, @Param("status") Integer status);
}