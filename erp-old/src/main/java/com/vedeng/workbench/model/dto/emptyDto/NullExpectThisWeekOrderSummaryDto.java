package com.vedeng.workbench.model.dto.emptyDto;

import com.vedeng.dwh.model.dto.DwhErpUserDto;
import com.vedeng.workbench.model.dto.ExpectThisWeekOrderSummaryDto;

import java.math.BigDecimal;

/**
 * 为了初始化空值防止报空指针错误
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2020/11/7 21:48.
 * @author: <PERSON><PERSON>.
 */
public class NullExpectThisWeekOrderSummaryDto extends ExpectThisWeekOrderSummaryDto {

    public NullExpectThisWeekOrderSummaryDto() {
        this.setHitRate(BigDecimal.ZERO);
        this.setLastWeekFailNum(0);
        this.setLastWeekSuccessNum(0);
        this.setName("默认");
        this.setThisWeekAmount(BigDecimal.ZERO);
        this.setUserInfo(new DwhErpUserDto());
        this.setThisWeekSuccessNum(0);
    }
}
