package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.finance.dto.PayVedengBankDto;
import com.vedeng.erp.finance.service.PayVedengBankApiService;
import com.vedeng.erp.kingdee.batch.common.enums.ChoosePayObjectEnum;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesDto;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto;
import com.vedeng.erp.kingdee.batch.dto.BatchCustomerFinanceDto;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchCapitalBillDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchCustomerFinanceDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveRefundBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveRefundEntryDto;
import com.vedeng.erp.kingdee.enums.KingDeePayBillUseTypeEnums;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 已结算 收款退款单 构造器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/24 16:59
 */
@Slf4j
@Service
public class SettledReceiptRefundBillProcessor extends BaseProcessor<BatchBankBillDto, KingDeeReceiveRefundBillDto> {
    private static final String BD_CUSTOMER = "BD_Customer"; //收款退款单

    @Autowired
    private BatchCustomerFinanceDtoMapper batchCustomerFinanceDtoMapper;

    @Autowired
    private PayVedengBankApiService payVedengBankApiService;

    @Autowired
    private BatchCapitalBillDtoMapper batchCapitalBillDtoMapper;

    @Autowired
    private BatchAfterSalesDtoMapper afterSalesMapper;
    //售后订单
    private final static Integer THREE = 3;//售后订单类型
    private final static Integer FIVETHREEFIVE = 535;//售后主体类型为销售

    private static final String FRECTYPE = "1"; //交易主体 1对公 2对私

    @Override
    public KingDeeReceiveRefundBillDto doProcess(BatchBankBillDto input, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("已结算 收款退款单 组装数据{}", JSONObject.toJSONString(input));
        BatchAfterSalesDto byAfterSalesNo = null;
        String logInfo = "已结算 收款退款单 ";
        if (THREE.equals(input.getOrderType())) {
            byAfterSalesNo = afterSalesMapper.findByAfterSalesNo(input.getOrderNo());
            if (Objects.nonNull(byAfterSalesNo) && FIVETHREEFIVE.equals(byAfterSalesNo.getSubjectType()) && judgeType(byAfterSalesNo)) {
                logInfo += "符合推送要求";
            } else {
                logInfo += "不符合推送要求，售后主体不是销售或售后类型不符合";
            }
        } else {
            logInfo += "不符合推送要求，不是售后单";
        }
        log.info(logInfo + "{}", JSONObject.toJSONString(byAfterSalesNo));
        KingDeeReceiveRefundBillDto receiveRefundBillDto = new KingDeeReceiveRefundBillDto();

        //单据内码
        receiveRefundBillDto.setFID("0");//默认为0
        //单据编号
        receiveRefundBillDto.setFBillNo(KingDeeConstant.SETTLE_ACCOUNT+input.getBankBillId().toString());
//        //单据日期
//        Date tranTime = input.getTranDate();
//        //转为yyyy-MM-dd格式
//        String tranTimeStr = DateUtil.formatDate(tranTime);

        receiveRefundBillDto.setFDATE(DateUtil.formatDateTime(input.getRealTrandatetime()));// VDERP-16088  款对接金蝶业务日期规则调整-精确到时分秒
        receiveRefundBillDto.setFQzokPzgsywdh(input.getOrderNo());
        BatchCustomerFinanceDto customerIdByTraderId = batchCustomerFinanceDtoMapper.getCustomerIdByTraderId(input.getTraderId());
        if (Objects.isNull(customerIdByTraderId)) {
            log.info("客户数据为空{}", JSONObject.toJSONString(input));
            return null;
        }
        log.info("客户数据数据{}", JSONObject.toJSONString(customerIdByTraderId));
        //往来单位类型
        receiveRefundBillDto.setFCONTACTUNITTYPE(BD_CUSTOMER);
        //往来单位
        receiveRefundBillDto.setFCONTACTUNIT(customerIdByTraderId.getTraderCustomerId().toString());
        //收款单位类型
        receiveRefundBillDto.setFRECTUNITTYPE(BD_CUSTOMER);
        //收款单位
        receiveRefundBillDto.setFRECTUNIT(customerIdByTraderId.getTraderCustomerId().toString());
        //交易流水号
        receiveRefundBillDto.setF_QZOK_LSH(input.getTranFlow());
        // 查询银行流水关联的所有资金流水
        List<BatchCapitalBillDto> capitalBillDtoList = batchCapitalBillDtoMapper.queryByTraderTypeAndBankBillId(KingDeeConstant.TWO, input.getBankBillId());
        if (CollUtil.isEmpty(capitalBillDtoList)) {
            log.error("已结算收款退款单银行流水推送金蝶processor执行失败，银行流水关联的资金流水不存在：{}", JSON.toJSONString(input));
            return null;
        }
        List<KingDeeReceiveRefundEntryDto> kingDeeReceiveRefundEntryDtos = new ArrayList<>();
        for (BatchCapitalBillDto batchCapitalBillDto : capitalBillDtoList) {
            KingDeeReceiveRefundEntryDto receiveRefundEntryDto = new KingDeeReceiveRefundEntryDto();
            //明细
            receiveRefundEntryDto.setFSETTLETYPEID(KingDeePayBillUseTypeEnums.WIRE_TRANSFER.getCode());
            switch (input.getBankTag()){
                case 4:
                    receiveRefundEntryDto.setFSETTLETYPEID(KingDeePayBillUseTypeEnums.ALI_PAY.getCode());
                    break;
                case 5:
                    receiveRefundEntryDto.setFSETTLETYPEID(KingDeePayBillUseTypeEnums.WE_CHAT_PAY.getCode());
                    break;
            }
            //收款金额
            receiveRefundEntryDto.setFRECTOTALAMOUNTFOR(batchCapitalBillDto.getAmount());
            //实退金额
            receiveRefundEntryDto.setFREFUNDAMOUNTFOR(batchCapitalBillDto.getAmount());
            //我方银行账号
            PayVedengBankDto payVedengBankDto = payVedengBankApiService.queryBankInfo(input.getBankTag());
            if (Objects.isNull(payVedengBankDto)) {
                log.info("查询 我方银行账号 信息为空{}",JSONObject.toJSONString(input));
                return null;
            }
            receiveRefundEntryDto.setFACCOUNTID(payVedengBankDto.getPayBankNo());
            //收款账户账号
            receiveRefundEntryDto.setFOPPOSITEBANKACCOUNT(input.getAccno2());
            //收款账户名称
            receiveRefundEntryDto.setFOPPOSITECCOUNTNAME(input.getAccName1());
            //收款账户开户行
            receiveRefundEntryDto.setFOPPOSITEBANKNAME(input.getCadBankNm());
            //收款账户联行号 VDERP-15926不传联行号
            receiveRefundEntryDto.setFCNAPS("");
            //原始订单号
            receiveRefundEntryDto.setF_QZOK_YSDDH("");
            //归属业务单号
            receiveRefundEntryDto.setF_QZOK_GSYWDH(batchCapitalBillDto.getOrderNo());
            //业务类型
            receiveRefundEntryDto.setF_QZOK_YWLX("销售售后付款");
            //收款方类型
            receiveRefundEntryDto.setFRecType(FRECTYPE.equals(batchCapitalBillDto.getTraderSubject().toString()) ? "0" : "1");
            kingDeeReceiveRefundEntryDtos.add(receiveRefundEntryDto);
        }


        receiveRefundBillDto.setFREFUNDBILLENTRY(kingDeeReceiveRefundEntryDtos);
        log.info("已结算 收款退款单 组装数据完毕{}", JSONObject.toJSONString(receiveRefundBillDto));
        return receiveRefundBillDto;
    }

    private boolean judgeType(BatchAfterSalesDto afterSalesDto) {
        Integer type = afterSalesDto.getType();
        if (Objects.nonNull(type)){
            if (ChoosePayObjectEnum.AFTER_SALES_AT.equals(type) || ChoosePayObjectEnum.AFTER_SALES_WX.equals(type) ||
                    ChoosePayObjectEnum.AFTER_SALES_HT_AT.equals(type) || ChoosePayObjectEnum.AFTER_SALES_FJ_AT.equals(type)){
                log.info("已结算 收款退款单 售后类型数据校验 不符合收款退款单流水要求{}", JSONObject.toJSONString(afterSalesDto));
                return false;
            }
            return true;
        }else{
            log.info("已结算 收款退款单 数据异常 售后类型为空{}", JSONObject.toJSONString(afterSalesDto));
            return false;
        }
    }
}
