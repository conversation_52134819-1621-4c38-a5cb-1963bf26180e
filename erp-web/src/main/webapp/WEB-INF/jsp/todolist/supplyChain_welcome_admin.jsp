<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<c:set var="title" value="供应链管理工作台" scope="application" />
<%@ include file="../common/common.jsp"%>
<link rel="stylesheet" href="<%=basePath%>static/css/workbench/base.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="<%=basePath%>static/css/workbench/management.css?rnd=${resourceVersionKey}" />
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .tab{
            font-size: 15px;
            color: black;
            text-align: center;
            position: relative;
            display: inline-block;
            width: 32%;
        }
        .subTab{
            color: #0099ff;
            text-decoration: underline;
        }
         .cannot-click {
             pointer-events: none;
             cursor: default;
         }
    </style>
</head>
<body>
<div class="management-wrapper">
    <div class="mw-left">
        <div class="common-box mb0">
            <div class="cb-title">
                订单交易待办项
            </div>
            <div class="cb-content">
                <div class="control-box">
                    <div class="control-box-title">
                        商品风控任务
                    </div>
                    <div class="control-box-content">
                        <div class="cbc-list">

                            <c:forEach var="item" items="${adminTodoDto.skuTodoMap}">
                                <div class="cbc-item">
                                    <div class="title">
                                        ${item.key.orgName}
                                    </div>
                                    <div class="content">
                                        <div class="c-left">
                                            <div class="circle-father-div J-circle-father-div">
                                                <div class="percent-div J-percent-div">
                                                    <div class="percent-left J-percent-left"></div>
                                                    <div class="percent-right wth0 J-percent-right"></div>
                                                </div>
                                                <div class="num">
                                                    <span class="J-percent-num">${item.value.percent}</span>%
                                                </div>
                                            </div>
                                        </div>
                                        <div class="c-right">
                                            <div>
                                                待办项：
                                                <a class="addtitle_dyn"
                                                   href="javascript:void(0);"
                                                   tabTitle='{"num":"riskCheckSkuTodoListCount<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"/ezadmin/list/list-goodsTodo?ASSIGNMENT_ASSISTANT_ID=${item.value.userIdList}&ASSIGNMENT_MANAGER_ID=${item.value.userIdList}",
											              "title":"商品信息完善"}'>
                                                    <font color="#0099ff">${item.value.skuTodoListCount == null ? 0 : item.value.skuTodoListCount}</font>
                                                </a>
                                            </div>
                                            <div>等候订单数：${item.value.skuTodoListCountGroupByOrder}</div>
                                            <div class="month-num">
                                                <span>本月新增：${item.value.addCurrentMonth}</span>
                                                <span>本月处理：${item.value.handlerCurrentMonth}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </c:forEach>

                        </div>
                    </div>
                </div>
                <div class="control-box">
                    <div class="control-box-title">
                        供应商风控任务
                    </div>
                    <div class="control-box-content">
                        <div class="cbc-list">

                            <c:forEach var="item" items="${adminTodoDto.traderTodoMap}">
                                <div class="cbc-item">
                                    <div class="title">
                                        ${item.key.orgName}
                                    </div>
                                    <div class="content">
                                        <div class="c-left">
                                            <div class="circle-father-div J-circle-father-div">
                                                <div class="percent-div J-percent-div">
                                                    <div class="percent-left J-percent-left"></div>
                                                    <div class="percent-right wth0 J-percent-right"></div>
                                                </div>
                                                <div class="num">
                                                    <span class="J-percent-num">${item.value.percent}</span>%
                                                </div>
                                            </div>
                                        </div>
                                        <div class="c-right">
                                            <div>待办项：
                                                <a class="addtitle_dyn"
                                                   href="javascript:void(0);"
                                                   tabTitle='{"num":"riskCheckTraderTodoListCount<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											              "link":"/ezadmin/list/list-supplierTodo?USER_LIST=${item.value.userIdList}",
											              "title":"供应商信息待完善"}'>
                                                    <font color="#0099ff">${item.value.traderSupplyTodoListCount == null ? 0 : item.value.traderSupplyTodoListCount}</font>
                                                </a>
                                            </div>
                                            <div>等候订单数：${item.value.traderSupplyTodoListCountGroupByOrder}</div>
                                            <div class="month-num">
                                                <span>本月新增：${item.value.addCurrentMonth}</span>
                                                <span>本月处理：${item.value.handlerCurrentMonth}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </c:forEach>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="common-box pt20">
            <div class="cb-title">
                采购预警
            </div>
            <div class="cb-content">
                <div class="goods-box">
                    <div class="goods-box-content">
                        <div class="gbc-list">
                            <div class="gbc-item">
                                <div class="task-title">
                                    采购任务
                                </div>
                                <div class="task-list" id="purchaseTaskDiv">
                                    <div class="task-item">
                                        <div class="item-num">
                                            <a class="addtitle_dyn can-click"
                                               href="javascript:void(0);"
                                               tabTitle='{"num":"purchaseTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/order/buyorder/indexPendingPurchase.do?tabFlag=0&orderAsistIdList=-1"}'>
                                                0
                                            </a>
                                        </div>
                                        <div class="item-name">总任务</div>
                                    </div>
                                    <div class="task-item">
                                        <div class="item-num">
                                            <a class="addtitle_dyn can-click"
                                               href="javascript:void(0);"
                                               tabTitle='{"num":"purchaseTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/order/buyorder/indexPendingPurchase.do?tabFlag=0&orderAsistIdList=-1"}'>
                                                0
                                            </a>
                                        </div>
                                        <div class="item-name">临期数</div>
                                    </div>
                                    <div class="task-item">
                                        <div class="item-num">
                                            <a class="addtitle_dyn can-click"
                                               href="javascript:void(0);"
                                               tabTitle='{"num":"purchaseTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/order/buyorder/indexPendingPurchase.do?tabFlag=0&orderAsistIdList=-1"}'>
                                                0
                                            </a>
                                        </div>
                                        <div class="item-name">逾期数</div>
                                    </div>
                                </div>
                            </div>
                            <div class="gbc-item">
                                <div class="task-title">
                                    催货任务
                                </div>
                                <div class="task-list" id="expeditingTaskDiv">
                                    <div class="task-item">
                                        <div class="item-num">
                                            <a class="addtitle_dyn can-click"
                                               href="javascript:void(0);"
                                               tabTitle='{"num":"expeditingGoodsTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/ezadmin/list/list-${ezUrlCg}?CREATOR=EZ_SESSION_USER_ID_KEY&_SEARCH_ITEM_DISPLAY=1&EXPEDITING_STATUS=2","title":"采购订单列表"}'>
                                                ${adminTodoDto.earlyWarningGoodsTaskToDoDto.totalExpeditingTaskNum ==null? 0 : adminTodoDto.earlyWarningGoodsTaskToDoDto.totalExpeditingTaskNum}
                                            </a>
                                        </div>
                                        <div class="item-name">总任务</div>
                                    </div>
                                    <div class="task-item">
                                        <div class="item-num">
                                            <a class="addtitle_dyn can-click"
                                               href="javascript:void(0);"
                                               tabTitle='{"num":"expeditingGoodsTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/ezadmin/list/list-${ezUrlCg}?CREATOR=EZ_SESSION_USER_ID_KEY&_SEARCH_ITEM_DISPLAY=1&EXPEDITING_STATUS=1","title":"采购订单列表"}'>
                                                ${adminTodoDto.earlyWarningGoodsTaskToDoDto.adventExpeditingTaskNum ==null? 0 : adminTodoDto.earlyWarningGoodsTaskToDoDto.adventExpeditingTaskNum}
                                            </a>
                                        </div>
                                        <div class="item-name">临期数</div>
                                    </div>
                                    <div class="task-item">
                                        <div class="item-num">
                                            <a class="addtitle_dyn can-click" href="javascript:void(0);"
                                               tabTitle='{"num":"expeditingGoodsTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/ezadmin/list/list-${ezUrlCg}?CREATOR=EZ_SESSION_USER_ID_KEY&_SEARCH_ITEM_DISPLAY=1&EXPEDITING_STATUS=2","title":"采购订单列表"}'>
                                                ${adminTodoDto.earlyWarningGoodsTaskToDoDto.overdueExpeditingTaskNum ==null? 0 : adminTodoDto.earlyWarningGoodsTaskToDoDto.overdueExpeditingTaskNum}
                                            </a>
                                        </div>
                                        <div class="item-name">逾期数</div>
                                    </div>
                                </div>
                            </div>
                            <div class="gbc-item">
                                <div class="task-title">
                                    备货任务
                                </div>
                                <div class="task-list">
                                    <div class="task-item">
                                        <div class="item-num">
                                            <a class="addtitle_dyn can-click"
                                               href="javascript:void(0);"
                                               tabTitle='{"num":"prepareStockTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/flash/prepare/list.do?warnLevel=-1&isAdmin=1&warnAll=1"}'>
                                                ${adminTodoDto.prepareStockTaskToDoDto.totalTaskNum == null? 0 : adminTodoDto.prepareStockTaskToDoDto.totalTaskNum}
                                            </a>
                                        </div>
                                        <div class="item-name">总任务</div>
                                    </div>
                                    <div class="task-item">
                                        <div class="item-num">
                                            <a class="addtitle_dyn can-click"
                                               href="javascript:void(0);"
                                               tabTitle='{"num":"prepareStockTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/flash/prepare/list.do?warnLevel=2&isAdmin=1"}'>
                                                ${adminTodoDto.prepareStockTaskToDoDto.firstLevelTaskNum == null? 0 : adminTodoDto.prepareStockTaskToDoDto.firstLevelTaskNum}
                                            </a>
                                        </div>
                                        <div class="item-name">一级预警</div>
                                    </div>
                                    <div class="task-item">
                                        <div class="item-num">
                                            <a class="addtitle_dyn can-click"
                                               href="javascript:void(0);"
                                               tabTitle='{"num":"prepareStockTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/flash/prepare/list.do?warnLevel=1&isAdmin=1"}'>
                                                ${adminTodoDto.prepareStockTaskToDoDto.secondLevelTaskNum == null? 0 : adminTodoDto.prepareStockTaskToDoDto.secondLevelTaskNum}
                                            </a>
                                        </div>
                                        <div class="item-name">二级预警</div>
                                    </div>
                                </div>
                            </div>
                            <div class="gbc-item">
                                <div class="task-title">
                                    催票任务
                                </div>
                                <div class="task-list" id="earlyWarningTicksTask">
                                    <div class="task-item">
                                        <div class="item-num">
                                            <a class="can-click"
                                               href="javascript:void(0);" onclick="showEarlyWarningTicksView(${adminTodoDto.earlyWarningTicksTaskTodoDto.userIdList})" >
                                                ${adminTodoDto.earlyWarningTicksTaskTodoDto.traderNum == null? 0 : adminTodoDto.earlyWarningTicksTaskTodoDto.traderNum}
                                            </a>
                                        </div>
                                        <div class="item-name">供应商数</div>
                                    </div>
                                    <div class="task-item">
                                        <div class="item-num">
                                            <a class="can-click"
                                               href="javascript:void(0);" onclick="showEarlyWarningTicksView(${adminTodoDto.earlyWarningTicksTaskTodoDto.userIdList})" >
                                                ${adminTodoDto.earlyWarningTicksTaskTodoDto.taskNum == null? 0 : adminTodoDto.earlyWarningTicksTaskTodoDto.taskNum}
                                            </a>
                                        </div>
                                        <div class="item-name">催票数</div>
                                    </div>
                                    <div class="task-item">
                                        <div class="item-num">
                                            <a class="can-click"
                                               href="javascript:void(0);" onclick="showEarlyWarningTicksView(${adminTodoDto.earlyWarningTicksTaskTodoDto.userIdList})" >
                                                ￥${adminTodoDto.earlyWarningTicksTaskTodoDto.totalTicksAmount == null? 0 : adminTodoDto.earlyWarningTicksTaskTodoDto.totalTicksAmount}
                                            </a>
                                        </div>
                                        <div class="item-name">催票金额</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="common-box">
            <div class="cb-title">
                日常管理任务项
            </div>
            <div class="cb-content">
                <c:if test="${isShow == 1}">
                      <div class="table-box">
                                    <div class="tb-title">
                                        <span>商品分级分档信息完善待办项</span>

                                        <select class="select" onchange="skuAndSpuChangeOrg(this.value)">
                                            <option value="-1">全部</option>
                                            <c:forEach items="${adminTodoDto.dailyManagementTodoDto.skuAndSpuSelectList}" var="org">
                                                <option value="${org.orgId}" <c:if test="${org.selected == true}">selected</c:if>>${org.orgName}</option>
                                            </c:forEach>
                                        </select>
                                    </div>
                                    <div class="tb-content">
                                        <div class="thead">
                                            <div class="thead-tr">
                                                <div class="thead-th">
                                                    待办事项
                                                </div>
                                                  <div class="thead-th">
                                                    S+级
                                                </div>
                                                  <div class="thead-th">
                                                    S级
                                                </div>
                                                <div class="thead-th">
                                                    A级
                                                </div>
                                                <div class="thead-th">
                                                    B级
                                                </div>
                                                <div class="thead-th">
                                                    C级
                                                </div>
                                                <div class="thead-th">
                                                    D级
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tbody" id="skuAndSpuTbody">
                                            <div class="tbody-tr">
                                                <div class="tbody-td">
                                                    商品信息完善
                                                </div>
                                                <div class="tbody-td">
                                                    <a class="addtitle_dyn can-click"
                                                       href="javascript:void(0);"
                                                       tabTitle='{"num":"maintainSkuAndSpuCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/goods/vgoods/list.do?buzTypeFromTodoList=9&goodsLevelFromTodoList=7&subordinateList=${adminTodoDto.dailyManagementTodoDto.skuAndSpuSubordinateList}"}'>
                                                        ${adminTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[7] == null ? 0 : adminTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[7]}
                                                    </a>
                                                </div>
                                                <div class="tbody-td">
                                                    <a class="addtitle_dyn can-click"
                                                       href="javascript:void(0);"
                                                       tabTitle='{"num":"maintainSkuAndSpuCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/goods/vgoods/list.do?buzTypeFromTodoList=9&goodsLevelFromTodoList=6&subordinateList=${adminTodoDto.dailyManagementTodoDto.skuAndSpuSubordinateList}"}'>
                                                        ${adminTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[6] == null ? 0 : adminTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[6]}
                                                    </a>
                                                </div>
                                                <div class="tbody-td">
                                                    <a class="addtitle_dyn can-click"
                                                       href="javascript:void(0);"
                                                       tabTitle='{"num":"maintainSkuAndSpuCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/goods/vgoods/list.do?buzTypeFromTodoList=9&goodsLevelFromTodoList=1&subordinateList=${adminTodoDto.dailyManagementTodoDto.skuAndSpuSubordinateList}"}'>
                                                        ${adminTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[1] == null ? 0 : adminTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[1]}
                                                    </a>
                                                </div>
                                                <div class="tbody-td can-click">
                                                    <a class="addtitle_dyn can-click"
                                                       href="javascript:void(0);"
                                                       tabTitle='{"num":"maintainSkuAndSpuCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/goods/vgoods/list.do?buzTypeFromTodoList=9&goodsLevelFromTodoList=2&subordinateList=${adminTodoDto.dailyManagementTodoDto.skuAndSpuSubordinateList}"}'>
                                                        ${adminTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[2] == null ? 0 : adminTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[2]}
                                                    </a>
                                                </div>
                                                <div class="tbody-td can-click">
                                                    <a class="addtitle_dyn can-click"
                                                       href="javascript:void(0);"
                                                       tabTitle='{"num":"maintainSkuAndSpuCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/goods/vgoods/list.do?buzTypeFromTodoList=9&goodsLevelFromTodoList=3&subordinateList=${adminTodoDto.dailyManagementTodoDto.skuAndSpuSubordinateList}"}'>
                                                        ${adminTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[3] == null ? 0 : adminTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[3]}
                                                    </a>
                                                </div>
                                                <div class="tbody-td can-click">
                                                    <a class="addtitle_dyn can-click"
                                                       href="javascript:void(0);"
                                                       tabTitle='{"num":"maintainSkuAndSpuCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/goods/vgoods/list.do?buzTypeFromTodoList=9&goodsLevelFromTodoList=4&subordinateList=${adminTodoDto.dailyManagementTodoDto.skuAndSpuSubordinateList}"}'>
                                                        ${adminTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[4] == null ? 0 : adminTodoDto.dailyManagementTodoDto.skuAndSpuCountGroupByGrade[4]}
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                </c:if>
                <div class="table-box">
                    <div class="tb-title">
                        <span>商品推送待办项</span>
                        <select class="select" onchange="pushChangeOrg(this.value)">
                            <option value="-1">全部</option>
                            <c:forEach items="${adminTodoDto.dailyManagementTodoDto.skuPushSelectList}" var="org">
                                <option value="${org.orgId}" <c:if test="${org.selected == true}">selected</c:if> >${org.orgName}</option>
                            </c:forEach>
                        </select>
                    </div>
                    <div class="tb-content">
                        <div class="thead">
                            <div class="thead-tr">
                                <div class="thead-th">
                                    待办事项
                                </div>
                                 <div class="thead-th">
                                                    S+级
                                </div>
                                  <div class="thead-th">
                                    S级
                                </div>
                                <div class="thead-th">
                                    A级
                                </div>
                                <div class="thead-th">
                                    B级
                                </div>
                                <div class="thead-th">
                                    C级
                                </div>
                                <div class="thead-th">
                                    D级
                                </div>
                            </div>
                        </div>

                        <div class="tbody" id="pushTbody">
                           <%-- <div class="tbody-tr" >
                                <div class="tbody-td">
                                    预计可发货时间维护
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainDeliveryTimeCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"${ezDomain}/ezlist/list/list.html?pageId=${ezIdArray[2]}&GOODS_LEVEL_NO=1&USER_LIST=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}",
											      "title":"预计可发货时间"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[1] == null ? 0 : adminTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[1]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainDeliveryTimeCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"${ezDomain}/ezlist/list/list.html?pageId=${ezIdArray[2]}&GOODS_LEVEL_NO=2&USER_LIST=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}",
											      "title":"预计可发货时间"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[2] == null ? 0 : adminTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[2]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainDeliveryTimeCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"${ezDomain}/ezlist/list/list.html?pageId=${ezIdArray[2]}&GOODS_LEVEL_NO=3&USER_LIST=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}",
											      "title":"预计可发货时间"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[3] == null ? 0 : adminTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[3]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainDeliveryTimeCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"${ezDomain}/ezlist/list/list.html?pageId=${ezIdArray[2]}&GOODS_LEVEL_NO=4&USER_LIST=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}",
											      "title":"预计可发货时间"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[4] == null ? 0 : adminTodoDto.dailyManagementTodoDto.deliveryTimeCountGroupByGrade[4]}
                                    </a>
                                </div>
                            </div>--%>

                            <div class="tbody-tr">
                                <div class="tbody-td">
                                    商品核价维护
                                </div>
                                 <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainPriceCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/price/basePriceMaintain/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&includeSkuNosStr=${adminTodoDto.dailyManagementTodoDto.priceSkuList[7]}"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[7] == null ? 0 : adminTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[7]}
                                    </a>
                                </div>
                                 <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainPriceCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/price/basePriceMaintain/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&includeSkuNosStr=${adminTodoDto.dailyManagementTodoDto.priceSkuList[6]}"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[6] == null ? 0 : adminTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[6]}
                                    </a>
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainPriceCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/price/basePriceMaintain/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&includeSkuNosStr=${adminTodoDto.dailyManagementTodoDto.priceSkuList[1]}"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[1] == null ? 0 : adminTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[1]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainPriceCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/price/basePriceMaintain/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&includeSkuNosStr=${adminTodoDto.dailyManagementTodoDto.priceSkuList[2]}"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[2] == null ? 0 : adminTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[2]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainPriceCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/price/basePriceMaintain/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&includeSkuNosStr=${adminTodoDto.dailyManagementTodoDto.priceSkuList[3]}"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[3] == null ? 0 : adminTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[3]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainPriceCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/price/basePriceMaintain/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&includeSkuNosStr=${adminTodoDto.dailyManagementTodoDto.priceSkuList[4]}"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[4] == null ? 0 : adminTodoDto.dailyManagementTodoDto.priceCountGroupByGrade[4]}
                                    </a>
                                </div>
                            </div>

                            <div class="tbody-tr">
                                <div class="tbody-td">
                                    贝登售后政策维护
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"aftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=12&goodsLevelFromTodoList=7"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[7] == null ? 0 : adminTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[7]}
                                    </a>
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"aftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=12&goodsLevelFromTodoList=6"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[6] == null ? 0 : adminTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[6]}
                                    </a>
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"aftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=12&goodsLevelFromTodoList=1"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[1] == null ? 0 : adminTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[1]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"aftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=12&goodsLevelFromTodoList=2"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[2] == null ? 0 : adminTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[2]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"aftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=12&goodsLevelFromTodoList=3"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[3] == null ? 0 : adminTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[3]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"aftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=12&goodsLevelFromTodoList=4"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[4] == null ? 0 : adminTodoDto.dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[4]}
                                    </a>
                                </div>
                            </div>

                            <div class="tbody-tr">
                                <div class="tbody-td">
                                    供应商售后政策维护
                                </div>
                                 <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"supplyAftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=13&goodsLevelFromTodoList=7"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[7] == null ? 0 : adminTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[7]}
                                    </a>
                                </div>
                                 <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"supplyAftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=13&goodsLevelFromTodoList=6"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[6] == null ? 0 : adminTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[6]}
                                    </a>
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"supplyAftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=13&goodsLevelFromTodoList=1"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[1] == null ? 0 : adminTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[1]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"supplyAftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=13&goodsLevelFromTodoList=2"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[2] == null ? 0 : adminTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[2]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"supplyAftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=13&goodsLevelFromTodoList=3"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[3] == null ? 0 : adminTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[3]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"supplyAftersalePolicyCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/aftersale/serviceStandard/index.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=13&goodsLevelFromTodoList=4"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[4] == null ? 0 : adminTodoDto.dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[4]}
                                    </a>
                                </div>
                            </div>

                            <div class="tbody-tr">
                                <div class="tbody-td">
                                    运营信息维护
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainOperationInfoCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/goods/vgoods/list.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=16&goodsLevelFromTodoList=7"}'>

                                        ${adminTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[7] == null ? 0 : adminTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[7]}
                                    </a>
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainOperationInfoCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/goods/vgoods/list.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=16&goodsLevelFromTodoList=6"}'>

                                        ${adminTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[6] == null ? 0 : adminTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[6]}
                                    </a>
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainOperationInfoCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/goods/vgoods/list.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=16&goodsLevelFromTodoList=1"}'>

                                        ${adminTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[1] == null ? 0 : adminTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[1]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainOperationInfoCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/goods/vgoods/list.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=16&goodsLevelFromTodoList=2"}'>

                                        ${adminTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[2] == null ? 0 : adminTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[2]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainOperationInfoCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/goods/vgoods/list.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=16&goodsLevelFromTodoList=3"}'>

                                        ${adminTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[3] == null ? 0 : adminTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[3]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"maintainOperationInfoCountGroupByGrade<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											      "link":"/goods/vgoods/list.do?subordinateList=${adminTodoDto.dailyManagementTodoDto.skuPushSubordinateList}&buzTypeFromTodoList=16&goodsLevelFromTodoList=4"}'>

                                        ${adminTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[4] == null ? 0 : adminTodoDto.dailyManagementTodoDto.operationInfoCountGroupByGrade[4]}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-box">
                    <div class="tb-title">
                        <span>同行单录入待办项</span>

                        <select id="receiptRecordChangeOrg" class="select" onchange="receiptRecordChangeOrg(this.value)">
                            <option value="-1">全部</option>
                            <c:forEach items="${adminTodoDto.dailyManagementTodoDto.receiptRecordSelectList}" var="org">
                                <option value="${org.orgId}" <c:if test="${org.selected == true}">selected</c:if>>${org.orgName}</option>
                            </c:forEach>
                        </select>
                    </div>
                    <div class="tb-content">
                        <div class="thead">
                            <div class="thead-tr">
                                <div class="thead-th">
                                    待办事项
                                </div>
                                <div class="thead-th">
                                    总待办
                                </div>
                                <div class="thead-th">
                                    紧急待办
                                </div>
                                <div class="thead-th">
                                    日常待办
                                </div>
                            </div>
                        </div>
                        <div class="tbody" id="receiptRecordTbody">
                            <div class="tbody-tr">
                                <div class="tbody-td">
                                    同行单数据录入
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"receiptRecordCountGroupByCategory<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/ezadmin/list/list-${ezIdArray[3]}?SUBORDINATELIST=${adminTodoDto.dailyManagementTodoDto.receiptRecordSubordinateList}","title":"同行单录入待办列表页"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.receiptRecordCountGroupByCategory[1] == null ? 0 : adminTodoDto.dailyManagementTodoDto.receiptRecordCountGroupByCategory[1]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"receiptRecordCountGroupByCategory<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/ezadmin/list/list-${ezIdArray[3]}?SUBORDINATELIST=${adminTodoDto.dailyManagementTodoDto.receiptRecordSubordinateList}&URGED_MAINTAIN_BATCH_INFO=1","title":"同行单录入待办列表页"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.receiptRecordCountGroupByCategory[2] == null ? 0 : adminTodoDto.dailyManagementTodoDto.receiptRecordCountGroupByCategory[2]}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"receiptRecordCountGroupByCategory<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/ezadmin/list/list-${ezIdArray[3]}?SUBORDINATELIST=${adminTodoDto.dailyManagementTodoDto.receiptRecordSubordinateList}&URGED_MAINTAIN_BATCH_INFO=0","title":"同行单录入待办列表页"}'>
                                        ${adminTodoDto.dailyManagementTodoDto.receiptRecordCountGroupByCategory[3] == null ? 0 : adminTodoDto.dailyManagementTodoDto.receiptRecordCountGroupByCategory[3]}
                                    </a>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-box">
                    <div class="tb-title">
                        <span>直发可确认收货待办项</span>
                    </div>
                    <div class="tb-content">
                        <div class="thead">
                            <div class="thead-tr">
                                <div class="thead-th">
                                    待办事项
                                </div>
                                <div class="thead-th">
                                    总订单数
                                </div>
                                <div class="thead-th">
                                    近30日订单数
                                </div>
                                <div class="thead-th">
                                    本月订单数
                                </div>
                            </div>
                        </div>
                        <div class="tbody" id="enableReceiveToDo">
                            <div class="tbody-tr">
                                <div class="tbody-td">
                                    直发可确认收货订单数
                                </div>
                                <div class="tbody-td">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"enableReceiveToDoDto<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/ezadmin/list/list-7ILK0W3lzBg?CREATOR=${userId}&ADDTIMESTR_START=&ADDTIMESTR_END=&DELIVERY_DIRECT=1&VALID_STATUS=1&ARRIVAL_STATUS=0%2C1&EXPRESS_ENABLE_RECEIVE=1&_SEARCH_ITEM_DISPLAY=1","title":"(订单流)采购订单列表"}'>
                                        ${adminTodoDto.enableReceiveToDoDto.totalNum == null ? 0 : adminTodoDto.enableReceiveToDoDto.totalNum}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"enableReceiveToDoDto<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/ezadmin/list/list-7ILK0W3lzBg?CREATOR=${userId}&ADDTIMESTR_START=${adminTodoDto.enableReceiveToDoDto.recent30DaysDate}&ADDTIMESTR_END=${adminTodoDto.enableReceiveToDoDto.nowDate}&DELIVERY_DIRECT=1&VALID_STATUS=1&ARRIVAL_STATUS=0%2C1&EXPRESS_ENABLE_RECEIVE=1&_SEARCH_ITEM_DISPLAY=1","title":"(订单流)采购订单列表"}'>
                                        ${adminTodoDto.enableReceiveToDoDto.recent30DaysNum == null ? 0 : adminTodoDto.enableReceiveToDoDto.recent30DaysNum}
                                    </a>
                                </div>
                                <div class="tbody-td can-click">
                                    <a class="addtitle_dyn can-click"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"enableReceiveToDoDto<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                                                      "link":"/ezadmin/list/list-7ILK0W3lzBg?CREATOR=${userId}&ADDTIMESTR_START=${adminTodoDto.enableReceiveToDoDto.firstOfMonth}&ADDTIMESTR_END=${adminTodoDto.enableReceiveToDoDto.nowDate}&DELIVERY_DIRECT=1&VALID_STATUS=1&ARRIVAL_STATUS=0%2C1&EXPRESS_ENABLE_RECEIVE=1&_SEARCH_ITEM_DISPLAY=1","title":"(订单流)采购订单列表"}'>
                                        ${adminTodoDto.enableReceiveToDoDto.currentMonthNum == null ? 0 : adminTodoDto.enableReceiveToDoDto.currentMonthNum}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="mw-right">
        <div class="message-total" style="width: 450px;">
            <div class="" style="width: 100%">
                <div class="item-right">
                    <input type="hidden" class="J-crm-task-url" value="${crmTodoTaskUrl}">
                    <tags:welcome_task/>
                </div>

                <div class="item-right">
                    <div style="  font-size: 16px;font-weight: bold;padding: 15px 20px;border-bottom: 1px solid #ebebeb;">
                        ERP（客户&订单）
                    </div>
                    <div class="item-r-content">
                        <ul class="task-list" id="messageDiv">
                            <li class="task-item tab">
                                <div class="ti-name">我的待办</div>
                                <div class="ti-num subTab" style="color: red;">
                                    <a class="addtitle_dyn can-click" style="color: red;"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"purchaseTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/review/workbench/remindReview.do?tabStatus=1"}'>
                                        ${adminTodoDto.reviewTaskToDoDto.remainReviewNum == null? 0 : adminTodoDto.reviewTaskToDoDto.remainReviewNum}
                                    </a>
                                </div>
                            </li>
                            <li class="task-item tab">
                                <div class="ti-name">我的申请</div>
                                <div class="ti-num subTab " style="color: red;">
                                    <a class="addtitle_dyn can-click" style="color: red;"
                                       href="javascript:void(0);"
                                       tabTitle='{"num":"purchaseTask<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"/review/workbench/remindReview.do?tabStatus=2"}'>
                                        ${adminTodoDto.reviewTaskToDoDto.submitUnfinishNum == null? 0 : adminTodoDto.reviewTaskToDoDto.submitUnfinishNum }
                                    </a>
                                </div>
                            </li>
                            <li class="task-item tab">
                                <div>未读消息</div>
                                <div class="ti-num subTab " style="color: red;">
                                    <div class="num " style="color: red;">
                                        <a class="addtitle_dyn can-click" style="color: red;"
                                           href="javascript:void(0);"
                                           tabTitle='{"num":"moreMessage<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
							                "link":"/system/message/index.do?isView=0"}'>
                                            ${adminTodoDto.messageTodoDto.unReadMessageCount}
                                        </a>
                                    </div>

                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="month-rank J-month-rank" style="width: 450px">
            <div class="rank-title">
                <span>商品风控任务处理月度排名</span>
                <span class="hc-icon icon-up J-slide-button"></span>
            </div>
            <div class="rank-content J-rank-content">
                <div class="rank-table">
                    <div class="rt-thead">
                        <div class="tr">
                            <div class="th number">排名</div>
                            <div class="th">组别</div>
                            <div class="th">处理人</div>
                            <div class="th">平均处理时长</div>
                        </div>
                    </div>
                    <div class="rt-tbody">
                        <c:forEach items="${adminTodoDto.rickSkuRankingDto}" var="ranking" varStatus="line">
                            <div class="tr">
                                <div class="td number">${line.count}</div>
                                <div class="td">${ranking.groupName}</div>
                                <div class="td">${ranking.handler}</div>
                                <div class="td">${ranking.avgDealTime}h</div>
                            </div>
                        </c:forEach>
                    </div>
                </div>
            </div>
        </div>
        <div class="month-rank J-month-rank" style="width: 450px;">
            <div class="rank-title">
                <span>采购任务处理月度排名</span>
                <span class="hc-icon icon-up J-slide-button"></span>
            </div>
            <div class="rank-content J-rank-content">
                <div class="rank-table" id="rankTable">
                    <div class="rt-thead">
                        <div class="tr">
                            <div class="th number">排名</div>
                            <div class="th">组别</div>
                            <div class="th">处理人</div>
                            <div class="th">平均处理时长</div>
                        </div>
                    </div>
                    <%--<div class="rt-tbody">
                        <div class="tr">
                            <div class="td number">1</div>
                            <div class="td">组别</div>
                            <div class="td">处理人</div>
                            <div class="td">300min</div>
                        </div>
                    </div>--%>
                </div>
            </div>
        </div>
    </div>
    <span style="display:none;"><a class="addtitle can-click" href="javascript:void(0);" id="openList"></a></span>
</div>
</body>

<script type="text/javascript">
    $(function() {

        // 获取百分比
        $('.J-percent-num').each(function() {
            percent($(this), $(this).text())
        })

        // 渲染百分比
        function percent(ele, percent) {
            // 超过100显示100
            if (percent > 100) {
                ele.parents('.J-circle-father-div').find('.J-percent-div').addClass('clip-auto')
                ele.parents('.J-circle-father-div').find('.J-percent-right').removeClass('wth0')
            } else if (percent > 50) {
                ele.parents('.J-circle-father-div').find('.J-percent-div').addClass('clip-auto')
                ele.parents('.J-circle-father-div').find('.J-percent-right').removeClass('wth0')
            }
            ele.parents('.J-circle-father-div').find('.J-percent-left').css('transform', 'rotate(' + 3.6 * percent + 'deg)')
            ele.text(percent)
        }

        $('.J-slide-button').click(function() {
            if ($(this).hasClass('down')) {
                $(this).removeClass('down')
                $(this).parents('.J-month-rank').children('.J-rank-content').slideDown()
            }else {
                $(this).addClass('down')
                $(this).parents('.J-month-rank').children('.J-rank-content').slideUp()
            }
        })


        dealwithPurchaseTaskAdmin();

        dealwithPurchaseRanking();
    })
    function showEarlyWarningTicksView(userIds){
        var random = Math.ceil(Math.random()*10000);
        if(userIds.length > 0){
            var userIdsString="";
            userIds.forEach(function(e){
                userIdsString = userIdsString+e+",";
            });
            var tital= "{\"num\":\"purchaseTask"+random+"\",\"link\":\"/flash/earlyWarningTicksTask/earlyWarningTicksTask.do?userIds="+escape(userIdsString)+"\"}";
            $("#openList").attr('tabTitle',tital);
            document.getElementById("openList").click()
        }
    }
    function dealwithPurchaseRanking() {
        $.ajax({
            type: "POST",
            async : true,
            url: "./dealwithPurchaseRanking.do",
            dataType:'json',
            success: function(data){
                if(data.code == '0'){

                    var rankingList = data.data;

                    if(rankingList == null || rankingList == undefined || rankingList == ''){
                        return;
                    }

                    for(var i = 0;i < rankingList.length;i++){

                        $("#rankTable").append("<div class=\"rt-tbody\">\n" +
                            "                        <div class=\"tr\">\n" +
                            "                            <div class=\"td number\">"+(i+1)+"</div>\n" +
                            "                            <div class=\"td\">"+rankingList[i].groupName+"</div>\n" +
                            "                            <div class=\"td\">"+rankingList[i].handler+"</div>\n" +
                            "                            <div class=\"td\">"+rankingList[i].avgDealTime+"h</div>\n" +
                            "                        </div>\n" +
                            "                    </div>")

                    }

                }else{
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }

    function dealwithPurchaseTaskAdmin() {
        var random_1 = Math.ceil(Math.random()*10000);
        var random_2 = Math.ceil(Math.random()*10000);
        var random_3 = Math.ceil(Math.random()*10000);
        $.ajax({
            type: "POST",
            async : true,
            url: "./dealwithPurchaseTaskAdmin.do",
            dataType:'json',
            success: function(data){
                if(data.code == '0'){

                    var purchaseInfoTotoDto = data.data;

                    if(purchaseInfoTotoDto ==  null || purchaseInfoTotoDto == undefined){
                        return;
                    }

                    //总任务数量
                    var totalTasks = purchaseInfoTotoDto.totalTasks == undefined ? 0 : purchaseInfoTotoDto.totalTasks;

                    //临期任务数
                    var adventTask = purchaseInfoTotoDto.adventTask == undefined ? 0 : purchaseInfoTotoDto.adventTask;

                    //逾期任务数
                    var overdueTask = purchaseInfoTotoDto.overdueTask == undefined ? 0 : purchaseInfoTotoDto.overdueTask;

                    var orderAssitIdList = purchaseInfoTotoDto.orderAssitIdList == undefined ? -1 : purchaseInfoTotoDto.orderAssitIdList;;

                    var divs = $("#purchaseTaskDiv .item-num");

                    divs.eq(0).find("a").eq(0).html(totalTasks);
                    divs.eq(0).find("a").eq(0).attr("tabTitle",'{"num":"purchaseTask'+random_1+'","link":"/order/buyorder/indexPendingPurchase.do?tabFlag=0&orderAsistIdList='+orderAssitIdList+'"}');

                    divs.eq(1).find("a").eq(0).html(adventTask);
                    divs.eq(1).find("a").eq(0).attr("tabTitle",'{"num":"purchaseTask'+random_2+'","link":"/order/buyorder/indexPendingPurchase.do?tabFlag=0&aging=1&orderAsistIdList='+orderAssitIdList+'"}');

                    divs.eq(2).find("a").eq(0).html(overdueTask);
                    divs.eq(2).find("a").eq(0).attr("tabTitle",'{"num":"purchaseTask'+random_3+'","link":"/order/buyorder/indexPendingPurchase.do?tabFlag=0&aging=2&orderAsistIdList='+orderAssitIdList+'"}');

                }else{
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }

    /**
     * 商品分级分档下拉框变换
     */
    function skuAndSpuChangeOrg(orgaId) {
        var random = Math.ceil(Math.random()*10000);
        $.ajax({
            type: "POST",
            url: "./skuAndSpuChangeOrg.do",
            data:{"orgaId":orgaId},
            dataType:'json',
            success: function(data){
                if(data.code == '0'){

                    var dailyManagementTodoDto = data.data;

                    var skuAndSpuSubordinateList = dailyManagementTodoDto.skuAndSpuSubordinateList;

                    for(var i = 1;i<=4;i++){

                        //商品等级数量
                        var skuAndSpuLevelNum = dailyManagementTodoDto.skuAndSpuCountGroupByGrade[i] == undefined ? 0 : dailyManagementTodoDto.skuAndSpuCountGroupByGrade[i];

                        var divs = $("#skuAndSpuTbody .tbody-tr").eq(0).find("div");

                        divs.eq(i).find("a").eq(0).html(skuAndSpuLevelNum);

                        divs.eq(i).find("a").eq(0).attr("tabTitle",
                            '{"num":"maintainSkuAndSpuCountGroupByGrade'+random+i+'",'
                            +'"link":"/goods/vgoods/list.do?buzTypeFromTodoList=9&goodsLevelFromTodoList='+i+'&subordinateList='+skuAndSpuSubordinateList+'"}');
                    }

                }else{
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }

    function pushChangeOrg(orgaId) {
        var random_1 = Math.ceil(Math.random()*10000);
        var random_2 = Math.ceil(Math.random()*10000);
        var random_3 = Math.ceil(Math.random()*10000);
        var random_4 = Math.ceil(Math.random()*10000);
        var random_5 = Math.ceil(Math.random()*10000);
        $.ajax({
            type: "POST",
            url: "./pushChangeOrg.do",
            data:{"orgaId":orgaId},
            dataType:'json',
            success: function(data){
                if(data.code == '0'){

                    var dailyManagementTodoDto = data.data;

                    var skuPushSubordinateList = dailyManagementTodoDto.skuPushSubordinateList;

                    for(var i = 1;i<=4;i++){
                        //设置预计可发货时间
                        var deliveryLevelNum = dailyManagementTodoDto.deliveryTimeCountGroupByGrade[i] == undefined ? 0 : dailyManagementTodoDto.deliveryTimeCountGroupByGrade[i];
                        var divs = $("#pushTbody .tbody-tr").eq(0).find("div");

                        divs.eq(i).find("a").eq(0).html(deliveryLevelNum);
                        divs.eq(i).find("a").eq(0).attr("tabTitle",
                                                '{"num":"maintainDeliveryTimeCountGroupByGrade'+random_1+i+'",'
                                                +'"link":"${ezDomain}/ezlist/list/list.html?pageId=${ezIdArray[2]}&GOODS_LEVEL_NO='+i+'&USER_LIST='+skuPushSubordinateList+'",'
                                                +'"title":"预计可发货时间"}');

                        //商品核价维护
                        var priceLevelNum = dailyManagementTodoDto.priceCountGroupByGrade[i] == undefined ? 0 : dailyManagementTodoDto.priceCountGroupByGrade[i];
                        var divs = $("#pushTbody .tbody-tr").eq(1).find("div");

                        var priceSkuList = dailyManagementTodoDto.priceSkuList[i] == undefined? "" : dailyManagementTodoDto.priceSkuList[i];

                        divs.eq(i).find("a").eq(0).html(priceLevelNum);
                        divs.eq(i).find("a").eq(0).attr("tabTitle",
                            '{"num":"maintainPriceCountGroupByGrade'+random_2+i+'",'
                            +'"link":"/price/basePriceMaintain/index.do?subordinateList='+skuPushSubordinateList+'&includeSkuNosStr='+priceSkuList+'"}');


                        //贝登售后政策维护
                        var aftersalePolicyNum = dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[i] == undefined ? 0 : dailyManagementTodoDto.aftersalePolicyCountGroupByGrade[i];
                        var divs = $("#pushTbody .tbody-tr").eq(2).find("div");

                        divs.eq(i).find("a").eq(0).html(aftersalePolicyNum);
                        divs.eq(i).find("a").eq(0).attr("tabTitle",
                            '{"num":"aftersalePolicyCountGroupByGrade'+random_3+i+'",'
                            +'"link":"/aftersale/serviceStandard/index.do?subordinateList='+skuPushSubordinateList+'&buzTypeFromTodoList=12&goodsLevelFromTodoList='+i+'"}');

                        //供应商售后政策维护
                        var supplyAftersalePolicyNum = dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[i] == undefined ? 0 : dailyManagementTodoDto.supplyAftersalePolicyCountGroupByGrade[i];
                        var divs = $("#pushTbody .tbody-tr").eq(3).find("div");

                        divs.eq(i).find("a").eq(0).html(supplyAftersalePolicyNum);
                        divs.eq(i).find("a").eq(0).attr("tabTitle",
                            '{"num":"supplyAftersalePolicyCountGroupByGrade'+random_4+i+'",'
                            + '"link":"/aftersale/serviceStandard/index.do?subordinateList='+skuPushSubordinateList+'&buzTypeFromTodoList=13&goodsLevelFromTodoList='+i+'"}');

                        //运营信息维护
                        var operationNum = dailyManagementTodoDto.operationInfoCountGroupByGrade[i] == undefined ? 0 : dailyManagementTodoDto.operationInfoCountGroupByGrade[i];
                        var divs = $("#pushTbody .tbody-tr").eq(4).find("div");

                        divs.eq(i).find("a").eq(0).html(operationNum);
                        divs.eq(i).find("a").eq(0).attr("tabTitle",
                            '{"num":"maintainOperationInfoCountGroupByGrade'+random_5+i+'",'
                            +'"link":"/goods/vgoods/list.do?subordinateList='+skuPushSubordinateList+'&buzTypeFromTodoList=16&goodsLevelFromTodoList='+i+'"}');

                    }

                }else{
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }



    function receiptRecordChangeOrg(orgaId) {
        var random_1 = Math.ceil(Math.random()*10000);
        $.ajax({
            type: "POST",
            url: "./receiptRecordChangeOrg.do",
            data:{"orgaId":orgaId},
            dataType:'json',
            success: function(data){
                if(data.code == '0'){

                    var dailyManagementTodoDto = data.data;

                    var orgIdstr =""
                    if (orgaId != '-1'){
                        orgIdstr = "&ORG_ID=" + orgaId
                    }

                 /*   var receiptRecordSubordinateList = dailyManagementTodoDto.receiptRecordSubordinateList;*/
                    for(var i = 1;i<=3;i++){
                        //设置预计可发货时间
                        var dreceiptRecordNum = dailyManagementTodoDto.receiptRecordCountGroupByCategory[i] == undefined ? 0 : dailyManagementTodoDto.receiptRecordCountGroupByCategory[i];
                        var divs = $("#receiptRecordTbody .tbody-tr").eq(0).find("div");

                        var URGED_MAINTAIN_BATCH_INFO = "";
                        if(i ==2){
                            URGED_MAINTAIN_BATCH_INFO = "&URGED_MAINTAIN_BATCH_INFO=1";
                        }
                        if(i ==3){
                            URGED_MAINTAIN_BATCH_INFO = "&URGED_MAINTAIN_BATCH_INFO=0";
                        }


                        divs.eq(i).find("a").eq(0).html(dreceiptRecordNum);
                        divs.eq(i).find("a").eq(0).attr("tabTitle",
                            '{"num":"receiptRecordCountGroupByCategory'+random_1+i+'",'
                            +'"link":"/ezadmin/list/list-${ezIdArray[3]}?IS_ADMIN=1'+orgIdstr + URGED_MAINTAIN_BATCH_INFO+'"'
                            +',"title":"同行单录入待办列表页"}');

                    }

                }else{
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }


    $(
        function initPage(){
            var orgId = $("#receiptRecordChangeOrg option:selected").val();
            receiptRecordChangeOrg(orgId);
        }
    )



</script>

</html>
