package com.vedeng.goods.model;

public class FirstEngageGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.FIRST_ENGAGE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer firstEngageId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.REGISTRATION_NUMBER_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer registrationNumberId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.GOODS_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer goodsType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.BRAND_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Byte brandType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.STANDARD_CATEGORY_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Byte standardCategoryType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.NEW_STANDARD_CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer newStandardCategoryId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.OLD_STANDARD_CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer oldStandardCategoryId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.EFFECTIVE_DAY_UNIT
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Byte effectiveDayUnit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.EFFECTIVE_DAYS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer effectiveDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.SORT_DAYS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer sortDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.CONDITION_ONE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer conditionOne;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.TEMPERATURE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String temperature;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.CHECK_AGAIN
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Byte checkAgain;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Byte status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.IS_DELETED
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Byte isDeleted;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Long addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Long modTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE.COMMENTS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    private String comments;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.FIRST_ENGAGE_ID
     *
     * @return the value of T_FIRST_ENGAGE.FIRST_ENGAGE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getFirstEngageId() {
        return firstEngageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.FIRST_ENGAGE_ID
     *
     * @param firstEngageId the value for T_FIRST_ENGAGE.FIRST_ENGAGE_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setFirstEngageId(Integer firstEngageId) {
        this.firstEngageId = firstEngageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.REGISTRATION_NUMBER_ID
     *
     * @return the value of T_FIRST_ENGAGE.REGISTRATION_NUMBER_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getRegistrationNumberId() {
        return registrationNumberId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.REGISTRATION_NUMBER_ID
     *
     * @param registrationNumberId the value for T_FIRST_ENGAGE.REGISTRATION_NUMBER_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setRegistrationNumberId(Integer registrationNumberId) {
        this.registrationNumberId = registrationNumberId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.GOODS_TYPE
     *
     * @return the value of T_FIRST_ENGAGE.GOODS_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getGoodsType() {
        return goodsType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.GOODS_TYPE
     *
     * @param goodsType the value for T_FIRST_ENGAGE.GOODS_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.BRAND_TYPE
     *
     * @return the value of T_FIRST_ENGAGE.BRAND_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Byte getBrandType() {
        return brandType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.BRAND_TYPE
     *
     * @param brandType the value for T_FIRST_ENGAGE.BRAND_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setBrandType(Byte brandType) {
        this.brandType = brandType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.STANDARD_CATEGORY_TYPE
     *
     * @return the value of T_FIRST_ENGAGE.STANDARD_CATEGORY_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Byte getStandardCategoryType() {
        return standardCategoryType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.STANDARD_CATEGORY_TYPE
     *
     * @param standardCategoryType the value for T_FIRST_ENGAGE.STANDARD_CATEGORY_TYPE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setStandardCategoryType(Byte standardCategoryType) {
        this.standardCategoryType = standardCategoryType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.NEW_STANDARD_CATEGORY_ID
     *
     * @return the value of T_FIRST_ENGAGE.NEW_STANDARD_CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getNewStandardCategoryId() {
        return newStandardCategoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.NEW_STANDARD_CATEGORY_ID
     *
     * @param newStandardCategoryId the value for T_FIRST_ENGAGE.NEW_STANDARD_CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setNewStandardCategoryId(Integer newStandardCategoryId) {
        this.newStandardCategoryId = newStandardCategoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.OLD_STANDARD_CATEGORY_ID
     *
     * @return the value of T_FIRST_ENGAGE.OLD_STANDARD_CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getOldStandardCategoryId() {
        return oldStandardCategoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.OLD_STANDARD_CATEGORY_ID
     *
     * @param oldStandardCategoryId the value for T_FIRST_ENGAGE.OLD_STANDARD_CATEGORY_ID
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setOldStandardCategoryId(Integer oldStandardCategoryId) {
        this.oldStandardCategoryId = oldStandardCategoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.EFFECTIVE_DAY_UNIT
     *
     * @return the value of T_FIRST_ENGAGE.EFFECTIVE_DAY_UNIT
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Byte getEffectiveDayUnit() {
        return effectiveDayUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.EFFECTIVE_DAY_UNIT
     *
     * @param effectiveDayUnit the value for T_FIRST_ENGAGE.EFFECTIVE_DAY_UNIT
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setEffectiveDayUnit(Byte effectiveDayUnit) {
        this.effectiveDayUnit = effectiveDayUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.EFFECTIVE_DAYS
     *
     * @return the value of T_FIRST_ENGAGE.EFFECTIVE_DAYS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getEffectiveDays() {
        return effectiveDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.EFFECTIVE_DAYS
     *
     * @param effectiveDays the value for T_FIRST_ENGAGE.EFFECTIVE_DAYS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setEffectiveDays(Integer effectiveDays) {
        this.effectiveDays = effectiveDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.SORT_DAYS
     *
     * @return the value of T_FIRST_ENGAGE.SORT_DAYS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getSortDays() {
        return sortDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.SORT_DAYS
     *
     * @param sortDays the value for T_FIRST_ENGAGE.SORT_DAYS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setSortDays(Integer sortDays) {
        this.sortDays = sortDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.CONDITION_ONE
     *
     * @return the value of T_FIRST_ENGAGE.CONDITION_ONE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getConditionOne() {
        return conditionOne;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.CONDITION_ONE
     *
     * @param conditionOne the value for T_FIRST_ENGAGE.CONDITION_ONE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setConditionOne(Integer conditionOne) {
        this.conditionOne = conditionOne;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.TEMPERATURE
     *
     * @return the value of T_FIRST_ENGAGE.TEMPERATURE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getTemperature() {
        return temperature;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.TEMPERATURE
     *
     * @param temperature the value for T_FIRST_ENGAGE.TEMPERATURE
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setTemperature(String temperature) {
        this.temperature = temperature;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.CHECK_AGAIN
     *
     * @return the value of T_FIRST_ENGAGE.CHECK_AGAIN
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Byte getCheckAgain() {
        return checkAgain;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.CHECK_AGAIN
     *
     * @param checkAgain the value for T_FIRST_ENGAGE.CHECK_AGAIN
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCheckAgain(Byte checkAgain) {
        this.checkAgain = checkAgain;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.STATUS
     *
     * @return the value of T_FIRST_ENGAGE.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.STATUS
     *
     * @param status the value for T_FIRST_ENGAGE.STATUS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.IS_DELETED
     *
     * @return the value of T_FIRST_ENGAGE.IS_DELETED
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Byte getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.IS_DELETED
     *
     * @param isDeleted the value for T_FIRST_ENGAGE.IS_DELETED
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setIsDeleted(Byte isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.ADD_TIME
     *
     * @return the value of T_FIRST_ENGAGE.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.ADD_TIME
     *
     * @param addTime the value for T_FIRST_ENGAGE.ADD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.CREATOR
     *
     * @return the value of T_FIRST_ENGAGE.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.CREATOR
     *
     * @param creator the value for T_FIRST_ENGAGE.CREATOR
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.MOD_TIME
     *
     * @return the value of T_FIRST_ENGAGE.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Long getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.MOD_TIME
     *
     * @param modTime the value for T_FIRST_ENGAGE.MOD_TIME
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.UPDATER
     *
     * @return the value of T_FIRST_ENGAGE.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.UPDATER
     *
     * @param updater the value for T_FIRST_ENGAGE.UPDATER
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE.COMMENTS
     *
     * @return the value of T_FIRST_ENGAGE.COMMENTS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getComments() {
        return comments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE.COMMENTS
     *
     * @param comments the value for T_FIRST_ENGAGE.COMMENTS
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setComments(String comments) {
        this.comments = comments;
    }
}