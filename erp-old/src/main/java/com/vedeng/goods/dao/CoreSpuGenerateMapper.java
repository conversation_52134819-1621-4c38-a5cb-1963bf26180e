package com.vedeng.goods.dao;

import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.goods.model.CoreSpuGenerateExample;
import java.util.List;

import com.vedeng.goods.model.dto.SkuExcelDataDto;
import org.apache.ibatis.annotations.Param;

public interface CoreSpuGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int countByExample(CoreSpuGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByExample(CoreSpuGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByPrimaryKey(Integer spuId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insert(CoreSpuGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insertSelective(CoreSpuGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    List<CoreSpuGenerate> selectByExample(CoreSpuGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    CoreSpuGenerate selectByPrimaryKey(Integer spuId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExampleSelective(@Param("record") CoreSpuGenerate record, @Param("example") CoreSpuGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExample(@Param("record") CoreSpuGenerate record, @Param("example") CoreSpuGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKeySelective(CoreSpuGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKey(CoreSpuGenerate record);

    /**
     * 获取数据导出需要的spu拥有的sku信息
     *
     * @param spuId
     * @return
     */
    List<SkuExcelDataDto> listOwnSkuForDataExport(Integer spuId);

    /**
     * 当sup更新为非医疗器械，首营ID置为NULL
     * @param record
     * @return
     */
    int updateApparatusByPrimaryKey(CoreSpuGenerate record);


    CoreSpuGenerate selectSpuInfoBySkuNo(@Param("skuNo") String skuNo);

}