package com.vedeng.customerbillperiod.service.impl;

import com.vedeng.customerbillperiod.constant.CustomerBillPeriodOverdueManageDetailTypeEnum;
import com.vedeng.customerbillperiod.dao.CustomerBillPeriodOverdueManagementDetailMapper;
import com.vedeng.customerbillperiod.dao.CustomerBillPeriodRiskManagementDetailMapper;
import com.vedeng.customerbillperiod.dto.RollbackCustomerBillPeriodManagementDetailDto;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodOverdueManagementDetail;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodRiskManagementDetail;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodManagementCodeCommonService;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: daniel
 * @Date: 2021/7/28 17 37
 * @Description:
 */
@Service
public class CustomerBillPeriodManagementCodeCommonServiceImpl implements CustomerBillPeriodManagementCodeCommonService {

    private static final Logger logger = LoggerFactory.getLogger(CustomerBillPeriodManagementCodeCommonServiceImpl.class);

    @Resource
    private CustomerBillPeriodRiskManagementDetailMapper customerBillPeriodRiskManagementDetailMapper;

    @Resource
    private CustomerBillPeriodOverdueManagementDetailMapper customerBillPeriodOverdueManagementDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateNegativeBillPeriodManagementCode(RollbackCustomerBillPeriodManagementDetailDto managementDetailDto) throws CustomerBillPeriodException {
        logger.info("生成负数的客户账期管理编码：{}",managementDetailDto.toString());

        List<CustomerBillPeriodOverdueManagementDetail> overdueManagementDetailListUnreturned =
                customerBillPeriodOverdueManagementDetailMapper.getOverdueManagementDetailListUnreturnedByOrderId(managementDetailDto.getCompanyId(), managementDetailDto.getCustomerId(), managementDetailDto.getOrderId());
        if (overdueManagementDetailListUnreturned.size() > 0) {
            //待生成负数账期逾期管理编码的金额
            BigDecimal toRepaymentAmountForOverdue = managementDetailDto.getAmount().abs();
            for (CustomerBillPeriodOverdueManagementDetail item : overdueManagementDetailListUnreturned){
                CustomerBillPeriodOverdueManagementDetail overdueManagementDetail = new CustomerBillPeriodOverdueManagementDetail();
                overdueManagementDetail.setBillPeriodOverdueManagementCode(item.getBillPeriodOverdueManagementCode());
                overdueManagementDetail.setCompanyId(item.getCompanyId());
                overdueManagementDetail.setCustomerId(item.getCustomerId());
                overdueManagementDetail.setType(managementDetailDto.getType());
                overdueManagementDetail.setSettlementPeriod(item.getSettlementPeriod());
                overdueManagementDetail.setRelatedId(managementDetailDto.getRelatedId());
                overdueManagementDetail.setParentManagementDetailId(item.getBillPeriodOverdueManagementDetailId());
                overdueManagementDetail.setAddTime(managementDetailDto.getAddTime());
                overdueManagementDetail.setBillPeriodUseDetailId(item.getBillPeriodUseDetailId());
                BigDecimal amount = item.getUnreturnedAmount().compareTo(toRepaymentAmountForOverdue) >=0 ? toRepaymentAmountForOverdue : item.getUnreturnedAmount();
                overdueManagementDetail.setAmount(amount.multiply(new BigDecimal("-1")));
                //保存负数账期逾期编码
                customerBillPeriodOverdueManagementDetailMapper.insertSelective(overdueManagementDetail);
                logger.info("保存负数的客户账期逾期管理编码：{}",overdueManagementDetail.toString());
                //同步更新账期编码的未还金额
                CustomerBillPeriodOverdueManagementDetail toUpdate = new CustomerBillPeriodOverdueManagementDetail();
                toUpdate.setBillPeriodOverdueManagementDetailId(item.getBillPeriodOverdueManagementDetailId());
                toUpdate.setUnreturnedAmount(item.getUnreturnedAmount().subtract(amount));
                toUpdate.setModTime(managementDetailDto.getAddTime());

                //如果是物流作废/订单退票，更新逾期金额时，判断逾期金额等于0时，同步重置其逾期时间为未逾期
                if (CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_EXPRESS_INVALID.getCode().equals(managementDetailDto.getType()) ||
                        CustomerBillPeriodOverdueManageDetailTypeEnum.AFTER_SALES_INVOICE.getCode().equals(managementDetailDto.getType())){

                    if (toUpdate.getUnreturnedAmount().compareTo(BigDecimal.ZERO) <= 0){
                        toUpdate.setOverdueDays(0);
                        toUpdate.setOverdueAmount(BigDecimal.ZERO);
                    }
                }
                customerBillPeriodOverdueManagementDetailMapper.updateByPrimaryKeySelective(toUpdate);
                logger.info("更新账期的逾期管理编码的未还金额：{}",toUpdate);

                if (toRepaymentAmountForOverdue.compareTo(item.getUnreturnedAmount()) <= 0){
                    break;
                }
                toRepaymentAmountForOverdue = toRepaymentAmountForOverdue.subtract(item.getUnreturnedAmount());
            }
        }

        //当售后退货、还款、作废物流时，同步生成负数的风险管理编码
        if (CustomerBillPeriodOverdueManageDetailTypeEnum.AFTER_SALES_RETURN.getCode().equals(managementDetailDto.getType()) ||
                CustomerBillPeriodOverdueManageDetailTypeEnum.REPAYMENT.getCode().equals(managementDetailDto.getType()) ||
                CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_EXPRESS_INVALID.getCode().equals(managementDetailDto.getType())){
            List<CustomerBillPeriodRiskManagementDetail> riskManagementDetailListUnreturned =
                    customerBillPeriodRiskManagementDetailMapper.getRiskManagementDetailListUnreturnedByOrderId(managementDetailDto.getCompanyId(),
                            managementDetailDto.getCustomerId(), managementDetailDto.getOrderId());
            if (riskManagementDetailListUnreturned.size() == 0) {
                return;
            }

            //待生成负数账期风险管理编码的金额
            BigDecimal toRepaymentAmountForRisk = managementDetailDto.getAmount().abs();
            for (CustomerBillPeriodRiskManagementDetail item : riskManagementDetailListUnreturned){
                CustomerBillPeriodRiskManagementDetail riskManagementDetail = new CustomerBillPeriodRiskManagementDetail();
                riskManagementDetail.setBillPeriodRiskManagementCode(item.getBillPeriodRiskManagementCode());
                riskManagementDetail.setCompanyId(item.getCompanyId());
                riskManagementDetail.setCustomerId(item.getCustomerId());
                riskManagementDetail.setType(managementDetailDto.getType());
                riskManagementDetail.setRelatedId(managementDetailDto.getRelatedId());
                riskManagementDetail.setSettlementPeriod(item.getSettlementPeriod());
                riskManagementDetail.setBillPeriodUseDetailId(item.getBillPeriodUseDetailId());
                riskManagementDetail.setParentManagementDetailId(item.getBillPeriodRiskManagementDetailId());
                riskManagementDetail.setAddTime(managementDetailDto.getAddTime());
                BigDecimal amountForRisk = toRepaymentAmountForRisk.compareTo(item.getUnreturnedAmount()) >= 0 ? item.getUnreturnedAmount() : toRepaymentAmountForRisk;
                riskManagementDetail.setAmount(amountForRisk.multiply(new BigDecimal("-1")));
                //保存负数风险管理编码
                customerBillPeriodRiskManagementDetailMapper.insertSelective(riskManagementDetail);
                logger.info("保存负数的风险管理编码：{}",riskManagementDetail.toString());

                //同步更新风险管理编码的未还金额
                CustomerBillPeriodRiskManagementDetail toUpdateForRisk = new CustomerBillPeriodRiskManagementDetail();
                toUpdateForRisk.setBillPeriodRiskManagementDetailId(item.getBillPeriodRiskManagementDetailId());
                toUpdateForRisk.setUnreturnedAmount(item.getUnreturnedAmount().subtract(amountForRisk));
                toUpdateForRisk.setModTime(managementDetailDto.getAddTime());
                customerBillPeriodRiskManagementDetailMapper.updateByPrimaryKeySelective(toUpdateForRisk);
                logger.info("更新账期风险管理编码的未还金额：{}",toUpdateForRisk.toString());

                if (toRepaymentAmountForRisk.compareTo(item.getUnreturnedAmount()) <= 0){
                    return;
                }
                toRepaymentAmountForRisk = toRepaymentAmountForRisk.subtract(item.getUnreturnedAmount());
            }
        }
    }


    /**
     * 生成账期管理编码
     * ZQ-VS2021231231-P-20210729-0001
     * @param companyId 公司id
     * @param customerId 客户id
     * @param orderNo 订单号，当编码生成类型为售后、还款时，订单号为空；
     * @param orderId 订单id
     * @param settlementType 结算方式，0：N编码，1：H编码，2：P编码
     * @return 编码
     */
    @Override
    public String customerBillPeriodManagementCodeGenerator(Integer companyId, Long customerId, String orderNo, Long orderId, Integer settlementType){
        String billPeriodManagementCode;
        String currentDate = LocalDate.now().toString("yyyyMMdd");
        int number = 1;
        String billPeriodManagementCodeFlag;
        if (settlementType > 0){
            billPeriodManagementCodeFlag = settlementType == 1 ? "H" : "P";
            billPeriodManagementCode = customerBillPeriodOverdueManagementDetailMapper.geNewestBillPeriodManagementCodeByOrderId(companyId, customerId,orderId);
        } else {
            billPeriodManagementCodeFlag = "N";
            billPeriodManagementCode = customerBillPeriodRiskManagementDetailMapper.geNewestBillPeriodManagementCodeByOrderId(companyId,customerId, orderId);
        }
        if (StringUtils.isNotBlank(billPeriodManagementCode)){
            String[] billPeriodManagementCodeArray = billPeriodManagementCode.split("-");
            if (currentDate.equals(billPeriodManagementCodeArray[3])){
                number = Integer.parseInt(billPeriodManagementCodeArray[4]) + 1;
            }
            orderNo = billPeriodManagementCode.split("-")[1];
            billPeriodManagementCodeFlag = billPeriodManagementCodeArray[2];
        }
        String numberStr = String.format("%0" + 4 + "d",number);
        billPeriodManagementCode = "ZQ-" + orderNo + "-" + billPeriodManagementCodeFlag + "-" + currentDate + "-" + numberStr;
        return billPeriodManagementCode;
    }


    /**
     * 账期管理编码尾部数字自动加一
     * @param code 原账期管理编码
     * @return 加一后的账期管理编码
     */
    @Override
    public String billPeriodManagementCodeIncrease(String code){
        String[] codeArray = code.split("-");
        String number = codeArray[codeArray.length - 1];
        String numberIncreased = String.format("%0" + 4 + "d",Integer.parseInt(number) + 1);
        return code.substring(0,code.length()-4) + numberIncreased;
    }

}
