package com.vedeng.erp.buyorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.buyorder.common.constant.BuyOrderConstant;
import com.vedeng.erp.buyorder.common.enums.SpuTypeEnum;
import com.vedeng.erp.buyorder.domain.entity.*;
import com.vedeng.erp.buyorder.domain.vo.PurchaseDeliveryDirectBatchDetailVo;
import com.vedeng.erp.buyorder.domain.vo.PurchaseDeliveryDirectBatchInfoVo;
import com.vedeng.erp.buyorder.dto.BuyOrderExpressDetailDto;
import com.vedeng.erp.buyorder.dto.PurchaseDeliveryDirectAttachmentDto;
import com.vedeng.erp.buyorder.mapper.*;
import com.vedeng.erp.buyorder.service.NewBuyorderPeerListService;
import com.vedeng.infrastructure.file.domain.Attachment;
import com.vedeng.infrastructure.file.mapper.AttachmentMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.wms.dto.PurchaseDeliveryDirectBatchDetailDto;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 直发同行单服务
 *
 * <AUTHOR>
 * @date 2022/4/13 9:38
 **/
@Slf4j
@Service
public class NewBuyorderPeerListServiceImpl implements NewBuyorderPeerListService {

    /**
     * oss文档路径
     */
    @Value("${oss_file_path}")
    private String ossFilePath;

    @Value("${api_http}")
    protected String api_http;

    /**
     * OSS地址
     */
    @Value("${oss_url}")
    private String ossUrl;

    @Resource
    private BuyorderMapper newBuyorderMapper;

    @Autowired
    private AttachmentMapper buyorderAttachmentMapper;

    @Resource
    private PurchaseDeliveryDirectBatchDetailMapper purchaseDeliveryDirectBatchDetailMapper;

    @Resource
    private PurchaseDeliveryDirectBatchInfoMapper purchaseDeliveryDirectBatchInfoMapper;

    @Resource
    private ExpressMapper buyorderExpressMapper;

    @Resource
    private com.vedeng.system.dao.AttachmentMapper attachmentMapper;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;


    @Override
    public List<PeerListBuyorderGoods>  getNeedEditBuyGoods(Integer expressId) {

        log.info("getNeedEditBuyGoods 入参：{}", JSONObject.toJSONString(expressId));
        if (Objects.isNull(expressId)) {
            log.error("传递的快递参数错误:{}",JSONObject.toJSONString(expressId));
            throw new ServiceException("传递的快递参数错误");
        }
        List<PeerListBuyorderGoods> needEditBuyGoods = newBuyorderMapper.getNeedEditBuyGoods(expressId);
        // todo 过滤已经提交的同行单的信息
        needEditBuyGoods = getNeedPeerListBuyorderGoods(needEditBuyGoods);
        List<PeerListBuyorderGoods> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(needEditBuyGoods)) {
            needEditBuyGoods.stream().forEach(c->{
                // 医疗器械 根据spu类型 非医疗器械的看 是否管理贝登追溯码 是的化 设备分行
                if (!"0".equals(c.getMedicalInstrumentCatalogIncluded())&&SpuTypeEnum.EQUIPMENT.getCode().equals(c.getSpuType())||"0".equals(c.getMedicalInstrumentCatalogIncluded())&&c.getIsManageVedengCode()!=null&&c.getIsManageVedengCode().equals(1)&&SpuTypeEnum.EQUIPMENT.getCode().equals(c.getSpuType())) {
                    // 设备按单台进行分行
                    for (int i = 0; i < c.getDnum(); i++) {
                        PeerListBuyorderGoods peerListBuyorderGoods = new PeerListBuyorderGoods();
                        BeanUtils.copyProperties(c, peerListBuyorderGoods);
                        peerListBuyorderGoods.setDnum(1);
                        result.add(peerListBuyorderGoods);
                    }
                } else {
                    result.add(c);
                }

            });
        }
        return result;
    }

    /**
     * 或取其中的还需要的，check时也需要
     * @param needEditBuyGoods
     * @return
     */
    private List<PeerListBuyorderGoods> getNeedPeerListBuyorderGoods(List<PeerListBuyorderGoods> needEditBuyGoods) {

        log.info("getNeedPeerListBuyorderGoods 入参：{}", JSON.toJSONString(needEditBuyGoods));
        if (!CollectionUtils.isEmpty(needEditBuyGoods)) {
            List<Integer> expressDetailIds = needEditBuyGoods.stream().map(PeerListBuyorderGoods::getExpressDetailId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(expressDetailIds)) {
                TPurchaseDeliveryDirectBatchDetailExample query = new TPurchaseDeliveryDirectBatchDetailExample();
                query.createCriteria().andExpressDetailIdIn(expressDetailIds);
                List<PurchaseDeliveryDirectBatchDetail> oldDeliveryDirectBatchDetails = purchaseDeliveryDirectBatchDetailMapper.selectByExample(query);
                if (!CollectionUtils.isEmpty(oldDeliveryDirectBatchDetails)) {
                    // 根据物流明细id进行分组
                    Map<Integer, List<PurchaseDeliveryDirectBatchDetail>> purchaseDeliveryDirectBatchDetail2Map = oldDeliveryDirectBatchDetails.stream().collect(Collectors.groupingBy(PurchaseDeliveryDirectBatchDetail::getExpressDetailId));

                    // 针对每一个物流明细id里的商品进行应收物品的计算 k1 是否引用并行流
                    needEditBuyGoods.forEach(c->{
                        List<PurchaseDeliveryDirectBatchDetail> hadDetails = purchaseDeliveryDirectBatchDetail2Map.get(c.getExpressDetailId());
                        if (!CollectionUtils.isEmpty(hadDetails)) {
                            Map<String, List<PurchaseDeliveryDirectBatchDetail>> hadDetails2SkuMap = hadDetails.stream().collect(Collectors.groupingBy(PurchaseDeliveryDirectBatchDetail::getSku));
                            List<PurchaseDeliveryDirectBatchDetail> purchaseDeliveryDirectBatchDetails = hadDetails2SkuMap.get(c.getSku());
                            if (!CollectionUtils.isEmpty(purchaseDeliveryDirectBatchDetails)) {
                                log.info("计算随货同行单的数量 tPurchaseDeliveryDirectBatchDetails:{},PeerListBuyorderGoods:{}", JSON.toJSONString(purchaseDeliveryDirectBatchDetails), JSON.toJSONString(c));
                                int hadEditNum = purchaseDeliveryDirectBatchDetails.stream().mapToInt(num -> num.getArrivalCount() >= 0 ? num.getArrivalCount() : 0).sum();
                                int oldNum = c.getOldNum() == null ? 0 : c.getOldNum();
                                int needNum = oldNum - hadEditNum;
                                if (needNum < 0) {
                                    log.error("此物流明细的收货数量和以有同行单的数量不匹配：PeerListBuyorderGoods:{}，oldNum:{},hadEditNum:{}，tPurchaseDeliveryDirectBatchDetails:{}", JSON.toJSONString(c),oldNum,hadEditNum, JSON.toJSONString(purchaseDeliveryDirectBatchDetails));
                                }
                                c.setDnum(needNum);
                                c.setOldNum(needNum);
                            }
                        }

                    });
                    // 还需要补充同行单数据的
                    needEditBuyGoods = needEditBuyGoods.stream().filter(c -> c.getDnum() > 0).collect(Collectors.toList());
                }
            }
        }
        log.info("返回结果：{}",JSON.toJSONString(needEditBuyGoods));
        return needEditBuyGoods;
    }

    /**
     * 数据校验
     * @param data 页面传递的数据
     *             0 空，90 91 92 必填项 ，102 序列号重复 1 原数据他人修改，幂等性问题 2 收货数量超出限制 200 成功
     */
    @Override
    public void checkDetailsExcel(List<PurchaseDeliveryDirectBatchDetailVo> data) {
        //  相同的物流id下的数量相加是否满足 代发数据
        if (CollectionUtils.isEmpty(data)) {
            throw new ServiceException(0, "数据为空");
        }
        // 必填项校验 “耗材”/“试剂”时，必填项为：收货数量、生产批号/序列号、生效日期、失效日期； “设备”/“配件”时，必填项为：收货数量、生产批号/序列号  非医疗器械的 根据sku的2个字段判断
        for (PurchaseDeliveryDirectBatchDetailVo item : data) {
            baseDataCheck(item);
        }
        // 序列号重复校验 当产品为设备的时候
        /*List<String> batchNumbers = data.stream().filter(c->c.getUnionSequence().equals(1)).map(PurchaseDeliveryDirectBatchDetailVo::getBatchNumber).collect(Collectors.toList());
        List<String> distinctString = batchNumbers.stream().distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(batchNumbers) && distinctString.size() < batchNumbers.size()) {
            // 重复
            List<String> collect1 = batchNumbers.stream()
                    .collect(Collectors.toMap(e -> e, e -> 1, (a, b) -> a + b)) // 获得元素出现频率的 Map，键为元素，值为元素出现的次数
                    .entrySet().stream() // Set<Entry>转换为Stream<Entry>
                    .filter(entry -> entry.getValue() > 1) // 过滤出元素出现次数大于 1 的 entry
                    .map(entry -> entry.getKey()) // 获得 entry 的键（重复元素）对应的 Stream
                    .collect(Collectors.toList());// 转化为 List

            throw new ServiceException(102,"生产批次号/序列号存在重复 "+collect1.stream().collect(Collectors.joining(",")));
        }
        if (!CollectionUtils.isEmpty(distinctString)) {
            TPurchaseDeliveryDirectBatchDetailExample query = new TPurchaseDeliveryDirectBatchDetailExample();
            // 只争对设别类需要唯一的
            query.createCriteria().andBatchNumberIn(distinctString).andUnionSequenceEqualTo(1);
            List<PurchaseDeliveryDirectBatchDetail> purchaseDeliveryDirectBatchDetails = purchaseDeliveryDirectBatchDetailMapper.selectByExample(query);
            if (!CollectionUtils.isEmpty(purchaseDeliveryDirectBatchDetails)) {
                // 序列号重复校验
                String  repeatBatchNumber = purchaseDeliveryDirectBatchDetails.stream().map(PurchaseDeliveryDirectBatchDetail::getBatchNumber).collect(Collectors.joining(","));
                throw new ServiceException(102,"生产批次号/序列号存在重复："+repeatBatchNumber);
            }
        }*/


        // 数量校验
        Map<Integer, List<PurchaseDeliveryDirectBatchDetailVo>> collect = data.stream().collect(Collectors.groupingBy(PurchaseDeliveryDirectBatchDetailVo::getExpressDetailId));
        List<Integer> expressDetailIds = new ArrayList<>(collect.keySet());
        if (!CollectionUtils.isEmpty(expressDetailIds)) {
            List<PeerListBuyorderGoods> detailsByExpressDetailId = newBuyorderMapper.getDetailsByExpressDetailId(expressDetailIds);
            // 获取此时还需要的填写的信息
            detailsByExpressDetailId = getNeedPeerListBuyorderGoods(detailsByExpressDetailId);
            if (CollectionUtils.isEmpty(detailsByExpressDetailId)) {
                log.info("此同行单数据已更新请刷新页面重新填写:{}",detailsByExpressDetailId);
                throw new ServiceException("此同行单数据已更新请刷新页面重新填写");
            }
            Map<Integer, List<PeerListBuyorderGoods>> newData = detailsByExpressDetailId.stream().collect(Collectors.groupingBy(PeerListBuyorderGoods::getExpressDetailId));

            for (Integer a : expressDetailIds) {
                List<PurchaseDeliveryDirectBatchDetailVo> purchaseDeliveryDirectBatchDetailVos = collect.get(a);
                List<PeerListBuyorderGoods> newDa = newData.get(a);
                if (CollectionUtils.isEmpty(newDa)) {
                    log.info("同行单明细为空:{}",a);
                    throw new ServiceException(1,"此同行单数据已更新请刷新页面重新填写");
                }
                Integer oldNum = newDa.get(0).getOldNum() == null ? 0 : newDa.get(0).getOldNum();
                Integer oldNumVo = purchaseDeliveryDirectBatchDetailVos.get(0).getOldNum() == null ? 0 : purchaseDeliveryDirectBatchDetailVos.get(0).getOldNum();
                if (!oldNum.equals(oldNumVo)) {
                    log.info("校验失败:{},{}",oldNum,oldNumVo);
                    throw new ServiceException(1,"此同行单数据已更新请刷新页面重新填写");
                }
                int arrivalCount = purchaseDeliveryDirectBatchDetailVos.stream().mapToInt(c -> {
                    return c.getArrivalCount() == null ? 0 : c.getArrivalCount();
                }).sum();
                if (oldNum < arrivalCount) {
                    // 数量超出
                    log.info("收货数量超出可填写的收货数量:{}",a);
                    throw new ServiceException(2,purchaseDeliveryDirectBatchDetailVos.get(0).getSku()+"的收货数量超出可填写的收货数量");
                }
            }
        } else {
            log.info("参数不全");
            throw new ServiceException(400,"参数不全");
        }
    }

    /**
     * 必填项校验 “耗材”/“试剂”时，必填项为：收货数量、生产批号/序列号、生效日期、失效日期； “设备”/“配件”时，必填项为：收货数量、生产批号/序列号  非医疗器械的 根据sku的2个字段判断
     * @param item
     */
    private void baseDataCheck(PurchaseDeliveryDirectBatchDetailVo item) {
        // 医疗器械 下 不可为空 非医疗器械sku需要 则不可为空
        if ("1".equals(item.getMedicalInstrumentCatalogIncluded())||(!"1".equals(item.getMedicalInstrumentCatalogIncluded())&& item.getIsManageVedengCode()!=null&& item.getIsManageVedengCode().equals(1))) {
            if (!StringUtils.hasText(item.getBatchNumber())) {
                throw  new ServiceException(90, item.getSku() + "的生产批号/序列号不可为空");
            }

        }

        if (StringUtils.hasText(item.getBatchNumber())) {
            if (item.getBatchNumber().contains("'")||item.getBatchNumber().contains("’")||item.getBatchNumber().contains("‘")||item.getBatchNumber().contains("\"")||item.getBatchNumber().contains(",")||item.getBatchNumber().contains("，")||item.getBatchNumber().contains("“")||item.getBatchNumber().contains("”")) {
                throw  new ServiceException(85, item.getSku() + "的生产批号/序列号不可包含中英文输入法的 单引号 双引号 逗号");
            }
        }

        // 医疗器械下 spu类型
        if ("1".equals(item.getMedicalInstrumentCatalogIncluded())  ) {
            if (SpuTypeEnum.CONSUMABLES.getCode().equals(item.getSpuType()) || SpuTypeEnum.REAGENT.getCode().equals(item.getSpuType())) {
                if (item.getManufactureDateTime() == null || item.getInvalidDateTime() == null) {
                    throw new ServiceException(92, item.getSku() + "的生效日期、失效日期必填");
                }
            }
        }

        // 非医疗器械下  sku需要则判断
        if ((!"1".equals(item.getMedicalInstrumentCatalogIncluded()) && item.getIsEnableValidityPeriod() != null && item.getIsEnableValidityPeriod().equals(1))) {
            if (item.getManufactureDateTime() == null || item.getInvalidDateTime() == null) {
                throw new ServiceException(92, item.getSku() + "的生效日期、失效日期必填");
            }
        }

        if (item.getArrivalCount()==null|| item.getArrivalCount()<=0) {
            throw new ServiceException(91, item.getSku() + "的收货数量必填");
        }

        if (item.getManufactureDateTime() != null && item.getInvalidDateTime() != null) {
            if (item.getManufactureDateTime().after(item.getInvalidDateTime())) {
                throw new ServiceException(88, item.getSku() + "的生效日期不可大于失效日期");
            }
        }
    }

    /**
     * 页面数据校验
     * @param data 页面传递的数据
     *             0 空，90 91 92 必填项 ，102 序列号重复 1 原数据他人修改，幂等性问题 2 收货数量超出限制 200 成功
     * @return Map<String,Object>
     */
    @Override
    public Map<String,Object> checkDetails(List<PurchaseDeliveryDirectBatchDetailVo> data) {
        //  相同的物流id下的数量相加是否满足 代发数据
        Map<String,Object> result = new HashMap<>();
        try {
            checkDetailsExcel(data);
        } catch (ServiceException e) {  // 不需要调整
            result.put("code", e.getCode());
            result.put("msg", e.getMessage());
            return result;
        }

        result.put("code", 200);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void savePurchaseDelivery(PurchaseDeliveryDirectBatchInfoVo data) {

        PurchaseDeliveryDirectBatchInfo saveInfo = new PurchaseDeliveryDirectBatchInfo();
        BeanUtils.copyProperties(data, saveInfo);
        // 主体信息
//        log.info("savePurchaseDelivery 保存数据：{}",JSON.toJSONString(data));
        purchaseDeliveryDirectBatchInfoMapper.insertSelective(saveInfo);
        Date now = new Date();

        // 详情
        List<String> skus = new ArrayList<>();
        if (!CollectionUtils.isEmpty(data.getList())) {
            List<PurchaseDeliveryDirectBatchDetail> saveItems = new ArrayList<>();
            List<PurchaseDeliveryDirectBatchDetailVo> list = data.getList();
            list.forEach(c->{
                PurchaseDeliveryDirectBatchDetail saveItem = new PurchaseDeliveryDirectBatchDetail();
                BeanUtils.copyProperties(c,saveItem);
                saveItem.setAddTime(now);
                saveItem.setModTime(now);
                saveItem.setCreator(data.getCreator());
                saveItem.setUpdater(data.getUpdater());
                saveItem.setPurchaseDeliveryDirectBatchInfoId(saveInfo.getPurchaseDeliveryDirectBatchInfoId());
                saveItems.add(saveItem);
                skus.add(c.getSku());
            });
            if (!CollectionUtils.isEmpty(saveItems)) {
//                log.info("savePurchaseDelivery 保存数据 items：{}",JSON.toJSONString(saveItems));
                purchaseDeliveryDirectBatchDetailMapper.batchInsertSelective(saveItems);
            }

            List<String> collect = skus.stream().distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                // 更新销售单专项发货
                List<Integer> saleorderGoodsList = newBuyorderMapper.selectSalorderGoodsBySkuBuyOrderId(data.getBuyorderId(),collect);
                if (!CollectionUtils.isEmpty(saleorderGoodsList)) {
                    newBuyorderMapper.updateSaleOrderGoodsSpecialDevlivery(saleorderGoodsList);
                    Integer saleOrderId = newBuyorderMapper.selectSalorderIdBySaleOrderGoodsId(saleorderGoodsList.get(0));
                    if (!Objects.isNull(saleOrderId)) {
                        newBuyorderMapper.updateComponentRelation(saleOrderId, collect);
                    }

                }
            }


        }

        //  图片
//        String reqUrl = ossHttp + ossUrl + ossFilePath;
        if (data.getFileUrls() != null && data.getFileUrls().length > 0) {
            List<Attachment> attachments = new ArrayList<>();
            for (int i = 0; i < data.getFileUrls().length; i++) {
                String fileUrl = data.getFileUrls()[i];
                if (StringUtils.hasText(fileUrl)) {
                    Attachment attachment = new Attachment();
                    String[] split = fileUrl.split(api_http + ossUrl);
                    if (split.length >=2&&!StringUtils.isEmpty(split[1])) {
                        attachment.setUri(split[1]);
                    }
                    attachment.setDomain(ossUrl);
                    attachment.setAddTime(System.currentTimeMillis());
                    attachment.setAttachmentType(BuyOrderConstant.ATTACHMENT_TYPE);
                    attachment.setAttachmentFunction(BuyOrderConstant.ATTACHMENT_FUNCTION);
                    attachment.setRelatedId(saveInfo.getPurchaseDeliveryDirectBatchInfoId());
                    attachments.add(attachment);
                }
            }
            buyorderAttachmentMapper.batchInsertSelective(attachments);
        }
    }


    @Override
    public List<PeerListBuyorderGoods> analysisExcel(MultipartFile file,Integer expressId) throws Exception {

        List<PeerListBuyorderGoods> needEditBuyGoods = getNeedEditBuyGoods(expressId);
        if (CollectionUtils.isEmpty(needEditBuyGoods)) {
            throw new ServiceException("该物流下已没有需要维护的信息");
        }

        Map<String, List<PeerListBuyorderGoods>> collect = needEditBuyGoods.stream().collect(Collectors.groupingBy(PeerListBuyorderGoods::getSku));
        PeerListBuyorderGoodsDoService doService = new PeerListBuyorderGoodsDoService();
        doService.setPeerListBuyorderGoods(needEditBuyGoods);
        doService.setList2Map(collect);
        doService.setExpressId(expressId);
        List<Integer> expressDetailIds = needEditBuyGoods.stream().map(PeerListBuyorderGoods::getExpressDetailId).distinct().collect(Collectors.toList());
        doService.setExpressDetailIds(expressDetailIds);
        EasyExcel.read(file.getInputStream(), PeerListBuyorderGoods.class, new PeerListBuyorderGoodsDataListener(doService)).sheet().headRowNumber(5).doRead();
        List<PeerListBuyorderGoods> result = doService.getResult();
        log.info("解析结果：{}",JSON.toJSONString(result));

        return result;
    }

    @Override
    public Buyorder getExcelOutData(Express express) {

        log.info("getExcelOutData 入参{}",JSON.toJSONString(express));
        Integer buyorderId = express.getBuyorderId();
        Integer expressId = express.getExpressId();
        if (buyorderId == null || expressId == null) {
            throw new ServiceException("参数缺失");
        }
        Buyorder buyorder = newBuyorderMapper.selectByPrimaryKey(buyorderId);
        if (buyorder == null) {
            throw new ServiceException("未查到此采购单信息");
        }
        List<PeerListBuyorderGoods> needEditBuyGoods = getNeedEditBuyGoods(express.getExpressId());
        if (!CollectionUtils.isEmpty(needEditBuyGoods)) {
            for (int i = 0; i < needEditBuyGoods.size(); i++) {
                needEditBuyGoods.get(i).setIndex(i + 1);
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime localDateTime = now.plusDays(-1);
//                LocalDateTime endTime = now.plusYears(1);
                Date from = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
//                Date end = Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant());
                //  hollis 排除非医疗器械的
                if ("1".equals(needEditBuyGoods.get(i).getMedicalInstrumentCatalogIncluded())) {
                    if (!SpuTypeEnum.EQUIPMENT.getCode().equals(needEditBuyGoods.get(i).getSpuType()) && !SpuTypeEnum.PARTS.getCode().equals(needEditBuyGoods.get(i).getSpuType())) {
                        needEditBuyGoods.get(i).setManufactureDateTime(from);
//                    needEditBuyGoods.get(i).setInvalidDateTime(end);
                    }
                }
                if ((!"1".equals(needEditBuyGoods.get(i).getMedicalInstrumentCatalogIncluded()) && needEditBuyGoods.get(i).getIsEnableValidityPeriod() != null && needEditBuyGoods.get(i).getIsEnableValidityPeriod().equals(1))) {
                    needEditBuyGoods.get(i).setManufactureDateTime(from);
                }

            }
        }

        buyorder.setNeedEditBuyGoods(needEditBuyGoods);
        log.info("导出模板数据：{}",JSON.toJSONString(buyorder));
        return buyorder;
    }
    @Override
    public List<PurchaseDeliveryDirectBatchDetailDto> getExOrInPurchaseDeliveryDirectBatchDetails(@NonNull Integer buyorderId, boolean isExWarehouse) {

        log.info("计算下发wms数据buyorderId:{}",buyorderId);

        TPurchaseDeliveryDirectBatchInfoExample query = new TPurchaseDeliveryDirectBatchInfoExample();
        query.createCriteria().andBuyorderIdEqualTo(buyorderId);
        List<PurchaseDeliveryDirectBatchInfo> purchaseDeliveryDirectBatchInfos = purchaseDeliveryDirectBatchInfoMapper.selectByExample(query);
        if (CollectionUtils.isEmpty(purchaseDeliveryDirectBatchInfos)) {
            return Collections.emptyList();
        }

        List<Integer> purchaseDeliveryDirectBatchInfoIds = purchaseDeliveryDirectBatchInfos.stream().map(PurchaseDeliveryDirectBatchInfo::getPurchaseDeliveryDirectBatchInfoId).collect(Collectors.toList());
        TPurchaseDeliveryDirectBatchDetailExample detailQuery = new TPurchaseDeliveryDirectBatchDetailExample();
        detailQuery.createCriteria().andPurchaseDeliveryDirectBatchInfoIdIn(purchaseDeliveryDirectBatchInfoIds);
        List<PurchaseDeliveryDirectBatchDetail> purchaseDeliveryDirectBatchDetails = purchaseDeliveryDirectBatchDetailMapper.selectByExample(detailQuery);

        if (CollectionUtils.isEmpty(purchaseDeliveryDirectBatchDetails)) {
            return Collections.emptyList();
        }
        // 脏数据处理
        purchaseDeliveryDirectBatchDetails.forEach(c->{
            if (Objects.isNull(c.getArrivalCount())) {
                c.setArrivalCount(0);
            }
            if (Objects.isNull(c.getWmsHandledDeliveryCount())) {
                c.setWmsHandledDeliveryCount(0);
            }
            if (Objects.isNull(c.getWmsHandledArrivalCount())) {
                c.setWmsHandledArrivalCount(0);
            }
        });
        List<PurchaseDeliveryDirectBatchDetailDto> result = new ArrayList<>();
        // 计算出库
        if (isExWarehouse) {

            // 如果之前维护的同行单数据中 wmsHandledDeliveryCount 小于 arrivalCount，那么之前维护的同行单数据还需再次重新下发，下发数量为 arrivalCount - wmsHandledDeliveryCount。
            List<PurchaseDeliveryDirectBatchDetail> exData = purchaseDeliveryDirectBatchDetails.stream().filter(c -> c.getArrivalCount() > c.getWmsHandledDeliveryCount()).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(exData)) {
                log.info("未过滤出需要出库的明细，原数据：{}", JSON.toJSONString(purchaseDeliveryDirectBatchDetails));
                return Collections.emptyList();
            }
            // 查询快递数据
            Map<Integer, List<com.vedeng.erp.buyorder.domain.entity.Express>> expresses2Map = queryExpressData(exData);
            // 组装返回数据
            for (PurchaseDeliveryDirectBatchDetail exDatum : exData) {
                PurchaseDeliveryDirectBatchDetailDto purchaseDeliveryDirectBatchDetailDto = new PurchaseDeliveryDirectBatchDetailDto();
                BeanUtils.copyProperties(exDatum, purchaseDeliveryDirectBatchDetailDto);
                purchaseDeliveryDirectBatchDetailDto.setBuyorderId(buyorderId);
                // 如果之前维护的同行单数据中 wmsHandledDeliveryCount 小于 arrivalCount，那么之前维护的同行单数据还需再次重新下发，下发数量为 arrivalCount - wmsHandledDeliveryCount。
                purchaseDeliveryDirectBatchDetailDto.setArrivalCount(exDatum.getArrivalCount() - exDatum.getWmsHandledDeliveryCount());
                bindExpressData(result, expresses2Map, exDatum, purchaseDeliveryDirectBatchDetailDto);

            }

        } else {
            // 入库计算
            // 如果之前维护的同行单数据中 wmsHandledArrivalCount 小于 arrivalCount，那么之前维护的同行单数据还需再次重新下发，下发数量为 arrivalCount - wmsHandledArrivalCount。
            List<PurchaseDeliveryDirectBatchDetail> inData = purchaseDeliveryDirectBatchDetails.stream().filter(c -> c.getArrivalCount() > c.getWmsHandledArrivalCount()).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(inData)) {
                log.info("未过滤出需要入库的明细，原数据：{}", JSON.toJSONString(purchaseDeliveryDirectBatchDetails));
                return Collections.emptyList();
            }
            // 查询快递数据
            Map<Integer, List<com.vedeng.erp.buyorder.domain.entity.Express>> expresses2Map = queryExpressData(inData);
            // 组装返回数据
            for (PurchaseDeliveryDirectBatchDetail inDatum : inData) {
                PurchaseDeliveryDirectBatchDetailDto purchaseDeliveryDirectBatchDetailDto = new PurchaseDeliveryDirectBatchDetailDto();
                BeanUtils.copyProperties(inDatum, purchaseDeliveryDirectBatchDetailDto);
                purchaseDeliveryDirectBatchDetailDto.setBuyorderId(buyorderId);
                // 如果之前维护的同行单数据中 wmsHandledArrivalCount 小于 arrivalCount，那么之前维护的同行单数据还需再次重新下发，下发数量为 arrivalCount - wmsHandledArrivalCount。
                purchaseDeliveryDirectBatchDetailDto.setArrivalCount(inDatum.getArrivalCount() - inDatum.getWmsHandledArrivalCount());
                bindExpressData(result, expresses2Map, inDatum, purchaseDeliveryDirectBatchDetailDto);


            }
        }

        log.info("下发出入库信息计算isExWarehouse:{} data：{}",isExWarehouse,JSON.toJSONString(result));
        return result;
    }

    /**
     * 查询物流信息
     * @param inData
     * @return
     */
    private  Map<Integer, List<com.vedeng.erp.buyorder.domain.entity.Express>> queryExpressData(List<PurchaseDeliveryDirectBatchDetail> inData) {
        Map<Integer, List<com.vedeng.erp.buyorder.domain.entity.Express>> expresses2Map = null;
        List<Integer> expressDetailIds = inData.stream().map(PurchaseDeliveryDirectBatchDetail::getExpressDetailId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(expressDetailIds)) {
            List<com.vedeng.erp.buyorder.domain.entity.Express> expresses = buyorderExpressMapper.selectByExpressDetailIds(expressDetailIds);
            expresses2Map = expresses.stream().collect(Collectors.groupingBy(com.vedeng.erp.buyorder.domain.entity.Express::getExpressDetailId));
        }
        return expresses2Map;
    }

    /**
     * 重复代码，绑定物流信息
     * @param result 结果结合
     * @param expresses2Map 物流信息
     * @param inDatum 原始数据
     * @param purchaseDeliveryDirectBatchDetailDto 结果集合item
     */
    private void bindExpressData(List<PurchaseDeliveryDirectBatchDetailDto> result, Map<Integer, List<com.vedeng.erp.buyorder.domain.entity.Express>> expresses2Map, PurchaseDeliveryDirectBatchDetail inDatum, PurchaseDeliveryDirectBatchDetailDto purchaseDeliveryDirectBatchDetailDto) {
        if (!CollectionUtils.isEmpty(expresses2Map)) {
            // 查询 物流单号
            List<com.vedeng.erp.buyorder.domain.entity.Express> expresses = expresses2Map.get(inDatum.getExpressDetailId());
            if (!CollectionUtils.isEmpty(expresses)) {
                Optional<com.vedeng.erp.buyorder.domain.entity.Express> first = expresses.stream().findFirst();
                if (first.isPresent()) {
                    DateTimeFormatter ftf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    String format = ftf.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(first.get().getDeliveryTime()), ZoneId.systemDefault()));
                    purchaseDeliveryDirectBatchDetailDto.setExpressDeliveryTime(format);
                    purchaseDeliveryDirectBatchDetailDto.setBuyorderGoodsId(first.get().getBuyorderGoodsId());
                    purchaseDeliveryDirectBatchDetailDto.setLogisticsNo(first.get().getLogisticsNo());
                }
            }
        }

        result.add(purchaseDeliveryDirectBatchDetailDto);
    }

    @Override
    public ResultInfo savePurchaseDeliveryDirectAttachment(PurchaseDeliveryDirectAttachmentDto purchaseDeliveryDirectAttachmentDto, User user) {
        ResultInfo resultInfo = new ResultInfo(0,"保存成功");
        //作废原有出库单附件
        com.vedeng.system.model.Attachment old = new com.vedeng.system.model.Attachment();
        old.setRelatedId(purchaseDeliveryDirectAttachmentDto.getRelatedId());
        old.setAttachmentFunction(SysOptionConstant.ID_4080);
        attachmentMapper.updateByRelation(old);
        //新增新附件
        List<Attachment> attachmentList = new ArrayList<>();
        int size = Math.min(purchaseDeliveryDirectAttachmentDto.getFileName().size(),purchaseDeliveryDirectAttachmentDto.getFileUri().size());
        for(int i = 0;i < size;i++){
            Attachment attachment = new Attachment();
            attachment.setAttachmentFunction(SysOptionConstant.ID_4080);
            attachment.setAttachmentType(SysOptionConstant.ID_460);
            attachment.setRelatedId(purchaseDeliveryDirectAttachmentDto.getRelatedId());
            attachment.setName(purchaseDeliveryDirectAttachmentDto.getFileName().get(i));
            attachment.setDomain(ossUrl);
            attachment.setUri(purchaseDeliveryDirectAttachmentDto.getFileUri().get(i));
            attachment.setAddTime(DateUtil.sysTimeMillis());
            attachment.setCreator(user.getUserId());
            attachmentList.add(attachment);
        }
        buyorderAttachmentMapper.batchInsertSelective(attachmentList);
        return resultInfo;
    }

    @Override
    public List<Integer> getSaleorderIdListByBuyorderId(Integer buyorderId) {
        if (Objects.isNull(buyorderId)) {
            return Collections.emptyList();
        }
        return newBuyorderMapper.getSaleorderIdListByBuyorderId(buyorderId);
    }

    @Override
    public List<Integer> getBuyorderIdListBySaleorderIds(List<Integer> saleorderIds) {
        if (CollectionUtils.isEmpty(saleorderIds)) {
            return Collections.emptyList();
        }
        return newBuyorderMapper.getBuyorderIdListBySaleorderIds(saleorderIds);
    }

    /**
     * 根据采购单ID查询同行单信息
     *
     * @param buyOrderId 采购单ID
     * @return 同行单信息，包含所有明细列表
     */
    @Override
    public PurchaseDeliveryDirectBatchInfoVo queryPeerList(Integer buyOrderId) {
        log.info("查询采购单同行单信息，buyorderId: {}", buyOrderId);
        if (buyOrderId == null) {
            throw new ServiceException("采购单ID不能为空");
        }

        try {
            // 查询采购单相关的物流数据
            List<BuyOrderExpressDetailDto> expresses = buyorderExpressMapper.selectByBuyorderId(buyOrderId);
            Integer expressNum = expresses.stream().mapToInt(BuyOrderExpressDetailDto::getNum).sum();

            // 创建返回对象
            PurchaseDeliveryDirectBatchInfoVo resultVo = new PurchaseDeliveryDirectBatchInfoVo();
            resultVo.setBuyorderId(buyOrderId);
            List<PurchaseDeliveryDirectBatchDetailVo> allDetailsList = new ArrayList<>();
            
            // 查询同行单批次信息
            TPurchaseDeliveryDirectBatchInfoExample infoExample = new TPurchaseDeliveryDirectBatchInfoExample();
            infoExample.createCriteria().andBuyorderIdEqualTo(buyOrderId);
            infoExample.setOrderByClause("add_time DESC");
            List<PurchaseDeliveryDirectBatchInfo> batchInfoList = purchaseDeliveryDirectBatchInfoMapper.selectByExample(infoExample);
            
            if (CollectionUtils.isEmpty(batchInfoList)) {
                log.info("未找到采购单的同行单信息，buyorderId: {}", buyOrderId);
                resultVo.setPeerStatus(0);
                resultVo.setList(allDetailsList);
                return resultVo;
            }
            
            // 获取所有批次ID
            List<Integer> batchIds = batchInfoList.stream()
                    .map(PurchaseDeliveryDirectBatchInfo::getPurchaseDeliveryDirectBatchInfoId)
                    .collect(Collectors.toList());
            
            // 查询同行单明细
            TPurchaseDeliveryDirectBatchDetailExample detailExample = new TPurchaseDeliveryDirectBatchDetailExample();
            detailExample.createCriteria().andPurchaseDeliveryDirectBatchInfoIdIn(batchIds);
            List<PurchaseDeliveryDirectBatchDetail> detailList = purchaseDeliveryDirectBatchDetailMapper.selectByExample(detailExample);
           
            if (CollectionUtils.isEmpty(detailList)){
                log.info("未找到采购单的同行单明细，buyorderId: {}", buyOrderId);
                resultVo.setList(allDetailsList);
                resultVo.setPeerStatus(0);
                return resultVo;
            }
            Integer totalNum = detailList.stream().mapToInt(PurchaseDeliveryDirectBatchDetail::getArrivalCount).sum();
            if (!Objects.equals(expressNum,totalNum)){
                resultVo.setPeerStatus(0);
                return resultVo;
            }
            resultVo.setPeerStatus(1);
            
            if (!CollectionUtils.isEmpty(detailList)) {
                // 转换所有明细
                for (PurchaseDeliveryDirectBatchDetail detail : detailList) {
                    PurchaseDeliveryDirectBatchDetailVo detailVo = new PurchaseDeliveryDirectBatchDetailVo();
                    BeanUtils.copyProperties(detail, detailVo);
                    String logisticsNo = buyorderExpressMapper.getLogisticsNo(detail.getExpressDetailId());
                    detailVo.setLogisticsNo(logisticsNo);
                    detailVo.setExpressDetailId(null);
                    allDetailsList.add(detailVo);
                }
            }
            
            // 设置所有明细列表
            resultVo.setList(allDetailsList);
            
            // 设置基本信息（使用最新一条批次信息）
            if (!CollectionUtils.isEmpty(batchInfoList)) {
                PurchaseDeliveryDirectBatchInfo latestBatchInfo = batchInfoList.get(0);
                BeanUtils.copyProperties(latestBatchInfo, resultVo);
                resultVo.setBuyorderId(buyOrderId); // 确保buyorderId正确设置
            }
            
            log.info("查询采购单同行单信息成功，buyorderId: {}, 明细数量: {}", buyOrderId, allDetailsList.size());
            return resultVo;
            
        } catch (Exception e) {
            log.error("查询采购单同行单信息失败，buyorderId: {}", buyOrderId, e);
            throw new ServiceException("查询采购单同行单信息失败: " + e.getMessage());
        }
    }
    
    @Resource
    private BuyorderGoodsMapper newBuyorderGoodsMapper;

    /**
     * 根据采购单ID查询出入库记录并组装返回同行单创建入参
     *
     * @param buyorderId 采购单ID
     * @return 同行单创建入参
     */
    @Override
    public PurchaseDeliveryDirectBatchInfoVo queryStockRecords(Integer buyorderId) {
        log.info("查询采购单出入库记录并组装同行单创建入参，buyorderId: {}", buyorderId);
        if (buyorderId == null) {
            throw new ServiceException("采购单ID不能为空");
        }

        try {
            // 查询采购单信息
            Buyorder buyorder = newBuyorderMapper.selectByPrimaryKey(buyorderId);
            if (buyorder == null) {
                throw new ServiceException("采购单不存在");
            }
            List<BuyorderGoods> buyorderGoodsList = newBuyorderGoodsMapper.findByBuyorderId(buyorderId);
            
            // 创建同行单批次信息
            PurchaseDeliveryDirectBatchInfoVo batchInfoVo = new PurchaseDeliveryDirectBatchInfoVo();
            batchInfoVo.setBuyorderId(buyorderId);
            batchInfoVo.setAddTime(new Date());
            batchInfoVo.setModTime(new Date());
            batchInfoVo.setPeerStatus(0);
            
            // 查询采购单相关的物流数据
            List<BuyOrderExpressDetailDto> expresses = buyorderExpressMapper.selectByBuyorderId(buyorderId);
            log.info("查询采购单相关的物流数据，Express: {}", JSON.toJSON(expresses));
            if (CollectionUtils.isEmpty(expresses)) {
                log.info("未找到采购单相关的物流数据，buyorderId: {}", buyorderId);
                return batchInfoVo;
            }
            
            // 按商品ID索引采购商品信息，便于快速查找
            Map<Integer, BuyorderGoods> goodsIdToInfoMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(buyorderGoodsList)) {
                for (BuyorderGoods goods : buyorderGoodsList) {
                    goodsIdToInfoMap.put(goods.getBuyorderGoodsId(), goods);
                }
            }
            
            // 查询采购单相关的出入库记录
            List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList = warehouseGoodsOperateLogMapper.getLog(buyorderId);
            if (CollectionUtils.isEmpty(warehouseGoodsOperateLogList)){
                log.info("未找到采购单相关的出入库记录，buyorderId: {}", buyorderId);
                return batchInfoVo;
            }

            Integer peerStatus = checkNum(expresses, warehouseGoodsOperateLogList);
            batchInfoVo.setPeerStatus(peerStatus);

            // 创建同行单明细列表
            List<PurchaseDeliveryDirectBatchDetailVo> detailVoList = new ArrayList<>();
            
            // 第一步：将入库数据按数量1展开，并按SKU分组
            Map<String, List<WarehouseGoodsOperateLog>> skuToExpandedLogsMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(warehouseGoodsOperateLogList)) {
                for (WarehouseGoodsOperateLog log : warehouseGoodsOperateLogList) {
                    if (log.getOperateType() != null && log.getOperateType() == 1) { // 只处理入库操作
                        String sku = log.getSku();
                        if (StringUtils.hasText(sku)) {
                            // 获取入库数量
                            Integer quantity = log.getNum() != null ? log.getNum() : 0;
                            
                            // 按数量1展开
                            for (int i = 0; i < quantity; i++) {
                                // 创建单个数量的记录
                                WarehouseGoodsOperateLog expandedLog = new WarehouseGoodsOperateLog();
                                BeanUtils.copyProperties(log, expandedLog);
                                expandedLog.setNum(1); // 设置数量为1
                                
                                // 添加到对应SKU的列表中
                                skuToExpandedLogsMap.computeIfAbsent(sku, k -> new ArrayList<>()).add(expandedLog);
                            }
                        }
                    }
                }
            }
            
            // 第二步：遍历物流数据，按商品展开并关联入库数据
            for (BuyOrderExpressDetailDto express : expresses) {
                // 获取物流明细信息
                Integer expressDetailId = express.getExpressDetailId();
                Integer buyorderGoodsId = express.getRelatedId();
                Integer quantity = express.getNum() != null ? express.getNum() : 0;
                
                // 获取对应的采购商品信息
                BuyorderGoods buyorderGoods = goodsIdToInfoMap.get(buyorderGoodsId);
                if (buyorderGoods == null) {
                    log.info("未找到对应的采购商品信息，buyorderGoodsId: {}", buyorderGoodsId);
                    continue;
                }
                
                // 获取商品SKU
                String sku = buyorderGoods.getSku();
                if (!StringUtils.hasText(sku)) {
                    log.info("采购商品SKU为空，buyorderGoodsId: {}", buyorderGoodsId);
                    continue;
                }
                
                // 获取该SKU的入库记录
                List<WarehouseGoodsOperateLog> expandedLogs = skuToExpandedLogsMap.get(sku);
                
                // 需要获取更多的商品信息，使用getNeedEditBuyGoods方法获取一次
                List<PeerListBuyorderGoods> peerListBuyorderGoodsList = null;
                if (express.getExpressId() != null) {
                    peerListBuyorderGoodsList = getNeedEditBuyGoods(express.getExpressId());
                }
                
                // 找到对应的PeerListBuyorderGoods
                PeerListBuyorderGoods peerListBuyorderGoods = null;
                if (!CollectionUtils.isEmpty(peerListBuyorderGoodsList)) {
                    for (PeerListBuyorderGoods goods : peerListBuyorderGoodsList) {
                        if (goods.getBuyorderGoodsId() != null && 
                            goods.getBuyorderGoodsId().equals(buyorderGoodsId) && 
                            goods.getExpressDetailId() != null && 
                            goods.getExpressDetailId().equals(expressDetailId)) {
                            peerListBuyorderGoods = goods;
                            break;
                        }
                    }
                }
                
                // 按数量1展开商品
                for (int i = 0; i < quantity; i++) {
                    PurchaseDeliveryDirectBatchDetailVo detailVo = new PurchaseDeliveryDirectBatchDetailVo();
                    
                    // 设置基本信息
                    detailVo.setSku(sku);
                    detailVo.setSkuName(buyorderGoods.getGoodsName());
                    detailVo.setModel(buyorderGoods.getModel());
                    detailVo.setUnit(buyorderGoods.getUnitName());
                    detailVo.setProductCompany(buyorderGoods.getManufacturerName());
                    detailVo.setExpressDetailId(expressDetailId);
                    
                    // 设置数量信息
                    detailVo.setArrivalCount(1); // 拆分后每条记录数量为1
                    detailVo.setOldNum(1);
                    
                    // 设置医疗器械相关信息
                    if (peerListBuyorderGoods != null) {
                        // 使用PeerListBuyorderGoods中的信息
                        detailVo.setSpuType(peerListBuyorderGoods.getSpuType());
                        detailVo.setMedicalInstrumentCatalogIncluded(peerListBuyorderGoods.getMedicalInstrumentCatalogIncluded());
                        detailVo.setRegisterNumber(peerListBuyorderGoods.getRegistrationNumber());
                        detailVo.setProductionLicence(peerListBuyorderGoods.getProductCompanyLicence());
                        detailVo.setIsManageVedengCode(peerListBuyorderGoods.getIsManageVedengCode());
                        detailVo.setIsEnableValidityPeriod(peerListBuyorderGoods.getIsEnableValidityPeriod());
                    } else {
                        // 使用BuyorderGoods中的信息
                        detailVo.setRegisterNumber(buyorderGoods.getRegistrationNumber());
                    }
                    
                    // 如果有对应的入库记录，使用入库记录中的信息
                    if (!CollectionUtils.isEmpty(expandedLogs) && i < expandedLogs.size()) {
                        WarehouseGoodsOperateLog operateLog = expandedLogs.get(i);
                        
                        // 设置批次号/序列号(使用批次号，设备类型商品的批次号通常就是序列号)
                        String batchNumber = operateLog.getBatchNumber() != null ? operateLog.getBatchNumber() : "";
                        detailVo.setBatchNumber(batchNumber);
                        
                        // 设置生产日期和失效日期
                        if (operateLog.getProductDate() != null) {
                            detailVo.setManufactureDateTime(new Date(operateLog.getProductDate()));
                        }
                        
                        if (operateLog.getExpirationDate() != null) {
                            detailVo.setInvalidDateTime(new Date(operateLog.getExpirationDate()));
                        }
                    } else {
                        // 没有对应的入库记录，设置默认值
                        detailVo.setBatchNumber("");
                        
                        // 对于需要有效期的商品，设置默认的生产日期
                        if (peerListBuyorderGoods != null) {
                            String medicalInstrumentCatalogIncluded = peerListBuyorderGoods.getMedicalInstrumentCatalogIncluded();
                            Integer isEnableValidityPeriod = peerListBuyorderGoods.getIsEnableValidityPeriod();
                            Integer spuType = peerListBuyorderGoods.getSpuType();
                            
                            if ("1".equals(medicalInstrumentCatalogIncluded) || 
                                (!"1".equals(medicalInstrumentCatalogIncluded) && 
                                 isEnableValidityPeriod != null && 
                                 isEnableValidityPeriod.equals(1))) {
                                
                                if (spuType != null && 
                                    !SpuTypeEnum.EQUIPMENT.getCode().equals(spuType) && 
                                    !SpuTypeEnum.PARTS.getCode().equals(spuType)) {
                                    detailVo.setManufactureDateTime(new Date());
                                }
                            }
                        }
                    }
                    
                    // 设置是否是序列号
                    if (peerListBuyorderGoods != null && peerListBuyorderGoods.getSpuType() != null) {
                        detailVo.setUnionSequence(SpuTypeEnum.EQUIPMENT.getCode().equals(peerListBuyorderGoods.getSpuType()) ? 1 : 0);
                    } else {
                        detailVo.setUnionSequence(0); // 默认不是序列号
                    }
                    
                    // 设置时间
                    detailVo.setAddTime(new Date());
                    detailVo.setModTime(new Date());
                    
                    // 设置物流信息
                    detailVo.setLogisticsNo(express.getLogisticsNo());
                    detailVo.setExpressId(express.getExpressId());
                    
                    // 设置发货时间
                    if (express.getDeliveryTime() != null) {
                        DateTimeFormatter ftf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                        String deliveryTime = ftf.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(express.getDeliveryTime()), ZoneId.systemDefault()));
                        detailVo.setExpressDeliveryTime(deliveryTime);
                    }
                    
                    detailVoList.add(detailVo);
                }
                
                // 从已使用的入库记录中移除，避免重复使用
                if (!CollectionUtils.isEmpty(expandedLogs) && quantity > 0) {
                    // 如果入库记录数量大于等于商品数量，移除已使用的记录
                    if (expandedLogs.size() >= quantity) {
                        expandedLogs.subList(0, quantity).clear();
                    } else {
                        // 如果入库记录数量小于商品数量，清空所有记录
                        expandedLogs.clear();
                    }
                }
            }
            
            batchInfoVo.setList(detailVoList);
            
            log.info("查询采购单出入库记录并组装同行单创建入参成功，buyorderId: {}, 明细数量: {}", buyorderId, detailVoList.size());
            return batchInfoVo;
            
        } catch (ServiceException e) {
            log.error("查询采购单出入库记录并组装同行单创建入参失败，buyorderId: {}", buyorderId, e);
            throw e;
        } catch (Exception e) {
            log.error("查询采购单出入库记录并组装同行单创建入参失败，buyorderId: {}", buyorderId, e);
            throw new ServiceException("查询采购单出入库记录并组装同行单创建入参失败: " + e.getMessage());
        }
    }

    private Integer checkNum(List<BuyOrderExpressDetailDto> expresses, List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList) {
        // 比较数量
        Integer expressNum = expresses.stream().mapToInt(BuyOrderExpressDetailDto::getNum).sum();
        Integer warehouseGoodsOperateLogNum = warehouseGoodsOperateLogList.stream().mapToInt(WarehouseGoodsOperateLog::getNum).sum();
        log.info("快递数量：{},库存数据：{}", expressNum, warehouseGoodsOperateLogNum);
        if (expressNum > 0 && warehouseGoodsOperateLogNum > 0 && expressNum.equals(warehouseGoodsOperateLogNum)) {
            return 1;
        }
        return 0;
    }

    @Override
    public PurchaseDeliveryDirectBatchInfoVo queryPeerRecords(Integer buyorderId) {
        log.info("查询采购单同行单，buyorderId: {}", buyorderId);
        PurchaseDeliveryDirectBatchInfoVo batchInfoVo = new PurchaseDeliveryDirectBatchInfoVo();
        // 查询采购单相关的物流数据
        List<BuyOrderExpressDetailDto> expresses = buyorderExpressMapper.selectByBuyorderId(buyorderId);
        Integer expressNum = expresses.stream().mapToInt(BuyOrderExpressDetailDto::getNum).sum();
        
        List<PurchaseDeliveryDirectBatchDetail> purchaseDeliveryDirectBatchDetails = purchaseDeliveryDirectBatchDetailMapper.queryInfoByBuyOrderId(buyorderId);
        if (CollectionUtils.isEmpty(purchaseDeliveryDirectBatchDetails)){
            log.info("未找到采购单的同行单明细，buyorderId: {}", buyorderId);
            batchInfoVo.setPeerStatus(0);
            return batchInfoVo;
        }
        Integer totalNum = purchaseDeliveryDirectBatchDetails.stream().mapToInt(PurchaseDeliveryDirectBatchDetail::getArrivalCount).sum();
        log.info("采购单物流数量：{},采购单明细数量：{}", expressNum, totalNum);
        if (!Objects.equals(expressNum,totalNum)){
            batchInfoVo.setPeerStatus(0);
            return batchInfoVo;
        }
        batchInfoVo.setPeerStatus(1);
        
        List<PurchaseDeliveryDirectBatchDetailVo> purchaseDeliveryDirectBatchDetailVos = BeanUtil.copyToList(purchaseDeliveryDirectBatchDetails, PurchaseDeliveryDirectBatchDetailVo.class);
        batchInfoVo.setList(purchaseDeliveryDirectBatchDetailVos);
        batchInfoVo.setBuyorderId(buyorderId);
        batchInfoVo.setAddTime(new Date());
        batchInfoVo.setModTime(new Date());
        return batchInfoVo;
    }

    /**
     * 从采购商品信息创建同行单明细
     * 
     * @param buyGoods 采购商品信息
     * @param express 物流信息
     * @param detailVoList 明细列表
     */
    private void createDetailVoFromBuyGoods(PeerListBuyorderGoods buyGoods, com.vedeng.erp.buyorder.domain.entity.Express express, List<PurchaseDeliveryDirectBatchDetailVo> detailVoList) {
        // 获取商品数量
        Integer quantity = buyGoods.getDnum() != null ? buyGoods.getDnum() : 0;
        
        // 如果数量大于1，则拆分成多条记录
        for (int i = 0; i < quantity; i++) {
            PurchaseDeliveryDirectBatchDetailVo detailVo = new PurchaseDeliveryDirectBatchDetailVo();
            
            // 设置基本信息
            detailVo.setSku(buyGoods.getSku());
            detailVo.setSkuName(buyGoods.getGoodsName());
            detailVo.setModel(buyGoods.getModel());
            detailVo.setUnit(buyGoods.getUnitName());
            detailVo.setProductCompany(buyGoods.getManufacturerName());
            detailVo.setSpuType(buyGoods.getSpuType());
            detailVo.setExpressDetailId(buyGoods.getExpressDetailId());
            
            // 设置数量信息
            detailVo.setArrivalCount(1); // 拆分后每条记录数量为1
            detailVo.setOldNum(1);
            
            // 设置医疗器械相关信息
            detailVo.setMedicalInstrumentCatalogIncluded(buyGoods.getMedicalInstrumentCatalogIncluded());
            detailVo.setRegisterNumber(buyGoods.getRegistrationNumber());
            detailVo.setProductionLicence(buyGoods.getProductCompanyLicence());
            
            // 设置批次号/序列号(初始为空，由用户填写)
            detailVo.setBatchNumber("");
            
            // 设置生产日期和失效日期(初始值)
            if ("1".equals(buyGoods.getMedicalInstrumentCatalogIncluded()) || 
                (!"1".equals(buyGoods.getMedicalInstrumentCatalogIncluded()) && 
                 buyGoods.getIsEnableValidityPeriod() != null && 
                 buyGoods.getIsEnableValidityPeriod().equals(1))) {
                
                // 对于需要有效期的商品，设置默认的生产日期为当前日期
                if (!SpuTypeEnum.EQUIPMENT.getCode().equals(buyGoods.getSpuType()) && 
                    !SpuTypeEnum.PARTS.getCode().equals(buyGoods.getSpuType())) {
                    detailVo.setManufactureDateTime(new Date());
                }
            }
            
            // 设置是否是序列号
            detailVo.setUnionSequence(SpuTypeEnum.EQUIPMENT.getCode().equals(buyGoods.getSpuType()) ? 1 : 0);
            
            // 设置是否管理贝登码和有效期
            detailVo.setIsManageVedengCode(buyGoods.getIsManageVedengCode());
            detailVo.setIsEnableValidityPeriod(buyGoods.getIsEnableValidityPeriod());
            
            // 设置时间
            detailVo.setAddTime(new Date());
            detailVo.setModTime(new Date());
            
            // 设置物流信息
            detailVo.setLogisticsNo(express.getLogisticsNo());
            
            // 设置发货时间
            if (express.getDeliveryTime() != null) {
                DateTimeFormatter ftf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String deliveryTime = ftf.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(express.getDeliveryTime()), ZoneId.systemDefault()));
                detailVo.setExpressDeliveryTime(deliveryTime);
            }
            
            detailVoList.add(detailVo);
        }
    }
}
