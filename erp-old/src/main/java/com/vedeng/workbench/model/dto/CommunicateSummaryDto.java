package com.vedeng.workbench.model.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CommunicateSummaryDto {

    /**
     * 待沟通商机对象姓名
     */
    private String name;

    /**
     * 待沟通商机销售用户id
     */
    private Integer userId;
    /**
     * 计划内沟通商机数
     */
    private Integer planingCommunicateNum;

    /**
     * 计划内数量
     */
    private Integer inPlaningNum;

    /**
     * 计划外数量
     */
    private Integer outPlaningNum;

    /**
     * 完成率
     */
    private BigDecimal completionRate;

    /**
     * 今日待沟通商机数量
     */
    private Integer todayToCommunicateNum;
}
