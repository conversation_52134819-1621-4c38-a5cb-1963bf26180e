package com.vedeng.goods.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 场景方案分类
 */
@Getter
@Setter
public class SkuSceneCategoryEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long id;

    /**
     * 所属方案ID
     */
    private Long sceneId;

    /**
     * 场景名称
     */
    private String name;

    /**
     * 场景描述
     */
    private String description;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * skuNo逗号分隔
     */
    private String skuNos;
}