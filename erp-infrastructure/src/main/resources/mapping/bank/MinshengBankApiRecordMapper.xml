<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.infrastructure.bank.api.mapper.MinshengBankApiRecordMapper">


    <insert id="insertRecord" parameterType="com.vedeng.infrastructure.bank.api.domain.entity.MinshengBankApiRecord">
        INSERT INTO T_MINSHENG_BANK_API_RECORDS
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="requestBody != null">
                REQUEST_BODY,
            </if>
            <if test="responseBody != null">
                RESPONSE_BODY,
            </if>
            <if test="trnId != null">
                TRN_ID,
            </if>
            <if test="insId != null">
                INS_ID,
            </if>
            <if test="apiType != null">
                API_TYPE,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides="," >
            <if test="requestBody != null">
                #{requestBody,jdbcType=VARCHAR},
            </if>
            <if test="responseBody != null">
                #{responseBody,jdbcType=VARCHAR},
            </if>
            <if test="trnId != null">
                #{trnId,jdbcType=VARCHAR},
            </if>
            <if test="insId != null">
                #{insId,jdbcType=VARCHAR},
            </if>
            <if test="apiType != null">
                #{apiType,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

</mapper>