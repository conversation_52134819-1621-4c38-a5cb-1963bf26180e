package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.PaySaleOrderBalanceBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 销售订单客户余额支付
 * （应收单调整单）
 */
@JobHandler(value = "SaleOrderPayByBalanceBatchTask")
@Component
public class SaleOrderPayByBalanceBatchTask extends AbstractJobHandler {
    @Autowired
    private JobLauncher jobLauncher;
    @Autowired
    PaySaleOrderBalanceBatchJob batchJob;
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================SaleOrderPayByBalanceBatchTask开始====================");
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = batchJob.saleOrderPayByBalanceBatchJob();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("==================SaleOrderPayByBalanceBatchTask结束====================");
        return SUCCESS;
    }
}
