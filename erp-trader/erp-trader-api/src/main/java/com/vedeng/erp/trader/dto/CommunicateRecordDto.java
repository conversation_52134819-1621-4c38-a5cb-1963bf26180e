package com.vedeng.erp.trader.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import com.vedeng.erp.system.dto.TagDto;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 沟通记录
 *
 * <AUTHOR>
 */
@Data
public class CommunicateRecordDto {
    /**
     * 沟通记录主键id
     */
    @NotNull(message = "主键不可为空",groups = {UpdateGroup.class})
    private Integer communicateRecordId;

    /**
     * 沟通记录类型-字典值-(244商机，4109线索)
     */
    @NotNull(message = "通话关联业务类型不可为空",groups = {AddGroup.class,UpdateGroup.class})
    private Integer communicateType;

    /**
     * 公司ID
     */
    @NotNull(message = "公司id不可为空",groups = {AddGroup.class,UpdateGroup.class})
    private Integer companyId;

    /**
     * 关联表的主键id（如：商机id，线索id）
     */
    @NotNull(message = "关联主表字段ID不可为空",groups = {AddGroup.class,UpdateGroup.class})
    private Integer relatedId;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 归属销售id
     */
    private Integer belongUserId;

    /**
     * 归属销售名称
     */
    private String belongUserName;
    
    /**
     * 归属销售头像
     */
    private String belongPic;

    /**
     * 跟进类型-字典值(电话，企微，其他)
     */
    private Integer followUpType;

    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 联系人手机
     */
    private String traderContactMobile;


    /**
     * 联系人电话
     */
    private String traderContactTelephone;

    /**
     * 联系人名称
     */
    private String contact;

    /**
     * 手机
     */
    private String contactMob;

    /**
     * 固定电话
     */
    private String telephone;

    /**
     * 跟进时间-开始
     */
    private Long begintime;

    /**
     * 跟进时间-开始-字符串格式
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTimeDate;

    /**
     * 跟进时间-结束
     */
    private Long endtime;

    /**
     * 跟进时间-结束-字符串格式
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTimeDate;

    /**
     * 沟通内容
     */
    private String contentSuffix;

    /**
     * AI摘要分析
     */
    private String aiSummaryAnalysis;

    /**
     * AI待办分析
     */
    private String aiTodoAnalysis;

    /**
     * 下次跟进时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date nextContactDate;

    /**
     * 下次跟进内容
     */
    private String nextContactContent;

    /**
     * 暂无下次跟进时间(0 否,1 是)
     */
    private Integer noneNextDate;

    /**
     * 所属类型(1:经销商（包含终端）2:供应商)
     */
    private Integer traderType;
    
    /**
     * 沟通方式-字典值
     */
    private Integer communicateMode;

    /**
     * 沟通目的-字典值
     */
    private Integer communicateGoal;

    /**
     * 联系人名称
     */
    private String traderContactNameNew;

    /**
     * 沟通电话
     */
    private String phone;

    /**
     * 呼叫中心COID
     */
    private String coid;

    /**
     * 1呼入2呼出
     */
    private Integer coidType;

    /**
     * 录音时长
     */
    private Integer coidLength;

    /**
     * 录音域名
     */
    private String coidDomain;

    /**
     * 录音地址
     */
    private String coidUri;

    /**
     * '商机精准度 0无法判断 1不精准 2一般精准 3高精准'
     */
    private Integer businessChanceAccuracy;

    /**
     * 备注
     */
    private String comments;

    /**
     * 是否处理0否 1是（有下次沟通记录，无下次沟通时间默认为1）
     */
    private Integer isDone;

    /**
     * 售后对象ID
     */
    private Integer afterSalesTraderId;

    /**
     * 关联沟通ID
     */
    private Integer relateCommunicateRecordId;

    /**
     * 沟通内容
     */
    private String contactContent;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 添加人 名称
     */
    private String creatorName;

    /**
     * 添加人 头像
     */
    private String creatorPic;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 同步字段0否1是
     */
    private Integer syncStatus;

    /**
     * 是否加入语音转换队列，0-否，1-是
     */
    @NotNull(message = "是否加入语音转换队列不可为空",groups = {AddGroup.class,UpdateGroup.class})
    private Integer isLfasr;

    /**
     * 是否已点评0：否1：是
     */
    private Integer isComment;

    /**
     * 主叫号码
     */
    private String ttNumber;

    /**
     * 标签值数组
     */
    private Integer[] sysTag;

    /**
     * 便于回写
     * 选中的系统标签值
     */
    private List<TagDto> sysTagUserCheck;

    /**
     * all tag 所有的标签
     */
    private List<TagDto> allTag;

    /**
     * 用户自增标签值数组
     */
    private String[] userTag;

    /**
     * 联系人部门
     */
    private String contactDepartment;

    /**
     * 联系人职位
     */
    private String contactPosition;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 沟通目的
     */
    private String communicateGoalName;

    /**
     * 沟通方式
     */
    private String communicateModeName;

    /**
     * ERP业务单号，例如商机编号 订单号等
     */
    private String orderNo;

    /**
     * AI 沟通记录的弹框
     */
    private String aiWindowUrl;

    /**
     * 沟通内容摘要
     */
    private CommunicateAiSummaryApiDto communicateAiSummaryApiDto;


    /**
     * 其他附属信息
     */
    private Object otherData;

}