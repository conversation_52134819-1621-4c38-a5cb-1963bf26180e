package com.vedeng.crm.business.business.service;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.crm.business.business.domain.dto.BusinessToOrderResponseDto;
import com.vedeng.crm.business.business.domain.dto.LinkOrderRequestDto;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.dto.BusinessCloseDto;

public interface CrmBusinessChanceService {

    /**
     * 创建商机
     * @param businessChanceDto
     * @return
     */
    BusinessChanceDto add(BusinessChanceDto businessChanceDto);

    /**
     * 商机详情
     * @param businessChanceId
     * @return
     */
    BusinessChanceDto detail(Integer businessChanceId);

    /**
     * 校验权限
     * @param businessChanceId
     */
    void checkPermission(Integer businessChanceId);

    /**
     * 更新商机
     * @param businessChanceDto
     * @return
     */
    BusinessChanceDto update(BusinessChanceDto businessChanceDto);

    /**
     * 商机转订单
     * @param businessChanceId
     * @return
     */
    BusinessToOrderResponseDto businessToOrder(Integer businessChanceId, Integer systemCode);

    boolean checkSkuRepeat(Integer businessChanceId);

    /**
     * 商机转订单验证
     * @param businessChanceId
     */
    void toOrderVerify(Integer businessChanceId);

    boolean checkHasSkutoOrderVerify(Integer businessChanceId);

    /**
     * 商机转报价
     * @param businessChanceId
     */
    Integer addQuote(Integer businessChanceId, CurrentUser currentUser);

    /**
     * 商机关联订单
     * @param linkOrderRequestDto
     */
    void linkOrder(LinkOrderRequestDto linkOrderRequestDto);

    void closeBusiness(BusinessCloseDto businessCloseDto);
}
