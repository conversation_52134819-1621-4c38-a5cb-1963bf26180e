<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.SaleorderGenerateMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.SaleorderGenerate" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 08 19:16:46 CST 2020.
    -->
    <id column="SALEORDER_ID" property="saleorderId" jdbcType="INTEGER" />
    <result column="QUOTEORDER_ID" property="quoteorderId" jdbcType="INTEGER" />
    <result column="PARENT_ID" property="parentId" jdbcType="INTEGER" />
    <result column="SALEORDER_NO" property="saleorderNo" jdbcType="VARCHAR" />
    <result column="M_SALEORDER_NO" property="mSaleorderNo" jdbcType="VARCHAR" />
    <result column="ORDER_TYPE" property="orderType" jdbcType="TINYINT" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="SOURCE" property="source" jdbcType="TINYINT" />
    <result column="CREATOR_ORG_ID" property="creatorOrgId" jdbcType="INTEGER" />
    <result column="CREATOR_ORG_NAME" property="creatorOrgName" jdbcType="VARCHAR" />
    <result column="ORG_ID" property="orgId" jdbcType="INTEGER" />
    <result column="ORG_NAME" property="orgName" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="INTEGER" />
    <result column="VALID_ORG_ID" property="validOrgId" jdbcType="INTEGER" />
    <result column="VALID_ORG_NAME" property="validOrgName" jdbcType="VARCHAR" />
    <result column="VALID_USER_ID" property="validUserId" jdbcType="INTEGER" />
    <result column="VALID_STATUS" property="validStatus" jdbcType="TINYINT" />
    <result column="VALID_TIME" property="validTime" jdbcType="BIGINT" />
    <result column="END_TIME" property="endTime" jdbcType="BIGINT" />
    <result column="STATUS" property="status" jdbcType="TINYINT" />
    <result column="PURCHASE_STATUS" property="purchaseStatus" jdbcType="TINYINT" />
    <result column="LOCKED_STATUS" property="lockedStatus" jdbcType="TINYINT" />
    <result column="INVOICE_STATUS" property="invoiceStatus" jdbcType="TINYINT" />
    <result column="INVOICE_TIME" property="invoiceTime" jdbcType="BIGINT" />
    <result column="PAYMENT_STATUS" property="paymentStatus" jdbcType="TINYINT" />
    <result column="PAYMENT_TIME" property="paymentTime" jdbcType="BIGINT" />
    <result column="DELIVERY_STATUS" property="deliveryStatus" jdbcType="TINYINT" />
    <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT" />
    <result column="IS_CUSTOMER_ARRIVAL" property="isCustomerArrival" jdbcType="TINYINT" />
    <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="TINYINT" />
    <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT" />
    <result column="SERVICE_STATUS" property="serviceStatus" jdbcType="TINYINT" />
    <result column="HAVE_ACCOUNT_PERIOD" property="haveAccountPeriod" jdbcType="TINYINT" />
    <result column="IS_PAYMENT" property="isPayment" jdbcType="TINYINT" />
    <result column="TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL" />
    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    <result column="CUSTOMER_TYPE" property="customerType" jdbcType="INTEGER" />
    <result column="CUSTOMER_NATURE" property="customerNature" jdbcType="INTEGER" />
    <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR" />
    <result column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER" />
    <result column="TRADER_CONTACT_NAME" property="traderContactName" jdbcType="VARCHAR" />
    <result column="TRADER_CONTACT_MOBILE" property="traderContactMobile" jdbcType="VARCHAR" />
    <result column="TRADER_CONTACT_TELEPHONE" property="traderContactTelephone" jdbcType="VARCHAR" />
    <result column="TRADER_ADDRESS_ID" property="traderAddressId" jdbcType="INTEGER" />
    <result column="TRADER_AREA_ID" property="traderAreaId" jdbcType="INTEGER" />
    <result column="TRADER_AREA" property="traderArea" jdbcType="VARCHAR" />
    <result column="TRADER_ADDRESS" property="traderAddress" jdbcType="VARCHAR" />
    <result column="TRADER_COMMENTS" property="traderComments" jdbcType="VARCHAR" />
    <result column="TAKE_TRADER_ID" property="takeTraderId" jdbcType="INTEGER" />
    <result column="TAKE_TRADER_NAME" property="takeTraderName" jdbcType="VARCHAR" />
    <result column="TAKE_TRADER_CONTACT_ID" property="takeTraderContactId" jdbcType="INTEGER" />
    <result column="TAKE_TRADER_CONTACT_NAME" property="takeTraderContactName" jdbcType="VARCHAR" />
    <result column="TAKE_TRADER_CONTACT_MOBILE" property="takeTraderContactMobile" jdbcType="VARCHAR" />
    <result column="TAKE_TRADER_CONTACT_TELEPHONE" property="takeTraderContactTelephone" jdbcType="VARCHAR" />
    <result column="TAKE_TRADER_ADDRESS_ID" property="takeTraderAddressId" jdbcType="INTEGER" />
    <result column="TAKE_TRADER_AREA_ID" property="takeTraderAreaId" jdbcType="INTEGER" />
    <result column="TAKE_TRADER_AREA" property="takeTraderArea" jdbcType="VARCHAR" />
    <result column="TAKE_TRADER_ADDRESS" property="takeTraderAddress" jdbcType="VARCHAR" />
    <result column="IS_SEND_INVOICE" property="isSendInvoice" jdbcType="TINYINT" />
    <result column="INVOICE_TRADER_ID" property="invoiceTraderId" jdbcType="INTEGER" />
    <result column="INVOICE_TRADER_NAME" property="invoiceTraderName" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_CONTACT_ID" property="invoiceTraderContactId" jdbcType="INTEGER" />
    <result column="INVOICE_TRADER_CONTACT_NAME" property="invoiceTraderContactName" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_CONTACT_MOBILE" property="invoiceTraderContactMobile" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_CONTACT_TELEPHONE" property="invoiceTraderContactTelephone" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_ADDRESS_ID" property="invoiceTraderAddressId" jdbcType="INTEGER" />
    <result column="INVOICE_TRADER_AREA_ID" property="invoiceTraderAreaId" jdbcType="INTEGER" />
    <result column="INVOICE_TRADER_AREA" property="invoiceTraderArea" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_ADDRESS" property="invoiceTraderAddress" jdbcType="VARCHAR" />
    <result column="SALES_AREA_ID" property="salesAreaId" jdbcType="INTEGER" />
    <result column="SALES_AREA" property="salesArea" jdbcType="VARCHAR" />
    <result column="TERMINAL_TRADER_ID" property="terminalTraderId" jdbcType="INTEGER" />
    <result column="TERMINAL_TRADER_NAME" property="terminalTraderName" jdbcType="VARCHAR" />
    <result column="TERMINAL_TRADER_TYPE" property="terminalTraderType" jdbcType="INTEGER" />
    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER" />
    <result column="FREIGHT_DESCRIPTION" property="freightDescription" jdbcType="INTEGER" />
    <result column="DELIVERY_TYPE" property="deliveryType" jdbcType="INTEGER" />
    <result column="LOGISTICS_ID" property="logisticsId" jdbcType="INTEGER" />
    <result column="PAYMENT_TYPE" property="paymentType" jdbcType="INTEGER" />
    <result column="PREPAID_AMOUNT" property="prepaidAmount" jdbcType="DECIMAL" />
    <result column="ACCOUNT_PERIOD_AMOUNT" property="accountPeriodAmount" jdbcType="DECIMAL" />
    <result column="PERIOD_DAY" property="periodDay" jdbcType="INTEGER" />
    <result column="LOGISTICS_COLLECTION" property="logisticsCollection" jdbcType="TINYINT" />
    <result column="RETAINAGE_AMOUNT" property="retainageAmount" jdbcType="DECIMAL" />
    <result column="RETAINAGE_AMOUNT_MONTH" property="retainageAmountMonth" jdbcType="INTEGER" />
    <result column="PAYMENT_COMMENTS" property="paymentComments" jdbcType="VARCHAR" />
    <result column="ADDITIONAL_CLAUSE" property="additionalClause" jdbcType="VARCHAR" />
    <result column="LOGISTICS_COMMENTS" property="logisticsComments" jdbcType="VARCHAR" />
    <result column="FINANCE_COMMENTS" property="financeComments" jdbcType="VARCHAR" />
    <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
    <result column="INVOICE_COMMENTS" property="invoiceComments" jdbcType="VARCHAR" />
    <result column="DELIVERY_DIRECT" property="deliveryDirect" jdbcType="TINYINT" />
    <result column="SUPPLIER_CLAUSE" property="supplierClause" jdbcType="VARCHAR" />
    <result column="HAVE_ADVANCE_PURCHASE" property="haveAdvancePurchase" jdbcType="TINYINT" />
    <result column="ADVANCE_PURCHASE_STATUS" property="advancePurchaseStatus" jdbcType="TINYINT" />
    <result column="ADVANCE_PURCHASE_COMMENTS" property="advancePurchaseComments" jdbcType="VARCHAR" />
    <result column="ADVANCE_PURCHASE_TIME" property="advancePurchaseTime" jdbcType="BIGINT" />
    <result column="IS_URGENT" property="isUrgent" jdbcType="TINYINT" />
    <result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL" />
    <result column="HAVE_COMMUNICATE" property="haveCommunicate" jdbcType="TINYINT" />
    <result column="PREPARE_COMMENTS" property="prepareComments" jdbcType="VARCHAR" />
    <result column="MARKETING_PLAN" property="marketingPlan" jdbcType="VARCHAR" />
    <result column="STATUS_COMMENTS" property="statusComments" jdbcType="INTEGER" />
    <result column="SYNC_STATUS" property="syncStatus" jdbcType="TINYINT" />
    <result column="LOGISTICS_API_SYNC" property="logisticsApiSync" jdbcType="TINYINT" />
    <result column="LOGISTICS_WXSEND_SYNC" property="logisticsWxsendSync" jdbcType="TINYINT" />
    <result column="SATISFY_INVOICE_TIME" property="satisfyInvoiceTime" jdbcType="BIGINT" />
    <result column="SATISFY_DELIVERY_TIME" property="satisfyDeliveryTime" jdbcType="BIGINT" />
    <result column="IS_SALES_PERFORMANCE" property="isSalesPerformance" jdbcType="TINYINT" />
    <result column="SALES_PERFORMANCE_TIME" property="salesPerformanceTime" jdbcType="BIGINT" />
    <result column="SALES_PERFORMANCE_MOD_TIME" property="salesPerformanceModTime" jdbcType="BIGINT" />
    <result column="IS_DELAY_INVOICE" property="isDelayInvoice" jdbcType="TINYINT" />
    <result column="INVOICE_METHOD" property="invoiceMethod" jdbcType="TINYINT" />
    <result column="LOCKED_REASON" property="lockedReason" jdbcType="VARCHAR" />
    <result column="COST_USER_IDS" property="costUserIds" jdbcType="VARCHAR" />
    <result column="OWNER_USER_ID" property="ownerUserId" jdbcType="INTEGER" />
    <result column="INVOICE_EMAIL" property="invoiceEmail" jdbcType="VARCHAR" />
    <result column="PAYMENT_MODE" property="paymentMode" jdbcType="TINYINT" />
    <result column="PAY_TYPE" property="payType" jdbcType="TINYINT" />
    <result column="IS_APPLY_INVOICE" property="isApplyInvoice" jdbcType="TINYINT" />
    <result column="APPLY_INVOICE_TIME" property="applyInvoiceTime" jdbcType="BIGINT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="ADK_SALEORDER_NO" property="adkSaleorderNo" jdbcType="VARCHAR" />
    <result column="CREATE_MOBILE" property="createMobile" jdbcType="VARCHAR" />
    <result column="BDTRADER_COMMENTS" property="bdtraderComments" jdbcType="VARCHAR" />
    <result column="CLOSE_COMMENTS" property="closeComments" jdbcType="VARCHAR" />
    <result column="BD_MOBILE_TIME" property="bdMobileTime" jdbcType="BIGINT" />
    <result column="WEB_TAKE_DELIVERY_TIME" property="webTakeDeliveryTime" jdbcType="BIGINT" />
    <result column="ACTION_ID" property="actionId" jdbcType="INTEGER" />
    <result column="IS_COUPONS" property="isCoupons" jdbcType="TINYINT" />
    <result column="EL_SALEORDRE_NO" property="elSaleordreNo" jdbcType="VARCHAR" />
    <result column="COUPONMONEY" property="couponmoney" jdbcType="DECIMAL" />
    <result column="ORIGINAL_AMOUNT" property="originalAmount" jdbcType="DECIMAL" />
    <result column="IS_PRINTOUT" property="isPrintout" jdbcType="TINYINT" />
    <result column="OUT_IS_FLAG" property="outIsFlag" jdbcType="TINYINT" />
    <result column="UPDATE_DATA_TIME" property="updateDataTime" jdbcType="TIMESTAMP" />
    <result column="REAL_PAY_AMOUNT" property="realPayAmount" jdbcType="DECIMAL" />
    <result column="REAL_RETURN_AMOUNT" property="realReturnAmount" jdbcType="DECIMAL" />
    <result column="REAL_TOTAL_AMOUNT" property="realTotalAmount" jdbcType="DECIMAL" />
    <result column="SEND_TO_PC" property="sendToPc" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 08 19:16:46 CST 2020.
    -->
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 08 19:16:46 CST 2020.
    -->
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 08 19:16:46 CST 2020.
    -->
    SALEORDER_ID, QUOTEORDER_ID, PARENT_ID, SALEORDER_NO, M_SALEORDER_NO, ORDER_TYPE, 
    COMPANY_ID, `SOURCE`, CREATOR_ORG_ID, CREATOR_ORG_NAME, ORG_ID, ORG_NAME, USER_ID, 
    VALID_ORG_ID, VALID_ORG_NAME, VALID_USER_ID, VALID_STATUS, VALID_TIME, END_TIME, 
    `STATUS`, PURCHASE_STATUS, LOCKED_STATUS, INVOICE_STATUS, INVOICE_TIME, PAYMENT_STATUS, 
    PAYMENT_TIME, DELIVERY_STATUS, DELIVERY_TIME, IS_CUSTOMER_ARRIVAL, ARRIVAL_STATUS, 
    ARRIVAL_TIME, SERVICE_STATUS, HAVE_ACCOUNT_PERIOD, IS_PAYMENT, TOTAL_AMOUNT, TRADER_ID, 
    CUSTOMER_TYPE, CUSTOMER_NATURE, TRADER_NAME, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, 
    TRADER_CONTACT_MOBILE, TRADER_CONTACT_TELEPHONE, TRADER_ADDRESS_ID, TRADER_AREA_ID, 
    TRADER_AREA, TRADER_ADDRESS, TRADER_COMMENTS, TAKE_TRADER_ID, TAKE_TRADER_NAME, TAKE_TRADER_CONTACT_ID, 
    TAKE_TRADER_CONTACT_NAME, TAKE_TRADER_CONTACT_MOBILE, TAKE_TRADER_CONTACT_TELEPHONE, 
    TAKE_TRADER_ADDRESS_ID, TAKE_TRADER_AREA_ID, TAKE_TRADER_AREA, TAKE_TRADER_ADDRESS, 
    IS_SEND_INVOICE, INVOICE_TRADER_ID, INVOICE_TRADER_NAME, INVOICE_TRADER_CONTACT_ID, 
    INVOICE_TRADER_CONTACT_NAME, INVOICE_TRADER_CONTACT_MOBILE, INVOICE_TRADER_CONTACT_TELEPHONE, 
    INVOICE_TRADER_ADDRESS_ID, INVOICE_TRADER_AREA_ID, INVOICE_TRADER_AREA, INVOICE_TRADER_ADDRESS, 
    SALES_AREA_ID, SALES_AREA, TERMINAL_TRADER_ID, TERMINAL_TRADER_NAME, TERMINAL_TRADER_TYPE, 
    INVOICE_TYPE, FREIGHT_DESCRIPTION, DELIVERY_TYPE, LOGISTICS_ID, PAYMENT_TYPE, PREPAID_AMOUNT, 
    ACCOUNT_PERIOD_AMOUNT, PERIOD_DAY, LOGISTICS_COLLECTION, RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH, 
    PAYMENT_COMMENTS, ADDITIONAL_CLAUSE, LOGISTICS_COMMENTS, FINANCE_COMMENTS, COMMENTS, 
    INVOICE_COMMENTS, DELIVERY_DIRECT, SUPPLIER_CLAUSE, HAVE_ADVANCE_PURCHASE, ADVANCE_PURCHASE_STATUS, 
    ADVANCE_PURCHASE_COMMENTS, ADVANCE_PURCHASE_TIME, IS_URGENT, URGENT_AMOUNT, HAVE_COMMUNICATE, 
    PREPARE_COMMENTS, MARKETING_PLAN, STATUS_COMMENTS, SYNC_STATUS, LOGISTICS_API_SYNC, 
    LOGISTICS_WXSEND_SYNC, SATISFY_INVOICE_TIME, SATISFY_DELIVERY_TIME, IS_SALES_PERFORMANCE, 
    SALES_PERFORMANCE_TIME, SALES_PERFORMANCE_MOD_TIME, IS_DELAY_INVOICE, INVOICE_METHOD, 
    LOCKED_REASON, COST_USER_IDS, OWNER_USER_ID, INVOICE_EMAIL, PAYMENT_MODE, PAY_TYPE, 
    IS_APPLY_INVOICE, APPLY_INVOICE_TIME, ADD_TIME, CREATOR, MOD_TIME, UPDATER, ADK_SALEORDER_NO, 
    CREATE_MOBILE, BDTRADER_COMMENTS, CLOSE_COMMENTS, BD_MOBILE_TIME, WEB_TAKE_DELIVERY_TIME, 
    ACTION_ID, IS_COUPONS, EL_SALEORDRE_NO, COUPONMONEY, ORIGINAL_AMOUNT, IS_PRINTOUT, 
    OUT_IS_FLAG, UPDATE_DATA_TIME, REAL_PAY_AMOUNT, REAL_RETURN_AMOUNT, REAL_TOTAL_AMOUNT, 
    SEND_TO_PC
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.vedeng.order.model.SaleorderGenerateExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 08 19:16:46 CST 2020.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_SALEORDER
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 08 19:16:46 CST 2020.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_SALEORDER
    where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 08 19:16:46 CST 2020.
    -->
    delete from T_SALEORDER
    where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.order.model.SaleorderGenerateExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 08 19:16:46 CST 2020.
    -->
    delete from T_SALEORDER
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.order.model.SaleorderGenerate" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 08 19:16:46 CST 2020.
    -->
    <selectKey resultType="java.lang.Integer" keyProperty="saleorderId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_SALEORDER (QUOTEORDER_ID, PARENT_ID, SALEORDER_NO, 
      M_SALEORDER_NO, ORDER_TYPE, COMPANY_ID, 
      `SOURCE`, CREATOR_ORG_ID, CREATOR_ORG_NAME, 
      ORG_ID, ORG_NAME, USER_ID, 
      VALID_ORG_ID, VALID_ORG_NAME, VALID_USER_ID, 
      VALID_STATUS, VALID_TIME, END_TIME, 
      `STATUS`, PURCHASE_STATUS, LOCKED_STATUS, 
      INVOICE_STATUS, INVOICE_TIME, PAYMENT_STATUS, 
      PAYMENT_TIME, DELIVERY_STATUS, DELIVERY_TIME, 
      IS_CUSTOMER_ARRIVAL, ARRIVAL_STATUS, ARRIVAL_TIME, 
      SERVICE_STATUS, HAVE_ACCOUNT_PERIOD, IS_PAYMENT, 
      TOTAL_AMOUNT, TRADER_ID, CUSTOMER_TYPE, 
      CUSTOMER_NATURE, TRADER_NAME, TRADER_CONTACT_ID, 
      TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, 
      TRADER_CONTACT_TELEPHONE, TRADER_ADDRESS_ID, 
      TRADER_AREA_ID, TRADER_AREA, TRADER_ADDRESS, 
      TRADER_COMMENTS, TAKE_TRADER_ID, TAKE_TRADER_NAME, 
      TAKE_TRADER_CONTACT_ID, TAKE_TRADER_CONTACT_NAME, 
      TAKE_TRADER_CONTACT_MOBILE, TAKE_TRADER_CONTACT_TELEPHONE, 
      TAKE_TRADER_ADDRESS_ID, TAKE_TRADER_AREA_ID, 
      TAKE_TRADER_AREA, TAKE_TRADER_ADDRESS, IS_SEND_INVOICE, 
      INVOICE_TRADER_ID, INVOICE_TRADER_NAME, INVOICE_TRADER_CONTACT_ID, 
      INVOICE_TRADER_CONTACT_NAME, INVOICE_TRADER_CONTACT_MOBILE, 
      INVOICE_TRADER_CONTACT_TELEPHONE, INVOICE_TRADER_ADDRESS_ID, 
      INVOICE_TRADER_AREA_ID, INVOICE_TRADER_AREA, 
      INVOICE_TRADER_ADDRESS, SALES_AREA_ID, SALES_AREA, 
      TERMINAL_TRADER_ID, TERMINAL_TRADER_NAME, TERMINAL_TRADER_TYPE, 
      INVOICE_TYPE, FREIGHT_DESCRIPTION, DELIVERY_TYPE, 
      LOGISTICS_ID, PAYMENT_TYPE, PREPAID_AMOUNT, 
      ACCOUNT_PERIOD_AMOUNT, PERIOD_DAY, LOGISTICS_COLLECTION, 
      RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH, 
      PAYMENT_COMMENTS, ADDITIONAL_CLAUSE, LOGISTICS_COMMENTS, 
      FINANCE_COMMENTS, COMMENTS, INVOICE_COMMENTS, 
      DELIVERY_DIRECT, SUPPLIER_CLAUSE, HAVE_ADVANCE_PURCHASE, 
      ADVANCE_PURCHASE_STATUS, ADVANCE_PURCHASE_COMMENTS, 
      ADVANCE_PURCHASE_TIME, IS_URGENT, URGENT_AMOUNT, 
      HAVE_COMMUNICATE, PREPARE_COMMENTS, MARKETING_PLAN, 
      STATUS_COMMENTS, SYNC_STATUS, LOGISTICS_API_SYNC, 
      LOGISTICS_WXSEND_SYNC, SATISFY_INVOICE_TIME, 
      SATISFY_DELIVERY_TIME, IS_SALES_PERFORMANCE, 
      SALES_PERFORMANCE_TIME, SALES_PERFORMANCE_MOD_TIME, 
      IS_DELAY_INVOICE, INVOICE_METHOD, LOCKED_REASON, 
      COST_USER_IDS, OWNER_USER_ID, INVOICE_EMAIL, 
      PAYMENT_MODE, PAY_TYPE, IS_APPLY_INVOICE, 
      APPLY_INVOICE_TIME, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, ADK_SALEORDER_NO, 
      CREATE_MOBILE, BDTRADER_COMMENTS, CLOSE_COMMENTS, 
      BD_MOBILE_TIME, WEB_TAKE_DELIVERY_TIME, ACTION_ID, 
      IS_COUPONS, EL_SALEORDRE_NO, COUPONMONEY, 
      ORIGINAL_AMOUNT, IS_PRINTOUT, OUT_IS_FLAG, 
      UPDATE_DATA_TIME, REAL_PAY_AMOUNT, REAL_RETURN_AMOUNT, 
      REAL_TOTAL_AMOUNT, SEND_TO_PC)
    values (#{quoteorderId,jdbcType=INTEGER}, #{parentId,jdbcType=INTEGER}, #{saleorderNo,jdbcType=VARCHAR}, 
      #{mSaleorderNo,jdbcType=VARCHAR}, #{orderType,jdbcType=TINYINT}, #{companyId,jdbcType=INTEGER}, 
      #{source,jdbcType=TINYINT}, #{creatorOrgId,jdbcType=INTEGER}, #{creatorOrgName,jdbcType=VARCHAR}, 
      #{orgId,jdbcType=INTEGER}, #{orgName,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER}, 
      #{validOrgId,jdbcType=INTEGER}, #{validOrgName,jdbcType=VARCHAR}, #{validUserId,jdbcType=INTEGER}, 
      #{validStatus,jdbcType=TINYINT}, #{validTime,jdbcType=BIGINT}, #{endTime,jdbcType=BIGINT}, 
      #{status,jdbcType=TINYINT}, #{purchaseStatus,jdbcType=TINYINT}, #{lockedStatus,jdbcType=TINYINT}, 
      #{invoiceStatus,jdbcType=TINYINT}, #{invoiceTime,jdbcType=BIGINT}, #{paymentStatus,jdbcType=TINYINT}, 
      #{paymentTime,jdbcType=BIGINT}, #{deliveryStatus,jdbcType=TINYINT}, #{deliveryTime,jdbcType=BIGINT}, 
      #{isCustomerArrival,jdbcType=TINYINT}, #{arrivalStatus,jdbcType=TINYINT}, #{arrivalTime,jdbcType=BIGINT}, 
      #{serviceStatus,jdbcType=TINYINT}, #{haveAccountPeriod,jdbcType=TINYINT}, #{isPayment,jdbcType=TINYINT}, 
      #{totalAmount,jdbcType=DECIMAL}, #{traderId,jdbcType=INTEGER}, #{customerType,jdbcType=INTEGER}, 
      #{customerNature,jdbcType=INTEGER}, #{traderName,jdbcType=VARCHAR}, #{traderContactId,jdbcType=INTEGER}, 
      #{traderContactName,jdbcType=VARCHAR}, #{traderContactMobile,jdbcType=VARCHAR}, 
      #{traderContactTelephone,jdbcType=VARCHAR}, #{traderAddressId,jdbcType=INTEGER}, 
      #{traderAreaId,jdbcType=INTEGER}, #{traderArea,jdbcType=VARCHAR}, #{traderAddress,jdbcType=VARCHAR}, 
      #{traderComments,jdbcType=VARCHAR}, #{takeTraderId,jdbcType=INTEGER}, #{takeTraderName,jdbcType=VARCHAR}, 
      #{takeTraderContactId,jdbcType=INTEGER}, #{takeTraderContactName,jdbcType=VARCHAR}, 
      #{takeTraderContactMobile,jdbcType=VARCHAR}, #{takeTraderContactTelephone,jdbcType=VARCHAR}, 
      #{takeTraderAddressId,jdbcType=INTEGER}, #{takeTraderAreaId,jdbcType=INTEGER}, 
      #{takeTraderArea,jdbcType=VARCHAR}, #{takeTraderAddress,jdbcType=VARCHAR}, #{isSendInvoice,jdbcType=TINYINT}, 
      #{invoiceTraderId,jdbcType=INTEGER}, #{invoiceTraderName,jdbcType=VARCHAR}, #{invoiceTraderContactId,jdbcType=INTEGER}, 
      #{invoiceTraderContactName,jdbcType=VARCHAR}, #{invoiceTraderContactMobile,jdbcType=VARCHAR}, 
      #{invoiceTraderContactTelephone,jdbcType=VARCHAR}, #{invoiceTraderAddressId,jdbcType=INTEGER}, 
      #{invoiceTraderAreaId,jdbcType=INTEGER}, #{invoiceTraderArea,jdbcType=VARCHAR}, 
      #{invoiceTraderAddress,jdbcType=VARCHAR}, #{salesAreaId,jdbcType=INTEGER}, #{salesArea,jdbcType=VARCHAR}, 
      #{terminalTraderId,jdbcType=INTEGER}, #{terminalTraderName,jdbcType=VARCHAR}, #{terminalTraderType,jdbcType=INTEGER}, 
      #{invoiceType,jdbcType=INTEGER}, #{freightDescription,jdbcType=INTEGER}, #{deliveryType,jdbcType=INTEGER}, 
      #{logisticsId,jdbcType=INTEGER}, #{paymentType,jdbcType=INTEGER}, #{prepaidAmount,jdbcType=DECIMAL}, 
      #{accountPeriodAmount,jdbcType=DECIMAL}, #{periodDay,jdbcType=INTEGER}, #{logisticsCollection,jdbcType=TINYINT}, 
      #{retainageAmount,jdbcType=DECIMAL}, #{retainageAmountMonth,jdbcType=INTEGER}, 
      #{paymentComments,jdbcType=VARCHAR}, #{additionalClause,jdbcType=VARCHAR}, #{logisticsComments,jdbcType=VARCHAR}, 
      #{financeComments,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, #{invoiceComments,jdbcType=VARCHAR}, 
      #{deliveryDirect,jdbcType=TINYINT}, #{supplierClause,jdbcType=VARCHAR}, #{haveAdvancePurchase,jdbcType=TINYINT}, 
      #{advancePurchaseStatus,jdbcType=TINYINT}, #{advancePurchaseComments,jdbcType=VARCHAR}, 
      #{advancePurchaseTime,jdbcType=BIGINT}, #{isUrgent,jdbcType=TINYINT}, #{urgentAmount,jdbcType=DECIMAL}, 
      #{haveCommunicate,jdbcType=TINYINT}, #{prepareComments,jdbcType=VARCHAR}, #{marketingPlan,jdbcType=VARCHAR}, 
      #{statusComments,jdbcType=INTEGER}, #{syncStatus,jdbcType=TINYINT}, #{logisticsApiSync,jdbcType=TINYINT}, 
      #{logisticsWxsendSync,jdbcType=TINYINT}, #{satisfyInvoiceTime,jdbcType=BIGINT}, 
      #{satisfyDeliveryTime,jdbcType=BIGINT}, #{isSalesPerformance,jdbcType=TINYINT}, 
      #{salesPerformanceTime,jdbcType=BIGINT}, #{salesPerformanceModTime,jdbcType=BIGINT}, 
      #{isDelayInvoice,jdbcType=TINYINT}, #{invoiceMethod,jdbcType=TINYINT}, #{lockedReason,jdbcType=VARCHAR}, 
      #{costUserIds,jdbcType=VARCHAR}, #{ownerUserId,jdbcType=INTEGER}, #{invoiceEmail,jdbcType=VARCHAR}, 
      #{paymentMode,jdbcType=TINYINT}, #{payType,jdbcType=TINYINT}, #{isApplyInvoice,jdbcType=TINYINT}, 
      #{applyInvoiceTime,jdbcType=BIGINT}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{adkSaleorderNo,jdbcType=VARCHAR}, 
      #{createMobile,jdbcType=VARCHAR}, #{bdtraderComments,jdbcType=VARCHAR}, #{closeComments,jdbcType=VARCHAR}, 
      #{bdMobileTime,jdbcType=BIGINT}, #{webTakeDeliveryTime,jdbcType=BIGINT}, #{actionId,jdbcType=INTEGER}, 
      #{isCoupons,jdbcType=TINYINT}, #{elSaleordreNo,jdbcType=VARCHAR}, #{couponmoney,jdbcType=DECIMAL}, 
      #{originalAmount,jdbcType=DECIMAL}, #{isPrintout,jdbcType=TINYINT}, #{outIsFlag,jdbcType=TINYINT}, 
      #{updateDataTime,jdbcType=TIMESTAMP}, #{realPayAmount,jdbcType=DECIMAL}, #{realReturnAmount,jdbcType=DECIMAL}, 
      #{realTotalAmount,jdbcType=DECIMAL}, #{sendToPc,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.order.model.SaleorderGenerate" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 08 19:16:46 CST 2020.
    -->
    <selectKey resultType="java.lang.Integer" keyProperty="saleorderId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_SALEORDER
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="quoteorderId != null" >
        QUOTEORDER_ID,
      </if>
      <if test="parentId != null" >
        PARENT_ID,
      </if>
      <if test="saleorderNo != null" >
        SALEORDER_NO,
      </if>
      <if test="mSaleorderNo != null" >
        M_SALEORDER_NO,
      </if>
      <if test="orderType != null" >
        ORDER_TYPE,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="source != null" >
        `SOURCE`,
      </if>
      <if test="creatorOrgId != null" >
        CREATOR_ORG_ID,
      </if>
      <if test="creatorOrgName != null" >
        CREATOR_ORG_NAME,
      </if>
      <if test="orgId != null" >
        ORG_ID,
      </if>
      <if test="orgName != null" >
        ORG_NAME,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="validOrgId != null" >
        VALID_ORG_ID,
      </if>
      <if test="validOrgName != null" >
        VALID_ORG_NAME,
      </if>
      <if test="validUserId != null" >
        VALID_USER_ID,
      </if>
      <if test="validStatus != null" >
        VALID_STATUS,
      </if>
      <if test="validTime != null" >
        VALID_TIME,
      </if>
      <if test="endTime != null" >
        END_TIME,
      </if>
      <if test="status != null" >
        `STATUS`,
      </if>
      <if test="purchaseStatus != null" >
        PURCHASE_STATUS,
      </if>
      <if test="lockedStatus != null" >
        LOCKED_STATUS,
      </if>
      <if test="invoiceStatus != null" >
        INVOICE_STATUS,
      </if>
      <if test="invoiceTime != null" >
        INVOICE_TIME,
      </if>
      <if test="paymentStatus != null" >
        PAYMENT_STATUS,
      </if>
      <if test="paymentTime != null" >
        PAYMENT_TIME,
      </if>
      <if test="deliveryStatus != null" >
        DELIVERY_STATUS,
      </if>
      <if test="deliveryTime != null" >
        DELIVERY_TIME,
      </if>
      <if test="isCustomerArrival != null" >
        IS_CUSTOMER_ARRIVAL,
      </if>
      <if test="arrivalStatus != null" >
        ARRIVAL_STATUS,
      </if>
      <if test="arrivalTime != null" >
        ARRIVAL_TIME,
      </if>
      <if test="serviceStatus != null" >
        SERVICE_STATUS,
      </if>
      <if test="haveAccountPeriod != null" >
        HAVE_ACCOUNT_PERIOD,
      </if>
      <if test="isPayment != null" >
        IS_PAYMENT,
      </if>
      <if test="totalAmount != null" >
        TOTAL_AMOUNT,
      </if>
      <if test="traderId != null" >
        TRADER_ID,
      </if>
      <if test="customerType != null" >
        CUSTOMER_TYPE,
      </if>
      <if test="customerNature != null" >
        CUSTOMER_NATURE,
      </if>
      <if test="traderName != null" >
        TRADER_NAME,
      </if>
      <if test="traderContactId != null" >
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null" >
        TRADER_CONTACT_NAME,
      </if>
      <if test="traderContactMobile != null" >
        TRADER_CONTACT_MOBILE,
      </if>
      <if test="traderContactTelephone != null" >
        TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="traderAddressId != null" >
        TRADER_ADDRESS_ID,
      </if>
      <if test="traderAreaId != null" >
        TRADER_AREA_ID,
      </if>
      <if test="traderArea != null" >
        TRADER_AREA,
      </if>
      <if test="traderAddress != null" >
        TRADER_ADDRESS,
      </if>
      <if test="traderComments != null" >
        TRADER_COMMENTS,
      </if>
      <if test="takeTraderId != null" >
        TAKE_TRADER_ID,
      </if>
      <if test="takeTraderName != null" >
        TAKE_TRADER_NAME,
      </if>
      <if test="takeTraderContactId != null" >
        TAKE_TRADER_CONTACT_ID,
      </if>
      <if test="takeTraderContactName != null" >
        TAKE_TRADER_CONTACT_NAME,
      </if>
      <if test="takeTraderContactMobile != null" >
        TAKE_TRADER_CONTACT_MOBILE,
      </if>
      <if test="takeTraderContactTelephone != null" >
        TAKE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="takeTraderAddressId != null" >
        TAKE_TRADER_ADDRESS_ID,
      </if>
      <if test="takeTraderAreaId != null" >
        TAKE_TRADER_AREA_ID,
      </if>
      <if test="takeTraderArea != null" >
        TAKE_TRADER_AREA,
      </if>
      <if test="takeTraderAddress != null" >
        TAKE_TRADER_ADDRESS,
      </if>
      <if test="isSendInvoice != null" >
        IS_SEND_INVOICE,
      </if>
      <if test="invoiceTraderId != null" >
        INVOICE_TRADER_ID,
      </if>
      <if test="invoiceTraderName != null" >
        INVOICE_TRADER_NAME,
      </if>
      <if test="invoiceTraderContactId != null" >
        INVOICE_TRADER_CONTACT_ID,
      </if>
      <if test="invoiceTraderContactName != null" >
        INVOICE_TRADER_CONTACT_NAME,
      </if>
      <if test="invoiceTraderContactMobile != null" >
        INVOICE_TRADER_CONTACT_MOBILE,
      </if>
      <if test="invoiceTraderContactTelephone != null" >
        INVOICE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="invoiceTraderAddressId != null" >
        INVOICE_TRADER_ADDRESS_ID,
      </if>
      <if test="invoiceTraderAreaId != null" >
        INVOICE_TRADER_AREA_ID,
      </if>
      <if test="invoiceTraderArea != null" >
        INVOICE_TRADER_AREA,
      </if>
      <if test="invoiceTraderAddress != null" >
        INVOICE_TRADER_ADDRESS,
      </if>
      <if test="salesAreaId != null" >
        SALES_AREA_ID,
      </if>
      <if test="salesArea != null" >
        SALES_AREA,
      </if>
      <if test="terminalTraderId != null" >
        TERMINAL_TRADER_ID,
      </if>
      <if test="terminalTraderName != null" >
        TERMINAL_TRADER_NAME,
      </if>
      <if test="terminalTraderType != null" >
        TERMINAL_TRADER_TYPE,
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE,
      </if>
      <if test="freightDescription != null" >
        FREIGHT_DESCRIPTION,
      </if>
      <if test="deliveryType != null" >
        DELIVERY_TYPE,
      </if>
      <if test="logisticsId != null" >
        LOGISTICS_ID,
      </if>
      <if test="paymentType != null" >
        PAYMENT_TYPE,
      </if>
      <if test="prepaidAmount != null" >
        PREPAID_AMOUNT,
      </if>
      <if test="accountPeriodAmount != null" >
        ACCOUNT_PERIOD_AMOUNT,
      </if>
      <if test="periodDay != null" >
        PERIOD_DAY,
      </if>
      <if test="logisticsCollection != null" >
        LOGISTICS_COLLECTION,
      </if>
      <if test="retainageAmount != null" >
        RETAINAGE_AMOUNT,
      </if>
      <if test="retainageAmountMonth != null" >
        RETAINAGE_AMOUNT_MONTH,
      </if>
      <if test="paymentComments != null" >
        PAYMENT_COMMENTS,
      </if>
      <if test="additionalClause != null" >
        ADDITIONAL_CLAUSE,
      </if>
      <if test="logisticsComments != null" >
        LOGISTICS_COMMENTS,
      </if>
      <if test="financeComments != null" >
        FINANCE_COMMENTS,
      </if>
      <if test="comments != null" >
        COMMENTS,
      </if>
      <if test="invoiceComments != null" >
        INVOICE_COMMENTS,
      </if>
      <if test="deliveryDirect != null" >
        DELIVERY_DIRECT,
      </if>
      <if test="supplierClause != null" >
        SUPPLIER_CLAUSE,
      </if>
      <if test="haveAdvancePurchase != null" >
        HAVE_ADVANCE_PURCHASE,
      </if>
      <if test="advancePurchaseStatus != null" >
        ADVANCE_PURCHASE_STATUS,
      </if>
      <if test="advancePurchaseComments != null" >
        ADVANCE_PURCHASE_COMMENTS,
      </if>
      <if test="advancePurchaseTime != null" >
        ADVANCE_PURCHASE_TIME,
      </if>
      <if test="isUrgent != null" >
        IS_URGENT,
      </if>
      <if test="urgentAmount != null" >
        URGENT_AMOUNT,
      </if>
      <if test="haveCommunicate != null" >
        HAVE_COMMUNICATE,
      </if>
      <if test="prepareComments != null" >
        PREPARE_COMMENTS,
      </if>
      <if test="marketingPlan != null" >
        MARKETING_PLAN,
      </if>
      <if test="statusComments != null" >
        STATUS_COMMENTS,
      </if>
      <if test="syncStatus != null" >
        SYNC_STATUS,
      </if>
      <if test="logisticsApiSync != null" >
        LOGISTICS_API_SYNC,
      </if>
      <if test="logisticsWxsendSync != null" >
        LOGISTICS_WXSEND_SYNC,
      </if>
      <if test="satisfyInvoiceTime != null" >
        SATISFY_INVOICE_TIME,
      </if>
      <if test="satisfyDeliveryTime != null" >
        SATISFY_DELIVERY_TIME,
      </if>
      <if test="isSalesPerformance != null" >
        IS_SALES_PERFORMANCE,
      </if>
      <if test="salesPerformanceTime != null" >
        SALES_PERFORMANCE_TIME,
      </if>
      <if test="salesPerformanceModTime != null" >
        SALES_PERFORMANCE_MOD_TIME,
      </if>
      <if test="isDelayInvoice != null" >
        IS_DELAY_INVOICE,
      </if>
      <if test="invoiceMethod != null" >
        INVOICE_METHOD,
      </if>
      <if test="lockedReason != null" >
        LOCKED_REASON,
      </if>
      <if test="costUserIds != null" >
        COST_USER_IDS,
      </if>
      <if test="ownerUserId != null" >
        OWNER_USER_ID,
      </if>
      <if test="invoiceEmail != null" >
        INVOICE_EMAIL,
      </if>
      <if test="paymentMode != null" >
        PAYMENT_MODE,
      </if>
      <if test="payType != null" >
        PAY_TYPE,
      </if>
      <if test="isApplyInvoice != null" >
        IS_APPLY_INVOICE,
      </if>
      <if test="applyInvoiceTime != null" >
        APPLY_INVOICE_TIME,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="adkSaleorderNo != null" >
        ADK_SALEORDER_NO,
      </if>
      <if test="createMobile != null" >
        CREATE_MOBILE,
      </if>
      <if test="bdtraderComments != null" >
        BDTRADER_COMMENTS,
      </if>
      <if test="closeComments != null" >
        CLOSE_COMMENTS,
      </if>
      <if test="bdMobileTime != null" >
        BD_MOBILE_TIME,
      </if>
      <if test="webTakeDeliveryTime != null" >
        WEB_TAKE_DELIVERY_TIME,
      </if>
      <if test="actionId != null" >
        ACTION_ID,
      </if>
      <if test="isCoupons != null" >
        IS_COUPONS,
      </if>
      <if test="elSaleordreNo != null" >
        EL_SALEORDRE_NO,
      </if>
      <if test="couponmoney != null" >
        COUPONMONEY,
      </if>
      <if test="originalAmount != null" >
        ORIGINAL_AMOUNT,
      </if>
      <if test="isPrintout != null" >
        IS_PRINTOUT,
      </if>
      <if test="outIsFlag != null" >
        OUT_IS_FLAG,
      </if>
      <if test="updateDataTime != null" >
        UPDATE_DATA_TIME,
      </if>
      <if test="realPayAmount != null" >
        REAL_PAY_AMOUNT,
      </if>
      <if test="realReturnAmount != null" >
        REAL_RETURN_AMOUNT,
      </if>
      <if test="realTotalAmount != null" >
        REAL_TOTAL_AMOUNT,
      </if>
      <if test="sendToPc != null" >
        SEND_TO_PC,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="quoteorderId != null" >
        #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="saleorderNo != null" >
        #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="mSaleorderNo != null" >
        #{mSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="source != null" >
        #{source,jdbcType=TINYINT},
      </if>
      <if test="creatorOrgId != null" >
        #{creatorOrgId,jdbcType=INTEGER},
      </if>
      <if test="creatorOrgName != null" >
        #{creatorOrgName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null" >
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="validOrgId != null" >
        #{validOrgId,jdbcType=INTEGER},
      </if>
      <if test="validOrgName != null" >
        #{validOrgName,jdbcType=VARCHAR},
      </if>
      <if test="validUserId != null" >
        #{validUserId,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null" >
        #{validStatus,jdbcType=TINYINT},
      </if>
      <if test="validTime != null" >
        #{validTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="purchaseStatus != null" >
        #{purchaseStatus,jdbcType=TINYINT},
      </if>
      <if test="lockedStatus != null" >
        #{lockedStatus,jdbcType=TINYINT},
      </if>
      <if test="invoiceStatus != null" >
        #{invoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="invoiceTime != null" >
        #{invoiceTime,jdbcType=BIGINT},
      </if>
      <if test="paymentStatus != null" >
        #{paymentStatus,jdbcType=TINYINT},
      </if>
      <if test="paymentTime != null" >
        #{paymentTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryStatus != null" >
        #{deliveryStatus,jdbcType=TINYINT},
      </if>
      <if test="deliveryTime != null" >
        #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="isCustomerArrival != null" >
        #{isCustomerArrival,jdbcType=TINYINT},
      </if>
      <if test="arrivalStatus != null" >
        #{arrivalStatus,jdbcType=TINYINT},
      </if>
      <if test="arrivalTime != null" >
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="serviceStatus != null" >
        #{serviceStatus,jdbcType=TINYINT},
      </if>
      <if test="haveAccountPeriod != null" >
        #{haveAccountPeriod,jdbcType=TINYINT},
      </if>
      <if test="isPayment != null" >
        #{isPayment,jdbcType=TINYINT},
      </if>
      <if test="totalAmount != null" >
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="traderId != null" >
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="customerType != null" >
        #{customerType,jdbcType=INTEGER},
      </if>
      <if test="customerNature != null" >
        #{customerNature,jdbcType=INTEGER},
      </if>
      <if test="traderName != null" >
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null" >
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null" >
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null" >
        #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null" >
        #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null" >
        #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="traderAreaId != null" >
        #{traderAreaId,jdbcType=INTEGER},
      </if>
      <if test="traderArea != null" >
        #{traderArea,jdbcType=VARCHAR},
      </if>
      <if test="traderAddress != null" >
        #{traderAddress,jdbcType=VARCHAR},
      </if>
      <if test="traderComments != null" >
        #{traderComments,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderId != null" >
        #{takeTraderId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderName != null" >
        #{takeTraderName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactId != null" >
        #{takeTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderContactName != null" >
        #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactMobile != null" >
        #{takeTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null" >
        #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddressId != null" >
        #{takeTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderAreaId != null" >
        #{takeTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderArea != null" >
        #{takeTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddress != null" >
        #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="isSendInvoice != null" >
        #{isSendInvoice,jdbcType=TINYINT},
      </if>
      <if test="invoiceTraderId != null" >
        #{invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderName != null" >
        #{invoiceTraderName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactId != null" >
        #{invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderContactName != null" >
        #{invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactMobile != null" >
        #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactTelephone != null" >
        #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddressId != null" >
        #{invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderAreaId != null" >
        #{invoiceTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderArea != null" >
        #{invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddress != null" >
        #{invoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="salesAreaId != null" >
        #{salesAreaId,jdbcType=INTEGER},
      </if>
      <if test="salesArea != null" >
        #{salesArea,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null" >
        #{terminalTraderId,jdbcType=INTEGER},
      </if>
      <if test="terminalTraderName != null" >
        #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderType != null" >
        #{terminalTraderType,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null" >
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="freightDescription != null" >
        #{freightDescription,jdbcType=INTEGER},
      </if>
      <if test="deliveryType != null" >
        #{deliveryType,jdbcType=INTEGER},
      </if>
      <if test="logisticsId != null" >
        #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null" >
        #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="prepaidAmount != null" >
        #{prepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodAmount != null" >
        #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null" >
        #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="logisticsCollection != null" >
        #{logisticsCollection,jdbcType=TINYINT},
      </if>
      <if test="retainageAmount != null" >
        #{retainageAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmountMonth != null" >
        #{retainageAmountMonth,jdbcType=INTEGER},
      </if>
      <if test="paymentComments != null" >
        #{paymentComments,jdbcType=VARCHAR},
      </if>
      <if test="additionalClause != null" >
        #{additionalClause,jdbcType=VARCHAR},
      </if>
      <if test="logisticsComments != null" >
        #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="financeComments != null" >
        #{financeComments,jdbcType=VARCHAR},
      </if>
      <if test="comments != null" >
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="invoiceComments != null" >
        #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null" >
        #{deliveryDirect,jdbcType=TINYINT},
      </if>
      <if test="supplierClause != null" >
        #{supplierClause,jdbcType=VARCHAR},
      </if>
      <if test="haveAdvancePurchase != null" >
        #{haveAdvancePurchase,jdbcType=TINYINT},
      </if>
      <if test="advancePurchaseStatus != null" >
        #{advancePurchaseStatus,jdbcType=TINYINT},
      </if>
      <if test="advancePurchaseComments != null" >
        #{advancePurchaseComments,jdbcType=VARCHAR},
      </if>
      <if test="advancePurchaseTime != null" >
        #{advancePurchaseTime,jdbcType=BIGINT},
      </if>
      <if test="isUrgent != null" >
        #{isUrgent,jdbcType=TINYINT},
      </if>
      <if test="urgentAmount != null" >
        #{urgentAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveCommunicate != null" >
        #{haveCommunicate,jdbcType=TINYINT},
      </if>
      <if test="prepareComments != null" >
        #{prepareComments,jdbcType=VARCHAR},
      </if>
      <if test="marketingPlan != null" >
        #{marketingPlan,jdbcType=VARCHAR},
      </if>
      <if test="statusComments != null" >
        #{statusComments,jdbcType=INTEGER},
      </if>
      <if test="syncStatus != null" >
        #{syncStatus,jdbcType=TINYINT},
      </if>
      <if test="logisticsApiSync != null" >
        #{logisticsApiSync,jdbcType=TINYINT},
      </if>
      <if test="logisticsWxsendSync != null" >
        #{logisticsWxsendSync,jdbcType=TINYINT},
      </if>
      <if test="satisfyInvoiceTime != null" >
        #{satisfyInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="satisfyDeliveryTime != null" >
        #{satisfyDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="isSalesPerformance != null" >
        #{isSalesPerformance,jdbcType=TINYINT},
      </if>
      <if test="salesPerformanceTime != null" >
        #{salesPerformanceTime,jdbcType=BIGINT},
      </if>
      <if test="salesPerformanceModTime != null" >
        #{salesPerformanceModTime,jdbcType=BIGINT},
      </if>
      <if test="isDelayInvoice != null" >
        #{isDelayInvoice,jdbcType=TINYINT},
      </if>
      <if test="invoiceMethod != null" >
        #{invoiceMethod,jdbcType=TINYINT},
      </if>
      <if test="lockedReason != null" >
        #{lockedReason,jdbcType=VARCHAR},
      </if>
      <if test="costUserIds != null" >
        #{costUserIds,jdbcType=VARCHAR},
      </if>
      <if test="ownerUserId != null" >
        #{ownerUserId,jdbcType=INTEGER},
      </if>
      <if test="invoiceEmail != null" >
        #{invoiceEmail,jdbcType=VARCHAR},
      </if>
      <if test="paymentMode != null" >
        #{paymentMode,jdbcType=TINYINT},
      </if>
      <if test="payType != null" >
        #{payType,jdbcType=TINYINT},
      </if>
      <if test="isApplyInvoice != null" >
        #{isApplyInvoice,jdbcType=TINYINT},
      </if>
      <if test="applyInvoiceTime != null" >
        #{applyInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="adkSaleorderNo != null" >
        #{adkSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null" >
        #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="bdtraderComments != null" >
        #{bdtraderComments,jdbcType=VARCHAR},
      </if>
      <if test="closeComments != null" >
        #{closeComments,jdbcType=VARCHAR},
      </if>
      <if test="bdMobileTime != null" >
        #{bdMobileTime,jdbcType=BIGINT},
      </if>
      <if test="webTakeDeliveryTime != null" >
        #{webTakeDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="actionId != null" >
        #{actionId,jdbcType=INTEGER},
      </if>
      <if test="isCoupons != null" >
        #{isCoupons,jdbcType=TINYINT},
      </if>
      <if test="elSaleordreNo != null" >
        #{elSaleordreNo,jdbcType=VARCHAR},
      </if>
      <if test="couponmoney != null" >
        #{couponmoney,jdbcType=DECIMAL},
      </if>
      <if test="originalAmount != null" >
        #{originalAmount,jdbcType=DECIMAL},
      </if>
      <if test="isPrintout != null" >
        #{isPrintout,jdbcType=TINYINT},
      </if>
      <if test="outIsFlag != null" >
        #{outIsFlag,jdbcType=TINYINT},
      </if>
      <if test="updateDataTime != null" >
        #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realPayAmount != null" >
        #{realPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="realReturnAmount != null" >
        #{realReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="realTotalAmount != null" >
        #{realTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="sendToPc != null" >
        #{sendToPc,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.order.model.SaleorderGenerateExample" resultType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 08 19:16:46 CST 2020.
    -->
    select count(*) from T_SALEORDER
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 08 19:16:46 CST 2020.
    -->
    update T_SALEORDER
    <set >
      <if test="record.saleorderId != null" >
        SALEORDER_ID = #{record.saleorderId,jdbcType=INTEGER},
      </if>
      <if test="record.quoteorderId != null" >
        QUOTEORDER_ID = #{record.quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="record.parentId != null" >
        PARENT_ID = #{record.parentId,jdbcType=INTEGER},
      </if>
      <if test="record.saleorderNo != null" >
        SALEORDER_NO = #{record.saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.mSaleorderNo != null" >
        M_SALEORDER_NO = #{record.mSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null" >
        ORDER_TYPE = #{record.orderType,jdbcType=TINYINT},
      </if>
      <if test="record.companyId != null" >
        COMPANY_ID = #{record.companyId,jdbcType=INTEGER},
      </if>
      <if test="record.source != null" >
        `SOURCE` = #{record.source,jdbcType=TINYINT},
      </if>
      <if test="record.creatorOrgId != null" >
        CREATOR_ORG_ID = #{record.creatorOrgId,jdbcType=INTEGER},
      </if>
      <if test="record.creatorOrgName != null" >
        CREATOR_ORG_NAME = #{record.creatorOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null" >
        ORG_ID = #{record.orgId,jdbcType=INTEGER},
      </if>
      <if test="record.orgName != null" >
        ORG_NAME = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null" >
        USER_ID = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.validOrgId != null" >
        VALID_ORG_ID = #{record.validOrgId,jdbcType=INTEGER},
      </if>
      <if test="record.validOrgName != null" >
        VALID_ORG_NAME = #{record.validOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.validUserId != null" >
        VALID_USER_ID = #{record.validUserId,jdbcType=INTEGER},
      </if>
      <if test="record.validStatus != null" >
        VALID_STATUS = #{record.validStatus,jdbcType=TINYINT},
      </if>
      <if test="record.validTime != null" >
        VALID_TIME = #{record.validTime,jdbcType=BIGINT},
      </if>
      <if test="record.endTime != null" >
        END_TIME = #{record.endTime,jdbcType=BIGINT},
      </if>
      <if test="record.status != null" >
        `STATUS` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.purchaseStatus != null" >
        PURCHASE_STATUS = #{record.purchaseStatus,jdbcType=TINYINT},
      </if>
      <if test="record.lockedStatus != null" >
        LOCKED_STATUS = #{record.lockedStatus,jdbcType=TINYINT},
      </if>
      <if test="record.invoiceStatus != null" >
        INVOICE_STATUS = #{record.invoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="record.invoiceTime != null" >
        INVOICE_TIME = #{record.invoiceTime,jdbcType=BIGINT},
      </if>
      <if test="record.paymentStatus != null" >
        PAYMENT_STATUS = #{record.paymentStatus,jdbcType=TINYINT},
      </if>
      <if test="record.paymentTime != null" >
        PAYMENT_TIME = #{record.paymentTime,jdbcType=BIGINT},
      </if>
      <if test="record.deliveryStatus != null" >
        DELIVERY_STATUS = #{record.deliveryStatus,jdbcType=TINYINT},
      </if>
      <if test="record.deliveryTime != null" >
        DELIVERY_TIME = #{record.deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="record.isCustomerArrival != null" >
        IS_CUSTOMER_ARRIVAL = #{record.isCustomerArrival,jdbcType=TINYINT},
      </if>
      <if test="record.arrivalStatus != null" >
        ARRIVAL_STATUS = #{record.arrivalStatus,jdbcType=TINYINT},
      </if>
      <if test="record.arrivalTime != null" >
        ARRIVAL_TIME = #{record.arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="record.serviceStatus != null" >
        SERVICE_STATUS = #{record.serviceStatus,jdbcType=TINYINT},
      </if>
      <if test="record.haveAccountPeriod != null" >
        HAVE_ACCOUNT_PERIOD = #{record.haveAccountPeriod,jdbcType=TINYINT},
      </if>
      <if test="record.isPayment != null" >
        IS_PAYMENT = #{record.isPayment,jdbcType=TINYINT},
      </if>
      <if test="record.totalAmount != null" >
        TOTAL_AMOUNT = #{record.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.traderId != null" >
        TRADER_ID = #{record.traderId,jdbcType=INTEGER},
      </if>
      <if test="record.customerType != null" >
        CUSTOMER_TYPE = #{record.customerType,jdbcType=INTEGER},
      </if>
      <if test="record.customerNature != null" >
        CUSTOMER_NATURE = #{record.customerNature,jdbcType=INTEGER},
      </if>
      <if test="record.traderName != null" >
        TRADER_NAME = #{record.traderName,jdbcType=VARCHAR},
      </if>
      <if test="record.traderContactId != null" >
        TRADER_CONTACT_ID = #{record.traderContactId,jdbcType=INTEGER},
      </if>
      <if test="record.traderContactName != null" >
        TRADER_CONTACT_NAME = #{record.traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="record.traderContactMobile != null" >
        TRADER_CONTACT_MOBILE = #{record.traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="record.traderContactTelephone != null" >
        TRADER_CONTACT_TELEPHONE = #{record.traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="record.traderAddressId != null" >
        TRADER_ADDRESS_ID = #{record.traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="record.traderAreaId != null" >
        TRADER_AREA_ID = #{record.traderAreaId,jdbcType=INTEGER},
      </if>
      <if test="record.traderArea != null" >
        TRADER_AREA = #{record.traderArea,jdbcType=VARCHAR},
      </if>
      <if test="record.traderAddress != null" >
        TRADER_ADDRESS = #{record.traderAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.traderComments != null" >
        TRADER_COMMENTS = #{record.traderComments,jdbcType=VARCHAR},
      </if>
      <if test="record.takeTraderId != null" >
        TAKE_TRADER_ID = #{record.takeTraderId,jdbcType=INTEGER},
      </if>
      <if test="record.takeTraderName != null" >
        TAKE_TRADER_NAME = #{record.takeTraderName,jdbcType=VARCHAR},
      </if>
      <if test="record.takeTraderContactId != null" >
        TAKE_TRADER_CONTACT_ID = #{record.takeTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="record.takeTraderContactName != null" >
        TAKE_TRADER_CONTACT_NAME = #{record.takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="record.takeTraderContactMobile != null" >
        TAKE_TRADER_CONTACT_MOBILE = #{record.takeTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="record.takeTraderContactTelephone != null" >
        TAKE_TRADER_CONTACT_TELEPHONE = #{record.takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="record.takeTraderAddressId != null" >
        TAKE_TRADER_ADDRESS_ID = #{record.takeTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="record.takeTraderAreaId != null" >
        TAKE_TRADER_AREA_ID = #{record.takeTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="record.takeTraderArea != null" >
        TAKE_TRADER_AREA = #{record.takeTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="record.takeTraderAddress != null" >
        TAKE_TRADER_ADDRESS = #{record.takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.isSendInvoice != null" >
        IS_SEND_INVOICE = #{record.isSendInvoice,jdbcType=TINYINT},
      </if>
      <if test="record.invoiceTraderId != null" >
        INVOICE_TRADER_ID = #{record.invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceTraderName != null" >
        INVOICE_TRADER_NAME = #{record.invoiceTraderName,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceTraderContactId != null" >
        INVOICE_TRADER_CONTACT_ID = #{record.invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceTraderContactName != null" >
        INVOICE_TRADER_CONTACT_NAME = #{record.invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceTraderContactMobile != null" >
        INVOICE_TRADER_CONTACT_MOBILE = #{record.invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceTraderContactTelephone != null" >
        INVOICE_TRADER_CONTACT_TELEPHONE = #{record.invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceTraderAddressId != null" >
        INVOICE_TRADER_ADDRESS_ID = #{record.invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceTraderAreaId != null" >
        INVOICE_TRADER_AREA_ID = #{record.invoiceTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceTraderArea != null" >
        INVOICE_TRADER_AREA = #{record.invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceTraderAddress != null" >
        INVOICE_TRADER_ADDRESS = #{record.invoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.salesAreaId != null" >
        SALES_AREA_ID = #{record.salesAreaId,jdbcType=INTEGER},
      </if>
      <if test="record.salesArea != null" >
        SALES_AREA = #{record.salesArea,jdbcType=VARCHAR},
      </if>
      <if test="record.terminalTraderId != null" >
        TERMINAL_TRADER_ID = #{record.terminalTraderId,jdbcType=INTEGER},
      </if>
      <if test="record.terminalTraderName != null" >
        TERMINAL_TRADER_NAME = #{record.terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="record.terminalTraderType != null" >
        TERMINAL_TRADER_TYPE = #{record.terminalTraderType,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceType != null" >
        INVOICE_TYPE = #{record.invoiceType,jdbcType=INTEGER},
      </if>
      <if test="record.freightDescription != null" >
        FREIGHT_DESCRIPTION = #{record.freightDescription,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryType != null" >
        DELIVERY_TYPE = #{record.deliveryType,jdbcType=INTEGER},
      </if>
      <if test="record.logisticsId != null" >
        LOGISTICS_ID = #{record.logisticsId,jdbcType=INTEGER},
      </if>
      <if test="record.paymentType != null" >
        PAYMENT_TYPE = #{record.paymentType,jdbcType=INTEGER},
      </if>
      <if test="record.prepaidAmount != null" >
        PREPAID_AMOUNT = #{record.prepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.accountPeriodAmount != null" >
        ACCOUNT_PERIOD_AMOUNT = #{record.accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.periodDay != null" >
        PERIOD_DAY = #{record.periodDay,jdbcType=INTEGER},
      </if>
      <if test="record.logisticsCollection != null" >
        LOGISTICS_COLLECTION = #{record.logisticsCollection,jdbcType=TINYINT},
      </if>
      <if test="record.retainageAmount != null" >
        RETAINAGE_AMOUNT = #{record.retainageAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.retainageAmountMonth != null" >
        RETAINAGE_AMOUNT_MONTH = #{record.retainageAmountMonth,jdbcType=INTEGER},
      </if>
      <if test="record.paymentComments != null" >
        PAYMENT_COMMENTS = #{record.paymentComments,jdbcType=VARCHAR},
      </if>
      <if test="record.additionalClause != null" >
        ADDITIONAL_CLAUSE = #{record.additionalClause,jdbcType=VARCHAR},
      </if>
      <if test="record.logisticsComments != null" >
        LOGISTICS_COMMENTS = #{record.logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="record.financeComments != null" >
        FINANCE_COMMENTS = #{record.financeComments,jdbcType=VARCHAR},
      </if>
      <if test="record.comments != null" >
        COMMENTS = #{record.comments,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceComments != null" >
        INVOICE_COMMENTS = #{record.invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryDirect != null" >
        DELIVERY_DIRECT = #{record.deliveryDirect,jdbcType=TINYINT},
      </if>
      <if test="record.supplierClause != null" >
        SUPPLIER_CLAUSE = #{record.supplierClause,jdbcType=VARCHAR},
      </if>
      <if test="record.haveAdvancePurchase != null" >
        HAVE_ADVANCE_PURCHASE = #{record.haveAdvancePurchase,jdbcType=TINYINT},
      </if>
      <if test="record.advancePurchaseStatus != null" >
        ADVANCE_PURCHASE_STATUS = #{record.advancePurchaseStatus,jdbcType=TINYINT},
      </if>
      <if test="record.advancePurchaseComments != null" >
        ADVANCE_PURCHASE_COMMENTS = #{record.advancePurchaseComments,jdbcType=VARCHAR},
      </if>
      <if test="record.advancePurchaseTime != null" >
        ADVANCE_PURCHASE_TIME = #{record.advancePurchaseTime,jdbcType=BIGINT},
      </if>
      <if test="record.isUrgent != null" >
        IS_URGENT = #{record.isUrgent,jdbcType=TINYINT},
      </if>
      <if test="record.urgentAmount != null" >
        URGENT_AMOUNT = #{record.urgentAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.haveCommunicate != null" >
        HAVE_COMMUNICATE = #{record.haveCommunicate,jdbcType=TINYINT},
      </if>
      <if test="record.prepareComments != null" >
        PREPARE_COMMENTS = #{record.prepareComments,jdbcType=VARCHAR},
      </if>
      <if test="record.marketingPlan != null" >
        MARKETING_PLAN = #{record.marketingPlan,jdbcType=VARCHAR},
      </if>
      <if test="record.statusComments != null" >
        STATUS_COMMENTS = #{record.statusComments,jdbcType=INTEGER},
      </if>
      <if test="record.syncStatus != null" >
        SYNC_STATUS = #{record.syncStatus,jdbcType=TINYINT},
      </if>
      <if test="record.logisticsApiSync != null" >
        LOGISTICS_API_SYNC = #{record.logisticsApiSync,jdbcType=TINYINT},
      </if>
      <if test="record.logisticsWxsendSync != null" >
        LOGISTICS_WXSEND_SYNC = #{record.logisticsWxsendSync,jdbcType=TINYINT},
      </if>
      <if test="record.satisfyInvoiceTime != null" >
        SATISFY_INVOICE_TIME = #{record.satisfyInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="record.satisfyDeliveryTime != null" >
        SATISFY_DELIVERY_TIME = #{record.satisfyDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="record.isSalesPerformance != null" >
        IS_SALES_PERFORMANCE = #{record.isSalesPerformance,jdbcType=TINYINT},
      </if>
      <if test="record.salesPerformanceTime != null" >
        SALES_PERFORMANCE_TIME = #{record.salesPerformanceTime,jdbcType=BIGINT},
      </if>
      <if test="record.salesPerformanceModTime != null" >
        SALES_PERFORMANCE_MOD_TIME = #{record.salesPerformanceModTime,jdbcType=BIGINT},
      </if>
      <if test="record.isDelayInvoice != null" >
        IS_DELAY_INVOICE = #{record.isDelayInvoice,jdbcType=TINYINT},
      </if>
      <if test="record.invoiceMethod != null" >
        INVOICE_METHOD = #{record.invoiceMethod,jdbcType=TINYINT},
      </if>
      <if test="record.lockedReason != null" >
        LOCKED_REASON = #{record.lockedReason,jdbcType=VARCHAR},
      </if>
      <if test="record.costUserIds != null" >
        COST_USER_IDS = #{record.costUserIds,jdbcType=VARCHAR},
      </if>
      <if test="record.ownerUserId != null" >
        OWNER_USER_ID = #{record.ownerUserId,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceEmail != null" >
        INVOICE_EMAIL = #{record.invoiceEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentMode != null" >
        PAYMENT_MODE = #{record.paymentMode,jdbcType=TINYINT},
      </if>
      <if test="record.payType != null" >
        PAY_TYPE = #{record.payType,jdbcType=TINYINT},
      </if>
      <if test="record.isApplyInvoice != null" >
        IS_APPLY_INVOICE = #{record.isApplyInvoice,jdbcType=TINYINT},
      </if>
      <if test="record.applyInvoiceTime != null" >
        APPLY_INVOICE_TIME = #{record.applyInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="record.addTime != null" >
        ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null" >
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null" >
        MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      </if>
      <if test="record.updater != null" >
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.adkSaleorderNo != null" >
        ADK_SALEORDER_NO = #{record.adkSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.createMobile != null" >
        CREATE_MOBILE = #{record.createMobile,jdbcType=VARCHAR},
      </if>
      <if test="record.bdtraderComments != null" >
        BDTRADER_COMMENTS = #{record.bdtraderComments,jdbcType=VARCHAR},
      </if>
      <if test="record.closeComments != null" >
        CLOSE_COMMENTS = #{record.closeComments,jdbcType=VARCHAR},
      </if>
      <if test="record.bdMobileTime != null" >
        BD_MOBILE_TIME = #{record.bdMobileTime,jdbcType=BIGINT},
      </if>
      <if test="record.webTakeDeliveryTime != null" >
        WEB_TAKE_DELIVERY_TIME = #{record.webTakeDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="record.actionId != null" >
        ACTION_ID = #{record.actionId,jdbcType=INTEGER},
      </if>
      <if test="record.isCoupons != null" >
        IS_COUPONS = #{record.isCoupons,jdbcType=TINYINT},
      </if>
      <if test="record.elSaleordreNo != null" >
        EL_SALEORDRE_NO = #{record.elSaleordreNo,jdbcType=VARCHAR},
      </if>
      <if test="record.couponmoney != null" >
        COUPONMONEY = #{record.couponmoney,jdbcType=DECIMAL},
      </if>
      <if test="record.originalAmount != null" >
        ORIGINAL_AMOUNT = #{record.originalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.isPrintout != null" >
        IS_PRINTOUT = #{record.isPrintout,jdbcType=TINYINT},
      </if>
      <if test="record.outIsFlag != null" >
        OUT_IS_FLAG = #{record.outIsFlag,jdbcType=TINYINT},
      </if>
      <if test="record.updateDataTime != null" >
        UPDATE_DATA_TIME = #{record.updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.realPayAmount != null" >
        REAL_PAY_AMOUNT = #{record.realPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.realReturnAmount != null" >
        REAL_RETURN_AMOUNT = #{record.realReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.realTotalAmount != null" >
        REAL_TOTAL_AMOUNT = #{record.realTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.sendToPc != null" >
        SEND_TO_PC = #{record.sendToPc,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 08 19:16:46 CST 2020.
    -->
    update T_SALEORDER
    set SALEORDER_ID = #{record.saleorderId,jdbcType=INTEGER},
      QUOTEORDER_ID = #{record.quoteorderId,jdbcType=INTEGER},
      PARENT_ID = #{record.parentId,jdbcType=INTEGER},
      SALEORDER_NO = #{record.saleorderNo,jdbcType=VARCHAR},
      M_SALEORDER_NO = #{record.mSaleorderNo,jdbcType=VARCHAR},
      ORDER_TYPE = #{record.orderType,jdbcType=TINYINT},
      COMPANY_ID = #{record.companyId,jdbcType=INTEGER},
      `SOURCE` = #{record.source,jdbcType=TINYINT},
      CREATOR_ORG_ID = #{record.creatorOrgId,jdbcType=INTEGER},
      CREATOR_ORG_NAME = #{record.creatorOrgName,jdbcType=VARCHAR},
      ORG_ID = #{record.orgId,jdbcType=INTEGER},
      ORG_NAME = #{record.orgName,jdbcType=VARCHAR},
      USER_ID = #{record.userId,jdbcType=INTEGER},
      VALID_ORG_ID = #{record.validOrgId,jdbcType=INTEGER},
      VALID_ORG_NAME = #{record.validOrgName,jdbcType=VARCHAR},
      VALID_USER_ID = #{record.validUserId,jdbcType=INTEGER},
      VALID_STATUS = #{record.validStatus,jdbcType=TINYINT},
      VALID_TIME = #{record.validTime,jdbcType=BIGINT},
      END_TIME = #{record.endTime,jdbcType=BIGINT},
      `STATUS` = #{record.status,jdbcType=TINYINT},
      PURCHASE_STATUS = #{record.purchaseStatus,jdbcType=TINYINT},
      LOCKED_STATUS = #{record.lockedStatus,jdbcType=TINYINT},
      INVOICE_STATUS = #{record.invoiceStatus,jdbcType=TINYINT},
      INVOICE_TIME = #{record.invoiceTime,jdbcType=BIGINT},
      PAYMENT_STATUS = #{record.paymentStatus,jdbcType=TINYINT},
      PAYMENT_TIME = #{record.paymentTime,jdbcType=BIGINT},
      DELIVERY_STATUS = #{record.deliveryStatus,jdbcType=TINYINT},
      DELIVERY_TIME = #{record.deliveryTime,jdbcType=BIGINT},
      IS_CUSTOMER_ARRIVAL = #{record.isCustomerArrival,jdbcType=TINYINT},
      ARRIVAL_STATUS = #{record.arrivalStatus,jdbcType=TINYINT},
      ARRIVAL_TIME = #{record.arrivalTime,jdbcType=BIGINT},
      SERVICE_STATUS = #{record.serviceStatus,jdbcType=TINYINT},
      HAVE_ACCOUNT_PERIOD = #{record.haveAccountPeriod,jdbcType=TINYINT},
      IS_PAYMENT = #{record.isPayment,jdbcType=TINYINT},
      TOTAL_AMOUNT = #{record.totalAmount,jdbcType=DECIMAL},
      TRADER_ID = #{record.traderId,jdbcType=INTEGER},
      CUSTOMER_TYPE = #{record.customerType,jdbcType=INTEGER},
      CUSTOMER_NATURE = #{record.customerNature,jdbcType=INTEGER},
      TRADER_NAME = #{record.traderName,jdbcType=VARCHAR},
      TRADER_CONTACT_ID = #{record.traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{record.traderContactName,jdbcType=VARCHAR},
      TRADER_CONTACT_MOBILE = #{record.traderContactMobile,jdbcType=VARCHAR},
      TRADER_CONTACT_TELEPHONE = #{record.traderContactTelephone,jdbcType=VARCHAR},
      TRADER_ADDRESS_ID = #{record.traderAddressId,jdbcType=INTEGER},
      TRADER_AREA_ID = #{record.traderAreaId,jdbcType=INTEGER},
      TRADER_AREA = #{record.traderArea,jdbcType=VARCHAR},
      TRADER_ADDRESS = #{record.traderAddress,jdbcType=VARCHAR},
      TRADER_COMMENTS = #{record.traderComments,jdbcType=VARCHAR},
      TAKE_TRADER_ID = #{record.takeTraderId,jdbcType=INTEGER},
      TAKE_TRADER_NAME = #{record.takeTraderName,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_ID = #{record.takeTraderContactId,jdbcType=INTEGER},
      TAKE_TRADER_CONTACT_NAME = #{record.takeTraderContactName,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_MOBILE = #{record.takeTraderContactMobile,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_TELEPHONE = #{record.takeTraderContactTelephone,jdbcType=VARCHAR},
      TAKE_TRADER_ADDRESS_ID = #{record.takeTraderAddressId,jdbcType=INTEGER},
      TAKE_TRADER_AREA_ID = #{record.takeTraderAreaId,jdbcType=INTEGER},
      TAKE_TRADER_AREA = #{record.takeTraderArea,jdbcType=VARCHAR},
      TAKE_TRADER_ADDRESS = #{record.takeTraderAddress,jdbcType=VARCHAR},
      IS_SEND_INVOICE = #{record.isSendInvoice,jdbcType=TINYINT},
      INVOICE_TRADER_ID = #{record.invoiceTraderId,jdbcType=INTEGER},
      INVOICE_TRADER_NAME = #{record.invoiceTraderName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_ID = #{record.invoiceTraderContactId,jdbcType=INTEGER},
      INVOICE_TRADER_CONTACT_NAME = #{record.invoiceTraderContactName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_MOBILE = #{record.invoiceTraderContactMobile,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_TELEPHONE = #{record.invoiceTraderContactTelephone,jdbcType=VARCHAR},
      INVOICE_TRADER_ADDRESS_ID = #{record.invoiceTraderAddressId,jdbcType=INTEGER},
      INVOICE_TRADER_AREA_ID = #{record.invoiceTraderAreaId,jdbcType=INTEGER},
      INVOICE_TRADER_AREA = #{record.invoiceTraderArea,jdbcType=VARCHAR},
      INVOICE_TRADER_ADDRESS = #{record.invoiceTraderAddress,jdbcType=VARCHAR},
      SALES_AREA_ID = #{record.salesAreaId,jdbcType=INTEGER},
      SALES_AREA = #{record.salesArea,jdbcType=VARCHAR},
      TERMINAL_TRADER_ID = #{record.terminalTraderId,jdbcType=INTEGER},
      TERMINAL_TRADER_NAME = #{record.terminalTraderName,jdbcType=VARCHAR},
      TERMINAL_TRADER_TYPE = #{record.terminalTraderType,jdbcType=INTEGER},
      INVOICE_TYPE = #{record.invoiceType,jdbcType=INTEGER},
      FREIGHT_DESCRIPTION = #{record.freightDescription,jdbcType=INTEGER},
      DELIVERY_TYPE = #{record.deliveryType,jdbcType=INTEGER},
      LOGISTICS_ID = #{record.logisticsId,jdbcType=INTEGER},
      PAYMENT_TYPE = #{record.paymentType,jdbcType=INTEGER},
      PREPAID_AMOUNT = #{record.prepaidAmount,jdbcType=DECIMAL},
      ACCOUNT_PERIOD_AMOUNT = #{record.accountPeriodAmount,jdbcType=DECIMAL},
      PERIOD_DAY = #{record.periodDay,jdbcType=INTEGER},
      LOGISTICS_COLLECTION = #{record.logisticsCollection,jdbcType=TINYINT},
      RETAINAGE_AMOUNT = #{record.retainageAmount,jdbcType=DECIMAL},
      RETAINAGE_AMOUNT_MONTH = #{record.retainageAmountMonth,jdbcType=INTEGER},
      PAYMENT_COMMENTS = #{record.paymentComments,jdbcType=VARCHAR},
      ADDITIONAL_CLAUSE = #{record.additionalClause,jdbcType=VARCHAR},
      LOGISTICS_COMMENTS = #{record.logisticsComments,jdbcType=VARCHAR},
      FINANCE_COMMENTS = #{record.financeComments,jdbcType=VARCHAR},
      COMMENTS = #{record.comments,jdbcType=VARCHAR},
      INVOICE_COMMENTS = #{record.invoiceComments,jdbcType=VARCHAR},
      DELIVERY_DIRECT = #{record.deliveryDirect,jdbcType=TINYINT},
      SUPPLIER_CLAUSE = #{record.supplierClause,jdbcType=VARCHAR},
      HAVE_ADVANCE_PURCHASE = #{record.haveAdvancePurchase,jdbcType=TINYINT},
      ADVANCE_PURCHASE_STATUS = #{record.advancePurchaseStatus,jdbcType=TINYINT},
      ADVANCE_PURCHASE_COMMENTS = #{record.advancePurchaseComments,jdbcType=VARCHAR},
      ADVANCE_PURCHASE_TIME = #{record.advancePurchaseTime,jdbcType=BIGINT},
      IS_URGENT = #{record.isUrgent,jdbcType=TINYINT},
      URGENT_AMOUNT = #{record.urgentAmount,jdbcType=DECIMAL},
      HAVE_COMMUNICATE = #{record.haveCommunicate,jdbcType=TINYINT},
      PREPARE_COMMENTS = #{record.prepareComments,jdbcType=VARCHAR},
      MARKETING_PLAN = #{record.marketingPlan,jdbcType=VARCHAR},
      STATUS_COMMENTS = #{record.statusComments,jdbcType=INTEGER},
      SYNC_STATUS = #{record.syncStatus,jdbcType=TINYINT},
      LOGISTICS_API_SYNC = #{record.logisticsApiSync,jdbcType=TINYINT},
      LOGISTICS_WXSEND_SYNC = #{record.logisticsWxsendSync,jdbcType=TINYINT},
      SATISFY_INVOICE_TIME = #{record.satisfyInvoiceTime,jdbcType=BIGINT},
      SATISFY_DELIVERY_TIME = #{record.satisfyDeliveryTime,jdbcType=BIGINT},
      IS_SALES_PERFORMANCE = #{record.isSalesPerformance,jdbcType=TINYINT},
      SALES_PERFORMANCE_TIME = #{record.salesPerformanceTime,jdbcType=BIGINT},
      SALES_PERFORMANCE_MOD_TIME = #{record.salesPerformanceModTime,jdbcType=BIGINT},
      IS_DELAY_INVOICE = #{record.isDelayInvoice,jdbcType=TINYINT},
      INVOICE_METHOD = #{record.invoiceMethod,jdbcType=TINYINT},
      LOCKED_REASON = #{record.lockedReason,jdbcType=VARCHAR},
      COST_USER_IDS = #{record.costUserIds,jdbcType=VARCHAR},
      OWNER_USER_ID = #{record.ownerUserId,jdbcType=INTEGER},
      INVOICE_EMAIL = #{record.invoiceEmail,jdbcType=VARCHAR},
      PAYMENT_MODE = #{record.paymentMode,jdbcType=TINYINT},
      PAY_TYPE = #{record.payType,jdbcType=TINYINT},
      IS_APPLY_INVOICE = #{record.isApplyInvoice,jdbcType=TINYINT},
      APPLY_INVOICE_TIME = #{record.applyInvoiceTime,jdbcType=BIGINT},
      ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=BIGINT},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      ADK_SALEORDER_NO = #{record.adkSaleorderNo,jdbcType=VARCHAR},
      CREATE_MOBILE = #{record.createMobile,jdbcType=VARCHAR},
      BDTRADER_COMMENTS = #{record.bdtraderComments,jdbcType=VARCHAR},
      CLOSE_COMMENTS = #{record.closeComments,jdbcType=VARCHAR},
      BD_MOBILE_TIME = #{record.bdMobileTime,jdbcType=BIGINT},
      WEB_TAKE_DELIVERY_TIME = #{record.webTakeDeliveryTime,jdbcType=BIGINT},
      ACTION_ID = #{record.actionId,jdbcType=INTEGER},
      IS_COUPONS = #{record.isCoupons,jdbcType=TINYINT},
      EL_SALEORDRE_NO = #{record.elSaleordreNo,jdbcType=VARCHAR},
      COUPONMONEY = #{record.couponmoney,jdbcType=DECIMAL},
      ORIGINAL_AMOUNT = #{record.originalAmount,jdbcType=DECIMAL},
      IS_PRINTOUT = #{record.isPrintout,jdbcType=TINYINT},
      OUT_IS_FLAG = #{record.outIsFlag,jdbcType=TINYINT},
      UPDATE_DATA_TIME = #{record.updateDataTime,jdbcType=TIMESTAMP},
      REAL_PAY_AMOUNT = #{record.realPayAmount,jdbcType=DECIMAL},
      REAL_RETURN_AMOUNT = #{record.realReturnAmount,jdbcType=DECIMAL},
      REAL_TOTAL_AMOUNT = #{record.realTotalAmount,jdbcType=DECIMAL},
      SEND_TO_PC = #{record.sendToPc,jdbcType=TINYINT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.SaleorderGenerate" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 08 19:16:46 CST 2020.
    -->
    update T_SALEORDER
    <set >
      <if test="quoteorderId != null" >
        QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null" >
        PARENT_ID = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="saleorderNo != null" >
        SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="mSaleorderNo != null" >
        M_SALEORDER_NO = #{mSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null" >
        ORDER_TYPE = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="source != null" >
        `SOURCE` = #{source,jdbcType=TINYINT},
      </if>
      <if test="creatorOrgId != null" >
        CREATOR_ORG_ID = #{creatorOrgId,jdbcType=INTEGER},
      </if>
      <if test="creatorOrgName != null" >
        CREATOR_ORG_NAME = #{creatorOrgName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null" >
        ORG_NAME = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="validOrgId != null" >
        VALID_ORG_ID = #{validOrgId,jdbcType=INTEGER},
      </if>
      <if test="validOrgName != null" >
        VALID_ORG_NAME = #{validOrgName,jdbcType=VARCHAR},
      </if>
      <if test="validUserId != null" >
        VALID_USER_ID = #{validUserId,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null" >
        VALID_STATUS = #{validStatus,jdbcType=TINYINT},
      </if>
      <if test="validTime != null" >
        VALID_TIME = #{validTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null" >
        END_TIME = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        `STATUS` = #{status,jdbcType=TINYINT},
      </if>
      <if test="purchaseStatus != null" >
        PURCHASE_STATUS = #{purchaseStatus,jdbcType=TINYINT},
      </if>
      <if test="lockedStatus != null" >
        LOCKED_STATUS = #{lockedStatus,jdbcType=TINYINT},
      </if>
      <if test="invoiceStatus != null" >
        INVOICE_STATUS = #{invoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="invoiceTime != null" >
        INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
      </if>
      <if test="paymentStatus != null" >
        PAYMENT_STATUS = #{paymentStatus,jdbcType=TINYINT},
      </if>
      <if test="paymentTime != null" >
        PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryStatus != null" >
        DELIVERY_STATUS = #{deliveryStatus,jdbcType=TINYINT},
      </if>
      <if test="deliveryTime != null" >
        DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="isCustomerArrival != null" >
        IS_CUSTOMER_ARRIVAL = #{isCustomerArrival,jdbcType=TINYINT},
      </if>
      <if test="arrivalStatus != null" >
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=TINYINT},
      </if>
      <if test="arrivalTime != null" >
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="serviceStatus != null" >
        SERVICE_STATUS = #{serviceStatus,jdbcType=TINYINT},
      </if>
      <if test="haveAccountPeriod != null" >
        HAVE_ACCOUNT_PERIOD = #{haveAccountPeriod,jdbcType=TINYINT},
      </if>
      <if test="isPayment != null" >
        IS_PAYMENT = #{isPayment,jdbcType=TINYINT},
      </if>
      <if test="totalAmount != null" >
        TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="traderId != null" >
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="customerType != null" >
        CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
      </if>
      <if test="customerNature != null" >
        CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
      </if>
      <if test="traderName != null" >
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null" >
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null" >
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null" >
        TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null" >
        TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null" >
        TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="traderAreaId != null" >
        TRADER_AREA_ID = #{traderAreaId,jdbcType=INTEGER},
      </if>
      <if test="traderArea != null" >
        TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
      </if>
      <if test="traderAddress != null" >
        TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
      </if>
      <if test="traderComments != null" >
        TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderId != null" >
        TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderName != null" >
        TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactId != null" >
        TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderContactName != null" >
        TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactMobile != null" >
        TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null" >
        TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddressId != null" >
        TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderAreaId != null" >
        TAKE_TRADER_AREA_ID = #{takeTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderArea != null" >
        TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddress != null" >
        TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="isSendInvoice != null" >
        IS_SEND_INVOICE = #{isSendInvoice,jdbcType=TINYINT},
      </if>
      <if test="invoiceTraderId != null" >
        INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderName != null" >
        INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactId != null" >
        INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderContactName != null" >
        INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactMobile != null" >
        INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactTelephone != null" >
        INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddressId != null" >
        INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderAreaId != null" >
        INVOICE_TRADER_AREA_ID = #{invoiceTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderArea != null" >
        INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddress != null" >
        INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="salesAreaId != null" >
        SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER},
      </if>
      <if test="salesArea != null" >
        SALES_AREA = #{salesArea,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null" >
        TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER},
      </if>
      <if test="terminalTraderName != null" >
        TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderType != null" >
        TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="freightDescription != null" >
        FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
      </if>
      <if test="deliveryType != null" >
        DELIVERY_TYPE = #{deliveryType,jdbcType=INTEGER},
      </if>
      <if test="logisticsId != null" >
        LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null" >
        PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="prepaidAmount != null" >
        PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodAmount != null" >
        ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null" >
        PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="logisticsCollection != null" >
        LOGISTICS_COLLECTION = #{logisticsCollection,jdbcType=TINYINT},
      </if>
      <if test="retainageAmount != null" >
        RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmountMonth != null" >
        RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
      </if>
      <if test="paymentComments != null" >
        PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
      </if>
      <if test="additionalClause != null" >
        ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
      </if>
      <if test="logisticsComments != null" >
        LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="financeComments != null" >
        FINANCE_COMMENTS = #{financeComments,jdbcType=VARCHAR},
      </if>
      <if test="comments != null" >
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="invoiceComments != null" >
        INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null" >
        DELIVERY_DIRECT = #{deliveryDirect,jdbcType=TINYINT},
      </if>
      <if test="supplierClause != null" >
        SUPPLIER_CLAUSE = #{supplierClause,jdbcType=VARCHAR},
      </if>
      <if test="haveAdvancePurchase != null" >
        HAVE_ADVANCE_PURCHASE = #{haveAdvancePurchase,jdbcType=TINYINT},
      </if>
      <if test="advancePurchaseStatus != null" >
        ADVANCE_PURCHASE_STATUS = #{advancePurchaseStatus,jdbcType=TINYINT},
      </if>
      <if test="advancePurchaseComments != null" >
        ADVANCE_PURCHASE_COMMENTS = #{advancePurchaseComments,jdbcType=VARCHAR},
      </if>
      <if test="advancePurchaseTime != null" >
        ADVANCE_PURCHASE_TIME = #{advancePurchaseTime,jdbcType=BIGINT},
      </if>
      <if test="isUrgent != null" >
        IS_URGENT = #{isUrgent,jdbcType=TINYINT},
      </if>
      <if test="urgentAmount != null" >
        URGENT_AMOUNT = #{urgentAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveCommunicate != null" >
        HAVE_COMMUNICATE = #{haveCommunicate,jdbcType=TINYINT},
      </if>
      <if test="prepareComments != null" >
        PREPARE_COMMENTS = #{prepareComments,jdbcType=VARCHAR},
      </if>
      <if test="marketingPlan != null" >
        MARKETING_PLAN = #{marketingPlan,jdbcType=VARCHAR},
      </if>
      <if test="statusComments != null" >
        STATUS_COMMENTS = #{statusComments,jdbcType=INTEGER},
      </if>
      <if test="syncStatus != null" >
        SYNC_STATUS = #{syncStatus,jdbcType=TINYINT},
      </if>
      <if test="logisticsApiSync != null" >
        LOGISTICS_API_SYNC = #{logisticsApiSync,jdbcType=TINYINT},
      </if>
      <if test="logisticsWxsendSync != null" >
        LOGISTICS_WXSEND_SYNC = #{logisticsWxsendSync,jdbcType=TINYINT},
      </if>
      <if test="satisfyInvoiceTime != null" >
        SATISFY_INVOICE_TIME = #{satisfyInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="satisfyDeliveryTime != null" >
        SATISFY_DELIVERY_TIME = #{satisfyDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="isSalesPerformance != null" >
        IS_SALES_PERFORMANCE = #{isSalesPerformance,jdbcType=TINYINT},
      </if>
      <if test="salesPerformanceTime != null" >
        SALES_PERFORMANCE_TIME = #{salesPerformanceTime,jdbcType=BIGINT},
      </if>
      <if test="salesPerformanceModTime != null" >
        SALES_PERFORMANCE_MOD_TIME = #{salesPerformanceModTime,jdbcType=BIGINT},
      </if>
      <if test="isDelayInvoice != null" >
        IS_DELAY_INVOICE = #{isDelayInvoice,jdbcType=TINYINT},
      </if>
      <if test="invoiceMethod != null" >
        INVOICE_METHOD = #{invoiceMethod,jdbcType=TINYINT},
      </if>
      <if test="lockedReason != null" >
        LOCKED_REASON = #{lockedReason,jdbcType=VARCHAR},
      </if>
      <if test="costUserIds != null" >
        COST_USER_IDS = #{costUserIds,jdbcType=VARCHAR},
      </if>
      <if test="ownerUserId != null" >
        OWNER_USER_ID = #{ownerUserId,jdbcType=INTEGER},
      </if>
      <if test="invoiceEmail != null" >
        INVOICE_EMAIL = #{invoiceEmail,jdbcType=VARCHAR},
      </if>
      <if test="paymentMode != null" >
        PAYMENT_MODE = #{paymentMode,jdbcType=TINYINT},
      </if>
      <if test="payType != null" >
        PAY_TYPE = #{payType,jdbcType=TINYINT},
      </if>
      <if test="isApplyInvoice != null" >
        IS_APPLY_INVOICE = #{isApplyInvoice,jdbcType=TINYINT},
      </if>
      <if test="applyInvoiceTime != null" >
        APPLY_INVOICE_TIME = #{applyInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="adkSaleorderNo != null" >
        ADK_SALEORDER_NO = #{adkSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null" >
        CREATE_MOBILE = #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="bdtraderComments != null" >
        BDTRADER_COMMENTS = #{bdtraderComments,jdbcType=VARCHAR},
      </if>
      <if test="closeComments != null" >
        CLOSE_COMMENTS = #{closeComments,jdbcType=VARCHAR},
      </if>
      <if test="bdMobileTime != null" >
        BD_MOBILE_TIME = #{bdMobileTime,jdbcType=BIGINT},
      </if>
      <if test="webTakeDeliveryTime != null" >
        WEB_TAKE_DELIVERY_TIME = #{webTakeDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="actionId != null" >
        ACTION_ID = #{actionId,jdbcType=INTEGER},
      </if>
      <if test="isCoupons != null" >
        IS_COUPONS = #{isCoupons,jdbcType=TINYINT},
      </if>
      <if test="elSaleordreNo != null" >
        EL_SALEORDRE_NO = #{elSaleordreNo,jdbcType=VARCHAR},
      </if>
      <if test="couponmoney != null" >
        COUPONMONEY = #{couponmoney,jdbcType=DECIMAL},
      </if>
      <if test="originalAmount != null" >
        ORIGINAL_AMOUNT = #{originalAmount,jdbcType=DECIMAL},
      </if>
      <if test="isPrintout != null" >
        IS_PRINTOUT = #{isPrintout,jdbcType=TINYINT},
      </if>
      <if test="outIsFlag != null" >
        OUT_IS_FLAG = #{outIsFlag,jdbcType=TINYINT},
      </if>
      <if test="updateDataTime != null" >
        UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realPayAmount != null" >
        REAL_PAY_AMOUNT = #{realPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="realReturnAmount != null" >
        REAL_RETURN_AMOUNT = #{realReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="realTotalAmount != null" >
        REAL_TOTAL_AMOUNT = #{realTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="sendToPc != null" >
        SEND_TO_PC = #{sendToPc,jdbcType=TINYINT},
      </if>
    </set>
    where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.order.model.SaleorderGenerate" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 08 19:16:46 CST 2020.
    -->
    update T_SALEORDER
    set QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      PARENT_ID = #{parentId,jdbcType=INTEGER},
      SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      M_SALEORDER_NO = #{mSaleorderNo,jdbcType=VARCHAR},
      ORDER_TYPE = #{orderType,jdbcType=TINYINT},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      `SOURCE` = #{source,jdbcType=TINYINT},
      CREATOR_ORG_ID = #{creatorOrgId,jdbcType=INTEGER},
      CREATOR_ORG_NAME = #{creatorOrgName,jdbcType=VARCHAR},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      ORG_NAME = #{orgName,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=INTEGER},
      VALID_ORG_ID = #{validOrgId,jdbcType=INTEGER},
      VALID_ORG_NAME = #{validOrgName,jdbcType=VARCHAR},
      VALID_USER_ID = #{validUserId,jdbcType=INTEGER},
      VALID_STATUS = #{validStatus,jdbcType=TINYINT},
      VALID_TIME = #{validTime,jdbcType=BIGINT},
      END_TIME = #{endTime,jdbcType=BIGINT},
      `STATUS` = #{status,jdbcType=TINYINT},
      PURCHASE_STATUS = #{purchaseStatus,jdbcType=TINYINT},
      LOCKED_STATUS = #{lockedStatus,jdbcType=TINYINT},
      INVOICE_STATUS = #{invoiceStatus,jdbcType=TINYINT},
      INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
      PAYMENT_STATUS = #{paymentStatus,jdbcType=TINYINT},
      PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
      DELIVERY_STATUS = #{deliveryStatus,jdbcType=TINYINT},
      DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      IS_CUSTOMER_ARRIVAL = #{isCustomerArrival,jdbcType=TINYINT},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=TINYINT},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      SERVICE_STATUS = #{serviceStatus,jdbcType=TINYINT},
      HAVE_ACCOUNT_PERIOD = #{haveAccountPeriod,jdbcType=TINYINT},
      IS_PAYMENT = #{isPayment,jdbcType=TINYINT},
      TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
      CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      TRADER_AREA_ID = #{traderAreaId,jdbcType=INTEGER},
      TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
      TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
      TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
      TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
      TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
      TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
      TAKE_TRADER_AREA_ID = #{takeTraderAreaId,jdbcType=INTEGER},
      TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
      TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      IS_SEND_INVOICE = #{isSendInvoice,jdbcType=TINYINT},
      INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
      INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
      INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
      INVOICE_TRADER_AREA_ID = #{invoiceTraderAreaId,jdbcType=INTEGER},
      INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
      INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
      SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER},
      SALES_AREA = #{salesArea,jdbcType=VARCHAR},
      TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER},
      TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER},
      INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
      DELIVERY_TYPE = #{deliveryType,jdbcType=INTEGER},
      LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
      ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      LOGISTICS_COLLECTION = #{logisticsCollection,jdbcType=TINYINT},
      RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
      RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
      PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
      ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
      LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      FINANCE_COMMENTS = #{financeComments,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      DELIVERY_DIRECT = #{deliveryDirect,jdbcType=TINYINT},
      SUPPLIER_CLAUSE = #{supplierClause,jdbcType=VARCHAR},
      HAVE_ADVANCE_PURCHASE = #{haveAdvancePurchase,jdbcType=TINYINT},
      ADVANCE_PURCHASE_STATUS = #{advancePurchaseStatus,jdbcType=TINYINT},
      ADVANCE_PURCHASE_COMMENTS = #{advancePurchaseComments,jdbcType=VARCHAR},
      ADVANCE_PURCHASE_TIME = #{advancePurchaseTime,jdbcType=BIGINT},
      IS_URGENT = #{isUrgent,jdbcType=TINYINT},
      URGENT_AMOUNT = #{urgentAmount,jdbcType=DECIMAL},
      HAVE_COMMUNICATE = #{haveCommunicate,jdbcType=TINYINT},
      PREPARE_COMMENTS = #{prepareComments,jdbcType=VARCHAR},
      MARKETING_PLAN = #{marketingPlan,jdbcType=VARCHAR},
      STATUS_COMMENTS = #{statusComments,jdbcType=INTEGER},
      SYNC_STATUS = #{syncStatus,jdbcType=TINYINT},
      LOGISTICS_API_SYNC = #{logisticsApiSync,jdbcType=TINYINT},
      LOGISTICS_WXSEND_SYNC = #{logisticsWxsendSync,jdbcType=TINYINT},
      SATISFY_INVOICE_TIME = #{satisfyInvoiceTime,jdbcType=BIGINT},
      SATISFY_DELIVERY_TIME = #{satisfyDeliveryTime,jdbcType=BIGINT},
      IS_SALES_PERFORMANCE = #{isSalesPerformance,jdbcType=TINYINT},
      SALES_PERFORMANCE_TIME = #{salesPerformanceTime,jdbcType=BIGINT},
      SALES_PERFORMANCE_MOD_TIME = #{salesPerformanceModTime,jdbcType=BIGINT},
      IS_DELAY_INVOICE = #{isDelayInvoice,jdbcType=TINYINT},
      INVOICE_METHOD = #{invoiceMethod,jdbcType=TINYINT},
      LOCKED_REASON = #{lockedReason,jdbcType=VARCHAR},
      COST_USER_IDS = #{costUserIds,jdbcType=VARCHAR},
      OWNER_USER_ID = #{ownerUserId,jdbcType=INTEGER},
      INVOICE_EMAIL = #{invoiceEmail,jdbcType=VARCHAR},
      PAYMENT_MODE = #{paymentMode,jdbcType=TINYINT},
      PAY_TYPE = #{payType,jdbcType=TINYINT},
      IS_APPLY_INVOICE = #{isApplyInvoice,jdbcType=TINYINT},
      APPLY_INVOICE_TIME = #{applyInvoiceTime,jdbcType=BIGINT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      ADK_SALEORDER_NO = #{adkSaleorderNo,jdbcType=VARCHAR},
      CREATE_MOBILE = #{createMobile,jdbcType=VARCHAR},
      BDTRADER_COMMENTS = #{bdtraderComments,jdbcType=VARCHAR},
      CLOSE_COMMENTS = #{closeComments,jdbcType=VARCHAR},
      BD_MOBILE_TIME = #{bdMobileTime,jdbcType=BIGINT},
      WEB_TAKE_DELIVERY_TIME = #{webTakeDeliveryTime,jdbcType=BIGINT},
      ACTION_ID = #{actionId,jdbcType=INTEGER},
      IS_COUPONS = #{isCoupons,jdbcType=TINYINT},
      EL_SALEORDRE_NO = #{elSaleordreNo,jdbcType=VARCHAR},
      COUPONMONEY = #{couponmoney,jdbcType=DECIMAL},
      ORIGINAL_AMOUNT = #{originalAmount,jdbcType=DECIMAL},
      IS_PRINTOUT = #{isPrintout,jdbcType=TINYINT},
      OUT_IS_FLAG = #{outIsFlag,jdbcType=TINYINT},
      UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      REAL_PAY_AMOUNT = #{realPayAmount,jdbcType=DECIMAL},
      REAL_RETURN_AMOUNT = #{realReturnAmount,jdbcType=DECIMAL},
      REAL_TOTAL_AMOUNT = #{realTotalAmount,jdbcType=DECIMAL},
      SEND_TO_PC = #{sendToPc,jdbcType=TINYINT}
    where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
  </update>
</mapper>