package com.vedeng.activiti.taskassign;

import com.vedeng.todolist.service.impl.ZlbCheckBuyOrder;
import com.vedeng.todolist.service.impl.ZlbCheckSaleOrder;
import com.vedeng.todolist.service.impl.ZlbCheckTraderCustomerCertificate;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

/**
 * @Author: daniel
 * @Date: 2020/12/23 11 22
 * @Description:
 */
public class AfterQualityDepartmentCheckListener implements ExecutionListener {

    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();

    private ZlbCheckTraderCustomerCertificate zlbCheckTraderCustomerCertificate = (ZlbCheckTraderCustomerCertificate) context.getBean("zlbCheckTraderCustomerCertificate");

    private ZlbCheckSaleOrder zlbCheckSaleOrder = (ZlbCheckSaleOrder) context.getBean("zlbCheckSaleOrder");

    private ZlbCheckBuyOrder zlbCheckBuyOrder = (ZlbCheckBuyOrder) context.getBean("zlbCheckBuyOrder");

    @Override
    public void notify(DelegateExecution delegateExecution) throws Exception {
        Integer relateTableKey = (Integer) delegateExecution.getVariable("relateTableKey");
        String processDefinitionKey = (String) delegateExecution.getVariable("processDefinitionKey");
        Object pass = delegateExecution.getVariable("pass");
        if (pass == null){
            return;
        }
        Integer checkStatus = (Boolean) pass ? 1 : 0;

        if ("customerAptitudeVerify".equals(processDefinitionKey)){
            zlbCheckTraderCustomerCertificate.finish(relateTableKey,checkStatus);
        } else if ("hc_order_auto_verify".equals(processDefinitionKey)){
            zlbCheckSaleOrder.finish(relateTableKey,checkStatus);
        } else if ("saleorderVerify".equals(processDefinitionKey)){
            zlbCheckSaleOrder.finish(relateTableKey,checkStatus);
        } else if ("buyorderVerify_HC".equals(processDefinitionKey)){
            zlbCheckBuyOrder.finish(relateTableKey,checkStatus);
        } else if ("buyorderVerify".equals(processDefinitionKey)){
            zlbCheckBuyOrder.finish(relateTableKey,checkStatus);
        } else if ("bd_order_auto_verify".equals(processDefinitionKey)){
            zlbCheckSaleOrder.finish(relateTableKey,checkStatus);
        }
    }
}
