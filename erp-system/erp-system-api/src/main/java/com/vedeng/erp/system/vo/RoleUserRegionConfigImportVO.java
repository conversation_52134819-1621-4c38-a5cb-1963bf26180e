package com.vedeng.erp.system.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 人员与产线区域配置导入VO
 */
@Data
public class RoleUserRegionConfigImportVO {

    /**
     * 线上销售
     */
    @ExcelProperty("线上销售")
    private String onlineSales;

    /**
     * 线下销售
     */
    @ExcelProperty("线下销售")
    private String offlineSales;

    /**
     * 销售产线人员
     */
    @ExcelProperty("销售产线人员")
    private String productionUser;

    /**
     * 参考省
     */
    @ExcelProperty("参考省")
    private String referenceProvince;

    /**
     * 参考市
     */
    @ExcelProperty("参考市")
    private String referenceCity;
}