package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseBackEntity;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.dto.result.KingDeePayCommonQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeePayCommonApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 票的数据
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchPurchaseBackInvoiceVatProcessor implements ItemProcessor<BatchInvoiceDto, KingDeePayCommonAndInvoiceDto> {


    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    @Autowired
    private BatchRInvoiceDetailJOperateLogDtoMapper batchRInvoiceDetailJOperateLogDtoMapper;

    @Autowired
    private KingDeePayCommonApiService kingDeePayCommonApiService;

    @Autowired
    private BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;

    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Override
    public KingDeePayCommonAndInvoiceDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {

        log.info("BatchPurchaseBackInvoiceProcessorService 处理采购单红字有效票{}" , JSON.toJSONString(batchInvoiceDto));

        List<BatchRInvoiceDetailJOperateLogDto> jOperateLogDtos = batchRInvoiceDetailJOperateLogDtoMapper.findByInvoiceId(batchInvoiceDto.getInvoiceId());
        batchInvoiceDto.setBatchRInvoiceDetailJOperateLogDtos(jOperateLogDtos);
        // 根据 商品维度聚合分组
        if (CollUtil.isEmpty(jOperateLogDtos)) {
            log.error("当前采购单红字有效票id:{},未关联到出库单信息",JSON.toJSONString(batchInvoiceDto.getInvoiceId()));
            throw new KingDeeException("当前采购单红字有效票id:" + JSON.toJSONString(batchInvoiceDto.getInvoiceId()) + "未关联到出库单信息");
        }

        List<Integer> ids = jOperateLogDtos.stream().map(BatchRInvoiceDetailJOperateLogDto::getOperateLogId).collect(Collectors.toList());
        List<BatchWarehouseGoodsOutInDto> batchWarehouseGoodsOutInDtos = batchWarehouseGoodsOutInDtoMapper.selectWarehouseOutInOrder(ids);
        Optional<Date> max = batchWarehouseGoodsOutInDtos.stream().max(Comparator.comparing(BatchWarehouseGoodsOutInDto::getOutInTime)).map(BatchWarehouseGoodsOutInDto::getOutInTime);
        if (max.isPresent() && max.get().getTime() > batchInvoiceDto.getValidTime()) {
            batchInvoiceDto.setValidTime(max.get().getTime());
        }

        Map<Integer, List<BatchRInvoiceDetailJOperateLogDto>> collect = jOperateLogDtos.stream().collect(Collectors.groupingBy(BatchRInvoiceDetailJOperateLogDto::getInvoiceDetailId));
        // 发票类型 1电票 2 纸票
        String QZOK_FPLX = Objects.isNull(batchInvoiceDto.getInvoiceProperty()) ? "2" : batchInvoiceDto.getInvoiceProperty().equals(1) ? "2" : "1";

        List<KingDeePayCommonQueryResultDto> kingDeePayCommon = kingDeePayCommonApiService.getKingDeePayCommon(batchInvoiceDto.getInvoiceId().toString());
        if (CollUtil.isEmpty(kingDeePayCommon)) {
            log.error("金蝶未查询到发票：{},应付单的数据",batchInvoiceDto.getInvoiceId());
            throw new KingDeeException("金蝶未查询到发票:" + JSON.toJSONString(batchInvoiceDto.getInvoiceId()) + "应付单的数据");
        }
        List<Integer> invoiceDetailIds = jOperateLogDtos.stream().map(BatchRInvoiceDetailJOperateLogDto::getInvoiceDetailId).collect(Collectors.toList());
        List<BatchInvoiceDetailDto> batchInvoiceDetailDtos = batchInvoiceDetailDtoMapper.selectByDetailIds(invoiceDetailIds);
        if (CollUtil.isEmpty(batchInvoiceDetailDtos)) {
            log.error("invoiceId:{},未查询到票明细id:{},的明细信息",batchInvoiceDto.getInvoiceId(),JSON.toJSONString(invoiceDetailIds));
            throw new KingDeeException("invoiceId:"+batchInvoiceDto.getInvoiceId()+"未查询到票明细id:" + JSON.toJSONString(invoiceDetailIds) + "的明细信息");
        }
        batchInvoiceDto.setBatchInvoiceDetailDtoList(batchInvoiceDetailDtos);
        batchInvoiceDetailDtos.forEach(x->{
            List<BatchRInvoiceDetailJOperateLogDto> batchRInvoiceDetailJOperateLogDtos = collect.get(x.getInvoiceDetailId());
            if (CollUtil.isNotEmpty(batchRInvoiceDetailJOperateLogDtos)) {
                x.setSku(batchRInvoiceDetailJOperateLogDtos.get(0).getSku());
            }
        });
        KingDeePayCommonAndInvoiceDto kingDeePayCommonAndInvoiceDto = new KingDeePayCommonAndInvoiceDto();
        if (StrUtil.isNotEmpty(batchInvoiceDto.getInvoiceTypeName())&&batchInvoiceDto.getInvoiceTypeName().contains("专用发票")) {
            kingDeePayCommonAndInvoiceDto.setSpecial(true);
            bindInvoiceData(batchInvoiceDto, QZOK_FPLX, kingDeePayCommonAndInvoiceDto, kingDeePayCommon);
        } else {
            kingDeePayCommonAndInvoiceDto.setSpecial(false);
            bindInvoiceData(batchInvoiceDto, QZOK_FPLX, kingDeePayCommonAndInvoiceDto, kingDeePayCommon);
        }

        return kingDeePayCommonAndInvoiceDto;
    }


    /**
     * 专票 普票对象封装
     * @param batchInvoiceDto
     * @param QZOK_FPLX
     * @param kingDeePayCommonAndInvoiceDto
     * @param kingDeePayCommon
     */
    private void bindInvoiceData(BatchInvoiceDto batchInvoiceDto, String QZOK_FPLX, KingDeePayCommonAndInvoiceDto kingDeePayCommonAndInvoiceDto, List<KingDeePayCommonQueryResultDto> kingDeePayCommon) {

        BatchAfterSalesDto byAfterSalesId = batchAfterSalesDtoMapper.findByAfterSalesId(batchInvoiceDto.getAfterSalesId());
        if (Objects.nonNull(byAfterSalesId)) {
            batchInvoiceDto.setAfterSalesNo(byAfterSalesId.getAfterSalesNo());
        }

        DecimalFormat decimalFormat = new DecimalFormat("0.00#");
        String taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? "0.00" : kingDeePayCommonAndInvoiceDto.getSpecial() ? decimalFormat.format(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP)) : "0.00";
        Map<String, List<KingDeePayCommonQueryResultDto>> entityGroup = kingDeePayCommon.stream().collect(Collectors.groupingBy(x -> StrUtil.split(x.getF_QZOK_BDDJHID(), "-").get(0)));
        if (kingDeePayCommonAndInvoiceDto.getSpecial()) {
            invoiceSpecial(batchInvoiceDto, kingDeePayCommonAndInvoiceDto, taxRate, entityGroup);
        } else {
            invoicePlain(batchInvoiceDto, QZOK_FPLX, kingDeePayCommonAndInvoiceDto, entityGroup);
        }
    }

    /**
     * 普票
     * @param batchInvoiceDto
     * @param QZOK_FPLX
     * @param kingDeePayCommonAndInvoiceDto
     * @param entityGroup
     */
    private void invoicePlain(BatchInvoiceDto batchInvoiceDto, String QZOK_FPLX, KingDeePayCommonAndInvoiceDto kingDeePayCommonAndInvoiceDto, Map<String, List<KingDeePayCommonQueryResultDto>> entityGroup) {
        PurchaseVatPlainInvoiceDto dto = new PurchaseVatPlainInvoiceDto();
        dto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
        Boolean exist = kingDeeBaseApi.isExist(dto);
        if (exist) {
            return;
        }
        kingDeePayCommonAndInvoiceDto.setPurchaseVatPlainInvoiceDto(dto);

        // 1电票 2 纸票
        dto.setFQzokFplx(QZOK_FPLX);
        dto.setFid("0");
        dto.setFdate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
        dto.setFinvoicedate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
        dto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
        dto.setFsupplierid(batchInvoiceDto.getTraderSupplierId().toString());
        dto.setFdocumentstatus("Z");
        dto.setFBillTypeID("CGPTFP01_SYS");
        dto.setFsettleorgid(KingDeeConstant.ORG_ID.toString());
        dto.setFpurchaseorgid(KingDeeConstant.ORG_ID.toString());
        dto.setFCancelStatus("A");
        dto.setFRedBlue("1");
        dto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());

        // 二级
        List<PurchaseVatPlainInvoiceDetailDto> FPURCHASEICENTRY = new ArrayList<>();
        dto.setFPURCHASEICENTRY(FPURCHASEICENTRY);
        batchInvoiceDto.getBatchInvoiceDetailDtoList().forEach(c -> {
            List<KingDeePayCommonQueryResultDto> kingDeePayCommonQueryResultDtos = entityGroup.get(c.getInvoiceDetailId().toString());
            if (CollUtil.isEmpty(kingDeePayCommonQueryResultDtos)) {
                log.error("应付单中未查询到次明细：{}数据", c.getInvoiceDetailId());
                return;
            }
            PurchaseVatPlainInvoiceDetailDto sonDto = new PurchaseVatPlainInvoiceDetailDto();
            sonDto.setFmaterialid(c.getSku());
            BigDecimal priceQty = kingDeePayCommonQueryResultDtos.stream().map(e -> new BigDecimal(e.getFPriceQty())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            sonDto.setFpriceqty(priceQty.toString());
            sonDto.setFauxtaxprice(kingDeePayCommonQueryResultDtos.get(0).getFTaxPrice().toString());
            sonDto.setFQzokBddjhid(c.getInvoiceDetailId().toString());
            sonDto.setFsourcetype(KingDeeFormConstant.PAY_EXPENSES);
            sonDto.setFQzokYsddh(batchInvoiceDto.getOrderNo());
            sonDto.setFQzokGsywdh(batchInvoiceDto.getAfterSalesNo());
            sonDto.setFQzokYwlx("采购退货");

            // 三级
            List<PurchaseVatPlainInvoiceDetailLinkDto> fpurchaseicentryLink = new ArrayList<>();
            sonDto.setFpurchaseicentryLink(fpurchaseicentryLink);
            FPURCHASEICENTRY.add(sonDto);
            kingDeePayCommonQueryResultDtos.forEach(x->{
                PurchaseVatPlainInvoiceDetailLinkDto inSon = new PurchaseVatPlainInvoiceDetailLinkDto();
                inSon.setFLinkId("0");
                inSon.setFpurchaseicentryLinkFsbillid(x.getFID());
                inSon.setFpurchaseicentryLinkFsid(x.getFEntityDetail_FEntryId());
                inSon.setFpurchaseicentryLinkFruleid("IV_PayableToPurchaseIC");
                inSon.setFpurchaseicentryLinkFstableid("0");
                inSon.setFpurchaseicentryLinkFstablename("T_AP_PAYABLEENTRY");
                inSon.setFpurchaseicentryLinkFbasicunitqtyold(x.getFPriceQty());
                inSon.setFpurchaseicentryLinkFbasicunitqty(x.getFPriceQty());
                inSon.setFpurchaseicentryLinkFallamountforold(new BigDecimal(x.getFALLAMOUNTFOR_D()));
                inSon.setFpurchaseicentryLinkFallamountfor(new BigDecimal(x.getFALLAMOUNTFOR_D()));
                fpurchaseicentryLink.add(inSon);
            });
        });
    }

    /**
     * 专票
     * @param batchInvoiceDto
     * @param kingDeePayCommonAndInvoiceDto
     * @param taxRate
     * @param entityGroup
     */
    private void invoiceSpecial(BatchInvoiceDto batchInvoiceDto, KingDeePayCommonAndInvoiceDto kingDeePayCommonAndInvoiceDto, String taxRate, Map<String, List<KingDeePayCommonQueryResultDto>> entityGroup) {
        PurchaseVatSpecialInvoiceDto dto = new PurchaseVatSpecialInvoiceDto();
        dto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
        Boolean exist = kingDeeBaseApi.isExist(dto);
        if (exist) {
            return;
        }
        kingDeePayCommonAndInvoiceDto.setPurchaseVatSpecialInvoiceDto(dto);
        dto.setFid("0");
        dto.setFdate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
        dto.setFinvoicedate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getValidTime())));
        dto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
        dto.setFsupplierid(batchInvoiceDto.getTraderSupplierId().toString());
        dto.setFdocumentstatus("Z");
        dto.setFBillTypeID("CGZZSZYFP01_SYS");
        dto.setFsettleorgid(KingDeeConstant.ORG_ID.toString());
        dto.setFCancelStatus("A");
        dto.setFRedBlue("1");
        dto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());

        // 二级
        List<PurchaseVatSpecialInvoiceDetailDto> FPURCHASEICENTRY = new ArrayList<>();
        dto.setFPURCHASEICENTRY(FPURCHASEICENTRY);
        batchInvoiceDto.getBatchInvoiceDetailDtoList().forEach(c -> {
            PurchaseVatSpecialInvoiceDetailDto sonDto = new PurchaseVatSpecialInvoiceDetailDto();
            List<KingDeePayCommonQueryResultDto> kingDeePayCommonQueryResultDtos = entityGroup.get(c.getInvoiceDetailId().toString());
            if (CollUtil.isEmpty(kingDeePayCommonQueryResultDtos)) {
                log.error("应付单中未查询到次明细：{}数据", c.getInvoiceDetailId());
                return;
            }
            sonDto.setFmaterialid(c.getSku());
            BigDecimal priceQty = kingDeePayCommonQueryResultDtos.stream().map(e -> new BigDecimal(e.getFPriceQty())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            sonDto.setFpriceqty(priceQty.toString());
            sonDto.setFauxtaxprice(kingDeePayCommonQueryResultDtos.get(0).getFTaxPrice());
            sonDto.setFtaxrate(taxRate);
            BigDecimal noTaxAmountFor = kingDeePayCommonQueryResultDtos.stream().map(e -> new BigDecimal(e.getFNoTaxAmountFor_D())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            sonDto.setFamountfor(noTaxAmountFor);
            BigDecimal taxAmountFor = kingDeePayCommonQueryResultDtos.stream().map(e -> new BigDecimal(e.getFTAXAMOUNTFOR_D())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            sonDto.setFdetailtaxamountfor(taxAmountFor);
            BigDecimal allAmountFor = kingDeePayCommonQueryResultDtos.stream().map(e -> new BigDecimal(e.getFALLAMOUNTFOR_D())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            sonDto.setFallamountfor(allAmountFor);
            sonDto.setFQzokBddjhid(c.getInvoiceDetailId().toString());
            sonDto.setFsourcetype(KingDeeFormConstant.PAY_EXPENSES);
            sonDto.setFQzokYsddh(batchInvoiceDto.getOrderNo());
            sonDto.setFQzokGsywdh(batchInvoiceDto.getAfterSalesNo());
            sonDto.setFQzokYwlx("采购退货");

            // 三级
            List<PurchaseVatSpecialInvoiceDetailLinkDto> fpurchaseicentryLink = new ArrayList<>();
            sonDto.setFPURCHASEICENTRY_Link(fpurchaseicentryLink);
            FPURCHASEICENTRY.add(sonDto);
            kingDeePayCommonQueryResultDtos.forEach(x->{
                PurchaseVatSpecialInvoiceDetailLinkDto inSon = new PurchaseVatSpecialInvoiceDetailLinkDto();
                inSon.setFLinkId("0");
                inSon.setFpurchaseicentryLinkFsbillid(x.getFID());
                inSon.setFpurchaseicentryLinkFsid(x.getFEntityDetail_FEntryId());
                inSon.setFpurchaseicentryLinkFflowlineid("0");
                inSon.setFpurchaseicentryLinkFruleid("IV_PayableToPurchaseIC");
                inSon.setFpurchaseicentryLinkFstableid("0");
                inSon.setFpurchaseicentryLinkFstablename("T_AP_PAYABLEENTRY");
                inSon.setFpurchaseicentryLinkFbasicunitqtyold(x.getFPriceQty());
                inSon.setFpurchaseicentryLinkFbasicunitqty(x.getFPriceQty());
                inSon.setFpurchaseicentryLinkFallamountforold(new BigDecimal(x.getFALLAMOUNTFOR_D()));
                inSon.setFpurchaseicentryLinkFallamountfor(new BigDecimal(x.getFALLAMOUNTFOR_D()));
                fpurchaseicentryLink.add(inSon);
            });

        });
    }
}
