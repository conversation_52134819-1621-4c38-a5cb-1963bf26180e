<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.GoodsFinanceMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.GoodsFinance">
    <!--@mbg.generated-->
    <!--@Table T_GOODS_FINANCE-->
    <id column="GOODS_FINANCE_ID" jdbcType="BIGINT" property="goodsFinanceId" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="IS_MEDICAL_EQUIPMENT" jdbcType="INTEGER" property="isMedicalEquipment" />
    <result column="MEDICAL_EQUIPMENT_TYPE" jdbcType="VARCHAR" property="medicalEquipmentType" />
    <result column="MEDICAL_EQUIPMENT_USE" jdbcType="VARCHAR" property="medicalEquipmentUse" />
    <result column="MEDICAL_EQUIPMENT_LINE" jdbcType="VARCHAR" property="medicalEquipmentLine" />
    <result column="IS_PUSH" jdbcType="BOOLEAN" property="isPush" />
    <result column="PUSH_TIME" jdbcType="TIMESTAMP" property="pushTime" />
    <result column="SKU_ADD_TIME" jdbcType="TIMESTAMP" property="skuAddTime" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />

    <result column="AUDIT_STATUS" property="auditStatus"/>
    <result column="IS_INSTALLATION" property="isInstallation"/>
    <result column="APPLY_TIME" property="applyTime"/>
    <result column="APPLY_USER_ID" property="applyUserId"/>
    <result column="APPLY_USERNAME" property="applyUsername"/>
    <result column="AUDIT_TIME" property="auditTime"/>
    <result column="AUDIT_USER_ID" property="auditUserId"/>
    <result column="AUDIT_USERNAME" property="auditUsername"/>
    <result column="AUDIT_REMARK" property="auditRemark"/>
  </resultMap>

  <resultMap id="ExtResultMap" type="com.vedeng.goods.domain.dto.GoodsFinanceInfoDto" extends="BaseResultMap">
    <result column="PUSH_TIME" jdbcType="VARCHAR" property="pushTime" />
    <result column="ADD_TIME" jdbcType="VARCHAR" property="addTime" />
    <result column="MOD_TIME" jdbcType="VARCHAR" property="modTime" />
    <result column="SHOW_NAME" jdbcType="VARCHAR" property="showName" />
    <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName" />
    <result column="SPU_TYPE" jdbcType="INTEGER" property="spuType" />
    <result column="SPU_TYPE_VALUE" jdbcType="VARCHAR" property="spuTypeValue" />
    <result column="SKU_ADD_TIME_STR" jdbcType="VARCHAR" property="skuAddTimeStr" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    GOODS_FINANCE_ID, SKU_ID, SKU_NO, IS_MEDICAL_EQUIPMENT, MEDICAL_EQUIPMENT_TYPE, MEDICAL_EQUIPMENT_USE,
    MEDICAL_EQUIPMENT_LINE, IS_PUSH, PUSH_TIME, SKU_ADD_TIME, IS_DELETE, ADD_TIME, CREATOR,
    CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, REMARK, UPDATE_REMARK,
    AUDIT_STATUS,IS_INSTALLATION,APPLY_TIME,APPLY_USER_ID,APPLY_USERNAME,AUDIT_TIME,AUDIT_USER_ID,AUDIT_USERNAME,AUDIT_REMARK

  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_GOODS_FINANCE
    where GOODS_FINANCE_ID = #{goodsFinanceId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_GOODS_FINANCE
    where GOODS_FINANCE_ID = #{goodsFinanceId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="GOODS_FINANCE_ID" keyProperty="goodsFinanceId" parameterType="com.vedeng.goods.domain.entity.GoodsFinance" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_GOODS_FINANCE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="isMedicalEquipment != null">
        IS_MEDICAL_EQUIPMENT,
      </if>
      <if test="medicalEquipmentType != null">
        MEDICAL_EQUIPMENT_TYPE,
      </if>
      <if test="medicalEquipmentUse != null">
        MEDICAL_EQUIPMENT_USE,
      </if>
      <if test="medicalEquipmentLine != null">
        MEDICAL_EQUIPMENT_LINE,
      </if>
      <if test="isPush != null">
        IS_PUSH,
      </if>
      <if test="pushTime != null">
        PUSH_TIME,
      </if>
      <if test="skuAddTime != null">
        SKU_ADD_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="isMedicalEquipment != null">
        #{isMedicalEquipment,jdbcType=BOOLEAN},
      </if>
      <if test="medicalEquipmentType != null">
        #{medicalEquipmentType,jdbcType=VARCHAR},
      </if>
      <if test="medicalEquipmentUse != null">
        #{medicalEquipmentUse,jdbcType=VARCHAR},
      </if>
      <if test="medicalEquipmentLine != null">
        #{medicalEquipmentLine,jdbcType=VARCHAR},
      </if>
      <if test="isPush != null">
        #{isPush,jdbcType=BOOLEAN},
      </if>
      <if test="pushTime != null">
        #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="skuAddTime != null">
        #{skuAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>





  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.domain.entity.GoodsFinance">
    <!--@mbg.generated-->
    update T_GOODS_FINANCE
    <set>
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null ">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="isMedicalEquipment != null">
        IS_MEDICAL_EQUIPMENT = #{isMedicalEquipment,jdbcType=BOOLEAN},
      </if>
      <if test="medicalEquipmentType != null">
        MEDICAL_EQUIPMENT_TYPE = #{medicalEquipmentType,jdbcType=VARCHAR},
      </if>
      <if test="medicalEquipmentUse != null ">
        MEDICAL_EQUIPMENT_USE = #{medicalEquipmentUse,jdbcType=VARCHAR},
      </if>
      <if test="medicalEquipmentLine != null">
        MEDICAL_EQUIPMENT_LINE = #{medicalEquipmentLine,jdbcType=VARCHAR},
      </if>
      <if test="isPush != null">
        IS_PUSH = #{isPush,jdbcType=BOOLEAN},
      </if>
      <if test="pushTime != null">
        PUSH_TIME = #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null ">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="skuAddTime != null">
        SKU_ADD_TIME = #{skuAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null ">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="isInstallation != null">
        IS_INSTALLATION = #{isInstallation,jdbcType=INTEGER},
      </if>
      <if test="auditInstallation != null">
        AUDIT_INSTALLATION = #{auditInstallation,jdbcType=INTEGER},
      </if>
      <if test="applyTime != null">
        APPLY_TIME = #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyUserId != null">
        APPLY_USER_ID = #{applyUserId,jdbcType=INTEGER},
      </if>
      <if test="applyUsername != null">
        APPLY_USERNAME = #{applyUsername,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="auditUserId != null">
        AUDIT_USER_ID = #{auditUserId,jdbcType=INTEGER},
      </if>
      <if test="auditUsername != null ">
        AUDIT_USERNAME = #{auditUsername,jdbcType=VARCHAR},
      </if>
      <if test="auditRemark != null">
        AUDIT_REMARK = #{auditRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where GOODS_FINANCE_ID = #{goodsFinanceId,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.domain.entity.GoodsFinance">
    <!--@mbg.generated-->
    update T_GOODS_FINANCE
    set SKU_ID = #{skuId,jdbcType=INTEGER},
    SKU_NO = #{skuNo,jdbcType=VARCHAR},
    IS_MEDICAL_EQUIPMENT = #{isMedicalEquipment,jdbcType=BOOLEAN},
    MEDICAL_EQUIPMENT_TYPE = #{medicalEquipmentType,jdbcType=VARCHAR},
    MEDICAL_EQUIPMENT_USE = #{medicalEquipmentUse,jdbcType=VARCHAR},
    MEDICAL_EQUIPMENT_LINE = #{medicalEquipmentLine,jdbcType=VARCHAR},
    IS_PUSH = #{isPush,jdbcType=BOOLEAN},
    PUSH_TIME = #{pushTime,jdbcType=TIMESTAMP},
    IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
    ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
    CREATOR = #{creator,jdbcType=INTEGER},
    CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
    MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    SKU_ADD_TIME = #{skuAddTime,jdbcType=TIMESTAMP},
    UPDATER = #{updater,jdbcType=INTEGER},
    UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    REMARK = #{remark,jdbcType=VARCHAR},
    UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
    AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER},
    IS_INSTALLATION = #{isInstallation,jdbcType=INTEGER},
    APPLY_TIME = #{applyTime,jdbcType=TIMESTAMP},
    APPLY_USER_ID = #{applyUserId,jdbcType=INTEGER},
    APPLY_USERNAME = #{applyUsername,jdbcType=VARCHAR},
    AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP},
    AUDIT_USER_ID = #{auditUserId,jdbcType=INTEGER},
    AUDIT_USERNAME = #{auditUsername,jdbcType=VARCHAR},
    AUDIT_REMARK = #{auditRemark,jdbcType=VARCHAR}
    where GOODS_FINANCE_ID = #{goodsFinanceId,jdbcType=BIGINT}
  </update>


  <insert id="batchInsert" parameterType="com.vedeng.goods.domain.entity.GoodsFinance">
    insert into T_GOODS_FINANCE (SKU_NO,SKU_ID,IS_MEDICAL_EQUIPMENT,MEDICAL_EQUIPMENT_TYPE,SKU_ADD_TIME)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix=" (" suffix=")" suffixOverrides=",">
        #{item.skuNo,jdbcType=VARCHAR},#{item.skuId},#{item.isMedicalEquipment},#{item.medicalEquipmentType},#{item.skuAddTime}
      </trim>
    </foreach>
  </insert>

  <select id="getSkuNoListByValidTime" parameterType="java.lang.Long" resultType="com.vedeng.goods.domain.entity.GoodsFinance">
    select TSG.SKU                               skuNo,
    TSG.GOODS_ID                          skuId,
    IFNULL(P.MEDICAL_INSTRUMENT_CATALOG_INCLUDED,-1) isMedicalEquipment,
    IFNULL(K.ADD_TIME,CURRENT_TIMESTAMP) skuAddTime,
    IFNULL(SC.CATEGORY_NAME,'')                      medicalEquipmentType
    from T_SALEORDER T
    join T_SALEORDER_GOODS TSG ON T.SALEORDER_ID = TSG.SALEORDER_ID
    LEFT JOIN T_GOODS_FINANCE TGF ON TSG.SKU = TGF.SKU_NO
    INNER JOIN V_CORE_SKU K ON K.SKU_NO = TSG.SKU
    LEFT JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
    LEFT JOIN T_FIRST_ENGAGE FE ON P.FIRST_ENGAGE_ID = FE.FIRST_ENGAGE_ID
    LEFT JOIN T_STANDARD_CATEGORY SC ON FE.NEW_STANDARD_CATEGORY_ID = SC.STANDARD_CATEGORY_ID
    where T.VALID_STATUS = 1
    and TGF.SKU_NO is null
    and T.COMPANY_ID = 1
    and T.ORDER_TYPE != 2
    <if test="time!=null">
      and T.VALID_TIME &lt;= #{time}
    </if>
    GROUP BY TSG.SKU
  </select>

  <select id="selectGoodsFinanceBySkuNo" resultMap="ExtResultMap">
    SELECT F.GOODS_FINANCE_ID,
    F.SKU_NO,
    F.IS_MEDICAL_EQUIPMENT,
    F.MEDICAL_EQUIPMENT_TYPE,
    F.MEDICAL_EQUIPMENT_USE,
    F.MEDICAL_EQUIPMENT_LINE,
    F.IS_PUSH,
    DATE_FORMAT(F.PUSH_TIME,'%Y-%m-%d %H:%i:%s') AS PUSH_TIME,
    F.IS_DELETE,
    DATE_FORMAT(F.ADD_TIME,'%Y-%m-%d %H:%i:%s') AS ADD_TIME,
    F.CREATOR,
    F.CREATOR_NAME,
    DATE_FORMAT(F.MOD_TIME,'%Y-%m-%d %H:%i:%s') AS MOD_TIME,
    F.UPDATER,
    F.UPDATER_NAME,
    F.REMARK,
    F.UPDATE_REMARK,
    K.SHOW_NAME,
    T.UNIT_NAME,
    P.SPU_TYPE,
    D1.TITLE AS SPU_TYPE_VALUE
    FROM T_GOODS_FINANCE F
    LEFT JOIN V_CORE_SKU K ON F.SKU_ID = K.SKU_ID
    LEFT JOIN T_UNIT T ON K.BASE_UNIT_ID = T.UNIT_ID
    LEFT JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
    LEFT JOIN T_SYS_OPTION_DEFINITION D1 ON P.SPU_TYPE = D1.SYS_OPTION_DEFINITION_ID
    WHERE F.SKU_NO = #{skuNo,jdbcType=VARCHAR}
  </select>

  <select id="selectGoodsFinanceByPrimaryKey" resultMap="ExtResultMap">
    SELECT F.GOODS_FINANCE_ID,
    F.SKU_NO,
    F.IS_MEDICAL_EQUIPMENT,
    F.MEDICAL_EQUIPMENT_TYPE,
    F.MEDICAL_EQUIPMENT_USE,
    F.MEDICAL_EQUIPMENT_LINE,
    F.IS_PUSH,
    DATE_FORMAT(F.PUSH_TIME,'%Y-%m-%d %H:%i:%s') AS PUSH_TIME,
    F.IS_DELETE,
    DATE_FORMAT(F.ADD_TIME,'%Y-%m-%d %H:%i:%s') AS ADD_TIME,
    F.CREATOR,
    F.CREATOR_NAME,
    DATE_FORMAT(F.MOD_TIME,'%Y-%m-%d %H:%i:%s') AS MOD_TIME,
    DATE_FORMAT(F.SKU_ADD_TIME,'%Y-%m-%d %H:%i:%s') AS SKU_ADD_TIME_STR,
    F.UPDATER,
    F.UPDATER_NAME,
    F.REMARK,
    F.UPDATE_REMARK,
    K.SHOW_NAME,
    T.UNIT_NAME,
    P.SPU_TYPE,
    D1.TITLE AS SPU_TYPE_VALUE,
    F.AUDIT_STATUS,
    F.IS_INSTALLATION,
    F.AUDIT_INSTALLATION,
    F.APPLY_TIME,
    F.APPLY_USER_ID,
    F.APPLY_USERNAME,
    F.AUDIT_TIME,
    F.AUDIT_USER_ID,
    F.AUDIT_USERNAME,
    F.AUDIT_REMARK
    FROM T_GOODS_FINANCE F
    LEFT JOIN V_CORE_SKU K ON F.SKU_ID = K.SKU_ID
    LEFT JOIN T_UNIT T ON K.BASE_UNIT_ID = T.UNIT_ID
    LEFT JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
    LEFT JOIN T_SYS_OPTION_DEFINITION D1 ON P.SPU_TYPE = D1.SYS_OPTION_DEFINITION_ID
    WHERE F.GOODS_FINANCE_ID = #{goodsFinanceId,jdbcType=BIGINT}
  </select>

  <select id="selectBySkuNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_GOODS_FINANCE
    where SKU_NO = #{skuNo,jdbcType=VARCHAR}
  </select>

  <select id="selectBySkuNoList" parameterType="java.util.List" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_GOODS_FINANCE
    where SKU_NO IN
    <foreach collection="skuNoList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="getSkuNoListByQueryList" parameterType="java.util.List" resultType="java.lang.String">
    <!--@mbg.generated-->
    select
    SKU_NO
    from T_GOODS_FINANCE
    where SKU_NO IN
    <foreach collection="skuNoList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <update id="updateBatchGoodsFinanceBySkuNo" parameterType="java.util.List">
    update T_GOODS_FINANCE
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="IS_MEDICAL_EQUIPMENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isMedicalEquipment != null">
            when SKU_NO = #{item.skuNo,jdbcType=VARCHAR} then #{item.isMedicalEquipment,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="MEDICAL_EQUIPMENT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.medicalEquipmentType != null">
            when SKU_NO = #{item.skuNo,jdbcType=VARCHAR} then #{item.medicalEquipmentType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MEDICAL_EQUIPMENT_USE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.medicalEquipmentUse != null">
            when SKU_NO = #{item.skuNo,jdbcType=VARCHAR} then #{item.medicalEquipmentUse,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MEDICAL_EQUIPMENT_LINE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.medicalEquipmentLine != null">
            when SKU_NO = #{item.skuNo,jdbcType=VARCHAR} then #{item.medicalEquipmentLine,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when SKU_NO = #{item.skuNo,jdbcType=VARCHAR} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when SKU_NO = #{item.skuNo,jdbcType=VARCHAR} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when SKU_NO = #{item.skuNo,jdbcType=VARCHAR} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_PUSH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isPush != null">
            when SKU_NO = #{item.skuNo,jdbcType=VARCHAR} then #{item.isPush,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where SKU_NO in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.skuNo,jdbcType=VARCHAR}
    </foreach>
  </update>

  <update id="updateBatchSelective" parameterType="java.util.List">
    update T_GOODS_FINANCE
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="SKU_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuId != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.skuId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuNo != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.skuNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_MEDICAL_EQUIPMENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isMedicalEquipment != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.isMedicalEquipment,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="MEDICAL_EQUIPMENT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.medicalEquipmentType != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.medicalEquipmentType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MEDICAL_EQUIPMENT_USE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.medicalEquipmentUse != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.medicalEquipmentUse,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MEDICAL_EQUIPMENT_LINE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.medicalEquipmentLine != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.medicalEquipmentLine,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_PUSH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isPush != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=INTEGER} then #{item.isPush,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PUSH_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pushTime != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.pushTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuAddTime != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.skuAddTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateRemark != null">
            when GOODS_FINANCE_ID = #{item.goodsFinanceId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where GOODS_FINANCE_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.goodsFinanceId,jdbcType=BIGINT}
    </foreach>
  </update>

<!--auto generated by MybatisCodeHelper on 2024-04-01-->
  <select id="findByGoodsFinanceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_GOODS_FINANCE
    where GOODS_FINANCE_ID=#{goodsFinanceId,jdbcType=BIGINT}
  </select>
</mapper>