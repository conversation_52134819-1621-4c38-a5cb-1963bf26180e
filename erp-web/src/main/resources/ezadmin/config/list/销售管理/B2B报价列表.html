<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>B2B报价列表</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="b2bquoteorder" datasource="erp-reportdatasource" fix_number="1" append_column_url="1" append_row_url="1" empty_show="-" fixednumber="2" firstcol="numbers">
  <div id="appendHead">
   <style>
a{color:green}
</style>
  </div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab">
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline ">
     <label class="layui-form-label">报价单号</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="QUOTEORDER_NO" placeholder="" style="" alias="Q" jdbctype="">
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">订货号</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="SKU" placeholder="" style="" alias="C" jdbctype="">
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">产品名称</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " oper="like" type="text" name="GOODS_NAME" placeholder="" style="" alias="C" jdbctype="">
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">品牌名称</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " oper="like" type="text" name="BRAND_NAME" placeholder="" style="" alias="C" jdbctype="">
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">型号</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " oper="like" type="text" name="MODEL" placeholder="" style="" alias="C" jdbctype="">
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">联系人</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " oper="like" type="text" name="联系人" placeholder="联系人、手机、电话" style="" alias="concat(Q.TRADER_CONTACT_NAME,'-',Q.MOBILE,'-',Q.TELEPHONE)" jdbctype="">
     </div>
    </div>
       <div class=" layui-inline ">
           <label class="layui-form-label">销售人员</label>
           <div class="layui-input-inline">
               <select class=" layui-input list-search-item " type="search" name="CREATOR" placeholder="" style="" alias="Q" jdbctype="" data="select user_id K,username V from T_USER ORDER BY USERNAME" datatype="KVSQLCACHE" oper=""></select>
           </div>
       </div>
       <div class=" layui-inline ">
           <label class="layui-form-label">归属销售</label>
           <div class="layui-input-inline">
               <select class=" layui-input list-search-item " type="search" name="USER_ID" placeholder="" style="" alias="J" jdbctype="" data="select user_id K,username V from T_USER ORDER BY USERNAME" datatype="KVSQLCACHE" oper=""></select>
           </div>
       </div>
    <div class=" layui-inline "><label class="layui-form-label">创建时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="daterange" name="QADD_TIME" placeholder="" style="" alias="from_unixtime(Q.QADD_TIME/1000 ,'%Y-%m-%d %h:%i:%s')" jdbctype="" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">销售部门</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="orgb2b" name="ORG_ID" placeholder="" style="" alias="Q" jdbctype="" data="" datatype="" oper="IN"></object>
     </div>
    </div>
<!--    <div class=" layui-inline "><label class="layui-form-label">是否授权</label>-->
<!--     <div class="layui-input-inline">-->
<!--      <select class=" layui-input list-search-item " type="search" name="AUTHORIZATION_APPLY_ID" placeholder="" style="" alias="IF(IFNULL(CC.AUTHORIZATION_APPLY_ID,0)>0,1,0)" jdbctype="" data="yesno" datatype=""  ></select>-->
<!--     </div>-->
<!--    </div>-->
   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton">
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
<!--      <th item_name="QUOTEORDER_ID" name="QUOTEORDER_ID" body="td-text" order="" url="" opentype="" windowname="" datatype="" data style head="th">报价单ID</th>-->
      <th item_name="QUOTEORDER_NO" name="QUOTEORDER_NO" body="td-link" order="" url="/order/quote/getQuoteDetail.do?quoteorderId=${QUOTEORDER_ID}&viewType=2" opentype="PARENT" windowname="报价单" datatype="" data style head="th">报价单编号</th>
      <th item_name="QADD_TIME" name="QADD_TIME" body="td-text" order="" url="" opentype="" windowname=""   style head="th">创建时间</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style head="th">客户名称</th>
          <th item_name="IS_NEW_CUSTOMER" name="IS_NEW_CUSTOMER" body="td-select" order="" url="" opentype="" windowname="" datatype="" data="yesno" style head="th">新/老客户</th>

         <th item_name="TOTAL_AMOUNT" name="TOTAL_AMOUNT" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style head="th" jdbctype="NUMBER">报价金额</th>
      <th item_name="ORG_NAME" name="ORG_NAME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style head="th">销售部门</th>
      <th item_name="CREATOR" name="CREATOR" body="td-select" datatype="KVSQLCACHE" data="SELECT  USER_ID K,USERNAME V FROM T_USER  " order="" url="" opentype="" windowname="" datatype="" data="" style head="th">销售人员</th>
      <th item_name="USER_ID" name="USER_ID" body="td-select" datatype="KVSQLCACHE" data="SELECT  USER_ID K,USERNAME V FROM T_USER  " order="" url="" opentype="" windowname=""   style head="th">归属销售</th>
      <th item_name="MOBILE" name="MOBILE" body="td-text"   order="" url="" opentype="" windowname=""   style head="th">手机</th>
      <th item_name="TRADER_CONTACT_NAME" name="TRADER_CONTACT_NAME" body="td-text"   order="" url="" opentype="" windowname=""   style head="th">联系人</th>
<!--      <th item_name="BUSSINESS_CHANCE_NO" name="BUSSINESS_CHANCE_NO" body="td-text" order="" url="" opentype="" windowname="" style head="th">商机编号</th>-->
      <th item_name="FOLLOW_ORDER_STATUS" name="FOLLOW_ORDER_STATUS" body="td-select" order="" url="" opentype="" windowname=""
          datatype="JSON" data="[{&quot;V&quot;:&quot;跟单中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;已成单&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;已失单&quot;,&quot;K&quot;:&quot;2&quot;}]"
          style head="th">跟单状态</th>
           </tr>
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by Q.QUOTEORDER_ID DESC" groupby="GROUP BY Q.QUOTEORDER_ID">StringBuilder sql=new StringBuilder("

			SELECT Q.QUOTEORDER_ID,
	Q.QUOTEORDER_NO,FROM_UNIXTIME( Q.ADD_TIME / 1000 ,'%Y-%m-%d %H:%i:%s') QADD_TIME,
	Q.TRADER_NAME  , Q.TOTAL_AMOUNT,
	O.ORG_NAME,Q.CREATOR  ,J.USER_ID ,
          Q.IS_NEW_CUSTOMER,
    B.BUSSINESS_CHANCE_NO  ,
CASE Q.VALID_STATUS
		WHEN 0 THEN
		'未生效' ELSE '已生效'
	END VALID_STATUS,

		Q.FOLLOW_ORDER_STATUS,

CASE
		Q.FOLLOW_ORDER_TIME
		WHEN 0 THEN
		'' ELSE FROM_UNIXTIME( Q.FOLLOW_ORDER_TIME / 1000 ,'%Y-%m-%d %H:%i:%s')
	END FOLLOW_ORDER_TIME,

CASE
		Q.PURCHASING_TYPE
		WHEN 405 THEN
		'直接采购'
		WHEN 406 THEN
		'招投标采购'
	END PURCHASING_TYPE,

	Q.TERMINAL_TRADER_NAME TERMINAL_TRADER_NAME,
CASE
		Q.TERMINAL_TRADER_TYPE
		WHEN 426 THEN
		'科研医疗'
		WHEN 427 THEN
		'临床医疗'
	END TERMINAL_TRADER_TYPE,C.SKU,C.GOODS_NAME,C.BRAND_NAME,C.MODEL,Q.TRADER_CONTACT_NAME,Q.MOBILE
          ,concat(Q.TRADER_CONTACT_NAME,'-',Q.MOBILE,'-',Q.TELEPHONE) 联系人
	 FROM T_QUOTEORDER Q

	LEFT JOIN T_ORGANIZATION O ON Q.ORG_ID = O.ORG_ID
           LEFT JOIN T_QUOTEORDER_GOODS C ON Q.QUOTEORDER_ID = C.QUOTEORDER_ID

	LEFT JOIN T_BUSSINESS_CHANCE B ON Q.BUSSINESS_CHANCE_ID = B.BUSSINESS_CHANCE_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION S ON Q.PURCHASING_TIME = S.SYS_OPTION_DEFINITION_ID
	 left join T_R_TRADER_J_USER J ON J.TRADER_ID=Q.TRADER_ID AND J.TRADER_TYPE=1

 where 1=1 ");


       sql.append ( " and (1=2  " );

       if ( isNotBlank("EZ_SESSION_ORG_IDS_LIST_KEY","session")) {
           sql.append ( " or  Q.ORG_ID  in (" );
           sql.append ( $$("EZ_SESSION_ORG_IDS_LIST_KEY") );
           sql.append (  ") " );
       }

       if ( isNotBlank("EZ_SESSION_MY_USER_KEY","session")) {
           sql.append ( " or Q.CREATOR in (" );
           sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
           sql.append (  ") " );
       }

       if ( !isNotBlank("EZ_SESSION_MY_USER_KEY","session") &amp;&amp; !isNotBlank("EZ_SESSION_ORG_IDS_LIST_KEY","session")) {
          sql.append ( " or 1=1" );
       }
       sql.append (  ") " );

       list=search(sql );
       return list;</pre> count: <pre id="count" class="layui-code">SELECT 10000 #group </pre></td>
     </tr> 
    </tbody> 
   </table> 
  </div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <div id="appendFoot">
   <script>

   </script>
  </div> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>