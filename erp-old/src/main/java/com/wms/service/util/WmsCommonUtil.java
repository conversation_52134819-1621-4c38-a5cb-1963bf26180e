package com.wms.service.util;

import com.vedeng.common.util.DateUtil;
import com.wms.constant.CancelCodeConstant;
import com.wms.dto.WmsResponse;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;

public class WmsCommonUtil {

    public static final String SPLIT_CHAR = "_";

    /**
     * 为单号添加 时间戳 (废弃)
     * @return
     */
    public static String addTimestampForOrderNo(String orderNo){

        if(StringUtils.isEmpty(orderNo)){
            return Strings.EMPTY;
        }

        return orderNo ;
//                + SPLIT_CHAR + DateUtil.convertString(System.currentTimeMillis(),DateUtil.TIME_FORMAT_LONG);
    }

    /**
     * 获取原始的单号
     * 如果有下划线 那就取下划线第一个单号 否则取原始单号
     * @param orderNo
     * @return
     */
    public static String getOriginalOrderNo(String orderNo){

        if(orderNo.indexOf(SPLIT_CHAR) == -1){
            return orderNo;
        }

        return orderNo.split(SPLIT_CHAR)[0];
    }

    /**
     * 通过ResourceId获取WMS发票文件地址
     *
     * @param resourceId
     * @return
     */
    public static String addInvoiceFileUrl(String resourceId){
        if(StringUtils.isEmpty(resourceId)){
            return Strings.EMPTY;
        }

        return "https://file.vedeng.com/file/display/" + resourceId + ".pdf";
    }

    /**
     * 判断一下 入库单能否撤销成功
     * @return
     */
    public static boolean wmsInputOrderCanCacel(WmsResponse wmsResponse){

        //可以取消
        if("1".equals(wmsResponse.getReturnFlag())){
            return true;
        }

        //不可以取消
        if ("0".equals(wmsResponse.getReturnFlag())) {

            //但是在 993和998 可以取消
            if(CancelCodeConstant.INNO_EXIST_CODE.equals(wmsResponse.getReturnCode()) ||
                    CancelCodeConstant.FINISH_RECEIVE_CODE.equals(wmsResponse.getReturnCode())){
                return true;
            }

            return false;
        }

        return false;
    }


    /**
     * 判断一下 出库单能否撤销成功
     * @param wmsResponse wms返回的相应对象
     * @return true 取消成功 false 取消失败
     */
    public static boolean wmsOutputOrderCanCacel(WmsResponse wmsResponse){

        //可以取消
        if("1".equals(wmsResponse.getReturnFlag())){
            return true;
        }

        //不可以取消
        if ("0".equals(wmsResponse.getReturnFlag())) {

            //但是在992可取消
            if(CancelCodeConstant.OUTNO_EXIST_CODE.equals(wmsResponse.getReturnCode())){
                return true;
            }

            return false;
        }

        return false;
    }
}
