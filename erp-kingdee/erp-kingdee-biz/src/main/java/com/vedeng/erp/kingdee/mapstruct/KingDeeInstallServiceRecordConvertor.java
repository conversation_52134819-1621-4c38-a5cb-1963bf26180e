package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeInstallServiceRecordEntity;
import com.vedeng.erp.kingdee.dto.KingDeeInstallServiceRecordDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售安调单 entity 、dto 互转
 * @date
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeInstallServiceRecordConvertor extends BaseMapStruct<KingDeeInstallServiceRecordEntity, KingDeeInstallServiceRecordDto> {
	
	/**
     * DTO转Entity
     *
     * @param dto
     * @return
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "fid", source = "fid")
    @Mapping(target = "fBillNo", source = "FBillNo")
    @Mapping(target = "fQzokOrgId", source = "FQzokOrgid")
    @Mapping(target = "fQzokYssj", source = "FQzokYssj")
    @Mapping(target = "fQzokDate", source = "FQzokDate")
    @Mapping(target = "fQzokBcfwsl", source = "FQzokBcfwsl")
    @Mapping(target = "fQzokQssj", source = "FQzokQssj")
    @Mapping(target = "fQzokYsfs", source = "FQzokYsfs")
    @Mapping(target = "fQzokYsjl", source = "FQzokYsjl")
    @Mapping(target = "fQzokYsddh", source = "FQzokYsddh")
    @Mapping(target = "fQzokGsywdh", source = "FQzokGsywdh")
    @Mapping(target = "fQzokYwlx", source = "FQzokYwlx")
    @Mapping(target = "fQzokWlbm", source = "FQzokWlbm")
    @Mapping(target = "fQzokXlh", source = "FQzokXlh")
    @Mapping(target = "fQzokFid", source = "FQzokFid")
    @Mapping(target = "fQzokFentryid", source = "FQzokFentryid")
    @Mapping(target = "fQzokCkdh", source = "FQzokCkdh")
    @Override
    KingDeeInstallServiceRecordEntity toEntity(KingDeeInstallServiceRecordDto dto);

    /**
     * Entity转DTO
     *
     * @param entity
     * @return
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "fid", source = "fid")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "FQzokOrgid", source = "FQzokOrgId")
    @Mapping(target = "FQzokYssj", source = "FQzokYssj")
    @Mapping(target = "FQzokDate", source = "FQzokDate")
    @Mapping(target = "FQzokBcfwsl", source = "FQzokBcfwsl")
    @Mapping(target = "FQzokQssj", source = "FQzokQssj")
    @Mapping(target = "FQzokYsfs", source = "FQzokYsfs")
    @Mapping(target = "FQzokYsjl", source = "FQzokYsjl")
    @Mapping(target = "FQzokYsddh", source = "FQzokYsddh")
    @Mapping(target = "FQzokGsywdh", source = "FQzokGsywdh")
    @Mapping(target = "FQzokYwlx", source = "FQzokYwlx")
    @Mapping(target = "FQzokWlbm", source = "FQzokWlbm")
    @Mapping(target = "FQzokXlh", source = "FQzokXlh")
    @Mapping(target = "FQzokFid", source = "FQzokFid")
    @Mapping(target = "FQzokFentryid", source = "FQzokFentryid")
    @Mapping(target = "FQzokCkdh", source = "FQzokCkdh")
    @Override
    KingDeeInstallServiceRecordDto toDto(KingDeeInstallServiceRecordEntity entity);

    
}
