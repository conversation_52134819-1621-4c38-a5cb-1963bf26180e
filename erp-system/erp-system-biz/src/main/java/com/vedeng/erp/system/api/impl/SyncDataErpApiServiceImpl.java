package com.vedeng.erp.system.api.impl;

import com.vedeng.erp.system.api.SyncDataErpApiService;
import com.vedeng.erp.system.dto.SyncDataErpDto;
import com.vedeng.erp.system.domain.entity.SyncDataErpEntity;
import com.vedeng.erp.system.mapper.DataIdempotencyRecordMapper;
import com.vedeng.erp.system.service.SyncDataErpService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 本service 适用于A erp需要同步给B erp的
 */
@Service
public class SyncDataErpApiServiceImpl implements SyncDataErpApiService {

    @Autowired
    private SyncDataErpService syncDataErpService;

    @Autowired
    private DataIdempotencyRecordMapper dataIdempotencyRecordMapper;

    @Override
    public boolean checkBuyOrderFromStandard(String buyOrderNo) {
        int result = dataIdempotencyRecordMapper.countByBusinessDocumentNo(buyOrderNo);
        return result>0;
    }

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return syncDataErpService.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(SyncDataErpDto record) {
        SyncDataErpEntity entity = new SyncDataErpEntity();
        BeanUtils.copyProperties(record, entity);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setCreateUser("system");
        entity.setUpdateUser("system");
        entity.setIsDeleted(false);
        return syncDataErpService.insert(entity);
    }

    @Override
    public int insertSelective(SyncDataErpDto record) {
        SyncDataErpEntity entity = new SyncDataErpEntity();
        BeanUtils.copyProperties(record, entity);
        return syncDataErpService.insertSelective(entity);
    }

    @Override
    public SyncDataErpDto selectByPrimaryKey(Integer id) {
        SyncDataErpEntity entity = syncDataErpService.selectByPrimaryKey(id);
        if (entity == null) {
            return null;
        }
        SyncDataErpDto dto = new SyncDataErpDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    @Override
    public int updateByPrimaryKeySelective(SyncDataErpDto record) {
        SyncDataErpEntity entity = new SyncDataErpEntity();
        BeanUtils.copyProperties(record, entity);
        return syncDataErpService.updateByPrimaryKeySelective(entity);
    }

    @Override
    public int updateByPrimaryKey(SyncDataErpDto record) {
        SyncDataErpEntity entity = new SyncDataErpEntity();
        BeanUtils.copyProperties(record, entity);
        return syncDataErpService.updateByPrimaryKey(entity);
    }

    @Override
    public List<SyncDataErpDto> selectByProcessStatus(Integer processStatus,String businessType) {
        List<SyncDataErpEntity> entities = syncDataErpService.selectByProcessStatus(processStatus,businessType);
        return entities.stream().map(entity -> {
            SyncDataErpDto dto = new SyncDataErpDto();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SyncDataErpDto> selectByBusinessNo(String businessNo) {
        List<SyncDataErpEntity> entities = syncDataErpService.selectByBusinessNo(businessNo);
        return entities.stream().map(entity -> {
            SyncDataErpDto dto = new SyncDataErpDto();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }).collect(Collectors.toList());
    }
} 