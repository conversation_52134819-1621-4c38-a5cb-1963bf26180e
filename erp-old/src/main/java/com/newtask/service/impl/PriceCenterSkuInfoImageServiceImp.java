package com.newtask.service.impl;

import com.newtask.model.SkuInfoImageDto;
import com.newtask.service.PriceCenterSkuInfoImageService;
import com.vedeng.goods.dao.PriceCenterSkuInfoImageMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PriceCenterSkuInfoImageServiceImp implements PriceCenterSkuInfoImageService {

    @Autowired
    private PriceCenterSkuInfoImageMapper priceCenterSkuInfoImageMapper;

    @Override
    public List<SkuInfoImageDto> getSkuInfoImage() {
        return priceCenterSkuInfoImageMapper.getSkuInfoImage();
    }

    @Override
    public List<SkuInfoImageDto> getSkuInfoImageAll() {
        return priceCenterSkuInfoImageMapper.getSkuInfoImageAll();
    }
}
