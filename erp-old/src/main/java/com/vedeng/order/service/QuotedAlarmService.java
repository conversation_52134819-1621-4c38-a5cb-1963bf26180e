package com.vedeng.order.service;

import com.newtask.quoteorder.model.QuotedAlarmMessageDto;

import java.util.List;

/**
 * 报价单预警
 *
 * <AUTHOR> [<EMAIL>]
 */
public interface QuotedAlarmService {


    /**
     * 解除报价单预警
     *
     * @param quoteOrderKey
     * @param quotedAlarmMode
     * @param goodsIdList
     * @since ERP_LV_2020_38
     */
    void unbindQuotedAlarm(Integer quoteOrder<PERSON>ey, Integer quotedAlarmMode, List<Integer> goodsIdList);

    /**
     * 发送报价预警站内消息
     *
     * @param messageToSend
     * @since ERP_LV_2020_38
     */
    void sendQuotedAlarmMessage(QuotedAlarmMessageDto messageToSend);


}
