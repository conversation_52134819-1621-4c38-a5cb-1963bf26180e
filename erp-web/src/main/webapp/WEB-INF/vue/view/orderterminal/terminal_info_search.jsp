<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<head>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application"/>
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}"/>
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css"/>
<script type="text/javascript" src="<%=basePath%>/static/js/region/index.js?rnd=${resourceVersionKey}"></script>
    <style>
        .el-table__empty-block{
            height: auto  !important;
        }
        i{
            background: none ;
        }

        table {
            font-size: 14px;
        }

        table th {
            font-weight: normal;
        }
    </style>
</head>
<body style="height: auto;">
<div id="app">
    <el-row style="margin-left: 30px;margin-top: 20px;" v-if="this.scene == 1 && this.hasTerminalInfo == 1">
        <div style="font-size:14px;margin-bottom: 20px;">终端名称：{{this.chosenTerminal}} <el-button type="primary" size="medium"  @click="resetting()">重新搜索</el-button></div>
        
        
        <div style="font-size:14px;margin-bottom: 20px;">
            <span id="terminalTraderNatureMust" style="color: red">*</span>
            终端性质：
            <el-select v-model="terminalTraderNature" placeholder="请选择" size="small">
                <el-option label="请选择" :value="0" :key="0"></el-option>
                <el-option
                        v-for="item in natureOptions"
                        :key="item.sysOptionDefinitionId"
                        :label="item.title"
                        :value="item.sysOptionDefinitionId">
                </el-option>
            </el-select>
        </div>
        
<%--        <div class="infor_name " style="width: auto;">--%>
<%--            <label style="font-size: 14px;">销售区域<span id="regionMust">*</span>：</label>--%>

<%--        </div>--%>
<%--        <div class="f_left"  >--%>
<%--            <select class="input-small f_left mr10" name="province" id="orderTerminal-province" style="font-size: 14px;">--%>
<%--                <option value="0">请选择</option>--%>
<%--                <c:if test="${not empty provinceList }">--%>
<%--                    <c:forEach items="${provinceList }" var="province">--%>
<%--                        <option value="${province.regionId }"--%>
<%--                                <c:if test="${ orderTerminalDto.provinceId == province.regionId }">selected="selected"</c:if>>${province.regionName }</option>--%>
<%--                    </c:forEach>--%>
<%--                </c:if>--%>
<%--            </select>--%>
<%--            <select class="input-small f_left mr10" name="city" id="orderTerminal-city" style="font-size: 14px;">--%>
<%--                <option value="0">请选择</option>--%>
<%--                <c:if test="${not empty cityList }">--%>
<%--                    <c:forEach items="${cityList }" var="city">--%>
<%--                        <option value="${city.regionId }"--%>
<%--                                <c:if test="${ orderTerminalDto.cityId == city.regionId }">selected="selected"</c:if>>${city.regionName }</option>--%>
<%--                    </c:forEach>--%>
<%--                </c:if>--%>
<%--            </select>--%>
<%--            <select class="input-small f_left" name="zone" id="orderTerminal-area" style="font-size: 14px;">--%>
<%--                <option value="0">请选择</option>--%>
<%--                <c:if test="${not empty zoneList }">--%>
<%--                    <c:forEach items="${zoneList }" var="zone">--%>
<%--                        <option value="${zone.regionId }"--%>
<%--                                <c:if test="${ orderTerminalDto.areaId == zone.regionId }">selected="selected"</c:if>>${zone.regionName }</option>--%>
<%--                    </c:forEach>--%>
<%--                </c:if>--%>
<%--            </select>--%>
<%--            <div id="sales_area_msg_div" style="clear:both"></div>--%>
<%--        </div>--%>


        <div style="margin-top: 100px; text-align: center"><el-button type="primary" size="medium" @click="saveTerminal()">保存</el-button></div>
    </el-row>

    <el-row style="margin-left: 10px;margin-top: 10px;" v-if="this.scene == 0 || (this.scene == 1 && this.hasTerminalInfo == 0)">
        <el-row style="margin-bottom: 5px">
            <el-input
                    size="medium"
                    v-model="terminalTraderName"
                    placeholder="搜索选择终端"
                    maxlength="30"
                    style="width: 300px">
            </el-input>
            <el-button type="primary" size="medium"  @click="doSearch()">搜索</el-button>
            <i class="el-icon-warning" style="margin-left: 10px; color: #e6740e"></i>
            <span>请尽量输入精确终端名称，关键字需大于等于4个字</span>
        </el-row>

        <el-table size="small"
                  :data="tableData"
                style="width: 100%;">
            <el-table-column
                    label="终端名称"
                    >
                <template slot-scope="scope">
                    <div>
                        <el-tag v-if="!scope.row.dwhTerminalId">天眼查</el-tag><span style="flex-wrap: wrap;">{{scope.row.terminalName}}</span>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                    prop="address"
                    label="终端区域"
                    >
            </el-table-column>
            <el-table-column
                    prop="hosModelName"
                    label="终端类型"
                    >
            </el-table-column>
            <el-table-column
                    prop="hosLevelName"
                    label="终端等级"
                    >
            </el-table-column>
            <el-table-column
                    prop="unifiedSocialCreditIdentifier"
                    label="统一社会信用代码"
                   >
            </el-table-column>
            <el-table-column
                    label="操作"
                    >
                <template slot-scope="scope">
                    <el-button type="text" @click="chooseTerminal(scope.row)">选择</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-row style="margin-top: 20px; margin-bottom: 20px" v-if="showTyc">
            <span>没有结果？不够准确？请点击</span>
            <el-button type="primary" size="mini" plain @click="searchTyc()">天眼查搜索</el-button>
        </el-row>

        <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPageNo"
                :page-sizes="[20]"
                :page-size="currentSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalLines">
        </el-pagination>
    </el-row>
</div>
<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomerTerminal.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    const hasTerminalInfo = '${hasTerminalInfo}';
    const scene = '${scene}';
    const businessId = '${businessId}';
    const natureOptions = JSON.parse('${natureOptions}');

    new Vue({
        el: '#app',
        data() {
            return {
                terminalTraderName: '',
                currentSize: 20,
                currentPageNo: 1,
                totalLines: 0,
                tableData: [],
                natureOptions: natureOptions,
                showTyc: false,
                hasTerminalInfo: hasTerminalInfo,
                scene: scene,
                businessId: businessId,
                chosenTerminal: '',
                terminalTraderNature: 0,
                dwhTerminalId: '',
                unifiedSocialCreditIdentifier: '',
                organizationCode: '',
                provinceCode:'',
                cityCode:'',
                areaCode:'',
                provinceName:'',
                cityName:'',
                areaName:'',

            }
        },

        mounted() {
            loadingApp()
        },

        created() {
            this.initPageData();
        },

        methods: {
            // 销售订单列表页进入时，若已维护，则需要展示已维护的信息
            initPageData() {
                getTerminalTraderNature({"saleOrderId" : this.businessId}).then(res => {
                    this.terminalTraderNature = res.data.data;
                })
                if (this.scene == 1 && this.hasTerminalInfo == 1) {
                    getSaleOrderTerminalBySaleOrderId({"saleOrderId" : this.businessId}).then(res => {
                        this.chosenTerminal = res.data.data.terminalName;
                    })
                }
            },

            doSearch() {
                searchTerminalInfo({
                    "terminalTraderName": this.terminalTraderName,
                    "pageSize": this.currentSize,
                    "pageNum": this.currentPageNo
                }).then(res => {
                    this.tableData = res.data.data.list;
                    this.totalLines = res.data.data.total;
                    if (this.totalLines < 6) {
                        this.showTyc = true;
                    } else {
                        this.showTyc = false;
                    }
                })
            },

            resetting() {
                this.hasTerminalInfo = 0;
            },

            saveTerminal() {
                // var province = $("select[name=\"province\"] option:selected").val();
                // var city = $("select[name=\"city\"] option:selected").val();
                // var area = $("select[name=\"zone\"] option:selected").val();
                // if(province == undefined || province==0
                //     || city == undefined || city==0
                //     || area == undefined || area==0 ){
                //     this.$message.error('请选择销售区域');
                //     return ;
                // }
                if (this.terminalTraderNature === undefined || this.terminalTraderNature === 0) {
                    this.$message.error('请选择终端性质');
                    return;
                }


                let saleOrderTerminalDto = {
                    "saleOrderId": this.businessId,
                    "terminalName": this.chosenTerminal,
                    "dwhTerminalId": this.dwhTerminalId,
                    "unifiedSocialCreditIdentifier": this.unifiedSocialCreditIdentifier,
                    "organizationCode": this.organizationCode,
                    "terminalTraderNature": this.terminalTraderNature
                    // "provinceCode":$("select[name=\"province\"] option:selected").val(),
                    // "provinceName":$("select[name=\"province\"] option:selected").text(),
                    // "cityCode":$("select[name=\"city\"] option:selected").val(),
                    // "cityName":$("select[name=\"city\"] option:selected").text(),
                    // "areaCode":$("select[name=\"zone\"] option:selected").val(),
                    // "areaName":$("select[name=\"zone\"] option:selected").text()

                };
                saveSaleOrderTerminal(saleOrderTerminalDto).then(res=> {
                    var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
                    // parent.layer.close(index);
                    // parent.layer.alert("建链修改成功");
                    //setTimeout(function () {
                    //    window.parent.location.reload();
                   //},2000);
                    parent.layer.alert('建链修改成功', function(index){
                        window.parent.location.reload();
                    });
                })
            },

            searchTyc() {
                searchTycTerminal({
                    "terminalTraderName": this.terminalTraderName,
                    "pageSize": this.currentSize,
                    "pageNum": this.currentPageNo
                }).then(res => {
                    this.tableData = res.data.data.list;
                    this.totalLines = res.data.data.total;
                    this.showTyc = false;
                })
            },

            handleSizeChange(val) {
                this.currentSize = val;
                this.doSearch();
            },
            handleCurrentChange(val) {
                this.currentPageNo = val;
                this.doSearch();
            },

            chooseTerminal(row) {
                // 商机、报价、销售单编辑页，按照原逻辑直接渲染到父页面
                if (this.scene == 0) {
                    parent.layer.closeAll();
                    parent.receiveTerminal(row);
                } else {
                    // 销售订单列表页，则在当前弹窗保存
                    this.chosenTerminal = row.terminalName;
                    this.dwhTerminalId = row.dwhTerminalId;
                    this.unifiedSocialCreditIdentifier = row.unifiedSocialCreditIdentifier;
                    this.organizationCode = row.organizationCode;
                    this.hasTerminalInfo = 1;
                    setTimeout(function () {
                        refreshLocation();
                        initRegProCityArea(row.address);
                    },1000);
                }
            }
        }
    })
</script>
<script type="text/javascript">
    // 根据省市区的地址字符串解析出对应的regionId
    function initRegProCityArea(regAddress) {
        var regLocation = "";//获取页面是否有经过天眼查带过来的注册地址
        if (regAddress.length > 0) {
            regLocation = regAddress;
            //第一步，先判断地址中，是否有省份
            var provinceNameDefault = "";
            var cityNameDefault = "";
            var areaNameDefault = "";
            if(regLocation.startsWith("北京")){  //先判断是否是四个直辖市
                provinceNameDefault = "北京市";
                cityNameDefault = "北京市";
                regLocation = regLocation.replaceAll("北京市","");
            } else if(regLocation.startsWith("天津")){
                provinceNameDefault = "天津市";
                cityNameDefault = "天津市";
                regLocation = regLocation.replaceAll("天津市","");
            }else if(regLocation.startsWith("上海")){
                provinceNameDefault = "上海市";
                cityNameDefault = "上海市";
                regLocation = regLocation.replaceAll("上海市","");
            }else if(regLocation.startsWith("重庆")){
                provinceNameDefault = "重庆市";
                cityNameDefault = "重庆市";
                regLocation = regLocation.replaceAll("重庆市","");
            }else{
                $("select[name=\"province\"] option").each(function() {
                    var optionText = $(this).text();
                    var optionSuf = optionText.replaceAll("省","").replaceAll("自治区","");
                    if(regLocation.startsWith(optionSuf)){
                        provinceNameDefault = optionText;
                        if(regLocation.startsWith(optionText)){
                            regLocation = regLocation.replaceAll(optionText,"");
                        }else{
                            regLocation = regLocation.replaceAll(optionSuf,"");
                        }
                        return false;//跳出该each循环
                    }
                });
            }
            if(provinceNameDefault == ''){//未匹配到省份，再匹配一次城市，有可能地址是	岳阳县新开镇胜天村长塘组- 实际是岳阳市。
                $.ajax({
                    type : "POST",
                    url : page_url+"/system/region/getRegionCityAll.do",
                    dataType : 'json',
                    async:true,
                    success : function(data) {
                        var provinceCode = 0;
                        $.each(data.listData,function(i,n){
                            var regionName = data.listData[i]['regionName']; //岳阳市   regLocation 岳阳县新开镇胜天村长塘组 regionName  岳阳市
                            var cityNameSuf = regionName.replaceAll("县","").replaceAll("市","");
                            if(regLocation.startsWith(regionName) || regLocation.startsWith(cityNameSuf)){
                                cityNameDefault = regionName;
                                regLocation = regLocation.startsWith(regionName)?regLocation.replaceAll(regionName,""):regLocation.replaceAll(cityNameSuf,"");
                                provinceCode =data.listData[i]['parentId'];
                                return false;
                            }
                        });
                        $('select[name="province"]').val(provinceCode);
                        $("select[name='province']").trigger('change');
                        //$('input[name="provinceName"]').val($('#orderTerminal-province').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-province').find('option:selected').text());

                        setTimeout(function () {
                            $("select[name='city'] option:contains('"+cityNameDefault+"')").prop("selected", true);
                            $("select[name='city']").trigger('change');
                            //$('input[name="cityId"]').val($('select[name="cityId"]').val());
                            //$('input[name="cityName"]').val($('#orderTerminal-city').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-city').find('option:selected').text());


                            setTimeout(function(){  //计算区的选择逻辑
                                $("select[name=\"zone\"] option").each(function() {
                                    var optionText = $(this).text();
                                    var optionSuf = optionText.replaceAll("区","").replaceAll("自治州","").replaceAll("市","");
                                    regLocation = regLocation.startsWith("市")?regLocation.replace("市",""):regLocation;
                                    regLocation = regLocation.startsWith("自治州")?regLocation.replace("自治州",""):regLocation;
                                    regLocation = regLocation.startsWith("县")?regLocation.replace("县",""):regLocation;

                                    if(regLocation.startsWith(optionSuf)){//optionSuf  相城
                                        areaNameDefault = optionText;
                                        //江苏苏州相城经济开发区（澄波路） 相城经济开发区（澄波路）
                                        regLocation = regLocation.replace(optionSuf,'');
                                        regLocation = regLocation.replace('区','');
                                        regLocation = regLocation.replace('自治区','');
                                        regLocation = regLocation.replace('县','');

                                        return false;//跳出该each循环
                                    }
                                });
                                if(areaNameDefault!=''){
                                    $("select[name='zone'] option:contains('"+areaNameDefault+"')").prop("selected", true);
                                    //$('input[name="areaId"]').val($('select[name="areaId"]').val());
                                    //$('input[name="areaName"]').val($('#orderTerminal-area').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-area').find('option:selected').text());

                                }
                            },500);
                        },500);

                    },
                    error:function(data){
                        if(data.status ==1001){
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                        }
                    }
                });
            }else{//如果省份已经匹配到了
                $("select[name='province'] option:contains('"+provinceNameDefault+"')").prop("selected", true);
                $("select[name='province']").trigger('change');
                //$('input[name="provinceId"]').val($('select[name="provinceId"]').val());
                //$('input[name="provinceName"]').val($('#orderTerminal-province').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-province').find('option:selected').text());

                setTimeout(function () {
                    if(cityNameDefault ==''){
                        $("select[name=\"city\"] option").each(function() {
                            var optionText = $(this).text();
                            var optionSuf = optionText.replaceAll("市","").replaceAll("县","").replaceAll("自治州","");
                            if(regLocation.startsWith(optionSuf)){
                                cityNameDefault = optionText;
                                regLocation = regLocation.startsWith(optionText)?regLocation.replaceAll(optionText,""):regLocation.replaceAll(optionSuf,"");
                                regLocation = regLocation.startsWith("市")?regLocation.replace("市",""):regLocation;
                                regLocation = regLocation.startsWith("自治州")?regLocation.replace("自治州",""):regLocation;
                                regLocation = regLocation.startsWith("县")?regLocation.replace("县",""):regLocation;
                                return false;//跳出该each循环
                            }
                        });
                        if(cityNameDefault  == ''){ //如果仍然没有匹配到城市
                            return ;
                        }
                    }
                    $("select[name='city'] option:contains('"+cityNameDefault+"')").prop("selected", true);
                    $("select[name='city']").trigger('change');
                    //$('input[name="cityId"]').val($('select[name="cityId"]').val());
                    //$('input[name="cityName"]').val($('#orderTerminal-city').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-city').find('option:selected').text());


                    setTimeout(function(){  //计算区的选择逻辑

                        $("select[name=\"zone\"] option").each(function() {
                            var optionText = $(this).text();
                            var optionSuf = optionText.replaceAll("区","").replaceAll("镇","");
                            regLocation = regLocation.startsWith("市")?regLocation.replace("市",""):regLocation;
                            regLocation = regLocation.startsWith("县")?regLocation.replace("县",""):regLocation;
                            if(regLocation.startsWith(optionSuf)){
                                areaNameDefault = optionText;
                                //江苏苏州相城经济开发区（澄波路） 相城经济开发区（澄波路）
                                regLocation = regLocation.replace(optionSuf,'');
                                regLocation = regLocation.replace('区','');
                                regLocation = regLocation.replace('自治区','');
                                regLocation = regLocation.replace('县','');
                                return false;//跳出该each循环
                            }
                        });
                        if(areaNameDefault!=''){
                            $("select[name='zone'] option:contains('"+areaNameDefault+"')").prop("selected", true);
                            //$('input[name="areaId"]').val($('select[name="areaId"]').val());
                            //$('input[name="areaName"]').val($('#orderTerminal-area').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-area').find('option:selected').text());

                        }
                    },500);
                },500);
            }
        }
    }


    function  refreshLocation() {
        $("select[name='province']").change(function(){
            checkLogin();
            var regionId = $(this).val();
            if(regionId > 0){
                $.ajax({
                    type : "POST",
                    url : page_url+"/system/region/getregion.do",
                    data :{'regionId':regionId},
                    dataType : 'json',
                    success : function(data) {
                        $option = "<option value='0'>请选择</option>";
                        $.each(data.listData,function(i,n){
                            $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
                        });
                        $("select[name='city'] option:gt(0)").remove();
                        $("select[name='zone'] option:gt(0)").remove();
                        $("#zone").val("0").trigger("change");
                        $("#city").val("0").trigger("change");

                        $("select[name='city']").html($option);
                        $("select[name='zone']").html("<option value='0'>请选择</option>");
                    },
                    error:function(data){
                        if(data.status ==1001){
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            }else if(regionId==0){
                $("select[name='city'] option:gt(0)").remove();
                $("select[name='zone'] option:gt(0)").remove();
            }
        });

        $("select[name='city']").change(function(){
            checkLogin();
            var regionId = $(this).val();
            if(regionId > 0){
                $.ajax({
                    type : "POST",
                    url : page_url+"/system/region/getregion.do",
                    data :{'regionId':regionId},
                    dataType : 'json',
                    success : function(data) {
                        $option = "<option value='0'>请选择</option>";
                        $.each(data.listData,function(i,n){
                            $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
                        });
                        $("select[name='zone'] option:gt(0)").remove();

                        $("select[name='zone']").html($option);
                    },
                    error:function(data){
                        if(data.status ==1001){
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            }else if(regionId==0){
                $("select[name='zone'] option:gt(0)").remove();
            }
        });
    }
</script>

<style>
    .el-input--mini .el-input__icon {
        line-height: 0;
    }

    .el-pagination .btn-next .el-icon, .el-pagination .btn-prev .el-icon {
        margin-right: 5px;
    }
</style>

</body>