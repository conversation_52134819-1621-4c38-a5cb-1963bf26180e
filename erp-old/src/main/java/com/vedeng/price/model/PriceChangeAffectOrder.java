package com.vedeng.price.model;

public class PriceChangeAffectOrder {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_PRICE_CHANGE_AFFECT_ORDER.PRICE_CHANGE_AFFECT_ORDER_ID
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    private Long priceChangeAffectOrderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_PRICE_CHANGE_AFFECT_ORDER.SKU_PRICE_MODIFY_RECORD_ID
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    private Long skuPriceModifyRecordId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_PRICE_CHANGE_AFFECT_ORDER.ORDER_ID
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    private Long orderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_PRICE_CHANGE_AFFECT_ORDER.ORDER_TYPE
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    private Integer orderType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_PRICE_CHANGE_AFFECT_ORDER.ADD_TIME
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    private Long addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_PRICE_CHANGE_AFFECT_ORDER.IS_DEALED
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    private Boolean isDealed;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_PRICE_CHANGE_AFFECT_ORDER.DEAL_TIME
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    private Long dealTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_PRICE_CHANGE_AFFECT_ORDER.PRICE_CHANGE_AFFECT_ORDER_ID
     *
     * @return the value of T_PRICE_CHANGE_AFFECT_ORDER.PRICE_CHANGE_AFFECT_ORDER_ID
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public Long getPriceChangeAffectOrderId() {
        return priceChangeAffectOrderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_PRICE_CHANGE_AFFECT_ORDER.PRICE_CHANGE_AFFECT_ORDER_ID
     *
     * @param priceChangeAffectOrderId the value for T_PRICE_CHANGE_AFFECT_ORDER.PRICE_CHANGE_AFFECT_ORDER_ID
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public void setPriceChangeAffectOrderId(Long priceChangeAffectOrderId) {
        this.priceChangeAffectOrderId = priceChangeAffectOrderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_PRICE_CHANGE_AFFECT_ORDER.SKU_PRICE_MODIFY_RECORD_ID
     *
     * @return the value of T_PRICE_CHANGE_AFFECT_ORDER.SKU_PRICE_MODIFY_RECORD_ID
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public Long getSkuPriceModifyRecordId() {
        return skuPriceModifyRecordId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_PRICE_CHANGE_AFFECT_ORDER.SKU_PRICE_MODIFY_RECORD_ID
     *
     * @param skuPriceModifyRecordId the value for T_PRICE_CHANGE_AFFECT_ORDER.SKU_PRICE_MODIFY_RECORD_ID
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public void setSkuPriceModifyRecordId(Long skuPriceModifyRecordId) {
        this.skuPriceModifyRecordId = skuPriceModifyRecordId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_PRICE_CHANGE_AFFECT_ORDER.ORDER_ID
     *
     * @return the value of T_PRICE_CHANGE_AFFECT_ORDER.ORDER_ID
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public Long getOrderId() {
        return orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_PRICE_CHANGE_AFFECT_ORDER.ORDER_ID
     *
     * @param orderId the value for T_PRICE_CHANGE_AFFECT_ORDER.ORDER_ID
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_PRICE_CHANGE_AFFECT_ORDER.ORDER_TYPE
     *
     * @return the value of T_PRICE_CHANGE_AFFECT_ORDER.ORDER_TYPE
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public Integer getOrderType() {
        return orderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_PRICE_CHANGE_AFFECT_ORDER.ORDER_TYPE
     *
     * @param orderType the value for T_PRICE_CHANGE_AFFECT_ORDER.ORDER_TYPE
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_PRICE_CHANGE_AFFECT_ORDER.ADD_TIME
     *
     * @return the value of T_PRICE_CHANGE_AFFECT_ORDER.ADD_TIME
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_PRICE_CHANGE_AFFECT_ORDER.ADD_TIME
     *
     * @param addTime the value for T_PRICE_CHANGE_AFFECT_ORDER.ADD_TIME
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_PRICE_CHANGE_AFFECT_ORDER.IS_DEALED
     *
     * @return the value of T_PRICE_CHANGE_AFFECT_ORDER.IS_DEALED
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public Boolean getIsDealed() {
        return isDealed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_PRICE_CHANGE_AFFECT_ORDER.IS_DEALED
     *
     * @param isDealed the value for T_PRICE_CHANGE_AFFECT_ORDER.IS_DEALED
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public void setIsDealed(Boolean isDealed) {
        this.isDealed = isDealed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_PRICE_CHANGE_AFFECT_ORDER.DEAL_TIME
     *
     * @return the value of T_PRICE_CHANGE_AFFECT_ORDER.DEAL_TIME
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public Long getDealTime() {
        return dealTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_PRICE_CHANGE_AFFECT_ORDER.DEAL_TIME
     *
     * @param dealTime the value for T_PRICE_CHANGE_AFFECT_ORDER.DEAL_TIME
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public void setDealTime(Long dealTime) {
        this.dealTime = dealTime;
    }
}