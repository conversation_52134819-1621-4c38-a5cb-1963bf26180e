package com.vedeng.common.feign.domain;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.ConfigService;
import com.vedeng.common.feign.exception.FeignException;
import com.vedeng.common.feign.support.ApacheHttpClient;
import feign.Feign;
import feign.Retryer;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import feign.spring.SpringContract;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: feign工厂bean
 * @date 2021/12/9 20:54
 */
@Slf4j
public class FeignFactoryBean<T> implements FactoryBean<T>, ApplicationContextAware {

    private static final int DEFAULT_RETRY_COUNT = 3;

    private static final int DEFAULT_RETRY_INTERVAL_TIME = 200;

    private static final int MAX_DEFAULT_RETRY_INTERVAL_TIME = 5000;

    private Class<T> targetClass;

    private String serverName;

    private String serverUrl;

    private Integer maxConnTotal;
    private Integer timeout;
    private Integer retry;
    private Boolean keepAlive;

    private boolean defaultFeign = false;

    private ApplicationContext applicationContext;


    public FeignFactoryBean() {

    }

    public FeignFactoryBean(Class<T> targetClass) {
        this.targetClass = targetClass;
    }


    @Override
    public boolean isSingleton() {
        return true;
    }

    @Override
    public T getObject() throws Exception {
        if (StrUtil.isBlank(serverUrl)) {
            String server = ConfigService.getAppConfig().getProperty("feign.server", "");
            JSONObject jsonObject = JSON.parseObject(server);
            String url = jsonObject.getString(serverName);
            if (StrUtil.isBlank(url)) {
                throw new FeignException(StrUtil.format("未找到url为：[{}]服务", url));
            }
            if ("uacServer".equals(serverName)){
                serverUrl = "http://qa.uac.ivedeng.com";
//                serverUrl = "http://172.16.9.227:8190";
            } else if ("priceServer".equals(serverName)){
                serverUrl = "http://192.168.2.140:8680";
            }else {
                serverUrl = url;
            }
            serverUrl = url;
        }

        Feign.Builder builder = Feign.builder()
                .encoder(new JacksonEncoder())
                .decoder(new JacksonDecoder())
                .retryer(new Retryer.Default(DEFAULT_RETRY_INTERVAL_TIME, MAX_DEFAULT_RETRY_INTERVAL_TIME, DEFAULT_RETRY_COUNT));
        if (!defaultFeign) {
            builder = getFeignBuilder(maxConnTotal, timeout, retry, keepAlive);
        }
        log.info("feign工厂生成bean{}", targetClass);
        return builder.target(targetClass, serverUrl);
    }

    @Override
    public Class<?> getObjectType() {
        return targetClass;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public ApplicationContext getApplicationContext() {
        return applicationContext;
    }


    public Feign.Builder getFeignBuilder(int maxConn, int timeout, int retry, boolean keepAlive) {
        return Feign.builder().
                client(new ApacheHttpClient(
                        getDefaultHttpClientPool(
                                maxConn,
                                timeout,
                                0,
                                keepAlive
                        )))
                .contract(new SpringContract())
                .encoder(new JacksonEncoder())
                .decoder(new JacksonDecoder())
                .retryer(new Retryer.Default(DEFAULT_RETRY_INTERVAL_TIME, MAX_DEFAULT_RETRY_INTERVAL_TIME, retry));
    }

    public static HttpClient getDefaultHttpClientPool(int maxTotal, int timeout, int retry, boolean keepAlive) {
        PoolingHttpClientConnectionManager manager = new PoolingHttpClientConnectionManager();
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(timeout)
                .setSocketTimeout(timeout)
                .build();
        HttpClientBuilder httpClient = HttpClientBuilder.create()
                .setConnectionManager(manager)
                .setDefaultRequestConfig(requestConfig)
                .setMaxConnPerRoute(maxTotal)
                .setMaxConnTotal(maxTotal)
                .setRetryHandler(new DefaultHttpRequestRetryHandler(retry, true));
        if (keepAlive) {
            httpClient.setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy());
        }
        return httpClient.build();
    }

    public Class<?> getTargetClass() {
        return targetClass;
    }

    public void setTargetClass(Class<T> targetClass) {
        this.targetClass = targetClass;
    }

    public String getServerName() {
        return serverName;
    }

    public void setServerName(String serverName) {
        this.serverName = serverName;
    }

    public String getServerUrl() {
        return serverUrl;
    }

    public void setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    public Integer getMaxConnTotal() {
        return maxConnTotal;
    }

    public void setMaxConnTotal(Integer maxConnTotal) {
        this.maxConnTotal = maxConnTotal;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public Integer getRetry() {
        return retry;
    }

    public void setRetry(Integer retry) {
        this.retry = retry;
    }

    public Boolean getKeepAlive() {
        return keepAlive;
    }

    public void setKeepAlive(Boolean keepAlive) {
        this.keepAlive = keepAlive;
    }

    public boolean isDefaultFeign() {
        return defaultFeign;
    }

    public void setDefaultFeign(boolean defaultFeign) {
        this.defaultFeign = defaultFeign;
    }
}

