package com.vedeng.erp.finance.domain.dto;

import com.vedeng.erp.finance.enums.TaxesInterfaceCodeEnum;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class TaxesGetInvoiceRequestDto implements ITaxesParam {

    private String requestcode;

    private List<DataParam> data;

    @Data
    public static class DataParam{
        /**
         * 唯一编号
         */
        private String xsdjbh;
    }
    public TaxesGetInvoiceRequestDto(String xsdjbh, TaxesInterfaceCodeEnum taxesInterfaceCodeEnum){
        List<DataParam> data = new ArrayList<>();
        DataParam dataParam = new DataParam();
        dataParam.setXsdjbh(xsdjbh);
        data.add(dataParam);
        this.data = data;
        this.requestcode = taxesInterfaceCodeEnum.getInterfaceCode();
    }
}
