package com.vedeng.erp.kingdee.batch.processor;

import com.vedeng.erp.kingdee.batch.dto.BatchMaterialFinanceDto;
import com.vedeng.erp.kingdee.domain.entity.KingDeeMaterialEntity;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialSubHeadEntityDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeMaterialMapper;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/5 10:38
 */
@Service
@Slf4j
public class BatchMaterialProcessor implements ItemProcessor<BatchMaterialFinanceDto, KingDeeMaterialDto> {

    @Autowired
    private KingDeeMaterialMapper kingDeeMaterialMapper;

    @Override
    public KingDeeMaterialDto process(BatchMaterialFinanceDto batchMaterialFinanceDto) throws Exception {
        KingDeeMaterialEntity kingDeeMaterialEntity = kingDeeMaterialMapper.getByfNumber(batchMaterialFinanceDto.getSkuNo());
        if (kingDeeMaterialEntity == null) {
            log.error("审计商品信息未推送过金蝶，无法更新，sku信息：{}", batchMaterialFinanceDto.getSkuNo());
            return null;
        }

        KingDeeMaterialDto kingDeeMaterialDto = new KingDeeMaterialDto(KingDeeBizEnums.updateMaterial);
        kingDeeMaterialDto.setKingDeeMaterialEntityId(kingDeeMaterialEntity.getKingDeeMaterialEntityId());
        kingDeeMaterialDto.setGoodsFinanceId(batchMaterialFinanceDto.getGoodsFinanceId());
        kingDeeMaterialDto.setFmaterialid(kingDeeMaterialEntity.getFmaterialid());
        kingDeeMaterialDto.setFNumber(batchMaterialFinanceDto.getSkuNo());
        kingDeeMaterialDto.setFName(batchMaterialFinanceDto.getSkuName());
        kingDeeMaterialDto.setFQzokYlqxtext(batchMaterialFinanceDto.getIsMedicalEquipmentText());
        kingDeeMaterialDto.setFQzokYlqxxfltext(batchMaterialFinanceDto.getMedicalEquipmentType());
        kingDeeMaterialDto.setFQzokYlqxyttext(batchMaterialFinanceDto.getMedicalEquipmentUse());
        kingDeeMaterialDto.setFQzokYlqxcxtext(batchMaterialFinanceDto.getMedicalEquipmentLine());
        if (batchMaterialFinanceDto.getIsInstallation() != null) {
            kingDeeMaterialDto.setFQzokAtfl(batchMaterialFinanceDto.getIsInstallation() == 0 ? "否" : "是");
        }

        KingDeeMaterialSubHeadEntityDto subHeadEntity = new KingDeeMaterialSubHeadEntityDto();
        subHeadEntity.setFErpClsID("1");
        switch (batchMaterialFinanceDto.getSpuType()) {
            case 318:
                subHeadEntity.setFCategoryID("CHLB01_SYS");
                break;
            case 317:
            case 653:
                subHeadEntity.setFCategoryID("CHLB02_SYS");
                break;
            case 316:
                subHeadEntity.setFCategoryID("CHLB03_SYS");
                break;
            case 319:
                subHeadEntity.setFCategoryID("");
                break;
            case 4282:
                subHeadEntity.setFCategoryID("CHLB06_SYS");
                break;
            case 4283:
                subHeadEntity.setFCategoryID("CHLB07_SYS");
                break;
            case 1008:
                subHeadEntity.setFCategoryID("CHLB08_SYS");
                break;
            default:
        }
        subHeadEntity.setFBaseUnitId(batchMaterialFinanceDto.getUnitKingDeeNo());
        kingDeeMaterialDto.setSubHeadEntity(subHeadEntity);
        return kingDeeMaterialDto;
    }
}