package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.model.User;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.dto.RiskTraderTodoDto;
import com.vedeng.todolist.dto.SupplyChainStaffTodoDtoBuild;
import com.vedeng.todolist.model.TodoList;
import com.wms.service.context.ThreadLocalContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class RiskTraderTodoStaffVisitor implements TodoVisitor<SupplyChainStaffTodoDtoBuild>{

    private static final DecimalFormat df = new DecimalFormat("0.0");

    @Resource
    private TodoListMapper todoListMapper;

    @Override
    public void visitor(SupplyChainStaffTodoDtoBuild supplyChainStaffTodoDtoBuild) {

        User user = ThreadLocalContext.get("userInfo");

        List<TodoList> traderSupplyTodoList = todoListMapper.getUnHandledRiskCheckTraderSupplyTodoListByUserList(
                                            TodoListBuzSceneEnum.RISK_CHECK_TRADER_SUPPLY_DATA.getBuzSceneId(), Arrays.asList(user.getUserId()));

        if(CollectionUtils.isEmpty(traderSupplyTodoList)){
            return;
        }

        RiskTraderTodoDto traderTodo = new RiskTraderTodoDto();

        traderTodo.setTraderSupplyTodoListCount(traderSupplyTodoList.parallelStream().map(TodoList::getBuzId).collect(Collectors.toSet()).size());

        Integer traderSupplyTodoListGroupByOrder = traderSupplyTodoList.parallelStream().map(TodoList::getBuzExtra).filter(StringUtils::isNotBlank).collect(Collectors.toSet()).size();
        traderTodo.setTraderSupplyTodoListCountGroupByOrder(traderSupplyTodoListGroupByOrder);

        //设置处理时间
        traderSupplyTodoList.stream().forEach(todo-> {
                                                todo.setProcessTime(todo.getStatus() == 1 ? todo.getUpdateTime() - todo.getAddTime() : System.currentTimeMillis() - todo.getAddTime());
                                             });

        //总时间
        Long totalProcessTime = traderSupplyTodoList.stream().mapToLong(TodoList::getProcessTime).sum();

        //总数量
        int totalOrderSum = traderSupplyTodoList.size();

        String avgTime = new BigDecimal(totalProcessTime / (totalOrderSum * 1000 * 60 * 60.0)).setScale(1, BigDecimal.ROUND_HALF_UP).toPlainString();

        String maxTime = traderSupplyTodoList.stream().map(TodoList::getProcessTime)
                                                 .max(Long::compareTo)
                                                 .map(p-> new BigDecimal(p /(1000 * 60 * 60.0)).setScale(1, BigDecimal.ROUND_HALF_UP).toPlainString())
                                                 .get();

        traderTodo.setAvgWaitTime(avgTime);
        traderTodo.setMaxWaitTime(maxTime);

        supplyChainStaffTodoDtoBuild.get().setRiskTraderTodoDto(traderTodo);
    }
}
