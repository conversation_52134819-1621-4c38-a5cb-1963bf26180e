package com.vedeng.common.core.enums;

import com.vedeng.common.core.exception.ServiceException;

import java.util.Arrays;

/**
 * 业务类型
 *
 * <AUTHOR>
 */
public enum SupplierAssetEnum {

    /**
     * 返利
     */
    rebate(1, "返利"),
    /**
     * 余额
     */
    balance(2, "余额"),

    /**
     * 银行
     */
    bank(3, "银行"),

    /**
     * 信用
     */
    credit(4, "信用额度");


    private final Integer code;

    private final String type;

    SupplierAssetEnum(Integer code, String type) {
        this.code = code;
        this.type = type;
    }

    public Integer getCode() {
        return code;
    }

    public String getType() {
        return type;
    }


    /**
     * 获取资产业务枚举
     *
     * @param code 业务类型
     * @return KingDeeBizEnums
     */
    public static SupplierAssetEnum getEnum(Integer code) {
        return Arrays.stream(SupplierAssetEnum.values())
                .filter(enums -> enums.getCode().equals(code))
                .findFirst().orElseThrow(() -> new ServiceException("无法获取对应的资产"));
    }

}
