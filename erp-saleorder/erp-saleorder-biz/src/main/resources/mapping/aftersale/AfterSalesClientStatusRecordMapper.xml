<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.aftersale.mapper.AfterSalesClientStatusRecordMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.AfterSalesClientStatusRecord">
    <!--@mbg.generated-->
    <!--@Table T_AFTER_SALES_CLIENT_STATUS_RECORD-->
    <id column="CLIENT_STATUS_RECORD_ID" jdbcType="BIGINT" property="clientStatusRecordId" />
    <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
    <result column="PUSH_CLIENT_STATUS" jdbcType="TINYINT" property="pushClientStatus" />
    <result column="PUSH_TIME" jdbcType="TIMESTAMP" property="pushTime" />
    <result column="MESSAGE" jdbcType="VARCHAR" property="message" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CLIENT_STATUS_RECORD_ID, AFTER_SALES_ID, PUSH_CLIENT_STATUS, PUSH_TIME, MESSAGE, 
    IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_CLIENT_STATUS_RECORD
    where CLIENT_STATUS_RECORD_ID = #{clientStatusRecordId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_AFTER_SALES_CLIENT_STATUS_RECORD
    where CLIENT_STATUS_RECORD_ID = #{clientStatusRecordId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="CLIENT_STATUS_RECORD_ID" keyProperty="clientStatusRecordId" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesClientStatusRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_CLIENT_STATUS_RECORD (AFTER_SALES_ID, PUSH_CLIENT_STATUS, PUSH_TIME, 
      MESSAGE, IS_DELETE, ADD_TIME, 
      MOD_TIME, CREATOR, CREATOR_NAME, 
      UPDATER, UPDATER_NAME, UPDATE_REMARK
      )
    values (#{afterSalesId,jdbcType=INTEGER}, #{pushClientStatus,jdbcType=TINYINT}, #{pushTime,jdbcType=TIMESTAMP},
      #{message,jdbcType=VARCHAR}, #{isDelete,jdbcType=TINYINT}, #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="CLIENT_STATUS_RECORD_ID" keyProperty="clientStatusRecordId" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesClientStatusRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_CLIENT_STATUS_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="pushClientStatus != null">
        PUSH_CLIENT_STATUS,
      </if>
      <if test="pushTime != null">
        PUSH_TIME,
      </if>
      <if test="message != null">
        MESSAGE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="pushClientStatus != null">
        #{pushClientStatus,jdbcType=TINYINT},
      </if>
      <if test="pushTime != null">
        #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesClientStatusRecord">
    <!--@mbg.generated-->
    update T_AFTER_SALES_CLIENT_STATUS_RECORD
    <set>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="pushClientStatus != null">
        PUSH_CLIENT_STATUS = #{pushClientStatus,jdbcType=TINYINT},
      </if>
      <if test="pushTime != null">
        PUSH_TIME = #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="message != null">
        MESSAGE = #{message,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where CLIENT_STATUS_RECORD_ID = #{clientStatusRecordId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesClientStatusRecord">
    <!--@mbg.generated-->
    update T_AFTER_SALES_CLIENT_STATUS_RECORD
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      PUSH_CLIENT_STATUS = #{pushClientStatus,jdbcType=TINYINT},
      PUSH_TIME = #{pushTime,jdbcType=TIMESTAMP},
      MESSAGE = #{message,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where CLIENT_STATUS_RECORD_ID = #{clientStatusRecordId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_AFTER_SALES_CLIENT_STATUS_RECORD
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="AFTER_SALES_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.afterSalesId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PUSH_CLIENT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.pushClientStatus,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="PUSH_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.pushTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MESSAGE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.message,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where CLIENT_STATUS_RECORD_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.clientStatusRecordId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_AFTER_SALES_CLIENT_STATUS_RECORD
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="AFTER_SALES_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterSalesId != null">
            when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.afterSalesId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PUSH_CLIENT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pushClientStatus != null">
            when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.pushClientStatus,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="PUSH_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pushTime != null">
            when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.pushTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MESSAGE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.message != null">
            when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.message,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateRemark != null">
            when CLIENT_STATUS_RECORD_ID = #{item.clientStatusRecordId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where CLIENT_STATUS_RECORD_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.clientStatusRecordId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="CLIENT_STATUS_RECORD_ID" keyProperty="clientStatusRecordId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_CLIENT_STATUS_RECORD
    (AFTER_SALES_ID, PUSH_CLIENT_STATUS, PUSH_TIME, MESSAGE, IS_DELETE, ADD_TIME, MOD_TIME, 
      CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.afterSalesId,jdbcType=INTEGER}, #{item.pushClientStatus,jdbcType=TINYINT},
        #{item.pushTime,jdbcType=TIMESTAMP}, #{item.message,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=TINYINT},
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.creatorName,jdbcType=VARCHAR}, #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.updateRemark,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="CLIENT_STATUS_RECORD_ID" keyProperty="clientStatusRecordId" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesClientStatusRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_CLIENT_STATUS_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="clientStatusRecordId != null">
        CLIENT_STATUS_RECORD_ID,
      </if>
      AFTER_SALES_ID,
      PUSH_CLIENT_STATUS,
      PUSH_TIME,
      MESSAGE,
      IS_DELETE,
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      CREATOR_NAME,
      UPDATER,
      UPDATER_NAME,
      UPDATE_REMARK,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="clientStatusRecordId != null">
        #{clientStatusRecordId,jdbcType=BIGINT},
      </if>
      #{afterSalesId,jdbcType=INTEGER},
      #{pushClientStatus,jdbcType=TINYINT},
      #{pushTime,jdbcType=TIMESTAMP},
      #{message,jdbcType=VARCHAR},
      #{isDelete,jdbcType=TINYINT},
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updater,jdbcType=INTEGER},
      #{updaterName,jdbcType=VARCHAR},
      #{updateRemark,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="clientStatusRecordId != null">
        CLIENT_STATUS_RECORD_ID = #{clientStatusRecordId,jdbcType=BIGINT},
      </if>
      AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      PUSH_CLIENT_STATUS = #{pushClientStatus,jdbcType=TINYINT},
      PUSH_TIME = #{pushTime,jdbcType=TIMESTAMP},
      MESSAGE = #{message,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="CLIENT_STATUS_RECORD_ID" keyProperty="clientStatusRecordId" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesClientStatusRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_CLIENT_STATUS_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="clientStatusRecordId != null">
        CLIENT_STATUS_RECORD_ID,
      </if>
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="pushClientStatus != null">
        PUSH_CLIENT_STATUS,
      </if>
      <if test="pushTime != null">
        PUSH_TIME,
      </if>
      <if test="message != null">
        MESSAGE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="clientStatusRecordId != null">
        #{clientStatusRecordId,jdbcType=BIGINT},
      </if>
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="pushClientStatus != null">
        #{pushClientStatus,jdbcType=TINYINT},
      </if>
      <if test="pushTime != null">
        #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="clientStatusRecordId != null">
        CLIENT_STATUS_RECORD_ID = #{clientStatusRecordId,jdbcType=BIGINT},
      </if>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="pushClientStatus != null">
        PUSH_CLIENT_STATUS = #{pushClientStatus,jdbcType=TINYINT},
      </if>
      <if test="pushTime != null">
        PUSH_TIME = #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="message != null">
        MESSAGE = #{message,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2024-04-10-->
  <select id="findByAfterSalesId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_AFTER_SALES_CLIENT_STATUS_RECORD
    where AFTER_SALES_ID=#{afterSalesId,jdbcType=INTEGER}
    and IS_DELETE=0
  </select>
</mapper>