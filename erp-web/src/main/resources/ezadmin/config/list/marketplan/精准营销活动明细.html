<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>精准营销活动明细</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="marketplandetaillist" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
<div id="appendHead">

</div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
            <!--        <li class="layui-this"><a href="/ezadmin/list/list-R8XBgDSwsUc">订单列表</a></li>-->
            <!--        <li><a href="/ezadmin/list/list-Jb8cQU_twaQ">售后列表</a></li>-->

        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline "><label class="layui-form-label">plan_id</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="PLAN_ID" placeholder style alias="tmpt" jdbctype data datatype oper validate_rules validate_messages> </object>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">

            <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:right" head="18" >公司名称</th>
            <th item_name="PROMOTION_PRIORITY" name="PROMOTION_PRIORITY" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:right" head="18" >推广优先级</th>
            <th item_name="LAST_COMM_TIME" name="LAST_COMM_TIME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:right" head="18" >最近一次沟通时间</th>
            <th item_name="LAST_COMM_CONTENT" name="LAST_COMM_CONTENT" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">最近沟通内容</th>
            <th item_name="TODO" name="TODO" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">同期待办</th>
            <th item_name="PROMOTION_CHANNELS" name="PROMOTION_CHANNELS" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">safd</th>
            <th item_name="SEND_MSG" name="SEND_MSG" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">是否触达</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by tmpt.ID DESC" groupby="">StringBuilder sql=new StringBuilder("select ");

	sql.append("
	tmpt.ID,tt.TRADER_NAME,tmpt.PROMOTION_PRIORITY,'最近一次沟通时间' AS LAST_COMM_TIME,'最近沟通内容' AS LAST_COMM_CONTENT,'同期待办' AS TODO ,tmpt.SEND_MSG   FROM

	  T_MARKETING_PLAN_TRADER tmpt
	  JOIN T_TRADER_CUSTOMER ttc  ON tmpt.TRADER_ID =ttc.TRADER_ID     and   tmpt.TRADER_CUSTOMER_ID = ttc.TRADER_CUSTOMER_ID
	  JOIN T_TRADER tt  ON tt.TRADER_ID =ttc.TRADER_ID
	JOIN T_R_TRADER_J_USER tu ON tmpt.TRADER_ID=tu.TRADER_ID  AND tu.USER_ID=");
                sql.append($$("EZ_SESSION_USER_ID_KEY") );
                sql.append(" where 1=1 " );

list=search(sql);
return list;</pre> count:
                <pre id="count" class="layui-code">

                </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">

    <script>

        $(function(){

            // $("td[item_name=DELIVERY_STATUS]").each(function(e){
            //     if($(this).text().indexOf("未")>=0){ $(this).css("color","red")}
            // })
            // $("td[item_name=INVOICE_STATUS]").each(function(e){
            //     if($(this).text().indexOf("未")>=0){ $(this).css("color","red")}
            // })
            //
            // $("td[item_name=DELIVERY_DIRECT]").each(function(e){
            //     if($(this).text().indexOf("是")>=0){ $(this).css("color","red")}
            // })

        })


    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>