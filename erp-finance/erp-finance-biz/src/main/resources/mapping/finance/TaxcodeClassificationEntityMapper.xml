<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.TaxcodeClassificationEntityMapper">
  <sql id="Base_Column_List">
    TAXCODE_CLASSIFICATION_ID,
    SUMMARY_CODE,
    GOODS_SERVICES_CLASSIFICATION_ABBREVIATION,
    GOODS_SERVICES_NAME_ABBREVIATION,
    FINAL_CODE,
    GOODS_SERVICES_NAME,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_ABBREVIATION,
    DESCRIPTION,
    KEYWORD,
    VATRATE,
    VAT_SPECIAL_MANAGEMENT,
    VAT_POLICY_BASIS,
    VAT_SPECIAL_CONTENT_CODE,
    CONSUMPTION_TAX_MANAGEMENT,
    CONSUMPTION_TAX_POLICY_BASIS,
    CONSUMPTION_TAX_SPECIAL_CONTENT_CODE,
    IS_SUMMARY_ITEM,
    NATIONAL_INDUSTRY_CODE,
    EXPORT_COMMODITY_ITEM,
    START_DATE,
    END_DATE,
    IS_COMMON,
    IS_DELETE,
    ADD_TIME,
    MOD_TIME,
    CREATOR,
    CREATOR_NAME,
    UPDATER,
    UPDATER_NAME,
    UPDATE_REMARK
  </sql>
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.TaxcodeClassificationEntity">

    <id column="TAXCODE_CLASSIFICATION_ID" jdbcType="INTEGER" property="taxcodeClassificationId" />
    <result column="SUMMARY_CODE" jdbcType="VARCHAR" property="summaryCode" />
    <result column="GOODS_SERVICES_CLASSIFICATION_ABBREVIATION" jdbcType="VARCHAR" property="goodsServicesClassificationAbbreviation" />
    <result column="GOODS_SERVICES_NAME_ABBREVIATION" jdbcType="VARCHAR" property="goodsServicesNameAbbreviation" />
    <result column="FINAL_CODE" jdbcType="VARCHAR" property="finalCode" />
    <result column="GOODS_SERVICES_NAME" jdbcType="VARCHAR" property="goodsServicesName" />
    <result column="CLASSIFICATION_ABBREVIATION" jdbcType="VARCHAR" property="classificationAbbreviation" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="KEYWORD" jdbcType="VARCHAR" property="keyword" />
    <result column="VATRATE" jdbcType="VARCHAR" property="vatrate" />
    <result column="VAT_SPECIAL_MANAGEMENT" jdbcType="VARCHAR" property="vatSpecialManagement" />
    <result column="VAT_POLICY_BASIS" jdbcType="VARCHAR" property="vatPolicyBasis" />
    <result column="VAT_SPECIAL_CONTENT_CODE" jdbcType="VARCHAR" property="vatSpecialContentCode" />
    <result column="CONSUMPTION_TAX_MANAGEMENT" jdbcType="VARCHAR" property="consumptionTaxManagement" />
    <result column="CONSUMPTION_TAX_POLICY_BASIS" jdbcType="VARCHAR" property="consumptionTaxPolicyBasis" />
    <result column="CONSUMPTION_TAX_SPECIAL_CONTENT_CODE" jdbcType="VARCHAR" property="consumptionTaxSpecialContentCode" />
    <result column="IS_SUMMARY_ITEM" jdbcType="VARCHAR" property="isSummaryItem" />
    <result column="NATIONAL_INDUSTRY_CODE" jdbcType="VARCHAR" property="nationalIndustryCode" />
    <result column="EXPORT_COMMODITY_ITEM" jdbcType="VARCHAR" property="exportCommodityItem" />
    <result column="START_DATE" jdbcType="DATE" property="startDate" />
    <result column="END_DATE" jdbcType="DATE" property="endDate" />
    <result column="IS_COMMON" jdbcType="VARCHAR" property="isCommon" />
    <result column="IS_DELETE" jdbcType="BIT" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">

    delete from T_TAXCODE_CLASSIFICATION
    where TAXCODE_CLASSIFICATION_ID = #{taxcodeClassificationId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.finance.domain.entity.TaxcodeClassificationEntity">

    insert into T_TAXCODE_CLASSIFICATION (TAXCODE_CLASSIFICATION_ID, SUMMARY_CODE, 
      GOODS_SERVICES_CLASSIFICATION_ABBREVIATION, GOODS_SERVICES_NAME_ABBREVIATION, 
      FINAL_CODE, GOODS_SERVICES_NAME, CLASSIFICATION_ABBREVIATION, 
      DESCRIPTION, KEYWORD, VATRATE, 
      VAT_SPECIAL_MANAGEMENT, VAT_POLICY_BASIS, VAT_SPECIAL_CONTENT_CODE, 
      CONSUMPTION_TAX_MANAGEMENT, CONSUMPTION_TAX_POLICY_BASIS, 
      CONSUMPTION_TAX_SPECIAL_CONTENT_CODE, IS_SUMMARY_ITEM, 
      NATIONAL_INDUSTRY_CODE, EXPORT_COMMODITY_ITEM, 
      START_DATE, END_DATE, IS_COMMON, 
      IS_DELETE, ADD_TIME, MOD_TIME, 
      CREATOR, CREATOR_NAME, UPDATER, 
      UPDATER_NAME, UPDATE_REMARK)
    values (#{taxcodeClassificationId,jdbcType=INTEGER}, #{summaryCode,jdbcType=VARCHAR}, 
      #{goodsServicesClassificationAbbreviation,jdbcType=VARCHAR}, #{goodsServicesNameAbbreviation,jdbcType=VARCHAR}, 
      #{finalCode,jdbcType=VARCHAR}, #{goodsServicesName,jdbcType=VARCHAR}, #{classificationAbbreviation,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{keyword,jdbcType=VARCHAR}, #{vatrate,jdbcType=VARCHAR}, 
      #{vatSpecialManagement,jdbcType=VARCHAR}, #{vatPolicyBasis,jdbcType=VARCHAR}, #{vatSpecialContentCode,jdbcType=VARCHAR}, 
      #{consumptionTaxManagement,jdbcType=VARCHAR}, #{consumptionTaxPolicyBasis,jdbcType=VARCHAR}, 
      #{consumptionTaxSpecialContentCode,jdbcType=VARCHAR}, #{isSummaryItem,jdbcType=VARCHAR}, 
      #{nationalIndustryCode,jdbcType=VARCHAR}, #{exportCommodityItem,jdbcType=VARCHAR}, 
      #{startDate,jdbcType=DATE}, #{endDate,jdbcType=DATE}, #{isCommon,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=BIT}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updater,jdbcType=INTEGER}, 
      #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.TaxcodeClassificationEntity">

    update T_TAXCODE_CLASSIFICATION
    set SUMMARY_CODE = #{summaryCode,jdbcType=VARCHAR},
      GOODS_SERVICES_CLASSIFICATION_ABBREVIATION = #{goodsServicesClassificationAbbreviation,jdbcType=VARCHAR},
      GOODS_SERVICES_NAME_ABBREVIATION = #{goodsServicesNameAbbreviation,jdbcType=VARCHAR},
      FINAL_CODE = #{finalCode,jdbcType=VARCHAR},
      GOODS_SERVICES_NAME = #{goodsServicesName,jdbcType=VARCHAR},
      CLASSIFICATION_ABBREVIATION = #{classificationAbbreviation,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=VARCHAR},
      KEYWORD = #{keyword,jdbcType=VARCHAR},
      VATRATE = #{vatrate,jdbcType=VARCHAR},
      VAT_SPECIAL_MANAGEMENT = #{vatSpecialManagement,jdbcType=VARCHAR},
      VAT_POLICY_BASIS = #{vatPolicyBasis,jdbcType=VARCHAR},
      VAT_SPECIAL_CONTENT_CODE = #{vatSpecialContentCode,jdbcType=VARCHAR},
      CONSUMPTION_TAX_MANAGEMENT = #{consumptionTaxManagement,jdbcType=VARCHAR},
      CONSUMPTION_TAX_POLICY_BASIS = #{consumptionTaxPolicyBasis,jdbcType=VARCHAR},
      CONSUMPTION_TAX_SPECIAL_CONTENT_CODE = #{consumptionTaxSpecialContentCode,jdbcType=VARCHAR},
      IS_SUMMARY_ITEM = #{isSummaryItem,jdbcType=VARCHAR},
      NATIONAL_INDUSTRY_CODE = #{nationalIndustryCode,jdbcType=VARCHAR},
      EXPORT_COMMODITY_ITEM = #{exportCommodityItem,jdbcType=VARCHAR},
      START_DATE = #{startDate,jdbcType=DATE},
      END_DATE = #{endDate,jdbcType=DATE},
      IS_COMMON = #{isCommon,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where TAXCODE_CLASSIFICATION_ID = #{taxcodeClassificationId,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">

    select TAXCODE_CLASSIFICATION_ID, SUMMARY_CODE, GOODS_SERVICES_CLASSIFICATION_ABBREVIATION, 
    GOODS_SERVICES_NAME_ABBREVIATION, FINAL_CODE, GOODS_SERVICES_NAME, CLASSIFICATION_ABBREVIATION, 
    DESCRIPTION, KEYWORD, VATRATE, VAT_SPECIAL_MANAGEMENT, VAT_POLICY_BASIS, VAT_SPECIAL_CONTENT_CODE, 
    CONSUMPTION_TAX_MANAGEMENT, CONSUMPTION_TAX_POLICY_BASIS, CONSUMPTION_TAX_SPECIAL_CONTENT_CODE, 
    IS_SUMMARY_ITEM, NATIONAL_INDUSTRY_CODE, EXPORT_COMMODITY_ITEM, START_DATE, END_DATE, 
    IS_COMMON, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, 
    UPDATE_REMARK
    from T_TAXCODE_CLASSIFICATION
    where TAXCODE_CLASSIFICATION_ID = #{taxcodeClassificationId,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">

    select TAXCODE_CLASSIFICATION_ID, SUMMARY_CODE, GOODS_SERVICES_CLASSIFICATION_ABBREVIATION, 
    GOODS_SERVICES_NAME_ABBREVIATION, FINAL_CODE, GOODS_SERVICES_NAME, CLASSIFICATION_ABBREVIATION, 
    DESCRIPTION, KEYWORD, VATRATE, VAT_SPECIAL_MANAGEMENT, VAT_POLICY_BASIS, VAT_SPECIAL_CONTENT_CODE, 
    CONSUMPTION_TAX_MANAGEMENT, CONSUMPTION_TAX_POLICY_BASIS, CONSUMPTION_TAX_SPECIAL_CONTENT_CODE, 
    IS_SUMMARY_ITEM, NATIONAL_INDUSTRY_CODE, EXPORT_COMMODITY_ITEM, START_DATE, END_DATE, 
    IS_COMMON, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, 
    UPDATE_REMARK
    from T_TAXCODE_CLASSIFICATION
  </select>

  <select id="findByAll" resultType="com.vedeng.erp.finance.dto.TaxcodeClassificationDto">
    select TAXCODE_CLASSIFICATION_ID, SUMMARY_CODE, GOODS_SERVICES_CLASSIFICATION_ABBREVIATION,
    GOODS_SERVICES_NAME_ABBREVIATION, FINAL_CODE, GOODS_SERVICES_NAME, CLASSIFICATION_ABBREVIATION,
    DESCRIPTION, KEYWORD, VATRATE, VAT_SPECIAL_MANAGEMENT, VAT_POLICY_BASIS, VAT_SPECIAL_CONTENT_CODE,
    CONSUMPTION_TAX_MANAGEMENT, CONSUMPTION_TAX_POLICY_BASIS, CONSUMPTION_TAX_SPECIAL_CONTENT_CODE,
    IS_SUMMARY_ITEM, NATIONAL_INDUSTRY_CODE, EXPORT_COMMODITY_ITEM, START_DATE, END_DATE,
    if(IS_COMMON = 'Y', '常用', '不常用') as IS_COMMON,
    IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME,UPDATE_REMARK
    from T_TAXCODE_CLASSIFICATION
    where
      1=1
    and IS_SUMMARY_ITEM != 'Y'
    <if test="finalCode != null and finalCode != ''">
            and FINAL_CODE LIKE CONCAT('%', #{finalCode,jdbcType=VARCHAR}, '%')
    </if>
    <if test="goodsServicesName != null and goodsServicesName != ''">
            and GOODS_SERVICES_NAME LIKE CONCAT('%', #{goodsServicesName,jdbcType=VARCHAR}, '%')
    </if>
    <if test="classificationAbbreviation != null and classificationAbbreviation != ''">
            and CLASSIFICATION_ABBREVIATION LIKE CONCAT('%', #{classificationAbbreviation,jdbcType=VARCHAR}, '%')
    </if>
    <if test="description != null and description != ''">
            and DESCRIPTION LIKE CONCAT('%', #{description,jdbcType=VARCHAR}, '%')
    </if>
    <if test="goodsServicesNameAbbreviation != null and goodsServicesNameAbbreviation != ''">
            and GOODS_SERVICES_NAME_ABBREVIATION LIKE CONCAT('%', #{goodsServicesNameAbbreviation,jdbcType=VARCHAR}, '%')
    </if>
    <if test="goodsServicesClassificationAbbreviation != null and goodsServicesClassificationAbbreviation != ''">
            and GOODS_SERVICES_CLASSIFICATION_ABBREVIATION LIKE CONCAT('%', #{goodsServicesClassificationAbbreviation,jdbcType=VARCHAR}, '%')
    </if>
    <if test="keyword != null and keyword != ''">
            and KEYWORD LIKE CONCAT('%', #{keyword,jdbcType=VARCHAR}, '%')
    </if>
    <if test="isCommon != null and isCommon != ''">
            and IS_COMMON = #{isCommon,jdbcType=VARCHAR}
    </if>
  </select>

<!--auto generated by MybatisCodeHelper on 2023-11-03-->
  <select id="findByFinalCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_TAXCODE_CLASSIFICATION
    where FINAL_CODE=#{finalCode,jdbcType=VARCHAR}
  </select>

  <select id="findBySkuId" resultType="com.vedeng.erp.finance.dto.TaxcodeClassificationDto">
    select
    a.*
    from  V_CORE_SKU b left join T_TAXCODE_CLASSIFICATION a on a.FINAL_CODE = b.TAX_CATEGORY_NO
    where b.SKU_ID=#{skuId,jdbcType=INTEGER}
  </select>
</mapper>