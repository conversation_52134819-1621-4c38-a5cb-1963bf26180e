package com.newtask.saleorder;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.sql.DataSource;
import java.sql.Date;
import java.sql.*;
import java.util.*;

/**
 * 转移代码，请勿复用
 */
@Deprecated
@Slf4j
public class Dao {
    private static Dao dao = new Dao();

    private Dao() {

    }

    public static Dao getInstance() {
        return dao;
    }


    public Map<String, Object> executeQueryOne(DataSource dataSource, String sql, Object[] bindArgs) throws SQLException {
        List<Map<String, Object>> list = executeQuery(dataSource, sql, bindArgs);
        return list.size() > 0 ? list.get(0) : Collections.emptyMap();
    }

    public List<Map<String, Object>> executeQuery(DataSource dataSource, String sql, Object[] bindArgs) throws SQLException {
        List<Map<String, Object>> datas = new ArrayList<>(0);
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        try {
            connection = dataSource.getConnection();
            preparedStatement = connection.prepareStatement(sql);
            if (bindArgs != null) {
                for (int i = 0; i < bindArgs.length; i++) {
                    preparedStatement.setObject(i + 1, bindArgs[i]);
                }
            }
            resultSet = preparedStatement.executeQuery();
            datas = getDatas(resultSet);
        } catch (Exception e) {
            throw e;
        } finally {
            try {
                if (resultSet != null) {
                    resultSet.close();
                }
            } catch (Exception e3) {
            }
            try {
                if (preparedStatement != null) {
                    preparedStatement.close();
                }
            } catch (Exception e1) {
            }
            try {
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e2) {
            }
        }
        return datas;
    }

    public long executeUpdate(DataSource dataSource, String sql, Object[] bindArgs) throws SQLException {
        Connection connection = null;
        PreparedStatement preparedStatement = null;
        try {
            connection = dataSource.getConnection();
            preparedStatement = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            if (bindArgs != null) {
                for (int i = 0; i < bindArgs.length; i++) {
                    if (bindArgs[i] instanceof Date) {
                        preparedStatement.setTimestamp(i + 1, new Timestamp(((Date) bindArgs[i]).getTime()));
                    } else {
                        preparedStatement.setObject(i + 1, bindArgs[i]);
                    }
                }
            }
            preparedStatement.executeUpdate();
            ResultSet generatedKeys = preparedStatement.getGeneratedKeys();
            while (generatedKeys.next()) {
                long generateKey = generatedKeys.getLong(1);
                return generateKey;
            }
        } catch (Exception e) {
            throw e;
        } finally {
            try {
                if (preparedStatement != null) {
                    preparedStatement.close();
                }
            } catch (Exception e1) {
            }
            try {
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e2) {
            }
        }
        return 0;
    }

    /**
     * 将结果集对象封装成List<Map<String, Object>> 对象
     *
     * @param resultSet 结果多想
     * @return 结果的封装
     * @throws SQLException
     */
    private static List<Map<String, Object>> getDatas(ResultSet resultSet) throws SQLException {
        List<Map<String, Object>> datas = new ArrayList<>();
        /**获取结果集的数据结构对象**/
        ResultSetMetaData metaData = resultSet.getMetaData();
        while (resultSet.next()) {
            Map<String, Object> rowMap = new HashMap<>();
            for (int i = 1; i <= metaData.getColumnCount(); i++) {
                try {
                    Object objectValue = resultSet.getObject(i);
                    if (objectValue != null && objectValue instanceof Timestamp) {
                        objectValue = objectValue == null ? "" : objectValue.toString();
                    }
                    if (StringUtils.isNotBlank(metaData.getColumnLabel(i))) {
                        rowMap.put(StringUtils.upperCase(metaData.getColumnLabel(i)), objectValue);
                    } else {
                        rowMap.put(StringUtils.upperCase(metaData.getColumnName(i)), objectValue);
                    }
                } catch (Exception e) {
                    log.error("【getDatas】处理异常",e);
                    rowMap.put(StringUtils.upperCase(metaData.getColumnName(i)), "");
                }
            }
            datas.add(rowMap);
        }
        return datas;
    }

}
