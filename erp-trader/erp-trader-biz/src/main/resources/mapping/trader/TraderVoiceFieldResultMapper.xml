<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderVoiceFieldResultMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.VoiceFieldResultEntity">
    <!--@mbg.generated-->
    <!--@Table T_VOICE_FIELD_RESULT-->
    <id column="FIELD_RESULT_ID" jdbcType="INTEGER" property="fieldResultId" />
    <result column="COMMUNICATE_RECORD_ID" jdbcType="INTEGER" property="communicateRecordId" />
    <result column="SENCE_CODE" jdbcType="VARCHAR" property="senceCode" />
    <result column="GROUP_CODE" jdbcType="VARCHAR" property="groupCode" />
    <result column="FIELD_CODE" jdbcType="VARCHAR" property="fieldCode" />
    <result column="FIELD_NAME" jdbcType="VARCHAR" property="fieldName" />
    <result column="FIELD_RESULT" jdbcType="VARCHAR" property="fieldResult" />
    <result column="DO_FLAG" jdbcType="VARCHAR" property="doFlag" />
    <result column="FIELD_ORDER" jdbcType="INTEGER" property="fieldOrder" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="ADD_USER_ID" jdbcType="INTEGER" property="addUserId" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="UPDATE_USER_ID" jdbcType="INTEGER" property="updateUserId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    FIELD_RESULT_ID, COMMUNICATE_RECORD_ID, SENCE_CODE, GROUP_CODE, FIELD_CODE, FIELD_NAME, 
    FIELD_RESULT, DO_FLAG, FIELD_ORDER, ADD_TIME, ADD_USER_ID, UPDATE_TIME, UPDATE_USER_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_VOICE_FIELD_RESULT
    where FIELD_RESULT_ID = #{fieldResultId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_VOICE_FIELD_RESULT
    where FIELD_RESULT_ID = #{fieldResultId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="FIELD_RESULT_ID" keyProperty="fieldResultId" parameterType="com.vedeng.erp.trader.domain.entity.VoiceFieldResultEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_VOICE_FIELD_RESULT (COMMUNICATE_RECORD_ID, SENCE_CODE, GROUP_CODE, 
      FIELD_CODE, FIELD_NAME, FIELD_RESULT, 
      DO_FLAG, FIELD_ORDER, ADD_TIME, 
      ADD_USER_ID, UPDATE_TIME, UPDATE_USER_ID
      )
    values (#{communicateRecordId,jdbcType=INTEGER}, #{senceCode,jdbcType=VARCHAR}, #{groupCode,jdbcType=VARCHAR}, 
      #{fieldCode,jdbcType=VARCHAR}, #{fieldName,jdbcType=VARCHAR}, #{fieldResult,jdbcType=VARCHAR}, 
      #{doFlag,jdbcType=VARCHAR}, #{fieldOrder,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{addUserId,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="FIELD_RESULT_ID" keyProperty="fieldResultId" parameterType="com.vedeng.erp.trader.domain.entity.VoiceFieldResultEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_VOICE_FIELD_RESULT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="communicateRecordId != null">
        COMMUNICATE_RECORD_ID,
      </if>
      <if test="senceCode != null and senceCode != ''">
        SENCE_CODE,
      </if>
      <if test="groupCode != null and groupCode != ''">
        GROUP_CODE,
      </if>
      <if test="fieldCode != null and fieldCode != ''">
        FIELD_CODE,
      </if>
      <if test="fieldName != null and fieldName != ''">
        FIELD_NAME,
      </if>
      <if test="fieldResult != null and fieldResult != ''">
        FIELD_RESULT,
      </if>
      <if test="doFlag != null and doFlag != ''">
        DO_FLAG,
      </if>
      <if test="fieldOrder != null">
        FIELD_ORDER,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="addUserId != null">
        ADD_USER_ID,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updateUserId != null">
        UPDATE_USER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="communicateRecordId != null">
        #{communicateRecordId,jdbcType=INTEGER},
      </if>
      <if test="senceCode != null and senceCode != ''">
        #{senceCode,jdbcType=VARCHAR},
      </if>
      <if test="groupCode != null and groupCode != ''">
        #{groupCode,jdbcType=VARCHAR},
      </if>
      <if test="fieldCode != null and fieldCode != ''">
        #{fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null and fieldName != ''">
        #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldResult != null and fieldResult != ''">
        #{fieldResult,jdbcType=VARCHAR},
      </if>
      <if test="doFlag != null and doFlag != ''">
        #{doFlag,jdbcType=VARCHAR},
      </if>
      <if test="fieldOrder != null">
        #{fieldOrder,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addUserId != null">
        #{addUserId,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.VoiceFieldResultEntity">
    <!--@mbg.generated-->
    update T_VOICE_FIELD_RESULT
    <set>
      <if test="communicateRecordId != null">
        COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER},
      </if>
      <if test="senceCode != null and senceCode != ''">
        SENCE_CODE = #{senceCode,jdbcType=VARCHAR},
      </if>
      <if test="groupCode != null and groupCode != ''">
        GROUP_CODE = #{groupCode,jdbcType=VARCHAR},
      </if>
      <if test="fieldCode != null and fieldCode != ''">
        FIELD_CODE = #{fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="fieldName != null and fieldName != ''">
        FIELD_NAME = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="fieldResult != null and fieldResult != ''">
        FIELD_RESULT = #{fieldResult,jdbcType=VARCHAR},
      </if>
      <if test="doFlag != null and doFlag != ''">
        DO_FLAG = #{doFlag,jdbcType=VARCHAR},
      </if>
      <if test="fieldOrder != null">
        FIELD_ORDER = #{fieldOrder,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addUserId != null">
        ADD_USER_ID = #{addUserId,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        UPDATE_USER_ID = #{updateUserId,jdbcType=INTEGER},
      </if>
    </set>
    where FIELD_RESULT_ID = #{fieldResultId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.VoiceFieldResultEntity">
    <!--@mbg.generated-->
    update T_VOICE_FIELD_RESULT
    set COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER},
      SENCE_CODE = #{senceCode,jdbcType=VARCHAR},
      GROUP_CODE = #{groupCode,jdbcType=VARCHAR},
      FIELD_CODE = #{fieldCode,jdbcType=VARCHAR},
      FIELD_NAME = #{fieldName,jdbcType=VARCHAR},
      FIELD_RESULT = #{fieldResult,jdbcType=VARCHAR},
      DO_FLAG = #{doFlag,jdbcType=VARCHAR},
      FIELD_ORDER = #{fieldOrder,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      ADD_USER_ID = #{addUserId,jdbcType=INTEGER},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      UPDATE_USER_ID = #{updateUserId,jdbcType=INTEGER}
    where FIELD_RESULT_ID = #{fieldResultId,jdbcType=INTEGER}
  </update>

    <select id="selectByCommunicateRecordIdAndSence" resultType="com.vedeng.erp.trader.domain.entity.VoiceFieldResultEntity">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from T_VOICE_FIELD_RESULT
        where COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER}
      AND SENCE_CODE = #{senceCode,jdbcType=VARCHAR}
      ORDER BY FIELD_ORDER ASC
    </select>

  <select id="selectByCommunicateRecordIdAndSenceGroup" resultType="com.vedeng.erp.trader.domain.entity.VoiceFieldResultEntity">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_VOICE_FIELD_RESULT
    where COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER}
    AND SENCE_CODE = #{senceCode,jdbcType=VARCHAR}
    AND GROUP_CODE = #{groupCode,jdbcType=VARCHAR}
    ORDER BY FIELD_ORDER ASC
  </select>

<!--auto generated by MybatisCodeHelper on 2024-05-11-->

  <!--auto generated by MybatisCodeHelper on 2024-05-16-->
  <select id="selectByGroupCodeAndFieldCodeAndCommunicateRecordId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_VOICE_FIELD_RESULT
    where GROUP_CODE=#{groupCode,jdbcType=VARCHAR} and FIELD_CODE=#{fieldCode,jdbcType=VARCHAR} and
    COMMUNICATE_RECORD_ID=#{communicateRecordId,jdbcType=INTEGER}
  </select>

<!--auto generated by MybatisCodeHelper on 2024-05-17-->
  <select id="findByAddTimeBetween" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_VOICE_FIELD_RESULT
        where ADD_TIME <![CDATA[>]]> #{minAddTime,jdbcType=TIMESTAMP} and ADD_TIME <![CDATA[<]]>
        #{maxAddTime,jdbcType=TIMESTAMP}
    </select>

<!--auto generated by MybatisCodeHelper on 2024-05-20-->
  <select id="findByCommunicateRecordIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_VOICE_FIELD_RESULT
    where COMMUNICATE_RECORD_ID in
    <foreach item="item" index="index" collection="communicateRecordIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2024-05-20-->
  <select id="findByCommunicateRecordId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_VOICE_FIELD_RESULT
    where COMMUNICATE_RECORD_ID=#{communicateRecordId,jdbcType=INTEGER}
    order by FIELD_ORDER asc
  </select>
</mapper>