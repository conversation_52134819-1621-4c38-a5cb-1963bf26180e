//采购订单审核
function buyOrderAudit(){
	$(".bg-light-green").prop('disabled', true).text("提交中").css('background-color', 'gray');
	setTimeout(function() {
		$(".bg-light-green").prop('disabled', false).text("提交").css('background-color', '');
	}, 10000); // 10秒后启用按钮
	$.ajax({
		type: "POST",
		url: "./complementTaskForBuyOrder.do",
		data: $('#complement').serialize(),
		dataType:'json',
		success: function(data){
			if(data.code == 0){
				layer.close(index);
				window.parent.location.href = page_url + '/order/buyorder/viewBuyordersh.do?buyorderId='+data.data;
			}else{

				layer.confirm(data.message,{btn: ['确认']},function () {
					window.parent.location.reload();
				});

			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}

//其他类型的单据审核操作 比如付款申请 采购单修改申请
function otherOrderSubmit(){
	$(".bg-light-green").prop('disabled', true).text("提交中").css('background-color', 'gray');
	setTimeout(function() {
		$(".bg-light-green").prop('disabled', false).text("提交").css('background-color', '');
	}, 10000); // 10秒后启用按钮
    //订单审核
    $.ajax({
        type: "POST",
        url: "./complementTask.do",
        data: $('#complement').serialize(),
        dataType:'json',
        success: function(data){
            if(data.status == 1){
                layer.close(index);
                window.parent.location.href = page_url + '/order/buyorder/viewBuyordersh.do?buyorderId='+data.data.buyorderId
            }else{
				if($("#refreshParent") && $("#refreshParent").val() == 1){
					parent.vm.queryList();
					$("#close-layer").click();
				}else {
					parent.location.reload();
				}
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}

function complementTask(){
	checkLogin();
	var comment = $("input[name='comment']").val()
	var taskId = $("input[name='taskId']").val()
	var pass = $("input[name='pass']").val()
	var type = $("input[name='type']").val()
	var buyorderId = $("input[name='buyorderId']").val();


	var  payVedengBankId = $("select[name='payVedengBankId']").val();
	var isShow = $("#isShow").val();
	if(isShow == 1) {
		if ($("#havePayVedengBankId").val() == 1 && payVedengBankId == null) {
			warnTips("payVedengBankId", "请选择付款银行");
			return false;
		}
	}

	var illegalString = RegExp(/[(\ )(\~)(\!)(\@)(\#)(\$)(\%)(\^)(\&)(\*)(\()(\))(\-)(\_)(\+)(\=)(\[)(\])(\{)(\})(\|)(\\)(\;)(\:)(\')(\")(\,)(\.)(\/)  (\<)(\>)(\?)(\)]+/);
	if(illegalString.test(comment)){
		warnTips("comment","不可填写特殊字符");
		return false;
	}

	if(pass =="false" && comment == ""){
		warnTips("comment","请填写备注");
		return false;
	}
	if(comment.length > 100){
		warnTips("comment","备注内容不允许超过100个字符");
		return false;
	}

	//采购订单的操作
	if(type == 3){

		var purchasePriceChange = $("#purchasePriceChange").val();
		var tips = $("#tips").val();

		if(purchasePriceChange == 1){
			layer.confirm(tips, {
				btn: ['确定','取消']
			}, function(){
				buyOrderAudit();
			}, function(){
			});
			return;
		}

		buyOrderAudit();
    }else if(type == 1){
        otherOrderSubmit();
	}else if(type == 2){
		$(".bg-light-green").prop('disabled', true).text("提交中").css('background-color', 'gray');
		setTimeout(function() {
			$(".bg-light-green").prop('disabled', false).text("提交").css('background-color', '');
		}, 10000); // 10秒后启用按钮
		//售后审核
		$.ajax({
			type: "POST",
			url: "./complementAfterSaleTask.do",
			data: $('#complement').serialize(),
			dataType:'json',
			success: function(data){
				refreshPageList(data)
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});

	}



}
