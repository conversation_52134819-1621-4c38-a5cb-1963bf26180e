package com.vedeng.erp.trader.mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

import com.vedeng.erp.trader.domain.dto.PublicCustomerExemptUsersDto;

/**
 * ${end}
 * <AUTHOR>
 * @date 2022/4/29 15:05
 **/
public interface PublicCustomerExemptUsersMapper {
    int deleteByPrimaryKey(Integer publicCustomerExemptUsersId);

    int insert(PublicCustomerExemptUsersDto record);

    int insertSelective(PublicCustomerExemptUsersDto record);

    PublicCustomerExemptUsersDto selectByPrimaryKey(Integer publicCustomerExemptUsersId);

    int updateByPrimaryKeySelective(PublicCustomerExemptUsersDto record);

    int updateByPrimaryKey(PublicCustomerExemptUsersDto record);

	List<PublicCustomerExemptUsersDto> selectByDeletedlistPage(@Param("map") Map<String, Object> map);

	List<PublicCustomerExemptUsersDto> selectByUserId(@Param("userId")Integer userId);


}