package com.vedeng.erp.kingdee.batch.dto;

import java.math.BigDecimal;
import java.util.Date;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.*;

/**
    * 快递单
    */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchExpressDto {
    private Integer expressId;

    /**
    * 物流公司dbcenter 物流表
    */
    private Integer logisticsId;

    /**
    * 物流单号
    */
    private String logisticsNo;

    /**
    * ERP公司ID(T_COMPANY)
    */
    private Integer companyId;

    /**
    * 寄送时间
    */
    private Long deliveryTime;

    /**
    * 客户收货状态0未收货 1部分收货 2全部收货
    */
    private Integer arrivalStatus;

    /**
    * 客户收货时间
    */
    private Long arrivalTime;

    /**
    * 始发地地区选择最小级ID
    */
    private Integer deliveryFrom;

    /**
    * 物流备注
    */
    private String logisticsComments;

    /**
    * 是否有效 0否 1是
    */
    private Integer isEnable;

    /**
    * 付款方式1寄付月结 2寄付现结 3到付现结 4第三方付
    */
    private Integer paymentType;

    /**
    * 月结卡号
    */
    private String cardNumber;

    /**
    * 业务类型1顺丰特惠 2物流普运 3顺丰标快
    */
    private Integer businessType;

    /**
    * 实际重量
    */
    private BigDecimal realWeight;

    /**
    * 件数
    */
    private Integer num;

    /**
    * 计费重量
    */
    private BigDecimal amountWeight;

    /**
    * 托寄物
    */
    private String mailGoods;

    /**
    * 托寄物数量
    */
    private Integer mailGoodsNum;

    /**
    * 是否保价 0否 1是
    */
    private Integer isProtectPrice;

    /**
    * 保价金额
    */
    private BigDecimal protectPrice;

    /**
    * 是否签回单0否 1是
    */
    private Integer isReceipt;

    /**
    * 寄方备注（默认:节假日正常派送，提前联系）
    */
    private String mailCommtents;

    /**
    * 是否已发送短信0否1是
    */
    private Integer sentSms;

    /**
    * 是否票货同行0为不同行1为同行
    */
    private Integer travelingByTicket;

    /**
    * 是否开据发票0为无需开票1为待开票2为已开票
    */
    private Integer isInvoicing;

    /**
    * 包裹回传WMS订单号
    */
    private String wmsOrderNo;

    /**
    * 签收记录ID 在线签收表
    */
    private Integer onlineReceiptId;

    /**
    * 批次编号
    */
    private String batchNo;

    /**
    * 是否可收货（0否 1是）
    */
    private Integer enableReceive;

    /**
    * 系统添加时间-不会被修改
    */
    private Date systemAddTime;

    /**
    * (快递反哺) 原快递单号
    */
    private String oldLogisticsNo;

    private int _pagesize;

    private int _skiprows;


    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 修改时间
     */
    private Long modTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 修改者
     */
    private Integer updater;




    /**
     * @组合对象@
     */
    private Long beginTime;

    /**
     * @组合对象@
     */
    private Long endTime;
}