<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.trader.mapper.SkuSupplyAuthDetailMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.dto.SkuSupplyAuthDetail" >
    <!--          -->
    <id column="SKU_SUPPLY_AUTH_DETAIL_ID" property="skuSupplyAuthDetailId" jdbcType="INTEGER" />
    <result column="SKU_SUPPLY_AUTH_ID" property="skuSupplyAuthId" jdbcType="INTEGER" />
    <result column="SKU_ID" property="skuId" jdbcType="INTEGER" />
    <result column="SKU_NAME" property="skuName" jdbcType="VARCHAR" />
    <result column="SKU_NO" property="skuNo" jdbcType="VARCHAR" />
    <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
    <result column="MODEL" property="model" jdbcType="VARCHAR" />
    <result column="IS_DELETE" property="isDelete" jdbcType="TINYINT" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="TIMESTAMP" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    SKU_SUPPLY_AUTH_DETAIL_ID, SKU_SUPPLY_AUTH_ID, SKU_ID, SKU_NAME, SKU_NO, BRAND_NAME, 
    MODEL, IS_DELETE, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    from T_SKU_SUPPLY_AUTH_DETAIL
    where SKU_SUPPLY_AUTH_DETAIL_ID = #{skuSupplyAuthDetailId,jdbcType=INTEGER}
  </select>

  <select id="selectRelatedSkuByAuthId" resultType="com.vedeng.erp.trader.domain.dto.SkuSupplyAuthDetail">
    SELECT
      D.SKU_SUPPLY_AUTH_DETAIL_ID,
      D.SKU_NAME,
      D.SKU_NO,
      D.BRAND_NAME,
      D.SPEC,
      D.MODEL,
      D.SKU_ID,
      P.SPU_TYPE
    FROM
        T_SKU_SUPPLY_AUTH_DETAIL D
        LEFT JOIN V_CORE_SKU K ON D.SKU_ID = K.SKU_ID
        LEFT JOIN V_CORE_SPU P ON P.SPU_ID = K.SPU_ID
    WHERE
        D.SKU_SUPPLY_AUTH_ID = #{skuSupplyAuthId,jdbcType=INTEGER}
        AND D.IS_DELETE = 0
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from T_SKU_SUPPLY_AUTH_DETAIL
    where SKU_SUPPLY_AUTH_DETAIL_ID = #{skuSupplyAuthDetailId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.trader.domain.dto.SkuSupplyAuthDetail" >
    <!--          -->
    insert into T_SKU_SUPPLY_AUTH_DETAIL (SKU_SUPPLY_AUTH_DETAIL_ID, SKU_SUPPLY_AUTH_ID, 
      SKU_ID, SKU_NAME, SKU_NO, 
      BRAND_NAME, MODEL, IS_DELETE, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER)
    values (#{skuSupplyAuthDetailId,jdbcType=INTEGER}, #{skuSupplyAuthId,jdbcType=INTEGER}, 
      #{skuId,jdbcType=INTEGER}, #{skuName,jdbcType=VARCHAR}, #{skuNo,jdbcType=VARCHAR}, 
      #{brandName,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{isDelete,jdbcType=TINYINT}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.trader.domain.dto.SkuSupplyAuthDetail" >
    <!--          -->
    insert into T_SKU_SUPPLY_AUTH_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="skuSupplyAuthDetailId != null" >
        SKU_SUPPLY_AUTH_DETAIL_ID,
      </if>
      <if test="skuSupplyAuthId != null" >
        SKU_SUPPLY_AUTH_ID,
      </if>
      <if test="skuId != null" >
        SKU_ID,
      </if>
      <if test="skuName != null" >
        SKU_NAME,
      </if>
      <if test="skuNo != null" >
        SKU_NO,
      </if>
      <if test="brandName != null" >
        BRAND_NAME,
      </if>
      <if test="model != null" >
        MODEL,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="skuSupplyAuthDetailId != null" >
        #{skuSupplyAuthDetailId,jdbcType=INTEGER},
      </if>
      <if test="skuSupplyAuthId != null" >
        #{skuSupplyAuthId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null" >
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuName != null" >
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null" >
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null" >
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch">
    INSERT INTO T_SKU_SUPPLY_AUTH_DETAIL ( SKU_SUPPLY_AUTH_ID, SKU_ID, SKU_NAME, SKU_NO, BRAND_NAME, SPEC, MODEL, ADD_TIME, CREATOR )
    VALUES
    <foreach collection ="paramSkuList" item="sku" separator =",">
      (#{sku.skuSupplyAuthId,jdbcType=INTEGER}, #{sku.skuId,jdbcType=INTEGER}, #{sku.skuName,jdbcType=VARCHAR}, #{sku.skuNo,jdbcType=VARCHAR},
       #{sku.brandName,jdbcType=VARCHAR}, #{sku.spec,jdbcType=VARCHAR}, #{sku.model,jdbcType=VARCHAR}, #{sku.addTime,jdbcType=DATE}, #{sku.creator,jdbcType=INTEGER})
    </foreach >
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.dto.SkuSupplyAuthDetail" >
    <!--          -->
    update T_SKU_SUPPLY_AUTH_DETAIL
    <set >
      <if test="skuSupplyAuthId != null" >
        SKU_SUPPLY_AUTH_ID = #{skuSupplyAuthId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null" >
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuName != null" >
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null" >
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null" >
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where SKU_SUPPLY_AUTH_DETAIL_ID = #{skuSupplyAuthDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.dto.SkuSupplyAuthDetail" >
    <!--          -->
    update T_SKU_SUPPLY_AUTH_DETAIL
    set SKU_SUPPLY_AUTH_ID = #{skuSupplyAuthId,jdbcType=INTEGER},
      SKU_ID = #{skuId,jdbcType=INTEGER},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER}
    where SKU_SUPPLY_AUTH_DETAIL_ID = #{skuSupplyAuthDetailId,jdbcType=INTEGER}
  </update>
  <update id="delSkuSupplyAuthByAuthId" parameterType="com.vedeng.erp.trader.domain.dto.SkuSupplyAuthDetail">
    update
        T_SKU_SUPPLY_AUTH_DETAIL
    set
    <if test="modTime != null">
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    </if>
    <if test="updater != null">
      UPDATER = #{updater,jdbcType=INTEGER},
    </if>
    IS_DELETE = 1
    where SKU_SUPPLY_AUTH_ID = #{skuSupplyAuthId,jdbcType=INTEGER}
  </update>
  <update id="saveDeleteRelatedSku">
    UPDATE T_SKU_SUPPLY_AUTH_DETAIL
    SET IS_DELETE = 1,
    UPDATER = #{updater,jdbcType=INTEGER},
    MOD_TIME = #{modTime,jdbcType=DATE}
    WHERE
        SKU_SUPPLY_AUTH_DETAIL_ID IN
        <foreach collection="skuSupplyAuthDetailIdList" index="index" item="item" open="(" separator="," close=")">
          #{item,jdbcType=INTEGER}
        </foreach>
  </update>
</mapper>