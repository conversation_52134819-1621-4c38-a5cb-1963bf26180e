package com.vedeng.erp.business.service;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.business.domain.dto.SupportRecordDto;
import com.vedeng.erp.business.domain.dto.SupportRecordReqDto;
import com.vedeng.erp.business.dto.BusinessRequestDto;
import com.vedeng.erp.business.dto.BusinessSupportReqDto;
import com.vedeng.erp.business.dto.NodeDto;

import java.util.List;

public interface BusinessSupportService {

    List<NodeDto> getSupportList();

    void saveSupportRecord(BusinessSupportReqDto businessSupportReqDto, CurrentUser currentUser);

    void saveSeekHelp(BusinessRequestDto businessRequestDto,CurrentUser currentUser);

    List<SupportRecordDto> getSupportRecordList(SupportRecordReqDto supportRecordReqDto);
}
