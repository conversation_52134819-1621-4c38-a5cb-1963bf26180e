package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.entity.VisitRecord;
import com.vedeng.erp.trader.dto.VisitInputApiDto;
import com.vedeng.erp.trader.dto.VisitInputDto;
import com.vedeng.erp.trader.dto.VisitSearchDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/6
 */
public interface VisitRecordNewMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(VisitRecord record);

    int insertSelective(VisitRecord record);

    VisitRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(VisitRecord record);

    int updateByPrimaryKey(VisitRecord record);

    int updateVisitRecordForOrgGroup(VisitInputDto  visitInputDto);

    List<VisitInputDto> selectHistoryVisitRecord();

    VisitInputApiDto selectVisitInputDtoByPrimaryKey(Integer id);

    Map<String, Integer> queryVisitRecordByVisitorId(Integer visitorId);

    List<VisitInputDto> searchVisitRecordListPage(@Param("visitSearchDto") VisitSearchDto visitSearchDto);

    List<VisitInputDto> searchVisitRecordListPageForYingji(@Param("visitSearchDto") VisitSearchDto visitSearchDto);

    List<VisitInputDto> searchVisitRecordListPageForFeigong(@Param("visitSearchDto") VisitSearchDto visitSearchDto);

    boolean checkIfTraderExists(@Param("userIdList") List<Integer> userIdList,@Param("traderId")Integer traderId,@Param("customerType")Integer customerType);
}