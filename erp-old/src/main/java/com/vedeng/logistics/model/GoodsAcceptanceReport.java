package com.vedeng.logistics.model;

import java.io.Serializable;
import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

/**
    * 商品验收报告表
    */
@Data
public class GoodsAcceptanceReport extends BaseEntity implements Serializable {
    /**
    * 主键
    */
    private Long goodsAcceptanceReportId;

    /**
    * 所属单据类型：1商品外借
    */
    private Integer type;

    /**
    * 所属单号
    */
    private String orderNo;

    /**
    * SKU唯一编码
    */
    private String sku;

    /**
    * 商品id
    */
    private Integer goodsId;

    /**
    * 外包装
    */
    private String packaging;

    /**
    * 外观
    */
    private String appearance;

    /**
    * 性能
    */
    private String performance;

    /**
    * 退回原因
    */
    private String backReason;

    /**
    * 具体描述
    */
    private String description;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 备注
    */
    private String remark;

    /**
    * 更新备注
    */
    private String updateRemark;

    private static final long serialVersionUID = 1L;
}