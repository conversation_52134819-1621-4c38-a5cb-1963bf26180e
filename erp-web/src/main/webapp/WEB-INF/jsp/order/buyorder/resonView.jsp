<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="修改采购要求" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/order/buyorder/resonView.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<div class="formpublic">
    <form>
        <blockquote class="layui-elem-quote layui-text" style="border-left: 5px solid #3384ef">
            请谨慎选择。
        </blockquote>
        <div>
            <ul>
                <li>
                    <div class="form-tips">
                        <span>*</span>
                        <lable>原因</lable>
                    </div>
                    <textarea name="reson" id="reson" cols="40" rows="5"></textarea>
                </li>
            </ul>
        </div>
        <div class='f_right'>
            <div class='inputfloat'>
                <input type="hidden" name="saleorderGoodsId" id="saleorderGoodsIds" value="${saleorderGoodsIds}" readonly="readonly"/>
                <input type="hidden" name="componentId" id="componentId" value="${componentId}" readonly="readonly"/>
                <span class="bg-light-grey bt-bg-style bt-small" id="cancel">取消</span>
                <span class="bg-light-blue bt-bg-style bt-small" id="determine">确定</span>
            </div>
        </div>
    </form>
</div>
</body>

</html>
