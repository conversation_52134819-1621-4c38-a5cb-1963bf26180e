package com.vedeng.workbench.model.dto;

import com.vedeng.workbench.model.vo.base.WorkbenchUserInfo;
import lombok.Data;

import java.util.Date;
import java.util.List;


@Data
public class WorkbenchDataQueryDto  {


    private Integer userId;

    private String username;

    private Integer orgId;

    /**
     * 查询的部门id
     */
    private Integer queryOrgId;

    /**
     * 查询的用户Id
     */
    private Integer queryUserId;

    /**
     * 职务名称
     */
    private String positionName;

    /**
     * 下次沟通时间
     */
    private Date nextContactDate;

    /**
     * 查询时间两天前
     */
    private Date twoDaysBeforeDate;

    /**
     * 查询时间段开始时间
     */
    private long startTime;

    /**
     * 查询时间段结束时间
     */
    private long endTime;


    /**
     * 查询的userIds列表
     */
    private List<Integer> userIds;


}
