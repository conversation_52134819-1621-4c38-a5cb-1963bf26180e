package com.vedeng.order.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.vedeng.order.model.SaleorderCoupon;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* @ClassName: OrderData
* @Description: TODO(这里用一句话描述这个类的作用)
* <AUTHOR>
* @date 2019年8月19日
*
*/
public class OrderData {

    private Integer accountId;
    private Integer ssoAccountId;
    private String orderNo;//订单号
    private Integer orderType;//订单类型
    private String username;//用户昵称
    private String phone;//用户手机号
    private Integer companyId;//公司id
    private String companyName;//公司名称
    private Integer deliveryAddressId;//收货地址ID
    private String deliveryUserName;//收货人名称
    private String deliveryUserArea;//收货地址：省 市 区
    private String deliveryUserAddress;//收件人地址
    private String deliveryUserTel;//收件人电话
    private String deliveryUserPhone;//收件人手机

    /*检测报告【1：不需要检测报告；2：需要检测报告（不盖贝登章）；3：需要检测报告（盖贝登章）】*/
    private Integer testReportMethod;

    private Integer invoiceTraderContactId;//收票联系人id
    private String invoiceTraderContactName;//收票联系人名称
    private String invoiceTraderContactMobile;//收票联系人手机
    private String invoiceTraderContactTelephone;//收票联系人电话
    private Integer invoiceTraderAddressId;//收票地址id
    private Integer invoiceTraderAreaId;//收票地区最小级ID
    private String invoiceTraderArea;//收票地区
    private String invoiceTraderAddress;//收票地址
    private String invoiceUserPhone;//收票电话

    //改字段名称
    private Integer invoiceUserId;//收票联系人id
    private String invoiceUserName;//收票联系人名称
    private String invoiceUserAddress;//收票地址

    private String remakes;//备注
    private BigDecimal marketMomney;//总价

    private BigDecimal awardAmount;//随机立减金额

    private Integer productTypeNum;//种类数量
    private Integer productNum;//商品总数量
    private Integer paymentMode;//支付方式
    private Integer orderStatus;//订单状态
    private BigDecimal jxSalePrice;//贝登精选销售价
    private Integer validStatus;//生效状态
    private Integer isSendInvoice;//是否寄送发票
    private String additionalClause;//附加条款
    private Integer invoiceType;//发票类型 1、增值 2、普通
    private String closeComments;//订单关闭原因
    private String deliveryLevel1Id;//省Id
    private String deliveryLevel2Id;//市id
    private String deliveryLevel3Id;//区id
    private String invoiceTraderDeliveryLevel1Id;
    private String invoiceTraderDeliveryLevel2Id;
    private String invoiceTraderDeliveryLevel3Id;
//    private String deliveryAreaIds;//省市区id
    private List<OrderGoodsData> goodsList=new ArrayList<>();//订单商品
    private Integer cancelType;// 1、超时关闭 2、手动关闭

    private Integer orderSrc;//订单来源：1PC、2微信、3APP 、4VS订单
    private BigDecimal totalMoney;//销售价

    private Integer traderId;//客户ID


    //优惠后的总金额
    private BigDecimal totalCouponedAmount;

    //取消日期
    private String cancelDate;

    //0-未使用 1-已使用
    private Integer isCoupons;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SaleorderCoupon couponInfo;

    /**
     * 秒杀活动id
     */
    private Integer activityId;

    /**
     * 结算标准
     */
    private Integer billPeriodSettlementType;

    private Integer logisticDays;

    private Integer quoteorderId;

    private Integer invoiceMethod;
    /**
     * 货期
     */
    private String purchaseTime;
    /**
     * TERMINAL_TRADER_ID 终端客户ID
     */
    private String terminalTraderId;

    /**
     * TERMINAL_TRADER_NAME 终端客户名称
     */
    private String terminalTraderName;

    /**
     * 指定物流
     */
    private String designatedLogistics;

    /**
     * 暂缓发货(0立即发货，1等通知发货)
     */
    private Integer delayDelivery;

    /**
     * 隐私发货(0不打印，1打印带价格，2打印不带价格 3打印不带贝登元素出库单)
     */
    private Integer privacyDelivery;

    /**
     * 是否偏远订单(0否，1是)
     */
    private Integer isRemoteOrder;
    
    /**
     * 发货方式
     */
    private Integer deliveryType;

    /**
     * 打印随货出库单  0不打印   1打印带价格出库单   2打印不带价格出库单 3打印出库单(归属销售为科研购)
     */
    private Integer isPrintout;

    /**
     * 物流备注
     */
    private String logisticsComments;

    /**
     * 内部备注
     */
    private String comments;
    
    /**
     * 原始采购单号
     */
    private String originBuyOrderNo;

    //DELIVERY_DIRECT采购订单是否直发
    private String deliveryDirect;//Y或N

    /**
     * 是否暂缓开票
     */
    private Integer isDelayInvoice;

    public Integer getIsDelayInvoice() {
        return isDelayInvoice;
    }

    public void setIsDelayInvoice(Integer isDelayInvoice) {
        this.isDelayInvoice = isDelayInvoice;
    }

    public String getDeliveryDirect() {
        return deliveryDirect;
    }

    public void setDeliveryDirect(String deliveryDirect) {
        this.deliveryDirect = deliveryDirect;
    }
    
    

    public String getOriginBuyOrderNo() {
		return originBuyOrderNo;
	}

	public void setOriginBuyOrderNo(String originBuyOrderNo) {
		this.originBuyOrderNo = originBuyOrderNo;
	}

	public Integer getDeliveryType() {
		return deliveryType;
	}

	public void setDeliveryType(Integer deliveryType) {
		this.deliveryType = deliveryType;
	}

	public Integer getIsPrintout() {
		return isPrintout;
	}

	public void setIsPrintout(Integer isPrintout) {
		this.isPrintout = isPrintout;
	}

	public String getLogisticsComments() {
		return logisticsComments;
	}

	public void setLogisticsComments(String logisticsComments) {
		this.logisticsComments = logisticsComments;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public Integer getTestReportMethod() {
        return testReportMethod;
    }

    public void setTestReportMethod(Integer testReportMethod) {
        this.testReportMethod = testReportMethod;
    }

    public BigDecimal getAwardAmount() {
        return awardAmount;
    }

    public void setAwardAmount(BigDecimal awardAmount) {
        this.awardAmount = awardAmount;
    }

    public String getDesignatedLogistics() {
        return designatedLogistics;
    }

    public void setDesignatedLogistics(String designatedLogistics) {
        this.designatedLogistics = designatedLogistics;
    }

    public Integer getDelayDelivery() {
        return delayDelivery;
    }

    public void setDelayDelivery(Integer delayDelivery) {
        this.delayDelivery = delayDelivery;
    }

    public Integer getPrivacyDelivery() {
        return privacyDelivery;
    }

    public void setPrivacyDelivery(Integer privacyDelivery) {
        this.privacyDelivery = privacyDelivery;
    }

    public Integer getIsRemoteOrder() {
        return isRemoteOrder;
    }

    public void setIsRemoteOrder(Integer isRemoteOrder) {
        this.isRemoteOrder = isRemoteOrder;
    }

    public String getPurchaseTime() {
        return purchaseTime;
    }

    public void setPurchaseTime(String purchaseTime) {
        this.purchaseTime = purchaseTime;
    }

    public String getTerminalTraderId() {
        return terminalTraderId;
    }

    public void setTerminalTraderId(String terminalTraderId) {
        this.terminalTraderId = terminalTraderId;
    }

    public String getTerminalTraderName() {
        return terminalTraderName;
    }

    public void setTerminalTraderName(String terminalTraderName) {
        this.terminalTraderName = terminalTraderName;
    }

    private Map<String,Integer> logisticDaysMap = new HashMap<>();

    public Integer getQuoteorderId() {
        return quoteorderId;
    }

    public void setQuoteorderId(Integer quoteorderId) {
        this.quoteorderId = quoteorderId;
    }

    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    public Integer getLogisticDays() {
        return logisticDays;
    }

    public void setLogisticDays(Integer logisticDays) {
        this.logisticDays = logisticDays;
    }

    public Integer getBillPeriodSettlementType() {
        return billPeriodSettlementType;
    }

    public void setBillPeriodSettlementType(Integer billPeriodSettlementType) {
        this.billPeriodSettlementType = billPeriodSettlementType;
    }
    public BigDecimal getTotalCouponedAmount() {
        return totalCouponedAmount;
    }

    public void setTotalCouponedAmount(BigDecimal totalCouponedAmount) {
        this.totalCouponedAmount = totalCouponedAmount;
    }

    public Integer getCancelType() {
        return cancelType;
    }

    public void setCancelType(Integer cancelType) {
        this.cancelType = cancelType;
    }


    public Integer getInvoiceUserId() {
        return invoiceUserId;
    }

    public void setInvoiceUserId(Integer invoiceUserId) {
        this.invoiceUserId = invoiceUserId;
    }

    public String getInvoiceUserName() {
        return invoiceUserName;
    }

    public void setInvoiceUserName(String invoiceUserName) {
        this.invoiceUserName = invoiceUserName;
    }

    public String getInvoiceUserAddress() {
        return invoiceUserAddress;
    }

    public void setInvoiceUserAddress(String invoiceUserAddress) {
        this.invoiceUserAddress = invoiceUserAddress;
    }


//    public String getDeliveryAreaIds() {
//		return deliveryAreaIds;
//	}
//
//	public void setDeliveryAreaIds(String deliveryAreaIds) {
//		this.deliveryAreaIds = deliveryAreaIds;
//	}

	public String getDeliveryLevel1Id() {
		return deliveryLevel1Id;
	}

	public void setDeliveryLevel1Id(String deliveryLevel1Id) {
		this.deliveryLevel1Id = deliveryLevel1Id;
	}

	public String getDeliveryLevel2Id() {
		return deliveryLevel2Id;
	}

	public void setDeliveryLevel2Id(String deliveryLevel2Id) {
		this.deliveryLevel2Id = deliveryLevel2Id;
	}

	public String getDeliveryLevel3Id() {
		return deliveryLevel3Id;
	}

	public void setDeliveryLevel3Id(String deliveryLevel3Id) {
		this.deliveryLevel3Id = deliveryLevel3Id;
	}

	public Integer getInvoiceType() {
		return invoiceType;
	}

	public void setInvoiceType(Integer invoiceType) {
		this.invoiceType = invoiceType;
	}

	public String getCloseComments() {
		return closeComments;
	}

	public void setCloseComments(String closeComments) {
		this.closeComments = closeComments;
	}

	public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getInvoiceUserPhone() {
        return invoiceUserPhone;
    }

    public void setInvoiceUserPhone(String invoiceUserPhone) {
        this.invoiceUserPhone = invoiceUserPhone;
    }

    public Integer getProductTypeNum() {
        return productTypeNum;
    }

    public void setProductTypeNum(Integer productTypeNum) {
        this.productTypeNum = productTypeNum;
    }

    public Integer getValidStatus() {
		return validStatus;
	}

	public void setValidStatus(Integer validStatus) {
		this.validStatus = validStatus;
	}

	public Integer getInvoiceTraderContactId() {
		return invoiceTraderContactId;
	}

	public void setInvoiceTraderContactId(Integer invoiceTraderContactId) {
		this.invoiceTraderContactId = invoiceTraderContactId;
	}

	public String getInvoiceTraderContactName() {
		return invoiceTraderContactName;
	}

	public void setInvoiceTraderContactName(String invoiceTraderContactName) {
		this.invoiceTraderContactName = invoiceTraderContactName;
	}

	public String getInvoiceTraderContactMobile() {
		return invoiceTraderContactMobile;
	}

	public void setInvoiceTraderContactMobile(String invoiceTraderContactMobile) {
		this.invoiceTraderContactMobile = invoiceTraderContactMobile;
	}

	public String getInvoiceTraderContactTelephone() {
		return invoiceTraderContactTelephone;
	}

	public void setInvoiceTraderContactTelephone(String invoiceTraderContactTelephone) {
		this.invoiceTraderContactTelephone = invoiceTraderContactTelephone;
	}

	public Integer getInvoiceTraderAddressId() {
		return invoiceTraderAddressId;
	}

	public void setInvoiceTraderAddressId(Integer invoiceTraderAddressId) {
		this.invoiceTraderAddressId = invoiceTraderAddressId;
	}

	public Integer getInvoiceTraderAreaId() {
		return invoiceTraderAreaId;
	}

	public void setInvoiceTraderAreaId(Integer invoiceTraderAreaId) {
		this.invoiceTraderAreaId = invoiceTraderAreaId;
	}

	public String getInvoiceTraderArea() {
		return invoiceTraderArea;
	}

	public void setInvoiceTraderArea(String invoiceTraderArea) {
		this.invoiceTraderArea = invoiceTraderArea;
	}

	public String getInvoiceTraderAddress() {
		return invoiceTraderAddress;
	}

	public void setInvoiceTraderAddress(String invoiceTraderAddress) {
		this.invoiceTraderAddress = invoiceTraderAddress;
	}

	public BigDecimal getJxSalePrice() {
		return jxSalePrice;
	}

	public void setJxSalePrice(BigDecimal jxSalePrice) {
		this.jxSalePrice = jxSalePrice;
	}

	public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getDeliveryAddressId() {
        return deliveryAddressId;
    }

    public void setDeliveryAddressId(Integer deliveryAddressId) {
        this.deliveryAddressId = deliveryAddressId;
    }

    public String getDeliveryUserName() {
        return deliveryUserName;
    }

    public void setDeliveryUserName(String deliveryUserName) {
        this.deliveryUserName = deliveryUserName;
    }

    public String getDeliveryUserArea() {
        return deliveryUserArea;
    }

    public void setDeliveryUserArea(String deliveryUserArea) {
        this.deliveryUserArea = deliveryUserArea;
    }

    public String getDeliveryUserAddress() {
        return deliveryUserAddress;
    }

    public void setDeliveryUserAddress(String deliveryUserAddress) {
        this.deliveryUserAddress = deliveryUserAddress;
    }

    public String getDeliveryUserTel() {
        return deliveryUserTel;
    }

    public void setDeliveryUserTel(String deliveryUserTel) {
        this.deliveryUserTel = deliveryUserTel;
    }

    public String getDeliveryUserPhone() {
        return deliveryUserPhone;
    }

    public void setDeliveryUserPhone(String deliveryUserPhone) {
        this.deliveryUserPhone = deliveryUserPhone;
    }

    public String getRemakes() {
        return remakes;
    }

    public void setRemakes(String remakes) {
        this.remakes = remakes;
    }

    public BigDecimal getMarketMomney() {
        return marketMomney;
    }

    public void setMarketMomney(BigDecimal marketMomney) {
        this.marketMomney = marketMomney;
    }

    public Integer getProductNum() {
        return productNum;
    }

    public void setProductNum(Integer productNum) {
        this.productNum = productNum;
    }

    public Integer getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(Integer paymentMode) {
        this.paymentMode = paymentMode;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getIsSendInvoice() {
        return isSendInvoice;
    }

    public void setIsSendInvoice(Integer isSendInvoice) {
        this.isSendInvoice = isSendInvoice;
    }

    public String getAdditionalClause() {
        return additionalClause;
    }

    public void setAdditionalClause(String additionalClause) {
        this.additionalClause = additionalClause;
    }

    public List<OrderGoodsData> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<OrderGoodsData> goodsList) {
        this.goodsList = goodsList;
    }

    public Integer getIsCoupons() {
        return isCoupons;
    }

    public void setIsCoupons(Integer isCoupons) {
        this.isCoupons = isCoupons;
    }

    public SaleorderCoupon getCouponInfo() {
        return couponInfo;
    }

    public void setCouponInfo(SaleorderCoupon couponInfo) {
        this.couponInfo = couponInfo;
    }

    public Integer getSsoAccountId() {
        return ssoAccountId;
    }

    public void setSsoAccountId(Integer ssoAccountId) {
        this.ssoAccountId = ssoAccountId;
    }

    @Override
    public String toString() {
        return "OrderData{" +
                "accountId=" + accountId +
                ", ssoAccountId=" + ssoAccountId +
                ", orderNo='" + orderNo + '\'' +
                ", orderType=" + orderType +
                ", username='" + username + '\'' +
                ", phone='" + phone + '\'' +
                ", companyId=" + companyId +
                ", companyName='" + companyName + '\'' +
                ", deliveryAddressId=" + deliveryAddressId +
                ", deliveryUserName='" + deliveryUserName + '\'' +
                ", deliveryUserArea='" + deliveryUserArea + '\'' +
                ", deliveryUserAddress='" + deliveryUserAddress + '\'' +
                ", deliveryUserTel='" + deliveryUserTel + '\'' +
                ", deliveryUserPhone='" + deliveryUserPhone + '\'' +
                ", invoiceTraderContactId=" + invoiceTraderContactId +
                ", invoiceTraderContactName='" + invoiceTraderContactName + '\'' +
                ", invoiceTraderContactMobile='" + invoiceTraderContactMobile + '\'' +
                ", invoiceTraderContactTelephone='" + invoiceTraderContactTelephone + '\'' +
                ", invoiceTraderAddressId=" + invoiceTraderAddressId +
                ", invoiceTraderAreaId=" + invoiceTraderAreaId +
                ", invoiceTraderArea='" + invoiceTraderArea + '\'' +
                ", invoiceTraderAddress='" + invoiceTraderAddress + '\'' +
                ", invoiceUserPhone='" + invoiceUserPhone + '\'' +
                ", invoiceUserId=" + invoiceUserId +
                ", invoiceUserName='" + invoiceUserName + '\'' +
                ", invoiceUserAddress='" + invoiceUserAddress + '\'' +
                ", remakes='" + remakes + '\'' +
                ", marketMomney=" + marketMomney +
                ", productTypeNum=" + productTypeNum +
                ", productNum=" + productNum +
                ", paymentMode=" + paymentMode +
                ", orderStatus=" + orderStatus +
                ", jxSalePrice=" + jxSalePrice +
                ", validStatus=" + validStatus +
                ", isSendInvoice=" + isSendInvoice +
                ", additionalClause='" + additionalClause + '\'' +
                ", invoiceType=" + invoiceType +
                ", closeComments='" + closeComments + '\'' +
                ", deliveryLevel1Id='" + deliveryLevel1Id + '\'' +
                ", deliveryLevel2Id='" + deliveryLevel2Id + '\'' +
                ", deliveryLevel3Id='" + deliveryLevel3Id + '\'' +
                ", invoiceTraderDeliveryLevel1Id='" + invoiceTraderDeliveryLevel1Id + '\'' +
                ", invoiceTraderDeliveryLevel2Id='" + invoiceTraderDeliveryLevel2Id + '\'' +
                ", invoiceTraderDeliveryLevel3Id='" + invoiceTraderDeliveryLevel3Id + '\'' +
                ", goodsList=" + goodsList +
                ", cancelType=" + cancelType +
                ", orderSrc=" + orderSrc +
                ", totalMoney=" + totalMoney +
                ", traderId=" + traderId +
                ", totalCouponedAmount=" + totalCouponedAmount +
                ", cancelDate='" + cancelDate + '\'' +
                ", isCoupons=" + isCoupons +
                ", couponInfo=" + couponInfo +
                ", activityId=" + activityId +
                ", billPeriodSettlementType=" + billPeriodSettlementType +
                ", logisticDays=" + logisticDays +
                ", quoteorderId=" + quoteorderId +
                ", invoiceMethod=" + invoiceMethod +
                ", logisticDaysMap=" + logisticDaysMap +
                '}';
    }

    public Integer getInvoiceMethod() {
        return invoiceMethod;
    }

    public void setInvoiceMethod(Integer invoiceMethod) {
        this.invoiceMethod = invoiceMethod;
    }

    public Integer getOrderSrc() {
        return orderSrc;
    }

    public void setOrderSrc(Integer orderSrc) {
        this.orderSrc = orderSrc;
    }

    public String getInvoiceTraderDeliveryLevel1Id() {
        return invoiceTraderDeliveryLevel1Id;
    }

    public void setInvoiceTraderDeliveryLevel1Id(String invoiceTraderDeliveryLevel1Id) {
        this.invoiceTraderDeliveryLevel1Id = invoiceTraderDeliveryLevel1Id;
    }

    public String getInvoiceTraderDeliveryLevel2Id() {
        return invoiceTraderDeliveryLevel2Id;
    }

    public void setInvoiceTraderDeliveryLevel2Id(String invoiceTraderDeliveryLevel2Id) {
        this.invoiceTraderDeliveryLevel2Id = invoiceTraderDeliveryLevel2Id;
    }

    public String getInvoiceTraderDeliveryLevel3Id() {
        return invoiceTraderDeliveryLevel3Id;
    }

    public void setInvoiceTraderDeliveryLevel3Id(String invoiceTraderDeliveryLevel3Id) {
        this.invoiceTraderDeliveryLevel3Id = invoiceTraderDeliveryLevel3Id;
    }

    public BigDecimal getTotalMoney() {
        return totalMoney;
    }

    public void setTotalMoney(BigDecimal totalMoney) {
        this.totalMoney = totalMoney;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public String getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(String cancelDate) {
        this.cancelDate = cancelDate;
    }

    public Map<String, Integer> getLogisticDaysMap() {
        return logisticDaysMap;
    }

    public void setLogisticDaysMap(Map<String, Integer> logisticDaysMap) {
        this.logisticDaysMap = logisticDaysMap;
    }
}
