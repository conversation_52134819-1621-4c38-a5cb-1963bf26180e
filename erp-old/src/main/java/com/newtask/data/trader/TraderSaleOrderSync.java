package com.newtask.data.trader;

import com.newtask.data.AbstractDataSync;
import com.newtask.data.dao.TraderDateMapper;
import com.vedeng.common.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 更新客户订单信息 交易次数，交易金额，首次交易时间，最近交易时间
 */
@Component
public class TraderSaleOrderSync extends AbstractDataSync {
    @Resource
    private TraderDateMapper traderDateMapper;

    @Override
    public void process(String startTime) {
        super.process(startTime);
    }

    /**
     * 查询某一时间段需要更新的客户
     * @param startTime
     * @return
     */
    @Override
    public List<Integer> loadBizId(Long startTime,Long endTime) {
        List<Integer> list=   traderDateMapper.getSaleOrderTraderId(DateUtil.convertString(startTime,null),DateUtil.convertString(endTime,null));
        return list;
    }

    /**
     * 查询客户 交易次数，交易金额，首次交易时间，最近交易时间
     * @param bizIds
     * @return
     */
    @Override
    public List<Map<String, Object>> loadBizData(List<Integer> bizIds) {
        // 根据 traderId查 退交易次数，交易金额，首次交易时间，最近交易时间
        if(CollectionUtils.isNotEmpty(bizIds)){
            List<Map<String, Object>> list = traderDateMapper.findSaleOrderInfo(bizIds);
            return list;
        }
        return null;
    }

    @Override
    public void updateData(List<Map<String, Object>> dataList) {

        if(CollectionUtils.isNotEmpty(dataList)){
            dataList.forEach(item->{
                Long nowDate = new Date().getTime();
                Integer traderId= NumberUtils.toInt(item.get("traderId")+"");
                //交易次数
                Integer orderCount=NumberUtils.toInt(item.get("orderCount")+"");
                //交易金额
                Double orderTotalAmount = NumberUtils.toDouble(item.get("orderTotalAmount")+"");
                //首次交易时间
                Long firstTraderTime =NumberUtils.toLong(item.get("firstTraderTime")+"") ;
                //最近交易时间
                Long lastTraderTime = NumberUtils.toLong(item.get("lastTraderTime")+"") ;
                //判断宽表中是否存在这条记录
                makeExist(traderId,nowDate);
                traderDateMapper.updateSaleOrderInfo(traderId,orderCount,orderTotalAmount,firstTraderTime,lastTraderTime,nowDate);
            });
        }
    }

    @Override
    public void makeExist(Integer traderId,Long nowDate) {
        Long count = traderDateMapper.makeExist(traderId);
        if (count <= 0) {
            //往宽表里添加数据
            traderDateMapper.insertTraderId(traderId,nowDate);
        }
    }


}
