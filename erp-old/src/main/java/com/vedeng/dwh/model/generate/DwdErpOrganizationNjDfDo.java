package com.vedeng.dwh.model.generate;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import lombok.Data;

/**
 * DWD_ERP_ORGANIZATION_NJ_DF
 * <AUTHOR>
@Data
public class DwdErpOrganizationNjDfDo implements Serializable {
    private Long vdOdsPk;

    private Timestamp startTime;

    private Timestamp endTime;

    /**
     * 直接部门ID
     */
    private Integer orgId;

    /**
     * 一级部门ID
     */
    private Integer l1Id;

    /**
     * 一级部门名称
     */
    private String l1Name;

    /**
     * 一级部门类型
     */
    private Integer l1Type;

    /**
     * 一级部门等级
     */
    private Integer l1Level;

    /**
     * 创建人
     */
    private Integer l1Creator;

    /**
     * 归属公司ID
     */
    private Integer l1CompanyId;

    /**
     * 创建时间
     */
    private Date l1AddTime;

    /**
     * 二级部门ID
     */
    private Integer l2Id;

    /**
     * 二级部门名称
     */
    private String l2Name;

    /**
     * 二级部门类型
     */
    private Integer l2Type;

    /**
     * 二级部门等级
     */
    private Integer l2Level;

    /**
     * 创建人
     */
    private Integer l2Creator;

    /**
     * 归属公司ID
     */
    private Integer l2CompanyId;

    /**
     * 创建时间
     */
    private Date l2AddTime;

    /**
     * 三级部门ID
     */
    private Integer l3Id;

    /**
     * 三级部门名称
     */
    private String l3Name;

    /**
     * 三级部门类型
     */
    private Integer l3Type;

    /**
     * 三级部门等级
     */
    private Integer l3Level;

    /**
     * 创建人
     */
    private Integer l3Creator;

    /**
     * 归属公司ID
     */
    private Integer l3CompanyId;

    /**
     * 创建时间
     */
    private Timestamp l3AddTime;

    private static final long serialVersionUID = 1L;
}