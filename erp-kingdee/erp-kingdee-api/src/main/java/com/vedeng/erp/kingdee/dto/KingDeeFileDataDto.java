package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/13 19:25
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KingDeeFileDataDto extends KingDeeMqBaseDto {

    private Integer id;

    /**
     * 金蝶业务表单
     */
    private String formId;

    /**
     * 金蝶fid 如 对应入库的那金蝶id
     */
    private String fId;

    /**
     * erp单据id 如 对应入库单id
     */
    private String erpId;

    /**
     * 附件url 全路径
     */
    private String url;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 创建者id
     */
    private Integer creator;

    /**
     * 修改者id
     */
    private Integer updater;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 修改者名称
     */
    private String updaterName;

    /**
     * king_dee_event_msg表的业务id，使用formId+erpId拼接
     */
    @BusinessID
    private String businessId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 是否最后一次上传
     */
    private Boolean isLast;

    /**
     * 附件别名
     */
    private String aliasFileName;

    /**
     * 单据编号
     */
    private String billNo;

    /**
     * Base64后的文件字节流
     */
    private String sendByte;
}