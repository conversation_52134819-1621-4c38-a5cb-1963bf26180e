<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.LogCheckGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.LogCheckGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 31 09:38:39 CST 2019.
    -->
    <id column="LOG_ID" jdbcType="INTEGER" property="logId" />
    <result column="LOG_TYPE" jdbcType="INTEGER" property="logType" />
    <result column="LOG_BIZ_ID" jdbcType="INTEGER" property="logBizId" />
    <result column="LOG_STATUS" jdbcType="INTEGER" property="logStatus" />
    <result column="LOG_MESSAGE" jdbcType="VARCHAR" property="logMessage" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="LOG_STATUS_NAME" jdbcType="VARCHAR" property="logStatusName" />
    <result column="SUBMIT_USER_ID" jdbcType="INTEGER" property="submitUserId" />
    <result column="SUBMIT_USER_NAME" jdbcType="VARCHAR" property="submitUserName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 31 09:38:39 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 31 09:38:39 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 31 09:38:39 CST 2019.
    -->
    LOG_ID, LOG_TYPE, LOG_BIZ_ID, LOG_STATUS, LOG_MESSAGE, ADD_TIME, CREATOR, CREATOR_NAME,
    LOG_STATUS_NAME,SUBMIT_USER_ID,SUBMIT_USER_NAME
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.goods.model.LogCheckGenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 31 09:38:39 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from V_LOG_CHECK
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    limit 0,50
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 31 09:38:39 CST 2019.
    -->
    select
    <include refid="Base_Column_List" />
    from V_LOG_CHECK
    where LOG_ID = #{logId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 31 09:38:39 CST 2019.
    -->
    delete from V_LOG_CHECK
    where LOG_ID = #{logId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.goods.model.LogCheckGenerateExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 31 09:38:39 CST 2019.
    -->
    delete from V_LOG_CHECK
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.LogCheckGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 31 09:38:39 CST 2019.
    -->
    <selectKey keyProperty="logId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into V_LOG_CHECK (LOG_TYPE, LOG_BIZ_ID, LOG_STATUS,
      LOG_MESSAGE, ADD_TIME, CREATOR,
      CREATOR_NAME, LOG_STATUS_NAME,SUBMIT_USER_ID,SUBMIT_USER_NAME)
    values (#{logType,jdbcType=INTEGER}, #{logBizId,jdbcType=INTEGER}, #{logStatus,jdbcType=INTEGER},
      #{logMessage,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR}, #{logStatusName,jdbcType=VARCHAR},
      #{submitUserId,jdbcType=INTEGER},#{submitUserName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.LogCheckGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 31 09:38:39 CST 2019.
    -->
    <selectKey keyProperty="logId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into V_LOG_CHECK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="logType != null">
        LOG_TYPE,
      </if>
      <if test="logBizId != null">
        LOG_BIZ_ID,
      </if>
      <if test="logStatus != null">
        LOG_STATUS,
      </if>
      <if test="logMessage != null">
        LOG_MESSAGE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="logStatusName != null">
        LOG_STATUS_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="logType != null">
        #{logType,jdbcType=INTEGER},
      </if>
      <if test="logBizId != null">
        #{logBizId,jdbcType=INTEGER},
      </if>
      <if test="logStatus != null">
        #{logStatus,jdbcType=INTEGER},
      </if>
      <if test="logMessage != null">
        #{logMessage,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="logStatusName != null">
        #{logStatusName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.goods.model.LogCheckGenerateExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 31 09:38:39 CST 2019.
    -->
    select count(*) from V_LOG_CHECK
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="selectCountByBuzIdAndType" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM V_LOG_CHECK WHERE LOG_BIZ_ID = #{buzId,jdbcType=INTEGER} AND LOG_TYPE= #{logType,jdbcType=INTEGER}
  </select>

  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 31 09:38:39 CST 2019.
    -->
    update V_LOG_CHECK
    <set>
      <if test="record.logId != null">
        LOG_ID = #{record.logId,jdbcType=INTEGER},
      </if>
      <if test="record.logType != null">
        LOG_TYPE = #{record.logType,jdbcType=INTEGER},
      </if>
      <if test="record.logBizId != null">
        LOG_BIZ_ID = #{record.logBizId,jdbcType=INTEGER},
      </if>
      <if test="record.logStatus != null">
        LOG_STATUS = #{record.logStatus,jdbcType=INTEGER},
      </if>
      <if test="record.logMessage != null">
        LOG_MESSAGE = #{record.logMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.creatorName != null">
        CREATOR_NAME = #{record.creatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.logStatusName != null">
        LOG_STATUS_NAME = #{record.logStatusName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 31 09:38:39 CST 2019.
    -->
    update V_LOG_CHECK
    set LOG_ID = #{record.logId,jdbcType=INTEGER},
      LOG_TYPE = #{record.logType,jdbcType=INTEGER},
      LOG_BIZ_ID = #{record.logBizId,jdbcType=INTEGER},
      LOG_STATUS = #{record.logStatus,jdbcType=INTEGER},
      LOG_MESSAGE = #{record.logMessage,jdbcType=VARCHAR},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      CREATOR_NAME = #{record.creatorName,jdbcType=VARCHAR},
      LOG_STATUS_NAME = #{record.logStatusName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.LogCheckGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 31 09:38:39 CST 2019.
    -->
    update V_LOG_CHECK
    <set>
      <if test="logType != null">
        LOG_TYPE = #{logType,jdbcType=INTEGER},
      </if>
      <if test="logBizId != null">
        LOG_BIZ_ID = #{logBizId,jdbcType=INTEGER},
      </if>
      <if test="logStatus != null">
        LOG_STATUS = #{logStatus,jdbcType=INTEGER},
      </if>
      <if test="logMessage != null">
        LOG_MESSAGE = #{logMessage,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="logStatusName != null">
        LOG_STATUS_NAME = #{logStatusName,jdbcType=VARCHAR},
      </if>
    </set>
    where LOG_ID = #{logId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.LogCheckGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 31 09:38:39 CST 2019.
    -->
    update V_LOG_CHECK
    set LOG_TYPE = #{logType,jdbcType=INTEGER},
      LOG_BIZ_ID = #{logBizId,jdbcType=INTEGER},
      LOG_STATUS = #{logStatus,jdbcType=INTEGER},
      LOG_MESSAGE = #{logMessage,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      LOG_STATUS_NAME = #{logStatusName,jdbcType=VARCHAR}
    where LOG_ID = #{logId,jdbcType=INTEGER}
  </update>
  <select id="getAllUnfinishProcess" resultType="com.vedeng.goods.model.LogCheckGenerate">
    SELECT
    <include refid="Base_Column_List"/>
    FROM V_LOG_CHECK
    WHERE LOG_ID IN (
        SELECT
        MAX(LOG_ID)
        FROM
            V_LOG_CHECK
        WHERE
          LOG_TYPE IN
          <foreach collection="selfReviewTypeList" open="(" close=")" separator="," item="type">
            #{type,jdbcType=INTEGER}
          </foreach>
          <if test="reviewQueryDto.submitStartTime != null and reviewQueryDto.submitStartTime != ''">
            and date_format(ADD_TIME ,'%Y-%m-%d' )  <![CDATA[ >= ]]> #{reviewQueryDto.submitStartTime}
          </if>
          <if test="reviewQueryDto.submitEndTime != null and reviewQueryDto.submitEndTime != ''">
            and date_format(ADD_TIME ,'%Y-%m-%d' )  <![CDATA[ <= ]]> #{reviewQueryDto.submitEndTime}
          </if>

        GROUP BY LOG_BIZ_ID , LOG_TYPE
        )
    AND LOG_STATUS_NAME = "申请审核";
  </select>

<!--auto generated by MybatisCodeHelper on 2022-05-31-->
  <select id="getByLogBizIdAndLogTypeAndLogStatusOrderByAddTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from V_LOG_CHECK
    where LOG_BIZ_ID=#{logBizId,jdbcType=INTEGER} and LOG_TYPE=#{logType,jdbcType=INTEGER} and
    LOG_STATUS=#{logStatus,jdbcType=INTEGER} order by ADD_TIME desc LIMIT 1
  </select>
</mapper>