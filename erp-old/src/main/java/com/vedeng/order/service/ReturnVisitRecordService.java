package com.vedeng.order.service;

import com.vedeng.orderstream.aftersales.model.ReturnVisitRecord;
import com.vedeng.orderstream.aftersales.model.dto.ReturnVisitRecordDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.aftersale.service
 * @Date 2021/11/29 13:06
 */
public interface ReturnVisitRecordService {

    /**
     * 新增售后回访记录
     * @param returnVisitRecord returnVisitRecord
     * @return 影响行数
     */
    Integer insertReturnVisitRecord(ReturnVisitRecord returnVisitRecord);

    /**
     * 根据售后订单id查询回访记录集合
     * @param afterSaleId 售后订单Id
     * @return 回访记录集合
     */
    List<ReturnVisitRecordDto> getReturnVisitRecordListByAfterSaleId(Integer afterSaleId);
}
