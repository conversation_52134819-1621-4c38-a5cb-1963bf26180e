package com.smallhospital.dao;

import com.smallhospital.model.ContractSku;
import com.smallhospital.model.ElContractSku;
import com.smallhospital.model.vo.ElContractSkuVO;

import javax.inject.Named;
import java.util.List;

@Named
public interface ElContractSkuMapper {

    int deleteByPrimaryKey(Integer elContractSkuId);

    int deleteByContractId(Integer contractId);

    int insert(ElContractSku record);

    int insertSelective(ElContractSku record);

    ElContractSku selectByPrimaryKey(Integer elContractSkuId);

    int updateByPrimaryKeySelective(ElContractSku record);

    int updateByPrimaryKey(ElContractSku record);

    ElContractSkuVO findSkuNameById(Integer skuId);

    int batchUpdateContractSkus(List<ContractSku> skuLists);



    List<ElContractSku> getElContractSkuBySkuId(List<Integer> skuList);

    List<ElContractSkuVO> findByContractId(Integer contractId);

    void batchAddContractSkus(List<ElContractSku> skuLists);
}