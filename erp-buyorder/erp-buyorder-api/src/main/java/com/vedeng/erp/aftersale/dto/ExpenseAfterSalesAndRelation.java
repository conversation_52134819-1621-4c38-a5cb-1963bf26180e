package com.vedeng.erp.aftersale.dto;

import lombok.*;

/**
 * <AUTHOR>
 * @description 自动转单中间对象
 * @date 2023/1/9 9:41
 **/
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExpenseAfterSalesAndRelation {

    /**
     * 采购费用单id
     */
    private Integer buyorderExpenseId;

    /**
     * 采购费用单明细id
     */
    private Integer buyorderExpenseItemId;

    /**
     * skuId
     */
    private Integer skuId;

    /**
     * sku
     */
    private String skuNo;

    /**
     * 实际可用数量
     */
    private Integer availableNum;
}
