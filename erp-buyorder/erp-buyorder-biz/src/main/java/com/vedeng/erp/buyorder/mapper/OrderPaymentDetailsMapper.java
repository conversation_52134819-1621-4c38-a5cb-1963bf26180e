package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.OrderPaymentDetails;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OrderPaymentDetailsMapper {
    int deleteByPrimaryKey(Long tOrderPaymentDetailsId);

    int insert(OrderPaymentDetails record);

    int insertOrUpdate(OrderPaymentDetails record);

    int insertOrUpdateSelective(OrderPaymentDetails record);

    int insertSelective(OrderPaymentDetails record);

    OrderPaymentDetails selectByPrimaryKey(Long tOrderPaymentDetailsId);

    int updateByPrimaryKeySelective(OrderPaymentDetails record);

    int updateByPrimaryKey(OrderPaymentDetails record);

    int updateBatch(List<OrderPaymentDetails> list);

    int updateBatchSelective(List<OrderPaymentDetails> list);

    int batchInsert(@Param("list") List<OrderPaymentDetails> list);

    OrderPaymentDetails findByOrderIdAndOrderTypeAndPaymentMethodType(@Param("orderId") Integer orderId, @Param("orderType") Integer orderType, @Param("paymentMethodType") Integer paymentMethodType);
	
}