package com.vedeng.logistics.controller;

import cn.hutool.core.lang.Assert;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.logistics.eums.WarehouseGoodsInEnum;
import com.vedeng.logistics.model.WarehouseGoodsOutIn;
import com.vedeng.logistics.model.outIn.OutInBaseInfo;
import com.vedeng.logistics.model.outIn.OutInDetail;
import com.vedeng.logistics.service.WarehouseGoodsInService;
import com.vedeng.logistics.service.WarehouseGoodsOutService;
import com.vedeng.logistics.service.WarehousesService;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.AttachmentService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 入库单
 * @Author: yana
 * @Description:
 */
@Controller
@RequestMapping(value = "/warehouseOutIn")
public class WarehouseOutInController extends BaseController {

    public static Log logger = LogFactory.getLog(WarehouseOutInController.class);

    @Resource
    private WarehousesService warehousesService;

    @Autowired
    private WarehouseGoodsInService warehouseGoodsInService;
    @Autowired
    private WarehouseGoodsOutService warehouseGoodsOutService;

    @Resource
    private AttachmentService attachmentService;

    @Value("${oss_url}")
    protected String domain;

    /**
     * 保存入库单附件
     * @param request
     * @param attachment
     * @param warehouseGoodsOutInId
     * @return
     */
    @RequestMapping(value = "/outInAttachReturnSave")
    @ResponseBody
    public ResultInfo<?> outInAttachReturnSave(HttpServletRequest request, Attachment attachment,String warehouseGoodsOutInId) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        attachment.setAttachmentType(SysOptionConstant.ID_461);
        attachment.setAttachmentFunction(SysOptionConstant.ID_4212);
        if (Objects.nonNull(user)) {
            attachment.setCreator(user.getUserId());
            attachment.setAddTime(DateUtil.sysTimeMillis());
        }
        try {
            attachment.setRelatedId(Integer.valueOf(warehouseGoodsOutInId));
            attachmentService.saveOrUpdateAttachment(attachment);
        }catch (Exception e){
            logger.error("saveOrUpdateAttachment error",e);
            return ResultInfo.error();
        }

        return ResultInfo.success();
    }

    /**
     * 去新增附件页面
     * @param outIn
     * @return
     */
    @RequestMapping("/toAddOutInAttach")
    public ModelAndView toAddOutInAttach(WarehouseGoodsOutIn outIn){
        ModelAndView mv = new ModelAndView();
        if(Objects.isNull(outIn) || Objects.isNull(outIn.getWarehouseGoodsOutInId())){
            return fail(mv);
        }
        mv.addObject("warehouseGoodsOutInId",outIn.getWarehouseGoodsOutInId());
        mv.setViewName("warehouseOutIn/outIn_attach_return");
        return mv;
    }

    /**
     * 入库单详情页
     * @param outIn
     * @return
     */
    @RequestMapping("/detail")
    public ModelAndView detail(WarehouseGoodsOutIn outIn){
        ModelAndView mv = new ModelAndView();
        List<WarehouseGoodsOutIn> outInList = new ArrayList<>();

        OutInBaseInfo outInBaseInfo = warehousesService.getOutInBaseInfo(outIn.getWarehouseGoodsOutInId());
        if(StringUtils.isEmpty(outInBaseInfo.getUpdateRemark())){
            outInBaseInfo.setUpdateRemark("");
        }
        //基本信息
        mv.addObject("outInBaseInfo",outInBaseInfo);
        //入库记录
        List<OutInDetail> outInDetailList = warehousesService.getOutInDetailList(outInBaseInfo.getValidTime(),outInBaseInfo.getOutInNo(),outInBaseInfo.getWmsNo(),outInBaseInfo.getOutInType(),outInBaseInfo.getTraderId());
        outInDetailList.stream().forEach(x->x.setCheckStatusTimeStr(outInBaseInfo.getOutInTime()));
        mv.addObject("outInDetailList",outInDetailList);
        Attachment attachment = new Attachment();
        attachment.setAttachmentType(SysOptionConstant.ID_461);
        attachment.setAttachmentFunction(SysOptionConstant.ID_4212);
        attachment.setRelatedId(Integer.valueOf(outInBaseInfo.getWarehouseGoodsOutInId().toString()));
        List<Attachment> attachmentList = attachmentService.queryOutInAttachmentList(attachment);
        //入库附件
        mv.addObject("attachmentList",attachmentList);

        Attachment attach = new Attachment();
        attach.setAttachmentType(SysOptionConstant.ID_462);
        attach.setAttachmentFunction(SysOptionConstant.ID_4211);
        attach.setRelatedId(Integer.valueOf(outInBaseInfo.getWarehouseGoodsOutInId().toString()));
        List<Attachment> attachList = attachmentService.queryOutInAttachmentList(attach);
        //入库验收报告
        mv.addObject("attachList",attachList);
        mv.setViewName("warehouseOutIn/warehouseOutIn_view");
        return mv;
    }

    /**
     * 去关联采购单或售后单详情页
     * @param outIn
     * @return
     */
    @RequestMapping("/to/relatedPage")
    public ModelAndView relatedPage(WarehouseGoodsOutIn outIn){
        ModelAndView mv = new ModelAndView();
        OutInBaseInfo outInBaseInfo = warehousesService.getOutInBaseInfo(outIn.getWarehouseGoodsOutInId());
        if(Objects.isNull(outInBaseInfo)){
            return fail(mv);
        }
        if(WarehouseGoodsInEnum.BUYORDER_WAREHOUSE_CHANGE_IN.getErpCode().equals(outInBaseInfo.getOutInType())){
            mv.setViewName("redirect:/order/aftersalesUpgrade/viewAfterSalesDetail.do?traderType=2&afterSalesId="+outInBaseInfo.getBuyorderId());
        }else if(WarehouseGoodsInEnum.PURCHASE_IN.getErpCode().equals(outInBaseInfo.getOutInType()) || WarehouseGoodsInEnum.PURCHASE_GIFT_IN.getErpCode().equals(outInBaseInfo.getOutInType())){
            mv.setViewName("redirect:/order/newBuyorder/newViewBuyOrderDetail.do?buyorderId="+outInBaseInfo.getBuyorderId());
        }else if(WarehouseGoodsInEnum.LENDOUT_WAREHOUSE_IN.getErpCode().equals(outInBaseInfo.getOutInType())){
            mv.setViewName("redirect:/wms/commodityLendOut/detail.do?lendOutId="+outInBaseInfo.getBuyorderId());
        }else if(WarehouseGoodsInEnum.RECEIVE_WAREHOUSE_OUT.getErpCode().equals(outInBaseInfo.getOutInType())){
            mv.setViewName("redirect:/wms/surplusIn/applyIndex.do?wmsInputOrderId="+outInBaseInfo.getBuyorderId());
        }else if(WarehouseGoodsInEnum.ORDER_WAREHOUSE_CHANGE_IN.getErpCode().equals(outInBaseInfo.getOutInType())){
            mv.setViewName("redirect:/order/aftersalesUpgrade/viewAfterSalesDetail.do?traderType=1&afterSalesId="+outInBaseInfo.getBuyorderId());
        }else if(WarehouseGoodsInEnum.ORDER_WAREHOUSE_BACK_IN.getErpCode().equals(outInBaseInfo.getOutInType())){
            mv.setViewName("redirect:/order/aftersalesUpgrade/viewAfterSalesDetail.do?traderType=1&afterSalesId="+outInBaseInfo.getBuyorderId());
        } else if (WarehouseGoodsInEnum.UNIT_CONVERSION_IN.getErpCode().equals(outInBaseInfo.getOutInType())) {
            mv.setViewName("redirect:/wmsUnitConversion/detail.do?wmsUnitConversionOrderId="+outInBaseInfo.getBuyorderId());
        }
        return mv;
    }



    /**
     * 删除入库单附件
     * @param attachment
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/delAttachmentId")
    @NoNeedAccessAuthorization
    public ResultInfo<?> delAttachmentId(Attachment attachment) {
        if (Objects.isNull(attachment)) {
            return ResultInfo.error();
        }
        try {
            attachmentService.delAttachment(attachment);
        }catch (Exception e){
            logger.error("delAttachmentId error",e);
            return ResultInfo.error();
        }
        return ResultInfo.success();
    }


    @ResponseBody
    @RequestMapping(value = "/regenerateWarehouseInReport")
    @NoNeedAccessAuthorization
    public R<?> regenerateWarehouseInReport(Long id) {
        try {
            Assert.notNull(id, "入参不可为空");
            warehouseGoodsInService.regenerateWarehouseInReport(id);
            return R.success();
        } catch (Exception e) {
            log.error("regenerateWarehouseInReport error", e);
            return R.error(e.getMessage());
        }

    }


    @ResponseBody
    @RequestMapping(value = "/regenerateWarehouseOutReport")
    @NoNeedAccessAuthorization
    public R<?> regenerateWarehouseOutReport(Long id) {
        try {
            Assert.notNull(id, "入参不可为空");
            warehouseGoodsOutService.regenerateWarehouseOutReport(id);
            return R.success();
        } catch (Exception e) {
            log.error("regenerateWarehouseOutReport error", e);
            return R.error(e.getMessage());
        }

    }
}
