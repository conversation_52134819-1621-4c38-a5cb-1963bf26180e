package com.vedeng.erp.saleorder.service;

import com.vedeng.authorization.model.User;
import com.vedeng.erp.saleorder.model.dto.UnCollectedOrderInfoDto;
import com.vedeng.erp.saleorder.model.dto.WorkbenchDataDto;
import com.vedeng.flash.dto.ReviewQueryDto;
import com.vedeng.flash.dto.vo.ReviewAndMessageVO;
import com.vedeng.order.model.dto.SaleorderUserInfoDto;
import org.apache.ibatis.annotations.Param;


import java.util.List;
import java.util.Map;

import java.util.List;
import java.util.concurrent.ExecutionException;

public interface SaleorderWorkbenchService{
    WorkbenchDataDto getOverviewInfoDate(SaleorderUserInfoDto userInfoDto) throws InterruptedException, ExecutionException;

    /**
     * 待办事项-待审核订单
     * @param userIds
     * @return
     */
    Map getNoVerifyOrderInfo(List<Integer> userIds);

    /**
     * 待办事项-未处理商机
     * @param userIds
     * @return
     */
    Map getUnHandleBusinessChance(List<Integer> userIds);

    /**
     * 待办事项-未收款/部分收款订单
     * @param userIds
     * @return
     */
    Map getNoPaymentOrderInfo(List<Integer> userIds);

    /**
     * 待办事项-未开票订单
     * @param userIds
     * @return
     */
    Map getNoInvoiceOrderInfo(List<Integer> userIds);

    /**
     * 待办事项-带合同回传订单
     * @param user
     * @return
     */
    Map getNoContractOrderInfo(User user);

    /**
     * 待跟踪事项-审核中订单
     * @param userIds
     * @return
     */
    Map getVerifyingOrderInfo(List<Integer> userIds);

    /**
     * 待跟踪事项-未采购/部分采购订单
     * @param userIds
     * @return
     */
    Map getNoPurchaseOrderInfo(List<Integer> userIds);

    /**
     * 待跟踪事项-未发货/部分发货订单
     * @param userIds
     * @return
     */
    Map getNoSendGoodsOrderInfo(List<Integer> userIds);

    /**
     * 待跟踪事项-未完结售后单
     * @param userIds
     * @return
     */
    Map getNoCompletedAfterSaleInfo(List<Integer> userIds);

    List<ReviewAndMessageVO> getApprovalDataInfo(ReviewQueryDto reviewQueryDto, User currentUser);

    /**
     * 获取未回款订单信息
     */
    UnCollectedOrderInfoDto getUnCollectedOrderInfo(List<Integer> userIdList);

    /**
     * 新业绩考核待办
     *
     * @param userIds 下属所有用户id
     * @return Map
     */
    Map getPerformanceEvaluationInfo(List<Integer> userIds);


    /**
     * 获取crm任务数量
     */
    Integer getCrmTaskCount(Integer userId);

    /**
     * 获取crm待办任务数量
     */
    Integer getCrmTodoTaskCount(Integer userId);


    List<com.vedeng.order.model.vo.TaskGroupVo> getCrmTaskGroupCount(Integer userId,String startDate,String endDate);


    /**
     * 获取某一天的任务明细
     * @param userId
     * @param dateStr
     * @return
     */
    List<com.vedeng.order.model.vo.TaskDetailVo> getCrmTaskForOneDay(Integer userId,String dateStr);

    /**
     * 获取工作台业务分析数据
     * @param workbenchDto
     * @return
     */
    com.vedeng.erp.saleorder.vo.WorkbenchBusinessAnalysisVO getWorkbenchBusinessAnalysis(com.vedeng.erp.saleorder.model.query.WorkbenchDto workbenchDto);
}
