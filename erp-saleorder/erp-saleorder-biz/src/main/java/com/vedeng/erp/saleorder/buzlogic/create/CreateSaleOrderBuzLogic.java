package com.vedeng.erp.saleorder.buzlogic.create;

import com.newtask.data.dao.SaleorderDataMapper;
import com.newtask.data.saleorder.SaleorderCurrentOrgAndUserIdSync;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.saleorder.constant.SalesOrderTypeEnum;
import com.vedeng.erp.saleorder.service.BaseSaleOrderService;
import com.vedeng.order.model.Saleorder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @Author: daniel
 * @Date: 2021/10/8 13 56
 * @Description:
 */
@Service
public class CreateSaleOrderBuzLogic {

    private List<String> sequence = Arrays.asList("createOrder","sendMessage");

    public List<String> getSequence() {
        return sequence;
    }

    public void setSequence(List<String> sequence) {
        this.sequence = sequence;
    }

    @Autowired
    private SaleorderDataMapper saleorderDataMapper;

    @Autowired
    private BaseSaleOrderService baseSaleOrderService;

    @Resource
    private SaleorderCurrentOrgAndUserIdSync saleorderCurrentOrgAndUserIdSync;

    public ResultInfo createOrder(Saleorder saleorder){
        baseSaleOrderService.saveSaleOrder(saleorder);
        return new ResultInfo(0,"创建销售单成功");
    }

    public void sendMessage(){

    }


    /**
     * 同步销售单的data数据到列表表
     * @return
     */
    public ResultInfo syncSaleorderDate(Saleorder saleorder){
//        saleorderCurrentOrgAndUserIdSync.process(saleorder.getSaleorderId());
        return new ResultInfo(0,"同步信息至销售data表成功");
    }
    public ResultInfo run(Saleorder saleorder){
        ResultInfo result = new ResultInfo(0,"操作成功");
        for (String methodName : getSequence()){
            switch (methodName) {
                case "createOrder":
                    if(ErpConst.ZERO.equals(result.getCode())){
                        result = this.createOrder(saleorder);
                    }
                    break;

                case "sendMessage":
                    this.sendMessage();
                    break;
                case "syncSaleorderDate":
                    if(ErpConst.ZERO.equals(result.getCode())){
                        result = this.syncSaleorderDate(saleorder);
                    }
                    break;
                default:
                    break;
            }
        }
        return result;
    }
}
