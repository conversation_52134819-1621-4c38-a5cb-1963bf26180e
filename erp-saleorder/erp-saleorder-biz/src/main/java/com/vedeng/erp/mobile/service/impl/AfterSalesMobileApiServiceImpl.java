package com.vedeng.erp.mobile.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.aftersale.mapper.AfterSalesBizMapper;
import com.vedeng.erp.aftersale.mapper.AfterSalesGoodsBizMapper;
import com.vedeng.erp.mobile.api.AfterSalesMobileApiService;
import com.vedeng.erp.mobile.dto.AfterSalesGoodsListResultDto;
import com.vedeng.erp.mobile.dto.AfterSalesListResultDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class AfterSalesMobileApiServiceImpl implements AfterSalesMobileApiService {

    @Autowired
    private AfterSalesBizMapper afterSalesBizMapper;

    @Autowired
    private AfterSalesGoodsBizMapper afterSalesGoodsBizMapper;

    @Override
    public List<AfterSalesListResultDto> getAfterSalesListByOrderIdAndSubjectType(List<Integer> orderIds, Integer subjectType) {
        if (CollUtil.isEmpty(orderIds) || Objects.isNull(subjectType)) {
            return Collections.emptyList();
        }
        return afterSalesBizMapper.getAfterSalesListByOrderIdAndSubjectType(orderIds, subjectType);
    }

    @Override
    public List<AfterSalesGoodsListResultDto> getAfterSalesGoodsListByAfterSalesIds(List<Integer> afterSalesIds) {
        if (CollUtil.isEmpty(afterSalesIds)) {
            return Collections.emptyList();
        }
        return afterSalesGoodsBizMapper.getAfterSalesGoodsListByAfterSalesIds(afterSalesIds);
    }

}
