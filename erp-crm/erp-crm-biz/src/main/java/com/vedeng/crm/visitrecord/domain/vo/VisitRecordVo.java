package com.vedeng.crm.visitrecord.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class VisitRecordVo {
    /** 拜访计划编号 */
    private String visitRecordNo;
    
    /** 序号 */
    private Integer id;
    
    /** 计划拜访时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date planVisitDate;
    
    /** 拜访人 */
    private Integer visitorId;

    /**
     * 拜访人工号
     */
    private String visitorJobNumber;
    
    /** 拜访人用户名 */
    private String visitorName;

    /**
     * 拜访人-头像
     */
    private String visitorPic;

    /** 拜访目标 (A新客开发B商机跟进C老客客情维护D签约会员E产品推广，以逗号隔开) */
    private String visitTarget;
    
    /** 拜访目标-字符串形式 */
    private String visitTargetStr;
    
    /** 省份编码 */
    private Integer provinceCode;
    
    /** 省份名称 */
    private String provinceName;
    
    /** 城市编码 */
    private Integer cityCode;
    
    /** 城市名称 */
    private String cityName;
    
    /** 区编码 */
    private Integer areaCode;
    
    /** 区名称 */
    private String areaName;
    
    /** 拜访地址 */
    private String visitAddress;
    
    /** 客户名称 */
    private String customerName;
    
    /** 客户来源方式 (1erp2终端库3天眼查) */
    private Integer customerFrom;
    
    /** 客户类型 (ERP客户时自动带入且不能修改，其他必填。465分销466终端) */
    private Integer customerNature;
    
    /** 交易者ID */
    private Integer traderId;
    
    /** 客户ID */
    private Integer traderCustomerId;
    
    /** 实际拜访时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualVisitDate;
    
    /** 是否打卡 (Y是N否) */
    private String cardOff;
    
    /** 打卡时间 */
    private String cardTime;
    
    /** 打卡照片 */
    private String pictureList;
    
    /** 拜访内容-联系人信息-姓名 */
    private String contactName;
    
    /** 拜访内容-联系人信息-手机 */
    private String contactMobile;
    
    /** 拜访内容-联系人信息-电话 */
    private String contactTele;
    
    /** 拜访内容-联系人信息-职位 */
    private String contactPosition;
    
    /** 其它联系方式 */
    private String otherContact;


    /** 无联系人 (Y是N否) */
    private String noContract;

    /** 拜访记录添加的-联系人信息-姓名 */
    private String recordContactName;

    /** 拜访记录添加的-联系人信息-手机 */
    private String recordContactMobile;

    /** 拜访记录添加的-联系人信息-电话 */
    private String recordContactTele;

    /** 拜访记录添加的-联系人信息-职位 */
    private String recordContactPosition;

    /** 记录添加的其它联系方式 */
    private String recordOtherContact;

    /** 记录添加的无联系人 */
    private String recordNoContract;



    
    /** 沟通事项-讲解PPT (默认否，Y是N否) */
    private String showPpt;
    
    /** 沟通事项-邀请客户注册 (默认否，Y是N否) */
    private String inviteReg;
    
    /** 沟通事项-邀请客户注册-手机号 */
    private String regMobile;
    
    /** 沟通事项-邀请客户注册-联系人ID */
    private Integer traderContractId;
    
    /** 沟通事项-沟通情况 */
    private String commucateContent;
    
    /** 沟通事项-预计下次拜访时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date nextVisitDate;
    
    /** 沟通事项-是否产生商机 (Y是N否) */
    private String createBusinessChange;
    
    /** 拜访结果，拜访成功Y拜访事项缺失N */
    private String visitSuccess;
    
    /** 添加时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date addTime;
    
    /** 添加人 */
    private Integer addUserId;

    /** 添加人-用户名 */
    private String addUserName;

    /**
     * 添加人-头像
     */
    private String addUserPic;
    
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modTime;
    
    /** 更新人 */
    private Integer modUserId;
    
    /** 是否删除0否1是 */
    private Integer isDelete;
    
    /** 拜访计划的状态：1.待拜访；2.拜访中；3.已拜访；4.已关闭 */
    private Integer visitRecordStatus;
    
    /** 关闭原因 */
    private Integer closeReasonType;

    /**
     * 关闭原因-中文名称
     */
    private String closeReasonTypeName;
    
    /** 关闭原因说明 */
    private String closeReasonContent;
    
    /** 部门ID */
    private Integer orgId;
    
    /** 部门分组名称（GROUP1 6大区，GROUP2 应急3部门） */
    private String orgGroup;

    
    /** 关联单据类型 (0默认商机线索类型 1线索2商机) */
    private Integer relateType;
    
    /** 商机表ID */
    private Integer bussinessChanceId;
    
    /** 商机编号 */
    private String bussinessChanceNo;
    
    /** 拜访完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeDatetime;
    
    /** 拜访关闭时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date closeDatetime;
    
    /** 备注 */
    private String remark;
    
    /** 需求和反馈 */
    private String customerRequires;

    /**
     * 同行人名称集合
     */
    private String tongXingUserNames;

    /**
     * 天眼查标识
     */
    private String tycFlag;
    /**
     * 客户等级
     */
    private String customerGrade;

    /** 客户信息对象 */
    private VisitCustomerVo visitCustomerVo;

    /** 同行人列表 */
    private List<VisitTongxingUserVo> tongxingUserList;
    
    /** 打卡记录列表 */
    private List<VisitRecordCardVo> cardList;
    
    /** 沟通记录列表 */
    private List<CommunicateRecordVo> communicateList;

    /**
     * 附属的商机信息
     */
    private BusinessChanceForVisitDto businessChanceForVisitDto;
    /**
     * 附属的线索信息
     */
    private BusinessLeadsForVisitDto businessLeadsForVisitDto;


    /**
     * 详情中的权限集合
     */
    private VisitDetailButtonDto visitDetailButtonDto;



} 