<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorder.mapper.ExpressDetailMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.ExpressDetail">
        <id column="EXPRESS_DETAIL_ID" jdbcType="INTEGER" property="expressDetailId"/>
        <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId"/>
        <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType"/>
        <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId"/>
        <result column="NUM" jdbcType="INTEGER" property="num"/>
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount"/>
        <result column="HISTORICAL_NUM" jdbcType="INTEGER" property="historicalNum"/>
        <result column="NON_ALL_ARRIVAL_REASON" jdbcType="VARCHAR" property="nonAllArrivalReason"/>
        <result column="BUYORDER_ID" jdbcType="INTEGER" property="buyOrderId"/>
    </resultMap>
    <sql id="Base_Column_List">
        EXPRESS_DETAIL_ID,
        EXPRESS_ID,
        BUSINESS_TYPE,
        RELATED_ID,
        NUM,
        AMOUNT,
        HISTORICAL_NUM,
        NON_ALL_ARRIVAL_REASON
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_EXPRESS_DETAIL
        where EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from T_EXPRESS_DETAIL
        where EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="EXPRESS_DETAIL_ID" keyProperty="expressDetailId"
            parameterType="com.vedeng.erp.buyorder.domain.entity.ExpressDetail" useGeneratedKeys="true">
        insert into T_EXPRESS_DETAIL (EXPRESS_ID, BUSINESS_TYPE, RELATED_ID,
                                      NUM, AMOUNT, HISTORICAL_NUM,
                                      NON_ALL_ARRIVAL_REASON)
        values (#{expressId,jdbcType=INTEGER}, #{businessType,jdbcType=INTEGER}, #{relatedId,jdbcType=INTEGER},
                #{num,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, #{historicalNum,jdbcType=INTEGER},
                #{nonAllArrivalReason,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="EXPRESS_DETAIL_ID" keyProperty="expressDetailId"
            parameterType="com.vedeng.erp.buyorder.domain.entity.ExpressDetail" useGeneratedKeys="true">
        insert into T_EXPRESS_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="expressId != null">
                EXPRESS_ID,
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE,
            </if>
            <if test="relatedId != null">
                RELATED_ID,
            </if>
            <if test="num != null">
                NUM,
            </if>
            <if test="amount != null">
                AMOUNT,
            </if>
            <if test="historicalNum != null">
                HISTORICAL_NUM,
            </if>
            <if test="nonAllArrivalReason != null">
                NON_ALL_ARRIVAL_REASON,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="expressId != null">
                #{expressId,jdbcType=INTEGER},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="num != null">
                #{num,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="historicalNum != null">
                #{historicalNum,jdbcType=INTEGER},
            </if>
            <if test="nonAllArrivalReason != null">
                #{nonAllArrivalReason,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.ExpressDetail">
        update T_EXPRESS_DETAIL
        <set>
            <if test="expressId != null">
                EXPRESS_ID = #{expressId,jdbcType=INTEGER},
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
            </if>
            <if test="relatedId != null">
                RELATED_ID = #{relatedId,jdbcType=INTEGER},
            </if>
            <if test="num != null">
                NUM = #{num,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                AMOUNT = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="historicalNum != null">
                HISTORICAL_NUM = #{historicalNum,jdbcType=INTEGER},
            </if>
            <if test="nonAllArrivalReason != null">
                NON_ALL_ARRIVAL_REASON = #{nonAllArrivalReason,jdbcType=VARCHAR},
            </if>
        </set>
        where EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.ExpressDetail">
        update T_EXPRESS_DETAIL
        set EXPRESS_ID             = #{expressId,jdbcType=INTEGER},
            BUSINESS_TYPE          = #{businessType,jdbcType=INTEGER},
            RELATED_ID             = #{relatedId,jdbcType=INTEGER},
            NUM                    = #{num,jdbcType=INTEGER},
            AMOUNT                 = #{amount,jdbcType=DECIMAL},
            HISTORICAL_NUM         = #{historicalNum,jdbcType=INTEGER},
            NON_ALL_ARRIVAL_REASON = #{nonAllArrivalReason,jdbcType=VARCHAR}
        where EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
    </update>

    <select id="getDeliveryDirectExpressInfo" resultMap="BaseResultMap">
        select TED.*,TB.BUYORDER_ID
        from T_EXPRESS_DETAIL TED
                 left join T_EXPRESS TE on TED.EXPRESS_ID = TE.EXPRESS_ID
                 left join T_BUYORDER_GOODS TBG on TED.RELATED_ID = TBG.BUYORDER_GOODS_ID and TBG.IS_DELETE = 0
                 left join T_BUYORDER TB on TBG.BUYORDER_ID = TB.BUYORDER_ID
        where TED.BUSINESS_TYPE = 515
          and (TE.LOGISTICS_COMMENTS IS NULL OR TE.LOGISTICS_COMMENTS != '虚拟快递单')
          and TE.IS_ENABLE = 1
          and TE.ARRIVAL_STATUS = 0
          and TB.DELIVERY_DIRECT = 1
          and TED.EXPRESS_ID in
            <foreach item="item" index="index" collection="expressIdList"
                     open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        group by TED.EXPRESS_DETAIL_ID;
    </select>
    
    <select id="getBuyorderExpressInfo" resultType="java.lang.Long">
        select sum(c.NUM)
        from T_BUYORDER_GOODS b
                 left join T_EXPRESS_DETAIL c on c.RELATED_ID = b.BUYORDER_GOODS_ID and c.BUSINESS_TYPE = 515
                 left join T_EXPRESS d on c.EXPRESS_ID = d.EXPRESS_ID
        where d.IS_ENABLE = 1
          and b.IS_DELETE = 0
          and b.BUYORDER_ID =  #{buyorderId,jdbcType=INTEGER}
    </select>
</mapper>
