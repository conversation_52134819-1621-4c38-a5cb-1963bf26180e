package com.vedeng.logistics.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2022-12-15
 * @Description: 出入库sn码工具类
 * @Version: 1.0
 */
public class SnUtil {
    /**
     * 包含重复元素的两个list过滤
     * @return
     */
    public static <T> List<T> duplicateRemovalList(List<T> allList,List<T> excludeList){
        List<T> list=new ArrayList<>();
        List<Integer> indexList=new ArrayList<>();
        for (int i=0;i<allList.size();i++) {
            boolean flag=false;
            for (int j=0;j<excludeList.size();j++) {
                if(indexList.contains(j)){
                    continue;
                }
                if(allList.get(i).equals(excludeList.get(j))){
                    indexList.add(j);
                    flag=true;
                    break;
                }
            }
            if (!flag){
                list.add(allList.get(i));
            }
        }
        return list;
    }
}
