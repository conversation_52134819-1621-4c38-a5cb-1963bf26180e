<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.aftersale.mapper.AfterSalesInstallServiceRecordMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecord" >
    <!--          -->
    <id column="AFTER_SALES_SERVICE_ID" property="afterSalesServiceId" jdbcType="INTEGER" />
    <result column="AFTER_SALES_ID" property="afterSalesId" jdbcType="INTEGER" />
    <result column="CHECK_DATE" property="checkDate" jdbcType="DATE" />
    <result column="CHECK_TYPE" property="checkType" jdbcType="INTEGER" />
    <result column="RECORD_ID" property="recordId" jdbcType="VARCHAR" />
    <result column="CHECK_CONCLUSION" property="checkConclusion" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="TIMESTAMP" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
  </resultMap>

  <resultMap id="AfterSalesInstallServiceRecordVo" type="com.vedeng.erp.aftersale.vo.AfterSalesInstallServiceRecordVo" extends="BaseResultMap">
    <result column="CHECK_CONCLUSION_NAME" property="checkConclusionName" jdbcType="VARCHAR" />
    <collection property="recordDetailList" ofType="com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecordDetail" column="afterSalesServiceId">
      <id column="AFTER_SALES_SERVICE_DETAIL_ID" property="afterSalesServiceDetailId" jdbcType="INTEGER" />
      <result column="AFTER_SALES_SERVICE_ID" property="afterSalesServiceId" jdbcType="INTEGER" />
      <result column="SKU" property="sku" jdbcType="VARCHAR" />
      <result column="SKU_NAME" property="skuName" jdbcType="VARCHAR" />
      <result column="BRAND" property="brand" jdbcType="VARCHAR" />
      <result column="MODEL" property="model" jdbcType="VARCHAR" />
      <result column="NUM" property="num" jdbcType="INTEGER" />
      <result column="SERIAL_NUMBER" property="serialNumber" jdbcType="VARCHAR" />
      <result column="SUPPL_CODE" property="supplCode" jdbcType="VARCHAR" />
      <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
      <result column="CREATOR" property="creator" jdbcType="INTEGER" />
      <result column="MOD_TIME" property="modTime" jdbcType="TIMESTAMP" />
      <result column="UPDATER" property="updater" jdbcType="INTEGER" />
      <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
      <result column="AFTER_SALES_GOODS_ID" property="afterSalesGoodsId" jdbcType="INTEGER" />
    </collection>
  </resultMap>


  <sql id="Base_Column_List" >
    <!--          -->
    AFTER_SALES_SERVICE_ID, AFTER_SALES_ID, CHECK_DATE, CHECK_TYPE, RECORD_ID, CHECK_CONCLUSION,
    ADD_TIME, CREATOR, MOD_TIME, UPDATER, IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_INSTALL_SERVICE_RECORD
    where AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER}
  </select>
  <select id="getInstallServiceRecordList" resultMap="AfterSalesInstallServiceRecordVo" parameterType="java.lang.Integer">
    SELECT
        r.AFTER_SALES_SERVICE_ID,
        d.AFTER_SALES_SERVICE_DETAIL_ID,
        d.SKU,
        d.SKU_NAME,
        d.BRAND,
        d.MODEL,
        r.CHECK_DATE,
        d.NUM,
        d.SERIAL_NUMBER,
        d.SUPPL_CODE,
        r.CHECK_TYPE,
        r.RECORD_ID,
        s.TITLE AS CHECK_CONCLUSION_NAME
    FROM
        T_AFTER_SALES_INSTALL_SERVICE_RECORD r
        LEFT JOIN T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL d ON d.AFTER_SALES_SERVICE_ID = r.AFTER_SALES_SERVICE_ID
        LEFT JOIN T_SYS_OPTION_DEFINITION s ON s.SYS_OPTION_DEFINITION_ID = r.CHECK_CONCLUSION
    WHERE
        d.IS_DELETE = 0
        AND r.IS_DELETE = 0
        AND r.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    ORDER BY r.ADD_TIME DESC
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from T_AFTER_SALES_INSTALL_SERVICE_RECORD
    where AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecord" >
    <!--          -->
    insert into T_AFTER_SALES_INSTALL_SERVICE_RECORD (AFTER_SALES_SERVICE_ID, AFTER_SALES_ID,
      CHECK_DATE, CHECK_TYPE, RECORD_ID,
      CHECK_CONCLUSION, ADD_TIME,
      CREATOR, MOD_TIME, UPDATER,
      IS_DELETE)
    values (#{afterSalesServiceId,jdbcType=INTEGER}, #{afterSalesId,jdbcType=INTEGER},
      #{checkDate,jdbcType=DATE}, #{checkType,jdbcType=INTEGER}, #{recordId,jdbcType=INTEGER},
      #{checkConclusion,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER},
      #{isDelete,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecord"
          keyColumn="AFTER_SALES_SERVICE_ID" keyProperty="afterSalesServiceId" useGeneratedKeys="true">
    <!--          -->
    insert into T_AFTER_SALES_INSTALL_SERVICE_RECORD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="afterSalesServiceId != null" >
        AFTER_SALES_SERVICE_ID,
      </if>
      <if test="afterSalesId != null" >
        AFTER_SALES_ID,
      </if>
      <if test="checkDate != null" >
        CHECK_DATE,
      </if>
      <if test="checkType != null" >
        CHECK_TYPE,
      </if>
      <if test="recordId != null" >
        RECORD_ID,
      </if>
      <if test="checkConclusion != null" >
        CHECK_CONCLUSION,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="afterSalesServiceId != null" >
        #{afterSalesServiceId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesId != null" >
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="checkDate != null" >
        #{checkDate,jdbcType=DATE},
      </if>
      <if test="checkType != null" >
        #{checkType,jdbcType=INTEGER},
      </if>
      <if test="recordId != null" >
        #{recordId,jdbcType=INTEGER},
      </if>
      <if test="checkConclusion != null" >
        #{checkConclusion,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecord" >
    <!--          -->
    update T_AFTER_SALES_INSTALL_SERVICE_RECORD
    <set >
      <if test="afterSalesId != null" >
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="checkDate != null" >
        CHECK_DATE = #{checkDate,jdbcType=DATE},
      </if>
      <if test="checkType != null" >
        CHECK_TYPE = #{checkType,jdbcType=INTEGER},
      </if>
      <if test="recordId != null" >
        RECORD_ID = #{recordId,jdbcType=INTEGER},
      </if>
      <if test="checkConclusion != null" >
        CHECK_CONCLUSION = #{checkConclusion,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
    </set>
    where AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecord" >
    <!--          -->
    update T_AFTER_SALES_INSTALL_SERVICE_RECORD
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      CHECK_DATE = #{checkDate,jdbcType=DATE},
      CHECK_TYPE = #{checkType,jdbcType=INTEGER},
      RECORD_ID = #{recordId,jdbcType=INTEGER},
      CHECK_CONCLUSION = #{checkConclusion,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=BIT}
    where AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER}
  </update>
    <update id="logicDelete">
      UPDATE T_AFTER_SALES_INSTALL_SERVICE_RECORD
      SET IS_DELETE = 1
      WHERE
          AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER}
    </update>


</mapper>
