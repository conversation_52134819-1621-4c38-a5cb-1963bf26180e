package com.vedeng.erp.kingdee.batch.read;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.kingdee.batch.common.reader.BaseItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchAttachmentDto;
import com.vedeng.erp.kingdee.batch.dto.BatchKingDeePurchaseReceiptDto;
import com.vedeng.erp.kingdee.batch.repository.BatchAttachmentDtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.NonTransientResourceException;
import org.springframework.batch.item.ParseException;
import org.springframework.batch.item.UnexpectedInputException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * 销售单实物商品出库验收报告 查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/12 10:08
 */
@Slf4j
public class BatchSalePhysicalGoodsOutAcceptanceFormRead extends BaseItemReader<BatchAttachmentDto> {

    private Integer index = 0;

    @Autowired
    private BatchAttachmentDtoMapper batchAttachmentDtoMapper;

    @Override
    public BatchAttachmentDto read() throws Exception {
        List<BatchKingDeePurchaseReceiptDto> saleOutData = (List<BatchKingDeePurchaseReceiptDto>) getStepParameter("saleOutData");
        String formId = (String) getStepParameter("formId");

        if (CollUtil.isEmpty(saleOutData)) {
            return null;
        }
        if (index >= saleOutData.size()) {
            index = 0;
            return null;
        }
        if (StrUtil.isEmpty(formId)) {
            log.error("formId不可为空");
            throw new ServiceException("formId不可为空");
        }
        BatchKingDeePurchaseReceiptDto batchKingDeePurchaseReceiptDto = saleOutData.get(index++);
        BatchAttachmentDto query = BatchAttachmentDto.builder()
                .attachmentType(462)
                .attachmentFunction(4213)
                .relatedId(batchKingDeePurchaseReceiptDto.getWarehouseGoodsOutInId().intValue())
                .build();
        BatchAttachmentDto batchAttachmentDto = batchAttachmentDtoMapper.purchaseInfindByQuery(query);
        if (Objects.isNull(batchAttachmentDto)) {
            return BatchAttachmentDto.builder().build();
        }
        batchAttachmentDto.setFId(batchKingDeePurchaseReceiptDto.getFId());
        batchAttachmentDto.setOutInNo(batchKingDeePurchaseReceiptDto.getOutInNo());
        batchAttachmentDto.setFormId(formId);
        return batchAttachmentDto;
    }
}