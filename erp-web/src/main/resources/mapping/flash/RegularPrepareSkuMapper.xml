<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.flash.dao.RegularPrepareSkuMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.flash.model.RegularPrepareSku" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 18 14:39:24 CST 2021.
    -->
    <id column="REGULAR_ID" property="regularId" jdbcType="INTEGER" />
    <result column="SKU_ID" property="skuId" jdbcType="INTEGER" />
    <result column="SKU_STATUS" property="skuStatus" jdbcType="TINYINT" />
    <result column="SAFE_STOCK" property="safeStock" jdbcType="INTEGER" />
    <result column="ORIGIN_SAFE_STOCK" property="originSafeStock" jdbcType="INTEGER" />
    <result column="SAFE_RATIO" property="safeRatio" jdbcType="INTEGER" />
    <result column="DEPLOY_TYPE" property="deployType" jdbcType="TINYINT" />
    <result column="IS_ADJUST_STOCK" property="isAdjustStock" jdbcType="TINYINT" />
    <result column="FIRST_OUT_STOCK_TIME" property="firstOutStockTime" jdbcType="BIGINT" />
    <result column="WARN_LEVEL" property="warnLevel" jdbcType="TINYINT" />
    <result column="IS_PREPARE" property="isPrepare" jdbcType="TINYINT" />
    <result column="LAST_ADD_TIME" property="lastAddTime" jdbcType="BIGINT" />
    <result column="TASK_DEALER" property="taskDealer" jdbcType="VARCHAR" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="UPDATOR" property="updator" jdbcType="INTEGER" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    REGULAR_ID, SKU_ID, SKU_STATUS, SAFE_STOCK, ORIGIN_SAFE_STOCK, SAFE_RATIO, DEPLOY_TYPE,
    IS_ADJUST_STOCK, FIRST_OUT_STOCK_TIME, WARN_LEVEL, IS_PREPARE, LAST_ADD_TIME, TASK_DEALER, CREATOR,
    ADD_TIME, UPDATOR, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultType="com.vedeng.flash.dto.PrepareStockDto" parameterType="java.lang.Integer" >
    select
        VCS.SKU_NO,
        VCS.SKU_NAME,
        TU.UNIT_NAME AS unit,
        TRPS.SAFE_STOCK
    from T_REGULAR_PREPARE_SKU TRPS
    left join V_CORE_SKU VCS on TRPS.SKU_ID = VCS.SKU_ID
    left join T_UNIT TU on VCS.UNIT_ID = TU.UNIT_ID
    where REGULAR_ID = #{regularId,jdbcType=INTEGER}
  </select>
  <select id="selectOrderingPoollistpage" resultType="com.vedeng.flash.dto.po.OrderingPoolQueryPO">
    select
        a.SKU_ID                'skuId',
        a.SKU_NO                'skuNo',
        a.SHOW_NAME             'goodsName',
        c.BRAND_NAME            'brandName',
        d.TITLE                 'goodsTypeName',
        c2.BASE_CATEGORY_NAME        'categoryNameTwo',
        concat(ifnull(concat(a.CHANGE_NUM,f0.UNIT_NAME),''),'/',f.UNIT_NAME)                  'spec',
        f.UNIT_NAME             'unitName',
        '-' as                  'costPrice',
        0                       'lastYearSum',
        0                       'lastYearPart',
        0                       'threeMonthSum',
        0                       'threeMonthPart',
        0                       'oneMonthSum',
        0                       'oneMonthPart',
        g.USERNAME              'goodsUserName',
        g1.USERNAME              'goodsAssistantName',
        T1.LAST_ADD_TIME        'latestJoinDate',
        T1.SKU_STATUS           'skuStatus'
    from T_REGULAR_PREPARE_SKU T1
        left join V_CORE_SKU a on T1.SKU_ID = a.SKU_ID
        left join V_CORE_SPU b on a.SPU_ID = b.SPU_ID
        left join T_BRAND c on b.BRAND_ID = c.BRAND_ID
        left join T_SYS_OPTION_DEFINITION d on b.SPU_TYPE = d.SYS_OPTION_DEFINITION_ID
        left join V_BASE_CATEGORY c1 on b.CATEGORY_ID = c1.BASE_CATEGORY_ID
        left join V_BASE_CATEGORY c2 on c1.PARENT_ID = c2.BASE_CATEGORY_ID
        left join T_UNIT f on a.BASE_UNIT_ID = f.UNIT_ID
        left join T_UNIT f0 on a.UNIT_ID = f0.UNIT_ID
        left join T_USER g on b.ASSIGNMENT_MANAGER_ID = g.USER_ID
        left join T_USER g1 on b.ASSIGNMENT_ASSISTANT_ID = g1.USER_ID
    <where>
      <if test="orderingPoolQueryVO != null">
          <if test="orderingPoolQueryVO.skuNo != null and orderingPoolQueryVO.skuNo.trim().length() != 0">
              a.SKU_NO like concat('%',#{orderingPoolQueryVO.skuNo,jdbcType=VARCHAR},'%')
          </if>
          <if test="orderingPoolQueryVO.goodsName != null and orderingPoolQueryVO.goodsName.trim().length() != 0">
              and a.SHOW_NAME like concat('%',#{orderingPoolQueryVO.goodsName,jdbcType=VARCHAR},'%')
          </if>
          <if test="orderingPoolQueryVO.brandName != null and orderingPoolQueryVO.brandName.trim().length() != 0">
              and c.BRAND_NAME like concat('%',#{orderingPoolQueryVO.brandName,jdbcType=VARCHAR},'%')
          </if>
          <if test="orderingPoolQueryVO.goodsType != null and orderingPoolQueryVO.goodsType != -1">
              and b.SPU_TYPE = #{orderingPoolQueryVO.goodsType,jdbcType=INTEGER}
          </if>
          <if test="orderingPoolQueryVO.categoryId != null and orderingPoolQueryVO.categoryId != 0">
              and b.CATEGORY_ID = #{orderingPoolQueryVO.categoryId,jdbcType=INTEGER}
          </if>
          <if test="orderingPoolQueryVO.goodsUserId != null and orderingPoolQueryVO.goodsUserId != -1">
              and (b.ASSIGNMENT_MANAGER_ID = #{orderingPoolQueryVO.goodsUserId,jdbcType=INTEGER}
              or b.ASSIGNMENT_ASSISTANT_ID = #{orderingPoolQueryVO.goodsUserId,jdbcType=INTEGER} )
          </if>
          <if test="orderingPoolQueryVO.skuStatus != null">
              and T1.SKU_STATUS = #{orderingPoolQueryVO.skuStatus,jdbcType=INTEGER}
          </if>
      </if>
    </where>
  </select>
  <select id="selectRecommendOrderinglistpage" resultType="com.vedeng.flash.dto.po.RecommendOrderingQueryPO">
    select
        a.SKU_ID                        'skuId',
        a.SKU_NO                        'skuNo',
        a.SHOW_NAME                     'goodsName',
        c.BRAND_NAME                    'brandName',
        d.TITLE                         'goodsTypeName',
        c2.BASE_CATEGORY_NAME                'categoryNameTwo',
        concat(ifnull(concat(a.CHANGE_NUM,f0.UNIT_NAME),''),'/',f.UNIT_NAME)                  'spec',
        f.UNIT_NAME                     'unitName',
        '-'                          as 'costPrice',
        0                               'lastYearSum',
        0                               'lastYearPart',
        0                               'threeMonthSum',
        0                               'threeMonthPart',
        0                               'oneMonthSum',
        0                               'oneMonthPart',
        g.USERNAME                      'goodsUserName',
        case a.CHECK_STATUS
            when 2 then '审核不通过'
            when 1 then '审核中'
            when 0 then '待完善'
            when 3 then '审核通过'
            when 5 then '待提交审核' end 'checkStatus',
        if(r.REGULAR_ID is null,0,1)    'joinPoolFlag'
    from
        V_CORE_SKU a
        left join V_CORE_SPU b on a.SPU_ID = b.SPU_ID
        left join T_BRAND c on b.BRAND_ID = c.BRAND_ID
        left join T_SYS_OPTION_DEFINITION d on b.SPU_TYPE = d.SYS_OPTION_DEFINITION_ID
        left join V_BASE_CATEGORY c1 on b.CATEGORY_ID = c1.BASE_CATEGORY_ID
        left join V_BASE_CATEGORY c2 on c1.PARENT_ID = c2.BASE_CATEGORY_ID
        left join T_UNIT f on a.BASE_UNIT_ID = f.UNIT_ID
        left join T_UNIT f0 on a.UNIT_ID = f0.UNIT_ID
        left join T_USER g on b.ASSIGNMENT_MANAGER_ID = g.USER_ID
        left join T_REGULAR_PREPARE_SKU r on a.SKU_ID = r.SKU_ID and r.SKU_STATUS = 0
      where
        a.STATUS = 1
        and b.STATUS = 1
        <if test="recommendOrderingQueryVO != null">
            <if test="recommendOrderingQueryVO.goodsType != null">
              and b.SPU_TYPE = #{recommendOrderingQueryVO.goodsType,jdbcType=INTEGER}
            </if>
            <if test="recommendOrderingQueryVO.orderingFlag != null">
              <if test="recommendOrderingQueryVO.orderingFlag == 1">
                and r.REGULAR_ID is not null
              </if>
              <if test="recommendOrderingQueryVO.orderingFlag == 0">
                and r.REGULAR_ID is null
              </if>
            </if>
            <if test="recommendOrderingQueryVO.timeFrame != null">
              <if test="recommendOrderingQueryVO.timeFrame == 1">
                and a.ONE_MONTH_RATIO_EIGHTY_SORT != 0
                order by a.ONE_MONTH_RATIO_EIGHTY_SORT
              </if>
              <if test="recommendOrderingQueryVO.timeFrame == 3">
                and a.THREE_MONTH_RATIO_EIGHTY_SORT != 0
                order by a.THREE_MONTH_RATIO_EIGHTY_SORT
              </if>
              <if test="recommendOrderingQueryVO.timeFrame == 12">
                and a.LAST_YEAR_RATIO_EIGHTY_SORT != 0
                order by a.LAST_YEAR_RATIO_EIGHTY_SORT
              </if>
            </if>
        </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_REGULAR_PREPARE_SKU
    where REGULAR_ID = #{regularId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.flash.model.RegularPrepareSku" >
    insert into T_REGULAR_PREPARE_SKU (REGULAR_ID, SKU_ID, SKU_STATUS,
      SAFE_STOCK, ORIGIN_SAFE_STOCK, SAFE_RATIO,
      DEPLOY_TYPE, IS_ADJUST_STOCK, FIRST_OUT_STOCK_TIME,
      WARN_LEVEL, IS_PREPARE, LAST_ADD_TIME,
      CREATOR, ADD_TIME, UPDATOR,
      UPDATE_TIME)
    values (#{regularId,jdbcType=INTEGER}, #{skuId,jdbcType=INTEGER}, #{skuStatus,jdbcType=TINYINT},
      #{safeStock,jdbcType=INTEGER}, #{originSafeStock,jdbcType=INTEGER}, #{safeRatio,jdbcType=INTEGER},
      #{deployType,jdbcType=TINYINT}, #{isAdjustStock,jdbcType=TINYINT}, #{firstOutStockTime,jdbcType=BIGINT},
      #{warnLevel,jdbcType=TINYINT}, #{isPrepare,jdbcType=TINYINT}, #{lastAddTime,jdbcType=BIGINT},
      #{creator,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT}, #{updator,jdbcType=INTEGER},
      #{updateTime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.flash.model.RegularPrepareSku" >
    insert into T_REGULAR_PREPARE_SKU
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="regularId != null" >
        REGULAR_ID,
      </if>
      <if test="skuId != null" >
        SKU_ID,
      </if>
      <if test="skuStatus != null" >
        SKU_STATUS,
      </if>
      <if test="safeStock != null" >
        SAFE_STOCK,
      </if>
      <if test="originSafeStock != null" >
        ORIGIN_SAFE_STOCK,
      </if>
      <if test="safeRatio != null" >
        SAFE_RATIO,
      </if>
      <if test="deployType != null" >
        DEPLOY_TYPE,
      </if>
      <if test="isAdjustStock != null" >
        IS_ADJUST_STOCK,
      </if>
      <if test="firstOutStockTime != null" >
        FIRST_OUT_STOCK_TIME,
      </if>
      <if test="warnLevel != null" >
        WARN_LEVEL,
      </if>
      <if test="isPrepare != null" >
        IS_PREPARE,
      </if>
      <if test="lastAddTime != null" >
        LAST_ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="updator != null" >
        UPDATOR,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="regularId != null" >
        #{regularId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null" >
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuStatus != null" >
        #{skuStatus,jdbcType=TINYINT},
      </if>
      <if test="safeStock != null" >
        #{safeStock,jdbcType=INTEGER},
      </if>
      <if test="originSafeStock != null" >
        #{originSafeStock,jdbcType=INTEGER},
      </if>
      <if test="safeRatio != null" >
        #{safeRatio,jdbcType=INTEGER},
      </if>
      <if test="deployType != null" >
        #{deployType,jdbcType=TINYINT},
      </if>
      <if test="isAdjustStock != null" >
        #{isAdjustStock,jdbcType=TINYINT},
      </if>
      <if test="firstOutStockTime != null" >
        #{firstOutStockTime,jdbcType=BIGINT},
      </if>
      <if test="warnLevel != null" >
        #{warnLevel,jdbcType=TINYINT},
      </if>
      <if test="isPrepare != null" >
        #{isPrepare,jdbcType=TINYINT},
      </if>
      <if test="lastAddTime != null" >
        #{lastAddTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.flash.model.RegularPrepareSku" >
    update T_REGULAR_PREPARE_SKU
    <set >
      <if test="skuId != null" >
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuStatus != null" >
        SKU_STATUS = #{skuStatus,jdbcType=TINYINT},
      </if>
      <if test="safeStock != null" >
        SAFE_STOCK = #{safeStock,jdbcType=INTEGER},
      </if>
      <if test="originSafeStock != null" >
        ORIGIN_SAFE_STOCK = #{originSafeStock,jdbcType=INTEGER},
      </if>
      <if test="safeRatio != null" >
        SAFE_RATIO = #{safeRatio,jdbcType=INTEGER},
      </if>
      <if test="deployType != null" >
        DEPLOY_TYPE = #{deployType,jdbcType=TINYINT},
      </if>
      <if test="isAdjustStock != null" >
        IS_ADJUST_STOCK = #{isAdjustStock,jdbcType=TINYINT},
      </if>
      <if test="firstOutStockTime != null" >
        FIRST_OUT_STOCK_TIME = #{firstOutStockTime,jdbcType=BIGINT},
      </if>
      <if test="warnLevel != null" >
        WARN_LEVEL = #{warnLevel,jdbcType=TINYINT},
      </if>
      <if test="isPrepare != null" >
        IS_PREPARE = #{isPrepare,jdbcType=TINYINT},
      </if>
      <if test="lastAddTime != null" >
        LAST_ADD_TIME = #{lastAddTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updator != null" >
        UPDATOR = #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      </if>
    </set>
    where REGULAR_ID = #{regularId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.flash.model.RegularPrepareSku" >
    update T_REGULAR_PREPARE_SKU
    set SKU_ID = #{skuId,jdbcType=INTEGER},
      SKU_STATUS = #{skuStatus,jdbcType=TINYINT},
      SAFE_STOCK = #{safeStock,jdbcType=INTEGER},
      ORIGIN_SAFE_STOCK = #{originSafeStock,jdbcType=INTEGER},
      SAFE_RATIO = #{safeRatio,jdbcType=INTEGER},
      DEPLOY_TYPE = #{deployType,jdbcType=TINYINT},
      IS_ADJUST_STOCK = #{isAdjustStock,jdbcType=TINYINT},
      FIRST_OUT_STOCK_TIME = #{firstOutStockTime,jdbcType=BIGINT},
      WARN_LEVEL = #{warnLevel,jdbcType=TINYINT},
      IS_PREPARE = #{isPrepare,jdbcType=TINYINT},
      LAST_ADD_TIME = #{lastAddTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      UPDATOR = #{updator,jdbcType=INTEGER},
      UPDATE_TIME = #{updateTime,jdbcType=BIGINT}
    where REGULAR_ID = #{regularId,jdbcType=INTEGER}
  </update>

  <select id="getPrepareStockListPage" parameterType="Map" resultType="com.vedeng.flash.dto.PrepareStockDto">
    SELECT T1.REGULAR_ID,
           T1.SKU_ID,
           T1.SKU_STATUS,
           T1.DEPLOY_TYPE,
           T1.SAFE_STOCK,
           T1.ORIGIN_SAFE_STOCK,
           T1.SAFE_RATIO,
           T1.IS_ADJUST_STOCK,
           T1.FIRST_OUT_STOCK_TIME,
           T1.WARN_LEVEL,
           T1.WARN_LEVEL                           AS stockWarn,
           T1.IS_PREPARE,
           T1.LAST_ADD_TIME,
           T1.CREATOR,
           T2.SKU_NO,
           T2.SKU_NAME,
           T4.BRAND_NAME,
           T3.SPU_TYPE AS spuTypeCode,
           T6.TITLE                                 AS spuType,
           T5.BASE_CATEGORY_NAME as CATEGORY_NAME,
           IF(T3.SPU_TYPE = 316, T2.MODEL, T2.SPEC) AS spec,
           T7.UNIT_NAME                             AS unit,
           T3.ASSIGNMENT_MANAGER_ID,
           T8.USER_ID                               AS owerUserId,
           T8.USERNAME                              AS owerUser,
           T9.USER_ID                               AS assignmentAssistantId,
           0                                        AS intransitStock,
           0                                        AS threeMonthDaysSaleNum,
           0                                        AS receiveTimes,
           (SELECT RATIO_NUM FROM T_PREPARE_RATIO WHERE IS_DELETE = 0 AND RATIO_STATUS = 0 LIMIT 1) AS globalSafeRatio,
           IFNULL((SELECT TIMESTAMPDIFF(DAY, DATE_FORMAT(FROM_UNIXTIME(T1.FIRST_OUT_STOCK_TIME/1000),'%Y-%m-%d %H:%i:%s'), DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%S'))), 0) AS outStockTimes
    FROM T_REGULAR_PREPARE_SKU T1
           LEFT JOIN V_CORE_SKU T2 ON T2.SKU_ID = T1.SKU_ID
           LEFT JOIN V_CORE_SPU T3 ON T3.SPU_ID = T2.SPU_ID
           LEFT JOIN T_BRAND T4 ON T4.BRAND_ID = T3.BRAND_ID
           LEFT JOIN V_BASE_CATEGORY T5 ON T5.BASE_CATEGORY_ID = T3.CATEGORY_ID
           LEFT JOIN T_SYS_OPTION_DEFINITION T6 ON T6.SYS_OPTION_DEFINITION_ID = T3.SPU_TYPE
           LEFT JOIN T_UNIT T7 ON T7.UNIT_ID = T2.BASE_UNIT_ID
           LEFT JOIN T_USER T8 ON T8.USER_ID = T3.ASSIGNMENT_MANAGER_ID
           LEFT JOIN T_USER T9 ON T9.USER_ID = T3.ASSIGNMENT_ASSISTANT_ID
    WHERE 1=1
    <if test="command.dealType != 0">
        AND T1.SKU_STATUS = 0
    </if>
    <if test="command.warnLevel != null and command.warnLevel != -1">
        <if test="command.warnLevel == 0">
          AND (T1.WARN_LEVEL = 0 OR T1.WARN_LEVEL IS NULL)
        </if>
        <if test="command.warnLevel == 1 or command.warnLevel == 2">
          AND T1.WARN_LEVEL = #{command.warnLevel, jdbcType=INTEGER}
        </if>
    </if>
    <if test="command.warnLevel != null and  command.warnLevel == -1 and command.warnAll != null and command.warnAll ==1 ">
        AND T1.WARN_LEVEL IN (1, 2)
    </if>
    <if test="command.skuId != null and command.skuId != ''">
      AND T1.SKU_ID = #{command.skuId, jdbcType=INTEGER}
    </if>
    <if test="command.skuNo != null and command.skuNo != ''">
      AND T2.SKU_NO = #{command.skuNo, jdbcType=VARCHAR}
    </if>
    <if test="command.skuName != null and command.skuName != ''">
      AND T2.SKU_NAME LIKE CONCAT('%',#{command.skuName,jdbcType=VARCHAR},'%' )
    </if>
    <if test="command.brandName != null and command.brandName != ''">
      AND T4.BRAND_NAME LIKE CONCAT('%', #{command.brandName, jdbcType=VARCHAR}, '%')
    </if>
    <if test="command.skuType != null and command.skuType != -1">
      AND T3.SPU_TYPE = #{command.skuType, jdbcType=INTEGER}
    </if>
    <if test="command.assignmentManagerId != null and command.assignmentManagerId != -1">
      AND T8.USER_ID = #{command.assignmentManagerId, jdbcType=INTEGER}
    </if>
    <if test="command.isAdjustStock != null and command.isAdjustStock != -1">
      AND T1.IS_ADJUST_STOCK = #{command.isAdjustStock, jdbcType=INTEGER}
    </if>
    <if test="command.startSafeStock != null">
      AND T1.SAFE_STOCK >= #{command.startSafeStock, jdbcType=INTEGER}
    </if>
    <if test="command.endSafeStock != null">
      <![CDATA[AND T1.SAFE_STOCK <= #{command.endSafeStock, jdbcType=INTEGER}]]>
    </if>
    <if test="command.dealType != null and command.dealType != -1">
        <if test="command.dealType == 0">
            AND T1.SKU_STATUS = 1
        </if>
      AND T1.SKU_ID IN (
        SELECT
          DISTINCT
          SKU_ID
        FROM
            T_REGULAR_OPERATE_LOG
        WHERE
            OPERATE_TYPE = #{command.dealType, jdbcType=INTEGER}
        AND OPERATE_TIME > (
          SELECT UNIX_TIMESTAMP( DATE_ADD( CURDATE(), INTERVAL - DAY ( CURDATE())+ 1 DAY )) * 1000
          )
        <![CDATA[ AND OPERATE_TIME < (
          SELECT UNIX_TIMESTAMP(NOW()) * 1000
        )]]>
      )
    </if>
    <if test="command.taskDealerId != null and command.taskDealerId != -1">
          AND FIND_IN_SET(#{command.taskDealerId, jdbcType=INTEGER}, T1.TASK_DEALER )
    </if>
    <if test="command.userIdList != null and command.userIdList.size() > 0">
<!--        AND (T8.USER_ID IN-->
<!--        <foreach collection="command.userIdList" index="index" open="(" close=")" item="userId" separator=",">-->
<!--            #{userId, jdbcType=INTEGER}-->
<!--        </foreach>OR T9.USER_ID IN-->
<!--        <foreach collection="command.userIdList" index="index" open="(" close=")" item="userId" separator=",">-->
<!--            #{userId, jdbcType=INTEGER}-->
<!--        </foreach>)-->
        AND
        <foreach collection="command.userIdList" item="item" open="(" close=")" separator="OR">
            FIND_IN_SET( #{item}, T1.TASK_DEALER )
        </foreach>
    </if>
    ORDER BY T1.LAST_ADD_TIME DESC
  </select>

  <select id="getPrepareStockByCommand" parameterType="com.vedeng.flash.dto.PrepareStockCommand" resultType="com.vedeng.flash.dto.PrepareStockDto">
    SELECT T1.REGULAR_ID,
    T1.SKU_ID,
    T1.SKU_STATUS,
    T1.DEPLOY_TYPE,
    T1.SAFE_STOCK,
    T1.ORIGIN_SAFE_STOCK,
    T1.SAFE_RATIO,
    T1.IS_ADJUST_STOCK,
    T1.FIRST_OUT_STOCK_TIME,
    T1.WARN_LEVEL,
    T1.IS_PREPARE,
    T1.LAST_ADD_TIME,
    T1.CREATOR,
    T2.SKU_NO,
    T2.SKU_NAME,
    T4.BRAND_NAME,
    T3.SPU_TYPE AS spuTypeCode,
    T6.TITLE                                 AS spuType,
    T5.BASE_CATEGORY_NAME as CATEGORY_NAME,
    IF(T3.SPU_TYPE = 316, T2.MODEL, T2.SPEC) AS spec,
    T7.UNIT_NAME                             AS unit,
    T3.ASSIGNMENT_MANAGER_ID,
    T8.USER_ID                               AS owerUserId,
    T8.USERNAME                              AS owerUser,
    0                                        AS intransitStock,
    0                                        AS threeMonthDaysSaleNum,
    0                                        AS receiveTimes,
    (SELECT RATIO_NUM FROM T_PREPARE_RATIO WHERE IS_DELETE = 0 AND RATIO_STATUS = 0 LIMIT 1) AS globalSafeRatio,
    IFNULL((SELECT TIMESTAMPDIFF(DAY, DATE_FORMAT(FROM_UNIXTIME(T1.FIRST_OUT_STOCK_TIME/1000),'%Y-%m-%d %H:%i:%s'), DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%S'))), 0) AS outStockTimes
    FROM T_REGULAR_PREPARE_SKU T1
    LEFT JOIN V_CORE_SKU T2 ON T2.SKU_ID = T1.SKU_ID
    LEFT JOIN V_CORE_SPU T3 ON T3.SPU_ID = T2.SPU_ID
    LEFT JOIN T_BRAND T4 ON T4.BRAND_ID = T3.BRAND_ID
    LEFT JOIN V_BASE_CATEGORY T5 ON T5.BASE_CATEGORY_ID = T3.CATEGORY_ID
    LEFT JOIN T_SYS_OPTION_DEFINITION T6 ON T6.SYS_OPTION_DEFINITION_ID = T3.SPU_TYPE
    LEFT JOIN T_UNIT T7 ON T7.UNIT_ID = T2.BASE_UNIT_ID
    LEFT JOIN T_USER T8 ON T8.USER_ID = T3.ASSIGNMENT_MANAGER_ID
    WHERE T1.SKU_STATUS = 0
    <if test="regularId != null">
        AND T1.REGULAR_ID = #{regularId, jdbcType=INTEGER}
    </if>
  </select>

  <select id="getReceiveTimesList" parameterType="list" resultType="com.vedeng.flash.dto.PrepareStockDto">
      SELECT
      BG.SKU AS skuNo,
      AVG(
      TIMESTAMPDIFF( DAY, FROM_UNIXTIME( BU.VALID_TIME / 1000, '%Y-%m-%d' ), FROM_UNIXTIME( WA.ADD_TIME / 1000, '%Y-%m-%d' ) )
      ) receiveTimes
      FROM
      T_BUYORDER BU
      LEFT JOIN T_BUYORDER_GOODS BG ON BU.BUYORDER_ID = BG.BUYORDER_ID
      LEFT JOIN T_WAREHOUSE_GOODS_OPERATE_LOG WA ON WA.RELATED_ID = BG.BUYORDER_GOODS_ID
      AND WA.OPERATE_TYPE = 1
      AND WA.IS_ENABLE = 1
      WHERE
      1 = 1
      AND BU.PAYMENT_STATUS = 2
      AND BG.SKU IN
        <foreach collection="list" item="skuNo" index="index"
                 open="(" close=")" separator=",">
          #{skuNo, jdbcType=VARCHAR}
        </foreach>
      AND BU.ORDER_TYPE = 1
      AND WA.ADD_TIME IS NOT NULL
      AND BU.VALID_TIME > ( SELECT UNIX_TIMESTAMP( DATE_ADD( now( ), INTERVAL - 3 MONTH ) ) * 1000 )
      GROUP BY
      BG.SKU
  </select>

    <select id="getInTransitSkuNumList" parameterType="list" resultType="com.vedeng.flash.dto.PrepareStockDto">
        SELECT
        A.skuNo, IF(A.intransitStock &lt; 0 ,0 ,A.intransitStock) intransitStock
        FROM (
        SELECT
        T1.SKU AS skuNo,
        SUM(T1.NUM-IFNULL(T1.AFTER_RETURN_NUM,0)-IFNULL(T1.ARRIVAL_NUM,0)) AS intransitStock
        FROM
        T_BUYORDER_GOODS T1
        LEFT JOIN T_BUYORDER T2 ON T2.BUYORDER_ID = T1.BUYORDER_ID
        WHERE
        T1.SKU IN
        <foreach collection="list" item="skuNo" index="index"
                 open="(" close=")" separator=",">
            #{skuNo, jdbcType=VARCHAR}
        </foreach>
        AND T1.IS_DELETE = 0
        AND T2.STATUS = 1
        AND ( T2.PAYMENT_STATUS = 1 OR T2.PAYMENT_STATUS = 2 )
        AND ( T2.ARRIVAL_STATUS = 0 OR T2.ARRIVAL_STATUS = 1 )
        AND T2.DELIVERY_DIRECT = 0
        GROUP BY T1.SKU
        ) A
    </select>

  <select id="getRecentSkuSaleNumList" parameterType="com.vedeng.flash.dto.PrepareStockCommand" resultType="com.vedeng.flash.dto.PrepareStockDto">
      SELECT
         skuId,
         ROUND(SUM(num)/#{threeMonthDays},0) AS threeMonthDaysSaleNum
      FROM (
          SELECT
                 T1.SKU_ID AS skuId,
                 T3.NUM AS num
          FROM V_CORE_SKU T1
          JOIN V_CORE_SPU T2 ON T1.SPU_ID = T2.SPU_ID
          JOIN T_SALEORDER_GOODS T3 ON T1.SKU_NO = T3.SKU
          JOIN T_SALEORDER T4 ON T3.SALEORDER_ID = T4.SALEORDER_ID
          WHERE T3.IS_DELETE = 0
          AND T3.DELIVERY_DIRECT = 0
          AND T4.VALID_STATUS = 1
          AND T4.PAYMENT_STATUS IN (1,2)
          AND T4.ORDER_TYPE != 2
          AND NOT exists(SELECT 1 FROM T_AFTER_SALES WHERE ORDER_ID = T4.SALEORDER_ID AND TYPE = 539)
          AND T1.SKU_NO IN
          <foreach collection="skuNoList" item="skuNo" index="index"
                   open="(" close=")" separator=",">
              #{skuNo}
          </foreach>
          <![CDATA[
            AND T4.VALID_TIME >= #{startDate}
            AND T4.VALID_TIME <= #{endDate}
          ]]>
      UNION ALL
          SELECT
              T1.SKU_ID AS skuId,
              CASE T5.ATFER_SALES_STATUS
              WHEN 2 THEN T3.NUM - T6.NUM
              WHEN 3 THEN T3.NUM
              ELSE 0 END AS num
          FROM V_CORE_SKU T1
          JOIN V_CORE_SPU T2 ON T1.SPU_ID = T2.SPU_ID
          JOIN T_SALEORDER_GOODS T3 ON T1.SKU_NO = T3.SKU
          JOIN T_SALEORDER T4 ON T3.SALEORDER_ID = T4.SALEORDER_ID
          LEFT JOIN T_AFTER_SALES T5 ON T4.SALEORDER_NO = T5.ORDER_NO
          LEFT JOIN T_AFTER_SALES_GOODS T6 ON T5.AFTER_SALES_ID = T6.AFTER_SALES_ID
          WHERE T3.IS_DELETE = 0
          AND T3.DELIVERY_DIRECT = 0
          AND T4.VALID_STATUS = 1
          AND T4.PAYMENT_STATUS IN (1,2)
          AND T4.ORDER_TYPE != 2
          AND T5.ATFER_SALES_STATUS IN (2, 3)
          AND T5.TYPE = 539
          AND T6.GOODS_ID = T1.SKU_ID
          AND T1.SKU_NO IN
          <foreach collection="skuNoList" item="skuNo" index="index"
                   open="(" close=")" separator=",">
              #{skuNo}
          </foreach>
          <![CDATA[
            AND T4.VALID_TIME >= #{startDate}
            AND T4.VALID_TIME <= #{endDate}
          ]]>
      ) temp
      GROUP BY temp.skuId
  </select>

    <select id="getRecentSkuSaleNumListModify" parameterType="com.vedeng.flash.dto.temp.JoinOrderingPoolOperateTemp" resultType="com.vedeng.flash.dto.PrepareStockDto">
        SELECT
            skuId,
            ROUND(SUM(num)/abs(datediff( date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 3 month),
                            DATE_ADD(curdate(), interval -day(curdate()) + 1 day))),0) AS threeMonthDaysSaleNum
        FROM (
        SELECT
            T1.SKU_ID AS skuId,
            T3.NUM AS num
        FROM V_CORE_SKU T1
            JOIN V_CORE_SPU T2 ON T1.SPU_ID = T2.SPU_ID
            JOIN T_SALEORDER_GOODS T3 ON T1.SKU_NO = T3.SKU
            JOIN T_SALEORDER T4 ON T3.SALEORDER_ID = T4.SALEORDER_ID
        WHERE T3.IS_DELETE = 0
            AND T3.DELIVERY_DIRECT = 0
            AND T4.VALID_STATUS = 1
            AND T4.PAYMENT_STATUS IN (1,2)
            AND T4.ORDER_TYPE != 2
            AND NOT exists(SELECT 1 FROM T_AFTER_SALES WHERE ORDER_ID = T4.SALEORDER_ID AND TYPE = 539)
            AND T1.SKU_ID IN
            <foreach collection="skuIdList" item="skuId" index="index"
                     open="(" close=")" separator=",">
                #{skuId,jdbcType=INTEGER}
            </foreach>
            AND T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 3 month)) *1000
            AND T4.VALID_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000

        UNION ALL

        SELECT
            T1.SKU_ID AS skuId,
            CASE T5.ATFER_SALES_STATUS
            WHEN 2 THEN T3.NUM - T6.NUM
            WHEN 3 THEN T3.NUM
            ELSE 0 END AS num
        FROM V_CORE_SKU T1
            JOIN V_CORE_SPU T2 ON T1.SPU_ID = T2.SPU_ID
            JOIN T_SALEORDER_GOODS T3 ON T1.SKU_NO = T3.SKU
            JOIN T_SALEORDER T4 ON T3.SALEORDER_ID = T4.SALEORDER_ID
            LEFT JOIN T_AFTER_SALES T5 ON T4.SALEORDER_NO = T5.ORDER_NO
            LEFT JOIN T_AFTER_SALES_GOODS T6 ON T5.AFTER_SALES_ID = T6.AFTER_SALES_ID
        WHERE T3.IS_DELETE = 0
            AND T3.DELIVERY_DIRECT = 0
            AND T4.VALID_STATUS = 1
            AND T4.PAYMENT_STATUS IN (1,2)
            AND T4.ORDER_TYPE != 2
            AND T5.ATFER_SALES_STATUS IN (2, 3)
            AND T5.TYPE = 539
            AND T6.GOODS_ID = T1.SKU_ID
            AND T1.SKU_ID IN
            <foreach collection="skuIdList" item="skuId" index="index"
                     open="(" close=")" separator=",">
                #{skuId}
            </foreach>
            AND T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 3 month)) *1000
            AND T4.VALID_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000
        ) temp
        GROUP BY temp.skuId
    </select>

  <select id="getBuyorderListByPrepareSku" parameterType="com.vedeng.flash.dto.PrepareStockCommand" resultType="com.vedeng.order.model.Buyorder">
    SELECT
      T1.BUYORDER_ID,
      T1.BUYORDER_NO,
      T1.VALID_TIME
    FROM
      T_BUYORDER T1
        LEFT JOIN T_BUYORDER_GOODS T2 ON T2.BUYORDER_ID = T1.BUYORDER_ID
    WHERE T1.PAYMENT_STATUS = 2
      AND T2.IS_DELETE = 0
      AND T2.SKU = #{skuNo}
      <![CDATA[
        AND T1.VALID_TIME >= #{startDate}
        AND T1.VALID_TIME <= #{endDate}
      ]]>
  </select>
    <select id="selectOneThreeMonthLastYearSaleNum"
            resultType="com.vedeng.flash.dto.temp.OneThreeMonthLastYearSaleNumTemp">

        select
        #{skuId,jdbcType=INTEGER} as skuId,
        (select ifnull(SUM(T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)), 0)
            from V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
            where T1.STATUS = 1
                and T2.STATUS = 1
                and T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and (T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)) > 0
                and T1.SKU_ID = #{skuId,jdbcType=INTEGER}
                and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month), interval - 1 year)) * 1000
                and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month)) * 1000)
        'lastYearSum',
        (select ifnull(SUM(T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)), 0)
            from V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
                left join T_R_TRADER_J_USER TT3 on TT3.TRADER_ID = T4.TRADER_ID
                left join T_USER TT4 on TT3.USER_ID = TT4.USER_ID
                left join T_R_USER_POSIT TT5 on TT4.USER_ID = TT5.USER_ID
                left join T_POSITION TT6 on TT5.POSITION_ID = TT6.POSITION_ID
                left join T_ORGANIZATION TT7 on TT6.ORG_ID = TT7.ORG_ID
            where T1.STATUS = 1
                and T2.STATUS = 1
                and T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and (T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)) > 0
                and (TT7.ORG_ID = 39 or TT7.PARENT_ID = 39)
                and T1.SKU_ID = #{skuId,jdbcType=INTEGER}
                and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month), interval - 1 year)) * 1000
                and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month)) * 1000)
        'lastYearPart',
        (select ifnull(SUM(T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)), 0)
            from V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
            where T1.STATUS = 1
                and T2.STATUS = 1
                and T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and (T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)) > 0
                and T1.SKU_ID = #{skuId,jdbcType=INTEGER}
                and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 3 month)) *1000
                and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000)
        'threeMonthSum',
        (select ifnull(SUM(T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)), 0)
            from V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
                left join T_R_TRADER_J_USER TT3 on TT3.TRADER_ID = T4.TRADER_ID
                left join T_USER TT4 on TT3.USER_ID = TT4.USER_ID
                left join T_R_USER_POSIT TT5 on TT4.USER_ID = TT5.USER_ID
                left join T_POSITION TT6 on TT5.POSITION_ID = TT6.POSITION_ID
                left join T_ORGANIZATION TT7 on TT6.ORG_ID = TT7.ORG_ID
            where T1.STATUS = 1
                and T2.STATUS = 1
                and T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and (T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)) > 0
                and (TT7.ORG_ID = 39 or TT7.PARENT_ID = 39)
                and T1.SKU_ID = #{skuId,jdbcType=INTEGER}
                and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 3 month)) *1000
                and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000)
        'threeMonthPart',
        (select ifnull(SUM(T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)), 0)
            from V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
            where T1.STATUS = 1
                and T2.STATUS = 1
                and T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and (T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)) > 0
                and T1.SKU_ID = #{skuId,jdbcType=INTEGER}
                and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 1 month)) *1000
                and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000)
        'oneMonthSum',
        (select ifnull(SUM(T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)), 0)
            from V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
                left join T_R_TRADER_J_USER TT3 on TT3.TRADER_ID = T4.TRADER_ID
                left join T_USER TT4 on TT3.USER_ID = TT4.USER_ID
                left join T_R_USER_POSIT TT5 on TT4.USER_ID = TT5.USER_ID
                left join T_POSITION TT6 on TT5.POSITION_ID = TT6.POSITION_ID
                left join T_ORGANIZATION TT7 on TT6.ORG_ID = TT7.ORG_ID
            where T1.STATUS = 1
                and T2.STATUS = 1
                and T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and (T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)) > 0
                and (TT7.ORG_ID = 39 or TT7.PARENT_ID = 39)
                and T1.SKU_ID = #{skuId,jdbcType=INTEGER}
                and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 1 month)) *1000
                and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000)
        'oneMonthPart'

    </select>
    <select id="selectOneThreeMonthLastYearSaleNumModify"
            resultType="com.vedeng.flash.dto.temp.OneThreeMonthLastYearSaleNumTemp">

    select
        TT.SKU_ID as skuId,
        (select ifnull(SUM(T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)), 0)
            from V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
            where T1.STATUS = 1
                and T2.STATUS = 1
                and T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and (T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)) > 0
                and T1.SKU_ID = TT.SKU_ID
                and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month), interval - 1 year)) * 1000
                and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month)) * 1000)
        'lastYearSum',
        (select ifnull(SUM(T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)), 0)
            from V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
                left join T_R_TRADER_J_USER TT3 on TT3.TRADER_ID = T4.TRADER_ID
                left join T_USER TT4 on TT3.USER_ID = TT4.USER_ID
                left join T_R_USER_POSIT TT5 on TT4.USER_ID = TT5.USER_ID
                left join T_POSITION TT6 on TT5.POSITION_ID = TT6.POSITION_ID
                left join T_ORGANIZATION TT7 on TT6.ORG_ID = TT7.ORG_ID
            where T1.STATUS = 1
                and T2.STATUS = 1
                and T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and (T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)) > 0
                and (TT7.ORG_ID = 39 or TT7.PARENT_ID = 39)
                and T1.SKU_ID = TT.SKU_ID
                and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month), interval - 1 year)) * 1000
                and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(date_add(date_add(curdate(), interval -day(curdate()) + 1 day), interval - month(curdate()) + 1 month)) * 1000)
        'lastYearPart',
        (select ifnull(SUM(T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)), 0)
            from V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
            where T1.STATUS = 1
                and T2.STATUS = 1
                and T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and (T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)) > 0
                and T1.SKU_ID = TT.SKU_ID
                and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 3 month)) *1000
                and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000)
        'threeMonthSum',
        (select ifnull(SUM(T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)), 0)
            from V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
                left join T_R_TRADER_J_USER TT3 on TT3.TRADER_ID = T4.TRADER_ID
                left join T_USER TT4 on TT3.USER_ID = TT4.USER_ID
                left join T_R_USER_POSIT TT5 on TT4.USER_ID = TT5.USER_ID
                left join T_POSITION TT6 on TT5.POSITION_ID = TT6.POSITION_ID
                left join T_ORGANIZATION TT7 on TT6.ORG_ID = TT7.ORG_ID
            where T1.STATUS = 1
                and T2.STATUS = 1
                and T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and (T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)) > 0
                and (TT7.ORG_ID = 39 or TT7.PARENT_ID = 39)
                and T1.SKU_ID = TT.SKU_ID
                and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 3 month)) *1000
                and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000)
        'threeMonthPart',
        (select ifnull(SUM(T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)), 0)
            from V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
            where T1.STATUS = 1
                and T2.STATUS = 1
                and T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and (T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)) > 0
                and T1.SKU_ID = TT.SKU_ID
                and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 1 month)) *1000
                and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000)
        'oneMonthSum',
        (select ifnull(SUM(T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)), 0)
            from V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
                left join T_R_TRADER_J_USER TT3 on TT3.TRADER_ID = T4.TRADER_ID
                left join T_USER TT4 on TT3.USER_ID = TT4.USER_ID
                left join T_R_USER_POSIT TT5 on TT4.USER_ID = TT5.USER_ID
                left join T_POSITION TT6 on TT5.POSITION_ID = TT6.POSITION_ID
                left join T_ORGANIZATION TT7 on TT6.ORG_ID = TT7.ORG_ID
            where T1.STATUS = 1
                and T2.STATUS = 1
                and T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and (T3.NUM - IFNULL(T3.AFTER_RETURN_NUM, 0)) > 0
                and (TT7.ORG_ID = 39 or TT7.PARENT_ID = 39)
                and T1.SKU_ID = TT.SKU_ID
                and T4.VALID_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 1 month)) *1000
                and T4.VALID_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000)
        'oneMonthPart'
    from
        V_CORE_SKU TT
    where
        TT.SKU_ID in <foreach item="item" collection="skuIdList" open="(" close=")" separator=",">#{item,jdbcType=INTEGER}</foreach>
    </select>
    <select id="selectBySkuId" resultType="com.vedeng.flash.model.RegularPrepareSku">
        select
            *
        from
            T_REGULAR_PREPARE_SKU
        where
            SKU_ID = #{skuId,jdbcType=INTEGER}
    </select>
    <select id="getSafeRatio" resultType="java.lang.Integer">
        select
            a.RATIO_NUM
        from
            T_PREPARE_RATIO a
        where a.IS_DELETE = 0
        limit 1
    </select>
    <select id="selectNewAddOrderinglistpage" resultType="com.vedeng.flash.dto.po.NewAddOrderingQueryPO">
        select
            a.SKU_ID                        'skuId',
            a.SKU_NO                        'skuNo',
            a.SHOW_NAME                     'goodsName',
            c.BRAND_NAME                    'brandName',
            d.TITLE                         'goodsTypeName',
            c2.BASE_CATEGORY_NAME                'categoryNameTwo',
            concat(ifnull(concat(a.CHANGE_NUM,f0.UNIT_NAME),''),'/',f.UNIT_NAME)                  'spec',
            f.UNIT_NAME                     'unitName',
            '-'                          as 'costPrice',
            0                               'lastYearSum',
            0                               'lastYearPart',
            0                               'threeMonthSum',
            0                               'threeMonthPart',
            0                               'oneMonthSum',
            0                               'oneMonthPart',
            g.USERNAME                      'goodsUserName',
            if(r.REGULAR_ID is null,0,1)    'joinPoolFlag'
        from
            V_CORE_SKU a
           left join V_CORE_SPU b on a.SPU_ID = b.SPU_ID
           left join T_BRAND c on b.BRAND_ID = c.BRAND_ID
           left join T_SYS_OPTION_DEFINITION d on b.SPU_TYPE = d.SYS_OPTION_DEFINITION_ID
           left join V_BASE_CATEGORY c1 on b.CATEGORY_ID = c1.BASE_CATEGORY_ID
           left join V_BASE_CATEGORY c2 on c1.PARENT_ID = c2.BASE_CATEGORY_ID
           left join T_UNIT f on a.BASE_UNIT_ID = f.UNIT_ID
            left join T_UNIT f0 on a.UNIT_ID = f0.UNIT_ID
           left join T_USER g on b.ASSIGNMENT_MANAGER_ID = g.USER_ID
           left join T_REGULAR_PREPARE_SKU r on a.SKU_ID = r.SKU_ID and r.SKU_STATUS = 0
        where
            a.STATUS = 1
            and b.STATUS = 1
            <if test="newAddOrderingQueryVO != null">
                <if test="newAddOrderingQueryVO.skuNo != null and newAddOrderingQueryVO.skuNo.trim().length() != 0">
                    and a.SKU_NO like concat('%',#{newAddOrderingQueryVO.skuNo,jdbcType=VARCHAR},'%')
                </if>
                <if test="newAddOrderingQueryVO.goodsName != null and newAddOrderingQueryVO.goodsName.trim().length() != 0">
                    and a.SHOW_NAME like concat('%',#{newAddOrderingQueryVO.goodsName,jdbcType=VARCHAR},'%')
                </if>
                <if test="newAddOrderingQueryVO.brandName != null and newAddOrderingQueryVO.brandName.trim().length() != 0">
                    and c.BRAND_NAME like concat('%',#{newAddOrderingQueryVO.brandName,jdbcType=VARCHAR},'%')
                </if>
                <if test="newAddOrderingQueryVO.goodsType != null and newAddOrderingQueryVO.goodsType != -1">
                    and b.SPU_TYPE = #{newAddOrderingQueryVO.goodsType,jdbcType=INTEGER}
                </if>
                <if test="newAddOrderingQueryVO.categoryId != null and newAddOrderingQueryVO.categoryId != 0">
                    and b.CATEGORY_ID = #{newAddOrderingQueryVO.categoryId,jdbcType=INTEGER}
                </if>
                <if test="newAddOrderingQueryVO.goodsUserId != null and newAddOrderingQueryVO.goodsUserId != -1">
                    and b.ASSIGNMENT_MANAGER_ID = #{newAddOrderingQueryVO.goodsUserId,jdbcType=INTEGER}
                </if>
            </if>
            order by a.ADD_TIME desc
    </select>
    <select id="selectRegularOperateLogBySkuNoList" resultType="com.vedeng.flash.model.RegularOperateLog">
        select
            T1.REGULAR_ID    'regularId',
            a.SKU_ID         'skuId',
            a.SKU_NO         'skuNo',
            a.SHOW_NAME      'skuName',
            c.BRAND_NAME     'brandName',
            d.TITLE          'spuType',
            c2.BASE_CATEGORY_NAME 'categoryName',
        concat(ifnull(concat(a.CHANGE_NUM,f0.UNIT_NAME),''),'/',f.UNIT_NAME)          'spec',
            f.UNIT_NAME      'unit',
            '-' as           'cost',
            0                'amount',
            0                'yxgAmount',
            0                'threeMonthAmount',
            0                'yxgThreeMonthAmount',
            0                'oneMonthAmount',
            0                'yxgOneMonthAmount',
            g.USER_ID        'owerUserId',
            g.USERNAME       'owerUser',
            0                'stock',
            0                'intransitStock',
            0                'orderStock',
            0                'forecastSafeStock',
            T1.SAFE_STOCK    'safeStock',
            T1.WARN_LEVEL    'stockWarn',
            0                'supplementStock',
            0                'receiveTimes',
            IFNULL((SELECT TIMESTAMPDIFF(DAY, DATE_FORMAT(FROM_UNIXTIME(T1.FIRST_OUT_STOCK_TIME/1000),'%Y-%m-%d %H:%i:%s'), DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%S'))), 0)
                             'outStockTimes',
            null             'operateType',
            null             'operateUserId',
            null             'operateUserName',
            null             'operateTime',
            null             'operateReason',
            T1.LAST_ADD_TIME 'lastAddTime'
        from T_REGULAR_PREPARE_SKU T1
         left join V_CORE_SKU a on T1.SKU_ID = a.SKU_ID
         left join V_CORE_SPU b on a.SPU_ID = b.SPU_ID
         left join T_BRAND c on b.BRAND_ID = c.BRAND_ID
         left join T_SYS_OPTION_DEFINITION d on b.SPU_TYPE = d.SYS_OPTION_DEFINITION_ID
         left join V_BASE_CATEGORY c1 on b.CATEGORY_ID = c1.BASE_CATEGORY_ID
         left join V_BASE_CATEGORY c2 on c1.PARENT_ID = c2.BASE_CATEGORY_ID
         left join T_UNIT f on a.BASE_UNIT_ID = f.UNIT_ID
        left join T_UNIT f0 on a.UNIT_ID = f0.UNIT_ID
         left join T_USER g on b.ASSIGNMENT_MANAGER_ID = g.USER_ID
        where
        a.SKU_NO in
        <foreach collection="skuNoList" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <update id="updatePrepareSkuById" parameterType="com.vedeng.flash.dto.PrepareStockDto" >
    UPDATE T_REGULAR_PREPARE_SKU
    SET
        SAFE_STOCK = #{safeStock,jdbcType=INTEGER},
        ORIGIN_SAFE_STOCK = #{originSafeStock,jdbcType=INTEGER},
        SAFE_RATIO = #{safeRatio,jdbcType=INTEGER},
        DEPLOY_TYPE = #{deployType,jdbcType=TINYINT},
        IS_ADJUST_STOCK = 1,
        UPDATOR = #{updator,jdbcType=INTEGER},
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT}
    WHERE REGULAR_ID = #{regularId,jdbcType=INTEGER}
  </update>

  <update id="deletePrepareRegularById" parameterType="com.vedeng.flash.dto.PrepareStockDto" >
    UPDATE T_REGULAR_PREPARE_SKU
    SET
        SKU_STATUS = 1,
        WARN_LEVEL = 0,
        UPDATOR = #{updator,jdbcType=INTEGER},
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT}
    WHERE REGULAR_ID = #{regularId,jdbcType=INTEGER}
  </update>

  <update id="updatePrepare" parameterType="list" >
    <foreach collection="list" item="item" index="index" open="" close="" separator=";">
      UPDATE T_REGULAR_PREPARE_SKU
      <set>
        IS_PREPARE = 1
      </set>
      WHERE REGULAR_ID = ${item}
    </foreach>
  </update>

  <update id="updatePrepareWarnLevelById" parameterType="com.vedeng.flash.dto.PrepareStockDto" >
    UPDATE T_REGULAR_PREPARE_SKU
    SET
        WARN_LEVEL = #{warnLevel,jdbcType=INTEGER},
        TASK_DEALER = #{taskDealer,jdbcType=INTEGER},
        UPDATOR = #{updator,jdbcType=INTEGER},
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT}
    WHERE REGULAR_ID = #{regularId,jdbcType=INTEGER}
  </update>
    <update id="updatePrepareRegularBySkuId">
        update
            T_REGULAR_PREPARE_SKU
        set
            SKU_STATUS = 1,
            UPDATE_TIME = unix_timestamp(now())*1000,
            UPDATOR = #{user.userId,jdbcType=INTEGER}
        where
            SKU_ID in
            <foreach collection="skuIdList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=INTEGER}
            </foreach>

    </update>

   <select id="getForecastSafeRatio" resultType="java.lang.Integer">
       SELECT RATIO_NUM FROM T_PREPARE_RATIO WHERE IS_DELETE = 0 AND RATIO_STATUS = 0 LIMIT 1
   </select>
    <select id="selectSnapshotListByExportMonth" resultType="com.vedeng.flash.model.RegularSnapshot">
        select
            a.SKU_ID                'skuId',
            a.SKU_NO                'skuNo',
            a.SHOW_NAME             'skuName',
            c.BRAND_NAME            'brandName',
            d.TITLE                 'spuType',
            c2.BASE_CATEGORY_NAME        'categoryName',
            concat(ifnull(a.CHANGE_NUM,''),'/',f.UNIT_NAME)                  'spec',
            f.UNIT_NAME             'unit',
            '-' as                  'cost',
            0                       'amount',
            0                       'yxgAmount',
            0                       'threeMonthAmount',
            0                       'yxgThreeMonthAmount',
            0                       'oneMonthAmount',
            0                       'yxgOneMonthAmount',
            g.USERNAME              'owerUser',
            T1.LAST_ADD_TIME        'lastAddTime',
            T1.SKU_STATUS           'skuStatus',
            FROM_UNIXTIME(CONVERT(T1.LAST_ADD_TIME/1000,SIGNED),'%Y-%m-%d') AS lastAddTimeStr
        from T_REGULAR_PREPARE_SKU T1
            left join V_CORE_SKU a on T1.SKU_ID = a.SKU_ID
            left join V_CORE_SPU b on a.SPU_ID = b.SPU_ID
            left join T_BRAND c on b.BRAND_ID = c.BRAND_ID
            left join T_SYS_OPTION_DEFINITION d on b.SPU_TYPE = d.SYS_OPTION_DEFINITION_ID
            left join V_BASE_CATEGORY c1 on b.CATEGORY_ID = c1.BASE_CATEGORY_ID
            left join V_BASE_CATEGORY c2 on c1.PARENT_ID = c2.BASE_CATEGORY_ID
            left join T_UNIT f on a.UNIT_ID = f.UNIT_ID
            left join T_USER g on b.ASSIGNMENT_MANAGER_ID = g.USER_ID
        where T1.SKU_STATUS = 0
        order by T1.LAST_ADD_TIME desc
    </select>

    <update id="deleteForecastSafeRatio" parameterType="com.vedeng.flash.dto.PrepareStockDto">
        update T_PREPARE_RATIO set IS_DELETE = 1, UPDATOR = #{creator, jdbcType=INTEGER}, UPDATE_TIME = #{addTime, jdbcType=BIGINT}
    </update>

    <insert id="insertForecastSafeRatio" parameterType="com.vedeng.flash.dto.PrepareStockDto">
        INSERT INTO T_PREPARE_RATIO ( RATIO_NUM, RATIO_STATUS, IS_DELETE, CREATOR, ADD_TIME, UPDATOR, UPDATE_TIME )
        VALUES
            ( #{safeRatio, jdbcType=INTEGER}, 0, 0, #{creator, jdbcType=INTEGER}, #{addTime, jdbcType=BIGINT}, #{creator, jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT} );
    </insert>

    <update id="updatePrepareFirstOutStockTimeById" parameterType="com.vedeng.flash.dto.PrepareStockDto" >
        UPDATE T_REGULAR_PREPARE_SKU
        SET
            FIRST_OUT_STOCK_TIME = #{firstOutStockTime,jdbcType=INTEGER},
            UPDATOR = #{updator,jdbcType=INTEGER},
            UPDATE_TIME = #{updateTime,jdbcType=BIGINT}
        WHERE REGULAR_ID = #{regularId,jdbcType=INTEGER}
    </update>

    <select id="getIntranstiStockList" parameterType="com.vedeng.flash.dto.PrepareStockCommand" resultType="com.vedeng.order.model.Buyorder">
        SELECT
               A.skuNo,A.BUYORDER_ID,A.BUYORDER_NO,A.VALID_TIME,
               IF(A.num &lt; 0 ,0 ,A.NUM) num
        FROM (
                 SELECT T2.BUYORDER_ID,
                        T2.BUYORDER_NO,
                        T2.VALID_TIME,
                        T1.SKU                                                                   AS skuNo,
                        SUM(T1.NUM - IFNULL(T1.AFTER_RETURN_NUM, 0) - IFNULL(T1.ARRIVAL_NUM, 0)) AS num
                 FROM T_BUYORDER T2
                          LEFT JOIN T_BUYORDER_GOODS T1 ON T2.BUYORDER_ID = T1.BUYORDER_ID
                 WHERE T1.GOODS_ID = #{skuId, jdbcType=INTEGER}
                   AND T1.IS_DELETE = 0
                   AND T2.STATUS = 1
                   AND (T2.PAYMENT_STATUS = 1 OR T2.PAYMENT_STATUS = 2)
                   AND (T2.ARRIVAL_STATUS = 0 OR T2.DELIVERY_STATUS = 1)
                   AND T2.DELIVERY_DIRECT = 0
                 GROUP BY T2.BUYORDER_ID
                 ORDER BY T2.VALID_TIME DESC
             )A
    </select>
    <select id="selectLastYearSumSaleNum"
            resultType="com.vedeng.flash.dto.temp.OneThreeMonthLastYearSaleNumTemp">
        select skuId, sum(num) as lastYearSum
        from (
            SELECT T1.SKU_ID as skuId, T3.NUM as num
                FROM V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
            where T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and not exists(select 1 from T_AFTER_SALES where ORDER_ID = T4.SALEORDER_ID and TYPE = 539)
                and T1.SKU_ID in
                    <foreach collection="skuIdList" item="item" open="(" close=")" separator=",">#{item,jdbcType=INTEGER}</foreach>
                and T4.SATISFY_DELIVERY_TIME >= UNIX_TIMESTAMP(date_add(date_add(date_add(curdate(), interval - day(curdate()) + 1 day), interval - month(curdate()) + 1 month), interval - 1 year)) * 1000
                and T4.SATISFY_DELIVERY_TIME &lt;= UNIX_TIMESTAMP(date_add(date_add(curdate(), interval - day(curdate()) + 1 day), interval - month(curdate()) + 1 month)) * 1000

            union all

            SELECT T1.SKU_ID as skuId,
                case T5.ATFER_SALES_STATUS
                when 2 then T3.NUM - T6.NUM
                when 3 then T3.NUM
                else 0 end as num
            FROM V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
                left join T_AFTER_SALES T5 ON T4.SALEORDER_NO = T5.ORDER_NO
                left join T_AFTER_SALES_GOODS T6 on T5.AFTER_SALES_ID = T6.AFTER_SALES_ID
            where T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and T5.ATFER_SALES_STATUS in (2, 3)
                and T5.TYPE = 539
                and T6.GOODS_ID = T1.SKU_ID
                and T1.SKU_ID in
                    <foreach collection="skuIdList" item="item" open="(" close=")" separator=",">#{item,jdbcType=INTEGER}</foreach>
                and T4.SATISFY_DELIVERY_TIME >= UNIX_TIMESTAMP(date_add(date_add(date_add(curdate(), interval - day(curdate()) + 1 day), interval - month(curdate()) + 1 month), interval - 1 year)) * 1000
                and T4.SATISFY_DELIVERY_TIME &lt;= UNIX_TIMESTAMP(date_add(date_add(curdate(), interval - day(curdate()) + 1 day), interval - month(curdate()) + 1 month)) * 1000
        ) temp
        group by temp.skuId
    </select>
    <select id="selectLastYearPartSaleNum"
            resultType="com.vedeng.flash.dto.temp.OneThreeMonthLastYearSaleNumTemp">
        select skuId, sum(num) as lastYearPart
        from (
            SELECT T1.SKU_ID as skuId, T3.NUM as num
            FROM V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
                left join T_R_TRADER_J_USER TT3 on TT3.TRADER_ID = T4.TRADER_ID
                left join T_USER TT4 on TT3.USER_ID = TT4.USER_ID
                left join T_R_USER_POSIT TT5 on TT4.USER_ID = TT5.USER_ID
                left join T_POSITION TT6 on TT5.POSITION_ID = TT6.POSITION_ID
                left join T_ORGANIZATION TT7 on TT6.ORG_ID = TT7.ORG_ID
            where T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and not exists(select 1 from T_AFTER_SALES where ORDER_ID = T4.SALEORDER_ID and TYPE = 539)
                and (TT7.ORG_ID = 39 or TT7.PARENT_ID = 39)
                and T1.SKU_ID in
                    <foreach collection="skuIdList" item="item" open="(" close=")" separator=",">#{item,jdbcType=INTEGER}</foreach>
                and T4.SATISFY_DELIVERY_TIME >= UNIX_TIMESTAMP(date_add(date_add(date_add(curdate(), interval - day(curdate()) + 1 day), interval - month(curdate()) + 1 month), interval - 1 year)) * 1000
                and T4.SATISFY_DELIVERY_TIME &lt;= UNIX_TIMESTAMP(date_add(date_add(curdate(), interval - day(curdate()) + 1 day), interval - month(curdate()) + 1 month)) * 1000

        union all

            SELECT T1.SKU_ID as skuId,
                case T5.ATFER_SALES_STATUS
                when 2 then T3.NUM - T6.NUM
                when 3 then T3.NUM
                else 0 end as num
            FROM V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
                left join T_AFTER_SALES T5 ON T4.SALEORDER_NO = T5.ORDER_NO
                left join T_AFTER_SALES_GOODS T6 on T5.AFTER_SALES_ID = T6.AFTER_SALES_ID
                left join T_R_TRADER_J_USER TT3 on TT3.TRADER_ID = T4.TRADER_ID
                left join T_USER TT4 on TT3.USER_ID = TT4.USER_ID
                left join T_R_USER_POSIT TT5 on TT4.USER_ID = TT5.USER_ID
                left join T_POSITION TT6 on TT5.POSITION_ID = TT6.POSITION_ID
                left join T_ORGANIZATION TT7 on TT6.ORG_ID = TT7.ORG_ID
            where T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and T5.ATFER_SALES_STATUS in (2, 3)
                and T5.TYPE = 539
                and T6.GOODS_ID = T1.SKU_ID
                and (TT7.ORG_ID = 39 or TT7.PARENT_ID = 39)
                and T1.SKU_ID in
                    <foreach collection="skuIdList" item="item" open="(" close=")" separator=",">#{item,jdbcType=INTEGER}</foreach>
                and T4.SATISFY_DELIVERY_TIME >= UNIX_TIMESTAMP(date_add(date_add(date_add(curdate(), interval - day(curdate()) + 1 day), interval - month(curdate()) + 1 month), interval - 1 year)) * 1000
                and T4.SATISFY_DELIVERY_TIME &lt;= UNIX_TIMESTAMP(date_add(date_add(curdate(), interval - day(curdate()) + 1 day), interval - month(curdate()) + 1 month)) * 1000
        ) temp
        group by temp.skuId
    </select>
    <select id="selectThreeMonthSumSaleNum"
            resultType="com.vedeng.flash.dto.temp.OneThreeMonthLastYearSaleNumTemp">
        select skuId, sum(num) as threeMonthSum
        from (
            SELECT T1.SKU_ID as skuId, T3.NUM as num
            FROM V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
            where T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and not exists(select 1 from T_AFTER_SALES where ORDER_ID = T4.SALEORDER_ID and TYPE = 539)
                and T1.SKU_ID in
                    <foreach collection="skuIdList" item="item" open="(" close=")" separator=",">#{item,jdbcType=INTEGER}</foreach>
                and T4.SATISFY_DELIVERY_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 3 month)) *1000
                and T4.SATISFY_DELIVERY_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000

            union all

            SELECT T1.SKU_ID as skuId,
                case T5.ATFER_SALES_STATUS
                when 2 then T3.NUM - T6.NUM
                when 3 then T3.NUM
                else 0 end as num
            FROM V_CORE_SKU T1
                join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
                join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
                join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
                left join T_AFTER_SALES T5 ON T4.SALEORDER_NO = T5.ORDER_NO
                left join T_AFTER_SALES_GOODS T6 on T5.AFTER_SALES_ID = T6.AFTER_SALES_ID
            where T3.IS_DELETE = 0
                and (T4.STATUS = 1 or T4.STATUS = 2)
                and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
                and T4.IS_DELETE = 0
                and T4.ORDER_TYPE != 2
                and T5.ATFER_SALES_STATUS in (2, 3)
                and T5.TYPE = 539
                and T6.GOODS_ID = T1.SKU_ID
                and T1.SKU_ID in
                    <foreach collection="skuIdList" item="item" open="(" close=")" separator=",">#{item,jdbcType=INTEGER}</foreach>
                and T4.SATISFY_DELIVERY_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 3 month)) *1000
                and T4.SATISFY_DELIVERY_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000
        ) temp
        group by temp.skuId
    </select>
    <select id="selectThreeMonthPartSaleNum"
            resultType="com.vedeng.flash.dto.temp.OneThreeMonthLastYearSaleNumTemp">
        select skuId, sum(num) as threeMonthPart
        from (
        SELECT T1.SKU_ID as skuId, T3.NUM as num
        FROM V_CORE_SKU T1
            join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
            join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
            join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
            left join T_R_TRADER_J_USER TT3 on TT3.TRADER_ID = T4.TRADER_ID
            left join T_USER TT4 on TT3.USER_ID = TT4.USER_ID
            left join T_R_USER_POSIT TT5 on TT4.USER_ID = TT5.USER_ID
            left join T_POSITION TT6 on TT5.POSITION_ID = TT6.POSITION_ID
            left join T_ORGANIZATION TT7 on TT6.ORG_ID = TT7.ORG_ID
        where T3.IS_DELETE = 0
            and (T4.STATUS = 1 or T4.STATUS = 2)
            and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
            and T4.IS_DELETE = 0
            and T4.ORDER_TYPE != 2
            and not exists(select 1 from T_AFTER_SALES where ORDER_ID = T4.SALEORDER_ID and TYPE = 539)
            and (TT7.ORG_ID = 39 or TT7.PARENT_ID = 39)
            and T1.SKU_ID in
                <foreach collection="skuIdList" item="item" open="(" close=")" separator=",">#{item,jdbcType=INTEGER}</foreach>
            and T4.SATISFY_DELIVERY_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 3 month)) *1000
            and T4.SATISFY_DELIVERY_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000

        union all

        SELECT T1.SKU_ID as skuId,
            case T5.ATFER_SALES_STATUS
            when 2 then T3.NUM - T6.NUM
            when 3 then T3.NUM
            else 0 end as num
        FROM V_CORE_SKU T1
            join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
            join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
            join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
            left join T_AFTER_SALES T5 ON T4.SALEORDER_NO = T5.ORDER_NO
            left join T_AFTER_SALES_GOODS T6 on T5.AFTER_SALES_ID = T6.AFTER_SALES_ID
            left join T_R_TRADER_J_USER TT3 on TT3.TRADER_ID = T4.TRADER_ID
            left join T_USER TT4 on TT3.USER_ID = TT4.USER_ID
            left join T_R_USER_POSIT TT5 on TT4.USER_ID = TT5.USER_ID
            left join T_POSITION TT6 on TT5.POSITION_ID = TT6.POSITION_ID
            left join T_ORGANIZATION TT7 on TT6.ORG_ID = TT7.ORG_ID
        where T3.IS_DELETE = 0
            and (T4.STATUS = 1 or T4.STATUS = 2)
            and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
            and T4.IS_DELETE = 0
            and T4.ORDER_TYPE != 2
            and T5.ATFER_SALES_STATUS in (2, 3)
            and T5.TYPE = 539
            and T6.GOODS_ID = T1.SKU_ID
            and (TT7.ORG_ID = 39 or TT7.PARENT_ID = 39)
            and T1.SKU_ID in
                <foreach collection="skuIdList" item="item" open="(" close=")" separator=",">#{item,jdbcType=INTEGER}</foreach>
            and T4.SATISFY_DELIVERY_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 3 month)) *1000
            and T4.SATISFY_DELIVERY_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000
        ) temp
        group by temp.skuId
    </select>
    <select id="selectOneMonthSumSaleNum"
            resultType="com.vedeng.flash.dto.temp.OneThreeMonthLastYearSaleNumTemp">
        select skuId, sum(num) as oneMonthSum
        from (
        SELECT T1.SKU_ID as skuId, T3.NUM as num
        FROM V_CORE_SKU T1
            join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
            join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
            join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
        where T3.IS_DELETE = 0
            and (T4.STATUS = 1 or T4.STATUS = 2)
            and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
            and T4.IS_DELETE = 0
            and T4.ORDER_TYPE != 2
            and not exists(select 1 from T_AFTER_SALES where ORDER_ID = T4.SALEORDER_ID and TYPE = 539)
            and T1.SKU_ID in
                <foreach collection="skuIdList" item="item" open="(" close=")" separator=",">#{item,jdbcType=INTEGER}</foreach>
            and T4.SATISFY_DELIVERY_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 1 month)) *1000
            and T4.SATISFY_DELIVERY_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000

        union all

        SELECT T1.SKU_ID as skuId,
            case T5.ATFER_SALES_STATUS
            when 2 then T3.NUM - T6.NUM
            when 3 then T3.NUM
            else 0 end as num
        FROM V_CORE_SKU T1
            join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
            join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
            join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
            left join T_AFTER_SALES T5 ON T4.SALEORDER_NO = T5.ORDER_NO
            left join T_AFTER_SALES_GOODS T6 on T5.AFTER_SALES_ID = T6.AFTER_SALES_ID
        where T3.IS_DELETE = 0
            and (T4.STATUS = 1 or T4.STATUS = 2)
            and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
            and T4.IS_DELETE = 0
            and T4.ORDER_TYPE != 2
            and T5.ATFER_SALES_STATUS in (2, 3)
            and T5.TYPE = 539
            and T6.GOODS_ID = T1.SKU_ID
            and T1.SKU_ID in
                <foreach collection="skuIdList" item="item" open="(" close=")" separator=",">#{item,jdbcType=INTEGER}</foreach>
            and T4.SATISFY_DELIVERY_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 1 month)) *1000
            and T4.SATISFY_DELIVERY_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000
        ) temp
        group by temp.skuId
    </select>
    <select id="selectOneMonthPartSaleNum"
            resultType="com.vedeng.flash.dto.temp.OneThreeMonthLastYearSaleNumTemp">
        select skuId, sum(num) as oneMonthPart
        from (
        SELECT T1.SKU_ID as skuId, T3.NUM as num
        FROM V_CORE_SKU T1
            join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
            join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
            join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
            left join T_R_TRADER_J_USER TT3 on TT3.TRADER_ID = T4.TRADER_ID
            left join T_USER TT4 on TT3.USER_ID = TT4.USER_ID
            left join T_R_USER_POSIT TT5 on TT4.USER_ID = TT5.USER_ID
            left join T_POSITION TT6 on TT5.POSITION_ID = TT6.POSITION_ID
            left join T_ORGANIZATION TT7 on TT6.ORG_ID = TT7.ORG_ID
        where T3.IS_DELETE = 0
            and (T4.STATUS = 1 or T4.STATUS = 2)
            and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
            and T4.IS_DELETE = 0
            and T4.ORDER_TYPE != 2
            and not exists(select 1 from T_AFTER_SALES where ORDER_ID = T4.SALEORDER_ID and TYPE = 539)
            and (TT7.ORG_ID = 39 or TT7.PARENT_ID = 39)
            and T1.SKU_ID in
                <foreach collection="skuIdList" item="item" open="(" close=")" separator=",">#{item,jdbcType=INTEGER}</foreach>
            and T4.SATISFY_DELIVERY_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 1 month)) *1000
            and T4.SATISFY_DELIVERY_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000

        union all

        SELECT T1.SKU_ID as skuId,
            case T5.ATFER_SALES_STATUS
            when 2 then T3.NUM - T6.NUM
            when 3 then T3.NUM
            else 0 end as num
        FROM V_CORE_SKU T1
            join V_CORE_SPU T2 on T1.SPU_ID = T2.SPU_ID
            join T_SALEORDER_GOODS T3 on T1.SKU_NO = T3.SKU
            join T_SALEORDER T4 on T3.SALEORDER_ID = T4.SALEORDER_ID
            left join T_AFTER_SALES T5 ON T4.SALEORDER_NO = T5.ORDER_NO
            left join T_AFTER_SALES_GOODS T6 on T5.AFTER_SALES_ID = T6.AFTER_SALES_ID
            left join T_R_TRADER_J_USER TT3 on TT3.TRADER_ID = T4.TRADER_ID
            left join T_USER TT4 on TT3.USER_ID = TT4.USER_ID
            left join T_R_USER_POSIT TT5 on TT4.USER_ID = TT5.USER_ID
            left join T_POSITION TT6 on TT5.POSITION_ID = TT6.POSITION_ID
            left join T_ORGANIZATION TT7 on TT6.ORG_ID = TT7.ORG_ID
        where T3.IS_DELETE = 0
            and (T4.STATUS = 1 or T4.STATUS = 2)
            and (T4.PAYMENT_STATUS = 1 or T4.PAYMENT_STATUS = 2)
            and T4.IS_DELETE = 0
            and T4.ORDER_TYPE != 2
            and T5.ATFER_SALES_STATUS in (2, 3)
            and T5.TYPE = 539
            and T6.GOODS_ID = T1.SKU_ID
            and (TT7.ORG_ID = 39 or TT7.PARENT_ID = 39)
            and T1.SKU_ID in
                <foreach collection="skuIdList" item="item" open="(" close=")" separator=",">#{item,jdbcType=INTEGER}</foreach>
            and T4.SATISFY_DELIVERY_TIME >= UNIX_TIMESTAMP(date_add(DATE_ADD(curdate(), interval -day(curdate()) + 1 day), interval - 1 month)) *1000
            and T4.SATISFY_DELIVERY_TIME &lt;= UNIX_TIMESTAMP(DATE_ADD(curdate(), interval -day(curdate()) + 1 day)) * 1000
        ) temp
        group by temp.skuId
    </select>
</mapper>
