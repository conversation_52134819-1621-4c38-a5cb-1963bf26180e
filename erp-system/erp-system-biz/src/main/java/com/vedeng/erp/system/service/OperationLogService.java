package com.vedeng.erp.system.service;

import com.vedeng.erp.system.domain.entity.OperationLogEntity;
public interface OperationLogService{

    int deleteByPrimaryKey(Long operationLogId);

    int insert(OperationLogEntity record);

    int insertSelective(OperationLogEntity record);

    OperationLogEntity selectByPrimaryKey(Long operationLogId);

    int updateByPrimaryKeySelective(OperationLogEntity record);

    int updateByPrimaryKey(OperationLogEntity record);



}
