package com.vedeng.flash.dto;

import com.vedeng.flash.model.RegularOperateLog;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2021/5/28 14:40
 */
@Data
public class RegularOperateLogDto extends RegularOperateLog {
    //生成备货单No
    private String saleorderNo;
    //操作时间
    private String operateTimeString;
    //备货数量
    private Integer buyNum;
    //操作子sku安全库存
    private String safeStockString;
    //操作子sku库存
    private String stockString;
    //操作子sku建议补充库存
    private String proposePrepareStockString;

}
