package com.vedeng.crm.business.quote.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.crm.business.quote.domain.dto.*;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoodsNoSku;
import com.vedeng.crm.business.quote.mapper.CrmQuoteorderGoodsMapper;
import com.vedeng.crm.business.quote.mapstruct.CrmQuoteGoodsConvertor;
import com.vedeng.crm.business.quote.service.CrmQuoteOrderGoodsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Service
@Slf4j
public class CrmQuoteOrderGoodsServiceImpl implements CrmQuoteOrderGoodsService {

    @Autowired
    public CrmQuoteorderGoodsMapper crmQuoteorderGoodsMapper;

    @Autowired
    public CrmQuoteGoodsConvertor crmQuoteGoodsConvertor;

    @Override
    public void deleteByPrimaryKey(Integer quoteorderGoodsId, CurrentUser currentUser) {
        CrmQuoteorderGoods deleteGoods = new CrmQuoteorderGoods();
        deleteGoods.setQuoteorderGoodsId(quoteorderGoodsId);
        deleteGoods.setIsDelete(1);
        deleteGoods.setModTime(System.currentTimeMillis());
        deleteGoods.setCreator(currentUser.getId());
        crmQuoteorderGoodsMapper.updateByPrimaryKeySelective(deleteGoods);
    }

    private String formatDeliveryCycle(String deliveryCycle){
        if (StringUtils.isBlank(deliveryCycle)){
            return "0";
        }
        // 将字符串转换为 BigDecimal
        BigDecimal deliveryCycleBigDecimal = new BigDecimal(deliveryCycle);

        // 向上取整
        BigDecimal roundedUpValue = deliveryCycleBigDecimal.setScale(0, RoundingMode.CEILING);

        return roundedUpValue.toPlainString();
    }

    @Override
    public CrmQuoteorderGoods insertSelectiveDto(QuoteGoodsInsertRequestDto record, CurrentUser currentUser) {
        long now = System.currentTimeMillis();
        CrmQuoteorderGoods crmQuoteorderGoods = new CrmQuoteorderGoods();
        crmQuoteorderGoods.setQuoteorderId(record.getQuoteorderId());
        crmQuoteorderGoods.setGoodsId(record.getGoodsId());
        crmQuoteorderGoods.setSku(record.getSku());
        crmQuoteorderGoods.setGoodsName(record.getGoodsName());
        crmQuoteorderGoods.setBrandName(record.getBrandName());
        crmQuoteorderGoods.setModel(record.getModel());
        crmQuoteorderGoods.setUnitName(record.getUnitName());
        crmQuoteorderGoods.setPrice(record.getPrice());
        crmQuoteorderGoods.setCurrencyUnitId(1);
        crmQuoteorderGoods.setNum(record.getNum());
        crmQuoteorderGoods.setDeliveryCycle(formatDeliveryCycle(record.getDeliveryCycle()));
        crmQuoteorderGoods.setAddTime(now);
        crmQuoteorderGoods.setCreator(currentUser.getId());
        crmQuoteorderGoods.setModTime(now);
        crmQuoteorderGoods.setUpdater(currentUser.getId());
        int i = crmQuoteorderGoodsMapper.insertSelective(crmQuoteorderGoods);
        if(record.getPrice()!=null  && record.getPrice().doubleValue()>0){
            log.info("需求模式添加商品时，初始化了一次价格");
        }else{
            crmQuoteorderGoodsMapper.updatePriceByPrimaryKey(crmQuoteorderGoods);
        }
        log.info("新增报价商品：{},结果：{}", JSON.toJSON(crmQuoteorderGoods),i);
        return crmQuoteorderGoods;
    }

    @Override
    public void updateSelective(CrmQuoteorderGoods crmQuoteorderGoods, CurrentUser currentUser) {
        int i = crmQuoteorderGoodsMapper.updateByPrimaryKeySelective(crmQuoteorderGoods);
        log.info("更新报价商品：{},结果：{}", JSON.toJSON(crmQuoteorderGoods),i);
    }

    @Override
    public int updateDeliveryCycle(QuoteGoodsUpdateLockDto updateRequestDto, CurrentUser currentUser) {
        updateRequestDto.setModTime(System.currentTimeMillis());
        updateRequestDto.setUpdater(currentUser.getId());
        int i = crmQuoteorderGoodsMapper.updateDeliveryCycle(updateRequestDto);
        return i;
    }

    @Override
    public int updatePrice(QuoteGoodsUpdateLockDto updateRequestDto, CurrentUser currentUser) {
        updateRequestDto.setModTime(System.currentTimeMillis());
        updateRequestDto.setUpdater(currentUser.getId());
        int i = crmQuoteorderGoodsMapper.updatePrice(updateRequestDto);
        return i;
    }

    @Override
    public int updateNum(QuoteGoodsUpdateLockDto updateRequestDto, CurrentUser currentUser) {
        updateRequestDto.setModTime(System.currentTimeMillis());
        updateRequestDto.setUpdater(currentUser.getId());
        int i = crmQuoteorderGoodsMapper.updateNum(updateRequestDto);
        return i;
    }

    @Override
    public int updateReportStatus(QuoteGoodsUpdateLockDto updateRequestDto, CurrentUser currentUser) {
        updateRequestDto.setModTime(System.currentTimeMillis());
        updateRequestDto.setUpdater(currentUser.getId());
        int i =  crmQuoteorderGoodsMapper.updateReportStatus(updateRequestDto);
        return i;
    }

    @Override
    public CrmQuoteorderGoods insertSelective(CrmQuoteorderGoods crmQuoteorderGoods, CurrentUser currentUser) {
        long now = System.currentTimeMillis();
        crmQuoteorderGoods.setAddTime(now);
        crmQuoteorderGoods.setCreator(currentUser.getId());
        crmQuoteorderGoods.setModTime(now);
        crmQuoteorderGoods.setUpdater(currentUser.getId());
        int i = crmQuoteorderGoodsMapper.insertSelective(crmQuoteorderGoods);
        if(crmQuoteorderGoods.getPrice() == null){
            crmQuoteorderGoodsMapper.updatePriceByPrimaryKey(crmQuoteorderGoods);
        }
        log.info("新增报价商品：{},结果：{}", JSON.toJSON(crmQuoteorderGoods),i);
        return crmQuoteorderGoods;
    }

    @Override
    public CrmQuoteorderGoods insertQuoteOrderGoodsNoSku(CrmQuoteorderGoodsNoSku crmQuoteorderGoods, CurrentUser currentUser) {
        long now = System.currentTimeMillis();
        crmQuoteorderGoods.setAddTime(now);
        crmQuoteorderGoods.setCreator(currentUser.getId());
        crmQuoteorderGoods.setModTime(now);
        crmQuoteorderGoods.setUpdater(currentUser.getId());
        int i = crmQuoteorderGoodsMapper.insertQuoteOrderGoodsNoSku(crmQuoteorderGoods);
        crmQuoteorderGoodsMapper.updatePriceByPrimaryKey(crmQuoteorderGoods);
        log.info("新增报价自定义商品：{},结果：{}", JSON.toJSON(crmQuoteorderGoods),i);
        return crmQuoteorderGoods;
    }

    @Override
    public CrmQuoteorderGoods updateQuoteOrderGoodsNoSku(CrmQuoteorderGoodsNoSku crmQuoteorderGoods, CurrentUser currentUser) {
        long now = System.currentTimeMillis();
        crmQuoteorderGoods.setAddTime(now);
        crmQuoteorderGoods.setCreator(currentUser.getId());
        crmQuoteorderGoods.setModTime(now);
        crmQuoteorderGoods.setUpdater(currentUser.getId());
        int i = crmQuoteorderGoodsMapper.updateQuoteOrderGoodsNoSku(crmQuoteorderGoods);
        log.info("更新报价自定义商品：{},结果：{}", JSON.toJSON(crmQuoteorderGoods),i);
        return crmQuoteorderGoods;
    }

    @Override
    public List<CrmQuoteorderGoods> selectByQuoteorderId(Integer quoteorderId) {
        List<CrmQuoteorderGoods> crmQuoteorderGoods = crmQuoteorderGoodsMapper.selectByQuoteorderId(quoteorderId);
        return crmQuoteorderGoods;
    }

    @Override
    public List<CrmQuoteorderGoods> selectByQuoteNeedsIdList(List<Long> quoteorderNeedsIdList){
        List<CrmQuoteorderGoods> crmQuoteorderGoods = crmQuoteorderGoodsMapper.selectByQuoteNeedsIdList(quoteorderNeedsIdList);
        return crmQuoteorderGoods;
    }

    @Override
    public void batchUpdateConsultationReport(ConsultationReportDto consultationReportDto,CurrentUser currentUser) {
        if (CollUtil.isEmpty(consultationReportDto.getQuoteorderGoodsIds())){
            log.info("咨询商品为空，不执行更新操作");
            return;
        }
        ConsultationReportUpdateEntity update = new ConsultationReportUpdateEntity();
        update.setQuoteorderGoodsIds(consultationReportDto.getQuoteorderGoodsIds());
        update.setIsConsulReport(consultationReportDto.getIsConsulReport());
        update.setIsConsulPrice(consultationReportDto.getIsConsulPrice());
        update.setIsConsulDeliveryCycle(consultationReportDto.getIsConsulDeliveryCycle());
        if(consultationReportDto.getIsConsulReport().equals(1)){//只有咨询了报备状态才更新报备
            update.setReportStatus(1);
        }
        update.setModTime(System.currentTimeMillis());
        update.setUpdater(currentUser.getId());
        crmQuoteorderGoodsMapper.batchUpdateConsultationReport(update);
    }

    @Override
    public CrmQuoteorderGoods selectById(Integer quoteorderGoodsId) {
        CrmQuoteorderGoods crmQuoteorderGoods = crmQuoteorderGoodsMapper.selectByPrimaryKey(quoteorderGoodsId);
        return crmQuoteorderGoods;
    }

    @Override
    public void cleanConsultationContent(CleanConsultationContentDto cleanConsultationContentDto) {
        log.info("完成咨询，清空咨询内容：{}", JSON.toJSON(cleanConsultationContentDto));
        crmQuoteorderGoodsMapper.cleanConsultationContent(cleanConsultationContentDto);
    }

}
