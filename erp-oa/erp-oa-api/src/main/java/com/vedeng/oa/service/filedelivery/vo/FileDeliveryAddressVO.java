package com.vedeng.oa.service.filedelivery.vo;

import java.util.Date;

import lombok.Data;

/**
 * 地址信息
 * <AUTHOR>
 *
 */
@Data
public class FileDeliveryAddressVO {

	/**ID*/
   private Integer id;

   /**申请人*/
   private Integer applyId;

   /**文件寄送ID*/
   private Integer fileDeliveryId;

   /**1客户2供应商3其他*/
   private Integer traderType;

   /**单位ID，对应TRADER_ID*/
   private Integer traderId;

   /**单位名称*/
   private String traderName;

   /**联系人填写方式*/
   private Integer contactType;

   /**联系人ID*/
   private Integer traderContactId;

   /**联系人名称*/
   private String traderContactName;

   /**联系人电话*/
   private String traderContactMobile;

   /**联系人地址ID*/
   private Integer traderContactAddressId;

   /**区域ID */
   private Integer areaId;
   
   /**市ID*/
   private Integer cityId;

   /**省ID*/
   private Integer provinceId;
   
   /**联系人地址详细信息（含省市区）*/
   private String traderContactAddress;
   
   /**联系人地址详细信息（不含省市区）*/
   private String traderContactAddressInfo;

   /**是否删除*/
   private Integer isDelete;

   /**添加时间*/
   private String addTime;

   /**修改时间*/
   private Date modTime;

   /**创建人*/
   private Integer creator;

   /**修改人*/
   private Integer updater;

   /**修改备注*/
   private String updateRemark;
   /**
    * 隐藏手机号
    */
   private String traderContactMobileHidden;

   /**快递单号*/
   private String logisticsNo;
   /**寄送状态*/
   private Integer deliveryStatus;

   private String expressLabelurl;

   /**更新的快递单号*/
   private String logisticsNoEdit;

}
