<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchVerifiesInfoDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchVerifiesInfoDto">
    <id column="VERIFIES_INFO_ID" jdbcType="INTEGER" property="verifiesInfoId" />
    <result column="RELATE_TABLE" jdbcType="VARCHAR" property="relateTable" />
    <result column="RELATE_TABLE_KEY" jdbcType="INTEGER" property="relateTableKey" />
    <result column="VERIFIES_TYPE" jdbcType="INTEGER" property="verifiesType" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    VERIFIES_INFO_ID,RELATE_TABLE,RELATE_TABLE_KEY,VERIFIES_TYPE,STATUS
  </sql>

  <select id="getStatusByRelateTableAndRelateTableKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_VERIFIES_INFO
    where RELATE_TABLE = #{relateTable,jdbcType=VARCHAR}
      and RELATE_TABLE_KEY = #{relateTableKey,jdbcType=INTEGER}
  </select>

  <select id="getBuyOrderContractVerifyInfo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_VERIFIES_INFO
    where RELATE_TABLE = #{relateTable,jdbcType=VARCHAR}
    and RELATE_TABLE_KEY = #{relateTableKey,jdbcType=INTEGER}
    and ADD_TIME = #{addTime,jdbcType=BIGINT}
    </select>
</mapper>