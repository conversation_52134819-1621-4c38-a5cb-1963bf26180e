package com.vedeng.oa.model;

import java.util.Date;

public class FileDeliveryAddress {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.APPLY_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer applyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.FILE_DELIVERY_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer fileDeliveryId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.TRADER_TYPE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer traderType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.TRADER_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer traderId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.TRADER_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String traderName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.CONTACT_TYPE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer contactType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer traderContactId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String traderContactName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_MOBILE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String traderContactMobile;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ADDRESS_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer traderContactAddressId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.AREA_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer areaId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String traderContactAddress;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.IS_DELETE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer isDelete;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.ADD_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Date addTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.MOD_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Date modTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.CREATOR
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.UPDATER
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer updater;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.UPDATE_REMARK
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String updateRemark;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.CITY_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer cityId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.PROVINCE_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer provinceId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.DELIVERY_STATUS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer deliveryStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.LOGISTICS_NO
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String logisticsNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ADDRESS_INFO
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String traderContactAddressInfo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.EXPRESS_LABELURL
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String expressLabelurl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_ADDRESS.LOGISTICS_NO_EDIT
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String logisticsNoEdit;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.ID
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.ID
     *
     * @param id the value for T_FILE_DELIVERY_ADDRESS.ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.APPLY_ID
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.APPLY_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getApplyId() {
        return applyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.APPLY_ID
     *
     * @param applyId the value for T_FILE_DELIVERY_ADDRESS.APPLY_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setApplyId(Integer applyId) {
        this.applyId = applyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.FILE_DELIVERY_ID
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.FILE_DELIVERY_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getFileDeliveryId() {
        return fileDeliveryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.FILE_DELIVERY_ID
     *
     * @param fileDeliveryId the value for T_FILE_DELIVERY_ADDRESS.FILE_DELIVERY_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setFileDeliveryId(Integer fileDeliveryId) {
        this.fileDeliveryId = fileDeliveryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_TYPE
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.TRADER_TYPE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getTraderType() {
        return traderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_TYPE
     *
     * @param traderType the value for T_FILE_DELIVERY_ADDRESS.TRADER_TYPE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setTraderType(Integer traderType) {
        this.traderType = traderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_ID
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.TRADER_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getTraderId() {
        return traderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_ID
     *
     * @param traderId the value for T_FILE_DELIVERY_ADDRESS.TRADER_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_NAME
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.TRADER_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getTraderName() {
        return traderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_NAME
     *
     * @param traderName the value for T_FILE_DELIVERY_ADDRESS.TRADER_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.CONTACT_TYPE
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.CONTACT_TYPE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getContactType() {
        return contactType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.CONTACT_TYPE
     *
     * @param contactType the value for T_FILE_DELIVERY_ADDRESS.CONTACT_TYPE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setContactType(Integer contactType) {
        this.contactType = contactType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ID
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getTraderContactId() {
        return traderContactId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ID
     *
     * @param traderContactId the value for T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setTraderContactId(Integer traderContactId) {
        this.traderContactId = traderContactId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_NAME
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getTraderContactName() {
        return traderContactName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_NAME
     *
     * @param traderContactName the value for T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setTraderContactName(String traderContactName) {
        this.traderContactName = traderContactName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_MOBILE
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_MOBILE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getTraderContactMobile() {
        return traderContactMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_MOBILE
     *
     * @param traderContactMobile the value for T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_MOBILE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setTraderContactMobile(String traderContactMobile) {
        this.traderContactMobile = traderContactMobile;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ADDRESS_ID
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ADDRESS_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getTraderContactAddressId() {
        return traderContactAddressId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ADDRESS_ID
     *
     * @param traderContactAddressId the value for T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ADDRESS_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setTraderContactAddressId(Integer traderContactAddressId) {
        this.traderContactAddressId = traderContactAddressId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.AREA_ID
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.AREA_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getAreaId() {
        return areaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.AREA_ID
     *
     * @param areaId the value for T_FILE_DELIVERY_ADDRESS.AREA_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ADDRESS
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getTraderContactAddress() {
        return traderContactAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ADDRESS
     *
     * @param traderContactAddress the value for T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setTraderContactAddress(String traderContactAddress) {
        this.traderContactAddress = traderContactAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.IS_DELETE
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.IS_DELETE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.IS_DELETE
     *
     * @param isDelete the value for T_FILE_DELIVERY_ADDRESS.IS_DELETE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.ADD_TIME
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.ADD_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.ADD_TIME
     *
     * @param addTime the value for T_FILE_DELIVERY_ADDRESS.ADD_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.MOD_TIME
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.MOD_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Date getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.MOD_TIME
     *
     * @param modTime the value for T_FILE_DELIVERY_ADDRESS.MOD_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.CREATOR
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.CREATOR
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.CREATOR
     *
     * @param creator the value for T_FILE_DELIVERY_ADDRESS.CREATOR
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.UPDATER
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.UPDATER
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.UPDATER
     *
     * @param updater the value for T_FILE_DELIVERY_ADDRESS.UPDATER
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.UPDATE_REMARK
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.UPDATE_REMARK
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getUpdateRemark() {
        return updateRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.UPDATE_REMARK
     *
     * @param updateRemark the value for T_FILE_DELIVERY_ADDRESS.UPDATE_REMARK
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setUpdateRemark(String updateRemark) {
        this.updateRemark = updateRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.CITY_ID
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.CITY_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getCityId() {
        return cityId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.CITY_ID
     *
     * @param cityId the value for T_FILE_DELIVERY_ADDRESS.CITY_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.PROVINCE_ID
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.PROVINCE_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getProvinceId() {
        return provinceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.PROVINCE_ID
     *
     * @param provinceId the value for T_FILE_DELIVERY_ADDRESS.PROVINCE_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setProvinceId(Integer provinceId) {
        this.provinceId = provinceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.DELIVERY_STATUS
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.DELIVERY_STATUS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.DELIVERY_STATUS
     *
     * @param deliveryStatus the value for T_FILE_DELIVERY_ADDRESS.DELIVERY_STATUS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.LOGISTICS_NO
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.LOGISTICS_NO
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getLogisticsNo() {
        return logisticsNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.LOGISTICS_NO
     *
     * @param logisticsNo the value for T_FILE_DELIVERY_ADDRESS.LOGISTICS_NO
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ADDRESS_INFO
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ADDRESS_INFO
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getTraderContactAddressInfo() {
        return traderContactAddressInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ADDRESS_INFO
     *
     * @param traderContactAddressInfo the value for T_FILE_DELIVERY_ADDRESS.TRADER_CONTACT_ADDRESS_INFO
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setTraderContactAddressInfo(String traderContactAddressInfo) {
        this.traderContactAddressInfo = traderContactAddressInfo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.EXPRESS_LABELURL
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.EXPRESS_LABELURL
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getExpressLabelurl() {
        return expressLabelurl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.EXPRESS_LABELURL
     *
     * @param expressLabelurl the value for T_FILE_DELIVERY_ADDRESS.EXPRESS_LABELURL
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setExpressLabelurl(String expressLabelurl) {
        this.expressLabelurl = expressLabelurl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_ADDRESS.LOGISTICS_NO_EDIT
     *
     * @return the value of T_FILE_DELIVERY_ADDRESS.LOGISTICS_NO_EDIT
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getLogisticsNoEdit() {
        return logisticsNoEdit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_ADDRESS.LOGISTICS_NO_EDIT
     *
     * @param logisticsNoEdit the value for T_FILE_DELIVERY_ADDRESS.LOGISTICS_NO_EDIT
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setLogisticsNoEdit(String logisticsNoEdit) {
        this.logisticsNoEdit = logisticsNoEdit;
    }
}