package com.newtask;

import com.newtask.util.CertificateImage2OssUtil;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName SkuCertificateImage2OssTask.java
 * @Description TODO 产品三证同步oss
 * @createTime 2021年01月11日 14:47:00
 */
@Component
@JobHandler(value = "SkuCertificateImage2OssTask")
public class SkuCertificateImage2OssTask extends AbstractJobHandler {

    public static Logger logger = LoggerFactory.getLogger(SkuCertificateImage2OssTask.class);

    @Resource
    private AttachmentMapper attachmentMapper;

    @Autowired
    private CertificateImage2OssUtil certificateImage2OssUtil;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        int start = 0;
        int size = 10;

        if (StringUtils.isNotBlank(param)){
            String[] array = param.split(",");
            start = Integer.parseInt(array[0].trim());
            if (array.length > 1){
                size = Integer.parseInt(array[1]);
            }
        }

        if (StringUtils.isNotBlank(param) && param.split(",").length == 1) {
            Attachment attachment = attachmentMapper.selectByPrimaryKey(start);
            if (attachment != null) {
                certificateImage2OssUtil.downloadFileByStream(attachment);
            }
        } else {
            //分页批量迁移
            int count = 1;
            while (count > 0){
                List<Attachment> list = attachmentMapper.getWmsQlaListHistory(start,size);
                count = list.size();
                for (int i = 0; i < list.size(); i++) {
                    certificateImage2OssUtil.downloadFileByStream(list.get(i));
                    if (i == list.size() - 1){
                        start = list.get(i).getAttachmentId();
                    }
                }
            }
        }
        return SUCCESS;
    }
}
