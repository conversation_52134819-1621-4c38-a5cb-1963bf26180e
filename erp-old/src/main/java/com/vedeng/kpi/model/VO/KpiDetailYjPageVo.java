package com.vedeng.kpi.model.VO;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

public class KpiDetailYjPageVo extends KpiDetailBasePageVo{

    private static final long serialVersionUID = -7923545266180398206L;

    private List<KpiLogDetailVo> saleOrderLogList;

    private List<KpiLogDetailVo> afterSaleLogList;

    private List<KpiLogDetailVo> sendAndNotPaid;

    private List<KpiLogDetailVo> paidNotKpi;

    private List<KpiDetailYjOverviewVo> hisMonthList;

    public List<KpiLogDetailVo> getSaleOrderLogList() {
        return saleOrderLogList;
    }

    public void setSaleOrderLogList(List<KpiLogDetailVo> saleOrderLogList) {
        this.saleOrderLogList = saleOrderLogList;
    }

    public List<KpiLogDetailVo> getAfterSaleLogList() {
        return afterSaleLogList;
    }

    public void setAfterSaleLogList(List<KpiLogDetailVo> afterSaleLogList) {
        this.afterSaleLogList = afterSaleLogList;
    }

    public List<KpiLogDetailVo> getSendAndNotPaid() {
        return sendAndNotPaid;
    }

    public void setSendAndNotPaid(List<KpiLogDetailVo> sendAndNotPaid) {
        this.sendAndNotPaid = sendAndNotPaid;
    }

    public List<KpiLogDetailVo> getPaidNotKpi() {
        return paidNotKpi;
    }

    public void setPaidNotKpi(List<KpiLogDetailVo> paidNotKpi) {
        this.paidNotKpi = paidNotKpi;
    }

    public List<KpiDetailYjOverviewVo> getHisMonthList() {
        return hisMonthList;
    }

    public void setHisMonthList(List<KpiDetailYjOverviewVo> hisMonthList) {
        this.hisMonthList = hisMonthList;
    }
}
