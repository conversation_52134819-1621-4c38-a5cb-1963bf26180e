package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.finance.dto.CapitalBillDetailDto;
import com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesDto;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.batch.dto.BatchTransactionVoucherDto;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchBankBillDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchCapitalBillDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchTransactionVoucherMapper;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeReceiveBillCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveBillEntity;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveBillDto;
import com.vedeng.erp.kingdee.enums.KingDeeBankBillFormIdAndContactUnitTypeEnum;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveBillCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveBillConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeReceiveBillRepository;
import com.vedeng.erp.kingdee.service.KingDeeReceiveBillApiService;
import com.vedeng.erp.kingdee.service.KingDeeReceiveBillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class KingDeeReceiveBillServiceImpl extends KingDeeBaseServiceImpl<KingDeeReceiveBillEntity, KingDeeReceiveBillDto,
        KingDeeReceiveBillCommand, KingDeeReceiveBillRepository, KingDeeReceiveBillConvertor,
        KingDeeReceiveBillCommandConvertor>
        implements KingDeeReceiveBillService, KingDeeReceiveBillApiService {

    @Autowired
    private BatchBankBillDtoMapper batchBankBillDtoMapper;

    @Autowired
    private BatchTransactionVoucherMapper batchTransactionVoucherMapper;
    @Autowired
    private BatchCapitalBillDtoMapper batchCapitalBillDtoMapper;
    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;

    @Override
    public void savePost(Object... objects) {
        KingDeeReceiveBillDto kingDeeReceiveBillDto = getD(objects);
        //更新流水表推送金蝶状态
        log.info("银行流水{},推送金蝶成功", kingDeeReceiveBillDto.getBankBillId());
        batchBankBillDtoMapper.updateKingdeePushedStatus(kingDeeReceiveBillDto.getBankBillId());
        //保存流水凭证表信息
        BatchTransactionVoucherDto batchTransactionVoucherDto = new BatchTransactionVoucherDto();
        batchTransactionVoucherDto.setBillNo(kingDeeReceiveBillDto.getFBillNo());
        batchTransactionVoucherDto.setTransactionType(ErpConstant.ONE);
        batchTransactionVoucherDto.setCreator(ErpConstant.ONE);
        batchTransactionVoucherDto.setCreatorName("admin");
        batchTransactionVoucherMapper.insertSelective(batchTransactionVoucherDto);
    }

    @Override
    public KingDeeBankBillFormIdAndContactUnitTypeEnum selectKingDeeBankBillEnumById(Integer bankBillId) {
        BatchBankBillDto batchBankBillDto = batchBankBillDtoMapper.selectByPrimaryKey(bankBillId);
        //未忽略(已结算)
        if (batchBankBillDto.getStatus().equals(ErpConstant.ZERO)){
            //支出(付款单、收款退款单)
            if (batchBankBillDto.getFlag1().equals(ErpConstant.ZERO)){
                return this.choosePayEnum(batchBankBillDto);
            }
            //收入(收款单、付款退款单)
            if (batchBankBillDto.getFlag1().equals(ErpConstant.ONE)){
                return this.chooseReceiveEnum(batchBankBillDto);
            }
        }
        //TODO 已忽略
        if (batchBankBillDto.getStatus().equals(ErpConstant.ONE)){

        }
        return null;
    }

    private KingDeeBankBillFormIdAndContactUnitTypeEnum choosePayEnum(BatchBankBillDto bankBillDto) {

        return null;
    }

    private KingDeeBankBillFormIdAndContactUnitTypeEnum chooseReceiveEnum(BatchBankBillDto bankBillDto) {
        //根据银行流水查询资金流水详情
        CapitalBillDetailDto capitalBillDetailDto = batchCapitalBillDtoMapper.getFirstDetailByBankBillId(bankBillDto.getBankBillId());
        if (ErpConstant.THREE.equals(capitalBillDetailDto.getOrderType())) {
            BatchAfterSalesDto afterSalesDto = batchAfterSalesDtoMapper.findByAfterSalesId(capitalBillDetailDto.getRelatedId());
            if (ErpConstant.PURCHASE.equals(afterSalesDto.getSubjectType())){
                return KingDeeBankBillFormIdAndContactUnitTypeEnum.PAY_BILL_SUPPLIER;
            }
        } else if (ErpConstant.FIVE.equals(capitalBillDetailDto.getOrderType())) {
            return KingDeeBankBillFormIdAndContactUnitTypeEnum.PAY_BILL_SUPPLIER;
        }
        return KingDeeBankBillFormIdAndContactUnitTypeEnum.RECIEVE_BILL_CUSTOMER;
    }
}
