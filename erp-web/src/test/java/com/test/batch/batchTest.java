package com.test.batch;

import com.test.BaseTestWithSpring;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import com.vedeng.erp.kingdee.service.KingDeeCustomerApiService;
import com.vedeng.erp.kingdee.service.KingDeeMaterialApiService;
import com.vedeng.erp.kingdee.service.KingDeeSupplierApiService;
import com.vedeng.erp.trader.service.TraderCustomerApiService;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import com.vedeng.goods.dto.KingDeeSkuInfoDto;
import com.vedeng.goods.service.GoodsApiService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/10/25 8:51
 */
public class batchTest extends BaseTestWithSpring {

    @Autowired
    private KingDeeMaterialApiService kingDeeMaterialApiService;
    @Autowired
    private GoodsApiService goodsApiService;
    @Autowired
    private KingDeeCustomerApiService kingDeeCustomerApiService;
    @Autowired
    private TraderCustomerApiService traderCustomerApiService;
    @Autowired
    private TraderSupplierApiService traderSupplierApiService;
    @Autowired
    private KingDeeSupplierApiService kingDeeSupplierApiService;




    @Test
    public void testSku() {
        String param = "";
        List<Integer> idList = Arrays.stream(param.split(",")).map(p -> Integer.parseInt(p.trim())).collect(Collectors.toList());
        idList.forEach(id -> {
            KingDeeSkuInfoDto skuInfoDto = goodsApiService.getSkuInfoBySkuId(id);
            if (skuInfoDto != null) {
                KingDeeMaterialDto kingDeeMaterialDto = goodsApiService.getPushSkuInfoToKingDee(skuInfoDto);
                kingDeeMaterialApiService.register(kingDeeMaterialDto);
            }
        });
    }



    @Test
    public void testCustomer() {
        String param = "58541,489993,30826,564590";
        List<Integer> idList = Arrays.stream(param.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        idList.forEach(id -> {
            KingDeeCustomerDto kingDeeCustomerInfo = traderCustomerApiService.getKingDeeCustomerInfo(id);
            kingDeeCustomerApiService.register(kingDeeCustomerInfo);
        });
    }

    @Test
    public void testSupplier() {
        String param = "8347,3040";
        List<Integer> idList = Arrays.stream(param.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        idList.forEach(id -> {
            KingDeeSupplierDto kingDeeSupplierInfo = traderSupplierApiService.getKingDeeSupplierInfo(id);
            kingDeeSupplierApiService.register(kingDeeSupplierInfo);
        });
    }
}
