package com.vedeng.orderrabbitmq.service;

import com.rabbitmq.client.Channel;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.goods.service.GoodsService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date created in 2020/10/13 13:10
 */
@Component
public class ErpPriceStockChangeConsumer extends AbstractMessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(ErpPriceStockChangeConsumer.class);

    @Value("${price.database}")
    private String priceDatabase;

    @Value("${stock.database}")
    private String stockDatabase;

    @Autowired
    private GoodsService goodsService;

    @Override
    public void doBusiness(Message message, Channel channel){
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            JSONObject jsonObject = JSONObject.fromObject(messageBody);
            boolean isDdl = jsonObject.getBoolean("isDdl");
            JSONArray data = jsonObject.getJSONArray("data");
            List<String> skuList2Update = new ArrayList<>();
            if (!isDdl && data.size() > 0){
                String database = jsonObject.getString("database");
                if (stockDatabase.equalsIgnoreCase(database)){
                    for (Object datum : data) {
                        JSONObject item = JSONObject.fromObject(datum);
                        skuList2Update.add(item.getString("SKU"));
                    }
                    if (skuList2Update.size() > 0){
                        LOGGER.info("库存中心数据变动，涉及sku：{}", com.alibaba.fastjson.JSONObject.toJSONString(skuList2Update));
                        goodsService.updateStockInfoOfSku(skuList2Update);
                    }
                } else if (priceDatabase.equalsIgnoreCase(database)){
                    for (Object datum : data) {
                        JSONObject item = JSONObject.fromObject(datum);
                        skuList2Update.add("V" + item.getInt("sku_id"));
                    }
                    if (skuList2Update.size() > 0){
                        LOGGER.info("价格中心数据变动，涉及sku：{}", com.alibaba.fastjson.JSONObject.toJSONString(skuList2Update));
                        goodsService.updatePriceInfoOfSku(skuList2Update);
                    }
                }
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
        } catch (Exception e){
            LOGGER.error("价格中心/库存中心数据变动canal推送的数据：{}，错误信息：",messageBody,e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,false);
            } catch (IOException ex) {
                LOGGER.error("价格中心/库存中心数据变动canal推送的数据，消息消费失败，将消息返回给rabbitmq错误：",ex);
            }
        }

    }

}
