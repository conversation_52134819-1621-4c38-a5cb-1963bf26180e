package com.vedeng.goods.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.goods.dto.BrandFrontDto;
import com.vedeng.goods.service.BrandApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/goodsBandApi")
@Slf4j
@ExceptionController
public class GoodsBandApi {
    @Autowired
    private BrandApiService brandApiService;

    /**
     * 查询品牌
     * @return
     */
    @RequestMapping("/queryBrand")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<List<BrandFrontDto>> queryBrand(String brandName, @RequestParam(defaultValue = "20") Integer limit) {

        return R.success(brandApiService.queryBrand(brandName,limit));
    }

    /**
     * 查询交易品牌下拉框
     * @return
     */
    @RequestMapping("/queryTransactionBrandDropdown")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<List<BrandFrontDto>> queryTransactionBrandDropdown() {
        return R.success(brandApiService.queryTransactionBrandDropdown());
    }

    @RequestMapping("/getBrand")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<List<BrandFrontDto>> getBrand(@RequestBody List<Integer> brandIds) {
        return R.success(brandApiService.getBrand(brandIds));
    }

}
