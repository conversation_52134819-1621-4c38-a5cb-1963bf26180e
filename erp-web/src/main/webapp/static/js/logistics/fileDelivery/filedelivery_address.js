layui.use(function(){
    let layer = layui.layer;
    let form=layui.form;
    let table=layui.table;
    form.on('radio(deliveryType)', function(data){
        var elem = data.elem; // 获得 radio 原始 DOM 对象
        var checked = elem.checked; // 获得 radio 选中状态
        var value = elem.value; // 获得 radio 值
        var othis = data.othis; // 获得 radio 元素被替换后的 jQuery 对象
        changeDept();
        //   layer.msg(['value: '+ value, 'checked: '+ checked].join('<br>'));
    });
})