<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/manage.css" />

<style>
    .mt0 {
        margin-top: 0;
        margin-left: 20px;
    }

    .pt0 {
        padding-top: 0;
        padding-bottom: 0;
    }
    
    .f_left {
        float: left;
    }

    ul, ol {
        list-style: none outside none;
    }
    ul, li {
        position: relative;
        margin-bottom: 0;
    }
    ul:after, li:after {
        display: block;
        content: '';
        clear: both;
    }
    .parts .table-style11 ul li,
    .parts .table-style11 ul {
        margin-bottom: 0;
    }
    .replaceThead ul {
        border: 1px solid #ddd;
        background: #e5e5e5;
    }

    .replaceThead ul li {
        border-right: 1px solid #ddd;
        float: left;
        height: 29px;
        line-height: 26px;
        text-align: center;
    }

    .replaceThead ul li:last-child {
        border-right: none;
    }

    .input-larger,
    .table input.input-larger {
        width: 298px;
    }

    a, a:hover, a:focus {
        text-decoration: none;
        outline-style: none;
        color: rgb(51, 132, 239);
        cursor: pointer;
    }
    .line {
        color: #606266;
        font-size: 14px;
    }
    
    ul {
        padding-inline-start: 2px;
    }

    .line:after, .linedown:after {
        content: "";
        width: 98%;
        margin: 0 auto;
        display: block;
        height: 0;
        clear: both;
    }
    .el-form-item{
        margin-bottom: 16px;
    }
</style>

<div id="app" style="display: none;">

    <div class="formpublic formpublic1 pt0">
        <div class="line">
            <ul>
                <li>
                    <div class="infor_name mt0">
                        <span>*</span>
                        <label>线索类型:</label>
                    </div>
                    <div class="f_left  ">
                        <ul style="width:688px">
                            <li style="float: left;margin: 0 10px 4px 0;">
                                <c:if test="${clueType eq 394}">
                                    <input type="hidden" id="clueType" name="clueType" value="394">
                                    <label>自主询价</label>
                                </c:if>
                                <c:if test="${clueType eq 391}">
                                    <input type="hidden" id="clueType" name="clueType" value="391">
                                    <label>总机询价</label>
                                </c:if>
                            </li>
                        </ul>
                        
                    </div>
                </li>
                
                <li>
                    <div class="infor_name mt0">
                        <span>*</span>
                        <label>询价行为:</label>
                    </div>
                    <div class="f_left  ">
                        <ul id="ul_inquire" style="width:688px">
                            <c:if test="${not empty inquiryData }">
                                <c:forEach items="${inquiryData}" var ="sl">
                                    <li style="float: left;margin: 0 10px 4px 0;">
                                        <input type="radio" name="inquiry" 
                                               <c:if test="${sl.sysOptionDefinitionId eq businessLeadsDto.inquiry }">checked="checked"</c:if>
                                               value="${sl.sysOptionDefinitionId}"><label>${sl.title}</label>
                                    </li>
                                </c:forEach>
                            </c:if>

                        </ul>
                    </div>
                </li>
                
                <li>
                    <div class="infor_name mt0">
                        <span>*</span>
                        <label>渠道类型:</label>
                    </div>
                    <div class="f_left  ">
                        <ul id="ul_source" style="width:900px">
                            <c:if test="${not empty bussSource }">
                                <c:forEach items="${bussSource}" var ="sl">
                                    <li style="float: left;margin: 0 10px 4px 0;">
                                        <input type="radio" name="source" onclick="changeData('source')" 
                                               <c:if test="${sl.sysOptionDefinitionId eq bussSource[0].sysOptionDefinitionId }">checked="checked"</c:if>
                                               <c:if test="${sl.sysOptionDefinitionId eq businessLeadsDto.source }">checked="checked"</c:if>
                                               value="${sl.sysOptionDefinitionId}"><label>${sl.title}</label>
                                    </li>
                                </c:forEach>
                            </c:if>
                        </ul>
                        
                    </div>
                </li>
                
                <li>
                    <div class="infor_name mt0">
                        <span>*</span>
                        <label>渠道名称:</label>
                    </div>
                    <div class="f_left  ">
                        <ul id="ul_inquiry" style="width:688px">
                            <c:if test="${not empty communications }">
                                <c:forEach items="${communications}" var ="il">
                                    <li style="float: left;margin: 0 10px 4px 0;">
                                        <input type="radio" name="communication" 
                                               <c:if test="${il.sysOptionDefinitionId eq communications[0].sysOptionDefinitionId}">checked="checked"</c:if>
                                               <c:if test="${il.sysOptionDefinitionId eq businessLeadsDto.communication}">checked="checked"</c:if>
                                               value="${il.sysOptionDefinitionId}"><label>${il.title}</label>
                                    </li>
                                </c:forEach>
                            </c:if>
                        </ul>
                        
                    </div>
                </li>

                <li id="productDiv_0">
                    <div class="infor_name mt0" id="productTitleDiv_0" idFlag="0">
                        <span>*</span>
                        <label>三级分类:</label>
                    </div>
                    <div  class="el-input el-input--mini" style="width: 340px;">
                        <input class="el-input__inner"  id="content_0" style="display: inline-block;width: 300px;"/>
                        <a onclick="deleteCategoryName(0)" style="display: inline-block;">&nbsp;&nbsp;删除</a>
                    </div>
                </li>
                <input type="hidden" id="productCountFlag" value="0">
                <input  id="content" name="content" type="hidden">

                <li>
                    <div class="infor_name">
                    </div>
                    <div class="f_left  ">
                        <a style="margin-left:20px" class="pop-new-data" layerparams='{"width":"80%","height":"788px","title":"","link":"/order/bussinesschance/addBussinessChanceProduct.do"}'>
                            + 添加
                        </a>
                    </div>
                    <input id="contentVa" value="${businessLeadsDto.content}" type="hidden">
                </li>
            </ul>
        </div>
    </div>

    <el-form :rules="rules" :label-position="labelPosition" label-width="150px" ref="form" :model="businessLeads"
             style="width: 800px;">
        <%--        不能删除 保证焦点事件问题--%>
        <el-select style="display: none">
        </el-select>


        <el-row :gutter="20">
            <el-col :span="24">

                <el-form-item label="产品信息：" prop="goodsInfo">
                    <el-input type="textarea"
                              :rows="1"
                              autosize
                              maxlength="500"
                              size="mini"
                              v-model="businessLeads.goodsInfo" style="width: 600px;"></el-input>
                </el-form-item>

                <el-form-item label="备注：" prop="remark">
                    <el-input type="textarea" v-model="businessLeads.remark"
                              :rows="1"
                              autosize
                              maxlength="500"
                              style="width: 600px;"></el-input>
                </el-form-item>
                
                <el-form-item label="客户名称：" prop="traderName">
                    <el-select
                            v-model="businessLeads.traderName"
                            filterable
                            clearable
                            @clear="clearBusinessLeadsTraderInfo"
                            remote
                            size="mini"
                            reserve-keyword
                            placeholder=""
                            :remote-method="remoteMethod"
                            :loading="loading"
                            @blur="selectBlur($event)"
                            style="width: 300px;"
                    >
                        <el-option
                                v-for="item in options"
                                :key="item.traderId"
                                :label="item.traderName"
                                :value="item.traderName"
                        <%--    :disabled="!item.belong" --%>
                                @click.native="clickTrader(item)"
                        >
                            <span style="float: left">{{ item.traderName }}</span>
                            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.saleName }}</span>
                        </el-option>
                    </el-select>


                    <el-button type="text" @click="skyEyes">
                        <img src="${pageContext.request.contextPath}/static/vue/images/businessServices.png">
                        <span>天眼查</span>
                    </el-button>


                    <el-button type="text" @click="addTrader">
                        <span>新增客户</span>
                    </el-button>

                </el-form-item>

                <el-form-item label="联系人：" prop="contact">
                    <el-input v-model="businessLeads.contact" size="mini" label-width="50px" style="width: 300px;"></el-input>

                    <el-button type="text" @click="matchContract">
                        <img src="${pageContext.request.contextPath}/static/vue/images/listMatch.png">
                        <span>匹配联系人</span>
                    </el-button>

                </el-form-item>

                <el-form-item label="手机：" prop="phone">
                    <el-input v-model="businessLeads.phone" size="mini" label-width="50px" style="width: 300px;"></el-input>
                    <el-button type="text" @click="repeatCheck">
                        <img src="${pageContext.request.contextPath}/static/vue/images/repeatAlarm.png">
                        <span>重复</span>
                    </el-button>
                </el-form-item>

                <div style="display: flex; align-items: center;">
                <el-form-item label="电话：" prop="telephone">
                    <el-input v-model="businessLeads.telephone" size="mini" label-width="50px" style="width: 300px;"></el-input>
                </el-form-item>

                <el-form-item   label="其他联系方式："  prop="otherContactInfo">
                    <el-input v-model="businessLeads.otherContactInfo"  size="mini"  label-width="50px" style="width: 300px;"></el-input>
                </el-form-item>
                </div>
                
                <el-form-item label="地区：" required>
                    <el-cascader size="mini"
                            ref="cascader"
                            :options="areaOptions"
                            :props="{ checkStrictly: true }"
                            style="width: 300px;"
                            v-model="areaSelectedOptions"
                            @change="getCheckedNodes"
                            filterable
                            clearable></el-cascader>

                    <el-input v-model="businessLeads.address"  size="mini"  label-width="50px"
                              placeholder="请填写详细地址，最长50个字符"
                              maxlength="50"
                              style="width: 300px;"></el-input>
                </el-form-item>

                <el-form-item label="跟进图片上传：" style="width: 1200px;">
                    <el-upload size="mini"
                            action="/vgoods/operate/fileUploadImg.do"
                            list-type="picture-card"
                            :on-preview="handlePictureCardPreview"
                            :on-success="handleAvatarSuccess"
                            :on-remove="handleRemove"
                            :before-upload="beforeAvatarUpload"
                            accept="image/jpg,image/jpeg,image/png"
                            :file-list="fileList"
                    >
                        <i class="el-icon-plus"></i>
                    </el-upload>
                    <el-dialog :visible.sync="pictureDialogVisible">
                        <img width="100%" :src="businessLeads.followPic" alt="">
                    </el-dialog>
                </el-form-item>


                <el-row>
                    <el-col :span="6">
                        <el-form-item label="线索分级：" style="width: 300px;">
                            <el-select v-model="businessLeads.status"  size="mini"  placeholder="请选择分级">
                                <el-option label="有效线索" :value="1"></el-option>
                                <el-option label="无效线索" :value="0"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item v-if="businessLeads.status!=='' && 0==businessLeads.status" style="width: 550px;"
                                      prop="invalidReason">
                            <el-input v-model="businessLeads.invalidReason"  size="mini"  placeholder="请填写无效的原因"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>


<%--                <el-form-item label="线索标签：">--%>
<%--                    <span slot="label">--%>
<%--                       <el-tooltip content="最多同时选择三个标签" placement="top">--%>
<%--                            <i class="el-icon-question"></i>--%>
<%--                        </el-tooltip>线索标签：--%>
<%--                    </span>--%>
<%--                    <el-checkbox-group v-model="businessLeads.tagIdList" :max="3">--%>
<%--                        <el-checkbox v-for="(item,inedx) in leadsTagList" :label="item.id" :key="inedx">--%>
<%--                            <el-tag :style="{color: item.cssClass,borderColor:item.cssClass}" effect="plain">--%>
<%--                                {{item.name}}--%>
<%--                            </el-tag>--%>
<%--                        </el-checkbox>--%>
<%--                    </el-checkbox-group>--%>
<%--                </el-form-item>--%>
            </el-col>
        </el-row>

        <el-row v-if="!leadsInfo.leadsId">
            <el-col :span="12">

                <el-form-item v-if="displaySelectOp && (businessLeads.traderId ==null|| businessLeads.traderId == 0)"
                              label="线索归属人：" prop="belongerId">
                    <el-select v-model="businessLeads.belongerId"  size="mini" filterable placeholder="请选择线索归属人" @change="chooseBelonger">
                        <el-option
                                v-for="item in belongerOptions"
                                :key="item.userId"
                                size="mini"
                                :label="item.username"
<%--                                @click.native="chooseBelonger(item)"--%>
                                :value="item.userId">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item v-else label="线索归属人：" prop="belongerId">
                    {{businessLeads.belonger}}
                </el-form-item>
            </el-col>
        </el-row>

        <el-row v-if="leadsInfo.leadsId">

<%--            <div class=".aaa" id="aaa">--%>
<%--                <el-collapse v-model="activeNames">--%>
<%--                    <el-collapse-item name="1">--%>
                        <el-row>
                            <el-form-item label="线索来源：">
                                销售自拓线索
                            </el-form-item>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item
                                        v-if="displaySelectOp && (businessLeads.traderId ==null|| businessLeads.traderId == 0)&&businessLeads.followStatus==1"
                                        label="线索归属人：" prop="belongerId">
                                    <el-select v-model="businessLeads.belongerId"  size="mini"  filterable placeholder="请选择线索归属人"  @change="chooseBelonger">
                                        <el-option
                                                v-if="!belongerOptions.some(option => option.userId === businessLeads.belongerId)"
                                                :key="businessLeads.belongerId"
                                                :value="businessLeads.belongerId"
                                                :label="businessLeads.belonger + ' (已离职)'">
                                        </el-option>
                                        <el-option
                                                v-for="item in belongerOptions"
                                                :key="item.userId"
                                                :label="item.username"
<%--                                                @click.native="chooseBelonger(item)"--%>
                                                :value="item.userId">
                                        </el-option>
                                    </el-select>
                                </el-form-item>

                                <el-form-item v-else label="线索归属人：" prop="belongerId">
                                    {{businessLeads.belonger}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="线索创建人：">
                                    {{businessLeads.creatorName}}
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="12">
                                <el-form-item label="线索创建时间：">
                                    {{parseTime(businessLeads.addTime, '{y}-{m}-{d} {h}:{i}')}}
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="首次跟进时间：">
                                    {{parseTime(businessLeads.firstFollowTime, '{y}-{m}-{d} {h}:{i}')}}
                                </el-form-item>
                            </el-col>
                        </el-row>
<%--                    </el-collapse-item>--%>
<%--                </el-collapse>--%>
<%--            </div>--%>


        </el-row>


        <el-form-item>
            <el-button @click="closeThis">取消</el-button>
            <el-button :disabled="submitDisable" type="primary" @click="onSubmit('form')">确定</el-button>
        </el-form-item>


    </el-form>


    <el-dialog title="匹配联系人" :visible.sync="dialogTableVisible" :append-to-body="true">
        <div style="display: inline-flex;width: 100%;text-align: center">
            <div style="width: 60%">
                <el-input v-model="matchContractSubmitParm" placeholder="请输入姓名、手机、电话等关键信息"></el-input>
            </div>
            <div>
                <el-button type="primary" @click="matchContractSubmit">查询</el-button>
            </div>
        </div>
        <el-table :data="traderConcatDatas">
            <el-table-column property="name" label="姓名" width="150"></el-table-column>
            <el-table-column property="mobile" label="手机" width="200"></el-table-column>
            <el-table-column property="telephone" label="电话"></el-table-column>
            <el-table-column property="department" label="部门" width="150"></el-table-column>
            <el-table-column
                    fixed="right"
                    label="操作"
                    width="100">
                <template slot-scope="scope">
                    <el-button @click="chooseTraderContract(scope.row)" type="text" size="small">选择</el-button>
                </template>
            </el-table-column>
        </el-table>
    </el-dialog>

    <el-dialog title="匹配联系人" :visible.sync="emptyDialogTableVisible" :append-to-body="true">
        <el-empty
                v-show="!(!(traderConcatDatas == undefined ||traderConcatDatas == null || traderConcatDatas.length <= 0))"
                description="此客户暂无维护联系人信息">

            <span type="primary" class="title-click  bg-light-bule pop-new-data bt-small mr10 bt-bg-style J-add-prod"
                  id="concat" onclick="addTraderContract();todo(false)"
                  style="margin-left:24px;color: #3384ef;padding-right: 10px;cursor: pointer;height: 34px;line-height: 34px;"
                  :layerParams="'{&quot;width&quot;:&quot;50%&quot;,&quot;height&quot;:&quot;75%&quot;,&quot;title&quot;:&quot;新增联系人&quot;,&quot;link&quot;:&quot;/orderstream/saleorder/addContact.do?indexId=3&amp;traderId='+businessLeads.traderId+'&quot;}'"><span
            >添加联系人</span></span>
        </el-empty>
    </el-dialog>


    <el-dialog title="线索查重" :visible.sync="leadsCheckDuplicatesVisible" :append-to-body="true">
        <div style="display: inline-flex;width: 100%;text-align: center">
            <div style="width: 60%">
                <el-input v-model="businessLeads.mergeFiled" placeholder="请输入手机或电话"></el-input>
            </div>
            <div>
                <el-button type="primary" @click="leadsCheckDuplicatesSubmit">查询</el-button>
            </div>
        </div>
        <el-table :data="businessLeadsList">
            <el-table-column property="contact" label="客户联系人"></el-table-column>
            <el-table-column label="手机/电话" width="250">
                <template slot-scope="scope">
                    <span>{{ scope.row.phone }}</span>
                    <span>/</span>
                    <span>{{ scope.row.telephone }}</span>
                </template>
            </el-table-column>
            <el-table-column property="traderName" label="客户名称"></el-table-column>
            <el-table-column property="belonger" label="线索归属"></el-table-column>
            <el-table-column label="跟进状态">
                <template slot-scope="scope">
                    <span v-if="scope.row.followStatus === 1" style="color: #f61c1c">未处理</span>
                    <span v-if="scope.row.followStatus === 2" style="color: #16780d">跟进中</span>
                    <span v-if="scope.row.followStatus === 3" style="color: #c2c2c2">已关闭</span>
                    <span v-if="scope.row.followStatus === 4" style="color: #d58c0f">已商机</span>
                </template>
            </el-table-column>
            <el-table-column property="addTime" :formatter="formatDate" label="创建日期"></el-table-column>
        </el-table>
    </el-dialog>


</div>

<script src="${pageContext.request.contextPath}/static/api/trader/businessLeads.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/communicateRecord.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/traderContact.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomer.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/system/customTag.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/system/region.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/system/user.js?rnd=${resourceVersionKey}"></script>

<script src="${pageContext.request.contextPath}/static/js/order/bussinesschance/add_business_leads.js?rnd=${resourceVersionKey}"></script>


<script type="text/javascript">

    const leadsInfo = {
        creatername: '${creatername}',
        creater: '${creater}',
        leadsId: ${leadsId},
    }
    const callInTraderId= ${callInTraderId};

    new Vue({
        el: '#app',

        data() {
            return {
                // firstOpen: true,
                //item右对齐
                labelPosition: 'right',
                //表单校验
                rules: {
                    traderName: [
                        {max: 50, message: '最大支持输入50个汉字', trigger: 'blur'}
                    ],
                    // contact: [
                    //     {required: true, pattern: /[^\s]/, message: '请输入联系人', trigger: 'blur'},
                    //     {max: 10, message: '最大支持输入10个汉字', trigger: 'blur'}
                    // ],
                    phone: [
                        {
                            pattern: /^1([38][0-9]|4[014-9]|[59][0-35-9]|6[2567]|7[0-8])\d{8}$/,
                            message: '请输入合法手机号',
                            trigger: 'blur'
                        }
                    ],
                    telephone: [
                        {
                            pattern: /^(0[1,2]{1}\d{1}-?\d{8})|(0[3-9]{1}\d{2}-?\d{7})/,
                            message: '请输入合法电话号码',
                            trigger: 'blur'
                        }
                    ],
                    goodsInfo: [
                        {required: true, pattern: /[^\s]/, message: '请输入产品信息', trigger: 'blur'}
                    ],
                    invalidReason: [
                        {required: true, pattern: /[^\s]/, message: '请填写无效的原因', trigger: 'blur'}
                    ],
                    belongerId: [
                        {required: true, message: '请选择线索归属人', trigger: 'blur'}
                    ],
                    creater: [
                        {required: true, message: '请选择线索创建人', trigger: 'blur'}
                    ],
                    otherContactInfo: [
                        {max: 100, message: '最大支持输入100个字符', trigger: 'blur'}
                    ],
                },
                //表单
                businessLeads: {
                    id: '',
                    traderId: 0,
                    traderName: '',
                    contact: '',
                    phone: '',
                    telephone: '',
                    goodsInfo: '',
                    remark: '',
                    followStatus: 1,
                    followPicList: [],
                    type: 1,
                    status: 1,
                    invalidReason: '',
                    tagIdList: [],
                    source: '',
                    provinceId: '',
                    cityId: '',
                    countyId: '',
                    province: '',
                    city: '',
                    county: '',
                    address: '',
                    belongerId: '',
                    belonger: '',
                    traderContactId: '',
                    mergeFiled: '',
                    addTime: '',
                    otherContactInfo: '',
                },
                loading: false,
                options: [],
                //图片展示
                pictureDialogVisible: false,
                fileList: [],
                //自定义标签值
                leadsTagList: [],

                //线索归属人选择
                belongerOptions: [],
                displaySelectOp: true,
                //地区三级联动
                areaOptions: [],
                areaSelectedOptions: [],

                //折叠面板
                activeNames: [],

                //客户联系人
                traderConcatDatas: [],
                dialogTableVisible: false,
                emptyDialogTableVisible: false,
                matchContractSubmitParm: '',

                //线索查重
                leadsCheckDuplicatesVisible: false,
                businessLeadsList: [],
                submitDisable: false,
            };
        },

        created() {
            //渲染归属数据
            sendThis(this);
            getAllNotDisabledUserList()
                .then(res => {
                    this.belongerOptions = res.data.data;
                    if (res.data.data.length === 1) {
                        this.businessLeads.belongerId = leadsInfo.creater;
                        this.businessLeads.belonger = leadsInfo.creatername;
                        this.displaySelectOp = false

                    }
                });

            //渲染编辑页数据
            if (leadsInfo.leadsId !== 0) {
                //获取线索信息
                getLeadsDetails({"id": leadsInfo.leadsId}).then(res => {
                    this.businessLeads = res.data.data;
                    //图片回显
                    if (this.businessLeads.followPicList) {
                        for (let i = 0; i < this.businessLeads.followPicList.length; i++) {
                            let image = {
                                "name": "",
                                "url": ""
                            };
                            image.url = this.businessLeads.followPicList[i];
                            this.fileList.push(image);
                        }
                    }
                    return getCascaderRegionOptions();
                }).then(res => {
                    this.areaOptions = res.data.data;
                    if (this.businessLeads.provinceId !== 0) {
                        this.areaSelectedOptions.push(this.businessLeads.provinceId);
                        if (this.businessLeads.cityId !== 0) {
                            this.areaSelectedOptions.push(this.businessLeads.cityId);
                            if (this.businessLeads.countyId !== 0) {
                                this.areaSelectedOptions.push(this.businessLeads.countyId);
                            }
                        }
                    }
                });
            }

            // 线索标签
            getBusinessTags({"type": "1"}).then((result) => {
                this.leadsTagList = result.data.data;
                if (this.businessLeads.tagIdList.length > 0 && this.leadsTagList.length > 0) {
                    let arry = [];
                    this.businessLeads.tagIdList.forEach(c => {
                        if (this.leadsTagList.find(i => i.id == c)) {
                            arry.push(c);
                        }
                    });

                    this.businessLeads.tagIdList = arry;
                }

            });

            getCascaderRegionOptions().then(res => {
                this.areaOptions = res.data.data;
                // if (this.businessLeads.provinceId !== 0) {
                //     this.areaSelectedOptions.push(this.businessLeads.provinceId);
                //     if (this.businessLeads.cityId !== 0) {
                //         this.areaSelectedOptions.push(this.businessLeads.cityId);
                //         if (this.businessLeads.countyId !== 0) {
                //             this.areaSelectedOptions.push(this.businessLeads.countyId);
                //         }
                //     }
                // }
            });

            if(callInTraderId > 0){
                getCustomerBaseInfo({"traderId": callInTraderId}).then(res => {
                    if (res.data.code == 0) {
                        this.businessLeads.traderName = res.data.data.traderName;
                        this.businessLeads.traderId = res.data.data.traderId;
                        this.businessLeads.belongerId = res.data.data.saleId;
                        this.businessLeads.belonger = res.data.data.saleName;
                        console.log(this.businessLeads);
                    }
                });
            }

        },

        mounted() {
            loadingApp()
        },

        computed: {
            // 计算属性的 getter
            traderConcatDatasStatus: function () {
                // `this` 指向 vm 实例
                return this.traderConcatDatas == undefined || this.traderConcatDatas == null || this.traderConcatDatas.length <= 0
            }
        },

        watch: {
            dialogTableVisible: function (val) {
                this.matchContractSubmitParm = '';
            },
            leadsCheckDuplicatesVisible: function (val) {
                this.businessLeads.mergeFiled = '';
            },

        },

        methods: {
            //提交表单
            onSubmit(form) {

                if (this.checkClueRule()) {
                    return
                }
                
                if (this.checkPhoneAndTel()) {
                    return
                }
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        this.submitDisable = true;
                        let followPicListNew = []
                        if (this.businessLeads.followPicList.length > 0) {
                            for (let i = 0; i < this.businessLeads.followPicList.length; i++) {
                                let indexNum = this.businessLeads.followPicList[i].indexOf("/file/display");
                                followPicListNew.push(this.businessLeads.followPicList[i].substr(indexNum));
                            }
                            this.businessLeads.followPicList = followPicListNew;
                        }

                        this.businessLeads.clueType = $('#clueType').val();
                        this.businessLeads.inquiry = $('input:radio[name="inquiry"]:checked').val();
                        this.businessLeads.source = $('input:radio[name="source"]:checked').val();
                        this.businessLeads.communication = $('input:radio[name="communication"]:checked').val();
                        this.businessLeads.content = $('#content').val();

                        if (leadsInfo.leadsId) {
                            updateLeads(this.businessLeads).then(res => {
                                if (res.data.code === 0) {
                                    openTab("线索详情", '/businessLeads/details.do?id=' + leadsInfo.leadsId)
                                    this.closeThis()
                                } else {
                                    this.$message.error(res.data.message);
                                    setTimeout(() =>{
                                        this.submitDisable = false;
                                    },1000)
                                }
                            })
                        } else {
                            add(this.businessLeads).then(res => {
                                if (res.data.code === 0) {
                                    let leadsId = res.data.data;
                                    window.location='/businessLeads/details.do?id=' + leadsId;
                                    // openTab("线索详情", '/businessLeads/details.do?id=' + leadsId);
                                    // this.closeThis();
                                } else {
                                    this.$message.error(res.data.message);
                                    setTimeout(() =>{
                                        this.submitDisable = false;
                                    },1000)
                                }
                            })
                        }

                    }
                })
            },
            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },
            //检验手机和电话
            checkPhoneAndTel() {
                if (this.businessLeads.phone === '' && this.businessLeads.telephone === '' && this.businessLeads.otherContactInfo === '') {
                    this.$message.error('手机、电话、其他联系方式三者其一必填');
                    return true;
                }
                if (this.businessLeads.province === '' && this.businessLeads.city === '' && this.businessLeads.county === '') {
                    this.$message.error('地区为必填');
                    return true;
                }
                return false
            },
            // 线索检验规则
            checkClueRule() {
                var inquiry=$('input:radio[name="inquiry"]:checked').val();
                if(inquiry==undefined||inquiry==""){
                    this.$message.error('询价行为不允许为空');
                    return true;
                }
                var source=$('input:radio[name="source"]:checked').val();
                if(source==undefined||source==""){
                    this.$message.error('渠道类型不允许为空');
                    return true;
                }
                var communication=$('input:radio[name="communication"]:checked').val();
                if(communication==undefined||communication==""){
                    this.$message.error('渠道名称不允许为空');
                    return true;
                }
                $('#content').val(getContentStr());
                if($('#content').val()==''){
                    this.$message.error('三级分类不允许为空');
                    return true;
                }
                if($("#content").val().length>512){
                    this.$message.error('三级分类内容不允许超过512字符');
                    return true;
                }
                return false
            },
            //图片上传
            handleAvatarSuccess(file) {
                this.businessLeads.followPicList.push(file.filePath);
            },
            handleRemove(file) {
                let index = this.businessLeads.followPicList.indexOf(file.url);
                this.businessLeads.followPicList.splice(index, 1);
            },
            handlePictureCardPreview(file) {
                this.businessLeads.followPic = file.url;
                this.pictureDialogVisible = true;
            },
            beforeAvatarUpload(file) {

                var img = file.name.substring(file.name.lastIndexOf('.') + 1)
                img = img.toLocaleLowerCase();
                const suffix = img === 'jpg'
                const suffix2 = img === 'png'
                const suffix3 = img === 'jpeg'
                if (!suffix && !suffix2 && !suffix3) {
                    this.$message.error("只能上传图片！");
                    return false
                }

                const isLt2M = file.size / 1024 / 1024 <= 5;
                if (!isLt2M) {
                    this.$message.error('上传头像图片大小不能超过 5MB!');
                    return false
                }
                return true;
            },
            //客户名称远程搜索
            remoteMethod(query) {
                this.loading = true;
                if (query) {
                    getTraderRemoteList({"name": query})
                        .then(res => {
                            this.loading = false;
                            this.options = res.data.data
                        }).catch(res => {
                        this.options = [];
                    })
                } else {
                    this.options = [];
                }
            },
            clickTrader(item) {
                //if (item.belong) {
                    this.businessLeads.traderName = item.traderName;
                    this.businessLeads.traderId = item.traderId;
                    this.businessLeads.belongerId = item.saleId;
                    this.businessLeads.belonger = item.saleName;
                //}
            },
            // chooseBelonger(item) {
            //     this.businessLeads.belongerId = item.userId;
            //     this.businessLeads.belonger = item.username;
            // },
            chooseBelonger(selectedUserId) {
                const selectedUser = this.belongerOptions.find(item => item.userId === selectedUserId);
                if (selectedUser) {
                    this.businessLeads.belonger = selectedUser.username;
                }
            },
            selectBlur(e) {
                this.businessLeads.traderId = 0;
                this.businessLeads.traderName = e.target.value;
                if (!this.displaySelectOp) {
                    this.businessLeads.belongerId = leadsInfo.creater;
                    this.businessLeads.belonger = leadsInfo.creatername;
                }
            },
            getCheckedNodes() {
                const selectedValues = this.areaSelectedOptions;
                console.log("areaSelectedOptions", selectedValues);
                // 初始化businessLeads信息
                const initBusinessLeads = {
                    provinceId: null,
                    province: null,
                    cityId: null,
                    city: null,
                    countyId: null,
                    county: null
                };
                Object.assign(this.businessLeads, initBusinessLeads);
                // 级别映射到businessLeads的key
                const levelMap = {
                    1: {idKey: 'provinceId', nameKey: 'province'},
                    2: {idKey: 'cityId', nameKey: 'city'},
                    3: {idKey: 'countyId', nameKey: 'county'}
                };
                // 递归查找并赋值
                const findNodes = (options, values, level = 1) => {
                    for (let option of options) {
                        if (values.includes(option.value)) {
                            const {idKey, nameKey} = levelMap[level] || {};
                            if (idKey && nameKey) {
                                this.businessLeads[idKey] = option.value;
                                this.businessLeads[nameKey] = option.label;
                            }
                            if (option.children && option.children.length > 0) {
                                findNodes(option.children, values, level + 1);
                            }
                            // 找到匹配项后，不需要继续在当前层级查找
                            break;
                        }
                    }
                };
                // 递归查找
                findNodes(this.areaOptions, selectedValues);
            },
            
            skyEyes() {
                var traderName = this.businessLeads.traderName;
                //var traderNameReg = /^[a-zA-Z0-9\u4e00-\u9fa5\.\(\)\,\（\）\s]{2,128}$/;
                if (traderName == "") {
                    this.$message.error('客户名称不允许为空');
                    return false;
                }
                if (traderName.length < 4) {
                    this.$message.error('请至少输入4个字符再点击接口查询');
                    return false;
                }
                // if (!traderNameReg.test(traderName)) {
                //     this.$message.error('客户名称不允许使用特殊字符');
                //     return false;
                // }
                upperCase(traderName);
            },
            addTrader() {
                openTab("新增客户", '/trader/customer/add.do')
            },
            matchContract() {
                if (this.businessLeads.traderId == 0) {
                    this.$message.error('无客户信息，无法匹配联系人');
                    return false;
                }
                getTraderContactDat(
                    {
                        "param": {"traderId": this.businessLeads.traderId},
                        "orderBy": 'TRADER_CONTACT_ID desc',
                        "pageSize": 1000
                    }
                ).then(res => {
                    if (res.data.data) {
                        this.traderConcatDatas = res.data.data.list;
                    }
                    if (this.traderConcatDatasStatus) {
                        this.emptyDialogTableVisible = true;
                    } else {
                        this.dialogTableVisible = true;
                    }
                });
            },
            matchContractSubmit() {
                matchTraderContractSearch(
                    {"traderId": this.businessLeads.traderId, "mergeFiled": this.matchContractSubmitParm.trim()}
                ).then(res => {
                    if (res.data.data) {
                        this.traderConcatDatas = res.data.data;
                    }
                });
            },
            chooseTraderContract(row) {
                this.dialogTableVisible = false;
                this.businessLeads.traderContactId = row.traderContactId;
                this.businessLeads.contact = row.name;
                this.businessLeads.phone = row.mobile;
                this.businessLeads.telephone = row.telephone;
                this.$refs['form'].clearValidate(['contact']);
            },
            repeatCheck() {
                this.leadsCheckDuplicatesVisible = true;
                if (!this.businessLeads.phone && !this.businessLeads.telephone) {
                    return;
                }
                getLeadsListByDto(
                    {
                        "phone": this.businessLeads.phone,
                        "telephone": this.businessLeads.telephone,
                    }
                ).then(res => {
                    this.businessLeadsList = res.data.data;
                })
            },
            leadsCheckDuplicatesSubmit() {
                if (!this.businessLeads.mergeFiled) {
                    this.businessLeadsList = [];
                    return;
                }
                getLeadsListByDto(
                    {
                        "mergeFiled": this.businessLeads.mergeFiled.trim(),
                    }
                ).then(res => {
                    this.businessLeadsList = res.data.data;
                })
            },
            clearBusinessLeadsTraderInfo() {
                this.businessLeads.traderId = 0;
                if (!this.displaySelectOp) {
                    this.businessLeads.belongerId = leadsInfo.creater;
                    this.businessLeads.belonger = leadsInfo.creatername;
                }
            }

        }

    });

    function formatDate(row, column) {
        // 获取单元格数据
        let data = row[column.property];
        if (data == null) {
            return null;
        }
        let dt = new Date(data)
        return dt.getFullYear() + '-' + (dt.getMonth() + 1) + '-' + dt.getDate() + ' ' + dt.getHours() + ':' + dt.getMinutes() + ':' + dt.getSeconds()
    }
</script>

<style>
    #aaa .el-collapse-item__header {
        color: #409EFF;
    }

    #aaa .el-icon-arrow-right:before {
        content: "展开";
    }

    #aaa .el-collapse-item__arrow.is-active {
        transform: none;
    }

    #aaa .el-collapse-item__arrow.is-active:before {
        content: "收起";
    }

</style>
