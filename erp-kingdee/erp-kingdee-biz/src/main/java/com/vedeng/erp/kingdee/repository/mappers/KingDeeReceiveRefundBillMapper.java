package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveRefundBill;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface KingDeeReceiveRefundBillMapper {
    int deleteByPrimaryKey(Integer kingDeeReceiveRefundBillId);

    int insert(KingDeeReceiveRefundBill record);

    int insertSelective(KingDeeReceiveRefundBill record);

    KingDeeReceiveRefundBill selectByPrimaryKey(Integer kingDeeReceiveRefundBillId);

    int updateByPrimaryKeySelective(KingDeeReceiveRefundBill record);

    int updateByPrimaryKey(KingDeeReceiveRefundBill record);

    int updateLshByFBillNo(@Param("fBillNo") String fBillNo, @Param("lsh") String fQzokLsh);
}