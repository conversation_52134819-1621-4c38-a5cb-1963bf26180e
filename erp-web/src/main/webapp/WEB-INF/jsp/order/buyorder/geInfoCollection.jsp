<%--
  Created by IntelliJ IDEA.
  User: vedeng
  Date: 2020/11/13
  Time: 14:06
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="GE合同编号和订单编号录入" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css"  media="all">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<div class="layui-container">
    <div class="layui-row" >
        <div class="layui-col-md12" style="margin-top: 30px">
            <form class="layui-form" action="" >
            <c:if test="${sku ne null}">
                <div class="layui-form-item" >
                    <div>
                        <label class="layui-form-label" style="width: 160px;text-align: left;margin-top: 16px">
                            <span style="color: red">*</span>
                            &nbsp;&nbsp;SKU
                        </label>
                    </div>
                    <div  style="width: 250px;margin-top: 20px;float: left">
                        <input type="text" value="${sku}" class="layui-input" readonly>
                    </div>
                </div>
            </c:if>
                    <div class="layui-form-item" >
                        <div>
                            <label class="layui-form-label" style="width: 160px;text-align: left;margin-top: 16px">
                                <span style="color: red">*</span>
                                &nbsp;&nbsp;GE合同编号
                            </label>
                        </div>
                        <div  style="width: 250px;margin-top: 16px;float: left">
                            <input type="text" placeholder="请输入GE合同编号" class="layui-input" id="geContractNo" name="geContractNo" value="${geContractNo}">
                        </div>
                    </div>
                <div class="layui-form-item" >
                    <div >
                        <label class="layui-form-label" style="width: 160px;text-align: left;margin-top: 16px">
                            <span style="color: red">*</span>
                            &nbsp;&nbsp;GE销售合同编号
                        </label>
                    </div>
                    <div  style="width: 250px;margin-top: 16px;float: left">
                        <input type="text" placeholder="请输入GE销售合同编号" class="layui-input" id="geSaleContractNo" name="geSaleContractNo" value="${geSaleContractNo}">
                    </div>
                </div>
             <%--   <div class="layui-form-item" style="margin-top: 20px">
                    <div >
                        <input id="cancle" type="button" class="layui-btn" value="取消" style="background-color: #7a8a99;margin-left: 10px;margin-right: 60px ">
                        <input id="submit" onclick="submit()" type="button" class="layui-btn" value="确定" style="background-color: #1E9FFF">
                    </div>
                </div>--%>
                <div class="layui-form-item" style="margin-top: 30px">
                    <div class="layui-input-block">
                        <input type="button" id="cancle" value="取消" style="width: 60px;color: white;background-color: #7a8a99;margin-right: 110px">
                        <input type="button" id="submit" onclick="submit()" value="确定" style="background-color: #1E9FFF;color:white;width: 60px">
                    </div>
                </div>
                <input type="hidden" name="geBuyorderGoodsIds" id="geBuyorderGoodsIds" value="${geBuyorderGoodsIds}">
            </form>

        </div>
    </div>
</div>


<script type="text/javascript">
    $("#submit").click(function submit() {
        checkLogin();
        var geBuyorderGoodsIds = $("#geBuyorderGoodsIds").val();
        var geContractNo = $("#geContractNo").val();
        var geSaleContractNo = $("#geSaleContractNo").val();
        if (geBuyorderGoodsIds == "") {
            layer.alert("要录入的采购商品不可为空");
            return false;
        }

        if (geContractNo == "") {
            layer.alert("GE合同编号不可为空");
            return false;
        }

        if (geSaleContractNo == "") {
            layer.alert("GE销售合同编号不可为空");
            return false;
        }
        $.ajax({
            url: page_url + '/order/georder/upadteGeInfo.do',
            data: {
                'geBuyorderGoodsIds': geBuyorderGoodsIds,
                'geContractNo': geContractNo,
                'geSaleContractNo': geSaleContractNo
            },
            type: "POST",
            dataType: "json",
            async: false,
            success: function (data) {
                if (data.code == 0) {
                    layer.alert(data.message);
                } else {
                    layer.alert(data.message);
                }
            },
            error: function (data) {
                if (data.status == 1001) {
                    alert("您没有操作权限，申请开通权限请联系研发部Aadi")
                }
            }
        });
        var index = parent.layer.getFrameIndex(window.name); //获取当前窗体索引
        parent.layer.close(index);
        parent.window.location.reload();
    })
</script>


