/**
 * 重置搜索框的值
 */
function searchReset() {
    $('#keyword').val('');
    $('#userId').val(0);
    $('#invoiceTaxRate').val(0);
    $('#startAddDateStr').val('');
    $('#endAddDateStr').val('');
    $('#comment').val('');
    $('#isRecording').val(0);
    $('#invoiceStatus').val(0);
}

/**
 * 更新航信发票的状态
 * @param hxInvoiceId 航信发票的ID
 * @param invoiceStatus 航信发票的状态
 */
function saveHxInvoiceStatus(hxInvoiceId, invoiceStatus) {
    var str = '';
    if (invoiceStatus == 4){
        str = '确定将该发票标记为非采购发票吗?';
    } else {
        str = '确定将该发票标记为采购退票吗?'
    }
    layer.confirm(str, {title: '标记发票状态'}, function (index) {
        $.ajax({
            url: '/supplyChain/invoice/saveHxInvoiceStatus.do',
            data: {
                hxInvoiceId: hxInvoiceId,
                invoiceStatus: invoiceStatus
            },
            type: 'post',
            dataType: 'json',
            success: function (res) {
                if (res.code == 0) {
                    layer.alert('标记成功', function () {
                        window.location.reload();
                    });
                } else {
                    layer.alert('操作失败');
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }, function () {
        layer.close(index);
    })
}

/**
 * iframe之间切换选项卡
 * @param index 选项卡下标
 */
function changeTab(index) {
    window.location = '/supplyChain/invoice/hx_invoice_wait.do?idFlag=' + index;
}