package com.vedeng.erp.trader.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PushBusinessLeadsDto extends BaseDto {

    /**
     * 线索编号
     */
    private String leadsNo;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 客户ID-根据手机号取出来注册账号里关联的客户ID
     */
    private Integer traderId;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 产品信息
     */
    private String goodsInfo;
    
    /**
     * 线索分级（0无效 1有效）
     */
    private Integer status;

    /**
     * 归属人id
     */
    private Integer belongerId;

    /**
     * 归属人
     */
    private String belonger;

    /**
     * 线索类型字典库
     */
    private Integer clueType;

    /**
     * 询价行为字典库
     */
    private Integer inquiry;

    /**
     * 渠道类型字典库
     */
    private Integer source;
    
    /**
     * 渠道名称字典库
     */
    private Integer communication;
    
    /**
     * 三级分类
     */
    private String content;

    /**
     * 咨询入口
     */
    private Integer entrances;

    /**
     * 功能
     */
    private Integer functions;

    /**
     * 是否发送微信标识
     */
    private  String sendVx;

    /**
     * 天眼查标识
     */
    private String tycFlag;

    /**
     * 分类ids
     */
    private String categoryIds;

    private String keywords;

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 市id
     */
    private Integer cityId;

    /**
     * 县id
     */
    private Integer countyId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 县
     */
    private String county;

}
