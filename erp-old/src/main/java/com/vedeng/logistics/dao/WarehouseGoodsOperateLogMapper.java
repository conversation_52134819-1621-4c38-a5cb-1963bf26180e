package com.vedeng.logistics.dao;

import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.flash.dto.PrepareStockCommand;
import com.vedeng.goods.model.Goods;
import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WarehouseLog;
import com.vedeng.logistics.model.outIn.OutInDetail;
import com.vedeng.logistics.model.vo.ExpressArrivalDetailVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


public interface WarehouseGoodsOperateLogMapper {


	/**
	 * <b>Description:</b><br> 采购入库信息
	 *
	 * @param wg
	 * @return
	 * @Note <b>Author:</b> scott
	 * <br><b>Date:</b> 2017年12月2日 上午10:43:00
	 */
	WarehouseGoodsOperateLog getWOLByC(WarehouseGoodsOperateLog wg);

	/**
	 * <b>Description:</b><br> 销售售后的采购信息
	 *
	 * @param wg
	 * @return
	 * @Note <b>Author:</b> scott
	 * <br><b>Date:</b> 2017年12月2日 上午10:46:43
	 */
	WarehouseGoodsOperateLog getWOLByS(WarehouseGoodsOperateLog wg);

	/**
	 * <b>Description:</b><br> 采购售后的采购信息
	 *
	 * @param wg
	 * @return
	 * @Note <b>Author:</b> scott
	 * <br><b>Date:</b> 2017年12月2日 下午1:50:19
	 */
	WarehouseGoodsOperateLog getWOLByCH(WarehouseGoodsOperateLog wg);

	/**
	 * @Description: 修改库存问题
	 * @Param: [wg]
	 * @return: java.lang.Integer
	 * @Author: addis
	 * @Date: 2019/8/19
	 */
	Integer updateWarehouse(@Param("wg") List<WarehouseGoodsOperateLogVo> wg);

	/**
	 * @param @param  woList
	 * @param @return
	 * @return List<WarehouseGoodsOperateLog>
	 * @throws
	 * @Description: TODO(首营的信息 产品注册证号 / 者备案凭证编号, 生产企业, 生产企业许可证号 / 备案凭证编号, 储运条件)
	 * <AUTHOR>
	 * @date 2019年9月30日
	 */
	List<WarehouseGoodsOperateLog> getfirstRegistrationInfo(List<WarehouseGoodsOperateLog> woList);

	/**
	 * @Description: 查询有问题goodid
	 * @Param: [warehouseGoodsOperateLogVo]
	 * @return: java.util.List<com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo>
	 * @Author: addis
	 * @Date: 2019/8/20
	 */
	List<WarehouseGoodsOperateLogVo> getWarehouseGoodsId(WarehouseGoodsOperateLogVo warehouseGoodsOperateLogVo);

	/**
	 * <b>Description:</b><br> 获取换货已退回或已重发数量
	 *
	 * @param wl
	 * @return
	 * @Note <b>Author:</b> scott
	 * <br><b>Date:</b> 2017年9月12日 下午6:11:09
	 */
	Integer getAftersalesGoodsSum(WarehouseGoodsOperateLog wl);

	Integer getAftersalesGoodsSum2(WarehouseGoodsOperateLog wl);

	/**
	 * @param @param saleorder
	 * @return List<WarehouseGoodsOperateLog>    返回类型
	 * @throws
	 * @Title: getWarehouseLendOutList
	 * @Description: TODO(获取商品外借单出库记录)
	 * <AUTHOR>
	 * @date 2019年8月29日
	 */
	List<WarehouseGoodsOperateLogVo> getWarehouseLendOutList(Saleorder saleorder);

	/**
	 * @param @param  w
	 * @param @return
	 * @return WarehouseGoodsOperateLog
	 * @throws
	 * @Description: TODO(外借单信息)
	 * <AUTHOR>
	 * @date 2019年9月18日
	 */
	WarehouseGoodsOperateLog getLendoutByL(WarehouseGoodsOperateLog w);

	/**
	 * 获取出入库记录
	 *
	 * @Author:strange
	 * @Date:16:33 2019-11-13
	 */
	WarehouseGoodsOperateLog getWarehouseInfoById(Integer warehouseGoodsOperateLogId);

	/**
	 * 获取使用过此条码的出库记录
	 *
	 * @Author:strange
	 * @Date:14:44 2019-12-16
	 */
    List<WarehouseGoodsOperateLog> getBarcodeIsEnable(WarehouseGoodsOperateLog wl);

	/**
	 * 获取未关联快递详情出入库日志id
	 *
	 * @Author:strange
	 * @Date:13:27 2020-02-10
	 */
    List<WarehouseGoodsOperateLog> getWarehouseIdByExpressDetail(ExpressDetail expressDetail);

	/**
	 * 保存快递关联出入库记录关系
	 *
	 * @Author:strange
	 * @Date:14:07 2020-02-10
	 */
	int insertExpressWarehouse(@Param("warehouseLogId") Integer warehouseLogId, @Param("expressDetailId") Integer expressDetailId);

	/**
	 * 更新重复出入库id无效
	 *
	 * @Author:strange
	 * @Date:15:38 2020-02-10
	 */
	void updateExpressWarehouse(Integer warehouseLogId);

	/**
	 * 获取关联快递的出入库记录id
	 *
	 * @Author:strange
	 * @Date:19:28 2020-02-10
	 */
    List<Integer> getExpressWlogIds(Integer expressId);

	/**
	 * 获取最后出库时间
	 *
	 * @Author:strange
	 * @Date:09:37 2020-02-25
	 */
    Long getLastOutTime(WarehouseGoodsOperateLog w);

	/**
	 * 获取订单下有效出库记录
	 *
	 * @Author:strange
	 * @Date:13:48 2020-02-26
	 */
    List<Integer> getWarehouseLogIdBy(Integer saleorderId);

    int getWarehouseoutRecordCounts(@Param("saleorderId") int saleorderId);


    /**
	 * @Description: 查询该商品可用的库存量
	 * @Param:
	 * @return:
	 * @Author: addis
	 * @Date: 2020/3/25
	 */
    List<WarehouseGoodsOperateLogVo> getSameBatchGoodsInfo(WarehouseGoodsOperateLogVo warehouseGoodsOperateLogVo);

	/**
	 * 获取日志记录的库位信息
	 *
	 * @Author:strange
	 * @Date:11:34 2020-03-24
	 */
	WarehouseGoodsOperateLog getStorageInfo(Integer warehouseGoodsOperateLogId);

	/**
	 * @return
	 * <AUTHOR>
	 * @Description //TODO 获取采购单采购价
	 * @Date 9:20 上午 2020/5/6
	 * @Param
	 **/
    List<WarehouseGoodsOperateLog> getBuyOrderPrice(@Param("warehouseGoodsOperateLogList") List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList);

	/**
	 * @return
	 * <AUTHOR>
	 * @Description //TODO 保存成本价
	 * @Date 9:56 上午 2020/5/6
	 * @Param
	 **/
	Integer saveCostPrice(WarehouseGoodsOperateLog warehouseGoodsOperateLog);

	/**
	 * @return
	 * <AUTHOR>
	 * @Description //TODO 获取采购售后成本价
	 * @Date 9:56 上午 2020/5/6
	 * @Param
	 **/
	List<WarehouseGoodsOperateLog> getBuyOrderChangePrice(@Param("warehouseGoodsOperateLogList") List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList);

	/**
	 * @return
	 * <AUTHOR>
	 * @Description //TODO 获取售后单关联销售订单id
	 * @Date 11:39 上午 2020/5/6
	 * @Param
	 **/
    List<WarehouseGoodsOperateLog> getSaleOrderRelateId(@Param("warehouseGoodsOperateLogList") List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList);

	/**
	 * @return
	 * <AUTHOR>
	 * @Description //TODO 获取外借出库时使用条码入库信息
	 * @Date 2:10 下午 2020/5/6
	 * @Param
	 **/
    List<WarehouseGoodsOperateLog> getLendOutWhareHouserInList(@Param("relatedId") Integer relatedId, @Param("goodsId") Integer goodsId);

	/**
	 * 获取在库商品数据
	 *
	 * @return
	 * <AUTHOR>
	 * @Date 10:20 上午 2020/5/7
	 * @Param operaType 入库业务类型
	 **/
    List<WarehouseGoodsOperateLog> getWarehouseZKlog(Integer operaType);

	/**
	 * 更新是否使用标识
	 *
	 * @return
	 * <AUTHOR>
	 * @Date 2:12 下午 2020/5/7
	 * @Param
	 **/
    int updateIsUse(@Param("list") List<WarehouseGoodsOperateLog> list, @Param("isUse") Integer isUse);

	/**
	 * 获取使用过的条码
	 * <AUTHOR>
	 * @Date 3:57 下午 2020/5/7
	 * @Param
	 * @return
	 **/
    List<WarehouseGoodsOperateLog> getIsUseLog();

    int updateBadHistoryLogIsUse();

	/**
	 * @description: 获取未出库的采购单入库条码信息
	 * @return:
	 * @author: Strange
	 * @date: 2020/7/28
	 **/
	List<WarehouseGoodsOperateLog> getBuyorderInlogicalByBuyorderGoodsId(@Param("buyorderGoodsId") Integer buyorderGoodsId);

	/**
	 * @description: 更新入库类型剩余库存量
	 * @return:
	 * @author: Strange
	 * @date: 2020/7/17
	 **/
    int updateInIsUseAndLastStockNum(@Param("list") List<WarehouseGoodsOperateLog> list);

	/**
	 * @description: 更新出库时使用入库条码剩余库存量
	 * @return: int
	 * @author: Strange
	 * @date: 2020/7/17
	 **/

	int updateOutIsUseAndLastStockNum(WarehouseGoodsOperateLog log);

	/**
	 * @description: 主键获取日志
	 * @return: WarehouseGoodsOperateLog
	 * @author: Strange
	 * @date: 2020/7/17
	 **/
	WarehouseGoodsOperateLog getLogByWarehouseGoodsOperateLogId(WarehouseGoodsOperateLog warehouseGoodsOperateLog);

	/**
	 * 保存产品出入库日志信息
	 * @param record
	 * @return
	 */
	int insertSelective(WarehouseGoodsOperateLog record);

	/**
	 * 更新可用逻辑仓库存信息
	 * @param warehouseGoodsOperateLog
	 * @return
	 */
	int updateAvailableLogicalGood(WarehouseGoodsOperateLog warehouseGoodsOperateLog);

	/**
	 * 获取逻辑仓可用商品信息
	 * @param warehouseGoodsOperateLog
	 * @return
	 */
	List<WarehouseGoodsOperateLog> getAvailableLogicalGoods(WarehouseGoodsOperateLog warehouseGoodsOperateLog);

	/**
	 * 获取逻辑仓在库列表
	 * @param goods
	 * @return
	 */
    List<WarehouseGoodsOperateLog> getLogincalStockOrderInfo(Goods goods);

	/**
	 * @description: 获取在库数量
	 * @return:
	 * @author: Strange
	 * @date: 2020/7/30
	 **/
    Integer getInstockNumByRelatedId(WarehouseGoodsOperateLog record);



	/**
	 * @description: 逻辑仓id和贝登批次码获取未出库在库记录
	 * @return: WarehouseGoodsOperateLog
	 * @author: Strange
	 * @date: 2020/8/4
	 **/
	List<WarehouseGoodsOperateLog> getInLogByLogicalIdAndVBatchNumber(WarehouseGoodsOperateLog search);

	/**
	 * @description: 更新剩余库存数量和是否使用完字段
	 * @return: int
	 * @author: Strange
	 * @date: 2020/8/4
	 **/
	int updateOutIsUseAndLastStockNumById(WarehouseGoodsOperateLog ininfo);

	/**
	 * @description 根据贝登批次码和Sku查找出入库记录
	 *
	 * @param warehouseGoodsOperateLog
	 * @return
	 */
	List<WarehouseGoodsOperateLog> getWarehouseGoodsOperateLogByVedengBatchNoAndSku(WarehouseGoodsOperateLog warehouseGoodsOperateLog);

    List<WarehouseGoodsOperateLog> getSpecialLocationLog(WarehouseGoodsOperateLog queryCon);

	int updateByPrimaryKeySelective(WarehouseGoodsOperateLog warehouseGoodsOperateLog);

    List<WarehouseGoodsOperateLog> getWarehouseoutLogByTypeAndRelateId(@Param("goodsId") Integer goodsId,@Param("operateType") Integer operateType,@Param("relatedId") Integer relatedId);

	List<WarehouseGoodsOperateLog> getInputWarehouseLog(List<Integer> buyOrderGoodIdList);

    List<WarehouseGoodsOperateLog> getInLogByInfo(WarehouseGoodsOperateLog warehouseGoodsOperateLog);

    List<Integer> getExpressWMSWlogIds(Integer expressId);

	List<WarehouseGoodsOperateLog> getAvailAbleStockNumByGoodsId(Integer valueOf);
	/**
	 * @description: 获取wms入库单入库记录
	 * @return: List<WarehouseGoodsOperateLog>
	 * @author: Strange
	 * @date: 2020/9/23
	 **/
    List<WarehouseGoodsOperateLog> getWmsInputOrderLogListByOrderId(@Param("wmsInputOrderId") Integer wmsInputOrderId, @Param("logOperateType") Integer logOperateType);

	List<Integer> getHaveChangeSkuNos();

	List<WarehouseGoodsOperateLog> getNullbarcodeIdInlog();

	List<WarehouseGoodsOperateLog> getNullbarcodeIdOutlog(WarehouseGoodsOperateLog log);

    List<WarehouseGoodsOperateLog> getWmsOutputOrderLogListByOrderId(@Param("wmsoutputOrderId")Integer wmsOutputOrderId, @Param("logOperateType")Integer logOperateType);

    int saveNewCostPrice(WarehouseGoodsOperateLog goodsOperateLog);


	List<WarehouseGoodsOperateLog> getWarehouseZKlogLimit(@Param("operaType") Integer operaType, @Param("limit") Integer limit,@Param("warehouseGoodsOperateLogId") Integer warehouseGoodsOperateLogId);

	Integer getOperateTypeById(Integer logId);

	List<WarehouseGoodsOperateLog> getwlById(WarehouseGoodsOperateLog warehouseGoodsOperateLog);

	WarehouseGoodsOperateLog getPrintPriceById(WarehouseGoodsOperateLog warehouseGoodsOperateLog);

    List<WarehouseGoodsOperateLog> getWarehouseHistoryOutList( @Param("limit") Integer limit,@Param("warehouseGoodsOperateLogId") Integer warehouseGoodsOperateLogId);

	List<WarehouseGoodsOperateLog> getWarehouseInlog(WarehouseGoodsOperateLog log);

	/**
	 * <b>Description:</b><br> 第一次出库操作人+时间
	 * @param saleorder
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年9月6日 下午3:32:11
	 */
    WarehouseGoodsOperateLog getWarehouseGoodsOperateOutLog(Saleorder saleorder);

	/**
	 * <b>Description:</b><br> 第一次入库操作人+时间
	 * @param buyorder
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年9月6日 下午3:32:11
	 */
	WarehouseGoodsOperateLog getWarehouseGoodsOperateInLog(Buyorder buyorder);

	/**
	 * 根据采购单ID和SKUID获取当前sku的入库记录
	 * @param command 查询条件
	 * @return 入库记录
	 */
    List<WarehouseGoodsOperateLog> getInputOrderLogListByOrderIdAndGoodsId(PrepareStockCommand command);

    int getAlreadyOutputNum(@Param("skuId") Integer skuId,@Param("saleorderGoodsId") Integer saleorderGoodsId,@Param("buyOrderNo") String buyOrderNo);

	/**
	 *
	 * <b>Description:</b>根据销售产品id查询未绑定快递详情的出库记录
	 * @param epd
	 * @return List<WarehouseGoodsOperateLog>
	 * @Note
	 * <b>Author：</b> scott.zhu
	 * <b>Date:</b> 2018年11月15日 下午1:23:19
	 */
	List<WarehouseGoodsOperateLog> getbdListBySaleGoodsId(ExpressDetail epd);
	/**
	 *
	 * <b>Description:</b>批量更新绑定状态
	 * @param bdList
	 * @return int
	 * @Note
	 * <b>Author：</b> scott.zhu
	 * <b>Date:</b> 2018年11月15日 下午1:51:37
	 */
	int batchUpdateWgolIsExpress(List<WarehouseGoodsOperateLog> bdList);

    Integer getOutStockByBuyorderGoodsId(WarehouseGoodsOperateLog record);

	Integer getInStockByRelateIdOptType(WarehouseGoodsOperateLog record);

    List<WarehouseGoodsOperateLog> getWarehouseOutList(Saleorder saleorder);

	List<WarehouseGoodsOperateLog> getWGOlog(WarehouseGoodsOperateLog wlg);

	/**
	 * 查询出库记录
	 * @param wlg
	 * @return
	 */
	List<WarehouseGoodsOperateLog> getWGOlog2(WarehouseGoodsOperateLog wlg);

	List<WarehouseGoodsOperateLog> getBuyorderAfterSaleLog(AfterSalesGoodsVo afterSalesGoodsVo);

	Integer getBuyorderAfterSaleLogNumByType(@Param("orderDetailId") Integer orderDetailId, @Param("type") Integer type);

	List<ExpressArrivalDetailVo> getDeliveryDetailOfSaleorderGoods(@Param("relatedId") Integer relatedId, @Param("goodsId") Integer goodsId);

    List<Integer> getExpressWMSWlogIdsByExpressIds(List<Integer> expressIds);


	/**
	 * <b>Description:</b><br> 批量查询销售商品的id查询出库记录产品总数
	 * @param list
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年8月3日 下午5:39:06
	 */
	List<SaleorderGoodsVo> batchOutStockBySaleorderGoodsIdList(@Param("list")List<SaleorderGoodsVo> list);


	/**
	 * <b>Description:</b><br> 批量查询销售商品的id查询关联采购入库数量
	 * @param list
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年8月3日 下午5:39:06
	 */
	List<SaleorderGoodsVo> batchBuyGoodsInStockBySaleorderGoodsIdList(@Param("list")List<SaleorderGoodsVo> list);


	/**
	 * <b>Description:</b><br> 批量查询销售商品的id查询关联采购未出库数量
	 * @param list
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年8月3日 下午5:39:06
	 */
	List<SaleorderGoodsVo> batchBuyGoodsNotStockBySaleorderGoodsIdList(@Param("list")List<SaleorderGoodsVo> list);
	/**
	 * <b>Description:</b><br>
	 * 根据relatedid查询出入库信息
	 *
	 * @param relatedId
	 * @return java.util.List<com.vedeng.model.logistics.WarehouseGoodsOperateLog>
	 * @Note <b>Author:</b> Thor <br>
	 *       <b>Date:</b> 2021/5/17 15:10
	 */
	List<WarehouseGoodsOperateLog> getListByRelatedId(Integer relatedId);

	/**
	 * <AUTHOR>
	 * @desc 根据销售单商品id查询出库sn码
	 * @param saleorderGoodsId
	 * @return
	 */
	List<String> querySnBySaleorderGoodsId(Integer saleorderGoodsId);

	/**
	 * 查询盘亏出库
	 * @param inventoryOutOrderId 出库单id
	 * @return
	 */
    List<WarehouseGoodsOperateLog> getWarehouseOutListByInventoryOutOrderId(Long inventoryOutOrderId);

	/**
	 * 获取入库明细
	 *
	 * @param outInNo
	 * @param wmsNo
	 * @param outInType
	 * @return
	 */
	List<OutInDetail> getOperateList(@Param("outInNo") String outInNo, @Param("wmsNo") String wmsNo, @Param("outInType") Integer outInType);

	List<OutInDetail> getOUtInDetailList(@Param("orderNo") String orderNo, @Param("outInType") Integer outInType, @Param("operateType") Integer operateType);

	List<WarehouseGoodsOperateLog> getWGOlistByComments(@Param("batChNo") String batChNo,@Param("saleorderId") Integer saleorderId);

	WarehouseGoodsOperateLog getAddTimeByRelatedId(Integer relateId);

	/**
	 * 获取单位转换单的出库日志
	 * @param wmsUnitConversionOrderId 订单id
	 * @return List<WarehouseGoodsOperateLog> 日志
	 */
	List<WarehouseGoodsOperateLog> getWmsUnitConversionOutLog(Integer wmsUnitConversionOrderId);

	/**
	 * 获取单位转换单的入库日志
	 * @param wmsUnitConversionOrderId 订单id
	 * @return List<WarehouseGoodsOperateLog> 日志
	 */
	List<WarehouseGoodsOperateLog> getWmsUnitConversionInLog(Integer wmsUnitConversionOrderId);


	List<WarehouseLog> getWarehouseLogById(@Param("idList")List<Integer> idList);

	List<Map<String,Object>> getWarehouseBySaleorderId(Integer saleorderId);
	List<WarehouseGoodsOperateLog> getLog( @Param("buyorderId") Integer buyorderId);
}
