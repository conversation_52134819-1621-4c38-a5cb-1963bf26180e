<!doctype html>
<html lang="en">
<head>
 <meta charset="UTF-8">
 <title>京东商品对应表</title>
 <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="jd_goods" datasource="erp-datasourcetarget" fixednumber="" fixednumberright="" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
<div id="appendHead"></div>
<div class="layui-fluid">
 <div class="layui-tab">
  <ul class="layui-tab-title" id="tab">
  </ul>
 </div>
 <form class="layui-form" id="search">
  <div class=" layui-inline "><label class="layui-form-label">京东商品ID</label>
   <div class="layui-input-inline">
    <input class=" layui-input list-search-item " type="text" name="JD_GOODS_ID" placeholder style alias="" jdbctype data datatype oper validate_rules validate_messages>
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">贝登SKU</label>
   <div class="layui-input-inline">
    <input class=" layui-input list-search-item " type="text" name="VD_SKU_NO" placeholder style alias="" jdbctype data datatype oper validate_rules validate_messages>
   </div>
  </div>
  <div class=" layui-inline ">
   <label class="layui-form-label">商品名称</label>
   <div class="layui-input-inline">
    <input class=" layui-input list-search-item " type="text" name="SKU_NAME" placeholder style="" alias="" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages="">
   </div>
  </div>
  <div class=" layui-inline "><label class="layui-form-label">修改时间</label>
   <div class="layui-input-inline">
    <object class=" layui-input list-search-item " type="daterange" name="MOD_TIME" placeholder style="" alias="A" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
   </div>
  </div>
 </form>
 <hr class="layui-border-blue">
 <div class="btn-group   bd-highlight" id="tableButton"><button item_name="新增" name="新增" url="/ezadmin/form/form-jd_goods" opentype="MODEL" windowname style type="table">新增</button>
 </div>
 <table id="table" class="layui-table" style=" width:100%">
  <thead>
  <tr id="column">
   <th item_name="JD_GOODS_ID" name="JD_GOODS_ID" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="">京东商品ID</th>
   <th item_name="VD_SKU_NO" name="VD_SKU_NO" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="0" edit_express="/ezadmin/form/doSubmit-jd_goods" edit_plugin="input-text">贝登SKU</th>
   <th item_name="SKU_NAME" name="SKU_NAME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="">商品名称</th>
   <th item_name="MOD_TIME" name="MOD_TIME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="DATETIME" head="th" edit_flag="" edit_express="" edit_plugin="">修改时间</th>
   <th type="rowbutton" id="rowbutton"><button item_name="修改" name="修改" url="/ezadmin/form/form-jd_goods?ID=${ID}" opentype="MODEL" windowname style area="" plugin_code="button-single" list_id="null" item_label="修改" open_type="MODEL" item_url="/ezadmin/form/form-jd_goods?ID=${ID}" window_name="" type="single">修改</button></th>
  </tr>
  </thead>
  <tbody>
  <tr>
   <td> express:</td>
   <td colspan="100"><pre id="express" class="layui-code" orderby="ORDER BY ID DESC" groupby="">StringBuilder sql=new StringBuilder();
 sql.append("
    select  ID,JD_GOODS_ID  ,VD_SKU_NO  ,B.SKU_NAME  ,A.MOD_TIME
from V_JD_GOODS A LEFT JOIN V_CORE_SKU B ON A.VD_SKU_NO=B.SKU_NO
WHERE 1=1
");
 return search(sql);</pre></td>
  </tr>
  <tr>
   <td> count:</td>
   <td colspan="100"><pre id="count" class="layui-code"></pre></td>
  </tr>
  </tbody>
 </table>
</div>
<div id="appendFoot"></div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
 layui.use(function () {

 })
</script>
</body>
</html>