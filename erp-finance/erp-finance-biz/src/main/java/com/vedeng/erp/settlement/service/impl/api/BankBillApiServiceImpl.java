package com.vedeng.erp.settlement.service.impl.api;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.finance.domain.entity.TransactionVoucherEntity;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.mapper.TransactionVoucherMapper;
import com.vedeng.erp.settlement.domain.entity.BankBillEntity;
import com.vedeng.erp.settlement.mapper.BankBillMapper;
import com.vedeng.erp.settlement.mapper.CapitalBillMapper;
import com.vedeng.erp.settlement.mapstruct.BankBillConvertor;
import com.vedeng.erp.finance.service.BankBillApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 服务实现类
 * @date 2022/9/6 9:39
 **/
@Service
@Slf4j
public class BankBillApiServiceImpl implements BankBillApiService {

    @Autowired
    private BankBillMapper bankBillMapper;

    @Autowired
    private BankBillConvertor bankBillConvertor;
    @Autowired
    private TransactionVoucherMapper transactionVoucherMapper;

    @Override
    public void updateBankBillReceipt(AlipayReceiptDto alipayReceiptDto) {

        if (alipayReceiptDto == null || StringUtils.isEmpty(alipayReceiptDto.getTranFlow())) {
            throw new ServiceException("流水号不可为空");
        }
        BankBillEntity bankBillEntity = bankBillMapper.selectByTranFlow(alipayReceiptDto.getTranFlow(), 4);
        if (bankBillEntity == null) {
            throw new ServiceException("未查到支付宝流水号");
        }
        BankBillEntity update = new BankBillEntity();
        update.setBankBillId(bankBillEntity.getBankBillId());
        update.setReceiptUrl(alipayReceiptDto.getReceiptUrl());

        log.info("前台支付宝更新回单：{}", JSON.toJSON(update));
        bankBillMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public void add(BankBillDto bankBillDto) {
        BankBillEntity bankBillEntity = bankBillConvertor.toEntity(bankBillDto);
        BankBillEntity bankBillExist = bankBillMapper.selectByTranFlow(bankBillDto.getTranFlow(),bankBillDto.getBankTag());
        log.info("获取到金蝶保存银行流水信息{},查询到数据库已有流水{},",JSON.toJSONString(bankBillEntity),JSON.toJSONString(bankBillExist));
        if(bankBillExist != null){
            bankBillExist.setAccBankno(bankBillDto.getAccBankno());
            bankBillExist.setMatchedAmount(bankBillDto.getMatchedAmount());
            bankBillExist.setMatchedObject(bankBillDto.getMatchedObject());
            bankBillMapper.updateByPrimaryKeySelective(bankBillExist);
            bankBillDto.setBankBillId(bankBillExist.getBankBillId());
        }else {
            bankBillMapper.insertSelective(bankBillEntity);
            bankBillDto.setBankBillId(bankBillEntity.getBankBillId());
        }
    }

    @Override
    public void update(BankBillDto bankBillDto) {
        BankBillEntity bankBillEntity = bankBillConvertor.toEntity(bankBillDto);
        bankBillMapper.updateByPrimaryKeySelective(bankBillEntity);
    }

    @Override
    public List<BankBillDto> getAliPayBillInfo() {
        return bankBillMapper.getAliPayBillNotPushedToKingDee();
    }

    @Override
    public List<BankBillDto> getByTranFlowLike(String flowNo) {
        return bankBillMapper.getByTranFlowLike(flowNo);
    }

    @Override
    public boolean checkTranFlow(String tranFlow, Integer bankTag) {
        List<BankBillEntity> byTranFlowAndBankTag = bankBillMapper.findByTranFlowAndBankTag(tranFlow, bankTag);
        return CollUtil.isNotEmpty(byTranFlowAndBankTag);
    }

    @Override
    public BankBillDto checkTranFlowForCharge(String tranFlow, Integer bankTag){
        List<BankBillEntity> byTranFlowAndBankTag = bankBillMapper.findByTranFlowAndBankTag(tranFlow, bankTag);
        return CollUtil.isNotEmpty(byTranFlowAndBankTag)?bankBillConvertor.toDto(byTranFlowAndBankTag.get(0)):null;
    }

    @Override
    public void updateTranFlowForCharge(String tranFlow, Integer bankTag, String newTranFlow){
        bankBillMapper.updateTranFlowForCharge(tranFlow,bankTag,newTranFlow);
    }

    @Override
    public List<BankBillDto> getByTranFlowAndBankTag(String tranFlow, Integer bankTag) {
        return bankBillMapper.getByTranFlowAndBankTag(tranFlow, bankTag);
    }

    @Override
    public BankBillDto getBankBillByBankBillId(Integer bankBillId) {
        BankBillEntity bankBillEntity = bankBillMapper.selectByPrimaryKey(bankBillId);
        return bankBillConvertor.toDto(bankBillEntity);
    }

    @Override
    public List<BankBillDto> getBankBillByDateAndCompany(Date startDate, Date endDate, String name) {
        return bankBillMapper.findByAccName1AndTrandateBetween(name,startDate,endDate);
    }

    @Override
    public List<BankBillDto> queryRemainBankBill(String tranFlow) {
        return bankBillMapper.queryRemainBankBill(tranFlow);
    }

    @Override
    public List<BankBillDto> querySKBankBillInfo(String tranFlow) {
        return bankBillMapper.querySKBankBillInfo(tranFlow);
    }

    @Override
    public List<BankBillDto> querySettlementBankBillInfo(String startTime,String endTime) {
        return bankBillMapper.querySettlementBankBillList(startTime,endTime);
    }

    @Override
    public VoucherNoAndDateDto matchVoucherNo(BankBillDto dto) {
        VoucherNoAndDateDto result = new VoucherNoAndDateDto();
        log.info("流水列表匹配云星空凭证号,流水号:{}",dto.getTranFlow());
        List<TransactionVoucherEntity> transactionVoucherList = transactionVoucherMapper.findByRelateIdAndSource(dto.getBankBillId(), ErpConstant.ONE);
        log.info("流水列表匹配云星空凭证号,通过bankBillId匹配,匹配参数:{},匹配结果:{}",dto.getBankBillId(), JSONUtil.toJsonStr(transactionVoucherList));
        if (CollUtil.isEmpty(transactionVoucherList)) {
            log.info("流水列表匹配云星空凭证号,通过bankBillId匹配,匹配参数:{},未匹配到结果", dto.getBankBillId());
            return new VoucherNoAndDateDto();
        }
        result.setVoucherNo(transactionVoucherList.stream().map(TransactionVoucherEntity::getVoucherNo).collect(Collectors.joining(",")));
        result.setVoucherDate(transactionVoucherList.stream().map(TransactionVoucherEntity::getVoucherDate).collect(Collectors.joining(",")));
        return result;
    }


    @Override
    public PageInfo<BankBillDto> queryConstructionBankBill(PageParam<BankBillQueryDto> page) {
        return PageHelper.startPage(page.getPageNum(), page.getPageSize()).doSelectPageInfo(() -> bankBillMapper.queryConstructionBankBill(page.getParam()));
    }

    @Override
    public BankBillDto checkConstructionBankBill(BankBillDto page) {
        return bankBillMapper.checkConstructionBankBill(page);
    }

    @Override
    public PageInfo<BankBillDto> queryIgnoreBankBill(PageParam<BankBillQueryDto> page) {
        return PageHelper.startPage(page.getPageNum(), page.getPageSize()).doSelectPageInfo(() -> bankBillMapper.queryIgnoreBankBill(page.getParam()));
    }

    @Override
    public BankBillDto checkIgnoreBankBill(Integer bankBillId) {
        return  bankBillMapper.checkIgnoreBankBill(bankBillId);
    }

    @Override
    public int countSpecialSerialNumbers() {
        return bankBillMapper.countSpecialSerialNumbers();
    }

    @Override
    public BankBillDto selectTranFlowForChargeLastOne(Integer bankTag){
        return bankBillMapper.selectTranFlowForChargeLastOne(bankTag);
    }
}
