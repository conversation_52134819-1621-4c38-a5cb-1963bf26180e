function chooseSnInfo(num) {
    let selVal = $("#serialNumber_" + num).val();
    if([-1,-2,-3].indexOf(Number(selVal)) > -1){
        $("#supple_code_" + num).attr("disabled", false);
    }else{
        $("#supple_code_" + num).attr("disabled", true);
        $("#supple_code_" + num).val('');
    }
}


function canelAll(obj) {
    checkLogin();
    var status = 0;
    $.each($("input[name='b_checknox']").not(':disabled'), function () {
        if ($(this).is(":checked") == false) {
            status = 1;
        }
    });
    if (status == 1) {
        $("input[name='allcheck']").prop('checked', false);
    } else {
        $("input[name='allcheck']").prop("checked", true);
    }
}

//全选
function selectall(obj) {
    checkLogin();
    if ($(obj).is(":checked")) {
        $("input[type='checkbox']").not(':disabled').prop("checked", true);
    } else {
        $("input[type='checkbox']").not(':disabled').prop('checked', false);
    }
}

async function submitInfo() {
    checkLogin();



    var checkTimes = $("#checkTimes").val();
    if (checkTimes == null || checkTimes == '') {
        layer.alert("本次验收时间不可为空");
        return false;
    }
    var checkType = $("#checkType").val();
    var recordId = $("#recordId").val();
    if (recordId.length > 30) {
        layer.alert("录音id超过30个字符")
        return false;
    }

    var snCheck = true;
    //判断补充码
    $.each($("input:checkbox[name='b_checknox']:checked").not(':disabled'), function () {
        let count = $(this).val();
        let serialNumber = $("#serialNumber_" + count).val();
        let supple_code = $("#supple_code_" + count).val();

        console.log(serialNumber)

        if (serialNumber == 0) {
            layer.alert("请维护服务商品序列号");
            snCheck = false;
            return false;
        }

        if ((serialNumber == "-1" || serialNumber == -2 || serialNumber == -3) && supple_code.length == 0) {
            layer.alert("请维护补充码");
            snCheck = false;
            return false;
        }

        if (supple_code.length > 30) {
            layer.alert("补充码码超过30个字符")
            snCheck = false;
            return false;
        }
    });

    if (!snCheck) {
        return false;
    }

    let detailList = [];
    $.each($("input:checkbox[name='b_checknox']:checked").not(':disabled'), function () {
        let count = $(this).val();
        let serialNumber = $("#serialNumber_" + count).val();
        let snSelectType = 1
        if([0,-1,-2,-3].indexOf( Number(serialNumber)) > -1 ){
            snSelectType = serialNumber;
        }
        if([0,-1,-2,-3].indexOf( Number(serialNumber)) > -1 ){
            serialNumber = ''
        }

        let AfterSalesInstallServiceRecordDetail = {
            "afterSalesGoodsId": $("#afterSalesGoodsId_" + count).val(),
            "sku": $("#sku_" + count).val(),
            "skuName": $("#skuName_" + count).val(),
            "brand": $("#brand_" + count).val(),
            "model": $("#model_" + count).val(),
            "num": "1",
            "snSelectType": snSelectType,
            "serialNumber": serialNumber,
            "supplCode": $("#supple_code_" + count).val() || ''
        }
        detailList.push(AfterSalesInstallServiceRecordDetail)
    });

    if (detailList.length == 0) {
        layer.alert("请选中本次的商品信息");
        return false;
    }



    var skus = new Set([]);
    var flag = true;
    for (let i = 0; i < detailList.length; i++) {
        let num = 0;
        var checkTimes = $("#checkTimes").val();
        let deliveryTime = $("#deliveryTime_" + i).val();
        let arrivalTime = $("#arrivalTime_" + i).val();

        if (deliveryTime != null && deliveryTime != '' && deliveryTime != 0) {
            let date = new Date(checkTimes);
            if (date.getTime() < deliveryTime) {
                num++;
            }
        }
        if (arrivalTime != null && arrivalTime != '' && arrivalTime != 0) {
            let date = new Date(checkTimes);
            if (date.getTime() < arrivalTime) {
                num++;
            }
        }
        if (num > 0) {
            skus.add($("#sku_" + i).val());
        }
    }

    if (skus.size > 0) {
        function backStop(ms) {
            let callback = null;
            layer.confirm("本次服务时间小于" + Array.from(skus).join(",") + "的出库时间或签收时间,请确认", {
                icon: 3,
                title: '提示'
            }, function (index) {
                if (typeof callback == 'function') callback();
                flag = true;
                layer.close(index);
            }, function (index) {
                if (typeof callback == 'function') callback();
                flag = false;
                layer.close(index);
            });
            return new Promise(resolve => callback = resolve);
        }

        await backStop();
    }

    if (!flag) {
        return false;
    }
    var checkConclusion = $("#checkConclusion").val();
    var afterSalesId = $("#afterSalesId").val();
    var fileNames = $("#fileNames").val();
    var fileUris = $("#fileUris").val();

    let dto = {
        checkDate: checkTimes,
        checkType: checkType,
        recordId: recordId,
        checkConclusion: checkConclusion,
        afterSalesId: afterSalesId,
        fileNames: fileNames,
        fileUris: fileUris,
        detail: detailList
    }
    $.ajax({
        url: page_url + '/order/afterSalesCommon/saveAddServiceRecord.do',
        data: JSON.stringify(dto),
        type: "POST",
        contentType: "application/json",
        dataType: "json",
        async: false,
        success: function (data) {
            if (data.code == 0) {
                window.parent.location.reload();
            } else {
                layer.alert(data.message);
            }
        },
        error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })
}
