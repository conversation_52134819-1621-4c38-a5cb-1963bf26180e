package com.vedeng.common.core.domain;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description 页面按钮对象
 * @date 2023/10/20 9:23
 **/
@Getter
@Setter
public class Button {

    /**
     * 自定义的编号
     */
    private Integer button;

    /**
     * 按钮名
     */
    private String buttonName;

    /**
     * 是否显示
     */
    private boolean show = true;

    /**
     * 是否需要确认
     */
    private boolean needConfirm = true;

    /**
     * 按钮触发的路径
     */
    private String url;

    /**
     * 0 接口 无页面
     * 1 弹窗
     * 2 新页面
     */
    private Integer viewType=0;

}
