$(function () {
    var checkSelectAll = function () {
        var selectFlag = true;

        $('.J-select-spu').each(function () {
            if (!$(this)[0].checked) {
                selectFlag = false;
            }
        })

        $('.J-select-list-all')[0].checked = selectFlag;
    };

    var checkOutput = function () {
        var len = $('.J-select-spu:checked').length;

        if (len > 0) {
            $('.J-prod-export').removeClass('btn-disabled');
        } else {
            $('.J-prod-export').addClass('btn-disabled');
        }
    };

    //表格全选复选框
    $('.J-select-list-all').change(function () {
        var $this = $(this);

        $('.J-select-spu').each(function () {
            $(this)[0].checked = $this[0].checked;
        })

        checkOutput();
    });

    $('.J-select-spu').change(function () {
        checkSelectAll();
        checkOutput();
    })


    //
    $('.J-export-record').click(function () {
        var selectSpu = $('.J-select-spu:checked');
        var selectSpuCount = selectSpu.length
        if (selectSpuCount === 0) {
            layer.alert("请选择要导出的记录信息");
            return false;
        }
        var currVal = [];

        for (var i = 0; i < selectSpuCount; i++) {
            currVal[i] = selectSpu[i].value
        }

        location.href = page_url + '/goods/vgoods/exportGoodsRemovalRecord.do?spuRemovalRecordIds=' + currVal;

    });

    var initPager = function ($wrap, page) {
        $wrap.attr('cz-page', page.pageNo);
        $wrap.find('.J-page-txt').html(page.pageNo);
        $wrap.find('.J-page-txt-total').html(page.totalPage);

        if (page.totalRecord <= 5) {
            $wrap.hide();
        } else {
            if (page.pageNo == 1) {
                $('.J-page-prev', $wrap).addClass('disabled');
            } else {
                $('.J-page-prev', $wrap).removeClass('disabled');
            }

            if (page.pageNo == page.totalPage) {
                $('.J-page-next', $wrap).addClass('disabled');
            } else {
                $('.J-page-next', $wrap).removeClass('disabled');
            }
        }
    };

    var turnPage = function ($wrap, page) {
        var spuId = $wrap.data('spuid');
        var total = Math.ceil($wrap.data('total') / 5);
        var lv = $wrap.data('lv');
        var wiki = $wrap.data('spuwiki');
        var spuName = $wrap.data('spuname');

        var spuInfo = {
            spuId: spuId,
            spulv: lv,
            spuWiki: wiki,
            spuName: spuName,
            auth: $wrap.data('auth'),
            tempauth: $wrap.data('tempauth')
        };

        if (!(page < 1 || page > total)) {
            $.ajax({
                url: page_url + '/goods/vgoods/listSku.do',
                data: {
                    pageNo: page,
                    pageSize: 5,
                    spuId: spuId
                },
                dataType: 'json',
                success: function (res) {
                    if (res.code == 0) {
                        $wrap.siblings('.J-sku-item').remove();
                        $wrap.before(skuListTmpl($.extend({}, res.data, spuInfo)));

                        initPager($wrap, res.data.page);
                    }
                }
            })
        }
    };

    $('.J-page-prev').click(function () {
        var $wrap = $(this).parents('.J-page-wrap:first');
        var page = parseInt($wrap.attr('cz-page') || 1);

        turnPage($wrap, page - 1);
    });

    $('.J-page-next').click(function () {
        var $wrap = $(this).parents('.J-page-wrap:first');
        var page = parseInt($wrap.attr('cz-page') || 1);

        turnPage($wrap, page + 1);
    });

    $('.J-page-wrap').each(function () {
        var total = $(this).data('total');

        $(this).find('.J-page-txt-total').html(Math.ceil(total / 5));
    })

    //操作提示
    GLOBAL.showGlobalTip('操作成功', null, 'spuListViewOption');

});
