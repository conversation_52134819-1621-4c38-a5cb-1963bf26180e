package com.vedeng.crm.visitrecord.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.enums.JumpErpTitleEnum;
import com.vedeng.crm.business.quote.domain.dto.SendMessageDto;
import com.vedeng.crm.visitrecord.domain.dto.VisitEditInputDto;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.dto.TraderUserDto;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/10
 */
@Service("CrmVisitSubmitRecordMessageServiceImpl")
public class CrmVisitSubmitRecordMessageServiceImpl extends BaseMessageSendServiceImpl {




    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;



    public void sendMessage(VisitRecordVo visitRecordVo, Integer recordId, List<Integer>  tongxingIdsList,VisitEditInputDto visitEditInputDto, CurrentUser currentUser) {

        //创建拼接消息，消息模板如下（没有同行人时，消息通知内无同行人字段）：
//        拜访编号：
//        拜访客户：
//        拜访记录：Y
//        提交时间：
        StringBuilder messageBuilder = new StringBuilder();
        messageBuilder.append("拜访编号：").append(visitRecordVo.getVisitRecordNo()).append("\n");
        messageBuilder.append("拜访客户：").append(visitRecordVo.getCustomerName()).append("\n");
        messageBuilder.append("拜访记录：").append(visitEditInputDto.getCustomerRequires()).append("\n");
        messageBuilder.append("提交时间：").append(DateUtil.format(visitRecordVo.getPlanVisitDate(),"yyyy-MM-dd HH:mm:ss") );
        String message = messageBuilder.toString();

        List<Integer> userIds = new ArrayList<>();
        userIds.add(visitRecordVo.getVisitorId());
        if(CollectionUtil.isNotEmpty(tongxingIdsList)) {
            userIds.addAll(tongxingIdsList);//同行人
        }
        userIds.add(visitRecordVo.getAddUserId());//创建人
        if(visitRecordVo.getTraderId() != null ){
            //查询归属销售，如果归属销售不在userIds中，则添加到userIds
            TraderUserDto traderUserDto =  traderCustomerBaseService.getTraderUserByTraderId(visitRecordVo.getTraderId());
            Integer belongerId = traderUserDto==null?null:traderUserDto.getUserId();
            if(belongerId!= null &&  !currentUser.getId().equals(belongerId)){
                //加入归属销售
                userIds.add(belongerId);
            }
        }
//        userIds.remove(currentUser.getId());//最后将当前人去掉
        userIds.removeAll(Collections.singleton(currentUser.getId()));//消息的发送人，去掉当前的操作人，即创建人

        if(!CollectionUtil.isEmpty(userIds)){
            List<Integer> belongerIdList = new ArrayList<>( );
            belongerIdList.addAll(userIds);
            List<UserDto> userDtoSendList = getUserInfoByUserIds(belongerIdList);
            //获取这些人的工号
            List<String> visitJobNumList = CollectionUtil.isEmpty(userDtoSendList)?new ArrayList<>()
                    :(userDtoSendList.stream().map(UserDto::getNumber).collect(Collectors.toList())) ;
            for(String jobNumber:visitJobNumList){
                SendMessageDto sendMessageDto = new SendMessageDto();
                sendMessageDto.setUrl(jumpService.getMjumpUrl(crmApplicationMessageJumpUrl + "/crm/visitRecord/m/detail?id=" + recordId,
                        lxcrmUrl + "/crm/visitRecord/profile/detail?id=" + recordId,
                        JumpErpTitleEnum.VISIT_RECORD_DETAIL));
                sendMessageDto.setUserNumber(jobNumber);
                sendMessageDto.setFormat(message);
                sendCardMsg(sendMessageDto, currentUser.getUsername()+"提交了拜访记录");
            }
        }



    }
}
