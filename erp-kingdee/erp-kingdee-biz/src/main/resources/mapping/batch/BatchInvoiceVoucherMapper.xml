<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchInvoiceVoucherMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchInvoiceVoucherDto">
        <!--@mbg.generated-->
        <!--@Table T_INVOICE_VOUCHER-->
        <id column="INVOICE_VOUCHER_ID" jdbcType="BIGINT" property="invoiceVoucherId"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="INVOICE_ID" jdbcType="VARCHAR" property="invoiceId"/>
        <result column="VOUCHER_NO" jdbcType="VARCHAR" property="voucherNo"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        INVOICE_VOUCHER_ID,
        ADD_TIME,
        MOD_TIME,
        CREATOR,
        UPDATER,
        CREATOR_NAME,
        UPDATER_NAME,
        INVOICE_ID,
        VOUCHER_NO,
        IS_DELETE
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE_VOUCHER
        where INVOICE_VOUCHER_ID = #{invoiceVoucherId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from T_INVOICE_VOUCHER
        where INVOICE_VOUCHER_ID = #{invoiceVoucherId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="INVOICE_VOUCHER_ID" keyProperty="invoiceVoucherId"
            parameterType="com.vedeng.erp.kingdee.batch.dto.BatchInvoiceVoucherDto" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_INVOICE_VOUCHER (ADD_TIME, MOD_TIME, CREATOR,
                                       UPDATER, CREATOR_NAME, UPDATER_NAME,
                                       INVOICE_ID, VOUCHER_NO, IS_DELETE)
        values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
                #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR},
                #{invoiceId,jdbcType=VARCHAR}, #{voucherNo,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyColumn="INVOICE_VOUCHER_ID" keyProperty="invoiceVoucherId"
            parameterType="com.vedeng.erp.kingdee.batch.dto.BatchInvoiceVoucherDto" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_INVOICE_VOUCHER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="creatorName != null">
                CREATOR_NAME,
            </if>
            <if test="updaterName != null">
                UPDATER_NAME,
            </if>
            <if test="invoiceId != null">
                INVOICE_ID,
            </if>
            <if test="voucherNo != null">
                VOUCHER_NO,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="invoiceId != null">
                #{invoiceId,jdbcType=VARCHAR},
            </if>
            <if test="voucherNo != null">
                #{voucherNo,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert">
        <!--@mbg.generated-->
        insert into T_INVOICE_VOUCHER
        (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, INVOICE_ID, VOUCHER_NO,
        IS_DELETE)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
            #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR},
            #{item.updaterName,jdbcType=VARCHAR},
            #{item.invoiceId,jdbcType=VARCHAR}, #{item.voucherNo,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=INTEGER})
        </foreach>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchInvoiceVoucherDto">
        <!--@mbg.generated-->
        update T_INVOICE_VOUCHER
        <set>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="invoiceId != null">
                INVOICE_ID = #{invoiceId,jdbcType=VARCHAR},
            </if>
            <if test="voucherNo != null">
                VOUCHER_NO = #{voucherNo,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=INTEGER},
            </if>
        </set>
        where INVOICE_VOUCHER_ID = #{invoiceVoucherId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchInvoiceVoucherDto">
        <!--@mbg.generated-->
        update T_INVOICE_VOUCHER
        set ADD_TIME     = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME     = #{modTime,jdbcType=TIMESTAMP},
            CREATOR      = #{creator,jdbcType=INTEGER},
            UPDATER      = #{updater,jdbcType=INTEGER},
            CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            INVOICE_ID   = #{invoiceId,jdbcType=VARCHAR},
            VOUCHER_NO   = #{voucherNo,jdbcType=VARCHAR},
            IS_DELETE    = #{isDelete,jdbcType=INTEGER}
        where INVOICE_VOUCHER_ID = #{invoiceVoucherId,jdbcType=BIGINT}
    </update>


    <!--auto generated by MybatisCodeHelper on 2023-04-13-->
    <select id="findByInvoiceIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE_VOUCHER
        where INVOICE_ID in
        <foreach item="item" index="index" collection="invoiceIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>



<!--auto generated by MybatisCodeHelper on 2023-06-05-->
    <select id="selectByInvoiceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE_VOUCHER
        where INVOICE_ID=#{invoiceId,jdbcType=VARCHAR}
    </select>
</mapper>