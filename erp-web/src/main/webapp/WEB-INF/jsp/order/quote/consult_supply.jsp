<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="向供应链询价" scope="application" />
<%@ include file="../../common/common.jsp"%>


<div class="main-container">
    <div id="selectTerminalInfo">
        <input id="consultRelatedId" value="${consultRelatedId}" type="hidden" />
        <input id="consultType" value="${consultType}" type="hidden" />
        <span id="span_isAdvanceTax" style="margin-bottom: 12px;float:none; font-weight: bold">选择询价内容</span>

        <table class="table" style="margin-bottom:0px;">
            <thead>
            <tr>
                <th hidden="hidden">商品ID</th>
                <th class="wid16">产品名称</th>
                <th class="wid10">品牌</th>
                <th class="wid10">型号</th>
                <th class="wid6">单位</th>
                <th >咨询内容</th>
            </tr>
            </thead>
            <tbody  class="goodsOpt" id="table_items">
            <c:forEach var="item" items="${consultGoodsList}">
                <tr>
                    <td hidden="hidden" name="quoteorderGoods">${item.quoteorderGoodsId}</td>
                    <input type="hidden" id="sku_${item.quoteorderGoodsId}" value="${item.sku}" />
                    <td>${item.goodsName}</td>
                    <td>${item.brandName}</td>
                    <td>${item.model}</td>
                    <td>${item.unitName}</td>
                    <td style="text-align: left">
                        <c:choose>
                            <c:when test="${empty item.referencePrice}">
                                <p><input type="checkbox" id="consult_price_${item.quoteorderGoodsId}" value="" checked="checked" />报价</p>
                            </c:when>
                            <c:otherwise>
                                <p><input type="checkbox" id="consult_price_${item.quoteorderGoodsId}" value=""  />报价 &nbsp;&nbsp;参考价：${item.referencePrice}</p>
                            </c:otherwise>
                        </c:choose>

                        <c:choose>
                            <c:when test="${(item.warehouseStock != null and item.warehouseStock.availableStockNum > 0 and item.warehouseStock.availableStockNum >= item.num) or (not empty item.deliveryCycle)}">
                                <p>
                                    <input type="checkbox" id="consult_delivery_${item.quoteorderGoodsId}" value=""  />
                                    货期&nbsp;&nbsp;
                                    <c:choose>
                                        <c:when test="${item.warehouseStock != null and item.warehouseStock.availableStockNum > 0 and item.warehouseStock.availableStockNum >= item.num}">
                                            可用库存/库存量：${item.warehouseStock.availableStockNum}/${item.warehouseStock.stockNum}
                                        </c:when>
                                        <c:otherwise>
                                            参考货期：${item.deliveryCycle}天
                                        </c:otherwise>
                                    </c:choose>
                                </p>
                            </c:when>
                            <c:otherwise>
                                <p><input type="checkbox" id="consult_delivery_${item.quoteorderGoodsId}" value="" checked="checked" />货期
                            </c:otherwise>
                        </c:choose>

                      <%--  <p><input type="checkbox" id="consult_report_${item.quoteorderGoodsId}" value="" />报备情况</p>--%>
                        <p><label>其他</label> <input type="text" id="consult_other_content_${item.quoteorderGoodsId}" style="width: 90%; " name="" value="" placeholder="请输入咨询内容" /> </p>
                    </td>
                </tr>
            </c:forEach>

            <c:if test="${empty consultGoodsList}">
                <!-- 查询无结果弹出 -->
                <tr>
                    <td colspan="10">暂无商品信息</td>
                </tr>
            </c:if>

            </tbody>
        </table>
        <input id="quoteorderId" type="hidden" value="${quoteInfo.quoteorderId}">
        <input id="isNeedReport" type="hidden" value="${isNeedReport}">
        <input id="terminalType" type="hidden" value="${quoteInfo.terminalType}">
        <input id="salesAreaId" type="hidden" value="${quoteInfo.salesAreaId}">
        <input id="salesArea" type="hidden" value="${quoteInfo.salesArea}">
        <input id="terminalTraderName" type="hidden" value="${quoteInfo.terminalTraderName}">
    </div>

    <div class='clear'></div>
    <div id="" class="mb15" style="margin-top: 12px;">
        <div class="add-tijiao tcenter">
            <button type="submit" onclick="submit()">提交</button>
            <button class="dele" type="button" id="close-layer">取消</button>
        </div>
    </div>
</div>
<script>
    function submit() {
        var consultSkuList = [];
        var result = true;
        $("td[name='quoteorderGoods']").each(function (index,dom) {
            var consultSku = {};
            var quoteorderGoodsId = dom.innerHTML;
            var consultPrice = false;
            var consultDelivery = false;
            var consultReport = false;
            var consultOther = false;
            consultSku.quoteorderGoodsId = quoteorderGoodsId;
            consultSku.sku = $('#sku_'+quoteorderGoodsId).val();
            if ($("#consult_price_"+quoteorderGoodsId).prop('checked')){
                consultPrice = true;
                consultSku.consultReferencePrice = true;
            }
            if ($("#consult_delivery_"+quoteorderGoodsId).prop('checked')){
                consultDelivery = true;
                consultSku.consultDeliveryCycle = true;
            }
            if ($("#consult_report_"+quoteorderGoodsId).prop('checked')){
                consultReport = true;
                consultSku.consultReport = true;
            }
            if ($("#consult_other_content_"+quoteorderGoodsId).val().length > 0){
                consultOther = true;
                consultSku.consultOther = true;
                consultSku.consultOtherContent = $("#consult_other_content_"+quoteorderGoodsId).val();
            }
            if (consultPrice || consultDelivery || consultReport || consultOther){
                consultSkuList.push(consultSku);
            }
        });
        if (result && consultSkuList.length == 0){
            layer.alert("咨询内容不得为空");
            result = false;
        }

        if (result){
            var checkAuthorization = checkCompleteAuthorization();

            if (checkAuthorization == 0){
                result = false;
                layer.open({
                    type: 2,
                    title: '维护终端信息',
                    area: ['100%', '100%'],
                    shade: 0.4,
                    content: '/order/quote/terminalInfoInit.do?consultRelatedId='+ $('#quoteorderId').val() +'"',
                });

            }
        }


        if (result){
            $.ajax({
                url: '/order/quote/consult/save.do',
                contentType: 'application/json',
                data: JSON.stringify({
                    consultRelatedId: Number($("#consultRelatedId").val()),
                    consultType: Number($("#consultType").val()),
                    skuConsults: consultSkuList
                }),
                type:"POST",
                dataType : "json",
                success: function(data){
                    layer.alert(data.message, {
                        icon : (data.code == 0 ? 1 : 2)
                    }, function() {
                        if (parent.layer != undefined) {
                            parent.layer.close(index);
                        }
                        parent.location.reload();
                    });
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            })
        }
    }

    /**
     * 检查报备信息是否完整
     */
    function checkCompleteAuthorization() {
        var isNeedReport = $('#isNeedReport').val();
        var salesAreaId = $('#salesAreaId').val();
        var salesArea = $('#salesArea').val();
        var terminalType = $('#terminalType').val();
        var terminalTraderName = $('#terminalTraderName').val();
        //是否需要报备
        if (isNeedReport == 0){
            return 1;
        }
        //报备区域
        if (salesAreaId == undefined || salesAreaId == null || (salesAreaId == 0 && salesArea != '未知')){
            return 0;
        }
        //报备类型
        if (terminalType == undefined || terminalType == null || terminalType == 0){
            return 0;
        }
        //终端报备类型
        if (terminalTraderName == undefined || terminalTraderName == null || terminalTraderName == ''){
            return 0;
        }
        return 1;
    }

    function viewTraderFunc() {
       parent.viewTraderInfoClick();
    }
</script>
<%@ include file="../../common/footer.jsp"%>
