package com.vedeng.docSync.service;

import com.alibaba.fastjson.JSON;
import com.newtask.celery.CeleryAsync;
import com.newtask.celery.model.CeleryQueueEnum;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.util.FileUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.doc.api.docsupplier.command.SupplierDocCommand;
import com.vedeng.doc.api.dto.DataResult;
import com.vedeng.docSync.model.pojo.DocBuzTagExtDo;
import com.vedeng.docSync.model.pojo.DocFileExtDo;
import com.vedeng.docSync.model.pojo.DocRBuzTagJFileExtDo;
import com.vedeng.docSync.model.pojo.DocSupplierExtDo;
import com.vedeng.system.service.FtpUtilService;
import com.vedeng.trader.dao.TraderCertificateMapper;
import com.vedeng.trader.dao.TraderSupplierMapper;
import com.vedeng.trader.feign.RemoteDocSupplierApiService;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.model.vo.TraderCertificateVo;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;
import java.util.Objects;

/**
 * @Author: Cherny.chen
 * @Create: 2021/6/4 10:19
 */

@Service
public class SyncSupplierService extends BaseService {

    private final static Logger logger = LoggerFactory.getLogger(SyncSupplierService.class);

    @Resource
    private TraderCertificateMapper traderCertificateMapper;

    @Resource
    private TraderSupplierMapper traderSupplierMapper;

    @Autowired
    private FtpUtilService ftpUtilService;

    @Value("${bussiness_license_str}")
    private String bussinessLicenseStr;

    @Value("${product_license_str}")
    private String productLicenseStr;

    @Value("${product_registration_form_str}")
    private String productRegistrationFormStr;

    @Value("${business_permit_str}")
    private String businessPermitStr;

    @Value("${second_class_record_str}")
    private String secondClassRecordStr;

    @Value("${certificate_authorization_str}")
    private String certificateAuthorizationStr;

    @Value("${class_first_medical_devices_str}")
    private String classFirstMedicalDevicesStr;

    @Value("${supplier_jump_url}")
    private String supplierJumpUrl;

    @Value("${ftp_file_url}")
    private String ftpFileUrl;
    @Autowired
    private RemoteDocSupplierApiService remoteDocSupplierApiService;

    @Transactional(rollbackFor = Exception.class)
    @CeleryAsync(queue = CeleryQueueEnum.SYNC_DOC)
    public void syncSupplier2Doc(String path, Integer key) {

        //获取厂商的信息
        TraderSupplier traderSupplier = traderSupplierMapper.selectByPrimaryKey(key);

        if (Objects.isNull(traderSupplier)) {
            logger.warn("获取厂商信息失败 , key :{}", key);
        }

        //判断资料库是否有相关厂商
        List<DocSupplierExtDo> docSupplierExtDoList = docSyncMapper.getDocSupplierByTraderSupplier(traderSupplier.getTraderId());
        if (CollectionUtils.isEmpty(docSupplierExtDoList)) {
            logger.info("同步资料库厂商 不存在 {}" ,traderSupplier.getTraderSupplierName());
            SupplierDocCommand supplierDocCommand = new SupplierDocCommand();
            supplierDocCommand.setSupplierId(traderSupplier.getTraderId());
            supplierDocCommand.setSupplierName(traderSupplier.getTraderSupplierName());
            supplierDocCommand.setUserId(traderSupplier.getCreator());
            try {
                DataResult dataResult = remoteDocSupplierApiService.addDocSupplier(supplierDocCommand);
                if (dataResult.getCode() != 0) {
                    logger.error("同步资料库厂商 不存在 ，同步失败 {}" ,traderSupplier.getTraderSupplierName());
                    return;
                }
            }catch (Exception e){
                logger.error("同步资料库厂商 不存在 ，同步失败 {}" ,traderSupplier.getTraderSupplierName(),e);
                return;
            }

        }
        logger.info("同步资料库厂商 不存在 ，同步新增成功 {}" ,traderSupplier.getTraderSupplierName());
        //获取厂商所有标签
        DocSupplierExtDo docSupplierExtDo = docSupplierExtDoList.get(0);
        List<DocBuzTagExtDo> docBuzTagExtDoList = docSyncMapper.listBuzTagByBuzType(2);

        if (CollectionUtils.isEmpty(docBuzTagExtDoList)) {
            logger.error("同步资料库厂商 标签不存在 ，同步失败 {}" ,traderSupplier.getTraderSupplierName() );
            return;
        }

        TraderCertificateVo traderCertificateVo = new TraderCertificateVo();
        traderCertificateVo.setTraderId(docSupplierExtDo.getSupplierId());
        traderCertificateVo.setTraderType(ErpConst.TWO);
        for (DocBuzTagExtDo docBuzTagExtDo : docBuzTagExtDoList) {
            logger.info("同步资料库厂商 标签 " + key + " 类型：" + docBuzTagExtDo.getBuzTagName());
            //营业执照
            if (docBuzTagExtDo.getBuzTagName().equals(bussinessLicenseStr)) {
                traderCertificateVo.setSysOptionDefinitionId(SysOptionConstant.ID_25);
                List<TraderCertificateVo> traderCertificateVoList = traderCertificateMapper.getTraderCertificateVoListByIdAndType(traderCertificateVo);
                if (CollectionUtils.isNotEmpty(traderCertificateVoList)) {
                    this.dealWithValidFileList(path, docSupplierExtDo, docBuzTagExtDo, traderCertificateVoList);
                }

            }
            //生产许可
            if (docBuzTagExtDo.getBuzTagName().equals(productLicenseStr)) {
                traderCertificateVo.setSysOptionDefinitionId(SysOptionConstant.ID_439);
                List<TraderCertificateVo> traderCertificateVoList = traderCertificateMapper.getTraderCertificateVoListByIdAndType(traderCertificateVo);
                if (CollectionUtils.isNotEmpty(traderCertificateVoList)) {
                    this.dealWithValidFileList(path, docSupplierExtDo, docBuzTagExtDo, traderCertificateVoList);
                }
            }
            //生产产品登记表
            if (docBuzTagExtDo.getBuzTagName().equals(productRegistrationFormStr)) {
                traderCertificateVo.setSysOptionDefinitionId(SysOptionConstant.ID_1102);
                List<TraderCertificateVo> traderCertificateVoList = traderCertificateMapper.getTraderCertificateVoListByIdAndType(traderCertificateVo);
                if (CollectionUtils.isNotEmpty(traderCertificateVoList)) {
                    this.dealWithValidFileList(path, docSupplierExtDo, docBuzTagExtDo, traderCertificateVoList);
                }
            }
            //经营许可(三类医疗资质)
            if (docBuzTagExtDo.getBuzTagName().equals(businessPermitStr)) {
                traderCertificateVo.setSysOptionDefinitionId(SysOptionConstant.ID_29);
                List<TraderCertificateVo> traderCertificateVoList = traderCertificateMapper.getTraderCertificateVoListByIdAndType(traderCertificateVo);
                if (CollectionUtils.isNotEmpty(traderCertificateVoList)) {
                    this.dealWithValidFileList(path, docSupplierExtDo, docBuzTagExtDo, traderCertificateVoList);
                }
            }
            //二类备案
            if (docBuzTagExtDo.getBuzTagName().equals(secondClassRecordStr)) {
                traderCertificateVo.setSysOptionDefinitionId(SysOptionConstant.ID_28);
                List<TraderCertificateVo> traderCertificateVoList = traderCertificateMapper.getTraderCertificateVoListByIdAndType(traderCertificateVo);
                if (CollectionUtils.isNotEmpty(traderCertificateVoList)) {
                    this.dealWithValidFileList(path, docSupplierExtDo, docBuzTagExtDo, traderCertificateVoList);
                }
            }
            //销售人员授权书
            if (docBuzTagExtDo.getBuzTagName().equals(certificateAuthorizationStr)) {
                traderCertificateVo.setSysOptionDefinitionId(SysOptionConstant.ID_1100);
                List<TraderCertificateVo> traderCertificateVoList = traderCertificateMapper.getTraderCertificateVoListByIdAndType(traderCertificateVo);
                if (CollectionUtils.isNotEmpty(traderCertificateVoList)) {
                    this.dealWithValidFileList(path, docSupplierExtDo, docBuzTagExtDo, traderCertificateVoList);
                }
            }
            //第一类医疗器械生产备案凭证
            if (docBuzTagExtDo.getBuzTagName().equals(classFirstMedicalDevicesStr)) {
                traderCertificateVo.setSysOptionDefinitionId(SysOptionConstant.ID_1101);
                List<TraderCertificateVo> traderCertificateVoList = traderCertificateMapper.getTraderCertificateVoListByIdAndType(traderCertificateVo);
                if (CollectionUtils.isNotEmpty(traderCertificateVoList)) {
                    this.dealWithValidFileList(path, docSupplierExtDo, docBuzTagExtDo, traderCertificateVoList);
                }
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void dealWithValidFileList(String path, DocSupplierExtDo docSupplierExtDo, DocBuzTagExtDo docBuzTagExtDo, List<TraderCertificateVo> traderCertificateVoList) {

        Integer buzId = docSupplierExtDo.getId();
        Integer buzTagId = docBuzTagExtDo.getId();

        List<DocRBuzTagJFileExtDo> docBuzTagFileExtDoList = docSyncMapper.listBuzTagFileForSupplier(buzId, buzTagId);
        //清除旧数据
        logger.info("同步资料库厂商  dealWithValidFileList size={}  buzId={} buzTagId={} getBuzTagName={}  ", traderCertificateVoList.size(), buzId, buzTagId, docBuzTagExtDo.getBuzTagName());
        super.clearOldData(docBuzTagFileExtDoList);
        //增加新文件
        for (TraderCertificateVo traderCertificateVo : traderCertificateVoList) {
            try {
                String fileUrl = traderCertificateVo.getDomain() + traderCertificateVo.getUri();
                if (StringUtils.isBlank(traderCertificateVo.getUri())) {
                    logger.info("同步资料库厂商    " + buzId + " \t uri为空，忽略 " + traderCertificateVo.getName());
                    continue;
                }
                String http = FileUtil.getHttp(fileUrl);
                DocFileExtDo docFileExtDo = new DocFileExtDo();
                docFileExtDo.setAddTime(traderCertificateVo.getAddTime());
                docFileExtDo.setCreator(traderCertificateVo.getCreator());
                docFileExtDo.setModTime(traderCertificateVo.getModTime());
                docFileExtDo.setUpdater(traderCertificateVo.getUpdater());
                docFileExtDo.setSyncTime(System.currentTimeMillis());
                docFileExtDo.setOriginLink(http + fileUrl);
                if (docBuzTagExtDo.getTransferOssMobile()) {
                    logger.info("掌上小贝: erp文件同步掌上小贝文件,文件链接[{}},资料标签[{}]", docFileExtDo.getOriginLink(), JSON.toJSONString(docBuzTagExtDo));
                    docFileExtDo.setOssLinkMobile(docFileExtDo.getOriginLink());
                    docFileExtDo.setSyncOssMobile(true);
                }
                // 如果是oss,需要上传到ftp.
                DocFileExtDo exitDocFileExtDoByOriginUrl = docSyncMapper.getDocFileByOriginUrl(http + fileUrl);
                Integer docFileId = null;
                if (exitDocFileExtDoByOriginUrl != null) {
                    logger.info("同步资料库厂商   资料库已经存在此文件 " + buzId + " \t" + buzTagId + "\t" + docBuzTagExtDo.getBuzTagName());
                    docFileId = exitDocFileExtDoByOriginUrl.getFileId();
                } else {
                    logger.info("同步资料库厂商   资料库不存在此文件 开始下载 " + buzId + " \t" + buzTagId + "\t" + docBuzTagExtDo.getBuzTagName() + http + fileUrl);
                    FileUtil.FileInfo fileInfoInner = FileUtil.getFile(path, http + fileUrl);
                    logger.info("同步资料库厂商   资料库不存在此文件  下载成功 " + buzId + " \t" + buzTagId + "\t" + docBuzTagExtDo.getBuzTagName() + http + fileUrl);
                    File uploadFile = fileInfoInner.getFile();
                    FileInfo fileInfo = ftpUtilService.exeUploadFileToFtp(uploadFile.getPath(), "/upload/ajax", uploadFile.getName());
                    logger.info("同步资料库厂商   资料库不存在此文件  上传至FTP成功 " + buzId + " \t" + buzTagId + "\t" + docBuzTagExtDo.getBuzTagName() + http + fileUrl);
                    if (StringUtil.isNotEmpty(fileUrl) && (fileUrl.indexOf("resourceId") != -1 || fileUrl.indexOf("file1.vedeng.com") != -1)) {
                        docFileExtDo.setDomain(ftpFileUrl);
                        docFileExtDo.setUri(fileInfo.getFilePath() + "/" + fileInfo.getFileName());
                        docFileExtDo.setSuffix(fileInfo.getPrefix());
                    } else {
                        docFileExtDo.setDomain(http + traderCertificateVo.getDomain());
                        docFileExtDo.setUri(traderCertificateVo.getUri());
                        docFileExtDo.setSuffix(StringUtils.isNotBlank(traderCertificateVo.getSuffix()) ? traderCertificateVo.getSuffix() : "jpg");
                    }
                    docFileExtDo.setMd5(fileInfoInner.getMd5());

                    //MD5校验
                    DocFileExtDo exitDocFileExtDo = docSyncMapper.getDocFileByMd5(fileInfoInner.getMd5());

                    if (exitDocFileExtDo == null) {
                        logger.info("新增文件同步到doc");
                        docSyncMapper.saveDocFile(docFileExtDo);
                        exitDocFileExtDo = docFileExtDo;
                    }
                    docFileId = exitDocFileExtDo.getFileId();
                }
                try {
                    if (docFileId != null) {
                        logger.info("更新文件同步到doc");
                        docFileExtDo.setFileId(docFileId);
                        docSyncMapper.updateDocFileOriginLink(docFileExtDo);
                    }
                } catch (Exception e) {
                    logger.error("更新文件同步到doc失败", e);
                }
                //增加关联表记录
                DocRBuzTagJFileExtDo docBuzTagFileExtDo = new DocRBuzTagJFileExtDo();
                docBuzTagFileExtDo.setFileId(docFileId);
                docBuzTagFileExtDo.setFileName(StringUtils.isBlank(traderCertificateVo.getName()) ? "-" : traderCertificateVo.getName());
                docBuzTagFileExtDo.setBuzType(2);
                docBuzTagFileExtDo.setBuzId(docSupplierExtDo.getId());
                docBuzTagFileExtDo.setBuzTagId(docBuzTagExtDo.getId());
                if (traderCertificateVo.getBegintime() != null && traderCertificateVo.getBegintime() == 0) {
                    docBuzTagFileExtDo.setValidStartTime(null);
                } else {
                    docBuzTagFileExtDo.setValidStartTime(traderCertificateVo.getBegintime());
                }
                if (traderCertificateVo.getEndtime() != null && traderCertificateVo.getEndtime() == 0) {
                    docBuzTagFileExtDo.setValidEndTime(null);
                } else {
                    docBuzTagFileExtDo.setValidEndTime(traderCertificateVo.getEndtime());
                }
                docBuzTagFileExtDo.setHasStamp(1);
                docBuzTagFileExtDo.setExternalUrl(erpUrl + supplierJumpUrl + docSupplierExtDo.getSupplierId());
                docBuzTagFileExtDo.setAddTime(traderCertificateVo.getAddTime());
                docBuzTagFileExtDo.setCreator(traderCertificateVo.getCreator());
                docBuzTagFileExtDo.setModTime(traderCertificateVo.getModTime());
                docBuzTagFileExtDo.setUpdater(traderCertificateVo.getUpdater());

                if (docSyncMapper.saveBuzTagJFile(docBuzTagFileExtDo) > 0) {
                    logger.info("同步资料库厂商 {} erp保存厂商资质资料文件关系表成功, Do: {}", buzId, docBuzTagFileExtDo);
                } else {
                    logger.info("同步资料库厂商 {} erp保存厂商资质文件关系表失败，Do: {}", buzId, docBuzTagFileExtDo);
                }
                //增加操作表记录
                super.saveActionLog(docSupplierExtDo.getId(), 2, docBuzTagExtDo.getId(),
                        docBuzTagFileExtDo.getId(), traderCertificateVo.getUpdater() == null ? traderCertificateVo.getCreator() : traderCertificateVo.getUpdater());
            } catch (Exception e) {
                logger.error("同步资料库厂商  " + buzId, e);
            }
        }


    }
}
