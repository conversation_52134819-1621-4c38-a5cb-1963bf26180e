package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeBuyOrderContractCommand;
import com.vedeng.erp.kingdee.domain.command.KingDeeSaleorderContractCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeBuyOrderContractEntity;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSaleorderContractEntity;
import com.vedeng.erp.kingdee.dto.KingDeeBuyOrderContractDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleorderContractDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeBuyOrderContractCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeBuyOrderContractConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleorderContractCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeSaleorderContractConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeBuyOrderContractRepository;
import com.vedeng.erp.kingdee.repository.KingDeeSaleorderContractRepository;
import com.vedeng.erp.kingdee.service.KingDeeBuyOrderContractApiService;
import com.vedeng.erp.kingdee.service.KingDeeBuyOrderContractService;
import com.vedeng.erp.kingdee.service.KingDeeSaleorderContractApiService;
import com.vedeng.erp.kingdee.service.KingDeeSaleorderContractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class KingDeeBuyOrderContractServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeBuyOrderContractEntity,
        KingDeeBuyOrderContractDto,
        KingDeeBuyOrderContractCommand,
        KingDeeBuyOrderContractRepository,
        KingDeeBuyOrderContractConvertor,
        KingDeeBuyOrderContractCommandConvertor> implements KingDeeBuyOrderContractService, KingDeeBuyOrderContractApiService {


}
