package com.vedeng.todolist.dto;

import lombok.Data;
import org.olap4j.impl.ArrayMap;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: daniel
 * @Date: 2020/12/15 09 55
 * @Description:
 */

@Data
public class SupplyChainTodoListDto {

    private Integer riskCheckSkuTodoListCount;

    private Integer riskCheckSkuTodoListCountGroupByOrder;

    private Integer riskCheckTraderSupplyTodoListCount;

    private Integer riskCheckTraderSupplyTodoListCountGroupByOrder;

    /**
     * 按照等级划分，统计SPU/SKU待维护的待办事项数
     */
    private Map<Integer, Integer> maintainSkuAndSpuCountGroupByGrade = new HashMap<>();

    /**
     * 按照等级划分，统计待维护的预计可发货时间的待办数量
     */
    private Map<Integer, Integer> maintainDeliveryTimeCountGroupByGrade = new HashMap<>();

    /**
     * 按照等级划分，统计待维护核价的待办数量
     */
    private Map<Integer, Integer> maintainPriceCountGroupByGrade = new HashMap<>();

    /**
     * 待维护价格的sku集合，逗号分隔
     */
    private Map<Integer, String> maintainPriceSkuList = new HashMap<>();

    /**
     * 按照等级划分，统计待维护售后政策的待办数量
     */
    private Map<Integer, Integer> maintainAftersalePolicyCountGroupByGrade = new HashMap<>();

    /**
     * 按照等级划分，统计待维护供应商售后政策的待办数量
     */
    private Map<Integer, Integer> maintainSupplyAftersalePolicyCountGroupByGrade = new HashMap<>();

    /**
     * 按照等级划分，统计待维护的报备信息的待办数量
     */
    private Map<Integer, Integer> maintainReportInfoCountGroupByGrade = new HashMap<>();

    /**
     * 按照等级划分，统计待维护的运营信息的待办数量
     */
    private Map<Integer, Integer> maintainOperationInfoCountGroupByGrade = new HashMap<>();

    /**
     * 按照等级划分，统计待维护的签约模式的待办数量
     */
    private Map<Integer, Integer> maintainSignContractModeCountGroupByGrade = new HashMap<>();

    /**
     * 当前用户包含下属的id集合(供应链部门下)
     */
    private String subordinateList;

    /**
     * 分级分档模块是否隐藏标识，默认为否
     */
    private Boolean moduleHiddenFlag = false;
}
