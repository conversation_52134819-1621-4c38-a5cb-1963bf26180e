package com.wms.dto;

/**
 * WMS开票结果信息
 */
public class WmsInvoiceDto {
    /**
     * 货主
     */
    private String CustomerID;

    /**
     * 仓库ID
     */
    private String WarehouseID;

    /**
     * ERP单据编号
     */
    private String SOReference1;

    /**
     * WMS单据编号
     */
    private String OrderNo;

    /**
     * 开票结果
     * ERP下传代码值 Y/N/C
     * Y 复核完成！请下载发票并随货寄出
     * N 复核完成！无需开具发票
     * C 符合票货同行，但开票失败
     */
    private String Invoice_Flag;

    /**
     * 开票申请ID
     * 必须下传开票请ID
     */
    private  String UserDefine1;

    /**
     * 开票失败原因
     * 如果开票失败，必须下传原因
     */
    private String UserDefine2;

    public String getCustomerID() {
        return CustomerID;
    }

    public void setCustomerID(String customerID) {
        CustomerID = customerID;
    }

    public String getWarehouseID() {
        return WarehouseID;
    }

    public void setWarehouseID(String warehouseID) {
        WarehouseID = warehouseID;
    }

    public String getSOReference1() {
        return SOReference1;
    }

    public void setSOReference1(String SOReference1) {
        this.SOReference1 = SOReference1;
    }

    public String getOrderNo() {
        return OrderNo;
    }

    public void setOrderNo(String orderNo) {
        OrderNo = orderNo;
    }

    public String getInvoice_Flag() {
        return Invoice_Flag;
    }

    public void setInvoice_Flag(String invoice_Flag) {
        Invoice_Flag = invoice_Flag;
    }

    public String getUserDefine1() {
        return UserDefine1;
    }

    public void setUserDefine1(String userDefine1) {
        UserDefine1 = userDefine1;
    }

    public String getUserDefine2() {
        return UserDefine2;
    }

    public void setUserDefine2(String userDefine2) {
        UserDefine2 = userDefine2;
    }

    @Override
    public String toString() {
        return "WmsInvoiceDto{" +
                "CustomerID='" + CustomerID + '\'' +
                ", WarehouseID='" + WarehouseID + '\'' +
                ", SOReference1='" + SOReference1 + '\'' +
                ", OrderNo='" + OrderNo + '\'' +
                ", Invoice_Flag='" + Invoice_Flag + '\'' +
                ", UserDefine1='" + UserDefine1 + '\'' +
                ", UserDefine2='" + UserDefine2 + '\'' +
                '}';
    }
}
