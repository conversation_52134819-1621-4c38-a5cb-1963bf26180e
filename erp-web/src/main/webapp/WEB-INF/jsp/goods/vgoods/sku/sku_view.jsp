<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>

<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>查看SKU</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/spu/spu_view.css?rnd=${resourceVersionKey}">

    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/general.css?rnd=${resourceVersionKey}" />
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/manage.css" />
    <!-- 模糊搜索下拉框css引入 -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/libs/searchableSelect/jquery.searchableSelect.css" />
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/labelMark.css">
    <script type="text/javascript" src='${pageContext.request.contextPath}/static/js/jquery.min.js'></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/jquery/validation/jquery-form.js"></script>
    <script type="text/javascript" src='${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js'></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/form.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/closable-tab.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" src='${pageContext.request.contextPath}/static/js/common.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/call/call.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src='${pageContext.request.contextPath}/static/js/movinghead.js?rnd=${resourceVersionKey}'></script>

    <style>
        .dlg-confirm i{
            background: none;
        }
        .del-wrap i {
            background: none;
        }

        .tip-wrap i{
            background: none;
        }

        .history {
            font: 12px / 1.5 "Microsoft YaHei", Arial, sans-serif;
        }
        .tip-wrap .tip {
            z-index:1;
         }
        .icon-info2{
            vertical-align: -3px;
        }
        .tips-i{
            vertical-align: 3px;
        }
        .vd-icon{
            background: none;
        }

    </style>
</head>

<body>
    <div class="detail-wrap">
        <input type="hidden" name="spuLevel" value="${coreSpuDto.spuLevel}">
        <input type="hidden" name="skuType" value="${command.skuType}">
        <input type="hidden" id="isSupplyAssistant" value="${isSupplyAssistant}">
        <input type="hidden" id="checkStatus" value="${skuGenerate.checkStatus}">
        <input type="hidden" name="goodsLevelNo" value="${skuGenerate.goodsLevelNo}">
        <div class="detail-title pos_rel">
            <div style="display:inline;">
            查看SKU：${skuGenerate.showName}
            <c:if test="${!empty skuGenerate.historyName && skuGenerate.showName ne skuGenerate.historyName}">
                <div class="tip-wrap"  style="display:inline;">
                    <i class="vd-icon icon-info2 " style="background: none">
                        <div class="tip arrow-left">
                            <div class="tip-con">
                                原值：${skuGenerate.historyName}
                            </div>
                            <span class="arrow arrow-out">
                                <span class="arrow arrow-in"></span>
                            </span>
                        </div>
                    </i>
                </div>
            </c:if>
            </div>
            <div class="tip-wrap" style="display:inline;position:relative;text-align:right;float: right;">
                订货号：${skuGenerate.skuNo} &nbsp;&nbsp;&nbsp;&nbsp;产品归属：
                <c:if test="${not empty coreSpuDto.productMgrName}">
                    ${coreSpuDto.productMgrName}
                </c:if>
                <c:if test="${not empty coreSpuDto.productAssistantName and not empty coreSpuDto.productAssistantName}">
                    &
                </c:if>
                <c:if test="${not empty coreSpuDto.productAssistantName}">
                    ${coreSpuDto.productAssistantName}
                </c:if>
            </div>
        </div>

        <!-- 后台报错的区域 -->
<c:if test="${not empty command.errors && command.errors.size()>0}">
        <c:forEach var="error" items="${command.errors}" varStatus="status">
            <div class="vd-tip tip-red">
                <i class="vd-tip-icon vd-icon icon-error2"></i>
                <div class="vd-tip-cnt">${error}</div>
            </div>
        </c:forEach>
    </c:if>

        <c:if test="${command.tips != '操作成功' }">
            <c:forEach var="tip" items="${command.tips}" varStatus="status">
                <div class="vd-tip tip-green">
                    <i class="vd-tip-icon vd-icon icon-yes1"></i>
                        ${tip}
                    </div>
                </div>
            </c:forEach>
        </c:if>
        <!-- end -->
        <div class="tab-nav">
            <a class="tab-item current" href="#">基本信息</a>
            <c:if test="${coreSpuDto.spuLevel!=2 && showType ne 'op'}"><%--showType ne 'op'运营后台连接跳转无需这些操作--%>
                <c:if test="${empty skuGenerate.operateInfoId or skuGenerate.operateInfoId==0 and skuGenerate.checkStatus==3}">
                <a class="tab-item"   href="/vgoods/operate/viewOperate.do?skuId=${skuGenerate.skuId}">运营信息</a>
                </c:if>
                <c:if test="${not empty skuGenerate.operateInfoId and skuGenerate.operateInfoId>0}">
                    <a class="tab-item"   href="/vgoods/operate/viewOperate.do?skuId=${skuGenerate.skuId}">运营信息</a>
                </c:if>
                <a class="tab-item"   href="/goods/vgoods/viewSku.do?skuId=${skuGenerate.skuId}&spuId=${skuGenerate.spuId}&&pageType=1&type=1">商品信息整合</a>
            </c:if>
        </div>
        <div class="detail-option-wrap">
            <c:if test="${showType ne 'op'}"><%--showType ne 'op'运营后台连接跳转无需这些操作--%>
                <div class="option-btns">
                    <c:if test="${ coreSpuDto.spuLevel!=2  && command.hasEditAuth && showType ne 'op' && skuGenerate.checkStatus ne 1}">
                         <a id = 'editSkuBtn' class="btn btn-small btn-blue" href="/goods/vgoods/addSku.do?skuId=${skuGenerate.skuId}&spuId=${coreSpuDto.spuId}"> 编辑</a>
                    </c:if>
                    <c:if test="${coreSpuDto.spuLevel==2 && command.hasEditTempAuth && skuGenerate.checkStatus ne 1}">
                            <a id = 'editSkuBtn' class="btn btn-small btn-blue J-sku-edit" data-spuid="${skuGenerate.spuId}" data-type="${command.skuType}" data-spuname="${coreSpuDto.spuShowName}" data-skuid="${skuGenerate.skuId}">编辑</a>
                    </c:if>
                    <c:if test="${command.hasEditAuth and skuGenerate.checkStatus == 5}">
                        <a id = 'approveBtn' class="btn btn-small btn-blue J-sku-option" data-dlg='0' data-href="/goods/vgoods/submitCheckSku.do" data-params='{"skuId":${skuGenerate.skuId},"checkStatus": 1, "spuId":${skuGenerate.spuId} }'>
                            提交审核
                        </a>
                    </c:if>

                    <c:if test="${skuGenerate.checkStatus == 1}">
                        <c:choose>
                            <c:when test="${command.hasCheckAuth}">
                                <a id = 'approveBtn' class="btn btn-small btn-blue J-sku-option" data-dlg='0' data-href="/goods/vgoods/checkSku.do" data-params='{"skuId":${skuGenerate.skuId},"checkStatus": 3, "spuId":${skuGenerate.spuId} }'>
                                    审核通过
                                </a>
                                <a id = 'vetoBtn' class="btn btn-small btn-blue J-sku-option" data-href="/goods/vgoods/checkSku.do" data-params='{"skuId":${skuGenerate.skuId},"checkStatus": 2, "spuId":${skuGenerate.spuId} }'>
                                    审核不通过
                                </a>
                            </c:when>
                            <c:otherwise>
                                <a class="btn btn-small" style="color: grey">已申请审核</a>
                            </c:otherwise>
                        </c:choose>
                    </c:if>

                </div>
            </c:if>
        </div>

    <div class="detail-block block-nohidden">
        <div class="block-title">SPU信息</div>
        <div class="detail-table">
            <table class="table table-base">
                <colgroup>
                    <col width="">
                    <col width="">
                    <col width="">
                    <col width="">
                    <col width="">
                    <col width="">
                    <col width="">
                </colgroup>
                <tbody>
                    <tr>
                        <th>商品名称</th>
                        <th>商品类型</th>
                        <th>商品等级</th>
                        <th>商品档位</th>
                        <th>注册证号</th>
                        <th>审核状态</th>
                        <th>更新时间</th>
                        <th>Wiki资料</th>
                    </tr>
                    <tr>
                        <td>
                            <div class="line-clamp2">
                                <a href="javascript:void(0);" tabTitle='{"num":"vgoodsview${coreSpuDto.spuId}","link":"/goods/vgoods/viewSpu.do?spuId=${coreSpuDto.spuId}","title":"查看SPU"}'>${coreSpuDto.spuShowName}</a>
                            </div>
                        </td>
                        <td>

<c:forEach var="spuType" items="${spuTypeList}" varStatus="status">
  <c:if test="${coreSpuDto.spuType == spuType.sysOptionDefinitionId}"> ${spuType.title} </c:if>
</c:forEach>

                        </td>
                        <td>${coreSpuDto.goodsLevelName}</td>
                        <td>${coreSpuDto.goodsPositionName}</td>
                        <td>
                            ${coreSpuDto.registrationNumber}
                        </td>
                        <td>
<c:if test="${coreSpuDto.checkStatus==0}">
    <span class="title-status status-yellow">待完善</span>
</c:if>
                            <c:if test="${coreSpuDto.checkStatus==2}">
                                <span class="title-status status-red">审核不通过</span>
                            </c:if>
                            <c:if test="${coreSpuDto.checkStatus==1}">
                                <span class="title-status status-yellow">审核中</span>
                            </c:if>
                            <c:if test="${coreSpuDto.checkStatus==3}">
                                <span class="title-status status-green">审核通过</span>
                            </c:if>
                            <c:if test="${coreSpuDto.checkStatus==5}">
                                <span class="title-status status-green">待提交审核</span>
                            </c:if>
                        </td>
                        <td>${coreSpuDto.modTimeShow}</td>
                        <td>
                            <c:if test="${not empty coreSpuDto.wikiHref}">
                            <a href="${coreSpuDto.wikiHref}" target="_blank">SPU Wiki</a>
                            </c:if>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>


    <div class="detail-block" style="overflow: inherit">
        <div class="block-title">等级及档位</div>
        <div class="detail-table">
            <div class="table-item">
                <div class="table-th customername pos_rel"><span>商品等级：
                    <c:if test="${oldCommand.goodsLevelVo.levelName ne command.goodsLevelVo.levelName and not empty oldCommand and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                    <i class="iconbluesigh ml4 contorlIcon"></i></span>
                    <div class="pos_abs customernameshow">原值：${oldCommand.goodsLevelVo.levelName}</div></c:if>
                </div>
                <div class="table-td">
                    <c:if test="${command.goodsLevelVo ne null}">
                        ${command.goodsLevelVo.levelName}
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th customername pos_rel"><span>描述：
                <c:if test="${oldCommand.goodsLevelVo.description ne command.goodsLevelVo.description and not empty oldCommand and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                    <i class="iconbluesigh ml4 contorlIcon"></i></span>
                    <div class="pos_abs customernameshow">原值：
                        <c:if test="${oldCommand.goodsLevelVo ne null}">
                            ${oldCommand.goodsLevelVo.description}
                        </c:if>
                        <c:if test="${oldCommand.goodsLevelVo eq null}">
                            无
                        </c:if>
                    </div></c:if>
                </div>
                <div class="table-td">
                    <c:if test="${command.goodsLevelVo ne null}">
                        ${command.goodsLevelVo.description}
                    </c:if>
                    <c:if test="${command.goodsLevelVo eq null}">
                        无
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th customername pos_rel"><span>商品档位
                    <c:if test="${oldCommand.goodsPositionVo.positionName ne command.goodsPositionVo.positionName and not empty oldCommand and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                    <i class="iconbluesigh ml4 contorlIcon"></i></span>
                    <div class="pos_abs customernameshow">原值：
                        <c:if test="${oldCommand.goodsPositionVo ne null}">
                            ${oldCommand.goodsPositionVo.positionName}
                        </c:if>
                        <c:if test="${oldCommand.goodsPositionVo eq null}">
                            无档位
                        </c:if>
                    </div></c:if>
                </div>
                <div class="table-td">
                    <c:if test="${command.goodsPositionVo ne null}">
                        ${command.goodsPositionVo.positionName}
                    </c:if>
                    <c:if test="${command.goodsPositionVo eq null}">
                        无档位
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                <div class="table-th customername pos_rel"><span>描述：
               <c:if test="${oldCommand.goodsPositionVo.description ne command.goodsPositionVo.description and not empty oldCommand and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                    <i class="iconbluesigh ml4 contorlIcon"></i></span>
                    <div class="pos_abs customernameshow">原值：
                        <c:if test="${oldCommand.goodsPositionVo ne null}">
                            ${oldCommand.goodsPositionVo.description}
                        </c:if>
                        <c:if test="${oldCommand.goodsPositionVo eq null}">
                            无
                        </c:if>
                    </div></c:if>
                </div>
                <div class="table-td">
                    <c:if test="${command.goodsPositionVo ne null}">
                        ${command.goodsPositionVo.description}
                    </c:if>
                </div>
            </div>
        </div>
    </div>

    <c:if test="${not empty goodsTodoItemVoList}">
        <div class="detail-block">
            <div class="block-title">待办事项</div>
            <div class="detail-table">
                <c:forEach items="${goodsTodoItemVoList}" var="tdoItem">
                    <div class="table-item" style="width: 40%;">
                        <div class="table-td" style="border: 0px;">
                            <div class="detail-block-tip vd-tip tip-blue" style="background: #fffce5;">
                                <i class="vd-tip-icon vd-icon icon-info2 goods-required-item" style="color: #efc859; background: none"></i>
                                <div class="vd-tip-cnt">${tdoItem.serviceName}
                                    <a href="javascript:void(0);" tabTitle='{"num":"vgoodsviews<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"${tdoItem.url}","title":"${tdoItem.viewName}"}'>去完善>></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </c:forEach>
            </div>
        </div>
    </c:if>


    <div class="detail-block">
        <div class="block-title">基本信息</div>


        <div class="detail-table">
            <c:if test="${not empty coreSpuDto.spuName}">
                <div class="table-item">
                    <div class="table-th">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                        产品名称（注册证/备案凭证）：
                    </div>
                    <div class="table-td">
                            ${coreSpuDto.spuName}
                    </div>
                </div>
            </c:if>
            <c:if test="${not empty coreSpuDto.specsModel}">
                <div class="table-item">
                    <div class="table-th">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                        规格、型号（注册证/备案凭证）：
                    </div>
                    <div class="table-td">
                            ${coreSpuDto.specsModel}
                    </div>
                </div>
            </c:if>
            <div class="table-item">
                <div class="table-th">订货号：</div>
                <div class="table-td">
                    ${skuGenerate.skuNo}
                </div>
            </div>
<c:if test="${not empty skuGenerate.model}">
                <div class="table-item">
                    <div class="table-th customername pos_rel"><span>制造商型号：
                        <c:if test="${oldSkuGenerate.model ne skuGenerate.model and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：${oldSkuGenerate.model}</div>
                        </c:if></div>
                    <div class="table-td">
                        ${skuGenerate.model}
                    </div>
                </div>
</c:if>
<c:if test="${not empty skuGenerate.spec}">
                <div class="table-item">
                    <div class="table-th customername pos_rel"><span>规格：
                        <c:if test="${oldSkuGenerate.spec ne skuGenerate.spec and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：${oldSkuGenerate.spec}</div></c:if></div>
                    <div class="table-td">
                        ${skuGenerate.spec}
                    </div>
                </div>
</c:if>
            <c:if test="${coreSpuDto.spuLevel!=2}">
                <div class="table-item">
                    <div class="table-th customername pos_rel"><span>商品名称：
                        <c:if test="${oldSkuGenerate.skuName ne skuGenerate.skuName and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：${oldSkuGenerate.skuName}</div>
                        </c:if>
                        <c:if test="${empty skuGenerate.skuName}">
                        <div class="tip-wrap"  style="position: absolute; z-index: 10">
                            <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="skuName">
                                <div class="tip arrow-left">
                                    <div class="tip-con">
                                        待完善
                                    </div>
                                    <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                </div>
                            </i>
                        </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                        ${skuGenerate.skuName}
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th customername pos_rel"><span>物料编码：
                        <c:if test="${oldSkuGenerate.materialCode ne skuGenerate.materialCode and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：${oldSkuGenerate.materialCode}</div></c:if>
                        <c:if test="${empty skuGenerate.materialCode}">
                            <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="materialCode">
                                    <div class="tip arrow-left">
                                        <div class="tip-con">
                                            待完善
                                        </div>
                                        <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                    </div>
                                </i>
                            </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                        ${skuGenerate.materialCode}
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th customername pos_rel"><span>供应商型号：
                        <c:if test="${oldSkuGenerate.supplyModel ne skuGenerate.supplyModel and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：${oldSkuGenerate.supplyModel}</div></c:if>
                        <c:if test="${empty skuGenerate.supplyModel}">
                            <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="supplyModel">
                                <div class="tip arrow-left">
                                    <div class="tip-con">
                                        待完善
                                    </div>
                                    <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                </div>
                                </i>
                            </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                        ${skuGenerate.supplyModel}
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">是否备货：</div>
                    <div class="table-td">
                <c:if test="${skuGenerate.isStockup==1}">
                    是
                </c:if>
                <c:if test="${skuGenerate.isStockup!=1}">
                    否
                </c:if>
                    </div>
                </div>
            </c:if>
            <div class="table-item">
                <div class="table-th">税收分类编码：
                    <c:if test="${skuGenerate.taxCategoryNoRecord ne null and skuGenerate.taxCategoryNoRecord ne ''}">
                        <div class="tip-wrap">
                            <i class="vd-icon icon-info2">
                                <span class="tips-i" style="font-size: smaller;">(记录)</span>
                                <div class="tip arrow-left" style="margin-top: 30px">
                                    <div class="tip-con">
                                            ${skuGenerate.taxCategoryNoRecord}
                                    </div>
                                </div>
                            </i>
                        </div>
                    </c:if>
                </div>
                <div class="table-td">
                    <c:if test="${skuGenerate.taxCategoryNo ne null and skuGenerate.taxCategoryNo ne ''}">
                        <div class="tip-wrap">
                            <i class="vd-icon icon-info2">
                                <span class="tips-i" style="font-size: smaller;">(信息)</span>
                                <div class="tip arrow-left" style="width: 450px; height: auto; margin-top: 30px">
                                    汇总项名称：${skuGenerate.taxcodeClassificationDto.goodsServicesNameAbbreviation}<br>
                                    汇总项简称：${skuGenerate.taxcodeClassificationDto.goodsServicesClassificationAbbreviation}<br>
                                    货物和劳务名称：${skuGenerate.taxcodeClassificationDto.goodsServicesName}<br>
                                    商品和服务分类简称：${skuGenerate.taxcodeClassificationDto.classificationAbbreviation}<br>
                                    关键字：${skuGenerate.taxcodeClassificationDto.keyword}<br>
                                    说明：${skuGenerate.taxcodeClassificationDto.description}<br>
                                </div>
                            </i>
                        </div>
                    </c:if>
                    ${skuGenerate.taxCategoryNo}
                </div>
            </div>

            <div class="table-item">
                    <div class="table-th customername pos_rel"><span>商品条形码（69码）：
                        <c:if test="${oldSkuGenerate.goodsBarcode ne skuGenerate.goodsBarcode and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：${oldSkuGenerate.goodsBarcode}</div></c:if>
                        <c:if test="${empty skuGenerate.goodsBarcode}">
                            <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="goodsBarcode">
                                <div class="tip arrow-left">
                                    <div class="tip-con">
                                        待完善
                                    </div>
                                    <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                </div>
                                </i>
                            </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                        ${skuGenerate.goodsBarcode}
                    </div>
            </div>
            <div class="table-item">
                <div class="table-th customername pos_rel">
                    <span>商品UDI-DI码：</span>
                </div>
                <div class="table-td">
                    ${skuGenerate.diCode}
                </div>
            </div>
            <div class="table-item">
                    <div class="table-th customername pos_rel"><span>是否必须检测报告：
                        <c:if test="${oldSkuGenerate.isNeedTestReprot ne skuGenerate.isNeedTestReprot and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:if test="${oldSkuGenerate.isNeedTestReprot eq 1}">
                                    是
                                </c:if>
                                <c:if test="${oldSkuGenerate.isNeedTestReprot eq 0}">
                                    否
                                </c:if></div></c:if>
                        <c:if test="${skuGenerate.isNeedTestReprot eq null}">
                            <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="isNeedTestReprot">
                                <div class="tip arrow-left">
                                    <div class="tip-con">
                                        待完善
                                    </div>
                                    <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                </div>
                                </i>
                            </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                <c:if test="${skuGenerate.isNeedTestReprot==1}">
                    是
                </c:if>
                <c:if test="${skuGenerate.isNeedTestReprot==0}">
                    否
                </c:if>
                    </div>
            </div>
            <c:if test="${coreSpuDto.spuType==316}">
                <div class="table-item">
                    <div class="table-th customername pos_rel"><span>定期维护：
                        <c:if test="${oldSkuGenerate.regularMaintainType ne skuGenerate.regularMaintainType and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：
                            <c:choose>
                                <c:when test="${oldSkuGenerate.regularMaintainType==0}">
                                    不维护
                                </c:when>
                                <c:when test="${oldSkuGenerate.regularMaintainType==1}">
                                    一般维护
                                </c:when>
                                <c:when test="${oldSkuGenerate.regularMaintainType==2}">
                                    定期维护
                                </c:when>
                                <c:otherwise>
                                    -
                                </c:otherwise>
                            </c:choose>
                        </div></c:if>
                    </div>
                    <div class="table-td">
                        <c:choose>
                            <c:when test="${skuGenerate.regularMaintainType==0}">
                                不维护
                            </c:when>
                            <c:when test="${skuGenerate.regularMaintainType==1}">
                                一般维护
                            </c:when>
                            <c:when test="${skuGenerate.regularMaintainType==2}">
                                定期维护
                            </c:when>
                            <c:otherwise>
                                -
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>
                <c:if test="${skuGenerate.regularMaintainType eq 1 or skuGenerate.regularMaintainType eq 2}">
                    <div class="table-item">
                        <div class="table-th customername pos_rel"><span>维护原因：
                            <c:if test="${oldSkuGenerate.regularMaintainReason ne skuGenerate.regularMaintainReason and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：${oldSkuGenerate.regularMaintainReason}</div></c:if></div>
                        <div class="table-td">
                                ${skuGenerate.regularMaintainReason}
                        </div>
                    </div>
                </c:if>
            </c:if>
            <div class="table-item">
                    <div class="table-th customername pos_rel"><span>是否套件：
                        <c:if test="${oldSkuGenerate.isKit ne skuGenerate.isKit and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：
                            <c:if test="${oldSkuGenerate.isKit eq 1}">
                                是
                            </c:if>
                            <c:if test="${oldSkuGenerate.isKit eq 0}">
                                否
                            </c:if>
                        </div></c:if>
                        <c:if test="${skuGenerate.isKit eq null}">
                            <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="isKit">
                                    <div class="tip arrow-left">
                                        <div class="tip-con">
                                            待完善
                                        </div>
                                        <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                    </div>
                                </i>
                            </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                <c:if test="${skuGenerate.isKit==1}">
                    是
                </c:if>
                <c:if test="${skuGenerate.isKit==0}">
                    否
                </c:if>
                    </div>
            </div>	
            <div class="table-item">
                <div class="table-th customername pos_rel"><span>有哪几个套件：
                    <c:if test="${oldSkuGenerate.kitDesc ne skuGenerate.kitDesc and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                    <i class="iconbluesigh ml4 contorlIcon"></i></span>
                    <div class="pos_abs customernameshow">原值：${oldSkuGenerate.kitDesc}</div></c:if></div>
                <div class="table-td">
                    ${skuGenerate.kitDesc}
                </div>
            </div>
            <div class="table-item">
                    <div class="table-th customername pos_rel"><span>套件中子件的SN码是否必须一致：
                        <c:if test="${oldSkuGenerate.isSameSnCode ne skuGenerate.isSameSnCode and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：
                            <c:if test="${oldSkuGenerate.isSameSnCode==1}">
                                是
                            </c:if>
                            <c:if test="${oldSkuGenerate.isSameSnCode==0}">
                                否
                            </c:if>
                        </div></c:if>
                    </div>
                    <div class="table-td">
                <c:if test="${skuGenerate.isSameSnCode==1}">
                    是
                </c:if>
                <c:if test="${skuGenerate.isSameSnCode==0}">
                    否
                </c:if>
                    </div>
            </div>				
            <div class="table-item">
                    <div class="table-th customername pos_rel"><span>是否厂家赋SN码：
                        <c:if test="${oldSkuGenerate.isFactorySnCode ne skuGenerate.isFactorySnCode and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：
                            <c:if test="${oldSkuGenerate.isFactorySnCode eq 1}">
                                是
                            </c:if>
                            <c:if test="${oldSkuGenerate.isFactorySnCode eq 0}">
                                否
                            </c:if>
                        </div></c:if>
                        <c:if test="${skuGenerate.isFactorySnCode eq null}">
                            <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="isFactorySnCode">
                                    <div class="tip arrow-left">
                                        <div class="tip-con">
                                            待完善
                                        </div>
                                        <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                    </div>
                                </i>
                            </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                <c:if test="${skuGenerate.isFactorySnCode==1}">
                    是
                </c:if>
                <c:if test="${skuGenerate.isFactorySnCode==0}">
                    否
                </c:if>
                    </div>
            </div>
            <div class="table-item">
                <div class="table-th customername pos_rel"><span>产品是否可安装：
                    <c:if test="${oldSkuGenerate.isInstallable ne skuGenerate.isInstallable and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                    <i class="iconbluesigh ml4 contorlIcon"></i></span>
                    <div class="pos_abs customernameshow">原值：
                        <c:if test="${oldSkuGenerate.isInstallable eq 1}">
                            是
                        </c:if>
                        <c:if test="${oldSkuGenerate.isInstallable eq 0}">
                            否
                        </c:if>
                    </div></c:if>
                    <c:if test="${skuGenerate.isInstallable eq null}">
                        <div class="tip-wrap"  style="position: absolute; z-index: 10">
                            <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="isInstallable">
                                <div class="tip arrow-left">
                                    <div class="tip-con">
                                        待完善
                                    </div>
                                    <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                </div>
                            </i>
                        </div>
                    </c:if>
                </div>
                <div class="table-td">
                    <c:if test="${skuGenerate.isInstallable eq 1}">
                        是
                    </c:if>
                    <c:if test="${skuGenerate.isInstallable eq 0}">
                        否
                    </c:if>
                </div>
            </div>
            <div class="table-item">
                    <div class="table-th customername pos_rel"><span>是否管理贝登追溯码：
                        <c:if test="${oldSkuGenerate.isManageVedengCode ne skuGenerate.isManageVedengCode and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：
                            <c:if test="${oldSkuGenerate.isManageVedengCode==1}">
                                是
                            </c:if>
                            <c:if test="${oldSkuGenerate.isManageVedengCode==0}">
                                否
                            </c:if>
                        </div></c:if>
                        <c:if test="${skuGenerate.isManageVedengCode eq null}">
                            <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="isManageVedengCode">
                                    <div class="tip arrow-left">
                                        <div class="tip-con">
                                            待完善
                                        </div>
                                        <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                    </div>
                                </i>
                            </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                <c:if test="${skuGenerate.isManageVedengCode==1}">
                    是
                </c:if>
                <c:if test="${skuGenerate.isManageVedengCode==0}">
                    否
                </c:if>
                    </div>
            </div>				
            <div class="table-item">
                    <div class="table-th customername pos_rel"><span>是否异形品：
                        <c:if test="${oldSkuGenerate.isBadGoods ne skuGenerate.isBadGoods and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：
                            <c:if test="${oldSkuGenerate.isBadGoods==1}">
                                是
                            </c:if>
                            <c:if test="${oldSkuGenerate.isBadGoods==0}">
                                否
                            </c:if>
                        </div></c:if>
                        <c:if test="${skuGenerate.isBadGoods eq null}">
                            <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="isBadGoods">
                                    <div class="tip arrow-left">
                                        <div class="tip-con">
                                            待完善
                                        </div>
                                        <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                    </div>
                                </i>
                            </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                <c:if test="${skuGenerate.isBadGoods==1}">
                    是
                </c:if>
                <c:if test="${skuGenerate.isBadGoods==0}">
                    否
                </c:if>
                    </div>
            </div>				
            <div class="table-item">
                    <div class="table-th customername pos_rel"><span>是否启用厂家批号：
                        <c:if test="${oldSkuGenerate.isEnableFactoryBatchnum ne skuGenerate.isEnableFactoryBatchnum and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：
                            <c:if test="${oldSkuGenerate.isEnableFactoryBatchnum==1}">
                                是
                            </c:if>
                            <c:if test="${oldSkuGenerate.isEnableFactoryBatchnum==0}">
                                否
                            </c:if>
                        </div></c:if>
                        <c:if test="${skuGenerate.isEnableFactoryBatchnum eq null}">
                            <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="isEnableFactoryBatchnum">
                                    <div class="tip arrow-left">
                                        <div class="tip-con">
                                            待完善
                                        </div>
                                        <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                    </div>
                                </i>
                            </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                <c:if test="${skuGenerate.isEnableFactoryBatchnum==1}">
                    是
                </c:if>
                <c:if test="${skuGenerate.isEnableFactoryBatchnum==0}">
                    否
                </c:if>
                    </div>
            </div>
            <div class="table-item">
                <div class="table-th customername pos_rel"><span>是否启用效期管理：
                    <c:if test="${oldSkuGenerate.isEnableValidityPeriod ne skuGenerate.isEnableValidityPeriod and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：
                            <c:if test="${oldSkuGenerate.isEnableValidityPeriod == 1}">
                                是
                            </c:if>
                            <c:if test="${oldSkuGenerate.isEnableValidityPeriod == 0}">
                                否
                            </c:if>
                            <c:if test="${oldSkuGenerate.isEnableValidityPeriod eq null}">
                                待完善
                            </c:if>
                        </div>
                    </c:if>
                    <c:if test="${skuGenerate.isEnableValidityPeriod eq null}">
                        <div class="tip-wrap" style="position: absolute; z-index: 10">
                            <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47"
                               itemName="isEnableValidityPeriod">
                                <div class="tip arrow-left">
                                    <div class="tip-con">
                                        待完善
                                    </div>
                                    <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                </div>
                            </i>
                        </div>
                    </c:if>
                </div>
                <div class="table-td">
                    <c:if test="${skuGenerate.isEnableValidityPeriod==1}">
                        是
                    </c:if>
                    <c:if test="${skuGenerate.isEnableValidityPeriod==0}">
                        否
                    </c:if>
                </div>
            </div>

            <div class="table-item">
                <div class="table-th">启用状态：</div>
                <div class="table-td">
                    <c:choose>
                        <c:when test="${skuGenerate.status == 1}">
                            <span>已启用</span>
                        </c:when>
                        <c:when test="${skuGenerate.status == 0}">
                            <span>已禁用</span>
                        </c:when>
                        <c:otherwise>
                            <span>审核中</span>
                        </c:otherwise>
                    </c:choose>
                </div>
            </div>

            <div class="table-item">
                <div class="table-th">禁用原因：</div>
                <div class="table-td">
                    <c:if test="${skuGenerate.status == 0}">
                        ${skuGenerate.disabledReason}
                    </c:if>
                </div>
            </div>
</div>
    </div>


    <c:if test="${not empty command.configurationName}">
    <div class="detail-block" style="overflow: inherit;">
        <div class="block-title customername pos_rel"><span>配置清单
            <c:if test="${oldConfigurationList ne skuGenerate.configurationList and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
            <i class="iconbluesigh ml4 contorlIcon"></i></span>
            <div class="pos_abs customernameshow history">原值：<br>
                    ${oldSkuGenerate.configurationList}
            </div></c:if>
        </div>
        <div class="detail-table">
            <div class="table-item item-col">
                <div class="table-th">配置内容：
                </div>
                <div class="table-td">
                    <c:forEach items="${command.configurationName}" var="params" varStatus="status">
                        ${command.configurationName[status.index]} : ${command.configurationQuantity[status.index]} <br>
                    </c:forEach>
                </div>
            </div>
        </div>
    </div>
    </c:if>


    <c:if test="${coreSpuDto.spuLevel!='2'}">
        <c:if test="${not empty baseAttributeVoList}">
        <div class="detail-block">
            <div class="block-title">商品属性</div>
            <div class="detail-table">
                    <div class="table-item">
                        <div class="table-th">选择带入属性：
                            <c:if test="${empty baseAttributeVoList}">
                                <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                    <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="baseAttributeValueId">
                                        <div class="tip arrow-left">
                                            <div class="tip-con">
                                                待完善
                                            </div>
                                            <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                        </div>
                                    </i>
                                </div>
                            </c:if>
                        </div>
                        <div class="table-td J-spring-filter">
                            <c:forEach items="${baseAttributeVoList}" var="attr">
                                ${attr.baseAttributeName}、
                            </c:forEach>
                        </div>
                    </div>
        <c:forEach items="${baseAttributeVoList}" var="attr">
                    <div class="table-item">
                    <div class="table-th">${attr.baseAttributeName}：</div>
                    <div class="table-td">
                        <c:forEach items="${attr.attrValue}" var="attrVal">
                            <c:if test="${attrVal.selected}">
                                ${attrVal.attrValue}${attrVal.unitName}
                            </c:if>
                        </c:forEach>
                    </div>
                    </div>
        </c:forEach>
            </div>
        </div>
        </c:if>

        <c:if test="${command.skuType==1 && showType ne 'op'}">
            <c:if test="${not empty command.paramsName1}">
            <div class="detail-block" style="overflow: inherit;">
                <div class="block-title customername pos_rel"><span>参数信息
                    <c:if test="${oldTechnicalParameter ne skuGenerate.technicalParameter and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                    <i class="iconbluesigh ml4 contorlIcon"></i></span>
                    <div class="pos_abs customernameshow history">原值：<br>
                            ${oldSkuGenerate.technicalParameter}
                    </div></c:if>
                </div>
                <div class="detail-table">
                    <div class="table-item item-col">
                        <div class="table-th">技术参数：
                            <c:if test="${empty command.paramsName1}">
                                <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                    <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="technicalParameter">
                                        <div class="tip arrow-left">
                                            <div class="tip-con">
                                                待完善
                                            </div>
                                            <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                        </div>
                                    </i>
                                </div>
                            </c:if>
                        </div>
                        <div class="table-td">
                            <c:forEach items="${command.paramsName1}" var="params" varStatus="status">
                                ${command.paramsName1[status.index]} : ${command.paramsValue1[status.index]} <br>
                            </c:forEach>
                        </div>
                    </div>
                </div>
            </div>

            </c:if>
        </c:if>
        <div class="detail-block" style="overflow: inherit;">
            <div class="block-title">物流和包装</div>
            <div class="detail-table">
                <div class="table-item">
                    <div class="table-th customername pos_rel"><span>SKU商品单位：
                        <c:if test="${oldSkuGenerate.baseUnitId ne skuGenerate.baseUnitId and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：
                            <c:if test="${not empty unitList }">
                                <c:forEach var="unit" items="${unitList}">
                                    <c:if test="${ oldSkuGenerate.baseUnitId ==unit.unitId}"> ${unit.unitName} </c:if>
                                </c:forEach>
                            </c:if>
                        </div></c:if>
                        <c:if test="${skuGenerate.baseUnitId eq null}">
                            <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="baseUnitId">
                                    <div class="tip arrow-left">
                                        <div class="tip-con">
                                            待完善
                                        </div>
                                        <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                    </div>
                                </i>
                            </div>
                        </c:if>
                    </div>

                    <div class="table-td">
                        <c:if test="${not empty unitList }">
                            <c:forEach var="unit" items="${unitList}">
                                <c:if test="${ skuGenerate.baseUnitId ==unit.unitId  }"> ${unit.unitName} </c:if>
                            </c:forEach>
                        </c:if>
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th customername pos_rel"><span>发货方式：

                    </div>
                    <div class="table-td">
                        <c:if test="${skuGenerate.isDirect ==1}">
                            直发
                        </c:if>
                        <c:if test="${skuGenerate.isDirect ==0}">
                            普发
                        </c:if>
                    </div>
                </div>
                <%--VDERP-2212 ERP新商品流-新增/编辑SKU（器械设备、配件），增加最小起订量字段--%>
                <c:if test="${coreSpuDto.spuType == 316 || coreSpuDto.spuType == 1008}">
                    <div class="table-item">
                        <div class="table-th customername pos_rel"><span>最小起订量：
                            <fmt:formatNumber var="nowMinOrder" value="${ skuGenerate.minOrder}"  pattern="0"/>
                            <c:if test="${oldSkuGenerate.minOrder ne nowMinOrder and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:choose>
                                    <c:when test="${empty oldSkuGenerate.minOrder}">
                                        待完善
                                    </c:when>
                                    <c:otherwise>
                                        <c:if test="${not empty unitList }">
                                            <c:forEach var="unit" items="${unitList}">
                                                <c:if test="${ oldSkuGenerate.baseUnitId == unit.unitId  }">
                                                    <fmt:formatNumber value="${ oldSkuGenerate.minOrder}"  pattern="0"/> ${unit.unitName}
                                                </c:if>
                                            </c:forEach>
                                        </c:if>
                                    </c:otherwise>
                                </c:choose>
                            </div></c:if>
                            <c:if test="${empty skuGenerate.minOrder}">
                                <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                    <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="minOrder">
                                        <div class="tip arrow-left">
                                            <div class="tip-con">
                                                待完善
                                            </div>
                                            <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                        </div>
                                    </i>
                                </div>
                            </c:if>
                        </div>
                        <div class="table-td">
                        <c:if test="${not empty unitList }">
                            <c:forEach var="unit" items="${unitList}">
                                <c:if test="${ skuGenerate.baseUnitId == unit.unitId  }">
                                    <fmt:formatNumber value="${ skuGenerate.minOrder}"  pattern="0"/> ${unit.unitName}
                                </c:if>
                            </c:forEach>
                        </c:if>
                        </div>
                    </div>
                </c:if>
            <div class="table-item">
                    <div class="table-th customername pos_rel"><span>是否启用多级包装：
                        <c:if test="${oldSkuGenerate.isEnableMultistagePackage ne skuGenerate.isEnableMultistagePackage and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：
                            <c:if test="${oldSkuGenerate.isEnableMultistagePackage==1}">
                                是
                            </c:if>
                            <c:if test="${oldSkuGenerate.isEnableMultistagePackage==0}">
                                否
                            </c:if>
                        </div></c:if>
                        <c:if test="${skuGenerate.isEnableMultistagePackage eq null}">
                            <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="isEnableMultistagePackage">
                                    <div class="tip arrow-left">
                                        <div class="tip-con">
                                            待完善
                                        </div>
                                        <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                    </div>
                                </i>
                            </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                <c:if test="${skuGenerate.isEnableMultistagePackage==1}">
                    是
                </c:if>
                <c:if test="${skuGenerate.isEnableMultistagePackage==0}">
                    否
                </c:if>
                    </div>
            </div>
            <div class="table-item">
                <div class="table-th customername pos_rel"><span>中包装数量：
                    <c:if test="${oldSkuGenerate.midPackageNum ne skuGenerate.midPackageNum and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                    <i class="iconbluesigh ml4 contorlIcon"></i></span>
                    <div class="pos_abs customernameshow">原值：${oldSkuGenerate.midPackageNum}</div></c:if></div>
                <div class="table-td">
                    ${skuGenerate.midPackageNum}
                </div>
            </div>
            <div class="table-item">
                <div class="table-th customername pos_rel"><span>箱包装数量：
                    <c:if test="${oldSkuGenerate.boxPackageNum ne skuGenerate.boxPackageNum and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                    <i class="iconbluesigh ml4 contorlIcon"></i></span>
                    <div class="pos_abs customernameshow">原值：${oldSkuGenerate.boxPackageNum}</div></c:if></div>
                <div class="table-td">
                    ${skuGenerate.boxPackageNum}
                </div>
            </div>


                <c:if test="${command.skuType!=1}">
                    <div class="table-item">
                        <div class="table-th customername pos_rel"><span>商品最小单位：
                            <c:if test="${oldSkuGenerate.unitId ne skuGenerate.unitId and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:if test="${not empty unitList }">
                                    <c:forEach var="unit" items="${unitList}">
                                        <c:if test="${ oldSkuGenerate.unitId ==unit.unitId  }"> ${unit.unitName} </c:if>
                                    </c:forEach>
                                </c:if>
                            </div></c:if>
                            <c:if test="${skuGenerate.unitId eq null}">
                                <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                    <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="unitId">
                                        <div class="tip arrow-left">
                                            <div class="tip-con">
                                                待完善
                                            </div>
                                            <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                        </div>
                                    </i>
                                </div>
                            </c:if>
                        </div>
                        <div class="table-td">
                            <c:if test="${not empty unitList }">
                                <c:forEach var="unit" items="${unitList}">
                                    <c:if test="${ skuGenerate.unitId ==unit.unitId  }"> ${unit.unitName} </c:if>
                                </c:forEach>
                            </c:if>
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th customername pos_rel"><span>内含最小商品数量：
                            <c:if test="${oldSkuGenerate.changeNum ne skuGenerate.changeNum and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:choose>
                                    <c:when test="${oldSkuGenerate.changeNum eq null}">
                                        待完善
                                    </c:when>
                                    <c:otherwise>
                                        <c:if test="${not empty unitList }">
                                            <c:forEach var="unit" items="${unitList}">
                                                <c:if test="${ oldSkuGenerate.unitId ==unit.unitId  }"> ${ oldSkuGenerate.changeNum} ${unit.unitName} </c:if>
                                            </c:forEach>
                                        </c:if>
                                    </c:otherwise>
                                </c:choose>
                            </div></c:if>
                            <c:if test="${skuGenerate.changeNum eq null}">
                                <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                    <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="changeNum">
                                        <div class="tip arrow-left">
                                            <div class="tip-con">
                                                待完善
                                            </div>
                                            <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                        </div>
                                    </i>
                                </div>
                            </c:if>
                        </div>
                        <div class="table-td">
                            <c:if test="${not empty unitList }">
                                <c:forEach var="unit" items="${unitList}">
                                    <c:if test="${ skuGenerate.unitId ==unit.unitId  }"> ${ skuGenerate.changeNum} ${unit.unitName} </c:if>
                                </c:forEach>
                            </c:if>
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">最小起订量：
                            <c:if test="${empty skuGenerate.minOrder}">
                                <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                    <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="minOrder">
                                        <div class="tip arrow-left">
                                            <div class="tip-con">
                                                待完善
                                            </div>
                                            <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                        </div>
                                    </i>
                                </div>
                            </c:if>
                        </div>
                        <div class="table-td">

                            <c:if test="${not empty unitList }">
                                <c:forEach var="unit" items="${unitList}">
                                    <c:if test="${ skuGenerate.baseUnitId ==unit.unitId  }">
                                        <fmt:formatNumber value="${ skuGenerate.minOrder}"  pattern="0"/>
                                        ${unit.unitName}
                                    </c:if>
                                </c:forEach>
                            </c:if>
                        </div>
                    </div>

                </c:if>

                <div class="table-item">
                    <div class="table-th customername pos_rel"><span>箱包装体积：
                        <fmt:formatNumber var="oldPackageLength" value="${ oldSkuGenerate.packageLength}"  pattern="0.00"/>
                        <fmt:formatNumber var="oldPackageWidth" value="${ oldSkuGenerate.packageWidth}"  pattern="0.00"/>
                        <fmt:formatNumber var="oldPackageHeight" value="${ oldSkuGenerate.packageHeight}"  pattern="0.00"/>
                        <c:if test="${(oldPackageLength ne skuGenerate.packageLength or oldPackageWidth ne skuGenerate.packageWidth or oldPackageHeight ne skuGenerate.packageHeight) and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：
                            <c:choose>
                                <c:when test="${oldSkuGenerate.packageLength eq null or oldSkuGenerate.packageWidth eq null or oldSkuGenerate.packageHeight eq null}">
                                    待完善
                                </c:when>
                                <c:otherwise>
                                    长度 ${oldSkuGenerate.packageLength}cm 宽度 ${oldSkuGenerate.packageWidth}cm 高度 ${oldSkuGenerate.packageHeight}cm
                                </c:otherwise>
                            </c:choose>
                        </div></c:if>
                        <c:if test="${skuGenerate.packageLength eq null or skuGenerate.packageWidth eq null or skuGenerate.packageHeight eq null}">
                            <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="packageLength">
                                    <div class="tip arrow-left">
                                        <div class="tip-con">
                                            待完善
                                        </div>
                                        <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                    </div>
                                </i>
                            </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                        长度 ${skuGenerate.packageLength}cm 宽度 ${skuGenerate.packageWidth}cm 高度 ${skuGenerate.packageHeight}cm
                    </div>
                </div>
                <c:if test="${command.skuType!=1}">
                    <div class="table-item">
                        <div class="table-th">商品体积：</div>
                        <div class="table-td">
                            长度 ${skuGenerate.goodsLength}cm 宽度 ${skuGenerate.goodsWidth}cm 高度 ${skuGenerate.goodsHeight}cm
                        </div>
                    </div>
                </c:if>
                <div class="table-item">
                    <div class="table-th customername pos_rel"><span>毛重（箱）：
                        <fmt:formatNumber var="oldGrossWeight" value="${ oldSkuGenerate.grossWeight}"  pattern="0.00"/>
                        <c:if test="${oldGrossWeight ne skuGenerate.grossWeight and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：
                                ${oldSkuGenerate.grossWeight}kg
                        </div></c:if>
                        <c:if test="${skuGenerate.grossWeight eq null}">
                            <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="grossWeight">
                                    <div class="tip arrow-left">
                                        <div class="tip-con">
                                            待完善
                                        </div>
                                        <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                    </div>
                                </i>
                            </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                        ${skuGenerate.grossWeight}kg
                    </div>
                </div>
                <c:if test="${command.skuType!=1}">
                    <div class="table-item">
                        <div class="table-th">净重：</div>
                        <div class="table-td">
                            ${skuGenerate.netWeight}kg
                        </div>
                    </div>
                </c:if>
                <c:if test="${command.skuType==1}">
                    <div class="table-item">
                        <div class="table-th customername pos_rel"><span>包装清单：
                            <c:if test="${oldSkuGenerate.packingList ne skuGenerate.packingList and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                    ${oldSkuGenerate.packingList}
                            </div></c:if>
                            <c:if test="${empty skuGenerate.packingList}">
                                <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                    <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="packingList">
                                        <div class="tip arrow-left">
                                            <div class="tip-con">
                                                待完善
                                            </div>
                                            <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                        </div>
                                    </i>
                                </div>
                            </c:if>
                        </div>
                        <div class="table-td">
                            ${skuGenerate.packingList}
                        </div>
                    </div>
                </c:if>
            </div>

        </div>
        <%--<c:if test="${command.skuType!=1}">--%>
            <div class="detail-block">
                <div class="block-title">存储与效期</div>
                <div class="detail-table">
                    <div class="table-item item-col">
                        <div class="table-th customername pos_rel">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px"><span>
                            存储条件（温度）：
                            <c:if test="${oldSkuGenerate.storageConditionOne ne skuGenerate.storageConditionOne and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:choose>
                                    <c:when test="${oldSkuGenerate.storageConditionOne eq null}">
                                        待完善
                                    </c:when>
                                    <c:otherwise>
                                        <c:if test="${ oldSkuGenerate.storageConditionOne ==1 }"> 常温0-30℃ </c:if>
                                        <c:if test="${ oldSkuGenerate.storageConditionOne ==2 }"> 阴凉0-20℃ </c:if>
                                        <c:if test="${ oldSkuGenerate.storageConditionOne ==3 }"> 冷藏2-10℃   </c:if>
                                        <c:if test="${ oldSkuGenerate.storageConditionOne ==4 }">
                                            <c:if test="${not empty oldSkuGenerate.storageConditionOneLowerValue and not empty oldSkuGenerate.storageConditionOneUpperValue}">
                                                <c:choose>
                                                <c:when test="${oldSkuGenerate.storageConditionOneLowerValue == oldSkuGenerate.storageConditionOneUpperValue}">
                                                    其他温度<fmt:formatNumber value="${oldSkuGenerate.storageConditionOneUpperValue}" type="number"/>℃
                                                </c:when>
                                                <c:otherwise>
                                                    其他温度<fmt:formatNumber value="${oldSkuGenerate.storageConditionOneLowerValue}" type="number"/>℃
                                                    -
                                                    <fmt:formatNumber value="${oldSkuGenerate.storageConditionOneUpperValue}" type="number"/>℃
                                                </c:otherwise>
                                                </c:choose>
                                            </c:if>
                                        </c:if>
                                    </c:otherwise>
                                </c:choose>
                            </div></c:if>
                            <c:if test="${skuGenerate.storageConditionOne eq null}">
                                <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                    <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="storageConditionTmeperature">
                                        <div class="tip arrow-left">
                                            <div class="tip-con">
                                                待完善
                                            </div>
                                            <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                        </div>
                                    </i>
                                </div>
                            </c:if>
                        </div>
                        <div class="table-td">
                            <c:if test="${ skuGenerate.storageConditionOne ==1 }"> 常温0-30℃ </c:if>
                            <c:if test="${ skuGenerate.storageConditionOne ==2 }"> 阴凉0-20℃ </c:if>
                            <c:if test="${ skuGenerate.storageConditionOne ==3 }"> 冷藏2-10℃   </c:if>
                            <c:if test="${ skuGenerate.storageConditionOne ==4 }">
                                <c:if test="${not empty skuGenerate.storageConditionOneLowerValue and not empty skuGenerate.storageConditionOneUpperValue}">
                                    <c:choose>
                                    <c:when test="${skuGenerate.storageConditionOneLowerValue == skuGenerate.storageConditionOneUpperValue}">
                                        其他温度<fmt:formatNumber value="${skuGenerate.storageConditionOneUpperValue}" type="number"/>℃
                                    </c:when>
                                    <c:otherwise>
                                        其他温度<fmt:formatNumber value="${skuGenerate.storageConditionOneLowerValue}" type="number"/>℃
                                        -
                                        <fmt:formatNumber value="${skuGenerate.storageConditionOneUpperValue}" type="number"/>℃
                                    </c:otherwise>
                                    </c:choose>
                                </c:if>
                            </c:if>
                        </div>
                    </div>
                    <div class="table-item item-col">
                        <div class="table-th customername pos_rel">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px"><span>
                            存储条件(湿度）：
                            <c:if test="${oldSkuGenerate.storageConditionHumidityLowerValue ne skuGenerate.storageConditionHumidityLowerValue and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:choose>
                                    <c:when test="${empty oldSkuGenerate.storageConditionHumidityLowerValue or empty oldSkuGenerate.storageConditionHumidityUpperValue}">
                                        待完善
                                    </c:when>
                                    <c:otherwise>
                                        <fmt:formatNumber value="${oldSkuGenerate.storageConditionHumidityLowerValue}" type="number"/>%
                                        -
                                        <fmt:formatNumber value="${oldSkuGenerate.storageConditionHumidityUpperValue}" type="number"/>%
                                    </c:otherwise>
                                </c:choose>
                            </div></c:if>
                            <c:if test="${empty skuGenerate.storageConditionHumidityLowerValue or empty skuGenerate.storageConditionHumidityUpperValue}">
                                <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                    <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="storageConditionHumidity">
                                        <div class="tip arrow-left">
                                            <div class="tip-con">
                                                待完善
                                            </div>
                                            <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                        </div>
                                    </i>
                                </div>
                            </c:if>
                        </div>
                        <div class="table-td">
                            <c:if test="${not empty skuGenerate.storageConditionHumidityLowerValue and not empty skuGenerate.storageConditionHumidityUpperValue}">
                                <fmt:formatNumber value="${skuGenerate.storageConditionHumidityLowerValue}" type="number"/>%
                                -
                                <fmt:formatNumber value="${skuGenerate.storageConditionHumidityUpperValue}" type="number"/>%
                            </c:if>
                        </div>
                    </div>
                    <div class="table-item item-col">
                        <div class="table-th customername pos_rel">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px"><span>
                            存储条件（其他）：
                            <c:if test="${oldSkuGenerate.storageConditionTwo ne skuGenerate.storageConditionTwo and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:choose>
                                    <c:when test="${empty oldSkuGenerate.storageConditionTwo}">
                                        待完善
                                    </c:when>
                                    <c:otherwise>
                                        <c:forEach items="${oldSkuGenerate.storageConditionTwo}" var="item">
                                            <c:if test="${fn:contains(item, '1') }"> 通风 </c:if>
                                            <c:if test="${fn:contains(item, '2')}"> 干燥 </c:if>
                                            <c:if test="${fn:contains(item, '3') }"> 避光 </c:if>
                                            <c:if test="${fn:contains(item, '4') }"> 防潮 </c:if>
                                            <c:if test="${fn:contains(item, '5') }"> 避热 </c:if>
                                            <c:if test="${fn:contains(item, '6')}"> 密封 </c:if>
                                            <c:if test="${fn:contains(item, '7') }"> 密闭 </c:if>
                                            <c:if test="${fn:contains(item, '8') }"> 严封 </c:if>
                                            <c:if test="${fn:contains(item, '9') }"> 遮光 </c:if>
                                        </c:forEach>
                                    </c:otherwise>
                                </c:choose>
                            </div></c:if>
                            <c:if test="${empty skuGenerate.storageConditionTwo}">
                                <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                    <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="storageConditionOthersArray">
                                        <div class="tip arrow-left">
                                            <div class="tip-con">
                                                待完善
                                            </div>
                                            <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                        </div>
                                    </i>
                                </div>
                            </c:if>
                        </div>
                        <div class="table-td">
                            <c:forEach items="${skuGenerate.storageConditionTwo}" var="item">
                                <c:if test="${fn:contains(item, '1') }"> 通风 </c:if>
                                <c:if test="${fn:contains(item, '2')}"> 干燥 </c:if>
                                <c:if test="${fn:contains(item, '3') }"> 避光 </c:if>
                                <c:if test="${fn:contains(item, '4') }"> 防潮 </c:if>
                                <c:if test="${fn:contains(item, '5') }"> 避热 </c:if>
                                <c:if test="${fn:contains(item, '6')}"> 密封 </c:if>
                                <c:if test="${fn:contains(item, '7') }"> 密闭 </c:if>
                                <c:if test="${fn:contains(item, '8') }"> 严封 </c:if>
                                <c:if test="${fn:contains(item, '9') }"> 遮光 </c:if>
                            </c:forEach>
                        </div>
                    </div>
                    <div class="table-item item-col">
                        <div class="table-th customername pos_rel">
                            使用年限：</div>
                        <div class="table-td">
                            ${skuGenerate.serviceLife} &nbsp;年
                        </div>
                    </div>
                    <c:if test="${not empty skuGenerate.effectiveDays and skuGenerate.effectiveDays > 0}">
                    <div class="table-item item-col">
                        <div class="table-th customername pos_rel">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px"><span>
                            有效期：
                            <c:if test="${oldSkuGenerate.effectiveDays ne skuGenerate.effectiveDays and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                <c:choose>
                                    <c:when test="${empty oldSkuGenerate.effectiveDays}">
                                        待完善
                                    </c:when>
                                    <c:otherwise>
                                        <c:if test="${ oldSkuGenerate.effectiveDayUnit==1 and oldSkuGenerate.effectiveDays > 0}"> ${oldSkuGenerate.effectiveDays}天 </c:if>
                                        <c:if test="${ oldSkuGenerate.effectiveDayUnit==2 and oldSkuGenerate.effectiveDays > 0}"> ${oldSkuGenerate.effectiveDays}月 </c:if>
                                        <c:if test="${ oldSkuGenerate.effectiveDayUnit==3 and oldSkuGenerate.effectiveDays > 0}"> ${oldSkuGenerate.effectiveDays}年 </c:if>
                                    </c:otherwise>
                                </c:choose>
                            </div></c:if>
                            <c:if test="${empty skuGenerate.effectiveDays}">
                                <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                    <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="isEnableValidityPeriod">
                                        <div class="tip arrow-left">
                                            <div class="tip-con">
                                                待完善
                                            </div>
                                            <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                        </div>
                                    </i>
                                </div>
                            </c:if>
                        </div>
                        <div class="table-td">
                            <c:if test="${ skuGenerate.effectiveDayUnit==1 and skuGenerate.effectiveDays > 0}"> ${skuGenerate.effectiveDays}天 </c:if>
                            <c:if test="${ skuGenerate.effectiveDayUnit==2 and skuGenerate.effectiveDays > 0}"> ${skuGenerate.effectiveDays}月 </c:if>
                            <c:if test="${ skuGenerate.effectiveDayUnit==3 and skuGenerate.effectiveDays > 0}"> ${skuGenerate.effectiveDays}年 </c:if>
                        </div>
                    </div>

                    <div class="table-item">
                        <div class="table-th customername pos_rel"><span>近效期预警天数：
                        <c:if test="${oldSkuGenerate.nearTermWarnDays ne skuGenerate.nearTermWarnDays and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                            <i class="iconbluesigh ml4 contorlIcon"></i> </span>
                            <div class="pos_abs customernameshow">原值：
                                    ${oldSkuGenerate.nearTermWarnDays}
                            </div></c:if>
                        </div>
                        <div class="table-td">
                            ${skuGenerate.nearTermWarnDays}
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th customername pos_rel"><span>超近效期预警天数：
                        <c:if test="${oldSkuGenerate.overNearTermWarnDays ne skuGenerate.overNearTermWarnDays and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：
                                    ${oldSkuGenerate.overNearTermWarnDays}
                            </div></c:if>
                        </div>
                        <div class="table-td">
                            ${skuGenerate.overNearTermWarnDays}
                        </div>
                    </div>
                    </c:if>

                    <c:if test="${coreSpuDto.spuType==316}">
                        <div class="table-item">
                            <div class="table-th customername pos_rel"><span>养护类型（质管专用）：
                                <c:if test="${oldCommand.curingTypeDesc ne command.curingTypeDesc and not empty oldCommand and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                                <i class="iconbluesigh ml4 contorlIcon"></i></span>
                                <div class="pos_abs customernameshow">原值：
                                    ${not empty oldCommand.curingTypeDesc?oldCommand.curingTypeDesc:'不养护'}
                                </div></c:if>
                            </div>
                            <div class="table-td">
                                    ${not empty command.curingTypeDesc?command.curingTypeDesc:'不养护'}
                            </div>
                        </div>
                    </c:if>
                </div>
            </div>
        <%--</c:if>//--%>

        <div class="detail-block">
            <div class="block-title">报备信息</div>
            <div class="detail-table">
                <div class="table-item item-col">
                    <div class="table-th customername pos_rel"><span>是否需要报备：
                        <c:if test="${oldSkuGenerate.isNeedReport ne skuGenerate.isNeedReport and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：
                            <c:choose>
                                <c:when test="${oldSkuGenerate.isNeedReport eq null}">
                                    待完善
                                </c:when>
                                <c:otherwise>
                                    <c:choose>
                                        <c:when test="${oldSkuGenerate.isNeedReport eq 1}">
                                            需报备
                                        </c:when>
                                        <c:when test="${oldSkuGenerate.isNeedReport eq 0}">
                                            无需报备
                                        </c:when>
                                        <c:otherwise>
                                            -
                                        </c:otherwise>
                                    </c:choose>
                                </c:otherwise>
                            </c:choose>
                        </div></c:if>
                        <c:if test="${skuGenerate.isNeedReport eq null}">
                            <div class="tip-wrap"  style="position: absolute; z-index: 10">
                                <i class="vd-icon icon-info2 goods-required-item" style="color: #eFcC47" itemName="isNeedReport">
                                    <div class="tip arrow-left">
                                        <div class="tip-con">
                                            待完善
                                        </div>
                                        <span class="arrow arrow-out"><span class="arrow arrow-in"></span></span>
                                    </div>
                                </i>
                            </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                        <c:choose>
                            <c:when test="${skuGenerate.isNeedReport eq 1}">
                                需报备
                            </c:when>
                            <c:when test="${skuGenerate.isNeedReport eq 0}">
                                无需报备
                            </c:when>
                            <c:otherwise>
                                -
                            </c:otherwise>
                        </c:choose>
                    </div>
                </div>

                 <c:if test="${skuGenerate.isNeedReport eq 1}">
                    <div class="table-item item-col">
                    <div class="table-th customername pos_rel">是否获得授权：
                        <c:if test="${oldSkuGenerate.isAuthorized ne skuGenerate.isAuthorized and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                            <i class="iconbluesigh ml4 contorlIcon"></i>
                            <div class="pos_abs customernameshow">原值：
                                <c:if test="${oldSkuGenerate.isAuthorized eq 1}">
                                    有授权
                                </c:if>
                                <c:if test="${oldSkuGenerate.isAuthorized eq 0}">
                                    无授权
                                </c:if>
                            </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                    <c:if test="${skuGenerate.isAuthorized eq 1}">
                        有授权
                    </c:if>
                    <c:if test="${skuGenerate.isAuthorized eq 0}">
                        无授权
                    </c:if>
                    </div>
                    </div>
                </c:if>

                <c:if test="${skuGenerate.isNeedReport eq 1 && skuGenerate.isAuthorized eq 1}">
                    <div class="table-item item-col">
                    <div class="table-th customername pos_rel">授权范围
                        <c:if test="${isChange eq true and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                            <i class="iconbluesigh ml4 contorlIcon"></i>
                            <div class="pos_abs customernameshow">原值：
                                <c:forEach items="${oldSkuAuthorizationItemVoList}" var="oldSkuAuthorizationItem">
                                    <c:choose>
                                        <c:when test="${fn:length(oldSkuAuthorizationItem.regionIds) == fn:length(regions)}">
                                            “全国”
                                        </c:when>
                                        <c:otherwise>
                                            <c:forEach items="${oldSkuAuthorizationItem.regionIds}" var="regionId"  varStatus="regionStatus">
                                                <c:if test="${regionStatus.first}">
                                                    "
                                                </c:if>
                                                <c:forEach items="${regions}" var="region">
                                                    <c:if test="${region.regionId eq regionId}">
                                                        ${region.regionName}
                                                    </c:if>
                                                </c:forEach>
                                                <c:if test="${!regionStatus.last}">
                                                    、
                                                </c:if>
                                                <c:if test="${regionStatus.last}">
                                                    "
                                                </c:if>
                                            </c:forEach>
                                        </c:otherwise>
                                    </c:choose>
                                    的
                                    <c:choose>
                                        <c:when test="${fn:length(oldSkuAuthorizationItem.terminalTypeIds) == fn:length(terminalTypes)}">
                                            “全部终端”
                                        </c:when>
                                        <c:otherwise>
                                            <c:forEach items="${oldSkuAuthorizationItem.terminalTypeIds}" var="terminalTypeId" varStatus="terminalTypeStatus">
                                                <c:if test="${terminalTypeStatus.first}">
                                                    "
                                                </c:if>
                                                <c:forEach items="${terminalTypes}" var="terminalType">
                                                    <c:if test="${terminalType.sysOptionDefinitionId eq terminalTypeId}">
                                                        ${terminalType.title}
                                                    </c:if>
                                                </c:forEach>
                                                <c:if test="${!terminalTypeStatus.last}">
                                                    、
                                                </c:if>
                                                <c:if test="${terminalTypeStatus.last}">
                                                    "
                                                </c:if>
                                            </c:forEach>
                                        </c:otherwise>
                                    </c:choose>
                                    已获得授权
                                    </br>
                                </c:forEach>
                            </div>
                        </c:if>
                    </div>
                    <div class="table-td">
                    <c:forEach items="${skuAuthorizationInfo.skuAuthorizationItemVoList}" var="skuAuthorizationItem">
                        <c:choose>
                            <c:when test="${fn:length(skuAuthorizationItem.regionIds) == fn:length(regions)}">
                                “全国”
                            </c:when>
                            <c:otherwise>
                                <c:forEach items="${skuAuthorizationItem.regionIds}" var="regionId"  varStatus="regionStatus">
                                    <c:if test="${regionStatus.first}">
                                        "
                                    </c:if>
                                    <c:forEach items="${regions}" var="region">
                                        <c:if test="${region.regionId eq regionId}">
                                            ${region.regionName}
                                        </c:if>
                                    </c:forEach>
                                    <c:if test="${!regionStatus.last}">
                                        、
                                    </c:if>
                                    <c:if test="${regionStatus.last}">
                                        "
                                    </c:if>
                                </c:forEach>
                            </c:otherwise>
                        </c:choose>
                        的
                        <c:choose>
                            <c:when test="${fn:length(skuAuthorizationItem.terminalTypeIds) == fn:length(terminalTypes)}">
                                “全部终端”
                            </c:when>
                            <c:otherwise>
                                <c:forEach items="${skuAuthorizationItem.terminalTypeIds}" var="terminalTypeId" varStatus="terminalTypeStatus">
                                    <c:if test="${terminalTypeStatus.first}">
                                        "
                                    </c:if>
                                    <c:forEach items="${terminalTypes}" var="terminalType">
                                        <c:if test="${terminalType.sysOptionDefinitionId eq terminalTypeId}">
                                            ${terminalType.title}
                                        </c:if>
                                    </c:forEach>
                                    <c:if test="${!terminalTypeStatus.last}">
                                        、
                                    </c:if>
                                    <c:if test="${terminalTypeStatus.last}">
                                        "
                                    </c:if>
                                </c:forEach>
                            </c:otherwise>
                        </c:choose>
                        已获得授权
                        </br>
                    </c:forEach>
                    <label style="color: red;font-weight: bold">注：除以上范围，需要报备。</label>
                    </div>
                    </div>
                    </div>
                </c:if>
        </div>

        <div class="detail-block">
            <div class="block-title">签约模式</div>
            <div class="detail-table">
                <div class="table-item item-col">
                    <div class="table-th customername pos_rel"><span>签约模式
                        <c:if test="${oldCommand.goodsSignContractModeStr ne command.goodsSignContractModeStr and not empty oldCommand and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：
                                ${oldCommand.goodsSignContractModeStr}
                        </div></c:if>
                    </div>
                    <div class="table-td">
                        ${command.goodsSignContractModeStr}
                    </div>
                </div>
            </div>
        </div>

        <div class="detail-block" style="overflow: inherit;">
            <div class="block-title customername pos_rel"><span>产品资料
                <c:if test="${(oldSkuGenerate.wikiHref ne skuGenerate.wikiHref  or skuCheckChange eq true) and not empty oldSkuGenerate and skuGenerate.checkStatus == 1 and command.hasCheckAuth}">
                <i class="iconbluesigh ml4 contorlIcon"></i></span>
                <div class="pos_abs customernameshow history" style="width: auto">原值：<br>
                    Wiki链接：
                    <c:choose>
                        <c:when test="${empty oldSkuGenerate.wikiHref}">
                            无
                        </c:when>
                        <c:otherwise>
                            ${oldSkuGenerate.wikiHref}
                        </c:otherwise>
                    </c:choose>
                    <br>检测报告：<br>
                    <div class="info-pic">
                    <c:forEach items="${oldCommand.skuCheck}" var="item">
                        <c:if test="${not item.pdfFlag}">
                            <div style="width: 50px;height:55px;margin-right: 20px;">
                                <div class="info-pic-item J-show-big" data-src="${item.ossUrl}">
                                    <img src="${item.ossUrl}">
                                </div>
                                <div title="${not empty item.fileName?item.fileName:'检查报告'}" style="width: 50px;white-space:nowrap; text-overflow:ellipsis; overflow: hidden;">
                                    ${not empty item.fileName?item.fileName:'检查报告'}
                                </div>
                            </div>
                        </c:if>
                    </c:forEach>
                    <c:forEach items="${oldCommand.skuCheck}" var="item">
                        <c:if test="${item.pdfFlag}">
                            <div style="width: 50px;height:80px;margin-right: 20px; ">
                                <div class="info-pic-item J-show-file"  data-src="${item.ossUrl}">
                                    <img src="${pageContext.request.contextPath}/static/images/pdf_icon.png">
                                </div>
                                <div title="${not empty item.fileName?item.fileName:'检查报告'}" style="width: 50px;white-space:nowrap; text-overflow:ellipsis; overflow: hidden;">
                                    ${not empty item.fileName?item.fileName:'检查报告'}
                                </div>
                            </div>
                        </c:if>
                    </c:forEach>
                    </div>
                </div></c:if>
            </div>
            <div class="detail-table">
                <div class="table-item item-col">
                    <div class="table-th">Wiki链接：</div>
                    <div class="table-td">
                        <a href="${skuGenerate.wikiHref}" target="_blank">${skuGenerate.wikiHref}</a>
                    </div>
                </div>
                <div class="table-item item-col">
                    <div class="table-th">检测报告</div>
                    <div class="table-td">
                        <div class="info-pic">
                            <c:forEach items="${command.skuCheck}" var="item">
                                <c:if test="${not item.pdfFlag}">
                                    <div style="width: 50px;height:55px;margin-right: 20px;">
                                        <div class="info-pic-item J-show-big" data-src="${item.ossUrl}">
                                            <img src="${item.ossUrl}">
                                        </div>
                                        <div title="${not empty item.fileName?item.fileName:'检查报告'}" style="width: 50px;white-space:nowrap; overflow: hidden; text-overflow: ellipsis;">
                                            ${not empty item.fileName?item.fileName:'检查报告'}
                                        </div>
                                    </div>
                                </c:if>
                            </c:forEach>
                            <c:forEach items="${command.skuCheck}" var="item">
                                <c:if test="${item.pdfFlag}">
                                    <div style="width: 50px;height:80px;margin-right: 20px; ">
                                        <div class="info-pic-item J-show-file"  data-src="${item.ossUrl}">
                                            <img src="${pageContext.request.contextPath}/static/images/pdf_icon.png">
                                        </div>
                                        <div title="${not empty item.fileName?item.fileName:'检查报告'}" style="width: 50px;white-space:nowrap; overflow: hidden; text-overflow: ellipsis;">
                                            ${not empty item.fileName?item.fileName:'检查报告'}
                                        </div>
                                    </div>
                                </c:if>
                            </c:forEach>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <div class="detail-block">
        <div class="block-title">
            禁用审核记录
        </div>
        <div class="status-title">
            <div class="status-item">操作时间</div>
            <div class="status-item">操作人</div>
            <div class="status-item">操作事项</div>
            <div class="status-item">审核状态</div>
            <div class="status-item" style="width:300px">备注</div>
        </div>
        <div class="status-list">
            <c:choose>
                <c:when test="${not empty disableGoodsVerifyRecord}">
                    <c:forEach items="${disableGoodsVerifyRecord}" var="log" varStatus="index">
                        <c:if test="${not empty  log.operate}">
                            <c:if test="${index.count==6}">
                                <div class="status-more J-optional-more">
                            </c:if>
                                    <div class="status-cnt">
                                        <div class="status-item">
                                            <fmt:formatDate value="${log.operateTime}" pattern="yyyy-MM-dd HH:mm:ss" />
                                        </div>
                                        <div class="status-item" style="word-wrap: break-word;">${log.operaterName}</div>
                                        <div class="status-item">${log.operate}</div>
                                        <div class="status-item">
                                            <c:choose>
                                                <c:when test="${log.operate == '审核不通过'}">审核不通过</c:when>
                                                <c:when test="${log.operate == '审核通过'}">审核通过</c:when>
                                                <c:otherwise>审核中</c:otherwise>
                                            </c:choose>
                                        </div>
                                        <div class="status-item" style="width:300px">${log.comment}</div>
                                    </div>
                            <c:if test="${disableGoodsVerifyRecord.size()== index.count && index.count>5 }">
                                </div>
                            </c:if>
                        </c:if>
                    </c:forEach>
                </c:when>
                <c:otherwise>
                    <div class="status-cnt">
                        <div class="status-item"></div>
                        <div class="status-item"></div>
                        <div class="status-item" style="text-align: center;">暂无审核记录！</div>
                    </div>
                </c:otherwise>
            </c:choose>
        </div>
        <c:if test="${disableGoodsVerifyRecord.size() > 5 }">
            <div class="detail-optional J-toggle-show">
                <span class="toggle-txt J-more">展开更多<i class="vd-icon icon-down" style="background: none"></i></span>
                <span class="toggle-txt J-less" style="display: none;">收起<i class="vd-icon icon-up" style="background: none"></i></span>
            </div>
        </c:if>

        <c:if test="${skuGenerate.status == 2}">
            <div style="text-align: center">
               <c:if test="${ not empty disableGoodsTask && isDisableGoodsCandidate}">
                   <a class="btn btn-small btn-blue J-sku-disableOption" data-dlg='0' data-href="/goods/vgoods/complementTask.do" data-params='{"taskId":${disableGoodsTask.id},"pass": true, "relatedId":${skuGenerate.skuId},"goodsType": 2 }'>审核通过</a>
                   &nbsp; &nbsp; &nbsp;&nbsp; &nbsp; &nbsp;
                   <a class="btn btn-small btn-red J-sku-disableOption" data-href="/goods/vgoods/complementTask.do" data-params='{"taskId":${disableGoodsTask.id},"pass": false, "relatedId":${skuGenerate.skuId},"goodsType": 2 }'>审核不通过</a>
               </c:if>
            </div>
        </c:if>
    </div>

        <div class="detail-block">
            <div class="block-title">审核记录
                <c:if test="${skuGenerate.checkStatus==2}">
                    <span class="title-status status-red">审核不通过</span>
                </c:if>
                <c:if test="${skuGenerate.checkStatus==1}">
                    <span class="title-status status-yellow">审核中</span>
                </c:if>
                <c:if test="${skuGenerate.checkStatus==3}">
                    <span class="title-status status-green">审核通过</span>
                </c:if>
                <c:if test="${skuGenerate.checkStatus==0}">
                    <span class="title-status status-yellow">待完善</span>
                </c:if>
            </div>
            <div class="status-title">
                <div class="status-item">操作时间</div>
                <div class="status-item">操作人</div>
                <div class="status-item">操作事项</div>
                <div class="status-item">审核状态</div>
                <div class="status-item" style="width:300px">备注</div>
            </div>
            <div class="status-list">
                <c:choose>
                    <c:when test="${not empty newLogCheckList}">
                        <c:forEach items="${newLogCheckList}" var="log" varStatus="index">
                            <c:if test="${index.count==6}">
                                <div class="status-more J-optional-more">
                            </c:if>
                            <div class="status-cnt">
                                <div class="status-item"><fmt:formatDate value="${log.addTime}" pattern="yyyy-MM-dd HH:mm:ss" /></div>
                                <div class="status-item">${log.creatorName}</div>
                                <div class="status-item">${log.logStatusName}</div>
                                <div class="status-item">
                                    <c:choose>
                                        <c:when test="${log.logStatus == 1}">审核中</c:when>
                                        <c:when test="${log.logStatus == 2}">审核不通过</c:when>
                                        <c:when test="${log.logStatus == 3}">审核通过</c:when>
                                    </c:choose>
                                </div>
                                <div class="status-item" style="width:300px">${log.logMessage}</div>
                            </div>
                            <c:if test="${newLogCheckList.size()== index.count && index.count>5 }">
                                </div>
                            </c:if>
                        </c:forEach>
                    </c:when>
                    <c:otherwise>
                        <div class="status-cnt">
                            <div class="status-item"></div>
                            <div class="status-item"></div>
                            <div class="status-item" style="text-align: center;">暂无审核记录！</div>
                        </div>
                    </c:otherwise>
                </c:choose>
            </div>
            <c:if test="${newLogCheckList.size() > 5 }">
                <div class="detail-optional J-toggle-show">
                    <span class="toggle-txt J-more">展开更多<i class="vd-icon icon-down" style="background: none"></i></span>
                    <span class="toggle-txt J-less" style="display: none;">收起<i class="vd-icon icon-up" style="background: none"></i></span>
                </div>
            </c:if>
        </div>

        <div class="detail-block">
            <div class="block-title">审核记录（旧）
                <c:if test="${skuGenerate.checkStatus==2}">
                    <span class="title-status status-red">审核不通过</span>
                </c:if>
                <c:if test="${skuGenerate.checkStatus==1}">
                    <span class="title-status status-yellow">审核中</span>
                </c:if>
                <c:if test="${skuGenerate.checkStatus==3}">
                    <span class="title-status status-green">审核通过</span>
                </c:if>
                <c:if test="${skuGenerate.checkStatus==0}">
                    <span class="title-status status-yellow">待完善</span>
                </c:if>
            </div>
            <div class="status-title">
                <div class="status-item">操作时间</div>
                <div class="status-item">操作人</div>
                <div class="status-item">操作事项</div>
                <div class="status-item">审核状态</div>
                <div class="status-item" style="width:300px">备注</div>
            </div>
            <div class="status-list">
                <c:choose>
                    <c:when test="${not empty oldLogCheckList}">
                        <c:forEach items="${oldLogCheckList}" var="log" varStatus="index">
                            <c:if test="${index.count==6}">
                                <div class="status-more J-optional-more">
                            </c:if>
                            <div class="status-cnt">
                                <div class="status-item"><fmt:formatDate value="${log.addTime}" pattern="yyyy-MM-dd HH:mm:ss" /></div>
                                <div class="status-item">${log.creatorName}</div>
                                <div class="status-item">${log.logStatusName}</div>
                                <div class="status-item">
                                    <c:choose>
                                        <c:when test="${log.logStatus == 1}">审核中</c:when>
                                        <c:when test="${log.logStatus == 2}">审核不通过</c:when>
                                        <c:when test="${log.logStatus == 3}">审核通过</c:when>
                                    </c:choose>
                                </div>
                                <div class="status-item" style="width:300px">${log.logMessage}</div>
                            </div>
                            <c:if test="${oldLogCheckList.size()== index.count && index.count>5 }">
                                </div>
                            </c:if>
                        </c:forEach>
                    </c:when>
                    <c:otherwise>
                        <div class="status-cnt">
                            <div class="status-item"></div>
                            <div class="status-item"></div>
                            <div class="status-item" style="text-align: center;">暂无审核记录！</div>
                        </div>
                    </c:otherwise>
                </c:choose>
            </div>
            <c:if test="${oldLogCheckList.size() > 5 }">
                <div class="detail-optional J-toggle-show">
                    <span class="toggle-txt J-more">展开更多<i class="vd-icon icon-down" style="background: none"></i></span>
                    <span class="toggle-txt J-less" style="display: none;">收起<i class="vd-icon icon-up" style="background: none"></i></span>
                </div>
            </c:if>
        </div>

    </c:if>
    </div>
</body>
    <script type="text/tmpl" class="J-dlg-tmpl">
        <div class="del-wrap">
            <div class="del-tip">
                <i class="vd-icon icon-caution2"></i><span class="J-dlg-tip"></span>
            </div>
            <form class="J-dlg-form">
                <div class="del-cnt base-form">
                    <textarea name="content" id="" cols="30" rows="10" class="input-textarea J-dlg-cnt" placeholder=""></textarea>
                </div>
            </form>
        </div>
    </script>
    <script type="text/tmpl" class="J-sku-tmpl">
        <div class="edit-sku-wrap base-form form-span-6">
            <form class="J-sku-form" style="display: none;">
                <div class="form-item">
                    <div class="form-label">SPU：</div>
                    <div class="form-fields form-label-txt">{{=spuName}}</div>
                </div>
                <div class="form-item">
                    <div class="form-label">
                        <span class="must">*</span>
                        {{if(type == '1'){ }}
                            制造商型号：
                        {{ }else{ }}
                            规格：
                        {{ } }}
                    </div>
                    <div class="form-fields">
                        <div class="form-col col-10">
                            <input class="input-text J-cnt" name="content" type="text">
                        </div>
                    </div>
                </div>
            </form>
            <div class="dlg-loading J-sku-loading">
                <img src="${pageContext.request.contextPath}/static/new/img/loading.gif" alt="">
            </div>
        </div>
    </script>

    <script type="text/tmpl" class="J-price-one-tmpl">
        <div class="dlg-price-wrap">
            <div class="dlg-loading J-dlg-loading">
                <img src="${pageContext.request.contextPath}/static/new/img/loading.gif" alt="">
            </div>
            <div class="dlg-cnt J-dlg-cnt" style="display: none;">
                <table class="table table-normal">
                    <colgroup>
                        <col width="50%">
                        <col width="50%">
                    </colgroup>
                    <tbody class="J-dlg-list">
                        <tr>
                            <th>时间</th>
                            <th>价格</th>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </script>

    <script type="text/tmpl" class="J-price-more-tmpl">
        <div class="dlg-price-wrap">
            <div class="dlg-loading J-dlg-loading">
                <img src="${pageContext.request.contextPath}/static/new/img/loading.gif" alt="">
            </div>
            <div class="dlg-cnt J-dlg-cnt" style="display: none;">
                <table class="table table-normal">
                    <colgroup>
                        <col width="50%">
                        <col width="50%">
                    </colgroup>
                    <tbody class="J-dlg-list">
                        <tr>
                            <th>数量</th>
                            <th>价格</th>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </script>

    <script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/pages/goods/vgoods/sku_view.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript"
src='${pageContext.request.contextPath}/static/js/jquery.PrintArea.js'></script>
<script>
    $(function(){

        $(".printAtta").each(function(){
            $(this).click(function(){
                $(this).hide();
                $(this).prev().printArea();
                $(this).show()
            })
        })


        var priceChangeApplyId = "${command.priceChangeApplyId}";

        if(priceChangeApplyId != ""){
              layer.confirm("保存成功！是否继续维护核价信息？", {
                  btn: ['前往核价','返回']
              }, function(){
                    var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');

                    var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random()*1000);
                    var uri = "${pageContext.request.contextPath}/price/basePriceMaintain/edit.do?skuPriceChangeApplyId="+priceChangeApplyId;

                    var item = { 'id': id, 'name': "编辑", 'url': uri, 'closable': true};

                    self.parent.closableTab.addTab(item);
                    self.parent.closableTab.resizeMove();

                    $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);

              }, function(){

              });
        }
    })
</script>

<%@ include file="../../../common/footer.jsp"%>