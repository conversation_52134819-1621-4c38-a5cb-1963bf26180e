package com.vedeng.trader.service;

import com.vedeng.common.service.BaseService;
import com.vedeng.trader.model.CommunicateRecord;

/**
 * 座机通话记录Service
 *
 * <AUTHOR>
 */
public interface LandLineRecordService extends BaseService {

    /**
     * 通话记录信息保存座机记录
     *
     * @param communicateRecord
     * @return
     */
    int saveLandLineRecordByCommunicate(CommunicateRecord communicateRecord);
}
