package com.vedeng.mobile.system.mapper;

import com.vedeng.mobile.system.dto.RegionInnerDto;
import com.vedeng.mobile.system.model.Region;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/1/10 17:15
 */
@Repository("mobilRegionMapper")
public interface RegionMapper  {

    /**
     * 查询子id地区
     * @param topRegionId
     * @return
     */
    List<RegionInnerDto> queryRegionByTopId(@Param("parentId") Integer topRegionId);

    /**
     * 根据区的ID查询三级
     * @param regionId
     * @return
     */
    List<RegionInnerDto> queryRegionByThreeId(@Param("regionId") Integer regionId);

    /**
     * 查询父子两级地区
     * @param topRegionId
     * @return
     */
    List<RegionInnerDto> queryRegionLevelTwo(@Param("parentId") Integer topRegionId);


    /**
     * 父级查子级，获取下一级的所有区域
     * @param parentId
     * @return
     */
    List<RegionInnerDto> queryRegionByParentId(@Param("parentId") Integer parentId);

}
