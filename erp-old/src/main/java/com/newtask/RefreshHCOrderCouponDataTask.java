package com.newtask;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.putHCutil.service.HcSaleorderService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleorderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
@JobHandler(value="RefreshHCOrderCouponDataTask")
public class RefreshHCOrderCouponDataTask extends AbstractJobHandler {

    private Logger logger = LoggerFactory.getLogger(RefreshHCOrderCouponDataTask.class);

    @Autowired
    private SaleorderService saleorderService;

    @Autowired
    @Qualifier("hcSaleorderService")
    protected HcSaleorderService hcSaleorderService;

    @Value("${api_url}")
    private String apiUrl;

    private static String INVALID_ORDER = "coupon/queryOrderCouponData";


    @Override
    public ReturnT<String> doExecute(String executeParam) throws Exception {

        JSONObject resultJsonObj= NewHttpClientUtils.httpPost(apiUrl + INVALID_ORDER,"");

        logger.info("获取的api数据:" + resultJsonObj);

        String string = resultJsonObj.get("data").toString();
        List<Map> maps = com.alibaba.fastjson.JSONObject.parseArray(string, Map.class);

        List<String> refreshDataList = new ArrayList<>();

        for (Map map:maps) {
            try{
                String s = map.get("orderNo").toString();
                /*String s1 = map.get("couponId").toString();*/
                int couponId = Integer.parseInt(map.get("couponId").toString());
                String s2 = map.get("couponCode").toString();
                Saleorder saleorder=new Saleorder();
                saleorder.setSaleorderNo(s);
                Saleorder saleorderBySaleorderNo = saleorderService.getSaleorderBySaleorderNo(saleorder);
                if (saleorderBySaleorderNo==null){
                    continue;
                }

                refreshDataList.add("update T_SALEORDER_COUPON set COUPON_ID= "+couponId+",COUPON_CODE= "+s2+" where SALEORDER_ID= "+saleorderBySaleorderNo.getSaleorderId()+";");
            }catch (Exception e){
                logger.error("订单更新error:"+ map.get("orderNo").toString());
            }
        }

        logger.info("待写入的数据大小:" + refreshDataList.size());
        logger.info("待写入的数据:" + JSON.toJSONString(refreshDataList));

        writeFile(refreshDataList);

        return null;
    }

    /**
     * 刷新文件
     * @param refreshDataList
     */
    private void writeFile(List<String> refreshDataList) {
        try {

            String path = "/app/logs/erp/refreshData.txt";

            BufferedWriter bufferedWriter = new BufferedWriter(
                    new OutputStreamWriter(new FileOutputStream(path), "UTF-8"));
            for (String line : refreshDataList) {
                bufferedWriter.write(line.toString());
                bufferedWriter.newLine();
                bufferedWriter.flush();
            }
            bufferedWriter.close();

        } catch (Exception e) {
            logger.error("文件写入失败:" + e);
        }
    }
}
