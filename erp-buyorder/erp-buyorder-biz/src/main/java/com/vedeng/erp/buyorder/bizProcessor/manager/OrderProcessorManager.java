package com.vedeng.erp.buyorder.bizProcessor.manager;


import com.alibaba.fastjson.JSON;
import com.vedeng.erp.buyorder.bizProcessor.dto.BizDto;
import com.vedeng.erp.buyorder.bizProcessor.core.OrderCore;
import com.vedeng.erp.buyorder.bizProcessor.exception.PreProcessorException;
import com.vedeng.erp.buyorder.bizProcessor.processor.OrderAfterProcessor;
import com.vedeng.erp.buyorder.bizProcessor.processor.OrderPreProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class OrderProcessorManager {

    private List<OrderPreProcessor> preProcessorList ;

    private OrderCore orderCore;

    private List<OrderAfterProcessor> afterProcessorList ;

    private BizDto bizDto;
    /**
     *设置前处理链
     */
    public OrderProcessorManager setPre(OrderPreProcessor nextOrderProcessor){
        if(CollectionUtils.isEmpty(preProcessorList)){
            preProcessorList = new ArrayList<OrderPreProcessor>();
        }
        preProcessorList.add(nextOrderProcessor);
        return this;
    };

    /**
     * 设置后处理链
     */
    public OrderProcessorManager setAfter(OrderAfterProcessor nextOrderProcessor){
        if(CollectionUtils.isEmpty(afterProcessorList)){
            afterProcessorList = new ArrayList<OrderAfterProcessor>();
        }
        afterProcessorList.add(nextOrderProcessor);
        return this;
    };

    public void doPre(BizDto bizDto) throws PreProcessorException {
        if(CollectionUtils.isNotEmpty(preProcessorList)){
            for (OrderPreProcessor preProcessor : preProcessorList) {
                try {
                    preProcessor.doProcess(bizDto);
                } catch (PreProcessorException e) {
                    log.error("前处理逻辑出错,信息:{},错误:{}", JSON.toJSONString(bizDto),e);
                    throw e;
                }
            }
        }
    };

    public void doAfter(BizDto bizDto){
        if(CollectionUtils.isNotEmpty(afterProcessorList)){
            for (OrderAfterProcessor preProcessor : afterProcessorList) {
                try {
                    preProcessor.doProcess(bizDto);
                } catch (Exception e) {
                    log.error("售后处理逻辑出错,信息:{},错误:{}", JSON.toJSONString(bizDto),e);
                    throw e;
                }
            }
        }
    };

    public OrderProcessorManager setCoreBiz(OrderCore orderCore){
        this.orderCore = orderCore;
        return this;
    }

    public OrderProcessorManager setBizDto(BizDto bizDto){
        this.bizDto = bizDto;
        return this;
    }


    public void excute(){
        try {
            doPre(bizDto);
        } catch (PreProcessorException e) {
            log.error("前处理报错,信息:{},错误:{}", JSON.toJSONString(bizDto),e);
            log.info("终止执行");
            return;
        }
        try {
            orderCore.doCoreBiz(bizDto);
        } catch (Exception e) {
            log.error("处理核心业务出错,信息:{},错误:{}", JSON.toJSONString(bizDto),e);
            log.info("终止执行");
            return;
        }
        try {
            doAfter(bizDto);
        } catch (Exception e) {
            log.error("后处理逻辑出错,信息:{},错误:{}", JSON.toJSONString(bizDto),e);
            log.info("终止执行");
            return;
        }
    }
}
