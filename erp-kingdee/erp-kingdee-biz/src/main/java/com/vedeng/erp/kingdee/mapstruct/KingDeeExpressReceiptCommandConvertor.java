package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeExpressReceiptCommand;
import com.vedeng.erp.kingdee.dto.KingDeeExpressReceiptDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售安调 dto
 * @date
 */
@Mapper(componentModel = "spring")
public interface KingDeeExpressReceiptCommandConvertor extends BaseCommandMapStruct<KingDeeExpressReceiptCommand, KingDeeExpressReceiptDto> {
    @Mapping(target = "fid", source = "fid")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "FQzokOrgId.FNumber", source = "FQzokOrgid")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_CRKDH", source = "FQzokCrkdh")
    @Mapping(target = "f_QZOK_KDH", source = "FQzokKdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "f_QZOK_QSSJ", source = "FQzokQssj")
    @Mapping(target = "f_QZOK_WLGS", source = "FQzokWlgs")
    @Mapping(target = "f_QZOK_WLBM.FNumber", source = "FQzokWlbm")
    @Mapping(target = "f_QZOK_XLH", source = "FQzokXlh")
    @Mapping(target = "f_QZOK_PCH", source = "FQzokPch")
    @Mapping(target = "f_QZOK_FHSL", source = "FQzokFhsl")
    @Mapping(target = "f_QZOK_SJR", source = "FQzokSjr")
    @Mapping(target = "f_QZOK_DH", source = "FQzokDh")
    @Mapping(target = "f_QZOK_DZ", source = "FQzokDz")
    @Mapping(target = "f_QZOK_BDDJBH", source = "FQzokBddjbh")
    @Mapping(target = "f_QZOK_SFSC", source = "FQzokSfsc")
    @Mapping(target = "f_QZOK_SFJRCB", source = "FQzokSfjrcb")
    @Mapping(target = "f_QZOK_SFZP", source = "FQzokSfzp")
    KingDeeExpressReceiptCommand toCommand(KingDeeExpressReceiptDto dto);

}
