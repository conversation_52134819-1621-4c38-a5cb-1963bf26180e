package com.vedeng.erp.trader.mapper;
import java.util.Date;

import com.vedeng.erp.trader.domain.dto.BankMatchConfigGroupDto;
import com.vedeng.erp.trader.domain.entity.BankMatchConfigGroupEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BankMatchConfigGroupMapper {
    /**
     * delete by primary key
     * @param groupId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long groupId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(BankMatchConfigGroupEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(BankMatchConfigGroupEntity record);

    /**
     * select by primary key
     * @param groupId primary key
     * @return object by primary key
     */
    BankMatchConfigGroupEntity selectByPrimaryKey(Long groupId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BankMatchConfigGroupEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BankMatchConfigGroupEntity record);

    int updateBatch(List<BankMatchConfigGroupEntity> list);

    int updateBatchSelective(List<BankMatchConfigGroupEntity> list);

    int batchInsert(@Param("list") List<BankMatchConfigGroupEntity> list);

    List<BankMatchConfigGroupEntity> findAll();


    int deleteByGroupId(@Param("groupId")Long groupId);

    List<BankMatchConfigGroupDto> findByAll(BankMatchConfigGroupDto bankMatchConfigGroupDto);


    List<BankMatchConfigGroupEntity> findAllByGroupName(@Param("groupName")String groupName);






}
