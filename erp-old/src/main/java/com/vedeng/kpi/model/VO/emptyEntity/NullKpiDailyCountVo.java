package com.vedeng.kpi.model.VO.emptyEntity;

import com.vedeng.kpi.model.VO.KpiDailyCountVo;

import java.math.BigDecimal;

public class NullKpiDailyCountVo extends KpiDailyCountVo {

    public NullKpiDailyCountVo(){
        this.setAmountSort(-1);
        this.setKpiAmount(BigDecimal.ZERO);
        this.setKpiAmountProgress(BigDecimal.ZERO);
        this.setKpiAmountScore(BigDecimal.ZERO);
        this.setBdCustomerScore(BigDecimal.ZERO);
        this.setBdCustomerSort(-1);
        this.setBdNewCustomerNum(0);
        this.setBdLostCustomerNum(0);
        this.setCustomerScore(BigDecimal.ZERO);
        this.setCustomerSort(-1);
        this.setCoCustomerNum(0);
        this.setLostCustomerNum(0);
        this.setChanceFailNum(0);
        this.setChanceSuccessNum(0);
        this.setChanceTransProportion(BigDecimal.ZERO);
        this.setChanceScore(BigDecimal.ZERO);
        this.setTotalScore(BigDecimal.ZERO);
        this.setTotalSort(-99);
    }
}
