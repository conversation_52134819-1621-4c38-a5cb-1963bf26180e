package com.vedeng.logistics.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class SyncExpressDto implements Serializable {
    private Integer expressId;

    private Integer companyId;

    private Integer logisticsId;

    private String logisticsNo;

    private Long deliveryTime;

    /**
     * 批次编码
     */
    private String batchNo;

    private Integer creator;

    private Integer updater;

    private String buyorderNo;//这单对应的采购订单号

    private List<SyncExpressDetailDto> expressDetailDtoList;


}
