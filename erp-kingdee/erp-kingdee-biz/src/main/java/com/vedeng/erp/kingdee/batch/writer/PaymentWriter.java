package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;
import com.vedeng.erp.kingdee.service.impl.KingDeePayBillServiceImpl;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName PaymentWriter.java
 * @Description TODO
 * @createTime 2023年06月02日 11:20:00
 */
@Slf4j
@Service
public class PaymentWriter extends BaseWriter<KingDeePayBillDto> {

    @Autowired
    private KingDeePayBillServiceImpl kingDeePayBillService;

    @Override
    public void doWrite(KingDeePayBillDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("银行流水推送金蝶信息{},", JSON.toJSONString(item));
        item.setKingDeeBizEnums(KingDeeBizEnums.savePayBill);
        kingDeePayBillService.register(item,true);
    }
}
