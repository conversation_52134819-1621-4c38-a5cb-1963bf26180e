package com.vedeng.common.constant;

import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.order.model.pi.CustomerLog;
import com.vedeng.order.model.pi.SaleorderLog;
import com.vedeng.order.model.pi.StockLog;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <b>Description:</b><br> 常量
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> dbcenter
 * <br><b>PackageName:</b> com.vedeng.common.constants
 * <br><b>ClassName:</b> Constants
 * <br><b>Date:</b> 2017年5月18日 下午12:58:22
 */
public class Constants {
    /**
     * @Fields ZERO : TODO 0
     */
    public final static Integer ZERO = 0;

    /**
     * @Fields ONE : TODO 1
     */
    public final static Integer ONE = 1;

    public final static Integer BD_ORDER = 1;

    /**
     * @Fields TWO : TODO 2
     */
    public final static Integer TWO = 2;

    /**
     * @Fields THREE : TODO 3
     */
    public final static Integer THREE = 3;

    /**
     * @Fields THREE : TODO 3
     */
    public final static Integer FOUR = 4;

    /**
     * @Fields THREE : TODO 3
     */
    public final static Integer FIVE = 5;

    public final static Integer HC_ORDER = 5;

    /**
     * 可以校验通过httpRequest传递过来的参数异常
     */
    public final static Integer NEGATIVE_ONE = -1;

    public final static Map<Integer, List<Express>> EXPRESS_MAP = new HashMap<>();//带快递缓存数据

    public final static Map<Integer, List<WarehouseGoodsOperateLog>> WLOG_MAP = new HashMap<>();//带出入库日志缓存数据

    public final static Map<Integer, List<SaleorderLog>> SLOG_MAP = new HashMap<>();//带销售信息缓存数据

    public final static Map<Integer, List<StockLog>> STOCK_MAP = new HashMap<>();//库存信息缓存数据

    public final static Map<Integer, List<CustomerLog>> CLOG_MAP = new HashMap<>();//带渠道商信息缓存数据

    /**
     * 未删除状态
     */
    public final static Integer IS_DELETE_0 = 0;

    /**
     * 删除状态
     */
    public final static Integer IS_DELETE_1 = 1;

    public static final Integer PRINT_ORDER = 101;//出库单

    /**
     * 商机未被合并
     */
    public static final Integer UNMERGE_STATUS = 0;
    /**
     * 商机被合并
     */
    public static final Integer BE_MERGED_STATUS = 1;
    /**
     * 商机合并其他
     */
    public static final Integer NEW_MERGED_STATUS = 2;

    /**
     * 商机类型总机询价
     */
    public static final Integer CHANCE_TYPE_CENTER = 391;
    /**
     * 商机类型自主询价
     */
    public static final Integer CHANCE_TYPE_SELF = 394;

    /**
     * 是赠品（前台传参标识）
     */
    public static final String IS_GIFT = "Y";

    /**
     * 不是赠品（前台传参标识）
     */
    public static final String NOT_GIFT = "N";

}
