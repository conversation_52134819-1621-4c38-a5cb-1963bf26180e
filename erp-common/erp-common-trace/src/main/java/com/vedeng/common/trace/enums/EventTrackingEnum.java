package com.vedeng.common.trace.enums;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * 枚举（档案ID）
 * @ClassName:  EventTrackingEnum
 * @author: <PERSON><PERSON>yang
 * @date:   2024年6月3日 下午3:03:33
 * @see <a href="http://192.168.1.179:20001/#/archived_qa/rule_qa">展示配置</a>
 * @Copyright:
 */
public enum EventTrackingEnum {

	//===============基础信息========================
	//新建客户
    BASE_INFO_NEW_CUSTOMER(101,"基础信息",101_100_002L,"新建客户", Arrays.asList("username","number"),"traderId",1),
    //新建客户(前台推送)
    BASE_INFO_NEW_CUSTOMER_FROM_FRONT(101,"基础信息",101_100_003L,"新建客户(前台)", null,"traderId",1),
    //客户划拨-【有】原始销售
    BASE_INFO_CUSTOMER_CHANGE_BY_OLD(101,"基础信息",101_100_004L,"客户划拨(单个、批量、导入)【有】原始销售",Arrays.asList("username","number","oldUserName","oldUserNumber","newUserName","newUserNumber"),"traderId",1),
    //客户划拨-【无】原始销售
	BASE_INFO_CUSTOMER_CHANGE_BY_NEW(101,"基础信息",101_100_005L,"客户划拨(单个、批量、导入)【无】原始销售",Arrays.asList("username","number","newUserName","newUserNumber"),"traderId",1),
	//客户cpm标签变化
	BASE_INFO_CUSTOMER_CHANGE_CPM(101,"基础信息",101_100_006L,"CPM标签变化",Arrays.asList("username","number"),"traderId",1),
	//落入公海
	BASE_INFO_CUSTOMER_LOST_PUBLIC(101,"基础信息",101_100_007L,"落入公海",Arrays.asList("oldUserName","oldUserNumber"),"traderId",1),
	//从公海解锁释放
	BASE_INFO_CUSTOMER_RELEASE(101,"基础信息",101_100_008L,"客户解锁",Arrays.asList("username","number"),"traderId",1),
	//金牌会员的签约解约
	BASE_INFO_CUSTOMER_LEVEL(101,"基础信息",101_100_009L,"金牌会员的签约解约",null,"traderId",1),
	// 公海划拨
	BASE_INFO_PUBLIC_CUSTOMER_CHANGE_BY_OLD(101,"基础信息",101_100_029L,"客户划拨（公海原归属销售）",Arrays.asList("username","number","oldUserName","oldUserNumber","newUserName","newUserNumber"),"traderId",1),

	//===============售前========================
	//新建线索
	PRE_SALE_NEW_BUSINESS_LEADS(102,"售前",102_100_010L,"新建线索",Arrays.asList("username","number","leadsNo","belonger","belongerNumber"),"traderId",1),
	//线索转商机
	PRE_SALE_BUSINESS_LEADS_TO_CHANCE_LEADS(102,"售前",102_100_011L,"线索转商机",Arrays.asList("username","number","bussinessChanceNo","belonger","belongerNumber"),"traderId",1),
	//新建商机
	PRE_SALE_NEW_BUSINESS_CHANCE(102,"售前",102_100_012L,"新建商机",Arrays.asList("username","number","bussinessChanceNo"),"traderId",1),
	//新建线索(前台询价)
	PRE_SALE_NEW_BUSINESS_LEADS_FRONT(102,"售前",102_100_013L,"新建线索(前台询价)",Arrays.asList("leadsNo","belonger","belongerNumber"),"traderId",1),
	//创建拜访
	PRE_SALE_NEW_VISITE(102,"售前",102_100_014L,"创建拜访",Arrays.asList("visitorName","visitorNumer","planVisitDateStr","visitTargetNames"),"traderId",1),
	//完成拜访
	PRE_SALE_COMPLETE_VISITE(102,"售前",102_100_015L,"拜访",Arrays.asList("visitorName","visitorNumer","actualVisitDateStr","visitTargetNames","contactInfo"),"traderId",1),
	//===============沟通========================
	//客户沟通
	COMMUNICATE_NEW_RECORD(103,"沟通",103_100_016L,"客户沟通",Arrays.asList("username","number","callType"),"traderId",1),
	//===============售中========================
	//客户报价
	SALE_NEW_QUOTATION(104,"售中",104_100_017L,"客户报价",Arrays.asList("username","number","quotationNo"),"traderId",1),
	//授权书申请
	SALE_APPLY_AUTHORIZATION(104,"售中",104_100_018L,"授权书申请",Arrays.asList("username","number","authorizationNo"),"traderId",1),
	//创建订单(后台)
	SALE_CREATE_ORDER_BACK(104,"售中",104_100_019L,"创建订单(后台)",Arrays.asList("username","number","orderSource","orderNo"),"traderId",1),
	//创建订单(前台)
	SALE_CREATE_ORDER_FRONT(104,"售中",104_100_020L,"创建订单(前台)",Arrays.asList("orderNo"),"traderId",1),
	//创建订单（历史数据同步）
	SALE_CREATE_ORDER_HISTORY_SYNC(104,"售中",104_100_028L,"创建订单（历史数据同步）",Arrays.asList("orderNo"),"traderId",1),
	//订单发货
	SALE_ORDER_DELIVER(104,"售中",104_100_021L,"订单发货",Arrays.asList("orderNo","logisticsNo"),"traderId",1),
	//订单开票
	SALE_ORDER_INVOICE(104,"售中",104_100_022L,"订单开票",Arrays.asList("orderNo"),"traderId",1),
	//===============售后========================
	//创建售后（后台）
	AFTER_SALE_ORDER_CREATE_BACK(105,"售后",105_100_023L,"创建售后（后台）",Arrays.asList("username","number","orderNo","afterSaleTypeName"),"traderId",1),
	//创建售后（前台）
	AFTER_SALE_ORDER_CREATE_FRONT(105,"售后",105_100_024L,"创建售后（前台）",Arrays.asList("orderNo","afterSaleTypeName"),"traderId",1),
	//售后关闭(后台)
	AFTER_SALE_ORDER_CLOSE_BACK(105,"售后",105_100_025L,"售后关闭(后台)",Arrays.asList("username","number","orderNo","afterSaleTypeName"),"traderId",1),
	//售后完结
	AFTER_SALE_ORDER_COMPLETE(105,"售后",105_100_026L,"售后完结",Arrays.asList("username","number","orderNo","afterSaleTypeName"),"traderId",1),
	//售后关闭(前台)
	AFTER_SALE_ORDER_CLOSE_FRONT(105,"售后",105_100_027L,"售后关闭(前台)",Arrays.asList("orderNo","afterSaleTypeName"),"traderId",1),;
	
    /**类型ID(http://192.168.1.179:20001/)*/
    private Integer categoryId;

    /**类型名称*/
    private String categoryName;

    /**档案ID*/
    private Long archivedId;

    /**档案名称*/
    private String archivedName;

    /**档案要记录的参数*/
    private List<String> params;

    /**档案的主键（必填字段）*/
    private String primaryKey;
    
    /**是否展示【1：是；0：否】*/
    private Integer isShow;

    EventTrackingEnum(Integer categoryId,String categoryName, Long archivedId, String archivedName,List<String> params,String primaryKey,Integer isShow) {
        this.categoryId = categoryId;
        this.categoryName = categoryName;
        this.archivedId = archivedId;
        this.archivedName = archivedName;
        this.params = params;
        this.primaryKey = primaryKey;
        this.isShow = isShow;
    }
    
    /**
     * 根据类型ID，获取这个类型下可展示的档案ID
     * @param categoryId
     * @return 可展示的档案ID集合
     */
	public static List<Long> getShowArchivedIdList(Integer categoryId){
    	List<Long> archiveIdList = new ArrayList<>();
    	for (EventTrackingEnum enumValue : EventTrackingEnum.values()) {
			if(enumValue.getCategoryId().equals(categoryId) && enumValue.getIsShow().equals(1)) {
				archiveIdList.add(enumValue.getArchivedId());
			}
		}
    	return archiveIdList;
    }

	public static List<ArchiveCategory> getCategoryNameList(){
		List<ArchiveCategory> eventCategoryList = new ArrayList<>();
		ArchiveCategory eventCategoryAll = new ArchiveCategory();
		eventCategoryAll.setCategoryName("全部");
		eventCategoryAll.setCategoryId(0);
		eventCategoryList.add(eventCategoryAll);
		for (EventTrackingEnum enumValue : EventTrackingEnum.values()) {
			ArchiveCategory eventCategory = new ArchiveCategory();
			eventCategory.setCategoryId(enumValue.getCategoryId());
			eventCategory.setCategoryName(enumValue.getCategoryName());
			if(!eventCategoryList.contains(eventCategory) ) {
				eventCategoryList.add(eventCategory);
			}
		}
		eventCategoryList.sort(Comparator.comparingInt(ArchiveCategory::getCategoryId));
		return eventCategoryList;
	}

    public Integer getCategoryId() {
        return categoryId;
    }

	public String getCategoryName() {
		return categoryName;
	}

    public Long getArchivedId() {
        return archivedId;
    }

    public String getArchivedName() {
        return archivedName;
    }

    public List<String> getParams() {
        return params;
    }

    public String getPrimaryKey() {
        return primaryKey;
    }

	public Integer getIsShow() {
		return isShow;
	}

}
