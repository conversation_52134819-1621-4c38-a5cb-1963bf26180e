package com.vedeng.logistics.service;

import com.vedeng.aftersales.model.AfterSaleBuyorderDirectOutLog;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.authorization.model.User;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WarehouseGoodsOutIn;
import com.vedeng.logistics.model.WarehouseGoodsOutInItem;
import com.vedeng.logistics.model.outIn.OutInDetail;
import com.vedeng.order.model.Buyorder;
import com.vedeng.orderstream.aftersales.model.AfterSalesDirectInfo;

import java.util.List;

public interface WarehouseGoodsOutService {
    WarehouseGoodsOutIn insertWarehouseGoodsOutIN(WarehouseGoodsOutIn warehouseGoodsOutIn);

    void saveDirectOutInLog(User user, List<AfterSaleBuyorderDirectOutLog> afterSaleBuyorderDirectOutLog);

    WarehouseGoodsOutInItem insertWarehouseGoodsOutInItem(WarehouseGoodsOperateLog inlog, WarehouseGoodsOutIn warehouseGoodsOutIn);

    void createWarehouseGoodsOutReport(WarehouseGoodsOutIn warehouse,List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList);

    /**
     * 根据skuId获取商品相关信息
     * @param skuInfo
     * @param skuId
     */
    void querySkuInfo(OutInDetail skuInfo, Integer skuId);

    void insertSaleorderDirectOut(User session_user, Buyorder buyorder, String id_sendN_sendedN_sumN, Express express);

    /**
     * 直发销售换货出库记录
     * @param afterSalesGoods
     * @param afterSalesDirectInfo
     */
    void saveAfterSalesDirectOutInLog(AfterSalesGoods afterSalesGoods, AfterSalesDirectInfo afterSalesDirectInfo, User user);

    /**
     * 判断sn码是否存在，在出库单中
     *
     * @param serialNumber sn码
     * @param warehouseGoodsOutInType 出入库类型
     * @return 是否存在
     */
    boolean isExistsSnInWarehouseGoodsOut(String serialNumber,List<Integer> warehouseGoodsOutInType);

    /**
     * 重新生成出库复核单
     * @param id
     */
    void regenerateWarehouseOutReport(Long id);

    /**
     * 判断是否满足销售出库安调拆行条件
     * @param realateId
     * @param operateType
     * @param inlog
     * @return
     */
    Boolean haveInstallationBreakLine(int realateId, int operateType, WarehouseGoodsOperateLog inlog);
    /**
     * 判断是否满足销售换货出入库安调拆行条件
     * @param realateId
     * @param operateType
     * @param inlog
     * @return
     */
    Boolean haveInstallationExchangeBreakLine(int realateId, int operateType, WarehouseGoodsOperateLog inlog);
}
