package com.vedeng.mobile.system.mapper;

import com.vedeng.mobile.system.model.VisitRecordLog;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/6
 */
public interface VisitRecordLogMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(VisitRecordLog record);

    int insertSelective(VisitRecordLog record);

    VisitRecordLog selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(VisitRecordLog record);

    int updateByPrimaryKey(VisitRecordLog record);
}