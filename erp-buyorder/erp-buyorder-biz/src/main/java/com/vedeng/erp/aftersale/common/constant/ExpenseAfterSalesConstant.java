package com.vedeng.erp.aftersale.common.constant;

/**
 * 采购费用售后单 常量类
 * <AUTHOR>
 */
public class ExpenseAfterSalesConstant {

    /**
     * 售后类型 退货
     */
    public static final Integer EXPENSE_RETURN_GOODS = 4121;

    /**
     * 售后类型 退票
     */
    public static final Integer EXPENSE_RETURN_INVOICE = 4122;

    /**
     * 开票类型 费用单
     */
    public static final Integer INVOICE_TYPE_EXPENSE = 4126;

    /**
     * 销售联动退货
     */
    public static final Integer EXPENSE_AFTER_SALES_REASON_SALES = 4284;

    /**
     * 常量 -1
     */
    public static final Integer NEGATIVE_ONE = -1;

    /**
     * 常量 0
     */
    public static final Integer ZERO = 0;

    /**
     * 常量 1
     */
    public static final Integer ONE = 1;

    /**
     * 常量 2
     */
    public static final Integer TWO = 2;


    /**
     * 退至公司账户 1
     */
    public static final Integer COMPANY_ACCOUNT = 1;

    /**
     * 退至供应商余额 2
     */
    public static final Integer COMPANY_BALANCE = 2;

    /**
     * 采购费用售后单状态 已完结
     */
    public static final Integer AFTER_SALES_STATUS_COMPLETED = 2;

    /**
     * 采购费用售后单状态 已关闭
     */
    public static final Integer AFTER_SALES_STATUS_CLOSED = 3;

    /**
     * 默认发票类型
     */
    public static final String INVOICE_TYPE_STR = "13%增值税专用发票";

    /**
     * 税率
     */
    public static final String INVOICE_TYPE_RATIO = "0.13";

    /**
     * 默认发票
     */
    public static final Integer INVOICE_TYPE = 972;

    /**
     * 退票备注
     */
    public static final String VALID_COMMENTS = "费用退票场景退票";

    /**
     * 无退票
     */
    public static final Integer NO_REFUND = 0;

    /**
     * 未退票
     */
    public static final Integer HAVE_NOT_REFUND = 1;

    /**
     * 部分退票
     */
    public static final Integer PART_REFUND = 2;

    /**
     * 全部退票
     */
    public static final Integer ALL_REFUND = 3;
}
