package com.vedeng.erp.system.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 自定义筛选器
 */
@Getter
@Setter
public class CommonSearchEntity  {

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date addTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date modTime;

    /**
     * 创建者id
     */
    private Integer creator;

    /**
     * 修改者id
     */
    private Integer updater;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 修改者名称
     */
    private String updaterName;
    /**
    * 主键
    */
    private Long id;

    /**
    * 用户ID
    */
    private Integer userId;

    /**
     * 自定义筛选器名称
     */
    private String searchName;

    /**
    * 枚举值类型：01线索池 02商机库
    */
    private String searchFromEnum;

    /**
    * 枚举值类型：LIST 自定义筛选 QUERY 查询条件 RESULT查询结果
    */
    private String searchType;

    /**
    * 前端搜索条件
    */
    private String searchContent;
}