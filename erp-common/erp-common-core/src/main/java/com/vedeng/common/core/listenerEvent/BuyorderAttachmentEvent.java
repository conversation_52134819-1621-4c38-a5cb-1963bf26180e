package com.vedeng.common.core.listenerEvent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 采购单附件事件
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BuyorderAttachmentEvent {
    /**
     * 采购单ID
     */
    private Integer buyorderId;
    
    /**
     * 采购单编号
     */
    private String buyorderNo;
    
    /**
     * 附件类型
     */
    private Integer attachmentType;
    
    /**
     * 附件功能类型
     */
    private Integer attachmentFunction;
    
    /**
     * 附件名称
     */
    private String fileName;
    
    /**
     * 附件路径
     */
    private String filePath;
    
    /**
     * 附件域名
     */
    private String domain;
    
    /**
     * 创建人ID
     */
    private Integer creator;
    
    /**
     * 创建时间
     */
    private Long addTime;

    private String ossResourceId;
}
