package com.newtask.ddi.dao.generate;

import com.wms.model.ddi.DdiGoodsStockExtDto;

public interface DdiGoodsStockMapper {
    int deleteByPrimaryKey(Integer tDdiGoodsStockId);

    int insert(DdiGoodsStockExtDto record);

    int insertSelective(DdiGoodsStockExtDto record);

    DdiGoodsStockExtDto selectByPrimaryKey(Integer tDdiGoodsStockId);

    int updateByPrimaryKeySelective(DdiGoodsStockExtDto record);

    int updateByPrimaryKey(DdiGoodsStockExtDto record);
}