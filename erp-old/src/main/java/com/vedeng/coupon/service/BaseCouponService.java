package com.vedeng.coupon.service;

import com.vedeng.market.api.dto.request.QueryCouponCenterRequest;
import com.vedeng.market.api.dto.request.QuerySkusCouponRequest;
import com.vedeng.market.api.dto.request.SubCouponSkusRequest;
import com.vedeng.market.api.dto.request.UpdateCouponRequest;
import com.vedeng.market.api.dto.response.couponcenter.QueryCouponCenterResponse;
import com.vedeng.market.api.dto.response.mycoupon.MyCouponDto;
import com.vedeng.market.api.dto.response.mycoupon.MyCouponSkuResponse;
import com.vedeng.market.api.dto.response.mycoupon.MyCouponSkuSubResponse;

/**
 * 基本的优惠券接口
 */
public interface BaseCouponService {

    public MyCouponSkuResponse findOrderCouponBySkuNosAndTraderId(SubCouponSkusRequest subCouponSkusRequest);

    public MyCouponDto getCouponByTraderId(QueryCouponCenterRequest queryCouponCenterRequest);

    MyCouponSkuSubResponse queryCouponPrice(SubCouponSkusRequest subCouponSkusRequest) throws Exception;

    QueryCouponCenterResponse cutCouponByTraderId(UpdateCouponRequest subCouponSkusRequest);
}
