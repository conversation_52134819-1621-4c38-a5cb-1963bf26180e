package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.enums.OtherTypeConst;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWmsInputOrderDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWmsInputOrderGoodsDto;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWmsInputOrderDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeInventoryProfitDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeInventoryProfitDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 盘盈入库单-21-22年历史数据
 * @date 2023/05/26 13:55
 */
@Service
@Slf4j
public class BatchInventoryProfitHistoryProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeInventoryProfitDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private BatchWmsInputOrderDtoMapper  batchWmsInputOrderDtoMapper;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;


    @Override
    public KingDeeInventoryProfitDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        // 主表
        KingDeeInventoryProfitDto dto = new KingDeeInventoryProfitDto();
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        //判断是否是21-22其他类型出入库
        if (!OtherTypeConst.UPDATE_REMARK_ORTHER_TYPE.equals(batchWarehouseGoodsOutInDto.getUpdateRemark())){
            return null;
        }
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(dto);
        if(old){
            log.info("盘盈入库单,数据已存在:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }

        log.info("盘盈入库单,BatchWarehouseOutInProcessorService.process:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
        List<BatchWarehouseGoodsOutInItemDto> inItemDtos = batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo());
        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(inItemDtos);
        Map<String, List<BatchWarehouseGoodsOutInItemDto>> itemsMap = inItemDtos.stream()
                .collect(Collectors.groupingBy(p -> p.getGoodsId() + "_" + p.getBarcodeFactory() + "_" + p.getBatchNumber()));
        dto.setFId("0");
        dto.setFQzokBddjtId(batchWarehouseGoodsOutInDto.getWarehouseGoodsOutInId().toString());
        dto.setFDate(DateUtil.formatDateTime(batchWarehouseGoodsOutInDto.getOutInTime()));

        // 详细
        List<KingDeeInventoryProfitDetailDto> detailList = new ArrayList<>();
        if (CollUtil.isEmpty(batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos())) {
            return null;
        }
        for (Map.Entry<String, List<BatchWarehouseGoodsOutInItemDto>> entry : itemsMap.entrySet()) {
            KingDeeInventoryProfitDetailDto detailDto = new KingDeeInventoryProfitDetailDto();
            BatchWarehouseGoodsOutInItemDto d = CollUtil.getFirst(entry.getValue());
            BigDecimal sumNum = entry.getValue().stream()
                    .map(x->x.getNum().abs())
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            detailDto.setFMaterialId("V"+d.getGoodsId());
            detailDto.setFBaseGainQty(sumNum.toString());
            detailDto.setFPrice("0");
            detailDto.setFAmount("0");
            detailDto.setFQzokYsddh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokGsywdh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokYwlx("盘盈单");
            detailDto.setFQzokPch(d.getVedengBatchNumber());
            detailDto.setFQzokXlh(d.getBarcodeFactory());
            detailList.add(detailDto);
        }
        dto.setFBillEntry(detailList);
        return dto;
    }


}
