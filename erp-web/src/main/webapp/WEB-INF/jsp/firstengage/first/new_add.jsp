<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script src="/webjars/ezadmin/plugins/cascader/xm-select.js"></script>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>
        <c:if test="${null ne firstEngage.firstEngageId }">
            编辑首营信息
        </c:if>
        <c:if test="${null eq firstEngage.firstEngageId }">
            新增首营信息
        </c:if>
    </title>
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/lib/tag.css">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/pages/firstengage/first/add.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/select2.css?rnd=${resourceVersionKey}"/>
    <style>
        .J-select-title {
            height: 30px;
        }

        .upload-file-wrap {
            margin-top: 10px;
            display: flex;
            align-items: center;
        }

        .tip-wrap {
            display: inline-block !important;
            position: relative !important;
            margin-left: 5px;
            top: auto !important;
            right: auto !important;
        }

        .tip-wrap .icon-info2 {
            color: #f63;
            cursor: pointer;
        }

        .tip-wrap .icon-info2.icon-grey {
            color: #999;
        }

        .tip-wrap .tip {
            top: -7px;
            left: 24px;
            display: none;
        }

        .tip-wrap .tip-con {
            white-space: nowrap;
        }

        .tip-wrap:hover .tip {
            display: block;
        }

        .vd-upload-wrap .vd-upload-list {
            flex-wrap: wrap;
        }
        .detail-table{
            border:1px solid #EDF0F2;
        }
        .detail-table-remove{
            float: right;
            font-size: 14px;
            padding: 10px 10px;
        }
        .div-check-factory{
            display: inline-block;
            line-height: 18px;
            float: left;
            padding-top: 12px;
            margin-bottom: 6px;
            padding-right: 10px;
            text-align: right;

        }
        .feedback-block{
            display: inline-block;
        }
        .div-check-factory .feedback-block{
            margin-left: 130px;
            position: absolute;
            line-height: 10px;
        }
    </style>
</head>

<body>
<form action="${pageContext.request.contextPath}/firstengage/baseinfo/addFirstEngageInfo.do" id="form_submit"
      class="J-form" method="POST">
    <input type="hidden" name="firstEngageId" id="firstEngageId" value="${firstEngage.firstEngageId }">
    <input type="hidden" name="formToken" value="${formToken}"/>
    <div class="form-wrap">
        <div class="form-container base-form form-span-8">
            <c:if test="${null ne firstEngage.firstEngageId }">
                <div class="form-title">编辑注册证信息</div>
            </c:if>
            <c:if test="${null eq firstEngage.firstEngageId }">
                <div class="form-title">新增注册证信息</div>
            </c:if>
            <!-- 后台报错的区域 -->
            <c:forEach var="error" items="${firstEngage.errors}" varStatus="status">
                <div class="vd-tip tip-red">
                    <i class="vd-tip-icon vd-icon icon-error2"></i>
                    <div class="vd-tip-cnt">${error}</div>
                </div>
            </c:forEach>

            <div class="form-block" id="re">
                <div class="form-block-title">注册证/备案信息</div>
                <div class="form-cnt J-block-1">
                    <div class="form-item">
                        <div class="form-label">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                            <span class="must">*</span>注册证号/备案凭证号：
                        </div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input type="text" name="registration.registrationNumber" autocomplete="off"
                                       placeholder="请输入注册证号/备案凭证号" class="input-text J-suggest-input" valid-max="128"
                                       value="${firstEngage.registration.registrationNumber }">
                                <input type="hidden" name="registration.registrationNumberId"
                                       value="${firstEngage.registration.registrationNumberId }">
                                <input type="hidden" id="preId">
                            </div>
                            <input type="button" class="btn btn-blue btn-small margin-left-20" onclick="syncTyInfo()"
                                   value="贝登天眼数据同步">
                            <div class="form-col">
                                <span class="disappear" style="color: red" id="tip"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">
                            <span class="must">*</span>注册证/备案凭证类别：
                        </div>
                        <div class="form-fields">
                            <div class="input-radio">
                                <label class="input-wrap">
                                    <input type="radio" name="registration.category"
                                           <c:if test="${0 eq firstEngage.registration.category}">checked</c:if>
                                           value="0">
                                    <span class="input-ctnr"></span>国产注册证
                                </label>
                                <label class="input-wrap">
                                    <input type="radio" name="registration.category"
                                           <c:if test="${2 eq firstEngage.registration.category}">checked</c:if>
                                           value="2">
                                    <span class="input-ctnr"></span>进口注册证
                                </label>
                                <label class="input-wrap">
                                    <input type="radio" name="registration.category"
                                           <c:if test="${1 eq firstEngage.registration.category}">checked</c:if>
                                           value="1">
                                    <span class="input-ctnr"></span>国产备案
                                </label>
                                <label class="input-wrap">
                                    <input type="radio" name="registration.category"
                                           <c:if test="${3 eq firstEngage.registration.category}">checked</c:if>
                                           value="3">
                                    <span class="input-ctnr"></span>进口备案
                                </label>
                            </div>
                            <div class="feedback-block" wrapfor="registration.category"></div>
                        </div>
                    </div>


                    <div class="form-item">
                        <div class="form-label">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                            <span class="must">*</span>注册人/备案人名称：
                        </div>
                        <div class="form-fields">
                            <div class="form-col sa col-6">
                                <input type="text" name="registration.productCompany.productCompanyChineseName"
                                       class="input-text" valid-max="128" placeholder="请输入"
                                       value="${firstEngage.registration.productCompany.productCompanyChineseName }">
                                <input type="hidden" name="registration.productCompany.productCompanyId"
                                       class="input-text">
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">注册人/备案人住所：</div>
                        <div class="form-fields">
                            <div class="form-col sa col-6">
                                <input type="text" name="registration.productCompany.productCompanyAddress"
                                       valid-max="256" class="input-text" placeholder="请输入"
                                       value="${firstEngage.registration.productCompany.productCompanyAddress }">
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">生产地址：</div>
                        <div class="form-fields">
                            <div class="form-col sa col-6">
                                <input type="text" name="registration.productionAddress" valid-max="256"
                                       placeholder="请输入"
                                       class="input-text" value="${firstEngage.registration.productionAddress }">
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">代理人名称：</div>
                        <div class="form-fields">
                            <div class="form-col sa col-6">
                                <input type="text" name="registration.registeredAgent" valid-max="256" placeholder="请输入"
                                       class="input-text" value="${firstEngage.registration.registeredAgent }">
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">代理人住所：</div>
                        <div class="form-fields">
                            <div class="form-col sa col-6">
                                <input type="text" name="registration.registeredAgentAddress" valid-max="256"
                                       placeholder="请输入"
                                       class="input-text" value="${firstEngage.registration.registeredAgentAddress }">
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                            <span class="must">*</span>产品名称（注册证/备案凭证）：
                        </div>
                        <div class="form-fields">
                            <div class="form-col sa col-6">
                                <input type="text" name="registration.productChineseName" valid-max="256"
                                       placeholder="请输入注册证/备案凭证上的产品名称" class="input-text"
                                       value="${firstEngage.registration.productChineseName }">
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                            <span class="must">*</span>管理类别：
                        </div>
                        <div class="form-fields">
                            <div class="input-radio">
                                <c:set var="list" value="${sysOptionMap['1090']}"/>
                                <c:forEach var="systemOption" items="${list }">
                                    <label class="input-wrap">
                                        <c:if test="${systemOption.sysOptionDefinitionId eq firstEngage.registration.manageCategoryLevel }">
                                            <input type="radio" name="registration.manageCategoryLevel" checked
                                                   value="${systemOption.sysOptionDefinitionId }">
                                        </c:if>
                                        <c:if test="${systemOption.sysOptionDefinitionId ne firstEngage.registration.manageCategoryLevel }">
                                            <input type="radio" name="registration.manageCategoryLevel"
                                                   value="${systemOption.sysOptionDefinitionId }">
                                        </c:if>
                                        <span class="input-ctnr"></span>${systemOption.title }
                                    </label>
                                </c:forEach>
                            </div>
                            <div class="feedback-block" wrapfor="registration.manageCategoryLevel"></div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                            <span class="must">*</span>规格、型号（注册证/备案凭证）：
                        </div>
                        <div class="form-fields">
                            <div class="form-col sa col-6">
                                <textarea name="registration.model" valid-max="5000"
                                          placeholder="请输入注册证/备案凭证上的规格型号，以“、”区分" class="input-textarea"
                                >${firstEngage.registration.model}</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">结构与组成/主要组成成分/产品描述：</div>
                        <div class="form-fields">
                            <div class="form-col sa col-6">
                                <textarea name="registration.proPerfStruAndComp" valid-max="1024" placeholder="请输入"
                                          class="input-textarea">${firstEngage.registration.proPerfStruAndComp }</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">适用范围/预期用途：</div>
                        <div class="form-fields">
                            <div class="form-col sa col-6">
                                <textarea name="registration.productUseRange" valid-max="1024" placeholder="请输入"
                                          class="input-textarea">${firstEngage.registration.productUseRange }</textarea>
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">附件：</div>
                        <div class="form-fields">
                            <div class="form-col sa col-6">
                                <input type="text" name="registration.attachment" valid-max="256" class="input-text"
                                       placeholder="请输入"
                                       value="${firstEngage.registration.attachment }">
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">产品存储及有效期：</div>
                        <div class="form-fields">
                            <div class="form-col sa col-6">
                                <input type="text" name="registration.storageAndExpiryDate" valid-max="256"
                                       placeholder="请输入"
                                       class="input-text"
                                       value="${firstEngage.registration.storageAndExpiryDate}">
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">其他内容：</div>
                        <div class="form-fields">
                            <div class="form-col sa col-6">
                                <textarea name="registration.otherContents" valid-max="1024" placeholder="请输入"
                                          class="input-textarea">${firstEngage.registration.otherContents }</textarea>

                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">备注：</div>
                        <div class="form-fields">
                            <div class="form-col sa col-6">
                                <textarea name="registration.remarks" valid-max="1024" placeholder="请输入"
                                          class="input-textarea">${firstEngage.registration.remarks }</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">审批部门/备案部门：</div>
                        <div class="form-fields">
                            <div class="form-col sa col-6">
                                <input type="text" name="registration.approvalDepartment" valid-max="128"
                                       placeholder="请输入"
                                       class="input-text" value="${firstEngage.registration.approvalDepartment }">
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                            <span class="must">*</span>批准日期/备案日期：
                        </div>
                        <div class="form-fields">
                            <div class="form-col span-6 input-date">
                                <input type="text" name="registration.issuingDateStr" placeholder="请选择" readonly
                                       class="input-text J-date J-date-1"
                                       value="${firstEngage.registration.issuingDateStr }">
                            </div>
                        </div>
                    </div>

                    <div class="form-item" id="exd">
                        <div class="form-label">
                            <span class="must">*</span>有效期至：
                        </div>
                        <div class="form-fields">
                            <div class="form-col span-6 input-date">
                                <input type="text" class="input-text J-date J-date-2"
                                       name="registration.effectiveDateStr" placeholder="请选择" readonly
                                       value="${firstEngage.registration.effectiveDateStr }">
                            </div>
                        </div>
                    </div>


                    <div class="form-item disappear" id="exd2">
                        <div class="form-label">
                            <span class="must">*</span>有效期至：
                        </div>
                        <div class="form-fields">
                            <div class="form-col col-6">
                                <input type="text" name="registration.effectiveDateStr" class="input-text"
                                       style="border: none" readonly
                                       value="无限期">
                            </div>
                        </div>
                    </div>


                    <div class="form-item">
                        <div class="form-label">变更情况：</div>
                        <div class="form-fields">
                            <div class="form-col sa col-6">
                                <textarea name="registration.changeContents" valid-max="1024"
                                          class="input-textarea">${firstEngage.registration.changeContents }</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="form-item J-inter-type">
                        <div class="form-label">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                            <span class="must">*</span>国标分类：
                        </div>
                        <div class="form-fields">
                            <div class="input-radio">
                                <label class="input-wrap" style="float:left">
                                    <c:if test="${firstEngage.standardCategoryType eq 2 }">
                                        <input type="radio" name="standardCategoryType" checked value='2'>
                                    </c:if>
                                    <c:if test="${firstEngage.standardCategoryType ne 2 }">
                                        <input type="radio" name="standardCategoryType" value='2'>
                                    </c:if>
                                    <span class="input-ctnr"></span>旧国标
                                </label>

                                <label class="input-wrap" style="float:left">
                                    <c:if test="${firstEngage.standardCategoryType eq 1 }">
                                        <input type="radio" name="standardCategoryType" checked value="1">
                                    </c:if>
                                    <c:if test="${firstEngage.standardCategoryType ne 1 }">
                                        <input type="radio" name="standardCategoryType" value="1">
                                    </c:if>
                                    <span class="input-ctnr"></span>新国标
                                </label>
                                <div class="J-inter-old display-inline ignore" style="display: none;float:left">
                                    <div class="J-old-wrap"></div>
                                    <input type="hidden" name="oldStandardCategoryId" class="J-old-value"
                                           value="<c:if test='${firstEngage.standardCategoryType eq 2 }'>${firstEngage.oldStandardCategoryId }</c:if>">
                                </div>
                                <div class="J-inter-new display-inline ignore" style="display: none;float:left">

                                    <div class="J-new-wrap"></div>
                                    <input type="hidden" name="newStandardCategoryId" class="J-new-value"
                                           value="<c:if test='${firstEngage.standardCategoryType eq 1 }'>${firstEngage.newStandardCategoryId }</c:if>">

                                </div>
                            </div>
                            <div class="feedback-block" wrapfor="standardCategoryType"></div>
                            <div class="feedback-block" wrapfor="newStandardCategoryId"></div>
                            <div class="feedback-block" wrapfor="oldStandardCategoryId"></div>
                        </div>
                    </div>
                </div>


                <div class="form-item" style="margin-top: 20px">
                    <div class="form-label">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                        <span class="must">*</span>注册证附件/备案凭证附件：
                    </div>
                    <div class="form-fields">
                        <div class="form-col">
                            <div class="J-upload"></div>
                            <input type="hidden" class="J-upload-data" value='${zczMapList}'>
                            <div class="upload-warn J-upload-warn">源文件质量较低，建议检查生成的图片清晰度。</div>
                            <div class="feedback-block" wrapfor="upload0"></div>
                            <div class="feedback-block J-upload-error">
                                <label class="error" for="" style="display: none;"></label>
                            </div>
                            <div class="form-fields-tip">
                                -仅支持上传JPG、JEPG、PNG三种格式文件；<br>
                                -单张宽度小于等于800，高度不限；<br>
                                -每个文件大小不大于800K；<br>
                                -若变更上传格式，请联系研发，变更WMS格式处理方式；<br>
                                -支持下方上传源文件，系统自动输出为小图并保存至附件。<br>
                            </div>
                            <div class="upload-file-wrap">
                                <div class="J-upload-file" style="display:none"></div>
                                <a class="btn btn-blue btn-small J-upload-file-btn">上传源文件并输出为小图</a>
                                <div class="tip-wrap">
                                    <i class="vd-icon icon-info2 icon-grey">
                                        <div class="tip arrow-left">
                                            <div class="tip-con">
                                                -仅支持上传pdf、jpg、jpeg、png格式文件；<br>
                                                -单个文件大小不超过10M；<br>
                                                -源文件用于生成小图，上传前请检查内容和清晰度；<br>
                                                -为保证性能，一次仅支持上传一个文件，如有多个文件请分次上传。
                                            </div>
                                            <span class="arrow arrow-out">
                                                <span class="arrow arrow-in"></span>
                                            </span>
                                        </div>
                                    </i>
                                </div>
                            </div>
                            <div class="feedback-block J-upload-file-error">
                                <label class="error" for="" style="display: none;"></label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-item" style="margin-top: 20px">
                    <div class="form-label">
                        注册证源文件/备案凭证源文件：
                    </div>
                    <input type="hidden" class="J-source-list" value='${zczyMapList}'>
                    <div class="form-fields file-source-wrap J-file-source-wrap">
                    </div>
                </div>


                <div class="form-item" style="margin-top: 20px">
                    <div class="form-label">
                        <span class="must"></span>标签样稿：
                    </div>
                    <div class="form-fields">
                        <div class="form-col">
                            <div class="J-upload-label"></div>
                            <input type="hidden" class="J-upload-data" value='${labelMapList}'>
                            <div class="upload-warn J-upload-warn">源文件质量较低，建议检查生成的图片清晰度。</div>
                            <div class="feedback-block" wrapfor="uploadLabel"></div>
                            <div class="feedback-block J-upload-error">
                                <label class="error" for="" style="display: none;"></label>
                            </div>
                            <div class="form-fields-tip">
                                -仅支持上传JPG、JEPG、PNG三种格式文件；<br>
                                -每个文件大小不大于10M； <br>
                            </div>
                            <div class="feedback-block J-upload-file-error-label">
                                <label class="error" for="" style="display: none;"></label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="ty" class="form-block col-6 disappear" style="background-color: #f3f3f3">
                <div class="form-block-title">天眼数据-药监局</div>
                <i class="vd-icon close icon-del-after" onclick="closeTy()"></i>
                <div class="form-cnt J-block-1">
                    <div class="form-item">
                        <div class="form-label">注册证号/备案凭证号：</div>
                        <div class="form-fields" id="t_no">
                        </div>
                    </div>


                    <div class="form-item">
                        <div class="form-label">注册人/备案人名称：</div>
                        <div class="form-fields">
                            <div class="form-col col-6" id="t_name">
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">注册人/备案人住所：</div>
                        <div class="form-fields">
                            <div class="form-col col-6" id="t_house">
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">生产地址：</div>
                        <div class="form-fields">
                            <div class="form-col col-6" id="t_proaddress">
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">代理人名称：</div>
                        <div class="form-fields">
                            <div class="form-col col-6" id="t_proxyName">
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">代理人住所：</div>
                        <div class="form-fields">
                            <div class="form-col col-6" id="t_proxyHouse">
                            </div>
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">产品名称：</div>
                        <div class="form-fields">
                            <div class="form-col col-6" id="t_proName">
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">管理类别：</div>
                        <div class="form-fields" id="t_ml">
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">规格、型号（注册证/备案凭证）：</div>
                        <div class="form-fields" id="t_model">
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">结构与组成/主要组成成分/产品描述：</div>
                        <div class="form-fields">
                            <div class="form-col" id="t_struct">
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">适用范围/预期用途：</div>
                        <div class="form-fields" id="t_use">
                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">附件：</div>
                        <div class="form-fields" id="t_attachment">

                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">产品存储及有效期：</div>
                        <div class="form-fields" id="t_storage">

                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">其他内容：</div>
                        <div class="form-fields" id="t_oc">

                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">备注：</div>
                        <div class="form-fields" id="t_remark">

                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">审批部门/备案部门：</div>
                        <div class="form-fields" id="t_pd">

                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label">批准日期/备案日期：</div>
                        <div class="form-fields" id="t_pt">

                        </div>
                    </div>

                    <div class="form-item" id="">
                        <div class="form-label">有效期至：</div>
                        <div class="form-fields" id="t_efd">

                        </div>
                    </div>
                    <div class="form-item">
                        <div class="form-label">变更情况：</div>
                        <div class="form-fields" id="t_change">

                        </div>
                    </div>
                </div>
            </div>

            <div style="clear: both"></div>
            <div class="form-block">
                <div class="form-block-title" style="margin-bottom: 10px;">生产企业资质</div>
                <div class="form-cnt">
                    <div class="form-item one">
                        <div class="form-label">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                            <span class="must">*</span>生产模式：
                        </div>
                        <div class="form-fields">
                            <%--<div class="input-radio">
                                <label class="input-wrap">
                                    <input type="radio" name="registration.isSubcontractProduction"
                                           <c:if test="${1 eq firstEngage.registration.isSubcontractProduction}">checked</c:if>
                                           value="1">
                                    <span class="input-ctnr"></span>是
                                </label>
                                <label class="input-wrap">
                                    <input type="radio" name="registration.isSubcontractProduction"
                                           <c:if test="${1 ne firstEngage.registration.isSubcontractProduction}">checked</c:if>
                                           value="0">
                                    <span class="input-ctnr"></span>否
                                </label>
                            </div>
                            <div class="feedback-block" wrapfor="registration.isSubcontractProduction"></div>--%>
                                <div class="div-check-factory">
                                    <label for="isSubcontractProduction">自行生产</label>
                                    <input type="checkbox" id="isSubcontractProduction" onclick="showCreateFactory(this,'manufacturer');" name="registration.isSubcontractProduction1"  >
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <label for="notSubcontractProduction">委托生产</label>
                                    <input type="checkbox" id="notSubcontractProduction" onclick="showCreateFactory(this,'manufacturerWeituo');"  name="registration.isSubcontractProduction1"  >
                                </div>
                        </div>
                    </div>

                    <div class="form-item" id="manufacturerDiv">
                        <div class="form-label">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                            <span class="must">*</span>
                            <span class="c-title" style="height: 30px" >生产企业名称：</span>
                        </div>
                        <div class="form-fields" style=" display: flex;">
                            <div id="mx-select"></div>
                            <input type="hidden" name="registration.manufacturerId" id="manufacturerId"
                                   >
                            <a class="btn btn-blue btn-small" style="margin-left: 10px;"
                               tabTitle='{"num":"addProduct","link":"./goods/manufacturer/addProduct.do","title":"新增企业", "random": "1"}'>新增生产企业</a>
                            <div  style=" position: absolute;    display: block;    float: left;    margin-top: 37px;color:#BABFC2;">
                                自行生产企业只可选择一个
                            </div>
                        </div>

                    </div>



                    <div class="manufacturerClass" id="manufacturer"></div>



                    <div class="form-item" id="manufacturerWeituoDiv" style="margin-top: 10px;margin-bottom: 20px;">
                        <div class="form-label">
                            <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                            <span class="must">*</span>
                            <span class="c-title" style="height: 30px">受委托生产的企业名称：</span>
                        </div>
                        <div class="form-fields" style=" display: flex;">
                            <div id="mx-select-weituo" style="z-index: 1;"></div>
                            <input type="hidden" name="registration.weituomanufacturerId" id="manufacturerIdWeituo"
                            >
                            <a class="btn btn-blue btn-small" style="margin-left: 10px;"
                               tabTitle='{"num":"addProduct","link":"./goods/manufacturer/addProduct.do","title":"新增企业", "random": "1"}'>新增生产企业</a>
                            <div  style=" position: absolute;    display: block;    float: left;    padding-top: 37px;color:#BABFC2;">
                                委托生产企业可以多次搜索添加多，最多10个
                            </div>
                        </div>

                    </div>
                    

                    <div class="manufacturerWeituoClass" id="manufacturerWeituo" ></div>

                </div>
                <div class="form-btn" style="margin-top: 30px">
                    <div class="form-item">
                        <div class="form-fields">
                            <button class="btn btn-blue btn-large" type="submit">保存</button>

                            <a href="javascript:void(0)" onclick="cancel()"
                               class="btn btn-large">取消</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<div class="upload-dlg-wrap J-upload-file-tip">
    <div class="dlg-cnt">
        <div class="dlg-title">输出为小图</div>

        <div class="dlg-main">
            <div class="loading-wrap J-loading">
                <div class="loading-icon"></div>
                <div class="loading-txt">系统正在为您上传源文件并自动生成小图，请耐心等待...</div>
            </div>
            <div class="error-wrap J-error">
                <div class="vd-icon icon-error1"></div>
                <div class="error-txt J-error-txt"></div>
                <div class="error-btn">
                    <a class="btn btn-blue J-upload-error-close">关闭</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/json" class="J-old-data">
        ${oldStandCategoryList}
</script>

<script type="text/json" class="J-new-data">
        ${newStandCategoryList}
</script>

<script type="text/javascript">
    function removeThis(t){
        $(t).parent().parent().remove();
    }

    // manufacturer 或 manufacturerWeituo
    function getFactoryDetail(manufacturerId,enterpriseName,divName){
        console.log(divName+"#########"+manufacturerId);
        if($("#"+divName+"_"+manufacturerId).length<1){
            // var req_url = '/goods/manufacturer/getManufacturerDetail.do?manufacturerId=' + manufacturerId + '&showCheck=0&weituo='+(divName=='manufacturerWeituo'?"Y":"");
            // var htmlObj = '<div enterpriseName="'+enterpriseName+'" manufacturerId="'+manufacturerId+'" class="detail-table '+divName+'Class" id="'+divName+'_'+manufacturerId+'"><div class="detail-table-remove"><a href="javascript:void(0);" onclick="javascript:removeThis(this)" >移除</a></div><iframe src="'+req_url+'"></iframe></div>';
            // //<div class="detail-table" id="manufacturer"></div>
            // $(htmlObj).insertBefore($("."+divName+"Class")[0]);

            $.ajax({
                url: '/goods/manufacturer/getManufacturerDetail.do?isFrame=true&manufacturerId=' + manufacturerId + '&showCheck=0&weituo='+(divName=='manufacturerWeituo'?"Y":""),
                type: 'GET',
                dataType: 'html',
                success: function (html) {
                    if (html != null) {
                        var htmlObj = '<div enterpriseName="'+enterpriseName+'" manufacturerId="'+manufacturerId+'" class="detail-table '+divName+'Class" id="'+divName+'_'+manufacturerId+'"><div class="detail-table-remove"><a href="javascript:void(0);" onclick="javascript:removeThis(this)" >移除</a></div>'+html+'</div>';
                        //<div class="detail-table" id="manufacturer"></div>
                        $(htmlObj).insertBefore($("."+divName+"Class")[0]);
                        //document.getElementById("manufacturer").innerHTML = html;
                    }
                    // 删除重复元素
                    //$('#manufacturer_inner').remove()
                }, error: function (data) {

                }
            })
        }


    }


    $(function() {
        var manufacturerList='${manufacturerList}';
        manufacturerList = (manufacturerList=="")?"[]":manufacturerList;
        // 将 JSON 字符串解析为 JavaScript 对象
        var manufacturers = JSON.parse(manufacturerList);
        if(manufacturers == undefined || manufacturers.length<1){//无生产企业数据,则将自行生产选中
            setTimeout(function () {
                $("#manufacturerWeituoDiv").hide();
                $("#isSubcontractProduction").click();
            },1000);
        }
        var initproductionMode = false;
        var initNotproductionMode = false;
        //先判断是否将按钮选中
        manufacturers.forEach(function(manufacturer) {
            var productionMode = manufacturer.productionMode;//0自行生产,1委托生产
            if(productionMode == 0){
                initproductionMode = true;
            }
            if(productionMode ==1){
                initNotproductionMode = true;
            }
        });
        //判断是否需要选中checkbox
        if(initproductionMode){
            if(!$("#isSubcontractProduction").is(':checked')){
                $("#isSubcontractProduction").click();
            }
        }else{
            $("#manufacturerDiv").hide();
        }
        //判断是否需要选中checkbox
        if(initNotproductionMode){//初始化按钮是否勾选
            if(!$("#notSubcontractProduction").is(':checked')){
                $("#notSubcontractProduction").click();
            }
        }else{
            $("#manufacturerWeituoDiv").hide();
        }
        //
        manufacturers.forEach(function(manufacturer) {
            // 取出 manufacturerId 字段
            var manufacturerId = manufacturer.manufacturerId;
            var enterpriseName = manufacturer.enterpriseName;
            var productionMode = manufacturer.productionMode;//0自行生产,1委托生产
            // 调用 sendMsg 函数
            getFactoryDetail(manufacturerId,enterpriseName,productionMode==0?"manufacturer":"manufacturerWeituo");
        });
    });

    var arrList = []
    // if (manufacturerId !== 'undefined' && manufacturerId) {
    //     arrList.push({name: manufacturerName, value: manufacturerId})
    // }
    var manufacturerId = "";

    var sel = xmSelect.render({
        el: '#mx-select',
        tips: '输入选择',
        searchTips: '请输入',
        style: {
            marginLeft: '0',
            width: '350px'
        },
        disabled: false,
        clickClose: true,
        radio: true,
        height: '450px',
        autoRow: true,
        filterable: true,
        paging: true,
        pageSize: 10,
        remoteSearch: true,
        model: {
            icon: 'hidden',
            label: {
                type: 'text'
            }
        },
        data: arrList,
        initValue: [manufacturerId],

        remoteMethod: function (val, cb, show) {
            //这里如果val为空, 则不触发搜索
            if (!val) {
                return cb([]);
            }
            $.ajax({
                type: "POST",
                url: "/firstengage/baseinfo/queryByName.do",
                data: {'name': val},
                dataType: 'json',
                success: function (data) {
                    var arr = [];
                    if (data.data.length > 0) {
                        data.data.forEach(function (item) {
                            var i = {
                                name: item.manufacturerName,
                                value: item.manufacturerId
                            };
                            arr.push(i)
                        });
                    }
                    cb(arr);
                },
                error: function (data) {
                    cb([]);
                }
            });
        },
        on: function (data) {
            if (data.arr.length > 0) {
                if($(".detail-table.manufacturerClass").length==0){
                    getFactoryDetail(data.arr[0].value,data.arr[0].name,"manufacturer");
                }else{
                    //alert("如需修改生产企业，需先移除已选企业，再重新搜索添加");
                    layer.alert("如需修改生产企业，需先移除已选企业，再重新搜索添加；");
                    console.log("生产企业已选择");
                }
                setTimeout(function () {
                    sel.setValue([]);
                },500);
            }


        }
    });

    //委托生产企业
    var sel2 = xmSelect.render({
        el: '#mx-select-weituo',
        tips: '输入选择',
        searchTips: '请输入',
        style: {
            marginLeft: '0',
            width: '350px'
        },
        disabled: false,
        clickClose: true,
        radio: true,
        height: '450px',
        autoRow: true,
        filterable: true,
        paging: true,
        pageSize: 10,
        remoteSearch: true,
        model: {
            icon: 'hidden',
            label: {
                type: 'text'
            }
        },
        data: arrList,
        initValue: [manufacturerId],

        remoteMethod: function (val, cb, show) {
            //这里如果val为空, 则不触发搜索
            if (!val) {
                return cb([]);
            }
            $.ajax({
                type: "POST",
                url: "/firstengage/baseinfo/queryByName.do",
                data: {'name': val},
                dataType: 'json',
                success: function (data) {
                    var arr = [];
                    if (data.data.length > 0) {
                        data.data.forEach(function (item) {
                            var i = {
                                name: item.manufacturerName,
                                value: item.manufacturerId
                            };
                            arr.push(i)
                        });
                    }
                    cb(arr);
                },
                error: function (data) {
                    cb([]);
                }
            });
        },
        on: function (data) {

            if (data.arr.length > 0) {
                if($(".detail-table.manufacturerWeituoClass").length>=10){
                    //alert("最多支持添加10个委托生产企业；");
                    layer.alert("最多支持添加10个委托生产企业；");

                }else{
                    //$('#manufacturerIdWeituo').val(data.arr[0].value);
                    // var manufacturerIdNew = $("#manufacturerIdWeituo").val();
                    getFactoryDetail(data.arr[0].value,data.arr[0].name,"manufacturerWeituo");
                }

                //cb([]);
                setTimeout(function () {
                    sel2.setValue([]);
                },500);
            } else {
                $('#manufacturerIdWeituo').val('');
            }

        }
    });

</script>

<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/pikaday.2.1.0.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/upload.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/lv-select.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/inputSuggest.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/dialogSearch.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/firstengage/first/new_add.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/goods/commons/goodsStorageCondition.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src='${pageContext.request.contextPath}/static/js/select2.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript"
        src='${pageContext.request.contextPath}/static/js/select2_locale_zh-CN.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js'></script>
</body>