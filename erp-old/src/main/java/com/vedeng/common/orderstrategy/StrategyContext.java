package com.vedeng.common.orderstrategy;

import com.google.common.collect.Lists;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.shiro.SpringContextHolder;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 *  策略上下文对象
 * <AUTHOR>
 * @date $
 */

public class StrategyContext {

    Logger logger = LoggerFactory.getLogger(OrderCheckedPushPCStrategy.class);

    SaleorderMapper saleorderMapper= SpringContextHolder.getBean(SaleorderMapper.class);

    OrderAmountStrategy orderAmountStrategy = SpringContextHolder.getBean(OrderAmountStrategy.class);

    final OrderCheckedPushPCStrategy orderCheckedPushPCStrategy = SpringContextHolder.getBean(OrderCheckedPushPCStrategy.class);

    /**
     *  businessType  1.金额计算   2 推送至PC
     */
    public static final Integer AMOUNT_STRATEGY = 1;
    public static final Integer PUSH_PC_STRATEGY = 2;

    private final List<OrderStrategy> list = Lists.newArrayList();

    public void add(Integer businessType,String operateType){
        if(AMOUNT_STRATEGY.equals(businessType)){
            switch (operateType){
                case OrderDataUpdateConstant.SALE_ORDER_GENERATE:
                case OrderDataUpdateConstant.SALE_ORDER_VAILD:
                case OrderDataUpdateConstant.SALE_ORDER_PAY:
                case OrderDataUpdateConstant.SALE_ORDER_AFTERODER_OPT:
                    list.add(orderAmountStrategy);
                    break;
            }
        }else if(PUSH_PC_STRATEGY.equals(businessType)){
            if (OrderDataUpdateConstant.SALE_ORDER_VAILD.equals(operateType)) {
                list.add(orderCheckedPushPCStrategy);
            }
        }
    }


    public void executeAll(Integer orderId){
        Saleorder saleorder = saleorderMapper.getSaleOrderById(orderId);
        if(saleorder==null){
            logger.info("订单不存在"+orderId);
            return;
        }
        list.forEach(item->{
            item.execute(saleorder);
        });
    }
}
