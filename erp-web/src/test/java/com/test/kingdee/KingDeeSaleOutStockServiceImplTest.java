package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.dto.result.KingDeeSaleOutStockQueryResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeStorageInQueryResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeStorageOutQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeSaleInStockService;
import com.vedeng.erp.kingdee.service.KingDeeSaleOutStockService;
import com.vedeng.erp.kingdee.service.KingDeeStorageInService;
import com.vedeng.erp.kingdee.service.KingDeeStorageOutService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeSysReportParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeSysReportResultDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class KingDeeSaleOutStockServiceImplTest {

    @Autowired
    private KingDeeSaleOutStockService kingDeeSaleOutStockServiceImplUnderTest;

    @Autowired
    private KingDeeStorageInService kingDeeStorageInService;


    @Test
    public void testGetKingDeeSaleOutStock2() {

        // Run the test
        final List<KingDeeSaleOutStockQueryResultDto> result = kingDeeSaleOutStockServiceImplUnderTest.getKingDeeSaleOutStock("2775", "5034");

        if(CollUtil.isNotEmpty(result)){
            KingDeeSaleOutStockQueryResultDto kingDeeSaleOutStockQueryResultDto = CollUtil.getFirst(result);
            KingDeeSaleOutStockDto kingDeeSaleOutStockDto = new KingDeeSaleOutStockDto();
            kingDeeSaleOutStockDto.setFid(kingDeeSaleOutStockQueryResultDto.getFID());
            kingDeeSaleOutStockDto.setF_qzok_bddjtid("2775");
            KingDeeSaleOutStockDetailDto stockDetailDto = new KingDeeSaleOutStockDetailDto();
            stockDetailDto.setFEntryId(kingDeeSaleOutStockQueryResultDto.getFEntity_FENTRYID());
            stockDetailDto.setF_QZOK_XLH("123456");
            kingDeeSaleOutStockDto.getFEntity().add(stockDetailDto);
            kingDeeSaleOutStockServiceImplUnderTest.update(kingDeeSaleOutStockDto);
        };

    }

    @Test
    public void testGetKingDeeSaleOutStock4() {

        List<KingDeeStorageInQueryResultDto> kingDeeStorageOut = kingDeeStorageInService.getKingDeeStorageIn(
                "1084",
               "1405");

        if (CollUtil.isNotEmpty(kingDeeStorageOut)) {
            KingDeeStorageInQueryResultDto kingDeeStorageInQueryResultDto = CollUtil.getFirst(kingDeeStorageOut);
            KingDeeStorageInDto kingDeeStorageInDto = new KingDeeStorageInDto();
            kingDeeStorageInDto.setFId(kingDeeStorageInQueryResultDto.getFID());
            kingDeeStorageInDto.setFBillNo("RK2302131700038");
            KingDeeStorageInDetailDto stockDetailDto = new KingDeeStorageInDetailDto();
            stockDetailDto.setFEntryId(kingDeeStorageInQueryResultDto.getFEntity_FENTRYID());
            stockDetailDto.setFQzokXlh("333333");
            kingDeeStorageInDto.getFEntity().add(stockDetailDto);
            kingDeeStorageInService.update(kingDeeStorageInDto);
        }
    }


    @Test
    public void testGetKingDeeSaleOutStock3() {
            KingDeeBaseApi kingDeeBaseApi = new KingDeeBaseApi();
        String fBillNo = "168070";
        KingDeePayBillDto data = new KingDeePayBillDto();

        KingDeeSysReportParam queryParam = new KingDeeSysReportParam();
        queryParam.setFormId(KingDeeFormConstant.RECEIPT_BILL);
        queryParam.setFieldKeys("fbillno,fbusirefeno,fdata");
        KingDeeSysReportParam.QueryModel model = new KingDeeSysReportParam.QueryModel();
        model.setFPayBillNo(fBillNo);
        queryParam.setModel(model);
        KingDeeSysReportResultDto kingDeeSysReportResultDto = kingDeeBaseApi.getSysReportData(KingDeeFormConstant.RECEIPT_BILL,queryParam,KingDeeSysReportResultDto.class);
        if (kingDeeSysReportResultDto != null) {
            if(CollUtil.isNotEmpty(kingDeeSysReportResultDto.getResult().get(0).getRows())
                    && kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).size() == 3) {
                String dzhzd = kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).get(2) == null ? null : kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).get(2).trim();
                data.setFQzokDzhzd(dzhzd);
                String lsh = kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).get(1) == null ? null : kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).get(1).trim();
                data.setFQzokLsh(lsh);
                String yhzhhm = kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).get(0) == null ? null : kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).get(0).trim();
                data.setFQzokYhzhhm(yhzhhm);
            }
        }
    }

}
