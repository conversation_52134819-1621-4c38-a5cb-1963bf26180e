package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶费用应付单明细 dto
 * @date
 * @see KingDeePayExpensesDto
 */
@Getter
@Setter
public class KingDeePayExpensesDetailDto {

    /**
     * 明细id
     */
    @WriteBackField(value = "FEntryID",backWriteByErpField = "F_QZOK_BDDJHID",backWriteToKingDeeField = "F_QZOK_BDDJHID")
    private String fEntryId;

    /**
     * 计价数量
     */
    private String FPriceQty;
    /**
     * 含税单价
     */
    private String FTaxPrice;
    /**
     * 税率%
     */
    private String FEntryTaxRate;
    /**
     * 是否计入采购成本
     */
    private Boolean FINCLUDECOST;

    /**
     * 贝登单据行ID
     */
    private String F_QZOK_BDDJHID;
    /**
     * 对应贝登的sku
     */
    private String F_QZOK_WLBM;

    /**
     * 费用项目
     */
    private String FCOSTID;

    /**
     * 费用承担部门
     */
    private String FCOSTDEPARTMENTID;

    /**
     * 归属销售单号
     */
    private String F_QZOK_GSXSDH;

}
