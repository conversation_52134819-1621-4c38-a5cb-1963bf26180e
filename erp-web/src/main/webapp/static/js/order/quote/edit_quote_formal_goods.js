$(function() {
	let componentHtml = $('#componentHtml').val();
	if (!componentHtml) {
		$('.iconbluemouth').css('display', 'none');
	}
});

function subBtn() {
	checkLogin();
	var $form = $("#quoteFormalGoodsForm");

//	$form.submit(function() {

		$form.find(":input").parents('li').find('.warning').remove();
		$form.find(":input").removeClass("errorbor");

		$("#haveInstallation").val($form.find("input[name='installation']:checked").val());
		$("#deliveryDirect").val($form.find("input[name='deliveryDirectRad']:checked").val());
		var price = $form.find("#price").val().trim();

		var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
		if(price.length>0 && !reg.test(price)){
			warnTips("priceError","报价金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
			$("#price").focus();
			return false;
		}else if(Number(price)>=10000000){
		//	warnTips("priceError","产品单价不允许超过一千万");//文本框ID和提示用语
			//return false;
		}

		var num = $("#num").val();
		if (num.length==0) {
			warnTips("num","产品数量不能为空");//文本框ID和提示用语
			return false;
		}else{
			var re = /^[0-9]+$/;
			if(num== "0" || !re.test(num)){
				warnTips("num","产品数量必须为大于0的正整数");//文本框ID和提示用语
				return false;
			}
		}
		if(Number(num)>=1000000){
			//warnTips("num","产品数量不允许超过一百万");//文本框ID和提示用语
			//return false;
		}
		if((Number(num)*Number(price)) >= 10000000){
			//warnTips($form,"num","产品总金额不允许超过一千万");//文本框ID和提示用语
			//return false;
		}

		var deliveryCycle = $form.find("#deliveryCycle").val();
		if (deliveryCycle.length!=0 && deliveryCycle.length>20) {
			warnTips("deliveryCycleErrMsg","货期长度应该在0-20个字符之间");//文本框ID和提示用语
			return false;
		}

		if($form.find("#deliveryDirect").val()=="1"){
			var deliveryDirectComments = $form.find("#deliveryDirectComments").val().trim();
			if(deliveryDirectComments.length==0){
				warnTips("deliveryDirectComments","直发原因不允许为空");
				return false;
			}
			if(deliveryDirectComments.length<2 || deliveryDirectComments.length>128){
				warnTips("deliveryDirectComments","直发原因长度应该在2-128个字符之间");
				return false;
			}
		}

		var insideComments = $form.find("#insideComments").val();
		if(insideComments.length>512){
			warnTips("insideComments","内部备注长度应该在0-512个字符之间");
			return false;
		}

		var goodsComments = $form.find("#goodsComments").val();
		if(goodsComments.length>512){
			warnTips("goodsComments","产品备注长度应该在0-512个字符之间");
			return false;
		}
		if(parent.$("#quotePayMoneForm").find("#quoteCustomerNature").val() == "465"){//客户为分销
			//终端信息
			// var terminalTraderName = "";var terminalTraderId = "";var terminalTraderType ="";var salesArea = "";var salesAreaId = "";
			// if(parent.$("#quotePayMoneForm").find("#terminalTraderName") != undefined && parent.$("#quotePayMoneForm").find("#terminalTraderName").val().length > 0){
			// 	terminalTraderName = parent.$("#quotePayMoneForm").find("#terminalTraderName").val();
			// 	terminalTraderId = parent.$("#quotePayMoneForm").find("#terminalTraderId").val();
			// 	terminalTraderType = parent.$("#quotePayMoneForm").find("#terminalTraderType").val();
			// }else{
			// 	terminalTraderName = parent.$("#updateTerminalInfo").find("#searchTraderName").val().trim();
			// }
			// var salesArea = (parent.$("#updateTerminalInfo").find("#province :selected").text()=="请选择"?"":parent.$("#updateTerminalInfo").find("#province :selected").text()) + " "
			// 	+ (parent.$("#updateTerminalInfo").find("#city :selected").text()=="请选择"?"":parent.$("#updateTerminalInfo").find("#city :selected").text()) + " "
			// 	+ (parent.$("#updateTerminalInfo").find("#zone :selected").text()=="请选择"?"":parent.$("#updateTerminalInfo").find("#zone :selected").text());
			// 	var province = parent.$("#updateTerminalInfo").find("#province :selected").val()=="0"?"":parent.$("#updateTerminalInfo").find("#province :selected").val();
			// 	var city = parent.$("#updateTerminalInfo").find("#city :selected").val();
			// 	var zone = parent.$("#updateTerminalInfo").find("#zone :selected").val();
			// $("#quoteFormalGoodsForm").find("#salesAreaId").val((zone=="0"?(city=="0"?province:city):zone));
			//
			// $("#quoteFormalGoodsForm").find("#terminalTraderName").val(terminalTraderName);
			// $("#quoteFormalGoodsForm").find("#terminalTraderId").val(terminalTraderId);
			// $("#quoteFormalGoodsForm").find("#terminalTraderType").val(terminalTraderType);
			// $("#quoteFormalGoodsForm").find("#salesArea").val(salesArea);


			var terminalTraderName = parent.$("#quotePayMoneForm").find("#terminalNameNew").val();
			var dwhTerminalId = parent.$("#quotePayMoneForm").find("#dwhTerminalId").val();
			var unifiedSocialCreditIdentifier = parent.$("#quotePayMoneForm").find("#unifiedSocialCreditIdentifier").val();
			var organizationCode = parent.$("#quotePayMoneForm").find("#organizationCode").val();

			var provinceId = parent.$("#quotePayMoneForm").find("#provinceId").val();
			var cityId = parent.$("#quotePayMoneForm").find("#cityId").val();
			var areaId = parent.$("#quotePayMoneForm").find("#areaId").val();
			var provinceName = parent.$("#quotePayMoneForm").find("#provinceName").val();
			var cityName = parent.$("#quotePayMoneForm").find("#cityName").val();
			var areaName = parent.$("#quotePayMoneForm").find("#areaName").val();

			$("#quoteFormalGoodsForm").find("#terminalTraderName").val(terminalTraderName);
			$("#quoteFormalGoodsForm").find("#dwhTerminalId").val(dwhTerminalId);
			$("#quoteFormalGoodsForm").find("#unifiedSocialCreditIdentifier").val(unifiedSocialCreditIdentifier);
			$("#quoteFormalGoodsForm").find("#organizationCode").val(organizationCode);

			$("#quoteFormalGoodsForm").find("#provinceId").val(provinceId);
			$("#quoteFormalGoodsForm").find("#cityId").val(cityId);
			$("#quoteFormalGoodsForm").find("#areaId").val(areaId);
			$("#quoteFormalGoodsForm").find("#provinceName").val(provinceName);
			$("#quoteFormalGoodsForm").find("#cityName").val(cityName);
			$("#quoteFormalGoodsForm").find("#areaName").val(areaName);
		}
		$.ajax({
			async:false,
			url:'./editQuoteGoods.do',
			data:$form.serialize(),
			type:"POST",
			dataType : "json",
			beforeSend:function(){
			},
			success:function(data){
				if(data.code==0){
					parent.$("#paymentType").val(parent.$("#paymentType").val()).change();
//					parent.location.reload();
					updateInsideComments();
					layerPFF(null);

					/*layer.alert(data.message,
							{ icon: 1 },
							function () {
								$('#cancle').click();
								if(parent.layer!=undefined){
									parent.layer.close(index);
								}
								parent.location.reload();
							}
					);*/
				}else{
					layer.alert(data.message);
				}
//				refreshNowPageList(data);
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		})
		return false;
//	});
};



function countTotalMoney(str){
	checkLogin();
	clearErroeMes();
	var $form = $("#quoteFormalGoodsForm");
	$form.find("#totalMoney").html("");

	var flag = false;

	var price = $form.find("#price").val().trim();
	var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;

	var num = $form.find("#num").val().trim();
	var re = /^[0-9]+$/;

	if(str=="price"){
		if(price.length>0 && !reg.test(price)){
			warnTips("priceError","报价金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
			$("#price").focus();
			return false;
		}else if(Number(price)>=10000000){
			//warnTips("priceError","产品单价不允许超过一千万");//文本框ID和提示用语
			//return false;
		}
		if(num.length!=0 && num!="0" && re.test(num)){
			flag = true;
		}
	}else{
		if (num.length==0) {
			warnTips("num","产品数量不允许为空");//文本框ID和提示用语
			return false;
		}else{
			if(num=="0" || !re.test(num)){
				warnTips("num","产品数量必须为正整数");//文本框ID和提示用语
				return false;
			}
		}
		if(Number(num)>=1000000){
			//warnTips("num","产品数量不允许超过一百万");//文本框ID和提示用语
			//return false;
		}
		if(price.length>0 && reg.test(price)){
			flag = true;
		}
	}
	if((Number(num)*Number(price)) >= 10000000){
		//warnTips("num","产品总金额不允许超过一千万");//文本框ID和提示用语
		//return false;
	}

	if(flag){
		if (num != undefined && num != "" && price != undefined && price != "") {
			/*var f = Number(num) * Number(price);
			var s = f.toString();
			var rs = s.indexOf('.');
			if (rs < 0) {
				rs = s.length;
				s += '.';
			}
			while (s.length <= rs + 2) {
				s += '0';
			}*/
			$form.find("#totalMoney").html((Number(num) * Number(price)).toFixed(2));
		}
	}
	return true;
}

// 内部备注重复点击 比较最后一次点击时间和当前时间
var lastClick;
function lockClick(){
	let nowClick = new Date()
	if (lastClick === undefined) {
		lastClick = nowClick
		return true
	} else {
		if (Math.round((nowClick.getTime() - lastClick.getTime())) > 2000) {
			lastClick = nowClick
			return true
		}
		else {
			lastClick = nowClick
			return false
		}
	}
}

/**
 * 内部备注组件触发事件
 * @param dom 当前dom元素
 */
function insideRemark(dom) {

	if(!lockClick()){
		return;
	}

	let labelData = $(dom).attr('label_data');
	let remark = $(dom).val();
	let hasRemark = false;
	if (remark) {
		hasRemark = true;
	}
	let skuList = [
		{
			skuId: $('#skuId').val(),
			skuNo: $('#sku').val(),
			skuName: $('#goodsName').val()
		}
	]
	if (!checkGoods(skuList, 1)) {
		return;
	}
	let relationId = $("#quoteorderId").val();
	new LabelMark({
		el: dom,
		value: labelData,
		url: page_url + '/order/remarkComponent/getInitComponent.do',
		query: {
			scene: 1,
			isAll: 1,
			remark: remark,
			hasRemark: hasRemark,
			relationId: relationId,
			skuList: skuList,
			showComponentIcon: true
		}
	});
}

/**
 * 更新产品备注标签
 */
function updateInsideComments() {
	let labelData = $("#insideComments").attr("label_data");
	// 为空，直接跳转
	if (!labelData) {
		let saleorderId = $("#quoteorderId").val();
		let sku = $("#sku").val();
		let skuName = $("#goodsName").val();
		let labelQuery = new Object();
		labelQuery.skuNo = sku;
		labelQuery.skuName = skuName;
		labelQuery.relationId = saleorderId;
		labelQuery.scene = 1;
		let dataSource = new Object();
		dataSource.labelQuery = labelQuery;
		labelData = JSON.stringify(dataSource);
		console.log(labelData);
	}
	$.ajax({
		async:false,
		url:page_url+'/order/saleorder/updateInsideComments.do',
		data: labelData,
		type:"POST",
		dataType : "json",
		contentType: "application/json",
		success:function(data){
			if (data.code == 0) {
				layerPFF(window.parent.location.href);
				$("#close-layer").click();
			} else {
				layer.alert(data.message,{ icon: 2 });
			}

		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	})
}

function showDeliveryCycle(obj, index) {
	let directDeliveryTimeStart = $("#directDeliveryTimeStart").val();
	let directDeliveryTimeEnd = $("#directDeliveryTimeEnd").val();
	let commonDeliveryTimeStart = $("#commonDeliveryTimeStart").val();
	let commonDeliveryTimeEnd = $("#commonDeliveryTimeEnd").val();
	if (index == 0) {
		if ($(obj).is(":checked")) {
			if (commonDeliveryTimeStart != '' && commonDeliveryTimeEnd != '') {
				if (commonDeliveryTimeStart == commonDeliveryTimeEnd) {
					$("#deliveryCycleFlag").text("参考：近90天平均货期" + commonDeliveryTimeStart + "天")
				} else {
					$("#deliveryCycleFlag").text("参考：近90天平均货期" + commonDeliveryTimeStart + "-" + commonDeliveryTimeEnd + "天")
				}
			} else {
				$("#deliveryCycleFlag").text("近90天无成交，货期请咨询相关人员")
			}
		}
	} else {
		if ($(obj).is(":checked")) {
			if (directDeliveryTimeStart != '' && directDeliveryTimeEnd != '') {
				if (directDeliveryTimeStart == directDeliveryTimeEnd) {
					$("#deliveryCycleFlag").text("参考：近90天供应链允诺货期" + directDeliveryTimeStart + " 天")
				} else {
					$("#deliveryCycleFlag").text("参考：近90天供应链允诺货期" + directDeliveryTimeStart + "-" + directDeliveryTimeEnd + " 天")
				}
			} else {
				$("#deliveryCycleFlag").text("近90天无成交，货期请咨询相关人员")
			}
		}
	}
}
