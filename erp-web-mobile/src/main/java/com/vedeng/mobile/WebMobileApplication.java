package com.vedeng.mobile;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

/**
 * 启动类
 *
 * <AUTHOR>
 */
@ComponentScan(basePackages = {"com.vedeng.**", "com.common.**"},

        excludeFilters = {
                //销售
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.controller..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.manager..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.rebbitmq..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.task..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.strategy..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.buzlogic..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.service..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder..*ServiceImpl"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.service.api..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.common..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.web..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.api.*"),


                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.confirmrecord..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.market..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.quote..*"),

                // 快递
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.express..*"),

                // 采购
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.activiti..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.buyorder..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.buyorderexpense..*"),

                // 商机
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.business..*"),

                // 线索
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.leads..*"),

                // 客户
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.trader..*ServiceImpl"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.trader.service.api..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.trader.common..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.trader.web..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.trader.facade.impl.*"),

                // 产品服务
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.goods.web.api..*"),

                // 售后
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.aftersale..*"),

                // 一些不规范写法的包，不要扫描
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.common.rabbitmq..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.meinian..*")
        }
)

@MapperScan(basePackages = {"com.vedeng.**.dao", "com.vedeng.**.mapper"})
@SpringBootApplication
@EnableApolloConfig({"application", "vedeng-sso-client", "ezadmin"})
public class WebMobileApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(WebMobileApplication.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(WebMobileApplication.class);
    }

}