package com.vedeng.erp.trader.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.trader.domain.dto.TraderTagDto;
import com.vedeng.erp.trader.domain.entity.TraderTagEntity;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description dto转entity
 * @date 2022/7/12 10:45
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TraderTagConvertor extends BaseMapStruct<TraderTagEntity, TraderTagDto> {
}
