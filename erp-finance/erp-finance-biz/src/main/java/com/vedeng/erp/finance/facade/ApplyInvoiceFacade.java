package com.vedeng.erp.finance.facade;

import com.vedeng.erp.finance.dto.CheckInvoiceApplyResponseDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.trader.dto.TraderFinanceDto;

public interface ApplyInvoiceFacade {

    CheckInvoiceApplyResponseDto checkApplyInvoice(InvoiceApplyDto invoiceApplyDto);

    CheckInvoiceApplyResponseDto checkApplyInvoiceAfter(Integer afterSalesId);

    TraderFinanceDto getCustomerTraderFinance(Integer traderId);

    /**
     * 根据销售订单id查询是否需要显示开票按钮
     * @param saleOrderId 销售订单id
     */
    void showButtonApplyInvoice(Integer saleOrderId);


    InvoiceCheckResultDto checkCheckRule(Integer saleOrderId);
}
