package com.vedeng.goods.model.vo;

import lombok.Data;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class GoodsTodoSummaryVo {

    private List<GoodsLevelTodoVo> goodsLevelTodoList = new LinkedList<>();

    private List<GoodsPositionTodoVo> goodsPositionTodoList = new LinkedList<>();


    public void addGoodsLevelTodoVo(GoodsLevelTodoVo goodsLevelTodoVo) {
        goodsLevelTodoList.add(goodsLevelTodoVo);
    }


    public void addGoodsPositionTodoVo(GoodsPositionTodoVo goodsPositionTodoVo) {
        goodsPositionTodoList.add(goodsPositionTodoVo);
    }

    public List<GoodsLevelTodoVo> getGoodsLevelTodoList() {
        return goodsLevelTodoList;
    }

    public void setGoodsLevelTodoList(List<GoodsLevelTodoVo> goodsLevelTodoList) {
        this.goodsLevelTodoList = goodsLevelTodoList;
    }

    public List<GoodsPositionTodoVo> getGoodsPositionTodoList() {
        return goodsPositionTodoList;
    }

    public void setGoodsPositionTodoList(List<GoodsPositionTodoVo> goodsPositionTodoList) {
        this.goodsPositionTodoList = goodsPositionTodoList;
    }

    @Data
    public static class GoodsLevelTodoVo {

        private Integer goodsLevelNo;

        private String goodsLevelName;

        private String uniqueIdentifier;

        private Integer totalCount;

        private Integer todoCount;
    }

    @Data
    public static class GoodsPositionTodoVo {

        private Integer goodsPositionNo;

        private String goodsPositionName;

        private String showName;

        private Integer totalCount;

        private Integer levelACount;

        private Integer levelBCount;

        private Integer levelCCount;

        private Integer levelDCount;

        private Integer levelECount;
    }

}
