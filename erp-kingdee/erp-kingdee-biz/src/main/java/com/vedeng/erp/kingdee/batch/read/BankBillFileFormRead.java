package com.vedeng.erp.kingdee.batch.read;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.reader.BaseItemReader;
import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveBillEntity;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeReceiveBillMapper;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
public class BankBillFileFormRead extends BaseItemReader<KingDeeReceiveBillEntity> {

    private Integer index = 0;

    @Resource
    private KingDeeReceiveBillMapper kingDeeReceiveBillMapper;

    @Override
    public KingDeeReceiveBillEntity read() throws Exception{
        List<KingDeeReceiveBillEntity> erpBankBillIdList = kingDeeReceiveBillMapper.queryErpBankBillIdByType(ErpConstant.ZERO);
        if (CollUtil.isEmpty(erpBankBillIdList)) {
            return null;
        }
        if (index >= erpBankBillIdList.size()) {
            index = 0;
            return null;
        }
        return erpBankBillIdList.get(index++);
    }
}
