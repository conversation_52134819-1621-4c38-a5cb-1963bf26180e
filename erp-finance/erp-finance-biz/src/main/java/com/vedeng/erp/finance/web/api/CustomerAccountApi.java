package com.vedeng.erp.finance.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.finance.facade.CustomerAccountFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@ExceptionController
@RestController
@Slf4j
@RequestMapping("/customer/account")
public class CustomerAccountApi{

    @Autowired
    private CustomerAccountFacade customerAccountFacade;

    /**
     * 重新计算银行
     * @param afterSalesId
     * @return
     */
    @RequestMapping(value = "/refresh")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<Boolean> refresh(@RequestParam Integer afterSalesId){
        customerAccountFacade.tryCreateCustomerAccount(afterSalesId);
        return R.success(true);
    }
}
