package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.domain.entity.DwhTraderTagChangeErpEntity;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 大数据客户标签更新记录表,表服务接口
 *
 * <AUTHOR>
 * @since 2023-07-12 15:44:48
 */
public interface DwhTraderTagChangeErpService {
    // 插入数据
    int insert(DwhTraderTagChangeErpEntity record);
    //根据操作时间查询
    List<DwhTraderTagChangeErpEntity> selectByOperateTime(LocalDateTime operateTimeBefore, LocalDateTime operateTimeAfter);
}
