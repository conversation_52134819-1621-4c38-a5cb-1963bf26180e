<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.trader.dao.TraderCreditMapper">

    <resultMap id="TraderCreditViewResultMap" type="com.vedeng.trader.model.dto.CustomerBillPeriodItemDto">
        <result column="BILL_PERIOD_ID" property="billPeriodId" jdbcType="BIGINT"/>
        <result column="BILL_PERIOD_USE_DETAIL_ID" property="billPeriodUseDetailId" jdbcType="BIGINT"/>
        <result column="RELATED_ID" property="orderId" jdbcType="BIGINT"/>
        <result column="CUSTOMER_ID" property="customerId" jdbcType="BIGINT"/>
        <result column="UNRETURNED_AMOUNT" property="unReturnedAmount" jdbcType="BIGINT"/>
        <result column="CREDIT_USE_AMOUNT" property="creditUseAmount" jdbcType="INTEGER"/>
        <result column="BILL_PERIOD_END" property="billPeriodEnd" jdbcType="BIGINT"/>
        <result column="BILL_PERIOD_TYPE" property="billPeriodType" jdbcType="INTEGER"/>
        <result column="CREDIT_TOTAL_AMOUNT" property="creditTotalAmount" jdbcType="DECIMAL"/>
        <result column="OVERDUE_AMOUNT" property="overDueAmount" jdbcType="DECIMAL"/>
        <result column="COUNT_OF_OVERDUE" property="countOfOverDue" jdbcType="INTEGER"/>
        <result column="DAYS_OF_OVERDUE" property="daysOfOverdue" jdbcType="INTEGER"/>
        <result column="RETURNED_AMOUNT" property="returnedAmount" jdbcType="DECIMAL"/>
        <result column="OVER_STATUS" property="overdueState" jdbcType="INTEGER"/>
        <result column="OCCUPY_AMOUNT" property="occupyAmount" jdbcType="DECIMAL"/>
        <result column="FREEZE_AMOUNT" property="freezeAmount" jdbcType="DECIMAL"/>
    </resultMap>

    <resultMap id="CustomerBillPeriodApplyResultMap" type="com.vedeng.trader.model.dto.CustomerBillPeriodApplyItemDto">
        <result column="BILL_PERIOD_APPLY_ID" property="billPeriodApplyId" jdbcType="BIGINT"/>
        <result column="BILL_PERIOD_ID" property="billPeriodId" jdbcType="BIGINT"/>
        <result column="CUSTOMER_ID" property="customerId" jdbcType="BIGINT"/>
        <result column="CREATOR" property="creator" jdbcType="INTEGER"/>
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT"/>
        <result column="BILL_PERIOD_TYPE" property="billPeriodType" jdbcType="INTEGER"/>
        <result column="OPERATE_TYPE" property="operateType" jdbcType="INTEGER"/>
        <result column="BEFORE_APPLY_AMOUNT" property="beforeApplyAmount" jdbcType="DECIMAL"/>
        <result column="COUNT_OF_OVERDUE" property="countOfOverDue" jdbcType="INTEGER"/>
        <result column="OVERDUE_AMOUNT" property="overDueAmount" jdbcType="DECIMAL"/>
        <result column="BEFORE_BILL_PERIOD_START" property="beforeBillPeriodStart" jdbcType="BIGINT"/>
        <result column="BEFORE_BILL_PERIOD_END" property="beforeBillPeriodEnd" jdbcType="BIGINT"/>
        <result column="APPLY_AMOUNT" property="applyAmount" jdbcType="DECIMAL"/>
        <result column="SETTLEMENT_PERIOD" property="settlementPeriod" jdbcType="INTEGER"/>
        <result column="BILL_PERIOD_START" property="billPeriodStart" jdbcType="BIGINT"/>
        <result column="BILL_PERIOD_END" property="billPeriodEnd" jdbcType="BIGINT"/>
        <result column="CHECK_STATUS" property="checkStatus" jdbcType="INTEGER"/>
        <result column="SETTLEMENT_TYPE" property="settlementType" jdbcType="INTEGER"/>
    </resultMap>

    <select id="getTraderCreditViewListPage" resultMap="TraderCreditViewResultMap">
        SELECT
            T1.BILL_PERIOD_ID,
            T1.BILL_PERIOD_USE_DETAIL_ID,
            T1.CUSTOMER_ID,
            T1.RELATED_ID,
            T1.UNRETURNED_AMOUNT,
            T1.AMOUNT CREDIT_USE_AMOUNT,
            T2.BILL_PERIOD_END,
            T2.BILL_PERIOD_TYPE,
            T2.APPLY_AMOUNT AS CREDIT_TOTAL_AMOUNT,
            SUM(
            IFNULL( IF ( T3.OVERDUE_DAYS > 0, T3.UNRETURNED_AMOUNT, 0 ), 0 ) ) as unReturnedOverDueAmount,
            SUM( T3.OVERDUE_AMOUNT ) OVERDUE_AMOUNT_FLAG,
            IF
            ( T1.OCCUPANCY = 0 AND T1.UNRETURNED_AMOUNT = 0, 1, 0 ) IS_CLOSED_ORDER,
            SUM(
            IF
            ( T3.OVERDUE_DAYS > 0, 1, 0 )) COUNT_OF_OVERDUE,
            SUM( IFNULL( T3.OVERDUE_DAYS, 0 ) ) DAYS_OF_OVERDUE,
            T1.AMOUNT - T1.UNRETURNED_AMOUNT RETURNED_AMOUNT,
            CASE
            WHEN SUM( T3.OVERDUE_AMOUNT ) = 0 THEN
            2
            WHEN SUM( T3.OVERDUE_AMOUNT ) > 0
            AND SUM( T3.OVERDUE_AMOUNT ) <![CDATA[<]]> T1.AMOUNT THEN
            3
            WHEN SUM( T3.OVERDUE_AMOUNT ) = T1.AMOUNT THEN
            4 ELSE 1
            END OVER_STATUS,
            CASE
            T1.OCCUPANCY
            WHEN 1 THEN
            T1.UNRETURNED_AMOUNT ELSE 0
            END OCCUPY_AMOUNT,
            CASE
            T1.OCCUPANCY
            WHEN 0 THEN
            T1.UNRETURNED_AMOUNT ELSE 0
            END FREEZE_AMOUNT
        FROM
        T_CUSTOMER_BILL_PERIOD_USE_DETAIL T1
        LEFT JOIN T_CUSTOMER_BILL_PERIOD T2 ON T1.BILL_PERIOD_ID = T2.BILL_PERIOD_ID
        AND T2.COMPANY_ID = 1
        LEFT JOIN T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL T3 ON T1.BILL_PERIOD_USE_DETAIL_ID =
        T3.BILL_PERIOD_USE_DETAIL_ID
        AND T3.COMPANY_ID = 1
        WHERE
        T1.USE_TYPE = 1
        AND T1.COMPANY_ID = 1
        <if test="orderIds != null and orderIds.size > 0">
            AND T1.RELATED_ID IN
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="billPeriodType != null and billPeriodType != 0">
            AND T2.BILL_PERIOD_TYPE = #{billPeriodType,jdbcType=INTEGER}
        </if>
        <if test="isReturn != null and isReturn != 0">
            <choose>
                <when test="isReturn == 1">
                    AND T1.UNRETURNED_AMOUNT = 0
                </when>
                <when test="isReturn == 2">
                    AND T1.UNRETURNED_AMOUNT > 0
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        GROUP BY
        T2.BILL_PERIOD_TYPE,
        T1.RELATED_ID
        HAVING
          IS_CLOSED_ORDER = 0
        <if test="overdueState != NULL and overdueState != 0">
            <choose>
                <when test="overdueState == 1">
                    AND OVERDUE_AMOUNT_FLAG IS NULL
                </when>
                <when test="overdueState == 2">
                    AND OVERDUE_AMOUNT_FLAG = 0
                </when>
                <when test="overdueState == 3">
                    AND OVERDUE_AMOUNT_FLAG > 0
                    AND OVERDUE_AMOUNT_FLAG <![CDATA[<]]> CREDIT_USE_AMOUNT
                </when>
                <when test="overdueState == 4">
                    AND OVERDUE_AMOUNT_FLAG = CREDIT_USE_AMOUNT
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="getCreditUsableAmountByPeriodIds" resultType="com.vedeng.trader.model.dto.CustomerBillPeriodUsableAmountDto">
       SELECT
            T1.BILL_PERIOD_ID,
            ( T1.APPLY_AMOUNT - IFNULL( sum( T2.AMOUNT ), 0 ) ) CREDIT_USABLE_AMOUNT
        FROM
            T_CUSTOMER_BILL_PERIOD T1
            LEFT JOIN T_CUSTOMER_BILL_PERIOD_USE_DETAIL T2 ON T1.BILL_PERIOD_ID = T2.BILL_PERIOD_ID
        WHERE
            T1.BILL_PERIOD_ID IN
            <foreach collection="periodIds" item="periodId" open="(" close=")" separator=",">
                #{periodId,jdbcType=BIGINT}
            </foreach>
        GROUP BY
            T1.BILL_PERIOD_ID
    </select>

    <select id="getRiskAmountByDetailIds"
            resultType="com.vedeng.trader.model.dto.CustomerBillPeriodRiskAmountDto">
        SELECT
            BILL_PERIOD_USE_DETAIL_ID,
            SUM( IFNULL( AMOUNT, 0 ) ) RISK_AMOUNT
        FROM
            T_CUSTOMER_BILL_PERIOD_RISK_MANAGEMENT_DETAIL
        WHERE
            TYPE IN (1,3)
            AND BILL_PERIOD_USE_DETAIL_ID IN
            <foreach collection="detailIds" item="detailId" open="(" close=")" separator=",">
                #{detailId,jdbcType=BIGINT}
        </foreach>
        GROUP BY
            BILL_PERIOD_USE_DETAIL_ID
    </select>

    <select id="getSuperViseAmountByDetailIds" resultType="com.vedeng.trader.model.dto.CustomerBillPeriodSuperViseAmountDto">
        SELECT
            BILL_PERIOD_USE_DETAIL_ID,
            SUM( IFNULL( AMOUNT, 0 ) ) SUPERVISE_AMOUNT
        FROM
          T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL
        WHERE
            TYPE IN ( 1, 2, 3, 4)
            AND BILL_PERIOD_USE_DETAIL_ID IN
            <foreach collection="detailIds" item="detailId" open="(" close=")" separator=",">
                #{detailId,jdbcType=BIGINT}
            </foreach>
        GROUP BY
          BILL_PERIOD_USE_DETAIL_ID
    </select>

    <select id="getCustomerBillPeriodApplyListPage" resultMap="CustomerBillPeriodApplyResultMap">
      SELECT
            T1.BILL_PERIOD_APPLY_ID,
            T1.BILL_PERIOD_ID,
            T1.CUSTOMER_ID,
            T1.CREATOR,
            T1.ADD_TIME,
            T1.BILL_PERIOD_TYPE,
            T1.OPERATE_TYPE,
            T1.BEFORE_APPLY_AMOUNT,
            SUM( IF ( T3.OVERDUE_AMOUNT IS NOT NULL, 1, 0 ) ) COUNT_OF_OVERDUE,
            /*SUM( IFNULL( T3.OVERDUE_AMOUNT, 0 ) ) OVERDUE_AMOUNT,*/
            SUM( IFNULL( IF ( T3.OVERDUE_DAYS > 0, T3.UNRETURNED_AMOUNT, 0 ), 0 ) ) as unReturnedOverDueAmount,
            T1.BEFORE_BILL_PERIOD_START,
            T1.BEFORE_BILL_PERIOD_END,
            T1.APPLY_AMOUNT,
            T1.SETTLEMENT_PERIOD,
            T1.BILL_PERIOD_START,
            T1.BILL_PERIOD_END,
            T1.CHECK_STATUS,
            T2.SETTLEMENT_TYPE
        FROM
            T_CUSTOMER_BILL_PERIOD_APPLY T1
            LEFT JOIN T_CUSTOMER_BILL_PERIOD_USE_DETAIL T2 ON T1.BILL_PERIOD_ID = T2.BILL_PERIOD_ID
            AND T2.USE_TYPE = 1
            AND T2.COMPANY_ID = 1
            LEFT JOIN T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL T3 ON T2.BILL_PERIOD_USE_DETAIL_ID = T3.BILL_PERIOD_USE_DETAIL_ID
            AND T3.OVERDUE_AMOUNT > 0
            AND T3.COMPANY_ID = 1
        WHERE
            T1.COMPANY_ID = 1
        <if test="customerIdList != null and customerIdList.size > 0">
            AND T1.CUSTOMER_ID IN
            <foreach collection="customerIdList" item="customerId" open="(" close=")" separator=",">
                #{customerId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="checkStatus != null">
            AND T1.CHECK_STATUS = #{checkStatus,jdbcType=INTEGER}
        </if>
        <if test="billPeriodStart != null">
            AND T1.ADD_TIME >= #{billPeriodStart,jdbcType=BIGINT}
        </if>
        <if test="billPeriodEnd != null">
            AND T1.ADD_TIME <![CDATA[<=]]> #{billPeriodEnd,jdbcType=BIGINT}
        </if>
        <if test="billPeriodType != null">
            AND T1.BILL_PERIOD_TYPE = #{billPeriodType,jdbcType=INTEGER}
        </if>
        <if test="customerBillPeriodApplyIdList != null and customerBillPeriodApplyIdList.size > 0">
            AND T1.BILL_PERIOD_APPLY_ID IN
            <foreach collection="customerBillPeriodApplyIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        GROUP BY
            T1.BILL_PERIOD_APPLY_ID
    </select>

    <select id="getPeriodRecordStatement" resultType="com.vedeng.trader.model.dto.PeriodRecordStatementDto">
        SELECT
            SUM( P.UNRETURNED_AMOUNT ) unReturnedAmount,
            SUM( P.AMOUNT ) periodUsedAmount,
            SUM( P.OVERDUE_AMOUNT_FLAG) unReturnedOverDueAmount
        FROM
        (
            SELECT
                T1.UNRETURNED_AMOUNT,
                T1.AMOUNT,
                T1.AMOUNT CREDIT_USE_AMOUNT,
                /*SUM( T3.OVERDUE_AMOUNT ) OVERDUE_AMOUNT_FLAG,*/
        SUM( IF ( T3.OVERDUE_DAYS > 0, T3.UNRETURNED_AMOUNT, 0 )) OVERDUE_AMOUNT_FLAG,
                IF
                ( T1.OCCUPANCY = 0 AND T1.UNRETURNED_AMOUNT = 0, 1, 0 ) IS_CLOSED_ORDER
            FROM
            T_CUSTOMER_BILL_PERIOD_USE_DETAIL T1
            LEFT JOIN T_CUSTOMER_BILL_PERIOD T2 ON T1.BILL_PERIOD_ID = T2.BILL_PERIOD_ID
            AND T2.COMPANY_ID = 1
            LEFT JOIN T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL T3 ON T1.BILL_PERIOD_USE_DETAIL_ID = T3.BILL_PERIOD_USE_DETAIL_ID
            AND T3.COMPANY_ID = 1
            WHERE
            T1.USE_TYPE = 1
            AND T1.COMPANY_ID = 1
            <if test="orderIds != null and orderIds.size > 0">
                AND T1.RELATED_ID IN
                <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                    #{orderId,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="billPeriodType != null and billPeriodType != 0">
                AND T2.BILL_PERIOD_TYPE = #{billPeriodType,jdbcType=INTEGER}
            </if>
            <if test="isReturn != null and isReturn != 0">
                <choose>
                    <when test="isReturn == 1">
                        AND T1.UNRETURNED_AMOUNT = 0
                    </when>
                    <when test="isReturn == 2">
                        AND T1.UNRETURNED_AMOUNT > 0
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
            GROUP BY
            T1.BILL_PERIOD_USE_DETAIL_ID
            HAVING
            IS_CLOSED_ORDER = 0
            <if test="overdueState != NULL and overdueState != 0">
                <choose>
                    <when test="overdueState == 1">
                        AND OVERDUE_AMOUNT_FLAG IS NULL
                    </when>
                    <when test="overdueState == 2">
                        AND OVERDUE_AMOUNT_FLAG = 0
                    </when>
                    <when test="overdueState == 3">
                        AND OVERDUE_AMOUNT_FLAG > 0
                        AND OVERDUE_AMOUNT_FLAG <![CDATA[<]]> CREDIT_USE_AMOUNT
                    </when>
                    <when test="overdueState == 4">
                        AND OVERDUE_AMOUNT_FLAG = CREDIT_USE_AMOUNT
                    </when>
                    <otherwise>
                    </otherwise>
                </choose>
            </if>
        ) P
    </select>

    <select id="getPeriodOrderStatement" resultType="com.vedeng.trader.model.dto.PeriodOrderStatementDto">
        SELECT
            COUNT( P.SALEORDER_ID ) orderNum,
            SUM( P.TOTAL_AMOUNT ) totalAmount
        FROM
            (
        SELECT
            T4.SALEORDER_ID,
            T4.TOTAL_AMOUNT,
            T1.AMOUNT CREDIT_USE_AMOUNT,
            /*SUM( T3.OVERDUE_AMOUNT ) OVERDUE_AMOUNT_FLAG,*/
            T3.UNRETURNED_AMOUNT  OVERDUE_AMOUNT_FLAG,
            IF
            ( T1.OCCUPANCY = 0 AND T1.UNRETURNED_AMOUNT = 0, 1, 0 ) IS_CLOSED_ORDER
        FROM
        T_CUSTOMER_BILL_PERIOD_USE_DETAIL T1
        LEFT JOIN T_CUSTOMER_BILL_PERIOD T2 ON T1.BILL_PERIOD_ID = T2.BILL_PERIOD_ID
        AND T2.COMPANY_ID = 1
        LEFT JOIN T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL T3 ON T1.BILL_PERIOD_USE_DETAIL_ID = T3.BILL_PERIOD_USE_DETAIL_ID
        AND T3.COMPANY_ID = 1
        LEFT JOIN T_SALEORDER T4 ON T1.RELATED_ID = T4.SALEORDER_ID
        AND T4.COMPANY_ID = 1
        WHERE
        T1.USE_TYPE = 1
        AND T1.COMPANY_ID = 1
        <if test="orderIds != null and orderIds.size > 0">
            AND T1.RELATED_ID IN
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="billPeriodType != null and billPeriodType != 0">
            AND T2.BILL_PERIOD_TYPE = #{billPeriodType,jdbcType=INTEGER}
        </if>
        <if test="isReturn != null and isReturn != 0">
            <choose>
                <when test="isReturn == 1">
                    AND T1.UNRETURNED_AMOUNT = 0
                </when>
                <when test="isReturn == 2">
                    AND T1.UNRETURNED_AMOUNT > 0
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
        GROUP BY
           T4.SALEORDER_ID
        HAVING
        IS_CLOSED_ORDER = 0
        <if test="overdueState != NULL and overdueState != 0">
            <choose>
                <when test="overdueState == 1">
                    AND OVERDUE_AMOUNT_FLAG IS NULL
                </when>
                <when test="overdueState == 2">
                    AND OVERDUE_AMOUNT_FLAG = 0
                </when>
                <when test="overdueState == 3">
                    AND OVERDUE_AMOUNT_FLAG > 0
                    AND OVERDUE_AMOUNT_FLAG <![CDATA[<]]> CREDIT_USE_AMOUNT
                </when>
                <when test="overdueState == 4">
                    AND OVERDUE_AMOUNT_FLAG = CREDIT_USE_AMOUNT
                </when>
                <otherwise>
                </otherwise>
            </choose>
        </if>
            ) P
    </select>
    <select id="getCustomerBIllPeriodApplyByCreator" resultType="java.lang.Long">
        SELECT
            A.CUSTOMER_ID
        FROM
            T_CUSTOMER_BILL_PERIOD_APPLY A
        WHERE A.CREATOR = #{creatorId} AND A.CHECK_STATUS=#{checkStatus}
    </select>
</mapper>