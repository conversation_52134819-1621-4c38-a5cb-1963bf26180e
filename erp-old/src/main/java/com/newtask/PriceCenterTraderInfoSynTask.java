package com.newtask;

import com.pricecenter.dto.ContractTraderDto;
import com.pricecenter.dto.CusomerInfoPageDto;
import com.pricecenter.dto.PageResultDto;
import com.pricecenter.service.ContractPriceService;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.trader.dao.TraderMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 价格中心客户信息同步接口 一天触发一次
 *
 */
@JobHandler(value = "priceCenterTraderInfoSynTask")
@Component
public class PriceCenterTraderInfoSynTask extends AbstractJobHandler {

    Logger logger = LoggerFactory.getLogger(PriceCenterTraderInfoSynTask.class);

    private static int PAGE_SIZE = 500;

    @Autowired
    private ContractPriceService contractPriceService;

    @Resource
    private TraderMapper traderMapper;

    //private static String FIND_CUSTOMER_INFO_PAGE = "contract_price/findCustomerInfoByPage";

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        XxlJobLogger.log("价格中心同步客户信息start-----------");
        logger.info("价格中心同步客户信息start-----------");

        PageResultDto firstPageResult =  getCurrentPageData(0);
        int totalRecords = firstPageResult.getTotalRecords();

        if(totalRecords <= 0){
            return ReturnT.SUCCESS;
        }

        int totalPage = getTotalPage(totalRecords);
        List<Integer> tradreIdList = null;

        for(int currentPage = 0; currentPage < totalPage ;currentPage++){

            if(currentPage == 0){
                tradreIdList = firstPageResult.getDatas();
            }else {
                tradreIdList = getCurrentPageData(currentPage).getDatas();
            }

            if(CollectionUtils.isEmpty(tradreIdList)){
                break;
            }

            List<ContractTraderDto> traders = this.traderMapper.findTradePlatFormByIds(tradreIdList);
            if(CollectionUtils.isEmpty(traders)){
                continue;
            }

            contractPriceService.batchUpdateTraderinfo(traders);

        }

        logger.info("价格中心同步客户信息end-----------");
        XxlJobLogger.log("价格中心同步客户信息end-----------");
        return SUCCESS;
    }

    private PageResultDto getCurrentPageData(int pageNo) {

        CusomerInfoPageDto cusomerInfoPageDto = new CusomerInfoPageDto();

        cusomerInfoPageDto.setPageNo(pageNo);
        cusomerInfoPageDto.setPageSize(PAGE_SIZE);

        return contractPriceService.findCustomerIdByPage(cusomerInfoPageDto);
    }

    private int getTotalPage(int totalRecords) {
        int pageNum = totalRecords / PAGE_SIZE;
        return (totalRecords % PAGE_SIZE) > 0 ? pageNum + 1 : pageNum;
    }
}
