package com.vedeng.erp.mobile.domain;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
    * 发票主表
    */
@Data
public class Invoice {
    private Integer invoiceId;

    /**
    * ERP公司ID(T_COMPANY)
    */
    private Integer companyId;

    /**
    * 发票代码
    */
    private String invoiceCode;

    /**
    * 电子发票流水号
    */
    private String invoiceFlow;

    /**
    * 1纸质发票2电子发票
    */
    private Boolean invoiceProperty;

    /**
    * 电子发票链接
    */
    private String invoiceHref;

    /**
    * 开票申请类型 字典库
503采购开票
504售后开票
505销售开票
    */
    private Integer type;

    /**
    * 录票/开票 1开票 2录票
    */
    private Boolean tag;

    /**
    * 关联表ID
    */
    private Integer relatedId;

    /**
    * 售后订单ID
    */
    private Integer afterSalesId;

    /**
    * 发票号码
    */
    private String invoiceNo;

    /**
    * 发票类型 
688	0%增值税专用发票
429	17%增值税专用发票
430	17%增值税普通发票
681	16%增值税普通发票
682	16%增值税专用发票
683	6%增值税普通发票
684	6%增值税专用发票
685	3%增值税普通发票
686	3%增值税专用发票
687	0%增值税普通发票
971	13%增值税普通发票
972	13%增值税专用发票
1758	1%增值税专用发票
4071	1%增值税普通发票

    */
    private Integer invoiceType;

    /**
    * 发票税率
    */
    private BigDecimal ratio;

    /**
    * 红蓝字 1红2蓝
    */
    private Boolean colorType;

    /**
    * 发票金额
    */
    private BigDecimal amount;

    /**
    * 是否有效 0否 1是
    */
    private Boolean isEnable;

    /**
    * 审核人ID
    */
    private Integer validUserid;

    /**
    * 审核 0待审核 1通过 不通过
    */
    private Boolean validStatus;

    /**
    * 最后一次审核时间
    */
    private Long validTime;

    /**
    * 审核备注
    */
    private String validComments;

    /**
    * 发票打印状态 0：未打印 1：已打印
    */
    private Boolean invoicePrintStatus;

    /**
    * 发票作废状态 0：未作废 1：已作废
    */
    private Boolean invoiceCancelStatus;

    /**
    * 快递单ID
    */
    private Integer expressId;

    /**
    * 是否认证0未认证 1已认证 2 认证失败
    */
    private Boolean isAuth;

    /**
    * 是否当月认证1是2否
    */
    private Boolean isMonthAuth;

    /**
    * 认证时间
    */
    private Long authTime;

    /**
    * 添加时间
    */
    private Long addTime;

    /**
    * 添加人
    */
    private Integer creator;

    /**
    * 最近一次编辑时间
    */
    private Long modTime;

    /**
    * 最近一次编辑人
    */
    private Integer updater;

    /**
    * 开票申请ID
    */
    private Integer invoiceApplyId;

    /**
    * 丢票寄送发票时间
    */
    private Date sendTime;

    /**
    * 售后快递单ID
    */
    private Integer afterExpressId;

    /**
    * oss文件链接
    */
    private String ossFileUrl;

    /**
    * oss文件资源ID
    */
    private String resourceId;

    /**
    * 发票来自哪儿，1：航信
    */
    private Integer invoiceFrom;

    /**
    * 关联的 T_HX_INVOICE_ID
    */
    private Integer hxInvoiceId;

    /**
    * 认证方式 0 线下认证 1 接口认证
    */
    private Boolean authMode;

    /**
    * 认证失败原因
    */
    private String authFailReason;

    /**
    * 是否正在认证 0 否 1 是
    */
    private Boolean isAuthing;

    /**
    * 红蓝字补充类型 1冲销
    */
    private Boolean colorComplementType;

    /**
    * 是否采购仅退票售后发票0否1是
    */
    private Boolean isAfterBuyorderOnly;
}