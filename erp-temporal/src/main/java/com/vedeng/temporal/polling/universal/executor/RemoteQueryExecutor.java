package com.vedeng.temporal.polling.universal.executor;

import java.util.Map;

/**
 * 远程查询执行器接口
 * 
 * 定义了执行远程API查询的标准接口，主要用于HTTP API调用。
 * 该接口专门处理网络调用相关的逻辑，包括超时、重试、错误处理等。
 * 
 * 职责范围：
 * - 执行HTTP API调用
 * - 处理网络超时和连接异常
 * - 实现请求重试机制
 * - 解析API响应格式
 * - 处理API错误码和异常响应
 * 
 * @param <T> 查询返回的数据类型
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-24
 */
public interface RemoteQueryExecutor<T> {
    
    /**
     * 执行远程查询
     * 
     * 通过网络调用远程API获取数据，需要处理各种网络异常和API异常。
     * 实现类应该包含适当的超时设置、重试机制和错误处理逻辑。
     * 
     * @param context 查询上下文，包含API配置、请求参数等信息
     * @return 查询结果数据
     * @throws RuntimeException 当网络异常、API异常或数据解析异常时抛出
     */
    T executeRemoteQuery(Map<String, Object> context) throws Exception;
    
    /**
     * 获取执行器名称
     * 用于日志记录和监控
     * 
     * @return 执行器名称
     */
    default String getExecutorName() {
        return this.getClass().getSimpleName();
    }
}