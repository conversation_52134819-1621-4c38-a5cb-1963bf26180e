package com.pricecenter.dto;

import com.vedeng.price.api.price.dto.price.SkuPriceInfoPurchaseHistoryDto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class SkuPriceChangeApplyDto implements Serializable {

    private Long id;

    private Long skuId;

    private String skuNo;

    private Integer verifyStatus;

    private String verifyStatusName;

    private Integer isDeleted;

    private String addTime;

    private String modTime;

    private String creator;

    private String updator;

    private String unitName;

    private String skuName;

    private String belongProductManager;

    private String belongProductAssit;

    private Integer disabled;

    private String disableReason;

    private Integer auditPass;
    
    /**前台商城是否可见【0：不可见；1：可见】，默认值不可见*/
    private Integer vdIsVisible;

    private Integer alreadyPriced;

    List<SkuPriceChangePurchaseInfoDto> purchaseInfoList;

    List<SkuPriceChangeSaleInfoDto> saleInfoList;

    private List<SkuPriceInfoPurchaseDetailDto> effectPurchaseList;

    private List<SkuPriceInfoPurchaseHistoryDto> purchaseInfoHistoryList;

    private List<SkuPriceInfoSaleDetailDto> effectSaleInfoList;

    private List<SkuPriceInfoSaleDetailHistoryDto> effectSaleInfoHistoryList;

    private BigDecimal marketPrice;

    private BigDecimal terminalPrice;

    private BigDecimal distributionPrice;

    private BigDecimal groupPrice;

    private String middlePrice;

    private BigDecimal lastMarketPrice;

    private BigDecimal lastTerminalPrice;

    private BigDecimal lastDistributionPrice;

    private BigDecimal lastGroupPrice;

    private boolean marketPriceChange;

    private boolean terminalPriceChange;

    private boolean distributionPriceChange;

    private boolean groupPriceChange;

    private String marketPriceChangeReason;

    private String terminalPriceChangeReason;

    private String distributionPriceChangeReason;

    private String groupPriceChangeReason;

    private BigDecimal electronicCommercePrice;

    private String electronicCommercePriceChangeReason;

    private boolean electronicCommercePriceChange;

    private BigDecimal lastElectronicCommercePrice;

    private BigDecimal researchTerminalPrice;

    private String researchTerminalChangeReason;

    private boolean researchTerminalPriceChange;

    private BigDecimal lastResearchTerminalPrice;

    /**
     * 主销售部门
     */
    private String mainDept;

    private Integer purchaseContainsFee;

    private Integer saleContainsFee;

    private String purchaseContainsFeeStr;

    private String saleContainsFeeStr;

    public String getMainDept() {
        return mainDept;
    }

    public void setMainDept(String mainDept) {
        this.mainDept = mainDept;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public String getVerifyStatusName() {
        return verifyStatusName;
    }

    public void setVerifyStatusName(String verifyStatusName) {
        this.verifyStatusName = verifyStatusName;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime;
    }

    public String getModTime() {
        return modTime;
    }

    public void setModTime(String modTime) {
        this.modTime = modTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getUpdator() {
        return updator;
    }

    public void setUpdator(String updator) {
        this.updator = updator;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getBelongProductManager() {
        return belongProductManager;
    }

    public void setBelongProductManager(String belongProductManager) {
        this.belongProductManager = belongProductManager;
    }

    public String getBelongProductAssit() {
        return belongProductAssit;
    }

    public void setBelongProductAssit(String belongProductAssit) {
        this.belongProductAssit = belongProductAssit;
    }

    public List<SkuPriceChangePurchaseInfoDto> getPurchaseInfoList() {
        return purchaseInfoList;
    }

    public void setPurchaseInfoList(List<SkuPriceChangePurchaseInfoDto> purchaseInfoList) {
        this.purchaseInfoList = purchaseInfoList;
    }

    public List<SkuPriceChangeSaleInfoDto> getSaleInfoList() {
        return saleInfoList;
    }

    public void setSaleInfoList(List<SkuPriceChangeSaleInfoDto> saleInfoList) {
        this.saleInfoList = saleInfoList;
    }

    public void setMiddlePrice(String middlePrice) {
        this.middlePrice = middlePrice;
    }

    public String getMiddlePrice() {
        return middlePrice;
    }

    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }

    public BigDecimal getTerminalPrice() {
        return terminalPrice;
    }

    public void setTerminalPrice(BigDecimal terminalPrice) {
        this.terminalPrice = terminalPrice;
    }

    public BigDecimal getDistributionPrice() {
        return distributionPrice;
    }

    public void setDistributionPrice(BigDecimal distributionPrice) {
        this.distributionPrice = distributionPrice;
    }

    public List<SkuPriceInfoPurchaseDetailDto> getEffectPurchaseList() {
        return effectPurchaseList;
    }

    public void setEffectPurchaseList(List<SkuPriceInfoPurchaseDetailDto> effectPurchaseList) {
        this.effectPurchaseList = effectPurchaseList;
    }

    public List<SkuPriceInfoSaleDetailDto> getEffectSaleInfoList() {
        return effectSaleInfoList;
    }

    public void setEffectSaleInfoList(List<SkuPriceInfoSaleDetailDto> effectSaleInfoList) {
        this.effectSaleInfoList = effectSaleInfoList;
    }


    public BigDecimal getLastMarketPrice() {
        return lastMarketPrice;
    }

    public void setLastMarketPrice(BigDecimal lastMarketPrice) {
        this.lastMarketPrice = lastMarketPrice;
    }

    public BigDecimal getLastTerminalPrice() {
        return lastTerminalPrice;
    }

    public void setLastTerminalPrice(BigDecimal lastTerminalPrice) {
        this.lastTerminalPrice = lastTerminalPrice;
    }

    public BigDecimal getLastDistributionPrice() {
        return lastDistributionPrice;
    }

    public void setLastDistributionPrice(BigDecimal lastDistributionPrice) {
        this.lastDistributionPrice = lastDistributionPrice;
    }

    public boolean isMarketPriceChange() {
        return marketPriceChange;
    }

    public void setMarketPriceChange(boolean marketPriceChange) {
        this.marketPriceChange = marketPriceChange;
    }

    public boolean isTerminalPriceChange() {
        return terminalPriceChange;
    }

    public void setTerminalPriceChange(boolean terminalPriceChange) {
        this.terminalPriceChange = terminalPriceChange;
    }

    public boolean isDistributionPriceChange() {
        return distributionPriceChange;
    }

    public void setDistributionPriceChange(boolean distributionPriceChange) {
        this.distributionPriceChange = distributionPriceChange;
    }

    public Integer getDisabled() {
        return disabled;
    }

    public void setDisabled(Integer disabled) {
        this.disabled = disabled;
    }

    public String getDisableReason() {
        return disableReason;
    }

    public void setDisableReason(String disableReason) {
        this.disableReason = disableReason;
    }

    public Integer getAlreadyPriced() {
        return alreadyPriced;
    }

    public void setAlreadyPriced(Integer alreadyPriced) {
        this.alreadyPriced = alreadyPriced;
    }

    public Integer getAuditPass() {
        return auditPass;
    }

    public void setAuditPass(Integer auditPass) {
        this.auditPass = auditPass;
    }

    public List<SkuPriceInfoSaleDetailHistoryDto> getEffectSaleInfoHistoryList() {
        return effectSaleInfoHistoryList;
    }

    public void setEffectSaleInfoHistoryList(List<SkuPriceInfoSaleDetailHistoryDto> effectSaleInfoHistoryList) {
        this.effectSaleInfoHistoryList = effectSaleInfoHistoryList;
    }

    public List<SkuPriceInfoPurchaseHistoryDto> getPurchaseInfoHistoryList() {
        return purchaseInfoHistoryList;
    }

    public void setPurchaseInfoHistoryList(List<SkuPriceInfoPurchaseHistoryDto> purchaseInfoHistoryList) {
        this.purchaseInfoHistoryList = purchaseInfoHistoryList;
    }

    public String getMarketPriceChangeReason() {
        return marketPriceChangeReason;
    }

    public void setMarketPriceChangeReason(String marketPriceChangeReason) {
        this.marketPriceChangeReason = marketPriceChangeReason;
    }

    public String getTerminalPriceChangeReason() {
        return terminalPriceChangeReason;
    }

    public void setTerminalPriceChangeReason(String terminalPriceChangeReason) {
        this.terminalPriceChangeReason = terminalPriceChangeReason;
    }

    public String getDistributionPriceChangeReason() {
        return distributionPriceChangeReason;
    }

    public void setDistributionPriceChangeReason(String distributionPriceChangeReason) {
        this.distributionPriceChangeReason = distributionPriceChangeReason;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigDecimal getGroupPrice() {
        return groupPrice;
    }

    public void setGroupPrice(BigDecimal groupPrice) {
        this.groupPrice = groupPrice;
    }

    public String getGroupPriceChangeReason() {
        return groupPriceChangeReason;
    }

    public void setGroupPriceChangeReason(String groupPriceChangeReason) {
        this.groupPriceChangeReason = groupPriceChangeReason;
    }

    public BigDecimal getLastGroupPrice() {
        return lastGroupPrice;
    }

    public void setLastGroupPrice(BigDecimal lastGroupPrice) {
        this.lastGroupPrice = lastGroupPrice;
    }

    public boolean isGroupPriceChange() {
        return groupPriceChange;
    }

    public void setGroupPriceChange(boolean groupPriceChange) {
        this.groupPriceChange = groupPriceChange;
    }

    public BigDecimal getElectronicCommercePrice() {
        return electronicCommercePrice;
    }

    public void setElectronicCommercePrice(BigDecimal electronicCommercePrice) {
        this.electronicCommercePrice = electronicCommercePrice;
    }

    public String getElectronicCommercePriceChangeReason() {
        return electronicCommercePriceChangeReason;
    }

    public void setElectronicCommercePriceChangeReason(String electronicCommercePriceChangeReason) {
        this.electronicCommercePriceChangeReason = electronicCommercePriceChangeReason;
    }

    public BigDecimal getResearchTerminalPrice() {
        return researchTerminalPrice;
    }

    public void setResearchTerminalPrice(BigDecimal researchTerminalPrice) {
        this.researchTerminalPrice = researchTerminalPrice;
    }

    public String getResearchTerminalChangeReason() {
        return researchTerminalChangeReason;
    }

    public void setResearchTerminalChangeReason(String researchTerminalChangeReason) {
        this.researchTerminalChangeReason = researchTerminalChangeReason;
    }

    public boolean isElectronicCommercePriceChange() {
        return electronicCommercePriceChange;
    }

    public void setElectronicCommercePriceChange(boolean electronicCommercePriceChange) {
        this.electronicCommercePriceChange = electronicCommercePriceChange;
    }

    public boolean isResearchTerminalPriceChange() {
        return researchTerminalPriceChange;
    }

    public void setResearchTerminalPriceChange(boolean researchTerminalPriceChange) {
        this.researchTerminalPriceChange = researchTerminalPriceChange;
    }

    public BigDecimal getLastElectronicCommercePrice() {
        return lastElectronicCommercePrice;
    }

    public void setLastElectronicCommercePrice(BigDecimal lastElectronicCommercePrice) {
        this.lastElectronicCommercePrice = lastElectronicCommercePrice;
    }

    public BigDecimal getLastResearchTerminalPrice() {
        return lastResearchTerminalPrice;
    }

    public void setLastResearchTerminalPrice(BigDecimal lastResearchTerminalPrice) {
        this.lastResearchTerminalPrice = lastResearchTerminalPrice;
    }

    public String getPurchaseContainsFeeStr() {
        return purchaseContainsFeeStr;
    }

    public void setPurchaseContainsFeeStr(String purchaseContainsFeeStr) {
        this.purchaseContainsFeeStr = purchaseContainsFeeStr;
    }

    public String getSaleContainsFeeStr() {
        return saleContainsFeeStr;
    }

    public void setSaleContainsFeeStr(String saleContainsFeeStr) {
        this.saleContainsFeeStr = saleContainsFeeStr;
    }

    public Integer getVerifyStatus() {
        return verifyStatus;
    }

    public void setVerifyStatus(Integer verifyStatus) {
        this.verifyStatus = verifyStatus;
    }

    public Integer getPurchaseContainsFee() {
        return purchaseContainsFee;
    }

    public void setPurchaseContainsFee(Integer purchaseContainsFee) {
        this.purchaseContainsFee = purchaseContainsFee;
    }

    public Integer getSaleContainsFee() {
        return saleContainsFee;
    }

    public void setSaleContainsFee(Integer saleContainsFee) {
        this.saleContainsFee = saleContainsFee;
    }

	public Integer getVdIsVisible() {
		return vdIsVisible;
	}

	public void setVdIsVisible(Integer vdIsVisible) {
		this.vdIsVisible = vdIsVisible;
	}
    
    
}
