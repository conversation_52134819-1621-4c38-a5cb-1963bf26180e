package com.vedeng.erp.buyorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.*;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.enums.SupplierAssetEnum;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.OrderNoDict;
import com.vedeng.erp.buyorder.dto.*;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.buyorder.service.BuyorderInfoSyncService;
import com.vedeng.erp.buyorder.service.NewBuyOrderService;
import com.vedeng.erp.saleorder.api.OrderInfoSyncService;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.erp.trader.dto.SpecialSalesDto;
import com.vedeng.erp.trader.dto.SupplierAssetApiDto;
import com.vedeng.erp.trader.dto.TraderSupplierDto;
import com.vedeng.erp.trader.service.SpecialSalesApiService;
import com.vedeng.erp.trader.service.SupplierAssetApiService;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import com.vedeng.goods.service.GoodsApiService;
import com.vedeng.goods.vo.CoreSkuVo;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.dao.RExpressWarehouseGoodsOutInMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOutInItemMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOutInMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.logistics.model.dto.RExpressWarehouseGoodsOutInDto;
import com.vedeng.order.dao.*;
import com.vedeng.order.model.*;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.order.service.OrderCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.buyorder.service.impl
 * @Date 2021/10/25 19:26
 */
@Slf4j
@Service
public class NewBuyOrderServiceImpl implements NewBuyOrderService {

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private ExpressMapper expressMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private RBuyorderSaleorderMapper rBuyorderSaleorderMapper;

    @Autowired
    private OrderCommonService orderCommonService;

    @Autowired
    OrderInfoSyncService orderInfoSyncService;

    @Autowired
    private BuyorderInfoSyncService buyorderInfoSyncService;

    @Autowired
    private OrderNoDict orderNoDict;

    @Autowired
    private BuyorderService buyorderService;

    @Autowired
    private GoodsApiService goodsApiService;

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;

    @Autowired
    private RExpressWarehouseGoodsOutInMapper rExpressWarehouseGoodsOutInMapper;

    @Autowired
    private WarehouseGoodsOutInItemMapper warehouseGoodsOutInItemMapper;

    @Autowired
    private WarehouseGoodsOutInMapper warehouseGoodsOutInMapper;

    @Autowired
    private SpecialSalesApiService specialSalesApiService;

    @Autowired
    @Qualifier("buyorderExpressMapper")
    private com.vedeng.erp.buyorder.mapper.ExpressMapper buyorderExpressMapper;



    @Override
    public int getSkuOnWayBySkuId(Integer skuId) {
        return buyorderMapper.getSkuOnWayBySkuId(skuId);
    }

    @Override
    public void updateBuyOrderLockStatus(Integer buyOrderId, boolean lockOrUnLock) {
        Objects.requireNonNull(buyOrderId);
        // 锁定采购单
        Buyorder buyorder = new Buyorder();
        buyorder.setBuyorderId(buyOrderId);
        buyorder.setLockedStatus(lockOrUnLock ? CommonConstants.ON : CommonConstants.OFF);
        buyorder.setModTime(System.currentTimeMillis());
        buyorder.setUpdater(ErpConst.NJ_ADMIN_ID);
        buyorderMapper.updateByPrimaryKeySelective(buyorder);
    }


    @Override
    public List<Integer> getSaleorderIdListByBuyorderId(Integer buyorderId) {
        if (buyorderId == null) {
            return Collections.emptyList();
        }
        return buyorderMapper.getSaleorderIdListByBuyorderId(buyorderId);
    }

    @Override
    public BuyorderVo getDirectDeliveryReceiveInfo(BuyorderVo bv) {
        // 上一步已经校验通过，因此默认此处 采购单只关联了一个销售单
        if (bv.getDeliveryDirect() == 0) { // 直发在getApplyBuyorderDetail中已经查出了信息
            List<Integer> saleorderIdList = buyorderMapper.getSaleorderIdListByBuyorderId(bv.getBuyorderId());
            if (CollectionUtils.isEmpty(saleorderIdList)) {
                log.info("采购单ID" + bv.getBuyorderId() + "未查询到关联的销售单");
                throw new RuntimeException("采购单ID" + bv.getBuyorderId() + "未查询到关联的销售单");
            }
            Saleorder sa = saleorderMapper.selectByPrimaryKey(saleorderIdList.get(0));
            if (sa != null) {
                bv.setSaleTakeTraderAddress(sa.getTakeTraderAddress());
                bv.setSaleTakeTraderAddressId(sa.getTakeTraderAddressId());
                bv.setSaleTakeTraderArea(sa.getTakeTraderArea());
                bv.setSaleTakeTraderContactId(sa.getTakeTraderContactId());
                bv.setSaleTakeTraderContactMobile(sa.getTakeTraderContactMobile());
                bv.setSaleTakeTraderContactName(sa.getTakeTraderContactName());
                bv.setSaleTakeTraderContactTelephone(sa.getTakeTraderContactTelephone());
                bv.setSaleTakeTraderId(sa.getTakeTraderId());
                bv.setSaleTakeTraderName(sa.getTakeTraderName());
            }
        }
        return bv;
    }

    @Transactional
    @Override
    public Integer createBuyOrder(BuyorderVo buyorderVo, User user) {
        if (buyorderVo.getBuyorderId() != null) {
            return 0;
        }

        buyorderVo.setCreator(user.getUserId());
        // 订单归属人和归属部门指产品负责人和产品负责人部门
        buyorderVo.setUserId(user.getUserId());
        buyorderVo.setOrgId(user.getOrgId());
        buyorderVo.setAddTime(DateUtil.sysTimeMillis());
        buyorderVo.setCreator(user.getUserId());
        buyorderVo.setCompanyId(user.getCompanyId());
        buyorderVo.setUpdater(user.getUserId());
        buyorderVo.setModTime(DateUtil.sysTimeMillis());

        String[] saleorderGoodsIds = buyorderVo.getSaleorderGoodsIds().split(",");
        List<Map<String, Integer>> mapList = new ArrayList<>();
        if (saleorderGoodsIds.length == 0) {
            return 0;
        }
        Arrays.asList(saleorderGoodsIds).forEach(id -> {
            Map<String, Integer> saleOrderGoodsIdAndSkuIdMap = new HashMap<>();
            Integer goodsId = Integer.valueOf(id.split("\\|")[0]);
            Integer saleOrderGoodsId = Integer.valueOf(id.split("\\|")[1]);
            saleOrderGoodsIdAndSkuIdMap.put("goodsId", goodsId);
            saleOrderGoodsIdAndSkuIdMap.put("saleOrderGoodsId", saleOrderGoodsId);
            mapList.add(saleOrderGoodsIdAndSkuIdMap);
        });


        // 销售单明细ids
        List<Integer> saleOrderGoodsIdList = new ArrayList<>();
        CollectionUtils.collect(Arrays.asList(saleorderGoodsIds), input -> Integer.valueOf(input.toString().split("\\|")[1]), saleOrderGoodsIdList);
        // 判断当前的销售产品是否有锁定，如果有全部返回失败
        int count = saleorderGoodsMapper.getLockedSaleoderGoodsNumBySaleorderGoodsIds(saleOrderGoodsIdList);
        if (count > 0) {
            return -2;
        }

        // 备货单和销售单不允许同时被选择,否则返回-3，创建失败
        List<Integer> orderTypes = saleorderGoodsMapper.getOrderTypesBySaleOrderGoodsIds(saleOrderGoodsIdList);
        if (orderTypes.size() > 1 && orderTypes.contains(2)) {
            return -3;
        }

        String[] goodsIds = buyorderVo.getGoodsIds().split(",");
        // 先判断选中的商品是否全部为虚拟商品，是的话，直接返回失败,不执行新增采购单和采购商品操作
        List<CoreSkuVo> virtualGoodsList = goodsApiService.getAllVirtualGoodsInfo(0);
        long virtualCount = virtualGoodsList.stream().filter(item -> Arrays.asList(goodsIds).contains(item.getSkuId().toString())).count();
        if (goodsIds.length == virtualCount) {
            // 若虚拟商品数量等于选中的所有商品数量，则全部是虚拟商品，直接抛出
            return -4;
        }

        // 销售单下的商品id
        List<Integer> saleOrderGoodsSkuId = new ArrayList<>();
        CollectionUtils.collect(Arrays.asList(saleorderGoodsIds), input -> Integer.valueOf(input.toString().split("\\|")[0]), saleOrderGoodsSkuId);
        List<SaleorderGoodsVo> saleorderGoodsVosList = saleorderGoodsMapper.getSaleorderGoodsVosList(saleOrderGoodsIdList);
        // 当存在一个销售商品未赠品时，则采购订单和采购商品为赠品
        boolean present = saleorderGoodsVosList.stream().anyMatch(s -> s.getIsGift() != null && s.getIsGift() == 1);
        Integer isGift = Convert.toInt(present);
        buyorderVo.setIsGift(isGift);
        // 新增采购订单
        Integer buyorderId = insertBuyorderInfo(buyorderVo);
        buyorderVo.setBuyorderId(buyorderId);

        // 判断是否含有特麦帮的销售订单
        List<Integer> saleorderIdList = saleorderGoodsVosList.stream().map(SaleorderGoods::getSaleorderId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(saleorderIdList)) {
            log.info("采购单创建时，判断销售单中是否含有特麦帮订单，saleorderIdList:{}", saleorderIdList);
            List<SpecialSalesDto> specialSalesEntityList = specialSalesApiService.findByRelateIdInAndRelateTypeAndIsDelete(saleorderIdList, SpecialSalesEnum.SALEORDER.getCode(), ErpConst.ZERO);
            if (CollectionUtils.isNotEmpty(specialSalesEntityList)) {
                log.info("采购单创建时，销售单中含有特麦帮订单，specialSalesList:{}", JSON.toJSONString(specialSalesEntityList));
                SpecialSalesDto specialSalesDto = SpecialSalesDto.builder()
                        .relateId(buyorderId)
                        .relateType(SpecialSalesEnum.BUYORDER.getCode())
                        .isDelete(ErpConst.ZERO)
                        .creator(user.getUserId())
                        .creatorName(user.getUsername())
                        .updater(user.getUserId())
                        .updaterName(user.getUsername())
                        .build();
                specialSalesApiService.insertSpecialSales(specialSalesDto);
                log.info("采购单创建时，销售单中含有特麦帮订单，新增采购订单特麦帮关联信息成功，specialSales:{}", JSON.toJSONString(specialSalesDto));
            }
        }

        Set<Integer> goodsIdList = Arrays.stream(goodsIds).map(Integer::valueOf).collect(Collectors.toSet());
        List<CoreSkuVo> coreSkuVoList = goodsApiService.findBySkuBySkuIds(CollUtil.newArrayList(goodsIdList));
        // 采购订单明细商品新增
        coreSkuVoList.stream().filter(coreSkuVo -> coreSkuVo.getIsVirtureSku() == null || coreSkuVo.getIsVirtureSku() == 0)
                .forEach(skuInfo -> this.assembleAndInsertBuyOrderGoods(buyorderVo, skuInfo, mapList, isGift));
        // 更新BUYORDER的SALEORDER_NOS字段
        this.updateSaleorderNos(saleOrderGoodsIdList, buyorderId);

        // 更新其他采购单信息
        String operateType = OrderDataUpdateConstant.BUY_VP_ORDER_GENERATE;
        if (buyorderVo.getOrderType().equals(1)) {
            operateType = OrderDataUpdateConstant.BUY_VB_ORDER_GENERATE;
        }
        orderCommonService.updateBuyOrderDataUpdateTime(buyorderId, null, operateType);

        // 虚拟商品
        List<CoreSkuVo> virtualSku = coreSkuVoList.stream().filter(coreSkuVo -> coreSkuVo.getIsVirtureSku() != null && coreSkuVo.getIsVirtureSku() == 1)
                .collect(Collectors.toList());
        // 先判断是否有虚拟商品，如果有，就新增 采购费用订单
        if (virtualSku.size() > 0) {
            BuyorderExpenseDetailDto buyorderExpenseDetailDto = new BuyorderExpenseDetailDto();
            buyorderExpenseDetailDto.setOrgId(user.getOrgId());
            List<BuyorderExpenseItemDto> buyorderExpenseItemDtos = new ArrayList<>();

            virtualSku.forEach(skuInfo -> {
                List<Integer> list = mapList.stream().filter(m -> m.get("goodsId").equals(skuInfo.getSkuId())).map(m -> m.get("saleOrderGoodsId")).collect(Collectors.toList());
                this.assembleExpenseBill(buyorderExpenseItemDtos, skuInfo, list);
            });
            // 费用单
            BuyorderExpenseDto buyorderExpenseDto = BuyorderExpenseDto.builder()
                    .buyorderNo(buyorderVo.getBuyorderNo())
                    .buyorderId(buyorderId)
                    .orderType(0)
                    .buyorderExpenseDetailDto(buyorderExpenseDetailDto)
                    .buyorderExpenseItemDtos(buyorderExpenseItemDtos)
                    .build();
            buyorderExpenseApiService.add(buyorderExpenseDto);
        }
        return buyorderId;
    }


    /**
     * 新增采购单信息
     */
    private Integer insertBuyorderInfo(BuyorderVo buyorderVo) {
        Buyorder buyorder = new Buyorder();
        buyorder.setCreator(buyorderVo.getCreator());
        buyorder.setAddTime(buyorderVo.getAddTime());
        buyorder.setUpdater(buyorderVo.getCreator());
        buyorder.setModTime(buyorderVo.getAddTime());
        buyorder.setDeliveryDirect(buyorderVo.getDeliveryDirect());
        buyorder.setCompanyId(buyorderVo.getCompanyId());
        buyorder.setOrgId(buyorderVo.getOrgId());
        buyorder.setNewFlow(1);
        buyorder.setIsNew(1);
        if (buyorderVo.getIsGift() != null && buyorderVo.getIsGift() == 1) {
            buyorder.setIsGift(1);
        } else {
            buyorder.setIsGift(0);
        }
        if (buyorderVo.getDeliveryDirect() == 1) {
            // 直发需同步销售订单的收货地址到采购订单
            Saleorder saleorder = saleorderMapper.selectByPrimaryKey(buyorderVo.getSaleorderId());
            buyorder.setTakeTraderId(saleorder.getTakeTraderId());
            buyorder.setTakeTraderAddress(saleorder.getTakeTraderAddress());
            buyorder.setTakeTraderAddressId(saleorder.getTakeTraderAddressId());
            buyorder.setTakeTraderContactId(saleorder.getTakeTraderContactId());
            buyorder.setTakeTraderContactMobile(saleorder.getTakeTraderContactMobile());
            buyorder.setTakeTraderContactName(saleorder.getTakeTraderContactName());
            buyorder.setTakeTraderContactTelephone(saleorder.getTakeTraderContactTelephone());
            buyorder.setTakeTraderName(saleorder.getTakeTraderName());
            buyorder.setTakeTraderArea(saleorder.getTakeTraderArea());
            buyorder.setLogisticsId(saleorder.getLogisticsId());
            buyorder.setFreightDescription(saleorder.getFreightDescription());
            buyorder.setLogisticsComments(saleorder.getLogisticsComments());
        }
        buyorder.setOrderType(buyorderVo.getOrderType());
        buyorder.setOrgId(buyorderVo.getOrgId());
        buyorder.setUserId(buyorderVo.getUserId());
        buyorderMapper.insertSelective(buyorder);

        // 更新buyorderNo
        String orderNo;
        if (buyorderVo.getOrderType() == 0) {
            orderNo = orderNoDict.getOrderNum(buyorder.getBuyorderId(), Constants.FIVE);
            buyorder.setBuyorderNo(orderNo);
        } else {
            orderNo = orderNoDict.getOrderNum(buyorder.getBuyorderId(), 14);
            buyorder.setBuyorderNo(orderNo);
        }
        // 此处将buyorderNo返回
        buyorderVo.setBuyorderNo(orderNo);
        buyorderMapper.updateByPrimaryKeySelective(buyorder);
        return buyorder.getBuyorderId();
    }

    /**
     * 组装并插入采购单商品信息
     */
    private void assembleAndInsertBuyOrderGoods(BuyorderVo buyorderVo,
                                                CoreSkuVo skuInfo,
                                                List<Map<String, Integer>> mapList,
                                                Integer isGift) {
        BuyorderGoods buyorderGoods = new BuyorderGoods();
        // 非虚拟商品，插入buyorder_goods
        buyorderGoods.setBrandName(skuInfo.getBrandName());
        buyorderGoods.setGoodsName(skuInfo.getShowName());
        buyorderGoods.setSku(skuInfo.getSkuNo());
        buyorderGoods.setModel(skuInfo.getModel());
        buyorderGoods.setUnitName(skuInfo.getUnitName());
        buyorderGoods.setAddTime(buyorderVo.getAddTime());
        buyorderGoods.setModTime(buyorderVo.getAddTime());
        buyorderGoods.setCreator(buyorderVo.getCreator());
        buyorderGoods.setUpdater(buyorderVo.getCreator());
        buyorderGoods.setBuyorderId(buyorderVo.getBuyorderId());
        buyorderGoods.setGoodsId(skuInfo.getSkuId());
        buyorderGoods.setNum(0);
        buyorderGoods.setIsGift(isGift);
        buyorderGoodsMapper.insertSelective(buyorderGoods);
        //更新快照信息
        BuyorderGoodsVo buyorderGoodsVo = new BuyorderGoodsVo();
        buyorderGoodsVo.setBuyorderGoodsId(buyorderGoods.getBuyorderGoodsId());
        buyorderGoodsVo.setSku(buyorderGoods.getSku());
        buyorderGoodsMapper.updateBuyorderGoodsSnapshotInfo(buyorderGoodsVo);
        // 更新buyorderGoods 和 saleorderGoods 关联表的信息
        if (buyorderGoods.getBuyorderGoodsId() > 0) {
            mapList.forEach(m -> {
                if (m.get("goodsId").equals(skuInfo.getSkuId())) {
                    this.insertBuyorderGoodsSaleorderGoodsRelation(m.get("saleOrderGoodsId"), buyorderGoods.getBuyorderGoodsId());
                }
            });
        }
    }

    /**
     * 组装费用详情
     *
     * @param buyorderExpenseItemDtos
     * @param skuInfo
     * @return
     */
    private BuyorderExpenseItemDto assembleExpenseBill(List<BuyorderExpenseItemDto> buyorderExpenseItemDtos, CoreSkuVo skuInfo, List<Integer> saleOrderGoodsIds) {
        // 虚拟商品，组装信息
        BuyorderExpenseItemDto expense = new BuyorderExpenseItemDto();
        if (CollUtil.isEmpty(saleOrderGoodsIds)) {
            log.info("组装费用单据,buyorderExpenseItemDtos:{},saleOrderGoodsIds:{}", buyorderExpenseItemDtos, saleOrderGoodsIds);
            return expense;
        }

        List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtoList = saleOrderGoodsApiService.getRelatedDetail(saleOrderGoodsIds);
        expense.setGoodsId(skuInfo.getSkuId());
        BuyorderExpenseItemDetailDto itemDetailDto = new BuyorderExpenseItemDetailDto();
        itemDetailDto.setSku(skuInfo.getSkuNo());
        itemDetailDto.setGoodsName(skuInfo.getShowName());
        itemDetailDto.setExpenseCategoryId(skuInfo.getCostCategoryId());
        itemDetailDto.setExpenseCategoryName(skuInfo.getCategoryName());
        itemDetailDto.setHaveStockManage(skuInfo.getHaveStockManage());
        expense.setBuyorderExpenseItemDetailDto(itemDetailDto);
        buyorderExpenseItemDtos.add(expense);
        //待采购列表关系模块
        List<BuyOrderSaleOrderGoodsDetailDto> buyOrderSaleOrderGoodsDetailDtos = new ArrayList<>();

        saleOrderGoodsDetailDtoList.forEach(saleOrderGoodsDetailDto -> {
            BuyOrderSaleOrderGoodsDetailDto buyOrderSaleOrderGoodsDetailDto = new BuyOrderSaleOrderGoodsDetailDto();
            buyOrderSaleOrderGoodsDetailDto.setGoodsId(skuInfo.getSkuId());
            buyOrderSaleOrderGoodsDetailDto.setBuyNum(0);
            buyOrderSaleOrderGoodsDetailDto.setSaleorderId(saleOrderGoodsDetailDto.getSaleorderId());
            buyOrderSaleOrderGoodsDetailDto.setSaleorderGoodsId(saleOrderGoodsDetailDto.getSaleorderGoodsId());
            buyOrderSaleOrderGoodsDetailDtos.add(buyOrderSaleOrderGoodsDetailDto);
        });
        expense.setBuyOrderSaleOrderGoodsDetailDtos(buyOrderSaleOrderGoodsDetailDtos);
        return expense;
    }

    /**
     * 插入采购商品 和 销售商品 的关联信息
     */
    private void insertBuyorderGoodsSaleorderGoodsRelation(Integer saleorderGoodsId, Integer buyorderGoodsId) {
        RBuyorderSaleorder rBuyorderSaleorder = new RBuyorderSaleorder();
        rBuyorderSaleorder.setBuyorderGoodsId(buyorderGoodsId);
        rBuyorderSaleorder.setSaleorderGoodsId(saleorderGoodsId);
        rBuyorderSaleorder.setNum(Constants.ZERO);
        rBuyorderSaleorderMapper.insertSelective(rBuyorderSaleorder);
    }

    /**
     * 根据saleorderGoodsIds获取采购单关联的销售单号集合，并更新至BUYORDER的SALEORDER_NOS字段
     */
    private void updateSaleorderNos(List<Integer> saleorderGoodsIdList, Integer buyorderId) {
        List<String> saleorderNoList = saleorderMapper.getSaleorderNoList(saleorderGoodsIdList);
        Buyorder buyorder = new Buyorder();
        buyorder.setBuyorderId(buyorderId);
        buyorder.setSaleorderNos(saleorderNoList.stream().map(String::valueOf).collect(Collectors.joining(",")));
        buyorderMapper.updateByPrimaryKeySelective(buyorder);
    }

    @Override
    @Transactional
    public ResultInfo deleteExpressByExpressId(Integer expressId) {
        int i = expressMapper.logicalDeleteExpress(expressId);
        Integer buyorderVoId = expressMapper.getBuyorderByExpressId(expressId);
        BuyorderVo buyorderVo = buyorderMapper.getBuyorderVoById(buyorderVoId);
        List<ExpressDetail> expressDetailListByBuyorderId = expressMapper.getEnableExpressDetailListByBuyorderId(buyorderVo.getBuyorderId());
        // 直发采购需修改销售订单的发货状态
        if (buyorderVo.getDeliveryDirect() == 1) {
            buyorderService.checkExpressEnableReceive(buyorderVoId);
            List<RBuyorderSaleorder> listR = rBuyorderSaleorderMapper.getRBuyorderSaleorderListByParam(buyorderVo.getBuyorderId());
            Buyorder bu = new Buyorder();
            bu.setBuyorderId(buyorderVo.getBuyorderId());
            if (CollectionUtils.isEmpty(expressDetailListByBuyorderId)) {
                bu.setDeliveryStatus(0);
            } else {
                bu.setDeliveryStatus(1);
                boolean flag = false;
                bu.setDeliveryTime(DateUtil.sysTimeMillis());
                for (RBuyorderSaleorder rBuyorderSaleorder : listR) {
                    for (ExpressDetail epd : expressDetailListByBuyorderId) {
                        if (rBuyorderSaleorder.getBuyorderGoodsId().equals(epd.getRelatedId())
                                && rBuyorderSaleorder.getNum() > epd.getNum()) {
                            flag = true;
                            break;
                        }
                    }
                    if (flag) {
                        bu.setDeliveryStatus(1);
                        break;
                    }
                }
            }
            buyorderMapper.updateByPrimaryKeySelective(bu);

            // 修改对应的销售商品的发货状态
            List<RBuyorderSaleorder> listInfo = rBuyorderSaleorderMapper.getRBuyorderSaleorderInfoList(buyorderVo.getBuyorderId());
            List<SaleorderGoods> sdLists = saleorderGoodsMapper.getSaleorderGoodsList(listInfo);
            for (SaleorderGoods saleorderGoods : sdLists) {
                //订单流升级更新销售单收发货信息
                orderInfoSyncService.syncDeliveryAndArrivalDetailOfSaleOrder(saleorderGoods.getSaleorderId());
            }

            List<RExpressWarehouseGoodsOutInDto> rExpressWarehouseGoodsOutInDtos = rExpressWarehouseGoodsOutInMapper.selectByExpressId(expressId);

            if (CollUtil.isNotEmpty(rExpressWarehouseGoodsOutInDtos)) {
                log.info("删除快递单id:{}关联的出入库信息：{}", expressId, JSON.toJSONString(rExpressWarehouseGoodsOutInDtos));
                List<Integer> collect = rExpressWarehouseGoodsOutInDtos.stream().map(RExpressWarehouseGoodsOutInDto::getWarehouseGoodsOutInId).collect(Collectors.toList());

                List<String> outInNoByWarehouseGoodsOutInId = warehouseGoodsOutInMapper.findOutInNoByWarehouseGoodsOutInId(collect);
                rExpressWarehouseGoodsOutInDtos.forEach(x -> {

                    // 直发单删除 改出入库
                    rExpressWarehouseGoodsOutInMapper.logicaldeleteByExpressId(expressId);
                    warehouseGoodsOutInMapper.logicalDeleteByWarehouseGoodsOutInId(Long.valueOf(x.getWarehouseGoodsOutInId()));
                });

                // 明细删除
                if (CollUtil.isNotEmpty(outInNoByWarehouseGoodsOutInId)) {
                    outInNoByWarehouseGoodsOutInId.forEach(x -> {
                        warehouseGoodsOutInItemMapper.logicalDeleteByOutInNo(x);
                    });
                }


            }

        }

        // 采购删除快递处理直发订单账期编码监管
        Express express = expressMapper.selectExpressByPrimaryKey(expressId);
        buyorderService.dealDirectOrderPeriodManagement(express, buyorderVo.getBuyorderId(), ErpConst.OperateType.DELETE_OPERATE);

        // VDERP-8759 订单流发货状态同步
        buyorderInfoSyncService.syncDeliveryStatus(buyorderVoId, 2);

        return ResultInfo.success();
    }

    @Override
    public PageInfo<BuyOrderExpressDto> needConfirmList(PageParam<BuyOrderExpressDto> pageParam) {
        BuyOrderExpressDto param = pageParam.getParam();
        Date searchDeliveryDateStart = param.getSearchDeliveryDateStart();
        Date searchDeliveryDateEnd = param.getSearchDeliveryDateEnd();
        param.setRemoteAreaRegionId(ErpConstant.REMOTE_AREA_REGION_ID);
        param.setSearchDeliveryDateStartStr(Objects.nonNull(searchDeliveryDateStart) ? searchDeliveryDateStart.getTime() : null);
        param.setSearchDeliveryDateEndStr(Objects.nonNull(searchDeliveryDateEnd) ? searchDeliveryDateEnd.getTime() : null);
        PageInfo<BuyOrderExpressDto> result = PageHelper.startPage(pageParam).doSelectPageInfo(() -> buyorderExpressMapper.getNeedConfirmList(param));
        return result;
    }

    @Override
    public BigDecimal getUsedRebateCharge(Integer buyorderId, Integer traderId) {
        BuyorderVo buyorderVoById = buyorderMapper.getBuyorderVoById(buyorderId);
        if (!buyorderVoById.getTraderId().equals(traderId)) {
            return BigDecimal.ZERO;
        }

        List<BuyorderGoodsVo> buyorderGoodsVoListByBuyorderIds = buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIds(buyorderId);
        buyorderGoodsVoListByBuyorderIds = buyorderGoodsVoListByBuyorderIds.stream().filter(v -> v.getIsDelete().equals(0)).collect(Collectors.toList());
        return buyorderGoodsVoListByBuyorderIds.stream().map(BuyorderGoodsVo::getRebateAmount).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }
}
