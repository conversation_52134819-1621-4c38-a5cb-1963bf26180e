package com.vedeng.workbench.model.vo.summary;

import com.vedeng.workbench.model.vo.base.WorkbenchSummaryObject;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 预计成单汇总.
 * @jira: VDERP-3073【商机管理中心】ERP销售工作台.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/10/25 9:04 下午.
 * @author: Tomcat.Hui.
 */
@Data
public class ExpectOrderSummary {

    /**
     * 统计对象信息
     */
    private WorkbenchSummaryObject objectInfo;

    /**
     * 上周预计成单数量
     */
    private Integer lastWeekSuccessNum;

    /**
     * 上周未成单数量
     */
    private Integer lastWeekFailNum;

    /**
     * 打靶率
     */
    private BigDecimal hitRate;

    /**
     * 预计本周成单数量
     */
    private Integer thisWeekSuccessNum;

    /**
     * 预计本周成单金额
     */
    private BigDecimal thisWeekAmount;
}
