package com.vedeng.erp.trader.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *<AUTHOR>
 *@Description com.vedeng.erp.trader.domain.entity
 *@Date 2022/8/30 10:25
 */
/**
    * 供应商
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TraderSupplierEntity {
    private Integer traderSupplierId;

    /**
    * 交易者ID
    */
    private Integer traderId;

    private BigDecimal amount;

    private BigDecimal periodAmount;

    /**
    * 帐期天数
    */
    private Integer periodDay;

    /**
    * 是否有效 0否 1是
    */
    private Boolean isEnable;

    /**
    * 是否置顶0否 1是
    */
    private Boolean isTop;

    /**
    * 供应品牌
    */
    private String supplyBrand;

    /**
    * 供应产品
    */
    private String supplyProduct;

    /**
    * 供应商等级　SYS_OPTION_DEFINITION_ID
    */
    private Integer grade;

    /**
    * 禁用时间
    */
    private Long disableTime;

    /**
    * 禁用原因
    */
    private String disableReason;

    /**
    * 备注
    */
    private String comments;

    /**
    * 简介
    */
    private String brief;

    /**
    * 品牌性质 1国产 2进口
    */
    private String hotTelephone;

    /**
    * 售后电话
    */
    private String serviceTelephone;

    /**
    * 承运商
    */
    private String logisticsName;

    /**
    * 官方网址
    */
    private String website;

    /**
    * 添加时间
    */
    private Long addTime;

    /**
    * 添加人
    */
    private Integer creator;

    /**
    * 最近一次编辑时间
    */
    private Long modTime;

    /**
    * 最近一次编辑人
    */
    private Integer updater;

    /**
    * 仓库地区最小级ID
    */
    private Integer warehouseAreaId;

    /**
    * 仓库多级地址逗号“,”拼接（冗余字段）
    */
    private String warehouseAreaIds;

    /**
    * 仓库详细地址
    */
    private String warehouseAddress;

    /**
    * 1 生产厂家  2 经销商
    */
    private Boolean traderType;

    /**
    * 售后总对接人-联系人姓名
    */
    private String afterSaleManager;

    /**
    * 安装服务联系人-联系人姓名
    */
    private String installserviceContactname;

    /**
    * 安装服务联系人-联系方式
    */
    private String installserviceContactway;

    /**
    * 技术支持联系人-联系人姓名
    */
    private String technicaldirectContactname;

    /**
    * 技术支持联系人-联系电话
    */
    private String technicaldirectContactway;

    /**
    * 维修服务联系人-联系人姓名
    */
    private String maintenanceContactname;

    /**
    * 维修服务联系人-联系电话
    */
    private String maintenanceContactway;

    /**
    * 退换货联系人-联系人姓名
    */
    private String exchangeContactname;

    /**
    * 退换货联系人-联系人电话
    */
    private String exchangeContactway;

    /**
    * 其他对接人-联系人姓名
    */
    private String otherContactname;

    /**
    * 其他对接人-联系人姓名
    */
    private String otherContactway;

    /**
    * 生效时间
    */
    private Date vaildTime;

    /**
    * 纳税人分类 1：一般纳税人 2 :小规模纳税人
    */
    private Byte taxPayerType;
}