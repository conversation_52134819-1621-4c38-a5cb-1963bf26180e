package com.vedeng.aftersales.component.dto;

import java.io.Serializable;
import lombok.Data;

/**
 * T_AFTER_SALE_SERVICE_LABEL
 * <AUTHOR>
@Data
public class AfterSaleServiceLabelDto implements Serializable {
    /**
     * 售后服务标签ID
     */
    private Integer afterSaleServiceLabelId;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 标签描述
     */
    private String labelDescription;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    /**
     * 标签编码
     */
    private String labelCode;

    private Integer checked;

    private Integer backUp;



    private static final long serialVersionUID = 1L;
}