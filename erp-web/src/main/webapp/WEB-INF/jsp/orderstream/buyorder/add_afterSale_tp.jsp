<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增售后-退票" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%=basePath%>/static/js/orderstream/buyorder/add_afterSales_buyorder.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%=basePath%>/static/js/aftersales/order/controlRadio.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src="<%= basePath %>/static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<div class="form-list  form-tips5">
  <form method="post" action="<%= basePath %>/order/newBuyorder/saveAddAfterSales.do">
    <ul>
      <li>
        <div class="form-tips">
          <lable>售后类型</lable>
        </div>
        <div class="f_left">
          <div class="form-blanks">
            <ul class="aftersales">
              <li>
                <a href="${pageContext.request.contextPath}/order/newBuyorder/addAfterSalesPage.do?flag=th&&buyorderId=${buyorder.buyorderId}">
                  <input type="radio" name="type" value="546">
                  <label>退货</label>
                </a>
              </li>
              <li>
                <a href="${pageContext.request.contextPath}/order/newBuyorder/addAfterSalesPage.do?flag=hh&&buyorderId=${buyorder.buyorderId}">
                  <input type="radio" name="type" value="547">
                  <label>换货</label>
                </a>
              </li>
              <li>
                <a href="${pageContext.request.contextPath}/order/newBuyorder/addAfterSalesPage.do?flag=tp&&buyorderId=${buyorder.buyorderId}">
                  <input type="radio" checked="true"  name="type" value="548">
                  <label>退票</label>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </li>
      <input type="hidden" name="orderId" value="${buyorder.buyorderId}" />
      <input type="hidden" name="orderNo" value="${buyorder.buyorderNo}" />
      <input type="hidden" name="traderId" value="${buyorder.traderId}" />
      <input type="hidden" name="invoiceNo" id="oldInvoiceNo"/>
      <input type="hidden" id="shtype" value="tp" />
      <input type="hidden" name="formToken" value="${formToken}"/>
      <li>
        <div class="form-tips">
          <span>*</span>
          <lable>售后原因</lable>
        </div>
        <div class="f_left">
          <div class="form-blanks">
            <ul>
              <c:forEach items="${sysList}" var="sys" >
                <c:if test="${sys.relatedField eq 548 && sys.status eq 1}">
                  <li>
                    <input type="radio" name="reason" value="${sys.sysOptionDefinitionId}">
                    <label>${sys.title}</label>
                  </li>
                </c:if>
              </c:forEach>
            </ul>
          </div>
          <div id="reasonError" class="font-red " style="display: none">售后原因不允许为空</div>
        </div>
      </li>
      <li>
        <div class="parts">
          <div class="title-container">
            <div class="table-title nobor">
              请选择退票商品及发票
            </div>
          </div>
          <table class="table">
            <thead>
            <tr>
              <th class="wid5">选择</th>
              <th class="wid18">产品名称</th>
              <th class="wid10">品牌</th>
              <th class="wid10">规格/型号</th>
              <th class="wid8">物料编码</th>
              <th class="wid6">采购价</th>
              <th class="wid8">数量</th>
              <th class="wid12">单位</th>
              <th class="wid8">收票数</th>
              <th class="wid10">发票号</th>
              <%--原退票数量选择隐藏，变更为发票代码展示--%>
<%--              <th class="wid10">本次退票数(不可大于收票数)</th>--%>
              <th class="wid10">发票代码</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty afterBuyorderInvoiceGoodsDtos}">
              <c:forEach items="${afterBuyorderInvoiceGoodsDtos}" var="bgv">
                <tr>
                  <td>
                    <input type="checkbox" name="oneSelect" alt="${bgv.detailGoodsId}_${bgv.invoiceNo}" onclick="checkAfterNum(${bgv.detailGoodsId}_${bgv.invoiceNo},this,1)">
                    <input type="hidden" id="detailGoodsId_${bgv.detailGoodsId}_${bgv.invoiceNo}" name="detailGoodsId" value="${bgv.detailGoodsId}">
                    <input type="hidden" id="sku_${bgv.detailGoodsId}_${bgv.invoiceNo}" value="${bgv.sku}">
                    <input type="hidden" id="goodsId_${bgv.detailGoodsId}_${bgv.invoiceNo}" value="${bgv.goodsId}">
                  </td>
                  <td class="text-left">
                    <span class="font-blue cursor-pointer addtitle"
                          tabTitle='{"num":"viewgoods<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                        "link":"./goods/goods/viewbaseinfo.do?goodsId=${bgv.goodsId}","title":"产品信息"}'>${bgv.goodsName}</span>
                      <br>${bgv.sku}
                  </td>
                  <td>${bgv.brandName}</td>
                  <td>${bgv.spec}/${bgv.model}</td>
                  <td>${bgv.materialCode}</td>
                  <td><span id="buyPrice_${bgv.detailGoodsId}_${bgv.invoiceNo}">${bgv.buyPrice}</span></td>
                  <td><fmt:formatNumber type="number" value="${bgv.num}" pattern="0"
                                        maxFractionDigits="0"/></td>
                  <td>${bgv.unitName}</td>
                  <td><span id="invoiceNum_${bgv.detailGoodsId}_${bgv.invoiceNo}"><fmt:formatNumber type="number" value="${bgv.invoiceNum}" pattern="0.00" maxFractionDigits="2"/></span></td>
                  <td><span id="invoiceNo_${bgv.detailGoodsId}_${bgv.invoiceNo}">${bgv.invoiceNo}</span></td>
                  <td>
                      ${bgv.invoiceCode}
                    <%--原退票数量选择隐藏，变更为发票代码展示--%>
                    <input type="hidden" id="afterNum_${bgv.detailGoodsId}_${bgv.invoiceNo}" name="jts" readonly value="<fmt:formatNumber type="number" value="${bgv.invoiceNum}" pattern="0.00" maxFractionDigits="2"/>" onblur="checkAfterNum(${bgv.detailGoodsId}_${bgv.invoiceNo},this,2)"/>
                    <input type="hidden" name="afterSaleNums" id="afterSaleNums_${bgv.detailGoodsId}_${bgv.invoiceNo}">
                    <div id="reasonError_${bgv.detailGoodsId}_${bgv.invoiceNo}" class="font-red " style="display: none">不得大于收票数</div>
                  </td>
                </tr>
              </c:forEach>
            </c:if>
            <c:if test="${empty afterBuyorderInvoiceGoodsDtos}">
              <tr>
                <td colspan="11">
                  无可退票商品
                </td>
              </tr>
            </c:if>
            </tbody>
          </table>
        </div>
      </li>
      <li>
        <div class="form-tips" style="width: 100px;">
          <span>*</span>
          <lable>待新录的发票号</lable>
        </div>
        <div class="f_left ">
          <input type="number" name="newInvoiceNo" style="width: 180px" id="newInvoiceNo"/>
        </div>
        <div style="margin-left: 300px">
          <i class="vd-icon icon-info2" style="color: #faad14; background: none"></i>
          退票完成后，将自动将此发票重新录入所退商品，请务必确保所输发票可录金额
          <br>及金额满足录票条件！
        </div>
      </li>
      <li>
        <div class="form-tips" style="width: 110px;">
          <span>*</span>
          <lable>待新录的发票代码</lable>
        </div>
        <div class="f_left ">
          <input type="number"  name="newInvoiceCode" id="newInvoiceCode"/>
          <input type="checkbox" name="allElectric" id="allElectric" /> 数电发票
        </div>
      </li>
      <li>
        <div class="form-tips" style="width: 100px;">
<%--          <span>*</span>--%>
          <lable>待新录的发票种类</lable>
        </div>
        <div class="f_left ">
          <select name="invoiceType" id="invoiceType">
            <c:forEach items="${invoiceTypeList}" var="invoiceType" >
              <option value="${invoiceType.sysOptionDefinitionId}" <c:if test="${invoiceType.sysOptionDefinitionId eq 972}">selected="selected"</c:if>>${invoiceType.title}</option>
            </c:forEach>
          </select>
        </div>
      </li>
      <li>
        <div class="form-tips">
          <span>*</span>
          <lable>详情说明</lable>
        </div>
        <div class="f_left ">
          <div class="form-blanks ">
            <textarea name="comments" id="comments" placeholder="请详细描述退票原因" rows="5" class="wid90"></textarea>
          </div>
          <div id="commentsError"></div>
        </div>
      </li>
      <li>
        <div class="form-tips">
          <lable>上传附件</lable>
        </div>
        <input type="hidden" id="domain" name="domain" value="${domain}">
        <div class="f_left ">
          <div class="form-blanks">
            <div class="pos_rel f_left">
              <input type="file" class="uploadErp" id='file_1' name="lwfile" onchange="uploadFile(this,1);">
              <input type="text" class="input-largest" id="name_1" readonly="readonly"
                     placeholder="请上传附件" name="fileName" onclick="file_1.click();" >
              <input type="hidden" id="uri_1" name="fileUri" >
            </div>
            <label class="bt-bg-style bt-small bg-light-blue" type="file" id="busUpload" onclick="return $('#file_1').click();">浏览</label>
            <!-- 上传成功出现 -->
            <div class="f_left">
              <i class="iconsuccesss mt3 none" id="img_icon_1"></i>
              <a href="" target="_blank" class="font-blue cursor-pointer  mt3 none" id="img_view_1">查看</a>
              <span class="font-red cursor-pointer  mt3 none" onclick="del(1)" id="img_del_1">删除</span>
            </div>
            <div class='clear'></div>
          </div>
          <div class="mt8" id="conadd">
            <span class="bt-border-style bt-small border-blue" onclick="conadd();">继续添加</span>
          </div>
          <div class="pop-friend-tips mt6">
            退票须知：
            <br/> 1、如果是供应商开错，且是当月，则无需创建售后单，尽快通知供应商重新开票，将原蓝字有效票作废，系统抓取到作废票后将自动处理（供应商开票后1-2天，若已开出紧急情况可联系财务进行人工拉取作废票），之后可重新录票；
            <br/> 2、仅能选择发票号相同的商品退票
            <br/> 2、选择此售后，只能基于当前订单做所选发票进行红冲，不影响其他订单。若其他订单亦需退票，请自行给对应订单创建退票售后单；
            <div class="add-tijiao text-left mt8">
              <button type="submit" id="submit">提交</button>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </form>
</div>
<style>
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  input[type="number"] {
    -moz-appearance: textfield;
  }
</style>
<%@ include file="../../common/footer.jsp"%>
