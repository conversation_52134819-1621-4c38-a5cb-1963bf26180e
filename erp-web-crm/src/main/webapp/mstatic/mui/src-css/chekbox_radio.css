.vd-ui-checkbox-group.is-margin- {
  margin-bottom: -10px;
}
.vd-ui-checkbox-group .vd-ui-checkbox-item {
  margin-right: 20px;
  margin-bottom: 10px;
}
.vd-ui-checkbox-group .vd-ui-checkbox-item:last-child {
  margin-right: 0;
}
.vd-ui-checkbox-group .vd-ui-input-error {
  margin-top: -5px !important;
}
.vd-ui-checkbox-group.is-label {
  margin-bottom: -5px;
}
.vd-ui-checkbox-group.is-label .vd-ui-checkbox-item {
  margin-right: 5px;
  margin-bottom: 5px;
}
.vd-ui-checkbox-group.is-label .vd-ui-checkbox-item:last-child {
  margin-right: 0;
}
.vd-ui-checkbox-item {
  display: inline-block;
  cursor: pointer;
  color: #333333;
  vertical-align: top;
}
.vd-ui-checkbox-item * {
  box-sizing: border-box;
}
.vd-ui-checkbox-item .vd-ui-checkbox-icon {
  position: relative;
  width: 16px;
  height: 16px;
  border-radius: 2px;
  border: 1px solid #969B9E;
  margin-right: 5px;
  transition: all 0.1s;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1px;
}
.vd-ui-checkbox-item .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 {
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: scale(0);
  transition: transform 0.1s;
}
.vd-ui-checkbox-item .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 .icon-selected2 {
  font-size: 12px;
  color: #ffffff;
}
.vd-ui-checkbox-item .vd-ui-checkbox-inner {
  display: flex;
}
.vd-ui-checkbox-item .vd-ui-checkbox-inner .strong {
  color: #f60;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-checked .vd-ui-checkbox-icon {
  background: #0099FF;
  border-color: #0099FF;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-checked .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 {
  transform: scale(1);
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-progress .vd-ui-checkbox-icon {
  background: #0099FF;
  border-color: #0099FF;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-progress .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 {
  transform: scale(1);
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-progress .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 .icon-selected2 {
  display: inline-block;
  width: 8px;
  height: 2px;
  background: #fff;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-progress .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 .icon-selected2::before {
  display: none;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-disabled {
  color: #999999;
  cursor: not-allowed;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-disabled .vd-ui-checkbox-icon {
  border-color: #D7DADE;
  background: #F5F7FA;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-disabled.vd-ui-checkbox-item-checked .vd-ui-checkbox-icon {
  border-color: #D7DADE;
  background: #D7DADE;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-labeltype .vd-ui-checkbox-icon {
  display: none;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-labeltype .vd-ui-checkbox-label {
  padding: 6px 10px;
  border-radius: 3px;
  background: #F5F7FA;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-labeltype.vd-ui-checkbox-item-checked .vd-ui-checkbox-label {
  background: #09f;
  color: #fff;
}
.vd-ui-checkbox-item.single-row {
  display: block;
  padding: 10px;
  margin: 0;
  border-bottom: solid 1px #F5F7FA;
}
.vd-ui-checkbox-item.single-row:last-child {
  border-bottom: none;
}
.vd-ui-checkbox-item.single-row.vd-ui-checkbox-item-checked {
  background: #E0F3FF;
}
.vd-ui-radio-group {
  margin-bottom: -10px;
}
.vd-ui-radio-group .vd-ui-radio-item {
  margin-right: 20px;
  margin-bottom: 10px;
}
.vd-ui-radio-group .vd-ui-radio-item:last-child {
  margin-right: 0;
}
.vd-ui-radio-group .vd-ui-input-error {
  margin-top: -5px !important;
}
.vd-ui-radio-group.is-label {
  margin-bottom: -5px;
}
.vd-ui-radio-group.is-label .vd-ui-radio-item {
  margin-right: 5px;
  margin-bottom: 5px;
}
.vd-ui-radio-group.is-label .vd-ui-radio-item:last-child {
  margin-right: 0;
}
.vd-ui-radio-item {
  display: inline-block;
  cursor: pointer;
  color: #333333;
  vertical-align: top;
}
.vd-ui-radio-item * {
  box-sizing: border-box;
}
.vd-ui-radio-item .vd-ui-radio-icon {
  flex-shrink: 0;
  position: relative;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 1px solid #969B9E;
  margin-right: 5px;
  transition: all 0.1s;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2px;
}
.vd-ui-radio-item .vd-ui-radio-icon .vd-ui-radio-icon-selected {
  width: 0;
  height: 0;
  background: #0099FF;
  transition: all 0.1s;
  border-radius: 50%;
}
.vd-ui-radio-item .vd-ui-radio-label {
  white-space: nowrap;
}
.vd-ui-radio-item .vd-ui-radio-tip {
  color: #999;
}
.vd-ui-radio-item .vd-ui-radio-inner {
  display: flex;
}
.vd-ui-radio-item.vd-ui-radio-item-checked .vd-ui-radio-icon {
  border-color: #0099FF;
}
.vd-ui-radio-item.vd-ui-radio-item-checked .vd-ui-radio-icon .vd-ui-radio-icon-selected {
  width: 6px;
  height: 6px;
}
.vd-ui-radio-item.vd-ui-radio-item-disabled {
  color: #999999;
  cursor: not-allowed;
}
.vd-ui-radio-item.vd-ui-radio-item-disabled .vd-ui-radio-icon {
  border-color: #D7DADE;
}
.vd-ui-radio-item.vd-ui-radio-item-disabled .vd-ui-radio-icon .vd-ui-radio-icon-selected {
  background: #D7DADE;
}
.vd-ui-radio-item.vd-ui-radio-item-labeltype .vd-ui-radio-icon {
  display: none;
}
.vd-ui-radio-item.vd-ui-radio-item-labeltype .vd-ui-radio-label {
  padding: 6px 10px;
  border-radius: 3px;
  background: #F5F7FA;
}
.vd-ui-radio-item.vd-ui-radio-item-labeltype.vd-ui-radio-item-checked .vd-ui-radio-label {
  background: #09f;
  color: #fff;
}
