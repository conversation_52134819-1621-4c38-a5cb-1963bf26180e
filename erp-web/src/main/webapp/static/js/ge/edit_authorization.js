$(function() {
	var parseNum = function(num){
		return num <= 9 ? '0' + num : num;
	};

	window.changeDate = function(item){
		var $parents = $(item).parents('.J-date-wrap:first');
		var $date2 = $parents.find('.J-date2');
		if($(item).val() && !$.trim($date2.val())){
			var date1Ms = new Date($.trim($(item).val())).valueOf();
			var date2Ms = date1Ms + 365 * 1000 * 60 * 60 * 24 * 5;
			var date2 = new Date(date2Ms);
			var date2Str = [date2.getFullYear(), parseNum(date2.getMonth() + 1), parseNum(date2.getDate())].join('-');
			$date2.val(date2Str);
		}
	}

	clickOne("twoMedicalType");
	clickOne("threeMedicalType");
	$("#file_1").change(function(){
		checkLogin();
		$("#uri_1").val($("#file_1").val());
	})

	
	$("#file_2").change(function(){
		checkLogin();
		$("#uri_2").val($("#file_2").val());
	})

	$("#file_3").change(function(){
		checkLogin();
		$("#uri_3").val($("#file_3").val());
	})

	$("#file_4").change(function(){
		checkLogin();
		$("#uri_4").val($("#file_4").val());
	})
	$("#file_5").change(function(){
		checkLogin();
		$("#uri_5").val($("#file_5").val());
	})


	var cansubmit = true;
	$("#submit").click(function() {
		checkLogin();

		if(checkUpContent()){
			if (cansubmit) {
				cansubmit = false;
				// $("form").attr("enctype", "multipart/form-data");
				/*var aaa = new FormData($("form")[0]);
				console.log(aaa)
				$.ajax({
					type: "POST",
					url: "/ge/authorization/saveAuthorization.do",
					data:  aaa,
					dataType:'json',
					contentType: false,
					processData:false,
					success: function(data){
						if (data.code == 0) {
							authorizationId=data.data;
							// debugger
							window.location.href='/ge/authorization/itemView.do?authorizationId='+authorizationId;
							// layer.alert("操作成功失败");
						} else {
							layer.confirm(
								"当前商机已存在未关闭的授权书",
								{btn:['跳转未关闭的授权书', '取消']},
								function (){
								window.location.href='/ge/authorization/itemView.do?authorizationId='+data.data;
							});
						}

					},
					error:function(data){
						if (data.status == 1001) {
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
						}
					}
				});*/
			} else {
			    console.log("请勿重复提交");
				return false;
			}
		}else{
			return false;
		}
	})
});
//删除
function delobj(obj){
	checkLogin();
	index = layer.confirm("您是否确认该操作？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
	$(obj).parent("li").remove();
	layer.close(index);
		}, function(){
		});
}

/*function add(obj){
	checkLogin();
	$(obj).parent("li").append('<div class="f_left bt-bg-style bt-middle bg-light-red ml8" onclick="delobj(this);">删除</div>');
	$(obj).parent("li").children(".add").remove();
	var num =$("select").length;
	$.ajax({
		url:page_url+'/trader/customer/getMedicalTypeByAjax.do',
		data:'',
		type:"POST",
		dataType : "json",
		async: false,
		success:function(data)
		{
			var medicalTypes=data.data;
			var medicalTypLevels=data.listData;
			var li='<li><select class="input-middle f_left" name="medicalType" onchange="changeType(this);"><option value="">请选择资质类别</option>';
			for(var i=0; i<medicalTypes.length;i++){
				li+='<option value="'+medicalTypes[i].sysOptionDefinitionId+'">'+medicalTypes[i].title+'</option>';
			}
			li+='</select><div class="f_left inputfloat mt4 ml8 meddiv" style="display: none">';
			for(var i=0; i<medicalTypLevels.length;i++){
				li+='<input type="radio" name="medicalTypLevel_'+(num+1)+'" value="'
						+medicalTypLevels[i].sysOptionDefinitionId+'"><label class="mr8">'+medicalTypLevels[i].title+'</label>';
			}
			li+='</div><div class="f_left bt-bg-style bt-middle bg-light-blue ml8 add" onclick="add(this);">添加</div></li>';
			$("#medical_ul").append(li);
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});

}*/

function changeType(obj){
	checkLogin();
	if($(obj).val()!='' && $(obj).val()!=194){
		$(obj).siblings(".meddiv").css("display","");
	}else{
		$(obj).siblings(".meddiv").css("display","none");
	}
}

function uploadFile(obj,num){
	checkLogin();
	var imgPath = $(obj).val();
	if(imgPath == '' || imgPath == undefined){
		return false;
	}
	var oldName=imgPath.substr(imgPath.lastIndexOf('\\')+1);
	var domain = $("#domain").val();
	//判断上传文件的后缀名
	var strExtension = imgPath.substr(imgPath.lastIndexOf('.') + 1);
	if (strExtension != 'jpg'  && strExtension != 'png'
		&& strExtension != 'JPG'  && strExtension != 'PNG' && strExtension !='PDF'&& strExtension !='pdf') {
		layer.alert("仅支持上传PDF，PNG，JPG格式，请重新选择文件");
		return false;
	}
	  var fileSize = 0;
	  var isIE = /msie/i.test(navigator.userAgent) && !window.opera;      
	  if (isIE && !obj.files) {     
	     var filePath = obj.value;      
	     var fileSystem = new ActiveXObject("Scripting.FileSystemObject");  
	     var file = fileSystem.GetFile (filePath);        
	     fileSize = file.Size;     
	  }else { 
	     fileSize = obj.files[0].size;   
	  } 
	  fileSize=Math.round(fileSize/1024*100)/100; //单位为KB
	  if(fileSize>10240){
		  layer.alert("上传的文件最大不允许超过10M，请压缩后上传");
	    return false;
	  }
	  $(obj).parent().parent().find("i").attr("class", "iconloading mt5").show();
	  $(obj).parent().parent().find("a").hide();
	  $(obj).parent().parent().find("span").hide();
	  var objCopy1 = $(obj).parent();
	  var objCopy2 = $(obj).parent().parent();
	$.ajaxFileUpload({
		url : page_url + '/fileUpload/uploadFile2Oss.do', //用于文件上传的服务器端请求地址
		secureuri : false, //一般设置为false
		fileElementId : $(obj).attr("id"), //文件上传控件的id属性  <input type="file" id="file" name="file" /> 注意，这里一定要有name值   //$("form").serialize(),表单序列化。指把所有元素的ID，NAME 等全部发过去
		dataType : 'json',//返回值类型 一般设置为json
		complete : function() {//只要完成即执行，最后执行
		},
		//服务器成功响应处理函数
		success : function(data) {
			if (data.code == 0) {

				objCopy1.find("input[type='text']").val(data.fileName);
				objCopy1.find("input[type='hidden']").val(data.filePath);
				$("#domain").val(data.httpUrl);
				objCopy2.find("i").attr("class", "iconsuccesss ml7").show();
				objCopy2.find("a").attr("href", 'http://' + data.httpUrl + data.filePath).show();
				objCopy2.find("span").show();
				debugger;
				if (num == 6) {
					$("#sy-message").css("display","none");
					// if((($("input[name='syName']").val()===undefined||$("input[name='syName']").val()===""))){
					// 	var a=false;
					// 	$("input[name='name_6']").each(function(){
					// 		// alert($(this).val());
					// 		if (($(this).val()!=undefined && $(this).val()!="")) {
					// 			a=true;
					// 		}
					// 	});
					// 	// alert(a)
					// 	if (!a) {
					// 		$("#sy-message").addClass("errorbor");
					// 		$("#sy-message").css("display","");
					// 	}else{
					//
					// 	}
					// }
				}

				if(num==1){
					$("#yy-message").css("display","none");
					// if((($("input[name='yyName']").val()==undefined||$("input[name='yyName']").val()==""))){
					// 	var a=false;
					// 	$("input[name='name_1']").each(function(){
					// 		// alert($(this).val());
					// 		if (($(this).val()!=undefined && $(this).val()!="")) {
					// 			a=true;
					// 		}
					// 	});
					// 	// alert(a)
					// 	if (!a) {
					// 		$("#yy-message").addClass("errorbor");
					// 		$("#yy-message").css("display","");
					// 		// layer.alert("请上传营业执照",function (index){
					// 		// 	layer.close(index);
					// 		// });
					// 	}else{
					//
					// 	}
					// }
				}

			} else {
				layer.alert("error"+data.message);
			}
		},
		//服务器响应失败处理函数
		error : function(data1, status, e) {

			if(data1.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}else{
				var data=JSON.parse(data1);
				if (data.code == 0) {

					objCopy1.find("input[type='text']").val(data.fileName);
					objCopy1.find("input[type='hidden']").val(data.filePath);
					$("#domain").val(data.httpUrl);
					objCopy2.find("i").attr("class", "iconsuccesss ml7").show();
					objCopy2.find("a").attr("href", 'http://' + data.httpUrl + data.filePath).show();
					objCopy2.find("span").show();
				} else {
					layer.alert(data.message);
				}
			}
			
		}
	});
	
}

//点击继续添加按钮
function con_add(id,desc){
	var rndNum = RndNum(8);
	var kind=0;

	kind=id;

	if($(".c_"+id)!=null) {
		var length = $(".c_" + id).length
		if (length > 4) {
			layer.alert("该种资质最多可上传5张，无法继续添加");
			return;
		}
	}
	var html = '<div class="c_'+kind+'">'+
					'<div class="pos_rel f_left mb8 ">'+
						'<input type="file" class=" uploadErp"  name="lwfile" id="lwfile_'+id+'_'+rndNum+'" onchange="uploadFile(this, '+id+');changeInfo('+id+')">'+
						'<input type="text" class="input-middle" style="margin-right:10px;" id="name_'+id+'_'+rndNum+'" readonly="readonly" placeholder="'+desc+'" name="name_'+id+'" onclick="lwfile_'+id+'_'+rndNum+'.click();" value ="">'+
					    '<input type="hidden" class="input-middle mr5" id="uri_'+id+'_'+rndNum+'" name="uri_'+id+'" value="" >'+
					    '<label class="bt-bg-style bt-middle bg-light-blue ml4" type="file" >浏览</label>'+
					'</div>'+
					'<div class="f_left ">'+
						'<i class="iconsuccesss mt5 none" id="img_icon_'+id+'"></i>'+
						'<a href="" target="_blank" class="font-blue cursor-pointer mr5 ml10 mt4 none" id="img_view_'+id+'" style="margin:0px 8px 0 13px;">查看</a>'+
						'<span class="font-red cursor-pointer mt4" onclick="delAttachment(this);changeInfo('+id+')" id="img_del_'+id+'">删除</span>'+
					'</div>'+
				'<div class="clear"></div></div>';
	$("#conadd"+id).before(html);
}

//其他继续添加按钮


function RndNum(n){
    var rnd="";
    for(var i=0;i<n;i++)
        rnd+=Math.floor(Math.random()*10);
    return rnd;
}

function delAttachment(obj) {
	var uri = $(obj).parent().find("a").attr("href");
	if (uri == '') {
		$(obj).parent().parent().remove();
	} else {
		index = layer.confirm("您是否确认该操作？", {
			  btn: ['确定','取消'] //按钮
			}, function(){
				var length = $(obj).parent().parent().parent().find("input[type='file']").length;
				if (length == 1) {
					$(obj).parent().find("i").hide();
					$(obj).parent().find("a").hide();
					$(obj).parent().find("span").hide();
					$(obj).parent().parent().parent().find("input[type='text']").val('');
				} else {
					$(obj).parent().parent().remove();
				}
				layer.close(index);
			}, function(){
			});
	}
}
function changeInfo(num){
	if (num == 9) {
		$("#change8").val(8);
	}
	if (num == 8) {
		$("#change7").val(7);
	}
	if (num == 7) {
		$("#change6").val(6);
	}
	if (num == 6) {
		$("#change5").val(5);

	}
	if (num == 5) {
		$("#change4").val(4);
	}
	if (num == 3) {
		$("#change3").val(3);
	}
	if(num == 2){
		$("#change2").val(2);
	}
	if(num == 1){
		$("#change1").val(1);

	}
	if (num == 0) {
		$("#change0").val(0);
		var type=$("#my-type").find("option:selected").val();
		// alert(medicalQualification)
		if (type == -1) {
			$("#type-message").addClass("errorbor");
			$("#type-message").css("display","");
			layer.alert("请选择产品类型",function (index){
				layer.close(index);
			});
		}else {
			$("#type-message").css("display","none");
		}
	}
	if (num == 11) {
		$("#change11").val(11);
	}
}
function del(num){
	checkLogin();
	index = layer.confirm("您是否确认该操作？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
			// var threeInOne=$('input:radio[name="threeInOne"]:checked').val();
			// var medicalQualification=$('input:radio[name="medicalQualification"]:checked').val();
			
			/*if( num ==1){
				$("#img_icon_" + 1).hide();
				$("#img_view_" + 1).hide();
				$("#img_del_" + 1).hide();
				$("#name_"+ 1).val("");
				$("#uri_"+ 1).val("");
				$("#busStartTime").val("");
				$("#busEndTime").val("");
				$("input[name='isMedical']").removeAttr("checked");
				$("#img_icon_" + 2).hide();
				$("#img_view_" + 2).hide();
				$("#img_del_" + 2).hide();
				$("#name_"+ 2).val("");
				$("#uri_"+ 2).val("");
				$("#taxStartTime").val("");
				$("#taxEndTime").val("");
				$("#img_icon_" + 3).hide();
				$("#img_view_" + 3).hide();
				$("#img_del_" + 3).hide();
				$("#name_"+ 3).val("");
				$("#uri_"+ 3).val("");
				$("#orgaStartTime").val("");
				$("#orgaEndTime").val("");
			}else if(threeInOne ==0){
				$("#img_icon_" + num).hide();
				$("#img_view_" + num).hide();
				$("#img_del_" + num).hide();
				$("#name_"+ num).val("");
				$("#uri_"+ num).val("");
				if(num==1){
					$("#busStartTime").val("");
					$("#busEndTime").val("");
					$("input[name='isMedical']").removeAttr("checked");
				}else if(num==2){
					$("#taxStartTime").val("");
					$("#taxEndTime").val("");
				}else if(num==3){
					$("#orgaStartTime").val("");
					$("#orgaEndTime").val("");
				}
				
			}
			var customerProperty = $("#customerProperty").val();*/
			if(num==11){
				$("#img_icon_" + num).hide();
				$("#img_view_" + num).hide();
				$("#img_del_" + num).hide();
				$("#name_"+ num).val("");
				$("#uri_"+ num).val("");
				// $("#yybusUri").val("");
				// $("input[name='yyName']").val("");
			}


			$("#img_icon_" + num).hide();
			$("#img_view_" + num).hide();
			$("#img_del_" + num).hide();
			$("#name_"+ num).val("");
			$("#uri_"+ num).val("");

			layer.close(index);
		}, function(){
		});
}
function custom_close(){
	window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'),window.parent.closableTab.resizeMove);
}
// function medOne(){
// 	checkLogin();
// 	$("#img_del_5").removeClass("cursor-pointer");
// 	$("#img_del_5").prop("onclick",null).off("click");
// 	$("#threeStartTime").attr("disabled","disabled");
// 	$("#threeEndTime").attr("disabled","disabled");
// 	$("#threeMeicalAdd").attr("disabled","disabled")
// //	$("input[name='threeMedicalType']").attr("disabled","disabled");
// //	$.each($("input[name='threeMedicalType']"),function(i,n){
// //		if($(this).prop("checked")){
// //			$(this).prop("checked", false);
// //		}
// //	});
// }
function medZero(){
	checkLogin();
	$("#img_del_5").addClass("cursor-pointer");
	$("#img_del_5").attr("onclick","del(5)");
	$("#threeStartTime").removeAttr("disabled");  
	$("#threeEndTime").removeAttr("disabled"); 
	$("input[name='threeMedicalType']").removeAttr("disabled");
	
}
function thOne(){
	checkLogin();
	$("#img_del_2").removeClass("cursor-pointer");
	$("#img_del_2").prop("onclick",null).off("click");
	$("#taxStartTime").attr("disabled","disabled");
	$("#taxEndTime").attr("disabled","disabled");
	$("#img_del_3").removeClass("cursor-pointer");
	$("#img_del_3").prop("onclick",null).off("click");
	$("#orgaStartTime").attr("disabled","disabled");
	$("#orgaEndTime").attr("disabled","disabled");
	
	
}
function thZero(){
	checkLogin();
	$("#img_del_2").addClass("cursor-pointer");
	$("#img_del_2").attr("onclick","del(2)");
	$("#taxStartTime").removeAttr("disabled");  
	$("#taxEndTime").removeAttr("disabled"); 
	$("#img_del_3").addClass("cursor-pointer");
	$("#img_del_3").attr("onclick","del(3)");
	$("#orgaStartTime").removeAttr("disabled");  
	$("#orgaEndTime").removeAttr("disabled"); 
}
//全选
function clickAll(type){
	//全选
	 $("input[name="+type+"All]").click(function () {
       var thisChecked = $(this).prop('checked');
       $('input[name='+type+']').prop('checked',thisChecked);
     })
     var thisChecked = $("input[name="+type+"All]").prop('checked');
	 $('input[name='+type+']').prop('checked',thisChecked);
}
//单选
function clickOne(type){
	var num = $('input[name='+type+']:checked').length;
    var sum = $('input[name='+type+']').length;
   if(num == sum){
       $('input[name='+type+'All]').prop('checked',true);
   }else{
        $('input[name='+type+'All]').prop('checked', false);
   }
}

function closeTab(){
	$('#myform').submit();
	// pagesContrlpages(true, null, true);
}

function checkUpContent() {

	var type=$("#my-type").find("option:selected").val();
	// alert(medicalQualification)
	if (type == -1) {
		$("#type-message").addClass("errorbor");
		$("#type-message").css("display","");
		layer.alert("请选择产品类型",function (index){
			layer.close(index);
		});
		return false;
	}
	if((($("input[name='yyName']").val()==undefined||$("input[name='yyName']").val()==""))){
		var a=false;
		$("input[name='name_1']").each(function(){
			// alert($(this).val());
			if (($(this).val()!=undefined && $(this).val()!="")) {
				a=true;
			}
		});
		// alert(a)
		if (!a) {
			$("#yy-message").addClass("errorbor");
			$("#yy-message").css("display","");
			layer.alert("请上传营业执照",function (index){
				layer.close(index);
			});
			return false;
		}
	}

	if((($("input[name='syName']").val()===undefined||$("input[name='syName']").val()===""))){
		var a=false;
		$("input[name='name_6']").each(function(){
			// alert($(this).val());
			if (($(this).val()!=undefined && $(this).val()!="")) {
				a=true;
			}
		});
		// alert(a)
		if (!a) {
			$("#sy-message").addClass("errorbor");
			$("#sy-message").css("display","");
			layer.alert("请上传使用二级分销商声明",function (index){
				layer.close(index);
			});
			return false;
		}
	}
    // alert($("textArea[name='content']").val().length)
	if($("textArea[name='content']").val().length>200){
		$("#content-message").addClass("errorbor");
		$("#content-message").css("display","");
		return false;
	}

	var testReg =/^[0-9a-zA-Z]{1,128}$/;
	return true;
}

