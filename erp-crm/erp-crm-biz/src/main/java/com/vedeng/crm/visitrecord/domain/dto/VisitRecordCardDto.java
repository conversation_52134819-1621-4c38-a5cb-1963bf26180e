package com.vedeng.crm.visitrecord.domain.dto;

import lombok.Data;

/**
 * @Description 拜访打卡DTO
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/1
 */
@Data
public class VisitRecordCardDto {
    
    /** 关联表的ID */
    private Integer recordId;
    
    /** 打卡人ID */
    private Integer visitUserId;
    
    /** 拍照的照片链接以逗号拼接 */
    private String cardPhotoUrls;
    
    /** 打卡位置x,y */
    private String cardLocation;
} 