package com.vedeng.system.controller;

import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.system.dao.ListConfigMapper;
import com.vedeng.system.model.ListConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date created in 2020/5/26 10:33
 */
@Controller
@RequestMapping("/system/list")
public class ListConfigController {

    @Autowired
    private ListConfigMapper listConfigMapper;


    @RequestMapping("/page")
    public ModelAndView getListConfigurePage(@RequestParam String columnList,
                                             @RequestParam String columnListHidden,
                                             @RequestParam String searchList,
                                             @RequestParam String searchListHidden){
        ModelAndView mv = new ModelAndView("system/listconfig/list_configure");
        mv.addObject("columnList",columnList);
        mv.addObject("searchList",searchList);
        mv.addObject("columnListHidden",columnListHidden);
        mv.addObject("searchListHidden",searchListHidden);
        return mv;
    }

    @RequestMapping(value = "/configure", method = RequestMethod.GET)
    @ResponseBody
    public ResultInfo<ListConfig> getListConfigurationInfo(@RequestParam String listName,
                                                           HttpSession session){
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        ListConfig listConfig = listConfigMapper.getListConfigByUserAndListName(user.getUserId(),listName);
        ResultInfo<ListConfig> resultInfo = new ResultInfo<>();
        resultInfo.setCode(0);
        resultInfo.setData(listConfig);
        resultInfo.setMessage("获取配置信息成功");
        return resultInfo;
    }


    @RequestMapping(value = "save",method = RequestMethod.POST)
    @ResponseBody
    public ResultInfo<Void> saveListConfigurationInfo(ListConfig listConfig, HttpSession session){
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        listConfig.setUserId(user.getUserId());
        ResultInfo<Void> resultInfo = new ResultInfo<>();
        if (listConfig.getListName() == null){
            resultInfo.setCode(-1);
            resultInfo.setMessage("参数异常");
            return resultInfo;
        }
        if (StringUtils.isBlank(listConfig.getColumnList()) && StringUtils.isBlank(listConfig.getSearchList())){
            //删除之前存在的配置
            listConfigMapper.deleteListConfigByUserAndListName(user.getUserId(),listConfig.getListName());
        } else {
            if (listConfigMapper.getListConfigByUserAndListName(user.getUserId(),listConfig.getListName()) == null){
                listConfigMapper.saveListConfig(listConfig);
            } else {
                listConfigMapper.updateListConfigByUserAndListName(listConfig);
            }
        }
        resultInfo.setCode(0);
        resultInfo.setMessage("保存成功");
        return resultInfo;
    }
}
