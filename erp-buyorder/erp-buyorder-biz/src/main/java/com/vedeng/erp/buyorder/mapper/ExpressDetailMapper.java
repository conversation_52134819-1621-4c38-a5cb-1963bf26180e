package com.vedeng.erp.buyorder.mapper;
import java.util.Collection;

import com.vedeng.erp.buyorder.domain.entity.ExpressDetail;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;

@Named("buyorderExpressDetailMapper")
public interface ExpressDetailMapper {

    int deleteByPrimaryKey(Integer expressDetailId);

    int insert(ExpressDetail record);

    int insertSelective(ExpressDetail record);

    ExpressDetail selectByPrimaryKey(Integer expressDetailId);

    int updateByPrimaryKeySelective(ExpressDetail record);

    int updateByPrimaryKey(ExpressDetail record);

    List<ExpressDetail> getDeliveryDirectExpressInfo(@Param("expressIdList") List<Integer> expressIdList);

    Long getBuyorderExpressInfo(@Param("buyorderId") Integer buyorderId);
}
