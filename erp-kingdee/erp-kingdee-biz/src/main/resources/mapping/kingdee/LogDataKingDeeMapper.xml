<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.LogDataKingDeeMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.LogDataKingDee">
    <!--@mbg.generated-->
    <!--@Table V_LOG_DATA_KING_DEE-->
    <id column="LOG_DATA_ID" jdbcType="BIGINT" property="logDataId" />
    <result column="OUT_IN_NO" jdbcType="VARCHAR" property="outInNo" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="ASS_ORDER_NO" jdbcType="VARCHAR" property="assOrderNo" />
    <result column="ORDER_TYPE" jdbcType="VARCHAR" property="orderType" />
    <result column="LOG_TYPE" jdbcType="INTEGER" property="logType" />
    <result column="REAL_PRICE" jdbcType="DECIMAL" property="realPrice" />
    <result column="REAL_TOTAL_AMOUNT" jdbcType="DECIMAL" property="realTotalAmount" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    LOG_DATA_ID, OUT_IN_NO, GOODS_ID, ORDER_NO, ASS_ORDER_NO, ORDER_TYPE, LOG_TYPE, REAL_PRICE, 
    REAL_TOTAL_AMOUNT, IS_DELETE, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from V_LOG_DATA_KING_DEE
    where LOG_DATA_ID = #{logDataId,jdbcType=BIGINT}
  </select>
  <select id="selectByOutInNoAndGoodsIdAndOrderType" parameterType="com.vedeng.erp.kingdee.domain.entity.LogDataKingDee"
          resultType="com.vedeng.erp.kingdee.domain.entity.LogDataKingDee">
#     根据outInNo和goodsId和orderType查询
    select
    <include refid="Base_Column_List" />
    from V_LOG_DATA_KING_DEE
    where OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
    AND GOODS_ID = #{goodsId,jdbcType=INTEGER}
    AND ORDER_TYPE = #{orderType,jdbcType=VARCHAR}


  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from V_LOG_DATA_KING_DEE
    where LOG_DATA_ID = #{logDataId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="LOG_DATA_ID" keyProperty="logDataId" parameterType="com.vedeng.erp.kingdee.domain.entity.LogDataKingDee" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_LOG_DATA_KING_DEE (OUT_IN_NO, GOODS_ID, ORDER_NO, 
      ASS_ORDER_NO, ORDER_TYPE, LOG_TYPE, 
      REAL_PRICE, REAL_TOTAL_AMOUNT, IS_DELETE, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER)
    values (#{outInNo,jdbcType=VARCHAR}, #{goodsId,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR}, 
      #{assOrderNo,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, #{logType,jdbcType=INTEGER}, 
      #{realPrice,jdbcType=DECIMAL}, #{realTotalAmount,jdbcType=DECIMAL}, #{isDelete,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="LOG_DATA_ID" keyProperty="logDataId" parameterType="com.vedeng.erp.kingdee.domain.entity.LogDataKingDee" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_LOG_DATA_KING_DEE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="outInNo != null">
        OUT_IN_NO,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="orderNo != null">
        ORDER_NO,
      </if>
      <if test="assOrderNo != null">
        ASS_ORDER_NO,
      </if>
      <if test="orderType != null">
        ORDER_TYPE,
      </if>
      <if test="logType != null">
        LOG_TYPE,
      </if>
      <if test="realPrice != null">
        REAL_PRICE,
      </if>
      <if test="realTotalAmount != null">
        REAL_TOTAL_AMOUNT,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="outInNo != null">
        #{outInNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="assOrderNo != null">
        #{assOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="logType != null">
        #{logType,jdbcType=INTEGER},
      </if>
      <if test="realPrice != null">
        #{realPrice,jdbcType=DECIMAL},
      </if>
      <if test="realTotalAmount != null">
        #{realTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.LogDataKingDee">
    <!--@mbg.generated-->
    update V_LOG_DATA_KING_DEE
    <set>
      <if test="outInNo != null">
        OUT_IN_NO = #{outInNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="assOrderNo != null">
        ASS_ORDER_NO = #{assOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="logType != null">
        LOG_TYPE = #{logType,jdbcType=INTEGER},
      </if>
      <if test="realPrice != null">
        REAL_PRICE = #{realPrice,jdbcType=DECIMAL},
      </if>
      <if test="realTotalAmount != null">
        REAL_TOTAL_AMOUNT = #{realTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where LOG_DATA_ID = #{logDataId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.LogDataKingDee">
    <!--@mbg.generated-->
    update V_LOG_DATA_KING_DEE
    set OUT_IN_NO = #{outInNo,jdbcType=VARCHAR},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      ASS_ORDER_NO = #{assOrderNo,jdbcType=VARCHAR},
      ORDER_TYPE = #{orderType,jdbcType=VARCHAR},
      LOG_TYPE = #{logType,jdbcType=INTEGER},
      REAL_PRICE = #{realPrice,jdbcType=DECIMAL},
      REAL_TOTAL_AMOUNT = #{realTotalAmount,jdbcType=DECIMAL},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER}
    where LOG_DATA_ID = #{logDataId,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="LOG_DATA_ID" keyProperty="logDataId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_LOG_DATA_KING_DEE
    (OUT_IN_NO, GOODS_ID, ORDER_NO, ASS_ORDER_NO, ORDER_TYPE, LOG_TYPE, REAL_PRICE, REAL_TOTAL_AMOUNT, 
      IS_DELETE, ADD_TIME, CREATOR, MOD_TIME, UPDATER)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.outInNo,jdbcType=VARCHAR}, #{item.goodsId,jdbcType=INTEGER}, #{item.orderNo,jdbcType=VARCHAR}, 
        #{item.assOrderNo,jdbcType=VARCHAR}, #{item.orderType,jdbcType=VARCHAR}, #{item.logType,jdbcType=INTEGER}, 
        #{item.realPrice,jdbcType=DECIMAL}, #{item.realTotalAmount,jdbcType=DECIMAL}, #{item.isDelete,jdbcType=INTEGER},
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=TIMESTAMP}, 
        #{item.updater,jdbcType=INTEGER})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-05-26-->
  <select id="findByOutInNoAndGoodsIdAndOrderType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from V_LOG_DATA_KING_DEE
    where OUT_IN_NO=#{outInNo,jdbcType=VARCHAR} and GOODS_ID=#{goodsId,jdbcType=INTEGER} and
    ORDER_TYPE=#{orderType,jdbcType=VARCHAR}
  </select>
</mapper>