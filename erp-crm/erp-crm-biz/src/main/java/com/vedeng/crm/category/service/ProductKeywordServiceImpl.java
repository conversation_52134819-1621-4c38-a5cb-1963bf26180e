package com.vedeng.crm.category.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.base.R;
import com.vedeng.crm.api.ProductKeywordApiService;
import com.vedeng.crm.dto.CategoryMatchDto;
import com.vedeng.crm.feign.category.CategoryThreeApiService;
import com.vedeng.crm.feign.goods.GoodsSelectSearchApiService;
import com.vedeng.crm.feign.goods.dto.GoodsSearchReqDto;
import com.vedeng.crm.feign.gpt.ChatGptApiService;
import com.vedeng.goods.dto.BusinessOrderCategoryDto;
import com.vedeng.goods.service.BusinessOrderCategoryApiService;
import com.vedeng.voice.api.response.ResponeInfo;
import com.vedeng.voice.domain.voicetask.common.chat.ChatCompletion;
import com.vedeng.voice.domain.voicetask.common.chat.ChatCompletionResponse;
import com.vedeng.voice.domain.voicetask.common.chat.Message;
import com.vedeng.voice.domain.voicetask.common.chat.base.BaseChatCompletion;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class ProductKeywordServiceImpl implements ProductKeywordApiService {
    private static final Logger logger = LoggerFactory.getLogger(ProductKeywordServiceImpl.class);

    private static final String SUCCESS = "SUCCESS";
    private static final int MAX_CATEGORIES = 10;
    private static final String SALE_COUNT = "saleCountOfLastYear";
    private static final String CATEGORY_NAME = "category";
    private static final String CHECK_STATUS = "checkStatus";
    private static final String APPROVED_STATUS = "审核通过";

    private static final Pattern NUMBER_PATTERN = Pattern.compile("\\d+");

    @Value("${product.keyword.system.prompt}")
    private String SYSTEM_PROMPT;


    @Autowired
    private GoodsSelectSearchApiService goodsSelectSearchApiService;

    @Autowired
    private ChatGptApiService chatGptApiService;

    @Autowired
    private CategoryThreeApiService categoryThreeApiService;

    @Autowired
    private BusinessOrderCategoryApiService businessOrderCategoryApiService;


    /**
     * 提取产品关键词
     *
     * @param input 用户输入内容
     * @return 提取的关键词列表
     */
    @Override
    public List<String> extractKeywords(String input) {
        if (input == null || input.trim().isEmpty()) {
            logger.warn("输入内容为空");
            return new ArrayList<>();
        }

        // 检查输入是否仅为数字或单个字符，如果是则直接返回空列表
        if (input.trim().matches("^\\d+$") || input.trim().length() <= 1) {
            logger.info("输入内容仅为数字或单个字符，跳过AI处理: {}", input);
            return new ArrayList<>();
        }

        try {
            Message messageSystem = Message.ofSystem(SYSTEM_PROMPT);
            Message messageUser = Message.of(input);

            ChatCompletion chatCompletion = ChatCompletion
                    .builder()
                    .model(BaseChatCompletion.Model.DEEPSEEK_CHAT.getName())
                    .messages(Arrays.asList(messageSystem, messageUser))
                    .build();

            long startTime = System.currentTimeMillis();
            ResponeInfo response = chatGptApiService.completions(chatCompletion);
            long endTime = System.currentTimeMillis();
            logger.info("ChatGPT API调用耗时: {}ms", (endTime - startTime));

            if (response == null || !SUCCESS.equals(response.getMessage()) || response.getData() == null) {
                logger.error("ChatGPT API调用失败: {}", response != null ? response.getMessage() : "response is null");
                return new ArrayList<>();
            }

            ChatCompletionResponse chatCompletionResponse = JSON.parseObject(
                    JSON.toJSONString(response.getData()),
                    ChatCompletionResponse.class
            );

            if (chatCompletionResponse == null ||
                    CollectionUtils.isEmpty(chatCompletionResponse.getChoices())) {
                logger.warn("ChatGPT响应内容为空");
                return new ArrayList<>();
            }

            String responseBody = chatCompletionResponse.getChoices().get(0).getMessage().getContent();
            return parseKeywords(responseBody);
        } catch (Exception e) {
            logger.error("提取关键词过程中发生异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 解析ChatGPT响应，提取关键词
     */
    private List<String> parseKeywords(String response) {
        if (response == null || response.trim().isEmpty()) {
            return new ArrayList<>();
        }

        List<String> keywords = new ArrayList<>();
        String[] keywordArray = response.split(",");
        for (String keyword : keywordArray) {
            String trimmedKeyword = keyword.trim();
            if (!trimmedKeyword.isEmpty() && !NUMBER_PATTERN.matcher(trimmedKeyword).matches()) {
                keywords.add(trimmedKeyword);
            }
        }

        return keywords;
    }


    /**
     * 匹配商品分类
     *
     * @param keywords 关键词列表
     * @return 匹配的商品分类列表
     */
    @Override
    public List<CategoryMatchDto.CategoryMatch> matchCategories(List<String> keywords) {
        if (CollectionUtils.isEmpty(keywords)) {
            logger.warn("关键词列表为空");
            return new ArrayList<>();
        }

        long totalStartTime = System.currentTimeMillis();
        List<CompletableFuture<List<CategoryMatchDto.CategoryMatch>>> futures = new ArrayList<>();

        // 为每个关键词创建一个异步任务
        for (String keyword : keywords) {
            CompletableFuture<List<CategoryMatchDto.CategoryMatch>> future = CompletableFuture.supplyAsync(() -> {
                List<CategoryMatchDto.CategoryMatch> results = new ArrayList<>();
                try {
                    GoodsSearchReqDto searchReq = new GoodsSearchReqDto();
                    searchReq.setKeywords(keyword);
                    //searchReq.setSortColoum(SALE_COUNT);
                    //searchReq.setSortAsc("1");

                    long startTime = System.currentTimeMillis();
                    R response = goodsSelectSearchApiService.searchForApi(searchReq);
                    long endTime = System.currentTimeMillis();
                    logger.info("商品搜索API调用耗时: {}ms, 关键词: {}", (endTime - startTime), keyword);

                    if (response != null && response.getCode() == 0 && response.getData() != null) {
                        Map<String, Object> map = (Map<String, Object>) response.getData();
                        logger.info("商品搜索API响应: {}", JSON.toJSONString(map));
                        List<Map<String, Object>> goodsList = (List<Map<String, Object>>) map.get("goodsSearchInfoList");

                        if (!CollectionUtils.isEmpty(goodsList)) {
                            results = goodsList.stream()
                                    // 只保留审核通过的商品
                                    .filter(goods -> APPROVED_STATUS.equals(goods.get(CHECK_STATUS)))
                                    // 只保留销量大于0的商品
                                    .filter(goods -> {
                                        Object saleCount = goods.get(SALE_COUNT);
                                        return saleCount != null && ((Number) saleCount).intValue() > 0;
                                    })
                                    .map(goods -> (String) goods.get(CATEGORY_NAME))
                                    .filter(category -> category != null && !category.isEmpty())
                                    .distinct()
                                    .map(category -> {
                                        CategoryMatchDto.CategoryMatch categoryMatch = new CategoryMatchDto.CategoryMatch();
                                        categoryMatch.setCategory(category);
                                        categoryMatch.setSubCategoryId(getSubCategoryId(category));
                                        return categoryMatch;
                                    })
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList());
                        }
                    }
                } catch (Exception e) {
                    logger.error("处理关键词[{}]时发生异常", keyword, e);
                }
                return results;
            });
            futures.add(future);
        }

        // 等待所有异步任务完成并合并结果
        List<CategoryMatchDto.CategoryMatch> matchedCategories = futures.stream()
                .map(future -> {
                    try {
                        return future.get(10, TimeUnit.SECONDS); // 设置10秒超时
                    } catch (Exception e) {
                        logger.error("获取异步任务结果异常", e);
                        return Collections.<CategoryMatchDto.CategoryMatch>emptyList();
                    }
                })
                .flatMap(Collection::stream)
                .distinct()
                .limit(MAX_CATEGORIES)
                .collect(Collectors.toList());

        long totalEndTime = System.currentTimeMillis();
        logger.info("商品分类匹配总耗时: {}ms, 关键词数量: {}", (totalEndTime - totalStartTime), keywords.size());

        return matchedCategories;
    }

    private Integer getSubCategoryId(String fullCategoryPath) {
        long startTime = System.currentTimeMillis();
        R<Integer> response = categoryThreeApiService.getCategoryIdByFullPath(fullCategoryPath);
        long endTime = System.currentTimeMillis();
        logger.info("获取分类ID API调用耗时: {}ms, 分类路径: {}", (endTime - startTime), fullCategoryPath);

        if (response != null && response.getCode() == 0) {
            return response.getData();
        }
        logger.warn("无法获取分类ID: {}", fullCategoryPath);
        return 0;
    }

    /**
     * 根据业务ID和业务类型获取三级分类和关键词
     *
     * @param businessId   业务ID
     * @param businessType 业务类型 0线索 1商机
     * @return 包含关键词和分类信息的结果
     */
    public CategoryMatchDto getCategoryByBusiness(Integer businessId, Integer businessType) {
        CategoryMatchDto result = new CategoryMatchDto();

        try {
            long startTime = System.currentTimeMillis();
            // 查询业务关联的分类信息
            List<BusinessOrderCategoryDto> byBusinessIdAndType = businessOrderCategoryApiService.getByBusinessIdAndType(businessId, businessType);
            long endTime = System.currentTimeMillis();
            logger.info("获取业务分类信息API调用耗时: {}ms, 业务ID: {}, 业务类型: {}", (endTime - startTime), businessId, businessType);

            if (CollUtil.isNotEmpty(byBusinessIdAndType)) {
                String keywords = byBusinessIdAndType.get(0).getKeywords();
                // 将逗号分隔的关键词转换为列表
                if (StrUtil.isNotEmpty(keywords)) {
                    List<String> keywordList = Arrays.asList(keywords.split(","));
                    result.setKeywords(keywordList);
                }

                List<CategoryMatchDto.CategoryMatch> matchedCategories = new ArrayList<>();
                // 获取分类名称
                byBusinessIdAndType.forEach(categoryEntity -> {
                    CategoryMatchDto.CategoryMatch categoryMatch = new CategoryMatchDto.CategoryMatch();
                    categoryMatch.setSubCategoryId(categoryEntity.getCategoryId());

                    long catStartTime = System.currentTimeMillis();
                    R<String> fullPathNameById = categoryThreeApiService.getFullPathNameById(categoryEntity.getCategoryId());
                    long catEndTime = System.currentTimeMillis();
                    logger.info("获取分类全路径API调用耗时: {}ms, 分类ID: {}", (catEndTime - catStartTime), categoryEntity.getCategoryId());

                    if (fullPathNameById != null && fullPathNameById.getCode() == 0) {
                        categoryMatch.setCategory(fullPathNameById.getData());
                    }
                    matchedCategories.add(categoryMatch);
                });
                result.setMatchedCategories(matchedCategories);
            }
        } catch (Exception e) {
            logger.error("获取业务分类信息异常", e);
        }

        return result;
    }
}
