package com.vedeng.erp.trader.service;

import java.math.BigDecimal;
import java.util.List;

import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.system.dto.LikeTraderDto;
import com.vedeng.erp.trader.dto.TraderBelongDto;
import com.vedeng.erp.trader.dto.TraderCustomerDto;

/**
 * <AUTHOR>
 * @description 客户信息
 * @date 2022/9/7 18:49
 **/
public interface TraderCustomerApiService {

    /**
     * 指定范围内新增审核通过的客户信息
     * @param begin
     * @param end
     * @return List<TraderCustomerDto>
     */
    List<TraderCustomerDto> selectPushKingDeeTraderCustomerData(Long begin, Long end,Integer limit);

    /**
     * 指定客户id内的信息
     * @param ids
     * @return List<TraderCustomerDto>
     */
    List<TraderCustomerDto> selectPushKingDeeTraderCustomerDataByIds(List<Integer> ids);

    /**
     * <AUTHOR>
     * @desc 查询资质审核通过的客户信息
     * @param begin
     * @param end
     * @param limit
     * @return
     */
    List<TraderCustomerDto> selectPushKingDeeTraderCustomerlicenceData(Long begin, Long end,Integer limit);

    /**
     * 2021年7月1日至今的所有已交易客户（有交易流水）的客户
     * <AUTHOR>
     * @return
     */
    List<TraderCustomerDto> queryHaveCapitalTraderCustomer();

    /**
     * 查询并组装金蝶客户信息
     *
     * @param traderCustomerId   客户id
     * @return KingDeeCustomerDto
     */
    KingDeeCustomerDto getKingDeeCustomerInfo(Integer traderCustomerId);

    /**
     * 根据traderId查询客户信息
     *
     * @param traderId traderId
     * @return TraderCustomerDto
     */
    TraderCustomerDto getTraderCustomerInfoByTraderId(Integer traderId);
    List<TraderCustomerDto> getTraderCustomerInfoByTraderIds(List<Integer> traderId);

    /**
     * 查询客户资质信息
     * @param traderId
     * @return
     */
    TraderCustomerDto getTraderCustomerAptitudeInfoByTraderId(Integer traderId);

    /**
     * 根据付款申请id查询客户信息
     * @param payApplyId
     * @return
     */
    TraderCustomerDto getTraderByPayApply(Integer payApplyId);

    /**
     * 更新客户交易额
     * @param traderId
     * @param multiply
     */
    void updateTraderAmount(Integer traderId, BigDecimal multiply);

    /**
     * 下拉框远程搜索，限制100条
     * @param keywords
     * @return
     */
    List<LikeTraderDto> getTrader( String keywords);

    String setShowInvalidReason( String invalidReason , String otherReason);

    List<TraderBelongDto> getTraderBelongInfo(List<String> traderNameList);

    List<TraderBelongDto> getTraderBelongInfoById(Integer traderId);

    /**
     * 根据traderIds查询客户信息
     * @param traderIds
     * @return
     */
    List<TraderCustomerDto> getTraderCustomerListByTraderIds(List<Integer> traderIds);

}
