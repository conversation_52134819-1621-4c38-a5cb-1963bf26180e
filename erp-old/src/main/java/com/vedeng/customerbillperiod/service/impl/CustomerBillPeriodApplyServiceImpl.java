package com.vedeng.customerbillperiod.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.annotation.MethodLockParam;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodApplyCheckStatusEnum;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodApplyTypeEnum;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodTypeEnum;
import com.vedeng.customerbillperiod.dao.CustomerBillPeriodMapper;
import com.vedeng.customerbillperiod.dao.CustomerBillPeriodUseDetailMapper;
import com.vedeng.customerbillperiod.dto.*;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.customerbillperiod.dao.CustomerBillPeriodApplyMapper;
import com.vedeng.customerbillperiod.model.CustomerBillPeriod;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodApply;
import com.vedeng.customerbillperiod.service.CustomerBillPeriodApplyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: daniel
 * @Date: 2021/7/26 15 10
 * @Description:
 */
@Service
public class CustomerBillPeriodApplyServiceImpl implements CustomerBillPeriodApplyService {

    private static final Logger logger = LoggerFactory.getLogger(CustomerBillPeriodApplyServiceImpl.class);

    @Resource
    private CustomerBillPeriodApplyMapper customerBillPeriodApplyMapper;

    @Resource
    private CustomerBillPeriodMapper customerBillPeriodMapper;

    @Resource
    private CustomerBillPeriodUseDetailMapper customerBillPeriodUseDetailMapper;


    @Override
    public CustomerBillPeriodApply selectByPrimaryKey(Long id) {
        if (id == null) {
            return null;
        }
        return customerBillPeriodApplyMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<CustomerBillPeriodApply> getCustomerBillPeriodApplyList(Integer companyId, Long customerId) {
        List<CustomerBillPeriodApply> applyList = customerBillPeriodApplyMapper.getApplyListByCustomerId(companyId,customerId);
        if (CollectionUtils.isEmpty(applyList)){
            return Collections.emptyList();
        }
        return applyList;
    }



    @Override
    public  List<CustomerBillPeriodApply> getCustomerBillPeriodApplyListByPage( Integer customerId, com.vedeng.common.page.Page page) {
        // 账期
        Map<String, Object> billMap = new HashMap<String, Object>();
        billMap.put("page", page);
        billMap.put("customerId", customerId);
        List<CustomerBillPeriodApply> applyList = customerBillPeriodApplyMapper.getApplyTraderAmountBillVolistpage(billMap);
        return applyList;
    }

    @Override
    public Long saveCustomerBillPeriodApply(CustomerBillPeriodApplyDto applyDto) throws CustomerBillPeriodException {
        logger.info("客户账期申请：{}", applyDto.toString());

        //同一时间只能有一个客户账期申请
        List<CustomerBillPeriodApply> customerBillPeriodApplyListInChecking =
                customerBillPeriodApplyMapper.getApplyListByCustomerId(applyDto.getCompanyId(), applyDto.getCustomerId())
                .stream()
                .filter(item -> CustomerBillPeriodApplyCheckStatusEnum.IN_CHECK.getCode().equals(item.getCheckStatus()))
                .collect(Collectors.toList());
        if (customerBillPeriodApplyListInChecking.size() > 0) {
            throw new CustomerBillPeriodException("保存客户账期申请失败，同一时间只能有一个账期申请");
        }
        //客户所有的账期（包含失效的）
        List<CustomerBillPeriod> customerBillPeriodList =
                customerBillPeriodMapper.getCustomerBillPeriodListByCustomerId(applyDto.getCompanyId(),
                        applyDto.getCustomerId());
        if (CustomerBillPeriodApplyTypeEnum.MODIFY.getCode().equals(applyDto.getOperateType()) && applyDto.getBillPeriodId() == null){
            throw new CustomerBillPeriodException("保存客户账期申请参数有误");
        }

        switch (CustomerBillPeriodTypeEnum.getTypeEnumByCode(applyDto.getBillPeriodType())){
            case OFFICIAL:
                checkOfficialCustomerBillPeriodApply(applyDto,customerBillPeriodList);
                break;
            case TEMPORARY:
                checkTemporaryCustomerBillPeriodApply(applyDto,customerBillPeriodList);
                break;
            case ORDER:
                checkOrderCustomerBillPeriodApply(applyDto,customerBillPeriodList);
                break;
            default:
                break;
        }

        CustomerBillPeriodApply customerBillPeriodApply = new CustomerBillPeriodApply();
        BeanUtils.copyProperties(applyDto,customerBillPeriodApply);
        if (CustomerBillPeriodApplyTypeEnum.MODIFY.getCode().equals(applyDto.getOperateType())){
            CustomerBillPeriod beforeBillPeriod = customerBillPeriodMapper.selectByPrimaryKey(applyDto.getBillPeriodId());
            if (beforeBillPeriod == null) {
                throw new CustomerBillPeriodException("保存客户账期申请错误，修改的账期不存在");
            }
            customerBillPeriodApply.setBeforeApplyAmount(beforeBillPeriod.getApplyAmount());
            customerBillPeriodApply.setBeforeBillPeriodStart(beforeBillPeriod.getBillPeriodStart());
            customerBillPeriodApply.setBeforeBillPeriodEnd(beforeBillPeriod.getBillPeriodEnd());
            customerBillPeriodApply.setBeforeSettlementPeriod(beforeBillPeriod.getSettlementPeriod());
        }
        customerBillPeriodApply.setAddTime(System.currentTimeMillis());
        customerBillPeriodApply.setCheckStatus(CustomerBillPeriodApplyCheckStatusEnum.IN_CHECK.getCode());
        customerBillPeriodApplyMapper.insertSelective(customerBillPeriodApply);
        logger.info("保存客户账期申请：{}",customerBillPeriodApply.toString());

        return customerBillPeriodApply.getBillPeriodApplyId();
    }

    @Override
    public void modifyCustomerBillPeriodApply(CustomerBillPeriodApplyModifyDto customerBillPeriodApplyModifyDto) throws CustomerBillPeriodException {
        logger.info("修改客户账期申请，{}",customerBillPeriodApplyModifyDto.toString());
        if (customerBillPeriodApplyModifyDto.getBillPeriodApplyId() == null || customerBillPeriodApplyModifyDto.getBillPeriodApplyId() <= 0){
            throw new CustomerBillPeriodException("修改客户账期申请失败，参数异常");
        }
        CustomerBillPeriodApply customerBillPeriodApply =
                customerBillPeriodApplyMapper.selectByPrimaryKey(customerBillPeriodApplyModifyDto.getBillPeriodApplyId());
        if (customerBillPeriodApply == null) {
            throw new CustomerBillPeriodException("修改客户账期申请失败，该客户账期不存在");
        }
        if (CustomerBillPeriodApplyCheckStatusEnum.APPROVAL.getCode().equals(customerBillPeriodApply.getCheckStatus())){
            throw new CustomerBillPeriodException("修改客户账期申请失败，该客户账期已审核通过");
        }
        CustomerBillPeriodApply toModify = new CustomerBillPeriodApply();
        toModify.setBillPeriodApplyId(customerBillPeriodApplyModifyDto.getBillPeriodApplyId());
        if (customerBillPeriodApplyModifyDto.getApplyAmount() != null) {
            if (CustomerBillPeriodApplyTypeEnum.MODIFY.getCode().equals(customerBillPeriodApply.getOperateType()) && customerBillPeriodApplyModifyDto.getApplyAmount().compareTo(BigDecimal.ZERO) < 0){
                BigDecimal freezingAmount =
                        customerBillPeriodUseDetailMapper.getFreezingAmountByBillPeriodId(customerBillPeriodApply.getBillPeriodId());
                CustomerBillPeriod customerBillPeriod = customerBillPeriodMapper.selectByPrimaryKey(customerBillPeriodApply.getBillPeriodId());
                if (customerBillPeriod.getApplyAmount().add(customerBillPeriodApplyModifyDto.getApplyAmount()).compareTo(freezingAmount) < 0){
                    throw new CustomerBillPeriodException("修改客户账期申请失败，调整后额度不能小于已占用金额");
                }
            }
            toModify.setApplyAmount(customerBillPeriodApplyModifyDto.getApplyAmount());
        }
        if (customerBillPeriodApplyModifyDto.getSettlementPeriod() != null) {
            toModify.setSettlementPeriod(customerBillPeriodApplyModifyDto.getSettlementPeriod());
        }
        toModify.setModTime(System.currentTimeMillis());
        toModify.setUpdater(customerBillPeriodApplyModifyDto.getUpdater());
        customerBillPeriodApplyMapper.updateByPrimaryKeySelective(toModify);
        logger.info("修改客户账期申请成功，{}",toModify.toString());
    }


    @MethodLock(className = Long.class, time = 10000)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvalCustomerBillPeriodApply(@MethodLockParam Long billPeriodApplyId, Boolean pass) throws CustomerBillPeriodException {
        logger.info("审核账期申请：{}，审核结果：{}",billPeriodApplyId,pass);
        CustomerBillPeriodApply apply = customerBillPeriodApplyMapper.selectByPrimaryKey(billPeriodApplyId);
        if (apply == null) {
            throw new CustomerBillPeriodException("客户账期申请不存在");
        }
        CustomerBillPeriodApply toUpdateApply = new CustomerBillPeriodApply();
        toUpdateApply.setBillPeriodApplyId(billPeriodApplyId);
        toUpdateApply.setCheckStatus(pass ? CustomerBillPeriodApplyCheckStatusEnum.APPROVAL.getCode() : CustomerBillPeriodApplyCheckStatusEnum.REJECT.getCode());
        customerBillPeriodApplyMapper.updateByPrimaryKeySelective(toUpdateApply);

        if (!pass){
            return;
        }

        //新增账期申请
        if (CustomerBillPeriodApplyTypeEnum.ADD.getCode().equals(apply.getOperateType())){
            CustomerBillPeriod customerBillPeriod = new CustomerBillPeriod();
            BeanUtils.copyProperties(apply,customerBillPeriod);
            customerBillPeriod.setAddTime(System.currentTimeMillis());
            customerBillPeriodMapper.insertSelective(customerBillPeriod);
            logger.info("新增客户账期成功，{}",customerBillPeriod.toString());
        } else {
            //账期修改申请
            CustomerBillPeriod customerBillPeriod = customerBillPeriodMapper.selectByPrimaryKey(apply.getBillPeriodId());
            CustomerBillPeriod toUpdateBillPeriod = new CustomerBillPeriod();
            toUpdateBillPeriod.setBillPeriodId(apply.getBillPeriodId());
            toUpdateBillPeriod.setBillPeriodStart(apply.getBillPeriodStart());
            toUpdateBillPeriod.setBillPeriodEnd(apply.getBillPeriodEnd());
            toUpdateBillPeriod.setSettlementPeriod(apply.getSettlementPeriod());
            toUpdateBillPeriod.setModTime(System.currentTimeMillis());
            toUpdateBillPeriod.setUpdater(apply.getCreator());
            if (apply.getApplyAmount() != null){
                if (apply.getApplyAmount().compareTo(BigDecimal.ZERO) < 0){
                    //校验 调整后额度不能小于已占用金额
                    BigDecimal freezingAmount = customerBillPeriodUseDetailMapper.getFreezingAmountByBillPeriodId(customerBillPeriod.getBillPeriodId());
                    if (customerBillPeriod.getApplyAmount().add(apply.getApplyAmount()).compareTo(freezingAmount) < 0){
                        throw new CustomerBillPeriodException("审核通过客户账期申请失败，调整后额度不能小于已占用金额");
                    }
                }
                toUpdateBillPeriod.setApplyAmount(customerBillPeriod.getApplyAmount().add(apply.getApplyAmount()));
            }
            customerBillPeriodMapper.updateByPrimaryKeySelective(toUpdateBillPeriod);
            logger.info("审核通过账期申请成功，更新账期：{}",toUpdateBillPeriod.toString());
        }
    }

    @Override
    public CustomerBillPeriodPageDto<List<CustomerBillPeriodApplyDetailsDto>> getCustomerBillPeriodApplyListByPage(CustomerBillPeriodApplyQueryDto queryDto) {
        Page page = PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        List<CustomerBillPeriodApplyDetailsDto> applyDetailsDtoList = customerBillPeriodApplyMapper.getApplyDetailsDtoList(queryDto);
        CustomerBillPeriodPageDto<List<CustomerBillPeriodApplyDetailsDto>> pageDto = new CustomerBillPeriodPageDto<>();
        pageDto.setPageNum(queryDto.getPageNum());
        pageDto.setPageSize(queryDto.getPageSize());
        pageDto.setPages(page.getPages());
        pageDto.setTotal(page.getTotal());
        pageDto.setData(applyDetailsDtoList);
        return pageDto;
    }


    /**
     * 正式账期申请校验
     * 1、客户只能存在一个正式账期
     * 2、账期申请为调低账期额度时，调整金额不能小于已占用金额
     * @param applyDto 账期申请
     * @param customerBillPeriodList 客户现有账期集合
     * @throws CustomerBillPeriodException 异常
     */
    private void checkOfficialCustomerBillPeriodApply(CustomerBillPeriodApplyDto applyDto, List<CustomerBillPeriod> customerBillPeriodList) throws CustomerBillPeriodException{
        //获取目前为止有效的正式账期
        long currentTime = System.currentTimeMillis();
        List<CustomerBillPeriod> validOfficialBillPeriodList = customerBillPeriodList.stream()
                        .filter(item -> CustomerBillPeriodTypeEnum.OFFICIAL.getCode().equals(item.getBillPeriodType()))
                        .filter(item -> item.getBillPeriodEnd() > currentTime)
                        .collect(Collectors.toList());
        if (CustomerBillPeriodApplyTypeEnum.ADD.getCode().equals(applyDto.getOperateType()) && validOfficialBillPeriodList.size() > 0){
            throw new CustomerBillPeriodException("保存客户账期申请有误，客户已经存在正式账期");
        }
        //账期申请为调低账期额度时，调整金额不能小于已占用金额
        if (CustomerBillPeriodApplyTypeEnum.MODIFY.getCode().equals(applyDto.getOperateType()) && applyDto.getApplyAmount().compareTo(BigDecimal.ZERO) < 0){
            BigDecimal freezingAmount = customerBillPeriodUseDetailMapper.getFreezingAmountByBillPeriodId(validOfficialBillPeriodList.get(0).getBillPeriodId());
            if (validOfficialBillPeriodList.get(0).getApplyAmount().add(applyDto.getApplyAmount()).compareTo(freezingAmount) < 0){
                throw new CustomerBillPeriodException("保存客户账期申请有误，调整后额度不能小于已占用金额");
            }
        }
    }

    /**
     * 临时账期申请校验
     * 1、临时账期有效时间不能存在重叠
     * 2、账期申请为调低账期额度时，调整金额不能小于已占用金额
     * @param applyDto 账期申请
     * @param customerBillPeriodList 客户现有账期集合
     * @throws CustomerBillPeriodException 异常
     */
    private void checkTemporaryCustomerBillPeriodApply(CustomerBillPeriodApplyDto applyDto, List<CustomerBillPeriod> customerBillPeriodList) throws CustomerBillPeriodException{

        //账期申请为调低账期额度时，调整金额不能小于已占用金额
        if (applyDto.getOperateType().equals(CustomerBillPeriodApplyTypeEnum.MODIFY.getCode()) && applyDto.getApplyAmount().compareTo(BigDecimal.ZERO) < 0){
            CustomerBillPeriod toModifyCustomerBillPeriod = customerBillPeriodMapper.selectByPrimaryKey(applyDto.getBillPeriodId());
            if (toModifyCustomerBillPeriod == null) {
                throw new CustomerBillPeriodException("保存客户账期申请有误，待修改的账期不存在");
            }
            BigDecimal freezingAmount = customerBillPeriodUseDetailMapper.getFreezingAmountByBillPeriodId(applyDto.getBillPeriodId());
            if (toModifyCustomerBillPeriod.getApplyAmount().add(applyDto.getApplyAmount()).compareTo(freezingAmount) < 0){
                throw new CustomerBillPeriodException("保存客户账期申请有误，调整金额不能小于已占用金额");
            }
        }

        if (CustomerBillPeriodApplyTypeEnum.ADD.getCode().equals(applyDto.getOperateType())){
            CustomerBillPeriod temporaryCustomerBillPeriodApply = new CustomerBillPeriod();
            temporaryCustomerBillPeriodApply.setBillPeriodStart(applyDto.getBillPeriodStart());
            temporaryCustomerBillPeriodApply.setBillPeriodEnd(applyDto.getBillPeriodEnd());
            temporaryCustomerBillPeriodApply.setBillPeriodType(applyDto.getBillPeriodType());
            customerBillPeriodList.add(temporaryCustomerBillPeriodApply);
        }

        List<CustomerBillPeriod> temporaryCustomerBillPeriodList = customerBillPeriodList.stream()
                .filter(item -> CustomerBillPeriodTypeEnum.TEMPORARY.getCode().equals(item.getBillPeriodType()))
                .peek(item -> {
                    //如果是申请修改临时账期，那么将申请修改的有效期替换原来账期的有效期
                    if (applyDto.getBillPeriodId() != null && applyDto.getBillPeriodId().equals(item.getBillPeriodId())){
                        if (applyDto.getBillPeriodStart() != null && applyDto.getBillPeriodStart() > 0) {
                            item.setBillPeriodStart(applyDto.getBillPeriodStart());
                        }
                        if (applyDto.getBillPeriodEnd() != null && applyDto.getBillPeriodEnd() > 0) {
                            item.setBillPeriodEnd(applyDto.getBillPeriodEnd());
                        }
                    }
                })
                //将临时账期按照生效开始时间进行升序排序
                .sorted(Comparator.comparing(CustomerBillPeriod::getBillPeriodStart))
                .collect(Collectors.toList());

        if (temporaryCustomerBillPeriodList.size() < 2) {
            return;
        }

        //校验所有临时账期是否存在有效期时间重叠
        long maxBillPeriodEnd = temporaryCustomerBillPeriodList.get(0).getBillPeriodEnd();
        for (int i = 1; i < temporaryCustomerBillPeriodList.size(); i++) {
            if (maxBillPeriodEnd > temporaryCustomerBillPeriodList.get(i).getBillPeriodStart()){
                throw new CustomerBillPeriodException("保存客户账期申请有误，临时账期有效期存在时间重叠");
            }
            maxBillPeriodEnd = temporaryCustomerBillPeriodList.get(i).getBillPeriodEnd();
        }
    }

    /**
     * 订单账期申请校验
     * 1、一个订单只能有一个订单账期
     * 2、订单账期有使用记录，就不能进行修改
     * @param applyDto 账期申请
     * @param customerBillPeriodList 客户现有账期集合
     * @throws CustomerBillPeriodException 异常
     */
    private void checkOrderCustomerBillPeriodApply(CustomerBillPeriodApplyDto applyDto, List<CustomerBillPeriod> customerBillPeriodList) throws CustomerBillPeriodException{
        if (applyDto.getRelatedOrderId() == null || applyDto.getRelatedOrderId() <= 0) {
            throw new CustomerBillPeriodException("保存账期申请有误，订单信息为空");
        }
        if (CustomerBillPeriodApplyTypeEnum.ADD.getCode().equals(applyDto.getOperateType())){
            List<CustomerBillPeriod> orderCustomerBillPeriodList = customerBillPeriodList.stream()
                    .filter(item -> CustomerBillPeriodTypeEnum.ORDER.getCode().equals(item.getBillPeriodType()))
                    .filter(item -> item.getRelatedOrderId().equals(applyDto.getRelatedOrderId()))
                    .collect(Collectors.toList());
            if (orderCustomerBillPeriodList.size() > 0){
                throw new CustomerBillPeriodException("保存账期申请有误，该订单账期已存在");
            }
        }

        if (CustomerBillPeriodApplyTypeEnum.MODIFY.getCode().equals(applyDto.getOperateType())){
            if (customerBillPeriodUseDetailMapper.getCustomerBillPeriodUseDetailListByBillPeriodId(applyDto.getBillPeriodId()).size() > 0){
                throw new CustomerBillPeriodException("保存账期申请有误，该订单账期已使用，不能进行修改");
            }
        }
    }


    @Override
    public List<CustomerBillPeriodApply> getApplyInfoByBillPeriodId(List<Long> billPeriodIds) {
        return customerBillPeriodApplyMapper.getApplyInfoByBillPeriodId(billPeriodIds);
    }
}
