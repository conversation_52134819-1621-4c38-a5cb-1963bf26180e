package com.vedeng.infrastructure.file.mapper;

import com.vedeng.infrastructure.file.domain.Attachment;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;

@Named("GlobalAttachmentMapper")
public interface AttachmentMapper {
    int deleteByPrimaryKey(Integer attachmentId);

    int insertSelective(Attachment record);

    Attachment selectByPrimaryKey(Integer attachmentId);

    int updateByPrimaryKeySelective(Attachment record);

    /**
     * @param relatedId          关联表id
     * @param attachmentFunction 附件应用类型
     * @return
     * <AUTHOR>
     * @desc 根据参数查询记录
     */
    List<Attachment> queryListByRelatedIdAndFunction(@Param("relatedId") Integer relatedId, @Param("attachmentFunction") Integer attachmentFunction);

    /**
     * @param attachmentId
     * <AUTHOR>
     * @desc 根据主键id作废附件
     */
    void delAttachmentByPrimaryKey(Integer attachmentId);

    /**
     * 批量插入
     *
     * @param attachments
     */
    void batchInsertAttachmentSelective(@Param("list") List<Attachment> attachments);

    /**
     * (buyorder model迁移)
     * 批量保存
     *
     * @param saveItems 数据集合
     * @return
     */
    int batchInsertSelective(@Param("list") List<Attachment> saveItems);

    /**
     * 根据相关id、附件类型、附件应用类型查询附件
     *
     * @param attachment attachment
     * @return List<Attachment>
     */
    List<Attachment> queryByRelatedIdAndAttachmentTypeAndAttachmentFunction(@Param("attachment") Attachment attachment);

    /**
     * 根据主键id集合批量返回附件
     */
    List<Attachment> selectAllByIds(@Param("ids") List<Long> ids);
}
