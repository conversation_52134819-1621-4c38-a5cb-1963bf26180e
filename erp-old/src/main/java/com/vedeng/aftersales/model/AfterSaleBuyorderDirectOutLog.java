package com.vedeng.aftersales.model;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
 * <AUTHOR>
@Data
public class AfterSaleBuyorderDirectOutLog implements Serializable {
    /**
     * id
     */
    private Integer afterSaleBuyorderDirectOutLogId;

    /**
     * 关联售后单id
     */
    private Integer afterSalesId;

    /**
     * 关联售后商品id
     */
    private Integer afterSalesGoodsId;

    /**
     * SKU
     */
    private String sku;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 更新人
     */
    private Date updater;

    /**
     * 是否删除
     */
    private Byte isDelete;

    /**
     * 贝登批次码
     */
    private String vedengBatchNum;

    /**
     * 生产日期
     */
    private Long produceTime;

    /**
     * 有效期至
     */
    private Long validTime;

    /**
     * 出库时间
     */
    private Long outTime;

    /**
     * 厂商编号
     */
    private String industryBatchNumber;

    /**
     * 灭菌编号
     */
    private String sterilizationNumber;

    /**
     * 注册证号
     */
    private String registrationNumber;

    /**
     * 注册证id
     */
    private Integer firstEngageId;

    /**
     * 产品名称
     */
    private String showName;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 品牌
     */
    private String brandName;

    /**
     * 规格
     */
    private String model;

    private String spec;

    /**
     * 单位
     */
    private String unitName;

    private Integer maxOutNum;

    private String produceTimeStr;

    private String validTimeStr;

    private String outTimeStr;

    private static final long serialVersionUID = 1L;
}