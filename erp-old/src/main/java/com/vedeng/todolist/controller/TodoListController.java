package com.vedeng.todolist.controller;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.firstengage.model.ProductCompany;
import com.vedeng.firstengage.model.RegistrationNumber;
import com.vedeng.firstengage.service.FirstEngageService;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.goods.model.Goods;
import com.vedeng.goods.model.vo.GoodsStorageConditionVo;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.service.RiskCheckService;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.UserService;
import com.vedeng.todolist.constant.DepartmentType;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.constant.UserType;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.dto.*;
import com.vedeng.todolist.model.TodoList;
import com.vedeng.todolist.service.visitor.*;
import com.vedeng.todolist.model.UserInfo;
import com.vedeng.todolist.service.TodoListService;
import com.wms.service.context.ThreadLocalContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 待办事项控制台
 * @Author: daniel
 * @Date: 2020/12/14 11 20
 * @Description:
 */
@Controller
@RequestMapping(value = "/todolist")
public class TodoListController extends BaseController {

    public static Log logger = LogFactory.getLog(TodoListController.class);

    @Autowired
    private TodoListService todoListService;

    @Autowired
    private UserService userService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private RiskCheckService riskCheckService;

    @Autowired
    private FirstEngageService firstEngageService;

    @Resource
    private RiskSkuTodoStaffVisitor riskSkuTodoStaffVisitor;

    @Resource
    private RiskTraderTodoStaffVisitor riskTraderTodoStaffVisitor;

    @Resource
    private PurchaseInfoTotoStaffVisitor purchaseInfoTotoStaffVisitor;

    @Resource
    private DailyManageTodoStaffVisitor dailyManageTodoStaffVisitor;


    @Resource
    private RiskSkuTodoAdminVisitor riskSkuTodoAdminVisitor;

    @Resource
    private RiskTraderTodoAdminVisitor riskTraderTodoAdminVisitor;

    @Resource
    private PurchaseInfoTotoAdminVisitor purchaseInfoTotoAdminVisitor;

    @Resource
    private DailyManageTodoAdminVisitor dailyManageTodoAdminVisitor;

    @Resource
    private RiskSkuRankingTodoVisitor riskSkuRankingTodoVisitor;

    @Resource
    private MessageTodoStaffVisitor messageTodoStaffVisitor;

    @Resource
    private EarlyWarningTicksTaskStaffVisitor earlyWarningTicksTaskStaffVisitor;

    @Resource
    private MessageTodoAdminVisitor messageTodoAdminVisitor;

    @Resource
    private EarlyWarningTicksTaskAdminVisitor earlyWarningTicksTaskAdminVisitor;

    @Resource
    private EarlyWarningGoodsTaskStaffVisitor earlyWarningGoodsTaskStaffVisitor;

    @Resource
    private EarlyWarningGoodsTaskAdminVisitor earlyWarningGoodsTaskAdminVisitor;

    @Resource
    private PrepareStockTaskAdminVisitor prepareStockTaskAdminVisitor;

    @Resource
    private PrepareStockTaskStaffVisitor prepareStockTaskStaffVisitor;

    @Resource
    private ReviewTaskAdminVisitor reviewTaskAdminVisitor;

    @Resource
    private ReviewTaskStaffVisitor reviewTaskStaffVisitor;

    @Resource
    private EnableReceiveStaffVisitor enableReceiveStaffVisitor;

    @Resource
    private EnableReceiveAdminVisitor enableReceiveAdminVisitor;

    @Resource
    private TodoListMapper todoListMapper;

    @Resource
    private UserMapper userMapper;

    /**
     * 供应链部门id集合，以逗号分隔
     */
    @Value("${supplyChainDepartList}")
    private String supplyChainDepartList;

    @Value("${ez.domain}")
    private String ezDomain;

    @Value("${ez_url_cg}")
    private String ezUrlCg;

    @Value("${peer_list_filter_time}")
    private Long peerListFilterTime;

    @Value("${customer_qualification_approval}")
    private String customerQualificationApproval;

    @Value("${saleorder_approval}")
    private String saleorderApproval;

    @Value("${buyorder_approval}")
    private String buyorderApproval;

    @Value("${contract_approval}")
    private String contractApproval;

    @Value("${confirmation_approval}")
    private String confirmationApproval;

    @Value("${saleorder_exception_risk}")
    private String saleorderExceptionRisk;

    @Value("${buyorder_exception_risk}")
    private String buyorderExceptionRisk;


    @Value("${supplier_business_license}")
    private String supplierBusinessLicense;

    @Value("${supplier_production license}")
    private String supplierProductionLicense;

    @Value("${supplier_product_registration_form}")
    private String supplierProductRegistrationForm;

    @Value("${supplier_business_license_of_medical_devices}")
    private String supplierBusinessLicenseOfMedicalDevices;

    @Value("${customer_business_license}")
    private String customerBusinessLicense;

    @Value("${customer_practice_license}")
    private String customerPracticeLicense;

    @Value("${customer_business_license_of_medical_devices}")
    private String customerBusinessLicenseOfMedicalDevices;


    @Value("${supplyChain.ez.id}")
    private String supplyChainEzId;

    @Value("${qualityDepart.ez.id}")
    private String qualityDepartEzId;

    @Value("${moduleHidden.startTime}")
    private String moduleHiddenStartTime;

    @Value("${lxcrmUrl}")
    private String lxcrmUrl;

    /**
     * 2024-02-21 Kerwin确认不再使用
     * @return
     */
    @RequestMapping(method = RequestMethod.GET,value = "/supplyChain/welcome/old")
    public ModelAndView supplyChainTodoListPage_old(){
        ModelAndView mv = new ModelAndView("todolist/supplyChain_welcome");
        mv.addObject("ezDomain",ezDomain);
        mv.addObject("ezIdArray",supplyChainEzId.split(","));
        return mv;
    }

    /**
     * 供应链工作台页面
     * @return
     */
    @RequestMapping(method = RequestMethod.GET,value = "/supplyChain/welcome")
    public ModelAndView supplyChainTodoListPage(HttpSession session){

        User currentUser = (User) session.getAttribute(Consts.SESSION_USER);
        ModelAndView mv =  new ModelAndView();
        mv.addObject("userId",currentUser.getUserId());
        mv.addObject("ezDomain",ezDomain);
        mv.addObject("ezIdArray",supplyChainEzId.split(","));
        mv.addObject("ezUrlCg",ezUrlCg);

        UserType userType = getCurrentUserType(currentUser);

        ThreadLocalContext.put("userInfo",currentUser);

        try{
            //员工视角
            if(UserType.Staff.getValue().equals(userType.getValue())){

                SupplyChainStaffTodoDto staffTodoDto = SupplyChainStaffTodoDtoBuild.newBuild()
                        .setRiskCheckSkuTodoDto(this.riskSkuTodoStaffVisitor)
                        .setRiskCheckTraderTodo(this.riskTraderTodoStaffVisitor)
                        /*.setPurchaseInfoToto(this.purchaseInfoTotoStaffVisitor)*/
                        .setDailyManagementTodo(this.dailyManageTodoStaffVisitor)
                        .setMessageTodoDto(this.messageTodoStaffVisitor)
                        .setEarlyWarningTicksTaskDto(this.earlyWarningTicksTaskStaffVisitor)
                        .setEarlyWarningGoodsTaskDto(this.earlyWarningGoodsTaskStaffVisitor)
                        .setPrepareStockTaskDto(this.prepareStockTaskStaffVisitor)
                        .setReviewTaskToDoDto(this.reviewTaskStaffVisitor)
                        .setEnableReceiveToDoDto(this.enableReceiveStaffVisitor)
                        .bulid();

                Long overZeroValue = staffTodoDto.getDailyManagementTodoDto().getSkuAndSpuCountGroupByGrade()
                        .values().stream()
                        .filter(e -> e > 0)
                        .findFirst()
                        .orElse(null);

                mv.addObject("staffTodoDto",staffTodoDto);
                mv.addObject("isShow",overZeroValue == null ? 0 : 1);
                mv.setViewName("todolist/supplyChain_welcome_staff");
            }

            //管理视角
            if(UserType.Admin.getValue().equals(userType.getValue())){

                //获取当前用户可以看的部门
                List<Organization> orgaList = getCurrentUserCanSeeOrga(currentUser);
                ThreadLocalContext.put("orgaList",orgaList);

                SupplyChainAdminTodoDto adminTodoDto = SupplyChainAdminTodoDtoBuild.newBuild()
                        .setRiskCheckSkuTodoDto(this.riskSkuTodoAdminVisitor)
                        .setRiskCheckTraderTodo(this.riskTraderTodoAdminVisitor)
                        /*.setPurchaseInfoToto(this.purchaseInfoTotoAdminVisitor)*/
                        .setDailyManagementTodo(this.dailyManageTodoAdminVisitor)
                        .setMessageTodoDto(this.messageTodoAdminVisitor)
                        .setRiskSkuRankingTodoDto(this.riskSkuRankingTodoVisitor)
                        .setEarlyWarningGoodsTaskDto(this.earlyWarningGoodsTaskAdminVisitor)
                        .setPrepareStockTaskDto(this.prepareStockTaskAdminVisitor)
                        /*.setPurchaseRankingDto(this.purchaseRankingTodoVisitor)*/
                        .setReviewTaskToDoDto(this.reviewTaskAdminVisitor)
                        .setEarlyWarningTicksTaskDto(this.earlyWarningTicksTaskAdminVisitor)
                        .setEnableReceiveToDoDto(this.enableReceiveAdminVisitor)
                        .bulid();
//
                Long overZeroValue = adminTodoDto.getDailyManagementTodoDto().getSkuAndSpuCountGroupByGrade()
                        .values().stream()
                        .filter(e -> e > 0)
                        .findFirst()
                        .orElse(null);

                mv.addObject("adminTodoDto",adminTodoDto);
                mv.addObject("isShow",overZeroValue == null ? 0 : 1);

                mv.setViewName("todolist/supplyChain_welcome_admin");
            }

            // crm任务
            Integer crmTaskCount = todoListService.getCrmTaskCount(currentUser.getUserId());
            mv.addObject("crmTaskCount", crmTaskCount);
            mv.addObject("crmTaskUrl", lxcrmUrl + "/crm/task/profile/index?list=2");
            Integer crmTodoTaskCount = todoListService.getCrmTodoTaskCount(currentUser.getUserId());
            mv.addObject("crmTodoTaskCount", crmTodoTaskCount);
            mv.addObject("crmTodoTaskUrl", lxcrmUrl + "/crm/task/profile/index?list=1");

        }catch(Exception e){
            logger.error("supplyChainTodoListPage->warn:",e);
        }

        return mv;
    }

    private List<Organization> getCurrentUserCanSeeOrga(User currentUser) {
        return orgService.getChildrenOrgByParentId(currentUser.getOrgId(),1);
    }

    private UserType getCurrentUserType(User use) {

        UserInfo userInfo = this.userService.getDepartAndPositionInfo(use.getUserId());

        if(userInfo == null || StringUtils.isEmpty(userInfo.getDepartTpyeName())){
            return UserType.Staff;
        }

        //部门类型是采购的且职位级别是员工 那就是员工
        if(DepartmentType.PURCHASE.getValue().equals(userInfo.getDepartTpyeName())){
            return UserType.Staff.getValue().equals(userInfo.getPositionTypeName()) ? UserType.Staff : UserType.Admin;
        }

        return UserType.Staff;
    }

    @RequestMapping(method = RequestMethod.GET,value = "/qualityDepartment/welcome")
    public ModelAndView qualityDepartmentTodoListPage(){
        ModelAndView mv = new ModelAndView("todolist/qualityDepartment_welcome");
        mv.addObject("ezDomain",ezDomain);
        mv.addObject("customerQualificationApproval",customerQualificationApproval);
        mv.addObject("saleorderApproval",saleorderApproval);
        mv.addObject("buyorderApproval",buyorderApproval);
        mv.addObject("contractApproval", contractApproval);
        mv.addObject("confirmationApproval", confirmationApproval);
        mv.addObject("saleorderExceptionRisk",saleorderExceptionRisk);
        mv.addObject("buyorderExceptionRisk",buyorderExceptionRisk);

        mv.addObject("supplierBusinessLicense",supplierBusinessLicense);
        mv.addObject("supplierProductionLicense",supplierProductionLicense);
        mv.addObject("supplierProductRegistrationForm",supplierProductRegistrationForm);
        mv.addObject("supplierBusinessLicenseOfMedicalDevices",supplierBusinessLicenseOfMedicalDevices);
        mv.addObject("customerBusinessLicense",customerBusinessLicense);
        mv.addObject("customerPracticeLicense",customerPracticeLicense);
        mv.addObject("customerBusinessLicenseOfMedicalDevices",customerBusinessLicenseOfMedicalDevices);
        mv.addObject("ezIdArray",qualityDepartEzId.split(","));
        return mv;
    }

    @RequestMapping(method = RequestMethod.POST, value = "/supplyChain/pushChangeOrg")
    @ResponseBody
    public ResultInfo<DailyManagementTodoDto> pushChangeOrg(HttpServletRequest request, HttpSession session,Integer orgaId){

        try{

            User currentUser = (User) session.getAttribute(Consts.SESSION_USER);

            DailyManagementTodoDto dailyManagementTodoDto = new DailyManagementTodoDto();

            List<Integer> userIdList = getUserList(orgaId,currentUser);

            List<Integer> sceneIdList = Arrays.asList(TodoListBuzSceneEnum.MAINTAIN_DATA_DELIVERY_TIME.getBuzSceneId(),
                    TodoListBuzSceneEnum.MAINTAIN_DATA_PRICE.getBuzSceneId(),
                    TodoListBuzSceneEnum.MAINTAIN_DATA_AFTER_SALE_POLICY.getBuzSceneId(),
                    TodoListBuzSceneEnum.MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY.getBuzSceneId(),
                    TodoListBuzSceneEnum.MAINTAIN_DATA_OPERATION_INFO.getBuzSceneId());

            List<TodoList> pushTodoList = todoListMapper.getUnHandledSkuPushTodoList(sceneIdList,userIdList);

            dailyManagementTodoDto.setSkuPushSubordinateList(userIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));

            dailyManagementTodoDto.setDeliveryTimeCountGroupByGrade(
                    pushTodoList.stream()
                            .filter(todo -> todo.getBuzType().equals(TodoListBuzSceneEnum.MAINTAIN_DATA_DELIVERY_TIME.getBuzSceneId()))
                            .collect(Collectors.groupingBy(TodoList::getGoodsLevelNo, Collectors.counting()))
            );

            dailyManagementTodoDto.setPriceCountGroupByGrade(
                    pushTodoList.stream()
                            .filter(todo -> todo.getBuzType().equals(TodoListBuzSceneEnum.MAINTAIN_DATA_PRICE.getBuzSceneId()))
                            .collect(Collectors.groupingBy(TodoList::getGoodsLevelNo, Collectors.counting()))
            );

            if (dailyManagementTodoDto.getPriceCountGroupByGrade().size() > 0){
                dailyManagementTodoDto.setPriceSkuList(getUnHandledMaintainPriceTodoListGroupByGrade(userIdList));
            }

            dailyManagementTodoDto.setAftersalePolicyCountGroupByGrade(
                    pushTodoList.stream()
                            .filter(todo -> todo.getBuzType().equals(TodoListBuzSceneEnum.MAINTAIN_DATA_AFTER_SALE_POLICY.getBuzSceneId()))
                            .collect(Collectors.groupingBy(TodoList::getGoodsLevelNo, Collectors.counting()))
            );

            dailyManagementTodoDto.setSupplyAftersalePolicyCountGroupByGrade(
                    pushTodoList.stream()
                            .filter(todo -> todo.getBuzType().equals(TodoListBuzSceneEnum.MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY.getBuzSceneId()))
                            .collect(Collectors.groupingBy(TodoList::getGoodsLevelNo, Collectors.counting()))
            );

            dailyManagementTodoDto.setOperationInfoCountGroupByGrade(
                    pushTodoList.stream()
                            .filter(todo -> todo.getBuzType().equals(TodoListBuzSceneEnum.MAINTAIN_DATA_OPERATION_INFO.getBuzSceneId()))
                            .collect(Collectors.groupingBy(TodoList::getGoodsLevelNo, Collectors.counting()))
            );

            return new ResultInfo<>(0,"根据orgaId查询商品推送代办成功",dailyManagementTodoDto);
        }catch (Exception e){
            logger.error("pushChangeOrg error:",e);
            return new ResultInfo<>(-1, "根据orgaId查询商品推送代办失败！");
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "/supplyChain/skuAndSpuChangeOrg")
    @ResponseBody
    public ResultInfo<DailyManagementTodoDto> skuAndSpuChangeOrg(HttpServletRequest request, HttpSession session,Integer orgaId){
        try{

            User currentUser = (User) session.getAttribute(Consts.SESSION_USER);

            DailyManagementTodoDto dailyManagementTodoDto = new DailyManagementTodoDto();

            List<Integer> userIdList = getUserList(orgaId,currentUser);

            List<TodoList> maintainTodoList = todoListMapper.getUnHandledRiskCheckSkuTodoListByUserList(TodoListBuzSceneEnum.MAINTAIN_DATA_SKU.getBuzSceneId(), userIdList);

            dailyManagementTodoDto.setSkuAndSpuSubordinateList(userIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));

            dailyManagementTodoDto.setSkuAndSpuCountGroupByGrade(
                    maintainTodoList.stream()
                            .collect(Collectors.groupingBy(TodoList::getGoodsLevelNo, Collectors.counting()))
            );

            return new ResultInfo<>(0,"根据orgaId查询商品等级代办成功",dailyManagementTodoDto);
        }catch (Exception e){
            logger.error("pushChangeOrg error:",e);
            return new ResultInfo<>(-1, "根据orgaId查询商品等级代办失败！");
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "/supplyChain/receiptRecordChangeOrg")
    @NoNeedAccessAuthorization
    @ResponseBody
    @SuppressWarnings("all")
    public ResultInfo<DailyManagementTodoDto> receiptRecordChangeOrg(HttpServletRequest request, HttpSession session,Integer orgaId){
        try{

            User currentUser = (User) session.getAttribute(Consts.SESSION_USER);

            List<Integer> userIdList = getUserList(orgaId,currentUser);
            Integer buyorderListAllCount = todoListMapper.getReceiptRecordCountGroupByCategoryCount(userIdList,peerListFilterTime,null,null);
            Integer buyorderListIsUrgentCount = todoListMapper.getReceiptRecordCountGroupByCategoryCount(userIdList,peerListFilterTime,1,null);
            Map<Long, Long> returnMap = new HashMap<>();
            //总待办
            returnMap.put(1L,(long)buyorderListAllCount);
            //紧急待办
            returnMap.put(2L,(long)buyorderListIsUrgentCount);
            //日常待办
            returnMap.put(3L,(long)buyorderListAllCount-buyorderListIsUrgentCount);

            DailyManagementTodoDto dailyManagementTodoDto = new DailyManagementTodoDto();
            dailyManagementTodoDto.setReceiptRecordSubordinateList(userIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));
            dailyManagementTodoDto.setReceiptRecordCountGroupByCategory(returnMap);

            return new ResultInfo<>(0,"根据orgaId查询商品等级代办成功",dailyManagementTodoDto);
        }catch (Exception e){
            logger.error("pushChangeOrg error:",e);
            return new ResultInfo<>(-1, "根据orgaId查询商品等级代办失败！");
        }
    }


    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/supplyChain/dealwithPurchaseTaskAdmin")
    public ResultInfo<PurchaseInfoTotoDto> dealwithPurchaseTaskAdmin(HttpServletRequest request, HttpSession session){
        try{

            User currentUser = (User) session.getAttribute(Consts.SESSION_USER);

//            //获取当前用户可以看的部门
//            List<Organization> orgaList = getCurrentUserCanSeeOrga(currentUser);
//
//            PurchaseInfoTotoDto purchaseInfoTotoDto = purchaseInfoTotoAdminVisitor.visitor(orgaList);

//            return new ResultInfo<>(0,"根据orgaId采购任务信息成功",purchaseInfoTotoDto);
            return new ResultInfo<>(0,"根据orgaId采购任务信息成功",new ArrayList<>());
        }catch (Exception e){
            logger.error("dealwithPurchaseTask error:",e);
            return new ResultInfo<>(-1, "根据orgaId采购任务信息失败！");
        }
    }


    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/supplyChain/dealwithPurchaseTaskStaff")
    public ResultInfo<PurchaseInfoTotoDto> dealwithPurchaseTaskStaff(HttpServletRequest request, HttpSession session){
        try{

            User currentUser = (User) session.getAttribute(Consts.SESSION_USER);

            PurchaseInfoTotoDto purchaseInfoTotoDto = purchaseInfoTotoStaffVisitor.visitor(currentUser);

            return new ResultInfo<>(0,"根据orgaId采购任务信息成功",purchaseInfoTotoDto);
        }catch (Exception e){
            logger.error("dealwithPurchaseTask error:",e);
            return new ResultInfo<>(-1, "根据orgaId采购任务信息失败！");
        }
    }


    @RequestMapping(method = RequestMethod.POST, value = "/supplyChain/dealwithPurchaseRanking")
    @ResponseBody
    public ResultInfo<List<RankingDto>> dealwithPurchaseRanking(){
        try{

            Integer rootDepartMentId = orgService.getOrgIdByOrgName("供应链管理部");

            if(rootDepartMentId ==  null){
                return new ResultInfo<>(-1, "无对应组织供应链管理部！");
            }

//            //获取供应链管理部所有数据
//            List<Organization> orgaList = orgService.getChildrenOrgByParentId(rootDepartMentId,1);
//
//            /*List<Organization> orgaList = getCurrentUserCanSeeOrga((User) session.getAttribute(Consts.SESSION_USER));*/
//
//            List<RankingDto> rankingList = purchaseRankingTodoVisitor.visitor(orgaList);
//
//            return new ResultInfo<List<RankingDto>>(0,"根据orgaId采购排名信息成功", rankingList);
            return new ResultInfo<List<RankingDto>>(0,"根据orgaId采购排名信息成功", new ArrayList<RankingDto>());

        }catch (Exception e){
            logger.error("dealwithPurchaseRanking error:",e);
            return new ResultInfo<>(-1, "根据orgaId采购排名信息失败！");
        }
    }


    private List<Integer> getUserList(Integer orgId,User currentUser) {

        List<Integer> userIdList = new ArrayList<>();
        //所有部门
        if(orgId == -1){

            Integer rootDepartMentId = this.orgService.getOrgIdByOrgName("供应链管理部");

            List<Organization> orgaList = orgService.getChildrenOrgByParentId(rootDepartMentId,1);

            for(Organization organization : orgaList){
                userIdList.addAll(userMapper.getUserIdListByOrgId(organization.getOrgId()));
            }

        }else{
            userIdList = userMapper.getUserIdListByOrgId(orgId);
        }

        return userIdList;
    }

    /**
     * 获取待维护价格的待办事项，按照商品等级统计sku
     * @param userList 用户集合
     * @return 结果
     */
    private Map<Long, String> getUnHandledMaintainPriceTodoListGroupByGrade(List<Integer> userList){
        Map<Integer, List<TodoList>> maintainPriceOfSku =
                todoListMapper.getUnHandledMaintainTodoListSkuByUserList(TodoListBuzSceneEnum.MAINTAIN_DATA_PRICE.getBuzSceneId(), userList)
                        .stream()
                        .collect(Collectors.groupingBy(TodoList::getBuzType));
        Map<Long, String> skuListGroupByGoodsLevel = new HashMap<>();
        for (Integer key : maintainPriceOfSku.keySet()){
            String skuListStr = maintainPriceOfSku.get(key).stream().map(TodoList::getBuzExtra).collect(Collectors.joining(","));
            skuListGroupByGoodsLevel.put(Long.valueOf(key),skuListStr);
        }
        return skuListGroupByGoodsLevel;
    }

    @RequestMapping(method = RequestMethod.GET, value = "/supplyChain/statistics")
    @ResponseBody
    public ResultInfo<SupplyChainTodoListDto> getSupplyChainTodoList(HttpServletRequest request, HttpSession session){
        User currentUser = (User) session.getAttribute(Consts.SESSION_USER);
        ResultInfo<SupplyChainTodoListDto> resultInfo = new ResultInfo<>();
        List<Integer> orgIdList = Arrays.stream(supplyChainDepartList.split(","))
                .map(departStr -> orgService.getChildrenByParentId(Integer.valueOf(departStr),1))
                .reduce((l1,l2) -> {
                    l1.addAll(l2);
                    return l1;
                }).get();
        List<Integer> subordinateList = new ArrayList<>();
        if (userService.checkUserInDepartList(currentUser.getUserId(),orgIdList)){
            subordinateList = userService.getAllSubordinateByUserId(currentUser.getUserId());
        }
        resultInfo.setCode(0);
        SupplyChainTodoListDto dto = todoListService.getSupplyChainTodoListByUser(subordinateList);
        if (subordinateList.size() > 0){
            dto.setSubordinateList(subordinateList.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        //当时间在上线后，如果分级分档没有待完成工作时，则在前端页面隐藏该模块
        if (DateUtil.sysTimeMillis() > Long.parseLong(moduleHiddenStartTime)){
            int todoCountOfMaintainSku = 0;
            for (Integer key : dto.getMaintainSkuAndSpuCountGroupByGrade().keySet()){
                todoCountOfMaintainSku += dto.getMaintainSkuAndSpuCountGroupByGrade().get(key);
            }
            if (todoCountOfMaintainSku == 0){
                dto.setModuleHiddenFlag(true);
            }
        }
        resultInfo.setData(dto);
        return resultInfo;
    }

    @RequestMapping(method = RequestMethod.GET, value = "/qualityDepartment/statistics")
    @ResponseBody
    public ResultInfo<QualityDepartmentTodoListDto> getQualityDepartmentTodoList(){
        ResultInfo<QualityDepartmentTodoListDto> dto = new ResultInfo<>();
        dto.setData(todoListService.getQualityDepartmentTodoList());
        dto.setCode(0);
        return dto;
    }

    /**
     * <b>Description:</b><br>
     * 质量管理部工作台预警
     *
     * @param
     * @return com.vedeng.common.model.ResultInfo<com.vedeng.todolist.dto.QualityDepartmentTodoListDto>
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/7/19 10:48
     */
    @RequestMapping(method = RequestMethod.GET, value = "/qualityDepartment/warnList")
    @ResponseBody
    public ResultInfo<QualityDepartmentWarnListDto> getQualityDepartmentWarnList(){
        ResultInfo<QualityDepartmentWarnListDto> dto = new ResultInfo<>();
        dto.setData(todoListService.getQualityDepartmentWarnList());
        dto.setCode(0);
        return dto;
    }

    @RequestMapping("/supplyChain/editSkuInfo")
    public ModelAndView editSkuInfo(HttpServletRequest request, HttpSession session, Goods goods){
        ModelAndView mv = new ModelAndView();
        Map<String,Object> map = todoListService.getRiskSkuInfo(goods);
        for (String key : map.keySet()) {
            mv.addObject(key,map.get(key));
        }
        List<RegistrationNumber> manufacturerList = firstEngageService.getAllManufacturer();
        mv.addObject("manufacturerList",manufacturerList);
        mv.setViewName("todolist/editSkuInfo");
        return mv;
    }

    @RequestMapping("/manufacturer/getManufacturerDetail")
    @ResponseBody
    public ResultInfo getManufacturerDetail(HttpServletRequest request, HttpSession session, Manufacturer manufacturer){
        Manufacturer manufacturerDetail = todoListService.getManufacturerDetail(manufacturer.getManufacturerId());
        if (manufacturerDetail != null){

            return new ResultInfo(0, "操作成功",manufacturerDetail);
        }
        return new ResultInfo(-1, "操作失败");
    }

    @ResponseBody
    @RequestMapping("/supplyChain/saveRiskSkuInfo")
    public ModelAndView saveRiskSkuInfo(HttpServletRequest request, HttpSession session,
                                      FirstEngage firstEngage, RegistrationNumber registrationNumber, ProductCompany productCompany,
                                      CoreSkuGenerate skuGenerate, CoreSpuGenerate spuGenerate, GoodsStorageConditionVo goodsStorageConditionVo){
        User currentUser = (User) session.getAttribute(Consts.SESSION_USER);

        // 是否厂家赋SN码： 选择是时，是否管理贝登追溯码：设置默认值：是
        if(skuGenerate.getIsFactorySnCode() != null && skuGenerate.getIsFactorySnCode() == 1){
            skuGenerate.setIsManageVedengCode(1);
        }

        todoListService.updateSku(skuGenerate,goodsStorageConditionVo);

        todoListService.updateSpu(spuGenerate);

        todoListService.updateFirstEngage(firstEngage,registrationNumber,productCompany,currentUser);

        riskCheckService.checkSkuAndtodo(skuGenerate.getSkuId());

        return success(new ModelAndView());
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST,value = "/supplyChain/dealWithExpeditingTaskStaff")
    public ResultInfo dealWithExpeditingTaskStaff(HttpServletRequest request, HttpSession session){
        try{

            User currentUser = (User) session.getAttribute(Consts.SESSION_USER);

            PurchaseInfoTotoDto purchaseInfoTotoDto = purchaseInfoTotoStaffVisitor.visitor(currentUser);

            return new ResultInfo<>(0,"根据orgaId催货任务信息成功",purchaseInfoTotoDto);
        }catch (Exception e){
            logger.error("dealWithExpeditingTaskStaff error:",e);
            return new ResultInfo<>(-1, "根据orgaId催货任务信息失败！");
        }
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/supplyChain/dealwithExpeditingTaskAdmin")
    public ResultInfo<PurchaseInfoTotoDto> dealwithExpeditingTaskAdmin(HttpServletRequest request, HttpSession session){
        try{

            User currentUser = (User) session.getAttribute(Consts.SESSION_USER);

            //获取当前用户可以看的部门
            List<Organization> orgaList = getCurrentUserCanSeeOrga(currentUser);

            PurchaseInfoTotoDto purchaseInfoTotoDto = purchaseInfoTotoAdminVisitor.visitor(orgaList);

            return new ResultInfo<>(0,"根据orgaId催货任务信息成功",purchaseInfoTotoDto);
        }catch (Exception e){
            logger.error("dealWithExpeditingTaskStaff error:",e);
            return new ResultInfo<>(-1, "根据orgaId催货任务信息失败！");
        }
    }



}
