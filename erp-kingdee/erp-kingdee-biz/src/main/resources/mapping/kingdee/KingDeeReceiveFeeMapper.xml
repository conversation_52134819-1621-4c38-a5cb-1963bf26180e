<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeReceiveFeeMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveFeeEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_RECEIVE_FEE-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="FID" jdbcType="VARCHAR" property="fid" />
    <result column="FDATE" jdbcType="VARCHAR" property="fdate" />
    <result column="F_QZOK_BDDJTID" jdbcType="VARCHAR" property="fQzokBddjtid" />
    <result column="FBUSINESSTYPE" jdbcType="VARCHAR" property="fbusinesstype" />
    <result column="F_SET_ACCOUNT_TYPE" jdbcType="VARCHAR" property="fSetAccountType" />
    <result column="F_BILL_TYPE_I_D" jdbcType="VARCHAR" property="fBillTypeID" />
    <result column="FCUSTOMERID" jdbcType="VARCHAR" property="fcustomerid" />
    <result column="FSETTLEORGID" jdbcType="VARCHAR" property="fsettleorgid" />

    <result column="F_ENTITY_DETAIL"
            javaType="com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDetailDto"
            property="fEntityDetail"
            typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>

  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, FID, FDATE, 
    F_QZOK_BDDJTID, FBUSINESSTYPE, F_SET_ACCOUNT_TYPE, F_BILL_TYPE_I_D, FCUSTOMERID, 
    FSETTLEORGID, F_ENTITY_DETAIL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_RECEIVE_FEE
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_RECEIVE_FEE
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveFeeEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_RECEIVE_FEE (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      FID, FDATE, F_QZOK_BDDJTID, 
      FBUSINESSTYPE, F_SET_ACCOUNT_TYPE, F_BILL_TYPE_I_D, 
      FCUSTOMERID, FSETTLEORGID, F_ENTITY_DETAIL
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{fid,jdbcType=VARCHAR}, #{fdate,jdbcType=VARCHAR}, #{fQzokBddjtid,jdbcType=VARCHAR}, 
      #{fbusinesstype,jdbcType=VARCHAR}, #{fSetAccountType,jdbcType=VARCHAR}, #{fBillTypeID,jdbcType=VARCHAR}, 
      #{fcustomerid,jdbcType=VARCHAR}, #{fsettleorgid,jdbcType=VARCHAR}, #{fEntityDetail,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveFeeEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_RECEIVE_FEE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fid != null">
        FID,
      </if>
      <if test="fdate != null">
        FDATE,
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID,
      </if>
      <if test="fbusinesstype != null">
        FBUSINESSTYPE,
      </if>
      <if test="fSetAccountType != null">
        F_SET_ACCOUNT_TYPE,
      </if>
      <if test="fBillTypeID != null">
        F_BILL_TYPE_I_D,
      </if>
      <if test="fcustomerid != null">
        FCUSTOMERID,
      </if>
      <if test="fsettleorgid != null">
        FSETTLEORGID,
      </if>
      <if test="fEntityDetail != null">
        F_ENTITY_DETAIL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fdate != null">
        #{fdate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="fbusinesstype != null">
        #{fbusinesstype,jdbcType=VARCHAR},
      </if>
      <if test="fSetAccountType != null">
        #{fSetAccountType,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeID != null">
        #{fBillTypeID,jdbcType=VARCHAR},
      </if>
      <if test="fcustomerid != null">
        #{fcustomerid,jdbcType=VARCHAR},
      </if>
      <if test="fsettleorgid != null">
        #{fsettleorgid,jdbcType=VARCHAR},
      </if>
      <if test="fEntityDetail != null">
        #{fEntityDetail,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveFeeEntity">
    <!--@mbg.generated-->
    update KING_DEE_RECEIVE_FEE
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        FID = #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fdate != null">
        FDATE = #{fdate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="fbusinesstype != null">
        FBUSINESSTYPE = #{fbusinesstype,jdbcType=VARCHAR},
      </if>
      <if test="fSetAccountType != null">
        F_SET_ACCOUNT_TYPE = #{fSetAccountType,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeID != null">
        F_BILL_TYPE_I_D = #{fBillTypeID,jdbcType=VARCHAR},
      </if>
      <if test="fcustomerid != null">
        FCUSTOMERID = #{fcustomerid,jdbcType=VARCHAR},
      </if>
      <if test="fsettleorgid != null">
        FSETTLEORGID = #{fsettleorgid,jdbcType=VARCHAR},
      </if>
      <if test="fEntityDetail != null">
        F_ENTITY_DETAIL = #{fEntityDetail,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveFeeEntity">
    <!--@mbg.generated-->
    update KING_DEE_RECEIVE_FEE
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      FID = #{fid,jdbcType=VARCHAR},
      FDATE = #{fdate,jdbcType=VARCHAR},
      F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      FBUSINESSTYPE = #{fbusinesstype,jdbcType=VARCHAR},
      F_SET_ACCOUNT_TYPE = #{fSetAccountType,jdbcType=VARCHAR},
      F_BILL_TYPE_I_D = #{fBillTypeID,jdbcType=VARCHAR},
      FCUSTOMERID = #{fcustomerid,jdbcType=VARCHAR},
      FSETTLEORGID = #{fsettleorgid,jdbcType=VARCHAR},
      F_ENTITY_DETAIL = #{fEntityDetail,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update KING_DEE_RECEIVE_FEE
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fid != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FDATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fdate != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fdate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_QZOK_BDDJTID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQzokBddjtid != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQzokBddjtid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FBUSINESSTYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fbusinesstype != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fbusinesstype,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_SET_ACCOUNT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fSetAccountType != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fSetAccountType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_BILL_TYPE_I_D = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fBillTypeID != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fBillTypeID,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FCUSTOMERID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fcustomerid != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fcustomerid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FSETTLEORGID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fsettleorgid != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fsettleorgid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_ENTITY_DETAIL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fEntityDetail != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fEntityDetail,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_RECEIVE_FEE
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, FID, FDATE, F_QZOK_BDDJTID, 
      FBUSINESSTYPE, F_SET_ACCOUNT_TYPE, F_BILL_TYPE_I_D, FCUSTOMERID, FSETTLEORGID, 
      F_ENTITY_DETAIL)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.fid,jdbcType=VARCHAR}, #{item.fdate,jdbcType=VARCHAR}, #{item.fQzokBddjtid,jdbcType=VARCHAR}, 
        #{item.fbusinesstype,jdbcType=VARCHAR}, #{item.fSetAccountType,jdbcType=VARCHAR}, 
        #{item.fBillTypeID,jdbcType=VARCHAR}, #{item.fcustomerid,jdbcType=VARCHAR}, #{item.fsettleorgid,jdbcType=VARCHAR}, 
        #{item.fEntityDetail,jdbcType=VARCHAR})
    </foreach>
  </insert>

</mapper>