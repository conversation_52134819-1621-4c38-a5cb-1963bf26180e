package com.vedeng.erp.saleorder.model.po;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 地区信息表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RegionJd {
    /**
    * 表的主键，UUID
    */
    private String id;

    /**
    * 省ID
    */
    private String jdProvinceCode;

    /**
    * 市ID
    */
    private String jdCityCode;

    /**
    * 区ID
    */
    private String jdAreaCode;

    /**
    * 省-市-区
    */
    private String jdPCAreaName;

    /**
    * 区ID
    */
    private Integer vdRegionId;

    /**
    * 区名
    */
    private String vdRegionName;

    /**
    * 市ID
    */
    private Integer vdCityId;

    /**
    * 市名
    */
    private String vdCityName;

    /**
    * 省ID
    */
    private Integer vdProvinceId;

    /**
    * 省名
    */
    private String vdProvinceName;

    /**
    * 创建时间
    */
    private Date addTime;

}