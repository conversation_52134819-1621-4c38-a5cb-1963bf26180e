package com.vedeng.erp.kingdee.batch.dto;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchCoreSpuDto {
    private Integer spuId;

    /**
    * 分类ID
    */
    private Integer categoryId;

    /**
    * 品牌ID
    */
    private Integer brandId;

    /**
    * SPU
    */
    private String spuNo;

    /**
    * 商品名称
    */
    private String spuName;

    /**
    * 通用名
    */
    private String showName;

    /**
    * 0:其他产品,1:核心产品、2:临时产品、
    */
    private Boolean spuLevel;

    /**
    * 1启用 0禁用 2审核中
    */
    private Boolean status;

    /**
    * 产品类型SYS_OPTION_DEFINITION_ID
    */
    private Integer spuType;

    /**
    * 首营ID
    */
    private Integer firstEngageId;

    /**
    * 注册商标
    */
    private String registrationIcon;

    /**
    * 产品链接
    */
    private String wikiHref;

    /**
    * 是否添加运营信息，1是 0否
    */
    private Boolean operateInfoFlag;

    /**
    * 审核状态 0待完善 1审核中 2审核不通过 3审核通过 4删除 5 待提交审核
    */
    private Boolean checkStatus;

    /**
    * 运营信息ID
    */
    private Integer operateInfoId;

    /**
    * 功能
    */
    private String hospitalTags;

    /**
    * 添加时间
    */
    private Date addTime;

    /**
    * 添加人
    */
    private Integer creator;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 修改人
    */
    private Integer updater;

    /**
    * 审核时间
    */
    private Date checkTime;

    /**
    * 审核人
    */
    private Integer checker;

    /**
    * 禁用原因
    */
    private String deleteReason;

    /**
    * 最后一次审核原因
    */
    private String lastCheckReason;

    /**
    * 归属经理
    */
    private Integer assignmentManagerId;

    /**
    * 归属助理
    */
    private Integer assignmentAssistantId;

    /**
    * 器械类型：1医疗器械，2非医疗器械
    */
    private Integer apparatusType;

    /**
    * 存储条件（温度），1常温0-30℃、2阴凉0-20℃、3冷藏2-10℃、4其他温度
    */
    private Boolean storageConditionTemperature;

    /**
    * 存储条件（温度,单位：摄氏度），其他温度范围值存储较小值
    */
    private Double storageConditionTemperatureLowerValue;

    /**
    * 存储条件（温度，单位：摄氏度），其他温度范围值存储较大值
    */
    private Double storageConditionTemperatureUpperValue;

    /**
    * 存储条件（湿度，单位：%）：范围值存储较小的值
    */
    private Double storageConditionHumidityLowerValue;

    /**
    * 存储条件（湿度，单位：%）：范围值存储较大的值
    */
    private Double storageConditionHumidityUpperValue;

    /**
    * 存储条件（其他），1通风、2干燥、3避光、4防潮、5避热、6密封、7密闭、8严封 9遮光 逗号隔开
    */
    private String storageConditionOthers;

    /**
    * 关联sku的技术参数名称列表（用逗号分隔）
    */
    private String technicalParameterNames;

    /**
    * 二级商品类型（目前只有“大型医疗设备”,便于后期扩展，其上级商品类型为“设备”）
    */
    private Integer secondLevelSpuType;

    /**
    * 规格、型号(用顿号分隔)
    */
    private String specsModel;

    /**
    * 是否在《医疗器械分类目录》，1是，2否
    */
    private Boolean medicalInstrumentCatalogIncluded;

    /**
    * 商品等级
    */
    private Integer goodsLevelNo;

    /**
    * 商品档位
    */
    private Integer goodsPositionNo;

    /**
    * 禁用原因
    */
    private String disabledReason;

    /**
    * 非医疗器械一级分类
    */
    private Integer noMedicalFirstType;

    /**
    * 非医疗器械二级分类
    */
    private Integer noMedicalSecondType;
}