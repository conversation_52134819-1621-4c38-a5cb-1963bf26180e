<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchSaleorderGoodsDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderGoodsDto">
    <!--@mbg.generated-->
    <!--@Table T_SALEORDER_GOODS-->
    <id column="SALEORDER_GOODS_ID" jdbcType="INTEGER" property="saleorderGoodsId" />
    <result column="SALEORDER_ID" jdbcType="INTEGER" property="saleorderId" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName" />
    <result column="BRAND_ID" jdbcType="INTEGER" property="brandId" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="REAL_PRICE" jdbcType="DECIMAL" property="realPrice" />
    <result column="PURCHASING_PRICE" jdbcType="DECIMAL" property="purchasingPrice" />
    <result column="CURRENCY_UNIT_ID" jdbcType="INTEGER" property="currencyUnitId" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="BUY_NUM" jdbcType="INTEGER" property="buyNum" />
    <result column="DELIVERY_NUM" jdbcType="INTEGER" property="deliveryNum" />
    <result column="DELIVERY_STATUS" jdbcType="BOOLEAN" property="deliveryStatus" />
    <result column="DELIVERY_TIME" jdbcType="BIGINT" property="deliveryTime" />
    <result column="DELIVERY_CYCLE" jdbcType="VARCHAR" property="deliveryCycle" />
    <result column="DELIVERY_DIRECT" jdbcType="INTEGER" property="deliveryDirect" />
    <result column="DELIVERY_DIRECT_COMMENTS" jdbcType="VARCHAR" property="deliveryDirectComments" />
    <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName" />
    <result column="REFERENCE_COST_PRICE" jdbcType="DECIMAL" property="referenceCostPrice" />
    <result column="REFERENCE_PRICE" jdbcType="VARCHAR" property="referencePrice" />
    <result column="REFERENCE_DELIVERY_CYCLE" jdbcType="VARCHAR" property="referenceDeliveryCycle" />
    <result column="REPORT_STATUS" jdbcType="BOOLEAN" property="reportStatus" />
    <result column="REPORT_COMMENTS" jdbcType="VARCHAR" property="reportComments" />
    <result column="HAVE_INSTALLATION" jdbcType="BOOLEAN" property="haveInstallation" />
    <result column="GOODS_COMMENTS" jdbcType="VARCHAR" property="goodsComments" />
    <result column="INSIDE_COMMENTS" jdbcType="VARCHAR" property="insideComments" />
    <result column="ARRIVAL_USER_ID" jdbcType="INTEGER" property="arrivalUserId" />
    <result column="ARRIVAL_STATUS" jdbcType="BOOLEAN" property="arrivalStatus" />
    <result column="ARRIVAL_TIME" jdbcType="BIGINT" property="arrivalTime" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="IS_IGNORE" jdbcType="BOOLEAN" property="isIgnore" />
    <result column="IGNORE_TIME" jdbcType="BIGINT" property="ignoreTime" />
    <result column="IGNORE_USER_ID" jdbcType="INTEGER" property="ignoreUserId" />
    <result column="LOCKED_STATUS" jdbcType="BOOLEAN" property="lockedStatus" />
    <result column="MAX_SKU_REFUND_AMOUNT" jdbcType="DECIMAL" property="maxSkuRefundAmount" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="OCCUPY_NUM" jdbcType="INTEGER" property="occupyNum" />
    <result column="IS_ACTION_GOODS" jdbcType="BOOLEAN" property="isActionGoods" />
    <result column="ACTION_OCCUPY_NUM" jdbcType="INTEGER" property="actionOccupyNum" />
    <result column="IS_COUPONS" jdbcType="BOOLEAN" property="isCoupons" />
    <result column="EL_ORDERLIST_ID" jdbcType="INTEGER" property="elOrderlistId" />
    <result column="UPDATE_DATA_TIME" jdbcType="TIMESTAMP" property="updateDataTime" />
    <result column="AFTER_RETURN_AMOUNT" jdbcType="DECIMAL" property="afterReturnAmount" />
    <result column="AFTER_RETURN_NUM" jdbcType="INTEGER" property="afterReturnNum" />
    <result column="REAL_PAY_AMOUNT" jdbcType="DECIMAL" property="realPayAmount" />
    <result column="BUY_PRICE_FLAG" jdbcType="VARCHAR" property="buyPriceFlag" />
    <result column="SKU_TAGS" jdbcType="VARCHAR" property="skuTags" />
    <result column="PRODUCT_AUDIT" jdbcType="INTEGER" property="productAudit" />
    <result column="PRODUCT_AUDIT_UESRID" jdbcType="INTEGER" property="productAuditUesrid" />
    <result column="DIRECT_SUPERIOR_AUDIT" jdbcType="INTEGER" property="directSuperiorAudit" />
    <result column="BUY_PRICE" jdbcType="DECIMAL" property="buyPrice" />
    <result column="HISTORY_AVG_PRICE" jdbcType="DECIMAL" property="historyAvgPrice" />
    <result column="BUYORDER_NOS" jdbcType="VARCHAR" property="buyorderNos" />
    <result column="COMPONENT_ID" jdbcType="INTEGER" property="componentId" />
    <result column="COMPONENT_HTML" jdbcType="VARCHAR" property="componentHtml" />
    <result column="AGING" jdbcType="INTEGER" property="aging" />
    <result column="WARN_LEVEL" jdbcType="INTEGER" property="warnLevel" />
    <result column="AGING_TIME" jdbcType="BIGINT" property="agingTime" />
    <result column="IS_WARN" jdbcType="INTEGER" property="isWarn" />
    <result column="JOIN_BUYORDER_ORDER_TIME" jdbcType="BIGINT" property="joinBuyorderOrderTime" />
    <result column="JOIN_BUYORDER_USER" jdbcType="INTEGER" property="joinBuyorderUser" />
    <result column="BUY_DOCK_USER_ID" jdbcType="INTEGER" property="buyDockUserId" />
    <result column="BUY_PROCESS_MOD_TIME" jdbcType="BIGINT" property="buyProcessModTime" />
    <result column="BUY_PROCESS_MOD_RESON" jdbcType="VARCHAR" property="buyProcessModReson" />
    <result column="PRODUCT_BELONG_ID_INFO" jdbcType="VARCHAR" property="productBelongIdInfo" />
    <result column="PRODUCT_BELONG_NAME_INFO" jdbcType="VARCHAR" property="productBelongNameInfo" />
    <result column="SPECIAL_DELIVERY" jdbcType="INTEGER" property="specialDelivery" />
    <result column="PRODUCT_CHINESE_NAME" jdbcType="VARCHAR" property="productChineseName" />
    <result column="SPEC" jdbcType="VARCHAR" property="spec" />
    <result column="MANUFACTURER_NAME" jdbcType="VARCHAR" property="manufacturerName" />
    <result column="PRODUCT_COMPANY_LICENCE" jdbcType="VARCHAR" property="productCompanyLicence" />
    <result column="PERFERMENCE_DELIVERY_TIME" jdbcType="INTEGER" property="perfermenceDeliveryTime" />
    <result column="Label_CODES" jdbcType="VARCHAR" property="labelCodes" />
    <result column="Label_NAMES" jdbcType="VARCHAR" property="labelNames" />
    <result column="PURCHASE_STATUS_DATA" jdbcType="BOOLEAN" property="purchaseStatusData" />
    <result column="BUY_NUM_DATA" jdbcType="INTEGER" property="buyNumData" />
    <result column="CONFIRM_NUMBER" jdbcType="INTEGER" property="confirmNumber" />
    <result column="VIEW_REAL_DELIVERY_NUM" jdbcType="INTEGER" property="viewRealDeliveryNum" />
    <result column="VIEW_REAL_ARRIVAL_NUM" jdbcType="INTEGER" property="viewRealArrivalNum" />
    <result column="IS_GIFT" jdbcType="BOOLEAN" property="isGift" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SALEORDER_GOODS_ID, SALEORDER_ID, GOODS_ID, SKU, GOODS_NAME, BRAND_NAME, BRAND_ID, 
    MODEL, UNIT_NAME, PRICE, REAL_PRICE, PURCHASING_PRICE, CURRENCY_UNIT_ID, NUM, BUY_NUM, 
    DELIVERY_NUM, DELIVERY_STATUS, DELIVERY_TIME, DELIVERY_CYCLE, DELIVERY_DIRECT, DELIVERY_DIRECT_COMMENTS, 
    REGISTRATION_NUMBER, SUPPLIER_NAME, REFERENCE_COST_PRICE, REFERENCE_PRICE, REFERENCE_DELIVERY_CYCLE, 
    REPORT_STATUS, REPORT_COMMENTS, HAVE_INSTALLATION, GOODS_COMMENTS, INSIDE_COMMENTS, 
    ARRIVAL_USER_ID, ARRIVAL_STATUS, ARRIVAL_TIME, IS_DELETE, IS_IGNORE, IGNORE_TIME, 
    IGNORE_USER_ID, LOCKED_STATUS, MAX_SKU_REFUND_AMOUNT, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER, OCCUPY_NUM, IS_ACTION_GOODS, ACTION_OCCUPY_NUM, IS_COUPONS, EL_ORDERLIST_ID, 
    UPDATE_DATA_TIME, AFTER_RETURN_AMOUNT, AFTER_RETURN_NUM, REAL_PAY_AMOUNT, BUY_PRICE_FLAG, 
    SKU_TAGS, PRODUCT_AUDIT, PRODUCT_AUDIT_UESRID, DIRECT_SUPERIOR_AUDIT, BUY_PRICE, 
    HISTORY_AVG_PRICE, BUYORDER_NOS, COMPONENT_ID, COMPONENT_HTML, AGING, WARN_LEVEL, 
    AGING_TIME, IS_WARN, JOIN_BUYORDER_ORDER_TIME, JOIN_BUYORDER_USER, BUY_DOCK_USER_ID, 
    BUY_PROCESS_MOD_TIME, BUY_PROCESS_MOD_RESON, PRODUCT_BELONG_ID_INFO, PRODUCT_BELONG_NAME_INFO, 
    SPECIAL_DELIVERY, PRODUCT_CHINESE_NAME, SPEC, MANUFACTURER_NAME, PRODUCT_COMPANY_LICENCE, 
    PERFERMENCE_DELIVERY_TIME, Label_CODES, Label_NAMES, PURCHASE_STATUS_DATA, BUY_NUM_DATA, 
    CONFIRM_NUMBER, VIEW_REAL_DELIVERY_NUM, VIEW_REAL_ARRIVAL_NUM, IS_GIFT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_SALEORDER_GOODS
    where SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_SALEORDER_GOODS
    where SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="SALEORDER_GOODS_ID" keyProperty="saleorderGoodsId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderGoodsDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SALEORDER_GOODS (SALEORDER_ID, GOODS_ID, SKU, 
      GOODS_NAME, BRAND_NAME, BRAND_ID, 
      MODEL, UNIT_NAME, PRICE, 
      REAL_PRICE, PURCHASING_PRICE, CURRENCY_UNIT_ID, 
      NUM, BUY_NUM, DELIVERY_NUM, 
      DELIVERY_STATUS, DELIVERY_TIME, DELIVERY_CYCLE, 
      DELIVERY_DIRECT, DELIVERY_DIRECT_COMMENTS, 
      REGISTRATION_NUMBER, SUPPLIER_NAME, REFERENCE_COST_PRICE, 
      REFERENCE_PRICE, REFERENCE_DELIVERY_CYCLE, 
      REPORT_STATUS, REPORT_COMMENTS, HAVE_INSTALLATION, 
      GOODS_COMMENTS, INSIDE_COMMENTS, ARRIVAL_USER_ID, 
      ARRIVAL_STATUS, ARRIVAL_TIME, IS_DELETE, 
      IS_IGNORE, IGNORE_TIME, IGNORE_USER_ID, 
      LOCKED_STATUS, MAX_SKU_REFUND_AMOUNT, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER, 
      OCCUPY_NUM, IS_ACTION_GOODS, ACTION_OCCUPY_NUM, 
      IS_COUPONS, EL_ORDERLIST_ID, UPDATE_DATA_TIME, 
      AFTER_RETURN_AMOUNT, AFTER_RETURN_NUM, REAL_PAY_AMOUNT, 
      BUY_PRICE_FLAG, SKU_TAGS, PRODUCT_AUDIT, 
      PRODUCT_AUDIT_UESRID, DIRECT_SUPERIOR_AUDIT, 
      BUY_PRICE, HISTORY_AVG_PRICE, BUYORDER_NOS, 
      COMPONENT_ID, COMPONENT_HTML, AGING, 
      WARN_LEVEL, AGING_TIME, IS_WARN, 
      JOIN_BUYORDER_ORDER_TIME, JOIN_BUYORDER_USER, 
      BUY_DOCK_USER_ID, BUY_PROCESS_MOD_TIME, BUY_PROCESS_MOD_RESON, 
      PRODUCT_BELONG_ID_INFO, PRODUCT_BELONG_NAME_INFO, 
      SPECIAL_DELIVERY, PRODUCT_CHINESE_NAME, SPEC, 
      MANUFACTURER_NAME, PRODUCT_COMPANY_LICENCE, 
      PERFERMENCE_DELIVERY_TIME, Label_CODES, Label_NAMES, 
      PURCHASE_STATUS_DATA, BUY_NUM_DATA, CONFIRM_NUMBER, 
      VIEW_REAL_DELIVERY_NUM, VIEW_REAL_ARRIVAL_NUM, 
      IS_GIFT)
    values (#{saleorderId,jdbcType=INTEGER}, #{goodsId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, 
      #{goodsName,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR}, #{brandId,jdbcType=INTEGER}, 
      #{model,jdbcType=VARCHAR}, #{unitName,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, 
      #{realPrice,jdbcType=DECIMAL}, #{purchasingPrice,jdbcType=DECIMAL}, #{currencyUnitId,jdbcType=INTEGER}, 
      #{num,jdbcType=INTEGER}, #{buyNum,jdbcType=INTEGER}, #{deliveryNum,jdbcType=INTEGER}, 
      #{deliveryStatus,jdbcType=BOOLEAN}, #{deliveryTime,jdbcType=BIGINT}, #{deliveryCycle,jdbcType=VARCHAR}, 
      #{deliveryDirect,jdbcType=BOOLEAN}, #{deliveryDirectComments,jdbcType=VARCHAR}, 
      #{registrationNumber,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, #{referenceCostPrice,jdbcType=DECIMAL}, 
      #{referencePrice,jdbcType=VARCHAR}, #{referenceDeliveryCycle,jdbcType=VARCHAR}, 
      #{reportStatus,jdbcType=BOOLEAN}, #{reportComments,jdbcType=VARCHAR}, #{haveInstallation,jdbcType=BOOLEAN}, 
      #{goodsComments,jdbcType=VARCHAR}, #{insideComments,jdbcType=VARCHAR}, #{arrivalUserId,jdbcType=INTEGER}, 
      #{arrivalStatus,jdbcType=BOOLEAN}, #{arrivalTime,jdbcType=BIGINT}, #{isDelete,jdbcType=BOOLEAN}, 
      #{isIgnore,jdbcType=BOOLEAN}, #{ignoreTime,jdbcType=BIGINT}, #{ignoreUserId,jdbcType=INTEGER}, 
      #{lockedStatus,jdbcType=BOOLEAN}, #{maxSkuRefundAmount,jdbcType=DECIMAL}, #{addTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, 
      #{occupyNum,jdbcType=INTEGER}, #{isActionGoods,jdbcType=BOOLEAN}, #{actionOccupyNum,jdbcType=INTEGER}, 
      #{isCoupons,jdbcType=BOOLEAN}, #{elOrderlistId,jdbcType=INTEGER}, #{updateDataTime,jdbcType=TIMESTAMP}, 
      #{afterReturnAmount,jdbcType=DECIMAL}, #{afterReturnNum,jdbcType=INTEGER}, #{realPayAmount,jdbcType=DECIMAL}, 
      #{buyPriceFlag,jdbcType=VARCHAR}, #{skuTags,jdbcType=VARCHAR}, #{productAudit,jdbcType=INTEGER}, 
      #{productAuditUesrid,jdbcType=INTEGER}, #{directSuperiorAudit,jdbcType=INTEGER}, 
      #{buyPrice,jdbcType=DECIMAL}, #{historyAvgPrice,jdbcType=DECIMAL}, #{buyorderNos,jdbcType=VARCHAR}, 
      #{componentId,jdbcType=INTEGER}, #{componentHtml,jdbcType=VARCHAR}, #{aging,jdbcType=INTEGER}, 
      #{warnLevel,jdbcType=INTEGER}, #{agingTime,jdbcType=BIGINT}, #{isWarn,jdbcType=INTEGER}, 
      #{joinBuyorderOrderTime,jdbcType=BIGINT}, #{joinBuyorderUser,jdbcType=INTEGER}, 
      #{buyDockUserId,jdbcType=INTEGER}, #{buyProcessModTime,jdbcType=BIGINT}, #{buyProcessModReson,jdbcType=VARCHAR}, 
      #{productBelongIdInfo,jdbcType=VARCHAR}, #{productBelongNameInfo,jdbcType=VARCHAR}, 
      #{specialDelivery,jdbcType=INTEGER}, #{productChineseName,jdbcType=VARCHAR}, #{spec,jdbcType=VARCHAR}, 
      #{manufacturerName,jdbcType=VARCHAR}, #{productCompanyLicence,jdbcType=VARCHAR}, 
      #{perfermenceDeliveryTime,jdbcType=INTEGER}, #{labelCodes,jdbcType=VARCHAR}, #{labelNames,jdbcType=VARCHAR}, 
      #{purchaseStatusData,jdbcType=BOOLEAN}, #{buyNumData,jdbcType=INTEGER}, #{confirmNumber,jdbcType=INTEGER}, 
      #{viewRealDeliveryNum,jdbcType=INTEGER}, #{viewRealArrivalNum,jdbcType=INTEGER}, 
      #{isGift,jdbcType=BOOLEAN})
  </insert>
  <insert id="insertSelective" keyColumn="SALEORDER_GOODS_ID" keyProperty="saleorderGoodsId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderGoodsDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SALEORDER_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        SALEORDER_ID,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="goodsName != null">
        GOODS_NAME,
      </if>
      <if test="brandName != null">
        BRAND_NAME,
      </if>
      <if test="brandId != null">
        BRAND_ID,
      </if>
      <if test="model != null">
        MODEL,
      </if>
      <if test="unitName != null">
        UNIT_NAME,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="realPrice != null">
        REAL_PRICE,
      </if>
      <if test="purchasingPrice != null">
        PURCHASING_PRICE,
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="buyNum != null">
        BUY_NUM,
      </if>
      <if test="deliveryNum != null">
        DELIVERY_NUM,
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS,
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME,
      </if>
      <if test="deliveryCycle != null">
        DELIVERY_CYCLE,
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT,
      </if>
      <if test="deliveryDirectComments != null">
        DELIVERY_DIRECT_COMMENTS,
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER,
      </if>
      <if test="supplierName != null">
        SUPPLIER_NAME,
      </if>
      <if test="referenceCostPrice != null">
        REFERENCE_COST_PRICE,
      </if>
      <if test="referencePrice != null">
        REFERENCE_PRICE,
      </if>
      <if test="referenceDeliveryCycle != null">
        REFERENCE_DELIVERY_CYCLE,
      </if>
      <if test="reportStatus != null">
        REPORT_STATUS,
      </if>
      <if test="reportComments != null">
        REPORT_COMMENTS,
      </if>
      <if test="haveInstallation != null">
        HAVE_INSTALLATION,
      </if>
      <if test="goodsComments != null">
        GOODS_COMMENTS,
      </if>
      <if test="insideComments != null">
        INSIDE_COMMENTS,
      </if>
      <if test="arrivalUserId != null">
        ARRIVAL_USER_ID,
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="isIgnore != null">
        IS_IGNORE,
      </if>
      <if test="ignoreTime != null">
        IGNORE_TIME,
      </if>
      <if test="ignoreUserId != null">
        IGNORE_USER_ID,
      </if>
      <if test="lockedStatus != null">
        LOCKED_STATUS,
      </if>
      <if test="maxSkuRefundAmount != null">
        MAX_SKU_REFUND_AMOUNT,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="occupyNum != null">
        OCCUPY_NUM,
      </if>
      <if test="isActionGoods != null">
        IS_ACTION_GOODS,
      </if>
      <if test="actionOccupyNum != null">
        ACTION_OCCUPY_NUM,
      </if>
      <if test="isCoupons != null">
        IS_COUPONS,
      </if>
      <if test="elOrderlistId != null">
        EL_ORDERLIST_ID,
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME,
      </if>
      <if test="afterReturnAmount != null">
        AFTER_RETURN_AMOUNT,
      </if>
      <if test="afterReturnNum != null">
        AFTER_RETURN_NUM,
      </if>
      <if test="realPayAmount != null">
        REAL_PAY_AMOUNT,
      </if>
      <if test="buyPriceFlag != null">
        BUY_PRICE_FLAG,
      </if>
      <if test="skuTags != null">
        SKU_TAGS,
      </if>
      <if test="productAudit != null">
        PRODUCT_AUDIT,
      </if>
      <if test="productAuditUesrid != null">
        PRODUCT_AUDIT_UESRID,
      </if>
      <if test="directSuperiorAudit != null">
        DIRECT_SUPERIOR_AUDIT,
      </if>
      <if test="buyPrice != null">
        BUY_PRICE,
      </if>
      <if test="historyAvgPrice != null">
        HISTORY_AVG_PRICE,
      </if>
      <if test="buyorderNos != null">
        BUYORDER_NOS,
      </if>
      <if test="componentId != null">
        COMPONENT_ID,
      </if>
      <if test="componentHtml != null">
        COMPONENT_HTML,
      </if>
      <if test="aging != null">
        AGING,
      </if>
      <if test="warnLevel != null">
        WARN_LEVEL,
      </if>
      <if test="agingTime != null">
        AGING_TIME,
      </if>
      <if test="isWarn != null">
        IS_WARN,
      </if>
      <if test="joinBuyorderOrderTime != null">
        JOIN_BUYORDER_ORDER_TIME,
      </if>
      <if test="joinBuyorderUser != null">
        JOIN_BUYORDER_USER,
      </if>
      <if test="buyDockUserId != null">
        BUY_DOCK_USER_ID,
      </if>
      <if test="buyProcessModTime != null">
        BUY_PROCESS_MOD_TIME,
      </if>
      <if test="buyProcessModReson != null">
        BUY_PROCESS_MOD_RESON,
      </if>
      <if test="productBelongIdInfo != null">
        PRODUCT_BELONG_ID_INFO,
      </if>
      <if test="productBelongNameInfo != null">
        PRODUCT_BELONG_NAME_INFO,
      </if>
      <if test="specialDelivery != null">
        SPECIAL_DELIVERY,
      </if>
      <if test="productChineseName != null">
        PRODUCT_CHINESE_NAME,
      </if>
      <if test="spec != null">
        SPEC,
      </if>
      <if test="manufacturerName != null">
        MANUFACTURER_NAME,
      </if>
      <if test="productCompanyLicence != null">
        PRODUCT_COMPANY_LICENCE,
      </if>
      <if test="perfermenceDeliveryTime != null">
        PERFERMENCE_DELIVERY_TIME,
      </if>
      <if test="labelCodes != null">
        Label_CODES,
      </if>
      <if test="labelNames != null">
        Label_NAMES,
      </if>
      <if test="purchaseStatusData != null">
        PURCHASE_STATUS_DATA,
      </if>
      <if test="buyNumData != null">
        BUY_NUM_DATA,
      </if>
      <if test="confirmNumber != null">
        CONFIRM_NUMBER,
      </if>
      <if test="viewRealDeliveryNum != null">
        VIEW_REAL_DELIVERY_NUM,
      </if>
      <if test="viewRealArrivalNum != null">
        VIEW_REAL_ARRIVAL_NUM,
      </if>
      <if test="isGift != null">
        IS_GIFT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=INTEGER},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="realPrice != null">
        #{realPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchasingPrice != null">
        #{purchasingPrice,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null">
        #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="buyNum != null">
        #{buyNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryNum != null">
        #{deliveryNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryCycle != null">
        #{deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null">
        #{deliveryDirect,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryDirectComments != null">
        #{deliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="referenceCostPrice != null">
        #{referenceCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="referencePrice != null">
        #{referencePrice,jdbcType=VARCHAR},
      </if>
      <if test="referenceDeliveryCycle != null">
        #{referenceDeliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        #{reportStatus,jdbcType=BOOLEAN},
      </if>
      <if test="reportComments != null">
        #{reportComments,jdbcType=VARCHAR},
      </if>
      <if test="haveInstallation != null">
        #{haveInstallation,jdbcType=BOOLEAN},
      </if>
      <if test="goodsComments != null">
        #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="insideComments != null">
        #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="arrivalUserId != null">
        #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null">
        #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="isIgnore != null">
        #{isIgnore,jdbcType=BOOLEAN},
      </if>
      <if test="ignoreTime != null">
        #{ignoreTime,jdbcType=BIGINT},
      </if>
      <if test="ignoreUserId != null">
        #{ignoreUserId,jdbcType=INTEGER},
      </if>
      <if test="lockedStatus != null">
        #{lockedStatus,jdbcType=BOOLEAN},
      </if>
      <if test="maxSkuRefundAmount != null">
        #{maxSkuRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="occupyNum != null">
        #{occupyNum,jdbcType=INTEGER},
      </if>
      <if test="isActionGoods != null">
        #{isActionGoods,jdbcType=BOOLEAN},
      </if>
      <if test="actionOccupyNum != null">
        #{actionOccupyNum,jdbcType=INTEGER},
      </if>
      <if test="isCoupons != null">
        #{isCoupons,jdbcType=BOOLEAN},
      </if>
      <if test="elOrderlistId != null">
        #{elOrderlistId,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null">
        #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="afterReturnAmount != null">
        #{afterReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterReturnNum != null">
        #{afterReturnNum,jdbcType=INTEGER},
      </if>
      <if test="realPayAmount != null">
        #{realPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="buyPriceFlag != null">
        #{buyPriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="skuTags != null">
        #{skuTags,jdbcType=VARCHAR},
      </if>
      <if test="productAudit != null">
        #{productAudit,jdbcType=INTEGER},
      </if>
      <if test="productAuditUesrid != null">
        #{productAuditUesrid,jdbcType=INTEGER},
      </if>
      <if test="directSuperiorAudit != null">
        #{directSuperiorAudit,jdbcType=INTEGER},
      </if>
      <if test="buyPrice != null">
        #{buyPrice,jdbcType=DECIMAL},
      </if>
      <if test="historyAvgPrice != null">
        #{historyAvgPrice,jdbcType=DECIMAL},
      </if>
      <if test="buyorderNos != null">
        #{buyorderNos,jdbcType=VARCHAR},
      </if>
      <if test="componentId != null">
        #{componentId,jdbcType=INTEGER},
      </if>
      <if test="componentHtml != null">
        #{componentHtml,jdbcType=VARCHAR},
      </if>
      <if test="aging != null">
        #{aging,jdbcType=INTEGER},
      </if>
      <if test="warnLevel != null">
        #{warnLevel,jdbcType=INTEGER},
      </if>
      <if test="agingTime != null">
        #{agingTime,jdbcType=BIGINT},
      </if>
      <if test="isWarn != null">
        #{isWarn,jdbcType=INTEGER},
      </if>
      <if test="joinBuyorderOrderTime != null">
        #{joinBuyorderOrderTime,jdbcType=BIGINT},
      </if>
      <if test="joinBuyorderUser != null">
        #{joinBuyorderUser,jdbcType=INTEGER},
      </if>
      <if test="buyDockUserId != null">
        #{buyDockUserId,jdbcType=INTEGER},
      </if>
      <if test="buyProcessModTime != null">
        #{buyProcessModTime,jdbcType=BIGINT},
      </if>
      <if test="buyProcessModReson != null">
        #{buyProcessModReson,jdbcType=VARCHAR},
      </if>
      <if test="productBelongIdInfo != null">
        #{productBelongIdInfo,jdbcType=VARCHAR},
      </if>
      <if test="productBelongNameInfo != null">
        #{productBelongNameInfo,jdbcType=VARCHAR},
      </if>
      <if test="specialDelivery != null">
        #{specialDelivery,jdbcType=INTEGER},
      </if>
      <if test="productChineseName != null">
        #{productChineseName,jdbcType=VARCHAR},
      </if>
      <if test="spec != null">
        #{spec,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerName != null">
        #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="productCompanyLicence != null">
        #{productCompanyLicence,jdbcType=VARCHAR},
      </if>
      <if test="perfermenceDeliveryTime != null">
        #{perfermenceDeliveryTime,jdbcType=INTEGER},
      </if>
      <if test="labelCodes != null">
        #{labelCodes,jdbcType=VARCHAR},
      </if>
      <if test="labelNames != null">
        #{labelNames,jdbcType=VARCHAR},
      </if>
      <if test="purchaseStatusData != null">
        #{purchaseStatusData,jdbcType=BOOLEAN},
      </if>
      <if test="buyNumData != null">
        #{buyNumData,jdbcType=INTEGER},
      </if>
      <if test="confirmNumber != null">
        #{confirmNumber,jdbcType=INTEGER},
      </if>
      <if test="viewRealDeliveryNum != null">
        #{viewRealDeliveryNum,jdbcType=INTEGER},
      </if>
      <if test="viewRealArrivalNum != null">
        #{viewRealArrivalNum,jdbcType=INTEGER},
      </if>
      <if test="isGift != null">
        #{isGift,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderGoodsDto">
    <!--@mbg.generated-->
    update T_SALEORDER_GOODS
    <set>
      <if test="saleorderId != null">
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        BRAND_ID = #{brandId,jdbcType=INTEGER},
      </if>
      <if test="model != null">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="realPrice != null">
        REAL_PRICE = #{realPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchasingPrice != null">
        PURCHASING_PRICE = #{purchasingPrice,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="buyNum != null">
        BUY_NUM = #{buyNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryNum != null">
        DELIVERY_NUM = #{deliveryNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS = #{deliveryStatus,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryCycle != null">
        DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT = #{deliveryDirect,jdbcType=INTEGER},
      </if>
      <if test="deliveryDirectComments != null">
        DELIVERY_DIRECT_COMMENTS = #{deliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="referenceCostPrice != null">
        REFERENCE_COST_PRICE = #{referenceCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="referencePrice != null">
        REFERENCE_PRICE = #{referencePrice,jdbcType=VARCHAR},
      </if>
      <if test="referenceDeliveryCycle != null">
        REFERENCE_DELIVERY_CYCLE = #{referenceDeliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        REPORT_STATUS = #{reportStatus,jdbcType=BOOLEAN},
      </if>
      <if test="reportComments != null">
        REPORT_COMMENTS = #{reportComments,jdbcType=VARCHAR},
      </if>
      <if test="haveInstallation != null">
        HAVE_INSTALLATION = #{haveInstallation,jdbcType=BOOLEAN},
      </if>
      <if test="goodsComments != null">
        GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="insideComments != null">
        INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="arrivalUserId != null">
        ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="isIgnore != null">
        IS_IGNORE = #{isIgnore,jdbcType=BOOLEAN},
      </if>
      <if test="ignoreTime != null">
        IGNORE_TIME = #{ignoreTime,jdbcType=BIGINT},
      </if>
      <if test="ignoreUserId != null">
        IGNORE_USER_ID = #{ignoreUserId,jdbcType=INTEGER},
      </if>
      <if test="lockedStatus != null">
        LOCKED_STATUS = #{lockedStatus,jdbcType=BOOLEAN},
      </if>
      <if test="maxSkuRefundAmount != null">
        MAX_SKU_REFUND_AMOUNT = #{maxSkuRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="occupyNum != null">
        OCCUPY_NUM = #{occupyNum,jdbcType=INTEGER},
      </if>
      <if test="isActionGoods != null">
        IS_ACTION_GOODS = #{isActionGoods,jdbcType=BOOLEAN},
      </if>
      <if test="actionOccupyNum != null">
        ACTION_OCCUPY_NUM = #{actionOccupyNum,jdbcType=INTEGER},
      </if>
      <if test="isCoupons != null">
        IS_COUPONS = #{isCoupons,jdbcType=BOOLEAN},
      </if>
      <if test="elOrderlistId != null">
        EL_ORDERLIST_ID = #{elOrderlistId,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="afterReturnAmount != null">
        AFTER_RETURN_AMOUNT = #{afterReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterReturnNum != null">
        AFTER_RETURN_NUM = #{afterReturnNum,jdbcType=INTEGER},
      </if>
      <if test="realPayAmount != null">
        REAL_PAY_AMOUNT = #{realPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="buyPriceFlag != null">
        BUY_PRICE_FLAG = #{buyPriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="skuTags != null">
        SKU_TAGS = #{skuTags,jdbcType=VARCHAR},
      </if>
      <if test="productAudit != null">
        PRODUCT_AUDIT = #{productAudit,jdbcType=INTEGER},
      </if>
      <if test="productAuditUesrid != null">
        PRODUCT_AUDIT_UESRID = #{productAuditUesrid,jdbcType=INTEGER},
      </if>
      <if test="directSuperiorAudit != null">
        DIRECT_SUPERIOR_AUDIT = #{directSuperiorAudit,jdbcType=INTEGER},
      </if>
      <if test="buyPrice != null">
        BUY_PRICE = #{buyPrice,jdbcType=DECIMAL},
      </if>
      <if test="historyAvgPrice != null">
        HISTORY_AVG_PRICE = #{historyAvgPrice,jdbcType=DECIMAL},
      </if>
      <if test="buyorderNos != null">
        BUYORDER_NOS = #{buyorderNos,jdbcType=VARCHAR},
      </if>
      <if test="componentId != null">
        COMPONENT_ID = #{componentId,jdbcType=INTEGER},
      </if>
      <if test="componentHtml != null">
        COMPONENT_HTML = #{componentHtml,jdbcType=VARCHAR},
      </if>
      <if test="aging != null">
        AGING = #{aging,jdbcType=INTEGER},
      </if>
      <if test="warnLevel != null">
        WARN_LEVEL = #{warnLevel,jdbcType=INTEGER},
      </if>
      <if test="agingTime != null">
        AGING_TIME = #{agingTime,jdbcType=BIGINT},
      </if>
      <if test="isWarn != null">
        IS_WARN = #{isWarn,jdbcType=INTEGER},
      </if>
      <if test="joinBuyorderOrderTime != null">
        JOIN_BUYORDER_ORDER_TIME = #{joinBuyorderOrderTime,jdbcType=BIGINT},
      </if>
      <if test="joinBuyorderUser != null">
        JOIN_BUYORDER_USER = #{joinBuyorderUser,jdbcType=INTEGER},
      </if>
      <if test="buyDockUserId != null">
        BUY_DOCK_USER_ID = #{buyDockUserId,jdbcType=INTEGER},
      </if>
      <if test="buyProcessModTime != null">
        BUY_PROCESS_MOD_TIME = #{buyProcessModTime,jdbcType=BIGINT},
      </if>
      <if test="buyProcessModReson != null">
        BUY_PROCESS_MOD_RESON = #{buyProcessModReson,jdbcType=VARCHAR},
      </if>
      <if test="productBelongIdInfo != null">
        PRODUCT_BELONG_ID_INFO = #{productBelongIdInfo,jdbcType=VARCHAR},
      </if>
      <if test="productBelongNameInfo != null">
        PRODUCT_BELONG_NAME_INFO = #{productBelongNameInfo,jdbcType=VARCHAR},
      </if>
      <if test="specialDelivery != null">
        SPECIAL_DELIVERY = #{specialDelivery,jdbcType=INTEGER},
      </if>
      <if test="productChineseName != null">
        PRODUCT_CHINESE_NAME = #{productChineseName,jdbcType=VARCHAR},
      </if>
      <if test="spec != null">
        SPEC = #{spec,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerName != null">
        MANUFACTURER_NAME = #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="productCompanyLicence != null">
        PRODUCT_COMPANY_LICENCE = #{productCompanyLicence,jdbcType=VARCHAR},
      </if>
      <if test="perfermenceDeliveryTime != null">
        PERFERMENCE_DELIVERY_TIME = #{perfermenceDeliveryTime,jdbcType=INTEGER},
      </if>
      <if test="labelCodes != null">
        Label_CODES = #{labelCodes,jdbcType=VARCHAR},
      </if>
      <if test="labelNames != null">
        Label_NAMES = #{labelNames,jdbcType=VARCHAR},
      </if>
      <if test="purchaseStatusData != null">
        PURCHASE_STATUS_DATA = #{purchaseStatusData,jdbcType=BOOLEAN},
      </if>
      <if test="buyNumData != null">
        BUY_NUM_DATA = #{buyNumData,jdbcType=INTEGER},
      </if>
      <if test="confirmNumber != null">
        CONFIRM_NUMBER = #{confirmNumber,jdbcType=INTEGER},
      </if>
      <if test="viewRealDeliveryNum != null">
        VIEW_REAL_DELIVERY_NUM = #{viewRealDeliveryNum,jdbcType=INTEGER},
      </if>
      <if test="viewRealArrivalNum != null">
        VIEW_REAL_ARRIVAL_NUM = #{viewRealArrivalNum,jdbcType=INTEGER},
      </if>
      <if test="isGift != null">
        IS_GIFT = #{isGift,jdbcType=BOOLEAN},
      </if>
    </set>
    where SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderGoodsDto">
    <!--@mbg.generated-->
    update T_SALEORDER_GOODS
    set SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      BRAND_ID = #{brandId,jdbcType=INTEGER},
      MODEL = #{model,jdbcType=VARCHAR},
      UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      PRICE = #{price,jdbcType=DECIMAL},
      REAL_PRICE = #{realPrice,jdbcType=DECIMAL},
      PURCHASING_PRICE = #{purchasingPrice,jdbcType=DECIMAL},
      CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      BUY_NUM = #{buyNum,jdbcType=INTEGER},
      DELIVERY_NUM = #{deliveryNum,jdbcType=INTEGER},
      DELIVERY_STATUS = #{deliveryStatus,jdbcType=BOOLEAN},
      DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
      DELIVERY_DIRECT = #{deliveryDirect,jdbcType=INTEGER},
      DELIVERY_DIRECT_COMMENTS = #{deliveryDirectComments,jdbcType=VARCHAR},
      REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
      REFERENCE_COST_PRICE = #{referenceCostPrice,jdbcType=DECIMAL},
      REFERENCE_PRICE = #{referencePrice,jdbcType=VARCHAR},
      REFERENCE_DELIVERY_CYCLE = #{referenceDeliveryCycle,jdbcType=VARCHAR},
      REPORT_STATUS = #{reportStatus,jdbcType=BOOLEAN},
      REPORT_COMMENTS = #{reportComments,jdbcType=VARCHAR},
      HAVE_INSTALLATION = #{haveInstallation,jdbcType=BOOLEAN},
      GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      IS_IGNORE = #{isIgnore,jdbcType=BOOLEAN},
      IGNORE_TIME = #{ignoreTime,jdbcType=BIGINT},
      IGNORE_USER_ID = #{ignoreUserId,jdbcType=INTEGER},
      LOCKED_STATUS = #{lockedStatus,jdbcType=BOOLEAN},
      MAX_SKU_REFUND_AMOUNT = #{maxSkuRefundAmount,jdbcType=DECIMAL},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      OCCUPY_NUM = #{occupyNum,jdbcType=INTEGER},
      IS_ACTION_GOODS = #{isActionGoods,jdbcType=BOOLEAN},
      ACTION_OCCUPY_NUM = #{actionOccupyNum,jdbcType=INTEGER},
      IS_COUPONS = #{isCoupons,jdbcType=BOOLEAN},
      EL_ORDERLIST_ID = #{elOrderlistId,jdbcType=INTEGER},
      UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      AFTER_RETURN_AMOUNT = #{afterReturnAmount,jdbcType=DECIMAL},
      AFTER_RETURN_NUM = #{afterReturnNum,jdbcType=INTEGER},
      REAL_PAY_AMOUNT = #{realPayAmount,jdbcType=DECIMAL},
      BUY_PRICE_FLAG = #{buyPriceFlag,jdbcType=VARCHAR},
      SKU_TAGS = #{skuTags,jdbcType=VARCHAR},
      PRODUCT_AUDIT = #{productAudit,jdbcType=INTEGER},
      PRODUCT_AUDIT_UESRID = #{productAuditUesrid,jdbcType=INTEGER},
      DIRECT_SUPERIOR_AUDIT = #{directSuperiorAudit,jdbcType=INTEGER},
      BUY_PRICE = #{buyPrice,jdbcType=DECIMAL},
      HISTORY_AVG_PRICE = #{historyAvgPrice,jdbcType=DECIMAL},
      BUYORDER_NOS = #{buyorderNos,jdbcType=VARCHAR},
      COMPONENT_ID = #{componentId,jdbcType=INTEGER},
      COMPONENT_HTML = #{componentHtml,jdbcType=VARCHAR},
      AGING = #{aging,jdbcType=INTEGER},
      WARN_LEVEL = #{warnLevel,jdbcType=INTEGER},
      AGING_TIME = #{agingTime,jdbcType=BIGINT},
      IS_WARN = #{isWarn,jdbcType=INTEGER},
      JOIN_BUYORDER_ORDER_TIME = #{joinBuyorderOrderTime,jdbcType=BIGINT},
      JOIN_BUYORDER_USER = #{joinBuyorderUser,jdbcType=INTEGER},
      BUY_DOCK_USER_ID = #{buyDockUserId,jdbcType=INTEGER},
      BUY_PROCESS_MOD_TIME = #{buyProcessModTime,jdbcType=BIGINT},
      BUY_PROCESS_MOD_RESON = #{buyProcessModReson,jdbcType=VARCHAR},
      PRODUCT_BELONG_ID_INFO = #{productBelongIdInfo,jdbcType=VARCHAR},
      PRODUCT_BELONG_NAME_INFO = #{productBelongNameInfo,jdbcType=VARCHAR},
      SPECIAL_DELIVERY = #{specialDelivery,jdbcType=INTEGER},
      PRODUCT_CHINESE_NAME = #{productChineseName,jdbcType=VARCHAR},
      SPEC = #{spec,jdbcType=VARCHAR},
      MANUFACTURER_NAME = #{manufacturerName,jdbcType=VARCHAR},
      PRODUCT_COMPANY_LICENCE = #{productCompanyLicence,jdbcType=VARCHAR},
      PERFERMENCE_DELIVERY_TIME = #{perfermenceDeliveryTime,jdbcType=INTEGER},
      Label_CODES = #{labelCodes,jdbcType=VARCHAR},
      Label_NAMES = #{labelNames,jdbcType=VARCHAR},
      PURCHASE_STATUS_DATA = #{purchaseStatusData,jdbcType=BOOLEAN},
      BUY_NUM_DATA = #{buyNumData,jdbcType=INTEGER},
      CONFIRM_NUMBER = #{confirmNumber,jdbcType=INTEGER},
      VIEW_REAL_DELIVERY_NUM = #{viewRealDeliveryNum,jdbcType=INTEGER},
      VIEW_REAL_ARRIVAL_NUM = #{viewRealArrivalNum,jdbcType=INTEGER},
      IS_GIFT = #{isGift,jdbcType=BOOLEAN}
    where SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_SALEORDER_GOODS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="SALEORDER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.saleorderId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="GOODS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.goodsId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SKU = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.sku,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="GOODS_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.goodsName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BRAND_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.brandName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BRAND_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.brandId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MODEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.model,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UNIT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.unitName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.price,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="REAL_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.realPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="PURCHASING_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.purchasingPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="CURRENCY_UNIT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.currencyUnitId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="BUY_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.deliveryNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.deliveryStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.deliveryTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_CYCLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.deliveryCycle,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_DIRECT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.deliveryDirect,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_DIRECT_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.deliveryDirectComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="REGISTRATION_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.registrationNumber,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SUPPLIER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.supplierName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="REFERENCE_COST_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.referenceCostPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="REFERENCE_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.referencePrice,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="REFERENCE_DELIVERY_CYCLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.referenceDeliveryCycle,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="REPORT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.reportStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="REPORT_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.reportComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="HAVE_INSTALLATION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.haveInstallation,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="GOODS_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.goodsComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INSIDE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.insideComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.arrivalUserId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.arrivalStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.arrivalTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_IGNORE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.isIgnore,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IGNORE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.ignoreTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="IGNORE_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.ignoreUserId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="LOCKED_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.lockedStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="MAX_SKU_REFUND_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.maxSkuRefundAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="OCCUPY_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.occupyNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_ACTION_GOODS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.isActionGoods,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="ACTION_OCCUPY_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.actionOccupyNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_COUPONS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.isCoupons,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="EL_ORDERLIST_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.elOrderlistId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATE_DATA_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.updateDataTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="AFTER_RETURN_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.afterReturnAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="AFTER_RETURN_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.afterReturnNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="REAL_PAY_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.realPayAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="BUY_PRICE_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyPriceFlag,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SKU_TAGS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.skuTags,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PRODUCT_AUDIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.productAudit,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PRODUCT_AUDIT_UESRID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.productAuditUesrid,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DIRECT_SUPERIOR_AUDIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.directSuperiorAudit,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="BUY_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="HISTORY_AVG_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.historyAvgPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="BUYORDER_NOS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyorderNos,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="COMPONENT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.componentId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="COMPONENT_HTML = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.componentHtml,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="AGING = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.aging,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="WARN_LEVEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.warnLevel,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="AGING_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.agingTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="IS_WARN = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.isWarn,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="JOIN_BUYORDER_ORDER_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.joinBuyorderOrderTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="JOIN_BUYORDER_USER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.joinBuyorderUser,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="BUY_DOCK_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyDockUserId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="BUY_PROCESS_MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyProcessModTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="BUY_PROCESS_MOD_RESON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyProcessModReson,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PRODUCT_BELONG_ID_INFO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.productBelongIdInfo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PRODUCT_BELONG_NAME_INFO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.productBelongNameInfo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SPECIAL_DELIVERY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.specialDelivery,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PRODUCT_CHINESE_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.productChineseName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SPEC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.spec,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MANUFACTURER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.manufacturerName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PRODUCT_COMPANY_LICENCE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.productCompanyLicence,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PERFERMENCE_DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.perfermenceDeliveryTime,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="Label_CODES = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.labelCodes,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="Label_NAMES = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.labelNames,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PURCHASE_STATUS_DATA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.purchaseStatusData,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="BUY_NUM_DATA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyNumData,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CONFIRM_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.confirmNumber,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="VIEW_REAL_DELIVERY_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.viewRealDeliveryNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="VIEW_REAL_ARRIVAL_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.viewRealArrivalNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_GIFT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.isGift,jdbcType=BOOLEAN}
        </foreach>
      </trim>
    </trim>
    where SALEORDER_GOODS_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.saleorderGoodsId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_SALEORDER_GOODS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="SALEORDER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.saleorderId != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.saleorderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsId != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.goodsId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sku != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.sku,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsName != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.goodsName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BRAND_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.brandName != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.brandName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BRAND_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.brandId != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.brandId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MODEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.model != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.model,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UNIT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.unitName != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.unitName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.price != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.price,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="REAL_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.realPrice != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.realPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="PURCHASING_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.purchasingPrice != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.purchasingPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="CURRENCY_UNIT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.currencyUnitId != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.currencyUnitId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.num != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUY_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.buyNum != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryNum != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.deliveryNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryStatus != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.deliveryStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryTime != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.deliveryTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_CYCLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryCycle != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.deliveryCycle,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_DIRECT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryDirect != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.deliveryDirect,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_DIRECT_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryDirectComments != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.deliveryDirectComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="REGISTRATION_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.registrationNumber != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.registrationNumber,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SUPPLIER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.supplierName != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.supplierName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="REFERENCE_COST_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.referenceCostPrice != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.referenceCostPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="REFERENCE_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.referencePrice != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.referencePrice,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="REFERENCE_DELIVERY_CYCLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.referenceDeliveryCycle != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.referenceDeliveryCycle,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="REPORT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reportStatus != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.reportStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="REPORT_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reportComments != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.reportComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="HAVE_INSTALLATION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.haveInstallation != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.haveInstallation,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsComments != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.goodsComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INSIDE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.insideComments != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.insideComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalUserId != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.arrivalUserId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalStatus != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.arrivalStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalTime != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.arrivalTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_IGNORE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isIgnore != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.isIgnore,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IGNORE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ignoreTime != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.ignoreTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="IGNORE_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ignoreUserId != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.ignoreUserId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="LOCKED_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.lockedStatus != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.lockedStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="MAX_SKU_REFUND_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.maxSkuRefundAmount != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.maxSkuRefundAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="OCCUPY_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.occupyNum != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.occupyNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_ACTION_GOODS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isActionGoods != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.isActionGoods,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ACTION_OCCUPY_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.actionOccupyNum != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.actionOccupyNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_COUPONS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isCoupons != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.isCoupons,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="EL_ORDERLIST_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.elOrderlistId != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.elOrderlistId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_DATA_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateDataTime != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.updateDataTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="AFTER_RETURN_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterReturnAmount != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.afterReturnAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="AFTER_RETURN_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterReturnNum != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.afterReturnNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="REAL_PAY_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.realPayAmount != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.realPayAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUY_PRICE_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.buyPriceFlag != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyPriceFlag,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_TAGS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuTags != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.skuTags,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRODUCT_AUDIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productAudit != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.productAudit,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRODUCT_AUDIT_UESRID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productAuditUesrid != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.productAuditUesrid,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DIRECT_SUPERIOR_AUDIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.directSuperiorAudit != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.directSuperiorAudit,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUY_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.buyPrice != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="HISTORY_AVG_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.historyAvgPrice != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.historyAvgPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUYORDER_NOS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.buyorderNos != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyorderNos,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="COMPONENT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.componentId != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.componentId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="COMPONENT_HTML = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.componentHtml != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.componentHtml,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="AGING = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.aging != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.aging,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="WARN_LEVEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.warnLevel != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.warnLevel,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="AGING_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.agingTime != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.agingTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_WARN = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isWarn != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.isWarn,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="JOIN_BUYORDER_ORDER_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.joinBuyorderOrderTime != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.joinBuyorderOrderTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="JOIN_BUYORDER_USER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.joinBuyorderUser != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.joinBuyorderUser,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUY_DOCK_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.buyDockUserId != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyDockUserId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUY_PROCESS_MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.buyProcessModTime != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyProcessModTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUY_PROCESS_MOD_RESON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.buyProcessModReson != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyProcessModReson,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRODUCT_BELONG_ID_INFO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productBelongIdInfo != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.productBelongIdInfo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRODUCT_BELONG_NAME_INFO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productBelongNameInfo != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.productBelongNameInfo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SPECIAL_DELIVERY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.specialDelivery != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.specialDelivery,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRODUCT_CHINESE_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productChineseName != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.productChineseName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SPEC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.spec != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.spec,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MANUFACTURER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.manufacturerName != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.manufacturerName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PRODUCT_COMPANY_LICENCE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.productCompanyLicence != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.productCompanyLicence,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PERFERMENCE_DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.perfermenceDeliveryTime != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.perfermenceDeliveryTime,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="Label_CODES = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.labelCodes != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.labelCodes,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="Label_NAMES = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.labelNames != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.labelNames,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PURCHASE_STATUS_DATA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.purchaseStatusData != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.purchaseStatusData,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUY_NUM_DATA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.buyNumData != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.buyNumData,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONFIRM_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.confirmNumber != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.confirmNumber,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="VIEW_REAL_DELIVERY_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.viewRealDeliveryNum != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.viewRealDeliveryNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="VIEW_REAL_ARRIVAL_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.viewRealArrivalNum != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.viewRealArrivalNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_GIFT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isGift != null">
            when SALEORDER_GOODS_ID = #{item.saleorderGoodsId,jdbcType=INTEGER} then #{item.isGift,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
    </trim>
    where SALEORDER_GOODS_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.saleorderGoodsId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="SALEORDER_GOODS_ID" keyProperty="saleorderGoodsId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SALEORDER_GOODS
    (SALEORDER_ID, GOODS_ID, SKU, GOODS_NAME, BRAND_NAME, BRAND_ID, MODEL, UNIT_NAME, 
      PRICE, REAL_PRICE, PURCHASING_PRICE, CURRENCY_UNIT_ID, NUM, BUY_NUM, DELIVERY_NUM, 
      DELIVERY_STATUS, DELIVERY_TIME, DELIVERY_CYCLE, DELIVERY_DIRECT, DELIVERY_DIRECT_COMMENTS, 
      REGISTRATION_NUMBER, SUPPLIER_NAME, REFERENCE_COST_PRICE, REFERENCE_PRICE, REFERENCE_DELIVERY_CYCLE, 
      REPORT_STATUS, REPORT_COMMENTS, HAVE_INSTALLATION, GOODS_COMMENTS, INSIDE_COMMENTS, 
      ARRIVAL_USER_ID, ARRIVAL_STATUS, ARRIVAL_TIME, IS_DELETE, IS_IGNORE, IGNORE_TIME, 
      IGNORE_USER_ID, LOCKED_STATUS, MAX_SKU_REFUND_AMOUNT, ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, OCCUPY_NUM, IS_ACTION_GOODS, ACTION_OCCUPY_NUM, IS_COUPONS, EL_ORDERLIST_ID, 
      UPDATE_DATA_TIME, AFTER_RETURN_AMOUNT, AFTER_RETURN_NUM, REAL_PAY_AMOUNT, BUY_PRICE_FLAG, 
      SKU_TAGS, PRODUCT_AUDIT, PRODUCT_AUDIT_UESRID, DIRECT_SUPERIOR_AUDIT, BUY_PRICE, 
      HISTORY_AVG_PRICE, BUYORDER_NOS, COMPONENT_ID, COMPONENT_HTML, AGING, WARN_LEVEL, 
      AGING_TIME, IS_WARN, JOIN_BUYORDER_ORDER_TIME, JOIN_BUYORDER_USER, BUY_DOCK_USER_ID, 
      BUY_PROCESS_MOD_TIME, BUY_PROCESS_MOD_RESON, PRODUCT_BELONG_ID_INFO, PRODUCT_BELONG_NAME_INFO, 
      SPECIAL_DELIVERY, PRODUCT_CHINESE_NAME, SPEC, MANUFACTURER_NAME, PRODUCT_COMPANY_LICENCE, 
      PERFERMENCE_DELIVERY_TIME, Label_CODES, Label_NAMES, PURCHASE_STATUS_DATA, BUY_NUM_DATA, 
      CONFIRM_NUMBER, VIEW_REAL_DELIVERY_NUM, VIEW_REAL_ARRIVAL_NUM, IS_GIFT)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.saleorderId,jdbcType=INTEGER}, #{item.goodsId,jdbcType=INTEGER}, #{item.sku,jdbcType=VARCHAR}, 
        #{item.goodsName,jdbcType=VARCHAR}, #{item.brandName,jdbcType=VARCHAR}, #{item.brandId,jdbcType=INTEGER}, 
        #{item.model,jdbcType=VARCHAR}, #{item.unitName,jdbcType=VARCHAR}, #{item.price,jdbcType=DECIMAL}, 
        #{item.realPrice,jdbcType=DECIMAL}, #{item.purchasingPrice,jdbcType=DECIMAL}, #{item.currencyUnitId,jdbcType=INTEGER}, 
        #{item.num,jdbcType=INTEGER}, #{item.buyNum,jdbcType=INTEGER}, #{item.deliveryNum,jdbcType=INTEGER}, 
        #{item.deliveryStatus,jdbcType=BOOLEAN}, #{item.deliveryTime,jdbcType=BIGINT}, 
        #{item.deliveryCycle,jdbcType=VARCHAR}, #{item.deliveryDirect,jdbcType=BOOLEAN}, 
        #{item.deliveryDirectComments,jdbcType=VARCHAR}, #{item.registrationNumber,jdbcType=VARCHAR}, 
        #{item.supplierName,jdbcType=VARCHAR}, #{item.referenceCostPrice,jdbcType=DECIMAL}, 
        #{item.referencePrice,jdbcType=VARCHAR}, #{item.referenceDeliveryCycle,jdbcType=VARCHAR}, 
        #{item.reportStatus,jdbcType=BOOLEAN}, #{item.reportComments,jdbcType=VARCHAR}, 
        #{item.haveInstallation,jdbcType=BOOLEAN}, #{item.goodsComments,jdbcType=VARCHAR}, 
        #{item.insideComments,jdbcType=VARCHAR}, #{item.arrivalUserId,jdbcType=INTEGER}, 
        #{item.arrivalStatus,jdbcType=BOOLEAN}, #{item.arrivalTime,jdbcType=BIGINT}, #{item.isDelete,jdbcType=BOOLEAN}, 
        #{item.isIgnore,jdbcType=BOOLEAN}, #{item.ignoreTime,jdbcType=BIGINT}, #{item.ignoreUserId,jdbcType=INTEGER}, 
        #{item.lockedStatus,jdbcType=BOOLEAN}, #{item.maxSkuRefundAmount,jdbcType=DECIMAL}, 
        #{item.addTime,jdbcType=BIGINT}, #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=BIGINT}, 
        #{item.updater,jdbcType=INTEGER}, #{item.occupyNum,jdbcType=INTEGER}, #{item.isActionGoods,jdbcType=BOOLEAN}, 
        #{item.actionOccupyNum,jdbcType=INTEGER}, #{item.isCoupons,jdbcType=BOOLEAN}, #{item.elOrderlistId,jdbcType=INTEGER}, 
        #{item.updateDataTime,jdbcType=TIMESTAMP}, #{item.afterReturnAmount,jdbcType=DECIMAL}, 
        #{item.afterReturnNum,jdbcType=INTEGER}, #{item.realPayAmount,jdbcType=DECIMAL}, 
        #{item.buyPriceFlag,jdbcType=VARCHAR}, #{item.skuTags,jdbcType=VARCHAR}, #{item.productAudit,jdbcType=INTEGER}, 
        #{item.productAuditUesrid,jdbcType=INTEGER}, #{item.directSuperiorAudit,jdbcType=INTEGER}, 
        #{item.buyPrice,jdbcType=DECIMAL}, #{item.historyAvgPrice,jdbcType=DECIMAL}, #{item.buyorderNos,jdbcType=VARCHAR}, 
        #{item.componentId,jdbcType=INTEGER}, #{item.componentHtml,jdbcType=VARCHAR}, #{item.aging,jdbcType=INTEGER}, 
        #{item.warnLevel,jdbcType=INTEGER}, #{item.agingTime,jdbcType=BIGINT}, #{item.isWarn,jdbcType=INTEGER}, 
        #{item.joinBuyorderOrderTime,jdbcType=BIGINT}, #{item.joinBuyorderUser,jdbcType=INTEGER}, 
        #{item.buyDockUserId,jdbcType=INTEGER}, #{item.buyProcessModTime,jdbcType=BIGINT}, 
        #{item.buyProcessModReson,jdbcType=VARCHAR}, #{item.productBelongIdInfo,jdbcType=VARCHAR}, 
        #{item.productBelongNameInfo,jdbcType=VARCHAR}, #{item.specialDelivery,jdbcType=INTEGER}, 
        #{item.productChineseName,jdbcType=VARCHAR}, #{item.spec,jdbcType=VARCHAR}, #{item.manufacturerName,jdbcType=VARCHAR}, 
        #{item.productCompanyLicence,jdbcType=VARCHAR}, #{item.perfermenceDeliveryTime,jdbcType=INTEGER}, 
        #{item.labelCodes,jdbcType=VARCHAR}, #{item.labelNames,jdbcType=VARCHAR}, #{item.purchaseStatusData,jdbcType=BOOLEAN}, 
        #{item.buyNumData,jdbcType=INTEGER}, #{item.confirmNumber,jdbcType=INTEGER}, #{item.viewRealDeliveryNum,jdbcType=INTEGER}, 
        #{item.viewRealArrivalNum,jdbcType=INTEGER}, #{item.isGift,jdbcType=BOOLEAN})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="SALEORDER_GOODS_ID" keyProperty="saleorderGoodsId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderGoodsDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SALEORDER_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID,
      </if>
      SALEORDER_ID,
      GOODS_ID,
      SKU,
      GOODS_NAME,
      BRAND_NAME,
      BRAND_ID,
      MODEL,
      UNIT_NAME,
      PRICE,
      REAL_PRICE,
      PURCHASING_PRICE,
      CURRENCY_UNIT_ID,
      NUM,
      BUY_NUM,
      DELIVERY_NUM,
      DELIVERY_STATUS,
      DELIVERY_TIME,
      DELIVERY_CYCLE,
      DELIVERY_DIRECT,
      DELIVERY_DIRECT_COMMENTS,
      REGISTRATION_NUMBER,
      SUPPLIER_NAME,
      REFERENCE_COST_PRICE,
      REFERENCE_PRICE,
      REFERENCE_DELIVERY_CYCLE,
      REPORT_STATUS,
      REPORT_COMMENTS,
      HAVE_INSTALLATION,
      GOODS_COMMENTS,
      INSIDE_COMMENTS,
      ARRIVAL_USER_ID,
      ARRIVAL_STATUS,
      ARRIVAL_TIME,
      IS_DELETE,
      IS_IGNORE,
      IGNORE_TIME,
      IGNORE_USER_ID,
      LOCKED_STATUS,
      MAX_SKU_REFUND_AMOUNT,
      ADD_TIME,
      CREATOR,
      MOD_TIME,
      UPDATER,
      OCCUPY_NUM,
      IS_ACTION_GOODS,
      ACTION_OCCUPY_NUM,
      IS_COUPONS,
      EL_ORDERLIST_ID,
      UPDATE_DATA_TIME,
      AFTER_RETURN_AMOUNT,
      AFTER_RETURN_NUM,
      REAL_PAY_AMOUNT,
      BUY_PRICE_FLAG,
      SKU_TAGS,
      PRODUCT_AUDIT,
      PRODUCT_AUDIT_UESRID,
      DIRECT_SUPERIOR_AUDIT,
      BUY_PRICE,
      HISTORY_AVG_PRICE,
      BUYORDER_NOS,
      COMPONENT_ID,
      COMPONENT_HTML,
      AGING,
      WARN_LEVEL,
      AGING_TIME,
      IS_WARN,
      JOIN_BUYORDER_ORDER_TIME,
      JOIN_BUYORDER_USER,
      BUY_DOCK_USER_ID,
      BUY_PROCESS_MOD_TIME,
      BUY_PROCESS_MOD_RESON,
      PRODUCT_BELONG_ID_INFO,
      PRODUCT_BELONG_NAME_INFO,
      SPECIAL_DELIVERY,
      PRODUCT_CHINESE_NAME,
      SPEC,
      MANUFACTURER_NAME,
      PRODUCT_COMPANY_LICENCE,
      PERFERMENCE_DELIVERY_TIME,
      Label_CODES,
      Label_NAMES,
      PURCHASE_STATUS_DATA,
      BUY_NUM_DATA,
      CONFIRM_NUMBER,
      VIEW_REAL_DELIVERY_NUM,
      VIEW_REAL_ARRIVAL_NUM,
      IS_GIFT,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderGoodsId != null">
        #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      #{saleorderId,jdbcType=INTEGER},
      #{goodsId,jdbcType=INTEGER},
      #{sku,jdbcType=VARCHAR},
      #{goodsName,jdbcType=VARCHAR},
      #{brandName,jdbcType=VARCHAR},
      #{brandId,jdbcType=INTEGER},
      #{model,jdbcType=VARCHAR},
      #{unitName,jdbcType=VARCHAR},
      #{price,jdbcType=DECIMAL},
      #{realPrice,jdbcType=DECIMAL},
      #{purchasingPrice,jdbcType=DECIMAL},
      #{currencyUnitId,jdbcType=INTEGER},
      #{num,jdbcType=INTEGER},
      #{buyNum,jdbcType=INTEGER},
      #{deliveryNum,jdbcType=INTEGER},
      #{deliveryStatus,jdbcType=BOOLEAN},
      #{deliveryTime,jdbcType=BIGINT},
      #{deliveryCycle,jdbcType=VARCHAR},
      #{deliveryDirect,jdbcType=BOOLEAN},
      #{deliveryDirectComments,jdbcType=VARCHAR},
      #{registrationNumber,jdbcType=VARCHAR},
      #{supplierName,jdbcType=VARCHAR},
      #{referenceCostPrice,jdbcType=DECIMAL},
      #{referencePrice,jdbcType=VARCHAR},
      #{referenceDeliveryCycle,jdbcType=VARCHAR},
      #{reportStatus,jdbcType=BOOLEAN},
      #{reportComments,jdbcType=VARCHAR},
      #{haveInstallation,jdbcType=BOOLEAN},
      #{goodsComments,jdbcType=VARCHAR},
      #{insideComments,jdbcType=VARCHAR},
      #{arrivalUserId,jdbcType=INTEGER},
      #{arrivalStatus,jdbcType=BOOLEAN},
      #{arrivalTime,jdbcType=BIGINT},
      #{isDelete,jdbcType=BOOLEAN},
      #{isIgnore,jdbcType=BOOLEAN},
      #{ignoreTime,jdbcType=BIGINT},
      #{ignoreUserId,jdbcType=INTEGER},
      #{lockedStatus,jdbcType=BOOLEAN},
      #{maxSkuRefundAmount,jdbcType=DECIMAL},
      #{addTime,jdbcType=BIGINT},
      #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=BIGINT},
      #{updater,jdbcType=INTEGER},
      #{occupyNum,jdbcType=INTEGER},
      #{isActionGoods,jdbcType=BOOLEAN},
      #{actionOccupyNum,jdbcType=INTEGER},
      #{isCoupons,jdbcType=BOOLEAN},
      #{elOrderlistId,jdbcType=INTEGER},
      #{updateDataTime,jdbcType=TIMESTAMP},
      #{afterReturnAmount,jdbcType=DECIMAL},
      #{afterReturnNum,jdbcType=INTEGER},
      #{realPayAmount,jdbcType=DECIMAL},
      #{buyPriceFlag,jdbcType=VARCHAR},
      #{skuTags,jdbcType=VARCHAR},
      #{productAudit,jdbcType=INTEGER},
      #{productAuditUesrid,jdbcType=INTEGER},
      #{directSuperiorAudit,jdbcType=INTEGER},
      #{buyPrice,jdbcType=DECIMAL},
      #{historyAvgPrice,jdbcType=DECIMAL},
      #{buyorderNos,jdbcType=VARCHAR},
      #{componentId,jdbcType=INTEGER},
      #{componentHtml,jdbcType=VARCHAR},
      #{aging,jdbcType=INTEGER},
      #{warnLevel,jdbcType=INTEGER},
      #{agingTime,jdbcType=BIGINT},
      #{isWarn,jdbcType=INTEGER},
      #{joinBuyorderOrderTime,jdbcType=BIGINT},
      #{joinBuyorderUser,jdbcType=INTEGER},
      #{buyDockUserId,jdbcType=INTEGER},
      #{buyProcessModTime,jdbcType=BIGINT},
      #{buyProcessModReson,jdbcType=VARCHAR},
      #{productBelongIdInfo,jdbcType=VARCHAR},
      #{productBelongNameInfo,jdbcType=VARCHAR},
      #{specialDelivery,jdbcType=INTEGER},
      #{productChineseName,jdbcType=VARCHAR},
      #{spec,jdbcType=VARCHAR},
      #{manufacturerName,jdbcType=VARCHAR},
      #{productCompanyLicence,jdbcType=VARCHAR},
      #{perfermenceDeliveryTime,jdbcType=INTEGER},
      #{labelCodes,jdbcType=VARCHAR},
      #{labelNames,jdbcType=VARCHAR},
      #{purchaseStatusData,jdbcType=BOOLEAN},
      #{buyNumData,jdbcType=INTEGER},
      #{confirmNumber,jdbcType=INTEGER},
      #{viewRealDeliveryNum,jdbcType=INTEGER},
      #{viewRealArrivalNum,jdbcType=INTEGER},
      #{isGift,jdbcType=BOOLEAN},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      BRAND_ID = #{brandId,jdbcType=INTEGER},
      MODEL = #{model,jdbcType=VARCHAR},
      UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      PRICE = #{price,jdbcType=DECIMAL},
      REAL_PRICE = #{realPrice,jdbcType=DECIMAL},
      PURCHASING_PRICE = #{purchasingPrice,jdbcType=DECIMAL},
      CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      BUY_NUM = #{buyNum,jdbcType=INTEGER},
      DELIVERY_NUM = #{deliveryNum,jdbcType=INTEGER},
      DELIVERY_STATUS = #{deliveryStatus,jdbcType=BOOLEAN},
      DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
      DELIVERY_DIRECT = #{deliveryDirect,jdbcType=INTEGER},
      DELIVERY_DIRECT_COMMENTS = #{deliveryDirectComments,jdbcType=VARCHAR},
      REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
      REFERENCE_COST_PRICE = #{referenceCostPrice,jdbcType=DECIMAL},
      REFERENCE_PRICE = #{referencePrice,jdbcType=VARCHAR},
      REFERENCE_DELIVERY_CYCLE = #{referenceDeliveryCycle,jdbcType=VARCHAR},
      REPORT_STATUS = #{reportStatus,jdbcType=BOOLEAN},
      REPORT_COMMENTS = #{reportComments,jdbcType=VARCHAR},
      HAVE_INSTALLATION = #{haveInstallation,jdbcType=BOOLEAN},
      GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      IS_IGNORE = #{isIgnore,jdbcType=BOOLEAN},
      IGNORE_TIME = #{ignoreTime,jdbcType=BIGINT},
      IGNORE_USER_ID = #{ignoreUserId,jdbcType=INTEGER},
      LOCKED_STATUS = #{lockedStatus,jdbcType=BOOLEAN},
      MAX_SKU_REFUND_AMOUNT = #{maxSkuRefundAmount,jdbcType=DECIMAL},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      OCCUPY_NUM = #{occupyNum,jdbcType=INTEGER},
      IS_ACTION_GOODS = #{isActionGoods,jdbcType=BOOLEAN},
      ACTION_OCCUPY_NUM = #{actionOccupyNum,jdbcType=INTEGER},
      IS_COUPONS = #{isCoupons,jdbcType=BOOLEAN},
      EL_ORDERLIST_ID = #{elOrderlistId,jdbcType=INTEGER},
      UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      AFTER_RETURN_AMOUNT = #{afterReturnAmount,jdbcType=DECIMAL},
      AFTER_RETURN_NUM = #{afterReturnNum,jdbcType=INTEGER},
      REAL_PAY_AMOUNT = #{realPayAmount,jdbcType=DECIMAL},
      BUY_PRICE_FLAG = #{buyPriceFlag,jdbcType=VARCHAR},
      SKU_TAGS = #{skuTags,jdbcType=VARCHAR},
      PRODUCT_AUDIT = #{productAudit,jdbcType=INTEGER},
      PRODUCT_AUDIT_UESRID = #{productAuditUesrid,jdbcType=INTEGER},
      DIRECT_SUPERIOR_AUDIT = #{directSuperiorAudit,jdbcType=INTEGER},
      BUY_PRICE = #{buyPrice,jdbcType=DECIMAL},
      HISTORY_AVG_PRICE = #{historyAvgPrice,jdbcType=DECIMAL},
      BUYORDER_NOS = #{buyorderNos,jdbcType=VARCHAR},
      COMPONENT_ID = #{componentId,jdbcType=INTEGER},
      COMPONENT_HTML = #{componentHtml,jdbcType=VARCHAR},
      AGING = #{aging,jdbcType=INTEGER},
      WARN_LEVEL = #{warnLevel,jdbcType=INTEGER},
      AGING_TIME = #{agingTime,jdbcType=BIGINT},
      IS_WARN = #{isWarn,jdbcType=INTEGER},
      JOIN_BUYORDER_ORDER_TIME = #{joinBuyorderOrderTime,jdbcType=BIGINT},
      JOIN_BUYORDER_USER = #{joinBuyorderUser,jdbcType=INTEGER},
      BUY_DOCK_USER_ID = #{buyDockUserId,jdbcType=INTEGER},
      BUY_PROCESS_MOD_TIME = #{buyProcessModTime,jdbcType=BIGINT},
      BUY_PROCESS_MOD_RESON = #{buyProcessModReson,jdbcType=VARCHAR},
      PRODUCT_BELONG_ID_INFO = #{productBelongIdInfo,jdbcType=VARCHAR},
      PRODUCT_BELONG_NAME_INFO = #{productBelongNameInfo,jdbcType=VARCHAR},
      SPECIAL_DELIVERY = #{specialDelivery,jdbcType=INTEGER},
      PRODUCT_CHINESE_NAME = #{productChineseName,jdbcType=VARCHAR},
      SPEC = #{spec,jdbcType=VARCHAR},
      MANUFACTURER_NAME = #{manufacturerName,jdbcType=VARCHAR},
      PRODUCT_COMPANY_LICENCE = #{productCompanyLicence,jdbcType=VARCHAR},
      PERFERMENCE_DELIVERY_TIME = #{perfermenceDeliveryTime,jdbcType=INTEGER},
      Label_CODES = #{labelCodes,jdbcType=VARCHAR},
      Label_NAMES = #{labelNames,jdbcType=VARCHAR},
      PURCHASE_STATUS_DATA = #{purchaseStatusData,jdbcType=BOOLEAN},
      BUY_NUM_DATA = #{buyNumData,jdbcType=INTEGER},
      CONFIRM_NUMBER = #{confirmNumber,jdbcType=INTEGER},
      VIEW_REAL_DELIVERY_NUM = #{viewRealDeliveryNum,jdbcType=INTEGER},
      VIEW_REAL_ARRIVAL_NUM = #{viewRealArrivalNum,jdbcType=INTEGER},
      IS_GIFT = #{isGift,jdbcType=BOOLEAN},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="SALEORDER_GOODS_ID" keyProperty="saleorderGoodsId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderGoodsDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SALEORDER_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID,
      </if>
      <if test="saleorderId != null">
        SALEORDER_ID,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="goodsName != null">
        GOODS_NAME,
      </if>
      <if test="brandName != null">
        BRAND_NAME,
      </if>
      <if test="brandId != null">
        BRAND_ID,
      </if>
      <if test="model != null">
        MODEL,
      </if>
      <if test="unitName != null">
        UNIT_NAME,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="realPrice != null">
        REAL_PRICE,
      </if>
      <if test="purchasingPrice != null">
        PURCHASING_PRICE,
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="buyNum != null">
        BUY_NUM,
      </if>
      <if test="deliveryNum != null">
        DELIVERY_NUM,
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS,
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME,
      </if>
      <if test="deliveryCycle != null">
        DELIVERY_CYCLE,
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT,
      </if>
      <if test="deliveryDirectComments != null">
        DELIVERY_DIRECT_COMMENTS,
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER,
      </if>
      <if test="supplierName != null">
        SUPPLIER_NAME,
      </if>
      <if test="referenceCostPrice != null">
        REFERENCE_COST_PRICE,
      </if>
      <if test="referencePrice != null">
        REFERENCE_PRICE,
      </if>
      <if test="referenceDeliveryCycle != null">
        REFERENCE_DELIVERY_CYCLE,
      </if>
      <if test="reportStatus != null">
        REPORT_STATUS,
      </if>
      <if test="reportComments != null">
        REPORT_COMMENTS,
      </if>
      <if test="haveInstallation != null">
        HAVE_INSTALLATION,
      </if>
      <if test="goodsComments != null">
        GOODS_COMMENTS,
      </if>
      <if test="insideComments != null">
        INSIDE_COMMENTS,
      </if>
      <if test="arrivalUserId != null">
        ARRIVAL_USER_ID,
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="isIgnore != null">
        IS_IGNORE,
      </if>
      <if test="ignoreTime != null">
        IGNORE_TIME,
      </if>
      <if test="ignoreUserId != null">
        IGNORE_USER_ID,
      </if>
      <if test="lockedStatus != null">
        LOCKED_STATUS,
      </if>
      <if test="maxSkuRefundAmount != null">
        MAX_SKU_REFUND_AMOUNT,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="occupyNum != null">
        OCCUPY_NUM,
      </if>
      <if test="isActionGoods != null">
        IS_ACTION_GOODS,
      </if>
      <if test="actionOccupyNum != null">
        ACTION_OCCUPY_NUM,
      </if>
      <if test="isCoupons != null">
        IS_COUPONS,
      </if>
      <if test="elOrderlistId != null">
        EL_ORDERLIST_ID,
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME,
      </if>
      <if test="afterReturnAmount != null">
        AFTER_RETURN_AMOUNT,
      </if>
      <if test="afterReturnNum != null">
        AFTER_RETURN_NUM,
      </if>
      <if test="realPayAmount != null">
        REAL_PAY_AMOUNT,
      </if>
      <if test="buyPriceFlag != null">
        BUY_PRICE_FLAG,
      </if>
      <if test="skuTags != null">
        SKU_TAGS,
      </if>
      <if test="productAudit != null">
        PRODUCT_AUDIT,
      </if>
      <if test="productAuditUesrid != null">
        PRODUCT_AUDIT_UESRID,
      </if>
      <if test="directSuperiorAudit != null">
        DIRECT_SUPERIOR_AUDIT,
      </if>
      <if test="buyPrice != null">
        BUY_PRICE,
      </if>
      <if test="historyAvgPrice != null">
        HISTORY_AVG_PRICE,
      </if>
      <if test="buyorderNos != null">
        BUYORDER_NOS,
      </if>
      <if test="componentId != null">
        COMPONENT_ID,
      </if>
      <if test="componentHtml != null">
        COMPONENT_HTML,
      </if>
      <if test="aging != null">
        AGING,
      </if>
      <if test="warnLevel != null">
        WARN_LEVEL,
      </if>
      <if test="agingTime != null">
        AGING_TIME,
      </if>
      <if test="isWarn != null">
        IS_WARN,
      </if>
      <if test="joinBuyorderOrderTime != null">
        JOIN_BUYORDER_ORDER_TIME,
      </if>
      <if test="joinBuyorderUser != null">
        JOIN_BUYORDER_USER,
      </if>
      <if test="buyDockUserId != null">
        BUY_DOCK_USER_ID,
      </if>
      <if test="buyProcessModTime != null">
        BUY_PROCESS_MOD_TIME,
      </if>
      <if test="buyProcessModReson != null">
        BUY_PROCESS_MOD_RESON,
      </if>
      <if test="productBelongIdInfo != null">
        PRODUCT_BELONG_ID_INFO,
      </if>
      <if test="productBelongNameInfo != null">
        PRODUCT_BELONG_NAME_INFO,
      </if>
      <if test="specialDelivery != null">
        SPECIAL_DELIVERY,
      </if>
      <if test="productChineseName != null">
        PRODUCT_CHINESE_NAME,
      </if>
      <if test="spec != null">
        SPEC,
      </if>
      <if test="manufacturerName != null">
        MANUFACTURER_NAME,
      </if>
      <if test="productCompanyLicence != null">
        PRODUCT_COMPANY_LICENCE,
      </if>
      <if test="perfermenceDeliveryTime != null">
        PERFERMENCE_DELIVERY_TIME,
      </if>
      <if test="labelCodes != null">
        Label_CODES,
      </if>
      <if test="labelNames != null">
        Label_NAMES,
      </if>
      <if test="purchaseStatusData != null">
        PURCHASE_STATUS_DATA,
      </if>
      <if test="buyNumData != null">
        BUY_NUM_DATA,
      </if>
      <if test="confirmNumber != null">
        CONFIRM_NUMBER,
      </if>
      <if test="viewRealDeliveryNum != null">
        VIEW_REAL_DELIVERY_NUM,
      </if>
      <if test="viewRealArrivalNum != null">
        VIEW_REAL_ARRIVAL_NUM,
      </if>
      <if test="isGift != null">
        IS_GIFT,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderGoodsId != null">
        #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="saleorderId != null">
        #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=INTEGER},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="realPrice != null">
        #{realPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchasingPrice != null">
        #{purchasingPrice,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null">
        #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="buyNum != null">
        #{buyNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryNum != null">
        #{deliveryNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryCycle != null">
        #{deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null">
        #{deliveryDirect,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryDirectComments != null">
        #{deliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="referenceCostPrice != null">
        #{referenceCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="referencePrice != null">
        #{referencePrice,jdbcType=VARCHAR},
      </if>
      <if test="referenceDeliveryCycle != null">
        #{referenceDeliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        #{reportStatus,jdbcType=BOOLEAN},
      </if>
      <if test="reportComments != null">
        #{reportComments,jdbcType=VARCHAR},
      </if>
      <if test="haveInstallation != null">
        #{haveInstallation,jdbcType=BOOLEAN},
      </if>
      <if test="goodsComments != null">
        #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="insideComments != null">
        #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="arrivalUserId != null">
        #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null">
        #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="isIgnore != null">
        #{isIgnore,jdbcType=BOOLEAN},
      </if>
      <if test="ignoreTime != null">
        #{ignoreTime,jdbcType=BIGINT},
      </if>
      <if test="ignoreUserId != null">
        #{ignoreUserId,jdbcType=INTEGER},
      </if>
      <if test="lockedStatus != null">
        #{lockedStatus,jdbcType=BOOLEAN},
      </if>
      <if test="maxSkuRefundAmount != null">
        #{maxSkuRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="occupyNum != null">
        #{occupyNum,jdbcType=INTEGER},
      </if>
      <if test="isActionGoods != null">
        #{isActionGoods,jdbcType=BOOLEAN},
      </if>
      <if test="actionOccupyNum != null">
        #{actionOccupyNum,jdbcType=INTEGER},
      </if>
      <if test="isCoupons != null">
        #{isCoupons,jdbcType=BOOLEAN},
      </if>
      <if test="elOrderlistId != null">
        #{elOrderlistId,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null">
        #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="afterReturnAmount != null">
        #{afterReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterReturnNum != null">
        #{afterReturnNum,jdbcType=INTEGER},
      </if>
      <if test="realPayAmount != null">
        #{realPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="buyPriceFlag != null">
        #{buyPriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="skuTags != null">
        #{skuTags,jdbcType=VARCHAR},
      </if>
      <if test="productAudit != null">
        #{productAudit,jdbcType=INTEGER},
      </if>
      <if test="productAuditUesrid != null">
        #{productAuditUesrid,jdbcType=INTEGER},
      </if>
      <if test="directSuperiorAudit != null">
        #{directSuperiorAudit,jdbcType=INTEGER},
      </if>
      <if test="buyPrice != null">
        #{buyPrice,jdbcType=DECIMAL},
      </if>
      <if test="historyAvgPrice != null">
        #{historyAvgPrice,jdbcType=DECIMAL},
      </if>
      <if test="buyorderNos != null">
        #{buyorderNos,jdbcType=VARCHAR},
      </if>
      <if test="componentId != null">
        #{componentId,jdbcType=INTEGER},
      </if>
      <if test="componentHtml != null">
        #{componentHtml,jdbcType=VARCHAR},
      </if>
      <if test="aging != null">
        #{aging,jdbcType=INTEGER},
      </if>
      <if test="warnLevel != null">
        #{warnLevel,jdbcType=INTEGER},
      </if>
      <if test="agingTime != null">
        #{agingTime,jdbcType=BIGINT},
      </if>
      <if test="isWarn != null">
        #{isWarn,jdbcType=INTEGER},
      </if>
      <if test="joinBuyorderOrderTime != null">
        #{joinBuyorderOrderTime,jdbcType=BIGINT},
      </if>
      <if test="joinBuyorderUser != null">
        #{joinBuyorderUser,jdbcType=INTEGER},
      </if>
      <if test="buyDockUserId != null">
        #{buyDockUserId,jdbcType=INTEGER},
      </if>
      <if test="buyProcessModTime != null">
        #{buyProcessModTime,jdbcType=BIGINT},
      </if>
      <if test="buyProcessModReson != null">
        #{buyProcessModReson,jdbcType=VARCHAR},
      </if>
      <if test="productBelongIdInfo != null">
        #{productBelongIdInfo,jdbcType=VARCHAR},
      </if>
      <if test="productBelongNameInfo != null">
        #{productBelongNameInfo,jdbcType=VARCHAR},
      </if>
      <if test="specialDelivery != null">
        #{specialDelivery,jdbcType=INTEGER},
      </if>
      <if test="productChineseName != null">
        #{productChineseName,jdbcType=VARCHAR},
      </if>
      <if test="spec != null">
        #{spec,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerName != null">
        #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="productCompanyLicence != null">
        #{productCompanyLicence,jdbcType=VARCHAR},
      </if>
      <if test="perfermenceDeliveryTime != null">
        #{perfermenceDeliveryTime,jdbcType=INTEGER},
      </if>
      <if test="labelCodes != null">
        #{labelCodes,jdbcType=VARCHAR},
      </if>
      <if test="labelNames != null">
        #{labelNames,jdbcType=VARCHAR},
      </if>
      <if test="purchaseStatusData != null">
        #{purchaseStatusData,jdbcType=BOOLEAN},
      </if>
      <if test="buyNumData != null">
        #{buyNumData,jdbcType=INTEGER},
      </if>
      <if test="confirmNumber != null">
        #{confirmNumber,jdbcType=INTEGER},
      </if>
      <if test="viewRealDeliveryNum != null">
        #{viewRealDeliveryNum,jdbcType=INTEGER},
      </if>
      <if test="viewRealArrivalNum != null">
        #{viewRealArrivalNum,jdbcType=INTEGER},
      </if>
      <if test="isGift != null">
        #{isGift,jdbcType=BOOLEAN},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="saleorderId != null">
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        BRAND_ID = #{brandId,jdbcType=INTEGER},
      </if>
      <if test="model != null">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="realPrice != null">
        REAL_PRICE = #{realPrice,jdbcType=DECIMAL},
      </if>
      <if test="purchasingPrice != null">
        PURCHASING_PRICE = #{purchasingPrice,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="buyNum != null">
        BUY_NUM = #{buyNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryNum != null">
        DELIVERY_NUM = #{deliveryNum,jdbcType=INTEGER},
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS = #{deliveryStatus,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryCycle != null">
        DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT = #{deliveryDirect,jdbcType=INTEGER},
      </if>
      <if test="deliveryDirectComments != null">
        DELIVERY_DIRECT_COMMENTS = #{deliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="referenceCostPrice != null">
        REFERENCE_COST_PRICE = #{referenceCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="referencePrice != null">
        REFERENCE_PRICE = #{referencePrice,jdbcType=VARCHAR},
      </if>
      <if test="referenceDeliveryCycle != null">
        REFERENCE_DELIVERY_CYCLE = #{referenceDeliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        REPORT_STATUS = #{reportStatus,jdbcType=BOOLEAN},
      </if>
      <if test="reportComments != null">
        REPORT_COMMENTS = #{reportComments,jdbcType=VARCHAR},
      </if>
      <if test="haveInstallation != null">
        HAVE_INSTALLATION = #{haveInstallation,jdbcType=BOOLEAN},
      </if>
      <if test="goodsComments != null">
        GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="insideComments != null">
        INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="arrivalUserId != null">
        ARRIVAL_USER_ID = #{arrivalUserId,jdbcType=INTEGER},
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="isIgnore != null">
        IS_IGNORE = #{isIgnore,jdbcType=BOOLEAN},
      </if>
      <if test="ignoreTime != null">
        IGNORE_TIME = #{ignoreTime,jdbcType=BIGINT},
      </if>
      <if test="ignoreUserId != null">
        IGNORE_USER_ID = #{ignoreUserId,jdbcType=INTEGER},
      </if>
      <if test="lockedStatus != null">
        LOCKED_STATUS = #{lockedStatus,jdbcType=BOOLEAN},
      </if>
      <if test="maxSkuRefundAmount != null">
        MAX_SKU_REFUND_AMOUNT = #{maxSkuRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="occupyNum != null">
        OCCUPY_NUM = #{occupyNum,jdbcType=INTEGER},
      </if>
      <if test="isActionGoods != null">
        IS_ACTION_GOODS = #{isActionGoods,jdbcType=BOOLEAN},
      </if>
      <if test="actionOccupyNum != null">
        ACTION_OCCUPY_NUM = #{actionOccupyNum,jdbcType=INTEGER},
      </if>
      <if test="isCoupons != null">
        IS_COUPONS = #{isCoupons,jdbcType=BOOLEAN},
      </if>
      <if test="elOrderlistId != null">
        EL_ORDERLIST_ID = #{elOrderlistId,jdbcType=INTEGER},
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="afterReturnAmount != null">
        AFTER_RETURN_AMOUNT = #{afterReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterReturnNum != null">
        AFTER_RETURN_NUM = #{afterReturnNum,jdbcType=INTEGER},
      </if>
      <if test="realPayAmount != null">
        REAL_PAY_AMOUNT = #{realPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="buyPriceFlag != null">
        BUY_PRICE_FLAG = #{buyPriceFlag,jdbcType=VARCHAR},
      </if>
      <if test="skuTags != null">
        SKU_TAGS = #{skuTags,jdbcType=VARCHAR},
      </if>
      <if test="productAudit != null">
        PRODUCT_AUDIT = #{productAudit,jdbcType=INTEGER},
      </if>
      <if test="productAuditUesrid != null">
        PRODUCT_AUDIT_UESRID = #{productAuditUesrid,jdbcType=INTEGER},
      </if>
      <if test="directSuperiorAudit != null">
        DIRECT_SUPERIOR_AUDIT = #{directSuperiorAudit,jdbcType=INTEGER},
      </if>
      <if test="buyPrice != null">
        BUY_PRICE = #{buyPrice,jdbcType=DECIMAL},
      </if>
      <if test="historyAvgPrice != null">
        HISTORY_AVG_PRICE = #{historyAvgPrice,jdbcType=DECIMAL},
      </if>
      <if test="buyorderNos != null">
        BUYORDER_NOS = #{buyorderNos,jdbcType=VARCHAR},
      </if>
      <if test="componentId != null">
        COMPONENT_ID = #{componentId,jdbcType=INTEGER},
      </if>
      <if test="componentHtml != null">
        COMPONENT_HTML = #{componentHtml,jdbcType=VARCHAR},
      </if>
      <if test="aging != null">
        AGING = #{aging,jdbcType=INTEGER},
      </if>
      <if test="warnLevel != null">
        WARN_LEVEL = #{warnLevel,jdbcType=INTEGER},
      </if>
      <if test="agingTime != null">
        AGING_TIME = #{agingTime,jdbcType=BIGINT},
      </if>
      <if test="isWarn != null">
        IS_WARN = #{isWarn,jdbcType=INTEGER},
      </if>
      <if test="joinBuyorderOrderTime != null">
        JOIN_BUYORDER_ORDER_TIME = #{joinBuyorderOrderTime,jdbcType=BIGINT},
      </if>
      <if test="joinBuyorderUser != null">
        JOIN_BUYORDER_USER = #{joinBuyorderUser,jdbcType=INTEGER},
      </if>
      <if test="buyDockUserId != null">
        BUY_DOCK_USER_ID = #{buyDockUserId,jdbcType=INTEGER},
      </if>
      <if test="buyProcessModTime != null">
        BUY_PROCESS_MOD_TIME = #{buyProcessModTime,jdbcType=BIGINT},
      </if>
      <if test="buyProcessModReson != null">
        BUY_PROCESS_MOD_RESON = #{buyProcessModReson,jdbcType=VARCHAR},
      </if>
      <if test="productBelongIdInfo != null">
        PRODUCT_BELONG_ID_INFO = #{productBelongIdInfo,jdbcType=VARCHAR},
      </if>
      <if test="productBelongNameInfo != null">
        PRODUCT_BELONG_NAME_INFO = #{productBelongNameInfo,jdbcType=VARCHAR},
      </if>
      <if test="specialDelivery != null">
        SPECIAL_DELIVERY = #{specialDelivery,jdbcType=INTEGER},
      </if>
      <if test="productChineseName != null">
        PRODUCT_CHINESE_NAME = #{productChineseName,jdbcType=VARCHAR},
      </if>
      <if test="spec != null">
        SPEC = #{spec,jdbcType=VARCHAR},
      </if>
      <if test="manufacturerName != null">
        MANUFACTURER_NAME = #{manufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="productCompanyLicence != null">
        PRODUCT_COMPANY_LICENCE = #{productCompanyLicence,jdbcType=VARCHAR},
      </if>
      <if test="perfermenceDeliveryTime != null">
        PERFERMENCE_DELIVERY_TIME = #{perfermenceDeliveryTime,jdbcType=INTEGER},
      </if>
      <if test="labelCodes != null">
        Label_CODES = #{labelCodes,jdbcType=VARCHAR},
      </if>
      <if test="labelNames != null">
        Label_NAMES = #{labelNames,jdbcType=VARCHAR},
      </if>
      <if test="purchaseStatusData != null">
        PURCHASE_STATUS_DATA = #{purchaseStatusData,jdbcType=BOOLEAN},
      </if>
      <if test="buyNumData != null">
        BUY_NUM_DATA = #{buyNumData,jdbcType=INTEGER},
      </if>
      <if test="confirmNumber != null">
        CONFIRM_NUMBER = #{confirmNumber,jdbcType=INTEGER},
      </if>
      <if test="viewRealDeliveryNum != null">
        VIEW_REAL_DELIVERY_NUM = #{viewRealDeliveryNum,jdbcType=INTEGER},
      </if>
      <if test="viewRealArrivalNum != null">
        VIEW_REAL_ARRIVAL_NUM = #{viewRealArrivalNum,jdbcType=INTEGER},
      </if>
      <if test="isGift != null">
        IS_GIFT = #{isGift,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-01-17-->
  <select id="findBySaleorderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_SALEORDER_GOODS
        where SALEORDER_ID=#{saleorderId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-01-31-->
  <select id="findBySaleorderGoodsIdIn" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_SALEORDER_GOODS
    where SALEORDER_GOODS_ID in
    <foreach close=")" collection="saleorderGoodsIdCollection" index="index" item="item" open="(" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>
  <select id="selectBysaleorderGoodsId" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select *
    from T_SALEORDER_GOODS
    where SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
  </select>

  <select id="selectIsVirtualGoods" resultType="java.lang.Integer">
    SELECT
      IS_VIRTURE_SKU
    FROM
      V_CORE_SKU
    WHERE
      SKU_ID = #{goodsId,jdbcType=INTEGER}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-06-21-->
  <select id="findBySkuInAndSaleorderId" resultMap="BaseResultMap">
    select
    TSG.SALEORDER_GOODS_ID,
    TSG.SKU
    from T_SALEORDER_GOODS TSG
    inner join T_SALEORDER TS on TSG.SALEORDER_ID = TS.SALEORDER_ID
    where TSG.SKU in
    <foreach item="item" index="index" collection="skuCollection"
             open="(" separator="," close=")">
        #{item,jdbcType=VARCHAR}
    </foreach>
    and TS.SALEORDER_NO=#{saleorderNo,jdbcType=VARCHAR}
    </select>

  <select id="getBySaleorderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_SALEORDER_GOODS
    where
    IS_DELETE = 0
    and SALEORDER_ID=#{saleorderId,jdbcType=INTEGER}
  </select>
</mapper>