<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>供应商-医疗器械经营许可证（三类）钻取页面</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="supplier_xuke" datasource="erp-datasourcetarget" empty_show="" fixednumber="0" firstcol="numbers">
<div id="appendHead">
</div>

<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">供应商名称：</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="TRADER_SUPPLIER_NAME" placeholder="" style="" alias="bbc" jdbctype="VARCHAR" data="" datatype="" oper="LIKE">

            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">医疗器械经营许可证（三类）是否过期</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="" name="IS_ENABLE" placeholder="" style="" alias="bbc" jdbctype="VARCHAR" data="[
                {&quot;V&quot;:&quot;全部&quot;,&quot;K&quot;:&quot;全部&quot;},
                {&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;是&quot;},
                {&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;否&quot;}
                ]" datatype="JSON" oper=""></select>
            </div>
        </div>
    </form>
    <div class="btn-group   bd-highlight" id="tableButton">
        <!--<button item_name="新增" name="新增" url="/ezadmin/form/form-modifySaleQa0"> 新增 </button>-->
        <!--<button type="table" class="layui-btn" url="/ezadmin/form/form-modifySaleQa0" opentype="SELF" area="800px,420px"  windowname="新增">+
            新增
        </button>-->


    </div>
    <hr class="layui-border-blue">

    <table id="table" class="layui-table" style=" ">
        <thead>

        <tr id="column">

            <th name="TRADER_SUPPLIER_NAME"  body="td-link" style="width:60px;max-width:180px;min-width: 30px;" url="/trader/supplier/baseinfo.do?traderId=${TRADER_ID}" >供应商名称</th>
            <th name="ADD_TIMEFOMAT" style="width:140px;max-width:120px;min-width: 80px;"  >创建时间</th>
            <th name="AREA_NAME"  style="width:100px;max-width:120px"  >地区</th>
            <th name="USERNAME" style="width:40px;max-width:80px;min-width: 60px;"  >归属采购</th>
            <th name="ORG_NAME" style="width:140px;max-width:140px;min-width: 140px;"  >所属部门</th>
            <th name="IS_ENABLE" body="td-link"  style="width:100px;max-width:60px"  >是否过期</th>
            <th name="ENDTIMEFORMAT" style="width:80px;max-width:140px;min-width: 80px;"  >有效期至</th>

            <!--
                        <th name="ANSWER" body="td-link" url="/system/call/getrecordpaly.do?url=${COID_URI}" style="width:100px;max-width:100px"  >录音地址</th>
            -->


        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="ORDER BY 	IS_ENABLE DESC,	ENDTIMEFORMAT DESC" groupby=""> SELECT
	*
FROM
	(
	SELECT
	  t.TRADER_ID,
		t.TRADER_NAME AS TRADER_SUPPLIER_NAME,
		ts.ADD_TIME,
	CASE

			WHEN ts.ADD_TIME IS NULL THEN
			''
			WHEN ts.ADD_TIME = 0 THEN
			'' ELSE FROM_UNIXTIME( ts.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%s' )
		END ADD_TIMEFOMAT,
	t.AREA_ID,
	ar.REGION_NAME AS REGION_NAME1,
	ae.REGION_NAME AS REGION_NAME2,
	ag.REGION_NAME AS REGION_NAME3,
	concat_ws( ' ', ag.REGION_NAME, ae.REGION_NAME, ar.REGION_NAME ) AS AREA_NAME,
	tu.USERNAME,
	ttc.BEGINTIME,
	ttc.ENDTIME,-- 	group_concat( d.ORG_NAME ) AS ORG_NAME
	ton.ORG_NAME,
	FROM_UNIXTIME( ttc.ENDTIME / 1000, '%Y-%m-%d %H:%i:%s' ) AS ENDTIMEFORMAT,
CASE

		WHEN ttc.ENDTIME = 0 THEN
		'否'
		WHEN unix_timestamp( ) > ( ttc.ENDTIME / 1000 ) THEN
		'是'
		WHEN unix_timestamp( ) <= ( ttc.ENDTIME / 1000 ) THEN
		'否'
	END IS_ENABLE
FROM
	T_TRADER_SUPPLIER ts
	LEFT JOIN T_TRADER t ON t.TRADER_ID = ts.TRADER_ID
	LEFT JOIN T_TRADER_CERTIFICATE ttc ON t.TRADER_ID = ttc.TRADER_ID
	AND ttc.SYS_OPTION_DEFINITION_ID = 29
	AND ttc.IS_DELETE = 0
	LEFT JOIN T_R_TRADER_J_USER tr ON t.TRADER_ID = tr.TRADER_ID
	LEFT JOIN T_USER tu ON tr.USER_ID = tu.USER_ID
	LEFT JOIN (
	SELECT
		d.ORG_NAME,
		r_u_p.USER_ID
	FROM
		T_ORGANIZATION d
		LEFT JOIN T_POSITION tc ON d.ORG_ID = tc.ORG_ID
		LEFT JOIN T_R_USER_POSIT r_u_p ON tc.POSITION_ID = r_u_p.POSITION_ID
	) ton ON tu.USER_ID = ton.USER_ID
	LEFT JOIN T_REGION ar ON t.AREA_ID = ar.REGION_ID
	LEFT JOIN T_REGION ae ON ar.PARENT_ID = ae.REGION_ID
	LEFT JOIN T_REGION ag ON ae.PARENT_ID = ag.REGION_ID
	LEFT JOIN T_VERIFIES_INFO a ON a.RELATE_TABLE_KEY = ts.TRADER_SUPPLIER_ID
	AND a.RELATE_TABLE = 'T_TRADER_SUPPLIER'
	AND a.VERIFIES_TYPE = 619
WHERE
	1 = 1
	AND a.`STATUS` = 1
	AND t.COMPANY_ID = 1
	AND ts.IS_ENABLE = 1
	AND ttc.ENDTIME <> 0
	AND ( unix_timestamp( ) + 31 * 24 * 60 * 60 ) > ttc.ENDTIME / 1000
GROUP BY
	TRADER_SUPPLIER_NAME
ORDER BY
	ts.IS_TOP DESC,
	ts.IS_ENABLE DESC,
	ts.MOD_TIME DESC
	) bbc where 1=1

 </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
</body>
<div id="appendFoot">

    <script>

        $(function(){

        })
    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
</html>