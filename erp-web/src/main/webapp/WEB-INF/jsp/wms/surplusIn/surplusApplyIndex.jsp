<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="盘盈入库审核" scope="application" />
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp"%>
<script>
    function closeSurplus() {
        checkLogin();
        index = layer.confirm("您是否确认关闭该盘盈入库单？", {
            btn: ['确定','取消'] //按钮
        }, function(){

            $.ajax({
                url:page_url+'/wms/surplusIn/closeSurplusInOrder.do',
                data:{"wmsInputOrderId":"${wmsInputOrder.wmsInputOrderId}"},
                type:"POST",
                dataType : "json",
                async: false,
                success:function(data)
                {
                    if(data.code==0){
                        window.location.reload();
                    }else{
                        layer.alert(data.message);
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });

            layer.close(index);

        }, function(){
        });
    }
</script>
<div class="content">
    <div class="formtitle"></div>
    <div  id="desc_div">
            <ul class="payplan">

                <div class="parts">

                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">
                            基本信息
                        </div>
                    </div>

                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <tbody>
                        <tr>
                            <td class="table-smaller">入库单号</td>
                            <td>${wmsInputOrder.orderNo}</td>
                            <td class="table-smaller">入库状态</td>
                            <td>
                                <c:choose>
                                    <c:when test="${wmsInputOrder.arrivalStatus eq 0}">
                                        未收货
                                    </c:when>
                                    <c:when test="${wmsInputOrder.arrivalStatus eq 1}">
                                        部分收货
                                    </c:when>
                                    <c:when test="${wmsInputOrder.arrivalStatus eq 2}">
                                        全部收货
                                    </c:when>
                                </c:choose>
                            </td>
                        </tr>
                        <tr>
                            <td>申请入库日期</td>
                            <td><fmt:formatDate value ="${wmsInputOrder.applyIntime}"/></td>
                            <td>申请人</td>
                            <td>${wmsInputOrder.applyer}</td>
                        </tr>
                        <tr>
                            <td>申请部门</td>
                            <td>${wmsInputOrder.applyerDepartment}</td>
                            <td>审核状态</td>
                            <td><c:choose>
                                <c:when test="${wmsInputOrder.verifyStatus eq 0}">
                                    未审核
                                </c:when>
                                <c:when test="${wmsInputOrder.verifyStatus eq 1}">
                                    审核中
                                </c:when>
                                <c:when test="${wmsInputOrder.verifyStatus eq 2}">
                                    审核通过
                                </c:when>
                                <c:when test="${wmsInputOrder.verifyStatus eq 3}">
                                    审核不通过
                                </c:when>
                                <c:when test="${wmsInputOrder.verifyStatus eq 4}">
                                    已关闭
                                </c:when>
                            </c:choose>
                            </td>
                        </tr>
                        <tr>
                            <td>备注</td>
                            <td>${wmsInputOrder.remark}</td>
                            <td></td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">产品信息</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered" >
                            <thead>
                            <tr>
                                <th style="width:80px">序号</th>
                                <th style="width:80px">订货号</th>
                                <th style="width:80px">产品名称</th>
                                <th style="width:80px">品牌</th>
                                <th style="width:80px">型号</th>
                                <th style="width:80px">入库数量</th>
                            </tr>
                            </thead>
                            <tbody id="thisTimeUpdateTbody">
                            <c:forEach var="ordergoods" items="${wmsInputOrderGoodsList}" varStatus="staut">
                                <tr>
                                    <td>${staut.count}</td>
                                    <td>${ordergoods.skuNo}</td>
                                    <td>${ordergoods.showName}</td>
                                    <td>${ordergoods.brandName}</td>
                                    <td>${ordergoods.model}</td>
                                    <td>${ordergoods.inputNum}</td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">入库记录</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered" >
                            <thead>
                            <tr>
                                <th style="width:80px">入库单据</th>
                                <th style="width:80px">序号</th>
                                <th style="width:80px">订货号</th>
                                <th style="width:80px">产品名称</th>
                                <th style="width:80px">品牌</th>
                                <th style="width:80px">型号</th>
                                <th style="width:80px">实际收货数量</th>
                                <th style="width:80px">单位</th>
                                <th style="width:80px">贝登批次码</th>
                                <th style="width:80px">生产日期</th>
                                <th style="width:80px">有效期至</th>
                                <th style="width:80px">入库时间</th>
                                <th style="width:80px">厂家批次号</th>
                                <th style="width:80px">灭菌编号</th>
                                <th style="width:80px">注册证号</th>
                            </tr>
                            </thead>
                            <tbody id="inRecordList">
                            <c:choose>
                                <c:when test="${not empty outInDetailList}">
                                    <c:forEach var="var" items="${outInDetailList}" varStatus="staut">
                                        <tr>
                                            <td><a class="addtitle" href="javascript:void(0);"
                                                   tabTitle='{"num":"","link":"/warehouseOutIn/detail.do?warehouseGoodsOutInId=${var.warehouseGoodsOutInId}","title":"盘盈入库详情"}'>${var.outInNo}</a></td>
                                            <td>${staut.index+1}</td>
                                            <td>${var.skuNo}</td>
                                            <td> <a class="addtitle" href="javascript:void(0);"
                                                    tabTitle='{"num":"viewgoods${var.skuId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${var.skuId}","title":"产品信息"}'>${var.skuName}</a></td>
                                            <td>${var.brandName}</td>
                                            <td>${var.model}</td>
                                            <td>${var.num}</td>
                                            <td>${var.unitName}</td>
                                            <td>${var.vedengBatchNumer}</td>
                                            <td>${var.productDateStr}</td>
                                            <td>${var.expirationDateStr}</td>
                                            <td>${var.checkStatusTimeStr}</td>
                                            <td>${var.batchNumber}</td>
                                            <td>${var.sterilizationBatchNo}</td>
                                            <td><a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewfirstgoods${var.firstEngageId}","link":"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${var.firstEngageId}","title":"首营信息"}'>${var.registrationNumber}</a></td>
                                        </tr>
                                    </c:forEach>
                                </c:when>
                                <c:otherwise>
                                    <tr><td colspan="15" style="text-align: center;">暂无入库记录！</td></tr>
                                </c:otherwise>
                            </c:choose>
                            </tbody>
                        </table>
                    </div>
                </li>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor ">审核记录</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">操作人</th>
                                <th style="width:80px">操作时间</th>
                                <th style="width:80px">操作事项</th>
                                <th style="width:80px">备注</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                                <c:if test="${not empty  hi.activityName}">
                                    <tr>
                                        <td>
                                            <c:choose>
                                                <c:when test="${hi.activityType == 'startEvent'}">
                                                    ${startUser}
                                                </c:when>
                                                <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                </c:when>
                                                <c:otherwise>
                                                    <c:if test="${historicActivityInstance.size() == status.count}">
                                                        <c:forEach var="vs" items="${verifyUsersList}" varStatus="status">
                                                            <c:if test="${fn:contains(verifyUserList, vs)}">
                                                                <span class="font-green">${vs}</span>&nbsp;
                                                            </c:if>
                                                            <c:if test="${!fn:contains(verifyUserList, vs)}">
                                                                <span>${vs}</span>&nbsp;
                                                            </c:if>
                                                        </c:forEach>

                                                        <c:if test="${empty verifyUsersList && empty hi.assignee}">
                                                            ${verifyUsers}
                                                        </c:if>
                                                    </c:if>
                                                    <c:if test="${historicActivityInstance.size() != status.count}">
                                                        ${hi.assignee}
                                                    </c:if>
                                                </c:otherwise>
                                            </c:choose>


                                        </td>
                                        <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${hi.activityType == 'startEvent'}">
                                                    开始
                                                </c:when>
                                                <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                    结束
                                                </c:when>
                                                <c:otherwise>
                                                    ${hi.activityName}
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td class="font-red">${commentMap[hi.taskId]}</td>
                                    </tr>
                                </c:if>
                            </c:forEach>
                            <!-- 查询无结果弹出 -->

                            <c:if test="${empty historicActivityInstance}">
                                <!-- 查询无结果弹出 -->
                                <tr>
                                    <td colspan="4">暂无审核记录。</td>
                                </tr>
                            </c:if>
                            </tbody>
                        </table>
                    </div>
                </li>
            </ul>
            <div class="add-tijiao tcenter mt10">

                <c:if test="${null!=historicInfo.taskInfo and (taskInfo.assignee == curr_user.username or historicInfo.candidateUserMap['belong']) }">
                    <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&wmsInputOrderId=${wmsInputOrder.wmsInputOrderId}&pass=true"}'>审核通过</button>
                    <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&wmsInputOrderId=${wmsInputOrder.wmsInputOrderId}&pass=false"}'>审核不通过</button>
                </c:if>

                <c:if test="${cancelflag}">
                    <button type="button" class="bt-bg-style bg-deep-red" onclick="closeSurplus();">取消申请</button>
                </c:if>

            </div>
    </div>
</div>
<%@ include file="../../common/footer.jsp"%>