package com.vedeng.erp.kingdee.batch.dto;

import java.math.BigDecimal;
import java.util.Date;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 采购费用明细详情
 * <AUTHOR>
 */
@Getter
@Setter
public class BatchBuyorderExpenseItemDetailDto extends BatchBaseDto {
    /**
     * 主键
     */
    private Integer buyorderExpenseItemDetailId;

    /**
     * 采购明细表id
     */
    private Integer buyorderExpenseItemId;

    /**
     * 唯一编码
     */
    private String sku;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 商品型号
     */
    private String model;

    /**
     * 单位中文名
     */
    private String unitName;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 货币单位ID
     */
    private Integer currencyUnitId;

    /**
     * 虚拟商品所属费用类别ID
     */
    private Integer expenseCategoryId;

    /**
     * 虚拟商品费用类别名称
     */
    private String expenseCategoryName;

    /**
     * 金蝶费用编码
     */
    private String unitKingDeeNo;


    /**
     * 虚拟商品是否有库存管理
     */
    private Integer haveStockManage;

    /**
     * 采购费用商品备注
     */
    private String insideComments;

    /**
     * 是否删除0否1是
     */
    private Integer isDelete;


}