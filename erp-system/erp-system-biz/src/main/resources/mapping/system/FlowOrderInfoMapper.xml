<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.FlowOrderInfoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.FlowOrderInfoEntity">
    <!--@mbg.generated-->
    <!--@Table T_FLOW_ORDER_INFO-->
    <id column="FLOW_ORDER_INFO_ID" property="flowOrderInfoId" />
    <result column="FLOW_ORDER_INFO_TYPE" property="flowOrderInfoType" />
    <result column="FLOW_ORDER_INFO_NO" property="flowOrderInfoNo" />
    <result column="FLOW_NODE_ID" property="flowNodeId" />
    <result column="ORDER_STATUS" property="orderStatus" />
    <result column="PAYMENT_STATUS" property="paymentStatus" />
    <result column="STORAGE_STATUS" property="storageStatus" />
    <result column="INVOICE_STATUS" property="invoiceStatus" />
    <result column="INVOICE_INFO" property="invoiceInfo" />
    <result column="CONTRACT_FILE_URL" property="contractFileUrl" />
    <result column="CONTRACT_FILE_NAME" property="contractFileName" />
    <result column="CONTRACT_UPLOAD_TIME" property="contractUploadTime" />
    <result column="IS_DELETE" property="isDelete" />
    <result column="CREATOR" property="creator" />
    <result column="UPDATER" property="updater" />
    <result column="CREATOR_NAME" property="creatorName" />
    <result column="UPDATER_NAME" property="updaterName" />
    <result column="ADD_TIME" property="addTime" />
    <result column="MOD_TIME" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    FLOW_ORDER_INFO_ID, FLOW_ORDER_INFO_TYPE, FLOW_ORDER_INFO_NO, FLOW_NODE_ID, ORDER_STATUS,
    PAYMENT_STATUS, STORAGE_STATUS, INVOICE_STATUS, INVOICE_INFO, CONTRACT_FILE_URL,
    CONTRACT_FILE_NAME, CONTRACT_UPLOAD_TIME, IS_DELETE, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, ADD_TIME, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_FLOW_ORDER_INFO
    where FLOW_ORDER_INFO_ID = #{flowOrderInfoId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_FLOW_ORDER_INFO
    where FLOW_ORDER_INFO_ID = #{flowOrderInfoId}
  </delete>
  <insert id="insert" keyColumn="FLOW_ORDER_INFO_ID" keyProperty="flowOrderInfoId" parameterType="com.vedeng.erp.system.domain.entity.FlowOrderInfoEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_FLOW_ORDER_INFO (FLOW_ORDER_INFO_TYPE, FLOW_ORDER_INFO_NO,
      FLOW_NODE_ID, ORDER_STATUS, PAYMENT_STATUS,
      STORAGE_STATUS, INVOICE_STATUS, INVOICE_INFO,
      CONTRACT_FILE_URL, CONTRACT_FILE_NAME,
      CONTRACT_UPLOAD_TIME, IS_DELETE, CREATOR,
      UPDATER, CREATOR_NAME, UPDATER_NAME,
      ADD_TIME, MOD_TIME)
    values (#{flowOrderInfoType}, #{flowOrderInfoNo},
      #{flowNodeId}, #{orderStatus}, #{paymentStatus},
      #{storageStatus}, #{invoiceStatus}, #{invoiceInfo},
      #{contractFileUrl}, #{contractFileName},
      #{contractUploadTime}, #{isDelete}, #{creator},
      #{updater}, #{creatorName}, #{updaterName},
      #{addTime}, #{modTime})
  </insert>
  <insert id="insertSelective" keyColumn="FLOW_ORDER_INFO_ID" keyProperty="flowOrderInfoId" parameterType="com.vedeng.erp.system.domain.entity.FlowOrderInfoEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_FLOW_ORDER_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="flowOrderInfoType != null">
        FLOW_ORDER_INFO_TYPE,
      </if>
      <if test="flowOrderInfoNo != null and flowOrderInfoNo != ''">
        FLOW_ORDER_INFO_NO,
      </if>
      <if test="flowNodeId != null">
        FLOW_NODE_ID,
      </if>
      <if test="orderStatus != null">
        ORDER_STATUS,
      </if>
      <if test="paymentStatus != null">
        PAYMENT_STATUS,
      </if>
      <if test="storageStatus != null">
        STORAGE_STATUS,
      </if>
      <if test="invoiceStatus != null">
        INVOICE_STATUS,
      </if>
      <if test="invoiceInfo != null and invoiceInfo != ''">
        INVOICE_INFO,
      </if>
      <if test="contractFileUrl != null and contractFileUrl != ''">
        CONTRACT_FILE_URL,
      </if>
      <if test="contractFileName != null and contractFileName != ''">
        CONTRACT_FILE_NAME,
      </if>
      <if test="contractUploadTime != null">
        CONTRACT_UPLOAD_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="flowOrderInfoType != null">
        #{flowOrderInfoType},
      </if>
      <if test="flowOrderInfoNo != null and flowOrderInfoNo != ''">
        #{flowOrderInfoNo},
      </if>
      <if test="flowNodeId != null">
        #{flowNodeId},
      </if>
      <if test="orderStatus != null">
        #{orderStatus},
      </if>
      <if test="paymentStatus != null">
        #{paymentStatus},
      </if>
      <if test="storageStatus != null">
        #{storageStatus},
      </if>
      <if test="invoiceStatus != null">
        #{invoiceStatus},
      </if>
      <if test="invoiceInfo != null and invoiceInfo != ''">
        #{invoiceInfo},
      </if>
      <if test="contractFileUrl != null and contractFileUrl != ''">
        #{contractFileUrl},
      </if>
      <if test="contractFileName != null and contractFileName != ''">
        #{contractFileName},
      </if>
      <if test="contractUploadTime != null">
        #{contractUploadTime},
      </if>
      <if test="isDelete != null">
        #{isDelete},
      </if>
      <if test="creator != null">
        #{creator},
      </if>
      <if test="updater != null">
        #{updater},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName},
      </if>
      <if test="addTime != null">
        #{addTime},
      </if>
      <if test="modTime != null">
        #{modTime},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.FlowOrderInfoEntity">
    <!--@mbg.generated-->
    update T_FLOW_ORDER_INFO
    <set>
      <if test="flowOrderInfoType != null">
        FLOW_ORDER_INFO_TYPE = #{flowOrderInfoType},
      </if>
      <if test="flowOrderInfoNo != null and flowOrderInfoNo != ''">
        FLOW_ORDER_INFO_NO = #{flowOrderInfoNo},
      </if>
      <if test="flowNodeId != null">
        FLOW_NODE_ID = #{flowNodeId},
      </if>
      <if test="orderStatus != null">
        ORDER_STATUS = #{orderStatus},
      </if>
      <if test="paymentStatus != null">
        PAYMENT_STATUS = #{paymentStatus},
      </if>
      <if test="storageStatus != null">
        STORAGE_STATUS = #{storageStatus},
      </if>
      <if test="invoiceStatus != null">
        INVOICE_STATUS = #{invoiceStatus},
      </if>
      <if test="invoiceInfo != null and invoiceInfo != ''">
        INVOICE_INFO = #{invoiceInfo},
      </if>
      <if test="contractFileUrl != null">
        CONTRACT_FILE_URL = #{contractFileUrl},
      </if>
      <if test="contractFileName != null">
        CONTRACT_FILE_NAME = #{contractFileName},
      </if>
      <if test="contractUploadTime != null">
        CONTRACT_UPLOAD_TIME = #{contractUploadTime},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete},
      </if>
      <if test="creator != null">
        CREATOR = #{creator},
      </if>
      <if test="updater != null">
        UPDATER = #{updater},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime},
      </if>
    </set>
    where FLOW_ORDER_INFO_ID = #{flowOrderInfoId}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.FlowOrderInfoEntity">
    <!--@mbg.generated-->
    update T_FLOW_ORDER_INFO
    set FLOW_ORDER_INFO_TYPE = #{flowOrderInfoType},
      FLOW_ORDER_INFO_NO = #{flowOrderInfoNo},
      FLOW_NODE_ID = #{flowNodeId},
      ORDER_STATUS = #{orderStatus},
      PAYMENT_STATUS = #{paymentStatus},
      STORAGE_STATUS = #{storageStatus},
      INVOICE_STATUS = #{invoiceStatus},
      INVOICE_INFO = #{invoiceInfo},
      CONTRACT_FILE_URL = #{contractFileUrl},
      CONTRACT_FILE_NAME = #{contractFileName},
      CONTRACT_UPLOAD_TIME = #{contractUploadTime},
      IS_DELETE = #{isDelete},
      CREATOR = #{creator},
      UPDATER = #{updater},
      CREATOR_NAME = #{creatorName},
      UPDATER_NAME = #{updaterName},
      ADD_TIME = #{addTime},
      MOD_TIME = #{modTime}
    where FLOW_ORDER_INFO_ID = #{flowOrderInfoId}
  </update>

  <select id="findByFlowNodeId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_FLOW_ORDER_INFO
    where FLOW_NODE_ID = #{flowNodeId}
    and IS_DELETE = 0
  </select>

  <select id="findByFlowOrderInfoNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_FLOW_ORDER_INFO
    where FLOW_ORDER_INFO_NO = #{flowOrderInfoNo}
    and IS_DELETE = 0
  </select>


  <select id="findByFlowNodeIdAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_FLOW_ORDER_INFO
    where FLOW_NODE_ID = #{flowNodeId}
    and FLOW_ORDER_INFO_TYPE = #{flowOrderInfoType}
    and IS_DELETE = 0
  </select>
</mapper>
