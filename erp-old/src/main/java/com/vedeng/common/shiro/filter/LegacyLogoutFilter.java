package com.vedeng.common.shiro.filter;

import com.vedeng.common.shiro.constant.SecurityConstants;

import org.apache.commons.lang.StringUtils;
import org.apache.shiro.web.filter.authc.LogoutFilter;
import org.apache.shiro.web.util.WebUtils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class LegacyLogoutFilter extends LogoutFilter {

    @Override
    protected void issueRedirect(ServletRequest request, ServletResponse response, String redirectUrl) throws Exception {
        super.issueRedirect(request, response, SecurityConstants.LOGIN_URL);

        HttpServletRequest httpServletRequest = WebUtils.toHttp(request);
        HttpServletResponse httpServletResponse = WebUtils.toHttp(response);

        Cookie cookieName = new Cookie("userName", "1");
        cookieName.setMaxAge(0);
        cookieName.setPath("/");
        cookieName.setHttpOnly(true);
        httpServletResponse.addCookie(cookieName);

        String serverName = request.getServerName();
        if (StringUtils.endsWith(serverName, "ivedeng.com")) {
            serverName = "ivedeng.com";
        }
//        DoCookie cookie = new DoCookie(serverName, httpServletRequest, httpServletResponse);
//        cookie.deleteCookie(CoookieConstants.IVEDENG_SID);
    }
}
