package com.newtask.filemove.dto;

import com.newtask.filemove.FileMoveInterface;

/**
 * 命令处理器的结果
 */
public class CommandProcessorResult {

    private FileMoveInterface fileMoveInterface;

    private String requestParam;

    public void fileMove() throws Exception{
        fileMoveInterface.fileMove(requestParam);
    }

    public CommandProcessorResult(FileMoveInterface fileMoveInterface, String requestParam) {
        this.fileMoveInterface = fileMoveInterface;
        this.requestParam = requestParam;
    }

    public FileMoveInterface getFileMoveInterface() {
        return fileMoveInterface;
    }

    public void setFileMoveInterface(FileMoveInterface fileMoveInterface) {
        this.fileMoveInterface = fileMoveInterface;
    }

    public String getRequestParam() {
        return requestParam;
    }

    public void setRequestParam(String requestParam) {
        this.requestParam = requestParam;
    }
}
