<%--
  Created by IntelliJ IDEA.
  User: ckt
  Date: 2023/7/21
  Time: 15:33
  To change this template use File | Settings | File Templates.
--%>

<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<head>
    <title>商机渠道分类</title>
</head>
<!-- 引入样式 -->
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/element-ui/css/index.css">
<!-- 引入脚本 -->
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/vue.js"></script>
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/ele-index.js"></script>
<style>
    #app {
        font-size: 16px;
    }
    .head {
        background-color: #f8f8f8;
    }
    .headButton {
        text-align: center;
        padding-bottom: 20px;
        padding-top: 40px;
    }
    .custom-tree-node {
        width: 100%;
    }
    .nodeSpan {
        font-size: 16px;
        display: inline-block;
        width: 300px;
        font-family: "微软雅黑", Arial, Verdana, "microsoft yahei";
    }
    .footer {
        padding-top: 20px;
        position: absolute;
        right: 20px;
        bottom: 0;
        padding-bottom: 80px;
    }
    .footerContainer {
        width: 100%;
        height: 300px;
        padding: 20px;
        background-color: #f8f8f8;
    }
    .custom-tree-container {
        padding: 20px;
    }
    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        padding-right: 8px;
    }
    .input-slot {
        width: 400px;
    }
    .span-text {
        display: inline-block;
        width: 150px;
        font-size: 16px;
        padding-top: 20px;
        text-align: right;
        padding-right: 20px;
    }
    .span-title {
        padding-top: 20px;
        display: inline-block;
    }


</style>

<div id="app">
    <%--头--%>
    <div class="head">
        <div class="searchName" style="padding-top: 20px">
            <span>&nbsp;&nbsp;渠道类型/渠道名称：<el-input v-model="searchName" placeholder="请输入" size="medium" style="width: 200px;padding-left: 20px"></el-input></span>
        </div>
        <div class="headButton">
            <el-button type="primary" plain @click="newSource()">新增渠道类型</el-button>
            <el-button type="warning" plain @click="reset()">重置</el-button>
        </div>
    </div>
    <%--内容--%>
        <el-button size="small" plain icon="el-icon-circle-plus" type="success" @click="expandButton()" style="margin: 10px">展开</el-button>
        <div>
        <span class="span-title" style="width: 30%;padding-left: 60px">名称</span>
        <span class="span-title" style="position: absolute;left: 761px">排序</span>
        <span class="span-title" style="position: absolute;right: 359px">操作</span>
        </div>
        <div class="custom-tree-container">
            <div class="block">
                <el-tree
                        :data="newSourceList"
                        node-key="sysOptionDefinitionId"
                        :expand-on-click-node="false"
                        :default-expand-all="expandAll"
                        :filter-node-method="filterNode" ref="tree">
                     <span class="custom-tree-node" slot-scope="{ node, data }">
                        <span class="nodeSpan"><pre>{{ data.title }}</pre></span>
                        <span class="nodeSpan nodeWeight" style="position: absolute;left: 750px">{{data.sort}}</span>
                        <span class="nodeSpan" style="position: absolute;right: 100px">
                           <el-button style="font-size: 18px" type="text" size="mini" @click="() => edit(node,data)">
                            编辑
                           </el-button>
                           <el-button v-if="data.children" style="font-size: 18px" type="text" size="mini" @click="() => addChildren(data)" >
                            添加渠道
                           </el-button>
                        </span>
                     </span>
               </el-tree>
            </div>
        </div>
    <%--底部--%>
        <%--分类--%>
        <el-dialog :title="slotTitle" :visible.sync="newSourceVisible" width="50%" center="true">
            <div>
                <span class="span-text"><span style="color: red">*</span>渠道类型:</span>
                <el-input class="input-slot" placeholder="请输入渠道类型" v-model="newSourceTitle" maxlength="20" show-word-limit></el-input>
            </div>
            <div>
                <span class="span-text">排序:</span>
                <el-input type="number" class="input-slot" v-model="newSourceSort" maxlength="6"
                          οninput="value=value.replace(/^0+(\d)|[^\d]+/g,'')"
                ></el-input></div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="newSourceVisible = false">取 消</el-button>
                <el-button type="primary" @click="newSourceSubmit()">确 定</el-button>
            </span>
        </el-dialog>
        <%--渠道--%>
        <el-dialog :title="slotTitle" :visible.sync="childVisible" width="50%" center="true">
            <div style="font-size: 16px">
                <span class="span-text"><span style="color: red">*</span>渠道类型:</span>
                {{newSourceName}}
            </div>
            <div>
                <span class="span-text">排序:</span>
                <el-input type="number" class="input-slot" v-model="childSort"></el-input>
            </div>
            <div>
                <span class="span-text"><span style="color: red">*</span>渠道名称:</span>
                <el-input class="input-slot" placeholder="请输入渠道名称" v-model="childTitle" maxlength="20" show-word-limit></el-input>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="childVisible = false">取 消</el-button>
                <el-button type="primary" @click="childSubmit()">确 定</el-button>
            </span>
        </el-dialog>
</div>

<script type="text/javascript">
    var newSourceList = ${newSourceList};
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                newSourceList: newSourceList,
                searchName: '',
                newSourceVisible: false,
                newSourceTitle: '',
                newSourceSort: 0,
                newSourceIsEdit: false,
                childVisible: false,
                childTitle: '',
                childSort: 0,
                childIsEdit: false,
                slotTitle: '',
                newSourceName: '',
                expandAll: false,
                sysOptionDefinitionId : null,
            }
        },
        methods: {
            expandButton(){
                this.expandAll = !this.expandAll;
                var nodes = this.$refs.tree.store.nodesMap;
                for(var i in nodes){
                    nodes[i].expanded = this.expandAll;
                }
            },
            filterNode(value, data) {
                if (!value) return true;
                return data.title.indexOf(value) !== -1;
            },
            newSource(){
                this.slotTitle = '新增渠道类型';
                this.newSourceTitle = '';
                this.newSourceSort = 0;
                this.newSourceIsEdit = false;
                this.newSourceVisible = true;
            },
            newSourceSubmit(){
                //提交请求
                if(this.newSourceTitle == ''){
                    this.$message({
                        message: '渠道类型不可为空',
                        type: 'error'
                    })
                    return;
                }
                if (!(/(^[0-9]\d*$)/.test(this.newSourceSort))){
                    this.$message({
                        message: '仅限输入大于等于0的阿拉伯整数，最多6位数',
                        type: 'error'
                    })
                    return;
                }
                if (this.newSourceSort > 999999 || this.newSourceSort < 0 || null == this.newSourceSort || this.newSourceSort === ''){
                    this.$message({
                        message: '仅限输入大于等于0的阿拉伯整数，最多6位数',
                        type: 'error'
                    })
                    return;
                }
                axios({
                    url: '/order/bussinesschance/addNewSource.do',
                    method: 'post',
                    data: {
                        "title": this.newSourceTitle,
                        "sort": this.newSourceSort,
                        "sysOptionDefinitionId": this.newSourceIsEdit ? this.sysOptionDefinitionId : null
                    }
                }).then(res => {
                    if (res.data.code === 0) {
                        window.location.reload();
                        this.newSourceVisible = false;
                        this.newSourceName = '';
                        this.newSourceSort = 0;
                    }else {
                        this.$message({
                            message: res.data.message,
                            type: 'error'
                        })
                    }
                });
            },
            childSubmit(){
                //提交请求
                if (this.childTitle == ''){
                    this.$message({
                        message: '渠道名称不可为空',
                        type: 'error'
                    })
                    return;
                }
                if (!(/(^[0-9]\d*$)/.test(this.childSort))){
                    this.$message({
                        message: '仅限输入大于等于0的阿拉伯整数，最多6位数',
                        type: 'error'
                    })
                    return;
                }
                if (this.childSort > 999999 || this.childSort < 0 || null == this.childSort || this.childSort === ''){
                    this.$message({
                        message: '仅限输入大于等于0的阿拉伯整数，最多6位数',
                        type: 'error'
                    })
                    return;
                }
                axios({
                    url: '/order/bussinesschance/addChild.do',
                    method: 'post',
                    data: {
                        "title": this.childTitle,
                        "sort": this.childSort,
                        "parentId": this.parentId,
                        "sysOptionDefinitionId": this.childIsEdit ? this.sysOptionDefinitionId : null
                    }
                }).then(res => {
                    if (res.data.code === 0) {
                        window.location.reload();
                        this.childVisible = false;
                    }else {
                        console.log(res)
                        this.$message({
                            message: res.data.message,
                            type: 'error'
                        })
                    }
                });
            },
            edit(node,data) {
                if (data.parentNode){
                    this.slotTitle = '编辑渠道类型';
                    this.newSourceIsEdit = true;
                    this.newSourceVisible = true;
                    this.sysOptionDefinitionId = data.sysOptionDefinitionId;
                    this.newSourceTitle = data.title
                    this.newSourceSort = data.sort
                }else {
                    this.slotTitle = '编辑渠道';
                    this.childIsEdit = true;
                    this.childVisible = true;
                    this.newSourceName = node.parent.data.title;
                    this.sysOptionDefinitionId = data.sysOptionDefinitionId;
                    this.childTitle = data.title
                    this.childSort = data.sort
                    this.parentId = node.parent.data.sysOptionDefinitionId
                }
            },
            addChildren(data) {
                this.slotTitle = '添加渠道';
                this.childIsEdit = false;
                this.childVisible = true;
                this.newSourceName = data.title
                this.parentId = data.sysOptionDefinitionId
                this.childTitle = ''
                this.childSort = 0
            },
            reset() {
                this.searchName='';
                var nodes = this.$refs.tree.store.nodesMap;
                for(var i in nodes){
                    nodes[i].expanded = false;
                }
            }
        },
        watch: {
            searchName(val) {
                if (val == ''){
                    var nodes = this.$refs.tree.store.nodesMap;
                    for(var i in nodes){
                        nodes[i].expanded = false;
                    }
                }
                this.$refs.tree.filter(val);
            },
        }
    });
</script>
