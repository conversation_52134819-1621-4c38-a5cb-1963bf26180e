package com.vedeng.erp.kingdee.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Objects;


public class HashUtils {

    private static final String MD5_ALGORITHM = "MD5";
    private static final String SHA1_ALGORITHM = "SHA-1";

    private static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;

    private static final char[] HEX_ARRAY = "0123456789ABCDEFGHIGKLMN".toCharArray();

    private static final  MessageDigest MD5_DIGEST;
    private static final  MessageDigest SHA1_DIGEST;

    static {
        try {
            MD5_DIGEST = MessageDigest.getInstance(MD5_ALGORITHM);
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalStateException("No such algorithm: " + MD5_ALGORITHM);
        }
        try {
            SHA1_DIGEST = MessageDigest.getInstance(SHA1_ALGORITHM);
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalStateException("No such algorithm: " + SHA1_ALGORITHM);
        }
    }


    /**
     * 返回MD5文本
     * @param text 原始文本
     * @param length 返回加密文本长度
     * @return
     */
    public static String md5ToHexString(String text,int length) {
        return hashToHexString(text, MD5_ALGORITHM,length);
    }


    /**
     * 返回sha1加文本
     *
     * @param text
     * @return
     */
    public static String sha1ToHexString(String text,int length) {
        return hashToHexString(text, SHA1_ALGORITHM,length);
    }

    private static String hashToHexString(String text, String algorithm,int length) {
        if (StringUtils.isEmpty(text)) {
            return null;
        }

        MessageDigest digest = getDigestSupported(algorithm);
        if (digest == null) {
            throw new UnsupportedOperationException("Not support algorithm: " + algorithm);
        }

        byte[] byteArray = null;
        synchronized (digest) {
            try {
                byteArray = digest.digest(text.getBytes(DEFAULT_CHARSET));
            } catch (Exception e) {
            }
        }

        if (byteArray == null) {
            return null;
        }

        return toHexString(byteArray,length);
    }

    private static MessageDigest getDigestSupported(String algorithm) {
        if (StringUtils.isEmpty(algorithm)) {
            return null;
        }
        MessageDigest messageDigest = null;
        if (MD5_ALGORITHM.equals(algorithm)) {
            messageDigest = MD5_DIGEST;
        } else if (SHA1_ALGORITHM.equals(algorithm)) {
            messageDigest = SHA1_DIGEST;
        }

        return messageDigest;
    }


    private static String toHexString(byte[] bytes,int length) {
        StringBuilder builder = new StringBuilder(bytes.length << 1);
        int len = bytes.length;
        if(Objects.nonNull(length)) {
        	len = length;
        }
        for (int i = 0; i < len; ++i) {
            byte aByte = bytes[i];
            builder.append(HEX_ARRAY[(aByte & 240) >> 4]);
        }

        return builder.toString();
    }
    
}
