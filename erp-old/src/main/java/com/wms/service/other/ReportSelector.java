package com.wms.service.other;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ai
 */
public class ReportSelector {

    /**
     * 选择合适的检验报告
     * @param reportList 检验报告数据集
     * @return 选中的检验报告文件名
     */
    public static String selectReport(List<Map<String, String>> reportList) {
        if (reportList == null || reportList.isEmpty()) {
            return null;
        }

        // 如果只有一条数据，直接返回
        if (reportList.size() == 1) {
            return reportList.get(0).values().iterator().next();
        }

        // 检查是否所有文件名都相同
        Set<String> uniqueFileNames = reportList.stream()
                .map(map -> map.values().iterator().next())
                .collect(Collectors.toSet());

        // 如果所有文件名相同，返回最新的
        if (uniqueFileNames.size() == 1) {
            return getLatestReport(reportList).values().iterator().next();
        }

        // 获取最新一年的数据
        List<Map<String, String>> latestYearReports = getLatestYearReports(reportList);

        // 如果最新一年只有一条数据
        if (latestYearReports.size() == 1) {
            return latestYearReports.get(0).values().iterator().next();
        }

        // 找出最新一年中文件名最长的长度
        int maxLength = latestYearReports.stream()
                .map(map -> map.values().iterator().next().length())
                .max(Integer::compareTo)
                .orElse(0);

        // 筛选出文件名最长的记录
        List<Map<String, String>> longestFileReports = latestYearReports.stream()
                .filter(map -> map.values().iterator().next().length() == maxLength)
                .collect(Collectors.toList());

        // 如果文件名最长的只有一条记录
        if (longestFileReports.size() == 1) {
            return longestFileReports.get(0).values().iterator().next();
        }

        // 如果有多条最长文件名的记录，返回最新的
        return getLatestReport(longestFileReports).values().iterator().next();
    }

    /**
     * 获取最新的报告记录
     */
    private static Map<String, String> getLatestReport(List<Map<String, String>> reports) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        return reports.stream()
                .max(Comparator.comparing(map ->
                        LocalDateTime.parse(map.keySet().iterator().next(), formatter)))
                .orElse(null);
    }

    /**
     * 获取最新一年的报告记录
     */
    private static List<Map<String, String>> getLatestYearReports(List<Map<String, String>> reports) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

        // 找出最新的年份
        int latestYear = reports.stream()
                .map(map -> LocalDateTime.parse(map.keySet().iterator().next(), formatter).getYear())
                .max(Integer::compareTo)
                .orElse(0);

        // 筛选最新年份的记录
        return reports.stream()
                .filter(map ->
                        LocalDateTime.parse(map.keySet().iterator().next(), formatter).getYear() == latestYear)
                .collect(Collectors.toList());
    }

    // 测试代码
    public static void main(String[] args) {
        // 测试用例1：单条数据
        System.out.println("测试用例1：单条数据");
        List<Map<String, String>> test1 = new ArrayList<>();
        Map<String, String> report1 = new HashMap<>();
        report1.put("20240101000000", "report1.pdf");
        test1.add(report1);
        System.out.println("Expected: report1.pdf");
        System.out.println("Actual: " + selectReport(test1));
        System.out.println();

        // 测试用例2：多条相同文件名
        System.out.println("测试用例2：多条相同文件名，取最新时间");
        List<Map<String, String>> test2 = new ArrayList<>();
        Map<String, String> report2_1 = new HashMap<>();
        report2_1.put("20240101000000", "same.pdf");
        Map<String, String> report2_2 = new HashMap<>();
        report2_2.put("20240102000000", "same.pdf");
        test2.add(report2_1);
        test2.add(report2_2);
        System.out.println("Expected: same.pdf");
        System.out.println("Actual: " + selectReport(test2));
        System.out.println();

        // 测试用例3：最新一年单条数据
        System.out.println("测试用例3：最新一年单条数据");
        List<Map<String, String>> test3 = new ArrayList<>();
        Map<String, String> report3_1 = new HashMap<>();
        report3_1.put("20230101000000", "old.pdf");
        Map<String, String> report3_2 = new HashMap<>();
        report3_2.put("20240101000000", "new.pdf");
        test3.add(report3_1);
        test3.add(report3_2);
        System.out.println("Expected: new.pdf");
        System.out.println("Actual: " + selectReport(test3));
        System.out.println();

        // 测试用例4：最新一年多条数据，文件名长度不同
        System.out.println("测试用例4：最新一年多条数据，文件名长度不同，取最长");
        List<Map<String, String>> test4 = new ArrayList<>();
        Map<String, String> report4_1 = new HashMap<>();
        report4_1.put("20240101000000", "short.pdf");
        Map<String, String> report4_2 = new HashMap<>();
        report4_2.put("20240102000000", "longer_file.pdf");
        Map<String, String> report4_3 = new HashMap<>();
        report4_3.put("20240103000000", "short.pdf");
        Map<String, String> report4_4 = new HashMap<>();
        report4_4.put("20230103000000", "longer_filelonger_file.pdf");
        test4.add(report4_1);
        test4.add(report4_2);
        test4.add(report4_3);
        test4.add(report4_4);
        System.out.println("Expected: longer_file.pdf");
        System.out.println("Actual: " + selectReport(test4));
        System.out.println();

        // 测试用例5：最新一年多条数据，最长文件名有多条，取最新时间
        System.out.println("测试用例5：最新一年多条数据，最长文件名有多条，取最新时间");
        List<Map<String, String>> test5 = new ArrayList<>();
        Map<String, String> report5_1 = new HashMap<>();
        report5_1.put("20240101000000", "long_file1.pdf");
        Map<String, String> report5_2 = new HashMap<>();
        report5_2.put("20240102000000", "long_file2.pdf");
        test5.add(report5_1);
        test5.add(report5_2);
        System.out.println("Expected: long_file2.pdf");
        System.out.println("Actual: " + selectReport(test5));
        System.out.println();

        // 测试用例6：跨年份数据，取最新年份
        System.out.println("测试用例6：跨年份数据，取最新年份");
        List<Map<String, String>> test6 = new ArrayList<>();
        Map<String, String> report6_1 = new HashMap<>();
        report6_1.put("20230101000000", "very_long_file_name.pdf");
        Map<String, String> report6_2 = new HashMap<>();
        report6_2.put("20240101000000", "short.pdf");
        test6.add(report6_1);
        test6.add(report6_2);
        System.out.println("Expected: short.pdf");
        System.out.println("Actual: " + selectReport(test6));
    }
}