<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.goods.dao.CategoryMigrationMapper" >
    <resultMap id="BaseResultMap" type="com.vedeng.goods.model.CategoryMigration" >
        <id column="CATEGORY_MIGTATION_LOG_ID" property="categoryMigrationId" jdbcType="INTEGER" />
        <result column="CATEGORY_ID" property="categoryId" jdbcType="INTEGER" />
        <result column="CATEGORY_NAME" property="categoryName" jdbcType="VARCHAR" />
        <result column="ORIGIN_PATH" property="originPath" jdbcType="VARCHAR" />
        <result column="TARGET_PATH" property="targetPath" jdbcType="VARCHAR" />
        <result column="ORIGIN_PARENT_ID" property="originParentId" jdbcType="INTEGER" />
        <result column="TARGET_PARENT_ID" property="targetParentId" jdbcType="INTEGER" />
        <result column="MIGRATION_REASON" property="migrationReason" jdbcType="VARCHAR" />
        <result column="ADD_TIME" property="addTime" jdbcType="BIT" />
        <result column="CREATOR" property="creator" jdbcType="INTEGER" />
        <result column="UPDATE_TIME" property="updateTime" jdbcType="BIT" />
        <result column="UPDATER" property="updater" jdbcType="INTEGER" />
        <result column="IS_DELETE" property="isDeleted" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List" >
   CATEGORY_MIGTATION_LOG_ID, CATEGORY_ID,CATEGORY_NAME,ORIGIN_PATH,TARGET_PATH, ORIGIN_PARENT_ID, TARGET_PARENT_ID, MIGRATION_REASON, ADD_TIME,
   CREATOR,UPDATE_TIME,UPDATER,IS_DELETE
  </sql>

    <insert id="insertCategoryMigration" parameterType="com.vedeng.goods.model.CategoryMigration">
        insert into T_CATEGORY_MIGRATION_LOG(CATEGORY_ID,CATEGORY_NAME,ORIGIN_PATH,TARGET_PATH,ORIGIN_PARENT_ID,TARGET_PARENT_ID,MIGRATION_REASON,ADD_TIME,CREATOR,UPDATE_TIME,UPDATER,IS_DELETE)
        values (
        #{categoryId},#{categoryName},#{originPath},#{targetPath},
        #{originParentId},#{targetParentId},#{migrationReason},
        #{addTime},#{creator},#{updateTime},#{updater},#{isDeleted}
        )
    </insert>

    <select id="getCtegoryMigretionlistPage" parameterType="com.vedeng.goods.model.vo.CategoryMigrationVo" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from  T_CATEGORY_MIGRATION_LOG
        where IS_DELETE=0
        <if test="categoryMigrationVo.categoryName != null and categoryMigrationVo.categoryName != ''" >
            and CATEGORY_NAME like CONCAT('%',#{categoryMigrationVo.categoryName},'%' )
        </if>
        <if test="categoryMigrationVo.categoryId != null" >
            and CATEGORY_ID like CONCAT('%',#{categoryMigrationVo.categoryId},'%' )
        </if>
    </select>
</mapper>