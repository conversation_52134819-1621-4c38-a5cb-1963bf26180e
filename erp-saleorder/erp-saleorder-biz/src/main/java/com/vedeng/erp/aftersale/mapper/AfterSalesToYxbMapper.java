package com.vedeng.erp.aftersale.mapper;

import com.vedeng.erp.aftersale.domain.dto.AfterSalesToYxbDto;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesToYxbEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AfterSalesToYxbMapper {

    int deleteByPrimaryKey(Integer afterSalesToYxbId);


    int insert(AfterSalesToYxbEntity row);


    AfterSalesToYxbEntity selectByPrimaryKey(Integer afterSalesToYxbId);


    List<AfterSalesToYxbEntity> selectAll();


    int updateByPrimaryKey(AfterSalesToYxbEntity row);

    /**
     * 根据不同条件查询
     */
    List<AfterSalesToYxbEntity> selectByCondition(AfterSalesToYxbEntity afterSalesToYxbEntity);


    int updateIsDeleteByAfterSalesOrderIdAndInterfaceTypeInt(@Param("afterSalesOrderId") Integer afterSalesOrderId,@Param("interfaceType") Integer interfaceType);

    List<AfterSalesToYxbDto> selectByForDownTask();

    List<AfterSalesToYxbDto> selectForPayPushTask();
}