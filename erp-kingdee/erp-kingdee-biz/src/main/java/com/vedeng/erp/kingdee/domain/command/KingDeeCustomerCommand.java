package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶实际接受参数类
 * @date 2022/8/29 13:59
 */
@Getter
@Setter
public class KingDeeCustomerCommand {


    /**
     * 供应商内码
     */
    private String FCUSTID;
    /**
     * 创建组织
     */
    private KingDeeNumberCommand FCreateOrgId = new KingDeeNumberCommand();
    /**
     * 客户编号
     */
    private String FNumber;

    /**
     * 客户名
     */
    private String FName;

    /**
     * 客户简称
     */
    private String FShortName;
    /**
     * 使用组织
     */
    private KingDeeNumberCommand FUseOrgId = new KingDeeNumberCommand();

    /**
     * 客户类别
     */
    private KingDeeNumberCommand FCustTypeId = new KingDeeNumberCommand();


    /**
     * 通讯地址
     */
    private String fAddress;

    /**
     * 开票信息公司抬头
     */
    private String fInvoiceTitle;

    /**
     * 纳税登记号
     */
    private String fTaxRegisterCode;

    /**
     * 开户银行
     */
    private String fInvoiceBankName;

    /**
     * 开票联系电话
     */
    private String fInvoiceTel;

    /**
     * 银行账号
     */
    private String fInvoiceBankAccount;

    /**
     * 开票通讯地址
     */
    private String fInvoiceAddress;

    /**
     * 客户分组
     */
    private String FGroup;

    /**
     * 结算币别
     */
    private KingDeeNumberCommand fTradingCurrid = new KingDeeNumberCommand();

    /**
     * 结算方式
     */
    private KingDeeNumberCommand fSettleTypeId = new KingDeeNumberCommand();

    /**
     * 贸易条款
     */
    private String F_QZOK_TradeTerms;

    /**
     * 销售部门
     */
    private KingDeeNumberCommand fSaldeptId = new KingDeeNumberCommand();

    /**
     * 默认税率
     */
    private KingDeeNumberCommand fTaxRate = new KingDeeNumberCommand();

    /** 2022-11-10 新增 ---start
    /**
     * 所属医院共体
     */
    private String F_QZOK_SYYYGT;
    /**
     * 所属集团
     */
    private String F_QZOK_SSJT;
    /**
     * 现用名
     */
    private String F_QZOK_XYM;
    /**
     * 客户性质
     */
    private String F_QZOK_KHXZTEXT;
    /**
     * 终端客户分类
     */
    private String F_QZOK_ZDKHFLTEXT;
    /**
     * 医疗机构分类
     */
    private String F_QZOK_YLJGFLTEXT;
    /**
     * 客户细分类
     */
    private String F_QZOK_KHXFLTEXT;
    /**
     * 医院等级
     */
    private String F_QZOK_YYDJTEXT;
    /**
     * 客户等级文本
     */
    private String F_QZOK_KHDJ;

    /**
     * 客户所属省份
     */
    private KingDeeNumberCommand FPROVINCE = new KingDeeNumberCommand();

    /**
     * 客户银行账户信息
     */
    private List<KingDeeCustomerDetailCommand> FT_BD_CUSTBANK = new ArrayList<>();

    @Getter
    @Setter
    public static class KingDeeCustomerDetailCommand{
        /**
         * 银行帐号
         */
        private String FBANKCODE;
        /**
         * 帐户名称
         */
        private String FACCOUNTNAME;
        /**
         * 开户行
         */
        private String FOPENBANKNAME;
        /**
         * 联行号
         */
        private String FCNAPS;
    }

   //2022-11-10 新增 ---end
}
