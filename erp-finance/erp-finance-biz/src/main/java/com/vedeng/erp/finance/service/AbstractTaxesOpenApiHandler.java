package com.vedeng.erp.finance.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.finance.enums.TaxesInterfaceCodeEnum;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.infrastructure.taxes.domain.TaxesOpenApiResult;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import com.vedeng.infrastructure.taxes.sevice.ITaxesBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 二级抽象 （针对销项票）
 */
@Slf4j
public abstract class AbstractTaxesOpenApiHandler extends AbstractTaxesHandler{

    @Autowired
    ITaxesBaseService iTaxesBaseService;

    /**
     * 入口
     * @param taxesParam
     * @param taxesInterfaceCodeEnum
     * @return
     */
    public ITaxesResult openapi(ITaxesParam taxesParam, TaxesInterfaceCodeEnum taxesInterfaceCodeEnum){
        // 前置处理
        beforeExecute(taxesParam);
        // 执行
        ITaxesResult executeResult = execute(taxesParam,taxesInterfaceCodeEnum);
        // 后置处理
        ITaxesResult iTaxesOrgResult = afterExecute(taxesParam,executeResult);
        return iTaxesOrgResult;
    }

    /**
     * 前置处理
     * @param taxesParam
     */
    public void beforeExecute(ITaxesParam taxesParam){
        log.info("前置处理 {} 算签前原入参：{}", this.getClass().getSimpleName(),JSONObject.toJSONString(taxesParam));
    }

    /**
     * 执行
     * @param taxesParam
     * @param taxesInterfaceCodeEnum
     * @return
     */
    public ITaxesResult execute(ITaxesParam taxesParam,TaxesInterfaceCodeEnum taxesInterfaceCodeEnum){
        TaxesOpenApiResult resultStr = (TaxesOpenApiResult)iTaxesBaseService.openapi(taxesParam, taxesInterfaceCodeEnum.getInterfaceCode());
        return resultStr;
    }

    /**
     * 后置处理
     * @param taxesParam
     * @param executeResult
     * @return
     */
    protected abstract ITaxesResult afterExecute(ITaxesParam taxesParam, ITaxesResult executeResult);

    /**
     * 获取拼接信息
     * @param return_info
     * @param message
     * @return
     */
    public String getMessage(TaxesOpenApiResult.ReturnInfo return_info,String message){
        StringBuilder sb = new StringBuilder();
        sb.append(StrUtil.isNotBlank(return_info.getReturn_message()) ? return_info.getReturn_message() : TaxesReturnCodeEnum.getMsg(return_info.getReturn_code()));
        if (StrUtil.isNotBlank(message)){
            sb.append("</br>").append(message);
        }
        return sb.toString();
    }
}
