package com.vedeng.erp.aftersale.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.aftersale.common.constant.ExpenseAfterSalesConstant;
import com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesEntity;
import com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesInvoiceEntity;
import com.vedeng.erp.aftersale.dto.*;
import com.vedeng.erp.aftersale.mapper.ExpenseAfterSalesInvoiceMapper;
import com.vedeng.erp.aftersale.mapper.ExpenseAfterSalesMapper;
import com.vedeng.erp.aftersale.mapper.RExpenseAfterSalesJSaleorderMapper;
import com.vedeng.erp.aftersale.service.AfterSaleOrder2ExpenseApiService;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesAutoApiService;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesService;
import com.vedeng.erp.buyorder.dto.*;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.buyorder.service.NewAfterBuyorderService;
import com.vedeng.erp.buyorderexpense.common.constant.BuyorderExpenseConstant;
import com.vedeng.erp.buyorderexpense.domain.entity.RBuyorderExpenseJSaleorderEntity;
import com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseMapper;
import com.vedeng.erp.buyorderexpense.mapper.RBuyorderExpenseJSaleorderMapper;
import com.vedeng.erp.buyorderexpense.service.BuyorderExpenseService;
import com.vedeng.erp.finance.dto.InvoiceReversalDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 采购费用售后 销售线采销联动 转单 以及 一键成单
 * @date 2023/1/5 10:39
 **/
@Service
@Slf4j
public class ExpenseAfterSalesAutoApiServiceImpl implements ExpenseAfterSalesAutoApiService {

    @Autowired
    private BuyorderExpenseMapper buyorderExpenseMapper;

    @Autowired
    private ExpenseAfterSalesMapper expenseAfterSalesMapper;

    @Autowired
    private ExpenseAfterSalesService expenseAfterSalesService;

    @Autowired
    private ExpenseAfterSalesInvoiceMapper expenseAfterSalesInvoiceMapper;

    @Autowired
    private AfterSaleOrder2ExpenseApiService afterSaleOrder2ExpenseApiService;

    @Autowired
    private RBuyorderExpenseJSaleorderMapper rBuyorderExpenseJSaleorderMapper;

    @Autowired
    private BuyorderExpenseService buyorderExpenseService;

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired
    private RExpenseAfterSalesJSaleorderMapper rExpenseAfterSalesJSaleorderMapper;

    @Autowired
    private NewAfterBuyorderService newAfterBuyorderService;


    @Override
    public List<BuyorderExpenseDto> autoExpenseAfterSales(Integer afterSaleOrderId,List<Integer> warnIds) {

        List<ExpenseAfterSalesDto> expenseAfterSalesDtos = bindingAndAllocationAutoExpenseAfterSales(afterSaleOrderId,warnIds);

        if (CollUtil.isNotEmpty(expenseAfterSalesDtos)) {

            ExpenseAfterSalesAutoApiServiceImpl bean = SpringUtil.getBean(ExpenseAfterSalesAutoApiServiceImpl.class);
            List<Long> longs = bean.autoAddExpenseAfterSales(expenseAfterSalesDtos);
            if (CollUtil.isNotEmpty(longs)) {

                List<BuyorderExpenseDto> buyorderExpenseDtos = buyorderExpenseMapper.selectByExpenseAfterIds(longs);
                log.info("查询到的原始费用单信息：{}", JSON.toJSONString(buyorderExpenseDtos));
                return buyorderExpenseDtos;

            }
        }
        return Collections.emptyList();
    }


    @Override
    public List<ExpenseAfterSalesDto> bindingAndAllocationAutoExpenseAfterSales(Integer afterSaleOrderId,List<Integer> warnIds) {

        log.info("bindingAndAllocationAutoExpenseAfterSales 入参:{},{}", JSON.toJSONString(afterSaleOrderId),JSON.toJSONString(warnIds));

        if (Objects.isNull(afterSaleOrderId)) {
            throw new ServiceException("销售售后单id不可为空");
        }

        // 查询销售的售后单
        AfterSaleOrder2ExpenseDto afterSaleOrderExpense = afterSaleOrder2ExpenseApiService.getAfterSaleOrderExpense(afterSaleOrderId);
        List<AfterSaleGoods2ExpenseDto> afterSaleGoods2ExpenseDtos = afterSaleOrderExpense.getAfterSaleGoods2ExpenseDtos();
        if (CollUtil.isEmpty(afterSaleGoods2ExpenseDtos)) {
            log.error("bindingAndAllocationAutoExpenseAfterSales 此销售售后单中未查到虚拟商品信息");
            return Collections.emptyList();
        }

        // 查询原始的被采购的
        List<Integer> orderDetailIds = afterSaleGoods2ExpenseDtos.stream().map(AfterSaleGoods2ExpenseDto::getOrderDetailId).collect(Collectors.toList());
        Map<Integer, Integer> totalNeedReturnNum = afterSaleGoods2ExpenseDtos.stream().collect(Collectors.toMap(AfterSaleGoods2ExpenseDto::getOrderDetailId, AfterSaleGoods2ExpenseDto::getNum, (k1, k2) -> k1));
        List<RBuyorderExpenseJSaleorderEntity> rBuyorderExpenseJSaleorderEntities = rBuyorderExpenseJSaleorderMapper.selectBySaleorderGoodsIdIn(orderDetailIds);
        if (CollUtil.isEmpty(rBuyorderExpenseJSaleorderEntities)) {
            return Collections.emptyList();
        }
        List<RBuyorderExpenseJSaleorderEntity> res = rBuyorderExpenseJSaleorderEntities.stream().filter(x -> !warnIds.contains(x.getBuyorderExpenseId())).collect(Collectors.toList());
        Map<Integer, List<RBuyorderExpenseJSaleorderEntity>> rBuyorderExpenseJSaleorderEntities2Map =
                res
                        .stream()
                        .collect(Collectors.groupingBy(RBuyorderExpenseJSaleorderEntity::getBuyorderExpenseId, TreeMap::new, Collectors.toList()))
                        .descendingMap();

        List<ExpenseAfterSalesDto> result = new ArrayList<>();
        // 查询费用单数据
        rBuyorderExpenseJSaleorderEntities2Map.forEach((k, v) -> {

            boolean checkFlag = buyorderExpenseService.checkExpenseCanAfterSales(k);
            if (!checkFlag) {
                log.error("当前费用单不满足售后条件：{}", JSON.toJSONString(k));
//                throw new ServiceException("当前费用单不满足售后条件");
                return;
            }
            if (CollUtil.isEmpty(v)) {
                log.error("当前费用单未查到和销售单的关联关系");
            }
            if (!checkExpenseOnlyOneSaleOrder(k)) {
                return;
            }
            BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseService.viewDetail(k);
            List<BuyorderExpenseItemDto> buyorderExpenseItemDtos = buyorderExpenseDto.getBuyorderExpenseItemDtos();
            if (CollUtil.isEmpty(buyorderExpenseItemDtos)) {
                log.error("原费用单商品数据缺失:{}", JSON.toJSONString(buyorderExpenseDto));
//                throw new ServiceException("原费用单商品数据缺失");
                return;
            }
            Map<Integer, BuyorderExpenseItemDto> buyorderExpenseItem2Map = buyorderExpenseItemDtos.stream().collect(Collectors.toMap(BuyorderExpenseItemDto::getBuyorderExpenseItemId, e -> e, (kx1, kx2) -> kx2));
            // 正向的绑定的关系
            Map<Integer, List<RBuyorderExpenseJSaleorderEntity>> collect = v.stream().collect(Collectors.groupingBy(RBuyorderExpenseJSaleorderEntity::getBuyorderExpenseItemId));
            // 售后已绑定的数量
            Map<Integer, Integer> returnNumWithSalesOrder2Map = buyorderExpenseApiService.calcReturnNumWithSalesOrder(k, afterSaleOrderExpense.getOrderId());
            List<ExpenseAfterSalesAndRelation> haveRelation = new ArrayList<>();
            // 实际可以售后数据
            collect.forEach((buyorderExpenseItemId, value) -> {

                log.info("当前售后单：{}，费用单：{}，开始分摊：{}",JSON.toJSONString(afterSaleOrderId),JSON.toJSONString(k),JSON.toJSONString(value));
                Integer num = returnNumWithSalesOrder2Map.get(buyorderExpenseItemId);
                int sum = value.stream().mapToInt(RBuyorderExpenseJSaleorderEntity::getNum).sum();
                if (Objects.isNull(num)) {
                    num = 0;
                }
                int availableNum = sum - num;
                log.info("当前售后单：{}，费用单：{}，开始分摊可用数量：{}",JSON.toJSONString(afterSaleOrderId),JSON.toJSONString(k),JSON.toJSONString(availableNum));
                if (availableNum > 0) {
                    ExpenseAfterSalesAndRelation build = ExpenseAfterSalesAndRelation.builder()
                            .availableNum(availableNum)
                            .buyorderExpenseItemId(buyorderExpenseItemId)
                            .buyorderExpenseId(value.get(0).getBuyorderExpenseId())
                            .skuId(value.get(0).getSkuId())
                            .skuNo(value.get(0).getSkuNo())
                            .build();
                    haveRelation.add(build);
                }

            });
            List<ExpenseAfterSalesItemDto> expenseAfterSalesItemDtos = new ArrayList<>();
            ExpenseAfterSalesDto expenseAfterSalesDto = new ExpenseAfterSalesDto();
            // 退货
            expenseAfterSalesDto.setExpenseAfterSalesType(ExpenseAfterSalesConstant.EXPENSE_RETURN_GOODS);
            // 售后 原因 销售联动退货
            expenseAfterSalesDto.setExpenseAfterSalesReason(ExpenseAfterSalesConstant.EXPENSE_AFTER_SALES_REASON_SALES);
            expenseAfterSalesDto.setTraderContactId(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderContactId());
            expenseAfterSalesDto.setTraderId(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderId());
            expenseAfterSalesDto.setRefundMethod(1);
            expenseAfterSalesDto.setExpenseAfterSalesItemDtoList(expenseAfterSalesItemDtos);
            expenseAfterSalesDto.setBuyorderExpenseId(k);
            expenseAfterSalesDto.setCreator(buyorderExpenseDto.getCreator());
            expenseAfterSalesDto.setCreatorName(buyorderExpenseDto.getCreatorName());
            afterSaleGoods2ExpenseDtos.forEach(c -> {

                Integer needReturn = totalNeedReturnNum.get(c.getOrderDetailId());
                log.info("当前售后单：{}，费用单：{}，剩余分摊可用数量：{}",JSON.toJSONString(afterSaleOrderId),JSON.toJSONString(k),JSON.toJSONString(needReturn));
                if (Objects.isNull(needReturn) || needReturn <= 0) {
                    return;
                }

                Integer goodsId = c.getGoodsId();

                List<ExpenseAfterSalesAndRelation> skuAvailable = haveRelation.stream().filter(a -> a.getSkuId().equals(goodsId)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(skuAvailable)) {
                    ExpenseAfterSalesItemDto expenseAfterSalesItemDto = new ExpenseAfterSalesItemDto();
                    expenseAfterSalesItemDto.setBuyorderExpenseItemId(skuAvailable.get(0).getBuyorderExpenseItemId());

                    BuyorderExpenseItemDto buyorderExpenseItemDto = buyorderExpenseItem2Map.get(expenseAfterSalesItemDto.getBuyorderExpenseItemId());
                    expenseAfterSalesItemDto.setExpenseCategoryName(buyorderExpenseItemDto.getBuyorderExpenseItemDetailDto().getExpenseCategoryName());
                    expenseAfterSalesItemDto.setGoodsName(buyorderExpenseItemDto.getBuyorderExpenseItemDetailDto().getGoodsName());
                    expenseAfterSalesItemDto.setSku(buyorderExpenseItemDto.getBuyorderExpenseItemDetailDto().getSku());
                    expenseAfterSalesItemDto.setPrice(buyorderExpenseItemDto.getBuyorderExpenseItemDetailDto().getPrice());
                    expenseAfterSalesItemDto.setGoodsId(goodsId);
                    int available = skuAvailable.stream().mapToInt(ExpenseAfterSalesAndRelation::getAvailableNum).sum();
                    if (available >= needReturn) {
                        expenseAfterSalesItemDto.setReturnNum(needReturn);
                        totalNeedReturnNum.put(c.getOrderDetailId(), 0);
                    } else {
                        expenseAfterSalesItemDto.setReturnNum(available);
                        totalNeedReturnNum.put(c.getOrderDetailId(), needReturn - available);
                    }
                    expenseAfterSalesItemDtos.add(expenseAfterSalesItemDto);

                    // 存储关系
                    RExpenseAfterSalesJSaleorderDto build = RExpenseAfterSalesJSaleorderDto.builder().afterSalesNum(expenseAfterSalesItemDto.getReturnNum())
                            .saleorderGoodsId(c.getOrderDetailId())
                            .saleorderId(afterSaleOrderExpense.getOrderId())
                            .skuId(c.getGoodsId())
                            .skuNo(expenseAfterSalesItemDto.getSku())
                            .build();
                    expenseAfterSalesItemDto.setRExpenseAfterSalesJSaleorderDtos(CollUtil.newArrayList(build));

                }
            });

            // 判断数据
            if (CollUtil.isNotEmpty(expenseAfterSalesItemDtos)) {
                // 兼容销售线 多个虚拟商品做赠品
                List<ExpenseAfterSalesItemDto> merge = new ArrayList<>();
                Map<Integer, List<ExpenseAfterSalesItemDto>> listMap = expenseAfterSalesItemDtos.stream().collect(Collectors.groupingBy(ExpenseAfterSalesItemDto::getBuyorderExpenseItemId));
                listMap.forEach((ex, items) -> {
                    if (CollUtil.isNotEmpty(items)) {
                        if (items.size() == 1) {
                            merge.addAll(items);
                        } else {
                            ExpenseAfterSalesItemDto newEx = new ExpenseAfterSalesItemDto();
                            BeanUtil.copyProperties(items.get(0), newEx);
                            List<RExpenseAfterSalesJSaleorderDto> list = new ArrayList<>();
                            newEx.setRExpenseAfterSalesJSaleorderDtos(list);
                            int sum = items.stream().mapToInt(ExpenseAfterSalesItemDto::getReturnNum).sum();
                            newEx.setReturnNum(sum);
                            items.forEach(c -> {
                                if (CollUtil.isNotEmpty(c.getRExpenseAfterSalesJSaleorderDtos())) {
                                    list.addAll(c.getRExpenseAfterSalesJSaleorderDtos());
                                }
                            });
                            merge.add(newEx);
                        }
                    }

                });
                expenseAfterSalesDto.setExpenseAfterSalesItemDtoList(merge);
                // 组装订单明细说明

                List<RExpenseAfterSalesJSaleorderDto> allRelation = merge.stream()
                        .map(ExpenseAfterSalesItemDto::getRExpenseAfterSalesJSaleorderDtos)
                        .filter(CollUtil::isNotEmpty)
                        .flatMap(List::stream)
                        .collect(Collectors.toList());

                // 售后总额
                BigDecimal totalAmount = expenseAfterSalesItemDtos.stream().map(x -> x.getPrice().multiply(BigDecimal.valueOf(x.getReturnNum()))).reduce(BigDecimal.ZERO,BigDecimal::add);
                expenseAfterSalesDto.setTotalAmount(totalAmount);
                result.add(expenseAfterSalesDto);

                Map<Integer, List<RExpenseAfterSalesJSaleorderDto>> stringListMap = allRelation.stream().collect(Collectors.groupingBy(RExpenseAfterSalesJSaleorderDto::getSkuId));
                List<OrderRemarkDto> orderDesc = new ArrayList<>();
                expenseAfterSalesDto.setOrderDesc(orderDesc);
                OrderRemarkDto orderRemarkDto = new OrderRemarkDto();
                orderDesc.add(orderRemarkDto);
                OriginalOrderDto originalOrderDto = new OriginalOrderDto();
                orderRemarkDto.setType(1);
                orderRemarkDto.setOriginalOrderDto(originalOrderDto);
                originalOrderDto.setOrderId(afterSaleOrderExpense.getOrderId());
                originalOrderDto.setNo(afterSaleOrderExpense.getOrderNo());
                ResultOrderDto resultOrderDto = new ResultOrderDto();
                resultOrderDto.setOrderId(buyorderExpenseDto.getBuyorderExpenseId());
                resultOrderDto.setNo(buyorderExpenseDto.getBuyorderExpenseNo());
                orderRemarkDto.setResultOrderDto(resultOrderDto);
                List<OriginalOrderGoodsDto> list = new ArrayList<>();
                originalOrderDto.setGoodsDtos(list);
                stringListMap.forEach((skuId, r) -> {
                    OriginalOrderGoodsDto originalOrderGoodsDto = new OriginalOrderGoodsDto();
                    originalOrderGoodsDto.setSku(r.get(0).getSkuNo());
                    originalOrderGoodsDto.setGoodsId(skuId);
                    list.add(originalOrderGoodsDto);
                });
            }

        });

        log.info("当前售后单：{}，自动售后单：{}",JSON.toJSONString(afterSaleOrderId),JSON.toJSONString(result));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public List<Long> autoAddExpenseAfterSales(List<ExpenseAfterSalesDto> expenseAfterSalesDtos) {

        // 调用的
        List<Long> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(expenseAfterSalesDtos)) {
            expenseAfterSalesDtos.forEach(c -> {
                Long expenseAfterSalesId = expenseAfterSalesService.saveAddExpenseAfterSale(c);
                result.add(expenseAfterSalesId);
            });
        }
        return result;
    }

    @Override
    public List<ExpenseAfterSalesDto> bindingAutoExpenseAfterSales(Integer afterSalesId,List<Integer> warnIds) {

        log.info("费用单自动转单 入参：{},{}", JSON.toJSONString(afterSalesId),JSON.toJSONString(warnIds));

        // 查询销售的售后单
        AfterSaleOrder2ExpenseDto afterSaleOrderExpense = afterSaleOrder2ExpenseApiService.getAfterSaleOrderExpense(afterSalesId);
        List<AfterSaleGoods2ExpenseDto> afterSaleGoods2ExpenseDtos = afterSaleOrderExpense.getAfterSaleGoods2ExpenseDtos();
        if (CollUtil.isEmpty(afterSaleGoods2ExpenseDtos)) {
            log.error("bindingAndAllocationAutoExpenseAfterSales 此销售售后单中未查到虚拟商品信息");
            return Collections.emptyList();
        }

        // 查询原始的被采购的
        List<Integer> orderDetailIds = afterSaleGoods2ExpenseDtos.stream().map(AfterSaleGoods2ExpenseDto::getOrderDetailId).collect(Collectors.toList());
        List<RBuyorderExpenseJSaleorderEntity> rBuyorderExpenseJSaleorderEntities = rBuyorderExpenseJSaleorderMapper.selectBySaleorderGoodsIdIn(orderDetailIds);

        if (CollUtil.isEmpty(rBuyorderExpenseJSaleorderEntities)) {
            return Collections.emptyList();
        }
        List<RBuyorderExpenseJSaleorderEntity> res = rBuyorderExpenseJSaleorderEntities.stream().filter(x -> !warnIds.contains(x.getBuyorderExpenseId())).collect(Collectors.toList());
        Map<Integer, List<RBuyorderExpenseJSaleorderEntity>> rBuyorderExpenseJSaleorderEntities2Map =
                        res
                        .stream()
                        .collect(Collectors.groupingBy(RBuyorderExpenseJSaleorderEntity::getBuyorderExpenseId, TreeMap::new, Collectors.toList()))
                        .descendingMap();

        List<ExpenseAfterSalesDto> result = new ArrayList<>();
        // 查询费用单数据
        rBuyorderExpenseJSaleorderEntities2Map.forEach((k, v) -> {
            boolean checkFlag = buyorderExpenseService.checkExpenseCanAfterSales(k);
            if (!checkFlag) {
                log.error("当前费用单不满足售后条件：{}", JSON.toJSONString(k));
                return;
            }
            if (CollUtil.isEmpty(v)) {
                log.error("当前费用单未查到和销售单的关联关系");
            }

            if (!checkExpenseOnlyOneSaleOrder(k)) {
                return;
            }
            BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseService.viewDetail(k);
            if (!buyorderExpenseDto.getPaymentStatus().equals(BuyorderExpenseConstant.PAYMENT_STATUS_ALL)) {
                log.error("当前费用单：{}付款状态不满足自动转单条件", JSON.toJSONString(k));
                throw new ServiceException("当前费用单付款状态不满足自动转单条件");
            }
            List<BuyorderExpenseItemDto> buyorderExpenseItemDtos = buyorderExpenseDto.getBuyorderExpenseItemDtos();
            if (CollUtil.isEmpty(buyorderExpenseItemDtos)) {
                log.error("原费用单商品数据缺失:{}", JSON.toJSONString(buyorderExpenseDto));
                return;
            }

            List<BuyorderExpenseItemDto> collect = buyorderExpenseItemDtos.stream().filter(a -> a.getNum() - a.getReturnNum() > 0).collect(Collectors.toList());
            if (CollUtil.isEmpty(collect)) {
                log.info("当前费用单：{}下所有商品已经售后", JSON.toJSONString(k));
                return;
            }
            List<RBuyorderExpenseJSaleorderDto> saleorderByBuyorderExpenseId = rBuyorderExpenseJSaleorderMapper.findByBuyorderExpenseId(k);
            List<RExpenseAfterSalesJSaleorderDto> list = rExpenseAfterSalesJSaleorderMapper.selectByBuyorderExpenseId(k);

            List<ExpenseAfterSalesItemDto> expenseAfterSalesItemDtos = new ArrayList<>();
            ExpenseAfterSalesDto expenseAfterSalesDto = new ExpenseAfterSalesDto();
            // 退货
            expenseAfterSalesDto.setExpenseAfterSalesType(ExpenseAfterSalesConstant.EXPENSE_RETURN_GOODS);
            // 售后 原因 销售联动退货
            expenseAfterSalesDto.setExpenseAfterSalesReason(ExpenseAfterSalesConstant.EXPENSE_AFTER_SALES_REASON_SALES);
            expenseAfterSalesDto.setTraderContactId(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderContactId());
            expenseAfterSalesDto.setTraderId(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderId());
            expenseAfterSalesDto.setRefundMethod(2);
            expenseAfterSalesDto.setExpenseAfterSalesItemDtoList(expenseAfterSalesItemDtos);
            expenseAfterSalesDto.setBuyorderExpenseId(k);
            expenseAfterSalesDto.setIsAuto(1);
            expenseAfterSalesDto.setCreator(buyorderExpenseDto.getCreator());
            expenseAfterSalesDto.setCreatorName(buyorderExpenseDto.getCreatorName());
            expenseAfterSalesDto.setAddTime(new Date());
            List<OrderRemarkDto> orderDesc = new ArrayList<>();
            expenseAfterSalesDto.setOrderDesc(orderDesc);

            collect.forEach(c -> {
                // 剩余的可售后数量
                c.setNum(c.getNum() - c.getReturnNum());
                List<RBuyorderExpenseJSaleorderDto> buyorderExpenseJSaleorderDtos = saleorderByBuyorderExpenseId.stream()
                        .filter(a -> a.getBuyorderExpenseItemId().equals(c.getBuyorderExpenseItemId())).collect(Collectors.toList());

                if (c.getNum() <= 0) {
                    return;
                }
                ExpenseAfterSalesItemDto expenseAfterSalesItemDto = new ExpenseAfterSalesItemDto();
                expenseAfterSalesItemDto.setBuyorderExpenseItemId(c.getBuyorderExpenseItemId());

                expenseAfterSalesItemDto.setExpenseCategoryName(c.getBuyorderExpenseItemDetailDto().getExpenseCategoryName());
                expenseAfterSalesItemDto.setGoodsName(c.getBuyorderExpenseItemDetailDto().getGoodsName());
                expenseAfterSalesItemDto.setSku(c.getBuyorderExpenseItemDetailDto().getSku());
                expenseAfterSalesItemDto.setPrice(c.getBuyorderExpenseItemDetailDto().getPrice());
                expenseAfterSalesItemDto.setGoodsId(c.getGoodsId());
                expenseAfterSalesItemDto.setReturnNum(c.getNum());
                expenseAfterSalesItemDtos.add(expenseAfterSalesItemDto);
                if (CollUtil.isNotEmpty(buyorderExpenseJSaleorderDtos)) {
                    buyorderExpenseJSaleorderDtos.forEach(b -> {
                        int sum = list.stream().filter(d -> d.getSaleorderGoodsId().equals(b.getSaleorderGoodsId()))
                                .mapToInt(RExpenseAfterSalesJSaleorderDto::getAfterSalesNum).sum();
                        b.setNum(b.getNum() - sum);
                    });


                    List<RExpenseAfterSalesJSaleorderDto> relation = new ArrayList<>();

                    // 仍有数量的 正向的数量
                    Map<Integer, List<RBuyorderExpenseJSaleorderDto>> listMap = buyorderExpenseJSaleorderDtos.stream()
                            .filter(dto -> dto.getNum() > 0)
                            .collect(Collectors.groupingBy(RBuyorderExpenseJSaleorderDto::getSaleorderGoodsId));

                    // 关系表
                    listMap.forEach((k1, v1) -> {
                        RExpenseAfterSalesJSaleorderDto build = RExpenseAfterSalesJSaleorderDto.builder()
                                .saleorderId(v1.get(0).getSaleorderId())
                                .saleorderGoodsId(k1)
                                .skuNo(v1.get(0).getSkuNo())
                                .skuId(v1.get(0).getSkuId())
                                .buyorderExpenseId(k)
                                .buyorderExpenseItemId(c.getBuyorderExpenseItemId())
                                .afterSalesNum(v1.stream().mapToInt(RBuyorderExpenseJSaleorderDto::getNum).sum())
                                .build();
                        relation.add(build);
                    });
                    if (CollUtil.isNotEmpty(relation)) {
                        expenseAfterSalesItemDto.setRExpenseAfterSalesJSaleorderDtos(relation);
                    }
                }

            });

            // 订单说明 到 自动转单的逻辑里去处理
            Map<Integer, List<RExpenseAfterSalesJSaleorderDto>> stringListMap = expenseAfterSalesItemDtos.stream()
                    .map(ExpenseAfterSalesItemDto::getRExpenseAfterSalesJSaleorderDtos)
                    .filter(CollUtil::isNotEmpty)
                    .flatMap(List::stream).collect(Collectors.groupingBy(RExpenseAfterSalesJSaleorderDto::getSkuId));
            // afterSalesSkus 当前售后单中的 sku
            List<String> afterSalesSkus = v.stream().filter(xx -> Objects.nonNull(xx.getSkuNo())).map(RBuyorderExpenseJSaleorderEntity::getSkuNo).distinct().collect(Collectors.toList());
            OrderRemarkDto orderRemarkDto = new OrderRemarkDto();
            orderDesc.add(orderRemarkDto);
            OriginalOrderDto originalOrderDto = new OriginalOrderDto();
            orderRemarkDto.setType(2);
            orderRemarkDto.setOriginalOrderDto(originalOrderDto);
            originalOrderDto.setOrderId(afterSaleOrderExpense.getOrderId());
            originalOrderDto.setNo(afterSaleOrderExpense.getOrderNo());
            ResultOrderDto resultOrderDto = new ResultOrderDto();
            resultOrderDto.setOrderId(buyorderExpenseDto.getBuyorderExpenseId());
            resultOrderDto.setNo(buyorderExpenseDto.getBuyorderExpenseNo());
            orderRemarkDto.setResultOrderDto(resultOrderDto);
            List<OriginalOrderGoodsDto> lists = new ArrayList<>();
            originalOrderDto.setGoodsDtos(lists);
            stringListMap.forEach((skuId, r) -> {
                String skuNo = r.get(0).getSkuNo();
                if (!afterSalesSkus.contains(skuNo)) {
                    return;
                }
                OriginalOrderGoodsDto originalOrderGoodsDto = new OriginalOrderGoodsDto();
                originalOrderGoodsDto.setSku(skuNo);
                originalOrderGoodsDto.setGoodsId(skuId);
                lists.add(originalOrderGoodsDto);
            });
            // 售后总额
            BigDecimal totalAmount = expenseAfterSalesItemDtos.stream().map(x -> x.getPrice().multiply(BigDecimal.valueOf(x.getReturnNum()))).reduce(BigDecimal.ZERO,BigDecimal::add);
            expenseAfterSalesDto.setTotalAmount(totalAmount);
            result.add(expenseAfterSalesDto);

        });
        return result;
    }

    /**
     * 校验是否唯一
     * @param buyorderExpenseId
     * @return
     */
    private boolean checkExpenseOnlyOneSaleOrder(Integer buyorderExpenseId) {

        if (Objects.isNull(buyorderExpenseId)) {
            return Boolean.FALSE;
        }

        List<RBuyorderExpenseJSaleorderDto> byBuyorderExpenseId = rBuyorderExpenseJSaleorderMapper.findByBuyorderExpenseId(buyorderExpenseId);
        List<Integer> collect = byBuyorderExpenseId.stream().map(RBuyorderExpenseJSaleorderDto::getSaleorderId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(collect)) {
            log.error("checkExpenseOnlyOneSaleOrder 费用单没有正向关联关系");
            return Boolean.FALSE;
        }
        // 有且只有一个
        if (CollUtil.isNotEmpty(collect) && collect.size() > 1) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void autoExecuteRefundOperation(List<Long> afterSalesIds) {
        if (CollUtil.isEmpty(afterSalesIds)) {
            return;
        }
        afterSalesIds.forEach(c->{
            ExpenseAfterSalesEntity expenseAfterSalesEntity = expenseAfterSalesMapper.selectByPrimaryKey(c);
            ExecuteRefundDto build = ExecuteRefundDto.builder()
                    .expenseAfterSalesId(expenseAfterSalesEntity.getExpenseAfterSalesId())
                    .buyorderExpenseId(expenseAfterSalesEntity.getBuyorderExpenseId())
                    .totalAmount(expenseAfterSalesEntity.getTotalAmount())
                    .build();
            expenseAfterSalesService.executeRefundOperation(build);
        });
    }

    @Override
    @Transactional
    public void autoReversalInvoices(List<Long> afterSalesIds) {

        if (CollUtil.isEmpty(afterSalesIds)) {
            return;
        }

        afterSalesIds.forEach(c->{
            List<ExpenseAfterSalesInvoiceEntity> byExpenseAfterSalesId = expenseAfterSalesInvoiceMapper.findByExpenseAfterSalesId(c);
            log.info("自动转单费用售后单：{},需要售后的票数据：{}", JSON.toJSONString(c), JSON.toJSONString(byExpenseAfterSalesId));
            List<ExpenseAfterSalesInvoiceEntity> collect = byExpenseAfterSalesId.stream().filter(x -> x.getIsRefundInvoice().equals(1) && x.getReturnInvoiceStatus().equals(0)).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                Map<String, List<ExpenseAfterSalesInvoiceEntity>> collect1 = collect.stream().collect(Collectors.groupingBy(r -> r.getExpenseAfterSalesId() + StrUtil.DASHED + r.getInvoiceCode() + StrUtil.DASHED + r.getInvoiceNo()));
                collect1.forEach((k,v)->{
                    ExpenseAfterSalesInvoiceEntity r = v.get(0);
                    InvoiceReversalDto invoiceReversalDto = expenseAfterSalesService.reversalInvoices(r.getExpenseAfterSalesId(), r.getInvoiceCode(), r.getInvoiceNo());
                    log.info("自动转单费用售后单：{},创建的冲销数据：{}", JSON.toJSONString(c), JSON.toJSONString(invoiceReversalDto));
                    newAfterBuyorderService.verifyCoverSave(invoiceReversalDto.getInvoiceReversalId(),Boolean.TRUE,"自动转单费用单售后审核通过");
                });
            }
        });
    }
}
