<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.goods.dao.SpuMigratedLogMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.SpuMigratedLogDomain" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 30 17:48:00 CST 2020.
    -->
    <id column="SPU_MIGTATION_LOG_ID" property="spuMigtationLogId" jdbcType="INTEGER" />
    <result column="SPU_ID" property="spuId" jdbcType="INTEGER" />
    <result column="SPU_NO" property="spuNo" jdbcType="VARCHAR" />
    <result column="CATEGORY_ID_PRE" property="categoryIdPre" jdbcType="INTEGER" />
    <result column="CATEGORY_NAME_PRE" property="categoryNamePre" jdbcType="VARCHAR" />
    <result column="CATEGORY_ID_AFTER" property="categoryIdAfter" jdbcType="INTEGER" />
    <result column="CATEGORY_NAME_AFTER" property="categoryNameAfter" jdbcType="VARCHAR" />
    <result column="ORIGIN_PATH" property="originPath" jdbcType="VARCHAR" />
    <result column="TARGET_PATH" property="targetPath" jdbcType="VARCHAR" />
    <result column="MIGRATION_REASON" property="migrationReason" jdbcType="VARCHAR" />
    <result column="ADD_ATTRS" property="addAttrs" jdbcType="VARCHAR" />
    <result column="ADD_ATTR_VALUES" property="addAttrValues" jdbcType="VARCHAR" />
    <result column="IS_DELETE" property="deleted" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <resultMap id="SpuRemovalLogDto" type="com.vedeng.goods.model.dto.SpuRemovalLogDto" >
    <id column="SPU_MIGTATION_LOG_ID" property="spuMigtationLogId" jdbcType="INTEGER" />
    <result column="SPU_ID" property="spuId" jdbcType="INTEGER" />
    <result column="SHOW_NAME" property="spuShowName" jdbcType="VARCHAR" />
    <result column="CATEGORY_ID_PRE" property="categoryIdPre" jdbcType="INTEGER" />
    <result column="CATEGORY_NAME_PRE" property="categoryNamePre" jdbcType="VARCHAR" />
    <result column="CATEGORY_ID_AFTER" property="categoryIdAfter" jdbcType="INTEGER" />
    <result column="CATEGORY_NAME_AFTER" property="categoryNameAfter" jdbcType="VARCHAR" />
    <result column="ORIGIN_PATH" property="originPath" jdbcType="VARCHAR" />
    <result column="TARGET_PATH" property="targetPath" jdbcType="VARCHAR" />
    <result column="MIGRATION_REASON" property="reason" jdbcType="VARCHAR" />
    <result column="ADD_ATTRS" property="addAttrs" jdbcType="VARCHAR" />
    <result column="ADD_ATTR_VALUES" property="addAttrValues" jdbcType="VARCHAR" />
    <result column="UPDATE_TIME" property="lastUpdateTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="lastOperatorId" jdbcType="INTEGER" />
    <result column="USERNAME" property="lastOperatorName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 30 17:48:00 CST 2020.
    -->
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 30 17:48:00 CST 2020.
    -->
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 30 17:48:00 CST 2020.
    -->
    SPU_MIGTATION_LOG_ID, SPU_ID, SPU_NO, CATEGORY_ID_PRE, CATEGORY_NAME_PRE, CATEGORY_ID_AFTER, 
    CATEGORY_NAME_AFTER, ORIGIN_PATH, TARGET_PATH, MIGRATION_REASON, ADD_ATTRS,ADD_ATTR_VALUES, ADD_TIME,
    CREATOR,IS_DELETE,UPDATER,UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 30 17:48:00 CST 2020.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_SPU_MIGRATION_LOG
    where SPU_MIGTATION_LOG_ID = #{spuMigtationLogId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 30 17:48:00 CST 2020.
    -->
    delete from T_SPU_MIGRATION_LOG
    where SPU_MIGTATION_LOG_ID = #{spuMigtationLogId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.SpuMigratedLogDomain" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 30 17:48:00 CST 2020.
    -->
    <selectKey resultType="java.lang.Integer" keyProperty="spuMigtationLogId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_SPU_MIGRATION_LOG (SPU_ID, SPU_NO, CATEGORY_ID_PRE, 
      CATEGORY_NAME_PRE, CATEGORY_ID_AFTER, CATEGORY_NAME_AFTER, 
      ORIGIN_PATH, TARGET_PATH, MIGRATION_REASON, 
      ADD_ATTRS, ADD_TIME, CREATOR
      )
    values (#{spuId,jdbcType=INTEGER}, #{spuNo,jdbcType=VARCHAR}, #{categoryIdPre,jdbcType=INTEGER}, 
      #{categoryNamePre,jdbcType=VARCHAR}, #{categoryIdAfter,jdbcType=INTEGER}, #{categoryNameAfter,jdbcType=VARCHAR}, 
      #{originPath,jdbcType=VARCHAR}, #{targetPath,jdbcType=VARCHAR}, #{migrationReason,jdbcType=VARCHAR}, 
      #{addAttrs,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.SpuMigratedLogDomain" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 30 17:48:00 CST 2020.
    -->
    <selectKey resultType="java.lang.Integer" keyProperty="spuMigtationLogId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_SPU_MIGRATION_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="spuId != null" >
        SPU_ID,
      </if>
      <if test="spuNo != null" >
        SPU_NO,
      </if>
      <if test="categoryIdPre != null" >
        CATEGORY_ID_PRE,
      </if>
      <if test="categoryNamePre != null" >
        CATEGORY_NAME_PRE,
      </if>
      <if test="categoryIdAfter != null" >
        CATEGORY_ID_AFTER,
      </if>
      <if test="categoryNameAfter != null" >
        CATEGORY_NAME_AFTER,
      </if>
      <if test="originPath != null" >
        ORIGIN_PATH,
      </if>
      <if test="targetPath != null" >
        TARGET_PATH,
      </if>
      <if test="migrationReason != null" >
        MIGRATION_REASON,
      </if>
      <if test="addAttrs != null" >
        ADD_ATTRS,
      </if>
      <if test="addAttrValues != null" >
        ADD_ATTR_VALUES,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="deleted != null" >
        IS_DELETE,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="spuId != null" >
        #{spuId,jdbcType=INTEGER},
      </if>
      <if test="spuNo != null" >
        #{spuNo,jdbcType=VARCHAR},
      </if>
      <if test="categoryIdPre != null" >
        #{categoryIdPre,jdbcType=INTEGER},
      </if>
      <if test="categoryNamePre != null" >
        #{categoryNamePre,jdbcType=VARCHAR},
      </if>
      <if test="categoryIdAfter != null" >
        #{categoryIdAfter,jdbcType=INTEGER},
      </if>
      <if test="categoryNameAfter != null" >
        #{categoryNameAfter,jdbcType=VARCHAR},
      </if>
      <if test="originPath != null" >
        #{originPath,jdbcType=VARCHAR},
      </if>
      <if test="targetPath != null" >
        #{targetPath,jdbcType=VARCHAR},
      </if>
      <if test="migrationReason != null" >
        #{migrationReason,jdbcType=VARCHAR},
      </if>
      <if test="addAttrs != null" >
        #{addAttrs,jdbcType=VARCHAR},
      </if>
      <if test="addAttrValues != null" >
        #{addAttrValues,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="deleted != null" >
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.SpuMigratedLogDomain" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 30 17:48:00 CST 2020.
    -->
    update T_SPU_MIGRATION_LOG
    <set >
      <if test="spuId != null" >
        SPU_ID = #{spuId,jdbcType=INTEGER},
      </if>
      <if test="spuNo != null" >
        SPU_NO = #{spuNo,jdbcType=VARCHAR},
      </if>
      <if test="categoryIdPre != null" >
        CATEGORY_ID_PRE = #{categoryIdPre,jdbcType=INTEGER},
      </if>
      <if test="categoryNamePre != null" >
        CATEGORY_NAME_PRE = #{categoryNamePre,jdbcType=VARCHAR},
      </if>
      <if test="categoryIdAfter != null" >
        CATEGORY_ID_AFTER = #{categoryIdAfter,jdbcType=INTEGER},
      </if>
      <if test="categoryNameAfter != null" >
        CATEGORY_NAME_AFTER = #{categoryNameAfter,jdbcType=VARCHAR},
      </if>
      <if test="originPath != null" >
        ORIGIN_PATH = #{originPath,jdbcType=VARCHAR},
      </if>
      <if test="targetPath != null" >
        TARGET_PATH = #{targetPath,jdbcType=VARCHAR},
      </if>
      <if test="migrationReason != null" >
        MIGRATION_REASON = #{migrationReason,jdbcType=VARCHAR},
      </if>
      <if test="addAttrs != null" >
        ADD_ATTRS = #{addAttrs,jdbcType=VARCHAR},
      </if>
      <if test="addAttrValues != null" >
        ADD_ATTR_VALUES = #{addAttrValues,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
    </set>
    where SPU_MIGTATION_LOG_ID = #{spuMigtationLogId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.SpuMigratedLogDomain" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jul 30 17:48:00 CST 2020.
    -->
    update T_SPU_MIGRATION_LOG
    set SPU_ID = #{spuId,jdbcType=INTEGER},
      SPU_NO = #{spuNo,jdbcType=VARCHAR},
      CATEGORY_ID_PRE = #{categoryIdPre,jdbcType=INTEGER},
      CATEGORY_NAME_PRE = #{categoryNamePre,jdbcType=VARCHAR},
      CATEGORY_ID_AFTER = #{categoryIdAfter,jdbcType=INTEGER},
      CATEGORY_NAME_AFTER = #{categoryNameAfter,jdbcType=VARCHAR},
      ORIGIN_PATH = #{originPath,jdbcType=VARCHAR},
      TARGET_PATH = #{targetPath,jdbcType=VARCHAR},
      MIGRATION_REASON = #{migrationReason,jdbcType=VARCHAR},
      ADD_ATTRS = #{addAttrs,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER}
    where SPU_MIGTATION_LOG_ID = #{spuMigtationLogId,jdbcType=INTEGER}
  </update>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.vedeng.goods.model.SpuMigratedLogDomainExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Aug 03 16:48:32 CST 2020.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_SPU_MIGRATION_LOG
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
    <select id="listSpuMigratedlistpage" parameterType="map" resultMap="SpuRemovalLogDto">
      select
      l.SPU_MIGTATION_LOG_ID, l.SPU_ID, l.SPU_NO, l.CATEGORY_ID_PRE, l.CATEGORY_NAME_PRE, l.CATEGORY_ID_AFTER,
      l.CATEGORY_NAME_AFTER, l.ORIGIN_PATH, l.TARGET_PATH, l.MIGRATION_REASON, l.ADD_ATTRS, l.ADD_TIME,spu.SHOW_NAME, u.USERNAME,
      l.CREATOR,l.IS_DELETE,l.UPDATER,l.UPDATE_TIME
      from T_SPU_MIGRATION_LOG l
      inner join  V_CORE_SPU spu on l.SPU_ID =spu.SPU_ID and spu.status=1
      left join T_USER u on l.UPDATER = u.USER_ID
      <where>
        <if test="goodsRemovalRecordQueryDto.spuId != null" >
          l.SPU_ID like concat('%',#{goodsRemovalRecordQueryDto.spuId, jdbcType=INTEGER},'%')
        </if>
        <if test="goodsRemovalRecordQueryDto.operateBeginTimeMills != null " >
         and  l.UPDATE_TIME <![CDATA[ >= ]]>  #{goodsRemovalRecordQueryDto.operateBeginTimeMills,jdbcType=BIGINT}
        </if>
        <if test="goodsRemovalRecordQueryDto.operateEndTimeMills != null " >
          and l.UPDATE_TIME <![CDATA[ <= ]]> #{goodsRemovalRecordQueryDto.operateEndTimeMills,jdbcType=BIGINT}
        </if>
        <if test="goodsRemovalRecordQueryDto.spuName != null and goodsRemovalRecordQueryDto.spuName != ''" >
          and spu.SPU_NAME like concat('%',#{goodsRemovalRecordQueryDto.spuName, jdbcType=VARCHAR},'%')
        </if>
        <if test="goodsRemovalRecordQueryDto.operatorName != null and goodsRemovalRecordQueryDto.operatorName != ''" >
          and u.USERNAME like concat('%',#{goodsRemovalRecordQueryDto.operatorName, jdbcType=VARCHAR},'%')
        </if>
      </where>
      order by l.ADD_TIME desc
    </select>
  <select id="listSpuRemovalRecordsBySpuIdList" parameterType="list" resultMap="SpuRemovalLogDto">
    select
    l.SPU_MIGTATION_LOG_ID, l.SPU_ID, l.SPU_NO, l.CATEGORY_ID_PRE, l.CATEGORY_NAME_PRE, l.CATEGORY_ID_AFTER,
    l.CATEGORY_NAME_AFTER, l.ORIGIN_PATH, l.TARGET_PATH, l.MIGRATION_REASON, l.ADD_ATTRS,l.ADD_ATTR_VALUES, l.ADD_TIME,spu.SHOW_NAME, u.USERNAME,
    l.CREATOR,l.IS_DELETE,l.UPDATER,l.UPDATE_TIME
    from T_SPU_MIGRATION_LOG l
    inner join  V_CORE_SPU spu on l.SPU_ID =spu.SPU_ID and spu.status=1
    left join T_USER u on l.UPDATER = u.USER_ID
    where
        l.SPU_MIGTATION_LOG_ID in
        <foreach item="logId" collection="list" separator="," open="(" close=")" index="">
          #{logId, jdbcType=INTEGER}
        </foreach>
  </select>
</mapper>