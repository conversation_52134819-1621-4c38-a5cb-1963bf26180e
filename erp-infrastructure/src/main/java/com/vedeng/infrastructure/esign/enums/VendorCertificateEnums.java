package com.vedeng.infrastructure.esign.enums;

/**
 * 厂商资格证书枚举
 *
 * <AUTHOR>
 */
public enum VendorCertificateEnums {

    /**
     * 证书枚举
     */
    REGISTRATION_FORM(1305, "注册登记表"),

    PRODUCTION_LICENSE(1306, "生产企业生产许可证"),

    BUSINESS_LICENSE(1307, "营业执照"),

    REGISTRATION_CERTIFICATE(975, "产品注册证附件"),

    PRODUCTION_RECORD_CERTIFICATE(1308, "生产备案证");


    private final Integer type;

    private final String name;

    VendorCertificateEnums(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

}
