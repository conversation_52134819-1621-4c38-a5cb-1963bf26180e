package com.newtask.ddi;

import com.newtask.ddi.dao.DdiGoodsStockExtMapper;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.order.model.GeSaleOrder;
import com.wms.dao.VWarehouseGoodsOperateLogExtMapper;
import com.wms.model.ddi.VWarehouseGoodsOperateLogExtDto;
import com.wms.model.dto.GoodsStockDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@JobHandler(value = "DdiGoodsStockOrderTask")
public class DdiGoodsStockOrderTask extends AbstractJobHandler {


    private static final Logger LOGGER = LoggerFactory.getLogger(DdiGoodsStockOrderTask.class);

    @Autowired
    private DdiGoodsStockExtMapper ddiGoodsStockExtMapper;

    @Autowired
    private VWarehouseGoodsOperateLogExtMapper vWarehouseGoodsOperateLogExtMapper;

    @Value("${GE_TRADER_SKU}")
    private String geTraderSku;

    public ReturnT<String> doExecute(String param) throws Exception {


        LOGGER.info("DDI库存订单数据采集定时任务开始" + param);
        XxlJobLogger.log("DDI库存订单数据采集定时任务开始执行" + param);


        GeSaleOrder geSaleOrder = JsonUtils.readValue(geTraderSku, GeSaleOrder.class);

        //将sku转换未sku_id
        List<String> skuList = geSaleOrder.getSkuList();

        List<Integer> skuIdList = new ArrayList<>();
        for (String s : skuList) {
            String substring = s.substring(1);
            Integer skuId = Integer.valueOf(substring);
            skuIdList.add(skuId);
        }


        //默认取开始时间位昨天的零点到23：59
        Date yesterdayDate = DateUtil.getPreviousDayByDateTime(new Date());
        Long startTime = DateUtil.getDayStartTime(yesterdayDate).getTime();
        Long endTime = DateUtil.getDayEndTime(yesterdayDate).getTime();

        if(!StringUtils.isBlank(param)){
            String[] params = param.split(",");
            String startTimeStr = params[0];
            String endTimeStr = params[1];

            long startTimeLong = DateUtil.convertLong(startTimeStr, DateUtil.DATE_FORMAT);
            long endTimeLong = DateUtil.convertLong(endTimeStr, DateUtil.DATE_FORMAT);

            startTime = DateUtil.getDayStartTime(new Date(startTimeLong)).getTime();
            endTime = DateUtil.getDayEndTime(new Date(endTimeLong)).getTime();
        }

        //获取当日设计GesKU的入库单
        List<VWarehouseGoodsOperateLogExtDto> VWarehouseGoodsOperateLogExtDtoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(skuIdList)) {

            VWarehouseGoodsOperateLogExtDtoList = vWarehouseGoodsOperateLogExtMapper.getAllTodayOrder(skuIdList, startTime, endTime);
        }

        //遍历获取新入库单
        try {
            if (!CollectionUtils.isEmpty(VWarehouseGoodsOperateLogExtDtoList)) {
                for (VWarehouseGoodsOperateLogExtDto VWarehouseGoodsOperateLogExtDto : VWarehouseGoodsOperateLogExtDtoList) {
                    List<GoodsStockDto> goodsStockDtoList = vWarehouseGoodsOperateLogExtMapper.getSingleOrder(VWarehouseGoodsOperateLogExtDto);

                    if (!CollectionUtils.isEmpty(goodsStockDtoList)) {
                        //转换入库时间格式
                        for (GoodsStockDto goodsStockDto : goodsStockDtoList) {


                            if (null != goodsStockDto.getStorageTimeLong()) {
                                long storageTimeLong = goodsStockDto.getStorageTimeLong();
                                Date storageTime = new Date(storageTimeLong);
                                goodsStockDto.setStorageTime(storageTime);
                            }

                            //库存时长
                            if (null != goodsStockDto.getStorageTimeLong()) {
                                long stockDuration = System.currentTimeMillis() - goodsStockDto.getStorageTimeLong();
                                goodsStockDto.setStockDuration(stockDuration);
                            }

                            //转换库存时间
                            if (null != goodsStockDto.getStockTimeLong()) {
                                Long stockTimeLong = goodsStockDto.getStockTimeLong();
                                Date stockTime = new Date(stockTimeLong);
                                goodsStockDto.setStockTime(stockTime);
                            }

                            //转换过期时间
                            if (null != goodsStockDto.getEffectiveDaysLong()) {
                                Long effectiveDaysLong = goodsStockDto.getEffectiveDaysLong();
                                Date effectiveDays = new Date(effectiveDaysLong);
                                goodsStockDto.setEffectiveDays(effectiveDays);
                            }

                            //逻辑仓处理
                            if (!StringUtils.isBlank(goodsStockDto.getStockStatus())) {
                                String stockStatus = goodsStockDto.getStockStatus();
                                if (stockStatus.endsWith("库")) {
                                    stockStatus = stockStatus.substring(0, stockStatus.length()-1);
                                    goodsStockDto.setStockStatus(stockStatus);
                                }
                            }
                            goodsStockDto.setNum(1);
                            ddiGoodsStockExtMapper.insertSingleOrder(goodsStockDto);
                        }
                    }

                }
            }
        } catch (Exception e) {
            LOGGER.error("DDI库存信息采集失败，错误原因:{}",e);
            return FAIL;
        }


        LOGGER.info("DDI库存订单数据采集定时任务结束" + param);
        XxlJobLogger.log("DDI库存订单数据采集定时任务结束 " + param);
        return SUCCESS;
    }
}
