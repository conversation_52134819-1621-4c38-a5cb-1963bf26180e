function getCascaderRegionOptions() {
    return axios({
        url: '/region/getCascaderRegionOptions.do',
        method: 'get'
    })
}

function getCascaderChannelOptions() {
    return axios({
        url: '/region/getCascaderChannelOptions.do',
        method: 'get'
    })
}

function getRegion(regionId) {
    return axios({
        url: '/system/region/getregion.do?regionId=' + regionId,
        method: 'get'
    })
}