package com.vedeng.goods.enums;

import com.vedeng.common.constant.ErpConst;
import com.wms.constant.LogicalEnum;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品存储条件其他
 *
 * <AUTHOR> [<EMAIL>]
 */
@Getter
public enum GoodsStorageConditionOthersEnum {

    WINDY(1, "通风"),

    DRY(2, "干燥"),

    NON_LIGHT(3, "避光"),

    NON_HUMID(4, "防潮"),

    NON_HEAT(5, "避热"),

    SEALED(6, "密封"),

    ENCLOSED(7, "密闭"),

    MORE_SEALED(8, "严封"),

    SHADING(9, "遮光"),
    ;


    private Integer code;
    private String name;

    GoodsStorageConditionOthersEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        return Arrays.stream(GoodsStorageConditionOthersEnum.values())
                     .filter(item -> item.getCode().equals(Integer.valueOf(code)))
                     .map(item -> item.getName())
                     .findFirst()
                     .orElse("");
    }



    public static Integer[] covert2OthersCodes(String othersStringValue) {
        if (othersStringValue == null || othersStringValue.length() == 0) {
            return null;
        }

        String[] slices = othersStringValue.split(ErpConst.Symbol.COMMA);
        return Arrays.stream(slices).map(slice -> {
            try {
                return Integer.valueOf(slice);
            } catch (NumberFormatException e) {
                //skip
            }
            return null;
        }).filter(Objects::nonNull).toArray(Integer[]::new);
    }

    private static final Set<Integer> OTHERS_CONDITION_CODES = Arrays.stream(GoodsStorageConditionOthersEnum.values()).mapToInt(GoodsStorageConditionOthersEnum::getCode).boxed().collect(Collectors.toSet());


    public static boolean containAllInputs(Integer[] inputs) {
        if (inputs == null || inputs.length == 0) {
            return false;
        }
        Set<Integer> othersAfterFilter = Arrays.stream(inputs).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(othersAfterFilter) || othersAfterFilter.size() > GoodsStorageConditionOthersEnum.values().length) {
            return false;
        }
        return OTHERS_CONDITION_CODES.containsAll(othersAfterFilter);
    }
}
