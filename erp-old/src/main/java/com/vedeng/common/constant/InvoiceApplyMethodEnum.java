package com.vedeng.common.constant;

/**
 * <AUTHOR>
 * @Description 发票申请 申请方式
 * @createTime 2020年08月17日 10:28:00
 */
public enum InvoiceApplyMethodEnum {
    MANUAL_PUSH(0, "为销售手动申请"),
    AUTO_PUSH(1, "系统自动推送"),
    TICKET_AND_FREIGH(2, "票货同行物流部申请"),
    ONLINE_INVOICE_OPEN(3, "客户在线申请开票");

    InvoiceApplyMethodEnum(Integer applyMethodCode, String applyMethodStr) {
        this.applyMethodCode = applyMethodCode;
        this.applyMethodStr = applyMethodStr;
    }

    private Integer applyMethodCode;

    private String applyMethodStr;

    public Integer getApplyMethodCode() {
        return applyMethodCode;
    }

    public String getApplyMethodStr() {
        return applyMethodStr;
    }}
