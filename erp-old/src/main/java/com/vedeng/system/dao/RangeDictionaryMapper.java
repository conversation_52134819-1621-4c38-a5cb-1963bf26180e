package com.vedeng.system.dao;

import com.vedeng.system.model.RangeDictionary;
import java.util.List;

/**
 * @description: RangeDictionaryMapper.
 * @notes: VDERP-2336 预计可发货时间接口.
 * @version: 1.0.
 * @date: 2020/5/9 4:25 下午.
 * @author: Tomcat.Hui.
 */
public interface RangeDictionaryMapper {

    /**
     * @description: 获取所有字典.
     * @jira: VDERP-2336 预计可发货时间接口.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/5/9 4:26 下午.
     * @author: Tomcat.Hui.

     * @return: java.util.List<com.vedeng.order.domain.model.RangeDictionary>.
     * @throws: .
     */
    List<RangeDictionary> getAllDict();
}
