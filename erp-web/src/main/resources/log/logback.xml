<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 日志记录器，日期滚动记录 -->
    <appender name="rollingAppend" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/app/logs/erp/erp.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/app/logs/history/erp/erp-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>365</maxHistory>
            <totalSizeCap>30GB</totalSizeCap>
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%level] [erp] [%X{traceId}] [%thread] [s]%logger[%L][e]-%msg %n </pattern>
            <charset>utf-8</charset>
        </encoder>

    </appender>

    <appender name="armorAppend" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/app/logs/erp/erp-armor.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/app/logs/history/erp/erp-armor-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>10</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%level] [erp] [%X{traceId}] [%thread] [s]%logger[%L][e]-%msg %n </pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>
    <appender name="visitorAppend" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/app/logs/erp/erp-visitor.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/app/logs/history/erp/erp-visitor-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>365</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%level] [erp] [%X{traceId}] [%thread] [s]%logger[%L][e]-%msg %n </pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <!-- add by Tomcat.Hui 2020/6/10 11:07 上午 .Desc: VDERP-2376【五行剑法】规则修改. start -->
    <appender name="kpiAppend" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/app/logs/erp/kpi.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/app/logs/history/erp/kpi-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>365</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%level] [erp] [%X{traceId}] [%thread] [s]%logger[%L][e]-%msg %n </pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>
    <!-- add by Tomcat.Hui 2020/6/10 11:07 上午 .Desc: VDERP-2376【五行剑法】规则修改. end -->

    <appender name="ASYNC_rollingAppend" class="ch.qos.logback.classic.AsyncAppender">
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="rollingAppend"/>
    </appender>
    <appender name="ASYNC_armorAppend" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="armorAppend"/>
    </appender>
    <appender name="ASYNC_visitorAppend" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="visitorAppend"/>
    </appender>
    <appender name="kpi_rollingAppend" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="kpiAppend"/>
    </appender>
    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%level] [erp] [%X{traceId}] [%thread] [s]%logger[%L][e]-%msg %n </pattern>
        </encoder>
    </appender>
    <appender name="CatLog" class="com.dianping.cat.logback.CatLogbackAppender"></appender>


    <root level="info">
        　	<appender-ref ref="ASYNC_rollingAppend" />
        <appender-ref ref="stdout" />
        <appender-ref ref="CatLog" />
    </root>
    <logger name="net.sf.json.JSONObject" level="OFF" additivity="false" >
        <appender-ref ref="ASYNC_rollingAppend" />
    </logger>
    <logger name="com.xxl.job.core.thread" level="OFF" additivity="false" >
        <appender-ref ref="ASYNC_rollingAppend" />
    </logger>
<!--    <logger name="com.vedeng.ez.utils.DoCookie" level="OFF" additivity="false" >-->
<!--        <appender-ref ref="ASYNC_rollingAppend" />-->
<!--    </logger>-->
    <logger name="org.activiti.engine.ProcessEngines" level="OFF" additivity="false" >
        <appender-ref ref="ASYNC_rollingAppend" />
    </logger>
    <logger name="org.pac4j.cas.logout" level="OFF" additivity="false" >
        <appender-ref ref="ASYNC_rollingAppend" />
    </logger>
    <logger name="visitor" level="info" additivity="false">
        <appender-ref ref="ASYNC_visitorAppend" />
    </logger>
    <logger name="kpilog" level="error" additivity="false">
        <appender-ref ref="kpi_rollingAppend" />
    </logger>
    <logger name="org.springframework.amqp.rabbit.connection.CachingConnectionFactory" level="OFF" additivity="false" />
    <logger name="org.pac4j.cas.logout.DefaultCasLogoutHandler" level="off" additivity="false"/>
    <logger name="org.activiti.engine.impl.variable.SerializableType" level="off" additivity="false"/>

</configuration>