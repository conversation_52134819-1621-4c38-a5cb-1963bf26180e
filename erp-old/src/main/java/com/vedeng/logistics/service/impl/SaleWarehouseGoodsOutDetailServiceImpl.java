package com.vedeng.logistics.service.impl;

import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.eums.WarehouseOutInSourceEnum;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutVo;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.trader.model.Trader;
import com.wms.model.po.WmsOutputOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 销售出库
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/15 13:46
 **/
@Service
@Slf4j
public class SaleWarehouseGoodsOutDetailServiceImpl extends BaseWarehouseGoodsOutDetailService{

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Autowired
    private SaleorderGoodsMapper saleorderGoodsMapper;


    /**
     * 销售单详情页
     */
    private final String DETAIL_URL_SALE = "/orderstream/saleorder/detail.do?scene=0&saleOrderId=";

    /**
     * BD 订单
     */
    private final Integer ORDER_TYPE_BD = 1;

    /**
     * 客户类型-经销商
     */
    private final Integer TRADER_TYPE_CUSTOMER = 1;


    @Override
    public WarehouseGoodsOutVo detailWarehouseGoodsOut(String outInNo) {
        WarehouseGoodsOutVo saleWarehouseOut = commonDetailWarehouseGoodsOut(outInNo, WarehouseGoodsOutEnum.SALE_WAREHOUSE_OUT.getErpCode());
        // 对于销售出库
        // 需要获取销售单的 这些字段
        // 订单总额、单据归属、归属部门、收货客户、收货联系人、联系电话、联系手机号、收货地区、收货地址、指定物流公司、运费说明、物流备注
        if(saleWarehouseOut != null){
            String saleorderNo = "";
            saleorderNo = saleWarehouseOut.getRelateNo();
            log.info("SaleWarehouseGoodsOutDetailServiceImpl.detailWarehouseGoodsOut 查询销售出库单:{} 关联的 销售单:{} 的信息", outInNo, saleorderNo);
            if(StringUtils.isNotBlank(saleorderNo)){
                Saleorder saleorderQuery = new Saleorder();
                saleorderQuery.setSaleorderNo(saleorderNo);
                Saleorder saleorder = saleorderMapper.getSaleorderBySaleorderNo(saleorderQuery);
                if(saleorder != null){
                    // 订单总额 快照字段
                    saleWarehouseOut.setOrderTotalAmount(saleorder.getTotalAmount());
                    // 指定物流公司Id
                    saleWarehouseOut.setLogisticsCompanyId(saleorder.getLogisticsId());
                    // 单据归属、归属部门信息
                    // 单据归属: 关联销售单的 生效时的 归属销售  validUserId
                    // 归属部门: 单据归属人的部门
                    Integer userId = saleorder.getValidUserId();
                    if(userId != null){
                        User userInfo = userMapper.getUserInfoByUserId(userId);
                        if(userInfo != null){
                            saleWarehouseOut.setBelongUserId(userInfo.getUserId());
                            saleWarehouseOut.setBelongUserName(userInfo.getUsername());
                            saleWarehouseOut.setBelongUserOrgName(userInfo.getOrgName());
                        }
                    }
                    saleWarehouseOut.setTakeTraderName(saleorder.getTakeTraderName());
                    // 运费说明
                    saleWarehouseOut.setFreightDescriptionId(saleorder.getFreightDescription());

                    // 物流备注
                    saleWarehouseOut.setLogisticsComments(saleorder.getLogisticsComments());

                    // 收货地区、收货地址、收货联系人、联系电话、联系手机号
                    saleWarehouseOut.setTakeArea(saleorder.getTakeTraderArea());
                    saleWarehouseOut.setTakeAddress(saleorder.getTakeTraderAddress());
                    saleWarehouseOut.setTakeTraderContactName(saleorder.getTakeTraderContactName());
                    saleWarehouseOut.setTakeTraderContactMobile(saleorder.getTakeTraderContactMobile());
                    saleWarehouseOut.setTakeTraderContactTelephone(saleorder.getTakeTraderContactTelephone());


                }
            }

            List<Integer> relatedIdList = new ArrayList<>();
            List<WarehouseGoodsOutLogVo> warehouseGoodsOutLogVoList = saleWarehouseOut.getWarehouseGoodsOutLogList();
            for (WarehouseGoodsOutLogVo warehouseGoodsOutLogVo : warehouseGoodsOutLogVoList) {
                relatedIdList.add(warehouseGoodsOutLogVo.getRelatedId());
                if(WarehouseOutInSourceEnum.DIRECT_DELIVERY.getSource().equalsIgnoreCase(saleWarehouseOut.getSource())){
                    warehouseGoodsOutLogVo.setSterilizationBatchNumber("");
                }
            }

            // 一次查出 gift
            List<SaleorderGoods> saleorderGoodsGiftList = saleorderGoodsMapper.getIsGiftBySaleorderGoodsIdList(relatedIdList);
            // 转map
            Map<Integer, Integer> saleorderGoodsGiftMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(saleorderGoodsGiftList)){
                saleorderGoodsGiftMap = saleorderGoodsGiftList.stream().collect(Collectors.toMap(SaleorderGoods::getSaleorderGoodsId, SaleorderGoods::getIsGift));
            }
            for (WarehouseGoodsOutLogVo warehouseGoodsOutLogVo : warehouseGoodsOutLogVoList) {
                warehouseGoodsOutLogVo.setGiftGoods(saleorderGoodsGiftMap.getOrDefault(warehouseGoodsOutLogVo.getRelatedId(), 0));
            }

        }

        return saleWarehouseOut;
    }

    @Override
    public List<WarehouseGoodsOutLogVo> relatedNoWarehouseGoodsOutLogList(String relatedNo) {
        List<WarehouseGoodsOutLogVo> warehouseGoodsOutLogVoList = warehouseGoodsOutInMapper.selectWarehouseGoodsOutLogListByRelatedNo(WarehouseGoodsOutEnum.SALE_WAREHOUSE_OUT.getErpCode(), relatedNo);
        return CollectionUtils.isNotEmpty(warehouseGoodsOutLogVoList) ? warehouseGoodsOutLogVoList : new ArrayList<>();
    }

    @Override
    public String relatedNoDetailRedirect(String relatedNo) {
        Saleorder saleorder = saleorderMapper.getSaleOrderId(relatedNo);
        String url = "";
        if (saleorder != null){
            url = REDIRECT_URL_PREFIX + DETAIL_URL_SALE + saleorder.getSaleorderId();
        }
        return url;
    }
}
