package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.trader.dto.TraderCustomerErpDto;
import com.vedeng.erp.trader.dto.TraderDealerFrontDto;
import com.vedeng.erp.trader.service.TraderCustomerErpApiService;
import com.vedeng.erp.trader.service.TraderCustomerMarketingApiService;
import com.vedeng.erp.trader.service.TraderCustomerMarketingPrincipalApiService;
import com.vedeng.erp.trader.service.TraderDealerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/17 9:11
 **/
@Service
@Slf4j
public class TraderDealerServiceImpl implements TraderDealerService {

    @Autowired
    private TraderCustomerMarketingApiService traderCustomerMarketingApiService;

    @Autowired
    private TraderCustomerMarketingPrincipalApiService traderCustomerMarketingPrincipalApiService;

    @Autowired
    private TraderCustomerErpApiService traderCustomerErpApiService;

    @Override
    public TraderDealerFrontDto traderDealerFrontData(Integer traderCustomerId) {

        Assert.notNull(traderCustomerId,"客户id不可为空");
        TraderDealerFrontDto result = new TraderDealerFrontDto();

        TraderCustomerErpDto traderCustomerErpDto = traderCustomerErpApiService.queryByTraderCustomerId(traderCustomerId);
        if (Objects.nonNull(traderCustomerErpDto)) {
            result.setTraderCustomerId(traderCustomerId);
            result.setTraderId(traderCustomerErpDto.getTraderId());
        }

        List<TraderDealerFrontDto.Terminal> terminals = traderCustomerMarketingApiService.stealthFrontData(traderCustomerId);
        TraderDealerFrontDto.Principal principal = traderCustomerMarketingPrincipalApiService.stealthFrontData(traderCustomerId);

        result.setPrincipal(principal);
        result.setTerminal(terminals);

        log.info("traderDealerFrontData;traderCustomerId:{},result:{}", traderCustomerId, JSON.toJSONString(result));

        return result;
    }
}
