<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.saleorder.dao.VJdSaleorderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.saleorder.model.po.VJdSaleorder">
    <!--@mbg.generated-->
    <!--@Table V_JD_SALEORDER-->
    <id column="SALEORDER_ID" jdbcType="BIGINT" property="saleorderId" />
    <result column="JD_SALEORDER_NO" jdbcType="VARCHAR" property="jdSaleorderNo" />
    <result column="SALEORDER_NO" jdbcType="VARCHAR" property="saleorderNo" />
    <result column="JD_SKU_NO" jdbcType="VARCHAR" property="jdSkuNo" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="PURCHASING_PRICE" jdbcType="DECIMAL" property="purchasingPrice" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="ARRIVAL_USER_NAME" jdbcType="VARCHAR" property="arrivalUserName" />
    <result column="ARRIVAL_USER_PHONE" jdbcType="VARCHAR" property="arrivalUserPhone" />
    <result column="CONSIGNEE_ADDRESS" jdbcType="VARCHAR" property="consigneeAddress" />
    <result column="PROVINCE" jdbcType="VARCHAR" property="province" />
    <result column="CITY" jdbcType="VARCHAR" property="city" />
    <result column="COUNTY" jdbcType="VARCHAR" property="county" />
    <result column="ORDER_STATUS" jdbcType="VARCHAR" property="orderStatus" />
    <result column="ORDER_GENERATE_STATUS" jdbcType="INTEGER" property="orderGenerateStatus" />
    <result column="REGION_ID" jdbcType="INTEGER" property="regionId" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="ERROR_REASON" jdbcType="VARCHAR" property="errorReason" />
    <result column="PURCHASE_TIME" jdbcType="VARCHAR" property="purchaseTime" />
    <result column="TERMINAL_TRADER_ID" jdbcType="VARCHAR" property="terminalTraderId" />
    <result column="TERMINAL_TRADER_NAME" jdbcType="VARCHAR" property="terminalTraderName" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SALEORDER_ID, JD_SALEORDER_NO, SALEORDER_NO, JD_SKU_NO, SKU_NO, PURCHASING_PRICE, 
    NUM, ARRIVAL_USER_NAME, ARRIVAL_USER_PHONE, CONSIGNEE_ADDRESS, PROVINCE, CITY, COUNTY, 
    ORDER_STATUS, ORDER_GENERATE_STATUS, REGION_ID, CREATOR, CREATOR_NAME, IS_DELETE, 
    UPDATER, ADD_TIME, REMARK, UPDATE_REMARK, UPDATER_NAME, MOD_TIME, ERROR_REASON,PURCHASE_TIME,TERMINAL_TRADER_ID,TERMINAL_TRADER_NAME,TRADER_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from V_JD_SALEORDER
    where SALEORDER_ID = #{saleorderId,jdbcType=BIGINT}
  </select>

  <select id="selectByJdSaleOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from V_JD_SALEORDER
    where JD_SALEORDER_NO = #{jdSaleOrderNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from V_JD_SALEORDER
    where SALEORDER_ID = #{saleorderId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="SALEORDER_ID" keyProperty="saleorderId" parameterType="com.vedeng.erp.saleorder.model.po.VJdSaleorder" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_JD_SALEORDER (JD_SALEORDER_NO, SALEORDER_NO, JD_SKU_NO, 
      SKU_NO, PURCHASING_PRICE, NUM, 
      ARRIVAL_USER_NAME, ARRIVAL_USER_PHONE, CONSIGNEE_ADDRESS, 
      PROVINCE, CITY, COUNTY, 
      ORDER_STATUS, ORDER_GENERATE_STATUS, REGION_ID, 
      CREATOR, CREATOR_NAME, IS_DELETE, 
      UPDATER, ADD_TIME, REMARK, 
      UPDATE_REMARK, UPDATER_NAME, MOD_TIME, 
      ERROR_REASON,PURCHASE_TIME,TERMINAL_TRADER_ID,TERMINAL_TRADER_NAME,TRADER_ID)
    values (#{jdSaleorderNo,jdbcType=VARCHAR}, #{saleorderNo,jdbcType=VARCHAR}, #{jdSkuNo,jdbcType=VARCHAR}, 
      #{skuNo,jdbcType=VARCHAR}, #{purchasingPrice,jdbcType=DECIMAL}, #{num,jdbcType=INTEGER}, 
      #{arrivalUserName,jdbcType=VARCHAR}, #{arrivalUserPhone,jdbcType=VARCHAR}, #{consigneeAddress,jdbcType=VARCHAR}, 
      #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{county,jdbcType=VARCHAR}, 
      #{orderStatus,jdbcType=VARCHAR}, #{orderGenerateStatus,jdbcType=INTEGER}, #{regionId,jdbcType=INTEGER}, 
      #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{isDelete,jdbcType=BOOLEAN}, 
      #{updater,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{updateRemark,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, 
      #{errorReason,jdbcType=VARCHAR},#{purchaseTime,jdbcType=VARCHAR},#{terminalTraderId,jdbcType=VARCHAR},#{terminalTraderName,jdbcType=VARCHAR}, #{traderId,jdbcType=INTEGER}
    )
  </insert>
  <insert id="insertSelective" keyColumn="SALEORDER_ID" keyProperty="saleorderId" parameterType="com.vedeng.erp.saleorder.model.po.VJdSaleorder" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_JD_SALEORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="jdSaleorderNo != null">
        JD_SALEORDER_NO,
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO,
      </if>
      <if test="jdSkuNo != null">
        JD_SKU_NO,
      </if>
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="purchasingPrice != null">
        PURCHASING_PRICE,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="arrivalUserName != null">
        ARRIVAL_USER_NAME,
      </if>
      <if test="arrivalUserPhone != null">
        ARRIVAL_USER_PHONE,
      </if>
      <if test="consigneeAddress != null">
        CONSIGNEE_ADDRESS,
      </if>
      <if test="province != null">
        PROVINCE,
      </if>
      <if test="city != null">
        CITY,
      </if>
      <if test="county != null">
        COUNTY,
      </if>
      <if test="orderStatus != null">
        ORDER_STATUS,
      </if>
      <if test="orderGenerateStatus != null">
        ORDER_GENERATE_STATUS,
      </if>
      <if test="regionId != null">
        REGION_ID,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="errorReason != null">
        ERROR_REASON,
      </if>
      <if test="purchaseTime != null">
        PURCHASE_TIME,
      </if>
      <if test="terminalTraderId != null">
        TERMINAL_TRADER_ID,
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="jdSaleorderNo != null">
        #{jdSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="saleorderNo != null">
        #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="jdSkuNo != null">
        #{jdSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="purchasingPrice != null">
        #{purchasingPrice,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="arrivalUserName != null">
        #{arrivalUserName,jdbcType=VARCHAR},
      </if>
      <if test="arrivalUserPhone != null">
        #{arrivalUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="consigneeAddress != null">
        #{consigneeAddress,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="county != null">
        #{county,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderGenerateStatus != null">
        #{orderGenerateStatus,jdbcType=INTEGER},
      </if>
      <if test="regionId != null">
        #{regionId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorReason != null">
        #{errorReason,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null">
        #{purchaseTime,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null">
        #{terminalTraderId,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderName != null">
        #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.saleorder.model.po.VJdSaleorder">
    <!--@mbg.generated-->
    update V_JD_SALEORDER
    <set>
      <if test="jdSaleorderNo != null">
        JD_SALEORDER_NO = #{jdSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="jdSkuNo != null">
        JD_SKU_NO = #{jdSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="purchasingPrice != null">
        PURCHASING_PRICE = #{purchasingPrice,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="arrivalUserName != null">
        ARRIVAL_USER_NAME = #{arrivalUserName,jdbcType=VARCHAR},
      </if>
      <if test="arrivalUserPhone != null">
        ARRIVAL_USER_PHONE = #{arrivalUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="consigneeAddress != null">
        CONSIGNEE_ADDRESS = #{consigneeAddress,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        PROVINCE = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        CITY = #{city,jdbcType=VARCHAR},
      </if>
      <if test="county != null">
        COUNTY = #{county,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        ORDER_STATUS = #{orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderGenerateStatus != null">
        ORDER_GENERATE_STATUS = #{orderGenerateStatus,jdbcType=INTEGER},
      </if>
      <if test="regionId != null">
        REGION_ID = #{regionId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorReason != null">
        ERROR_REASON = #{errorReason,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null">
        PURCHASE_TIME = #{purchaseTime,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null">
        TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="vdSaleorderId != null">
        VD_SALEORDER_ID = #{vdSaleorderId,jdbcType=BIGINT},
      </if>
    </set>
    where SALEORDER_ID = #{saleorderId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.saleorder.model.po.VJdSaleorder">
    <!--@mbg.generated-->
    update V_JD_SALEORDER
    set JD_SALEORDER_NO = #{jdSaleorderNo,jdbcType=VARCHAR},
      SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      JD_SKU_NO = #{jdSkuNo,jdbcType=VARCHAR},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      PURCHASING_PRICE = #{purchasingPrice,jdbcType=DECIMAL},
      NUM = #{num,jdbcType=INTEGER},
      ARRIVAL_USER_NAME = #{arrivalUserName,jdbcType=VARCHAR},
      ARRIVAL_USER_PHONE = #{arrivalUserPhone,jdbcType=VARCHAR},
      CONSIGNEE_ADDRESS = #{consigneeAddress,jdbcType=VARCHAR},
      PROVINCE = #{province,jdbcType=VARCHAR},
      CITY = #{city,jdbcType=VARCHAR},
      COUNTY = #{county,jdbcType=VARCHAR},
      ORDER_STATUS = #{orderStatus,jdbcType=VARCHAR},
      ORDER_GENERATE_STATUS = #{orderGenerateStatus,jdbcType=INTEGER},
      REGION_ID = #{regionId,jdbcType=INTEGER},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      UPDATER = #{updater,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      ERROR_REASON = #{errorReason,jdbcType=VARCHAR},
      PURCHASE_TIME = #{purchaseTime,jdbcType=VARCHAR},
      TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=VARCHAR},
      TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      TRADER_ID = #{traderId,jdbcType=INTEGER}
    where SALEORDER_ID = #{saleorderId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update V_JD_SALEORDER
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="JD_SALEORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.jdSaleorderNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SALEORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.saleorderNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="JD_SKU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.jdSkuNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SKU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.skuNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PURCHASING_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.purchasingPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.num,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_USER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.arrivalUserName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_USER_PHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.arrivalUserPhone,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="CONSIGNEE_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.consigneeAddress,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PROVINCE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.province,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="CITY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.city,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="COUNTY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.county,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ORDER_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.orderStatus,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ORDER_GENERATE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.orderGenerateStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="REGION_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.regionId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="ERROR_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.errorReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PURCHASE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.purchaseTime,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TERMINAL_TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.terminalTraderId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TERMINAL_TRADER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.terminalTraderName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.traderId,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where SALEORDER_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.saleorderId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update V_JD_SALEORDER
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="JD_SALEORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.jdSaleorderNo != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.jdSaleorderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SALEORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.saleorderNo != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.saleorderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="JD_SKU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.jdSkuNo != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.jdSkuNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuNo != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.skuNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PURCHASING_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.purchasingPrice != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.purchasingPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.num != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.num,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_USER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalUserName != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.arrivalUserName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_USER_PHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalUserPhone != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.arrivalUserPhone,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONSIGNEE_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.consigneeAddress != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.consigneeAddress,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PROVINCE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.province != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.province,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CITY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.city != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.city,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="COUNTY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.county != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.county,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORDER_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderStatus != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.orderStatus,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORDER_GENERATE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderGenerateStatus != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.orderGenerateStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="REGION_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.regionId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.regionId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateRemark != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="ERROR_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.errorReason != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.errorReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PURCHASE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.purchaseTime,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TERMINAL_TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.terminalTraderId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TERMINAL_TRADER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.terminalTraderName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=BIGINT} then #{item.traderId,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where SALEORDER_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.saleorderId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="SALEORDER_ID" keyProperty="saleorderId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_JD_SALEORDER
    (JD_SALEORDER_NO, SALEORDER_NO, JD_SKU_NO, SKU_NO, PURCHASING_PRICE, NUM, ARRIVAL_USER_NAME, 
      ARRIVAL_USER_PHONE, CONSIGNEE_ADDRESS, PROVINCE, CITY, COUNTY, ORDER_STATUS, ORDER_GENERATE_STATUS, 
      REGION_ID, CREATOR, CREATOR_NAME, IS_DELETE, UPDATER, ADD_TIME, REMARK, UPDATE_REMARK, 
      UPDATER_NAME, MOD_TIME, ERROR_REASON,PURCHASE_TIME,TERMINAL_TRADER_ID,TERMINAL_TRADER_NAME,TRADER_ID)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.jdSaleorderNo,jdbcType=VARCHAR}, #{item.saleorderNo,jdbcType=VARCHAR}, #{item.jdSkuNo,jdbcType=VARCHAR}, 
        #{item.skuNo,jdbcType=VARCHAR}, #{item.purchasingPrice,jdbcType=DECIMAL}, #{item.num,jdbcType=INTEGER}, 
        #{item.arrivalUserName,jdbcType=VARCHAR}, #{item.arrivalUserPhone,jdbcType=VARCHAR}, 
        #{item.consigneeAddress,jdbcType=VARCHAR}, #{item.province,jdbcType=VARCHAR}, #{item.city,jdbcType=VARCHAR}, 
        #{item.county,jdbcType=VARCHAR}, #{item.orderStatus,jdbcType=VARCHAR}, #{item.orderGenerateStatus,jdbcType=INTEGER}, 
        #{item.regionId,jdbcType=INTEGER}, #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.isDelete,jdbcType=BOOLEAN}, #{item.updater,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.updateRemark,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.modTime,jdbcType=TIMESTAMP}, #{item.errorReason,jdbcType=VARCHAR},#{item.purchaseTime,jdbcType=VARCHAR},
      #{item.terminalTraderId,jdbcType=VARCHAR},#{item.terminalTraderName,jdbcType=VARCHAR},#{traderId,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="SALEORDER_ID" keyProperty="saleorderId" parameterType="com.vedeng.erp.saleorder.model.po.VJdSaleorder" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_JD_SALEORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        SALEORDER_ID,
      </if>
      JD_SALEORDER_NO,
      SALEORDER_NO,
      JD_SKU_NO,
      SKU_NO,
      PURCHASING_PRICE,
      NUM,
      ARRIVAL_USER_NAME,
      ARRIVAL_USER_PHONE,
      CONSIGNEE_ADDRESS,
      PROVINCE,
      CITY,
      COUNTY,
      ORDER_STATUS,
      ORDER_GENERATE_STATUS,
      REGION_ID,
      CREATOR,
      CREATOR_NAME,
      IS_DELETE,
      UPDATER,
      ADD_TIME,
      REMARK,
      UPDATE_REMARK,
      UPDATER_NAME,
      MOD_TIME,
      ERROR_REASON,
      PURCHASE_TIME,
      TERMINAL_TRADER_ID,
      TERMINAL_TRADER_NAME,
      TRADER_ID,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        #{saleorderId,jdbcType=BIGINT},
      </if>
      #{jdSaleorderNo,jdbcType=VARCHAR},
      #{saleorderNo,jdbcType=VARCHAR},
      #{jdSkuNo,jdbcType=VARCHAR},
      #{skuNo,jdbcType=VARCHAR},
      #{purchasingPrice,jdbcType=DECIMAL},
      #{num,jdbcType=INTEGER},
      #{arrivalUserName,jdbcType=VARCHAR},
      #{arrivalUserPhone,jdbcType=VARCHAR},
      #{consigneeAddress,jdbcType=VARCHAR},
      #{province,jdbcType=VARCHAR},
      #{city,jdbcType=VARCHAR},
      #{county,jdbcType=VARCHAR},
      #{orderStatus,jdbcType=VARCHAR},
      #{orderGenerateStatus,jdbcType=INTEGER},
      #{regionId,jdbcType=INTEGER},
      #{creator,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{isDelete,jdbcType=BOOLEAN},
      #{updater,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP},
      #{remark,jdbcType=VARCHAR},
      #{updateRemark,jdbcType=VARCHAR},
      #{updaterName,jdbcType=VARCHAR},
      #{modTime,jdbcType=TIMESTAMP},
      #{errorReason,jdbcType=VARCHAR},
      #{purchaseTime,jdbcType=VARCHAR},
      #{terminalTraderId,jdbcType=VARCHAR},
      #{terminalTraderName,jdbcType=VARCHAR},
      #{traderId,jdbcType=INTEGER}
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="saleorderId != null">
        SALEORDER_ID = #{saleorderId,jdbcType=BIGINT},
      </if>
      JD_SALEORDER_NO = #{jdSaleorderNo,jdbcType=VARCHAR},
      SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      JD_SKU_NO = #{jdSkuNo,jdbcType=VARCHAR},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      PURCHASING_PRICE = #{purchasingPrice,jdbcType=DECIMAL},
      NUM = #{num,jdbcType=INTEGER},
      ARRIVAL_USER_NAME = #{arrivalUserName,jdbcType=VARCHAR},
      ARRIVAL_USER_PHONE = #{arrivalUserPhone,jdbcType=VARCHAR},
      CONSIGNEE_ADDRESS = #{consigneeAddress,jdbcType=VARCHAR},
      PROVINCE = #{province,jdbcType=VARCHAR},
      CITY = #{city,jdbcType=VARCHAR},
      COUNTY = #{county,jdbcType=VARCHAR},
      ORDER_STATUS = #{orderStatus,jdbcType=VARCHAR},
      ORDER_GENERATE_STATUS = #{orderGenerateStatus,jdbcType=INTEGER},
      REGION_ID = #{regionId,jdbcType=INTEGER},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      UPDATER = #{updater,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      ERROR_REASON = #{errorReason,jdbcType=VARCHAR},
      PURCHASE_TIME = #{purchaseTime,jdbcType=VARCHAR},
      TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=VARCHAR},
      TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      TRADER_ID=#{traderId,jdbcType=INTEGER}
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="SALEORDER_ID" keyProperty="saleorderId" parameterType="com.vedeng.erp.saleorder.model.po.VJdSaleorder" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_JD_SALEORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        SALEORDER_ID,
      </if>
      <if test="jdSaleorderNo != null">
        JD_SALEORDER_NO,
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO,
      </if>
      <if test="jdSkuNo != null">
        JD_SKU_NO,
      </if>
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="purchasingPrice != null">
        PURCHASING_PRICE,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="arrivalUserName != null">
        ARRIVAL_USER_NAME,
      </if>
      <if test="arrivalUserPhone != null">
        ARRIVAL_USER_PHONE,
      </if>
      <if test="consigneeAddress != null">
        CONSIGNEE_ADDRESS,
      </if>
      <if test="province != null">
        PROVINCE,
      </if>
      <if test="city != null">
        CITY,
      </if>
      <if test="county != null">
        COUNTY,
      </if>
      <if test="orderStatus != null">
        ORDER_STATUS,
      </if>
      <if test="orderGenerateStatus != null">
        ORDER_GENERATE_STATUS,
      </if>
      <if test="regionId != null">
        REGION_ID,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="errorReason != null">
        ERROR_REASON,
      </if>
      <if test="purchaseTime != null">
        PURCHASE_TIME ,
      </if>
      <if test="terminalTraderId != null">
        TERMINAL_TRADER_ID ,
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME ,
      </if>
      <if test="traderId != null">
        TRADER_ID ,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        #{saleorderId,jdbcType=BIGINT},
      </if>
      <if test="jdSaleorderNo != null">
        #{jdSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="saleorderNo != null">
        #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="jdSkuNo != null">
        #{jdSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="purchasingPrice != null">
        #{purchasingPrice,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="arrivalUserName != null">
        #{arrivalUserName,jdbcType=VARCHAR},
      </if>
      <if test="arrivalUserPhone != null">
        #{arrivalUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="consigneeAddress != null">
        #{consigneeAddress,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="county != null">
        #{county,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderGenerateStatus != null">
        #{orderGenerateStatus,jdbcType=INTEGER},
      </if>
      <if test="regionId != null">
        #{regionId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorReason != null">
        #{errorReason,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null">
        #{purchaseTime,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null">
        #{terminalTraderId,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderName != null">
        #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="saleorderId != null">
        SALEORDER_ID = #{saleorderId,jdbcType=BIGINT},
      </if>
      <if test="jdSaleorderNo != null">
        JD_SALEORDER_NO = #{jdSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="jdSkuNo != null">
        JD_SKU_NO = #{jdSkuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuNo != null">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="purchasingPrice != null">
        PURCHASING_PRICE = #{purchasingPrice,jdbcType=DECIMAL},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="arrivalUserName != null">
        ARRIVAL_USER_NAME = #{arrivalUserName,jdbcType=VARCHAR},
      </if>
      <if test="arrivalUserPhone != null">
        ARRIVAL_USER_PHONE = #{arrivalUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="consigneeAddress != null">
        CONSIGNEE_ADDRESS = #{consigneeAddress,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        PROVINCE = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        CITY = #{city,jdbcType=VARCHAR},
      </if>
      <if test="county != null">
        COUNTY = #{county,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        ORDER_STATUS = #{orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderGenerateStatus != null">
        ORDER_GENERATE_STATUS = #{orderGenerateStatus,jdbcType=INTEGER},
      </if>
      <if test="regionId != null">
        REGION_ID = #{regionId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="errorReason != null">
        ERROR_REASON = #{errorReason,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTime != null">
        PURCHASE_TIME = #{purchaseTime,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null">
        TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID=#{traderId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2022-12-07-->
  <select id="findByOrderGenerateStatusInAndIsDeleteFalseOrderByAddTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from V_JD_SALEORDER
    where ORDER_GENERATE_STATUS in
    <foreach item="item" index="index" collection="orderGenerateStatusCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
    and IS_DELETE = false order by ADD_TIME asc limit 1000
  </select>

  <insert id="batchInsertJdSaleorder" keyColumn="SALEORDER_ID" keyProperty="saleorderId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_JD_SALEORDER
    (JD_SALEORDER_NO, JD_SKU_NO, SKU_NO, PURCHASING_PRICE, NUM, ARRIVAL_USER_NAME,
    ARRIVAL_USER_PHONE, CONSIGNEE_ADDRESS, PROVINCE, CITY, COUNTY, ORDER_STATUS,
    REGION_ID,ERROR_REASON,ORDER_GENERATE_STATUS,PURCHASE_TIME,TERMINAL_TRADER_ID,TERMINAL_TRADER_NAME,REMARK,TRADER_ID)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.jdSaleorderNo,jdbcType=VARCHAR}, #{item.jdSkuNo,jdbcType=VARCHAR},
      #{item.skuNo,jdbcType=VARCHAR}, #{item.purchasingPrice,jdbcType=DECIMAL}, #{item.num,jdbcType=INTEGER},
      #{item.arrivalUserName,jdbcType=VARCHAR}, #{item.arrivalUserPhone,jdbcType=VARCHAR},
      #{item.consigneeAddress,jdbcType=VARCHAR}, #{item.province,jdbcType=VARCHAR}, #{item.city,jdbcType=VARCHAR},
      #{item.county,jdbcType=VARCHAR}, #{item.orderStatus,jdbcType=VARCHAR},
      #{item.regionId,jdbcType=INTEGER},#{item.errorReason,jdbcType=VARCHAR}, #{item.orderGenerateStatus,jdbcType=INTEGER},#{item.purchaseTime,jdbcType=VARCHAR},#{item.terminalTraderId,jdbcType=VARCHAR},#{item.terminalTraderName,jdbcType=VARCHAR},#{item.remark,jdbcType=VARCHAR},#{item.traderId,jdbcType=INTEGER})
    </foreach>
  </insert>
  
  <select id="getNotAllowedOrder" resultType="string">
    select JD_SALEORDER_NO from V_JD_SALEORDER where JD_SALEORDER_NO in
    <foreach collection="list" item="item" separator="," open="(" close=")">
      #{item.jdSaleorderNo}
    </foreach>
  </select>

  <select id="getErrorOrderList" resultType="string">
    select JD_SALEORDER_NO from V_JD_SALEORDER where ORDER_GENERATE_STATUS in(3,4)
    AND JD_SALEORDER_NO in
    <foreach collection="list" item="item" separator="," open="(" close=")">
      #{item.jdSaleorderNo}
    </foreach>
  </select>
  <select id="getVerifyStatusByOrderId" resultType="java.lang.Integer">
    select
      b.VERIFY_STATUS
    from
      T_SALEORDER_DATA b
    where b.SALEORDER_ID=#{saleorderId,jdbcType=INTEGER}
  </select>

  <update id="updateBatchByJdSaleorderNo" parameterType="java.util.List">
    <!--@mbg.generated-->
    update V_JD_SALEORDER
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="SALEORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.saleorderNo != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.saleorderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="JD_SKU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.jdSkuNo != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.jdSkuNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SKU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuNo != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.skuNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PURCHASING_PRICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.purchasingPrice != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.purchasingPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.num != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.num,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_USER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalUserName != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.arrivalUserName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_USER_PHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalUserPhone != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.arrivalUserPhone,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONSIGNEE_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.consigneeAddress != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.consigneeAddress,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PROVINCE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.province != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.province,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CITY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.city != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.city,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="COUNTY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.county != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.county,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORDER_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderStatus != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.orderStatus,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORDER_GENERATE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderGenerateStatus != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.orderGenerateStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="REGION_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.regionId != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.regionId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.isDelete,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateRemark != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.updateRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="ERROR_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.errorReason != null">
            when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.errorReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PURCHASE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.purchaseTime,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TERMINAL_TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.terminalTraderId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TERMINAL_TRADER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.terminalTraderName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when JD_SALEORDER_NO = #{item.jdSaleorderNo,jdbcType=VARCHAR} then #{item.traderId,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where JD_SALEORDER_NO in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.jdSaleorderNo,jdbcType=VARCHAR}
    </foreach>
  </update>
</mapper>