<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="线索详情" scope="application"/>
<%@ include file="../common/common.jsp" %>

<div class="main-container">
    <div class="parts">
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <td style="border: none">
                    <span style="float: left">
                        <a class="addtitle" href="javascript:void(0);"
                           tabTitle='{"num":"viewcustomer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                "link":"./trader/customer/baseinfo.do?traderId=${tuokeLabelInfo.traderId}",
                                "title":"客户信息"}'>
                            ${tuokeLabelInfo.traderName}
                        </a>
                    </span>
                </td>
                <td style="border: none"></td>
                <td style="border: none"></td>
                <td style="border: none"></td>
                <td style="border: none">
                        <span class="pop-new-data" style="float: right; color: #438DEF"
                              layerparams='{"width":"1250px","height":"750px","title":"修改标签","link":"/tuoke/traderCustomerLabel/editView.do?traderId=${tuokeLabelInfo.traderId}"}'>
                                                    修改标签
                                    </span>
                </td>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>标签类型</td>
                <td>标签</td>
                <td>标签值</td>
                <td>标签</td>
                <td>标签值</td>
            </tr>
            <tr>
                <td>客群标签</td>
                <td>经营对象</td>
                <td>${tuokeLabelInfo.businessTargetStr}</td>
                <td>医院等级</td>
                <td>${tuokeLabelInfo.hospitalLevelStr}</td>
            </tr>
            <tr>
                <td>产品标签</td>
                <td>经营科室</td>
                <td>${tuokeLabelInfo.businessDepartmentStr}</td>
                <td>经营产品</td>
                <td>${tuokeLabelInfo.businessGoodsStr}</td>
            </tr>
            <tr>
                <td>销售标签</td>
                <td>业务模式</td>
                <td>${tuokeLabelInfo.businessModelStr}</td>
                <td>销售模式</td>
                <td>${tuokeLabelInfo.salesModelStr}</td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title ">
                线索价值
            </div>
            <div class="title-click   pop-new-data"
                 layerparams='{"width":"550px","height":"300px","title":"修改线索价值","link":"/business/clues/editCluesWorth.do?businessCluesId=${tuokeLabelInfo.businessCluesId}"}'>
                评估价值
            </div>
        </div>

        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td>线索价值</td>
                <td>
                    <c:choose>
                        <c:when test="${tuokeLabelInfo.worth eq 0}">
                            无效
                        </c:when>
                        <c:when test="${tuokeLabelInfo.worth eq 1}">
                            有效
                        </c:when>
                        <c:otherwise>
                            未评估
                        </c:otherwise>
                    </c:choose>

                </td>
                <td>备注</td>
                <td>${tuokeLabelInfo.comment}</td>
            </tr>
            </tbody>
        </table>

    </div>

</div>

<div class="main-container">
    <div class="list-pages-search">
        <form action="${pageContext.request.contextPath}/business/clues/viewBusinessClues.do?traderId=${tuokeLabelInfo.traderId}&groupId=${groupId}&businessCluesId=${tuokeLabelInfo.businessCluesId}"
              method="post" id="search">
            <ul>
                <li>
                    <label class="infor_name">关键词</label>
                    <input type="text" class="input-middle" name="keywords" id="keywords" placeholder="请输入品牌/商品/型号"
                           value="${businessCluesDetailQueryDTO.keywords}">
                </li>
                <li>
                    <label class="infor_name">中标时间</label>
                    <input class="Wdate f_left input-smaller96 mr5" type="text" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endTime\')}'})"
                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="zhongbiaoStarTime" id="startTime"
                           value="${businessCluesDetailQueryDTO.zhongbiaoStarTime}">
                    <div class="gang">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startTime\')}'})"
                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="zhongbiaoEndTime" id="endTime"
                           value="${businessCluesDetailQueryDTO.zhongbiaoEndTime}">
                </li>
                <li>
                    <label class="infor_name">招标地区</label>
                    <ul class="inputfloat f_left">
                        <li>
                            <select class="wid9" name="zhaobiaoProvince" id="zhaobiaoProvince">
                                <option value="">全部</option>
                                <c:if test="${not empty provinceList }">
                                    <c:forEach items="${provinceList }" var="prov">
                                        <option value="${prov.regionId }"
                                                <c:if test="${businessCluesDetailQueryDTO.zhaobiaoProvince eq prov.regionId }">selected="selected"</c:if>>${prov.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                        </li>
                        <li>
                            <select class="wid9" name="zhaobiaoCity" id="zhaobiaoCity">
                                <option value="">全部</option>
                                <c:if test="${not empty cityList }">
                                    <c:forEach items="${cityList }" var="cy">
                                        <option value="${cy.regionId }"
                                                <c:if test="${businessCluesDetailQueryDTO.zhaobiaoCity eq cy.regionId }">selected="selected"</c:if>>${cy.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                        </li>
                        <li>
                            <select class="wid9" name="zhaobiaoCountry" id="zhaobiaoCountry">
                                <option value="">全部</option>
                                <c:if test="${not empty zoneList }">
                                    <c:forEach items="${zoneList }" var="zo">
                                        <option value="${zo.regionId }"
                                                <c:if test="${businessCluesDetailQueryDTO.zhaobiaoCountry eq zo.regionId }">selected="selected"</c:if>>${zo.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                        </li>
                    </ul>
                </li>
            </ul>
            <div class="tcenter">
                <span class="bg-light-blue bt-bg-style bt-middle" id="searchSpan" onclick="search();">搜索</span>
                <span class="bt-middle bg-light-blue bt-bg-style" onclick="reset();">重置</span>
                <c:if test="${tuokeLabelInfo.businessChanceId == null}">
                                    <span class="bt-middle bg-light-blue bt-bg-style mr20 addtitle"
                                          tabTitle='{"num":"businesschanceservice<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"./order/bussinesschance/newAddBussinesChance.do?traderId=${tuokeLabelInfo.traderId}&isClues=1&businessCluesId=${tuokeLabelInfo.businessCluesId}",
                                          "title":"新增商机"}'>转商机</span>
                </c:if>
            </div>
        </form>
    </div>

    <div class="fixdiv">
        <div class="superdiv" style="margin:auto;">
            <table class="table table-bordered table-striped table-condensed table-centered" style="border-color: black">
                <thead>
                <tr>
                    <td rowspan="2" style="border-color: black">序号</td>
                    <td colspan="7" style="border-color: black">招标单位</td>
                    <td colspan="4" style="border-color: black">中标商品</td>
                    <td rowspan="2" style="border-color: black">中标时间</td>
                    <td rowspan="2" style="border-color: black" class="wid25">操作</td>
                </tr>
                <tr>
                    <td style="border-color: black">单位名称</td>
                    <td style="border-color: black">省</td>
                    <td style="border-color: black">市</td>
                    <td style="border-color: black">区</td>
                    <td style="border-color: black">单位性质</td>
                    <td style="border-color: black">单位等级</td>
                    <td style="border-color: black">单位类型</td>
                    <td style="border-color: black">序号</td>
                    <td style="border-color: black">品牌</td>
                    <td style="border-color: black">商品</td>
                    <td style="border-color: black">型号</td>
                </tr>
                </thead>
                <tbody class="employeestate">
                <c:if test="${not empty businessCluesDetailList}">
                    <c:forEach items="${businessCluesDetailList}" var="businessCluesDetail" varStatus="status">
                        <c:forEach items="${businessCluesDetail.zhongbiaoGoodsList}" var="goods" varStatus="status2">
                            <tr>
                                <c:if test="${status2.count eq 1}">
                                    <td rowspan="${businessCluesDetail.zhongbiaoGoodsList.size()}" style="border-color: black">${status.count}</td>
                                </c:if>
                                <c:if test="${status2.count eq 1}">
                                    <td rowspan="${businessCluesDetail.zhongbiaoGoodsList.size()}" style="border-color: black">${businessCluesDetail.zhaobiaoUnit}</td>
                                </c:if>
                                <c:if test="${status2.count eq 1}">
                                    <td rowspan="${businessCluesDetail.zhongbiaoGoodsList.size()}" style="border-color: black">${businessCluesDetail.zhaobiaoProvinceName}</td>
                                </c:if>
                                <c:if test="${status2.count eq 1}">
                                    <td rowspan="${businessCluesDetail.zhongbiaoGoodsList.size()}" style="border-color: black">${businessCluesDetail.zhaobiaoCityName}</td>
                                </c:if>
                                <c:if test="${status2.count eq 1}">
                                    <td rowspan="${businessCluesDetail.zhongbiaoGoodsList.size()}" style="border-color: black">${businessCluesDetail.zhaobiaoCountryName}</td>
                                </c:if>
                                <c:if test="${status2.count eq 1}">
                                    <td rowspan="${businessCluesDetail.zhongbiaoGoodsList.size()}" style="border-color: black">${businessCluesDetail.zhaobiaoXingzhi}</td>
                                </c:if>
                                <c:if test="${status2.count eq 1}">
                                    <td rowspan="${businessCluesDetail.zhongbiaoGoodsList.size()}" style="border-color: black">${businessCluesDetail.zhaobiaoLevel}</td>
                                </c:if>
                                <c:if test="${status2.count eq 1}">
                                    <td rowspan="${businessCluesDetail.zhongbiaoGoodsList.size()}" style="border-color: black">${businessCluesDetail.zhaobiaoType}</td>
                                </c:if>
                                <td style="border-color: black">${status2.count}</td>
                                <td style="border-color: black">${goods.zhongbiaoBrand}</td>
                                <td style="border-color: black">${goods.zhongbiaoGoods}</td>
                                <td style="border-color: black">${goods.zhongbiaoModel}</td>
                                <c:if test="${status2.count eq 1}">
                                    <td rowspan="${businessCluesDetail.zhongbiaoGoodsList.size()}" style="border-color: black">${businessCluesDetail.infoPublishTime}</td>
                                </c:if>
                                <c:if test="${status2.count eq 1}">
                                    <td rowspan="${businessCluesDetail.zhongbiaoGoodsList.size()}" style="border-color: black">
                                    <span class="bg-light-blue bt-bg-style bt-smallest"
                                          onclick="viewRemarks('${businessCluesDetail.remarks}')">查看备注</span>
                                        <c:choose>
                                            <c:when test="${businessCluesDetail.infoId ne ''}">
                                                <span class="bg-light-blue bt-bg-style bt-smallest"
                                                      onclick="viewZhaoBiao(${businessCluesDetail.infoId})">查看招投标</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="bg-light-grey bt-bg-style bt-smallest">查看招投标</span>
                                            </c:otherwise>
                                        </c:choose>

                                    </td>
                                </c:if>
                            </tr>
                        </c:forEach>
                    </c:forEach>
                </c:if>
                </tbody>
            </table>
        </div>
    </div>
    <c:if test="${not empty businessCluesDetailList}">
        <tags:page page="${page}"/>
    </c:if>
</div>


<div class="main-container">
    <div class="parts">
        <div class="title-container">
            <div class="table-title ">
                联系信息
            </div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <c:if test="${not empty traderContactList}">
                <c:forEach items="${traderContactList}" var="traderContact" varStatus="status">
                    <tr>
                        <td>联系人${status.count}</td>
                        <td>${traderContact.name}</td>
                        <td>电话</td>
                        <td>
                            <c:if test="${not empty traderContact.telephone}">
                                <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${traderContact.telephone}',${tuokeLabelInfo.traderId},1,6,${tuokeLabelInfo.businessCluesId},${traderContact.traderContactId});"></i>
                            </c:if>
                            <span>${traderContact.telephone}</span>
                        </td>
                        <td>手机</td>
                        <td>
                            <c:if test="${not empty traderContact.mobile}">
                                <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${traderContact.mobile}',${tuokeLabelInfo.traderId},1,6,${tuokeLabelInfo.businessCluesId},${traderContact.traderContactId});"></i>
                            </c:if>
                           <span>${traderContact.mobile}</span>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>

            </tbody>
        </table>

    </div>


    <div class="parts">
        <div class="title-container">
            <div class="table-title ">
                沟通记录
            </div>
            <div class="title-click   pop-new-data"
                 layerParams='{"width":"800px","height":"580px","title":"新增沟通","link":"/business/clues/addCommunicatePage.do?businessCluesId=${tuokeLabelInfo.businessCluesId}&traderId=${tuokeLabelInfo.traderId}&traderType=1"}'>
                新增
            </div>

        </div>
        <table class="table">
            <thead>
            <tr>
                <th class="wid10">沟通时间</th>
                <th class="">录音</th>
                <th>录音内容</th>
                <th class="">联系人</th>
                <th class="">联系方式</th>
                <th class="">沟通方式</th>
                <th class="wid30">沟通内容（AI分析整理）</th>
                <th class="">操作人</th>
                <th class="wid10">下次沟通时间</th>
                <th class="wid15">下次沟通内容</th>
                <th class="">备注</th>
                <th class="wid10">创建时间</th>
                <th class="wid6">操作</th>
            </tr>
            </thead>
            <tbody>
            <c:if test="${not empty communicateList}">
                <c:forEach items="${communicateList}" var="communicateRecord" varStatus="">
                    <tr>
                        <td><date:date value="${communicateRecord.begintime} " />~<date:date value="${communicateRecord.endtime}" format="HH:mm:ss" /></td>
                        <td><c:if test="${not empty communicateRecord.coidUri }">${communicateRecord.communicateRecordId }</c:if></td>
                        <td><c:if test="${not empty communicateRecord.coidUri}">
                            <c:if test="${communicateRecord.isTranslation eq 1}">
									  <span class="edit-user pop-new-data"
                                            layerParams='{"width":"90%","height":"90%","title":"查看详情","link":"${pageContext.request.contextPath}/phoneticTranscription/phonetic/viewContent.do?communicateRecordId=${communicateRecord.communicateRecordId}"}'>查看</span>
                            </c:if>
                            <span class="edit-user"
                                  onclick="playrecord('${communicateRecord.coidUri}');">播放</span>
                        </c:if></td>
                        <td>${communicateRecord.contactName}</td>
                        <td>${communicateRecord.phone}</td>
                        <td>
                            <c:choose>
                                <c:when test="${communicateRecord.coidType == 2}">
                                    呼出
                                </c:when>
                                <c:otherwise>
                                    呼入
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td>
                            <ul class="communicatecontent ml0">
                                <c:if test="${not empty communicateRecord.tag }">
                                    <c:forEach items="${communicateRecord.tag }" var="tag">
                                        <li class="bluetag" title="${tag.tagName}">${tag.tagName}</li>
                                    </c:forEach>
                                </c:if>
                                <c:if test="${empty communicateRecord.tag }">
                                    ${communicateRecord.contactContent}
                                </c:if>
                            </ul>
                            <div style="float: left">${communicateRecord.contentSuffix}</div>
                        </td>
                        <td>${communicateRecord.user.username}</td>
                        <c:choose>
                            <c:when test="${communicateRecord.isDone == 0 }">
                                <td class="font-red">${communicateRecord.nextContactDate }</td>
                            </c:when>
                            <c:otherwise>
                                <td>${communicateRecord.nextContactDate }</td>
                            </c:otherwise>
                        </c:choose>
                        <td>${communicateRecord.nextContactContent}</td>
                        <td>${communicateRecord.comments}</td>
                        <td><date:date value="${communicateRecord.addTime} "/></td>

                        <td class="caozuo">
                            <span class="border-blue pop-new-data"
                                  layerParams='{"width":"60%","height":"70%","title":"编辑沟通记录","link":"./editcommunicate.do?communicateRecordId=${communicateRecord.communicateRecordId}&businessCluesId=${tuokeLabelInfo.businessCluesId}&traderId=${tuokeLabelInfo.traderId}"}'>编辑</span>
                        </td>

                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty communicateList}">
                <tr>
                    <td colspan="13">暂无记录</td>
                </tr>
            </c:if>
            </tbody>
        </table>

    </div>




</div>

<%@ include file="../common/footer.jsp" %>
<script type="text/javascript"
        src="<%=basePath%>/static/js/businessclues/view_business_clues.js?rnd=${resourceVersionKey}"></script>
