package com.vedeng.crm.business.web.controller;

import com.dianping.cat.util.StringUtils;
import com.vedeng.common.core.annotation.MenuDesc;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.crm.shard.service.ShareService;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.service.BusinessLeadsService;
import com.vedeng.erp.common.dto.RSalesJBusinessOrderDto;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo;
import com.vedeng.erp.trader.service.BusinessLeadsApiService;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 线索页面跳转层
 * @date 2022/7/14 10:41
 **/
@ExceptionController
@Controller
@RequestMapping("/crm/businessLeads/profile")
@Slf4j
public class BusinessLeadsController extends BaseController {


    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;

    @Autowired
    private UserApiService userApiService;

    /**
     * 线索列表
     * @return 页面
     */
    @MenuDesc(menuValue = "C01,C02",menuDesc = "线索列表分页查询")
    @RequestMapping(value = "/index")
    public String index(HttpServletRequest request) {
        //request.getSession().getAttribute("current_user");
        return "/vue/view/crm/profile/businessleads/index";
    }


    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;
    /**
     * 新增线索
     * @return 页面
     */
    @MenuDesc(menuValue = "C0101",menuDesc = "新增线索")
    @RequestMapping(value = "/add")
    public ModelAndView add(@RequestParam(required = false) Integer fromType, @RequestParam(required = false) Integer source ,@RequestParam(required = false) Integer traderId) {


        ModelAndView view=  new ModelAndView();
        view.addObject("fromType", fromType);
        view.addObject("source", source);
//        if(fromType != null){
//            view.addObject("sourceId", getRealSource(fromType));//渠道类型
//        }
        if(source != null){
            Integer communicationId = newGetRealSource(source);//渠道ID
            if(communicationId != null && communicationId > 0){
                SysOptionDefinitionDto  sysOptionDefinitionDto =  sysOptionDefinitionApiService.getOptionDefinitionById(communicationId);
                if(sysOptionDefinitionDto !=null){
                    view.addObject("inquiryId",4001);//线索询价行为-即时通讯

                    view.addObject("sourceId", sysOptionDefinitionDto.getParentId());//渠道ID-1
                    view.addObject("communicationId", communicationId);//渠道ID-2
                }

            }

        }
        if(traderId != null && traderId != 0){
            view.addObject("traderId", traderId);
            TraderCustomerInfoVo vo = traderCustomerBaseService.getTraderCustomerInfo(traderId);
            if(vo != null ){
                view.addObject("traderName", vo.getTraderName());
                Integer userId = vo.getSaleId();
                if(userId != null && userId != 0){
                    view.addObject("belongerId", userId);
                    UserDto userDto = userApiService.getUserBaseInfo(userId);
                    view.addObject("belonger", userDto.getUsername());
                    view.addObject("belongerPic", userDto.getAliasHeadPicture());
                }
            }
        }
        view.setViewName("/vue/view/crm/profile/businessleads/add");
        return view;
    }


    /**
     * 新增线索/商机，综合新建页面
     * @return 页面
     */
    @MenuDesc(menuValue = "C0201",menuDesc = "新增线索/商机，综合新建页面")
    @RequestMapping(value = "/addForSale")
    public ModelAndView addForSale(@RequestParam(required = false) Integer fromType, @RequestParam(required = false) Integer source ,@RequestParam(required = false) Integer traderId) {


        ModelAndView view=  new ModelAndView();
        view.addObject("pageType", "1");
        view.addObject("fromType", fromType);
        view.addObject("source", source);
//        if(fromType != null){
//            view.addObject("sourceId", getRealSource(fromType));//渠道类型
//        }
        if(source != null){
            Integer communicationId = newGetRealSource(source);//渠道ID
            if(communicationId != null && communicationId > 0){
                SysOptionDefinitionDto  sysOptionDefinitionDto =  sysOptionDefinitionApiService.getOptionDefinitionById(communicationId);
                if(sysOptionDefinitionDto !=null){
                    view.addObject("inquiryId",4001);//线索询价行为-即时通讯

                    view.addObject("sourceId", sysOptionDefinitionDto.getParentId());//渠道ID-1
                    view.addObject("communicationId", communicationId);//渠道ID-2
                }

            }

        }
        if(traderId != null && traderId != 0){
            view.addObject("traderId", traderId);
            TraderCustomerInfoVo vo = traderCustomerBaseService.getTraderCustomerInfo(traderId);
            if(vo != null ){
                view.addObject("traderName", vo.getTraderName());
                Integer userId = vo.getSaleId();
                if(userId != null && userId != 0){
                    view.addObject("belongerId", userId);
                    UserDto userDto = userApiService.getUserBaseInfo(userId);
                    view.addObject("belonger", userDto.getUsername());
                    view.addObject("belongerPic", userDto.getAliasHeadPicture());
                }
            }
        }
        view.setViewName("/vue/view/crm/profile/businessleads/addForSale");//同样指向到原来新建线索的页面里去
        return view;
    }

    /**
     * 新增线索-商机的起始页
     * @return 页面
     */
    @MenuDesc(menuValue = "C0101,C0201",menuDesc = "新增线索-商机的起始页")
    @RequestMapping(value = "/addLeadAndChance")
    public ModelAndView addLeadAndChance() {
        ModelAndView view=  new ModelAndView();
        view.setViewName("/vue/view/crm/profile/businessleads/addLeadAndChance");
        return view;
    }

    /**
     * 新增线索
     * @return 页面
     */
    @MenuDesc(menuValue = "C0101",menuDesc = "新增线索")
    @RequestMapping(value = "/addSimple")
    public ModelAndView addSimple(@RequestParam(required = false) Integer fromType, @RequestParam(required = false) Integer source ,@RequestParam(required = false) Integer traderId) {


        ModelAndView view=  new ModelAndView();
        view.addObject("fromType", fromType);
        view.addObject("source", source);
//        if(fromType != null){
//            view.addObject("sourceId", getRealSource(fromType));//渠道类型
//        }
        if(source != null){
            Integer communicationId = newGetRealSource(source);//渠道ID
            if(communicationId != null && communicationId > 0){
                SysOptionDefinitionDto  sysOptionDefinitionDto =  sysOptionDefinitionApiService.getOptionDefinitionById(communicationId);
                if(sysOptionDefinitionDto !=null){
                    view.addObject("inquiryId",4001);//线索询价行为-即时通讯

                    view.addObject("sourceId", sysOptionDefinitionDto.getParentId());//渠道ID-1
                    view.addObject("communicationId", communicationId);//渠道ID-2
                }

            }

        }
        if(traderId != null && traderId != 0){
            view.addObject("traderId", traderId);
            TraderCustomerInfoVo vo = traderCustomerBaseService.getTraderCustomerInfo(traderId);
            if(vo != null ){
                view.addObject("traderName", vo.getTraderName());
                Integer userId = vo.getSaleId();
                if(userId != null && userId != 0){
                    view.addObject("belongerId", userId);
                    UserDto userDto = userApiService.getUserBaseInfo(userId);
                    view.addObject("belonger", userDto.getUsername());
                    view.addObject("belongerPic", userDto.getAliasHeadPicture());
                }
            }
        }
        view.setViewName("/vue/view/crm/profile/businessleads/addSimple");
        return view;
    }

    @MenuDesc(menuValue = "C0102",menuDesc = "编辑线索")
    @RequestMapping(value = "/edit")
    public ModelAndView edit(@RequestParam  Integer id,
            @RequestParam(required = false) Integer fromType, @RequestParam(required = false) Integer source ,@RequestParam(required = false) Integer traderId) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        ModelAndView mv = new ModelAndView();
        List<RSalesJBusinessOrderDto> list = shareService.getShareListByBusinessId(id,5);
        String errorMessage = businessLeadsApiService.checkBusinessLeadsIfCurrentCurrentCanSee(id,currentUser.getId(),list);
        if(StringUtils.isNotEmpty(errorMessage)){
            mv.addObject("errorMessage", errorMessage);
            mv.setViewName("/vue/view/crm/noPermission_data");
            return mv;
        }

        ModelAndView view=  new ModelAndView();
        view.addObject("fromType", fromType);
        view.addObject("source", source);
//        if(fromType != null){
//            view.addObject("sourceId", getRealSource(fromType));//渠道类型
//        }
        if(source != null){
            Integer communicationId = newGetRealSource(source);//渠道ID
            if(communicationId != null && communicationId > 0){
                SysOptionDefinitionDto  sysOptionDefinitionDto =  sysOptionDefinitionApiService.getOptionDefinitionById(communicationId);
                if(sysOptionDefinitionDto !=null){
                    view.addObject("inquiryId",4001);//线索询价行为-即时通讯

                    view.addObject("sourceId", sysOptionDefinitionDto.getParentId());//渠道ID-1
                    view.addObject("communicationId", communicationId);//渠道ID-2
                }

            }

        }
        if(traderId != null && traderId != 0){
            view.addObject("traderId", traderId);
            TraderCustomerInfoVo vo = traderCustomerBaseService.getTraderCustomerInfo(traderId);
            if(vo != null ){
                view.addObject("traderName", vo.getTraderName());
                Integer userId = vo.getSaleId();
                if(userId != null && userId != 0){
                    view.addObject("belongerId", userId);
                    UserDto userDto = userApiService.getUserBaseInfo(userId);
                    view.addObject("belonger", userDto.getUsername());
                    view.addObject("belongerPic", userDto.getAliasHeadPicture());
                }
            }
        }
        view.setViewName("/vue/view/crm/profile/businessleads/add");
        return view;
    }

    private Integer getRealSource(Integer source){
        if(ONE.equals(source)){
            return bcSourceBdPc;
        }else if(TWO.equals(source)){
            return bcSourceBdApp;
        }else if(THREE.equals(source)){
            return bcSourceBdM;
        }else{
            return 0;
        }
    }

    private Integer newGetRealSource(Integer source){
        if(ONE.equals(source)){
            return bc_source_bd_pc;
        }else if(TWO.equals(source)){
            return bc_source_bd_app;
        }else if(THREE.equals(source)){
            return bc_source_bd_m;
        }else{
            return 0;
        }
    }



    @Autowired
    private ShareService shareService;

    @Autowired
    private BusinessLeadsApiService businessLeadsApiService;


    /**
     * 线索详情
     * @return 页面
     */
    @RequestMapping(value = "/detail")
    public ModelAndView detail(@RequestParam Integer id) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        ModelAndView mv = new ModelAndView();
        List<RSalesJBusinessOrderDto> list = shareService.getShareListByBusinessId(id,5);
        String errorMessage = businessLeadsApiService.checkBusinessLeadsIfCurrentCurrentCanSee(id,currentUser.getId(),list);
        if(StringUtils.isNotEmpty(errorMessage)){
            mv.addObject("errorMessage", errorMessage);
            mv.setViewName("/vue/view/crm/noPermission_data");
            //return "/vue/view/crm/noPermission_data";
        }else{
            mv.setViewName("/vue/view/crm/profile/businessleads/detail");
        }

        return mv;

        //return ;
    }


    /**
     * @Fields ONE : TODO 1
     */
    public static final Integer ONE = 1;

    /**
     * @Fields TWO : TODO 2
     */
    public static final Integer TWO = 2;
    /**
     * @Fields THREE : TODO 3
     */
    public static final Integer THREE = 3;
    /**
     * @Fields FOUR : TODO 4
     */
    public static final Integer FOUR = 4;
    /**
     * @Fields FIVE : TODO 5
     */
    public static final Integer FIVE = 5;
    /**
     * @Fields FIVE : TODO 5
     */
    public static final Integer SIX = 6;

    public static Integer bc_source_bd_pc = 4059;
    public static Integer bc_source_bd_app = 4061;
    public static Integer bc_source_bd_m = 4060;

    @Value("${bc_source_bd_m}")
    protected Integer bcSourceBdM;
    @Value("${bc_source_bd_pc}")
    protected Integer bcSourceBdPc;
    @Value("${bc_source_bd_app}")
    protected Integer bcSourceBdApp;
    
}
