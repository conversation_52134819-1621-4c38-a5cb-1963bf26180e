package com.vedeng.erp.finance.web.api;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.annotation.NoRepeatSubmit;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationDto;
import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationItemDto;
import com.vedeng.erp.finance.domain.dto.RedConfirmReqApplyRequestDto;
import com.vedeng.erp.finance.dto.RedConfirmReqAuditRequestDto;
import com.vedeng.erp.finance.dto.RedConfirmReqCommonRequestDto;
import com.vedeng.erp.finance.dto.RedConfirmResponseDto;
import com.vedeng.erp.finance.service.InvoiceRedConfirmationService;
import com.vedeng.erp.saleorder.dto.AfterSalesGoodsDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;


/**
 * 确认单相关按钮
 * <AUTHOR>
 */
@ExceptionController
@RestController
@Slf4j
@RequestMapping("/redConfirm/api")
public class RedConfirmApi {

    @Autowired
    InvoiceRedConfirmationService invoiceRedConfirmationService;

    /**
     * 列表查询
     * @param query
     * @return
     */
    @RequestMapping(value = "/page")
    @NoNeedAccessAuthorization
    public R<?> page(@RequestBody PageParam<InvoiceRedConfirmationDto> query) {
        return R.success(invoiceRedConfirmationService.page(query,true));
    }

    /**
     * 列表汇总数量
     * @return
     */
    @RequestMapping(value = "/typeNum")
    @NoNeedAccessAuthorization
    public R<?> typeNum() {
        return R.success(invoiceRedConfirmationService.typeNum());
    }

    /**
     * 确认单申请
     * @param redConfirmReqApplyRequestDto
     * @return
     */
    @RequestMapping("/apply")
    @ResponseBody
    @NoRepeatSubmit
    public R<List<RedConfirmResponseDto>> apply(@RequestBody RedConfirmReqApplyRequestDto redConfirmReqApplyRequestDto) {
        log.info("确认单申请入参：{}", JSONObject.toJSON(redConfirmReqApplyRequestDto));
        List<RedConfirmResponseDto> responseDto = invoiceRedConfirmationService.apply(redConfirmReqApplyRequestDto);
        return R.success(responseDto);
    }

    /**
     * 确认单审核
     * @param requestDtoList
     * @return
     */
    @RequestMapping("/audit")
    @ResponseBody
    @NoRepeatSubmit
    public R<List<RedConfirmResponseDto>> audit(@RequestBody RedConfirmReqAuditRequestDto requestDtoList) {
        List<RedConfirmResponseDto> responseDto = invoiceRedConfirmationService.audit(requestDtoList);
        log.info("确认单审核入参：{}", JSONObject.toJSON(responseDto));
        return R.success(responseDto);
    }


    /**
     * 确认单开票
     * @param requestDto
     * @return
     */
    @RequestMapping("/open")
    @ResponseBody
    @NoRepeatSubmit
    public R<List<RedConfirmResponseDto>> open(@RequestBody RedConfirmReqCommonRequestDto requestDto) {
        log.info("确认单开票入参：{}", JSONObject.toJSON(requestDto));
        List<RedConfirmResponseDto> responseDto = invoiceRedConfirmationService.openInvoice(requestDto);
        return R.success(responseDto);
    }

    /**
     * 确认单撤销
     * @param requestDto
     * @return
     */
    @RequestMapping("/cancel")
    @ResponseBody
    @NoRepeatSubmit
    public R<List<RedConfirmResponseDto>> cancel(@RequestBody RedConfirmReqCommonRequestDto requestDto) {
        log.info("确认单撤销入参：{}", JSONObject.toJSON(requestDto));
        List<RedConfirmResponseDto> responseDto = invoiceRedConfirmationService.cancel(requestDto);
        return R.success(responseDto);
    }

    /**
     * 确认单作废
     * @param requestDto
     * @return
     */
    @RequestMapping("/invalid")
    @ResponseBody
    @NoRepeatSubmit
    public R<List<RedConfirmResponseDto>> invalid(@RequestBody RedConfirmReqCommonRequestDto requestDto) {
        log.info("确认单作废入参：{}", JSONObject.toJSON(requestDto));
        List<RedConfirmResponseDto> responseDto = invoiceRedConfirmationService.invalid(requestDto);
        return R.success(responseDto);
    }

    /**
     * 行更新
     * @param requestDto
     * @return
     */
    @RequestMapping("/updateByDetail")
    @ResponseBody
    @NoRepeatSubmit
    public R<List<RedConfirmResponseDto>> updateByDetail(@RequestBody RedConfirmReqCommonRequestDto requestDto) {
        log.info("确认单行更新入参：{}",JSON.toJSONString(requestDto));
        List<RedConfirmResponseDto> responseDto = invoiceRedConfirmationService.renew(requestDto);
        return R.success(responseDto);
    }


    /**
     * 查询红字明细列表
     *
     * @param invoiceRedConfirmationId 确认单id
     * @return List<AfterSalesGoodsDto>
     */
    @RequestMapping("/getRedInvoiceDetailList")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<List<AfterSalesGoodsDto>> getRedInvoiceDetailList(Integer invoiceRedConfirmationId) {
        return R.success(invoiceRedConfirmationService.getRedDetailList(invoiceRedConfirmationId));
    }

    /**
     * 查询确认单详情信息
     *
     * @param invoiceRedConfirmationId 确认单id
     * @return List<AfterSalesGoodsDto>
     */
    @RequestMapping("/detail")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<InvoiceRedConfirmationDto> getInvoiceRedConfirmationById(Integer invoiceRedConfirmationId) {
        return R.success(invoiceRedConfirmationService.getInvoiceRedConfirmationById(invoiceRedConfirmationId));
    }

    /**
     * 根据蓝票id查询蓝票详情（调用hx调用初始化接口返回后，与erp数据处理过滤后的数据）
     *
     * @param blueInvoiceId 蓝票id
     * @return List<BlueInvoiceDetailDto>
     */
    @RequestMapping(value = "/blueInvoiceInfo", method = RequestMethod.POST)
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<List<InvoiceRedConfirmationItemDto>> getBlueInvoiceDetailList(Integer blueInvoiceId) {
        return R.success(invoiceRedConfirmationService.getBlueInvoiceDetail(blueInvoiceId));
    }

    /**
     * 关联售后红字确认单主体信息
     *
     * @param invoiceRedConfirmationId 红字确认单id
     * @return invoiceRedConfirmationDto
     */
    @RequestMapping("/associateSubjectInfo")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> associateSubjectInfo(Integer invoiceRedConfirmationId) {
        log.info("关联售后红字确认单主体信息入参：{}", invoiceRedConfirmationId);
        return R.success(invoiceRedConfirmationService.associateSubjectInfo(invoiceRedConfirmationId));
    }

    @RequestMapping(value = "/redLetterConfirmInitCheck")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> redLetterConfirmInitCheck(InvoiceRedConfirmationDto initCheckDto) {
        log.info("红字确认单检查初始化 initCheckDto:{}", JSON.toJSONString(initCheckDto));
        if (Objects.isNull(initCheckDto) || Objects.isNull(initCheckDto.getAfterSaleBusinessOrderId()) ||
                Objects.isNull(initCheckDto.getBlueInvoiceId()) || Objects.isNull(initCheckDto.getBusinessOrderId())) {
            return R.error("红字申请参数错误");
        }
        invoiceRedConfirmationService.redLetterConfirmInitCheck(initCheckDto);
        return R.success();
    }

    /**
     * 关联售后提交
     *
     * @param invoiceRedConfirmationDto 红字确认单
     * @return invoiceRedConfirmationDto
     */
    @RequestMapping("/associateAftersale")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> associateAftersale(@RequestBody InvoiceRedConfirmationDto invoiceRedConfirmationDto) {
        log.info("关联售后提交入参：{}", JSON.toJSONString(invoiceRedConfirmationDto));
        if (Objects.isNull(invoiceRedConfirmationDto) || Objects.isNull(invoiceRedConfirmationDto.getInvoiceRedConfirmationId()) ||
                Objects.isNull(invoiceRedConfirmationDto.getBusinessOrderId()) || Objects.isNull(invoiceRedConfirmationDto.getAfterSaleBusinessOrderId())) {
            return R.error("传参异常");
        }
        invoiceRedConfirmationService.associateAftersale(invoiceRedConfirmationDto);
        return R.success();
    }

    /**
     * 根据售后单id查询售后商品明细
     *
     * @param afterSaleId 售后单id
     * @return List<BlueInvoiceDetailDto>
     */
    @RequestMapping(value = "/associateAfterSaleDetailInfo", method = RequestMethod.POST)
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> associateAfterSaleDetailInfo(Integer afterSaleId, Integer saleOrderId, String blueInvoiceNo) {
        log.info("关联售后提交入参:afterSaleId:{},saleOrderId:{},blueInvoiceNo:{}", afterSaleId, saleOrderId, blueInvoiceNo);
        if (Objects.isNull(afterSaleId) || Objects.isNull(saleOrderId) || StrUtil.isBlank(blueInvoiceNo)) {
            return R.error("传参错误");
        }
        return R.success(invoiceRedConfirmationService.associateAfterSaleDetailInfo(afterSaleId, saleOrderId, blueInvoiceNo));
    }

}
