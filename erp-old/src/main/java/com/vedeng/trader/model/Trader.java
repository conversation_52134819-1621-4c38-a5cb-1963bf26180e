package com.vedeng.trader.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class Trader implements Serializable {

    private static final long serialVersionUID = 2202486432851713199L;

    private Integer traderId;

    private Integer companyId;

    private Integer isEnable;

    private String traderName;

    private Integer areaId;

    private String areaIds;

    private String address;

    /**
     * 三证合一
     */
    private Integer threeInOne;

    /**
     * 医疗资质合一
     */
    private Integer medicalQualification;

    private Long addTime;

    private Integer creator;

    private Long modTime;

    private Integer updater;

    private TraderCustomer traderCustomer;

    private TraderSupplier traderSupplier;

    private Integer traderCustomerId, traderSupplierId;

    private TraderFinance traderFinance;

    private Integer traderType;

    /**
     * 修改之前的客户名称
     */
    private String traderNameBefore;

    private Integer openStore;

    private Integer openNum;

    private Integer ordergoodsStoreId;

    /**
     * 表 T_TRADER_CUSTOMER
     * 客户类型
     */
    private Integer customerType;

    /**
     * 客户等级
     */
    private String customerLevelStr;

    /**
     * 客户性质
     */
    private Integer customerNature;
    /**
     * 表: T_TRADER_CONTACT
     */
    private Integer traderContactId;
    private String traderContactName;
    private String traderContactTelephone;
    private String traderContactMobile;

    /**
     * 表: T_TRADER_ADDRESS
     */
    private Integer traderAddressId;
    private String traderAddress;

    /**
     * 来源: 0=ERP 1=耗材商城
     */
    private Integer source;

    /**
     * 耗材商城客户ID
     */
    private Integer accountCompanyId;

    /**
     * 客户资质状态
     */
    private Integer traderStatus;

    /**
     * 客户归属平台
     */
    private Integer belongPlatform;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 代付款证明审核状态
     */
    private Integer payofStatus;

    /**
     * 代付款证明审核备注
     */
    private String payofCheckMsg;

    /**
     * 客户审核备注
     */
    private String traderCheckMsg;

    /**
     * 客户最后一次审核通过时间
     */
    private Date lastValidTime;

    /**
     * 仓库地址id
     */
    private Integer warehouseAreaId;

    /**
     * 仓库地址id集合
     */
    private String warehouseAreaIds;

    /**
     * 仓库的详细地址
     */
    private String warehouseDetailAddress;

    private Integer parentId;

    /**
     * 经销商vue数据
     */
    private String theDataOfThePage;

    /**
     * 经销商是否有效性，和isEnable 不是同一个东西
     */
    private Integer effectiveness;

    private Integer work_zone;

    private Integer isAiAssistant;
    private Integer aiCommunicateRecordId;

    /**
     * 无效原因
     */
    private String invalidReason;

    /**
     * 无效原因-具体原因
     */
    private String otherReason;
 
    public void setTraderName(String traderName) {
        this.traderName = traderName == null ? null : traderName.replaceAll("\\(", "（").replaceAll("\\)", "）").trim();
    }


}
