package com.vedeng.goods.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 新商品流 - 运营信息详情来源枚举类
 * <AUTHOR>
 */
@Getter
public enum OperateInfoSourceEnum {

    /**
     * note:商品类型未知
     */
    UNKNOWN(0, "未知"),

    ERP(1, "ERP富文本"),

    ML(2, "马良");

    /**
     * 来源
     */
    private Integer source;

    /**
     * 描述
     */
    private String desc;

    OperateInfoSourceEnum(Integer source, String desc) {
        this.source = source;
        this.desc = desc;
    }

    /**
     * 根据来源获取枚举值
     *
     * @param source
     * @return
     */
    public static OperateInfoSourceEnum getBySource(Integer source) {
        if (Objects.isNull(source) || source <= 0) {
            return UNKNOWN;
        }

        for (OperateInfoSourceEnum sourceEnum : values()) {
            if (sourceEnum.getSource().equals(source)) {
                return sourceEnum;
            }
        }
        return UNKNOWN;
    }

}
