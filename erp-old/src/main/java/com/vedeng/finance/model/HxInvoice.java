package com.vedeng.finance.model;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 航信发票类
 * <AUTHOR>
 * @date created in 2020/5/18 14:46
 */
@Data
public class HxInvoice {

    private Integer hxInvoiceId;

    /**
     * 发票类型，0销项发票，1进项发票
     */
    private Integer invoiceType;

    /**
     * 发票种类：1专票，0普票
     */
    private String invoiceCategory;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号码
     */
    private String invoiceNum;

    /**
     * 销方税号
     */
    private String salerTaxNum;

    /**
     * 销方名称
     */
    private String salerName;

    /**
     * 销方地址、电话
     */
    private String salerAddress;

    private String salerTel;

    /**
     * 销方开户行
     */
    private String salerBank;

    /**
     * 销方开户行账号
     */
    private String salerBankAccount;

    /**
     * 	购方税号
     */
    private String buyerTaxNum;

    /**
     * 购方名称
     */
    private String buyerName;

    /**
     * 购方地址、电话
     */
    private String buyerAddress;

    private String buyerTel;

    /**
     * 购方开户行
     */
    private String buyerBank;

    /**
     * 购方开户行行号
     */
    private String buyerBankAccount;

    /**
     * 开票时间
     */
    private Long createTime;

    /**
     * 发票金额，发票种类为 01，02，03，14时不可为空，填写发票不含税金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 发票税额
     */
    private BigDecimal taxAmount;

    /**
     * 价税合计
     */
    private BigDecimal amount;

    /**
     * 税率
     */
    private Integer taxRate;

    /**
     * 1蓝字有效、2红字有效、3蓝字作废
     */
    private Integer colorType;

    /**
     * 备注
     */
    private String comment;

    /**
     * 开票人
     */
    private String creator;

    /**
     * 复核人
     */
    private String checker;

    /**
     * 收款人
     */
    private String payee;

    /**
     * 发票图像附件链接
     */
    private String attachment;

    /**
     * 发票状态，1待录票，2审核中，3已审核，4待认领，5费用票，6异常票，7负数票，8其他，9：待退票，10:已退票，11：已录票 12:无效票
     */
    private Integer invoiceStatus;

    /**
     * 发票退票状态，0待退票，1已退票
     */
    private Integer invoiceRefundStatus;

    /**
     * 发票添加时间
     */
    private Long addTime;

    /**
     * 发票更新时间
     */
    private Long updateTime;

    /**
     * 更新者
     */
    private Integer updater;

    private Integer traderId;

    /**
     * 是否已认证
     */
    private Integer isAuth;

    /**
     * 是否正在认证
     */
    private Integer isAuthing;

    /**
     * 认证时间
     */
    private Long authTime;


    /**
     * 认证失败原因
     */
    private String authFailReason;


    /**
     * 认证方式  0 线下认证  1 接口认证
     */
    private Integer authMode;

}
