package com.vedeng.erp.buyorder.bizProcessor.core.ext;

import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.erp.buyorder.bizProcessor.dto.BizDto;
import com.vedeng.erp.buyorder.bizProcessor.core.OrderCore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class OverBuyordeChangeAfterSalesCore extends OrderCore {

    @Autowired
    AfterSalesMapper afterSalesMapper;

    @Override
    public void doCoreBiz(BizDto bizDto) {
        //将售后单的状态改成已完结
        AfterSalesVo afterSalesVo = (AfterSalesVo) bizDto.getBizData();
        afterSalesMapper.updateByPrimaryKeySelective(afterSalesVo);
    }
}
