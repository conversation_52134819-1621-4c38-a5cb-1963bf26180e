package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.domain.PublicCustomerCalculateRules;
import com.vedeng.erp.trader.domain.dto.PublicCustomerCalculateRulesChangeDto;
import com.vedeng.erp.trader.domain.vo.PublicCustomerCalculateRulesDetailVo;
import com.vedeng.erp.trader.mapper.PublicCustomerCalculateRulesMapper;
import com.vedeng.erp.trader.service.PublicCustomerCalculateRulesService;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.trader.service.impl
 * @Date 2022/2/16 14:30
 */
@Service
public class PublicCustomerCalculateRulesServiceImpl implements PublicCustomerCalculateRulesService {

    @Resource
    private PublicCustomerCalculateRulesMapper publicCustomerCalculateRulesMapper;

    @Resource
    private UserApiService userApiService;

    @Override
    public PublicCustomerCalculateRulesDetailVo getRulesDetail() {
        PublicCustomerCalculateRulesDetailVo publicCustomerCalculateRulesDetailVo = new PublicCustomerCalculateRulesDetailVo();
        List<PublicCustomerCalculateRules> allRules = publicCustomerCalculateRulesMapper.getAllRules();

        // 最新的一条即为当前生效的
        publicCustomerCalculateRulesDetailVo.setPublicCustomerCalculateRules(allRules.get(0));

        // 如果记录大于1，则两两分组拼接为修改记录(只循环到 倒数第二个)
        if (allRules.size() > 1) {
            List<PublicCustomerCalculateRulesChangeDto> publicCustomerCalculateRulesChangeDtoList = new ArrayList<>();
            for (int i = 0; i < allRules.size() - 1; i++) {
                PublicCustomerCalculateRulesChangeDto changeRecord = new PublicCustomerCalculateRulesChangeDto();
                PublicCustomerCalculateRules newer = allRules.get(i);
                BeanUtil.copyProperties(newer, changeRecord);
                changeRecord.setCreatorName(userApiService.getUserById(newer.getCreator()).getUsername());
                PublicCustomerCalculateRules older = allRules.get(i + 1);

                // 只展示改变的规则
                if (!newer.getCustomerCreatedDays().equals(older.getCustomerCreatedDays())) {
                    changeRecord.setOldCustomerCreatedDays(older.getCustomerCreatedDays());
                }

                if ((!newer.getValidOrderDays().equals(older.getValidOrderDays())) || (!newer.getValidOrderCount().equals(older.getValidOrderCount()))) {
                    changeRecord.setOldValidOrderCalculator(older.getValidOrderCalculator());
                    changeRecord.setOldValidOrderCount(older.getValidOrderCount());
                    changeRecord.setOldValidOrderDays(older.getValidOrderDays());
                }

                if ((!newer.getCommunicationCount().equals(older.getCommunicationCount())) || (!newer.getCommunicationDays().equals(older.getCommunicationDays()))) {
                    changeRecord.setOldCommunicationCalculator(older.getCommunicationCalculator());
                    changeRecord.setOldCommunicationCount(older.getCommunicationCount());
                    changeRecord.setOldCommunicationDays(older.getCommunicationDays());
                }
                
                if (!newer.getLockProtectDays().equals(older.getLockProtectDays())){
                    changeRecord.setOldLockProtectDays(older.getLockProtectDays());
                }

                publicCustomerCalculateRulesChangeDtoList.add(changeRecord);
            }
            publicCustomerCalculateRulesDetailVo.setPublicCustomerCalculateRulesChangeDtoList(publicCustomerCalculateRulesChangeDtoList);
        }

        return publicCustomerCalculateRulesDetailVo;
    }

    @Override
    public int saveCalculateRule(PublicCustomerCalculateRules publicCustomerCalculateRules) {
        // 需求变更: 运算符不再区分小于等于，这边统一设置为1
        publicCustomerCalculateRules.setCommunicationCalculator(1);
        publicCustomerCalculateRules.setValidOrderCalculator(1);

        // 若当次提交内容和最新生效的一条数据相比没有变化，则返回0
        PublicCustomerCalculateRules recent = publicCustomerCalculateRulesMapper.getRecentlyPublicCustomerCalculateRule();
        // 转化为json字符串比较，因此先将id登置为一样的
        recent.setPublicCustomerCalculateRulesId(null);
        recent.setAddTime(publicCustomerCalculateRules.getAddTime());
        recent.setCreator(publicCustomerCalculateRules.getCreator());
        recent.setValidOrderCalculator(1);
        recent.setCommunicationCalculator(1);
        return JSONObject.fromObject(publicCustomerCalculateRules).toString().equals(JSONObject.fromObject(recent).toString()) ? 0 : publicCustomerCalculateRulesMapper.insert(publicCustomerCalculateRules);
    }
}
