<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="商品分类" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<div class="main-container">
    <div class='form-blanks'>
        <input type="text" value="${keyWords}" id="sousuo" style="margin-left: 5px;width: 350px;margin-bottom: 5px" class="input-middle" name="searchData" placeholder="请输入产品名称/品牌/型号等关键词">
        <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="sea(${thirdCategoryId});" id="searchSpan">搜索</span>
    </div>
    <c:forEach var="firstAndSecondCategoryInfo" items="${firstAndSecondCategoryInfoList}">
        <ul style="margin-top: 10px">
            <li style="height: 25px">
                <input type="radio" name="choiceCategoryId" value="${firstAndSecondCategoryInfo.firstCategoryId},${firstAndSecondCategoryInfo.secondCategoryId}" style="margin-top: 7px;margin-right: 15px">${firstAndSecondCategoryInfo.firstCategoryName}->${firstAndSecondCategoryInfo.secondCategoryName}
            </li>
        </ul>
    </c:forEach>

    <div class="add-tijiao tcenter" style="margin-top: 15px;margin-bottom: 2px">
        <button style="background-color: #00b7ee" onclick="qd(${thirdCategoryId})">确定</button>
        <button style="background-color: #00b7ee" type="button" onclick="quxiao()">取消</button>
    </div>
</div>


<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/pages/goodsdribute/choiceCategory.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>
