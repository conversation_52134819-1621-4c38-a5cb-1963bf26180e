package com.vedeng.goods.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public enum RegularMaintainTypeEnum {

    NONE(0, "不维护"),

    GENERAL(1, "一般维护"),

    PERIODIC(2, "定期维护");


    private Integer type;
    private String message;

    private static final List<Integer> NEED_REASON_ITEM_TYPES = Collections.unmodifiableList(Arrays.asList(GENERAL.getType(),PERIODIC.getType()));

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    RegularMaintainTypeEnum(Integer type, String message) {
        this.type = type;
        this.message = message;
    }

    public static String getMessageByType(Integer type) {
        return Arrays.stream(values())
                .filter(item -> item.getType().equals(type))
                .map(item -> item.getMessage())
                .findFirst()
                .orElse("");
    }


    /**
     * 是否需要填写定期维护原因
     *
     * @param type
     * @return
     */
    public static boolean hasRegularMaintainReason(Integer type) {
        return type !=null && NEED_REASON_ITEM_TYPES.contains(type);
    }
}