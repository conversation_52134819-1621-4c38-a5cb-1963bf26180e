package com.wms.service.validator;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.constant.AfterSaleStatusEnum;
import com.vedeng.order.api.constant.DeliveryDirectEnum;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.wms.model.dto.ValidatorResult;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 采购售后直发校验
 * VEDENG-12080
 */
@Service
public class PurchaseAfterSaleCloseValidator extends AbstractValidator{

    public static Logger logger = LoggerFactory.getLogger(PurchaseAfterSaleCloseValidator.class);

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Override
    protected boolean needToValidate(Object[] requestData) {
        AfterSalesVo afterSalesVo = (AfterSalesVo) requestData[0];
        // 546 采购订单退货
        // 547 采购订单换货
        return afterSalesVo.getSubjectType() == 536 && (afterSalesVo.getType() == 546 || afterSalesVo.getType() == 547);
    }

    /**
     * 执行校验
     * @param requestData
     * @return
     * @throws Exception
     */
    @Override
    protected ValidatorResult doValidator(Object[] requestData) throws Exception {
        AfterSalesVo afterSaleDB = (AfterSalesVo) requestData[0];
        Optional.ofNullable(afterSaleDB).map(AfterSalesVo::getAtferSalesStatus).orElseThrow(() -> new Exception("关闭售后单-售后状态字段为空"));
        Optional.ofNullable(afterSaleDB).map(AfterSalesVo::getAfterSalesId).orElseThrow(() -> new Exception("关闭售后单-售后ID字段为空"));

        boolean isClose = isClose(afterSaleDB);

        List<AfterSalesGoods> afterSalesGoodsList = afterSalesGoodsMapper.getAfterSalesGoodsByAfterSalesId(afterSaleDB.getAfterSalesId());
        if (CollectionUtils.isEmpty(afterSalesGoodsList)){
            logger.error("关闭采购售后单未查询到退换货信息：售后单号：{}",afterSaleDB.getAfterSalesNo());
            return ValidatorResult.newBuild().setMessage("未查询到采购售后商品信息，采购售后单不得关闭！");
        }
        // 过滤出直发售后商品
        List<AfterSalesGoods> directDeliveryList = afterSalesGoodsList.stream().filter(afterSalesGoods -> DeliveryDirectEnum.DIRECT_DELIVERY.getDeliveryDirect().equals(afterSalesGoods.getDeliveryDirect())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(directDeliveryList) ){
            // 首先判断其售后商品收货状态，
            List<BuyorderGoodsVo> buyorderGoodsVos = buyorderGoodsMapper.queryBuyorderGoodListByBuyorderId(afterSaleDB.getOrderId());
            if (CollUtil.isNotEmpty(buyorderGoodsVos)) {
                Map<Integer, BuyorderGoodsVo> collect = buyorderGoodsVos.stream().collect(Collectors.toMap(BuyorderGoodsVo::getBuyorderGoodsId, c -> c, (k1, k2) -> k1));
                for (AfterSalesGoods afterSalesGood : directDeliveryList) {
                    BuyorderGoodsVo buyorderGoodsVo = collect.get(afterSalesGood.getOrderDetailId());
                    if (Objects.nonNull(buyorderGoodsVo)) {
                        //若售后数量大于采购数量减去已收货数量的值
                        //继续判断其关联的销售售后单状态是否未关闭状态，非关闭状态下不可关闭该采购售后单，
                        int arrivalNum = buyorderGoodsVo.getArrivalNum() == null ? 0 : buyorderGoodsVo.getArrivalNum();
                        int checkNum = buyorderGoodsVo.getNum() - arrivalNum;
                        if (afterSalesGood.getNum() > checkNum) {
                            if (!isClose) {
                                logger.info("此为直发，销售售后单未关闭，采购售后单不得关闭！售后单号ID：{}", afterSaleDB.getOrderId());
                                return ValidatorResult.newBuild().setMessage("此为直发，销售售后单未关闭，采购售后单不得关闭！");
                            }
                        }
                    }
                }
            }

        }
        return ValidatorResult.newBuild().setResult(true);
    }

    private boolean isClose(AfterSalesVo afterSaleDB){
        AfterSales afterSalesById = afterSalesMapper.getAfterSalesById(afterSaleDB.getAfterSalesId());
        Integer deliveryDirectAfterSalesId = afterSalesById.getDeliveryDirectAfterSalesId();
        if(Objects.isNull(deliveryDirectAfterSalesId) || deliveryDirectAfterSalesId == 0){
            logger.info("{}直发采购售后关联销售售后ID为 空，允许关闭",afterSaleDB.getAfterSalesNo());
            return true;
        }

        AfterSales afterSalesById1 = afterSalesMapper.getAfterSalesById(deliveryDirectAfterSalesId);
        if (Objects.isNull(afterSalesById1)){
            logger.info("{}直发采购售后关联销售售后单不存在，允许关闭",afterSaleDB.getAfterSalesNo());
            return true;
        }
        return AfterSaleStatusEnum.CLOSED.getStatus().equals(afterSalesById1.getAtferSalesStatus());
    }

}
