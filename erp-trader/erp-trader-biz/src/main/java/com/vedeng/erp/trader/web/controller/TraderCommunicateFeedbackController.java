package com.vedeng.erp.trader.web.controller;


import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: traderCommunicateFeedback
 * @date 2024/1/22 14:33
 */
@Controller
@RequestMapping("/traderCommunicateFeedback")
public class TraderCommunicateFeedbackController extends BaseController {

    @RequestMapping(value = "/index")
    @NoNeedAccessAuthorization
    public ModelAndView index(@RequestParam("traderId") Integer traderId,
                              @RequestParam("traderCustomerId") Integer traderCustomerId) {
        ModelAndView view = new ModelAndView("vue/view/tradercommunicatefeedback/index");
        view.addObject("traderId", traderId);
        view.addObject("traderCustomerId", traderCustomerId);
        return view;
    }
}
