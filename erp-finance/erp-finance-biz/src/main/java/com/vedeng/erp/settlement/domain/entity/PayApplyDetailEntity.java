package com.vedeng.erp.settlement.domain.entity;

import java.math.BigDecimal;
import lombok.Data;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/11/4 15:43
 **/
/**
    * 付款申请详情
    */
@Data
public class PayApplyDetailEntity {

    private Integer payApplyDetailId;

    private Integer payApplyId;

    /**
    * 订单详情ID
    */
    private Integer detailgoodsId;

    private BigDecimal price;

    private BigDecimal num;

    private BigDecimal totalAmount;
}