package com.vedeng.docSync.model.pojo.generate;

import java.io.Serializable;
import lombok.Data;

/**
 * T_DOC_BUZ_TAG_MAINTAIN_LOG
 * <AUTHOR>
@Data
public class DocBuzTagMaintainLogDo implements Serializable {
    private Integer id;

    /**
     * 业务类型，1：商品资料，2：厂商资质
     */
    private Integer buzType;

    /**
     * 业务ID
     */
    private Integer buzId;

    /**
     * 业务的标签
     */
    private Integer buzTag;

    /**
     * 操作类型，1：新增，2：删除
     */
    private Integer operateType;

    /**
     * 维护的业务标签的文件ID
     */
    private Integer buzTagJFileId;

    /**
     * 操作人
     */
    private Integer operator;

    /**
     * 操作时间
     */
    private Long operateTime;

    private static final long serialVersionUID = 1L;
}