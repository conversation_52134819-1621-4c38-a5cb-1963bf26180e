package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.InvoiceApplyDetailXhEntity;

import java.util.List;

public interface InvoiceApplyDetailXhMapper {
    int deleteByPrimaryKey(Long invoiceApplyDetailXhId);

    int insert(InvoiceApplyDetailXhEntity record);

    int insertSelective(InvoiceApplyDetailXhEntity record);

    InvoiceApplyDetailXhEntity selectByPrimaryKey(Long invoiceApplyDetailXhId);
    
    List<InvoiceApplyDetailXhEntity> selectByApplyId(Integer invoiceApplyDetailId);

    int updateByPrimaryKeySelective(InvoiceApplyDetailXhEntity record);

    int updateByPrimaryKey(InvoiceApplyDetailXhEntity record);
}
