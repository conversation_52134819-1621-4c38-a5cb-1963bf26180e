package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.batch.common.enums.WarehouseGoodsInEnum;
import com.vedeng.erp.kingdee.batch.dto.*;

import java.util.List;
import java.util.Map;

/**
 * 入库服务类
 */
public interface KingDeeWarehouseGoodsInService {

    /**
	 * 直发物流入库单操作
	 *
	 * @param user              用户信息
	 * @param pkId              采购订单为 T_BUYORDER 主键buyOrderId 售后订单为 T_AFTER_SALES 主键AFTER_SALES_ID
	 * @param goodsIdAndSendNum byOrderGoodsId 为T_BUYORDER_GOODS中的主键或者为T_AFTER_SALES_GOODS中的主键 ，sendNum 为发货数量
	 * @param purchaseIn        参考 WarehouseGoodsInEnum 枚举
	 * @return Map<Integer, Boolean> key 为采购商品ID，value 为是否拆行
	 */
	Map<Integer, Boolean> insertWarehouseGoodsPurchaseDirectOutInDirect(BatchUserDto user, Integer pkId, List<Map<String, Object>> goodsIdAndSendNum,
													   WarehouseGoodsInEnum purchaseIn, BatchExpressDto express) throws Exception;
	
	/**
	 * 生成入库验收报告
	 */
	void createWarehouseGoodsInReport(BatchWarehouseGoodsOutInDto warehouse, List<BatchWarehouseGoodsOutInItemDto> warehouseGoodsOutInItemList);

	/**
	 * 获取商品明细信息
	 */
	void querySkuInfo(BatchOutInDetailDto skuInfo, Integer skuId);

}
