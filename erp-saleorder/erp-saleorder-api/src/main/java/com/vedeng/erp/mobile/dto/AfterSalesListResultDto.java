package com.vedeng.erp.mobile.dto;

import com.vedeng.erp.aftersale.dto.AfterBuyorderGoodsDto;
import com.vedeng.erp.aftersale.dto.ReturnBuyorderInvoiceDto;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.infrastructure.file.domain.Attachment;
import lombok.Data;

import java.util.List;

/**
 * 售后api返回dto
 */
@Data
public class AfterSalesListResultDto {
    /**
     * 售后id
     */
    private Integer afterSalesId;
    /**
     * 售后单号
     */
    private String afterSalesNo;
    /**
     * 售后类型
     */
    private Integer type;
    /**
     * 售后类型名称
     */
    private String typeName;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单id
     */
    private Integer orderId;
    /**
     * 售后状态0待确认1进行中2已完结3已关闭
     */
    private Integer afterSalesStatus;
    /**
     * 创建时间
     */
    private String addTime;
    /**
     * 售后原因
     */
    private Integer reason;
    /**
     * 售后原因名称
     */
    private String reasonName;

    /**
     * 售后商品集合
     */
    private List<AfterSalesGoodsListResultDto> afterSalesGoodsListResultDtoList;

}
