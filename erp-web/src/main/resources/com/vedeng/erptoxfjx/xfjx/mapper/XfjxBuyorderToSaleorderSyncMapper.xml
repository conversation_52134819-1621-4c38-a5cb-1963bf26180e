<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erptoxfjx.xfjx.mapper.XfjxBuyorderToSaleorderSyncMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erptoxfjx.xfjx.domain.XfjxBuyorderToSaleorderSync">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="SYNC_DIRECT_TO" jdbcType="INTEGER" property="syncDirectTo" />
    <result column="BUYORDER_NO" jdbcType="VARCHAR" property="buyorderNo" />
    <result column="SYNC_STATUS" jdbcType="INTEGER" property="syncStatus" />
    <result column="BD_SALEORDER_NO" jdbcType="VARCHAR" property="bdSaleorderNo" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    ID, SYNC_DIRECT_TO, BUYORDER_NO, SYNC_STATUS, BD_SALEORDER_NO, ADD_TIME, UPDATE_TIME
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.erptoxfjx.xfjx.domain.XfjxBuyorderToSaleorderSyncExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_buyorder_to_saleorder_sync
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from t_buyorder_to_saleorder_sync
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_buyorder_to_saleorder_sync
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.erptoxfjx.xfjx.domain.XfjxBuyorderToSaleorderSyncExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from t_buyorder_to_saleorder_sync
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.erptoxfjx.xfjx.domain.XfjxBuyorderToSaleorderSync">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_buyorder_to_saleorder_sync (SYNC_DIRECT_TO, BUYORDER_NO, SYNC_STATUS, 
      BD_SALEORDER_NO, ADD_TIME, UPDATE_TIME)
    values (#{syncDirectTo,jdbcType=INTEGER}, #{buyorderNo,jdbcType=VARCHAR}, #{syncStatus,jdbcType=INTEGER}, 
      #{bdSaleorderNo,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erptoxfjx.xfjx.domain.XfjxBuyorderToSaleorderSync">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_buyorder_to_saleorder_sync
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="syncDirectTo != null">
        SYNC_DIRECT_TO,
      </if>
      <if test="buyorderNo != null">
        BUYORDER_NO,
      </if>
      <if test="syncStatus != null">
        SYNC_STATUS,
      </if>
      <if test="bdSaleorderNo != null">
        BD_SALEORDER_NO,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="syncDirectTo != null">
        #{syncDirectTo,jdbcType=INTEGER},
      </if>
      <if test="buyorderNo != null">
        #{buyorderNo,jdbcType=VARCHAR},
      </if>
      <if test="syncStatus != null">
        #{syncStatus,jdbcType=INTEGER},
      </if>
      <if test="bdSaleorderNo != null">
        #{bdSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.erptoxfjx.xfjx.domain.XfjxBuyorderToSaleorderSyncExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from t_buyorder_to_saleorder_sync
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_buyorder_to_saleorder_sync
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.syncDirectTo != null">
        SYNC_DIRECT_TO = #{record.syncDirectTo,jdbcType=INTEGER},
      </if>
      <if test="record.buyorderNo != null">
        BUYORDER_NO = #{record.buyorderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.syncStatus != null">
        SYNC_STATUS = #{record.syncStatus,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_buyorder_to_saleorder_sync
    set ID = #{record.id,jdbcType=INTEGER},
      SYNC_DIRECT_TO = #{record.syncDirectTo,jdbcType=INTEGER},
      BUYORDER_NO = #{record.buyorderNo,jdbcType=VARCHAR},
      SYNC_STATUS = #{record.syncStatus,jdbcType=INTEGER},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erptoxfjx.xfjx.domain.XfjxBuyorderToSaleorderSync">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_buyorder_to_saleorder_sync
    <set>
      <if test="syncDirectTo != null">
        SYNC_DIRECT_TO = #{syncDirectTo,jdbcType=INTEGER},
      </if>
      <if test="buyorderNo != null">
        BUYORDER_NO = #{buyorderNo,jdbcType=VARCHAR},
      </if>
      <if test="syncStatus != null">
        SYNC_STATUS = #{syncStatus,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erptoxfjx.xfjx.domain.XfjxBuyorderToSaleorderSync">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update t_buyorder_to_saleorder_sync
    set SYNC_DIRECT_TO = #{syncDirectTo,jdbcType=INTEGER},
      BUYORDER_NO = #{buyorderNo,jdbcType=VARCHAR},
      SYNC_STATUS = #{syncStatus,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=INTEGER}
  </update>
</mapper>