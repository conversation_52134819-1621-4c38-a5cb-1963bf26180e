package com.vedeng.goods.manager.validator.InvalidatorChain.spu.Invalid;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.goods.model.CoreSpu;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.SpuNoNotValidInvalidatorError;

@Service
public class SpuNotValidInvalidator extends GoodsInvalidatorChain {

    @Resource
    CoreSkuMapper coreSkuMapper;

    @Resource
    CoreSpuMapper coreSpuMapper;

    @Override
    public void doInvalid() {
        String spuNo = getSpuNo();
        List<String> skuNoList = getSkuNoList();
        List<CoreSpu> spuList = coreSpuMapper.getValidingSpuInfoBySpuNo(spuNo);
        List<CoreSku> validingSkuInfoListByNo = null;
        if(CollectionUtils.isNotEmpty(skuNoList)){
            validingSkuInfoListByNo = coreSkuMapper.getValidingSkuInfoListByNo(skuNoList);
        }

        if(CollectionUtils.isNotEmpty(spuList) || CollectionUtils.isNotEmpty(validingSkuInfoListByNo)){
            throw new InvalidatorChainException(SpuNoNotValidInvalidatorError.getCode(),SpuNoNotValidInvalidatorError.getMessage());
        }
    }
}
