@import (less) '../common.css';

.vd-ui-phone-related {
    position: relative;
    display: flex;

    > .vd-ui-input {
        width: 300px;
    }

    > .vd-ui-search-related {
        max-height: 338px;
        overflow-y: auto;
        border-radius: 3px;
        background: #fff;
        border: solid 1px #BABFC2;
        box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
        position: absolute;
        top: 30px;
        z-index: 9;
        .scrollbar();

        .loading {
            color: #666;
            height: 39px;
            line-height: 37px;
            padding: 0px 10px;
            overflow: hidden;
            i {
                animation: loading 1.8s linear  infinite;
                display: inline-block;
                position: relative;
                top: 1px;
                font-size: 16px;
                margin-right: 5px;
                color: #09F;
            }
        }
        .failed-li {
            height: 39px;
            line-height: 39px;
            padding: 0px 10px;
            i {
                position: relative;
                top: 2px;
                font-size: 16px;
                color: #E64545;
                margin-right: 5px;
            }
            .reload {
                color: #09f;
                cursor: pointer;
            }
        }
        .empty-li {
            height: 39px;
            line-height: 39px;
            padding: 0px 10px;
            text-align: center;
            color: #999;
        }

        .search-list {
            padding: 5px 10px;

            .sr-item {
                padding: 6px 0;
                display: flex;
                justify-content: space-between;
                cursor: pointer;

                .name {
                    flex: 1;
                    min-width: 0;
                    margin-right: 10px;
                }
                .mobile {
                    width: 120px;
                    flex-shrink: 0;
                    text-align: right;
                    color: #999;
                }

                &:hover {
                    .name {
                        color: #f60;
                    }
                    .mobile {
                        color: #f60;
                    }
                }
            }
            
        }
    }

}
