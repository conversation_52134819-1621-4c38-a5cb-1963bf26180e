package com.vedeng.goods.dao;

import com.vedeng.goods.model.CoreOperateInfoGenerate;
import com.vedeng.goods.model.CoreOperateInfoGenerateExample;
import com.vedeng.goods.model.vo.CoreOperateInfoGenerateVo;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;

@Named("coreOperateInfoGenerateMapper")
public interface CoreOperateInfoGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_OPERATE_INFO
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int countByExample(CoreOperateInfoGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_OPERATE_INFO
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int deleteByExample(CoreOperateInfoGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_OPERATE_INFO
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int deleteByPrimaryKey(Integer operateInfoId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_OPERATE_INFO
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int insert(CoreOperateInfoGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_OPERATE_INFO
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int insertSelective(CoreOperateInfoGenerateVo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_OPERATE_INFO
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    List<CoreOperateInfoGenerate> selectByExampleWithBLOBs(CoreOperateInfoGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_OPERATE_INFO
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    List<CoreOperateInfoGenerate> selectByExample(CoreOperateInfoGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_OPERATE_INFO
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    CoreOperateInfoGenerate selectByPrimaryKey(Integer operateInfoId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_OPERATE_INFO
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int updateByExampleSelective(@Param("record") CoreOperateInfoGenerate record, @Param("example") CoreOperateInfoGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_OPERATE_INFO
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int updateByExampleWithBLOBs(@Param("record") CoreOperateInfoGenerate record, @Param("example") CoreOperateInfoGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_OPERATE_INFO
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int updateByExample(@Param("record") CoreOperateInfoGenerate record, @Param("example") CoreOperateInfoGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_OPERATE_INFO
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int updateByPrimaryKeySelective(CoreOperateInfoGenerateVo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_OPERATE_INFO
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int updateByPrimaryKeyWithBLOBs(CoreOperateInfoGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_OPERATE_INFO
     *
     * @mbggenerated Wed Jun 05 11:49:39 CST 2019
     */
    int updateByPrimaryKey(CoreOperateInfoGenerate record);

    /**
     * 功能描述: 根据skuId或spuId验证 是否已存在运营信息
     * @param: [record]
     * @return: CoreOperateInfoGenerate
     * @auther: duke.li
     * @date: 2019/8/21 15:58
     */
    CoreOperateInfoGenerate selectCoreOperateInfo(CoreOperateInfoGenerate record);

    /**
     * 更新seokeywords
     * @param seoKeyWords
     * @param skuId
     * @return
     */
    int updateSeoKeyWords(@Param("seoKeyWords") String seoKeyWords, @Param("skuId")Integer skuId);
}