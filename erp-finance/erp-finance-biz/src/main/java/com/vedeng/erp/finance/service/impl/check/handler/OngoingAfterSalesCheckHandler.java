package com.vedeng.erp.finance.service.impl.check.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.finance.service.AbstractCheckHandler;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.enums.CheckHandlerEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 进行中售后单校验
 */
@Service
@Slf4j
public class OngoingAfterSalesCheckHandler extends AbstractCheckHandler {

    @Autowired
    private AfterSalesApiService afterSalesApiService;

    @Override
    public void handleSalesCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("进行中售后单校验-销售,invoiceCheckRequestDto:{}", invoiceCheckRequestDto);
        List<AfterSalesDto> afterSalesDtoList = afterSalesApiService.getOngoingAfterSalesByOrderId(invoiceCheckRequestDto.getRelatedId());
        if(CollUtil.isNotEmpty(afterSalesDtoList)){
            AfterSalesDto afterSalesDto = afterSalesDtoList.get(0);
            String afterSalesNo = afterSalesDto.getAfterSalesNo();
            Integer type = afterSalesDto.getType();
            String typeStr = getTypeStr(type);
            CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
            String promptText = StrUtil.format(checkHandlerEnum.getPromptText(), typeStr, afterSalesNo);
            buildResult(invoiceCheckResultDto, promptText);
        }
    }

    @Override
    public void handleAfterCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
    }

    private static String getTypeStr(Integer type) {
        String typeStr;
        switch (type) {
            case 539:
                typeStr = "销售退货";
                break;
            case 540:
                typeStr = "销售换货";
                break;
            case 542:
                typeStr = "销售退票";
                break;
            case 543:
                typeStr = "销售退款";
                break;
            default:
                typeStr = "";
                break;
        }
        return typeStr;
    }

}
