<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.CostCategoryMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.CostCategoryEntity">
    <!--@mbg.generated-->
    <!--@Table T_SYS_COST_CATEGORY-->
    <id column="COST_CATEGORY_ID" jdbcType="INTEGER" property="costCategoryId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="CATEGORY_NAME" jdbcType="VARCHAR" property="categoryName" />
    <result column="OLD_CATEGORY_NAME" jdbcType="VARCHAR" property="oldCategoryName" />
    <result column="IS_DEL" jdbcType="BOOLEAN" property="isDel" />
    <result column="UNIT_KING_DEE_NO" jdbcType="VARCHAR" property="unitKingDeeNo" />
    <result column="OLD_UNIT_KING_DEE_NAME" jdbcType="VARCHAR" property="oldUnitKingDeeName" />
    <result column="IS_NEED_PURCHASE" jdbcType="INTEGER" property="purchase" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    COST_CATEGORY_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    CATEGORY_NAME, OLD_CATEGORY_NAME, IS_DEL, UNIT_KING_DEE_NO,OLD_UNIT_KING_DEE_NAME,IS_NEED_PURCHASE as purchase
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultType="com.vedeng.erp.system.domain.dto.CostCategoryDto">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_SYS_COST_CATEGORY
    where COST_CATEGORY_ID = #{costCategoryId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_SYS_COST_CATEGORY
    where COST_CATEGORY_ID = #{costCategoryId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="COST_CATEGORY_ID" keyProperty="costCategoryId" parameterType="com.vedeng.erp.system.domain.entity.CostCategoryEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SYS_COST_CATEGORY (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      CATEGORY_NAME, OLD_CATEGORY_NAME, IS_DEL,UNIT_KING_DEE_NO,OLD_UNIT_KING_DEE_NAME,IS_NEED_PURCHASE
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{categoryName,jdbcType=VARCHAR}, #{oldCategoryName,jdbcType=VARCHAR}, #{isDel,jdbcType=BOOLEAN},
      #{unitKingDeeNo,jdbcType=VARCHAR},#{oldUnitKingDeeName,jdbcType=VARCHAR},#{purchase,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="COST_CATEGORY_ID" keyProperty="costCategoryId" parameterType="com.vedeng.erp.system.domain.entity.CostCategoryEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SYS_COST_CATEGORY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="categoryName != null">
        CATEGORY_NAME,
      </if>
      <if test="oldCategoryName != null">
        OLD_CATEGORY_NAME,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="unitKingDeeNo != null">
        UNIT_KING_DEE_NO,
      </if>
      <if test="oldUnitKingDeeName != null">
        OLD_UNIT_KING_DEE_NAME,
      </if>
      <if test="purchase != null">
        IS_NEED_PURCHASE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="oldCategoryName != null">
        #{oldCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=BOOLEAN},
      </if>
      <if test="unitKingDeeNo != null">
        #{unitKingDeeNo,jdbcType=VARCHAR},
      </if>
      <if test="oldUnitKingDeeName != null">
        #{oldUnitKingDeeName,jdbcType=VARCHAR},
      </if>
      <if test="purchase != null">
        #{purchase,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.CostCategoryEntity">
    <!--@mbg.generated-->
    update T_SYS_COST_CATEGORY
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        CATEGORY_NAME = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="oldCategoryName != null">
        OLD_CATEGORY_NAME = #{oldCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=BOOLEAN},
      </if>
      <if test="unitKingDeeNo != null">
        UNIT_KING_DEE_NO = #{unitKingDeeNo,jdbcType=VARCHAR},
      </if>
      <if test="oldUnitKingDeeName != null">
        OLD_UNIT_KING_DEE_NAME = #{oldUnitKingDeeName,jdbcType=VARCHAR},
      </if>
      <if test="purchase != null">
        IS_NEED_PURCHASE = #{purchase,jdbcType=INTEGER},
      </if>
    </set>
    where COST_CATEGORY_ID = #{costCategoryId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.CostCategoryEntity">
    <!--@mbg.generated-->
    update T_SYS_COST_CATEGORY
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      CATEGORY_NAME = #{categoryName,jdbcType=VARCHAR},
      OLD_CATEGORY_NAME = #{oldCategoryName,jdbcType=VARCHAR},
      IS_DEL = #{isDel,jdbcType=BOOLEAN},
      UNIT_KING_DEE_NO = #{unitKingDeeNo,jdbcType=VARCHAR},
      IS_NEED_PURCHASE = #{purchase,jdbcType=INTEGER},
      OLD_UNIT_KING_DEE_NAME = #{oldUnitKingDeeName,jdbcType=VARCHAR}
    where COST_CATEGORY_ID = #{costCategoryId,jdbcType=INTEGER}
  </update>

  <select id="listFindRepeatInfo" parameterType="com.vedeng.erp.system.domain.dto.CostCategoryDto"
          resultType="java.lang.Integer">
    <!--@mbg.generated-->
    select
    count(*)
    from T_SYS_COST_CATEGORY
    where
    CATEGORY_NAME = #{categoryName,jdbcType=VARCHAR}
    <if test="costCategoryId != null and costCategoryId != 0">
    and  COST_CATEGORY_ID != #{costCategoryId,jdbcType=INTEGER}
    </if>

  </select>

  <select id="queryAllValidInfos" resultType="com.vedeng.erp.system.dto.CostCategoryApiDto">
    SELECT COST_CATEGORY_ID,CATEGORY_NAME
    FROM T_SYS_COST_CATEGORY
    WHERE IS_DEL = 0
  </select>
</mapper>