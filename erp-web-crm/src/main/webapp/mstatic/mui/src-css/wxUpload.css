.ui-wx-upload-wrap {
  display: flex;
  flex-wrap: wrap;
  margin-top: -10px;
}
.ui-wx-upload-wrap .ui-wx-upload-item,
.ui-wx-upload-wrap .ui-wx-upload-btn {
  width: 90px;
  height: 90px;
  margin-right: 10px;
  border-radius: 6px;
  overflow: hidden;
  background: #F5F7FA;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.ui-wx-upload-wrap .ui-wx-upload-item .ui-wx-upload-btn-inner,
.ui-wx-upload-wrap .ui-wx-upload-btn .ui-wx-upload-btn-inner {
  width: 32px;
  height: 32px;
  background-image: url(/mstatic/image/camera.svg);
  background-size: 100% 100%;
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.ui-wx-upload-wrap .ui-wx-upload-item .icon-loading,
.ui-wx-upload-wrap .ui-wx-upload-btn .icon-loading {
  font-size: 32px;
  color: #C2C6CC;
  animation: loading 2s linear infinite;
}
.ui-wx-upload-wrap .ui-wx-upload-item:last-child,
.ui-wx-upload-wrap .ui-wx-upload-btn:last-child {
  margin-right: 0;
}
.ui-wx-upload-wrap .ui-wx-upload-item img,
.ui-wx-upload-wrap .ui-wx-upload-btn img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.ui-wx-upload-wrap .ui-wx-upload-item .ui-wx-upload-item-del,
.ui-wx-upload-wrap .ui-wx-upload-btn .ui-wx-upload-item-del {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  width: 24px;
  height: 24px;
  border-radius: 3px;
}
.ui-wx-upload-wrap .ui-wx-upload-item .ui-wx-upload-item-del .vd-ui_icon,
.ui-wx-upload-wrap .ui-wx-upload-btn .ui-wx-upload-item-del .vd-ui_icon {
  line-height: 1;
}
