package com.newtask.filemove.theadpool;

import com.newtask.filemove.dto.FileMoveDto;
import com.vedeng.common.model.FileInfo;
import com.vedeng.filemove.dao.FileMoveDao;
import com.vedeng.goods.model.Brand;
import com.vedeng.trader.model.TraderCertificate;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.util.List;

/**
 * 品牌信息修改
 */
public class BrandTask extends FileMoveTask {

    @Resource
    private FileMoveDao fileMoveDao;

    public BrandTask(List<FileMoveDto> fileMoveList) {
        super(fileMoveList);
        // 运行时注入service
        WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
        fileMoveDao = (FileMoveDao) context.getBean("fileMoveDao");
    }

    @Override
    protected void updateErrorInfo(FileMoveDto fileMoveDto) {
        Brand toUpdateBrand = new Brand();
        toUpdateBrand.setBrandId(fileMoveDto.getId());
        toUpdateBrand.setSynSuccess(2);
        fileMoveDao.updateBrand(toUpdateBrand);
    }

    @Override
    protected void updateOriginalFileInfo(FileMoveDto fileMoveDto, FileInfo fileInfo) {
        //更新uri、domain、resourceId
        Brand toUpdateBrand = new Brand();
        toUpdateBrand.setBrandId(fileMoveDto.getId());
        toUpdateBrand.setLogoUri(fileInfo.getFilePath());
        toUpdateBrand.setLogoDomain(fileInfo.getHttpUrl());
        toUpdateBrand.setOriginalFilepath(fileMoveDto.getDomain() + fileMoveDto.getUri());
        toUpdateBrand.setSynSuccess(1);
        toUpdateBrand.setOssResourceId(fileInfo.getOssResourceId());

        if(fileInfo.getPrefix() != null){
            toUpdateBrand.setCostTime(Long.valueOf(fileInfo.getPrefix()));
        }

        fileMoveDao.updateBrand(toUpdateBrand);
    }
}
