<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.RSalesJTraderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.RSalesJTraderEntity">
    <!--@mbg.generated-->
    <!--@Table T_R_SALES_J_TRADER-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="SALE_USER_ID" jdbcType="INTEGER" property="saleUserId" />
    <result column="SALE_USER_NAME" jdbcType="VARCHAR" property="saleUserName" />
    <result column="OPERATE_TYPE" jdbcType="VARCHAR" property="operateType" />
    <result column="IS_DELETED" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, TRADER_ID, 
    TRADER_CUSTOMER_ID, SALE_USER_ID, SALE_USER_NAME, OPERATE_TYPE, IS_DELETED
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_R_SALES_J_TRADER
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <select id="getByTraderId" resultType="com.vedeng.erp.trader.dto.RSalesJTraderDto">
    SELECT
      trsjt.*,
      tud.REAL_NAME AS saleUserChineseName,
      tud2.REAL_NAME AS creatorChineseName,
      tt.TRADER_NAME AS traderName
    FROM
      T_R_SALES_J_TRADER trsjt
        LEFT JOIN T_USER_DETAIL tud ON
        trsjt.SALE_USER_ID = tud.USER_ID
        LEFT JOIN T_USER_DETAIL tud2 ON
        trsjt.CREATOR = tud2.USER_ID
        LEFT JOIN T_TRADER tt ON trsjt.TRADER_ID = tt.TRADER_ID
    where IS_DELETED = 0
        AND trsjt.TRADER_ID = #{traderId,jdbcType=INTEGER}
    ORDER BY ID DESC
  </select>
  <select id="getByTraderIdAndUserId" resultType="com.vedeng.erp.trader.dto.RSalesJTraderDto">
    SELECT
    trsjt.*,
    tud.REAL_NAME AS saleUserChineseName,
    tud2.REAL_NAME AS creatorChineseName,
    tt.TRADER_NAME AS traderName
    FROM
    T_R_SALES_J_TRADER trsjt
    LEFT JOIN T_USER_DETAIL tud ON
    trsjt.SALE_USER_ID = tud.USER_ID
    LEFT JOIN T_USER_DETAIL tud2 ON
    trsjt.CREATOR = tud2.USER_ID
    LEFT JOIN T_TRADER tt ON trsjt.TRADER_ID = tt.TRADER_ID
    where IS_DELETED = 0
    AND trsjt.TRADER_ID = #{traderId,jdbcType=INTEGER}
    AND trsjt.SALE_USER_ID = #{userId,jdbcType=INTEGER}
    ORDER BY ID DESC
    LIMIT 1
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_R_SALES_J_TRADER
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.trader.domain.entity.RSalesJTraderEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_SALES_J_TRADER (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      TRADER_ID, TRADER_CUSTOMER_ID, SALE_USER_ID, 
      SALE_USER_NAME, OPERATE_TYPE, IS_DELETED
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{traderId,jdbcType=INTEGER}, #{traderCustomerId,jdbcType=INTEGER}, #{saleUserId,jdbcType=INTEGER}, 
      #{saleUserName,jdbcType=VARCHAR}, #{operateType,jdbcType=VARCHAR}, #{isDeleted,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.trader.domain.entity.RSalesJTraderEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_SALES_J_TRADER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID,
      </if>
      <if test="saleUserId != null">
        SALE_USER_ID,
      </if>
      <if test="saleUserName != null and saleUserName != ''">
        SALE_USER_NAME,
      </if>
      <if test="operateType != null and operateType != ''">
        OPERATE_TYPE,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="saleUserId != null">
        #{saleUserId,jdbcType=INTEGER},
      </if>
      <if test="saleUserName != null and saleUserName != ''">
        #{saleUserName,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null and operateType != ''">
        #{operateType,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.RSalesJTraderEntity">
    <!--@mbg.generated-->
    update T_R_SALES_J_TRADER
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="saleUserId != null">
        SALE_USER_ID = #{saleUserId,jdbcType=INTEGER},
      </if>
      <if test="saleUserName != null and saleUserName != ''">
        SALE_USER_NAME = #{saleUserName,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null and operateType != ''">
        OPERATE_TYPE = #{operateType,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.RSalesJTraderEntity">
    <!--@mbg.generated-->
    update T_R_SALES_J_TRADER
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      SALE_USER_ID = #{saleUserId,jdbcType=INTEGER},
      SALE_USER_NAME = #{saleUserName,jdbcType=VARCHAR},
      OPERATE_TYPE = #{operateType,jdbcType=VARCHAR},
      IS_DELETED = #{isDeleted,jdbcType=INTEGER}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_R_SALES_J_TRADER
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.traderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_CUSTOMER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderCustomerId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.traderCustomerId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SALE_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.saleUserId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.saleUserId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SALE_USER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.saleUserName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.saleUserName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="OPERATE_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operateType != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.operateType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETED = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.isDeleted,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_SALES_J_TRADER
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, TRADER_ID, TRADER_CUSTOMER_ID, 
      SALE_USER_ID, SALE_USER_NAME, OPERATE_TYPE, IS_DELETED)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.traderId,jdbcType=INTEGER}, #{item.traderCustomerId,jdbcType=INTEGER}, #{item.saleUserId,jdbcType=INTEGER}, 
        #{item.saleUserName,jdbcType=VARCHAR}, #{item.operateType,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=INTEGER}
        )
    </foreach>
  </insert>


  <select id="queryShardUserList"  resultType="java.util.Map">
    select  U.USERNAME AS V,CAST(U.USER_ID AS CHAR) AS K FROM T_R_TRADER_J_USER R1
    JOIN T_R_SALES_J_TRADER SHARD1 ON R1.TRADER_ID =SHARD1.TRADER_ID
    JOIN T_USER U ON SHARD1.SALE_USER_ID = U.USER_ID
    WHERE R1.USER_ID =#{userId,jdbcType=INTEGER}
    GROUP BY  SHARD1.SALE_USER_ID
     order by U.USERNAME ASC
  </select>


  <select id="queryShareCustomerListByUserId" parameterType="com.vedeng.erp.trader.dto.TraderBatchShareDto" resultMap="BaseResultMap">
  select
	now() AS ADD_TIME,
	now() AS MOD_TIME,
	TU.USER_ID AS CREATOR,
	TU.USER_ID AS UPDATER,
	U.USERNAME AS CREATOR_NAME,
	U.USERNAME AS UPDATER_NAME,
	T.TRADER_ID,
	TC.TRADER_CUSTOMER_ID,
	'insert' AS OPERATE_TYPE,
	0 AS IS_DELETED
FROM
	T_TRADER T
LEFT JOIN T_R_TRADER_J_USER TU ON
	T.TRADER_ID = TU.TRADER_ID
left join T_TRADER_CUSTOMER TC ON
	TC.TRADER_ID = T.TRADER_ID
	and TU.TRADER_TYPE = 1
LEFT JOIN T_USER U ON
	U.USER_ID = TU.USER_ID
where
	TU.USER_ID =#{USERID,jdbcType=INTEGER}
	AND T.TRADER_ID NOT IN (
	select
		distinct TRADER_ID
	from
		T_R_SALES_J_TRADER
	WHERE
		SALE_USER_ID IN (#{belongSalesId,jdbcType=INTEGER}) )

  </select>
    
  <select id="getBySaleUserAndRegion" resultType="com.vedeng.erp.trader.dto.RSalesJTraderDto">
    select a.TRADER_CUSTOMER_ID,
        a.TRADER_ID,
        d.USER_ID saleUserId,
        d.USERNAME saleUserName
    from T_TRADER_CUSTOMER a
    left join T_TRADER b on a.TRADER_ID = b.TRADER_ID
    left join T_R_TRADER_J_USER c on c.TRADER_ID = b.TRADER_ID
    left join T_USER d on d.USER_ID = c.USER_ID
        where b.COMPANY_ID = 1
        and c.USER_ID = #{userId,jdbcType=INTEGER}
        <if test="regionId != null">
            AND FIND_IN_SET(#{regionId}, b.AREA_IDS)
        </if>
  </select>
  
  <select id="getShareAndBelongTrader" resultType="java.lang.Integer">
    select *
    from (select TRADER_ID
          from T_R_TRADER_J_USER
          where TRADER_ID in 
                <foreach collection="traderIds" item="traderId" index="index" open="(" close=")" separator=",">
                  #{traderId,jdbcType=INTEGER}
                </foreach>
            and USER_ID in 
                 <foreach collection="userIds" item="userId" index="index" open="(" close=")" separator=",">
                  #{userId,jdbcType=INTEGER}
                </foreach>
          union all
          select TRADER_ID
          from T_R_SALES_J_TRADER
          where TRADER_ID in
               <foreach collection="traderIds" item="traderId" index="index" open="(" close=")" separator=",">
                  #{traderId,jdbcType=INTEGER}
                </foreach>
            and SALE_USER_ID in
                 <foreach collection="userIds" item="userId" index="index" open="(" close=")" separator=",">
                  #{userId,jdbcType=INTEGER}
                </foreach>
    ) cc
  </select>
</mapper>
