package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchDetail;
import com.vedeng.erp.buyorder.domain.entity.TPurchaseDeliveryDirectBatchDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface PurchaseDeliveryDirectBatchDetailMapper {
    long countByExample(TPurchaseDeliveryDirectBatchDetailExample example);

    int deleteByExample(TPurchaseDeliveryDirectBatchDetailExample example);

    int deleteByPrimaryKey(Integer purchaseDeliveryDirectBatchDetailId);

    int insert(PurchaseDeliveryDirectBatchDetail record);

    int insertSelective(PurchaseDeliveryDirectBatchDetail record);

    List<PurchaseDeliveryDirectBatchDetail> selectByExample(TPurchaseDeliveryDirectBatchDetailExample example);

    PurchaseDeliveryDirectBatchDetail selectByPrimaryKey(Integer purchaseDeliveryDirectBatchDetailId);

    int updateByExampleSelective(@Param("record") PurchaseDeliveryDirectBatchDetail record, @Param("example") TPurchaseDeliveryDirectBatchDetailExample example);

    int updateByExample(@Param("record") PurchaseDeliveryDirectBatchDetail record, @Param("example") TPurchaseDeliveryDirectBatchDetailExample example);

    int updateByPrimaryKeySelective(PurchaseDeliveryDirectBatchDetail record);

    int updateByPrimaryKey(PurchaseDeliveryDirectBatchDetail record);

    /**
     * <AUTHOR>
     * @desc 根据id更新出库已作业数量
     * @return
     */
    int updateOutNumById(@Param("purchaseDeliveryDirectBatchDetailId") Integer purchaseDeliveryDirectBatchDetailId, @Param("num") Integer num);

    /**
     * 批量保存
     * @param saveItems 数据集合
     * @return
     */
    int batchInsertSelective(@Param("list") List<PurchaseDeliveryDirectBatchDetail> saveItems);

    /**
     * 更新通信单已入库数量
     *
     * @param purchaseDeliveryDirectBatchDetailId
     * @param num
     * @return
     */
    int updateArrivalCountByDetailId(@Param("purchaseDeliveryDirectBatchDetailId") Integer purchaseDeliveryDirectBatchDetailId, @Param("num") Integer num);

    /**
     * <AUTHOR>
     * @desc 根据快递详情id查询批次详情
     * @param expressDetailId
     * @return
     */
    List<PurchaseDeliveryDirectBatchDetail> queryInfoByExpressDetailId(Integer expressDetailId);
    List<PurchaseDeliveryDirectBatchDetail> queryInfoByBuyOrderId(Integer buyOrderId);

    /**
     * <AUTHOR>
     * @desc 根据明细id查询同行单主表id
     * @param purchaseDeliveryDirectBatchDetailId
     * @return
     */
    Integer queryMainIdByDetailId(Integer purchaseDeliveryDirectBatchDetailId);

    /**
     * <AUTHOR>
     * @desc 根据主表id查询明细信息
     * @param purchaseDeliveryDirectBatchInfoId
     * @return
     */
    List<PurchaseDeliveryDirectBatchDetail> queryInfoByMainId(Integer purchaseDeliveryDirectBatchInfoId);
}
