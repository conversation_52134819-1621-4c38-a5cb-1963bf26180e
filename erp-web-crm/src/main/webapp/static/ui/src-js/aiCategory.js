Vue.component('ai-category-choose', {
    template: `<div class="ai-category-choose-wrap" v-show="isShow">
        <div class="ai-category-choose-label">
            <img src="/static/image/common/ai-logo.gif" />
            <div class="ai-category-logo-tip">
                <div class="ai-category-logo-tip-txt">Hi~我是小贝，您的AI助理！</div>
            </div>
        </div>
        <div class="ai-category-choose-cnt">
            <div class="ai-category-loading" v-if="isloading">贝壳助理努力分析中<i class="vd-ui_icon icon-loading"></i></div>
            <template v-else>
                <div class="ai-category-detail" v-if="!isEmpty">
                    <div class="ai-category-detail-txt">根据您输入的产品信息提取出的以下产品名或型号：</div>
                    <div class="ai-category-tags">
                        <div class="ai-category-tag-item" v-for="(item, index) in keywords">{{ item }}</div>
                    </div>
                    <div class="ai-category-detail-txt">为您推荐相关产品分类，您可以手动选择，确定后系统将根据产品分类为您匹配产线协作人员，助您快速赢单！</div>
                    <div class="ai-category-list">
                        <ui-checkbox :checked.sync="isAllSelect" @change="handlerSelectAllChange" label="全部"></ui-checkbox>
                        <ui-checkbox-group :list="categoryList" ref="aiCheckboxGroup" @change="handlerAIChange" :values.sync="aiCategoryIds" layout="vertical"></ui-checkbox-group>
                        <div class="ai-category-custom-item" v-for="(categoryItem, index) in customCategoryList">
                            <ui-checkbox :checked.sync="categoryItem.checked" @change="handlerAIChange"></ui-checkbox>
                            <div class="ai-category-suggest-wrap">
                                <ui-input
                                    @input="handlerSuggestInput"
                                    @focus="handlerSuggestFocus(index)"
                                    @blur="handlerSuggestBlur"
                                    clearable
                                    @clear="handlerSuggestClear"
                                    v-model="categoryItem.label"
                                ></ui-input>
                                <i class="vd-ui_icon icon-delete" @click="deleteCustomCategory(index)"></i>
                                <transition name="fade">
                                    <div class="ai-category-search-panel" v-if="rotate == index">
                                        <div class="ai-category-search-panel-inner">
                                            <div class="ai-category-search-loading" v-if="searchLoading">
                                                <i class="vd-ui_icon icon-loading"></i>
                                                <span>加载中...</span>
                                            </div>
                                            <div class="ai-category-search-panel-list" v-else-if="searchList && searchList.length">
                                                <div class="ai-category-search-panel-title">
                                                    <span class="ai-category-search-panel-title-item">分类</span>
                                                    <span class="ai-category-search-panel-title-item">产品数量</span>
                                                </div>
                                                <div class="ai-category-search-list-content">
                                                    <template v-for="(search, sIndex) in searchList">
                                                        <div class="ai-category-search-panel-item"
                                                            :key="sIndex"
                                                            v-if="search.categoryName"
                                                            @click.stop="selectCategoryItem(search)"
                                                        >
                                                            <span v-html="light(search.categoryName)"></span>
                                                            <span v-if="search.countNum" class="num">{{ search.countNum }}</span>
                                                        </div>
                                                    </template>
                                                </div>
                                            </div>
                                            <div v-else class="ai-category-null-data">
                                                <p>无匹配数据</p>
                                            </div>
                                        </div>
                                    </div>
                                </transition>
                                <transition name="fade">
                                    <div class="ui-category-search-history" v-if="historyRotate == index">
                                        <div class="ui-category-search-history-title">最近使用</div>
                                        <div class="ui-category-search-history-list">
                                            <div class="ui-category-search-history-item" @click.stop="selectHistory(item, hItem)" v-for="(hItem, hIndex) in historyList" :key="hIndex">{{ hItem }}</div>
                                        </div>
                                    </div>
                                </transition>
                            </div>
                        </div>
                        <div class="ai-category-add" v-if="customCategoryList.length < 10">
                            <div class="ai-category-add-btn" @click="addCustomCategory">
                                <i class="vd-ui_icon icon-add"></i>
                                添加分类（{{ customCategoryList.length }}/10）
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ai-category-empty" v-else>抱歉，未能解析出产品名称或型号信息，小贝建议您完善具体客户产品需求信息。</div>
            </template>
        </div>
    </div>`,
    props: {
        defaultData: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    data() {
        return {
            isShow: false,
            isloading: false,
            isEmpty: false,
            keywords: [],
            categoryList: [],
            aiCategoryIds: [],
            rotate: null,
            historyRotate: null,
            customCategoryList: [],
            searchLoading: false,
            searchList: [],
            historyList: [],
            inputTimeout: null,
            isFromSelect: false,
            prevValue: '',
            isAllSelect: true,

        }
    },
    mounted() {
        let historyList = JSON.parse(localStorage.getItem('crm_ai_custom_category') || '[]');
        this.historyList = historyList;

        if(this.defaultData.list) {
            this.initData();
        }

        console.log('aicate')
    },
    computed: {
        // 高亮
        light() {
            return (name) => {
                let inputValue = this.customCategoryList[this.indexFlag].value;
                console.log('inputValue:', inputValue);
                if (!inputValue) return name;
                const regExp = new RegExp(inputValue, 'g');
                name = (name || '').replace(regExp, `<font color='#FF6600'">${inputValue}</font>`);
                return name;
            }
        },
    },
    methods: {
        initData() {
            this.keywords = this.defaultData.keywords;
            let list = this.defaultData.list;
            let ids = [];

            list.forEach(item => {
                item.checked = true;
                ids.push(item.value);
            })

            this.aiCategoryIds = ids;
            this.categoryList = list;
            this.prevValue = this.defaultData.word || '';
            this.isEmpty = false;
            this.isShow = true;
        },
        getAiInfo(word) {
            if(word && word !== this.prevValue) {
                this.isShow = true;
                this.isloading = true;
                this.prevValue = word;
                
                this.$axios.post('/crm/category/match', {
                    input: word
                }).then(({data}) => {
                    this.isloading = false;
                    if(data.success) {
                        let list = [];

                        this.customCategoryList = [];

                        if(data.data.matchedCategories && data.data.matchedCategories.length) {
                            this.isEmpty = false;
                            let ids = [];

                            data.data.matchedCategories.forEach(item => {
                                list.push({
                                    label: item.category,
                                    value: item.subCategoryId,
                                    checked: true
                                })

                                ids.push(item.subCategoryId);
                            })
                     
                            this.keywords = data.data.keywords;
                            this.categoryList = list;
                            this.aiCategoryIds = ids;
                        } else {
                            this.isEmpty = true;
                            this.keywords = [];
                            this.categoryList = [];
                            this.aiCategoryIds = [];
                        }

                        this.handlerAIChange();
                    }
                })
            }
        },
        handlerAIChange() {
            let ids = JSON.parse(JSON.stringify(this.aiCategoryIds));

            let allCustomSelected = true;
            this.customCategoryList.forEach(item => {
                if(item.checked && item.value) {
                    ids.push(item.value);
                }

                if(!item.checked) {
                    allCustomSelected = false;
                }
            })

            this.$emit('change', {
                keywords: this.keywords,
                categoryIds: ids
            })

            this.isAllSelect = this.aiCategoryIds.length === this.categoryList.length && allCustomSelected;
        },
        handlerSelectAllChange() {
            console.log(this.isAllSelect)
            if(this.isAllSelect) {
                let aiCategoryIds = [];

                this.categoryList.forEach(item => {
                    item.checked = true;
                    aiCategoryIds.push(item.value);
                })
                
                this.aiCategoryIds = aiCategoryIds;

                setTimeout(() => {
                    this.$refs.aiCheckboxGroup.setList();
                })

                this.customCategoryList.forEach(item => {
                    item.checked = true;
                })
            } else {
                this.aiCategoryIds = [];

                this.categoryList.forEach(item => {
                    item.checked = false;
                })

                setTimeout(() => {
                    this.$refs.aiCheckboxGroup.setList();
                })

                this.customCategoryList.forEach(item => {
                    item.checked = false;
                })
            }

            this.handlerAIChange();
        },
        handlerSuggestInput(value) {
            this.isFromSelect = false;

            if (!value) {
                this.rotate = null;
                this.checkHistory();
            } else {
                this.historyRotate = null;
                this.rotate = this.indexFlag;
                this.inputTimeout && clearTimeout(this.inputTimeout);
                this.inputTimeout = setTimeout(() => {
                    this.searchRelated(value);
                }, 300);
            }
        },
        handlerSuggestFocus(index) {
            this.isFromSelect = true;
            this.indexFlag = index;
        },
        handlerSuggestBlur() {
            if (!this.isFromSelect) {
                this.customCategoryList[this.indexFlag] = {
                    label: '',
                    value: '',
                    checked: true
                }
            }

            setTimeout(() => {
                this.historyRotate = null;
                this.rotate = null;
            }, 200)
        },
        checkHistory() {
            if (!this.customCategoryList[this.indexFlag].val && this.historyList.length) {
                this.historyRotate = this.indexFlag;
            }
        },
        async searchRelated(keyword) {
            if (!keyword.trim()) return;
            this.searchLoading = true;
            try {
                let { data: searchRes } = await this.$axios.post('/crm/category/public/findThreeCategory', {
                    keyword: keyword,
                    pageNum: 1,
                    pageSize: 100
                })

                this.searchLoading = false;
                if (searchRes.success) {
                    let res = searchRes.data.list || [];
                    this.searchList = res;
                } else {
                    this.searchList = [];
                }
            } catch (err) {
                console.log('err:', err);
            }
        },
        addCustomCategory() {
            if(this.customCategoryList.length >= 10) {
                return;
            }

            this.customCategoryList.push({
                value: '',
                id: '',
                checked: true
            })

            this.$forceUpdate();
        },
        deleteCustomCategory(index) {
            this.customCategoryList.splice(index, 1);
            this.handlerAIChange();
            this.$forceUpdate();
        },
        selectCategoryItem(item) {
            this.customCategoryList[this.indexFlag].label = item.categoryName;
            this.customCategoryList[this.indexFlag].value = item.categoryId;
            this.isFromSelect = true;
            this.handlerAIChange();
        },
        handlerSuggestClear() {
            this.customCategoryList[this.indexFlag] = {
                label: '',
                value: '',
                checked: this.customCategoryList[this.indexFlag].checked
            }

            this.$forceUpdate();
        }
    }
})

Vue.component('ai-category-detail', {
    template: `<div class="ai-category-detail-wrap">
        <div class="ai-category-detail-trigger" @click="show">
            <img src="/static/image/common/ai-logo.gif"/>
            <div class="ai-category-logo-tip">
                <div class="ai-category-logo-tip-txt">让小贝为您分析下需求产品吧~</div>
            </div>
        </div>
        <ui-dialog
            :visible.sync="isShowDetailDialog"
            title="贝壳助理"
            width="720px"
            titleIcon="/static/image/common/ai-logo.gif"
        >
            <div class="ai-category-detail-dialog">
                <div class="ai-category-detail-tip">
                    小贝根据单据录入的产品信息提取出产品名或型号以及相关产品分类（<span class="strong">点击可搜索商品</span>）：
                </div>
                <div class="ai-category-detail-tags">
                    <div class="ai-category-detail-tag-item" v-for="(item, index) in data.keywords" @click="showProd(item)">
                        {{ item }}
                    </div>
                </div>
                <div class="ai-category-detail-list">
                    <div class="ai-category-detail-item" v-for="(item, index) in data.list" @click="showProd(item.category)">
                        <div class="ai-category-detail-item-label text-line-1">{{ item.category }}</div>
                        <i class="vd-ui_icon icon-search"></i>
                    </div>
                </div>
            </div>
        </ui-dialog>
        <ui-select-prod ref="selectProd"></ui-select-prod>
    </div>`,
    props: {
        data: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    data() {
        return {
            isShowDetailDialog: false
        }
    },
    mounted() {
        
    },
    methods: {
        show() {
            this.isShowDetailDialog = true;
        },
        showProd(value) {
            this.$refs.selectProd.show({
                searchValue: value,
                noSelect: true
            })
            document.body.append(this.$refs.selectProd.$el);
        }
    }
})