package com.vedeng.orderstream.aftersales.constant;

/**
 * 退票状态
 */
public enum AfterSalesStatusEnum {
    /**
     * 无退票
     */
    NONE_REFUND_INVOICE(0, "无退票"),

    /**
     * 未退票
     */
    NEED_REFUND_INVOICE(1, "未退票"),

    /**
     * 部分退票
     */
    PART_REFUND_INVOICE(2, "部分退票"),

    /**
     * 全部退票
     */
    ALL_REFUND_INVOICE(3, "全部退票"),

    /**
     * 无入库
     */
    NONE_IN_STOCK(4, "无入库"),

    /**
     * 未入库
     */
    NEED_IN_STOCK(5, "未入库"),

    /**
     * 部分入库
     */
    PART_IN_STOCK(6, "部分入库"),

    /**
     * 全部入库
     */
    ALL_IN_STOCK(7, "全部入库"),

    /**
     * 无退款
     */
    NONE_BACK_MONEY(8, "无退款"),

    /**
     * 未退款
     */
    NEED_BACK_MONEY(9, "未退款"),

    /**
     * 部分退款
     */
    PART_BACK_MONEY(10, "部分退款"),

    /**
     * 全部退款
     */
    ALL_BACK_MONEY(11, "全部退款"),

    /**
     * 无开票
     */
    NONE_CREATE_INVOICE(12, "无开票"),

    /**
     * 未开票
     */
    NEED_CREATE_INVOICE(13, "未开票"),

    /**
     * 部分开票
     */
    PART_CREATE_INVOICE(14, "部分开票"),

    /**
     * 全部开票
     */
    ALL_CREATE_INVOICE(15, "全部开票"),

    /**
     * 无出库
     */
    NONE_OUT_STOCK(16, "无出库"),

    /**
     * 未出库
     */
    NEED_OUT_STOCK(17, "未出库"),

    /**
     * 部分出库
     */
    PART_OUT_STOCK(18, "部分出库"),

    /**
     * 全部出库
     */
    ALL_OUT_STOCK(19, "全部出库"),
    ;

    private Integer code;

    private String desc;

    AfterSalesStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AfterSalesStatusEnum getInstance(Integer code) {
        for (AfterSalesStatusEnum v : values()) {
            if (v.code.equals(code)) {
                return v;
            }
        }
        return NONE_REFUND_INVOICE;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
