package com.vedeng.infrastructure.wxrobot.service;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.infrastructure.wxrobot.constants.WxRobotUrl;
import com.vedeng.infrastructure.wxrobot.dto.WxMsgDto;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信机器人消息通知
 * @date 2023/9/20 9:35
 */
@Component
public class WxRobotService {


    /**
     * 微信机器人发送消息通知
     * url:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=
     *
     * @param robotNum 机器人编号
     * @param wxMsgDto 消息
     */
    public void send(String robotNum, WxMsgDto wxMsgDto) {
        HttpUtil.post(WxRobotUrl.SEND + robotNum, JSON.toJSONString(wxMsgDto),5000);
    }
}
