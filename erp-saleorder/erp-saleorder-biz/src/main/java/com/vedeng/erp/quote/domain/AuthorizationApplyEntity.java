package com.vedeng.erp.quote.domain;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class AuthorizationApplyEntity {
    /**
    * 授权书暂存主键
    */
    private Integer authorizationApplyId;

    private String authorizationApplyNum;

    /**
    * 报价单id
    */
    private Integer quoteorderId;

    /**
    * skuID
    */
    private Integer skuId;

    /**
    * 采购单位/招标公司
    */
    private String purchaseOrBidding;

    /**
    * 生产厂家
    */
    private String productCompany;

    /**
    * 经营性质1为生产2为代理3为销售
    */
    private Integer natureOfOperation;

    /**
    * 品牌名称
    */
    private String brandName;

    /**
    * 产品名称
    */
    private String skuName;

    /**
    * 产品型号
    */
    private String skuModel;

    /**
    * 经销类型1为独家经销商2为经销商3为代理商
    */
    private Integer distributionsType;

    /**
    * 授权公司
    */
    private String authorizedCompany;

    /**
    * 采购项目全程
    */
    private String purchaseProjectName;

    /**
    * 采购项目编号
    */
    private String purchaseProjectNum;

    /**
    * 文件类型，1为投标2为响应
    */
    private Integer fileType;

    /**
    * 售后公司全称
    */
    private String aftersalesCompany;

    /**
    * 授权有限期开始日期
    */
    private String beginTime;

    /**
    * 授权有效期结束日期
    */
    private String endTime;

    /**
    * 修改人
    */
    private Integer updator;

    private String applyPerson;

    /**
    * 当前审核人
    */
    private String reviewer;

    /**
    * 描述
    */
    private String described;

    /**
    * 份数
    */
    private Integer num;

    /**
    * 审核状态
    */
    private Integer applyStatus;

    private String yearAndMonth;

    /**
    * 0为标准模板1为非标准模板
    */
    private Integer standardTemplate;

    /**
    * 备注
    */
    private String comment;

    private String applyYear;

    private String applyMonth;

    private String applyDay;

    /**
    * 非标授权书附件链接
    */
    private String nonStandardAuthorizationUrl;

    /**
    * 非标授权书附件名称
    */
    private String nonStandardAuthorizationName;

    /**
    * 非标授权书签章附件链接
    */
    private String nonStandardAuthorizationSignUrl;

    /**
    * 非标授权书签章附件名称
    */
    private String nonStandardAuthorizationSignName;

    /**
    * 附件是否可签章
    */
    private Integer whetherSign;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 修改时间
     */
    private Long modTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 公章类型 1.南京贝登医疗股份有限公司 2.南京医购优选供应链管理有限公司
     */
    private Integer sealType;

}