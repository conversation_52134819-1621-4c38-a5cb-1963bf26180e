package com.vedeng.erp.system.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.system.domain.entity.RoleUserCategoryConfigEntity;
import com.vedeng.erp.system.dto.RoleUserCategoryConfigDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * 人员与商品分类配置转换器
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface RoleUserCategoryConfigConvertor extends BaseMapStruct<RoleUserCategoryConfigEntity, RoleUserCategoryConfigDto> {
} 