package com.vedeng.erp.saleorder.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.saleorder.domain.entity.SaleOrderTerminalEntity;
import com.vedeng.erp.saleorder.dto.SaleOrderTerminalDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.saleorder.mapstruct
 * @Date 2023/9/4 15:10
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SaleOrderTerminalConvertor extends BaseMapStruct<SaleOrderTerminalEntity, SaleOrderTerminalDto> {
}
