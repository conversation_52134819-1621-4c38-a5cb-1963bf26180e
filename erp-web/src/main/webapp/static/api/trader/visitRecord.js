function page(data) {
    return axios({
        url: '/visitrecord/page.do',
        method: 'post',
        data: data
    })
}


function queryVisitUserListByBelongUser() {
    return axios({
        url: '/visitrecord/queryVisitUserListByBelongUser.do',
        type:"GET",
        dataType : "json",
    })
}

function del(data) {
    return axios({
        url: '/visitrecord/delete.do',
        method: 'post',
        data: data
    })
}