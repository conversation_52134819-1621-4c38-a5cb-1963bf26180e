package com.vedeng.order.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * @Description:  备注组件、场景关联实体类
 * @Author:       davis
 * @Date:         2021/4/14 下午3:01
 * @Version:      1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComponentRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 场景
     */
    private Integer scene;

    /**
     * 关联ID 例如订单ID,商机ID
     */
    private Integer relationId;

    /**
     * 关联明细ID
     */
    private Integer detailId;

    /**
     * 组件ID
     */
    private Integer componentId;

    /**
     * 订货号
     */
    private String skuNo;

    /**
     * 产品名称
     */
    private String skuName;
    /**
     * 日期
     */
    private Long time;

    /**
     * 原因
     */
    private String reason;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 组件父ID
     */
    private Integer parentId;

    /**
     * 组件名称
     */
    private String name;

    /**
     * 组件类型
     */
    private Integer type;

    /**
     * 是否需要日期
     */
    private Integer isDate;

    /**
     * 是否需要展示原因
     */
    private Integer isReason;

    /**
     * 预警等级
     */
    private Integer warnLevel;

    /**
     * 时效状态
     */
    private Integer aging;

    /**
     * 时效状态监控时间
     */
    private Long agingTime;

}
