package com.vedeng.erp.system.service;

import com.vedeng.erp.system.dto.RoleUserCategoryConfigDto;
import com.vedeng.erp.system.vo.RoleUserCategoryConfigQueryVO;
import com.vedeng.erp.system.vo.RoleUserCategoryConfigVO;
import com.vedeng.common.mybatis.domain.PageParam;
import com.github.pagehelper.PageInfo;
import com.vedeng.erp.system.vo.BusinessUserVO;

import java.util.List;

/**
 * 人员与商品分类配置Service接口
 */
public interface RoleUserCategoryConfigApiService {

    /**
     * 分页查询人员与商品分类配置
     */
    PageInfo<RoleUserCategoryConfigVO> pageRoleUserCategoryConfig(PageParam<RoleUserCategoryConfigQueryVO> pageParam);

    /**
     * 获取人员与商品分类配置详情
     */
    RoleUserCategoryConfigDto getById(Long id);

    /**
     * 新增人员与商品分类配置
     */
    boolean saveRoleUserCategoryConfig(RoleUserCategoryConfigDto dto);

    /**
     * 修改人员与商品分类配置
     */
    boolean updateRoleUserCategoryConfig(RoleUserCategoryConfigDto dto);

    /**
     * 删除人员与商品分类配置
     */
    boolean deleteRoleUserCategoryConfig(Long id);

    /**
     * 批量删除人员与商品分类配置
     */
    boolean batchDeleteRoleUserCategoryConfig(List<Long> ids);

    /**
     * 获取所有业务人员信息
     */
    List<BusinessUserVO> getAllBusinessUsers(String userName);
} 