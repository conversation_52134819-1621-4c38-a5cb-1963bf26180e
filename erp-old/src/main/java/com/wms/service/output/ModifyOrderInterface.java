package com.wms.service.output;

import com.wms.constant.WMSContant;
import com.wms.dto.ModifyOrderDto;
import com.wms.dto.WmsRequest;
import com.wms.service.AbstractWmsInterface;
import com.xxl.job.core.log.XxlJobLogger;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * @Description:  出库修改接口
 * @Author:       davis
 * @Date:         2021/6/8 下午7:17
 * @Version:      1.0
 */
@Service
public class ModifyOrderInterface extends AbstractWmsInterface {

    Logger logger= LoggerFactory.getLogger(ModifyOrderInterface.class);

    @Override
    protected void setMethodAndMessageId(WmsRequest wmsRequest) {
        wmsRequest.setMethod(WMSContant.MODIFY_SALEORDER);
        wmsRequest.setMessageId(WMSContant.MODIFY_SALEORDER);
    }

    @Override
    protected JSONObject getXmlDate(Object... params) {

        if (params == null || params.length == 0) {
            return null;
        }

        ModifyOrderDto modifyOrderDto = (ModifyOrderDto) params[0];

        JSONObject headItem = parseJsonObject(modifyOrderDto);

        JSONObject headObject = new JSONObject();
        headObject.put("data", headItem);
        logger.info("ERP出库单修改发货方式、发货要求、等待截止日期至WMS的接口内容:{}",headObject);
        //XxlJobLogger.log("ERP出库单修改发货方式、发货要求、等待截止日期至WMS的接口内容:{}",headObject);
        return headObject;
    }

    private JSONObject parseJsonObject(ModifyOrderDto modifyOrderDto) {
        JSONObject order = new JSONObject();

        order.put("customerId", "VEDENG");
        order.put("warehouseId", "NJ01");
        order.put("soReference1", modifyOrderDto.getOrderNo());
        order.put("orderType", modifyOrderDto.getOrderType());
        order.put("expectedShipmentTime2", modifyOrderDto.getExpectedShipmentTime2());
        order.put("hedi04", modifyOrderDto.getHedi04());
        order.put("hedi07", modifyOrderDto.getHedi07());
        order.put("hedi03", modifyOrderDto.getHedi03());
        order.put("userDefine1", modifyOrderDto.getUserDefine1());
        order.put("userDefine2", modifyOrderDto.getUserDefine2());
        order.put("userDefine3", modifyOrderDto.getUserDefine3());
        order.put("userDefine4", modifyOrderDto.getUserDefine4());
        order.put("userDefine5", modifyOrderDto.getUserDefine5());
        order.put("notes", modifyOrderDto.getNotes());

        JSONObject orders = new JSONObject();
        orders.put("ordernos", order);
        return orders;
    }

    @Override
    protected <T> T parseResponseDate(JSONObject returnObject) {
        return null;
    }
}
