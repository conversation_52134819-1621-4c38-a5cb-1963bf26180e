package com.vedeng.erp.buyorderexpense.domain.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

/**
 * @description 采购费用单详细
 * <AUTHOR>
 * @date 2022/10/17 15:17
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class BuyorderExpenseDetailEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer buyorderExpenseDetailId;

    /**
     * 采购费用单ID
     */
    private Integer buyorderExpenseId;

    /**
     * 付款方式 字典表
     */
    private Integer paymentType;

    /**
     * 付款备注
     */
    private String paymentComments;

    /**
     * 发票类型 字典表
     */
    private Integer invoiceType;

    /**
     * 开票备注
     */
    private String invoiceComments;

    /**
     * 采购费用单应付总额
     */
    private BigDecimal totalAmount;

    /**
     * 供应商id
     */
    private Integer traderId;

    /**
     * 供应商名称
     */
    private String traderName;

    /**
     * 供应商联系人ID
     */
    private Integer traderContactId;

    /**
     * 供应商联系人
     */
    private String traderContactName;

    /**
     * 供应商手机
     */
    private String traderContactMobile;

    /**
     * 供应商电话
     */
    private String traderContactTelephone;

    /**
     * 供应商联系地址ID
     */
    private Integer traderAddressId;

    /**
     * 供应商供应商地区
     */
    private String traderArea;

    /**
     * 供应商联系详细地址(含省市区)
     */
    private String traderAddress;

    /**
     * 供应商备注
     */
    private String traderComments;

    /**
     * 预付金额
     */
    private BigDecimal prepaidAmount;

    /**
     * 账期支付金额
     */
    private BigDecimal accountPeriodAmount;

    /**
     * 账期天数
     */
    private Integer periodDay;

    /**
     * 尾款
     */
    private BigDecimal retainageAmount;

    /**
     * 尾款期限(月)
     */
    private Integer retainageAmountMonth;

    /**
     * 费用来源
     */
    private String expenseSource;

    /**
     * 添加人部门ID
     */
    private Integer orgId;

    /**
     * 采购费用订单合同url
     */
    private String contractUrl;


    /**
     * 是否删除0否1是
     */
    private Integer isDelete;

    /**
     * 订单说明
     */
    private JSONArray orderDesc;
}