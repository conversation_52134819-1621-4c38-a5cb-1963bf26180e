package com.vedeng.trader.model.vo;

import com.vedeng.trader.model.WebAccount;

public class WebAccountVo extends WebAccount {
	private Integer relateStatus;
	
	private Integer assignStatus;
	
	private String relateComapnyName;
	
	private String maybeSaler;
	
	private String owner;
	
	private Integer traderCustomerId;

	private Long startJoinDate,endJoinDate;

	private Long startMemberDate,endMemberDate;

	private Integer userTraderId;

	private String belongPlatformStr;

	private String registerPlatformStr;


	/**
	 * 用户属性【0：未确认、1：个人、2：经销商、3：终端】默认0
	 */
	private Integer accountType;

	private String accountTypeStr;
	/**
	 * 用户角色【0：未确认、1：老板、2：采购经理、3：销售经理、4：技术或研发老师】 默认0
	 */
	private Integer accountRole;

	private String accountRoleStr;

	public Integer getAccountType() {
		return accountType;
	}

	public void setAccountType(Integer accountType) {
		this.accountType = accountType;
	}

	public Integer getAccountRole() {
		return accountRole;
	}

	public void setAccountRole(Integer accountRole) {
		this.accountRole = accountRole;
	}

	public String getAccountTypeStr() {
		return accountTypeStr;
	}

	public void setAccountTypeStr(String accountTypeStr) {
		this.accountTypeStr = accountTypeStr;
	}

	public String getAccountRoleStr() {
		return accountRoleStr;
	}

	public void setAccountRoleStr(String accountRoleStr) {
		this.accountRoleStr = accountRoleStr;
	}

	public Long getStartMemberDate() {
		return startMemberDate;
	}

	public void setStartMemberDate(Long startMemberDate) {
		this.startMemberDate = startMemberDate;
	}

	public Long getEndMemberDate() {
		return endMemberDate;
	}

	public void setEndMemberDate(Long endMemberDate) {
		this.endMemberDate = endMemberDate;
	}

	public TraderCustomerVo getTraderCustomerVo() {
		return traderCustomerVo;
	}

	public void setTraderCustomerVo(TraderCustomerVo traderCustomerVo) {
		this.traderCustomerVo = traderCustomerVo;
	}

	private TraderCustomerVo traderCustomerVo;


	public Long getStartJoinDate() {
		return startJoinDate;
	}

	public void setStartJoinDate(Long startJoinDate) {
		this.startJoinDate = startJoinDate;
	}

	public Long getEndJoinDate() {
		return endJoinDate;
	}

	public void setEndJoinDate(Long endJoinDate) {
		this.endJoinDate = endJoinDate;
	}

	public Integer getRelateStatus() {
		return relateStatus;
	}

	public void setRelateStatus(Integer relateStatus) {
		this.relateStatus = relateStatus;
	}

	public Integer getAssignStatus() {
		return assignStatus;
	}

	public void setAssignStatus(Integer assignStatus) {
		this.assignStatus = assignStatus;
	}

	public String getRelateComapnyName() {
		return relateComapnyName;
	}

	public void setRelateComapnyName(String relateComapnyName) {
		this.relateComapnyName = relateComapnyName;
	}

	public String getMaybeSaler() {
		return maybeSaler;
	}

	public void setMaybeSaler(String maybeSaler) {
		this.maybeSaler = maybeSaler;
	}

	public String getOwner() {
		return owner;
	}

	public void setOwner(String owner) {
		this.owner = owner;
	}

	public Integer getTraderCustomerId() {
		return traderCustomerId;
	}

	public void setTraderCustomerId(Integer traderCustomerId) {
		this.traderCustomerId = traderCustomerId;
	}

	public Integer getUserTraderId() {
		return userTraderId;
	}

	public void setUserTraderId(Integer userTraderId) {
		this.userTraderId = userTraderId;
	}

	public String getBelongPlatformStr() {
		return belongPlatformStr;
	}

	public void setBelongPlatformStr(String belongPlatformStr) {
		this.belongPlatformStr = belongPlatformStr;
	}

	public String getRegisterPlatformStr() {
		return registerPlatformStr;
	}

	public void setRegisterPlatformStr(String registerPlatformStr) {
		this.registerPlatformStr = registerPlatformStr;
	}
}
