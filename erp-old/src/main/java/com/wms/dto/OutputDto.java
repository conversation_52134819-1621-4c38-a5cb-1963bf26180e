package com.wms.dto;

import java.util.List;

/**
 * WMS回传出库单的DTO
 */
public class OutputDto {
    //WMS单据编号
    private String OrderNo;

    //ERP单据号
    private String SOReference1;

    /**
     * SO 销售订单
     * PT 采购售后退货单
     * SA 领用出库单
     * TT 调拔出库单
     * SB 报废出库单
     * TR ERP移仓单
     * SD 样品出库单
     * PS 采购售后换货出库
     * SS销售售后换货出库
     * JS借货出库单
     */
    private String OrderType;

    private List<OutputGoodDto> details;

    public OutputDto() {
    }

    public OutputDto(String orderNo, String SOReference1) {
        OrderNo = orderNo;
        this.SOReference1 = SOReference1;
    }

    public String getOrderNo() {
        return OrderNo;
    }

    public void setOrderNo(String orderNo) {
        OrderNo = orderNo;
    }

    public String getSOReference1() {
        return SOReference1;
    }

    public void setSOReference1(String SOReference1) {
        this.SOReference1 = SOReference1;
    }

    public String getOrderType() {
        return OrderType;
    }

    public void setOrderType(String orderType) {
        OrderType = orderType;
    }

    public List<OutputGoodDto> getDetails() {
        return details;
    }

    public void setDetails(List<OutputGoodDto> details) {
        this.details = details;
    }

    @Override
    public String toString() {
        return "OutputDto{" +
                "OrderNo='" + OrderNo + '\'' +
                ", SOReference1='" + SOReference1 + '\'' +
                ", OrderType='" + OrderType + '\'' +
                ", details=" + details +
                '}';
    }
}
