package com.vedeng.erp.wms.dto;

import java.math.BigDecimal;
import java.util.Date;

import lombok.*;

/**
    * 样品出库单商品信息拓展表
    */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsSampleOrderGoodsExtra {
    /**
    * 主键
    */
    private Long sampleOrderGoodsExtraId;

    /**
    * 出库单商品的id
    */
    private Long wmsOutputOrderGoodsId;

    /**
    * 单位
    */
    private String unitName;

    /**
    * 销售单价
    */
    private BigDecimal price;

    /**
    * 销售总价
    */
    private BigDecimal totalPrice;

    /**
    * 成本单价
    */
    private BigDecimal purchasePrice;

    /**
    * 成本总价
    */
    private BigDecimal totalPurchasePrice;

    /**
    * 是否删除 0否 1是
    */
    private Boolean isDelete;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 添加人ID
    */
    private Integer creator;

    /**
    * 添加人名称
    */
    private String creatorName;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 更新人ID
    */
    private Integer updater;

    /**
    * 更新人名称
    */
    private String updaterName;

    /**
    * 备注
    */
    private String remark;

    /**
    * 更新备注
    */
    private String updateRemark;
}