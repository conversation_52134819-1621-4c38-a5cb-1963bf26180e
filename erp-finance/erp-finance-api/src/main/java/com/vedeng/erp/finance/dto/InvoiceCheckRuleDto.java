package com.vedeng.erp.finance.dto;

import lombok.Data;

import java.util.Date;

@Data
public class InvoiceCheckRuleDto {
    /**
     * 主键
     */
    private Long checkRuleId;

    /**
     * 规则类型（1-发票申请，2-发票开票）
     */
    private Integer ruleType;

    /**
     * 规则编码
     */
    private String ruleCode;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则内容
     */
    private String ruleContent;

    /**
     * 适用场景
     */
    private String usageScene;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 创建者ID
     */
    private Integer creator;

    /**
     * 修改者ID
     */
    private Integer updater;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 修改者名称
     */
    private String updaterName;
}
