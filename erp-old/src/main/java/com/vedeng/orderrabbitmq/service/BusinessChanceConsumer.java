package com.vedeng.orderrabbitmq.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.vedeng.authorization.dao.PositionMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Position;
import com.vedeng.base.api.dto.reqParam.IpInfo;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.call.feign.RemoteCrmCategoryApiService;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.erp.trader.dto.PushBusinessLeadsDto;
import com.vedeng.erp.trader.service.BusinessLeadsApiService;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.model.CoreSpu;
import com.vedeng.goods.service.BaseCategoryService;
import com.vedeng.infrastructure.ip.api.IpApi;
import com.vedeng.order.model.BussinessChance;
import com.vedeng.order.model.vo.BusinessChanceMqDto;
import com.vedeng.order.service.BussinessChanceService;
import com.vedeng.trader.dao.RTraderJUserMapper;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.trader.model.RTraderJUser;
import com.vedeng.trader.model.WebAccount;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 前台推送商机消息
 * <AUTHOR>
 * @date 2020/03/17
 **/
@Component
public class BusinessChanceConsumer extends AbstractMessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(BusinessChanceConsumer.class);

    @Autowired
    private BussinessChanceService bussinessChanceService;

    @Autowired
    private PositionMapper positionMapper;

    @Autowired
    private RTraderJUserMapper rTraderJUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private WebAccountMapper webAccountMapper;

    @Value("${business_chance_source}")
    private String businessChanceSourceId;

    @Value("${business_chance_entrance}")
    private String businessChanceEntranceId;

    @Value("${business_chance_function}")
    private String businessChanceFunctionId;

    @Value("#{'${special.belong.sales}'.split(',')}")
    private List<String> specialBelongSalesList;

    @Autowired
    private BaseCategoryService baseCategoryService;

    @Autowired
    private CoreSpuMapper coreSpuMapper;

    @Autowired
    private BusinessLeadsApiService businessLeadsApiService;

    @Autowired
    private RemoteCrmCategoryApiService remoteCrmCategoryApiService;
    
    @Autowired
    private IpApi ipApi;


    @Override
    public void doBusiness(Message message, Channel channel){

        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        LOGGER.info("前台推送商机信息：{}",messageBody);

        try {
            BusinessChanceMqDto mqDto = JSONObject.parseObject(messageBody,BusinessChanceMqDto.class);

            //数据校验
            if (mqDto.getSource() == null || mqDto.getEntrances() == null || mqDto.getFunctions() == null ||
                    StringUtils.isBlank(mqDto.getCheckTraderContactMobile())){
                throw new Exception("商机信息参数异常");
            }

            PushBusinessLeadsDto dto = new PushBusinessLeadsDto();
            dto.setLeadsNo(mqDto.getBusinessCode());
            dto.setStatus(1);
            dto.setEntrances(getSysOptionDefinitionByMapping(businessChanceEntranceId,mqDto.getEntrances()));
            dto.setFunctions(getSysOptionDefinitionByMapping(businessChanceFunctionId,mqDto.getFunctions()));
            dto.setClueType(394);
            dto.setInquiry(4003);
            dto.setSource(4017);
            dto.setCommunication(Objects.nonNull(mqDto.getPosterId()) ? 4115 : getSysOptionDefinitionByMapping(businessChanceSourceId, mqDto.getSource()));
            String goodsInfo = xssPreventionHandle(mqDto.getGoodsName());
            String lineContent= StringUtils.replace(xssPreventionHandle(mqDto.getContent()),"&&","<br>");
            if (StrUtil.isNotBlank(lineContent)) {
                dto.setGoodsInfo(lineContent);
            } else if (StrUtil.isNotBlank(goodsInfo)) {
                dto.setGoodsInfo(goodsInfo);
            }

            String content = mqDto.getContent();
            String skuNo = ReUtil.get("\\[(.*?)\\]", content, 1);
            if (StrUtil.isNotBlank(skuNo)) {
                CoreSpu coreSpu = coreSpuMapper.getSpuBySku(skuNo);
                if (Objects.nonNull(coreSpu)) {
                    String categoryName = baseCategoryService.getOrganizedCategoryNameById(coreSpu.getCategoryId());
                    String replace = categoryName.replace("-", "/");
                    dto.setContent(replace);
                    dto.setCategoryIds(coreSpu.getCategoryId() + "");
                    dto.setKeywords(skuNo);
                }
            }else{
                // 优化远程服务调用，只调用一次接口
                R<RemoteCrmCategoryApiService.CategoryMatchDto> matchResult = remoteCrmCategoryApiService.matchCategories(content);
                
                if (matchResult != null && matchResult.getData() != null) {
                    RemoteCrmCategoryApiService.CategoryMatchDto categoryMatchDto = matchResult.getData();
                    List<RemoteCrmCategoryApiService.CategoryMatchDto.CategoryMatch> matchedCategories = categoryMatchDto.getMatchedCategories();
                    List<String> keywords = categoryMatchDto.getKeywords();
                    
                    if (matchedCategories != null && !matchedCategories.isEmpty()) {
                        // 提取分类ID并拼接为逗号分隔的字符串
                        dto.setCategoryIds(matchedCategories.stream()
                            .map(RemoteCrmCategoryApiService.CategoryMatchDto.CategoryMatch::getSubCategoryId)
                            .map(String::valueOf)
                            .collect(Collectors.joining(",")));
                    } else {
                        dto.setCategoryIds("");
                    }
                    
                    if (keywords != null && !keywords.isEmpty()) {
                        dto.setKeywords(String.join(",", keywords));
                    } else {
                        dto.setKeywords("");
                    }
                } else {
                    LOGGER.warn("商品分类匹配服务未返回有效结果，内容：{}", content);
                    dto.setCategoryIds("");
                    dto.setKeywords("");
                }
            }

            dto.setTraderName(xssPreventionHandle(mqDto.getTraderName()));
            dto.setContact(xssPreventionHandle(mqDto.getCheckTraderContactName()));
            dto.setPhone(xssPreventionHandle(mqDto.getCheckTraderContactMobile()));

            Integer saleUserId = getSaleUserIdByBusinessChanceLoginMobile(mqDto);
            if (Objects.nonNull(saleUserId) && !ErpConstant.ZERO.equals(saleUserId)) {
                String oldUsername = userMapper.getUserNameByUserId(saleUserId);
                if (!specialBelongSalesList.contains(oldUsername)) {
                    Integer newSaleUserId = bussinessChanceService.checkBusiness2OtherSaleUser(saleUserId);
                    String username = userMapper.getUserNameByUserId(newSaleUserId);
                    if (!specialBelongSalesList.contains(username)) {
                        dto.setBelongerId(newSaleUserId);
                        dto.setBelonger(username);
                    }
                }
            }
            dto.setCreator(1);
            dto.setCreatorName("admin");
            dto.setSendVx("Y"); //默认提醒
            dto.setTycFlag("N");

            try {
                IpInfo ipInfo = new IpInfo();
                ipInfo.setIp(mqDto.getUserIp());
                RestfulResult<IpInfo> vedengRegionByIp = ipApi.getVedengRegionByIp(ipInfo);
                LOGGER.info("调用IP接口返回结果：{}", JSONObject.toJSON(vedengRegionByIp));
                if (vedengRegionByIp.isSuccess()) {
                    IpInfo data = vedengRegionByIp.getData();
                    dto.setProvinceId(Optional.ofNullable(data).map(IpInfo::getVedengProvinceId).orElse(null));
                    dto.setProvince(Optional.ofNullable(data).map(IpInfo::getVedengProvinceName).orElse(null));
                    dto.setCityId(Optional.ofNullable(data).map(IpInfo::getVedengCityId).orElse(null));
                    dto.setCity(Optional.ofNullable(data).map(IpInfo::getVedengCityName).orElse(null));
                    dto.setCountyId(Optional.ofNullable(data).map(IpInfo::getVedengDistrictId).orElse(null));
                    dto.setCounty(Optional.ofNullable(data).map(IpInfo::getVedengDistrictName).orElse(null));
                }
                
            }catch (Exception e){
                LOGGER.info("调用IP接口异常",e);
            }
           
            // 保存推送的线索
            businessLeadsApiService.pushSave(dto);




//            BussinessChance bussinessChance = new BussinessChance();
//            bussinessChance.setBussinessChanceNo(mqDto.getBusinessCode());
//            bussinessChance.setEntrances(getSysOptionDefinitionByMapping(businessChanceEntranceId,mqDto.getEntrances()));
//            bussinessChance.setFunctions(getSysOptionDefinitionByMapping(businessChanceFunctionId,mqDto.getFunctions()));
//            //默认为自主询价
//            bussinessChance.setType(394);
//            //VDERP-3172 商机类型为自主询价的商机，默认提供询价方式为自主询价
//            // VDERP-8191 前台映射转化 4057 留言 4065 自营网站及APP
//            bussinessChance.setInquiry(4057);
//            // 识别商机来源是否是 海报 还是 其他 后期需要用他查询
//            Integer communication=ObjectUtil.isNotEmpty(mqDto.getPosterId())?4115:getSysOptionDefinitionByMapping(businessChanceSourceId,mqDto.getSource());
//            bussinessChance.setCommunication(communication);
//            bussinessChance.setSource(4017);
//            bussinessChance.setGoodsName(xssPreventionHandle(mqDto.getGoodsName()));
//            String content=xssPreventionHandle(mqDto.getContent());
//            String lineContent= StringUtils.replace(content,"&&","<br>");
//            bussinessChance.setContent(lineContent);
//            bussinessChance.setTraderContactName(xssPreventionHandle(mqDto.getCheckTraderContactName()));
//            bussinessChance.setMobile(xssPreventionHandle(mqDto.getCheckTraderContactMobile()));
//            bussinessChance.setTraderName(xssPreventionHandle(mqDto.getTraderName()));
//            bussinessChance.setAddTime(mqDto.getAddTime());
//            //咨询询价商机的询价方式默认为自主询价
//            bussinessChance.setReceiveTime(DateUtil.sysTimeMillis());
//            bussinessChance.setCompanyId(1);
//            //商机分配给销售
//            //根据商机联系方式查找销售
//            Integer saleUserId = getSaleUserIdByBusinessChanceLoginMobile(mqDto);
//            Integer newSaleUserId = bussinessChanceService.checkBusiness2OtherSaleUser(saleUserId);
//            bussinessChance.setUserId(newSaleUserId);
//
//            bussinessChance.setOrgId(getOrgByUserId(bussinessChance.getUserId()));
//            if (mqDto.getPosterId() != null) {
//                bussinessChance.setProductCommentsSale(getProductDesc(mqDto));
//            }
//            //附件
//            Attachment attachment = null;
//            if (StringUtils.isNotBlank(mqDto.getAttachment())){
//                attachment = new Attachment();
//                //根据attachmentId来区分附件存储的位置，-1为存储在oss
//                attachment.setAttachmentId(-1);
//                attachment.setUri("/file/display?resourceId=" + mqDto.getAttachment());
//            }
//
//            //商机操作人默认为njadmin
//            User njadmin = userMapper.getByUsername("njadmin",1);

//            ResultInfo result=bussinessChanceService.saveBussinessChance(bussinessChance,njadmin,attachment);
//            if (result==null||result.getCode()!=0){
//                throw new Exception("商机同步到dbcenter后台失败，商机消息");
//            }
//            try {
//                net.sf.json.JSONObject json = net.sf.json.JSONObject.fromObject(result.getData());
//                BussinessChance bc = (BussinessChance) net.sf.json.JSONObject.toBean(json, BussinessChance.class);
//                bussinessChanceService.sendMessageIfMerge(null, bc);
//                //查一遍，检查商机是否被合并
//                BussinessChance bussinessChanceSaved = bussinessChanceService.getBusinessChanceByChanceNo(bc.getBussinessChanceNo());
//                //判断当前是否为休息时间:
//               // boolean isWorkTime = userWorkApiService.isWorkTime();
//                LOGGER.info("商机推送是否推送微信消息:{},{}",new Object[]{bc.getBussinessChanceNo(),bussinessChanceSaved.getMergeStatus()});
//                if(bussinessChanceSaved.getMergeStatus() ==NOT_MERGED){
//                    Long time = System.currentTimeMillis();
//                    BussinessChanceMessageVo messageVo = new BussinessChanceMessageVo();
//                    messageVo.setBussinessNo(bc.getBussinessChanceNo());
//                    messageVo.setCustomerName(StringUtils.isEmpty(bussinessChance.getTraderName())?"":bussinessChance.getTraderName());
//                    messageVo.setMobile((StringUtils.isEmpty(bussinessChance.getTraderContactName())?"":bussinessChance.getTraderContactName())+" "+  (StringUtils.isEmpty(bussinessChance.getMobile())?"":bussinessChance.getMobile()));
//                    messageVo.setSendTime(DateUtil.convertString(time,null));
//                    messageVo.setRemark(
//                            (StringUtils.isEmpty(bussinessChance.getContent())?"":bussinessChance.getContent()));
//                    try{
//                        userWorkApiService.sendMsg(newSaleUserId,messageVo);
//                    }catch (Exception e){
//                        LOGGER.error("非工作日推送消息失败",e);
//                    }
//                }
//
//            }catch (Exception ex){
//                LOGGER.error("前台新增商机合并商机,发送消息失败：",ex);
//            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
        } catch (Exception e){
            LOGGER.error("消费前台推送的商机信息发送错误，商机内容：{}，错误信息：",messageBody,e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,false);
            } catch (IOException ex) {
                LOGGER.error("商机消息消费失败，将消息返回给rabbitmq错误：",ex);
            }
        }
    }

    /**
     * 拼接产品备注 格式 商机来源于{}分享的海报{}
     * @param mqDto 参数
     * @return 拼接好的备注信息
     */
    private String getProductDesc(BusinessChanceMqDto mqDto) {
        return String.format("商机来源于%s分享的海报%s",userMapper.getUserNameByUserId(mqDto.getSharerId()),mqDto.getPosterId());
    }


    private String xssPreventionHandle(String source){
        return StringEscapeUtils.escapeHtml4(source);
    }

    private Integer getSysOptionDefinitionByMapping(String idString, Integer index){
        String[] idArray = idString.split(",");
        if (idArray.length < index){
            throw new IndexOutOfBoundsException("商机信息参数越界");
        }
        return Integer.valueOf(idArray[index - 1]);
    }



    /**
     * 根据商机联系方式查找对应的销售
     * VDERP-2266： 当该手机号匹配到多个客户时，查看这个客户是否归属同一个销售，如果是，则分配到该销售；如果不是，那么则不分配，将商机推送给总机
     * @param mobile 手机号
     * @return 销售
     */
    private Integer getSaleUserIdByBusinessChanceMobile(String mobile){
        List<RTraderJUser> saleUserIdByContactMobile = rTraderJUserMapper.getSaleUserIdByContactMobile(mobile);
        if (saleUserIdByContactMobile.size() == 0){
            return 0;
        } else if (saleUserIdByContactMobile.size() == 1){
            return saleUserIdByContactMobile.get(0).getUserId();
        } else {
            //当该手机号匹配到多个客户时，查看这个客户是否归属同一个销售，如果是，则分配到该销售；如果不是，那么则不分配，将商机推送给总机
            int countOfSales = saleUserIdByContactMobile.stream().map(RTraderJUser::getUserId).collect(Collectors.toSet()).size();
            if (countOfSales == 1){
                return saleUserIdByContactMobile.get(0).getUserId();
            } else {
                return 0;
            }
        }
    }


    /**
     * VDERP-3164（Lynn） 可合并商机已确认客户，归属销售逻辑优化
     * 用户未登录：
     *    根据填写的手机号，去查联系人信息-客户名称-归属销售；（延用目前线上逻辑）
     * 用户已登录：
     *    已关联客户并分配归属销售；------系统自动将商机分配给归属销售；
     *    已关联客户并未分配归属销售；------首先判断用户是否已分配销售，若有，将商机分配给该销售；若无，商机无归属销售，流转至市场部对其进行分配；
     *    未关联客户但已分配销售；------系统自动将商机分配给该销售；
     *    未关联客户未分配销售；------商机无归属销售，流转至市场部对其进行分配；
     * @param loginMobile 登录手机号
     * @param contactMobile 商机联系方式
     * @return 商机归属销售
     */
    private Integer getSaleUserIdByBusinessChanceLoginMobile(BusinessChanceMqDto dto){
        //判断是否为销售
        Function<Integer,Boolean> verifySales=shareId->
                Optional.ofNullable(positionMapper.getPositionByUserId(shareId))
                        .orElse(new ArrayList<>())
                        .stream().anyMatch(s->s.getType().equals(SysOptionConstant.ID_310));
        if (StringUtils.isBlank(dto.getLoginMobile()) && StringUtils.isBlank(dto.getCheckTraderContactMobile())){
            return 0;
        }
        if (StringUtils.isBlank(dto.getLoginMobile())){
            //用户未登录
            return getSaleUserIdByBusinessChanceMobile(dto.getCheckTraderContactMobile());
        } else {
            //用户已登录
            WebAccount webAccount = webAccountMapper.getWenAccountInfoByMobile(dto.getLoginMobile());
            // 如果未存在该用户信息 则有可能是海报 则判断海报相关信息是否为空
            if (webAccount == null){
                return ObjectUtil.isNotEmpty(dto.getPosterId())?allocateSalesUser(dto):0;
            }
            if (webAccount.getTraderId() != null && webAccount.getTraderId() > 0){
                //登录用户已关联客户逻辑
                //获取关联的客户信息
                RTraderJUser rTraderJUser = rTraderJUserMapper.getUserByTraderId(webAccount.getTraderId());

                if (rTraderJUser == null) {
                    //有关联客户id但是数据库中并没有关联客户信息 先视为未关联客户
                    if (webAccount.getUserId() != null) {
                        //未关联客户但已分配销售；------系统自动将商机分配给该销售；
                        return webAccount.getUserId();
                    }else {

                        //先判断是否是海报分享，如果不是，流转至市场部，如果是，执行以下逻辑
                        //⦁	未关联客户未分配销售，且分享人为销售；------系统将商机分配给海报分享人（根据历史，有多个分享人，取第一个销售）；
                        //⦁	未关联客户未分配销售，且分享人不是销售；------商机无归属销售，流转至市场部对其进行分配
                        return ObjectUtil.isNotEmpty(dto.getPosterId())?allocateSalesUser(dto):0;


                    }

                } else {

                    //已关联客户并分配归属销售
                    if (rTraderJUser.getUserId() != null) {
                        return  rTraderJUser.getUserId();
                    }
                    //已关联客户并未分配归属销售
                    if (webAccount.getUserId() != null) {
                        //首先判断用户是否已分配销售，若有，将商机分配给该销售
                        return webAccount.getUserId();
                    }else {
                        //若无，商机无归属销售，流转至市场部门对其进行分配
                        return 0;
                    }
                }
            } else {
                //登录用户未关联客户逻辑
                // 判断是否是 分享源是否是海报
                if (ObjectUtil.isEmpty(dto.getPosterId())) {
                    return webAccount.getUserId();
                }
                //不是海报来源
                //⦁	未关联客户但已分配销售
                if (webAccount.getUserId() != null) {
                    if(verifySales.apply(webAccount.getUserId())){
                        return webAccount.getUserId();
                    }
                }

                // ⦁未关联客户未分配销售，且分享人为销售；------系统将商机分配给海报分享人（根据历史，有多个分享人，取第一个销售）；
                //⦁	未关联客户未分配销售，且分享人不是销售；------商机无归属销售，流转至市场部对其进行分配
                return allocateSalesUser(dto);
            }
        }
    }

    /**
     * 根据海报分享人Id 来决定当前用户是否分配给当前海报分享人
     * @param dto 海报信息
     * @return 分享人Id
     */
    private Integer allocateSalesUser(BusinessChanceMqDto dto) {

        //⦁	未关联客户未分配销售，且分享人为销售；------系统将商机分配给海报分享人（根据历史，有多个分享人，取第一个销售）；
        //⦁	未关联客户未分配销售，且分享人不是销售；------商机无归属销售，流转至市场部对其进行分配



        // 定义判断用户是否是销售的函数
        Function<Integer,Boolean> verifySales=shareId->
                Optional.ofNullable(positionMapper.getPositionByUserId(shareId))
                .orElse(new ArrayList<>())
                .stream().anyMatch(s->s.getType().equals(SysOptionConstant.ID_310));

        // 查询T_BUSSINESS_CHANCE 是否存在商机记录 如果存在 直接返回（根据历史，有多个分享人，取第一个销售）
        BussinessChance bussinessChance=bussinessChanceService.listBussinessByMobileAndSource(dto.getLoginMobile(),4115);

         // 代表分配过 则直接返回之前分配过的销售的Id
        if (ObjectUtil.isNotEmpty(bussinessChance)&&verifySales.apply(bussinessChance.getUserId())){
            return bussinessChance.getUserId();
        }

        // 当前用户所绑定的职位存在销售部,则分配给当前用户
        return verifySales.apply(dto.getSharerId())
                ?dto.getSharerId()
                :0;
    }


    /**
     * 根据销售查找其归属部门
     * @param userId 用户id
     * @return 部门id
     */
    private Integer getOrgByUserId(Integer userId){
        if (userId.equals(0)){
            return 0;
        }
        List<Position> positions = positionMapper.getPositionByUserId(userId);
        if (CollectionUtils.isEmpty(positions)){
            return 0;
        } else {
            return positions.get(0).getOrgId();
        }
    }


}
