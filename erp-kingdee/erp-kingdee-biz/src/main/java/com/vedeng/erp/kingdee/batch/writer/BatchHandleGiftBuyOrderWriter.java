package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.BatchKingDeePurchaseReceiptDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.erp.kingdee.service.KingDeeStorageInApiService;
import com.vedeng.erp.kingdee.service.KingDeeStorageInService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class BatchHandleGiftBuyOrderWriter extends BaseWriter<KingDeeStorageInDto> {
    @Autowired
    private KingDeeStorageInApiService kingDeeStorageInApiService;
    @Autowired
    private KingDeeStorageInService kingDeeStorageInService;

    @Override
    public void doWrite(KingDeeStorageInDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("采购赠品售后换货出入库:{}", JSON.toJSONString(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.saveStorageIn);
        kingDeeStorageInApiService.register(dto,true);
        kingDeeStorageInService.query(dto);

        // 传递参数
        BatchKingDeePurchaseReceiptDto build = BatchKingDeePurchaseReceiptDto.builder()
                .outInNo(dto.getFBillNo())
                .warehouseGoodsOutInId(Long.valueOf(dto.getFQzokBddjtId()))
                .fId(dto.getFId())
                .build();
        List<BatchKingDeePurchaseReceiptDto> purchaseInData = (List<BatchKingDeePurchaseReceiptDto>) getStepParameter("purchaseInData");
        List<BatchKingDeePurchaseReceiptDto> batchKingDeePurchaseReceiptDtos = new ArrayList<>();
        if (CollUtil.isEmpty(purchaseInData)) {
            batchKingDeePurchaseReceiptDtos.add(build);
            saveStepParameter("purchaseInData", batchKingDeePurchaseReceiptDtos);
            saveStepParameter("formId", dto.getFormId());
        } else {
            purchaseInData.add(build);
        }
    }
}
