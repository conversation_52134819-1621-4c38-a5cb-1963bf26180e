package com.vedeng.goods.dao;

import com.vedeng.goods.model.dto.CoreGoodsDisabledStatusHistoryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Title: ${file_name}
 * @Package ${package_name}
 * @Description: ${todo}
 * @date 2021/8/3016:24
 */
public interface CoreGoodsDisabledStatusHistoryMapper {

    /**
     * 根据goodsNo查询对应的禁用操作人、审核通过时间、对应的goodsNo
     * @param goodsNo goodsNo
     */
    CoreGoodsDisabledStatusHistoryDTO getCreatorAndTime(@Param("goodsNo") String goodsNo, @Param("goodsType") Integer goodsType);

    /**
     * 批量插入记录
     */
    int batchInsert(List<CoreGoodsDisabledStatusHistoryDTO> coreSkuDisabledStatusHistoryDTOList);

    /**
     * insert
     * @param coreSkuDisabledStatusHistoryDTO
     * @return
     */
    int insert(CoreGoodsDisabledStatusHistoryDTO coreSkuDisabledStatusHistoryDTO);
}
