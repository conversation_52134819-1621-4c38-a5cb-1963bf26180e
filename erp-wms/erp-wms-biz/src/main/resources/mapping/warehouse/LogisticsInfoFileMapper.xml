<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.wms.mapper.LogisticsInfoFileMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.wms.domain.entity.LogisticsInfoFileEntity">
    <!--@mbg.generated-->
    <!--@Table T_LOGISTICS_INFO_FILE-->
    <id column="LOGISTICS_INFO_FILE_ID" jdbcType="INTEGER" property="logisticsInfoFileId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="LOGISTICS_NO" jdbcType="VARCHAR" property="logisticsNo" />
    <result column="LOGISTICS_ID" jdbcType="INTEGER" property="logisticsId" />
    <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="URL" jdbcType="VARCHAR" property="url" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    LOGISTICS_INFO_FILE_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    ORDER_NO, LOGISTICS_NO, LOGISTICS_ID, EXPRESS_ID, TRADER_NAME, URL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_LOGISTICS_INFO_FILE
    where LOGISTICS_INFO_FILE_ID = #{logisticsInfoFileId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_LOGISTICS_INFO_FILE
    where LOGISTICS_INFO_FILE_ID = #{logisticsInfoFileId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="LOGISTICS_INFO_FILE_ID" keyProperty="logisticsInfoFileId" parameterType="com.vedeng.erp.wms.domain.entity.LogisticsInfoFileEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_LOGISTICS_INFO_FILE (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      ORDER_NO, LOGISTICS_NO, LOGISTICS_ID, 
      EXPRESS_ID, TRADER_NAME, URL
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{orderNo,jdbcType=VARCHAR}, #{logisticsNo,jdbcType=VARCHAR}, #{logisticsId,jdbcType=INTEGER}, 
      #{expressId,jdbcType=INTEGER}, #{traderName,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="LOGISTICS_INFO_FILE_ID" keyProperty="logisticsInfoFileId" parameterType="com.vedeng.erp.wms.domain.entity.LogisticsInfoFileEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_LOGISTICS_INFO_FILE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="orderNo != null and orderNo != ''">
        ORDER_NO,
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        LOGISTICS_NO,
      </if>
      <if test="logisticsId != null">
        LOGISTICS_ID,
      </if>
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      <if test="traderName != null and traderName != ''">
        TRADER_NAME,
      </if>
      <if test="url != null and url != ''">
        URL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null and orderNo != ''">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="logisticsId != null">
        #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null and traderName != ''">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="url != null and url != ''">
        #{url,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.wms.domain.entity.LogisticsInfoFileEntity">
    <!--@mbg.generated-->
    update T_LOGISTICS_INFO_FILE
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null and orderNo != ''">
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="logisticsNo != null and logisticsNo != ''">
        LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="logisticsId != null">
        LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null and traderName != ''">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="url != null and url != ''">
        URL = #{url,jdbcType=VARCHAR},
      </if>
    </set>
    where LOGISTICS_INFO_FILE_ID = #{logisticsInfoFileId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.wms.domain.entity.LogisticsInfoFileEntity">
    <!--@mbg.generated-->
    update T_LOGISTICS_INFO_FILE
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      URL = #{url,jdbcType=VARCHAR}
    where LOGISTICS_INFO_FILE_ID = #{logisticsInfoFileId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2024-06-13-->
  <select id="findAllByExpressId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_LOGISTICS_INFO_FILE
    where EXPRESS_ID=#{expressId,jdbcType=INTEGER}
  </select>
</mapper>