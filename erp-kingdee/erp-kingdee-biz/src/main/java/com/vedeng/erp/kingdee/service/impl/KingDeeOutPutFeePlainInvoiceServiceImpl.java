package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeOutPutFeePlainInvoiceCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeOutPutFeePlainInvoiceEntity;
import com.vedeng.erp.kingdee.dto.OutPutFeePlainInvoiceDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeOutPutFeePlainInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeOutPutFeePlainInvoiceConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeOutPutFeePlainInvoiceRepository;
import com.vedeng.erp.kingdee.service.KingDeeOutPutFeePlainInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeeOutPutFeePlainInvoiceService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/3/7 16:32
 * @version 1.0
 */
@Service
public class KingDeeOutPutFeePlainInvoiceServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeOutPutFeePlainInvoiceEntity,
        OutPutFeePlainInvoiceDto,
        KingDeeOutPutFeePlainInvoiceCommand,
        KingDeeOutPutFeePlainInvoiceRepository,
        KingDeeOutPutFeePlainInvoiceConvertor,
        KingDeeOutPutFeePlainInvoiceCommandConvertor
        > implements KingDeeOutPutFeePlainInvoiceService, KingDeeOutPutFeePlainInvoiceApiService {
}