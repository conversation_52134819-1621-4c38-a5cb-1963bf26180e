package com.vedeng.customerbillperiod.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: daniel
 * @Date: 2021/7/28 09 55
 * @Description: 账期可用金额类
 */
@Data
public class BillPeriodItem {

    /**
     * 账期类型，1正式账期，2临时账期，3订单账期
     */
    private Integer billPeriodType;

    /**
     * 账期申请的金额
     */
    private BigDecimal amount;

    /**
     * 剩余可用金额
     */
    private BigDecimal availableAmount;

    /**
     * 结算标准
     */
    private Integer billPeriodSettlementType;

    /**
     * 账期结算周期
     */
    private Integer settlementPeriod;


}
