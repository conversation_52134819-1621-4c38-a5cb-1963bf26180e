package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.trader.domain.dto.TraderPaidAccountDto;
import com.vedeng.erp.trader.domain.entity.TraderPaidAccountEntity;
import com.vedeng.erp.trader.mapper.TraderPaidAccountMapper;
import com.vedeng.erp.trader.mapstruct.TraderPaidAccountConvertor;
import com.vedeng.erp.trader.service.TraderPaidAccountApiService;
import com.vedeng.erp.trader.service.TraderPaidAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户已交易账号
 * @date 2022/8/16 14:30
 */
@Service
public class TraderPaidAccountServiceImpl implements TraderPaidAccountService, TraderPaidAccountApiService {

    @Autowired
    private TraderPaidAccountMapper traderPaidAccountMapper;

    @Autowired
    private TraderPaidAccountConvertor traderPaidAccountConvertor;

    @Override
    public void delete(HashSet<Integer> ids) {
        ids.forEach(id -> {
            TraderPaidAccountEntity entity = new TraderPaidAccountEntity();
            entity.setIsDel(ErpConstant.T);
            entity.setTraderPaidAccountId(id);
            traderPaidAccountMapper.updateByPrimaryKeySelective(entity);
        });
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void importFile(MultipartFile file) throws Exception {
        List<TraderPaidAccountDto> addTraderPaidAccount = new ArrayList<>();
        List<TraderPaidAccountDto> updateTraderPaidAccount = new ArrayList<>();
        AtomicInteger row = new AtomicInteger(1);
        EasyExcel.read(file.getInputStream(), TraderPaidAccountDto.class, new PageReadListener<TraderPaidAccountDto>(dataList ->
                dataList.forEach(data -> {
                    row.getAndIncrement();
                    if (StrUtil.isBlank(data.getTraderName())
                            || StrUtil.length(data.getTraderName()) > 255
                            || StrUtil.isBlank(data.getTraderPaidAccount())
                            || StrUtil.length(data.getTraderPaidAccount()) > 255
                            || !ReUtil.isMatch("^-?[0-9]+", data.getTraderPaidAccount())) {
                        throw new ServiceException(StrUtil.format("第{}行数据有误",row.get()));
                    }

                    TraderPaidAccountEntity traderPaidAccount = traderPaidAccountMapper.findByTraderNameAndTraderPaidAccountAndIsDel(data.getTraderName(),
                            data.getTraderPaidAccount(), ErpConstant.F);
                    if (traderPaidAccount != null) {
                        data.setTraderPaidAccountId(traderPaidAccount.getTraderPaidAccountId());
                        updateTraderPaidAccount.add(data);
                    } else {
                        data.setIsDel(ErpConstant.F);
                        addTraderPaidAccount.add(data);
                    }
                }))).sheet().doRead();

        if (CollUtil.isNotEmpty(addTraderPaidAccount)) {
            // 去除新增列表中重复项
            List<TraderPaidAccountDto> list = addTraderPaidAccount.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                            () -> new TreeSet<>(Comparator.comparing(o -> o.getTraderName()+ "#" + o.getTraderPaidAccount()))),
                    ArrayList::new));
            traderPaidAccountMapper.batchInsert(traderPaidAccountConvertor.toEntity(list));
        }
        if (CollUtil.isNotEmpty(updateTraderPaidAccount)) {
            traderPaidAccountMapper.updateBatchSelective(traderPaidAccountConvertor.toEntity(updateTraderPaidAccount));
        }
    }

    @Override
    public Boolean hasTraderPaidAccount(String traderPaidAccount) {
        List<TraderPaidAccountEntity> list = traderPaidAccountMapper.findByTraderPaidAccountAndIsDel(traderPaidAccount, ErpConstant.F);
        return CollUtil.isNotEmpty(list);
    }
}
