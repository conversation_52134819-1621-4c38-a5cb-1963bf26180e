package com.vedeng.erp.kingdee.batch.repository;
import org.apache.ibatis.annotations.Param;

import com.vedeng.erp.kingdee.batch.dto.BatchSettlementBillDto;
import java.util.List;


/**
 * <AUTHOR>
 * @description ${end}
 * @date 2023/12/9 12:56
 **/
public interface BatchSettlementBillDtoMapper {
    int deleteByPrimaryKey(Integer settleBillId);

    int insert(BatchSettlementBillDto record);

    int insertSelective(BatchSettlementBillDto record);

    BatchSettlementBillDto selectByPrimaryKey(Integer settleBillId);

    int updateByPrimaryKeySelective(BatchSettlementBillDto record);

    int updateByPrimaryKey(BatchSettlementBillDto record);

    List<BatchSettlementBillDto> queryBuyOrderRebate(BatchSettlementBillDto query);

    List<BatchSettlementBillDto> queryBuyOrderAfterRebate(BatchSettlementBillDto query);

    List<BatchSettlementBillDto> selectByBusinessSourceTypeIdAndSourceType(@Param("businessSourceTypeId")Integer businessSourceTypeId,@Param("sourceType")String sourceType);


}