package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchExpressDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BatchExpressDtoMapper {
    int deleteByPrimaryKey(Integer expressId);

    int insert(BatchExpressDto record);

    int insertOrUpdate(BatchExpressDto record);

    int insertOrUpdateSelective(BatchExpressDto record);

    int insertSelective(BatchExpressDto record);

    BatchExpressDto selectByPrimaryKey(Integer expressId);

    int updateByPrimaryKeySelective(BatchExpressDto record);

    int updateByPrimaryKey(BatchExpressDto record);

    int updateBatch(List<BatchExpressDto> list);

    int updateBatchSelective(List<BatchExpressDto> list);

    int batchInsert(@Param("list") List<BatchExpressDto> list);

    List<BatchExpressDto> queryDirectPurchaseArrivedExpress(BatchExpressDto batchExpressDto);
}