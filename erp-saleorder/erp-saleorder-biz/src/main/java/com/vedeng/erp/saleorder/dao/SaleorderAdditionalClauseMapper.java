package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.saleorder.domain.SaleorderAdditionalClause;
import com.vedeng.erp.saleorder.domain.SaleorderAdditionalClauseExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SaleorderAdditionalClauseMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    long countByExample(SaleorderAdditionalClauseExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    int deleteByExample(SaleorderAdditionalClauseExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    int insert(SaleorderAdditionalClause record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    int insertSelective(SaleorderAdditionalClause record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    List<SaleorderAdditionalClause> selectByExample(SaleorderAdditionalClauseExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    SaleorderAdditionalClause selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    int updateByExampleSelective(@Param("record") SaleorderAdditionalClause record, @Param("example") SaleorderAdditionalClauseExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    int updateByExample(@Param("record") SaleorderAdditionalClause record, @Param("example") SaleorderAdditionalClauseExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    int updateByPrimaryKeySelective(SaleorderAdditionalClause record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER_ADDITIONAL_CLAUSE
     *
     * @mbg.generated Mon Apr 07 16:24:30 CST 2025
     */
    int updateByPrimaryKey(SaleorderAdditionalClause record);
}