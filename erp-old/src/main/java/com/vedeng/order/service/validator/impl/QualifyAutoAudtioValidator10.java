package com.vedeng.order.service.validator.impl;

import com.vedeng.firstengage.model.RegistrationNumber;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.service.validator.dto.ManageCategoryLevel;
import com.vedeng.order.service.validator.dto.QualifyAutoAudtioException;
import com.vedeng.order.service.validator.dto.RegistrationCategory;
import org.springframework.stereotype.Service;

/**
 * match:
 * 若采购订单产品【"是否有注册证/备案凭证"字段为"有"，注册证类型为"国产注册证"，注册人名称与供应商名称一致，管理类别为"二类"或"三类"】
 * validators:
 * 该供应商须满足以下资质条件才能自动审核通过：
 * 1）	营业执照已上传，
 * 2）	执照在有效期内
 * 3）	医疗器械生产许可证已上传
 * 4）	医疗器械生产许可证在有效期内
 * 5）	产品注册证在有效期内
 * 6）	生产企业生产产品登记表已上传
 * 7）	生产企业生产产品登记表在有效期内
 * 备注输出内容为：订单中产品在供应商医疗器械生产许可证、生产产品登记表范围内
 */
@Service
public class QualifyAutoAudtioValidator10 extends AbstractQualifyAutoAudtioValidator{

    @Override
    public boolean isMatch(BuyorderGoodsVo buyOrderGoods) {

        //是否有注册证
        boolean hasRegistrationCert = super.hasRegistrationCert(buyOrderGoods.getSku());

        //获取SKU的注册证信息
        RegistrationNumber registrationNumber = super.getRegisterCertificateInfo(buyOrderGoods.getSku());

        boolean isNameMatch = super.isRegisterNameMatchTraderName(buyOrderGoods.getSku());

        return hasRegistrationCert
                && registrationNumber.getCategory() == RegistrationCategory.DOMESTIC_REGISTRATION
                && isNameMatch
                && (registrationNumber.getManageCategoryLevel() == ManageCategoryLevel.SECOND_CATEGORY
                        || registrationNumber.getManageCategoryLevel() == ManageCategoryLevel.THIRD_CATEGORY);
    }

    @Override
    public String validator(BuyorderGoodsVo buyOrderGoods) throws QualifyAutoAudtioException {

        Integer traderId = super.getTradeInfo(buyOrderGoods).getTraderId();

        if(!super.isBusinessLicenseUpload(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照缺失");
        }

        if(!super.isBusinessLicenseInValidityPeriod(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照过期");
        }

        if(!super.getProductPermissionUpload(traderId)){
            throw new QualifyAutoAudtioException("该供应商的医疗器械生产许可证缺失");
        }

        if(!super.getProductPermissionInValidityPeriod(traderId)){
            throw new QualifyAutoAudtioException("该供应商的医疗器械生产许可证过期");
        }

        if(!super.isProductRegisteValid(buyOrderGoods.getSku())){
            throw new QualifyAutoAudtioException("产品注册证过期");
        }

        if(!super.getProductRegistrationUpload(traderId)){
            throw new QualifyAutoAudtioException("该供应商的生产企业生产产品登记表缺失");
        }

        if(!super.getProductRegistrationInValidityPeriod(traderId)){
            throw new QualifyAutoAudtioException("该供应商的生产企业生产产品登记表过期");
        }

        return "订单中产品在供应商医疗器械生产许可证、生产产品登记表范围内";
    }
    
    @Override
    public boolean isMatch(Integer traderId, String skuNo) {
        //是否有注册证
        boolean hasRegistrationCert = super.hasRegistrationCert(skuNo);

        //获取SKU的注册证信息
        RegistrationNumber registrationNumber = super.getRegisterCertificateInfo(skuNo);

        boolean isNameMatch = super.isRegisterNameMatchTraderName(skuNo);

        return hasRegistrationCert
                && registrationNumber.getCategory() == RegistrationCategory.DOMESTIC_REGISTRATION
                && isNameMatch
                && (registrationNumber.getManageCategoryLevel() == ManageCategoryLevel.SECOND_CATEGORY
                        || registrationNumber.getManageCategoryLevel() == ManageCategoryLevel.THIRD_CATEGORY);
    }

    @Override
    public String validator(Integer traderId, String skuNo) throws QualifyAutoAudtioException {
        if(!super.isBusinessLicenseUpload(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照缺失");
        }

        if(!super.isBusinessLicenseInValidityPeriod(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照过期");
        }

        if(!super.getProductPermissionUpload(traderId)){
            throw new QualifyAutoAudtioException("该供应商的医疗器械生产许可证缺失");
        }

        if(!super.getProductPermissionInValidityPeriod(traderId)){
            throw new QualifyAutoAudtioException("该供应商的医疗器械生产许可证过期");
        }

        if(!super.isProductRegisteValid(skuNo)){
            throw new QualifyAutoAudtioException("产品注册证过期");
        }

        if(!super.getProductRegistrationUpload(traderId)){
            throw new QualifyAutoAudtioException("该供应商的生产企业生产产品登记表缺失");
        }

        if(!super.getProductRegistrationInValidityPeriod(traderId)){
            throw new QualifyAutoAudtioException("该供应商的生产企业生产产品登记表过期");
        }

        return "订单中产品在供应商医疗器械生产许可证、生产产品登记表范围内";
    }
}
