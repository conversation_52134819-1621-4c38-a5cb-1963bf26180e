<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<div class="parts">
    <%--<div class="title-container">
        <div class="table-title nobor">
            支出记录
        </div>
        <c:choose>
            <c:when test="${afterSalesVo.atferSalesStatus eq 2 || afterSalesVo.atferSalesStatus eq 3}">
                <div class="title-click  pop-new-data" layerParams='{"width":"600px","height":"380px","title":"新增售后支出记录","link":"/order/afterSalesCommon/addExpenditureRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>新增售后支出记录</div>
            </c:when>
        </c:choose>
    </div>--%>
    <table class="table">
        <thead>
        <tr>
            <th>费用类型</th>
            <th>支付金额</th>
            <th>支付对象</th>
            <th>责任部门</th>
            <th>备注</th>
        </tr>
        </thead>
        <tbody>
        <c:if test="${not empty afterSalesExpenditureRecords}">
            <c:forEach items="${afterSalesExpenditureRecords}" var="expenditureList">
                <tr>
                    <td>
                        <c:forEach items="${typeList}" var="typeL">
                            <c:if test="${expenditureList.type eq typeL.sysOptionDefinitionId}">
                                ${typeL.title}
                            </c:if>
                        </c:forEach>
                    </td>
                    <td>${expenditureList.amount}</td>
                    <td>
                            ${expenditureList.payer}
                            <%--<c:forEach items="${createList}" var="createL">
                                <c:if test="${expenditureList.creator eq createL.userId}">
                                    ${createL.username}
                                </c:if>
                            </c:forEach>--%>
                    </td>
                    <td>
                        <c:forEach items="${orgList}" var="exOrg">
                            <c:if test="${expenditureList.orgId eq exOrg.orgId}">
                                ${exOrg.orgName}
                            </c:if>
                        </c:forEach>
                    </td>
                    <td>
                        ${expenditureList.remark}
                    </td>
                </tr>
            </c:forEach>
        </c:if>
        <c:if test="${empty afterSalesExpenditureRecords}">
            <!-- 查询无结果弹出 -->
            <tr>
                <td colspan='5'>暂无记录！</td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>