function addUser() {
    // var userId = $("#userId").val();
    var userIdObj = xmSelect.get('#userIdSelect', true);
    var userIdArray = userIdObj.getValue("value");

    // if ( !userIdObj ) {
    //     layer.alert('请选择豁免人员');
    //     return;
    // }
    // var userId = userIdObj[0].userId;
    var orgId = 0;
    if ( userIdArray.length ==0) {
        layer.alert('请选择豁免人员');
    } else {
        var userId =userIdArray[0];
        layer.confirm('是否确认添加该豁免人员？',{icon:3,title:'提示'},function (index){
            layer.close(index);
            // $(obj).get(0).parentNode.remove();
            $.ajax({

                type:"POST",
                url:"/trader/customer/addExemptUser.do",
                data:{"userId":userId},
                dataType:'json',
                success:function (data){
                    debugger
                    if (data.code == 0) {
                        if (data.data == 1) {
                            layer.msg('操作成功', {
                                icon: 1,
                                time: 1000 //2秒关闭（如果不配置，默认是3秒）
                            }, function(){
                                //do something
                                location.href='/trader/customer/view/calculate/rules/exemption.do?orgId='+orgId+"&userId="+userId

                            });
                        }else if (data.data == 2) {
                            layer.alert('该豁免人员已添加，请勿重复添加。')
                        }
                        //

                        // layer.alert("操作成功失败");
                    } else {
                        layer.msg('操作失败', {
                            icon: 2,
                            time: 1000 //2秒关闭（如果不配置，默认是3秒）
                        }, function(){
                            //do something
                            layer.close(index);
                        });
                    }

                },
                error:function (data){
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                    }
                }
            });

        })
    }
}
function delete0(value,obj) {
    layer.confirm('确定删除该豁免人员？',{icon:3,title:'提示'},function (index){
        console.log($(obj))
        console.log($(obj).get(0).parentNode.parentNode.parentNode.parentNode)
        layer.close(index);
        // $(obj).get(0).parentNode.remove();
        $.ajax({

            type:"POST",
            url:"/trader/customer/deleteExemptionUser.do",
            data:{"publicCustomerExemptUsersId":value},
            dataType:'json',
            success:function (data){
                debugger
                if (data.code == 0) {
                    if (data.data == 1) {
                        layer.msg('操作成功', {
                            icon: 1,
                            time: 1000 //2秒关闭（如果不配置，默认是3秒）
                        }, function(){
                            //do something
                            $(obj).get(0).parentNode.parentNode.parentNode.parentNode.remove()

                        });
                    }else if (data.data == 2) {
                        layer.alert('该豁免人员已删除，请勿重复删除。')
                    }
                    //

                    // layer.alert("操作成功失败");
                } else {
                    layer.msg('操作失败', {
                        icon: 2,
                        time: 1000 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        //do something
                        layer.close(index);
                    });
                }

            },
            error:function (data){
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                }
            }
        });

    })
}
$(function(){
    $("select[name='orgId']").change(function(){
        checkLogin();
        var orgId = $(this).val();
        if(orgId > 0){
            $.ajax({
                type : "POST",
                url : page_url+"/trader/customer/getSaleUser.do",
                data :{'orgId':orgId},
                dataType : 'json',
                success : function(data) {
                    $option = "<option value='0'>请选择</option>";
                    $.each(data.listData,function(i,n){
                        $option += "<option value='"+data.listData[i]['userId']+"'>"+data.listData[i]['username']+"</option>";
                    });
                    $("select[name='userId'] option:gt(0)").remove();
                    $("#city").val("0").trigger("change");

                    $("select[name='userId']").html($option);
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }else {
            $("select[name='userId'] option:gt(0)").remove();
        }
    });


});