package com.vedeng.common.core.utils.numgenerator;

import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 基于主键的编号生成器
 * @date 2022/7/13 12:36
 */
public class PrimaryKeyNumGenerator extends NumGenerator{

    @Override
    public String compose(NoGeneratorBean t) {
        return this.getDateFormatNo(t) + this.generatePrimaryKeyNum(t);
    }

    @Override
    public String getPrefix(NoGeneratorBean t) {
        if (StrUtil.isNotEmpty(t.getPrefix())) {
            return t.getPrefix();
        }
        return "";
    }

    @Override
    public boolean persistence() {
        return false;
    }
}
