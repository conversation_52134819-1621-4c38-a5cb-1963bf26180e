<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="商品确认" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/orderstream/saleorder/productConfirmPage.js?rnd=${resourceVersionKey}'></script>
<script >
    $(function (){
        var skuNoList = $("input[name='skuNoList']").val();
        var traderId = $("input[name='traderId']").val();
        //补订单产品详情相关数据
        $.ajax({
            async:true,
            url:page_url+'/orderstream/saleorder/getSkuPriceInfo.do',
            data:{"skuNos":skuNoList,"traderId":traderId},//销售订单产品价格信息
            type:"POST",
            dataType : "json",
            success:function(data){
                if(data.code==0){
                    var result = data.data
                    for (var key in result){
                        $(".skuNoAndPrice_"+key).val(result[key]);
                    }
                }else{
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    })
</script>
<div class="content mt10 ">
    <div class="parts">
<%--        <div class="title-container title-container-blue">--%>
<%--            <div class="table-title nobor">客户信息</div>--%>
<%--        </div>--%>
        <div class="formpublic formpublic1">
            <div class="infor_name  infor_name6 ">
                <label>销售订单</label>
            </div>

            <div class="f_left table-larger" style="width: 80%">
                <div class="mb10">
                    <input type="text" class="input-larger mr5"
                           name="saleOrderNo" id="saleOrderNo"
                           value="" placeholder="请输入订单号">
                    <input type="hidden" id="orderIdList" value="${orderIdList}">
                    <input type="hidden" id="skuNoList" name="skuNoList" value="${skuNoList}">
                    <span
                            class="bt-bg-style bt-small bg-light-blue" onclick="continueQuote();"
                            id="searchError">继续引用</span>

                </div>
            </div>
        </div>
        <div class="formpublic formpublic1" id="updateTerminalInfo" >
            <div>
                <form method="post" id="editForm"
                      action="${pageContext.request.contextPath}/orderstream/saleorder/saveRePurchaseOrder.do?indexId=${indexId}">
                    <div>
                        <!-- 初始模式 -->
                        <ul class="searchTable visible">
                            <li>

                                <div>
                                    <table
                                            class="table table-bordered table-striped table-condensed table-centered mb10" id="product_table">
                                        <thead>
                                        <th>序号</th>
                                        <th>订货号</th>
                                        <th>产品名称</th>
                                        <th>品牌</th>
                                        <th>规格/型号</th>
                                        <th>单位</th>
                                        <th>单价</th>
                                        <th>数量</th>
                                        <th>商品等级</th>
                                        <th>商品档位</th>
                                        <th>审核状态</th>
                                        <th class="table-smallest6">操作</th>
                                        </thead>
                                        <tbody>

                                        <c:if test="${not empty saleorderGoodsList}">
                                            <c:forEach items="${saleorderGoodsList}" var="list"
                                                       varStatus="status">
                                                <tr>
                                                    <input type="hidden" name="goodsList[${status.index}].saleorderId"
                                                           value="${list.saleorderId}"/>
                                                    <input type="hidden" name="goodsList[${status.index}].goodsId"
                                                           value="${list.goodsId}"/>
                                                    <input type="hidden" name="goodsList[${status.index}].sku"
                                                           value="${list.sku}"/>
                                                    <input type="hidden" name="goodsList[${status.index}].goodsName"
                                                           value="${list.goodsName}"/>
                                                    <input type="hidden" name="goodsList[${status.index}].brandName"
                                                           value="${list.brandName}"/>
                                                    <input type="hidden" name="goodsList[${status.index}].model"
                                                           value="${list.model}"/>
                                                    <input type="hidden" name="goodsList[${status.index}].unitName"
                                                           value="${list.unitName}"/>
                                                    <td >${status.index+1}</td>
                                                    <td>${list.sku}</td>
                                                    <td>${list.goodsName}</td>
                                                    <td>${list.brandName}</td>
                                                    <td>${list.model}</td>
                                                    <td>${list.unitName}</td>
                                                    <td>
                                                        <input class="skuNoAndPrice_${list.sku}" name="goodsList[${status.index}].price" value="${list.price}" style="width: 100%">
                                                    </td>
                                                    <td>
                                                        <input name="goodsList[${status.index}].num" value="${list.num}" style="width: 100%">
                                                    </td>
                                                    <td>${list.goodsLevelName}</td>
                                                    <td>${list.goodsPositionName}</td>
                                                    <td>
                                                        <c:if test="${list.checkStatus eq 0}">待完善</c:if>
                                                        <c:if test="${list.checkStatus eq 1}">审核中</c:if>
                                                        <c:if test="${list.checkStatus eq 2}">审核不通过</c:if>
                                                        <c:if test="${list.checkStatus eq 3}">审核通过</c:if>
                                                    </td>
                                                    <td width="5%" style="text-align: center"><a
                                                            href="javaScript:void(0)"
                                                            onclick="delSaleOrderGoods(this)">删除</a>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="add-tijiao tcenter mt10">
                        <input type="hidden" name="formToken" value="${formToken}"/>
                        <input hidden id = "traderId" name="traderId" value="${traderId}">
                        <button type="button" class="bt-bg-style bg-light-blue" onclick="addSubmit();">下一步</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
</body>
</html>
