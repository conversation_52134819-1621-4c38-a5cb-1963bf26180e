<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
       xmlns:jdbc="http://www.springframework.org/schema/jdbc" xmlns:jee="http://www.springframework.org/schema/jee"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:util="http://www.springframework.org/schema/util"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd
		http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc-3.2.xsd
		http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-3.2.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.2.xsd
		http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.2.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.2.xsd">

    <bean id="dwhSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dwhDataSource" />
        <property name="mapperLocations">
            <list>
                <value>classpath:/mapping/dwh/**/*.xml</value>
            </list>
        </property>
    </bean>

    <bean id="dwhDataSource"
          class="com.ezadmin.plugins.sqlog.EzSqlogDataSource">
        <property name="realDataSource" ref="dwhDataSourceTarget"></property>
        <property name="logType" value="${logType}"></property>
    </bean>

    <bean id="dwhDataSourceTarget" class="com.alibaba.druid.pool.DruidDataSource"  init-method="init" destroy-method="close">
        <property name="url" value="${dwh.url}" />
        <property name="username" value="${dwh.username}" />
        <property name="password" value="${dwh.password}" />
<!--        <property name="url" value="*********************************************************************************************************************" />-->
<!--        <property name="username" value="dwh_rr" />-->
<!--        <property name="password" value="hatKCJGze4c=" />-->

        <property name="initialSize" value="0" />
        <property name="maxActive" value="20" />

        <property name="minIdle" value="0" />
        <property name="maxWait" value="60000" />

        <property name="validationQuery" value="${validationQuery}" />
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
        <property name="testWhileIdle" value="true" />
        <property name="timeBetweenEvictionRunsMillis" value="60000" />
        <property name="minEvictableIdleTimeMillis" value="25200000" />
        <property name="removeAbandoned" value="true" />
        <property name="removeAbandonedTimeout" value="1800" />
        <property name="logAbandoned" value="true" />
        <property name="filters" value="mergeStat" />
    </bean>
    <bean id="wmsDataSourceTarget" class="com.alibaba.druid.pool.DruidDataSource"  init-method="init" destroy-method="close">
        <property name="url" value="${wms_oracle_url}" />
        <property name="username" value="${wms_oracle_username}" />
        <property name="password" value="${wms_oracle_password}" />
        <property name="initialSize" value="0" />
        <property name="maxActive" value="5" />
        <property name="minIdle" value="0" />
        <property name="maxWait" value="60000" />
        <property name="validationQuery" value="select 1 from dual" />
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
        <property name="testWhileIdle" value="true" />
        <property name="timeBetweenEvictionRunsMillis" value="60000" />
        <property name="minEvictableIdleTimeMillis" value="25200000" />
        <property name="removeAbandoned" value="true" />
        <property name="removeAbandonedTimeout" value="1800" />
        <property name="logAbandoned" value="true" />
        <property name="driverClassName" value="oracle.jdbc.OracleDriver" />
    </bean>

    <bean id="dwhMapperScannerConfigurer" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="sqlSessionFactoryBeanName" value="dwhSqlSessionFactory" />
        <property name="basePackage" value="com.vedeng.dwh.externaldb.dao" />
<!--        <property name="annotationClass" value="javax.inject.Named" />-->
    </bean>

    <bean id="dwhTransactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dwhDataSource" />
    </bean>

    <tx:annotation-driven transaction-manager="dwhTransactionManager" proxy-target-class="true" />
</beans>
