package com.vedeng.api.standard.adapter.peerlist.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * 直发同行单响应
 *
 * <AUTHOR>
 */
@Data
public class PeerListResponse implements Serializable {
    
    /**
     * 同行单信息ID
     */
    private Integer purchaseDeliveryDirectBatchInfoId;
    
    /**
     * 采购单ID
     */
    private Integer buyorderId;
    
    /**
     * 创建者ID
     */
    private Integer creator;
    
    /**
     * 操作结果描述
     */
    private String message;
    
    /**
     * 操作是否成功
     */
    private Boolean success;
    
    /**
     * 创建一个成功的响应
     */
    public static PeerListResponse success(Integer purchaseDeliveryDirectBatchInfoId, Integer buyorderId) {
        PeerListResponse response = new PeerListResponse();
        response.setPurchaseDeliveryDirectBatchInfoId(purchaseDeliveryDirectBatchInfoId);
        response.setBuyorderId(buyorderId);
        response.setSuccess(true);
        response.setMessage("直发同行单创建成功");
        return response;
    }
    
    /**
     * 创建一个失败的响应
     */
    public static PeerListResponse error(String message) {
        PeerListResponse response = new PeerListResponse();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }
    
    private static final long serialVersionUID = 1L;
} 