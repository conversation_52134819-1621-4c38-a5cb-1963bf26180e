package com.vedeng.crm.visitrecord.domain.dto;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/2/28
 */
public enum VisitRecordStatusEnum {
    //1.待拜访；2.拜访中；3.已拜访；4.已关闭;
    WAIT_VISIT(1, "待拜访"),
    VISITING(2, "拜访中"),
    VISITED(3, "已拜访"),
    CLOSED(4, "已关闭");

    private Integer recordStatus ;

    private String recordStatusName;


    VisitRecordStatusEnum(Integer recordStatus, String recordStatusName) {
        this.recordStatus = recordStatus;
        this.recordStatusName = recordStatusName;
    }

    public Integer getRecordStatus() {
        return recordStatus;
    }

    public String getRecordStatusName() {
        return recordStatusName;
    }
}
