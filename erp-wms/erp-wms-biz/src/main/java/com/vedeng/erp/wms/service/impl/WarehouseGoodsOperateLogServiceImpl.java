package com.vedeng.erp.wms.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.vedeng.erp.wms.domain.entity.BuyOrderAfterSaleDirectOutLogEntity;
import com.vedeng.erp.wms.domain.entity.WmsWarehouseGoodsOperateLogEntity;
import com.vedeng.erp.wms.dto.AfterSaleBuyorderDirectOutLogDto;
import com.vedeng.erp.wms.dto.WarehouseGoodsOperateLogDto;
import com.vedeng.erp.wms.mapper.BuyOrderAfterSaleDirectOutLogMapper;
import com.vedeng.erp.wms.mapper.WmsWarehouseGoodsOperateLogMapper;
import com.vedeng.erp.wms.mapstruct.AfterSaleBuyorderDirectOutLogConvertor;
import com.vedeng.erp.wms.mapstruct.WmsWarehouseGoodsOperateLogConvertor;
import com.vedeng.erp.wms.service.WarehouseGoodsOperateLogApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 出入库操作日志实现类
 */
@Slf4j
@Service
public class WarehouseGoodsOperateLogServiceImpl implements WarehouseGoodsOperateLogApiService {

    @Autowired
    WmsWarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Autowired
    BuyOrderAfterSaleDirectOutLogMapper afterSaleBuyorderDirectOutLogMapper;

    @Autowired
    WmsWarehouseGoodsOperateLogConvertor wmsWarehouseGoodsOperateLogConvertor;

    @Autowired
    AfterSaleBuyorderDirectOutLogConvertor afterSaleBuyorderDirectOutLogConvertor;

    @Override
    public List<WarehouseGoodsOperateLogDto> findWarehouseByAll(WarehouseGoodsOperateLogDto warehouseGoodsOperateLogDto) {
        log.info("出入库操作日志实现类findByAll 入参：{}", JSONUtil.toJsonStr(warehouseGoodsOperateLogDto));
        WmsWarehouseGoodsOperateLogEntity warehouseGoodsOperateLogEntity = wmsWarehouseGoodsOperateLogConvertor.toEntity(warehouseGoodsOperateLogDto);
        List<WmsWarehouseGoodsOperateLogEntity> all = warehouseGoodsOperateLogMapper.findByAll(warehouseGoodsOperateLogEntity);
        if (CollUtil.isEmpty(all)) {
            return null;
        }
        List<WarehouseGoodsOperateLogDto> warehouseGoodsOperateLogDtos = wmsWarehouseGoodsOperateLogConvertor.toDto(all);
        log.info("出入库操作日志实现类findWarehouseByAll 出参数量：{}", warehouseGoodsOperateLogDtos.size());
        return warehouseGoodsOperateLogDtos;
    }

    @Override
    public List<AfterSaleBuyorderDirectOutLogDto> findBuyOrderAfterSaleDirectByAll(AfterSaleBuyorderDirectOutLogDto afterSaleBuyorderDirectOutLogDto) {
        log.info("采购售后直发出入库操作日志实现类findByAll 入参：{}", JSONUtil.toJsonStr(afterSaleBuyorderDirectOutLogDto));
        BuyOrderAfterSaleDirectOutLogEntity directOutLogEntity = afterSaleBuyorderDirectOutLogConvertor.toEntity(afterSaleBuyorderDirectOutLogDto);
        List<BuyOrderAfterSaleDirectOutLogEntity> all = afterSaleBuyorderDirectOutLogMapper.findByAll(directOutLogEntity);
        if (CollUtil.isEmpty(all)) {
            return null;
        }
        List<AfterSaleBuyorderDirectOutLogDto> AfterSaleBuyorderDirectOutLogDtos = afterSaleBuyorderDirectOutLogConvertor.toDto(all);
        log.info("采购售后直发出入库操作日志实现类findBuyOrderAfterSaleDirectByAll 出参数量：{}", AfterSaleBuyorderDirectOutLogDtos.size());
        return AfterSaleBuyorderDirectOutLogDtos;
    }
}
