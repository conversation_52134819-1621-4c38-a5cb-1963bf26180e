package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceDetailJOperateLogDto;
import com.vedeng.erp.kingdee.batch.repository.BatchRInvoiceDetailJOperateLogDtoMapper;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.OperateParam;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.OperateExtCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购应付单以及票推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchPurchaseReversalInvoiceWriter extends BaseWriter<KingDeeInvoiceRollBackDto> {


    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private BatchRInvoiceDetailJOperateLogDtoMapper batchRInvoiceDetailJOperateLogDtoMapper;

    @Override
    public void doWrite(KingDeeInvoiceRollBackDto item, JobParameters params, ExecutionContext stepContext) throws Exception {

        log.info("采购冲销票：{}原蓝票以及应付单驳回并删除:{}",item.getInvoiceId(),JSON.toJSONString(item));
        if (CollUtil.isNotEmpty(item.getOriginalInvoice())) {

            List<KingDeeInvoiceRollBackDto.KingDeeInvoiceAndId> originalInvoice = item.getOriginalInvoice();

            for (KingDeeInvoiceRollBackDto.KingDeeInvoiceAndId data : originalInvoice) {

                if (data.isSpecial()) {
                    if (StrUtil.isNotEmpty(data.getFid())) {
                        OperateParam operateParam = new OperateParam();
                        operateParam.setIds(data.getFid());
                        kingDeeBaseApi.unAudit(KingDeeFormConstant.PURCHASE_VAT_SPECIAL_INVOICE,operateParam);
                        kingDeeBaseApi.delete(KingDeeFormConstant.PURCHASE_VAT_SPECIAL_INVOICE,operateParam);

                    }

                } else {
                    if (StrUtil.isNotEmpty(data.getFid())) {
                        OperateParam operateParam = new OperateParam();
                        operateParam.setIds(data.getFid());
                        kingDeeBaseApi.unAudit(KingDeeFormConstant.PURCHASE_VAT_PLAIN_INVOICE,operateParam);
                        kingDeeBaseApi.delete(KingDeeFormConstant.PURCHASE_VAT_PLAIN_INVOICE,operateParam);
                    }

                }

                if (StrUtil.isNotEmpty(data.getPayId())) {
                    OperateParam operateParam = new OperateParam();
                    operateParam.setIds(data.getPayId());
                    kingDeeBaseApi.unAudit(KingDeeFormConstant.PAY_EXPENSES,operateParam);
                    ArrayList<SuccessEntity> delete = kingDeeBaseApi.delete(KingDeeFormConstant.PAY_EXPENSES,operateParam);
                    if (CollUtil.isNotEmpty(delete)) {
                        List<BatchRInvoiceDetailJOperateLogDto> byInvoiceIds = batchRInvoiceDetailJOperateLogDtoMapper.findByInvoiceIds(Collections.singletonList(data.getId()));

                        if (CollUtil.isNotEmpty(byInvoiceIds)) {
                            log.info("金蝶冲销回滚发票删除货票关系,invoiceId:{},data:{}",item.getInvoiceId(),JSON.toJSONString(byInvoiceIds));
                            List<Integer> collect = byInvoiceIds.stream().map(BatchRInvoiceDetailJOperateLogDto::getRInvoiceDetailJOperateLogId).collect(Collectors.toList());
                            batchRInvoiceDetailJOperateLogDtoMapper.deleteByRInvoiceDetailJOperateLogId(collect);
                        }
                    }
                }


            }

        }



    }

}
