<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
            <table class="table  table-style6">
                <thead>
                    <tr>
                        <th class="wid6">序号</th>
                        <th class="wid40">产品信息</th>
						<th class="wid12">物料编号</th>
						<th class="wid10">所属采购单号</th>
                        <th class="wid8">单价</th>
                        <th class="wid8">原预定总数</th>
                        <th class="wid5">销售发货方式</th>
						<th class="wid5">换货方式</th>
                        <th>已采购数量</th>
                        <th>已发货数量</th>
                        <th>已收货数量</th>
                        <th>换货数量</th>
                    </tr>
                </thead>
                <tbody>
                	<c:if test="${not empty afterSalesGoodsVoListPage}">
                		<c:forEach items="${afterSalesGoodsVoListPage}" var="asg" varStatus="sttaus">
	                		<tr class="J-skuInfo-tr" skuId="${asg.goodsId}">
		                        <td>${sttaus.count }</td>
		                         <td class="text-left">
									<div class="customername pos_rel">
										<c:if test="${asg.isActionGoods==1}"><span style="color:red;">【活动】</span></c:if>
										<c:if test="${asg.isGift eq '1'}">
											<img src="<%= basePath %>static/images/gift_icon.svg" style="width: 15px" />
										</c:if>
										<c:if test="${asg.isDirectPurchase == 1}">
											<img id="isDirectPurchase" src="<%= basePath %>static/images/promote_icon.svg" style="width: 15px" title="商城&quot;现货现价&quot;促销"/>
										</c:if>
										   <span class="brand-color1 addtitle JskuName" style="float:none;" tabTitle='{"num":"viewgoods<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./goods/goods/viewbaseinfo.do?goodsId=${asg.goodsId}","title":"产品信息"}'>${asg.goodsName}</span>

										<br><span class="JskuCode"></span>
										<i class="iconbluemouth"></i>
									   <div class="pos_abs customernameshow JskuInfo" style="display: none;">
									   </div>
										<br>
										<span style="font-weight: bold">品牌：</span><span class="JbrandName"></span>
										<br>
										<span style="font-weight: bold">规格/型号：</span><span class="JskuModel"></span>
										<br>
										<span style="font-weight: bold">单位：</span><span class="JskuUnit"></span>
									</div>
		                        </td>
								</td>
								<td class="JmaterialCode"> </td>
								<td>
									<c:forEach items="${asg.buyorderNos}" var="buyorder">
										${buyorder.buyorderNo}<br>
									</c:forEach>
								</td>
								<td><fmt:formatNumber type="number" value="${asg.saleorderPrice}" pattern="0.00" maxFractionDigits="2" /></td>
		                        <td>${asg.saleorderNum}</td>
								<td>
									<c:if test="${asg.saleorderDeliveryDirect eq 0}">普发</c:if>
									<c:if test="${asg.saleorderDeliveryDirect eq 1}">直发</c:if>
								</td>
								<td class="warning-color1">
									<c:if test="${asg.deliveryDirect eq 0}">普发</c:if>
									<c:if test="${asg.deliveryDirect eq 1}">直发</c:if>
								</td>
		                        <td>${asg.buyNum}</td>
		                        <td>${asg.saleorderDelivery}</td>
		                        <td>${asg.receiveNum}</td>
		                        <td class="warning-color1">${asg.num}</td>
		                    </tr>
	                	</c:forEach>
                     <tr>
                        <td colspan="12" class="allchosetr text-left">
                        	<c:set var="sum" value="0"></c:set>
                        	<c:forEach items="${afterSalesGoodsVoListPage}" var="asg">
                        		<c:set var="sum" value="${sum+asg.num}"></c:set>
                        	</c:forEach>
                            	 换货总件数:<span class="warning-color1 mr10">${sum}</span>
                        </td>
                    </tr>
                    </c:if>
                    <c:if test="${empty afterSalesGoodsVoListPage}">
	                    <tr>
	                        <td colspan="15">暂无记录</td>
	                    </tr>
                    </c:if>
                </tbody>
            </table>
<script type="text/javascript" src='<%= basePath %>static/new/js/pages/goods/goodinfoajax.js?rnd=${resourceVersionKey}'></script>
