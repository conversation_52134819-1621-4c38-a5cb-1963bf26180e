package com.vedeng.erp.buyorder.web.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * 采购-快递
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/buyOrder/express")
public class BuyOrderExpressController {

    /**
     * 需要物流人员确认的采购直发快递
     *
     * @return ModelAndView
     */
    @NoNeedAccessAuthorization
    @RequestMapping("/needConfirmList")
    public ModelAndView index() {
        return new ModelAndView("vue/view/buyorderexpress/need_confirm_list");
    }

}