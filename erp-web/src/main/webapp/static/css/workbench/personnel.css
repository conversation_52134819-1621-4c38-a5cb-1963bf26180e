.personnel-wrapper {
  max-width: 1280px;
  min-width: 960px;
  margin: 0 auto;
  padding: 20px 0;
  display: flex;
}
.personnel-wrapper .pw-left {
  flex: 1;
  margin-right: 20px;
}
.personnel-wrapper .pw-left .common-box {
  background-color: #fff;
  margin-bottom: 10px;
}
.personnel-wrapper .pw-left .common-box:last-child {
  margin-bottom: 0;
}
.personnel-wrapper .pw-left .common-box .cb-title {
  padding: 10px 20px;
  font-size: 24px;
  border-bottom: solid 1px #EDF0F2;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box {
  padding: 20px 0;
  border-bottom: solid 1px #EDF0F2;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box:last-child {
  border-bottom: none;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-title {
  padding: 0 20px;
  font-size: 18px;
  margin-bottom: 5px;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content {
  padding: 0 15px;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list {
  display: flex;
  flex-wrap: wrap;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list .ib-item {
  border: solid 1px #EDF0F2;
  width: calc(33.3% - 10px);
  margin: 5px 5px 0;
  display: flex;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list .ib-item .item-left {
  width: 20px;
  background-color: #0099ff;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list .ib-item .item-right {
  flex: 1;
  padding: 10px;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list .ib-item .item-right .item-r-title {
  font-weight: 700;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list .ib-item .item-right .item-r-content {
  margin-top: 5px;
  display: flex;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list .ib-item .item-right .item-r-content .total-num {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 50px;
  margin-right: 10px;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list .ib-item .item-right .item-r-content .total-num a {
  font-size: 24px;
  color: #0099ff;
  text-decoration: underline;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list .ib-item .item-right .item-r-content .total-num a:hover {
  color: #ff6600;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list .ib-item .item-right .item-r-content .total-desc {
  flex: 1;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list .ib-item .item-right .item-r-content .task-list {
  flex: 1;
  display: flex;
  justify-content: space-around;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list .ib-item .item-right .item-r-content .task-list .task-item {
  text-align: center;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list .ib-item .item-right .item-r-content .task-list .task-item .ti-num a {
  font-size: 24px;
  color: #0099ff;
  text-decoration: underline;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list .ib-item .item-right .item-r-content .task-list .task-item .ti-num a.red {
  color: #E64545;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list .ib-item .item-right .item-r-content .task-list .task-item .ti-num a:hover {
  color: #ff6600;
}
.personnel-wrapper .pw-left .common-box .cb-content .info-box .ib-content .ib-list .ib-item .item-right .item-r-content .task-list .task-item .ti-name {
  margin-top: 5px;
}
.personnel-wrapper .pw-left .common-box .cb-content .table-box {
  padding: 20px;
  border-bottom: solid 1px #EDF0F2;
}
.personnel-wrapper .pw-left .common-box .cb-content .table-box:last-child {
  border-bottom: none;
}
.personnel-wrapper .pw-left .common-box .cb-content .table-box .tb-title {
  font-size: 18px;
  margin-bottom: 10px;
}
.personnel-wrapper .pw-left .common-box .cb-content .table-box .tb-content .thead {
  background-color: #F5F7FA;
}
.personnel-wrapper .pw-left .common-box .cb-content .table-box .tb-content .thead .thead-tr {
  display: flex;
  border-top: solid 1px #EDF0F2;
  border-left: solid 1px #EDF0F2;
  border-bottom: solid 1px #EDF0F2;
}
.personnel-wrapper .pw-left .common-box .cb-content .table-box .tb-content .thead .thead-tr .thead-th {
  color: #999999;
  text-align: center;
  flex: 1;
  padding: 10px 0;
  border-right: solid 1px #EDF0F2;
}
.personnel-wrapper .pw-left .common-box .cb-content .table-box .tb-content .tbody .tbody-tr {
  display: flex;
  border-left: solid 1px #EDF0F2;
  border-bottom: solid 1px #EDF0F2;
}
.personnel-wrapper .pw-left .common-box .cb-content .table-box .tb-content .tbody .tbody-tr .tbody-td {
  text-align: center;
  flex: 1;
  padding: 5px 0;
  border-right: solid 1px #EDF0F2;
}
.personnel-wrapper .pw-left .common-box .cb-content .table-box .tb-content .tbody .tbody-tr .tbody-td .can-click {
  color: #0099ff;
  text-decoration: underline;
}
.personnel-wrapper .pw-left .common-box .cb-content .table-box .tb-content .tbody .tbody-tr .tbody-td .can-click:hover {
  color: #ff6600;
}
.personnel-wrapper .pw-right {
  width: 350px;
}
.personnel-wrapper .pw-right .message-total {
  padding: 10px 20px;
  background-color: #fff;
  display: flex;
  margin-bottom: 10px;
}
.personnel-wrapper .pw-right .message-total .mt-left {
  font-size: 18px;
  display: flex;
  align-items: center;
}
.personnel-wrapper .pw-right .message-total .mt-right {
  flex: 1;
  text-align: center;
}
.personnel-wrapper .pw-right .message-total .mt-right .num a {
  font-size: 24px;
  font-weight: 700;
  color: #E64545;
  text-decoration: underline;
}
.personnel-wrapper .pw-right .message-total .mt-right .num a:hover {
  color: #ff6600;
}
.personnel-wrapper .pw-right .message-list {
  background-color: #fff;
  padding: 10px 20px;
}
.personnel-wrapper .pw-right .message-list .ml-ul .ml-item {
  padding: 10px 0;
  border-bottom: solid 1px #EDF0F2;
}
.personnel-wrapper .pw-right .message-list .ml-ul .ml-item:last-child {
  border-bottom: none;
}
.personnel-wrapper .pw-right .message-list .ml-ul .ml-item .item-desc {
  font-weight: 700;
}
.personnel-wrapper .pw-right .message-list .ml-ul .ml-item .item-link {
  max-height: 42px;
}
.personnel-wrapper .pw-right .message-list .ml-ul .ml-item .item-link a {
  color: #0099ff;
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.personnel-wrapper .pw-right .message-list .ml-ul .ml-item .item-link a:hover {
  color: #ff6600;
}
.personnel-wrapper .pw-right .message-list .ml-ul .ml-item .item-time {
  text-align: right;
  color: #999999;
}
.personnel-wrapper .pw-right .message-list .ml-link-more {
  margin-top: 10px;
  text-align: right;
}
.personnel-wrapper .pw-right .message-list .ml-link-more a:hover {
  color: #ff6600;
}
