package com.vedeng.common.trace.track.factory;

import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.EnumMap;
import java.util.Map;

/**
 * 工厂类
 * @ClassName:  TrackStrategyFactory
 * @author: <PERSON>.yang
 * @date:   2024年6月3日 下午3:28:17
 * @Copyright:
 */
@Component
public class TrackStrategyFactory implements ApplicationContextAware {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(TrackStrategyFactory.class);

    /**spring上下文*/
    private ApplicationContext applicationContext;

    /**实现埋点集合*/
    private EnumMap<EventTrackingEnum, TrackStrategy> trackStrategyMap = new EnumMap<>(EventTrackingEnum.class);

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    private void init() {
    	LOGGER.info("=======TrackStrategyFactory init...=======");
        Map<String, TrackStrategy> beansMap = applicationContext.getBeansOfType(TrackStrategy.class);
        beansMap.forEach((k,v)-> this.trackStrategyMap.put(v.getTrackStrategy(),v));
    }

    public TrackStrategy getStrategyByType(EventTrackingEnum eventTrackingEnum) {
        return trackStrategyMap.get(eventTrackingEnum);
    }
}
