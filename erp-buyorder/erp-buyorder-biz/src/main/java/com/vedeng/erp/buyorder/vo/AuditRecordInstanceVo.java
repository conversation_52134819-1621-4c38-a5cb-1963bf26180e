package com.vedeng.erp.buyorder.vo;

import lombok.Data;

@Data
public class AuditRecordInstanceVo {

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private String operateTime;

    /**
     * 操作事项
     */
    private String operateInstance;

    /**
     * 备注
     */
    private String comment;

    public AuditRecordInstanceVo() {
    }

    public AuditRecordInstanceVo(String operatorName, String operateTime, String operateInstance, String comment) {
        this.operatorName = operatorName;
        this.operateTime = operateTime;
        this.operateInstance = operateInstance;
        this.comment = comment;
    }
}
