<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.logistics.dao.ExpressOprateLogMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.ExpressOprateLog" >
    <id column="R_EXPRESS_OPERATE_LOG" property="rExpressOperateLog" jdbcType="INTEGER" />
    <result column="EXPRESS_DETAIL_ID" property="expressDetailId" jdbcType="INTEGER" />
    <result column="WAREHOUSE_GOODS_OPERATE_LOG_ID" property="warehouseGoodsOperateLogId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    R_EXPRESS_OPERATE_LOG, EXPRESS_DETAIL_ID, WAREHOUSE_GOODS_OPERATE_LOG_ID
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_R_EXPRESS_OPERATE_LOG
    where R_EXPRESS_OPERATE_LOG = #{rExpressOperateLog,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_R_EXPRESS_OPERATE_LOG
    where R_EXPRESS_OPERATE_LOG = #{rExpressOperateLog,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.logistics.model.ExpressOprateLog" >
    insert into T_R_EXPRESS_OPERATE_LOG (R_EXPRESS_OPERATE_LOG, EXPRESS_DETAIL_ID, 
      WAREHOUSE_GOODS_OPERATE_LOG_ID)
    values (#{rExpressOperateLog,jdbcType=INTEGER}, #{expressDetailId,jdbcType=INTEGER}, 
      #{warehouseGoodsOperateLogId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.logistics.model.ExpressOprateLog" >
    insert into T_R_EXPRESS_OPERATE_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="rExpressOperateLog != null" >
        R_EXPRESS_OPERATE_LOG,
      </if>
      <if test="expressDetailId != null" >
        EXPRESS_DETAIL_ID,
      </if>
      <if test="warehouseGoodsOperateLogId != null" >
        WAREHOUSE_GOODS_OPERATE_LOG_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="rExpressOperateLog != null" >
        #{rExpressOperateLog,jdbcType=INTEGER},
      </if>
      <if test="expressDetailId != null" >
        #{expressDetailId,jdbcType=INTEGER},
      </if>
      <if test="warehouseGoodsOperateLogId != null" >
        #{warehouseGoodsOperateLogId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.logistics.model.ExpressOprateLog" >
    update T_R_EXPRESS_OPERATE_LOG
    <set >
      <if test="expressDetailId != null" >
        EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER},
      </if>
      <if test="warehouseGoodsOperateLogId != null" >
        WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER},
      </if>
    </set>
    where R_EXPRESS_OPERATE_LOG = #{rExpressOperateLog,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.logistics.model.ExpressOprateLog" >
    update T_R_EXPRESS_OPERATE_LOG
    set EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER},
      WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
    where R_EXPRESS_OPERATE_LOG = #{rExpressOperateLog,jdbcType=INTEGER}
  </update>
  <!-- 批量保存 -->
  <insert id="insertSelectiveBatch" parameterType="com.vedeng.logistics.model.ExpressOprateLog" >
  <foreach item="data" index="index" collection="list" separator=";">
    insert into T_R_EXPRESS_OPERATE_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="data.expressDetailId != null" >
        EXPRESS_DETAIL_ID,
      </if>
      <if test="data.warehouseGoodsOperateLogId != null" >
        WAREHOUSE_GOODS_OPERATE_LOG_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="data.expressDetailId != null" >
        #{data.expressDetailId,jdbcType=INTEGER},
      </if>
      <if test="data.warehouseGoodsOperateLogId != null" >
        #{data.warehouseGoodsOperateLogId,jdbcType=INTEGER},
      </if>
    </trim>
    </foreach>
  </insert>
  <!-- 根据快递详情id查询列表 -->
   <select id="getExpressOprateLogList" resultMap="BaseResultMap" parameterType="java.util.List" >
         SELECT
			WAREHOUSE_GOODS_OPERATE_LOG_ID
		FROM
			T_R_EXPRESS_OPERATE_LOG
		WHERE
			EXPRESS_DETAIL_ID IN
    	<foreach collection="list" item="wlog" separator="," open="(" close=")">
					#{wlog.expressDetailId,jdbcType=INTEGER}
		</foreach>
  </select>
  <!-- 批量删除绑定关系 -->
  <delete id="deleteByPrimaryKeyBatch" parameterType="java.util.List" >
    delete from T_R_EXPRESS_OPERATE_LOG
    where EXPRESS_DETAIL_ID IN
    <foreach collection="list" item="wlog" separator="," open="(" close=")">
		#{wlog.expressDetailId,jdbcType=INTEGER}
	</foreach>
  </delete>
</mapper>