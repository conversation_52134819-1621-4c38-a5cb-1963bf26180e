package com.vedeng.erp.trader.web.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/18 15:26
 */
@Controller
@ExceptionController
@RequestMapping("/communicateSummary")
public class CommunicateSummaryController {

    @RequestMapping(value = "/modify/dialog")
    @NoNeedAccessAuthorization
    public ModelAndView modifySummaryPage(Integer communicateSummaryId) {
        ModelAndView view = new ModelAndView("vue/view/tradercustomer/modify_communicate_summary");
        view.addObject("communicateSummaryId", communicateSummaryId);
        return view;
    }

}