package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveRefundBillDto;
import com.vedeng.erp.kingdee.service.impl.KingDeeReceiveRefundBillServiceImpl;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 金蝶收款退款单推送 构造器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/24 16:59
 */
@Service
@Slf4j
public class ReceiptRefundBillWriter extends BaseWriter<KingDeeReceiveRefundBillDto> {

    @Autowired
    private KingDeeReceiveRefundBillServiceImpl kingDeeReceiveRefundBillServiceImpl;
    @Override
    public void doWrite(KingDeeReceiveRefundBillDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("金蝶收款退款单推送数据{}", JSONObject.toJSONString(item));
        //推送金蝶
        item.setKingDeeBizEnums(KingDeeBizEnums.saveReceiveRefundBill);
        kingDeeReceiveRefundBillServiceImpl.register(item,true);
    }
}
