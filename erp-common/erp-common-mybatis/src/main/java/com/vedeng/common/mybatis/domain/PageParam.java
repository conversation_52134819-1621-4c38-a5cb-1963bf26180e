package com.vedeng.common.mybatis.domain;

import com.github.pagehelper.IPage;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 分页参数
 * @date 2022/3/7 19:02
 */
@Data
@Accessors(chain = true)
public class PageParam<T> implements IPage {

    /**
     * description = "页码",
     * defaultValue =  1
     */
    private Integer pageNum = 1;

    /**
     * description = "页数",
     * defaultValue = 20
     */
    private Integer pageSize = 10;

    /**
     * description = "排序",
     * example = "id desc"
     */
    private String orderBy;

    /**
     * description = "参数"
     */
    private T param;

}
