package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.saleorder.dto.AfterSalesGoodsDto;
import com.vedeng.erp.trader.api.CommunicateVoiceTaskApi;
import com.vedeng.erp.trader.constant.AiConstant;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.domain.entity.CommunicateRecordEntity;
import com.vedeng.erp.trader.domain.entity.RCommunicateTodoJAiEntity;
import com.vedeng.erp.trader.domain.entity.TraderContactEntity;
import com.vedeng.erp.trader.domain.entity.VoiceFieldResultEntity;
import com.vedeng.erp.trader.domain.vo.AiCommunicateRecordSummary;
import com.vedeng.erp.trader.domain.vo.AiCommunicateRecordVo;
import com.vedeng.erp.trader.dto.CommunicateVoiceTaskDto;
import com.vedeng.erp.trader.dto.RCommunicateTodoJAiDto;
import com.vedeng.erp.trader.dto.TraderCustomerDto;
import com.vedeng.erp.trader.dto.VoiceFieldResultDto;
import com.vedeng.erp.trader.mapper.*;
import com.vedeng.erp.trader.mapstruct.RCommunicateTodoJAiConvertor;
import com.vedeng.erp.trader.mapstruct.VoiceFieldResultConvertor;
import com.vedeng.erp.trader.service.CommunicateRecordService;
import com.vedeng.erp.trader.service.VoiceFieldResultService;
import org.apache.axis2.util.ArrayStack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class VoiceFieldResultServiceImpl implements VoiceFieldResultService {

    @Autowired
    private TraderVoiceFieldResultMapper traderVoiceFieldResultMapper;
    @Autowired
    private CommunicateRecordService communicateRecordService;
    @Autowired
    private RCommunicateTodoJAiMapper rCommunicateTodoJAiMapper;
    @Autowired
    VoiceFieldResultConvertor voiceFieldResultConvertor;
    @Autowired
    RCommunicateTodoJAiConvertor rCommunicateTodoJAiConvertor;
    @Autowired
    TraderCustomerBaseMapper traderCustomerBaseMapper;
    @Autowired
    CommunicateRecordMapper communicateRecordMapper;
    @Autowired
    AfterSalesApiService afterSalesApiService;
    @Autowired
    TraderContactMapper traderContactMapper;
    @Autowired
    CommunicateVoiceTaskApi communicateVoiceTaskApi;

    @Value("${voice.minCoidLength:30}")
    private Integer minCoidLength;

    @Value("${voice.maxCoidLength:600}")
    private Integer maxCoidLength;

    @Override
    public int insertSelective(VoiceFieldResultEntity record) {
        return traderVoiceFieldResultMapper.insertSelective(record);
    }

    @Override
    public int deleteByPrimaryKey(Integer fieldResultId) {
        return traderVoiceFieldResultMapper.deleteByPrimaryKey(fieldResultId);
    }

    @Override
    public int insert(VoiceFieldResultEntity record) {
        return traderVoiceFieldResultMapper.insert(record);
    }

    @Override
    public VoiceFieldResultEntity selectByPrimaryKey(Integer fieldResultId) {
        return traderVoiceFieldResultMapper.selectByPrimaryKey(fieldResultId);
    }

    @Override
    public int updateByPrimaryKeySelective(VoiceFieldResultEntity record) {
        return traderVoiceFieldResultMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(VoiceFieldResultEntity record) {
        return traderVoiceFieldResultMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<VoiceFieldResultEntity> selectByCommunicateRecordIdAndSence(Integer communicateRecordId, String senceCode) {
        return traderVoiceFieldResultMapper.selectByCommunicateRecordIdAndSence(communicateRecordId, senceCode);
    }

    @Override
    public List<VoiceFieldResultEntity> selectByCommunicateRecordIdAndSenceGroup(Integer communicateRecordId, String senceCode, String groupCode) {
        return traderVoiceFieldResultMapper.selectByCommunicateRecordIdAndSenceGroup(communicateRecordId, senceCode, groupCode);
    }

    @Override
    public AiCommunicateRecordVo selectByCommunicateRecordId(Integer communicateRecordId) {
        CommunicateRecordDto dto = new CommunicateRecordDto();
        dto.setCommunicateRecordId(communicateRecordId);
        CommunicateRecordDto recordDto = communicateRecordService.getOne(dto);
        dto.setCoidType(recordDto.getCoidType());

        List<VoiceFieldResultEntity> byCommunicateRecordId = traderVoiceFieldResultMapper.findByCommunicateRecordId(communicateRecordId);
        Map<String, List<VoiceFieldResultEntity>> collect = byCommunicateRecordId.stream().collect(Collectors.groupingBy(VoiceFieldResultEntity::getGroupCode));
        String beginTime = DateUtil.format(DateUtil.date(recordDto.getBegintime()), "yyyy-MM-dd HH:mm:ss");
        String endtime = DateUtil.format(DateUtil.date(recordDto.getEndtime()), "yyyy-MM-dd HH:mm:ss");

        List<VoiceFieldResultEntity> groupSummary = collect.get("GROUP_SUMMARY");
        List<AiCommunicateRecordVo.Digest> digestList = new ArrayList<>();
        if (CollUtil.isNotEmpty(groupSummary)) {
            groupSummary.stream().filter(g -> StrUtil.isNotEmpty(g.getFieldResult())).forEach(g -> {
                AiCommunicateRecordVo.Digest digest = new AiCommunicateRecordVo.Digest();
                digest.setId(g.getFieldResultId());
                digest.setValue(g.getFieldName() + ":" + g.getFieldResult());
                digest.setDisabled("Y".equals(g.getDoFlag()));
                digestList.add(digest);
            });
        }

        RCommunicateTodoJAiEntity rCommunicateTodoJAiEntity = rCommunicateTodoJAiMapper.findByCommunicateRecordId(communicateRecordId);
        RCommunicateTodoJAiDto rCommunicateTodoJAiDto = rCommunicateTodoJAiEntity == null ? new RCommunicateTodoJAiDto() :
                rCommunicateTodoJAiConvertor.toDto(rCommunicateTodoJAiEntity);
        if (rCommunicateTodoJAiDto != null && rCommunicateTodoJAiDto.getTraderId() != null) {
            List<TraderCustomerDto> traderCustomerListByTraderIds = traderCustomerBaseMapper.getTraderCustomerListByTraderIds(CollUtil.newArrayList(rCommunicateTodoJAiDto.getTraderId()));
            if (CollUtil.isNotEmpty(traderCustomerListByTraderIds)) {
                rCommunicateTodoJAiDto.setTraderCustomerName(CollUtil.getFirst(traderCustomerListByTraderIds).getTraderName());
                rCommunicateTodoJAiDto.setTraderCustomerId(CollUtil.getFirst(traderCustomerListByTraderIds).getTraderCustomerId());
            }
        }


        List<VoiceFieldResultEntity> todotask = collect.get("GROUP_TODOTASK");
        List<VoiceFieldResultDto> todotaskList = voiceFieldResultConvertor.toDto(todotask) == null ? new ArrayList<>() : voiceFieldResultConvertor.toDto(todotask);

        // 获取客户基本信息
        TraderCustomerDto traderCustomerDto = new TraderCustomerDto();
        if (recordDto.getTraderId() != null) {
            List<TraderCustomerDto> traderCustomerListByTraderIds = traderCustomerBaseMapper.getTraderCustomerListByTraderIds(CollUtil.newArrayList(recordDto.getTraderId()));
            if (CollUtil.isNotEmpty(traderCustomerListByTraderIds)) {
                traderCustomerDto = CollUtil.getFirst(traderCustomerListByTraderIds);
            }
        }
        // 发送售后政策待办单独处理
        if (CollUtil.isNotEmpty(todotaskList)) {
            todotaskList.forEach(t -> {
                List<VoiceFieldResultDto.AfterSaleGoodsTodo> afterSaleGoodsTodos = new ArrayStack();
                if ("sendAfterSalesPolicy".equals(t.getFieldCode()) && "是".equals(t.getFieldResult())) {
                    if (recordDto.getRelatedId() != null && recordDto.getRelatedId() != 0) {
                        List<AfterSalesGoodsDto> afterSalesGoodsByAfterSalesId = afterSalesApiService.getAfterSalesGoodsByAfterSalesId(recordDto.getRelatedId());

                        afterSalesGoodsByAfterSalesId.forEach(a -> {
                            VoiceFieldResultDto.AfterSaleGoodsTodo afterSaleGoodsTodo = new VoiceFieldResultDto.AfterSaleGoodsTodo();
                            afterSaleGoodsTodo.setSkuName(a.getSkuName());
                            afterSaleGoodsTodo.setSkuNo(a.getSku());
                            afterSaleGoodsTodos.add(afterSaleGoodsTodo);
                        });

                    }
                }
                t.setAfterSaleGoodsTodos(afterSaleGoodsTodos);
            });
        }

        // 获取沟通记录中的联系人
        TraderContactEntity traderContactEntity = traderContactMapper.selectByTraderContactId(recordDto.getTraderContactId());

        CommunicateVoiceTaskDto communicateVoiceTaskDto = communicateVoiceTaskApi.selectByCommunicateRecordId(communicateRecordId);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        String tipsTemplate = "{} , 贝壳助理目前只解析2024年5月30日之后 {} 分钟 - {} 分钟之间的通话录音哦，贝壳将继续努力，增加分析范围。";
        String tip = StrUtil.format(tipsTemplate, currentUser.getUsername(), this.convertToText(minCoidLength), this.convertToText(maxCoidLength));
        boolean exist = communicateVoiceTaskDto != null && !Objects.equals(communicateVoiceTaskDto.getVoiceStatus(), "-1");

        return AiCommunicateRecordVo
                .builder()
                .rCommunicateTodoJAiDto(rCommunicateTodoJAiDto)
                .traderId((traderCustomerDto.getTraderId() == null || traderCustomerDto.getTraderId().equals(0)) ? null : traderCustomerDto.getTraderId())
                .traderContactId((recordDto.getTraderContactId() == null || recordDto.getTraderContactId().equals(0)) ? null : recordDto.getTraderContactId())
                .traderCustomerId((traderCustomerDto.getTraderId() == null || traderCustomerDto.getTraderCustomerId().equals(0)) ? null : traderCustomerDto.getTraderCustomerId())
                .traderCustomerName(traderCustomerDto.getTraderName())
                .traderContactPhone(recordDto.getPhone())
                .digestList(digestList)
                .todoList(todotaskList)
                .contactDate(beginTime + "-" + endtime)
                .contactType(AiConstant.getEnumByCommunicateType(recordDto.getCommunicateType()).getName())
                .traderContactName(traderContactEntity != null ? traderContactEntity.getName() : recordDto.getContact())
                .communicateRecordId(recordDto.getCommunicateRecordId())
                .exist(exist)
                .tip(exist ? "" : tip)
                .coidType(recordDto.getCoidType())
                .build();
    }


    private String convertToText(Integer coidLength) {
        double result = (double) coidLength / 60;

        DecimalFormat df = new DecimalFormat("0.00");
        String formatted = df.format(result);

        DecimalFormat df2 = new DecimalFormat("0.#");
        return df2.format(Double.parseDouble(formatted));
    }

    @Override
    public AiCommunicateRecordSummary summary(Date date, Integer userId) {
        AiCommunicateRecordSummary aiCommunicateRecordSummary = new AiCommunicateRecordSummary();
        aiCommunicateRecordSummary.setSummaryDate(date == null ? new Date() : date);
        // 构建饼图信息,根据日期查询
        Date startDate = DateUtil.beginOfDay(date);
        Date endDate = DateUtil.endOfDay(date);
        Long start = startDate.getTime();
        Long end = endDate.getTime();
        List<CommunicateRecordEntity> byAddTimeBetween = communicateRecordMapper.findByAddTimeBetweenAndCreator(start, end, userId);
        List<Integer> communicateRecordIdList = byAddTimeBetween.stream().map(CommunicateRecordEntity::getCommunicateRecordId).collect(Collectors.toList());

        // 1.总通话数
        aiCommunicateRecordSummary.setSumCommunicatNum(byAddTimeBetween.size());
        // 2.总通话时长
        int sum = byAddTimeBetween.stream().filter(c -> c.getCoidLength() != null).mapToInt(CommunicateRecordEntity::getCoidLength).sum();
        BigDecimal bigDecimalValue = new BigDecimal(sum);
        aiCommunicateRecordSummary.setSumCommunicateTime(bigDecimalValue.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP));
        // 3.意向客户预测  needFollowUp  customerCooperationIntention  hasCustomerCooperationIntention
        List<VoiceFieldResultEntity> byCommunicateRecordId = new ArrayList<>();
        if (CollUtil.isNotEmpty(communicateRecordIdList)) {
            byCommunicateRecordId = traderVoiceFieldResultMapper.findByCommunicateRecordIdIn(communicateRecordIdList);
            List<VoiceFieldResultEntity> intentionList = byCommunicateRecordId.stream().filter(
                            b -> "needFollowUp".equals(b.getFieldCode())
                                    || "customerCooperationIntention".equals(b.getFieldCode())
                                    || "hasCustomerCooperationIntention".equals(b.getFieldCode()))
                    .filter(b -> "是".equals(b.getFieldResult())).collect(Collectors.toList());
            aiCommunicateRecordSummary.setTraderForecastNum(intentionList.size());
        }

        // 4.生成商机数
        if (CollUtil.isNotEmpty(communicateRecordIdList)) {
            List<RCommunicateTodoJAiEntity> byCommunicateRecordIdIn = rCommunicateTodoJAiMapper.findByCommunicateRecordIdIn(communicateRecordIdList);
            List<RCommunicateTodoJAiEntity> businessList = byCommunicateRecordIdIn.stream().filter(b -> b.getBusinessNo() != null).collect(Collectors.toList());
            aiCommunicateRecordSummary.setBusinessNum(businessList.size());
        }
        // 5.添加微信数预测
        List<VoiceFieldResultEntity> wechatList = byCommunicateRecordId.stream().filter(b -> "isWeChatAdded".equals(b.getFieldCode())).filter(b -> "是".equals(b.getFieldResult())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(communicateRecordIdList)) {
            aiCommunicateRecordSummary.setWechatForecastNum(wechatList.size());
        }


        List<AiCommunicateRecordSummary.Summary> summaryList = new ArrayList<>();
        int traderCount = (int) byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(5502)).count();
        AiCommunicateRecordSummary.Summary trader = AiCommunicateRecordSummary.Summary.builder()
                .type("客户开发")
                .total(traderCount)
                .completed((int) byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(5502) && b.getCoidLength() != null && b.getCoidLength() > 0).count())
                .duration(BigDecimal.valueOf(byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(5502) && b.getCoidLength() != null && b.getCoidLength() > 0)
                        .mapToInt(CommunicateRecordEntity::getCoidLength).sum()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP))
                .build();
        summaryList.add(trader);

        int productCount = (int) byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(5501)).count();
        AiCommunicateRecordSummary.Summary product = AiCommunicateRecordSummary.Summary.builder()
                .type("产品推广")
                .total(productCount)
                .completed((int) byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(5501) && b.getCoidLength() != null && b.getCoidLength() > 0).count())
                .duration(BigDecimal.valueOf(byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(5501) && b.getCoidLength() != null && b.getCoidLength() > 0)
                        .mapToInt(CommunicateRecordEntity::getCoidLength).sum()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP))
                .build();
        summaryList.add(product);


        int businessChangeCount = (int) byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(244)).count();

        AiCommunicateRecordSummary.Summary businessChange = AiCommunicateRecordSummary.Summary.builder()
                .type("商机跟进")
                .total(businessChangeCount)
                .completed((int) byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(244) && b.getCoidLength() != null && b.getCoidLength() > 0).count())
                .duration(BigDecimal.valueOf(byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(244) && b.getCoidLength() != null && b.getCoidLength() > 0)
                        .mapToInt(CommunicateRecordEntity::getCoidLength).sum()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP))
                .build();
        summaryList.add(businessChange);

        int quoteCount = (int) byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(245)).count();
        AiCommunicateRecordSummary.Summary quote = AiCommunicateRecordSummary.Summary.builder()
                .type("报价响应")
                .total(quoteCount)
                .completed((int) byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(245) && b.getCoidLength() != null && b.getCoidLength() > 0).count())
                .duration(BigDecimal.valueOf(byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(245) && b.getCoidLength() != null && b.getCoidLength() > 0)
                        .mapToInt(CommunicateRecordEntity::getCoidLength).sum()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP))
                .build();
        summaryList.add(quote);

        int businessCount = (int) byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(246)).count();

        AiCommunicateRecordSummary.Summary business = AiCommunicateRecordSummary.Summary.builder()
                .type("商务处理")
                .total(businessCount)
                .completed((int) byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(246) && b.getCoidLength() != null && b.getCoidLength() > 0).count())
                .duration(BigDecimal.valueOf(byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(246) && b.getCoidLength() != null && b.getCoidLength() > 0)
                        .mapToInt(CommunicateRecordEntity::getCoidLength).sum()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP))
                .build();
        summaryList.add(business);

        int afterSaleCount = (int) byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(248)).count();
        AiCommunicateRecordSummary.Summary afterSale = AiCommunicateRecordSummary.Summary.builder()
                .type("售后")
                .total(afterSaleCount)
                .completed((int) byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(248) && b.getCoidLength() != null && b.getCoidLength() > 0).count())
                .duration(BigDecimal.valueOf(byAddTimeBetween.stream().filter(b -> b.getCommunicateType().equals(248) && b.getCoidLength() != null && b.getCoidLength() > 0)
                        .mapToInt(CommunicateRecordEntity::getCoidLength).sum()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP))
                .build();
        summaryList.add(afterSale);

        List<Integer> communicateTypeList = CollUtil.newArrayList(5501, 244, 5502, 245, 246, 248);
        int otherCount = (int) byAddTimeBetween.stream().filter(b -> !communicateTypeList.contains(b.getCommunicateType())).count();
        AiCommunicateRecordSummary.Summary other = AiCommunicateRecordSummary.Summary.builder()
                .type("其他")
                .total(otherCount)
                .completed((int) byAddTimeBetween.stream().filter(b -> !communicateTypeList.contains(b.getCommunicateType()) && b.getCoidLength() != null && b.getCoidLength() > 0).count())
                .duration(BigDecimal.valueOf(byAddTimeBetween.stream().filter(b -> !communicateTypeList.contains(b.getCommunicateType()) && b.getCoidLength() != null && b.getCoidLength() > 0)
                        .mapToInt(CommunicateRecordEntity::getCoidLength).sum()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP))
                .build();
        summaryList.add(other);

        AiCommunicateRecordSummary.Summary sumCount = AiCommunicateRecordSummary.Summary.builder()
                .type("总计(根据实际通话数）")
                .total(summaryList.stream().mapToInt(AiCommunicateRecordSummary.Summary::getTotal).sum())
                .completed(summaryList.stream().mapToInt(AiCommunicateRecordSummary.Summary::getCompleted).sum())
                .duration(summaryList.stream().map(AiCommunicateRecordSummary.Summary::getDuration).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .build();
        summaryList.add(sumCount);

        aiCommunicateRecordSummary.setSummaryList(summaryList);

        // 构建饼图信息
        Map<String, List<AiCommunicateRecordSummary.Pie>> pieMap = new HashMap<>();

        List<AiCommunicateRecordSummary.Pie> sumCommunicatNum = CollUtil.newArrayList(
                new AiCommunicateRecordSummary.Pie(traderCount, "客户开发"),
                new AiCommunicateRecordSummary.Pie(productCount, "产品推广"),
                new AiCommunicateRecordSummary.Pie(businessChangeCount, "商机跟进"),
                new AiCommunicateRecordSummary.Pie(quoteCount, "报价响应"),
                new AiCommunicateRecordSummary.Pie(businessCount, "商务处理"),
                new AiCommunicateRecordSummary.Pie(afterSaleCount, "售后"),
                new AiCommunicateRecordSummary.Pie(otherCount, "其他")
        );
        pieMap.put("sumCommunicatNum", sumCommunicatNum);
        pieMap.put("sumCommunicateTime", CollUtil.newArrayList(
                new AiCommunicateRecordSummary.Pie(Convert.toBigDecimal(aiCommunicateRecordSummary.getSumCommunicateTime(), BigDecimal.ZERO), "总通话时长"))
        );
        pieMap.put("traderForecastNum", CollUtil.newArrayList(
                new AiCommunicateRecordSummary.Pie(Convert.toInt(aiCommunicateRecordSummary.getTraderForecastNum(), 0), "意向客户预测"))
        );
        pieMap.put("businessNum", CollUtil.newArrayList(
                new AiCommunicateRecordSummary.Pie(Convert.toInt(aiCommunicateRecordSummary.getBusinessNum(), 0), "生成商机数"))
        );
        pieMap.put("wechatForecastNum", CollUtil.newArrayList(
                new AiCommunicateRecordSummary.Pie(Convert.toInt(aiCommunicateRecordSummary.getWechatForecastNum(), 0), "添加微信数预测"))
        );

        aiCommunicateRecordSummary.setPieMap(pieMap);


        AiCommunicateRecordSummary.TodoDetail todoDetail = new AiCommunicateRecordSummary.TodoDetail();
        aiCommunicateRecordSummary.setTodoDetail(todoDetail);

        if (CollUtil.isNotEmpty(byCommunicateRecordId)) {
            todoDetail.setNeedBusinessOpportunityPhoneList(byCommunicateRecordId.stream().filter(b -> "needBusinessOpportunity".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).map(VoiceFieldResultEntity::getCommunicateRecordId).collect(Collectors.toList()));
            todoDetail.setNeedBusinessOpportunityNum((int) byCommunicateRecordId.stream().filter(b -> "needBusinessOpportunity".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).count());

            todoDetail.setNeedToSendProductInfoRecordIdList(byCommunicateRecordId.stream().filter(b -> "needToSendProductInfo".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).map(VoiceFieldResultEntity::getCommunicateRecordId).collect(Collectors.toList()));
            todoDetail.setNeedToSendProductInfoNum((int) byCommunicateRecordId.stream().filter(b -> "needToSendProductInfo".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).count());

            todoDetail.setNeedToSendQuoteRecordIdList(byCommunicateRecordId.stream().filter(b -> "needToSendQuote".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).map(VoiceFieldResultEntity::getCommunicateRecordId).collect(Collectors.toList()));
            todoDetail.setNeedToSendQuoteNum((int) byCommunicateRecordId.stream().filter(b -> "needToSendQuote".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).count());

            todoDetail.setRecommendProductList(byCommunicateRecordId.stream().filter(b -> "recommendProduct".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).map(VoiceFieldResultEntity::getCommunicateRecordId).collect(Collectors.toList()));
            todoDetail.setRecommendProductNum((int) byCommunicateRecordId.stream().filter(b -> "recommendProduct".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).count());

            todoDetail.setNeedToSendProductPlanRecordIdList(byCommunicateRecordId.stream().filter(b -> "needToSendProductPlan".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).map(VoiceFieldResultEntity::getCommunicateRecordId).collect(Collectors.toList()));
            todoDetail.setNeedToSendProductPlanNum((int) byCommunicateRecordId.stream().filter(b -> "needToSendProductPlan".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).count());

            todoDetail.setSendAfterSalesPolicyRecordIdList(byCommunicateRecordId.stream().filter(b -> "sendAfterSalesPolicy".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).map(VoiceFieldResultEntity::getCommunicateRecordId).collect(Collectors.toList()));
            todoDetail.setSendAfterSalesPolicyNum((int) byCommunicateRecordId.stream().filter(b -> "sendAfterSalesPolicy".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).count());

            todoDetail.setCompleteCustomerTagList(byCommunicateRecordId.stream().filter(b -> "completeCustomerTag".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).map(VoiceFieldResultEntity::getCommunicateRecordId).collect(Collectors.toList()));
            todoDetail.setCompleteCustomerTagNum((int) byCommunicateRecordId.stream().filter(b -> "completeCustomerTag".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).count());

            todoDetail.setCompleteContactPositionInfoRecordIdList(byCommunicateRecordId.stream().filter(b -> "completeContactPositionInfo".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).map(VoiceFieldResultEntity::getCommunicateRecordId).collect(Collectors.toList()));
            todoDetail.setCompleteContactPositionInfoNum((int) byCommunicateRecordId.stream().filter(b -> "completeContactPositionInfo".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).count());


            List<Integer> isWeChatAddedList = byCommunicateRecordId.stream().filter(b -> "isWeChatAdded".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).map(VoiceFieldResultEntity::getCommunicateRecordId).collect(Collectors.toList());
            List<String> phone = byAddTimeBetween.stream().filter(b -> isWeChatAddedList.contains(b.getCommunicateRecordId()) && StrUtil.isNotEmpty(b.getPhone())).map(CommunicateRecordEntity::getPhone).collect(Collectors.toList());
            todoDetail.setIsWeChatAddedRecordIdList(phone.stream().distinct().collect(Collectors.toList()));
            todoDetail.setIsWeChatAddedNum((int) byCommunicateRecordId.stream().filter(b -> "isWeChatAdded".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).count());

            todoDetail.setNeedToSendContractInformationAddedRecordIdList(byCommunicateRecordId.stream().filter(b -> "needToSendContractInformation".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).map(VoiceFieldResultEntity::getCommunicateRecordId).collect(Collectors.toList()));
            todoDetail.setNeedToSendContractInformationNum((int) byCommunicateRecordId.stream().filter(b -> "needToSendContractInformation".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).count());

            todoDetail.setOtherMattersRecordIdList(byCommunicateRecordId.stream().filter(b -> "needOtherMatters".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).map(VoiceFieldResultEntity::getCommunicateRecordId).collect(Collectors.toList()));
            todoDetail.setOtherMattersNum((int) byCommunicateRecordId.stream().filter(b -> "needOtherMatters".equals(b.getFieldCode()) && "是".equals(b.getFieldResult())).count());

            todoDetail.setTotal(
                    todoDetail.getOtherMattersNum() +
                            todoDetail.getNeedToSendContractInformationNum() +
                            todoDetail.getNeedToSendProductPlanNum() +
                            todoDetail.getRecommendProductNum() +
                            todoDetail.getNeedToSendQuoteNum() +
                            todoDetail.getNeedToSendProductInfoNum() +
                            todoDetail.getSendAfterSalesPolicyNum() +
                            todoDetail.getCompleteCustomerTagNum() +
                            todoDetail.getIsWeChatAddedNum() +
                            todoDetail.getNeedBusinessOpportunityNum());
        }
        return aiCommunicateRecordSummary;
    }


}

