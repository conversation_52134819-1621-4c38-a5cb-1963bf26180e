package com.vedeng.temporal.polling.universal.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.vedeng.temporal.polling.universal.enums.DataSourceType;
import com.vedeng.temporal.config.TemporalProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 统一轮询请求类 - 使用Lombok Builder
 * 
 * 提供类型安全的Lombok Builder模式，支持流式API和链式调用。
 * 与 PollingRequest 保持字段名称一致，使用字符串配置完成条件。
 * 
 * 设计特点：
 * - 类型安全：支持Map<String, Object>数据类型
 * - Lombok Builder：自动生成Builder模式，支持链式调用
 * - 字符串配置：使用completionCheckConfig替代枚举方式
 * - 自动构建：无需手动维护Builder逻辑
 *
 * 
 * Lombok Builder使用示例：
 * <pre>
 * // 数据库轮询 - 等待销售订单完成
 * UniversalPollingRequest request = UniversalPollingRequest.builder()
 *     .businessId("ORDER_12345")
 *     .businessType("SALES_ORDER")
 *     .dataSourceType(DataSourceType.LOCAL_DATABASE)
 *     .companyCode("COMPANY_A")
 *     .queryType("TEMPORAL_FLOW_ORDER_QUERY")
 *     .queryParameters(Map.of("orderType", "SALE"))
 *     .completionCheckConfig("saleOrderNo:isNotBlank")
 *     .build();
 * 
 * // API轮询 - 等待采购订单审核通过
 * UniversalPollingRequest request = UniversalPollingRequest.builder()
 *     .businessId("PO_67890")
 *     .businessType("PURCHASE_ORDER")
 *     .dataSourceType(DataSourceType.REMOTE_API)
 *     .companyCode("COMPANY_B")
 *     .apiPath("/api/v1/purchaseorder/query.do")
 *     .apiParameters(Map.of("orderId", 67890))
 *     .completionCheckConfig("data.validStatus:1")
 *     .build();
 * 
 * // 复杂条件组合
 * UniversalPollingRequest request = UniversalPollingRequest.builder()
 *     .businessId("INVOICE_111")
 *     .businessType("INVOICE")
 *     .dataSourceType(DataSourceType.LOCAL_DATABASE)
 *     .companyCode("COMPANY_C")
 *     .queryType("INVOICE_QUERY")
 *     .queryParameters(queryParams)
 *     .completionCheckConfig("auditStatus:APPROVED&paidAmount:>0")
 *     .build();
 * </pre>
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 6.0 (使用Lombok Builder，移除自定义Builder)
 * @since 2025-01-24
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class  UniversalPollingRequest {
    
    /**
     * 业务ID (必需)
     * 唯一标识这次轮询的业务对象
     */
    private String businessId;
    
    /**
     * 业务类型 (必需)
     * 如：SALES_ORDER, PURCHASE_ORDER, INVENTORY, INVOICE, PAYMENT 等
     */
    private String businessType;
    
    /**
     * 公司代码 (必需)
     * 业务单据所属的公司标识
     */
    private String companyCode;
    
    /**
     * API路径 (API轮询时必需)
     * 状态查询的API路径，不包含BaseURL（由SystemApiClient自动处理）
     */
    private String apiPath;
    
    /**
     * API参数 (API轮询时可选)
     * 调用API时需要传递的具体参数，由调用方根据API要求自定义设置
     * 如：{"saleOrderId": 123} 或 {"buyOrderId": 456, "status": "PENDING"}
     */
    private Map<String, Object> apiParameters;
    
    /**
     * 查询类型 (数据库轮询时必需)
     * 数据库查询的类型标识，如：TEMPORAL_FLOW_ORDER_QUERY
     */
    private String queryType;
    
    /**
     * 查询参数 (数据库轮询时可选)
     * 数据库查询时需要传递的具体参数
     */
    private Map<String, Object> queryParameters;
    
    /**
     * 轮询配置 (可选)
     * 如果为空，将使用默认配置
     */
    private TemporalProperties.PollingConfig pollingConfig;
    
    /**
     * 数据源类型 (必需)
     * 决定使用哪种查询执行器
     */
    private DataSourceType dataSourceType;
    
    
    /**
     * 业务完成检查配置（新增，推荐使用）
     * 用于配置业务完成判断逻辑的字符串配置
     *
     * 支持的配置格式：
     * - "field:value" - 简单字段值检查，如 "auditStatus:APPROVED"
     * - "field:value1,value2" - 多值检查，如 "status:COMPLETED,APPROVED"  
     * - "field:>value" - 数值比较，如 "paidAmount:>0"
     * - "field1:value1&field2:value2" - 多条件AND，如 "auditStatus:APPROVED&paidAmount:>0"
     * - "field1:value1|field2:value2" - 多条件OR，如 "status:COMPLETED|status:APPROVED"
     * - "field:isNotBlank" - 字段非空检查
     * - "field:isNull" - 字段为空检查
     * - "field:contains:value" - 字符串包含检查
     * 
     * 高级语法：
     * - "!(field:value)" - 取反操作
     * - "(field1:value1|field2:value2)&field3:value3" - 括号分组
     * - "field:after:2025-01-01" - 日期比较
     * - "field:matches:regex" - 正则表达式匹配
     *
     * 使用示例：
     * <pre>
     * // 简单状态检查
     * .completionCheckConfig("auditStatus:APPROVED")
     *
     * // 复杂条件组合  
     * .completionCheckConfig("auditStatus:APPROVED&paidAmount:>0")
     * 
     * // 销售订单完成检查
     * .completionCheckConfig("saleOrderNo:isNotBlank")
     * 
     * // 采购订单审核通过检查
     * .completionCheckConfig("data.validStatus:1")
     * </pre>
     */
    private String completionCheckConfig;
    
    /**
     * 验证请求的有效性
     * 
     * @throws IllegalArgumentException 如果请求参数无效
     */
    public void validate() {
        // 验证基础信息
        if (businessId == null || businessId.trim().isEmpty()) {
            throw new IllegalArgumentException("业务ID不能为空");
        }
        
        if (businessType == null || businessType.trim().isEmpty()) {
            throw new IllegalArgumentException("业务类型不能为空");
        }
        
        if (companyCode == null || companyCode.trim().isEmpty()) {
            throw new IllegalArgumentException("公司代码不能为空");
        }
        
        if (dataSourceType == null) {
            throw new IllegalArgumentException("数据源类型不能为空");
        }
        
        if (completionCheckConfig == null || completionCheckConfig.trim().isEmpty()) {
            throw new IllegalArgumentException("完成检查配置不能为空");
        }
        
        // 验证数据源配置
        switch (dataSourceType) {
            case REMOTE_API:
                if (apiPath == null || apiPath.trim().isEmpty()) {
                    throw new IllegalArgumentException("远程API数据源需要提供API路径");
                }
                break;
                
            case LOCAL_DATABASE:
                if (queryType == null || queryType.trim().isEmpty()) {
                    throw new IllegalArgumentException("本地数据库数据源需要提供查询类型");
                }
                break;
                
            default:
                throw new IllegalArgumentException("不支持的数据源类型: " + dataSourceType);
        }
    }
    
    /**
     * 获取公司代码
     * 
     * @return 公司代码
     */
    public String getCompanyCode() {
        return companyCode;
    }
    
    /**
     * 获取格式化的请求描述
     * 用于日志输出和调试
     * 
     * @return 格式化的请求描述
     */
    @JsonIgnore
    public String formatDescription() {
        return String.format("UniversalPollingRequest{businessId='%s', dataSourceType=%s, companyCode='%s'}",
                businessId, dataSourceType, getCompanyCode());
    }
    
    /**
     * 获取请求唯一标识
     * 基于业务ID和数据源类型生成
     * 
     * @return 请求唯一标识
     */
    @JsonIgnore
    public String createRequestKey() {
        return String.format("%s-%s-%s-%d", 
                dataSourceType.getCode(), 
                businessId != null ? businessId : "unknown",
                getCompanyCode() != null ? getCompanyCode() : "unknown",
                System.currentTimeMillis());
    }
    
}