package com.vedeng.activiti.taskassign;

import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.*;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.orderstrategy.StrategyContext;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.saleorder.api.AutoRegistrationService;
import com.vedeng.erp.saleorder.api.SaleorderSyncService;
import com.vedeng.erp.saleorder.enums.PCOrderStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleorderSyncEnum;
import com.vedeng.erp.saleorder.enums.WebAccountFromEnum;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.CapitalBillDetail;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.controller.SaleorderController;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.enums.PaymentTypeEnum;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.OrderData;
import com.vedeng.order.service.OrderAccountPeriodService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.soap.service.VedengSoapService;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderContact;
import com.vedeng.trader.model.WebAccount;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.h2.util.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class EditSaleorderExecutionListener implements ExecutionListener {
    /**
     * 记录日志
     */
    private static final org.slf4j.Logger LOG = LoggerFactory.getLogger(SaleorderController.class);
    /**
     * 运行时注入service
     */
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private final VedengSoapService vedengSoapService = (VedengSoapService) context.getBean("vedengSoapService");
    private final ActionProcdefService actionProcdefService = (ActionProcdefService) context.getBean("actionProcdefService");
    private final SaleorderService saleorderService = (SaleorderService) context.getBean("saleorderService");
    private final CapitalBillService capitalBillService = (CapitalBillService) context.getBean("capitalBillService");
    private final UserService userService = (UserService) context.getBean("userService");
    private final ExpressService expressService = (ExpressService) context.getBean("expressService");
    private final SaleorderMapper saleorderMapper = (SaleorderMapper) context.getBean("saleorderMapper");
    private final SaleorderGoodsMapper saleorderGoodsMapper = (SaleorderGoodsMapper) context.getBean("saleorderGoodsMapper");
    private final WarehouseStockService warehouseStockService = (WarehouseStockService) context.getBean("warehouseStockService");
    private final OrderAccountPeriodService orderAccountPeriodService = (OrderAccountPeriodService) context.getBean("orderAccountPeriodService");
    private final AutoRegistrationService autoRegistrationService = (AutoRegistrationService) context.getBean("autoRegistrationService");
    private final SaleorderSyncService saleorderSyncService = (SaleorderSyncService) context.getBean("saleorderSyncService");


    /**
     * 订单审核触发器
     * 根据穿参通用回写主表中状态
     *
     * @param execution 主表信息 底层调用selectOne
     * @throws Exception
     */
    @Override
    public void notify(DelegateExecution execution) throws Exception {
        final Saleorder saleOrderInfo = (Saleorder) execution.getVariable("saleorderInfo");
        try {

            LOG.info("订单审核触发器{}", JSON.toJSONString(saleOrderInfo));
            User user = null;
            HttpServletRequest request = null;

            // 获取用户信息
            if (RequestContextHolder.getRequestAttributes() == null) {
                user = userService.getByUsername("njadmin", 1);
            } else {
                ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                request = ra.getRequest();
                user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
                if (user == null) {
                    user = userService.getByUsername("njadmin", 1);
                }
            }

            // 获取归属销售信息
            User belongUser = new User();
            if (saleOrderInfo.getTraderId() != null) {
                // 1客户，2供应商
                belongUser = userService.getUserInfoByTraderId(saleOrderInfo.getTraderId(), 1);
            }

            // 进行订单解锁
            actionProcdefService.updateInfo("T_SALEORDER", "SALEORDER_ID", saleOrderInfo.getSaleorderId(), "LOCKED_STATUS", 0, 2);
            saleorderService.updateUnlockSaleOrderWarning(saleOrderInfo.getSaleorderId());

            // 返回db中订单信息
            ResultInfo resultInfo = expressService.sendWxMessageForArrival(saleOrderInfo.getSaleorderId());
            Map<String, Object> saleTempMap = (Map) resultInfo.getData();

            /**
             * 1、如果使用账期，则冻结相应的账期金额
             * 2、如果是100%账期支付，则占用第一步冻结的账期金额
             * 3、对订单添加相应的账期支付流水
             */
            if (saleOrderInfo.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
                //订单原始金额为0时，默认执行生成流水逻辑，更新订单支付状态为全部付款
                addCapitalBillByBillPeriod(saleOrderInfo, user, request, belongUser);
            } else {
                try {
                    Boolean freezeResult = orderAccountPeriodService.freezingBillPeriodAmountWhenOrderValid(saleOrderInfo);
                    Saleorder saleorder = saleorderMapper.getSaleOrderById(saleOrderInfo.getSaleorderId());
                    if (freezeResult && saleorder.getIsNew() == 0) {
                        // VDERP-8121 订单流新的订单，不自定生成账期流水，而由详情页手动点击信用支付
                        orderAccountPeriodService.occupyBillPeriodAmount(saleOrderInfo);

                        addCapitalBillByBillPeriod(saleOrderInfo, user, request, belongUser);
                    }
                } catch (Exception e) {
                    LOG.error("订单审核,冻结账期失败， 账期余额不足{}", saleOrderInfo, e);
                }
            }

            //销售订单生效更新db
            saveEditSaleOrderInfo(saleOrderInfo, belongUser, request);

            // 处理前台订单推送
            if (!OrderConstant.ORDER_TYPE_BD.equals(saleOrderInfo.getOrderType())) {
                pushOrder2Vedeng(saleOrderInfo);
            }

            // 处理耗材
            if (OrderConstant.ORDER_TYPE_HC.equals(saleOrderInfo.getOrderType())) {
                handleHCOrder(saleOrderInfo, request);
            }
            // 处理销售订单
            if (OrderConstant.ORDER_TYPE_SALE.equals(saleOrderInfo.getOrderType())) {
                handleVSOrder(saleOrderInfo.getSaleorderId());
            }

            // 处理BD订单
            if (OrderConstant.ORDER_TYPE_BD.equals(saleOrderInfo.getOrderType())) {
                handleBDOrder(saleOrderInfo, saleTempMap);
            }

            //发送微信或短信消息
            saleorderService.waitSendOrderFor((Saleorder) saleTempMap.get("saleOrderInfo"), saleTempMap, user);

            // --------ERP的VS订单生效时，订单的客户所属平台为贝登医疗 客户性质为分销 订单联系人未注册 推送注册----------start
            // 订单类型为vs
            if (saleOrderInfo.getTraderContactId() != null && Objects.equals(saleOrderInfo.getOrderType(), OrderConstant.ORDER_TYPE_SALE)) {
                log.info("自动注册==判断当前vs订单并注册；订单号：{},客户联系人：{}", saleOrderInfo.getSaleorderNo(), saleOrderInfo.getTraderContactId());
                autoRegistrationService.doRegistration(saleOrderInfo.getTraderContactId(), true);
            }
            //VDERP-17686 销售订单增加成本/利润计算 插入N条初始化数据，job每2分钟跑一次，不做实时查询，onedata服务在阿里云，防止接口响应慢影响审批。
            saleorderService.refreshSaleOrderBuyPrice(saleOrderInfo.getSaleorderId());

            // --------ERP的VS订单生效时，订单联系人未注册 推送注册----------end
        }catch (Exception e){
            log.error("审核流报错{}", JSON.toJSONString(saleOrderInfo),e);
            throw new IllegalStateException(e);
        }
    }



    // & -------------------------------------------  private method


    /**
     * 前台推送相关处理
     *
     * @param saleOrderInfo 订单
     */
    private void pushOrder2Vedeng(Saleorder saleOrderInfo) {
        log.info("前台推送相关处理:{}",JSON.toJSON(saleOrderInfo));
        if (saleOrderInfo.getOrderType() == 5 && saleOrderInfo.getIsCoupons() == 0) {
            HashMap<String, Object> hcOrder = new HashMap<>(4);
            hcOrder.put("saleOrder", saleOrderInfo);
            saleorderService.putOrderPricetoHC(hcOrder);
        }
        if (saleOrderInfo.getOrderType() == 3) {
            vedengSoapService.orderSync(saleOrderInfo.getSaleorderId());
        }
        if (saleOrderInfo.getOrderType() == 0 || saleOrderInfo.getOrderType() == 4) {
            vedengSoapService.orderSyncWeb(saleOrderInfo.getSaleorderId());
            if (saleOrderInfo.getOrderType() == 0) {
                //订单推web调用消息
                vedengSoapService.messageSyncWeb(2, saleOrderInfo.getSaleorderId(), 1);
            }
        }
    }


    /**
     * 处理耗材订单
     * //HC订单审核通过后 需要修改默认值
     * // 发票寄送节点 全部发货时一次寄送
     *
     * @param saleOrderInfo
     * @param request
     */
    private void handleHCOrder(Saleorder saleOrderInfo, HttpServletRequest request) {
        log.info("处理耗材订单:{}",JSON.toJSON(saleOrderInfo));
        if (OrderConstant.ORDER_TYPE_HC.equals(saleOrderInfo.getOrderType())) {
            Saleorder updateSaleorderInfo = new Saleorder();
            updateSaleorderInfo.setSaleorderId(saleOrderInfo.getSaleorderId());
            updateSaleorderInfo.setInvoiceSendNode(0);

            List<SaleorderGoods> saleorderGoodList = saleorderGoodsMapper.getSaleorderGoodsListBySaleorderId(saleOrderInfo.getSaleorderId());

            Map<String, SaleorderGoods> saleorderGoodMap = saleorderGoodList.stream().collect(Collectors.toMap(key -> key.getSku(), v -> v, (k, v) -> v));

            List<String> skuNoList = new ArrayList<>(saleorderGoodMap.keySet());

            Map<String, WarehouseStock> skuStockList = warehouseStockService.getStockInfo(skuNoList);

            //货齐发货
            int deliveryType = SysOptionConstant.NO_PART_DELIVER;

            for (String sku : skuNoList) {
                if (saleorderGoodMap.get(sku).getNum() > skuStockList.get(sku).getAvailableStockNum()) {
                    //分批发货
                    deliveryType = 482;
                    break;
                }
            }
            updateSaleorderInfo.setDeliveryType(deliveryType);
            saleorderService.saveEditSaleorderInfo(updateSaleorderInfo, request, request == null ? null : request.getSession());

        }
    }


    /**
     * 处理BD订单
     *
     * @param saleOrderInfo
     * @param hashmap
     */
    private void handleBDOrder(Saleorder saleOrderInfo, Map<String, Object> hashmap) {
        log.info("处理BD订单:{},hashMap:{}",JSON.toJSON(saleOrderInfo),hashmap);
        if (OrderConstant.ORDER_TYPE_BD.equals(saleOrderInfo.getOrderType())) {
            //待支付订单发送消息
            Saleorder saleorder = (Saleorder) hashmap.get("saleOrderInfo");
//            //待确认订单
//            OrderData orderData = saleorderService.waitMjxSendOrderFor(saleorder, 2);
//            orderData.setTraderId(saleorder.getTraderId());

            saleorderSyncService.syncSaleorderStatus2Mjx(saleorder.getSaleorderId(),
                    PCOrderStatusEnum.PRE_PAY, SaleorderSyncEnum.EDIT_LISTENER);
            try {
                log.info("BD订单审核通过更新前台订单数据,订单号：{}", saleorder.getSaleorderNo());
                saleorderService.updateVedengJX(saleorder.getSaleorderId());
            } catch (Exception e) {
                log.info("销售订单审核通过更新前台订单数据失败", e);
            }
        }
    }

    /**
     * 处理销售订单
     * 1.推送VS订单到前台PC
     * 2. .....
     *
     * @param saleOrderId
     */
    private void handleVSOrder(Integer saleOrderId) {
        log.info("处理销售订单:{}",JSON.toJSON(saleOrderId));
        StrategyContext strategyContext = new StrategyContext();

        strategyContext.add(StrategyContext.PUSH_PC_STRATEGY, OrderDataUpdateConstant.SALE_ORDER_VAILD);

        strategyContext.executeAll(saleOrderId);
    }

    /**
     * 如果预付款金额等于0，添加触发账期流水
     *
     * @param saleOrderInfo 订单信息
     */
    private void addCapitalBillByBillPeriod(Saleorder saleOrderInfo, User user, HttpServletRequest request, User belongUser) {

        if (saleOrderInfo.getPrepaidAmount().compareTo(BigDecimal.ZERO) == 0) {

            Saleorder saleorder = new Saleorder();
            saleorder.setSaleorderId(saleOrderInfo.getSaleorderId());
            if (saleorder.getRetainageAmount() == null || saleOrderInfo.getRetainageAmount().compareTo(BigDecimal.ZERO) == 0) {
                //如果尾款等于0.付款状态为全部付款
                saleorder.setPaymentStatus(2);
            } else {
                //如果尾款不等于0.付款状态为部分付款
                saleorder.setPaymentStatus(1);
            }
            saleorder.setAccountPeriodAmount(saleOrderInfo.getAccountPeriodAmount());
            saleorder.setSatisfyDeliveryTime(DateUtil.sysTimeMillis());
            saleorder.setPaymentTime(DateUtil.sysTimeMillis());
            //添加流水
            CapitalBill capitalBill = new CapitalBill();
            capitalBill.setCompanyId(user.getCompanyId());
            //信用支付
            capitalBill.setTraderMode(527);
            capitalBill.setCurrencyUnitId(1);
            capitalBill.setTraderTime(DateUtil.sysTimeMillis());
            //交易类型 转移
            capitalBill.setTraderType(3);
            capitalBill.setPayer(saleOrderInfo.getTraderName());
            capitalBill.setPayee(user.getCompanyName());

            List<CapitalBillDetail> capitalBillDetails = new ArrayList<>();
            CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
            //订单类型   销售订单
            capitalBillDetail.setOrderType(1);
            capitalBillDetail.setOrderNo(saleOrderInfo.getSaleorderNo());
            capitalBillDetail.setRelatedId(saleOrderInfo.getSaleorderId());
            //所属类型  经销商（包含终端）
            capitalBillDetail.setTraderType(1);
            capitalBillDetail.setTraderId(saleOrderInfo.getTraderId());
            capitalBillDetail.setUserId(saleOrderInfo.getUserId());
            //业务类型  订单收款
            capitalBillDetail.setBussinessType(526);
            capitalBillDetail.setAmount(saleOrderInfo.getAccountPeriodAmount());
            if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
                capitalBillDetail.setOrgName(belongUser.getOrgName());
                capitalBillDetail.setOrgId(belongUser.getOrgId());
            }
            capitalBillDetails.add(capitalBillDetail);
            capitalBill.setCapitalBillDetails(capitalBillDetails);
            CapitalBillDetail capitalBillDetailInfo = new CapitalBillDetail();
            capitalBillDetailInfo.setOrderType(1);
            capitalBillDetailInfo.setOrderNo(saleOrderInfo.getSaleorderNo());
            capitalBillDetailInfo.setRelatedId(saleOrderInfo.getSaleorderId());
            capitalBillDetailInfo.setTraderType(1);
            capitalBillDetailInfo.setTraderId(saleOrderInfo.getTraderId());
            capitalBillDetailInfo.setUserId(saleOrderInfo.getUserId());
            //业务类型  订单收款
            capitalBillDetailInfo.setBussinessType(526);
            capitalBillDetailInfo.setAmount(saleOrderInfo.getAccountPeriodAmount());
            if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
                capitalBillDetailInfo.setOrgName(belongUser.getOrgName());
                capitalBillDetailInfo.setOrgId(belongUser.getOrgId());
            }
            capitalBill.setCapitalBillDetail(capitalBillDetailInfo);
            //添加当前登陆人
            capitalBill.setCreator(user.getUserId());
            saleorderService.saveEditSaleorderInfo(saleorder, request, request == null ? null : request.getSession());
            saleorderService.updateSaleGoodsByAllSpecialGoods(saleOrderInfo.getSaleorderId());
            //如果是VS订单，或者京东订单  不发送update到前-
            if (saleOrderInfo.getOrderType() == 0 || StringUtils.equals("194723",saleOrderInfo.getTraderId()+"")) {
                capitalBill.setBdPayStatusFlag(false);
            }
            capitalBillService.saveCapitalBill(capitalBill);
        }

    }

    /**
     * （VALID_ORG_ID，VALID_ORG_NAME，VALID_USER_ID）
     *
     * @param saleOrderInfo
     * @param belongUser
     * @param request
     */
    private void saveEditSaleOrderInfo(Saleorder saleOrderInfo, User belongUser, HttpServletRequest request) {
        log.info("订单生效更新：{}",JSON.toJSON(saleOrderInfo));
        Saleorder validSaleOrder = new Saleorder();
        validSaleOrder.setSaleorderId(saleOrderInfo.getSaleorderId());
        if (belongUser != null && belongUser.getUserId() != null) {
            validSaleOrder.setValidUserId(belongUser.getUserId());
        }
        if (belongUser != null && belongUser.getOrgId() != null) {
            validSaleOrder.setValidOrgId(belongUser.getOrgId());
        }
        if (belongUser != null && belongUser.getOrgName() != null) {
            validSaleOrder.setValidOrgName(belongUser.getOrgName());
        }
        //代表是订单生效或者审核通过
        validSaleOrder.setOptType("2");
        saleorderService.saveEditSaleorderInfo(validSaleOrder, request, request == null ? null : request.getSession());

        Saleorder updateSaleorder = new Saleorder();
        updateSaleorder.setSaleorderId(saleOrderInfo.getSaleorderId());
        //如果MAX_SKU_REFUND_AMOUNT为0.00 则更新成( PRICE * NUM )
        saleorderService.updateMaxSkuRefundAmount(updateSaleorder);

        //如果realPrice为0，则更新成 price
        saleorderService.updateRealPrice(updateSaleorder);
    }


}
