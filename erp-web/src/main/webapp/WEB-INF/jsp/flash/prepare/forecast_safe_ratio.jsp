<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="配置预测安全库存系数" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/flash/prepare/prepare_list.js?rnd=${resourceVersionKey}'></script>
<div class="content">
    <div class="sku-opr">
        <form id="forecast_safe_ratio">
            <ul class="lastResult">
                <li>
                    <div class="infor_name label-c" style="width: 150px">
                        <label class="info">预测安全库存&nbsp=&nbsp</label>
                    </div>
                    <div class="f_left">
                        <div>
                            <input type="text" class="input-c label-c mr5 input-color" readonly value="近三个月日均销售数量"> X&nbsp
                            <input type="text" class="input-c100 label-c mr5" name="safeRatio" id="safeRatio" value="${forecastSafeRatio}" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">&nbsp天
                        </div>
                    </div>
                </li>
                <li class="safe_ratio_error">
                    <div class="infor_name label-c">
                    </div>
                    <div class="f_left">
                        <span style="color:red;">注：统一调整备货计划所有SKU的预测安全库存值!</span>
                    </div>
                </li>
            </ul>
            <div class="submit-c tcenter">
                <button class="bt-largest bt-bg-style bg-light-blue" type="button" onclick="setForecastSafeRatio();">确定</button>
                <button id="close-layer" type="button" class="bg-cancle-blue bt-bg-style bt-largest">取消</button>
            </div>
        </form>
    </div>
</div>
<style>

    .sku-opr {
        padding: 20px;
        font-size: 15px;
    }

    .sku-opr ul li {
        margin-top: 20px;
    }

    .label-c {
        height: 40px;
        line-height: 40px;
    }

    .input-color {
        background-color: #f3f3f3;
    }

    .input-c {
        width: 200px;
        border-color: darkgray;
        text-align: center;
    }

    .input-c100 {
        width: 100px;
        border-color: #CCCCCC;
        text-align: center;
    }

    .select-c {
        width: 400px;
    }

    .input-textarea {
        width: 600px;
    }

    .submit-c {
        margin-top:30px;
    }

    .bg-cancle-blue {
        background: white;
        color: #1b76ed;
        border: 1px solid #1b76ed;
    }

    .submit-c {
        width: 90%;
        margin: 150px auto 0px;
    }
</style>
<%@ include file="../../common/footer.jsp"%>
