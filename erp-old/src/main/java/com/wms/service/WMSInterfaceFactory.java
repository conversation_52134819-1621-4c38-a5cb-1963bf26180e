package com.wms.service;

import com.wms.constant.WMSContant;
import com.wms.service.exchange.ExchangeOrderInterface;
import com.wms.service.input.CancelPoInterface;
import com.wms.service.input.PutPurchaseOrderInterface;
import com.wms.service.lendout.LendOutOrderInterface;
import com.wms.service.other.*;
import com.wms.service.output.CancelOutputInterface;
import com.wms.service.output.ModifyOrderInterface;
import com.wms.service.output.PutSaleOrderInterface;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("wmsInterfaceFactory")
public class WMSInterfaceFactory {

    //采购单下传接口
    @Autowired
    private PutPurchaseOrderInterface putPurchaseOrderInterface;

    //出库单下传接口
    @Autowired
    private PutSaleOrderInterface putSaleOrderInterface;

    //入库单据取消接口
    @Autowired
    private CancelPoInterface cancelPoInterface;

    //客户资料接口
    @Autowired
    private PutCustomerInterface putCustomerInterface;

    //商品接口
    @Autowired
    private PutSkuInterface putSkuInterface;

    //注册证接口
    @Autowired
    private PutRegDataInterface putRegDataInterface;

    //资质接口
    @Autowired
    private PutQLAInterface putQLAInterface;

    //出库单撤销
    @Autowired
    private CancelOutputInterface cancelOutputInterface;

    /**
     * 换货单接口
     */
    @Autowired
    private ExchangeOrderInterface exchangeOrderInterface;

    /**
     * 库存调整单接口
     */
    @Autowired
    private PutInventoryAdjustmentInterface putInventoryAdjustmentInterface;

    /**
     * 发票信息下传接口
     */
    @Autowired
    private PutInvoiceDataInterface putInvoiceDataInterface;

    /**
     * 图片下传WMS
     */
    @Autowired
    private PutPathDataInterface putPathDataInterface;

    @Autowired
    private QuerySkuWMSStockInterface querySkuWMSStockInterface;

    /**
     * 出库修改接口
     */
    @Autowired
    private ModifyOrderInterface modifyOrderInterface;

    @Autowired
    private LendOutOrderInterface lendOutOrderInterface;


    public WmsInterface getWmsInterface(String type){

        WmsInterface wmsInterface = null;

        switch (type) {

            case WMSContant.PUT_SKU:
                wmsInterface = putSkuInterface;
                break;

            case WMSContant.PUT_REG:
                wmsInterface = putRegDataInterface;
                break;

            case WMSContant.PUT_QLA:
                wmsInterface = putQLAInterface;
                break;

            case WMSContant.PUT_CUSTOMER:
                wmsInterface = putCustomerInterface;
                break;

            case WMSContant.PUT_PURCHASE_ORDER:
                wmsInterface = putPurchaseOrderInterface;
                break;

            case WMSContant.PUT_ORIGINAL_SALESORDER:
                wmsInterface = putSaleOrderInterface;
                break;

            case WMSContant.PUT_EXG_DATA:
                wmsInterface = exchangeOrderInterface;
                break;

            case WMSContant.CANCEL_PO:
                wmsInterface = cancelPoInterface;
                break;

            case WMSContant.PUT_INVENTORY_ADJUSRMENT:
                wmsInterface = putInventoryAdjustmentInterface;
                break;

            case WMSContant.CANCEL_ORGGINCAL_SALESORDER:
                wmsInterface = cancelOutputInterface;
                break;
            case WMSContant.PUT_INVOICE_DATA:
                wmsInterface = putInvoiceDataInterface;
                break;

            case WMSContant.QUERT_WMS_SKUSTOCK:
                wmsInterface = querySkuWMSStockInterface;
                break;

            case WMSContant.PUT_PATH_DATA:
                wmsInterface = putPathDataInterface;
                break;

            case WMSContant.MODIFY_SALEORDER:
                wmsInterface = modifyOrderInterface;
                break;

            case WMSContant.PUT_LEND_OUT_DATA:
                wmsInterface = lendOutOrderInterface;
                break;

            default:
                return null;

        }

        return wmsInterface;
    }

}
