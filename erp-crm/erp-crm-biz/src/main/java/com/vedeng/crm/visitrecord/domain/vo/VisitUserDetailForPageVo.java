package com.vedeng.crm.visitrecord.domain.vo;

import com.vedeng.erp.system.dto.UserDto;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/11
 */
@Data
public class VisitUserDetailForPageVo {

    /**
     * 创建人列表
     */
    private List<UserForVisitDto> createUserList;
    /**
     * 同行人列表
     */
    private List<UserForVisitDto> tongXingUserList;
    /**
     * 拜访人列表
     */
    private List<UserForVisitDto> visitUserList;
 }
