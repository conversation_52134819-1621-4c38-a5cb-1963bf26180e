package com.vedeng.order.model;

import java.math.BigDecimal;

public class SaleorderCoupon {

	private Integer saleorderCouponId;

    private Integer saleorderId;

    private Integer couponType;
    
    private BigDecimal denomination;

	private Integer couponId;

	private String couponCode;

	private BigDecimal useThreshold;

	private String limitTypeStr;

	private String effevtiveStartTime;

	private String effevtiveEndTime;

	public Integer getSaleorderCouponId() {
		return saleorderCouponId;
	}

	public void setSaleorderCouponId(Integer saleorderCouponId) {
		this.saleorderCouponId = saleorderCouponId;
	}

	public Integer getSaleorderId() {
		return saleorderId;
	}

	public void setSaleorderId(Integer saleorderId) {
		this.saleorderId = saleorderId;
	}

	public Integer getCouponType() {
		return couponType;
	}

	public void setCouponType(Integer couponType) {
		this.couponType = couponType;
	}

	public BigDecimal getDenomination() {
		return denomination;
	}

	public void setDenomination(BigDecimal denomination) {
		this.denomination = denomination;
	}

	public Integer getCouponId() {
		return couponId;
	}

	public void setCouponId(Integer couponId) {
		this.couponId = couponId;
	}

	public String getCouponCode() {
		return couponCode;
	}

	public void setCouponCode(String couponCode) {
		this.couponCode = couponCode;
	}

	public BigDecimal getUseThreshold() {
		return useThreshold;
	}

	public void setUseThreshold(BigDecimal useThreshold) {
		this.useThreshold = useThreshold;
	}

	public String getLimitTypeStr() {
		return limitTypeStr;
	}

	public void setLimitTypeStr(String limitTypeStr) {
		this.limitTypeStr = limitTypeStr;
	}

	public String getEffevtiveStartTime() {
		return effevtiveStartTime;
	}

	public void setEffevtiveStartTime(String effevtiveStartTime) {
		this.effevtiveStartTime = effevtiveStartTime;
	}

	public String getEffevtiveEndTime() {
		return effevtiveEndTime;
	}

	public void setEffevtiveEndTime(String effevtiveEndTime) {
		this.effevtiveEndTime = effevtiveEndTime;
	}


}
