package com.vedeng.goods.mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.vedeng.goods.domain.entity.AfterSaleSupplyPolicy;

public interface GoodsAfterSaleSupplyPolicyMapper {
    /**
     * delete by primary key
     * @param supplyPolicyId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long supplyPolicyId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(AfterSaleSupplyPolicy record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(AfterSaleSupplyPolicy record);

    /**
     * select by primary key
     * @param supplyPolicyId primary key
     * @return object by primary key
     */
    AfterSaleSupplyPolicy selectByPrimaryKey(Long supplyPolicyId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(AfterSaleSupplyPolicy record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(AfterSaleSupplyPolicy record);

    List<AfterSaleSupplyPolicy> findBySkuNo(@Param("traderId")Long traderId, @Param("skuNo")String skuNo);


}