package com.vedeng.department.model;

import java.util.List;

public class DepartmentFeeItems {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_DEPARTMENT_FEE_ITEMS.DEPARTMENT_FEE_ITEMS_ID
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    private Integer departmentFeeItemsId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_DEPARTMENT_FEE_ITEMS.PARENT_ID
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    private Integer parentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_DEPARTMENT_FEE_ITEMS.FEE_ITEMS_NUMBER
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    private Integer feeItemsNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_DEPARTMENT_FEE_ITEMS.FEE_ITEMS_NAME
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    private String feeItemsName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_DEPARTMENT_FEE_ITEMS.EVENT_MEANING
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    private String eventMeaning;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_DEPARTMENT_FEE_ITEMS.EXTRA_CONTENT
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    private String extraContent;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_DEPARTMENT_FEE_ITEMS.DESCRIPTION
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    private String description;

    /**
     * 三级分类
     */
    private String feeItemsNameThree;
    
    /**
     * @description 子集收费项目
     * <AUTHOR>
     * @param
     * @date 2019/4/16
     */
    private List<DepartmentFeeItems> departmentFeeItemsList;

    public String getFeeItemsNameThree() {
        return feeItemsNameThree;
    }

    public void setFeeItemsNameThree(String feeItemsNameThree) {
        this.feeItemsNameThree = feeItemsNameThree;
    }

    public List<DepartmentFeeItems> getDepartmentFeeItemsList() {
        return departmentFeeItemsList;
    }

    public void setDepartmentFeeItemsList(List<DepartmentFeeItems> departmentFeeItemsList) {
        this.departmentFeeItemsList = departmentFeeItemsList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_DEPARTMENT_FEE_ITEMS.DEPARTMENT_FEE_ITEMS_ID
     *
     * @return the value of T_DEPARTMENT_FEE_ITEMS.DEPARTMENT_FEE_ITEMS_ID
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    public Integer getDepartmentFeeItemsId() {
        return departmentFeeItemsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_DEPARTMENT_FEE_ITEMS.DEPARTMENT_FEE_ITEMS_ID
     *
     * @param departmentFeeItemsId the value for T_DEPARTMENT_FEE_ITEMS.DEPARTMENT_FEE_ITEMS_ID
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    public void setDepartmentFeeItemsId(Integer departmentFeeItemsId) {
        this.departmentFeeItemsId = departmentFeeItemsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_DEPARTMENT_FEE_ITEMS.PARENT_ID
     *
     * @return the value of T_DEPARTMENT_FEE_ITEMS.PARENT_ID
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    public Integer getParentId() {
        return parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_DEPARTMENT_FEE_ITEMS.PARENT_ID
     *
     * @param parentId the value for T_DEPARTMENT_FEE_ITEMS.PARENT_ID
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_DEPARTMENT_FEE_ITEMS.FEE_ITEMS_NUMBER
     *
     * @return the value of T_DEPARTMENT_FEE_ITEMS.FEE_ITEMS_NUMBER
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    public Integer getFeeItemsNumber() {
        return feeItemsNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_DEPARTMENT_FEE_ITEMS.FEE_ITEMS_NUMBER
     *
     * @param feeItemsNumber the value for T_DEPARTMENT_FEE_ITEMS.FEE_ITEMS_NUMBER
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    public void setFeeItemsNumber(Integer feeItemsNumber) {
        this.feeItemsNumber = feeItemsNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_DEPARTMENT_FEE_ITEMS.FEE_ITEMS_NAME
     *
     * @return the value of T_DEPARTMENT_FEE_ITEMS.FEE_ITEMS_NAME
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    public String getFeeItemsName() {
        return feeItemsName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_DEPARTMENT_FEE_ITEMS.FEE_ITEMS_NAME
     *
     * @param feeItemsName the value for T_DEPARTMENT_FEE_ITEMS.FEE_ITEMS_NAME
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    public void setFeeItemsName(String feeItemsName) {
        this.feeItemsName = feeItemsName == null ? null : feeItemsName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_DEPARTMENT_FEE_ITEMS.EVENT_MEANING
     *
     * @return the value of T_DEPARTMENT_FEE_ITEMS.EVENT_MEANING
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    public String getEventMeaning() {
        return eventMeaning;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_DEPARTMENT_FEE_ITEMS.EVENT_MEANING
     *
     * @param eventMeaning the value for T_DEPARTMENT_FEE_ITEMS.EVENT_MEANING
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    public void setEventMeaning(String eventMeaning) {
        this.eventMeaning = eventMeaning == null ? null : eventMeaning.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_DEPARTMENT_FEE_ITEMS.EXTRA_CONTENT
     *
     * @return the value of T_DEPARTMENT_FEE_ITEMS.EXTRA_CONTENT
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    public String getExtraContent() {
        return extraContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_DEPARTMENT_FEE_ITEMS.EXTRA_CONTENT
     *
     * @param extraContent the value for T_DEPARTMENT_FEE_ITEMS.EXTRA_CONTENT
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    public void setExtraContent(String extraContent) {
        this.extraContent = extraContent == null ? null : extraContent.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_DEPARTMENT_FEE_ITEMS.DESCRIPTION
     *
     * @return the value of T_DEPARTMENT_FEE_ITEMS.DESCRIPTION
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_DEPARTMENT_FEE_ITEMS.DESCRIPTION
     *
     * @param description the value for T_DEPARTMENT_FEE_ITEMS.DESCRIPTION
     *
     * @mbg.generated Tue Apr 09 16:31:49 CST 2019
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }
}