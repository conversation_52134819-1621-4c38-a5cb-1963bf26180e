package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeeInPutFeePlainInvoiceCommand;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeInPutFeePlainInvoiceMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInPutFeePlainInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInPutFeePlainInvoiceConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * 金蝶  进项费用普通发票
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeInPutFeePlainInvoice {

    @Autowired
    private KingDeeInPutFeePlainInvoiceCommandConvertor inPutFeePlainInvoiceCommandConvertor;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeInPutFeePlainInvoiceMapper inPutFeePlainInvoiceMapper;

    @Autowired
    private KingDeeInPutFeePlainInvoiceConvertor inPutFeePlainInvoiceConvertor;

    @Test
    public void kingDeOutPutFeePlainInvoice() {

        InPutFeePlainInvoiceDto dto = new InPutFeePlainInvoiceDto();
        dto.setFid("0");
        //贝登单据头
        dto.setFQzokBddjtid("BD12222");
        //发票号
        dto.setFinvoiceno("FP1233443");
        //发票代码
        dto.setFQzokFpdm("DM2222");
        dto.setFinvoicedate("2022-11-10 00:00:00");
        dto.setFdate("2022-11-10 00:00:00");
        dto.setFcontactunittype("BD_Supplier");
        dto.setFBillTypeID("FYFP01_SYS");
        dto.setFsupplierid("VEN0006");
        dto.setFsettleorgid("101");
        dto.setFpurchaseorgid("101");
        dto.setFdocumentstatus("Z");
        dto.setFRedBlue("0");

        List<InPutFeePlainInvoiceDetailDto> FPUREXPINVENTRY = new ArrayList<>();
        InPutFeePlainInvoiceDetailDto sonDto = new InPutFeePlainInvoiceDetailDto();
        sonDto.setFexpenseid("026");
        sonDto.setFQty("1");
        sonDto.setFunitprice(new BigDecimal(106));
        sonDto.setFtaxrate("6");
        sonDto.setFQzokBddjhid("122");
        sonDto.setFQzokYsddh("BD1111111");
        sonDto.setFQzokGsywdh("BD999888");
        sonDto.setFQzokYwlx("ADD_O");
        sonDto.setFQzokBdsku("V444");
        sonDto.setFsourcetype("AP_Payable");

        List<InPutFeePlainInvoiceDetailLinkDto> FPUREXPINVENTRY_Link = new ArrayList<>();
        InPutFeePlainInvoiceDetailLinkDto inSon = new InPutFeePlainInvoiceDetailLinkDto();
        inSon.setFLinkId("0");
        inSon.setFpurexpinventryLinkFflowid("");
        inSon.setFpurexpinventryLinkFflowlineid("0");
        inSon.setFpurexpinventryLinkFruleid("IV_PayableToPUREXVATIN");
        inSon.setFpurexpinventryLinkFstableid("0");
        inSon.setFpurexpinventryLinkFstablename("T_AP_PAYABLEENTRY");
        inSon.setFpurexpinventryLinkFsbillid("100091");
        inSon.setFpurexpinventryLinkFsid("100096");
        inSon.setFpurexpinventryLinkFamountforDold("106");
        inSon.setFpurexpinventryLinkFamountforD("106");
        FPUREXPINVENTRY_Link.add(inSon);
        sonDto.setFPUREXPINVENTRY_Link(FPUREXPINVENTRY_Link);
        FPUREXPINVENTRY.add(sonDto);

        dto.setFPUREXPINVENTRY(FPUREXPINVENTRY);

        System.out.println("dto-->"+JSON.toJSON(dto));
        //转换为金蝶入参
        KingDeeInPutFeePlainInvoiceCommand command = inPutFeePlainInvoiceCommandConvertor.toCommand(dto);
        System.out.println("入参commad-->"+JSON.toJSON(command));
        //调用金蝶方法
        RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, dto.getFormId()));
        ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();

        System.out.println("金蝶返回-->"+JSON.toJSON(successEntities));

        if (CollUtil.isNotEmpty(successEntities)) {
            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
            dto.setFid(successEntity.getId());
            //金蝶业务数据入表
            inPutFeePlainInvoiceMapper.insertSelective(inPutFeePlainInvoiceConvertor.toEntity(dto));
        System.out.println("finish-->");
        }
    }

    //调用记录 - 1
    //{"fID":"0","fSUPPLIERID":{"fNumber":"VEN0006"},"f_QZOK_FPDM":"DM2222","fPUREXPINVENTRY":[{"fEXPENSEID":{"fNumber":"026"},"fSOURCETYPE":"AP_Payable","f_QZOK_GSYWDH":"BD999888","f_QZOK_YSDDH":"BD1111111","f_QZOK_YWLX":"ADD_O","fPUREXPINVENTRY_Link":[{"fPUREXPINVENTRY_Link_FSTableName":"T_AP_PAYABLEENTRY","fPUREXPINVENTRY_Link_FAMOUNTFOR_D":106,"fPUREXPINVENTRY_Link_FRuleId":"IV_PayableToPUREXVATIN","fPUREXPINVENTRY_Link_FSTableId":"0","fLinkId":"0","fPUREXPINVENTRY_Link_FAMOUNTFOR_DOLD":106,"fPUREXPINVENTRY_Link_FFlowId":"","fPUREXPINVENTRY_Link_FSBillId":"100091","fPUREXPINVENTRY_Link_FFlowLineId":"0","fPUREXPINVENTRY_Link_FSId":"100096"}],"fTAXRATE":"6","f_QZOK_BDSKU":"V444","fUNITPRICE":106,"fQty":"1","f_QZOK_BDDJHID":"122"}],"f_QZOK_BDDJTID":"BD12222","fINVOICENO":"FP1233443","fSETTLEORGID":{"fNumber":"101"},"fDATE":"2022-11-10 00:00:00","fCONTACTUNITTYPE":"BD_Supplier","fPURCHASEORGID":{"fNumber":"101"},"fDOCUMENTSTATUS":"Z","fINVOICEDATE":"2022-11-10 00:00:00","fRedBlue":"0","fBillTypeID":{"fNumber":"FYFP01_SYS"}}

    //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"IsSuccess":true,"Errors":[],"SuccessEntitys":[{"Id":100032,"Number":"EXINV00000004","DIndex":0}],"SuccessMessages":[],"MsgCode":0},"Id":100032,"Number":"EXINV00000004","NeedReturnData":[{}]}}]
    //金蝶返回-->[{"number":"EXINV00000004","dIndex":0,"id":"100032"}]

}
