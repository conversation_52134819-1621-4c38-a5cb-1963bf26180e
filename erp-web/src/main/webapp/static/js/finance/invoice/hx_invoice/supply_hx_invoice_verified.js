/**
 * 重置搜索框的值
 */
function searchReset() {
    $('#invoiceNum').val('');
    $('#saleorderNo').val('');
    $('#salerName').val('');
    $('#invoiceTaxRate').val(0);
    $('#invoiceAmountFrom').val('');
    $('#invoiceAmountTo').val('');
    $('#validStatus').val(0);
    $('#entryUser').val(0);
    $('#timeSearchType').val(0);
    $('#startAddDateStr').val('');
    $('#endAddDateStr').val('');
}

/**
 * iframe之间切换选项卡
 * @param index 选项卡下标
 */
function changeTab(index) {
    window.location = '/supplyChain/invoice/hx_invoice_wait.do?idFlag=' + index;
}

/**
 * @desc 校验数字框输入合法性
 * @param obj
 * <AUTHOR>
 * @date 2020/6/9 14:35:35
 */
function checkValue(obj) {
    if ($(obj).val() != undefined && $(obj).val() != ''){
        var reg =  /^\d+(?=\.{0,1}\d+$|$)/;
        if (!reg.test($(obj).val())) {
            layer.alert('请输入数字!');
            $(obj).val('');
        }
    }
}