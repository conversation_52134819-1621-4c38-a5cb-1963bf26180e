package com.wms.dto;

public class WMSResultInfo {

    private String returnCode;

    private String returnDesc;

    private String returnFlag;

    public WMSResultInfo(String returnCode, String returnDesc, String returnFlag) {
        this.returnCode = returnCode;
        this.returnDesc = returnDesc;
        this.returnFlag = returnFlag;
    }

    public static WMSResultInfo success(){
        return new WMSResultInfo("0001","ok","1");
    }

    public static WMSResultInfo error(String message){
        return new WMSResultInfo("0000",message,"0");
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnDesc() {
        return returnDesc;
    }

    public void setReturnDesc(String returnDesc) {
        this.returnDesc = returnDesc;
    }

    public String getReturnFlag() {
        return returnFlag;
    }

    public void setReturnFlag(String returnFlag) {
        this.returnFlag = returnFlag;
    }
}
