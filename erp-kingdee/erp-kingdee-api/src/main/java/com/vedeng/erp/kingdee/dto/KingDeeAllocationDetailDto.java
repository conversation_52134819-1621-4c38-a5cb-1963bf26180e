package com.vedeng.erp.kingdee.dto;

import lombok.*;

/**
 * 调拨单 dto  https://www.yuque.com/manhuo/gf1570/my2awk
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶调拨单 dto  由erp实际业务转换
 * @date
 */
@Data
@Getter
@Setter
@ToString
@NoArgsConstructor
public class KingDeeAllocationDetailDto {
    /**
     * fMaterialId
     */
    private String fMaterialId;
    /**
     * 调拨数量
     */
    private String fQty;
    /**
     * 调出仓库
     */
    private String fSrcStockId;
    /**
     * 调入仓库
     */
    private String fDestStockId;
    /**
     * 借货的客户id
     */
    private String fQzokJdkhid;
    /**
     * 原始订单号
     */
    private String fQzokYsddh;
    /**
     * 归属业务单号
     */
    private String fQzokGsywdh;
    /**
     * 业务类型
     */
    private String fQzokYwlx;
    /**
     * 批次号
     */
    private String fQzokPch;
    /**
     * 序列号
     */
    private String fQzokXlh;

}
