package com.vedeng.erp.trader.mapper;

import java.util.List;

import com.vedeng.erp.trader.domain.entity.TraderPaidAccountEntity;
import org.apache.ibatis.annotations.Param;

public interface TraderPaidAccountMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(TraderPaidAccountEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(TraderPaidAccountEntity record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    TraderPaidAccountEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(TraderPaidAccountEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(TraderPaidAccountEntity record);

    List<TraderPaidAccountEntity> findByAll(TraderPaidAccountEntity traderPaidAccountEntity);

    int updateBatchSelective(List<TraderPaidAccountEntity> list);

    int batchInsert(List<TraderPaidAccountEntity> list);

    /**
     * 获取条件账号唯一
     *
     * @param traderName        账号名称
     * @param traderPaidAccount 账号
     * @param isDel 是否删除
     * @return
     */
    TraderPaidAccountEntity findByTraderNameAndTraderPaidAccountAndIsDel(@Param("traderName") String traderName, @Param("traderPaidAccount") String traderPaidAccount, @Param("isDel") Integer isDel);

    /**
     * 获取条件账
     *
     * @param traderPaidAccount 账号
     * @param isDel 是否删除
     * @return
     */
    List<TraderPaidAccountEntity> findByTraderPaidAccountAndIsDel(@Param("traderPaidAccount")String traderPaidAccount,@Param("isDel")Integer isDel);



}