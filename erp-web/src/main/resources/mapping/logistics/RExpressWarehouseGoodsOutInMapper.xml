<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.logistics.dao.RExpressWarehouseGoodsOutInMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.dto.RExpressWarehouseGoodsOutInDto">
    <!--@mbg.generated-->
    <!--@Table T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN-->
    <id column="R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID" jdbcType="INTEGER" property="rExpressWarehouseGoodsOutInId" />
    <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId" />
    <result column="WAREHOUSE_GOODS_OUT_IN_ID" jdbcType="INTEGER" property="warehouseGoodsOutInId" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID, EXPRESS_ID, WAREHOUSE_GOODS_OUT_IN_ID, IS_DELETE,
    ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    where R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{rExpressWarehouseGoodsOutInId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    where R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{rExpressWarehouseGoodsOutInId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID" keyProperty="rExpressWarehouseGoodsOutInId" parameterType="com.vedeng.logistics.model.dto.RExpressWarehouseGoodsOutInDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN (EXPRESS_ID, WAREHOUSE_GOODS_OUT_IN_ID,
    IS_DELETE, ADD_TIME, MOD_TIME,
    CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME)
    values (#{expressId,jdbcType=INTEGER}, #{warehouseGoodsOutInId,jdbcType=INTEGER},
    #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP},
    #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR},
    #{updaterName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID" keyProperty="rExpressWarehouseGoodsOutInId" parameterType="com.vedeng.logistics.model.dto.RExpressWarehouseGoodsOutInDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      <if test="warehouseGoodsOutInId != null">
        WAREHOUSE_GOODS_OUT_IN_ID,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      <if test="warehouseGoodsOutInId != null">
        #{warehouseGoodsOutInId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.logistics.model.dto.RExpressWarehouseGoodsOutInDto">
    <!--@mbg.generated-->
    update T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    <set>
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      <if test="warehouseGoodsOutInId != null">
        WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{rExpressWarehouseGoodsOutInId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.logistics.model.dto.RExpressWarehouseGoodsOutInDto">
    <!--@mbg.generated-->
    update T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    set EXPRESS_ID = #{expressId,jdbcType=INTEGER},
    WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=INTEGER},
    IS_DELETE = #{isDelete,jdbcType=INTEGER},
    ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
    MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    CREATOR = #{creator,jdbcType=INTEGER},
    UPDATER = #{updater,jdbcType=INTEGER},
    CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
    UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{rExpressWarehouseGoodsOutInId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-04-03-->
  <update id="updateByExpressId">
    update T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    <set>
      <if test="updated.rExpressWarehouseGoodsOutInId != null">
        R_EXPRESS_WAREHOUSE_GOODS_OUT_IN_ID = #{updated.rExpressWarehouseGoodsOutInId,jdbcType=INTEGER},
      </if>
      <if test="updated.expressId != null">
        EXPRESS_ID = #{updated.expressId,jdbcType=INTEGER},
      </if>
      <if test="updated.warehouseGoodsOutInId != null">
        WAREHOUSE_GOODS_OUT_IN_ID = #{updated.warehouseGoodsOutInId,jdbcType=INTEGER},
      </if>
      <if test="updated.isDelete != null">
        IS_DELETE = #{updated.isDelete,jdbcType=INTEGER},
      </if>
      <if test="updated.addTime != null">
        ADD_TIME = #{updated.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.modTime != null">
        MOD_TIME = #{updated.modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.creator != null">
        CREATOR = #{updated.creator,jdbcType=INTEGER},
      </if>
      <if test="updated.updater != null">
        UPDATER = #{updated.updater,jdbcType=INTEGER},
      </if>
      <if test="updated.creatorName != null">
        CREATOR_NAME = #{updated.creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updated.updaterName != null">
        UPDATER_NAME = #{updated.updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where EXPRESS_ID=#{expressId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-04-03-->
  <update id="logicaldeleteByExpressId">
    update  T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN set IS_DELETE = 1
    where EXPRESS_ID=#{expressId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-04-03-->
  <select id="selectByExpressId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_R_EXPRESS_WAREHOUSE_GOODS_OUT_IN
    where EXPRESS_ID=#{expressId,jdbcType=INTEGER}
  </select>
</mapper>