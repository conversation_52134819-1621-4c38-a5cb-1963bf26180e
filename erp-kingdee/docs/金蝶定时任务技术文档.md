# 金蝶定时任务技术文档

## 目录
- [1. 系统架构概览](#1-系统架构概览)
- [2. 任务分类详解](#2-任务分类详解)
- [3. 详细任务清单](#3-详细任务清单)
- [4. 执行流程分析](#4-执行流程分析)
- [5. 技术实现说明](#5-技术实现说明)

## 1. 系统架构概览

### 1.1 任务架构图

```mermaid
graph TB
    subgraph "金蝶定时任务系统"
        subgraph "数据同步层"
            A1[银行流水拉取]
            A2[基础数据同步]
            A3[物料数据初始化]
        end
        
        subgraph "业务流程层"
            B1[采购订单流程]
            B2[销售订单流程]
            B3[售后处理流程]
            B4[发票处理流程]
            B5[库存管理流程]
        end
        
        subgraph "补偿机制层"
            C1[MQ消息补偿]
            C2[基础数据补偿]
            C3[错误告警处理]
        end
        
        subgraph "支撑服务层"
            D1[Spring Batch]
            D2[XXL-Job调度]
            D3[金蝶API接口]
            D4[RabbitMQ]
        end
    end
    
    A1 --> D3
    A2 --> D3
    A3 --> D3
    B1 --> D1
    B2 --> D1
    B3 --> D1
    B4 --> D1
    B5 --> D1
    C1 --> D4
    C2 --> D1
    C3 --> D4
    
    style A1 fill:#e1f5fe
    style A2 fill:#e1f5fe
    style A3 fill:#e1f5fe
    style B1 fill:#f3e5f5
    style B2 fill:#f3e5f5
    style B3 fill:#f3e5f5
    style B4 fill:#f3e5f5
    style B5 fill:#f3e5f5
    style C1 fill:#fff3e0
    style C2 fill:#fff3e0
    style C3 fill:#fff3e0
```

### 1.2 任务分布统计

| 任务类型 | 数量 | 主要功能 |
|---------|------|----------|
| 数据同步任务 | 8个 | 银行流水、基础数据、物料信息同步 |
| 采购业务流程 | 12个 | 采购订单、售后、费用处理 |
| 销售业务流程 | 15个 | 销售订单、售后、预付款处理 |
| 库存管理流程 | 6个 | 入库、出库、库存调整 |
| 发票处理流程 | 5个 | 发票生成、作废、附件处理 |
| 补偿机制任务 | 4个 | MQ补偿、数据补偿、错误处理 |

## 2. 任务分类详解

### 2.1 数据同步任务

#### 银行流水同步
- **KingDeePullBankBillTask**: 通用银行流水拉取
- **KingDeeHongRuiPullBankBillTask**: 鸿瑞银行流水拉取
- **BankBillBatchTask**: 银行流水批处理
- **PaymentBankBillNewBatchTask**: 已结算付款银行流水推送

#### 基础数据同步
- **BaseInfoBatchTask**: 审计基础信息批处理
- **BaseInfoCompensateTask**: 基础数据补偿任务
- **KingDeeMaterialInitTask**: 物料数据初始化
- **KingDeeTraderCustomerDateTask**: 客户数据推送
- **KingDeeTraderSupplierDateTask**: 供应商数据推送

### 2.2 业务流程任务

#### 采购订单流程
- **BuyorderBatchTask**: 采购订单主流程
- **BuyOrderAfterSaleBatchTask**: 采购售后退货
- **BuyOrderAfterSaleBalanceBatchTask**: 采购售后收款
- **BuyOrderAfterSaleExchangeBatchTask**: 采购换货售后
- **BuyOrderAfterSaleOnlyInvoiceBatchTask**: 采购仅退票
- **BuyOrderExpenseBatchTask**: 采购订单费用
- **BuyOrderExpenseAfterSaleBatchTask**: 采购费用售后
- **GiftBuyOrderBatchTask**: 赠品单入库
- **GiftBuyOrderAfterSaleBatchTask**: 赠品售后

#### 销售订单流程
- **SaleOrderAdvanceBatchTask**: 销售蓝票提前开票
- **SaleOrderAfterSaleExchangeBatchTask**: 销售换货出入库
- **SaleOrderAfterSaleGiftPayTask**: 销售售后入库赠品应付单
- **SaleOrderAfterSalePayExpenseBatchTask**: 销售售后录票费用
- **SaleOrderExpenseBatchTask**: 销售订单费用商品
- **SaleOrderExpenseAfterSaleBatchTask**: 销售费用售后
- **SaleOrderGiftPayTask**: 销售赠品应付单
- **SaleOrderPayByBalanceBatchTask**: 销售订单余额支付
- **SaleSettlementAdjustmentBatchTask**: 销售结算调整

### 2.3 补偿机制任务

- **KingDeeMqCompensateTask**: MQ异步事件补偿
- **KingDeeMqCompensateByEventTypeTask**: 按事件类型MQ补偿
- **KingDeeMqErrorTask**: MQ重试异常微信告警

## 3. 详细任务清单

### 3.1 根目录任务

| 任务名称 | JobHandler值 | 功能描述 | 参数格式 | 执行频率 |
|---------|-------------|----------|----------|----------|
| KingDeePullBankBillTask | KingDeePullBankBillTask | 拉取金蝶银行流水数据 | `{"beginTime":"2022-11-01 00:00:00","endTime":"2022-12-01 00:00:00"}` | 按需 |
| KingDeeHongRuiPullBankBillTask | KingDeeHongRuiPullBankBillTask | 鸿瑞拉取金蝶银行流水 | 同上 | 按需 |
| KingDeeMqCompensateTask | KingDeeMqCompensateTask | 金蝶MQ异步事件补偿 | `Integer id` (可选) | 定时 |
| KingDeeMqCompensateByEventTypeTask | KingDeeMqCompensateByEventTypeTask | 按事件类型MQ补偿 | `String flag` | 定时 |
| KingDeeMqErrorTask | KingDeeMqErrorTask | 金蝶重试异常微信告警 | 无参数 | 定时 |

### 3.2 批处理任务 (batch目录)

| 任务名称 | JobHandler值 | 功能描述 | 业务分类 |
|---------|-------------|----------|----------|
| BuyorderBatchTask | BuyorderBatchTask | 采购订单流程批处理 | 采购流程 |
| BalancePaymentBatchTask | BalancePaymentBatchTask | 余额扣减批处理 | 支付流程 |
| BankBillBatchTask | BankBillBatchTask | 银行流水批处理 | 数据同步 |
| BankFileBatchTask | BankFileBatchTask | 银行文件批处理 | 数据同步 |
| BaseInfoBatchTask | BaseInfoBatchTask | 审计基础信息批处理 | 基础数据 |
| BaseInfoCompensateTask | BaseInfoCompensateTask | 基础数据补偿任务 | 补偿机制 |
| BatchFlowOrderBatchTask | BatchFlowOrderBatchTask | 批次流程订单处理 | 流程管理 |
| BuyOrderAfterSaleBatchTask | BuyOrderAfterSaleBatchTask | 采购售后退货批处理 | 采购售后 |
| BuyOrderAfterSaleBalanceBatchTask | BuyOrderAfterSaleBalanceBatchTask | 采购售后收款批处理 | 采购售后 |
| BuyOrderAfterSaleExchangeBatchTask | BuyOrderAfterSaleExchangeBatchTask | 采购换货售后批处理 | 采购售后 |
| BuyOrderAfterSaleOnlyInvoiceBatchTask | BuyOrderAfterSaleOnlyInvoiceBatchTask | 采购仅退票批处理 | 采购售后 |
| BuyOrderExpenseBatchTask | BuyOrderExpenseBatchTask | 采购订单费用批处理 | 采购流程 |
| BuyOrderExpenseAfterSaleBatchTask | BuyOrderExpenseAfterSaleBatchTask | 采购费用售后批处理 | 采购售后 |
| ExpressCostBatchTask | ExpressCostBatchTask | 快递成本批处理 | 物流管理 |
| ExpressReceiptBatchTask | ExpressReceiptBatchTask | 快递签收批处理 | 物流管理 |
| GenerateDirectPurchaseOutAndInBatchTask | GenerateDirectPurchaseOutAndInBatchTask | 直采出入库生成 | 库存管理 |
| GiftBuyOrderBatchTask | GiftBuyOrderBatchTask | 赠品单入库批处理 | 采购流程 |
| GiftBuyOrderAfterSaleBatchTask | GiftBuyOrderAfterSaleBatchTask | 赠品售后批处理 | 采购售后 |
| InvoiceAttachmentBatchTask | InvoiceAttachmentBatchTask | 发票附件推送 | 发票管理 |
| OrderContractAndConfirmationBatchTask | OrderContractAndConfirmationBatchTask | 订单合同确认批处理 | 订单管理 |
| OtherWarehouseInBatchTask | OtherWarehouseInBatchTask | 其他入库批处理 | 库存管理 |
| OtherWarehouseInOutHistoryBatchTask | OtherWarehouseInOutHistoryBatchTask | 其他出入库历史批处理 | 库存管理 |
| PaymentBankBillNewBatchTask | PaymentBankBillNewBatchTask | 已结算付款银行流水推送 | 支付流程 |
| RollbackInvoiceBatchTask | RollbackInvoiceBatchTask | 蓝字发票作废处理 | 发票管理 |
| SaleOrderAdvanceBatchTask | SaleOrderAdvanceBatchTask | 销售蓝票提前开票 | 销售流程 |
| SaleOrderAfterSaleExchangeBatchTask | SaleOrderAfterSaleExchangeBatchTask | 销售换货出入库 | 销售售后 |
| SaleOrderAfterSaleGiftPayTask | SaleOrderAfterSaleGiftPayTask | 销售售后入库赠品应付单 | 销售售后 |
| SaleOrderAfterSalePayExpenseBatchTask | SaleOrderAfterSalePayExpenseBatchTask | 销售售后录票费用应付单 | 销售售后 |
| SaleOrderExpenseBatchTask | SaleOrderExpenseBatchTask | 销售订单费用商品流程 | 销售流程 |
| SaleOrderExpenseAfterSaleBatchTask | SaleOrderExpenseAfterSaleBatchTask | 销售费用售后批处理 | 销售售后 |
| SaleOrderGiftPayTask | SaleOrderGiftPayTask | 销售赠品应付单 | 销售流程 |
| SaleOrderPayByBalanceBatchTask | SaleOrderPayByBalanceBatchTask | 销售订单余额支付 | 销售流程 |
| SaleSettlementAdjustmentBatchTask | SaleSettlementAdjustmentBatchTask | 销售结算调整 | 销售流程 |
| SerialNumberTraceBatchTask | SerialNumberTraceBatchTask | 序列号追踪批处理 | 库存管理 |
| AfterSaleToBalanceBatchTask | AfterSaleToBalanceBatchTask | 销售售后退款至客户余额 | 销售售后 |

### 3.3 数据任务 (data目录)

| 任务名称 | JobHandler值 | 功能描述 | 参数说明 |
|---------|-------------|----------|----------|
| KingDeeMaterialInitTask | KingDeeMaterialInitTask | 物料数据初始化推送金蝶 | 无参数：批量推送所有；有参数：指定SKU ID列表 |
| KingDeeTraderCustomerDateTask | KingDeeTraderCustomerDateTask | 历史客户数据推送金蝶 | 客户ID列表，逗号分隔 |
| KingDeeTraderSupplierDateTask | KingDeeTraderSupplierDateTask | 历史供应商数据推送金蝶 | 供应商ID列表，逗号分隔 |

### 3.4 混合任务 (batch/mix目录)

| 任务名称 | JobHandler值 | 功能描述 | 包含流程 |
|---------|-------------|----------|----------|
| BuyorderBatchAllTask | BuyorderBatchAllTask | 采购订单全流程批处理 | 发票作废 + 采购正向流程 |
| SaleOrderBatchAllTask | SaleOrderBatchAllTask | 销售订单全流程批处理 | 发票作废 + 销售正向流程 |

### 3.5 单一任务 (batch/single目录)

| 任务名称 | JobHandler值 | 功能描述 | 业务场景 |
|---------|-------------|----------|----------|
| BuyorderOnlyInTask | BuyorderOnlyInTask | 采购仅入库流程 | 采购退货入库 |
| BuyorderOnlyOutTask | BuyorderOnlyOutTask | 采购仅出库流程 | 采购正常出库 |
| SaleOrderOnlyInTask | SaleOrderOnlyInTask | 销售仅入库流程 | 销售退货入库 |
| SaleOrderOnlyOutTask | SaleOrderOnlyOutTask | 销售仅出库流程 | 销售正常出库 |

## 4. 执行流程分析

### 4.1 采购订单主流程 (BuyorderBatchTask)

```mermaid
flowchart TD
    A[开始执行] --> B[虚拟发票入库]
    B --> C[虚拟发票出库]
    C --> D[虚拟发票蓝字绑定]
    D --> E[虚拟发票红字绑定]
    E --> F[采购发票仓库入库]
    F --> G[采购发票仓库出库]
    G --> H[采购入库]
    H --> I[验收单]
    I --> J[发票付款通用]
    J --> K[发票]
    K --> L[虚拟发票付款通用]
    L --> M[虚拟发票]
    M --> N[发票费用付款通用]
    N --> O[发票费用]
    O --> P[流程结束]

    style A fill:#e8f5e8
    style P fill:#ffe8e8
    style J fill:#fff3cd
    style L fill:#fff3cd
    style N fill:#fff3cd
```

### 4.2 MQ补偿机制流程

```mermaid
sequenceDiagram
    participant Scheduler as XXL-Job调度器
    participant Task as 补偿任务
    participant MsgService as 消息服务
    participant KingDee as 金蝶API
    participant Alert as 告警系统

    Scheduler->>Task: 触发补偿任务
    Task->>MsgService: 获取重试消息列表
    MsgService-->>Task: 返回待补偿消息

    loop 处理每个消息
        Task->>MsgService: 执行消息处理
        MsgService->>KingDee: 调用金蝶接口
        alt 处理成功
            KingDee-->>MsgService: 返回成功
            MsgService-->>Task: 标记成功
        else 处理失败
            KingDee-->>MsgService: 返回失败
            MsgService-->>Task: 记录失败
            Task->>Alert: 发送告警
        end
    end

    Task-->>Scheduler: 返回执行结果
```

### 4.3 银行流水同步流程

```mermaid
graph LR
    A[定时触发] --> B{参数检查}
    B -->|有参数| C[解析时间范围]
    B -->|无参数| D[默认昨天到今天]
    C --> E[调用银行流水服务]
    D --> E
    E --> F[拉取金蝶银行流水]
    F --> G[数据处理和存储]
    G --> H[记录执行日志]
    H --> I[返回执行结果]

    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style I fill:#e8f5e8
```

### 4.4 Spring Batch 执行模式

```mermaid
graph TB
    subgraph "Spring Batch 执行架构"
        A[JobLauncher] --> B[Job]
        B --> C[Step1]
        B --> D[Step2]
        B --> E[StepN]

        subgraph "Step执行流程"
            F[Reader] --> G[Processor]
            G --> H[Writer]
        end

        C --> F
        D --> F
        E --> F
    end

    subgraph "任务参数处理"
        I[TaskBatchHandle] --> J[buildJobParameters]
        J --> K[JobParameters]
        K --> A
    end

    subgraph "监听器"
        L[JobListener]
        M[StepListener]
        N[ReadListener]
        O[WriteListener]
    end

    B -.-> L
    C -.-> M
    F -.-> N
    H -.-> O

    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style F fill:#fff3e0
    style G fill:#fff3e0
    style H fill:#fff3e0
```

## 5. 技术实现说明

### 5.1 核心技术栈

| 技术组件 | 版本 | 用途 | 配置说明 |
|---------|------|------|----------|
| XXL-Job | - | 分布式任务调度 | `@JobHandler` 注解配置 |
| Spring Batch | - | 批处理框架 | Job/Step/Reader/Processor/Writer |
| RabbitMQ | - | 消息队列 | MQ补偿机制 |
| 金蝶API | - | ERP系统集成 | RESTful接口调用 |

### 5.2 任务配置模式

#### 5.2.1 XXL-Job 任务配置
```java
@JobHandler(value = "TaskName")
@Component
public class TaskClass extends AbstractJobHandler {
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        // 任务执行逻辑
        return ReturnT.SUCCESS;
    }
}
```

#### 5.2.2 Spring Batch 任务配置
```java
@Autowired
private JobLauncher jobLauncher;

@Autowired
private BatchJob batchJob;

public ReturnT<String> execute(String param) throws Exception {
    JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
    Job job = batchJob.createJob();
    jobLauncher.run(job, jobParameters);
    return SUCCESS;
}
```

### 5.3 参数处理机制

#### 5.3.1 标准参数格式
```json
{
    "beginTime": "2022-11-01 00:00:00",
    "endTime": "2022-12-01 00:00:00",
    "timestamp": "1666687179395"
}
```

#### 5.3.2 参数处理逻辑
- **时间范围处理**: 默认昨天00:00:00到今天23:59:59
- **时间戳处理**: 用于任务去重和幂等性控制
- **特殊参数**: ID列表、标志位等业务参数

### 5.4 错误处理机制

#### 5.4.1 重试策略
```java
.faultTolerant()
.retryLimit(3)
.retry(Exception.class)
.skipPolicy(new CustomSkipPolicy())
```

#### 5.4.2 补偿机制
1. **MQ消息补偿**: 定时重试失败的MQ消息
2. **数据补偿**: 补偿未成功同步的基础数据
3. **告警机制**: 微信告警通知异常情况

### 5.5 监控和日志

#### 5.5.1 执行日志
- **XxlJobLogger**: XXL-Job 执行日志
- **业务日志**: 详细的业务处理日志
- **异常日志**: 错误信息和堆栈跟踪

#### 5.5.2 监控指标
- 任务执行成功率
- 任务执行时长
- 数据处理量统计
- 异常频率监控

### 5.6 性能优化

#### 5.6.1 批处理优化
- **Chunk Size**: 合理设置批处理块大小
- **并发处理**: 多线程处理提升性能
- **分页查询**: 避免大数据量内存溢出

#### 5.6.2 接口调用优化
- **连接池**: 复用HTTP连接
- **超时设置**: 合理的超时时间配置
- **限流机制**: 避免对金蝶系统造成压力

## 6. 运维指南

### 6.1 任务调度配置

#### 6.1.1 任务分类调度建议

| 任务类型 | 建议调度频率 | 执行时间窗口 | 注意事项 |
|---------|-------------|-------------|----------|
| 银行流水同步 | 每日1次 | 凌晨2:00 | 避开业务高峰期 |
| 业务流程批处理 | 每日1次 | 凌晨3:00-5:00 | 按依赖关系排序 |
| MQ补偿任务 | 每小时1次 | 全天候 | 及时处理失败消息 |
| 数据初始化 | 按需执行 | 业务低峰期 | 大数据量处理 |

#### 6.1.2 具体任务调度时间建议

| 执行时间 | 任务名称 | 优先级 | 依赖关系 |
|---------|----------|--------|----------|
| 02:00 | KingDeePullBankBillTask | 高 | 无 |
| 02:30 | BaseInfoBatchTask | 高 | 无 |
| 03:00 | BuyorderBatchTask | 高 | 依赖基础数据 |
| 03:30 | SaleOrderAdvanceBatchTask | 高 | 依赖基础数据 |
| 04:00 | BuyOrderAfterSaleBatchTask | 中 | 依赖采购主流程 |
| 04:30 | SaleOrderAfterSaleExchangeBatchTask | 中 | 依赖销售主流程 |
| 05:00 | ExpressCostBatchTask | 低 | 无强依赖 |
| 05:30 | InvoiceAttachmentBatchTask | 低 | 依赖发票生成 |
| 每小时 | KingDeeMqCompensateTask | 高 | 无 |
| 每4小时 | KingDeeMqErrorTask | 中 | 无 |

### 6.2 故障排查

#### 6.2.1 常见问题
1. **任务执行失败**: 检查参数格式、网络连接、金蝶API状态
2. **数据不一致**: 检查补偿任务执行情况
3. **性能问题**: 检查批处理配置、数据库连接池

#### 6.2.2 排查步骤
1. 查看XXL-Job执行日志
2. 检查应用程序日志
3. 验证金蝶API接口状态
4. 检查数据库数据状态
5. 执行补偿任务

### 6.3 监控指标详解

#### 6.3.1 关键性能指标 (KPI)

| 指标类型 | 指标名称 | 正常范围 | 告警阈值 | 监控频率 |
|---------|----------|----------|----------|----------|
| 执行成功率 | 任务成功率 | >95% | <90% | 实时 |
| 执行时长 | 平均执行时间 | <30分钟 | >60分钟 | 每次执行 |
| 数据处理量 | 每小时处理记录数 | 根据业务量 | 异常波动±50% | 每小时 |
| 错误频率 | 每日错误次数 | <5次 | >10次 | 每日 |
| 资源使用 | CPU/内存使用率 | <70% | >90% | 每5分钟 |

#### 6.3.2 业务监控指标

| 业务场景 | 监控指标 | 数据来源 | 告警条件 |
|---------|----------|----------|----------|
| 银行流水同步 | 同步数据量 | 金蝶API返回 | 连续2天无数据 |
| 采购订单处理 | 订单处理成功率 | 批处理日志 | 成功率<95% |
| 销售订单处理 | 订单处理时长 | 执行时间统计 | 超过预期时间2倍 |
| MQ消息补偿 | 补偿成功率 | 消息队列状态 | 补偿失败率>10% |
| 发票处理 | 发票生成成功率 | 发票系统反馈 | 失败率>5% |

### 6.4 维护建议

#### 6.4.1 日常维护
1. **定期清理**: 清理过期的执行日志和临时数据
2. **性能监控**: 监控任务执行时间和资源使用情况
3. **版本管理**: 记录任务配置变更和版本信息
4. **备份策略**: 重要配置和数据的备份机制

#### 6.4.2 定期检查清单

| 检查项目 | 检查频率 | 检查内容 | 负责人 |
|---------|----------|----------|--------|
| 任务执行状态 | 每日 | 检查前一日所有任务执行情况 | 运维人员 |
| 错误日志分析 | 每周 | 分析错误模式，优化任务配置 | 开发人员 |
| 性能趋势分析 | 每月 | 分析执行时间趋势，预测资源需求 | 技术负责人 |
| 配置备份验证 | 每季度 | 验证备份完整性和恢复流程 | 系统管理员 |

#### 6.4.3 应急处理流程

```mermaid
flowchart TD
    A[发现任务异常] --> B{异常类型判断}
    B -->|执行失败| C[检查错误日志]
    B -->|性能异常| D[检查系统资源]
    B -->|数据异常| E[检查数据一致性]

    C --> F[分析失败原因]
    D --> G[分析资源瓶颈]
    E --> H[执行数据补偿]

    F --> I{是否需要紧急修复}
    G --> I
    H --> I

    I -->|是| J[立即修复并重新执行]
    I -->|否| K[记录问题，计划修复]

    J --> L[验证修复效果]
    K --> M[更新问题跟踪]

    L --> N[更新监控规则]
    M --> N
    N --> O[完成处理]

    style A fill:#ffebee
    style J fill:#e8f5e8
    style O fill:#e3f2fd
```

---

**文档版本**: v1.0
**最后更新**: 2024年
**维护人员**: ERP开发团队
