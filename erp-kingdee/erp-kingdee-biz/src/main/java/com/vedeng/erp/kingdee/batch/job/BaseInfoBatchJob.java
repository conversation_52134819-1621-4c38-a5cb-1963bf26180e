package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchCustomerFinanceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchMaterialFinanceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchSupplierFinanceDto;
import com.vedeng.erp.kingdee.batch.processor.BatchCustomerProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchMaterialProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchSupplierProcessor;
import com.vedeng.erp.kingdee.batch.writer.BatchCustomerWriter;
import com.vedeng.erp.kingdee.batch.writer.BatchMaterialWriter;
import com.vedeng.erp.kingdee.batch.writer.BatchSupplierWriter;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/5 9:09
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class BaseInfoBatchJob extends BaseJob {

    @Autowired
    private BatchMaterialProcessor batchMaterialProcessor;

    @Autowired
    private BatchMaterialWriter batchMaterialWriter;

    @Autowired
    private BatchCustomerProcessor batchCustomerProcessor;

    @Autowired
    private BatchCustomerWriter batchCustomerWriter;

    @Autowired
    private BatchSupplierProcessor batchSupplierProcessor;

    @Autowired
    private BatchSupplierWriter batchSupplierWriter;



    public Job baseInfoFlowJob() {
        return jobBuilderFactory.get("baseInfoBatchJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(materialPush())
                .next(customerPush())
                .next(supplierPush())
                .build();
    }

    /**
     * 商品（物料）推送
     *
     * @return Step
     */
    private Step materialPush() {
        return stepBuilderFactory.get("materialPush")
                .<BatchMaterialFinanceDto, KingDeeMaterialDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchMaterialDtoItemReader(null, null))
                .processor(batchMaterialProcessor)
                .writer(batchMaterialWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 客户信息推送
     *
     * @return Step
     */
    private Step customerPush() {
        return stepBuilderFactory.get("customerPush")
                .<BatchCustomerFinanceDto, KingDeeCustomerDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchCustomerDtoItemReader(null, null))
                .processor(batchCustomerProcessor)
                .writer(batchCustomerWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 供应商信息推送
     *
     * @return Step
     */
    private Step supplierPush() {
        return stepBuilderFactory.get("supplierPush")
                .<BatchSupplierFinanceDto, KingDeeSupplierDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchSupplierDtoItemReader(null, null))
                .processor(batchSupplierProcessor)
                .writer(batchSupplierWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchMaterialFinanceDto> batchMaterialDtoItemReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchMaterialFinanceDto materialDto = new BatchMaterialFinanceDto();
        materialDto.setBeginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.offsetDay(new Date(),-7)) : DateUtil.parseDateTime(beginTime));
        materialDto.setEndTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime));
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchMaterialFinanceDto.class.getSimpleName(), materialDto);
    }



    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchCustomerFinanceDto> batchCustomerDtoItemReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchCustomerFinanceDto customerDto = new BatchCustomerFinanceDto();
        customerDto.setBeginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.offsetDay(new Date(),-7)) : DateUtil.parseDateTime(beginTime));
        customerDto.setEndTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime));
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchCustomerFinanceDto.class.getSimpleName(), customerDto);
    }



    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchSupplierFinanceDto> batchSupplierDtoItemReader(@Value("#{jobParameters['beginTime']}") String beginTime, @Value("#{jobParameters['endTime']}") String endTime) {
        BatchSupplierFinanceDto supplierDto = new BatchSupplierFinanceDto();
        supplierDto.setBeginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.offsetDay(new Date(),-7)) : DateUtil.parseDateTime(beginTime));
        supplierDto.setEndTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime));
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchSupplierFinanceDto.class.getSimpleName(), supplierDto);
    }

}