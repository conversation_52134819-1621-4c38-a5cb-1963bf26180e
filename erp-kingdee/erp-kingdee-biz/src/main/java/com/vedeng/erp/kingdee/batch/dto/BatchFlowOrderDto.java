package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 业务流转单主表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchFlowOrderDto extends BatchBaseDto {
    /**
     * 主键
     */
    private Long flowOrderId;

    /**
     * 业务流转单编号
     */
    private String flowOrderNo;

    /**
     * 基础业务订单ID
     */
    private Integer baseOrderId;

    /**
     * 基础业务订单编号
     */
    private String baseOrderNo;

    /**
     * 基础业务类型 1.采购 2.销售
     */
    private Integer baseBusinessType;

    /**
     * 审核状态，0:未审核, 1:已审核
     */
    private Integer auditStatus;



    /**
     * 流转单详细
     */
    List<BatchFlowOrderDetailDto> flowOrderDetailList;

    /**
     * 流转单节点
     * 交易者信息
     * 款票信息
     */
    private List<BatchFlowNodeDto> flowNodeDtoList;


    /**
     * 最大时间
     */
    private Date beginTime;

    /**
     * 最小时间
     */
    private Date endTime;


}