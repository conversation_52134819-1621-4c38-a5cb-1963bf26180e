package com.newtask;

import com.task.service.SalesPerformanceTaskService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.model.SearchModel;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.system.service.UserService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;

@JobHandler(value = "salesPerformanceCallTask")
@Component
@Slf4j
public class SalesPerformanceCallTask extends AbstractJobHandler {
 	@Autowired
	private SalesPerformanceTaskService salesPerformanceTaskService ;
	@Autowired
	private UserService userService ;
	@Value("${http_url}")
	protected String httpUrl;

	@Value("${client_id}")
	protected String clientId;

	@Value("${client_key}")
	protected String clientKey;
	@Override
	public ReturnT<String> doExecute(String s) throws Exception {
		try {
			this.callRecordInfoSync();
			return SUCCESS;
		}catch (Exception e){
			log.error("拉取通话记录录音地址失败",e);
			return ReturnT.FAIL;
		}
	}
	private void callRecordInfoSync() throws Exception {
		SearchModel searchModel = new SearchModel();
		Page page = new Page(1, ErpConst.EXPORT_DATA_LIMIT);
//		//获取前一天的月份第一天
//		//获取前一天的日
//		String dateString = DateUtil.convertString(DateUtil.getDateBefore(new Date(), 1), "yyyy-MM-dd");
//		//获取前一天的所在月的第一日
//		String yearMonth = DateUtil.getFirstDayOfGivenMonth(dateString, "yyyy-MM-dd");
//		long startTime = DateUtil.convertLong(yearMonth, "yyyy-MM-dd");
		// 获取当前时间
		Calendar calendar = Calendar.getInstance();
		// 设置为昨天
		calendar.add(Calendar.DAY_OF_YEAR, -1);
		// 设置时间为0点
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		// 获取昨天0点的时间戳
		long startTime = calendar.getTimeInMillis();
		long endTime = DateUtil.sysTimeMillis();
		searchModel.setStartDateLong(startTime);
		searchModel.setEndDateLong(endTime);
		salesPerformanceTaskService.callRecordInfoSync(searchModel,page);
	}

//	public static void main(String[] args) {
//		String dateString = DateUtil.convertString(DateUtil.getDateBefore(new Date(), 1), "yyyy-MM-dd");
//		System.out.println(dateString);
//		String yearMonth = DateUtil.getFirstDayOfGivenMonth(dateString, "yyyy-MM-dd");
//		System.out.println(yearMonth);
//		long startTime = DateUtil.convertLong(yearMonth, "yyyy-MM-dd");
//		System.out.println(startTime);
////		System.out.println(DateUtil.convertLong("2021-02-01", "yyyy-MM-dd"));
//
//		Calendar calendar = Calendar.getInstance();
//		// 设置为昨天
//		calendar.add(Calendar.DAY_OF_YEAR, -1);
//		// 设置时间为0点
//		calendar.set(Calendar.HOUR_OF_DAY, 0);
//		calendar.set(Calendar.MINUTE, 0);
//		calendar.set(Calendar.SECOND, 0);
//		calendar.set(Calendar.MILLISECOND, 0);
//		// 获取昨天0点的时间戳
//		long startTime1 = calendar.getTimeInMillis();
//		System.out.println(startTime1);
//
//	}


}
