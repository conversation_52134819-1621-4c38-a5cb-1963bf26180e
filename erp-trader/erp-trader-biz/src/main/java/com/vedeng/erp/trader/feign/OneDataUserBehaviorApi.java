package com.vedeng.erp.trader.feign;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.onedataapi.api.usertrace.UserBehaviorServiceApi;
import com.vedeng.onedataapi.api.usertrace.req.UserBehaviorDto;
import com.vedeng.onedataapi.api.usertrace.res.UserBehaviorResDto;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

@FeignApi(serverName = "onedataapi")
public interface OneDataUserBehaviorApi extends UserBehaviorServiceApi {
    /**
     * 获取用户轨迹
     *
     * @param userBehaviorDto
     * @return
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /user/online/behaviorList")
    @Override
    RestfulResult<UserBehaviorResDto> getUserBehaviorList(@RequestBody UserBehaviorDto userBehaviorDto);
}
