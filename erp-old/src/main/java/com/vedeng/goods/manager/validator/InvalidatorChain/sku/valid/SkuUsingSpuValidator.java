package com.vedeng.goods.manager.validator.InvalidatorChain.sku.valid;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.goods.model.CoreSpu;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.vedeng.goods.manager.validator.enums.GoodsValidatorErrorEnum.SkuUsingSpuValidatorError;

@Service
public class SkuUsingSpuValidator extends GoodsInvalidatorChain {

    @Resource
    CoreSpuMapper coreSpuMapper;

    @Override
    public void doInvalid() {
        String skuNo = getSkuNo();
        CoreSpu spuBySku = coreSpuMapper.getSpuBySku(skuNo);
        if(spuBySku !=null && spuBySku.getStatus() == 1){
            return  ;
        }
        throw new InvalidatorChainException(SkuUsingSpuValidatorError.getCode(),SkuUsingSpuValidatorError.getMessage());
    }
}
