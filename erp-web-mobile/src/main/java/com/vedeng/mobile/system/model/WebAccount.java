package com.vedeng.mobile.system.model;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/7
 */
/**
    * 注册用户
    */
public class WebAccount {
    /**
    * 自增ID
    */
    private Integer erpAccountId;

    /**
    * 网站用户ID
    */
    private Integer webAccountId;

    /**
    * sso账户ID
    */
    private Integer ssoAccountId;

    /**
    * 客户ID
    */
    private Integer traderId;

    /**
    * ERP联系人ID
    */
    private Integer traderContactId;

    /**
    * ERP联系地址ID
    */
    private Integer traderAddressId;

    /**
    * ERP归属人ID
    */
    private Integer userId;

    /**
    * 是否有效 0否 1是
    */
    private Boolean isEnable;

    /**
    * 由 来源1WEB,2微信,3APP(ios),4APP(android),5其它  改为 来源1PC端,2M端,3APP端,4APP端,5其它
    */
    private Byte from;

    /**
    * 0待审核1审核通过2审核不通过（三证审核状态）
    */
    private Boolean companyStatus;

    /**
    * 0待审核1审核通过2审核不通过（资质审核状态）
    */
    private Boolean indentityStatus;

    /**
    * 是否开通商家0未申请1申请待审核2审核通过3审核不通过
    */
    private Boolean isOpenStore;

    /**
    * 是否是贝登精选会员0否1是(已废弃)
    */
    private Boolean isVedengJx;

    /**
    * 用户名
    */
    private String account;

    /**
    * 邮箱地址
    */
    private String email;

    /**
    * 账号名（注册手机号码）
    */
    private String mobile;

    /**
    * 公司名称
    */
    private String companyName;

    /**
    * 注册姓名
    */
    private String name;

    /**
    * 性别:0女 1 男 2 保密
    */
    private Boolean sex;

    /**
    * 微信OPENID
    */
    private String weixinOpenid;

    /**
    * 唯一ID
    */
    private String uuid;

    /**
    * 创建时间
    */
    private Long addTime;

    /**
    * 最后登录时间
    */
    private Long lastLoginTime;

    /**
    * 是否申请加入:0 否 1 是
    */
    private Byte isVedengJoin;

    /**
    * 申请时间
    */
    private Long modTimeJoin;

    /**
    * 是否已推送过消息（0 否  1 是）
    */
    private Boolean isSendMessage;

    /**
    * 注册平台(1贝登医疗，2医械购，3科研购，4集团业务部，5其他，6集采)
    */
    private Integer registerPlatform;

    /**
    * 注册平台(1贝登医疗，2医械购，3科研购，4集团业务部，5其他，6集采)
    */
    private Integer belongPlatform;

    /**
    * 是否贝登会员0否1是
    */
    private Boolean isVedengMember;

    /**
    * 加入贝登会员时间
    */
    private Date vedengMemberTime;

    /**
    * 更新时间
    */
    private Date modTime;

    /**
    * 是否注册于区域商城，0否，1是
    */
    private Integer isRegionalMall;

    /**
    * 注册的区域商城id
    */
    private Integer registerRegionalMall;

    /**
    * 认证方式:1-营业执照认证 2-个人名片认证
    */
    private Integer authType;

    /**
    * 用户属性【0：未确认、1：个人、2：经销商、3：终端】
    */
    private Boolean accountType;

    /**
    * 用户角色【0：未确认、1：老板、2：采购经理、3：销售经理、4：技术或研发老师】
    */
    private Boolean accountRole;

    public Integer getErpAccountId() {
        return erpAccountId;
    }

    public void setErpAccountId(Integer erpAccountId) {
        this.erpAccountId = erpAccountId;
    }

    public Integer getWebAccountId() {
        return webAccountId;
    }

    public void setWebAccountId(Integer webAccountId) {
        this.webAccountId = webAccountId;
    }

    public Integer getSsoAccountId() {
        return ssoAccountId;
    }

    public void setSsoAccountId(Integer ssoAccountId) {
        this.ssoAccountId = ssoAccountId;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public Integer getTraderContactId() {
        return traderContactId;
    }

    public void setTraderContactId(Integer traderContactId) {
        this.traderContactId = traderContactId;
    }

    public Integer getTraderAddressId() {
        return traderAddressId;
    }

    public void setTraderAddressId(Integer traderAddressId) {
        this.traderAddressId = traderAddressId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Boolean isEnable) {
        this.isEnable = isEnable;
    }

    public Byte getFrom() {
        return from;
    }

    public void setFrom(Byte from) {
        this.from = from;
    }

    public Boolean getCompanyStatus() {
        return companyStatus;
    }

    public void setCompanyStatus(Boolean companyStatus) {
        this.companyStatus = companyStatus;
    }

    public Boolean getIndentityStatus() {
        return indentityStatus;
    }

    public void setIndentityStatus(Boolean indentityStatus) {
        this.indentityStatus = indentityStatus;
    }

    public Boolean getIsOpenStore() {
        return isOpenStore;
    }

    public void setIsOpenStore(Boolean isOpenStore) {
        this.isOpenStore = isOpenStore;
    }

    public Boolean getIsVedengJx() {
        return isVedengJx;
    }

    public void setIsVedengJx(Boolean isVedengJx) {
        this.isVedengJx = isVedengJx;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getSex() {
        return sex;
    }

    public void setSex(Boolean sex) {
        this.sex = sex;
    }

    public String getWeixinOpenid() {
        return weixinOpenid;
    }

    public void setWeixinOpenid(String weixinOpenid) {
        this.weixinOpenid = weixinOpenid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Long getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(Long lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public Byte getIsVedengJoin() {
        return isVedengJoin;
    }

    public void setIsVedengJoin(Byte isVedengJoin) {
        this.isVedengJoin = isVedengJoin;
    }

    public Long getModTimeJoin() {
        return modTimeJoin;
    }

    public void setModTimeJoin(Long modTimeJoin) {
        this.modTimeJoin = modTimeJoin;
    }

    public Boolean getIsSendMessage() {
        return isSendMessage;
    }

    public void setIsSendMessage(Boolean isSendMessage) {
        this.isSendMessage = isSendMessage;
    }

    public Integer getRegisterPlatform() {
        return registerPlatform;
    }

    public void setRegisterPlatform(Integer registerPlatform) {
        this.registerPlatform = registerPlatform;
    }

    public Integer getBelongPlatform() {
        return belongPlatform;
    }

    public void setBelongPlatform(Integer belongPlatform) {
        this.belongPlatform = belongPlatform;
    }

    public Boolean getIsVedengMember() {
        return isVedengMember;
    }

    public void setIsVedengMember(Boolean isVedengMember) {
        this.isVedengMember = isVedengMember;
    }

    public Date getVedengMemberTime() {
        return vedengMemberTime;
    }

    public void setVedengMemberTime(Date vedengMemberTime) {
        this.vedengMemberTime = vedengMemberTime;
    }

    public Date getModTime() {
        return modTime;
    }

    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    public Integer getIsRegionalMall() {
        return isRegionalMall;
    }

    public void setIsRegionalMall(Integer isRegionalMall) {
        this.isRegionalMall = isRegionalMall;
    }

    public Integer getRegisterRegionalMall() {
        return registerRegionalMall;
    }

    public void setRegisterRegionalMall(Integer registerRegionalMall) {
        this.registerRegionalMall = registerRegionalMall;
    }

    public Integer getAuthType() {
        return authType;
    }

    public void setAuthType(Integer authType) {
        this.authType = authType;
    }

    public Boolean getAccountType() {
        return accountType;
    }

    public void setAccountType(Boolean accountType) {
        this.accountType = accountType;
    }

    public Boolean getAccountRole() {
        return accountRole;
    }

    public void setAccountRole(Boolean accountRole) {
        this.accountRole = accountRole;
    }
}