package com.vedeng.erp.finance.domain.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 发票红字确认单
 */
@Getter
@Setter
public class RedConfirmReqApplyRequestDto implements Serializable {

    private static final long serialVersionUID = -7866470597164612400L;
    /**
     * 红字申请税金所需入参(部分红冲时必传)
     */
    private InvoiceRedConfirmationDto invoiceRedConfirmationDto = new InvoiceRedConfirmationDto();

    /**
     * 红冲范围
     * 0全部
     * 1部分
     */
    private Integer redConfirmationScope;


    /**
     * 批量红字确认单IDs
     */
    private List<Integer> invoiceRedConfirmationIds = new ArrayList<>();

}
