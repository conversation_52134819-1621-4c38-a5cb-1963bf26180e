package com.vedeng.api.standard.processor;

import com.vedeng.api.standard.core.ApiRequest;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 请求处理器接口
 * 定义请求预处理的标准规范
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
public interface RequestProcessor {
    
    /**
     * 预处理请求
     * 在业务逻辑执行前对请求进行预处理
     * 
     * @param apiRequest 统一请求对象
     */
    void preProcess(ApiRequest apiRequest);
    
    /**
     * 从HTTP请求中提取参数
     * 
     * @param httpRequest HTTP请求对象
     * @return 参数Map
     */
    Map<String, Object> extractParametersFromRequest(HttpServletRequest httpRequest);
    
    /**
     * 从HTTP请求中提取请求头
     * 
     * @param httpRequest HTTP请求对象
     * @return 请求头Map
     */
    Map<String, String> extractHeadersFromRequest(HttpServletRequest httpRequest);
    
    /**
     * 验证请求参数
     * 
     * @param apiRequest 统一请求对象
     * @throws IllegalArgumentException 参数验证失败时抛出
     */
    void validateRequest(ApiRequest apiRequest);
    
    /**
     * 记录请求日志
     * 
     * @param apiRequest 统一请求对象
     */
    void logRequest(ApiRequest apiRequest);
}
