package com.vedeng.erp.saleorder.buzlogic.create.impl;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.saleorder.buzlogic.create.CreateSaleOrderBuzLogic;
import com.vedeng.erp.saleorder.dao.SaleorderCopyRecordMapper;
import com.vedeng.erp.saleorder.domain.entity.SaleorderCopyRecordEntity;
import com.vedeng.erp.saleorder.service.BaseSaleOrderService;
import com.vedeng.order.enums.SaleOrderTypeEnum;
import com.vedeng.order.model.Quoteorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.BussinessChanceVo;
import com.vedeng.order.service.OrderCommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.vedeng.common.constant.CommonConstants.CREATE_SALE_ORDER_SUCCESS;

/**
 * <AUTHOR>
 */
@Service
public class RePurchaseCreateSaleOrderBuzLogic extends CreateSaleOrderBuzLogic {

    Logger logger= LoggerFactory.getLogger(RePurchaseCreateSaleOrderBuzLogic.class);

    @Autowired
    private BaseSaleOrderService baseSaleOrderService;

    @Autowired
    private OrderCommonService orderCommonService;

    public RePurchaseCreateSaleOrderBuzLogic() {
        List<String> sequence = new ArrayList<>();
        sequence.add("createOrder");
//        sequence.add("syncCreateBusiness");
//        sequence.add("syncCreateQuote");
        super.setSequence(sequence);
    }

    @Autowired
    private SaleorderCopyRecordMapper saleorderCopyRecordMapper;

    public ResultInfo<?> saveRePurchaseOrder(Saleorder saleorder){
        List<Integer> fromSaleorderIds = saleorder.getGoodsList().stream().map(SaleorderGoods::getSaleorderId).distinct().collect(Collectors.toList());

        ResultInfo<Saleorder> saleorderResultInfo = baseSaleOrderService.saveRePurchaseOrder(saleorder);

        try {
            Saleorder data = saleorderResultInfo.getData();
            Integer saleorderId = data.getSaleorderId();
            for (Integer fromSaleorderId : fromSaleorderIds) {
                SaleorderCopyRecordEntity saleorderCopyRecordEntity = new SaleorderCopyRecordEntity();
                saleorderCopyRecordEntity.setChannel(2);
                saleorderCopyRecordEntity.setSaleorderId(saleorderId);
                saleorderCopyRecordEntity.setFromSaleorderId(fromSaleorderId);
                saleorderCopyRecordMapper.insertSelective(saleorderCopyRecordEntity);
            }
        }catch (Exception e){
            logger.error("保存来源关系异常，不影响业务流程",e);
        }
        return saleorderResultInfo;
    }

    public ResultInfo<?> syncCreateBusiness(Saleorder saleorder){
        return baseSaleOrderService.syncCreateBusiness(saleorder);
    }

    public ResultInfo<?> syncCreateQuote(Saleorder saleorder,Integer businessChanceId){
        ResultInfo resultInfo = baseSaleOrderService.createOrderSyncCreateQuote(saleorder.getSaleorderId());
        if (resultInfo != null && ErpConst.ZERO.equals(resultInfo.getCode())){
            //设置报价单其它信息
            Quoteorder quoteorder = (Quoteorder) resultInfo.getData();
//            baseSaleOrderService.setQuoteOrderInfo(quoteorder, businessChanceId, saleorder);
        }
        return ResultInfo.success("创建报价单成功",saleorder);
    }

    /**
     * @desc 推送前台
     * @return
     */
    public ResultInfo pushFormal(Saleorder saleorder){
        if (SaleOrderTypeEnum.JCF.getType().equals(saleorder.getOrderType())) {
            orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(),null, OrderDataUpdateConstant.SALE_ORDER_GENERATE);
        }
        return new ResultInfo(0,"操作成功");
    }

    @Override
    public ResultInfo<?> run(Saleorder saleorder){
        ResultInfo<?> result = new ResultInfo<>();
        new RePurchaseCreateSaleOrderBuzLogic();
        for (String methodName : getSequence()){
            switch (methodName) {
                case "createOrder":
                    result = this.saveRePurchaseOrder(saleorder);
                    logger.info("同步创建销售单{},{},",saleorder.getSaleorderId(),result.getMessage());
                    break;
                case "pushFormal":
                    if (ErpConst.ZERO.equals(result.getCode())){
                        result = this.pushFormal(saleorder);
                    }
                    break;
                case "syncCreateBusiness":
                    if(ErpConst.ZERO.equals(result.getCode())) {
                        result = this.syncCreateBusiness(saleorder);
                        logger.info("同步创建销售单{},商机反馈{},",saleorder.getSaleorderId(),result.getMessage());
                    }
                    break;
                case "syncCreateQuote":
                    if(ErpConst.ZERO.equals(result.getCode())) {
                        if (CREATE_SALE_ORDER_SUCCESS.equals(result.getMessage())){
//                            result = this.syncCreateQuote(saleorder,null);
                            logger.info("同步创建销售单{},报价单{},",saleorder.getSaleorderId(),result.getMessage());
                            break;
                        }
                        BussinessChanceVo bussinessChanceVo = (BussinessChanceVo) result.getData();
//                        result = this.syncCreateQuote(saleorder,bussinessChanceVo.getBussinessChanceId());
                        logger.info("同步创建销售单{},报价单{},",saleorder.getSaleorderId(),result.getMessage());
                    }
                    break;
                default:
                    break;
            }

        }
        return result;
    }
}
