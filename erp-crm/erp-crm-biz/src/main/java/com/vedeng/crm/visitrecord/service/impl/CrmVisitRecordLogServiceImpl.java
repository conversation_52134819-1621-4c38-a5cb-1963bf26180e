package com.vedeng.crm.visitrecord.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordLogVo;
import com.vedeng.crm.visitrecord.enums.VisitOperationTypeEnum;
import com.vedeng.crm.visitrecord.mapper.CrmVisitRecordLogMapper;
import com.vedeng.crm.visitrecord.service.CrmVisitRecordLogService;
import com.vedeng.erp.system.dto.OperationLogDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.vedeng.common.mybatis.domain.PageParam;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/3
 */
@Slf4j
@Service
public class CrmVisitRecordLogServiceImpl implements CrmVisitRecordLogService {

    @Resource
    private CrmVisitRecordLogMapper crmVisitRecordLogMapper;

    @Override
    public void addOperationLog(Integer recordId, VisitOperationTypeEnum operationType, String content, CurrentUser currentUser){
        Integer operationTypeValue = operationType.getType();
        addOperationLog(recordId, operationTypeValue, content, currentUser);
    }

    @Override
    public void addOperationLog(Integer recordId, VisitOperationTypeEnum operationType, CurrentUser currentUser){
        Integer operationTypeValue = operationType.getType();
        String content  = operationType.getDesc();
        addOperationLog(recordId, operationTypeValue, content, currentUser);
    }

    @Override
    public void addOperationLog(Integer recordId, Integer operationType, String content, CurrentUser currentUser) {
        VisitRecordLogVo log = new VisitRecordLogVo();
        log.setRecordId(recordId);
        log.setOperationType(operationType);
        log.setOperationContent(content);
        log.setOperationTime(new Date());
        log.setAddTime(new Date());
        log.setAddUserId(currentUser.getId());
        log.setAddUserName(currentUser.getUsername());
        crmVisitRecordLogMapper.insert(log);
    }

    @Override
    public PageInfo<VisitRecordLogVo> getOperationLogs(PageParam<Integer> pageParam) {
        PageHelper.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        List<VisitRecordLogVo> list = crmVisitRecordLogMapper.selectPageByRecordId(pageParam.getParam());
        return new PageInfo<>(list);
    }

    @Override
    public PageInfo<OperationLogDto> selectPageByVisitRecordId(PageParam<Integer> pageParam) {
        PageHelper.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        List<OperationLogDto> list = crmVisitRecordLogMapper.selectPageByVisitRecordId(pageParam.getParam());
        return new PageInfo<>(list);
    }
}
