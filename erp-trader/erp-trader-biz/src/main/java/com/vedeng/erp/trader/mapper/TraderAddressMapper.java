package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.entity.TraderAddressEntity;
import java.util.List;

import com.vedeng.erp.trader.dto.TraderAddressDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.access.method.P;

import javax.inject.Named;

/**
 * <AUTHOR>
 * @description ${end}
 * @date 2022/10/20 19:38
 **/
@Named("newTraderAddressMapper")
public interface TraderAddressMapper {

    TraderAddressDto findTraderAddressById(Integer traderAddressId);

    /**
     * delete by primary key
     *
     * @param traderAddressId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer traderAddressId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(TraderAddressEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(TraderAddressEntity record);

    /**
     * select by primary key
     *
     * @param traderAddressId primary key
     * @return object by primary key
     */
    TraderAddressEntity selectByPrimaryKey(Integer traderAddressId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(TraderAddressEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(TraderAddressEntity record);

    int batchInsert(@Param("list") List<TraderAddressEntity> list);

    /**
     * 查询客户地址
     * @param query 条件
     * @return 客户地址
     */
    List<TraderAddressDto> findAll(TraderAddressDto query);

    /**
     * 查询最近交易过的2个客户联系地址
     *
     * @param traderId traderId
     * @return 客户地址id集合
     */
    List<Integer> getLatestTransactionAddress(@Param("traderId") Integer traderId);

    /**
     * 查询客户联系地址信息（迁移EZ列表）
     *
     * @param traderAddressIdList 联系地址id集合
     * @return List<TraderAddressDto>
     */
    List<TraderAddressDto> getByIdList(@Param("traderAddressIdList") List<Integer> traderAddressIdList);


    List<TraderAddressDto> findByTraderIdAndTraderTypeAndAddressLike(@Param("traderId")Integer traderId,
                                                                     @Param("traderType")Integer traderType,
                                                                     @Param("keywords")String keywords);



}