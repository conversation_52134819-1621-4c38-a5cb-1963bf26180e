package com.vedeng.infrastructure.shorturl.service;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.base.api.dto.reqParam.ShortUrlDto;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.infrastructure.shorturl.api.ShortUrlApi;
import com.vedeng.infrastructure.shorturl.dto.ShortUrlResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/9/25
 */
@Component
@Slf4j
public class ShortUrlApiService {

    @Value("${shorturl.appCode}")
    private String appCode;
    @Value("${shorturl.appSecret}")
    private String appSecret;

    @Value("${shorturl.domain}")
    private String urlDomain;


    @Resource
    private ShortUrlApi shortUrlApi;

    /**
     * 获取短链接
     * @param longLinkUrl
     * @return
     */
    public ShortUrlResponseDto getShortUrl(String longLinkUrl){
        if(StringUtils.isEmpty(longLinkUrl)){
            log.error("getShortUrl error ,longLinkUrl is empty");
            return null;
        }


        ShortUrlDto shortUrlDto = new ShortUrlDto();
        try {
            shortUrlDto.setLongUrl(longLinkUrl);
            shortUrlDto.setAppCode(appCode);
            String md5 = DigestUtil.md5Hex(longLinkUrl + appSecret);
            shortUrlDto.setSecretStr(StringUtils.upperCase(md5));
            RestfulResult<String> result = shortUrlApi.getShortUrl(shortUrlDto);
            if(Objects.nonNull(result)&& org.apache.commons.lang3.StringUtils.isNotBlank(result.getData())){
                String shortLinkUrl = result.getData();
                String shortCode = shortLinkUrl.replaceAll(urlDomain,"");

                return new ShortUrlResponseDto(shortLinkUrl,shortCode);
            }else {
                return null;
            }
        }catch (Exception e){
            log.error("shortUrlFeignApi.getShortUrl(shortUrlDto):{}", JSONObject.toJSONString(shortUrlDto),e);
            return null;
        }
    }

}
