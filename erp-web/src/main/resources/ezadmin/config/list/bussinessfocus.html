<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>商机关注列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">

</head>
<body id="bussiness" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show=""  firstcol="numbers">
<div id="appendHead">
    <style type="text/css">
        .edit-table-head{
            display: none;
        }
        button.ezopenbutton{
            display: none !important;
        }
        .layui-laydate-hint{
            display: none !important;
        }
    </style>
    <script type="text/javascript">
        $(document).ready(function() {
            // var objVisitorId1 = $('#itemName-VISITOR_ID');
            // // var xmel = $(objVisitorId)[0];
            // // //var initdata=objVisitorId.attr("itemsJson");
            // var initvalue1=objVisitorId1.attr("value");
            // // 创建隐藏域
            // var input = $('<input>').attr({
            //     type: 'hidden',
            //     id:'VISITOR_ID_HIDDEN',
            //     name: 'VISITOR_ID',
            //     value: initvalue1
            // });
            //
            // // 将隐藏域添加到form中
            // $('#searchForm').append(input);
        });
    </script>

</div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
            <!--        <li class="layui-this"><a href="/ezadmin/list/list-R8XBgDSwsUc">订单列表</a></li>-->
            <!--        <li><a href="/ezadmin/list/list-Jb8cQU_twaQ">售后列表</a></li>-->

        </ul>
    </div>
    <form class="layui-form" id="search">

        <div class=" layui-inline ">
            <label class="layui-form-label">商机编号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="BUSSINESS_CHANCE_NO" placeholder="请输入" style="" alias="CHANCE" jdbctype="" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">创建时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="CHANCE_ADD_TIME" placeholder="" style="" alias="FROM_UNIXTIME(CHANCE.ADD_TIME/1000)" jdbctype="" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">商机状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="STATUS" alias ="CHANCE" jdbctype="INTEGER" placeholder="全部"
                        data='[{"K": "0", "V": "未处理"}, {"K": "1", "V": "报价中"}, {"K": "2", "V": "已报价"}, {"K": "3", "V": "已订单"}, {"K": "4", "V": "已关闭"}, {"K": "6", "V": "处理中"}, {"K": "7", "V": "已成单"}]'
                        datatype="json" oper="in" ></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">客户名称</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="TRADER_NAME" placeholder="请输入" style="" alias="CHANCE.TRADER_NAME" jdbctype="" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">产品信息</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="PRODUCT_COMMENTS" placeholder="请输入" style="" alias="CHANCE" jdbctype="BODY" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">我关注时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="FOCUS_ADD_TIME" placeholder="" style="" alias="tcdo.ADD_TIME" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">我是否支持</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="select" name="IS_SUPPORT" alias ="CHANCE" jdbctype="BODY" placeholder="全部"
                        data='[{"K": "1", "V": "是"}, {"K": "0", "V": "否"}]'
                        datatype="json"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">最近支持时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="SUPPORT_ADD_TIME" placeholder="" style="" alias="record.ADD_TIME" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">最近评估级别</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="select" name="BUSINESS_LEVEL" alias ="record" jdbctype="INTEGER" placeholder="全部"
                        data='[{"K": "S", "V": "S"}, {"K": "A", "V": "A"}, {"K": "B", "V": "B"}, {"K": "C", "V": "C"}]'
                        datatype="json"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">最近评估成交时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="ORDER_DATE" placeholder="" style="" alias="record" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">下次支持时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="NEXT_SUPPORT_DATE" placeholder="" style="" alias="record" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">归属销售</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select-share" name="USER_ID" placeholder="" style="" radio="true" alias="CHANCE.USER_ID" jdbctype="INTEGER" data="${session.EZ_SESSION_MY_USER_MAP_KEY}" datatype="KVSQLCACHE" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">销售部门</label>
            <div class="layui-input-inline">
                <object type="search-org" class=" layui-input list-search-item " name="ORG_ID" placeholder="" style="" alias="CHANCE" jdbctype="NUMBER" data="" datatype="" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">关注人</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select-share" name="FOCUS_USER_ID" placeholder="" style="" radio="true"  jdbctype="BODY" data="${session.EZ_SESSION_MY_USER_MAP_KEY}" datatype="KVSQLCACHE" oper="IN"></object>
            </div>
        </div>



    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
        <!--<button item_name="修改拜访人" name="修改拜访人" type="table">修改拜访人</button>-->
        <!--<button item_name="批量创建拜访计划" name="batchCreate" type="table" >批量创建拜访计划</button>-->
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th item_name="BUSSINESS_CHANCE_NO" name="BUSSINESS_CHANCE_NO"body="td-linktext" url="/businessChance/details.do?id=${BUSSINESS_CHANCE_ID}"   opentype="PARENT" windowname="商机详情" datatype="" data="" style="" head="18">商机编号</th>
            <th item_name="BUSSINESS_ADD_TIME" name="BUSSINESS_ADD_TIME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 80px;" head="18">商机创建时间</th>
            <th item_name="BUSINESS_CHANCE_STATUS" name="BUSINESS_CHANCE_STATUS" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 60px;" head="18" >商机状态</th>
            <th item_name="TRADER_NAME " name="TRADER_NAME " body="td-linktext"     datatype="" data="" style="min-width:80px;width: 140px;max-width: 300px;
word-break: break-all;" head="18">客户名称</th>
            <th item_name="BELONG_USER_NAME" name="BELONG_USER_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 60px;" head="18" >归属销售</th>
            <th item_name="ORG_NAME" name="ORG_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 60px;" head="18" >销售部门</th>
            <th item_name="PRODUCT_COMMENTS_SALE" name="PRODUCT_COMMENTS_SALE" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 140px;min-width: 60px;max-width: 240px;" head="18" >产品备注（销售）</th>
            <th item_name="CONTENT" name="CONTENT" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 140px;min-width: 80px;max-width: 240px;" head="18" >询价产品/产品备注（总机）</th>
            <th item_name="FOCUS_ADD_TIME" name="FOCUS_ADD_TIME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 80px;" head="18">我关注时间</th>
            <th item_name="FOCUS_USER_NAME" name="FOCUS_USER_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 60px;" head="18" >关注人</th>

            <th item_name="IS_FOCUS" name="IS_FOCUS" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 40px;max-width: 40px;" head="18">我是否支持</th>
            <th item_name="SUPPORT_ADD_TIME" name="SUPPORT_ADD_TIME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 80px;" head="18">我最近支持时间</th>
            <th item_name="SUPPORT_CONTENT" name="SUPPORT_CONTENT" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 80px;max-width: 240px;" head="18">我最近支持内容</th>
            <th item_name="AMOUNT" name="AMOUNT" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 80px;" head="18">我最近评估成单金额/元</th>
            <th item_name="BUSINESS_LEVEL" name="BUSINESS_LEVEL" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 60px;min-width: 60px;max-width: 60px;" head="18">我最近评估级别</th>
            <th item_name="ORDER_DATE" name="ORDER_DATE" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 80px;" head="18">我最近评估成交时间</th>
            <th item_name="NEXT_SUPPORT_DATE" name="NEXT_SUPPORT_DATE" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 80px;" head="18">我下次支持时间</th>

        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by CHANCE.BUSSINESS_CHANCE_ID DESC" groupby="GROUP BY CHANCE.BUSSINESS_CHANCE_ID,tcdo.CREATOR">
    StringBuilder sql=new StringBuilder("
       SELECT
	CHANCE.BUSSINESS_CHANCE_ID,
	CHANCE.BUSSINESS_CHANCE_NO,
    DATE_FORMAT(FROM_UNIXTIME(CHANCE.ADD_TIME/1000), '%Y-%m-%d %H:%i:%s') BUSSINESS_ADD_TIME,
	CASE CHANCE.STATUS
	    WHEN '0' THEN '未处理'
	    WHEN '1' THEN '报价中'
	    WHEN '2' THEN '已报价'
	    WHEN '3' THEN '已订单'
	    WHEN '4' THEN '已关闭'
	    WHEN '5' THEN '未分配'
	    WHEN '6' THEN '处理中'
	    WHEN '7' THEN '已成单'
	    ELSE '未知状态'
	END AS BUSINESS_CHANCE_STATUS,
	CHANCE.TRADER_NAME TRADER_NAME,
	tu.USERNAME    BELONG_USER_NAME,
	to2.ORG_NAME   ORG_NAME,
	CHANCE.PRODUCT_COMMENTS_SALE PRODUCT_COMMENTS_SALE,
	CASE WHEN LENGTH(CHANCE.PRODUCT_COMMENTS)>0
	THEN CHANCE.PRODUCT_COMMENTS
	ELSE CHANCE.CONTENT END AS CONTENT,
	DATE_FORMAT(tcdo.ADD_TIME, '%Y-%m-%d %H:%i:%s')  FOCUS_ADD_TIME,
    tu2.USERNAME AS FOCUS_USER_NAME,
	CASE WHEN record.BUSINESS_CHANCE_ID IS NOT NULL
	THEN '是'
	ELSE  '否' END AS IS_FOCUS,
	DATE_FORMAT(record.ADD_TIME, '%Y-%m-%d %H:%i:%s') SUPPORT_ADD_TIME,
	record.CONTENT SUPPORT_CONTENT,
	record.AMOUNT,
	record.BUSINESS_LEVEL,
	DATE_FORMAT(record.ORDER_DATE, '%Y-%m-%d') ORDER_DATE,
	DATE_FORMAT(record.NEXT_SUPPORT_DATE, '%Y-%m-%d') NEXT_SUPPORT_DATE
FROM T_BUSSINESS_CHANCE  CHANCE
LEFT JOIN T_CUSTOM_DATA_OPER tcdo ON CHANCE.BUSSINESS_CHANCE_ID = tcdo.RELATED_ID  AND tcdo.BIZ_TYPE=2 and tcdo.OPER_TYPE=2 AND tcdo.CREATOR IN ( ");

sql.append($$("EZ_SESSION_MY_USER_KEY"));
sql.append(" )
LEFT JOIN     (        SELECT CREATOR,BUSINESS_CHANCE_ID, MAX(ADD_TIME) AS MAX_ADD_TIME        FROM T_BUSINESS_CHANCE_SUPPORT_RECORD        WHERE CREATOR IN ( ");

sql.append($$("EZ_SESSION_MY_USER_KEY"));
sql.append(" )
GROUP BY CREATOR,BUSINESS_CHANCE_ID
    ) max_record ON CHANCE.BUSSINESS_CHANCE_ID = max_record.BUSINESS_CHANCE_ID and max_record.CREATOR=tcdo.CREATOR
LEFT JOIN
    T_BUSINESS_CHANCE_SUPPORT_RECORD record ON CHANCE.BUSSINESS_CHANCE_ID = record.BUSINESS_CHANCE_ID AND record.ADD_TIME = max_record.MAX_ADD_TIME
LEFT JOIN T_USER tu ON CHANCE.USER_ID =tu.USER_ID

LEFT JOIN T_USER tu2 ON tcdo.CREATOR =tu2.USER_ID
LEFT JOIN T_ORGANIZATION to2  ON CHANCE.ORG_ID =to2 .ORG_ID
WHERE MERGE_STATUS IN (0,2)");

if ( isNotBlank("EZ_SESSION_USER_ID_KEY","session")) {
    sql.append ( " and (tcdo.BELONGER_ID IN ( " );
    sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
    sql.append ( ") or record.CREATOR IN ( " );
    sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
     sql.append ( "))");
}
productComments = $("PRODUCT_COMMENTS");
if ( isNotBlank("PRODUCT_COMMENTS","request") ) {
     sql.append (" AND ( CHANCE.PRODUCT_COMMENTS LIKE CONCAT('%', '").append(productComments).append("', '%')");
     sql.append ("or  CHANCE.PRODUCT_COMMENTS_SALE LIKE CONCAT('%', '").append(productComments).append("', '%')");
     sql.append ("or  CHANCE.CONTENT LIKE CONCAT('%', '").append(productComments).append("', '%'))");
}
isSupport = $("IS_SUPPORT");
if ( isNotBlank("IS_SUPPORT","request") && isSupport.toString().equals("1") ) {
     sql.append (" and record.BUSINESS_CHANCE_ID is not null");
}
if ( isNotBlank("IS_SUPPORT","request") && isSupport.toString().equals("0") ) {
     sql.append (" and record.BUSINESS_CHANCE_ID is   null");
}
focusUserId = $("FOCUS_USER_ID");
if ( isNotBlank("FOCUS_USER_ID","request") && !focusUserId.toString().equals("") ) {
     sql.append (" and tcdo.CREATOR =");
     sql.append(focusUserId);
}
list=search(sql);


return list;</pre> count:
                <pre id="count" class="layui-code">

                </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">


    <script>
        function getCheckIdsUrl() {
            var goodsIdArr="-1";
            $("input[name='list-body-checkbox']:not(:disabled)").each(function () {
                if (this.checked) {
                    goodsIdArr+=','+$(this).attr("_CHECK_ID_VALUE");
                }
            })
            if(goodsIdArr=="-1"){
                return "";
            }
            return "ID=0&_CHECKD_IDS=" + encodeURI(goodsIdArr)+"&USERLIST=";
        }


        var userIdOnInit =  $("div[name='USER_ID']").attr("value");

        var sharedSale = xmSelect.render({
            el: '#itemName-USER_ID',
            name:'USER_ID',
            style: {
                minHeight: '26px',
                height: '36px'
            },
            toolbar: {
                show: false,
                showIcon: false
            },
            size: 'mini',
            filterable: true,
            remoteSearch: true,
            prop: {
                value: 'userId',
                name: 'username'
            },
            theme:{
                color: '#409eff'
            },
            data: [],
            delay: 500,
            radio: true,
            clickClose: true,
            on: function (data){
                var arr = data.arr;
                console.log(arr);
            },
            remoteMethod: function (val, cb, show) {
                if (!val) {
                    return cb([]);
                }
                $.ajax({
                    type: "POST",
                    url: "/system/user/searchHasDelete.do?type=ALL",
                    data: {'username': val},
                    dataType: 'json',
                    success: function (data) {
                        cb(data.data)
                    }
                });
            }
        });

        if(userIdOnInit.length >2){
            sharedSale.setValue([{"userId":userIdOnInit.substring(1,userIdOnInit.length-1),"username":userIdOnInit.substring(1,userIdOnInit.length-1)}]);
            $.ajax({
                type: "POST",
                url: "/system/user/searchByUserId.do",
                data: {'userId': userIdOnInit.substring(1,userIdOnInit.length-1)},
                dataType: 'json',
                success: function (data) {
                    sharedSale.setValue(data.data);
                }
            });
        }


        var focusUserIdOnInit =  $("div[name='FOCUS_USER_ID']").attr("value");

        var sharedSaleFocus = xmSelect.render({
            el: '#itemName-FOCUS_USER_ID',
            name:'FOCUS_USER_ID',
            style: {
                minHeight: '26px',
                height: '36px'
            },
            toolbar: {
                show: false,
                showIcon: false
            },
            size: 'mini',
            filterable: true,
            remoteSearch: true,
            prop: {
                value: 'userId',
                name: 'username'
            },
            theme:{
                color: '#409eff'
            },
            data: [],
            delay: 500,
            radio: true,
            clickClose: true,
            on: function (data){
                var arr = data.arr;
                console.log(arr);
            },
            remoteMethod: function (val, cb, show) {
                if (!val) {
                    return cb([]);
                }
                $.ajax({
                    type: "POST",
                    url: "/system/user/searchHasDelete.do?type=ALL",
                    data: {'username': val},
                    dataType: 'json',
                    success: function (data) {
                        cb(data.data)
                    }
                });
            }
        });

        if(focusUserIdOnInit.length >2){
            sharedSaleFocus.setValue([{"userId":focusUserIdOnInit.substring(1,focusUserIdOnInit.length-1),"username":focusUserIdOnInit.substring(1,focusUserIdOnInit.length-1)}]);
            $.ajax({
                type: "POST",
                url: "/system/user/searchByUserId.do",
                data: {'userId': focusUserIdOnInit.substring(1,focusUserIdOnInit.length-1)},
                dataType: 'json',
                success: function (data) {
                    sharedSaleFocus.setValue(data.data);
                }
            });
        }


    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>

    layui.use(function () {

    })
</script>
</body>
</html>