<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="../../common/common.jsp" %>
<script src="/webjars/ezadmin/plugins/cascader/xm-select.js" type="text/javascript" ></script>
<style>
    i {
        background: none;
    }
</style>

<%--<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">--%>
<%--<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">--%>

<div class="main-container" >
    <span style="width: 100%; margin-bottom: 20px; font-size: 14px">
        <div class="line-distance">提醒：添加豁免人员后，相关人员名下客户均不纳入公海。</div>
    </span>

    <div class="searchfunc " >
        <ul style="display: inline-block">
            <li>
                <input type="hidden" value="0" id="userId" name="userId" />
                <label class="infor_name" style="width: 110px;font-size: 13px" for="userId">请选择销售人员：</label>
                <%--<ul class="inputfloat f_left" >--%>
                    <%--<li>--%>
                        <%--<select class="wid19 selector"  name="orgId" id="orgId">--%>
                            <%--<option value="0">请选择</option>--%>
                            <%--<c:if test="${not empty orgs }">--%>
                                <%--<c:forEach items="${orgs }" var="org">--%>
                                    <%--<option value="${org.orgId }"  <c:if test="${user.orgId==org.orgId}">selected="selected"</c:if>>${org.orgName }</option>--%>
                                <%--</c:forEach>--%>
                            <%--</c:if>--%>
                        <%--</select>--%>
                    <%--</li>--%>
                    <%--<li>--%>
                        <%--<select class="wid16 selector" name="userId" id="userId">--%>
                            <%--<option value="0">请选择</option>--%>
                            <%--<c:if test="${not empty users }">--%>
                                <%--<c:forEach items="${users }" var="cy">--%>
                                    <%--<option value="${cy.userId }" <c:if test="${user.userId==cy.userId}">selected="selected"</c:if>>${cy.username }</option>--%>
                                <%--</c:forEach>--%>
                            <%--</c:if>--%>
                        <%--</select>--%>
                    <%--</li>--%>


                <%--</ul>--%>
                <div id="userIdSelect" style="width: 220px;" lay-verify="required"  ></div>
            </li>
            <li>
                &nbsp;&nbsp;&nbsp;<span class="bt-large bg-light-blue bt-bg-style " onclick="addUser()">
                                添加
                </span>
            </li>
        </ul>
    </div>

    <div class="parts content1">

        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid6" style="font-size: 14px">序号</th>
                <th style="font-size: 14px">豁免人员</th>
                <th style="font-size: 14px">添加时间</th>
                <th style="font-size: 14px">添加人</th>
                <th style="font-size: 14px">操作</th>
            </tr>
            </thead>

            <tbody>
            <c:if test="${not empty list}">
                <c:forEach items="${list}" var="item" varStatus="status">
                    <tr>
                        <td>
                                ${status.count}
                        </td>
                        <td>
                            <div class="tr-item">
                                <span class="item-value" name="">${item.username}</span>
                            </div>
                        </td>
                        <td>
                            <div class="tr-item">
                                <span class="item-value"><fmt:formatDate value="${item.addTime}" pattern="yyyy-MM-dd HH:mm:ss"/> </span>
                            </div>
                        </td>
                        <td>
                            <div class="tr-item">
                                <span class="item-value">${item.creatorUsername}</span>
                            </div>
                        </td>
                        <td>
                            <div class="tr-item">
                                                    <span class="item-value" >
        <%--                                                <c:if test="${item.skuStatus == 0}">--%>
        <%--                                                <a href="javascript:void(0);" style="cursor:pointer" onclick="updateRegionRules(${item.publicCustomerRegionRulesId})">修改</a>--%>
                                                            <a href="javascript:void(0);" style="cursor:pointer" onclick="delete0(${item.publicCustomerExemptUsersId},this)">删除</a>
        <%--                                                </c:if>--%>
                                                    </span>
                            </div>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty list}">
                <tr>
                    <td colspan="5">暂无数据</td>
                </tr>
            </c:if>
            </tbody>

        </table>
    </div>
    <div>
        <tags:page page="${page}"/>
    </div>



</div>
<script>
    var userIdSelectObj = xmSelect.render({
        el: '#userIdSelect',
        radio:true,//设置为单选
        style: {
            minHeight: '26px',
            height: '36px'
        },
        toolbar: {
            show: false,
            showIcon: false
        },
        size: 'medium',
        filterable: true,
        remoteSearch: true,
        prop: {
            value: 'userId',
            name: 'username'
        },
        theme:{
            color: '#409eff'
        },
        placeholder:'请选择',
        data: [],
        delay: 500,
        radio: true,
        clickClose: true,
        on: {
            select: function(item){
                // 这里的item参数包含了选中的项的信息
                console.log("选中了");
                console.log(item);
                $("#userId").val(item.userId);
            },
            clear: function(){
                // 这个函数会在用户清空所有选项时被调用
                console.log('All options have been cleared.');
                $("#userId").val(0);
            }
        },
        remoteMethod: function (val, cb, show) {
            if (!val) {
                return cb([]);
            }
            $.ajax({
                type: "POST",
                url: "/system/user/searchHasDelete.do?type=ALL",
                data: {'username': val},
                dataType: 'json',
                success: function (data) {
                    cb(data.data)
                }
            });
        }
    });


</script>
<%@ include file="../../common/footer.jsp" %>
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/jquery.min.js'></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/trader/public/public_customer_calculate_rules_exemption.js?rnd=${resourceVersionKey}'></script>


