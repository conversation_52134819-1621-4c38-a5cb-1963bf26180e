package com.vedeng.logistics.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WmsWarehouseGoodsOperateLogDataExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public WmsWarehouseGoodsOperateLogDataExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("ID is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("ID is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("ID =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("ID <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("ID >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ID >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("ID <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("ID <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("ID in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("ID not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("ID between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("ID not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andWarehouseGoodsOperateLogIdIsNull() {
            addCriterion("WAREHOUSE_GOODS_OPERATE_LOG_ID is null");
            return (Criteria) this;
        }

        public Criteria andWarehouseGoodsOperateLogIdIsNotNull() {
            addCriterion("WAREHOUSE_GOODS_OPERATE_LOG_ID is not null");
            return (Criteria) this;
        }

        public Criteria andWarehouseGoodsOperateLogIdEqualTo(Integer value) {
            addCriterion("WAREHOUSE_GOODS_OPERATE_LOG_ID =", value, "warehouseGoodsOperateLogId");
            return (Criteria) this;
        }

        public Criteria andWarehouseGoodsOperateLogIdNotEqualTo(Integer value) {
            addCriterion("WAREHOUSE_GOODS_OPERATE_LOG_ID <>", value, "warehouseGoodsOperateLogId");
            return (Criteria) this;
        }

        public Criteria andWarehouseGoodsOperateLogIdGreaterThan(Integer value) {
            addCriterion("WAREHOUSE_GOODS_OPERATE_LOG_ID >", value, "warehouseGoodsOperateLogId");
            return (Criteria) this;
        }

        public Criteria andWarehouseGoodsOperateLogIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("WAREHOUSE_GOODS_OPERATE_LOG_ID >=", value, "warehouseGoodsOperateLogId");
            return (Criteria) this;
        }

        public Criteria andWarehouseGoodsOperateLogIdLessThan(Integer value) {
            addCriterion("WAREHOUSE_GOODS_OPERATE_LOG_ID <", value, "warehouseGoodsOperateLogId");
            return (Criteria) this;
        }

        public Criteria andWarehouseGoodsOperateLogIdLessThanOrEqualTo(Integer value) {
            addCriterion("WAREHOUSE_GOODS_OPERATE_LOG_ID <=", value, "warehouseGoodsOperateLogId");
            return (Criteria) this;
        }

        public Criteria andWarehouseGoodsOperateLogIdIn(List<Integer> values) {
            addCriterion("WAREHOUSE_GOODS_OPERATE_LOG_ID in", values, "warehouseGoodsOperateLogId");
            return (Criteria) this;
        }

        public Criteria andWarehouseGoodsOperateLogIdNotIn(List<Integer> values) {
            addCriterion("WAREHOUSE_GOODS_OPERATE_LOG_ID not in", values, "warehouseGoodsOperateLogId");
            return (Criteria) this;
        }

        public Criteria andWarehouseGoodsOperateLogIdBetween(Integer value1, Integer value2) {
            addCriterion("WAREHOUSE_GOODS_OPERATE_LOG_ID between", value1, value2, "warehouseGoodsOperateLogId");
            return (Criteria) this;
        }

        public Criteria andWarehouseGoodsOperateLogIdNotBetween(Integer value1, Integer value2) {
            addCriterion("WAREHOUSE_GOODS_OPERATE_LOG_ID not between", value1, value2, "warehouseGoodsOperateLogId");
            return (Criteria) this;
        }

        public Criteria andOperateTypeIsNull() {
            addCriterion("OPERATE_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andOperateTypeIsNotNull() {
            addCriterion("OPERATE_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andOperateTypeEqualTo(Integer value) {
            addCriterion("OPERATE_TYPE =", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeNotEqualTo(Integer value) {
            addCriterion("OPERATE_TYPE <>", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeGreaterThan(Integer value) {
            addCriterion("OPERATE_TYPE >", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("OPERATE_TYPE >=", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeLessThan(Integer value) {
            addCriterion("OPERATE_TYPE <", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeLessThanOrEqualTo(Integer value) {
            addCriterion("OPERATE_TYPE <=", value, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeIn(List<Integer> values) {
            addCriterion("OPERATE_TYPE in", values, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeNotIn(List<Integer> values) {
            addCriterion("OPERATE_TYPE not in", values, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeBetween(Integer value1, Integer value2) {
            addCriterion("OPERATE_TYPE between", value1, value2, "operateType");
            return (Criteria) this;
        }

        public Criteria andOperateTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("OPERATE_TYPE not between", value1, value2, "operateType");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("ORDER_ID is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("ORDER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(Integer value) {
            addCriterion("ORDER_ID =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(Integer value) {
            addCriterion("ORDER_ID <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(Integer value) {
            addCriterion("ORDER_ID >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ORDER_ID >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(Integer value) {
            addCriterion("ORDER_ID <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(Integer value) {
            addCriterion("ORDER_ID <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<Integer> values) {
            addCriterion("ORDER_ID in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<Integer> values) {
            addCriterion("ORDER_ID not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(Integer value1, Integer value2) {
            addCriterion("ORDER_ID between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ORDER_ID not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andRelatedIdIsNull() {
            addCriterion("RELATED_ID is null");
            return (Criteria) this;
        }

        public Criteria andRelatedIdIsNotNull() {
            addCriterion("RELATED_ID is not null");
            return (Criteria) this;
        }

        public Criteria andRelatedIdEqualTo(Integer value) {
            addCriterion("RELATED_ID =", value, "relatedId");
            return (Criteria) this;
        }

        public Criteria andRelatedIdNotEqualTo(Integer value) {
            addCriterion("RELATED_ID <>", value, "relatedId");
            return (Criteria) this;
        }

        public Criteria andRelatedIdGreaterThan(Integer value) {
            addCriterion("RELATED_ID >", value, "relatedId");
            return (Criteria) this;
        }

        public Criteria andRelatedIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("RELATED_ID >=", value, "relatedId");
            return (Criteria) this;
        }

        public Criteria andRelatedIdLessThan(Integer value) {
            addCriterion("RELATED_ID <", value, "relatedId");
            return (Criteria) this;
        }

        public Criteria andRelatedIdLessThanOrEqualTo(Integer value) {
            addCriterion("RELATED_ID <=", value, "relatedId");
            return (Criteria) this;
        }

        public Criteria andRelatedIdIn(List<Integer> values) {
            addCriterion("RELATED_ID in", values, "relatedId");
            return (Criteria) this;
        }

        public Criteria andRelatedIdNotIn(List<Integer> values) {
            addCriterion("RELATED_ID not in", values, "relatedId");
            return (Criteria) this;
        }

        public Criteria andRelatedIdBetween(Integer value1, Integer value2) {
            addCriterion("RELATED_ID between", value1, value2, "relatedId");
            return (Criteria) this;
        }

        public Criteria andRelatedIdNotBetween(Integer value1, Integer value2) {
            addCriterion("RELATED_ID not between", value1, value2, "relatedId");
            return (Criteria) this;
        }

        public Criteria andBusinessNoIsNull() {
            addCriterion("BUSINESS_NO is null");
            return (Criteria) this;
        }

        public Criteria andBusinessNoIsNotNull() {
            addCriterion("BUSINESS_NO is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessNoEqualTo(String value) {
            addCriterion("BUSINESS_NO =", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoNotEqualTo(String value) {
            addCriterion("BUSINESS_NO <>", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoGreaterThan(String value) {
            addCriterion("BUSINESS_NO >", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoGreaterThanOrEqualTo(String value) {
            addCriterion("BUSINESS_NO >=", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoLessThan(String value) {
            addCriterion("BUSINESS_NO <", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoLessThanOrEqualTo(String value) {
            addCriterion("BUSINESS_NO <=", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoLike(String value) {
            addCriterion("BUSINESS_NO like", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoNotLike(String value) {
            addCriterion("BUSINESS_NO not like", value, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoIn(List<String> values) {
            addCriterion("BUSINESS_NO in", values, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoNotIn(List<String> values) {
            addCriterion("BUSINESS_NO not in", values, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoBetween(String value1, String value2) {
            addCriterion("BUSINESS_NO between", value1, value2, "businessNo");
            return (Criteria) this;
        }

        public Criteria andBusinessNoNotBetween(String value1, String value2) {
            addCriterion("BUSINESS_NO not between", value1, value2, "businessNo");
            return (Criteria) this;
        }

        public Criteria andTraderIdIsNull() {
            addCriterion("TRADER_ID is null");
            return (Criteria) this;
        }

        public Criteria andTraderIdIsNotNull() {
            addCriterion("TRADER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTraderIdEqualTo(Integer value) {
            addCriterion("TRADER_ID =", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdNotEqualTo(Integer value) {
            addCriterion("TRADER_ID <>", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdGreaterThan(Integer value) {
            addCriterion("TRADER_ID >", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_ID >=", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdLessThan(Integer value) {
            addCriterion("TRADER_ID <", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_ID <=", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdIn(List<Integer> values) {
            addCriterion("TRADER_ID in", values, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdNotIn(List<Integer> values) {
            addCriterion("TRADER_ID not in", values, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_ID between", value1, value2, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_ID not between", value1, value2, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderNameIsNull() {
            addCriterion("TRADER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andTraderNameIsNotNull() {
            addCriterion("TRADER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andTraderNameEqualTo(String value) {
            addCriterion("TRADER_NAME =", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotEqualTo(String value) {
            addCriterion("TRADER_NAME <>", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameGreaterThan(String value) {
            addCriterion("TRADER_NAME >", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_NAME >=", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameLessThan(String value) {
            addCriterion("TRADER_NAME <", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameLessThanOrEqualTo(String value) {
            addCriterion("TRADER_NAME <=", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameLike(String value) {
            addCriterion("TRADER_NAME like", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotLike(String value) {
            addCriterion("TRADER_NAME not like", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameIn(List<String> values) {
            addCriterion("TRADER_NAME in", values, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotIn(List<String> values) {
            addCriterion("TRADER_NAME not in", values, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameBetween(String value1, String value2) {
            addCriterion("TRADER_NAME between", value1, value2, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotBetween(String value1, String value2) {
            addCriterion("TRADER_NAME not between", value1, value2, "traderName");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNull() {
            addCriterion("USER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNotNull() {
            addCriterion("USER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andUserNameEqualTo(String value) {
            addCriterion("USER_NAME =", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotEqualTo(String value) {
            addCriterion("USER_NAME <>", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThan(String value) {
            addCriterion("USER_NAME >", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("USER_NAME >=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThan(String value) {
            addCriterion("USER_NAME <", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThanOrEqualTo(String value) {
            addCriterion("USER_NAME <=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLike(String value) {
            addCriterion("USER_NAME like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotLike(String value) {
            addCriterion("USER_NAME not like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameIn(List<String> values) {
            addCriterion("USER_NAME in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotIn(List<String> values) {
            addCriterion("USER_NAME not in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameBetween(String value1, String value2) {
            addCriterion("USER_NAME between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotBetween(String value1, String value2) {
            addCriterion("USER_NAME not between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andDeptNameIsNull() {
            addCriterion("DEPT_NAME is null");
            return (Criteria) this;
        }

        public Criteria andDeptNameIsNotNull() {
            addCriterion("DEPT_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andDeptNameEqualTo(String value) {
            addCriterion("DEPT_NAME =", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotEqualTo(String value) {
            addCriterion("DEPT_NAME <>", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameGreaterThan(String value) {
            addCriterion("DEPT_NAME >", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameGreaterThanOrEqualTo(String value) {
            addCriterion("DEPT_NAME >=", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameLessThan(String value) {
            addCriterion("DEPT_NAME <", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameLessThanOrEqualTo(String value) {
            addCriterion("DEPT_NAME <=", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameLike(String value) {
            addCriterion("DEPT_NAME like", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotLike(String value) {
            addCriterion("DEPT_NAME not like", value, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameIn(List<String> values) {
            addCriterion("DEPT_NAME in", values, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotIn(List<String> values) {
            addCriterion("DEPT_NAME not in", values, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameBetween(String value1, String value2) {
            addCriterion("DEPT_NAME between", value1, value2, "deptName");
            return (Criteria) this;
        }

        public Criteria andDeptNameNotBetween(String value1, String value2) {
            addCriterion("DEPT_NAME not between", value1, value2, "deptName");
            return (Criteria) this;
        }

        public Criteria andSkuNoIsNull() {
            addCriterion("SKU_NO is null");
            return (Criteria) this;
        }

        public Criteria andSkuNoIsNotNull() {
            addCriterion("SKU_NO is not null");
            return (Criteria) this;
        }

        public Criteria andSkuNoEqualTo(String value) {
            addCriterion("SKU_NO =", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoNotEqualTo(String value) {
            addCriterion("SKU_NO <>", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoGreaterThan(String value) {
            addCriterion("SKU_NO >", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoGreaterThanOrEqualTo(String value) {
            addCriterion("SKU_NO >=", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoLessThan(String value) {
            addCriterion("SKU_NO <", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoLessThanOrEqualTo(String value) {
            addCriterion("SKU_NO <=", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoLike(String value) {
            addCriterion("SKU_NO like", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoNotLike(String value) {
            addCriterion("SKU_NO not like", value, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoIn(List<String> values) {
            addCriterion("SKU_NO in", values, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoNotIn(List<String> values) {
            addCriterion("SKU_NO not in", values, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoBetween(String value1, String value2) {
            addCriterion("SKU_NO between", value1, value2, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNoNotBetween(String value1, String value2) {
            addCriterion("SKU_NO not between", value1, value2, "skuNo");
            return (Criteria) this;
        }

        public Criteria andSkuNameIsNull() {
            addCriterion("SKU_NAME is null");
            return (Criteria) this;
        }

        public Criteria andSkuNameIsNotNull() {
            addCriterion("SKU_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andSkuNameEqualTo(String value) {
            addCriterion("SKU_NAME =", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotEqualTo(String value) {
            addCriterion("SKU_NAME <>", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameGreaterThan(String value) {
            addCriterion("SKU_NAME >", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameGreaterThanOrEqualTo(String value) {
            addCriterion("SKU_NAME >=", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameLessThan(String value) {
            addCriterion("SKU_NAME <", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameLessThanOrEqualTo(String value) {
            addCriterion("SKU_NAME <=", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameLike(String value) {
            addCriterion("SKU_NAME like", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotLike(String value) {
            addCriterion("SKU_NAME not like", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameIn(List<String> values) {
            addCriterion("SKU_NAME in", values, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotIn(List<String> values) {
            addCriterion("SKU_NAME not in", values, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameBetween(String value1, String value2) {
            addCriterion("SKU_NAME between", value1, value2, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotBetween(String value1, String value2) {
            addCriterion("SKU_NAME not between", value1, value2, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuModelIsNull() {
            addCriterion("SKU_MODEL is null");
            return (Criteria) this;
        }

        public Criteria andSkuModelIsNotNull() {
            addCriterion("SKU_MODEL is not null");
            return (Criteria) this;
        }

        public Criteria andSkuModelEqualTo(String value) {
            addCriterion("SKU_MODEL =", value, "skuModel");
            return (Criteria) this;
        }

        public Criteria andSkuModelNotEqualTo(String value) {
            addCriterion("SKU_MODEL <>", value, "skuModel");
            return (Criteria) this;
        }

        public Criteria andSkuModelGreaterThan(String value) {
            addCriterion("SKU_MODEL >", value, "skuModel");
            return (Criteria) this;
        }

        public Criteria andSkuModelGreaterThanOrEqualTo(String value) {
            addCriterion("SKU_MODEL >=", value, "skuModel");
            return (Criteria) this;
        }

        public Criteria andSkuModelLessThan(String value) {
            addCriterion("SKU_MODEL <", value, "skuModel");
            return (Criteria) this;
        }

        public Criteria andSkuModelLessThanOrEqualTo(String value) {
            addCriterion("SKU_MODEL <=", value, "skuModel");
            return (Criteria) this;
        }

        public Criteria andSkuModelLike(String value) {
            addCriterion("SKU_MODEL like", value, "skuModel");
            return (Criteria) this;
        }

        public Criteria andSkuModelNotLike(String value) {
            addCriterion("SKU_MODEL not like", value, "skuModel");
            return (Criteria) this;
        }

        public Criteria andSkuModelIn(List<String> values) {
            addCriterion("SKU_MODEL in", values, "skuModel");
            return (Criteria) this;
        }

        public Criteria andSkuModelNotIn(List<String> values) {
            addCriterion("SKU_MODEL not in", values, "skuModel");
            return (Criteria) this;
        }

        public Criteria andSkuModelBetween(String value1, String value2) {
            addCriterion("SKU_MODEL between", value1, value2, "skuModel");
            return (Criteria) this;
        }

        public Criteria andSkuModelNotBetween(String value1, String value2) {
            addCriterion("SKU_MODEL not between", value1, value2, "skuModel");
            return (Criteria) this;
        }

        public Criteria andSkuSpecIsNull() {
            addCriterion("SKU_SPEC is null");
            return (Criteria) this;
        }

        public Criteria andSkuSpecIsNotNull() {
            addCriterion("SKU_SPEC is not null");
            return (Criteria) this;
        }

        public Criteria andSkuSpecEqualTo(String value) {
            addCriterion("SKU_SPEC =", value, "skuSpec");
            return (Criteria) this;
        }

        public Criteria andSkuSpecNotEqualTo(String value) {
            addCriterion("SKU_SPEC <>", value, "skuSpec");
            return (Criteria) this;
        }

        public Criteria andSkuSpecGreaterThan(String value) {
            addCriterion("SKU_SPEC >", value, "skuSpec");
            return (Criteria) this;
        }

        public Criteria andSkuSpecGreaterThanOrEqualTo(String value) {
            addCriterion("SKU_SPEC >=", value, "skuSpec");
            return (Criteria) this;
        }

        public Criteria andSkuSpecLessThan(String value) {
            addCriterion("SKU_SPEC <", value, "skuSpec");
            return (Criteria) this;
        }

        public Criteria andSkuSpecLessThanOrEqualTo(String value) {
            addCriterion("SKU_SPEC <=", value, "skuSpec");
            return (Criteria) this;
        }

        public Criteria andSkuSpecLike(String value) {
            addCriterion("SKU_SPEC like", value, "skuSpec");
            return (Criteria) this;
        }

        public Criteria andSkuSpecNotLike(String value) {
            addCriterion("SKU_SPEC not like", value, "skuSpec");
            return (Criteria) this;
        }

        public Criteria andSkuSpecIn(List<String> values) {
            addCriterion("SKU_SPEC in", values, "skuSpec");
            return (Criteria) this;
        }

        public Criteria andSkuSpecNotIn(List<String> values) {
            addCriterion("SKU_SPEC not in", values, "skuSpec");
            return (Criteria) this;
        }

        public Criteria andSkuSpecBetween(String value1, String value2) {
            addCriterion("SKU_SPEC between", value1, value2, "skuSpec");
            return (Criteria) this;
        }

        public Criteria andSkuSpecNotBetween(String value1, String value2) {
            addCriterion("SKU_SPEC not between", value1, value2, "skuSpec");
            return (Criteria) this;
        }

        public Criteria andSkuNumIsNull() {
            addCriterion("SKU_NUM is null");
            return (Criteria) this;
        }

        public Criteria andSkuNumIsNotNull() {
            addCriterion("SKU_NUM is not null");
            return (Criteria) this;
        }

        public Criteria andSkuNumEqualTo(Integer value) {
            addCriterion("SKU_NUM =", value, "skuNum");
            return (Criteria) this;
        }

        public Criteria andSkuNumNotEqualTo(Integer value) {
            addCriterion("SKU_NUM <>", value, "skuNum");
            return (Criteria) this;
        }

        public Criteria andSkuNumGreaterThan(Integer value) {
            addCriterion("SKU_NUM >", value, "skuNum");
            return (Criteria) this;
        }

        public Criteria andSkuNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("SKU_NUM >=", value, "skuNum");
            return (Criteria) this;
        }

        public Criteria andSkuNumLessThan(Integer value) {
            addCriterion("SKU_NUM <", value, "skuNum");
            return (Criteria) this;
        }

        public Criteria andSkuNumLessThanOrEqualTo(Integer value) {
            addCriterion("SKU_NUM <=", value, "skuNum");
            return (Criteria) this;
        }

        public Criteria andSkuNumIn(List<Integer> values) {
            addCriterion("SKU_NUM in", values, "skuNum");
            return (Criteria) this;
        }

        public Criteria andSkuNumNotIn(List<Integer> values) {
            addCriterion("SKU_NUM not in", values, "skuNum");
            return (Criteria) this;
        }

        public Criteria andSkuNumBetween(Integer value1, Integer value2) {
            addCriterion("SKU_NUM between", value1, value2, "skuNum");
            return (Criteria) this;
        }

        public Criteria andSkuNumNotBetween(Integer value1, Integer value2) {
            addCriterion("SKU_NUM not between", value1, value2, "skuNum");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberIsNull() {
            addCriterion("REGISTER_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberIsNotNull() {
            addCriterion("REGISTER_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberEqualTo(String value) {
            addCriterion("REGISTER_NUMBER =", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberNotEqualTo(String value) {
            addCriterion("REGISTER_NUMBER <>", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberGreaterThan(String value) {
            addCriterion("REGISTER_NUMBER >", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberGreaterThanOrEqualTo(String value) {
            addCriterion("REGISTER_NUMBER >=", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberLessThan(String value) {
            addCriterion("REGISTER_NUMBER <", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberLessThanOrEqualTo(String value) {
            addCriterion("REGISTER_NUMBER <=", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberLike(String value) {
            addCriterion("REGISTER_NUMBER like", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberNotLike(String value) {
            addCriterion("REGISTER_NUMBER not like", value, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberIn(List<String> values) {
            addCriterion("REGISTER_NUMBER in", values, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberNotIn(List<String> values) {
            addCriterion("REGISTER_NUMBER not in", values, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberBetween(String value1, String value2) {
            addCriterion("REGISTER_NUMBER between", value1, value2, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andRegisterNumberNotBetween(String value1, String value2) {
            addCriterion("REGISTER_NUMBER not between", value1, value2, "registerNumber");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNull() {
            addCriterion("BRAND_NAME is null");
            return (Criteria) this;
        }

        public Criteria andBrandNameIsNotNull() {
            addCriterion("BRAND_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andBrandNameEqualTo(String value) {
            addCriterion("BRAND_NAME =", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotEqualTo(String value) {
            addCriterion("BRAND_NAME <>", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThan(String value) {
            addCriterion("BRAND_NAME >", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameGreaterThanOrEqualTo(String value) {
            addCriterion("BRAND_NAME >=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThan(String value) {
            addCriterion("BRAND_NAME <", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLessThanOrEqualTo(String value) {
            addCriterion("BRAND_NAME <=", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameLike(String value) {
            addCriterion("BRAND_NAME like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotLike(String value) {
            addCriterion("BRAND_NAME not like", value, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameIn(List<String> values) {
            addCriterion("BRAND_NAME in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotIn(List<String> values) {
            addCriterion("BRAND_NAME not in", values, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameBetween(String value1, String value2) {
            addCriterion("BRAND_NAME between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andBrandNameNotBetween(String value1, String value2) {
            addCriterion("BRAND_NAME not between", value1, value2, "brandName");
            return (Criteria) this;
        }

        public Criteria andBarcodeFactoryIsNull() {
            addCriterion("BARCODE_FACTORY is null");
            return (Criteria) this;
        }

        public Criteria andBarcodeFactoryIsNotNull() {
            addCriterion("BARCODE_FACTORY is not null");
            return (Criteria) this;
        }

        public Criteria andBarcodeFactoryEqualTo(String value) {
            addCriterion("BARCODE_FACTORY =", value, "barcodeFactory");
            return (Criteria) this;
        }

        public Criteria andBarcodeFactoryNotEqualTo(String value) {
            addCriterion("BARCODE_FACTORY <>", value, "barcodeFactory");
            return (Criteria) this;
        }

        public Criteria andBarcodeFactoryGreaterThan(String value) {
            addCriterion("BARCODE_FACTORY >", value, "barcodeFactory");
            return (Criteria) this;
        }

        public Criteria andBarcodeFactoryGreaterThanOrEqualTo(String value) {
            addCriterion("BARCODE_FACTORY >=", value, "barcodeFactory");
            return (Criteria) this;
        }

        public Criteria andBarcodeFactoryLessThan(String value) {
            addCriterion("BARCODE_FACTORY <", value, "barcodeFactory");
            return (Criteria) this;
        }

        public Criteria andBarcodeFactoryLessThanOrEqualTo(String value) {
            addCriterion("BARCODE_FACTORY <=", value, "barcodeFactory");
            return (Criteria) this;
        }

        public Criteria andBarcodeFactoryLike(String value) {
            addCriterion("BARCODE_FACTORY like", value, "barcodeFactory");
            return (Criteria) this;
        }

        public Criteria andBarcodeFactoryNotLike(String value) {
            addCriterion("BARCODE_FACTORY not like", value, "barcodeFactory");
            return (Criteria) this;
        }

        public Criteria andBarcodeFactoryIn(List<String> values) {
            addCriterion("BARCODE_FACTORY in", values, "barcodeFactory");
            return (Criteria) this;
        }

        public Criteria andBarcodeFactoryNotIn(List<String> values) {
            addCriterion("BARCODE_FACTORY not in", values, "barcodeFactory");
            return (Criteria) this;
        }

        public Criteria andBarcodeFactoryBetween(String value1, String value2) {
            addCriterion("BARCODE_FACTORY between", value1, value2, "barcodeFactory");
            return (Criteria) this;
        }

        public Criteria andBarcodeFactoryNotBetween(String value1, String value2) {
            addCriterion("BARCODE_FACTORY not between", value1, value2, "barcodeFactory");
            return (Criteria) this;
        }

        public Criteria andBatchNumberIsNull() {
            addCriterion("BATCH_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andBatchNumberIsNotNull() {
            addCriterion("BATCH_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andBatchNumberEqualTo(String value) {
            addCriterion("BATCH_NUMBER =", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberNotEqualTo(String value) {
            addCriterion("BATCH_NUMBER <>", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberGreaterThan(String value) {
            addCriterion("BATCH_NUMBER >", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberGreaterThanOrEqualTo(String value) {
            addCriterion("BATCH_NUMBER >=", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberLessThan(String value) {
            addCriterion("BATCH_NUMBER <", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberLessThanOrEqualTo(String value) {
            addCriterion("BATCH_NUMBER <=", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberLike(String value) {
            addCriterion("BATCH_NUMBER like", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberNotLike(String value) {
            addCriterion("BATCH_NUMBER not like", value, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberIn(List<String> values) {
            addCriterion("BATCH_NUMBER in", values, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberNotIn(List<String> values) {
            addCriterion("BATCH_NUMBER not in", values, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberBetween(String value1, String value2) {
            addCriterion("BATCH_NUMBER between", value1, value2, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andBatchNumberNotBetween(String value1, String value2) {
            addCriterion("BATCH_NUMBER not between", value1, value2, "batchNumber");
            return (Criteria) this;
        }

        public Criteria andVedengBatchNumerIsNull() {
            addCriterion("VEDENG_BATCH_NUMER is null");
            return (Criteria) this;
        }

        public Criteria andVedengBatchNumerIsNotNull() {
            addCriterion("VEDENG_BATCH_NUMER is not null");
            return (Criteria) this;
        }

        public Criteria andVedengBatchNumerEqualTo(String value) {
            addCriterion("VEDENG_BATCH_NUMER =", value, "vedengBatchNumer");
            return (Criteria) this;
        }

        public Criteria andVedengBatchNumerNotEqualTo(String value) {
            addCriterion("VEDENG_BATCH_NUMER <>", value, "vedengBatchNumer");
            return (Criteria) this;
        }

        public Criteria andVedengBatchNumerGreaterThan(String value) {
            addCriterion("VEDENG_BATCH_NUMER >", value, "vedengBatchNumer");
            return (Criteria) this;
        }

        public Criteria andVedengBatchNumerGreaterThanOrEqualTo(String value) {
            addCriterion("VEDENG_BATCH_NUMER >=", value, "vedengBatchNumer");
            return (Criteria) this;
        }

        public Criteria andVedengBatchNumerLessThan(String value) {
            addCriterion("VEDENG_BATCH_NUMER <", value, "vedengBatchNumer");
            return (Criteria) this;
        }

        public Criteria andVedengBatchNumerLessThanOrEqualTo(String value) {
            addCriterion("VEDENG_BATCH_NUMER <=", value, "vedengBatchNumer");
            return (Criteria) this;
        }

        public Criteria andVedengBatchNumerLike(String value) {
            addCriterion("VEDENG_BATCH_NUMER like", value, "vedengBatchNumer");
            return (Criteria) this;
        }

        public Criteria andVedengBatchNumerNotLike(String value) {
            addCriterion("VEDENG_BATCH_NUMER not like", value, "vedengBatchNumer");
            return (Criteria) this;
        }

        public Criteria andVedengBatchNumerIn(List<String> values) {
            addCriterion("VEDENG_BATCH_NUMER in", values, "vedengBatchNumer");
            return (Criteria) this;
        }

        public Criteria andVedengBatchNumerNotIn(List<String> values) {
            addCriterion("VEDENG_BATCH_NUMER not in", values, "vedengBatchNumer");
            return (Criteria) this;
        }

        public Criteria andVedengBatchNumerBetween(String value1, String value2) {
            addCriterion("VEDENG_BATCH_NUMER between", value1, value2, "vedengBatchNumer");
            return (Criteria) this;
        }

        public Criteria andVedengBatchNumerNotBetween(String value1, String value2) {
            addCriterion("VEDENG_BATCH_NUMER not between", value1, value2, "vedengBatchNumer");
            return (Criteria) this;
        }

        public Criteria andProductDateIsNull() {
            addCriterion("PRODUCT_DATE is null");
            return (Criteria) this;
        }

        public Criteria andProductDateIsNotNull() {
            addCriterion("PRODUCT_DATE is not null");
            return (Criteria) this;
        }

        public Criteria andProductDateEqualTo(Date value) {
            addCriterion("PRODUCT_DATE =", value, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateNotEqualTo(Date value) {
            addCriterion("PRODUCT_DATE <>", value, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateGreaterThan(Date value) {
            addCriterion("PRODUCT_DATE >", value, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateGreaterThanOrEqualTo(Date value) {
            addCriterion("PRODUCT_DATE >=", value, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateLessThan(Date value) {
            addCriterion("PRODUCT_DATE <", value, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateLessThanOrEqualTo(Date value) {
            addCriterion("PRODUCT_DATE <=", value, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateIn(List<Date> values) {
            addCriterion("PRODUCT_DATE in", values, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateNotIn(List<Date> values) {
            addCriterion("PRODUCT_DATE not in", values, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateBetween(Date value1, Date value2) {
            addCriterion("PRODUCT_DATE between", value1, value2, "productDate");
            return (Criteria) this;
        }

        public Criteria andProductDateNotBetween(Date value1, Date value2) {
            addCriterion("PRODUCT_DATE not between", value1, value2, "productDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateIsNull() {
            addCriterion("EXPIRATION_DATE is null");
            return (Criteria) this;
        }

        public Criteria andExpirationDateIsNotNull() {
            addCriterion("EXPIRATION_DATE is not null");
            return (Criteria) this;
        }

        public Criteria andExpirationDateEqualTo(Date value) {
            addCriterion("EXPIRATION_DATE =", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateNotEqualTo(Date value) {
            addCriterion("EXPIRATION_DATE <>", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateGreaterThan(Date value) {
            addCriterion("EXPIRATION_DATE >", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateGreaterThanOrEqualTo(Date value) {
            addCriterion("EXPIRATION_DATE >=", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateLessThan(Date value) {
            addCriterion("EXPIRATION_DATE <", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateLessThanOrEqualTo(Date value) {
            addCriterion("EXPIRATION_DATE <=", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateIn(List<Date> values) {
            addCriterion("EXPIRATION_DATE in", values, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateNotIn(List<Date> values) {
            addCriterion("EXPIRATION_DATE not in", values, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateBetween(Date value1, Date value2) {
            addCriterion("EXPIRATION_DATE between", value1, value2, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateNotBetween(Date value1, Date value2) {
            addCriterion("EXPIRATION_DATE not between", value1, value2, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpressNosIsNull() {
            addCriterion("EXPRESS_NOS is null");
            return (Criteria) this;
        }

        public Criteria andExpressNosIsNotNull() {
            addCriterion("EXPRESS_NOS is not null");
            return (Criteria) this;
        }

        public Criteria andExpressNosEqualTo(String value) {
            addCriterion("EXPRESS_NOS =", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosNotEqualTo(String value) {
            addCriterion("EXPRESS_NOS <>", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosGreaterThan(String value) {
            addCriterion("EXPRESS_NOS >", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosGreaterThanOrEqualTo(String value) {
            addCriterion("EXPRESS_NOS >=", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosLessThan(String value) {
            addCriterion("EXPRESS_NOS <", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosLessThanOrEqualTo(String value) {
            addCriterion("EXPRESS_NOS <=", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosLike(String value) {
            addCriterion("EXPRESS_NOS like", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosNotLike(String value) {
            addCriterion("EXPRESS_NOS not like", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosIn(List<String> values) {
            addCriterion("EXPRESS_NOS in", values, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosNotIn(List<String> values) {
            addCriterion("EXPRESS_NOS not in", values, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosBetween(String value1, String value2) {
            addCriterion("EXPRESS_NOS between", value1, value2, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosNotBetween(String value1, String value2) {
            addCriterion("EXPRESS_NOS not between", value1, value2, "expressNos");
            return (Criteria) this;
        }

        public Criteria andQualityReportIsNull() {
            addCriterion("QUALITY_REPORT is null");
            return (Criteria) this;
        }

        public Criteria andQualityReportIsNotNull() {
            addCriterion("QUALITY_REPORT is not null");
            return (Criteria) this;
        }

        public Criteria andQualityReportEqualTo(String value) {
            addCriterion("QUALITY_REPORT =", value, "qualityReport");
            return (Criteria) this;
        }

        public Criteria andQualityReportNotEqualTo(String value) {
            addCriterion("QUALITY_REPORT <>", value, "qualityReport");
            return (Criteria) this;
        }

        public Criteria andQualityReportGreaterThan(String value) {
            addCriterion("QUALITY_REPORT >", value, "qualityReport");
            return (Criteria) this;
        }

        public Criteria andQualityReportGreaterThanOrEqualTo(String value) {
            addCriterion("QUALITY_REPORT >=", value, "qualityReport");
            return (Criteria) this;
        }

        public Criteria andQualityReportLessThan(String value) {
            addCriterion("QUALITY_REPORT <", value, "qualityReport");
            return (Criteria) this;
        }

        public Criteria andQualityReportLessThanOrEqualTo(String value) {
            addCriterion("QUALITY_REPORT <=", value, "qualityReport");
            return (Criteria) this;
        }

        public Criteria andQualityReportLike(String value) {
            addCriterion("QUALITY_REPORT like", value, "qualityReport");
            return (Criteria) this;
        }

        public Criteria andQualityReportNotLike(String value) {
            addCriterion("QUALITY_REPORT not like", value, "qualityReport");
            return (Criteria) this;
        }

        public Criteria andQualityReportIn(List<String> values) {
            addCriterion("QUALITY_REPORT in", values, "qualityReport");
            return (Criteria) this;
        }

        public Criteria andQualityReportNotIn(List<String> values) {
            addCriterion("QUALITY_REPORT not in", values, "qualityReport");
            return (Criteria) this;
        }

        public Criteria andQualityReportBetween(String value1, String value2) {
            addCriterion("QUALITY_REPORT between", value1, value2, "qualityReport");
            return (Criteria) this;
        }

        public Criteria andQualityReportNotBetween(String value1, String value2) {
            addCriterion("QUALITY_REPORT not between", value1, value2, "qualityReport");
            return (Criteria) this;
        }

        public Criteria andQualityReportOssIsNull() {
            addCriterion("QUALITY_REPORT_OSS is null");
            return (Criteria) this;
        }

        public Criteria andQualityReportOssIsNotNull() {
            addCriterion("QUALITY_REPORT_OSS is not null");
            return (Criteria) this;
        }

        public Criteria andQualityReportOssEqualTo(String value) {
            addCriterion("QUALITY_REPORT_OSS =", value, "qualityReportOss");
            return (Criteria) this;
        }

        public Criteria andQualityReportOssNotEqualTo(String value) {
            addCriterion("QUALITY_REPORT_OSS <>", value, "qualityReportOss");
            return (Criteria) this;
        }

        public Criteria andQualityReportOssGreaterThan(String value) {
            addCriterion("QUALITY_REPORT_OSS >", value, "qualityReportOss");
            return (Criteria) this;
        }

        public Criteria andQualityReportOssGreaterThanOrEqualTo(String value) {
            addCriterion("QUALITY_REPORT_OSS >=", value, "qualityReportOss");
            return (Criteria) this;
        }

        public Criteria andQualityReportOssLessThan(String value) {
            addCriterion("QUALITY_REPORT_OSS <", value, "qualityReportOss");
            return (Criteria) this;
        }

        public Criteria andQualityReportOssLessThanOrEqualTo(String value) {
            addCriterion("QUALITY_REPORT_OSS <=", value, "qualityReportOss");
            return (Criteria) this;
        }

        public Criteria andQualityReportOssLike(String value) {
            addCriterion("QUALITY_REPORT_OSS like", value, "qualityReportOss");
            return (Criteria) this;
        }

        public Criteria andQualityReportOssNotLike(String value) {
            addCriterion("QUALITY_REPORT_OSS not like", value, "qualityReportOss");
            return (Criteria) this;
        }

        public Criteria andQualityReportOssIn(List<String> values) {
            addCriterion("QUALITY_REPORT_OSS in", values, "qualityReportOss");
            return (Criteria) this;
        }

        public Criteria andQualityReportOssNotIn(List<String> values) {
            addCriterion("QUALITY_REPORT_OSS not in", values, "qualityReportOss");
            return (Criteria) this;
        }

        public Criteria andQualityReportOssBetween(String value1, String value2) {
            addCriterion("QUALITY_REPORT_OSS between", value1, value2, "qualityReportOss");
            return (Criteria) this;
        }

        public Criteria andQualityReportOssNotBetween(String value1, String value2) {
            addCriterion("QUALITY_REPORT_OSS not between", value1, value2, "qualityReportOss");
            return (Criteria) this;
        }

        public Criteria andQualityReportSizeIsNull() {
            addCriterion("QUALITY_REPORT_SIZE is null");
            return (Criteria) this;
        }

        public Criteria andQualityReportSizeIsNotNull() {
            addCriterion("QUALITY_REPORT_SIZE is not null");
            return (Criteria) this;
        }

        public Criteria andQualityReportSizeEqualTo(Integer value) {
            addCriterion("QUALITY_REPORT_SIZE =", value, "qualityReportSize");
            return (Criteria) this;
        }

        public Criteria andQualityReportSizeNotEqualTo(Integer value) {
            addCriterion("QUALITY_REPORT_SIZE <>", value, "qualityReportSize");
            return (Criteria) this;
        }

        public Criteria andQualityReportSizeGreaterThan(Integer value) {
            addCriterion("QUALITY_REPORT_SIZE >", value, "qualityReportSize");
            return (Criteria) this;
        }

        public Criteria andQualityReportSizeGreaterThanOrEqualTo(Integer value) {
            addCriterion("QUALITY_REPORT_SIZE >=", value, "qualityReportSize");
            return (Criteria) this;
        }

        public Criteria andQualityReportSizeLessThan(Integer value) {
            addCriterion("QUALITY_REPORT_SIZE <", value, "qualityReportSize");
            return (Criteria) this;
        }

        public Criteria andQualityReportSizeLessThanOrEqualTo(Integer value) {
            addCriterion("QUALITY_REPORT_SIZE <=", value, "qualityReportSize");
            return (Criteria) this;
        }

        public Criteria andQualityReportSizeIn(List<Integer> values) {
            addCriterion("QUALITY_REPORT_SIZE in", values, "qualityReportSize");
            return (Criteria) this;
        }

        public Criteria andQualityReportSizeNotIn(List<Integer> values) {
            addCriterion("QUALITY_REPORT_SIZE not in", values, "qualityReportSize");
            return (Criteria) this;
        }

        public Criteria andQualityReportSizeBetween(Integer value1, Integer value2) {
            addCriterion("QUALITY_REPORT_SIZE between", value1, value2, "qualityReportSize");
            return (Criteria) this;
        }

        public Criteria andQualityReportSizeNotBetween(Integer value1, Integer value2) {
            addCriterion("QUALITY_REPORT_SIZE not between", value1, value2, "qualityReportSize");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeIsNull() {
            addCriterion("MODE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModeTimeIsNotNull() {
            addCriterion("MODE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModeTimeEqualTo(Date value) {
            addCriterion("MODE_TIME =", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeNotEqualTo(Date value) {
            addCriterion("MODE_TIME <>", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeGreaterThan(Date value) {
            addCriterion("MODE_TIME >", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("MODE_TIME >=", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeLessThan(Date value) {
            addCriterion("MODE_TIME <", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeLessThanOrEqualTo(Date value) {
            addCriterion("MODE_TIME <=", value, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeIn(List<Date> values) {
            addCriterion("MODE_TIME in", values, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeNotIn(List<Date> values) {
            addCriterion("MODE_TIME not in", values, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeBetween(Date value1, Date value2) {
            addCriterion("MODE_TIME between", value1, value2, "modeTime");
            return (Criteria) this;
        }

        public Criteria andModeTimeNotBetween(Date value1, Date value2) {
            addCriterion("MODE_TIME not between", value1, value2, "modeTime");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("IS_DELETE is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("IS_DELETE is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Byte value) {
            addCriterion("IS_DELETE =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Byte value) {
            addCriterion("IS_DELETE <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Byte value) {
            addCriterion("IS_DELETE >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_DELETE >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Byte value) {
            addCriterion("IS_DELETE <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Byte value) {
            addCriterion("IS_DELETE <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Byte> values) {
            addCriterion("IS_DELETE in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Byte> values) {
            addCriterion("IS_DELETE not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Byte value1, Byte value2) {
            addCriterion("IS_DELETE between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_DELETE not between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andOutTimeIsNull() {
            addCriterion("OUT_TIME is null");
            return (Criteria) this;
        }

        public Criteria andOutTimeIsNotNull() {
            addCriterion("OUT_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andOutTimeEqualTo(Date value) {
            addCriterion("OUT_TIME =", value, "outTime");
            return (Criteria) this;
        }

        public Criteria andOutTimeNotEqualTo(Date value) {
            addCriterion("OUT_TIME <>", value, "outTime");
            return (Criteria) this;
        }

        public Criteria andOutTimeGreaterThan(Date value) {
            addCriterion("OUT_TIME >", value, "outTime");
            return (Criteria) this;
        }

        public Criteria andOutTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("OUT_TIME >=", value, "outTime");
            return (Criteria) this;
        }

        public Criteria andOutTimeLessThan(Date value) {
            addCriterion("OUT_TIME <", value, "outTime");
            return (Criteria) this;
        }

        public Criteria andOutTimeLessThanOrEqualTo(Date value) {
            addCriterion("OUT_TIME <=", value, "outTime");
            return (Criteria) this;
        }

        public Criteria andOutTimeIn(List<Date> values) {
            addCriterion("OUT_TIME in", values, "outTime");
            return (Criteria) this;
        }

        public Criteria andOutTimeNotIn(List<Date> values) {
            addCriterion("OUT_TIME not in", values, "outTime");
            return (Criteria) this;
        }

        public Criteria andOutTimeBetween(Date value1, Date value2) {
            addCriterion("OUT_TIME between", value1, value2, "outTime");
            return (Criteria) this;
        }

        public Criteria andOutTimeNotBetween(Date value1, Date value2) {
            addCriterion("OUT_TIME not between", value1, value2, "outTime");
            return (Criteria) this;
        }

        public Criteria andSterilzationBatchNumberIsNull() {
            addCriterion("STERILZATION_BATCH_NUMBER is null");
            return (Criteria) this;
        }

        public Criteria andSterilzationBatchNumberIsNotNull() {
            addCriterion("STERILZATION_BATCH_NUMBER is not null");
            return (Criteria) this;
        }

        public Criteria andSterilzationBatchNumberEqualTo(String value) {
            addCriterion("STERILZATION_BATCH_NUMBER =", value, "sterilzationBatchNumber");
            return (Criteria) this;
        }

        public Criteria andSterilzationBatchNumberNotEqualTo(String value) {
            addCriterion("STERILZATION_BATCH_NUMBER <>", value, "sterilzationBatchNumber");
            return (Criteria) this;
        }

        public Criteria andSterilzationBatchNumberGreaterThan(String value) {
            addCriterion("STERILZATION_BATCH_NUMBER >", value, "sterilzationBatchNumber");
            return (Criteria) this;
        }

        public Criteria andSterilzationBatchNumberGreaterThanOrEqualTo(String value) {
            addCriterion("STERILZATION_BATCH_NUMBER >=", value, "sterilzationBatchNumber");
            return (Criteria) this;
        }

        public Criteria andSterilzationBatchNumberLessThan(String value) {
            addCriterion("STERILZATION_BATCH_NUMBER <", value, "sterilzationBatchNumber");
            return (Criteria) this;
        }

        public Criteria andSterilzationBatchNumberLessThanOrEqualTo(String value) {
            addCriterion("STERILZATION_BATCH_NUMBER <=", value, "sterilzationBatchNumber");
            return (Criteria) this;
        }

        public Criteria andSterilzationBatchNumberLike(String value) {
            addCriterion("STERILZATION_BATCH_NUMBER like", value, "sterilzationBatchNumber");
            return (Criteria) this;
        }

        public Criteria andSterilzationBatchNumberNotLike(String value) {
            addCriterion("STERILZATION_BATCH_NUMBER not like", value, "sterilzationBatchNumber");
            return (Criteria) this;
        }

        public Criteria andSterilzationBatchNumberIn(List<String> values) {
            addCriterion("STERILZATION_BATCH_NUMBER in", values, "sterilzationBatchNumber");
            return (Criteria) this;
        }

        public Criteria andSterilzationBatchNumberNotIn(List<String> values) {
            addCriterion("STERILZATION_BATCH_NUMBER not in", values, "sterilzationBatchNumber");
            return (Criteria) this;
        }

        public Criteria andSterilzationBatchNumberBetween(String value1, String value2) {
            addCriterion("STERILZATION_BATCH_NUMBER between", value1, value2, "sterilzationBatchNumber");
            return (Criteria) this;
        }

        public Criteria andSterilzationBatchNumberNotBetween(String value1, String value2) {
            addCriterion("STERILZATION_BATCH_NUMBER not between", value1, value2, "sterilzationBatchNumber");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated do_not_delete_during_merge Fri Feb 21 14:37:02 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}