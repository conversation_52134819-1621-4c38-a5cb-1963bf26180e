package com.vedeng.erp.saleorder.service.impl;


import com.vedeng.erp.saleorder.service.SaleorderCouponService;
import com.vedeng.order.dao.SaleorderCouponMapper;
import com.vedeng.order.model.SaleorderCoupon;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class SaleorderCouponServiceImpl implements SaleorderCouponService {
    @Resource
    private SaleorderCouponMapper saleorderCouponMapper;
    @Override
    public SaleorderCoupon selectBySaleorderId(Integer saleorderId) {
        return saleorderCouponMapper.selectBySaleorderId(saleorderId);
    }
}
