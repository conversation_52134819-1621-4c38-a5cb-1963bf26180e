package com.wms.dao;

import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WMSScrappedOutOrder;
import com.wms.model.po.WmsOutputOrder;

import java.util.List;
import java.util.Map;

public interface WmsScrappedOutMapper {

    List<WMSScrappedOutOrder> queryScrapedOutlistPage(Map<String, Object> map);

    List<WmsOutputOrderGoodsDto> queryOutputGoodsByScrapedOutId(Long scrapedOutOrderId);

    WmsOutputOrder getScrappedOrderByNo(String scrappedNo);

    WMSScrappedOutOrder selectById(Long scrappedOutId);
}
