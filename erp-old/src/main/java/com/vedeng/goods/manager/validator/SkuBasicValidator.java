package com.vedeng.goods.manager.validator;

import com.baidu.unbiz.fluentvalidator.FluentValidator;
import com.baidu.unbiz.fluentvalidator.Result;
import com.baidu.unbiz.fluentvalidator.ResultCollectors;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.validator.*;
import com.vedeng.goods.manager.rule.GoodsValidTypeEnum;
import com.vedeng.goods.manager.validator.model.SkuValidObject;
import com.vedeng.goods.utils.NamingUtils;
import org.apache.commons.lang3.StringUtils;

import java.beans.PropertyDescriptor;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class SkuBasicValidator extends GoodsValidator<SkuValidObject> {

    private static final Integer ON = 1;

    private static final List<String> IGNORE_PROP_NAMES = Arrays.asList("class", "skuName", "skuType"
            , "baseUnitId", "model", "spec");

    /**
     * 耗材类型忽略字段
     */
    private static final List<String> CONSUMABLES_IGNORE_PROP_NAMES = Arrays.asList("technicalParameter", "packingList");
    /**
     * 设备类型忽略字段
     */
    private static final List<String> INSTRUMENT_IGNORE_PROP_NAMES = Arrays.asList("changeNum", "unitId");

    private SkuBasicValidator() {
        super(GoodsValidTypeEnum.SKU);
    }

    public static SkuBasicValidator getInstance() {
        return new SkuBasicValidator();
    }

    @Override
    public Result validate(SkuValidObject requestData) {
        FluentValidator validator = getValidator();

        validator.on(requestData.getSkuType(), new NumberValidatorHandler("请输入SKU类型为空"));

        PropertyDescriptor[] propertyDescriptors = GoodsValidator.getPropertyDesc(requestData.getClass());
        goodsValidContext.addProperties(propertyDescriptors, requestData, getIgnorePropNames(requestData.getSkuType()));

        if (StringUtils.isNotEmpty(requestData.getName())) {
            validator.on(requestData.getName(), new MaxLengthValidate(NamingUtils.MAX_GOODS_NAME_LENGTH, "SKU名称超过长度限制"));
        }

        onValidCommonProperties(requestData);

        if (requestData.getSkuType() == GoodsConstants.SKU_TYPE_CONSUMABLES) {
            validator.on(requestData.getSpec(), new StringValidatorHandler("请填写规格"));
        }
        if (requestData.getSkuType() == GoodsConstants.SKU_TYPE_INSTRUMENT) {
            validator.on(requestData.getModel(), new StringValidatorHandler("请填写制造商型号"));
        }

        if (ON.equals(requestData.getIsEnableValidityPeriod())) {
            validator.on(requestData.getEffectiveDays(), new NumberValidate("产品有效期必须为数字类型"))
                    .when(StringUtils.isNotBlank(requestData.getEffectiveDays()));
        }

        return validator.doValidate().result(ResultCollectors.toSimple());
    }


    private List<String> getIgnorePropNames(Integer skuType) {
        List<String> ignorePropertyNames = new LinkedList<>(IGNORE_PROP_NAMES);
        //SKU属于器械时不校验字段
        if (skuType == GoodsConstants.SKU_TYPE_INSTRUMENT) {
            ignorePropertyNames.addAll(INSTRUMENT_IGNORE_PROP_NAMES);
        }
        //SKU属于耗材时不校验字段
        if (skuType == GoodsConstants.SKU_TYPE_CONSUMABLES) {
            ignorePropertyNames.addAll(CONSUMABLES_IGNORE_PROP_NAMES);
        }
        return ignorePropertyNames;
    }
}
