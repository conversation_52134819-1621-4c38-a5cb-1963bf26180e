package com.vedeng.goods.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Description com.vedeng.goods.vo
 * @Date 2022/6/16 13:41
 */
@Getter
@Setter
public class CoreSkuVo {

    /**
     * skuId
     */
    private Integer skuId;

    /**
     * 订货号
     */
    private String skuNo;

    /**
     * 产品名称
     */
    private String skuName;

    /**
     *品牌
     */
    private String brandName;

    /**
     * 规格
     */
    private String spec;

    /**
     * 型号
     */
    private String model;

    /**
     * SPU 产品类型SYS_OPTION_DEFINITION_ID
     */
    private Integer spuType;

    /**
     * 商品名称
     */
    private String showName;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 是否虚拟商品0否1是
     */
    private Integer isVirtureSku;

    /**
     * 虚拟商品所属费用类别ID
     */
    private Integer costCategoryId;

    /**
     * 属费用类别名称
     */
    private String categoryName;

    private Integer haveStockManage;

    private Integer isDirect;

}
