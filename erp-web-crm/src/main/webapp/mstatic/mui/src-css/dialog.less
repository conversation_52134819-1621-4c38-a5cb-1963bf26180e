
.dialog-fade-enter-active {
    transition: all 0.15s ease-out;
}
.dialog-fade-leave-active {
    transition: all 0.15s ease-in;
}
.dialog-fade-enter, .dialog-fade-leave-to {
    opacity: 0;
}

.dialog-move-enter-active {
    transition: all 0.15s ease-out;
}
.dialog-move-leave-active {
    transition: all 0.15s ease-in;
}
.dialog-move-enter, .dialog-move-leave-to {
    opacity: 0;
    transform: translate3d(0, -30px, 0)
}

.mui-dialog-container {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 2019;
    background-color: rgba(0,0,0,0.6);
    text-align: center;

    &::after {
        content: "";
        display: inline-block;
        height: 100%;
        width: 0;
        vertical-align: middle;
    }

    .nui-custom-dislog-center {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

.mui-dialog-wrapper {
    position: relative;
    border-radius: 12px;
    background-color: #fff;
    width: 300px;
    font-weight: 400;
    line-height: 1.5;
    overflow: hidden;

    &.success {
        background: linear-gradient(180deg, #dbffdb 0%, #ffffff 100%) no-repeat top #fff;
        background-size: 100% 62px;
    }
    &.error {
        background: linear-gradient(180deg, #ffe5e5 0%, #ffffff 100%) no-repeat top #fff;
        background-size: 100% 62px;
    }
    &.warn {
        background: linear-gradient(180deg, #ffede0 0%, #ffffff 100%) no-repeat top #fff;
        background-size: 100% 62px;
    }
    &.info {
        background: linear-gradient(180deg, #c3e7ff 0%, #ffffff 100%) no-repeat top #fff;
        background-size: 100% 62px;
    }

    .mui-dialog-content {
        padding: 30px 20px;

        .mui-dialog-icon {
            margin-bottom: 20px;

            > i {
                font-size: 32px;
                height: 32px;

                &.icon-info2 {
                    color: #0099FF;
                }
                &.icon-yes2 {
                    color: #13bf13;
                }
                &.icon-caution2 {
                    color: #ff6600;
                }
                &.icon-error2 {
                    color: #e64545;
                }
            }
        }

        .mui-dialog-title {
            font-size: 16px;
            color: #000;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .mui-dialog-middle {
            font-size: 14px;
            color: #000;
        }
    }

    .mui-dialog-button-choice {
        height: 50px;
        display: flex;
        border: none;
        border-top: solid 1px #E1E5E8;
        font-size: 14px;

        .mui-dialog-button {
            flex: 1;
            min-width: 0;
            color: #000;
            line-height: 50px;
            border-right: solid 1px #E1E5E8;
            cursor: pointer;

            &:last-child {
                border-right: none;
            }

            &.disabled {
                color: #CED2D9;
            }

            &.confirm {
                color: #09f;
            }

            &.delete {
                color: #E64545;
            }

            &.cancel {
                color: #333;
            }
        }
    }
}


.msg-fork {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    cursor: pointer;
    &:hover {
        color: #333333;
        .icon-delete {
            color: #333333;
        }
    }
    .icon-delete {
        font-size: 24px;
        color: #CCCCCC;
        &:hover {
            color: #333333;
        }
    }
}


.slide-dialog-default-footer {
    height: 53px;
    padding: 10px;
    background: #fff;
    border-top: solid 1px #EBEFF2;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 99;

    // &::after {
    //     content: "";
    //     display: block;
    //     border-top: solid 1px #EBEFF2;
    //     position: absolute;
    //     top: 0;
    //     left: 0;
    //     right: 0;
    // }

    .btn-cancel-flex {
        flex: 17;
        margin-right: 10px;
    }
    .btn-confirm-flex {
        flex: 25;
    }
}

