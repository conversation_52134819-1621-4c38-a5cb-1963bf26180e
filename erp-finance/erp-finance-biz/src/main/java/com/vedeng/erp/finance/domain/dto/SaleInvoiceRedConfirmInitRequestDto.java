package com.vedeng.erp.finance.domain.dto;

import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.utils.TaxesUtil;
import lombok.Data;

/**
 * 红字确认单初始化入参
 */
@Data
public class SaleInvoiceRedConfirmInitRequestDto implements ITaxesParam {
    /**
     * 纳税人识别号
     * 参：贝登税务信息
     * 长度：15/18/20
     */
    private String nsrsbh;

    /**
     * 购销方性质
     * 0-销售方
     * 1-购买方
     */
    private Integer gxfxz;

    /**
     * 发票来源代码
     * 1-增值税
     * 2-数电
     */
    private Integer fplyDm;

    /**
     * 购买方纳税人识别号
     * 客户-财务信息：税务登记号
     */
    private String gmfnsrsbh;

    /**
     * 销售方纳税人识别号
     * 参：贝登税务信息
     */
    private String xsfnsrsbh;

    /**
     * 开票方纳税人识别号
     * 参：贝登税务信息
     * 当 gxfxz 为 0 时，必填，默认为 xsfnsrsbh
     */
    private String kpfnsrsbh;

    /**
     * 数电发票号码
     * 蓝票发票号码
     * 当 fplyDm 为 2 时必填
     */
    private String lzfpqdhm;

    /**
     * 发票号码
     * 增值税发票号码
     */
    private String lzfphm;

    /**
     * 发票代码
     * 增值税发票代码
     */
    private String lzfpdm;

    /**
     * 开票日期
     * 蓝票开票日期
     * 格式：YYYY-MM-DD HH:mm:ss
     */
    private String lzkprq;

    /**
     * 特定业务类型代码
     */
    private String tdyslxDm;

    public SaleInvoiceRedConfirmInitRequestDto() {
        this.nsrsbh = TaxesUtil.taxesConfig.taxNo;
        this.gxfxz = 0;
        this.fplyDm = 2;
        this.xsfnsrsbh = TaxesUtil.taxesConfig.taxNo;
        this.kpfnsrsbh = TaxesUtil.taxesConfig.taxNo;
        this.lzfphm = "";
        this.lzfpdm = "";
        this.tdyslxDm = "";

    }

}
