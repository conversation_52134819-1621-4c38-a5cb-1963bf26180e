package com.vedeng.erp.finance.service;

import com.vedeng.erp.finance.dto.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 开票信息基础接口
 * @date 2022/8/27 16:07
 **/
public interface InvoiceApiService {

    /**
     * 查询发票
     *
     * @param invoiceDto 入参
     * @return List<InvoiceDto>
     */
    List<InvoiceDto> getInvoiceListByRelatedIdGroupByInvoiceNoAndColor(InvoiceDto invoiceDto);

    /**
     * 获取订单的发票列表信息（以发票+商品为维度聚合）
     *
     * @param invoiceDto 查询参数
     * @return Map<Integer, List < InvoiceByGoodsDto>>
     */
    Map<Integer, List<InvoiceByGoodsDto>> getInvoiceListByRelatedIdGroupByInvoiceNoAndColorAndOrderGoodsItemId(InvoiceDto invoiceDto);

    /**
     * 保存申请冲销的发票信息
     *
     * @param invoiceReversalInfo 申请冲销信息
     * @param afterSalesNo        售后单号
     * @param coverType           冲销售后单类型，1采购售后，2采购费用售后
     * @return 返回冲销保存的发票信息
     * <AUTHOR>
     */
    Integer coverInvoiceInfoSave(InvoiceReversalDto invoiceReversalInfo, String afterSalesNo, Integer coverType, Integer userId);

    /**
     * 费用单
     * 根据业务主键获取发票数据
     * 根据发票号的聚合分组
     *
     * @param relatedId 业务主键
     * @return List<CanRefundInvoiceDto>
     */
    List<InvoiceDto> getInvoicesByRelatedId(Integer relatedId);

    /**
     * 根据售后单id和开票类型查询发票信息
     *
     * @param afterSalesId 售后订单id
     * @param type         开票类型
     * @return List<InvoiceDto>
     */
    List<InvoiceDto> getInvoiceListByAfterSalesId(Integer afterSalesId, Integer type);

    /**
     * 根据票号 票编码 采购单id 查询1张票信息
     *
     * @param param
     * @return
     */
    List<InvoiceDto> getOneInvoiceByNoCodeType(InvoiceDto param);

    /**
     * 根据商品id查询该商品已录票的数量之和
     *
     * @param goodsId 商品id
     * @return 该商品已录票的数量之和
     */
    BigDecimal getAlreadyInvoiceNumByGoodsId(Integer goodsId);

    /**
     * 保存退票发票信息
     *
     * @param invoiceDto 发票主体 加 发票商品明细
     * @return 发票id
     */
    Integer saveRedInvoice(InvoiceDto invoiceDto);

    /**
     * 查询某个商品在所有红字有效票中的  数量之和
     *
     * @param invoiceIdList invoiceIdList
     * @param goodsId       detailgoodsId
     * @return InvoiceDetailDto
     */
    BigDecimal getAlreadyRedInvoiceNum(List<Integer> invoiceIdList, Integer goodsId);

    /**
     * 根据订单id查询收票金额之和（减去蓝字作废和红字票的金额）
     *
     * @param relatedId 订单id
     * @param type      开票申请类型
     * @return 收票金额之和
     */
    BigDecimal getInvoiceTotalAmountByRelatedId(Integer relatedId, Integer type);

    /**
     * 根据采购单id和发票号查询发票id（多个id随机返回一个）
     *
     * @param buyorderId
     * @param invoiceNo
     * @return
     * <AUTHOR>
     */
    Integer queryInvoiceIdByInfo(Integer buyorderId, String invoiceNo);

    /**
     * 反射调用查询发票信息
     * @param buyorderNo
     * @param invoiceNo
     * @return
     */
    InvoiceDto queryReceiptInvoiceRecord(String buyorderNo, String invoiceNo);

    /**
     * 发票保存关联入库记录(采购)
     *
     * @param invoiceIdList
     */
    boolean saveRelatedWarehousingLogId(List<Integer> invoiceIdList) throws Exception;

    /**
     * 回滚入库关联关系
     *
     * @param invoiceNo
     * @param invoiceCode
     * @param orderId
     * @throws Exception
     */
    void rollbackRelatedWarehousingLog(String invoiceNo, String invoiceCode, Integer orderId) throws Exception;

    /**
     * 发票保存关联出库记录(采购)
     *
     * @param invoiceId
     * @return
     * @throws Exception
     */
    boolean saveRelatedIssueLogId(Integer invoiceId) throws Exception;

    /**
     * 根据发票号，关联id，发票业务类型查询记录（单条）
     *
     * @param invoiceNo
     * @param relatedId
     * @param type
     * @return
     * <AUTHOR>
     */
    InvoiceDto queryInvoiceByInvoiceNoAndRelatedId(String invoiceNo, Integer relatedId, Integer type);

    /**
     * 保存采购仅退票红字发票
     *
     * @param invoiceDto
     * @param detailGoodsIdList
     * @param invoiceAmountList
     * @param invoicePriceList
     * @param invoiceNumList
     * @return
     * <AUTHOR>
     */
    Integer saveBuyorderTpRedInfo(InvoiceDto invoiceDto, List<Integer> detailGoodsIdList, List<BigDecimal> invoiceAmountList,
                                  List<BigDecimal> invoicePriceList, List<BigDecimal> invoiceNumList);

    /**
     * 保存仅退票新蓝字发票信息
     *
     * @param invoiceDto
     * @param afterSalesId
     * @param detailGoodsIdList
     * @param invoiceAmountList
     * @param invoicePriceList
     * @param invoiceNumList
     * <AUTHOR>
     */
    void saveTpNewInvoiceInfo(InvoiceDto invoiceDto, Integer afterSalesId, List<Integer> detailGoodsIdList, List<BigDecimal> invoiceAmountList,
                              List<BigDecimal> invoicePriceList, List<BigDecimal> invoiceNumList);

    List<String> getRedValidInvoiceListByExpenseId(Integer buyorderExpenseId);

    /**
     * 根据费用单id 获取审核通过的所有蓝票（含作废票）的信息
     *
     * @param buyorderExpenseId
     * @return List<InvoiceDto> 发票信息
     */
    List<InvoiceDto> getBlueInvoiceByExpenseId(Integer buyorderExpenseId);

    /**
     * 保存蓝票信息
     *
     * @param invoiceDtos
     */
    void saveBlueInvoice(List<InvoiceDto> invoiceDtos);


    /**
     * 获取销售售后发票
     *
     * @param saleOrderId 销售订单id
     * @return List<InvoiceDto>
     */
    List<InvoiceDto> getSaleOrderInvoice(Integer saleOrderId);

    /**
     * 根据开票申请去更新票的信息
     *
     * @param invoiceDto
     */
    void updateDownloadData(InvoiceDto invoiceDto);


    /**
     * 获取销售订单的开票金额
     *
     * @param saleOrderId 订单id
     * @return
     */
    BigDecimal getSaleOpenInvoiceAmount(Integer saleOrderId);

    /**
     * 获取开票申请对应的一条数据
     * @param invoiceApplyId
     * @return
     */
    InvoiceDto getSaleOrderInvoiceByApply(Integer invoiceApplyId,String invoiceNo);

    /**
     * 获取售后安调申请对应的一条数据
     * @param invoiceApplyId
     * @return
     */
    InvoiceDto getAtInvoiceByApply(Integer invoiceApplyId,String invoiceNo);


    /**
     * 根据发票主键Id查询
     */
    InvoiceDto findbyInvoiceId(Integer invoiceId);


    /**
     * 根据发票id和订单商品id查询发票明细
     *
     * @param invoiceId     invoiceId
     * @param detailGoodsId detailGoodsId
     * @return InvoiceDetailDto
     */
    InvoiceDetailDto findByInvoiceIdAndDetailGoodsId(Integer invoiceId, Integer detailGoodsId);

    /**
     * 根据发票id查询发票明细
     *
     * @param invoiceId invoiceId
     * @return InvoiceDetailDto
     */
    List<InvoiceDetailDto> findByInvoiceId(List<Integer> invoiceIdList);

    InvoiceGoodsResultDto queryInvoiceGoods(String invoiceNo);

    InvoiceGoodsResultDto queryInvoiceGoodsBySaleorderNo(String saleorderNo);

    /**
     * 计算发票金额
     *
     * @param invoiceDto 发票信息
     * @return 发票金额
     */
    BigDecimal calAmount(Integer afterSalesId);


    /**
     * 能否开票并发控制
     * @param orderId 订单id
     * @param orderType 订单类型(1-销售订单,2-售后订单)
     */
    EnableOpenInvoiceDto enableOpenInvoice(Integer orderId, Integer orderType);

    /**
     * 获取已开票数量
     * @param saleorderId
     * @param saleorderNo
     * @return
     */
    List<Map<String, Object>> getInvoicedTaxNum(Integer saleorderId, String saleorderNo);

    /**
     * 查询老数据发票信息
     * @param saleorderId
     * @param saleorderNo
     * @return
     */
    List<Map<String, Object>> getInvoicedDataOld(Integer saleorderId, String saleorderNo);

    /**
     * 获取已申请数量(新数据)
     * @param saleorderId
     * @param saleorderNo
     * @return
     */
    List<Map<String, Object>> getAppliedTaxNum(Integer saleorderId, String saleorderNo);

    /**
     * 查询发票链接信息
     * @param invoiceNo 发票号
     * @return 发票链接信息
     */
    InvoiceDto queryHref(String invoiceNo);

    /**
     * 更新发票链接信息
     * @param invoiceNo 发票号
     * @param invoiceHref 发票链接
     * @return 更新结果
     */
    void updateHref(String invoiceNo, String invoiceHref);
}
