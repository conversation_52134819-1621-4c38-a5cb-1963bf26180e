package com.vedeng.activiti.taskassign;

import com.vedeng.activiti.service.ActionProcdefService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

/**
 * @Description 实现抄送的监听器
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/10/18
 */
public class AutoNoticeTaskListener implements TaskListener {
    private static final Logger log = LoggerFactory.getLogger(AutoCompleteExecutionListener.class);
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private ActionProcdefService actionProcdefService = context.getBean(ActionProcdefService.class);

    @Override
    public void notify(DelegateTask delegateTask) {
        actionProcdefService.completeTaskByAdmin(delegateTask.getId(),"抄送自动完成",delegateTask.getVariables());
    }
}