package com.vedeng.crm.web.controller;

import com.vedeng.common.core.annotation.MenuDesc;
import com.vedeng.common.core.base.BaseController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品分类人员配置
 * @date 2025/3/26 8:56
 */
@Controller
@RequestMapping("/role/userCategoryConfig")
public class UserCategoryConfigController extends BaseController {


    @RequestMapping(value = "/index")
    @MenuDesc(menuValue = "C06", menuDesc = "商品分类人员配置")
    public String index() {

        return "/vue/view/crm/profile/role/userCategoryConfig/index";
    }

}
