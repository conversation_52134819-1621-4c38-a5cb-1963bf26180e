package com.newtask.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.TraderConstants;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.crm.api.dto.customergroup.BaseGroupDto;
import com.vedeng.crm.api.dto.customergroup.TraderBehaviorAttributeDto;
import com.vedeng.crm.api.dto.customergroup.TraderGroupEXTDto;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.group.BaseBehavior;
import com.vedeng.trader.group.BehaviorFactory;
import com.vedeng.trader.model.TraderAttributeQueryParams;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;

public class TraderGroupOrLabelService extends BaseServiceimpl {
    private Logger logger= LoggerFactory.getLogger(TraderGroupOrLabelService.class);
    public static ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 8, 6, TimeUnit.SECONDS, new LinkedBlockingQueue<>(128));


    protected BehaviorFactory behaviorFactory=new BehaviorFactory();

    @Autowired
    public TraderCustomerMapper traderCustomerMapper;
    /**
     * <b>Description:</b>根据客户基本信息得到客户id集合<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/27
     */
    public BitSet getBaseTraderIds(BaseGroupDto group, List<Integer> organizations) {
        BitSet bitSet = new BitSet();
        Map<String, Object> queryMap = new HashMap<>();
        TraderAttributeQueryParams params = new TraderAttributeQueryParams();
        params.setArea(group.getBusinessArea());
        params.setBrands(parseStrToList(group.getBusinessBrand(), String.class));
        params.setCustomerTypes(parseStrToList(group.getBusinessCustomerCategory(),String.class));
        params.setOrganizations(organizations);
        params.setProductTypes(parseStrToList(group.getBusinessGoodsCategory(), String.class));
        params.setCustomerGradeIds(parseStrToList(group.getCustomerGrade(), Integer.class));
        params.setCustomerTripIds(parseStrToList(group.getCustomerTrip(), Integer.class));
        int currentPage = 1;
        Page page = new Page(currentPage, 2000);
        queryMap.put("q", params);
        queryMap.put("page", page);
        do {
            List<Integer> traderIds = listTraderIdsByBaseCondition(queryMap, group);

            if (CollectionUtils.isNotEmpty(traderIds)) {
                for (Integer id : traderIds) {
                    bitSet.set(id);
                }
            }
            if(bitSet.size()>2000000){
                logger.error("traderId可能大于1百万");
                return bitSet;
            }
            currentPage++;
            page.setPageNo(currentPage);
        } while (currentPage <= page.getTotalPage());
        solveTraderContract(group.getTraderContract(), bitSet);
        return bitSet;
    }

    /**
     * 基本信息中经营数据更改为订单覆盖数据
     */
    private List<Integer> listTraderIdsByBaseCondition(Map<String, Object> oldQueryMap, BaseGroupDto group) {
        List<Integer> traderIds = traderCustomerMapper.getTraderIdsByBaseAttributesListPage(oldQueryMap);
        if (traderIds.size() > 0) {
            // 2021-4-30 经营产品 数据 更改为【订单覆盖品类】 group.getBusinessGoods()
            List<String> categoryIds = parseStrToList(group.getBusinessGoods(), String.class);
            // 2021-4-30 经营科室 数据 更改为【订单覆盖科室】 group.getBusinessDepartment()
            List<String> departments = parseStrToList(group.getBusinessDepartment(), String.class);

            traderIds = traderCustomerMapper.filterTraderIdsByCoverData(traderIds, categoryIds, departments);
        }

        return traderIds;

    }

    public ResultInfo solveBehaviors(BitSet result, List<TraderBehaviorAttributeDto> behaviors,BaseGroupDto group){
        try {
            List<Future<BitSet>> resultList = new ArrayList<>();
            CountDownLatch countDownLatch = new CountDownLatch(behaviors.size());
            for (TraderBehaviorAttributeDto a : behaviors) {
                if (StringUtil.isEmpty(a.getTraderBehaviorAttributeName())
                        || StringUtil.isEmpty(a.getTraderBehaviorAttributeContent())) {
                    return new ResultInfo(-1, "群组中有行为信息为空");
                }
                BaseBehavior behavior = behaviorFactory.getBehavior(a);
                if (behavior == null) {
                    return new ResultInfo(-1, "生成行为处理类失败");
                }
                behavior.setCountDownLatch(countDownLatch);
                behavior.setBaseTraderSet((BitSet) result.clone());
                Future<BitSet> future = executor.submit(new Callable<BitSet>() {
                    @Override
                    public BitSet call() throws Exception {
                        return behavior.getValidateTrader();
                    }
                });
                resultList.add(future);
            }
            countDownLatch.await(3600, TimeUnit.SECONDS);
            System.out.println("结束行为计算");
            if (TraderConstants.RULE_CONDITION_AND.equals(group.getRuleCondition())) {
                for (Future f : resultList) {
                    BitSet b = (BitSet) f.get();
                    logger.info("行为结果",b);
                    if (b == null) {
                        return new ResultInfo(-1, "行为运算出错");
                    }
                    result.and(b);
                }
            } else if (TraderConstants.RULE_CONDITION_OR.equals(group.getRuleCondition())) {
                int count=0;
                for (Future f : resultList) {

                    BitSet b = (BitSet) f.get();
                    logger.info("行为结果",b);
                    if (b == null) {
                        b = new BitSet();
                    }
                    if(count==0){
                        result.and(b);
                    }else {
                        result.or(b);
                    }
                    count++;
                }
            }
        }catch (Exception ex){
            logger.error("处理客户行为时出错",ex);
            return new ResultInfo(-1,"操作失败");
        }
        return new ResultInfo(0,"操作成功");
    }
    /**
     * <b>Description:</b>处理是否合约客户<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/27
     */
    private BitSet solveTraderContract(Integer contract, BitSet bitSet) {
        if (contract == null||contract<0) {
            return bitSet;
        }
        try {
            String url = crmUrl + "api/customer/contract/trader/ids";
            com.alibaba.fastjson.TypeReference<RestfulResult<List<Integer>>> typeReference = new com.alibaba.fastjson.TypeReference<RestfulResult<List<Integer>>>() {
            };
            RestfulResult<List<Integer>> result = HttpRestClientUtil.restGet(url, typeReference, null, null);
            BitSet contractSet = new BitSet();
            if(result!=null&&result.isSuccess()){
                List<Integer> traderIds=result.getData();
                if(CollectionUtils.isNotEmpty(traderIds)){
                    for(Integer id:traderIds){
                        if(id==null){
                            continue;
                        }
                        contractSet.set(id);
                    }
                }
            }
            if (contract == 1) {
                bitSet.and(contractSet);
                return bitSet;
            } else if (contract == 0) {
                bitSet.andNot(contractSet);
                return bitSet;
            }
        } catch (Exception ex) {
            logger.error("向crm请求合约用户失败", ex);
        }
        return bitSet;
    }

    /**
     * <b>Description:</b>根据字符串获取集合<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/27
     */
    private <T> List<T> parseStrToList(String content, Class<T> tClass) {
        if (StringUtil.isEmpty(content) || content.length() < 3) {
            return null;
        }
        try {
            TypeReference<List<T>> typeReference = new TypeReference<List<T>>() {
            };
            return JsonUtils.readValueByType(content, typeReference);
        } catch (Exception ex) {
            logger.error("基本信息属性出错");
        }
        return null;
    }

    /**
     * <b>Description:</b>处理指定客户<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/27
     */
    public void solveTraderIds(BitSet res, BaseGroupDto group) {
        try {
            if (StringUtil.isNotBlank(group.getTraderIds()) && group.getTraderIds().length() > 2) {
                BitSet traderSet = new BitSet();
                TypeReference<List<Integer>> typeReference = new TypeReference<List<Integer>>() {
                };
                List<Integer> traderIds = JsonUtils.readValueByType(group.getTraderIds(), typeReference);
                if (CollectionUtils.isEmpty(traderIds)) {
                    return;
                }
                for (Integer id : traderIds) {
                    if (id == null) {
                        continue;
                    }
                    traderSet.set(id);
                }
                if (group.getRuleCondition() == 0) {
                    res.or(traderSet);
                } else if (group.getRuleCondition() == 1) {
                    res.and(traderSet);
                }
            }
        } catch (Exception ex) {
            logger.error("指定客户出错", ex);
        }
    }

    protected void saveResultToCrm(Integer id, Integer success, String type) {
        try {
            String base = crmUrl + "api/customergroup/erp/consumer/%d/%d/?type=%s";
            String url = String.format(base, id, success, type);
            logger.info("请求保存结果地址 ：url :{}", url);
            com.alibaba.fastjson.TypeReference<RestfulResult<?>> typeReference = new com.alibaba.fastjson.TypeReference<RestfulResult<?>>() {
            };
            HttpRestClientUtil.restPost(url, typeReference, null, null);
        } catch (Exception e) {
            logger.error("请求保存结果失败 msg :{}", e.getMessage(), e);
        }

    }

}
