package com.vedeng.order.enums;


import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public enum MessageTemplateEnum {

    /**
     * 响应预警 - 采购端触发(点击跳转只报价咨询页面)
     */
    TEMPLATE_123(123, "三级响应预警待处理人", "./order/quote/getQuoteDetail.do?quoteorderId={0}&viewType=5", MessageAcceptorType.ONLY_SELF, QuotedAlarmMessageParameter.ONLY_ORDER_NO_PARAM),

    TEMPLATE_124(124, "二级响应预警待处理人", "./order/quote/getQuoteDetail.do?quoteorderId={0}&viewType=5", MessageAcceptorType.ONLY_SELF, QuotedAlarmMessageParameter.ONLY_ORDER_NO_PARAM),

    TEMPLATE_125(125, "二级响应预警待处理人上级", "./order/quote/getQuoteDetail.do?quoteorderId={0}&viewType=5", MessageAcceptorType.ONLY_SUPER, QuotedAlarmMessageParameter.USERNAME_AND_ORDER_NO_PARAM),

    TEMPLATE_126(126, "一级响应预警待处理人", "./order/quote/getQuoteDetail.do?quoteorderId={0}&viewType=5", MessageAcceptorType.ONLY_SELF, QuotedAlarmMessageParameter.ONLY_ORDER_NO_PARAM),

    TEMPLATE_127(127, "一级响应预警待处理人上级和所属部分总监", "./order/quote/getQuoteDetail.do?quoteorderId={0}&viewType=5", MessageAcceptorType.SUPER_AND_HEADER, QuotedAlarmMessageParameter.USERNAME_AND_ORDER_NO_PARAM),

    /**
     * 跟进预警 - 销售端触发(点击跳转只报价详情页面)
     */
    TEMPLATE_128(128, "二级跟进预警待处理人", "./order/quote/getQuoteDetail.do?quoteorderId={0}&viewType=2", MessageAcceptorType.ONLY_SELF, QuotedAlarmMessageParameter.ONLY_ORDER_NO_PARAM),
    TEMPLATE_129(129, "一级跟进预警待处理人", "./order/quote/getQuoteDetail.do?quoteorderId={0}&viewType=2", MessageAcceptorType.ONLY_SELF, QuotedAlarmMessageParameter.ORDER_NO_AND_PURCHASING_TIME_PARAM),
    TEMPLATE_130(130, "一级跟进预警待处理人上级", "./order/quote/getQuoteDetail.do?quoteorderId={0}&viewType=4", MessageAcceptorType.ONLY_SUPER, QuotedAlarmMessageParameter.FULL_PARAM),
    ;


    private Integer id;
    private String description;
    private String url;
    private Integer acceptorType;
    private List<String> parameterNames;

    MessageTemplateEnum(Integer id, String description, String url, Integer acceptorType, List<String> parameterNames) {
        this.id = id;
        this.description = description;
        this.url = url;
        this.acceptorType = acceptorType;
        this.parameterNames = parameterNames;
    }

    public Integer getId() {
        return id;
    }

    public String getDescription() {
        return description;
    }

    public String getUrl() {
        return url;
    }

    public Integer getAcceptorType() {
        return acceptorType;
    }

    public List<String> getParameterNames() {
        return parameterNames;
    }

    /**
     * 判断是否包含URL
     *
     * @return
     */
    public boolean hasURL() {
        return getUrl() != null;
    }

    /**
     * 消息接受人类型
     */
    public static class MessageAcceptorType {

        public final static int ONLY_SELF = 1;
        public final static int ONLY_SUPER = 2;
        public final static int SUPER_AND_HEADER = 3;

    }


    public static class QuotedAlarmMessageParameter {

        public static final String PARAM_QUOTED_ORDER_NO = "quoteOrderNo";
        public static final String PARAM_USERNAME = "userName";
        public static final String PARAM_PURCHASING_TIME = "purchasingTimeStr";


        private final static List<String> ONLY_ORDER_NO_PARAM = new ArrayList<>();

        static {
            ONLY_ORDER_NO_PARAM.add(PARAM_QUOTED_ORDER_NO);
        }


        private final static List<String> USERNAME_AND_ORDER_NO_PARAM = new ArrayList<>();

        static {
            USERNAME_AND_ORDER_NO_PARAM.add(PARAM_USERNAME);
            USERNAME_AND_ORDER_NO_PARAM.add(PARAM_QUOTED_ORDER_NO);
        }


        private final static List<String> ORDER_NO_AND_PURCHASING_TIME_PARAM = new ArrayList<>();

        static {
            ORDER_NO_AND_PURCHASING_TIME_PARAM.add(PARAM_QUOTED_ORDER_NO);
            ORDER_NO_AND_PURCHASING_TIME_PARAM.add(PARAM_PURCHASING_TIME);
        }


        private final static List<String> FULL_PARAM = new ArrayList<>();

        static {
            FULL_PARAM.add(PARAM_USERNAME);
            FULL_PARAM.add(PARAM_QUOTED_ORDER_NO);
            FULL_PARAM.add(PARAM_PURCHASING_TIME);
        }
    }

}
