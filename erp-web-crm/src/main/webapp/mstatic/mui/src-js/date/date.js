Vue.component('ui-date-picker', {
    template: `
        <div class="vd-ui-date">
            <div 
                class="vd-ui-date-wrapper"
                :class="[animation ? animation : '', 'vd-ui-date-wrapper--' + type]" 
                ref="pickers"
                @click.stop
            >
                <!-- 时间 -->
                <ui-time-panel
                    v-if="type === 'time'"
                    :type="type"
                    :value="baseValue"
                    @pick="pickTime"
                    :disabled-date="disabledDate"
                    v-bind="pickerOptions"
                ></ui-time-panel>

                <!-- 日期时间 -->
                <ui-date-time-panel
                    v-else-if="type === 'datetime'"
                    :firstDayOfWeek="firstDayOfWeek"
                    :type="type"
                    :value="baseValue"
                    @pick="pickDateTime"
                    :disabled-date="disabledDate"
                    :pickerOptions="pickerOptions"
                    :selectionMode="selectionMode"
                ></ui-date-time-panel>

                <ui-date-panel
                    v-else
                    :type="type"
                    :value="baseValue"
                    :firstDayOfWeek="firstDayOfWeek"
                    :shortcuts="shortcuts"
                    @pick="pickDate"
                    :disabled-date="disabledDate"
                    :selectionMode="selectionMode"
                    v-bind="pickerOptions"
                    :max="max"
                    :min="min"
                ></ui-date-panel>
            </div>
        </div>
    `,
    props: {
        value: {},
        readonly: Boolean,   // 是否只读
        disabled: {  // 是否禁用
            type: Boolean,
            default: false
        },
        editable: {  // 是否显示清除按钮
            type: Boolean,
            default: true
        },
        clearable: {
            type: Boolean,
            default: true
        },
        width: {
            type: String,
            default: '100%'
        },
        size: String,   // large, small, mini
        placeholder: String,

        type: {   // date, year, month, time, datetime
            type: String,
            default: 'date'
        },
        appendToBody: {
            type: Boolean,
            default: false,
        },
        format: String,
        popperClass: String,
        pickerOptions: {},
        defaultValue: {}, // 可选，选择器打开时默认显示的时间
        valueFormat: String,   // 绑定值的格式。不指定则绑定值为 Date 对象
        name: {
            default: '',
        },
        prefixIcon: String,
        clearIcon: {
            type: String,
            default: 'vd-ui-icon-circle-close'
        },
        validateEvent: {    // 输入时是否触发表单的校验
            type: Boolean,
            default: true
        },
        id: {
            default: '',
        },
        shortcuts: Array, // 快捷选项
        firstDayOfWeek: {
            type: Number,
            default: 1,
            validator: val => val >= 1 && val <= 7
        },
        disabledDate: Function,
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
        valid: String,
        zindex: String,
        calcerror: {
            type: Boolean,
            default: true
        },
        min: '',
        max: '',
    },

    data () {
        return {
            pickerVisible: false,   // panel是否展示
            showClose: false,
            valueOnOpen: null, // 选择器打开时的值，用于确认是否发生变化
            unwatchPickerOptions: null,

            baseValue: null,    // 真实选中的日期 【一定是日期格式】 【可能是数组中多个日期】 同 ele parsedValue
            userInput: null,    // 输入框实时输入值
            animation: '',
            realDate: '',    // 实际上选择的时间（日期，年月，月都放在这里），显示都根据这个来
        }
    },
    watch: {
        value () {
            this.initDate();
        },
    },
    computed: {
        // 同 ele reference
        vdInput () {
            const vdInput = this.$refs.vdInput;
            if (vdInput) return vdInput.$el || vdInput;
            else return '';
        },

        refInput () {
            if (this.vdInput) {
                return [].slice.call(this.vdInput.querySelectorAll('input'));
            }
            return [];
        },

        // 是否显示清除按钮【需要显示 且 有值】
        clearValue () {
            return this.clearable && this.valueOnOpen && true;
        },

        selectionMode () {
            if (this.type == 'month') {
                return 'month';
            } else if (this.type == 'year') {
                return 'year';
            } else if (this.type === 'dates') {
                return 'dates';
            } else if (this.type === 'week') {
                return 'week';
            }
            return 'day';
        },

        // 回显示在input中的值 【需要处理显示格式】
        displayValue () {
            if (this.userInput !== null) {
                return this.userInput;
            }
            // if (Array.isArray(this.baseValue)) {
            //     return [
            //         util_date.formatDate(this.baseValue[0]),
            //         util_date.formatDate(this.baseValue[1])
            //     ];
            // }
            if (this.baseValue !== null) {
                return util_date.format(this.baseValue, this.type);
            }
            else {
                return ''
            }
        },

    },
    mounted () {
        this.initDate();
    },
    methods: {
        // 初始化日期
        initDate () {
            if (this.value) {
                if (util_date.isDate(this.value)) {
                    this.baseValue = this.value;
                }
                else if (typeof(this.value) == 'string' && util_date.checkStringCanToDate(this.value, this.type)) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(this.value);
                    } else if (this.type == 'datetime') {
                        this.baseValue = util_date.timeToDatetime(this.value);
                    } else {
                        this.baseValue = util_date.toDate(this.value);
                    }
                }
            } else {
                this.baseValue = null;
            }
        },

        // 展示panel
        showPanel () {
            if (this.readonly || this.pickerDisabled) return;
            this.pickerVisible = true;
            this.valueOnOpen = Array.isArray(this.baseValue) ? [...this.baseValue] : this.baseValue;
        },
        // 关闭panel
        hidePanel () {
            this.pickerVisible = false;
            this.userInput = null;
            this.dealBlur();
        },
        triggerFocus() {
            setTimeout(() => {
                this.$refs.vdInput.focus();
                this.showPanel();
            }, 10)
        },
        // input获焦
        handleFocus (e) {
            // const type = this.type;
            if (util_date.HAVE_TRIGGER_TYPES.indexOf('date') !== -1 && !this.pickerVisible) {
                this.showPanel();
            }
            this.$emit('focus', e);
        },
        // input 失焦
        handleBlur () {
            // this.pickerVisible = false;
            // this.dealBlur();
        },
        // 主动 失去焦点
        dealBlur() {
            console.log('blur');
            this.refInput.forEach(input => input.blur());
            this.$emit('blur', this);
        },
        // 单选-清除
        handleClear () {
            this.emitInput(null);
            this.emitChange(null);
        },
        // change
        handleChange () {
            // 如果有值
            if (this.userInput) {

                const value = util_date.checkStringCanToDate(this.userInput, this.type);
                // 验证合法性
                if (value) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(value);   // 年和月是否合法
                    } else {
                        this.baseValue = util_date.toDate(value);   // 年和月是否合法
                    }
                    this.emitInput(this.baseValue);
                    this.emitChange(this.baseValue);
                }
                else {
                    // 年份，月份输入时，重新处理把时间给user-input，不然后被赋一次年月或月份后，不好对比时间
                    this.baseValue = this.valueOnOpen || null;
                }

                this.hidePanel();   // 关闭弹窗
            }

            if (this.userInput === '') {
                this.emitInput(null);
                this.emitChange(null);
                this.baseValue = null;
                this.userInput = null;
            }


        },
        // input 触发按键
        handleKeydown (event) {
            const keyCode = event.keyCode;

            // ESC
            if (keyCode === 27) {
                this.hidePanel();
                event.stopPropagation();
                return;
            }

            // Tab
            if (keyCode === 9) {
                this.handleChange();
                this.hidePanel();
                event.stopPropagation();
                return;
            }

            if (keyCode === 13) {
                if (this.userInput) {
                    this.handleChange();
                    this.hidePanel();
                }
                event.stopPropagation();
                return;
            }

            if (this.userInput) {
                event.stopPropagation();
                return;
            }
        },
        // change事件
        emitChange(val) {
            if (!util_date.valueEquals(val, this.valueOnOpen)) {
                this.$emit('change', util_date.format(val, this.type));
                this.valueOnOpen = val;
                // this.checkValid(util_date.format(val, this.type));
            }
        },
        // input事件
        emitInput(val) {
            this.$emit('input', util_date.format(val, this.type));
            // this.checkValid(util_date.format(val, this.type));
        },
        // 选中日期  [关闭panel => emit值]
        pickDate (val) {
            this.baseValue = val;
            this.userInput = null;
            this.hidePanel();
            this.emitInput(val);
            this.emitChange(val);
        },
        // 选中时间  [关闭panel => emit值]
        pickTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            this.emitInput(val);
            this.emitChange(val);
            if (isFinal) {
                this.hidePanel();
            }
        },
        // 选中日期时间
        pickDateTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            this.emitInput(val);
            this.emitChange(val);
            if (isFinal) {
                this.hidePanel();
            }
        },
    }
})