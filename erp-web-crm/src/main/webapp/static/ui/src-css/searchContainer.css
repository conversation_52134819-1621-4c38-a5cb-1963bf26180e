.vd-ui-search-wrap {
  background: #fff;
}
.vd-ui-search-wrap .vd-ui-search-container {
  padding: 20px 10px;
}
.vd-ui-search-wrap .vd-ui-search-container.hidden {
  opacity: 0;
}
.vd-ui-search-wrap .vd-ui-search-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: -10px;
  position: relative;
}
.vd-ui-search-wrap .vd-ui-search-list.show-line {
  max-height: none !important;
}
.vd-ui-search-wrap .vd-ui-search-list .search-item {
  align-items: center;
  margin-right: 20px;
  margin-bottom: 10px;
  width: calc(20% - 20px);
  display: flex;
}
.vd-ui-search-wrap .vd-ui-search-list .search-item.hidden {
  opacity: 0;
  z-index: -1;
}
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-label {
  width: 100px;
  text-align: right;
  color: #999;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-content {
  flex: 1;
}
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-content .vd-ui-select,
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-content .vd-ui-date,
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-content .vd-ui-cascader {
  width: 100%;
}
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-content .vd-ui-input-multiple {
  width: 100%;
}
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-content .vd-ui-date-editor,
.vd-ui-search-wrap .vd-ui-search-list .search-item .search-content .vd-ui-range {
  width: 100%;
}
.vd-ui-search-wrap .vd-ui-search-btns {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  position: relative;
}
.vd-ui-search-wrap .vd-ui-search-btns .search-btn-inner {
  display: flex;
  margin: 0 auto;
  align-items: center;
}
.vd-ui-search-wrap .vd-ui-search-btns .search-btn-inner .vd-ui-button {
  margin-left: 10px;
}
.vd-ui-search-wrap .vd-ui-search-btns .search-btn-inner .vd-ui-button:first-child {
  margin-left: 0;
}
.vd-ui-search-wrap .vd-ui-search-btns .vd-ui-search-toggle {
  color: #09f;
  cursor: pointer;
  margin-left: 20px;
}
.vd-ui-search-wrap .vd-ui-search-btns .vd-ui-search-toggle .vd-ui_icon {
  font-size: 16px;
  margin-left: 5px;
  vertical-align: -2px;
}
.vd-ui-search-wrap .vd-ui-search-btns .vd-ui-search-toggle:hover {
  color: #f60;
}
.vd-ui-search-wrap .vd-ui-search-option {
  position: absolute;
  right: 20px;
  display: flex;
  align-items: center;
}
.vd-ui-search-wrap .vd-ui-search-option .search-option-item {
  color: #09f;
  cursor: pointer;
  margin-left: 20px;
  position: relative;
}
.vd-ui-search-wrap .vd-ui-search-option .search-option-item::before {
  content: '';
  position: absolute;
  width: 1px;
  height: 14px;
  background: #e1e5e8;
  left: -10px;
  top: 5px;
}
.vd-ui-search-wrap .vd-ui-search-option .search-option-item:first-child {
  margin-left: 0;
}
.vd-ui-search-wrap .vd-ui-search-option .search-option-item:first-child::before {
  display: none;
}
.vd-ui-search-wrap .vd-ui-search-option .search-option-item .vd-ui_icon {
  font-size: 16px;
  vertical-align: -2px;
  margin-right: 5px;
}
.vd-ui-search-wrap .vd-ui-search-option .search-option-item.item-setting {
  color: #999;
}
.vd-ui-search-wrap .vd-ui-search-option .search-option-item:hover {
  color: #f60;
}
.vd-ui-search-wrap .dlg-form-footer {
  padding-left: 150px;
  width: 100%;
  display: flex;
}
.search-setting-dlg {
  overflow: hidden;
}
.search-setting-dlg .search-setting-top {
  display: flex;
  margin-bottom: 20px;
}
.search-setting-dlg .search-setting-top .setting-select-all {
  flex: 1;
}
.search-setting-dlg .search-setting-top .setting-refresh {
  color: #09f;
  cursor: pointer;
}
.search-setting-dlg .search-setting-top .setting-refresh .vd-ui_icon {
  font-size: 16px;
  vertical-align: -2px;
  margin-right: 5px;
}
.search-setting-dlg .search-setting-top .setting-refresh:hover {
  color: #f60;
}
.search-setting-dlg .dlg-setting-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -5px;
}
.search-setting-dlg .dlg-setting-list.sortable .dlg-setting-item .vd-ui-checkbox-item {
  cursor: pointer;
}
.search-setting-dlg .dlg-setting-list .dlg-setting-item {
  width: calc(25% - 3.75px);
  margin-right: 5px;
  border: solid 1px #E1E5E8;
  border-radius: 3px;
  margin-bottom: 5px;
  user-select: none;
}
.search-setting-dlg .dlg-setting-list .dlg-setting-item:nth-child(4n) {
  margin-right: 0;
}
.search-setting-dlg .dlg-setting-list .dlg-setting-item .vd-ui-checkbox-item {
  width: 100%;
  padding: 10px;
  cursor: move;
}
.search-setting-dlg .dlg-setting-list .dlg-setting-item .vd-ui-checkbox-item .vd-ui-checkbox-inner {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.search-setting-dlg .dlg-setting-list .dlg-setting-item.placehodler {
  opacity: 0.1;
}
@media screen and (max-width: 2500px) {
  .vd-ui-search-wrap .vd-ui-search-list .search-item {
    width: calc(25% - 20px);
  }
}
@media screen and (max-width: 1366px) {
  .vd-ui-search-wrap .vd-ui-search-list .search-item {
    width: calc(33.33% - 20px);
  }
  .vd-ui-search-wrap.no-left .vd-ui-search-list .search-item {
    width: calc(33.33% - 20px);
  }
}
@media screen and (max-width: 1680px) and (min-width: 1367px) {
  .vd-ui-search-wrap .vd-ui-search-list .search-item {
    width: calc(33.33% - 20px);
  }
  .vd-ui-search-wrap.no-left .vd-ui-search-list .search-item {
    width: calc(25% - 20px);
  }
}
.vd-ui-list-container {
  padding: 20px;
}
.vd-ui-list-container .vd-ui-list-inner {
  background: #fff;
  border: solid 1px #E1E5E8;
}
.vd-ui-list-container .vd-ui-list-inner.no-border-top {
  border-top: 0;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-top {
  padding: 10px 20px;
  display: flex;
  align-items: center;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-top .list-container-top-buttons {
  display: flex;
  align-items: center;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-top .vd-ui-button {
  margin-right: 10px;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-top .vd-ui-button:last-child {
  margin-right: 0;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-top .list-select-num {
  margin-left: 20px;
  color: #999;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-content {
  position: relative;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-content .list-container-setting {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 11;
  width: 37px;
  height: 37px;
  text-align: center;
  line-height: 37px;
  font-size: 16px;
  color: #666;
  border-left: solid 1px #E1E5E8;
  cursor: pointer;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-content .list-container-setting:hover {
  color: #f60;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-pager {
  padding: 10px 20px;
  display: flex;
  align-items: center;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-pager .list-container-total {
  flex: 1;
  color: #999;
}
.vd-ui-list-container .vd-ui-list-inner .list-container-pager .vd-ui-select {
  margin-left: 20px;
}
