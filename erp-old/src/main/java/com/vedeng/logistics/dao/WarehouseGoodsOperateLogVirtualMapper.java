package com.vedeng.logistics.dao;

import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WarehouseGoodsOperateLogVirtual;
import com.vedeng.logistics.model.WarehouseLog;
import com.vedeng.order.model.Saleorder;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 出入库日志Mapper
 *
 * <AUTHOR>
 */
public interface WarehouseGoodsOperateLogVirtualMapper {


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL
     *
     * @mbggenerated Wed Apr 13 14:44:16 CST 2022
     */
    int insert(WarehouseGoodsOperateLogVirtual record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL
     *
     * @mbggenerated Wed Apr 13 14:44:16 CST 2022
     */
    int insertSelective(WarehouseGoodsOperateLogVirtual record);


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL
     *
     * @mbggenerated Wed Apr 13 14:44:16 CST 2022
     */
    WarehouseGoodsOperateLogVirtual selectByPrimaryKey(Integer warehouseGoodsOperateLogVirtualId);


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL
     *
     * @mbggenerated Wed Apr 13 14:44:16 CST 2022
     */
    int updateByPrimaryKeySelective(WarehouseGoodsOperateLogVirtual record);

    /**
     * <AUTHOR>
     * @desc 查询入库记录
     * @param warehouseGoodsOperateLog
     * @return
     */
    List<WarehouseGoodsOperateLogVirtual> getAvailableLogicalGoods(WarehouseGoodsOperateLogVirtual warehouseGoodsOperateLog);

    /**
     * @description: 更新剩余库存数量和是否使用完字段
     * @return: int
     * @author: Strange
     * @date: 2020/8/4
     **/
    int updateOutIsUseAndLastStockNumById(WarehouseGoodsOperateLogVirtual ininfo);

    /**
     * 获取未关联快递详情出入库日志id
     *
     * @Author:strange
     * @Date:13:27 2020-02-10
     */
    List<WarehouseGoodsOperateLogVirtual> getWarehouseIdByExpressDetail(ExpressDetail expressDetail);

    /**
     * <AUTHOR>
     * @desc 根据销售单信息查询直发出库记录
     * @param saleorder
     * @return
     */
    List<WarehouseGoodsOperateLogVirtual> getWarehouseOutList(Saleorder saleorder);

    /**
     * <AUTHOR>
     * @desc 根据销售单id查询有效的直发出库记录
     * @param saleorderId
     * @return
     */
    List<Integer> queryWarehouseLogIdBySaleorderId(Integer saleorderId);

    /**
     * <AUTHOR>
     * @desc 根据出库记录id查询记录类型
     * @param logId
     * @return
     */
    Integer getOperateTypeById(Integer logId);
    /**
     * <AUTHOR>
     * @desc 查询直发出库记录
     * @param warehouseGoodsOperateLog
     * @return
     */
    List<WarehouseGoodsOperateLog> getwlById(WarehouseGoodsOperateLog warehouseGoodsOperateLog);

    /**
     * <AUTHOR>
     * @desc 根据出入库记录id获取采购单价或销售单价
     * @param warehouseGoodsOperateLog
     * @return
     */
    WarehouseGoodsOperateLog getPrintPriceById(WarehouseGoodsOperateLog warehouseGoodsOperateLog);/**
     * 获取最后出库时间
     *
     * @Author:strange
     * @Date:09:37 2020-02-25
     */
    Long getLastOutTime(WarehouseGoodsOperateLog w);

    /**
     * <AUTHOR>
     * @desc 获取出入库日志列表（不分页）
     * @param w
     * @return
     */
    List<WarehouseGoodsOperateLogVirtual> getWGOlog(WarehouseGoodsOperateLog w);
    /**
     * <AUTHOR>
     * @desc 根据销售单商品id查询出库sn码
     * @param saleorderGoodsId
     * @return
     */
    List<String> querySnBySaleorderGoodsId(Integer saleorderGoodsId);

    List<WarehouseGoodsOperateLogVirtual> getWGOlistByComments(String batChNo);


    List<WarehouseLog> getWarehouseLogDirectById(@Param("idList")List<Integer> idList);

}