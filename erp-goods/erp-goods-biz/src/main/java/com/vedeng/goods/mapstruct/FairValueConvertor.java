package com.vedeng.goods.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.goods.domain.entity.FairValueEntity;
import com.vedeng.goods.dto.FairValueDto;
import com.vedeng.goods.dto.HistoryDataDto;
import com.vedeng.goods.dto.SaleOrderDataDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/13 16:45
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface FairValueConvertor extends BaseMapStruct<FairValueEntity, FairValueDto> {

    /**
     * entity 转dto
     *
     * @param entity FairValueEntity
     * @return FairValueDto
     */
    @Override
    @Mapping(target = "historyDataDtoList", source = "historyData", qualifiedByName = "jsonArrayToHistoryDataDto")
    @Mapping(target = "saleOrderDataDtoList", source = "saleOrderData", qualifiedByName = "jsonArrayToSaleOrderDataDto")
    FairValueDto toDto(FairValueEntity entity);

    /**
     * dto 转 entity
     *
     * @param dto FairValueDto
     * @return FairValueEntity
     */
    @Override
    @Mapping(target = "historyData", source = "historyDataDtoList", qualifiedByName = "historyDataDtoToJsonArray")
    @Mapping(target = "saleOrderData", source = "saleOrderDataDtoList", qualifiedByName = "saleOrderDataDtoToJsonArray")
    FairValueEntity toEntity(FairValueDto dto);


    /**
     * entity 中JSONArray 转 原对象
     *
     * @param source JSONArray
     * @return List<HistoryDataDto>
     */
    @Named("jsonArrayToHistoryDataDto")
    default List<HistoryDataDto> jsonArrayToHistoryDataDto(JSONArray source) {
        if (CollUtil.isEmpty(source)) {
            return Collections.emptyList();
        }
        return source.toJavaList(HistoryDataDto.class);
    }

    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("historyDataDtoToJsonArray")
    default JSONArray historyDataDtoToJsonArray(List<HistoryDataDto> source) {
        if (CollUtil.isEmpty(source)) {
            return new JSONArray();
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }

    /**
     * entity 中JSONArray 转 原对象
     *
     * @param source JSONArray
     * @return List<SaleOrderDataDto>
     */
    @Named("jsonArrayToSaleOrderDataDto")
    default List<SaleOrderDataDto> jsonArrayToSaleOrderDataDto(JSONArray source) {
        if (CollUtil.isEmpty(source)) {
            return Collections.emptyList();
        }
        return source.toJavaList(SaleOrderDataDto.class);
    }

    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("saleOrderDataDtoToJsonArray")
    default JSONArray saleOrderDataDtoToJsonArray(List<SaleOrderDataDto> source) {
        if (CollUtil.isEmpty(source)) {
            return new JSONArray();
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
}