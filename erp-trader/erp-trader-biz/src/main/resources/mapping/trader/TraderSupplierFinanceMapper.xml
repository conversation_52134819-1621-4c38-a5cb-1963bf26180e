<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderSupplierFinanceMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderSupplierFinance">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_SUPPLIER_FINANCE-->
    <id column="TRADER_SUPPLIER_FINANCE_ID" jdbcType="INTEGER" property="traderSupplierFinanceId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_TYPE" jdbcType="BOOLEAN" property="traderType" />
    <result column="IS_PUSH" jdbcType="BOOLEAN" property="isPush" />
    <result column="PUSH_TIME" jdbcType="TIMESTAMP" property="pushTime" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_SUPPLIER_FINANCE_ID, TRADER_ID, TRADER_TYPE, IS_PUSH, PUSH_TIME, IS_DELETE, 
    ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, REMARK, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_SUPPLIER_FINANCE
    where TRADER_SUPPLIER_FINANCE_ID = #{traderSupplierFinanceId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER_SUPPLIER_FINANCE
    where TRADER_SUPPLIER_FINANCE_ID = #{traderSupplierFinanceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_SUPPLIER_FINANCE_ID" keyProperty="traderSupplierFinanceId" parameterType="com.vedeng.erp.trader.domain.entity.TraderSupplierFinance" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_SUPPLIER_FINANCE (TRADER_ID, TRADER_TYPE, IS_PUSH, 
      PUSH_TIME, IS_DELETE, ADD_TIME, 
      CREATOR, CREATOR_NAME, MOD_TIME, 
      UPDATER, UPDATER_NAME, REMARK, 
      UPDATE_REMARK)
    values (#{traderId,jdbcType=INTEGER}, #{traderType,jdbcType=BOOLEAN}, #{isPush,jdbcType=BOOLEAN}, 
      #{pushTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=BOOLEAN}, #{addTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_SUPPLIER_FINANCE_ID" keyProperty="traderSupplierFinanceId" parameterType="com.vedeng.erp.trader.domain.entity.TraderSupplierFinance" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_SUPPLIER_FINANCE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderType != null">
        TRADER_TYPE,
      </if>
      <if test="isPush != null">
        IS_PUSH,
      </if>
      <if test="pushTime != null">
        PUSH_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null">
        #{traderType,jdbcType=BOOLEAN},
      </if>
      <if test="isPush != null">
        #{isPush,jdbcType=BOOLEAN},
      </if>
      <if test="pushTime != null">
        #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderSupplierFinance">
    <!--@mbg.generated-->
    update T_TRADER_SUPPLIER_FINANCE
    <set>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null">
        TRADER_TYPE = #{traderType,jdbcType=BOOLEAN},
      </if>
      <if test="isPush != null">
        IS_PUSH = #{isPush,jdbcType=BOOLEAN},
      </if>
      <if test="pushTime != null">
        PUSH_TIME = #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where TRADER_SUPPLIER_FINANCE_ID = #{traderSupplierFinanceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderSupplierFinance">
    <!--@mbg.generated-->
    update T_TRADER_SUPPLIER_FINANCE
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_TYPE = #{traderType,jdbcType=BOOLEAN},
      IS_PUSH = #{isPush,jdbcType=BOOLEAN},
      PUSH_TIME = #{pushTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where TRADER_SUPPLIER_FINANCE_ID = #{traderSupplierFinanceId,jdbcType=INTEGER}
  </update>


  <select id="getTraderSupplierFinancialById"
          resultType="com.vedeng.erp.trader.domain.dto.TraderSupplierFinanceDetail">
    select a.TRADER_SUPPLIER_FINANCE_ID,
           a.TRADER_ID,
           b.TRADER_NAME,
           a.TRADER_TYPE,
           c.USER_ID,
           d.USERNAME
    from T_TRADER_SUPPLIER_FINANCE a
           left join T_TRADER b on a.TRADER_ID = b.TRADER_ID
           left join T_R_TRADER_J_USER c on c.TRADER_ID = a.TRADER_ID and c.TRADER_TYPE = 2
           LEFT JOIN T_USER d ON c.USER_ID = d.USER_ID
    where 1 = 1
    and a.TRADER_SUPPLIER_FINANCE_ID = #{traderCustomerFinanceId,jdbcType=INTEGER}
    limit 1
  </select>

  <select id="getTraderSupplierFinancialByTraderId" resultMap="BaseResultMap">
    select a.TRADER_SUPPLIER_FINANCE_ID,
           a.TRADER_ID,
           b.TRADER_NAME,
           a.TRADER_TYPE,
           c.USER_ID,
           d.USERNAME
    from T_TRADER_SUPPLIER_FINANCE a
           left join T_TRADER b on a.TRADER_ID = b.TRADER_ID
           left join T_R_TRADER_J_USER c on c.TRADER_ID = a.TRADER_ID and c.TRADER_TYPE = 2
           LEFT JOIN T_USER d ON c.USER_ID = d.USER_ID
    where 1 = 1
    and a.TRADER_ID = #{traderId,jdbcType=INTEGER}
    limit 1
  </select>


  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_TRADER_SUPPLIER_FINANCE
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderId != null">
            when TRADER_SUPPLIER_FINANCE_ID = #{item.traderSupplierFinanceId,jdbcType=INTEGER} then #{item.traderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderType != null">
            when TRADER_SUPPLIER_FINANCE_ID = #{item.traderSupplierFinanceId,jdbcType=INTEGER} then #{item.traderType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when TRADER_SUPPLIER_FINANCE_ID = #{item.traderSupplierFinanceId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when TRADER_SUPPLIER_FINANCE_ID = #{item.traderSupplierFinanceId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when TRADER_SUPPLIER_FINANCE_ID = #{item.traderSupplierFinanceId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>

      <trim prefix="IS_PUSH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isPush != null">
            when TRADER_SUPPLIER_FINANCE_ID = #{item.traderSupplierFinanceId,jdbcType=INTEGER} then #{item.isPush,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>

    </trim>
    where TRADER_SUPPLIER_FINANCE_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.traderSupplierFinanceId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="TRADER_SUPPLIER_FINANCE_ID" keyProperty="traderSupplierFinanceId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_SUPPLIER_FINANCE
    (TRADER_ID, TRADER_TYPE, IS_PUSH, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME,
    MOD_TIME, UPDATER, UPDATER_NAME, REMARK, UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.traderId,jdbcType=INTEGER}, #{item.traderType,jdbcType=BOOLEAN}, #{item.isPush,jdbcType=BOOLEAN}, #{item.isDelete,jdbcType=BOOLEAN}, #{item.addTime,jdbcType=TIMESTAMP},
      #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.modTime,jdbcType=TIMESTAMP},
      #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
      #{item.updateRemark,jdbcType=VARCHAR})
    </foreach>
  </insert>


  <select id="selectByTraderIds" resultType="java.lang.Integer" parameterType="java.util.List">
     SELECT TRADER_ID FROM T_TRADER_SUPPLIER_FINANCE WHERE IS_DELETE=0 AND TRADER_ID
     IN
     <foreach close=")" collection="list" item="traderId" open="(" separator=", ">
      #{traderId,jdbcType=INTEGER}
    </foreach>
  </select>

  <insert id="batchInsertSelect" keyColumn="TRADER_SUPPLIER_FINANCE_ID" keyProperty="traderCustomerFinanceId" parameterType="java.util.List" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert IGNORE into T_TRADER_SUPPLIER_FINANCE
    (TRADER_ID)
    values
    <foreach collection="list" item="traderId" separator=",">
      (#{traderId,jdbcType=INTEGER})
    </foreach>
  </insert>

</mapper>