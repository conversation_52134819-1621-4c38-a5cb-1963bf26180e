<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="商品分类" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/categoryList.css?rnd=${resourceVersionKey}" />

<div class="main-container">
    <div class='form-blanks'>
        <input type="text" id="sousuo" style="margin-left: 5px;width: 350px;margin-bottom: 5px" class="input-middle" name="keyWords" placeholder="请输入产品名称/品牌/型号等关键词">
        <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="searchCategory(${thirdCatrgoryId});" id="searchSpan">搜索</span>
    </div>


    <input type="hidden" name="firstCatrgoryId" class="firstCatrgoryId">
    <input type="hidden" name="secondCatrgoryId" class="secondCatrgoryId">
    <input type="hidden" name="thirdCatrgoryId" class="thirdCatrgoryId" value="${thirdCatrgoryId}">
    <div style="float: left;width: 200px;height: 350px;overflow:scroll" class="ul">
        <c:forEach items="${firstCatrgory}" var="fCategory">
            <ul style="cursor:pointer">
                <li class="first" id="${fCategory.baseCategoryId}" onclick="choiceSecondCategory(${fCategory.baseCategoryId})">${fCategory.baseCategoryName}</li>
            </ul>
        </c:forEach>

    </div>

    <div style="float: left;width: 200px;height: 350px;overflow:scroll" class="ul">
        <ul class="secondCategory" style="cursor:pointer">

        </ul>

    </div>
    <div class="clear"></div>
    <div class="add-tijiao tcenter" style="margin-top: 5px">
        <button style="background-color: gainsboro" class="confirmButton" id="button_id"  disabled="true" onclick="qd()">确定</button>
        <button class="dele" id="close-layer" type="button" >取消</button>
        <div class="title-click nobor  pop-new-data" id="popEngineer"></div>
    </div>

</div>


<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/pages/goodsdribute/categoryList.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>
