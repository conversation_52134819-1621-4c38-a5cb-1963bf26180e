package com.vedeng.erp.kingdee.batch.processor;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.enums.InvoiceBelongTypeEnum;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchRollbackInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchRollbackInvoiceProcessDto;
import com.vedeng.erp.kingdee.batch.repository.BatchRollbackInvoiceDtoMapper;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 蓝字发票回滚批处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchRollbackVirtualInvoiceProcessor extends BaseProcessor<BatchRollbackInvoiceDto, BatchRollbackInvoiceProcessDto> {

    @Resource
    private BatchRollbackInvoiceDtoMapper batchRollbackInvoiceDtoMapper;


    @Override
    public BatchRollbackInvoiceProcessDto doProcess(BatchRollbackInvoiceDto batchRollbackInvoiceDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("蓝字发票回滚批处理 batchWarehouseGoodsOutInDto:{}", JSON.toJSONString(batchRollbackInvoiceDto));

        boolean isSpecialInvoice = InvoiceTaxTypeEnum.getIsSpecialInvoice(batchRollbackInvoiceDto.getInvoiceType());

        String ids = "";
        String formId = "";
        ids = isSpecialInvoice ? batchRollbackInvoiceDtoMapper.getBuyOrderSpecialInvoiceFid(batchRollbackInvoiceDto.getUuid()) :
                batchRollbackInvoiceDtoMapper.getBuyOrderPlainInvoiceFid(batchRollbackInvoiceDto.getInvoiceId().toString());
        formId = isSpecialInvoice ? KingDeeFormConstant.PURCHASE_VAT_SPECIAL_INVOICE : KingDeeFormConstant.PURCHASE_VAT_PLAIN_INVOICE;


        if (StringUtils.isEmpty(ids)) {
            log.warn("蓝字发票回滚批处理原蓝字发票信息内码信息异常 batchWarehouseGoodsOutInDto:{}", JSON.toJSONString(batchRollbackInvoiceDto));
            throw new RuntimeException("蓝字发票回滚批处理原蓝字发票信息内码信息异常");
        }

        if (StringUtils.isEmpty(formId)) {
            log.warn("蓝字发票回滚批处理原蓝字发票信息formId异常 batchWarehouseGoodsOutInDto:{}", JSON.toJSONString(batchRollbackInvoiceDto));
            throw new RuntimeException("蓝字发票回滚批处理原蓝字发票信息formId异常");
        }

        log.info("蓝字发票回滚批处理 invoiceId:{},formId:{},ids:{}", batchRollbackInvoiceDto.getVirtualInvoiceId(), formId, ids);

        return BatchRollbackInvoiceProcessDto.builder().invoiceId(batchRollbackInvoiceDto.getVirtualInvoiceId()).isPay(Boolean.FALSE)
                .formId(formId).ids(ids).orgId(KingDeeConstant.ORG_ID.toString()).build();
    }
}
