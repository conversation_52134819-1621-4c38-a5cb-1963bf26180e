package com.vedeng.logistics.model.outIn;

/**
 * @description:
 * @author: yana.jiang
 * @date: 2022/11/16
 */
public class OutInBaseInfo {
    /**
     * 订单生效时间
     */
    private Long validTime;
    /**
     * 来源 ERP WMS
     */
    private String source;
    private Long warehouseGoodsOutInId;
    /**
     * ERP出入库单号（自动生成）
     */
    private String outInNo;
    /**
     * WMS出入库单号
     */
    private String wmsNo;
    /**
     * 关联单号
     */
    private String relateNo;
    /**
     * 出入库类型 1入库 2出库 3销售换货入库 4销售换货出库 5销售退货入库 6采购退货出库 7采购换货出库 8采购换货入库 9外借入库 10外借出库 11调整盘盈入库 12盘盈入库 13报废出库 14领用出库 15 调整盘亏 16 盘亏出库
     */
    private Integer outInType;

    /**
     * 物流公司
     */
    private String outInCompany;

    /**
     * 单据归属人
     */
    private String owner;

    /**
     * 收货状态0未收货 1部分收货 2全部收货
     */
    private Integer arrivalStatus;
    /**
     * FREIGHT_DESCRIPTION 运费说明
     */
    private String freightDescription;
    /**
     * LOGISTICS_COMMENTS 物流备注
     */
    private String logisticsComments;
    /**
     * 附加条款ADDITIONAL_CLAUSE
     */
    private String additionalClause;

    private Integer buyorderId;

    private Integer traderId;
    /**
     * 供应商名称
     */
    private String traderName;
    /**
     * 供应商详细地址
     */
    private String traderAddressDetail;



    private Integer isGift;

    /**
     * 入库时间
     */
    private String outInTime;
    /**
     * 是否虚拟0否1是
     */
    private Integer isVirture;
    /**
     * 更新备注
     */
    private String updateRemark;


    public Long getValidTime() {
        return validTime;
    }

    public void setValidTime(Long validTime) {
        this.validTime = validTime;
    }

    public Integer getIsGift() {
        return isGift;
    }

    public void setIsGift(Integer isGift) {
        this.isGift = isGift;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public String getTraderAddressDetail() {
        return traderAddressDetail;
    }

    public void setTraderAddressDetail(String traderAddressDetail) {
        this.traderAddressDetail = traderAddressDetail;
    }

    public String getOutInNo() {
        return outInNo;
    }

    public void setOutInNo(String outInNo) {
        this.outInNo = outInNo;
    }

    public String getWmsNo() {
        return wmsNo;
    }

    public void setWmsNo(String wmsNo) {
        this.wmsNo = wmsNo;
    }

    public String getRelateNo() {
        return relateNo;
    }

    public void setRelateNo(String relateNo) {
        this.relateNo = relateNo;
    }

    public Integer getOutInType() {
        return outInType;
    }

    public void setOutInType(Integer outInType) {
        this.outInType = outInType;
    }

    public String getOutInCompany() {
        return outInCompany;
    }

    public void setOutInCompany(String outInCompany) {
        this.outInCompany = outInCompany;
    }

    public Integer getArrivalStatus() {
        return arrivalStatus;
    }

    public void setArrivalStatus(Integer arrivalStatus) {
        this.arrivalStatus = arrivalStatus;
    }

    public String getFreightDescription() {
        return freightDescription;
    }

    public void setFreightDescription(String freightDescription) {
        this.freightDescription = freightDescription;
    }

    public String getLogisticsComments() {
        return logisticsComments;
    }

    public void setLogisticsComments(String logisticsComments) {
        this.logisticsComments = logisticsComments;
    }

    public String getAdditionalClause() {
        return additionalClause;
    }

    public void setAdditionalClause(String additionalClause) {
        this.additionalClause = additionalClause;
    }

    public Long getWarehouseGoodsOutInId() {
        return warehouseGoodsOutInId;
    }

    public void setWarehouseGoodsOutInId(Long warehouseGoodsOutInId) {
        this.warehouseGoodsOutInId = warehouseGoodsOutInId;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public Integer getBuyorderId() {
        return buyorderId;
    }

    public void setBuyorderId(Integer buyorderId) {
        this.buyorderId = buyorderId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getOutInTime() {
        return outInTime;
    }

    public void setOutInTime(String outInTime) {
        this.outInTime = outInTime;
    }

    public Integer getIsVirture() {
        return isVirture;
    }

    public void setIsVirture(Integer isVirture) {
        this.isVirture = isVirture;
    }


    public String getUpdateRemark() {
        return updateRemark;
    }

    public void setUpdateRemark(String updateRemark) {
        this.updateRemark = updateRemark;
    }
}
