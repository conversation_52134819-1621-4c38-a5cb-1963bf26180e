package com.vedeng.order.chain;

/**
 * <AUTHOR>
 * @ClassName RiskHandlerKeyConsta.java
 * @Description TODO 风控常量
 * @createTime 2020年12月12日 11:15:00
 */
public class RiskHandlerKeyConstant {

    public static final String RISK_MODEL = "riskModel";

    public static final String TRADER_ID = "traderId";

    public static final String SKU = "sku";

    public static final String IGNORE = "ignore";

    public static final Integer IS_SEND = 1;

    public static final Integer NO_SEND = 0;

    public static final Integer DEFAULT_RISK = 0;
    //销售被风控
    public static final Integer SALE_RISK = 1;
    //报价被风控
    public static final Integer QUOTE_RISK = 2;
    //销售解除风控
    public static final Integer FINISH_RISK = 3;

    public static final Integer NO_RISK = 999;

}
