package com.vedeng.kpi.service.impl;

import com.vedeng.kpi.cache.KpiLoadingCache;
import com.vedeng.kpi.model.DO.KpiDailyCountDo;
import com.vedeng.kpi.model.DO.KpiOrderLogDo;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiUserConfigDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;
import com.vedeng.kpi.model.VO.KpiBaseInfoVo;
import com.vedeng.kpi.model.base.KpiBaseParams;
import com.vedeng.kpi.service.KpiParamTransService;
import com.vedeng.kpi.share.KpiUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class KpiParamTransServiceImpl implements KpiParamTransService {

    @Autowired
    KpiLoadingCache kpiLoadingCache;

    public KpiDailyCountDo baseParamsToCount(KpiUserInfoDto source ){
        KpiDailyCountDo target = new KpiDailyCountDo();
        target.setCompanyId(1);
        target.setUserId(source.getUserId());
        target.setGroupId(source.getGroupId());
        target.setKpiDate(KpiUtils.getDateStart(source.getKpiDateEnd()));
        target.setTeamId(source.getTeamId());
        return target;
    }

    public KpiDataQueryDto baseParamsToQuery(KpiUserInfoDto source){
        KpiDataQueryDto target = new KpiDataQueryDto();
        KpiUserConfigDto userConfig = kpiLoadingCache.getUserConfig(source.getUserId());
        source.setGroupId(userConfig.getGroupId());
        source.setGroupName(userConfig.getGroupName());
        source.setTeamId(userConfig.getTeamId());
        source.setTeamName(userConfig.getTeamName());
        target.setCompanyId(1);
        target.setUserId(source.getUserId());
        target.setGroupId(userConfig.getGroupId());
        target.setKpiDate(KpiUtils.getDateStart(source.getKpiDateEnd()));
        target.setTeamId(userConfig.getTeamId());
        target.setKpiDateStart(KpiUtils.getDateStart(source.getKpiDateStart()));
        target.setKpiDateEnd(KpiUtils.getDateStart(source.getKpiDateEnd()));
        return target;
    }

    public KpiUserInfoDto logToUserInfo(KpiOrderLogDo source, Date updateKpiDate){
        KpiUserInfoDto target = new KpiUserInfoDto();
        target.setId(source.getId());
        target.setUserName(kpiLoadingCache.getUserConfig(source.getUserId()).getUserName());
        target.setGroupName(kpiLoadingCache.getUserConfig(source.getUserId()).getGroupName());
        target.setTeamName(kpiLoadingCache.getUserConfig(source.getUserId()).getTeamName());
        target.setGroupId(kpiLoadingCache.getUserConfig(source.getUserId()).getGroupId());
        target.setUserId(source.getUserId());
        target.setKpiDate(KpiUtils.getDateStart(updateKpiDate));
        target.setCompanyId(source.getCompanyId());
        target.setKpiDateEnd(KpiUtils.getDateStart(updateKpiDate));
        target.setKpiDateStart(KpiUtils.getMonthStart(updateKpiDate));
        return target;
    }

    /**
     * @description: 获取基本参数.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/17 10:38 上午.
     * @author: Tomcat.Hui.
     * @param base: .
     * @param userId: .
     * @return: com.vedeng.kpi.model.base.KpiBaseParams.
     * @throws: .
     */
    public List<? extends KpiBaseParams> transParams(List<? extends KpiBaseParams> baseList){
        baseList.stream().map(base -> this.getCurrentUserInfo(base)).collect(Collectors.toList());
        return baseList;
    }

    @Override
    public KpiBaseParams getCurrentUserInfo(KpiBaseParams userInfo) {
        KpiUserConfigDto userConfig = kpiLoadingCache.getUserConfig(userInfo.getUserId());
        userInfo.setTeamId(userConfig.getTeamId());
        userInfo.setGroupId(userConfig.getGroupId());
        userInfo.setUserName(userConfig.getUserName());
        userInfo.setGroupName(userConfig.getGroupName());
        userInfo.setTeamName(userConfig.getTeamName());
        return userInfo;
    }

    @Override
    public KpiBaseInfoVo getKpiBaseGroupInfo(Integer userId) {
        KpiBaseInfoVo result = new KpiBaseInfoVo();
        KpiUserConfigDto userConfig = kpiLoadingCache.getUserConfig(userId);
        result.setGroupId(userConfig.getGroupId());
        result.setGroupName(userConfig.getGroupName());
        result.setUserId(userId);
        result.setUserName(userConfig.getUserName());
        result.setTeamId(userConfig.getTeamId());
        result.setTeamName(userConfig.getTeamName());
        return result;
    }

}
