<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wms.unitconversionorder.dao.WmsUnitConversionOrderMapper">
  <resultMap id="BaseResultMap" type="com.wms.unitconversionorder.model.entity.WmsUnitConversionOrder">
    <!--@mbg.generated-->
    <!--@Table T_WMS_UNIT_CONVERSION_ORDER-->
    <id column="WMS_UNIT_CONVERSION_ORDER_ID" jdbcType="INTEGER" property="wmsUnitConversionOrderId" />
    <result column="WMS_UNIT_CONVERSION_ORDER_NO" jdbcType="VARCHAR" property="wmsUnitConversionOrderNo" />
    <result column="ORDER_TYPE" jdbcType="INTEGER" property="orderType" />
    <result column="REASON" jdbcType="VARCHAR" property="reason" />
    <result column="VERIFY_STATUS" jdbcType="INTEGER" property="verifyStatus" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
  </resultMap>

  <resultMap id="orderAndItem" type="com.wms.unitconversionorder.model.dto.WmsUnitConversionOrderDto">
    <!--@mbg.generated-->
    <!--@Table T_WMS_UNIT_CONVERSION_ORDER-->
    <id column="WMS_UNIT_CONVERSION_ORDER_ID" jdbcType="INTEGER" property="wmsUnitConversionOrderId" />
    <result column="WMS_UNIT_CONVERSION_ORDER_NO" jdbcType="VARCHAR" property="wmsUnitConversionOrderNo" />
    <result column="ORDER_TYPE" jdbcType="INTEGER" property="orderType" />
    <result column="REASON" jdbcType="VARCHAR" property="reason" />
    <result column="VERIFY_STATUS" jdbcType="INTEGER" property="verifyStatus" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <collection property="wmsUnitConversionOrderItemDtoList" ofType="com.wms.unitconversionorder.model.dto.WmsUnitConversionOrderItemDto">
      <id column="WMS_UNIT_CONVERSION_ORDER_ITEM_ID" jdbcType="INTEGER" property="wmsUnitConversionOrderItemId" />
      <result column="WMS_UNIT_CONVERSION_ORDER_ID" jdbcType="INTEGER" property="wmsUnitConversionOrderId" />
      <result column="SOURCE_SKU_ID" jdbcType="INTEGER" property="sourceSkuId" />
      <result column="SOURCE_SKU_NO" jdbcType="VARCHAR" property="sourceSkuNo" />
      <result column="SOURCE_GOODS_NAME" jdbcType="VARCHAR" property="sourceGoodsName" />
      <result column="SOURCE_UNIT_ID" jdbcType="INTEGER" property="sourceUnitId" />
      <result column="SOURCE_UNIT_NAME" jdbcType="VARCHAR" property="sourceUnitName" />
      <result column="SOURCE_NUM" jdbcType="DECIMAL" property="sourceNum" />
      <result column="SOURCE_PRICE" jdbcType="DECIMAL" property="sourcePrice" />
      <result column="WMS_REAL_OUT_NUM" jdbcType="DECIMAL" property="wmsRealOutNum" />
      <result column="OUT_STATUS" jdbcType="INTEGER" property="outStatus" />
      <result column="TARGET_SKU_ID" jdbcType="INTEGER" property="targetSkuId" />
      <result column="TARGET_SKU_NO" jdbcType="VARCHAR" property="targetSkuNo" />
      <result column="TARGET_GOODS_NAME" jdbcType="VARCHAR" property="targetGoodsName" />
      <result column="TARGET_UNIT_ID" jdbcType="INTEGER" property="targetUnitId" />
      <result column="TARGET_UNIT_NAME" jdbcType="VARCHAR" property="targetUnitName" />
      <result column="TARGET_NUM" jdbcType="DECIMAL" property="targetNum" />
      <result column="TARGET_PRICE" jdbcType="DECIMAL" property="targetPrice" />
      <result column="WMS_REAL_IN_NUM" jdbcType="DECIMAL" property="wmsRealInNum" />
      <result column="IN_STATUS" jdbcType="INTEGER" property="inStatus" />
      <result column="TAX_RATE" jdbcType="INTEGER" property="taxRate" />
      <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
      <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
      <result column="CREATOR" jdbcType="INTEGER" property="creator" />
      <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
      <result column="UPDATER" jdbcType="INTEGER" property="updater" />
      <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
      <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    </collection>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    WMS_UNIT_CONVERSION_ORDER_ID, WMS_UNIT_CONVERSION_ORDER_NO, ORDER_TYPE, REASON, VERIFY_STATUS, 
    ORG_ID, ORG_NAME, COMMENTS, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, UPDATER, 
    UPDATER_NAME, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_WMS_UNIT_CONVERSION_ORDER
    where WMS_UNIT_CONVERSION_ORDER_ID = #{wmsUnitConversionOrderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_WMS_UNIT_CONVERSION_ORDER
    where WMS_UNIT_CONVERSION_ORDER_ID = #{wmsUnitConversionOrderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="WMS_UNIT_CONVERSION_ORDER_ID" keyProperty="wmsUnitConversionOrderId" parameterType="com.wms.unitconversionorder.model.entity.WmsUnitConversionOrder" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_UNIT_CONVERSION_ORDER (WMS_UNIT_CONVERSION_ORDER_NO, ORDER_TYPE, 
      REASON, VERIFY_STATUS, ORG_ID, 
      ORG_NAME, COMMENTS, IS_DELETE, 
      ADD_TIME, CREATOR, CREATOR_NAME, 
      UPDATER, UPDATER_NAME, MOD_TIME
      )
    values (#{wmsUnitConversionOrderNo,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER}, 
      #{reason,jdbcType=VARCHAR}, #{verifyStatus,jdbcType=INTEGER}, #{orgId,jdbcType=INTEGER}, 
      #{orgName,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="WMS_UNIT_CONVERSION_ORDER_ID" keyProperty="wmsUnitConversionOrderId" parameterType="com.wms.unitconversionorder.model.entity.WmsUnitConversionOrder" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_UNIT_CONVERSION_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="wmsUnitConversionOrderNo != null">
        WMS_UNIT_CONVERSION_ORDER_NO,
      </if>
      <if test="orderType != null">
        ORDER_TYPE,
      </if>
      <if test="reason != null">
        REASON,
      </if>
      <if test="verifyStatus != null">
        VERIFY_STATUS,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="orgName != null">
        ORG_NAME,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="wmsUnitConversionOrderNo != null">
        #{wmsUnitConversionOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="verifyStatus != null">
        #{verifyStatus,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wms.unitconversionorder.model.entity.WmsUnitConversionOrder">
    <!--@mbg.generated-->
    update T_WMS_UNIT_CONVERSION_ORDER
    <set>
      <if test="wmsUnitConversionOrderNo != null">
        WMS_UNIT_CONVERSION_ORDER_NO = #{wmsUnitConversionOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        ORDER_TYPE = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        REASON = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="verifyStatus != null">
        VERIFY_STATUS = #{verifyStatus,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        ORG_NAME = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where WMS_UNIT_CONVERSION_ORDER_ID = #{wmsUnitConversionOrderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wms.unitconversionorder.model.entity.WmsUnitConversionOrder">
    <!--@mbg.generated-->
    update T_WMS_UNIT_CONVERSION_ORDER
    set WMS_UNIT_CONVERSION_ORDER_NO = #{wmsUnitConversionOrderNo,jdbcType=VARCHAR},
      ORDER_TYPE = #{orderType,jdbcType=INTEGER},
      REASON = #{reason,jdbcType=VARCHAR},
      VERIFY_STATUS = #{verifyStatus,jdbcType=INTEGER},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      ORG_NAME = #{orgName,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
    where WMS_UNIT_CONVERSION_ORDER_ID = #{wmsUnitConversionOrderId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="WMS_UNIT_CONVERSION_ORDER_ID" keyProperty="wmsUnitConversionOrderId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_UNIT_CONVERSION_ORDER
    (WMS_UNIT_CONVERSION_ORDER_NO, ORDER_TYPE, REASON, VERIFY_STATUS, ORG_ID, ORG_NAME, 
      COMMENTS, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, MOD_TIME
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.wmsUnitConversionOrderNo,jdbcType=VARCHAR}, #{item.orderType,jdbcType=INTEGER}, 
        #{item.reason,jdbcType=VARCHAR}, #{item.verifyStatus,jdbcType=INTEGER}, #{item.orgId,jdbcType=INTEGER}, 
        #{item.orgName,jdbcType=VARCHAR}, #{item.comments,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=INTEGER}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR}, #{item.modTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>

  <select id="selectByWmsUnitConversionOrderId" resultMap="orderAndItem">
    select TWUCO.WMS_UNIT_CONVERSION_ORDER_ID, TWUCO.WMS_UNIT_CONVERSION_ORDER_NO, TWUCO.ORDER_TYPE, TWUCO.REASON,
    TWUCO.VERIFY_STATUS, TWUCO.ORG_ID, TWUCO.ORG_NAME, TWUCO.COMMENTS, TWUCO.IS_DELETE, TWUCO.ADD_TIME, TWUCO.CREATOR,
    TWUCO.CREATOR_NAME, TWUCO.UPDATER, TWUCO.UPDATER_NAME, TWUCO.MOD_TIME,
    TWUCOI.WMS_UNIT_CONVERSION_ORDER_ITEM_ID, TWUCOI.WMS_UNIT_CONVERSION_ORDER_ID, TWUCOI.SOURCE_SKU_ID,
    TWUCOI.SOURCE_SKU_NO, TWUCOI.SOURCE_GOODS_NAME, TWUCOI.SOURCE_UNIT_ID, TWUCOI.SOURCE_UNIT_NAME, TWUCOI.SOURCE_NUM,
    TWUCOI.SOURCE_PRICE, TWUCOI.WMS_REAL_OUT_NUM, TWUCOI.OUT_STATUS, TWUCOI.TARGET_SKU_ID, TWUCOI.TARGET_SKU_NO,
    TWUCOI.TARGET_GOODS_NAME, TWUCOI.TARGET_UNIT_ID, TWUCOI.TARGET_UNIT_NAME, TWUCOI.TARGET_NUM, TWUCOI.TARGET_PRICE,
    TWUCOI.WMS_REAL_IN_NUM, TWUCOI.IN_STATUS, TWUCOI.TAX_RATE, TWUCOI.IS_DELETE, TWUCOI.ADD_TIME, TWUCOI.CREATOR,
    TWUCOI.CREATOR_NAME, TWUCOI.UPDATER, TWUCOI.UPDATER_NAME, TWUCOI.MOD_TIME
    from T_WMS_UNIT_CONVERSION_ORDER TWUCO
    left join T_WMS_UNIT_CONVERSION_ORDER_ITEM TWUCOI on
    TWUCO.WMS_UNIT_CONVERSION_ORDER_ID =TWUCOI.WMS_UNIT_CONVERSION_ORDER_ID
    where TWUCO.WMS_UNIT_CONVERSION_ORDER_ID = #{wmsUnitConversionOrderId}
    and TWUCOI.IS_DELETE = 0

  </select>

<!--auto generated by MybatisCodeHelper on 2023-02-20-->
  <select id="selectByWmsUnitConversionOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_WMS_UNIT_CONVERSION_ORDER
    where WMS_UNIT_CONVERSION_ORDER_NO=#{wmsUnitConversionOrderNo,jdbcType=VARCHAR}
  </select>
</mapper>