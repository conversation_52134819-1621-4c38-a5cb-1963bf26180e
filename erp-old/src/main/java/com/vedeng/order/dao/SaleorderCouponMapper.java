package com.vedeng.order.dao;

import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderCoupon;
import org.apache.ibatis.annotations.Param;

public interface SaleorderCouponMapper {

    int insertSelective(SaleorderCoupon record);

    SaleorderCoupon selectByPrimaryKey(Integer saleorderCouponId);

    SaleorderCoupon selectBySaleOrderId(Integer saleorderId);

    int updateByPrimaryKeySelective(SaleorderCoupon record);

    void deleteBySaleOrderId(@Param("saleorderId") Integer saleorderId);

    Saleorder selectByCouponCode(@Param("couponCode") Long couponCode);


    /**
     * 根据订单ID查询优惠券
     * @param saleorderId
     * @return
     */
    SaleorderCoupon selectBySaleorderId(@Param("saleorderId")Integer saleorderId);
}
