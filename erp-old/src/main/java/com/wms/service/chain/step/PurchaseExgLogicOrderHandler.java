package com.wms.service.chain.step;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.constant.ErpConst;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dto.ExgOrderGoodDto;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.service.context.ThreadLocalContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 新增逻辑订单数据处理器
 */
@Service
public class PurchaseExgLogicOrderHandler extends AbstractHandlerStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseExgLogicOrderHandler.class);

    @Resource
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        AfterSalesVo afterSalesInfo = context.get("afterSalesInfo");

        List<AfterSalesGoodsVo> afterSalesGoodList =
                this.afterSalesGoodsMapper.getAfterSalesGoodList(afterSalesInfo.getAfterSalesId());

        List<ExgOrderGoodDto> exgOrderGoodList = ThreadLocalContext.get("orderGoodList");

        exgOrderGoodList.stream().forEach(exgOrderGood -> {

            WmsLogicalOrdergoods wmsLogicalOrdergoods = new WmsLogicalOrdergoods();

            int realateId = afterSalesGoodList.stream()
                    .filter(afterSalesGood -> {
                        //追加限制普发
                        return afterSalesGood.getSku().equals(exgOrderGood.getSKU()) && ErpConst.ZERO.equals(afterSalesGood.getDeliveryDirect());
                    }).map(afterSalesGood -> afterSalesGood.getAfterSalesGoodsId())
                    .findFirst()
                    .orElse(null);

            wmsLogicalOrdergoods.setRelatedId(realateId);

            //采购换货出库
            wmsLogicalOrdergoods.setOperateType(WmsLogicalOperateTypeEnum.PURCHASE_EXG.getOperateTypeCode());

            wmsLogicalOrdergoods.setSku(exgOrderGood.getSKU());
            wmsLogicalOrdergoods.setGoodsId(Integer.valueOf(exgOrderGood.getSKU().substring(1)));
            wmsLogicalOrdergoods.setNum(exgOrderGood.getQty().intValue());
            wmsLogicalOrdergoods.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(exgOrderGood.getLotAtt08()));
            wmsLogicalOrdergoods.setOccupyNum(exgOrderGood.getQty().intValue());
            wmsLogicalOrdergoods.setAddTime(new Date());
            wmsLogicalOrdergoods.setModeTime(new Date());

            LOGGER.info("换货单新增逻辑订单数据start==================,参数:" + JSON.toJSONString(wmsLogicalOrdergoods));

            wmsLogicalOrdergoodsMapper.insertSelective(wmsLogicalOrdergoods);
        });
    }


}
