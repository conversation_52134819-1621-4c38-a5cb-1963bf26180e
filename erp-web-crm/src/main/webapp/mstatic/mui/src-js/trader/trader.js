/* 客户名称 */

Vue.component('ui-trader', {
    template: `
        <div class="ui-trader-name">
            <!-- 客户名称回显 -->
            <div class="trader-show-wrap">
                <ui-placeholder v-if="!inputValue" @click="openDialog">{{ placeholder }}</ui-placeholder>
                <div v-else class="trader-show">
                    <div class="input-value" @click="openDialog">{{ inputValue }}</div>
                    <template v-if="chooseTrader.traderId">
                        <template v-if="needTraderStatus">
                            <template v-if="!chooseTrader.belong">
                                <ui-tip v-if="!chooseTrader.share" margin-bottom="10px">该客户的归属销售是{{chooseTrader.saleName}}，建议您拜访前先与归属销售沟通</ui-tip>
                                <ui-tip type="success" v-else margin-bottom="10px">已选择建档客户</ui-tip>
                            </template>
                            <ui-tip type="success" v-else margin-bottom="10px">已选择建档客户</ui-tip>
                        </template>
                        <div class="other-info" v-if="needTraderDetail">
                            <div class="other-item">交易总额：{{ traderDetail.historyTransactionAmount || '-' }}</div>
                            <div class="other-item">交易次数：{{ traderDetail.historyTransactionNum || '-' }}</div>
                            <div class="other-item">最近下单：{{ traderDetail.lastOrderTime || '-' }}</div>
                        </div>
                    </template>
                    <ui-tip v-else-if="needTraderStatus" margin-bottom="10px">当前录入客户为非建档客户</ui-tip>
                </div>

                <div class="trader-icon" v-if="needTyc && inputValue">
                    <template v-if="chooseTrader.tycFlag != 'Y'">
                        <i class="vd-ui_icon icon-search" @click.stop="handlerSearch"></i>
                    </template>
                    <i class="vd-ui_icon icon-tianyancha" v-else @click.stop="showDetail"></i>
                </div>
            </div>

            <!-- erp联想 -->
            <ui-erp-search 
                ref="traderList" 
                v-$attrs 
                @change="handlerTrader"
            ></ui-erp-search>

            <!-- 天眼查列表 -->
            <ui-tyc-list ref="tycList" @change="handlerChangeTyc"></ui-tyc-list>

            <!-- 天眼查详情 -->
            <ui-tyc-detail ref="tycDetail"></ui-tyc-detail>
        </div>
    `,
    props: {
        value: {
            type: String,
            default: ''
        },
        traderInfo: {
            type: Object,
            default: () => ({})
        },
        // 客户交易信息-回显
        traderDetailInfo: {
            type: Object,
            default: () => ({})
        },

        // traderId: {
        //     type: [String, Number],
        // },
        // tycFlag: String,
        // saleName: String,
        // belong: '', // 客户·归属销售
        // share: '', // 客户·是否分享给当前登录人

        // 是否启用天眼查
        needTyc: {
            type: Boolean,
            default: true
        },
        // 是否展示客户建档状态
        needTraderStatus: {
            type: Boolean,
            default: true
        },
        // 是否展示客户交易信息
        needTraderDetail: {
            type: Boolean,
            default: true
        },
        // 选项中 启用禁用
        needDisable: {
            type: Boolean,
            default: false
        },

        placeholder: {
            type: String,
            default: ''
        },
        width: {
            type: String,
            default: '300px'
        },
        maxlength: {
            type: [Number, String],
            default: 1000,
        },
    },
    data() {
        return {
            inputValue: this.value || '',
            // 建档客户-基本信息
            chooseTrader: {
                traderId: '',
                tycFlag: 'N', // Y:天眼查客户  N:不是天眼查客户 
                saleName: '',
                belong: '',
                share: '',
            },
            // 建档客户-交易信息
            traderDetail: {},
            // 天眼查公司信息
            tycInfo: null,
        };
    },
    watch: {
        value (newV) {
            this.inputValue = newV;
        },
        traderInfo: {
            handler (newV) {
                this.chooseTrader = newV;
                if (this.chooseTrader.traderId && this.needTraderDetail) {
                    this.getTraderDetail();
                }
            },
            deep: true,
            immediate: true
        },
        traderDetailInfo: {
            handler (newV) {                
                this.traderDetail = newV;
            },
            deep: true,
            immediate: true
        }
    },
    mounted () {
    },
    methods: {
        async getTraderDetail() {
            if (!Object.keys(this.traderDetail)) {
                await this.$axios.post(`/crm/trader/m/queryTraderDetail?traderId=${this.chooseTrader.traderId}`)
                .then(({data}) => {
                    if (data.success) {
                        this.traderDetail = data.data || {};
                    }
                }).catch(err=> {});
            }
        },

        // ERP建档客户查询
        openDialog () {
            this.$refs.traderList.show(this.value, this.chooseTrader, this.traderDetail);
        },

        clearTyc () {
            this.tycInfo = {};
        },
        clearTrader () {
            this.chooseTrader = {
                traderId: '',
                tycFlag: 'N', // Y:天眼查客户  N:不是天眼查客户 
                saleName: '',
            };
            this.traderDetail = {};
        },

        // 确定
        handlerTrader (data) {
            this.clearTyc();
            this.chooseTrader = data.chooseTrader || {};
            this.traderDetail = data.traderDetail || {};
            console.log('chooseTrader:', JSON.parse(JSON.stringify(data.chooseTrader)));
            console.log('traderDetail:', JSON.parse(JSON.stringify(data.traderDetail)));

            let emitData = Object.assign({}, this.chooseTrader, this.traderDetail);
            this.$emit("input", data.traderName); // 修改外层v-model值
            this.$emit('change', emitData);
        },

        // 天眼查列表
        handlerSearch () {
            if (!this.inputValue) {
                this.$message({
                    type: 'warn',
                    message: '请输入公司名称后进行查询'
                })
                return false;
            }
            this.$refs.tycList.show(this.inputValue);
        },
        // 选择·天眼查客户
        handlerChangeTyc (data) {
            this.clearTrader();
            this.inputValue = data.name;
            this.chooseTrader.tycFlag = 'Y';

            let emitData = Object.assign({}, this.chooseTrader, data);
            this.$emit("input", data.name); // 修改外层v-model值
            this.$emit('change', emitData);
        },
        // 天眼查详情
        async showDetail () {
            this.$refs.tycDetail.show(this.inputValue, this.tycInfo);
        },
    }
})