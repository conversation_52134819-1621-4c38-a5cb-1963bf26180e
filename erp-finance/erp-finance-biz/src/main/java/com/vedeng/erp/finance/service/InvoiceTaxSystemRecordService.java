package com.vedeng.erp.finance.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.erp.finance.dto.InvoiceTaxSystemRecordDto;
import com.vedeng.erp.finance.domain.dto.TaxesReturnInfo;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface InvoiceTaxSystemRecordService {


    /**
     * 是否存在
     *
     * @param invoiceTaxSystemRecordDto invoiceTaxSystemRecordDto
     * @return invoiceTaxSystemRecordDto
     */
    InvoiceTaxSystemRecordDto isExist(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto);

    /**
     * 税金系统记录初始化
     *
     * @param invoiceTaxSystemRecordDto invoiceTaxSystemRecordDto
     */
    void init(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto);

    /**
     * 税金系统记录处理成功
     *
     * @param invoiceTaxSystemRecordDto invoiceTaxSystemRecordDto
     */
    void success(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto);

    /**
     * 税金系统记录处理失败
     * 失败重试次数递增
     *
     * @param invoiceTaxSystemRecordDto invoiceTaxSystemRecordDto
     */
    void fail(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto);


    /**
     * 基于税金接口信息初始化税金系统记录
     * @param iTaxesParam 入参
     * @param taxesReturnInfo 返回值
     * @return InvoiceTaxSystemRecordDto InvoiceTaxSystemRecordDto
     */
     InvoiceTaxSystemRecordDto fromTaxesApiInfoInitInvoiceTaxSystemRecord(ITaxesParam iTaxesParam, TaxesReturnInfo taxesReturnInfo);

    /**
     * 获取税金系统调用记录
     * @param invoiceTaxSystemRecordDto invoiceTaxSystemRecordDto
     * @return List<InvoiceTaxSystemRecordEntity>
     */
    List<InvoiceTaxSystemRecordDto> findInvoiceTaxSystemRecordDto(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto);



    /**
     * 查询
     * @param invoiceTaxSystemRecordId
     * @return
     */
    InvoiceTaxSystemRecordDto queryById(Long invoiceTaxSystemRecordId);

    /**
     * 查询下载pdf 失败任务
     * @param page
     * @return PageInfo<InvoiceTaxSystemRecordDto>
     */
    PageInfo<InvoiceTaxSystemRecordDto> queryDownPdfError(Page page);

    /**
     * 查询下载xml
     * @param page
     * @return
     */
    PageInfo<InvoiceTaxSystemRecordDto> queryDownXmlNeedDown(Page page);


    List<String> getPdfUrlsByInvoiceNos(Set<String> invoiceNos);

    List<String> getXmlUrlsByInvoiceNos(Set<String> invoiceNos);
}
