package com.vedeng.erp.aftersale.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesEntity;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesGoodsEntity;
import com.vedeng.erp.aftersale.dto.AfterSaleGoods2ExpenseDto;
import com.vedeng.erp.aftersale.dto.AfterSaleOrder2ExpenseDto;
import com.vedeng.erp.aftersale.mapper.AfterSalesBizMapper;
import com.vedeng.erp.aftersale.mapper.AfterSalesGoodsBizMapper;
import com.vedeng.erp.aftersale.mapstruct.AfterSaleOrder2ExpenseConvertor;
import com.vedeng.erp.aftersale.service.AfterSaleOrder2ExpenseApiService;
import com.vedeng.erp.buyorder.dto.BuyOrderSaleOrderGoodsDetailDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;
import com.vedeng.erp.buyorder.dto.RBuyorderExpenseJSaleorderDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 费用单采销联动 服务类
 * @date 2023/1/5 15:54
 **/
@Service
@Slf4j
public class AfterSaleOrder2ExpenseApiServiceImpl implements AfterSaleOrder2ExpenseApiService {

    @Autowired
    private AfterSalesBizMapper afterSalesBizMapper;

    @Autowired
    private AfterSalesGoodsBizMapper afterSalesGoodsBizMapper;

    @Autowired
    private AfterSaleOrder2ExpenseConvertor afterSaleOrder2ExpenseConvertor;

    @Override
    public AfterSaleOrder2ExpenseDto getAfterSaleOrderExpense(Integer afterSaleId) {

        log.info("getAfterSaleOrderExpense 入参:{}", JSON.toJSONString(afterSaleId));
        AfterSalesEntity afterSalesEntity = afterSalesBizMapper.selectByPrimaryKey(afterSaleId);
        List<AfterSalesGoodsEntity> afterSalesGoodsEntities = afterSalesGoodsBizMapper.selectByAfterSalesIdAndIsVirtual(afterSaleId);
        if (Objects.isNull(afterSalesEntity)) {
            throw new ServiceException("未查询到售后单主表信息");
        }

        AfterSaleOrder2ExpenseDto afterSaleOrder2ExpenseDto = afterSaleOrder2ExpenseConvertor.toDto(afterSalesEntity);
        List<AfterSaleGoods2ExpenseDto> afterSaleGoods2ExpenseDtos = afterSaleOrder2ExpenseConvertor.toDto(afterSalesGoodsEntities);
        afterSaleOrder2ExpenseDto.setAfterSaleGoods2ExpenseDtos(afterSaleGoods2ExpenseDtos);
        log.info("getAfterSaleOrderExpense 查询结果:{}", JSON.toJSONString(afterSaleOrder2ExpenseDto));
        return afterSaleOrder2ExpenseDto;
    }


    @Override
    public List<RBuyorderExpenseJSaleorderDto> saleorderAfterSaleNum(List<BuyOrderSaleOrderGoodsDetailDto> saleOrderGoodsDetailDtos) {
        return afterSalesGoodsBizMapper.expSaleorderAfterNum(saleOrderGoodsDetailDtos);
    }
}
