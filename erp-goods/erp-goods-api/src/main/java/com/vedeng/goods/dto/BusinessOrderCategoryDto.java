package com.vedeng.goods.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 业务单据与商品分类关联DTO
 */
@Getter
@Setter
public class BusinessOrderCategoryDto extends BaseDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 商机/线索ID
     */
    private Integer businessId;

    /**
     * 业务类型 0线索 1商机
     */
    private Integer businessType;

    /**
     * 商品分类ID
     */
    private Integer categoryId;

    /**
     * 关键词
     */
    private String keywords;

} 