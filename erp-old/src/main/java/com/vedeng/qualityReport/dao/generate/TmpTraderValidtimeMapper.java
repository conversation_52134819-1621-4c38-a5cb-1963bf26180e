package com.vedeng.qualityReport.dao.generate;

import com.vedeng.qualityReport.model.generate.TmpTraderValidtimeDo;

public interface TmpTraderValidtimeMapper {
    int deleteByPrimaryKey(Integer tmpTraderSupplierValidId);

    int insert(TmpTraderValidtimeDo record);

    int insertSelective(TmpTraderValidtimeDo record);

    TmpTraderValidtimeDo selectByPrimaryKey(Integer tmpTraderSupplierValidId);

    int updateByPrimaryKeySelective(TmpTraderValidtimeDo record);

    int updateByPrimaryKey(TmpTraderValidtimeDo record);
}