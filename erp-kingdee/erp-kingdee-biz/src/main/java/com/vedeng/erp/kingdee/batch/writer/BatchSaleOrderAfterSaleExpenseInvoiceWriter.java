package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.kingdee.batch.common.exception.BatchException;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.domain.command.KingDeeOutPutFeePlainInvoiceCommand;
import com.vedeng.erp.kingdee.domain.command.KingDeeOutPutFeeSpecialInvoiceCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeOutPutFeePlainInvoiceEntity;
import com.vedeng.erp.kingdee.domain.entity.KingDeeOutPutFeeSpecialInvoiceEntity;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.mapstruct.*;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeOutPutFeePlainInvoiceMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeOutPutFeeSpecialInvoiceMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售负向发票推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchSaleOrderAfterSaleExpenseInvoiceWriter extends BaseWriter<KingDeeRedInvoiceDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;


    @Autowired
    private KingDeeOutPutFeeSpecialInvoiceCommandConvertor specialCommandConvertor;
    @Autowired
    private KingDeeOutPutFeePlainInvoiceCommandConvertor plainCommandConvertor;
    @Autowired
    private KingDeeOutPutFeeSpecialInvoiceMapper specialInvoiceMapper;
    @Autowired
    private KingDeeOutPutFeeSpecialInvoiceConvertor specialInvoiceConvertor;
    @Autowired
    private KingDeeOutPutFeePlainInvoiceMapper outPutFeePlainInvoiceMapper;
    @Autowired
    private KingDeeOutPutFeePlainInvoiceConvertor outPutFeePlainInvoiceConvertor;


    @Override
    public void doWrite(KingDeeRedInvoiceDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("销售费用红票：{}", JSON.toJSONString(item));

        OutPutFeeSpecialInvoiceDto outPutFeeSpecialInvoiceDto = item.getOutPutFeeSpecialInvoiceDto();
        OutPutFeePlainInvoiceDto outPutFeePlainInvoiceDto = item.getOutPutFeePlainInvoiceDto();
        this.pushVatSpecialInvoice(outPutFeeSpecialInvoiceDto);
        this.pushVatPlainInvoice(outPutFeePlainInvoiceDto);
    }

    private void pushVatSpecialInvoice(OutPutFeeSpecialInvoiceDto outPutFeeSpecialInvoiceDto) {
        if (outPutFeeSpecialInvoiceDto != null) {
            KingDeeOutPutFeeSpecialInvoiceCommand command = specialCommandConvertor.toCommand(outPutFeeSpecialInvoiceDto);
            ArrayList<String> needReturnFields = new ArrayList<>();
            needReturnFields.add("FSALEEXINVENTRY.FEntryID");
            needReturnFields.add("FSALEEXINVENTRY.F_QZOK_BDDJHID");
            RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, outPutFeeSpecialInvoiceDto.getFormId(), needReturnFields));
            ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();
            if (CollUtil.isNotEmpty(successEntities)) {
                SuccessEntity successEntity = CollUtil.getFirst(successEntities);
                // 回写主表id
                outPutFeeSpecialInvoiceDto.setFid(successEntity.getId());
                // 回写明细id
                List<Map<String, Object>> returnData = successEntity.returnData(needReturnFields);
                if (CollUtil.isNotEmpty(returnData)) {
                    // 单条新增所以仅有一条记录
                    Map<String, Object> returnMap = CollUtil.getFirst(returnData);
                    List<JSONObject> jsonObjectList = Convert.toList(JSONObject.class, returnMap.get("FSALEEXINVENTRY"));
                    outPutFeeSpecialInvoiceDto.getFSALEEXINVENTRY().forEach(c -> {
                        String fEntryId = jsonObjectList
                                .stream()
                                .filter(o -> o.get("F_QZOK_BDDJHID").equals(c.getFQzokBddjhid()))
                                .map(o -> o.getString("FEntryID"))
                                .findFirst()
                                .orElseThrow(() -> new BatchException("金蝶采购费用专票详细行id错误:" + JSON.toJSONString(jsonObjectList)));
                        c.setFEntryId(fEntryId);
                    });
                }

                //金蝶业务数据入表
                KingDeeOutPutFeeSpecialInvoiceEntity entity = specialInvoiceConvertor.toEntity(outPutFeeSpecialInvoiceDto);
                log.info("销售费用专票 数据库保存{}", JSON.toJSONString(entity));
                specialInvoiceMapper.insertSelective(entity);
            }
        }
    }

    private void pushVatPlainInvoice(OutPutFeePlainInvoiceDto outPutFeePlainInvoiceDto) {
        if (outPutFeePlainInvoiceDto != null) {
            KingDeeOutPutFeePlainInvoiceCommand command = plainCommandConvertor.toCommand(outPutFeePlainInvoiceDto);
            ArrayList<String> needReturnFields = new ArrayList<>();
            needReturnFields.add("FSALEEXINVENTRY.FEntryID");
            needReturnFields.add("FSALEEXINVENTRY.F_QZOK_BDDJHID");
            RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, outPutFeePlainInvoiceDto.getFormId(), needReturnFields));
            ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();
            if (CollUtil.isNotEmpty(successEntities)) {
                SuccessEntity successEntity = CollUtil.getFirst(successEntities);
                // 回写主表id
                outPutFeePlainInvoiceDto.setFid(successEntity.getId());
                // 回写明细id
                List<Map<String, Object>> returnData = successEntity.returnData(needReturnFields);
                if (CollUtil.isNotEmpty(returnData)) {
                    // 单条新增所以仅有一条记录
                    Map<String, Object> returnMap = CollUtil.getFirst(returnData);
                    List<JSONObject> jsonObjectList = Convert.toList(JSONObject.class, returnMap.get("FSALEEXINVENTRY"));
                    outPutFeePlainInvoiceDto.getFSALEEXINVENTRY().forEach(c -> {
                        String fEntryId = jsonObjectList
                                .stream()
                                .filter(o -> o.get("F_QZOK_BDDJHID").equals(c.getFQzokBddjhid()))
                                .map(o -> o.getString("FEntryID"))
                                .findFirst()
                                .orElseThrow(() -> new BatchException("金蝶采购费用普票详细行id错误:" + JSON.toJSONString(jsonObjectList)));
                        c.setFEntryId(fEntryId);
                    });
                }

                //金蝶业务数据入表
                KingDeeOutPutFeePlainInvoiceEntity entity = outPutFeePlainInvoiceConvertor.toEntity(outPutFeePlainInvoiceDto);
                log.info("销售费用普票 数据库保存{}", JSON.toJSONString(entity));
                outPutFeePlainInvoiceMapper.insertSelective(entity);
            }
        }
    }
}
