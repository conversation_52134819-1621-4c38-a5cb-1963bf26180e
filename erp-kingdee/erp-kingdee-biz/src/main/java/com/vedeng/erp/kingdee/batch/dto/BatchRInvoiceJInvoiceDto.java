package com.vedeng.erp.kingdee.batch.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @description 发票关联发票
 * <AUTHOR>
 * @date 2022/12/6 14:38
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchRInvoiceJInvoiceDto {
    private Integer rInvoiceJInvoiceId;

    /**
    * 发票ID
    */
    private Integer invoiceId;

    /**
    * 关联发票ID
    */
    private Integer relateInvoiceId;
}