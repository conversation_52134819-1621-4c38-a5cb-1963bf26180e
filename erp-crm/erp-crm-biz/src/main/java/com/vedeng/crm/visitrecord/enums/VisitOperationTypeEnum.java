package com.vedeng.crm.visitrecord.enums;

import lombok.Getter;

/**
 * @Description 拜访记录操作类型枚举
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/1
 */
@Getter
public enum VisitOperationTypeEnum {
    
    CREATE(1, "创建了拜访计划"),
    EDIT(2, "编辑了拜访计划"),
    CARD(3, "提交了打卡信息"),
    EDIT_CARD(4, "编辑了打卡信息"),
    SUBMIT_VISIT(5, "添加了拜访记录"),
    SUBMIT_COMMUNICATE(6, "补充了与客户的沟通记录"),
    CREATE_NEXT(7, "创建下次拜访"),
    RELATE_BIZ(8, "关联线索/商机"),
    CLOSE(9, "关闭了拜访计划");

    private final Integer type;
    private final String desc;

    VisitOperationTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDesc(Integer type) {
        for (VisitOperationTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value.getDesc();
            }
        }
        return "";
    }
} 