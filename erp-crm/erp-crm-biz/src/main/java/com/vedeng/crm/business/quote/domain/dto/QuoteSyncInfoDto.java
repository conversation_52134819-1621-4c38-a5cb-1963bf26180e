package com.vedeng.crm.business.quote.domain.dto;

import com.vedeng.crm.business.business.domain.dto.CrmBusinessChanceDto;
import lombok.Data;

import java.util.List;

@Data
public class QuoteSyncInfoDto {

    /**
     * 商机信息
     */
    private CrmBusinessChanceDto crmBusinessChanceDto;

    /**
     * 报价单信息
     */
    private CrmQuoteOrderDto crmQuoteOrderDto;

    /**
     * 报价需求
     */
    private List<QuoteorderNeedsDto> quoteorderNeedsList;

    /**
     * 商品信息
     */
    private List<CrmQuoteOrderCoreSkuDto> crmQuoteorderGoodsList;

    /**
     * 一些配置信息，例如轮询周期
     */
    private QuoteConfigDto quoteConfigDto;
}
