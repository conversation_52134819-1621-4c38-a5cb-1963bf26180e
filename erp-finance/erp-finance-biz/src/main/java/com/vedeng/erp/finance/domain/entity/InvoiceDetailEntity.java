package com.vedeng.erp.finance.domain.entity;

import java.math.BigDecimal;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 发票详情
 * @date 2022/10/13 10:57
 **/

@Data
public class InvoiceDetailEntity {
    private Integer invoiceDetailId;

    /**
     * 发票ID
     */
    private Integer invoiceId;

    /**
     * 订单详情ID
     */
    private Integer detailgoodsId;

    /**
     * 开票单价
     */
    private BigDecimal price;

    /**
     * 开票数量
     */
    private BigDecimal num;

    /**
     * 开票总额
     */
    private BigDecimal totalAmount;

    /**
     * 修改后的商品名称
     */
    private String changedGoodsName;
}