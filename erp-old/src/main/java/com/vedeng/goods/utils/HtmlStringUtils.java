package com.vedeng.goods.utils;

/**
 * <AUTHOR>
 * @Description com.vedeng.goods.utils
 * @Date 2021/11/9 18:28
 */
public class HtmlStringUtils {

    /**
     * 构造私有化
     */
    private HtmlStringUtils(){};

    /**
     * 去除富文本内容的html标签
     * @param content
     * @return
     */
    public static String stripHtml(String content) {
        // <p>段落替换为换行
        content = content.replaceAll("<p .*?>", "\r\n");
        // <br><br/>替换为换行
        content = content.replaceAll("<br\\s*/?>", "\r\n");
        // 去掉其它的<>之间的东西
        content = content.replaceAll("\\<.*?>", "");
        //去掉nbsp
        content = content.replace("&nbsp;","");

        return content;
    }
}
