package com.vedeng.kpi.model.VO;

import lombok.Getter;
import lombok.Setter;

public class KpiDetailBdKhOverviewVo extends KpiDetailBaseOverviewVo {

    private static final long serialVersionUID = -7877203587203693681L;

    private String userName;

    private Integer userId;

    private Integer coBdCustomerNum;

    private Integer lostBdCustomerNum;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getCoBdCustomerNum() {
        return coBdCustomerNum;
    }

    public void setCoBdCustomerNum(Integer coBdCustomerNum) {
        this.coBdCustomerNum = coBdCustomerNum;
    }

    public Integer getLostBdCustomerNum() {
        return lostBdCustomerNum;
    }

    public void setLostBdCustomerNum(Integer lostBdCustomerNum) {
        this.lostBdCustomerNum = lostBdCustomerNum;
    }
}
