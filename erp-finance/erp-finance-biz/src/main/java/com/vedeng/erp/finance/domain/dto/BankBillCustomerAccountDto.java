package com.vedeng.erp.finance.domain.dto;

import lombok.Data;

/**
 * select b.ORDER_NO, c.BANK_BILL_ID, c.ACCNO2, c.ACC_NAME1, c.BANK_TAG, c.RECEIPT_URL
 */
@Data
public class BankBillCustomerAccountDto {

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * id
     */
    private Integer bankBillId;
    /**
     * 账号
     */
    private String accountNo;
    /**
     * 账户名称 T_BANK_BILL.ACC_NAME1
     */
    private String accountName;
    /**
     * 银行标示1建设银行2南京银行3中国银行4支付宝5微信6交通银行7民生银行
     */
    private Integer bankTag;
    /**
     * 回单url
     */
    private String receiptUrl;

    /**
     * 流水号
     */
    private String tranFlow;
}
