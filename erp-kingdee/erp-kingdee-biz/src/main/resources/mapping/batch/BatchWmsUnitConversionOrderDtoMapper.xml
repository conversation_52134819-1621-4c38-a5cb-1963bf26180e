<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchWmsUnitConversionOrderDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchWmsUnitConversionOrderDto">
    <!--@mbg.generated-->
    <!--@Table T_WMS_UNIT_CONVERSION_ORDER-->
    <id column="WMS_UNIT_CONVERSION_ORDER_ID" jdbcType="INTEGER" property="wmsUnitConversionOrderId" />
    <result column="WMS_UNIT_CONVERSION_ORDER_NO" jdbcType="VARCHAR" property="wmsUnitConversionOrderNo" />
    <result column="ORDER_TYPE" jdbcType="INTEGER" property="orderType" />
    <result column="REASON" jdbcType="VARCHAR" property="reason" />
    <result column="VERIFY_STATUS" jdbcType="INTEGER" property="verifyStatus" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    WMS_UNIT_CONVERSION_ORDER_ID, WMS_UNIT_CONVERSION_ORDER_NO, ORDER_TYPE, REASON, VERIFY_STATUS, 
    ORG_ID, ORG_NAME, COMMENTS, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, UPDATER, 
    UPDATER_NAME, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_WMS_UNIT_CONVERSION_ORDER
    where WMS_UNIT_CONVERSION_ORDER_ID = #{wmsUnitConversionOrderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_WMS_UNIT_CONVERSION_ORDER
    where WMS_UNIT_CONVERSION_ORDER_ID = #{wmsUnitConversionOrderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="WMS_UNIT_CONVERSION_ORDER_ID" keyProperty="wmsUnitConversionOrderId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWmsUnitConversionOrderDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_UNIT_CONVERSION_ORDER (WMS_UNIT_CONVERSION_ORDER_NO, ORDER_TYPE, 
      REASON, VERIFY_STATUS, ORG_ID, 
      ORG_NAME, COMMENTS, IS_DELETE, 
      ADD_TIME, CREATOR, CREATOR_NAME, 
      UPDATER, UPDATER_NAME, MOD_TIME
      )
    values (#{wmsUnitConversionOrderNo,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER}, 
      #{reason,jdbcType=VARCHAR}, #{verifyStatus,jdbcType=INTEGER}, #{orgId,jdbcType=INTEGER}, 
      #{orgName,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="WMS_UNIT_CONVERSION_ORDER_ID" keyProperty="wmsUnitConversionOrderId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWmsUnitConversionOrderDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_UNIT_CONVERSION_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="wmsUnitConversionOrderNo != null">
        WMS_UNIT_CONVERSION_ORDER_NO,
      </if>
      <if test="orderType != null">
        ORDER_TYPE,
      </if>
      <if test="reason != null">
        REASON,
      </if>
      <if test="verifyStatus != null">
        VERIFY_STATUS,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="orgName != null">
        ORG_NAME,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="wmsUnitConversionOrderNo != null">
        #{wmsUnitConversionOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="verifyStatus != null">
        #{verifyStatus,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWmsUnitConversionOrderDto">
    <!--@mbg.generated-->
    update T_WMS_UNIT_CONVERSION_ORDER
    <set>
      <if test="wmsUnitConversionOrderNo != null">
        WMS_UNIT_CONVERSION_ORDER_NO = #{wmsUnitConversionOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        ORDER_TYPE = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        REASON = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="verifyStatus != null">
        VERIFY_STATUS = #{verifyStatus,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        ORG_NAME = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where WMS_UNIT_CONVERSION_ORDER_ID = #{wmsUnitConversionOrderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWmsUnitConversionOrderDto">
    <!--@mbg.generated-->
    update T_WMS_UNIT_CONVERSION_ORDER
    set WMS_UNIT_CONVERSION_ORDER_NO = #{wmsUnitConversionOrderNo,jdbcType=VARCHAR},
      ORDER_TYPE = #{orderType,jdbcType=INTEGER},
      REASON = #{reason,jdbcType=VARCHAR},
      VERIFY_STATUS = #{verifyStatus,jdbcType=INTEGER},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      ORG_NAME = #{orgName,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
    where WMS_UNIT_CONVERSION_ORDER_ID = #{wmsUnitConversionOrderId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="WMS_UNIT_CONVERSION_ORDER_ID" keyProperty="wmsUnitConversionOrderId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_UNIT_CONVERSION_ORDER
    (WMS_UNIT_CONVERSION_ORDER_NO, ORDER_TYPE, REASON, VERIFY_STATUS, ORG_ID, ORG_NAME, 
      COMMENTS, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, MOD_TIME
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.wmsUnitConversionOrderNo,jdbcType=VARCHAR}, #{item.orderType,jdbcType=INTEGER}, 
        #{item.reason,jdbcType=VARCHAR}, #{item.verifyStatus,jdbcType=INTEGER}, #{item.orgId,jdbcType=INTEGER}, 
        #{item.orgName,jdbcType=VARCHAR}, #{item.comments,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=INTEGER}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR}, #{item.modTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
</mapper>