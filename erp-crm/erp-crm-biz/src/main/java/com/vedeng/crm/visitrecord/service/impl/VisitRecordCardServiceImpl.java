package com.vedeng.crm.visitrecord.service.impl;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.crm.visitrecord.domain.dto.VisitRecordCardDto;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordCardVo;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;
import com.vedeng.crm.visitrecord.enums.VisitOperationTypeEnum;
import com.vedeng.crm.visitrecord.mapper.CrmVisitRecordMapper;
import com.vedeng.crm.visitrecord.mapper.VisitRecordCardMapper;
import com.vedeng.crm.visitrecord.service.CrmVisitRecordLogService;
import com.vedeng.crm.visitrecord.service.VisitRecordCardService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.system.service.impl.UserApiServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class VisitRecordCardServiceImpl implements VisitRecordCardService {

    @Autowired
    private VisitRecordCardMapper visitRecordCardMapper;

    @Autowired
    private CrmVisitRecordMapper visitRecordMapper;
    @Autowired
    private CrmVisitRecordLogService crmVisitRecordLogService;
    @Autowired
    private UserApiService userApiService;

    @Override
    public List<VisitRecordCardVo> getLatestCardList(Integer recordId) {
        return visitRecordCardMapper.selectLatestCardByRecordId(recordId);
    }

    @Override
    public void saveCard(VisitRecordCardDto cardDto, CurrentUser currentUser) {
        VisitRecordVo visitRecordVo =  visitRecordMapper.selectVisitRecordById(cardDto.getRecordId());//拜访计划的Id
        if(visitRecordVo!=null && visitRecordVo.getVisitRecordStatus() !=null && visitRecordVo.getVisitRecordStatus()>2){//'拜访计划的状态：1.待拜访；2.拜访中；3.已拜访；4.已关闭;',
            throw new ServiceException("拜访计划状态已变化，请刷新页面后重试");
        }


        VisitRecordCardVo card = new VisitRecordCardVo();
        BeanUtils.copyProperties(cardDto, card);
        UserDto cardUserDto = userApiService.getUserBaseInfo(cardDto.getVisitUserId());
        card.setVisitUserId(cardUserDto.getUserId());
        card.setAddUserId(cardUserDto.getUserId());
        card.setAddTime(new Date());
        card.setModUserId(cardUserDto.getUserId());
        card.setModTime(new Date());


        String lastTime = visitRecordCardMapper.selectLastestCheckInTime(currentUser.getId(), card.getRecordId());

//        card.setVisitUserId(currentUser.getId());
//        card.setAddUserId(currentUser.getId());
//        card.setAddTime(new Date());
//        card.setModUserId(currentUser.getId());
//        card.setModTime(new Date());
        visitRecordCardMapper.insert(card);

        // 2. 设置更新内容
        VisitRecordVo record = new VisitRecordVo();
        record.setModUserId(currentUser.getId());
        record.setModTime(new Date());
        record.setVisitRecordStatus(2);// 拜访中
        record.setId(cardDto.getRecordId());
        // 3. 执行更新
        visitRecordMapper.update(record);


        crmVisitRecordLogService.addOperationLog(cardDto.getRecordId(), StringUtils.isEmpty(lastTime)?VisitOperationTypeEnum.CARD:VisitOperationTypeEnum.EDIT_CARD, currentUser);
    }
} 