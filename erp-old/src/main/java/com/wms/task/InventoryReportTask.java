package com.wms.task;

import com.vedeng.common.constant.ApiUrlConstant;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.wms.dto.InventoryReportReqDto;
import com.wms.service.util.GlobalThreadPool;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.UUID;

/**
 * <AUTHOR>
 * 进销存报告任务
 */
@Component
@JobHandler(value = "inventoryReportTask")
public class InventoryReportTask extends AbstractJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(InventoryReportTask.class);

    @Value("${stock_admin_url}")
    private String stockAdminUrl;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("InventoryReportTask-JOB, START");

        InventoryReportReqDto reqDto = new InventoryReportReqDto();
        if (StringUtils.isNotBlank(s)) {
            logger.info("手动触发进销存报告任务 s:{}", s);

            try {
                reqDto = com.alibaba.fastjson.JSONObject.parseObject(s, InventoryReportReqDto.class);
            } catch (Exception e) {
                XxlJobLogger.log("进销存报告输入参数不正确! s:" + s);
                logger.info("进销存报告输入参数不正确! s:{}", s);
                return FAIL;
            }
        } else {
            logger.info("系统自动生成进销存报告 time:{}", DateTimeFormatter.ofPattern(DateUtil.TIME_FORMAT).format(LocalDateTime.now()));
            reqDto.setCheckBeginDate(getBeforeDateTimeEnd(-2));
            reqDto.setCheckEndDate(getBeforeDateTimeEnd(-1));
        }

        String json = JsonUtils.translateToJson(reqDto);
        String url = stockAdminUrl + ApiUrlConstant.API_INVENTORY_REPORT;

        GlobalThreadPool.submitMessage(new Runnable() {
            @Override
            public void run() {
                String uuid = UUID.randomUUID().toString().replace("-", "");
                logger.info("开始执行生成进销存报告信息,uuid:{}",uuid);
                logger.info("开始生成进销存报告信息 reqDto:{}", json);
                NewHttpClientUtils.httpPost(url, json);
                logger.info("结束执行生成进销存报告信息,uuid:{}",uuid);
            }
        });
        return SUCCESS;
    }


    /**
     * 获取某一天的结束时间
     *
     * @param day 相对于今天的某一天
     * @return
     */
    private String getBeforeDateTimeEnd(int day) {
        Calendar instance = Calendar.getInstance();
        instance.add(Calendar.DATE, day);
        instance.set(instance.get(Calendar.YEAR), instance.get(Calendar.MONTH), instance.get(Calendar.DATE), 23, 59, 59);
        return new SimpleDateFormat(DateUtil.TIME_FORMAT).format(instance.getTime());
    }
}
