package com.vedeng.order.model.query;

import com.vedeng.goods.model.dto.BaseQueryDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> [<EMAIL>]
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class JcGoodsQuery extends BaseQueryDto {

    private Integer saleorderId;

    private Integer goodsBrandId;

    private String searchContent;

    private Integer goodsType;

    private String typeSpecification;

    private String unitName;
}
