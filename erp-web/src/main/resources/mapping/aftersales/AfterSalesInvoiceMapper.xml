<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.aftersales.dao.AfterSalesInvoiceMapper" >
	<resultMap id="BaseResultMap" type="com.vedeng.aftersales.model.AfterSalesInvoice" >
		<id column="AFTER_SALES_INVOICE_ID" property="afterSalesInvoiceId" jdbcType="INTEGER" />
		<result column="AFTER_SALES_ID" property="afterSalesId" jdbcType="INTEGER" />
		<result column="INVOICE_ID" property="invoiceId" jdbcType="INTEGER" />
		<result column="IS_REFUND_INVOICE" property="isRefundInvoice" jdbcType="BIT" />
		<result column="STATUS" property="status" jdbcType="BIT" />
		<result column="IS_SEND_INVOICE" property="isSendInvoice" jdbcType="BIT" />
		<result column="SEND_TIME" property="sendTime"/>
		<result column="HANDLE_STATUS" property="handleStatus" jdbcType="INTEGER"/>
		<result column="HANDLE_COMMENTS" property="handleComments" jdbcType="VARCHAR"/>
		<result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR"/>
	</resultMap>

	<resultMap type="com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo" id="VoResultMap" extends="BaseResultMap">
		<result column="INVOICE_NO" property="invoiceNo" jdbcType="VARCHAR" />
		<result column="INVOICE_CODE" property="invoiceCode" jdbcType="VARCHAR" />
		<result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER" />
		<result column="INVOICE_PROPERTY" property="invoiceProperty" jdbcType="INTEGER"/>
		<result column="INVOICE_TYP_NAME" property="invoiceTypeName" jdbcType="VARCHAR" />
		<result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
		<result column="EXPRESS_ID" property="expressId" jdbcType="INTEGER" />
		<result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
		<result column="CREATOR" property="creator" jdbcType="INTEGER" />
		<result column="COLOR_TYPE" property="colorType" jdbcType="BIT" />
		<result column="AMOUNT_COUNT" property="amountCount" jdbcType="DECIMAL" />
		<result column="VALID_STATUS" property="validStatus" jdbcType="BIT" />
		<result column="VALID_TIME" property="validTime" jdbcType="BIGINT" />
		<result column="VALID_USERID" property="validUserId" jdbcType="INTEGER" />
		<result column="LOGISTICS_NO" property="logisticsNo" jdbcType="VARCHAR"/>
		<result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="INTEGER"/>
		<result column="ARRIVAL_TIME" property="arrivalTime"/>
		<result column="IS_ENABLE" property="isEnable" jdbcType="INTEGER" />
		<result column="TYPE" property="type" jdbcType="INTEGER"/>
		<result column="AFTER_EXPRESS_ID" property="afterExpressId" jdbcType="INTEGER" />
	</resultMap>

	<insert id="insertSelective" parameterType="com.vedeng.aftersales.model.AfterSalesInvoice" >
		insert into T_AFTER_SALES_INVOICE
		<trim prefix="(" suffix=")" suffixOverrides="," >
			<if test="afterSalesInvoiceId != null" >
				AFTER_SALES_INVOICE_ID,
			</if>
			<if test="afterSalesId != null" >
				AFTER_SALES_ID,
			</if>
			<if test="invoiceId != null" >
				INVOICE_ID,
			</if>
			<if test="isRefundInvoice != null" >
				IS_REFUND_INVOICE,
			</if>
			<if test="status != null" >
				STATUS,
			</if>
			<if test="isSendInvoice != null" >
				IS_SEND_INVOICE,
			</if>
			<if test="invoiceNo != null" >
				INVOICE_NO,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides="," >
			<if test="afterSalesInvoiceId != null" >
				#{afterSalesInvoiceId,jdbcType=INTEGER},
			</if>
			<if test="afterSalesId != null" >
				#{afterSalesId,jdbcType=INTEGER},
			</if>
			<if test="invoiceId != null" >
				#{invoiceId,jdbcType=INTEGER},
			</if>
			<if test="isRefundInvoice != null" >
				#{isRefundInvoice,jdbcType=BIT},
			</if>
			<if test="status != null" >
				#{status,jdbcType=BIT},
			</if>
			<if test="isSendInvoice != null" >
				#{isSendInvoice,jdbcType=BIT},
			</if>
			<if test="invoiceNo != null" >
				#{invoiceNo,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>

	<update id="update" parameterType="com.vedeng.aftersales.model.AfterSalesInvoice" >
		update T_AFTER_SALES_INVOICE
		<set >
			<if test="afterSalesId != null" >
				AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
			</if>
			<if test="invoiceId != null" >
				INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
			</if>
			<if test="isRefundInvoice != null" >
				IS_REFUND_INVOICE = #{isRefundInvoice,jdbcType=BIT},
			</if>
			<if test="status != null" >
				STATUS = #{status,jdbcType=BIT},
			</if>
			<if test="isSendInvoice != null" >
				IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BIT},
			</if>
		</set>
		where AFTER_SALES_INVOICE_ID = #{afterSalesInvoiceId,jdbcType=INTEGER}
	</update>

    <update id="saveInvoiceHandleInfo">
		 update T_AFTER_SALES_INVOICE SET  HANDLE_STATUS = #{handleStatus,jdbcType = INTEGER}, HANDLE_COMMENTS = #{handleComments,jdbcType =VARCHAR} WHERE AFTER_SALES_INVOICE_ID = #{afterSalesInvoiceId,jdbcType = INTEGER}
	</update>

    <delete id="delAfterSalesInvoiceByAfterSalesId" parameterType="java.lang.Integer">
		delete from T_AFTER_SALES_INVOICE
		where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
	</delete>

	<!-- 蓝字有效，红字有效 -->
	<select id="getAfterSalesInvoiceVos" parameterType="com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo"
										 resultType="com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo">
		select 
			a.INVOICE_ID, a.INVOICE_NO, a.INVOICE_TYPE, a.AMOUNT, a.EXPRESS_ID,c.TITLE AS INVOICE_TYPE_NAME,ABS(SUM(a.AMOUNT)) as AMOUNT_COUNT
		from T_INVOICE a 
		left join T_INVOICE_DETAIL b on a.INVOICE_ID = b.INVOICE_ID 
		left join T_SYS_OPTION_DEFINITION c on c.SYS_OPTION_DEFINITION_ID = a.INVOICE_TYPE
		where a.VALID_STATUS = 1
		<if test="orderGoodsIdList != null">
			AND b.DETAILGOODS_ID in 
			<foreach collection="orderGoodsIdList" item="orderGoodsId" open="(" close=")" separator=",">
      			#{orderGoodsId,jdbcType=INTEGER}
      		</foreach>
		</if>
		<if test = "type != null and type != 0">
			and a.TYPE = #{type,jdbcType=INTEGER}
		</if>
		<if test = "typeList != null">
			and a.TYPE in
			<foreach collection="typeList" item="type" open="(" close=")" separator=",">
      			#{type,jdbcType=INTEGER}
      		</foreach>
		</if>
		<if test = "companyId != null and companyId != 0">
			and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		</if>
		<if test = "invoiceProperty != null and invoiceProperty != 0">
			and a.INVOICE_PROPERTY = #{invoiceProperty,jdbcType=INTEGER}
		</if>
		<if test = "relatedId != null and relatedId != 0">
			and a.RELATED_ID = #{relatedId,jdbcType=INTEGER}
		</if>
		group by a.INVOICE_NO having AMOUNT_COUNT > 0
	</select>
    <select id="getAfterSalesInvoiceVosByParam"
            resultType="com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo">
		select
		d.AFTER_SALES_INVOICE_ID, d.AFTER_SALES_ID, d.INVOICE_ID, d.IS_REFUND_INVOICE, d.STATUS, d.IS_SEND_INVOICE,
		a.INVOICE_NO, a.INVOICE_TYPE, a.AMOUNT, a.EXPRESS_ID,c.TITLE AS INVOICE_TYP_NAME, ABS(SUM(a.AMOUNT)) as AMOUNT_COUNT,a.INVOICE_CODE
		from T_INVOICE a
		left join T_AFTER_SALES_INVOICE d on a.INVOICE_ID = d.INVOICE_ID
		left join T_INVOICE_DETAIL b on a.INVOICE_ID = b.INVOICE_ID
		left join T_SYS_OPTION_DEFINITION c on c.SYS_OPTION_DEFINITION_ID = a.INVOICE_TYPE
		where a.VALID_STATUS = 1
		<if test="orderGoodsIdList != null">
			AND b.DETAILGOODS_ID in
			<foreach collection="orderGoodsIdList" item="orderGoodsId" open="(" close=")" separator=",">
				#{orderGoodsId,jdbcType=INTEGER}
			</foreach>
		</if>
		<if test="afterSalesId != null" >
			AND d.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
		</if>
		<if test = "typeList != null">
			and a.TYPE in
			<foreach collection="typeList" item="type" open="(" close=")" separator=",">
				#{type,jdbcType=INTEGER}
			</foreach>
		</if>
		<if test = "companyId != null and companyId != 0">
			and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		</if>
		group by a.INVOICE_NO having AMOUNT_COUNT > 0
	</select>
	<select id="getAfterSalesInvoiceForInValid" resultType="com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo">
		SELECT
	    I.RELATED_ID AS AFTER_SALES_ID,
	    I.INVOICE_ID,
	    I.IS_ENABLE,
		I.INVOICE_NO,
		I.INVOICE_TYPE,
		I.AMOUNT,
		I.EXPRESS_ID,
		SYS.TITLE AS INVOICE_TYP_NAME,
		ABS(SUM(I.AMOUNT)) as AMOUNT_COUNT,
		I.INVOICE_CODE
		FROM
		T_INVOICE I
		INNER JOIN T_HX_INVOICE HI ON I.HX_INVOICE_ID = HI.HX_INVOICE_ID
		LEFT JOIN T_SYS_OPTION_DEFINITION SYS on SYS.SYS_OPTION_DEFINITION_ID = I.INVOICE_TYPE
		WHERE
			HI.INVOICE_TYPE = 1
			AND HI.COLOR_TYPE = 1
			AND HI.INVOICE_STATUS = 12
			AND I.INVOICE_FROM = 2
			AND I.TYPE = 504
			AND I.TAG = 2
			AND I.RELATED_ID = #{afterSalesId}
	</select>

	<!-- 获取售后收、开票记录(安调、维修服务费，售后开票都是蓝字有效:收工程师费用发票) -->
	<select id="getAfterInvoiceList" parameterType="java.lang.Integer" resultType="com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo">
		SELECT B.INVOICE_NO,
		B.INVOICE_ID,
		B.INVOICE_HREF,
		B.INVOICE_CODE,
		B.INVOICE_TYPE,
		B.COLOR_TYPE,
		B.IS_ENABLE,
		B.AMOUNT,
		B.CREATOR,
		B.ADD_TIME,
		B.EXPRESS_ID,
		C.LOGISTICS_NO,
		D.NAME AS logisticsName,
		C.DELIVERY_TIME,
		C.ARRIVAL_STATUS,
		C.LOGISTICS_COMMENTS,
		B.VALID_TIME,B.VALID_STATUS,B.VALID_USERID,B.OSS_FILE_URL
		FROM T_INVOICE B
		LEFT JOIN T_EXPRESS C ON B.EXPRESS_ID = C.EXPRESS_ID AND C.IS_ENABLE = 1
		LEFT JOIN T_LOGISTICS D ON C.LOGISTICS_ID = D.LOGISTICS_ID
		WHERE B.RELATED_ID = #{afterSalesId,jdbcType=INTEGER} AND B.TYPE = #{type,jdbcType=INTEGER} <!-- 504售后开票 -->
		AND B.COLOR_TYPE = 2 AND B.IS_ENABLE = 1 AND B.VALID_STATUS = 1 AND B.TAG = #{tag,jdbcType=INTEGER}	<!-- 1开票    2收票 -->
		ORDER BY B.ADD_TIME DESC
	</select>

	<select id="getAfterAtPaymentApply" resultType="com.vedeng.finance.model.PayApply">
		SELECT A.PAY_APPLY_ID,
		       A.RELATED_ID,
		       A.AMOUNT,
		       A.ADD_TIME,
		       A.CREATOR,
		       A.TRADER_NAME,
		       A.TRADER_SUBJECT,
		       A.VALID_STATUS,
		       A.VALID_TIME,
		       A.VALID_COMMENTS,
		       A.TRADER_NAME,
		       A.BANK,
		       A.BANK_CODE,
		       A.BANK_ACCOUNT,
		       A.COMMENTS,
			   TE.MOBILE,
			   U.USERNAME creatorName,
			   TE.CARD
		FROM T_PAY_APPLY A
		LEFT JOIN (
			SELECT
				b.`NAME` AS na,
				b.MOBILE,
				b.CARD
			FROM
				T_AFTER_SALES_INSTALLSTION TA
				LEFT JOIN T_ENGINEER b ON TA.ENGINEER_ID = b.ENGINEER_ID
			WHERE
				1 = 1
				AND TA.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
			ORDER BY
				TA.SERVICE_TIME DESC
				LIMIT 1
		) TE
		ON A.TRADER_NAME = TE.na
		LEFT JOIN T_USER U ON A.CREATOR = U.USER_ID
		WHERE A.PAY_TYPE = 518 AND A.RELATED_ID = #{afterSalesId,jdbcType=INTEGER}
		order by A.ADD_TIME DESC
	</select>
	<select id="getIsReturnAfterInvoice" resultType="java.lang.Integer">
		select
	    	count(AFTER_SALES_INVOICE_ID)
	    from T_AFTER_SALES_INVOICE
	    where STATUS = 1 and AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
	</select>

	<select id="getAfterSalesAWInvoiceVoList" parameterType="java.lang.Integer" resultMap="VoResultMap">
		SELECT B.INVOICE_NO,
		B.INVOICE_ID,
		B.INVOICE_CODE,
		B.INVOICE_TYPE,
		B.COLOR_TYPE,
		B.IS_ENABLE,
		ABS(SUM(B.AMOUNT)) AS AMOUNT,
		B.CREATOR,
		B.ADD_TIME,
		B.EXPRESS_ID,
		B.VALID_TIME,B.VALID_STATUS,B.VALID_USERID,
		C.TITLE AS INVOICE_TYP_NAME
		FROM T_INVOICE B
		LEFT JOIN T_SYS_OPTION_DEFINITION C ON B.INVOICE_TYPE = C.SYS_OPTION_DEFINITION_ID
		WHERE B.RELATED_ID = #{afterSalesId,jdbcType=INTEGER} AND B.TYPE = 504 <!-- 504售后开票 -->
		AND B.COLOR_TYPE = 2 AND B.IS_ENABLE = 1 AND B.VALID_STATUS = 1 AND B.TAG = 2	<!-- 1开票    2收票 -->
		GROUP BY B.INVOICE_NO having AMOUNT > 0
	</select>

	<select id="getAfterSalesInvoiceVoList" parameterType="com.vedeng.aftersales.model.AfterSalesInvoice" resultMap="VoResultMap">
		SELECT A.AFTER_SALES_INVOICE_ID,
		A.INVOICE_ID,
		A.HANDLE_STATUS,
		A.HANDLE_COMMENTS,
		B.INVOICE_NO,
		B.INVOICE_CODE,
		B.INVOICE_PROPERTY,
		B.AMOUNT,
		B.INVOICE_TYPE,
		B.EXPRESS_ID,
		A.IS_REFUND_INVOICE,
		A.STATUS,
		B.ADD_TIME,
		B.CREATOR,
		B.COLOR_TYPE,
		B.INVOICE_TYPE,
		A.IS_SEND_INVOICE,
		t.`LOGISTICS_NO`,
		t.`ARRIVAL_STATUS`,
		t.`ARRIVAL_TIME`,
		B.COLOR_TYPE,
		B.IS_ENABLE,
		B.`SEND_TIME`,
		s.NAME logisticsName,
		<if test="type==1135">
			B.`AFTER_EXPRESS_ID`,
		</if>
		A.MOD_TIME,
		U.USERNAME updaterName,
		B.TYPE
		FROM T_AFTER_SALES_INVOICE A
		LEFT JOIN T_AFTER_SALES_DETAIL C ON A.AFTER_SALES_ID = C.AFTER_SALES_ID
		LEFT JOIN T_INVOICE B ON A.INVOICE_ID = B.INVOICE_ID and B.TYPE <![CDATA[ <> ]]> 504 <!-- 504售后开票 -->
		LEFT JOIN T_EXPRESS t
		<if test="type!=1135">
			ON B.`EXPRESS_ID`=t.`EXPRESS_ID`
		</if>
		<if test="type==1135">
			ON B.`AFTER_EXPRESS_ID`=t.`EXPRESS_ID`
		</if>
		LEFT JOIN T_LOGISTICS s ON t.LOGISTICS_ID=s.LOGISTICS_ID
		LEFT JOIN T_USER U ON A.UPDATER = U.USER_ID
		WHERE 1 = 1
		<if test="afterSalesId != null" >
			AND A.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
		</if>
		ORDER BY B.ADD_TIME DESC

	</select>
	<select id="getAfterSalesInvoiceVosByParamNew" resultType="com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo">
		select
		d.AFTER_SALES_INVOICE_ID, d.AFTER_SALES_ID, d.INVOICE_ID, d.IS_REFUND_INVOICE, d.STATUS, d.IS_SEND_INVOICE,
		a.INVOICE_NO, a.INVOICE_TYPE, a.AMOUNT, a.EXPRESS_ID,c.TITLE AS INVOICE_TYP_NAME, ABS(SUM(b.TOTAL_AMOUNT)) as AMOUNT_COUNT,a.INVOICE_CODE
		from T_INVOICE a
		left join T_AFTER_SALES_INVOICE d on a.INVOICE_ID = d.INVOICE_ID
		left join T_INVOICE_DETAIL b on a.INVOICE_ID = b.INVOICE_ID
		left join T_SYS_OPTION_DEFINITION c on c.SYS_OPTION_DEFINITION_ID = a.INVOICE_TYPE
		where a.VALID_STATUS != 2
		<if test="orderGoodsIdList != null">
			AND b.DETAILGOODS_ID in
			<foreach collection="orderGoodsIdList" item="orderGoodsId" open="(" close=")" separator=",">
				#{orderGoodsId,jdbcType=INTEGER}
			</foreach>
		</if>
<!--		<if test="afterSalesId != null" >-->
<!--			AND d.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}-->
<!--		</if>-->
		<if test = "typeList != null">
			and a.TYPE in
			<foreach collection="typeList" item="type" open="(" close=")" separator=",">
				#{type,jdbcType=INTEGER}
			</foreach>
		</if>
		<if test="relatedId != null" >
			AND a.RELATED_ID = #{relatedId,jdbcType=INTEGER}
		</if>
		<if test = "companyId != null and companyId != 0">
			and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		</if>
		<if test = "afterSalesId != null and afterSalesId != 0">
			and d.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
		</if>
		group by a.INVOICE_NO having AMOUNT_COUNT > 0
	</select>

    <select id="getAfterSalesInvoiceByAfterSaleId" resultType="com.vedeng.aftersales.model.AfterSalesInvoice">
        SELECT * FROM T_AFTER_SALES_INVOICE WHERE AFTER_SALES_ID = #{afterSaleId,jdbcType=INTEGER}
    </select>
	<select id="selectOneByRelatedId" resultType="com.vedeng.aftersales.model.AfterSalesInvoice">
		select *
		from T_AFTER_SALES_INVOICE where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER} and INVOICE_ID = #{invoiceId,jdbcType=INTEGER} limit 1
	</select>


	<select id="getAllInvoiceNoBgZero" resultType="com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo">
		SELECT
		a.INVOICE_NO,
		ABS( SUM( a.AMOUNT ) ) AS AMOUNT_COUNT
		FROM
		T_INVOICE a
		LEFT JOIN T_INVOICE_DETAIL b ON a.INVOICE_ID = b.INVOICE_ID
		left join T_AFTER_SALES_INVOICE d on a.INVOICE_ID = d.INVOICE_ID
		WHERE
		a.VALID_STATUS != 2
		<if test="orderGoodsIdList != null">
			AND b.DETAILGOODS_ID in
			<foreach collection="orderGoodsIdList" item="orderGoodsId" open="(" close=")" separator=",">
				#{orderGoodsId,jdbcType=INTEGER}
			</foreach>
		</if>
		<if test="afterSalesId != null" >
			AND d.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
		</if>
		<if test = "typeList != null">
			and a.TYPE in
			<foreach collection="typeList" item="type" open="(" close=")" separator=",">
				#{type,jdbcType=INTEGER}
			</foreach>
		</if>
		<if test = "companyId != null and companyId != 0">
			and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		</if>
		group by a.INVOICE_NO having AMOUNT_COUNT > 0
	</select>

    <select id="getUnHandleRecordByInvoiceNoAndCode" resultType="com.vedeng.aftersales.model.AfterSalesInvoice">
		SELECT
			ASI.*
		FROM
			T_INVOICE I
			INNER JOIN T_AFTER_SALES_INVOICE ASI ON I.INVOICE_ID = ASI.INVOICE_ID
		WHERE
			I.COMPANY_ID = 1
		  AND I.VALID_STATUS = 1
		  AND ASI.STATUS = 0
		  AND I.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
		  AND I.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
	</select>

	<select id="queryAfterBuyorderInvoice" resultType="com.vedeng.erp.aftersale.dto.ReturnBuyorderInvoiceDto">
		SELECT AFTER_SALES_INVOICE_ID,AFTER_SALES_ID,IS_REFUND_INVOICE,STATUS,INVOICE_NO
		    FROM T_AFTER_SALES_INVOICE
		WHERE AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
	</select>

	<select id="getPushInvoiceByAfterSalesId" resultMap="VoResultMap">
		SELECT TI.INVOICE_ID,
		TI.INVOICE_NO,
		TI.INVOICE_CODE,
		TI.INVOICE_PROPERTY,
		TI.TYPE,
		TI.AMOUNT,
		TI.COLOR_TYPE,
		TI.ADD_TIME
		FROM T_INVOICE TI
		INNER JOIN T_AFTER_SALES_INVOICE ASI ON TI.INVOICE_ID = ASI.INVOICE_ID
		WHERE ASI.AFTER_SALES_ID = #{afterSalesId,jdbcType = INTEGER}
	</select>
</mapper>