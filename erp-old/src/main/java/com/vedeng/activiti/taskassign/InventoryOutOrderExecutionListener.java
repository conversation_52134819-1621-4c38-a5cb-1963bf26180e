package com.vedeng.activiti.taskassign;

import com.pricecenter.constant.VerifyStatusEnum;
import com.wms.model.po.WmsOutputOrder;
import com.wms.service.WmsInventoryOutService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 审核流最终执行节点
 * <AUTHOR>
 * @date 2022/9/26 13:57
 */
@Slf4j
public class InventoryOutOrderExecutionListener implements ExecutionListener {

    public static final String PASS = "pass";
    public static final String BUSINESS_KEY = "businessKey";
    public static final String REGEX = "_";
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private final WmsInventoryOutService inventoryOutService = (WmsInventoryOutService) context.getBean("wmsInventoryOutServiceImpl");

    @Override
    public void notify(DelegateExecution execution) throws Exception {

        String businessKey = execution.getVariable(BUSINESS_KEY).toString();
        Long inventoryOutOrderId = Long.valueOf(businessKey.split(REGEX)[1]);
        boolean pass = Boolean.parseBoolean(execution.getVariable(PASS).toString());

        inventoryOutService.updateInventoryOutOrderAuditStatus(inventoryOutOrderId,pass ? VerifyStatusEnum.Approved.getValue() : VerifyStatusEnum.Reject.getValue());

        if(pass){
            // 先锁库存 后下发wms 临时方案下发wms
//            ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(1, 1, 1, TimeUnit.MINUTES,Link);

            new Thread(() -> {
                try {
                    String uuid = UUID.randomUUID().toString().replace("-", "");
                    log.info("开始执行synchStockAndPutWmsInventoryOutOrder方法,uuid:{}",uuid);
                    inventoryOutService.synchStockAndPutWmsInventoryOutOrder(inventoryOutOrderId);
                    log.info("结束执行synchStockAndPutWmsInventoryOutOrder方法,uuid:{}",uuid);
                } catch (Exception e) {
                    log.error("盘亏出库审核通过下发wms", e);
                }
            }).start();


        }
    }
}
