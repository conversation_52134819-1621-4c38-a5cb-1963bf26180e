package com.vedeng.dwh.model.generate;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * DWS_BUSSINESS_CHANCE_WORKBENCH_DF
 * <AUTHOR>
@Data
public class DwsBussinessChanceWorkbenchDfDo implements Serializable {
    /**
     * 商机ID
     */
    private Integer bussinessChanceId;

    /**
     * 商机编号
     */
    private String bussinessChanceNo;

    /**
     * 公司ID
     */
    private Integer companyId;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 部门ID
     */
    private Integer orgId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 一级部门ID
     */
    private Integer l1Id;

    /**
     * 一级部门名称
     */
    private String l1Name;

    /**
     * 二级部门ID
     */
    private Integer l2Id;

    /**
     * 二级部门名称
     */
    private String l2Name;

    /**
     * 三级部门ID
     */
    private Integer l3Id;

    /**
     * 三级部门名称
     */
    private String l3Name;

    /**
     * 职位ID
     */
    private Integer positionId;

    /**
     * 职位级别
     */
    private Integer positionLevel;

    /**
     * 职位名称
     */
    private String positionName;

    /**
     * 职位类型
     */
    private Integer positionType;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 商机类型
     */
    private Integer type;

    /**
     * 商机时间
     */
    private Long receiveTime;

    /**
     * 商机来源
     */
    private Integer source;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 询价方式
     */
    private Integer communication;

    /**
     * 注释
     */
    private String content;

    /**
     * 商品分类
     */
    private Integer goodsCategory;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品型号
     */
    private String goodsBrand;

    /**
     * 分配时间
     */
    private Long assignTime;

    /**
     * 首次查看时间
     */
    private Long firstViewTime;

    /**
     * 商机状态0未处理、1报价中、2已报价、3已订单、4已关闭
     */
    private Integer status;


    /**
     * 商机等级
     */
    private Integer bussinessLevel;

    /**
     * 商机阶段
     */
    private Integer bussinessStage;

    /**
     * 询价类型
     */
    private Integer enquiryType;

    /**
     * 成单几率
     */
    private Integer orderRate;

    /**
     * 预计金额
     */
    private BigDecimal amount;

    /**
     * 报价金额
     */
    private BigDecimal quoteAmount;

    /**
     * 订单成单金额
     */
    private BigDecimal saleorderAmount;

    /**
     * 预计成单时间
     */
    private Long orderTime;

    /**
     * 0：老商机，1：新商机
     */
    private Integer isNew;

    /**
     * 合并状态，0未合并，1被合并，2合并其他的
     */
    private Integer mergeStatus;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 是否大项目 0否1是
     */
    private Integer isBigProject;

    /**
     * 是否包含核心商品
     */
    private Integer hasCoreSpu;

    /**
     * 是否大商机 0小1大
     */
    private Integer isBigChance;

    /**
     * 商机金额 该商机的报价金额，若无，则取预计金额
     */
    private BigDecimal bussinessAmount;

    /**
     * 关联报价单ID
     */
    private Integer quoteorderId;

    /**
     * 关联报价单号
     */
    private String quoteorderNo;

    /**
     * 订单ID
     */
    private Integer saleorderId;

    /**
     * 关联订单号
     */
    private String relatedSaleorderNo;

    /**
     * 采购时间
     */
    private Long purchasingTime;

    /**
     * 采购类型
     */
    private Integer purchasingType;

    /**
     * 是否采购关键人
     */
    private Integer isPolicymaker;

    /**
     * 客户名称
     */
    private String checkTraderName;

    /**
     * 客户地区
     */
    private String checkTraderArea;

    /**
     * 客户联系人
     */
    private String checkTraderContactName;

    /**
     * 客户联系人手机
     */
    private String checkTraderContactMobile;

    /**
     * 客户联系人电话
     */
    private String checkTraderContactTelephone;

    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 联系人
     */
    private String traderContactName;

    /**
     * 联系电话
     */
    private String telephone;

    private static final long serialVersionUID = 1L;
}