package com.vedeng.erp.saleorder.api;

/**
 * vs、商机生效后联系人自动注册
 *
 * <AUTHOR>
 * @date 2021/12/27 8:50
 **/
public interface AutoRegistrationService {

    /**
     * 检查联系人 是否注册
     * @param mobile 联系人手机
     * @return true 已经注册 false 未注册
     */
    boolean checkTraderContactRegistration(String mobile);

    /**
     * 发送前台注册
     * @param registrationData webAccount的jsonString
     * @return true 成功发起注册 false 失败
     */
    boolean sendFrontDeskRegistration(String registrationData);

    /**
     * 判断 客户归属平台贝登医疗，客户性质为分销
     * @return boolean
     */
    boolean checkTraderBelongAndNature(Integer traderId);

    /**
     * 查询当前客户的归属销售
     * @param traderId 客户id
     * @return 销售id <b>不存在则为null<b/>
     */
    Integer relatedSellerByTraderId(Integer traderId);

    /**
     * 根据客户联系人去判断是否要注测，并执行注册
     * @param traderContactId 联系人id
     * @param isSaleOrder 区分订单，商机自动注册，true 订单，false 商机
     */
    void doRegistration(Integer traderContactId,boolean isSaleOrder);
}
