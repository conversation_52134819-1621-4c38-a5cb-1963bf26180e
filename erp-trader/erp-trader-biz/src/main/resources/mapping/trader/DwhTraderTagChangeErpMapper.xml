<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.DwhTraderTagChangeErpMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.DwhTraderTagChangeErpEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="SOURCE" jdbcType="INTEGER" property="source" />
    <result column="OPER_TIME" jdbcType="TIMESTAMP" property="operTime" />
    <result column="OPER_TYPE" jdbcType="INTEGER" property="operType" />
    <result column="TAG_MODEL_NAME" jdbcType="VARCHAR" property="tagModelName" />
    <result column="TAG_NAME" jdbcType="VARCHAR" property="tagName" />
    <result column="OLD_TAG_LABEL" jdbcType="VARCHAR" property="oldTagLabel" />
    <result column="NEW_TAG_LABEL" jdbcType="VARCHAR" property="newTagLabel" />
    <result column="TAG_CHANGE_LOG" jdbcType="VARCHAR" property="tagChangeLog" />
    <result column="ETL_DAY" jdbcType="DATE" property="etlDay" />
  </resultMap>
  <insert id="insert" parameterType="com.vedeng.erp.trader.domain.entity.DwhTraderTagChangeErpEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    insert into DWH_TRADER_TAG_CHANGE_ERP (TRADER_ID, TRADER_CUSTOMER_ID, `SOURCE`, 
      OPER_TIME, OPER_TYPE, TAG_MODEL_NAME, 
      TAG_NAME, OLD_TAG_LABEL, NEW_TAG_LABEL, 
      TAG_CHANGE_LOG, ETL_DAY)
    values (#{traderId,jdbcType=INTEGER}, #{traderCustomerId,jdbcType=INTEGER}, #{source,jdbcType=INTEGER}, 
      #{operTime,jdbcType=TIMESTAMP}, #{operType,jdbcType=INTEGER}, #{tagModelName,jdbcType=VARCHAR}, 
      #{tagName,jdbcType=VARCHAR}, #{oldTagLabel,jdbcType=VARCHAR}, #{newTagLabel,jdbcType=VARCHAR}, 
      #{tagChangeLog,jdbcType=VARCHAR}, #{etlDay,jdbcType=DATE})
  </insert>
  <select id="selectAll" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select TRADER_ID, TRADER_CUSTOMER_ID, `SOURCE`, OPER_TIME, OPER_TYPE, TAG_MODEL_NAME, 
    TAG_NAME, OLD_TAG_LABEL, NEW_TAG_LABEL, TAG_CHANGE_LOG, ETL_DAY
    from DWH_TRADER_TAG_CHANGE_ERP
  </select>
  <select id="selectByOperateTime"
          resultType="com.vedeng.erp.trader.domain.entity.DwhTraderTagChangeErpEntity">
    select TRADER_ID, TRADER_CUSTOMER_ID, `SOURCE`, OPER_TIME, OPER_TYPE, TAG_MODEL_NAME,
           TAG_NAME, OLD_TAG_LABEL, NEW_TAG_LABEL, TAG_CHANGE_LOG, ETL_DAY
    from DWH_TRADER_TAG_CHANGE_ERP
    where OPER_TIME BETWEEN #{operateTimeBefore} AND #{operateTimeAfter}
  </select>
</mapper>