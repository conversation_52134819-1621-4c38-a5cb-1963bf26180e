package com.wms.inventoryadjustment.service.impl;

import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.EmptyUtils;
import com.wms.constant.InventoryAdjustmentTypeEnum;
import com.wms.constant.LogicalEnum;
import com.wms.inventoryadjustment.dao.InventoryAdjustmentMapper;
import com.wms.inventoryadjustment.model.dto.InventoryAdjustmentSearchDto;
import com.wms.inventoryadjustment.model.vo.InventoryAdjustmentDetailVo;
import com.wms.inventoryadjustment.model.vo.InventoryAdjustmentVo;
import com.wms.inventoryadjustment.service.InventoryAdjustmentService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 库存调整单服务实现类
 *
 * <AUTHOR>
 * @date 2020/7/29 13:19:28
 */
@Service("inventoryAdjustmentService")
public class InventoryAdjustmentServiceImpl extends BaseServiceimpl implements InventoryAdjustmentService {
    @Autowired
    private InventoryAdjustmentMapper inventoryAdjustmentMapper;

    /**
     * @return
     * @describe 获取库存调整单信息
     * <AUTHOR>
     * @date 2020/7/29 13:16:15
     */
    @Override
    public Map<String, Object> getInventoryAdjustmentVos(InventoryAdjustmentSearchDto inventoryAdjustmentSearchDto, Page page) {
        HashMap<String, Object> queryParams = new HashMap<>(3);
        if (inventoryAdjustmentSearchDto != null && EmptyUtils.isNotBlank(inventoryAdjustmentSearchDto.getSearchBeginTime())) {
            inventoryAdjustmentSearchDto.setStartTime(DateUtil.convertLong(inventoryAdjustmentSearchDto.getSearchBeginTime() + " 00:00:00", DateUtil.TIME_FORMAT));
        }
        if (inventoryAdjustmentSearchDto != null && EmptyUtils.isNotBlank(inventoryAdjustmentSearchDto.getSearchEndTime())) {
            inventoryAdjustmentSearchDto.setEndTime(DateUtil.convertLong(inventoryAdjustmentSearchDto.getSearchEndTime() + " 23:59:59", DateUtil.TIME_FORMAT));
        }
        queryParams.put("inventoryAdjustmentSearchDto", inventoryAdjustmentSearchDto);
        queryParams.put("page", page);

        HashMap<String, Object> resultMap = new HashMap<>(3);
        List<InventoryAdjustmentVo> inventoryAdjustmentVos = inventoryAdjustmentMapper.getInventoryAdjustmentVosListPage(queryParams);

        if (CollectionUtils.isNotEmpty(inventoryAdjustmentVos)) {
            inventoryAdjustmentVos.stream().forEach(inventoryAdjustmentVo -> {
                inventoryAdjustmentVo.setTypeStr(InventoryAdjustmentTypeEnum.getTypeStr(inventoryAdjustmentVo.getType()));
            });
        }

        resultMap.put("inventoryAdjustmentVos", inventoryAdjustmentVos);
        resultMap.put("page", page);
        return resultMap;
    }

    /**
     * 通过ID获取库存调整单信息
     *
     * @param inventoryAdjustmentId
     * @return
     * <AUTHOR>
     * @date 2020/7/29 13:40:28
     */
    @Override
    public InventoryAdjustmentVo getInventoryAdjustmentById(Integer inventoryAdjustmentId) {
        InventoryAdjustmentVo inventoryAdjustmentVo = inventoryAdjustmentMapper.getInventoryAdjustmentById(inventoryAdjustmentId);
        if (inventoryAdjustmentVo != null) {
            inventoryAdjustmentVo.setTypeStr(InventoryAdjustmentTypeEnum.getTypeStr(inventoryAdjustmentVo.getType()));
        }
        return inventoryAdjustmentVo;
    }

    /**
     * @return
     * @describe 获取库存调整单详情信息
     * <AUTHOR>
     * @date 2020/7/29 13:17:15
     */
    @Override
    public List<InventoryAdjustmentDetailVo> getInventoryAdjustmentDetailsById(Integer inventoryAdjustmentId) {
        List<InventoryAdjustmentDetailVo> inventoryAdjustmentDetailVos = inventoryAdjustmentMapper.getInventoryAdjustmentDetailById(inventoryAdjustmentId);
        if (CollectionUtils.isNotEmpty(inventoryAdjustmentDetailVos)) {
            inventoryAdjustmentDetailVos.stream().forEach(inventoryAdjustmentDetailVo -> {
                inventoryAdjustmentDetailVo.setLogicalWarehouseStr(LogicalEnum.getLogicalWarehouseName(inventoryAdjustmentDetailVo.getLogicalWarehouseId()));
            });
        }
        return inventoryAdjustmentDetailVos;
    }
}
