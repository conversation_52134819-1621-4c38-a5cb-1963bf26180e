package com.vedeng.erp.buyorderexpense.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.buyorder.dto.BuyOrderSaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,builder = @Builder(disableBuilder = true))
public interface SaleOrderGoodsDetailConvertor extends BaseMapStruct<SaleOrderGoodsDetailDto,BuyOrderSaleOrderGoodsDetailDto> {

}
