package com.vedeng.orderstream.aftersales.model;

import java.util.Date;

public class AfterSalesFollowUpRecord {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_FOLLOW_UP_RECORD.RECORD_ID
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    private Integer recordId;
    
    private Date nextTime;
    
    private Integer isOver;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_FOLLOW_UP_RECORD.AFTER_SALES_ID
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    private Integer afterSalesId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_FOLLOW_UP_RECORD.ORG_ID
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    private Integer orgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_FOLLOW_UP_RECORD.CONTENT
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    private String content;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_FOLLOW_UP_RECORD.OPERATIONAL_MATTERS
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    private String operationalMatters;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_FOLLOW_UP_RECORD.ADD_TIME
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    private Long addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_FOLLOW_UP_RECORD.MODE_TIME
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    private Long modeTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_FOLLOW_UP_RECORD.CREATOR
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_FOLLOW_UP_RECORD.UPDATER
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_AFTER_SALES_FOLLOW_UP_RECORD.IS_DELETE
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    private Integer isDelete;


    public Date getNextTime() {
        return nextTime;
    }

    public void setNextTime(Date nextTime) {
        this.nextTime = nextTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.RECORD_ID
     *
     * @return the value of T_AFTER_SALES_FOLLOW_UP_RECORD.RECORD_ID
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public Integer getRecordId() {
        return recordId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.RECORD_ID
     *
     * @param recordId the value for T_AFTER_SALES_FOLLOW_UP_RECORD.RECORD_ID
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public void setRecordId(Integer recordId) {
        this.recordId = recordId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.AFTER_SALES_ID
     *
     * @return the value of T_AFTER_SALES_FOLLOW_UP_RECORD.AFTER_SALES_ID
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public Integer getAfterSalesId() {
        return afterSalesId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.AFTER_SALES_ID
     *
     * @param afterSalesId the value for T_AFTER_SALES_FOLLOW_UP_RECORD.AFTER_SALES_ID
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public void setAfterSalesId(Integer afterSalesId) {
        this.afterSalesId = afterSalesId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.ORG_ID
     *
     * @return the value of T_AFTER_SALES_FOLLOW_UP_RECORD.ORG_ID
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public Integer getOrgId() {
        return orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.ORG_ID
     *
     * @param orgId the value for T_AFTER_SALES_FOLLOW_UP_RECORD.ORG_ID
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public void setOrgId(Integer orgId) {
        this.orgId = orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.CONTENT
     *
     * @return the value of T_AFTER_SALES_FOLLOW_UP_RECORD.CONTENT
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.CONTENT
     *
     * @param content the value for T_AFTER_SALES_FOLLOW_UP_RECORD.CONTENT
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public void setContent(String content) {
        this.content = content;
    }

    public Integer getIsOver() {
        return isOver;
    }

    public void setIsOver(Integer isOver) {
        this.isOver = isOver;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.OPERATIONAL_MATTERS
     *
     * @return the value of T_AFTER_SALES_FOLLOW_UP_RECORD.OPERATIONAL_MATTERS
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public String getOperationalMatters() {
        return operationalMatters;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.OPERATIONAL_MATTERS
     *
     * @param operationalMatters the value for T_AFTER_SALES_FOLLOW_UP_RECORD.OPERATIONAL_MATTERS
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public void setOperationalMatters(String operationalMatters) {
        this.operationalMatters = operationalMatters;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.ADD_TIME
     *
     * @return the value of T_AFTER_SALES_FOLLOW_UP_RECORD.ADD_TIME
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.ADD_TIME
     *
     * @param addTime the value for T_AFTER_SALES_FOLLOW_UP_RECORD.ADD_TIME
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.MODE_TIME
     *
     * @return the value of T_AFTER_SALES_FOLLOW_UP_RECORD.MODE_TIME
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public Long getModeTime() {
        return modeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.MODE_TIME
     *
     * @param modeTime the value for T_AFTER_SALES_FOLLOW_UP_RECORD.MODE_TIME
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public void setModeTime(Long modeTime) {
        this.modeTime = modeTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.CREATOR
     *
     * @return the value of T_AFTER_SALES_FOLLOW_UP_RECORD.CREATOR
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.CREATOR
     *
     * @param creator the value for T_AFTER_SALES_FOLLOW_UP_RECORD.CREATOR
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.UPDATER
     *
     * @return the value of T_AFTER_SALES_FOLLOW_UP_RECORD.UPDATER
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.UPDATER
     *
     * @param updater the value for T_AFTER_SALES_FOLLOW_UP_RECORD.UPDATER
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.IS_DELETE
     *
     * @return the value of T_AFTER_SALES_FOLLOW_UP_RECORD.IS_DELETE
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_AFTER_SALES_FOLLOW_UP_RECORD.IS_DELETE
     *
     * @param isDelete the value for T_AFTER_SALES_FOLLOW_UP_RECORD.IS_DELETE
     *
     * @mbggenerated Tue Oct 12 11:17:55 CST 2021
     */
    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }
}
