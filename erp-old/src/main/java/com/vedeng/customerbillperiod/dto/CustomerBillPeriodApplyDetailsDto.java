package com.vedeng.customerbillperiod.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: daniel
 * @Date: 2021/7/23 18 18
 * @Description: 客户账期申请记录详情
 */
@Data
public class CustomerBillPeriodApplyDetailsDto {

    private String customerName;

    private Long customerId;

    private Integer customerNature;

    private Integer customerType;

    /**
     * 账期申请人
     */
    private Integer creator;

    /**
     * 申请时间
     */
    private Long addTime;

    /**
     * 账期类型，1正式账期，2临时账期，3订单账期 @CustomerBillPeriodTypeEnum
     */
    private Integer billPeriodType;

    /**
     * 申请操作类型，1新增，2调整  @CustomerBillPeriodApplyTypeEnum
     */
    private Integer operateType;

    /**
     * 之前的账期申请额度
     */
    private BigDecimal beforeApplyAmount;

    /**
     * 可用额度
     */
    private BigDecimal availableAmount;

    /**
     * 逾期次数
     */
    private Integer countOfOverdue;


    /**
     * 逾期金额
     */
    private BigDecimal overdueAmount;


    /**
     * 账期原生效开始时间
     */
    private Long beforeBillPeriodStart;

    /**
     * 账期原生效截止时间
     */
    private Long beforeBillPeriodEnd;

    /**
     * 原结算周期
     */
    private Integer beforeSettlementPeriod;

    /**
     * 本次申请额度
     */
    private BigDecimal applyAmount;

    /**
     * 本次申请结算周期
     */
    private Integer settlementPeriod;

    private Long billPeriodStart;

    private Long billPeriodEnd;

    private Integer checkStatus;

    private Long billPeriodApplyId;
}
