package com.vedeng.erp.trader.service;

import com.vedeng.authorization.model.User;
import com.vedeng.erp.trader.domain.dto.SkuSupplyAuthDetail;
import com.vedeng.erp.trader.domain.dto.TraderSupplierDto;
import com.vedeng.erp.trader.domain.vo.SkuSupplyAuthVo;
import com.vedeng.infrastructure.file.domain.Attachment;

import java.math.BigDecimal;
import java.util.List;

public interface TraderSupplierBizService {

    /**
     * 影响范围：/trader/tradersupplier/authList 供应商详情页授权书信息
     *
     * @param traderSupplierId
     * @return
     * <AUTHOR>
     * @desc 根据供应商id查询授权书信息
     */
    List<SkuSupplyAuthVo> queryAuthInfoByTraderSupplierId(Integer traderSupplierId);

    /**
     * 影响范围：/trader/tradersupplier/authPictureDel 供应商详情页删除授权书附件图片
     *
     * @param attachmentId
     * @param user
     * <AUTHOR>
     * @desc 根据主键id作废附件
     */
    void delAttachmentByPrimaryKey(Integer attachmentId, User user);

    /**
     * 影响范围 /trader/tradersupplier/authPictureAdd 供应商详情页新增授权书附件
     *
     * @param attachment
     * @param user
     * <AUTHOR>
     * @desc 新增供应商授权书类型附件
     */
    Attachment addTraderSupplierAuthAttachment(Attachment attachment, User user);

    /**
     * 影响范围：/trader/tradersupplier/authRemove 供应商详情页授权书列表删除授权书信息
     *
     * @param skuApplyAuthId
     * @param user
     * <AUTHOR>
     * @desc 根据主键id删除授权书信息
     */
    void delSkuSupplyAuthById(Integer skuApplyAuthId, User user);

    /**
     * 批量更新sku关联信息
     *
     * @param paramSkuList 关联sku集合
     */
    int batchInsertRelatedSku(List<SkuSupplyAuthDetail> paramSkuList);

    /**
     * 根据授权书id查询所有未删除的sku信息
     *
     * @param skuSupplyAuthId 授权书id
     * @return List<SkuSupplyAuthDetail>
     */
    List<SkuSupplyAuthDetail> selectRelatedSkuByAuthId(Integer skuSupplyAuthId);

    /**
     * 保存删除关联商品
     *
     * @param skuSupplyAuthDetailIdList skuSupplyAuthDetailIdList 关联商品id集合
     * @param updater                   操作人
     */
    void saveDeleteRelatedSku(List<Integer> skuSupplyAuthDetailIdList, Integer updater);


    /**
     * 根据traderId获取供应商基本信息
     *
     * @param traderId 交易者ID
     * @return TraderSupplierDto
     */
    TraderSupplierDto getTraderSupplierInfoById(Integer traderId);



}
