package com.vedeng.trader.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.trader.dao.LandLineRecordMapper;
import com.vedeng.trader.enums.LandLineCodeEnum;
import com.vedeng.trader.model.CommunicateRecord;
import com.vedeng.trader.model.LandLineCallRecord;
import com.vedeng.trader.service.LandLineRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 座机通话记录Service
 *
 * <AUTHOR>
 */
@Service("landLineRecordService")
public class LandLineRecordServiceImpl extends BaseServiceimpl implements LandLineRecordService {
    public static Logger logger = LoggerFactory.getLogger(LandLineRecordServiceImpl.class);

    @Resource
    private LandLineRecordMapper landLineRecordMapper;


    @Override
    public int saveLandLineRecordByCommunicate(CommunicateRecord communicateRecord) {
        logger.info("saveLandLineRecordByCommunicate communicateRecord:{}", JSON.toJSONString(communicateRecord));
        if (communicateRecord == null || communicateRecord.getLineCode() == null){
            return 0;
        }

        if (LandLineCodeEnum.NUMBER_POOL.getLineCode().equals(communicateRecord.getLineCode())) {
            logger.info("号码池通话记录暂时跳过 communicateRecord:{}", JSON.toJSONString(communicateRecord));
            return 0;
        }

        LandLineCallRecord landLineCallRecord = new LandLineCallRecord();
        landLineCallRecord.setPhone(communicateRecord.getPhone());

        landLineCallRecord.setCallingNumber(communicateRecord.getCallingNumber());
        landLineCallRecord.setLineCode(communicateRecord.getLineCode());
        landLineCallRecord.setCoid(communicateRecord.getCoid());
        //呼出类型
        landLineCallRecord.setCoidType(communicateRecord.getCoidType());
        landLineCallRecord.setIsEnable(ErpConst.ONE);
        landLineCallRecord.setCreator(communicateRecord.getCreator());
        landLineCallRecord.setAddTime(System.currentTimeMillis());
        logger.info("保存座机呼出信息 landLineCallRecord:{}", JSON.toJSONString(landLineCallRecord));
        return landLineRecordMapper.insert(landLineCallRecord);
    }
}
