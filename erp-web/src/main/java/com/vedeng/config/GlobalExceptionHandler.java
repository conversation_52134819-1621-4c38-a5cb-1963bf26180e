package com.vedeng.config;

import cn.hutool.json.JSONUtil;
import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 全局异常处理类
 * @date 2022/5/18 9:37
 */
@ControllerAdvice(annotations = ExceptionController.class)
@ResponseBody
@Slf4j
public class GlobalExceptionHandler {

    /**
     * 捕捉系统异常
     *
     * @param e :
     * @return com.bluewhitetoy.system.common.entity.CommonResponse<?>
     * <AUTHOR>
     * @date 2020-09-10 上午 10:41
     */
    @ExceptionHandler(Exception.class)
    public R handleIllegalArgumentException(Exception e) {
        log.error("系统异常", e);
        return R.error(BaseResponseCode.SYSTEM_BUSY);
    }

    /**
     * 捕捉自定义业务异常
     *
     * @param request :
     * @param ex      :
     * @return com.bluewhitetoy.system.common.entity.CommonResponse<?>
     * <AUTHOR>
     * @date 2020-09-10 上午 10:41
     */
    @ExceptionHandler(ServiceException.class)
    public R serviceException(HttpServletRequest request, ServiceException ex) {
        log.info("业务异常 {}", ex.getMessage(), ex);
        return R.error(getStatus(request).value(), ex.getMessage());
    }


    /**
     * 捕捉请求异常
     *
     * @param e :
     * @return com.bluewhitetoy.system.common.entity.CommonResponse<?>
     * <AUTHOR>
     * @date 2020-09-10 上午 10:41
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public R handleIllegalArgumentException(HttpRequestMethodNotSupportedException e) {
        log.error("【全局异常拦截】HttpRequestMethodNotSupportedException: 当前请求方式 {}, 支持请求方式 {}",
                e.getMethod(), JSONUtil.toJsonStr(e.getSupportedHttpMethods()));
        return R.error(BaseResponseCode.HTTP_BAD_METHOD);
    }


    /**
     * validation Exception
     *
     * @param exception
     * @return R
     */
    @ExceptionHandler({MethodArgumentNotValidException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R handleBodyValidException(MethodArgumentNotValidException exception) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        log.warn("参数绑定异常,ex = {}", fieldErrors.get(0).getDefaultMessage());
        return R.error(fieldErrors.get(0).getDefaultMessage());
    }

    /**
     * validation Exception (以form-data形式传参)
     *
     * @param exception
     * @return R
     */
    @ExceptionHandler({BindException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R bindExceptionHandler(BindException exception) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        log.warn("参数绑定异常,ex = {}", fieldErrors.get(0).getDefaultMessage());
        return R.error(fieldErrors.get(0).getDefaultMessage());
    }


    private HttpStatus getStatus(HttpServletRequest request) {
        Integer statusCode = (Integer) request.getAttribute("javax.servlet.error.status_code");
        if (statusCode == null) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
        return HttpStatus.valueOf(statusCode);
    }


}
