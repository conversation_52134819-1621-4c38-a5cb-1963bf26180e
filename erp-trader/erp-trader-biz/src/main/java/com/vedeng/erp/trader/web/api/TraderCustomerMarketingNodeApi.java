package com.vedeng.erp.trader.web.api;

import cn.hutool.core.lang.Assert;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.trader.dto.TraderCustomerMarketingNodeDto;
import com.vedeng.erp.trader.service.TraderCustomerMarketingNodeApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/9 13:16
 **/
@ExceptionController
@RestController
@RequestMapping("/traderCustomerMarketingNodeApi")
@Slf4j
public class TraderCustomerMarketingNodeApi {

    @Autowired
    private TraderCustomerMarketingNodeApiService traderCustomerMarketingNodeApiService;

    /**
     * 获取分类
     * @param type 类型 6终端 5 经销
     * @param isSort 是否排序
     * @return
     */
    @RequestMapping(value = "/query")
    @NoNeedAccessAuthorization
    public R<List<TraderCustomerMarketingNodeDto>> traderCustomerMarketingNode(Integer type, boolean isSort) {

        return R.success(traderCustomerMarketingNodeApiService.getTraderCustomerMarketList(type,isSort));
    }


    /**
     * 获取机构类型的子类
     * @param threeCustomerType 三级
     * @param institutionType 机构类型
     * @return
     */
    @RequestMapping(value = "/getInstitutionTypeChild")
    @NoNeedAccessAuthorization
    public R<List<TraderCustomerMarketingNodeDto.Node>> getInstitutionTypeChild(Integer threeCustomerType,Integer institutionType) {
        Assert.notNull(threeCustomerType,"参数不可为空");
        Assert.notNull(institutionType,"参数不可为空");
        return R.success(traderCustomerMarketingNodeApiService.getInstitutionTypeChild(threeCustomerType,institutionType));
    }




}
