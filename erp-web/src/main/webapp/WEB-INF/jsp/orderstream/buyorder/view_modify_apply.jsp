<%--
  Created by IntelliJ IDEA.
  User: Simple
  Date: 2021/10/26
  Time: 13:43
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="订单修改详情" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script src="${pageContext.request.contextPath}/static/js/orderstream/index.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/order/style.css">
<script type="text/javascript">
    $(function () {
        let buyorderModifyApplyId = $("#buyorderModifyApplyId").val();
        let url = page_url + "/order/newBuyorder/viewModifyApply.do?buyorderModifyApplyId=" + buyorderModifyApplyId;
        if ($(window.frameElement).attr('src').indexOf("viewModifyApply") < 0) {
            $(window.frameElement).attr('data-url', url);
        }
    });

    let data_json = [
        {
            label: 'XXX', //标签文字
            jump: '#XXX', //需要跳转的元素的ID
            tip: '', //文字下面的备注
            lock: '', //是否需要上面的锁和文字
            status: 3 //当前的状态，默认是0  0：未进行 1：已进行 2：正在进行 3：正在同步进行
        }];
</script>
<div class="content mt10 ">
    <%--  进度条  --%>
    <div class="parts" style="margin-bottom: 0px;padding:0 0 0 0;">
        <c:if test="${bmav.verifyStatus eq null}">
            <div class="t-line-wrap J-line-wrap " style="padding:0px 15% 10px 15%;"
                 data-json='[{"label":"待审核","status":2},{"label":"审核中","status":0},{"label":"已完成","status":0}]'></div>
        </c:if>
        <c:if test="${bmav.verifyStatus eq 0}">
            <div class="t-line-wrap J-line-wrap " style="padding:0px 15% 10px 15%;"
                 data-json='[{"label":"待审核","status":1},{"label":"审核中","status":2},{"label":"已完成","status":0}]'></div>
        </c:if>
        <c:if test="${bmav.verifyStatus eq 1 or bmav.verifyStatus eq 2}">
            <div class="t-line-wrap J-line-wrap " style="padding:0px 15% 10px 15%;"
                 data-json='[{"label":"待审核","status":1},{"label":"审核中","status":1},{"label":"已完成","status":2}]'></div>
        </c:if>
    </div>
    <%--  操作项  --%>
    <div class="parts">
        <div class="table-buttons" style="text-align: center;margin-right: 15%">
            <c:choose>
                <c:when test="${bmav.validStatus eq 0}">
                    <c:if test="${(null!=taskInfo and null!=taskInfo.getProcessInstanceId() and null!=taskInfo.assignee) or !empty candidateUserMap[taskInfo.id]}">
                        <c:set var="shenhe" value="0"></c:set>
                        <c:forEach items="${verifyUserList}" var="verifyUsernameInfo">
                            <c:if test="${verifyUsernameInfo == curr_user.username}">
                                <c:set var="shenhe" value="1"></c:set>
                            </c:if>
                        </c:forEach>
                        <c:choose>
                            <c:when test="${(taskInfo.assignee == curr_user.username or candidateUserMap['belong']) and shenhe!=1}">
                                <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data"
                                        style="background-color: #09f"
                                        layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=true&type=1&buyorderId=${bmav.buyorderId}"}'>
                                    审核通过
                                </button>
                                <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 pop-new-data"
                                        style="background-color: #09f"
                                        layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=false&type=1&buyorderId=${bmav.buyorderId}"}'>
                                    审核不通过
                                </button>
                            </c:when>
                            <c:otherwise>
                                <button type="button" class="bt-bg-style bt-small bg-light-greybe mr10">已申请审核
                                </button>
                            </c:otherwise>
                        </c:choose>
                    </c:if>
                </c:when>
                <c:otherwise>
                </c:otherwise>
            </c:choose>
        </div>
    </div>
    <%--  基本信息  --%>
    <div class="parts" style="margin-top: 0px;">
        <div>
            <div class="title-container title-container-gray">
                <div class="table-title nobor">
                    基本信息
                </div>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <tbody>
                <tr>
                    <td>修改申请单号</td>
                    <td>${bmav.buyorderModifyApplyNo}</td>
                    <td>采购订单</td>
                    <td><c:if test="${bmav.isGift == 1}"><span style="color:#FF0000">赠品订单</span></c:if>${bmav.buyorderNo}</td>
                </tr>
                <tr>
                    <td>审核状态</td>
                    <td><c:choose>
                        <c:when test="${bmav.verifyStatus == null}">待审核</c:when>
                        <c:when test="${bmav.verifyStatus eq 0}">审核中</c:when>
                        <c:when test="${bmav.verifyStatus eq 1}">审核通过</c:when>
                        <c:when test="${bmav.verifyStatus eq 2}">审核不通过</c:when>
                        <c:otherwise></c:otherwise>
                    </c:choose></td>
                    <td>创建时间</td>
                    <td><date:date value="${bmav.addTime}"/></td>
                </tr>
                <tr>
                    <td>创建人</td>
                    <td>${bmav.creatorName}</td>
                    <td></td>
                    <td></td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <%--  下划线  --%>
<%--    <div class="parts" style="margin-top: 50px;">--%>
<%--        <hr style="color: snow;"/>--%>
<%--    </div>--%>
<%--    <br>--%>
    <%--  审核进度  --%>
    <div class="parts">
        <div class="parts">
            <div class="title-container" style="background-color: white;border: none">
                <div class="table-title nobor" style="font-size: 1.1em">
                    审核进度
                </div>
            </div>
            <div style="padding:0px 0 60px 0;">
                <div class="t-line-wrap J-line-wrap " style="padding:24px 15% 10px 15%;"
                     data-json='${statusNodes}'></div>
            </div>
        </div>
    </div>
    <%--  收货信息  --%>
    <div class="parts">
        <div class="title-container title-container-gray">
            <div class="table-title nobor">
                收货信息
            </div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td>是否直发</td>
                <td>
                    <div class="customername pos_rel">
                                <span>
                                	<c:if test="${bmav.deliveryDirect eq 0}">普发</c:if>
                                	<c:if test="${bmav.deliveryDirect eq 1}">直发</c:if>
                                <c:if test="${bmav.deliveryDirect ne bmav.oldDeliveryDirect}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：<c:if test="${bmav.oldDeliveryDirect eq 0}">普发</c:if>
                            <c:if test="${bmav.oldDeliveryDirect eq 1}">直发</c:if></div>
                        </c:if>
                    </div>
                </td>
                <td>发货方式修改原因</td>
                <td>${bmav.deliveryDirectChangeReason}</td>
            </tr>
            <tr>
                <td class="table-smaller">收货客户</td>
                <td>${bmav.takeTraderName}</td>
                <td class="table-smaller">收货联系人</td>
                <td>
                    <div class="customername pos_rel">
                                <span>${bmav.takeTraderContactName}

                                <c:if test="${bmav.takeTraderContactName ne bmav.oldTakeTraderContactName}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：${bmav.oldTakeTraderContactName}</div>
                        </c:if>
                    </div>
                </td>
            </tr>
            <tr>
                <td>电话</td>
                <td>
                    <div class="customername pos_rel">
                                <span>${bmav.takeTraderContactTelephone}

                                <c:if test="${bmav.takeTraderContactTelephone ne bmav.oldTakeTraderContactTelephone}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：${bmav.oldTakeTraderContactTelephone}</div>
                        </c:if>
                    </div>
                </td>
                <td>手机</td>
                <td>
                    <div class="customername pos_rel">
                                <span>${bmav.takeTraderContactMobile}

                                <c:if test="${bmav.takeTraderContactMobile ne bmav.oldTakeTraderContactMobile}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：${bmav.oldTakeTraderContactMobile}</div>
                        </c:if>
                    </div>
                </td>
            </tr>
            <tr>
                <td>收货地址</td>
                <td>
                    <div class="customername pos_rel">
                                <span>${bmav.takeTraderArea}

                                <c:if test="${bmav.takeTraderArea ne bmav.oldTakeTraderArea}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：${bmav.oldTakeTraderArea}</div>
                        </c:if>
                    </div>
                    <div class="customername pos_rel">
                                <span>${bmav.takeTraderAddress}

                                <c:if test="${bmav.takeTraderAddress ne bmav.oldTakeTraderAddress}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：${bmav.oldTakeTraderAddress}</div>
                        </c:if>
                    </div>
                </td>
                <td>物流备注</td>
                <td>
                    <div class="customername pos_rel">
                                <span>${bmav.logisticsComments}

                                <c:if test="${bmav.logisticsComments ne bmav.oldLogisticsComments}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：${bmav.oldLogisticsComments}</div>
                        </c:if>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <%--  收票信息  --%>
    <div class="parts">
        <div class="title-container title-container-gray">
            <div class="table-title nobor">
                收票信息
            </div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td>发票类型</td>
                <td>
                    <c:if test="${bmav.oldInvoiceType != 0}">
                        <div class="customername pos_rel">
	                        		<span>
		                        	<c:forEach var="list" items="${invoiceTypes}">
                                        <c:if test="${bmav.invoiceType == list.sysOptionDefinitionId}">${list.title}</c:if>
                                    </c:forEach>
			                    	<c:if test="${bmav.invoiceType ne bmav.oldInvoiceType}">
                                        <i class="iconbluesigh ml4 contorlIcon"></i>
                                        <div class="pos_abs customernameshow">原值：
			                                <c:forEach var="list" items="${invoiceTypes}">
                                                <c:if test="${bmav.oldInvoiceType == list.sysOptionDefinitionId}">${list.title}</c:if>
                                            </c:forEach>
		                            	</div>
                                    </c:if>
		                            </span>
                        </div>
                    </c:if>
                </td>
                <td>收票备注</td>
                <td>
                    <div class="customername pos_rel">
                                <span>${bmav.invoiceComments}

                                <c:if test="${bmav.invoiceComments ne bmav.oldInvoiceComments}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：${bmav.oldInvoiceComments}</div>
                        </c:if>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <%--  产品信息  --%>
    <div class="parts">
        <div class="title-container title-container-gray">
            <div class="table-title nobor">产品信息</div>
        </div>
        <table class="table  table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid2">序号</th>
                <th class="wid6">订货号</th>
                <th class="wid20">产品名称</th>
                <th class="wid10">品牌</th>
                <th class="wid10">规格/型号</th>
                <th class="wid5">单价</th>
                <th class="wid5">赠品参考价</th>
                <th class="wid5">采购数量</th>
                <th class="wid4">单位</th>
                <th class="wid9">采购预计发货日</th>
                <th class="wid9">采购预计到货日</th>
                <th class="wid9">货期(天)</th>
                <th class="wid4">是否有授权</th>
                <th class="wid15 rebate_class">
                    <div>
                        返利信息
                    </div>
                </th>
                <th class="wid10">采购备注</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach var="bgv" items="${bmav.bgvList}" varStatus="staut">
                <tr>
                    <td>${staut.count}</td>
                    <td>
                        <div>${bgv.sku}</div>
                    </td>
                    <td <%--class="text-left"--%>>
                        <div class="customername pos_rel">
                        <c:if test="${bmav.isGift == 1}"><span style="color:#FF0000">赠品</span></c:if>
						<span class="font-blue">
							<a class="addtitle" href="javascript:void(0);"
                               tabTitle='{"num":"viewgoods${bgv.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${bgv.goodsId}","title":"产品信息"}'>${newSkuInfosMap[bgv.sku].SHOW_NAME}</a>&nbsp;
								<i class="iconbluemouth contorlIcon"></i><br/>
						</span>
                            <c:set var="skuNo" value="${bgv.sku}"></c:set>
                            <%@ include file="../../common/new_sku_common_tip.jsp" %>
                        </div>
                    </td>
                    <td>${newSkuInfosMap[bgv.sku].BRAND_NAME}</td>
                    <td>${newSkuInfosMap[bgv.sku].MODEL}</td>
                    <td>
                        <div class="customername pos_rel"><span><fmt:formatNumber type="number" value="${bgv.newPrice}" pattern="0.00" maxFractionDigits="2" />
                        <c:if test="${bgv.newPrice ne bgv.oldPrice}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：<fmt:formatNumber type="number" value="${bgv.oldPrice}" pattern="0.00" maxFractionDigits="2" /></div>
                            </c:if></div>
                    </td>
                    <td>
                        <div class="customername pos_rel"><span><fmt:formatNumber type="number" value="${bgv.referPrice}" pattern="0.00" maxFractionDigits="2" />
                        <c:if test="${bgv.referPrice ne bgv.oldReferPrice}">
                            <i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：<fmt:formatNumber type="number" value="${bgv.oldReferPrice}" pattern="0.00" maxFractionDigits="2" /></div>
                            </c:if></div>
                    </td>
                    <td>${bgv.num}</td>
                    <td>${newSkuInfosMap[bgv.sku].UNIT_NAME}</td>
                    <td><date:date value="${bgv.sendGoodsTime}" format="yyyy-MM-dd"/></td>
                    <td><date:date value="${bgv.receiveGoodsTime}" format="yyyy-MM-dd"/></td>
                    <td>
                        <div class="customername pos_rel">
                             <span>${bgv.newDeliveryCycle}
                      	<c:if test="${bgv.newDeliveryCycle ne bgv.oldDeliveryCycle}">
							<i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：${bgv.oldDeliveryCycle}</div>
                            </c:if>
                        </div>
                    </td>

                    <td>
                        <c:if test="${bgv.sku ne 'V127063'}">
                            <div class="customername pos_rel">
                             <span>${bgv.newIsHaveAuth eq 1 ? '是' : '否'}
                      	<c:if test="${bgv.newIsHaveAuth ne bgv.oldIsHaveAuth}">
							<i class="iconbluesigh ml4 contorlIcon"></i></span>
                                <div class="pos_abs customernameshow">原值：${bgv.oldIsHaveAuth eq 1 ? '是' : '否'}</div>
                                </c:if>
                            </div>
                        </c:if>

                    </td>
                    <td class="text-left">
                        <span style="display: block">返利总额:<span name="totalRebate" id="totalRebate${bgv.goodsId}">${bgv.rebateAmount}</span></span>
                        <span style="display: block">返利单价:<span id="rebatePrice${bgv.goodsId}">${bgv.rebatePrice}</span></span>
                        <span style="display: block">返利后单价:<span id="afterRebatePrice${bgv.goodsId}">${bgv.rebateAfterPrice}</span></span>
                    </td>
                    <td>
                        <div class="customername pos_rel">
                             <span>${bgv.newInsideComments}
                      	<c:if test="${bgv.newInsideComments ne bgv.insideComments}">
							<i class="iconbluesigh ml4 contorlIcon"></i></span>
                            <div class="pos_abs customernameshow">原值：${bgv.insideComments}</div>
                            </c:if>
                        </div>
                    </td>
                </tr>
            </c:forEach>
            </tbody>
        </table>
    </div>



        <div class="parts">
            <div class="title-container title-container-gray">
                <div class="table-title nobor">虚拟商品</div>
            </div>
            <table class="table  table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th class="wid2">序号</th>
                    <th class="wid6">订货号</th>
                    <th class="wid20">产品名称</th>
                    <th class="wid10">费用类别</th>
                    <th class="wid10">是否可库存管理</th>
                    <th class="wid5">采购数量</th>
                    <th class="wid5">单价</th>
                    <th class="wid4">总额</th>
                    <th class="wid10">采购备注</th>
                </tr>
                </thead>
                <tbody>
                    <c:forEach var="item" items="${buyorderExpenseItemDtoList}" varStatus="staut">
                        <tr>
                            <td>${staut.count}</td>
                            <td>${item.buyorderExpenseItemDetailDto.sku}</td>

                            <td>${item.buyorderExpenseItemDetailDto.goodsName}</td>
                            <td>${item.buyorderExpenseItemDetailDto.expenseCategoryName}</td>
                            <td>${item.buyorderExpenseItemDetailDto.haveStockManage eq 1 ? '是' : '否'}</td>
                            <td>${item.num}</td>
                            <td>
                                <fmt:formatNumber type="number" value="${item.buyorderExpenseItemDetailDto.price}" pattern="0.00" maxFractionDigits="2"/>
                            </td>
                            <td>
                                <fmt:formatNumber type="number" value="${item.num * item.buyorderExpenseItemDetailDto.price}" pattern="0.00" maxFractionDigits="2"/>
                            </td>

                            <td>
                                <div class="customername pos_rel">
                                 <span>${item.buyorderExpenseItemDetailDto.insideComments}
                                    <c:if test="${item.buyorderExpenseItemDetailDto.insideComments ne item.buyorderExpenseItemDetailDto.oldInsideComments}">
                                        <i class="iconbluesigh ml4 contorlIcon"></i></span>
                                            <div class="pos_abs customernameshow">原值：${item.buyorderExpenseItemDetailDto.oldInsideComments}</div>
                                    </c:if>
                                </div>
                            </td>
                        </tr>
                    </c:forEach>

                    <c:if test="${empty buyorderExpenseItemDtoList}">
                        <tr>
                            <td colspan="9">暂无数据</td>
                        </tr>
                    </c:if>
                </tbody>
            </table>
        </div>

</div>

<input type="hidden" name="saleorderId" value="${saleorder.saleorderId}">
<input type="hidden" id="buyorderModifyApplyId" value="${bmav.buyorderModifyApplyId}"/>
<input type="hidden" id="statusNodes" value='${statusNodes}'/>

<%@ include file="../../common/footer.jsp" %>
