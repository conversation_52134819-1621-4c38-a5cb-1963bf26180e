package com.vedeng.department.model;

import java.util.ArrayList;
import java.util.List;

public class DepartmentsHospitalGenerateExample {
    /**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	protected String orderByClause;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	protected boolean distinct;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	protected List<Criteria> oredCriteria;

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public DepartmentsHospitalGenerateExample() {
		oredCriteria = new ArrayList<Criteria>();
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public String getOrderByClause() {
		return orderByClause;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public void setDistinct(boolean distinct) {
		this.distinct = distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public boolean isDistinct() {
		return distinct;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public List<Criteria> getOredCriteria() {
		return oredCriteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public void or(Criteria criteria) {
		oredCriteria.add(criteria);
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
			oredCriteria.add(criteria);
		}
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
	}

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	protected abstract static class GeneratedCriteria {
		protected List<Criterion> criteria;

		protected GeneratedCriteria() {
			super();
			criteria = new ArrayList<Criterion>();
		}

		public boolean isValid() {
			return criteria.size() > 0;
		}

		public List<Criterion> getAllCriteria() {
			return criteria;
		}

		public List<Criterion> getCriteria() {
			return criteria;
		}

		protected void addCriterion(String condition) {
			if (condition == null) {
				throw new RuntimeException("Value for condition cannot be null");
			}
			criteria.add(new Criterion(condition));
		}

		protected void addCriterion(String condition, Object value, String property) {
			if (value == null) {
				throw new RuntimeException("Value for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value));
		}

		protected void addCriterion(String condition, Object value1, Object value2, String property) {
			if (value1 == null || value2 == null) {
				throw new RuntimeException("Between values for " + property + " cannot be null");
			}
			criteria.add(new Criterion(condition, value1, value2));
		}

		public Criteria andDepartmentIdIsNull() {
			addCriterion("DEPARTMENT_ID is null");
			return (Criteria) this;
		}

		public Criteria andDepartmentIdIsNotNull() {
			addCriterion("DEPARTMENT_ID is not null");
			return (Criteria) this;
		}

		public Criteria andDepartmentIdEqualTo(Integer value) {
			addCriterion("DEPARTMENT_ID =", value, "departmentId");
			return (Criteria) this;
		}

		public Criteria andDepartmentIdNotEqualTo(Integer value) {
			addCriterion("DEPARTMENT_ID <>", value, "departmentId");
			return (Criteria) this;
		}

		public Criteria andDepartmentIdGreaterThan(Integer value) {
			addCriterion("DEPARTMENT_ID >", value, "departmentId");
			return (Criteria) this;
		}

		public Criteria andDepartmentIdGreaterThanOrEqualTo(Integer value) {
			addCriterion("DEPARTMENT_ID >=", value, "departmentId");
			return (Criteria) this;
		}

		public Criteria andDepartmentIdLessThan(Integer value) {
			addCriterion("DEPARTMENT_ID <", value, "departmentId");
			return (Criteria) this;
		}

		public Criteria andDepartmentIdLessThanOrEqualTo(Integer value) {
			addCriterion("DEPARTMENT_ID <=", value, "departmentId");
			return (Criteria) this;
		}

		public Criteria andDepartmentIdIn(List<Integer> values) {
			addCriterion("DEPARTMENT_ID in", values, "departmentId");
			return (Criteria) this;
		}

		public Criteria andDepartmentIdNotIn(List<Integer> values) {
			addCriterion("DEPARTMENT_ID not in", values, "departmentId");
			return (Criteria) this;
		}

		public Criteria andDepartmentIdBetween(Integer value1, Integer value2) {
			addCriterion("DEPARTMENT_ID between", value1, value2, "departmentId");
			return (Criteria) this;
		}

		public Criteria andDepartmentIdNotBetween(Integer value1, Integer value2) {
			addCriterion("DEPARTMENT_ID not between", value1, value2, "departmentId");
			return (Criteria) this;
		}

		public Criteria andDepartmentNameIsNull() {
			addCriterion("DEPARTMENT_NAME is null");
			return (Criteria) this;
		}

		public Criteria andDepartmentNameIsNotNull() {
			addCriterion("DEPARTMENT_NAME is not null");
			return (Criteria) this;
		}

		public Criteria andDepartmentNameEqualTo(String value) {
			addCriterion("DEPARTMENT_NAME =", value, "departmentName");
			return (Criteria) this;
		}

		public Criteria andDepartmentNameNotEqualTo(String value) {
			addCriterion("DEPARTMENT_NAME <>", value, "departmentName");
			return (Criteria) this;
		}

		public Criteria andDepartmentNameGreaterThan(String value) {
			addCriterion("DEPARTMENT_NAME >", value, "departmentName");
			return (Criteria) this;
		}

		public Criteria andDepartmentNameGreaterThanOrEqualTo(String value) {
			addCriterion("DEPARTMENT_NAME >=", value, "departmentName");
			return (Criteria) this;
		}

		public Criteria andDepartmentNameLessThan(String value) {
			addCriterion("DEPARTMENT_NAME <", value, "departmentName");
			return (Criteria) this;
		}

		public Criteria andDepartmentNameLessThanOrEqualTo(String value) {
			addCriterion("DEPARTMENT_NAME <=", value, "departmentName");
			return (Criteria) this;
		}

		public Criteria andDepartmentNameLike(String value) {
			addCriterion("DEPARTMENT_NAME like", value, "departmentName");
			return (Criteria) this;
		}

		public Criteria andDepartmentNameNotLike(String value) {
			addCriterion("DEPARTMENT_NAME not like", value, "departmentName");
			return (Criteria) this;
		}

		public Criteria andDepartmentNameIn(List<String> values) {
			addCriterion("DEPARTMENT_NAME in", values, "departmentName");
			return (Criteria) this;
		}

		public Criteria andDepartmentNameNotIn(List<String> values) {
			addCriterion("DEPARTMENT_NAME not in", values, "departmentName");
			return (Criteria) this;
		}

		public Criteria andDepartmentNameBetween(String value1, String value2) {
			addCriterion("DEPARTMENT_NAME between", value1, value2, "departmentName");
			return (Criteria) this;
		}

		public Criteria andDepartmentNameNotBetween(String value1, String value2) {
			addCriterion("DEPARTMENT_NAME not between", value1, value2, "departmentName");
			return (Criteria) this;
		}

		public Criteria andDescriptionIsNull() {
			addCriterion("DESCRIPTION is null");
			return (Criteria) this;
		}

		public Criteria andDescriptionIsNotNull() {
			addCriterion("DESCRIPTION is not null");
			return (Criteria) this;
		}

		public Criteria andDescriptionEqualTo(String value) {
			addCriterion("DESCRIPTION =", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotEqualTo(String value) {
			addCriterion("DESCRIPTION <>", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionGreaterThan(String value) {
			addCriterion("DESCRIPTION >", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
			addCriterion("DESCRIPTION >=", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionLessThan(String value) {
			addCriterion("DESCRIPTION <", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionLessThanOrEqualTo(String value) {
			addCriterion("DESCRIPTION <=", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionLike(String value) {
			addCriterion("DESCRIPTION like", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotLike(String value) {
			addCriterion("DESCRIPTION not like", value, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionIn(List<String> values) {
			addCriterion("DESCRIPTION in", values, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotIn(List<String> values) {
			addCriterion("DESCRIPTION not in", values, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionBetween(String value1, String value2) {
			addCriterion("DESCRIPTION between", value1, value2, "description");
			return (Criteria) this;
		}

		public Criteria andDescriptionNotBetween(String value1, String value2) {
			addCriterion("DESCRIPTION not between", value1, value2, "description");
			return (Criteria) this;
		}

		public Criteria andIsDeleteIsNull() {
			addCriterion("IS_DELETE is null");
			return (Criteria) this;
		}

		public Criteria andIsDeleteIsNotNull() {
			addCriterion("IS_DELETE is not null");
			return (Criteria) this;
		}

		public Criteria andIsDeleteEqualTo(Integer value) {
			addCriterion("IS_DELETE =", value, "isDelete");
			return (Criteria) this;
		}

		public Criteria andIsDeleteNotEqualTo(Integer value) {
			addCriterion("IS_DELETE <>", value, "isDelete");
			return (Criteria) this;
		}

		public Criteria andIsDeleteGreaterThan(Integer value) {
			addCriterion("IS_DELETE >", value, "isDelete");
			return (Criteria) this;
		}

		public Criteria andIsDeleteGreaterThanOrEqualTo(Integer value) {
			addCriterion("IS_DELETE >=", value, "isDelete");
			return (Criteria) this;
		}

		public Criteria andIsDeleteLessThan(Integer value) {
			addCriterion("IS_DELETE <", value, "isDelete");
			return (Criteria) this;
		}

		public Criteria andIsDeleteLessThanOrEqualTo(Integer value) {
			addCriterion("IS_DELETE <=", value, "isDelete");
			return (Criteria) this;
		}

		public Criteria andIsDeleteIn(List<Integer> values) {
			addCriterion("IS_DELETE in", values, "isDelete");
			return (Criteria) this;
		}

		public Criteria andIsDeleteNotIn(List<Integer> values) {
			addCriterion("IS_DELETE not in", values, "isDelete");
			return (Criteria) this;
		}

		public Criteria andIsDeleteBetween(Integer value1, Integer value2) {
			addCriterion("IS_DELETE between", value1, value2, "isDelete");
			return (Criteria) this;
		}

		public Criteria andIsDeleteNotBetween(Integer value1, Integer value2) {
			addCriterion("IS_DELETE not between", value1, value2, "isDelete");
			return (Criteria) this;
		}

		public Criteria andUpdaterIsNull() {
			addCriterion("UPDATER is null");
			return (Criteria) this;
		}

		public Criteria andUpdaterIsNotNull() {
			addCriterion("UPDATER is not null");
			return (Criteria) this;
		}

		public Criteria andUpdaterEqualTo(Integer value) {
			addCriterion("UPDATER =", value, "updater");
			return (Criteria) this;
		}

		public Criteria andUpdaterNotEqualTo(Integer value) {
			addCriterion("UPDATER <>", value, "updater");
			return (Criteria) this;
		}

		public Criteria andUpdaterGreaterThan(Integer value) {
			addCriterion("UPDATER >", value, "updater");
			return (Criteria) this;
		}

		public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
			addCriterion("UPDATER >=", value, "updater");
			return (Criteria) this;
		}

		public Criteria andUpdaterLessThan(Integer value) {
			addCriterion("UPDATER <", value, "updater");
			return (Criteria) this;
		}

		public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
			addCriterion("UPDATER <=", value, "updater");
			return (Criteria) this;
		}

		public Criteria andUpdaterIn(List<Integer> values) {
			addCriterion("UPDATER in", values, "updater");
			return (Criteria) this;
		}

		public Criteria andUpdaterNotIn(List<Integer> values) {
			addCriterion("UPDATER not in", values, "updater");
			return (Criteria) this;
		}

		public Criteria andUpdaterBetween(Integer value1, Integer value2) {
			addCriterion("UPDATER between", value1, value2, "updater");
			return (Criteria) this;
		}

		public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
			addCriterion("UPDATER not between", value1, value2, "updater");
			return (Criteria) this;
		}

		public Criteria andModTimeIsNull() {
			addCriterion("MOD_TIME is null");
			return (Criteria) this;
		}

		public Criteria andModTimeIsNotNull() {
			addCriterion("MOD_TIME is not null");
			return (Criteria) this;
		}

		public Criteria andModTimeEqualTo(Long value) {
			addCriterion("MOD_TIME =", value, "modTime");
			return (Criteria) this;
		}

		public Criteria andModTimeNotEqualTo(Long value) {
			addCriterion("MOD_TIME <>", value, "modTime");
			return (Criteria) this;
		}

		public Criteria andModTimeGreaterThan(Long value) {
			addCriterion("MOD_TIME >", value, "modTime");
			return (Criteria) this;
		}

		public Criteria andModTimeGreaterThanOrEqualTo(Long value) {
			addCriterion("MOD_TIME >=", value, "modTime");
			return (Criteria) this;
		}

		public Criteria andModTimeLessThan(Long value) {
			addCriterion("MOD_TIME <", value, "modTime");
			return (Criteria) this;
		}

		public Criteria andModTimeLessThanOrEqualTo(Long value) {
			addCriterion("MOD_TIME <=", value, "modTime");
			return (Criteria) this;
		}

		public Criteria andModTimeIn(List<Long> values) {
			addCriterion("MOD_TIME in", values, "modTime");
			return (Criteria) this;
		}

		public Criteria andModTimeNotIn(List<Long> values) {
			addCriterion("MOD_TIME not in", values, "modTime");
			return (Criteria) this;
		}

		public Criteria andModTimeBetween(Long value1, Long value2) {
			addCriterion("MOD_TIME between", value1, value2, "modTime");
			return (Criteria) this;
		}

		public Criteria andModTimeNotBetween(Long value1, Long value2) {
			addCriterion("MOD_TIME not between", value1, value2, "modTime");
			return (Criteria) this;
		}

		public Criteria andCreatorIsNull() {
			addCriterion("CREATOR is null");
			return (Criteria) this;
		}

		public Criteria andCreatorIsNotNull() {
			addCriterion("CREATOR is not null");
			return (Criteria) this;
		}

		public Criteria andCreatorEqualTo(Integer value) {
			addCriterion("CREATOR =", value, "creator");
			return (Criteria) this;
		}

		public Criteria andCreatorNotEqualTo(Integer value) {
			addCriterion("CREATOR <>", value, "creator");
			return (Criteria) this;
		}

		public Criteria andCreatorGreaterThan(Integer value) {
			addCriterion("CREATOR >", value, "creator");
			return (Criteria) this;
		}

		public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
			addCriterion("CREATOR >=", value, "creator");
			return (Criteria) this;
		}

		public Criteria andCreatorLessThan(Integer value) {
			addCriterion("CREATOR <", value, "creator");
			return (Criteria) this;
		}

		public Criteria andCreatorLessThanOrEqualTo(Integer value) {
			addCriterion("CREATOR <=", value, "creator");
			return (Criteria) this;
		}

		public Criteria andCreatorIn(List<Integer> values) {
			addCriterion("CREATOR in", values, "creator");
			return (Criteria) this;
		}

		public Criteria andCreatorNotIn(List<Integer> values) {
			addCriterion("CREATOR not in", values, "creator");
			return (Criteria) this;
		}

		public Criteria andCreatorBetween(Integer value1, Integer value2) {
			addCriterion("CREATOR between", value1, value2, "creator");
			return (Criteria) this;
		}

		public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
			addCriterion("CREATOR not between", value1, value2, "creator");
			return (Criteria) this;
		}

		public Criteria andAddTimeIsNull() {
			addCriterion("ADD_TIME is null");
			return (Criteria) this;
		}

		public Criteria andAddTimeIsNotNull() {
			addCriterion("ADD_TIME is not null");
			return (Criteria) this;
		}

		public Criteria andAddTimeEqualTo(Long value) {
			addCriterion("ADD_TIME =", value, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeNotEqualTo(Long value) {
			addCriterion("ADD_TIME <>", value, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeGreaterThan(Long value) {
			addCriterion("ADD_TIME >", value, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeGreaterThanOrEqualTo(Long value) {
			addCriterion("ADD_TIME >=", value, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeLessThan(Long value) {
			addCriterion("ADD_TIME <", value, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeLessThanOrEqualTo(Long value) {
			addCriterion("ADD_TIME <=", value, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeIn(List<Long> values) {
			addCriterion("ADD_TIME in", values, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeNotIn(List<Long> values) {
			addCriterion("ADD_TIME not in", values, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeBetween(Long value1, Long value2) {
			addCriterion("ADD_TIME between", value1, value2, "addTime");
			return (Criteria) this;
		}

		public Criteria andAddTimeNotBetween(Long value1, Long value2) {
			addCriterion("ADD_TIME not between", value1, value2, "addTime");
			return (Criteria) this;
		}
	}

	/**
	 * This class was generated by MyBatis Generator. This class corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	public static class Criterion {
		private String condition;
		private Object value;
		private Object secondValue;
		private boolean noValue;
		private boolean singleValue;
		private boolean betweenValue;
		private boolean listValue;
		private String typeHandler;

		public String getCondition() {
			return condition;
		}

		public Object getValue() {
			return value;
		}

		public Object getSecondValue() {
			return secondValue;
		}

		public boolean isNoValue() {
			return noValue;
		}

		public boolean isSingleValue() {
			return singleValue;
		}

		public boolean isBetweenValue() {
			return betweenValue;
		}

		public boolean isListValue() {
			return listValue;
		}

		public String getTypeHandler() {
			return typeHandler;
		}

		protected Criterion(String condition) {
			super();
			this.condition = condition;
			this.typeHandler = null;
			this.noValue = true;
		}

		protected Criterion(String condition, Object value, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.typeHandler = typeHandler;
			if (value instanceof List<?>) {
				this.listValue = true;
			} else {
				this.singleValue = true;
			}
		}

		protected Criterion(String condition, Object value) {
			this(condition, value, null);
		}

		protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
			super();
			this.condition = condition;
			this.value = value;
			this.secondValue = secondValue;
			this.typeHandler = typeHandler;
			this.betweenValue = true;
		}

		protected Criterion(String condition, Object value, Object secondValue) {
			this(condition, value, secondValue, null);
		}
	}

	/**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_DEPARTMENTS_HOSPITAL
     *
     * @mbg.generated do_not_delete_during_merge Wed May 15 11:40:54 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }
}