package com.vedeng.erp.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.system.mapper.CustomTagMapper;
import com.vedeng.erp.system.dto.CustomTagDto;
import com.vedeng.erp.system.domain.entity.CustomTagEntity;
import com.vedeng.erp.system.mapstruct.CustomTagConvertor;
import com.vedeng.erp.system.service.CustomTagApiService;
import com.vedeng.erp.system.service.CustomTagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务实现
 * @date 2022/7/12 11:41
 */
@Service
@Slf4j
public class CustomTagServiceImpl implements CustomTagService, CustomTagApiService {

    @Autowired
    private CustomTagMapper customTagMapper;
    @Autowired
    private CustomTagConvertor customTagConvertor;


    @Override
    public void add(CustomTagDto customTagDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        customTagDto.setBelongerId(currentUser.getId());
        CustomTagEntity customTagEntity = customTagConvertor.toEntity(customTagDto);
        CustomTagEntity oldTag = customTagMapper.findByNameAndBelongerIdAndType(customTagEntity.getName(), customTagEntity.getBelongerId(), customTagDto.getType());
        List<CustomTagEntity> byTypeAndDefaultFlag = customTagMapper.findByTypeAndDefaultFlagAndName(customTagDto.getType(), true, customTagDto.getName());
        if (oldTag != null || CollUtil.isNotEmpty(byTypeAndDefaultFlag)) {
            throw new ServiceException(customTagDto.getName() + "标签已存在");
        }
        customTagMapper.insert(customTagEntity);
    }

    @Override
    public void update(CustomTagDto customTagDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        CustomTagEntity oldCustomTagEntity = customTagMapper.selectByPrimaryKey(customTagDto.getId());
        CustomTagEntity tag = customTagMapper.findByNameAndBelongerIdAndType(customTagDto.getName(), customTagDto.getBelongerId(), customTagDto.getType());
        List<CustomTagEntity> byTypeAndDefaultFlag = customTagMapper.findByTypeAndDefaultFlagAndName(customTagDto.getType(), true, customTagDto.getName());
        if (!oldCustomTagEntity.getName().equals(customTagDto.getName()) && (tag != null || CollUtil.isNotEmpty(byTypeAndDefaultFlag))) {
            throw new ServiceException(customTagDto.getName() + "标签已存在");
        }
        customTagDto.setBelongerId(currentUser.getId());
        CustomTagEntity customTagEntity = customTagConvertor.update(customTagDto, oldCustomTagEntity);
        customTagMapper.updateByPrimaryKey(customTagEntity);
    }

    @Override
    public int delete(Set<Integer> ids) {
        if (CollUtil.isEmpty(ids)) {
            throw new ServiceException("id不能为空");
        }
        ids.forEach(customTagMapper::deleteByPrimaryKey);
        return ids.size();
    }

    @Override
    public List<CustomTagDto> getUserTags(Integer type) {
        List<CustomTagDto> customTagDtos = new ArrayList<>();
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        List<CustomTagEntity> byTypeAndBelongerId = customTagMapper.findByTypeAndBelongerId(type, currentUser.getId());
        List<CustomTagEntity> byTypeAndDefaultFlag = customTagMapper.findByTypeAndDefaultFlag(type, true);
        customTagDtos.addAll(customTagConvertor.toDto(byTypeAndBelongerId));
        customTagDtos.addAll(customTagConvertor.toDto(byTypeAndDefaultFlag));
        return customTagDtos;
    }

    @Override
    public List<CustomTagDto> getAllTags(Integer type) {
        List<CustomTagEntity> byType = customTagMapper.findByType(type);
        return new ArrayList<>(customTagConvertor.toDto(byType));
    }

    @Override
    public List<CustomTagDto> getByIdList(List<Integer> idList) {
        List<CustomTagDto> customTagDtoList = new ArrayList<>();
        if (CollUtil.isEmpty(idList)) {
            return customTagDtoList;
        }
        List<CustomTagDto> list = customTagMapper.findByIdIn(idList);
        if (CollUtil.isNotEmpty(list)) {
            customTagDtoList.addAll(list);
        }
        return customTagDtoList;
    }

    @Override
    public Map<String, List<CustomTagDto>> getUserTagsToMap(Integer type) {
        Map<String, List<CustomTagDto>> map = new HashMap<>(2);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        List<CustomTagEntity> byTypeAndBelongerId = customTagMapper.findByTypeAndBelongerId(type, currentUser.getId());
        List<CustomTagEntity> byTypeAndDefaultFlag = customTagMapper.findByTypeAndDefaultFlag(type, true);
        map.put("default", customTagConvertor.toDto(byTypeAndDefaultFlag));
        map.put("belonger", customTagConvertor.toDto(byTypeAndBelongerId));
        return map;
    }

    @Override
    public List<CustomTagDto> findByTypeAndDefaultFlag(Integer type, boolean defaultFlag, String name) {
        return customTagConvertor.toDto(customTagMapper.findByTypeAndDefaultFlagAndNameLike(type, defaultFlag, name));
    }

    @Override
    public List<CustomTagDto> getTagByIds(List<Integer> ids, Integer userId) {
        List<CustomTagDto> list = customTagMapper.findByIdInAndDefaultFlag(ids, true);
        List<CustomTagDto> byIdInAndBelongerId = customTagMapper.findByIdInAndBelongerId(ids, userId);
        return new ArrayList<>(CollUtil.unionDistinct(list, byIdInAndBelongerId));
    }
}
