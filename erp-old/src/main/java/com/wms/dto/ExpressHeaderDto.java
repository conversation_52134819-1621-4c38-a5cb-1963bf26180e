package com.wms.dto;

import java.util.List;

/**
 * 快递信息
 */
public class ExpressHeaderDto {

    /**
     * 货主
     */
    private String CustomerID;

    /**
     * 仓库ID
     */
    private String WarehouseID;

    /**
     * ERP单据号
     */
    private String SOReference1;

    /**
     * WMS单据编号
     */
    private String OrderNo;

    /**
     * 单据类型
     */
    private String OrderType;

    /**
     * 承运商ID
     */
    private String carrierId;

    /**
     * 承运商名称
     */
    private String carrierName;

    /**
     * 预留字段
     */
    private String UserDefine1;

    /**
     * 预留字段
     */
    private String UserDefine2;

    /**
     * 预留字段
     */
    private String UserDefine3;

    /**
     * 预留字段
     */
    private String UserDefine4;

    /**
     * 预留字段
     */
    private String UserDefine5;

    /**
     * 包裹单详情
     */
    private List<ExpressDetailDto> details;

    public String getCustomerID() {
        return CustomerID;
    }

    public void setCustomerID(String customerID) {
        CustomerID = customerID;
    }

    public String getWarehouseID() {
        return WarehouseID;
    }

    public void setWarehouseID(String warehouseID) {
        WarehouseID = warehouseID;
    }

    public String getSOReference1() {
        return SOReference1;
    }

    public void setSOReference1(String SOReference1) {
        this.SOReference1 = SOReference1;
    }

    public String getOrderNo() {
        return OrderNo;
    }

    public void setOrderNo(String orderNo) {
        OrderNo = orderNo;
    }

    public String getOrderType() {
        return OrderType;
    }

    public void setOrderType(String orderType) {
        OrderType = orderType;
    }

    public String getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(String carrierId) {
        this.carrierId = carrierId;
    }

    public String getCarrierName() {
        return carrierName;
    }

    public void setCarrierName(String carrierName) {
        this.carrierName = carrierName;
    }

    public String getUserDefine1() {
        return UserDefine1;
    }

    public void setUserDefine1(String userDefine1) {
        UserDefine1 = userDefine1;
    }

    public String getUserDefine2() {
        return UserDefine2;
    }

    public void setUserDefine2(String userDefine2) {
        UserDefine2 = userDefine2;
    }

    public String getUserDefine3() {
        return UserDefine3;
    }

    public void setUserDefine3(String userDefine3) {
        UserDefine3 = userDefine3;
    }

    public String getUserDefine4() {
        return UserDefine4;
    }

    public void setUserDefine4(String userDefine4) {
        UserDefine4 = userDefine4;
    }

    public String getUserDefine5() {
        return UserDefine5;
    }

    public void setUserDefine5(String userDefine5) {
        UserDefine5 = userDefine5;
    }

    public List<ExpressDetailDto> getDetails() {
        return details;
    }

    public void setDetails(List<ExpressDetailDto> details) {
        this.details = details;
    }

    @Override
    public String toString() {
        return "ExpressHeaderDto{" +
                "CustomerID='" + CustomerID + '\'' +
                ", WarehouseID='" + WarehouseID + '\'' +
                ", SOReference1='" + SOReference1 + '\'' +
                ", OrderNo='" + OrderNo + '\'' +
                ", OrderType='" + OrderType + '\'' +
                ", carrierId='" + carrierId + '\'' +
                ", carrierName='" + carrierName + '\'' +
                ", UserDefine1='" + UserDefine1 + '\'' +
                ", UserDefine2='" + UserDefine2 + '\'' +
                ", UserDefine3='" + UserDefine3 + '\'' +
                ", UserDefine4='" + UserDefine4 + '\'' +
                ", UserDefine5='" + UserDefine5 + '\'' +
                ", details=" + details +
                '}';
    }
}
