package com.newtask.data.saleorderdata;

import com.newtask.data.saleorder.SaleorderCurrentOrgAndUserIdSync;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 初始化订单信息
 */
@Component
@JobHandler(value = "SaleorderDataSyncTask")
public class SaleorderDataSyncTask extends AbstractJobHandler {
    Logger logger = LoggerFactory.getLogger(SaleorderDataSyncTask.class);

    @Autowired
    SaleorderAutoCheckSync saleorderAutoCheckSync;

    @Autowired
    SaleorderContractStatusSync saleorderContractStatusSync;

    @Autowired
    SaleorderFirstExpressAndReceiveTimeSync saleorderFirstExpressAndReceiveTimeSync;

    @Autowired
    SaleorderFirstPayTimeSync saleorderFirstPayTimeSync;

    @Autowired
    SaleorderLastInvoiceTimeSync saleorderLastInvoiceTimeSync;

    @Autowired
    SaleorderLeftAmountPeriodSync saleorderLeftAmountPeriodSync;

    @Autowired
    SaleorderCommunicateNumSync saleorderCommunicateNumSync;

    @Autowired
    SaleorderAfterSaleStatusSync saleorderAfterSaleStatusSync;

    @Autowired
    SaleorderInvoiceApplyFlaySync saleorderInvoiceApplyFlaySync;

    @Autowired
    SaleorderVerifyStatusSync saleorderVerifyStatusSync;

    @Autowired
    SaleorderContractVerifyStatusSync saleorderContractVerifyStatusSync;

    @Autowired
    SaleorderSubStatusSync saleorderSubStatusSync;

    @Autowired
    SaleorderNameConcatInfoSync saleorderNameConcatInfoSync;

    @Autowired
    SaleorderCurrentOrgAndUserIdSync saleorderCurrentOrgAndUserIdSync;


    @Autowired
    SaleorderCouponTypeSync saleorderCouponTypeSync;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        if(StringUtil.isEmpty(s)){
            s = "0";
        }
        String [] splitTime =  s.split("-");

        if (splitTime.length >= 3 && "1".equals(splitTime[2])) {
            XxlJobLogger.log("初始化销售部门");
            // 初始化销售部门、销售人员
            saleorderCurrentOrgAndUserIdSync.process(s);

            return SUCCESS;
        }

        long start=System.currentTimeMillis();
        XxlJobLogger.log("初始化首次付款时间");
        // 初始化首次付款时间
        saleorderFirstPayTimeSync.process(s);

        XxlJobLogger.log("初始化最后开票时间");
        // 初始化最后开票时间
        saleorderLastInvoiceTimeSync.process(s);

        XxlJobLogger.log("初始化首次发货时间");
        // 初始化首次发货时间
        saleorderFirstExpressAndReceiveTimeSync.process(s);

        XxlJobLogger.log("初始化合同回传状态");
        // 初始化合同回传状态
        saleorderContractStatusSync.process(s);

        XxlJobLogger.log("初始化剩余账期");
        // 初始化剩余账期
        saleorderLeftAmountPeriodSync.process(s);

        XxlJobLogger.log("初始化是否自动审核");
        // 初始化是否自动审核 未查询到使用场景
       // saleorderAutoCheckSync.process(s);

        XxlJobLogger.log("初始化沟通次数");
        // 初始化沟通次数
        saleorderCommunicateNumSync.process(s);

        XxlJobLogger.log("初始化售后状态");
        // 初始化售后状态
        saleorderAfterSaleStatusSync.process(s);

        XxlJobLogger.log("初始化是否审核开票");
        // 初始化是否申请开票
        saleorderInvoiceApplyFlaySync.process(s);

        XxlJobLogger.log("初始化订单审核状态");
        // 初始化订单审核状态
        saleorderVerifyStatusSync.process(s);

        XxlJobLogger.log("初始化合同审核状态");
        // 初始化合同审核状态
        saleorderContractVerifyStatusSync.process(s);

        XxlJobLogger.log("初始化订单流订单状态");
        // 初始化订单流订单状态
        saleorderSubStatusSync.process(s);

        XxlJobLogger.log("初始化订单流订单sku品牌商品名称型号");
        // 初始化订单流订单sku品牌商品名称型号
        saleorderNameConcatInfoSync.updateStatusProcess(s);

        XxlJobLogger.log("初始化订单优惠类型");
        // 初始化订单优惠类型COUPON_TYPE
        saleorderCouponTypeSync.process(s);

        if(System.currentTimeMillis()-start>1000*60*5){
            logger.error("SaleorderDataSyncTask超过5分钟执行");
        }
        XxlJobLogger.log("初始化销售部门");
        // 初始化销售部门、销售人员
        saleorderCurrentOrgAndUserIdSync.process(s);

        return SUCCESS;
    }
}
