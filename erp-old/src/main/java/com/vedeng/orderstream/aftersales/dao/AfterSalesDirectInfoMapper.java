package com.vedeng.orderstream.aftersales.dao;

import com.vedeng.orderstream.aftersales.model.AfterSalesDirectInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AfterSalesDirectInfoMapper {
    int deleteByPrimaryKey(Integer afterSalesDirectId);

    int insert(AfterSalesDirectInfo record);

    int insertSelective(AfterSalesDirectInfo record);

    AfterSalesDirectInfo selectByPrimaryKey(Integer afterSalesDirectId);

    int updateByPrimaryKeySelective(AfterSalesDirectInfo record);

    int updateByPrimaryKey(AfterSalesDirectInfo record);

    List<AfterSalesDirectInfo> selectStockList(@Param("afterSalesGoodsId")Integer afterSalesGoodsId, @Param("type")Integer type);
}