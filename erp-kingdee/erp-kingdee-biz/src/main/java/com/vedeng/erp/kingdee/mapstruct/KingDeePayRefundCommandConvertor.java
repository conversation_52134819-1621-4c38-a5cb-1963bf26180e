package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeePayRefundCommand;
import com.vedeng.erp.kingdee.dto.KingDeePayRefundBillDto;
import com.vedeng.erp.kingdee.dto.KingDeePayRefundEntryDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.kingdee.mapstruct
 * @Date 2023/8/24 15:44
 */
@Mapper(componentModel = "spring")
public interface KingDeePayRefundCommandConvertor extends BaseCommandMapStruct<KingDeePayRefundCommand, KingDeePayRefundBillDto> {

    @Override
    @Mapping(target = "fid", source = "FId")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "fcontactunittype", source = "FContactUnitType")
    @Mapping(target = "fdate", source = "FDate")
    @Mapping(target = "fcontactunit.FNumber", source = "FContactUnit")
    @Mapping(target = "fbusinesstype", source = "FBusinessType")
    @Mapping(target = "fpayunittype", source = "FPayUnitType")
    @Mapping(target = "fsettleorgid.FNumber", source = "FSettleOrgId")
    @Mapping(target = "fpurchaseorgid.FNumber", source = "FPurchaseOrgId")
    @Mapping(target = "fpayorgid.FNumber", source = "FPayOrgId")
    @Mapping(target = "f_QZOK_SFZK", source = "FQzokSfzk")
    @Mapping(target = "f_QZOK_LSH", source = "FQzokLsh")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtid")
    @Mapping(target = "FBillTypeID.FNumber", source = "FBillTypeId")
    @Mapping(target = "f_qzok_pzgsywdh", source = "FQzokPzgsywdh")
    KingDeePayRefundCommand toCommand(KingDeePayRefundBillDto dto);

    @Mapping(target = "fsettletypeid.FNumber", source = "fsettletypeid")
    @Mapping(target = "fpurposeid.FNumber", source = "fpurposeid")
    @Mapping(target = "frefundamountfor", source = "frefundamountfor")
    @Mapping(target = "faccountid.FNumber", source = "faccountid")
    @Mapping(target = "fhandlingchargefor", source = "fhandlingchargefor")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "f_QZOK_BDDJHID", source = "FQzokBddjhid")
    KingDeePayRefundCommand.FREFUNDBILLENTRY toCommand(KingDeePayRefundEntryDto entryDto);
}
