<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<div id="app" style="display: none;">
    <template>
        <div>
            <el-form :inline="true" ref="visitSearchDto" class="search-form" @keyup.enter.native="handleSearch()">
                <!-- 第一组表单项 -->
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="客户名称">
                            <el-input v-model="visitSearchDto.customerName" placeholder="请输入" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="拜访人员">
                            <el-select v-model="visitSearchDto.userIdList" multiple placeholder="请选择">
                                <el-option v-for="visitor in visitors" :key="visitor.userId" :label="visitor.username" :value="visitor.userId"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="客户类型">
                            <el-select v-model="visitSearchDto.customerNature" placeholder="请选择" clearable>
                                <el-option v-for="nature in customerNatures" :key="nature.value" :label="nature.label" :value="nature.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="同舟会员">
                            <el-select v-model="visitSearchDto.tzCustomer" placeholder="请选择" clearable>
                                <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 第二组表单项 -->
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="同舟会员等级">
                            <el-select v-model="visitSearchDto.customerLevel" placeholder="请选择" clearable>
                                <el-option v-for="level in customerLevels" :key="level.value" :label="level.label" :value="level.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="贝登会员">
                            <el-select v-model="visitSearchDto.vdCustomer" placeholder="请选择" clearable>
                                <el-option v-for="item in yesNoOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="拜访结果">
                            <el-select v-model="visitSearchDto.actualVisitResult" placeholder="请选择" clearable>
                                <el-option v-for="result in visitResults" :key="result.value" :label="result.label" :value="result.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="商机编号">
                            <el-input v-model="visitSearchDto.businessChanceNo" placeholder="请输入" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- 第三组表单项 -->
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="商机状态">
                            <el-select v-model="visitSearchDto.businessChanceStatus" placeholder="请选择" clearable>
                                <el-option v-for="status in businessChanceStatus" :key="status.value" :label="status.label" :value="status.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="沟通概况">
                            <el-input v-model="visitSearchDto.commucateContent" placeholder="请输入关键词" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="计划拜访时间">
                            <el-date-picker
                                    v-model="visitSearchDto.planVisitDate"
                                    style="width: 200px"
                                    type="daterange"
                                    @change="handlePlanVisitDateDateChange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="实际拜访时间">
                            <el-date-picker v-model="visitSearchDto.actualVisitDate"
                                            style="width: 200px"
                                            type="daterange" format="yyyy-MM-dd" @change="handleActualVisitDateChange" value-format="yyyy-MM-dd" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>


            <div class="btn-group">
                <el-button type="primary" @click="handleSearch(true)">搜索</el-button>
                <el-button type="primary" plain @click="reset()">重置</el-button>
            </div>

            <el-table :data="tableData" style="width:100%" size="mini" v-loading="loading">
                <el-table-column prop="customerName" label="客户名称" width="250">
                    <template slot-scope="scope">
                        <el-link type="primary" @click="toCustomerDetail(scope.row)" v-if="scope.row.traderViewAble">{{scope.row.customerName}}</el-link>
                        <span v-else>{{ scope.row.customerName }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="belongUserName" label="归属销售" ></el-table-column>
                <el-table-column prop="visitorName" label="拜访人"></el-table-column>
                <el-table-column prop="planVisitDate" label="计划拜访时间"></el-table-column>
                <el-table-column prop="actualVisitDate" label="实际拜访时间"></el-table-column>
                <el-table-column prop="visitResult" label="拜访结果"></el-table-column>
                <el-table-column prop="bussinessChanceNo" label="商机编号" >
                    <template slot-scope="scope">
                        <el-link type="primary" @click="toBusinessChanceDetail(scope.row)">{{ scope.row.bussinessChanceNo}}</el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="statusName" label="商机状态" ></el-table-column>
                <el-table-column
                        prop="commucateContent"
                        label="沟通备忘录">
                    <template slot-scope="scope">
                        <div class="ellipsis-content" :title="scope.row.commucateContent">
                            {{ scope.row.commucateContent }}
                        </div>
                    </template>
                </el-table-column>
                <!-- 操作列 -->
                <el-table-column fixed="right" label="操作" >
                    <template slot-scope="scope">
                        <el-button type="text" @click="handleView(scope.row)" >查看</el-button>
                        <el-button type="text" @click="handleDelete(scope.row)" v-if="scope.row.cardOff != 'Y'">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>


            <el-pagination
                    style="text-align: right"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPageNo"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="currentSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="totalLines">
            </el-pagination>
        </div>
    </template>
</div>

<script src="${pageContext.request.contextPath}/static/api/trader/visitRecord.js?rnd=${resourceVersionKey}"></script>

<script>

    const app = new Vue({
        el: '#app',
        data() {
            return {
                visitSearchDto: {
                    customerName: '',
                    visitIdList: [],
                    customerNature: null,
                    tzCustomer: null,
                    customerLevel: null,
                    vdCustomer: null,
                    actualVisitResult: null,
                    businessChanceNo: '',
                    businessChanceStatus: null,
                    commucateContent: '',
                    actualVisitDate:[],
                    planVisitDate:[]
                },
                loading: false, // 加载状态，默认为false
                visitors: [], // 拜访人列表
                customerNatures: [{label: '渠道商', value: 465}, {label: '终端', value: 466}], // 客户类型列表
                yesNoOptions: [{label: '否', value: 0}, {label: '是', value: 1}], // 是/否选项
                customerLevels: [{label: '金牌会员', value: 0}, {label: '银牌会员', value: 1}], // 同舟会员等级列表
                visitResults: [
                    {label: '未打卡', value: 1},
                    {label: '仅打卡', value: 2},
                    {label: '拜访事项缺失', value: 3},
                    {label: '拜访成功', value: 4}], // 拜访结果列表
                businessChanceStatus: [
                    {label: '未处理', value: 0},
                    {label: '报价中', value: 1},
                    {label: '已报价', value: 2},
                    {label: '已订单', value: 3},
                    {label: '已关闭', value: 4},
                    {label: '未分配', value: 5},
                    {label: '处理中', value: 6},
                    {label: '已成单', value: 7}],
                tableData: [],
                currentSize: 10,
                currentPageNo: 1,
                totalLines: 0,
            };
        },

        methods: {
            // 获取拜访人列表等
            fetchData() {
                this.handleSearch()
                queryVisitUserListByBelongUser().then(res => {
                    this.visitors = res.data.data;
                })
            },
            handleSizeChange(val) {
                this.currentSize = val;
                this.currentPageNo = 1;
                this.handleSearch();
            },
            handleCurrentChange(val) {
                this.currentPageNo = val;
                this.handleSearch();
            },
            // 查询操作
            handleSearch(isResetPage = false) {
                // 如果 isResetPage 为 true，则重置页码为第一页
                if (isResetPage) {
                    this.currentPageNo = 1;
                }

                this.loading = true;
                let pageParam = {
                    "pageNum": this.currentPageNo,
                    "pageSize": this.currentSize,
                    "param": this.visitSearchDto,
                    "orderBy":"PLAN_VISIT_DATE desc,CARD_TIME desc"
                };
                page(pageParam).then(res => {
                    this.tableData = res.data.data.list;
                    this.totalLines = res.data.data.total;
                    this.loading = false; // 请求失败也要关闭 loading 状态
                }).catch(error => {
                    console.error('Failed to fetch data:', error);
                    this.loading = false; // 请求失败也要关闭 loading 状态
                });
            },
            // 查看操作
            handleView(row) {
                openTab("详情", '/visitrecord/profile/getVisitRecordDetail.do?visitId=' + row.id)
            },
            handleDelete(row) {
                this.$confirm('确定删除该记录吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let dto = {
                        "id": row.id
                    };
                    del(dto).then(res => {
                        this.$message({
                            type: 'success',
                            message: '删除成功'
                        });
                        this.handleSearch();
                    }).catch(error => {
                        this.$message.error('删除失败，请稍后重试');
                    });
                }).catch(() => {
                    // 取消删除
                    this.$message.info('已取消删除');
                });
            },
            handlePlanVisitDateDateChange(date) {
                if (date != null &&  date.length > 1) {
                    this.visitSearchDto.planVisitDateStart = date[0]
                    this.visitSearchDto.planVisitDateEnd = date[1]
                }
            },

            handleActualVisitDateChange(date) {
                if (date != null && date.length > 1) {
                    this.visitSearchDto.actualVisitDateStart = date[0]
                    this.visitSearchDto.actualVisitDateEnd = date[1]
                }
            },
            // 重置
            reset() {
                this.visitSearchDto =  {
                        customerName: '',
                        userIdList: [],
                        customerNature: null,
                        tzCustomer: null,
                        customerLevel: null,
                        vdCustomer: null,
                        actualVisitResult: null,
                        businessChanceNo: '',
                        businessChanceStatus: null,
                        commucateContent: '',
                        actualVisitDate:[],
                        planVisitDate:[]
                }
            },
            toBusinessChanceDetail(row) {
                openTab("商机详情", '/businessChance/details.do?id=' + row.bussinessChanceId)
            },
            toCustomerDetail(row) {
                if (row.customerFrom == 1) {
                    openTab("客户详情", '/trader/customer/baseinfo.do?traderId=' + row.traderId)
                }
                if (row.customerFrom == 2) {
                    openTab("客户详情", '/trader/customer/new/terminalPortrait.do?searchName' + row.customerName)
                }
            }

        },
        mounted() {
            // 在组件加载完成后，获取数据
            loadingApp()
            this.fetchData();
        }
    })
</script>

<style>
    .search-form {
        margin-bottom: 20px;
    }

    .btn-group {
        display: flex;
        justify-content: center;
        margin-bottom: 10px;
    }

     .ellipsis-content {
         overflow: hidden;
         text-overflow: ellipsis;
         display: -webkit-box;
         -webkit-line-clamp: 2;
         -webkit-box-orient: vertical;
         white-space: normal;
     }
</style>


</body>
</html>