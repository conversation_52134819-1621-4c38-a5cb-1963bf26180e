package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.model.User;
import com.vedeng.order.model.Buyorder;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.dto.DailyManagementTodoDto;
import com.vedeng.todolist.dto.SupplyChainStaffTodoDtoBuild;
import com.vedeng.todolist.model.TodoList;
import com.wms.service.context.ThreadLocalContext;
import io.swagger.models.auth.In;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class DailyManageTodoStaffVisitor implements TodoVisitor<SupplyChainStaffTodoDtoBuild> {

    @Resource
    private TodoListMapper todoListMapper;

    @Value("${peer_list_filter_time}")
    private Long peerListFilterTime;

    @Override
    public void visitor(SupplyChainStaffTodoDtoBuild supplyChainStaffTodoDtoBuild) {

        User user = ThreadLocalContext.get("userInfo");

        DailyManagementTodoDto dailyManagementTodoDto = new DailyManagementTodoDto();

        List<Integer> userIdList = Arrays.asList(user.getUserId());
        dailyManagementTodoDto.setSkuAndSpuCountGroupByGrade(getUnhandledMainTodoListCountGroupByGrade(TodoListBuzSceneEnum.MAINTAIN_DATA_SKU,userIdList));
        dailyManagementTodoDto.setDeliveryTimeCountGroupByGrade(getUnhandledMainTodoListCountGroupByGrade(TodoListBuzSceneEnum.MAINTAIN_DATA_DELIVERY_TIME,userIdList));
        dailyManagementTodoDto.setPriceCountGroupByGrade(getUnhandledMainTodoListCountGroupByGrade(TodoListBuzSceneEnum.MAINTAIN_DATA_PRICE,userIdList));
        if (dailyManagementTodoDto.getPriceCountGroupByGrade().size() > 0){
            dailyManagementTodoDto.setPriceSkuList(getUnHandledMaintainPriceTodoListGroupByGrade(userIdList));
        }
        dailyManagementTodoDto.setAftersalePolicyCountGroupByGrade(getUnhandledMainTodoListCountGroupByGrade(TodoListBuzSceneEnum.MAINTAIN_DATA_AFTER_SALE_POLICY,userIdList));
        dailyManagementTodoDto.setSupplyAftersalePolicyCountGroupByGrade(getUnhandledMainTodoListCountGroupByGrade(TodoListBuzSceneEnum.MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY,userIdList));
        dailyManagementTodoDto.setOperationInfoCountGroupByGrade(getUnhandledMainTodoListCountGroupByGrade(TodoListBuzSceneEnum.MAINTAIN_DATA_OPERATION_INFO,userIdList));

        dailyManagementTodoDto.setReceiptRecordCountGroupByCategory(getReceiptRecordCountGroupByCategory(CollectionUtils.isNotEmpty(user.getSubUserIdList()) ? user.getSubUserIdList():userIdList));

        supplyChainStaffTodoDtoBuild.get().setDailyManagementTodoDto(dailyManagementTodoDto);
    }

    private Map<Long, Long> getReceiptRecordCountGroupByCategory(List<Integer> subUserIdList) {
        Integer buyorderListAllCount = todoListMapper.getReceiptRecordCountGroupByCategoryCount(subUserIdList, peerListFilterTime, null,null);
        Integer buyorderListIsUrgentCount = todoListMapper.getReceiptRecordCountGroupByCategoryCount(subUserIdList, peerListFilterTime, 1,null);
        Map<Long, Long> returnMap = new HashMap<>();
        //总待办
        returnMap.put(1L, (long) buyorderListAllCount);
        //紧急待办
        returnMap.put(2L, (long) buyorderListIsUrgentCount);
        //日常待办
        returnMap.put(3L, (long) buyorderListAllCount - buyorderListIsUrgentCount);
        return returnMap;
    }

    private Map<Long, Long> getUnhandledMainTodoListCountGroupByGrade(TodoListBuzSceneEnum sceneEnum,
                                                                            List<Integer> userList){
        List<TodoList> maintainTodoList =
                todoListMapper.getUnHandledMaintainTodoListCountByUserListGroupByGrade(sceneEnum.getBuzSceneId(), userList);
        //按照sku等级分类统计
        return maintainTodoList.stream().collect(Collectors.toMap(key -> Long.valueOf(key.getId()),value -> Long.valueOf(value.getBuzId())));
    }

    /**
     * 获取待维护价格的待办事项，按照商品等级统计sku
     * @param userList 用户集合
     * @return 结果
     */
    private Map<Long, String> getUnHandledMaintainPriceTodoListGroupByGrade(List<Integer> userList){
        Map<Integer, List<TodoList>> maintainPriceOfSku =
                todoListMapper.getUnHandledMaintainTodoListSkuByUserList(TodoListBuzSceneEnum.MAINTAIN_DATA_PRICE.getBuzSceneId(), userList)
                        .stream()
                        .collect(Collectors.groupingBy(TodoList::getBuzType));
        Map<Long, String> skuListGroupByGoodsLevel = new HashMap<>();
        for (Integer key : maintainPriceOfSku.keySet()){
            String skuListStr = maintainPriceOfSku.get(key).stream().map(TodoList::getBuzExtra).collect(Collectors.joining(","));
            skuListGroupByGoodsLevel.put(Long.valueOf(key),skuListStr);
        }
        return skuListGroupByGoodsLevel;
    }
}
