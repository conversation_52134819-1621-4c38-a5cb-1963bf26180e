package com.vedeng.orderstream.aftersales.dao;

import com.vedeng.orderstream.aftersales.model.AfterSalesExpenditureRecord;
import com.vedeng.orderstream.aftersales.model.AfterSalesExpenditureRecordExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

@Named("afterSalesExpenditureRecordMapper")
public interface AfterSalesExpenditureRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_EXPENDITURE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int countByExample(AfterSalesExpenditureRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_EXPENDITURE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int deleteByExample(AfterSalesExpenditureRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_EXPENDITURE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int deleteByPrimaryKey(Integer recordId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_EXPENDITURE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int insert(AfterSalesExpenditureRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_EXPENDITURE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int insertSelective(AfterSalesExpenditureRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_EXPENDITURE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    List<AfterSalesExpenditureRecord> selectByExample(AfterSalesExpenditureRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_EXPENDITURE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    AfterSalesExpenditureRecord selectByPrimaryKey(Integer recordId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_EXPENDITURE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int updateByExampleSelective(@Param("record") AfterSalesExpenditureRecord record, @Param("example") AfterSalesExpenditureRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_EXPENDITURE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int updateByExample(@Param("record") AfterSalesExpenditureRecord record, @Param("example") AfterSalesExpenditureRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_EXPENDITURE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int updateByPrimaryKeySelective(AfterSalesExpenditureRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_AFTER_SALES_EXPENDITURE_RECORD
     *
     * @mbggenerated Thu Oct 21 16:35:45 CST 2021
     */
    int updateByPrimaryKey(AfterSalesExpenditureRecord record);

    /**
     * Obtain after sales follow-up records
     * Using parameter afterSalesId
     * @simgo Tue Oct 12 11:17:55 CST 2021
     * @param afterSalesId
     * @return
     */
    List<AfterSalesExpenditureRecord> selectByAfterSalesId(@Param("afterSalesId") Integer afterSalesId);
}