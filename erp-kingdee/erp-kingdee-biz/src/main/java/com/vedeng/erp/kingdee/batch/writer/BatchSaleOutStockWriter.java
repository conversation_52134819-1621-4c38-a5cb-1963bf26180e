package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDto;
import com.vedeng.erp.kingdee.service.KingDeeSaleOutStockApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/7 13:42
 */
@Service
@Slf4j
public class BatchSaleOutStockWriter extends BaseWriter<KingDeeSaleOutStockDto> {

    @Autowired
    private KingDeeSaleOutStockApiService kingDeeSaleOutStockApiService;

    @Override
    public void doWrite(KingDeeSaleOutStockDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("销售实物商品出库单推送：{}", JSON.toJSONString(item));
        item.setKingDeeBizEnums(KingDeeBizEnums.saveSaleOutStock);
        kingDeeSaleOutStockApiService.register(item,true);
    }

}
