<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<div id="app" style="display: none;">
    <vxe-grid ref='xGrid' v-bind="gridOptions" @toolbar-button-click="toolbarButtonClickEvent"
              @edit-closed="editClosedEvent">
        <%--    表单搜索    --%>
        <template #form_status="{ data }">
            <el-select v-model="data.statusList" placeholder="全部" clearable multiple style="width: 328px">
                <el-option value="" label="全部"></el-option>
                <el-option value="0" label="未处理"></el-option>
                <el-option value="1" label="报价中"></el-option>
                <el-option value="2" label="已报价"></el-option>
                <el-option value="3" label="已订单"></el-option>
                <el-option value="4" label="已关闭"></el-option>
                <%--<vxe-option value="5" label="未分配"></vxe-option>--%>
                <el-option value="6" label="处理中"></el-option>
                <el-option value="7" label="已成单"></el-option>
            </el-select>
        </template>

        <template #form_type="{ data }">
            <vxe-select v-model="data.type" placeholder="全部" clearable>
                <vxe-option value="" label="全部"></vxe-option>
                <vxe-option v-for="item in businessChanceTypeList" :key="item.sysOptionDefinitionId"
                            :value="item.sysOptionDefinitionId" :label="item.title"></vxe-option>
            </vxe-select>
        </template>

        <template #form_trader_name="{ data }">
            <el-select
                    v-model="data.traderName"
                    filterable
                    clearable
                    remote
                    reserve-keyword
                    placeholder="请输入"
                    :remote-method="remoteMethod"
                    :loading="loading"
                    @blur="selectBlur($event)"
                    style="width: 100%;">
                <el-option
                        v-for="item in traderOptions"
                        :key="item.traderId"
                        :label="item.traderName"
                        :value="item.traderId"
                        :disabled="!item.belong"
                        @click.native="clickTrader(item)">
                    <span style="float: left">{{ item.traderName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.saleName }}</span>
                </el-option>
            </el-select>
        </template>

        <template #form_contact_way="{ data }">
            <vxe-input v-model="data.contactWay" placeholder="请输入客户联系人、手机号、电话" clearable></vxe-input>
        </template>

        <template #form_product_info="{ data }">
            <vxe-input v-model="data.productInfo" placeholder="请输入" clearable></vxe-input>
        </template>

        <template #form_business_level_type="{ data }">
            <vxe-select v-model="data.businessLevelType" placeholder="系统值" clearable>
                <vxe-option value="1" label="系统值"></vxe-option>
                <vxe-option value="2" label="人工校正值"></vxe-option>
            </vxe-select>
        </template>

        <template #form_business_level_value="{ data }">
            <vxe-select v-model="data.businessLevelValue" placeholder="全部" clearable>
                <vxe-option value="-1" label="全部"></vxe-option>
                <vxe-option v-for="item in businessChanceLevelList"
                            :key="item.sysOptionDefinitionId"
                            :value="item.sysOptionDefinitionId"
                            :label="item.title">
                </vxe-option>
            </vxe-select>
        </template>

        <template #form_order_rate_type="{ data }">
            <vxe-select v-model="data.orderRateType" placeholder="系统值" clearable>
                <vxe-option value="1" label="系统值"></vxe-option>
                <vxe-option value="2" label="人工校正值"></vxe-option>
            </vxe-select>
        </template>

        <template #form_order_rate_value="{ data }">
            <vxe-select v-model="data.orderRateValue" placeholder="全部" clearable>
                <vxe-option value="" label="全部"></vxe-option>
                <vxe-option v-for="item in orderRateList"
                            :key="item.sysOptionDefinitionId"
                            :value="item.sysOptionDefinitionId"
                            :label="item.title">
                </vxe-option>
            </vxe-select>
        </template>

        <template #form_bussiness_stage="{ data }">
            <vxe-select v-model="data.bussinessStage" placeholder="全部" clearable>
                <vxe-option value="" label="全部"></vxe-option>
                <vxe-option v-for="item in businessStageList"
                            :key="item.sysOptionDefinitionId"
                            :value="item.sysOptionDefinitionId"
                            :label="item.title">
                </vxe-option>
            </vxe-select>
        </template>

        <template #form_bussiness_chance_no="{ data }">
            <vxe-input v-model="data.bussinessChanceNo" placeholder="请输入" clearable></vxe-input>
        </template>

        <template #form_area="{ data }">
            <el-cascader
                    v-model="data.areaIdList"
                    style="width: 100%;"
                    ref="cascader"
                    :options="areaOptions"
                    :props="{multiple: true }"
                    collapse-tags
                    @change="getCheckedNodes"
                    clearable></el-cascader>
        </template>

        <template #form_time="{data}">
            <el-select v-model="timeType" placeholder="请选择" size="small" style="width: 23%;">
                <el-option v-for="item in timeTypeOptions"
                           :key="item.type"
                           :label="item.nameTime"
                           :value="item.type"/>
            </el-select>
            <el-date-picker size="small"
                            style="width: 76%;"
                            v-model="data.time"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']">
            </el-date-picker>
        </template>

        <template #form_next_contact_date="{ data }">
            <el-date-picker size="small"
                            style="width: 100%;"
                            v-model="data.nextCommunicationDates"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']">
            </el-date-picker>
        </template>

        <template #form_completion="{ data }">
            <vxe-select v-model="data.completion" placeholder="全部" clearable>
                <vxe-option value="" label="全部"></vxe-option>
                <vxe-option value="1" label="≤30分"></vxe-option>
                <vxe-option value="2" label="≤60分"></vxe-option>
                <vxe-option value="3" label="≤90分"></vxe-option>
                <vxe-option value="4" label="=90-100分"></vxe-option>
            </vxe-select>
        </template>

        <template #form_tag="{ data }">
            <el-select v-model="data.tagIdList" placeholder="全部" multiple clearable style="width: 100%;">
                <el-option v-for="tag in tagList" :key="tag.id" :value="tag.id" :label="tag.name"></el-option>
            </el-select>
        </template>

        <template #form_is_confirm_trader="{ data }">
            <vxe-select v-model="data.isConfirmTrader" placeholder="全部" clearable>
                <vxe-option value="" label="全部"></vxe-option>
                <vxe-option value="0" label="否"></vxe-option>
                <vxe-option value="1" label="是"></vxe-option>
            </vxe-select>
        </template>

        <template #form_belonger="{ data }">
            <el-select v-model="data.belongerIdList" filterable multiple placeholder="请选择" prop="belongerId"
                       style="width: 100%;">
                <el-option
                        v-for="item in belongerOptions"
                        :key="item.userId"
                        :label="item.username"
                        :value="item.userId">
                </el-option>
            </el-select>

        </template>

        <%--最新沟通记录筛选--%>
        <template #form_communicate_state="{ data }">
            <vxe-select v-model="data.communicateState" placeholder="全部" clearable>
                <vxe-option value="" label="全部"></vxe-option>
                <vxe-option v-for="item in communicateStateList"
                            :key="item.sysOptionDefinitionId"
                            :value="item.sysOptionDefinitionId"
                            :label="item.title">
                </vxe-option>
            </vxe-select>
        </template>
#            我关注的
        <template #form_attention_state="{ data }">
            <vxe-select v-model="data.attentionState" placeholder="全部" clearable>
                <vxe-option value="" label="全部"></vxe-option>
                <vxe-option value="1" label="已关注"></vxe-option>
                <vxe-option value="0" label="未关注"></vxe-option>
            </vxe-select>
        </template>

        <template #form_order_amount_opt="{ data }" :rules="rules">
            <vxe-select v-model="data.orderAmountOpt" placeholder="条件" clearable>
                <vxe-option value=">" label=">"></vxe-option>
                <vxe-option value="=" label="="></vxe-option>
                <vxe-option value="<" label="<"></vxe-option>
            </vxe-select>
        </template>

        <template #form_order_amount="{ data }" prop="orderAmount">
            <vxe-input
                    v-model="data.orderAmount"
                    placeholder="请填写金额"
                    oninput="value=value.replace(/[^0-9.]/g,'')"
                    @blur="checkOrderAmount(data.orderAmount)"
                    clearable>
            </vxe-input>
        </template>

        <template #form_order_time="{ data }">
            <el-date-picker size="small"
                            style="width: 100%;"
                            v-model="data.orderTimes"
                            format="yyyy 年 MM 月 dd 日"
                            value-format="timestamp"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']">
            </el-date-picker>
        </template>

        <%--     表格数据   --%>
        <template #chance_status="{ row }">
            <span>{{ buildChanceStatus(row).value }}</span>
        </template>

        <template #chance_level="{ row }">
            <span>{{ getSysDefinitionName(row).level }}</span>
        </template>

        <template #system_level="{ row }">
            <span>{{ getSysDefinitionName(row).systemLevel }}</span>
        </template>

        <template #trader_name="{ row }">
            <span v-if="row.traderId == 0">{{row.traderName}}</span>
            <el-link type="primary" v-if="row.traderId != 0" @click="viewTraderDetail(row)">
                {{row.checkTraderName}}
            </el-link>
        </template>

        <template #order_rate="{ row }">
            <span>{{ getSysDefinitionName(row).orderRate }}</span>
        </template>

        <template #system_rate="{ row }">
            <span>{{ getSysDefinitionName(row).systemOrderRate }}</span>
        </template>

        <template #completion_percent="{ row }">
            <span>{{ buildCompletion(row).completionStr}}</span>
        </template>

        <template #product_content="{ row }">
            <span>{{row.content}}</span>
            <br>
            <span>{{row.productComments}}</span>
        </template>

        <template #chance_no="{ row }">
            <span v-if="row.mergeStatus == 2" style="color: #ff0000;" @click="toDetail(row)">[合]</span>
            <el-link type="primary" @click="viewChanceDetail(row)">
                {{row.bussinessChanceNo}}
            </el-link>
        </template>

        <template #tags="{ row }">
            <template v-for="item in tagList">
                <template v-for="id in row.tagIdList">
                    <el-tag
                            :key="id"
                            :style="{color: item.cssClass,borderColor:item.cssClass}"
                            style="margin-right: 5px"
                            effect="plain"
                            v-if="item.id === id"
                    >
                        {{ item.name }}
                    </el-tag>
                </template>
            </template>

        </template>

        <template #phone="{ row }">
            <span v-if="row.mobile"><i class="el-icon-phone" style="color: #409eff;" @click="call(row,row.mobile)"></i>{{ row.mobile}}</span>
        </template>

        <template #telephone="{ row }">
            <span v-if="row.telephone"> <i class="el-icon-phone" style="color: #409eff;"
                                           @click="call(row,row.telephone)"></i>{{ row.telephone }}</span>
        </template>

        <template #communication_record="{ row }">
            <span v-if="row.communicateRecordDto==null">-</span>
            <i v-else class="el-icon-document-copy" style="color: #409eff;" @click="getCommunicationRecord(row)"></i>
            <span v-if="row.communicateRecordDto!=null && row.communicateRecordDto.nextContactDate!=null ">下次沟通时间：{{parseTime(row.communicateRecordDto.nextContactDate, '{y}-{m}-{d}')}}</span>
        </template>

        <template #supervisor_guidance="{ row }">
            <i class="el-icon-document-copy" style="color: #409eff;" @click="sendSupervisorGuidance(row)"></i>
            <span v-if="!row.supervisorGuidance">-</span>
            <span v-if="row.supervisorGuidance">{{row.supervisorGuidance}}</span>
        </template>

        <template #operate="{ row }">
            <vxe-button @click="cancelAttention(row)" v-if="row.customDataOperDto" size="small" type="text" status="info">
                取消关注
            </vxe-button>
            <vxe-button @click="attention(row)" v-else size="small" type="text" status="primary">关注</vxe-button>
        </template>

    </vxe-grid>

    <el-dialog title="自定义商机标签" :visible.sync="outerVisible" width="60%" center>
        <el-form :model="tagForm">
            <el-row>
                <el-col :span="5">
                    <el-form-item label-width="150px">
                        <span slot="label">
                           <span>默认商机标签：</span>
                        </span>
                    </el-form-item>
                </el-col>

                <el-col :span="12">
                    <el-tag v-for="item in defaultTags"
                            :key="item.name"
                            :style="{color: item.cssClass,borderColor:item.cssClass}"
                            style="margin-right: 10px"
                            effect="plain">
                        {{ item.name }}
                    </el-tag>
                </el-col>
            </el-row>

            <template v-for="(tagItem, index) in tagForm">
                <el-row>
                    <el-col :span="5">
                        <el-form-item label-width="150px">
                        <span slot="label">
                           <span v-if="index === 0">自定义商机标签：</span>
                           <span v-else>&nbsp;</span>
                        </span>
                            <el-button type="primary" icon="el-icon-plus" circle size="small"
                                       v-if="index === tagForm.length - 1" @click="addTagItem"></el-button>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-input v-model="tagItem.name" maxlength="5"></el-input>
                    </el-col>
                    <el-col :span="2">
                        <el-color-picker v-model="tagItem.cssClass" size="medium"
                                         :predefine="predefineColors"></el-color-picker>
                    </el-col>
                    <el-col :span="4">
                        <div class="grid-content bg-purple"></div>
                    </el-col>
                    <el-col :span="4">
                        <span class="demonstration">样式参考:</span>
                        <el-tag v-if="tagItem.name" :style="{color: tagItem.cssClass,borderColor:tagItem.cssClass}"
                                effect="plain">{{tagItem.name}}
                        </el-tag>
                    </el-col>
                    <el-col :span="2">
                        <el-button type="danger" icon="el-icon-delete" circle size="small" v-if="tagForm.length >= 1"
                                   @click="deleteTag(index)"></el-button>
                    </el-col>
                </el-row>
            </template>

        </el-form>


        <div slot="footer" class="dialog-footer">
            <el-button @click="outerVisible = false">取消</el-button>
            <el-button type="primary" @click="saveTag">确定</el-button>
        </div>
    </el-dialog>

    <el-dialog title="沟通记录" :visible.sync="communicationRecordDialog" width="60%" center top="20px">
        <div style="margin: 10px">
            <el-row>
                <el-button type="text" @click="addCommunicateRecord()">+ 新增沟通记录</el-button>
            </el-row>

            <el-row v-for="item in communicateRecordList" :key="item.communicateRecordId">
                <div style="border: 1px solid #d7dae2;border-radius: 0px">
                    <el-row>
                        <div style="margin: 10px;">
                            <el-col :span="12">
                                <div>
                                    {{ item.creatorName }}
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div style="float: right">
                                    {{parseTime(item.begintime, '{y}-{m}-{d} {h}:{i}')}}
                                </div>
                            </el-col>
                        </div>
                    </el-row>

                    <div>
                        <div style="margin: 20px">
                            <div>
                                <el-descriptions :colon="false">
                                    <el-descriptions-item>
                                        {{ item.communicateGoalName}}{{ item.communicateModeName}}{{ item.contactContent}}{{ item.contentSuffix}}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </div>
                            <div>
                                <el-descriptions>
                                    <el-descriptions-item label="下次沟通时间"
                                                          v-if="item.nextContactDate != null">
                                        {{parseTime(item.nextContactDate, '{y}-{m}-{d}')}}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </div>
                            <div>
                                <el-descriptions>
                                    <el-descriptions-item label="下次沟通内容"
                                                          v-if="item.nextContactContent != null">
                                        {{ item.nextContactContent }}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </div>
                        </div>
                    </div>

                    <el-row>
                        <div style="margin: 20px">
                            <el-divider></el-divider>
                            <el-col :span="1">
                                <div v-if="item.coid !=null">
                                    <img @click="playrecord(item.coidUri)"
                                         src="${pageContext.request.contextPath}/static/vue/images/trumpet.png">
                                </div>
                            </el-col>
                            <el-col :span="10">
                                <div>
                                    <p v-if="item.traderContactId != null">
                                        {{ item.contactName }}&nbsp;{{ item.contactDepartment
                                        }}&nbsp;{{ item.contactPosition }}
                                    </p>
                                    <p v-else style="height: 30px">
                                        {{ item.contact }}
                                    </p>
                                </div>
                            </el-col>
                        </div>
                    </el-row>
                </div>
            </el-row>


            <el-row v-if="communicateRecordTotalNum > 3">
                <div style="float: right">
                    <el-button v-if="showMoreFalg" type="text" @click="getMoreCommunicateRecord">查看更多跟进情况
                    </el-button>
                </div>

            </el-row>
        </div>
    </el-dialog>

    <el-dialog title="新增沟通记录" :visible.sync="dialogFormVisible" top="20px" :close-on-click-modal="false"
               :close-on-press-escape="false">
        <el-form :model="communicateRecordDto" ref="communicateRecordFrom" :rules="rules">
            <div v-show="businessChance.traderId!= 0">
                <el-form-item label="客户名称:" label-width="150px">
                    {{businessChance.traderName}}
                </el-form-item>

                <el-form-item label="联系人:" label-width="150px" prop="traderContactNameView">
                    <el-select
                            v-model="communicateRecordDto.traderContactNameView"
                            filterable
                            clearable
                            reserve-keyword
                            placeholder="请选择联系人"
                            style="width: 300px;"
                            @clear="traderContactClearComm"
                    >
                        <el-option
                                v-for="item in traderConcatDatas"
                                :key="item.traderContactId"
                                :label="item.name"
                                :value="item.traderContactId"
                                @click.native="traderConcatSelectComm(item)"
                        >
                            <span style="float: left">{{ item.name + ' ' + item.mobile + ' ' + item.telephone }}</span>
                        </el-option>

                    </el-select>
                    <span class="title-click  bg-light-bule pop-new-data bt-small mr10 bt-bg-style J-add-prod"
                          id="concat" onclick="addTraderContract()"
                          style="margin-left:24px;color: #3384ef;padding-right: 10px;cursor: pointer;height: 34px;line-height: 34px;"
                          :layerParams="'{&quot;width&quot;:&quot;50%&quot;,&quot;height&quot;:&quot;75%&quot;,&quot;title&quot;:&quot;添加联系人&quot;,&quot;link&quot;:&quot;/orderstream/saleorder/addContact.do?indexId=3&amp;traderId='+businessChance.traderId+'&quot;}'"><span
                            v-show="businessChance.traderId!=null">添加联系人</span></span>

                </el-form-item>

            </div>

            <div v-show="businessChance.traderId== 0">
                <el-form-item label="联系人:" label-width="150px" prop="traderContactNameNew">
                    <span slot="label" style="color: #F56C6C;font-weight: bold">
                    *
                </span>
                <span slot="label" style="color: #00a0e9;font-weight: bold">
                    联系人:
                </span>
                    <el-input type="text" v-model="communicateRecordDto.traderContactNameNew" placeholder="联系人姓名"></el-input>
                </el-form-item>

                <el-form-item label="联系电话:" label-width="150px" prop="traderContactMobile">
                    <span slot="label" style="color: #F56C6C;font-weight: bold">
                    *
                </span>
                <span slot="label" style="color: #00a0e9;font-weight: bold">
                    联系电话:
                </span>
                    <el-input type="tel" v-model="communicateRecordDto.traderContactMobile" placeholder="联系电话"></el-input>
                </el-form-item>
            </div>

            <el-form-item label="沟通时间:" label-width="150px" prop="time">
                <el-date-picker
                        v-model="communicateRecordDto.time"
                        type="datetimerange"
                        value-format="timestamp"
                        range-separator="至"
                        start-placeholder="沟通开始时间"
                        end-placeholder="沟通结束时间">
                </el-date-picker>
            </el-form-item>

            <el-form-item label="沟通内容:" label-width="150px" prop="contentSuffix">
                <el-input
                        v-model="communicateRecordDto.contentSuffix"
                        type="textarea"
                        placeholder="沟通内容最多输入200个汉字"
                        style="width: 500px"
                        maxlength="200"
                        rows="6"
                >
                </el-input>
            </el-form-item>

            <div v-show="businessChance.type==391|| businessChance.type== 394">
                <el-form-item label="商机精准度:" label-width="150px" prop="businessChanceAccuracy">
                    <span slot="label" style="color: #F56C6C;font-weight: bold">
                    *
                </span>
                <span slot="label" style="color: #00a0e9;font-weight: bold">
                    商机精准度:
                </span>

                    <el-select
                            v-model="communicateRecordDto.businessChanceAccuracy"
                            reserve-keyword
                            placeholder="请选择商机精准度"
                            style="width: 300px;"
                    >
                        <el-option
                                v-for="item in businessChanceAccuracys"
                                :key="item.businessChanceAccuracy"
                                :label="item.businessChanceAccuracyShow"
                                :value="item.businessChanceAccuracy"
                                @click.native="businessChanceAccuracySelectComm(item)"
                        >
                        </el-option>

                    </el-select>
                    <el-tooltip class="item" v-if="businessChance.belongPlatfromByOrgAndUser == 0" placement="bottom" effect="light"  placement="top">
                        <div slot="content">高精准：客户属于本部门经营对象，购买意向为公司签约商品或者一年内有成交过的商品；
                            <br/>一般精准：客户属于本部门经营对象，但当前没有明确购买意向或者需求商品不是公司签约商品和一年内有成交过的商品
                            <br/>不精准：客户不属于本部门经营对象，且目前没有明确购买意向或者需求商品不是公司签约商品和一年内有成交过的商品
                            <br/>无法判断：未接通（连续三天联系不上）</div>
                        <i class="el-icon-warning" style="margin-left: 5px; color: rgba(7,174,241,0.95)"></i>
                    </el-tooltip>
                    <el-tooltip class="item" v-if="businessChance.belongPlatfromByOrgAndUser == 1" placement="bottom" effect="light"  placement="top">
                        <div slot="content">高精准：客户有明确购买意向，且需求与我司匹配
                            <br/>一般精准：客户有明确购买意向，但需求与我司不匹配
                            <br/>不精准：客户目前没有明确购买意向
                            <br/>无法判断：未接通（连续三天联系不上）
                        </div>
                        <i class="el-icon-warning" style="margin-left: 5px; color: rgba(7,174,241,0.95)"></i>
                    </el-tooltip>
                </el-form-item>
            </div>

            <el-form-item label="下次沟通时间:" label-width="150px" prop="nextContactDate">
                <el-date-picker
                        v-model="communicateRecordDto.nextContactDate"
                        type="date"
                        :disabled="communicateRecordDto.noneNextDate"
                        placeholder="选择日期时间"
                        default-time="00:00:00">
                </el-date-picker>
                <el-checkbox v-model="communicateRecordDto.noneNextDate"
                             @change="communicateRecordNextBind">暂无下次沟通记录
                </el-checkbox>
            </el-form-item>

            <el-form-item label="下次沟通内容:" label-width="150px" prop="nextContactContent">
                <el-input
                        v-model="communicateRecordDto.nextContactContent"
                        type="textarea"
                        placeholder=""
                        style="width: 500px"
                        maxlength="200"
                        rows="6"
                >
                </el-input>
            </el-form-item>

            <el-form-item label="备注:" label-width="150px" prop="comments">
                <el-input
                        v-model="communicateRecordDto.comments"
                        type="textarea"
                        placeholder=""
                        style="width: 500px"
                        maxlength="200"
                        rows="3"
                >
                </el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: center">
            <el-button @click="cancelCommunicateRecord('communicateRecordFrom')">取 消</el-button>
            <el-button type="primary" @click="submitCommunicateRecord('communicateRecordFrom')">确 定</el-button>
        </div>
    </el-dialog>

</div>

<script src="${pageContext.request.contextPath}/static/api/trader/businessChance.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/system/customTag.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/system/user.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">

    let vm0 = null;
    const sendThis0 = (_this) => {
        vm0 = _this;
    };
    const communicateState = '${communicateState}';

    new Vue({
        el: '#app',

        data() {
            var dateTime = new Date().getTime();
            var dateTimeEnd = new Date(dateTime + 1000 * 60 * 2).getTime();

            return {
                rules: {
                    'nextContactDate': [
                        {required: true, message: '请输入下次沟通时间', trigger: 'blur'}
                    ],
                    'time': [
                        {type: 'array', required: true, message: '请输入沟通时间', trigger: ['blur', 'change']}
                    ],
                    'contentSuffix': [
                        {required: true, message: '请输入沟通内容', trigger: 'blur'}
                    ],
                    'businessChanceAccuracy': [
                        { validator: this.validatebusinessChanceAccuracy, trigger: 'blur'},
                    ],
                    'contact': [
                        {required: true, message: '请输入联系人', trigger: 'blur'},
                    ],
                    'contactMob': [
                        {required: true, message: '请输入手机号', trigger: 'blur'},
                    ],
                'traderContactNameView': [
                    {validator: this.validateNameView,trigger: ['change', 'blur']},
                ],
                'traderContactNameNew': [
                    { validator: this.validateName, trigger: 'blur'},
                ],
                traderContactMobile: [
                    { validator: this.validatePhone, trigger: 'blur' }
                ]
                },
                traderConcatDatas: [],
                loading: false,
                // 列表沟通记录相关
                communicationRecordDialog: false,
                dialogFormVisible: false,
                // 查看更多
                communicateRecordTotalNum: '',
                showMoreFalg: true,
                // 沟通记录
                communicateRecordDto: {
                    traderContactNameView: '',
                    relatedId: '',
                    traderId: '',
                    businessChanceAccuracy:"",
                    companyId: 1,
                    traderContactId: '',
                    traderContactNameNew: '',
                    traderContactMobile: '',
                    contact: null,
                    time: [dateTime, dateTimeEnd],
                    begintime: null,
                    endtime: null,
                    noneNextDate: false,
                    isLfasr: 0,
                    contactMob: null,
                    contentSuffix: null,
                    communicateType: 244,
                    nextContactContent: '',
                    comments: '',
                    nextContactDate: ''
                },
                communicateStateList:[],
                //客户
                traderOptions: [],
                businessChance: {
                    bussinessChanceId: 0,
                    traderId: 0,
                    type: 0,
                    belongPlatfromByOrgAndUser:0,
                },
                communicateRecordList: [],
                businessChanceTypeList: [],
                businessChanceLevelList: [],
                businessChanceAccuracys: [],
                orderRateList: [],
                businessStageList: [],
                communicateState: [],
                tagList: [],
                //地区三级联动
                areaOptions: [],
                defaultTags: [],
                timeType: "addTime",
                timeTypeOptions: [
                    {type: 'addTime', nameTime: '创建时间'},
                    {type: 'assignTime', nameTime: '分配时间'},
                    {type: 'offerTime', nameTime: '报价时间'},
                    {type: 'orderTime', nameTime: '订单时间'},
                    {type: 'closeTime', nameTime: '关闭时间'}
                ],
                collapseStatus: true,
                outerVisible: false,
                belongerOptions: "",
                predefineColors: [
                    '#ff4500',
                    '#ff8c00',
                    '#ffd700',
                    '#90ee90',
                    '#00ced1',
                    '#1e90ff',
                    '#c71585',
                ],
                tagForm: [{
                    id: null,
                    name: null,
                    cssClass: '#409EFF',
                    type: 2
                }],
                currentRow: {},

                gridOptions: {
                    height: '800px',
                    align: 'center',
                    border: true,
                    showHeaderOverflow: true,
                    showOverflow: true,
                    keepSource: true,
                    id: 'business_chance_index',
                    rowId: 'id',
                    rowConfig: {
                        isHover: true
                    },
                    columnConfig: {
                        resizable: true
                    },
                    customConfig: {
                        storage: true,
                    },
                    pagerConfig: {
                        pageSize: 50,
                        pageSizes: [10, 20, 50, 100, 200]
                    },

                    // 列表搜索区域
                    formConfig: {
                        titleWidth: 100,
                        titleAlign: 'right',
                        items: [
                            {
                                field: 'type',
                                title: '商机类型',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_type'}
                            },
                            {
                                field: 'traderName',
                                title: '客户名称',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_trader_name'}
                            },
                            {
                                field: 'contactWay',
                                title: '联系方式',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_contact_way'}
                            },
                            {
                                field: 'productInfo',
                                title: '产品信息',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_product_info'},
                            },
                            {
                                field: 'statusList',
                                title: '商机状态',
                                span: 6,
                                itemRender: {
                                    defaultValue: ['0','1','2','3','6','7']
                                },
                                slots: {default: 'form_status'},
                                folding: true
                            },
                            {
                                field: 'businessLevelType',
                                title: '商机等级',
                                span: 4,
                                itemRender: {},
                                slots: {default: 'form_business_level_type'},
                                folding: true,
                            },
                            {
                                field: 'businessLevelValue',
                                span: 2,
                                itemRender: {},
                                slots: {default: 'form_business_level_value'},
                                folding: true,
                            },
                            {
                                field: 'orderRateType',
                                title: '成单几率',
                                span: 4,
                                itemRender: {},
                                slots: {default: 'form_order_rate_type'},
                                folding: true,
                            },
                            {
                                field: 'orderRateValue',
                                span: 2,
                                itemRender: {},
                                slots: {default: 'form_order_rate_value'},
                                folding: true,
                            },
                            {
                                field: 'bussinessStage',
                                title: '商机阶段',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_bussiness_stage'},
                                folding: true,
                            },
                            {
                                field: 'bussinessChanceNo',
                                title: '商机编号',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_bussiness_chance_no'},
                                folding: true,
                            },
                            {
                                field: 'areaIdList',
                                title: '地区',
                                folding: true,
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_area'}
                            },
                            {
                                field: 'time',
                                span: 6,
                                itemRender: {},
                                folding: true,
                                slots: {default: 'form_time'},
                                resetValue: [getLastMonthFirst(3), getNowDateLastTime()],
                            },
                            {
                                field: 'nextCommunicationDates',
                                title: '下次沟通时间',
                                span: 6,
                                folding: true,
                                itemRender: {},
                                slots: {default: 'form_next_contact_date'}
                            },
                            {
                                field: 'completion',
                                title: '商机完整度',
                                span: 6,
                                folding: true,
                                itemRender: {},
                                slots: {default: 'form_completion'}
                            },
                            {
                                field: 'tagIdList',
                                title: '商机标签',
                                span: 6,
                                folding: true,
                                itemRender: {},
                                slots: {default: 'form_tag'}
                            },
                            {
                                field: 'isConfirmTrader',
                                title: '是否确认客户',
                                span: 6,
                                folding: true,
                                itemRender: {},
                                slots: {default: 'form_is_confirm_trader'}
                            },
                            {
                                field: 'belongerIdList',
                                title: '商机归属人',
                                folding: true,
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_belonger'}
                            },
                            {
                                field: 'orderAmountOpt',
                                title: '预计成单金额',
                                span: 3,
                                itemRender: {},
                                slots: {default: 'form_order_amount_opt'},
                                folding: true,
                            },
                            {
                                field: 'orderAmount',
                                span: 3,
                                itemRender: {},
                                slots: {default: 'form_order_amount'},
                                folding: true,
                            },
                            {
                                field: 'orderTimes',
                                title: '预计成单时间',
                                span: 6,
                                folding: true,
                                itemRender: {},
                                slots: {default: 'form_order_time'}
                            },
                            {
                                field: 'communicateState',
                                title: '最新沟通记录',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_communicate_state'},
                                folding: true,
                            },
                            {
                                field: 'attentionState',
                                title: '我关注的',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_attention_state'},
                                folding: true,
                            },
                            {
                                span: 24,
                                align: 'center',
                                collapseNode: true,
                                itemRender: {
                                    name: '$buttons',
                                    children: [
                                        {
                                            props: {
                                                type: 'submit',
                                                content: '搜索',
                                                status: 'primary'
                                            }
                                        },
                                        {
                                            props: {
                                                type: 'reset',
                                                content: '重置'
                                            }
                                        }
                                    ]
                                }
                            }
                        ]
                    },

                    toolbarConfig: {
                        buttons: [
                            {code: 'add', name: '新增商机', status: 'warning'},
                            {code: 'init', name: '初始化商机', status: 'primary'},
                            {code: 'openTag', name: '自定义商机标签', status: 'primary'},
                            {code: 'batchAttention', name: '批量关注', status: 'primary'}
                        ],
                        // tools: [
                        //     {code: 'add', icon: 'vxe-icon--funnel ', circle: true},
                        // ],
                        custom: true
                    },

                    proxyConfig: {
                        seq: true, //启用动态序号代理，每一页的序号会根据当前页数变化
                        form: true, //启用表单代理，当点击表单提交按钮时会自动触发 reload 行为
                        props: {
                            result: 'list', // 配置响应结果列表字段
                            total: 'total' // 配置响应结果总页数字段
                        },
                        // 只接收Promise，具体实现自由发挥
                        ajax: {
                            // 当点击工具栏查询按钮或者手动提交指令 query或reload 时会被触发
                            query: ({page, sorts, filters, form}) => {
                                const queryParams = Object.assign({}, form);
                                if (queryParams.time != null && queryParams.time.length > 0) {
                                    queryParams.searchTime = this.timeType
                                    queryParams.startTime = new Date(queryParams.time[0]).getTime()
                                    queryParams.endTime = new Date(queryParams.time[1]).getTime()
                                }
                                if (queryParams.nextCommunicationDates != null && queryParams.nextCommunicationDates.length > 0) {
                                    queryParams.nextCommunicationDateStart = queryParams.nextCommunicationDates[0]
                                    queryParams.nextCommunicationDateEnd = queryParams.nextCommunicationDates[1]
                                }
                                if (queryParams.orderAmountOpt != null && queryParams.orderAmountOpt !== '') {
                                    queryParams.amountOpt = queryParams.orderAmountOpt;
                                }
                                if (queryParams.orderAmount != null && queryParams.orderAmount !== '') {
                                    if ((/(^[1-9]\d*$)/.test(queryParams.orderAmount)) && queryParams.orderAmount < ***********) {
                                        queryParams.amount = queryParams.orderAmount;
                                    }
                                }
                                if (queryParams.orderTimes != null && queryParams.orderTimes.length > 0) {
                                    queryParams.orderTimeStart = queryParams.orderTimes[0]
                                    queryParams.orderTimeEnd = queryParams.orderTimes[1]
                                }
                                // 骚操作，areaIdList真实数据为二维数组，但实际并不需要，只需要保证areaIdList不为空，让spring接受参数
                                if (queryParams.areaIdList != null && queryParams.areaIdList.length > 0) {
                                    queryParams.areaIdList = [1]
                                }

                                let pageParams = {
                                    pageNum: page.currentPage,
                                    pageSize: page.pageSize,
                                    param: queryParams
                                };

                                return axios({
                                    url: '/businessChance/page.do',
                                    method: 'post',
                                    data: pageParams
                                }).then(response => response.data.data);
                            }
                        }
                    },
                    columns: [
                        {type: 'checkbox', width: 50},
                        {
                            field: 'bussinessChanceNo',
                            title: '商机编号',
                            width: 150,
                            slots: {default: 'chance_no'},
                        },
                        {
                            field: 'typeName',
                            title: '商机类型',
                            width: 150,
                        },
                        {
                            field: 'status',
                            title: '商机状态',
                            width: 100,
                            slots: {default: 'chance_status'},
                            visible: false
                        },
                        {
                            title: "商机等级",
                            children: [
                                {
                                    field: 'systemBusinessLevel',
                                    title: '系统值',
                                    width: 100,
                                    slots: {default: 'system_level'},
                                    visible: false
                                },
                                {
                                    field: 'bussinessLevel',
                                    title: '个人校正值',
                                    width: 120,
                                    slots: {default: 'chance_level'},
                                    editRender: {
                                        name: '$select',
                                        options: [],
                                        optionProps: {value: 'sysOptionDefinitionId', label: 'title'},
                                        props: {placeholder: '请选择'}
                                    },
                                    visible: false
                                }
                            ]
                        },
                        {
                            field: 'tagIdList',
                            title: '商机标签',
                            width: 200,
                            slots: {default: 'tags'},
                            editRender: {
                                name: '$select',
                                options: [],
                                optionProps: {value: 'id', label: 'name'},
                                props: {placeholder: '请选择', multiple: true},
                            }
                        },
                        {
                            field: 'traderName',
                            title: '客户名称',
                            width: 250,
                            slots: {default: 'trader_name'},
                        },
                        {
                            field: 'productCommentsSale',
                            title: '产品备注(销售)',
                            width: 200,
                            showOverflow: true,
                            editRender: {name: 'textarea', props: {placeholder: '请输入'}}
                        },
                        {
                            field: 'content',
                            title: '询价产品/产品备注（总机）',
                            width: 250,
                            type: 'html',
                            showOverflow: true,
                            slots: {default: 'product_content'},
                        },
                        {
                            field: 'traderContactName',
                            title: '客户联系人',
                            width: 200
                        },
                        {
                            field: 'mobile',
                            title: '手机',
                            width: 150,
                            slots: {default: 'phone'},

                        },
                        {
                            field: 'telephone',
                            title: '电话',
                            width: 150,
                            slots: {default: 'telephone'},
                            visible: false
                        },
                        {
                            field: 'communicationRecord',
                            title: '沟通记录',
                            width: 300,
                            slots: {default: 'communication_record'},
                        },
                        {
                            field: 'areaStr',
                            title: '地区',
                            width: 200,
                            visible: false
                        },

                        {
                            title: '成单几率',
                            children: [
                                {
                                    field: 'systemOrderRate',
                                    title: '系统值',
                                    width: 100,
                                    slots: {default: 'system_rate'},
                                    visible: false
                                },
                                {
                                    field: 'orderRate',
                                    title: '个人校正值',
                                    width: 120,
                                    editRender: {
                                        name: '$select',
                                        options: [],
                                        optionProps: {value: 'sysOptionDefinitionId', label: 'title'},
                                        props: {placeholder: '请选择'}
                                    },
                                    visible: false
                                },
                            ],

                        },
                        {
                            field: 'completion',
                            title: '商机完整度',
                            width: 100,
                            slots: {default: 'completion_percent'},
                            visible: false
                        },
                        {
                            field: 'addTime',
                            title: '创建时间',
                            width: 160,
                            formatter: this.formatDate,
                            visible: false
                        },
                        {
                            field: 'firstViewTime',
                            title: '首次跟进时间',
                            width: 160,
                            formatter: this.formatDate,
                            visible: false
                        },
                        {
                            field: 'username',
                            title: '商机归属销售',
                            width: 120,
                            visible: false
                        },
                        {
                            field: 'creatorName',
                            title: '商机创建人',
                            width: 120,
                            visible: false
                        },
                        {field: 'operate', title: '操作', width: 100, slots: {default: 'operate'}, fixed: 'right'}
                    ],
                    editConfig: {
                        trigger: 'click',
                        mode: 'cell',
                        showStatus: true
                    },
                    checkboxConfig: {
                        reserve: true,
                        highlight: true,
                        range: true
                    }
                },
            }
        },

        watch: {
            // 下拉框中的值变化后 清空上一个 数组的值
            timeType(newName, oldName) {
                this.perInfo[oldName] = [];
            }
        },

        created() {
            this.getTags()
            getCascaderRegionOptions().then(res => {
                this.areaOptions = res.data.data;
            });
            getAllSubUserList().then(res => {
                this.belongerOptions = res.data.data;
            });
            this.getDict()
            sendThis0(this);
        },

        mounted() {
            loadingApp()
            const $grid = this.$refs.xGrid
            $grid.formData.time = [getLastMonthFirst(3), getNowDateLastTime()]
            if (communicateState == 4131){
                $grid.formData.communicateState = 4131
            }
            if (communicateState == 4130){
                $grid.formData.communicateState = 4130
            }
        },

        methods: {
            checkOrderAmount(val){
                if (val != null && val != '') {
                    if (!(/(^[1-9]\d*$)/.test(val))) {
                        this.$message.warning("预计成单金额请填写正整数");
                    }
                    if (val > ***********) {
                        this.$message.warning("预计成单金额不能超过***********");
                    }
                }
            },
            validateNameView(rule, value, callback) {
                if ( this.businessChance.traderId === 0) {
                    callback();
                    return;
                }
                if( value ==="" || value === undefined || value === null || value.length === 0)  {
                    callback(new Error('请选择联系人'));
                }else {
                    callback();
                }
            },
            validateName(rule, value, callback) {
                if (this.businessChance.traderId !== 0) {
                    callback();
                    return;
                }
                if (value ==="" || value === undefined || value === null || value.length === 0) {
                    callback(new Error('联系人不能为空'));
                }else if(value.length > 20)  {
                    callback(new Error('联系人姓名不能超过20个字符'));
                }else {
                    callback();
                }
            },
            validatePhone(rule, value, callback) {
                if (this.businessChance.traderId !== 0) {
                    callback();
                    return;
                }
                var phoneReg = /(^1[3|4|5|6|7|8|9]\d{9}$)|(^09\d{8}$)/;
                if (!phoneReg.test(value)) {
                    callback(new Error('请输入正确的电话号码'));
                } else {
                    callback();
                }
            },
            validatebusinessChanceAccuracy(rule, value, callback) {
                if ( !(this.businessChance.type  == 394 || this.businessChance.type == 391)) {
                    callback();
                    return;
                }
                if(value === null || value === '' || value === undefined ) {
                    callback(new Error('商机精准度不能为空'));
                } else {
                    callback();
                }
            },
            businessChanceAccuracySelectComm(item) {
                this.communicateRecordDto.businessChanceAccuracy = item.businessChanceAccuracy;
                this.$forceUpdate();
            },
            async getDict() {
                // 商机类型下拉框
                await getSysOptionDefinitionList({parentCode: "BUSINESS_CHANCE_TYPE"}).then((result) => {
                    this.businessChanceTypeList = result.data.data;
                });

                // 商机等级下拉框(系统值和自定义公用)
                await getSysOptionDefinitionList({parentCode: "BUSINESS_CHANCE_LEVEL"}).then((result) => {
                    this.businessChanceLevelList = result.data.data;
                });

                // 成单几率
                await getSysOptionDefinitionList({parentCode: "ORDER_RATE"}).then((result) => {
                    this.orderRateList = result.data.data;
                });

                // 商机阶段
                await getSysOptionDefinitionList({parentCode: "BUSINESS_CHANCE_STAGE"}).then((result) => {
                    this.businessStageList = result.data.data;
                });

                // 沟通记录筛选
                await getSysOptionDefinitionList({parentCode: "COMMUNICATE_STATE"}).then((result) => {
                    this.communicateStateList = result.data.data;
                });

                // 商机标签
                await getBusinesChanceTagList({type: "2"}).then((result) => {
                    this.tagList = result.data.data;
                });

                this.findSelectList()
            },

            //客户名称远程搜索
            remoteMethod(query) {
                this.loading = true;
                if (query) {
                    getTraderRemoteList({"name": query})
                        .then(res => {
                            this.loading = false;
                            this.traderOptions = res.data.data
                        }).catch(res => {
                        this.traderOptions = [];
                    })
                } else {
                    this.traderOptions = [];
                }
            },

            selectBlur(e) {
                const $table = this.$refs.xGrid
                $table.formData.traderName = e.target.value;
            },

            clickTrader(item) {
                const $table = this.$refs.xGrid
                $table.formData.traderName = item.traderName;
            },

            findSelectList() {
                const $grid = this.$refs.xGrid
                if ($grid) {
                    const levelColumn = $grid.getColumnByField('bussinessLevel')
                    levelColumn.editRender.options = this.businessChanceLevelList
                    const tagColumn = $grid.getColumnByField('tagIdList')
                    tagColumn.editRender.options = this.tagList
                    const orderRateColumn = $grid.getColumnByField('orderRate')
                    orderRateColumn.editRender.options = this.orderRateList

                }
            },

            formatDate({cellValue}) {
                return XEUtils.toDateString(cellValue, 'yyyy-MM-dd HH:mm')
            },

            traderConcatSelectComm(item) {
                if (this.businessChance.traderId !== 0) {
                    this.communicateRecordDto.traderContactNameView = item.name + " " + item.mobile + " " + item.telephone;
                    this.communicateRecordDto.traderContactId = item.traderContactId

                } else {
                    this.communicateRecordDto.traderContactNameView = item.name + " " + item.mobile;
                    this.communicateRecordDto.contact = item.name;
                    this.communicateRecordDto.contactMob = item.mobile
                }
            },

            buildChanceStatus(row) {
                let chanceStatus = {
                    'value': '',
                };
                switch (row.status) {
                    case 0:
                        chanceStatus.value = '未处理';
                        break;
                    case 1:
                        chanceStatus.value = '报价中';
                        break;
                    case 2:
                        chanceStatus.value = '已报价';
                        break;
                    case 3:
                        chanceStatus.value = '已订单';
                        break;
                    case 4:
                        chanceStatus.value = '已关闭';
                        break;
                    case 5:
                        chanceStatus.value = '未分配';
                        break;
                    case 6:
                        chanceStatus.value = '处理中';
                        break;
                    case 7:
                        chanceStatus.value = '已成单';
                        break;
                    default:
                }
                return chanceStatus
            },

            getSysDefinitionName(row) {
                let chance = {
                    'level': '',
                    'systemLevel': '',
                    'orderRate': '',
                    'systemOrderRate': ''
                };
                // 商机等级
                this.businessChanceLevelList.forEach(sys => {
                    if (row.bussinessLevel === sys.sysOptionDefinitionId) {
                        chance.level = sys.title;
                    }
                    if (row.systemBusinessLevel === sys.sysOptionDefinitionId) {
                        chance.systemLevel = sys.title;
                    }
                });

                // 成单几率
                this.orderRateList.forEach(sys => {
                    if (row.orderRate === sys.sysOptionDefinitionId) {
                        chance.orderRate = sys.title;
                    }
                    if (row.systemOrderRate === sys.sysOptionDefinitionId) {
                        chance.systemOrderRate = sys.title;
                    }
                });

                return chance;
            },

            getGoodsInfo(data) {
                if (data != null && data.length > 0) {
                    return data.slice(0, 14)
                }
            },
            buildCompletion(row) {
                let completionInfo = {
                    'completionStr': ''
                };
                completionInfo.completionStr = row.completion + '%';
                return completionInfo;
            },

            toolbarButtonClickEvent({code}) {
                switch (code) {
                    case 'add':
                        openTab("新增商机", "/businessChance/edit.do")
                        break
                    case 'init':
                        layer.open({
                            title: '初始化商机',
                            type: 2,
                            shade: 0.2,
                            maxmin: true,
                            shadeClose: true,
                            area: ['50%', '90%'],
                            content: '/initialization/upLoadFile.do?type=2',
                            moveOut: true
                        });
                        break
                    case 'openTag':
                        this.outerVisible = true
                        this.getInitTags()
                        break
                    case 'batchAttention':
                        this.batchAttentionEvent();
                        break
                }
            },

            editClosedEvent({row, column}) {
                const $table = this.$refs.xGrid
                let field = column.property
                // 判断单元格值是否被修改
                if ($table.isUpdateByRow(row, field)) {
                    let data = {
                        bussinessChanceId:row.bussinessChanceId,
                        [field]:row[field]
                    };

                    if (field === 'tagIdList') {
                        if (row.tagIdList.length > 3) {
                            this.$message({
                                showClose: true,
                                message: '仅允许选择三个标签',
                                type: 'error'
                            });
                            return
                        }
                        row.tagIds = (row.tagIdList || []).join(",")
                        data.tagIds =  row.tagIds
                    }

                    updateSingleData(data).then(res => {
                        $table.reloadRow(row, null, field)
                    })
                }
            },

            viewChanceDetail(row) {
                openTab("商机详情", '/businessChance/details.do?id=' + row.bussinessChanceId);
            },

            viewTraderDetail(row) {
                openTab("客户详情", 'trader/customer/baseinfo.do?traderId=' + row.traderId);
            },

            top(row) {
                toTop({"id": row.bussinessChanceId}).then(res => {
                    if (res.data.code === 0) {
                        this.$message({
                            showClose: true,
                            message: '置顶成功，您可以在页面更新时查看置顶效果',
                            type: 'success'
                        });
                    }
                })
            },

            unTop(row) {
                toUnTop({"id": row.bussinessChanceId}).then(res => {
                    if (res.data.code === 0) {
                        this.$message({
                            showClose: true,
                            message: '取消置顶成功,您可以在页面更新时查看效果',
                            type: 'success'
                        });
                    }
                })
            },
            attention(row) {
                let data = [row.bussinessChanceId];
                toAttention(data).then(res => {
                    if (res.data.code === 0) {
                        row.customDataOperDto = true;
                        this.$message({
                            showClose: true,
                            message: '关注成功',
                            type: 'success'
                        });
                    }else{
                        this.$message({
                            showClose: true,
                            message: res.data.message,
                            type: 'error'
                        });
                    }
                })
            },

            cancelAttention(row) {
                toCancelAttention({"id": row.bussinessChanceId}).then(res => {
                    if (res.data.code === 0) {
                        row.customDataOperDto = false;
                        this.$message({
                            showClose: true,
                            message: '取消关注成功',
                            type: 'success'
                        });
                    }else{
                        this.$message({
                            showClose: true,
                            message: res.data.message,
                            type: 'error'
                        });
                    }
                })
            },

            getCheckedNodes() {
                const $table = this.$refs.xGrid
                let nodesInfo = this.$refs['cascader'].getCheckedNodes()
                let provinceIds = []
                let cityIds = []
                let countyIds = []
                if (nodesInfo.length > 0) {
                    nodesInfo.forEach(node => {
                        if (node.level === 1) {
                            provinceIds.push(node.value)
                        }
                        if (node.level === 2) {
                            cityIds.push(node.value)
                        }
                        if (node.level === 3) {
                            countyIds.push(node.value)
                        }
                    })
                }
                $table.formData.provinceIdList = provinceIds
                $table.formData.cityIdList = cityIds
                $table.formData.countyIdList = countyIds
            },
            getTags() {
                getBusinessTags({"type": "2"}).then(res => {
                    this.tagList = res.data.data
                })
            },

            getInitTags() {
                getBusinessTagsToMap({"type": "2"}).then(res => {
                    let belongerTag = res.data.data.belonger
                    if (belongerTag && belongerTag.length > 0) {
                        this.tagForm = []
                        this.tagForm = belongerTag
                    }
                    let defaultTag = res.data.data.default
                    if (defaultTag && defaultTag.length > 0) {
                        this.defaultTags = []
                        this.defaultTags = defaultTag
                    }
                })
            },
            batchAttentionEvent: function () {
                const checkbox = this.$refs.xGrid.getCheckboxRecords();
                if (checkbox.length === 0) {
                    this.$message({
                        showClose: true,
                        message: '请选择商机',
                        type: 'warning'
                    });
                    return
                }
                // 筛选出businessChanceId集合
                let data = checkbox.map(item => {
                    return item.bussinessChanceId
                })
                toAttention(data).then(res => {
                    if (res.data.code === 0) {
                        this.$message({
                            showClose: true,
                            message: '批量关注成功',
                            type: 'success',
                            duration: 2000
                        });
                    }else{
                        this.$message({
                            showClose: true,
                            message: res.data.message,
                            type: 'error',
                            duration: 2000
                        });
                    }
                    var submitButton = document.querySelector('button[type="submit"]');
                    if (submitButton) {
                        submitButton.click();
                    }
                })
            },

            addTagItem() {
                if (this.tagForm.length < 5) {
                    this.tagForm.push({
                        name: null,
                        cssClass: '#409EFF',
                        type: 2
                    })
                }
            },

            deleteTag(index) {
                let tagId = this.tagForm[index].id
                if (tagId == null) {
                    if (this.tagForm.length > 1) {
                        this.tagForm.splice(index, 1)
                    } else {
                        this.tagForm[0].name = null
                        this.tagForm[0].cssClass = '#409EFF'
                    }
                    return
                }

                this.$confirm('删除标签，曾使用过此标签的商机，标签值也同时被删除，请谨慎操作', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    if (this.tagForm.length > 1) {
                        this.tagForm.splice(index, 1)
                    } else {
                        this.tagForm[0].name = null
                        this.tagForm[0].cssClass = '#409EFF'
                        this.tagForm[0].id = null
                    }

                    removeTag({"id": tagId}).then(res => {
                        this.$message({
                            showClose: true,
                            message: '删除成功',
                            type: 'success'
                        });
                    })
                }).catch(() => {
                    this.$message({
                        showClose: true,
                        type: 'info',
                        message: '已取消'
                    });
                });
            },

            saveTag() {
                if (this.tagForm.length === 1) {
                    let tag = this.tagForm[0]
                    this.persistentTag(tag)
                }

                if (this.tagForm.length > 1) {
                    this.$confirm('修改标签，曾使用此标签的线索，标签值也同时被修改', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.tagForm.forEach(tag => {
                            this.persistentTag(tag)
                        })
                    }).catch(() => {
                        this.$message({
                            type: 'info',
                            message: '已取消'
                        });
                    });
                }
            },

            persistentTag(tag) {
                if (tag.id == null) {
                    addTag(tag).then(res => {
                        if (res.data.code !== 0) {
                            this.$message({
                                showClose: true,
                                message: res.data.message,
                                type: 'error'
                            });
                        } else {
                            this.$message({
                                showClose: true,
                                message: tag.name + '新增成功',
                                type: 'success'
                            });
                            this.outerVisible = false
                        }
                    })
                } else {
                    updateTag(tag).then(res => {
                        if (res.data.code !== 0) {
                            this.$message({
                                showClose: true,
                                message: res.data.message,
                                type: 'error'
                            });
                        } else {
                            this.$message({
                                showClose: true,
                                message: tag.name + '修改成功',
                                type: 'success'
                            });
                            this.outerVisible = false
                        }
                    })
                }

            },

            // phone traderId,traderType,callType,orderId,traderContactId
            call(row, phone) {
                callout(phone, row.traderId, 1, 1, row.bussinessChanceId, row.traderContactId)
                updateChanceStatus({'id': row.bussinessChanceId});
            },

            sendSupervisorGuidance(row){
                this.$prompt('', '主管意见:', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputValue: row.supervisorGuidance,
                    inputType: "textarea",
                    inputValidator: ( value ) => {
                        return value.length <= 500;
                    },
                    inputErrorMessage: '最多填写500字'
                }).then(({ value }) => {
                    if (value === row.supervisorGuidance) {
                        console.log("主观意见未发生改变");
                    } else {
                        row.supervisorGuidance = value;
                        console.log(row);
                        let data = JSON.parse(JSON.stringify(row));
                        axios({
                            url: '/businessChance/updateSupervisorGuidance.do',
                            method: 'post',
                            data: data
                        })
                        this.$message.success("更新成功:" + value);
                    }
                }).catch(() => {
                });
            },

            getCommunicationRecord(row) {
                this.communicationRecordDialog = true
                //获取沟通记录
                getCommunicateRecord(
                    {
                        "param": {"communicateType": 244, "relatedId": row.bussinessChanceId},
                        "orderBy": 'COMMUNICATE_RECORD_ID desc',
                        "pageSize": 3
                    }
                ).then(res => {
                    this.communicateRecordList = res.data.data.list;
                    this.communicateRecordTotalNum = res.data.data.total;
                });
                this.businessChance = row;
                this.currentRow = row

            },

            traderContactClearComm() {
                this.communicateRecordDto.traderContactNameView = null;
                this.communicateRecordDto.traderContactId = null;
                this.communicateRecordDto.contact = null;
                this.communicateRecordDto.contactMob = null;
            },

            communicateRecordNextBind() {
                if (this.communicateRecordDto.noneNextDate) {
                    this.communicateRecordDto.nextContactDate = null;
                    this.rules = {
                        ...this.rules,
                        'nextContactDate': [{
                            required: false,
                            message: '',
                            trigger: 'change'
                        }]
                    };
                } else {
                    this.communicateRecordDto.nextContactDate = null;
                    this.rules = {
                        ...this.rules,
                        'nextContactDate': [{
                            required: true,
                            message: '请输入下次沟通时间',
                            trigger: 'change'
                        }]
                    };
                }
            },

            getMoreCommunicateRecord() {
                this.showMoreFalg = false;
                //获取更多沟通记录
                getCommunicateRecord(
                    {
                        "param": {"communicateType": 244, "relatedId": this.businessChance.bussinessChanceId},
                        "orderBy": 'COMMUNICATE_RECORD_ID desc',
                        "pageSize": 1000
                    }
                ).then(res => {
                    this.communicateRecordList = res.data.data.list;
                });

            },

            addCommunicateRecord() {
                this.communicationRecordDialog = false
                this.dialogFormVisible = true;
                this.communicateRecordDto.relatedId = this.businessChance.bussinessChanceId;
                getAccuracyEnum().then(res => {
                    this.businessChanceAccuracys = res.data.data;
                });
                if (this.businessChance.traderId !== 0) {
                    this.communicateRecordDto.traderId = this.businessChance.traderId;
                    getTraderContactDat(
                        {
                            "param": {"traderId": this.businessChance.traderId},
                            "orderBy": 'TRADER_CONTACT_ID desc',
                            "pageSize": 1000
                        }
                    ).then(res => {
                        this.traderConcatDatas = res.data.data.list;
                    });
                    if (this.businessChance.businessChanceAccuracy != null){
                        this.communicateRecordDto.businessChanceAccuracy = this.businessChance.businessChanceAccuracy;
                    }
                } else {

                    let array = [];
                    if (this.businessChance.traderContactName != null
                        && this.businessChance.traderContactName !== ''
                        && this.businessChance.mobile != null
                        && this.businessChance.mobile !== '') {
                        let mob = {
                            traderContactId: 1,
                            name: this.businessChance.traderContactName,
                            mobile: this.businessChance.mobile,
                        };
                        array.push(mob)
                    }
                    if (this.businessChance.traderContactName != null
                        && this.businessChance.traderContactName !== ''
                        && this.businessChance.telephone != null
                        && this.businessChance.telephone !== '') {
                        let data = {
                            traderContactId: 2,
                            name: this.businessChance.traderContactName,
                            mobile: this.businessChance.telephone
                        };
                        array.push(data)
                    }
                    if (this.businessChance.businessChanceAccuracy != null){
                        this.communicateRecordDto.businessChanceAccuracy = this.businessChance.businessChanceAccuracy;
                    }
                    this.traderConcatDatas = array;
                }
            },

            submitCommunicateRecord(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        if (this.communicateRecordDto.noneNextDate) {
                            this.communicateRecordDto.noneNextDate = 1;
                        } else {
                            this.communicateRecordDto.noneNextDate = 0;
                        }
                        if (this.communicateRecordDto.time.length > 1) {
                            this.communicateRecordDto.begintime = this.communicateRecordDto.time[0]
                            this.communicateRecordDto.endtime = this.communicateRecordDto.time[1]
                        }
                        this.communicateRecordDto.relatedId = this.businessChance.bussinessChanceId;
                        addCommunicateRecord(this.communicateRecordDto).then(res => {
                            if (res.data.code === 0) {
                                this.dialogFormVisible = false;
                                const $table = this.$refs.xGrid
                                $table.commitProxy('query')
                                this.cancelCommunicateRecord(form)
                            }
                        }).catch((e) => {
                        });
                    }
                })
            },

            cancelCommunicateRecord(form) {
                this.dialogFormVisible = false
                this.$refs[form].resetFields();
            }

        }
    });

    function updateTrader() {
        let data = {
            "param": {"traderId": vm0.businessChance.traderId},
            "orderBy": 'TRADER_CONTACT_ID desc',
            "pageSize": 1000
        };
        getTraderContactDat(data).then(res => {
            vm0.traderConcatDatas = res.data.data.list;
            if (vm0.traderConcatDatas.length > 0) {
                vm0.communicateRecordDto.traderContactNameView = vm0.traderConcatDatas[0].name + " " + vm0.traderConcatDatas[0].mobile + " " + vm0.traderConcatDatas[0].telephone
                vm0.communicateRecordDto.traderContactId = vm0.traderConcatDatas[0].traderContactId
            }
        });
    }

</script>

<style>
    .vxe-row > .vxe-col--6 {
        float: none;
    }
</style>


</body>
</html>
