<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseItemMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemEntity">
        <!--@mbg.generated-->
        <!--@Table T_BUYORDER_EXPENSE_ITEM-->
        <id column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId"/>
        <result column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId"/>
        <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus"/>
        <result column="ARRIVAL_TIME" jdbcType="TIMESTAMP" property="arrivalTime"/>
        <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus"/>
        <result column="DELIVERY_TIME" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="INVOICE_STATUS" jdbcType="INTEGER" property="invoiceStatus"/>
        <result column="INVOICE_TIME" jdbcType="TIMESTAMP" property="invoiceTime"/>
        <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId"/>
        <result column="NUM" jdbcType="INTEGER" property="num"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
    </resultMap>
    <resultMap id="itemDtoAndAssignmentManager" type="com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto" >
        <id column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId"/>
        <result column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId"/>
        <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus"/>
        <result column="ARRIVAL_TIME" jdbcType="TIMESTAMP" property="arrivalTime"/>
        <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus"/>
        <result column="DELIVERY_TIME" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="INVOICE_STATUS" jdbcType="INTEGER" property="invoiceStatus"/>
        <result column="INVOICE_TIME" jdbcType="TIMESTAMP" property="invoiceTime"/>
        <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId"/>
        <result column="NUM" jdbcType="INTEGER" property="num"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <association property="buyorderExpenseItemDetailDto"
                     javaType="com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDetailDto">
            <id column="BUYORDER_EXPENSE_ITEM_DETAIL_ID" jdbcType="INTEGER" property="buyorderExpenseItemDetailId"/>
            <result column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId"/>
            <result column="SKU" jdbcType="VARCHAR" property="sku"/>
            <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName"/>
            <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName"/>
            <result column="MODEL" jdbcType="VARCHAR" property="model"/>
            <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName"/>
            <result column="PRICE" jdbcType="DECIMAL" property="price"/>
            <result column="CURRENCY_UNIT_ID" jdbcType="INTEGER" property="currencyUnitId"/>
            <result column="EXPENSE_CATEGORY_ID" jdbcType="INTEGER" property="expenseCategoryId"/>
            <result column="EXPENSE_CATEGORY_NAME" jdbcType="VARCHAR" property="expenseCategoryName"/>
            <result column="HAVE_STOCK_MANAGE" jdbcType="INTEGER" property="haveStockManage"/>
            <result column="INSIDE_COMMENTS" jdbcType="VARCHAR" property="insideComments"/>
            <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
            <result column="assignmentManager" jdbcType="VARCHAR" property="assignmentManager"/>
        </association>
    </resultMap>

    <resultMap id="itemDto" type="com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto">
        <id column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId"/>
        <result column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId"/>
        <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus"/>
        <result column="ARRIVAL_TIME" jdbcType="TIMESTAMP" property="arrivalTime"/>
        <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus"/>
        <result column="DELIVERY_TIME" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="INVOICE_STATUS" jdbcType="INTEGER" property="invoiceStatus"/>
        <result column="INVOICE_TIME" jdbcType="TIMESTAMP" property="invoiceTime"/>
        <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId"/>
        <result column="NUM" jdbcType="INTEGER" property="num"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <association property="buyorderExpenseItemDetailDto"
                     javaType="com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDetailDto">
            <id column="BUYORDER_EXPENSE_ITEM_DETAIL_ID" jdbcType="INTEGER" property="buyorderExpenseItemDetailId"/>
            <result column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId"/>
            <result column="SKU" jdbcType="VARCHAR" property="sku"/>
            <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName"/>
            <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName"/>
            <result column="MODEL" jdbcType="VARCHAR" property="model"/>
            <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName"/>
            <result column="PRICE" jdbcType="DECIMAL" property="price"/>
            <result column="CURRENCY_UNIT_ID" jdbcType="INTEGER" property="currencyUnitId"/>
            <result column="EXPENSE_CATEGORY_ID" jdbcType="INTEGER" property="expenseCategoryId"/>
            <result column="EXPENSE_CATEGORY_NAME" jdbcType="VARCHAR" property="expenseCategoryName"/>
            <result column="HAVE_STOCK_MANAGE" jdbcType="INTEGER" property="haveStockManage"/>
            <result column="INSIDE_COMMENTS" jdbcType="VARCHAR" property="insideComments"/>
            <result column="OLD_INSIDE_COMMENTS" jdbcType="VARCHAR" property="oldInsideComments"/>
            <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        </association>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        BUYORDER_EXPENSE_ITEM_ID,
        BUYORDER_EXPENSE_ID,
        ARRIVAL_STATUS,
        ARRIVAL_TIME,
        DELIVERY_STATUS,
        DELIVERY_TIME,
        INVOICE_STATUS,
        INVOICE_TIME,
        GOODS_ID,
        NUM,
        IS_DELETE,
        ADD_TIME,
        CREATOR,
        CREATOR_NAME,
        MOD_TIME,
        UPDATER,
        UPDATER_NAME
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_BUYORDER_EXPENSE_ITEM
        where BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_BUYORDER_EXPENSE_ITEM
        where BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="BUYORDER_EXPENSE_ITEM_ID" keyProperty="buyorderExpenseItemId"
            parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemEntity"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_BUYORDER_EXPENSE_ITEM (BUYORDER_EXPENSE_ID, ARRIVAL_STATUS,
                                             ARRIVAL_TIME,
                                             DELIVERY_STATUS, DELIVERY_TIME, INVOICE_STATUS,
                                             INVOICE_TIME, GOODS_ID, NUM,
                                             IS_DELETE, ADD_TIME, CREATOR,
                                             CREATOR_NAME, MOD_TIME, UPDATER,
                                             UPDATER_NAME)
        values (#{buyorderExpenseId,jdbcType=INTEGER}, #{arrivalStatus,jdbcType=INTEGER},
                #{arrivalTime,jdbcType=TIMESTAMP},
                #{deliveryStatus,jdbcType=INTEGER}, #{deliveryTime,jdbcType=TIMESTAMP},
                #{invoiceStatus,jdbcType=INTEGER},
                #{invoiceTime,jdbcType=TIMESTAMP}, #{goodsId,jdbcType=INTEGER}, #{num,jdbcType=INTEGER},
                #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
                #{creatorName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER},
                #{updaterName,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="BUYORDER_EXPENSE_ITEM_ID" keyProperty="buyorderExpenseItemId"
            parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemEntity"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_BUYORDER_EXPENSE_ITEM
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="buyorderExpenseId != null">
                BUYORDER_EXPENSE_ID,
            </if>
            <if test="arrivalStatus != null">
                ARRIVAL_STATUS,
            </if>
            <if test="arrivalTime != null">
                ARRIVAL_TIME,
            </if>
            <if test="deliveryStatus != null">
                DELIVERY_STATUS,
            </if>
            <if test="deliveryTime != null">
                DELIVERY_TIME,
            </if>
            <if test="invoiceStatus != null">
                INVOICE_STATUS,
            </if>
            <if test="invoiceTime != null">
                INVOICE_TIME,
            </if>
            <if test="goodsId != null">
                GOODS_ID,
            </if>
            <if test="num != null">
                NUM,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="creatorName != null">
                CREATOR_NAME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="updaterName != null">
                UPDATER_NAME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="buyorderExpenseId != null">
                #{buyorderExpenseId,jdbcType=INTEGER},
            </if>
            <if test="arrivalStatus != null">
                #{arrivalStatus,jdbcType=INTEGER},
            </if>
            <if test="arrivalTime != null">
                #{arrivalTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deliveryStatus != null">
                #{deliveryStatus,jdbcType=INTEGER},
            </if>
            <if test="deliveryTime != null">
                #{deliveryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="invoiceStatus != null">
                #{invoiceStatus,jdbcType=INTEGER},
            </if>
            <if test="invoiceTime != null">
                #{invoiceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="goodsId != null">
                #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="num != null">
                #{num,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null">
                #{updaterName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemEntity">
        <!--@mbg.generated-->
        update T_BUYORDER_EXPENSE_ITEM
        <set>
            <if test="buyorderExpenseId != null">
                BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER},
            </if>
            <if test="arrivalStatus != null">
                ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
            </if>
            <if test="arrivalTime != null">
                ARRIVAL_TIME = #{arrivalTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deliveryStatus != null">
                DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER},
            </if>
            <if test="deliveryTime != null">
                DELIVERY_TIME = #{deliveryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="invoiceStatus != null">
                INVOICE_STATUS = #{invoiceStatus,jdbcType=INTEGER},
            </if>
            <if test="invoiceTime != null">
                INVOICE_TIME = #{invoiceTime,jdbcType=TIMESTAMP},
            </if>
            <if test="goodsId != null">
                GOODS_ID = #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="num != null">
                NUM = #{num,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
        </set>
        where BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemEntity">
        <!--@mbg.generated-->
        update T_BUYORDER_EXPENSE_ITEM
        set BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER},
            ARRIVAL_STATUS      = #{arrivalStatus,jdbcType=INTEGER},
            ARRIVAL_TIME        = #{arrivalTime,jdbcType=TIMESTAMP},
            DELIVERY_STATUS     = #{deliveryStatus,jdbcType=INTEGER},
            DELIVERY_TIME       = #{deliveryTime,jdbcType=TIMESTAMP},
            INVOICE_STATUS      = #{invoiceStatus,jdbcType=INTEGER},
            INVOICE_TIME        = #{invoiceTime,jdbcType=TIMESTAMP},
            GOODS_ID            = #{goodsId,jdbcType=INTEGER},
            NUM                 = #{num,jdbcType=INTEGER},
            IS_DELETE           = #{isDelete,jdbcType=INTEGER},
            ADD_TIME            = #{addTime,jdbcType=TIMESTAMP},
            CREATOR             = #{creator,jdbcType=INTEGER},
            CREATOR_NAME        = #{creatorName,jdbcType=VARCHAR},
            MOD_TIME            = #{modTime,jdbcType=TIMESTAMP},
            UPDATER             = #{updater,jdbcType=INTEGER},
            UPDATER_NAME        = #{updaterName,jdbcType=VARCHAR}
        where BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER}
    </update>
    <insert id="batchInsert" keyColumn="BUYORDER_EXPENSE_ITEM_ID" keyProperty="buyorderExpenseItemId"
            parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_BUYORDER_EXPENSE_ITEM
        (BUYORDER_EXPENSE_ID, ARRIVAL_STATUS, ARRIVAL_TIME,
         DELIVERY_STATUS, DELIVERY_TIME, INVOICE_STATUS, INVOICE_TIME, GOODS_ID, NUM, IS_DELETE,
         ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.buyorderExpenseId,jdbcType=INTEGER}, #{item.arrivalStatus,jdbcType=INTEGER},
             #{item.arrivalTime,jdbcType=TIMESTAMP},  #{item.deliveryStatus,jdbcType=INTEGER},
             #{item.deliveryTime,jdbcType=TIMESTAMP}, #{item.invoiceStatus,jdbcType=INTEGER},
             #{item.invoiceTime,jdbcType=TIMESTAMP}, #{item.goodsId,jdbcType=INTEGER}, #{item.num,jdbcType=INTEGER},
             #{item.isDelete,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
             #{item.creatorName,jdbcType=VARCHAR}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.updater,jdbcType=INTEGER},
             #{item.updaterName,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="selectWithAssignmentManagerByBuyorderExpenseIdAndIsDelete"
            resultMap="itemDtoAndAssignmentManager">
        select a.BUYORDER_EXPENSE_ID,
               a.ARRIVAL_STATUS ,
               a.ARRIVAL_TIME,
               a.DELIVERY_STATUS,
               a.DELIVERY_TIME,
               a.INVOICE_STATUS,
               a.INVOICE_TIME,
               a.GOODS_ID,
               a.NUM,
               a.IS_DELETE,
               TBEID.BUYORDER_EXPENSE_ITEM_DETAIL_ID,
               TBEID.BUYORDER_EXPENSE_ITEM_ID,
               TBEID.SKU,
               TBEID.GOODS_NAME,
               TBEID.BRAND_NAME,
               TBEID.MODEL,
               TBEID.UNIT_NAME,
               TBEID.PRICE,
               TBEID.CURRENCY_UNIT_ID,
               TBEID.EXPENSE_CATEGORY_ID,
               TBEID.EXPENSE_CATEGORY_NAME,
               TBEID.HAVE_STOCK_MANAGE,
               TBEID.INSIDE_COMMENTS,
               TBEID.IS_DELETE,
               d.USERNAME assignmentManager
        from T_BUYORDER_EXPENSE_ITEM a
                 left join T_BUYORDER_EXPENSE_ITEM_DETAIL TBEID
                           on a.BUYORDER_EXPENSE_ITEM_ID = TBEID.BUYORDER_EXPENSE_ITEM_ID
                 left join V_CORE_SKU b on b.SKU_ID = a.GOODS_ID
                 left join V_CORE_SPU c on c.SPU_ID = b.SPU_ID
                 left join T_USER d on d.USER_ID = c.ASSIGNMENT_MANAGER_ID
        where a.BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
          and a.IS_DELETE = #{isDelete,jdbcType=INTEGER}
          and TBEID.IS_DELETE = #{isDelete,jdbcType=INTEGER}
    </select>
    <select id="getByBuyOrderId" resultMap="itemDto">
        SELECT
            BEI.BUYORDER_EXPENSE_ITEM_ID,
            BEID.SKU,
            BEID.GOODS_NAME,
            BEID.EXPENSE_CATEGORY_NAME,
            BEID.HAVE_STOCK_MANAGE,
            BEI.NUM,
            BEID.PRICE,
            BEID.INSIDE_COMMENTS,
            BEI.GOODS_ID,
            BEI.BUYORDER_EXPENSE_ID,
            BEI.IS_DELETE,
            BEID.MODEL,
            BEID.UNIT_NAME,
            BEID.BRAND_NAME,
            BEID.BUYORDER_EXPENSE_ITEM_DETAIL_ID
        FROM
            T_BUYORDER_EXPENSE_ITEM BEI
            LEFT JOIN T_BUYORDER_EXPENSE_ITEM_DETAIL BEID ON BEI.BUYORDER_EXPENSE_ITEM_ID  = BEID.BUYORDER_EXPENSE_ITEM_ID
        LEFT JOIN T_BUYORDER_EXPENSE BE ON
            BEI.BUYORDER_EXPENSE_ID = BE.BUYORDER_EXPENSE_ID
        WHERE
            BE.BUYORDER_ID =
            #{buyOrderId,jdbcType=INTEGER}
            AND BEI.IS_DELETE = 0
            AND BE.ORDER_TYPE = 0
        ORDER BY
            BEI.ADD_TIME ASC
    </select>

    <select id="selectByBuyorderExpenseIdAndIsDelete" resultMap="itemDto">
        select TBEI.BUYORDER_EXPENSE_ID,
        TBEI.ARRIVAL_STATUS ,
        TBEI.ARRIVAL_TIME,
        TBEI.DELIVERY_STATUS,
        TBEI.DELIVERY_TIME,
        TBEI.INVOICE_STATUS,
        TBEI.INVOICE_TIME,
        TBEI.GOODS_ID,
        TBEI.NUM,
        TBEI.IS_DELETE,
        TBEID.BUYORDER_EXPENSE_ITEM_DETAIL_ID,
        TBEID.BUYORDER_EXPENSE_ITEM_ID,
        TBEID.SKU,
        TBEID.GOODS_NAME,
        TBEID.BRAND_NAME,
        TBEID.MODEL,
        TBEID.UNIT_NAME,
        TBEID.PRICE,
        TBEID.CURRENCY_UNIT_ID,
        TBEID.EXPENSE_CATEGORY_ID,
        TBEID.EXPENSE_CATEGORY_NAME,
        TBEID.HAVE_STOCK_MANAGE,
        TBEID.INSIDE_COMMENTS,
        TBEID.IS_DELETE
        from T_BUYORDER_EXPENSE_ITEM TBEI
        left join T_BUYORDER_EXPENSE_ITEM_DETAIL TBEID
        on TBEI.BUYORDER_EXPENSE_ITEM_ID = TBEID.BUYORDER_EXPENSE_ITEM_ID
        where TBEI.BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
        and TBEI.IS_DELETE = #{isDelete,jdbcType=INTEGER}
        and TBEID.IS_DELETE = #{isDelete,jdbcType=INTEGER}
    </select>
    <select id="getExpenseItemAndInvoiceList" resultType="com.vedeng.erp.aftersale.dto.ExpenseItemAndInvoiceDto">
        SELECT
            ti.INVOICE_NO ,
            ti.INVOICE_CODE,
            tbei.BUYORDER_EXPENSE_ITEM_ID ,
            SUM(IF(ti.COLOR_TYPE = 2 AND ti.IS_ENABLE = 1, tid.NUM, tid.NUM * -1)) AS INVOICE_NUM,
            tbeid.SKU,
            tbeid.GOODS_NAME ,
            tbeid.BRAND_NAME ,
            tbeid.MODEL ,
            tbeid.PRICE ,
            tbei.NUM,
            tbei.GOODS_ID,
            tbeid.UNIT_NAME
        FROM
            T_INVOICE ti
        INNER JOIN T_INVOICE_DETAIL tid ON
            ti.INVOICE_ID = tid.INVOICE_ID
        LEFT JOIN T_BUYORDER_EXPENSE_ITEM tbei ON
            tbei.BUYORDER_EXPENSE_ITEM_ID = tid.DETAILGOODS_ID
        LEFT JOIN T_BUYORDER_EXPENSE_ITEM_DETAIL tbeid ON
            tbei.BUYORDER_EXPENSE_ITEM_ID = tbeid.BUYORDER_EXPENSE_ITEM_ID
        WHERE
            ti.VALID_STATUS = 1
            AND ti.COMPANY_ID = 1
            AND ti.`TYPE` = 4126
            AND tbei.IS_DELETE = 0
            AND tbeid.IS_DELETE = 0
            AND ti.COLOR_TYPE = 2
            AND tbei.BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
        GROUP BY
            ti.INVOICE_NO ,
            ti.INVOICE_CODE,
            tid.DETAILGOODS_ID
        HAVING INVOICE_NUM > 0
    </select>
    <select id="getByBuyorderExpenseId" resultType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemEntity">
        select
        <include refid="Base_Column_List"/>
        from T_BUYORDER_EXPENSE_ITEM
        where BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
        and IS_DELETE = 0
    </select>
    <select id="getExpenseItemModifyInfo" resultMap="itemDto">
        SELECT
            tbmaeg.BUYORDER_EXPENSE_ITEM_ID,
            tbmaeg.INSIDE_COMMENTS,
            tbmaeg.OLD_INSIDE_COMMENTS,
            tbeid.SKU,
            tbeid.GOODS_NAME,
            tbeid.EXPENSE_CATEGORY_NAME ,
            tbeid.HAVE_STOCK_MANAGE,
            tbei.NUM,
            tbeid.PRICE
        FROM
            T_BUYORDER_MODIFY_APPLY_EXPENSE_GOODS tbmaeg
        LEFT JOIN T_BUYORDER_EXPENSE_ITEM_DETAIL tbeid ON
            tbmaeg.BUYORDER_EXPENSE_ITEM_ID = tbeid.BUYORDER_EXPENSE_ITEM_ID
        LEFT JOIN T_BUYORDER_EXPENSE_ITEM tbei ON
            tbeid.BUYORDER_EXPENSE_ITEM_ID = tbei.BUYORDER_EXPENSE_ITEM_ID
        WHERE
            tbmaeg.BUYORDER_MODIFY_APPLY_ID = #{buyorderModifyApplyId,jdbcType=INTEGER}
            AND tbmaeg.IS_DELETE = 0
            AND tbeid.IS_DELETE = 0
            AND tbei.IS_DELETE = 0
    </select>

    <update id="deliveryAllByBuyorderExpenseId">
        UPDATE T_BUYORDER_EXPENSE_ITEM SET DELIVERY_STATUS = 2,DELIVERY_TIME = NOW()
        WHERE BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
    </update>
    <update id="batchUpdate">
        <foreach collection="list" item="item" index="index" separator=";">
            update T_BUYORDER_EXPENSE_ITEM
            set
                BUYORDER_EXPENSE_ID = #{item.buyorderExpenseId,jdbcType=INTEGER},
                ARRIVAL_STATUS      = #{item.arrivalStatus,jdbcType=INTEGER},
                ARRIVAL_TIME        = #{item.arrivalTime,jdbcType=TIMESTAMP},
                DELIVERY_STATUS     = #{item.deliveryStatus,jdbcType=INTEGER},
                DELIVERY_TIME       = #{item.deliveryTime,jdbcType=TIMESTAMP},
                INVOICE_STATUS      = #{item.invoiceStatus,jdbcType=INTEGER},
                INVOICE_TIME        = #{item.invoiceTime,jdbcType=TIMESTAMP},
                GOODS_ID            = #{item.goodsId,jdbcType=INTEGER},
                NUM                 = #{item.num,jdbcType=INTEGER},
                IS_DELETE           = #{item.isDelete,jdbcType=INTEGER}
            where BUYORDER_EXPENSE_ITEM_ID = #{item.buyorderExpenseItemId,jdbcType=INTEGER}
        </foreach>
    </update>

    <delete id="arrivalAllByBuyorderExpenseId">
        UPDATE T_BUYORDER_EXPENSE_ITEM SET ARRIVAL_STATUS = 2,ARRIVAL_TIME = NOW()
        WHERE BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
    </delete>

    <select id="getByExpenseIdAndSkuNo" resultType="com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto">
        SELECT
            tbei.*
        FROM
            T_BUYORDER_EXPENSE_ITEM tbei
                LEFT JOIN V_CORE_SKU vcs ON
                tbei.GOODS_ID = vcs.SKU_ID
        WHERE
            tbei.BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
          AND tbei.IS_DELETE = 0
          AND vcs.SKU_NO = #{skuNo,jdbcType=VARCHAR}
    </select>

</mapper>