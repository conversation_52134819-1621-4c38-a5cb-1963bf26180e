$(function() {
    $("#submit").click(function(){
        checkLogin();
        $(".warning").remove();
        $("input").removeClass("errorbor");

        if ($("#realName").val().length == 0){
            warnTips("realName"," 请输入客户姓名");
            return false;
        }
        if ($("#realName").val().length > 128){
            warnTips("realName"," 输入客户姓名过长");
            return false;
        }

        /*var phoneNumber = $("#phoneNumber").val();*/

        $.ajax({
            url:page_url+'/orderstream/saleorder/saveElectronicSignature.do',
            data:$('#addElectronicSignature').serialize(),
            type:"POST",
            dataType : "json",
            async: false,
            success:function(data){
                if (null!=data && data.code != 0) {
                    layer.alert(data.message);
                }else {
                    //$('#close-layer').click();
                    parent.layer.closeAll();
                    parent.openNewLayer();
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
        return false;
    });
});