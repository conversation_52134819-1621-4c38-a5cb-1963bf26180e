package com.vedeng.doc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2022/1/11 13:31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocBuzTagFileDto {

    // -- 额外字段

    /**
     * oss链接
     */
    private String ossUrl;

    /**
     * 掌上小贝-外部链接
     */
    private String ossLinkMobile;

    /**
     * 文件增加时间
     */
    private String addTimeStr;

    /**
     * 文件创建者姓名
     */
    private String createName;

    /**
     * 源文件名
     */
    private String originalFileName;


    private String validStartTimeStr;

    private String validEndTimeStr;

    private Long operateTime;

    private Integer operateType;

    private String operateTimeStr;

    private String domain;

    private String uri;

    private String md5;

    private String fileUrl;

    private String suffix;

    private Integer isValid;

    private Integer maintainValidPeriod;


    // -- 基本信息

    /**
     * id
     */
    private Integer id;

    /**
     * 文件ID
     */
    private Integer fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 业务类型，1：商品资料，2：厂商资质
     */
    private Integer buzType;

    /**
     * 业务主键
     */
    private Integer buzId;

    /**
     * 业务标签ID
     */
    private Integer buzTagId;

    /**
     * 效期开始时间
     */
    private Long validStartTime;

    /**
     * 效期截止时间
     */
    private Long validEndTime;

    /**
     * 是否含章，0不含章，1含章
     */
    private Integer hasStamp;

    /**
     * 外部跳转链接
     */
    private String externalUrl;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 是否删除
     */
    private Integer isDelete;
}
