<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.SaleorderMapper" >
      <resultMap id="BaseResultMap" type="com.vedeng.order.model.Saleorder">
        <id column="SALEORDER_ID" property="saleorderId" jdbcType="INTEGER"/>
        <result column="QUOTEORDER_ID" property="quoteorderId" jdbcType="INTEGER"/>
        <result column="PARENT_ID" property="parentId" jdbcType="INTEGER"/>
        <result column="SALEORDER_NO" property="saleorderNo" jdbcType="VARCHAR"/>
        <result column="ORDER_TYPE" property="orderType" jdbcType="BIT"/>
        <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER"/>
        <result column="ORG_ID" property="orgId" jdbcType="INTEGER"/>
        <result column="USER_ID" property="userId" jdbcType="INTEGER"/>
        <result column="VALID_STATUS" property="validStatus" jdbcType="BIT"/>
        <result column="VALID_TIME" property="validTime" jdbcType="BIGINT"/>
        <result column="STATUS" property="status" jdbcType="BIT"/>
        <result column="PURCHASE_STATUS" property="purchaseStatus" jdbcType="BIT"/>
        <result column="LOCKED_STATUS" property="lockedStatus" jdbcType="BIT"/>
        <result column="INVOICE_STATUS" property="invoiceStatus" jdbcType="BIT"/>
        <result column="INVOICE_TIME" property="invoiceTime" jdbcType="BIGINT"/>
        <result column="PAYMENT_STATUS" property="paymentStatus" jdbcType="BIT"/>
        <result column="PAYMENT_TIME" property="paymentTime" jdbcType="BIGINT"/>
        <result column="DELIVERY_STATUS" property="deliveryStatus" jdbcType="BIT"/>
        <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT"/>
        <result column="IS_CUSTOMER_ARRIVAL" property="isCustomerArrival" jdbcType="BIT"/>
        <result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="BIT"/>
        <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT"/>
        <result column="SERVICE_STATUS" property="serviceStatus" jdbcType="BIT"/>
        <result column="HAVE_ACCOUNT_PERIOD" property="haveAccountPeriod" jdbcType="BIT"/>
        <result column="TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL"/>
        <result column="TRADER_ID" property="traderId" jdbcType="INTEGER"/>
        <result column="CUSTOMER_TYPE" property="customerType" jdbcType="INTEGER"/>
        <result column="CUSTOMER_NATURE" property="customerNature" jdbcType="INTEGER"/>
        <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR"/>
        <result column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER"/>
        <result column="TRADER_CONTACT_NAME" property="traderContactName" jdbcType="VARCHAR"/>
        <result column="TRADER_CONTACT_MOBILE" property="traderContactMobile" jdbcType="VARCHAR"/>
        <result column="TRADER_CONTACT_TELEPHONE" property="traderContactTelephone" jdbcType="VARCHAR"/>
        <result column="TRADER_ADDRESS_ID" property="traderAddressId" jdbcType="INTEGER"/>
        <result column="TRADER_ADDRESS" property="traderAddress" jdbcType="VARCHAR"/>
        <result column="TRADER_COMMENTS" property="traderComments" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_ID" property="takeTraderId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_NAME" property="takeTraderName" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_CONTACT_ID" property="takeTraderContactId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_CONTACT_NAME" property="takeTraderContactName" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_CONTACT_MOBILE" property="takeTraderContactMobile" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_CONTACT_TELEPHONE" property="takeTraderContactTelephone" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_ADDRESS_ID" property="takeTraderAddressId" jdbcType="INTEGER"/>
        <result column="TAKE_TRADER_ADDRESS" property="takeTraderAddress" jdbcType="VARCHAR"/>
        <result column="IS_SEND_INVOICE" property="isSendInvoice" jdbcType="BIT"/>
        <result column="INVOICE_TRADER_ID" property="invoiceTraderId" jdbcType="INTEGER"/>
        <result column="INVOICE_TRADER_NAME" property="invoiceTraderName" jdbcType="VARCHAR"/>
        <result column="INVOICE_TRADER_CONTACT_ID" property="invoiceTraderContactId" jdbcType="INTEGER"/>
        <result column="INVOICE_TRADER_CONTACT_NAME" property="invoiceTraderContactName" jdbcType="VARCHAR"/>
        <result column="INVOICE_TRADER_CONTACT_MOBILE" property="invoiceTraderContactMobile" jdbcType="VARCHAR"/>
        <result column="INVOICE_TRADER_CONTACT_TELEPHONE" property="invoiceTraderContactTelephone" jdbcType="VARCHAR"/>
        <result column="INVOICE_TRADER_ADDRESS_ID" property="invoiceTraderAddressId" jdbcType="INTEGER"/>
        <result column="INVOICE_TRADER_ADDRESS" property="invoiceTraderAddress" jdbcType="VARCHAR"/>
        <result column="SALES_AREA_ID" property="salesAreaId" jdbcType="INTEGER"/>
        <result column="SALES_AREA" property="salesArea" jdbcType="VARCHAR"/>
        <result column="TERMINAL_TRADER_ID" property="terminalTraderId" jdbcType="INTEGER"/>
        <result column="TERMINAL_TRADER_NAME" property="terminalTraderName" jdbcType="VARCHAR"/>
        <result column="TERMINAL_TRADER_TYPE" property="terminalTraderType" jdbcType="INTEGER"/>
        <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER"/>
        <result column="INVOICE_METHOD" property="invoiceMethod" jdbcType="INTEGER"/>
        <result column="FREIGHT_DESCRIPTION" property="freightDescription" jdbcType="INTEGER"/>
        <result column="DELIVERY_TYPE" property="deliveryType" jdbcType="INTEGER"/>
        <result column="LOGISTICS_ID" property="logisticsId" jdbcType="INTEGER"/>
        <result column="PAYMENT_TYPE" property="paymentType" jdbcType="INTEGER"/>
        <result column="PREPAID_AMOUNT" property="prepaidAmount" jdbcType="DECIMAL"/>
        <result column="ACCOUNT_PERIOD_AMOUNT" property="accountPeriodAmount" jdbcType="DECIMAL"/>
        <result column="LOGISTICS_COLLECTION" property="logisticsCollection" jdbcType="BIT"/>
        <result column="RETAINAGE_AMOUNT" property="retainageAmount" jdbcType="DECIMAL"/>
        <result column="RETAINAGE_AMOUNT_MONTH" property="retainageAmountMonth" jdbcType="INTEGER"/>
        <result column="PAYMENT_COMMENTS" property="paymentComments" jdbcType="VARCHAR"/>
        <result column="ADDITIONAL_CLAUSE" property="additionalClause" jdbcType="VARCHAR"/>
        <result column="LOGISTICS_COMMENTS" property="logisticsComments" jdbcType="VARCHAR"/>
        <result column="FINANCE_COMMENTS" property="financeComments" jdbcType="VARCHAR"/>
        <result column="COMMENTS" property="comments" jdbcType="VARCHAR"/>
        <result column="INVOICE_COMMENTS" property="invoiceComments" jdbcType="VARCHAR"/>
        <result column="DELIVERY_DIRECT" property="deliveryDirect" jdbcType="BIT"/>
        <result column="SUPPLIER_CLAUSE" property="supplierClause" jdbcType="VARCHAR"/>
        <result column="HAVE_ADVANCE_PURCHASE" property="haveAdvancePurchase" jdbcType="BIT"/>
        <result column="IS_URGENT" property="isUrgent" jdbcType="BIT"/>
        <result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL"/>
        <result column="HAVE_COMMUNICATE" property="haveCommunicate" jdbcType="BIT"/>
        <result column="PREPARE_COMMENTS" property="prepareComments" jdbcType="VARCHAR"/>
        <result column="MARKETING_PLAN" property="marketingPlan" jdbcType="VARCHAR"/>
        <result column="STATUS_COMMENTS" property="statusComments" jdbcType="INTEGER"/>
        <result column="SYNC_STATUS" property="syncStatus" jdbcType="BIT"/>
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT"/>
        <result column="CREATOR" property="creator" jdbcType="INTEGER"/>
        <result column="MOD_TIME" property="modTime" jdbcType="BIGINT"/>
        <result column="UPDATER" property="updater" jdbcType="INTEGER"/>
        <result column="TRADER_AREA" property="traderArea" jdbcType="VARCHAR"/>
        <result column="TAKE_TRADER_AREA" property="takeTraderArea" jdbcType="VARCHAR"/>
        <result column="INVOICE_TRADER_AREA" property="invoiceTraderArea" jdbcType="VARCHAR"/>
        <result column="IS_PAYMENT" property="isPayment" jdbcType="BIT"/>
        <result column="VERIFIES_TYPE" property="verifiesType" jdbcType="INTEGER"/>
        <result column="VERIFY_USERNAME" property="verifyUsername" jdbcType="VARCHAR"/>
        <result column="VERIFY_STATUS" property="verifyStatus" jdbcType="INTEGER"/>
        <result column="CONTRACT_STATUS" property="contractStatus" jdbcType="INTEGER"/>
        <result column="ADVANCE_PURCHASE_STATUS" property="advancePurchaseStatus" jdbcType="INTEGER"/>
        <result column="ADVANCE_PURCHASE_COMMENTS" property="advancePurchaseComments" jdbcType="VARCHAR"/>
        <result column="ADVANCE_PURCHASE_TIME" property="advancePurchaseTime" jdbcType="BIGINT"/>

		<result column="IS_SALES_PERFORMANCE" property="isSalesPerformance" jdbcType="INTEGER" />
	    <result column="SALES_PERFORMANCE_TIME" property="salesPerformanceTime" jdbcType="BIGINT" />

        <result column="SATISFY_INVOICE_TIME" property="satisfyInvoiceTime" jdbcType="BIGINT"/>
        <result column="SATISFY_DELIVERY_TIME" property="satisfyDeliveryTime" jdbcType="BIGINT"/>

        <result column="IS_DELAY_INVOICE" property="isDelayInvoice" jdbcType="BIT"/>
        <result column="LOCKED_REASON" property="lockedReason" jdbcType="VARCHAR"/>
        <result column="COST_USER_IDS" property="costUserIds" jdbcType="VARCHAR"/>
		<!-- 收票邮箱 -->
		<result column="INVOICE_EMAIL" property="invoiceEmail" jdbcType="VARCHAR" />
		<!-- 订单归属  userId -->
		<result column="OWNER_USER_ID" property="ownerUserId" jdbcType="INTEGER" />


		<result column="PAY_TYPE" property="payType" jdbcType="BIT"/>
		<result column="PAYMENT_MODE" property="paymentMode" jdbcType="BIT"/>

		<result column="TRADER_AREA_ID" property="traderAreaId" jdbcType="INTEGER"/>
		<result column="TAKE_TRADER_AREA_ID" property="takeTraderAreaId" jdbcType="INTEGER"/>
		<result column="INVOICE_TRADER_AREA_ID" property="invoiceTraderAreaId" jdbcType="INTEGER"/>
		<result column="CREATE_MOBILE" property="createMobile" jdbcType="VARCHAR"/>
		<result column="BD_MOBILE_TIME" property="bdMobileTime" jdbcType="BIT"/>
		  <result column="ACTION_ID" property="actionId" jdbcType="INTEGER"/>
		  <result column="IS_PRINTOUT" property="isPrintout" jdbcType="INTEGER"/>

		  <result column="DELIVERY_METHOD" property="deliveryMethod" jdbcType="INTEGER"/>
		  <result column="SEND_TO_PC" property="sendToPc" jdbcType="INTEGER"/>
		  <result column="SSO_ACCOUNT_ID" property="ssoAccountId" jdbcType="INTEGER"/>
		  <result column="WEB_ACCOUNT_ID" property="webAccountId" jdbcType="INTEGER"/>
		  <result column="IS_SAME_ADDRESS" property="isSameAddress" jdbcType="TINYINT"/>
		  <result column="INVOICE_SEND_NODE" property="invoiceSendNode" jdbcType="TINYINT"/>

		  <result column="IS_COUPONS" property="isCoupons" jdbcType="INTEGER"/>
		  <result column="CONTRACT_URL" property="contractUrl" jdbcType="VARCHAR" />
		  <result column="IS_CONTRACT_RETURN" property="isContractReturn" jdbcType="TINYINT"/>
		  <result column="IS_DELIVERYORDER_RETURN" property="isDeliveryOrderReturn" jdbcType="TINYINT"/>
		  <result column="DELIVERY_CLAIM" property="deliveryClaim" jdbcType="TINYINT"/>
	   <result column="AUTO_AUDIT" property="autoAudit" jdbcType="INTEGER"/>

		  <result column="GROUP_CUSTOMER_ID" property="groupCustomerId" jdbcType="INTEGER"/>
		  <result column="DELIVERY_CLAIM" property="deliveryClaim" jdbcType="TINYINT"/>
		  <result column="DELIVERY_DELAY_TIME" property="deliveryDelayTime" jdbcType="BIGINT"/>
		  <result column="BILL_PERIOD_SETTLEMENT_TYPE" property="billPeriodSettlementType" jdbcType="INTEGER"/>
		  <result column="CONFIRMATION_FORM_UPLOAD" property="confirmationFormUpload" jdbcType="INTEGER"/>
		  <result column="CONFIRMATION_FORM_AUDIT" property="confirmationFormAudit" jdbcType="INTEGER"/>
		  <result column="CONFIRMATION_SUBMIT_TIME" property="confirmationSubmitTime" jdbcType="BIGINT"/>
		  <result column="NUMBER" property="number" jdbcType="VARCHAR"/>

    </resultMap>

     <resultMap type="com.vedeng.order.model.SaleorderContract" id="ContractResultMap" extends="BaseResultMap">
     	<result column="CUSTOMER_NAME" property="customerName" jdbcType="VARCHAR"/>
      	<result column="FIRST_PAYMENT_TIME" property="firstPaymentTime" jdbcType="VARCHAR"/>
       	<result column="NOW_DAY_BETWEEN" property="nowDayBetween" jdbcType="INTEGER"/>
       	<result column="CONTRACT_TYPE" property="contractType" jdbcType="VARCHAR"/>
       	<result column="PAYMENT_AMOUNT" property="paymentAmount" jdbcType="DECIMAL"/>
       	<result column="OPT_USER_NAME" property="optUserName" jdbcType="VARCHAR"/>
    </resultMap>

	<resultMap type="com.vedeng.order.model.GoodsData" id="goodsDataMap">
		<result column="GOODS_ID" property="goodsId" jdbcType="INTEGER"/>
		<result column="occupyNum" property="occupyNum" jdbcType="INTEGER"/>
		<result column="saleNum30" property="saleNum30" jdbcType="INTEGER"/>
		<result column="needNum" property="needNum" jdbcType="INTEGER"/>
	</resultMap>







	<sql id="Base_Column_List">
	   SALEORDER_ID, QUOTEORDER_ID, PARENT_ID, SALEORDER_NO, M_SALEORDER_NO, ORDER_TYPE,
    COMPANY_ID, SOURCE, CREATOR_ORG_ID, CREATOR_ORG_NAME, ORG_ID, ORG_NAME, USER_ID,
    VALID_ORG_ID, VALID_ORG_NAME, VALID_USER_ID, VALID_STATUS, VALID_TIME, END_TIME,
    STATUS, PURCHASE_STATUS, LOCKED_STATUS, INVOICE_STATUS, INVOICE_TIME, PAYMENT_STATUS,
    PAYMENT_TIME, DELIVERY_STATUS, DELIVERY_TIME, IS_CUSTOMER_ARRIVAL, ARRIVAL_STATUS,
    ARRIVAL_TIME, SERVICE_STATUS, HAVE_ACCOUNT_PERIOD, IS_PAYMENT, TOTAL_AMOUNT, TRADER_ID,
    CUSTOMER_TYPE, CUSTOMER_NATURE, TRADER_NAME, TRADER_CONTACT_ID, TRADER_CONTACT_NAME,
    TRADER_CONTACT_MOBILE, TRADER_CONTACT_TELEPHONE, TRADER_ADDRESS_ID, TRADER_AREA_ID,
    TRADER_AREA, TRADER_ADDRESS, TRADER_COMMENTS, TAKE_TRADER_ID, TAKE_TRADER_NAME, TAKE_TRADER_CONTACT_ID,
    TAKE_TRADER_CONTACT_NAME, TAKE_TRADER_CONTACT_MOBILE, TAKE_TRADER_CONTACT_TELEPHONE,
    TAKE_TRADER_ADDRESS_ID, TAKE_TRADER_AREA_ID, TAKE_TRADER_AREA, TAKE_TRADER_ADDRESS,
    IS_SEND_INVOICE, INVOICE_TRADER_ID, INVOICE_TRADER_NAME, INVOICE_TRADER_CONTACT_ID,
    INVOICE_TRADER_CONTACT_NAME, INVOICE_TRADER_CONTACT_MOBILE, INVOICE_TRADER_CONTACT_TELEPHONE,
    INVOICE_TRADER_ADDRESS_ID, INVOICE_TRADER_AREA_ID, INVOICE_TRADER_AREA, INVOICE_TRADER_ADDRESS,
    SALES_AREA_ID, SALES_AREA, TERMINAL_TRADER_ID, TERMINAL_TRADER_NAME, TERMINAL_TRADER_TYPE,
    INVOICE_TYPE, FREIGHT_DESCRIPTION, DELIVERY_TYPE, LOGISTICS_ID, PAYMENT_TYPE, PREPAID_AMOUNT,
    ACCOUNT_PERIOD_AMOUNT, PERIOD_DAY, LOGISTICS_COLLECTION, RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH,
    PAYMENT_COMMENTS, ADDITIONAL_CLAUSE, LOGISTICS_COMMENTS, FINANCE_COMMENTS, COMMENTS,
    INVOICE_COMMENTS, DELIVERY_DIRECT, SUPPLIER_CLAUSE, HAVE_ADVANCE_PURCHASE, ADVANCE_PURCHASE_STATUS,
    ADVANCE_PURCHASE_COMMENTS, ADVANCE_PURCHASE_TIME, IS_URGENT, URGENT_AMOUNT, HAVE_COMMUNICATE,
    PREPARE_COMMENTS, MARKETING_PLAN, STATUS_COMMENTS, SYNC_STATUS, LOGISTICS_API_SYNC,
    LOGISTICS_WXSEND_SYNC, SATISFY_INVOICE_TIME, SATISFY_DELIVERY_TIME, IS_SALES_PERFORMANCE,
    SALES_PERFORMANCE_TIME, SALES_PERFORMANCE_MOD_TIME, IS_DELAY_INVOICE, INVOICE_METHOD,
    LOCKED_REASON, COST_USER_IDS, OWNER_USER_ID, INVOICE_EMAIL, PAYMENT_MODE, PAY_TYPE,
    IS_APPLY_INVOICE, APPLY_INVOICE_TIME, ADD_TIME, CREATOR, MOD_TIME, UPDATER, ADK_SALEORDER_NO,
    CREATE_MOBILE, BDTRADER_COMMENTS, CLOSE_COMMENTS, BD_MOBILE_TIME, WEB_TAKE_DELIVERY_TIME,ACTION_ID,
    IS_PRINTOUT,SEND_TO_PC,IS_SAME_ADDRESS,INVOICE_SEND_NODE,IS_COUPONS,AUTO_AUDIT,IS_RISK,RISK_COMMENTS,RISK_TIME
	, GROUP_CUSTOMER_ID, IS_CONTRACT_RETURN, IS_DELIVERYORDER_RETURN ,DELIVERY_CLAIM ,DELIVERY_DELAY_TIME,
	BILL_PERIOD_SETTLEMENT_TYPE,IS_NEW,CONFIRM_TIME,CONFIRM_STATUS,CONFIRMATION_FORM_UPLOAD,CONFIRMATION_FORM_AUDIT,CONFIRMATION_SUBMIT_TIME
	</sql>
    <update id="cancelValidOfSaleOrder">
		update T_SALEORDER
		set VALID_STATUS = 0,VALID_TIME = 0,STATUS = 0, IS_RISK = 0, MOD_TIME = #{modTime}
		where SALEORDER_ID = #{saleorderId}
	</update>
    <!---->
  <select id="getSaleOrderIdListByParam" resultType="java.lang.Integer">
	SELECT
		ts.SALEORDER_ID
	FROM
		T_SALEORDER ts
	LEFT JOIN T_ATTACHMENT ta ON ts.SALEORDER_ID = ta.RELATED_ID
	WHERE
		1 = 1
	AND ta.ATTACHMENT_FUNCTION = #{attachmentFunction, javaType=INTEGER}
	AND ts.COMPANY_ID = #{companyId, javaType=INTEGER}
	AND(ts.VALID_TIME / 1000) <![CDATA[ >= ]]> UNIX_TIMESTAMP('2018-01-01')
	AND(ts.VALID_TIME / 1000) <![CDATA[ <= ]]> UNIX_TIMESTAMP('2018-12-31')
	GROUP BY
		ts.SALEORDER_ID
  </select>

  <select id="getOrderListInfoById" resultType="com.vedeng.order.model.Saleorder">
	SELECT
		a.SALEORDER_ID,
		u.USERNAME AS verifyUsername
	FROM
		T_SALEORDER a
	LEFT JOIN T_QUOTEORDER c ON a.QUOTEORDER_ID = c.QUOTEORDER_ID
	LEFT JOIN T_BUSSINESS_CHANCE d ON c.BUSSINESS_CHANCE_ID = d.BUSSINESS_CHANCE_ID
	LEFT JOIN T_VERIFIES_INFO e ON a.SALEORDER_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_SALEORDER'
	AND e.VERIFIES_TYPE = 623
	LEFT JOIN T_VERIFIES_INFO con ON a.SALEORDER_ID = con.RELATE_TABLE_KEY
	AND con.RELATE_TABLE = 'T_SALEORDER'
	AND con.VERIFIES_TYPE = 868
	LEFT JOIN T_USER u ON a.USER_ID = u.USER_ID
	WHERE
		1 = 1
		AND a.SALEORDER_ID IN
	<foreach collection="list" item="saleorderId" open="(" close=")" separator=",">
		#{saleorderId, jdbcType=INTEGER}
	</foreach>
	AND a.COMPANY_ID = 1
	AND con.`STATUS` = 0
	AND con.VERIFY_USERNAME = ''
/*	AND con.`STATUS` IS NULL*/
	AND(
		a.HAVE_ACCOUNT_PERIOD = 1
		OR a.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000
	)
	AND(a.VALID_TIME / 1000) <![CDATA[ >= ]]> UNIX_TIMESTAMP('2018-01-01')
	AND(a.VALID_TIME / 1000) <![CDATA[ <= ]]> UNIX_TIMESTAMP('2018-12-31')
	AND a.ORDER_TYPE <![CDATA[ != ]]> 2
	AND a.VALID_STATUS = 1
	AND a.ADD_TIME <![CDATA[ > ]]> 0
	ORDER BY
		a.ADD_TIME DESC
  </select>
  <select id="getContractReturnOrderListPage" resultMap="ContractResultMap" parameterType="com.vedeng.order.model.SaleorderContract">
       SELECT
			ts.TRADER_NAME,
			ts.TRADER_ID,
			tu.USERNAME as OPT_USER_NAME,
			ts.SALEORDER_ID,
			ts.SALEORDER_NO,
			cbt.AMOUNT AS PAYMENT_AMOUNT,
			FROM_UNIXTIME(
				cbt.TRADE_TIME / 1000,
				'%Y-%m-%d %H:%i:%s'
			) AS FIRST_PAYMENT_TIME,
			CASE
				WHEN ts.HAVE_ACCOUNT_PERIOD = 1 THEN
					'电子+纸质'
				WHEN ts.HAVE_ACCOUNT_PERIOD = 0 AND ts.TOTAL_AMOUNT <![CDATA[ < ]]> 10000 THEN
					NULL
				WHEN ts.HAVE_ACCOUNT_PERIOD = 0 AND ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 AND ts.TOTAL_AMOUNT <![CDATA[ < ]]> 200000 THEN
					'电子'
				WHEN ts.HAVE_ACCOUNT_PERIOD = 0 AND ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 200000 THEN
					'电子+纸质'
				END AS CONTRACT_TYPE,
			DATEDIFF(SYSDATE(),FROM_UNIXTIME(
				cbt.TRADE_TIME / 1000,
				'%Y-%m-%d %H:%i:%s'
			)) AS NOW_DAY_BETWEEN,
				CASE
					WHEN DATEDIFF(
						SYSDATE(),
						FROM_UNIXTIME(
							cbt.TRADE_TIME / 1000,
							'%Y-%m-%d %H:%i:%s'
						)
					) > 30 THEN
						DATEDIFF(
						SYSDATE(),
						FROM_UNIXTIME(
							cbt.TRADE_TIME / 1000,
							'%Y-%m-%d %H:%i:%s'
						)
					)
					ELSE 30 - 	DATEDIFF(
						SYSDATE(),
						FROM_UNIXTIME(
							cbt.TRADE_TIME / 1000,
							'%Y-%m-%d %H:%i:%s'
						)
					)
				 END AS ORDER_COL
		FROM
			T_SALEORDER ts
		LEFT JOIN T_ATTACHMENT ta ON ts.SALEORDER_ID = ta.RELATED_ID
		AND ta.ATTACHMENT_FUNCTION = 492
		LEFT JOIN T_R_TRADER_J_USER trj ON ts.TRADER_ID = trj.TRADER_ID
		LEFT JOIN T_USER tu ON tu.USER_ID = trj.USER_ID
		INNER JOIN (
			SELECT
				COALESCE (
					sum(
						IF (
							a.TRADER_TYPE = 1
							OR a.TRADER_TYPE = 4,
							ABS(a.AMOUNT),

						IF (
							a.TRADER_TYPE = 2
							OR a.TRADER_TYPE = 5 ,- ABS(a.AMOUNT),
							0
						)
						)
					),
					0
				) AS AMOUNT,
				b.RELATED_ID AS SALEORDER_ID,
				MIN(a.TRADER_TIME) AS TRADE_TIME
			FROM
				T_CAPITAL_BILL a
			LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
			LEFT JOIN (
				SELECT
					COALESCE (sum(ABS(a2.AMOUNT)), 0) AS periodAmount,
					COALESCE (
						sum(ABS(a2.AMOUNT)) - IFNULL(c.hk_amount, 0) - IFNULL(d.hk_amount, 0),
						0
					) AS zq_amount,
					b2.RELATED_ID AS zq_RELATED_ID
				FROM
					T_CAPITAL_BILL a2
				LEFT JOIN T_CAPITAL_BILL_DETAIL b2 ON a2.CAPITAL_BILL_ID = b2.CAPITAL_BILL_ID
				LEFT JOIN (
					SELECT
						COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
						b1.RELATED_ID
					FROM
						T_CAPITAL_BILL a1
					LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
					WHERE
						a1.TRADER_TYPE IN (1, 4)
					AND b1.ORDER_TYPE = 1
					AND b1.BUSSINESS_TYPE = 533
					GROUP BY
						b1.RELATED_ID
				) AS c ON b2.RELATED_ID = c.RELATED_ID
				LEFT JOIN (
					SELECT
						COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
						c1.ORDER_ID
					FROM
						T_CAPITAL_BILL a1
					LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
					LEFT JOIN T_AFTER_SALES c1 ON c1.AFTER_SALES_ID = b1.RELATED_ID
					AND c1.SUBJECT_TYPE = 535
					WHERE
						a1.TRADER_TYPE = 3
					AND b1.ORDER_TYPE = 3
					AND b1.BUSSINESS_TYPE = 533
					GROUP BY
						c1.ORDER_ID
				) AS d ON d.ORDER_ID = b2.RELATED_ID
				WHERE
					a2.TRADER_TYPE = 3
				AND b2.ORDER_TYPE = 1
				AND a2.TRADER_MODE = 527
				GROUP BY
					b2.RELATED_ID
			) AS zq ON zq.zq_RELATED_ID = b.RELATED_ID
			WHERE
				b.ORDER_TYPE = 1
			AND b.BUSSINESS_TYPE != 533
			GROUP BY
				b.RELATED_ID
		) cbt ON cbt.SALEORDER_ID = ts.SALEORDER_ID
		LEFT JOIN T_VERIFIES_INFO con ON ts.SALEORDER_ID = con.RELATE_TABLE_KEY
		AND con.RELATE_TABLE = 'T_SALEORDER'
		AND con.VERIFIES_TYPE = 868
		WHERE
			trj.TRADER_TYPE = 1
		AND (ts.VALID_TIME / 1000) <![CDATA[ >= ]]> UNIX_TIMESTAMP('2018-08-01')
		AND ts.ORDER_TYPE IN (0,1, 4)
	  	AND ts.STATUS <![CDATA[ <> ]]> 3
		AND ts.COMPANY_ID = #{saleOrderContract.companyId, jdbcType=INTEGER}
		AND trj.USER_ID = #{saleOrderContract.userId, jdbcType=INTEGER}
		AND (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
		AND con.`STATUS` is null
		 <if test="saleOrderContract.customerName != null">
            and ts.TRADER_NAME LIKE  CONCAT('%',#{saleOrderContract.customerName,jdbcType=VARCHAR},'%' )
        </if>
         <if test="saleOrderContract.saleorderNo != null">
         	and ts.SALEORDER_NO LIKE  CONCAT('%',#{saleOrderContract.saleorderNo,jdbcType=VARCHAR},'%' )
        </if>
        <if test="saleOrderContract.searchBegintime != null">
            and cbt.TRADE_TIME <![CDATA[ >= ]]> #{saleOrderContract.searchBegintime}
        </if>
        <if test="saleOrderContract.searchEndtime != null">
            and cbt.TRADE_TIME <![CDATA[ <= ]]> #{saleOrderContract.searchEndtime}
        </if>
         <if test="saleOrderContract.contractType != null and saleOrderContract.contractType == '电子'">
            and ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 AND ts.TOTAL_AMOUNT <![CDATA[ < ]]> 200000
        </if>
          <if test="saleOrderContract.contractType != null and saleOrderContract.contractType == '电子+纸质'">
            and (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 200000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
        </if>
        GROUP BY ts.SALEORDER_ID
		ORDER BY ORDER_COL DESC
  </select>

  <select id="getContractReturnOrderListCount" resultType="java.lang.Integer" parameterType="Map">
      SELECT COUNT(tt.NUMS) FROM (
		SELECT
			COUNT(1) AS NUMS
		FROM
			T_SALEORDER ts
		LEFT JOIN T_ATTACHMENT ta ON ts.SALEORDER_ID = ta.RELATED_ID
		AND ta.ATTACHMENT_FUNCTION = 492
		LEFT JOIN T_R_TRADER_J_USER trj ON ts.TRADER_ID = trj.TRADER_ID

		LEFT JOIN T_VERIFIES_INFO con ON ts.SALEORDER_ID = con.RELATE_TABLE_KEY
		AND con.RELATE_TABLE = 'T_SALEORDER'
		AND con.VERIFIES_TYPE = 868
		WHERE
			trj.TRADER_TYPE = 1
		AND (ts.VALID_TIME / 1000) <![CDATA[ >= ]]> UNIX_TIMESTAMP('2018-08-01')
		AND ts.ORDER_TYPE IN (0, 1,4)
	  	AND ts.STATUS <![CDATA[ <> ]]> 3
		AND ts.COMPANY_ID = 1
		AND trj.USER_ID = #{saleOrderContract.userId, jdbcType=INTEGER}
		AND (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
		AND con.`STATUS` is null

        GROUP BY ts.SALEORDER_ID)  tt
  </select>

	<select id="getContractReturnOrderListRealityMoney" resultType="java.math.BigDecimal">
		SELECT IFNULL(SUM(tt.REAL_TOTAL_AMOUNT),0) FROM (
		SELECT
		ts.REAL_TOTAL_AMOUNT
		FROM
		T_SALEORDER ts
		LEFT JOIN T_ATTACHMENT ta ON ts.SALEORDER_ID = ta.RELATED_ID
		AND ta.ATTACHMENT_FUNCTION = 492
		LEFT JOIN T_R_TRADER_J_USER trj ON ts.TRADER_ID = trj.TRADER_ID
		LEFT JOIN T_VERIFIES_INFO con ON ts.SALEORDER_ID = con.RELATE_TABLE_KEY
		AND con.RELATE_TABLE = 'T_SALEORDER'
		AND con.VERIFIES_TYPE = 868
		WHERE
		trj.TRADER_TYPE = 1
		AND (ts.VALID_TIME / 1000) <![CDATA[ >= ]]> UNIX_TIMESTAMP('2018-08-01')
		AND ts.ORDER_TYPE IN (0, 1,4)
		AND ts.STATUS <![CDATA[ <> ]]> 3
		AND ts.COMPANY_ID = #{saleOrderContract.companyId, jdbcType=INTEGER}
		AND trj.USER_ID = #{saleOrderContract.userId, jdbcType=INTEGER}
		AND (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
		AND con.`STATUS` is null

		GROUP BY ts.SALEORDER_ID)  tt
	</select>

    <select id="getContractReturnOrderNoqualityListPage" resultMap="ContractResultMap" parameterType="com.vedeng.order.model.SaleorderContract">
       SELECT
			ts.TRADER_NAME,
			ts.TRADER_ID,
			tu.USERNAME as OPT_USER_NAME,
			ts.SALEORDER_ID,
			ts.SALEORDER_NO,
			cbt.AMOUNT AS PAYMENT_AMOUNT,
			FROM_UNIXTIME(
				cbt.TRADE_TIME / 1000,
				'%Y-%m-%d %H:%i:%s'
			) AS FIRST_PAYMENT_TIME,
			CASE
				WHEN ts.HAVE_ACCOUNT_PERIOD = 1 THEN
					'电子+纸质'
				WHEN ts.HAVE_ACCOUNT_PERIOD = 0 AND ts.TOTAL_AMOUNT <![CDATA[ < ]]> 10000 THEN
					NULL
				WHEN ts.HAVE_ACCOUNT_PERIOD = 0 AND ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 AND ts.TOTAL_AMOUNT <![CDATA[ < ]]> 200000 THEN
					'电子'
				WHEN ts.HAVE_ACCOUNT_PERIOD = 0 AND ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 200000 THEN
					'电子+纸质'
				END AS CONTRACT_TYPE,
			DATEDIFF(SYSDATE(),FROM_UNIXTIME(
				cbt.TRADE_TIME / 1000,
				'%Y-%m-%d %H:%i:%s'
			)) AS NOW_DAY_BETWEEN,
				CASE
					WHEN DATEDIFF(
						SYSDATE(),
						FROM_UNIXTIME(
							cbt.TRADE_TIME / 1000,
							'%Y-%m-%d %H:%i:%s'
						)
					) > 30 THEN
						DATEDIFF(
						SYSDATE(),
						FROM_UNIXTIME(
							cbt.TRADE_TIME / 1000,
							'%Y-%m-%d %H:%i:%s'
						)
					)
					ELSE 30 - 	DATEDIFF(
						SYSDATE(),
						FROM_UNIXTIME(
							cbt.TRADE_TIME / 1000,
							'%Y-%m-%d %H:%i:%s'
						)
					)
				 END AS ORDER_COL
		FROM
			T_SALEORDER ts
		LEFT JOIN T_R_TRADER_J_USER trj ON ts.TRADER_ID = trj.TRADER_ID
		LEFT JOIN T_USER tu ON tu.USER_ID = trj.USER_ID
		INNER JOIN (
			SELECT
				COALESCE (
					sum(
						IF (
							a.TRADER_TYPE = 1
							OR a.TRADER_TYPE = 4,
							ABS(a.AMOUNT),

						IF (
							a.TRADER_TYPE = 2
							OR a.TRADER_TYPE = 5 ,- ABS(a.AMOUNT),
							0
						)
						)
					),
					0
				) AS AMOUNT,
				b.RELATED_ID AS SALEORDER_ID,
				MIN(a.TRADER_TIME) AS TRADE_TIME
			FROM
				T_CAPITAL_BILL a
			LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
			LEFT JOIN (
				SELECT
					COALESCE (sum(ABS(a2.AMOUNT)), 0) AS periodAmount,
					COALESCE (
						sum(ABS(a2.AMOUNT)) - IFNULL(c.hk_amount, 0) - IFNULL(d.hk_amount, 0),
						0
					) AS zq_amount,
					b2.RELATED_ID AS zq_RELATED_ID
				FROM
					T_CAPITAL_BILL a2
				LEFT JOIN T_CAPITAL_BILL_DETAIL b2 ON a2.CAPITAL_BILL_ID = b2.CAPITAL_BILL_ID
				LEFT JOIN (
					SELECT
						COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
						b1.RELATED_ID
					FROM
						T_CAPITAL_BILL a1
					LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
					WHERE
						a1.TRADER_TYPE IN (1, 4)
					AND b1.ORDER_TYPE = 1
					AND b1.BUSSINESS_TYPE = 533
					GROUP BY
						b1.RELATED_ID
				) AS c ON b2.RELATED_ID = c.RELATED_ID
				LEFT JOIN (
					SELECT
						COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
						c1.ORDER_ID
					FROM
						T_CAPITAL_BILL a1
					LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
					LEFT JOIN T_AFTER_SALES c1 ON c1.AFTER_SALES_ID = b1.RELATED_ID
					AND c1.SUBJECT_TYPE = 535
					WHERE
						a1.TRADER_TYPE = 3
					AND b1.ORDER_TYPE = 3
					AND b1.BUSSINESS_TYPE = 533
					GROUP BY
						c1.ORDER_ID
				) AS d ON d.ORDER_ID = b2.RELATED_ID
				WHERE
					a2.TRADER_TYPE = 3
				AND b2.ORDER_TYPE = 1
				AND a2.TRADER_MODE = 527
				GROUP BY
					b2.RELATED_ID
			) AS zq ON zq.zq_RELATED_ID = b.RELATED_ID
			WHERE
				b.ORDER_TYPE = 1
			AND b.BUSSINESS_TYPE != 533
			GROUP BY
				b.RELATED_ID
		) cbt ON cbt.SALEORDER_ID = ts.SALEORDER_ID
		LEFT JOIN T_VERIFIES_INFO con ON ts.SALEORDER_ID = con.RELATE_TABLE_KEY
		AND con.RELATE_TABLE = 'T_SALEORDER'
		AND con.VERIFIES_TYPE = 868
		WHERE
			trj.TRADER_TYPE = 1
		AND (ts.VALID_TIME / 1000) <![CDATA[ >= ]]> UNIX_TIMESTAMP('2018-08-01')
		AND ts.ORDER_TYPE IN (0, 1,4)
		AND ts.COMPANY_ID = #{saleOrderContract.companyId, jdbcType=INTEGER}
		AND trj.USER_ID = #{saleOrderContract.userId, jdbcType=INTEGER}
		AND (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
		AND con.`STATUS` = 2
		 <if test="saleOrderContract.customerName != null">
            and ts.TRADER_NAME LIKE  CONCAT('%',#{saleOrderContract.customerName,jdbcType=VARCHAR},'%' )
        </if>
         <if test="saleOrderContract.saleorderNo != null">
         	and ts.SALEORDER_NO LIKE  CONCAT('%',#{saleOrderContract.saleorderNo,jdbcType=VARCHAR},'%' )
        </if>
        <if test="saleOrderContract.searchBegintime != null">
            and cbt.TRADE_TIME <![CDATA[ >= ]]> #{saleOrderContract.searchBegintime}
        </if>
        <if test="saleOrderContract.searchEndtime != null">
            and cbt.TRADE_TIME <![CDATA[ <= ]]> #{saleOrderContract.searchEndtime}
        </if>
         <if test="saleOrderContract.contractType != null and saleOrderContract.contractType == '电子'">
            and ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 AND ts.TOTAL_AMOUNT <![CDATA[ < ]]> 200000
        </if>
          <if test="saleOrderContract.contractType != null and saleOrderContract.contractType == '电子+纸质'">
            and (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 200000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
        </if>
        GROUP BY ts.SALEORDER_ID
		ORDER BY ORDER_COL DESC
  </select>

  <select id="getContractReturnOrderNoqualityListCount" resultType="java.lang.Integer" parameterType="Map">
      SELECT COUNT(tt.NUMS) FROM (
		SELECT
			COUNT(1) AS NUMS
		FROM
			T_SALEORDER ts
		LEFT JOIN T_R_TRADER_J_USER trj ON ts.TRADER_ID = trj.TRADER_ID
		INNER JOIN (
			SELECT
				COALESCE (
					sum(
						IF (
							a.TRADER_TYPE = 1
							OR a.TRADER_TYPE = 4,
							ABS(a.AMOUNT),

						IF (
							a.TRADER_TYPE = 2
							OR a.TRADER_TYPE = 5 ,- ABS(a.AMOUNT),
							0
						)
						)
					),
					0
				) AS AMOUNT,
				b.RELATED_ID AS SALEORDER_ID,
				MIN(a.TRADER_TIME) AS TRADE_TIME
			FROM
				T_CAPITAL_BILL a
			LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
			LEFT JOIN (
				SELECT
					COALESCE (sum(ABS(a2.AMOUNT)), 0) AS periodAmount,
					COALESCE (
						sum(ABS(a2.AMOUNT)) - IFNULL(c.hk_amount, 0) - IFNULL(d.hk_amount, 0),
						0
					) AS zq_amount,
					b2.RELATED_ID AS zq_RELATED_ID
				FROM
					T_CAPITAL_BILL a2
				LEFT JOIN T_CAPITAL_BILL_DETAIL b2 ON a2.CAPITAL_BILL_ID = b2.CAPITAL_BILL_ID
				LEFT JOIN (
					SELECT
						COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
						b1.RELATED_ID
					FROM
						T_CAPITAL_BILL a1
					LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
					WHERE
						a1.TRADER_TYPE IN (1, 4)
					AND b1.ORDER_TYPE = 1
					AND b1.BUSSINESS_TYPE = 533
					GROUP BY
						b1.RELATED_ID
				) AS c ON b2.RELATED_ID = c.RELATED_ID
				LEFT JOIN (
					SELECT
						COALESCE (sum(ABS(b1.AMOUNT)), 0) AS hk_amount,
						c1.ORDER_ID
					FROM
						T_CAPITAL_BILL a1
					LEFT JOIN T_CAPITAL_BILL_DETAIL b1 ON a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
					LEFT JOIN T_AFTER_SALES c1 ON c1.AFTER_SALES_ID = b1.RELATED_ID
					AND c1.SUBJECT_TYPE = 535
					WHERE
						a1.TRADER_TYPE = 3
					AND b1.ORDER_TYPE = 3
					AND b1.BUSSINESS_TYPE = 533
					GROUP BY
						c1.ORDER_ID
				) AS d ON d.ORDER_ID = b2.RELATED_ID
				WHERE
					a2.TRADER_TYPE = 3
				AND b2.ORDER_TYPE = 1
				AND a2.TRADER_MODE = 527
				GROUP BY
					b2.RELATED_ID
			) AS zq ON zq.zq_RELATED_ID = b.RELATED_ID
			WHERE
				b.ORDER_TYPE = 1
			AND b.BUSSINESS_TYPE != 533
			GROUP BY
				b.RELATED_ID
		) cbt ON cbt.SALEORDER_ID = ts.SALEORDER_ID
		LEFT JOIN T_VERIFIES_INFO con ON ts.SALEORDER_ID = con.RELATE_TABLE_KEY
		AND con.RELATE_TABLE = 'T_SALEORDER'
		AND con.VERIFIES_TYPE = 868
		WHERE
			trj.TRADER_TYPE = 1
		AND (ts.VALID_TIME / 1000) <![CDATA[ >= ]]> UNIX_TIMESTAMP('2018-08-01')
		AND ts.ORDER_TYPE IN (0,1, 4)
		AND ts.COMPANY_ID = #{saleOrderContract.companyId, jdbcType=INTEGER}
		AND trj.USER_ID = #{saleOrderContract.userId, jdbcType=INTEGER}
		AND (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
		AND con.`STATUS` = 2
		 <if test="saleOrderContract.customerName != null">
            and ts.TRADER_NAME LIKE  CONCAT('%',#{saleOrderContract.customerName,jdbcType=VARCHAR},'%' )
        </if>
        <if test="saleOrderContract.saleorderNo != null">
         	and ts.SALEORDER_NO LIKE  CONCAT('%',#{saleOrderContract.saleorderNo,jdbcType=VARCHAR},'%' )
        </if>
        <if test="saleOrderContract.searchBegintime != null">
            and cbt.TRADE_TIME <![CDATA[ >= ]]> #{saleOrderContract.searchBegintime}
        </if>
        <if test="saleOrderContract.searchEndtime != null">
            and cbt.TRADE_TIME <![CDATA[ <= ]]> #{saleOrderContract.searchEndtime}
        </if>
         <if test="saleOrderContract.contractType != null and saleOrderContract.contractType == '电子'">
            and ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 10000 AND ts.TOTAL_AMOUNT <![CDATA[ < ]]> 200000
        </if>
          <if test="saleOrderContract.contractType != null and saleOrderContract.contractType == '电子+纸质'">
            and (ts.TOTAL_AMOUNT <![CDATA[ >= ]]> 200000 OR ts.HAVE_ACCOUNT_PERIOD = 1)
        </if>
        GROUP BY ts.SALEORDER_ID)  tt
  </select>

	<!-- 修改订单税率保存 -->
	<update id="saveOrderRatioEdit" parameterType="java.lang.Integer">
        UPDATE T_SALEORDER A SET A.INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER} WHERE A.SALEORDER_ID = #{orderId,jdbcType=INTEGER}
    </update>
	<select id="getSaleOrderlistByStatusTraderId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
	SELECT
	A.*
	FROM
	`T_SALEORDER` A
	LEFT JOIN T_VERIFIES_INFO B
	ON B.`RELATE_TABLE` = 'T_SALEORDER'
	AND B.`RELATE_TABLE_KEY` = A.`SALEORDER_ID`
	AND B.`STATUS`= 2
	WHERE A.`TRADER_ID` = #{traderId}
	AND A.STATUS = 0
	AND A.`ORDER_TYPE`=1
	</select>
	<select id="getSaleOrderlistByStatusMobile"  resultMap="BaseResultMap">
	SELECT
	A.*
	FROM
	`T_SALEORDER` A
	LEFT JOIN T_VERIFIES_INFO B
	ON B.`RELATE_TABLE` = 'T_SALEORDER'
	AND B.`RELATE_TABLE_KEY` = A.`SALEORDER_ID`
	AND B.`STATUS`= 2
	WHERE A.`CREATE_MOBILE` = #{createMobile}
	AND A.STATUS = 0
	AND A.`ORDER_TYPE`=1
	</select>
	<!--耗材商城推送消息-->
	<select id="getHcOrderList" resultMap="ContractResultMap" parameterType="com.vedeng.order.model.SaleorderContract">
		SELECT
			SALEORDER_ID,
			SALEORDER_NO
		FROM
			T_SALEORDER
		WHERE
			1=1
		and VALID_STATUS = 0
		and ORDER_TYPE = 5
		<if test="traderId != null">
			and TRADER_ID = #{traderId, jdbcType=INTEGER}
		</if>
		<if test="saleorderNo != null and saleorderNo !=''">
			and SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
		</if>
	</select>

	<insert id="saveSaleOrderPrice">
		INSERT IGNORE INTO T_SALEORDER_PRICE(SALEORDER_ID,ALTER_PRICE,ALTER_TYPE,ADD_TIME,MOD_TIME)
		VALUES(#{saleOrderId,jdbcType=INTEGER},#{alterPrice,jdbcType=DECIMAL},#{alterType,jdbcType=INTEGER},now(),now())
	</insert>


	<select resultType="java.math.BigDecimal" id="getSaleorderPriceInfo">
		SELECT ALTER_PRICE FROM   T_SALEORDER_PRICE where SALEORDER_ID = #{saleOrderId,jdbcType=INTEGER} AND ALTER_TYPE=1
	</select>


	<update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.Saleorder" >
		update T_SALEORDER
		<set >
			<if test="originBuyOrderNo != null" >
				ORIGIN_BUYORDER_NO = #{originBuyOrderNo,jdbcType=VARCHAR},
			</if>
			<if test="invoiceSendNode != null" >
				INVOICE_SEND_NODE = #{invoiceSendNode,jdbcType=INTEGER},
			</if>
			<if test="quoteorderId != null" >
				QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
			</if>
			<if test="parentId != null" >
				PARENT_ID = #{parentId,jdbcType=INTEGER},
			</if>
			<if test="saleorderNo != null" >
				SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
			</if>
			<if test="orderType != null" >
				ORDER_TYPE = #{orderType,jdbcType=BIT},
			</if>
			<if test="companyId != null" >
				COMPANY_ID = #{companyId,jdbcType=INTEGER},
			</if>
			<if test="orgId != null" >
				ORG_ID = #{orgId,jdbcType=INTEGER},
			</if>
			<if test="orgName !=null">
				ORG_NAME = #{orgName,jdbcType=VARCHAR},
			</if>
			<if test="userId != null" >
				USER_ID = #{userId,jdbcType=INTEGER},
			</if>
			<if test="validStatus != null" >
				VALID_STATUS = #{validStatus,jdbcType=BIT},
			</if>
			<if test="validTime != null" >
				VALID_TIME = #{validTime,jdbcType=BIGINT},
			</if>
			<if test="status != null" >
				STATUS = #{status,jdbcType=BIT},
			</if>
			<if test="purchaseStatus != null" >
				PURCHASE_STATUS = #{purchaseStatus,jdbcType=BIT},
			</if>
			<if test="lockedStatus != null" >
				LOCKED_STATUS = #{lockedStatus,jdbcType=BIT},
			</if>
			<if test="invoiceStatus != null" >
				INVOICE_STATUS = #{invoiceStatus,jdbcType=BIT},
			</if>
			<if test="invoiceTime != null" >
				INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
			</if>
			<if test="paymentStatus != null" >
				PAYMENT_STATUS = #{paymentStatus,jdbcType=BIT},
			</if>
			<if test="isPayment != null" >
				IS_PAYMENT = #{isPayment,jdbcType=BIT},
			</if>
			<if test="paymentTime != null" >
				PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
			</if>
			<if test="deliveryStatus != null" >
				DELIVERY_STATUS = #{deliveryStatus,jdbcType=BIT},
			</if>
			<if test="deliveryTime != null" >
				DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
			</if>
			<if test="isCustomerArrival != null" >
				IS_CUSTOMER_ARRIVAL = #{isCustomerArrival,jdbcType=BIT},
			</if>
			<if test="arrivalStatus != null" >
				ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BIT},
			</if>
			<if test="arrivalTime != null" >
				ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
			</if>
			<if test="serviceStatus != null" >
				SERVICE_STATUS = #{serviceStatus,jdbcType=BIT},
			</if>
			<if test="haveAccountPeriod != null" >
				HAVE_ACCOUNT_PERIOD = #{haveAccountPeriod,jdbcType=BIT},
			</if>
			<if test="totalAmount != null" >
				TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
			</if>
			<if test="traderId != null" >
				TRADER_ID = #{traderId,jdbcType=INTEGER},
			</if>
			<if test="customerType != null" >
				CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
			</if>
			<if test="customerNature != null" >
				CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
			</if>
			<if test="traderName != null" >
				TRADER_NAME = #{traderName,jdbcType=VARCHAR},
			</if>
			<if test="traderContactId != null" >
				TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
			</if>
			<if test="traderContactName != null" >
				TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
			</if>
			<if test="traderContactMobile != null" >
				TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="traderContactTelephone != null" >
				TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="traderAddressId != null" >
				TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
			</if>
			<if test="traderAddress != null" >
				TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
			</if>
			<if test="traderComments != null" >
				TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderId != null" >
				TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderName != null" >
				TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactId != null" >
				TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderContactName != null" >
				TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactMobile != null" >
				TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactTelephone != null" >
				TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderAddressId != null" >
				TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderAddress != null" >
				TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
			</if>
			<if test="isSendInvoice != null" >
				IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BIT},
			</if>
			<if test="invoiceTraderId != null" >
				INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderName != null" >
				INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderContactId != null" >
				INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderContactName != null" >
				INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderContactMobile != null" >
				INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderContactTelephone != null" >
				INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderAddressId != null" >
				INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderAddress != null" >
				INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
			</if>
			<if test="salesAreaId != null" >
				SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER},
			</if>
			<if test="salesArea != null" >
				SALES_AREA = #{salesArea,jdbcType=VARCHAR},
			</if>
			<if test="terminalTraderId != null" >
				TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER},
			</if>
			<if test="terminalTraderName != null" >
				TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
			</if>
			<if test="terminalTraderType != null" >
				TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER},
			</if>
			<if test="invoiceType != null" >
				INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
			</if>
			<if test="invoiceMethod != null" >
				INVOICE_METHOD = #{invoiceMethod,jdbcType=INTEGER},
			</if>
			<if test="freightDescription != null" >
				FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
			</if>
			<if test="deliveryType != null" >
				DELIVERY_TYPE = #{deliveryType,jdbcType=INTEGER},
			</if>
			<if test="logisticsId != null" >
				LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
			</if>
			<if test="paymentType != null" >
				PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
			</if>
			<if test="prepaidAmount != null" >
				PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
			</if>
			<if test="accountPeriodAmount != null" >
				ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
			</if>
			<if test="logisticsCollection != null" >
				LOGISTICS_COLLECTION = #{logisticsCollection,jdbcType=BIT},
			</if>
			<if test="retainageAmount != null" >
				RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
			</if>
			<if test="retainageAmountMonth != null" >
				RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
			</if>
			<if test="paymentComments != null" >
				PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
			</if>
			<if test="additionalClause != null" >
				ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
			</if>
			<if test="logisticsComments != null" >
				LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
			</if>
			<if test="financeComments != null" >
				FINANCE_COMMENTS = #{financeComments,jdbcType=VARCHAR},
			</if>
			<if test="comments != null" >
				COMMENTS = #{comments,jdbcType=VARCHAR},
			</if>
			<if test="invoiceComments != null" >
				INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
			</if>
			<if test="deliveryDirect != null" >
				DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BIT},
			</if>
			<if test="supplierClause != null" >
				SUPPLIER_CLAUSE = #{supplierClause,jdbcType=VARCHAR},
			</if>
			<if test="haveAdvancePurchase != null" >
				HAVE_ADVANCE_PURCHASE = #{haveAdvancePurchase,jdbcType=BIT},
			</if>
			<if test="isUrgent != null" >
				IS_URGENT = #{isUrgent,jdbcType=BIT},
			</if>
			<if test="urgentAmount != null" >
				URGENT_AMOUNT = #{urgentAmount,jdbcType=DECIMAL},
			</if>
			<if test="haveCommunicate != null" >
				HAVE_COMMUNICATE = #{haveCommunicate,jdbcType=BIT},
			</if>
			<if test="prepareComments != null" >
				PREPARE_COMMENTS = #{prepareComments,jdbcType=VARCHAR},
			</if>
			<if test="marketingPlan != null" >
				MARKETING_PLAN = #{marketingPlan,jdbcType=VARCHAR},
			</if>
			<if test="syncStatus != null" >
				SYNC_STATUS = #{syncStatus,jdbcType=BIT},
			</if>
			<if test="satisfyInvoiceTime != null" >
				SATISFY_INVOICE_TIME = #{satisfyInvoiceTime,jdbcType=BIGINT},
			</if>
			<if test="satisfyDeliveryTime != null" >
				SATISFY_DELIVERY_TIME = #{satisfyDeliveryTime,jdbcType=BIGINT},
			</if>
			<if test="addTime != null" >
				ADD_TIME = #{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null" >
				CREATOR = #{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null" >
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null" >
				UPDATER = #{updater,jdbcType=INTEGER},
			</if>
			<if test="traderArea != null" >
				TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderArea != null" >
				TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderArea != null" >
				INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
			</if>
			<if test="advancePurchaseStatus != null" >
				ADVANCE_PURCHASE_STATUS = #{advancePurchaseStatus,jdbcType=INTEGER},
			</if>
			<if test="advancePurchaseComments != null" >
				ADVANCE_PURCHASE_COMMENTS = #{advancePurchaseComments,jdbcType=VARCHAR},
			</if>
			<if test="advancePurchaseTime != null" >
				ADVANCE_PURCHASE_TIME = #{advancePurchaseTime,jdbcType=BIGINT},
			</if>
			<if test="validUserId != null" >
				VALID_USER_ID = #{validUserId,jdbcType=INTEGER},
			</if>
			<if test="validOrgId != null" >
				VALID_ORG_ID = #{validOrgId,jdbcType=INTEGER},
			</if>
			<if test="validOrgName != null" >
				VALID_ORG_NAME = #{validOrgName,jdbcType=VARCHAR},
			</if>
			<if test="isSalesPerformance != null" >
				IS_SALES_PERFORMANCE = #{isSalesPerformance,jdbcType=INTEGER},
			</if>
			<if test="salesPerformanceTime != null" >
				SALES_PERFORMANCE_TIME = #{salesPerformanceTime,jdbcType=BIGINT},
			</if>
			<if test="salesPerformanceModTime != null" >
				SALES_PERFORMANCE_MOD_TIME = #{salesPerformanceModTime,jdbcType=BIGINT},
			</if>
			<if test="isDelayInvoice != null" >
				IS_DELAY_INVOICE = #{isDelayInvoice,jdbcType=BIT},
			</if>
			<if test="lockedReason != null" >
				LOCKED_REASON = #{lockedReason,jdbcType=VARCHAR},
			</if>
			<if test="costUserIds != null" >
				COST_USER_IDS = #{costUserIds,jdbcType=VARCHAR},
			</if>
			<if test="invoiceEmail != null">
				INVOICE_EMAIL = #{invoiceEmail,jdbcType=VARCHAR},
			</if>
			<if test="ownerUserId != null">
				OWNER_USER_ID = #{ownerUserId,jdbcType=INTEGER},
			</if>
			<if test="traderAreaId != null">
				TRADER_AREA_ID = #{traderAreaId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderAreaId != null">
				TAKE_TRADER_AREA_ID = #{takeTraderAreaId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderAreaId != null">
				INVOICE_TRADER_AREA_ID = #{invoiceTraderAreaId, jdbcType=INTEGER},
			</if>
			<if test="logisticsApiSync != null" >
				LOGISTICS_API_SYNC = #{logisticsApiSync,jdbcType=BIT},
			</if>
			<if test="logisticsWxsendSync != null" >
				LOGISTICS_WXSEND_SYNC = #{logisticsWxsendSync,jdbcType=BIT},
			</if>
			<if test="closeComments != null">
				CLOSE_COMMENTS = #{closeComments,jdbcType=VARCHAR},
			</if>
			<if test="bdMobileTime != null">
				BD_MOBILE_TIME = #{bdMobileTime,jdbcType=VARCHAR},
			</if>
			<if test="contractUrl != null">
				CONTRACT_URL = #{contractUrl,jdbcType=VARCHAR},
			</if>
			<if test="autoAudit != null">
				AUTO_AUDIT = #{autoAudit, jdbcType=INTEGER},
			</if>
			<if test="isRisk != null">
				IS_RISK = #{isRisk,jdbcType=INTEGER},
			</if>
			<if test="riskComments != null">
				RISK_COMMENTS = #{riskComments,jdbcType=VARCHAR},
			</if>
			<if test="riskTime != null">
				RISK_TIME = #{riskTime,jdbcType=BIGINT},
			</if>
			<if test="isPrintout !=null">
				IS_PRINTOUT =#{isPrintout,jdbcType=BIT},
			</if>
			<if test="outIsFlag!=null">
				OUT_IS_FLAG=#{outIsFlag,jdbcType=INTEGER},
			</if>
			<if test="retentionMoney!=null">
				RETENTION_MONEY=#{retentionMoney,jdbcType=DECIMAL},
			</if>
			<if test="retentionMoneyDay!=null">
				RETENTION_MONEY_DAY=#{retentionMoneyDay,jdbcType=INTEGER},
			</if>
			<if test="isSameAddress!=null">
				IS_SAME_ADDRESS=#{isSameAddress,jdbcType=TINYINT},
			</if>
			<if test="invoiceSendNode!=null">
				INVOICE_SEND_NODE=#{invoiceSendNode,jdbcType=TINYINT},
			</if>
			<if test="groupCustomerId != null">
				GROUP_CUSTOMER_ID = #{groupCustomerId, jdbcType=INTEGER},
			</if>
			<if test="createMobile != null">
				CREATE_MOBILE = #{createMobile, jdbcType=VARCHAR},
			</if>
			<if test="deliveryClaim != null">
				DELIVERY_CLAIM = #{deliveryClaim, jdbcType=INTEGER},
			</if>
			<if test="deliveryDelayTime != null">
				DELIVERY_DELAY_TIME = #{deliveryDelayTime, jdbcType=BIGINT},
			</if>
			<if test="billPeriodSettlementType != null">
				BILL_PERIOD_SETTLEMENT_TYPE = #{billPeriodSettlementType, jdbcType=INTEGER},
			</if>
			<if test="isNew != null">
				IS_NEW = #{isNew, jdbcType=TINYINT},
			</if>
			<if test="periodDay != null" >
				PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
			</if>
			<if test="confirmTime != null">
				CONFIRM_TIME = #{confirmTime},
			</if>

			<if test="confirmationFormUpload != null" >
				CONFIRMATION_FORM_UPLOAD = #{confirmationFormUpload, jdbcType=INTEGER},
			</if>
			<if test="confirmationFormAudit != null">
				CONFIRMATION_FORM_AUDIT = #{confirmationFormAudit, jdbcType=INTEGER},
			</if>
			<if test="confirmationSubmitTime != null">
				CONFIRMATION_SUBMIT_TIME = #{confirmationSubmitTime, jdbcType=BIGINT},
			</if>
			<if test="prepareReaseonType != null" >
				PREPARE_REASEON_TYPE = #{prepareReaseonType},
			</if>
			<if test="terminalTraderNature != null" >
				TERMINAL_TRADER_NATURE = #{terminalTraderNature, jdbcType=INTEGER},
			</if>
			<if test="orderTestContent != null" >
				ORDER_TEST_CONTENT = #{orderTestContent, jdbcType=VARCHAR},
			</if>
		</set>
		where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</update>

	<select id="getSaleOrderById" resultMap="BaseResultMap">
	SELECT * FROM `T_SALEORDER`  WHERE `SALEORDER_ID`=#{saleorderId}
	</select>
	 <select id="getSaleorderBySaleorderNo" resultMap="BaseResultMap" parameterType="com.vedeng.order.model.Saleorder">
  	select a.*,b.COMMENTS AS taxRate
    from T_SALEORDER a
    left join T_SYS_OPTION_DEFINITION b on a.INVOICE_TYPE = b.SYS_OPTION_DEFINITION_ID
    where a.SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
  </select>
	<select id="getSaleorderBySaleorderNo2" resultMap="BaseResultMap" parameterType="com.vedeng.order.model.Saleorder">
		select a.*
		from T_SALEORDER a
		where a.SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
	</select>
  <select id="getSaleorderListByStatus" resultMap="BaseResultMap">
  	SELECT <include refid="Base_Column_List"/> FROM T_SALEORDER  WHERE `STATUS`=#{status,jdbcType=INTEGER} AND `ORDER_TYPE`=#{orderType,jdbcType=INTEGER}
  </select>
	<select id="getSaleorderidByStatusLimit" resultType="java.lang.Integer">
		SELECT
		a.SALEORDER_ID
		FROM
		T_SALEORDER a
		WHERE
		a.`STATUS` != 3
		AND a.ORDER_TYPE != 2
		AND a.UPDATE_DATA_TIME > DATE_SUB(
		CURDATE(),
		INTERVAL #{day} DAY)
		ORDER BY
		a.SALEORDER_ID DESC
		LIMIT #{limit},1000
	</select>
    <select id="getActionOrderLimit" resultType="java.lang.Integer">
		SELECT
		a.SALEORDER_ID
		FROM
		T_SALEORDER a
		WHERE
		a.ACTION_ID > 0
		AND a.`STATUS` != 3
		AND a.ORDER_TYPE != 2
		AND a.UPDATE_DATA_TIME > DATE_SUB(
		CURDATE(),
		INTERVAL  #{day} DAY)
		ORDER BY
		a.SALEORDER_ID DESC
		LIMIT #{limit},1000
    </select>
	<select id="getSaleorderidByStatus" resultType="java.lang.Integer" >
	SELECT
	count(*)
	FROM
	T_SALEORDER a
	WHERE
	a.`STATUS` != 3
	AND a.ORDER_TYPE !=2
	AND a.UPDATE_DATA_TIME > DATE_SUB(
		CURDATE(),
	INTERVAL #{day} DAY)
	</select>
	<select id="getSaleorderidAll" resultType="java.lang.Integer" >
	select count(*) from T_SALEORDER a where a.ORDER_TYPE !=2
	</select>
	<select id="getSaleorderidAllLimit" resultType="java.lang.Integer">
	select a.SALEORDER_ID from T_SALEORDER a where a.ORDER_TYPE !=2   ORDER BY a.SALEORDER_ID desc limit  #{orgId},1000
	</select>
	<select id="selectLatestSaleOrderByMobile" resultMap="BaseResultMap">
	select a.TRADER_ID,a.saleorder_id from T_SALEORDER a where a.ORDER_TYPE =0 and TRADER_CONTACT_MOBILE= #{mobile}
	 and valid_time &gt; 1592236800000
	 ORDER BY a.VALID_TIME desc
	limit 30
	</select>

	<select id="selectSaleorderNo" resultMap="BaseResultMap">
		SELECT SALEORDER_NO,SALEORDER_ID FROM T_SALEORDER
		WHERE 1=1
		and `STATUS` IN (0,1,4)
		and ORDER_TYPE=1
		<if test="saleorderId!=null">
			AND SALEORDER_ID=#{saleorderId}
		</if>
		<if test="createMobile!=null and createMobile!=''">
			AND  CREATE_MOBILE=#{createMobile}
		</if>
		<if test="createMobileList!=null">
			AND CREATE_MOBILE IN <foreach collection="createMobileList" item="createMo" index="index" open="(" separator="," close=")">
			#{createMo}
		</foreach>
		</if>
	</select>

	<!-- add by Tomcat.hui transplanted from project:dbcenter 20190816 -->
	<select id="getGoodsOccupyNum" resultType="java.lang.Integer">
		select
		COALESCE(SUM(if(b.NUM-b.DELIVERY_NUM-IFNULL(c.thNum,0)>0,b.NUM-b.DELIVERY_NUM-IFNULL(c.thNum,0),0) ),0)
		from
		T_SALEORDER a
		left join
		T_SALEORDER_GOODS b
		on
		a.SALEORDER_ID = b.SALEORDER_ID
		left join
		(
		select
		COALESCE(SUM(aa.NUM),0) as thNum,
		aa.ORDER_DETAIL_ID
		from
		T_AFTER_SALES_GOODS aa
		left join
		T_AFTER_SALES bb
		on
		aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
		where
		bb.ATFER_SALES_STATUS in (1,2)
		AND
		bb.SUBJECT_TYPE = 535
		AND
		bb.TYPE = 539
		AND aa.GOODS_ID = #{goodsId,jdbcType=INTEGER}
		GROUP BY
		aa.ORDER_DETAIL_ID
		) as c on b.SALEORDER_GOODS_ID = c.ORDER_DETAIL_ID
		where
		a.STATUS = 1
		and
		a.DELIVERY_STATUS != 2
		and
		b.DELIVERY_DIRECT = 0
		and
		b.IS_DELETE = 0
		and
		a.ORDER_TYPE in (0,3)
		and
		b.GOODS_ID = #{goodsId,jdbcType=INTEGER}
		and
		a.SATISFY_DELIVERY_TIME>0
	</select>

	<!-- add by Tomcat.hui transplanted from project:dbcenter 20190816 -->
	<select id="getGoodsOccupyNumList" resultMap="goodsDataMap">
		select
		COALESCE(SUM(if(b.NUM-b.DELIVERY_NUM-IFNULL(c.thNum,0)>0,b.NUM-b.DELIVERY_NUM-IFNULL(c.thNum,0),0) ),0) as
		occupyNum,b.GOODS_ID
		from
		T_SALEORDER a
		left join
		T_SALEORDER_GOODS b
		on
		a.SALEORDER_ID = b.SALEORDER_ID
		left join
		(
		select
		COALESCE(SUM(aa.NUM),0) as thNum,
		aa.ORDER_DETAIL_ID
		from
		T_AFTER_SALES_GOODS aa
		left join
		T_AFTER_SALES bb
		on
		aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
		where
		bb.ATFER_SALES_STATUS in (1,2)
		AND
		bb.SUBJECT_TYPE = 535
		AND
		bb.TYPE = 539
		<if test="goodsIds != null">
			and aa.GOODS_ID in
			<foreach collection="goodsIds" item="goodsId" index="index"
					 open="(" close=")" separator=",">
				#{goodsId}
			</foreach>
		</if>
		GROUP BY
		aa.ORDER_DETAIL_ID
		) as c on b.SALEORDER_GOODS_ID = c.ORDER_DETAIL_ID
		where
		a.STATUS = 1
		and
		a.DELIVERY_STATUS != 2
		and
		b.DELIVERY_DIRECT = 0
		and
		b.IS_DELETE = 0
		and
		a.ORDER_TYPE in (0,3)
		<if test="goodsIds != null">
			and b.GOODS_ID in
			<foreach collection="goodsIds" item="goodsId" index="index"
					 open="(" close=")" separator=",">
				#{goodsId}
			</foreach>
		</if>
		and
		a.SATISFY_DELIVERY_TIME>0
		group by b.GOODS_ID
	</select>

	<select id="isExistQuoteorderId" resultType="int">
     SELECT COUNT(1) FROM T_SALEORDER
      WHERE QUOTEORDER_ID=#{quoteorderId}

	</select>


	<!-- add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 取LackAccountPeriodAmount. start -->
	<select id="getSaleorderLackAccountPeriodAmount" parameterType="java.lang.Integer"
			resultType="java.math.BigDecimal">
  	select
  		COALESCE(sum(ABS(a.AMOUNT))-IFNULL(c.hk_amount,0)-ifnull(d.hk_amount,0),0)
  	from
  		T_CAPITAL_BILL a
  	left join
		T_CAPITAL_BILL_DETAIL b
	on
		a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
  	left join
  		(
  			select
  				COALESCE(sum(ABS(b1.AMOUNT)),0) as hk_amount,b1.RELATED_ID
  			from
		  		T_CAPITAL_BILL a1
		  	left join
				T_CAPITAL_BILL_DETAIL b1
			on
				a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
			where
				a1.TRADER_TYPE in (1,4)
			and
  				b1.ORDER_TYPE = 1
			and
				b1.BUSSINESS_TYPE = 533
			group by b1.RELATED_ID
  		) as c
  	on
  		b.RELATED_ID = c.RELATED_ID
  	left join
  		(
  			select
				COALESCE(sum(ABS(b1.AMOUNT)),0) as hk_amount,c1.ORDER_ID
			from
				T_CAPITAL_BILL a1
			left join
				T_CAPITAL_BILL_DETAIL b1
			on
				a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
			left JOIN
				T_AFTER_SALES c1
			ON
				c1.AFTER_SALES_ID = b1.RELATED_ID and c1.SUBJECT_TYPE = 535
			where
				a1.TRADER_TYPE = 3
			and
				b1.ORDER_TYPE = 3
			and
				b1.BUSSINESS_TYPE = 533
			group by c1.ORDER_ID
  		) as d
  	on
  		d.ORDER_ID = b.RELATED_ID
  	where
  		a.TRADER_TYPE = 3
  	and
  		a.TRADER_MODE = 527
  	and
  		b.ORDER_TYPE = 1
  	and
  		b.RELATED_ID = #{saleorderId,jdbcType=INTEGER}
  </select>
	<!-- add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 取LackAccountPeriodAmount. end -->

	<!-- add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 取Amount. start -->
	<select id="getPeriodAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
  	select
  		COALESCE(sum(ABS(a.AMOUNT))-IFNULL(abs(c.tk_amount),0),0)
  	from
  		T_CAPITAL_BILL a
  	left join
		T_CAPITAL_BILL_DETAIL b
	on
		a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
	left join
		(
			SELECT
				sum(aa.AMOUNT) as tk_amount,cc.ORDER_ID
			FROM
				T_CAPITAL_BILL aa
			left JOIN
				T_CAPITAL_BILL_DETAIL bb
			ON
				aa.CAPITAL_BILL_ID = bb.CAPITAL_BILL_ID
			left JOIN
				T_AFTER_SALES cc
			ON
				cc.AFTER_SALES_ID = bb.RELATED_ID
			AND
				bb.ORDER_TYPE = 3
			WHERE
				aa.TRADER_MODE = 529
			AND
				aa.TRADER_TYPE = 3
			AND
				cc.SUBJECT_TYPE = 535
			GROUP BY
				cc.ORDER_ID
		) as c
		ON
		c.ORDER_ID = b.RELATED_ID
  	where
  		a.TRADER_TYPE = 3
  	and
  		a.TRADER_MODE = 527
  	and
  		b.ORDER_TYPE = 1
  	and
  		b.RELATED_ID = #{saleorderId,jdbcType=INTEGER}
  </select>
	<!--根据订单id查询订单号和用户id-->
	<select id="getWebAccountId" resultMap="BaseResultMap">
           SELECT
        ORDER_TYPE,
        SALEORDER_NO,
        SALEORDER_ID,
				CASE
				WHEN ORDER_TYPE=0
				THEN K.WEB_ACCOUNT_ID
				ELSE
        w.WEB_ACCOUNT_ID
				END AS WEB_ACCOUNT_ID,
				CASE
				WHEN ORDER_TYPE=0
				THEN K.SSO_ACCOUNT_ID
				ELSE

        w.SSO_ACCOUNT_ID
				END AS SSO_ACCOUNT_ID,t.SEND_TO_PC
    FROM
        T_SALEORDER t
    LEFT  JOIN
        T_WEB_ACCOUNT w
            ON t.CREATE_MOBILE=w.MOBILE
            <!--如果是VS订单，则需要根据联系人手机号来匹配-->
	 left join T_WEB_ACCOUNT K ON t.TRADER_CONTACT_MOBILE=K.MOBILE
    WHERE
         t.SALEORDER_ID=#{SaleOrderId}
	</select>

	<!-- add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 取PeriodAmount. end -->
	<select id="getSaleorderBySaleorderNoList" resultType="com.vedeng.order.model.Saleorder" >
		SELECT  a.* ,e.`STATUS` AS VERIFY_STATUS ,u.USERNAME userName,DATE_FORMAT(FROM_UNIXTIME(a.`ADD_TIME`/1000, '%Y-%m-%d %H:%i:%S'),"%Y-%m-%d %H:%i:%S")  addTimeStr
		FROM T_SALEORDER a
		LEFT JOIN T_VERIFIES_INFO e ON a.SALEORDER_ID = e.RELATE_TABLE_KEY
		AND e.RELATE_TABLE = 'T_SALEORDER' AND e.VERIFIES_TYPE = 623
		LEFT JOIN T_R_TRADER_J_USER f ON a.TRADER_ID = f.TRADER_ID
		LEFT JOIN T_USER u
		ON f.USER_ID=u.USER_ID
		WHERE a.SALEORDER_NO IN
		<foreach collection="list" item="saleorderNo" open="(" close=")" separator=",">
			#{saleorderNo, jdbcType=VARCHAR}
		</foreach>
	</select>

	<update id="updateDeliveryStatusBySaleorderNo" parameterType="com.vedeng.order.model.Saleorder">
		update T_SALEORDER
		<set >
			<if test="arrivalStatus  != null" >
				ARRIVAL_STATUS = #{arrivalStatus ,jdbcType=INTEGER},
			</if>
			<if test="deliveryStatus  != null" >
				DELIVERY_STATUS = #{deliveryStatus ,jdbcType=INTEGER},
			</if>
			<if test="modTime != null" >
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="webTakeDeliveryTime != null" >
				WEB_TAKE_DELIVERY_TIME = #{webTakeDeliveryTime,jdbcType=BIGINT},
			</if>
			<if test="deliveryDirect != null" >
				DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BIT}
			</if>
		</set>
		where SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
	</update>
	<update id="updateSaleorderStatusById">
		UPDATE T_SALEORDER SET STATUS = 3, UPDATER = #{userId,jdbcType=INTEGER}, MOD_TIME = #{modTime,jdbcType=BIGINT} WHERE SALEORDER_ID IN
		<foreach collection="idList" index="index" item="item" separator="," open="(" close=")">
			#{item,jdbcType=INTEGER}
		</foreach>
	</update>


	<select id="getSaleorderCountByTime" parameterType="com.vedeng.order.model.SaleorderCountParam" resultType="java.lang.Integer">
		SELECT COALESCE(COUNT(1),0) FROM T_SALEORDER
		WHERE
		 TRADER_ID=#{traderId}
		 AND STATUS != 3
		<if test="type==0">
			AND VALID_STATUS=1
		</if>
		<if test="beginTime !=null">
			AND ADD_TIME <![CDATA[ >= ]]> #{beginTime}
		</if>
		<if test="endTime != null">
			AND ADD_TIME <![CDATA[ <= ]]> #{endTime}
		</if>
	</select>


	<select id="getOrderGoodsSkuByTraderId" parameterType="com.vedeng.trader.model.TraderOrderGoods" resultType="java.lang.String">
		SELECT distinct g.sku FROM T_SALEORDER_GOODS g LEFT JOIN T_SALEORDER o on o.SALEORDER_ID=g.SALEORDER_ID
		WHERE o.TRADER_ID=#{traderId}
		AND g.IS_DELETE = 0
	    AND g.IS_IGNORE = 0
	</select>


	<select id="getDaysCountSum" parameterType="com.vedeng.order.model.SaleorderCountParam" resultType="com.vedeng.order.model.SaleorderCountResult">
		SELECT COUNT(1) as orderDaysCount,COALESCE (SUM(b.totalAmount),0) as orderDaysMoneySum from (
		SELECT a.addTime,SUM(TOTAL_AMOUNT) as totalAmount FROM (
		SELECT FROM_UNIXTIME(ADD_TIME/1000,'%Y-%m-%d') as addTime,TOTAL_AMOUNT FROM T_SALEORDER
		WHERE
		TRADER_ID=#{traderId}
		AND ORDER_TYPE = 5
		<if test="beginTime !=null">
			AND ADD_TIME <![CDATA[ >= ]]> #{beginTime}
		</if>
		<if test="endTime != null">
			AND ADD_TIME <![CDATA[ <= ]]> #{endTime}
		</if>) a
		GROUP BY a.addTime
		) b
	</select>
	<select id="getSaleorderBySaleorderGoodsId" resultMap="BaseResultMap">
		SELECT
			B.*
			FROM
				T_SALEORDER_GOODS A
				LEFT JOIN T_SALEORDER B ON A.SALEORDER_ID = B.SALEORDER_ID
			WHERE
				A.SALEORDER_GOODS_ID=#{saleorderGoodsId}
	</select>
	<update id="updateOrderStatusByOrderNo" parameterType="com.vedeng.order.model.Saleorder">
		update T_SALEORDER
		<set >
			<if test="status != null" >
				STATUS = #{status,jdbcType=INTEGER},
			</if>
			<if test="updater != null" >
				UPDATER = #{updater,jdbcType=INTEGER},
			</if>
			<if test="modTime != null" >
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>

			<if test="arrivalStatus != null" >
				ARRIVAL_STATUS = #{arrivalStatus,jdbcType=INTEGER},
			</if>

			<if test="deliveryStatus != null" >
				DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER},
			</if>
		</set>
		where SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
	</update>


	<insert id="insertSelective" parameterType="com.vedeng.order.model.Saleorder" useGeneratedKeys="true" keyProperty="saleorderId">
		insert into T_SALEORDER
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="saleorderId != null">
				SALEORDER_ID,
			</if>
			<if test="quoteorderId != null">
				QUOTEORDER_ID,
			</if>
			<if test="parentId != null">
				PARENT_ID,
			</if>
			<if test="saleorderNo != null">
				SALEORDER_NO,
			</if>
			<if test="orderType != null">
				ORDER_TYPE,
			</if>
			<if test="companyId != null">
				COMPANY_ID,
			</if>
			<if test="source != null">
				SOURCE,
			</if>
			<if test="creatorOrgId != null">
				CREATOR_ORG_ID,
			</if>
			<if test="creatorOrgName != null">
				CREATOR_ORG_NAME,
			</if>
			<if test="orgId != null">
				ORG_ID,
			</if>
			<if test="orgName != null">
				ORG_NAME,
			</if>
			<if test="userId != null">
				USER_ID,
			</if>
			<if test="validOrgId != null">
				VALID_ORG_ID,
			</if>
			<if test="validOrgName != null">
				VALID_ORG_NAME,
			</if>
			<if test="validUserId != null">
				VALID_USER_ID,
			</if>
			<if test="validStatus != null">
				VALID_STATUS,
			</if>
			<if test="validTime != null">
				VALID_TIME,
			</if>
			<if test="endTime != null">
				END_TIME,
			</if>
			<if test="status != null">
				STATUS,
			</if>
			<if test="purchaseStatus != null">
				PURCHASE_STATUS,
			</if>
			<if test="lockedStatus != null">
				LOCKED_STATUS,
			</if>
			<if test="invoiceStatus != null">
				INVOICE_STATUS,
			</if>
			<if test="invoiceTime != null">
				INVOICE_TIME,
			</if>
			<if test="paymentStatus != null">
				PAYMENT_STATUS,
			</if>
			<if test="paymentTime != null">
				PAYMENT_TIME,
			</if>
			<if test="deliveryStatus != null">
				DELIVERY_STATUS,
			</if>
			<if test="deliveryTime != null">
				DELIVERY_TIME,
			</if>
			<if test="isCustomerArrival != null">
				IS_CUSTOMER_ARRIVAL,
			</if>
			<if test="arrivalStatus != null">
				ARRIVAL_STATUS,
			</if>
			<if test="arrivalTime != null">
				ARRIVAL_TIME,
			</if>
			<if test="serviceStatus != null">
				SERVICE_STATUS,
			</if>
			<if test="haveAccountPeriod != null">
				HAVE_ACCOUNT_PERIOD,
			</if>
			<if test="isPayment != null">
				IS_PAYMENT,
			</if>
			<if test="totalAmount != null">
				TOTAL_AMOUNT,
			</if>
			<if test="traderId != null">
				TRADER_ID,
			</if>
			<if test="customerType != null">
				CUSTOMER_TYPE,
			</if>
			<if test="customerNature != null">
				CUSTOMER_NATURE,
			</if>
			<if test="traderName != null">
				TRADER_NAME,
			</if>
			<if test="traderContactId != null">
				TRADER_CONTACT_ID,
			</if>
			<if test="traderContactName != null">
				TRADER_CONTACT_NAME,
			</if>
			<if test="traderContactMobile != null">
				TRADER_CONTACT_MOBILE,
			</if>
			<if test="traderContactTelephone != null">
				TRADER_CONTACT_TELEPHONE,
			</if>
			<if test="traderAddressId != null">
				TRADER_ADDRESS_ID,
			</if>
			<if test="traderArea != null">
				TRADER_AREA,
			</if>
			<if test="traderAddress != null">
				TRADER_ADDRESS,
			</if>
			<if test="traderComments != null">
				TRADER_COMMENTS,
			</if>
			<if test="takeTraderId != null">
				TAKE_TRADER_ID,
			</if>
			<if test="takeTraderName != null">
				TAKE_TRADER_NAME,
			</if>
			<if test="takeTraderContactId != null">
				TAKE_TRADER_CONTACT_ID,
			</if>
			<if test="takeTraderContactName != null">
				TAKE_TRADER_CONTACT_NAME,
			</if>
			<if test="takeTraderContactMobile != null">
				TAKE_TRADER_CONTACT_MOBILE,
			</if>
			<if test="takeTraderContactTelephone != null">
				TAKE_TRADER_CONTACT_TELEPHONE,
			</if>
			<if test="takeTraderAddressId != null">
				TAKE_TRADER_ADDRESS_ID,
			</if>
			<if test="takeTraderArea != null">
				TAKE_TRADER_AREA,
			</if>
			<if test="takeTraderAddress != null">
				TAKE_TRADER_ADDRESS,
			</if>
			<if test="isSendInvoice != null">
				IS_SEND_INVOICE,
			</if>
			<if test="invoiceTraderId != null">
				INVOICE_TRADER_ID,
			</if>
			<if test="invoiceTraderName != null">
				INVOICE_TRADER_NAME,
			</if>
			<if test="invoiceTraderContactId != null">
				INVOICE_TRADER_CONTACT_ID,
			</if>
			<if test="invoiceTraderContactName != null">
				INVOICE_TRADER_CONTACT_NAME,
			</if>
			<if test="invoiceTraderContactMobile != null">
				INVOICE_TRADER_CONTACT_MOBILE,
			</if>
			<if test="invoiceTraderContactTelephone != null">
				INVOICE_TRADER_CONTACT_TELEPHONE,
			</if>
			<if test="invoiceTraderAddressId != null">
				INVOICE_TRADER_ADDRESS_ID,
			</if>
			<if test="invoiceTraderArea != null">
				INVOICE_TRADER_AREA,
			</if>
			<if test="invoiceTraderAddress != null">
				INVOICE_TRADER_ADDRESS,
			</if>
			<if test="salesAreaId != null">
				SALES_AREA_ID,
			</if>
			<if test="salesArea != null">
				SALES_AREA,
			</if>
			<if test="terminalTraderId != null">
				TERMINAL_TRADER_ID,
			</if>
			<if test="terminalTraderName != null">
				TERMINAL_TRADER_NAME,
			</if>
			<if test="terminalTraderType != null">
				TERMINAL_TRADER_TYPE,
			</if>
			<if test="invoiceType != null">
				INVOICE_TYPE,
			</if>
			<if test="freightDescription != null">
				FREIGHT_DESCRIPTION,
			</if>
			<if test="deliveryType != null">
				DELIVERY_TYPE,
			</if>
			<if test="logisticsId != null">
				LOGISTICS_ID,
			</if>
			<if test="paymentType != null">
				PAYMENT_TYPE,
			</if>
			<if test="prepaidAmount != null">
				PREPAID_AMOUNT,
			</if>
			<if test="accountPeriodAmount != null">
				ACCOUNT_PERIOD_AMOUNT,
			</if>
			<if test="periodDay != null">
				PERIOD_DAY,
			</if>
			<if test="logisticsCollection != null">
				LOGISTICS_COLLECTION,
			</if>
			<if test="retainageAmount != null">
				RETAINAGE_AMOUNT,
			</if>
			<if test="retainageAmountMonth != null">
				RETAINAGE_AMOUNT_MONTH,
			</if>
			<if test="paymentComments != null">
				PAYMENT_COMMENTS,
			</if>
			<if test="additionalClause != null">
				ADDITIONAL_CLAUSE,
			</if>
			<if test="logisticsComments != null">
				LOGISTICS_COMMENTS,
			</if>
			<if test="financeComments != null">
				FINANCE_COMMENTS,
			</if>
			<if test="comments != null">
				COMMENTS,
			</if>
			<if test="invoiceComments != null">
				INVOICE_COMMENTS,
			</if>
			<if test="deliveryDirect != null">
				DELIVERY_DIRECT,
			</if>
			<if test="supplierClause != null">
				SUPPLIER_CLAUSE,
			</if>
			<if test="haveAdvancePurchase != null">
				HAVE_ADVANCE_PURCHASE,
			</if>
			<if test="advancePurchaseStatus != null">
				ADVANCE_PURCHASE_STATUS,
			</if>
			<if test="advancePurchaseComments != null">
				ADVANCE_PURCHASE_COMMENTS,
			</if>
			<if test="advancePurchaseTime != null">
				ADVANCE_PURCHASE_TIME,
			</if>
			<if test="isUrgent != null">
				IS_URGENT,
			</if>
			<if test="urgentAmount != null">
				URGENT_AMOUNT,
			</if>
			<if test="haveCommunicate != null">
				HAVE_COMMUNICATE,
			</if>
			<if test="prepareComments != null">
				PREPARE_COMMENTS,
			</if>
			<if test="marketingPlan != null">
				MARKETING_PLAN,
			</if>
			<if test="statusComments != null">
				STATUS_COMMENTS,
			</if>
			<if test="syncStatus != null">
				SYNC_STATUS,
			</if>
			<if test="satisfyInvoiceTime != null">
				SATISFY_INVOICE_TIME,
			</if>
			<if test="satisfyDeliveryTime != null">
				SATISFY_DELIVERY_TIME,
			</if>
			<if test="isSalesPerformance != null">
				IS_SALES_PERFORMANCE,
			</if>
			<if test="salesPerformanceTime != null">
				SALES_PERFORMANCE_TIME,
			</if>
			<if test="salesPerformanceModTime != null">
				SALES_PERFORMANCE_MOD_TIME,
			</if>
			<if test="isDelayInvoice != null">
				IS_DELAY_INVOICE,
			</if>
			<if test="invoiceMethod != null">
				INVOICE_METHOD,
			</if>
			<if test="lockedReason != null">
				LOCKED_REASON,
			</if>
			<if test="costUserIds != null">
				COST_USER_IDS,
			</if>
			<if test="ownerUserId != null">
				OWNER_USER_ID,
			</if>
			<if test="invoiceEmail != null">
				INVOICE_EMAIL,
			</if>
			<if test="addTime != null">
				ADD_TIME,
			</if>
			<if test="creator != null">
				CREATOR,
			</if>
			<if test="modTime != null">
				MOD_TIME,
			</if>
			<if test="updater != null">
				UPDATER,
			</if>
			<if test="traderAreaId != null">
				TRADER_AREA_ID,
			</if>
			<if test="takeTraderAreaId != null">
				TAKE_TRADER_AREA_ID,
			</if>
			<if test="invoiceTraderAreaId != null">
				INVOICE_TRADER_AREA_ID,
			</if>

			<if test="couponMoney!=null">
				COUPONMONEY,
			</if>
			<if test="isCoupons!=null">
				IS_COUPONS,
			</if>
			<if test="actionId!=null">
				ACTION_ID,
			</if>
			<if test="originalAmount!=null">
				ORIGINAL_AMOUNT,
			</if>
			<if test="autoAudit!=null">
				AUTO_AUDIT,
			</if>
			<if test="isSameAddress!=null">
				IS_SAME_ADDRESS,
			</if>
			<if test="bdtraderComments != null and bdtraderComments != ''">
				BDTRADER_COMMENTS,
			</if>
			<if test="groupCustomerId != null">
				GROUP_CUSTOMER_ID,
			</if>
			<if test="createMobile != null">
				CREATE_MOBILE,
			</if>
			<if test="isPrintout != null">
				IS_PRINTOUT,
			</if>
			<if test="invoiceSendNode != null" >
				INVOICE_SEND_NODE,
			</if>
			<if test="reasonNo != null" >
				REASON_NO,
			</if>
			<if test="billPeriodSettlementType != null">
				BILL_PERIOD_SETTLEMENT_TYPE,
			</if>
			<if test="deliveryClaim != null">
				DELIVERY_CLAIM,
			</if>
			<if test="deliveryDelayTime != null">
				DELIVERY_DELAY_TIME,
			</if>

			<if test="isNew != null">
				IS_NEW,
			</if>

			<if test="confirmTime != null">
				CONFIRM_TIME,
			</if>

			<if test="confirmStatus != null" >
			CONFIRM_STATUS,
			</if>
			<if test="prepareReaseonType != null" >
				PREPARE_REASEON_TYPE,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="saleorderId != null">
				#{saleorderId,jdbcType=INTEGER},
			</if>
			<if test="quoteorderId != null">
				#{quoteorderId,jdbcType=INTEGER},
			</if>
			<if test="parentId != null">
				#{parentId,jdbcType=INTEGER},
			</if>
			<if test="saleorderNo != null">
				#{saleorderNo,jdbcType=VARCHAR},
			</if>
			<if test="orderType != null">
				#{orderType,jdbcType=BIT},
			</if>
			<if test="companyId != null">
				#{companyId,jdbcType=INTEGER},
			</if>
			<if test="source != null">
				#{source,jdbcType=BIT},
			</if>
			<if test="creatorOrgId != null">
				#{creatorOrgId,jdbcType=INTEGER},
			</if>
			<if test="creatorOrgName != null">
				#{creatorOrgName,jdbcType=VARCHAR},
			</if>
			<if test="orgId != null">
				#{orgId,jdbcType=INTEGER},
			</if>
			<if test="orgName != null">
				#{orgName,jdbcType=VARCHAR},
			</if>
			<if test="userId != null">
				#{userId,jdbcType=INTEGER},
			</if>
			<if test="validOrgId != null">
				#{validOrgId,jdbcType=INTEGER},
			</if>
			<if test="validOrgName != null">
				#{validOrgName,jdbcType=VARCHAR},
			</if>
			<if test="validUserId != null">
				#{validUserId,jdbcType=INTEGER},
			</if>
			<if test="validStatus != null">
				#{validStatus,jdbcType=BIT},
			</if>
			<if test="validTime != null">
				#{validTime,jdbcType=BIGINT},
			</if>
			<if test="endTime != null">
				#{endTime,jdbcType=BIGINT},
			</if>
			<if test="status != null">
				#{status,jdbcType=BIT},
			</if>
			<if test="purchaseStatus != null">
				#{purchaseStatus,jdbcType=BIT},
			</if>
			<if test="lockedStatus != null">
				#{lockedStatus,jdbcType=BIT},
			</if>
			<if test="invoiceStatus != null">
				#{invoiceStatus,jdbcType=BIT},
			</if>
			<if test="invoiceTime != null">
				#{invoiceTime,jdbcType=BIGINT},
			</if>
			<if test="paymentStatus != null">
				#{paymentStatus,jdbcType=BIT},
			</if>
			<if test="paymentTime != null">
				#{paymentTime,jdbcType=BIGINT},
			</if>
			<if test="deliveryStatus != null">
				#{deliveryStatus,jdbcType=BIT},
			</if>
			<if test="deliveryTime != null">
				#{deliveryTime,jdbcType=BIGINT},
			</if>
			<if test="isCustomerArrival != null">
				#{isCustomerArrival,jdbcType=BIT},
			</if>
			<if test="arrivalStatus != null">
				#{arrivalStatus,jdbcType=BIT},
			</if>
			<if test="arrivalTime != null">
				#{arrivalTime,jdbcType=BIGINT},
			</if>
			<if test="serviceStatus != null">
				#{serviceStatus,jdbcType=BIT},
			</if>
			<if test="haveAccountPeriod != null">
				#{haveAccountPeriod,jdbcType=BIT},
			</if>
			<if test="isPayment != null">
				#{isPayment,jdbcType=BIT},
			</if>
			<if test="totalAmount != null">
				#{totalAmount,jdbcType=DECIMAL},
			</if>
			<if test="traderId != null">
				#{traderId,jdbcType=INTEGER},
			</if>
			<if test="customerType != null">
				#{customerType,jdbcType=INTEGER},
			</if>
			<if test="customerNature != null">
				#{customerNature,jdbcType=INTEGER},
			</if>
			<if test="traderName != null">
				#{traderName,jdbcType=VARCHAR},
			</if>
			<if test="traderContactId != null">
				#{traderContactId,jdbcType=INTEGER},
			</if>
			<if test="traderContactName != null">
				#{traderContactName,jdbcType=VARCHAR},
			</if>
			<if test="traderContactMobile != null">
				#{traderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="traderContactTelephone != null">
				#{traderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="traderAddressId != null">
				#{traderAddressId,jdbcType=INTEGER},
			</if>
			<if test="traderArea != null">
				#{traderArea,jdbcType=VARCHAR},
			</if>
			<if test="traderAddress != null">
				#{traderAddress,jdbcType=VARCHAR},
			</if>
			<if test="traderComments != null">
				#{traderComments,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderId != null">
				#{takeTraderId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderName != null">
				#{takeTraderName,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactId != null">
				#{takeTraderContactId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderContactName != null">
				#{takeTraderContactName,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactMobile != null">
				#{takeTraderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactTelephone != null">
				#{takeTraderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderAddressId != null">
				#{takeTraderAddressId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderArea != null">
				#{takeTraderArea,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderAddress != null">
				#{takeTraderAddress,jdbcType=VARCHAR},
			</if>
			<if test="isSendInvoice != null">
				#{isSendInvoice,jdbcType=BIT},
			</if>
			<if test="invoiceTraderId != null">
				#{invoiceTraderId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderName != null">
				#{invoiceTraderName,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderContactId != null">
				#{invoiceTraderContactId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderContactName != null">
				#{invoiceTraderContactName,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderContactMobile != null">
				#{invoiceTraderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderContactTelephone != null">
				#{invoiceTraderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderAddressId != null">
				#{invoiceTraderAddressId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderArea != null">
				#{invoiceTraderArea,jdbcType=VARCHAR},
			</if>
			<if test="invoiceTraderAddress != null">
				#{invoiceTraderAddress,jdbcType=VARCHAR},
			</if>
			<if test="salesAreaId != null">
				#{salesAreaId,jdbcType=INTEGER},
			</if>
			<if test="salesArea != null">
				#{salesArea,jdbcType=VARCHAR},
			</if>
			<if test="terminalTraderId != null">
				#{terminalTraderId,jdbcType=INTEGER},
			</if>
			<if test="terminalTraderName != null">
				#{terminalTraderName,jdbcType=VARCHAR},
			</if>
			<if test="terminalTraderType != null">
				#{terminalTraderType,jdbcType=INTEGER},
			</if>
			<if test="invoiceType != null">
				#{invoiceType,jdbcType=INTEGER},
			</if>
			<if test="freightDescription != null">
				#{freightDescription,jdbcType=INTEGER},
			</if>
			<if test="deliveryType != null">
				#{deliveryType,jdbcType=INTEGER},
			</if>
			<if test="logisticsId != null">
				#{logisticsId,jdbcType=INTEGER},
			</if>
			<if test="paymentType != null">
				#{paymentType,jdbcType=INTEGER},
			</if>
			<if test="prepaidAmount != null">
				#{prepaidAmount},
			</if>
			<if test="accountPeriodAmount != null">
				#{accountPeriodAmount,jdbcType=DECIMAL},
			</if>
			<if test="periodDay != null">
				#{periodDay,jdbcType=INTEGER},
			</if>
			<if test="logisticsCollection != null">
				#{logisticsCollection,jdbcType=BIT},
			</if>
			<if test="retainageAmount != null">
				#{retainageAmount,jdbcType=DECIMAL},
			</if>
			<if test="retainageAmountMonth != null">
				#{retainageAmountMonth,jdbcType=INTEGER},
			</if>
			<if test="paymentComments != null">
				#{paymentComments,jdbcType=VARCHAR},
			</if>
			<if test="additionalClause != null">
				#{additionalClause,jdbcType=VARCHAR},
			</if>
			<if test="logisticsComments != null">
				#{logisticsComments,jdbcType=VARCHAR},
			</if>
			<if test="financeComments != null">
				#{financeComments,jdbcType=VARCHAR},
			</if>
			<if test="comments != null">
				#{comments,jdbcType=VARCHAR},
			</if>
			<if test="invoiceComments != null">
				#{invoiceComments,jdbcType=VARCHAR},
			</if>
			<if test="deliveryDirect != null">
				#{deliveryDirect,jdbcType=BIT},
			</if>
			<if test="supplierClause != null">
				#{supplierClause,jdbcType=VARCHAR},
			</if>
			<if test="haveAdvancePurchase != null">
				#{haveAdvancePurchase,jdbcType=BIT},
			</if>
			<if test="advancePurchaseStatus != null">
				#{advancePurchaseStatus,jdbcType=BIT},
			</if>
			<if test="advancePurchaseComments != null">
				#{advancePurchaseComments,jdbcType=VARCHAR},
			</if>
			<if test="advancePurchaseTime != null">
				#{advancePurchaseTime,jdbcType=BIGINT},
			</if>
			<if test="isUrgent != null">
				#{isUrgent,jdbcType=BIT},
			</if>
			<if test="urgentAmount != null">
				#{urgentAmount,jdbcType=DECIMAL},
			</if>
			<if test="haveCommunicate != null">
				#{haveCommunicate,jdbcType=BIT},
			</if>
			<if test="prepareComments != null">
				#{prepareComments,jdbcType=VARCHAR},
			</if>
			<if test="marketingPlan != null">
				#{marketingPlan,jdbcType=VARCHAR},
			</if>
			<if test="statusComments != null">
				#{statusComments,jdbcType=INTEGER},
			</if>
			<if test="syncStatus != null">
				#{syncStatus,jdbcType=BIT},
			</if>
			<if test="satisfyInvoiceTime != null">
				#{satisfyInvoiceTime,jdbcType=BIGINT},
			</if>
			<if test="satisfyDeliveryTime != null">
				#{satisfyDeliveryTime,jdbcType=BIGINT},
			</if>
			<if test="isSalesPerformance != null">
				#{isSalesPerformance,jdbcType=BIT},
			</if>
			<if test="salesPerformanceTime != null">
				#{salesPerformanceTime,jdbcType=BIGINT},
			</if>
			<if test="salesPerformanceModTime != null">
				#{salesPerformanceModTime,jdbcType=BIGINT},
			</if>
			<if test="isDelayInvoice != null">
				#{isDelayInvoice,jdbcType=BIT},
			</if>
			<if test="invoiceMethod != null">
				#{invoiceMethod,jdbcType=BIT},
			</if>
			<if test="lockedReason != null">
				#{lockedReason,jdbcType=VARCHAR},
			</if>
			<if test="costUserIds != null">
				#{costUserIds,jdbcType=VARCHAR},
			</if>
			<if test="ownerUserId != null">
				#{ownerUserId,jdbcType=INTEGER},
			</if>
			<if test="invoiceEmail != null">
				#{invoiceEmail,jdbcType=VARCHAR},
			</if>
			<if test="addTime != null">
				#{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				#{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				#{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				#{updater,jdbcType=INTEGER},
			</if>
			<if test="traderAreaId != null">
				#{traderAreaId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderAreaId != null">
				#{takeTraderAreaId,jdbcType=INTEGER},
			</if>
			<if test="invoiceTraderAreaId != null">
				#{invoiceTraderAreaId, jdbcType=INTEGER},
			</if>
			<if test="couponMoney!=null">
				#{couponMoney},
			</if>
			<if test="isCoupons!=null">
				#{isCoupons},
			</if>
			<if test="actionId!=null">
				#{actionId},
			</if>
			<if test="originalAmount!=null">
				#{originalAmount},
			</if>
			<if test="autoAudit!=null">
				#{autoAudit},
			</if>
			<if test="isSameAddress!=null">
				#{isSameAddress},
			</if>
			<if test="bdtraderComments != null and bdtraderComments != ''">
				#{bdtraderComments, jdbcType=VARCHAR},
			</if>
			<if test="groupCustomerId != null">
				#{groupCustomerId, jdbcType=VARCHAR},
			</if>
			<if test="createMobile != null">
				#{createMobile, jdbcType=VARCHAR},
			</if>
			<if test="isPrintout != null">
				#{isPrintout},
			</if>
			<if test="invoiceSendNode != null">
				#{invoiceSendNode},
			</if>
			<if test="reasonNo != null">
				#{reasonNo, jdbcType=VARCHAR},
			</if>
			<if test="billPeriodSettlementType != null">
				#{billPeriodSettlementType, jdbcType=INTEGER},
			</if>
			<if test="deliveryClaim != null">
				#{deliveryClaim, jdbcType=INTEGER},
			</if>
			<if test="deliveryDelayTime != null">
				#{deliveryDelayTime, jdbcType=BIGINT},
			</if>
			<if test="isNew != null">
				#{isNew,jdbcType=BIT},
			</if>

			<if test="confirmTime != null">
				#{confirmTime},
			</if>

			<if test="confirmStatus != null" >
				#{confirmStatus},
			</if>

			<if test="prepareReaseonType != null" >
				#{prepareReaseonType},
			</if>
		</trim>
	</insert>
	<select id="getSaleOrersByIdList" resultType="com.vedeng.order.model.Saleorder">
		SELECT *
		FROM
		T_SALEORDER
		WHERE SALEORDER_ID IN
		<foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</select>

	<!--根据订单号查询订单-->
	<select id="getSaleOrderId" resultMap="BaseResultMap">
		select * from T_SALEORDER where SALEORDER_NO=#{saleorderNo}
	</select>
    <select id="getSaleorderByExpressDetailId" resultType="com.vedeng.order.model.Saleorder" parameterType="list">
		SELECT S.SALEORDER_ID, S.SALEORDER_NO, S.TRADER_NAME, GROUP_CONCAT(ED.EXPRESS_DETAIL_ID) AS COMMENTS FROM T_SALEORDER S
		JOIN T_SALEORDER_GOODS SG ON S.SALEORDER_ID = SG.SALEORDER_ID
		JOIN T_EXPRESS_DETAIL ED ON SG.SALEORDER_GOODS_ID = ED.RELATED_ID
		WHERE
		ED.EXPRESS_DETAIL_ID
		IN
		<foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
			#{item,jdbcType=INTEGER}
		</foreach>
		GROUP BY SG.SALEORDER_ID
	</select>


    <select id="getAllActionId" resultType="int">
		SELECT
			A.ACTION_ID
		FROM
			T_SALEORDER A
		WHERE
			A.ACTION_ID > 0
		  AND FROM_UNIXTIME( A.ADD_TIME / 1000, '%Y-%m-%d' ) > DATE_SUB( CURDATE(), INTERVAL #{day} DAY )
		GROUP BY
			A.ACTION_ID
    </select>
	<select id="getSaleorderGoodsByOrderListId" resultType="com.vedeng.order.model.SaleorderGoods">
		SELECT * FROM T_SALEORDER_GOODS WHERE EL_ORDERLIST_ID IN
		<foreach collection="list" index="index" item="item" open="(" close=")" separator=",">
			#{item,jdbcType=INTEGER}
		</foreach>
	</select>
	<select id="getSaleorderByOrderListId" resultType="com.vedeng.order.model.Saleorder">
		SELECT S.* FROM T_SALEORDER S JOIN T_SALEORDER_GOODS SG ON S.SALEORDER_ID = SG.SALEORDER_ID WHERE SG.EL_ORDERLIST_ID = #{orderListId,jdbcType=INTEGER}
	</select>

	<!--改变第一次物流的评论信息-->
	<update id="updateLogisticsComments">
			UPDATE T_SALEORDER SET LOGISTICS_COMMENTS=#{logisticsComments} WHERE SALEORDER_ID=#{saleorderId}
	</update>

	<select id="getUserDetailInfoByUserId" resultType="com.vedeng.authorization.model.User" parameterType="java.lang.Integer">
        select
          USER_ID,EMAIL
        from T_USER_DETAIL
        where USER_ID = #{userId,jdbcType=INTEGER}
  	</select>

	<!--所属类型TRADER_TYPE 1::经销商（包含终端）2:供应商-->
	<select id="getUserInfoByTraderId" parameterType="java.lang.Integer" resultType="com.vedeng.authorization.model.User">
		SELECT
			b.USER_ID,b.USERNAME,d.ORG_ID,e.ORG_NAME
		FROM
			T_R_TRADER_J_USER a
		LEFT JOIN
			T_USER b
		ON
			a.USER_ID = b.USER_ID
		LEFT JOIN
			T_R_USER_POSIT c
		ON
			b.USER_ID = c.USER_ID
		LEFT JOIN
			T_POSITION d
		ON
			d.POSITION_ID = c.POSITION_ID
		LEFT JOIN
			T_ORGANIZATION e
		ON
			d.ORG_ID = e.ORG_ID
		WHERE
			a.TRADER_ID = #{traderId}
			AND
			a.TRADER_TYPE = 1
			limit 1
	</select>

	<!--宝石花出库单列表-->
	<select id="getFlowerPrintOutListPage" resultMap="BaseResultMap" parameterType="Map">
	SELECT
	DISTINCT
		A.SALEORDER_ID,A.SALEORDER_NO,A.TRADER_NAME,A.PAYMENT_STATUS,A.DELIVERY_STATUS,A.ARRIVAL_STATUS,A.VALID_TIME,A.TRADER_ID
	FROM
		T_SALEORDER A
	LEFT JOIN T_SALEORDER_GOODS  B ON A.SALEORDER_ID=B.SALEORDER_ID
	JOIN T_WAREHOUSE_GOODS_OPERATE_LOG C ON C.RELATED_ID = B.SALEORDER_GOODS_ID AND C.OPERATE_TYPE=2 AND C.IS_ENABLE =1
	WHERE
		1=1
		<if test="saleorder.takeTraderName != null and saleorder.takeTraderName != '' ">
		  AND A.TAKE_TRADER_NAME LIKE CONCAT('%',#{saleorder.takeTraderName },'%' )
		</if>
		AND A.TRADER_ID IN
		<foreach collection="saleorder.traderIdList" item="item" open="(" close=")"  separator=",">
		 #{item}
		</foreach>
		<if test="saleorder.searchBegintime != null and saleorder.searchBegintime != ''">
			AND C.ADD_TIME >= #{saleorder.searchBegintime,jdbcType=INTEGER}
		</if>
		<if test="saleorder.searchEndtime != null and saleorder.searchEndtime != ''">
			AND C.ADD_TIME <![CDATA[ <= ]]>
			#{saleorder.searchEndtime,jdbcType=INTEGER}
		</if>
		ORDER BY A.SALEORDER_ID DESC
	</select>

	<update id="cleanSaleOrder"  >

UPDATE T_SALEORDER
SET COMPANY_ID = 6 ,STATUS = 3,MOD_TIME=NOW()
WHERE
	SALEORDER_ID IN (
	SELECT
		t.SALEORDER_ID
	FROM
		(
		SELECT
			SALEORDER_ID
		FROM
			T_SALEORDER S
		WHERE
		S.COMPANY_ID=1 AND
			S.USER_ID IN ( SELECT user_id FROM T_USER WHERE username IN ( ${testUserNames} ) )
			AND S.TRADER_ID IN ( SELECT TRADER_ID FROM T_R_TRADER_J_USER WHERE USER_ID IN ( SELECT USER_ID FROM T_USER WHERE username IN ( ${testUserNames} ) ) )
		) t
	)

	</update>
	<update id="cleanSaleOrder2"  >
		update  T_SALEORDER set COMPANY_ID = 6 ,STATUS = 3  where user_id in (select USER_ID from T_USER where
				USERNAME in
				(${testUserNames}))  and COMPANY_ID = 1

	</update>
	<update id="clearBuyorderInvoice"  >
		update   T_INVOICE set COMPANY_ID = 6  where  TAG = 2 and TYPE = 503 and
				RELATED_ID in (
				select BUYORDER_ID from T_BUYORDER where USER_ID in
														 (select USER_ID from T_USER where USERNAME in (${testUserNames}))) and COMPANY_ID = 1
	</update>
	<update id="clearSaleorderInvoice"  >
	update  T_INVOICE  SET COMPANY_ID = 6
	where TAG = 1 AND TYPE = 505 and RELATED_ID in (
	select SALEORDER_ID from T_SALEORDER where USER_ID in
	(select USER_ID from T_USER where USERNAME in (${testUserNames})))   and COMPANY_ID = 1
	</update>

	<update id="clearBuyorder"  >
		update  T_BUYORDER set COMPANY_ID = 6 where  USER_ID in
													 (select USER_ID from T_USER where USERNAME in (${testUserNames})) and COMPANY_ID  = 1
   </update>
	<update id="cleanAfterSale"  >
		update  T_AFTER_SALES set COMPANY_ID = 6        ,STATUS = 3,mod_time = now( )
		where CREATOR  in (SELECT user_id FROM T_USER WHERE username IN ( ${testUserNames})) and COMPANY_ID = 1
	</update>


	<!--校验锁的状态-->
	<update id="updateLockedStatus">
		update T_SALEORDER set LOCKED_STATUS=1 where SALEORDER_ID=#{saleorderId,jdbcType=INTEGER}
	</update>
	<update id="clearBussiness"  >
	update T_BUSSINESS_CHANCE set COMPANY_ID = 6 where USER_ID in (select  USER_ID from T_USER  where USERNAME  in(${testUserNames}))
	</update>
	<update id="updateDataTimeByOrderId">
	UPDATE T_SALEORDER
	SET UPDATE_DATA_TIME = NOW()
	WHERE
	SALEORDER_ID = #{orderId,jdbcType=INTEGER}
	</update>
	<update id="updateDataTimeByDetailId">
	UPDATE T_SALEORDER
	SET UPDATE_DATA_TIME = NOW()
	WHERE
	SALEORDER_ID = (
	  SELECT A.SALEORDER_ID FROM T_SALEORDER_GOODS A WHERE A.SALEORDER_GOODS_ID = #{orderDetailId,jdbcType=INTEGER}
	)
	</update>
	<select id="getPaymentAmount" resultType="java.math.BigDecimal">
	SELECT
	IFNULL(SUM( a.AMOUNT ),0) receive_amount
FROM
	T_CAPITAL_BILL a
	LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
WHERE
	b.ORDER_TYPE = 1
	AND (
		a.TRADER_TYPE IN ( 1, 4 )
		OR (
			a.TRADER_TYPE = 2
			AND (a.TRADER_MODE = 520 OR a.TRADER_MODE= 522)
		))
	 AND  b.RELATED_ID = #{orderId,jdbcType=INTEGER}
	</select>
	<select id="getReturnAmount" resultType="java.math.BigDecimal">
	SELECT
	IFNULL(ABS(SUM(C.AMOUNT)),0) as AMOUNT
	FROM
	T_AFTER_SALES A
	LEFT JOIN T_CAPITAL_BILL_DETAIL B ON A.AFTER_SALES_ID=B.RELATED_ID
	LEFT JOIN T_CAPITAL_BILL C ON B.CAPITAL_BILL_ID=C.CAPITAL_BILL_ID
	WHERE
	A.ORDER_ID = #{orderId,jdbcType=INTEGER}
	AND A.SUBJECT_TYPE = 535
	AND A.ATFER_SALES_STATUS IN ( 1, 2 )
	AND B.BUSSINESS_TYPE=531
	AND B.ORDER_TYPE=3
	AND C.TRADER_MODE=530
	</select>
	<update id="updateAmountInfo">
	UPDATE T_SALEORDER
	SET REAL_PAY_AMOUNT = #{paymentAmount,jdbcType=DECIMAL},
	REAL_RETURN_AMOUNT = #{endAmount,jdbcType=DECIMAL},
	REAL_TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
	NOPAYBACK_AMOUNT = #{accountPayable,jdbcType=DECIMAL}
	WHERE
	SALEORDER_ID=#{saleorderId,jdbcType=INTEGER}
	</update>
    <update id="updateNotSalesPerformanceOfSaleorder">
		UPDATE T_SALEORDER SET IS_SALES_PERFORMANCE = 0, SALES_PERFORMANCE_TIME = 0 WHERE SALEORDER_ID = #{saleorderId}
	</update>
	<select id="getrecentDayOrder" resultType="java.lang.Integer">
		SELECT
		a.SALEORDER_ID
		FROM
		T_SALEORDER a
		WHERE
		a.`STATUS` != 3
		AND a.ORDER_TYPE != 2
		AND a.UPDATE_DATA_TIME > DATE_SUB(
		CURDATE(),
		INTERVAL ${num} DAY)
		ORDER BY
		a.SALEORDER_ID DESC
	</select>
	<select id="getSaleorderGoodsById" resultType="com.vedeng.order.model.SaleorderGoods">
		SELECT * FROM T_SALEORDER_GOODS WHERE SALEORDER_ID = #{saleorderId} AND IS_DELETE = 0;
	</select>
	<select id="getSaleorderGoodsByIdNoSpecial" resultType="com.vedeng.order.model.SaleorderGoods">
		SELECT * FROM T_SALEORDER_GOODS WHERE SALEORDER_ID = #{saleorderId} AND IS_DELETE = 0
										  and  GOODS_ID not in (select SKU_ID from V_CORE_SKU a left join T_SYS_COST_CATEGORY b on a.COST_CATEGORY_ID = b.COST_CATEGORY_ID
																where a.IS_VIRTURE_SKU = 1 and a.STATUS = 1 and b.IS_NEED_PURCHASE =0
																union
																SELECT
																	COMMENTS SKU_ID
																FROM
																	T_SYS_OPTION_DEFINITION
																WHERE
																	PARENT_ID = 693)
	</select>


	<select id="findByElorderNo" parameterType="java.lang.String" resultType="java.lang.Integer">
    select
     count(SALEORDER_ID)
    from T_SALEORDER
    where EL_SALEORDRE_NO = #{orderNumber,jdbcType=VARCHAR}
  </select>
<!--	<select id="getSaleorderRealAmountByTraderId" resultMap="BaseResultMap">-->
<!--	SELECT-->
<!--	A.SALEORDER_ID-->
<!--	FROM-->
<!--	T_SALEORDER A-->
<!--	WHERE-->
<!--	A.`STATUS` IN ( 1, 2 )-->
<!--	AND A.REAL_PAY_AMOUNT > 0-->
<!--	AND A.TRADER_ID =#{traderId,jdbcType=INTEGER}-->
<!--	</select>-->
    <select id="getCouponOrderDataByCouponId" resultType="com.vedeng.order.model.coupon.CouponOrderData">
    SELECT
    COUNT(DISTINCT B.TRADER_ID) AS traderNum,
	COUNT(B.SALEORDER_ID) AS orderNum,
	SUM(B.REAL_PAY_AMOUNT) AS allTotalAmount,
	SUM(A.DENOMINATION) AS totalCouponAmount,
	(SUM(B.TOTAL_AMOUNT)+SUM(A.DENOMINATION)) AS saleAmount
    FROM
	T_SALEORDER_COUPON A
	LEFT JOIN T_SALEORDER B ON A.SALEORDER_ID = B.SALEORDER_ID
    WHERE
	B.ORDER_TYPE IN ( 1, 5 )
	AND B.PAYMENT_STATUS = 2
	AND A.COUPON_ID = #{couponId,jdbcType=INTEGER}
    </select>
	<select id="getCouponOrderDataDetailListByCouponId" resultType="com.vedeng.order.model.coupon.CouponOrderDetailData">
	SELECT
	B.SALEORDER_NO as orderNo,
	B.TRADER_ID as traderId,
	C.TRADER_NAME as traderName,
	A.COUPON_ID as couponId,
	A.COUPON_CODE as couponCode,
	B.TOTAL_AMOUNT as originalAmount,
	B.REAL_PAY_AMOUNT  as realPayAmount
    FROM
	T_SALEORDER_COUPON A
	LEFT JOIN T_SALEORDER B ON A.SALEORDER_ID = B.SALEORDER_ID
	LEFT JOIN T_TRADER C ON B.TRADER_ID = C.TRADER_ID
    WHERE
    B.ORDER_TYPE IN (1,5)
    AND B.PAYMENT_STATUS=2
	AND A.COUPON_ID = #{couponId,jdbcType=INTEGER}
    </select>
    <select id="getCouponOrderDataDetailListBycouponCodeList"
            resultType="com.vedeng.order.model.coupon.CouponOrderDetailData">
		SELECT
	B.SALEORDER_NO as orderNo,
	B.TRADER_ID as traderId,
	C.TRADER_NAME as traderName,
	A.COUPON_ID as couponId,
	A.COUPON_CODE as couponCode,
	B.TOTAL_AMOUNT as originalAmount,
	B.REAL_PAY_AMOUNT  as realPayAmount
    FROM
	T_SALEORDER_COUPON A
	LEFT JOIN T_SALEORDER B ON A.SALEORDER_ID = B.SALEORDER_ID
	LEFT JOIN T_TRADER C ON B.TRADER_ID = C.TRADER_ID
    WHERE
    B.ORDER_TYPE IN (1,5)
	AND A.COUPON_CODE IN
		<foreach collection="list" item="item" open="(" close=")"  separator=",">
			#{item}
		</foreach>
	GROUP BY B.SALEORDER_ID
	</select>

	<select id="getBDSaleOrderAndHasCoupon" resultType="com.vedeng.order.model.Saleorder">
		SELECT
			A.*
		FROM
			T_SALEORDER A
		INNER JOIN T_SALEORDER_COUPON B ON A.SALEORDER_ID = B.SALEORDER_ID
		WHERE A.STATUS IN (0,1) AND A.ORDER_TYPE = 1
    </select>

    <select id="getSaleorderByQuoteorderId" resultType="com.vedeng.order.model.Saleorder">
		SELECT * FROM T_SALEORDER WHERE QUOTEORDER_ID = #{quoteorderId}
	</select>

	<select id="getTraderIdOrderedInPeriod" resultType="int" parameterType="com.vedeng.trader.model.Period">
		SELECT TRADER_ID FROM T_SALEORDER
		 WHERE PAYMENT_STATUS=0
		 AND ORDER_TYPE IN (0,1)
		AND ADD_TIME <![CDATA[>=]]> #{beginTime}
      and ADD_TIME <![CDATA[<=]]> #{endTime}
      AND TRADER_ID IS NOT NULL
	</select>

	<select id="getTraderIdBuyInPeriod" resultType="int" parameterType="com.vedeng.trader.model.Period">
		SELECT TRADER_ID FROM T_SALEORDER
		WHERE
		PAYMENT_STATUS!=0
		AND ORDER_TYPE IN (0,1)
		AND ADD_TIME <![CDATA[>=]]> #{beginTime}
      and ADD_TIME <![CDATA[<=]]> #{endTime}
      AND TRADER_ID IS NOT NULL
	</select>

	<select id="getTraderIdsOrderGoods" resultType="int" parameterType="com.vedeng.trader.group.model.GoodsQueryParam">
		SELECT DISTINCT O.TRADER_ID FROM T_SALEORDER O
		LEFT JOIN T_SALEORDER_GOODS G ON O.SALEORDER_ID=G.SALEORDER_ID
		<if test="categoryIds !=null or brandId !=null">
			LEFT JOIN V_CORE_SKU K ON G.SKU=K.SKU_NO
			LEFT JOIN V_CORE_SPU P ON K.SPU_ID=K.SPU_ID
		</if>
		WHERE 1=1
		AND O.ORDER_TYPE IN (0,1)
		AND O.PAYMENT_STATUS=0
		AND G.IS_DELETE=0
		and O.ADD_TIME <![CDATA[>=]]> #{period.beginTime}
		and O.ADD_TIME <![CDATA[<=]]> #{period.endTime}
		<if test="categoryIds !=null ">
			AND P.CATEGORY_ID IN
			<foreach collection="categoryIds" item="id" separator="," open="(" close=")">
				#{id}
			</foreach>
		</if>
		<if test="brandId !=null ">
			AND P.BRAND_ID =#{brandId}
		</if>
		<if test="skuList !=null ">
			AND G.SKU IN
			<foreach collection="skuList" item="id" separator="," open="(" close=")">
				#{id}
			</foreach>
		</if>
	</select>

	<select id="getTraderIdsBuyGoods" resultType="int" parameterType="com.vedeng.trader.group.model.GoodsQueryParam">
		SELECT DISTINCT O.TRADER_ID FROM T_SALEORDER O
		LEFT JOIN T_SALEORDER_GOODS G ON O.SALEORDER_ID=G.SALEORDER_ID
		<if test="categoryIds !=null or brandId !=null">
			LEFT JOIN V_CORE_SKU K ON G.SKU=K.SKU_NO
			LEFT JOIN V_CORE_SPU P ON P.SPU_ID=K.SPU_ID
		</if>
		WHERE 1=1
		AND O.ORDER_TYPE IN (0,1)
		AND O.PAYMENT_STATUS!=0
		AND G.IS_DELETE=0
		AND O.ADD_TIME <![CDATA[>=]]> #{period.beginTime}
		AND O.ADD_TIME <![CDATA[<=]]> #{period.endTime}
		<if test="categoryIds !=null ">
			AND P.CATEGORY_ID IN
			<foreach collection="categoryIds" item="id" separator="," open="(" close=")">
				#{id}
			</foreach>
		</if>
		<if test="brandId !=null ">
			AND P.BRAND_ID =#{brandId}
		</if>
		<if test="skuList !=null ">
			AND G.SKU IN
			<foreach collection="skuList" item="id" separator="," open="(" close=")">
				#{id}
			</foreach>
		</if>
	</select>

	<select id="getTraderIdsByAmount" parameterType="com.vedeng.trader.group.model.SaleorderSum" resultType="java.lang.Integer">
		select A.TRADER_ID from (
		   SELECT e.TRADER_ID,SUM(e.realAmount) AS AMOUNT from (
		SELECT a.TRADER_ID, if(COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE = 5,b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0) <![CDATA[ < ]]> 0,
		0,
		COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE = 5,b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0)) as realAmount
		FROM T_SALEORDER a
		LEFT JOIN
		(SELECT sum(bb.NUM * cc.PRICE) AS tk_amount,
		sum(bb.SKU_REFUND_AMOUNT) AS SKU_REFUND_AMOUNT,
		aa.ORDER_ID
		FROM T_AFTER_SALES aa
		LEFT JOIN T_AFTER_SALES_GOODS bb
		ON aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
		LEFT JOIN T_SALEORDER_GOODS cc
		ON bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
		WHERE     aa.TYPE = 539
		AND aa.SUBJECT_TYPE = 535
		AND aa.VALID_STATUS = 1
		AND aa.ATFER_SALES_STATUS IN (1,2)
		GROUP BY aa.ORDER_ID) AS b
		ON a.SALEORDER_ID = b.ORDER_ID
		WHERE a.ORDER_TYPE=1
		and a.PAYMENT_STATUS = 2
		and a.ADD_TIME <![CDATA[>=]]> #{period.beginTime}
		and a.ADD_TIME <![CDATA[<=]]> #{period.endTime}
		) e
		   GROUP BY e.TRADER_ID
		) A
		WHERE 1=1
		<if test="lowAmount!=null">
			and A.AMOUNT <![CDATA[>=]]> #{lowAmount}
		</if>
		<if test="highAmount!=null">
			and A.AMOUNT <![CDATA[<=]]> #{highAmount}
		</if>
	</select>

	<select id="getTraderIdsByTimes" parameterType="com.vedeng.trader.group.model.SaleorderSum" resultType="java.lang.Integer">
		select A.TRADER_ID FROM (
		select e.TRADER_ID,COUNT(e.TRADER_ID) AS `TIMES` from (
		SELECT a.TRADER_ID, if(COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE = 5,b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0) <![CDATA[<]]> 0,
		0,
		COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE = 5,b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0)) as realAmount
		FROM T_SALEORDER a
		LEFT JOIN
		(SELECT sum(bb.NUM * cc.PRICE) AS tk_amount,
		sum(bb.SKU_REFUND_AMOUNT) AS SKU_REFUND_AMOUNT,
		aa.ORDER_ID
		FROM T_AFTER_SALES aa
		LEFT JOIN T_AFTER_SALES_GOODS bb
		ON aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
		LEFT JOIN T_SALEORDER_GOODS cc
		ON bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
		WHERE     aa.TYPE = 539
		AND aa.SUBJECT_TYPE = 535
		AND aa.VALID_STATUS = 1
		AND aa.ATFER_SALES_STATUS IN (1,2)
		GROUP BY aa.ORDER_ID) AS b
		ON a.SALEORDER_ID = b.ORDER_ID
		WHERE a.PAYMENT_STATUS!=0 AND a.ORDER_TYPE=1 AND  a.ADD_TIME <![CDATA[>=]]> #{period.beginTime}
		and a.ADD_TIME <![CDATA[<=]]> #{period.endTime}
		) e where e.realAmount <![CDATA[>]]>0
		GROUP BY e.TRADER_ID
		) A
		WHERE 1=1
		<if test="lowTimes!=null">
			and A.TIMES <![CDATA[>=]]> #{lowTimes}
		</if>
		<if test="highTimes!=null">
			and A.TIMES <![CDATA[<=]]> #{highTimes}
		</if>
	</select>

	<select id="getTraderIdsByDealRecently" parameterType="com.vedeng.trader.group.model.SaleorderSum" resultType="java.lang.Integer">
		   SELECT DISTINCT TRADER_ID from T_SALEORDER
		   WHERE ORDER_TYPE=1 AND ADD_TIME <![CDATA[>=]]> #{period.beginTime}
		     and ADD_TIME <![CDATA[<=]]> #{period.endTime}

	</select>
    <select id="getVaildOrderAndNoChooseOrderId" resultType="java.lang.Integer">
	SELECT
	A.SALEORDER_ID
	FROM
	T_SALEORDER_GOODS A
	LEFT JOIN T_SALEORDER B ON A.SALEORDER_ID = B.SALEORDER_ID
	WHERE
	B.`STATUS` = 1
	AND A.IS_DELETE = 0
	AND A.DELIVERY_STATUS !=2
	AND B.DELIVERY_STATUS !=2
	AND A.DELIVERY_DIRECT = 0
	AND B.ORDER_TYPE != 2
	AND A.GOODS_ID != 127063
	AND B.ADD_TIME > 1577808000000
	GROUP BY
	A.SALEORDER_ID
	</select>
	<select id="getSaleorderRealAmountByTraderId" resultType="com.vedeng.order.model.Saleorder">
    SELECT
	A.SALEORDER_ID
	FROM
	T_SALEORDER A
	WHERE
	A.`STATUS` IN ( 1, 2 )
	AND A.REAL_PAY_AMOUNT > 0
	AND A.TRADER_ID =#{traderId,jdbcType=INTEGER}
    </select>

	<select id="getHcSaleorderInfoByInvoiceIds" resultType="com.vedeng.order.model.Saleorder">
		SELECT
			T1.*
		FROM
			T_SALEORDER T1
			INNER JOIN T_INVOICE T2 ON T1.SALEORDER_ID = T2.RELATED_ID
		WHERE
			T1.ORDER_TYPE = 5
		AND T2.INVOICE_ID IN
		<foreach collection="invoiceIds" item="invoiceId" index="index" open="(" close=")" separator=",">
			#{invoiceId,jdbcType=INTEGER}
		</foreach>
		GROUP BY
			T2.INVOICE_ID
	</select>

	<select id="findRelateSaleOrderNo" resultType="java.lang.String">
		SELECT
			s.SALEORDER_NO
		FROM T_SALEORDER_GOODS sg
		LEFT JOIN T_SALEORDER s ON s.SALEORDER_ID = sg.SALEORDER_ID
		LEFT JOIN T_R_BUYORDER_J_SALEORDER rjs ON rjs.SALEORDER_GOODS_ID=sg.SALEORDER_GOODS_ID
		WHERE rjs.BUYORDER_GOODS_ID = #{buyOrderGoodId,jdbcType=INTEGER}
	</select>
	<select id="getSaleorderRealAmountByTraderIdAndSaleorderId" resultType="com.vedeng.order.model.Saleorder">
	SELECT
	A.SALEORDER_ID
	FROM
	T_SALEORDER A
	WHERE
	A.`STATUS` IN ( 1, 2 )
	AND A.REAL_PAY_AMOUNT > 0
	AND A.SALEORDER_ID != #{saleorderId,jdbcType=INTEGER}
	AND A.TRADER_ID = #{traderId,jdbcType=INTEGER}
	</select>
    <select id="getRealAmount" resultType="java.math.BigDecimal">
		SELECT if(COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE IN(1, 5),b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0) <![CDATA[ < ]]> 0,
		          0,
		          COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE IN(1, 5),b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0))
		FROM T_SALEORDER a
		     LEFT JOIN
		     (SELECT sum(bb.NUM * cc.PRICE) AS tk_amount,
		             sum(bb.SKU_REFUND_AMOUNT) AS SKU_REFUND_AMOUNT,
		             aa.ORDER_ID
		      FROM T_AFTER_SALES aa
		           LEFT JOIN T_AFTER_SALES_GOODS bb
		              ON aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
		           LEFT JOIN T_SALEORDER_GOODS cc
		              ON bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
		      WHERE     aa.TYPE = 539
		            AND aa.SUBJECT_TYPE = 535
		            AND aa.VALID_STATUS = 1
		            AND aa.ATFER_SALES_STATUS IN (1,2)
		        	AND bb.GOODS_TYPE = 0 <!-- VDERP-7492 计算实际金额，需要剔除手续费 -->
		            AND aa.ORDER_ID = #{saleorderId,jdbcType=INTEGER}
		      GROUP BY aa.ORDER_ID) AS b
		        ON a.SALEORDER_ID = b.ORDER_ID
		WHERE a.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</select>
	<update id="updateSaleorderPayStatus" parameterType="com.vedeng.order.model.Saleorder">
		UPDATE T_SALEORDER
	  	SET
	  		PAYMENT_STATUS = #{paymentStatus,jdbcType=INTEGER},
	  		PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
	  		IS_PAYMENT = #{isPayment,jdbcType=BIT},
	  		SATISFY_DELIVERY_TIME = #{satisfyDeliveryTime,jdbcType=BIGINT},
	  		PAY_TYPE = #{payType,jdbcType=BIT},
	  		PAYMENT_MODE = #{paymentMode,jdbcType=BIT}
		WHERE SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</update>


	<update id="updateIsDeleteByOrderNo" parameterType="com.vedeng.order.model.Saleorder">
		UPDATE T_SALEORDER SET IS_DELETE=1 WHERE SALEORDER_NO =#{saleorderNo}
	</update>
	<update id="updateContractUrlOfSaleorder">
		UPDATE T_SALEORDER SET CONTRACT_URL = #{contractUrl,jdbcType=VARCHAR} WHERE SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</update>

	<update id="updateContractNoStampUrlOfSaleorder">
		UPDATE T_SALEORDER SET CONTRACT_NO_STAMP_URL = #{contractUrl,jdbcType=VARCHAR} WHERE SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</update>

	<select id="getHCSaleOrderAndHasCoupon"  resultType="com.vedeng.order.model.Saleorder">
		SELECT SALEORDER_ID,SALEORDER_NO,TRADER_ID,STATUS,PAYMENT_STATUS,VALID_TIME,ADD_TIME FROM T_SALEORDER WHERE (ACTION_ID = 0 OR ACTION_ID IS NULL) and PAYMENT_STATUS=0 AND ORDER_TYPE=5 and STATUS <![CDATA[ <> ]]> 3
	</select>


	<select id="getHcOrgValidStatus" resultType="com.vedeng.order.model.Saleorder">

SELECT
	A.SALEORDER_NO,
	A.SALEORDER_ID,
	A.`STATUS`,
	ORG.ORG_NAME,
	A.TRADER_NAME,
	B.SKU,
	B.NUM,
	IFNULL( B.AFTER_RETURN_NUM, 0 ) AFTER_RETURN_NUM
FROM
	T_SALEORDER A
	LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID = B.SALEORDER_ID
	AND B.NUM - IFNULL( B.AFTER_RETURN_NUM, 0 ) > 0 AND B.IS_DELETE=0
	LEFT JOIN T_R_TRADER_J_USER C ON C.TRADER_ID = A.TRADER_ID
	AND C.TRADER_TYPE = 1
	LEFT JOIN T_USER D ON D.USER_ID = C.USER_ID
	LEFT JOIN T_R_USER_POSIT UP ON UP.USER_ID = D.USER_ID
	LEFT JOIN T_POSITION PO ON UP.POSITION_ID = PO.POSITION_ID
	LEFT JOIN T_ORGANIZATION ORG ON PO.ORG_ID = ORG.ORG_ID
WHERE
	A.`STATUS` IN (1,2)
	AND A.SALEORDER_ID != #{saleorderId,jdbcType=INTEGER}
	AND A.TRADER_ID = #{traderId,jdbcType=INTEGER}
	AND ORG.ORG_NAME LIKE '%医械购%'
	AND B.SALEORDER_GOODS_ID IS NOT NULL
	AND B.GOODS_ID NOT IN ( SELECT COMMENTS FROM T_SYS_OPTION_DEFINITION WHERE PARENT_ID = 693 )
	AND A.VALID_TIME <![CDATA[ < ]]> (SELECT S.VALID_TIME FROM T_SALEORDER S WHERE S.SALEORDER_ID =  #{saleorderId,jdbcType=INTEGER})
GROUP BY
	A.SALEORDER_ID
	</select>

    <select id="getBdOrderListPage" parameterType="java.util.Map" resultType="com.vedeng.order.model.SimpleBdOrderChooseRes">
        SELECT S.SALEORDER_ID,S.SALEORDER_NO,S.TRADER_ID,S.TRADER_NAME,S.TRADER_CONTACT_NAME,S.TRADER_CONTACT_MOBILE,
        S.REAL_TOTAL_AMOUNT AS REAL_AMOUNT,S.TOTAL_AMOUNT,
        S.STATUS,S.ADD_TIME,U.USERNAME AS OPT_USERNAME
        FROM T_SALEORDER S
        LEFT JOIN T_SALEORDER_GOODS G ON S.SALEORDER_ID=G.SALEORDER_ID
        LEFT JOIN T_USER U ON S.USER_ID=U.USER_ID
        LEFT JOIN T_QUOTEORDER Q ON S.QUOTEORDER_ID=Q.QUOTEORDER_ID
        WHERE 1=1 AND S.ORDER_TYPE =1 AND S.STATUS IN (0,1) AND (S.QUOTEORDER_ID IS NULL OR S.QUOTEORDER_ID=0 OR Q.BUSSINESS_CHANCE_ID =0)
        <if test="order.saleorderNo!=null and order.saleorderNo!=''">
            AND S.SALEORDER_NO=#{order.saleorderNo}
        </if>
        <if test="order.traderId!=null">
            AND S.TRADER_ID=#{order.traderId}
        </if>
        <if test="order.traderName!=null and order.traderName!=''">
            AND S.TRADER_NAME like CONCAT('%',#{order.traderName},'%')
        </if>
        <if test="order.traderContactName!=null and order.traderContactName!=''">
            AND S.TRADER_CONTACT_NAME =#{order.traderContactName}
        </if>
        <if test="order.traderContactMobile!=null and order.traderContactMobile!=''">
            AND S.TRADER_CONTACT_MOBILE=#{order.traderContactMobile}
        </if>
        <if test="order.productName!=null and order.productName!=''">
            AND G.GOODS_NAME LIKE CONCAT('%',#{order.productName},'%')
        </if>
        <if test="order.skuNo!=null and order.skuNo!=''">
            AND G.SKU LIKE CONCAT('%',#{order.skuNo},'%')
        </if>
        <if test="order.status!=null and order.status != -1">
            AND S.STATUS =#{order.status}
        </if>
        <if test="order.beginTime!=null">
            AND S.ADD_TIME <![CDATA[ >= ]]> #{order.beginTime}
        </if>
        <if test="order.beginTime!=null">
            AND S.ADD_TIME <![CDATA[ <= ]]> #{order.beginTime}
        </if>
        <if test="order.saleUserList != null and order.saleUserList.size() > 0">
            and S.USER_ID in
            <foreach collection="order.saleUserList" item="list" separator="," open="(" close=")">
                #{list.userId,jdbcType=INTEGER}
            </foreach>
        </if>
        GROUP BY S.SALEORDER_ID
    </select>
    <select id="getNoPayBackAmount" resultType="java.math.BigDecimal">
		SELECT COALESCE(
						   SUM(ABS(A.AMOUNT))
						   - IFNULL(C.HK_AMOUNT, 0)
						   - IFNULL(D.HK_AMOUNT, 0),
						   0)
							AS RELATEDDECIMAL,
			   B.RELATED_ID AS RELATEDID
		FROM T_CAPITAL_BILL A
				 LEFT JOIN T_CAPITAL_BILL_DETAIL B
						   ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
				 LEFT JOIN
			 (SELECT COALESCE(SUM(ABS(B1.AMOUNT)), 0) AS HK_AMOUNT, B1.RELATED_ID
			  FROM T_CAPITAL_BILL A1
					   LEFT JOIN T_CAPITAL_BILL_DETAIL B1
								 ON A1.CAPITAL_BILL_ID = B1.CAPITAL_BILL_ID
			  WHERE A1.TRADER_TYPE in (1, 4)
				AND B1.ORDER_TYPE = 1
				AND B1.BUSSINESS_TYPE = 533
				AND B1.RELATED_ID = #{orderId,jdbcType=INTEGER}
			  GROUP BY B1.RELATED_ID) AS C
			 ON B.RELATED_ID = C.RELATED_ID
				 LEFT JOIN
			 (SELECT COALESCE(SUM(ABS(B1.AMOUNT)), 0) AS HK_AMOUNT, C1.ORDER_ID
			  FROM T_CAPITAL_BILL A1
					   LEFT JOIN T_CAPITAL_BILL_DETAIL B1
								 ON A1.CAPITAL_BILL_ID = B1.CAPITAL_BILL_ID
					   LEFT JOIN T_AFTER_SALES C1
								 ON C1.AFTER_SALES_ID = B1.RELATED_ID AND C1.SUBJECT_TYPE = 535
			  WHERE A1.TRADER_TYPE = 3
				AND B1.ORDER_TYPE = 3
				AND B1.BUSSINESS_TYPE = 533
				and C1.ORDER_ID = #{orderId,jdbcType=INTEGER}
			  GROUP BY C1.ORDER_ID) AS D
			 ON D.ORDER_ID = B.RELATED_ID
		WHERE A.TRADER_TYPE = 3
		  AND A.TRADER_MODE = 527
		  AND B.ORDER_TYPE = 1
		  AND B.RELATED_ID = #{orderId,jdbcType=INTEGER}
		GROUP BY B.RELATED_ID
	</select>
	<select id="getManageCategoryOfSkuBySaleorderId" resultType="java.lang.Integer">
		SELECT DISTINCT
			R.MANAGE_CATEGORY_LEVEL
		FROM
			T_SALEORDER_GOODS SG
		JOIN V_CORE_SKU SKU ON SG.GOODS_ID = SKU.SKU_ID AND SG.IS_DELETE = 0
		JOIN V_CORE_SPU SPU ON SKU.SPU_ID = SPU.SPU_ID
		JOIN T_FIRST_ENGAGE F ON SPU.FIRST_ENGAGE_ID = F.FIRST_ENGAGE_ID
		JOIN T_REGISTRATION_NUMBER R ON F.REGISTRATION_NUMBER_ID = R.REGISTRATION_NUMBER_ID
		WHERE
			SG.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</select>
	<select id="getSaleorderByOrderNo" resultType="com.vedeng.order.model.Saleorder">
		SELECT * FROM T_SALEORDER WHERE SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
	</select>

	<select id="getSaleorderDataByOrderNo" resultType="com.vedeng.order.model.Saleorder">
		SELECT
		  TS.SALEORDER_ID,
		  TS.SALEORDER_NO,
		  TSD.CURRENT_USER_ID optUserId
		FROM T_SALEORDER TS
		 LEFT JOIN T_SALEORDER_DATA TSD	ON TS.SALEORDER_ID = TSD.SALEORDER_ID
		 WHERE SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
	</select>
    <select id="getSaleorderBySaleorderId" resultType="com.vedeng.order.model.Saleorder">
		SELECT * FROM T_SALEORDER WHERE SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</select>
    <select id="getSaleorderOfNeedUpdateContract" resultType="java.lang.Integer">
		SELECT DISTINCT
			S.SALEORDER_ID
		FROM
		( SELECT * FROM T_SALEORDER WHERE ORDER_TYPE IN ( 0, 1, 7, 8) AND `STATUS` = 1 AND CONTRACT_URL IS NOT NULL AND TRADER_ID =
		#{traderId,jdbcType=INTEGER} ) S
			LEFT JOIN ( SELECT * FROM T_ATTACHMENT WHERE ATTACHMENT_FUNCTION = 492 ) A ON S.SALEORDER_ID = A.RELATED_ID
		WHERE
			A.ATTACHMENT_ID IS NULL
	</select>
	<select id="getSaleorderIdByModifyApplyId" resultType="java.lang.Integer">
		SELECT SALEORDER_ID FROM T_SALEORDER_MODIFY_APPLY WHERE SALEORDER_MODIFY_APPLY_ID = #{saleorderModifyApplyId,jdbcType=INTEGER}
	</select>

    <select id="getSaleorderModifyApplyList" resultType="com.vedeng.order.model.SaleorderModifyApply">
		SELECT
		b.*, e.VERIFIES_TYPE, e.VERIFY_USERNAME, IFNULL(e.STATUS, -1) AS VERIFY_STATUS
		from T_SALEORDER_MODIFY_APPLY b
		LEFT JOIN T_VERIFIES_INFO e ON b.SALEORDER_MODIFY_APPLY_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_SALEORDER_MODIFY_APPLY'
		WHERE b.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
		order by b.ADD_TIME DESC
	</select>

	<select id="getUncheckedSkuCountOfSaleorder" resultType="java.lang.String">
		SELECT
			SG.SKU
		FROM
			T_SALEORDER_GOODS SG
		JOIN V_CORE_SKU SKU ON SG.SKU = SKU.SKU_NO
		WHERE
			SG.IS_DELETE = 0
			AND SKU.CHECK_STATUS != 3 AND SG.SALEORDER_ID = #{saleorderId，jdbcType=INTEGER}
	</select>

	<select id="getOneYearSkuSaleNumListPage" parameterType="java.util.Map" resultType="com.newtask.model.SkuSaleNum">
		select G.SKU AS SKU_NO,SUM(G.NUM-IFNULL(G.AFTER_RETURN_NUM,0)) AS SALE_NUM
		from T_SALEORDER_GOODS G left join T_SALEORDER S
		ON G.SALEORDER_ID=S.SALEORDER_ID
		where G.IS_DELETE=0
		AND S.`STATUS` IN (1,2)
		and S.PAYMENT_STATUS IN (1,2)
		and S.VALID_TIME>UNIX_TIMESTAMP(date_sub(curdate(),interval 1 YEAR))*1000
		GROUP BY G.SKU
	</select>
    <select id="getSalerOfSaleorderByOrderNo" resultType="com.vedeng.order.model.Saleorder">
		SELECT S.SALEORDER_ID, S.ORDER_TYPE, T.USER_ID AS CREATOR FROM T_SALEORDER S JOIN T_R_TRADER_J_USER T on S.TRADER_ID = T.TRADER_ID WHERE
				S.SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR} LIMIT 1
	</select>

    <update id="updateHcOrderDefaultValue" parameterType="com.vedeng.order.model.Saleorder">
		update T_SALEORDER
		<set >
			<if test="invoiceSendNode != null" >
				INVOICE_SEND_NODE = #{invoiceSendNode,jdbcType=INTEGER},
			</if>
			<if test="deliveryType != null" >
				DELIVERY_TYPE = #{deliveryType,jdbcType=INTEGER},
			</if>
		</set>
		where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</update>

	<select id="getSaleordergetVerifyStatus" resultType="java.lang.Integer" >
		SELECT b.STATUS
		FROM T_SALEORDER a
		left join T_VERIFIES_INFO b on a.SALEORDER_ID=b.RELATE_TABLE_KEY
		WHERE SALEORDER_ID = #{saleorderId,jdbcType=INTEGER} AND b.VERIFIES_TYPE=623
	</select>
	<select id="getFinishOrderStatus" resultType="com.vedeng.order.model.Saleorder">
		SELECT
			A.SALEORDER_ID,A.SALEORDER_NO
		FROM
			T_SALEORDER A
		WHERE
			A.`STATUS` = #{status,jdbcType=INTEGER}
		  AND A.IS_RISK = #{isRisk,jdbcType=INTEGER}
	</select>

	<select id="getLastWeekIdsByUpdateDateTime" resultType= "java.lang.Integer" >
		SELECT SALEORDER_ID FROM T_SALEORDER WHERE UPDATE_DATA_TIME > #{lastWeek} AND STATUS = 1;
	</select>

	<select id="getAllSaleOrderIds" resultType= "java.lang.Integer" >
		SELECT SALEORDER_ID FROM T_SALEORDER WHERE STATUS = 1 AND SALEORDER_ID > #{minId} limit #{limit} ;
	</select>

	<update id="clearBhOrderAuditInfo" parameterType="java.lang.Integer">
        UPDATE T_SALEORDER_GOODS A
        SET
			PRODUCT_AUDIT = 0,
			DIRECT_SUPERIOR_AUDIT = 0,
			PRODUCT_AUDIT_UESRID = 0
        WHERE IS_DELETE =0 and A.SALEORDER_ID = #{saleOrderId,jdbcType=INTEGER}
    </update>


	<sql id="jcSaleOrder_Where_Str">
		<if test="saleorder.companyId != null">
			<!-- 当前用户所属公司-->
			AND a.COMPANY_ID = #{saleorder.companyId,jdbcType=INTEGER}
		</if>
		<if test="saleorderIdsList!=null and saleorderIdsList.size()>0">
			<!-- 订单号列表(合同回传订单号回传查出来的订单） -->
			AND a.SALEORDER_ID IN
			<foreach collection="saleorderIdsList" index="index" item="saleorderId" open="(" separator="," close=")">
				#{saleorderId, jdbcType=INTEGER}
			</foreach>
		</if>
		<if test="saleorder.saleorderNo != null and saleorder.saleorderNo != ''">
			<!-- 订单号 -->
			AND a.SALEORDER_NO like CONCAT('%',#{saleorder.saleorderNo,jdbcType=VARCHAR},'%' )
		</if>
		<if test="saleorder.orderType != null">
			<!-- 订单类型 -->
			<if test="saleorder.orderType == -1 ">
				AND ( a.`ORDER_TYPE` IN ( 0,4 ) OR (a.`ORDER_TYPE` = 1 AND a.`VALID_STATUS` = 1 ))
			</if>
			<if test="saleorder.orderType != -1 and saleorder.orderType == 6">
				AND a.ORDER_TYPE = 6
			</if>
			<if test="saleorder.orderType != -1 and saleorder.orderType == 7">
				AND a.ORDER_TYPE = 7
			</if>
			<if test="saleorder.orderType != -1 and saleorder.orderType == 8">
				AND a.ORDER_TYPE = 8
			</if>
			<if test="saleorder.orderType == -3">
				AND a.ORDER_TYPE IN ( 7,8 )
			</if>
		</if>
		<if test="saleorder.traderName != null and saleorder.traderName != ''">
			<!-- 客户名称 -->
			AND (a.INVOICE_TRADER_NAME like CONCAT('%',#{saleorder.traderName,jdbcType=VARCHAR},'%' )
			OR tt.TRADER_NAME like CONCAT('%',#{saleorder.traderName,jdbcType=VARCHAR},'%' ))
		</if>
		<if test="saleorder.traderContact != null and saleorder.traderContact != ''">
			<!-- 联系人信息 -->
			AND (
			a.TRADER_CONTACT_NAME like CONCAT('%',#{saleorder.traderContact,jdbcType=VARCHAR},'%' )
			OR
			a.TRADER_CONTACT_MOBILE like CONCAT('%',#{saleorder.traderContact,jdbcType=VARCHAR},'%' )
			OR
			a.TRADER_CONTACT_TELEPHONE like CONCAT('%',#{saleorder.traderContact,jdbcType=VARCHAR},'%' )
			)
		</if>
		<if test="saleorder.status != null and saleorder.status !=-2">
			<!-- 订单状态 -->
			AND a.STATUS = #{saleorder.status,jdbcType=INTEGER}
		</if>
		<if test="(saleorder.verifyStatus != null and saleorder.verifyStatus != '' and saleorder.verifyStatus != 3) or saleorder.verifyStatus eq 0">
			<!-- 审核 -->
			AND e.STATUS = #{saleorder.verifyStatus}
		</if>
		<if test="saleorder.verifyStatus eq 3">
			<!-- 审核 -->
			AND e.STATUS is null
		</if>
		<if test="saleorder.validStatus != null and saleorder.validStatus != -1">
			<!-- 生效状态 -->
			AND a.VALID_STATUS = #{saleorder.validStatus,jdbcType=INTEGER}
		</if>
		<if test="saleorder.paymentStatus != null and saleorder.paymentStatus != -1">
			<!-- 收款状态 -->
			AND a.PAYMENT_STATUS = #{saleorder.paymentStatus,jdbcType=INTEGER}
		</if>
		<if test="saleorder.deliveryStatus != null and saleorder.deliveryStatus != -1">
			<!-- 发货状态 -->
			AND a.DELIVERY_STATUS = #{saleorder.deliveryStatus,jdbcType=INTEGER}
		</if>
		<if test="saleorder.arrivalStatus != null and saleorder.arrivalStatus != -1">
			<!-- 收货状态 -->
			AND a.ARRIVAL_STATUS = #{saleorder.arrivalStatus,jdbcType=INTEGER}
		</if>
		<if test="saleorder.serviceStatus != null and saleorder.serviceStatus != -1">
			<!-- 售后状态 -->
			AND a.SERVICE_STATUS = #{saleorder.serviceStatus,jdbcType=INTEGER}
		</if>
		<if test="saleorder.lockedStatus != null and saleorder.lockedStatus != -1">
			<!-- 锁定状态 -->
			AND a.LOCKED_STATUS = #{saleorder.lockedStatus,jdbcType=INTEGER}
		</if>
		<if test="saleorder.invoiceStatus != null and saleorder.invoiceStatus != -1">
			<!-- 开票状态 -->
			AND a.INVOICE_STATUS = #{saleorder.invoiceStatus,jdbcType=INTEGER}
		</if>
		<if test="saleorder.haveAdvancePurchase != null and saleorder.haveAdvancePurchase != -1">
			<!-- 提前采购 -->
			AND a.HAVE_ADVANCE_PURCHASE = #{saleorder.haveAdvancePurchase,jdbcType=INTEGER}
		</if>
		<if test="saleorder.deliveryDirect != null and saleorder.deliveryDirect != -1">
			<!-- 直发？ -->
			AND a.DELIVERY_DIRECT = #{saleorder.deliveryDirect,jdbcType=INTEGER}
		</if>
		<if test="saleorder.goodsName != null and saleorder.goodsName != ''">
			<!-- 产品名称 -->
			<!-- AND b.GOODS_NAME like CONCAT('%',#{saleorder.goodsName,jdbcType=VARCHAR},'%' )-->
			AND a.SALEORDER_ID IN (
			select SALEORDER_ID from T_SALEORDER_GOODS where GOODS_NAME like
			CONCAT('%',#{saleorder.goodsName,jdbcType=VARCHAR},'%' ) AND IS_DELETE = 0
			)
		</if>
		<if test="saleorder.sku != null and saleorder.sku != ''">
			<!-- 订货号 -->
			AND a.SALEORDER_ID IN (
			select SALEORDER_ID from T_SALEORDER_GOODS where SKU like CONCAT('%',#{saleorder.sku,jdbcType=VARCHAR},'%' )
			AND IS_DELETE = 0
			)
		</if>
		<if test="saleorder.brandName != null and saleorder.brandName != ''">
			<!-- 品牌 -->
			<!-- AND b.BRAND_NAME like CONCAT('%',#{saleorder.brandName,jdbcType=VARCHAR},'%' )-->
			AND a.SALEORDER_ID IN (
			select SALEORDER_ID from T_SALEORDER_GOODS where BRAND_NAME like
			CONCAT('%',#{saleorder.brandName,jdbcType=VARCHAR},'%' ) AND IS_DELETE = 0
			)
		</if>
		<if test="saleorder.model != null and saleorder.model != ''">
			<!-- 型号 -->
			<!-- AND b.MODEL like CONCAT('%',#{saleorder.model,jdbcType=VARCHAR},'%' )-->
			AND a.SALEORDER_ID IN (
			select SALEORDER_ID from T_SALEORDER_GOODS where MODEL like
			CONCAT('%',#{saleorder.model,jdbcType=VARCHAR},'%' ) AND IS_DELETE = 0
			)
		</if>
		<if test="saleorder.saleUserList != null and saleorder.saleUserList.size() > 0">
			<!-- 订单归属销售-->
			AND f.TRADER_TYPE = 1 and f.USER_ID in
			<foreach collection="saleorder.saleUserList" item="list" separator="," open="(" close=")">
				#{list.userId,jdbcType=INTEGER}
			</foreach>
		</if>
		<if test="saleorder.keyIds != null and saleorder.keyIds.size > 0">
			<!-- 账期未还-->
			AND a.SALEORDER_ID IN
			<foreach collection="saleorder.keyIds" item="saleorderId" close=")" open="(" separator=",">
				#{saleorderId,jdbcType=INTEGER}
			</foreach>
		</if>
		<!--时间搜索-->
		<if test="saleorder.searchDateType !=null and saleorder.searchDateType != ''">
			<choose>
				<when test="saleorder.searchDateType == 1"><!-- 创建时间 -->
					<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
						AND a.ADD_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
					</if>
					<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
						AND a.ADD_TIME > 0
						AND a.ADD_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
					</if>
				</when>
				<when test="saleorder.searchDateType == 2"><!-- 生效时间 -->
					<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
						AND a.VALID_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
					</if>
					<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
						AND a.VALID_TIME > 0
						AND a.VALID_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
					</if>
				</when>
				<when test="saleorder.searchDateType == 3"><!-- 付款时间 -->
					<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
						AND a.PAYMENT_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
					</if>
					<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
						AND a.PAYMENT_TIME > 0
						AND a.PAYMENT_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
					</if>
				</when>
				<when test="saleorder.searchDateType == 4"><!-- 发货时间 -->
					<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
						AND a.DELIVERY_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
					</if>
					<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
						AND a.DELIVERY_TIME > 0
						AND a.DELIVERY_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
					</if>
				</when>
				<when test="saleorder.searchDateType == 5"><!-- 收货时间 -->
					<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
						AND a.ARRIVAL_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
					</if>
					<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
						AND a.ARRIVAL_TIME > 0
						AND a.ARRIVAL_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
					</if>
				</when>
				<when test="saleorder.searchDateType == 6"><!-- 开票时间 -->
					<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
						AND a.INVOICE_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
					</if>
					<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
						AND a.INVOICE_TIME > 0
						AND a.INVOICE_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
					</if>
				</when>
				<otherwise>
				</otherwise>
			</choose>
		</if>


		<if test="saleorder.isReferenceCostPrice != null and saleorder.isReferenceCostPrice != -1">
			<!-- 待填 -->
			<if test="saleorder.isReferenceCostPrice == 0">
				AND (
				a.PAYMENT_STATUS = 2 AND a.STATUS != 3 AND
				(
				SELECT COUNT(*)
				FROM T_SALEORDER_GOODS g
				WHERE SALEORDER_ID = a.SALEORDER_ID AND IS_DELETE = 0  AND `REFERENCE_COST_PRICE` = 0
				AND
				(
				NOT EXISTS (
				SELECT SUM(F.NUM)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID)
				OR  (
				SELECT g.`NUM`- IFNULL(SUM(F.NUM),0)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID
				) > 0
				)
				) > 0
				)
			</if>

			<!-- 已填 -->
			<if test="saleorder.isReferenceCostPrice == 1">
				AND
				(
				SELECT COUNT(*)
				FROM T_SALEORDER_GOODS g
				WHERE SALEORDER_ID = a.SALEORDER_ID AND IS_DELETE = 0  AND `REFERENCE_COST_PRICE` = 0
				AND
				(
				NOT EXISTS (
				SELECT SUM(F.NUM)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID)
				OR  (
				SELECT g.`NUM`- IFNULL(SUM(F.NUM),0)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID
				) > 0
				)
				) = 0
			</if>

			<!-- 无需填写 -->
			<if test="saleorder.isReferenceCostPrice == 2">
				AND !(
				(
				a.PAYMENT_STATUS = 2 AND a.STATUS != 3 AND
				(
				SELECT COUNT(*)
				FROM T_SALEORDER_GOODS g
				WHERE SALEORDER_ID = a.SALEORDER_ID AND IS_DELETE = 0  AND `REFERENCE_COST_PRICE` = 0
				AND
				(
				NOT EXISTS (
				SELECT SUM(F.NUM)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID)
				OR  (
				SELECT g.`NUM`- IFNULL(SUM(F.NUM),0)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID
				) > 0
				)
				) > 0
				)
				or
				(
				(
				SELECT COUNT(*)
				FROM T_SALEORDER_GOODS g
				WHERE SALEORDER_ID = a.SALEORDER_ID AND IS_DELETE = 0  AND `REFERENCE_COST_PRICE` = 0
				AND
				(
				NOT EXISTS (
				SELECT SUM(F.NUM)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID)
				OR  (
				SELECT g.`NUM`- IFNULL(SUM(F.NUM),0)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID
				) > 0
				)
				) = 0
				)
				)
			</if>
		</if>
	</sql>

    <select id="queryWhetherMaintain" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        SELECT COUNT(*)
				FROM T_SALEORDER_GOODS g
				WHERE SALEORDER_ID = #{saleorderId} AND IS_DELETE = 0  AND REFERENCE_COST_PRICE = 0
				AND
				(
				NOT EXISTS (
				SELECT SUM(F.NUM)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = #{saleorderId}
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID)
				OR  (
				SELECT g.`NUM`- IFNULL(SUM(F.NUM),0)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = #{saleorderId}
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID
				) > 0
				)
    </select>


	<!-- 查询集采订单列表（分页） -->
	<select id="getJcOrderListPage" resultMap="BaseResultMap" parameterType="Map">
		select
		a.*, c.QUOTEORDER_NO, d.BUSSINESS_CHANCE_ID, d.BUSSINESS_CHANCE_NO, e.VERIFIES_TYPE, e.VERIFY_USERNAME,
		IFNULL(e.STATUS, -1) AS VERIFY_STATUS
		from T_SALEORDER a
		LEFT JOIN T_QUOTEORDER c ON a.QUOTEORDER_ID = c.QUOTEORDER_ID
		LEFT JOIN T_BUSSINESS_CHANCE d ON c.BUSSINESS_CHANCE_ID = d.BUSSINESS_CHANCE_ID
		LEFT JOIN T_TRADER_CUSTOMER tc ON a.GROUP_CUSTOMER_ID = tc.TRADER_ID
		LEFT JOIN T_TRADER tt ON tt.TRADER_ID = tc.TRADER_ID
		LEFT JOIN T_VERIFIES_INFO e ON a.SALEORDER_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_SALEORDER' AND
		e.VERIFIES_TYPE = 623
		LEFT JOIN T_VERIFIES_INFO con ON a.SALEORDER_ID = con.RELATE_TABLE_KEY AND con.RELATE_TABLE = 'T_SALEORDER' AND
		con.VERIFIES_TYPE = 868
		<if test="saleorder.saleUserList != null and saleorder.saleUserList.size() > 0">
			LEFT JOIN T_R_TRADER_J_USER f on a.TRADER_ID = f.TRADER_ID
		</if>

		<where>
			1=1
			<include refid="jcSaleOrder_Where_Str"/>
			<!--开票状态与提前开票状态应该都为待审核-->
			<if test="saleorder.openInvoiceApply != null and saleorder.openInvoiceApply == 1">
				and
				exists
				(select 1 from
				T_INVOICE_APPLY g
				where a.SALEORDER_ID = g.RELATED_ID
				and g.TYPE = 505
				and (g.VALID_STATUS = 0 and g.ADVANCE_VALID_STATUS!=2)  )
			</if>

		</where>
		order by
		ADD_TIME desc
	</select>

	<!-- 合同回传-->
	<select id="getSaleorderReturnOrNotList" resultType="java.lang.Integer" parameterType="Map">
		SELECT ts.SALEORDER_ID FROM T_SALEORDER ts
		LEFT JOIN T_ATTACHMENT ta ON ts.SALEORDER_ID=ta.RELATED_ID
		<if test="saleorder.saleUserList != null and saleorder.saleUserList.size() > 0">
			LEFT JOIN T_R_TRADER_J_USER trj ON ts.TRADER_ID = trj.TRADER_ID
		</if>
		WHERE 1=1
		<if test="type eq 1">
			AND ta.ATTACHMENT_FUNCTION = 492
			<!-- <if test="saleorder.isContractReturn eq 1">

            </if>
            <if test="saleorder.isContractReturn eq 0">
                AND ta.ATTACHMENT_FUNCTION != 492
            </if> -->
		</if>
		<if test="type eq 2">
			AND ta.ATTACHMENT_FUNCTION = 493
			<!-- <if test="saleorder.isDeliveryOrderReturn eq 1">
                AND ta.ATTACHMENT_FUNCTION = 493
            </if>
            <if test="saleorder.isDeliveryOrderReturn eq 0">
                AND ta.ATTACHMENT_FUNCTION != 493
            </if> -->
		</if>
		<if test="saleorder.companyId!=null">
			AND ts.COMPANY_ID=#{saleorder.companyId,jdbcType=INTEGER}
		</if>
		<if test="saleorder.saleUserList != null and saleorder.saleUserList.size() > 0">
			AND trj.TRADER_TYPE = 1 and trj.USER_ID in
			<foreach collection="saleorder.saleUserList" item="list" separator="," open="(" close=")">
				#{list.userId,jdbcType=INTEGER}
			</foreach>
		</if>
	</select>

	<!--账期未还-->
	<select id="getLackAccountPeriodOrderSaleOrderId" resultType="java.lang.Integer">
  	SELECT P.RELATED_ID
	FROM (SELECT B.RELATED_ID,
	             COALESCE(SUM(ABS(A.AMOUNT)) - IFNULL(C.HK_AMOUNT, 0) - IFNULL(D.HK_AMOUNT, 0), 0) AS AMOUNT
	      FROM T_CAPITAL_BILL A
	           LEFT JOIN T_CAPITAL_BILL_DETAIL B
	              ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
	           LEFT JOIN
	           (SELECT COALESCE(SUM(ABS(B1.AMOUNT)), 0) AS HK_AMOUNT, B1.RELATED_ID
	            FROM T_CAPITAL_BILL A1
	                 LEFT JOIN T_CAPITAL_BILL_DETAIL B1
	                    ON A1.CAPITAL_BILL_ID = B1.CAPITAL_BILL_ID
	            WHERE     A1.TRADER_TYPE in (1,4)
	                  AND B1.ORDER_TYPE = 1
	                  AND B1.BUSSINESS_TYPE = 533
	            GROUP BY B1.RELATED_ID) AS C
	              ON B.RELATED_ID = C.RELATED_ID
	           LEFT JOIN
	           (SELECT COALESCE(SUM(ABS(B1.AMOUNT)), 0) AS HK_AMOUNT, C1.ORDER_ID
	            FROM T_CAPITAL_BILL A1
	                 LEFT JOIN T_CAPITAL_BILL_DETAIL B1
	                    ON A1.CAPITAL_BILL_ID = B1.CAPITAL_BILL_ID
	                 LEFT JOIN T_AFTER_SALES C1
	                    ON     C1.AFTER_SALES_ID = B1.RELATED_ID
	                       AND C1.SUBJECT_TYPE = 535
	            WHERE     A1.TRADER_TYPE = 3
	                  AND B1.ORDER_TYPE = 3
	                  AND B1.BUSSINESS_TYPE = 533
	            GROUP BY C1.ORDER_ID) AS D
	              ON D.ORDER_ID = B.RELATED_ID
	      WHERE A.TRADER_TYPE = 3 AND A.TRADER_MODE = 527 AND B.ORDER_TYPE = 1
	      GROUP BY B.RELATED_ID) P
	WHERE P.AMOUNT > 0
  </select>

	<!-- 集采销售订单总数 -->
	<select id="getSaleorderListCount" resultType="Map" parameterType="Map">
		select
		count(*) as total_count,
		COALESCE(sum(COALESCE(a.TOTAL_AMOUNT, 0)), 0) as total_amount
		from T_SALEORDER a
		LEFT JOIN T_TRADER_CUSTOMER tc ON a.GROUP_CUSTOMER_ID = tc.TRADER_ID
		LEFT JOIN T_TRADER tt ON tt.TRADER_ID = tc.TRADER_ID
		LEFT JOIN T_VERIFIES_INFO e ON a.SALEORDER_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_SALEORDER' AND
		e.VERIFIES_TYPE = 623
		LEFT JOIN T_VERIFIES_INFO con ON a.SALEORDER_ID = con.RELATE_TABLE_KEY AND con.RELATE_TABLE = 'T_SALEORDER' AND
		con.VERIFIES_TYPE = 868
		<if test="saleorder.saleUserList != null and saleorder.saleUserList.size() > 0">
			LEFT JOIN T_R_TRADER_J_USER f on a.TRADER_ID = f.TRADER_ID
		</if>
		<where>
			1=1
			<include refid="jcSaleOrder_Where_Str"/>
			<!--开票状态与提前开票状态应该都为待审核-->
			<if test="saleorder.openInvoiceApply != null and saleorder.openInvoiceApply == 1">
				and
				exists
				(select 1 from
				T_INVOICE_APPLY g
				where a.SALEORDER_ID = g.RELATED_ID
				and g.TYPE = 505
				and (g.VALID_STATUS = 0 and g.ADVANCE_VALID_STATUS!=2) )
			</if>

			<if test="saleorder.productBelongUserId != null and saleorder.productBelongUserId != -1">
				and a.SALEORDER_ID in
				<choose>
					<when test="couponSaleOrderIds == null or couponSaleOrderIds.size == 0">
						(null)
					</when>
					<when test="couponSaleOrderIds.size > 0">
						<foreach collection="couponSaleOrderIds" item="item" open="(" close=")"  separator=",">
							${item}
						</foreach>
					</when>
				</choose>
			</if>

			<if test="saleorder.couponType != null and saleorder.couponType != -1">
				and a.SALEORDER_ID in
				<choose>
					<when test="productSaleIds == null or productSaleIds.size == 0">
						(null)
					</when>
					<when test="productSaleIds.size > 0">
						<foreach collection="productSaleIds" item="item" open="(" close=")"  separator=",">
							${item}
						</foreach>
					</when>
				</choose>
			</if>
			<if test="saleorder.orderType != null">
				AND a.ORDER_TYPE = #{saleorder.orderType,jdbcType=INTEGER}
			</if>
		</where>
<!--		select-->
<!--		count(*) as total_count-->
<!--		from T_SALEORDER a-->
<!--		LEFT JOIN T_VERIFIES_INFO e ON a.SALEORDER_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_SALEORDER' AND-->
<!--		e.VERIFIES_TYPE = 623-->
<!--		LEFT JOIN T_VERIFIES_INFO con ON a.SALEORDER_ID = con.RELATE_TABLE_KEY AND con.RELATE_TABLE = 'T_SALEORDER' AND-->
<!--		con.VERIFIES_TYPE = 868-->
<!--		<if test="saleorder.saleUserList != null and saleorder.saleUserList.size() > 0">-->
<!--			LEFT JOIN T_R_TRADER_J_USER f on a.TRADER_ID = f.TRADER_ID-->
<!--		</if>-->

<!--		<if test="saleorder.consultStatus != null and saleorder.consultStatus > 0">-->
<!--			LEFT JOIN T_QUOTEORDER q ON a.QUOTEORDER_ID = q.QUOTEORDER_ID-->
<!--		</if>-->

<!--		<where>-->
<!--		1=1-->
<!--		<include refid="jcSaleOrder_Where_Str"/>-->
<!--		&lt;!&ndash;开票状态与提前开票状态应该都为待审核&ndash;&gt;-->
<!--		<if test="saleorder.openInvoiceApply != null and saleorder.openInvoiceApply == 1">-->
<!--			and-->
<!--			exists-->
<!--			(select 1 from-->
<!--			T_INVOICE_APPLY g-->
<!--			where a.SALEORDER_ID = g.RELATED_ID-->
<!--			and g.TYPE = 505-->
<!--			and (g.VALID_STATUS = 0 and g.ADVANCE_VALID_STATUS!=2)  )-->
<!--		</if>-->
<!--		</where>-->

	</select>

    <select id="getSaleorderListSum" resultType="java.math.BigDecimal">
		SELECT SUM(p.TOTAL_AMOUNT) AS allTotalAmount
		FROM (
		select a.TOTAL_AMOUNT
		from T_SALEORDER a
		LEFT JOIN T_SALEORDER_GOODS b ON a.SALEORDER_ID = b.SALEORDER_ID
		<where>
			1=1
			<if test="saleorder.companyId != null">
				AND a.COMPANY_ID = #{saleorder.companyId,jdbcType=INTEGER}
			</if>
			<if test="saleorder.traderId != null">
				<!-- 客户 -->
				AND a.TRADER_ID = #{saleorder.traderId,jdbcType=INTEGER}
			</if>
			<if test="saleorder.orderType != null">
				<!-- 订单状态 -->
				 AND a.ORDER_TYPE = #{saleorder.orderType,jdbcType=INTEGER}
			</if>
			<if test="saleorder.saleorderNo != null and saleorder.saleorderNo != ''">
				<!-- 订单号 -->
				AND a.SALEORDER_NO like CONCAT('%',#{saleorder.saleorderNo,jdbcType=VARCHAR},'%' )
			</if>
			<if test="saleorder.status != null and saleorder.status != -1">
				<!-- 订单状态 -->
				AND a.STATUS = #{saleorder.status,jdbcType=INTEGER}
			</if>
			<if test="saleorder.validStatus != null and saleorder.validStatus != -1">
				<!-- 生效状态 -->
				AND a.VALID_STATUS = #{saleorder.validStatus,jdbcType=INTEGER}
			</if>
			<if test="saleorder.paymentStatus != null and saleorder.paymentStatus != -1">
				<!-- 付款状态 -->
				AND a.PAYMENT_STATUS = #{saleorder.paymentStatus,jdbcType=INTEGER}
			</if>
			<if test="saleorder.deliveryStatus != null and saleorder.deliveryStatus != -1">
				<!-- 发货状态 -->
				AND a.DELIVERY_STATUS = #{saleorder.deliveryStatus,jdbcType=INTEGER}
			</if>
			<if test="saleorder.arrivalStatus != null and saleorder.arrivalStatus != -1">
				<!-- 收货状态 -->
				AND a.ARRIVAL_STATUS = #{saleorder.arrivalStatus,jdbcType=INTEGER}
			</if>
			<if test="saleorder.invoiceType != null and saleorder.invoiceType != -1">
				AND a.INVOICE_TYPE = #{saleorder.invoiceType,jdbcType=INTEGER}
			</if>
			<if test="saleorder.invoiceStatus != null and saleorder.invoiceStatus != -1">
				<!-- 开票状态 -->
				AND a.INVOICE_STATUS = #{saleorder.invoiceStatus,jdbcType=INTEGER}
			</if>
			<if test="saleorder.serviceStatus != null and saleorder.serviceStatus != -1">
				<!-- 售后状态 -->
				AND a.SERVICE_STATUS = #{saleorder.serviceStatus,jdbcType=INTEGER}
			</if>
			<if test="saleorder.lockedStatus != null and saleorder.lockedStatus != -1">
				<!-- 锁定状态 -->
				AND a.LOCKED_STATUS = #{saleorder.lockedStatus,jdbcType=INTEGER}
			</if>
			<if test="saleorder.traderName != null and saleorder.traderName != ''">
				<!-- 客户名称 -->
				AND a.TRADER_NAME like CONCAT('%',#{saleorder.traderName,jdbcType=VARCHAR},'%' )
			</if>
			<if test="saleorder.customerNature != null and saleorder.customerNature != -1">
				<!-- 客户性质 -->
				AND a.CUSTOMER_NATURE = #{saleorder.customerNature,jdbcType=INTEGER}
			</if>
			<if test="saleorder.traderContact != null and saleorder.traderContact != ''">
				<!-- 联系人信息 -->
				AND (
				a.TRADER_CONTACT_NAME like CONCAT('%',#{saleorder.traderContact,jdbcType=VARCHAR},'%' )
				OR
				a.TRADER_CONTACT_MOBILE like CONCAT('%',#{saleorder.traderContact,jdbcType=VARCHAR},'%' )
				OR
				a.TRADER_CONTACT_TELEPHONE like CONCAT('%',#{saleorder.traderContact,jdbcType=VARCHAR},'%' )
				)
			</if>
			<if test="saleorder.goodsName != null and saleorder.goodsName != ''">
				<!-- 产品名称 -->
				AND b.GOODS_NAME like CONCAT('%',#{saleorder.goodsName,jdbcType=VARCHAR},'%' )
			</if>
			<if test="saleorder.brandName != null and saleorder.brandName != ''">
				<!-- 品牌 -->
				AND b.BRAND_NAME like CONCAT('%',#{saleorder.brandName,jdbcType=VARCHAR},'%' )
			</if>
			<if test="saleorder.model != null and saleorder.model != ''">
				<!-- 型号 -->
				AND b.MODEL like CONCAT('%',#{saleorder.model,jdbcType=VARCHAR},'%' )
			</if>
			<if test="saleorder.sku != null and saleorder.sku != ''">
				<!-- 订货号 -->
				AND b.SKU like CONCAT('%',#{saleorder.sku,jdbcType=VARCHAR},'%' )
			</if>
			<if test="saleorder.orgId != null and saleorder.orgId != -1">
				<!-- 销售部门 -->
				AND a.ORG_ID = #{saleorder.orgId,jdbcType=INTEGER}
			</if>
			<!-- 客户ID -->
			<if test="saleorder.traderIdList != null and saleorder.traderIdList.size > 0">
				AND a.TRADER_ID IN
				<foreach collection="saleorder.traderIdList" item="traderId" separator="," open="(" close=")">
					#{traderId,jdbcType=INTEGER}
				</foreach>
			</if>
			<if test="saleorder.deliveryDirect != null and saleorder.deliveryDirect != -1">
				<!-- 直发？ -->
				AND a.DELIVERY_DIRECT = #{saleorder.deliveryDirect,jdbcType=INTEGER}
			</if>
			<if test="saleorder.haveAdvancePurchase != null and saleorder.haveAdvancePurchase != -1">
				<!-- 提前采购 -->
				AND a.HAVE_ADVANCE_PURCHASE = #{saleorder.haveAdvancePurchase,jdbcType=INTEGER}
			</if>
			<if test="saleorder.haveCommunicate != null and saleorder.haveCommunicate != -1">
				<!-- 有沟通 -->
				AND a.HAVE_COMMUNICATE = #{saleorder.haveCommunicate,jdbcType=INTEGER}
			</if>
			<if test="saleorder.creator != null and saleorder.creator != -1">
				<!-- 申请人 -->
				AND a.CREATOR = #{saleorder.creator,jdbcType=INTEGER}
			</if>
			<if test="saleorder.searchDateType !=null and saleorder.searchDateType != ''">
				<choose>
					<when test="saleorder.searchDateType == 1"><!-- 创建时间 -->
						<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
							AND a.ADD_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
						</if>
						<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
							AND a.ADD_TIME > 0
							and a.ADD_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
						</if>
					</when>
					<when test="saleorder.searchDateType == 2"><!-- 生效时间 -->
						<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
							AND a.VALID_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
						</if>
						<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
							and a.VALID_TIME > 0
							and a.VALID_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
						</if>
					</when>
					<when test="saleorder.searchDateType == 3"><!-- 付款时间 -->
						<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
							AND a.PAYMENT_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
						</if>
						<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
							and a.PAYMENT_TIME > 0
							and a.PAYMENT_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
						</if>
					</when>
					<when test="saleorder.searchDateType == 4"><!-- 发货时间 -->
						<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
							AND a.DELIVERY_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
						</if>
						<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
							and a.DELIVERY_TIME > 0
							and a.DELIVERY_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
						</if>
					</when>
					<when test="saleorder.searchDateType == 5"><!-- 收货时间 -->
						<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
							AND a.ARRIVAL_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
						</if>
						<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
							and a.ARRIVAL_TIME > 0
							and a.ARRIVAL_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
						</if>
					</when>
					<when test="saleorder.searchDateType == 6"><!-- 开票时间 -->
						<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
							AND a.INVOICE_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
						</if>
						<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
							and a.INVOICE_TIME > 0
							and a.INVOICE_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
						</if>
					</when>
					<otherwise>
					</otherwise>
				</choose>
			</if>
			<!-- 订单总额 -->
			<if test="saleorder.startAmount != null">
				AND a.TOTAL_AMOUNT <![CDATA[ >= ]]> #{saleorder.startAmount,jdbcType=DECIMAL}
			</if>
			<if test="saleorder.endAmount != null">
				AND a.TOTAL_AMOUNT <![CDATA[ <= ]]> #{saleorder.endAmount,jdbcType=DECIMAL}
			</if>
			<if test="saleorder.keyIds != null and saleorder.keyIds.size > 0">
				AND a.SALEORDER_ID IN
				<foreach collection="saleorder.keyIds" item="saleorderId" close=")" open="(" separator=",">
					#{saleorderId,jdbcType=INTEGER}
				</foreach>
			</if>
		</where>
		GROUP BY a.SALEORDER_ID
		) p
	</select>

	<select id="getSaleorderIdListBelongtoProductRole" resultType="java.lang.Integer">
		SELECT DISTINCT(t.SALEORDER_ID)
		FROM T_SALEORDER t
		LEFT JOIN T_SALEORDER_GOODS g ON t.`SALEORDER_ID` = g.`SALEORDER_ID`
		LEFT JOIN V_CORE_SKU sku ON sku.SKU_NO = g.`SKU`
		LEFT JOIN V_CORE_SPU spu ON spu.SPU_ID = sku.SPU_ID
		<where>
			g.IS_DELETE = 0
			<if test="productBelongUserId != null">
				and ( ASSIGNMENT_MANAGER_ID = #{productBelongUserId,jdbcType=INTEGER} OR ASSIGNMENT_ASSISTANT_ID = #{productBelongUserId,jdbcType=INTEGER} )
			</if>
		</where>
		order by t.SALEORDER_ID desc limit 1000
	</select>

	<!-- 销售订单总数和总金额数 -->
	<select id="getSaleorderListCountAndTotalCount" resultType="Map" parameterType="Map">
		select
		count(*) as total_count,
		COALESCE(sum(COALESCE(a.TOTAL_AMOUNT, 0)), 0) as total_amount
		from T_SALEORDER a
		LEFT JOIN T_VERIFIES_INFO e ON a.SALEORDER_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_SALEORDER' AND
		e.VERIFIES_TYPE = 623
		LEFT JOIN T_VERIFIES_INFO con ON a.SALEORDER_ID = con.RELATE_TABLE_KEY AND con.RELATE_TABLE = 'T_SALEORDER' AND
		con.VERIFIES_TYPE = 868
		<if test="saleorder.saleUserList != null and saleorder.saleUserList.size() > 0">
			LEFT JOIN T_R_TRADER_J_USER f on a.TRADER_ID = f.TRADER_ID
		</if>
		<where>
			1=1
			<if test="saleorder.isConfirmTime!=null and saleorder.isConfirmTime==0">
				AND a.CONFIRM_TIME is null
			</if>

			<if test="saleorder.isConfirmTime!=null and saleorder.isConfirmTime==1">
				AND a.CONFIRM_TIME is not  null
			</if>
			<include refid="Saleorder_Where_Str"/>
			<!--开票状态与提前开票状态应该都为待审核-->
			<if test="saleorder.openInvoiceApply != null and saleorder.openInvoiceApply == 1">
				and
				exists
				(select 1 from
				T_INVOICE_APPLY g
				where a.SALEORDER_ID = g.RELATED_ID
				and g.TYPE = 505
				and (g.VALID_STATUS = 0 and g.ADVANCE_VALID_STATUS!=2) )
			</if>

			<if test="saleorder.productBelongUserId != null and saleorder.productBelongUserId != -1">
				and a.SALEORDER_ID in
				<choose>
					<when test="couponSaleOrderIds == null or couponSaleOrderIds.size == 0">
						(null)
					</when>
					<when test="couponSaleOrderIds.size > 0">
						<foreach collection="couponSaleOrderIds" item="item" open="(" close=")"  separator=",">
							${item}
						</foreach>
					</when>
				</choose>
			</if>

			<if test="saleorder.couponType != null and saleorder.couponType != -1">
				and a.SALEORDER_ID in
				<choose>
					<when test="productSaleIds == null or productSaleIds.size == 0">
						(null)
					</when>
					<when test="productSaleIds.size > 0">
						<foreach collection="productSaleIds" item="item" open="(" close=")"  separator=",">
							${item}
						</foreach>
					</when>
				</choose>
			</if>
			<if test="saleorder.orderType != null">
				AND a.ORDER_TYPE = #{saleorder.orderType,jdbcType=INTEGER}
			</if>
		</where>
	</select>

	<sql id="Saleorder_Where_Str">
		and a.company_id=1
		<if test="saleorderIdsList!=null and saleorderIdsList.size()>0">
			AND a.SALEORDER_ID IN
			<foreach collection="saleorderIdsList" index="index" item="saleorderId" open="(" separator="," close=")">
				#{saleorderId, jdbcType=INTEGER}
			</foreach>
		</if>
		<if test="saleorder.orderType != null">
			<if test="saleorder.orderType == -2">
				AND a.ORDER_TYPE <![CDATA[ <> ]]>  2
			</if>
			<!-- 订单类型 -->
			<if test="saleorder.orderType == -1 ">
				<!--  AND (a.ORDER_TYPE <![CDATA[ <> ]]>  2 OR (a.`ORDER_TYPE` = 1 AND a.`VALID_STATUS` = 1)) -->
				AND ( a.`ORDER_TYPE` IN ( 0,4 ) OR (a.`ORDER_TYPE` = 1 AND a.`VALID_STATUS` = 1 ))
			</if>
			<if test="saleorder.orderType != -1 and saleorder.orderType == 0">
				AND a.ORDER_TYPE = 0
			</if>
			<if test="saleorder.orderType != -1 and saleorder.orderType == 1">
				AND a.ORDER_TYPE = 1 AND a.`VALID_STATUS`= 1
			</if>
			<if test="saleorder.orderType != -1 and (saleorder.orderType == 3 or saleorder.orderType == 2 or saleorder.orderType == 5)">
				AND a.ORDER_TYPE = #{saleorder.orderType,jdbcType=INTEGER}
			</if>
			<if test="saleorder.orderType != -1 and saleorder.orderType == 6">
				AND a.`SOURCE`= 3 AND a.`ADK_SALEORDER_NO` IS NOT NULL
			</if>
			<if test="saleorder.orderType != -1 and saleorder.orderType == 4">
				AND a.ORDER_TYPE = 4
			</if>
		</if>
		<if test="saleorder.isSalesPerformance != null and saleorder.isSalesPerformance != -1 ">
			AND a.IS_SALES_PERFORMANCE = #{saleorder.isSalesPerformance, jdbcType=INTEGER}
		</if>
		<if test="saleorder.companyId != null">
			AND a.COMPANY_ID = #{saleorder.companyId,jdbcType=INTEGER}
		</if>
		<if test="saleorder.traderId != null">
			<!-- 客户 -->
			AND a.TRADER_ID = #{saleorder.traderId,jdbcType=INTEGER}
		</if>
		<if test="(saleorder.verifyStatus != null and saleorder.verifyStatus != '' and saleorder.verifyStatus != 3) or saleorder.verifyStatus eq 0">
			<!-- 审核 -->
			AND e.STATUS = #{saleorder.verifyStatus}
		</if>
		<if test="saleorder.verifyStatus eq 3">
			<!-- 审核 -->
			AND e.STATUS is null
		</if>
		<if test="(saleorder.contractStatus != null and saleorder.contractStatus != '' and saleorder.contractStatus != 3 and saleorder.contractStatus != -1) or saleorder.contractStatus eq 0">
			<!-- 合同审核 -->
			AND con.STATUS = #{saleorder.contractStatus}
		</if>
		<if test="saleorder.contractStatus eq 3">
			<!-- 合同审核 -->
			AND con.STATUS is null AND (a.HAVE_ACCOUNT_PERIOD=1 OR a.TOTAL_AMOUNT&gt;=10000) AND (a.VALID_TIME/1000)&gt;=UNIX_TIMESTAMP('2018-01-01')
		</if>

		<!-- if test="saleorder.orderType == null">
            AND (a.ORDER_TYPE <![CDATA[ <> ]]>  2 OR (a.`ORDER_TYPE` = 1 AND a.`VALID_STATUS` = 1))
            AND  a.ORDER_TYPE  NOT IN (a.ORDER_TYPE  <![CDATA[ <> ]]>  2  AND a.`ORDER_TYPE` = 1 AND a.`VALID_STATUS`= 0)
        </if> -->
		<if test="saleorder.saleorderNo != null and saleorder.saleorderNo != ''">
			<!-- 订单号 -->
			AND a.SALEORDER_NO like CONCAT('%',#{saleorder.saleorderNo,jdbcType=VARCHAR},'%' )
		</if>
		<if test="saleorder.status != null and saleorder.status != -2">
			<!-- 订单状态 -->
			AND a.STATUS = #{saleorder.status,jdbcType=INTEGER}
		</if>
		<if test="saleorder.validStatus != null and saleorder.validStatus != -1">
			<!-- 生效状态 -->
			AND a.VALID_STATUS = #{saleorder.validStatus,jdbcType=INTEGER}
		</if>
		<if test="saleorder.paymentStatus != null and saleorder.paymentStatus != -1">
			<!-- 付款状态 -->
			AND a.PAYMENT_STATUS = #{saleorder.paymentStatus,jdbcType=INTEGER}
		</if>
		<if test="saleorder.deliveryStatus != null and saleorder.deliveryStatus != -1">
			<!-- 发货状态 -->
			AND a.DELIVERY_STATUS = #{saleorder.deliveryStatus,jdbcType=INTEGER}
		</if>
		<if test="saleorder.arrivalStatus != null and saleorder.arrivalStatus != -1">
			<!-- 收货状态 -->
			AND a.ARRIVAL_STATUS = #{saleorder.arrivalStatus,jdbcType=INTEGER}
		</if>
		<if test="saleorder.invoiceType != null and saleorder.invoiceType != -1">
			AND a.INVOICE_TYPE = #{saleorder.invoiceType,jdbcType=INTEGER}
		</if>

		<if test="saleorder.couponType != null and saleorder.couponType != -1">
			<if test="saleorder.couponType == 1">
				and (select count(*) from T_SALEORDER_COUPON coupon where coupon.SALEORDER_ID = a.SALEORDER_ID and coupon.COUPON_TYPE = 1) > 0
			</if>
			<if test="saleorder.couponType == 5">
				and (select count(*) from T_SALEORDER_COUPON coupon where coupon.SALEORDER_ID = a.SALEORDER_ID) = 0
			</if>
		</if>

		<if test="saleorder.invoiceStatus != null and saleorder.invoiceStatus != -1">
			<!-- 开票状态 -->
			AND a.INVOICE_STATUS = #{saleorder.invoiceStatus,jdbcType=INTEGER}
		</if>
		<if test="saleorder.serviceStatus != null and saleorder.serviceStatus != -1">
			<!-- 售后状态 -->
			AND a.SERVICE_STATUS = #{saleorder.serviceStatus,jdbcType=INTEGER}
		</if>
		<if test="saleorder.lockedStatus != null and saleorder.lockedStatus != -1">
			<!-- 锁定状态 -->
			AND a.LOCKED_STATUS = #{saleorder.lockedStatus,jdbcType=INTEGER}
		</if>
		<if test="saleorder.purchaseStatus != null and saleorder.purchaseStatus != -1">
			<!-- 采购状态 -->
			AND a.PURCHASE_STATUS = #{saleorder.purchaseStatus,jdbcType=INTEGER}
		</if>
		<if test="saleorder.traderName != null and saleorder.traderName != ''">
			<!-- 客户名称 -->
			AND a.TRADER_NAME like CONCAT('%',#{saleorder.traderName,jdbcType=VARCHAR},'%' )
		</if>
		<if test="saleorder.customerNature != null and saleorder.customerNature != -1">
			<!-- 客户性质 -->
			AND a.CUSTOMER_NATURE = #{saleorder.customerNature,jdbcType=INTEGER}
		</if>
		<if test="saleorder.traderContact != null and saleorder.traderContact != ''">
			<!-- 联系人信息 -->
			AND (
			a.TRADER_CONTACT_NAME like CONCAT('%',#{saleorder.traderContact,jdbcType=VARCHAR},'%' )
			OR
			a.TRADER_CONTACT_MOBILE like CONCAT('%',#{saleorder.traderContact,jdbcType=VARCHAR},'%' )
			OR
			a.TRADER_CONTACT_TELEPHONE like CONCAT('%',#{saleorder.traderContact,jdbcType=VARCHAR},'%' )
			)
		</if>
		<if test="saleorder.goodsName != null and saleorder.goodsName != ''">
			<!-- 产品名称 -->
			<!-- AND b.GOODS_NAME like CONCAT('%',#{saleorder.goodsName,jdbcType=VARCHAR},'%' )-->
			AND a.SALEORDER_ID IN (
			select SALEORDER_ID from T_SALEORDER_GOODS where GOODS_NAME like
			CONCAT('%',#{saleorder.goodsName,jdbcType=VARCHAR},'%' ) AND IS_DELETE = 0
			)
		</if>
		<if test="saleorder.brandName != null and saleorder.brandName != ''">
			<!-- 品牌 -->
			<!-- AND b.BRAND_NAME like CONCAT('%',#{saleorder.brandName,jdbcType=VARCHAR},'%' )-->
			AND a.SALEORDER_ID IN (
			select SALEORDER_ID from T_SALEORDER_GOODS where BRAND_NAME like
			CONCAT('%',#{saleorder.brandName,jdbcType=VARCHAR},'%' ) AND IS_DELETE = 0
			)
		</if>
		<if test="saleorder.model != null and saleorder.model != ''">
			<!-- 型号 -->
			<!-- AND b.MODEL like CONCAT('%',#{saleorder.model,jdbcType=VARCHAR},'%' )-->
			AND a.SALEORDER_ID IN (
			select SALEORDER_ID from T_SALEORDER_GOODS where MODEL like
			CONCAT('%',#{saleorder.model,jdbcType=VARCHAR},'%' ) AND IS_DELETE = 0
			)
		</if>
		<if test="saleorder.sku != null and saleorder.sku != ''">
			<!-- 订货号 -->
			<!-- AND b.SKU like CONCAT('%',#{saleorder.sku,jdbcType=VARCHAR},'%' ) -->
			AND a.SALEORDER_ID IN (
			select SALEORDER_ID from T_SALEORDER_GOODS where SKU like CONCAT('%',#{saleorder.sku,jdbcType=VARCHAR},'%' ) AND IS_DELETE = 0
			)
		</if>
		<!-- 销售部门 -->

		<if test="saleorder.orgIdList ==null and saleorder.orgId != null and saleorder.orgId != -1">
			AND a.ORG_ID = #{saleorder.orgId,jdbcType=INTEGER}
		</if>

		<!-- 销售人员；根据某个用户查询或查询当前登录用户下级销售用户 -->
		<if test="saleorder.saleUserList != null and saleorder.saleUserList.size() > 0">
			AND f.TRADER_TYPE = 1 and f.USER_ID in
			<foreach collection="saleorder.saleUserList" item="list" separator="," open="(" close=")">
				#{list.userId,jdbcType=INTEGER}
			</foreach>
		</if>
		<!-- 客户ID -->
		<if test="saleorder.traderIdList != null and saleorder.traderIdList.size > 0">
			AND a.TRADER_ID IN
			<foreach collection="saleorder.traderIdList" item="traderId" separator="," open="(" close=")">
				#{traderId,jdbcType=INTEGER}
			</foreach>
		</if>
		<if test="saleorder.deliveryDirect != null and saleorder.deliveryDirect != -1">
			<!-- 直发？ -->
			AND a.DELIVERY_DIRECT = #{saleorder.deliveryDirect,jdbcType=INTEGER}
		</if>
		<if test="saleorder.haveAdvancePurchase != null and saleorder.haveAdvancePurchase != -1">
			<!-- 提前采购 -->
			AND a.HAVE_ADVANCE_PURCHASE = #{saleorder.haveAdvancePurchase,jdbcType=INTEGER}
		</if>
		<if test="saleorder.haveCommunicate != null and saleorder.haveCommunicate != -1">
			<!-- 有沟通 -->
			AND a.HAVE_COMMUNICATE = #{saleorder.haveCommunicate,jdbcType=INTEGER}
		</if>
		<if test="saleorder.creator != null and saleorder.creator != -1">
			<!-- 申请人 -->
			AND a.CREATOR = #{saleorder.creator,jdbcType=INTEGER}
		</if>
		<if test="saleorder.searchDateType !=null and saleorder.searchDateType != ''">
			<choose>
				<when test="saleorder.searchDateType == 1"><!-- 创建时间 -->
					<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
						AND a.ADD_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
					</if>
					<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
						AND a.ADD_TIME > 0
						AND a.ADD_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
					</if>
				</when>
				<when test="saleorder.searchDateType == 2"><!-- 生效时间 -->
					<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
						AND a.VALID_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
					</if>
					<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
						AND a.VALID_TIME > 0
						AND a.VALID_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
					</if>
				</when>
				<when test="saleorder.searchDateType == 3"><!-- 付款时间 -->
					<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
						AND a.PAYMENT_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
					</if>
					<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
						AND a.PAYMENT_TIME > 0
						AND a.PAYMENT_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
					</if>
				</when>
				<when test="saleorder.searchDateType == 4"><!-- 发货时间 -->
					<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
						AND a.DELIVERY_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
					</if>
					<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
						AND a.DELIVERY_TIME > 0
						AND a.DELIVERY_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
					</if>
				</when>
				<when test="saleorder.searchDateType == 5"><!-- 收货时间 -->
					<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
						AND a.ARRIVAL_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
					</if>
					<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
						AND a.ARRIVAL_TIME > 0
						AND a.ARRIVAL_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
					</if>
				</when>
				<when test="saleorder.searchDateType == 6"><!-- 开票时间 -->
					<if test="saleorder.searchBegintime!=null and saleorder.searchBegintime!=0">
						AND a.INVOICE_TIME >= #{saleorder.searchBegintime,jdbcType=BIGINT}
					</if>
					<if test="saleorder.searchEndtime!=null and saleorder.searchEndtime!=0">
						AND a.INVOICE_TIME > 0
						AND a.INVOICE_TIME <![CDATA[ <= ]]> #{saleorder.searchEndtime,jdbcType=BIGINT}
					</if>
				</when>
				<otherwise>
				</otherwise>
			</choose>
		</if>
		<!-- 订单总额 -->
		<if test="saleorder.startAmount != null">
			AND a.TOTAL_AMOUNT <![CDATA[ >= ]]> #{saleorder.startAmount,jdbcType=DECIMAL}
		</if>
		<if test="saleorder.endAmount != null">
			AND a.TOTAL_AMOUNT <![CDATA[ <= ]]> #{saleorder.endAmount,jdbcType=DECIMAL}
		</if>
		<!-- 搜索用 -->
		<!-- <if test="saleorder.search != null">
            AND a.PAYMENT_STATUS != 2
        </if> -->
		<if test="saleorder.search != null">
			AND a.SALEORDER_NO like CONCAT('%',#{saleorder.search,jdbcType=VARCHAR},'%' )
		</if>
		<if test="saleorder.search != null">
			OR a.TRADER_NAME like CONCAT('%',#{saleorder.search,jdbcType=VARCHAR},'%' )
		</if>
		<if test="saleorder.isReferenceCostPrice != null and saleorder.isReferenceCostPrice != -1">
			<!-- 待填 -->
			<if test="saleorder.isReferenceCostPrice == 0">
				AND (
				a.PAYMENT_STATUS = 2 AND a.STATUS != 3 AND
				(
				SELECT COUNT(*)
				FROM T_SALEORDER_GOODS g
				WHERE SALEORDER_ID = a.SALEORDER_ID AND IS_DELETE = 0  AND `REFERENCE_COST_PRICE` = 0
				AND
				(
				NOT EXISTS (
				SELECT SUM(F.NUM)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID)
				OR  (
				SELECT g.`NUM`- IFNULL(SUM(F.NUM),0)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID
				) > 0
				)
				) > 0
				)
			</if>

			<!-- 已填 -->
			<if test="saleorder.isReferenceCostPrice == 1">
				AND
				(
				SELECT COUNT(*)
				FROM T_SALEORDER_GOODS g
				WHERE SALEORDER_ID = a.SALEORDER_ID AND IS_DELETE = 0  AND `REFERENCE_COST_PRICE` = 0
				AND
				(
				NOT EXISTS (
				SELECT SUM(F.NUM)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID)
				OR  (
				SELECT g.`NUM`- IFNULL(SUM(F.NUM),0)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID
				) > 0
				)
				) = 0
			</if>

			<!-- 无需填写 -->
			<if test="saleorder.isReferenceCostPrice == 2">
				AND !(
				(
				a.PAYMENT_STATUS = 2 AND a.STATUS != 3 AND
				(
				SELECT COUNT(*)
				FROM T_SALEORDER_GOODS g
				WHERE SALEORDER_ID = a.SALEORDER_ID AND IS_DELETE = 0  AND `REFERENCE_COST_PRICE` = 0
				AND
				(
				NOT EXISTS (
				SELECT SUM(F.NUM)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID)
				OR  (
				SELECT g.`NUM`- IFNULL(SUM(F.NUM),0)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID
				) > 0
				)
				) > 0
				)
				or
				(
				(
				SELECT COUNT(*)
				FROM T_SALEORDER_GOODS g
				WHERE SALEORDER_ID = a.SALEORDER_ID AND IS_DELETE = 0  AND `REFERENCE_COST_PRICE` = 0
				AND
				(
				NOT EXISTS (
				SELECT SUM(F.NUM)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID)
				OR  (
				SELECT g.`NUM`- IFNULL(SUM(F.NUM),0)
				FROM T_AFTER_SALES_GOODS F LEFT JOIN T_AFTER_SALES H ON F.AFTER_SALES_ID = H.AFTER_SALES_ID
				WHERE H.ATFER_SALES_STATUS = 2
				AND H.SUBJECT_TYPE = 535
				AND H.TYPE = 539
				AND H.ORDER_ID = a.SALEORDER_ID
				AND F.ORDER_DETAIL_ID  = g.SALEORDER_GOODS_ID
				GROUP BY F.ORDER_DETAIL_ID
				) > 0
				)
				) = 0
				)
				)
			</if>
		</if>
		<if test="saleorder.keyIds != null and saleorder.keyIds.size > 0">
			AND a.SALEORDER_ID IN
			<foreach collection="saleorder.keyIds" item="saleorderId" close=")" open="(" separator=",">
				#{saleorderId,jdbcType=INTEGER}
			</foreach>
		</if>

		<if test="saleorder.discountTypeId != null">
			<!-- 限购活动 -->
			<if test="saleorder.discountTypeId == 1">
				AND a.ACTION_ID > 0
			</if>
			<!-- 满减券 -->
			<if test="saleorder.discountTypeId == 2">
				AND a.IS_COUPONS = 1
			</if>
		</if>
	</sql>

	<select id="getOfflineDirectSalesOrderListPage" resultMap="BaseResultMap" parameterType="Map">
		select
		a.*, c.QUOTEORDER_NO, d.BUSSINESS_CHANCE_ID, d.BUSSINESS_CHANCE_NO, e.VERIFIES_TYPE, e.VERIFY_USERNAME,
		IFNULL(e.STATUS, -1) AS VERIFY_STATUS,
		IFNULL(con.`STATUS`,CASE WHEN (a.VALID_TIME / 1000) &gt;= UNIX_TIMESTAMP('2018-01-01') THEN CASE WHEN
		a.HAVE_ACCOUNT_PERIOD = 0 AND a.TOTAL_AMOUNT &lt; 10000 THEN 4 ELSE 3 END ELSE 4 END) CONTRACT_STATUS
		from T_SALEORDER a
		LEFT JOIN T_QUOTEORDER c ON a.QUOTEORDER_ID = c.QUOTEORDER_ID
		LEFT JOIN T_BUSSINESS_CHANCE d ON c.BUSSINESS_CHANCE_ID = d.BUSSINESS_CHANCE_ID
		LEFT JOIN T_VERIFIES_INFO e ON a.SALEORDER_ID = e.RELATE_TABLE_KEY AND e.RELATE_TABLE = 'T_SALEORDER' AND
		e.VERIFIES_TYPE = 623
		LEFT JOIN T_VERIFIES_INFO con ON a.SALEORDER_ID = con.RELATE_TABLE_KEY AND con.RELATE_TABLE = 'T_SALEORDER' AND
		con.VERIFIES_TYPE = 868
		<if test="saleorder.saleUserList != null and saleorder.saleUserList.size() > 0">
			LEFT JOIN T_R_TRADER_J_USER f on a.TRADER_ID = f.TRADER_ID
		</if>

		<if test="saleorder.consultStatus != null and saleorder.consultStatus > 0">
			LEFT JOIN T_QUOTEORDER q ON a.QUOTEORDER_ID = q.QUOTEORDER_ID
		</if>

		<where>
			a.ORDER_TYPE = 9


			<if test="saleorder.isConfirmTime!=null and saleorder.isConfirmTime==0">
				AND a.CONFIRM_TIME is null
			</if>

			<if test="saleorder.isConfirmTime!=null and saleorder.isConfirmTime==1">
				AND a.CONFIRM_TIME is not  null
			</if>
			<include refid="Saleorder_Where_Str"/>
			<!--开票状态与提前开票状态应该都为待审核-->
			<if test="saleorder.openInvoiceApply != null and saleorder.openInvoiceApply == 1">
				and
				exists
				(select 1 from
				T_INVOICE_APPLY g
				where a.SALEORDER_ID = g.RELATED_ID
				and g.TYPE = 505
				and (g.VALID_STATUS = 0 and g.ADVANCE_VALID_STATUS!=2)  )
			</if>
			<if test="saleorder.productBelongUserId != null and saleorder.productBelongUserId != -1">
				and a.SALEORDER_ID in
				<choose>
					<when test="productSaleIds == null or productSaleIds.size == 0">
						(null)
					</when>
					<when test="productSaleIds.size > 0">
						<foreach collection="productSaleIds" item="item" open="(" close=")"  separator=",">
							${item}
						</foreach>
					</when>
				</choose>
			</if>

			<if test="saleorder.consultStatus != null and saleorder.consultStatus > 0">
				AND  a.QUOTEORDER_ID > 0
				<if test="saleorder.consultStatus == 1"> AND (q.CONSULT_STATUS = 1 OR q.CONSULT_STATUS = 3)</if>
				<if test="saleorder.consultStatus == 2"> AND (q.CONSULT_STATUS = 2 OR q.CONSULT_STATUS = 3)</if>
				<if test="saleorder.consultStatus == 4"> AND (q.CONSULT_STATUS = 4)</if>
			</if>
		</where>
		<choose>
			<when test="saleorder.isSearchCount != null and saleorder.isSearchCount == 1">
				order by a.STATUS ASC, a.MOD_TIME DESC
			</when>
			<otherwise>
				order by a.ADD_TIME DESC
			</otherwise>
		</choose>
	</select>

	<update id="updateByJCOrder" parameterType="com.vedeng.order.model.Saleorder">
		UPDATE T_SALEORDER
		<set >
			<if test="status != null" >
				STATUS = #{status,jdbcType=INTEGER},
			</if>
			<if test="closeComments != null" >
				CLOSE_COMMENTS = #{closeComments,jdbcType=VARCHAR},
			</if>
		</set>
		 WHERE SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</update>

	<select id="getRBuyorderSaleorderByBuyOrderGoodIds" resultType="com.vedeng.order.model.RBuyorderSaleorder">
		SELECT
		BUYORDER_GOODS_ID,
		SALEORDER_GOODS_ID
		FROM
		T_R_BUYORDER_J_SALEORDER
		WHERE
		BUYORDER_GOODS_ID IN
		<foreach collection="saleorderGoodIds" item="saleorderGoodId" open="(" close=")" separator=",">
			#{saleorderGoodId,jdbcType=INTEGER}
		</foreach>

	</select>
	<select id="selectSaleorderNoByBuyOrderId" resultType="com.vedeng.order.model.Saleorder">
		select
			distinct e.SALEORDER_ID,e.SALEORDER_NO
		from
			T_BUYORDER a
			left join T_BUYORDER_GOODS b on a.BUYORDER_ID = b.BUYORDER_ID
			left join T_R_BUYORDER_J_SALEORDER c on b.BUYORDER_GOODS_ID = c.BUYORDER_GOODS_ID
			left join T_SALEORDER_GOODS d on c.SALEORDER_GOODS_ID = d.SALEORDER_GOODS_ID
			left join T_SALEORDER e on d.SALEORDER_ID = e.SALEORDER_ID
		where
			a.BUYORDER_ID = #{buyOrderId,jdbcType=INTEGER}
			and a.ORDER_TYPE = 0
			and d.DELIVERY_STATUS in (0,1)
 			<!--and a.STATUS != 3
            and e.STATUS != 3-->
	</select>
	<select id="getOwnerNameBySaleorderId" resultType="java.lang.Integer">
		SELECT f.USER_ID FROM T_SALEORDER AS A
		LEFT JOIN T_R_TRADER_J_USER f on A.TRADER_ID = f.TRADER_ID
		WHERE A.SALEORDER_ID=#{saleorderId,jdbcType=INTEGER};
	</select>
    <select id="getSaleorderGoodsBySaleOrderId" resultType="com.vedeng.order.model.SaleorderGoods">
		SELECT
			A.*
		FROM
			T_SALEORDER_GOODS A
		WHERE A.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
		AND A.IS_DELETE =0 AND A.DELIVERY_DIRECT = 0
		AND A.GOODS_ID NOT IN ( SELECT
					COMMENTS
				FROM
					T_SYS_OPTION_DEFINITION
				WHERE
					PARENT_ID = 693)
		GROUP BY
			A.SALEORDER_GOODS_ID
	</select>

	<select id="getPeriodOrderAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
  	select
  		COALESCE(sum(if(a.PREPAID_AMOUNT <![CDATA[<=]]> aa.payAmount,a.ACCOUNT_PERIOD_AMOUNT,0)),0 )
  	from
  		T_SALEORDER a
  	left join
  		(
  			select
  				COALESCE(sum(ABS(b.AMOUNT)),0) as payAmount ,b.RELATED_ID
  			from
		  		T_CAPITAL_BILL_DETAIL b
		  	left join
		  		T_CAPITAL_BILL c on c.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
  			where

	  			b.ORDER_TYPE = 1
	  		and
	  			b.BUSSINESS_TYPE = 526
	  		and
	  			b.TRADER_ID = #{traderId}
	  		GROUP BY b.RELATED_ID
  		) aa on a.SALEORDER_ID = aa.RELATED_ID
  	where
  		a.TRADER_ID = #{traderId}
  		and
  		a.PAYMENT_STATUS = 1
  		and
  		a.HAVE_ACCOUNT_PERIOD = 1
  		and
  		a.STATUS != 3
  </select>
	<select id="getRemarkComponetInfo" resultType="com.vedeng.order.model.vo.SaleorderGoodsVo">
		SELECT  *  FROM  T_SALEORDER_GOODS WHERE SALEORDER_ID = #{relationId,jdbcType=INTEGER} AND SKU = #{skuNo,jdbcType=VARCHAR} AND IS_DELETE = 0
	</select>
    <select id="getSaleorderModifyApplyBySaleorderModifyId"
            resultType="com.vedeng.order.model.SaleorderModifyApply">
		select a.*,b.USERNAME  from T_SALEORDER_MODIFY_APPLY a left join T_USER b on a.CREATOR = b.USER_ID
		where a.SALEORDER_MODIFY_APPLY_ID = #{saleorderModifyApplyId,jdbcType = INTEGER}
	</select>

    <update id="uptContactStatus">
		UPDATE T_SALEORDER SET IS_CONTRACT_RETURN = #{isContactReturn,jdbcType=TINYINT},UPDATE_DATA_TIME = NOW()
		WHERE SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</update>

	<update id="uptDeliveryStatus">
		UPDATE T_SALEORDER SET IS_DELIVERYORDER_RETURN = #{isDeliveryOrderReturn,jdbcType=TINYINT}
		WHERE SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</update>
	<update id="updateSaleorderRemarkComponent" parameterType="com.vedeng.order.model.ComponentRelation">
		UPDATE T_SALEORDER_GOODS SET COMPONENT_ID = #{componentId,jdbcType=INTEGER} WHERE SALEORDER_ID = #{relationId,jdbcType=INTEGER} AND SKU = #{skuNo,jdbcType=VARCHAR} AND IS_DELETE = 0
	</update>
	<update id="updateSaleOrderFlagRemarkComponent" parameterType="com.vedeng.order.model.ComponentRelation">
		UPDATE T_SALEORDER_GOODS SET SPECIAL_DELIVERY = 1 WHERE SALEORDER_ID = #{relationId,jdbcType=INTEGER} AND SKU = #{skuNo,jdbcType=VARCHAR} AND IS_DELETE = 0
	</update>
	<update id="updateSaleOrderFlagRemarkComponent0" parameterType="com.vedeng.order.model.ComponentRelation">
		UPDATE T_SALEORDER_GOODS SET SPECIAL_DELIVERY = 0 WHERE SALEORDER_ID = #{relationId,jdbcType=INTEGER} AND SKU = #{skuNo,jdbcType=VARCHAR} AND IS_DELETE = 0
	</update>

	<update id="updateWarnStatus" parameterType="com.vedeng.order.model.ComponentRelation">
		UPDATE T_SALEORDER_GOODS SET WARN_LEVEL = #{componentId,jdbcType=INTEGER},AGING=#{aging,jdbcType=INTEGER}
		<if test="agingTime != null">
			,AGING_TIME = #{agingTIme,jdbcType=BIGINT}
		</if>
		WHERE SALEORDER_ID = #{relationId,jdbcType=INTEGER} AND SKU = #{skuNo,jdbcType=VARCHAR} AND IS_DELETE = 0
	</update>
	<select id="selectTimeOutSales" resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List"/>
		FROM T_SALEORDER WHERE
		DELIVERY_DELAY_TIME >= #{compstartTime,jdbcType=BIGINT}
		AND DELIVERY_DELAY_TIME &lt;= #{compendTime,jdbcType=BIGINT}
		AND VALID_STATUS = 1 AND PAYMENT_STATUS = 2 AND STATUS = 1
	</select>

	<select id="getSaleorderIncludeSpecialDilivery" resultType="com.vedeng.order.model.SaleorderGoods">
		SELECT
			A.*
		FROM
			T_SALEORDER_GOODS A
		WHERE A.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
		AND A.IS_DELETE =0 AND A.DELIVERY_DIRECT = 0
		AND A.GOODS_ID NOT IN ( SELECT
					COMMENTS
				FROM
					T_SYS_OPTION_DEFINITION
				WHERE
					PARENT_ID = 693)
		GROUP BY
			A.SALEORDER_GOODS_ID
	</select>


	<select id="getRBuyorderSaleorderBySaleOrderGoodsId" resultType="com.vedeng.order.model.dto.RBuyorderSaleorderDto">
		SELECT
			r.BUYORDER_GOODS_ID,
			r.SALEORDER_GOODS_ID,
			r.NUM,
			b.status,
			b.BUYORDER_NO,
		    b.BUYORDER_ID
		FROM T_R_BUYORDER_J_SALEORDER r
			INNER JOIN T_BUYORDER_GOODS g ON r.BUYORDER_GOODS_ID = g.BUYORDER_GOODS_ID
			INNER JOIN T_BUYORDER b ON b.BUYORDER_ID = g.BUYORDER_ID
		WHERE
			b.status in (1,2)  and  b.VALID_STATUS = 1
		  and g.IS_DELETE = 0
			and SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
	</select>

	<select id="getRelateSaleOrderId" resultType="java.lang.Integer">
		SELECT
			distinct s.SALEORDER_ID
		FROM T_R_BUYORDER_J_SALEORDER r
		INNER JOIN T_BUYORDER_GOODS g ON r.BUYORDER_GOODS_ID = g.BUYORDER_GOODS_ID
		INNER JOIN T_BUYORDER b ON b.BUYORDER_ID = g.BUYORDER_ID
		LEFT JOIN T_SALEORDER_GOODS sg ON sg.SALEORDER_GOODS_ID = r.SALEORDER_GOODS_ID
        LEFT JOIN T_SALEORDER s ON s.SALEORDER_ID = sg.SALEORDER_ID
		WHERE b.BUYORDER_ID = #{buyorderId,jdbcType=INTEGER} and s.SALEORDER_ID is not null
	</select>
	<select id="selectBySaleOrderId" resultType="com.vedeng.order.model.Saleorder">
		select
			c.DELIVERY_DIRECT,t.TRADER_NAME,c.TRADER_ID,c.ORG_ID,c.ORG_NAME,c.SALEORDER_ID,c.DELIVERY_STATUS,c.ARRIVAL_STATUS,
			c.SALEORDER_NO,c.VALID_TIME,c.TOTAL_AMOUNT,c.REAL_TOTAL_AMOUNT,c.RETENTION_MONEY,c.BILL_PERIOD_SETTLEMENT_TYPE,c.STATUS
		from
			T_SALEORDER c
		LEFT JOIN T_TRADER t ON c.TRADER_ID =  t.TRADER_ID
		where c.SALEORDER_ID = #{relationId,jdbcType=INTEGER}
	</select>

    <select id="getOrderApplyIdsBySaleOrderId" resultType="java.lang.Integer">
		SELECT
			SALEORDER_MODIFY_APPLY_ID
		FROM
			T_SALEORDER_MODIFY_APPLY
		WHERE
			SALEORDER_ID = #{orderId,jdbcType=INTEGER}
	</select>

	<select id="getSaleOrderIdsByStatus" resultType="java.lang.Integer">
		SELECT
			SALEORDER_ID
		FROM
			T_SALEORDER
		WHERE
			`STATUS` = #{status,jdbcType=INTEGER}
	</select>

	<select id="getCustomerIdByOrderId" resultType="java.lang.Long">
		SELECT
			TU.TRADER_CUSTOMER_ID
		FROM
			T_SALEORDER SO
			INNER JOIN T_TRADER_CUSTOMER TU ON SO.TRADER_ID = TU.TRADER_ID
		WHERE
			SO.SALEORDER_ID = #{orderId,jdbcType=INTEGER}
	</select>
	<select id="getSaleOrderTraderById" resultType="com.vedeng.order.model.Saleorder">
		SELECT
			A.SALEORDER_NO,A.TRADER_NAME,B.USER_ID,A.ADD_TIME
		FROM
			T_SALEORDER A LEFT JOIN T_R_TRADER_J_USER B ON A.TRADER_ID = B.TRADER_ID AND B.TRADER_TYPE =1
		WHERE A.SALEORDER_ID= #{saleorderId}
	</select>
    <select id="getSaleOrderInfoBySaleOrderInfo" resultType="com.vedeng.order.model.Saleorder">
		SELECT C.TRADER_ID,C.SALEORDER_ID,C.ORG_ID
		FROM T_SALEORDER C
		LEFT JOIN T_TRADER t ON C.TRADER_ID =  t.TRADER_ID
		<!-- 销售人员登录：下属人员不为空 -->
		<if test="userIdList != null and userIdList.size > 0">
			INNER JOIN T_R_TRADER_J_USER G ON C.TRADER_ID = G.TRADER_ID
		</if>
		where 1=1 AND C.HAVE_ACCOUNT_PERIOD = 1
		<!-- 销售人员登录：下属人员不为空 -->
		<if test="userIdList != null and userIdList.size > 0">
			AND G.USER_ID IN
			<foreach collection="userIdList" item="userId" open="(" close=")" separator=",">
				#{userId,jdbcType=INTEGER}
			</foreach>
		</if>

		<if test="companyId != null">
			AND C.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		</if>
		<!-- 客户名称 -->
		<if test="traderName != null and traderName != ''">
			AND t.TRADER_NAME LIKE CONCAT('%',#{traderName,jdbcType=VARCHAR},'%')
		</if>
		<!-- 归属销售 -->
		<if test="traderIdList != null and traderIdList.size > 0">
			AND C.TRADER_ID IN
			<foreach collection="traderIdList" item="traderId" separator="," open="(" close=")">
				#{traderId,jdbcType=INTEGER}
			</foreach>
		</if>
		<!-- 订单号 -->
		<if test="saleorderNo !=null and saleorderNo != ''">
			AND C.SALEORDER_NO LIKE CONCAT('%',#{saleorderNo,jdbcType=VARCHAR},'%')
		</if>
		<!-- 生效日期开始 -->
		<if test="searchBegintime != null and searchBegintime != ''">
			AND C.VALID_TIME <![CDATA[ >= ]]> #{searchBegintime,jdbcType=BIGINT}
		</if>
		<!-- 生效日期结束 -->
		<if test="searchEndtime != null and searchEndtime != ''">
			AND C.VALID_TIME <![CDATA[ <= ]]> #{searchEndtime,jdbcType=BIGINT}
		</if>
		<if test="orgIdList != null">
			<trim prefix="AND ( " suffix=" )" prefixOverrides="or">
				<foreach item="item" collection="orgIdList" index="">
					or( C.ORG_ID = #{item, jdbcType=INTEGER} )
				</foreach>
			</trim>
		</if>
		GROUP BY C.SALEORDER_ID
	</select>
	<select id="getSaleorderLackAccountPeriodAmountDatas" resultType="com.vedeng.finance.model.SaleorderData">
		select
		b.RELATED_ID as saleorderId,
		zq.zq_amount as lackAccountPeriodAmount,
		zq.periodAmount
		from
		T_CAPITAL_BILL a
		left join
		T_CAPITAL_BILL_DETAIL b
		on
		a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		left join (
		select
		COALESCE(sum(ABS(a2.AMOUNT)),0) AS
		periodAmount,COALESCE(sum(ABS(a2.AMOUNT))-IFNULL(c.hk_amount,0)-IFNULL(d.hk_amount,0),0) as
		zq_amount,b2.RELATED_ID as zq_RELATED_ID
		from
		T_CAPITAL_BILL a2
		left join
		T_CAPITAL_BILL_DETAIL b2
		on
		a2.CAPITAL_BILL_ID = b2.CAPITAL_BILL_ID
		left join
		(
		select
		COALESCE(sum(ABS(b1.AMOUNT)),0) as hk_amount,b1.RELATED_ID
		from
		T_CAPITAL_BILL a1
		left join
		T_CAPITAL_BILL_DETAIL b1
		on
		a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
		where
		a1.TRADER_TYPE in (1,4)
		and
		b1.ORDER_TYPE = 1
		and
		b1.BUSSINESS_TYPE = 533
		group by b1.RELATED_ID
		) as c
		on
		b2.RELATED_ID = c.RELATED_ID
		left join
		(
		select
		COALESCE(sum(ABS(b1.AMOUNT)),0) as hk_amount,c1.ORDER_ID
		from
		T_CAPITAL_BILL a1
		left join
		T_CAPITAL_BILL_DETAIL b1
		on
		a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
		left JOIN
		T_AFTER_SALES c1
		ON
		c1.AFTER_SALES_ID = b1.RELATED_ID and c1.SUBJECT_TYPE = 535
		where
		a1.TRADER_TYPE = 3
		and
		b1.ORDER_TYPE = 3
		and
		b1.BUSSINESS_TYPE = 533
		group by c1.ORDER_ID
		) as d
		on
		d.ORDER_ID = b2.RELATED_ID
		where
		a2.TRADER_TYPE = 3
		and
		b2.ORDER_TYPE = 1
		and
		a2.TRADER_MODE = 527
		group by b2.RELATED_ID
		) as zq
		on
		zq.zq_RELATED_ID=b.RELATED_ID
		where
		b.ORDER_TYPE = 1
		and
		b.BUSSINESS_TYPE != 533
		and
		b.RELATED_ID in
		<foreach item="saleorderId" index="index" collection="saleorderIds" open="(" separator="," close=")">
			#{saleorderId,jdbcType=INTEGER}
		</foreach>
		GROUP BY b.RELATED_ID
	</select>
	<select id="getAccountPeriodSaleorderIdList" resultType="java.lang.Integer">
		SELECT
			A.SALEORDER_ID,
			A.SALEORDER_NO,
			A.PREPAID_AMOUNT,
			A.ACCOUNT_PERIOD_AMOUNT,
			A.PERIOD_DAY,
			A.REAL_RETURN_AMOUNT,
			A.ADD_TIME
		FROM
			T_SALEORDER A
		WHERE
			A.ACCOUNT_PERIOD_AMOUNT > 0 AND A.`STATUS` IN (1,0) AND A.COMPANY_ID = 1
		<if test="saleorderNoList != null and saleorderNoList.size > 0">
			AND A.SALEORDER_NO IN
			<foreach collection="saleorderNoList" item="orderNo" separator="," open="(" close=")">
				#{orderNo}
			</foreach>
		</if>
	</select>

	<select id="getPaymentAndPeriodAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
		select
			COALESCE(
						sum(
								if(a.TRADER_TYPE=1 or a.TRADER_TYPE = 4,ABS(a.AMOUNT) ,-ABS(a.AMOUNT))
							) - IFNULL(cc.thAmount,0)-IFNULL(dd.tkAmount,0) ,0)
		from
			T_CAPITAL_BILL a
				left join
			T_CAPITAL_BILL_DETAIL b
			on
				a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
				LEFT JOIN (
				SELECT
					sum(abs(a.AMOUNT)) AS thAmount,
					c.ORDER_ID
				FROM
					T_CAPITAL_BILL_DETAIL a
						LEFT JOIN T_CAPITAL_BILL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
						LEFT JOIN T_AFTER_SALES c ON a.RELATED_ID = c.AFTER_SALES_ID
				WHERE
					a.BUSSINESS_TYPE = 531
				  AND b.TRADER_TYPE = 5
				  AND c.VALID_STATUS = 1
				  AND c.SUBJECT_TYPE = 535
				  AND c.TYPE in (543,539)
				  AND c.ORDER_ID = #{saleorderId,jdbcType=INTEGER}
				GROUP BY
					c.ORDER_ID
			) cc ON b.RELATED_ID = cc.ORDER_ID
				LEFT JOIN (
				SELECT
					sum(abs(b.AMOUNT)) AS tkAmount,
					b.RELATED_ID
				FROM
					T_CAPITAL_BILL a
						LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
				WHERE
					b.BUSSINESS_TYPE = 531
				  AND b.ORDER_TYPE = 1
				  AND b.RELATED_ID = #{saleorderId,jdbcType=INTEGER}
				GROUP BY
					b.RELATED_ID
			) dd ON dd.RELATED_ID = b.RELATED_ID
		where
			(
					(a.TRADER_TYPE in (1,4,5))
					or
					(a.TRADER_TYPE = 2 and b.BUSSINESS_TYPE = 679)
				)
		  and
			b.ORDER_TYPE = 1
		  and b.RELATED_ID = #{saleorderId,jdbcType=INTEGER}

	</select>
	<select id="getSaleorder" resultMap="BaseResultMap" parameterType="com.vedeng.order.model.Saleorder">
		select
		<include refid="Base_Column_List"/>
		from T_SALEORDER
		where 1=1
		<choose>
			<when test="saleorderId != null">
				AND SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
			</when>
			<otherwise>
				AND SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
			</otherwise>
		</choose>
	</select>

	<select id="getSpecialGoodsAmountByOrderId" resultType="java.math.BigDecimal">
		SELECT
			SUM( IFNULL( T2.REAL_PRICE, 0 ) )
		FROM
			T_SALEORDER T1
			LEFT JOIN T_SALEORDER_GOODS T2 ON T1.SALEORDER_ID = T2.SALEORDER_ID
			AND T2.IS_DELETE = 0
		WHERE
			T1.SALEORDER_ID = #{orderId,jdbcType=INTEGER}
			AND T2.GOODS_ID IN ( SELECT COMMENTS FROM T_SYS_OPTION_DEFINITION WHERE PARENT_ID = 693 )
	</select>
    <select id="getPriceChangeAffectSaleorderBySkuId" resultType="java.lang.Integer">
	SELECT
		A.SALEORDER_ID
	FROM
		T_SALEORDER AS A
		LEFT JOIN T_SALEORDER_GOODS AS B ON A.SALEORDER_ID = B.SALEORDER_ID
		LEFT JOIN V_CORE_SKU AS C ON C.SKU_NO = B.SKU
	WHERE
		C.SKU_ID = #{skuId,jdbcType=BIGINT}
		AND A.ADD_TIME BETWEEN unix_timestamp(now())*1000 - 30*24*60*60*1000 AND unix_timestamp(now())*1000
		AND A.PAYMENT_STATUS = 0
		AND A.ORDER_TYPE IN (0,1,5,7,8,9)
		AND A.STATUS != 3
	</select>

	<select id="getTraderIdList" resultType="java.lang.Integer" parameterType="java.lang.Integer">
  	select
  		distinct a.TRADER_ID
  	from
  		T_SALEORDER a
  	left join T_TRADER t on t.TRADER_ID = a.TRADER_ID
  	where a.VALID_STATUS = 1 and a.STATUS != 3 and t.COMPANY_ID = #{companyId,jdbcType=INTEGER}
  </select>
    <select id="getRelatedSaleOrderInprocess" resultType="com.vedeng.order.model.Saleorder">
		select distinct SO.SALEORDER_NO
		from T_SALEORDER SO
		LEFT JOIN T_SALEORDER_GOODS SG ON SO.SALEORDER_ID = SG.SALEORDER_ID
		WHERE SO.STATUS IN (0,1)
		AND  SG.SKU = #{skuNo,jdbcType=VARCHAR}

	</select>

	<select id="getAllSmsSaleOrder" parameterType="java.lang.Integer" resultType="com.vedeng.order.model.dto.SmsSaleorderDTO">
	SELECT
	RES.TRADER_NAME,
	RES.TRADER_CONTACT_NAME,
	RES.TAKE_TRADER_NAME,
	RES.SALEORDER_NO,
	L.`NAME` AS EXPRESS_NAME,
	E.LOGISTICS_NO,
	UD.MOBILE AS SALE_MOBILE,
	RES.ORDER_TYPE,
	RES.USER_ID AS SALE_ID,
	RES.TAKE_TRADER_CONTACT_MOBILE,
	RES.TRADER_CONTACT_MOBILE,
	E.EXPRESS_ID
FROM
	(
	SELECT
		S.TRADER_NAME,
		S.TRADER_CONTACT_NAME,
		S.TAKE_TRADER_NAME,
		S.SALEORDER_NO,
		S.ORDER_TYPE,
		S.USER_ID,
		S.TAKE_TRADER_CONTACT_MOBILE,
		S.TRADER_CONTACT_MOBILE,
	IF
		( BG.BUYORDER_GOODS_ID IS NOT NULL AND S.DELIVERY_DIRECT = 1, BG.BUYORDER_GOODS_ID, SG.SALEORDER_GOODS_ID ) AS GOODS_ID,
	IF
		( BG.BUYORDER_GOODS_ID IS NOT NULL AND S.DELIVERY_DIRECT = 1, 515, 496 ) AS TYPE
	FROM
		T_SALEORDER S
		LEFT JOIN T_SALEORDER_GOODS SG ON SG.SALEORDER_ID = S.SALEORDER_ID
		LEFT JOIN T_R_BUYORDER_J_SALEORDER BS ON BS.SALEORDER_GOODS_ID = SG.SALEORDER_GOODS_ID
		LEFT JOIN T_BUYORDER_GOODS BG ON BG.BUYORDER_GOODS_ID = BS.BUYORDER_GOODS_ID
	WHERE
		S.ORDER_TYPE != 2
		AND S.DELIVERY_STATUS != 0
		AND S.ARRIVAL_STATUS != 2
		AND S.VALID_STATUS = 1
		AND S.`STATUS` = 1
		<if test="traderIds!=null and traderIds.size()>0">
	      AND S.TRADER_ID NOT IN
			<foreach collection="traderIds" item="traderId" open="(" close=")" separator=",">
				#{traderId}
			</foreach>
		</if>
		AND S.VALID_TIME > 1630425600000
	) AS RES
	LEFT JOIN T_EXPRESS_DETAIL ED ON ED.RELATED_ID = RES.GOODS_ID AND RES.TYPE = ED.BUSINESS_TYPE
	LEFT JOIN T_EXPRESS E ON E.EXPRESS_ID = ED.EXPRESS_ID
	LEFT JOIN T_USER_DETAIL UD ON UD.USER_ID = RES.USER_ID
	LEFT JOIN T_LOGISTICS L ON L.LOGISTICS_ID = E.LOGISTICS_ID
WHERE
	ED.NUM > 0
	AND ED.BUSINESS_TYPE IN ( 496, 515 )
	AND E.SENT_SMS = 0
	AND L.`NAME` IS NOT NULL
	AND E.LOGISTICS_NO IS NOT NULL
	AND E.LOGISTICS_NO != ''
GROUP BY
	E.LOGISTICS_NO
	</select>
	<select id="getSaleorderListPage" resultType="com.vedeng.order.model.Saleorder">
		SELECT
		*
        FROM T_SALEORDER
        WHERE  VALID_TIME between 1577808000000 and 1609430400000
	</select>

	<select id="selectSumNumBySaleorderGoodsId" resultMap="VoResultMap" parameterType="java.util.Map">
  	SELECT
		s.*, IFNULL(sum(sog.NUM), 0) AS ALL_NUM,
		IFNULL(sum(sog.DELIVERY_NUM), 0) AS ALL_DELIVERY_NUM
	FROM
		T_SALEORDER s
	LEFT JOIN T_SALEORDER_GOODS sog ON s.SALEORDER_ID = sog.SALEORDER_ID
	AND sog.IS_DELETE = 0
	AND sog.GOODS_ID NOT IN (
		SELECT
			COMMENTS
		FROM
			T_SYS_OPTION_DEFINITION
		WHERE
			PARENT_ID = 693
	)
	WHERE
		s.SALEORDER_ID = (
			SELECT
				a.SALEORDER_ID
			FROM
				T_SALEORDER_GOODS a
			WHERE
				a.SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
	)
  </select>
	<!-- 获取订单基本信息 -->
	<select id="getBaseSaleorderInfo" resultType="com.vedeng.order.model.Saleorder" parameterType="java.lang.Integer">
	SELECT
	a.*,
	b.QUOTEORDER_NO,
	c.BUSSINESS_CHANCE_ID,
	c.BUSSINESS_CHANCE_NO,
	d.VERIFY_USERNAME,
	d.STATUS AS VERIFY_STATUS,
	e.STATUS AS CONTRACT_STATUS,
	bh.STATUS AS bhVerifyStatus,
	a.IS_PRINTOUT,
	FROM_UNIXTIME(a.DELIVERY_DELAY_TIME/1000,'%Y-%m-%d') as DELIVERY_DELAY_TIME_STR
	FROM
	T_SALEORDER a
	LEFT JOIN T_QUOTEORDER b
	ON a.QUOTEORDER_ID = b.QUOTEORDER_ID
	LEFT JOIN T_BUSSINESS_CHANCE c
	ON b.BUSSINESS_CHANCE_ID = c.BUSSINESS_CHANCE_ID
	LEFT JOIN T_VERIFIES_INFO d
	ON a.SALEORDER_ID = d.RELATE_TABLE_KEY
	AND d.RELATE_TABLE = "T_SALEORDER"
	AND d.VERIFIES_TYPE = 623
	LEFT JOIN T_VERIFIES_INFO e
	ON a.SALEORDER_ID = e.RELATE_TABLE_KEY
	AND e.RELATE_TABLE = "T_SALEORDER"
	AND e.VERIFIES_TYPE = 868
	LEFT JOIN T_VERIFIES_INFO bh
	ON a.SALEORDER_ID = bh.RELATE_TABLE_KEY
	AND bh.RELATE_TABLE = "T_SALEORDER"
	AND bh.VERIFIES_TYPE = 620
	WHERE a.SALEORDER_ID =
	#{saleorderId,jdbcType=INTEGER}
  </select>

	<select id="getHaveInvoiceNums" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
  	select
	  		COALESCE(sum(if(b.PRICE>0,abs(b.NUM),-b.NUM))+IFNULL(c.refundNum,0),0)
	  	from
	  		T_INVOICE a
	  	left join
	  		T_INVOICE_DETAIL b
	  	on
	  		a.INVOICE_ID = b.INVOICE_ID
			LEFT JOIN
			(
				SELECT
				sum(if(dd.PRICE>0,abs(dd.NUM),-abs(dd.NUM))) as refundNum,dd.DETAILGOODS_ID as detailId
				FROM
				T_AFTER_SALES aa
				LEFT JOIN
				T_INVOICE cc on aa.AFTER_SALES_ID = cc.RELATED_ID and cc.TYPE=504
				LEFT JOIN
				T_INVOICE_DETAIL dd on cc.INVOICE_ID = dd.INVOICE_ID
				WHERE
				aa.SUBJECT_TYPE = 535
				AND
				cc.VALID_STATUS=1
				GROUP BY dd.DETAILGOODS_ID
			) as c on c.detailId = b.DETAILGOODS_ID
	  	where
	  		a.TYPE = 505
	  	and
	  		a.VALID_STATUS = 1
	  	and
	  		b.DETAILGOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
  </select>
	<select id="getSaleorderPaymentAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
		<!-- 此方法未减去售后退货的金额 -->
		select
		COALESCE(sum(if(a.TRADER_TYPE=1 or a.TRADER_TYPE=4,ABS(a.AMOUNT),-ABS(a.AMOUNT))),0)
		from
		T_CAPITAL_BILL a
		left join
		T_CAPITAL_BILL_DETAIL b
		on
		a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		where
		a.TRADER_TYPE in (1,2,4,5)
		and (
		(a.TRADER_TYPE in (1,4,5))
		or
		(a.TRADER_TYPE = 2 and b.BUSSINESS_TYPE = 679)
		)
		and
		b.ORDER_TYPE = 1
		and
		b.BUSSINESS_TYPE != 533
		and
		b.RELATED_ID = #{saleorderId,jdbcType=INTEGER}

	</select>
	<select id="getSaleOrderBaseInfo" resultType="com.vedeng.order.model.Saleorder">
		SELECT
			a.*,
			u1.USERNAME AS CREATOR_NAME,
			u2.USERNAME AS OPT_USER_NAME,
			o.ORG_NAME AS SALES_DEPT_NAME,
			b.QUOTEORDER_NO,
			c.BUSSINESS_CHANCE_ID,
			c.BUSSINESS_CHANCE_NO,
			d.VERIFY_USERNAME,
			d.STATUS AS VERIFY_STATUS,
			e.STATUS AS CONTRACT_STATUS,
			bh.STATUS AS bhVerifyStatus,
			a.IS_PRINTOUT,
			FROM_UNIXTIME(a.DELIVERY_DELAY_TIME/1000,'%Y-%m-%d') as DELIVERY_DELAY_TIME_STR,
			cn.TITLE AS CUSTOMER_NATURE_STR,
			ct.TITLE AS CUSTOMER_TYPE_STR,
			tt.TITLE AS TERMINAL_TRADER_TYPE_STR
		FROM
			T_SALEORDER a
				LEFT JOIN T_USER u1 ON a.CREATOR = u1.USER_ID
				LEFT JOIN T_R_TRADER_J_USER tu ON a.TRADER_ID = tu.TRADER_ID and tu.TRADER_TYPE = 1
				LEFT JOIN T_USER u2 ON tu.USER_ID = u2.USER_ID
				LEFT JOIN T_R_USER_POSIT up ON tu.USER_ID = up.USER_ID
				LEFT JOIN T_POSITION p ON up.POSITION_ID = p.POSITION_ID
				LEFT JOIN T_ORGANIZATION o ON o.ORG_ID = p.ORG_ID
				LEFT JOIN T_QUOTEORDER b
						  ON a.QUOTEORDER_ID = b.QUOTEORDER_ID
				LEFT JOIN T_BUSSINESS_CHANCE c
						  ON b.BUSSINESS_CHANCE_ID = c.BUSSINESS_CHANCE_ID
				LEFT JOIN T_VERIFIES_INFO d
						  ON a.SALEORDER_ID = d.RELATE_TABLE_KEY
							  AND d.RELATE_TABLE = 'T_SALEORDER'
							  AND d.VERIFIES_TYPE = 623
				LEFT JOIN T_VERIFIES_INFO e
						  ON a.SALEORDER_ID = e.RELATE_TABLE_KEY
							  AND e.RELATE_TABLE = 'T_SALEORDER'
							  AND e.VERIFIES_TYPE = 868
				LEFT JOIN T_VERIFIES_INFO bh
						  ON a.SALEORDER_ID = bh.RELATE_TABLE_KEY
							  AND bh.RELATE_TABLE = 'T_SALEORDER'
							  AND bh.VERIFIES_TYPE = 620
				LEFT JOIN T_SYS_OPTION_DEFINITION ct ON a.CUSTOMER_TYPE = ct.SYS_OPTION_DEFINITION_ID
				LEFT JOIN T_SYS_OPTION_DEFINITION cn ON a.CUSTOMER_NATURE = cn.SYS_OPTION_DEFINITION_ID
				LEFT JOIN T_SYS_OPTION_DEFINITION tt ON a.TERMINAL_TRADER_TYPE = tt.SYS_OPTION_DEFINITION_ID
		WHERE a.SALEORDER_ID = #{saleOrderId,jdbcType=INTEGER} limit 1
	</select>

	<resultMap id="BaseResultMap4DBCenter" type="com.vedeng.order.model.Saleorder">
		<id column="SALEORDER_ID" property="saleorderId" jdbcType="INTEGER"/>
		<result column="QUOTEORDER_ID" property="quoteorderId" jdbcType="INTEGER"/>
		<result column="PARENT_ID" property="parentId" jdbcType="INTEGER"/>
		<result column="SALEORDER_NO" property="saleorderNo" jdbcType="VARCHAR"/>
		<result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER"/>
		<result column="COMPANY_ID" property="companyId" jdbcType="INTEGER"/>
		<result column="ORG_ID" property="orgId" jdbcType="INTEGER"/>
		<result column="USER_ID" property="userId" jdbcType="INTEGER"/>
		<result column="VALID_STATUS" property="validStatus" jdbcType="BIT"/>
		<result column="VALID_TIME" property="validTime" jdbcType="BIGINT"/>
		<result column="STATUS" property="status" jdbcType="BIT"/>
		<result column="PURCHASE_STATUS" property="purchaseStatus" jdbcType="BIT"/>
		<result column="LOCKED_STATUS" property="lockedStatus" jdbcType="BIT"/>
		<result column="INVOICE_STATUS" property="invoiceStatus" jdbcType="BIT"/>
		<result column="INVOICE_TIME" property="invoiceTime" jdbcType="BIGINT"/>
		<result column="PAYMENT_STATUS" property="paymentStatus" jdbcType="BIT"/>
		<result column="PAYMENT_TIME" property="paymentTime" jdbcType="BIGINT"/>
		<result column="DELIVERY_STATUS" property="deliveryStatus" jdbcType="BIT"/>
		<result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT"/>
		<result column="IS_CUSTOMER_ARRIVAL" property="isCustomerArrival" jdbcType="BIT"/>
		<result column="ARRIVAL_STATUS" property="arrivalStatus" jdbcType="BIT"/>
		<result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT"/>
		<result column="SERVICE_STATUS" property="serviceStatus" jdbcType="BIT"/>
		<result column="HAVE_ACCOUNT_PERIOD" property="haveAccountPeriod" jdbcType="BIT"/>
		<result column="TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL"/>
		<result column="TRADER_ID" property="traderId" jdbcType="INTEGER"/>
		<result column="CUSTOMER_TYPE" property="customerType" jdbcType="INTEGER"/>
		<result column="CUSTOMER_NATURE" property="customerNature" jdbcType="INTEGER"/>
		<result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR"/>
		<result column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER"/>
		<result column="TRADER_CONTACT_NAME" property="traderContactName" jdbcType="VARCHAR"/>
		<result column="TRADER_CONTACT_MOBILE" property="traderContactMobile" jdbcType="VARCHAR"/>
		<result column="TRADER_CONTACT_TELEPHONE" property="traderContactTelephone" jdbcType="VARCHAR"/>
		<result column="TRADER_ADDRESS_ID" property="traderAddressId" jdbcType="INTEGER"/>
		<result column="TRADER_ADDRESS" property="traderAddress" jdbcType="VARCHAR"/>
		<result column="TRADER_COMMENTS" property="traderComments" jdbcType="VARCHAR"/>
		<result column="TAKE_TRADER_ID" property="takeTraderId" jdbcType="INTEGER"/>
		<result column="TAKE_TRADER_NAME" property="takeTraderName" jdbcType="VARCHAR"/>
		<result column="TAKE_TRADER_CONTACT_ID" property="takeTraderContactId" jdbcType="INTEGER"/>
		<result column="TAKE_TRADER_CONTACT_NAME" property="takeTraderContactName" jdbcType="VARCHAR"/>
		<result column="TAKE_TRADER_CONTACT_MOBILE" property="takeTraderContactMobile" jdbcType="VARCHAR"/>
		<result column="TAKE_TRADER_CONTACT_TELEPHONE" property="takeTraderContactTelephone" jdbcType="VARCHAR"/>
		<result column="TAKE_TRADER_ADDRESS_ID" property="takeTraderAddressId" jdbcType="INTEGER"/>
		<result column="TAKE_TRADER_ADDRESS" property="takeTraderAddress" jdbcType="VARCHAR"/>
		<result column="IS_SEND_INVOICE" property="isSendInvoice" jdbcType="BIT"/>
		<result column="INVOICE_TRADER_ID" property="invoiceTraderId" jdbcType="INTEGER"/>
		<result column="INVOICE_TRADER_NAME" property="invoiceTraderName" jdbcType="VARCHAR"/>
		<result column="INVOICE_TRADER_CONTACT_ID" property="invoiceTraderContactId" jdbcType="INTEGER"/>
		<result column="INVOICE_TRADER_CONTACT_NAME" property="invoiceTraderContactName" jdbcType="VARCHAR"/>
		<result column="INVOICE_TRADER_CONTACT_MOBILE" property="invoiceTraderContactMobile" jdbcType="VARCHAR"/>
		<result column="INVOICE_TRADER_CONTACT_TELEPHONE" property="invoiceTraderContactTelephone" jdbcType="VARCHAR"/>
		<result column="INVOICE_TRADER_ADDRESS_ID" property="invoiceTraderAddressId" jdbcType="INTEGER"/>
		<result column="INVOICE_TRADER_ADDRESS" property="invoiceTraderAddress" jdbcType="VARCHAR"/>
		<result column="SALES_AREA_ID" property="salesAreaId" jdbcType="INTEGER"/>
		<result column="SALES_AREA" property="salesArea" jdbcType="VARCHAR"/>
		<result column="TERMINAL_TRADER_ID" property="terminalTraderId" jdbcType="INTEGER"/>
		<result column="TERMINAL_TRADER_NAME" property="terminalTraderName" jdbcType="VARCHAR"/>
		<result column="TERMINAL_TRADER_TYPE" property="terminalTraderType" jdbcType="INTEGER"/>
		<result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER"/>
		<result column="INVOICE_METHOD" property="invoiceMethod" jdbcType="INTEGER"/>
		<result column="FREIGHT_DESCRIPTION" property="freightDescription" jdbcType="INTEGER"/>
		<result column="DELIVERY_TYPE" property="deliveryType" jdbcType="INTEGER"/>
		<result column="LOGISTICS_ID" property="logisticsId" jdbcType="INTEGER"/>
		<result column="PAYMENT_TYPE" property="paymentType" jdbcType="INTEGER"/>
		<result column="PREPAID_AMOUNT" property="prepaidAmount" jdbcType="DECIMAL"/>
		<result column="ACCOUNT_PERIOD_AMOUNT" property="accountPeriodAmount" jdbcType="DECIMAL"/>
		<result column="LOGISTICS_COLLECTION" property="logisticsCollection" jdbcType="BIT"/>
		<result column="RETAINAGE_AMOUNT" property="retainageAmount" jdbcType="DECIMAL"/>
		<result column="RETAINAGE_AMOUNT_MONTH" property="retainageAmountMonth" jdbcType="INTEGER"/>
		<result column="PAYMENT_COMMENTS" property="paymentComments" jdbcType="VARCHAR"/>
		<result column="ADDITIONAL_CLAUSE" property="additionalClause" jdbcType="VARCHAR"/>
		<result column="LOGISTICS_COMMENTS" property="logisticsComments" jdbcType="VARCHAR"/>
		<result column="FINANCE_COMMENTS" property="financeComments" jdbcType="VARCHAR"/>
		<result column="COMMENTS" property="comments" jdbcType="VARCHAR"/>
		<result column="INVOICE_COMMENTS" property="invoiceComments" jdbcType="VARCHAR"/>
		<result column="DELIVERY_DIRECT" property="deliveryDirect" jdbcType="BIT"/>
		<result column="SUPPLIER_CLAUSE" property="supplierClause" jdbcType="VARCHAR"/>
		<result column="HAVE_ADVANCE_PURCHASE" property="haveAdvancePurchase" jdbcType="BIT"/>
		<result column="IS_URGENT" property="isUrgent" jdbcType="BIT"/>
		<result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL"/>
		<result column="HAVE_COMMUNICATE" property="haveCommunicate" jdbcType="BIT"/>
		<result column="PREPARE_COMMENTS" property="prepareComments" jdbcType="VARCHAR"/>
		<result column="MARKETING_PLAN" property="marketingPlan" jdbcType="VARCHAR"/>
		<result column="STATUS_COMMENTS" property="statusComments" jdbcType="INTEGER"/>
		<result column="SYNC_STATUS" property="syncStatus" jdbcType="BIT"/>
		<result column="ADD_TIME" property="addTime" jdbcType="BIGINT"/>
		<result column="CREATOR" property="creator" jdbcType="INTEGER"/>
		<result column="MOD_TIME" property="modTime" jdbcType="BIGINT"/>
		<result column="UPDATER" property="updater" jdbcType="INTEGER"/>
		<result column="TRADER_AREA" property="traderArea" jdbcType="VARCHAR"/>
		<result column="TAKE_TRADER_AREA" property="takeTraderArea" jdbcType="VARCHAR"/>
		<result column="INVOICE_TRADER_AREA" property="invoiceTraderArea" jdbcType="VARCHAR"/>
		<result column="IS_PAYMENT" property="isPayment" jdbcType="BIT"/>
		<result column="VERIFIES_TYPE" property="verifiesType" jdbcType="INTEGER"/>
		<result column="VERIFY_USERNAME" property="verifyUsername" jdbcType="VARCHAR"/>
		<result column="VERIFY_STATUS" property="verifyStatus" jdbcType="INTEGER"/>
		<result column="CONTRACT_STATUS" property="contractStatus" jdbcType="INTEGER"/>
		<result column="ADVANCE_PURCHASE_STATUS" property="advancePurchaseStatus" jdbcType="INTEGER"/>
		<result column="ADVANCE_PURCHASE_COMMENTS" property="advancePurchaseComments" jdbcType="VARCHAR"/>
		<result column="ADVANCE_PURCHASE_TIME" property="advancePurchaseTime" jdbcType="BIGINT"/>
		<result column="APPLY_INVOICE_TIME" property="applyInvoiceTime" jdbcType="BIGINT"/>
		<result column="IS_APPLY_INVOICE" property="isApplyInvoice" jdbcType="INTEGER"/>
		<result column="SATISFY_INVOICE_TIME" property="satisfyInvoiceTime" jdbcType="BIGINT"/>
		<result column="SATISFY_DELIVERY_TIME" property="satisfyDeliveryTime" jdbcType="BIGINT"/>
		<result column="IS_DELAY_INVOICE" property="isDelayInvoice" jdbcType="BIT"/>
		<result column="LOCKED_REASON" property="lockedReason" jdbcType="VARCHAR"/>
		<result column="COST_USER_IDS" property="costUserIds" jdbcType="VARCHAR"/>
		<!-- 收票邮箱 -->
		<result column="INVOICE_EMAIL" property="invoiceEmail" jdbcType="VARCHAR"/>
		<!-- 订单归属  userId -->
		<result column="OWNER_USER_ID" property="ownerUserId" jdbcType="INTEGER"/>

		<result column="PAY_TYPE" property="payType" jdbcType="BIT"/>
		<result column="PAYMENT_MODE" property="paymentMode" jdbcType="BIT"/>

		<result column="TRADER_AREA_ID" property="traderAreaId" jdbcType="INTEGER"/>
		<result column="TAKE_TRADER_AREA_ID" property="takeTraderAreaId" jdbcType="INTEGER"/>
		<result column="INVOICE_TRADER_AREA_ID" property="invoiceTraderAreaId" jdbcType="INTEGER"/>
		<result column="CREATE_MOBILE" property="createMobile" jdbcType="VARCHAR"/>
		<result column="BDTRADER_COMMENTS" property="bdtraderComments" jdbcType="VARCHAR"/>
		<result column="CLOSE_COMMENTS" property="closeComments" jdbcType="VARCHAR"/>
		<result column="WEB_TAKE_DELIVERY_TIME" property="webTakeDeliveryTime" jdbcType="BIGINT"/>
		<result column="IS_COUPONS" property="isCoupons" jdbcType="INTEGER"/>
		<result column="ACTION_ID" property="actionId" jdbcType="INTEGER"/>
		<result column="IS_PRINTOUT" property="isPrintout" jdbcType="INTEGER"/>
		<result column="SEND_TO_PC" property="sendToPc" jdbcType="TINYINT"/>
		<result column="DELIVERY_METHOD" property="deliveryMethod" jdbcType="INTEGER"/>
		<result column="IS_SAME_ADDRESS" property="isSameAddress" jdbcType="TINYINT"/>
		<result column="INVOICE_SEND_NODE" property="invoiceSendNode" jdbcType="TINYINT"/>

		<result column="auto_audit" property="autoAudit" jdbcType="INTEGER"/>

		<result column="CAPITAL_BILL_ID" property="capitalBillId" jdbcType="INTEGER"/>
		<result column="CAPITAL_BILL_AMOUNT" property="capitalBillAmount" jdbcType="DECIMAL"/>
		<result column="TRADER_SUBJECT" property="traderSubject" jdbcType="INTEGER"/>

		<result column="GROUP_CUSTOMER_ID" property="groupCustomerId" jdbcType="INTEGER"/>

		<result column="BILL_PERIOD_SETTLEMENT_TYPE" property="billPeriodSettlementType" jdbcType="INTEGER"/>
	</resultMap>
	<resultMap type="com.vedeng.order.model.vo.SaleorderVo" id="VoResultMap" extends="BaseResultMap4DBCenter">
		<result column="NUM" property="num" jdbcType="INTEGER"/>
		<result column="DELIVERY_NUM" property="deliveryNum" jdbcType="INTEGER"/>
		<result column="GOODS_COMMENTS" property="goodsComments" jdbcType="VARCHAR"/>
		<result column="INSIDE_COMMENTS" property="insideComments" jdbcType="VARCHAR"/>
		<result column="DELIVERY_DIRECT_COMMENTS" property="deliveryDirectComments" jdbcType="VARCHAR"/>
		<result column="GOODS_ID" property="goodsId" jdbcType="INTEGER"/>
		<result column="BUY_NUM" property="buyNum" jdbcType="INTEGER"/>
		<result column="SALEORDER_GOODS_ID" property="saleorderGoodId" jdbcType="INTEGER"/>
		<result column="DELIVERY_CYCLE" property="deliveryCycle" jdbcType="VARCHAR"/>
		<result column="PRICE" property="price" jdbcType="DECIMAL"/>
		<result column="ALL_NUM" property="allNum" jdbcType="INTEGER"/>
		<result column="ALL_DELIVERY_NUM" property="allDeliveryNum" jdbcType="INTEGER"/>
		<result column="ALL_TOTAL_AMOUNT" property="allTotalAmount" jdbcType="DECIMAL"/>
		<result column="BANK" property="bank" jdbcType="VARCHAR"/>
		<result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR"/>
		<result column="BANK_ACCOUNT" property="bankAccount" jdbcType="VARCHAR"/>
		<result column="TAX_NUM" property="taxNum" jdbcType="VARCHAR"/>
		<result column="NAME" property="name" jdbcType="VARCHAR"/>
		<result column="FAX" property="fax" jdbcType="VARCHAR"/>
		<result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
		<result column="EMAIL" property="email" jdbcType="VARCHAR"/>
		<result column="LAST_VERIFY_USERNAME" property="shName" jdbcType="VARCHAR"/>
		<result column="SEX" property="sex" jdbcType="INTEGER"/>
		<result column="SALEORDER_NUM" property="saleorderNum" jdbcType="INTEGER"/>
		<result column="SALEORDER_MONEY" property="saleorderMoney" jdbcType="DECIMAL"/>

		<result column="REG_ADDRESS" property="regAddress" jdbcType="VARCHAR"/>
		<result column="REG_TEL" property="regTel" jdbcType="VARCHAR"/>
	</resultMap>

    <resultMap type="com.vedeng.order.model.vo.SaleorderVo" id="SaleOrderVoResultMap" extends="BaseResultMap4DBCenter">
        <result column="NUM" property="num" jdbcType="INTEGER"/>
        <result column="DELIVERY_NUM" property="deliveryNum" jdbcType="INTEGER"/>
        <result column="GOODS_COMMENTS" property="goodsComments" jdbcType="VARCHAR"/>
        <result column="INSIDE_COMMENTS" property="insideComments" jdbcType="VARCHAR"/>
        <result column="DELIVERY_DIRECT_COMMENTS" property="deliveryDirectComments" jdbcType="VARCHAR"/>
        <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER"/>
        <result column="BUY_NUM" property="buyNum" jdbcType="INTEGER"/>
        <result column="SALEORDER_GOODS_ID" property="saleorderGoodId" jdbcType="INTEGER"/>
        <result column="DELIVERY_CYCLE" property="deliveryCycle" jdbcType="VARCHAR"/>
        <result column="PRICE" property="price" jdbcType="DECIMAL"/>
        <result column="ALL_NUM" property="allNum" jdbcType="INTEGER"/>
        <result column="ALL_DELIVERY_NUM" property="allDeliveryNum" jdbcType="INTEGER"/>
        <result column="ALL_TOTAL_AMOUNT" property="allTotalAmount" jdbcType="DECIMAL"/>
        <result column="BANK" property="bank" jdbcType="VARCHAR"/>
        <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR"/>
        <result column="BANK_ACCOUNT" property="bankAccount" jdbcType="VARCHAR"/>
        <result column="TAX_NUM" property="taxNum" jdbcType="VARCHAR"/>
        <result column="NAME" property="name" jdbcType="VARCHAR"/>
        <result column="FAX" property="fax" jdbcType="VARCHAR"/>
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="EMAIL" property="email" jdbcType="VARCHAR"/>
        <result column="LAST_VERIFY_USERNAME" property="shName" jdbcType="VARCHAR"/>
        <result column="SEX" property="sex" jdbcType="INTEGER"/>
        <result column="SALEORDER_NUM" property="saleorderNum" jdbcType="INTEGER"/>
        <result column="SALEORDER_MONEY" property="saleorderMoney" jdbcType="DECIMAL"/>

        <result column="REG_ADDRESS" property="regAddress" jdbcType="VARCHAR"/>
        <result column="REG_TEL" property="regTel" jdbcType="VARCHAR"/>
    </resultMap>

	<select id="selectOrderInfo4PrintContractTemplate" resultMap="VoResultMap" parameterType="com.vedeng.order.model.Saleorder">
		SELECT
			DISTINCT a.*,
			b.NAME,
			b.SEX,
			b.MOBILE,
			b.FAX,
			b.EMAIL,
			c.BANK,
			c.BANK_ACCOUNT,
			c.TAX_NUM,
			c.REG_ADDRESS,
			c.REG_TEL,
			d.LAST_VERIFY_USERNAME
		FROM T_SALEORDER a
			LEFT JOIN T_TRADER_CONTACT b ON a.TRADER_CONTACT_ID = b.TRADER_CONTACT_ID
			LEFT JOIN T_TRADER_FINANCE c ON a.TRADER_ID = c.TRADER_ID AND c.TRADER_TYPE = 1
			LEFT JOIN T_VERIFIES_INFO d ON a.SALEORDER_ID = d.RELATE_TABLE_KEY AND d.RELATE_TABLE = 'T_SALEORDER' AND d.VERIFIES_TYPE != 643
		WHERE 1 = 1
			<!-- AND a.VALID_STATUS =1 -->
			<if test="saleorderId != null and saleorderId > 0">
				AND a.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
			</if>
			<if test="saleorderNo != null and saleorderNo != ''">
				AND a.SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR}
			</if>
			AND a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		LIMIT 1
	</select>

	<select id="getSaleorderVoBySaleorderId" resultMap="SaleOrderVoResultMap" parameterType="java.lang.Integer">
		select
			a.*,
			b.BANK_CODE,
			b.TAX_NUM,
			b.REG_ADDRESS,
			b.REG_TEL,
			b.BANK,
			b.BANK_ACCOUNT,
			b.AVERAGE_TAXPAYER_URI
		from T_SALEORDER a
        left join T_TRADER_FINANCE b on a.TRADER_ID = b.TRADER_ID and b.TRADER_TYPE = 1
	    where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
  </select>

	<select id="getSaleorderInfoById"  resultType="com.vedeng.order.model.Saleorder">
    	SELECT * FROM `T_SALEORDER`  a WHERE a.`SALEORDER_ID`=#{saleorderId,jdbcType=INTEGER}
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from T_SALEORDER
        where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </select>

	<select id="getSaleorderDatas" parameterType="java.util.List" resultType="com.vedeng.finance.model.SaleorderData">
		select
		COALESCE(sum(if(a.TRADER_TYPE=1 or a.TRADER_TYPE=4,ABS(a.AMOUNT),if(a.TRADER_TYPE=2 or
		a.TRADER_TYPE=5,-ABS(a.AMOUNT),0))),0) as paymentAmount,
		b.RELATED_ID as saleorderId,
		zq.zq_amount as lackAccountPeriodAmount,
		zq.periodAmount
		from
		T_CAPITAL_BILL a
		left join
		T_CAPITAL_BILL_DETAIL b
		on
		a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		left join (
		select
		COALESCE(sum(ABS(a2.AMOUNT)),0) AS
		periodAmount,COALESCE(sum(ABS(a2.AMOUNT))-IFNULL(c.hk_amount,0)-IFNULL(d.hk_amount,0),0) as
		zq_amount,b2.RELATED_ID as zq_RELATED_ID
		from
		T_CAPITAL_BILL a2
		left join
		T_CAPITAL_BILL_DETAIL b2
		on
		a2.CAPITAL_BILL_ID = b2.CAPITAL_BILL_ID
		left join
		(
		select
		COALESCE(sum(ABS(b1.AMOUNT)),0) as hk_amount,b1.RELATED_ID
		from
		T_CAPITAL_BILL a1
		left join
		T_CAPITAL_BILL_DETAIL b1
		on
		a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
		where
		a1.TRADER_TYPE in (1,4)
		and
		b1.ORDER_TYPE = 1
		and
		b1.BUSSINESS_TYPE = 533
		group by b1.RELATED_ID
		) as c
		on
		b2.RELATED_ID = c.RELATED_ID
		left join
		(
		select
		COALESCE(sum(ABS(b1.AMOUNT)),0) as hk_amount,c1.ORDER_ID
		from
		T_CAPITAL_BILL a1
		left join
		T_CAPITAL_BILL_DETAIL b1
		on
		a1.CAPITAL_BILL_ID = b1.CAPITAL_BILL_ID
		left JOIN
		T_AFTER_SALES c1
		ON
		c1.AFTER_SALES_ID = b1.RELATED_ID and c1.SUBJECT_TYPE = 535
		where
		a1.TRADER_TYPE = 3
		and
		b1.ORDER_TYPE = 3
		and
		b1.BUSSINESS_TYPE = 533
		group by c1.ORDER_ID
		) as d
		on
		d.ORDER_ID = b2.RELATED_ID
		where
		a2.TRADER_TYPE = 3
		and
		b2.ORDER_TYPE = 1
		and
		a2.TRADER_MODE = 527
		group by b2.RELATED_ID
		) as zq
		on
		zq.zq_RELATED_ID=b.RELATED_ID
		where
		b.ORDER_TYPE = 1
		and
		b.BUSSINESS_TYPE != 533
		and
		b.RELATED_ID in
		<foreach item="saleorderId" index="index" collection="saleorderIds" open="(" separator="," close=")">
			#{saleorderId,jdbcType=INTEGER}
		</foreach>
		GROUP BY b.RELATED_ID
	</select>

	<!-- 有资金流水作为前提，避免订单关闭。使用退货数量乘退货金额计算：防止：一：部分退款；二：订单多收钱，发起退货时会将多收的钱一并退，导致退货金额变多 -->
	<select id="getNewRealAmount" parameterType="java.lang.Integer" resultType="com.vedeng.system.model.ResultAssist">
		SELECT IF(COALESCE(D.TOTAL_AMOUNT - IFNULL(E.AMOUNT, 0), 0) <![CDATA[ < ]]> 0, 0, COALESCE(D.TOTAL_AMOUNT -
		IFNULL(E.AMOUNT, 0), 0)) AS relatedDecimal,
		D.SALEORDER_ID AS relatedId
		FROM T_SALEORDER D
		LEFT JOIN
		(SELECT SUM(ABS(IFNULL(E.NUM * F.PRICE, 0))) AS AMOUNT, C.ORDER_ID
		FROM T_CAPITAL_BILL A
		INNER JOIN T_CAPITAL_BILL_DETAIL B
		ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
		INNER JOIN T_AFTER_SALES C ON B.RELATED_ID = C.AFTER_SALES_ID
		INNER JOIN T_AFTER_SALES_GOODS E
		ON C.AFTER_SALES_ID = E.AFTER_SALES_ID AND E.GOODS_TYPE = 0    <!-- 普通商品 -->
		INNER JOIN T_SALEORDER_GOODS F
		ON E.ORDER_DETAIL_ID = F.SALEORDER_GOODS_ID
		WHERE A.TRADER_TYPE <![CDATA[ <> ]]> 2    <!-- 支出 -->
		AND B.BUSSINESS_TYPE = 531    <!-- 业务类型,退货 -->
		AND A.TRADER_SUBJECT = 1    <!-- 对公 -->
		AND A.TRADER_MODE = 530    <!-- 交易方式字，退还余额 -->
		AND C.ORDER_ID IN
		<foreach collection="saleOrderIdList" open="(" close=")" item="saleOrderId" separator=",">
			#{saleOrderId,jdbcType=INTEGER}
		</foreach>
		AND C.SUBJECT_TYPE = 535    <!-- 售后类型：销售 -->
		AND C.TYPE = 539    <!-- 退货 -->
		<if test="companyId != null and companyId != 0">
			AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		</if>
		GROUP BY C.ORDER_ID) E
		ON D.SALEORDER_ID = E.ORDER_ID
		WHERE D.SALEORDER_ID IN
		<foreach collection="saleOrderIdList" open="(" close=")" item="saleOrderId" separator=",">
			#{saleOrderId,jdbcType=INTEGER}
		</foreach>
		<if test="companyId != null and companyId != 0">
			AND D.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		</if>
	</select>

	<select id="getBuyAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
  	select
  		COALESCE(sum(ABS(c.PRICE)*b.NUM),0)
  	from
  		T_SALEORDER_GOODS a
  	left join
  		T_R_BUYORDER_J_SALEORDER b
  	on
  		a.SALEORDER_GOODS_ID = b.SALEORDER_GOODS_ID
  	left join
  		T_BUYORDER_GOODS c
  	on
  		b.BUYORDER_GOODS_ID = c.BUYORDER_GOODS_ID
  	left join
  		T_BUYORDER d
  	on
  		c.BUYORDER_ID = d.BUYORDER_ID
  	where
  		a.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
  	and
  		d.STATUS != 3
  	and
  		d.VALID_STATUS = 1
  </select>

	<select id="getSaleorderIsEnd" parameterType="java.lang.Integer" resultMap="VoResultMap">
  	select
	       a.PAYMENT_STATUS,
  	       a.INVOICE_STATUS,
  	       a.ARRIVAL_STATUS,
  	       COALESCE(e.EXPRESS_ID,0) as EXPRESS_ID,
  	       a.STATUS,
  	       a.IS_SEND_INVOICE,
  	       a.INVOICE_METHOD,
  	       a.REAL_PAY_AMOUNT,
           a.REAL_TOTAL_AMOUNT
	FROM
	T_SALEORDER a
	left JOIN
	T_SALEORDER_GOODS b
	ON
	a.SALEORDER_ID = b.SALEORDER_ID and b.IS_DELETE=0 AND b.GOODS_ID NOT IN (SELECT COMMENTS FROM T_SYS_OPTION_DEFINITION WHERE PARENT_ID = 693)
	left JOIN
	T_INVOICE_DETAIL c
	ON
	b.SALEORDER_GOODS_ID = c.DETAILGOODS_ID
	left JOIN
	T_INVOICE e
	ON
	c.INVOICE_ID=e.INVOICE_ID
	left join
	(
		SELECT
			sum(bb.NUM) as tk_num,bb.ORDER_DETAIL_ID
		FROM
			T_AFTER_SALES aa
		left JOIN
			T_AFTER_SALES_GOODS bb
		ON
			aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
		left JOIN
			T_SALEORDER_GOODS cc
		ON
			bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
		WHERE
			aa.TYPE = 539
		AND
			aa.SUBJECT_TYPE = 535
		AND
			aa.VALID_STATUS = 1
		AND
			aa.ATFER_SALES_STATUS = 2
		GROUP BY
			bb.ORDER_DETAIL_ID
	) cc on b.SALEORDER_GOODS_ID = cc.ORDER_DETAIL_ID
	WHERE
	b.NUM-IFNULL(cc.tk_num,0) > 0 and
	a.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
  </select>

	<update id="updateSaleorderStatus">
  	UPDATE
  		T_SALEORDER
  	SET
  		STATUS=#{status,jdbcType=INTEGER},
  		END_TIME = #{time,jdbcType=BIGINT}
  	WHERE
  	 	SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
  </update>

	<update id="updateConfirmTimeAndStatus">
		update T_SALEORDER
		set CONFIRM_TIME=#{confirmTime},
		CONFIRM_STATUS =#{confirmStatus},
		MOD_TIME=#{modTime}
		where SALEORDER_ID = #{saleorderId}
	</update>


	<update id="updateSaleOrderContractUrl">
		update T_SALEORDER
		set MOD_TIME=#{modTime}
		,CONTRACT_URL =#{contractUrl}
		,CONTRACT_NO_STAMP_URL=#{contractNoStampUrl}
		where SALEORDER_ID = #{saleorderId}
	</update>

	<select id="getPublicPaymentAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
  	  	SELECT COALESCE(sum(ABS(a.AMOUNT)), 0)
	  	from
	  		T_CAPITAL_BILL a
		left join
			T_CAPITAL_BILL_DETAIL b
		on
			a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		where
			a.TRADER_TYPE IN (1, 4)
		and b.ORDER_TYPE = 1
		AND a.TRADER_SUBJECT = 1
		and b.RELATED_ID = #{saleorderId,jdbcType=INTEGER}
  </select>

	<select id="getRealPreAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
  	SELECT
		if(COALESCE(a.PREPAID_AMOUNT - IFNULL(abs(b.tk_amount),0),0)  <![CDATA[ < ]]> 0,0,COALESCE(a.PREPAID_AMOUNT - IFNULL(abs(b.tk_amount),0),0))
	FROM
		T_SALEORDER a
	left JOIN
	(
		SELECT
			sum(bb.NUM*cc.PRICE) as tk_amount,aa.ORDER_ID
		FROM
			T_AFTER_SALES aa
		left JOIN
			T_AFTER_SALES_GOODS bb
		ON
			aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
		left JOIN
			T_SALEORDER_GOODS cc
		ON
			bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
		WHERE
			aa.TYPE = 539
		AND
			aa.SUBJECT_TYPE = 535
		AND
			aa.VALID_STATUS = 1
		AND
			aa.ATFER_SALES_STATUS in (1,2)
		GROUP BY
			aa.ORDER_ID
	) as b
	ON
		a.SALEORDER_ID = b.ORDER_ID
	WHERE
		a.SALEORDER_ID =#{saleorderId,jdbcType=INTEGER}
  </select>

	<select id="getBuyOrderGoodsPrice" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
  	SELECT (SUM(C.PRICE * B.NUM) / SUM(B.NUM)) AS AMOUNT,D.BUYORDER_NO
	FROM T_SALEORDER_GOODS A
	     LEFT JOIN T_R_BUYORDER_J_SALEORDER B
	        ON A.SALEORDER_GOODS_ID = B.SALEORDER_GOODS_ID
	     LEFT JOIN T_BUYORDER_GOODS C
	        ON B.BUYORDER_GOODS_ID = C.BUYORDER_GOODS_ID
	     LEFT JOIN T_BUYORDER D ON C.BUYORDER_ID = D.BUYORDER_ID
	WHERE     D.STATUS <![CDATA[ <> ]]> 3
	      AND C.IS_DELETE = 0
	      AND A.SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
  </select>

	<select id="getBhOrderGoodsPrice" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
		SELECT COALESCE(SUM(C.PRICE * ABS(A.NUM)) / SUM(ABS(A.NUM)), 0) AS AMOUNT,
		D.BUYORDER_NO
		FROM T_WAREHOUSE_GOODS_OPERATE_LOG A
		JOIN T_WAREHOUSE_GOODS_OPERATE_LOG B
		ON A.BARCODE_ID = B.BARCODE_ID AND A.BARCODE_ID <![CDATA[ <> ]]> 0
		JOIN T_BUYORDER_GOODS C ON B.RELATED_ID = C.BUYORDER_GOODS_ID
		JOIN T_BUYORDER D ON C.BUYORDER_ID = D.BUYORDER_ID
		WHERE A.OPERATE_TYPE = 2
		AND B.OPERATE_TYPE = 1
		AND A.IS_ENABLE = 1
		AND B.IS_ENABLE = 1
		AND A.RELATED_ID = #{saleorderGoodsId,jdbcType=INTEGER}
		<!--  (SELECT DISTINCT C2.SALEORDER_GOODS_ID
                  FROM T_SALEORDER_GOODS C2
                       LEFT JOIN T_R_BUYORDER_J_SALEORDER B2
                          ON B2.SALEORDER_GOODS_ID = C2.SALEORDER_GOODS_ID
                  WHERE B2.SALEORDER_GOODS_ID IS NULL AND C2.SALEORDER_ID IN (1)) -->
		GROUP BY A.RELATED_ID
	</select>

	<select id="getAmountByTraderSubject" resultType="java.math.BigDecimal">
  	select
  		COALESCE(sum(if(b.BUSSINESS_TYPE=526,ABS(a.AMOUNT),-ABS(a.AMOUNT))),0)
  	from
  		T_CAPITAL_BILL a
  	left join
  		T_CAPITAL_BILL_DETAIL b on a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
  	where
		b.ORDER_TYPE = 1
	and
		b.RELATED_ID = #{saleorderId,jdbcType=INTEGER}
	and
		a.TRADER_SUBJECT = #{traderSubject,jdbcType=INTEGER}
  </select>

	<select id="getBuyOrderNoBySaleorderGoodsId" parameterType="java.lang.Integer"
			resultType="com.vedeng.order.model.Buyorder">
  	SELECT D.BUYORDER_ID,D.BUYORDER_NO
	FROM T_SALEORDER_GOODS A
	     LEFT JOIN T_R_BUYORDER_J_SALEORDER B
	        ON A.SALEORDER_GOODS_ID = B.SALEORDER_GOODS_ID
	     LEFT JOIN T_BUYORDER_GOODS C
	        ON B.BUYORDER_GOODS_ID = C.BUYORDER_GOODS_ID
	     LEFT JOIN T_BUYORDER D ON C.BUYORDER_ID = D.BUYORDER_ID
	WHERE     D.STATUS <![CDATA[ <> ]]> 3
	      AND C.IS_DELETE = 0
	      AND A.SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
  </select>

	<select id="getBhOrderNoBySaleorderGoodsId" parameterType="java.lang.Integer"
			resultType="com.vedeng.order.model.Buyorder">
		SELECT D.BUYORDER_ID,D.BUYORDER_NO
		FROM T_WAREHOUSE_GOODS_OPERATE_LOG A
		JOIN T_WAREHOUSE_GOODS_OPERATE_LOG B
		ON A.BARCODE_ID = B.BARCODE_ID AND A.BARCODE_ID <![CDATA[ <> ]]> 0
		JOIN T_BUYORDER_GOODS C ON B.RELATED_ID = C.BUYORDER_GOODS_ID
		JOIN T_BUYORDER D ON C.BUYORDER_ID = D.BUYORDER_ID
		WHERE A.OPERATE_TYPE = 2
		AND B.OPERATE_TYPE = 1
		AND A.IS_ENABLE = 1
		AND B.IS_ENABLE = 1
		AND A.RELATED_ID = #{saleorderGoodsId,jdbcType=INTEGER}
		GROUP BY A.RELATED_ID
	</select>

	<!-- 订单已收款金额 = （业务类型==订单收款）的流水总额-（业务类型==退款 && 交易类型==转出）-支付宝提现的流水总额； -->
	<select id="getReceivedAmount" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
		SELECT
			COALESCE (
				sum(a.AMOUNT) - IFNULL(c.amount_tk, 0) - IFNULL(d.amount_zfb, 0),
				0
			)
		FROM
			T_CAPITAL_BILL a
		LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		LEFT JOIN (
			SELECT
				COALESCE (sum(ABS(aa.AMOUNT)), 0) AS amount_tk,
				cc.ORDER_ID AS RELATED_ID
			FROM
				T_CAPITAL_BILL aa
			JOIN T_CAPITAL_BILL_DETAIL bb ON aa.CAPITAL_BILL_ID = bb.CAPITAL_BILL_ID
			LEFT JOIN T_AFTER_SALES cc ON bb.RELATED_ID = cc.AFTER_SALES_ID
			WHERE
				bb.BUSSINESS_TYPE = 531
			AND bb.ORDER_TYPE = 3
			AND aa.TRADER_TYPE = 5
			AND cc.VALID_STATUS = 1
			AND cc.SUBJECT_TYPE = 535
			AND cc.ORDER_ID = #{saleorderId,jdbcType=INTEGER}
			GROUP BY
				cc.ORDER_ID
		) c ON b.RELATED_ID = c.RELATED_ID
		LEFT JOIN (
			SELECT
				COALESCE (sum(ABS(a.AMOUNT)), 0) AS amount_zfb,
				b.RELATED_ID
			FROM
				T_CAPITAL_BILL a
			JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
			WHERE
				b.BUSSINESS_TYPE = 679
			AND b.RELATED_ID = #{saleorderId,jdbcType=INTEGER}
			GROUP BY
				b.RELATED_ID
		) d ON b.RELATED_ID = d.RELATED_ID
		WHERE
			b.BUSSINESS_TYPE IN (526,531)
		AND b.ORDER_TYPE = 1
		AND b.RELATED_ID = #{saleorderId,jdbcType=INTEGER}
  </select>

	<!-- 照搬数据导出模块的查询2018-07-17 duke -->
	<select id="getBuyPriceOrderAmount" parameterType="java.util.List"
			resultType="com.vedeng.order.model.vo.BuyorderGoodsVo">
		SELECT
		COALESCE(B.p, 0) AS buyOrderPriceStr,
		COALESCE(B.N, 0) AS buyOrderNumStr,
		COALESCE(B.relatedStrFour) AS buyorderGoodsId,
		IFNULL(B.relatedStr, '') AS buyOrderNoStr,
		IFNULL(B.relatedStrFive, '') AS buyOrderIdStr,
		A.SALEORDER_GOODS_ID AS saleorderGoodsId
		FROM
		T_SALEORDER_GOODS A
		JOIN (
		SELECT
		(C.PRICE) AS p,
		(B.NUM) AS N,
		(C.BUYORDER_GOODS_ID) AS relatedStrFour,
		( D.BUYORDER_NO) AS relatedStr,
		(D.CREATOR) AS relatedStrTwo,
		(D.TRADER_NAME) AS relatedStrThree,
		( D.BUYORDER_ID) AS relatedStrFive,
		A.SALEORDER_GOODS_ID t
		FROM
		T_SALEORDER_GOODS A
		LEFT JOIN T_R_BUYORDER_J_SALEORDER B ON A.SALEORDER_GOODS_ID = B.SALEORDER_GOODS_ID
		LEFT JOIN T_BUYORDER_GOODS C ON B.BUYORDER_GOODS_ID = C.BUYORDER_GOODS_ID
		LEFT JOIN T_BUYORDER D ON C.BUYORDER_ID = D.BUYORDER_ID
		WHERE
		D. STATUS != 3
		AND C.IS_DELETE = 0
		AND A.SALEORDER_GOODS_ID IN
		<foreach collection="saleOrderGoodsIdList" item="saleorderGoodsId" open="(" close=")" separator=",">
			#{saleorderGoodsId,jdbcType=INTEGER}
		</foreach>
		) B ON B.t = A.SALEORDER_GOODS_ID

		WHERE
		A.SALEORDER_GOODS_ID IN

		<foreach collection="saleOrderGoodsIdList" item="saleorderGoodsId" open="(" close=")" separator=",">
			#{saleorderGoodsId,jdbcType=INTEGER}
		</foreach>
	</select>

	<select id="getPublicAmount" resultType="java.math.BigDecimal">
		SELECT COALESCE(sum(if(b.BUSSINESS_TYPE = 526, ABS(a.AMOUNT), -ABS(a.AMOUNT))), 0)
		FROM T_CAPITAL_BILL a
		LEFT JOIN T_CAPITAL_BILL_DETAIL b
		ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		WHERE b.ORDER_TYPE = 1
		AND b.RELATED_ID = #{saleorderId,jdbcType=INTEGER}
		<if test="traderSubject!=null">
			AND a.TRADER_SUBJECT = #{traderSubject,jdbcType=INTEGER}
		</if>
		AND b.BUSSINESS_TYPE <![CDATA[ <> ]]> 533 <!-- 信用还款 -->
	</select>

	<!-- 实际已收款金额,对公+银行+（交易客户名称一致） -->
	<select id="getOpenInvoiceOrderAmount" resultType="java.math.BigDecimal">
		SELECT COALESCE(SUM(if(b.BUSSINESS_TYPE = 526, ABS(a.AMOUNT), -ABS(a.AMOUNT))), 0) AS AMOUNT
		FROM T_CAPITAL_BILL a
		LEFT JOIN T_CAPITAL_BILL_DETAIL b
		ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
		WHERE     b.ORDER_TYPE = 1  <!-- 销售订单 -->
		AND b.RELATED_ID = #{saleorderId,jdbcType=INTEGER}
		AND a.TRADER_SUBJECT = 1  <!-- 对公 -->
		<!-- AND a.TRADER_MODE = 521  银行 -->
		AND a.PAYER = #{traderName,jdbcType=VARCHAR}
		AND b.BUSSINESS_TYPE <![CDATA[ <> ]]> 533 <!-- 信用还款 -->
	</select>

	<select id="getRealPreAmountForHcOrder" parameterType="java.lang.Integer" resultType="java.math.BigDecimal">
  	SELECT
		if(COALESCE(a.PREPAID_AMOUNT - IFNULL(abs(b.tk_amount),0),0)  <![CDATA[ < ]]> 0,0,COALESCE(a.PREPAID_AMOUNT - IFNULL(abs(b.tk_amount),0),0))
	FROM
		T_SALEORDER a
	left JOIN
	(
		SELECT
			sum(bb.SKU_REFUND_AMOUNT) as tk_amount,aa.ORDER_ID
		FROM
			T_AFTER_SALES aa
		left JOIN
			T_AFTER_SALES_GOODS bb
		ON
			aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
		left JOIN
			T_SALEORDER_GOODS cc
		ON
			bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
		WHERE
			aa.TYPE = 539
		AND
			aa.SUBJECT_TYPE = 535
		AND
			aa.VALID_STATUS = 1
		AND
			aa.ATFER_SALES_STATUS in (1,2)
		GROUP BY
			aa.ORDER_ID
	) as b
	ON
		a.SALEORDER_ID = b.ORDER_ID
	WHERE
		a.SALEORDER_ID =#{saleorderId,jdbcType=INTEGER}
  </select>




	<select id="getBuyorderGoodsNum" resultType="java.lang.Integer">
		SELECT
			COALESCE(SUM(B.NUM),0)
		FROM T_R_BUYORDER_J_SALEORDER B
				 LEFT JOIN T_BUYORDER_GOODS C ON B.BUYORDER_GOODS_ID = C.BUYORDER_GOODS_ID
				 LEFT JOIN T_BUYORDER D ON C.BUYORDER_ID = D.BUYORDER_ID
		WHERE D.STATUS <![CDATA[ <> ]]> 3 AND C.IS_DELETE = 0 AND B.SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
	</select>
	<select id="getAfterSaleCnt" resultType="java.lang.Integer">
		SELECT
			COUNT(*)
		FROM
			T_AFTER_SALES
		WHERE
			ORDER_ID = #{saleorderId,jdbcType=INTEGER}
		  AND ATFER_SALES_STATUS in(0,1) AND TYPE IN(539,543,540)
	</select>

	<select id="selectAttributableSalesById" resultType="com.vedeng.authorization.model.User">
		SELECT
			distinct h.*
		FROM T_SALEORDER f
			LEFT JOIN T_R_TRADER_J_USER g ON f.TRADER_ID = g.TRADER_ID and g.TRADER_TYPE = 1
            LEFT JOIN T_USER h ON g.`USER_ID` = h.`USER_ID`
		WHERE
			f.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</select>
    <select id="getSaleOrderGoodsExceptFreight" resultType="com.vedeng.order.model.SaleorderGoods">
		SELECT SKU, NUM
		FROM T_SALEORDER_GOODS
		WHERE SALEORDER_ID = #{saleorderId,jdbcType=INTEGER} AND SKU != 'V127063' AND IS_DELETE = 0
	</select>

	<select id="getSaleorderNoList" resultType="java.lang.String">
		SELECT
			SALEORDER_NO
		FROM
			T_SALEORDER
		WHERE
			SALEORDER_ID IN ( SELECT DISTINCT SALEORDER_ID FROM T_SALEORDER_GOODS WHERE SALEORDER_GOODS_ID IN
		<foreach
				collection="saleorderGoodsIds"
				item="saleorderGoodsId"
				open="("
				separator=","
				close=")">
			#{saleorderGoodsId,jdbcType=INTEGER}
		</foreach>
			)
	</select>

    <select id="getLockedStatusBySaleorderIds" resultType="java.lang.Integer">
		SELECT DISTINCT
			LOCKED_STATUS
		FROM
			T_SALEORDER
		WHERE
			SALEORDER_ID IN
			<foreach
				collection="saleOrderIdList"
				item="saleorderId"
				open="("
				separator=","
				close=")">
			#{saleorderId,jdbcType=INTEGER}
			</foreach>
	</select>
    <select id="isAllSpecialGoods" parameterType="java.lang.Integer" resultType="java.lang.Integer">
	SELECT COUNT(*) AS NUM FROM T_SALEORDER_GOODS WHERE
  	GOODS_ID NOT IN (
  		SELECT COMMENTS FROM T_SYS_OPTION_DEFINITION WHERE PARENT_ID = 693
  	) AND
  	SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</select>
	<!-- 获取销售订单财务信息 -->
	<select id="getSaleorderFinanceInfo" resultType="com.vedeng.order.model.vo.SaleorderVo"
			parameterType="com.vedeng.order.model.vo.SaleorderVo">
		SELECT A.SALEORDER_ID,
		       A.INVOICE_TYPE,
		       A.TRADER_ID,
		       A.TRADER_NAME,
		       A.ORDER_TYPE,
		       B.TAX_NUM,
		       B.REG_ADDRESS,
		       B.REG_TEL,
		       B.BANK,
		       B.BANK_ACCOUNT,
		       B.AVERAGE_TAXPAYER_URI,
		       B.BANK_CODE,
		       D.REGION_TYPE,
		       V.STATUS AS FINANCE_CHECK_STATUS,
		       T.SOURCE AS TRADER_SOURCE
		FROM T_SALEORDER A
		     LEFT JOIN T_TRADER T ON A.TRADER_ID=T.TRADER_ID
		     LEFT JOIN T_TRADER_FINANCE B
		        ON A.TRADER_ID = B.TRADER_ID AND B.TRADER_TYPE = 1
		     LEFT JOIN T_VERIFIES_INFO V
		        ON V.RELATE_TABLE_KEY = B.TRADER_FINANCE_ID AND V.RELATE_TABLE='T_TRADER_FINANCE'
		     LEFT JOIN T_TRADER_ADDRESS C
		        ON     A.TRADER_ID = C.TRADER_ID
		           AND A.INVOICE_TRADER_ADDRESS_ID = C.TRADER_ADDRESS_ID
		           AND C.TRADER_TYPE = 1
		           AND C.IS_ENABLE = 1
		     LEFT JOIN T_REGION D ON C.AREA_ID = D.REGION_ID
		WHERE A.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
		limit 1
	</select>
	<select id="getRealTotalAmountExceptAfterSalesFinished" resultType="java.math.BigDecimal">
		SELECT if(COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE IN(1, 5),b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0) <![CDATA[ < ]]> 0,
		0,
		COALESCE(a.TOTAL_AMOUNT - IFNULL(abs(if(a.ORDER_TYPE IN(1, 5),b.SKU_REFUND_AMOUNT,  b.tk_amount)), 0), 0))
		FROM T_SALEORDER a
		LEFT JOIN
		(SELECT sum(bb.NUM * cc.PRICE) AS tk_amount,
		sum(bb.SKU_REFUND_AMOUNT) AS SKU_REFUND_AMOUNT,
		aa.ORDER_ID
		FROM T_AFTER_SALES aa
		LEFT JOIN T_AFTER_SALES_GOODS bb
		ON aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
		LEFT JOIN T_SALEORDER_GOODS cc
		ON bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
		WHERE     aa.TYPE = 539
		AND aa.SUBJECT_TYPE = 535
		AND aa.VALID_STATUS = 1
		AND aa.ATFER_SALES_STATUS = 2
		AND bb.GOODS_TYPE = 0 <!-- VDERP-7492 计算实际金额，需要剔除手续费 -->
		AND aa.ORDER_ID = #{saleorderId,jdbcType=INTEGER}
		GROUP BY aa.ORDER_ID) AS b
		ON a.SALEORDER_ID = b.ORDER_ID
		WHERE a.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</select>
	<select id="getSaleorderGoodsId" resultType="com.vedeng.order.model.vo.SaleorderVo">
		SELECT GOODS_NAME goodsName,SALEORDER_GOODS_ID saleorderGoodsId,CONFIRM_NUMBER confirmNumber
		from T_SALEORDER_GOODS
		where SALEORDER_ID in (
			select SALEORDER_ID from T_SALEORDER s
			where
			s.TRADER_ID = #{traderId}
			AND s.TRADER_CONTACT_MOBILE = #{traderContactMobile}

			 AND s.IS_DELETE = 0
		)
		/* 剔除特殊产品所在记录   */
		AND SKU not in ('V127063','V251526','V256675','V253620','V251462','V140633')
		and IFNULL(CONFIRM_NUMBER,0) >0
		AND IS_DELETE = 0
	</select>

	<select id="getSaleOrderGoodsListByOrderIdList" resultType="com.vedeng.order.model.SaleorderGoods">

		SELECT
		B.SKU as sku,
	    B.GOODS_ID as goodsId,
		B.SALEORDER_ID as saleorderId,
		B.SALEORDER_GOODS_ID as saleorderGoodsId,
		C.SHOW_NAME as goodsName,
		E.BRAND_NAME as brandName,
		C.MODEL as  model,
		B.IS_GIFT as isGift,
		F.UNIT_NAME as  unitName,
		C.CHECK_STATUS as checkStatus,
		IFNULL(G.LEVEL_NAME, '无')  AS goodsLevelName,
		IFNULL(H.POSITION_NAME, '无档位') AS goodsPositionName
		FROM
		(SELECT SKU,MAX(SALEORDER_ID) SALEORDER_ID  FROM T_SALEORDER_GOODS
			WHERE SALEORDER_ID IN
		<foreach collection="list" item="saleorderId" open="(" close=")" separator=",">
			#{saleorderId, jdbcType=INTEGER}
		</foreach>
			GROUP BY SKU
		) A
		LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID = B.SALEORDER_ID AND A.SKU = B.SKU
		LEFT JOIN V_CORE_SKU C ON B.SKU = C.SKU_NO
		LEFT JOIN V_CORE_SPU D ON C.SPU_ID = D.SPU_ID
		LEFT JOIN T_BRAND E ON D.BRAND_ID = E.BRAND_ID
		LEFT JOIN T_UNIT F ON C.BASE_UNIT_ID = F.UNIT_ID
		LEFT JOIN V_GOODS_LEVEL G ON C.GOODS_LEVEL_NO = G.ID
		LEFT JOIN V_GOODS_POSITION H ON C.GOODS_POSITION_NO = H.ID
		WHERE C.CHECK_STATUS = 3 AND C.IS_AVAILABLE_SALE = 1 AND B.IS_DELETE=0 AND B.IS_IGNORE=0
		ORDER BY B.SALEORDER_ID DESC

	</select>
    <select id="getSaleorderpayAmountByTime" resultType="java.math.BigDecimal">
		SELECT
			IFNULL(SUM(A.skamount),0)-IFNULL(SUM(C.tkamount),0)
		FROM T_SALEORDER SO
		LEFT JOIN (
		select sum(CB.AMOUNT) as skamount,CBD.RELATED_ID
		from T_CAPITAL_BILL_DETAIL CBD
		LEFT JOIN T_CAPITAL_BILL CB ON CBD.CAPITAL_BILL_ID = CB.CAPITAL_BILL_ID
		WHERE CBD.ORDER_TYPE = 1
		AND CBD.BUSSINESS_TYPE = 526
		AND CB.TRADER_TYPE IN (1,4)
		AND CB.TRADER_MODE IN (520,521,522,523,528)
		AND CB.TRADER_TIME <![CDATA[<=]]> #{endTime,jdbcType = INTEGER}
		AND CB.TRADER_TIME <![CDATA[>=]]> #{startTime,jdbcType = INTEGER}
		GROUP BY CBD.RELATED_ID) A ON A.RELATED_ID = SO.SALEORDER_ID

		LEFT JOIN (
		select sum(ABS(CB.AMOUNT)) tkamount,CBD.RELATED_ID
		from T_CAPITAL_BILL_DETAIL CBD
		LEFT JOIN T_CAPITAL_BILL CB ON CBD.CAPITAL_BILL_ID = CB.CAPITAL_BILL_ID
		WHERE CBD.ORDER_TYPE = 1
		AND CB.TRADER_TYPE IN (2,5)
		AND CBD.BUSSINESS_TYPE = 531
		AND CB.TRADER_MODE IN (521,520,530)
		AND CB.TRADER_TIME <![CDATA[<=]]> #{endTime,jdbcType = INTEGER}
		AND CB.TRADER_TIME <![CDATA[>=]]> #{startTime,jdbcType = INTEGER}
		GROUP BY CBD.RELATED_ID) C ON C.RELATED_ID = SO.SALEORDER_ID
		LEFT JOIN T_R_TRADER_J_USER TJU ON SO.TRADER_ID = TJU.TRADER_ID AND TJU.TRADER_TYPE = 1
		WHERE TJU.USER_ID IN
		<foreach collection="subUserIdList" separator="," open="(" close=")" item="id">
			#{id,jdbcType=INTEGER}
		</foreach>
		AND SO.ORDER_TYPE IN ( 0, 1, 5, 7, 8, 9)

	</select>

	<update id="updateQuoteorderIdById" parameterType="com.vedeng.order.model.Saleorder">
		update T_SALEORDER
		set QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
		where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}

	</update>

	<select id="getSaleOrderByOrderNoAndTraderId" resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List"/>
		FROM T_SALEORDER
		WHERE SALEORDER_NO = #{saleOrderNo,jdbcType=VARCHAR} AND TRADER_ID = #{traderId,jdbcType=INTEGER}
	</select>

    <update id="clearPeriod">
		update  T_CUSTOMER_BILL_PERIOD_APPLY set COMPANY_ID = 6  where COMPANY_ID = 1 and
		CREATOR in (select user_id from T_USER where USERNAME in (${testUserNames}))
	</update>
	<update id="clearInvoice"  >
		update  T_INVOICE set COMPANY_ID = 6  where CREATOR IN (select user_id from T_USER where USERNAME  in (${testUserNames}) ) and COMPANY_ID = 1
	</update>
	<update id="clearReceiveInvoice">
		update T_INVOICE_APPLY set COMPANY_ID = 6 where VALID_USERID IN (select user_id from T_USER where USERNAME in (${testUserNames}) ) and COMPANY_ID = 1
	</update>
	<update id="urgeDeliveryOrder">
		update T_BUYORDER set URGED_MAINTAIN_BATCH_INFO = 1 where BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
	</update>

	<select id="getNotHcSaleOrderInfo" resultType="com.vedeng.order.model.Saleorder">
		SELECT
		     <include refid="Base_Column_List"/>
			FROM T_SALEORDER
		WHERE ORDER_TYPE != 5 AND SALEORDER_ID IN
		<foreach collection="list" item="saleorderId" open="(" close=")" separator=",">
			#{saleorderId, jdbcType=INTEGER}
		</foreach>
	</select>
	<select id="getReceivedAmountByTime" resultType="java.math.BigDecimal">
		SELECT
		IFNULL(SUM(A.skamount),0)
		FROM T_SALEORDER SO
		LEFT JOIN (
		select sum(CB.AMOUNT) as skamount,CBD.RELATED_ID
		from T_CAPITAL_BILL_DETAIL CBD
		LEFT JOIN T_CAPITAL_BILL CB ON CBD.CAPITAL_BILL_ID = CB.CAPITAL_BILL_ID
		WHERE CBD.ORDER_TYPE = 1
		AND CBD.BUSSINESS_TYPE = 526
		AND CB.TRADER_MODE IN (520,521,522,523,528)
		AND CB.TRADER_TIME <![CDATA[<=]]> #{endtime,jdbcType = INTEGER}
		AND CB.TRADER_TIME <![CDATA[>=]]> #{starttime,jdbcType = INTEGER}
		GROUP BY CBD.RELATED_ID) A ON A.RELATED_ID = SO.SALEORDER_ID
		LEFT JOIN T_R_TRADER_J_USER TJU ON SO.TRADER_ID = TJU.TRADER_ID AND TJU.TRADER_TYPE = 1
		WHERE TJU.USER_ID IN
		<foreach collection="subUserIdList" separator="," open="(" close=")" item="id">
			#{id,jdbcType=INTEGER}
		</foreach>
		AND SO.ORDER_TYPE IN ( 0, 1, 5, 7, 8, 9)
	</select>
	<select id="getReturnAmountByTime" resultType="java.math.BigDecimal">
		SELECT
		IFNULL(SUM(C.tkamount),0)
		FROM T_AFTER_SALES ASS
		INNER JOIN T_SALEORDER SO ON SO.SALEORDER_ID = ASS.ORDER_ID
		LEFT JOIN (
		select sum(ABS(CB.AMOUNT)) tkamount,CBD.RELATED_ID
		from T_CAPITAL_BILL_DETAIL CBD
		LEFT JOIN T_CAPITAL_BILL CB ON CBD.CAPITAL_BILL_ID = CB.CAPITAL_BILL_ID
		WHERE CBD.ORDER_TYPE = 3
		AND CBD.BUSSINESS_TYPE IN (531,679)
		AND CB.TRADER_MODE IN (530)
		AND CB.TRADER_TIME <![CDATA[<=]]> #{endtime,jdbcType = INTEGER}
		AND CB.TRADER_TIME <![CDATA[>=]]> #{starttime,jdbcType = INTEGER}
		GROUP BY CBD.RELATED_ID) C ON C.RELATED_ID =ASS.AFTER_SALES_ID
		LEFT JOIN T_R_TRADER_J_USER TJU ON SO.TRADER_ID = TJU.TRADER_ID AND TJU.TRADER_TYPE = 1
		WHERE TJU.USER_ID IN
		<foreach collection="subUserIdList" separator="," open="(" close=")" item="id">
			#{id,jdbcType=INTEGER}
		</foreach>
		AND SO.ORDER_TYPE IN ( 0, 1, 5, 7, 8, 9)
		AND ASS.SUBJECT_TYPE = 535
	</select>

	<select id="getCloseAndVerifyBh" resultType="java.lang.Integer">
		SELECT
			S.SALEORDER_ID
		FROM
			T_SALEORDER S
		LEFT JOIN T_VERIFIES_INFO bh ON S.SALEORDER_ID = bh.RELATE_TABLE_KEY
			AND bh.RELATE_TABLE = "T_SALEORDER"
			AND bh.VERIFIES_TYPE = 620
		WHERE
			S.`STATUS` = 3
			AND bh.`STATUS` = 0
			AND S.ORDER_TYPE = 2
	</select>

    <select id="getTraderOrderCountByRecentDaysList" resultType="com.vedeng.order.model.dto.CustomerCountDto">
		SELECT
			T4.TRADER_CUSTOMER_ID,
			T3.MYCOUNT
		FROM
			(
			SELECT
				T2.ASSOCIATED_CUSTOMER_GROUP,
				COUNT( 1 ) MYCOUNT
			FROM
				T_SALEORDER T1
				LEFT JOIN T_TRADER_CUSTOMER T2 ON T1.TRADER_ID = T2.TRADER_ID
			WHERE
				T1.VALID_STATUS = 1
				AND T1.ADD_TIME >= unix_timestamp( now( ) ) * 1000 - #{validOrderDays, jdbcType=INTEGER} * 24 * 60 * 60 * 1000
				AND T2.TRADER_CUSTOMER_ID IS NOT NULL
				AND T2.ASSOCIATED_CUSTOMER_GROUP > 0
			GROUP BY
				T2.ASSOCIATED_CUSTOMER_GROUP
			) T3
			LEFT JOIN T_TRADER_CUSTOMER T4 ON T3.ASSOCIATED_CUSTOMER_GROUP = T4.ASSOCIATED_CUSTOMER_GROUP
        WHERE
	        T4.TRADER_CUSTOMER_ID IN
        <foreach collection="customerIds" item="customerId" open="(" close=")" separator=",">
            #{customerId, jdbcType=INTEGER}
        </foreach>
        UNION ALL
		SELECT
			T12.TRADER_CUSTOMER_ID,
			COUNT( 1 ) MYCOUNT
		FROM
			T_SALEORDER T11
			LEFT JOIN T_TRADER_CUSTOMER T12 ON T11.TRADER_ID = T12.TRADER_ID
		WHERE
			T11.VALID_STATUS = 1
			AND T11.ADD_TIME >= unix_timestamp( now( ) ) * 1000 - #{validOrderDays, jdbcType=INTEGER} * 24 * 60 * 60 * 1000
			AND T12.TRADER_CUSTOMER_ID IS NOT NULL
			AND T12.ASSOCIATED_CUSTOMER_GROUP = 0
            AND T12.TRADER_CUSTOMER_ID IN
            <foreach collection="customerIds" item="customerId" open="(" close=")" separator=",">
                #{customerId, jdbcType=INTEGER}
            </foreach>
		GROUP BY
			T12.TRADER_CUSTOMER_ID
	</select>
    <select id="queryUrgeDeliveryOrder" resultType="com.vedeng.order.model.Buyorder" parameterType="java.lang.Integer">
            SELECT
            DISTINCT b.BUYORDER_ID bid,b.*
            FROM
                T_SALEORDER s
                LEFT JOIN T_SALEORDER_GOODS sg ON s.SALEORDER_ID = sg.SALEORDER_ID
                LEFT JOIN T_R_BUYORDER_J_SALEORDER rbs ON sg.SALEORDER_GOODS_ID = rbs.SALEORDER_GOODS_ID
                LEFT JOIN T_BUYORDER_GOODS bg ON rbs.BUYORDER_GOODS_ID = bg.BUYORDER_GOODS_ID
                LEFT JOIN T_BUYORDER b ON bg.BUYORDER_ID = b.BUYORDER_ID
            WHERE
                s.DELIVERY_STATUS = 2
                AND sg.DELIVERY_DIRECT = 1
                and b.VALID_STATUS = 1
                AND s.`STATUS` IN ( 1, 2 )
                AND b.status in (1,2)
                and s.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
	</select>
	<select id="getFirstOrderOfTraderAfterSomeTime" resultType="com.vedeng.order.model.Saleorder">
		select *
		from T_SALEORDER where TRADER_ID = #{traderId} and VALID_STATUS = 1 and ADD_TIME >= #{addTime} limit 1
	</select>

	<!-- 销售订单自动关闭相关查询 -->
	<select id="getOrdersForAutoClose" resultMap="BaseResultMap" parameterType="java.util.Map">
		SELECT
			s.SALEORDER_ID, s.SALEORDER_NO, s.TRADER_NAME, s.VALID_TIME, s.VALID_STATUS,
			s.PAYMENT_STATUS, s.INVOICE_STATUS, s.STATUS, u.USERNAME, u.NUMBER
		FROM T_SALEORDER s
		LEFT JOIN T_USER u ON s.USER_ID = u.USER_ID
		WHERE s.VALID_STATUS = #{validStatus}
		  AND s.PAYMENT_STATUS = #{paymentStatus}
		  AND s.INVOICE_STATUS = #{invoiceStatus}
		  AND s.STATUS = #{status}
		  AND s.VALID_TIME &lt; #{validTimeBefore}
		  AND s.ORDER_TYPE != 2
		ORDER BY s.VALID_TIME ASC
	</select>

	<select id="getOrdersForWarning" resultMap="BaseResultMap" parameterType="java.util.Map">
		SELECT
			s.SALEORDER_ID, s.SALEORDER_NO, s.TRADER_NAME, s.VALID_TIME, s.VALID_STATUS,
			s.PAYMENT_STATUS, s.INVOICE_STATUS, s.STATUS,  u.USERNAME, u.NUMBER
		FROM T_SALEORDER s
		LEFT JOIN T_USER u ON s.USER_ID = u.USER_ID
		WHERE s.VALID_STATUS = #{validStatus}
		  AND s.PAYMENT_STATUS = #{paymentStatus}
		  AND s.INVOICE_STATUS = #{invoiceStatus}
		  AND s.STATUS = #{status}
		  AND s.VALID_TIME &lt; #{validTimeBefore}
		  AND s.VALID_TIME &gt;= #{validTimeAfter}
		  AND s.ORDER_TYPE != 2
		ORDER BY s.VALID_TIME ASC
	</select>

	<select id="getHistoricalOrdersToProcess" resultMap="BaseResultMap" parameterType="java.util.Map">
		SELECT
			s.SALEORDER_ID, s.SALEORDER_NO, s.TRADER_NAME, s.VALID_TIME, s.VALID_STATUS,
			s.PAYMENT_STATUS, s.INVOICE_STATUS, s.STATUS, u.USERNAME, u.NUMBER
		FROM T_SALEORDER s
		LEFT JOIN T_USER u ON s.USER_ID = u.USER_ID
		WHERE s.VALID_STATUS = #{validStatus}
		  AND s.PAYMENT_STATUS = #{paymentStatus}
		  AND s.INVOICE_STATUS = #{invoiceStatus}
		  AND s.STATUS = #{status}
		  AND s.ORDER_TYPE != 2
		ORDER BY s.VALID_TIME ASC
	</select>

	<select id="getOrdersByOrderNoList" resultMap="BaseResultMap" parameterType="java.util.List">
		SELECT
			s.SALEORDER_ID, s.SALEORDER_NO, s.TRADER_NAME, s.VALID_TIME, s.VALID_STATUS,
			s.PAYMENT_STATUS, s.INVOICE_STATUS, s.STATUS, u.USERNAME, u.NUMBER
		FROM T_SALEORDER s
		LEFT JOIN T_USER u ON s.USER_ID = u.USER_ID
		WHERE s.SALEORDER_NO IN
		<foreach collection="orderNoList" item="orderNo" open="(" close=")" separator=",">
			#{orderNo, jdbcType=VARCHAR}
		</foreach>
		  AND s.VALID_STATUS = 1
		  AND s.PAYMENT_STATUS = 0
		  AND s.INVOICE_STATUS = 0
		  AND s.STATUS = 1
		  AND s.ORDER_TYPE != 2
		ORDER BY s.VALID_TIME ASC
	</select>

	<select id="getOrdersByOrderNoListForWarning" resultMap="BaseResultMap" parameterType="java.util.Map">
		SELECT
			s.SALEORDER_ID, s.SALEORDER_NO, s.TRADER_NAME, s.VALID_TIME, s.VALID_STATUS,
			s.PAYMENT_STATUS, s.INVOICE_STATUS, s.STATUS, u.USERNAME, u.NUMBER
		FROM T_SALEORDER s
		LEFT JOIN T_USER u ON s.USER_ID = u.USER_ID
		WHERE s.SALEORDER_NO IN
		<foreach collection="orderNoList" item="orderNo" open="(" close=")" separator=",">
			#{orderNo, jdbcType=VARCHAR}
		</foreach>
		  AND s.VALID_STATUS = #{validStatus}
		  AND s.PAYMENT_STATUS = #{paymentStatus}
		  AND s.INVOICE_STATUS = #{invoiceStatus}
		  AND s.STATUS = #{status}
		  AND s.ORDER_TYPE != 2
		ORDER BY s.VALID_TIME ASC
	</select>
	<select id="getRealTotalAmountBySaleorderIdList" resultType="java.math.BigDecimal">
		select ifnull(sum(REAL_TOTAL_AMOUNT),0)
		from T_SALEORDER where SALEORDER_ID in
		<foreach collection="saleorderIdList" index="index" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</select>

	<select id="getOrderNumberByPhone" resultType="java.lang.Integer">
		SELECT
			COUNT( 1 )
		FROM
			T_SALEORDER
		WHERE
			(
					INVOICE_TRADER_CONTACT_MOBILE = #{phone,jdbcType=VARCHAR}
					OR INVOICE_TRADER_CONTACT_TELEPHONE = #{phone,jdbcType=VARCHAR}
					OR TAKE_TRADER_CONTACT_MOBILE = #{phone,jdbcType=VARCHAR}
					OR TAKE_TRADER_CONTACT_TELEPHONE = #{phone,jdbcType=VARCHAR}
					OR TRADER_CONTACT_MOBILE = #{phone,jdbcType=VARCHAR}
					OR TRADER_CONTACT_TELEPHONE = #{phone,jdbcType=VARCHAR}
				)
		  AND ADD_TIME > UNIX_TIMESTAMP(date_sub( curdate(), INTERVAL + 2 YEAR )) * 1000
	</select>

	<select id="getQuoteOrderListPage" resultType="com.vedeng.order.model.SimpleBdOrderChooseRes">
		SELECT S.SALEORDER_ID,S.SALEORDER_NO,S.TRADER_ID,S.TRADER_NAME,S.TRADER_CONTACT_NAME,S.TRADER_CONTACT_MOBILE,
		S.REAL_TOTAL_AMOUNT AS REAL_AMOUNT,S.TOTAL_AMOUNT,
		S.STATUS,S.ADD_TIME,U.USERNAME AS OPT_USERNAME
		FROM T_SALEORDER S
		LEFT JOIN T_SALEORDER_GOODS G ON S.SALEORDER_ID=G.SALEORDER_ID
		LEFT JOIN T_USER U ON S.USER_ID=U.USER_ID
		LEFT JOIN T_QUOTEORDER Q ON S.QUOTEORDER_ID=Q.QUOTEORDER_ID
		WHERE S.ORDER_TYPE IN (1, 5, 7) AND S.STATUS IN (0,1) AND (S.QUOTEORDER_ID IS NULL OR S.QUOTEORDER_ID=0 OR Q.BUSSINESS_CHANCE_ID =0)
		<if test="order.saleorderNo!=null and order.saleorderNo!=''">
			AND S.SALEORDER_NO=#{order.saleorderNo}
		</if>
		<if test="order.traderId!=null">
			AND S.TRADER_ID=#{order.traderId}
		</if>
		<if test="order.traderName!=null and order.traderName!=''">
			AND S.TRADER_NAME like CONCAT('%',#{order.traderName},'%')
		</if>
		<if test="order.traderContactName!=null and order.traderContactName!=''">
			AND S.TRADER_CONTACT_NAME =#{order.traderContactName}
		</if>
		<if test="order.traderContactMobile!=null and order.traderContactMobile!=''">
			AND S.TRADER_CONTACT_MOBILE=#{order.traderContactMobile}
		</if>
		<if test="order.productName!=null and order.productName!=''">
			AND G.GOODS_NAME LIKE CONCAT('%',#{order.productName},'%')
		</if>
		<if test="order.skuNo!=null and order.skuNo!=''">
			AND G.SKU LIKE CONCAT('%',#{order.skuNo},'%')
		</if>
		<if test="order.status!=null and order.status != -1">
			AND S.STATUS =#{order.status}
		</if>
		<if test="order.beginTime!=null">
			AND S.ADD_TIME <![CDATA[ >= ]]> #{order.beginTime}
		</if>
		<if test="order.endTime!=null">
			AND S.ADD_TIME <![CDATA[ <= ]]> #{order.endTime}
		</if>
		<if test="order.saleUserList != null and order.saleUserList.size() > 0">
			and S.USER_ID in
			<foreach collection="order.saleUserList" item="list" separator="," open="(" close=")">
				#{list.userId,jdbcType=INTEGER}
			</foreach>
		</if>
		GROUP BY S.SALEORDER_ID
		ORDER BY
			S.ADD_TIME DESC
	</select>

	<select id="selectTraderHaveOrder" resultType="java.lang.Integer">
		  SELECT
				  a.SALEORDER_ID
		  FROM
				  T_SALEORDER a
		  WHERE
				  a.`STATUS` = 2
			AND a.TRADER_ID = #{traderId,jdbcType=INTEGER}
		limit 1
	</select>

	<update id="updateOnlineReceiptStatusById">
		UPDATE T_SALEORDER
		SET ONLINE_RECEIPT_STATUS = #{onlineReceiptStatus,jdbcType=INTEGER}
		WHERE
			SALEORDER_ID = #{orderId,jdbcType=INTEGER}
	</update>
	<update id="updateIssueWMSByTraderIdList">
		UPDATE T_TRADER_PERIOD_WHITE
		SET IS_ISSUE_WMS = 1
		WHERE
			TRADER_ID IN
		<foreach collection="list" item="traderId" open="(" close=")" separator=",">
			#{traderId, jdbcType=INTEGER}
		</foreach>
	</update>


	<select id="getTraderPeriodWhiteByTraderId" resultType="java.lang.Integer">
		SELECT
			COUNT( DISTINCT w.TRADER_ID )
		FROM
			T_TRADER_PERIOD_WHITE w
		WHERE
			w.IS_DEL = 0
			AND w.TRADER_ID = #{traderId,jdbcType=INTEGER}
	</select>

	<select id="getTraderIdListInWhitelist" resultType="java.lang.Integer">
		SELECT
			w.TRADER_ID
		FROM
			T_TRADER_PERIOD_WHITE w
		WHERE
			w.IS_DEL = 0
			AND w.IS_ISSUE_WMS = 0
	</select>

	<select id="getSaleOrderByTraderIdListInWhitelist" resultType="com.vedeng.order.model.Saleorder">
		SELECT
		<include refid="Base_Column_List"/>
		FROM
		T_SALEORDER
		WHERE
		`STATUS` = 1
		AND PAYMENT_STATUS = 2
		AND VALID_STATUS = 1
		AND TAKE_TRADER_ADDRESS_ID != 0
		AND TAKE_TRADER_ADDRESS IS NOT NULL
		AND LOCKED_STATUS = 0
		AND PAYMENT_TYPE IN ( 420, 421, 422, 423 )
		AND TRADER_ID IN
		<foreach collection="list" item="traderId" open="(" close=")" separator=",">
			#{traderId, jdbcType=INTEGER}
		</foreach>
	</select>

	<select id="findContractVerifyStatusBySaleorderId" resultType="java.lang.Integer">
		SELECT
			CASE
				when e.`status` is null and b.RELATED_ID is not null  then '4' -- 待提交审核
				when e.`status` is null and b.RELATED_ID is   null  then '5' -- 未上传
				when e.`status` = 0 then '0' -- 审核中
				when e.`status` = 1 then '1' -- 审核通过
				when e.`status` = 2 then '2' -- 审核不通过

				end contractVerifyStatus
		FROM
			T_SALEORDER a
				LEFT JOIN T_ATTACHMENT b ON a.SALEORDER_ID = b.RELATED_ID and b.ATTACHMENT_FUNCTION = 492 and b.is_deleted=0
				LEFT JOIN T_VERIFIES_INFO e ON a.SALEORDER_ID = e.RELATE_TABLE_KEY
				AND e.RELATE_TABLE = 'T_SALEORDER' AND e.VERIFIES_TYPE = 868
		WHERE
		   a.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER};
	</select>

	<update id="updateContractVerifyStatus">
		UPDATE T_SALEORDER_DATA
		SET CONTRACT_VERIFY_STATUS = #{status,jdbcType=INTEGER}
		WHERE
			SALEORDER_ID = #{saleorderId,jdbcType=INTEGER};
	</update>

	<select id="getReceiptSaleOrderIdList" resultType="java.lang.Integer">
		SELECT
			SALEORDER_ID
		FROM
			T_SALEORDER
		WHERE
			ONLINE_RECEIPT_STATUS = 1
	</select>

	<update id="batchUpdateOrderReceiptStatus">
		UPDATE T_SALEORDER
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="ONLINE_RECEIPT_STATUS = case" suffix="end,">
				<foreach collection="orderReceiptStatusList" item="orderReceiptStatusInfo" index="index">
					<if test="orderReceiptStatusInfo.orderId!=null">
						when SALEORDER_ID = #{orderReceiptStatusInfo.orderId} then #{orderReceiptStatusInfo.receiptStatus}
					</if>
				</foreach>
			</trim>
		</trim>
		WHERE SALEORDER_ID IN
		<foreach collection="orderReceiptStatusList" item="orderReceiptStatusInfo" separator="," open="(" close=")">
			#{orderReceiptStatusInfo.orderId}
		</foreach>
	</update>

      <select id="confirmationFormAuditBySaleOrderId" resultType="java.lang.Integer">
          SELECT CONFIRMATION_FORM_AUDIT
          FROM T_SALEORDER
          WHERE SALEORDER_ID = #{saleOrderId,jdbcType=INTEGER}
	</select>

      <update id="updateConfirmationFormAuditById">
          UPDATE T_SALEORDER
          SET CONFIRMATION_FORM_AUDIT = #{confirmationFormAudit,jdbcType=INTEGER}
          WHERE SALEORDER_ID = #{saleOrderId,jdbcType=INTEGER}
    </update>
	<update id="updateConfirmationFormUploadById">
		UPDATE T_SALEORDER
		SET CONFIRMATION_FORM_UPLOAD = #{confirmationFormUpload,jdbcType=INTEGER}
		WHERE SALEORDER_ID = #{saleOrderId,jdbcType=INTEGER}
	</update>


	<select id="selectByAddTime" resultType="java.lang.Integer" parameterType="java.util.Map">
		SELECT
		  DISTINCT(TT.TRADER_ID) traderId
		FROM
		  T_SALEORDER TS
		  LEFT JOIN T_TRADER TT ON TS.TRADER_ID = TT.TRADER_ID
		  LEFT JOIN T_TRADER_CUSTOMER TTC ON TT.TRADER_ID = TTC.TRADER_ID
		WHERE
		  1 = 1
		  AND TS.VALID_STATUS = 1
		  <if test="startDate != null and endDate != null" >
		  	AND TS.VALID_TIME  <![CDATA[>= ]]> #{startDate}
		  	AND TS.VALID_TIME  <![CDATA[<= ]]> #{endDate}
		  </if>
		  <if test="traderId != null and traderId > 0 " >
		  	AND TT.TRADER_ID  <![CDATA[> ]]> #{traderId}
		  </if>
		  AND TT.TRADER_ID IS NOT NULL
		  ORDER BY TT.TRADER_ID  ASC
		  LIMIT #{pageSize}
	</select>

	<select id="selectSaleOrderByOrderId" resultType="com.vedeng.order.model.Saleorder">
		SELECT
		*
		FROM T_SALEORDER
		WHERE SALEORDER_ID = #{orderId}
		limit 1
	</select>

	<update id="updateContractReas">
		UPDATE T_SALEORDER SET CONTRACT_REAS =#{comment} WHERE SALEORDER_ID = #{orderId}
	</update>

	<select id="selectConfirmOrderHistoryData" resultType="java.lang.Integer">
		select ts.SALEORDER_ID
		from T_SALEORDER ts
		where ts.ADD_TIME > 1669979401000
		  and ts.VALID_STATUS = 1
		  and ts.DELIVERY_STATUS = 2
		  and ts.CONFIRMATION_FORM_AUDIT in (0, 3)
		  and ts.STATUS in (1, 2, 3);
    </select>

	<select id="selectReviewConfirmOrderHistoryData" resultType="java.lang.Integer">
		select ts.SALEORDER_ID
		from T_SALEORDER ts
		where ts.ADD_TIME > 1669979401000
		  and ts.VALID_STATUS = 1
		  and ts.DELIVERY_STATUS = 2
		  and ts.CONFIRMATION_FORM_AUDIT = 1
		  and ts.STATUS in (1, 2, 3);
	</select>

      <select id="getSaleorderStatusBySaleorderId" resultType="java.lang.Integer">
		  select STATUS
		  from T_SALEORDER
		  where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </select>

    <select id="getSaleOrderAttributionInfo" resultType="com.vedeng.order.model.dto.SaleorderAttributionInfoDto">
		SELECT
			TS.SALEORDER_ID,
			TS.SALEORDER_NO,
			TS.TRADER_ID,
		    TS.PAYMENT_TYPE,
			TU.USER_ID AS OPT_USER_ID,
			TU.USERNAME AS OPT_USER_NAME,
			O.ORG_ID AS SALES_DEPT_ID,
			O.ORG_NAME AS SALES_DEPT_NAME
		FROM
			T_SALEORDER TS
				LEFT JOIN T_R_TRADER_J_USER TRTJU ON TS.TRADER_ID = TRTJU.TRADER_ID and TRTJU.TRADER_TYPE = 1
				LEFT JOIN T_USER TU ON TRTJU.USER_ID = TU.USER_ID
				LEFT JOIN T_R_USER_POSIT TRUP ON TRTJU.USER_ID = TRUP.USER_ID
				LEFT JOIN T_POSITION TP ON TRUP.POSITION_ID = TP.POSITION_ID
				LEFT JOIN T_ORGANIZATION O ON O.ORG_ID = TP.ORG_ID
		WHERE
		TS.SALEORDER_ID = #{saleOrderId,jdbcType=INTEGER}
		and O.TYPE = 310
		limit 1
	</select>


	<select id="getGoodsBelong" resultType="com.vedeng.order.model.dto.GoodsBelong">
		SELECT g1.GOODS_ID skuId,
		u.USER_ID assignmentManagerId,
		u.USERNAME assignmentManagerName,
		u1.USER_ID assignmentAssistantId,
		u1.USERNAME assignmentAssistantName
		from T_GOODS g1
				 LEFT JOIN V_CORE_SKU sku1 ON g1.SKU = sku1.SKU_NO
				 LEFT JOIN V_CORE_SPU spu1 ON sku1.SPU_ID = spu1.SPU_ID
				 LEFT JOIN T_USER u ON u.USER_ID = spu1.ASSIGNMENT_MANAGER_ID
				 LEFT JOIN T_USER u1 ON u1.USER_ID = spu1.ASSIGNMENT_ASSISTANT_ID
		WHERE g1.GOODS_ID IN
		<foreach collection="list" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</select>

      <select id="getModifyApplyList" resultType="com.vedeng.order.model.SaleorderModifyApplyGoods">
		  select * from T_SALEORDER_MODIFY_APPLY_GOODS where SALEORDER_MODIFY_APPLY_ID = #{saleorderModifyApplyId,jdbcType=INTEGER}
	</select>

      <select id="getByBusinessChanceId" resultMap="BaseResultMap">
		  select TS.*
		  from T_SALEORDER TS
				   left join T_QUOTEORDER TQ on TQ.QUOTEORDER_ID = TS.QUOTEORDER_ID
				   left join T_BUSSINESS_CHANCE TBC on TBC.BUSSINESS_CHANCE_ID = TQ.BUSSINESS_CHANCE_ID
		  where TBC.BUSSINESS_CHANCE_ID = #{businessChanceId,jdbcType=INTEGER}
    </select>

      <select id="isExistSaleOrder" resultType="java.lang.Integer">
		  SELECT COUNT(1) FROM T_SALEORDER
		  WHERE QUOTEORDER_ID = #{quoteOrderId,jdbcType=INTEGER}
	</select>

	<select id="queryNewestTraderIdBySaleOrder" resultType="java.lang.Integer">
		SELECT TRADER_ID FROM T_SALEORDER
		WHERE TRADER_ID IN
			<foreach collection="traderIdList" close=")" index="traderIndex" item="traderId" open="(" separator=",">
				${traderId}
			</foreach>
		ORDER BY DELIVERY_TIME desc
		LIMIT 1
	</select>
</mapper>

