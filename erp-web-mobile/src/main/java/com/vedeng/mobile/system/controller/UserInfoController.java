package com.vedeng.mobile.system.controller;

import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.ServletUtils;
import com.vedeng.mobile.constant.ErpMobileCacheConstant;
import com.vedeng.mobile.constant.ErpMobileConst;
import com.vedeng.mobile.feign.RemoteUacUserinfoApiService;
import com.vedeng.mobile.system.dto.UacUserInfoDto;
import com.vedeng.mobile.system.service.UserInfoService;
import com.vedeng.mobile.utils.JwtUtil;
import com.vedeng.mobile.utils.MobileRedisUtil;
import com.vedeng.uac.api.dto.UserDTO;
import com.vedeng.uac.api.dto.UserInfoDto;
import com.vedeng.uac.api.dto.UserLoginInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.concurrent.TimeUnit;

@Slf4j
@RequestMapping("/mobile")
@RestController
public class UserInfoController {

    @Autowired
    private RemoteUacUserinfoApiService remoteUacUserinfoApiService;

    @Autowired
    private UserInfoService userInfoService;

    @Value("${mobile_redis_timeout}")
    private long mobileRedisTimeout;

    /**
     * 密码最大重试次数
     */
    private static final int MAX_RETRIES = 20;

    /**
     * <AUTHOR>
     * @desc 掌上小贝调用查询用户信息接口
     */
    @PostMapping("/userInfo")
    public R<UacUserInfoDto> userInfo(@RequestBody UacUserInfoDto user) {
        UserDTO userDTO = new UserDTO();
        userDTO.setUserId(user.getUserId());
        try {
            //feign调用uac获取用户信息
            UserInfoDto userInfoDto = remoteUacUserinfoApiService.getUacUserInfo(userDTO);
            UacUserInfoDto uacUserInfoDto = new UacUserInfoDto();
            uacUserInfoDto.setUserId(user.getUserId());
            uacUserInfoDto.setRealName(userInfoDto.getRealName());
            uacUserInfoDto.setDepartmentIds(userInfoDto.getDepartmentIds());
            uacUserInfoDto.setHeadPicture(userInfoDto.getHeadPicture());
            uacUserInfoDto.setUserName(userInfoDto.getDisplayName());
            uacUserInfoDto.setEmail(userInfoDto.getEmail());
            uacUserInfoDto.setNumber(userInfoDto.getJobNumber());
            uacUserInfoDto.setCompanyName(userInfoDto.getCompanyName());
            uacUserInfoDto.setOrgName(userInfoDto.getOrgName());
            uacUserInfoDto.setPositionName(userInfoDto.getPositionName());
            uacUserInfoDto.setMainDepartmentName(userInfoDto.getMainDepartmentName());
            //从uac抓取的都为贝登员工
            uacUserInfoDto.setStaff(ErpMobileConst.ID_ZERO);
            //查询erp中的业务手机号
            String telephone = userInfoService.getUserErpMobile(user.getUserId());
            if(telephone == null || "".equals(telephone)){
                telephone = "4006999569";
            }
            uacUserInfoDto.setTelephone(telephone);
            return R.success(uacUserInfoDto);
        } catch (Exception e) {
            log.error("掌上小贝:查询用户信息异常",e);
            return R.error(-1, "查询用户信息失败");
        }
    }

    /**
     * @param loginUser
     * @return
     * <AUTHOR>
     * @desc 掌上小贝登录
     */
    @PostMapping("/login")
    public R<UserLoginInfo> login(@RequestBody UserLoginInfo loginUser) {
        //掌上小贝登录认证
        UserLoginInfo  userLoginInfo = userInfoService.userLoginInfo(loginUser.getUserName());
        if (StringUtils.isEmpty(userLoginInfo)) {
            return R.error(-1, "账号不存在或密码错误，请重新输入");
        }
        //获取用户对应输错密码的次数 , username为英文名，人事限制不会重复
        String numObj = MobileRedisUtil.StringOps.get(ErpMobileCacheConstant.LOGIN_FAIL_COUNT + loginUser.getUserName());
        //密码输入错误记录数
        int num = (numObj == null || "".equals(numObj)) ? 0 : Integer.parseInt(numObj);
        if (num >= MAX_RETRIES) {
            return R.error(-1, "该账号已锁定，请联系贝登");
        }
        if (!userLoginInfo.getPassWord().equals(DigestUtils.md5Hex(loginUser.getPassWord() + userLoginInfo.getSalt()))) {
            if (MobileRedisUtil.KeyOps.hasKey(ErpMobileCacheConstant.LOGIN_FAIL_COUNT + loginUser.getUserName())) {
                MobileRedisUtil.StringOps.incrBy(ErpMobileCacheConstant.LOGIN_FAIL_COUNT + loginUser.getUserName(), 1L);
            } else {
                MobileRedisUtil.StringOps.setEx(ErpMobileCacheConstant.LOGIN_FAIL_COUNT + loginUser.getUserName(), "1", mobileRedisTimeout, TimeUnit.MILLISECONDS);
            }
            log.info(loginUser.getUserName() + "密码错误次数：{}", num);
            return R.error(-1, "账号不存在或密码错误，请重新输入");
        }
        //登录成功，清除认证失败的redis信息
        MobileRedisUtil.KeyOps.delete(ErpMobileCacheConstant.LOGIN_FAIL_COUNT + loginUser.getUserName());
        String token = JwtUtil.sign(userLoginInfo.getUserName(), userLoginInfo.getPassWord());
        userLoginInfo.setToken(token);
        //token存入redis
        MobileRedisUtil.StringOps.setEx(ErpMobileCacheConstant.TOKEN + loginUser.getUserName(), token, mobileRedisTimeout, TimeUnit.MILLISECONDS);
        //清空密码
        userLoginInfo.setPassWord("");
        userLoginInfo.setMainDepartmentName(userLoginInfo.getMainDepartmentName());
        // 使用新的当前登录用户对象,初始化当前登录用户信息
        CurrentUser currentUser = CurrentUser.builder().id(userLoginInfo.getUserId())
                .username(userLoginInfo.getUserName())
                .build();
        HttpSession session = ServletUtils.getRequest().getSession();
        session.setAttribute(ErpConstant.CURRENT_USER, currentUser);
        return R.success("登录成功", userLoginInfo);
    }

    /**
     * @return
     * <AUTHOR>
     * @desc 掌上小贝登出
     */
    @PostMapping("/logout")
    public R<String> logout(HttpServletRequest httpServletRequest) {
        try {
            String token = httpServletRequest.getHeader("token");
            if (null == token) {
                return R.error(BaseResponseCode.TOKEN_INVALID);
            }
            String userName = JwtUtil.getUsername(httpServletRequest.getHeader("token"));
            //登出移除token
            MobileRedisUtil.KeyOps.delete(ErpMobileCacheConstant.TOKEN + userName);
        } catch (Exception e) {
            log.error("掌上小贝:登出异常",e);
            return R.error(BaseResponseCode.SYSTEM_BUSY);
        }
        return R.success();
    }
}
