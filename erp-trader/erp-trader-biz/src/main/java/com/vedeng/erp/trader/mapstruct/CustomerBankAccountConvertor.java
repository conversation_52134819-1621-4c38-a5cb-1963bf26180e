package com.vedeng.erp.trader.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.trader.domain.entity.CustomerBankAccountEntity;
import com.vedeng.erp.trader.dto.CustomerBankAccountReqDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CustomerBankAccountConvertor extends BaseMapStruct<CustomerBankAccountEntity, CustomerBankAccountReqDto> {
}
