package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.*;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchRollbackInvoiceDto extends BatchBaseDto {

    private Integer invoiceId;

    /**
     * 发票类型 字典库
     */
    private Integer invoiceType;

    /**
     * 开票申请类型 字典库
     */
    private Integer type;

    /**
     * 最大时间
     */
    private Long beginTime;

    /**
     * 最小时间
     */
    private Long endTime;


    private Integer virtualInvoiceId;

    private String uuid;

}
