<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.kpi.replica.dao.CapitalBillReplicaMapper" >

    <resultMap id="BaseResultMap" type="com.vedeng.finance.model.CapitalBill">
        <id column="CAPITAL_BILL_ID" property="capitalBillId" jdbcType="INTEGER" />
        <result column="BANK_BILL_ID" property="bankBillId" jdbcType="INTEGER" />
        <result column="CAPITAL_BILL_NO" property="capitalBillNo" jdbcType="VARCHAR" />
        <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
        <result column="TRADER_TIME" property="traderTime" jdbcType="BIGINT" />
        <result column="TRADER_SUBJECT" property="traderSubject" jdbcType="BIT" />
        <result column="TRADER_TYPE" property="traderType" jdbcType="BIT" />
        <result column="TRADER_MODE" property="traderMode" jdbcType="INTEGER" />
        <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
        <result column="CURRENCY_UNIT_ID" property="currencyUnitId" jdbcType="INTEGER" />
        <result column="PAYER" property="payer" jdbcType="VARCHAR" />
        <result column="PAYEE" property="payee" jdbcType="VARCHAR" />
        <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
        <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
        <result column="CREATOR" property="creator" jdbcType="INTEGER" />
        <result column="TRAN_FLOW" property="tranFlow" jdbcType="VARCHAR" />
        <result column="PAYER_BANK_ACCOUNT" property="payerBankAccount" jdbcType="VARCHAR" />
        <result column="PAYEE_BANK_ACCOUNT" property="payeeBankAccount" jdbcType="VARCHAR" />
        <result column="PAYER_BANK_NAME" property="payerBankName" jdbcType="VARCHAR" />
        <result column="PAYEE_BANK_NAME" property="payeeBankName" jdbcType="VARCHAR" />
        <result column="ARRIVAL_TIME" property="arrivalTime" jdbcType="BIGINT" />
        <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="BIGINT" />
        <result column="RELATED_ORDER_ID" property="relatedOrderId" jdbcType="INTEGER"/>
        <result column="RELATED_ORDER_NO" property="relatedOrderNo" jdbcType="VARCHAR" />

        <collection property="capitalBillDetails" ofType="com.vedeng.finance.model.CapitalBillDetail">
            <id column="CAPITAL_BILL_DETAIL_ID" property="capitalBillDetailId" jdbcType="INTEGER" />
            <result column="CAPITAL_BILL_ID" property="capitalBillId" jdbcType="INTEGER" />
            <result column="BUSSINESS_TYPE" property="bussinessType" jdbcType="INTEGER" />
            <result column="ORDER_TYPE" property="orderType" jdbcType="BIT" />
            <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
            <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
            <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
            <result column="PAYER" property="payer" jdbcType="VARCHAR" />
            <result column="PAYEE" property="payee" jdbcType="VARCHAR" />
            <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
            <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
            <result column="TRADER_TYPE" property="traderType" jdbcType="BIT" />
            <result column="USER_ID" property="userId" jdbcType="INTEGER" />
            <result column="ORG_ID" property="orgId" jdbcType="INTEGER" />
            <result column="ORG_NAME" property="orgName" jdbcType="VARCHAR" />
        </collection>
    </resultMap>

    <select id="getLatestCapitalBillByOrder" resultMap="BaseResultMap">
        SELECT B.* FROM T_CAPITAL_BILL B LEFT JOIN T_CAPITAL_BILL_DETAIL D on B.CAPITAL_BILL_ID = D.CAPITAL_BILL_ID WHERE D.ORDER_NO = #{orderNo} ORDER BY B.CAPITAL_BILL_ID DESC LIMIT 1
    </select>
    <select id="getCapitalBillById" resultType="com.vedeng.finance.model.CapitalBill">
        SELECT * FROM T_CAPITAL_BILL B LEFT JOIN T_CAPITAL_BILL_DETAIL D ON B.CAPITAL_BILL_ID = D.CAPITAL_BILL_ID WHERE B.CAPITAL_BILL_ID = #{capitalBillId}
    </select>
    <select id="getRealReceivedAmountOfSaleorderBeforeExecuteTime" resultType="java.math.BigDecimal">
        SELECT sum(C.AMOUNT) FROM
        (
            SELECT
                SUM( a.AMOUNT ) AS AMOUNT
            FROM
                T_CAPITAL_BILL a
            LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
            WHERE
                b.ORDER_TYPE = 1
            AND ( a.TRADER_TYPE IN ( 1, 4 ) OR ( a.TRADER_TYPE = 2 AND ( a.TRADER_MODE = 520 OR a.TRADER_MODE = 522 ) ) )
            AND b.ORDER_NO = #{saleorderNo}
            UNION ALL
            SELECT
                IFNULL(SUM(C.AMOUNT), 0 ) AS AMOUNT
            FROM
                T_AFTER_SALES A
                    LEFT JOIN T_CAPITAL_BILL_DETAIL B ON A.AFTER_SALES_ID = B.RELATED_ID
                    LEFT JOIN T_CAPITAL_BILL C ON B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
            WHERE
                A.ORDER_NO = #{saleorderNo}
              AND A.SUBJECT_TYPE = 535
              AND A.ATFER_SALES_STATUS IN ( 1, 2 )
              AND B.BUSSINESS_TYPE = 531
              AND B.ORDER_TYPE = 3
              AND C.TRADER_MODE = 530 AND C.ADD_TIME &lt;= #{executeTime}
        ) as C
    </select>
    <select id="getCapitalBillCountOfSaleorderByTraderSubjectBeforeExecuteTime" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM T_CAPITAL_BILL B LEFT JOIN T_CAPITAL_BILL_DETAIL D ON B.CAPITAL_BILL_ID = D.CAPITAL_BILL_ID
        WHERE D.ORDER_NO = #{saleorderNo} AND D.ORDER_TYPE = 1 AND B.ADD_TIME &lt;= #{executeTime} AND B.TRADER_SUBJECT = 1
    </select>
    <select id="getCapitalBillListOfSaleorder" resultMap="BaseResultMap">
        SELECT
            a.*
        FROM
            T_CAPITAL_BILL a
                LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
        WHERE
            b.ORDER_TYPE = 1
          AND ( a.TRADER_TYPE IN ( 1, 4 ) OR ( a.TRADER_TYPE = 2 AND ( a.TRADER_MODE = 520 OR a.TRADER_MODE = 522 ) ) )
          AND b.ORDER_NO = #{saleorderNo}
    </select>
    <select id="getCapitalBillOfAfterSalesBySaleorderNo" resultMap="BaseResultMap">
        SELECT B.* FROM T_CAPITAL_BILL B LEFT JOIN T_CAPITAL_BILL_DETAIL BD ON B.CAPITAL_BILL_ID = BD.CAPITAL_BILL_ID
        LEFT JOIN T_AFTER_SALES A ON BD.ORDER_NO = A.AFTER_SALES_NO
        WHERE BD.ORDER_TYPE = 3 AND A.ORDER_NO = #{saleorderNo} AND B.ADD_TIME &gt;= #{executeTime}
    </select>
    <select id="getFirstCapitalBillByOrder" resultMap="BaseResultMap">
        SELECT B.* FROM T_CAPITAL_BILL B LEFT JOIN T_CAPITAL_BILL_DETAIL D on B.CAPITAL_BILL_ID = D.CAPITAL_BILL_ID WHERE D.ORDER_NO = #{orderNo} ORDER BY B.CAPITAL_BILL_ID LIMIT 1
    </select>
    <select id="getAllRealCapitalBillOfSaleorder" resultMap="BaseResultMap">
        SELECT * FROM
            (
                SELECT
                    a.AMOUNT, a.ADD_TIME
                FROM
                    T_CAPITAL_BILL a
                        LEFT JOIN T_CAPITAL_BILL_DETAIL b ON a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
                WHERE
                    b.ORDER_TYPE = 1
                  AND ( a.TRADER_TYPE IN ( 1, 4 ) OR ( a.TRADER_TYPE = 2 AND ( a.TRADER_MODE = 520 OR a.TRADER_MODE = 522 ) ) )
                  AND b.ORDER_NO = #{saleorderNo}
                UNION ALL
                SELECT
                    C.AMOUNT, C.ADD_TIME
                FROM
                    T_AFTER_SALES A
                        LEFT JOIN T_CAPITAL_BILL_DETAIL B ON A.AFTER_SALES_ID = B.RELATED_ID
                        LEFT JOIN T_CAPITAL_BILL C ON B.CAPITAL_BILL_ID = C.CAPITAL_BILL_ID
                WHERE
                    A.ORDER_NO = #{saleorderNo}
                  AND A.SUBJECT_TYPE = 535
                  AND A.ATFER_SALES_STATUS IN ( 1, 2 )
                  AND B.BUSSINESS_TYPE = 531
                  AND B.ORDER_TYPE = 3
                  AND C.TRADER_MODE = 530 AND C.ADD_TIME
            ) as C
    </select>
</mapper>