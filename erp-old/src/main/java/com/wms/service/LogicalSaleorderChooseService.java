package com.wms.service;

import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import com.vedeng.flash.enums.WmsSendOrderTypeEnum;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderModifyApply;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.wms.dto.WmsResponse;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.model.po.WmsSendOrder;
import lombok.NonNull;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName LogicalSaleorderService.java
 * @Description TODO 销售单选择逻辑仓
 * @createTime 2020年07月23日 16:31:00
 */
public interface LogicalSaleorderChooseService {
    /**
     * @description: 销售单指定逻辑仓
     * @author: Strange
     * @date: 2020/7/28
     **/
    public void chooseLogicalSaleorder(Saleorder saleorder, User user) throws Exception;

//    /**
//     * @description: 销售单编辑商品直发状态重新指定逻辑仓
//     * @return:
//     * @author: Strange
//     * @date: 2020/7/28
//     **/
//    public void  editchooseLogicalOrderGoods(Saleorder saleorder,User user) throws Exception;

    public void chooseLogicalOrder(Map<String, WarehouseStock> logicalStockInfo, List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList, SaleorderGoodsVo saleorderGoodsVo);


    /**
     * @description: 关闭订单HC订单同步库存服务数据
     * @return:
     * @author: Strange
     * @date: 2020/8/13
     **/
    void closeOrdersynchronizeStockData(Saleorder s1);

    /**
     * @description: 销售退货单关闭重新下发销售单
     * @return:
     * @author: Strange
     * @date: 2020/8/17
     **/
    void closeAfterPutSaleorder(AfterSalesVo afterSalesInfo, User user) throws Exception;

//    public void cancelAfterOrder(AfterSales afterSales);

    /**
     * @description: 取消销售单
     * @return:
     * @author: Strange
     * @date: 2021/12/10
     **/
//    public WmsResponse cancelOutByNo(@NonNull String no,@NonNull String CancelReason);

    /**
     * @description: 生成售后退货单下发未收货部分
     * @return:
     * @author: Strange
     * @date: 2021/2/23
     **/
    void putSaleAfterReturnStartGoods(String orderNo, User user);

    List<WmsLogicalOrdergoods> getWmsLogicalOrdergoodsByOrderNo(Saleorder saleorder);

    /**
     * 销售出库修改接口
     * @param orderId 订单ID
     * @return
     */
    boolean modifyOrderOutput(Integer orderId,SaleorderModifyApply saleorderModifyApply, User user);

    WmsSendOrder getWmsSendOrder(String orderNo,Integer orderId,Integer orderType,User user);

    /**
     * 下发wms成功，修改V_WMS_SEND_ORDER状态
     * @param wmsSendOrder
     */
    void saveWmsSendOrder(WmsSendOrder wmsSendOrder);

    /**
     * @description: 保存触发wms事件
     * @return:
     * @author: Strange
     * @date: 2021/12/9
     **/
    void saveWmsSendOrderEvent(Saleorder saleorder, User user, WmsSendOrderTypeEnum code);
}
