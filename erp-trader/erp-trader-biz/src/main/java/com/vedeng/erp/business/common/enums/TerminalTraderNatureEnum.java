package com.vedeng.erp.business.common.enums;

import lombok.Getter;

@Getter
public enum TerminalTraderNatureEnum {

    PUBLIC_CLASS(5601,"公立等级"),

    PUBLIC_GRASSROOTS(5602,"公立基层"),

    NON_PUBLIC_RANK(5603,"非公等级"),

    NON_PUBLIC_BASE(5604,"非公基层"),

    NON_PUBLIC_GROUP(5605,"非公集团"),

    EMERGENCY(5606,"应急"),

    OUT_OF_HOSPITAL(5607,"院外"),
    
    ;
    
    
    
    private final Integer code;


    private final String title;

    TerminalTraderNatureEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }


    public static String getTitleByCode(Integer code) {
        for (TerminalTraderNatureEnum businessChanceLevelEnum : TerminalTraderNatureEnum.values()) {
            if (businessChanceLevelEnum.getCode().equals(code)) {
                return businessChanceLevelEnum.getTitle();
            }
        }
        return "";
    }

}
