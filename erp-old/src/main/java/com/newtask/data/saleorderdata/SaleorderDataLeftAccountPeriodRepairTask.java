package com.newtask.data.saleorderdata;

import com.newtask.data.dao.SaleorderDataMapper;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: daniel
 * @Date: 2022/4/21 09 19
 * @Description:
 */
@Component
@JobHandler(value = "saleorderDataLeftAccountPeriodRepair")
public class SaleorderDataLeftAccountPeriodRepairTask extends AbstractJobHandler {

    @Autowired
    private SaleorderDataMapper saleorderDataMapper;

    @Autowired
    private SaleorderLeftAmountPeriodSync saleorderLeftAmountPeriodSync;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        List<Integer> saleorderIdList;
        if (StringUtils.isNotEmpty(param)){
            saleorderIdList = Arrays.stream(param.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            saleorderIdList.parallelStream().forEach(item -> saleorderLeftAmountPeriodSync.process(item));
            return ReturnT.SUCCESS;
        }
        int start = 0;
        while (true) {
            saleorderIdList = saleorderDataMapper.getFailLeftAccountPeriodSaleorderList(start);
            if (CollectionUtils.isEmpty(saleorderIdList)){
                break;
            }
            saleorderIdList.parallelStream().forEach(item -> saleorderLeftAmountPeriodSync.process(item));
            start = saleorderIdList.get(saleorderIdList.size() - 1);
        }
        return ReturnT.SUCCESS;
    }
}
