package com.vedeng.oa.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FileDeliveryAddressExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public FileDeliveryAddressExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("ID is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("ID is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("ID =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("ID <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("ID >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ID >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("ID <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("ID <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("ID in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("ID not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("ID between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ID not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andApplyIdIsNull() {
            addCriterion("APPLY_ID is null");
            return (Criteria) this;
        }

        public Criteria andApplyIdIsNotNull() {
            addCriterion("APPLY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andApplyIdEqualTo(Integer value) {
            addCriterion("APPLY_ID =", value, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdNotEqualTo(Integer value) {
            addCriterion("APPLY_ID <>", value, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdGreaterThan(Integer value) {
            addCriterion("APPLY_ID >", value, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("APPLY_ID >=", value, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdLessThan(Integer value) {
            addCriterion("APPLY_ID <", value, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdLessThanOrEqualTo(Integer value) {
            addCriterion("APPLY_ID <=", value, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdIn(List<Integer> values) {
            addCriterion("APPLY_ID in", values, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdNotIn(List<Integer> values) {
            addCriterion("APPLY_ID not in", values, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdBetween(Integer value1, Integer value2) {
            addCriterion("APPLY_ID between", value1, value2, "applyId");
            return (Criteria) this;
        }

        public Criteria andApplyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("APPLY_ID not between", value1, value2, "applyId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdIsNull() {
            addCriterion("FILE_DELIVERY_ID is null");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdIsNotNull() {
            addCriterion("FILE_DELIVERY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdEqualTo(Integer value) {
            addCriterion("FILE_DELIVERY_ID =", value, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdNotEqualTo(Integer value) {
            addCriterion("FILE_DELIVERY_ID <>", value, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdGreaterThan(Integer value) {
            addCriterion("FILE_DELIVERY_ID >", value, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("FILE_DELIVERY_ID >=", value, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdLessThan(Integer value) {
            addCriterion("FILE_DELIVERY_ID <", value, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdLessThanOrEqualTo(Integer value) {
            addCriterion("FILE_DELIVERY_ID <=", value, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdIn(List<Integer> values) {
            addCriterion("FILE_DELIVERY_ID in", values, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdNotIn(List<Integer> values) {
            addCriterion("FILE_DELIVERY_ID not in", values, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdBetween(Integer value1, Integer value2) {
            addCriterion("FILE_DELIVERY_ID between", value1, value2, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("FILE_DELIVERY_ID not between", value1, value2, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andTraderTypeIsNull() {
            addCriterion("TRADER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andTraderTypeIsNotNull() {
            addCriterion("TRADER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andTraderTypeEqualTo(Integer value) {
            addCriterion("TRADER_TYPE =", value, "traderType");
            return (Criteria) this;
        }

        public Criteria andTraderTypeNotEqualTo(Integer value) {
            addCriterion("TRADER_TYPE <>", value, "traderType");
            return (Criteria) this;
        }

        public Criteria andTraderTypeGreaterThan(Integer value) {
            addCriterion("TRADER_TYPE >", value, "traderType");
            return (Criteria) this;
        }

        public Criteria andTraderTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_TYPE >=", value, "traderType");
            return (Criteria) this;
        }

        public Criteria andTraderTypeLessThan(Integer value) {
            addCriterion("TRADER_TYPE <", value, "traderType");
            return (Criteria) this;
        }

        public Criteria andTraderTypeLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_TYPE <=", value, "traderType");
            return (Criteria) this;
        }

        public Criteria andTraderTypeIn(List<Integer> values) {
            addCriterion("TRADER_TYPE in", values, "traderType");
            return (Criteria) this;
        }

        public Criteria andTraderTypeNotIn(List<Integer> values) {
            addCriterion("TRADER_TYPE not in", values, "traderType");
            return (Criteria) this;
        }

        public Criteria andTraderTypeBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_TYPE between", value1, value2, "traderType");
            return (Criteria) this;
        }

        public Criteria andTraderTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_TYPE not between", value1, value2, "traderType");
            return (Criteria) this;
        }

        public Criteria andTraderIdIsNull() {
            addCriterion("TRADER_ID is null");
            return (Criteria) this;
        }

        public Criteria andTraderIdIsNotNull() {
            addCriterion("TRADER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTraderIdEqualTo(Integer value) {
            addCriterion("TRADER_ID =", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdNotEqualTo(Integer value) {
            addCriterion("TRADER_ID <>", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdGreaterThan(Integer value) {
            addCriterion("TRADER_ID >", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_ID >=", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdLessThan(Integer value) {
            addCriterion("TRADER_ID <", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_ID <=", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdIn(List<Integer> values) {
            addCriterion("TRADER_ID in", values, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdNotIn(List<Integer> values) {
            addCriterion("TRADER_ID not in", values, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_ID between", value1, value2, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_ID not between", value1, value2, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderNameIsNull() {
            addCriterion("TRADER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andTraderNameIsNotNull() {
            addCriterion("TRADER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andTraderNameEqualTo(String value) {
            addCriterion("TRADER_NAME =", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotEqualTo(String value) {
            addCriterion("TRADER_NAME <>", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameGreaterThan(String value) {
            addCriterion("TRADER_NAME >", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_NAME >=", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameLessThan(String value) {
            addCriterion("TRADER_NAME <", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameLessThanOrEqualTo(String value) {
            addCriterion("TRADER_NAME <=", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameLike(String value) {
            addCriterion("TRADER_NAME like", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotLike(String value) {
            addCriterion("TRADER_NAME not like", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameIn(List<String> values) {
            addCriterion("TRADER_NAME in", values, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotIn(List<String> values) {
            addCriterion("TRADER_NAME not in", values, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameBetween(String value1, String value2) {
            addCriterion("TRADER_NAME between", value1, value2, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotBetween(String value1, String value2) {
            addCriterion("TRADER_NAME not between", value1, value2, "traderName");
            return (Criteria) this;
        }

        public Criteria andContactTypeIsNull() {
            addCriterion("CONTACT_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andContactTypeIsNotNull() {
            addCriterion("CONTACT_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andContactTypeEqualTo(Integer value) {
            addCriterion("CONTACT_TYPE =", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeNotEqualTo(Integer value) {
            addCriterion("CONTACT_TYPE <>", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeGreaterThan(Integer value) {
            addCriterion("CONTACT_TYPE >", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("CONTACT_TYPE >=", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeLessThan(Integer value) {
            addCriterion("CONTACT_TYPE <", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeLessThanOrEqualTo(Integer value) {
            addCriterion("CONTACT_TYPE <=", value, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeIn(List<Integer> values) {
            addCriterion("CONTACT_TYPE in", values, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeNotIn(List<Integer> values) {
            addCriterion("CONTACT_TYPE not in", values, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeBetween(Integer value1, Integer value2) {
            addCriterion("CONTACT_TYPE between", value1, value2, "contactType");
            return (Criteria) this;
        }

        public Criteria andContactTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("CONTACT_TYPE not between", value1, value2, "contactType");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdIsNull() {
            addCriterion("TRADER_CONTACT_ID is null");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdIsNotNull() {
            addCriterion("TRADER_CONTACT_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ID =", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdNotEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ID <>", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdGreaterThan(Integer value) {
            addCriterion("TRADER_CONTACT_ID >", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ID >=", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdLessThan(Integer value) {
            addCriterion("TRADER_CONTACT_ID <", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ID <=", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdIn(List<Integer> values) {
            addCriterion("TRADER_CONTACT_ID in", values, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdNotIn(List<Integer> values) {
            addCriterion("TRADER_CONTACT_ID not in", values, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_CONTACT_ID between", value1, value2, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_CONTACT_ID not between", value1, value2, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameIsNull() {
            addCriterion("TRADER_CONTACT_NAME is null");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameIsNotNull() {
            addCriterion("TRADER_CONTACT_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameEqualTo(String value) {
            addCriterion("TRADER_CONTACT_NAME =", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameNotEqualTo(String value) {
            addCriterion("TRADER_CONTACT_NAME <>", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameGreaterThan(String value) {
            addCriterion("TRADER_CONTACT_NAME >", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_NAME >=", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameLessThan(String value) {
            addCriterion("TRADER_CONTACT_NAME <", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameLessThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_NAME <=", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameLike(String value) {
            addCriterion("TRADER_CONTACT_NAME like", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameNotLike(String value) {
            addCriterion("TRADER_CONTACT_NAME not like", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameIn(List<String> values) {
            addCriterion("TRADER_CONTACT_NAME in", values, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameNotIn(List<String> values) {
            addCriterion("TRADER_CONTACT_NAME not in", values, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_NAME between", value1, value2, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameNotBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_NAME not between", value1, value2, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileIsNull() {
            addCriterion("TRADER_CONTACT_MOBILE is null");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileIsNotNull() {
            addCriterion("TRADER_CONTACT_MOBILE is not null");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileEqualTo(String value) {
            addCriterion("TRADER_CONTACT_MOBILE =", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileNotEqualTo(String value) {
            addCriterion("TRADER_CONTACT_MOBILE <>", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileGreaterThan(String value) {
            addCriterion("TRADER_CONTACT_MOBILE >", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_MOBILE >=", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileLessThan(String value) {
            addCriterion("TRADER_CONTACT_MOBILE <", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileLessThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_MOBILE <=", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileLike(String value) {
            addCriterion("TRADER_CONTACT_MOBILE like", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileNotLike(String value) {
            addCriterion("TRADER_CONTACT_MOBILE not like", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileIn(List<String> values) {
            addCriterion("TRADER_CONTACT_MOBILE in", values, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileNotIn(List<String> values) {
            addCriterion("TRADER_CONTACT_MOBILE not in", values, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_MOBILE between", value1, value2, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileNotBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_MOBILE not between", value1, value2, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressIdIsNull() {
            addCriterion("TRADER_CONTACT_ADDRESS_ID is null");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressIdIsNotNull() {
            addCriterion("TRADER_CONTACT_ADDRESS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressIdEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ADDRESS_ID =", value, "traderContactAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressIdNotEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ADDRESS_ID <>", value, "traderContactAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressIdGreaterThan(Integer value) {
            addCriterion("TRADER_CONTACT_ADDRESS_ID >", value, "traderContactAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ADDRESS_ID >=", value, "traderContactAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressIdLessThan(Integer value) {
            addCriterion("TRADER_CONTACT_ADDRESS_ID <", value, "traderContactAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressIdLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ADDRESS_ID <=", value, "traderContactAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressIdIn(List<Integer> values) {
            addCriterion("TRADER_CONTACT_ADDRESS_ID in", values, "traderContactAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressIdNotIn(List<Integer> values) {
            addCriterion("TRADER_CONTACT_ADDRESS_ID not in", values, "traderContactAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressIdBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_CONTACT_ADDRESS_ID between", value1, value2, "traderContactAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_CONTACT_ADDRESS_ID not between", value1, value2, "traderContactAddressId");
            return (Criteria) this;
        }

        public Criteria andAreaIdIsNull() {
            addCriterion("AREA_ID is null");
            return (Criteria) this;
        }

        public Criteria andAreaIdIsNotNull() {
            addCriterion("AREA_ID is not null");
            return (Criteria) this;
        }

        public Criteria andAreaIdEqualTo(Integer value) {
            addCriterion("AREA_ID =", value, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdNotEqualTo(Integer value) {
            addCriterion("AREA_ID <>", value, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdGreaterThan(Integer value) {
            addCriterion("AREA_ID >", value, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("AREA_ID >=", value, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdLessThan(Integer value) {
            addCriterion("AREA_ID <", value, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdLessThanOrEqualTo(Integer value) {
            addCriterion("AREA_ID <=", value, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdIn(List<Integer> values) {
            addCriterion("AREA_ID in", values, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdNotIn(List<Integer> values) {
            addCriterion("AREA_ID not in", values, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdBetween(Integer value1, Integer value2) {
            addCriterion("AREA_ID between", value1, value2, "areaId");
            return (Criteria) this;
        }

        public Criteria andAreaIdNotBetween(Integer value1, Integer value2) {
            addCriterion("AREA_ID not between", value1, value2, "areaId");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressIsNull() {
            addCriterion("TRADER_CONTACT_ADDRESS is null");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressIsNotNull() {
            addCriterion("TRADER_CONTACT_ADDRESS is not null");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressEqualTo(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS =", value, "traderContactAddress");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressNotEqualTo(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS <>", value, "traderContactAddress");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressGreaterThan(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS >", value, "traderContactAddress");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS >=", value, "traderContactAddress");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressLessThan(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS <", value, "traderContactAddress");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressLessThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS <=", value, "traderContactAddress");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressLike(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS like", value, "traderContactAddress");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressNotLike(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS not like", value, "traderContactAddress");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressIn(List<String> values) {
            addCriterion("TRADER_CONTACT_ADDRESS in", values, "traderContactAddress");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressNotIn(List<String> values) {
            addCriterion("TRADER_CONTACT_ADDRESS not in", values, "traderContactAddress");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_ADDRESS between", value1, value2, "traderContactAddress");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressNotBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_ADDRESS not between", value1, value2, "traderContactAddress");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("IS_DELETE is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("IS_DELETE is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Integer value) {
            addCriterion("IS_DELETE =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Integer value) {
            addCriterion("IS_DELETE <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Integer value) {
            addCriterion("IS_DELETE >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Integer value) {
            addCriterion("IS_DELETE >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Integer value) {
            addCriterion("IS_DELETE <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Integer value) {
            addCriterion("IS_DELETE <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Integer> values) {
            addCriterion("IS_DELETE in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Integer> values) {
            addCriterion("IS_DELETE not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Integer value1, Integer value2) {
            addCriterion("IS_DELETE between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Integer value1, Integer value2) {
            addCriterion("IS_DELETE not between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Date value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Date value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Date value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Date value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Date> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Date> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkIsNull() {
            addCriterion("UPDATE_REMARK is null");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkIsNotNull() {
            addCriterion("UPDATE_REMARK is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkEqualTo(String value) {
            addCriterion("UPDATE_REMARK =", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkNotEqualTo(String value) {
            addCriterion("UPDATE_REMARK <>", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkGreaterThan(String value) {
            addCriterion("UPDATE_REMARK >", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("UPDATE_REMARK >=", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkLessThan(String value) {
            addCriterion("UPDATE_REMARK <", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkLessThanOrEqualTo(String value) {
            addCriterion("UPDATE_REMARK <=", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkLike(String value) {
            addCriterion("UPDATE_REMARK like", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkNotLike(String value) {
            addCriterion("UPDATE_REMARK not like", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkIn(List<String> values) {
            addCriterion("UPDATE_REMARK in", values, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkNotIn(List<String> values) {
            addCriterion("UPDATE_REMARK not in", values, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkBetween(String value1, String value2) {
            addCriterion("UPDATE_REMARK between", value1, value2, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkNotBetween(String value1, String value2) {
            addCriterion("UPDATE_REMARK not between", value1, value2, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andCityIdIsNull() {
            addCriterion("CITY_ID is null");
            return (Criteria) this;
        }

        public Criteria andCityIdIsNotNull() {
            addCriterion("CITY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andCityIdEqualTo(Integer value) {
            addCriterion("CITY_ID =", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotEqualTo(Integer value) {
            addCriterion("CITY_ID <>", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdGreaterThan(Integer value) {
            addCriterion("CITY_ID >", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("CITY_ID >=", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdLessThan(Integer value) {
            addCriterion("CITY_ID <", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdLessThanOrEqualTo(Integer value) {
            addCriterion("CITY_ID <=", value, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdIn(List<Integer> values) {
            addCriterion("CITY_ID in", values, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotIn(List<Integer> values) {
            addCriterion("CITY_ID not in", values, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdBetween(Integer value1, Integer value2) {
            addCriterion("CITY_ID between", value1, value2, "cityId");
            return (Criteria) this;
        }

        public Criteria andCityIdNotBetween(Integer value1, Integer value2) {
            addCriterion("CITY_ID not between", value1, value2, "cityId");
            return (Criteria) this;
        }

        public Criteria andProvinceIdIsNull() {
            addCriterion("PROVINCE_ID is null");
            return (Criteria) this;
        }

        public Criteria andProvinceIdIsNotNull() {
            addCriterion("PROVINCE_ID is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceIdEqualTo(Integer value) {
            addCriterion("PROVINCE_ID =", value, "provinceId");
            return (Criteria) this;
        }

        public Criteria andProvinceIdNotEqualTo(Integer value) {
            addCriterion("PROVINCE_ID <>", value, "provinceId");
            return (Criteria) this;
        }

        public Criteria andProvinceIdGreaterThan(Integer value) {
            addCriterion("PROVINCE_ID >", value, "provinceId");
            return (Criteria) this;
        }

        public Criteria andProvinceIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("PROVINCE_ID >=", value, "provinceId");
            return (Criteria) this;
        }

        public Criteria andProvinceIdLessThan(Integer value) {
            addCriterion("PROVINCE_ID <", value, "provinceId");
            return (Criteria) this;
        }

        public Criteria andProvinceIdLessThanOrEqualTo(Integer value) {
            addCriterion("PROVINCE_ID <=", value, "provinceId");
            return (Criteria) this;
        }

        public Criteria andProvinceIdIn(List<Integer> values) {
            addCriterion("PROVINCE_ID in", values, "provinceId");
            return (Criteria) this;
        }

        public Criteria andProvinceIdNotIn(List<Integer> values) {
            addCriterion("PROVINCE_ID not in", values, "provinceId");
            return (Criteria) this;
        }

        public Criteria andProvinceIdBetween(Integer value1, Integer value2) {
            addCriterion("PROVINCE_ID between", value1, value2, "provinceId");
            return (Criteria) this;
        }

        public Criteria andProvinceIdNotBetween(Integer value1, Integer value2) {
            addCriterion("PROVINCE_ID not between", value1, value2, "provinceId");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIsNull() {
            addCriterion("DELIVERY_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIsNotNull() {
            addCriterion("DELIVERY_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusEqualTo(Integer value) {
            addCriterion("DELIVERY_STATUS =", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotEqualTo(Integer value) {
            addCriterion("DELIVERY_STATUS <>", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusGreaterThan(Integer value) {
            addCriterion("DELIVERY_STATUS >", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_STATUS >=", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusLessThan(Integer value) {
            addCriterion("DELIVERY_STATUS <", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusLessThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_STATUS <=", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIn(List<Integer> values) {
            addCriterion("DELIVERY_STATUS in", values, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotIn(List<Integer> values) {
            addCriterion("DELIVERY_STATUS not in", values, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_STATUS between", value1, value2, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_STATUS not between", value1, value2, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoIsNull() {
            addCriterion("LOGISTICS_NO is null");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoIsNotNull() {
            addCriterion("LOGISTICS_NO is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoEqualTo(String value) {
            addCriterion("LOGISTICS_NO =", value, "logisticsNo");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoNotEqualTo(String value) {
            addCriterion("LOGISTICS_NO <>", value, "logisticsNo");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoGreaterThan(String value) {
            addCriterion("LOGISTICS_NO >", value, "logisticsNo");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoGreaterThanOrEqualTo(String value) {
            addCriterion("LOGISTICS_NO >=", value, "logisticsNo");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoLessThan(String value) {
            addCriterion("LOGISTICS_NO <", value, "logisticsNo");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoLessThanOrEqualTo(String value) {
            addCriterion("LOGISTICS_NO <=", value, "logisticsNo");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoLike(String value) {
            addCriterion("LOGISTICS_NO like", value, "logisticsNo");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoNotLike(String value) {
            addCriterion("LOGISTICS_NO not like", value, "logisticsNo");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoIn(List<String> values) {
            addCriterion("LOGISTICS_NO in", values, "logisticsNo");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoNotIn(List<String> values) {
            addCriterion("LOGISTICS_NO not in", values, "logisticsNo");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoBetween(String value1, String value2) {
            addCriterion("LOGISTICS_NO between", value1, value2, "logisticsNo");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoNotBetween(String value1, String value2) {
            addCriterion("LOGISTICS_NO not between", value1, value2, "logisticsNo");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressInfoIsNull() {
            addCriterion("TRADER_CONTACT_ADDRESS_INFO is null");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressInfoIsNotNull() {
            addCriterion("TRADER_CONTACT_ADDRESS_INFO is not null");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressInfoEqualTo(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS_INFO =", value, "traderContactAddressInfo");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressInfoNotEqualTo(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS_INFO <>", value, "traderContactAddressInfo");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressInfoGreaterThan(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS_INFO >", value, "traderContactAddressInfo");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressInfoGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS_INFO >=", value, "traderContactAddressInfo");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressInfoLessThan(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS_INFO <", value, "traderContactAddressInfo");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressInfoLessThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS_INFO <=", value, "traderContactAddressInfo");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressInfoLike(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS_INFO like", value, "traderContactAddressInfo");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressInfoNotLike(String value) {
            addCriterion("TRADER_CONTACT_ADDRESS_INFO not like", value, "traderContactAddressInfo");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressInfoIn(List<String> values) {
            addCriterion("TRADER_CONTACT_ADDRESS_INFO in", values, "traderContactAddressInfo");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressInfoNotIn(List<String> values) {
            addCriterion("TRADER_CONTACT_ADDRESS_INFO not in", values, "traderContactAddressInfo");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressInfoBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_ADDRESS_INFO between", value1, value2, "traderContactAddressInfo");
            return (Criteria) this;
        }

        public Criteria andTraderContactAddressInfoNotBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_ADDRESS_INFO not between", value1, value2, "traderContactAddressInfo");
            return (Criteria) this;
        }

        public Criteria andExpressLabelurlIsNull() {
            addCriterion("EXPRESS_LABELURL is null");
            return (Criteria) this;
        }

        public Criteria andExpressLabelurlIsNotNull() {
            addCriterion("EXPRESS_LABELURL is not null");
            return (Criteria) this;
        }

        public Criteria andExpressLabelurlEqualTo(String value) {
            addCriterion("EXPRESS_LABELURL =", value, "expressLabelurl");
            return (Criteria) this;
        }

        public Criteria andExpressLabelurlNotEqualTo(String value) {
            addCriterion("EXPRESS_LABELURL <>", value, "expressLabelurl");
            return (Criteria) this;
        }

        public Criteria andExpressLabelurlGreaterThan(String value) {
            addCriterion("EXPRESS_LABELURL >", value, "expressLabelurl");
            return (Criteria) this;
        }

        public Criteria andExpressLabelurlGreaterThanOrEqualTo(String value) {
            addCriterion("EXPRESS_LABELURL >=", value, "expressLabelurl");
            return (Criteria) this;
        }

        public Criteria andExpressLabelurlLessThan(String value) {
            addCriterion("EXPRESS_LABELURL <", value, "expressLabelurl");
            return (Criteria) this;
        }

        public Criteria andExpressLabelurlLessThanOrEqualTo(String value) {
            addCriterion("EXPRESS_LABELURL <=", value, "expressLabelurl");
            return (Criteria) this;
        }

        public Criteria andExpressLabelurlLike(String value) {
            addCriterion("EXPRESS_LABELURL like", value, "expressLabelurl");
            return (Criteria) this;
        }

        public Criteria andExpressLabelurlNotLike(String value) {
            addCriterion("EXPRESS_LABELURL not like", value, "expressLabelurl");
            return (Criteria) this;
        }

        public Criteria andExpressLabelurlIn(List<String> values) {
            addCriterion("EXPRESS_LABELURL in", values, "expressLabelurl");
            return (Criteria) this;
        }

        public Criteria andExpressLabelurlNotIn(List<String> values) {
            addCriterion("EXPRESS_LABELURL not in", values, "expressLabelurl");
            return (Criteria) this;
        }

        public Criteria andExpressLabelurlBetween(String value1, String value2) {
            addCriterion("EXPRESS_LABELURL between", value1, value2, "expressLabelurl");
            return (Criteria) this;
        }

        public Criteria andExpressLabelurlNotBetween(String value1, String value2) {
            addCriterion("EXPRESS_LABELURL not between", value1, value2, "expressLabelurl");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoEditIsNull() {
            addCriterion("LOGISTICS_NO_EDIT is null");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoEditIsNotNull() {
            addCriterion("LOGISTICS_NO_EDIT is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoEditEqualTo(String value) {
            addCriterion("LOGISTICS_NO_EDIT =", value, "logisticsNoEdit");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoEditNotEqualTo(String value) {
            addCriterion("LOGISTICS_NO_EDIT <>", value, "logisticsNoEdit");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoEditGreaterThan(String value) {
            addCriterion("LOGISTICS_NO_EDIT >", value, "logisticsNoEdit");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoEditGreaterThanOrEqualTo(String value) {
            addCriterion("LOGISTICS_NO_EDIT >=", value, "logisticsNoEdit");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoEditLessThan(String value) {
            addCriterion("LOGISTICS_NO_EDIT <", value, "logisticsNoEdit");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoEditLessThanOrEqualTo(String value) {
            addCriterion("LOGISTICS_NO_EDIT <=", value, "logisticsNoEdit");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoEditLike(String value) {
            addCriterion("LOGISTICS_NO_EDIT like", value, "logisticsNoEdit");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoEditNotLike(String value) {
            addCriterion("LOGISTICS_NO_EDIT not like", value, "logisticsNoEdit");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoEditIn(List<String> values) {
            addCriterion("LOGISTICS_NO_EDIT in", values, "logisticsNoEdit");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoEditNotIn(List<String> values) {
            addCriterion("LOGISTICS_NO_EDIT not in", values, "logisticsNoEdit");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoEditBetween(String value1, String value2) {
            addCriterion("LOGISTICS_NO_EDIT between", value1, value2, "logisticsNoEdit");
            return (Criteria) this;
        }

        public Criteria andLogisticsNoEditNotBetween(String value1, String value2) {
            addCriterion("LOGISTICS_NO_EDIT not between", value1, value2, "logisticsNoEdit");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated do_not_delete_during_merge Fri Nov 22 14:17:41 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_FILE_DELIVERY_ADDRESS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}