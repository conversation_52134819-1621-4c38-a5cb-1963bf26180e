package com.vedeng.erp.saleorder.buzlogic.create.impl;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.saleorder.buzlogic.create.EditSaleOrderBuzLogic;
import com.vedeng.erp.saleorder.service.BaseSaleOrderService;
import com.vedeng.order.model.Saleorder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Author: thor
 * @Date: 2021/10/8 13 56
 * @Description:
 */
@Service
public class HcEditSaleOrderBuzLogic extends EditSaleOrderBuzLogic {
    public HcEditSaleOrderBuzLogic(){
        List<String> sequence = new ArrayList<>();
        sequence.add("saveEditSaleOrderInfo");
        super.setSequence(sequence);
    }

    public ResultInfo run(Saleorder saleorder){
        ResultInfo result = new ResultInfo(0,"操作成功");
        new HcEditSaleOrderBuzLogic();
        for (String methodName : getSequence()){
            switch (methodName) {
                case "saveEditSaleOrderInfo":
                    if(ErpConst.ZERO.equals(result.getCode())) {
                        result = super.saveEditSaleOrderInfo(saleorder);
                    }
                    break;
                default:
                    break;
            }
        }
        return result;
    }
}
