package com.vedeng.erp.finance.service;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.erp.finance.enums.TaxesInterfaceCodeEnum;
import com.vedeng.infrastructure.taxes.domain.TaxesCommonApiResult;
import com.vedeng.infrastructure.taxes.sevice.ITaxesBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 二级抽象类 （针对公共接口）
 */
@Slf4j
public abstract class AbstractTaxesCommonApiHandler extends AbstractTaxesHandler{

    @Autowired
    ITaxesBaseService iTaxesBaseService;

    /**
     * 入口
     * @param taxesParam
     * @param taxesInterfaceCodeEnum
     * @return
     */
    public ITaxesResult openapi(ITaxesParam taxesParam, TaxesInterfaceCodeEnum taxesInterfaceCodeEnum){
        // 前置处理
        beforeExecute(taxesParam);
        // 执行
        ITaxesResult executeResult = execute(taxesParam,taxesInterfaceCodeEnum);
        // 后置处理
        ITaxesResult iTaxesOrgResult = afterExecute(taxesParam,executeResult);
        return iTaxesOrgResult;
    }

    private void beforeExecute(ITaxesParam taxesParam){
        log.info("前置处理 {} 算签前原入参：{}", this.getClass().getSimpleName(),JSONObject.toJSONString(taxesParam));
    }

    private ITaxesResult execute(ITaxesParam taxesParam,TaxesInterfaceCodeEnum taxesInterfaceCodeEnum){
        TaxesCommonApiResult resultStr = (TaxesCommonApiResult)iTaxesBaseService.commonapi(taxesParam, taxesInterfaceCodeEnum.getInterfaceCode());
        return resultStr;
    }

    protected abstract ITaxesResult afterExecute(ITaxesParam taxesParam, ITaxesResult executeResult);
}
