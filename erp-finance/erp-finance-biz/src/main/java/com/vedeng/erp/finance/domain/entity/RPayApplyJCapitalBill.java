package com.vedeng.erp.finance.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 付款申请与资金流水关系表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RPayApplyJCapitalBill {
    /**
    * 主键自增ID
    */
    private Integer rPayApplyJCapitalBillId;

    /**
    * 付款申请ID
    */
    private Integer payApplyId;

    /**
    * 资金流水ID
    */
    private Integer capitalBillId;
}