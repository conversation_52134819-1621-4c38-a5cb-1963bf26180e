package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.TraderCustomerAttributeDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 客户属性
 * @date 2023/8/11 16:36
 **/
public interface TraderCustomerAttributeApiService {

    /**
     * 批量保存
     * @param traderCustomerAttributeDtoList 入参
     */
    void addAll(List<TraderCustomerAttributeDto> traderCustomerAttributeDtoList);

    /**
     * 根据客户id删除
     * @param traderCustomerId 客户id
     */
    void deleteByTraderCustomerId(Integer traderCustomerId);
}
