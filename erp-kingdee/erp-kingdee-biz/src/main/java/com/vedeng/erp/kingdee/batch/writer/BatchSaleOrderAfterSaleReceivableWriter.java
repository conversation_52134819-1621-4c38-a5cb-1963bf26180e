package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveCommonCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveCommonConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeReceiveCommonMapper;
import com.vedeng.erp.kingdee.service.KingDeeReceiveCommonApiService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售负向应付单推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchSaleOrderAfterSaleReceivableWriter extends BaseWriter<KingDeeReceiveCommonDto> {

    @Autowired
    private KingDeeReceiveCommonApiService kingDeeReceiveCommonApiService;

    @Override
    public void doWrite(KingDeeReceiveCommonDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("销售退货应收单：{}", JSON.toJSONString(item));
        item.setKingDeeBizEnums(KingDeeBizEnums.saveReceiveCommon);
        kingDeeReceiveCommonApiService.register(item,true);

    }
}
