package com.vedeng.erp.mobile.domain;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
    * 快递单
    */
@Data
public class Express {
    private Integer expressId;

    /**
    * 物流公司dbcenter 物流表
    */
    private Integer logisticsId;

    /**
    * 物流单号
    */
    private String logisticsNo;

    /**
    * ERP公司ID(T_COMPANY)
    */
    private Integer companyId;

    /**
    * 寄送时间
    */
    private Long deliveryTime;

    /**
    * 客户收货状态0未收货 1部分收货 2全部收货
    */
    private Boolean arrivalStatus;

    /**
    * 客户收货时间
    */
    private Long arrivalTime;

    /**
    * 始发地地区选择最小级ID
    */
    private Integer deliveryFrom;

    /**
    * 物流备注
    */
    private String logisticsComments;

    /**
    * 是否有效 0否 1是
    */
    private Boolean isEnable;

    /**
    * 付款方式1寄付月结 2寄付现结 3到付现结 4第三方付
    */
    private Boolean paymentType;

    /**
    * 月结卡号
    */
    private String cardNumber;

    /**
    * 业务类型1顺丰特惠 2物流普运 3顺丰标快
    */
    private Boolean businessType;

    /**
    * 实际重量
    */
    private BigDecimal realWeight;

    /**
    * 件数
    */
    private Integer num;

    /**
    * 计费重量
    */
    private BigDecimal amountWeight;

    /**
    * 托寄物
    */
    private String mailGoods;

    /**
    * 托寄物数量
    */
    private Integer mailGoodsNum;

    /**
    * 是否保价 0否 1是
    */
    private Boolean isProtectPrice;

    /**
    * 保价金额
    */
    private BigDecimal protectPrice;

    /**
    * 是否签回单0否 1是
    */
    private Boolean isReceipt;

    /**
    * 寄方备注（默认:节假日正常派送，提前联系）
    */
    private String mailCommtents;

    /**
    * 是否已发送短信0否1是
    */
    private Boolean sentSms;

    /**
    * 创建时间
    */
    private Long addTime;

    /**
    * 创建人
    */
    private Integer creator;

    /**
    * 更新时间
    */
    private Long modTime;

    /**
    * 更新人
    */
    private Integer updater;

    /**
    * 是否票货同行0为不同行1为同行
    */
    private Boolean travelingByTicket;

    /**
    * 是否开据发票0为无需开票1为待开票2为已开票
    */
    private Boolean isInvoicing;

    /**
    * 包裹回传WMS订单号
    */
    private String wmsOrderNo;

    /**
    * 审计-运输日期
    */
    private Date transportDate;

    /**
    * 审计-收件公司
    */
    private String receivedCompany;

    /**
    * 审计-地址
    */
    private String receivedAddress;

    /**
    * 审计-收件人
    */
    private String receivedUsername;

    /**
    * 审计-签收日期
    */
    private Date signedDate;

    /**
    * 审计-不含税金额
    */
    private BigDecimal withoutTaxAmount;

    /**
    * 审计-快递公司
    */
    private String expressName;

    /**
    * 签收记录ID 在线签收表
    */
    private Integer onlineReceiptId;

    /**
    * 批次编号
    */
    private String batchNo;

    /**
    * 系统添加时间-不要修改
    */
    private Date systemAddTime;

    /**
    * 是否可收货（0否 1是）
    */
    private Boolean enableReceive;

    /**
    * (快递反哺) 原快递单号
    */
    private String oldLogisticsNo;

    /**
    * 是否已拦截 0 否 1是
    */
    private Boolean isIntercepted;

    /**
    * 拦截时间
    */
    private Long interceptTime;
}