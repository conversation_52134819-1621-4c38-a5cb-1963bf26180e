package com.vedeng.erp.saleorder.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.system.service.NewMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 海报中心菜单跳转
 * @date 2022/8/11 15:38
 */
@Controller
@RequestMapping("/poster/center")
@Slf4j
public class PosterCenterController {

    @Value("${poster_center_index}")
    private String indexUrl;

    @Value("${poster_center_verify}")
    private String verifyUrl;

    @Autowired
    private NewMessageService newMessageService;

    /**
     * 跳转至海报中心首页
     *
     * @return ModelAndView
     */
    @RequestMapping(value = "/index")
    public ModelAndView index() {
        ModelAndView modelAndView = new ModelAndView("postercenter/index");
        modelAndView.addObject("indexUrl", indexUrl);
        return modelAndView;
    }

    /**
     * 跳转至 海报中心审核页
     *
     * @return ModelAndView
     */
    @RequestMapping(value = "/verify")
    public ModelAndView verify() {
        ModelAndView modelAndView = new ModelAndView("postercenter/verify");
        modelAndView.addObject("verifyUrl", verifyUrl);
        return modelAndView;
    }

    /**
     * 查询当前用户未读的海报审核站内信数量
     * @param userId 当前登录用户id
     * @return 未读消息数量
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getUnReadPosterVerifyMessage")
    @ResponseBody
    public ResultInfo<?> getUnReadPosterVerifyMessage(@RequestParam Integer userId) {
        return ResultInfo.success(newMessageService.getPosterCenterUnReadMessage(userId));
    }


}