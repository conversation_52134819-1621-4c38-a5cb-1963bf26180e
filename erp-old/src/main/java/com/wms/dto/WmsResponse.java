package com.wms.dto;

public class WmsResponse<T> {

    private String returnCode;

    private String returnDesc;

    private String returnFlag;

    private String totalCount;

    private T data;

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnDesc() {
        return returnDesc;
    }

    public void setReturnDesc(String returnDesc) {
        this.returnDesc = returnDesc;
    }

    public String getReturnFlag() {
        return returnFlag;
    }

    public void setReturnFlag(String returnFlag) {
        this.returnFlag = returnFlag;
    }

    public String getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(String totalCount) {
        this.totalCount = totalCount;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "WmsResponse{" +
                "returnCode='" + returnCode + '\'' +
                ", returnDesc='" + returnDesc + '\'' +
                ", returnFlag='" + returnFlag + '\'' +
                ", totalCount='" + totalCount + '\'' +
                ", data=" + data +
                '}';
    }
}
