package com.vedeng.docSync.model.pojo.generate;

import java.io.Serializable;
import lombok.Data;

/**
 * T_DOC_BUZ_TAG
 * <AUTHOR>
@Data
public class DocBuzTagDo implements Serializable {
    private Integer id;

    /**
     * 业务类型，1：商品资料，2：厂商资质
     */
    private Integer buzType;

    /**
     * 业务标签名称
     */
    private String buzTagName;

    /**
     * 是否必填项，0：否，1：是
     */
    private Integer required;

    /**
     * 支持上传文件个数
     */
    private Integer maxCount;

    /**
     * 是否需要维护有效期，0：否，1：是
     */
    private Integer maintainValidPeriod;

    /**
     * 是否需要保存变更历史记录，0：否，1：是
     */
    private Integer saveMaintainLog;

    /**
     * 是否需要提供含章文件，0：否，1：是，2：都需要
     */
    private Byte needHasStamp;

    /**
     * 文件来源，0：资料库系统内部，1：外部
     */
    private Integer source;

    /**
     * 外部跳转链接
     */
    private String externalUrl;

    /**
     * 参数展示或隐藏，0：展示，1：隐藏
     */
    private Integer hidden;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 是否删除
     */
    private Integer isDelete;

    private static final long serialVersionUID = 1L;
}