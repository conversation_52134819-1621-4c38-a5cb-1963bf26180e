<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeSupplierMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_SUPPLIER-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="F_NUMBER" jdbcType="INTEGER" property="fNumber" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="F_SUPPLIER_ID" jdbcType="VARCHAR" property="fSupplierId" />
    <result column="F_CREATE_ORG_ID" jdbcType="INTEGER" property="fCreateOrgId" />
    <result column="F_USE_ORG_ID" jdbcType="INTEGER" property="fUseOrgId" />
    <result column="F_NAME" jdbcType="VARCHAR" property="fName" />
    <result column="F_BASE_INFO" jdbcType="VARCHAR" property="fBaseInfo" />
    <result column="FSUPPLIERCLASSIFY" jdbcType="VARCHAR" property="fsupplierclassify" />
    <result column="FBANKINFO" jdbcType="LONGVARCHAR" property="fbankinfo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, F_NUMBER, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, F_SUPPLIER_ID, 
    F_CREATE_ORG_ID, F_USE_ORG_ID, F_NAME, F_BASE_INFO, FSUPPLIERCLASSIFY, FBANKINFO
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_SUPPLIER
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_SUPPLIER
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_SUPPLIER (F_NUMBER, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, CREATOR_NAME, 
      UPDATER_NAME, F_SUPPLIER_ID, F_CREATE_ORG_ID, 
      F_USE_ORG_ID, F_NAME, F_BASE_INFO, 
      FSUPPLIERCLASSIFY, FBANKINFO)
    values (#{fNumber,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updaterName,jdbcType=VARCHAR}, #{fSupplierId,jdbcType=VARCHAR}, #{fCreateOrgId,jdbcType=INTEGER}, 
      #{fUseOrgId,jdbcType=INTEGER}, #{fName,jdbcType=VARCHAR}, #{fBaseInfo,jdbcType=VARCHAR}, 
      #{fsupplierclassify,jdbcType=VARCHAR}, #{fbankinfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_SUPPLIER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fNumber != null">
        F_NUMBER,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fSupplierId != null">
        F_SUPPLIER_ID,
      </if>
      <if test="fCreateOrgId != null">
        F_CREATE_ORG_ID,
      </if>
      <if test="fUseOrgId != null">
        F_USE_ORG_ID,
      </if>
      <if test="fName != null">
        F_NAME,
      </if>
      <if test="fBaseInfo != null">
        F_BASE_INFO,
      </if>
      <if test="fsupplierclassify != null">
        FSUPPLIERCLASSIFY,
      </if>
      <if test="fbankinfo != null">
        FBANKINFO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fNumber != null">
        #{fNumber,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fSupplierId != null">
        #{fSupplierId,jdbcType=VARCHAR},
      </if>
      <if test="fCreateOrgId != null">
        #{fCreateOrgId,jdbcType=INTEGER},
      </if>
      <if test="fUseOrgId != null">
        #{fUseOrgId,jdbcType=INTEGER},
      </if>
      <if test="fName != null">
        #{fName,jdbcType=VARCHAR},
      </if>
      <if test="fBaseInfo != null">
        #{fBaseInfo,jdbcType=VARCHAR},
      </if>
      <if test="fsupplierclassify != null">
        #{fsupplierclassify,jdbcType=VARCHAR},
      </if>
      <if test="fbankinfo != null">
        #{fbankinfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity">
    <!--@mbg.generated-->
    update KING_DEE_SUPPLIER
    <set>
      <if test="fNumber != null">
        F_NUMBER = #{fNumber,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fSupplierId != null">
        F_SUPPLIER_ID = #{fSupplierId,jdbcType=VARCHAR},
      </if>
      <if test="fCreateOrgId != null">
        F_CREATE_ORG_ID = #{fCreateOrgId,jdbcType=INTEGER},
      </if>
      <if test="fUseOrgId != null">
        F_USE_ORG_ID = #{fUseOrgId,jdbcType=INTEGER},
      </if>
      <if test="fName != null">
        F_NAME = #{fName,jdbcType=VARCHAR},
      </if>
      <if test="fBaseInfo != null">
        F_BASE_INFO = #{fBaseInfo,jdbcType=VARCHAR},
      </if>
      <if test="fsupplierclassify != null">
        FSUPPLIERCLASSIFY = #{fsupplierclassify,jdbcType=VARCHAR},
      </if>
      <if test="fbankinfo != null">
        FBANKINFO = #{fbankinfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity">
    <!--@mbg.generated-->
    update KING_DEE_SUPPLIER
    set F_NUMBER = #{fNumber,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      F_SUPPLIER_ID = #{fSupplierId,jdbcType=VARCHAR},
      F_CREATE_ORG_ID = #{fCreateOrgId,jdbcType=INTEGER},
      F_USE_ORG_ID = #{fUseOrgId,jdbcType=INTEGER},
      F_NAME = #{fName,jdbcType=VARCHAR},
      F_BASE_INFO = #{fBaseInfo,jdbcType=VARCHAR},
      FSUPPLIERCLASSIFY = #{fsupplierclassify,jdbcType=VARCHAR},
      FBANKINFO = #{fbankinfo,jdbcType=LONGVARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update KING_DEE_SUPPLIER
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="F_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fNumber != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fNumber,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_SUPPLIER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fSupplierId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fSupplierId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_CREATE_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fCreateOrgId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fCreateOrgId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_USE_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fUseOrgId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fUseOrgId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_BASE_INFO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fBaseInfo != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fBaseInfo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FSUPPLIERCLASSIFY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fsupplierclassify != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fsupplierclassify,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FBANKINFO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fbankinfo != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fbankinfo,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_SUPPLIER
    (F_NUMBER, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, F_SUPPLIER_ID, 
      F_CREATE_ORG_ID, F_USE_ORG_ID, F_NAME, F_BASE_INFO, FSUPPLIERCLASSIFY, FBANKINFO
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fNumber,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.updaterName,jdbcType=VARCHAR}, #{item.fSupplierId,jdbcType=VARCHAR}, #{item.fCreateOrgId,jdbcType=INTEGER}, 
        #{item.fUseOrgId,jdbcType=INTEGER}, #{item.fName,jdbcType=VARCHAR}, #{item.fBaseInfo,jdbcType=VARCHAR}, 
        #{item.fsupplierclassify,jdbcType=VARCHAR}, #{item.fbankinfo,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>

  <select id="queryInfoBySupplierId" resultType="com.vedeng.erp.kingdee.dto.KingDeeSupplierDto">
    SELECT * FROM
    KING_DEE_SUPPLIER
    WHERE F_NUMBER = #{traderSupplierId,jdbcType=INTEGER}
  </select>

  <select id="selectByFNumber" resultMap="BaseResultMap">
    SELECT * FROM
    KING_DEE_SUPPLIER
    WHERE F_NUMBER = #{fNumber,jdbcType=INTEGER}
  </select>

  <select id="queryInfoBySupplierName" resultMap="BaseResultMap">
    SELECT * FROM
      KING_DEE_SUPPLIER
    WHERE F_NAME = #{traderSupperlierName,jdbcType=VARCHAR}
  </select>
</mapper>