package com.vedeng.logistics.model.vo;

import com.vedeng.logistics.model.WarehouseGoodsOutInItem;
import lombok.Data;

/**
 * @description:
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/15 16:40
 **/
@Data
public class WarehouseGoodsOutLogVo extends WarehouseGoodsOutInItem {
    /**
     * String 类型 goodsId
     */
    private String skuId;

    /**
     * Long 出入库主表 ID
     */
    private Long warehouseGoodsOutInId;

    /**
     * addTime 字符串类型
     */
    private String addTimeStr;

    /**
     * 是否赠品 1是 其他为否
     */
    private Integer giftGoods;

}
