<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<meta charset="UTF-8">
<div id="app" style="display: none;">
    <vxe-grid ref='xGrid' v-bind="gridOptions" >
        <%--    表单搜索    --%>

            <template #form_tranflow="{ data }">
                <vxe-input v-model="data.tranFlow" maxlength="100" placeholder="请输入完整流水号搜索"  size="mini" clearable></vxe-input>
            </template>

            <template #form_accname1="{ data }">
                <vxe-input v-model="data.accName1" maxlength="100" placeholder=""  size="mini" clearable></vxe-input>
            </template>
            <template #form_flag1="{ data }">
                <el-select v-model="data.flag1" placeholder="全部" size="mini" style="width: 100%;">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="收入" value="1"></el-option>
                    <el-option label="支出" value="0"></el-option>
                </el-select>
            </template>
            <template #form_trandate="{ data }">
                <el-date-picker
                        v-model="data.trandate"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        size="mini"
                        style="width: 100%;">
                </el-date-picker>
            </template>
            <template #form_message="{ data }">
                <vxe-input v-model="data.message" maxlength="100" placeholder="" size="mini" clearable></vxe-input>
            </template>
            <template #form_det="{ data }">
                <vxe-input v-model="data.det" maxlength="100" placeholder="" size="mini" clearable></vxe-input>
            </template>
            <template #operate_item="{ data }">
                <vxe-button type="submit" status="primary" content="查询" ></vxe-button>
                <vxe-button status="" content="重置" @click="resetForm()" ></vxe-button>

            </template>


        <%--    工具栏--%>
        <template #toolbar_buttons>
            <el-tabs v-model="bankTag" style="margin-left: 10px" @tab-click="changePane">
                <el-tab-pane name="bankTag7">
                    <template slot="label">
                        <el-badge>
                            <span>民生银行</span>
                        </el-badge>

                    </template>
                </el-tab-pane>
                <el-tab-pane name="bankTag6">
                    <template slot="label">
                        <el-badge>
                            <span>交通银行</span>
                        </el-badge>

                    </template>
                </el-tab-pane>

            </el-tabs>

        </template>



        <template #red_confirmation_status="{ row }">
            <span v-if="row.redConfirmationStatus==0">民生银行</span>
            <span v-if="row.redConfirmationStatus==1">交通银行</span>
        </template>



        <template #operate="{ row }">
           <vxe-button  @click="doButton(row)" size="small" type="text" status="primary" >预览</vxe-button>
        </template>


    </vxe-grid>





</div>

<script src="${pageContext.request.contextPath}/static/js/moment/moment.js?rnd=${resourceVersionKey}"></script>


<script type="text/javascript">

    let vm0 = null;
    const sendThis0 = (_this) => {
        vm0 = _this;
    };

    new Vue({
        el: '#app',

        data() {

            return {
                globalUpdateLoading:false,
                allReversalLoading: false,
                allReversalShow:false,
                allReversal:false,
                bankTag: 'bankTag7',
                loading: false,
                errorTable:false,
                errorTableData: [],
                formData: {
                    tranFlow: '',
                    accName1: '',
                    flag1: '',
                    trandate: [],
                    message: '',
                    det:''
                },
                currentRow: {},
                isAllChecked: false,
                isIndeterminate: false,
                selectRecords: [],
                pane_init_num:0,
                pane_applied_num:0,
                pane_confirmed_num:0,
                pickerOptions: {

                },
                gridOptions: {
                    height: 800,
                    size:'mini',
                    align: 'center',
                    border: true,
                    showHeaderOverflow: true,
                    showOverflow: true,
                    keepSource: true,
                    id: 'red_confirm_list',
                    rowId: 'id',
                    rowConfig: {
                        isHover: true
                    },
                    columnConfig: {
                        resizable: true
                    },
                    customConfig: {
                        storage: true,
                    },
                    pagerConfig: {
                        pageSize: 50,
                        pageSizes: [50, 100, 200],
                        slots: {left: 'pager_left'}
                    },

                    // 列表搜索区域
                    formConfig: {
                        size: 'mini',
                        titleWidth: 150,
                        titleAlign: 'right',
                        items: [
                            {
                                field: 'tranFlow',
                                title: '流水号',
                                span: 6,
                                slots: { default: 'form_tranflow' }
                            },
                            {
                                field: 'accName1',
                                title: '对方名称',
                                span: 6,
                                itemRender: {},
                                slots: { default: 'form_accname1' }
                            },
                            {
                                field: 'flag1',
                                title: '收支',
                                span: 6,
                                itemRender: {},
                                slots: { default: 'form_flag1' }
                            },
                            {
                                field: 'trandate',
                                title: '交易时间',
                                span: 6,
                                itemRender: {},
                                slots: { default: 'form_trandate' }
                            },
                            {
                                field: 'message',
                                title: '摘要',
                                span: 6,
                                itemRender: {},
                                slots: { default: 'form_message' }
                            },
                            {
                                field: 'det',
                                title: '备注',
                                span: 6,
                                itemRender: {},
                                slots: { default: 'form_det' }
                            },
                            {span:24,align:'center',slots: {default: 'operate_item'}}
                            // {
                            //     span: 24,
                            //     align: 'center',
                            //     collapseNode: true,
                            //     itemRender: {
                            //         name: '$buttons',
                            //         children: [
                            //             {
                            //                 props: {
                            //                     type: 'submit',
                            //                     content: '搜索',
                            //                     status: 'primary'
                            //                 }
                            //             },
                            //             {
                            //                 props: {
                            //                     type: 'reset',
                            //                     content: '重置',
                            //                 }
                            //                 // ,
                            //                 // on: {
                            //                 //     click: () => this.resetForm()
                            //                 // }
                            //             }
                            //         ]
                            //     }
                            // }
                        ]
                    },
                    toolbarConfig: {
                        slots: {buttons: 'toolbar_buttons'},
                        custom: true
                    },
                    proxyConfig: {
                        seq: false, //启用动态序号代理，每一页的序号会根据当前页数变化
                        form: true, //启用表单代理，当点击表单提交按钮时会自动触发 reload 行为
                        props: {
                            result: 'list', // 配置响应结果列表字段
                            total: 'total' // 配置响应结果总页数字段
                        },
                        // 只接收Promise，具体实现自由发挥
                        ajax: {
                            // 当点击工具栏查询按钮或者手动提交指令 query或reload 时会被触发
                            query: ({page, sorts, filters, form}) => {
                                let that = this;
                                const queryParams = Object.assign({}, form);

                                // 清空选中行
                                this.selectRecords = [];
                                if (queryParams.trandate != null && queryParams.trandate.length > 0) {
                                    queryParams.beginTime = this.formatSelectDate(queryParams.trandate[0],'00:00:00');
                                    queryParams.endTime = this.formatSelectDate(queryParams.trandate[1],'23:59:59');
                                }
                                if (that.bankTag == 'bankTag7') {
                                    queryParams.bankTag = 7;
                                }
                                if (that.bankTag == 'bankTag6') {
                                    queryParams.bankTag = 6;
                                }
                                //tranFlow
                                //accName1
                                //message
                                //det
                                let query = queryParams;

                                ['tranFlow', 'accName1', 'message', 'det'].forEach(key => {
                                    query[key] = typeof queryParams[key] === 'string' ? queryParams[key].trim() : queryParams[key];
                                });



                                let pageParams = {
                                    pageNum: page.currentPage,
                                    pageSize: page.pageSize,
                                    param: query
                                };
                                return axios({
                                    url: '/finance/capitalbill/HongRuipage.do',
                                    method: 'post',
                                    data: pageParams
                                }).then(response => {
                                    return response.data.data
                                });
                            }
                        }
                    },
                    columns: [
                        /*{ type: 'seq', title: '序号', width: 60 },*/
                        { field: 'bankBillId', title: 'ID', width: 100 },
                        { field: 'tranFlow', title: '流水号',align: 'left',  width: 240,align:'left' },
                        { field: 'accName1', title: '对方名称',align: 'left',  width: 200,align:'left' },
                        { field: 'cadbankNm', title: '对方开户行',align: 'left',  width: 280,align:'left' },
                        { field: 'accno2', title: '对方账号',align: 'left',  width: 180 },
                        { field: 'realTrandatetime', title: '交易时间', formatter: this.formatDate, width: 140 },
                        { field: 'message', title: '摘要',align: 'left',  width: 150 },
                        { field: 'det', title: '备注',align: 'left',  width: 150 },
                        { field: 'flag1', title: '收支', formatter: this.formatFlag1, width: 80 },
                        { field: 'amt', title: '收款金额',align: 'right', formatter: this.formatAmount1, width: 120 },
                        { field: 'amt2', title: '付款金额',align: 'right', formatter: this.formatAmount2, width: 120 },
                        { field: 'amt1', title: '银行余额',align: 'right', formatter: this.formatAmount, width: 120 },
                        {title: '操作', width: 100, fixed: "right", slots: {default: 'operate'}}
                    ],
                    editConfig: {
                        trigger: 'click',
                        mode: 'cell',
                        showStatus: true
                    },
                    checkboxConfig: {
                        reserve: true,
                        highlight: true,
                        range: true
                    }
                },
            }
        },

        watch: {
        },

        created() {

            sendThis0(this);
        },

        mounted() {
            loadingApp()
            const $grid = this.$refs.xGrid
            // if (afterSaleBusinessOrderNo_view != '' && afterSaleBusinessOrderNo_view != undefined) {
            //     $grid.formData.afterSaleBusinessOrderNo = afterSaleBusinessOrderNo_view;
            // }


            // 绑定重置按钮事件
            const resetButton = this.$refs.xGrid.$el.querySelector('.vxe-button--reset');
            if (resetButton) {
                resetButton.addEventListener('click', this.resetForm);
            }
        },

        methods: {
            async getDict() {

                this.findSelectList()
            },
            resetForm() {
                // 清空表单数据
                console.log("Form data before reset:", this.formData);
                this.$refs.xGrid.formData = {
                    tranFlow: '',
                    accName1: '',
                    flag1: '',
                    trandate: [],
                    message: '',
                    det: ''
                };
                this.$forceUpdate();
                console.log("Form data after reset:", this.formData);
                // 重新加载数据
                this.reloadData();
            },
            formatSelectDate(date,time){
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return year+"-"+month+"-"+day+" "+time;
            },
            formatDate({ cellValue }) {
                return XEUtils.toDateString(cellValue, 'yyyy-MM-dd HH:mm:ss')
            },
            formatFlag1({ cellValue }) {
                return cellValue == '1' ? '收入' : '支出'
            },
            formatAmount({ cellValue }) {
                return XEUtils.commafy(cellValue, { digits: 2 })
            },
            formatAmount1({ row }) {
                // return XEUtils.commafy(cellValue, { digits: 2 })
                return row.flag1 == '1' ? XEUtils.commafy(row.amt, { digits: 2 }):'';
            },
            formatAmount2({ row }) {
                // return XEUtils.commafy(cellValue, { digits: 2 })
                return row.flag1 == '1' ?'': XEUtils.commafy(row.amt, { digits: 2 });
            },
            changePane() {
                // this.changePaneFlag = true;
                //debugger
                this.$refs.xGrid.tablePage.currentPage=1;
                this.reloadData();
                // this.changePaneFlag = false;
            },
            doButton(row) {
                //console.log(row);
                if(row.flag1==1){
                    VXETable.modal.message({content: '仅支出流水可预览', status: 'error'});
                }else{
                    openTab("电子回执单", '/finance/capitalbill/credentialsForHongRui.do?bankBillId=' + row.bankBillId);
                }
                // let pageParams=[];
                //pageParams.push(row.invoiceRedConfirmationId)
                // if (button.viewType == 1) {
                //     openModelParam( button.url, button.buttonName, "70%", "100%");
                // }



            },
            async confirmDoButton(row, button) {
            },

            formatDate({cellValue}) {
                return XEUtils.toDateString(cellValue, 'yyyy-MM-dd HH:mm:ss')
            },
            formatRemainingTime(cellValue) {
                if (cellValue <= 0) {
                    return "0小时";
                } else {
                    let day =  Math.floor(cellValue / 24);
                    let hour = cellValue % 24;
                    return day+"天"+hour+"小时";
                }
            },

            formatDate({cellValue}) {
                return XEUtils.toDateString(cellValue, 'yyyy-MM-dd HH:mm:ss')
            },
            beforeBatchApply() {
                let that = this;
                debugger
                if (this.selectRecords.length == 0) {
                    VXETable.modal.message({ content: '请至少选中一条数据', status: 'warning' })
                    return;
                }
                this.allReversalShow = true;
            },
            async allReversalShowTrue() {
                this.allReversalShow = false;
                this.allReversalLoading = true;
                await this.batchApply();
                this.allReversalLoading = false;
                this.allReversal=false
            },
            allReversalShowFalse() {
                this.allReversalShow = false;
                this.allReversal=false
            },
            async batchApply() {
                const loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                let arr = this.selectRecords.map(obj => obj.invoiceRedConfirmationId);
                await batchApplyApi({invoiceRedConfirmationIds:arr,redConfirmationScope:0}).then(res=>{
                    loading.close();
                    if (res.data.success) {
                        debugger;
                        if (res.data.data != undefined && res.data.data.length > 0) {
                            that.errorTable = true;
                            that.errorTableData = res.data.data;
                        } else {
                            VXETable.modal.message({content: '操作成功', status: 'success'});
                            this.reloadData();
                        }
                    } else {
                        VXETable.modal.alert({content: res.data.message, title: '操作失败信息', status: 'error'});
                    }

                });
            },
            reloadData() {
                const $table = this.$refs.xGrid;
                $table.commitProxy('query');
            },
            checkboxChangeEvent() {
                const $grid = this.$refs.xGrid;
                this.isAllChecked = $grid.isAllCheckboxChecked()
                this.isIndeterminate = $grid.isAllCheckboxIndeterminate()
                this.selectRecords = $grid.getCheckboxRecords()
            },
            changeAllEvent() {
                const $grid = this.$refs.xGrid;
                $grid.setAllCheckboxRow(this.isAllChecked)
                this.selectRecords = $grid.getCheckboxRecords()
            }


        }
    });


</script>

<style>
    /*.vxe-row > .vxe-col--6 {*/
    /*    float: none;*/
    /*}*/

    .page-left {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
    }

    .badge-item {
        margin-top: 10px;
        /*margin-right: 15px;*/
    }

    .el-tabs__header {
        padding: 0;
        position: relative;
        margin: 0 0 0px !important;
    }
    .vxe-form--item-trigger-node{
        display: none;
    }

    /*.vxe-button + .vxe-button, .vxe-button + .vxe-button--dropdown, .vxe-input + .vxe-button, .vxe-input + .vxe-button--dropdown {*/
    /*    margin-left: 0px !important;*/
    /*}*/

</style>


</body>
</html>
