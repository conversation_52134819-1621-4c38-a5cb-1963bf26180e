package com.vedeng.todolist.service.impl;

import com.vedeng.authorization.dao.RoleMapper;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.TraderConstants;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.RiskCheckLogMapper;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.model.RiskCheckLog;
import com.vedeng.todolist.model.TodoList;
import com.vedeng.todolist.service.ITodoInstance;
import com.vedeng.todolist.service.RiskCheckLogService;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderCustomer;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: daniel
 * @Date: 2020/12/11 10 38
 * @Description:
 */
@Service
public class RiskCheckTraderCustomerCertificate implements ITodoInstance {

    private static final Logger logger = LoggerFactory.getLogger(RiskCheckTraderCustomerCertificate.class);

    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Resource
    private TodoListMapper todoListMapper;

    @Resource
    private RiskCheckLogMapper riskCheckLogMapper;

    @Resource
    private TraderCustomerMapper traderCustomerMapper;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    private TraderMapper traderMapper;

    @Resource
    private RiskCheckLogService riskCheckLogService;

    @Value("${new_trader_certificate_check_startTime}")
    private Long newTraderCertificateCheckStartTime;

    @Override
    public synchronized void add(Integer buzId, String buzExtra, String comment, String buzProperty) {
        logger.info("待办事项---风控校验客户资质，buzId:{}，buzExtra：{}生成待办事项",buzId,buzExtra);
        riskCheckLogService.sendMessage2SaleWhenHcOrderRiskCheck(buzExtra);
        if (todoListMapper.selectUnHandledByBuzTypeAndBuzIdAndBuzExtra(TodoListBuzSceneEnum.RISK_CHECK_TRADER_CUSTOMER_CERTIFICATE.getBuzSceneId(), buzId,
                buzExtra).size() > 0) {
            todoListMapper.deleteByBuzTypeAndBuzIdAndBuzExtra(TodoListBuzSceneEnum.RISK_CHECK_TRADER_CUSTOMER_CERTIFICATE.getBuzSceneId(), buzId,buzExtra);
            riskCheckLogMapper.deleteByBuzTypeAndBuzIdAndBuzExtra(TodoListBuzSceneEnum.RISK_CHECK_TRADER_CUSTOMER_CERTIFICATE.getBuzSceneId(), buzId,buzExtra);
        }
        TodoList instance = new TodoList(TodoListBuzSceneEnum.RISK_CHECK_TRADER_CUSTOMER_CERTIFICATE,buzId,buzExtra,buzProperty,comment,
                DateUtil.sysTimeMillis(),2);
        todoListMapper.insertSelective(instance);
        RiskCheckLog riskCheckLog = new RiskCheckLog(instance.getId(),TodoListBuzSceneEnum.RISK_CHECK_TRADER_CUSTOMER_CERTIFICATE.getBuzSceneId(),
                buzId,
                buzExtra,
                buzProperty,DateUtil.sysTimeMillis());
        riskCheckLogMapper.insertSelective(riskCheckLog);
        //当客户资质处于初审或者复审时，给对应的审核人发送站内信
        sendMessageWhenTraderCertificateInChecking(buzId);
    }

    @Override
    public void finish(Integer buzId) {
        todoListMapper.updateStatusByBuzTypeAndBuzId(TodoListBuzSceneEnum.RISK_CHECK_TRADER_CUSTOMER_CERTIFICATE.getBuzSceneId(),buzId,1,
                                                    getCurrentRequestUser().getUserId(),
                                                    System.currentTimeMillis());
        logger.info("待办事项---风控校验客户资质，buzId;{}更新为已完成",buzId);
    }

    /**
     * 当客户资质审核处于审核中时，初审状态给"贝登商城客户运营"角色发送站内信；复审状态给"质量部质量专员"角色发送站内信
     * @param traderId 客户id
     */
    private void sendMessageWhenTraderCertificateInChecking(Integer traderId){
        Trader trader = traderMapper.getTraderByTraderId(traderId);
        if (trader == null) {
            return;
        }
        TraderCustomer tc = traderCustomerMapper.getTraderCustomerById(traderId);
        if (tc == null) {
            return;
        }
        Integer checkStatus = traderCustomerMapper.getTraderCertificateCheckStatusByTraderId(traderId);
        logger.info("风控之客户资质，客户ID：{}，客户资质审核状态：{}",traderId,checkStatus);
        Integer messageTemplateId = 97;
        Map<String,String> params = new HashMap<>(1);
        List<Integer> userList = new ArrayList<>();
        if (TraderConstants.CUSTOMER_FIRST_CHECK.equals(checkStatus)){
            userList = roleMapper.getUserIdByRoleName(TraderConstants.BEDENG_CUSTOMER_OPERATE,1);
        } else if (TraderConstants.CUSTOMER_SECOND_CHECK.equals(checkStatus)){
            if (isNewActivitiInstance(tc.getTraderCustomerId())){
                userList = roleMapper.getUserIdByRoleName(TraderConstants.QUALITY_DEPART_ASSISTANT,1);
            } else {
                userList = roleMapper.getUserIdByRoleName(TraderConstants.VEDENG_YUNYING_ROLE,1);
            }
        }
        if (userList.size() > 0) {
            params.put("traderName",trader.getTraderName());
            String url = String.format(ErpConst.GET_APTITUDE_URL,traderId,tc.getTraderCustomerId());
            MessageUtil.sendMessage(messageTemplateId,userList,params,url);
            logger.info("风控之客户资质，客户ID：{}，给用户：{}发送站内信",traderId, StringUtils.join(userList,","));
        }
    }

    /**
     * 判断是否为新的客户资质审核流程实例
     * @param traderCustomerId 客户id
     * @return 结果
     */
    private Boolean isNewActivitiInstance(Integer traderCustomerId){
        List<HistoricProcessInstance> historicProcessInstance = new ArrayList<>();
        HistoryService historyService = processEngine.getHistoryService();
        historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceBusinessKey("customerAptitude_" + traderCustomerId).orderByProcessInstanceStartTime().asc().list();
        if (historicProcessInstance == null || historicProcessInstance.size() == 0){
            return true;
        } else {
            //获取最新的审核流程实例
            HistoricProcessInstance hi = historicProcessInstance.get(historicProcessInstance.size() - 1);
            List<HistoricActivityInstance> hia = historyService.createHistoricActivityInstanceQuery()
                    .processInstanceId(hi.getId()).orderByHistoricActivityInstanceStartTime().asc().list();
            if (hia.get(0).getEndTime().getTime() < newTraderCertificateCheckStartTime){
                return false;
            } else {
                return true;
            }
        }
    }

}
