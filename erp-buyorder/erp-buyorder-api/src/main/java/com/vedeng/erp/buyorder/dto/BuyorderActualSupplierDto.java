package com.vedeng.erp.buyorder.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购单实际供应商Dto
 */
@Data
public class BuyorderActualSupplierDto {
    /**
    * 主键
    */
    private Long buyorderActualSupplierId;

    /**
    * 采购单主表ID
    */
    private Integer buyorderId;

    /**
    * 交易者ID
    */
    private Integer traderId;

    /**
    * 交易者名称
    */
    private String traderName;

    /**
    * 联系人ID
    */
    private Integer traderContactId;

    /**
    * 联系人
    */
    private String traderContactName;

    /**
    * 手机
    */
    private String traderContactMobile;

    /**
    * 电话
    */
    private String traderContactTelephone;

    /**
    * 付款方式 字典库
    */
    private Integer paymentType;

    /**
    * 预付金额
    */
    private BigDecimal prepaidAmount;

    /**
    * 账期支付金额
    */
    private BigDecimal accountPeriodAmount;

    /**
    * 尾款
    */
    private BigDecimal retainageAmount;

    /**
    * 尾款期限(月)
    */
    private Integer retainageAmountMonth;

    /**
    * 发票类型 字典表
    */
    private Integer invoiceType;

    /**
     * 是否需要发票
     */
    private Integer needInvoice;
}