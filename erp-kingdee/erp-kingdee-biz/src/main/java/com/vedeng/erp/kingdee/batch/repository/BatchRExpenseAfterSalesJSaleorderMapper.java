package com.vedeng.erp.kingdee.batch.repository;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.vedeng.erp.kingdee.batch.dto.BatchRExpenseAfterSalesJSaleorderDto;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/3/21 10:22
 **/
public interface BatchRExpenseAfterSalesJSaleorderMapper {
    int deleteByPrimaryKey(Integer tRExpenseAfterSalesJSaleorderId);

    int insert(BatchRExpenseAfterSalesJSaleorderDto record);

    int insertSelective(BatchRExpenseAfterSalesJSaleorderDto record);

    BatchRExpenseAfterSalesJSaleorderDto selectByPrimaryKey(Integer tRExpenseAfterSalesJSaleorderId);

    int updateByPrimaryKeySelective(BatchRExpenseAfterSalesJSaleorderDto record);

    int updateByPrimaryKey(BatchRExpenseAfterSalesJSaleorderDto record);

    /**
     * 根据销售单加正向明细聚合
     * @param buyorderExpenseId
     * @return
     */
    List<BatchRExpenseAfterSalesJSaleorderDto> findByBuyorderExpenseId(Integer buyorderExpenseId);

    /**
     * 根据费用售后单查
     * @param expenseAfterSalesId
     * @return
     */
    List<BatchRExpenseAfterSalesJSaleorderDto> findByExpenseAfterSalesId(@Param("expenseAfterSalesId")Long expenseAfterSalesId);






}