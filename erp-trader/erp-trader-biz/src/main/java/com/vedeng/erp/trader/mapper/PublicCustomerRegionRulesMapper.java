package com.vedeng.erp.trader.mapper;


import com.vedeng.authorization.model.User;
import com.vedeng.erp.trader.domain.PublicCustomerRegionRules;
import com.vedeng.erp.trader.domain.vo.PublicCustomerRegionRulesVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface PublicCustomerRegionRulesMapper {
    int deleteByPrimaryKey(Integer publicCustomerRegionRulesId);

    int insert(PublicCustomerRegionRules record);

    int insertSelective(PublicCustomerRegionRules record);

    PublicCustomerRegionRules selectByPrimaryKey(Integer publicCustomerRegionRulesId);

    int updateByPrimaryKeySelective(PublicCustomerRegionRules record);

    int updateByPrimaryKey(PublicCustomerRegionRules record);

    List<PublicCustomerRegionRulesVo> queryListPage(Map<String, Object> map);

    int batchDeleteByPrimaryKey(@Param("list") Integer[] idList);

    PublicCustomerRegionRules selectByRegionId(Integer regionId);

    int batchAddPublicCustomerCalculateRules(@Param("list") List<PublicCustomerRegionRules> list);

    List<PublicCustomerRegionRulesVo> queryCustomerRegionRulesList(@Param("regionIdList") List<Integer> regionIdList,@Param("userIdList") List<Integer> userIdList);

}