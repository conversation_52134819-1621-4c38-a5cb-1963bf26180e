package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceDetailJOperateLogDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchRInvoiceDetailJOperateLogDtoMapper;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.dto.result.KingDeeReceiveQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeReceiveCommonService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 销售单蓝票推送，包含专票普票
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/11 11:01
 */
@Service
@Slf4j
public class BatchSaleOrderBlueInvoiceProcessor implements ItemProcessor<BatchInvoiceDto, KingDeeSalesVatPlainAndSpecialInvoiceDto> {

    private static final String SPECIAL_INVOICE = "专用发票";

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    @Autowired
    private KingDeeReceiveCommonService kingDeeReceiveCommonService;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private BatchRInvoiceDetailJOperateLogDtoMapper batchRInvoiceDetailJOperateLogDtoMapper;

    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;


    @Override
    public KingDeeSalesVatPlainAndSpecialInvoiceDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {
        // 筛选出实物商品的蓝票信息
        log.info("构造销售单实物商品蓝票信息开始: {}", JSON.toJSONString(batchInvoiceDto));
        if (StrUtil.isBlank(batchInvoiceDto.getInvoiceNo()) || StrUtil.isBlank(batchInvoiceDto.getInvoiceCode())) {
            log.warn("销售单实物商品蓝票无发票号或者发票代码{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        // 排除蓝字作废
        List<BatchInvoiceDto> blueDisEnableByInvoiceCodeAndInvoiceNo = batchInvoiceDtoMapper.findBlueDisEnableByInvoiceCodeAndInvoiceNo(batchInvoiceDto.getInvoiceCode(), batchInvoiceDto.getInvoiceNo(), 505);
        if (CollUtil.isNotEmpty(blueDisEnableByInvoiceCodeAndInvoiceNo)) {
            log.info("当前蓝票存在蓝字作废{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        List<BatchInvoiceDetailDto> allInvoiceDetailDtoList;
        List<BatchInvoiceDetailDto> physicalInvoiceDetailList;
        if (CollectionUtils.isEmpty(batchInvoiceDto.getBatchInvoiceDetailDtoList())) {
            // 发票明细为空，则表示是非历史数据，直接取数据库查询发票明细
            log.info("开始处理销售实物商品蓝票明细：{}", JSON.toJSONString(batchInvoiceDto));
            allInvoiceDetailDtoList = batchInvoiceDetailDtoMapper.getSaleOrderInvoiceDetailList(batchInvoiceDto.getInvoiceId());
        } else {
            // 否则，则证明是从Excel中读取的发票明细
            log.info("开始处理21-22历史销售实物商品蓝票明细：{}", JSON.toJSONString(batchInvoiceDto));
            allInvoiceDetailDtoList = batchInvoiceDto.getBatchInvoiceDetailDtoList();
        }
        physicalInvoiceDetailList = allInvoiceDetailDtoList.stream().filter(coreSkuVo -> coreSkuVo.getIsVirtureSku() == null || coreSkuVo.getIsVirtureSku() == 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(physicalInvoiceDetailList)) {
            // 当前发票关联的销售单商品没有实物，直接返回
            log.info("当前发票关联的销售单商品没有实物商品：{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }
        KingDeeSalesVatPlainAndSpecialInvoiceDto vatPlainAndSpecialInvoiceDto = new KingDeeSalesVatPlainAndSpecialInvoiceDto();

        // 查询推送过的标准应收单
        List<KingDeeReceiveQueryResultDto> kingDeeSaleReceivable = kingDeeReceiveCommonService.getKingDeeReceiveCommon(batchInvoiceDto.getInvoiceId().toString());
        if (CollectionUtils.isEmpty(kingDeeSaleReceivable)) {
            log.info("未查询到标准应收单,无法推送销售单实物商品蓝票信息: {}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        if (batchInvoiceDto.getInvoiceTypeName().contains(SPECIAL_INVOICE)) {

            KingDeeSalesVatSpecialInvoiceDto query = new KingDeeSalesVatSpecialInvoiceDto();
            query.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            // 判断是否数据已存在
            boolean old = kingDeeBaseApi.isExist(query);
            if (old) {
                log.info("销售实物蓝票专用发票,金蝶数据已存在:{}", JSON.toJSONString(query));
                return null;
            }

            // 专票
            vatPlainAndSpecialInvoiceDto.setSpecial(true);
            // 第一层
            KingDeeSalesVatSpecialInvoiceDto specialInvoiceDto = new KingDeeSalesVatSpecialInvoiceDto();
            specialInvoiceDto.setFid("0");

            // VDERP-15040
            Date outInTime = batchRInvoiceDetailJOperateLogDtoMapper.findWarehouseOutByInvoiceId(batchInvoiceDto.getInvoiceId(), 2);
            specialInvoiceDto.setFdate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getAddTime()).compareTo(outInTime) < 0 ? outInTime : new Date(batchInvoiceDto.getAddTime())));
            specialInvoiceDto.setFinvoicedate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getAddTime())));

            specialInvoiceDto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            specialInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
            specialInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
            specialInvoiceDto.setFcustomerid(batchInvoiceDto.getTraderCustomerId().toString());
            specialInvoiceDto.setFRedBlue(0);
            specialInvoiceDto.setFQzokPzgsywdh(batchInvoiceDto.getOrderNo());//VDERP-16094 票对接金蝶增加入参：归属业务单号

            // 第二层 发票明细
            List<KingDeeSalesVatSpecialInvoiceDetailDto> invoiceDetailDtoList = new ArrayList<>();
            physicalInvoiceDetailList.forEach(detail -> {
                KingDeeSalesVatSpecialInvoiceDetailDto tempDetail = new KingDeeSalesVatSpecialInvoiceDetailDto();
                tempDetail.setFmaterialid(detail.getSku());
                tempDetail.setFpriceqty(detail.getNum());
                // 税率
                BigDecimal taxRatio = batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
                // 不含税单价 = （含税总价/数量）/（1+税率） 用乘法交换律做，防止两次除法过早丢失精度
                BigDecimal noTaxPrice = detail.getTotalAmount().divide(detail.getNum().multiply(batchInvoiceDto.getRatio().add(BigDecimal.ONE)), 6, RoundingMode.HALF_UP);

                // 税额=[价税合计/（1+税率）]*税率 (此处需要先乘再除才能保留两位小数)
                BigDecimal taxAmount = detail.getTotalAmount().multiply(batchInvoiceDto.getRatio()).divide(BigDecimal.ONE.add(batchInvoiceDto.getRatio()), BigDecimal.ROUND_CEILING, RoundingMode.HALF_UP);
                // 不含税金额=价税合计-税额（四舍五入保留2位小数）
                BigDecimal noTaxAmount = detail.getTotalAmount().subtract(taxAmount);

                tempDetail.setFauxprice(noTaxPrice);
                tempDetail.setFtaxrate(taxRatio);
                tempDetail.setFamountfor(noTaxAmount);
                tempDetail.setFdetailtaxamountfor(taxAmount);

                tempDetail.setFQzokBddjhid(detail.getInvoiceDetailId().toString());
                tempDetail.setFQzokYsddh(batchInvoiceDto.getOrderNo());
                tempDetail.setFQzokGsywdh(batchInvoiceDto.getOrderNo());
                tempDetail.setFQzokYwlx("销售");
                tempDetail.setFsrcbilltypeid("AR_receivable");

                // 第三层
                List<KingDeeSalesVatSpecialInvoiceDetailLinkDto> detailLinkDtoList = new ArrayList<>();

                KingDeeReceiveQueryResultDto kingDeeReceiveQueryResultDto = CollUtil.getFirst(kingDeeSaleReceivable);
                List<BatchRInvoiceDetailJOperateLogDto> detailIdAndOperateType = batchRInvoiceDetailJOperateLogDtoMapper.findByInvoiceDetailIdAndOperateType(detail.getInvoiceDetailId(), 2);
                detailIdAndOperateType.forEach(relation -> {
                    String bddjhid = detail.getInvoiceDetailId().toString() + "-" + relation.getOperateLogId().toString();
                    KingDeeReceiveQueryResultDto kingDeeReceiveQueryResultDtoDetail = kingDeeSaleReceivable.stream()
                            .filter(q -> q.getF_QZOK_BDDJHID().equals(bddjhid)).findFirst().orElse(null);
                    if (Objects.isNull(kingDeeReceiveQueryResultDtoDetail)) {
                        log.error("无法查询标准应收单明细单{}", JSON.toJSONString(detail));
                        throw new KingDeeException("无法查询标准应收单明细单");
                    }
                    KingDeeSalesVatSpecialInvoiceDetailLinkDto tempLink = new KingDeeSalesVatSpecialInvoiceDetailLinkDto();
                    tempLink.setFLinkId(0);
                    tempLink.setFsalesicentryLinkFflowid("");
                    tempLink.setFsalesicentryLinkFflowlineid(0);
                    tempLink.setFsalesicentryLinkFruleid("IV_ReceivableToSalesIC");
                    tempLink.setFsalesicentryLinkFstableid("0");
                    tempLink.setFsalesicentryLinkFstablename("t_AR_receivableEntry");
                    tempLink.setFsalesicentryLinkFsbillid(kingDeeReceiveQueryResultDto.getFID());
                    tempLink.setFsalesicentryLinkFsid(kingDeeReceiveQueryResultDtoDetail.getFEntityDetail_FEntryId());
                    tempLink.setFsalesicentryLinkFbasicunitqtyold(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFPriceQty()));
                    tempLink.setFsalesicentryLinkFbasicunitqty(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFPriceQty()));
                    tempLink.setFsalesicentryLinkFallamountforold(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFALLAMOUNTFOR_D()));
                    tempLink.setFsalesicentryLinkFallamountfor(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFNoTaxAmountFor_D()));
                    detailLinkDtoList.add(tempLink);
                    tempDetail.setFsalesicentryLink(detailLinkDtoList);
                });
                invoiceDetailDtoList.add(tempDetail);
            });
            specialInvoiceDto.setFsalesicentry(invoiceDetailDtoList);
            vatPlainAndSpecialInvoiceDto.setKingDeeSalesVatSpecialInvoiceDto(specialInvoiceDto);
        } else {
            KingDeeSalesVatPlainInvoiceDto query = new KingDeeSalesVatPlainInvoiceDto();
            query.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            // 判断是否数据已存在
            boolean old = kingDeeBaseApi.isExist(query);
            if (old) {
                log.info("销售实物蓝票普通发票,金蝶数据已存在:{}", JSON.toJSONString(query));
                return null;
            }

            // 普票
            vatPlainAndSpecialInvoiceDto.setSpecial(false);
            // 第一层
            KingDeeSalesVatPlainInvoiceDto plainInvoiceDto = new KingDeeSalesVatPlainInvoiceDto();
            plainInvoiceDto.setFid("0");

            Date outInTime = batchRInvoiceDetailJOperateLogDtoMapper.findWarehouseOutByInvoiceId(batchInvoiceDto.getInvoiceId(), 2);
            plainInvoiceDto.setFdate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getAddTime()).compareTo(outInTime) < 0 ? outInTime : new Date(batchInvoiceDto.getAddTime())));
            plainInvoiceDto.setFinvoicedate(DateUtil.formatDateTime(new Date(batchInvoiceDto.getAddTime())));

            plainInvoiceDto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
            plainInvoiceDto.setFinvoiceno(batchInvoiceDto.getInvoiceNo());
            plainInvoiceDto.setFQzokFpdm(batchInvoiceDto.getInvoiceCode());
            plainInvoiceDto.setFcustomerid(batchInvoiceDto.getTraderCustomerId().toString());
            plainInvoiceDto.setFRedBlue(0);
            plainInvoiceDto.setFQzokPzgsywdh(batchInvoiceDto.getOrderNo());//VDERP-16094 票对接金蝶增加入参：归属业务单号
            plainInvoiceDto.setFBillTypeID("XSPTFP01_SYS");

            // 第二层
            List<KingDeeSalesVatPlainInvoiceDetail> invoiceDetailList = new ArrayList<>();
            physicalInvoiceDetailList.forEach(item -> {
                KingDeeSalesVatPlainInvoiceDetail tempDetail = new KingDeeSalesVatPlainInvoiceDetail();
                tempDetail.setFmaterialid(item.getSku());
                tempDetail.setFpriceqty(item.getNum());
                tempDetail.setFauxtaxprice(item.getTotalAmount().divide(item.getNum(), 6, RoundingMode.HALF_UP));
                tempDetail.setFtaxrate(batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
                tempDetail.setFQzokBddjhid(item.getInvoiceDetailId().toString());
                tempDetail.setFQzokYsddh(batchInvoiceDto.getOrderNo());
                tempDetail.setFQzokGsywdh(batchInvoiceDto.getOrderNo());
                tempDetail.setFQzokYwlx("销售");
                tempDetail.setFsrcbilltypeid("AR_receivable");

                // 第三层
                List<KingDeeSalesVatPlainInvoiceDetailLinkDto> detailLinkDtoList = new ArrayList<>();

                KingDeeReceiveQueryResultDto kingDeeReceiveQueryResultDto = CollUtil.getFirst(kingDeeSaleReceivable);
                List<BatchRInvoiceDetailJOperateLogDto> detailIdAndOperateType = batchRInvoiceDetailJOperateLogDtoMapper.findByInvoiceDetailIdAndOperateType(item.getInvoiceDetailId(), 2);
                detailIdAndOperateType.forEach(relation -> {
                    String bddjhid = item.getInvoiceDetailId().toString() + "-" + relation.getOperateLogId().toString();
                    KingDeeReceiveQueryResultDto kingDeeReceiveQueryResultDtoDetail = kingDeeSaleReceivable.stream()
                            .filter(q -> q.getF_QZOK_BDDJHID().equals(bddjhid)).findFirst().orElseGet(null);
                    if (Objects.isNull(kingDeeReceiveQueryResultDtoDetail)) {
                        log.error("无法查询标准应收单明细单{}", JSON.toJSONString(item));
                        throw new KingDeeException("无法查询标准应收单明细单");
                    }
                    KingDeeSalesVatPlainInvoiceDetailLinkDto tempLink = new KingDeeSalesVatPlainInvoiceDetailLinkDto();
                    tempLink.setFLinkId(0);
                    tempLink.setFsalesicentryLinkFflowid("");
                    tempLink.setFsalesicentryLinkFflowlineid(0);
                    tempLink.setFsalesicentryLinkFruleid("IV_ReceivableToSalesIC");
                    tempLink.setFsalesicentryLinkFstableid("0");
                    tempLink.setFsalesicentryLinkFstablename("t_AR_receivableEntry");
                    tempLink.setFsalesicentryLinkFsbillid(kingDeeReceiveQueryResultDto.getFID());
                    tempLink.setFsalesicentryLinkFsid(kingDeeReceiveQueryResultDtoDetail.getFEntityDetail_FEntryId());
                    tempLink.setFsalesicentryLinkFbasicunitqtyold(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFPriceQty()));
                    tempLink.setFsalesicentryLinkFbasicunitqty(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFPriceQty()));
                    tempLink.setFsalesicentryLinkFallamountforold(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFALLAMOUNTFOR_D()));
                    tempLink.setFsalesicentryLinkFallamountfor(new BigDecimal(kingDeeReceiveQueryResultDtoDetail.getFNoTaxAmountFor_D()));
                    detailLinkDtoList.add(tempLink);
                    tempDetail.setFsalesicentryLink(detailLinkDtoList);
                });
                invoiceDetailList.add(tempDetail);
            });
            plainInvoiceDto.setFsalesicentry(invoiceDetailList);
            vatPlainAndSpecialInvoiceDto.setKingDeeSalesVatPlainInvoiceDto(plainInvoiceDto);
        }
        log.info("构造销售单实物商品蓝票信息成功: {}", JSON.toJSONString(vatPlainAndSpecialInvoiceDto));
        return vatPlainAndSpecialInvoiceDto;

    }
}