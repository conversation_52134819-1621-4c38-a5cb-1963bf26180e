package com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderChangeSales.afterProcessor;

import com.vedeng.erp.buyorder.bizProcessor.processor.OrderAfterProcessor;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.afterProcessor.OverBuyorderAfterSaleReturnOrderAfterProcessor;

/**
 * <AUTHOR>
 */
public abstract class OverBuyorderAfterSaleChangeOrderAfterProcessor implements OrderAfterProcessor {
}
