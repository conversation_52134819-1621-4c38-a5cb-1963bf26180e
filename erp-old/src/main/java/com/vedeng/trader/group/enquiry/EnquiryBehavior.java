package com.vedeng.trader.group.enquiry;

import com.vedeng.trader.group.BaseBehavior;

import java.util.BitSet;

/**
 * <b>Description:</b>最近有询价<br>
 * @Note
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/5/25
 */
public class EnquiryBehavior extends EnquiryBaseBehavior {
    @Override
    public BitSet calculate() {
        BitSet enquirySet=getRencentEnquiryData();
        BitSet result=getBaseTraderSet();
        result.and(enquirySet);
        return result;
    }
}
