package com.vedeng.trader.model;

import java.io.Serializable;

/**
 * T_R_TRADER_GROUP_J_TRADER
 * <AUTHOR>
public class RTraderGroupJTrader implements Serializable {
    private Long rTraderGroupJTrader;

    /**
     * 客户分群id
     */
    private Integer traderGroupId;

    /**
     * 客户分群名称
     */
    private String traderGroupName;

    /**
     * 客户id
     */
    private Integer traderId;

    private static final long serialVersionUID = 1L;

    public Long getrTraderGroupJTrader() {
        return rTraderGroupJTrader;
    }

    public void setrTraderGroupJTrader(Long rTraderGroupJTrader) {
        this.rTraderGroupJTrader = rTraderGroupJTrader;
    }

    public Integer getTraderGroupId() {
        return traderGroupId;
    }

    public void setTraderGroupId(Integer traderGroupId) {
        this.traderGroupId = traderGroupId;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public String getTraderGroupName() {
        return traderGroupName;
    }

    public void setTraderGroupName(String traderGroupName) {
        this.traderGroupName = traderGroupName;
    }
}