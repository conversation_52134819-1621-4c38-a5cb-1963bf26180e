<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>地址信息列表</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="addressInformationList" datasource="erp-datasourcetarget" fixednumber="" fixednumberright="1" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead">
      <style>
          li{
              position: relative;
              float: left;
              padding: 10px 0;
              display: list-item;
          }
          li>a{    color: #333;
              border-right: 1px solid #ddd;
              padding: 0 10px;}
          ul{    overflow: hidden;
              list-style: none outside none;}
          .rowButtons{
              min-width: 160px;
          }
      </style>
      <script>
          $(function(){
              $("#list-tab").removeClass("layui-tab-title");
              $("li[item_name=addressInformation5]").addClass("layui-text");

          })
      </script>
  </div>
  <div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
            <li item_name="traderContract11"><a href="/trader/customer/new/portrait.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract11">客户360</a></li>
            <li item_name="traderContract1"><a href="/trader/customer/baseinfo.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract1">基本信息</a></li>
            <li item_name="traderContract12"><a href="/trader/customer/new/distribution/link.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract12">经销链路</a></li>
            <li item_name="traderContract2"><a href="/trader/customer/new/customeDetail.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract2">客户行为</a></li>
            <li item_name="traderContract3"><a href="/trader/customer/getFinanceAndAptitude.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract3">财务与资质信息</a></li>
            <li item_name="traderContract4"><a href="/ezadmin/list/list-traderContractList?IS_ENABLE=1&TRADER_ID=${TRADER_ID}&amp;TRADER_CUSTOMERID=${TRADER_CUSTOMERID}&amp;perPageInt=50" item_name="traderContract4">联系人</a></li>
            <li item_name="addressInformation5"><a href="/ezadmin/list/list-addressInformationList?TRADER_ID=${TRADER_ID}&amp;TRADER_CUSTOMERID=${TRADER_CUSTOMERID}" item_name="addressInformation5">联系地址</a></li>
            <li item_name="customerAddAddress6"><a href="/ezadmin/list/list-customerAddAddressList?TRADER_ID=${TRADER_ID}&amp;TRADER_CUSTOMERID=${TRADER_CUSTOMERID}" item_name="customerAddAddress6">客户添加的地址</a></li>
            <li item_name="customerAddAddress8"><a href="/trader/customer/businesslist.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="customerAddAddress8">交易记录</a></li>
            <li item_name="customerAddAddress9"><a href="/trader/customer/communicaterecord.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="customerAddAddress9">沟通记录</a></li>
            <li item_name="customerAddAddress10"><a href="/trader/relation/info.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="customerAddAddress10">关联客户</a></li>
            <li item_name="traderContract12"><a href="/trader/customer/new/share.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract12">分享客户</a></li>
        </ul>
    </div>
    <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">trader_id</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="TRADER_ID" placeholder style alias="" jdbctype data datatype oper validate_rules validate_messages> </object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">地区</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="REGION" placeholder style="" alias="CONCAT_WS(' ', R3.REGION_NAME, R2.REGION_NAME, R1.REGION_NAME)" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages="">
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">详细地址</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="ADDRESS" placeholder style="" alias="A" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages="">
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">traderCustomerId</label>
       <div class="layui-input-inline">
           <hidden class=" layui-input list-search-item " type="nowhere" name="TRADER_CUSTOMERID" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper="" ></hidden>
       </div>
    </div>
   </form>
   <hr class="layui-border-blue">
      <div class="btn-group   bd-highlight" id="tableButton">
          <button item_name="新增" name="新增" url="/trader/customer/toAddAddressPage.do?traderId=${TRADER_ID}" opentype="MODEL" windowname="新增地址" area="70%,80%" style type="table">新增</button>
      </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
      <th item_name="ID" name="ID" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="display: none" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">ID</th>
      <th item_name="TRADER_ID" name="TRADER_ID" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="display: none" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">TRADER_ID</th>
      <th item_name="AREA_ID" name="AREA_ID" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="display: none" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">AREA_ID</th>
      <th item_name="AREA_IDS" name="AREA_IDS" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="display: none" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">AREA_IDS</th>
      <th item_name="IS_TOP" name="IS_TOP" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="display: none" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">IS_TOP</th>
      <th item_name="REGION" name="REGION" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">地区</th>
      <th item_name="ADDRESS" name="ADDRESS" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">详细地址</th>
      <th item_name="ZIP_CODE" name="ZIP_CODE" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">邮编</th>
      <th item_name="IS_DEFAULT" name="IS_DEFAULT" body="td-select" order="" url="" opentype="" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;<span style='color:red'>默认</span>&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">默认地址</th>
      <th item_name="IS_TOP" name="IS_TOP" body="td-select" order="" url="" opentype="" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;<SPAN STYLE='COLOR:red'>置顶</SPAN>&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">是否置顶</th>
      <th item_name="IS_ENABLE" name="IS_ENABLE" body="td-select" order="" url="" opentype="" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;<span style='color:red'>禁用</span>&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;<SPAN STYLE='COLOR:#5db75d'>启用</SPAN>&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">启用或禁用</th>
      <th item_name="COMMENTS" name="COMMENTS" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">备注</th>
      <th type="rowbutton" id="rowbutton" style="min-width: 150px">
          <button item_name="a" name="a" type="group" opentype="MODEL" url="/trader/customer/toEditAddressPage.do?traderAddressId=${ID}"
                  windowname="编辑" area="800px,500px">编辑
          </button>
          <button item_name="c" name="c" url="/ezadmin/form/doSubmit-addressInformationForm?ID=${ID}&amp;TYPE=2&amp;TRADER_ID=${TRADER_ID}&amp;TRADER_ADDRESS_ID=${ID}&amp;AREA_ID=${AREA_ID}&amp;AREA_IDS=${AREA_IDS}" opentype="CONFIRM_AJAX" windowname="" style type="group">设为默认</button>
          <button item_name="b" name="b" url="/ezadmin/form/doSubmit-addressInformationForm?ID=${ID}&amp;TYPE=3&amp;TRADER_ADDRESS_ID=${ID}&amp;IS_TOP=${IS_TOP}" opentype="CONFIRM_AJAX" windowname="" style type="group">置顶或取消</button>
          <button item_name="d" name="d" url="/ezadmin/form/doSubmit-addressInformationForm?ID=${ID}&amp;TYPE=1&amp;TRADER_ADDRESS_ID=${ID}&amp;IS_ENABLE=${IS_ENABLE}" opentype="CONFIRM_AJAX" windowname="" style type="group">启用或禁用</button></th>
     </tr>
    </thead>
    <tbody>
     <tr> 
      <td> express:</td> 
      <td colspan="100"> <pre id="express" class="layui-code" orderby="ORDER BY 	IS_TOP DESC, 	IS_ENABLE DESC, 	MOD_TIME DESC" groupby="">StringBuilder sql=new StringBuilder();
TRADER_ID=$$("TRADER_ID");
sql.append("
    SELECT
        A.TRADER_ADDRESS_ID ID,
        A.TRADER_ID,
	    CONCAT_WS(' ', R3.REGION_NAME, R2.REGION_NAME, R1.REGION_NAME) REGION,
        A.ADDRESS,
        A.ZIP_CODE,
        A.IS_DEFAULT,
        A.COMMENTS,
        A.IS_ENABLE,
        A.AREA_ID,
        A.AREA_IDS,
        A.IS_TOP
    FROM
        T_TRADER_ADDRESS A
    LEFT JOIN
        T_REGION R1
            ON R1.REGION_ID = A.AREA_ID
            AND R1.REGION_ID &gt; 100000
    LEFT JOIN
        T_REGION R2
            ON R1.PARENT_ID = R2.REGION_ID
            AND R2.REGION_ID &gt; 100000
    LEFT JOIN
        T_REGION R3
            ON R2.PARENT_ID = R3.REGION_ID
            AND R3.REGION_ID &gt; 100000
    WHERE
        TRADER_TYPE = 1
   ");
if(isBlank("TRADER_ID")){
              sql.append(" AND 1=2 " );
              }
return search(sql);</pre> </td>
     </tr> 
     <tr> 
      <td> count:</td> 
      <td colspan="100"> <pre id="count" class="layui-code"></pre> </td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot">
      <script>
      $(function(){
      $("#list-tab").removeClass("layui-tab-title");
      $("li[item_name=addressInformation5]").addClass("layui-text");

      })
      </script>
  </div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>