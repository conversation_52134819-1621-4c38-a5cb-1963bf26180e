package com.vedeng.goodsSearch.service;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.pricecenter.dto.PageResultDto;
import com.pricecenter.dto.SkuPriceChangeApplyDto;
import com.pricecenter.dto.SkuPriceChangeApplyPageQueryDto;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.department.dao.DepartmentsHospitalGenerateMapper;
import com.vedeng.department.model.DepartmentsHospitalGenerate;
import com.vedeng.goods.dao.*;
import com.vedeng.goods.domain.dto.GoodsSearchBaseInfoDto;
import com.vedeng.goods.feign.goods.GoodsSearchApiService;
import com.vedeng.goods.mapper.GoodsSearchMapper;
import com.vedeng.goods.model.BaseAttributeGenerate;
import com.vedeng.goods.model.BaseAttributeValue;
import com.vedeng.goods.model.BrandGenerate;
import com.vedeng.goods.model.dto.SyncSkuInfo2EsDto;
import com.vedeng.goods.model.entity.GoodsLevelDo;
import com.vedeng.goods.model.entity.GoodsPositionDo;
import com.vedeng.goods.model.vo.BaseCategoryVo;
import com.vedeng.goodsSearch.constant.BrandNatureEnum;
import com.vedeng.goodsSearch.constant.GoodsOptionEnum;
import com.vedeng.goodsSearch.constant.GoodsSortColumnMappingEnum;
import com.vedeng.goodsSearch.model.dto.*;
import com.vedeng.price.api.price.dto.price.SkuPriceInfoBatchQueryDto;
import com.vedeng.price.api.price.dto.price.SkuPriceInfoDetailResponseDto;
import com.vedeng.search.api.dto.requestparam.AggParam;
import com.vedeng.search.api.dto.requestparam.ComplexQueryParam;
import com.vedeng.search.api.dto.requestparam.PriceRange;
import com.vedeng.search.api.dto.requestparam.SortCustomParam;
import com.vedeng.search.api.dto.responseparam.AggResult;
import com.vedeng.search.api.dto.responseparam.SearchResult;
import com.vedeng.search.api.dto.responseparam.SearchResultParam;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.SysOptionDefinition;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品搜索服务层
 *
 * <AUTHOR>
 */
@Service("goodsSearchService")
public class GoodsSearchServiceImpl extends BaseServiceimpl implements GoodsSearchService {
    public Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 价格单位数量
     */
    private static final Integer PRICE_UNIT_NUM = 10000;

    @Autowired
    private GoodsSearchApiService goodsSearchApiService;

    @Resource
    private GoodsSearchMapper goodsSearchMapper;

    @Resource
    private BaseCategoryMapper baseCategoryMapper;

    @Resource
    private BrandGenerateMapper brandGenerateMapper;

    @Resource
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;

    @Resource
    private DepartmentsHospitalGenerateMapper departmentsHospitalGenerateMapper;

    @Resource
    private GoodsLevelMapper goodsLevelMapper;

    @Resource
    private GoodsPositionMapper goodsPositionMapper;

    @Resource
    private BaseAttributeGenerateMapper baseAttributeGenerateMapper;

    @Resource
    private BaseAttributeValueMapper baseAttributeValueMapper;

    private static final String SUCCESS_CODE = "success";

    private static final String BASE_PRICEMAINTAIN_PAGE = "sku_price_change_apply/list/page";

    @Override
    public ResultInfo goodsSearch(GoodsSearchReqDto goodsSearchReqDto) {
        logger.info("goodsSearchStart goodsSearchReqDto:{}", JSON.toJSONString(goodsSearchReqDto));

        if (StringUtil.isBlank(goodsSearchReqDto.getKeywords()) && StringUtil.isBlank(goodsSearchReqDto.getSkuSceneCategoryId())) {
            return defaultGoodsSearchInfo(goodsSearchReqDto);
        }

        ComplexQueryParam complexQueryParam = dealRequestParam(goodsSearchReqDto);

        logger.info("商品搜索请求es complexQueryParam{}", JSON.toJSONString(complexQueryParam));
        RestfulResult<SearchResult> searchResultRestfulResult = goodsSearchApiService.search(complexQueryParam);
        logger.info("goodsSearchResponse searchResultRestfulResult:{}", JSON.toJSONString(searchResultRestfulResult));
        if (searchResultRestfulResult == null || !searchResultRestfulResult.isSuccess()) {
            return ResultInfo.error("ES返回搜索结果信息错误。");
        }

        //搜索返回数据信息
        SearchResult searchResult = searchResultRestfulResult.getData();
        List<SearchResultParam> searchResultParams = searchResult.getResult();

        ResultInfo resultInfo = ResultInfo.success();

        //1.分页page信息
        Page page = new Page(goodsSearchReqDto.getPageNo(), goodsSearchReqDto.getPageSize());
        page.setTotalRecord(searchResult.getTotalCount());
        page.setTotalPage((searchResult.getTotalCount() - 1) / goodsSearchReqDto.getPageSize() + 1);
        resultInfo.setPage(page);

        //2.返回数据信息
        GoodsSearchResDto goodsSearchResInfo = getGoodsSearchResInfo(searchResult);

        //3.返回SKU信息
        List<GoodsSearchInfoResDto> goodsSearchInfoList = getGoodsBaseInfo(searchResultParams);

        resultInfo.setData(goodsSearchResInfo);
        resultInfo.setListData(goodsSearchInfoList);
        return resultInfo;
    }

    /**
     * 商品搜索默认结果集
     *
     * @param goodsSearchReqDto
     * @return
     */
    private ResultInfo defaultGoodsSearchInfo(GoodsSearchReqDto goodsSearchReqDto) {
        ResultInfo resultInfo = ResultInfo.success(new GoodsSearchResDto());

        Map<String, Object> paraMap = new HashMap<>(16);
        Page page = new Page(goodsSearchReqDto.getPageNo(), goodsSearchReqDto.getPageSize());
        paraMap.put("page", page);

        List<GoodsSearchBaseInfoDto> defaultGoodsBaseInfoList;
        ArrayList<GoodsSearchInfoResDto> defaultGoodsInfo = new ArrayList<>();

        /**
         * 去掉默认状态下排序功能提升性能
         */
        if (StringUtil.isBlank(goodsSearchReqDto.getSortColoum())) {
            List<Integer> skuIds = goodsSearchMapper.defaultSkuIdsSearchListPage(paraMap);
            if (CollectionUtils.isEmpty(skuIds)) {
                return resultInfo;
            }
            defaultGoodsBaseInfoList = goodsSearchMapper.getGoodsSearchBaseInfoBySkuIds(skuIds);
        } else {
            dealCustomSortColumn(goodsSearchReqDto, paraMap);
            defaultGoodsBaseInfoList = goodsSearchMapper.defaultGoodsSearchBaseInfoListPage(paraMap);
        }

        if (CollectionUtils.isEmpty(defaultGoodsBaseInfoList)) {
            return resultInfo;
        }

        // 调用价格中心查询价格
        List<Integer> skuIds = defaultGoodsBaseInfoList.stream().map(GoodsSearchBaseInfoDto::getSkuId).collect(Collectors.toList());
        Map<Long, SkuPriceInfoDetailResponseDto> priceBySkuIdList = this.getPriceBySkuIdList(skuIds);
        List<SkuPriceChangeApplyDto> findPriceChangeApply = findPriceChangeApply(skuIds);
        Map<Integer,SkuPriceChangeApplyDto> findPriceChangeApplyMap =
                findPriceChangeApply.stream().collect(Collectors.toMap(item -> Long.valueOf(item.getSkuId()).intValue(), item -> item));
        defaultGoodsBaseInfoList.forEach(defaultGoodsBaseInfo -> {
            GoodsSearchInfoResDto goodsSearchInfoResDto = new GoodsSearchInfoResDto();
            BeanUtils.copyProperties(defaultGoodsBaseInfo, goodsSearchInfoResDto);

            SkuPriceInfoDetailResponseDto res = priceBySkuIdList.get(defaultGoodsBaseInfo.getSkuId().longValue());
            if (Objects.nonNull(res)) {
                goodsSearchInfoResDto.setElectronicCommercePrice(res.getElectronicCommercePrice());
                goodsSearchInfoResDto.setResearchTerminalPrice(res.getResearchTerminalPrice());
            }
            if(CollectionUtils.isNotEmpty(findPriceChangeApply)){
                //以SkuPriceChangeApplyDto的skuId为key，SkuPriceChangeApplyDto为value，转换成Map,skuId同时转为Integer类型


                SkuPriceChangeApplyDto skuPriceChangeApplyDto = findPriceChangeApplyMap.get(defaultGoodsBaseInfo.getSkuId());
                if(skuPriceChangeApplyDto!=null){
                    if (skuPriceChangeApplyDto !=null && skuPriceChangeApplyDto.getAuditPass()!= null
                            && skuPriceChangeApplyDto.getAuditPass()== 1 &&
                            skuPriceChangeApplyDto !=null &&  skuPriceChangeApplyDto.getDisabled()!= null
                            &&  skuPriceChangeApplyDto.getDisabled()== 0
                    ){
                        goodsSearchInfoResDto.setAvgPriceOfLastYear(new BigDecimal(-1));
                    }
                    goodsSearchInfoResDto.setSaleContainsFee(skuPriceChangeApplyDto.getSaleContainsFee());
                    goodsSearchInfoResDto.setPurchseContainsFee(skuPriceChangeApplyDto.getPurchaseContainsFee());
                }
            }

            defaultGoodsInfo.add(goodsSearchInfoResDto);
        });


        resultInfo.setPage(page);
        resultInfo.setListData(defaultGoodsInfo);
        return resultInfo;
    }

    /**
     * 处理初始化自定义排序字段
     *
     * @param goodsSearchReqDto
     * @param paraMap
     */
    private void dealCustomSortColumn(GoodsSearchReqDto goodsSearchReqDto, Map<String, Object> paraMap) {
        if (StringUtil.isBlank(goodsSearchReqDto.getSortColoum())) {
            return;
        }
        StringBuilder sortColumnStringBuffer = new StringBuilder();

        List<String> sortColumns = Arrays.stream(goodsSearchReqDto.getSortColoum().split(",")).collect(Collectors.toList());
        List<Integer> sortAsc = Arrays.stream(goodsSearchReqDto.getSortAsc().split(","))
                .collect(Collectors.toList()).stream().mapToInt(Integer::parseInt)
                .boxed().collect(Collectors.toList());

        for (int index = 0; index < sortColumns.size(); index++) {
            String sortColumn = sortColumns.get(index);
            GoodsSortColumnMappingEnum instance = GoodsSortColumnMappingEnum.getInstance(ErpConst.ONE, sortColumn);
            if (instance == null) {
                logger.info("sortColumnWarn:{}", sortColumn);
                continue;
            }
            if (index != 0) {
                sortColumnStringBuffer.append(",");
            }
            sortColumnStringBuffer.append(instance.getTableColumn());
            sortColumnStringBuffer.append(" ");
            sortColumnStringBuffer.append(ErpConst.ZERO.equals(sortAsc.get(index)) ? "ASC" : "DESC");
        }
        if (StringUtil.isBlank(sortColumnStringBuffer.toString())) {
            return;
        }
        paraMap.put("sortParam", sortColumnStringBuffer.toString());
    }


    /**
     * 处理ES请求参数信息
     *
     * @param goodsSearchReqDto
     * @return
     */
    private ComplexQueryParam dealRequestParam(GoodsSearchReqDto goodsSearchReqDto) {
        ComplexQueryParam complexQueryParam = new ComplexQueryParam();
        complexQueryParam.setKeyword(goodsSearchReqDto.getKeywords());
        complexQueryParam.setIsVerifiedPrice(goodsSearchReqDto.getHasVerifiedPrice());
        complexQueryParam.setHasStock(goodsSearchReqDto.getHasStock());
        complexQueryParam.setIsAuthorized(goodsSearchReqDto.getHasAuthorized());
        complexQueryParam.setIsSupportInstallation(goodsSearchReqDto.getIsSupportInstallation());
        complexQueryParam.setFilterErpCategoryId(goodsSearchReqDto.getCategoryId());
        complexQueryParam.setSpuType(goodsSearchReqDto.getSpuType());
        if (goodsSearchReqDto.getMinSalePrice() != null || goodsSearchReqDto.getMaxSalePrice() != null) {
            PriceRange filterPrice = new PriceRange();
            if (goodsSearchReqDto.getMinSalePrice() != null) {
                filterPrice.setMin(goodsSearchReqDto.getMinSalePrice().doubleValue());
            }
            if (goodsSearchReqDto.getMaxSalePrice() != null) {
                filterPrice.setMax(goodsSearchReqDto.getMaxSalePrice().doubleValue());
            }
            complexQueryParam.setFilterPrice(filterPrice);
        }
        complexQueryParam.setFilterSource(goodsSearchReqDto.getBrandNatureId());

        if (StringUtil.isNotBlank(goodsSearchReqDto.getAttributeId())) {
            complexQueryParam.setAttrNameIds(Arrays.stream(goodsSearchReqDto.getAttributeId().split(","))
                    .mapToInt(Integer::parseInt).boxed().toArray(Integer[]::new));
        }
        if (StringUtil.isNotBlank(goodsSearchReqDto.getAttributeValueId())) {
            complexQueryParam.setAttrValueIdList(Arrays.stream(goodsSearchReqDto.getAttributeValueId().split(","))
                    .mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()));
        }
        if (StringUtil.isNotBlank(goodsSearchReqDto.getInstitutionId())) {
            complexQueryParam.setMechanismIdList(Arrays.stream(goodsSearchReqDto.getInstitutionId().split(","))
                    .mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()));
        }
        if (StringUtil.isNotBlank(goodsSearchReqDto.getDepartmentId())) {
            complexQueryParam.setDeptIdList(Arrays.stream(goodsSearchReqDto.getDepartmentId().split(","))
                    .mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()));
        }
        if (StringUtil.isNotBlank(goodsSearchReqDto.getGoodsLevelId())) {
            complexQueryParam.setRuleLevelList(Arrays.stream(goodsSearchReqDto.getGoodsLevelId().split(","))
                    .mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()));
        }
        if (StringUtil.isNotBlank(goodsSearchReqDto.getGoodsPositionId())) {
            complexQueryParam.setRuleGearList(Arrays.stream(goodsSearchReqDto.getGoodsPositionId().split(","))
                    .mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()));
        }
        if (StringUtil.isNotBlank(goodsSearchReqDto.getSkuSceneCategoryId())) {
            complexQueryParam.setSkuSceneCategoryIdList(Arrays.stream(goodsSearchReqDto.getSkuSceneCategoryId().split(","))
                    .mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()));
        }
        complexQueryParam.setIncludeFieldList(Collections.singletonList("skuId"));
        complexQueryParam.setPlatformId(ErpConst.ZERO);
        complexQueryParam.setAccessCode(ErpConst.ACCESS_CODE);
        complexQueryParam.setCurrentPage(goodsSearchReqDto.getPageNo());
        complexQueryParam.setPageSize(goodsSearchReqDto.getPageSize());
        complexQueryParam.setNeedCalAttrCatId(false);
        complexQueryParam.setNeedInputProcess(true);
        complexQueryParam.setNeedCorrectMistakes(true);
        complexQueryParam.setCategoryPrediction(true);

        ArrayList<AggParam> aggParams = new ArrayList<>();
        Arrays.stream(GoodsOptionEnum.values()).collect(Collectors.toList()).stream().filter(item -> !GoodsOptionEnum.DEFAULT_ENUM.equals(item))
                .map(GoodsOptionEnum::getDesc).collect(Collectors.toList()).forEach(aggStr -> aggParams.add(new AggParam(aggStr)));
        complexQueryParam.setAggFieldList(aggParams);

        if (StringUtil.isNotBlank(goodsSearchReqDto.getBrandId())) {
            complexQueryParam.setBrandIdList(Arrays.stream(goodsSearchReqDto.getBrandId().split(","))
                    .mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()));
        }


        dealEsSortParam(goodsSearchReqDto, complexQueryParam);
        complexQueryParam.setSortType(CollectionUtils.isEmpty(complexQueryParam.getSortCustomList()) ? ErpConst.FIVE : ErpConst.THREE);

        //查询是否销售价含运费,(销售价是否含运费 ,0 未维护 1 含运费 2 不含运费)
        if(Objects.nonNull(goodsSearchReqDto.getSaleNotContainsFee()) && ErpConst.ONE == goodsSearchReqDto.getSaleNotContainsFee()) {
        	complexQueryParam.setSaleContainsFee(ErpConst.TWO);
        }

        //查询是否成本价含运费,(成本价是否含运费 ,0 未维护 1 含运费 2 不含运费)
        if(Objects.nonNull(goodsSearchReqDto.getPurchaseNotContainsFee()) && ErpConst.ONE == goodsSearchReqDto.getPurchaseNotContainsFee()) {
        	complexQueryParam.setSaleContainsFee(ErpConst.TWO);
        }

        return complexQueryParam;
    }

    /**
     * 处理ES排序信息
     *
     * @param goodsSearchReqDto
     * @param complexQueryParam
     */
    private void dealEsSortParam(GoodsSearchReqDto goodsSearchReqDto, ComplexQueryParam complexQueryParam) {
        if (StringUtil.isBlank(goodsSearchReqDto.getSortColoum())) {
            return;
        }
        List<String> sortColumnList = Arrays.stream(goodsSearchReqDto.getSortColoum().split(",")).collect(Collectors.toList());
        List<Integer> sortAscList = StringUtil.isNotBlank(goodsSearchReqDto.getSortAsc()) ?
                Arrays.stream(goodsSearchReqDto.getSortAsc().split(","))
                        .collect(Collectors.toList()).stream().mapToInt(Integer::parseInt)
                        .boxed().collect(Collectors.toList()) : null;

        ArrayList<SortCustomParam.BaseSortCustom> sortCustomParams = new ArrayList<>();

        for (int index = 0; index < sortColumnList.size(); index++) {
            SortCustomParam.BaseSortCustom baseSortCustom = new SortCustomParam.BaseSortCustom();
            String sortColumn = sortColumnList.get(index);
            if (StringUtil.isBlank(sortColumn)) {
                continue;
            }
            GoodsSortColumnMappingEnum instance = GoodsSortColumnMappingEnum.getInstance(ErpConst.ONE, sortColumn);
            if (instance == null || StringUtil.isBlank(instance.getEsColumn())) {
                continue;
            }
            baseSortCustom.setSortColoum(instance.getEsColumn());
            baseSortCustom.setSortAsc(ErpConst.ZERO.equals(sortAscList.get(index)));
            baseSortCustom.setScript(CollectionUtils.isEmpty(sortAscList));
            baseSortCustom.setOrder(index + 1);
            sortCustomParams.add(baseSortCustom);
        }

        if (CollectionUtils.isEmpty(sortCustomParams)) {
            return;
        }
        complexQueryParam.setSortCustomList(sortCustomParams);

    }

    /**
     * 处理商品搜索结果信息
     *
     * @param searchResult
     */
    private GoodsSearchResDto getGoodsSearchResInfo(SearchResult searchResult) {
        GoodsSearchResDto goodsSearchResDto = new GoodsSearchResDto();

        Map<String, List<AggResult>> aggMap = searchResult.getAggMap();
        if (MapUtils.isEmpty(aggMap)) {
            return goodsSearchResDto;
        }
        aggMap.forEach((key, values) -> {
            if (CollectionUtils.isEmpty(values) || values.size() < 2) {
                return;
            }
            switch (GoodsOptionEnum.getInstance(key)) {
                case PRICE: {
                    ArrayList<PriceSectionDto> priceSectionList = new ArrayList<>();
                    List<BigDecimal> priceList = values.stream().map(AggResult::getFieldName)
                            .filter(Objects::nonNull).map(BigDecimal::new).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(priceList)) {
                        break;
                    }

                    if (CollectionUtils.isNotEmpty(priceList.stream().filter(item -> new BigDecimal(-1).compareTo(item) == 0).collect(Collectors.toList()))) {
                        PriceSectionDto unCheckPriceSection = new PriceSectionDto();
                        unCheckPriceSection.setMinPrice(new BigDecimal(-1));
                        unCheckPriceSection.setMaxPrice(new BigDecimal(-1));
                        priceSectionList.add(unCheckPriceSection);
                    }


                    priceList = priceList.stream().filter(i -> i.compareTo(BigDecimal.ZERO) > -1).collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(priceList)) {
                        break;
                    }

                    if (CollectionUtils.isNotEmpty(priceList) && priceList.size() == 1) {
                        PriceSectionDto onlyOnePriceSection = new PriceSectionDto();
                        onlyOnePriceSection.setMinPrice(priceList.get(0).setScale(BigDecimal.ROUND_CEILING, RoundingMode.HALF_UP));
                        onlyOnePriceSection.setMaxPrice(priceList.get(0).setScale(BigDecimal.ROUND_CEILING, RoundingMode.HALF_UP));
                        priceSectionList.add(onlyOnePriceSection);
                        goodsSearchResDto.setRegionPriceList(priceSectionList);
                        break;
                    }


                    BigDecimal maxPrice = priceList.stream().max(Comparator.comparing(i -> i)).orElse(BigDecimal.ZERO).setScale(BigDecimal.ROUND_CEILING, RoundingMode.HALF_UP);
                    BigDecimal minPrice = priceList.stream().min(Comparator.comparing(i -> i)).orElse(BigDecimal.ZERO).setScale(BigDecimal.ROUND_CEILING, RoundingMode.HALF_UP);
                    BigDecimal averagePrice = (maxPrice.subtract(minPrice)).divide(new BigDecimal(3), BigDecimal.ROUND_CEILING, RoundingMode.HALF_UP);
                    BigDecimal midPrice = minPrice.add(averagePrice).setScale(BigDecimal.ROUND_CEILING, RoundingMode.HALF_UP);
                    BigDecimal secondMaxPrice = averagePrice.multiply(new BigDecimal(2)).add(minPrice).setScale(BigDecimal.ROUND_CEILING, RoundingMode.HALF_UP);

                    if (averagePrice.compareTo(new BigDecimal(10)) < 1){
                        PriceSectionDto onlyOnePriceSection = new PriceSectionDto();
                        onlyOnePriceSection.setMinPrice(minPrice);
                        onlyOnePriceSection.setMaxPrice(maxPrice);
                        priceSectionList.add(onlyOnePriceSection);
                        goodsSearchResDto.setRegionPriceList(priceSectionList);
                        break;
                    }


                    PriceSectionDto minPriceSection = new PriceSectionDto();
                    minPriceSection.setMinPrice(minPrice);
                    minPriceSection.setMaxPrice(midPrice);
                    priceSectionList.add(minPriceSection);

                    PriceSectionDto secondPriceSection = new PriceSectionDto();
                    secondPriceSection.setMinPrice(midPrice);
                    secondPriceSection.setMaxPrice(secondMaxPrice);
                    priceSectionList.add(secondPriceSection);

                    PriceSectionDto maxPriceSection = new PriceSectionDto();
                    maxPriceSection.setMinPrice(secondMaxPrice);
                    maxPriceSection.setMaxPrice(maxPrice);
                    priceSectionList.add(maxPriceSection);
                    goodsSearchResDto.setRegionPriceList(priceSectionList);
                    break;
                }

                case ERP_CATEGORY: {
                    List<Integer> baseCategoryIds = values.stream().map(AggResult::getFieldName).mapToInt(Integer::parseInt).distinct()
                            .filter(Objects::nonNull).boxed().collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(baseCategoryIds)) {
                        break;
                    }

                    List<BaseCategoryVo> baseCategoryList = baseCategoryMapper.getBaseCategoryListByIds(baseCategoryIds);
                    if (CollectionUtils.isEmpty(baseCategoryList)) {
                        break;
                    }

                    List<CategoryDto> categoryList = CollectionUtils.isNotEmpty(goodsSearchResDto.getCategoryList()) ?
                            goodsSearchResDto.getCategoryList() : new ArrayList<>();

                    baseCategoryList.forEach(baseCategoryInfo -> {
                        CategoryDto categoryDto = new CategoryDto();
                        categoryDto.setCategoryId(baseCategoryInfo.getBaseCategoryId());
                        categoryDto.setCategoryName(baseCategoryInfo.getBaseCategoryName());
                        categoryList.add(categoryDto);
                    });
                    goodsSearchResDto.setCategoryList(categoryList);
                    break;
                }

                case SKU_ATTR: {
                    List<Integer> skuAttributeValueIds = values.stream().map(AggResult::getFieldName).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(skuAttributeValueIds)) {
                        break;
                    }
                    List<BaseAttributeValue> baseAttributeValues = baseAttributeValueMapper.listAttributeValueByIds(skuAttributeValueIds);
                    if (CollectionUtils.isEmpty(baseAttributeValues)) {
                        break;
                    }
                    HashMap<Integer, List<AttributeValueDto>> attributeIdValuesMap = new HashMap<>(16);
                    baseAttributeValues.forEach(baseAttributeValue -> {
                        Integer baseAttributeId = baseAttributeValue.getBaseAttributeId();
                        AttributeValueDto attributeValueDto = new AttributeValueDto();
                        attributeValueDto.setAttributeValueId(baseAttributeValue.getBaseAttributeValueId());
                        attributeValueDto.setAttributeValueName(baseAttributeValue.getAttrValue());
                        if (attributeIdValuesMap.containsKey(baseAttributeId)) {
                            attributeIdValuesMap.get(baseAttributeId).add(attributeValueDto);
                        } else {
                            ArrayList<AttributeValueDto> attributeValueDtoList = new ArrayList<>();
                            attributeValueDtoList.add(attributeValueDto);
                            attributeIdValuesMap.put(baseAttributeId, attributeValueDtoList);
                        }
                    });
                    if (MapUtils.isEmpty(attributeIdValuesMap)) {
                        break;
                    }

                    Iterator<Integer> iterator = attributeIdValuesMap.keySet().iterator();
                    while (iterator.hasNext()) {
                        Integer attributeId = iterator.next();
                        List<AttributeValueDto> attributeValues = attributeIdValuesMap.get(attributeId);
                        if (CollectionUtils.isEmpty(attributeValues) || attributeValues.size() < 2) {
                            iterator.remove();
                            attributeIdValuesMap.remove(attributeId);
                        }
                    }

                    if (MapUtils.isEmpty(attributeIdValuesMap)) {
                        break;
                    }

                    List<AttributeDto> attributeList = CollectionUtils.isNotEmpty(goodsSearchResDto.getAttributeList()) ? goodsSearchResDto.getAttributeList() : new ArrayList<>();
                    attributeIdValuesMap.forEach((attributeId, attributeValues) -> {
                        AttributeDto attributeDto = new AttributeDto();
                        attributeDto.setAttributeId(attributeId);
                        attributeDto.setAttributeValueList(attributeValues);
                        BaseAttributeGenerate baseAttributeGenerate = baseAttributeGenerateMapper.selectByPrimaryKey(attributeId);
                        if (baseAttributeGenerate != null) {
                            attributeDto.setAttributeName(baseAttributeGenerate.getBaseAttributeName());
                        }
                        attributeList.add(attributeDto);
                    });
                    goodsSearchResDto.setAttributeList(attributeList);
                    break;
                }

                case MECHANISM: {
                    List<Integer> mechanismIds = values.stream().map(AggResult::getFieldName).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(mechanismIds)) {
                        break;
                    }
                    List<SysOptionDefinition> mechanismInfoList = sysOptionDefinitionMapper.findBySysOptionDefinitionIdIn(mechanismIds);
                    if (CollectionUtils.isEmpty(mechanismInfoList)) {
                        break;
                    }
                    List<InstitutionDto> institutionList = CollectionUtils.isNotEmpty(goodsSearchResDto.getInstitutionList()) ?
                            goodsSearchResDto.getInstitutionList() : new ArrayList<>();

                    mechanismInfoList.forEach(mechanismInfo -> {
                        InstitutionDto institutionDto = new InstitutionDto();
                        institutionDto.setInstitutionId(mechanismInfo.getSysOptionDefinitionId());
                        institutionDto.setInstitutionName(mechanismInfo.getTitle());
                        institutionList.add(institutionDto);
                    });
                    goodsSearchResDto.setInstitutionList(institutionList);
                    break;
                }

                case BRAND: {
                    HashMap<Integer, Long> brandCountMap = new HashMap<>(16);
                    values.forEach(aggResult -> brandCountMap.put(Integer.parseInt(aggResult.getFieldName()), aggResult.getFieldCount()));
                    if (MapUtils.isEmpty(brandCountMap)) {
                        break;
                    }
                    List<BrandDto> brandList = CollectionUtils.isNotEmpty(goodsSearchResDto.getBrandList()) ? goodsSearchResDto.getBrandList() : new ArrayList<>();
                    brandCountMap.forEach((brandId, count) -> {
                        BrandGenerate brandGenerate = brandGenerateMapper.selectByPrimaryKey(brandId);
                        if (brandGenerate == null) {
                            return;
                        }
                        BrandDto brandDto = new BrandDto();
                        brandDto.setBrandId(brandId);
                        brandDto.setBrandName(brandGenerate.getBrandName());
                        brandDto.setGoodsCount(count);
                        brandList.add(brandDto);
                    });
                    goodsSearchResDto.setBrandList(brandList);
                    break;
                }

                case SPU_TYPE: {
                    List<Integer> spuTypeIdList = values.stream().map(AggResult::getFieldName).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(spuTypeIdList)) {
                        break;
                    }
                    List<SysOptionDefinition> spuTypeInfoList = sysOptionDefinitionMapper.findBySysOptionDefinitionIdIn(spuTypeIdList);
                    if (CollectionUtils.isEmpty(spuTypeInfoList)) {
                        break;
                    }
                    List<SpuTypeDto> spuTypeList = CollectionUtils.isNotEmpty(goodsSearchResDto.getSpuType()) ? goodsSearchResDto.getSpuType() : new ArrayList<>();
                    spuTypeInfoList.forEach(spuTypeInfo -> {
                        SpuTypeDto spuTypeDto = new SpuTypeDto();
                        spuTypeDto.setSpuTypeId(spuTypeInfo.getSysOptionDefinitionId());
                        spuTypeDto.setSpuTypeName(spuTypeInfo.getTitle());
                        spuTypeList.add(spuTypeDto);
                    });
                    goodsSearchResDto.setSpuType(spuTypeList);
                    break;
                }

                case DEPARTMENT: {
                    List<Integer> departmentIdList = values.stream().map(AggResult::getFieldName).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(departmentIdList)) {
                        break;
                    }
                    List<DepartmentsHospitalGenerate> departmentsHospitalGenerateList = departmentsHospitalGenerateMapper.getDepartmentsHospitalGenerateByIds(departmentIdList);
                    if (CollectionUtils.isEmpty(departmentsHospitalGenerateList)) {
                        break;
                    }
                    List<DepartmentDto> departmentDtoList = CollectionUtils.isNotEmpty(goodsSearchResDto.getDepartmentList()) ? goodsSearchResDto.getDepartmentList() : new ArrayList<>();
                    departmentsHospitalGenerateList.forEach(departmentsHospitalGenerate -> {
                        DepartmentDto departmentDto = new DepartmentDto();
                        departmentDto.setDepartmentId(departmentsHospitalGenerate.getDepartmentId());
                        departmentDto.setDepartmentName(departmentsHospitalGenerate.getDepartmentName());
                        departmentDtoList.add(departmentDto);
                    });
                    goodsSearchResDto.setDepartmentList(departmentDtoList);
                    break;
                }

                case SOURCE: {
                    ArrayList<BrandNatureDto> brandNatureList = new ArrayList<>();
                    Arrays.stream(BrandNatureEnum.values()).forEach(e -> {
                        BrandNatureDto brandNatureDto = new BrandNatureDto();
                        brandNatureDto.setBrandNatureId(e.getCode());
                        brandNatureDto.setBrandNatureName(e.getDesc());
                        brandNatureList.add(brandNatureDto);
                    });
                    goodsSearchResDto.setBrandNature(brandNatureList);
                    break;
                }

                case RULE_LEVEL: {
                    List<Integer> ruleLevelIds = values.stream().map(AggResult::getFieldName).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(ruleLevelIds)) {
                        break;
                    }
                    List<GoodsLevelDto> goodsLevelList = CollectionUtils.isNotEmpty(goodsSearchResDto.getGoodsLevelList()) ? goodsSearchResDto.getGoodsLevelList() : new ArrayList<>();
                    ruleLevelIds.forEach(ruleLevelId -> {
                        GoodsLevelDo goodsLevelDo = goodsLevelMapper.selectByPrimaryKey(ruleLevelId);
                        if (goodsLevelDo == null) {
                            return;
                        }
                        GoodsLevelDto goodsLevelDto = new GoodsLevelDto();
                        goodsLevelDto.setGoodsLevelId(ruleLevelId);
                        goodsLevelDto.setGoodsLevelName(goodsLevelDo.getLevelName());
                        goodsLevelList.add(goodsLevelDto);
                    });
                    goodsSearchResDto.setGoodsLevelList(goodsLevelList);
                    break;
                }

                case RULE_GEAR: {
                    List<Integer> positionIds = values.stream().map(AggResult::getFieldName).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(positionIds)) {
                        return;
                    }
                    List<GoodsPositionDto> goodsPositionList = CollectionUtils.isNotEmpty(goodsSearchResDto.getGoodsPositionList()) ? goodsSearchResDto.getGoodsPositionList() : new ArrayList<>();
                    positionIds.forEach(positionId -> {
                        GoodsPositionDo goodsPosition = goodsPositionMapper.selectByPrimaryKey(positionId);
                        if (goodsPosition == null) {
                            return;
                        }
                        GoodsPositionDto goodsPositionDto = new GoodsPositionDto();
                        goodsPositionDto.setGoodsPositionId(positionId);
                        goodsPositionDto.setGoodsPositionName(goodsPosition.getPositionName());
                        goodsPositionList.add(goodsPositionDto);
                    });
                    goodsSearchResDto.setGoodsPositionList(goodsPositionList);
                    break;
                }

                case DEFAULT_ENUM:
                default:
            }
        });
        return goodsSearchResDto;
    }

    /**
     * 价格字符串处理
     *
     * @param price
     * @return
     */
    private String priceInt2Str(Integer price) {
        if (price == null) {
            return "";
        }
        if (price < PRICE_UNIT_NUM) {
            return Integer.toString(price);
        }
        return new BigDecimal(price).divide(new BigDecimal(PRICE_UNIT_NUM), BigDecimal.ROUND_CEILING, RoundingMode.HALF_UP) + "万";
    }

    @Resource
    private SyncGoodsInfoMapper syncGoodsInfoMapper;

    /**
     * 处理商品搜索返回信息
     *
     * @param searchResultParams
     */
    private List<GoodsSearchInfoResDto> getGoodsBaseInfo(List<SearchResultParam> searchResultParams) {
        if (CollectionUtils.isEmpty(searchResultParams)) {
            return null;
        }
        List<Integer> skuIds = searchResultParams.stream().map(SearchResultParam::getSkuId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuIds)) {
            return null;
        }

        ArrayList<GoodsSearchInfoResDto> goodsSearchInfoList = new ArrayList<>();

        Map<Integer, GoodsSearchBaseInfoDto> skuGoodsBaseInfoMap = goodsSearchMapper.getGoodsSearchBaseInfoBySkuIds(skuIds)
                .stream().collect(Collectors.toMap(GoodsSearchBaseInfoDto::getSkuId, item -> item));

        HashMap<Integer, List<String>> skuLabelsMap =new HashMap<>();
        //VD-17145
                //getSkuLabelsMap(searchResultParams.subList(0, Math.min(searchResultParams.size(), 10)).stream().collect(Collectors.toMap(SearchResultParam::getSkuId, SearchResultParam::getSortValues)));
        // 去价格中心查集团价、科研终端价
        Map<Long, SkuPriceInfoDetailResponseDto> priceResult = this.getPriceBySkuIdList(skuIds);
        List<SkuPriceChangeApplyDto> findPriceChangeApply = findPriceChangeApply(skuIds);
        Map<Integer,SkuPriceChangeApplyDto> findPriceChangeApplyMap =
                findPriceChangeApply.stream().collect(Collectors.toMap(item -> Long.valueOf(item.getSkuId()).intValue(), item -> item));
        for (Integer skuId : skuIds) {
            GoodsSearchBaseInfoDto goodsSearchBaseInfo = skuGoodsBaseInfoMap.get(skuId);
            if (goodsSearchBaseInfo == null) {
                continue;
            }
            GoodsSearchInfoResDto goodsSearchInfoResDto = new GoodsSearchInfoResDto();
            BeanUtils.copyProperties(goodsSearchBaseInfo, goodsSearchInfoResDto);
            if (MapUtils.isNotEmpty(skuLabelsMap) && skuLabelsMap.containsKey(skuId)) {
                goodsSearchInfoResDto.setLabelList(skuLabelsMap.get(skuId));
            }

            // 赋值电商价 科研终端价
            SkuPriceInfoDetailResponseDto res = priceResult.get(skuId.longValue());
            if (Objects.nonNull(res)) {
                goodsSearchInfoResDto.setElectronicCommercePrice(res.getElectronicCommercePrice());
                goodsSearchInfoResDto.setResearchTerminalPrice(res.getResearchTerminalPrice());
            }
            if(CollectionUtils.isNotEmpty(findPriceChangeApply)){
                //以SkuPriceChangeApplyDto的skuId为key，SkuPriceChangeApplyDto为value，转换成Map,skuId同时转为Integer类型


                SkuPriceChangeApplyDto skuPriceChangeApplyDto = findPriceChangeApplyMap.get(skuId);
                if(skuPriceChangeApplyDto!=null){
                    if (skuPriceChangeApplyDto !=null && skuPriceChangeApplyDto.getAuditPass()!= null
                            && skuPriceChangeApplyDto.getAuditPass()== 1 &&
                            skuPriceChangeApplyDto !=null &&  skuPriceChangeApplyDto.getDisabled()!= null
                            &&  skuPriceChangeApplyDto.getDisabled()== 0
                    ){
                        goodsSearchInfoResDto.setAvgPriceOfLastYear(new BigDecimal(-1));
                    }
                    goodsSearchInfoResDto.setSaleContainsFee(skuPriceChangeApplyDto.getSaleContainsFee());
                    goodsSearchInfoResDto.setPurchseContainsFee(skuPriceChangeApplyDto.getPurchaseContainsFee());
                }
            }
            goodsSearchInfoList.add(goodsSearchInfoResDto);
        }
        return goodsSearchInfoList;
    }

     private List<SkuPriceChangeApplyDto> findPriceChangeApply(List<Integer> skuIds) {
         List<String> skuNoList = skuIds.stream()
                 .map(id -> "V" + id)
                 .collect(Collectors.toList());
         SkuPriceChangeApplyPageQueryDto skuPriceChangeApplyPageQueryDto = new SkuPriceChangeApplyPageQueryDto();
         skuPriceChangeApplyPageQueryDto.setPageNo(1);
         skuPriceChangeApplyPageQueryDto.setPageSize(100);
         skuPriceChangeApplyPageQueryDto.setIncludeSkuNosStr(String.join(",",skuNoList));

         try {
            //封装请求参数
            String requestJson = JsonUtils.translateToJson(skuPriceChangeApplyPageQueryDto);


            logger.info("findPriceChangeApply->findByPage 请求参数:" + requestJson);
            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASE_PRICEMAINTAIN_PAGE, requestJson);

            logger.info("findPriceChangeApply->findByPage 响应:" + resultJsonObj.toString());

            if (!SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                return new ArrayList<>();
            }

            Gson gson = new Gson();

            List<SkuPriceChangeApplyDto> priceChangeApplyList = gson.fromJson(resultJsonObj.getJSONObject("data").get("priceChangeApplyList").toString(),
                    new TypeToken<List<SkuPriceChangeApplyDto>>() {
                    }.getType());
            return priceChangeApplyList;

         } catch (Exception e) {
            logger.error("基础信息核价列表分页查询失败", e);
         }
        return new ArrayList<>();
    }



    @Override
    public Map<Long, SkuPriceInfoDetailResponseDto> getPriceBySkuIdList(List<Integer> skuIdList) {
        SkuPriceInfoBatchQueryDto param = new SkuPriceInfoBatchQueryDto();
        Map<Long, SkuPriceInfoDetailResponseDto> priceResult = new HashMap<>();
        param.setSkuIdList(skuIdList);
        param.setSkuNos(new ArrayList<>());
        String requestJson;
        try {
            requestJson = JsonUtils.translateToJson(param);
            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + "/sku_price_info/batch/skuIdList", requestJson);
            Gson gson = new Gson();
            if (Objects.nonNull(resultJsonObj) && SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                priceResult = gson.fromJson(resultJsonObj.getJSONObject("data").toString(), new TypeToken<Map<Long, SkuPriceInfoDetailResponseDto>>() {
                }.getType());
            }
        } catch (Exception e) {
            logger.error("价格中心查询异常", e);
        }
        return priceResult;
    }

    /**
     * 获取sku标签信息
     *
     * @param skuSortValuesMap
     * @return
     */
    private HashMap<Integer, List<String>> getSkuLabelsMap(Map<Integer, Object[]> skuSortValuesMap) {
        HashMap<Integer, List<String>> skuLabelsMap = new HashMap<>(16);
        if (MapUtils.isEmpty(skuSortValuesMap)) {
            return skuLabelsMap;
        }


        //SkuNo干预分
        HashMap<Integer, Double> skuInterveneMap = new HashMap<>(16);
        //分类干预分
        HashMap<Integer, Double> skuClassificationMap = new HashMap<>(16);
        //类目预测分
        HashMap<Integer, Double> skuCategoryMap = new HashMap<>(16);
        //热销分
        HashMap<Integer, Double> skuSalesMap = new HashMap<>(16);
        //新品分
        HashMap<Integer, Double> skuNewMap = new HashMap<>(16);
        //人气分
        HashMap<Integer, Double> skuMoodMap = new HashMap<>(16);
        //核价分
        HashMap<Integer, Double> skuCheckPriceMap = new HashMap<>(16);
        //库存分
        HashMap<Integer, Double> skuStockMap = new HashMap<>(16);

        skuSortValuesMap.forEach((skuId, sortValues) -> {
            for (int index = 0; index < sortValues.length; index++) {
                Double value = Double.valueOf(sortValues[index].toString());
                if (value == 0) {
                    continue;
                }
                switch (index) {
                    case 0: {
                        skuInterveneMap.put(skuId, value);
                        break;
                    }
                    case 1: {
                        skuClassificationMap.put(skuId, value);
                        break;
                    }
                    case 2: {
                        skuCategoryMap.put(skuId, value);
                        break;
                    }
                    case 3: {
                        skuSalesMap.put(skuId, value);
                        break;
                    }
                    case 4: {
                        skuNewMap.put(skuId, value);
                        break;
                    }
                    case 5: {
                        skuMoodMap.put(skuId, value);
                        break;
                    }
                    case 6: {
                        skuCheckPriceMap.put(skuId, value);
                        break;
                    }
                    case 7: {
                        skuStockMap.put(skuId, value);
                        break;
                    }
                    default: {

                    }
                }
            }
        });


        /**
         * 1.1 “销量”因子得分最高的商品，即 人气分最高，展示“销量之王”
         */
        setSkuLabels(skuMoodMap, skuLabelsMap, "销量之王");

        /**
         * 1.2有人工干预得分最高的，即热销分  新品分最高 ，展示标签“新品”“热销”；
         */
        setSkuLabels(skuNewMap, skuLabelsMap, "新品");
        setSkuLabels(skuSalesMap, skuLabelsMap, "热销");


        /**
         * 1.3商品在点击次数最高的，即类目预测分最高，展示“同行热推”
         * 需求变更 本期去掉
         */
//        setSkuLabels(skuCategoryMap, skuLabelsMap, "同行热推");

        /**
         * 1.4是否核价得分的商品，即核价分最高，展示标签“价格透明”
         */
        setSkuLabels(skuCheckPriceMap, skuLabelsMap, "价格透明");

        /**
         * 1.5是否有库存得分的商品，即库存分最高，展示标签“现货供应”
         */
        setSkuLabels(skuStockMap, skuLabelsMap, "现货供应");

        return skuLabelsMap;
    }

    /**
     * 设置商品标签信息
     *
     * @param map
     * @param skuLabelsMap
     * @param msg
     */
    private void setSkuLabels(HashMap<Integer, Double> map, HashMap<Integer, List<String>> skuLabelsMap, String msg) {
        List<Integer> maxScoreSkuIds = getMaxScoreSkuIds(map);
        if (CollectionUtils.isNotEmpty(maxScoreSkuIds)) {
            maxScoreSkuIds.forEach(maxScoreSkuId -> {
                if (skuLabelsMap.containsKey(maxScoreSkuId)) {
                    List<String> labels = skuLabelsMap.get(maxScoreSkuId);
                    labels.add(msg);
                } else {
                    ArrayList<String> labels = new ArrayList<>();
                    labels.add(msg);
                    skuLabelsMap.put(maxScoreSkuId, labels);
                }
            });
        }
    }

    /**
     * 返回最大的分数SKU的集合
     *
     * @param map
     * @return
     */
    private List<Integer> getMaxScoreSkuIds(HashMap<Integer, Double> map) {
        if (MapUtils.isEmpty(map)) {
            return null;
        }

        ArrayList<Integer> skuIds = new ArrayList<>();
        GoodsScoreDto goodsScoreDto = new GoodsScoreDto();
        map.forEach((k, v) -> {
            if (goodsScoreDto.getSkuId() == null) {
                goodsScoreDto.setSkuId(k);
                goodsScoreDto.setScore(v);
                skuIds.add(k);
            } else {
                if (goodsScoreDto.getScore() < v) {
                    goodsScoreDto.setSkuId(k);
                    goodsScoreDto.setScore(v);
                    skuIds.clear();
                    skuIds.add(k);
                } else if (goodsScoreDto.getScore().equals(v)) {
                    skuIds.add(k);
                }
            }
        });
        return skuIds;
    }
}
