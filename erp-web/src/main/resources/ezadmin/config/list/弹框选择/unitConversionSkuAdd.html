<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>添加产品</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="unitConversionSkuAdd" datasource="erp-datasourcetarget" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="radio">
  <div id="appendHead"></div>
  <div class="layui-fluid">
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
    </ul>
   </div>

   <form class="layui-form" id="search">

       <div class=" layui-inline ">
           <label class="layui-form-label"></label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " jdbctype="VARCHAR" type="union" name="SKU_NAME,SKU_NO,MODEL,SPEC" placeholder="可搜索订货号/商品名称/制造商型号/规格" style="" alias="vcs"  data="" datatype="" oper="like"></object>
           </div>
       </div>

    <div class=" layui-inline ">
     <label class="layui-form-label">品牌</label>
     <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="BRAND_ID" placeholder="" style="" alias="vcp" jdbctype="NUMBER" data="SELECT BRAND_ID AS `K`,BRAND_NAME AS `V`  FROM T_BRAND WHERE COMPANY_ID=1 AND IS_DELETE=0" datatype="KVSQLCACHE" oper="="></select>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">单位</label>
     <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="BASE_UNIT_ID" placeholder="" style="" alias="vcs" jdbctype="NUMBER" data="select UNIT_ID K,UNIT_NAME V from T_UNIT   WHERE IS_DEL=0 " datatype="KVSQLCACHE" oper="=">
     </select></div>
    </div>

       <!--组合搜索的隐藏域-->
       <div class=" layui-inline "><label class="layui-form-label">商品名称</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="hidden" name="SKU_NAME" placeholder="" style="" alias="vcs" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
           </div>
       </div>
       <div class=" layui-inline "><label class="layui-form-label">订货号</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="hidden" name="SKU_NO" placeholder="" style="" alias="vcs" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
           </div>
       </div>
       <div class=" layui-inline "><label class="layui-form-label">制造商型号</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="hidden" name="MODEL" placeholder="" style="" alias="vcs" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
           </div>
       </div>
       <div class=" layui-inline "><label class="layui-form-label">规格</label>
           <div class="layui-input-inline">
               <object class=" layui-input list-search-item " type="hidden" name="SPEC" placeholder="" style="" alias="vcs" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
           </div>
       </div>

   </form>
   <hr class="layui-border-blue">
      <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
         <th type="rowbutton" id="rowbutton"></th>
         <th item_name="SKU_NO" name="SKU" body="td-text" order="1" url="" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">订货号</th>
         <th item_name="SKU_NAME" name="SKU_NAME" body="td-text"   windowname="" datatype="" data="" style="min-width:300px" head="18">产品名称</th>
         <th item_name="BRAND_NAME" name="BRAND_NAME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="min-width:150px" head="18">品牌</th>
         <th item_name="SPEC_MODEL" name="SPEC_MODEL" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="min-width:150px" head="18">型号</th>
         <th item_name="UNIT_NAME" name="UNIT_NAME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">单位</th>
         <th item_name="GOODS_LEVEL_NO" name="GOODS_LEVEL_NO" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select LEVEL_NAME AS `V`,ID AS `K` FROM V_GOODS_LEVEL" style="" head="18">商品等级</th>
         <th item_name="GOODS_POSITION_NO" name="GOODS_POSITION_NO" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;无档位&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;独家产品&quot;,&quot;K&quot;:&quot;5&quot;},{&quot;V&quot;:&quot;代理产品&quot;,&quot;K&quot;:&quot;4&quot;},{&quot;V&quot;:&quot;经销产品&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;代销产品&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;未签约&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" head="18">商品档位</th>
         <th item_name="CHECK_STATUS" name="CHECK_STATUS" body="td-select" order="1" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;待完善&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;3&quot;}]" style="" head="18">审核状态</th>
      </tr>
    </thead>
    <tbody>
     <tr>
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="" groupby="">
          String sql="
          SELECT
	vcs.SKU_ID ,
    vcs.SKU_NO,
	vcs.SKU_NAME,
	tb.BRAND_NAME,
	IF(vcs.MODEL IS NULL OR vcs.MODEL = '',vcs.SPEC,vcs.MODEL) AS SPEC_MODEL,
	tu.UNIT_NAME ,
    vcs.GOODS_LEVEL_NO,
    vcs.GOODS_POSITION_NO,
	vcs.CHECK_STATUS * 1 AS CHECK_STATUS,
    tu.UNIT_ID
FROM
	V_CORE_SKU vcs
LEFT JOIN V_CORE_SPU vcp ON
	vcs.SPU_ID = vcp.SPU_ID
LEFT JOIN T_BRAND tb ON
	vcp.BRAND_ID = tb.BRAND_ID
LEFT JOIN T_UNIT tu ON
	vcs.BASE_UNIT_ID = tu.UNIT_ID
LEFT JOIN V_GOODS_LEVEL vgl ON
	vcs.GOODS_LEVEL_NO = vgl.ID
LEFT JOIN V_GOODS_POSITION vgp ON
	vgp.ID = vcs.GOODS_POSITION_NO
WHERE
	vcs.STATUS = 1
          AND vcs.IS_VIRTURE_SKU = 0
          AND vcs.CHECK_STATUS = 3
	AND vcp.STATUS = 1";


list=search(sql);

return list;</pre> count: <pre id="count" class="layui-code">SELECT count(1)
FROM
	V_CORE_SKU vcs
LEFT JOIN V_CORE_SPU vcp ON
	vcs.SPU_ID = vcp.SPU_ID
LEFT JOIN T_BRAND tb ON
	vcp.BRAND_ID = tb.BRAND_ID
LEFT JOIN T_UNIT tu ON
	vcs.BASE_UNIT_ID = tu.UNIT_ID
LEFT JOIN V_GOODS_LEVEL vgl ON
	vcs.GOODS_LEVEL_NO = vgl.ID
LEFT JOIN V_GOODS_POSITION vgp ON
	vgp.ID = vcs.GOODS_POSITION_NO
WHERE
	vcs.STATUS = 1
           AND vcs.IS_VIRTURE_SKU = 0
           AND vcs.CHECK_STATUS = 3
	AND vcp.STATUS = 1</pre></td>
     </tr>
    </tbody>
   </table>
  </div>
  <div id="appendFoot">

  </div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
 </body>
</html>