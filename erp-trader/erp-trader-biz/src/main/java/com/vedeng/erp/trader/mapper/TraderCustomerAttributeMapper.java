package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.entity.TraderCustomerAttributeEntity;
import com.vedeng.erp.trader.domain.entity.TraderCustomerBussinessAreaEntity;
import com.vedeng.erp.trader.dto.TraderCustomerAttributeDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.trader.mapper
 * @Date 2023/8/9 15:33
 */
@Repository("newTraderCustomerAttributeMapper")
public interface TraderCustomerAttributeMapper {

    /**
     * 根据客户id查询对应的客户属性（需要在OPTION_TYPE不为空的情况下使用）
     *
     * @param traderCustomerId 客户id
     * @return 根据OPTION_TYPE分组后拼接完成的字典项属性字符串
     */
    List<TraderCustomerAttributeDto> getCustomerAttributeByCustomerId(@Param("traderCustomerId") Integer traderCustomerId);

    int deleteByPrimaryKey(Integer traderCustomerAttributeId);

    int insert(TraderCustomerAttributeEntity record);

    int insertSelective(TraderCustomerAttributeEntity record);

    TraderCustomerAttributeEntity selectByPrimaryKey(Integer traderCustomerAttributeId);

    int updateByPrimaryKeySelective(TraderCustomerAttributeEntity record);

    int updateByPrimaryKey(TraderCustomerAttributeEntity record);

    int batchInsert(@Param("list") List<TraderCustomerAttributeEntity> list);

    int deleteByTraderCustomerId(@Param("traderCustomerId")Integer traderCustomerId);



}
