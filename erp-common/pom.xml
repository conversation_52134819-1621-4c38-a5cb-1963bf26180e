<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
      	<groupId>com.vedeng.erp</groupId>
		<artifactId>erp</artifactId>
		<version>1.0.0-SNAPSHOT</version>
    </parent>
	<artifactId>erp-common</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>erp-common</name>
    <description>erp-common</description>

    <properties>
        <track-filter-server-bigdata.version>1.0.2-SNAPSHOT</track-filter-server-bigdata.version>
    </properties>

    <modules>
        <module>erp-common-core</module>
        <module>erp-common-redis</module>
        <module>erp-common-feign</module>
        <module>erp-common-mybatis</module>
        <module>erp-common-statemachine</module>
        <module>erp-common-trace</module>
        <module>erp-common-cat</module>
        <module>erp-common-activiti</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.vedeng.track</groupId>
                <artifactId>track-filter-server-bigdata</artifactId>
                <version>${track-filter-server-bigdata.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
