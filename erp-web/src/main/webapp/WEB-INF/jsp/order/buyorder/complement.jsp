<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="确认审核" scope="application" />
<%@ include file="../../common/common.jsp"%>

<style>
	#payVedengBankIdLi .warning{
		margin-left: 110px;
	}
</style>

<script type="text/javascript">
	$(function () {
		let expenseCheckStatus = $('input[name=expenseCheckStatus]').val();
		expenseCheckStatus = eval(expenseCheckStatus);
		if (!expenseCheckStatus) {
			layer.confirm('当前采购费用订单有审核不通过的付款申请，请核实后再付款', {
				btn: ['确定继续', '取消']
			}, function(){
				layer.close(layer.index);
			}, function(index){
				parent.layer.closeAll();
			});
		}
	});
</script>


 <div class="formpublic">
            <form method="post" action="" id="complement">
                <ul>
					<c:if test="${pageType =='T_PAY_APPLY' && isShow == 1}">
						<li id="payVedengBankIdLi">
							<input value="1" type="hidden" id="havePayVedengBankId">
							<label class="infor_name"><span style="color: red">*</span>交易方式</label>
							<select id="payVedengBankId" class="input-middle f_left" name="payVedengBankId"  style="width: 297px">
								<option disabled selected value="-1">请选择该申请的付款银行</option>
								<c:forEach var="list" items="${paymentBankList}">
									<option value="${list.payVedengBankId}" >${list.payBankName}</option>
								</c:forEach>
							</select>
						</li>
					</c:if>
                   <li>
					<div class="infor_name">
						<c:if test="${pass==false}">
						<span>*</span>
						</c:if>
						<lable for='name'>备注</lable>
					</div>
					<div class="f_left">
						<input type="text" name="comment" id="comment" class="input-larger" value="" />
					</div>
				</li>
                </ul>
                <div class="add-tijiao tcenter">
                	<input type="hidden" id="refreshParent" value="${refreshParent}"/><%--刷新父页面--%>
                	<input type="hidden" value="${taskId}" name="taskId">
                	<input type="hidden" value="${pass}" name="pass">
                	<input type="hidden" value="${type}" name="type">
                	<input type="hidden" value="${buyorderId}" name="buyorderId">
					<input type="hidden" value="${expenseTaskId}" name="expenseTaskId">
					<input type="hidden" value="${purchasePriceChange}" id="purchasePriceChange">
					<input type="hidden" value="${tips}" id="tips">
					<input type="hidden" value="${isShow}" id="isShow">
                	<input type="hidden" name="formToken" value="${formToken}"/>
					<input type="hidden" name="expenseCheckStatus" value="${expenseCheckStatus}">
					<button type="button" class="bg-light-green" onclick="complementTask()">提交</button>
                    <button class="dele" type="button" id="close-layer">取消</button>
                </div>
           </form>
</div>
<script type="text/javascript" src='<%= basePath %>static/js/order/buyorder/complete.js?rnd=${resourceVersionKey}'></script>
<%@ include file="../../common/footer.jsp"%>