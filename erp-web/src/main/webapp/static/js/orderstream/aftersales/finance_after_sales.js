function afterSalePayApplyAudit(payApplyId,relatedId,status){
	checkLogin();
	layer.confirm("您是否确认审核通过该付款申请？", {
		btn : [ '确定', '取消' ]// 按钮
	}, function() {
		$.ajax({
			async : false,
			url : './editAfterPayApplyAudit.do',
			data : {"payApplyId":payApplyId,"relatedId":relatedId,"status":status},
			type : "POST",
			dataType : "json",
			success : function(data) {
				layer.alert(data.message, {
					icon : (data.code == 0 ? 1 : 2)
				}, function() {
					/*if (parent.layer != undefined) {
						parent.layer.close(index);
					}*/
					location.reload();
				});
			},error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		})
//		$("#auditAccountPeriodForm").submit();
	});
}

function confirmRefundAmount(afterSalesId,subjectType,type,status,atferSalesStatus){
	checkLogin();
	layer.confirm("您是否确认退款？", {
		btn : [ '确定', '取消' ]// 按钮
	}, function() {
		$.ajax({
			async : false,
			url : './afterThRefundAmountBalance.do',
			data : {"afterSalesId":afterSalesId,"subjectType":subjectType,"type":type,"status":status,"atferSalesStatus":atferSalesStatus},
			type : "POST",
			dataType : "json",
			success : function(data) {
				layer.alert(data.message, {
					icon : (data.code == 0 ? 1 : 2)
				}, function(index) {
					/*if (parent.layer != undefined) {
						parent.layer.close(index);
					}*/
					if(data.code == 0){
						location.reload();
					}else{
						layer.close(index);
					}
				});
			},error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		})
	});
}
var flag = true;//js防止重复提交
function cancelEInvoicePush(invoiceId,afterSalesId){
	checkLogin();
	layer.confirm('您是否确认退票？', {
		btn : [ '确定', '取消' ] ,// 按钮
		cancel : function(index, layero) { // 取消操作，点击右上角的X
			flag = true;
		}
	}, function() { // 是
		if(flag){
			flag = false;
			$.ajax({
				async : false,
				url : page_url + '/order/aftersalesUpgrade/cancelEInvoicePush.do',
				data : {"invoiceId":invoiceId,"afterSalesId":afterSalesId},
				type : "POST",
				dataType : "json",
				success : function(data) {
					layer.alert(data.message, {
						icon : (data.code == 0 ? 1 : 2)
					}, function(index) {
						if(data.code == 0){
							location.reload();
						}else{
							flag = true;
							layer.close(index);
						}
					});
				},error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			})
		}
	}, function() { //否
		flag = true;
	});

}

var redLetterFlag = true;//js防止重复提交
function redLetterApplication(afterSalesId, afterSalesInvoiceId, orderId, invoiceId) {
	checkLogin();
	layer.confirm('是否确认红字申请?', {
		btn: ['确定', '取消'],// 按钮
		cancel: function (index, layero) { // 取消操作，点击右上角的X
			redLetterFlag = true;
		}
	}, function () { // 是
		if (redLetterFlag) {
			redLetterFlag = false;
			$.ajax({
				async: false,
				url: page_url + '/redConfirm/api/redLetterConfirmInitCheck.do',
				data: {"afterSaleBusinessOrderId": afterSalesId, "businessOrderId": orderId, "blueInvoiceId": invoiceId},
				type: "POST",
				dataType: "json",
				success: function (data) {
					layer.alert(data.message, {
						icon: (data.code == 0 ? 1 : 2),
						cancel: function (index, layero) {
							redLetterFlag = true; // 在取消操作时重置redLetterFlag
						}
					}, function (index) {
						if (data.code == 0) {
							$("#redLetterApplication").click();
							layer.close(index);
							// location.reload();
						} else {
							layer.close(index);
						}
						redLetterFlag = true;
					});
				}, error: function (data) {
					if (data.status == 1001) {
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					} else {
						layer.alert(data.message);
					}
					redLetterFlag = true;
				}
			})
		}
	}, function () { //否
		redLetterFlag = true;
	});

}

//物料编码、注册证号（对应spu的“注册证号”）、管理类别、产品负责人（字段内容改为：该商品对应spu的归属产品经理&归属产品助理）、采购提醒、包装清单、服务条款、审核状态
$(function(){
	$(".J-skuInfo-tr").each(function(){
		var tr=$(this)
		$.getJSON("/goods/vgoods/static/skuTip.do?skuId="+$(this).attr("skuId"),function(result){
			console.log(result.data)
			var data=result.data
			var goodsInfo  ='物料编号：'+data.MATERIAL_CODE;
			goodsInfo +='<br>注册证号：'+data.REGISTRATION_NUMBER;
			goodsInfo +='<br>管理类别：'+data.MANAGE_CATEGORY_LEVEL;
			goodsInfo +='<br>产品负责人：'+data.PRODUCTMANAGER;
			goodsInfo +='<br>包装清单：'+data.PACKING_LIST;
			goodsInfo +='<br>质保年限：'+(data.QA_YEARS==''?'':data.QA_YEARS+'年') ;

			goodsInfo +='<br>库存：'+data.STOCKNUM;
			goodsInfo +='<br>可用库存：'+data.AVAILABLESTOCKNUM;
			goodsInfo +='<br>订单占用：'+data.OCCUPYNUM;
			goodsInfo +='<br>审核状态：'+data.CHECK_STATUS;
			tr.find(".JskuCode").html(data.SKU_NO);
			tr.find(".JmaterialCode").html(data.MATERIAL_CODE);
			tr.find(".JbrandName").html(data.BRAND_NAME);
			tr.find(".JskuName").html(data.SHOW_NAME);
			tr.find(".JskuModel").html(data.MODEL);
			tr.find(".JskuInfo").html(goodsInfo);
			tr.find(".JskuUnit").html(data.UNIT_NAME);
		})
	})
});

/**
 * 初始化包裹保存页面
 *
 * @param afterSalesId
 */
function initExpressSave(afterSalesId) {
	if($("#listInfo").find("input[type='checkbox'][name='checkName']:checked").length == 0 && $("#sInvoiceNo").val().trim()==""){
		layer.alert("请选择或填写需要寄送的发票！",
			{ icon: 2 },
			function (index) {
				layer.close(index);
				return false;
			}
		);
		return false;
	}

	var invoiceIdArr = [];
	var printInvoiceNo = "";
	var type = "";
	var invoicNos=[];
	var types=[];
	$("#listInfo").find("input[type='checkbox'][name='checkName']:checked").each(function(i){
		if($(this).attr("class").length > 0 && $(this).attr("class") != 0){
			layer.alert("第【"+(i+1)+"】记录已寄送发票，请验证！",
				{ icon: 2 },
				function (index) {
					layer.close(index);
					flag = false;
					return flag;
				}
			);
		}
		invoiceIdArr.push($(this).attr("id"));
		invoicNos.push($(this).attr("alt"));
		types.push($(this).attr("placeholder"));
		printInvoiceNo = $(this).attr("alt");
		type = $(this).attr("placeholder");
	});

	url =  '/order/afterSalesCommon/initExpressSave.do'
	$.ajax({
		type: 'POST',
		url: url,
		data:{
			"afterSalesId": afterSalesId,
			"invoiceIdArr" : JSON.stringify(invoiceIdArr)
		},
		dataType : "html",
		success: function(result) {
			var htmlCont = result;
			var open = layer.open({
				type: 1,
				title: '寄送发票',
				shadeClose: false,
				area : ['320px', '180px'],
				content: htmlCont,
				success: function(layero, index){

				}
			});
			$('#layerIndex').val(open);
		}
	});
}

/**
 * 关闭子页面
 */
function closeChildrenView() {
	layer.closeAll();
}

function printview(afterSalesId){
	checkLogin();
	var companyId = $("#companyId").val();
	var flag = true;
	if($("#listInfo").find("input[type='checkbox'][name='checkName']:checked").length == 0 && $("#sInvoiceNo").val().trim()==""){
		layer.alert("请选择或填写需要寄送的发票！",
			{ icon: 2 },
			function (index) {
				layer.close(index);
				return false;
			}
		);
		return false;
	}
	var invoiceIdArr = [];var invoiceNo = "";var printInvoiceNo = "";var type = "";var invoicNos=[];var types=[];
	if($("#listInfo").find("input[type='checkbox'][name='checkName']:checked").length > 0){
		$("#listInfo").find("input[type='checkbox'][name='checkName']:checked").each(function(i){
			if($(this).attr("class").length > 0 && $(this).attr("class") != 0){
				layer.alert("第【"+(i+1)+"】记录已寄送发票，请验证！",
					{ icon: 2 },
					function (index) {
						layer.close(index);
						flag = false;
						return flag;
					}
				);
			}
			invoiceIdArr.push($(this).attr("id"));
			invoicNos.push($(this).attr("alt"));
			types.push($(this).attr("placeholder"));
			printInvoiceNo = $(this).attr("alt");
			type = $(this).attr("placeholder");
		});
		if(!flag){
			return false;
		}
	}else if($("#listInfo").find("input[type='checkbox'][name='checkName']:checked").length == 0 && $("#sInvoiceNo").val().trim() != ""){
		invoiceNo = $("#sInvoiceNo").val().trim();
		printInvoiceNo = invoiceNo;
	}
	if($("#logisticsName").val().trim().length == 0){
		layer.alert("快递公司不允许为空！",
			{ icon: 2 },
			function (index) {
				layer.close(index);
				$("#logisticsName").focus();
				return false;
			}
		);
		return false;
	}
	if(companyId=="1"){
		var lName = $("#logisticsName").val();
		if(lName!="顺丰速运" && lName!="中通快递"){
			if($("#logisticsNo").val().trim().length == 0){
				layer.alert("快递单号不允许为空！",
					{ icon: 2 },
					function (index) {
						layer.close(index);
						$("#logisticsNo").focus();
						return false;
					}
				);
				return false;
			}
		}
	}else{
		if($("#logisticsNo").val().trim().length == 0){
			layer.alert("快递单号不允许为空！",
				{ icon: 2 },
				function (index) {
					layer.close(index);
					$("#logisticsNo").focus();
					return false;
				}
			);
			return false;
		}
	}

	$.ajax({
		type: "POST",
		url: page_url+"/finance/invoice/saveExpress.do",
		data: {"invoiceNo":invoiceNo,"invoiceIdArr":JSON.stringify(invoiceIdArr),"logisticsNo":$("#logisticsNo").val().trim(),"logisticsId":$('#logisticsName>option:selected').attr("id"),"qpType":$("#qpType").val(),"afterSaleId":afterSalesId},
		dataType:'json',
		success: function(data){
			var msg = "";
			var flag =0;
			if(data.code == 0){
				var logisticsName = $("#logisticsName").val();
				var url = page_url+ "/finance/invoice/printExpress.do?type="+type+"&invoiceNo="+printInvoiceNo+"&expressId="+data.data.expressId;
				var name="";
				var w=800;
				var h=600;
				if(logisticsName=="EMS"){
					url +="&logisticsId=4";
					name="EMS快递单";
				}else if(logisticsName=="中通快递"){
					url +="&logisticsId=2";
					name="中通快递单";
				}else if(logisticsName=="顺丰速运"){
					url +="&logisticsId=1";
					name="顺丰快递单";
				}else if(logisticsName=="中通快运"){
					url +="&logisticsId=18";
					name="中通快运单";
				}else if(logisticsName=="德邦快递"){
					url +="&logisticsId=7";
					name="德邦快递单";
				}
				if(companyId=="1"){
					if(logisticsName!="顺丰速运" && logisticsName!="中通快递"){
						refreshNowPageList(data);
						window.open(url,name,"top=100,left=400,width=" + w + ",height=" + h + ",toolbar=no,menubar=no,scrollbars=yes,resizable=no,location=no,status=no");
					}else if(logisticsName=="顺丰速运" || logisticsName=="中通快递"){
						$.ajax({
							type: "POST",
							url: page_url+"/finance/invoice/printExpressSf.do",
							data: {
								"invoiceNo":printInvoiceNo,
								"invoiceNos":JSON.stringify(invoicNos),
								"type":type,
								"types":JSON.stringify(types),
								"expressId":data.data.expressId,
								"logisticsName":logisticsName,
								"invoiceIdArr":JSON.stringify(invoiceIdArr),
								"logisticsId":$('#logisticsName>option:selected').attr("id")},
							dataType:'json',
							success: function(data){
								if(data.code!=0){
									layer.alert(data.message+"无法下单打印！");
									flag = 1;
								}else{
									if(data.data == null || data.data==''){
										layer.alert("快递单号为空，请检查");
										return false;
									}
									refreshNowPageList(data);//刷新列表数据
								}
							}
						})
					}
				}else{
					refreshNowPageList(data);
					window.open(url,name,"top=100,left=400,width=" + w + ",height=" + h + ",toolbar=no,menubar=no,scrollbars=yes,resizable=no,location=no,status=no");
				}

			}else{
				layer.alert(data.message,  { icon: 2 },
					function (index) {
						layer.close(index);
						return false;
					}
				);
			}

		},error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}

function auditRecordsCommonClose(num,the){

	$(".athchoice").removeClass("a-th-noChoice a-th-choice");
	$(".athchoicebu").removeClass("a-bu-noChoice a-bu-choice");
	$(the).addClass("a-bu-choice");
	$(the).parent().addClass("a-th-choice");

	if (num == 1) {
		$("#auditRecordsCommon").show();
		$("#auditRecordsOverCommon").hide();
		$("#auditRecordsPayCommon").hide();
	}else if (num == 2){
		$("#auditRecordsCommon").hide();
		$("#auditRecordsOverCommon").show();
		$("#auditRecordsPayCommon").hide();
	}else if (num == 3){
		$("#auditRecordsCommon").hide();
		$("#auditRecordsOverCommon").hide();
		$("#auditRecordsPayCommon").show();
	}
}