<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper
	namespace="com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper">
	<resultMap id="BaseResultMap"
		type="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		<id column="WAREHOUSE_GOODS_OPERATE_LOG_ID"
			property="warehouseGoodsOperateLogId" jdbcType="INTEGER" />
		<result column="WAREHOUSE_PICKING_DETAIL_ID"
			property="warehousePickingDetailId" jdbcType="INTEGER" />
		<result column="BARCODE_ID" property="barcodeId"
			jdbcType="INTEGER" />
		<result column="COMPANY_ID" property="companyId"
			jdbcType="INTEGER" />
		<result column="OPERATE_TYPE" property="operateType"
			jdbcType="BIT" />
		<result column="RELATED_ID" property="relatedId"
			jdbcType="INTEGER" />
		<result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
		<result column="BARCODE_FACTORY" property="barcodeFactory"
			jdbcType="VARCHAR" />
		<result column="NUM" property="num" jdbcType="INTEGER" />
		<!-- <result column="IS_EXPRESS" property="isExpress" jdbcType="INTEGER"
			/> -->
		<result column="WAREHOUSE_ID" property="warehouseId"
			jdbcType="INTEGER" />
		<result column="STORAGE_ROOM_ID" property="storageRoomId"
			jdbcType="INTEGER" />
		<result column="STORAGE_AREA_ID" property="storageAreaId"
			jdbcType="INTEGER" />
		<result column="STORAGE_LOCATION_ID"
			property="storageLocationId" jdbcType="INTEGER" />
		<result column="STORAGE_RACK_ID" property="storageRackId"
			jdbcType="INTEGER" />
		<result column="BATCH_NUMBER" property="batchNumber"
			jdbcType="VARCHAR" />
		<result column="STORE_TOTAL_NUM" property="storeTotalNum"
			jdbcType="VARCHAR" />
		<result column="EXPIRATION_DATE" property="expirationDate"
			jdbcType="BIGINT" />
		<result column="CHECK_STATUS" property="checkStatus"
			jdbcType="BIT" />
		<result column="CHECK_STATUS_USER" property="checkStatusUser"
			jdbcType="INTEGER" />
		<result column="CHECK_STATUS_TIME" property="checkStatusTime"
			jdbcType="BIGINT" />
		<result column="RECHECK_STATUS" property="recheckStatus"
			jdbcType="BIT" />
		<result column="RECHECK_STATUS_USER"
			property="recheckStatusUser" jdbcType="INTEGER" />
		<result column="RECHECK_STATUS_TIME"
			property="recheckStatusTime" jdbcType="BIGINT" />
		<result column="COMMENTS" property="comments"
			jdbcType="VARCHAR" />
		<result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
		<result column="CREATOR" property="creator" jdbcType="INTEGER" />
		<result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
		<result column="UPDATER" property="updater" jdbcType="INTEGER" />
		<result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
		<result column="YWTYPE" property="ywType" jdbcType="INTEGER" />
		<result column="SKU" property="sku" jdbcType="VARCHAR" />
		<result column="COST_PRICE" property="costPrice" jdbcType="DECIMAL"/>
		<result column="LOGICAL_WAREHOUSE_ID" property="logicalWarehouseId" jdbcType="INTEGER"/>
		<result column="LAST_STOCK_NUM" property="lastStockNum" jdbcType="INTEGER"/>
		<result column="goodsType" property="goodsType" jdbcType="INTEGER"/>
		<result column="DEDICATED_BUYORDER_NO" property="dedicatedBuyorderNo" jdbcType="VARCHAR"/>
	</resultMap>

	<resultMap id="WarehouseLogGroupResultMap"
			   type="com.vedeng.logistics.model.WarehouseLog">
		<id column="WAREHOUSE_GOODS_OPERATE_LOG_ID"
			property="warehouseGoodsOperateLogId" jdbcType="INTEGER" />
		<result column="ADD_TIME" property="addTime" jdbcType="VARCHAR" />

	</resultMap>

	<resultMap type="com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo"
		id="VoResultMap" extends="BaseResultMap">
		<result column="GOODS_NAME" property="goodsName"
			jdbcType="VARCHAR" />
		<result column="TRADER_NAME" property="traderName"
			jdbcType="VARCHAR" />
		<result column="BRAND_NAME" property="brandName"
			jdbcType="VARCHAR" />
		<result column="MODEL" property="model" jdbcType="VARCHAR" />
		<result column="SPEC" property="spec" jdbcType="VARCHAR" />
		<result column="SUPPLY_MODEL" property="supplyModel" jdbcType="VARCHAR" />
		<result column="MATERIAL_CODE" property="materialCode"
			jdbcType="VARCHAR" />
		<result column="UNIT_NAME" property="unitName"
			jdbcType="VARCHAR" />
		<result column="OUT_TIME" property="outTime" jdbcType="VARCHAR" />
		<result column="OPERATOR" property="operator"
			jdbcType="VARCHAR" />
		<result column="CHECKUSERNAME" property="checkUserName"
			jdbcType="VARCHAR" />
		<result column="RECHECHECKUSERNAME" property="recheckUserName"
			jdbcType="VARCHAR" />
		<result column="SKU" property="sku" jdbcType="VARCHAR" />
		<result column="STORAGEADDRESS" property="warehouseArea"
			jdbcType="VARCHAR" />
		<result column="BUYORDER_NO" property="buyorderNo"
			jdbcType="VARCHAR" />
		<result column="BUYORDER_ID" property="buyorderId"
			jdbcType="INTEGER" />
		<result column="SALEORDER_NO" property="saleorderNo"
			jdbcType="VARCHAR" />
		<result column="SALEORDER_ID" property="saleorderId"
			jdbcType="INTEGER" />
		<result column="AFTER_SALES_ID" property="afterSalesId"
			jdbcType="INTEGER" />
		<result column="AFTER_SALES_NO" property="afterSalesNo"
			jdbcType="VARCHAR" />
		<result column="CNT" property="cnt" jdbcType="INTEGER" /><!--可捡商品数（
			按入库日期、批次号、效期截止日期、关联采购单四个维度来区分的商品数 ） -->
		<result column="STORE_NUM" property="storeNum"
			jdbcType="INTEGER" />
		<result column="ZKCNT" property="zkCnt" jdbcType="INTEGER" />
		<result column="PRICE" property="price" jdbcType="DECIMAL" />
		<result column="BUYTRADERNAME" property="buytraderName"
			jdbcType="VARCHAR" />
		<result column="SALETRADERNAME" property="saletraderName"
			jdbcType="VARCHAR" />
		<result column="BARCODE" property="barcode" jdbcType="VARCHAR" />
		<result column="XSPRICE" property="xsprice" jdbcType="DECIMAL" />
		<result column="PURCHASING_PRICE" property="cgprice"
			jdbcType="DECIMAL" />
		<result column="ROWNUM" property="rowNum" jdbcType="VARCHAR" />
		<result column="ADD_TIMES" property="addtimes"
			jdbcType="VARCHAR" />
		<result column="NUMS" property="nums" jdbcType="INTEGER" />
		<result column="PICKD_ID" property="pickdId" jdbcType="INTEGER" />
		<result column="FLAG" property="flag" jdbcType="VARCHAR" />
		<result column="EXPIRATION_DATE_STR"
			property="expiration_date_str" jdbcType="VARCHAR" />
		<result column="ADD_TIME_STR" property="add_time_str"
			jdbcType="VARCHAR" />
		<result column="PICKCNT" property="pCtn" jdbcType="INTEGER" /><!--已捡商品数（
			按入库日期、批次号、效期截止日期、关联采购单四个维度来区分） -->
		<!-- 生产厂家 -->
		<result column="MANUFACTURER" property="manufacturer"
			jdbcType="VARCHAR" />
		<!-- 生产许可证号 -->
		<result column="PRODUCTION_LICENSE"
			property="productionLicenseNumber" jdbcType="VARCHAR" />
		<!-- 注册证号 -->
		<result column="REGISTRATION_NUMBER"
			property="registrationNumber" jdbcType="VARCHAR" />
		<!-- 产品名称（注册证/备案凭证） -->
		<result column="PRODUCT_CHINESE_NAME"
				property="productChineseName" jdbcType="VARCHAR" />
			<!-- 商品备案编号 -->
		<result column="RECORD_NUMBER" property="recordNumber"
			jdbcType="VARCHAR" />
		<!-- 储运温度 -->
		<result column="TEMPERATURE" property="temperaTure"
			jdbcType="VARCHAR" />
		<!-- 字典表,判断储运条件 -->
		<result column="CONDITION_ONE" property="conditionOne"
			jdbcType="VARCHAR" />
		<result column="CATEGORY_ID" property="categoryId"
			jdbcType="INTEGER" />
		<result column="IS_PROBLEM" property="isProblem"
			jdbcType="INTEGER" />
		<result column="PROBLEM_REMARK" property="problemRemark"
			jdbcType="VARCHAR" />

		<result column="VEDENG_BATCH_NUMER" property="vedengBatchNumer" />
		<result column="STERILZATION_BATCH_NUMBER" property="sterilizationBatchNo" />
		<result column="REAL_GOODS_NUM" property="realGoodsNum" />


		<result column="REGISTRATION_NUMBER_ID" property="registrationNumberId" />
		<result column="REGISTRATION_NUMBER" property="registrationNo" />
		<result column="FIRST_ENGAGE_ID" property="fitstEngageId" />
		<result column="DEDICATED_BUYORDER_NO" property="dedicatedBuyorderNo" />
	</resultMap>

	<resultMap
		type="com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo"
		id="VoLogResultMap" extends="BaseResultMap">
		<result column="GOODS_NAME" property="goodsName"
			jdbcType="VARCHAR" />
		<result column="BRAND_NAME" property="brandName"
			jdbcType="VARCHAR" />
		<result column="MODEL" property="model" jdbcType="VARCHAR" />
		<result column="MATERIAL_CODE" property="materialCode"
			jdbcType="VARCHAR" />
		<result column="UNIT_NAME" property="unitName"
			jdbcType="VARCHAR" />
		<result column="SKU" property="sku" jdbcType="VARCHAR" />
	</resultMap>


	<sql id="Base_Column_List">
		WAREHOUSE_GOODS_OPERATE_LOG_ID, BARCODE_ID, COMPANY_ID, OPERATE_TYPE, RELATED_ID,
		GOODS_ID, BARCODE_FACTORY, NUM, WAREHOUSE_ID, STORAGE_ROOM_ID, STORAGE_AREA_ID,
		STORAGE_LOCATION_ID,
		STORAGE_RACK_ID, BATCH_NUMBER, EXPIRATION_DATE, CHECK_STATUS, CHECK_STATUS_USER,
		CHECK_STATUS_TIME, RECHECK_STATUS, RECHECK_STATUS_USER,
		RECHECK_STATUS_TIME, COMMENTS,
		ADD_TIME, CREATOR, MOD_TIME, UPDATER,IS_ENABLE, DEDICATED_BUYORDER_NO
	</sql>

	<!-- 采购入库的采购信息 -->
	<select id="getWOLByC" resultMap="VoResultMap"
		parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
		b.PRICE,
		c.BUYORDER_NO,
		c.BUYORDER_ID,
		c.TRADER_NAME
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG a
		LEFT JOIN T_BUYORDER_GOODS b ON a.RELATED_ID = b.BUYORDER_GOODS_ID
		AND b.IS_DELETE = 0
		INNER JOIN T_BUYORDER c ON b.BUYORDER_ID =
		c.BUYORDER_ID
		WHERE a.OPERATE_TYPE =1
		AND
		a.WAREHOUSE_GOODS_OPERATE_LOG_ID =
		#{warehouseGoodsOperateLogId,jdbcType=INTEGER}
		AND a.IS_ENABLE =1
	</select>
	<!-- 销售业务入库的采购信息 -->
	<select id="getWOLByS" resultMap="VoResultMap"
		parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
		<!-- TT.BUYORDER_ID, TT.BUYORDER_NO, -->
		TT.PRICE,
		TT.TRADER_NAME,
		T.AFTER_SALES_ID BUYORDER_ID,
		T.AFTER_SALES_NO BUYORDER_NO
		FROM
		(
		SELECT
		a.*, c.SALEORDER_GOODS_ID,d.AFTER_SALES_ID,d.AFTER_SALES_NO
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG a
		LEFT JOIN T_AFTER_SALES_GOODS b ON a.RELATED_ID = b.AFTER_SALES_GOODS_ID
		AND b.GOODS_TYPE = 0
		LEFT JOIN T_AFTER_SALES d ON b.AFTER_SALES_ID = d.AFTER_SALES_ID
		INNER JOIN T_SALEORDER_GOODS c ON b.ORDER_DETAIL_ID = c.SALEORDER_GOODS_ID
		AND c.IS_DELETE = 0
		WHERE
		a.OPERATE_TYPE IN (3, 5)
		AND a.IS_ENABLE = 1
		AND a.WAREHOUSE_GOODS_OPERATE_LOG_ID =
		#{warehouseGoodsOperateLogId,jdbcType=INTEGER}
		) T
		LEFT JOIN (
		SELECT
		a.RELATED_ID,
		a.ADD_TIME,
		e.BUYORDER_ID,
		e.BUYORDER_NO,
		d.PRICE,
		e.TRADER_NAME
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG a
		LEFT JOIN T_WAREHOUSE_PICKING_DETAIL b ON a.WAREHOUSE_PICKING_DETAIL_ID =
		b.WAREHOUSE_PICKING_DETAIL_ID
		AND b.IS_ENABLE = 1
		LEFT JOIN T_WAREHOUSE_GOODS_OPERATE_LOG c ON
		b.WAREHOUSE_GOODS_OPERATE_LOG_ID = c.WAREHOUSE_GOODS_OPERATE_LOG_ID
		AND c.IS_ENABLE = 1
		AND c.OPERATE_TYPE = 1
		LEFT JOIN T_BUYORDER_GOODS d ON c.RELATED_ID = d.BUYORDER_GOODS_ID
		AND d.IS_DELETE = 0
		LEFT JOIN T_BUYORDER e ON d.BUYORDER_ID = e.BUYORDER_ID
		WHERE
		1 = 1
		AND a.OPERATE_TYPE = 2
		AND a.IS_ENABLE = 1
		) TT ON T.SALEORDER_GOODS_ID = TT.RELATED_ID
		ORDER BY
		TT.ADD_TIME DESC
		LIMIT 1
	</select>
	<!-- 采购售后采购信息 -->
	<select id="getWOLByCH" resultMap="VoResultMap"
		parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
		c.PRICE,
		e.AFTER_SALES_ID BUYORDER_ID,
		e.AFTER_SALES_NO BUYORDER_NO,
		d.TRADER_NAME
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG a
		LEFT JOIN T_AFTER_SALES_GOODS b ON a.RELATED_ID =
		b.AFTER_SALES_GOODS_ID
		AND b.GOODS_TYPE = 0
		LEFT JOIN T_BUYORDER_GOODS c
		ON b.ORDER_DETAIL_ID = c.BUYORDER_GOODS_ID
		LEFT JOIN T_AFTER_SALES e ON
		b.AFTER_SALES_ID = e.AFTER_SALES_ID
		AND c.IS_DELETE = 0
		LEFT JOIN
		T_BUYORDER d ON c.BUYORDER_ID = d.BUYORDER_ID
		WHERE
		a.OPERATE_TYPE = 8
		AND a.IS_ENABLE = 1
		AND a.WAREHOUSE_GOODS_OPERATE_LOG_ID =
		#{warehouseGoodsOperateLogId,jdbcType=INTEGER}
	</select>
	<!-- 采购售后采购信息 -->
	<select id="getLendoutByL" resultMap="VoResultMap"
		parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
		  b.`LEND_OUT_NO` AS BUYORDER_NO,
		  b.`LEND_OUT_ID` AS BUYORDER_ID,
		  b.`TRADER_NAME`
		FROM
		  T_WAREHOUSE_GOODS_OPERATE_LOG a
		  LEFT JOIN T_LEND_OUT b
		  ON a.RELATED_ID = b.`LEND_OUT_ID`
		  AND b.`IS_ENABLE` = 0
		WHERE
		a.OPERATE_TYPE = 9
		AND a.IS_ENABLE = 1
		AND a.WAREHOUSE_GOODS_OPERATE_LOG_ID =
		#{warehouseGoodsOperateLogId,jdbcType=INTEGER}
	</select>
	<select id="getAftersalesGoodsSum"
		resultType="java.lang.Integer"
		parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
		SUM(ABS(NUM))
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG
		WHERE
		IS_ENABLE = 1
		<if test="operateType != null">
			AND OPERATE_TYPE = #{operateType,jdbcType=BIT}
		</if>
		<if test="relatedId != null">
			AND RELATED_ID = #{relatedId,jdbcType=INTEGER}
		</if>
	</select>

	<select id="getAftersalesGoodsSum2"
			resultType="java.lang.Integer"
			parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
		SUM(ABS(NUM))
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG
		WHERE
		IS_ENABLE = 1
		<if test="operateType != null">
			AND OPERATE_TYPE = #{operateType,jdbcType=BIT}
		</if>
		<if test="afterSalesId != null">
			AND AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
		</if>
	</select>

	<update id="updateWarehouse">
		<foreach collection="wg" item="item" index="index" open=""
			close="" separator=";">
			update T_WAREHOUSE_GOODS_OPERATE_LOG
			<set>
				<if test="item.isProblem!=null">
					IS_PROBLEM= #{item.isProblem},
				</if>
				<if test="item.problemRemark!=null">
					PROBLEM_REMARK=#{item.problemRemark}
				</if>
			</set>
			where
			WAREHOUSE_GOODS_OPERATE_LOG_ID=#{item.warehouseGoodsOperateLogId}
		</foreach>
	</update>
	<select id="getWarehouseGoodsId" resultMap="VoResultMap"
		parameterType="com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo">
		select GOODS_ID from T_WAREHOUSE_GOODS_OPERATE_LOG
		where
		IS_PROBLEM=#{isProblem}
	</select>

	<!-- 当前订单下商品出库清单 -->
	<select id="getWarehouseLendOutList" resultMap="VoResultMap"
		parameterType="com.vedeng.order.model.Saleorder">
		select
		<if test="bussinessType == 10">
			f.GOODS_NAME,
			g.BRAND_NAME,
			f.MODEL,
			h.UNIT_NAME,
		</if>
		d.BARCODE,
		c.MATERIAL_CODE,
		k.SKU_NO as SKU,
		a.NUM ,
		a.ADD_TIME,
		a.WAREHOUSE_GOODS_OPERATE_LOG_ID,
		a.IS_ENABLE,
		a.CREATOR,
		a.GOODS_ID,
		a.BARCODE_FACTORY,
		a.BATCH_NUMBER
		<if test="bussinessType == 10">
		  ,ABS(sum(a.NUM)) as REAL_GOODS_NUM,
		a.VEDENG_BATCH_NUMER,
		a.STERILZATION_BATCH_NUMBER,
			n.REGISTRATION_NUMBER_ID,
			n.REGISTRATION_NUMBER,
			a.EXPIRATION_DATE,
			a.CHECK_STATUS_TIME,
			a.PRODUCT_DATE,
			z.FIRST_ENGAGE_ID
		</if>
		from T_WAREHOUSE_GOODS_OPERATE_LOG a
		LEFT JOIN
		<if test="bussinessType == 10">
			T_LEND_OUT b
            ON b.`LEND_OUT_ID` =    a.RELATED_ID
			LEFT JOIN T_GOODS f ON b.GOODS_ID =
			f.GOODS_ID
			LEFT JOIN T_BRAND g ON f.BRAND_ID = g.BRAND_ID
			LEFT JOIN
			T_UNIT h ON f.UNIT_ID = h.UNIT_ID
		</if>
		LEFT JOIN T_GOODS c on b.GOODS_ID = c.GOODS_ID
		LEFT JOIN T_BARCODE d ON
		a.BARCODE_ID = d.BARCODE_ID
		AND d.IS_ENABLE = 1
		left join V_CORE_SKU k on a.GOODS_ID=k.SKU_ID
		left join V_CORE_SPU p on k.SPU_ID=p.SPU_ID
		left join T_FIRST_ENGAGE z on p.FIRST_ENGAGE_ID=z.FIRST_ENGAGE_ID and z.IS_DELETED=0
		left join T_REGISTRATION_NUMBER n on z.REGISTRATION_NUMBER_ID=n.REGISTRATION_NUMBER_ID
		where 1=1
		<if test="bussinessType == 10">
			and b.`LEND_OUT_ID` = #{bussinessId,jdbcType=INTEGER}
		</if>
		and a.OPERATE_TYPE =#{bussinessType,jdbcType=INTEGER}
		and a.IS_ENABLE=1
		<if test="outGoodsTime != null and outGoodsTime != ''">
			AND a.ADD_TIME in (${outGoodsTime})
		</if>
		<if test="bussinessType == 10">
			group by a.VEDENG_BATCH_NUMER,a.GOODS_ID,a.BARCODE_ID
		</if>
		ORDER BY a.WAREHOUSE_GOODS_OPERATE_LOG_ID DESC
	</select>
	 <select id="getfirstRegistrationInfo" parameterType="java.util.List" resultMap="VoResultMap">
		SELECT
		a.`WAREHOUSE_GOODS_OPERATE_LOG_ID`,c.SKU_ID AS `GOODS_ID`,
		IFNULL(m.`PRODUCT_COMPANY_CHINESE_NAME`,'') as MANUFACTURER,
		IFNULL(re.`PRODUCT_COMPANY_LICENCE`,'') as PRODUCT_COMPANY_LICENCE,
		IFNULL(fir.`TEMPERATURE`,'') as TEMPERATURE,
		IFNULL(fir.CONDITION_ONE,'') as CONDITION_ONE,
		IFNULL(re.`REGISTRATION_NUMBER`,'')  as REGISTRATION_NUMBER,
		re.PRODUCT_CHINESE_NAME,
		d.SPU_TYPE,
		c.SPEC,
		c.SUPPLY_MODEL
		FROM
		`T_WAREHOUSE_GOODS_OPERATE_LOG` a

		LEFT JOIN `V_CORE_SKU` c
		ON b.GOODS_ID = c.`SKU_ID`
		LEFT JOIN `V_CORE_SPU` d
		ON c.`SPU_ID` = d.`SPU_ID`
		LEFT JOIN `T_FIRST_ENGAGE` fir
		ON d.`FIRST_ENGAGE_ID` = fir.`FIRST_ENGAGE_ID`
		LEFT JOIN T_REGISTRATION_NUMBER re
		ON fir.`REGISTRATION_NUMBER_ID` = re.`REGISTRATION_NUMBER_ID`
		LEFT JOIN T_PRODUCT_COMPANY m
		ON re.`PRODUCT_COMPANY_ID` = m.`PRODUCT_COMPANY_ID`
		WHERE a.`WAREHOUSE_GOODS_OPERATE_LOG_ID` IN
		<foreach item="wo" index="index" collection="list" open="("
			separator="," close=")">
			#{wo.warehouseGoodsOperateLogId,jdbcType=INTEGER}
		</foreach>
   </select>
	<select id="getWarehouseInfoById" resultMap="BaseResultMap">
	SELECT
	A.*,B.SKU
	FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG A
	LEFT JOIN T_GOODS B ON A.GOODS_ID = B.GOODS_ID
	WHERE
	A.WAREHOUSE_GOODS_OPERATE_LOG_ID=#{warehouseGoodsOperateLogId,jdbcType=INTEGER}
	</select>
	<select id="getBarcodeIsEnable" resultMap="BaseResultMap" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		 SELECT
	a.WAREHOUSE_GOODS_OPERATE_LOG_ID,a.NUM,a.BARCODE_ID
	FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG a
	WHERE
	a.IS_ENABLE = 1
	AND a.BARCODE_ID= #{barcodeId,jdbcType=INTEGER}
	<if test="operatorfalg == 2">
	AND a.LOG_TYPE =1
	</if>
	<if test="operatorfalg == 1">
	AND a.LOG_TYPE =0
	</if>
	</select>
	<select id="getWarehouseIdByExpressDetail" parameterType="com.vedeng.logistics.model.ExpressDetail" resultMap="BaseResultMap">
		SELECT
			C.WAREHOUSE_GOODS_OPERATE_LOG_ID,ABS(C.NUM)
		FROM
			T_WAREHOUSE_GOODS_OPERATE_LOG C
		WHERE
		 C.RELATED_ID = #{relatedId,jdbcType=INTEGER}
		 AND C.OPERATE_TYPE IN (2,18)
		 AND C.IS_ENABLE=1
		<if test="wmsOrderNo != null">
		AND C.COMMENTS = #{wmsOrderNo,jdbcType=VARCHAR}
		</if>
		AND	C.WAREHOUSE_GOODS_OPERATE_LOG_ID NOT IN (
			SELECT
				B.WAREHOUSE_GOODS_OPERATE_LOG_ID
			FROM
				T_EXPRESS_DETAIL A
				JOIN T_EXPRESS D ON A.EXPRESS_ID=D.EXPRESS_ID
				JOIN V_E_W_EXPRESS_WAREHOUSE B ON A.EXPRESS_DETAIL_ID = B.EXPRESS_DETAIL_ID
			WHERE
				A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
				AND A.BUSINESS_TYPE IN (496,660)
				AND D.IS_ENABLE=1
			)
		<if test="wmsOrderNo == null">
		limit #{num}
		</if>
	</select>
	<insert id="insertExpressWarehouse">
		INSERT INTO
		V_E_W_EXPRESS_WAREHOUSE  (EXPRESS_DETAIL_ID ,WAREHOUSE_GOODS_OPERATE_LOG_ID)
		VALUES
		(#{expressDetailId,jdbcType=INTEGER},#{warehouseLogId,jdbcType=INTEGER})
	</insert>
	<update id="updateExpressWarehouse">
		UPDATE V_E_W_EXPRESS_WAREHOUSE SET IS_ENABLE=1 WHERE WAREHOUSE_GOODS_OPERATE_LOG_ID =#{warehouseLogId}
	</update>
	<select id="getExpressWlogIds" resultType="integer">
	SELECT
		B.WAREHOUSE_GOODS_OPERATE_LOG_ID
	FROM
		T_EXPRESS_DETAIL A
		LEFT JOIN V_E_W_EXPRESS_WAREHOUSE B ON A.EXPRESS_DETAIL_ID = B.EXPRESS_DETAIL_ID
	WHERE
		B.IS_ENABLE = 0
		AND A.EXPRESS_ID=#{expressId,jdbcType=INTEGER}
	</select>

	<select id="getLastOutTime" resultType="long">
		SELECT
		MAX(a.ADD_TIME)
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG a
		WHERE
		a.WAREHOUSE_GOODS_OPERATE_LOG_ID IN
		<foreach item="wId" index="index" collection="idList" open="("
				 separator="," close=")">
			#{wId}
		</foreach>
	</select>
	<select id="getWarehouseLogIdBy" resultType="integer">
	  SELECT
	C.WAREHOUSE_GOODS_OPERATE_LOG_ID
		FROM
	T_SALEORDER A
	LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID = B.SALEORDER_ID AND B.IS_DELETE=0
	LEFT JOIN T_WAREHOUSE_GOODS_OPERATE_LOG C ON B.SALEORDER_GOODS_ID = C.RELATED_ID
	AND C.OPERATE_TYPE = 2  AND C.IS_ENABLE= 1
	WHERE A.SALEORDER_ID=#{saleorderId,jdbcType=INTEGER};
	</select>

	<select id="getWarehouseoutRecordCounts" parameterType="java.lang.Integer" resultType="java.lang.Integer">
		SELECT
			COUNT( * )
		FROM
			(
			SELECT
				a.WAREHOUSE_GOODS_OPERATE_LOG_ID AS LOG_ID
			FROM
				T_WAREHOUSE_GOODS_OPERATE_LOG a
				LEFT JOIN T_SALEORDER_GOODS b ON b.SALEORDER_GOODS_ID = a.RELATED_ID
				AND b.IS_DELETE = 0
			WHERE
				a.OPERATE_TYPE = 2
				AND b.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
				AND a.IS_ENABLE = 1 UNION ALL
			SELECT
				a.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID AS LOG_ID
			FROM
				T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL a
				LEFT JOIN T_SALEORDER_GOODS b ON b.SALEORDER_GOODS_ID = a.RELATED_ID
				AND b.IS_DELETE = 0
			WHERE
				a.OPERATE_TYPE = 2
				AND b.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
			AND a.IS_ENABLE = 1
			) res

	</select>

	<select id="getSameBatchGoodsInfo" parameterType="com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo" resultMap="VoLogResultMap">
    SELECT
 	WAREHOUSE_GOODS_OPERATE_LOG_ID,
 	GOODS_ID,
 	T.NUM
FROM (
 SELECT
	e.GOODS_ID,
	e.WAREHOUSE_GOODS_OPERATE_LOG_ID,
	(
		IFNULL( SUM( e.NUM ), 0 ) - IFNULL( ABS( aa.outnum ), 0 )
	) NUM
FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG e
	LEFT JOIN (
	SELECT
		SUM( b.NUM ) AS outnum,
		b.BARCODE_ID ,
		WAREHOUSE_GOODS_OPERATE_LOG_ID
	FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG b
	WHERE
		b.LOG_TYPE =1
		AND b.IS_ENABLE = 1
		AND b.GOODS_ID =#{goodsId}
		AND b.BATCH_NUMBER=#{batchNumber}
		AND b.EXPIRATION_DATE=#{expirationDate}
		AND b.`COMPANY_ID`=1
		and b.BARCODE_ID!=0

	GROUP BY
		b.WAREHOUSE_GOODS_OPERATE_LOG_ID
	) AS aa ON e.BARCODE_ID = aa.BARCODE_ID
WHERE
	1 = 1
	AND e.LOG_TYPE =0
	AND e.IS_ENABLE = 1
    AND e.GOODS_ID=#{goodsId}
    <if test="batchNumber!=null">
            AND e.BATCH_NUMBER=#{batchNumber}
    </if>
     <if test="expirationDate!=null">
            AND e.EXPIRATION_DATE=#{expirationDate}
     </if>
	AND e.COMPANY_ID = 1
	AND e.BARCODE_ID!=0

GROUP BY
      e.WAREHOUSE_GOODS_OPERATE_LOG_ID
	 ) T
WHERE
	T.NUM > 0
	</select>
	<select id="getStorageInfo" resultMap="BaseResultMap" parameterType="integer">
SELECT
	g.WAREHOUSE_NAME,
	IFNULL( h.STORAGE_ROOM_NAME, '' ) STORAGE_ROOM_NAME,
	IFNULL( i.STORAGE_AREA_NAME, '' ) STORAGE_AREA_NAME,
	IFNULL( j.STORAGE_RACK_NAME, '' ) STORAGE_RACK_NAME,
	IFNULL( k.STORAGE_LOCATION_NAME, '' )STORAGE_LOCATION_NAME
FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG a
	LEFT JOIN T_WAREHOUSE g ON a.WAREHOUSE_ID = g.WAREHOUSE_ID
	LEFT JOIN T_STORAGE_ROOM h ON a.STORAGE_ROOM_ID = h.STORAGE_ROOM_ID
	LEFT JOIN T_STORAGE_AREA i ON a.STORAGE_AREA_ID = i.STORAGE_AREA_ID
	LEFT JOIN T_STORAGE_RACK j ON a.STORAGE_RACK_ID = j.STORAGE_RACK_ID
	LEFT JOIN T_STORAGE_LOCATION k ON a.STORAGE_LOCATION_ID = k.STORAGE_LOCATION_ID
	WHERE a.WAREHOUSE_GOODS_OPERATE_LOG_ID=#{warehouseGoodsOperateLogId,jdbcType=INTEGER};
	</select>
	<select id="getBuyOrderPrice" resultMap="BaseResultMap" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
	SELECT
	A.BUYORDER_GOODS_ID AS RELATED_ID,
	A.PRICE  AS COST_PRICE
	FROM
	T_BUYORDER_GOODS A
	WHERE
	A.BUYORDER_GOODS_ID IN
	<foreach item="log" index="index" collection="warehouseGoodsOperateLogList" open="("
				 separator="," close=")">
			#{log.relatedId,jdbcType=INTEGER}
	</foreach>
	</select>
	<update id="saveCostPrice" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog" >
	UPDATE T_WAREHOUSE_GOODS_OPERATE_LOG
	SET COST_PRICE = #{costPrice,jdbcType=DECIMAL}
	WHERE
	WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER} AND COST_PRICE = 0
	</update>
	<update id="saveNewCostPrice" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
	UPDATE T_WAREHOUSE_GOODS_OPERATE_LOG
	SET NEW_COST_PRICE = #{newCostPrice,jdbcType=DECIMAL}
	WHERE
	WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER} AND NEW_COST_PRICE = 0
	</update>

	<select id="getBuyOrderChangePrice" resultMap="BaseResultMap">
	SELECT
	A.WAREHOUSE_GOODS_OPERATE_LOG_ID,D.PRICE AS COST_PRICE,
	D.PRICE AS newCostPrice,A.GOODS_ID
	FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG A
	LEFT JOIN T_AFTER_SALES_GOODS B ON A.RELATED_ID = B.AFTER_SALES_GOODS_ID
	LEFT JOIN T_BUYORDER_GOODS D ON D.BUYORDER_GOODS_ID = B.ORDER_DETAIL_ID
	WHERE
		A.WAREHOUSE_GOODS_OPERATE_LOG_ID IN
		<foreach item="log" index="index" collection="warehouseGoodsOperateLogList" open="("
				 separator="," close=")">
			#{log.warehouseGoodsOperateLogId,jdbcType=INTEGER}
		</foreach>
	</select>
	<select id="getSaleOrderRelateId" resultMap="BaseResultMap" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
	SELECT
	A.WAREHOUSE_GOODS_OPERATE_LOG_ID,A.GOODS_ID,
	B.ORDER_DETAIL_ID AS RELATED_ID
	FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG A
	LEFT JOIN T_AFTER_SALES_GOODS B ON A.RELATED_ID = B.AFTER_SALES_GOODS_ID
	WHERE A.WAREHOUSE_GOODS_OPERATE_LOG_ID IN
		<foreach item="log" index="index" collection="warehouseGoodsOperateLogList" open="("
				 separator="," close=")">
			#{log.warehouseGoodsOperateLogId,jdbcType=INTEGER}
		</foreach>
	</select>

	<select id="getLendOutWhareHouserInList" resultMap="BaseResultMap">
	SELECT
	A.WAREHOUSE_GOODS_OPERATE_LOG_ID,A.OPERATE_TYPE,A.RELATED_ID,A.GOODS_ID,A.BARCODE_ID
    FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG A
    WHERE A.OPERATE_TYPE NOT IN (10)
    AND A.IS_ENABLE = 1
    AND A.GOODS_ID = #{goodsId,jdbcType=INTEGER}
	AND	A.BARCODE_ID IN (
	SELECT
		B.BARCODE_ID
	FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG B
	WHERE
	B.OPERATE_TYPE = 10 AND B.IS_ENABLE=1
	AND B.RELATED_ID = #{relatedId,jdbcType=INTEGER})
	</select>
	<select id="getWarehouseZKlog" resultMap="BaseResultMap">
	SELECT
		A.WAREHOUSE_GOODS_OPERATE_LOG_ID,A.OPERATE_TYPE,A.RELATED_ID,A.GOODS_ID,A.BARCODE_ID,A.NUM
	FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG A
	WHERE
	A.IS_ENABLE = 1
	AND (A.COST_PRICE = 0 OR A.NEW_COST_PRICE = 0)
	AND A.OPERATE_TYPE IN ( #{operaType,jdbcType=INTEGER} )
	AND A.COMPANY_ID = 1
	AND A.BARCODE_ID != 0
	AND A.GOODS_ID NOT IN ( SELECT COMMENTS FROM T_SYS_OPTION_DEFINITION WHERE PARENT_ID = 693 )
	AND A.LOG_TYPE = 0
	AND A.NUM > 0

-- 	AND A.VEDENG_BATCH_NUMER != ''
-- 	AND A.LAST_STOCK_NUM > 0
-- 	AND A.IS_USE = 0
	</select>
	<update id="updateIsUse">
	UPDATE T_WAREHOUSE_GOODS_OPERATE_LOG
	SET IS_USE = #{isUse,jdbcType=INTEGER}
	WHERE
	LOG_TYPE =0
	AND BARCODE_ID IN
		<foreach item="log" index="index" collection="list" open="("
				 separator="," close=")">
			#{log.barcodeId,jdbcType=INTEGER}
		</foreach>
	</update>
	<select id="getIsUseLog" resultMap="BaseResultMap">
	SELECT DISTINCT
		B.BARCODE_ID
	FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG B
	WHERE
		 B.LOG_TYPE =1 AND BARCODE_ID IS NOT NULL AND  BARCODE_ID !=0
	AND B.IS_ENABLE = 1
	</select>

    <update id="updateBadHistoryLogIsUse">
		UPDATE T_WAREHOUSE_GOODS_OPERATE_LOG
	SET IS_USE = 1
	WHERE  IS_USE = 0 AND LOG_TYPE =0
	AND ( BARCODE_ID IS NULL OR BARCODE_ID = 0 )
	</update>

	<select id="getBuyorderInlogicalByBuyorderGoodsId"
			resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog" resultMap="BaseResultMap">
		SELECT
			A.OPERATE_TYPE,A.GOODS_ID,
			A.RELATED_ID,
			SUM(A.LAST_STOCK_NUM) AS LAST_STOCK_NUM,
			A.LOGICAL_WAREHOUSE_ID
		FROM
			T_WAREHOUSE_GOODS_OPERATE_LOG A
		WHERE
			A.OPERATE_TYPE = 1
			AND A.RELATED_ID = #{buyorderGoodsId,jdbcType=INTEGER}
			AND A.IS_ENABLE = 1
			AND A.IS_USE = 0
			GROUP BY A.LOGICAL_WAREHOUSE_ID , A.GOODS_ID, A.RELATED_ID
	</select>

    <update id="updateInIsUseAndLastStockNum">
	UPDATE T_WAREHOUSE_GOODS_OPERATE_LOG
	SET LAST_STOCK_NUM = NUM
	WHERE
	WAREHOUSE_GOODS_OPERATE_LOG_ID IN
	<foreach item="log" index="index" collection="list" open="("  separator="," close=")">
			#{log.warehouseGoodsOperateLogId,jdbcType=INTEGER}
		</foreach>
	</update>
	<update id="updateOutIsUseAndLastStockNum">
	UPDATE
	T_WAREHOUSE_GOODS_OPERATE_LOG
	SET
	LAST_STOCK_NUM = LAST_STOCK_NUM + #{num,jdbcType=INTEGER} ,
	IS_USE = (CASE LAST_STOCK_NUM WHEN 0 THEN 1 ELSE 0 END )
	WHERE
	BARCODE_ID = #{barcodeId,jdbcType=INTEGER}
	AND LOG_TYPE =0
	AND IS_ENABLE = 1
	</update>
	<select id="getLogByWarehouseGoodsOperateLogId"
			resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
	SELECT
	A.NUM,
	A.OPERATE_TYPE,
	A.NUM,
	A.RELATED_ID
	FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG  A
	WHERE
	A.WAREHOUSE_GOODS_OPERATE_LOG_ID=#{warehouseGoodsOperateLogId,jdbcType=INTEGER}
	</select>

	<insert id="insertSelective" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog"
			useGeneratedKeys="true" keyProperty="warehouseGoodsOperateLogId">
		insert into T_WAREHOUSE_GOODS_OPERATE_LOG
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="barcodeId != null">
				BARCODE_ID,
			</if>
			<if test="companyId != null">
				COMPANY_ID,
			</if>
			<if test="operateType != null">
				OPERATE_TYPE,
			</if>
			<if test="relatedId != null">
				RELATED_ID,
			</if>
			<if test="goodsId != null">
				GOODS_ID,
			</if>
			<if test="barcodeFactory != null">
				BARCODE_FACTORY,
			</if>
			<if test="num != null">
				NUM,
			</if>
			<if test="warehouseId != null">
				WAREHOUSE_ID,
			</if>
			<if test="storageRoomId != null">
				STORAGE_ROOM_ID,
			</if>
			<if test="storageAreaId != null">
				STORAGE_AREA_ID,
			</if>
			<if test="storageLocationId != null">
				STORAGE_LOCATION_ID,
			</if>
			<if test="storageRackId != null">
				STORAGE_RACK_ID,
			</if>
			<if test="batchNumber != null">
				BATCH_NUMBER,
			</if>
			<if test="expirationDate != null">
				EXPIRATION_DATE,
			</if>
			<if test="productDate != null">
				PRODUCT_DATE,
			</if>
			<if test="checkStatus != null">
				CHECK_STATUS,
			</if>
			<if test="checkStatusUser != null">
				CHECK_STATUS_USER,
			</if>
			<if test="checkStatusTime != null">
				CHECK_STATUS_TIME,
			</if>
			<if test="recheckStatus != null">
				RECHECK_STATUS,
			</if>
			<if test="recheckStatusUser != null">
				RECHECK_STATUS_USER,
			</if>
			<if test="recheckStatusTime != null">
				RECHECK_STATUS_TIME,
			</if>
			<if test="comments != null">
				COMMENTS,
			</if>
			<if test="addTime != null">
				ADD_TIME,
			</if>
			<if test="creator != null">
				CREATOR,
			</if>
			<if test="modTime != null">
				MOD_TIME,
			</if>
			<if test="updater != null">
				UPDATER,
			</if>
			<if test="isEnable != null">
				IS_ENABLE,
			</if>
			<if test="warehousePickingDetailId != null">
				WAREHOUSE_PICKING_DETAIL_ID,
			</if>
			<if test="logicalWarehouseId != null">
				LOGICAL_WAREHOUSE_ID,
			</if>
			<if test="vedengBatchNumer != null">
				VEDENG_BATCH_NUMER,
			</if>
			<if test="lastStockNum != null">
				LAST_STOCK_NUM,
			</if>
			<if test="isUse != null">
				IS_USE,
			</if>
			<if test="costPrice != null">
				COST_PRICE,
			</if>
			<if test="sterilizationBatchNo != null">
				STERILZATION_BATCH_NUMBER,
			</if>
			<if test="logType != null">
				LOG_TYPE,
			</if>
			<if test="tagSources != null">
				TAG_SOURCES,
			</if>
			<if test="newCostPrice != null">
				NEW_COST_PRICE,
			</if>
			<if test="dedicatedBuyorderNo != null">
				DEDICATED_BUYORDER_NO,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="barcodeId != null">
				#{barcodeId,jdbcType=INTEGER},
			</if>
			<if test="companyId != null">
				#{companyId,jdbcType=INTEGER},
			</if>
			<if test="operateType != null">
				#{operateType,jdbcType=BIT},
			</if>
			<if test="relatedId != null">
				#{relatedId,jdbcType=INTEGER},
			</if>
			<if test="goodsId != null">
				#{goodsId,jdbcType=INTEGER},
			</if>
			<if test="barcodeFactory != null">
				#{barcodeFactory,jdbcType=VARCHAR},
			</if>
			<if test="num != null">
				#{num,jdbcType=INTEGER},
			</if>
			<if test="warehouseId != null">
				#{warehouseId,jdbcType=INTEGER},
			</if>
			<if test="storageRoomId != null">
				#{storageRoomId,jdbcType=INTEGER},
			</if>
			<if test="storageAreaId != null">
				#{storageAreaId,jdbcType=INTEGER},
			</if>
			<if test="storageLocationId != null">
				#{storageLocationId,jdbcType=INTEGER},
			</if>
			<if test="storageRackId != null">
				#{storageRackId,jdbcType=INTEGER},
			</if>
			<if test="batchNumber != null">
				#{batchNumber,jdbcType=VARCHAR},
			</if>
			<if test="expirationDate != null">
				#{expirationDate,jdbcType=BIGINT},
			</if>
			<if test="productDate != null">
				#{productDate,jdbcType=BIGINT},
			</if>
			<if test="checkStatus != null">
				#{checkStatus,jdbcType=BIT},
			</if>
			<if test="checkStatusUser != null">
				#{checkStatusUser,jdbcType=INTEGER},
			</if>
			<if test="checkStatusTime != null">
				#{checkStatusTime,jdbcType=BIGINT},
			</if>
			<if test="recheckStatus != null">
				#{recheckStatus,jdbcType=BIT},
			</if>
			<if test="recheckStatusUser != null">
				#{recheckStatusUser,jdbcType=INTEGER},
			</if>
			<if test="recheckStatusTime != null">
				#{recheckStatusTime,jdbcType=BIGINT},
			</if>
			<if test="comments != null">
				#{comments,jdbcType=VARCHAR},
			</if>
			<if test="addTime != null">
				#{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				#{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				#{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				#{updater,jdbcType=INTEGER},
			</if>
			<if test="isEnable != null">
				#{isEnable,jdbcType=BIT},
			</if>
			<if test="warehousePickingDetailId != null">
				#{warehousePickingDetailId,jdbcType=INTEGER},
			</if>
			<if test="logicalWarehouseId != null">
				#{logicalWarehouseId,jdbcType=INTEGER},
			</if>
			<if test="vedengBatchNumer != null">
				#{vedengBatchNumer,jdbcType=VARCHAR},
			</if>
			<if test="lastStockNum != null">
				#{lastStockNum,jdbcType=INTEGER},
			</if>
			<if test="isUse != null">
				#{isUse,jdbcType=INTEGER},
			</if>
			<if test="costPrice != null">
				#{costPrice,jdbcType=DECIMAL},
			</if>
			<if test="sterilizationBatchNo != null">
				#{sterilizationBatchNo,jdbcType=VARCHAR},
			</if>
			<if test="logType != null">
				#{logType,jdbcType=VARCHAR},
			</if>
			<if test="tagSources != null">
				#{tagSources,jdbcType=VARCHAR},
			</if>
			<if test="newCostPrice != null">
				#{newCostPrice,jdbcType=DECIMAL},
			</if>
			<if test="dedicatedBuyorderNo != null">
				#{dedicatedBuyorderNo,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>

	<select id="getAvailableLogicalGoods" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
			*
		FROM
			T_WAREHOUSE_GOODS_OPERATE_LOG
		<where>
			1 = 1
			<if test="logicalWarehouseId != null">
				AND LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER}
			</if>
			<if test="relatedId != null">
				AND RELATED_ID = #{relatedId,jdbcType=INTEGER}
			</if>
			AND VEDENG_BATCH_NUMER = #{vedengBatchNumer,jdbcType=VARCHAR}
			AND GOODS_ID = #{goodsId,jdbcType=INTEGER}
			AND LOG_TYPE = 0
			AND IS_ENABLE = 1
			AND IS_USE = 0
			AND LAST_STOCK_NUM>0
		</where>
	</select>

    <update id="updateAvailableLogicalGood">
		UPDATE T_WAREHOUSE_GOODS_OPERATE_LOG
		<set>
			<if test="num != null">
				NUM = #{num,jdbcType=INTEGER},
			</if>
			<if test="lastStockNum != null">
				LAST_STOCK_NUM = #{lastStockNum,jdbcType=INTEGER},
			</if>
			<if test="isUse != null">
				IS_USE = #{isUse,jdbcType=INTEGER},
			</if>
			<if test="modTime != null and modTime != ''">
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="comments != null and comments != ''">
				COMMENTS = #{comments,jdbcType=VARCHAR},
			</if>
			<if test="isEnable != null">
				IS_ENABLE = #{isEnable,jdbcType=INTEGER},
			</if>
			<if test="tagSources != null">
				TAG_SOURCES  = #{tagSources,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE
			WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
	</update>

	<select id="getInstockNumByRelatedId" resultType="java.lang.Integer">
		SELECT
        COALESCE(SUM(A.LAST_STOCK_NUM),
        0)
    FROM
        T_WAREHOUSE_GOODS_OPERATE_LOG  A
    WHERE
        A.IS_ENABLE = 1
        and A.OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
       	AND A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
		AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
        AND A.IS_USE = 0
	</select>

	<select id="getLogincalStockOrderInfo" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
	A.GOODS_ID,
	A.RELATED_ID,
	A.OPERATE_TYPE,
	A.LOGICAL_WAREHOUSE_ID,
	SUM( A.LAST_STOCK_NUM ) AS NUM,
	C.BUYORDER_NO,
	C.BUYORDER_ID ,
	E.AFTER_SALES_NO ,
	E.AFTER_SALES_ID,
	FF.id LEND_OUT_ID ,
	FF.order_no LEND_OUT_NO ,
	C.TRADER_NAME ,
	FF.borrow_trader_name AS buytraderName,
	SY.TITLE,
	IA.INVENTORY_ADJUSTMENT_ID,
	IA.INVENTORY_ADJUSTMENT_NO,
	WIO.ORDER_NO,
	WIO.WMS_INPUT_ORDER_ID
FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG A
	LEFT JOIN T_BUYORDER_GOODS B ON A.RELATED_ID = B.BUYORDER_GOODS_ID
	AND A.OPERATE_TYPE = 1
	LEFT JOIN T_BUYORDER C ON B.BUYORDER_ID = C.BUYORDER_ID
	AND B.BUYORDER_GOODS_ID IS NOT NULL
	LEFT JOIN T_AFTER_SALES_GOODS D ON A.RELATED_ID = D.AFTER_SALES_GOODS_ID
	AND A.OPERATE_TYPE IN ( 3, 5, 8 )
	LEFT JOIN T_AFTER_SALES E ON D.AFTER_SALES_ID = E.AFTER_SALES_ID AND D.AFTER_SALES_GOODS_ID IS NOT NULL
	LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS F ON A.RELATED_ID=F.id AND A.OPERATE_TYPE = 9
	LEFT JOIN T_WMS_OUTPUT_ORDER FF ON FF.id=F.wms_output_order_id
	LEFT JOIN T_SYS_OPTION_DEFINITION SY ON SY.SYS_OPTION_DEFINITION_ID=A.LOGICAL_WAREHOUSE_ID
	LEFT JOIN T_WMS_INVENTORY_ADJUSTMENT_DETAIL IAD ON IAD.INVENTORY_ADJUSTMENT_DETAIL_ID = A.RELATED_ID AND A.OPERATE_TYPE=11
	LEFT JOIN T_WMS_INVENTORY_ADJUSTMENT IA ON IA.INVENTORY_ADJUSTMENT_ID=IAD.INVENTORY_ADJUSTMENT_ID
	LEFT JOIN T_WMS_INPUT_ORDER_GOODS IOG ON IOG.WMS_INPUT_ORDER_GOODS_ID=A.RELATED_ID AND A.OPERATE_TYPE=12
	LEFT JOIN T_WMS_INPUT_ORDER WIO ON IOG.WMS_INPUT_ORDER_ID=WIO.WMS_INPUT_ORDER_ID
WHERE
	A.GOODS_ID = #{goodsId}
	AND A.IS_USE = 0
	AND A.IS_ENABLE = 1
	AND A.LOG_TYPE =0
	AND A.LAST_STOCK_NUM > 0
	AND A.VEDENG_BATCH_NUMER !=''
GROUP BY
	A.GOODS_ID,
	A.OPERATE_TYPE,
	A.RELATED_ID,
	A.LOGICAL_WAREHOUSE_ID

	</select>
	<select id="getInLogByLogicalIdAndVBatchNumber"
			resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
	SELECT
	*
	FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG A
	WHERE
	A.LOG_TYPE =0
	AND A.IS_ENABLE = 1
	AND A.IS_USE = 0
	AND A.LAST_STOCK_NUM > 0
	AND A.VEDENG_BATCH_NUMER = #{vedengBatchNumer,jdbcType=VARCHAR}
	AND A.LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER}
	</select>
	<update id="updateOutIsUseAndLastStockNumById">
	UPDATE
	T_WAREHOUSE_GOODS_OPERATE_LOG
	SET
	LAST_STOCK_NUM = LAST_STOCK_NUM + #{num,jdbcType=INTEGER} ,
	IS_USE = (CASE LAST_STOCK_NUM WHEN 0 THEN 1 ELSE 0 END ),
	MOD_TIME = UNIX_TIMESTAMP(now())*1000
	WHERE
	WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
	AND LOG_TYPE =0
	AND IS_ENABLE = 1

	</update>

	<select id="getWarehouseGoodsOperateLogByVedengBatchNoAndSku"
			resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
			WAREHOUSE_GOODS_OPERATE_LOG_ID,
			BARCODE_ID,
			GOODS_ID,
			COST_PRICE,
			NUM,
			VEDENG_BATCH_NUMER
		FROM
			`T_WAREHOUSE_GOODS_OPERATE_LOG`
		WHERE
			GOODS_ID = #{goodsId,jdbcType=INTEGER}
			AND LOG_TYPE =0
			AND COST_PRICE != 0
			AND IS_ENABLE = 1
			AND NUM > 0
	</select>

	<select id="getSpecialLocationLog" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
			GOODS_ID,
			OPERATE_TYPE,
			RELATED_ID,
			sum(NUM) as num
		FROM
			T_WAREHOUSE_GOODS_OPERATE_LOG
		<where>
			STORAGE_LOCATION_ID = '1325'
			AND STORAGE_RACK_ID = '284'
			AND STORAGE_AREA_ID = '15'
			AND STORAGE_ROOM_ID = '19'
			AND WAREHOUSE_ID = '9'
			<if test="goodsId != null">
				and GOODS_ID = #{goodsId,jdbcType=INTEGER}
			</if>
			AND OPERATE_TYPE IN( 1,3,5,8,9 )
			and IS_ENABLE = 1
			and VEDENG_BATCH_NUMER = ''
		</where>
		GROUP BY OPERATE_TYPE,RELATED_ID
		ORDER BY OPERATE_TYPE,RELATED_ID
	</select>

	<update id="updateByPrimaryKeySelective" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog" >
		update T_WAREHOUSE_GOODS_OPERATE_LOG
		<set >
			<if test="barcodeId != null" >
				BARCODE_ID = #{barcodeId,jdbcType=INTEGER},
			</if>
			<if test="productDate != null" >
				PRODUCT_DATE = #{productDate,jdbcType=BIGINT},
			</if>
			<if test="expirationDate != null" >
				EXPIRATION_DATE = #{expirationDate,jdbcType=BIGINT},
			</if>
			<if test="checkStatusTime != null" >
				CHECK_STATUS_TIME = #{checkStatusTime,jdbcType=BIGINT},
			</if>
			<if test="barcodeFactory != null" >
				BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
			</if>
			<if test="sterilizationBatchNo != null" >
				STERILZATION_BATCH_NUMBER = #{sterilizationBatchNo,jdbcType=VARCHAR},
			</if>
			<if test="vedengBatchNumer != null" >
				VEDENG_BATCH_NUMER = #{vedengBatchNumer,jdbcType=VARCHAR},
			</if>
			<if test="modTime != null" >
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="logicalWarehouseId != null">
				LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER},
			</if>
		</set>
		where WAREHOUSE_GOODS_OPERATE_LOG_ID = #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
	</update>

	<select id="getWarehouseoutLogByTypeAndRelateId" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
			*
		FROM
			T_WAREHOUSE_GOODS_OPERATE_LOG
		where
			STORAGE_LOCATION_ID = '1325'
			AND STORAGE_RACK_ID = '284'
			AND STORAGE_AREA_ID = '15'
			AND STORAGE_ROOM_ID = '19'
			AND WAREHOUSE_ID = '9'
			and GOODS_ID = #{goodsId,jdbcType=INTEGER}
			AND OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
			AND RELATED_ID = #{relatedId,jdbcType=INTEGER}
			and IS_ENABLE = 1
			and VEDENG_BATCH_NUMER = ''
	</select>

	<select id="getInputWarehouseLog" parameterType="java.util.List" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
			*
		FROM
			T_WAREHOUSE_GOODS_OPERATE_LOG
		where
			OPERATE_TYPE = 1
			AND RELATED_ID in
			<foreach collection="list" item="buyorderGoodId" open="(" close=")" separator=",">
				#{buyorderGoodId,jdbcType=INTEGER}
			</foreach>
	</select>
	<select id="getInLogByInfo" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
		*
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG A
		WHERE
		A.IS_USE = 0
		AND A.OPERATE_TYPE IN ( 1, 3, 5, 8, 9 )
		AND A.IS_ENABLE = 1
		AND A.ADD_TIME BETWEEN #{addTime,jdbcType=BIGINT} AND #{addTime,jdbcType=BIGINT}+86400000
		AND A.GOODS_ID = #{goodsId,jdbcType=INTEGER}
		<if test="expirationDate != null">
			AND A.EXPIRATION_DATE = #{expirationDate,jdbcType=BIGINT}
		</if>
		<if test="productDate != null">
			AND A.PRODUCT_DATE = #{productDate,jdbcType=BIGINT}
		</if>

		<if test="batchNumber != null">
			AND	A.BATCH_NUMBER = #{batchNumber}
		</if>
		<if test="batchNumber == null">
			AND	(A.BATCH_NUMBER IS  NULL OR  A.BATCH_NUMBER = '')
		</if>
		LIMIT #{num}
	</select>
	<select id="getExpressWMSWlogIds" resultType="java.lang.Integer">
		SELECT
		B.WAREHOUSE_GOODS_OPERATE_LOG_ID
	FROM
		T_EXPRESS_DETAIL A
		LEFT JOIN V_E_W_EXPRESS_WAREHOUSE B ON A.EXPRESS_DETAIL_ID = B.EXPRESS_DETAIL_ID
	WHERE
		 A.EXPRESS_ID=#{expressId,jdbcType=INTEGER}
	</select>
	<select id="getAvailAbleStockNumByGoodsId"
			resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT * FROM T_WAREHOUSE_GOODS_OPERATE_LOG A WHERE A.LOG_TYPE=0 AND A.GOODS_ID=#{goodsId} AND A.IS_ENABLE=1 AND A.IS_USE=0 AND A.VEDENG_BATCH_NUMER !=''
	</select>
	<select id="getWmsInputOrderLogListByOrderId"
			resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
	SELECT
	C.*,
	RE.REGISTRATION_NUMBER,
	RE.REGISTRATION_NUMBER_ID,
	S.SHOW_NAME AS goodsName,
	S.MODEL,
	UN.UNIT_NAME,
	BR.BRAND_NAME,
	S.SKU_NO AS sku
	FROM
	T_WMS_INPUT_ORDER A
	LEFT JOIN T_WMS_INPUT_ORDER_GOODS B ON A.WMS_INPUT_ORDER_ID = B.WMS_INPUT_ORDER_ID
	LEFT JOIN T_WAREHOUSE_GOODS_OPERATE_LOG C ON B.WMS_INPUT_ORDER_GOODS_ID=C.RELATED_ID
	LEFT JOIN V_CORE_SKU S ON B.GOODS_ID=S.SKU_ID
	LEFT JOIN V_CORE_SPU P ON S.SPU_ID=P.SPU_ID
	LEFT JOIN T_UNIT UN ON UN.UNIT_ID=S.UNIT_ID
	LEFT JOIN T_BRAND BR ON BR.BRAND_ID=P.BRAND_ID
	LEFT JOIN T_FIRST_ENGAGE FI ON P.FIRST_ENGAGE_ID=FI.FIRST_ENGAGE_ID
	LEFT JOIN T_REGISTRATION_NUMBER RE ON FI.REGISTRATION_NUMBER_ID=RE.REGISTRATION_NUMBER_ID
	WHERE C.IS_ENABLE=1
	AND C.OPERATE_TYPE = #{logOperateType,jdbcType=INTEGER}
	AND A.WMS_INPUT_ORDER_ID= #{wmsInputOrderId,jdbcType=INTEGER}
	</select>
    <select id="getNullbarcodeIdInlog" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
	SELECT
	A.*
	FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG A
	WHERE
	A.OPERATE_TYPE = 11
	AND A.BARCODE_ID IS NULL
	AND A.VEDENG_BATCH_NUMER != ''
	</select>
	<select id="getNullbarcodeIdOutlog" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
	SELECT
	*
	FROM
	T_WAREHOUSE_GOODS_OPERATE_LOG A
	WHERE
	A.LOG_TYPE = 1
	AND A.IS_ENABLE = 1
	AND A.GOODS_ID=#{goodsId,jdbcType=INTEGER}
	AND A.VEDENG_BATCH_NUMER= #{vedengBatchNumer,jdbcType=VARCHAR}
	AND A.BARCODE_ID IS NULL
	</select>


	<select id="getHaveChangeSkuNos" resultType="java.lang.Integer">
		SELECT
            DISTINCT GOODS_ID
        FROM
            T_WAREHOUSE_GOODS_OPERATE_LOG K
        WHERE

			WAREHOUSE_GOODS_OPERATE_LOG_ID > 7308856
		  AND	K.MOD_TIME >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 5 MINUTE)) * 1000


	</select>
	<select id="getWmsOutputOrderLogListByOrderId"
			resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
	SELECT
	C.*,
	C.STERILZATION_BATCH_NUMBER sterilizationBatchNo,
	RE.REGISTRATION_NUMBER,
	RE.REGISTRATION_NUMBER_ID,
	S.SHOW_NAME AS goodsName,
	S.SKU_ID AS goodsId,
	S.MODEL,
	UN.UNIT_NAME,
	BR.BRAND_NAME,
	S.SKU_NO AS sku
	FROM
	T_WMS_OUTPUT_ORDER A
	LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS B ON A.ID = B.wms_output_order_id
	LEFT JOIN T_WAREHOUSE_GOODS_OPERATE_LOG C ON B.ID=C.RELATED_ID
	LEFT JOIN V_CORE_SKU S ON B.sku_no=S.SKU_NO
	LEFT JOIN V_CORE_SPU P ON S.SPU_ID=P.SPU_ID
	LEFT JOIN T_UNIT UN ON UN.UNIT_ID=S.BASE_UNIT_ID
	LEFT JOIN T_BRAND BR ON BR.BRAND_ID=P.BRAND_ID
	LEFT JOIN T_FIRST_ENGAGE FI ON P.FIRST_ENGAGE_ID=FI.FIRST_ENGAGE_ID
	LEFT JOIN T_REGISTRATION_NUMBER RE ON FI.REGISTRATION_NUMBER_ID=RE.REGISTRATION_NUMBER_ID
	WHERE C.IS_ENABLE=1
	AND C.OPERATE_TYPE = #{logOperateType,jdbcType=INTEGER}
	AND A.ID= #{wmsoutputOrderId,jdbcType=INTEGER}
	</select>
	<select id="getWarehouseZKlogLimit" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
			A.WAREHOUSE_GOODS_OPERATE_LOG_ID,A.OPERATE_TYPE,A.RELATED_ID,A.GOODS_ID,A.BARCODE_ID,A.NUM,A.LOG_TYPE
		FROM
			T_WAREHOUSE_GOODS_OPERATE_LOG A
		WHERE
			A.IS_ENABLE = 1
		  AND (A.COST_PRICE = 0 OR A.NEW_COST_PRICE = 0)
		  AND A.OPERATE_TYPE IN ( #{operaType,jdbcType=INTEGER} )
		  AND A.COMPANY_ID = 1
		  AND A.BARCODE_ID != 0
	AND A.GOODS_ID NOT IN ( SELECT COMMENTS FROM T_SYS_OPTION_DEFINITION WHERE PARENT_ID = 693 )
	AND A.LOG_TYPE = 0
	AND A.NUM > 0
	AND A.WAREHOUSE_GOODS_OPERATE_LOG_ID > #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
	ORDER BY A.WAREHOUSE_GOODS_OPERATE_LOG_ID ASC
	LIMIT #{limit}

	</select>
	<select id="getWarehouseHistoryOutList" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
		A.WAREHOUSE_GOODS_OPERATE_LOG_ID,A.OPERATE_TYPE,A.RELATED_ID,A.GOODS_ID,A.BARCODE_ID,A.NUM,A.LOG_TYPE
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG A
		WHERE
			A.IS_ENABLE = 1
		AND (A.COST_PRICE = 0 AND A.NEW_COST_PRICE = 0)
		AND A.COMPANY_ID = 1
		AND A.BARCODE_ID != 0
		AND A.GOODS_ID NOT IN ( SELECT COMMENTS FROM T_SYS_OPTION_DEFINITION WHERE PARENT_ID = 693 )
		AND A.LOG_TYPE = 1
		AND A.NUM &lt; 0
		AND A.IS_ENABLE =1
		AND A.WAREHOUSE_GOODS_OPERATE_LOG_ID > #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
		ORDER BY A.WAREHOUSE_GOODS_OPERATE_LOG_ID ASC
		LIMIT #{limit}
	</select>
	<select id="getWarehouseInlog" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
			A.WAREHOUSE_GOODS_OPERATE_LOG_ID,A.OPERATE_TYPE,A.RELATED_ID,A.GOODS_ID ,A.BARCODE_ID,A.COST_PRICE,A.NEW_COST_PRICE
		FROM
			T_WAREHOUSE_GOODS_OPERATE_LOG A
		WHERE
			A.IS_ENABLE = 1
		  AND A.COMPANY_ID = 1
		  AND (A.COST_PRICE > 0 OR A.NEW_COST_PRICE > 0)
		  AND A.BARCODE_ID = #{barcodeId,jdbcType=INTEGER}
		  AND A.LOG_TYPE = 0
		  AND A.NUM > 0
	</select>

	<select id="getOperateTypeById" resultType="java.lang.Integer">
        SELECT OPERATE_TYPE FROM T_WAREHOUSE_GOODS_OPERATE_LOG WHERE WAREHOUSE_GOODS_OPERATE_LOG_ID = #{logId,jdbcType=INTEGER}
    </select>

	<!-- 第一次出库操作人+时间 -->
	<select id="getWarehouseGoodsOperateOutLog"
			resultMap="VoResultMap"
			parameterType="com.vedeng.order.model.Saleorder">
		SELECT
		a.CREATOR,a.ADD_TIME
		from
		T_WAREHOUSE_GOODS_OPERATE_LOG a
		LEFT JOIN T_SALEORDER_GOODS b ON
		a.RELATED_ID = b.SALEORDER_GOODS_ID
		where b.SALEORDER_ID =
		#{saleorderId,jdbcType=INTEGER}
		and a.OPERATE_TYPE=2
		ORDER BY a.ADD_TIME
		limit 1
	</select>

	<!-- 第一次入库操作人+时间 -->
	<select id="getWarehouseGoodsOperateInLog"
			resultMap="VoResultMap"
			parameterType="com.vedeng.order.model.Buyorder">
		SELECT
		a.CREATOR,a.ADD_TIME
		from
		T_WAREHOUSE_GOODS_OPERATE_LOG a
		LEFT JOIN T_BUYORDER_GOODS b ON
		a.RELATED_ID = b.BUYORDER_GOODS_ID
		where b.BUYORDER_ID =
		#{buyorderId,jdbcType=INTEGER}
		and a.OPERATE_TYPE=1
		ORDER BY a.ADD_TIME
		limit 1
	</select>

	<!-- 根据id查询出库信息 -->
	<select id="getwlById" resultMap="VoResultMap" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
		SUM(a.NUM) NUM,
		a.WAREHOUSE_GOODS_OPERATE_LOG_ID,
		a.OPERATE_TYPE,
		a.RELATED_ID,
		a.ADD_TIME,
		a.CREATOR,
		a.EXPIRATION_DATE,
		a.PRODUCT_DATE,
		a.BATCH_NUMBER,
		a.BARCODE_FACTORY,
		n.SKU_NO SKU,
		n.MATERIAL_CODE,
		MM.PRODUCT_COMPANY_LICENCE PRODUCTION_LICENSE,
		MM.RECORD_CERTIFICATE_LICENCE recordCertificateLicence,
		CONCAT(
		e.WAREHOUSE_NAME,
		' ',
		IFNULL(f.STORAGE_ROOM_NAME, ''),
		' ',
		IFNULL(g.STORAGE_AREA_NAME, ''),
		' ',
		IFNULL(h.STORAGE_RACK_NAME, ''),
		' ',
		IFNULL(i.STORAGE_LOCATION_NAME, '')
		) AS STORAGEADDRESS,
		RR.REGISTRATION_NUMBER `RECORD_NUMBER` ,
		RR.MANAGE_CATEGORY_LEVEL manageCategoryLevel,
		<!-- b.`MANUFACTURER`, -->
		j.TEMPERATURE ,
		j.REGISTRATION_NUMBER_ID,
		j.`CONDITION_ONE` ,
		k.`TITLE` ,
		n.`STORAGE_CONDITION_ONE`,
		p.`PRICE` ,
		p.REAL_PRICE,
		p.MAX_SKU_REFUND_AMOUNT,
		p.NUM AS SALESNUM,
		<choose>
			<when test="operateType != null and operateType== 4">
				q.IS_ACTION_GOODS,
			</when>
			<otherwise>
				p.IS_ACTION_GOODS,
			</otherwise>
		</choose>
		n.STORAGE_CONDITION_ONE_LOWER_VALUE,
		n.STORAGE_CONDITION_ONE_UPPER_VALUE,
		n.STORAGE_CONDITION_ONE,
		a.GOODS_ID,
		o.SPU_TYPE,
		n.SUPPLY_MODEL,
		<!--  q.`ORDER_TYPE` -->
		b.PRODUCT_CHINESE_NAME,
		b.GOODS_NAME,
		b.BRAND_NAME,
		b.MODEL,
		b.SPEC,
		b.UNIT_NAME,
		b.REGISTRATION_NUMBER,
		b.MANUFACTURER_NAME MANUFACTURER,
		b.PRODUCT_COMPANY_LICENCE
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG a
		LEFT JOIN T_SALEORDER_GOODS b ON a.RELATED_ID = b.SALEORDER_GOODS_ID and a.OPERATE_TYPE = 2
		<!--	LEFT
            JOIN T_GOODS b
            ON a.GOODS_ID = b.GOODS_ID -->
		LEFT JOIN
		T_WAREHOUSE e
		ON e.WAREHOUSE_ID = a.WAREHOUSE_ID
		LEFT JOIN
		T_STORAGE_ROOM f
		ON f.STORAGE_ROOM_ID = a.STORAGE_ROOM_ID
		LEFT JOIN
		T_STORAGE_AREA g
		ON g.STORAGE_AREA_ID = a.STORAGE_AREA_ID
		LEFT JOIN
		T_STORAGE_RACK h
		ON h.STORAGE_RACK_ID = a.STORAGE_RACK_ID
		LEFT JOIN
		T_STORAGE_LOCATION i
		ON i.STORAGE_LOCATION_ID = a.STORAGE_LOCATION_ID
		<!--
       LEFT JOIN
       T_REGISTRATION_NUMBER l
       ON
       b.`PRODUCTION_LICENSE` =
       l.`REGISTRATION_NUMBER`
       LEFT JOIN
       T_PRODUCT_COMPANY m
       ON
       l.`PRODUCT_COMPANY_ID` = m.`PRODUCT_COMPANY_ID`
       -->
		LEFT JOIN `V_CORE_SKU` n ON a.GOODS_ID = n.`SKU_ID`
		LEFT JOIN `V_CORE_SPU` o ON n.`SPU_ID` = o.`SPU_ID`
		LEFT JOIN T_UNIT c ON n.BASE_UNIT_ID   = c.UNIT_ID
		LEFT JOIN T_BRAND d ON o.BRAND_ID = d.BRAND_ID
		LEFT JOIN T_FIRST_ENGAGE j ON o.`FIRST_ENGAGE_ID` = j.`FIRST_ENGAGE_ID`
		LEFT JOIN T_REGISTRATION_NUMBER RR ON RR.REGISTRATION_NUMBER_ID=j.REGISTRATION_NUMBER_ID
		LEFT JOIN T_MANUFACTURER MM ON MM.MANUFACTURER_ID=RR.MANUFACTURER_ID
		LEFT JOIN T_SYS_OPTION_DEFINITION k ON RR.`MANAGE_CATEGORY_LEVEL` = 	k.`SYS_OPTION_DEFINITION_ID`

		LEFT JOIN `T_SALEORDER_GOODS` p ON  p.`SALEORDER_GOODS_ID`=a.`RELATED_ID`
		<if test="operateType != null and operateType== 4">
			LEFT JOIN T_AFTER_SALES_GOODS q ON q.AFTER_SALES_GOODS_ID = a.RELATED_ID
		</if>
		WHERE 1 = 1
		<!-- FIND_IN_SET(a.WAREHOUSE_GOODS_OPERATE_LOG_ID, #{creatorNm} ) -->
		<!-- a.WAREHOUSE_GOODS_OPERATE_LOG_ID IN ( #{idList} ) -->
		<if test="goodsFlag != null and goodsFlag== 1">
			o.SPU_TYPE = 316
		</if>
		<if test="goodsFlag != null and goodsFlag== 2">
			o.SPU_TYPE <![CDATA[ <> ]]> 316
		</if>
		AND
		a.WAREHOUSE_GOODS_OPERATE_LOG_ID IN
		<foreach item="wId" index="index" collection="idList" open="("
				 separator="," close=")">
			#{wId}
		</foreach>
		<choose>
			<when test="operateType != null and operateType== 4">
				GROUP BY q.IS_ACTION_GOODS
			</when>
			<otherwise>
				GROUP BY p.IS_ACTION_GOODS
			</otherwise>
		</choose>

		<if test="ywType ==0">
			, n.SKU_NO
		</if>
		<if test="ywType ==1">
			, a.EXPIRATION_DATE,n.SKU_NO,a.BATCH_NUMBER,a.PRODUCT_DATE,a.BARCODE_FACTORY,a.RELATED_ID
		</if>
		<if test="ywType ==2">
			, a.EXPIRATION_DATE,n.SKU_NO,a.BATCH_NUMBER,a.PRODUCT_DATE,a.BARCODE_FACTORY,a.RELATED_ID
		</if>
		<if test="ywType ==3">
			, a.WAREHOUSE_GOODS_OPERATE_LOG_ID
		</if>
		<!-- spu类型为设备的会被排到前面 -->
		ORDER BY o.SPU_TYPE = 316
	</select>

	<!-- 根据出入库id获取当前采购单价或销售单�?-->
	<select id="getPrintPriceById" resultMap="VoResultMap"
			parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
		<if test="operateType != null">
			b.PRICE,
		</if>
		a.*
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG a
		<!-- 入库 -->
		<if test="operateType == 1">
			LEFT JOIN T_BUYORDER_GOODS b ON b.BUYORDER_GOODS_ID =
			a.RELATED_ID
		</if>
		<!-- 出库 -->
		<if test="operateType == 2">
			LEFT JOIN T_SALEORDER_GOODS b ON b.SALEORDER_GOODS_ID =
			a.RELATED_ID
		</if>
		<!-- 销售业务入�?3�?5退 销售业务出�?4�?-->
		<if
				test="operateType == 3 or operateType == 4 or operateType == 5 ">
			LEFT JOIN T_AFTER_SALES_GOODS c ON c.AFTER_SALES_GOODS_ID =
			a.RELATED_ID
			LEFT JOIN T_SALEORDER_GOODS b ON b.SALEORDER_GOODS_ID =
			c.ORDER_DETAIL_ID
		</if>
		<!-- 采购业务出库 6退/7�?采购业务入库 8�?-->
		<if
				test="operateType == 6 or operateType == 7 or operateType == 8 ">
			LEFT JOIN T_AFTER_SALES_GOODS c ON c.AFTER_SALES_GOODS_ID =
			a.RELATED_ID
			LEFT JOIN T_BUYORDER_GOODS b ON b.BUYORDER_GOODS_ID =
			c.ORDER_DETAIL_ID
		</if>
		WHERE
		1 = 1
		AND a.WAREHOUSE_GOODS_OPERATE_LOG_ID =
		#{warehouseGoodsOperateLogId,
		jdbcType=INTEGER}

	</select>

	<select id="getInputOrderLogListByOrderIdAndGoodsId" parameterType="com.vedeng.flash.dto.PrepareStockCommand" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT
			T1.RELATED_ID,
			T1.NUM,
			T1.EXPIRATION_DATE
		FROM
			T_WAREHOUSE_GOODS_OPERATE_LOG T1
		WHERE
			T1.GOODS_ID = #{skuId, jdbcType=INTEGER}
		  AND T1.OPERATE_TYPE = 1
		  AND T1.IS_ENABLE = 1
		  AND T1.RELATED_ID = #{buyorderId, jdbcType=INTEGER};
	</select>


	<select id="getAlreadyOutputNum" resultType="java.lang.Integer">
		SELECT
			COALESCE(SUM(T1.NUM),0)
		FROM
			T_WAREHOUSE_GOODS_OPERATE_LOG T1
		WHERE
			T1.GOODS_ID = #{skuId, jdbcType=INTEGER}
		  AND T1.OPERATE_TYPE = 2
		  AND T1.IS_ENABLE = 1
		  AND T1.RELATED_ID = #{saleorderGoodsId, jdbcType=INTEGER}
		  AND T1.DEDICATED_BUYORDER_NO = #{buyOrderNo, jdbcType=INTEGER}
	</select>
	<select id="getOutStockByBuyorderGoodsId" resultType="java.lang.Integer">
		SELECT
        COALESCE(SUM(B.NUM),
        0)
    FROM
        T_WAREHOUSE_GOODS_OPERATE_LOG  A
	LEFT JOIN T_WAREHOUSE_GOODS_OPERATE_LOG B ON A.BARCODE_ID=B.BARCODE_ID
    WHERE
        A.IS_ENABLE = 1
        and A.OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
       	AND A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
		AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
        AND A.IS_USE = 0
		AND B.OPERATE_TYPE IN (2,4,6,7,10);
	</select>
	<select id="getInStockByRelateIdOptType" resultType="java.lang.Integer">
		SELECT
        COALESCE(SUM(A.NUM),
        0)
    FROM
        T_WAREHOUSE_GOODS_OPERATE_LOG  A
         WHERE
        A.IS_ENABLE = 1
        and A.OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
       	AND A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
		AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
	</select>
    <select id="getWarehouseOutList" resultMap="VoResultMap"
		parameterType="com.vedeng.order.model.Saleorder">
		select
		<if test="bussinessType == 2">
			b.GOODS_NAME,
			b.MODEL,
			b.BRAND_NAME,
			b.UNIT_NAME,
		</if>
		<if test="bussinessType != 2">
			f.GOODS_NAME,
			g.BRAND_NAME,
			f.MODEL,
			h.UNIT_NAME,
		</if>
		d.BARCODE,
		c.MATERIAL_CODE,
		k.SKU_NO as SKU,
		a.NUM ,
		a.ADD_TIME,
		a.WAREHOUSE_GOODS_OPERATE_LOG_ID,
		a.IS_ENABLE,
		a.COMMENTS,
		a.CREATOR,
		a.GOODS_ID,
		a.BARCODE_FACTORY,
		a.BATCH_NUMBER,
		ABS(a.NUM) as REAL_GOODS_NUM,
		a.VEDENG_BATCH_NUMER,
		a.STERILZATION_BATCH_NUMBER,
		n.REGISTRATION_NUMBER_ID,
		n.REGISTRATION_NUMBER,
		a.EXPIRATION_DATE,
		a.CHECK_STATUS_TIME,
		a.PRODUCT_DATE,
		z.FIRST_ENGAGE_ID,
		a.RELATED_ID
		from T_WAREHOUSE_GOODS_OPERATE_LOG a
		LEFT JOIN
		<if test="bussinessType == 2">
			T_SALEORDER_GOODS b on b.SALEORDER_GOODS_ID = a.RELATED_ID
			AND b.IS_DELETE =0
		</if>
		<if test="bussinessType != 2">
			T_AFTER_SALES_GOODS b on b.AFTER_SALES_GOODS_ID =
			a.RELATED_ID
			AND b.GOODS_TYPE =0
			INNER JOIN T_GOODS f ON b.GOODS_ID =
			f.GOODS_ID
			INNER JOIN T_BRAND g ON f.BRAND_ID = g.BRAND_ID
			INNER JOIN
			T_UNIT h ON f.UNIT_ID = h.UNIT_ID
		</if>
		LEFT JOIN T_GOODS c on b.GOODS_ID = c.GOODS_ID
		LEFT JOIN T_BARCODE d ON
		a.BARCODE_ID = d.BARCODE_ID
		AND d.IS_ENABLE = 1
		left join V_CORE_SKU k on a.GOODS_ID=k.SKU_ID
		left join V_CORE_SPU p on k.SPU_ID=p.SPU_ID
		left join T_FIRST_ENGAGE z on p.FIRST_ENGAGE_ID=z.FIRST_ENGAGE_ID and z.IS_DELETED=0
		left join T_REGISTRATION_NUMBER n on z.REGISTRATION_NUMBER_ID=n.REGISTRATION_NUMBER_ID
		where 1=1
		<if test="bussinessType != 2">
			and b.AFTER_SALES_ID = #{bussinessId,jdbcType=INTEGER}
		</if>
		<if test="bussinessType == 2">
			and b.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
		</if>
		and a.OPERATE_TYPE =#{bussinessType,jdbcType=INTEGER}
		and a.IS_ENABLE=1
		<if test="outGoodsTime != null and outGoodsTime != ''">
			AND a.ADD_TIME in (${outGoodsTime})
		</if>
		<if test="batchNoComments != null and batchNoComments != ''">
			AND a.COMMENTS like CONCAT('%',#{batchNoComments})
		</if>
		<if test="batchNoCommentArray != null and batchNoCommentArray != ''">
			AND a.COMMENTS REGEXP (${batchNoCommentArray})
		</if>

		ORDER BY a.WAREHOUSE_GOODS_OPERATE_LOG_ID DESC
	</select>

	<select id="getWGOlog" resultMap="VoResultMap" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		<if
				test="operateType != 3  and operateType != 5 and operateType != 8 and operateType!=9">
			select
			a.*,b.PRICE,b.GOODS_NAME,b.MODEL,b.BRAND_NAME,d.MATERIAL_CODE,b.UNIT_NAME,b.SKU,b.PRICE,j.BARCODE,
		</if>
		<if test="operateType == 3 or operateType == 5 or operateType == 8">
			select a.*,d.GOODS_NAME,
			d.SKU,d.GOODS_ID,l.BRAND_NAME,d.MODEL,d.MATERIAL_CODE,m.UNIT_NAME,j.BARCODE,
		</if>
		<if test="operateType==9">
			SELECT
			a.*,
			d.GOODS_NAME,
			d.MODEL,
			br.BRAND_NAME,
			d.MATERIAL_CODE,
			un.UNIT_NAME,
			d.SKU,
			j.BARCODE,
		</if>
		k.PRICE XSPRICE,
		k.PURCHASING_PRICE,
		<if test="operateType == 5">
		SUM(ABS(IFNULL(r.NUM,0))) NUMS,
		</if>
		<if test="operateType != 5">
			ABS(IFNULL(r.NUM,0)) NUMS,
		</if>
		concat(e.WAREHOUSE_NAME,
		' ',
		IFNULL(f.STORAGE_ROOM_NAME, ''),
		' ',
		IFNULL(g.STORAGE_AREA_NAME, ''),
		' ',
		IFNULL(h.STORAGE_RACK_NAME, ''),
		'
		',
		IFNULL(i.STORAGE_LOCATION_NAME, '')
		) AS STORAGEADDRESS
		<if test="operateType == 1">
			,c.BUYORDER_NO,c.TRADER_NAME as
			BUYTRADERNAME,c.BUYORDER_ID,   num.REGISTRATION_NUMBER, z.FIRST_ENGAGE_ID as firstEngageId
		</if>
		<if test="operateType == 2">
			,c.SALEORDER_NO,c.TAKE_TRADER_NAME as
			SALETRADERNAME,c.SALEORDER_ID
		</if>
		<if test="operateType == 3 or operateType == 5 or operateType == 8">
			,c.AFTER_SALES_NO,c.AFTER_SALES_ID , num.REGISTRATION_NUMBER, z.FIRST_ENGAGE_ID as firstEngageId
		</if>
		<if test="operateType == 9">
			,le.`LEND_OUT_ID`,
			le.`LEND_OUT_NO`,
			le.`TRADER_NAME` AS SALETRADERNAME , num.REGISTRATION_NUMBER, z.FIRST_ENGAGE_ID as firstEngageId
		</if>
		from T_WAREHOUSE_GOODS_OPERATE_LOG a
		<if test="operateType == 1">
			LEFT JOIN T_BUYORDER_GOODS b on a.RELATED_ID =
			b.BUYORDER_GOODS_ID
			AND b.IS_DELETE =0
			LEFT JOIN T_BUYORDER c on
			b.BUYORDER_ID = c.BUYORDER_ID
			AND c.VALID_STATUS =1
			LEFT JOIN T_GOODS d
			on b.GOODS_ID = d.GOODS_ID
			left join V_CORE_SKU sku on b.GOODS_ID=sku.SKU_ID
			left join V_CORE_SPU spu on sku.SPU_ID=spu.SPU_ID
			left join T_FIRST_ENGAGE z on spu.FIRST_ENGAGE_ID=z.FIRST_ENGAGE_ID and z.IS_DELETED=0
			left join T_REGISTRATION_NUMBER num on z.REGISTRATION_NUMBER_ID=num.REGISTRATION_NUMBER_ID
		</if>
		<if test="operateType == 2">
			LEFT JOIN T_SALEORDER_GOODS b on a.RELATED_ID =
			b.SALEORDER_GOODS_ID
			AND b.IS_DELETE =0
			LEFT JOIN T_SALEORDER c on
			b.SALEORDER_ID = c.SALEORDER_ID
			AND c.VALID_STATUS =1
			LEFT JOIN T_GOODS
			d on b.GOODS_ID = d.GOODS_ID
		</if>
		<if test="operateType == 3 or operateType == 5 or operateType == 8">
			LEFT JOIN T_AFTER_SALES_GOODS b on a.RELATED_ID =
			b.AFTER_SALES_GOODS_ID
			AND b.GOODS_TYPE =0
			LEFT JOIN T_AFTER_SALES c on
			b.AFTER_SALES_ID = c.AFTER_SALES_ID
			AND c.VALID_STATUS =1
			LEFT JOIN
			T_GOODS d on b.GOODS_ID = d.GOODS_ID
			INNER JOIN T_BRAND l ON
			l.BRAND_ID = d.BRAND_ID
			INNER JOIN T_UNIT m ON m.UNIT_ID = d.UNIT_ID
			left join V_CORE_SKU sku on b.GOODS_ID=sku.SKU_ID
			left join V_CORE_SPU spu on sku.SPU_ID=spu.SPU_ID
			left join T_FIRST_ENGAGE z on spu.FIRST_ENGAGE_ID=z.FIRST_ENGAGE_ID and z.IS_DELETED=0
			left join T_REGISTRATION_NUMBER num on z.REGISTRATION_NUMBER_ID=num.REGISTRATION_NUMBER_ID
		</if>
		<if test="operateType == 9">
			LEFT JOIN `T_LEND_OUT` le
			ON a.`RELATED_ID` = le.`LEND_OUT_ID`
			LEFT JOIN `T_GOODS` d
			ON le.`GOODS_ID` = d.`GOODS_ID`
			LEFT JOIN `T_BRAND` br
			ON d.`BRAND_ID` = br.`BRAND_ID`
			LEFT JOIN `T_UNIT` un ON d.UNIT_ID = d.UNIT_ID
			left join V_CORE_SKU sku on le.GOODS_ID=sku.SKU_ID
			left join V_CORE_SPU spu on sku.SPU_ID=spu.SPU_ID
			left join T_FIRST_ENGAGE z on spu.FIRST_ENGAGE_ID=z.FIRST_ENGAGE_ID and z.IS_DELETED=0
			left join T_REGISTRATION_NUMBER num on z.REGISTRATION_NUMBER_ID=num.REGISTRATION_NUMBER_ID
		</if>
		LEFT JOIN T_WAREHOUSE e ON a.WAREHOUSE_ID = e.WAREHOUSE_ID
		LEFT JOIN
		T_STORAGE_ROOM f ON a.STORAGE_ROOM_ID = f.STORAGE_ROOM_ID
		LEFT JOIN
		T_STORAGE_AREA g ON a.STORAGE_AREA_ID = g.STORAGE_AREA_ID
		LEFT JOIN
		T_STORAGE_RACK h ON a.STORAGE_RACK_ID = h.STORAGE_RACK_ID
		LEFT JOIN
		T_STORAGE_LOCATION i ON a.STORAGE_LOCATION_ID = i.STORAGE_LOCATION_ID
		LEFT JOIN T_BARCODE j ON a.BARCODE_ID = j.BARCODE_ID AND j.IS_ENABLE
		=1
		LEFT JOIN T_SALEORDER_GOODS k ON a.RELATED_ID = k.SALEORDER_GOODS_ID
		AND k.IS_DELETE =0
		LEFT JOIN T_WAREHOUSE_GOODS_OPERATE_LOG r
		ON
		a.BARCODE_ID = r.BARCODE_ID AND r.IS_ENABLE = 1 AND r.OPERATE_TYPE IN
		(2,4,6,7,10)
		where 1=1
		<if test="operateType == 3 or operateType == 5 or operateType == 8">
			<if test="ywType!=null">
				AND c.TYPE = #{ywType,jdbcType=INTEGER}
			</if>
		</if>
		<if test="operateType == 0">
			and a.OPERATE_TYPE in (3,5,8)
		</if>
		<if test="warehouseGoodsOperateLogId!=null">
			and
			a.WAREHOUSE_GOODS_OPERATE_LOG_ID=#{warehouseGoodsOperateLogId,jdbcType=INTEGER}
		</if>
		<if test="barcodeId!=null">
			and a.BARCODE_ID=#{barcodeId,jdbcType=INTEGER}
		</if>
		<if test="companyId!=null">
			and a.COMPANY_ID=#{companyId,jdbcType=INTEGER}
		</if>
		<if test="creator!=null and creator!=''">
			and a.CREATOR=#{creator,jdbcType=INTEGER}
		</if>
		<if test="operateType!=null and operateType != 0">
			and a.OPERATE_TYPE=#{operateType,jdbcType=INTEGER}
		</if>
		<if test="relatedId!=null">
			and a.RELATED_ID=#{relatedId,jdbcType=INTEGER}
		</if>
		<if test="goodsId!=null">
			and a.GOODS_ID=#{goodsId,jdbcType=INTEGER}
		</if>
		<if test="barcodeFactory!=null">
			and a.BARCODE_FACTORY like
			CONCAT('%',#{barcodeFactory},'%' )
		</if>
		<if test="num!=null">
			and a.NUM=#{num,jdbcType=INTEGER}
		</if>
		<if test="warehouseId!=null">
			and a.WAREHOUSE_ID=#{warehouseId,jdbcType=INTEGER}
		</if>
		<if test="storageRoomId!=null">
			and a.STORAGE_ROOM_ID=#{storageRoomId,jdbcType=INTEGER}
		</if>
		<if test="storageAreaId!=null">
			and a.STORAGE_AREA_ID=#{storageAreaId,jdbcType=INTEGER}
		</if>
		<if test="storageLocationId!=null">
			and
			a.STORAGE_LOCATION_ID=#{storageLocationId,jdbcType=INTEGER}
		</if>
		<if test="storageRackId!=null">
			and a.STORAGE_RACK_ID=#{storageRackId,jdbcType=INTEGER}
		</if>
		<if test="batchNumber!=null">
			and a.BATCH_NUMBER like CONCAT('%',#{batchNumber},'%' )
		</if>
		<if test="expirationDate!=null">
			and a.EXPIRATION_DATE=#{expirationDate,jdbcType=INTEGER}
		</if>
		<if test="checkStatus!=null and checkStatus!='-1'">
			and a.checkStatus=#{checkStatus,jdbcType=INTEGER}
		</if>
		<if test="checkStatusUser!=null and checkStatus!='-1'">
			and
			a.CHECK_STATUS_USER=#{checkStatusUser,jdbcType=INTEGER}
		</if>
		<if test="recheckStatus!=null and recheckStatus!='-1'">
			and a.RECHECK_STATUS=#{recheckStatus,jdbcType=INTEGER}
		</if>
		<if test="recheckStatusUser!=null and recheckStatusUser!='-1'">
			and
			a.RECHECK_STATUS_USER=#{recheckStatusUser,jdbcType=INTEGER}
		</if>
		<if test="isEnable != null and isEnable != ''">
			and a.IS_ENABLE=#{isEnable,jdbcType=BIT}
		</if>
		<if test="beginTime != null and beginTime != ''">
			AND a.ADD_TIME >= #{beginTime,jdbcType=INTEGER}
		</if>
		<if test="endTime != null and endTime != ''">
			AND a.ADD_TIME <![CDATA[ <= ]]>
			#{endTime,jdbcType=INTEGER}
		</if>

		<if
				test="operateType == 1 and buyorderNo != null and buyorderNo != ''">
			and c.BUYORDER_NO like CONCAT('%',#{buyorderNo},'%' )
		</if>
		<if
				test="operateType == 1 and buytraderName != null and buytraderName != ''">
			and c.TRADER_NAME like CONCAT('%',#{buytraderName},'%' )
		</if>

		<if
				test="operateType == 2 and saleorderNo != null and saleorderNo != ''">
			and c.SALEORDER_NO like CONCAT('%',#{saleorderNo},'%' )
		</if>
		<if
				test="operateType == 2 and saletraderName != null and saletraderName != ''">
			and c.TRADER_NAME like CONCAT('%',#{saletraderName},'%' )
		</if>
		<if test="operateType == 3 or operateType == 5 or operateType == 8">
			<if test="afterSalesId !=null">
				and c.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
			</if>
		</if>
		<if test="goodsName != null and goodsName != ''">
			and b.GOODS_NAME like CONCAT('%',#{goodsName},'%' )
		</if>
		<if test="brandName != null and brandName != ''">
			and b.BRAND_NAME like CONCAT('%',#{brandName},'%' )
		</if>
		<if test="model != null and model != ''">
			and b.MODEL like CONCAT('%',#{model},'%' )
		</if>
		<if test="materialCode != null and materialCode != ''">
			and d.MATERIAL_CODE like CONCAT('%',#{materialCode},'%' )
		</if>
		<if test="sku != null and sku != ''">
			and b.SKU like CONCAT('%',#{sku},'%' )
		</if>
		<if test="barcode != null and barcode != ''">
			and j.BARCODE like CONCAT('%',#{barcode},'%' )
		</if>
		<if test="operateType == 2">
			and c.ORDER_TYPE =0
		</if>
		<if test="isBarcode == 1">
			AND a.OPERATE_TYPE IN (1,3,5,8,9)
		</if>
		GROUP BY a.WAREHOUSE_GOODS_OPERATE_LOG_ID
		order by a.WAREHOUSE_GOODS_OPERATE_LOG_ID desc,d.SKU asc
	</select>

	<select id="getWGOlog2" resultMap="VoResultMap" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		<if
				test="operateType != 3  and operateType != 5 and operateType != 8 and operateType!=9">
			select
			a.*,b.PRICE,b.GOODS_NAME,b.MODEL,b.BRAND_NAME,d.MATERIAL_CODE,b.UNIT_NAME,b.SKU,b.PRICE,j.BARCODE,
		</if>
		<if test="operateType == 3 or operateType == 5 or operateType == 8">
			select a.*,d.GOODS_NAME,
			d.SKU,d.GOODS_ID,l.BRAND_NAME,d.MODEL,d.MATERIAL_CODE,m.UNIT_NAME,j.BARCODE,
		</if>
		<if test="operateType==9">
			SELECT
			a.*,
			d.GOODS_NAME,
			d.MODEL,
			br.BRAND_NAME,
			d.MATERIAL_CODE,
			un.UNIT_NAME,
			d.SKU,
			j.BARCODE,
		</if>
		k.PRICE XSPRICE,
		k.PURCHASING_PRICE,
		<if test="operateType == 5">
			SUM(ABS(IFNULL(r.NUM,0))) NUMS,
		</if>
		<if test="operateType != 5 and operateType != 8">
			ABS(IFNULL(r.NUM,0)) NUMS,
		</if>
		concat(e.WAREHOUSE_NAME,
		' ',
		IFNULL(f.STORAGE_ROOM_NAME, ''),
		' ',
		IFNULL(g.STORAGE_AREA_NAME, ''),
		' ',
		IFNULL(h.STORAGE_RACK_NAME, ''),
		'
		',
		IFNULL(i.STORAGE_LOCATION_NAME, '')
		) AS STORAGEADDRESS
		<if test="operateType == 1">
			,c.BUYORDER_NO,c.TRADER_NAME as
			BUYTRADERNAME,c.BUYORDER_ID,   num.REGISTRATION_NUMBER, z.FIRST_ENGAGE_ID as firstEngageId
		</if>
		<if test="operateType == 2">
			,c.SALEORDER_NO,c.TAKE_TRADER_NAME as
			SALETRADERNAME,c.SALEORDER_ID
		</if>
		<if test="operateType == 3 or operateType == 5 or operateType == 8">
			,c.AFTER_SALES_NO,c.AFTER_SALES_ID , num.REGISTRATION_NUMBER, z.FIRST_ENGAGE_ID as firstEngageId
		</if>
		<if test="operateType == 9">
			,le.`LEND_OUT_ID`,
			le.`LEND_OUT_NO`,
			le.`TRADER_NAME` AS SALETRADERNAME , num.REGISTRATION_NUMBER, z.FIRST_ENGAGE_ID as firstEngageId
		</if>
		from T_WAREHOUSE_GOODS_OPERATE_LOG a
		<if test="operateType == 1">
			LEFT JOIN T_BUYORDER_GOODS b on a.RELATED_ID =
			b.BUYORDER_GOODS_ID
			AND b.IS_DELETE =0
			LEFT JOIN T_BUYORDER c on
			b.BUYORDER_ID = c.BUYORDER_ID
			AND c.VALID_STATUS =1
			LEFT JOIN T_GOODS d
			on b.GOODS_ID = d.GOODS_ID
			left join V_CORE_SKU sku on b.GOODS_ID=sku.SKU_ID
			left join V_CORE_SPU spu on sku.SPU_ID=spu.SPU_ID
			left join T_FIRST_ENGAGE z on spu.FIRST_ENGAGE_ID=z.FIRST_ENGAGE_ID and z.IS_DELETED=0
			left join T_REGISTRATION_NUMBER num on z.REGISTRATION_NUMBER_ID=num.REGISTRATION_NUMBER_ID
		</if>
		<if test="operateType == 2">
			LEFT JOIN T_SALEORDER_GOODS b on a.RELATED_ID =
			b.SALEORDER_GOODS_ID
			AND b.IS_DELETE =0
			LEFT JOIN T_SALEORDER c on
			b.SALEORDER_ID = c.SALEORDER_ID
			AND c.VALID_STATUS =1
			LEFT JOIN T_GOODS
			d on b.GOODS_ID = d.GOODS_ID
		</if>
		<if test="operateType == 3 or operateType == 5 or operateType == 8">
			LEFT JOIN T_AFTER_SALES_GOODS b on a.RELATED_ID =
			b.AFTER_SALES_GOODS_ID
			AND b.GOODS_TYPE =0
			LEFT JOIN T_AFTER_SALES c on
			b.AFTER_SALES_ID = c.AFTER_SALES_ID
			AND c.VALID_STATUS =1
			LEFT JOIN
			T_GOODS d on b.GOODS_ID = d.GOODS_ID
			INNER JOIN T_BRAND l ON
			l.BRAND_ID = d.BRAND_ID
			INNER JOIN T_UNIT m ON m.UNIT_ID = d.UNIT_ID
			left join V_CORE_SKU sku on b.GOODS_ID=sku.SKU_ID
			left join V_CORE_SPU spu on sku.SPU_ID=spu.SPU_ID
			left join T_FIRST_ENGAGE z on spu.FIRST_ENGAGE_ID=z.FIRST_ENGAGE_ID and z.IS_DELETED=0
			left join T_REGISTRATION_NUMBER num on z.REGISTRATION_NUMBER_ID=num.REGISTRATION_NUMBER_ID
		</if>
		<if test="operateType == 9">
			LEFT JOIN `T_LEND_OUT` le
			ON a.`RELATED_ID` = le.`LEND_OUT_ID`
			LEFT JOIN `T_GOODS` d
			ON le.`GOODS_ID` = d.`GOODS_ID`
			LEFT JOIN `T_BRAND` br
			ON d.`BRAND_ID` = br.`BRAND_ID`
			LEFT JOIN `T_UNIT` un ON d.UNIT_ID = d.UNIT_ID
			left join V_CORE_SKU sku on le.GOODS_ID=sku.SKU_ID
			left join V_CORE_SPU spu on sku.SPU_ID=spu.SPU_ID
			left join T_FIRST_ENGAGE z on spu.FIRST_ENGAGE_ID=z.FIRST_ENGAGE_ID and z.IS_DELETED=0
			left join T_REGISTRATION_NUMBER num on z.REGISTRATION_NUMBER_ID=num.REGISTRATION_NUMBER_ID
		</if>
		LEFT JOIN T_WAREHOUSE e ON a.WAREHOUSE_ID = e.WAREHOUSE_ID
		LEFT JOIN
		T_STORAGE_ROOM f ON a.STORAGE_ROOM_ID = f.STORAGE_ROOM_ID
		LEFT JOIN
		T_STORAGE_AREA g ON a.STORAGE_AREA_ID = g.STORAGE_AREA_ID
		LEFT JOIN
		T_STORAGE_RACK h ON a.STORAGE_RACK_ID = h.STORAGE_RACK_ID
		LEFT JOIN
		T_STORAGE_LOCATION i ON a.STORAGE_LOCATION_ID = i.STORAGE_LOCATION_ID
		LEFT JOIN T_BARCODE j ON a.BARCODE_ID = j.BARCODE_ID AND j.IS_ENABLE
		=1
		LEFT JOIN T_SALEORDER_GOODS k ON a.RELATED_ID = k.SALEORDER_GOODS_ID
		AND k.IS_DELETE =0
		<if test="operateType != 8">
			LEFT JOIN T_WAREHOUSE_GOODS_OPERATE_LOG r
			ON
			a.BARCODE_ID = r.BARCODE_ID AND r.IS_ENABLE = 1 AND r.OPERATE_TYPE IN
			(2,4,6,7,10)
		</if>

		where 1=1
		<if test="operateType == 3 or operateType == 5 or operateType == 8">
			<if test="ywType!=null">
				AND c.TYPE = #{ywType,jdbcType=INTEGER}
			</if>
		</if>
		<if test="operateType == 0">
			and a.OPERATE_TYPE in (3,5,8)
		</if>
		<if test="warehouseGoodsOperateLogId!=null">
			and
			a.WAREHOUSE_GOODS_OPERATE_LOG_ID=#{warehouseGoodsOperateLogId,jdbcType=INTEGER}
		</if>
		<if test="barcodeId!=null">
			and a.BARCODE_ID=#{barcodeId,jdbcType=INTEGER}
		</if>
		<if test="companyId!=null">
			and a.COMPANY_ID=#{companyId,jdbcType=INTEGER}
		</if>
		<if test="creator!=null and creator!=''">
			and a.CREATOR=#{creator,jdbcType=INTEGER}
		</if>
		<if test="operateType!=null and operateType != 0">
			and a.OPERATE_TYPE=#{operateType,jdbcType=INTEGER}
		</if>
		<if test="relatedId!=null">
			and a.RELATED_ID=#{relatedId,jdbcType=INTEGER}
		</if>
		<if test="goodsId!=null">
			and a.GOODS_ID=#{goodsId,jdbcType=INTEGER}
		</if>
		<if test="barcodeFactory!=null">
			and a.BARCODE_FACTORY like
			CONCAT('%',#{barcodeFactory},'%' )
		</if>
		<if test="num!=null">
			and a.NUM=#{num,jdbcType=INTEGER}
		</if>
		<if test="warehouseId!=null">
			and a.WAREHOUSE_ID=#{warehouseId,jdbcType=INTEGER}
		</if>
		<if test="storageRoomId!=null">
			and a.STORAGE_ROOM_ID=#{storageRoomId,jdbcType=INTEGER}
		</if>
		<if test="storageAreaId!=null">
			and a.STORAGE_AREA_ID=#{storageAreaId,jdbcType=INTEGER}
		</if>
		<if test="storageLocationId!=null">
			and
			a.STORAGE_LOCATION_ID=#{storageLocationId,jdbcType=INTEGER}
		</if>
		<if test="storageRackId!=null">
			and a.STORAGE_RACK_ID=#{storageRackId,jdbcType=INTEGER}
		</if>
		<if test="batchNumber!=null">
			and a.BATCH_NUMBER like CONCAT('%',#{batchNumber},'%' )
		</if>
		<if test="expirationDate!=null">
			and a.EXPIRATION_DATE=#{expirationDate,jdbcType=INTEGER}
		</if>
		<if test="checkStatus!=null and checkStatus!='-1'">
			and a.checkStatus=#{checkStatus,jdbcType=INTEGER}
		</if>
		<if test="checkStatusUser!=null and checkStatus!='-1'">
			and
			a.CHECK_STATUS_USER=#{checkStatusUser,jdbcType=INTEGER}
		</if>
		<if test="recheckStatus!=null and recheckStatus!='-1'">
			and a.RECHECK_STATUS=#{recheckStatus,jdbcType=INTEGER}
		</if>
		<if test="recheckStatusUser!=null and recheckStatusUser!='-1'">
			and
			a.RECHECK_STATUS_USER=#{recheckStatusUser,jdbcType=INTEGER}
		</if>
		<if test="isEnable != null and isEnable != ''">
			and a.IS_ENABLE=#{isEnable,jdbcType=BIT}
		</if>
		<if test="beginTime != null and beginTime != ''">
			AND a.ADD_TIME >= #{beginTime,jdbcType=INTEGER}
		</if>
		<if test="endTime != null and endTime != ''">
			AND a.ADD_TIME <![CDATA[ <= ]]>
			#{endTime,jdbcType=INTEGER}
		</if>

		<if
				test="operateType == 1 and buyorderNo != null and buyorderNo != ''">
			and c.BUYORDER_NO like CONCAT('%',#{buyorderNo},'%' )
		</if>
		<if
				test="operateType == 1 and buytraderName != null and buytraderName != ''">
			and c.TRADER_NAME like CONCAT('%',#{buytraderName},'%' )
		</if>

		<if
				test="operateType == 2 and saleorderNo != null and saleorderNo != ''">
			and c.SALEORDER_NO like CONCAT('%',#{saleorderNo},'%' )
		</if>
		<if
				test="operateType == 2 and saletraderName != null and saletraderName != ''">
			and c.TRADER_NAME like CONCAT('%',#{saletraderName},'%' )
		</if>
		<if test="operateType == 3 or operateType == 5 or operateType == 8">
			<if test="afterSalesId !=null">
				and c.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
			</if>
		</if>
		<if test="goodsName != null and goodsName != ''">
			and b.GOODS_NAME like CONCAT('%',#{goodsName},'%' )
		</if>
		<if test="brandName != null and brandName != ''">
			and b.BRAND_NAME like CONCAT('%',#{brandName},'%' )
		</if>
		<if test="model != null and model != ''">
			and b.MODEL like CONCAT('%',#{model},'%' )
		</if>
		<if test="materialCode != null and materialCode != ''">
			and d.MATERIAL_CODE like CONCAT('%',#{materialCode},'%' )
		</if>
		<if test="sku != null and sku != ''">
			and b.SKU like CONCAT('%',#{sku},'%' )
		</if>
		<if test="barcode != null and barcode != ''">
			and j.BARCODE like CONCAT('%',#{barcode},'%' )
		</if>
		<if test="operateType == 2">
			and c.ORDER_TYPE =0
		</if>
		<if test="isBarcode == 1">
			AND a.OPERATE_TYPE IN (1,3,5,8,9)
		</if>
		<if test="operateType == 5">
			GROUP BY a.WAREHOUSE_GOODS_OPERATE_LOG_ID
		</if>
		order by a.WAREHOUSE_GOODS_OPERATE_LOG_ID desc,d.SKU asc
	</select>
	<!-- 根据销售产品id查询未绑定快递详情的出库记录 -->
	<select id="getbdListBySaleGoodsId" resultMap="VoResultMap"
			parameterType="com.vedeng.logistics.model.ExpressDetail">
		SELECT
		WAREHOUSE_GOODS_OPERATE_LOG_ID
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG
		WHERE
		OPERATE_TYPE = 2
		AND IS_ENABLE = 1
		AND (IS_EXPRESS = 0 OR IS_EXPRESS IS NULL)
		AND RELATED_ID =
		#{relatedId,jdbcType=INTEGER}
	</select>
	<select id="getExpressWMSWlogIdsByExpressIds" resultType="java.lang.Integer">
		SELECT
		B.WAREHOUSE_GOODS_OPERATE_LOG_ID
	FROM
		T_EXPRESS_DETAIL A
		LEFT JOIN V_E_W_EXPRESS_WAREHOUSE B ON A.EXPRESS_DETAIL_ID = B.EXPRESS_DETAIL_ID
	WHERE
		 A.EXPRESS_ID in
		 <foreach collection="list" index="index" item="item" close=")" separator="," open="(">
			 #{item}
		 </foreach>
	</select>
	<!-- 批量更新出库记录和快递详情的绑定 -->
	<update id="batchUpdateWgolIsExpress"
			parameterType="java.util.List">
		UPDATE T_WAREHOUSE_GOODS_OPERATE_LOG
		SET IS_EXPRESS = CASE
		WAREHOUSE_GOODS_OPERATE_LOG_ID
		<foreach collection="list" item="wlog" index="index"
				 separator="">
			WHEN #{wlog.warehouseGoodsOperateLogId,jdbcType=INTEGER}
			THEN
			#{wlog.isExpress,jdbcType=INTEGER}
		</foreach>
		END
		WHERE WAREHOUSE_GOODS_OPERATE_LOG_ID IN
		<foreach collection="list" item="wlog" separator="," open="("
				 close=")">
			#{wlog.warehouseGoodsOperateLogId,jdbcType=INTEGER}
		</foreach>

	</update>
	<select id="getBuyorderAfterSaleLog" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		select * from T_WAREHOUSE_GOODS_OPERATE_LOG
		WHERE RELATED_ID = #{afterSalesGoodsId,jdbcType=INTEGER}
		AND OPERATE_TYPE = 6 AND IS_ENABLE = 1
	</select>
    <select id="getBuyorderAfterSaleLogNumByType" resultType="java.lang.Integer">
        select coalesce(sum(abs(NUM)),0)
        FROM T_WAREHOUSE_GOODS_OPERATE_LOG
		WHERE RELATED_ID = #{orderDetailId,jdbcType=INTEGER}
		AND OPERATE_TYPE = #{type,jdbcType=INTEGER}
		  AND IS_ENABLE = 1
    </select>
	<select id="getDeliveryDetailOfSaleorderGoods" resultType="com.vedeng.logistics.model.vo.ExpressArrivalDetailVo">
		select a.RELATED_ID,
			   a.DELIVERY_NUM,
			   a.DELIVERY_TIME,
			   ifnull(b.ARRIVAL_NUM, 0)  ARRIVAL_NUM,
			   ifnull(b.ARRIVAL_TIME, 0) ARRIVAL_TIME
		from (
				 select RELATED_ID, -sum(NUM) as DELIVERY_NUM, MAX(ADD_TIME) AS DELIVERY_TIME
				 from T_WAREHOUSE_GOODS_OPERATE_LOG
				 where OPERATE_TYPE = 2
				   and IS_ENABLE = 1
				   and GOODS_ID = #{goodsId}
				   and RELATED_ID = #{relatedId}
				 group by RELATED_ID
			 ) a
				 left join
			 (
				 select sum(if(e.ARRIVAL_STATUS = 2, ed.NUM, 0)) as ARRIVAL_NUM,
						ed.RELATED_ID,
						max(e.ARRIVAL_TIME) as ARRIVAL_TIME
				 from T_EXPRESS e
						  join T_EXPRESS_DETAIL ed on e.EXPRESS_ID = ed.EXPRESS_ID
				 where e.IS_ENABLE = 1
				   and ed.RELATED_ID = #{relatedId}
				   and ed.BUSINESS_TYPE = 496 group by ed.RELATED_ID
			 ) b on a.RELATED_ID = b.RELATED_ID
	</select>
	<select id="batchOutStockBySaleorderGoodsIdList" resultType="com.vedeng.order.model.vo.SaleorderGoodsVo" parameterType="java.util.List">
		SELECT
		abs(COALESCE(SUM(b.NUM),0)) as saleGoodsOut
		,a.SALEORDER_GOODS_ID
		FROM T_SALEORDER_GOODS a
		LEFT JOIN
		T_WAREHOUSE_GOODS_OPERATE_LOG b ON a.SALEORDER_GOODS_ID =
		b.RELATED_ID
		WHERE b.OPERATE_TYPE = 2 AND b.IS_ENABLE = 1 and
		a.DELIVERY_DIRECT = 0
		and a.SALEORDER_GOODS_ID in
		<foreach item="sg" index="index" collection="list" open="("
				 separator="," close=")">
			#{sg.saleorderGoodsId}
		</foreach>
		group by a.SALEORDER_GOODS_ID
	</select>

	<select id="batchBuyGoodsInStockBySaleorderGoodsIdList"
			resultType="com.vedeng.order.model.vo.SaleorderGoodsVo"
			parameterType="com.vedeng.order.model.vo.SaleorderGoodsVo">
		SELECT
		COALESCE(SUM(b.NUM),0) AS buyGoodsIn, d.SALEORDER_GOODS_ID
		FROM
		T_BUYORDER_GOODS a
		INNER JOIN T_WAREHOUSE_GOODS_OPERATE_LOG b ON
		a.BUYORDER_GOODS_ID = b.RELATED_ID AND b.OPERATE_TYPE = 1 AND
		b.IS_ENABLE = 1
		INNER JOIN T_R_BUYORDER_J_SALEORDER d ON
		a.BUYORDER_GOODS_ID = d.BUYORDER_GOODS_ID
		WHERE d.SALEORDER_GOODS_ID in
		<foreach item="sg" index="index" collection="list" open="("
				 separator="," close=")">
			#{sg.saleorderGoodsId}
		</foreach>
		group by d.SALEORDER_GOODS_ID
	</select>

	<select id="batchBuyGoodsNotStockBySaleorderGoodsIdList"
			resultType="com.vedeng.order.model.vo.SaleorderGoodsVo"
			parameterType="com.vedeng.order.model.vo.SaleorderGoodsVo">
		<!-- SELECT COALESCE((SUM(b.NUM) + SUM(c.NUM)),0) AS buyGoodsNotOut, d.SALEORDER_GOODS_ID
			FROM T_WAREHOUSE_GOODS_OPERATE_LOG b left JOIN T_WAREHOUSE_GOODS_OPERATE_LOG
			c ON b.BARCODE_ID = c.BARCODE_ID AND c.OPERATE_TYPE = 2 AND c.IS_ENABLE =
			1 left JOIN T_R_BUYORDER_J_SALEORDER d ON b.RELATED_ID = d.BUYORDER_GOODS_ID
			left JOIN T_SALEORDER_GOODS e ON e.SALEORDER_GOODS_ID = d.SALEORDER_GOODS_ID
			WHERE e.DELIVERY_DIRECT = 0 AND b.OPERATE_TYPE = 1 AND b.IS_ENABLE = 1 and
			d.SALEORDER_GOODS_ID in -->
		SELECT
		COALESCE (SUM(b.NUM + ifnull(c.NUM,0)), 0) AS buyGoodsNotOut,
		d.SALEORDER_GOODS_ID
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG b
		LEFT JOIN
		T_R_BUYORDER_J_SALEORDER d ON b.RELATED_ID = d.BUYORDER_GOODS_ID
		LEFT
		JOIN T_SALEORDER_GOODS e ON e.SALEORDER_GOODS_ID =
		d.SALEORDER_GOODS_ID
		LEFT JOIN (
		SELECT
		sum(NUM) AS NUM,
		BARCODE_ID
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG
		WHERE
		OPERATE_TYPE = 2
		AND IS_ENABLE = 1
		AND BARCODE_ID > 0
		AND
		RELATED_ID in
		<foreach item="sg" index="index" collection="list" open="("
				 separator="," close=")">
			#{sg.saleorderGoodsId}
		</foreach>
		GROUP BY
		BARCODE_ID
		) c ON b.BARCODE_ID = c.BARCODE_ID
		WHERE
		e.DELIVERY_DIRECT = 0
		AND b.OPERATE_TYPE = 1
		AND b.IS_ENABLE = 1
		AND
		d.SALEORDER_GOODS_ID IN
		<foreach item="sg" index="index" collection="list" open="("
				 separator="," close=")">
			#{sg.saleorderGoodsId}
		</foreach>
		group by e.SALEORDER_GOODS_ID
	</select>

	<select id="getListByRelatedId" resultMap="VoResultMap">
		SELECT * FROM T_WAREHOUSE_GOODS_OPERATE_LOG a
		WHERE a.RELATED_ID=#{relatedId,jdbcType=INTEGER} AND a.IS_ENABLE = 1 ORDER BY a.RECHECK_STATUS_TIME
	</select>

	<select id="querySnBySaleorderGoodsId" resultType="java.lang.String">
		SELECT DISTINCT BARCODE_FACTORY
		FROM T_WAREHOUSE_GOODS_OPERATE_LOG
		WHERE RELATED_ID = #{saleorderGoodsId,jdbcType=INTEGER} AND OPERATE_TYPE = 2
		AND IS_ENABLE = 1 AND BARCODE_FACTORY IS NOT NULL
	</select>
	<select id="getWGOlistByComments" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		SELECT a.ADD_TIME
		FROM T_WAREHOUSE_GOODS_OPERATE_LOG a
		LEFT JOIN
			T_SALEORDER_GOODS b on b.SALEORDER_GOODS_ID = a.RELATED_ID
			AND b.IS_DELETE =0
		WHERE COMMENTS like CONCAT('%',#{batChNo},'%' ) AND OPERATE_TYPE = 2
		  and b.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
		  AND IS_ENABLE = 1 AND BARCODE_FACTORY IS NOT NULL
	</select>
	<select id="getAddTimeByRelatedId" parameterType="java.lang.Integer" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
		select TW.COMMENTS comments, TW.ADD_TIME addTime
		from T_WAREHOUSE_GOODS_OPERATE_LOG TW
		WHERE TW.RELATED_ID = #{relateId,jdbcType=INTEGER}
		AND TW.IS_ENABLE = 1
		limit 1
	</select>

	<select id="getWarehouseOutListByInventoryOutOrderId" resultMap="VoResultMap">
		SELECT
			f.GOODS_NAME,
			g.BRAND_NAME,
			f.MODEL,
			h.UNIT_NAME,
			d.BARCODE,
			c.MATERIAL_CODE,
			k.SKU_NO AS SKU,
			a.NUM,
			a.ADD_TIME,
			a.WAREHOUSE_GOODS_OPERATE_LOG_ID,
			a.IS_ENABLE,
			a.CREATOR,
			a.GOODS_ID,
			a.BARCODE_FACTORY,
			a.BATCH_NUMBER,
			ABS( a.NUM ) AS REAL_GOODS_NUM,
			a.VEDENG_BATCH_NUMER,
			a.STERILZATION_BATCH_NUMBER,
			n.REGISTRATION_NUMBER_ID,
			n.REGISTRATION_NUMBER,
			a.EXPIRATION_DATE,
			a.CHECK_STATUS_TIME,
			a.PRODUCT_DATE,
			z.FIRST_ENGAGE_ID,
			a.RELATED_ID,
		    a.OPERATE_TYPE
		FROM
			T_WAREHOUSE_GOODS_OPERATE_LOG a
				LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS b ON b.id = a.RELATED_ID
				INNER JOIN T_GOODS f ON b.sku_no = f.SKU
				INNER JOIN T_BRAND g ON f.BRAND_ID = g.BRAND_ID
				INNER JOIN T_UNIT h ON f.UNIT_ID = h.UNIT_ID
				LEFT JOIN T_GOODS c ON b.sku_no = c.SKU
				LEFT JOIN T_BARCODE d ON a.BARCODE_ID = d.BARCODE_ID
				AND d.IS_ENABLE = 1
				LEFT JOIN V_CORE_SKU k ON a.GOODS_ID = k.SKU_ID
				LEFT JOIN V_CORE_SPU p ON k.SPU_ID = p.SPU_ID
				LEFT JOIN T_FIRST_ENGAGE z ON p.FIRST_ENGAGE_ID = z.FIRST_ENGAGE_ID
				AND z.IS_DELETED = 0
				LEFT JOIN T_REGISTRATION_NUMBER n ON z.REGISTRATION_NUMBER_ID = n.REGISTRATION_NUMBER_ID
		WHERE
			1 = 1
		  	AND b.wms_output_order_id = #{inventoryOutOrderId}
		  AND a.OPERATE_TYPE = 16
		  AND a.IS_ENABLE = 1
		ORDER BY
			a.WAREHOUSE_GOODS_OPERATE_LOG_ID DESC
	</select>




	<select id="getOperateList" resultType="com.vedeng.logistics.model.outIn.OutInDetail">
		SELECT
		RELATED_ID,
		ifnull(TSG.IS_GIFT,0) isGift,
		a.GOODS_ID,
		BARCODE_FACTORY,
		a.NUM,
		BATCH_NUMBER,
		CASE
			a.ADD_TIME
			WHEN '' THEN ''
			ELSE STR_TO_DATE(a.ADD_TIME, '%Y-%m-%d')
		END as checkStatusTimeStr,
		STERILIZATION_BATCH_NUMBER,
		CASE
			PRODUCT_DATE
			WHEN '' THEN ''
			ELSE STR_TO_DATE(PRODUCT_DATE, '%Y-%m-%d')
		END as productDateStr,
		CASE
			EXPIRATION_DATE
			WHEN '' THEN ''
			ELSE STR_TO_DATE(a.EXPIRATION_DATE, '%Y-%m-%d')
		END as expirationDateStr,
		VEDENG_BATCH_NUMBER as vedengBatchNumer,
		STERILIZATION_BATCH_NUMBER sterilizationBatchNo
		FROM T_WAREHOUSE_GOODS_OUT_IN_ITEM a
		LEFT JOIN
		T_AFTER_SALES_GOODS TASG ON a.RELATED_ID = TASG.AFTER_SALES_GOODS_ID
		LEFT JOIN
		T_SALEORDER_GOODS TSG ON TASG.ORDER_DETAIL_ID = TSG.SALEORDER_GOODS_ID
		WHERE 1=1
		<if test="outInNo != '' and outInNo != null">
			AND a.OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
		</if>
		<if test="wmsNo != '' and wmsNo != null">
			AND a.WMS_NO = #{wmsNo,jdbcType=VARCHAR}
		</if>
		<if test="outInType != null">
			AND a.OPERATE_TYPE = #{outInType,jdbcType=INTEGER}
		</if>
		group by a.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID
	</select>
	<select id="getOUtInDetailList" resultType="com.vedeng.logistics.model.outIn.OutInDetail">
		SELECT
		T1.WAREHOUSE_GOODS_OUT_IN_ID,
		T2.GOODS_ID,
		T2.OUT_IN_NO,
		T2.BARCODE_FACTORY,
		T2.BATCH_NUMBER,
		ABS(T2.NUM)                                                   num,
		CASE
		T2.CHECK_STATUS_TIME
		WHEN '' THEN ''
		ELSE STR_TO_DATE(T2.CHECK_STATUS_TIME, '%Y-%m-%d')
		END as checkStatusTimeStr,
		T2.STERILIZATION_BATCH_NUMBER                                 sterilizationBatchNo,
		CASE
		T2.PRODUCT_DATE
		WHEN '' THEN ''
		ELSE STR_TO_DATE(T2.PRODUCT_DATE, '%Y-%m-%d')
		END as productDateStr,
		CASE
		T2.EXPIRATION_DATE
		WHEN '' THEN ''
		ELSE STR_TO_DATE(T2.EXPIRATION_DATE, '%Y-%m-%d')
		END as expirationDateStr,
		T2.VEDENG_BATCH_NUMBER vedengBatchNumer
		FROM T_WAREHOUSE_GOODS_OUT_IN T1
		INNER JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM T2 ON T1.OUT_IN_NO = T2.OUT_IN_NO
		WHERE T1.IS_DELETE = 0
		AND T2.IS_DELETE = 0
		AND T2.COMPANY_ID = 1
		AND T2.LOG_TYPE = 0
		AND T1.OUT_IN_TYPE = #{outInType,jdbcType=INTEGER}
		AND T2.OPERATE_TYPE = #{operateType,jdbcType=INTEGER}
		AND T1.RELATE_NO = #{orderNo,jdbcType=VARCHAR}
	</select>

	<select id="getWmsUnitConversionOutLog" resultMap="VoResultMap">
		SELECT
		k.SKU_NO AS SKU,
		a.NUM,
		a.ADD_TIME,
		a.WAREHOUSE_GOODS_OPERATE_LOG_ID,
		a.IS_ENABLE,
		a.CREATOR,
		a.GOODS_ID,
		a.BARCODE_FACTORY,
		a.BATCH_NUMBER,
		ABS( a.NUM ) AS REAL_GOODS_NUM,
		a.VEDENG_BATCH_NUMER,
		a.STERILZATION_BATCH_NUMBER,
		n.REGISTRATION_NUMBER_ID,
		n.REGISTRATION_NUMBER,
		a.EXPIRATION_DATE,
		a.CHECK_STATUS_TIME,
		a.PRODUCT_DATE,
		z.FIRST_ENGAGE_ID,
		a.RELATED_ID,
		a.OPERATE_TYPE
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG a
		LEFT JOIN T_WMS_UNIT_CONVERSION_ORDER_ITEM b ON b.WMS_UNIT_CONVERSION_ORDER_ITEM_ID = a.RELATED_ID
		LEFT JOIN V_CORE_SKU k ON a.GOODS_ID = k.SKU_ID
		LEFT JOIN V_CORE_SPU p ON k.SPU_ID = p.SPU_ID
		LEFT JOIN T_FIRST_ENGAGE z ON p.FIRST_ENGAGE_ID = z.FIRST_ENGAGE_ID
		AND z.IS_DELETED = 0
		LEFT JOIN T_REGISTRATION_NUMBER n ON z.REGISTRATION_NUMBER_ID = n.REGISTRATION_NUMBER_ID
		WHERE
		 b.WMS_UNIT_CONVERSION_ORDER_ID = #{wmsUnitConversionOrderId}
		AND a.OPERATE_TYPE = 19
		AND a.IS_ENABLE = 1
		ORDER BY
		a.WAREHOUSE_GOODS_OPERATE_LOG_ID DESC
	</select>

	<select id="getWmsUnitConversionInLog" resultMap="VoResultMap">
		SELECT
		k.SKU_NO AS SKU,
		a.NUM,
		a.ADD_TIME,
		a.WAREHOUSE_GOODS_OPERATE_LOG_ID,
		a.IS_ENABLE,
		a.CREATOR,
		a.GOODS_ID,
		a.BARCODE_FACTORY,
		a.BATCH_NUMBER,
		ABS( a.NUM ) AS REAL_GOODS_NUM,
		a.VEDENG_BATCH_NUMER,
		a.STERILZATION_BATCH_NUMBER,
		n.REGISTRATION_NUMBER_ID,
		n.REGISTRATION_NUMBER,
		a.EXPIRATION_DATE,
		a.CHECK_STATUS_TIME,
		a.PRODUCT_DATE,
		z.FIRST_ENGAGE_ID,
		a.RELATED_ID,
		a.OPERATE_TYPE
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG a
		LEFT JOIN T_WMS_UNIT_CONVERSION_ORDER_ITEM b ON b.WMS_UNIT_CONVERSION_ORDER_ITEM_ID = a.RELATED_ID
		LEFT JOIN V_CORE_SKU k ON a.GOODS_ID = k.SKU_ID
		LEFT JOIN V_CORE_SPU p ON k.SPU_ID = p.SPU_ID
		LEFT JOIN T_FIRST_ENGAGE z ON p.FIRST_ENGAGE_ID = z.FIRST_ENGAGE_ID
		AND z.IS_DELETED = 0
		LEFT JOIN T_REGISTRATION_NUMBER n ON z.REGISTRATION_NUMBER_ID = n.REGISTRATION_NUMBER_ID
		WHERE
		b.WMS_UNIT_CONVERSION_ORDER_ID = #{wmsUnitConversionOrderId}
		AND a.OPERATE_TYPE = 20
		AND a.IS_ENABLE = 1
		ORDER BY
		a.WAREHOUSE_GOODS_OPERATE_LOG_ID DESC
	</select>


	<select id="getWarehouseLogById" resultMap="WarehouseLogGroupResultMap">
		SELECT
			WAREHOUSE_GOODS_OPERATE_LOG_ID,
			LEFT(FROM_UNIXTIME(ADD_TIME/1000),19) AS ADD_TIME
		FROM
		T_WAREHOUSE_GOODS_OPERATE_LOG a
		WHERE a.WAREHOUSE_GOODS_OPERATE_LOG_ID IN
		<foreach item="id" index="index" collection="idList" open="("
				 separator="," close=")">
			#{id}
		</foreach>
	</select>


	<select id="getWarehouseBySaleorderId" resultType="map">
		SELECT
			g.SALEORDER_GOODS_ID,
			g.SALEORDER_ID,
			g.GOODS_ID,
			g.NUM AS ORDER_NUM,							  -- 商品数量
			IFNULL(g.AFTER_RETURN_NUM, 0) AS RETURN_NUM,  -- 退货数量
			IFNULL(log1.OUT_NUM, 0) AS NORMAL_OUT_NUM,    -- 普发出库数量
			IFNULL(log2.OUT_NUM, 0) AS VIRTUAL_OUT_NUM,   -- 直发出库数量
			(IFNULL(log1.OUT_NUM, 0) + IFNULL(log2.OUT_NUM, 0)) AS TOTAL_OUT_NUM, -- 全部出库数量
			CASE                                                                   -- (商品数量-退货数量）小于等于 全部出库数量时，为全部出库
			WHEN (g.NUM - IFNULL(g.AFTER_RETURN_NUM, 0)) <![CDATA[ <= ]]> (IFNULL(log1.OUT_NUM, 0) + IFNULL(log2.OUT_NUM, 0))
			THEN 1 ELSE 0
			END AS IS_ALL_OUT
		FROM
		T_SALEORDER_GOODS g
		-- 普发出库
		LEFT JOIN (
			SELECT
			b.SALEORDER_GOODS_ID,
			SUM(abs(a.NUM)) AS OUT_NUM
			FROM
			T_WAREHOUSE_GOODS_OPERATE_LOG a
			INNER JOIN T_SALEORDER_GOODS b ON b.SALEORDER_GOODS_ID = a.RELATED_ID
			WHERE
			a.OPERATE_TYPE = 2
			AND a.IS_ENABLE = 1
			AND b.IS_DELETE = 0
			AND b.SALEORDER_ID = #{saleorderId}
			GROUP BY b.SALEORDER_GOODS_ID
		) log1 ON log1.SALEORDER_GOODS_ID = g.SALEORDER_GOODS_ID
		-- 直发出库
		LEFT JOIN (
			SELECT
			b.SALEORDER_GOODS_ID,
			SUM(abs(a.NUM)) AS OUT_NUM
			FROM
			T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL a
			INNER JOIN T_SALEORDER_GOODS b ON b.SALEORDER_GOODS_ID = a.RELATED_ID
			WHERE
			a.OPERATE_TYPE = 2
			AND a.IS_ENABLE = 1
			AND b.IS_DELETE = 0
			AND b.SALEORDER_ID = #{saleorderId}
			GROUP BY b.SALEORDER_GOODS_ID
		) log2 ON log2.SALEORDER_GOODS_ID = g.SALEORDER_GOODS_ID
		WHERE
		g.SALEORDER_ID = #{saleorderId}
		AND g.IS_DELETE = 0
	</select>


	<select id="getLog" resultMap="BaseResultMap">
		select *
		from T_WAREHOUSE_GOODS_OPERATE_LOG a
				 left join T_BUYORDER_GOODS b on a.RELATED_ID = b.BUYORDER_GOODS_ID
		where a.OPERATE_TYPE = 1 and b.IS_DELETE = 0 and b.BUYORDER_ID = #{buyorderId}
	</select>
</mapper>
