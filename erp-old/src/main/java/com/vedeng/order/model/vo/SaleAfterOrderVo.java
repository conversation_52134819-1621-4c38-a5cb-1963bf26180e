package com.vedeng.order.model.vo;


import java.util.List;

/**
 * <AUTHOR>
 * @ClassName SaleAfterOrderVo.java
 * @Description TODO
 * @createTime 2021年02月24日 15:32:00
 */
public class SaleAfterOrderVo {
    /**
     * 订单ID
     */
    private Integer orderId;


    /**
     * 售后类型（字典库）DB的数字字典
     * 539 销售订单退货
     * 540 销售订单换货
     */
    private Integer type;

    /**
     * T_AFTER_SALES_DETAIL  REASON
     * 售后原因（字典库）
     *
     */
    private Integer reason;

    /**
     * `COMMENTS` varchar(512) DEFAULT NULL COMMENT '详细说明',
     * 问题描述
     */
    private String comments;

    /**
     * 联系人
     */
    private String traderContactName;

    /**
     * 联系电话
     */
    private String traderContactMobile;

    /**
     * 订单单号
     */
    private String orderNo;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 图片集合
     */
    List<MapVo<String>> imgList;

    /**
     * 公司ID
     */
    private Integer companyId;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getReason() {
        return reason;
    }

    public void setReason(Integer reason) {
        this.reason = reason;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getTraderContactName() {
        return traderContactName;
    }

    public void setTraderContactName(String traderContactName) {
        this.traderContactName = traderContactName;
    }

    public String getTraderContactMobile() {
        return traderContactMobile;
    }

    public void setTraderContactMobile(String traderContactMobile) {
        this.traderContactMobile = traderContactMobile;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public List<MapVo<String>> getImgList() {
        return imgList;
    }

    public void setImgList(List<MapVo<String>> imgList) {
        this.imgList = imgList;
    }

    @Override
    public String toString() {
        return "SaleAfterOrderVo{" +
                "orderId=" + orderId +
                ", type=" + type +
                ", reason=" + reason +
                ", comments='" + comments + '\'' +
                ", traderContactName='" + traderContactName + '\'' +
                ", traderContactMobile='" + traderContactMobile + '\'' +
                ", orderNo='" + orderNo + '\'' +
                ", traderId=" + traderId +
                ", traderName='" + traderName + '\'' +
                ", imgList=" + imgList +
                ", companyId=" + companyId +
                '}';
    }
}
