package com.vedeng.erp.trader.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.dto.TraderCustomerTerminalDto;
import com.vedeng.erp.trader.dto.TraderCustomerTerminalQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/4 19:14
 **/
public interface TraderCustomerTerminalApiService {


    /**
     * 保存集合对象
     * @param traderCustomerTerminalDtoList 数据
     */
    void addByList(List<TraderCustomerTerminalDto> traderCustomerTerminalDtoList);


    /**
     * 根据客户 id 查询数据
     * @param traderId 客户id
     * @return List<TraderCustomerTerminalDto>
     */
    List<TraderCustomerTerminalDto> queryByTraderId(Integer traderId);

    /**
     * 分页 查询
     * @param query
     * @return
     */
    PageInfo<TraderCustomerTerminalDto> page(PageParam<TraderCustomerTerminalQuery> query);

    /**
     * 获取审核中的数据
     * @param traderId
     * @return
     */
    List<TraderCustomerTerminalDto> getAuditData(Integer traderId);

    /**
     * 审核通过
     * @param traderCustomerTerminalId
     */
    void doPass(Integer traderCustomerTerminalId);

    /**
     * 审核不通过
     * @param traderCustomerTerminalId
     * @param desc
     */
    void doReject(Integer traderCustomerTerminalId, String desc);

    /**
     * 根据主键查询数据
     * @param traderCustomerTerminalId
     * @return
     */
    TraderCustomerTerminalDto getData(Integer traderCustomerTerminalId);

    /**
     * 更新数据
     * @param traderCustomerTerminalDto
     */
    void update(TraderCustomerTerminalDto traderCustomerTerminalDto);

}
