$(function () {
	var contentVa = $('#contentVa').val();
	var splits = contentVa.split('&&');
	for (var index = 0; index < splits.length; index++) {
		var productCountFlag = $('#productCountFlag').val();
		$('#content_' + productCountFlag).val(splits[index]);
		var count = parseFloat(productCountFlag) + 1;
		$('#productCountFlag').val(count);
		$('#productDiv_' + productCountFlag).after(
			" <li id=\"productDiv_" + count + "\">\n" +
			"                        <div class=\"infor_name\" id=\"productTitleDiv_" + count + "\" idFlag=\"" + count + "\">\n" +
			"                            <label></label>\n" +
			"                        </div>\n" +
			"                        <div class=\"f_left\">\n" +
			"                            <input class=\"input-larger\" id=\"content_" + count + "\" />\n &nbsp;  <a onclick='deleteCategoryName(" + count + ")'>删除</a>" +
			"                        </div>\n" +
			"                    </li>"
		);
		$('#content').val(getContentStr());
	}
})

/**
 * @description 获取商机产品的拼接结果
 * <AUTHOR>
 * @date 2020/6/16 14:32:12
 */
function getContentStr() {
	var contentResult = '';
	var contents = $("input[id^=content_]");
	contents.each(function () {
		contentResult = addResultStr(contentResult, $(this).val());
	})
	return contentResult;
}

/**
 * @description 循环添加每个输入框的产品
 * @param result
 * @param str
 * @returns {*}
 * <AUTHOR>
 * @date 2020/6/16 14:36:18
 */
function addResultStr(result, str) {
	var str = str.replace(/(^\s*)|(\s*$)/g, '');
	if (!(str == '' || str == undefined || str == null)) {
		result = result == '' ? str : result + '&&' + str;
	}
	return result;
}