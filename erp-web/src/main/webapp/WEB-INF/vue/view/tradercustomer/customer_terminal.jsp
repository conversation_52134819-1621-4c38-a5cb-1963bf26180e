<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />

<style>
    .radio-item {
        display: block;
        margin-bottom: 15px;
        margin-inline: 25px;
        margin-top: 15px;
    }
</style>
<div id="app">
    <el-radio-group v-model="radio">
    <div class="radio-item" v-for="item in traderCustomerTerminalList">
        <el-radio :label="item">{{item.hosName}}</el-radio> <br>
    </div>
    </el-radio-group>


</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomerTerminal.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">
    const queryData = '${query}';
    const sendThis = (_this) => {
        vm = _this;
    }
    new Vue({
        el: '#app',
        data() {
            return {
                radio:'',
                traderCustomerTerminalList: []

            }
        },

        mounted() {
            loadingApp()
        },

        created() {
            sendThis(this);

            queryTerminalData({"name": queryData}).then(res => {
                this.traderCustomerTerminalList = res.data.data.list;
            })
        },

        methods: {
            scrollTo(ref){
                let top = this.$refs[ref].$el.offsetTop;
                window.scrollTo(0, top);
            }
        }
    })
</script>
