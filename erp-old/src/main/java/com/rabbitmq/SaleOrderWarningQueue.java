package com.rabbitmq;

import com.vedeng.order.model.SaleOrderWarnVo;

import java.util.Iterator;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * @Description:  待采购订单队列
 * @Author:       allenyll
 * @Date:         2021/4/20 下午2:06
 * @Version:      1.0
 */
public class SaleOrderWarningQueue {

    //队列大小
    static final int QUEUE_MAX_SIZE   = 2000;

    static BlockingQueue<SaleOrderWarnVo> blockingQueue = new LinkedBlockingQueue<>();

    /**
     * 私有的默认构造子，保证外界无法直接实例化
     */
    private SaleOrderWarningQueue(){};

    /**
     * 类级的内部类，也就是静态的成员式内部类，该内部类的实例与外部类的实例
     * 没有绑定关系，而且只有被调用到才会装载，从而实现了延迟加载
     */
    private static class SingletonHolder{
        /**
         * 静态初始化器，由JVM来保证线程安全
         */
        private  static SaleOrderWarningQueue queue = new SaleOrderWarningQueue();
    }

    //单例队列
    public static SaleOrderWarningQueue getSaleOrderQueue(){
        return SingletonHolder.queue;
    }

    //生产入队
    public  void  produce(SaleOrderWarnVo saleOrderWarnVo) throws InterruptedException {
        blockingQueue.put(saleOrderWarnVo);
    }

    //消费出队
    public  SaleOrderWarnVo consume() throws InterruptedException {
        return blockingQueue.take();
    }

    // 获取队列大小
    public int size() {
        return blockingQueue.size();
    }

}
