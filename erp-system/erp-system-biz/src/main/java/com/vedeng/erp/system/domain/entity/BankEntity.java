package com.vedeng.erp.system.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 银行信息
 * @date 2022/8/11 10:54
 */
@Getter
@Setter
public class BankEntity extends BaseEntity {


    /**
     * 主键
     */
    private Integer bankId;

    /**
     * 银联号
     */
    private String bankNo;

    /**
     * 支行名称
     */
    private String bankName;

    /**
     * 来源（0:银行同步 1:手动添加）
     */
    private Integer source;

    /**
     * 是否删除
     */
    private Integer isDel;

    /**
     * 是否禁用(当银行同步联行号覆盖手动添加联行号时，手动添加联行号则不可用)
     */
    private Integer disableFlag;

}