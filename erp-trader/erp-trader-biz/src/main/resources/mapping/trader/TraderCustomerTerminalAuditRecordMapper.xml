<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCustomerTerminalAuditRecordMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderCustomerTerminalAuditRecordEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD-->
    <id column="TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD_ID" jdbcType="INTEGER" property="traderCustomerTerminalAuditRecordId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="TRADER_CUSTOMER_TERMINAL_ID" jdbcType="INTEGER" property="traderCustomerTerminalId" />
    <result column="AUDIT_STATUS" jdbcType="INTEGER" property="auditStatus" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="AUDIT_USER_ID" jdbcType="INTEGER" property="auditUserId" />
    <result column="AUDIT_USERNAME" jdbcType="VARCHAR" property="auditUsername" />
    <result column="AUDIT_TIME" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="IS_DELETED" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, 
    UPDATER_NAME, TRADER_ID, TRADER_CUSTOMER_ID, TRADER_CUSTOMER_TERMINAL_ID, AUDIT_STATUS, 
    COMMENTS, AUDIT_USER_ID, AUDIT_USERNAME, AUDIT_TIME, IS_DELETED
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD
    where TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD_ID = #{traderCustomerTerminalAuditRecordId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD
    where TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD_ID = #{traderCustomerTerminalAuditRecordId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD_ID" keyProperty="traderCustomerTerminalAuditRecordId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerTerminalAuditRecordEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      TRADER_ID, TRADER_CUSTOMER_ID, TRADER_CUSTOMER_TERMINAL_ID, 
      AUDIT_STATUS, COMMENTS, AUDIT_USER_ID, 
      AUDIT_USERNAME, AUDIT_TIME, IS_DELETED
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{traderId,jdbcType=INTEGER}, #{traderCustomerId,jdbcType=INTEGER}, #{traderCustomerTerminalId,jdbcType=INTEGER}, 
      #{auditStatus,jdbcType=INTEGER}, #{comments,jdbcType=VARCHAR}, #{auditUserId,jdbcType=INTEGER}, 
      #{auditUsername,jdbcType=VARCHAR}, #{auditTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD_ID" keyProperty="traderCustomerTerminalAuditRecordId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerTerminalAuditRecordEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID,
      </if>
      <if test="traderCustomerTerminalId != null">
        TRADER_CUSTOMER_TERMINAL_ID,
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="auditUserId != null">
        AUDIT_USER_ID,
      </if>
      <if test="auditUsername != null">
        AUDIT_USERNAME,
      </if>
      <if test="auditTime != null">
        AUDIT_TIME,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerTerminalId != null">
        #{traderCustomerTerminalId,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="auditUserId != null">
        #{auditUserId,jdbcType=INTEGER},
      </if>
      <if test="auditUsername != null">
        #{auditUsername,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerTerminalAuditRecordEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerTerminalId != null">
        TRADER_CUSTOMER_TERMINAL_ID = #{traderCustomerTerminalId,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="auditUserId != null">
        AUDIT_USER_ID = #{auditUserId,jdbcType=INTEGER},
      </if>
      <if test="auditUsername != null">
        AUDIT_USERNAME = #{auditUsername,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD_ID = #{traderCustomerTerminalAuditRecordId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerTerminalAuditRecordEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      TRADER_CUSTOMER_TERMINAL_ID = #{traderCustomerTerminalId,jdbcType=INTEGER},
      AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      AUDIT_USER_ID = #{auditUserId,jdbcType=INTEGER},
      AUDIT_USERNAME = #{auditUsername,jdbcType=VARCHAR},
      AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP},
      IS_DELETED = #{isDeleted,jdbcType=INTEGER}
    where TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD_ID = #{traderCustomerTerminalAuditRecordId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD_ID" keyProperty="traderCustomerTerminalAuditRecordId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, TRADER_ID, TRADER_CUSTOMER_ID, 
      TRADER_CUSTOMER_TERMINAL_ID, AUDIT_STATUS, COMMENTS, AUDIT_USER_ID, AUDIT_USERNAME, 
      AUDIT_TIME, IS_DELETED)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.traderId,jdbcType=INTEGER}, #{item.traderCustomerId,jdbcType=INTEGER}, #{item.traderCustomerTerminalId,jdbcType=INTEGER}, 
        #{item.auditStatus,jdbcType=INTEGER}, #{item.comments,jdbcType=VARCHAR}, #{item.auditUserId,jdbcType=INTEGER}, 
        #{item.auditUsername,jdbcType=VARCHAR}, #{item.auditTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=INTEGER}
        )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-09-05-->
  <select id="queryLastRejectDesc" resultType="java.lang.String">
    select
    COMMENTS
    from T_TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD
    where TRADER_CUSTOMER_TERMINAL_ID=#{traderCustomerTerminalId,jdbcType=INTEGER}
    and AUDIT_STATUS = 2 and IS_DELETED = 0
    order by TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD_ID desc limit 1
  </select>

<!--auto generated by MybatisCodeHelper on 2023-09-07-->
  <select id="selectByTraderCustomerTerminalId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD
        where TRADER_CUSTOMER_TERMINAL_ID=#{traderCustomerTerminalId,jdbcType=INTEGER}
        and IS_DELETED = 0 order by TRADER_CUSTOMER_TERMINAL_AUDIT_RECORD_ID asc
    </select>
</mapper>