<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="确认通知人" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript">

    function complementTask() {
        if($("input[type='checkbox']").is(':checked') == false){
            layer.alert("未选择通知人：请选择通知人！")
            return;
        }
        $.ajax({
            type: "POST",
            url: "./notice.do",
            data: $('#complement').serialize(),
            dataType:'json',
            success: function(data){
                if (data.code == 0) {
                    layer.close(index);
                    window.parent.location.reload();
                } else {
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }

</script>
<div class="formpublic">
    <form method="post" action="" id="complement">
        <ul>
            <li>
              <c:forEach items="${userList}" var="var" varStatus="index">
               <input type="checkbox" name="userId" value="${var.userId}"/>${var.username}
               </c:forEach>
            </li>
        </ul>
        <div class="add-tijiao tcenter">
            <input type="hidden" name="lendOutId" value="${lendOutId}">
            <button type="button" class="bg-light-green" onclick="complementTask()">提交</button>
            <button class="dele" type="button" id="close-layer">取消</button>
        </div>
    </form>
</div>
<%@ include file="../../common/footer.jsp"%>