package com.vedeng.oa.service.filedelivery.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Data;

/**
 * 导入结果对象
 * <AUTHOR>
 *
 */
@Data
public class FileDeliveryImportResultVO {
	

	/**收件单位*/
	@ExcelProperty(value = "*收件单位", index = 0)
	private String traderName;
	
	/**联系人名称*/
	@ExcelProperty(value = "*联系人", index = 1)
	private String traderContractName;
	
	/**联系电话*/
	@ExcelProperty(value = "*联系电话", index = 2)
	private String mobile;
	
	/**最小区域ID*/
	@ExcelProperty(value = "*区代码", index = 3)
	private String areaId;
	
	/**地址信息（不含省市区）*/
	@ExcelProperty(value = "*联系地址", index = 4)
	private String traderAddressInfo;
	
	/**错误信息*/
	@ExcelIgnore
	private String errorMessage;
}
