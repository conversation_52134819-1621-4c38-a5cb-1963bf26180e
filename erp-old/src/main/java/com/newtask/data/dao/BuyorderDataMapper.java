package com.newtask.data.dao;

import com.newtask.data.dto.BuyorderDataDto;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.vo.BuyorderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description com.newtask.data.dao
 * @Date 2021/10/19 15:34
 */
public interface BuyorderDataMapper {

    /**
     * 根据更新时间取一段时间类采购订单ID
     * @param startTime
     * @param endTime
     * @return
     */
    List<Integer> getBuyorderIdByTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 根据更新时间取一段时间类采购订单详细信息
     * @param startTime
     * @param endTime
     * @return
     */
    List<BuyorderVo> getBuyorderByTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * T_BUYORDER_DATA 插入数据
     * @param id
     * @param nowTime
     */
    void insertBuyorderData(@Param("orderId") Integer id, @Param("nowTime") Long nowTime);

    /**
     * 根据ID查询数据是否存在
     * @param id
     * @return
     */
    int getBuyorderDataById(@Param("orderId")Integer id);


    /**
     * 更新状态
     * @param buyorderDataDtos
     * @param nowTime
     */
    void updateOrderSubStatus(@Param("buyorderDataDtos")List<BuyorderDataDto> buyorderDataDtos,@Param("nowTime") long nowTime);

    /**
     *  根据更新时间取一段时间类采购订单审核状态
     * @param startTime
     * @param endTime
     * @return
     */
    List<BuyorderDataDto> getBuyorderVerifyStatusByTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime);


    /**
     * 更新合同回传状态
     * @param orderIds
     */
    void updateOrderIsContractByTime(@Param("orderIds") List<Integer> orderIds, @Param("nowTime")long nowTime);

    /**
     * 根据更新时间获取已合同回传的采购订单
     * @param startTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getOrderIsContractByTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<Integer> getOrderIsContractByBuyorderId(@Param("bizIds") List<Integer> bizIds);

    /**
     * 根据更新时间获取账期已还订单
     * @param startTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getOrderLackPeriodByTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 更新已偿还账期状态
     * @param orderIds
     * @param nowTime
     */
    void updateOrderLackPeriodByTime(@Param("orderIds") List<Integer> orderIds, @Param("nowTime")long nowTime);


    /**
     * 根据更新时间获取账期已还订单
     * @param startTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getOrderIsFinanceAlreadyByTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 更新付款申请到财务状态：0、否，1、是
     * @param buyorderDataDtos
     * @param nowTime
     */
    void updateOrderIsFinanceAlreadyByTime(@Param("buyorderDataDtos")List<BuyorderDataDto> buyorderDataDtos,@Param("nowTime") long nowTime);


    /**
     * 获取财务人员姓名集合
     * @return
     */
    List<String> getFinanceUserNameList();


    /**
     * 根据时间获取是否全部添加物流信息
     * @param startTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getOrderIsAllLogisticsByTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 更新是否全部添加物流信息状态
     * @param buyorderDataDtos
     * @param nowTime
     */
    void updateOrderIsAllLogisticsByTime(@Param("buyorderDataDtos")List<BuyorderDataDto> buyorderDataDtos,@Param("nowTime") long nowTime);

    /**
     * 根据时间获取未录票订单
     * @param startTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getRecordInvoiceApplyOneByTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 根据时间获取部分录票订单
     * @param startTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getRecordInvoiceApplyTwoByTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 根据时间获取已录票审核中的订单
     * @param startTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getRecordInvoiceApplyThreeByTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 更新录票申请状态
     * @param buyorderDataDtos
     * @param nowTime
     */
    void updateOrderRecordInvoiceApplyStatusByTime(@Param("buyorderDataDtos")List<BuyorderDataDto> buyorderDataDtos,@Param("nowTime") long nowTime);


    /**
     * 根据时间获取有流水信息的采购订单
     * @param startTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getOrderLackAccountPeriodAmountByTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 更新订单未还账期款
     * @param buyorderDataDtos
     * @param nowTime
     */
    void updateOrderLackAccountPeriodAmount(@Param("buyorderDataDtos")List<BuyorderDataDto> buyorderDataDtos,@Param("nowTime") long nowTime);

    void updateContractReturnStatus(@Param("buyorderId") Integer buyorderId, @Param("status") Integer status);

    /**
     * 根据采购订单ID查询合同回传状态
     * @param buyorderId 采购订单ID
     * @return 合同回传状态：0-否，1-是
     */
    Integer getContractReturnStatusByBuyorderId(@Param("buyorderId") Integer buyorderId);
}
