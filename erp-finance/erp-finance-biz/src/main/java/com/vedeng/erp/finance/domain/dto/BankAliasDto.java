package com.vedeng.erp.finance.domain.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Data;

/**
    * 银行别名表
    */
@Data
public class BankAliasDto extends BaseDto {
    /**
    * 主键
    */
    private Long bankReceiptAliasId;

    /**
    * 银行ID
    */
    private Integer bankId;

    /**
    * 别名
    */
    private String alias;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 更新备注
    */
    private String updateRemark;
}
