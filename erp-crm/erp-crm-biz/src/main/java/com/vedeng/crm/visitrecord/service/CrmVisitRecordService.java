package com.vedeng.crm.visitrecord.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.crm.visitrecord.domain.dto.*;
import com.vedeng.crm.visitrecord.domain.entity.CrmVisitRecordEntity;
import com.vedeng.crm.visitrecord.domain.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmVisitRecordService {

    CrmVisitRecordEntity selectByPrimaryKey(Integer id );

    List<VisitRecordVo> selectVisitRecordByRelateNo(String relateNo, Integer relateType);

    List<VisitRecordVo> selectVisitRecordByRelateId(Integer relateId, Integer relateType);
    
    PageInfo<VisitRecordVo> visitRecordPage(PageParam<VisitRecordQueryDto> pageParam);

    VisitUserDetailForPageVo pageDetail(CurrentUser currentUser);

    /**
     * 判断是否检查权限
     * @param id
     * @param currentUser
     * @param checkPermission
     * @return
     */
    VisitRecordVo detail(Integer id,CurrentUser currentUser,boolean checkPermission);


    VisitRecordVo detail(Integer id,CurrentUser currentUser);

    /**
     * 检查拜访记录是否可查看
     * @param visitRecordVo
     * @param currentUser
     * @return
     */
    boolean checkVisitRecordVisable(VisitRecordVo visitRecordVo,CurrentUser currentUser);


    /**
     * 保存或更新拜访记录
     */
    VisitRecordVo saveOrUpdate(CurrentUser currentUser, VisitRecordInputDto inputDto);

    void dealVisitInputDto(VisitRecordInputDto inputDto);

    VisitRecordVo saveNextVisitRecord(CurrentUser currentUser, VisitRecordInputDto inputDto);



    void saveBusinessChanceForVisit(VisitRecordInputDto.RelateBizData relateBizData,Integer visitId,CurrentUser currentUser);


    /**
     * 选择性更新拜访记录
     * @param currentUser 当前用户
     * @param editDto 更新的数据
     */
    void updateSelective(CurrentUser currentUser, VisitEditInputDto editDto);

    /**
     * 关闭拜访记录
     * @param currentUser 当前用户
     * @param closeDto 关闭的数据
     */
    void closeVisitRecord(CurrentUser currentUser, VisitCloseDto closeDto);

    /**
     * 检查手机号是否注册贝登商城
     * @param mobile 手机号
     * @return true:已注册 false:未注册
     */
    Boolean checkMobileExists(String mobile);

    /**
     * 查询客户的交易信息
     * @param traderId
     * @param traderCustomerId
     * @param traderName
     * @return
     */
    VisitCustomerVo getVisitCustomerForVisit(Integer traderId,Integer traderCustomerId,String traderName,String tycFlag);


    /**
     * 判断客户是否可点击
     * @param traderId
     * @return
     */
    String checkVisitCustomerCanClick(Integer traderId);
    /**
     * 拜访当天通知信息
     */
    void remindVisitToday();

    /**
     * 拜访当天未打卡
     */
    void sendTodayVisitNotCardReminder();

    /**
     * 拜访人昨天打卡了，但是没有填写拜访记录
     * 即-最近一次打卡时间是昨天，但拜访您计划状态为拜访中（拜访中即为没有填写拜访记录）
     */
    void sendYestodayNotRecordReminder();

} 