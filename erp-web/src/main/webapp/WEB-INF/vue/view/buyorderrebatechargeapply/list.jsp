<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<div id="app" style="display: none;">
    <template>
        <el-form :inline="true" :model="buyOrderRebateChargeApply" ref="form">
            <el-form-item label="关键字：">
                <el-input v-model="buyOrderRebateChargeApply.keyWords" placeholder="供应商名称、编号" style="width: 200px"></el-input>
            </el-form-item>

            <el-form-item label="品牌：">
                <el-select
                        v-model="buyOrderRebateChargeApply.chooseBrandIds"
                        multiple
                        filterable
                        remote
                        collapse-tags
                        reserve-keyword
                        placeholder="请输入关键词"
                        :remote-method="remoteMethod"
                        @change="handleSelectChange"
                        :loading="loading">
                    <el-option
                            v-for="item in brandOptions"
                            :key="item.brandId"
                            :label="item.brandName"
                            :value="item.brandId">
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="产线负责人：">
                <el-select v-model="buyOrderRebateChargeApply.headUserId" placeholder="请选择" ref="headUserIdRef" clearable="true" filterable>
                    <el-option
                            v-for="item in headUserList"
                            :key="item.userId"
                            :label="item.username"
                            :value="item.userId">
                    </el-option>
                </el-select>
            </el-form-item>


            <el-form-item label="使用期限：">
                <el-select v-model="buyOrderRebateChargeApply.usePeriodStatus" clearable="true">
                    <el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="状态：">
                <el-select v-model="buyOrderRebateChargeApply.auditStatus" placeholder="请选择" clearable="true">
                    <el-option key="-1" label="全部" value="-1"></el-option>
                    <el-option key="0" label="审核中" value="0"></el-option>
                    <el-option key="1" label="审核通过" value="1"></el-option>
                    <el-option key="2" label="审核不通过" value="2"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item>
                <el-button type="primary" @click="doSearch()">搜索</el-button>
                <el-button type="primary" plain @click="reset()">重置</el-button>
            </el-form-item>
        </el-form>

        <el-row style="margin-bottom: 20px; text-align: left">
            <el-button type="primary" @click="addRebateChargeApply()">新增</el-button>
        </el-row>

        <el-table
                :data="rebateChargeApplyDtoList"
                ref="rebateChargeApplyDto"
                border
                style="width: 100%; "
                key="rebateChargeApplyDto">
            <el-table-column
                    align="center"
                    text-align="center"
                    label="单据编号">
                <template slot-scope="scope">
                    <el-link type="primary" :underline="false" @click="viewDetail(scope.row.buyOrderRebateChargeId)">{{scope.row.buyOrderRebateChargeNo}}</el-link>
                </template>
            </el-table-column>
            <el-table-column
                    align="center"
                    text-align="center"
                    prop="traderSupplierName"
                    label="供应商名称">
            </el-table-column>
            <el-table-column
                    align="center"
                    text-align="center"
                    prop="brandName"
                    label="品牌">
                <template slot-scope="scope">
                    <span class="ellipsis-span" :title="scope.row.brandName" @mouseover="showFullText = true" @mouseleave="showFullText = false">
                        {{scope.row.brandName}}
                    </span>
                </template>
            </el-table-column>
            <el-table-column
                    align="center"
                    text-align="center"
                    label="返利周期">
                <template slot-scope="scope">
                    <span>{{parseTime(scope.row.cycleBeginTime, '{y}-{m}-{d}')}} ~ {{parseTime(scope.row.cycleEndTime, '{y}-{m}-{d}')}}</span>
                </template>
            </el-table-column>

            <el-table-column
                    align="center"
                    text-align="center"
                    label="使用期限">
                <template slot-scope="scope">
                    <span v-if="parseTime(new Date(), '{y}-{m}-{d}') <= parseTime(new Date(scope.row.usePeriodEndTime), '{y}-{m}-{d}') ">{{parseTime(scope.row.usePeriodBeginTime, '{y}-{m}-{d}')}} ~ {{parseTime(scope.row.usePeriodEndTime, '{y}-{m}-{d}')}}</span>
                    <span v-else style="color: red">{{parseTime(scope.row.usePeriodBeginTime, '{y}-{m}-{d}')}} ~ {{parseTime(scope.row.usePeriodEndTime, '{y}-{m}-{d}')}} (已过期)</span>
                </template>
            </el-table-column>
            <el-table-column
                    align="center"
                    text-align="center"
                    label="结算金额">
                <template slot-scope="scope">
                    <span>{{Number(scope.row.totalAmount).toFixed(2)}}</span>
                </template>
            </el-table-column>
            <el-table-column
                    align="center"
                    text-align="center"
                    prop="headUserName"
                    label="产线负责人">
            </el-table-column>
            <el-table-column
                    align="center"
                    text-align="center"
                    label="确认函">
                <template slot-scope="scope">
                    <el-link type="primary" :underline="false" v-for="(item,index) in scope.row.billFileList" @click="downloadAttachment(item.wholeUrl, item.suffix)">
                        <span class="ellipsis-span" :title="item.name" @mouseover="showFullText = true" @mouseleave="showFullText = false">
                            {{ index == scope.row.billFileList.length-1 && item.name || (item.name + ',') }}
                        </span>
                    </el-link>
                </template>
            </el-table-column>
            <el-table-column
                    align="center"
                    text-align="center"
                    label="结算单明细">
                <template slot-scope="scope">
                    <el-link type="primary" :underline="false" v-for="(item,index) in scope.row.detailFileList" @click="downloadAttachment(item.wholeUrl, item.suffix)">
                        <span class="ellipsis-span" :title="item.name" @mouseover="showFullText = true" @mouseleave="showFullText = false">
                            {{ index == scope.row.detailFileList.length-1 && item.name || (item.name + ',') }}
                        </span>
                    </el-link>
                </template>
            </el-table-column>
            <el-table-column
                    align="center"
                    text-align="center"
                    prop="remark"
                    label="备注">
            </el-table-column>
            <el-table-column
                    align="center"
                    text-align="center"
                    label="状态">
                <template slot-scope="scope">
                    <span v-if="scope.row.auditStatus == 0">审核中</span>
                    <span v-else-if="scope.row.auditStatus == 1">审核通过</span>
                    <span v-else-if="scope.row.auditStatus == 2">审核不通过</span>
                    <span v-else>-</span>
                </template>
            </el-table-column>
            <el-table-column
                    align="center"
                    text-align="center"
                    prop="creatorName"
                    label="创建人">
            </el-table-column>
            <el-table-column
                    align="center"
                    text-align="center"
                    label="创建时间">
                <template slot-scope="scope">
                    {{parseTime(scope.row.addTime)}}
                </template>
            </el-table-column>
            <el-table-column
                    align="center"
                    text-align="center"
                    label="操作">
                <template slot-scope="scope">
                    <el-button v-if="scope.row.showCheckButton" type="text" @click="auditOperation(true, scope.row.buyOrderRebateChargeId, scope.row.taskId)">通过</el-button>
                    <el-button v-if="scope.row.showCheckButton" type="text" @click="auditOperation(false, scope.row.buyOrderRebateChargeId, scope.row.taskId)">不通过</el-button>
                    <el-button v-if="!scope.row.showCheckButton" type="text">——</el-button>
                </template>
            </el-table-column>
        </el-table>

        <el-dialog title="操作确认" :visible.sync="dialogFormVisible" width="500px">
            <el-form :model="auditForm" :rules="rules" ref="form">
                <el-form-item label="备注" prop="comments">
                    <el-input v-model="auditForm.comments" autocomplete="off" style="width: 85%; height: 26px"></el-input>
                </el-form-item>

                <el-form-item style="text-align: center; margin-top: 10px">
                    <el-button style="background-color: #72BB72; color: #FFFFFF" size="mini" @click="submitAudit('form')">提交</el-button>
                    <el-button style="background-color: #FFAA02; color: #FFFFFF" size="mini" @click="dialogFormVisible = false">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>

        <el-pagination
                style="text-align: center; margin-top: 25px"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPageNo"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="currentSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="totalLines">
        </el-pagination>
    </template>
</div>
<script src="${pageContext.request.contextPath}/static/api/buyorder/buyOrderRebateChargeApply.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">
    new Vue({
        el: '#app',
        data() {
            return {
                text: '这是一个超过10个字符的文本',
                showFullText: false,
                buyOrderRebateChargeApply: {
                    keyWords: '',
                    usePeriodStatus: 0,
                    chooseBrandIds: [],
                    auditStatus: '-1'
                },
                currentSize: 10,
                currentPageNo: 1,
                totalLines: 0,
                rebateChargeApplyDtoList: [],
                options: [{
                    value: 0,
                    label: '全部'
                }, {
                    value: 1,
                    label: '已过期'
                }, {
                    value: 2,
                    label: '未过期'
                }],

                auditForm: {
                    comments: ''
                },
                rules: {
                    comments: [
                        {required: true, message: '请填写备注！', trigger: 'blur'},
                        {max: 256, message: '备注内容不允许超过256个字符', trigger: 'blur' }
                    ]
                },
                dialogFormVisible: false,
                globalPassFlag: false,
                buyOrderRebateChargeId : 0,
                globalTaskId: null,
                headUserList: [],
                brandOptions: [],
                loading: false
            }
        },

        computed: {
            truncatedText() {
                if (this.showFullText) {
                    return this.text;
                } else {
                    if (this.text.length > 10) {
                        return this.text.slice(0, 10) + '...';
                    } else {
                        return this.text;
                    }
                }
            }
        },

        mounted() {
            loadingApp();
        },

        created() {
            this.doSearch();
            this.initUser();
        },

        methods: {
            initUser() {
                getAllHeadUser({"positionType" : 311}).then(res => {
                    this.headUserList = res.data.data;
                })
            },

            doSearch() {
                var pageParam = {
                    "pageSize": this.currentSize,
                    "pageNum": this.currentPageNo,
                    "param": {
                        keyWords: this.buyOrderRebateChargeApply.keyWords,
                        usePeriodStatus: this.buyOrderRebateChargeApply.usePeriodStatus,
                        brandIds : this.buyOrderRebateChargeApply.chooseBrandIds != null ? this.buyOrderRebateChargeApply.chooseBrandIds.join(",") : null,
                        headUserId: this.buyOrderRebateChargeApply.headUserId,
                        auditStatus: this.buyOrderRebateChargeApply.auditStatus
                    }
                };

                page(pageParam).then(res => {
                    this.rebateChargeApplyDtoList = res.data.data.list;
                    this.totalLines = res.data.data.total;
                })
            },

            remoteMethod(query) {
                if (query !== '') {
                    this.loading = true;
                    getBrand({"brandName" : query}).then(res => {
                        this.brandOptions = res.data.data;
                        this.loading = false;
                    })
                } else {
                    this.brandOptions = [];
                }
            },

            handleSelectChange() {
                // 如果已选选项数量超过 10，只保留前 10 个选项
                if (this.buyOrderRebateChargeApply.chooseBrandIds.length > 10) {
                    this.buyOrderRebateChargeApply.chooseBrandIds = this.buyOrderRebateChargeApply.chooseBrandIds.slice(0, 10);
                }
            },

            // 重置
            reset() {
                this.buyOrderRebateChargeApply = {
                    keyWords: '',
                    usePeriodStatus: 0,
                    auditStatus: '-1'
                }
            },

            // 跳转到详情页
            viewDetail(id) {
                openTab("返利申请详情", '/buyOrder/rebateChargeApply/detail.do?buyOrderRebateChargeId=' + id);
            },

            handleSizeChange(val) {
                this.currentSize = val;
                this.currentPageNo = 1;
                this.doSearch();
            },
            handleCurrentChange(val) {
                this.currentPageNo = val;
                this.doSearch();
            },

            // 新增按钮
            addRebateChargeApply() {
                openTab("新增结算单", "/buyOrder/rebateChargeApply/edit.do");
            },

            downloadAttachment(url, suffix) {
                if (suffix == 'jpg' || suffix == 'pdf' || suffix == 'png') {
                    // 预览
                    window.open(url);
                } else {
                    // 下载
                    window.open(url.replace("display", "download"));
                }
            },

            auditOperation(pass, buyOrderRebateChargeId, taskId) {
                this.buyOrderRebateChargeId = buyOrderRebateChargeId;
                this.globalTaskId = taskId;
                if (pass) {
                    this.rules.comments = [
                        {required: false, trigger: 'blur'}
                    ]
                } else {
                    this.rules.comments = [
                        {required: true, message: '请填写备注！', trigger: 'blur'},
                        {max: 256, message: '备注内容不允许超过256个字符', trigger: 'blur' }
                    ]
                }

                this.globalPassFlag = pass;
                this.auditForm.comments = '';
                this.dialogFormVisible = true;
            },


            // 审核操作
            submitAudit(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        let auditVariables = {
                            taskId: this.globalTaskId,
                            comments: this.auditForm.comments,
                            pass: this.globalPassFlag,
                            businessId: this.buyOrderRebateChargeId
                        };
                        doAudit(auditVariables).then(res => {
                            if (res.data.code == 0) {
                                this.dialogFormVisible = false;
                                this.buyOrderRebateChargeId = 0;
                                this.doSearch();
                            } else {
                                this.$message.error(res.data.message);
                            }
                        });
                    } else {
                        return false;
                    }
                });
            }
        }
    })

</script>

<style>
    .ellipsis-span {
        display: inline-block;
        max-width: 100px; /* 设置适当的宽度 */
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        cursor: pointer;
    }
</style>

</body>
</html>