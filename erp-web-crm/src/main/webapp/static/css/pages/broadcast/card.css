.vd-ui-rank-card {
  border-radius: 5px;
  background: #fff;
  padding: 20px;
  height: 703px;
}
.vd-ui-rank-card .rank-card-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.vd-ui-rank-card .rank-card-top .card-title {
  font-size: 16px;
  font-weight: 700;
  color: #333;
}
.vd-ui-rank-card .rank-card-top .card-filter {
  display: flex;
  align-items: center;
}
.vd-ui-rank-card .rank-card-top .card-filter .filter-item {
  height: 24px;
  font-size: 14px;
  color: #333;
  line-height: 24px;
  border-radius: 12px;
  padding: 0 10px;
  margin-right: 10px;
  cursor: pointer;
}
.vd-ui-rank-card .rank-card-top .card-filter .filter-item:last-child {
  margin-right: 0;
}
.vd-ui-rank-card .rank-card-top .card-filter .filter-item:hover {
  background: #FFEDE0;
  color: #F60;
}
.vd-ui-rank-card .rank-card-top .card-filter .filter-item.active {
  background: #f60 !important;
  color: #fff !important;
  cursor: auto;
}
.vd-ui-rank-card .rank-card-signature {
  height: 160px;
  margin-top: 20px;
}
.vd-ui-rank-card .rank-card-signature > img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 5px;
}
.vd-ui-rank-card .rank-card-list {
  margin-top: 20px;
}
.vd-ui-rank-card .rank-card-list .rank_front-three {
  display: flex;
  margin: 0 -5px;
}
.vd-ui-rank-card .rank-card-list .rank_front-three.center {
  justify-content: center;
}
.vd-ui-rank-card .rank-card-list .rank_front-three .front-three-item {
  width: calc(33.33% - 10px);
  flex-shrink: 0;
  margin: 0 5px;
  position: relative;
  padding-top: 35px;
}
.vd-ui-rank-card .rank-card-list .rank_front-three .front-three-item .avatar-wrap {
  position: absolute;
  top: 10px;
  left: 50%;
  margin-left: -25px;
}
.vd-ui-rank-card .rank-card-list .rank_front-three .front-three-item .avatar-wrap > .img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
}
.vd-ui-rank-card .rank-card-list .rank_front-three .front-three-item .avatar-wrap .level {
  width: 24px;
  height: 24px;
  position: absolute;
  right: -9px;
  top: -9px;
}
.vd-ui-rank-card .rank-card-list .rank_front-three .front-three-item .front-three-cup {
  height: 111px;
  text-align: center;
  font-size: 16px;
  color: #333;
  border-radius: 5px;
  padding-top: 30px;
}
.vd-ui-rank-card .rank-card-list .rank_front-three .front-three-item .front-three-cup .group {
  font-size: 12px;
  color: #999;
}
.vd-ui-rank-card .rank-card-list .rank_front-three .front-three-item .front-three-cup .price {
  font-weight: 700;
}
.vd-ui-rank-card .rank-card-list .rank_front-three .front-three-item.level2 .front-three-cup {
  background: linear-gradient(180deg, #f5f5f5 0%, #ffffff 100%);
}
.vd-ui-rank-card .rank-card-list .rank_front-three .front-three-item.level1 {
  position: relative;
  top: -10px;
}
.vd-ui-rank-card .rank-card-list .rank_front-three .front-three-item.level1 .front-three-cup {
  background: linear-gradient(180deg, #fff8ed 0%, #ffffff 100%);
}
.vd-ui-rank-card .rank-card-list .rank_front-three .front-three-item.level3 .front-three-cup {
  background: linear-gradient(180deg, #fff1ea 0%, #ffffff 100%);
}
.vd-ui-rank-card .rank-card-list .user-rank-list {
  padding: 0 5px;
  margin-top: 10px;
}
.vd-ui-rank-card .rank-card-list .user-rank-list .rank-item {
  display: flex;
  align-items: center;
  color: #333;
  margin-top: 20px;
}
.vd-ui-rank-card .rank-card-list .user-rank-list .rank-item:first-child {
  margin-top: 5px;
}
.vd-ui-rank-card .rank-card-list .user-rank-list .rank-item .num {
  width: 22px;
  height: 22px;
  flex-shrink: 0;
  border-radius: 50%;
  background: #ebeff2;
  margin-right: 10px;
  font-size: 12px;
  text-align: center;
  line-height: 22px;
}
.vd-ui-rank-card .rank-card-list .user-rank-list .rank-item .name {
  flex: 1;
  min-width: 0;
  margin-right: 10px;
  font-size: 16px;
}
.vd-ui-rank-card .rank-card-list .user-rank-list .rank-item .group {
  width: 36%;
  min-width: 131px;
  flex-shrink: 0;
  margin-right: 10px;
  font-size: 12px;
  color: #999;
}
.vd-ui-rank-card .rank-card-list .user-rank-list .rank-item .price {
  width: 23%;
  min-width: 82px;
  flex-shrink: 0;
  font-size: 14px;
  font-weight: 700;
  text-align: right;
}
.vd-ui-rank-card .group-rank-list {
  padding-top: 5px;
}
.vd-ui-rank-card .group-rank-list .rank-item {
  display: flex;
  align-items: center;
  padding-right: 30px;
  margin-top: 20px;
}
.vd-ui-rank-card .group-rank-list .rank-item.before-three {
  height: 34px;
  padding: 5px 30px 5px 0;
  margin-top: 15px;
}
.vd-ui-rank-card .group-rank-list .rank-item.bg0 {
  background: url('../../../image/broadcast/bg1.png') no-repeat;
  background-size: 100% 100%;
}
.vd-ui-rank-card .group-rank-list .rank-item.bg1 {
  background: url('../../../image/broadcast/bg2.png') no-repeat;
  background-size: 100% 100%;
}
.vd-ui-rank-card .group-rank-list .rank-item.bg2 {
  background: url('../../../image/broadcast/bg3.png') no-repeat;
  background-size: 100% 100%;
}
.vd-ui-rank-card .group-rank-list .rank-item .rank {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}
.vd-ui-rank-card .group-rank-list .rank-item .num {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  background: #ebeff2;
  margin-right: 11px;
  margin-left: 1px;
  font-size: 12px;
  text-align: center;
  line-height: 22px;
}
.vd-ui-rank-card .group-rank-list .rank-item .group {
  flex: 1;
  min-width: 0;
  margin-right: 5px;
  font-size: 14px;
  color: #333;
}
.vd-ui-rank-card .group-rank-list .rank-item .process-wrap {
  width: 105px;
  display: flex;
  align-items: center;
  margin-right: 5px;
}
.vd-ui-rank-card .group-rank-list .rank-item .process-wrap .process-line {
  width: 65px;
  height: 5px;
  border-radius: 3px;
  background: #EBEFF2;
}
.vd-ui-rank-card .group-rank-list .rank-item .process-wrap .process-line .process {
  width: 60px;
  height: 100%;
  border-radius: 3px;
  background: #59D25A;
}
.vd-ui-rank-card .group-rank-list .rank-item .process-wrap .process-num {
  width: 45px;
  margin-left: 5px;
  font-size: 16px;
  font-weight: 700;
  text-align: right;
}
.vd-ui-rank-card .group-rank-list .rank-item .price {
  width: 74px;
  font-size: 14px;
  font-weight: 700;
  text-align: right;
}
.vd-ui-rank-card .group-rank-list.rank-percent .rank-item {
  padding-right: 15px;
}
.vd-ui-rank-card .group-rank-list.rank-percent .rank-item.before-three {
  padding-right: 15px;
}
.vd-ui-rank-card .group-rank-list.rank-percent .price {
  font-size: 12px;
  color: #999;
  font-weight: normal;
  width: 74px;
}
.vd-ui-rank-card .card-empty {
  padding-top: 200px;
  text-align: center;
  font-size: 14px;
}
.vd-ui-rank-card .card-empty .icon-info1 {
  font-size: 40px;
  margin-bottom: 10px;
  color: #666;
}
@media screen and (max-width: 768px) {
  .vd-ui-rank-card {
    padding: 15px;
    height: auto;
  }
  .vd-ui-rank-card .card-empty {
    padding: 90px 0;
  }
  .vd-ui-rank-card .rank-card-top .card-filter .filter-item {
    margin-right: 5px;
  }
  .vd-ui-rank-card .rank-card-top .card-filter .filter-item:hover {
    background: none;
    color: #333;
  }
  .vd-ui-rank-card .rank-card-signature {
    height: 0;
    margin-top: 15px;
    padding-top: 42.77%;
    position: relative;
  }
  .vd-ui-rank-card .rank-card-signature > img {
    height: auto;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  .vd-ui-rank-card .rank-card-list .rank_front-three .front-three-item .front-three-cup {
    height: 108px;
    font-size: 14px;
    color: #000;
  }
  .vd-ui-rank-card .rank-card-list .rank_front-three .front-three-item .front-three-cup .price {
    font-size: 14px;
    font-weight: 700;
  }
  .vd-ui-rank-card .rank-card-list .user-rank-list {
    margin-top: 0;
  }
  .vd-ui-rank-card .rank-card-list .user-rank-list .rank-item {
    margin-top: 15px;
    color: #000;
  }
  .vd-ui-rank-card .rank-card-list .user-rank-list .rank-item .name {
    font-size: 14px;
    margin-right: 5px;
  }
  .vd-ui-rank-card .rank-card-list .user-rank-list .rank-item .group {
    width: 105px;
    min-width: auto;
    margin-right: 5px;
  }
  .vd-ui-rank-card .rank-card-list .user-rank-list .rank-item .price {
    font-size: 14px;
    min-width: auto;
    width: 82px;
  }
  .vd-ui-rank-card .group-rank-list {
    padding-top: 5px;
  }
  .vd-ui-rank-card .group-rank-list .rank-item {
    margin-top: 15px;
    padding-right: 15px;
  }
  .vd-ui-rank-card .group-rank-list .rank-item.before-three {
    padding: 8px 15px 8px 0;
    margin-top: 10px;
  }
  .vd-ui-rank-card .group-rank-list .rank-item .group {
    margin-right: 5px;
    color: #000;
  }
  .vd-ui-rank-card .group-rank-list .rank-item .process-wrap {
    width: 42px;
    min-width: auto;
  }
  .vd-ui-rank-card .group-rank-list .rank-item .process-wrap .process-line {
    display: none;
  }
  .vd-ui-rank-card .group-rank-list .rank-item .process-wrap .process-num {
    width: 42px;
    font-size: 15px;
    text-align: right;
    margin-left: 0;
  }
}
@media screen and (min-width: 320px) and (max-width: 359px) {
  .vd-ui-rank-card .rank-card-list .rank_front-three .front-three-item .front-three-cup {
    height: 95px;
    font-size: 12px;
  }
  .vd-ui-rank-card .rank-card-list .rank_front-three .front-three-item .front-three-cup .price {
    font-size: 12px;
  }
  .vd-ui-rank-card .rank-card-list .user-rank-list .rank-item .name {
    font-size: 12px;
  }
  .vd-ui-rank-card .rank-card-list .user-rank-list .rank-item .group {
    width: 105px;
  }
  .vd-ui-rank-card .rank-card-list .user-rank-list .rank-item .price {
    font-size: 12px;
    width: 62px;
  }
  .vd-ui-rank-card .group-rank-list .rank-item .group {
    font-size: 12px;
  }
  .vd-ui-rank-card .group-rank-list .rank-item .process-wrap {
    width: 30px;
  }
  .vd-ui-rank-card .group-rank-list .rank-item .process-wrap .process-num {
    width: 30px;
    font-size: 13px;
  }
}
