package com.vedeng.mobile.system.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.base.Page;
import com.vedeng.mobile.system.dto.TraderCustomerInputDto;
import com.vedeng.mobile.system.dto.TraderCustomerSearchApiDto;
import com.vedeng.mobile.system.dto.TraderCustomerSearchResultDto;
import com.vedeng.mobile.system.model.WebAccount;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/6
 */
public interface TraderCustomerSearchService {


    List<TraderCustomerSearchResultDto> searchTraderCustomerByName(String customerName);


    /**
     * 查询客户列表-6个大区
     * @param traderCustomerVo
     * @param page
     * @return
     */
    PageInfo<TraderCustomerSearchResultDto> searchTraderCustomerListPage(TraderCustomerSearchApiDto traderCustomerVo, Page page);

    PageInfo<TraderCustomerSearchResultDto> searchTraderCustomerListPageForYingji(TraderCustomerSearchApiDto traderCustomerVo, Page page);


    PageInfo<TraderCustomerSearchResultDto> searchTraderCustomerListPageForFeigong(TraderCustomerSearchApiDto traderCustomerVo, Page page);

    /**
     * 查询客户列表-终端库
     * @param terminalName
     * @return
     */
    PageInfo<TraderCustomerSearchResultDto> getOneDataTerminalInfo(String terminalName, Integer pageSize, Integer pageNum);

    boolean checkTraderCanSelected(TraderCustomerInputDto inputDto);




    /**
     * 分页查询天眼查终端信息
     *
     * @param terminalName 终端名称
     * @param pageSize     页大小
     * @param pageNum      当前页
     * @return PageResult<OrderTerminalDto>
     */
    PageInfo<TraderCustomerSearchResultDto> getTycTerminalInfo(String terminalName, Integer pageSize, Integer pageNum);

    /**
     * 查询客户名下的注册联系人手机号
     * @param traderId
     * @return
     */
    List<WebAccount> queryWebAccountByTraderId(Integer traderId);
    /**
     *
     * @param customerName
     * @return
     */
    Integer getTraderWorkAreaByCustomerName(String customerName);


}

