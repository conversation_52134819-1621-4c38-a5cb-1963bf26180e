<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchBuyorderExpenseItemDtoMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchBuyorderExpenseItemDto">
        <!--@mbg.generated-->
        <!--@Table T_BUYORDER_EXPENSE_ITEM-->
        <id column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId"/>
        <result column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId"/>
        <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus"/>
        <result column="ARRIVAL_TIME" jdbcType="TIMESTAMP" property="arrivalTime"/>
        <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus"/>
        <result column="DELIVERY_TIME" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="INVOICE_STATUS" jdbcType="INTEGER" property="invoiceStatus"/>
        <result column="INVOICE_TIME" jdbcType="TIMESTAMP" property="invoiceTime"/>
        <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId"/>
        <result column="NUM" jdbcType="INTEGER" property="num"/>
        <result column="preciseNum" jdbcType="DECIMAL" property="preciseNum"/>
        <association property="buyorderExpenseItemDetailDto"
                     javaType="com.vedeng.erp.kingdee.batch.dto.BatchBuyorderExpenseItemDetailDto">
            <id column="BUYORDER_EXPENSE_ITEM_DETAIL_ID" jdbcType="INTEGER" property="buyorderExpenseItemDetailId"/>
            <result column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId"/>
            <result column="SKU" jdbcType="VARCHAR" property="sku"/>
            <result column="EXPENSE_CATEGORY_ID" jdbcType="INTEGER" property="expenseCategoryId"/>
            <result column="EXPENSE_CATEGORY_NAME" jdbcType="VARCHAR" property="expenseCategoryName"/>
            <result column="UNIT_KING_DEE_NO" jdbcType="VARCHAR" property="unitKingDeeNo"/>
        </association>
    </resultMap>

    <!--auto generated by MybatisCodeHelper on 2022-12-05-->
    <select id="findByBuyorderExpenseItemIdIn" resultMap="BaseResultMap">
        select TBEI.BUYORDER_EXPENSE_ITEM_ID,
        BUYORDER_EXPENSE_ID,
        ARRIVAL_STATUS,
        ARRIVAL_TIME,
        DELIVERY_STATUS,
        DELIVERY_TIME,
        INVOICE_STATUS,
        INVOICE_TIME,
        GOODS_ID,
        NUM,
        NUM as preciseNum,
        TBEID.BUYORDER_EXPENSE_ITEM_DETAIL_ID,
        TBEID.SKU,
        TBEID.EXPENSE_CATEGORY_ID,
        TBEID.EXPENSE_CATEGORY_NAME,
        TSCC.UNIT_KING_DEE_NO
        from T_BUYORDER_EXPENSE_ITEM TBEI
        left join T_BUYORDER_EXPENSE_ITEM_DETAIL TBEID
        on TBEI.BUYORDER_EXPENSE_ITEM_ID = TBEID.BUYORDER_EXPENSE_ITEM_ID
        left join T_SYS_COST_CATEGORY TSCC on TSCC.COST_CATEGORY_ID =  TBEID.EXPENSE_CATEGORY_ID
        where TBEI.BUYORDER_EXPENSE_ITEM_ID in
        <foreach item="item" index="index" collection="buyorderExpenseItemIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="selectUnitKingDeeNo" resultType="java.lang.String">
        selecT TSCC.UNIT_KING_DEE_NO from V_CORE_SKU VCS left join  T_SYS_COST_CATEGORY TSCC on VCS.COST_CATEGORY_ID = TSCC.COST_CATEGORY_ID where VCS.SKU_NO = #{sku,jdbcType=VARCHAR}
    </select>
</mapper>