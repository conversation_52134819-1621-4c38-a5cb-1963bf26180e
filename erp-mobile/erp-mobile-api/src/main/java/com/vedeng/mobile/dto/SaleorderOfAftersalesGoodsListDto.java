package com.vedeng.mobile.dto;

import lombok.Data;

import java.math.BigDecimal;


/**
 * 销售订单对应的售后订单列表查询类
 */
@Data
public class SaleorderOfAftersalesGoodsListDto {

    /**
     * 售后商品主键id
     */
    private Integer afterSalesGoodsId;

    /**
     * 售后订单主键id
     */
    private Integer afterSalesId;

    /**
     * 销售/采购订单详情ID
     */
    private Integer orderDetailId;

    /**
     * 产品类型0普通产品1特殊产品（手续费）
     */
    private Integer goodsType;

    /**
     * 产品ID
     */
    private Integer goodsId;

    /**
     * 售后数量
     */
    private Integer num;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * SKU
     */
    private String SKU;

    /**
     * 商品名称
     */
    private String goodsName;

}
