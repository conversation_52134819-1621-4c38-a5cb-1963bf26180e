package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.SaleOrderAfterSaleBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 销售售后入库赠品 应付单推送
 * @date 2023/8/31 15:34
 **/
@JobHandler(value = "SaleOrderAfterSaleGiftPayTask")
@Component
public class SaleOrderAfterSaleGiftPayTask extends AbstractJobHandler {
    @Autowired
    private JobLauncher jobLauncher;
    @Autowired
    SaleOrderAfterSaleBatchJob batchJob;
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================SaleOrderAfterSaleGiftPayTask开始====================");
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = batchJob.saleGiftPayAfterSale();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("==================SaleOrderAfterSaleGiftPayTask结束====================");
        return SUCCESS;
    }
}
