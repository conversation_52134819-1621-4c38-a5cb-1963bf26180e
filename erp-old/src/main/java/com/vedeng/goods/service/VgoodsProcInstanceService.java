package com.vedeng.goods.service;

import com.sun.org.apache.xpath.internal.operations.Bool;
import com.vedeng.activiti.model.HistoryVerfiyRecord;
import com.vedeng.authorization.model.User;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.springframework.ui.Model;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description com.vedeng.goods.service
 * @Date 2021/8/31 11:00
 */
public interface VgoodsProcInstanceService {
    /**
     * 完成审核节点
     *
     */
    void completeTask(Task currentTask, User assignee, Map<String, Object> variables, String comment, Boolean pass);


    /**
     * 开始商品禁用审核
     * @param relatedId
     * @param disableReason
     * @param user
     * @param goodsType
     */
    void disableGoodsApplyVerify(Integer relatedId, String disableReason, Integer user, Integer goodsType);


    /**
     * 获取商品禁用流程Task
     */

    Task getDisableGoodsTask(Integer related, Integer goodsType);

    /**
     * 获取商品禁用流程Task
     */

    Task getDisableGoodsTaskByTaskId(String taskId);

    /**
     * 获取商品禁用审核记录
     * @param user
     * @param related
     * @param goodsType
     */
    List<HistoryVerfiyRecord> getDisableGoodsRecord(Integer related, Integer goodsType,User user);

    /**
     * 检验用户是否正常
     * @param userId
     * @return
     */
    User checkCurrentAssignee(Integer userId);



    /**
     * 判断是是否是任务候选人
     */
    Boolean isTaskCandidate(String taskId,User user);


    /**
     * 根据流程Id获取变量
     * @param taskId
     * @return
     */
    Map<String, Object> getProvessVars(String taskId);
}
