package com.vedeng.order.service.impl;

import com.vedeng.common.model.ResultAssist;
import com.vedeng.common.putHCutil.service.HcSaleorderService;
import com.vedeng.common.util.DateUtil;
import com.vedeng.finance.model.SaleorderData;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.SaleorderVo;
import com.vedeng.order.service.AfterSalesDataService;
import com.vedeng.order.service.SaleorderDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("saleorderDataService")
public class SaleorderDataServiceImpl implements SaleorderDataService {
    Logger logger = LoggerFactory.getLogger(SaleorderDataServiceImpl.class);

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Autowired
    @Qualifier("hcSaleorderService")
    private HcSaleorderService hcSaleorderService;

    @Autowired
    private AfterSalesDataService afterSalesDataService;


    @Override
    public BigDecimal getPaymentAmount(Integer saleorderId) {
        BigDecimal paymentAmount = saleorderMapper.getSaleorderPaymentAmount(saleorderId);
        return paymentAmount;
    }

    @Override
    public BigDecimal getLackAccountPeriodAmount(Integer saleorderId) {
        BigDecimal lackAccountPeriodAmount = saleorderMapper.getSaleorderLackAccountPeriodAmount(saleorderId);
        return lackAccountPeriodAmount;
    }

    @Override
    public SaleorderData getSaleorderData(Integer saleorderId) {
        SaleorderData saleorderData = new SaleorderData();
        BigDecimal paymentAmount = this.getPaymentAmount(saleorderId);
        BigDecimal lackAccountPeriodAmount = this.getLackAccountPeriodAmount(saleorderId);
        BigDecimal periodAmount = this.getPeriodAmount(saleorderId);
        BigDecimal realAmount = this.getRealAmount(saleorderId);
        BigDecimal buyAmount = this.getBuyAmount(saleorderId);
        Boolean isEnd = this.getIsEnd(saleorderId);
        saleorderData.setPaymentAmount(paymentAmount);
        saleorderData.setLackAccountPeriodAmount(lackAccountPeriodAmount);
        saleorderData.setPeriodAmount(periodAmount);
        saleorderData.setRealAmount(realAmount);
        saleorderData.setBuyAmount(buyAmount);
        saleorderData.setIsEnd(isEnd);
        return saleorderData;
    }

    @Override
    public Integer getArrivalNum(Integer saleorderGoodsId) {
        return saleorderGoodsMapper.getArrivalNum(saleorderGoodsId);
    }

    @Override
    public List<SaleorderData> getSaleorderDatas(List<Integer> saleorderIds) {
        if (saleorderIds == null || saleorderIds.size() <= 0) {
            return null;
        }
        return saleorderMapper.getSaleorderDatas(saleorderIds);
    }

    @Override
    public BigDecimal getPeriodAmount(Integer saleorderId) {
        return saleorderMapper.getPeriodAmount(saleorderId);
    }

    @Override
    public BigDecimal getRealAmount(Integer saleorderId) {
        return saleorderMapper.getRealAmount(saleorderId);
    }

    @Override
    public List<ResultAssist> getNewRealAmount(List<Integer> saleOrderIdList, Integer companyId) {
        return saleorderMapper.getNewRealAmount(saleOrderIdList, companyId);
    }

    @Override
    public BigDecimal getBuyAmount(Integer saleorderId) {
        return saleorderMapper.getBuyAmount(saleorderId);
    }

    @Override
    public Boolean getIsEnd(Integer saleorderId) {
        Boolean isEnd = true;
        // 货、款、票全部完结。
        // 货物（包含直发）必须全部到货、款项全部付完（帐期支付也可以）、发票全部开票并寄送（不包含无需寄送的发票）

        List<SaleorderVo> saleorderList = saleorderMapper.getSaleorderIsEnd(saleorderId);
        if (null != saleorderList && saleorderList.size() > 0) {
            Integer status = 0;
            //VDERP-2477  订单完结条件的修改
            for (SaleorderVo vo : saleorderList) {
                if (!vo.getArrivalStatus().equals(2)// 货未到全
                        || !vo.getInvoiceStatus().equals(2)) {// 票未开全
                    isEnd = false;
                    return isEnd;
                }

                if (vo.getInvoiceMethod() != 3 && vo.getIsSendInvoice().equals(1) && vo.getExpressId().equals(0)) {// 需寄送发票但未寄送
                    isEnd = false;
                    return isEnd;
                }
                //VDERP-2477  订单完结条件的修改
                boolean amountFalg = checkSaleAmountStatus(vo);
                if (!amountFalg) {
                    isEnd = false;
                    return isEnd;
                }
                status = vo.getStatus();
            }

            if (status.equals(0) || status.equals(1)) {// 订单已完结 但状态未变
                // 更新订单完结状态
                Integer i = saleorderMapper.updateSaleorderStatus(saleorderId, 2, DateUtil.sysTimeMillis());
                // 如果更新成功调用接口推送状态给网站
                if (i > 0) {
                    updateSaleOrderDataUpdateTime(saleorderId, null, "5");
		 /*   new Thread() {
			@Override
			public void run() {*/
                    // 推送订单完结状态到耗材商城
                    Map<String, Object> map = new HashMap<String, Object>();
                    Saleorder saleorder = saleorderMapper.selectByPrimaryKey(saleorderId);
                    if (saleorder.getOrderType() == 5) {// 如果是耗材商城的订单
                        map.put("orderNo", saleorder.getSaleorderNo());// 订单号
                        map.put("orderStatus", 2);// 已完结
                        try {
                            hcSaleorderService.putOrderStatustoHC(map);
                        } catch (Exception e) {
                            logger.error("推送耗材订单已完结状态失败", e);
                        }

                    }
                }
		 /*   }.start();
		}*/
            }
        } else {
            isEnd = false;
        }
        return isEnd;
    }

    private boolean checkSaleAmountStatus(SaleorderVo saleorderVo) {
        try {
            //原规则：订单的收款状态是“全部收款”
            // 改为：客户实付金额=订单剩余商品金额
            //客户实付
//			BigDecimal  realPayAmount = this.getRealPayAmount(saleorderId);
            //订单实际金额
//			BigDecimal realAmount = this.getRealAmount(saleorderId);
            BigDecimal realPayAmount = saleorderVo.getRealPayAmount();
            BigDecimal realTotalAmount = saleorderVo.getRealTotalAmount();
            if (realPayAmount != null && realTotalAmount != null && realTotalAmount.compareTo(realPayAmount) == 0) {
                return true;
            }
        } catch (Exception e) {
            logger.error("checkSaleAmountStatus error", e);
        }
        return false;
    }

    /**
     * 获取客户实付金额
     *
     * @return
     * <AUTHOR>
     * @Date 3:30 下午 2020/5/23
     * @Param
     **/
    @Override
    public BigDecimal getRealPayAmount(Integer saleorderId) {
        //获取订单已收款金额(不含账期) + (订单账期金额(已收款) : 已收款账期金额减去退还账期金额 ) - (剩余账期未还金额 : 使用的账期额度减去归还的账期额度减去退还的账期额度) - 查询销售订单退还余额的金额
        BigDecimal result = BigDecimal.ZERO;
        try {
            BigDecimal paymentAmount = this.getPaymentAmount(saleorderId);
            BigDecimal periodAmount = this.getPeriodAmount(saleorderId);
            BigDecimal lackAccountPeriodAmount = this.getLackAccountPeriodAmount(saleorderId);
            BigDecimal refundBalanceAmount = afterSalesDataService.getRefundBalanceAmountBySaleorderId(saleorderId);
            result = paymentAmount.add(periodAmount).subtract(lackAccountPeriodAmount).subtract(refundBalanceAmount);
        } catch (Exception e) {
            logger.error("getRealPayAmount error", e);
        }
        return result;
    }

    @Override
    public BigDecimal getHaveInvoiceNums(Integer saleorderGoodsId) {
        return saleorderMapper.getHaveInvoiceNums(saleorderGoodsId);
    }

    @Override
    public BigDecimal getPubPaymentAmount(Integer saleorderId) {
        return saleorderMapper.getPublicPaymentAmount(saleorderId);
    }

    @Override
    public BigDecimal getRealPreAmount(Integer saleorderId) {
        return saleorderMapper.getRealPreAmount(saleorderId);
    }

    @Override
    public BigDecimal getCostPrice(Integer saleorderGoodsId, Integer deliveryDirect, Integer companyId) {
        // 通过关联表；根据销售单商品ID查询采购单商品单价
        BigDecimal buyGoodsPrice = saleorderMapper.getBuyOrderGoodsPrice(saleorderGoodsId);
        if (buyGoodsPrice == null) {
            // 通过出入库记录查询销售单商品对应的备货单商品单价
            buyGoodsPrice = saleorderMapper.getBhOrderGoodsPrice(saleorderGoodsId);
        }
        return buyGoodsPrice;
        /*
         * if (deliveryDirect == 1) {//直发产品 直接关联采购订单详情 return
         * saleorderMapper.getZfCostPrice(saleorderGoodsId); } else if
         * (deliveryDirect == 0) {//普发产品 通过出入库算出 return
         * saleorderMapper.getPfCostPrice(saleorderGoodsId); } return null;
         */
    }

    @Override
    public BigDecimal getAmountByTraderSubject(Integer saleorderId, Integer traderSubject) {
        return saleorderMapper.getAmountByTraderSubject(saleorderId, traderSubject);
    }

    @Override
    public List<Buyorder> getBuyorderListBySaleorderGoodsId(Integer saleorderGoodsId) {
        // 通过关联表；根据销售单商品ID查询采购单单号集合
        List<Buyorder> list = saleorderMapper.getBuyOrderNoBySaleorderGoodsId(saleorderGoodsId);
        if (list == null || list.size() == 0) {
            // 通过出入库记录查询销售单商品对应的备货单单号集合
            list = saleorderMapper.getBhOrderNoBySaleorderGoodsId(saleorderGoodsId);
        }
        return list;
    }

    @Override
    public BigDecimal getPaymentAndPeriodAmount(Integer saleorderId) {
        BigDecimal paymentAmount = saleorderMapper.getPaymentAndPeriodAmount(saleorderId);
        return paymentAmount;
    }

    @Override
    public BigDecimal getReceivedAmount(Integer saleorderId) {
        return saleorderMapper.getReceivedAmount(saleorderId);
    }

    @Override
    public List<BuyorderGoodsVo> getBuyOrderInfoBySaleGoodsId(List<Integer> saleOrderGoodsIdList) {
        // List<BuyorderGoodsVo> buyPriceList = new ArrayList<>();
        if (saleOrderGoodsIdList.size() > 0) {
            List<BuyorderGoodsVo> buyOrderList = new ArrayList<>();
            return buyOrderList;
        }
        return null;

    }

    @Override
    public List<BuyorderGoodsVo> getBuyOrderInfoBySaleGoodsIdForAfterSale(List<Integer> saleOrderGoodsIdList) {
        if (saleOrderGoodsIdList.size() > 0) {
            // buyPrice采购单价
            List<BuyorderGoodsVo> buyPriceOrderAmount = saleorderMapper.getBuyPriceOrderAmount(saleOrderGoodsIdList);
            List<BuyorderGoodsVo> buyOrderList = new ArrayList<>();
            if (buyPriceOrderAmount != null && buyPriceOrderAmount.size() > 0) {
                buyOrderList.addAll(buyPriceOrderAmount);
            }
            buyPriceOrderAmount.clear();
            //buyPriceOrderAmountByBarcode.clear();
            for (int i = 0; i < buyOrderList.size(); i++) {
                Double price = Double.valueOf(buyOrderList.get(i).getBuyOrderPriceStr() == null ? "0" : buyOrderList.get(i).getBuyOrderPriceStr());
                Double num = Double.valueOf(buyOrderList.get(i).getBuyOrderNumStr() == null ? "0" : buyOrderList.get(i).getBuyOrderNumStr());
                buyOrderList.get(i).setPrice(new BigDecimal(String.format("%.8f", price)));
                buyOrderList.get(i).setTotalAmount(new BigDecimal(String.format("%.8f", price * num)));
                // }
            }
            return buyOrderList;
        }
        return null;
    }

    @Override
    public BigDecimal getPublicAmount(Integer saleorderId, Integer traderSubject) {
        return saleorderMapper.getPublicAmount(saleorderId, traderSubject);
    }

    @Override
    public BigDecimal getOpenInvoiceOrderAmount(Integer saleorderId, String traderName) {
        return saleorderMapper.getOpenInvoiceOrderAmount(saleorderId, traderName);
    }

    @Override
    public BigDecimal getRealPreAmountForHcOrder(Integer saleorderId) {
        return saleorderMapper.getRealPreAmountForHcOrder(saleorderId);
    }

    /**
     * 5.订单完结<p>订单完结逻辑在db开票及快递签收中因此可以合并.方法为saleorderDataService.getIsEnd
     * 8.订单开票<p>-自动纸质票开票方法:ST_INVOICE,为航信软件直调db
     * 更新销售单updateDataTime
     *
     * @param operateType 和上述编号一致
     * @Author:strange
     * @Date:10:15 2020-04-06
     */
    @Override
    public void updateSaleOrderDataUpdateTime(Integer orderId, Integer orderDetailId, String operateType) {
        try {
            //如果是订单维度更新则订单和订单商品表一起更新
            //如果是订单商品更新则更新订单和相应商品
            if (orderId == null && orderDetailId == null) {
                return;
            }
            if (orderId != null && orderDetailId == null) {
                //更新订单和商品
                saleorderMapper.updateDataTimeByOrderId(orderId);
                saleorderGoodsMapper.updateDataTimeByOrderId(orderId);
            }
            if (orderDetailId != null) {
                //更新订单和当前商品
                saleorderGoodsMapper.updateDataTimeByDetailId(orderDetailId);
                saleorderMapper.updateDataTimeByDetailId(orderDetailId);
            }
        } catch (Exception e) {
            logger.error("updateSaleOrderDataUpdateTime info:{},error:{}", orderId + "," + orderDetailId + "," + operateType, e);
        }

    }
}
