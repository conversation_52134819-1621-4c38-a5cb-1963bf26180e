$(function () {
	var contentVa = $('#contentVa').val();
	if (contentVa == null || contentVa == '') {
		return;
	}
	var splits = contentVa.split('&&');
	for (var index = 0; index < splits.length; index++) {
		var productCountFlag = $('#productCountFlag').val();
		$('#content_' + productCountFlag).val(splits[index]);
		var count = parseFloat(productCountFlag) + 1;
		$('#productCountFlag').val(count);
		$('#productDiv_' + productCountFlag).after(
			" <li id=\"productDiv_" + count + "\">\n" +
			"                        <div style='margin-left: 20px' class=\"infor_name\" id=\"productTitleDiv_" + count + "\" idFlag=\"" + count + "\">\n" +
			"                            <label></label>\n" +
			"                        </div>\n" +
			"                        <div class=\"el-input el-input--mini\" style=\"width: 340px;\">\n" +
			"                            <input class=\"el-input__inner\" id=\"content_" + count + "\" style=\"display: inline-block;width: 300px;\"/>\n &nbsp;  <a onclick='deleteCategoryName(" + count + ")' style=\"display: inline-block;\">删除</a>" +
			"                        </div>\n" +
			"                    </li>"
		);
		$('#content').val(getContentStr());
	}
})

function changeData(str){
	var inquiry = $('input:radio[name='+str+']:checked').val();
	if (inquiry !== 0) {
		$.ajax({
			url: '/order/bussinesschance/getRadioData.do',
			data: inquiry,
			type: 'post',
			dataType: 'JSON',
			contentType: 'application/json;charset=utf-8',
			async:true,
			success: function (res) {
				if (str==='type') {
					if (res.code == 0) {
						$("#ul_inquire").empty();
						$("#ul_inquire").append();
						res.data.forEach(function (a) {
							var instr='<li style="float: left;margin: 0 10px 4px 0;">\n' +
								'<input type="radio" name="inquiry"  onclick="changeData(\'inquiry\')" value="'+a.sysOptionDefinitionId+'"><label>'+a.title+'</label>\n' +
								'</li>'

							$("#ul_inquire").append(instr)
						});

						$("#ul_source").empty();
						res.data[0].sysOptionDefinitions.forEach(function (a) {
							var instr='<li style="float: left;margin: 0 10px 4px 0;">\n' +
								'<input type="radio" name="source" onclick="changeData(\'source\')"  value="'+a.sysOptionDefinitionId+'"><label>'+a.title+'</label>\n' +
								'</li>'
							$("#ul_source").append(instr)
						});
						// debugger
						$("#ul_inquiry").empty();
						res.data[0].sysOptionDefinitions[0].sysOptionDefinitions.forEach(function (a) {
							var instr='<li style="float: left;margin: 0 10px 4px 0;">\n' +
								'<input type="radio" name="communication"  value="'+a.sysOptionDefinitionId+'"><label>'+a.title+'</label>\n' +
								'</li>'
							$("#ul_inquiry").append(instr)
						});
					} else {
						layer.alert(res.message);
					}
				}

				if (str === 'source') {
					if (res.code == 0) {
						$("#ul_inquiry").empty();
						$("#ul_inquiry").append();
						res.data.forEach(function (a) {
							var instr='<li style="float: left;margin: 0 10px 4px 0;">\n' +
								'<input type="radio" name="communication"   value="'+a.sysOptionDefinitionId+'"><label>'+a.title+'</label>\n' +
								'</li>'

							$("#ul_inquiry").append(instr)
						})
					} else {
						layer.alert(res.message);
					}
				}

			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Fly。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}
}


/**
 * @description 添加商品产品分类信息
 * @param categoryName 产品分类信息
 * <AUTHOR>
 * @date 2020/6/15 13:55:20
 */
function addCategoryName(categoryName) {
	var productCountFlag = $('#productCountFlag').val();
	$('#content_' + productCountFlag).val(categoryName);
	var count = parseFloat(productCountFlag) + 1;
	$('#productCountFlag').val(count);
	$('#productDiv_' + productCountFlag).after(
		" <li id=\"productDiv_" + count + "\">\n" +
		"                        <div style='margin-left: 20px' class=\"infor_name\" id=\"productTitleDiv_" + count + "\" idFlag=\"" + count + "\">\n" +
		"                            <label></label>\n" +
		"                        </div>\n" +
		"                        <div class=\"el-input el-input--mini\" style=\"width: 340px;\">\n" +
		"                            <input class=\"el-input__inner\" id=\"content_" + count + "\" style=\"display: inline-block;width: 300px;\"/>\n &nbsp;  <a onclick='deleteCategoryName(" + count + ")' style=\"display: inline-block;\">删除</a>" +
		"                        </div>\n" +
		"                    </li>"
	);
	$('#content').val(getContentStr());
	layer.closeAll();
}

/**
 * @description 获取商机产品的拼接结果
 * <AUTHOR>
 * @date 2020/6/16 14:32:12
 */
function getContentStr() {
	var contentResult = '';
	var contents = $("input[id^=content_]");
	contents.each(function () {
		contentResult = addResultStr(contentResult, $(this).val());
    })
    return contentResult;
}

/**
 * @description 循环添加每个输入框的产品
 * @param result
 * @param str
 * @returns {*}
 * <AUTHOR>
 * @date 2020/6/16 14:36:18
 */
function addResultStr(result, str) {
	var str = str.replace(/(^\s*)|(\s*$)/g, '');
	if (!(str == '' || str == undefined || str == null)) {
	    result = result == '' ? str : result + '&&' + str;
	}
	return result;
}

/**
 * @description 删除对应的产品分类信息
 * @param productCountFlag 下标
 * <AUTHOR>
 * @date 2020/6/15 15:33:12
 */
function deleteCategoryName(productCountFlag) {
	layer.confirm('确认删除此三级分类？',
		{btn: ['确定', '取消'], title: "提示"},
		function () {
			var countFlag = $('#productCountFlag').val();
            //是否为最后一个div
			if (productCountFlag != countFlag){
                var idFlag = $("div[id^=productTitleDiv_]")[0].getAttribute('idFlag');
				$('#productDiv_' + productCountFlag).remove();
                //是否为第一个div
				if (productCountFlag == idFlag){
                    $("div[id^=productTitleDiv_]")[0].innerHTML = "<span>*&nbsp;</span><label>三级分类:</label>"
                }
				layer.closeAll();
			} else {
				layer.alert('未选择三级分类无需删除');
			}
			$('#content').val(getContentStr());
		})
}

