package com.vedeng.trader.group.buy;

import com.vedeng.common.util.DateUtil;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.trader.group.GoodsBehavior;
import com.vedeng.trader.group.model.GoodsQueryParam;
import com.vedeng.trader.model.Period;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import java.util.BitSet;
import java.util.List;

public abstract class BuyBaseBehavior extends GoodsBehavior {

    // 运行时注入Mapper
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private SaleorderMapper saleorderMapper=(SaleorderMapper) context.getBean(SaleorderMapper.class);

    public BitSet getBuyInPeriod(){
        BitSet bitSet=new BitSet();
        Period period= DateUtil.getStartAndEndPointForDays(getPeriod());
        List<Integer> traderIds=saleorderMapper.getTraderIdBuyInPeriod(period);
        if (CollectionUtils.isNotEmpty(traderIds)) {
            for(Integer id:traderIds){
                if(id==null){
                    continue;
                }
                bitSet.set(id);
            }
        }
        return bitSet;
    }

    public BitSet getBuyGoods(){
        BitSet buySet=new BitSet();
        GoodsQueryParam param=new GoodsQueryParam();
        Integer type=getChooseType();
        Period period= DateUtil.getStartAndEndPointForDays(getPeriod());
        param.setPeriod(period);
        if(type==0){
            param.setCategoryIds(getCategoryIds());
        }else if(type==1){
            param.setBrandId(getBrandId());
        }else if(type==2){
            param.setSkuList(getSkuList());
        }
        List<Integer> traderIds=saleorderMapper.getTraderIdsBuyGoods(param);
        if(CollectionUtils.isNotEmpty(traderIds)){
            for(Integer id:traderIds){
                if(id!=null){
                    buySet.set(id);
                }
            }
        }
        return buySet;
    }
}
