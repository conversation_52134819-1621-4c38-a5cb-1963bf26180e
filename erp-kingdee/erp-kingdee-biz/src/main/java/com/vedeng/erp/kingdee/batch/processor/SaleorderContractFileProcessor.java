package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchSaleorderContractDto;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeFileDataMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSaleorderContractMapper;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class SaleorderContractFileProcessor extends BaseProcessor<BatchSaleorderContractDto, KingDeeFileDataDto> {
    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;
    @Autowired
    private KingDeeSaleorderContractMapper kingDeeSaleorderContractMapper;
    @Value("${oss_http}")
    private String ossHttp;

    @Override
    public KingDeeFileDataDto doProcess(BatchSaleorderContractDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("开始推送销售合同附件,dto:{}", JSONUtil.toJsonStr(dto));
        if(ObjectUtil.isNotNull(dto.getDataId())){
            log.info("销售合同推送金蝶，数据已存在:{}",JSON.toJSONString(dto));
            return null;
        }
        //通过附件回写表判断该附件是否推送过
        KingDeeFileDataDto query = KingDeeFileDataDto.builder()
                .formId(KingDeeFormConstant.SALEORDER_CONTRACT)
                .erpId(dto.getSaleorderId().toString())
                .url(ossHttp+dto.getUrl())
                .build();
        List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(query);
        if (!CollectionUtils.isEmpty(existFile)) {
            log.info("当前附件已经推送过金蝶，{}", JSON.toJSONString(query));
            return null;
        }
        //本地获取销售合同FID
        Integer FID = kingDeeSaleorderContractMapper.getFidByDDH(dto.getSaleorderNo());
        return KingDeeFileDataDto.builder()
                .fileName("合同_"+dto.getName())
                .aliasFileName("合同_"+dto.getName())
                .billNo(dto.getSaleorderNo())
                .formId(KingDeeFormConstant.SALEORDER_CONTRACT)
                .isLast(true)
                .fId(FID.toString())
                .url(ossHttp+dto.getUrl())
                .erpId(dto.getAttachmentId().toString())
                .businessId(KingDeeFormConstant.SALEORDER_CONTRACT+dto.getAttachmentId().toString())
                .build();
    }
}
