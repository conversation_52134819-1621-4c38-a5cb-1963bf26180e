package com.test.kingdee;

import com.vedeng.infrastructure.kingdee.common.sdk.entity.JsonBase;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/9/9 13:41
 */
@Data
public class QueryExtParam extends JsonBase {

    String FormId;
    public String FieldKeys;
    List<Object> FilterString;
    String OrderString;
    int StartRow;
    int Limit;
    int TopRowCount;
}
