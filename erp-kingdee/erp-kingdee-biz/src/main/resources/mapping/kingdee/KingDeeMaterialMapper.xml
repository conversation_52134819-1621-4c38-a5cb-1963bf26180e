<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeMaterialMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeMaterialEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_MATERIAL-->
    <id column="KING_DEE_MATERIAL_ENTITY_ID" jdbcType="INTEGER" property="kingDeeMaterialEntityId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="FMATERIALID" jdbcType="VARCHAR" property="fmaterialid" />
    <result column="F_CREATE_ORG_ID" jdbcType="VARCHAR" property="fCreateOrgId" />
    <result column="F_USE_ORG_ID" jdbcType="VARCHAR" property="fUseOrgId" />
    <result column="F_NUMBER" jdbcType="VARCHAR" property="fNumber" />
    <result column="F_NAME" jdbcType="VARCHAR" property="fName" />
    <result column="F_SPECIFICATION" jdbcType="VARCHAR" property="fSpecification" />
    <result column="F_OLD_NUMBER" jdbcType="VARCHAR" property="fOldNumber" />
    <result column="F_QZOK_DHH" jdbcType="VARCHAR" property="fQzokDhh" />
    <result column="F_QZOK_GYSWLBM" jdbcType="VARCHAR" property="fQzokGyswlbm" />
    <result column="F_QZOK_PPPTEXT" jdbcType="VARCHAR" property="fQzokPpptext" />
    <result column="F_QZOK_ZCZHTEXT" jdbcType="VARCHAR" property="fQzokZczhtext" />
    <result column="F_QZOK_YLQXTEXT" jdbcType="VARCHAR" property="fQzokYlqxtext" />
    <result column="F_QZOK_YLQXCXTEXT" jdbcType="VARCHAR" property="fQzokYlqxcxtext" />
    <result column="F_QZOK_YLQXYTTEXT" jdbcType="VARCHAR" property="fQzokYlqxyttext" />
    <result column="F_QZOK_YLQXXGJFL" jdbcType="VARCHAR" property="fQzokYlqxxgjfl" />
    <result column="F_QZOK_YLQXXFLTEXT" jdbcType="VARCHAR" property="fQzokYlqxxfltext" />
    <result column="F_QZOK_YLQXFLTEXT" jdbcType="VARCHAR" property="fQzokYlqxfltext" />
    <result column="F_QZOK_SPYYYJFLTEXT" jdbcType="VARCHAR" property="fQzokSpyyyjfltext" />
    <result column="F_QZOK_SPYYEJFLTEXT" jdbcType="VARCHAR" property="fQzokSpyyejfltext" />
    <result column="F_QZOK_SPYYSJFLTEXT" jdbcType="VARCHAR" property="fQzokSpyysjfltext" />
    <result column="F_QZOK_SPYYSIJFLTEXT" jdbcType="VARCHAR" property="fQzokSpyysijfltext" />
    <result column="F_QZOK_SPYYWJFLTEXT" jdbcType="VARCHAR" property="fQzokSpyywjfltext" />
    <result column="F_QZOK_FYLQXYJFLTEXT" jdbcType="VARCHAR" property="fQzokFylqxyjfltext" />
    <result column="F_QZOK_FYLQXEJFLTEXT" jdbcType="VARCHAR" property="fQzokFylqxejfltext" />
    <result column="F_QZOK_ZYCPX" jdbcType="VARCHAR" property="fQzokZycpx" />
    <result column="F_QZOK_CXFL" jdbcType="VARCHAR" property="fQzokCxfl" />
    <result column="SUBHEADENTITY" jdbcType="VARCHAR" property="subheadentity" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    KING_DEE_MATERIAL_ENTITY_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, FMATERIALID, F_CREATE_ORG_ID, F_USE_ORG_ID, F_NUMBER, F_NAME, F_SPECIFICATION,
    F_OLD_NUMBER, F_QZOK_DHH, F_QZOK_GYSWLBM, F_QZOK_PPPTEXT, F_QZOK_ZCZHTEXT, F_QZOK_YLQXTEXT,
    F_QZOK_YLQXCXTEXT, F_QZOK_YLQXYTTEXT, F_QZOK_YLQXXGJFL, F_QZOK_YLQXXFLTEXT, F_QZOK_YLQXFLTEXT,
    F_QZOK_SPYYYJFLTEXT, F_QZOK_SPYYEJFLTEXT, F_QZOK_SPYYSJFLTEXT, F_QZOK_SPYYSIJFLTEXT,
    F_QZOK_SPYYWJFLTEXT, F_QZOK_FYLQXYJFLTEXT, F_QZOK_FYLQXEJFLTEXT, F_QZOK_ZYCPX, F_QZOK_CXFL,
    SUBHEADENTITY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from KING_DEE_MATERIAL
    where KING_DEE_MATERIAL_ENTITY_ID = #{kingDeeMaterialEntityId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_MATERIAL
    where KING_DEE_MATERIAL_ENTITY_ID = #{kingDeeMaterialEntityId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="KING_DEE_MATERIAL_ENTITY_ID" keyProperty="kingDeeMaterialEntityId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeMaterialEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_MATERIAL (ADD_TIME, MOD_TIME, CREATOR,
    UPDATER, CREATOR_NAME, UPDATER_NAME,
    FMATERIALID, F_CREATE_ORG_ID, F_USE_ORG_ID,
    F_NUMBER, F_NAME, F_SPECIFICATION,
    F_OLD_NUMBER, F_QZOK_DHH, F_QZOK_GYSWLBM,
    F_QZOK_PPPTEXT, F_QZOK_ZCZHTEXT, F_QZOK_YLQXTEXT,
    F_QZOK_YLQXCXTEXT, F_QZOK_YLQXYTTEXT, F_QZOK_YLQXXGJFL,
    F_QZOK_YLQXXFLTEXT, F_QZOK_YLQXFLTEXT, F_QZOK_SPYYYJFLTEXT,
    F_QZOK_SPYYEJFLTEXT, F_QZOK_SPYYSJFLTEXT, F_QZOK_SPYYSIJFLTEXT,
    F_QZOK_SPYYWJFLTEXT, F_QZOK_FYLQXYJFLTEXT,
    F_QZOK_FYLQXEJFLTEXT, F_QZOK_ZYCPX, F_QZOK_CXFL,
    SUBHEADENTITY)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
    #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR},
    #{fmaterialid,jdbcType=VARCHAR}, #{fCreateOrgId,jdbcType=VARCHAR}, #{fUseOrgId,jdbcType=VARCHAR},
    #{fNumber,jdbcType=VARCHAR}, #{fName,jdbcType=VARCHAR}, #{fSpecification,jdbcType=VARCHAR},
    #{fOldNumber,jdbcType=VARCHAR}, #{fQzokDhh,jdbcType=VARCHAR}, #{fQzokGyswlbm,jdbcType=VARCHAR},
    #{fQzokPpptext,jdbcType=VARCHAR}, #{fQzokZczhtext,jdbcType=VARCHAR}, #{fQzokYlqxtext,jdbcType=VARCHAR},
    #{fQzokYlqxcxtext,jdbcType=VARCHAR}, #{fQzokYlqxyttext,jdbcType=VARCHAR}, #{fQzokYlqxxgjfl,jdbcType=VARCHAR},
    #{fQzokYlqxxfltext,jdbcType=VARCHAR}, #{fQzokYlqxfltext,jdbcType=VARCHAR}, #{fQzokSpyyyjfltext,jdbcType=VARCHAR},
    #{fQzokSpyyejfltext,jdbcType=VARCHAR}, #{fQzokSpyysjfltext,jdbcType=VARCHAR}, #{fQzokSpyysijfltext,jdbcType=VARCHAR},
    #{fQzokSpyywjfltext,jdbcType=VARCHAR}, #{fQzokFylqxyjfltext,jdbcType=VARCHAR},
    #{fQzokFylqxejfltext,jdbcType=VARCHAR}, #{fQzokZycpx,jdbcType=VARCHAR}, #{fQzokCxfl,jdbcType=VARCHAR},
    #{subheadentity,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="KING_DEE_MATERIAL_ENTITY_ID" keyProperty="kingDeeMaterialEntityId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeMaterialEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_MATERIAL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="fmaterialid != null and fmaterialid != ''">
        FMATERIALID,
      </if>
      <if test="fCreateOrgId != null and fCreateOrgId != ''">
        F_CREATE_ORG_ID,
      </if>
      <if test="fUseOrgId != null and fUseOrgId != ''">
        F_USE_ORG_ID,
      </if>
      <if test="fNumber != null and fNumber != ''">
        F_NUMBER,
      </if>
      <if test="fName != null and fName != ''">
        F_NAME,
      </if>
      <if test="fSpecification != null and fSpecification != ''">
        F_SPECIFICATION,
      </if>
      <if test="fOldNumber != null and fOldNumber != ''">
        F_OLD_NUMBER,
      </if>
      <if test="fQzokDhh != null and fQzokDhh != ''">
        F_QZOK_DHH,
      </if>
      <if test="fQzokGyswlbm != null and fQzokGyswlbm != ''">
        F_QZOK_GYSWLBM,
      </if>
      <if test="fQzokPpptext != null and fQzokPpptext != ''">
        F_QZOK_PPPTEXT,
      </if>
      <if test="fQzokZczhtext != null and fQzokZczhtext != ''">
        F_QZOK_ZCZHTEXT,
      </if>
      <if test="fQzokYlqxtext != null and fQzokYlqxtext != ''">
        F_QZOK_YLQXTEXT,
      </if>
      <if test="fQzokYlqxcxtext != null and fQzokYlqxcxtext != ''">
        F_QZOK_YLQXCXTEXT,
      </if>
      <if test="fQzokYlqxyttext != null and fQzokYlqxyttext != ''">
        F_QZOK_YLQXYTTEXT,
      </if>
      <if test="fQzokYlqxxgjfl != null and fQzokYlqxxgjfl != ''">
        F_QZOK_YLQXXGJFL,
      </if>
      <if test="fQzokYlqxxfltext != null and fQzokYlqxxfltext != ''">
        F_QZOK_YLQXXFLTEXT,
      </if>
      <if test="fQzokYlqxfltext != null and fQzokYlqxfltext != ''">
        F_QZOK_YLQXFLTEXT,
      </if>
      <if test="fQzokSpyyyjfltext != null and fQzokSpyyyjfltext != ''">
        F_QZOK_SPYYYJFLTEXT,
      </if>
      <if test="fQzokSpyyejfltext != null and fQzokSpyyejfltext != ''">
        F_QZOK_SPYYEJFLTEXT,
      </if>
      <if test="fQzokSpyysjfltext != null and fQzokSpyysjfltext != ''">
        F_QZOK_SPYYSJFLTEXT,
      </if>
      <if test="fQzokSpyysijfltext != null and fQzokSpyysijfltext != ''">
        F_QZOK_SPYYSIJFLTEXT,
      </if>
      <if test="fQzokSpyywjfltext != null and fQzokSpyywjfltext != ''">
        F_QZOK_SPYYWJFLTEXT,
      </if>
      <if test="fQzokFylqxyjfltext != null and fQzokFylqxyjfltext != ''">
        F_QZOK_FYLQXYJFLTEXT,
      </if>
      <if test="fQzokFylqxejfltext != null and fQzokFylqxejfltext != ''">
        F_QZOK_FYLQXEJFLTEXT,
      </if>
      <if test="fQzokZycpx != null and fQzokZycpx != ''">
        F_QZOK_ZYCPX,
      </if>
      <if test="fQzokCxfl != null and fQzokCxfl != ''">
        F_QZOK_CXFL,
      </if>
      <if test="subheadentity != null and subheadentity != ''">
        SUBHEADENTITY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fmaterialid != null and fmaterialid != ''">
        #{fmaterialid,jdbcType=VARCHAR},
      </if>
      <if test="fCreateOrgId != null and fCreateOrgId != ''">
        #{fCreateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fUseOrgId != null and fUseOrgId != ''">
        #{fUseOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fNumber != null and fNumber != ''">
        #{fNumber,jdbcType=VARCHAR},
      </if>
      <if test="fName != null and fName != ''">
        #{fName,jdbcType=VARCHAR},
      </if>
      <if test="fSpecification != null and fSpecification != ''">
        #{fSpecification,jdbcType=VARCHAR},
      </if>
      <if test="fOldNumber != null and fOldNumber != ''">
        #{fOldNumber,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDhh != null and fQzokDhh != ''">
        #{fQzokDhh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokGyswlbm != null and fQzokGyswlbm != ''">
        #{fQzokGyswlbm,jdbcType=VARCHAR},
      </if>
      <if test="fQzokPpptext != null and fQzokPpptext != ''">
        #{fQzokPpptext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokZczhtext != null and fQzokZczhtext != ''">
        #{fQzokZczhtext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYlqxtext != null and fQzokYlqxtext != ''">
        #{fQzokYlqxtext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYlqxcxtext != null and fQzokYlqxcxtext != ''">
        #{fQzokYlqxcxtext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYlqxyttext != null and fQzokYlqxyttext != ''">
        #{fQzokYlqxyttext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYlqxxgjfl != null and fQzokYlqxxgjfl != ''">
        #{fQzokYlqxxgjfl,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYlqxxfltext != null and fQzokYlqxxfltext != ''">
        #{fQzokYlqxxfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYlqxfltext != null and fQzokYlqxfltext != ''">
        #{fQzokYlqxfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSpyyyjfltext != null and fQzokSpyyyjfltext != ''">
        #{fQzokSpyyyjfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSpyyejfltext != null and fQzokSpyyejfltext != ''">
        #{fQzokSpyyejfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSpyysjfltext != null and fQzokSpyysjfltext != ''">
        #{fQzokSpyysjfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSpyysijfltext != null and fQzokSpyysijfltext != ''">
        #{fQzokSpyysijfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSpyywjfltext != null and fQzokSpyywjfltext != ''">
        #{fQzokSpyywjfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFylqxyjfltext != null and fQzokFylqxyjfltext != ''">
        #{fQzokFylqxyjfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFylqxejfltext != null and fQzokFylqxejfltext != ''">
        #{fQzokFylqxejfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokZycpx != null and fQzokZycpx != ''">
        #{fQzokZycpx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCxfl != null and fQzokCxfl != ''">
        #{fQzokCxfl,jdbcType=VARCHAR},
      </if>
      <if test="subheadentity != null and subheadentity != ''">
        #{subheadentity,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeMaterialEntity">
    <!--@mbg.generated-->
    update KING_DEE_MATERIAL
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fmaterialid != null and fmaterialid != ''">
        FMATERIALID = #{fmaterialid,jdbcType=VARCHAR},
      </if>
      <if test="fCreateOrgId != null and fCreateOrgId != ''">
        F_CREATE_ORG_ID = #{fCreateOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fUseOrgId != null and fUseOrgId != ''">
        F_USE_ORG_ID = #{fUseOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fNumber != null and fNumber != ''">
        F_NUMBER = #{fNumber,jdbcType=VARCHAR},
      </if>
      <if test="fName != null and fName != ''">
        F_NAME = #{fName,jdbcType=VARCHAR},
      </if>
      <if test="fSpecification != null and fSpecification != ''">
        F_SPECIFICATION = #{fSpecification,jdbcType=VARCHAR},
      </if>
      <if test="fOldNumber != null and fOldNumber != ''">
        F_OLD_NUMBER = #{fOldNumber,jdbcType=VARCHAR},
      </if>
      <if test="fQzokDhh != null and fQzokDhh != ''">
        F_QZOK_DHH = #{fQzokDhh,jdbcType=VARCHAR},
      </if>
      <if test="fQzokGyswlbm != null and fQzokGyswlbm != ''">
        F_QZOK_GYSWLBM = #{fQzokGyswlbm,jdbcType=VARCHAR},
      </if>
      <if test="fQzokPpptext != null and fQzokPpptext != ''">
        F_QZOK_PPPTEXT = #{fQzokPpptext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokZczhtext != null and fQzokZczhtext != ''">
        F_QZOK_ZCZHTEXT = #{fQzokZczhtext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYlqxtext != null and fQzokYlqxtext != ''">
        F_QZOK_YLQXTEXT = #{fQzokYlqxtext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYlqxcxtext != null and fQzokYlqxcxtext != ''">
        F_QZOK_YLQXCXTEXT = #{fQzokYlqxcxtext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYlqxyttext != null and fQzokYlqxyttext != ''">
        F_QZOK_YLQXYTTEXT = #{fQzokYlqxyttext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYlqxxgjfl != null and fQzokYlqxxgjfl != ''">
        F_QZOK_YLQXXGJFL = #{fQzokYlqxxgjfl,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYlqxxfltext != null and fQzokYlqxxfltext != ''">
        F_QZOK_YLQXXFLTEXT = #{fQzokYlqxxfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokYlqxfltext != null and fQzokYlqxfltext != ''">
        F_QZOK_YLQXFLTEXT = #{fQzokYlqxfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSpyyyjfltext != null and fQzokSpyyyjfltext != ''">
        F_QZOK_SPYYYJFLTEXT = #{fQzokSpyyyjfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSpyyejfltext != null and fQzokSpyyejfltext != ''">
        F_QZOK_SPYYEJFLTEXT = #{fQzokSpyyejfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSpyysjfltext != null and fQzokSpyysjfltext != ''">
        F_QZOK_SPYYSJFLTEXT = #{fQzokSpyysjfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSpyysijfltext != null and fQzokSpyysijfltext != ''">
        F_QZOK_SPYYSIJFLTEXT = #{fQzokSpyysijfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokSpyywjfltext != null and fQzokSpyywjfltext != ''">
        F_QZOK_SPYYWJFLTEXT = #{fQzokSpyywjfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFylqxyjfltext != null and fQzokFylqxyjfltext != ''">
        F_QZOK_FYLQXYJFLTEXT = #{fQzokFylqxyjfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokFylqxejfltext != null and fQzokFylqxejfltext != ''">
        F_QZOK_FYLQXEJFLTEXT = #{fQzokFylqxejfltext,jdbcType=VARCHAR},
      </if>
      <if test="fQzokZycpx != null and fQzokZycpx != ''">
        F_QZOK_ZYCPX = #{fQzokZycpx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokCxfl != null and fQzokCxfl != ''">
        F_QZOK_CXFL = #{fQzokCxfl,jdbcType=VARCHAR},
      </if>
      <if test="subheadentity != null and subheadentity != ''">
        SUBHEADENTITY = #{subheadentity,jdbcType=VARCHAR},
      </if>
    </set>
    where KING_DEE_MATERIAL_ENTITY_ID = #{kingDeeMaterialEntityId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeMaterialEntity">
    <!--@mbg.generated-->
    update KING_DEE_MATERIAL
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
    MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    CREATOR = #{creator,jdbcType=INTEGER},
    UPDATER = #{updater,jdbcType=INTEGER},
    CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
    UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    FMATERIALID = #{fmaterialid,jdbcType=VARCHAR},
    F_CREATE_ORG_ID = #{fCreateOrgId,jdbcType=VARCHAR},
    F_USE_ORG_ID = #{fUseOrgId,jdbcType=VARCHAR},
    F_NUMBER = #{fNumber,jdbcType=VARCHAR},
    F_NAME = #{fName,jdbcType=VARCHAR},
    F_SPECIFICATION = #{fSpecification,jdbcType=VARCHAR},
    F_OLD_NUMBER = #{fOldNumber,jdbcType=VARCHAR},
    F_QZOK_DHH = #{fQzokDhh,jdbcType=VARCHAR},
    F_QZOK_GYSWLBM = #{fQzokGyswlbm,jdbcType=VARCHAR},
    F_QZOK_PPPTEXT = #{fQzokPpptext,jdbcType=VARCHAR},
    F_QZOK_ZCZHTEXT = #{fQzokZczhtext,jdbcType=VARCHAR},
    F_QZOK_YLQXTEXT = #{fQzokYlqxtext,jdbcType=VARCHAR},
    F_QZOK_YLQXCXTEXT = #{fQzokYlqxcxtext,jdbcType=VARCHAR},
    F_QZOK_YLQXYTTEXT = #{fQzokYlqxyttext,jdbcType=VARCHAR},
    F_QZOK_YLQXXGJFL = #{fQzokYlqxxgjfl,jdbcType=VARCHAR},
    F_QZOK_YLQXXFLTEXT = #{fQzokYlqxxfltext,jdbcType=VARCHAR},
    F_QZOK_YLQXFLTEXT = #{fQzokYlqxfltext,jdbcType=VARCHAR},
    F_QZOK_SPYYYJFLTEXT = #{fQzokSpyyyjfltext,jdbcType=VARCHAR},
    F_QZOK_SPYYEJFLTEXT = #{fQzokSpyyejfltext,jdbcType=VARCHAR},
    F_QZOK_SPYYSJFLTEXT = #{fQzokSpyysjfltext,jdbcType=VARCHAR},
    F_QZOK_SPYYSIJFLTEXT = #{fQzokSpyysijfltext,jdbcType=VARCHAR},
    F_QZOK_SPYYWJFLTEXT = #{fQzokSpyywjfltext,jdbcType=VARCHAR},
    F_QZOK_FYLQXYJFLTEXT = #{fQzokFylqxyjfltext,jdbcType=VARCHAR},
    F_QZOK_FYLQXEJFLTEXT = #{fQzokFylqxejfltext,jdbcType=VARCHAR},
    F_QZOK_ZYCPX = #{fQzokZycpx,jdbcType=VARCHAR},
    F_QZOK_CXFL = #{fQzokCxfl,jdbcType=VARCHAR},
    SUBHEADENTITY = #{subheadentity,jdbcType=VARCHAR}
    where KING_DEE_MATERIAL_ENTITY_ID = #{kingDeeMaterialEntityId,jdbcType=INTEGER}
  </update>
  
  <select id="getByfNumber" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from KING_DEE_MATERIAL
    where F_NUMBER = #{fNumber,jdbcType=VARCHAR}
  </select>
</mapper>