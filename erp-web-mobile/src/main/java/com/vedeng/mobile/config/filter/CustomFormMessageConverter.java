package com.vedeng.mobile.config.filter;


import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.http.converter.*;
import org.springframework.stereotype.Component;
import org.springframework.util.*;

import javax.mail.internet.MimeUtility;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.*;

/**
 * @Author: daniel
 * @Date: 2020/11/11 08 45
 * @Description:
 */
@Component
public class CustomFormMessageConverter implements HttpMessageConverter<MultiValueMap<String, ?>> {

    public static final Charset DEFAULT_CHARSET = Charset.forName("UTF-8");
    private static final byte[] BOUNDARY_CHARS = new byte[]{45, 95, 49, 50, 51, 52, 53, 54, 55, 56, 57, 48, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90};
    private Charset charset;
    private Charset multipartCharset;
    private List<MediaType> supportedMediaTypes;
    private List<HttpMessageConverter<?>> partConverters;
    private final Random random;

    public CustomFormMessageConverter() {
        this.charset = DEFAULT_CHARSET;
        this.supportedMediaTypes = new ArrayList();
        this.partConverters = new ArrayList();
        this.random = new Random();
        this.supportedMediaTypes.add(MediaType.APPLICATION_FORM_URLENCODED);
        this.supportedMediaTypes.add(MediaType.MULTIPART_FORM_DATA);
        this.partConverters.add(new ByteArrayHttpMessageConverter());
        StringHttpMessageConverter stringHttpMessageConverter = new StringHttpMessageConverter();
        stringHttpMessageConverter.setWriteAcceptCharset(false);
        this.partConverters.add(stringHttpMessageConverter);
        this.partConverters.add(new ResourceHttpMessageConverter());
    }

    public void setCharset(Charset charset) {
        this.charset = charset;
    }

    public void setMultipartCharset(Charset multipartCharset) {
        this.multipartCharset = multipartCharset;
    }

    public void setSupportedMediaTypes(List<MediaType> supportedMediaTypes) {
        this.supportedMediaTypes = supportedMediaTypes;
    }

    public List<MediaType> getSupportedMediaTypes() {
        return Collections.unmodifiableList(this.supportedMediaTypes);
    }

    public void setPartConverters(List<HttpMessageConverter<?>> partConverters) {
        Assert.notEmpty(partConverters, "'partConverters' must not be empty");
        this.partConverters = partConverters;
    }

    public void addPartConverter(HttpMessageConverter<?> partConverter) {
        Assert.notNull(partConverter, "'partConverter' must not be null");
        this.partConverters.add(partConverter);
    }

    public boolean canRead(Class<?> clazz, MediaType mediaType) {
        if (!MultiValueMap.class.isAssignableFrom(clazz)) {
            return false;
        } else if (mediaType == null) {
            return true;
        } else {
            Iterator var3 = this.getSupportedMediaTypes().iterator();

            MediaType supportedMediaType;
            do {
                if (!var3.hasNext()) {
                    return false;
                }

                supportedMediaType = (MediaType)var3.next();
            } while(supportedMediaType.equals(MediaType.MULTIPART_FORM_DATA) || !supportedMediaType.includes(mediaType));

            return true;
        }
    }

    public boolean canWrite(Class<?> clazz, MediaType mediaType) {
        if (!MultiValueMap.class.isAssignableFrom(clazz)) {
            return false;
        } else if (mediaType != null && !MediaType.ALL.equals(mediaType)) {
            Iterator var3 = this.getSupportedMediaTypes().iterator();

            MediaType supportedMediaType;
            do {
                if (!var3.hasNext()) {
                    return false;
                }

                supportedMediaType = (MediaType)var3.next();
            } while(!supportedMediaType.isCompatibleWith(mediaType));

            return true;
        } else {
            return true;
        }
    }

    public MultiValueMap<String, String> read(Class<? extends MultiValueMap<String, ?>> clazz, HttpInputMessage inputMessage) throws IOException, HttpMessageNotReadableException {
        MediaType contentType = inputMessage.getHeaders().getContentType();
        Charset charset = contentType.getCharset() != null ? contentType.getCharset() : this.charset;
        String body = StreamUtils.copyToString(inputMessage.getBody(), charset);
        String[] pairs = StringUtils.tokenizeToStringArray(body, "&");
        MultiValueMap<String, String> result = new LinkedMultiValueMap(pairs.length);
        String[] var8 = pairs;
        int var9 = pairs.length;

        for(int var10 = 0; var10 < var9; ++var10) {
            String pair = var8[var10];
            int idx = pair.indexOf(61);
            if (idx == -1) {
                result.add(URLDecoder.decode(pair, charset.name()), null);
            } else {
                String name = URLDecoder.decode(pair.substring(0, idx), charset.name());
                String value = URLDecoder.decode(pair.substring(idx + 1), charset.name());
                result.add(name, value);
            }
        }

        return result;
    }

    public void write(MultiValueMap<String, ?> map, MediaType contentType, HttpOutputMessage outputMessage) throws IOException, HttpMessageNotWritableException {
        if (!this.isMultipart(map, contentType)) {
            this.writeForm(map, contentType, outputMessage);
        } else {
            this.writeMultipart(map, outputMessage);
        }

    }

    private boolean isMultipart(MultiValueMap<String, ?> map, MediaType contentType) {
        if (contentType != null) {
            return MediaType.MULTIPART_FORM_DATA.includes(contentType);
        } else {
            Iterator var3 = map.keySet().iterator();

            while(var3.hasNext()) {
                String name = (String)var3.next();
                Iterator var5 = ((List)map.get(name)).iterator();

                while(var5.hasNext()) {
                    Object value = var5.next();
                    if (value != null && !(value instanceof String)) {
                        return true;
                    }
                }
            }

            return false;
        }
    }

    private void writeForm(MultiValueMap<String, ?> form, MediaType contentType, HttpOutputMessage outputMessage) throws IOException {
        Charset charset;
        if (contentType != null) {
            outputMessage.getHeaders().setContentType(contentType);
            charset = contentType.getCharset() != null ? contentType.getCharset() : this.charset;
        } else {
            outputMessage.getHeaders().setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            charset = this.charset;
        }

        StringBuilder builder = new StringBuilder();
        Iterator nameIterator = form.keySet().iterator();

        while(nameIterator.hasNext()) {
            String name = (String)nameIterator.next();
            Iterator valueIterator = ((List)form.get(name)).iterator();

            while(valueIterator.hasNext()) {
                String value = (String)valueIterator.next();
                builder.append(URLEncoder.encode(name, charset.name()));
                if (value != null) {
                    builder.append('=');
                    builder.append(URLEncoder.encode(value, charset.name()));
                    if (valueIterator.hasNext()) {
                        builder.append('&');
                    }
                }
            }

            if (nameIterator.hasNext()) {
                builder.append('&');
            }
        }

        byte[] bytes = builder.toString().getBytes(charset.name());
        outputMessage.getHeaders().setContentLength((long)bytes.length);
        StreamUtils.copy(bytes, outputMessage.getBody());
    }

    private void writeMultipart(MultiValueMap<String, ?> parts, HttpOutputMessage outputMessage) throws IOException {
        byte[] boundary = this.generateMultipartBoundary();
        Map<String, String> parameters = Collections.singletonMap("boundary", new String(boundary, "US-ASCII"));
        MediaType contentType = new MediaType(MediaType.MULTIPART_FORM_DATA, parameters);
        outputMessage.getHeaders().setContentType(contentType);
        this.writeParts(outputMessage.getBody(), parts, boundary);
        writeEnd(outputMessage.getBody(), boundary);
    }

    private void writeParts(OutputStream os, MultiValueMap<String, ?> parts, byte[] boundary) throws IOException {
        Iterator var4 = parts.entrySet().iterator();

        while(var4.hasNext()) {
            Map.Entry<String, List<Object>> entry = (Map.Entry)var4.next();
            String name = (String)entry.getKey();
            Iterator var7 = ((List)entry.getValue()).iterator();

            while(var7.hasNext()) {
                Object part = var7.next();
                if (part != null) {
                    this.writeBoundary(os, boundary);
                    this.writePart(name, this.getHttpEntity(part), os);
                    writeNewLine(os);
                }
            }
        }

    }

    private void writePart(String name, HttpEntity<?> partEntity, OutputStream os) throws IOException {
        Object partBody = partEntity.getBody();
        Class<?> partType = partBody.getClass();
        HttpHeaders partHeaders = partEntity.getHeaders();
        MediaType partContentType = partHeaders.getContentType();
        Iterator var8 = this.partConverters.iterator();

        HttpMessageConverter messageConverter;
        do {
            if (!var8.hasNext()) {
                throw new HttpMessageNotWritableException("Could not write request: no suitable HttpMessageConverter found for request type [" + partType.getName() + "]");
            }

            messageConverter = (HttpMessageConverter)var8.next();
        } while(!messageConverter.canWrite(partType, partContentType));

        HttpOutputMessage multipartMessage = new MultipartHttpOutputMessage(os);
        multipartMessage.getHeaders().setContentDispositionFormData(name, this.getFilename(partBody));
        if (!partHeaders.isEmpty()) {
            multipartMessage.getHeaders().putAll(partHeaders);
        }

        messageConverter.write(partBody, partContentType, multipartMessage);
    }

    protected byte[] generateMultipartBoundary() {
        byte[] boundary = new byte[this.random.nextInt(11) + 30];

        for(int i = 0; i < boundary.length; ++i) {
            boundary[i] = BOUNDARY_CHARS[this.random.nextInt(BOUNDARY_CHARS.length)];
        }

        return boundary;
    }

    protected HttpEntity<?> getHttpEntity(Object part) {
        return part instanceof HttpEntity ? (HttpEntity)part : new HttpEntity(part);
    }

    protected String getFilename(Object part) {
        if (part instanceof Resource) {
            Resource resource = (Resource)part;
            String filename = resource.getFilename();
            if (this.multipartCharset != null) {
                filename = MimeDelegate.encode(filename, this.multipartCharset.name());
            }

            return filename;
        } else {
            return null;
        }
    }

    private void writeBoundary(OutputStream os, byte[] boundary) throws IOException {
        os.write(45);
        os.write(45);
        os.write(boundary);
        writeNewLine(os);
    }

    private static void writeEnd(OutputStream os, byte[] boundary) throws IOException {
        os.write(45);
        os.write(45);
        os.write(boundary);
        os.write(45);
        os.write(45);
        writeNewLine(os);
    }

    private static void writeNewLine(OutputStream os) throws IOException {
        os.write(13);
        os.write(10);
    }

    private static class MimeDelegate {
        private MimeDelegate() {
        }

        public static String encode(String value, String charset) {
            try {
                return MimeUtility.encodeText(value, charset, (String)null);
            } catch (UnsupportedEncodingException var3) {
                throw new IllegalStateException(var3);
            }
        }
    }

    private static class MultipartHttpOutputMessage implements HttpOutputMessage {
        private final OutputStream outputStream;
        private final HttpHeaders headers = new HttpHeaders();
        private boolean headersWritten = false;

        public MultipartHttpOutputMessage(OutputStream outputStream) {
            this.outputStream = outputStream;
        }

        public HttpHeaders getHeaders() {
            return this.headersWritten ? HttpHeaders.readOnlyHttpHeaders(this.headers) : this.headers;
        }

        public OutputStream getBody() throws IOException {
            this.writeHeaders();
            return this.outputStream;
        }

        private void writeHeaders() throws IOException {
            if (!this.headersWritten) {
                Iterator var1 = this.headers.entrySet().iterator();

                while(var1.hasNext()) {
                    Map.Entry<String, List<String>> entry = (Map.Entry)var1.next();
                    byte[] headerName = this.getAsciiBytes((String)entry.getKey());
                    Iterator var4 = ((List)entry.getValue()).iterator();

                    while(var4.hasNext()) {
                        String headerValueString = (String)var4.next();
                        byte[] headerValue = this.getAsciiBytes(headerValueString);
                        this.outputStream.write(headerName);
                        this.outputStream.write(58);
                        this.outputStream.write(32);
                        this.outputStream.write(headerValue);
                        CustomFormMessageConverter.writeNewLine(this.outputStream);
                    }
                }

                CustomFormMessageConverter.writeNewLine(this.outputStream);
                this.headersWritten = true;
            }

        }

        private byte[] getAsciiBytes(String name) {
            try {
                return name.getBytes("UTF-8");
            } catch (UnsupportedEncodingException var3) {
                throw new IllegalStateException(var3);
            }
        }
    }
}
