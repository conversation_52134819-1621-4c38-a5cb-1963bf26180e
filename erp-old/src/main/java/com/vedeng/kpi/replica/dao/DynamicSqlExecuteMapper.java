package com.vedeng.kpi.replica.dao;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date created in 2020/6/19 15:20
 */
public interface DynamicSqlExecuteMapper {

    @Select("${executeSql}")
    void executeSql(@Param("executeSql") String executeSql);

    @Select("${selectSql}")
    int selectCountById(@Param("selectSql") String selectSql);

    @Select("${selectColumnsOfTable}")
    List<String> getColumnsOfTable(@Param("selectColumnsOfTable") String selectColumnsOfTable);
}
