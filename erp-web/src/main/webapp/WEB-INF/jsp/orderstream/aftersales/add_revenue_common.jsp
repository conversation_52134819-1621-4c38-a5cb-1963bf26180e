<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<div class="parts">
    <%--<div class="title-container">
        <div class="table-title nobor">
            收入记录
        </div>
        <c:choose>
            <c:when test="${afterSalesVo.atferSalesStatus eq 2 || afterSalesVo.atferSalesStatus eq 3}">
                <div class="title-click  pop-new-data" layerParams='{"width":"600px","height":"380px","title":"新增售后收入记录","link":"/order/afterSalesCommon/addRevenueRecord.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>新增售后收入记录</div>
            </c:when>
        </c:choose>
    </div>--%>
    <table class="table">
        <thead>
        <tr>
            <th>费用类型</th>
            <th>支付金额</th>
            <th>支付方</th>
            <td>备注</td>
        </tr>
        </thead>
        <tbody>
        <c:if test="${not empty afterSalesRevenueRecords}">
            <c:forEach items="${afterSalesRevenueRecords}" var="revenueList">
                <tr>
                    <td>
                        <c:forEach items="${typeList}" var="typeLR">
                            <c:if test="${revenueList.type eq typeLR.sysOptionDefinitionId}">
                                ${typeLR.title}
                            </c:if>
                        </c:forEach>
                    </td>
                    <td>${revenueList.amount}</td>
                    <td>
                            ${revenueList.payer}
                    </td>
                    <td>
                            ${revenueList.remark}
                    </td>
                </tr>
            </c:forEach>
        </c:if>

        <c:if test="${empty afterSalesRevenueRecords}">
            <!-- 查询无结果弹出 -->
            <tr>
                <td colspan='4'>暂无记录！</td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>