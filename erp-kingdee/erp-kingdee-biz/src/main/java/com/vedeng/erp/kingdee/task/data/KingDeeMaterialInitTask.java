package com.vedeng.erp.kingdee.task.data;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.service.KingDeeMaterialApiService;
import com.vedeng.goods.dto.KingDeeSkuInfoDto;
import com.vedeng.goods.service.GoodsApiService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 历史sku推送金蝶
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/8 13:48
 */
@Component
@JobHandler(value = "KingDeeMaterialInitTask")
public class KingDeeMaterialInitTask extends AbstractJobHandler {

    @Autowired
    private GoodsApiService goodsApiService;
    @Autowired
    private KingDeeMaterialApiService kingDeeMaterialApiService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        Integer totalPushCount = 0;
        if (StringUtils.isBlank(param)) {
            Integer passSkuNum = goodsApiService.getCheckPassSkuNum();
            for (int i = 0; i < passSkuNum; i += 1000) {
                List<KingDeeSkuInfoDto> skuList = goodsApiService.getBatchInitSkuToKingDee(i);
                totalPushCount = handelPushKingDee(skuList);
            }
        } else {
            List<Integer> skuIdList = Arrays.stream(param.split(",")).map(Integer::parseInt).collect(Collectors.toList());
            List<KingDeeSkuInfoDto> skuList = new ArrayList<>();
            skuIdList.forEach(id -> {
                KingDeeSkuInfoDto kingDeeSkuInfoDto = goodsApiService.getSkuInfoBySkuId(id);
                skuList.add(kingDeeSkuInfoDto);
            });
            totalPushCount = handelPushKingDee(skuList);
        }
        XxlJobLogger.log("本次推送sku数量：{}", totalPushCount);
        return ReturnT.SUCCESS;
    }

    private Integer handelPushKingDee(List<KingDeeSkuInfoDto> skuList) {
        int totalPushCount = 0;
        for (KingDeeSkuInfoDto skuInfoDto : skuList) {
            KingDeeMaterialDto skuInfoToKingDee = goodsApiService.getPushSkuInfoToKingDee(skuInfoDto);
            // spuType为其他类型的，不推送
            if (!"".equals(skuInfoToKingDee.getSubHeadEntity().getFCategoryID())) {
                try {

                    kingDeeMaterialApiService.register(skuInfoToKingDee);
                    XxlJobLogger.log("sku初始化推送金蝶成功， sku：{}", skuInfoToKingDee.getFNumber());
                    totalPushCount++;
                } catch (Exception e) {
                    XxlJobLogger.log("sku初始化推送金蝶失败， sku：{}", skuInfoToKingDee.getFNumber());
                }
            }
        }
        return totalPushCount;
    }
}
