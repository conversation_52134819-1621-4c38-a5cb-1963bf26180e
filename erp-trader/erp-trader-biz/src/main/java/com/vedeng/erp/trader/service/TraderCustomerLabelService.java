package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.domain.vo.TraderCustomerTuokeLabelVo;
import com.vedeng.goods.dto.BaseCategoryDto;
import com.vedeng.goods.dto.DepartmentsHospitalDto;
import lombok.NonNull;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/10 17:13
 **/
public interface TraderCustomerLabelService {

    /**
     * 查询所有未删除的标签
     * @return
     */
    List<DepartmentsHospitalDto> findAllLabel();

    /**
     * 查询几级分类
     * @param isDelete 是否删除 0 否 1 是
     * @param level 等级 1递增
     * @return
     */
    List<BaseCategoryDto> findLevelCategory(@NonNull Integer isDelete, @NonNull Integer level);

    /**
     * 获取当前客户的标签信息
     * 数据库中设定好的几个属性,分割的转为list，jsp页面显示
     * 从其他表中查数据的分list后设置属性为checked
     * @param traderCustomerTuokeLabelVo
     */
    void getTraderCustomerLabel(TraderCustomerTuokeLabelVo traderCustomerTuokeLabelVo);

    /**
     * 保存或更新客户群组标签
     * @param traderCustomerTuokeLabelVo
     */
    void updateOrSaveTraderCustomerTuokeLabel(TraderCustomerTuokeLabelVo traderCustomerTuokeLabelVo);

    /**
     * 根据客户Id查询客户分群标签
     * @param traderId
     * @return
     */
    TraderCustomerTuokeLabelVo getTuokeLabelInfo(Integer traderId, Integer businessCluesId);

}
