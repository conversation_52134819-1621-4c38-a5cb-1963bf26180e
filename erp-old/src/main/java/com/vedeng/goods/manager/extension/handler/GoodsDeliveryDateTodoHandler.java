package com.vedeng.goods.manager.extension.handler;

import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.todolist.service.ITodoInstance;
import com.vedeng.todolist.service.impl.MaintainDataDeliveryTime;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Component
public class GoodsDeliveryDateTodoHandler extends BaseGoodsTodoHandler {
    @Resource
    private MaintainDataDeliveryTime maintainDataDeliveryTime;
    @Resource
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Override
    public int getServiceId() {
        return GOODS_DELIVERY_DATE_SERVICE_ID;
    }

    @Override
    public ITodoInstance getITodoInstance() {
        return maintainDataDeliveryTime;
    }

    @Override
    public boolean isDone() {
        CoreSkuGenerate coreSkuGenerate = coreSkuGenerateMapper.selectByPrimaryKey(getGoodId());
        return coreSkuGenerate != null && StringUtils.isNotEmpty(coreSkuGenerate.getDeclareDeliveryRange());
    }

    @Override
    public void onHandler() {
        if (!isDone()) {
            addGoodsTodoRecord(getGoodId(), getSkuNo());
        }
    }
}
