<!DOCTYPE html>
<html>
<head>
    <%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
    <%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
    <c:set var="title" value="列表配置" scope="application" />
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css"  media="all">
    <style>
        .main_content {
            margin: 0 10px;
            padding-top: 10px;
            font-size: 10px;
        }
    </style>
</head>
<body>
<div class="main_content">
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
        <legend>筛选条件配置</legend>
    </fieldset>
    <input name="searchList" value="${searchList}" hidden="hidden">
    <div id="searchItem" class="demo-transfer"></div>

    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
        <legend>列表展示配置</legend>
    </fieldset>
    <input name="columnList" value="${columnList}" hidden="hidden">
    <div id="listItem" class="demo-transfer"></div>

    <input name="columnListHidden" value="${columnListHidden}" hidden="hidden">
    <input name="searchListHidden" value="${searchListHidden}" hidden="hidden">
</div>

<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script>
    layui.use(['transfer'], function(){
        var $ = layui.$
            ,transfer = layui.transfer;
        var searchArray = [];
        var columnListArray = [];

        var searchListHiddenArray = $("input[name='searchListHidden']").val().split(',');
        var columnListHiddenArray = $("input[name='columnListHidden']").val().split(',');

        var searchItemSelected = [];
        var columnListSelected = [];

        $("input[name='searchList']").val().split(',').forEach(function (item, index) {
            var arrayItem = {
                value: index,
                title: item
            }
            searchArray.push(arrayItem);

            if ($.inArray(item,searchListHiddenArray) === -1){
                searchItemSelected.push(index);
            }
        })

        $("input[name='columnList']").val().split(',').forEach(function (item, index) {
            var arrayItem = {
                value: index,
                title: item
            }
            columnListArray.push(arrayItem)

            if ($.inArray(item,columnListHiddenArray) === -1){
                columnListSelected.push(index);
            }
        })

        transfer.render({
            elem: '#searchItem'
            ,id: 'searchItem'
            ,data: searchArray
            ,height: 300
            ,value: searchItemSelected
            ,title: ['剩余筛选项', '已选筛选项']
            ,text: {
                none: '无数据'
                ,searchNone: '无匹配数据'
            }
            ,showSearch: true
        })

        transfer.render({
            elem: '#listItem'
            ,id: 'listItem'
            ,data: columnListArray
            ,height: 300
            ,value: columnListSelected
            ,title: ['剩余列表项', '已选列表项']
            ,text: {
                none: '无数据'
                ,searchNone: '无匹配数据'
            }
            ,showSearch: true
        })

        window.getSearchTransferUnselectedValue = function () {
            var searchItem = [];
            transfer.getData('searchItem').forEach(function (item, index) {
                searchItem.push(item.title);
            })
            return $("input[name='searchList']").val().split(',').filter(item => $.inArray(item,searchItem) < 0);
        }

        window.getcolumnListTransferUnselectedValue = function () {
            var columnList = [];
            transfer.getData('listItem').forEach(function (item, index) {
                columnList.push(item.title);
            })
            return $("input[name='columnList']").val().split(',').filter(item => $.inArray(item,columnList) < 0);
        }

    });

    var getListConfigureValue = function () {
        var value = {};
        value.search = getSearchTransferUnselectedValue();
        value.columnList = getcolumnListTransferUnselectedValue();
        return value;
    }

</script>

</body>
</html>