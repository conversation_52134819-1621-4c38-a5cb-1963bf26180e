package com.vedeng.erp.system.service.impl;

import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.system.common.constant.SystemConstant;
import com.vedeng.erp.system.domain.dto.CostCategoryDto;
import com.vedeng.erp.system.domain.entity.CostCategoryEntity;
import com.vedeng.erp.system.domain.entity.SysCostCategoryKingDee;
import com.vedeng.erp.system.mapper.CostCategoryMapper;
import com.vedeng.erp.system.mapper.SysCostCategoryKingDeeMapper;
import com.vedeng.erp.system.mapstruct.CostCategoryConvertor;
import com.vedeng.erp.system.service.CostCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *<AUTHOR>
 *@Description com.vedeng.erp.system.service
 *@Date 2022/8/15 13:29
 */
@Service
@Slf4j
public class CostCategoryServiceImpl implements CostCategoryService {

    @Autowired
    private CostCategoryMapper costCategoryEntityMapper;

    @Autowired
    private CostCategoryConvertor costCategoryConvertor;

    @Autowired
    private SysCostCategoryKingDeeMapper sysCostCategoryKingDeeMapper;

    @Override
    public int deleteByPrimaryKey(Integer costCategoryId) {
        return costCategoryEntityMapper.deleteByPrimaryKey(costCategoryId);
    }

    @Override
    public int insert(CostCategoryDto record) {
        CostCategoryEntity costCategoryEntity = costCategoryConvertor.toEntity(record);
        return costCategoryEntityMapper.insert(costCategoryEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertSelective(CostCategoryDto record) {
        CostCategoryEntity costCategoryEntity = costCategoryConvertor.toEntity(record);
        costCategoryEntity.setPurchase(record.getPurchase());
        costCategoryEntityMapper.insertSelective(costCategoryEntity);
        if (costCategoryEntity.getCostCategoryId() > SystemConstant.COST_CATEGORY_MAX_ID){
            throw new ServiceException("超出系统的千位限制！");
        }
    }

    @Override
    public CostCategoryDto selectByPrimaryKey(Integer costCategoryId) {
        return costCategoryEntityMapper.selectByPrimaryKey(costCategoryId);
    }

    @Override
    public int updateByPrimaryKeySelective(CostCategoryDto record) {
        CostCategoryEntity costCategoryEntity = costCategoryConvertor.toEntity(record);
        costCategoryEntity.setPurchase(record.getPurchase());
        return costCategoryEntityMapper.updateByPrimaryKeySelective(costCategoryEntity);
    }

    @Override
    public int updateByPrimaryKey(CostCategoryDto record) {
        CostCategoryEntity costCategoryEntity = costCategoryConvertor.toEntity(record);
        return costCategoryEntityMapper.updateByPrimaryKey(costCategoryEntity);
    }

    @Override
    public void checkRepeat(CostCategoryDto costCategoryDto) {
        int count = costCategoryEntityMapper.listFindRepeatInfo(costCategoryDto);
        if (count > 0){
            throw new ServiceException("本次添加的费用类别与系统现有重复!");
        }
    }

    @Override
    public List<SysCostCategoryKingDee> selectKingDeeCategoryByNameOrId(String category) {
        return sysCostCategoryKingDeeMapper.selectKingDeeCategoryByNameOrId(category);
    }


    @Override
    public List<SysCostCategoryKingDee> getKingDeeCategory() {
        return sysCostCategoryKingDeeMapper.selectKingDeeCategory();
    }


    @Override
    public int insertBatchKingDeeCategory(List<SysCostCategoryKingDee> sysCostCategoryKingDeeList) {
        return sysCostCategoryKingDeeMapper.insertBatchKingDeeCategory(sysCostCategoryKingDeeList);
    }

    @Override
    public SysCostCategoryKingDee selectByKingDeeNo(String categoryNo) {
        return sysCostCategoryKingDeeMapper.selectByKingDeeNo(categoryNo);
    }


    @Override
    public void checkKingDeeRepeat(List<SysCostCategoryKingDee> list) {
        int count = sysCostCategoryKingDeeMapper.listFindRepeatInfo(list);
        if (count > 0){
            throw new ServiceException("本次添加的金蝶费用编号与系统现有重复!");
        }
    }
}
