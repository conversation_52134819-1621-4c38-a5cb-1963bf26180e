package com.vedeng.erp.finance.domain.dto;

import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.utils.TaxesUtil;
import lombok.Data;

/**
 * 销项票下载入参
 */
@Data
public class SaleInvoiceDownRequestDto  implements ITaxesParam {

    private static final long serialVersionUID = -2239544099196174872L;


    /**
     * 纳税人识别号
     */
    private String nsrsbh;

    /**
     * 发票号码
     */
    private String fphm;

    /**
     * 开票日期(YYYY-MM-DD HH:mm:ss)
     */
    private String kprq;

    /**
     * 文件类型(PDF或者OFD或者XML（注意大小写）)
     */
    private String filetype;

    public SaleInvoiceDownRequestDto(){
        this.nsrsbh = TaxesUtil.taxesConfig.taxNo;
    }
}
