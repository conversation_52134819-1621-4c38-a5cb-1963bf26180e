$(function () {
    var checkSelectAll = function () {
        var selectFlag = true;

        $('.J-select-spu').each(function () {
            if (!$(this)[0].checked) {
                selectFlag = false;
            }
        })

        $('.J-select-list-all')[0].checked = selectFlag;
    };

    var checkOutput = function () {
        var len = $('.J-select-spu:checked').length;

        if (len > 0) {
            $('.J-prod-export').removeClass('btn-disabled');
            $('#batchSKU').removeClass('btn-disabled');
            $('#batchSKU').attr('disabled',false);
        } else {
            $('.J-prod-export').addClass('btn-disabled');
            $('#batchSKU').addClass('btn-disabled');
            $('#batchSKU').attr('disabled',true);
        }
    };

    var checkStock = function () {
        var len = $('.J-select-sku:checked').length;

        if (len > 0) {
            $('.J-prod-stockcalculate').removeClass('btn-disabled');
            $('#batchSaveAuthorization')[0].disabled = false;
            $('#batchSaveAuthorization')[0].title = '';
        } else {
            $('.J-prod-stockcalculate').addClass('btn-disabled');
            $('#batchSaveAuthorization')[0].disabled = true;
            $('#batchSaveAuthorization')[0].title = '请先选择sku';
        }
    };

    //表格全选复选框
    $('.J-select-list-all').change(function () {
        var $this = $(this);

        $('.J-select-spu').each(function () {
            $(this)[0].checked = $this[0].checked;
        })

        checkOutput();
    });

    $('.J-select-spu').change(function () {
        checkSelectAll();
        checkOutput();
    })

    $(document).on('change', '.J-select-sku', function () {
        checkStock();
    })

    //批量设置备货
    // $('.J-prod-stockcalculate').click(function () {
    //     if (!$('.J-select-sku:checked').length) {
    //         return false;
    //     }
    //
    //     var dialog = new artDialog({
    //         content: $('.J-stockcalculate-tmpl').html(),
    //         init: function () {
    //             var $form = $('.J-stockcalculate-form');
    //             $form.validate({
    //                 errorWrap: true,
    //                 rules: {
    //                     setGoods: {
    //                         required: true
    //                     }
    //                 },
    //                 messages: {
    //                     setGoods: {
    //                         required: '请选择设置项'
    //                     }
    //                 }
    //             })
    //         },
    //         width: 420,
    //         button: [{
    //             name: '确认',
    //             highlight: true,
    //             callback: function () {
    //                 if ($('.J-stockcalculate-form').valid()) {
    //                     var skuIds = [];
    //
    //                     $('.J-select-sku:checked').each(function () {
    //                         skuIds.push($(this).val());
    //                     })
    //
    //                     $.ajax({
    //                         url: page_url + '/goods/vgoods/backupSku.do',
    //                         data: {
    //                             skuIds: skuIds.join(','),
    //                             hasBackupMachine: $('.J-stockcalculate-form input:checked').val()
    //                         },
    //                         type: 'post',
    //                         dataType: 'json',
    //                         success: function (res) {
    //                             dialog.close();
    //                             if (res.code == 0) {
    //                                 window.localStorage.setItem('spuListViewOption', 'true');
    //                                 window.location.reload();
    //                             } else {
    //                                 errorTip(res.message || '请求错误。');
    //                             }
    //                         }
    //                     })
    //                 }
    //                 return false;
    //             }
    //         }, {
    //             name: '取消'
    //         }],
    //     })
    // });

    //展示商品分类信息--用于单个SPU迁移
    $(".J-remove-spu").click(function () {
        var currentSpuId = $(this).attr('data-spuid');
        setSpuIds(currentSpuId);
        return true
    });

    //展示商品分类信息--用于批量SPU迁移
    $("#batchSKU").click(function () {
        var selectSpu =$('.J-select-spu:checked');
        var selectSpuCount = selectSpu.length
        if (selectSpuCount===0) {
            $('#batchSKU').attr('disabled',true);
            return false;
        }else {
            $('#batchSKU').attr('disabled',false);
        }
        var currVal=[];

        for(var i=0; i<selectSpuCount; i++){
            currVal[i]=selectSpu[i].value
        }
        if(currVal.length!==0){
            setSpuIds(currVal)
        }

        return true
    });

    var setSpuIds=function (val) {
        $('#selectiveSPUIds').val(val);
    };

    //商品分类选择框---用于spu迁移
    new DialogSearch({
        el: '.J-remove-spu',
        async: true,
        dataUrl: page_url + '/category/base/getCategoryList.do',
        input: '',
        label: '',
        searchUrl: page_url + '/category/base/getCategoryListByKeyWords.do',
        params: 'keyWords',
        placeholder: '请输入商品名/分类名/国际码',
        needTab: ['商品分类', '历史分类'],
        dataparse: function (data) {
            var resData = [];
            $.each(data.listData, function (i, lv1) {
                var lv1item = {
                    label: lv1.baseCategoryName,
                    value: lv1.baseCategoryId,
                    child: []
                };

                if (lv1.secondCategoryList) {
                    $.each(lv1.secondCategoryList, function (ii, lv2) {
                        var lv2item = {
                            label: lv2.baseCategoryName,
                            value: lv2.baseCategoryId,
                            child: []
                        };

                        if (lv2.thirdCategoryList) {
                            $.each(lv2.thirdCategoryList, function (iii, lv3) {
                                lv2item.child.push({
                                    label: lv3.baseCategoryName,
                                    value: lv3.baseCategoryId,
                                    baseCategoryType: lv3.baseCategoryType
                                })
                            })
                        }

                        if (lv2item.child.length) {
                            lv1item.child.push(lv2item);
                        }
                    })
                }

                if (lv1item.child.length) {
                    resData.push(lv1item);
                }
            });

            return resData;
        },
        searchList: [
            {
                label: '分类',
                name: 'label',
                width: '380px'
            },
            {
                label: '分类类型',
                name: 'typeName',
                width: ''
            }
        ],
        parseSearchData: function (res) {
            var resData = [];

            $.each(res.listData, function (i, item) {
                resData.push({
                    value: item.baseCategoryId,
                    label: item.categoryJoinName,
                    baseCategoryType: item.baseCategoryType,
                    typeName: item.baseCategoryType == 1 ? '医疗器械' : '非医疗器械'
                })
            });

            return resData;
        },
        historyName: 'prod_category',
        onselect: function (obj, params) {
            var spuIds = $('#selectiveSPUIds').val();
            $('#selectiveSPUIds').val("");

             layer.open({
                title: 'SPU迁移确认',
                type: 1,
                area:  ['850px', '600px'],
                content: $(".J-spu-preConfirm").html() //这里content是一个普通的String
                ,btn: ['确认', '取消']
                ,yes: function(index, layero){
                    //check
                    var checkFlag = $("#spuConfirmForm input[name='spuIds']:checked").length;
                    if(checkFlag===null||checkFlag===0) {
                        layer.alert('请选择需要移动的spu', {
                            icon: 5,
                            title: "提示"
                        });

                        return false;
                    }

                    //spu确认
                    $.ajax({
                        url: page_url + '/goods/vgoods/confirmSPUsRemoval.do',
                        method: 'post',
                        data: $('#spuConfirmForm').serialize(),
                        dataType: 'json',
                        success: function (res) {
                            if (res.code === 0) {
                                layer.alert('操作成功');
                                //关闭父亲弹框
                                setTimeout(layer.close(index),1000);
                            } else {
                                layer.alert('操作失败', {
                                    icon: 5,
                                    title: "提示"
                                });
                                // setTimeout(layer.close(index),1000);
                            }
                        }
                    });
                }
                ,btn2: function(index, layero){
                    //按钮【按钮二】的回调
                    layer.close(index)
                },
                success: function(index, layero) {
                    var targetCategoryId = obj.value
                    //spu移动属性校验
                    $.ajax({
                        url: page_url + '/goods/vgoods/checkSPUAttributes.do',
                        method: 'post',
                        data: {
                            categoryId: targetCategoryId,
                            spuIds: spuIds
                        },
                        dataType: 'json',
                        success: function (res) {
                            if (res.code === 0) {
                                var spuCount=0;
                                var skuCount=0;
                                var initCategoryFlag=false;

                                var text=""
                                if(res.data.length===0){
                                text='<tr>'+
                                    '<td colspan="9" align="center" valian="middle">'+
                                        '<div><i class="vd-icon icon-caution1"></i></div>当前无可迁移SPU</td>'+
                                    '</tr>'
                                }else {
                                    $.each(res.data,function (i, obj) {
                                        if(initCategoryFlag===false){
                                            $('#targetCategoryId').val(obj.targetCategoryId);
                                            initCategoryFlag=true;
                                        }

                                        spuCount++;
                                        skuCount+=obj.ownSkuCount;

                                        text+="<tr> " +
                                            "<td>"+
                                            "<input onclick='changeSpuAndSkuCountWhenChecked()' type=\"checkbox\" name=\"spuIds\" value=\""+obj.spuId+"\"checked>"+
                                            "</td>"+
                                            "<td onclick='viewSpuDetailWhenClick("+obj.spuId+")'>"+obj.spuShowName+"</td>"+
                                            "<td>"+obj.spuId+"</td>"+
                                            "<td class='ownSkuNum'>"+obj.ownSkuCount+"</td>"+
                                            "<td>"+obj.originalCategoryName+"</td>"+
                                            "<td>"+obj.originalCategoryId+"</td>"+
                                            "<td>"+obj.targetCategoryName+"</td>"+
                                            "<td>"+obj.targetCategoryId+"</td>"+
                                            "<td onclick='showChangedPropertyValue("+ targetCategoryId +","+obj.spuId + ")'>"+obj.changedAttrCount+"</td>"+
                                            "</tr>"
                                    });
                                }


                                $('#spuPrepareTable tbody').append(text);
                                $('#spuCount').text(spuCount);
                                $('#skuCount').text(skuCount);
                            } else {
                                layer.alert( '操作失败',{
                                    icon: 5,
                                    title: "提示"
                                });
                                setTimeout(layer.close(index),1000);
                            }
                        }
                    });
                },
                 error: function () {
                     layer.alert( '系统错误',{
                         icon: 5,
                         title: "提示"
                     });
                 }
            });

            historyName = {
                value: obj.value,
                label: obj.label,
                baseCategoryType: params.baseCategoryType,
                typeName: params.baseCategoryType == 1 ? '医疗器械' : '非医疗器械'
            };
        }
    });

    //获取商品数量
    $.ajax({
        url: page_url + '/goods/vgoods/count.do',
        dataType: 'json',
        success: function (res) {
            if (res.code == 0 && res.data) {
                for (var item in res.data) {
                    $('.J-' + item).html('(' + res.data[item] + ')');
                }
            }
        },
        error: function () { }
    })

    //新增或者编辑sku
    var skuTmpl = template($('.J-sku-tmpl').html());

    var editSku = function (params, id) {
        var title = id ? '编辑SKU' : '新增SKU';
        var isHaocai = params.type == 317 || params.type == 318 || params.type == 653;
        var message = isHaocai ? '规格' : '制造商型号';
        params.isHaocai = isHaocai;

        var dialog = new artDialog({
            title: title,
            content: skuTmpl(params),
            init: function () {
                var $form = $('.J-sku-form');
                $form.validate({
                    errorWrap: true,
                    rules: {
                        content: {
                            required: true
                        }
                    },
                    messages: {
                        content: {
                            required: '请填写' + message
                        }
                    }
                })

                if (id) {
                    $.ajax({
                        url: page_url + '/goods/vgoods/viewTempSkuAjax.do',
                        data: {
                            skuId: id
                        },
                        dataType: 'json',
                        success: function (res) {
                            if (res.code == 0) {
                                $('.J-cnt', $form).val(res.data.skuInfo);
                                $('.J-sku-loading').hide();
                                $form.show();
                            }
                        }
                    })
                } else {
                    $('.J-sku-loading').hide();
                    $('.J-sku-form').show();
                }
            },
            width: 680,
            button: [{
                name: '确认',
                highlight: true,
                callback: function () {
                    if ($('.J-sku-form').valid()) {
                        $.ajax({
                            url: page_url + '/goods/vgoods/saveTempSku.do',
                            data: {
                                spuId: params.spuId,
                                skuId: id,
                                skuInfo: $('.J-sku-form .J-cnt').val()
                            },
                            type: 'post',
                            dataType: 'json',
                            success: function (res) {
                                dialog.close();
                                if (res.code == 0) {
                                    window.localStorage.setItem('spuListViewOption', 'true');
                                    window.location.reload();
                                } else {
                                    errorTip(res.message || '请求错误。');
                                }
                            }
                        })
                    }
                    return false;
                }
            }, {
                name: '取消'
            }],
        })
    };


    $(document).on('click', '.J-sku-edit', function () {
        var params = {};
        var $parent = $(this).parents('.J-list:first');
        var id = $(this).data('skuid');

        params.type = $.trim($parent.find('.J-spu-type').data('type'));
        params.spuId = $(this).data('spuid');
        params.spuName = $(this).data('spuname');

        editSku(params, id);
    })

    //后台报错显示
    var errorTip = function (tip) {
        var dia = artDialog.alert(tip, null, {
            fn: function () {
                dia.close();
            }, text: '我知道了'
        }, { type: "warn" });
    };

    //操作弹窗
    var optionDialog = function (obj) {
        var dialog = new artDialog({
            content: $('.J-dlg-tmpl').html(),
            init: function () {
                $('.J-dlg-tip').html(obj.tip);

                $('.J-dlg-form').validate({
                    errorWrap: true,
                    rules: {
                        content: {
                            required: true,
                            minlength: 10,
                            maxlength: 300
                        }
                    },
                    messages: {
                        content: {
                            required: '请填写删除原因，10-300个字',
                            minlength: '请填写删除原因，10-300个字',
                            maxlength: '请填写删除原因，10-300个字'
                        }
                    }
                })
            },
            width: 420,
            button: [{
                name: '提交',
                highlight: true,
                callback: function () {
                    if ($('.J-dlg-form').valid()) {
                        var data = $.extend({}, obj.data, { deleteReason: $('.J-dlg-form .J-dlg-cnt').val() });

                        $.ajax({
                            url: page_url + obj.url,
                            data: data,
                            type: 'post',
                            dataType: 'json',
                            success: function (res) {
                                dialog.close();
                                if (res.code == 0) {
                                    obj.callback && obj.callback();
                                } else {
                                    errorTip(res.message || '操作异常');
                                }
                            }
                        })
                    }
                    return false;
                }
            }, {
                name: '取消'
            }],
        })
    }

    //删除sku
    $(document).on('click', '.J-del-sku', function () {
        //todo 目前暂时跳转只无权操作页面
        window.location.href = page_url+"/goods/vgoods/deleteSku.do";
        return;

        var _this = this;
        optionDialog({
            url: '/goods/vgoods/deleteSku.do',
            tip: '确认删除该商品信息么？',
            data: {
                skuId: $(this).data('id'),
                spuId: $(this).data('spuid')
            },
            callback: function () {
                var $wrap = $(_this).parents('.J-item-wrap:first');
                var $pageWrap = $wrap.find('.J-page-wrap');

                turnPage($pageWrap, 1);
                GLOBAL.showGlobalTip('操作成功');
                // window.location.reload();
            }
        })
    });

    //删除spu
    $('.J-del-spu').click(function () {
        //todo 目前暂时跳转只无权操作页面
        window.location.href = page_url+"/goods/vgoods/deleteSpu.do";
        return;

        optionDialog({
            url: '/goods/vgoods/deleteSpu.do',
            tip: '温馨提示：删除SPU会删掉对应的所有SKU信息。<br>确认删除该商品信息么？',
            data: {
                spuId: $(this).data('id')
            },
            callback: function () {
                window.localStorage.setItem('spuListViewOption', 'true');
                window.location.reload();
            }
        })
    });


    //sku翻页
    var skuListTmpl = template($('.J-sku-list-tmpl').html());

    var initPager = function ($wrap, page) {

        $wrap.attr('cz-page', page.pageNo);
        $wrap.find('.J-page-txt').html(page.pageNo);
        $wrap.find('.J-page-txt-total').html(page.totalPage);

        if (page.totalRecord <= 5) {
            $wrap.hide();
        } else {
            if (page.pageNo == 1) {
                $('.J-page-prev', $wrap).addClass('disabled');
            } else {
                $('.J-page-prev', $wrap).removeClass('disabled');
            }

            if (page.pageNo == page.totalPage) {
                $('.J-page-next', $wrap).addClass('disabled');
            } else {
                $('.J-page-next', $wrap).removeClass('disabled');
            }
        }
    };

    var turnPage = function ($wrap, page) {
        var spuId = $wrap.data('spuid');
        var total = Math.ceil($wrap.data('total') / 5);
        var lv = $wrap.data('lv');
        var wiki = $wrap.data('spuwiki');
        var spuName = $wrap.data('spuname');

        var spuInfo = {
            spuId: spuId,
            spulv: lv,
            spuWiki: wiki,
            spuName: spuName,
            auth: $wrap.data('auth'),
            tempauth: $wrap.data('tempauth')
        };

        if (!(page < 1 || page > total)) {
            $.ajax({
                url: page_url + '/goods/vgoods/listSku.do',
                data: {
                    pageNo: page,
                    pageSize: 5,
                    spuId: spuId
                },
                dataType: 'json',
                success: function (res) {
                    if (res.code == 0) {
                        $wrap.siblings('.J-sku-item').remove();
                        $wrap.before(skuListTmpl($.extend({}, res.data, spuInfo)));

                        initPager($wrap, res.data.page);
                    }
                }
            })
        }
    };

    $('.J-page-prev').click(function () {
        var $wrap = $(this).parents('.J-page-wrap:first');
        var page = parseInt($wrap.attr('cz-page') || 1);

        turnPage($wrap, page - 1);
    });

    $('.J-page-next').click(function () {
        var $wrap = $(this).parents('.J-page-wrap:first');
        var page = parseInt($wrap.attr('cz-page') || 1);

        turnPage($wrap, page + 1);
    });

    $('.J-page-wrap').each(function () {
        var total = $(this).data('total');

        $(this).find('.J-page-txt-total').html(Math.ceil(total / 5));
    })

    var parseData = {
        brandName: function (data) {
            var list = data.listData || [];
            var reslist = [];
            $.each(list, function (i, item) {
                reslist.push({
                    word: item.brandName,
                })
            })
            return reslist;
        },
        productCompanyName: function (data) {
            var list = data.data || [];
            var reslist = [];
            $.each(list, function (i, item) {
                reslist.push({
                    word: item.label,
                })
            })
            return reslist;
        },
        departmentName: function (data) {
            var list = data.data || [];
            var reslist = [];
            $.each(list, function (i, item) {
                reslist.push({
                    word: item.label,
                })
            })
            return reslist;
        }
    }

    //搜索建议词
    $('.J-suggest-input').each(function () {
        var _this = this;
        new Suggest({
            el: this,
            url: page_url + $(this).data('url'),
            params: $(this).attr('name'),
            parseData: function (data) {
                return parseData[$(_this).attr('name')](data);
            }
        });
    })

    //分类搜索三级
    new LvSelect({
        el: '.J-category-wrap',
        input: '.J-category-value',
        async: true,
        url: page_url + '/category/base/getCategoryList.do',
        parseData: function (res) {
            var categoryData = [];

            $.each(res.listData, function (i, lv1) {
                var lv1item = {
                    label: lv1.baseCategoryName,
                    value: lv1.baseCategoryId,
                    child: []
                };

                if (lv1.secondCategoryList) {
                    $.each(lv1.secondCategoryList, function (ii, lv2) {
                        var lv2item = {
                            label: lv2.baseCategoryName,
                            value: lv2.baseCategoryId,
                            child: []
                        };

                        if (lv2.thirdCategoryList) {
                            $.each(lv2.thirdCategoryList, function (iii, lv3) {
                                lv2item.child.push({
                                    label: lv3.baseCategoryName,
                                    value: lv3.baseCategoryId
                                })
                            })
                        }

                        if (lv2item.child.length) {
                            lv1item.child.push(lv2item);
                        }
                    })
                }

                if (lv1item.child.length) {
                    categoryData.push(lv1item);
                }
            })

            return categoryData;
        }
    })

    //新国标三级
    new LvSelect({
        el: '.J-stand-wrap',
        input: '.J-stand-value',
        async: true,
        url: page_url + '/firstengage/baseinfo/allstandard.do',
        parseData: function (res) {
            var newStandData = [];
            $.each(res.data, function (i, lv1) {
                var lv1item = {
                    label: lv1.categoryName,
                    value: lv1.standardCategoryId,
                    child: []
                };

                if (lv1.standardCategoryList && lv1.standardCategoryList.length) {
                    $.each(lv1.standardCategoryList, function (ii, lv2) {
                        var lv2item = {
                            label: lv2.categoryName,
                            value: lv2.standardCategoryId,
                            child: []
                        };

                        if (lv2.standardCategoryList && lv2.standardCategoryList.length) {
                            $.each(lv2.standardCategoryList, function (iii, lv3) {
                                lv2item.child.push({
                                    label: lv3.categoryName,
                                    value: lv3.standardCategoryId
                                })
                            })
                        }

                        if (lv2item.child.length) {
                            lv1item.child.push(lv2item);
                        }
                    })
                }

                if (lv1item.child.length) {
                    newStandData.push(lv1item);
                }
            })

            return newStandData;
        }
    })

    //操作提示
    GLOBAL.showGlobalTip('操作成功', null, 'spuListViewOption');

    $('#batchSaveAuthorization').click(function(){
        batchSaveAuthorization();
    })

    //禁用spu
    $(".J-invalid-spu").click(function () {
        var currentSpuId = $(this).attr('data-spuid');
        //在这里面输入任何合法的js语句
        layer.open({
            type: 1,
            closeBtn: 1,
            title: "确认禁用该商品的信息吗?",
            shadeClose: true,
            area:["510px","250px"],
            content:  "<div hidden id='inValidGoods' data-relatedId=" + currentSpuId + " data-type='1'/>" +
                "<div style='width:500px;margin-left: 10px;margin-top: 10px'><p style='display: inline-block'><span style='color: red'>*</span>禁用原因</p><textarea type='text' style='margin-left: 10px;height: 100px;width: 400px' id='invalidReason' name='invalidReason' value=''/></div>"+
                "<div style='text-align: center;margin-top: 20px'><button  class='layui-btn layui-btn-normal ' onclick='invalidGoods()'>提交</button><button class='layui-btn layui-btn-normal ' style='margin-left: 30px;background-color:  #ADADAD' onclick='closeLayer()' >取消</button></div>"
        });
    });
    //启用spu
    $(".J-valid-spu").click(function () {
        var currentSpuId = $(this).attr('data-spuid');
        validGoods(currentSpuId,"1");
    });
    //禁用sku
    $(document).on('click', '.J-invalid-sku', function () {
        var currentSkuId = $(this).attr('data-skuid');
        layer.open({
            type: 1,
            closeBtn: 1,
            title: "确认禁用该商品的信息吗?",
            shadeClose: true,
            area:["510px","250px"],
            content:  "<div hidden id='inValidGoods' data-relatedId=" + currentSkuId + " data-type='2'/>" +
                "<div style='width:500px;;margin-left: 10px;margin-top: 10px'><p style='display: inline-block'><span style='color: red'>*</span>禁用原因</p><textarea type='text' style='margin-left: 10px;height: 100px;width: 400px' id='invalidReason' name='invalidReason' value=''/></div>"+
                "<div style='text-align: center;margin-top: 20px'><button  class='layui-btn layui-btn-normal ' onclick='invalidGoods()'>提交</button><button class='layui-btn layui-btn-normal ' style='margin-left: 30px;background-color:  #ADADAD' onclick='closeLayer()' >取消</button></div>"

        });
        return true
    });
    //启用sku
    $(document).on('click', '.J-valid-sku', function () {
        var currentSkuId = $(this).attr('data-skuid');
        validGoods(currentSkuId,"2");
    });
});


function uploadTaxCategoryNo() {
    $.ajax({
        type: 'GET',
        url: "/goods/goods/uplodeTaxCategoryNo.do?id=1",
        dataType : "html",
        success: function(result) {
            var htmlCont = result;
            var open = layer.open({
                type: 1,
                title: '上传税收分类编码',
                shadeClose: false,
                area : ['500px', '200px'],
                content: htmlCont,
                success: function(layero, index){

                }
            });
            $('#layerIndex').val(open);
        }
    });
}
function skuMove() {
    $.ajax({
        type: 'GET',
        url: "/goods/vgoods/skuMove.do?id=1",
        dataType : "html",
        success: function(result) {
            var htmlCont = result;
            var open = layer.open({
                type: 1,
                title: 'SKU迁移SPU',
                shadeClose: false,
                area : ['500px', '400px'],
                content: htmlCont,
                success: function(layero, index){

                },error:function(){
                    layer.alert("没有权限")
                }
            });
            $('#layerIndex').val(open);
        }
    });
}


function viewSpuDetailWhenClick(spuid) {
    layer.open({
        type:2,
        title:'查看spu详情',
        area: ['850px', '600px'],
        btn:['关闭'],
        content: page_url+"/goods/vgoods/viewSpu.do?spuId="+spuid,
        yes:function (index, layero) {
            layer.close(index)
        }
    })
}

function changeSpuAndSkuCountWhenChecked() {
    var spuCount=0;
    var skuCount=0;
     $('#spuPrepareTable tbody input[type=checkbox]').each(function () {
         if(this.checked){
            spuCount++;
            var text = $(this).parent().parent().children('.ownSkuNum').text();
            skuCount+= parseInt(text)
         }
     });

    $('#spuCount').text(spuCount);
    $('#skuCount').text(skuCount);
}

//spu迁移增补属性明细
function showChangedPropertyValue(targetCategoryId, spuId) {
    layer.open({
        title: 'SPU迁移属性列表页',
        content: page_url + '/goods/vgoods/getSpuRemovalDetail.do?categoryId='+targetCategoryId+"&spuId="+ spuId,
        type: 2,
        area: ['850px', '600px'],
        btn: ['确认']
        ,btn1: function(index, layero){
            layer.close(index);
        }
    })
}

/**
 * 批量维护报备信息
 */
function batchSaveAuthorization() {
    var skuCheckBox = $("input[class='J-select-sku']:checked");
    if (skuCheckBox.length < 1){
        layer.alert('请选中SKU进行报备信息维护');
        return;
    }

    var skuIdsStr = '';
    var hasEditAuthFlag = 1;
    skuCheckBox.each(function () {
        var hasEditAuth = $(this).attr('hasEditAuth');
        if (hasEditAuth == 0){
            hasEditAuthFlag = 0;
        }
        skuIdsStr += ($(this).val());
        skuIdsStr += '@';
    });

    if (hasEditAuthFlag == 0){
        layer.alert('只可维护当前用户名下的sku', function () {
           location.reload();
        })
        return;
    }

    var open = layer.open({
        type: 1,
        title: '维护报备信息',
        shadeClose: false,
        area : ['1200px', '500px'],
        content: '<iframe style="width: calc(100% - 10px);height: calc(100% - 10px); border: 0;" src="/goods/vgoods/skuAuthorizationInit.do?skuIdsStr=' + skuIdsStr + '"></iframe>',
        success: function(layero, index){

        }
    });

    // $.ajax({
    //     type: 'POST',
    //     data:{
    //         skuIdsStr:skuIdsStr
    //     },
    //     url: "/goods/vgoods/skuAuthorizationInit.do",
    //     dataType : "html",
    //     success: function(result) {
    //         var htmlCont = result;
    //         var open = layer.open({
    //             type: 1,
    //             title: '维护报备信息',
    //             shadeClose: false,
    //             area : ['1200px', '500px'],
    //             content: htmlCont,
    //             success: function(layero, index){
    //
    //             }
    //         });
    //         $('#layerIndex').val(open);
    //     }
    // });

}

function closeLayer(){
    layer.closeAll();
}



function checkInvalidSpu(spuId) {


    $.ajax({
            type: 'POST',
            data:{
                spuId:spuId,
                type: 1
            },
            async: false,
            url: page_url+"/goods/vgoods/checkInvalidSpuOrSku.do",
            dataType : "json",
            success: function(result) {
                console.log(result);
                debugger;
                if(result.code == 2002 || result.code == 3002){
                    layer.msg(result.message, {
                        time: 0 //不自动关闭
                        ,btn: ['查看', '关闭']
                        ,yes: function(index){
                            var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
                            var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random()*1000);
                            var uri = page_url +"/goods/goods/viewbaseinfo.do?goodsId="+spuId;
                            var item = { 'id': id, 'name': "查看库存", 'url': uri, 'closable': true};
                            // self.parent.closableTab.addTab(item);
                            // self.parent.closableTab.resizeMove();
                            // $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
                            if (typeof(self.parent.closableTab) != 'undefined') {
                                self.parent.closableTab.addTab(item);
                                self.parent.closableTab.resizeMove();
                                $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
                            }else{
                                try{
                                    var uniqueName = uri.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
                                    window.parent.postMessage({
                                        from:'ez',
                                        name: title,
                                        url:uri,
                                        id:"tab-"+uniqueName
                                    }, '*');
                                }catch (e){}
                            }
                            closeLayer;
                        }
                    });
                }else if(result.code != 0 ){
                    console.log(result);
                    layer.close(index);
                    layer.alert(result.message)
                } else {
                    layer.open({
                        type: 2,
                        area: ['700px', '450px'],
                        fixed: false, //不固定
                        maxmin: true,
                        content: '<div><div>'
                    });
                }
            },
            error: function () {
                layer.alert("访问出错");
            }
        });
}

function validSpuOrSku(spuIdOrSkuId,type){
    if(type == 1){
        validSpu(spuIdOrSkuId);
    } else if(type == 2){
        validSku(spuIdOrSkuId);
    }
}

function invalidGoods() {
    debugger;
    let relatedId = $('#inValidGoods').attr('data-relatedId');
    let type = $('#inValidGoods').attr('data-type');
    let reason = $('#invalidReason').val();
    console.log(reason);
    console.log(relatedId);
    $.ajax({
        type: 'POST',
        async: false,
        data: JSON.stringify({disableReason: reason,
               goodsType: type,
                relatedId: relatedId
            }),
        contentType: "application/json;charset=utf-8",
        url: page_url+"/goods/vgoods/disableGoodsApplyVerify.do",
        dataType: "json",
        success: function(result) {
            if(result.code == 2002|| result.code == 3002){
                layer.alert(result.message, {
                    time: 0 //不自动关闭
                    ,btn: ['查看', '关闭']
                    ,yes: function(index){
                        layer.closeAll();
                        var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
                        var id = 'index' + Date.parse(new Date()) + Math.floor(Math.random()*1000);
                        var uri = page_url +"/goods/goods/viewbaseinfo.do?goodsId="+relatedId;
                        var item = { 'id': id, 'name': "查看库存", 'url': uri, 'closable': true};
                        // self.parent.closableTab.addTab(item);
                        // self.parent.closableTab.resizeMove();
                        // $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);

                        if (typeof(self.parent.closableTab) != 'undefined') {
                            self.parent.closableTab.addTab(item);
                            self.parent.closableTab.resizeMove();
                            $(window.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
                        }else{
                            try{
                                var uniqueName = uri.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
                                window.parent.postMessage({
                                    from:'ez',
                                    name: title,
                                    url:uri,
                                    id:"tab-"+uniqueName
                                }, '*');
                            }catch (e){}
                        }
                    }
                });
            } else if(result.code !=0){
                layer.alert(result.message);
            } else {
                layer.msg("已申请禁用",{
                    time:3000},
                    function () {
                        location.reload();
                    }
                )
            }
        }
    })
}

function validGoods(relatedId,type) {
    debugger;
    /*
    var relatedId= $('#inValidGoods').attr('data-relatedId');
    var type= $('#inValidGoods').attr('data-type');
    */
    console.log(relatedId);
    $.ajax({
        type: 'GET',
        async: false,
        dataType: "json",
        url: page_url+"/goods/vgoods/enableGoods.do?relatedId="+relatedId+"&goodsType="+type,
        success: function(result) {
            if(result.code !=0){
                layer.alert(result.message);
            } else {
                layer.msg("启用成功",{
                        time:1000},
                    function () {
                        location.reload();
                    }
                )
            }
        }
    })
}