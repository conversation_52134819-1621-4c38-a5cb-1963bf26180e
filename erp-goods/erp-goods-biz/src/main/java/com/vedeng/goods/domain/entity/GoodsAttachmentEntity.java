package com.vedeng.goods.domain.entity;

import lombok.Data;

import java.io.Serializable;

@Data
public class GoodsAttachmentEntity implements Serializable{
    private static final long serialVersionUID = 1L;

    private Integer goodsAttachmentId;

    private Integer goodsId;

    private Integer attachmentType;

    private String domain;

    private String uri;

    private String url;

    private String alt;

    private Integer sort;

    private Integer isDefault;

    private Integer status;

    private String ossResourceId;

    private String originalFilepath;

    private Integer synSuccess;

    private Long costTime;

    private String displayName;
}