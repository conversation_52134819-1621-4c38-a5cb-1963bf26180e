package com.vedeng.erp.common.broadcast.param;

import java.util.Date;

import lombok.Data;

/**
 * 时间区间
 * @ClassName:  TimePeriod   
 * @author: <PERSON><PERSON>yang
 * @date:   2025年6月6日 下午5:45:52    
 * @Copyright:
 */
@Data
public class TimePeriod {
	
	/**统计开始时间*/
	private Date startTime;
	
	/**统计结束时间*/
	private Date endTime;
	
	/**月开始时间*/
	private Date monthStartTime;
	
	/**月结束时间*/
	private Date monthEndTime;
	
	/**年份*/
	private Integer year;
	
	/**月份*/
	private Integer month;
	

	public TimePeriod(Date startTime, Date endTime) {
		super();
		this.startTime = startTime;
		this.endTime = endTime;
	}
	
	public TimePeriod(Date startTime,Date endTime,Date monthStartTime,Date monthEndTime) {
		super();
		this.startTime = startTime;
		this.endTime = endTime;
		this.monthStartTime = monthStartTime;
		this.monthEndTime = monthEndTime;
	}
	
	public TimePeriod(Date startTime,Date endTime,Date monthStartTime,Date monthEndTime,Integer year,Integer month) {
		super();
		this.startTime = startTime;
		this.endTime = endTime;
		this.monthStartTime = monthStartTime;
		this.monthEndTime = monthEndTime;
		this.year = year;
		this.month = month;
	}

}
