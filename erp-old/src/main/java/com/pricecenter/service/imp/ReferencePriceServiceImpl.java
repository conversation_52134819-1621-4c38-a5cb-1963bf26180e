package com.pricecenter.service.imp;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.pricecenter.dto.*;
import com.pricecenter.service.ReferencePriceService;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.util.JsonUtils;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

@Service
public class ReferencePriceServiceImpl implements ReferencePriceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReferencePriceServiceImpl.class);

    @Value("${price.url}")
    private String priceUrl;

    private static String REFERENCE_PRICE_PAGE = "referencePrice/list/page";

    private static String REFERENCE_PRICE_DETAIL = "referencePrice/findById";

    private static String REFERENCE_PRICE_EDIT_REFERENCEPRICE = "referencePrice/editReferencePrice";

    private static String REFERENCE_PRICE_BATCH_UPLOAD = "referencePrice/batchUpload";

    private static String SUCCESS_CODE = "success";

    @Override
    public PageResultDto<SkuReferencePriceResponseDto> findByPage(SkuReferencePricePageQueryDto queryDto) {
        PageResultDto pageResultDto = new PageResultDto();

        try {
            //封装请求参数
            String requestJson = JsonUtils.translateToJson(queryDto);


            LOGGER.info("ReferencePriceServiceImpl->findByPage 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + REFERENCE_PRICE_PAGE, requestJson);

            LOGGER.info("ReferencePriceServiceImpl->findByPage 响应:" + resultJsonObj.toString());

            if (!SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                return pageResultDto;
            }

            Gson gson = new Gson();

            List<SkuReferencePriceResponseDto> referencePriceList = gson.fromJson(resultJsonObj.getJSONObject("data").get("referencePriceList").toString(),
                    new TypeToken<List<SkuReferencePriceResponseDto>>() {
                    }.getType());

            //获取总记录数
            String totleRecord = resultJsonObj.getJSONObject("data").get("totalRecord").toString();
            pageResultDto.setDatas(referencePriceList);
            pageResultDto.setTotalRecords(Integer.valueOf(totleRecord));

        } catch (Exception e) {
            LOGGER.error("参考价列表分页查询失败", e);
        }
        return pageResultDto;
    }

    @Override
    public SkuReferencePriceResponseDto findReferencePriceById(Integer referencePriceId) {

        try {

            //封装请求参数
            Map<String, Object> requestMap = new HashMap<String, Object>();
            requestMap.put("referencePriceId", referencePriceId);
            String requestJson = JsonUtils.translateToJson(requestMap);


            LOGGER.info("ReferencePriceServiceImpl->findReferencePriceById 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + REFERENCE_PRICE_DETAIL, requestJson);

            LOGGER.info("ReferencePriceServiceImpl->findReferencePriceById 响应:" + resultJsonObj.toString());

            if (!SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                return null;
            }

            Gson gson = new Gson();
            SkuReferencePriceResponseDto skuReferencePriceResponseDto = gson.fromJson(resultJsonObj.getJSONObject("data").toString(),
                    new TypeToken<SkuReferencePriceResponseDto>() {
                    }.getType());

            return skuReferencePriceResponseDto;

        } catch (IOException e) {
            LOGGER.error("基础信息核价列表详情查询", e);
        }

        return null;
    }

    @Override
    public void editReferencePrice(EditReferencePriceDto editReferencePriceDto) {

        try {
            //封装请求参数
            String requestJson = JsonUtils.translateToJson(editReferencePriceDto);

            LOGGER.info("ReferencePriceServiceImpl->editReferencePrice 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + REFERENCE_PRICE_EDIT_REFERENCEPRICE, requestJson);

            LOGGER.info("ReferencePriceServiceImpl->editReferencePrice 响应:" + resultJsonObj.toString());

        } catch (IOException e) {
            LOGGER.error("编辑参考价失败", e);
        }
    }

    @Override
    public void batchUploadReferencePrice(List<ReferencePriceUploadDto> referencePriceDtoList) {

        try {

            //封装请求参数
            Map<String, Object> requestMap = new HashMap<String, Object>();
            requestMap.put("referencePriceDtoList", referencePriceDtoList);
            String requestJson = JsonUtils.translateToJson(requestMap);

            LOGGER.info("ReferencePriceServiceImpl->batchUploadReferencePrice 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + REFERENCE_PRICE_BATCH_UPLOAD, requestJson);

            LOGGER.info("ReferencePriceServiceImpl->batchUploadReferencePrice 响应:" + resultJsonObj.toString());

        } catch (IOException e) {
            LOGGER.error("批量上传核价信息失败", e);
        }

    }
}
