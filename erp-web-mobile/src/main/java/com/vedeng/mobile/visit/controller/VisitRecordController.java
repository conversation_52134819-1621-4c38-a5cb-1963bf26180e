package com.vedeng.mobile.visit.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.base.Page;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.trace.annotation.EventTrackingAnnotation;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.erp.system.common.enums.UserOrgEnums;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.dto.VisitDeleteDto;
import com.vedeng.erp.trader.dto.VisitInputDto;
import com.vedeng.erp.trader.dto.VisitSearchDto;
import com.vedeng.erp.trader.service.VisitRecordApiService;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.vedeng.mobile.system.dto.*;
import com.vedeng.mobile.system.service.TraderCustomerSearchService;
import com.vedeng.mobile.system.service.VisitRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/2
 */
@Slf4j
@RestController
@RequestMapping("/mobile/visit/record")
@RequiredArgsConstructor
public class VisitRecordController {

    @Value("${mobile.visitConfigList:[]}")
    private String visitConfigList;

    @Value("${contentTips:提示信息}")
    private String contentTips;


    @Autowired
    private VisitRecordService visitRecordService;


    @Autowired
    private TraderCustomerSearchService traderCustomerSearchService;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Autowired
    private UserApiService userService;


    public void dealVisitTargetNames(VisitInputDto visitInputDto) {
        if (visitInputDto != null && StringUtils.isNotBlank(visitInputDto.getVisitTarget())) {
            List<VisitConfigDto> visitConfigDtoList = JSONObject.parseArray(visitConfigList, VisitConfigDto.class);
            //将visitConfigDtoList转换为map，key为visitType，value为visitName
            Map<String, String> visitConfigMap = visitConfigDtoList.stream().collect(Collectors.toMap(VisitConfigDto::getVisitType, VisitConfigDto::getVisitName));
            //将VisitInputDto.getVisitTarget()按英文逗号分割，并将visitConfigList转换成map，获取对应的中文名称
            String[] visitTargetArr = visitInputDto.getVisitTarget().split(",");
            List<String> visitTargetNameList = new ArrayList<>();
            for (String visitTarget : visitTargetArr) {
                visitTargetNameList.add(visitConfigMap.get(visitTarget));
            }
            visitInputDto.setVisitTargetNames(StringUtils.join(visitTargetNameList, ","));
            visitInputDto.setContentTips(contentTips);

        }
    }

    /**
     *
     * @param visitInputDto
     * @param userId
     * @param userIds
     */
    public void dealVisitEditAndDelete(VisitInputDto visitInputDto,Integer userId,List<Integer> userIds){
        if(visitInputDto.getCardOff() == null || StringUtils.equals("N",visitInputDto.getCardOff())){//未打卡的情况
            if(userId !=null && userId.equals(visitInputDto.getAddUserId()) ){
                visitInputDto.setEditAble(true);
            }else if(visitInputDto.getAddUserId() !=null && userIds.contains(visitInputDto.getAddUserId()) ){
                visitInputDto.setEditAble(true);
                visitInputDto.setDeleteAble(true);
            }

        }
    }


    /**
     * 获取文件名的后缀
     * @param fileName
     * @return
     */
    public static String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf(".");
        if (dotIndex != -1 && dotIndex < fileName.length() - 1) {
            return fileName.substring(dotIndex + 1);
        } else {
            return "";
        }
    }



    @ResponseBody
    @RequestMapping(value = "/searchVisitRecordListPage")
    public R<PageResult<VisitInputDto>> searchVisitRecordListPage(@RequestBody VisitSearchDto visitSearchDto,HttpServletRequest request) {
        String appVersion = request.getHeader("app-version");
        log.info("appVersion is:{}",appVersion);
        return R.error("请使用【企微-灵犀CRM】操作拜访") ;
//        log.info("searchCustomerList :"+JSONObject.toJSONString(visitSearchDto));
//        Page page = new Page(visitSearchDto.getPageNo(), visitSearchDto.getPageSize());
//        try {
//            List<Integer> xiashuUserIds = new ArrayList<>();
//            //如果发现传的用户ID都为空，则根据当前人去查所有的下属，即查看全部下属的用户
//            if(CollectionUtils.isEmpty(visitSearchDto.getUserIdList())){
//                List<Integer> userIds = userService.getAllSubordinateByUserIdForVisit(visitSearchDto.getUserId());
//                xiashuUserIds.addAll(userIds);
//            }else{
//                List<Integer> userIds = userService.getAllSubordinateByUserIdForVisit(visitSearchDto.getUserId());
//                xiashuUserIds.addAll(userIds);
//            }
//            PageInfo<VisitInputDto> pageInfo =  visitRecordService.searchVisitRecordListPage(visitSearchDto, page);
//            PageResult<VisitInputDto> resultPage = new PageResult<>();
//            if(CollectionUtils.isNotEmpty(pageInfo.getList())){
//                for(VisitInputDto visitInputDto : pageInfo.getList()){
//                    dealVisitTargetNames(visitInputDto);
//                    dealVisitEditAndDelete(visitInputDto,visitSearchDto.getUserId(),xiashuUserIds);
//                }
//            }
//            resultPage.setDataList(pageInfo.getList());
//            resultPage.setPageSize(pageInfo.getPageSize());
//            resultPage.setPageNo(pageInfo.getPageNum());
//            resultPage.setTotalPage(pageInfo.getPages());
//            resultPage.setTotalRecord(pageInfo.getTotal());
//            return R.success(resultPage);
//
//        }catch (Exception e){
//            log.error("searchCustomerList failure", e);
//            return R.error("查询失败");
//        }

    }


    /**
     * 创建拜访计划
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/checkPermission")
    public R<VisitInputDto> checkPermission( @RequestParam(required = true) Integer userId) {
        return R.error("请使用【企微-灵犀CRM】操作拜访") ;
//        try {
//            UserOrgEnums userOrgEnums = userService.getUserOrgEnum(userId);
//            if (userOrgEnums.equals(UserOrgEnums.ORG_OTHER)) {
//                log.warn("目前仅对营销中心部分部门开放，如有需求请联系研发产品");
//                return R.error("目前仅对营销中心部分部门开放，如有需求请联系研发产品");
//            }
//            return R.success("可访问拜访计划");
//        } catch (Exception e) {
//            log.error("checkPermission failure", e);
//            return R.error("目前仅对营销中心部分部门开放，如有需求请联系研发产品");
//        }
    }

    /**
     * 创建拜访计划
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/create")
    public R<VisitInputDto> create(@RequestBody VisitInputDto visitInputDto) {
        try{
            log.info("visitInputDto:{}", JSONObject.toJSONString(visitInputDto));
            if(StringUtils.isBlank(visitInputDto.getAreaName())){
                log.warn("客户地址请选择到具体的区。{}",new Object[]{JSONObject.toJSONString(visitInputDto)});
                return R.error("请选择客户所在地区");
            }
            UserOrgEnums userOrgEnums = userService.getUserOrgEnum(visitInputDto.getUserId());
            if(userOrgEnums.equals(UserOrgEnums.ORG_OTHER)){
                log.warn("目前仅对营销中心部分部门开放，如有需求请联系研发产品；");
                return R.error("目前仅对营销中心部分部门开放，如有需求请联系研发产品；");
            }
            if(userOrgEnums.equals(UserOrgEnums.ORG_DAQU) ){
                TraderCustomerInputDto traderCustomerInputDto = new TraderCustomerInputDto();
                traderCustomerInputDto.setTraderCustomerId(visitInputDto.getTraderCustomerId());
                traderCustomerInputDto.setTraderId(visitInputDto.getTraderId());
                traderCustomerInputDto.setTraderName(visitInputDto.getCustomerName());
                traderCustomerInputDto.setProvinceCode(visitInputDto.getProvinceCode());
                traderCustomerInputDto.setProvinceName(visitInputDto.getProvinceName());
                traderCustomerInputDto.setCityCode(visitInputDto.getCityCode());
                traderCustomerInputDto.setCityName(visitInputDto.getCityName());
                traderCustomerInputDto.setAreaCode(visitInputDto.getAreaCode());
                traderCustomerInputDto.setAreaName(visitInputDto.getAreaName());
                traderCustomerInputDto.setCustomerFrom(visitInputDto.getCustomerFrom());
                traderCustomerInputDto.setUserId(visitInputDto.getUserId());
                boolean checkCanSelected = traderCustomerSearchService.checkTraderCanSelected(traderCustomerInputDto);
                if(!checkCanSelected ){
                    return R.error("您无权拜访此区域客户");
                }
            }
            Integer traderCustomerId = visitInputDto.getTraderCustomerId();
            if(traderCustomerId == null || traderCustomerId == 0){
                List<TraderCustomerSearchResultDto> traderlist = traderCustomerSearchService.searchTraderCustomerByName(visitInputDto.getCustomerName());
                if(CollectionUtils.isNotEmpty(traderlist)){
                    TraderCustomerSearchResultDto trader = traderlist.get(0);
                    visitInputDto.setTraderId(trader.getTraderId());
                    visitInputDto.setTraderCustomerId(trader.getTraderCustomerId());
                    visitInputDto.setCustomerTz(trader.getTzCustomer());
                    visitInputDto.setCustomerLevel(trader.getTzCustomerLevel());
                    visitInputDto.setCustomerFrom(1);//修改成来源于ERP客户
                }
            }

            visitInputDto.setOrgId(0);
            visitInputDto.setOrgGroup(userOrgEnums.getOrgGroupName());
            VisitInputDto record = visitRecordService.insertVisitRecord(visitInputDto);
            dealVisitTargetNames(record);
            visitInputDto.setEditAble(true);
            visitInputDto.setDeleteAble(false);
            return R.success(record);
        } catch (Exception e) {
            log.error("visitInputDto create failure", e);
            return R.error("创建拜访记录失败");
        }

    }

    /**
     *  编辑拜访计划
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/edit")
    public R<VisitInputDto> edit(@RequestBody VisitInputDto visitInputDto) {
        try{
            log.info("visitInputDto:{}", JSONObject.toJSONString(visitInputDto));
            if(StringUtils.isBlank(visitInputDto.getAreaName())){
                log.warn("系统优化了拜访计划等功能，请更新版本再使用；{}",new Object[]{JSONObject.toJSONString(visitInputDto)});
                return R.error("系统优化了拜访计划等功能，请更新版本再使用");
            }
            UserOrgEnums userOrgEnums = userService.getUserOrgEnum(visitInputDto.getUserId());
            if(userOrgEnums.equals(UserOrgEnums.ORG_OTHER)){
                log.warn("目前仅对营销中心部分部门开放，如有需求请联系研发产品；");
                return R.error("目前仅对营销中心部分部门开放，如有需求请联系研发产品；");
            }
            if(userOrgEnums.equals(UserOrgEnums.ORG_DAQU) ){
                TraderCustomerInputDto traderCustomerInputDto = new TraderCustomerInputDto();
                traderCustomerInputDto.setTraderCustomerId(visitInputDto.getTraderCustomerId());
                traderCustomerInputDto.setTraderId(visitInputDto.getTraderId());
                traderCustomerInputDto.setTraderName(visitInputDto.getCustomerName());
                traderCustomerInputDto.setProvinceCode(visitInputDto.getProvinceCode());
                traderCustomerInputDto.setProvinceName(visitInputDto.getProvinceName());
                traderCustomerInputDto.setCityCode(visitInputDto.getCityCode());
                traderCustomerInputDto.setCityName(visitInputDto.getCityName());
                traderCustomerInputDto.setAreaCode(visitInputDto.getAreaCode());
                traderCustomerInputDto.setAreaName(visitInputDto.getAreaName());
                traderCustomerInputDto.setCustomerFrom(visitInputDto.getCustomerFrom());
                traderCustomerInputDto.setUserId(visitInputDto.getUserId());
                boolean checkCanSelected = traderCustomerSearchService.checkTraderCanSelected(traderCustomerInputDto);
                if(!checkCanSelected ){
                    return R.error("您无权拜访此区域客户");
                }
            }
            VisitInputDto record = visitRecordService.editVisitRecord(visitInputDto);
            dealVisitTargetNames(record);
            List<Integer> userIds = userService.getAllSubordinateByUserIdForVisit(visitInputDto.getUserId());
            dealVisitEditAndDelete(record,visitInputDto.getUserId(),userIds);
             boolean canCheckTraderDetail = visitRecordService.canCheckTraderDetail(userOrgEnums,record,record.getUserId(),userIds);
            record.setCanCheckTraderDetail(canCheckTraderDetail);
            return R.success(record);
        } catch (Exception e) {
            log.error("visitInputDto create failure", e);
            return R.error("创建拜访记录失败");
        }

    }

    @ResponseBody
    @RequestMapping(value = "/query")
    public R<VisitInputDto> queryVisitRecordDetail(@RequestParam("id") Integer id,@RequestParam("userId") Integer userId) {
        try{
            log.info("visitInputDto:{}", id);
            VisitInputDto record = visitRecordService.queryVisitRecordById(id);
            List<Integer> userIds = userService.getAllSubordinateByUserIdForVisit(userId);
            dealVisitTargetNames(record);
            dealVisitEditAndDelete(record,userId,userIds);
             UserOrgEnums userOrgEnums = userService.getUserOrgEnum(userId);
            boolean canCheckTraderDetail = visitRecordService.canCheckTraderDetail(userOrgEnums,record,userId,userIds);
            record.setCanCheckTraderDetail(canCheckTraderDetail);




            return R.success(record);
        } catch (Exception e) {
            log.error("visitInputDto query failure", e);
            return R.error("查询拜访记录失败");
        }
    }




    @ResponseBody
    @RequestMapping(value = "/addCard")
    public R<VisitInputDto> addCard(@RequestBody VisitCardInputDto visitCardInputDto) {
        try{
            log.info("visitInputDto:{}", visitCardInputDto);
            VisitInputDto visitInputDto = visitRecordService.queryVisitRecordById(visitCardInputDto.getId());
            if(StringUtils.isBlank(visitInputDto.getAreaName())){
                log.warn("打卡，系统优化了拜访计划等功能，请更新版本再使用；{}",new Object[]{JSONObject.toJSONString(visitInputDto)});
                return R.error("系统优化了拜访计划等功能，请更新版本再使用");
            }

            VisitInputDto record = visitRecordService.saveVisitCard(visitCardInputDto);
            List<Integer> userIds = userService.getAllSubordinateByUserIdForVisit(visitCardInputDto.getUserId());
            dealVisitEditAndDelete(record,visitCardInputDto.getUserId(),userIds);
            dealVisitTargetNames(record);
            UserOrgEnums userOrgEnums = userService.getUserOrgEnum(visitInputDto.getUserId());
            boolean canCheckTraderDetail = visitRecordService.canCheckTraderDetail(userOrgEnums,record,record.getUserId(),userIds);
            record.setCanCheckTraderDetail(canCheckTraderDetail);

            return R.success("打卡成功",record);
        } catch (Exception e) {
            log.error("visitInputDto query failure", e);
            return R.error("打卡失败");
        }

    }


    /**
     * 删除拜访计划
     * @param visitDeleteDto
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/delete")
    public R<VisitInputDto> delete(@RequestBody VisitDeleteDto visitDeleteDto) {
        try{
            log.info("visitDeleteDto:{}", visitDeleteDto);
            boolean result = visitRecordService.deleteVisitRecord(visitDeleteDto);
            if(!result){
                return R.error("删除失败");
            }
            return R.success("删除成功");
        } catch (Exception e) {
            log.error("visitDeleteDto delete failure", e);
            return R.error("删除失败");
        }

    }

    @RequestMapping(value = "/uploadFile")
    @ResponseBody
    public R<?> uploadUpgradeFile(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        String fileName = file.getOriginalFilename();

        InputStream inputStream = null;
        try{
            inputStream = file.getInputStream();
            String extension = getFileExtension(fileName);
            String fileUrl = ossUtilsService.upload2OssForInputStream(extension,fileName,inputStream);
            if(StringUtils.isNotBlank(fileUrl)){
                return R.success("上传照片成功",fileUrl);
            }
        }catch (Exception e){
            log.error("上传照片失败",e);
            return R.error("上传照片失败"+e.getMessage());
        }finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                log.error("上传照片失败，流关闭失败");
                return R.error("上传照片失败");
            }
        }
        return R.error("上传照片失败");
    }


    //获取用户上传的拜访内容
    @ResponseBody
    @RequestMapping(value = "/saveVisitContent")
    public R<VisitInputDto> saveVisitContent(@RequestBody VisitContentInputDto visitContentInputDto){
        try{
            log.info("saveVisitContent-visitContentInputDto:{}", JSONObject.toJSONString(visitContentInputDto));
            VisitInputDto visitInputDto = visitRecordService.queryVisitRecordById(visitContentInputDto.getId());
            if(StringUtils.isBlank(visitInputDto.getAreaName())){
                log.warn("打卡，系统优化了拜访计划等功能，请更新版本再使用；{}",new Object[]{JSONObject.toJSONString(visitInputDto)});
                return R.error("系统优化了拜访计划等功能，请更新版本再使用");
            }
            VisitInputDto record = visitRecordService.saveVisitContent(visitContentInputDto);
            dealVisitTargetNames(record);
            List<Integer> userIds = userService.getAllSubordinateByUserIdForVisit(visitContentInputDto.getUserId());
            dealVisitEditAndDelete(record,visitContentInputDto.getUserId(),userIds);
            UserOrgEnums userOrgEnums = userService.getUserOrgEnum(visitInputDto.getUserId());
            boolean canCheckTraderDetail = visitRecordService.canCheckTraderDetail(userOrgEnums,record,visitContentInputDto.getUserId(),userIds);
            record.setCanCheckTraderDetail(canCheckTraderDetail);
            return R.success("保存拜访内容成功",record);
        } catch (Exception e) {
            log.error("visitInputDto query failure", e);
            return R.error("保存拜访内容失败");
        }

    }

    @ResponseBody
    @RequestMapping(value = "/editVisitContent")
    public R<VisitInputDto> editVisitContent(@RequestBody VisitContentInputDto visitContentInputDto){
        try{
            log.info("editVisitContent-visitContentInputDto:{}", JSONObject.toJSONString(visitContentInputDto));
            VisitInputDto visitInputDto = visitRecordService.queryVisitRecordById(visitContentInputDto.getId());
            if(StringUtils.isBlank(visitInputDto.getAreaName())){
                log.warn("打卡，系统优化了拜访计划等功能，请更新版本再使用；{}",new Object[]{JSONObject.toJSONString(visitInputDto)});
                return R.error("系统优化了拜访计划等功能，请更新版本再使用");
            }
            VisitInputDto record = visitRecordService.editVisitContent(visitContentInputDto);
            List<Integer> userIds = userService.getAllSubordinateByUserIdForVisit(visitContentInputDto.getUserId());
            dealVisitTargetNames(record);
            dealVisitEditAndDelete(record,visitContentInputDto.getUserId(),userIds);
            UserOrgEnums userOrgEnums = userService.getUserOrgEnum(visitInputDto.getUserId());
            boolean canCheckTraderDetail = visitRecordService.canCheckTraderDetail(userOrgEnums,record,visitContentInputDto.getUserId(),userIds);
            record.setCanCheckTraderDetail(canCheckTraderDetail);
            return R.success("编辑拜访记录成功",record);
        } catch (Exception e) {
            log.error("visitContentInputDto query failure", e);
            return R.error("编辑拜访记录失败");
        }

    }







}
