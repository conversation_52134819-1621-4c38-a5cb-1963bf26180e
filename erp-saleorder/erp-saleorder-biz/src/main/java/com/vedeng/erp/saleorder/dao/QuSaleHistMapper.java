package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.saleorder.model.dto.QuSaleHist;

import java.util.List;

public interface QuSaleHistMapper {
    int deleteByPrimaryKey(Integer quSaleHisId);

    int insert(QuSaleHist record);

    int insertSelective(QuSaleHist record);

    QuSaleHist selectByPrimaryKey(Integer quSaleHisId);

    int updateByPrimaryKeySelective(QuSaleHist record);

    int updateByPrimaryKey(QuSaleHist record);

    List<QuSaleHist> getHistInfoBySaleorderId(Integer saleorderId);
}
