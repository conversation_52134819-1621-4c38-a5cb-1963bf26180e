package com.vedeng.order.controller;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.ge.GeTraderSku;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.order.service.GeInfoCollectionService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;


@Controller
@RequestMapping("/order/georder")
public class GeInfoController extends BaseController {

    @Autowired
    @Qualifier("GeInfoCollectionService")
    private GeInfoCollectionService geInfoCollectionService;

    @Resource
    private BuyorderService buyorderService;

    @Value("${GE_TRADER_SKU}")
    protected String  GE_TRADER_SKU;
    /**
     * GE信息采集页面
     */
    @RequestMapping(value = "/geInfoCollection")
    public ModelAndView geInfoCollection(@RequestParam(required = false, value = "") String geContractNo,
                                         @RequestParam(required = false, value = "") String geSaleContractNo,
                                         @RequestParam(required = false, value = "") String sku,
                                         @RequestParam String geBuyorderGoodsIds) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("sku",sku);
        mv.addObject("geBuyorderGoodsIds", geBuyorderGoodsIds);
        mv.addObject("geContractNo", geContractNo);
        mv.addObject("geSaleContractNo", geSaleContractNo);
        mv.setViewName("order/buyorder/geInfoCollection");
        return mv;
    }

     //校验供应商和SKU是否匹配
    @ResponseBody
    @RequestMapping(value = "checkGeInfo")
    public ResultInfo checkGeInfo(HttpServletRequest request, @RequestParam Integer buyorderId, @RequestParam Integer traderId, @RequestParam String traderInfoJSON) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        if (GE_TRADER_SKU == null) {
            return new ResultInfo();
        }
        Buyorder buyorder = new Buyorder();
        buyorder.setBuyorderId(buyorderId);
        buyorder.setTraderId(traderId);
        //根据采购订单id查询出该采购订单中的所有sku
        BuyorderVo bv = buyorderService.getAddBuyorderVoDetail(buyorder, user);
        if(bv==null|| CollectionUtils.isEmpty(bv.getBuyorderGoodsVoList())){
            return new ResultInfo(-1, "该采购订单不存在");
        }
        //将apollo中的供应商和sku拉去下来进行比较
        GeTraderSku geTraderSku = JSON.parseObject(GE_TRADER_SKU,GeTraderSku.class);// JSONObject.toBean(obj, GeTraderSku.class);
        ResultInfo resultInfo=new ResultInfo(-1, "此采购订单中无GE商品，不需要维护GE采集信息");
        //比较信息，查询采购订单中是否包含GE商品
        if (geTraderSku!=null){
            if (!geTraderSku.getTraderId().equals(traderId)){
                return new ResultInfo(-1, "此采购订单中无GE商品，不需要维护GE采集信息");
            }
            bv.getBuyorderGoodsVoList().forEach(item->{
                boolean containsGeSku=CollectionUtils.isNotEmpty(geTraderSku.getSkuList())&&geTraderSku.getSkuList().contains(item.getSku());
                if(containsGeSku){
                    resultInfo.setCode(0);
                    resultInfo.setMessage("该采购订单包含GE信息");
                    return;
                }
            });
        }
        Map map=(Map)JSON.parse(traderInfoJSON);
        Buyorder buyorderAddTraderInfo=new Buyorder();
        buyorderAddTraderInfo.setBuyorderId((Integer)map.get("buyorderId"));
        buyorderAddTraderInfo.setTraderName((String) map.get("traderName"));
        buyorderAddTraderInfo.setTraderId(Integer.parseInt((String) map.get("traderId")));
        String traderAddressStr=(String)map.get("traderAddressStr");
        String traderContactStr=(String)map.get("traderContactStr");
        geInfoCollectionService.upadteGeTraderInfo(buyorderAddTraderInfo,traderContactStr,traderAddressStr);
        return resultInfo;
    }


    //录入和修改GE合同编号和GE销售合同编号
    @ResponseBody
    @RequestMapping("/upadteGeInfo")
    public ResultInfo upadteGeInfo(@RequestParam String  geBuyorderGoodsIds,
                                   @RequestParam String geContractNo,
                                   @RequestParam String geSaleContractNo) {
        return geInfoCollectionService.upadteGeConInfo(geBuyorderGoodsIds,geContractNo,geSaleContractNo);
    }

    //删除录入的GE信息
    @ResponseBody
    @RequestMapping("/delGeInfoCollection")
    public ResultInfo delGeInfoCollection(HttpServletRequest request, @RequestParam Integer buyorderGoodsId) {

        return geInfoCollectionService.delGeInfoCollection(buyorderGoodsId);
    }

    //增加主机和探头页面
    @ResponseBody
    @RequestMapping("/addSncode")
    public ModelAndView addSncode(@RequestParam String sku,
                                  @RequestParam Integer goodsId,
                                  @RequestParam Integer buyorderGoodsId) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("masterSlaveLists", buyorderService.getMasterSlave(buyorderGoodsId));
        mv.addObject("sku",sku);
        mv.addObject("buyorderGoodsId",buyorderGoodsId);
        mv.addObject("goodsId", goodsId);
        mv.setViewName("order/buyorder/geSqInfoCollection");
       return  mv;
    }

    //保存主机和探头数据
    @ResponseBody
    @RequestMapping("/saveSncode")
    public ResultInfo saveSncode(HttpServletRequest request, @RequestParam String sku, @RequestParam String masterSlavejson,
                                 @RequestParam Integer buyorderGoodsId,
                                 @RequestParam Integer goodsId) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        return  geInfoCollectionService.saveSncode(sku,masterSlavejson,buyorderGoodsId,goodsId,user);
    }

    //校验主机序列号是否已存在
    @ResponseBody
    @RequestMapping("/checkMasterSno")
    public ResultInfo checkMasterSno(@RequestParam String masterSno,@RequestParam Integer buyorderGoodsId){
        return geInfoCollectionService.checkMasterSno(masterSno,buyorderGoodsId);
    }

    //校验探头序列号是否已存在
    @ResponseBody
    @RequestMapping("/checkSlaveSno")
    public ResultInfo checkSlaveSno(@RequestParam String slaveSno,@RequestParam String masterSlavejson){
        return geInfoCollectionService.checkSlaveSno(slaveSno,masterSlavejson);
    }

}
