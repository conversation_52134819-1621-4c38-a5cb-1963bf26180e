package com.vedeng.erp.settlement.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.enums.BusinessSourceTypeEnum;
import com.vedeng.common.core.enums.SupplierAssetEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.erp.finance.cmd.SettlementBillCreateCmd;
import com.vedeng.erp.finance.cmd.SettlementBillSettleCmd;
import com.vedeng.erp.finance.dto.CapitalBillDetailDto;
import com.vedeng.erp.finance.dto.CapitalBillDto;
import com.vedeng.erp.settlement.domain.context.SettlementBillSettleContext;
import com.vedeng.erp.settlement.domain.dto.SettlementBillDto;
import com.vedeng.erp.settlement.domain.dto.SettlementBillItemDto;
import com.vedeng.erp.settlement.domain.entity.SettlementBillEntity;
import com.vedeng.erp.settlement.domain.entity.SettlementBillItemEntity;
import com.vedeng.erp.settlement.mapper.SettlementBillItemMapper;
import com.vedeng.erp.settlement.mapper.SettlementBillMapper;
import com.vedeng.erp.settlement.mapstruct.SettlementBillConvertor;
import com.vedeng.erp.settlement.mapstruct.SettlementBillItemConvertor;
import com.vedeng.erp.settlement.service.CapitalBillService;
import com.vedeng.erp.trader.dto.SupplierAssetChangeDto;
import com.vedeng.erp.trader.dto.TraderSupplierDto;
import com.vedeng.erp.trader.service.SupplierAssetApiService;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 结算抽象类
 * @date 2023/11/27 11:08
 */
@Slf4j
public abstract class AbstractSettlementHandle implements Settlement{

    /**
     * 2支出 5转出
     */
    protected static final List<Integer> PAY_OUT = CollUtil.newArrayList(2, 5);
    /**
     * 1收入 4转入
     */
    protected static final List<Integer> PAY_IN = CollUtil.newArrayList(1, 4);

    @Autowired
    private SettlementBillConvertor settlementBillConvertor;
    @Autowired
    private SettlementBillMapper settlementBillMapper;
    @Autowired
    private SettlementBillItemMapper settlementBillItemMapper;
    @Autowired
    private SettlementBillItemConvertor settlementBillItemConvertor;
    @Autowired
    private SupplierAssetApiService supplierAssetApiService;
    @Autowired
    CapitalBillService capitalBillService;
    @Autowired
    TraderSupplierApiService traderSupplierApiService;



    @Transactional(rollbackFor = Throwable.class)
    @Override
    public Integer create(SettlementBillCreateCmd settlementBillCreateCmd) {
        log.info("生成结算单开始{}",JSON.toJSONString(settlementBillCreateCmd));
        SettlementBillDto settlementBillDto = this.preCreate(settlementBillCreateCmd);
        log.info("生成结算单{}",JSON.toJSONString(settlementBillDto));
        this.doCreate(settlementBillDto);
        this.postCreate(settlementBillDto);
        log.info("生成结算单结束{}",JSON.toJSONString(settlementBillDto));
        return settlementBillDto.getSettleBillId();
    }


    @Transactional(rollbackFor = Throwable.class)
    @Override
    public void settle(SettlementBillSettleCmd settlementBillSettleCmd) {
        List<SettlementBillSettleContext> contexts = this.preSettlement(settlementBillSettleCmd);
        contexts.forEach(c -> {
            log.info("结算单结算开始，结算参数上下文：{}", JSON.toJSONString(c));
            this.doSettlement(c);
            this.postSettlement(c);
        });
        log.info("结算单结算结束");
    }

    /**
     * 生成结算单前置
     * 处理业务订单向结算单的转换
     *
     * @param settlementBillCreateCmd settlementBillCreateCmd
     */
    protected abstract SettlementBillDto preCreate(SettlementBillCreateCmd settlementBillCreateCmd);

    /**
     * 新增结算单
     * 存入结算单表和结算单明细表到数据库
     *
     * @param settlementBillDto settlementBillDto
     */
    private void doCreate(SettlementBillDto settlementBillDto) {
        // 新增明细
        List<SettlementBillItemDto> settlementBillItemDtoList = settlementBillDto.getSettlementBillItemDtoList();
        if (CollectionUtil.isEmpty(settlementBillItemDtoList)) {
            return;
        }
        SettlementBillEntity settlementBillEntity = settlementBillConvertor.toEntity(settlementBillDto);
        settlementBillEntity.setIsDelete(ErpConstant.F);
        settlementBillEntity.setSettlementStatus(ErpConstant.F);
        long count = settlementBillDto.getSettlementBillItemDtoList().stream().map(SettlementBillItemDto::getSettleBillId).distinct().count();
        settlementBillEntity.setTransactionCount((int) count);
        settlementBillEntity.setAlreadySettleAmount(BigDecimal.ZERO);
        //结算编号
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.SETTLEMENT_BILL);
        settlementBillEntity.setSettleBillNo(new BillNumGenerator().distribution(billGeneratorBean));
        settlementBillMapper.insertSelective(settlementBillEntity);
        settlementBillDto.setSettleBillId(settlementBillEntity.getSettleBillId());
        settlementBillDto.setSettleBillNo(settlementBillEntity.getSettleBillNo());
        settlementBillItemDtoList.forEach(s -> {
            s.setSettleBillNo(settlementBillDto.getSettleBillNo());
            s.setSettleBillId(settlementBillDto.getSettleBillId());
            s.setInvoiceStatus(ErpConstant.F);
            s.setSettlementStatus(ErpConstant.F);
            s.setIsDelete(ErpConstant.F);
            settlementBillItemMapper.insertSelective(settlementBillItemConvertor.toEntity(s));
        });
    }

    /**
     * 生成结算单后置
     *
     * @param settlementBillDto settlementBillDto
     */
    protected abstract void postCreate(SettlementBillDto settlementBillDto);

    /**
     * 结算单结算前置
     * 根据结算类型 获取实际需要结算的结算单
     *
     * @param settlementBillSettleCmd 结算参数上下文类
     * @return 结算单
     */
    private List<SettlementBillSettleContext> preSettlement(SettlementBillSettleCmd settlementBillSettleCmd) {
        SettlementBillEntity settlementBillEntity = settlementBillMapper.selectByPrimaryKey(settlementBillSettleCmd.getSettlementBillId());
        SettlementBillDto settlementBillDto = settlementBillConvertor.toDto(settlementBillEntity);
        List<SettlementBillItemEntity> settlementBillItemEntityList = settlementBillItemMapper.findBySettleBillId(settlementBillSettleCmd.getSettlementBillId());
        List<SettlementBillItemDto> settlementBillItemDtos = settlementBillItemConvertor.toDto(settlementBillItemEntityList);
        List<SettlementBillSettleContext> settlementBillSettleContextList = new ArrayList<>();
        settlementBillSettleCmd.getSettlementTypeEnum().forEach(settlementTypeEnum -> {
            List<SettlementBillItemDto> collect = settlementBillItemDtos.stream().filter(s -> settlementTypeEnum.getSupplierAssetEnum().getCode()
                    .equals(s.getSettlementType())).collect(Collectors.toList());
            settlementBillDto.setSettlementBillItemDtoList(collect);

            SettlementBillSettleContext settlementBillSettleContext = new SettlementBillSettleContext();
            settlementBillSettleContext.setSettlementBillDto(settlementBillDto);
            settlementBillSettleContext.setSettlementTypeEnum(settlementTypeEnum);

            BigDecimal settleAmount = this.buildPayAmount(settlementBillSettleContext);
            settlementBillSettleContext.setSettleAmount(settleAmount);
            settlementBillSettleContextList.add(settlementBillSettleContext);
        });
        return settlementBillSettleContextList;
    }


    /**
     * 结算单结算
     *
     * @param context context
     */
    private void doSettlement(SettlementBillSettleContext context) {
        // 1. 支付
        this.pay(context);

        // 2. 生成资金流水
        this.createCapitalBill(context);

        // 3.更新结算单状态
        this.updateSettlementBillStatus(context);
    }

    private void updateSettlementBillStatus(SettlementBillSettleContext context) {
        SettlementBillDto settlementBillDto = context.getSettlementBillDto();
        List<SettlementBillItemDto> settlementBillItemDtoList = settlementBillDto.getSettlementBillItemDtoList();
        BigDecimal settleAmount = this.buildPayAmount(context);
        if (settleAmount.equals(BigDecimal.ZERO)) {
            throw new ServiceException("结算金额不能为0");
        }
        // 结算总应付 和 结算实付进行对比
        if (settlementBillDto.getSettleAmount().compareTo(settleAmount) > 0) {
            settlementBillDto.setSettlementStatus(ErpConstant.ONE);
        }
        if (settlementBillDto.getSettleAmount().compareTo(settleAmount) == 0) {
            settlementBillDto.setSettlementStatus(ErpConstant.TWO);
        }

        // 结算明细总应付 结算实付进行对比
        BigDecimal itemAmount = settlementBillItemDtoList.stream().map(SettlementBillItemDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if(itemAmount.compareTo(settleAmount) > 0){
            settlementBillItemDtoList.forEach(s -> s.setSettlementStatus(ErpConstant.ONE));
        }
        if(itemAmount.compareTo(settleAmount) == 0){
            settlementBillItemDtoList.forEach(s -> s.setSettlementStatus(ErpConstant.TWO));
        }
        settlementBillMapper.updateByPrimaryKeySelective(settlementBillConvertor.toEntity(settlementBillDto));
        settlementBillItemMapper.updateBatchSelective(settlementBillItemConvertor.toEntity(settlementBillItemDtoList));
    }


    /**
     * 生成资金流水
     *
     * @param context context
     */
    private void createCapitalBill(SettlementBillSettleContext context) {
        SettlementBillDto settlementBillDto = context.getSettlementBillDto();
        log.info("结算单生成资金流水，结算单：{}", JSON.toJSONString(settlementBillDto));
        List<SettlementBillItemDto> settlementBillItemDtoList = settlementBillDto.getSettlementBillItemDtoList();
        log.info("结算单生成资金流水，结算单明细：{}", JSON.toJSONString(settlementBillItemDtoList));

        Integer traderDirection = CollUtil.getFirst(settlementBillItemDtoList).getTraderDirection();

        CapitalBillDto capitalBillDto = new CapitalBillDto();
        capitalBillDto.setAddTime(DateUtil.current());
        capitalBillDto.setCompanyId(ErpConstant.ONE);
        capitalBillDto.setTraderTime(DateUtil.current());
        capitalBillDto.setTraderSubject(ErpConstant.ONE);
        capitalBillDto.setAmount(context.getSettleAmount());
        capitalBillDto.setPayer(ErpConstant.BD);
        capitalBillDto.setCreator(settlementBillDto.getCreator());
        TraderSupplierDto traderSupplierDto = traderSupplierApiService.getTraderSupplierByTraderId(settlementBillDto.getTraderId());
        capitalBillDto.setPayee(traderSupplierDto.getTraderName());
        capitalBillDto.setTraderType(traderDirection);
        CapitalBillDetailDto capitalBillDetail = new CapitalBillDetailDto();
        capitalBillDto.setCapitalBillDetailDto(capitalBillDetail);
        capitalBillDetail.setTraderType(settlementBillDto.getTraderSubjectType());
        capitalBillDetail.setOrderNo(settlementBillDto.getBusinessSourceTypeNo());
        capitalBillDetail.setRelatedId(settlementBillDto.getBusinessSourceTypeId());
        capitalBillDetail.setTraderId(settlementBillDto.getTraderId());
        capitalBillDetail.setAmount(context.getSettleAmount());
        context.setCapitalBillDto(capitalBillDto);
        // 2. 组装真实资金流水具体参数
        log.info("结算单生成资金流水，上下文：{}", JSON.toJSONString(context));
        this.buildCapitalBill(context);
        capitalBillService.create(capitalBillDto);
    }


    /**
     * 使用资产付款
     *
     * @param context context
     */
    private void pay(SettlementBillSettleContext context) {
        SettlementBillDto settlementBillDto = context.getSettlementBillDto();
        List<SettlementBillItemDto> settlementBillItemDtoList = settlementBillDto.getSettlementBillItemDtoList();
        log.info("结算单生成资金流水，结算单明细：{}", JSON.toJSONString(settlementBillItemDtoList));
        SettlementBillItemDto settlementBillItemDto = CollUtil.getFirst(settlementBillItemDtoList);

        SupplierAssetChangeDto dto = SupplierAssetChangeDto
                .builder()
                .quantity(context.getSettleAmount())
                .businessSourceType(BusinessSourceTypeEnum.getEnum(settlementBillDto.getSourceType()))
                .supplierAsset(SupplierAssetEnum.getEnum(settlementBillItemDto.getSettlementType()))
                .traderId(settlementBillDto.getTraderId())
                .traderSupplierId(settlementBillDto.getTraderSubjectId())
                .businessNumber(settlementBillDto.getBusinessSourceTypeNo())
                .build();

        //2支出 5转出  减少资产
        if (PAY_OUT.contains(settlementBillItemDto.getTraderDirection())) {
            supplierAssetApiService.sub(dto);
        }
        //1收入 4转入 增加资产
        if (PAY_IN.contains(settlementBillItemDto.getTraderDirection())) {
            supplierAssetApiService.add(dto);
        }
    }

    /**
     * 组装实付金额
     *
     * @param context context
     * @return 付款金额
     */
    protected abstract BigDecimal buildPayAmount(SettlementBillSettleContext context);


    /**
     * 组装资金流水具体参数
     *
     * @param context context
     */
    protected abstract void buildCapitalBill(SettlementBillSettleContext context);

    /**
     * 结算单结算后置
     *
     * @param context context
     */
    protected abstract void postSettlement(SettlementBillSettleContext context);
}
