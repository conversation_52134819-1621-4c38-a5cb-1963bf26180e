package com.vedeng.erp.buyorderexpense.mapper;
import java.util.Collection;

import com.vedeng.erp.buyorder.dto.*;
import com.vedeng.erp.buyorderexpense.domain.entity.RBuyorderExpenseJSaleorderEntity;

import java.util.Date;
import java.util.List;

import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import org.apache.ibatis.annotations.Param;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/1/5 13:46
 **/
public interface RBuyorderExpenseJSaleorderMapper {
    /**
     * delete by primary key
     * @param tRBuyorderExpenseJSaleorderId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer tRBuyorderExpenseJSaleorderId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(RBuyorderExpenseJSaleorderEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(RBuyorderExpenseJSaleorderEntity record);

    /**
     * select by primary key
     * @param tRBuyorderExpenseJSaleorderId primary key
     * @return object by primary key
     */
    RBuyorderExpenseJSaleorderEntity selectByPrimaryKey(Integer tRBuyorderExpenseJSaleorderId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(RBuyorderExpenseJSaleorderEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(RBuyorderExpenseJSaleorderEntity record);

    int batchInsert(@Param("list") List<RBuyorderExpenseJSaleorderEntity> list);

    /**
     * 根据销售单商品id 查询关联关系
     * @param saleorderGoodsIdCollection
     * @return List<RBuyorderExpenseJSaleorderEntity>
     */
    List<RBuyorderExpenseJSaleorderEntity> selectBySaleorderGoodsIdIn(@Param("saleorderGoodsIdCollection")List<Integer> saleorderGoodsIdCollection);

    /**
     * 根据 销售单商品ID 和 费用单ID 查询 总的采购数量
     * @param saleorderGoodsId 销售单商品ID
     * @param buyorderExpenseId 费用单ID
     * @return 费用单ID 采购 销售单商品ID 的数量
     */
    RBuyorderExpenseJSaleorderEntity getTotalNumBySaleorderGoodsIdAndBuyorderExpenseId(@Param("saleorderGoodsId") Integer saleorderGoodsId,@Param("buyorderExpenseId") Integer buyorderExpenseId);

    /**
     * 根据 销售单商品ID 和 费用单ID 查询 售后数量
     * @param saleorderGoodsId 销售单商品ID
     * @param buyorderExpenseId 费用单ID
     * @return 费用单ID 对 销售单商品ID 的售后数量
     */
    RBuyorderExpenseJSaleorderEntity getReturnNumByBuyorderExpenseIdAndSaleorderGoodsId(@Param("saleorderGoodsId") Integer saleorderGoodsId,@Param("buyorderExpenseId") Integer buyorderExpenseId);
    /**
     * 查询关联信息
     * @param buyorderExpenseItemDtoList
     * @return
     */
    List<RBuyorderExpenseJSaleorderDto> getRelatedDetail(@Param("buyorderExpenseItemDtoList")List<BuyorderExpenseItemDto> buyorderExpenseItemDtoList);

    /**
     * 批量更新关联数量
     * @param buyOrderSaleOrderGoodsDetailDtoList
     * @return
     */
    int updateBuyNum(@Param("buyOrderSaleOrderGoodsDetailDtoList") List<BuyOrderSaleOrderGoodsDetailDto> buyOrderSaleOrderGoodsDetailDtoList);

    /**
     * 查询已采购数量
     * @param buyOrderSaleOrderGoodsDetailDtoList
     * @return
     */
    List<RBuyorderExpenseJSaleorderDto> historyBuyNum(@Param("buyOrderSaleOrderGoodsDetailDtoList")List<BuyOrderSaleOrderGoodsDetailDto> buyOrderSaleOrderGoodsDetailDtoList);

    /**
     * 查询销售关联采购的售后数量
     * @param buyOrderSaleOrderGoodsDetailDtoList
     * @return
     */
    List<RBuyorderExpenseJSaleorderDto> expenseBuyorderAfterSaleNum(@Param("buyOrderSaleOrderGoodsDetailDtoList")List<BuyOrderSaleOrderGoodsDetailDto> buyOrderSaleOrderGoodsDetailDtoList);
    /**
     * 根据售后订单号查询符合条件的采购费用订单
     * @param afterSalesId
     * @return
     */
    List<RBuyorderExpenseJSaleorderEntity> getBuyorderExpenseByAfterSalesId(@Param("afterSalesId") Integer afterSalesId);

    List<Integer> findExpenseSaleorderIds(@Param("ids") List<Integer> saleorderIds);
    /**
     * 根据采购费用订单id查询关联销售订单id
     * @param buyorderExpenseId
     * @return
     */
    List<Integer> getSaleorderByBuyorderExpenseId(@Param("buyorderExpenseId") Integer buyorderExpenseId);

    /**
     * 根据费用单id 查询所有商品的关联关系
     * @param buyorderExpenseId 费用单id
     * @return List<RBuyorderExpenseJSaleorderDto> 关联关系
     */
    List<RBuyorderExpenseJSaleorderDto> findByBuyorderExpenseId(Integer buyorderExpenseId);

    List<RBuyorderExpenseJSaleorderEntity> getReleaseReturnEarlyWarnByAfterSalesId(@Param("afterSalesId") Integer afterSalesId);

    List<ExpenseBuyForSaleDetail> needReplaceExpenseBuyDetails(@Param("saleorderGoodsIdCollection")List<Integer> saleorderGoodsIdCollection);

    /**
     * 根据费用单明细id查询 正向的关联关系
     * @param buyorderExpenseItemIds 明细id
     * @return List<RBuyorderExpenseJSaleorderEntity>
     */
    List<RBuyorderExpenseJSaleorderDto> selectByBuyorderExpenseItemId(@Param("list")List<Integer> buyorderExpenseItemIds);

    /**
     * 根据费用单删除和销售的关系
     * @param buyorderExpenseId
     * @return
     */
    int deleteByBuyorderExpenseId(@Param("buyorderExpenseId")Integer buyorderExpenseId);




}