package com.vedeng.erp.trader.web.controller;


import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.trader.domain.dto.TraderCustomerActionDto;
import com.vedeng.erp.trader.domain.dto.TraderCustomerInfoDto;
import com.vedeng.erp.trader.dto.TraderUserDto;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;


import java.util.*;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.trader.web.controller
 * @Date 2023/8/4 13:11
 */
@RequestMapping("/trader/customer/new")
@Controller
public class TraderCustomerNewController extends BaseController {


    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;

    /**
     * 客户360页面跳转
     *
     * @param traderId         traderId
     * @param traderCustomerId traderCustomerId
     * @return ModelAndView
     */
    @NoNeedAccessAuthorization
    @RequestMapping("/portrait")
    public ModelAndView portrait(Integer traderId, Integer traderCustomerId, Integer customerNature) {
        ModelAndView mv = new ModelAndView("vue/view/tradercustomer/customer_portrait");
        Map<String, Integer> traderCustomer = new HashMap<>();
        if(Objects.isNull(traderCustomerId) || Objects.isNull(customerNature)) {
            TraderCustomerActionDto customerActionDto=  Optional.ofNullable(traderCustomerBaseService.getTraderCustomerAction(traderId)).orElse(new TraderCustomerActionDto());
            traderCustomerId = customerActionDto.getTraderCustomerId();
            customerNature = customerActionDto.getCustomerNature();
        }
        traderCustomer.put("traderId", traderId);
        traderCustomer.put("traderCustomerId", traderCustomerId);
        TraderCustomerActionDto traderCustomerAction = traderCustomerBaseService.getTraderCustomerAction(traderId);
        traderCustomer.put("belongPlatform", traderCustomerAction.getBelongPlatform());
        traderCustomer.put("customerNature", customerNature);
        mv.addObject("traderCustomer", traderCustomer);
        mv.addObject("method", "portrait");
        mv.addObject("traderId", traderId);
        return mv;
    }
    /**
     *
     * 【ERP】客户行为详情页（替换管理信息）
     * @param
     * @return
     */
    @RequestMapping(value = "/customeDetail")
    @NoNeedAccessAuthorization
    public ModelAndView customeDetail(Integer traderId, Integer traderCustomerId) {
        ModelAndView mv = new ModelAndView("vue/view/tradercustomer/customer_management_information");
        Map<String, Integer> traderCustomer = new HashMap<>();
        TraderCustomerActionDto traderCustomerAction = traderCustomerBaseService.getTraderCustomerAction(traderId);
        traderCustomer.put("belongPlatform", traderCustomerAction.getBelongPlatform());
        traderCustomer.put("traderId", traderId);
        traderCustomer.put("traderCustomerId", traderCustomerId);
        mv.addObject("traderCustomer", traderCustomer);
        mv.addObject("method", "manageinfo");
        mv.addObject("traderId", traderId);
        return mv;
    }

    /**
     * 经销链路tab页跳转
     *
     * @param traderId         traderId
     * @param traderCustomerId traderCustomerId
     * @return ModelAndView
     */
    @NoNeedAccessAuthorization
    @RequestMapping("/distribution/link")
    public ModelAndView distributionLink(Integer traderId, Integer traderCustomerId) {
        ModelAndView mv = new ModelAndView("vue/view/tradercustomer/distribution_link");
        Map<String, Integer> traderCustomer = new HashMap<>();
        traderCustomer.put("traderId", traderId);
        traderCustomer.put("traderCustomerId", traderCustomerId);
        TraderCustomerActionDto traderCustomerAction = traderCustomerBaseService.getTraderCustomerAction(traderId);
        traderCustomer.put("belongPlatform", traderCustomerAction.getBelongPlatform());
        mv.addObject("traderCustomer", traderCustomer);
        mv.addObject("traderId", traderId);
        TraderCustomerInfoDto traderCustomerInfoDto = Optional.ofNullable(traderCustomerBaseService.getTraderCutomerInfoById(traderCustomerId)).orElse(new TraderCustomerInfoDto());
        mv.addObject("customerNature", traderCustomerInfoDto.getCustomerNature());
        mv.addObject("method", "distributionLink");
        return mv;
    }


    /**
     * 分享客户tab页
     *
     * @param traderId         traderId
     * @param traderCustomerId traderCustomerId
     * @return ModelAndView
     */
    @NoNeedAccessAuthorization
    @RequestMapping("/share")
    public ModelAndView shareCustomer(Integer traderId, Integer traderCustomerId) {
        ModelAndView mv = new ModelAndView("vue/view/tradercustomer/share_customer");
        Map<String, Integer> traderCustomer = new HashMap<>();
        traderCustomer.put("traderId", traderId);
        traderCustomer.put("traderCustomerId", traderCustomerId);
        TraderCustomerActionDto traderCustomerAction = traderCustomerBaseService.getTraderCustomerAction(traderId);
        traderCustomer.put("belongPlatform", traderCustomerAction.getBelongPlatform());
        mv.addObject("traderCustomer", traderCustomer);
        mv.addObject("method", "share");
        // 判断当前登录用户是不是客户的归属销售
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        Optional<TraderUserDto> traderUser = Optional.ofNullable(traderCustomerBaseService.getTraderUserByTraderId(traderId));
        if (traderUser.isPresent()) {
            if (currentUser.getId().equals(traderUser.get().getUserId())) {
                mv.addObject("isBelongSale", true);
            } else {
                mv.addObject("isBelongSale", false);
                mv.addObject("saleName", traderUser.get().getUserName());
            }
        }
        return mv;
    }

    /**
     * 终端360页面跳转
     *
     * @param searchName searchName
     * @return ModelAndView
     */
    @NoNeedAccessAuthorization
    @RequestMapping("/terminalPortrait")
    public ModelAndView terminalPortrait(String searchName) {
        ModelAndView mv = new ModelAndView("vue/view/tradercustomer/terminal_portrait");
        mv.addObject("searchName", searchName);
        mv.addObject("method", "portrait");
        return mv;
    }

    /**
     * 终端经销链路tab页跳转
     *
     * @param searchName searchName
     * @return ModelAndView
     */
    @NoNeedAccessAuthorization
    @RequestMapping("/terminal/distributionLink")
    public ModelAndView terminalDistributionLink(String searchName) {
        ModelAndView mv = new ModelAndView("vue/view/tradercustomer/terminal_distribution_link");
        mv.addObject("searchName", searchName);
        mv.addObject("method", "distributionLink");
        return mv;
    }

}