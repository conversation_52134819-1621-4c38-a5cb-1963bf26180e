package com.vedeng.crm.visitrecord.facade.impl;

import com.vedeng.crm.visitrecord.domain.dto.VisitBizCheckDto;
import com.vedeng.crm.visitrecord.domain.vo.VisitBizCheckVo;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;
import com.vedeng.crm.visitrecord.facade.CrmVisitRecordFacade;
import com.vedeng.crm.visitrecord.service.CrmVisitRecordService;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.service.BusinessChanceService;
import com.vedeng.erp.business.service.BusinessLeadsService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/3
 */
@Service
public class CrmVisitRecordFacadeImpl implements CrmVisitRecordFacade {
    @Autowired
    private BusinessLeadsService businessLeadsService;

    @Autowired
    private BusinessChanceService businessChanceService;

    @Autowired
    private UserApiService userApiService;

    @Override
    public VisitBizCheckVo checkBizNo(VisitBizCheckDto checkDto) {
        VisitBizCheckVo result = new VisitBizCheckVo();

        // 1. 为空校验
        if (StringUtils.isBlank(checkDto.getBizNo())) {
            result.setCheckResult(false);
            result.setErrorMsg("请填写线索/商机编号");
            return result;
        }

        // 2. 查询线索信息
        BusinessLeadsDto businessLeadsDto = businessLeadsService.findByLeadsNo(checkDto.getBizNo());
        // 3. 查询商机信息
        BusinessChanceDto businessChanceDto = businessChanceService.findBussinessByBusinessChanceNo(checkDto.getBizNo());

        // 4. 校验编号是否存在
        if (businessLeadsDto == null && businessChanceDto == null) {
            result.setCheckResult(false);
            result.setErrorMsg("该线索/商机编号不存在，请确认后重新填写");
            return result;
        }

        // 5. 处理线索相关校验
        if (businessLeadsDto != null) {
            result.setBizId(businessLeadsDto.getId());
            result.setRelateType(1);
            result.setCustomerName(businessLeadsDto.getTraderName());
            result.setSalesName(businessLeadsDto.getBelonger());//可能为空
            Integer closeStatus = 3;
            // 5.1 检查线索状态是否关闭
            if (closeStatus.equals(businessLeadsDto.getFollowStatus())) {//0未分配，1.未处理、2.跟进中、3.已关闭、4.已商机
                result.setCheckResult(false);
                result.setErrorMsg("该线索已关闭");
                return result;
            }
            Integer createBusinessStatus = 3;
            // 5.2 检查线索是否已转商机和客户名称是否一致
            StringBuilder warnMsg = new StringBuilder();
            if (createBusinessStatus.equals(businessLeadsDto.getFollowStatus())) {
                warnMsg.append("该线索已商机");
            }

            if (StringUtils.isNotEmpty(checkDto.getCustomerName())&& !checkDto.getCustomerName().equals(businessLeadsDto.getTraderName())) {
                if (warnMsg.length() > 0) {
                    warnMsg.append("，");
                }
                warnMsg.append("线索客户与当前拜访客户不一致");
            }

            if (warnMsg.length() > 0) {
                result.setWarnMsg(warnMsg.toString());
            }
            return result;
        }

        // 6. 处理商机相关校验
        if (businessChanceDto != null) {
            result.setBizId(businessChanceDto.getBussinessChanceId());
            result.setRelateType(2);
            result.setCustomerName(businessChanceDto.getTraderName());
            if(businessChanceDto.getUserId() != null && businessChanceDto.getUserId()>0){
                UserDto userDto = userApiService.getUserBaseInfo(businessChanceDto.getUserId());
                result.setSalesName(userDto==null?"":userDto.getUsername());
            }


            Integer businessCloseStatus = 6;//商机阶段(1初步洽谈，2商机验证，3初步方案，4最终方案，5赢单，6关闭)
            Integer businessOrderedStatus = 5;
            // 6.1 检查商机状态是否关闭
            if (businessCloseStatus.equals(businessChanceDto.getStage())) {
                result.setCheckResult(false);
                result.setErrorMsg("该商机已关闭");
                return result;
            }

            // 6.2 检查商机是否已赢单和客户名称是否一致
            StringBuilder warnMsg = new StringBuilder();
            if (businessOrderedStatus.equals(businessChanceDto.getStage())) {
                warnMsg.append("该商机已赢单");
            }

            if (StringUtils.isNotEmpty(checkDto.getCustomerName())&& !checkDto.getCustomerName().equals(businessChanceDto.getTraderName())) {
                if (warnMsg.length() > 0) {
                    warnMsg.append("，");
                }
                warnMsg.append("商机客户与当前拜访客户不一致");
            }

            if (warnMsg.length() > 0) {
                result.setWarnMsg(warnMsg.toString());
            }
        }
        return result;
    }
}
