package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/12/2 11:17
 * @version 1.0
 */
@Setter
@Getter
public class  BatchCapitalBillDto extends BatchBaseDto {
    private Integer capitalBillId;

    /**
     * 记账编号
     */
    private String capitalBillNo;

    /**
     * 交易时间
     */
    private Long traderTime;

    /**
     * 订单单号
     */
    private String orderNo;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 业务类型 字典库
     */
    private Integer bussinessType;

    /**
     * 最大时间
     */
    private Long beginTime;

    /**
     * 最小时间
     */
    private Long endTime;

    /**
     * erp供应商id
     */
    private Integer traderSupplierId;

    /**
     * erp客户id
     */
    private Integer traderCustomerId;

    /**
     * 售后单号(归属业务单号)
     */
    private String afterSaleNo;

    /**
     * 收款方银行账号
     */
    private String payeeBankAccount;
    /**
     * 收款方银行名称
     */
    private String payeeBankName;
    /**
     * 收款方
     */
    private String payee;

    /**
     * 交易类型
     */
    private Integer traderType;

    private Integer traderId;

    /**
     * 交易主体1对公2对私
     */
    private Integer traderSubject;

    /**
     *  交易方式字典库TRADER_MODE
     */
    private Integer traderMode;

    /**
     * 订单类型 1销售订单 2采购订单 3售后订单 4采购费用单 5采购费用售后单
     */
    private Integer orderType;
}
