package com.vedeng.finance.vo;

import com.vedeng.finance.model.InvoiceEntryStash;

import java.math.BigDecimal;

/**
 * @describe 航信进项票暂存商品视图对象
 * <AUTHOR>
 * @date created in 2020/5/27 16:19:20
 */
public class InvoiceEntryStashVo extends InvoiceEntryStash {

    /**
     * 商品对应采购订单ID
     */
    private String buyorderNo;

    /**
     * 供应商名称
     */
    private String traderName;

    /**
     * 产品名称
     */
    private String goodsName;

    /**
     * 型号
     */
    private String model;

    /**
     * 采购价
     */
    private BigDecimal price;

    /**
     * 已入库数量
     */
    private Integer arrivalNum;

    /**
     * 航信进项票的商品ID
     */
    private Integer hxInvoiceDetailId;

    public String getBuyorderNo() {
        return buyorderNo;
    }

    public void setBuyorderNo(String buyorderNo) {
        this.buyorderNo = buyorderNo;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getArrivalNum() {
        return arrivalNum;
    }

    public void setArrivalNum(Integer arrivalNum) {
        this.arrivalNum = arrivalNum;
    }

    @Override
    public Integer getHxInvoiceDetailId() {
        return hxInvoiceDetailId;
    }

    @Override
    public void setHxInvoiceDetailId(Integer hxInvoiceDetailId) {
        this.hxInvoiceDetailId = hxInvoiceDetailId;
    }
}
