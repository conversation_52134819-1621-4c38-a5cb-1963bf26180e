<!DOCTYPE html>

<html>

<head>
    <title>CRM-线索</title>
    <link rel="stylesheet" href="./css/common/common.css">
    <link rel="stylesheet" href="./ui/ui.css">
    <link rel="stylesheet" href="./css/pages/businessLeads.css">
</head>

<body>
    <!-- 线索id -->
    <input type="hidden" id="businessLeads-id" value="1344">


    <input type="hidden" id="isHiddenLayout" value="0">
    <div class="page-wrap" id="page-container">
        <page-header></page-header>
        <div class="page-container" style="padding-left: 0;">
            <!-- <page-side></page-side> -->
            <div class="page-main">

                <div class="businessLeads-detail-container">
                    <div class="leadsDetail-header-wrap">
                        <div class="header-main">
                            <div class="header-content">
                                <div class="header-left">
                                    <div class="title">线索详情</div>
                                    <div class="status">状态中</div>
                                </div>
                                <div class="header-right">
                                    <button class="btn confirm">转商机</button>
                                    <button class="btn">编辑</button>
                                    <button class="btn">分配</button>
                                    <button class="btn">添加跟进记录</button>
                                    <button class="btn warn">关闭</button>
                                </div>
                            </div>
                            <div class="header-aside-wrap">
                                <!-- 大屏幕 -->
                                <div class="header-md-aside">
                                    <div 
                                        class="h-a-item" 
                                        v-for="item in asideList" :key="item.name"
                                        :class="[item.icon, {'active': item.id == asideIndex}]"
                                        @click="asideIndex = item.id"
                                    ></div>
                                </div>
                                <div class="header-xs-aside">
                                    <div 
                                        :class="['h-a-item', item.icon]"
                                        v-for="item in asideList" :key="item.name"
                                        @click="alert('弹层：'+item.name)"
                                    ></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="leadsDetail-page-wrap">
                        <div class="main">
                            <div class="card">
                                <div class="card-title">基础信息</div>
                                <div class="info-wrap">
                                    <div class="info-item" >
                                        <div class="label">线索编号：</div>
                                        <div class="content leadsno" @click="copy">
                                            <i class="vd-ui_icon icon-popup"></i>{{detail.leadsNo}}
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">线索类型：</div>
                                        <div class="content">{{detail.clueTypeName}}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">企微提醒：</div>
                                        <div class="content">{{detail.sendVx == 'Y'? '提醒': detail.sendVx == 'N'? '不提醒': '' }}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">询价行为：</div>
                                        <div class="content">{{detail.inquiry}}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">渠道类型：</div>
                                        <div class="content">{{detail.sourceName}}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">渠道名称：</div>
                                        <div class="content">{{detail.communicationName}}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">创建人：</div>
                                        <div class="content">{{detail.creatorName}}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">创建时间：</div>
                                        <div class="content">{{detail.addTime}}</div>
                                    </div>
                                </div>
                            </div>
        
                            <div class="card">
                                <div class="card-title">产品信息</div>
                                <div class="info-wrap">
                                    <div class="info-item" >
                                        <div class="label">三级分类：</div>
                                        <div class="content">{{detail.content}}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">产品信息：</div>
                                        <div class="content">{{detail.goodsInfo}}</div>
                                    </div>
                                </div>
                            </div>
        
                            <div class="card">
                                <div class="card-title">客户信息</div>
                                <div class="info-wrap">
                                    <div class="info-item">
                                        <div class="label">客户名称：</div>
                                        <div class="content detail-tyc">
                                            <span v-if="detail.traderNameLink" @click="GLOBAL.link(detail.traderNameLink)" class="company blue">{{detail.traderName}}</span>
                                            <span v-else class="company">{{detail.traderName}}</span>
                                            <span v-if="detail.tycFlag == 'Y'" class="icon"></span>
                                        </span>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">联系人：</div>
                                        <div class="content">{{detail.contact || '-'}}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">手机：</div>
                                        <div class="content" :class="{'highlight': detail.phone}">
                                            <template v-if="detail.phone">
                                                <i class="vd-ui_icon icon-call2"></i>
                                                <span @click="GLOBAL.callNumber(detail.phone)">{{detail.phone}}</span>
                                            </template>
                                            <span v-else>-</span>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">固话：</div>
                                        <div class="content" :class="{'highlight': detail.telephone}">
                                            <template v-if="detail.telephone">
                                                <i class="vd-ui_icon icon-call2"></i>
                                                <span @click="GLOBAL.callNumber(detail.telephone)">{{detail.telephone}}</span>
                                            </template>
                                            <span v-else>-</span>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">其他联系方式：</div>
                                        <div class="content">{{ detail.otherContactInfo || '-' }}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">地区：</div>
                                        <div class="content">{{ area }}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">线索创建人：</div>
                                        <div class="content">{{ detail.belonger }}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="remark-wrap">
                                <div class="info-item">
                                    <div class="label">线索备注：</div>
                                    <div class="content">{{detail.remark}}</div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-title">客户信息</div>
                                <div class="">
                                    <ui-table 
                                        :width-border="true" 
                                        :auto-scroll="false" 
                                        :headers="leadsDetailHeaders" 
                                        :list="LeadMergeList"
                                    ></ui-table>
                                </div>
                            </div>
                        </div>
                        <div class="right-aside hidden-xs">
                            <div class="right-aside-inner">
                                <follow-up-record
                                    v-show="asideIndex == 1"
                                    :chance-follow-up-record-list="chanceFollowUpRecordList"
                                    :leads-follow-up-record-list="leadsFollowUpRecordList"
                                ></follow-up-record>
                                <operation-log
                                    v-show="asideIndex == 2"
                                    :operation-log="operationLog"
                                    biz-type-enum="01"
                                    :biz-id="businessLeadsId"
                                ></operation-log>
                                <partner
                                    v-show="asideIndex == 4"
                                    :list="humanList"
                                ></partner>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script src="./js/common/vue.js"></script>
    <script src="./js/common/axios.js"></script>
    <script src="./js/common/lodash.min.js"></script>
    <script src="./ui/ui.js"></script>
    <script src="./js/common/layout.js"></script>
    <script src="./js/common/components/business/records.js"></script>
    <script src="./js/common/components/business/operationLog.js"></script>
    <script src="./js/common/components/business/partner.js"></script>
    <script src="./js/pages/businessleadsDetail.js"></script>
</body>

</html>