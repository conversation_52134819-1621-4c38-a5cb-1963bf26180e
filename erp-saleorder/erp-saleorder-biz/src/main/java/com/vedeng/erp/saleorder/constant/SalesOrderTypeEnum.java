package com.vedeng.erp.saleorder.constant;

import lombok.Getter;

/**
 * @Author: daniel
 * @Date: 2021/10/8 13 35
 * @Description: 销售订单类型枚举类
 */
@Getter
public enum SalesOrderTypeEnum {
    BASE(-1),
    /**
     * vs订单类型
     */
    VS(0),
    BD(1),
    HC(5),
    JCF(8),
    JCO(7),
    ZXF(9);

    private Integer code;

    SalesOrderTypeEnum(Integer code){
        this.code = code;
    }

    public static SalesOrderTypeEnum getInstance(Integer code) {
        for (SalesOrderTypeEnum v : values()) {
            if (v.code.equals(code)) {
                return v;
            }
        }
        return BASE;
    }
}
