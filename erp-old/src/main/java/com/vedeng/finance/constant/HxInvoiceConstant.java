package com.vedeng.finance.constant;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/08/07
 **/
public class HxInvoiceConstant {

    public static final String HX_RESPONSE_SUCCESS = "0000";

    public static final String HX_RESPONSE_FAIL = "0001";

    /**
     * 下载航信发票图片接口
     */
    public static final String HX_DOWNLOAD_IMG_URL = "/taxinfo_web_sys/form/jsp/njbd/queryimg.do";

    /**
     * 航信进项发票批量查询接口
     */
    public static final String HX_INVOICE_QUERY_LIST_URL = "/taxinfo_web_sys/form/jsp/njbd/querylist.do";

    public static final String HX_INCOME_INVOICE_QUERY_LIST_URL = "/taxinfo_web_sys/form/jsp/njbd/queryincome.do";

    /**
     * 航信发票认证接口
     */
    public static final String HX_INVOICE_AUTH_URL = "/taxinfo_web_sys/form/jsp/njbd/deduction.do";

    /**
     * 航信发票保存ftp服务器路径
     */
    public static final String HX_INVOICE_UPLOAD_PATH = "/upload/hx_invoice";

    /**
     * 贝登发票信息：联系地址
     */
    public static final List<String> VEDENG_ADDRESS_IN_INVOICE = Arrays.asList("南京市秦淮区永丰大道8号南京白下高新技术产业园区三号楼106A室","南京市秦淮区永顺路2号2幢三楼301室");

    /**
     * 贝登发票信息：联系方式
     */
    public static final List<String> VEDENG_TEL_IN_INVOICE = Collections.singletonList("025-********");

    /**
     * 贝登发票信息：银行账号
     */
    public static final List<String> VEDENG_BANK_ACCOUNT_IN_INVOICE = Collections.singletonList("32001881236052503686");

    /**
     * 贝登发票信息：银行名称
     */
    public static final List<String> VEDENG_BANK_NAME_IN_INVOICE = Collections.singletonList("建设银行南京市中山南路支行");

    /**
     * 贝登纳税人识别号
     */
    public static String VEDENG_TAX_NUMBER = "91320100589439066H";

    /**
     * 专票类型
     */
    public static final List<Integer> SPECIAL_INVOICE_TYPE_ = Arrays.asList(429, 682, 972, 684, 686, 688);

}
