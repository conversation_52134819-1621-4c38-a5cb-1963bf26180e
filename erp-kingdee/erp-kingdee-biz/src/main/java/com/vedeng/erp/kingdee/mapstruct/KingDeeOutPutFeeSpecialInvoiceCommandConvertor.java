package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeOutPutFeeSpecialInvoiceCommand;
import com.vedeng.erp.kingdee.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 金蝶 销项专票 dto 转 command
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface KingDeeOutPutFeeSpecialInvoiceCommandConvertor extends BaseCommandMapStruct<KingDeeOutPutFeeSpecialInvoiceCommand, OutPutFeeSpecialInvoiceDto> {
    /**
     * KingDeeOutPutFeeSpecialInvoiceCommand
     *
     * @param dto OutPutFeePlainInvoiceDto
     * @return KingDeeOutPutFeeSpecialInvoiceCommand
     */
    @Mapping(target = "FID", source = "fid")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtid")
    @Mapping(target = "FINVOICENO", source = "finvoiceno")
    @Mapping(target = "f_QZOK_FPDM", source = "FQzokFpdm")
    @Mapping(target = "FINVOICEDATE", source = "finvoicedate")
    @Mapping(target = "FDATE", source = "fdate")
    @Mapping(target = "FCONTACTUNITTYPE", source = "fcontactunittype")
    @Mapping(target = "FCONTACTUNIT.FNumber", source = "fcontactunit")
    @Mapping(target = "FSALEORGID.FNumber", source = "fsaleorgid")
    @Mapping(target = "FSETTLEORGID.FNumber", source = "fsettleorgid")
    @Mapping(target = "FDOCUMENTSTATUS", source = "fdocumentstatus")
    @Mapping(target = "f_QZOK_PZGSYWDH", source = "FQzokPzgsywdh")
    @Override
    KingDeeOutPutFeeSpecialInvoiceCommand toCommand(OutPutFeeSpecialInvoiceDto dto);

    /**
     * OutPutFeeSpecialInvoiceDetailCommand
     *
     * @param dto OutPutFeeSpecialInvoiceDetailDto
     * @return OutPutFeeSpecialInvoiceDetailCommand
     */
    @Mapping(target = "FEXPENSEID.FNumber", source = "fexpenseid")
    @Mapping(target = "FAUXTAXPRICE", source = "fauxtaxprice")
    @Mapping(target = "FPRICEQTY", source = "fpriceqty")
    @Mapping(target = "FTAXRATE", source = "ftaxrate")
    @Mapping(target = "f_QZOK_BDDJHID", source = "FQzokBddjhid")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "FSRCBILLTYPEID", source = "fsrcbilltypeid")
    @Mapping(target = "f_QZOK_WLBM.FNumber", source = "f_QZOK_WLBM")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    KingDeeOutPutFeeSpecialInvoiceCommand.OutPutFeeSpecialInvoiceDetailCommand toCommand(OutPutFeeSpecialInvoiceDetailDto dto);

    /**
     * OutPutFeeSpecialInvoiceDetailLinkCommand
     *
     * @param dto OutPutFeeSpecicalInvoiceDetailLinkDto
     * @return OutPutFeeSpecialInvoiceDetailLinkCommand
     */
    @Mapping(target = "FSALEEXINVENTRY_Link_FFlowId", source = "fsaleexinventryLinkFflowid")
    @Mapping(target = "FSALEEXINVENTRY_Link_FFlowLineId", source = "fsaleexinventryLinkFflowlineid")
    @Mapping(target = "FSALEEXINVENTRY_Link_FRuleId", source = "fsaleexinventryLinkFruleid")
    @Mapping(target = "FSALEEXINVENTRY_Link_FSTableId", source = "fsaleexinventryLinkFstableid")
    @Mapping(target = "FSALEEXINVENTRY_Link_FSTableName", source = "fsaleexinventryLinkFstablename")
    @Mapping(target = "FSALEEXINVENTRY_Link_FSBillId", source = "fsaleexinventryLinkFsbillid")
    @Mapping(target = "FSALEEXINVENTRY_Link_FSId", source = "fsaleexinventryLinkFsid")
    @Mapping(target = "FSALEEXINVENTRY_Link_FALLAMOUNTFOROLD", source = "fsaleexinventryLinkFallamountforold")
    @Mapping(target = "FSALEEXINVENTRY_Link_FALLAMOUNTFOR", source = "fsaleexinventryLinkFallamountfor")
    KingDeeOutPutFeeSpecialInvoiceCommand.OutPutFeeSpecialInvoiceDetailLinkCommand toCommand(OutPutFeeSpecialInvoiceDetailLinkDto dto);
}
