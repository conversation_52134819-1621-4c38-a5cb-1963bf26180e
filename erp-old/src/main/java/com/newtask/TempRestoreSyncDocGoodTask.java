package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.docSync.dao.DocSyncMapper;
import com.vedeng.docSync.enums.DocSyncEventEnum;
import com.vedeng.docSync.model.TempSyncSku;
import com.vedeng.docSync.service.SyncGoodsService;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.goods.model.dto.CoreSpuBaseDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@JobHandler(value = "tempRestoreSyncDocGoodTask")
@Component
public class TempRestoreSyncDocGoodTask extends AbstractJobHandler {

    @Resource
    private DocSyncMapper docSyncMapper;

    @Autowired
    private SyncGoodsService syncGoodsService;
    @Autowired
    com.vedeng.firstengage.dao.FirstEngageMapper firstEngageMapper;

    @Autowired
    com.vedeng.goods.service.BaseGoodsService baseGoodsService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {

        if(StringUtils.startsWith(s, "sku")){
            s=s.replace("sku","");
            String skus[]=s.split(",");
            for (int i = 0; i < skus.length; i++) {
                FirstEngage fist=firstEngageMapper.getFirstEngageInfoBySkuNo("V"+skus[i]);
                if(fist==null){
                    XxlJobLogger.log("无需同步，该sku在erp中没有维护对应注册证 "+skus[i]);
                    continue;
                }
                syncGoodsService.syncGoods2Doc(DocSyncEventEnum.SKU_SYNC.getType(), Integer.parseInt(skus[i]), fist.getRegistrationNumberId());
            }
            return SUCCESS;
        }else if(StringUtils.startsWith(s, "spu")){
            s=s.replace("spu","");
            String spus[]=s.split(",");

            for (int i = 0; i < spus.length; i++) {
                CoreSpuBaseDTO spuBaseDTO= baseGoodsService.selectSpuBaseById(Integer.parseInt(spus[i]));
                if(spuBaseDTO==null||spuBaseDTO.getRegistrationNumberId()==null){
                    XxlJobLogger.log("无需同步，该spu在erp中没有维护对应注册证 "+spus[i]);
                    continue;
                }
                syncGoodsService.syncGoods2Doc(DocSyncEventEnum.SPU_SYNC.getType(), Integer.parseInt(spus[i]), spuBaseDTO.getRegistrationNumberId());
            }
            return SUCCESS;
        }
        List<TempSyncSku> tempSyncSkus = docSyncMapper.selectNotSyncSku();
        if (tempSyncSkus.size() == 0) {
            XxlJobLogger.log("无需恢复同步, 无未同步数据");
            return SUCCESS;
        }
        for (TempSyncSku syncSkus : tempSyncSkus) {
            if (syncSkus.getRegistrationNumberId() == null || syncSkus.getSkuId() == null) {
                XxlJobLogger.log("无需同步，该sku在erp中没有维护对应注册证 "+syncSkus.getSkuId());
                continue;
            }
            syncGoodsService.syncGoods2Doc(DocSyncEventEnum.SKU_SYNC.getType(), syncSkus.getSkuId(), syncSkus.getRegistrationNumberId());
            XxlJobLogger.log("开始恢复同步sku到doc , model :{}", syncSkus);
        }

        return SUCCESS;
    }
}
