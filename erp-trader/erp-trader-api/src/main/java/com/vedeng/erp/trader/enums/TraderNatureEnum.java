package com.vedeng.erp.trader.enums;

public enum TraderNatureEnum {

    FX(465, "分销"),
    // 供应商
    ZD(466, "终端");


    private final Integer code;

    private final String type;

    TraderNatureEnum(Integer code, String type) {
        this.code = code;
        this.type = type;
    }

    public Integer getCode() {
        return code;
    }

    public String getType() {
        return type;
    }
}
