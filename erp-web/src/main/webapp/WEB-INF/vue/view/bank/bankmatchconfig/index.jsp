<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../../common/common.jsp" %>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/tailwind/tailwind.min.css">

<div id="app" class="p-8">
    <el-row :gutter="20">
        <el-col :span="8">
            <div style="display: flex; ">
                <div class="mt-2" style="width: 110px">银行名称</div>
                <el-input v-model="searchText" placeholder="银行名称" maxlength="100"></el-input>
            </div>
        </el-col>
        <el-col :span="3">
            <el-button type="primary" :loading="loadingSearch" @click="search">搜索</el-button>
        </el-col>
        <el-col :span="3" :offset="10">
            <el-button type="success" @click="createNew">新建</el-button>
        </el-col>
    </el-row>

    <template>
        <el-table :data="banks" style="width: 100%; max-width: 800px;">
            <el-table-column prop="groupId" label="ID" width="120" align="center"></el-table-column>
            <el-table-column prop="groupName" label="银行名称" width="450" align="center"></el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button size="mini" type="success" @click="editBank(scope.row)">编辑</el-button>
                    <el-button size="mini" type="danger" :loading="loadingDelete" @click="deleteBank(scope.row)">删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
    </template>

    <el-pagination
            layout="total, prev, pager, next, jumper"
            :total="total"
            :page-size="pageSize"
            :current-page.sync="pageNum"
            @current-change="handlePageChange">
    </el-pagination>


    <el-dialog :title="editBankId ? '编辑开户行配置' : '新建开户行配置'" :visible.sync="dialogVisible" width="60%" top="5%"
               :close-on-click-modal="false"  @close="clearForm">
        <el-form :model="newBank" :rules="rules" ref="newBankForm" label-width="100px" label-position="top">
            <el-form-item label="银行名称" prop="groupName">
                <el-input v-model="newBank.groupName" maxlength="100"></el-input>
            </el-form-item>
            <el-form-item label="银行别名(每行一个)" prop="bankConfig">
                <el-input type="textarea" :rows="12" v-model="newBank.bankConfig" maxlength="500"></el-input>
            </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" :disabled="loadingSave">取 消</el-button>
        <el-button type="primary" :loading="loadingSave" @click="saveNewBank">确 定</el-button>
    </span>
    </el-dialog>

</div>


<script src="${pageContext.request.contextPath}/static/api/trader/bankMatchConfig.js?rnd=${resourceVersionKey}"></script>

<script>
    new Vue({
        el: '#app',
        data() {
            return {
                searchText: '',
                pageNum: 1,
                pageSize: 20,
                total: 0,
                banks: [],
                dialogVisible: false,
                editBankId: null,
                newBank: {
                    groupName: '',
                    bankConfig: ''
                },
                rules: {
                    groupName: [
                        {required: true, message: '请输入银行名称', trigger: 'blur'},
                        {validator: this.validateGroupName, trigger: 'blur'}
                    ],
                    bankConfig: [
                        {required: true, message: '请输入银行别名', trigger: 'blur'},
                        {validator: this.validateBankConfig, trigger: 'blur'}
                    ]
                },
                loadingSearch: false,
                loadingSave: false,
                loadingDelete: false
            }
        },
        created() {
            this.fetchBanks();
        },
        methods: {
            fetchBanks() {
                this.loadingSearch = true;
                page({pageNum: this.pageNum, pageSize: this.pageSize, param: {groupName: this.searchText}})
                    .then(response => {
                        if (response.data.code !== 0) {
                            this.$message.error(response.data.message);
                            return;
                        }
                        this.banks = response.data.data.list;
                        this.total = response.data.data.total;
                    })
                    .catch(error => {
                        console.error('Error fetching banks:', error);
                        this.$message.error('请求失败，请稍后重试');
                    })
                    .finally(() => {
                        this.loadingSearch = false;
                    });
            },
            validateGroupName(rule, value, callback) {
                if (!value || value.trim() === '') {
                    callback(new Error('请输入银行名称'));
                } else {
                    callback();
                }
            },
            validateBankConfig(rule, value, callback) {
                if (!value || value.trim() === '') {
                    callback(new Error('请输入银行别名'));
                } else {
                    callback();
                }
            },
            search() {
                this.fetchBanks();
            },
            createNew() {
                this.editBankId = null;
                this.newBank = {groupName: '', bankConfig: ''};
                this.dialogVisible = true;
            },
            saveNewBank() {
                this.$refs.newBankForm.validate((valid) => {
                    if (valid) {
                        this.loadingSave = true;
                        const saveMethod = this.editBankId ? update : save;
                        saveMethod(this.newBank)
                            .then(response => {
                                if (response.data.code !== 0) {
                                    this.$message({
                                        message: response.data.message,
                                        type: 'error',
                                        duration: 5000, // 5秒显示时间
                                        showClose: true // 显示关闭按钮
                                    });
                                    return;
                                }
                                this.dialogVisible = false;
                                this.newBank = {groupName: '', bankConfig: ''};
                                this.editBankId = null;
                                this.fetchBanks();
                            })
                            .catch(error => {
                                console.error('Error saving bank:', error);
                                this.$message({
                                    message: '请求失败，请稍后重试',
                                    type: 'error',
                                    duration: 5000, // 5秒显示时间
                                    showClose: true // 显示关闭按钮
                                });
                            })
                            .finally(() => {
                                this.loadingSave = false;
                            });
                    } else {
                        console.log('Validation failed');
                        return false;
                    }
                });
            },
            editBank(bank) {
                this.editBankId = bank.groupId;
                detail({groupId: bank.groupId})
                    .then(response => {
                        if (response.data.code !== 0) {
                            this.$message({
                                message: response.data.message,
                                type: 'error',
                                duration: 5000, // 5秒显示时间
                                showClose: true // 显示关闭按钮
                            });
                            return;
                        }
                        this.newBank = {...response.data.data};
                        this.dialogVisible = true;
                    })
                    .catch(error => {
                        console.error('Error fetching bank details:', error);
                        this.$message({
                            message: '请求失败，请稍后重试',
                            type: 'error',
                            duration: 5000, // 5秒显示时间
                            showClose: true // 显示关闭按钮
                        });
                    });
            },

            deleteBank(bank) {
                this.$confirm('确定删除吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.loadingDelete = true;
                    deleteById({groupId: bank.groupId})
                        .then(response => {
                            if (response.data.code !== 0) {
                                this.$message.error(response.data.message);
                                return;
                            }
                            console.log('Bank deleted:', bank);
                            this.fetchBanks();
                            this.$message({
                                type: 'success',
                                message: '删除成功!'
                            });
                        })
                        .catch(error => {
                            console.error('Error deleting bank:', error);
                            this.$message.error('请求失败，请稍后重试');
                        })
                        .finally(() => {
                            this.loadingDelete = false;
                        });
                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消删除'
                    });
                });
            },
            handlePageChange(newPage) {
                this.pageNum = newPage;
                this.fetchBanks();
            },
            clearForm() {
                this.$refs.newBankForm.clearValidate();
            }
        }
    });
</script>

<style>
    .el-dialog__body {
        padding: 10px 20px 0px;
        color: #606266;
        font-size: 14px;
        word-break: break-all;
    }

    .el-form--label-top .el-form-item__label {
        float: none;
        display: inline-block;
        text-align: left;
        padding: 0 0 0;
    }

</style>