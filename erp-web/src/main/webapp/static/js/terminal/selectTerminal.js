// javascript for selectTermial.jsp

$(function () {
    var saleOrderId = $('[name=saleorderId]').val();

    window.layerIndex = null;
    var $addTerminalDlg;

    //打开终端新增、编辑弹层
    $(document).on('click', '.J-terminal-edit', function () {
        var loadIndex = layer.load(2);

        var id = $(this).data('id') || '';
        var title = '新增终端';

        if (id) {
            title = '编辑终端';
        }

        $.ajax({
            url: '/order/terminal/querySaleOrderTerminal.do?id=' + id,
            dataType: 'json',
            success: function (res) {
                layer.close(loadIndex);
                if (res.code === 0) {
                    var types = res.data.naturnList || [];
                    var terminalInfo = res.data.terminal;

                    layerIndex = layer.open({
                        type: 1,
                        title: title,
                        area: ['720px', 'auto'],
                        content: $('.J-terminal-add-tmpl').html(),
                        success: function (layero) {
                            $.each(types, function (i, item) {
                                $('.J-terminal-type-list').append(`<label class="radio-label-item">
                                    <input type="radio" value="${item.sysOptionDefinitionId}" name="terminalTraderNature" data-label="${item.title}"/>${item.title}
                                </label>`)
                            })

                            $('.J-terminal-add-wrap').find('.J-terminalName').each(function () {
                                new SearchRelated({
                                    el: $(this),
                                    remoteInfo: {
                                        url: '/order/terminal/searchOneData.do',
                                        method: 'get',
                                        paramsKey: 'terminalTraderName',
                                        hasPage: true,
                                        parseData: (res) => {
                                            return res.data.list || [];
                                        },
                                        parseLabel: 'terminalName',
                                        parseLabelRight: '',
                                    },
                                    maxlength: '100',
                                    inputName: 'terminalName',
                                    input: $(this).siblings('.J-terminalName-value'),
                                    onchange: function (val, data) {
                                        console.log('change', val, data);
                                        $.each(['dwhTerminalId', 'unifiedSocialCreditIdentifier', 'organizationCode'], function (i, item) {
                                            $('.J-terminal-add-wrap').find('[name=' + item + ']').val(data[item] || '')
                                        })

                                        if(data.cityId) {
                                            $('.J-terminal-add-wrap').find('.J-address-value2').val(data.cityId)[0].triggerChange();
                                        }
                                    },
                                    openTyc: function () {
                                        openTycList($('.J-search-input').val().trim());
                                    }
                                })
                            })

                            $addTerminalDlg = $(layero);

                            if (terminalInfo) {
                                $addTerminalDlg.find('[name=terminalTraderNature][value=' + terminalInfo.terminalTraderNature + ']').attr('checked', true);
                                $addTerminalDlg.find('[name=beforeTerminalName]').val(terminalInfo.terminalName);
                                $addTerminalDlg.find('[name=beforeOrderTerminalId]').val(id);
                                $.each(['terminalName', 'contractUser', 'contractMobile', 'detailAddress', 'provinceName', 'provinceCode', 'cityCode', 'cityName', 'dwhTerminalId', 'unifiedSocialCreditIdentifier', 'organizationCode'], function (i, item) {
                                    $addTerminalDlg.find('[name=' + item + ']').val(terminalInfo[item] || '');
                                })
                            }

                            $addTerminalDlg.find('.layui-layer-content').css('height', 'auto');

                            $('.J-terminal-add-wrap').find('[name=natureOption]').change(function () {
                                validTerminalType();
                            })

                            $('.J-terminal-add-wrap').find('.J-input-number').on('input', function () {
                                $(this).val($(this).val().replace(/[^\d]/g, ''));
                            })


                            $addTerminalDlg.find('.layui-layer-content').css('overflow', 'visible');
                            $('.J-terminal-add-wrap').find('.J-address').each(function () {
                                var $this = $(this);
                                var $value1 = $this.siblings('.J-address-value1');
                                var $value2 = $this.siblings('.J-address-value2');

                                new Caecader({
                                    el: $(this),
                                    url: '/region/getCascaderRegionOptions.do',
                                    filterable: true,
                                    input: $(this).siblings('.J-address-value2'),
                                    level: 2,

                                    onchange: function (labels, values) {
                                        console.log(labels);
                                        $value1.val(values[0]);
                                        $value2.val(values[1]);

                                        $('.J-terminal-add-wrap').find('[name=provinceName]').val(labels[0]);
                                        $('.J-terminal-add-wrap').find('[name=cityName]').val(labels[1]);
                                    }
                                })
                            })

                        }
                    })


                } else {
                    layer.alert(res.message || '请求失败，请重新尝试')
                }
            }
        })
    })

    var clearTerminalInfo = function () {
        $.each(['dwhTerminalId', 'unifiedSocialCreditIdentifier', 'organizationCode'], function(i, item) {
            $('.J-terminal-add-wrap').find('[name=' + item + ']').val('')
        })
    }

    var validTerminalType = function () {
        var $typeChecked = $('.J-terminal-add-wrap').find('[name=terminalTraderNature]:checked');

        if (!$typeChecked.length) {
            $('.J-terminal-type-error').show();
            return false;
        } else {
            $('.J-terminal-type-error').hide();
            return true;
        }
    };

    var cansubmit = true;

    $(document).on('click', '.J-terminal-dlg-confirm', function () {
        if (validTerminalType() && cansubmit) {
            cansubmit = false;
            var reqData = {
                saleOrderId: saleOrderId,
                terminalTraderNature: $addTerminalDlg.find('[name=terminalTraderNature]:checked').val() || '',
                natureTypeName: $addTerminalDlg.find('[name=terminalTraderNature]:checked').data('label') || '',
            };

            $.each(['terminalName', 'dwhTerminalId', 'unifiedSocialCreditIdentifier', 'organizationCode', 'provinceCode', 'provinceName', 'cityCode', 'cityName', 'detailAddress', 'contractUser', 'contractMobile', 'beforeTerminalName', 'beforeOrderTerminalId'], function (i, item) {
                reqData[item] = $addTerminalDlg.find('[name=' + item + ']').val() || '';
            })

            $.ajax({
                url: '/order/terminal/addNew.do',
                type: 'post',
                contentType: 'application/json',
                data: JSON.stringify(reqData),
                dataType: 'json',
                success: function (res) {
                    if (res.code === 0) {
                        layer.close(layerIndex);
                        layer.msg($addTerminalDlg.find('[name=beforeOrderTerminalId]').val() ? '编辑成功' : '新增成功', { icon: 1 });
                        $('#terminalTraderName').val(reqData.terminalName);
                        refreshTerminalList();

                        setTimeout(function() {
                            cansubmit = true;
                        }, 500);
                    } else {
                        cansubmit = true;
                        layer.alert(res.message);
                    }
                },
                error: function() {
                    cansubmit = true;
                }
            })
        }
    })

    $(document).on('click', '.J-terminal-dlg-cancel', function () {
        layer.close(layerIndex);
    })

    //获取天眼查列表
    var getTycList = function () {
        var searchValue = $('.J-tyc-search-input').val().trim();
        if (searchValue.length < 4) {
            layer.msg('请输入不低于4个字进行查询', { icon: 7 });

            return;
        }

        var loadIndex = layer.load(2);

        $.ajax({
            url: '/order/terminal/searchTyc.do',
            data: {
                terminalTraderName: searchValue,
                pageSize: 20,
                pageNum: 1
            },
            dataType: 'json',
            success: function (res) {
                layer.close(loadIndex);

                if (res.code === 0) {
                    var list = res.data.list || [];

                    if (!list.length) {
                        $('.J-tyc-empty').show();
                        $('.J-tyc-table').hide();
                        layer.style(layerTycIndex, {
                            top: ($(window).height() - $tycDialog.height()) / 2 + 'px'
                        })
                        return;
                    } else {
                        $('.J-tyc-empty').hide();
                        $('.J-tyc-table').show();
                    }

                    if (list.length < 10) {
                        $('.J-tyc-list').removeClass('on-scroll');
                    } else {
                        $('.J-tyc-list').addClass('on-scroll');
                    }

                    $('.J-tyc-list').empty();

                    $.each(list, function (i, item) {

                        let companyType = ['普通公司', '香港公司', '社会组织', '律所', '事业单位', '基金'][item.companyType - 1] || '';

                        $('.J-tyc-list').append(`
                            <div class="list-table-tr">
                                <div class="td-item">
                                    <div class="text-line-1" title="${item.terminalName}">${item.terminalName}</div>
                                </div>
                                <div class="td-item">
                                    <div class="text-line-1" title="${companyType}">${companyType}</div>
                                </div>
                                <div class="td-item">
                                    <div class="text-line-1" title="${item.address}">${item.address || ''}</div>
                                </div>
                                <div class="td-item">
                                    <div class="td-option J-tyc-list-choose" data-name="${item.terminalName}">选择</div>
                                </div>
                            </div>
                        `);
                    })

                    $('.J-tyc-list-choose').click(function () {
                        var terminalName = $(this).data('name') || '';

                        $('.J-terminal-add-wrap').find('[name=terminalName]').val(terminalName);
                        clearTerminalInfo();
                        layer.close(layerTycIndex);
                    })

                    layer.style(layerTycIndex, {
                        top: ($(window).height() - $tycDialog.height()) / 2 + 'px'
                    })
                } else {
                    layer.alert(res.message || '请求失败，请重新尝试')
                }
            }

        })


    }

    var $tycDialog;
    var layerTycIndex;

    //打开天眼查弹层
    var openTycList = function (keyword) {
        if (keyword.trim().length < 4) {
            layer.msg('请输入不低于4个字进行查询', { icon: 7 });

            return;
        }

        layerTycIndex = layer.open({
            type: 1,
            title: '天眼查查询',
            area: ['960px', 'auto'],
            offset: 'auto',
            content: $('.J-terminal-tyc-tmpl').html(),
            success: function (layero) {
                $tycDialog = $(layero);
            }
        })

        $tycDialog.find('.layui-layer-content').css('height', 'auto');

        if (keyword) {
            $tycDialog.find('.J-tyc-search-input').val(keyword);
        }

        getTycList();
    }

    $(document).on('keydown', '.J-tyc-search-input', function (e) {
        if (e.keyCode === 13) {
            getTycList();
        }
    })

    $(document).on('click', '.J-tyc-search-btn', function () {
        getTycList();
    })

    // openTycList('测试公司');

    //刷新终端信息
    var refreshTerminalList = function () {
        $.ajax({
            url: '/order/terminal/listForSaleOrder.do?saleOrderId=' + saleOrderId,
            dataType: 'json',
            success: function (res) {
                if (res.code === 0) {
                    var terminalList = res.data || [];
                    $('.J-terminal-list-body').empty();
                    if (terminalList.length) {
                        $.each(terminalList, function (i, item) {
                            var deleteDom = '';

                            if (terminalList.length > 1) {
                                deleteDom = `<div class="option-item J-terminal-del" data-id="${item.id}">删除</div>`;
                            }

                            $('.J-terminal-list-body').append(`
                                <tr>
                                    <td>${item.terminalName}</td>
                                    <td>${item.natureTypeName}</td>
                                    <td>${item.contractUser}</td>
                                    <td>${item.contractMobile}</td>
                                    <td>${item.provinceName || ''}${item.cityName || ''}${item.detailAddress || ''}</td>
                                    <td>
                                        <div class="option-wrap">
                                            <div class="option-item J-terminal-edit" data-id="${item.id}">编辑</div>
                                            ${deleteDom}
                                        </div>
                                    </td>
                                </tr>
                            `)
                        })
                    } else {
                        $('.J-terminal-list-body').append(`
                            <tr>
                                <td class="td-empty" colspan="6">
                                    <div class="list-empty">
                                        <div class="empty-txt J-terminal-empty">
                                            <span class="vd-icon icon-info1"></span>请至少添加一个终端信息，
                                        </div>
                                        <div class="empty-option J-terminal-edit">添加终端</div>
                                    </div>
                                </td> 
                            </tr>
                        `)
                    }
                }
            }
        })
    }

    refreshTerminalList();

    $(document).on('click', '.J-terminal-del', function () {
        var id = $(this).data('id');
        layer.confirm('删除后数据将无法恢复，您确定需要删除吗？', {
            icon: 0,
            title: '',
            btn: ['删除', '取消'],
            // 按钮1 的回调
            btn1: function (index, layero, that) {
                $.ajax({
                    url: '/order/terminal/deleteForSaleOrder.do?id=' + id,
                    data: {},
                    dataType: 'json',
                    success: function (res) {
                        if (res.code === 0) {
                            layer.msg('删除成功', { icon: 1 });
                            refreshTerminalList();
                        } else {
                            layer.alert(res.message);
                        }
                    }
                })
            },
        });
    })
})