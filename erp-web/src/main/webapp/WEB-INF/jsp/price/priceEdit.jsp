<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="编辑" scope="application"/>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../common/common.jsp" %>
<script type="text/javascript">

    //修改价格
    function modifyPrice(trid,originPrice,originChangeReason){
        var tr = $("#" + trid);
        tr.find("td:eq(1)").html("<span class=\"font-red\">*</span>&nbsp;<input type=\"text\" style=\"width:200px;\" name=\"marketPrice\"/>&nbsp;元/${skuPriceChangeApplyDto.unitName}");
        tr.find("td:eq(2)").html("<span class=\"font-red\">*</span>&nbsp;<input type=\"text\" style=\"width:200px;\" name=\"marketPriceChangeReason\"/>");
        tr.find("td:eq(4)").html("<a href=\"#\" onclick=\"resetPrice('"+trid+"','"+originPrice+ "','"+originChangeReason+"')\">撤銷</a>");
    }

    function resetPrice(trid,originPrice,originChangeReason){
        var tr = $("#" + trid);
        tr.find("td:eq(1)").html(originPrice+"&nbsp;元/${skuPriceChangeApplyDto.unitName}");
        tr.find("td:eq(2)").html(originChangeReason);
        tr.find("td:eq(4)").html("<a href=\"#\" onclick=\"modifyPrice('"+trid+"','"+originPrice+ "','"+originChangeReason+"')\">修改</a>");
    }

    var effectSupplyIds = [];

    <c:forEach var="purchaseInfo" items="${skuPriceChangeApplyDto.effectPurchaseList}">
        effectSupplyIds.push("${purchaseInfo.traderId}");
    </c:forEach>
    
    function checkPrice(tdPrice,validatorEmpty) {

        debugger;

        if (validatorEmpty && tdPrice.value.length == 0) {
            layer.alert("价格不能为空");
            var rateTd = $(tdPrice).parent().next();
            if(tdPrice.value == ""){
                rateTd.html("-");
            }
            return false;
        }

        if(!validatorPrice(tdPrice.value, ["价格不能为0","价格必须是数字，最多2位小数", "价格不能超过3亿"])){
            return;
        }

        var rateTd = $(tdPrice).parent().next();
        if(tdPrice.value == ""){
            rateTd.html("-");
            return;
        }

        debugger;
        var avgPrice = Number(0);
        var currentPriceList = document.getElementsByName("purchasePrice");
        $( "input[name='purchasePrice']").each(
            function(){
                avgPrice=avgPrice+Number($(this).val());
            }
        )
        avgPrice=avgPrice/currentPriceList.length;
        if (!avgPrice){
            rateTd.html("-");
            return ;
        }
        var priceValue = Number(tdPrice.value);
        var perCent = Number((priceValue - avgPrice)/priceValue * 100).toFixed(2);

        var hmtl = perCent + "%";
        if(perCent < 8){
            hmtl = "<font color='red'>"+hmtl+"</font>"
        }

        rateTd.html(hmtl);
    }

    function changePriceGross(){
        debugger;
        changeTerminalPriceGross();
        changeDistributionPriceGross();
        changeGroupPriceGross();
        changeResearchTerminalPriceGross();
        changeElectronicCommercePriceGross();
    }
    function changeResearchTerminalPriceGross() {
        var rateR = $("#researchTerminalPrice").parent().next();
        var priceValue = Number($("#researchTerminalPrice").val());
        if (!priceValue || priceValue == ''){
            rateR.html("-");
            return;
        }
        var perCent = Number((priceValue - getAvgPrice())/priceValue * 100).toFixed(2);
        var hmtl = perCent + "%";
        if(perCent < 8){
            hmtl = "<font color='red'>"+hmtl+"</font>"
        }
        rateR.html(hmtl);
    }
    function changeElectronicCommercePriceGross() {
        var rateE = $("#electronicCommercePrice").parent().next();
        var priceValue = Number($("#electronicCommercePrice").val());
        if (!priceValue || priceValue == ''){
            rateE.html("-");
            return;
        }
        var perCent = Number((priceValue - getAvgPrice())/priceValue * 100).toFixed(2);
        var hmtl = perCent + "%";
        if(perCent < 8){
            hmtl = "<font color='red'>"+hmtl+"</font>"
        }
        rateE.html(hmtl);
    }
    function changeGroupPriceGross() {
        var rateG = $("#groupPrice").parent().next();
        var priceValue = Number($("#groupPrice").val());
        if (!priceValue || priceValue == ''){
            rateG.html("-");
            return;
        }
        var perCent = Number((priceValue - getAvgPrice())/priceValue * 100).toFixed(2);
        var hmtl = perCent + "%";
        if(perCent < 8){
            hmtl = "<font color='red'>"+hmtl+"</font>"
        }
        rateG.html(hmtl);
    }
    function changeTerminalPriceGross() {
        var rateT = $("#terminalPrice").parent().next();
        var priceValue = Number($("#terminalPrice").val());
        if (!priceValue || priceValue == ''){
            rateT.html("-");
            return;
        }
        var perCent = Number((priceValue - getAvgPrice())/priceValue * 100).toFixed(2);
        var hmtl = perCent + "%";
        if(perCent < 8){
            hmtl = "<font color='red'>"+hmtl+"</font>"
        }
        rateT.html(hmtl);
    }
    function changeDistributionPriceGross() {
        var rateD = $("#distributionPrice").parent().next();
        var priceValue = Number($("#distributionPrice").val());
        if (!priceValue || priceValue == ''){
            rateD.html("-");
            return;
        }
        var perCent = Number((priceValue - getAvgPrice())/priceValue * 100).toFixed(2);
        var hmtl = perCent + "%";
        if(perCent < 8){
            hmtl = "<font color='red'>"+hmtl+"</font>"
        }
        rateD.html(hmtl);
    }
    function getAvgPrice(){
        var avgPrice = Number(0);
        var currentPriceList = document.getElementsByName("purchasePrice");
        $( "input[name='purchasePrice']").each(
            function(){
                avgPrice=avgPrice+Number($(this).val());
            }
        )
        avgPrice=avgPrice/currentPriceList.length;
        return avgPrice;
    }
    function addSubmit() {

        debugger;
        checkLogin();

        if (!validateEmpty($("#marketPrice").val(), ["市场价不能为空"])) {
            return;
        }

        if (!validateEmpty($("#terminalPrice").val(), ["终端价不能为空"])) {
            return;
        }

        if (!validateEmpty($("#distributionPrice").val(), ["经销价不能为空"])) {
            return;
        }

        if (!validatorPrice($("#marketPrice").val(), ["市场价价格不能为0","市场价必须是数字，最多2位小数", "市场价不能超过3亿"])) {
            return;
        }

        if (!validatorPrice($("#terminalPrice").val(), ["终端价价格不能为0","终端价必须是数字，最多2位小数", "终端价不能超过3亿"])) {
            return;
        }

        if (!validatorPrice($("#distributionPrice").val(), ["经销价价格不能为0","经销价必须是数字，最多2位小数", "经销价不能超过3亿"])) {
            return;
        }

        if (!validatorPrice($("#groupPrice").val(), ["集团价价格不能为0","集团价必须是数字，最多2位小数", "集团价不能超过3亿"])) {
            return;
        }

        if (!validatorPrice($("#electronicCommercePrice").val(), ["电商价不能为0","电商价必须是数字，最多2位小数", "电商价不能超过3亿"])) {
            return;
        }

        if (!validateEmpty($("#distributionPriceChangeReason").val(), ["经销价变动原因不能为空"])) {
            return;
        }

        var supplynames = [];
        var trs = $('#thisTimeUpdateTbody tr');
        if (trs.length == 0) {
            layer.alert("本次更新供应商价格不能为空!");
            return;
        }
        for (var i = 0; i < trs.length; i++) {
            var suppliyid = $(trs[i]).find('td').eq(0).find('input').eq(1).val();
            var suppliyname = $(trs[i]).find('td').eq(0).find('input').eq(2).val();
            var priceValue = $(trs[i]).find('td').eq(1).find('input').eq(0).val();
            var changeReason = $(trs[i]).find('td').eq(2).find('input').eq(0).val();

            if (suppliyname == '') {
                layer.alert("第" + (i + 1) + "行供应商名称不能为空!");
                return;
            }

            if (changeReason == '') {
                layer.alert("第" + (i + 1) + "行变更原因不能为空!");
                return;
            }

            if (supplynames.includes(suppliyname)) {
                layer.alert("供应商:'" + suppliyname + "'已添加，不可以添加重复数据!");
                return;
            }

            if(priceValue == ""){
                layer.alert("供应商:'" + suppliyname + "'采购价不能为空!");
                return;
            }

            if (!validatorPrice(priceValue, [
                "供应商'" + suppliyname + "'采购价不能为0",
                "供应商'" + suppliyname + "'采购价必须是数字，最多2位小数",
                "供应商'" + suppliyname + "'采购价不能超过3亿"])) {
                return;
            }

            if (effectSupplyIds.includes(suppliyid)) {

                if (changeReason == '') {
                    layer.alert("供应商:'" + suppliyname + "'已经审核通过，变动原因不可为空!");
                    return;
                }

                if (strlen(changeReason) > 50) {
                    layer.alert("变更原因不能超过50个字符");
                    return;
                }
            }

            supplynames.push(suppliyname);
        }

        //特殊处理，如果变动原因为空，就设置一个默认字符*,不然往后台传值有问题
        for (var i = 0; i < trs.length; i++) {
            var changeReasonInput = $(trs[i]).find('td').eq(2).find('input').eq(0);
            if (changeReasonInput.val() == "") {
                changeReasonInput.val("*");
            }
        }

        var firstVerifyPirce = '${firstVerifyPirce}';

        debugger;

        //经销价或终端价是否小于毛利率
        var isPriceSmallGrossPrice = Number($("#terminalPrice").val()) < Number(getAvgPrice()/0.92)
            || Number($("#distributionPrice").val()) < Number(getAvgPrice()/0.92)
            || ($("#researchTerminalPrice").val() && Number($("#researchTerminalPrice").val()) < Number(getAvgPrice()/0.92))
            || ($("#electronicCommercePrice").val() && Number($("#electronicCommercePrice").val()) < Number(getAvgPrice()/0.92));

        //是否已上架
        var isOnSale  = ${isOnSale};
        //VDERP-17872 需求要求不再提醒该消息
        isPriceSmallGrossPrice = false;
        isOnSale = false;
        if (isPriceSmallGrossPrice || isOnSale) {

            var message = getMessage(isPriceSmallGrossPrice,isOnSale);

            layer.confirm(message, {
                btn: ['继续提交', '取消']
            }, function () {
                $("#addForm").submit();
                return;
            }, function () {
                return;
            });

        }else{
            $("#addForm").submit();
        }
    }

    //获取提示消息的内容
    function getMessage(isPriceSmallGrossPrice,isOnSale){

        var mesage = "";

        if(isPriceSmallGrossPrice && isOnSale){
            mesage = "经销价/终端价/科研终端价/电商价的毛利低于8%，且商品已经在${platStr}上架，确认继续操作吗？";
        }else if(isPriceSmallGrossPrice){
            mesage = "经销价/终端价/科研终端价/电商价的毛利低于8%，确认继续操作吗？";
        }else if(isOnSale){
            mesage = "商品已经在${platStr}上架，确认继续操作吗？";
        }
        return mesage;
    }

    function validatorPrice(price, errorTips) {

        if(price == ""){
            return true;
        }

        if (price == "0") {
            layer.alert(errorTips[0]);
            return false;
        }

        var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        if (!reg.test(price)) {
            layer.alert(errorTips[1]);
            return false;
        }

        if (Number(price) > 300000000) {
            layer.alert(errorTips[2]);
            return false;
        }

        return true;

    }

    function validateEmpty(changeReason, errorTips) {
        if (changeReason == "" || changeReason.length == 0) {
            layer.alert(errorTips[0]);
            return false;
        }
        return true;
    }

    //校验市场价、终端价、经销价
    function validatorPrices(price, errorTips) {
        debugger;

        if(price == ""){
            return true;
        }

        var priceNum  = Number(price);
        if (priceNum > 0 && priceNum < 10) {
            if (price.indexOf(".") == -1) {
                return true;
            }
            var index = price.lastIndexOf(".");
            var decimalNumber = price.substring(index + 1, price.length);
            if (decimalNumber.length > 2) {
                layer.alert(errorTips[0]);
                return false;
            }
        } else if (priceNum >= 10 && priceNum < 100) {
            if (price.indexOf(".") == -1) {
                return true;
            }
            var index = price.lastIndexOf(".");
            var decimalNumber = price.substring(index + 1, price.length);
            if (decimalNumber.length > 1) {
                layer.alert(errorTips[1]);
                return false;
            }
            var lastChar = decimalNumber.substr(decimalNumber.length - 1, 1);
            if (lastChar != 8 && lastChar != 9) {
                layer.alert(errorTips[1]);
                return false;
            }
        } else if (priceNum >= 100 && priceNum < 1000) {
            if (price.indexOf(".") == -1) {
                var lastChar = price.substr(price.length - 1, 1);
                if (lastChar != 0 && lastChar != 5 && lastChar != 8 && lastChar != 9) {
                    layer.alert(errorTips[2]);
                    return false;
                } else {
                    return true;
                }
            }
            layer.alert(errorTips[2]);
            return false;
        } else if (priceNum >= 1000 && priceNum < 100000) {
            if (price.indexOf(".") == -1) {
                var lastChar = price.substr(price.length - 1, 1);
                if (lastChar != 0 && lastChar != 8 && lastChar != 9) {
                    layer.alert(errorTips[3]);
                    return false;
                } else {
                    return true;
                }
            }
            layer.alert(errorTips[3]);
            return false;
        } else {
            if (priceNum % 100 == 0) {
                return true;
            }
            layer.alert(errorTips[4]);
            return false;
        }
        return true;
    }

    function delContractSku(contactSkuId) {
        checkLogin();
        layer.confirm("您是否确认删除？", {
            btn: ['确定', '取消'] //按钮
        }, function () {
            $.ajax({
                type: "POST",
                url: "./delContractSku.do",
                data: {'contactSkuId': contactSkuId},
                dataType: 'json',
                success: function (data) {
                    window.location.reload();
                },
                error: function (data) {
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                    }
                }
            });
        }, function () {
        });
    }

    function deleteRow(row) {
        $(row).parent().parent().remove();
    }

    function addRow() {
        var tbody = $("#thisTimeUpdateTbody");

        var tr = $("<tr>\n" +
            "<td>\n" +
            "<input type=\"text\" class=\"input-middle\" onclick=\"searchSupplier(this)\" readonly>\n" +
            "<input type=\"hidden\" name=\"traderId\" />\n" +
            "<input type=\"hidden\" name=\"supplierName\"/>\n" +
            "</td>\n" +
            "<td><input type=\"text\" class=\"input-middle\" name=\"purchasePrice\"  onblur=\"changePriceGross()\">&nbsp;元/${skuPriceChangeApplyDto.unitName}</td>\n" +
            "<td><span class=\"font-red\">*</span>&nbsp;<input placeholder=\"请输入价格变动原因\" type=\"text\" id=\"changeReason\" class=\"input-middle\" name=\"changeReason\"></td>\n" +
            "<td><a href=\"#\" onclick=\"deleteRow(this)\">删除</a></td>\n" +
            "</tr>");
        tbody.append(tr);
    }

    var supplyTd = null;

    function searchSupplier(supplyText) {
        checkLogin();
        supplyTd = $(supplyText).parent();
        var searchUrl = page_url + "/order/buyorder/getSupplierByName.do?supplierName=&callbackFuntion=setTraderInfo";
        $("#popSupplier").attr('layerParams', '{"width":"800px","height":"500px","title":"搜索供应商","link":"' + searchUrl + '"}');
        $("#popSupplier").click();
    }

    function setTraderInfo(traderId, traderSupplierName) {
        var traderIdTd = supplyTd.find('input').eq(1);
        traderIdTd.val(traderId);

        var supplyNameTd = supplyTd.find('input').eq(2);
        supplyNameTd.val(traderSupplierName);

        supplyTd.find('input').first().val(traderSupplierName);

        if (!effectSupplyIds.includes(traderId)) {
            var changeReason = supplyTd.parent().find("td:eq(2)").find("input:first");
            changeReason.val("首次新增");
            changeReason.attr("readonly", "readonly");
        }

    }


    /**
     * 价格规则变更
     */
    function priceRuleChange() {
        var radioValue = $('input:radio[name="priceRuleType"]:checked').val();
        if (radioValue == '2') {
            layer.alert("梯度价格开发中....");
            $('input:radio[name="priceRuleType"][value="1"]').prop("checked", true);
            return;
        }
    }

    //显示悬浮层
    function showInform() {
        $("div").css("top",$("#salePrice").css("top"));
        $("#inform").show();
    }

    //隐藏悬浮层
    function hiddenInform() {
       $("#inform").hide();
    }

</script>
<style>
    #inform {
        position: absolute;
        top: 425px;
        left: 50px;
        width: 450px;
        max-height: 250px; /* 设置最大高度，当高度达到此值时出现滚动条 */
        z-index: 10;
        background-color: white;
        overflow: auto; /* 自动添加滚动条 */
        box-shadow: 0px 0px 10px #000; /* 外阴影 */
        display: none; /* 默认隐藏 */
    }

</style>
<div class="content">
    <div class="formtitle"></div>
    <div id="desc_div">
        <form method="post" id="addForm" action="${pageContext.request.contextPath}/price/basePriceMaintain/editBasePrice.do">

            <input type="hidden" name="skuPriceChangeApplyId" id="skuPriceChangeApplyId"
                   value="${skuPriceChangeApplyDto.id}"/>
            <input type="hidden" name="skuNo" id="skuNo" value="${skuPriceChangeApplyDto.skuNo}"/>

            <ul class="payplan">

                <li>
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">基本信息</div>
                    </div>
                    <div class="parts">
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">订货号</th>
                                <th style="width:80px">商品名称</th>
                                <th style="width:80px">安装费</th>
                                <th style="width:80px">成本价是否含运费</th>
                                <th style="width:80px">销售价是否含运费</th>
                                <th style="width:80px">主销售部门</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td>${skuPriceChangeApplyDto.skuNo}</td>
                                <td>${skuPriceChangeApplyDto.skuName}</td>
                                <td>${installFee}</td>
                                <td>
                                    <select class="input-middle f_left" name="purchaseContainsFee">
                                        <option value="0" <c:if test="${skuPriceChangeApplyDto.purchaseContainsFee == 0}">selected</c:if>>请选择</option>
                                        <option value="1" <c:if test="${skuPriceChangeApplyDto.purchaseContainsFee == 1}">selected</c:if>>含运费</option>
                                        <option value="2" <c:if test="${skuPriceChangeApplyDto.purchaseContainsFee == 2}">selected</c:if>>不含运费</option>
                                    </select>
                                </td>
                                <td>
                                    <select class="input-middle f_left" name="saleContainsFee">
                                        <option value="0" <c:if test="${skuPriceChangeApplyDto.saleContainsFee == 0}">selected</c:if>>请选择</option>
                                        <option value="1" <c:if test="${skuPriceChangeApplyDto.saleContainsFee == 1}">selected</c:if>>含运费</option>
                                        <option value="2" <c:if test="${skuPriceChangeApplyDto.saleContainsFee == 2}">selected</c:if>>不含运费</option>
                                    </select>
                                </td>
                                <td>${skuPriceChangeApplyDto.mainDept}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">成本价</div>

                            <a class="title-click addtitle" href="javascript:void(0);"
                               tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
					            "link":"./price/basePriceMaintain/purchasePriceTrace.do?skuId=${skuPriceChangeApplyDto.skuId}","title":"查看轨迹"}'>查看轨迹</a>
                        </div>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container">
                            <div class="table-title nobor">已生效</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">供应商名称</th>
                                <th style="width:80px">采购成本</th>
                                <th style="width:80px">生效时间</th>
                                <th style="width:80px">毛利率(终端价)</th>
                                <th style="width:80px">毛利率(经销价)</th>
                                <th style="width:80px">毛利率(集团价)</th>
                                <th style="width:80px">毛利率(电商价)</th>
                                <th style="width:80px">毛利率(科研终端价)</th>
                                <th style="width:80px">售后政策</th>
                            </tr>
                            </thead>
                            <tbody id="effect">
                            <c:forEach var="purchaseDetail" items="${skuPriceChangeApplyDto.effectPurchaseList}"
                                       varStatus="staut">
                                <tr>
                                    <td>${purchaseDetail.traderName}</td>
                                    <td style="display:none;">${purchaseDetail.traderId}</td>
                                    <td>${myfn:toString(purchaseDetail.purchasePrice)}&nbsp;元/${skuPriceChangeApplyDto.unitName}</td>
                                    <td>${purchaseDetail.modTime}</td>
                                    <td>${purchaseDetail.terminalRate}</td>
                                    <td>${purchaseDetail.distributionRate}</td>
                                    <td>${purchaseDetail.groupRate}</td>
                                    <td>${purchaseDetail.electronicCommerceRate}</td>
                                    <td>${purchaseDetail.researchTerminalRate}</td>
                                    <td>
                                        <c:if test="${purchaseDetail.supplyPolicyMaintained == 1}">
                                            <span class="title-click addtitle" style='float:none' style="float: right;margin-top: 5px" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                            "link":"${pageContext.request.contextPath}/aftersale/serviceStandard/detail.do?skuNo=${skuPriceChangeApplyDto.skuNo}","title":"查看售后政策"}'>
                                                        查看售后政策
                                                    </span>
                                        </c:if>
                                        <c:if test="${purchaseDetail.supplyPolicyMaintained == 0}">
                                            <span class="title-click addtitle" style='float:none' style="float: right;margin-top: 5px" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                            "link":"${pageContext.request.contextPath}/aftersale/serviceStandard/toAddSupplyAfterSalePolicy.do?skuNo=${skuPriceChangeApplyDto.skuNo}&traderId=${purchaseDetail.traderId}&traderName=${purchaseDetail.traderName}","title":"维护供应商售后政策"}'>
                                                        去维护
                                                    </span>
                                        </c:if>
                                    </td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </li>
                <li>
                    <div class="parts">
                        <div class="title-container">
                            <div class="table-title nobor">本次更新</div>
                            <div class="title-click nobor" onclick="addRow()">新增</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:80px">供应商名称</th>
                                <th style="width:80px">采购成本</th>
                                <th style="width:80px">变动原因</th>
                                <th style="width:80px">操作</th>
                            </tr>
                            </thead>
                            <tbody id="thisTimeUpdateTbody">
                            <c:forEach var="purchaseInfo" items="${skuPriceChangeApplyDto.purchaseInfoList}"
                                       varStatus="staut">
                                <tr>
                                    <td>
                                        <input type="text" class="input-middle" onclick="searchSupplier(this)"
                                               value="${purchaseInfo.traderName}" readonly>
                                        <input type="hidden" name="traderId" value="${purchaseInfo.traderId}"/>
                                        <input type="hidden" name="supplierName" value="${purchaseInfo.traderName}"/>
                                    </td>
                                    <td><input type="text" class="input-middle" name="purchasePrice"
                                               value="${myfn:toString(purchaseInfo.purchasePrice)}" onblur="changePriceGross()">&nbsp;元/${skuPriceChangeApplyDto.unitName}
                                    </td>
                                    <td>
                                        <span class="font-red">*</span>&nbsp;
                                        <input type="text" class="input-middle" name="changeReason" value="${purchaseInfo.changeReason}">
                                    </td>
                                    <td><a href="#" onclick="deleteRow(this)">删除</a></td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>

                        <span style="display:none;">
                            <div class="title-click nobor  pop-new-data" id="popSupplier"></div>
                        </span>

                    </div>
                </li>

                <div>
                    <div class="title-container title-container-blue" id="salePrice">
                        <div class="table-title nobor">销售价</div>
                        &nbsp;&nbsp;
                        <img src="<%= basePath%>static/new/img/wenhao.png" onMouseOver="showInform();" onMouseOut="hiddenInform();" style="margin-top:8px;width: 18px;height: 18px"/>
                    </div>
                    &nbsp;
                    <div id="inform" onMouseOut="hiddenInform()">
                        <p style="margin-left: 10px;margin-top: 5px;font-size:18px;font-family:Arial;font-weight: bolder">销售价填写规范：</p>
                        <p style="margin-left: 20px">0 < 单价 < 10，可保留2位小数。【例:6.99】</p>
                        <p style="margin-left: 20px">10 <= 单价 < 100，只可保留1位小数，且小数只能为8或9 【例:69.9】</p>
                        <p style="margin-left: 20px">100 <= 单价 < 1000，只能填写整数，且个数仅限于0、5、8、9 【例:135】</p>
                        <p style="margin-left: 20px">1000 <= 单价 < 100000，只能填写整数，且个位数仅限于0、8、9 【例:19999】</p>
                        <p style="margin-left: 20px">单价超过100000时，只能填写整数，且个位、十位必须为0 【例:158000】</p>
                        <br>
                    </div>

                    <table class="table  table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th style="width:50px">类型</th>
                            <th style="width:80px">价格</th>
                            <th style="width:80px">毛利率</th>
                            <th style="width:80px">变动原因</th>
                            <th style="width:80px">生效时间</th>
                        </tr>
                        </thead>
                        <tr>
                            <td>市场价</td>
                            <td>
                                <input type="text" style="width:100px;" id="marketPrice" name="marketPrice" value="${skuPriceChangeApplyDto.marketPrice}"/>&nbsp;元/${skuPriceChangeApplyDto.unitName}
                                    &nbsp;
                            </td>
                            <td>-</td>
                            <td>
                                <input type="text" style="width:200px;" id="marketPriceChangeReason"  name="marketPriceChangeReason" placeholder="请输入价格变动原因"
                                       value="${firstVerifyPirce == 1 ? '首次新增': skuPriceChangeApplyDto.marketPriceChangeReason == '首次新增' ? '': skuPriceChangeApplyDto.marketPriceChangeReason}"
                                       <c:if test="${firstVerifyPirce == 1}">readonly</c:if> />
                            </td>
                            <td>${firstEffectTime}</td>
                        </tr>
                        <tr>
                            <td>终端价</td>
                            <td>
                                <input type="text" style="width:100px;" id="terminalPrice" name="terminalPrice" value="${skuPriceChangeApplyDto.terminalPrice}" onblur="checkPrice(this,true)"/>&nbsp;元/${skuPriceChangeApplyDto.unitName}
                                    &nbsp;
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${terminalPriceRate < 8}">
                                        <font color="red">${terminalPriceRate}%</font>
                                    </c:when>
                                    <c:when test="${empty terminalPriceRate}">
                                        -
                                    </c:when>
                                    <c:otherwise>
                                        ${terminalPriceRate}%
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td>
                                <input type="text" style="width:200px;" id="terminalPriceChangeReason" name="terminalPriceChangeReason"  placeholder="请输入价格变动原因"
                                       value="${firstVerifyPirce == 1 ? '首次新增':skuPriceChangeApplyDto.terminalPriceChangeReason == '首次新增' ? '': skuPriceChangeApplyDto.terminalPriceChangeReason}"
                                       <c:if test="${firstVerifyPirce == 1}">readonly</c:if> />
                            </td>
                            <td>${firstEffectTime}</td>
                        </tr>
                        <tr>
                            <td>经销价</td>
                            <td>
                                <input type="text" style="width:100px;" id="distributionPrice" name="distributionPrice" value="${skuPriceChangeApplyDto.distributionPrice}" onblur="checkPrice(this,true)"/>&nbsp;元/${skuPriceChangeApplyDto.unitName}
                                    &nbsp;
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${distributionPriceRate < 8}">
                                        <font color="red">${distributionPriceRate}%</font>
                                    </c:when>
                                    <c:when test="${empty distributionPriceRate}">
                                        -
                                    </c:when>
                                    <c:otherwise>
                                        ${distributionPriceRate}%
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td>
                                <span class="font-red">*</span>
                                <input
                                    typgoods_search.jspe="text"
                                    style="width:200px;"
                                    id="distributionPriceChangeReason"
                                    name="distributionPriceChangeReason"
                                    placeholder="请输入价格变动原因"
                                    onfocus="if(!this.readOnly){ this.value=''; }"
                                    value="${firstVerifyPirce == 1 ? '首次新增'
                                             : (skuPriceChangeApplyDto.distributionPriceChangeReason == '首次新增' ? ''
                                             : skuPriceChangeApplyDto.distributionPriceChangeReason)}"
                                    <c:if test="${firstVerifyPirce == 1}">readonly</c:if>
                                />
                            </td>
                            <td>${firstEffectTime}</td>
                        </tr>
                        <tr>
                            <td>集团价</td>
                            <td>
                                <input type="text" style="width:100px;" id="groupPrice" name="groupPrice" value="${skuPriceChangeApplyDto.groupPrice}" onblur="checkPrice(this,false)"/>&nbsp;元/${skuPriceChangeApplyDto.unitName}
                                    &nbsp;
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${groupPriceRate < 8}">
                                        <font color="red">${groupPriceRate}%</font>
                                    </c:when>
                                    <c:when test="${empty groupPriceRate}">
                                        -
                                    </c:when>
                                    <c:otherwise>
                                        ${groupPriceRate}%
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td>
                                &nbsp;
                                <input type="text" style="width:200px;" id="groupPriceChangeReason" name="groupPriceChangeReason"  placeholder="请输入价格变动原因"
                                       value="${firstVerifyPirce == 1 ? '首次新增':skuPriceChangeApplyDto.groupPriceChangeReason == '首次新增' ? '': skuPriceChangeApplyDto.groupPriceChangeReason}"
                                       <c:if test="${firstVerifyPirce == 1}">readonly</c:if>/>
                            </td>
                            <td>${firstEffectTime}</td>
                        </tr>
                        <tr>
                            <td>电商价</td>
                            <td>
                                <input type="text" style="width:100px;" id="electronicCommercePrice" name="electronicCommercePrice" value="${skuPriceChangeApplyDto.electronicCommercePrice}" onblur="checkPrice(this,false)"/>&nbsp;元/${skuPriceChangeApplyDto.unitName}
                                    &nbsp;
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${eCommercePriceRate < 8}">
                                        <font color="red">${eCommercePriceRate}%</font>
                                    </c:when>
                                    <c:when test="${empty eCommercePriceRate}">
                                        -
                                    </c:when>
                                    <c:otherwise>
                                        ${eCommercePriceRate}%
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td>
                                &nbsp;
                                <input type="text" style="width:200px;" id="electronicCommercePriceChangeReason" name="electronicCommercePriceChangeReason"  placeholder="请输入价格变动原因"
                                       value="${firstVerifyPirce == 1 ? '首次新增':skuPriceChangeApplyDto.electronicCommercePriceChangeReason == '首次新增' ? '': skuPriceChangeApplyDto.electronicCommercePriceChangeReason}"
                                       <c:if test="${firstVerifyPirce == 1}">readonly</c:if>/>
                            </td>
                            <td>${firstEffectTime}</td>
                        </tr>
                        <input type="hidden" name="firstVerifyPirce" value="${firstVerifyPirce}"/>
                        </tbody>
                    </table>
                </div>
            </ul>
            <div class="add-tijiao tcenter mt10">
                <button type="button" class="bt-bg-style bg-deep-green" onclick="addSubmit();">提交</button>
            </div>
            <div class="tcenter mt5" style="color:black;font-size:12px;">
                <i class="fa fa-info-circle"></i> PS：提交后，将默认禁用价格，同时审核中将无法启用，审核通过后将自动启用
            </div>
            <br>
            <br>
            <br>
            <br>
            <input type="hidden" name="formToken" value="${formToken}"/>
        </form>
    </div>
</div>
<%@ include file="../common/footer.jsp" %>