package com.test.finance;

import com.test.BaseTestWithSpring;
import com.vedeng.erp.finance.domain.context.InvoiceRedConfirmationContext;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationEvent;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationStateEnum;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import com.vedeng.common.statemachine.StateMachine;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/10/17 13:41
 */
public class FinanceStateMachineBuilderTest extends BaseTestWithSpring {

    @Autowired
    @Qualifier("invoiceRedConfirmationOperaMachine")
    StateMachine<InvoiceRedConfirmationStateEnum, InvoiceRedConfirmationEvent, InvoiceRedConfirmationContext> invoiceRedConfirmationOperaMachine;

    @Test
    public void test() {
        InvoiceRedConfirmationContext context = new InvoiceRedConfirmationContext();
        InvoiceRedConfirmationStateEnum state = invoiceRedConfirmationOperaMachine.fireEvent(InvoiceRedConfirmationStateEnum.INIT,
                InvoiceRedConfirmationEvent.SYS_INIT, context);
        System.out.println(state.getInfo());
    }




}
