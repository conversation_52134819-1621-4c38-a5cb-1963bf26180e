package com.vedeng.erp.system.service;

import com.vedeng.erp.system.dto.ThirdRequestLogDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.system.service
 * @Date 2023/5/16 9:14
 */
public interface ThirdRequestLogApiService {

    /**
     * 保存日志
     *
     * @param insert ThirdRequestLogDto
     */
    void saveLog(ThirdRequestLogDto insert);

    /**
     * 根据重试状态和重试次数查询错误日志信息
     *
     * @param retryStatus 重试状态 0.待重试 1.重试完成
     * @param retryTimes  重试次数
     * @return List<ThirdRequestLogDto>
     */
    List<ThirdRequestLogDto> getRetryRequest(Integer retryStatus, Integer retryTimes);

    /**
     * 更新日志信息
     *
     * @param update ThirdRequestLogDto
     */
    void updateLog(ThirdRequestLogDto update);
}
