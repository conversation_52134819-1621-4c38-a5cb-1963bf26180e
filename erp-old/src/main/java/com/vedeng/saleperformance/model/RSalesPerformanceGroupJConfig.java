package com.vedeng.saleperformance.model;

public class RSalesPerformanceGroupJConfig {
    private Integer rSalesPerformanceGroupJConfigId;

    private Integer salesPerformanceGroupId;

    private Integer salesPerformanceConfigId;

    private Integer weight;

    private Long addTime;

    private Integer creator;

    private Long modTime;

    private Integer updater;

    public Integer getrSalesPerformanceGroupJConfigId() {
        return rSalesPerformanceGroupJConfigId;
    }

    public void setrSalesPerformanceGroupJConfigId(Integer rSalesPerformanceGroupJConfigId) {
        this.rSalesPerformanceGroupJConfigId = rSalesPerformanceGroupJConfigId;
    }

    public Integer getSalesPerformanceGroupId() {
        return salesPerformanceGroupId;
    }

    public void setSalesPerformanceGroupId(Integer salesPerformanceGroupId) {
        this.salesPerformanceGroupId = salesPerformanceGroupId;
    }

    public Integer getSalesPerformanceConfigId() {
        return salesPerformanceConfigId;
    }

    public void setSalesPerformanceConfigId(Integer salesPerformanceConfigId) {
        this.salesPerformanceConfigId = salesPerformanceConfigId;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}