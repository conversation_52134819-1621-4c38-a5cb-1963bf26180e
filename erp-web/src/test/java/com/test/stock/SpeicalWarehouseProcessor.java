package com.test.stock;

import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.io.*;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName SpeicalWarehouseProcessor.java
 * @Description TODO 查询复核条件库存
 * @createTime 2020年08月24日 19:18:00
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={"classpath:spring.xml","classpath:spring-mybatis.xml"})
public class SpeicalWarehouseProcessor {
    @Resource
    private WarehouseGoodsOperateLogMapper   warehouseGoodsOperateLogMapper;

    @Test
    public void test() throws Exception {
        FileInputStream fileInputStream = new FileInputStream("/Users/<USER>/Downloads/1.txt");
        InputStreamReader isr = new InputStreamReader(fileInputStream);
        BufferedReader br = new BufferedReader(isr);
        String str = null;
        StringBuffer sb = new StringBuffer();
        int count = 0;
        while((str = br.readLine() )!= null){
            String[] split = str.split("\t");
            String sku = split[0];
            String productDateStr = split[3];
            String exprdate = split[4];
            String addTimeStr = split[5];
            String code = split[6];
            Integer num = Integer.valueOf(split[10]);
            Long prodTime = 0L;
             if(StringUtil.isBlank(productDateStr)){
                prodTime = null;
            }else if(!productDateStr.equals("-")){
                 prodTime = DateUtil.convertLong(productDateStr + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
             }
            Long exprTime = 0L;
             if(StringUtil.isBlank(exprdate)){
                exprTime = null;
            }else if(!exprdate.equals("-")){
                exprTime = DateUtil.convertLong(exprdate + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
            }
            long addTime = 0L;
            if(!addTimeStr.isEmpty()){
                addTime = DateUtil.convertLong(addTimeStr + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
            }
            WarehouseGoodsOperateLog warehouseGoodsOperateLog = new WarehouseGoodsOperateLog();
            warehouseGoodsOperateLog.setGoodsId(Integer.valueOf(sku.substring(1)));
            warehouseGoodsOperateLog.setNum(num);
            warehouseGoodsOperateLog.setBatchNumber(StringUtil.isBlank(code) ? null : code);
            warehouseGoodsOperateLog.setExpirationDate(exprTime);
            warehouseGoodsOperateLog.setProductDate(prodTime);
            warehouseGoodsOperateLog.setAddTime(addTime);
            List<WarehouseGoodsOperateLog> inlog =  warehouseGoodsOperateLogMapper.getInLogByInfo(warehouseGoodsOperateLog);
            for (WarehouseGoodsOperateLog goodsOperateLog : inlog) {
                sb = sb.append(goodsOperateLog.getWarehouseGoodsOperateLogId()).append(",");
                count++;
            }
        }

        FileOutputStream fileOutputStream = new FileOutputStream("/Users/<USER>/Downloads/out.txt");
        OutputStreamWriter ow = new OutputStreamWriter(fileOutputStream);
        BufferedWriter bo = new BufferedWriter(ow);
        bo.write("count :" +count +"\n");
        bo.write(sb.toString());
        bo.close();
    }
}
