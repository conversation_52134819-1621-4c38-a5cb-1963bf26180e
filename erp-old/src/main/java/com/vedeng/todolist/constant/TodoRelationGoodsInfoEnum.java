package com.vedeng.todolist.constant;


import com.vedeng.goods.manager.extension.GoodsExtValidModuleEnum;
import com.vedeng.goods.manager.extension.handler.GoodsTodoHandler;

import java.util.Arrays;

/**
 * 关联sku待办信息关联分级分档
 */
public enum TodoRelationGoodsInfoEnum {

    HAS_PRICING(GoodsTodoHandler.PRICING_SERVICE_ID, TodoListBuzSceneEnum.MAINTAIN_DATA_PRICE.getBuzSceneId(), "商品定价"),

    ALLOW_FUTURES(GoodsExtValidModuleEnum.HAS_DELIVERY_TIME.getId(), TodoListBuzSceneEnum.MAINTAIN_DATA_DELIVERY_TIME.getBuzSceneId(), "预计可发货时间"),

    EXTERNAL_AFTER_SALES(GoodsExtValidModuleEnum.EXTERNAL_AFTER_SALES.getId(), TodoListBuzSceneEnum.MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY.getBuzSceneId(), "供应商售后政策"),

    INTERNAL_AFTER_SALES(GoodsExtValidModuleEnum.INTERNAL_AFTER_SALES.getId(), TodoListBuzSceneEnum.MAINTAIN_DATA_AFTER_SALE_POLICY.getBuzSceneId(), "贝登售后标准"),

    AUTHORIZATION(GoodsExtValidModuleEnum.HAS_REPORT.getId(), TodoListBuzSceneEnum.MAINTAIN_DATA_REPORT_INFO.getBuzSceneId(), "报备信息"),

    HAS_SIGN_CONTRACT(GoodsExtValidModuleEnum.HAS_SIGN_CONTRACT.getId(), TodoListBuzSceneEnum.MAINTAIN_DATA_SIGN_CONTRACT_MODE.getBuzSceneId(), "签约模式"),

    HAS_OPERATION(GoodsExtValidModuleEnum.HAS_OPERATION.getId(), TodoListBuzSceneEnum.MAINTAIN_DATA_OPERATION_INFO.getBuzSceneId(), "运营信息");


    private Integer buzSceneId;
    private Integer todoSceneId;
    private String describe;

    TodoRelationGoodsInfoEnum(Integer buzSceneId, Integer todoSceneId, String describe) {
        this.buzSceneId = buzSceneId;
        this.todoSceneId = todoSceneId;
        this.describe = describe;
    }

    public Integer getBuzSceneId() {
        return buzSceneId;
    }

    public void setBuzSceneId(Integer buzSceneId) {
        this.buzSceneId = buzSceneId;
    }

    public Integer getTodoSceneId() {
        return todoSceneId;
    }

    public void setTodoSceneId(Integer todoSceneId) {
        this.todoSceneId = todoSceneId;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public static TodoRelationGoodsInfoEnum getTodoSceneIdByBuzId(Integer buzSceneId) {
        return Arrays.stream(values()).filter(item -> item.getBuzSceneId().equals(buzSceneId)).findFirst().orElse(null);
    }
}
