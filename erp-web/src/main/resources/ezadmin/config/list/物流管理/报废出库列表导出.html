<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>报废出库列表导出</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="cbF9X8zGb0U" datasource="erp-datasourcetarget" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">出库状态</label>
     <div class="layui-input-inline">
      <select class=" layui-input list-search-item " type="search" name="OUT_STATUS" placeholder="" style="" alias="A" jdbctype="" data="[{&quot;V&quot;:&quot;未出库&quot;,&quot;K&quot;:&quot;未出库&quot;},{&quot;V&quot;:&quot;部分出库&quot;,&quot;K&quot;:&quot;部分出库&quot;},{&quot;V&quot;:&quot;全部出库&quot;,&quot;K&quot;:&quot;全部出库&quot;}]" datatype="JSON" oper="EQ"></select>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">申请出库日期</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="APPLE_OUT_DATE" placeholder="" style="" alias="A" jdbctype="" data="" datatype="" oper=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">申请人</label>
     <div class="layui-input-inline">
      <select class=" layui-input list-search-item " type="search" name="APPLYER" placeholder="" style="" alias="A" jdbctype="" data="SELECT A.USERNAME AS 'K' ,A.USERNAME AS 'V' FROM T_USER A WHERE A.COMPANY_ID=1 AND A.IS_DISABLED=0" datatype="KVSQL" oper="EQ"></select>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">申请人部门</label>
     <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="APPLYER_DEPARTMENT" placeholder="" style="" alias="A" jdbctype="" data="SELECT ORG_NAME AS 'K' ,ORG_NAME AS 'V' FROM T_ORGANIZATION A WHERE A.COMPANY_ID=1" datatype="KVSQL" oper="EQ"></select>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">报废处理方式</label>
     <div class="layui-input-inline">
      <select class=" layui-input list-search-item " type="search" name="SCRAPDEALTYPESTR" placeholder="" style="" alias="A" jdbctype="" data="[{&quot;V&quot;:&quot;内部领用&quot;,&quot;K&quot;:&quot;内部领用&quot;},{&quot;V&quot;:&quot;销毁&quot;,&quot;K&quot;:&quot;销毁&quot;},{&quot;V&quot;:&quot;零配件领用&quot;,&quot;K&quot;:&quot;零配件领用&quot;}]" datatype="JSON" oper="EQ"></select>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">报废品级别</label>
     <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="SCRAPLEVELSTR" placeholder="" style="" alias="A" jdbctype="" data="[{&quot;V&quot;:&quot;I级报废品&quot;,&quot;K&quot;:&quot;I级报废品&quot;},{&quot;V&quot;:&quot;II级报废品&quot;,&quot;K&quot;:&quot;II级报废品&quot;},{&quot;V&quot;:&quot;III级报废品&quot;,&quot;K&quot;:&quot;III级报废品&quot;},{&quot;V&quot;:&quot;其他&quot;,&quot;K&quot;:&quot;其他&quot;}]" datatype="JSON" oper="EQ"></select>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">报废品分类</label>
     <div class="layui-input-inline">
      <select class=" layui-input list-search-item " type="search" name="SCRAPTYPESTR" placeholder="" style="" alias="A" jdbctype="" data="[{&quot;V&quot;:&quot;常规报废品&quot;,&quot;K&quot;:&quot;常规报废品&quot;},{&quot;V&quot;:&quot;危险报废品&quot;,&quot;K&quot;:&quot;危险报废品&quot;},{&quot;V&quot;:&quot;其他&quot;,&quot;K&quot;:&quot;其他&quot;}]" datatype="JSON" oper="EQ"></select>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">品牌</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="BRAND_NAME" placeholder="" style="" alias="A" jdbctype="" data="" datatype="" oper=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">单位</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="UNIT_NAME" placeholder="" style="" alias="A" jdbctype="" data="" datatype="" oper=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">型号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="MODEL" placeholder="" style="" alias="A" jdbctype="" data="" datatype="" oper=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">订货号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="SKU_NO" placeholder="" style="" alias="A" jdbctype="" data="" datatype="" oper=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">产品名称</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SKU_NAME" placeholder="" style="" alias="A" jdbctype="" data="" datatype="" oper=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">出库单号</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="ORDER_NO" placeholder="" style="" alias="A" jdbctype="" data="" datatype="" oper=""></object>
     </div>
    </div>
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton">
       <button item_name="导出所有" name="导出所有" url="/ezadmin/list/export-cbF9X8zGb0U" opentype="_BLANK_PARAM" windowname="" style type="table">导出所有</button>
       <button item_name="新增" name="新增" url="/wms/scrapOut/toAddScrappedOut.do" opentype="PARENT" windowname="" style type="table">新增</button>
   </div>
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="ORDER_NO" name="A.ORDER_NO" body="td-link" order="0" url="/wms/scrapOut/scrapDetail.do?scrappedOutId=${ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">出库单号</th>
      <th item_name="SKU_NAME" name="A.SKU_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">产品名称</th>
      <th item_name="SKU_NO" name="A.SKU_NO" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">订货号</th>
      <th item_name="MODEL" name="A.MODEL" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">型号</th>
      <th item_name="UNIT_NAME" name="A.UNIT_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">单位</th>
      <th item_name="BRAND_NAME" name="A.BRAND_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">品牌</th>
      <th item_name="SCRAPTYPESTR" name="A.SCRAPTYPESTR" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">报废品分类</th>
      <th item_name="SCRAPLEVELSTR" name="A.SCRAPLEVELSTR" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">报废品级别</th>
      <th item_name="SCRAPDEALTYPESTR" name="A.SCRAPDEALTYPESTR" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">报废处理方式</th>
      <th item_name="APPLYER_DEPARTMENT" name="A.APPLYER_DEPARTMENT" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">申请人部门</th>
      <th item_name="APPLYER" name="A.APPLYER" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">申请人</th>
      <th item_name="APPLE_OUT_DATE" name="A.APPLE_OUT_DATE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">申请出库日期</th>
      <th item_name="REAL_OUTPUT_TIME" name="A.REAL_OUTPUT_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">实际出库时间</th>
      <th item_name="OUT_STATUS" name="A.OUT_STATUS" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">出库状态</th>
      <th item_name="OUTPUT_NUM" name="A.OUTPUT_NUM" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">出库数量</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="ORDER BY A.ID DESC" groupby="">
        SELECT * FROM (
          SELECT
        T.ID,
        T.ORDER_NO ,
        SKU.SKU_NAME ,
        GOOD.SKU_NO ,
        SKU.MODEL ,
        U.UNIT_NAME ,
        BR.BRAND_NAME ,
	TY.TITLE scrapTypeStr,
        LE.TITLE scrapLevelStr,
        DEA.TITLE scrapDealTypeStr,
	T.APPLYER_DEPARTMENT ,
        T.APPLYER ,
        T.APPLE_OUT_DATE ,
        T.REAL_OUTPUT_TIME ,
        (CASE T.OUT_STATUS
	  WHEN 2 THEN
		 '全部出库'
		WHEN 1 THEN
		'部分出库'
	  ELSE '未出库'   END ) OUT_STATUS,
        GOOD.OUTPUT_NUM 
        FROM
        T_WMS_OUTPUT_ORDER T
        LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS GOOD ON GOOD.WMS_OUTPUT_ORDER_ID = T.ID
        LEFT JOIN V_CORE_SKU SKU ON SKU.SKU_NO = GOOD.SKU_NO
        LEFT JOIN V_CORE_SPU SPU ON SPU.SPU_ID = SKU.SPU_ID
        LEFT JOIN T_UNIT U ON SKU.UNIT_ID=U.UNIT_ID
        LEFT JOIN T_BRAND BR ON BR.BRAND_ID = SPU.BRAND_ID
        LEFT JOIN T_SYS_OPTION_DEFINITION DEA ON DEA.SYS_OPTION_DEFINITION_ID=T.scrap_deal_type
        LEFT JOIN T_SYS_OPTION_DEFINITION LE ON LE.SYS_OPTION_DEFINITION_ID=T.scrap_level
        LEFT JOIN T_SYS_OPTION_DEFINITION TY ON TY.SYS_OPTION_DEFINITION_ID = T.scrap_type
        WHERE
        T.TYPE IN (2,3) AND T.is_delete=0 AND T.ORDER_NO NOT LIKE 'LY%') A
      WHERE 1=1 </pre> count: <pre id="count" class="layui-code"></pre></td>
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>