<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.system.dao.AddressMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.system.model.Address" >
    <id column="ADDRESS_ID" property="addressId" jdbcType="INTEGER" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="TYPE" property="type" jdbcType="BIT" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="AREA_ID" property="areaId" jdbcType="INTEGER" />
    <result column="AREA_IDS" property="areaIds" jdbcType="VARCHAR" />
    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
    <result column="CONTACT_NAME" property="contactName" jdbcType="VARCHAR" />
    <result column="TELEPHONE" property="telephone" jdbcType="VARCHAR" />
    <result column="MOBILE" property="mobile" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  
  <resultMap type="com.vedeng.system.model.vo.AddressVo" id="VoResultMap" extends="BaseResultMap">
  	<result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
  	<result column="PARAMS_CONFIG_VALUE_ID" property="paramsConfigValueId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    ADDRESS_ID, COMPANY_ID, TYPE, IS_ENABLE, AREA_ID, AREA_IDS, ADDRESS, CONTACT_NAME, 
    TELEPHONE, MOBILE, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_ADDRESS
    where ADDRESS_ID = #{addressId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_ADDRESS
    where ADDRESS_ID = #{addressId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.system.model.Address" >
    insert into T_ADDRESS (ADDRESS_ID, COMPANY_ID, TYPE, 
      IS_ENABLE, AREA_ID, AREA_IDS, 
      ADDRESS, CONTACT_NAME, TELEPHONE, 
      MOBILE, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER)
    values (#{addressId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, #{type,jdbcType=BIT}, 
      #{isEnable,jdbcType=BIT}, #{areaId,jdbcType=INTEGER}, #{areaIds,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{contactName,jdbcType=VARCHAR}, #{telephone,jdbcType=VARCHAR}, 
      #{mobile,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.system.model.Address" >
    insert into T_ADDRESS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="addressId != null" >
        ADDRESS_ID,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="isEnable != null" >
        IS_ENABLE,
      </if>
      <if test="areaId != null" >
        AREA_ID,
      </if>
      <if test="areaIds != null" >
        AREA_IDS,
      </if>
      <if test="address != null" >
        ADDRESS,
      </if>
      <if test="contactName != null" >
        CONTACT_NAME,
      </if>
      <if test="telephone != null" >
        TELEPHONE,
      </if>
      <if test="mobile != null" >
        MOBILE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="addressId != null" >
        #{addressId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        #{type,jdbcType=BIT},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=BIT},
      </if>
      <if test="areaId != null" >
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null" >
        #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null" >
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null" >
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
    <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="addressId">
		SELECT LAST_INSERT_ID() AS addressId
    </selectKey>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.system.model.Address" >
    update T_ADDRESS
    <set >
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        TYPE = #{type,jdbcType=BIT},
      </if>
      <if test="isEnable != null" >
        IS_ENABLE = #{isEnable,jdbcType=BIT},
      </if>
      <if test="areaId != null" >
        AREA_ID = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null" >
        AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null" >
        CONTACT_NAME = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        TELEPHONE = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null" >
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where ADDRESS_ID = #{addressId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.system.model.Address" >
    update T_ADDRESS
    set COMPANY_ID = #{companyId,jdbcType=INTEGER},
      TYPE = #{type,jdbcType=BIT},
      IS_ENABLE = #{isEnable,jdbcType=BIT},
      AREA_ID = #{areaId,jdbcType=INTEGER},
      AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      CONTACT_NAME = #{contactName,jdbcType=VARCHAR},
      TELEPHONE = #{telephone,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where ADDRESS_ID = #{addressId,jdbcType=INTEGER}
  </update>
  
  <select id="getAddressVoList" resultMap="VoResultMap" parameterType="java.lang.Integer" >
  	select
  		a.ADDRESS_ID, a.COMPANY_ID, a.TYPE, a.IS_ENABLE, a.AREA_ID, a.AREA_IDS, a.ADDRESS, a.CONTACT_NAME, 
    	a.TELEPHONE, a.MOBILE, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER, c.COMPANY_NAME
  	from T_ADDRESS a
  	left join T_COMPANY c on c.COMPANY_ID = a.COMPANY_ID
  	where a.IS_ENABLE =1 and a.ADDRESS_ID in
  	<foreach item="addressId" index="index" collection="addressIds" open="(" separator="," close=")">  
		  #{addressId}  
	</foreach>
  </select>
  
  <select id="getAddressVoListByParam" resultMap="VoResultMap"  >
  	select
  		a.ADDRESS_ID, a.COMPANY_ID, a.TYPE, a.IS_ENABLE, a.AREA_ID, a.AREA_IDS, a.ADDRESS, a.CONTACT_NAME, 
    	a.TELEPHONE, a.MOBILE, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER, c.COMPANY_NAME
  	from T_ADDRESS a
  	left join T_COMPANY c on c.COMPANY_ID = a.COMPANY_ID
  	where a.IS_ENABLE =1 and a.TYPE = 2 and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
  </select>
  
  <select id="getAddressVoByConfigParams" resultMap="VoResultMap" parameterType="com.vedeng.system.model.vo.ParamsConfigVo" >
  	select
  		a.ADDRESS_ID, a.COMPANY_ID, a.TYPE, a.IS_ENABLE, a.AREA_ID, a.AREA_IDS, a.ADDRESS, a.CONTACT_NAME, 
    	a.TELEPHONE, a.MOBILE, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER, c.COMPANY_NAME
  	from T_ADDRESS a
  	left join T_COMPANY c on c.COMPANY_ID = a.COMPANY_ID
  	left join T_PARAMS_CONFIG_VALUE d on a.ADDRESS_ID = d.PARAMS_VALUE 
  	left join T_PARAMS_CONFIG e on d.PARAMS_CONFIG_ID = e.PARAMS_CONFIG_ID 
  	where a.IS_ENABLE =1 and e.STATUS = 1 
  		and e.PARAMS_KEY = #{paramsKey,jdbcType=INTEGER} and d.COMPANY_ID = #{companyId,jdbcType=INTEGER}
  </select>
  
  <select id="getAddressVoListByConfigParams" resultMap="VoResultMap" parameterType="com.vedeng.system.model.vo.ParamsConfigVo" >
  	select
  		a.ADDRESS_ID, a.COMPANY_ID, a.TYPE, a.IS_ENABLE, a.AREA_ID, a.AREA_IDS, a.ADDRESS, a.CONTACT_NAME, 
    	a.TELEPHONE, a.MOBILE, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER, c.COMPANY_NAME,d.PARAMS_CONFIG_VALUE_ID
  	from T_ADDRESS a
  	left join T_COMPANY c on c.COMPANY_ID = a.COMPANY_ID
  	left join T_PARAMS_CONFIG_VALUE d on a.ADDRESS_ID = d.PARAMS_VALUE and d.PARAMS_CONFIG_ID = 2
  	where a.IS_ENABLE =1 and a.TYPE = 2 and a.COMPANY_ID = #{companyId,jdbcType=INTEGER} 
  		
  </select>
  <select id="getDeliveryAddressByAddressId" resultType="com.vedeng.system.model.vo.AddressVo">
    SELECT
      a.ADDRESS_ID,
      a.COMPANY_ID,
      a.TYPE,
      a.IS_ENABLE,
      a.AREA_ID,
      a.AREA_IDS,
      a.ADDRESS,
      a.CONTACT_NAME,
      a.TELEPHONE,
      a.MOBILE,
      a.ADD_TIME,
      a.CREATOR,
      a.MOD_TIME,
      a.UPDATER,
      c.COMPANY_NAME
    FROM
      T_ADDRESS a
        LEFT JOIN T_COMPANY c ON c.COMPANY_ID = a.COMPANY_ID
        LEFT JOIN T_PARAMS_CONFIG_VALUE d ON a.ADDRESS_ID = d.PARAMS_VALUE
    WHERE
      a.IS_ENABLE = 1
      AND a.COMPANY_ID = 1
      AND a.ADDRESS_ID = #{addressId,jdbcType=INTEGER}
  </select>
</mapper>