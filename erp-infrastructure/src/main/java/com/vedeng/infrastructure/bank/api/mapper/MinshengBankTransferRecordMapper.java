package com.vedeng.infrastructure.bank.api.mapper;

import com.vedeng.infrastructure.bank.api.domain.entity.MinshengBankTransferRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MinshengBankTransferRecordMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(MinshengBankTransferRecord record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(MinshengBankTransferRecord record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    MinshengBankTransferRecord selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(MinshengBankTransferRecord record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(MinshengBankTransferRecord record);

    List<MinshengBankTransferRecord> findByPayApplyId(@Param("payApplyId") Integer payApplyId);

    List<MinshengBankTransferRecord> batchFindByPayApplyIds(@Param("payApplyIds") List<Integer> payApplyIds);
}