package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 销售出库单 https://www.yuque.com/manhuo/gf1570/fwa5f4
 * @date 2023/1/6 10:37
 **/
@AllArgsConstructor
@Setter
@Getter
@WriteBackField(needBackField = {"FID","FEntity.FENTRYID", "FEntity.F_QZOK_BDDJHID"})
public class KingDeeSaleOutStockDto extends KingDeeMqBaseDto {

    private Integer kingDeeSaleOutStockId;

    /**
     * 单据内码
     */
    @WriteBackField
    @KingDeeID
    private String fid;
    /**
     * 单据类型
     */
    private String fBillTypeID;
    /**
     * 单据编号
     */
    private String fBillNo;
    /**
     * 贝登单据头ID
     */
    @BusinessID("F_QZOK_BDDJTID")
    private String f_qzok_bddjtid;
    /**
     * 归属部门
     */
    private String f_qzok_gsbm;
    /**
     * 单据日期
     */
    private String fDate;
    /**
     * 销售组织
     */
    private String fSaleOrgId;
    /**
     * 库存组织
     */
    private String fStockOrgId;
    /**
     * 客户
     */
    private String fCustomerID;
    /**
     * fEntity
     */
    private List<KingDeeSaleOutStockDetailDto> fEntity = new ArrayList<>();

    @Override
    public String getFormId() {
        return KingDeeFormConstant.SAL_OUT_STOCK;
    }

    public KingDeeSaleOutStockDto() {
        this.fBillTypeID = "XSCKD01_SYS";
    }

}
