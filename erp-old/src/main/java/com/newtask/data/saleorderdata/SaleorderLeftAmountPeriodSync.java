package com.newtask.data.saleorderdata;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.newtask.data.AbstractDataSync;
import com.newtask.data.dao.SaleorderDataMapper;
import com.report.dao.SaleorderDataSelectMapper;
import com.newtask.data.dto.SaleoderDataDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 初始化剩余账期
 */
@Component
public class SaleorderLeftAmountPeriodSync extends AbstractDataSync {

    @Resource
    SaleorderDataMapper saleorderDataMapper;
    @Resource
    SaleorderDataSelectMapper saleorderDataSelectMapper;

    @Override
    public List<Integer> loadBizId(Long startTime, Long endTime) {
        List<Integer> saleorderIdsList = saleorderDataSelectMapper.getSaleorderLeftAmountPeriodIdsList(startTime, endTime);

        return saleorderIdsList;
    }

    @Override
    public List<Map<String, Object>> loadBizData(List<Integer> bizIds) {
        List<Map<String, Object>> leftAmountPeriod = null;

        if (CollectionUtils.isNotEmpty(bizIds)) {
            leftAmountPeriod = saleorderDataSelectMapper.findLeftAmountPeriodBySaleorderId(bizIds);
        }

        return leftAmountPeriod;
    }

    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        logger.info("更新订单账期信息,待更新列表:{}", JSONUtil.toJsonStr(dataList));
        if (CollectionUtils.isNotEmpty(dataList)) {
            long timeMillis = System.currentTimeMillis();
            List<SaleoderDataDto> saleoderDataDtos = new ArrayList<>();
            dataList.forEach(item -> {
                Integer saleorderId = NumberUtils.toInt(item.get("saleorderId").toString());

                SaleoderDataDto saleoderDataDto = new SaleoderDataDto();
                saleoderDataDto.setSaleorderId(saleorderId);
                saleoderDataDto.setLeftAmountPeriod(new BigDecimal(item.get("leftAmountPeriod").toString()));
                saleoderDataDtos.add(saleoderDataDto);
                // 判断saleorderId是否存在
                makeExist(saleorderId, timeMillis);
            });
            // 根据id修改firstPayTime
            saleorderDataMapper.updateSaleorderLeftAmountPeriod(saleoderDataDtos, timeMillis);
        }
    }

    @Override
    public void makeExist(Integer id, Long nowTime) {

        Long count = saleorderDataMapper.getSaleorderById(id);
        if (count <= 0) {
            saleorderDataMapper.insertSaleOrder(id, nowTime);
        }

    }
}
