package com.vedeng;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.vedeng.security.EnableSecurity;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

/**
 * 启动类
 *
 * <AUTHOR>
 */
@EnableSecurity
@ComponentScan(basePackages = {"com.vedeng.**", "com.common.**"},
        excludeFilters = {
                //销售
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.controller..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.api..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.manager..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.rebbitmq..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.task..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.strategy..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.buzlogic..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.service..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder..*ServiceImpl"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.service.api..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.common..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.saleorder.web..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.broadcast..*"),


                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.confirmrecord..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.market..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.quote..*"),

                // 快递
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.express..*"),

                // 采购
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.activiti..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.buyorder..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.buyorderexpense..*"),

                // 商机
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.business..*ServiceImpl"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.business.service.api..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.business.common..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.business.web..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.business.strategy..*"),

                // 线索
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.leads..*ServiceImpl"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.leads.service.api..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.leads.facade..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.leads.controller..*"),

                // 客户
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.trader..*ServiceImpl"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.trader.service.api..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.trader.feign..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.trader.common..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.trader.web..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.trader.facade..*"),

                // 产品服务
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.goods.web.api..*"),

                // 系统
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.system.web..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.system.service.impl.FlowOrderServiceImpl"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.system.service.ContractService"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.system.service.impl.ContractServiceImpl"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.system.service.impl.ContractApiServiceImpl"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.system.service.impl.ContractApiService"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.system.handler..*"),
                //@ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.system.service..*"),
                //@ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.system.service.impl..*"),

                // 售后
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.aftersale..*"),

                // 一些不规范写法的包，不要扫描
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.erp.common.rabbitmq..*"),
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.meinian..*"),

                // 不主动扫描sso相关
                @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.vedeng.security..*")
        }
)
@MapperScan(basePackages = {"com.vedeng.**.dao", "com.vedeng.**.mapper"})
@SpringBootApplication
@EnableApolloConfig
public class WebCrmApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(WebCrmApplication.class, args);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(WebCrmApplication.class);
    }

}
