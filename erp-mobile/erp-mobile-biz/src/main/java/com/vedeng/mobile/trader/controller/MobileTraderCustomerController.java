package com.vedeng.mobile.trader.controller;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.domain.dto.DistributionLinkDto;
import com.vedeng.erp.trader.domain.dto.TraderCustomerPortraitDto;
import com.vedeng.erp.trader.dto.CustomerQueryDto;
import com.vedeng.erp.trader.dto.TraderCustomerErpDto;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import com.vedeng.mobile.trader.service.TraderCustomerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 掌上小贝客户管理
 * @date 2024/2/2 10:06
 */
@Slf4j
@RestController
@RequestMapping("/mobile/trader")
public class MobileTraderCustomerController {

    @Autowired
    TraderCustomerService traderCustomerService;
    @Autowired
    TraderCustomerBaseService traderCustomerBaseService;


    /**
     * 客户列表
     *
     * @param pageParam 分页查询参数
     * @return PageInfo<TraderCustomerErpDto>
     */
    @PostMapping("/page")
    public R<PageInfo<TraderCustomerErpDto>> page(@RequestBody PageParam<CustomerQueryDto> pageParam) {
        return R.success(traderCustomerService.page(pageParam));
    }

    /**
     * 客户查询选项
     */
    @PostMapping("/queryParam")
    public R<?> queryParam() {
        return R.success(traderCustomerService.queryParam());
    }


    /**
     * 基本信息
     *
     * @param traderId traderId
     * @return
     */
    @PostMapping("/base")
    @ExcludeAuthorization
    public R<TraderCustomerPortraitDto> base(@RequestParam("traderId") Integer traderId, @RequestParam("traderCustomerId") Integer traderCustomerId) {
        return R.success(traderCustomerService.queryBaseInfo(traderId, traderCustomerId));
    }

    /**
     * 查询经销链路列表信息
     *
     * @param pageParam 分页查询参数
     * @return PageInfo<DistributionLinkDto>
     */
    @PostMapping(value = "/distributionLink/page")
    @ExcludeAuthorization
    public R<PageInfo<DistributionLinkDto>> searchDistributionLinkPage(@RequestBody PageParam<DistributionLinkDto> pageParam) {
        return R.success(traderCustomerBaseService.searchDistributionLinkPage(pageParam));
    }




}
