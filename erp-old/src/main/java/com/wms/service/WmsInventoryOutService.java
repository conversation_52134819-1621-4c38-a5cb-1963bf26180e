package com.wms.service;

import com.vedeng.common.model.ResultInfo;
import com.wms.model.dto.WmsInputOrderGoodsDto;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;

import java.util.List;

/**
 * <AUTHOR>
 * @description 盘亏出库单
 * @date 2022/9/26 10:26
 **/
public interface WmsInventoryOutService {

    /**
     * 盘亏出库保存
     * @param wmsOutputOrder
     * @return
     */
    WmsOutputOrder saveInventoryOutOrder(WmsOutputOrder wmsOutputOrder);

    /**
     * 获取盘亏出库单信息
     * @param wmsOutPutOrderId
     * @return
     */
    WmsOutputOrder getInventoryOutById(Long wmsOutPutOrderId);

    /**
     * 更改盘亏单审核状态
     * @param inventoryOutOrderId
     * @param value
     */
    void updateInventoryOutOrderAuditStatus(Long inventoryOutOrderId, Integer value);

    /**
     * 锁库存
     * @param inventoryOutOrderId 盘亏出库单id
     */
    void synchStockAndPutWmsInventoryOutOrder(Long inventoryOutOrderId) throws Exception;

    /**
     * 获取盘亏出库单明细信息
     * @param inventoryOutOrderId 盘亏出库单id
     * @return List<WmsInputOrderGoodsDto>
     */
    List<WmsOutputOrderGoodsDto> getWmsInputOrderGoodsDto(Long inventoryOutOrderId);

    /**
     * 根据盘亏出库单号获取
     * @param orderNo 单号
     * @return WmsOutputOrder
     */
    WmsOutputOrder getInventoryOutByOrderNo(String orderNo);

    /**
     * 库存检查 库存不够抛出
     * @param check 数据
     */
    void checkData(List<WmsOutputOrderGoods> check);
}
