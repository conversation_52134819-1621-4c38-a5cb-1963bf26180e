.vd-ui-trader-wrap {
    display: flex;
    align-items: flex-start;

    .vd-ui-trader-txt {
        &.vd-ui-trader-link {
            color: #09f;
            cursor: pointer;

            &:hover {
                color: #f60;
            }
        }
    }

    .vd-ui_icon {
        font-size: 16px;
        margin-top: -3px;
        margin-left: 10px;
        cursor: pointer;

        &:hover {
            color: #f60;
        }
    }

    .vd-ui-trader-tyc {
        color: #0084FF;
    }

    .vd-ui-trader-baidu {
        color: #2932E1;
    }

    &.txt-flex {
        .vd-ui-trader-txt {
            flex: 1;
        }
    }
}


.vd-ui-trader-grade {
    width: 20px;
    height: 20px;
    background-size: 100% 100%;

    @level: S, S1, S2, A1, A2, A3, B1, B2, B3, C1, C2, C3, D;

    each(@level, {
        &.lv-@{value} {
            background-image: url('/static/image/traderLevel/@{value}.svg');
        }
    })
}

.vd-ui-call-wrap {
    display: flex;
    align-items: center;

    .vd-ui-call-cnt {

        .icon-call2 {
            font-size: 16px;
            margin-right: 5px;
            line-height: 1;
            vertical-align: -2px;
            color: #666;
        }

        &.link {
            color: #09f;
            cursor: pointer;

            .icon-call2 {
                color: #09f;
            }

            &:hover {
                color: #f60;

                .icon-call2 {
                    color: #f60;
                }
            }
        }
    }
  
    .ui-tel-tip {
        display: flex;
        align-items: center;
        margin-left: 10px;

        .vd-ui_icon {
            font-size: 16px;
            margin-right: 5px;
            line-height: 1;
        }

        &.warn {
            color: #f60;
        }

        &.success {
            color: #13BF13;
        }
    }
}
