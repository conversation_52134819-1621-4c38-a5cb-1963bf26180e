package com.vedeng.crm.business.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.MenuDesc;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.annotation.NoRepeatSubmit;
import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.crm.business.business.domain.dto.BusinessToOrderResponseDto;
import com.vedeng.crm.business.business.domain.dto.LinkOrderRequestDto;
import com.vedeng.crm.business.business.service.CrmBusinessChanceService;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.dto.BusinessCloseDto;
import com.vedeng.erp.business.service.BusinessChanceService;
import com.vedeng.erp.system.dto.CustomTagDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.CustomTagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商机
 * @menu
 */
@ExceptionController
@RestController
@RequestMapping("/crm/businessChance/profile")
@Slf4j
public class BusinessChanceApi {

    @Autowired
    private BusinessChanceService businessChanceService;

    @Autowired
    private CustomTagService customTagService;

    @Autowired
    private CrmBusinessChanceService crmBusinessChanceService;

    /**
     * 添加商机
     */
    @MenuDesc(menuValue = "C0201",menuDesc = "新增商机")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public R<BusinessChanceDto> add(@RequestBody BusinessChanceDto businessChanceDto) {
        BusinessChanceDto data = crmBusinessChanceService.add(businessChanceDto);
        return R.success(data);
    }

    /**
     * 编辑更新商机
     */
    @MenuDesc(menuValue = "C0202",menuDesc = "编辑商机")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<BusinessChanceDto> update(@RequestBody BusinessChanceDto businessChanceDto) {
        BusinessChanceDto update = crmBusinessChanceService.update(businessChanceDto);
        return R.success(update);
    }

    /**
     * 商机列表分页接口
     */
    @MenuDesc(menuValue = "C02",menuDesc = "商机列表")
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public R<PageInfo<BusinessChanceDto>> page(@RequestBody PageParam<BusinessChanceDto> businessChanceQueryDto) {
        return R.success(businessChanceService.page(businessChanceQueryDto));
    }

    /**
     * 列表页更新单条数据
     */
    @RequestMapping(value = "updateSingleData", method = RequestMethod.POST)
    public R<?> updateSingleData(@RequestBody BusinessChanceDto businessChanceDto) {
        businessChanceService.updateSingleData(businessChanceDto);
        return R.success();
    }

    /**
     * 商机列表-归属销售查询条件
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/findAllBelongUser", method = RequestMethod.GET)
    public R<List<UserDto>> findAllBelongUser(@RequestParam(value = "name", required = false) String name) {
        return R.success(businessChanceService.getBussinessChanceBelongers(name));
    }

    /**
     * 商机列表-创建人查询条件
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/findAllCreator", method = RequestMethod.GET)
    public R<List<UserDto>> findAllCreator(@RequestParam(value = "name", required = false) String name) {
        return R.success(businessChanceService.getBussinessChanceCreators(name));
    }

    /**
     * 商机列表-协作人查询条件
     */
    @MenuDesc(menuValue = "C02",menuDesc = "查看商机列表对应的所有协作人")
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/findAllShareUser", method = RequestMethod.GET)
    public R<List<UserDto>> findAllShareUser(@RequestParam(value = "name", required = false) String name) {
        return R.success(businessChanceService.findAllShareUser(name));
    }

    /**
     * 商机列表-标签查询条件
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/findAllTag", method = RequestMethod.GET)
    public R<List<CustomTagDto>> findAllTag(@RequestParam(value = "name", required = false) String name) {
        return R.success(customTagService.findByTypeAndDefaultFlag(ErpConstant.TWO, true, name));
    }

    /**
     * 更新商机等级
     */
    @RequestMapping(value = "/updateLevel", method = RequestMethod.GET)
    @NoNeedAccessAuthorization
    public R<?> updateLevel(@RequestParam Integer businessChanceId) {
        businessChanceService.updateLevel(businessChanceId);
        return R.success();
    }

    /**
     * 关注
     */
    @MenuDesc(menuValue = "C0203",menuDesc = "关注商机")
    @RequestMapping(value = "/attention", method = RequestMethod.POST)
    @NoRepeatSubmit
    public R<?> attention(@RequestBody List<Integer> id) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        boolean flag = businessChanceService.attention(id,currentUser);
        if (!flag) {
            return R.error("关注失败，请刷新后重试");
        }
        return R.success();
    }

    /**
     * 取消关注
     */
    @MenuDesc(menuValue = "C0204",menuDesc = "取消关注")
    @RequestMapping(value = "/cancelAttention", method = RequestMethod.POST)
    @NoRepeatSubmit
    public R<?> cancelAttention(@RequestParam Integer id) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        businessChanceService.cancelAttention(id,currentUser);
        return R.success();
    }

    /**
     * 商机详情
     */
    @RequestMapping(value = "/getDetail", method = RequestMethod.GET)
    @NoNeedAccessAuthorization
    public R<BusinessChanceDto> detail(@RequestParam Integer businessChanceId) {
        return R.success(crmBusinessChanceService.detail(businessChanceId));
    }

    /**
     * 关闭商机
     */
    @MenuDesc(menuValue = "C0207",menuDesc = "关闭商机")
    @RequestMapping(value = "/close", method = RequestMethod.POST)
    @NoRepeatSubmit
    public R<?> closeBusinessAudit(@RequestBody BusinessCloseDto businessCloseDto) {
        crmBusinessChanceService.closeBusiness(businessCloseDto);
        return R.success();
    }


    /**
     * 商机转订单校验
     */
    @RequestMapping(value = "/toOrderVerify", method = RequestMethod.GET)
    @NoRepeatSubmit
    @NoNeedAccessAuthorization
    public R<?> toOrderVerify(@RequestParam Integer businessChanceId, @RequestParam(required = false) String confirm) {
        boolean checkHasSku = crmBusinessChanceService.checkHasSkutoOrderVerify(businessChanceId);
        if(!checkHasSku) {
            return  R.error(BaseResponseCode.NOT_DATA_ERROR.getCode(),"商机尚未添加报价，请先添加报价并生效");
        }

        crmBusinessChanceService.toOrderVerify(businessChanceId);
        if(!"Y".equals(confirm)){
            boolean repeat = crmBusinessChanceService.checkSkuRepeat(businessChanceId);
            if(repeat){
                return R.confirm("报价单中存在重复产品，转订单后将进行数量合并，并取最低价格和货期，是否确认？");
            }
        }
        return R.success();
    }

    /**
     * 商机转订单
     */
    @MenuDesc(menuValue = "C0208",menuDesc = "转订单")
    @RequestMapping(value = "/toOrder", method = RequestMethod.GET)
    @NoRepeatSubmit
    public R<BusinessToOrderResponseDto> toOrder(@RequestParam Integer businessChanceId,@RequestParam Integer systemCode) {
        return R.success(crmBusinessChanceService.businessToOrder(businessChanceId, systemCode));
    }

    /**
     * 商机关联订单
     */
    @MenuDesc(menuValue = "C0209",menuDesc = "关联订单")
    @RequestMapping(value = "/linkOrder", method = RequestMethod.POST)
    @NoRepeatSubmit
    public R<?> linkOrder(@RequestBody LinkOrderRequestDto linkOrderRequestDto) {
        crmBusinessChanceService.linkOrder(linkOrderRequestDto);
        return R.success();
    }

    /**
     * 添加报价
     */
    @RequestMapping(value = "/addQuote", method = RequestMethod.GET)
    @NoRepeatSubmit
    @NoNeedAccessAuthorization
    public R<?> addQuote(@RequestParam Integer businessChanceId) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        Integer quoteOrderId = crmBusinessChanceService.addQuote(businessChanceId, currentUser);
        return R.success(quoteOrderId);
    }
}
