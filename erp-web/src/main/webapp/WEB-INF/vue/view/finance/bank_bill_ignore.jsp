<%--
  Created by IntelliJ IDEA.
  User: ckt
  Date: 2023/8/7
  Time: 09:38
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<head>
    <title>订单结款、付款忽略</title>
</head>
<!-- 引入样式 -->
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/element-ui/css/index.css">
<!-- 引入脚本 -->
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/vue.js"></script>
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/ele-index.js"></script>
<style>

</style>

<div id="app">
    <el-form label-position="right" label-width="200px" :model="ignoreForm" :rules="rules" ref="ignoreForm">
        <el-form-item style="padding-top: 20px" label="对方名称:" hide-required-asterisk="true">
            {{name}}
        </el-form-item>
        <el-form-item label="交易主体:" prop="traderSubject">
            <el-radio-group v-model="ignoreForm.traderSubject">
                <el-radio label="对公"></el-radio>
                <el-radio label="对私"></el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="隐藏" style="display: none">
            <el-input v-model="hide"></el-input>
        </el-form-item>
        <el-form-item label="忽略原因:" prop="selectedIgnoreReasonId">
            <el-select placeholder="请选择忽略原因" filterable v-model="ignoreForm.selectedIgnoreReasonId" @change="changeIgnoreReason" size="medium">
                    <el-option v-for="b in ignoreList" :label="b.title" :value="b.sysOptionDefinitionId" :key="b.sysOptionDefinitionId"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="往来单位类型:" prop="selectedUnitTypeId">
            <el-select placeholder="请选择往来单位类型" v-model="ignoreForm.selectedUnitTypeId" @change="changeUnitType" :disabled="ignoreForm.unitTypeDisable" size="medium">
                <el-option v-for="u in unitTypeList" :label="u.unitType" :value="u.unitTypeId" :disabled="u.disabled"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="往来单位:" prop="unitName">
            <el-select v-model="ignoreForm.unitName"
                       filterable remote
                       :remote-method="remoteUnitNameSearch"
                       placeholder="请选择往来单位"
                       :disabled="ignoreForm.unitNameDisable"
                       clearable = "true"
                       size="medium"
                       @change="selectUnitName">
                <el-option v-for="u in unitNameList" :label="u.fname" :value="u.fnumber" :key="u.fnumber"></el-option>
            </el-select>
        </el-form-item>

        <el-form-item style="padding-top: 140px">
            <el-button :disabled="submitDisable" type="primary" @click="onSubmit('ignoreForm')" style="margin-right: 30px">提交</el-button>
            <el-button @click="onCancel">取消</el-button>
        </el-form-item>
    </el-form>

</div>

<script type="text/javascript">
    var ignoreList = ${ignoreList};
    var name = '${name}';
    var bankBillId = ${bankBillId};
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                ignoreForm: {
                    bankBillId: null,
                    name: '',
                    traderSubject: '对公',
                    unitName: '',
                    selectedUnitTypeId: null,
                    selectedIgnoreReasonId: null,
                    unitNameDisable: false,
                    unitTypeDisable: false,
                    selectFname: '',
                    selectFnumber: ''
                },
                unitNameList: [
                    {
                        fname: '',
                        fnumber: ''
                    }
                ],
                ignoreList: [],
                unitTypeList: [
                    {
                        unitTypeId: 1,
                        unitType: '客户',
                        disabled: false
                    },
                    {
                        unitTypeId: 2,
                        unitType: '供应商',
                        disabled: false
                    },
                    {
                        unitTypeId: 3,
                        unitType: '其他往来单位'
                    },
                    {
                        unitTypeId: 4,
                        unitType: '银行'
                    },
                ],
                submitDisable: false,
                hide: null,
                rules: {
                    traderSubject: [{
                        required: true, message: '请选择交易主体', trigger: 'change'
                    }],
                    selectedIgnoreReasonId: [{
                        required: true, message: '请选择忽略原因', trigger: 'change'
                    }],
                    selectedUnitTypeId: [{
                        required: true, message: '请选择往来单位类型', trigger: 'change'
                    }],
                    unitName: [{
                        required: true, message: '请选择往来单位', trigger: 'change'
                    }]
                }
            }
        },
        methods: {
            /*变更忽略原因*/
            changeIgnoreReason(v){
                console.log("忽略原因change,param:"+v)
                if (4300 == v || 4326 == v){
                    this.unitTypeList.forEach(u => {
                        if (u.unitTypeId == 1 || u.unitTypeId == 2){
                            u.disabled = true;
                        }
                    })
                    this.ignoreForm.selectedUnitTypeId = null;
                }else {
                    this.unitTypeList.forEach(u => {
                        if (u.unitTypeId == 1 || u.unitTypeId == 2){
                            u.disabled = false;
                        }
                    })
                    axios({
                        url: '/finance/bankbill/changeIgnoreReason.do?ignoreReasonId='+this.ignoreForm.selectedIgnoreReasonId,
                        method: 'get'
                    }).then(res => {
                        this.ignoreForm.selectedUnitTypeId = res.data.data;
                        if (4 != this.ignoreForm.selectedUnitTypeId){
                            this.ignoreForm.unitName = '';
                        }
                    });
                }
            },
            /*变更往来单位类型*/
            changeUnitType(v){
                console.log("往来单位类型change,param:"+v)

            },
            /*提交*/
            onSubmit(ignoreForm){
                this.$refs[ignoreForm].validate((valid) => {
                    if(valid){
                        this.submitDisable = true;
                        var uList = this.unitNameList;
                        var uName = this.ignoreForm.unitName;
                        var res = uList.find(u =>{
                            return u.fnumber == uName
                        });
                        this.ignoreForm.selectFname = res.fname;
                        this.ignoreForm.selectFnumber = res.fnumber;
                        axios({
                            url: '/finance/bankbill/saveIgnoreRecord.do?',
                            method: 'post',
                            data: this.ignoreForm
                        }).then(res => {
                            if (res.data.code == 0){
                                this.submitDisable = false;
                                layer.closeAll();
                                parent.layer.closeAll();
                                parent.window.location.reload();
                            }else {
                                this.$message.error('提交失败,请稍后重试!');
                                setTimeout(() =>{
                                    this.submitDisable = false;
                                },1000)
                                return false;
                            }
                        });
                    }else {
                        return false;
                    }
                })
            },
            /*选择往来单位*/
            selectUnitName(v) {
                console.log(v)
            },
            /*取消*/
            onCancel(){
                layer.closeAll();
                parent.layer.closeAll();
            },
            /*远程查询往来单位*/
            remoteUnitNameSearch(v){
                if(v && v != ''){
                    console.log("远程查询金蝶往来单位,名称:"+v)
                    axios({
                        url: '/finance/bankbill/getUnitNameList.do?name='+v+'&unitTypeId='+this.ignoreForm.selectedUnitTypeId+'&bankBillId=-1',
                        method: 'get'
                    }).then(res => {
                        this.unitNameList = res.data.data;
                        console.log(this.unitNameList);
                    });
                }
            }
        },
        watch: {
            'ignoreForm.selectedIgnoreReasonId': {
                handler: function(){
                    this.ignoreForm.unitType = '';
                    if (!this.ignoreForm.selectedIgnoreReasonId){
                        this.ignoreForm.unitTypeDisable = true;
                    }else {
                        this.ignoreForm.unitTypeDisable = false;
                    }
                },
                deep: true
            },
            'ignoreForm.selectedUnitTypeId': {
                handler: function(v){
                    if(this.ignoreForm.selectedIgnoreReasonId){
                        console.log("查询金蝶用户编码,用户名称:"+this.ignoreForm.name+",用户类型枚举:"+v)
                        axios({
                            url: '/finance/bankbill/getUnitNameList.do?name='+this.ignoreForm.name+'&unitTypeId='+v+'&bankBillId='+this.ignoreForm.bankBillId,
                            method: 'get'
                        }).then(res => {
                            this.unitNameList = res.data.data;
                            if(this.unitNameList && this.unitNameList.length>0){
                                this.ignoreForm.unitName = this.unitNameList[0].fnumber;
                            }else {
                                this.ignoreForm.unitName = '';
                                this.unitNameList = null;
                            }
                        });
                    }
                    if (4===v){
                        this.ignoreForm.unitNameDisable = true;
                    }else this.ignoreForm.unitNameDisable = false;
                },
                deep: true
            }
        },
        created() {
            this.ignoreList = ignoreList;
            this.ignoreForm.name = name;
            this.ignoreForm.bankBillId = bankBillId;
            this.ignoreForm.unitTypeDisable = true;
            this.ignoreForm.unitNameDisable = true;
        },
    });
</script>
