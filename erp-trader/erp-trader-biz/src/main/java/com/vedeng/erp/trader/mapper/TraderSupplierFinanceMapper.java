package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.dto.TraderSupplierFinanceDetail;
import com.vedeng.erp.trader.domain.dto.TraderSupplierFinanceExcelDto;
import java.util.List;

import com.vedeng.erp.trader.domain.entity.TraderSupplierFinance;

import java.util.List;

public interface TraderSupplierFinanceMapper {
    /**
     * delete by primary key
     * @param traderSupplierFinanceId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer traderSupplierFinanceId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(TraderSupplierFinance record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(TraderSupplierFinance record);

    /**
     * select by primary key
     * @param traderSupplierFinanceId primary key
     * @return object by primary key
     */
    TraderSupplierFinance selectByPrimaryKey(Integer traderSupplierFinanceId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(TraderSupplierFinance record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(TraderSupplierFinance record);


    /**
     * 获取编辑页供应商信息
     * @param traderCustomerFinanceId
     * @return
     */
    TraderSupplierFinanceDetail getTraderSupplierFinancialById(Integer traderCustomerFinanceId);

    /**
     * 根据供应商id 查询供应商信息
     * @param traderId
     * @return
     */
    TraderSupplierFinance getTraderSupplierFinancialByTraderId(Integer traderId);

    /**
     * 批量新增供应商
     * @param list
     */
    void batchInsert(List<TraderSupplierFinanceExcelDto> list);

    /**
     * 批量更新供应商
     * @param list
     */
    void updateBatchSelective(List<TraderSupplierFinanceExcelDto> list);

    /**
     * 查询traderId已存在记录
     * @param traderIdList
     * @return
     */
	List<Integer> selectByTraderIds(List<Integer> traderIdList);

	/**
	 * 批量插入供应商财务专用表
	 * @param traderIdList
	 */
	void batchInsertSelect(List<Integer> traderIdList);
}