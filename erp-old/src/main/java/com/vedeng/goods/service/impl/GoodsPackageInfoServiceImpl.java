package com.vedeng.goods.service.impl;

import com.vedeng.goods.dao.GoodsPackageInfoMapper;
import com.vedeng.goods.model.dto.GoodsPackageInfoDTO;
import com.vedeng.goods.service.GoodsPackageInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 包装信息服务类接口实现类
 * @author: Laden.chu[<EMAIL>]
 * @createDate: 2021-06-10 16:16
 * @version: 1.0
 */
@Service("goodsPackageInfoServiceImpl")
public class GoodsPackageInfoServiceImpl implements GoodsPackageInfoService {

    @Resource
    private GoodsPackageInfoMapper goodsPackageMapper;

    @Override
    public GoodsPackageInfoDTO getGoodPackageInfo(Integer skuId, Integer spuId) {
        return goodsPackageMapper.getGoodPackageInfo(skuId,spuId);
    }
}
