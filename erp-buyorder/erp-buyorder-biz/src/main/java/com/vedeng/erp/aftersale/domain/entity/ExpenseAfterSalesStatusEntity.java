package com.vedeng.erp.aftersale.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 费用售后状态表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExpenseAfterSalesStatusEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long expenseAfterSalesStatusId;

    /**
     * 费用售后表主键ID
     */
    private Long expenseAfterSalesId;

    /**
     * 费用售后单状态 0.待确认 1.进行中 2.已完结 3.已关闭
     */
    private Integer afterSalesStatus;

    /**
     * 审核状态 0.待审核 1.审核中 2.审核通过 3.审核不通过
     */
    private Integer auditStatus;

    /**
     * 是否生效 0否 1是
     */
    private Integer validStatus;

    /**
     * 生效时间
     */
    private Date validTime;

    /**
     * 退款状态 0无退款 1未退款 2部分退款 3全部退款
     */
    private Integer refundStatus;

    /**
     * 退票状态 0无退票 1未退票 2部分退票 3全部退票
     */
    private Integer returnInvoiceStatus;

    /**
     * 应退金额
     */
    private BigDecimal needReturnAmount;

    /**
     * 账期退还金额
     */
    private BigDecimal repaymentPeriod;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 更新备注
     */
    private String updateRemark;
}