package com.vedeng.kpi.service;

import com.vedeng.kpi.model.DO.KpiDailyCountDo;
import com.vedeng.kpi.model.DTO.KpiDailyCountExtDto;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiUserConfigDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;
import com.vedeng.kpi.model.VO.KpiDailyCountVo;
import com.vedeng.kpi.model.VO.KpiGroupCountSumVo;
import com.vedeng.kpi.model.VO.KpiGroupVo;
import com.vedeng.kpi.model.VO.KpiTeamVo;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @description: 五行daily表service.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/9 10:44 上午.
 * @author: Tomcat.Hui.
 */
public interface KpiDailyCountService {

    /**
     * @description: 插入或更新.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/9 10:45 上午.
     * @author: Tomcat.Hui.
     * @param kpiDailyCountDo: .
     * @return: com.vedeng.kpi.model.data.KpiDailyCountDo.
     * @throws: .
     */
    Boolean insertOrUpdate(KpiDailyCountDo query);

    /**
     * @description: 查询团队kpi统计数据.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 分组后组内排序取出每个分组内KPI_DATE最大的一条数据,然后排序.
     * @version: 1.0.
     * @date: 2020/6/9 10:45 上午.
     * @author: Tomcat.Hui.
     * @param userInfo: .
     * @return: java.util.List<com.vedeng.kpi.model.data.KpiDailyCountDo>.
     * @throws: .
     */
    List<KpiDailyCountExtDto> selectByGroupDate(KpiUserInfoDto userInfo,String type);

    /**
     * @description: 查询单个用户业绩情况(按照给定时间区间).
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/16 4:06 下午.
     * @author: Tomcat.Hui.
     * @param query: .
     * @return: com.vedeng.kpi.model.DO.KpiDailyCountDo.
     * @throws: .
     */
    Map<String,List<KpiDailyCountExtDto>> selectBySingleUserHis(KpiDataQueryDto query,String type);

    /**
     * @description: 获取业绩list(当前时间).
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/18 9:50 上午.
     * @author: Tomcat.Hui.
     * @param userInfo: .
     * @param start: .
     * @param end: .
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDailyCountExtDto>.
     * @throws: .
     */
    List<KpiDailyCountExtDto> getKpiCountList(KpiUserInfoDto userInfo, Date start, Date end, String type);

    /**
     * @description: 查询单个用户kpi统计结果.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/18 11:39 上午.
     * @author: Tomcat.Hui.
     * @param userInfo: .
     * @param start: .
     * @param end: .
     * @param userId: .
     * @return: com.vedeng.kpi.model.DTO.KpiDailyCountExtDto.
     * @throws: .
     */
    KpiDailyCountExtDto getKpiCountVoByUser(KpiUserInfoDto userInfo, Date start, Date end, String type);

    /**
     * @description: 获取团队页面数据.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/6/21 9:07 下午.
     * @author: Tomcat.Hui.
     * @param query: .
     * @return: java.util.Map<java.lang.String,java.util.Map<java.lang.String,com.vedeng.kpi.model.VO.KpiDailyCountSumVo>>.
     * @throws: .
     */
    Map<String, Map<Integer,KpiDailyCountExtDto>> getGroupCountDataAll(KpiDataQueryDto query);

    Map<KpiGroupVo,Map<KpiTeamVo, KpiGroupCountSumVo>> getGroupCountData(Map<String, Map<Integer,KpiDailyCountExtDto>> allData,
                                                                          KpiDataQueryDto query);

    List<KpiDailyCountVo> getKpiDailyCountMonthSortList(KpiDataQueryDto query);

    List<KpiDailyCountExtDto> getKpiDailyCountMonthList(KpiDataQueryDto query);


    Map<String,List<KpiDailyCountVo>> getTeamCountData(Map<String, Map<Integer, KpiDailyCountExtDto>> monthList,
                                                       KpiUserInfoDto userInfoi,Date queryMonth);

    Map<String, Map<Integer, KpiDailyCountExtDto>> getTeamCountDataAll(KpiDataQueryDto query);

    Map<String,KpiGroupCountSumVo> transGroupHisData(Map<String, List<KpiDailyCountExtDto>> data);

    List<KpiDailyCountExtDto> getByTeams(KpiDataQueryDto query);
}
