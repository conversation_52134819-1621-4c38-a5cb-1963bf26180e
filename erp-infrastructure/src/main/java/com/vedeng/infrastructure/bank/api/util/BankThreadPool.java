package com.vedeng.infrastructure.bank.api.util;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 定义全局线程池对象
 */
public class BankThreadPool {

    private static ExecutorService messageExecute = new ThreadPoolExecutor(4, 8,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<Runnable>(1024),
            new ThreadFactoryBuilder().setNameFormat("bank-thread").build(),
            new ThreadPoolExecutor.AbortPolicy());


    public static void submit(Runnable message){
        messageExecute.submit(message);
    }

}
