<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.dwh.externaldb.dao.generate.DwdErpOrganizationNjDfMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.dwh.model.generate.DwdErpOrganizationNjDfDo">
    <id column="VD_ODS_PK" jdbcType="BIGINT" property="vdOdsPk" />
    <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime" />
    <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="L1_ID" jdbcType="INTEGER" property="l1Id" />
    <result column="L1_NAME" jdbcType="VARCHAR" property="l1Name" />
    <result column="L1_TYPE" jdbcType="INTEGER" property="l1Type" />
    <result column="L1_LEVEL" jdbcType="INTEGER" property="l1Level" />
    <result column="L1_CREATOR" jdbcType="INTEGER" property="l1Creator" />
    <result column="L1_COMPANY_ID" jdbcType="INTEGER" property="l1CompanyId" />
    <result column="L1_ADD_TIME" jdbcType="TIMESTAMP" property="l1AddTime" />
    <result column="L2_ID" jdbcType="INTEGER" property="l2Id" />
    <result column="L2_NAME" jdbcType="VARCHAR" property="l2Name" />
    <result column="L2_TYPE" jdbcType="INTEGER" property="l2Type" />
    <result column="L2_LEVEL" jdbcType="INTEGER" property="l2Level" />
    <result column="L2_CREATOR" jdbcType="INTEGER" property="l2Creator" />
    <result column="L2_COMPANY_ID" jdbcType="INTEGER" property="l2CompanyId" />
    <result column="L2_ADD_TIME" jdbcType="TIMESTAMP" property="l2AddTime" />
    <result column="L3_ID" jdbcType="INTEGER" property="l3Id" />
    <result column="L3_NAME" jdbcType="VARCHAR" property="l3Name" />
    <result column="L3_TYPE" jdbcType="INTEGER" property="l3Type" />
    <result column="L3_LEVEL" jdbcType="INTEGER" property="l3Level" />
    <result column="L3_CREATOR" jdbcType="INTEGER" property="l3Creator" />
    <result column="L3_COMPANY_ID" jdbcType="INTEGER" property="l3CompanyId" />
    <result column="L3_ADD_TIME" jdbcType="TIMESTAMP" property="l3AddTime" />
  </resultMap>
  <sql id="Base_Column_List">
    VD_ODS_PK, START_TIME, END_TIME, ORG_ID, L1_ID, L1_NAME, L1_TYPE, L1_LEVEL, L1_CREATOR, 
    L1_COMPANY_ID, L1_ADD_TIME, L2_ID, L2_NAME, L2_TYPE, L2_LEVEL, L2_CREATOR, L2_COMPANY_ID, 
    L2_ADD_TIME, L3_ID, L3_NAME, L3_TYPE, L3_LEVEL, L3_CREATOR, L3_COMPANY_ID, L3_ADD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from DWD_ERP_ORGANIZATION_NJ_DF
    where VD_ODS_PK = #{vdOdsPk,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from DWD_ERP_ORGANIZATION_NJ_DF
    where VD_ODS_PK = #{vdOdsPk,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="VD_ODS_PK" keyProperty="vdOdsPk" parameterType="com.vedeng.dwh.model.generate.DwdErpOrganizationNjDfDo" useGeneratedKeys="true">
    insert into DWD_ERP_ORGANIZATION_NJ_DF (START_TIME, END_TIME, ORG_ID, 
      L1_ID, L1_NAME, L1_TYPE, 
      L1_LEVEL, L1_CREATOR, L1_COMPANY_ID, 
      L1_ADD_TIME, L2_ID, L2_NAME, 
      L2_TYPE, L2_LEVEL, L2_CREATOR, 
      L2_COMPANY_ID, L2_ADD_TIME, L3_ID, 
      L3_NAME, L3_TYPE, L3_LEVEL, 
      L3_CREATOR, L3_COMPANY_ID, L3_ADD_TIME
      )
    values (#{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{orgId,jdbcType=INTEGER}, 
      #{l1Id,jdbcType=INTEGER}, #{l1Name,jdbcType=VARCHAR}, #{l1Type,jdbcType=INTEGER}, 
      #{l1Level,jdbcType=INTEGER}, #{l1Creator,jdbcType=INTEGER}, #{l1CompanyId,jdbcType=INTEGER}, 
      #{l1AddTime,jdbcType=TIMESTAMP}, #{l2Id,jdbcType=INTEGER}, #{l2Name,jdbcType=VARCHAR}, 
      #{l2Type,jdbcType=INTEGER}, #{l2Level,jdbcType=INTEGER}, #{l2Creator,jdbcType=INTEGER}, 
      #{l2CompanyId,jdbcType=INTEGER}, #{l2AddTime,jdbcType=TIMESTAMP}, #{l3Id,jdbcType=INTEGER}, 
      #{l3Name,jdbcType=VARCHAR}, #{l3Type,jdbcType=INTEGER}, #{l3Level,jdbcType=INTEGER}, 
      #{l3Creator,jdbcType=INTEGER}, #{l3CompanyId,jdbcType=INTEGER}, #{l3AddTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="VD_ODS_PK" keyProperty="vdOdsPk" parameterType="com.vedeng.dwh.model.generate.DwdErpOrganizationNjDfDo" useGeneratedKeys="true">
    insert into DWD_ERP_ORGANIZATION_NJ_DF
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="startTime != null">
        START_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="l1Id != null">
        L1_ID,
      </if>
      <if test="l1Name != null">
        L1_NAME,
      </if>
      <if test="l1Type != null">
        L1_TYPE,
      </if>
      <if test="l1Level != null">
        L1_LEVEL,
      </if>
      <if test="l1Creator != null">
        L1_CREATOR,
      </if>
      <if test="l1CompanyId != null">
        L1_COMPANY_ID,
      </if>
      <if test="l1AddTime != null">
        L1_ADD_TIME,
      </if>
      <if test="l2Id != null">
        L2_ID,
      </if>
      <if test="l2Name != null">
        L2_NAME,
      </if>
      <if test="l2Type != null">
        L2_TYPE,
      </if>
      <if test="l2Level != null">
        L2_LEVEL,
      </if>
      <if test="l2Creator != null">
        L2_CREATOR,
      </if>
      <if test="l2CompanyId != null">
        L2_COMPANY_ID,
      </if>
      <if test="l2AddTime != null">
        L2_ADD_TIME,
      </if>
      <if test="l3Id != null">
        L3_ID,
      </if>
      <if test="l3Name != null">
        L3_NAME,
      </if>
      <if test="l3Type != null">
        L3_TYPE,
      </if>
      <if test="l3Level != null">
        L3_LEVEL,
      </if>
      <if test="l3Creator != null">
        L3_CREATOR,
      </if>
      <if test="l3CompanyId != null">
        L3_COMPANY_ID,
      </if>
      <if test="l3AddTime != null">
        L3_ADD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="l1Id != null">
        #{l1Id,jdbcType=INTEGER},
      </if>
      <if test="l1Name != null">
        #{l1Name,jdbcType=VARCHAR},
      </if>
      <if test="l1Type != null">
        #{l1Type,jdbcType=INTEGER},
      </if>
      <if test="l1Level != null">
        #{l1Level,jdbcType=INTEGER},
      </if>
      <if test="l1Creator != null">
        #{l1Creator,jdbcType=INTEGER},
      </if>
      <if test="l1CompanyId != null">
        #{l1CompanyId,jdbcType=INTEGER},
      </if>
      <if test="l1AddTime != null">
        #{l1AddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="l2Id != null">
        #{l2Id,jdbcType=INTEGER},
      </if>
      <if test="l2Name != null">
        #{l2Name,jdbcType=VARCHAR},
      </if>
      <if test="l2Type != null">
        #{l2Type,jdbcType=INTEGER},
      </if>
      <if test="l2Level != null">
        #{l2Level,jdbcType=INTEGER},
      </if>
      <if test="l2Creator != null">
        #{l2Creator,jdbcType=INTEGER},
      </if>
      <if test="l2CompanyId != null">
        #{l2CompanyId,jdbcType=INTEGER},
      </if>
      <if test="l2AddTime != null">
        #{l2AddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="l3Id != null">
        #{l3Id,jdbcType=INTEGER},
      </if>
      <if test="l3Name != null">
        #{l3Name,jdbcType=VARCHAR},
      </if>
      <if test="l3Type != null">
        #{l3Type,jdbcType=INTEGER},
      </if>
      <if test="l3Level != null">
        #{l3Level,jdbcType=INTEGER},
      </if>
      <if test="l3Creator != null">
        #{l3Creator,jdbcType=INTEGER},
      </if>
      <if test="l3CompanyId != null">
        #{l3CompanyId,jdbcType=INTEGER},
      </if>
      <if test="l3AddTime != null">
        #{l3AddTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.dwh.model.generate.DwdErpOrganizationNjDfDo">
    update DWD_ERP_ORGANIZATION_NJ_DF
    <set>
      <if test="startTime != null">
        START_TIME = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="l1Id != null">
        L1_ID = #{l1Id,jdbcType=INTEGER},
      </if>
      <if test="l1Name != null">
        L1_NAME = #{l1Name,jdbcType=VARCHAR},
      </if>
      <if test="l1Type != null">
        L1_TYPE = #{l1Type,jdbcType=INTEGER},
      </if>
      <if test="l1Level != null">
        L1_LEVEL = #{l1Level,jdbcType=INTEGER},
      </if>
      <if test="l1Creator != null">
        L1_CREATOR = #{l1Creator,jdbcType=INTEGER},
      </if>
      <if test="l1CompanyId != null">
        L1_COMPANY_ID = #{l1CompanyId,jdbcType=INTEGER},
      </if>
      <if test="l1AddTime != null">
        L1_ADD_TIME = #{l1AddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="l2Id != null">
        L2_ID = #{l2Id,jdbcType=INTEGER},
      </if>
      <if test="l2Name != null">
        L2_NAME = #{l2Name,jdbcType=VARCHAR},
      </if>
      <if test="l2Type != null">
        L2_TYPE = #{l2Type,jdbcType=INTEGER},
      </if>
      <if test="l2Level != null">
        L2_LEVEL = #{l2Level,jdbcType=INTEGER},
      </if>
      <if test="l2Creator != null">
        L2_CREATOR = #{l2Creator,jdbcType=INTEGER},
      </if>
      <if test="l2CompanyId != null">
        L2_COMPANY_ID = #{l2CompanyId,jdbcType=INTEGER},
      </if>
      <if test="l2AddTime != null">
        L2_ADD_TIME = #{l2AddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="l3Id != null">
        L3_ID = #{l3Id,jdbcType=INTEGER},
      </if>
      <if test="l3Name != null">
        L3_NAME = #{l3Name,jdbcType=VARCHAR},
      </if>
      <if test="l3Type != null">
        L3_TYPE = #{l3Type,jdbcType=INTEGER},
      </if>
      <if test="l3Level != null">
        L3_LEVEL = #{l3Level,jdbcType=INTEGER},
      </if>
      <if test="l3Creator != null">
        L3_CREATOR = #{l3Creator,jdbcType=INTEGER},
      </if>
      <if test="l3CompanyId != null">
        L3_COMPANY_ID = #{l3CompanyId,jdbcType=INTEGER},
      </if>
      <if test="l3AddTime != null">
        L3_ADD_TIME = #{l3AddTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where VD_ODS_PK = #{vdOdsPk,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.dwh.model.generate.DwdErpOrganizationNjDfDo">
    update DWD_ERP_ORGANIZATION_NJ_DF
    set START_TIME = #{startTime,jdbcType=TIMESTAMP},
      END_TIME = #{endTime,jdbcType=TIMESTAMP},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      L1_ID = #{l1Id,jdbcType=INTEGER},
      L1_NAME = #{l1Name,jdbcType=VARCHAR},
      L1_TYPE = #{l1Type,jdbcType=INTEGER},
      L1_LEVEL = #{l1Level,jdbcType=INTEGER},
      L1_CREATOR = #{l1Creator,jdbcType=INTEGER},
      L1_COMPANY_ID = #{l1CompanyId,jdbcType=INTEGER},
      L1_ADD_TIME = #{l1AddTime,jdbcType=TIMESTAMP},
      L2_ID = #{l2Id,jdbcType=INTEGER},
      L2_NAME = #{l2Name,jdbcType=VARCHAR},
      L2_TYPE = #{l2Type,jdbcType=INTEGER},
      L2_LEVEL = #{l2Level,jdbcType=INTEGER},
      L2_CREATOR = #{l2Creator,jdbcType=INTEGER},
      L2_COMPANY_ID = #{l2CompanyId,jdbcType=INTEGER},
      L2_ADD_TIME = #{l2AddTime,jdbcType=TIMESTAMP},
      L3_ID = #{l3Id,jdbcType=INTEGER},
      L3_NAME = #{l3Name,jdbcType=VARCHAR},
      L3_TYPE = #{l3Type,jdbcType=INTEGER},
      L3_LEVEL = #{l3Level,jdbcType=INTEGER},
      L3_CREATOR = #{l3Creator,jdbcType=INTEGER},
      L3_COMPANY_ID = #{l3CompanyId,jdbcType=INTEGER},
      L3_ADD_TIME = #{l3AddTime,jdbcType=TIMESTAMP}
    where VD_ODS_PK = #{vdOdsPk,jdbcType=BIGINT}
  </update>
</mapper>