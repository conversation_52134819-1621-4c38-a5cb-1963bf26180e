package com.pricecenter.service.imp;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.pricecenter.constant.VerifyStatusEnum;
import com.pricecenter.dto.*;
import com.pricecenter.service.BasePriceMaintainService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.dao.UnitMapper;
import com.vedeng.goods.manager.extension.handler.GoodsPriceTodoHandler;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.Unit;
import com.vedeng.price.api.price.dto.price.SkuPriceContainsFeeApi;
import com.vedeng.price.dao.SkuPriceModifyRecordMapper;
import com.vedeng.price.model.SkuPriceModifyRecord;
import com.vedeng.trader.dao.TraderMapper;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BasePriceMaintainServiceImpl implements BasePriceMaintainService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BasePriceMaintainServiceImpl.class);

    @Value("${price.url}")
    private String priceUrl;

    private static final String SUCCESS_CODE = "success";

    private static final String BASE_PRICEMAINTAIN_PAGE = "sku_price_change_apply/list/page";
    
    private static final String BASE_PRICEMAINTAIN_CONTAINS_FEE = "sku_price_change_apply/selectPriceInfoBySkuNos";

    private static final String BASE_PRICEMAINTAIN_DETAIL = "sku_price_change_apply/findSkuPriceChangeApplyById";

    private static final String BASICPRICECHANGEINFO_DETAIL = "sku_price_change_apply/findBasicPricechangeInfoById";

    private static final String BASE_PRICEMAINTAIN_EFFECTPURCHASELIST = "sku_price_change_apply/findPurchaseHistoryListBySkuId";

    private static final String BASE_PRICEMAINTAIN_INFORMPRICECHANGEAPPLY = "sku_price_change_apply/informPriceChangeApply";

    private static final String BASE_PRICEMAINTAIN_UPDATE_PRICECHANGE_STATUS = "sku_price_change_apply/updatePricechangeStatus";

    private static final String BASE_PRICEMAINTAIN_BATCHADDPRICEINFO = "sku_price_change_apply/batchAddPriceInfo";

    private static final String BASE_PRICEMAINTAIN_BATCHGETSKUPRICING = "sku_price_change_apply/batchGetSkuPricing";

    private static final String BASE_PRICEMAINTAIN_UPDATEPRICECHANGEAUDITORBYIDS = "sku_price_change_apply/updatePriceChangeAuditorByIds";

    private static final String BASE_PRICEMAINTAIN_ADDPRICECHANGE = "sku_price_change_apply/addPriceChange";

    private static final String BASE_PRICEMAINTAIN_DISABLEPRICE = "sku_price_change_apply/disablePrice";

    private static final String BASE_PRICEMAINTAIN_ENDABLEPRICE= "sku_price_change_apply/endablePrice";
    
    private static final String BASE_PRICEMAINTAIN_UPDATE_VD_VISIBILITY = "sku_price_change_apply/updateVdVisibility";

    private static final String FIND_PRICEDSKU_PAGEQUERY = "sku_price_change_apply/findPricedSkuPage";

    private static final String BATCH_UPDATE_GROUPPRICE = "sku_price_change_apply/batchUpdateGroupPrice";

    @Autowired
    private TraderMapper traderMapper;

    @Autowired
    private UnitMapper unitMapper;

    @Autowired
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Autowired
    private SkuPriceModifyRecordMapper skuPriceModifyRecordMapper;

    @Resource
    private GoodsPriceTodoHandler goodsPriceTodoHandler;

    @Override
    public PageResultDto<SkuPriceChangeApplyDto> findByPage(SkuPriceChangeApplyPageQueryDto skuPriceChangeApplyPageQueryDto) {
        PageResultDto pageResultDto = new PageResultDto();
        try {
            //封装请求参数
            String requestJson = JsonUtils.translateToJson(skuPriceChangeApplyPageQueryDto);
            LOGGER.info("BasePriceMaintainServiceImpl->findByPage 请求参数:" + requestJson);
            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASE_PRICEMAINTAIN_PAGE, requestJson);
            LOGGER.info("BasePriceMaintainServiceImpl->findByPage 响应:" + resultJsonObj.toString());
            if (!SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                return pageResultDto;
            }
            Gson gson = new Gson();
            List<SkuPriceChangeApplyDto> priceChangeApplyList = gson.fromJson(resultJsonObj.getJSONObject("data").get("priceChangeApplyList").toString(),
                    new TypeToken<List<SkuPriceChangeApplyDto>>() {
                    }.getType());
            if (!CollectionUtils.isEmpty(priceChangeApplyList)) {
                for (SkuPriceChangeApplyDto priceChangeApply : priceChangeApplyList) {
                    List<BigDecimal> purchasePrice = priceChangeApply.getPurchaseInfoList().stream().filter(apply -> apply != null).map(apply -> apply.getPurchasePrice()).collect(Collectors.toList());
                    priceChangeApply.setMiddlePrice(dealWithPurchasePrice(purchasePrice));
                    dealWithSalePrice(priceChangeApply);
                }
            }
            //获取总记录数
            String totleRecord = resultJsonObj.getJSONObject("data").get("totalRecord").toString();
            pageResultDto.setDatas(priceChangeApplyList);
            pageResultDto.setTotalRecords(Integer.valueOf(totleRecord));
        } catch (Exception e) {
            LOGGER.error("基础信息核价列表分页查询失败", e);
        }
        return pageResultDto;
    }
    
    @Override
    public List<SkuPriceContainsFeeApi> selectPriceInfoBySkuNos(List<String> skuNos) {
    	List<SkuPriceContainsFeeApi> skuPriceContainsFeeApiList = new ArrayList<>();
        try {
            //封装请求参数
            String requestJson = JsonUtils.translateToJson(skuNos);
            LOGGER.info("BasePriceMaintainServiceImpl->selectPriceInfoBySkuNos 请求参数:" + requestJson);
            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASE_PRICEMAINTAIN_CONTAINS_FEE, requestJson);
            LOGGER.info("BasePriceMaintainServiceImpl->selectPriceInfoBySkuNos 响应:" + resultJsonObj.toString());
            Gson gson = new Gson();
            if (!SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                return skuPriceContainsFeeApiList;
            }
            skuPriceContainsFeeApiList = gson.fromJson(resultJsonObj.getJSONArray("data").toString(),
                    new TypeToken<List<SkuPriceContainsFeeApi>>() {
                    }.getType());
        } catch (Exception e) {
            LOGGER.error("基础信息核价列表分页查询失败", e);
        }
        return skuPriceContainsFeeApiList;
    }

    /**
     * 处理销售价
     *
     * @param priceChangeApply
     */
    private void dealWithSalePrice(SkuPriceChangeApplyDto priceChangeApply) {

        if (CollectionUtils.isEmpty(priceChangeApply.getSaleInfoList())) {
            return;
        }

        SkuPriceChangeSaleInfoDto saleInfoDto = priceChangeApply.getSaleInfoList().stream().findFirst().get();
        priceChangeApply.setTerminalPrice(saleInfoDto.getTerminalPrice());
        priceChangeApply.setDistributionPrice(saleInfoDto.getDistributionPrice());
        priceChangeApply.setMarketPrice(saleInfoDto.getMarketPrice());
        priceChangeApply.setGroupPrice(saleInfoDto.getGroupPrice());
        priceChangeApply.setElectronicCommercePrice(saleInfoDto.getElectronicCommercePrice());
        priceChangeApply.setResearchTerminalPrice(saleInfoDto.getResearchTerminalPrice());
        priceChangeApply.setMarketPriceChangeReason(saleInfoDto.getMarketPriceChangeReason());
        priceChangeApply.setTerminalPriceChangeReason(saleInfoDto.getTerminalPriceChangeReason());
        priceChangeApply.setDistributionPriceChangeReason(saleInfoDto.getDistributionPriceChangeReason());
        priceChangeApply.setGroupPriceChangeReason(saleInfoDto.getGroupPriceChangeReason());
        priceChangeApply.setElectronicCommercePriceChangeReason(saleInfoDto.getElectronicCommercePriceChangeReason());
        priceChangeApply.setResearchTerminalChangeReason(saleInfoDto.getResearchTerminalChangeReason());
    }

    /**
     * 处理采购价
     *
     * @param purchasePrice
     */
    public String dealWithPurchasePrice(List<BigDecimal> purchasePrice) {

        String middlePrice = null;

        if (CollectionUtils.isEmpty(purchasePrice)) {
            return Strings.EMPTY;
        }

        //如果采购价只有一个，则直接取值
        if (purchasePrice.size() == 1) {
            middlePrice = purchasePrice.get(0).toPlainString() + StringUtils.EMPTY;
        } else {

            BigDecimal min = Collections.min(purchasePrice);
            BigDecimal max = Collections.max(purchasePrice);

            middlePrice = (min.compareTo(max) == 0 ? max.toPlainString() + StringUtils.EMPTY : min.toPlainString() + "-" + max.toPlainString());
        }

        return middlePrice;
    }

    /**
     * 详情查询
     *
     * @param skuPriceChangeApplyId
     * @return
     */
    @Override
    public SkuPriceChangeApplyDto findBasePriceMaintainById(Integer skuPriceChangeApplyId) {

        SkuPriceChangeApplyDto skuPriceChangeApplyDto = new SkuPriceChangeApplyDto();
        try {

            //封装请求参数
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("skuPriceChangeApplyId", skuPriceChangeApplyId);
            String requestJson = JsonUtils.translateToJson(requestMap);


            LOGGER.info("BasePriceMaintainServiceImpl->findBasePriceMaintainById 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASE_PRICEMAINTAIN_DETAIL, requestJson);

            LOGGER.info("BasePriceMaintainServiceImpl->findBasePriceMaintainById 响应:" + resultJsonObj.toString());

            if (!SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                return skuPriceChangeApplyDto;
            }

            Gson gson = new Gson();
            skuPriceChangeApplyDto = gson.fromJson(resultJsonObj.getJSONObject("data").toString(),
                    new TypeToken<SkuPriceChangeApplyDto>() {
                    }.getType());

            List<BigDecimal> purchasePrice = skuPriceChangeApplyDto.getEffectPurchaseList().stream().filter(Objects::nonNull).map(SkuPriceInfoPurchaseDetailDto::getPurchasePrice).collect(Collectors.toList());
            Map<Integer, String> traderIdAndNameMap = new HashMap<>();

            if(!CollectionUtils.isEmpty(purchasePrice)) {
            	skuPriceChangeApplyDto.getEffectPurchaseList().forEach(purchaseInfo -> {
            		String traderName = Objects.nonNull(traderMapper.getTraderByTraderId(purchaseInfo.getTraderId()))?traderMapper.getTraderByTraderId(purchaseInfo.getTraderId()).getTraderName():"";
            		purchaseInfo.setTraderName(traderName);
            		
            		traderIdAndNameMap.put(purchaseInfo.getTraderId(), traderName);
            		
            	});
            }

            skuPriceChangeApplyDto.setMiddlePrice(dealWithPurchasePrice(purchasePrice));

            dealWithSalePrice(skuPriceChangeApplyDto);

            if (CollectionUtils.isNotEmpty(skuPriceChangeApplyDto.getPurchaseInfoList())) {
                for (SkuPriceChangePurchaseInfoDto purchaseInfo : skuPriceChangeApplyDto.getPurchaseInfoList()) {

                    String traderName = traderIdAndNameMap.get(purchaseInfo.getTraderId());
                    if (!StringUtils.isEmpty(traderName)) {
                        purchaseInfo.setTraderName(traderName);
                    } else {
                        purchaseInfo.setTraderName(Objects.nonNull(traderMapper.getTraderByTraderId(purchaseInfo.getTraderId()))?traderMapper.getTraderByTraderId(purchaseInfo.getTraderId()).getTraderName():"");
                    }

                    if (purchaseInfo.getPurchasePrice().compareTo(BigDecimal.ZERO) <= 0
                            || skuPriceChangeApplyDto.getTerminalPrice() == null
                            || skuPriceChangeApplyDto.getTerminalPrice().compareTo(BigDecimal.ZERO) <= 0
                            || skuPriceChangeApplyDto.getDistributionPrice() == null
                            || skuPriceChangeApplyDto.getDistributionPrice().compareTo(BigDecimal.ZERO) <= 0) {
                        purchaseInfo.setDistributionRate("0%");
                        purchaseInfo.setTerminalRate("0%");
                        purchaseInfo.setGroupRate("0%");
                        purchaseInfo.setElectronicCommerceRate("0%");
                        purchaseInfo.setResearchTerminalRate("0%");
                    } else {
                        BigDecimal terminalPriceRate = skuPriceChangeApplyDto.getTerminalPrice().subtract(purchaseInfo.getPurchasePrice())
                                .divide(skuPriceChangeApplyDto.getTerminalPrice(),4,BigDecimal.ROUND_HALF_UP)
                                .multiply(BigDecimal.valueOf(100))
                                .setScale(2,BigDecimal.ROUND_HALF_UP);

                        BigDecimal distributionPrice = skuPriceChangeApplyDto.getDistributionPrice().subtract(purchaseInfo.getPurchasePrice())
                                .divide(skuPriceChangeApplyDto.getDistributionPrice(),4,BigDecimal.ROUND_HALF_UP)
                                .multiply(BigDecimal.valueOf(100))
                                .setScale(2,BigDecimal.ROUND_HALF_UP);

                        purchaseInfo.setDistributionRate(distributionPrice + "%");
                        purchaseInfo.setTerminalRate(terminalPriceRate + "%");

                        if(skuPriceChangeApplyDto.getGroupPrice() != null){
                            BigDecimal groupPrice = skuPriceChangeApplyDto.getGroupPrice().subtract(purchaseInfo.getPurchasePrice())
                                    .divide(skuPriceChangeApplyDto.getGroupPrice(),4,BigDecimal.ROUND_HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                                    .setScale(2,BigDecimal.ROUND_HALF_UP);
                            purchaseInfo.setGroupRate(groupPrice + "%");
                        }

                        if(skuPriceChangeApplyDto.getElectronicCommercePrice() != null){
                            BigDecimal electronicCommercePrice = skuPriceChangeApplyDto.getElectronicCommercePrice().subtract(purchaseInfo.getPurchasePrice())
                                    .divide(skuPriceChangeApplyDto.getElectronicCommercePrice(),4,BigDecimal.ROUND_HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                                    .setScale(2,BigDecimal.ROUND_HALF_UP);
                            purchaseInfo.setElectronicCommerceRate(electronicCommercePrice + "%");
                        }

                        if(skuPriceChangeApplyDto.getResearchTerminalPrice() != null){
                            BigDecimal researchTerminalPrice = skuPriceChangeApplyDto.getResearchTerminalPrice().subtract(purchaseInfo.getPurchasePrice())
                                    .divide(skuPriceChangeApplyDto.getResearchTerminalPrice(),4,BigDecimal.ROUND_HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                                    .setScale(2,BigDecimal.ROUND_HALF_UP);
                            purchaseInfo.setResearchTerminalRate(researchTerminalPrice + "%");
                        }

                    }

                }
            }

            if (CollectionUtils.isNotEmpty(skuPriceChangeApplyDto.getEffectPurchaseList())) {
                for (SkuPriceInfoPurchaseDetailDto purchaseInfo : skuPriceChangeApplyDto.getEffectPurchaseList()) {
                    if (purchaseInfo.getPurchasePrice().compareTo(BigDecimal.ZERO) <= 0
                            || skuPriceChangeApplyDto.getTerminalPrice() == null
                            || skuPriceChangeApplyDto.getTerminalPrice().compareTo(BigDecimal.ZERO) <= 0
                            || skuPriceChangeApplyDto.getDistributionPrice() == null
                            || skuPriceChangeApplyDto.getDistributionPrice().compareTo(BigDecimal.ZERO) <= 0) {
                        // 此处是因为: 终端价和经销价是必填的，集团价是非必填的，因此只要终端价和经销价为0，那么集团和电商、科研终端必定为0
                        purchaseInfo.setDistributionRate("0%");
                        purchaseInfo.setTerminalRate("0%");
                        purchaseInfo.setGroupRate("0%");
                        purchaseInfo.setElectronicCommerceRate("0%");
                        purchaseInfo.setDistributionRate("0%");
                    } else {
                        BigDecimal terminalPriceRate = skuPriceChangeApplyDto.getTerminalPrice().subtract(purchaseInfo.getPurchasePrice())
                                .divide(skuPriceChangeApplyDto.getTerminalPrice(),4,BigDecimal.ROUND_HALF_UP)
                                .multiply(BigDecimal.valueOf(100))
                                .setScale(2,BigDecimal.ROUND_HALF_UP);

                        BigDecimal distributionPrice = skuPriceChangeApplyDto.getDistributionPrice().subtract(purchaseInfo.getPurchasePrice())
                                .divide(skuPriceChangeApplyDto.getDistributionPrice(),4,BigDecimal.ROUND_HALF_UP)
                                .multiply(BigDecimal.valueOf(100))
                                .setScale(2,BigDecimal.ROUND_HALF_UP);

                        if(skuPriceChangeApplyDto.getGroupPrice() != null){
                            BigDecimal groupPrice = skuPriceChangeApplyDto.getGroupPrice().subtract(purchaseInfo.getPurchasePrice())
                                    .divide(skuPriceChangeApplyDto.getGroupPrice(),4,BigDecimal.ROUND_HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                                    .setScale(2,BigDecimal.ROUND_HALF_UP);
                            purchaseInfo.setGroupRate(groupPrice + "%");
                        }

                        if (skuPriceChangeApplyDto.getElectronicCommercePrice() != null) {
                            BigDecimal electronicCommercePrice = skuPriceChangeApplyDto.getElectronicCommercePrice().subtract(purchaseInfo.getPurchasePrice())
                                    .divide(skuPriceChangeApplyDto.getElectronicCommercePrice(), 4, BigDecimal.ROUND_HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP);
                            purchaseInfo.setElectronicCommerceRate(electronicCommercePrice + "%");
                        }

                        if (skuPriceChangeApplyDto.getResearchTerminalPrice() != null) {
                            BigDecimal researchTerminalPrice = skuPriceChangeApplyDto.getResearchTerminalPrice().subtract(purchaseInfo.getPurchasePrice())
                                    .divide(skuPriceChangeApplyDto.getResearchTerminalPrice(), 4, BigDecimal.ROUND_HALF_UP)
                                    .multiply(BigDecimal.valueOf(100))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP);
                            purchaseInfo.setResearchTerminalRate(researchTerminalPrice + "%");
                        }

                        purchaseInfo.setDistributionRate(distributionPrice + "%");
                        purchaseInfo.setTerminalRate(terminalPriceRate + "%");

                    }
                }
            }

            //查询商品单位
            CoreSkuGenerate sku = coreSkuGenerateMapper.selectBySkuNo(skuPriceChangeApplyDto.getSkuNo());
            Unit unit = unitMapper.selectByPrimaryKey(sku.getBaseUnitId());
            skuPriceChangeApplyDto.setUnitName(org.springframework.util.StringUtils.isEmpty(unit) ? "" : unit.getUnitName());

            if (ErpConst.TWO.equals(skuPriceChangeApplyDto.getVerifyStatus()) || ErpConst.THREE.equals(skuPriceChangeApplyDto.getVerifyStatus())) {
                // 审核通过或不通过时，展示最新一次已生效的
                if (CollectionUtils.isNotEmpty(skuPriceChangeApplyDto.getEffectSaleInfoList())) {
                    SkuPriceInfoSaleDetailDto saleDetailDto = skuPriceChangeApplyDto.getEffectSaleInfoList().get(0);
                    skuPriceChangeApplyDto.setSaleContainsFeeStr(this.checkIsContainsFee(saleDetailDto.getSaleContainsFee()));
                    skuPriceChangeApplyDto.setSaleContainsFee(saleDetailDto.getSaleContainsFee());
                    skuPriceChangeApplyDto.setPurchaseContainsFeeStr(this.checkIsContainsFee(saleDetailDto.getPurchaseContainsFee()));
                    skuPriceChangeApplyDto.setPurchaseContainsFee(saleDetailDto.getPurchaseContainsFee());
                }
            } else {
                // 审核中展示变更申请里的，待完善的，则不展示
                if (CollectionUtils.isNotEmpty(skuPriceChangeApplyDto.getSaleInfoList())) {
                    SkuPriceChangeSaleInfoDto priceChangeSaleInfoDto = skuPriceChangeApplyDto.getSaleInfoList().get(0);
                    skuPriceChangeApplyDto.setSaleContainsFeeStr(this.checkIsContainsFee(priceChangeSaleInfoDto.getSaleContainsFee()));
                    skuPriceChangeApplyDto.setPurchaseContainsFeeStr(this.checkIsContainsFee(priceChangeSaleInfoDto.getPurchaseContainsFee()));
                    skuPriceChangeApplyDto.setSaleContainsFee(priceChangeSaleInfoDto.getSaleContainsFee());
                    skuPriceChangeApplyDto.setPurchaseContainsFee(priceChangeSaleInfoDto.getPurchaseContainsFee());
                }
            }
        } catch (IOException e) {
            LOGGER.error("基础信息核价列表详情查询", e);
        }
        return skuPriceChangeApplyDto;
    }

    /**
     * 判断是否包含运费
     *
     * @param containsFee
     * @return
     */
    private String checkIsContainsFee(Integer containsFee) {
        return ErpConst.ONE.equals(containsFee) ? "含运费" : (ErpConst.TWO.equals(containsFee) ? "不含运费" : "未维护");
    }

    @Override
    public List<PurchaseHistoryDto> findPurchaseHistoryListBySkuId(Integer skuId) {

        List<PurchaseHistoryDto> purchaseHistoryList = null;
        try {
            //封装请求参数
            Map<String, Object> requestMap = new HashMap<String, Object>();
            requestMap.put("skuId", skuId);
            String requestJson = JsonUtils.translateToJson(requestMap);


            LOGGER.info("BasePriceMaintainServiceImpl->findPurchaseHistoryListBySkuId 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASE_PRICEMAINTAIN_EFFECTPURCHASELIST, requestJson);

            LOGGER.info("BasePriceMaintainServiceImpl->findPurchaseHistoryListBySkuId 响应:" + resultJsonObj.toString());

            if (!SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                return new ArrayList<>();
            }

            Gson gson = new Gson();

            purchaseHistoryList = gson.fromJson(resultJsonObj.getJSONObject("data").get("purchaseHistoryList").toString(),
                    new TypeToken<List<PurchaseHistoryDto>>() {
                    }.getType());

            purchaseHistoryList.stream().forEach(purchaseHistory -> {
                purchaseHistory.setSupplyName(traderMapper.getTraderByTraderId(purchaseHistory.getSupplyId()).getTraderName());
            });

        } catch (IOException e) {
            LOGGER.error("基础信息核价列表详情查询", e);
        }
        return purchaseHistoryList;
    }


    @Override
    public boolean informPriceChangeApply(EditBasePriceDto editBasePriceDto) {
        try {
            //封装请求参数
            String requestJson = JsonUtils.translateToJson(editBasePriceDto);

            LOGGER.info("BasePriceMaintainServiceImpl->informPriceChangeApply 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASE_PRICEMAINTAIN_INFORMPRICECHANGEAPPLY, requestJson);

            LOGGER.info("BasePriceMaintainServiceImpl->informPriceChangeApply 响应:" + resultJsonObj.toString());

            if (SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                editBasePriceDto.setSecondChangePrice("1".equals(resultJsonObj.get("data").toString()) ? true : false);
                return true;
            }

        } catch (IOException e) {
            LOGGER.error("基础信息核价列表详情查询", e);
        }
        return false;
    }

    @Override
    public SkuPriceChangeApplyDto getBasicPriceChangeInfoById(Integer skuPriceChangeApplyId) {
        SkuPriceChangeApplyDto skuPriceChangeApplyDto = new SkuPriceChangeApplyDto();
        try {

            //封装请求参数
            Map<String, Object> requestMap = new HashMap<String, Object>();
            requestMap.put("skuPriceChangeApplyId", skuPriceChangeApplyId);
            String requestJson = JsonUtils.translateToJson(requestMap);

            LOGGER.info("BasePriceMaintainServiceImpl->getBasicPriceChangeInfoById 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASICPRICECHANGEINFO_DETAIL, requestJson);

            LOGGER.info("BasePriceMaintainServiceImpl->getBasicPriceChangeInfoById 响应:" + resultJsonObj.toString());

            if (!SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                return skuPriceChangeApplyDto;
            }

            Gson gson = new Gson();
            skuPriceChangeApplyDto = gson.fromJson(resultJsonObj.getJSONObject("data").toString(),
                    new TypeToken<SkuPriceChangeApplyDto>() {
                    }.getType());

        } catch (IOException e) {
            LOGGER.error("获取核价基本信息失败", e);
        }
        return skuPriceChangeApplyDto;
    }

    @Override
    public boolean updatePriceChangeStatus(Integer skuPriceChangeApplyId, int verifyStatus) {

        try {

            //封装请求参数
            Map<String, Object> requestMap = new HashMap<String, Object>();
            requestMap.put("skuPriceChangeApplyId", skuPriceChangeApplyId);
            requestMap.put("verifyStatus", verifyStatus);
            String requestJson = JsonUtils.translateToJson(requestMap);

            LOGGER.info("BasePriceMaintainServiceImpl->updatePriceChangeStatus 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASE_PRICEMAINTAIN_UPDATE_PRICECHANGE_STATUS, requestJson);

            LOGGER.info("BasePriceMaintainServiceImpl->updatePriceChangeStatus 响应:" + resultJsonObj.toString());

            if (SUCCESS_CODE.equals(resultJsonObj.get("code"))) {

                if(verifyStatus == VerifyStatusEnum.Approved.getValue()){
                    //新增修改记录
                    Gson gson = new Gson();
                    SkuPriceModifyRecord skuPriceModifyRecord = gson.fromJson(resultJsonObj.getJSONObject("data").toString(),
                            new TypeToken<SkuPriceModifyRecord>() {}.getType());
                    if(isHavePriceChange(skuPriceModifyRecord)){
                        skuPriceModifyRecord.setUpdater(2);
                        //默认更新人为njadmin
                        skuPriceModifyRecord.setCreator(2);
                        skuPriceModifyRecord.setPriceModTime(System.currentTimeMillis());
                        skuPriceModifyRecord.setAddTime(System.currentTimeMillis());
                        skuPriceModifyRecord.setUpdateTime(System.currentTimeMillis());
                        skuPriceModifyRecordMapper.insertSelective(skuPriceModifyRecord);
                    }
                }
                return true;
            }

        } catch (Exception e) {
            LOGGER.error("获取核价基本信息失败", e);
        }
        return false;
    }

//    private boolean isHavePriceChange(SkuPriceModifyRecord skuPriceModifyRecord){
//        if(null == skuPriceModifyRecord.getBeforeModDistributionPrice()
//                || null == skuPriceModifyRecord.getBeforeModGroupPrice()
//                || null == skuPriceModifyRecord.getBeforeModMarketPrice()
//                || null == skuPriceModifyRecord.getBeforeModTerminalPrice()
//                || null == skuPriceModifyRecord.getBeforeModPurchaseCosts()
//        || null == skuPriceModifyRecord.getBeforeModElectronicCommercePrice()
//        || null == skuPriceModifyRecord.getBeforeModResearchTerminalPrice()){
//            return true;
//        }
//        // 这五个字段为空，则不进行insert
//        if (null == skuPriceModifyRecord.getAfterModDistributionPrice()
//                || null == skuPriceModifyRecord.getAfterModGroupPrice()
//                || null == skuPriceModifyRecord.getAfterModMarketPrice()
//                || null == skuPriceModifyRecord.getAfterModTerminalPrice()
//                || null == skuPriceModifyRecord.getAfterModPurchaseCosts()
//                || null == skuPriceModifyRecord.getAfterModElectronicCommercePrice()
//                || null == skuPriceModifyRecord.getAfterModResearchTerminalPrice() ) {
//            return false;
//        }
//        return  !(skuPriceModifyRecord.getAfterModDistributionPrice().compareTo(skuPriceModifyRecord.getBeforeModDistributionPrice()) == 0
//                && skuPriceModifyRecord.getAfterModGroupPrice().compareTo(skuPriceModifyRecord.getBeforeModGroupPrice()) == 0
//                && skuPriceModifyRecord.getAfterModMarketPrice().compareTo(skuPriceModifyRecord.getBeforeModMarketPrice()) == 0
//                && skuPriceModifyRecord.getAfterModTerminalPrice().compareTo(skuPriceModifyRecord.getBeforeModTerminalPrice()) == 0
//                && skuPriceModifyRecord.getAfterModPurchaseCosts().compareTo(skuPriceModifyRecord.getBeforeModPurchaseCosts()) == 0
//                && skuPriceModifyRecord.getAfterModElectronicCommercePrice().compareTo(skuPriceModifyRecord.getBeforeModElectronicCommercePrice()) == 0
//                && skuPriceModifyRecord.getAfterModResearchTerminalPrice().compareTo(skuPriceModifyRecord.getBeforeModResearchTerminalPrice()) == 0
//        );
//    }


    private boolean isHavePriceChange(SkuPriceModifyRecord skuPriceModifyRecord) {
        return isFieldChanged(skuPriceModifyRecord.getBeforeModDistributionPrice(), skuPriceModifyRecord.getAfterModDistributionPrice()) ||
                isFieldChanged(skuPriceModifyRecord.getBeforeModGroupPrice(), skuPriceModifyRecord.getAfterModGroupPrice()) ||
                isFieldChanged(skuPriceModifyRecord.getBeforeModMarketPrice(), skuPriceModifyRecord.getAfterModMarketPrice()) ||
                isFieldChanged(skuPriceModifyRecord.getBeforeModTerminalPrice(), skuPriceModifyRecord.getAfterModTerminalPrice()) ||
                isFieldChanged(skuPriceModifyRecord.getBeforeModPurchaseCosts(), skuPriceModifyRecord.getAfterModPurchaseCosts()) ||
                isFieldChanged(skuPriceModifyRecord.getBeforeModElectronicCommercePrice(), skuPriceModifyRecord.getAfterModElectronicCommercePrice()) ||
                isFieldChanged(skuPriceModifyRecord.getBeforeModResearchTerminalPrice(), skuPriceModifyRecord.getAfterModResearchTerminalPrice());
    }

    private boolean isFieldChanged(BigDecimal before, BigDecimal after) {
        // 处理 null 值场景
        if (before == null && after == null) return false; // 都为 null 视为无变化
        if (before == null || after == null) return true;  // 一方为 null 视为变化
        return !before.equals(after); // 值不同视为变化
    }

    /**
     * @param priceInfoUploadDtoList
     * @return核价信息
     */
    @Override
    public List<PriceInfoUploadResponseDto> batchAddPriceInfo(List<PriceInfoUploadDto> priceInfoUploadDtoList) {

        List<PriceInfoUploadResponseDto> skuPriceChangeApplyIdList = new ArrayList<>();

        try {

            //封装请求参数
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("priceInfoUploadDtoList", priceInfoUploadDtoList);
            String requestJson = JsonUtils.translateToJson(requestMap);

            LOGGER.info("BasePriceMaintainServiceImpl->batchAddPriceInfo 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASE_PRICEMAINTAIN_BATCHADDPRICEINFO, requestJson);

            LOGGER.info("BasePriceMaintainServiceImpl->batchAddPriceInfo 响应:" + resultJsonObj.toString());

            if (!SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                return skuPriceChangeApplyIdList;
            }

            Gson gson = new Gson();
            skuPriceChangeApplyIdList = gson.fromJson(resultJsonObj.getJSONArray("data").toString(),
                    new TypeToken<List<PriceInfoUploadResponseDto>>() {
                    }.getType());


        } catch (IOException e) {
            LOGGER.error("批量上传核价信息失败", e);
        }

        //批量核价时解除商品待办
        Set<String> goodsNoPriceChangedList = priceInfoUploadDtoList.stream().map(priceInfo->{
            if (priceInfo.getPurchasePrice() != null) {
                return priceInfo.getSkuNo();
            }
            return null;
        }).filter(StringUtils::isNotEmpty).collect(Collectors.toSet());

        for (String skuNo : goodsNoPriceChangedList) {
            goodsPriceTodoHandler.finish(skuNo);
        }

        return skuPriceChangeApplyIdList;
    }

    /**
     * 批量获取已经核价的skuid集合
     *
     * @return
     */
    @Override
    public List<Long> batchGetSkuPricing() {

        List<Long> skuPriceChangeApplyIdList = new ArrayList<>();

        try {

            LOGGER.info("BasePriceMaintainServiceImpl->batchGetSkuPricing 请求");
            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASE_PRICEMAINTAIN_BATCHGETSKUPRICING, "");
            LOGGER.info("BasePriceMaintainServiceImpl->batchGetSkuPricing 响应:" + resultJsonObj.toString());

            if (!SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                return skuPriceChangeApplyIdList;
            }

            Gson gson = new Gson();
            skuPriceChangeApplyIdList = gson.fromJson(resultJsonObj.getJSONArray("data").toString(),
                    new TypeToken<List<Long>>() {
                    }.getType());


        } catch (Exception e) {
            LOGGER.error("批量获取已经核价的skuid集合", e);
        }

        return skuPriceChangeApplyIdList;
    }

    @Override
    public boolean updatePriceChangeAuditorByIds(List<Long> skuChangeApplyIdList, List<Integer> userIds) {

        try {

            UpdatePriceChangeAuditorDto updatePriceChangeAuditorDto = new UpdatePriceChangeAuditorDto();
            updatePriceChangeAuditorDto.getSkuPriceChangeIds().addAll(skuChangeApplyIdList);
            updatePriceChangeAuditorDto.setUserIds(userIds);


            //封装请求参数
            String requestJson = JsonUtils.translateToJson(updatePriceChangeAuditorDto);

            LOGGER.info("BasePriceMaintainServiceImpl->updatePriceChangeAuditorByIds 请求参数:" + requestJson);
            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASE_PRICEMAINTAIN_UPDATEPRICECHANGEAUDITORBYIDS, requestJson);
            LOGGER.info("BasePriceMaintainServiceImpl->updatePriceChangeAuditorByIds 响应:" + resultJsonObj.toString());

            if (SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                return true;
            }

        } catch (IOException e) {
            LOGGER.error("updatePriceChangeAuditorByIds error", e);
        }
        return false;
    }

    @Override
    public Long addPriceChange(String skuNo) {

        Long skuPriceChangeApplyId = null;

        try {
            //封装请求参数
            Map<String, Object> requestMap = new HashMap<String, Object>();
            requestMap.put("skuNo", skuNo);

            String requestJson = JsonUtils.translateToJson(requestMap);

            LOGGER.info("BasePriceMaintainServiceImpl->addPriceChange 请求参数:" + requestJson);
            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASE_PRICEMAINTAIN_ADDPRICECHANGE, requestJson);
            LOGGER.info("BasePriceMaintainServiceImpl->addPriceChange 响应:" + resultJsonObj.toString());

            if (SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                skuPriceChangeApplyId = resultJsonObj.getLong("data");
            }

        } catch (Exception e) {
            LOGGER.error("addPriceChange error", e);
        }

        return skuPriceChangeApplyId;
    }

    @Override
    public boolean enablePrice(Integer skuPriceChangeApplyId) {
        try {


            Map<String,Object> requestMap=new HashMap<String, Object>();
            requestMap.put("id",skuPriceChangeApplyId);

            String requestJson = JsonUtils.translateToJson(requestMap);

            LOGGER.info("BasePriceMaintainServiceImpl->enablePrice 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASE_PRICEMAINTAIN_ENDABLEPRICE,requestJson);

            LOGGER.info("BasePriceMaintainServiceImpl->enablePrice 响应:" + resultJsonObj.toString());

            if(SUCCESS_CODE.equals(resultJsonObj.get("code")) ){
                return true;
            }

        } catch (IOException e) {
            LOGGER.error("启用核价数据失败",e);
        }
        return false;
    }

    @Override
    public boolean disablePrice(Integer skuPriceChangeApplyId,String disableReason) {
        try {

            Map<String,Object> requestMap=new HashMap<String, Object>();
            requestMap.put("id",skuPriceChangeApplyId);
            requestMap.put("disableReason",disableReason);

            String requestJson = JsonUtils.translateToJson(requestMap);

            LOGGER.info("BasePriceMaintainServiceImpl->disablePrice 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASE_PRICEMAINTAIN_DISABLEPRICE,requestJson);

            LOGGER.info("BasePriceMaintainServiceImpl->disablePrice 响应:" + resultJsonObj.toString());

            if(SUCCESS_CODE.equals(resultJsonObj.get("code")) ){
                return true;
            }

        } catch (IOException e) {
            LOGGER.error("禁用核价数据失败",e);
        }
        return false;
    }

    /**
     * 更新商城可见状态
     * 
     * @param skuPriceChangeApplyId 价格变更申请ID
     * @param isVisible 可见状态（1-可见，0-不可见）
     * @return 更新是否成功
     */
    @Override
    public boolean updateVdVisibility(Integer skuPriceChangeApplyId, Integer isVdVisible) {
        try {
            // 封装请求参数
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("skuPriceChangeApplyId", skuPriceChangeApplyId);
            requestMap.put("isVdVisible", isVdVisible);
            String requestJson = JsonUtils.translateToJson(requestMap);
            LOGGER.info("BasePriceMaintainServiceImpl->updateVdVisibility 请求参数:" + requestJson);
            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BASE_PRICEMAINTAIN_UPDATE_VD_VISIBILITY, requestJson);
            LOGGER.info("BasePriceMaintainServiceImpl->updateVdVisibility 响应:" + resultJsonObj.toString());
            return SUCCESS_CODE.equals(resultJsonObj.get("code"));
        } catch (IOException e) {
            LOGGER.error("BasePriceMaintainServiceImpl->updateVdVisibility", e);
            return false;
        }
    }

    @Override
    public PageResultDto findPricedSkuPageQuery(PricedSkuPageQueryDto pricedSkuPageQueryDto) {

        PageResultDto pageResultDto = new PageResultDto();

        try {
            //封装请求参数
            String requestJson = JsonUtils.translateToJson(pricedSkuPageQueryDto);


            LOGGER.info("BasePriceMaintainServiceImpl->findPricedSkuPageQuery 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + FIND_PRICEDSKU_PAGEQUERY, requestJson);

            LOGGER.info("BasePriceMaintainServiceImpl->findPricedSkuPageQuery 响应:" + resultJsonObj.toString());

            if (!SUCCESS_CODE.equals(resultJsonObj.get("code"))) {
                return pageResultDto;
            }

            Gson gson = new Gson();

            List<PriceSkuGroupPriceDto> pricedSkuList = gson.fromJson(resultJsonObj.getJSONObject("data").get("pricedSkuList").toString(),
                    new TypeToken<List<PriceSkuGroupPriceDto>>() {
                    }.getType());

            //获取总记录数
            String totleRecord = resultJsonObj.getJSONObject("data").get("totalRecord").toString();
            pageResultDto.setDatas(pricedSkuList);
            pageResultDto.setTotalRecords(Integer.valueOf(totleRecord));

        } catch (Exception e) {
            LOGGER.error("已核价sku列表分页查询失败", e);
        }
        return pageResultDto;
    }

    @Override
    public void batchUpdateGroupPrice(List<PriceSkuGroupPriceDto> pricedSkuList) {
        try {

            String requestJson = JsonUtils.translateToJson(pricedSkuList);

            LOGGER.info("batchAddContractPriceDtoList->批量更新集团价请求:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BATCH_UPDATE_GROUPPRICE,requestJson);

            LOGGER.info("batchAddContractPriceDtoList->批量更新集团价请求:" + resultJsonObj);

        } catch (Exception e) {
            LOGGER.error("调用价格中心服务失败:",e);
        }
    }


}
