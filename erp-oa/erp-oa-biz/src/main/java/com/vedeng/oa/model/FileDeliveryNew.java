package com.vedeng.oa.model;

import java.math.BigDecimal;
import java.util.Date;

public class FileDeliveryNew {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.FILE_DELIVERY_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer fileDeliveryId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.FILE_DELIVERY_NO
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String fileDeliveryNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.APPLY_USER_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer applyUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.APPLY_ORG_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String applyOrgName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.APPLY_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Date applyTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.VERIFY_STATUS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer verifyStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.DELIVERY_STATUS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer deliveryStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.DELIVERY_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Date deliveryTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.LOGISTICS_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer logisticsName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.EXPRESS_NOS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String expressNos;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.DELIVERY_TYPE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer deliveryType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.DELIVERY_DEPT
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer deliveryDept;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.DELIVERY_TOTAL_WEIGHT
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private BigDecimal deliveryTotalWeight;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.DELIVERY_PROD_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String deliveryProdName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.DELIVERY_EXPRESS_NUM
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer deliveryExpressNum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.CONTENT
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String content;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.IS_CLOSED
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer isClosed;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.CLOSED_COMMENTS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String closedComments;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.IS_DELETED
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.ADD_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Date addTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.CREATOR
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.MOD_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Date modTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.UPDATER
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer updater;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.UPDATE_REMARK
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String updateRemark;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.SEND_USER_TYPE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private Integer sendUserType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FILE_DELIVERY_NEW.TRADER_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    private String traderName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.FILE_DELIVERY_ID
     *
     * @return the value of T_FILE_DELIVERY_NEW.FILE_DELIVERY_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getFileDeliveryId() {
        return fileDeliveryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.FILE_DELIVERY_ID
     *
     * @param fileDeliveryId the value for T_FILE_DELIVERY_NEW.FILE_DELIVERY_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setFileDeliveryId(Integer fileDeliveryId) {
        this.fileDeliveryId = fileDeliveryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.FILE_DELIVERY_NO
     *
     * @return the value of T_FILE_DELIVERY_NEW.FILE_DELIVERY_NO
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getFileDeliveryNo() {
        return fileDeliveryNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.FILE_DELIVERY_NO
     *
     * @param fileDeliveryNo the value for T_FILE_DELIVERY_NEW.FILE_DELIVERY_NO
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setFileDeliveryNo(String fileDeliveryNo) {
        this.fileDeliveryNo = fileDeliveryNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.APPLY_USER_ID
     *
     * @return the value of T_FILE_DELIVERY_NEW.APPLY_USER_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getApplyUserId() {
        return applyUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.APPLY_USER_ID
     *
     * @param applyUserId the value for T_FILE_DELIVERY_NEW.APPLY_USER_ID
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setApplyUserId(Integer applyUserId) {
        this.applyUserId = applyUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.APPLY_ORG_NAME
     *
     * @return the value of T_FILE_DELIVERY_NEW.APPLY_ORG_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getApplyOrgName() {
        return applyOrgName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.APPLY_ORG_NAME
     *
     * @param applyOrgName the value for T_FILE_DELIVERY_NEW.APPLY_ORG_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setApplyOrgName(String applyOrgName) {
        this.applyOrgName = applyOrgName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.APPLY_TIME
     *
     * @return the value of T_FILE_DELIVERY_NEW.APPLY_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Date getApplyTime() {
        return applyTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.APPLY_TIME
     *
     * @param applyTime the value for T_FILE_DELIVERY_NEW.APPLY_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.VERIFY_STATUS
     *
     * @return the value of T_FILE_DELIVERY_NEW.VERIFY_STATUS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getVerifyStatus() {
        return verifyStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.VERIFY_STATUS
     *
     * @param verifyStatus the value for T_FILE_DELIVERY_NEW.VERIFY_STATUS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setVerifyStatus(Integer verifyStatus) {
        this.verifyStatus = verifyStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.DELIVERY_STATUS
     *
     * @return the value of T_FILE_DELIVERY_NEW.DELIVERY_STATUS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.DELIVERY_STATUS
     *
     * @param deliveryStatus the value for T_FILE_DELIVERY_NEW.DELIVERY_STATUS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.DELIVERY_TIME
     *
     * @return the value of T_FILE_DELIVERY_NEW.DELIVERY_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Date getDeliveryTime() {
        return deliveryTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.DELIVERY_TIME
     *
     * @param deliveryTime the value for T_FILE_DELIVERY_NEW.DELIVERY_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.LOGISTICS_NAME
     *
     * @return the value of T_FILE_DELIVERY_NEW.LOGISTICS_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getLogisticsName() {
        return logisticsName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.LOGISTICS_NAME
     *
     * @param logisticsName the value for T_FILE_DELIVERY_NEW.LOGISTICS_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setLogisticsName(Integer logisticsName) {
        this.logisticsName = logisticsName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.EXPRESS_NOS
     *
     * @return the value of T_FILE_DELIVERY_NEW.EXPRESS_NOS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getExpressNos() {
        return expressNos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.EXPRESS_NOS
     *
     * @param expressNos the value for T_FILE_DELIVERY_NEW.EXPRESS_NOS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setExpressNos(String expressNos) {
        this.expressNos = expressNos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.DELIVERY_TYPE
     *
     * @return the value of T_FILE_DELIVERY_NEW.DELIVERY_TYPE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getDeliveryType() {
        return deliveryType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.DELIVERY_TYPE
     *
     * @param deliveryType the value for T_FILE_DELIVERY_NEW.DELIVERY_TYPE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setDeliveryType(Integer deliveryType) {
        this.deliveryType = deliveryType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.DELIVERY_DEPT
     *
     * @return the value of T_FILE_DELIVERY_NEW.DELIVERY_DEPT
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getDeliveryDept() {
        return deliveryDept;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.DELIVERY_DEPT
     *
     * @param deliveryDept the value for T_FILE_DELIVERY_NEW.DELIVERY_DEPT
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setDeliveryDept(Integer deliveryDept) {
        this.deliveryDept = deliveryDept;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.DELIVERY_TOTAL_WEIGHT
     *
     * @return the value of T_FILE_DELIVERY_NEW.DELIVERY_TOTAL_WEIGHT
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public BigDecimal getDeliveryTotalWeight() {
        return deliveryTotalWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.DELIVERY_TOTAL_WEIGHT
     *
     * @param deliveryTotalWeight the value for T_FILE_DELIVERY_NEW.DELIVERY_TOTAL_WEIGHT
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setDeliveryTotalWeight(BigDecimal deliveryTotalWeight) {
        this.deliveryTotalWeight = deliveryTotalWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.DELIVERY_PROD_NAME
     *
     * @return the value of T_FILE_DELIVERY_NEW.DELIVERY_PROD_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getDeliveryProdName() {
        return deliveryProdName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.DELIVERY_PROD_NAME
     *
     * @param deliveryProdName the value for T_FILE_DELIVERY_NEW.DELIVERY_PROD_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setDeliveryProdName(String deliveryProdName) {
        this.deliveryProdName = deliveryProdName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.DELIVERY_EXPRESS_NUM
     *
     * @return the value of T_FILE_DELIVERY_NEW.DELIVERY_EXPRESS_NUM
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getDeliveryExpressNum() {
        return deliveryExpressNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.DELIVERY_EXPRESS_NUM
     *
     * @param deliveryExpressNum the value for T_FILE_DELIVERY_NEW.DELIVERY_EXPRESS_NUM
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setDeliveryExpressNum(Integer deliveryExpressNum) {
        this.deliveryExpressNum = deliveryExpressNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.CONTENT
     *
     * @return the value of T_FILE_DELIVERY_NEW.CONTENT
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.CONTENT
     *
     * @param content the value for T_FILE_DELIVERY_NEW.CONTENT
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.IS_CLOSED
     *
     * @return the value of T_FILE_DELIVERY_NEW.IS_CLOSED
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getIsClosed() {
        return isClosed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.IS_CLOSED
     *
     * @param isClosed the value for T_FILE_DELIVERY_NEW.IS_CLOSED
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setIsClosed(Integer isClosed) {
        this.isClosed = isClosed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.CLOSED_COMMENTS
     *
     * @return the value of T_FILE_DELIVERY_NEW.CLOSED_COMMENTS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getClosedComments() {
        return closedComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.CLOSED_COMMENTS
     *
     * @param closedComments the value for T_FILE_DELIVERY_NEW.CLOSED_COMMENTS
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setClosedComments(String closedComments) {
        this.closedComments = closedComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.IS_DELETED
     *
     * @return the value of T_FILE_DELIVERY_NEW.IS_DELETED
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.IS_DELETED
     *
     * @param isDeleted the value for T_FILE_DELIVERY_NEW.IS_DELETED
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.ADD_TIME
     *
     * @return the value of T_FILE_DELIVERY_NEW.ADD_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.ADD_TIME
     *
     * @param addTime the value for T_FILE_DELIVERY_NEW.ADD_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.CREATOR
     *
     * @return the value of T_FILE_DELIVERY_NEW.CREATOR
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.CREATOR
     *
     * @param creator the value for T_FILE_DELIVERY_NEW.CREATOR
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.MOD_TIME
     *
     * @return the value of T_FILE_DELIVERY_NEW.MOD_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Date getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.MOD_TIME
     *
     * @param modTime the value for T_FILE_DELIVERY_NEW.MOD_TIME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.UPDATER
     *
     * @return the value of T_FILE_DELIVERY_NEW.UPDATER
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.UPDATER
     *
     * @param updater the value for T_FILE_DELIVERY_NEW.UPDATER
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.UPDATE_REMARK
     *
     * @return the value of T_FILE_DELIVERY_NEW.UPDATE_REMARK
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getUpdateRemark() {
        return updateRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.UPDATE_REMARK
     *
     * @param updateRemark the value for T_FILE_DELIVERY_NEW.UPDATE_REMARK
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setUpdateRemark(String updateRemark) {
        this.updateRemark = updateRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.SEND_USER_TYPE
     *
     * @return the value of T_FILE_DELIVERY_NEW.SEND_USER_TYPE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Integer getSendUserType() {
        return sendUserType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.SEND_USER_TYPE
     *
     * @param sendUserType the value for T_FILE_DELIVERY_NEW.SEND_USER_TYPE
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setSendUserType(Integer sendUserType) {
        this.sendUserType = sendUserType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FILE_DELIVERY_NEW.TRADER_NAME
     *
     * @return the value of T_FILE_DELIVERY_NEW.TRADER_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getTraderName() {
        return traderName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FILE_DELIVERY_NEW.TRADER_NAME
     *
     * @param traderName the value for T_FILE_DELIVERY_NEW.TRADER_NAME
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }
}