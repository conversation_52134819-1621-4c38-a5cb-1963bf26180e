package com.vedeng.erp.aftersale.mapper;

import com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesInvoiceEntity;
import com.vedeng.erp.aftersale.dto.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface ExpenseAfterSalesInvoiceMapper {
    /**
     * delete by primary key
     *
     * @param expenseAfterSalesInvoiceId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long expenseAfterSalesInvoiceId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ExpenseAfterSalesInvoiceEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ExpenseAfterSalesInvoiceEntity record);

    /**
     * select by primary key
     *
     * @param expenseAfterSalesInvoiceId primary key
     * @return object by primary key
     */
    ExpenseAfterSalesInvoiceEntity selectByPrimaryKey(Long expenseAfterSalesInvoiceId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ExpenseAfterSalesInvoiceEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ExpenseAfterSalesInvoiceEntity record);

    /**
     * 批量插入
     *
     * @param list List<ExpenseAfterSalesInvoiceEntity>
     */
    void batchPartInsertSelective(List<ExpenseAfterSalesInvoiceEntity> list);

    /**
     * 批量更新
     *
     * @param list List<ExpenseAfterSalesInvoiceEntity>
     */
    void updateBatchSelective(List<ExpenseAfterSalesInvoiceEntity> list);


    /**
     * 通过采购费用售后单id查询退票发票信息
     *
     * @param expenseAfterSalesId 采购费用售后单id
     * @return
     */
    List<ExpenseAfterSalesInvoiceEntity> findByExpenseAfterSalesId(@Param("expenseAfterSalesId") Long expenseAfterSalesId);


    /**
     * 根据查询
     *
     * @param query
     * @return
     */
    List<ReturnInvoiceGoodsDto> getReturnInvoiceGoodsData(ExpenseAfterSalesViewDto query);

    /**
     * 根据售后单id、费用单商品id、发票号、发票代码
     *
     * @param query ExpenseAfterSalesInvoiceEntity
     * @return ExpenseAfterSalesInvoiceEntity
     */
    ExpenseAfterSalesInvoiceEntity queryReturnInvoiceInfo(ExpenseAfterSalesInvoiceEntity query);

    /**
     * 逻辑删除费用售后单下的 所有退票信息
     *
     * @param delete 采购费用售后单
     */
    void deleteInvoiceByExpenseAfterSalesId(ExpenseAfterSalesInvoiceEntity delete);

    /**
     * 查询 售后未完成的待录票的记录
     *
     * @param list 蓝票id
     * @return 待录票的记录
     */
    List<ExpenseAfterSalesInvoiceEntity> queryNotSuccessData(List<Integer> list);

    /**
     * 批量置为售后退票记录为已退票
     *
     * @param list 退票记录集合
     * @return 影响行数
     */
    int batchReturnInvoiceStatusSuccess(List<Long> list);

    /**
     * 查询售后录票信息 未退票
     *
     * @param returnInvoiceWriteBackDto 蓝票no 蓝票code 售后单id
     * @return 需要退票票信息
     */
    List<ExpenseAfterSalesInvoiceEntity> selectNeedRefund(ReturnInvoiceWriteBackDto returnInvoiceWriteBackDto);

    /**
     * 回写录完后的发票
     *
     * @param collect   需要回写的退票票记录id 集合
     * @param invoiceId 发票id
     * @return
     */
    int writebackInvocie(@Param("list") List<Long> collect, @Param("invoiceId") Integer invoiceId,@Param("remark")String remark);

    /**
     * 查询售后录票信息 审核中
     *
     * @param returnInvoiceWriteBackDto 蓝票no 蓝票code 售后单id
     * @return 需要冲销的票信息
     */
    List<ExpenseAfterSalesInvoiceEntity> selectNeedReversal(ReturnInvoiceWriteBackDto returnInvoiceWriteBackDto);

    /**
     * 根据费用单itemId查询该费用商品已退票的invoiceId
     *
     * @param afterSaleIdList
     * @return invoiceId
     */
    List<Integer> getAlreadyReturnInvoice(@Param("afterSaleIdList") List<Long> afterSaleIdList, @Param("invoiceNo") String invoiceNo, @Param("invoiceCode") String invoiceCode);

    /**
     * 查询 仅退票类型的 退票商品信息集合
     *
     * @param expenseAfterSalesId
     * @return
     */
    List<ReturnInvoiceGoodsDto> getReturnInvoiceGoodsInfoList(@Param("expenseAfterSalesId") Long expenseAfterSalesId);

    /**
     *
     * @param expenseAfterSalesId
     * @param invoiceCode
     * @param invoiceNo
     * @return
     */
    List<ExpenseAfterSalesInvoiceEntity> findByExpenseAfterSalesIdAndInvoiceCodeAndInvoiceNo(@Param("expenseAfterSalesId") Long expenseAfterSalesId,
                                                                                          @Param("invoiceCode") String invoiceCode,
                                                                                          @Param("invoiceNo") String invoiceNo);

    /**
     * 更新费用售后退票信息为 未退票
     * @param collect id集合
     */
    void updateReturnInvoiceStatus(@Param("list") List<Long> collect);
}