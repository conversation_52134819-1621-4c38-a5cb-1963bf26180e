package com.vedeng.erp.buyorder.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description 采购费用明细
 * <AUTHOR>
 * @date 2022/10/17 15:18
 **/

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BuyorderExpenseItemDto extends BaseDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Integer buyorderExpenseItemId;

    /**
     * 采购费用单ID
     */
    private Integer buyorderExpenseId;

    /**
     * 收货状态0未收货 1部分收货 2全部收货
     */
    private Integer arrivalStatus;

    /**
     * 收货时间
     */
    private Date arrivalTime;

    /**
     * 发货状态0未发货 1部分发货 2全部发货
     */
    private Integer deliveryStatus;

    /**
     * 发货时间
     */
    private Date deliveryTime;

    /**
     * 收票状态0未收票 1部分收票 2全部收票
     */
    private Integer invoiceStatus;

    /**
     * 收票时间
     */
    private Date invoiceTime;

    /**
     * 商品ID
     */
    private Integer goodsId;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 是否删除0否1是
     */
    private Integer isDelete;

    /**
     * @组合对象@ 采购费用单商品明细详情
     */
    private BuyorderExpenseItemDetailDto buyorderExpenseItemDetailDto;

    /**
     * @组合对象@ 已申请付款金额
     */
    private BigDecimal passedPayApplyAmount;

    /**
     * @组合对象@ 已申请付款数量
     */
    private BigDecimal passedPayApplyNum;


    /**
     * @组合对象@ 已录(收)票数量
     */
    private BigDecimal invoicedNum;

    /**
     * @组合对象@ 已录票金额
     */
    private BigDecimal invoicedTotalAmount;

    /**
     * @组合对象@ 可售后数量
     */
    private Integer afterSalesQuantity;

    /**
     * @组合对象@ 售后数量
     */
    private Integer returnNum;


    /**
     * @组合对象@ 到货数量
     */
    private Integer arrivalNum;

    /**
     * @组合对象@ 采购单ID
     */
    private Integer buyOrderId;

    /**
     * @组合对象@ 价格
     */
    private BigDecimal price;

    /**
     * @组合对象@ sku
     */
    private String sku;

    /**
     * @组合对象@ 详情页每个商品维度的开票信息
     */
    private List<BuyOrderExpenseInvoiceByGoodsDto> invoiceByGoodsDtos;


    /**
     * @组合对象@ 合同打印用对象
     */
    private String priceStr;

    /**
     * @组合对象@ 合同打印用对象
     */
    private String allPrice;

    /**
     * 对应的 费用售后itemId
     */
    private Long expenseAfterSalesItemId;
    /**
     * 售后数量
     */
    private Integer afterReturnNum;

    /**
     * 关联销售信息
     */
    private List<BuyOrderSaleOrderGoodsDetailDto> buyOrderSaleOrderGoodsDetailDtos;
}