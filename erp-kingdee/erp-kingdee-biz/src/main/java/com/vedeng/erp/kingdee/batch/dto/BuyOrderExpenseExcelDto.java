package com.vedeng.erp.kingdee.batch.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 21 -22  采购费用
 * @date 2023/6/5 9:29
 **/
@Setter
@Getter
@NoArgsConstructor
public class BuyOrderExpenseExcelDto {

    /**
     * 采购单号
     */
    private String buyOrderNo;

    /**
     * sku
     */
    private String sku;

    /**
     * 类型
     */
    private String type;

    /**
     * 数量
     */
    private BigDecimal num;

    /**
     * 单价
     */
    private BigDecimal price;

    private String unitKingDeeNo;
}
