<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>详情</title>
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,viewport-fit=cover">
    <link rel="stylesheet" href="/mstatic/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/mstatic/mui/mui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/mstatic/css/pages/visitRecord.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>
    <jsp:include page="./head_import_im.jsp"></jsp:include>
    <input type="hidden" id="visitRecordId" name="visitRecordId" value="${param.id}"/>

    <div class="page-wrap" id="page-container">
        <div class="page-container">
            <div class="page-main">
                <div class="business-detail-container" v-if="!pageLoading">
                    <div class="page-top-bg" :class="stepStatus"></div>

                    <div class="page-nav" :class="stepStatus">
                        <div class="nav-item">
                            <div class="nav" :class="{'active': showNav == 1}" @click="changeNav(1)">拜访详情</div>
                        </div>
                        <div class="nav-item">
                            <div class="nav" :class="{'active': showNav == 2}" @click="changeNav(2)">操作记录</div>
                        </div>
                    </div>

                    <!-- 详情页面 -->
                    <div class="detail-page-wrap" v-show="showNav == 1">
                        <div class="dedtail-chance-step">
                            <ui-step :list="stepList" :active="stepActive" :status="stepStatus"></ui-step>
                            <ui-tip type="error" styles="2" v-if="detail.visitRecordStatus == 4" class="close-reason">
                                关闭原因：{{ detail.closeReasonTypeName }}
                                <template v-if="detail.closeReasonContent">-{{ detail.closeReasonContent }}</template>
                            </ui-tip>
                        </div>

                        <div class="form-section">
                            <div class="form-card">
                                <ui-form-item label="拜访编号">
                                    <div class="visitRecord-num">{{ detail.visitRecordNo }}
                                        <span class="copy" @click="onCopy(detail.visitRecordNo)">复制</span>
                                    </div>
                                </ui-form-item>
                                <ui-form-item label="拜访状态" label-middle>
                                    <div class="visit-status" :class="'status'+detail.visitRecordStatus">{{ {1: '待拜访', 2: '拜访中', 3: '已拜访', 4: '已关闭'}[detail.visitRecordStatus] }}</div>
                                </ui-form-item>
                                <ui-form-item label="拜访人">
                                    <ui-user 
                                        :name="detail.visitorName" :avatar="detail.visitorPic" 
                                        @click="openChat(detail.visitorId, detail.visitorJobNumber)"
                                    ></ui-user>
                                </ui-form-item>
                                <ui-form-item label="创建时间">{{detail.addTime || '-'}}</ui-form-item>
                            </div>
                        </div>

                        <div class="form-section">
                            <div class="form-section-title">拜访客户信息</div>
                            <div class="form-card">
                                <ui-form-item label="客户名称" class="detail-trader-name">
                                    <ui-trader-name :info="{
                                        'traderName': detail.customerName,
                                        'tycFlag': visitCustomerVo.tycFlag,
                                        'baidu': true
                                    }"></ui-trader-name>
                                </ui-form-item>
                                <ui-form-item label="客户等级" label-middle>
                                    <ui-trader-level v-if="visitCustomerVo.customerGrade" :level="visitCustomerVo.customerGrade"></ui-trader-level>
                                    <template v-else>-</template>
                                </ui-form-item>
                                <ui-form-item label="客户类型">
                                    {{ {465: '渠道商', 466: '终端'}[detail.customerNature] }}
                                </ui-form-item>
                                <template v-if="Object.keys(visitCustomerVo).length">
                                    <ui-form-item label="交易总额">
                                        <span 
                                            :class="{'price-light': visitCustomerVo.historyTransactionAmount}"
                                        >{{ visitCustomerVo.historyTransactionAmount || '-' }}</span>
                                    </ui-form-item>
                                    <ui-form-item label="交易次数">{{ visitCustomerVo.historyTransactionNum || '-' }}</ui-form-item>
                                    <ui-form-item label="最近下单">{{ visitCustomerVo.lastOrderTime || '-' }}</ui-form-item>
                                </template>
                                <ui-form-item label="联系人" v-if="detail.contactName">{{ detail.contactName }}</ui-form-item>
                                <ui-form-item label="职位" v-if="detail.contactPosition">{{ detail.contactPosition }}</ui-form-item>
                                <ui-form-item label="手机" v-if="detail.contactMobile">
                                    <ui-tip type="tel" :tel="detail.contactMobile">{{ detail.contactMobile }}</ui-tip>
                                    <template v-if="contactMobileStatus">
                                        <ui-tip v-if="contactMobileStatus == 2" margin-top="10px">该手机号未注册贝登商城</ui-tip>
                                        <ui-tip v-if="contactMobileStatus == 1" margin-top="10px" type="success">该手机号已注册贝登商城</ui-tip>
                                    </template>
                                </ui-form-item>
                                <ui-form-item label="固话" v-if="detail.contactTele">
                                    <ui-tip type="tel" :tel="detail.contactTele">{{ detail.contactTele }}</ui-tip>
                                </ui-form-item>
                                <template v-for="(item, index) in otherContactList">
                                    <ui-form-item :key="index" :label="item.label">{{ item.value }}</ui-form-item>
                                </template>
                                <ui-form-item label="拜访地区">
                                    {{ [detail.provinceName, detail.cityName, detail.areaName].join('-') }}
                                </ui-form-item>
                                <ui-form-item label="拜访详细地址">
                                    {{ detail.visitAddress || '-' }}
                                    <ui-map-view 
                                        v-if="detail.visitAddress"
                                        :address="detail.visitAddress" 
                                        :area="areaStr"
                                    >
                                        <ui-tip v-if="detail.visitAddress" type="map" margin-top="10px">查看地图</ui-tip>
                                    </ui-map-view>
                                </ui-form-item>
                            </div>
                        </div>

                        <div class="form-section">
                            <div class="form-section-title">计划信息</div>
                            <div class="form-card">
                                <ui-form-item label="拜访人">
                                    <ui-user 
                                        :name="detail.visitorName" :avatar="detail.visitorPic" 
                                        @click="openChat(detail.visitorId, detail.visitorJobNumber)"
                                    ></ui-user>
                                </ui-form-item>
                                <ui-form-item label="同行人" v-if="detail.tongxingUserList.length">
                                    <div class="user-label-list">
                                        <div 
                                            class="user-label-wrap"
                                            v-for="(item, index) in detail.tongxingUserList"
                                            :key="index"
                                        >
                                            <ui-user 
                                                :name="item.userName" :avatar="item.aliasHeadPicture" 
                                                @click="openChat(item.tongxingUserId, item.tongxingJobNumber)"
                                            ></ui-user>
                                        </div>
                                    </div>
                                </ui-form-item>
                                <ui-form-item label="计划拜访时间">{{ detail.planVisitDate || '-' }}</ui-form-item>
                                <ui-form-item label="拜访目标">{{ detail.visitTargetStr || '-' }}</ui-form-item>
                                <ui-form-item label="备注">{{ detail.remark || '-' }}</ui-form-item>
                            </div>
                        </div>

                        <div class="form-section card-wrap">
                            <div class="form-section-title">打卡信息</div>
                            <template v-if="Object.keys(visitUserInfo).length || tongxingUserList.length">
                                <!-- 拜访人-打卡信息 -->
                                <div class="form-card" v-if="Object.keys(visitUserInfo).length">
                                    <ui-form-item label="拜访人">
                                        <ui-user 
                                            :name="visitUserInfo.userName" :avatar="visitUserInfo.aliasHeadPicture" 
                                            @click="openChat(visitUserInfo.visitUserId)"
                                        ></ui-user>
                                    </ui-form-item>
                                    <ui-form-item label="打卡位置">
                                        <template v-if="visitUserInfo.id">
                                            <template v-if="visitUserInfo.cardLocation">
                                                <ui-location-view :location="visitUserInfo.cardLocation">
                                                    <div class="card-address-btn">
                                                        <i class="vd-ui_icon icon-address"></i>
                                                        查看打卡位置
                                                    </div>
                                                </ui-location-view>
                                            </template>
                                            <template v-else>历史打卡数据，无法地图查看</template>
                                        </template>
                                        <template v-else>未打卡</template>
                                    </ui-form-item>
                                    <ui-form-item label="拍照图片" v-if="cardPicList && cardPicList.length">
                                        <div class="card-pic-list">
                                            <div class="pic s60" v-for="(item, index) in cardPicList" :key="index">
                                                <img :src="item" alt="" @click="previewImg(index)" />
                                            </div>
                                        </div>
                                    </ui-form-item>
                                    <ui-form-item label="打卡时间" v-if="visitUserInfo.cardTime">{{ visitUserInfo.cardTime }}</ui-form-item>
                                </div>
                                <!-- 同行人-打卡信息 -->
                                <template v-for="(item, index) in tongxingUserList">
                                    <div class="form-card" :key="index">
                                        <ui-form-item label="同行人">
                                            <ui-user 
                                                :name="item.userName" :avatar="item.aliasHeadPicture" 
                                                @click="openChat(item.visitUserId)"
                                            ></ui-user>
                                        </ui-form-item>
                                        <ui-form-item label="打卡位置">
                                            <template v-if="item.id">
                                                <template v-if="item.cardLocation">
                                                    <ui-location-view :location="item.cardLocation">
                                                        <div class="card-address-btn">
                                                            <i class="vd-ui_icon icon-address"></i>
                                                            查看打卡位置
                                                        </div>
                                                    </ui-location-view>
                                                </template>
                                                <template v-else>历史打卡数据，无法地图查看</template>
                                            </template>
                                            <template v-else>未打卡</template>
                                        </ui-form-item>
                                        <ui-form-item label="打卡时间" v-if="item.cardTime">{{ item.cardTime }}</ui-form-item>
                                    </div>
                                </template>
                            </template>
                            <div class="empty-data" v-else>
                                <i class="vd-ui_icon icon-info2"></i>
                                <p>暂无打卡信息</p>
                            </div>
                        </div>

                        <!-- 相关线索/商机 -->
                        <div class="form-section" v-if="businessLeadsInfo || businessChanceInfo">
                            <template v-if="businessLeadsInfo">
                                <div class="form-section-title" >相关线索</div>
                                <div class="form-card">
                                    <ui-business-card
                                        :business-info="businessLeadsInfo"
                                        type="1"
                                        :trader-check="true"
                                        :trader-name="detail.customerName"
                                    ></ui-business-card>
                                </div>
                            </template>
                            <template v-else-if="businessChanceInfo">
                                <div class="form-section-title">相关商机</div>
                                <div class="form-card">
                                    <ui-business-card
                                        :business-info="businessChanceInfo"
                                        type="2"
                                        :trader-check="true"
                                        :trader-name="detail.customerName"
                                    ></ui-business-card>
                                </div>
                            </template>
                        </div>

                        <div class="form-section">
                            <div class="form-section-title">拜访记录
                                <div class="section-title-btn" v-if="detail.visitDetailButtonDto.addCommuncateRecordBtn" @click="showConnectDialog">
                                    <i class="vd-ui_icon icon-add"></i>
                                    <span>添加沟通记录</span>
                                </div>
                            </div>
                            <!-- 首次添加拜访记录会默认有一条沟通记录, so有沟通记录就代表没有生成过拜访记录 -->
                            <template v-if="communicateList.length">
                                <div class="form-card">
                                    <ui-form-item label="联系人">{{ detail.recordContactName || '-' }}</ui-form-item>
                                    <ui-form-item label="手机">
                                        <template v-if="detail.recordContactMobile">
                                            <ui-tip type="tel" :tel="detail.recordContactMobile">{{ detail.recordContactMobile }}</ui-tip>
                                            <ui-tip v-if="recordContactMobileStatus == 2" margin-top="10px">该手机号未注册贝登商城</ui-tip>
                                            <ui-tip v-if="recordContactMobileStatus == 1" margin-top="10px" type="success">该手机号已注册贝登商城</ui-tip>
                                        </template>
                                        <template v-else>-</template>
                                    </ui-form-item>
                                    <ui-form-item label="固话">
                                        <template v-if="detail.recordContactTele">
                                            <ui-tip type="tel" :tel="detail.recordContactTele">{{ detail.recordContactTele }}</ui-tip>
                                        </template>
                                        <div v-else>-</div>
                                    </ui-form-item>
                                    <template v-for="(item, index) in recordOtherContactList">
                                        <ui-form-item :key="index" :label="item.label">{{ item.value }}</ui-form-item>
                                    </template>
                                    <ui-form-item label="职位">{{ detail.recordContactPosition || '-' }}</ui-form-item>
                                    <ui-form-item label="讲解PPT">{{ detail.showPpt ? {Y: '已讲解', N: '未讲解'}[detail.showPpt] : '-' }}</ui-form-item>
                                </div>
                                <!-- 沟通记录 -->
                                <div class="form-card" v-if="communicateList.length" style="margin-top: 5px;">
                                    <div class="communicate-list">
                                        <div class="communicate-item" v-for="(item, index) in communicateList" :key="index">
                                            <div class="c-top">
                                                <div class="c-user">
                                                    <ui-user 
                                                        :name="item.userName" :avatar="item.aliasHeadPicture" 
                                                        @click="openChat(item.creator)"
                                                    ></ui-user>
                                                </div>
                                                <div class="c-time">{{ item.addTime }}</div>
                                            </div>
                                            <div class="c-content">{{ item.contentSuffix }}</div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <div class="empty-data" v-else>
                                <i class="vd-ui_icon icon-info2"></i>
                                <p>暂无拜访记录</p>
                            </div>
                        </div>

                        <!-- 按钮 -->
                        <div class="form-fixed-bottom" :class="{'z-index-top': showBtnDialog}" v-if="detailBtns.length">
                            <div class="featurn-box" :class="{'single': detailBtns.length == 1}">
                                <div class="more" @click="openBtnDialog" v-if="detailBtns.length > 1">
                                    <i class="icon1 vd-ui_icon icon-app-more"></i>
                                </div>
                                <vd-ui-button type="primary" @click="dealBtns(detailBtns[0])">{{ detailBtns[0].txt }}</vd-ui-button>
                            </div>
                        </div>
                    </div>

                    <!-- 操作记录 -->
                    <div class="operation-pagr-wrap" v-show="showNav == 2">
                        <div class="operation-records" id="operation">
                            <div class="list" v-if="operationList.length">
                                <div class="item" v-for="(item, index) in operationList" :key="index">
                                    <div class="row">
                                        <div class="creator" @click="openChat(item.creator)">
                                            <ui-user :name="item.creatorName" :avatar="item.aliasHeadPicture"></ui-user>
                                        </div>
                                        <div class="time">{{ item.operationTime }}</div>
                                    </div>
                                    <div class="content">{{ item.logContent }}</div>
                                </div>
                            </div>
                            <div class="page-null-data" v-else>
                                <i class="vd-ui_icon icon-info1 icon"></i>
                                <p class="font">历史数据暂无操作记录</p>
                            </div>
                        </div>
                    </div>

                    
                    <crm-slide-dialog ref="connectDialog" title="添加沟通记录" @hidn="hideConnectDialog">
                        <div class="slide-panel">
                            <div class="slide-wrap form">
                                <div class="form-card">
                                    <ui-form-item label="沟通记录" must vertical no-padding>
                                        <span class="textarea-count">{{ connectValue.length }} / 1000</span>
                                        <ui-textarea
                                            v-model="connectValue"
                                            maxlength="1000"
                                            height="144px"
                                            maxheight="450"
                                            height-auto
                                            placeholder="请补充拜访中的内容，最多不超过1000字"
                                        ></ui-textarea>
                                    </ui-form-item>
                                </div>
                            </div>
                            <div class="slide-dialog-default-footer">
                                <div class="btn-cancel-flex">
                                    <vd-ui-button @click="hideConnectDialog">取消</vd-ui-button>
                                </div>
                                <div class="btn-confirm-flex">
                                    <vd-ui-button type="primary" @click="addConnectRecord">提交</vd-ui-button>
                                </div>
                            </div>
                        </div>
                    </crm-slide-dialog>

                    <crm-slide-dialog ref="closeDialog" title="关闭拜访计划" @hidn="hideCloseDialog">
                        <div class="slide-panel">
                            <div class="slide-wrap form">
                                <div class="form-card">
                                    <ui-form-item label="关闭原因" must vertical>
                                        <ui-radio-group
                                            class="close-radio"
                                            :clearable="true"
                                            :list="CloseReasonTypes"
                                            v-model="closeReasonType"
                                        ></ui-radio-group>
                                    </ui-form-item>
                                    <ui-form-item label="说明" vertical no-padding>
                                        <span class="textarea-count">{{ closeReason.length }} / 200</span>
                                        <ui-textarea
                                            v-model="closeReason"
                                            maxlength="200"
                                            height="144px"
                                            height-auto
                                            placeholder="请输入"
                                        ></ui-textarea>
                                    </ui-form-item>
                                </div>
                            </div>
                            <div class="slide-dialog-default-footer">
                                <div class="btn-cancel-flex">
                                    <vd-ui-button @click="hideCloseDialog">取消</vd-ui-button>
                                </div>
                                <div class="btn-confirm-flex">
                                    <vd-ui-button type="primary" @click="submitClose">确定</vd-ui-button>
                                </div>
                            </div>
                        </div>
                    </crm-slide-dialog>

                    <crm-slide-dialog ref="btnsDialog" @hidn="hidnBtnDialog">
                        <div class="slide-panel">
                            <div class="slide-wrap">
                                <div class="page-btns">
                                    <template v-for="(item, index) in detailBtns">
                                        <div
                                            v-if="index > 0"
                                            :key="index"
                                            class="btn"
                                            :class="item.class"
                                            @click="dealBtns(item)"
                                        >{{ item.txt }}</div>
                                    </template>
                                    <!-- ??? -->
                                    <!-- <div class="btn" @click="toVisitAdd">添加拜访记录</div> -->
                                    <!-- <div class="btn" @click="editPlan">编辑计划</div> -->
                                    <!-- <div class="btn" @click="addNextPlan">创建下次拜访</div> -->
                                    <!-- <div class="btn" @click="addBusinessChance">创建商机</div> -->
                                    <!-- <div class="btn" @click="showConnectDialog">添加沟通记录</div> -->
                                    <!-- <div class="btn" @click="showCloseDialog">关闭</div> -->
                                </div>
                            </div>
                        </div>
                    </crm-slide-dialog>

                    <div class="fixed-btn" :class="{'maxTop': showNav == 1}" @click="gotoList">
                        <div class="to-list">
                            <i class="vd-ui_icon icon-home"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://wwcdn.weixin.qq.com/node/open/js/wecom-jssdk-2.0.2.js"></script>
    <script src="/mstatic/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/vuedraggable.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/mui/mui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/pages/visitRecord/detail.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
