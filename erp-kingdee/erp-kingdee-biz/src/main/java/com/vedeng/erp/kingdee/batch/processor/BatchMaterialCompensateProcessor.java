package com.vedeng.erp.kingdee.batch.processor;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchCoreSkuDto;
import com.vedeng.erp.kingdee.batch.dto.BatchMaterialFinanceDto;
import com.vedeng.erp.kingdee.domain.entity.KingDeeMaterialEntity;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialSubHeadEntityDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeMaterialMapper;
import com.vedeng.goods.dto.KingDeeSkuInfoDto;
import com.vedeng.goods.service.GoodsApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 基础物料补偿处理器
 */
@Service
@Slf4j
public class BatchMaterialCompensateProcessor implements ItemProcessor<BatchCoreSkuDto, KingDeeMaterialDto> {

    @Autowired
    private GoodsApiService goodsApiService;

    @Override
    public KingDeeMaterialDto process(BatchCoreSkuDto batchCoreSkuDto) throws Exception {
        if (Objects.isNull(batchCoreSkuDto) || Objects.isNull(batchCoreSkuDto.getSkuId())) {
            log.info("基础物料补偿处理器，skuId为空，不进行处理:{}", JSON.toJSONString(batchCoreSkuDto));
            return null;
        }
        KingDeeSkuInfoDto skuInfoDto = goodsApiService.getSkuInfoBySkuId(batchCoreSkuDto.getSkuId());
        if (Objects.isNull(skuInfoDto)) {
            log.info("基础物料补偿处理器，skuId:{}，未查询到sku信息", batchCoreSkuDto.getSkuId());
            return null;
        }
        KingDeeMaterialDto kingDeeMaterialDto = goodsApiService.getPushSkuInfoToKingDee(skuInfoDto);
        if (Objects.isNull(kingDeeMaterialDto)) {
            log.info("基础物料补偿处理器，skuId:{}，未查询到基础物料信息", batchCoreSkuDto.getSkuId());
            return null;
        }
        if (KingDeeBizEnums.updateMaterial.equals(kingDeeMaterialDto.getKingDeeBizEnums())) {
            log.info("基础物料补偿处理器，skuId:{}，基础物料信息为更新，不进行处理", batchCoreSkuDto.getSkuId());
            return null;
        }
        log.info("基础物料补偿处理器，skuId:{}，kingDeeMaterialDto:{}", batchCoreSkuDto.getSkuId(), JSON.toJSONString(kingDeeMaterialDto));
        return kingDeeMaterialDto;
    }
}