package com.vedeng.logistics.dao;

import com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogData;
import com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogDataExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WmsWarehouseGoodsOperateLogDataMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    int countByExample(WmsWarehouseGoodsOperateLogDataExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    int deleteByExample(WmsWarehouseGoodsOperateLogDataExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    int insert(WmsWarehouseGoodsOperateLogData record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    int insertSelective(WmsWarehouseGoodsOperateLogData record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    List<WmsWarehouseGoodsOperateLogData> selectByExample(WmsWarehouseGoodsOperateLogDataExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    WmsWarehouseGoodsOperateLogData selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    int updateByExampleSelective(@Param("record") WmsWarehouseGoodsOperateLogData record, @Param("example") WmsWarehouseGoodsOperateLogDataExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    int updateByExample(@Param("record") WmsWarehouseGoodsOperateLogData record, @Param("example") WmsWarehouseGoodsOperateLogDataExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    int updateByPrimaryKeySelective(WmsWarehouseGoodsOperateLogData record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
     *
     * @mbggenerated Fri Feb 21 14:37:02 CST 2025
     */
    int updateByPrimaryKey(WmsWarehouseGoodsOperateLogData record);
}