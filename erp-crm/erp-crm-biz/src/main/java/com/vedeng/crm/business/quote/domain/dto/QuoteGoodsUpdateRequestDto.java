package com.vedeng.crm.business.quote.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class QuoteGoodsUpdateRequestDto extends QuoteGoodsBaseDto{
    /**
     * 客户需求id
     */
    private Long quoteorderNeedsId;

    /**
     * 报价ID
     */
    private Integer quoteorderId;

    /**
     * 报价商品ID
     */
    private Integer quoteorderGoodsId;

    /**
     * sku
     */
    private String skuNo;

    private String oldSkuNo;

    /**
     * 货期
     */
    private String deliveryCycle;

    private String oldDeliveryCycle;

    /**
     * 价格
     */
    private BigDecimal price;

    private BigDecimal oldPrice;

    /**
     * 报备状态
     */
    private Integer reportStatus;

    private Integer oldReportStatus;

    /**
     * 报备状态
     */
    private Integer num;

    private Integer oldNum;
}
