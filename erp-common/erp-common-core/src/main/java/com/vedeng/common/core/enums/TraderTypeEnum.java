package com.vedeng.common.core.enums;

/**
 * 交易类型
 *
 * <AUTHOR>
 */
public enum TraderTypeEnum {


    /**
     * 1收入2支出3转移4转入5转出
     */
    income(1, "收入"),
    expense(2, "支出"),
    transfer(3, "转移"),
    rollIn(4, "转入"),
    rollOut(5, "转出");



    private final Integer type;

    private final String desc;

    TraderTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
