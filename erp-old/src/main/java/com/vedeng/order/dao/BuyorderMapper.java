package com.vedeng.order.dao;

import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.common.model.ResultAssist;
import com.vedeng.common.orderstrategy.model.BuyorderAmount;
import com.vedeng.erp.buyorder.dto.InvoiceSaveSearchDto;
import com.vedeng.finance.model.BuyorderData;
import com.vedeng.goods.model.CoreSpu;
import com.vedeng.logistics.model.bo.SaleAndBuyOrderGoodsBO;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.BuyorderModifyApply;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import com.wms.model.dto.SendSystemMsgDto;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Named("buyorderMapper")
public interface BuyorderMapper {

    int insert(Buyorder record);

    int insertSelective(Buyorder record);

    Buyorder selectByPrimaryKey(Integer buyorderId);

    int updateByPrimaryKeySelective(Buyorder record);

    int updateByPrimaryKey(Buyorder record);

    /**
     * <b>Description:</b><br> 查询分页
     * @param map
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月11日 下午1:20:35
     */
    List<BuyorderVo> getbuyordervolistpage(Map<String, Object> map);

    /**
     * <b>Description:</b><br> 查询分页的总记录数
     * @param map
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月11日 下午1:20:35
     */
    Integer getbuyordervolistpagecount(Map<String, Object> map);

    /**
     * <b>Description:</b><br> 查询采购订单的基本信息
     * @param buyorderId
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月19日 上午10:58:46
     */
    BuyorderVo getBuyorderVoById(Integer buyorderId);
    /**
     * <b>Description:</b><br> 获取产品总数以及总额
     * @param buyorderId
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月19日 上午10:58:46
     */
    BuyorderVo getBuyorderVoNumById(Integer buyorderId);
    /**
     * <b>Description:</b><br> 查询采购订单的基本信息
     * @param buyorderNo
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月19日 上午10:58:46
     */
    BuyorderVo getBuyorderVoByBuyorderNo(String buyorderNo);

    /**
     * <b>Description:</b><br> 获取客户生效的订单
     * @param traderId
     * @param limit
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年7月13日 上午10:55:07
     */
    List<Buyorder> getEffectOrders(@Param("traderId") Integer traderId, @Param("limit") Integer limit);

    /**
     * <b>Description:</b><br> 获取未完成的订单
     * @param buyorder
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2017年7月13日 下午4:01:38
     */
    List<BuyorderVo> getUnCompleteBuyorder(Buyorder buyorder);

    /**
     * <b>Description:</b><br> 获取采购入库的列表
     * @param map
     * @return
     * @Note
     * <b>Author:</b> Michael
     * <br><b>Date:</b> 2017年8月3日 下午5:02:08
     */
    List<BuyorderVo> getbuyorderinfolistpage(Map<String, Object> map);

    /**
     * <b>Description:</b><br> 获取采购订单列表
     * @param bo
     * @return
     * @Note
     * <b>Author:</b> duke
     * <br><b>Date:</b> 2017年8月17日 下午1:33:11
     */
	List<BuyorderVo> getInvoiceBuyorderList(BuyorderVo bo);

	/**
	 * <b>Description:</b><br> 产品在途数量（采购订单已发货但未入库的数量，不包含直发订单范围）
	 * @param goodsId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年9月4日 下午5:05:43
	 */
	Integer getGoodsOnWayNum(@Param("goodsId") Integer goodsId);


	/**
	 *
	 * <b>Description:</b><br> 根据采购订单详情ID获取采购订单的订单产品总数和总到货数量
	 * @param buyorderGoodsId
	 * @return
	 * @Note
	 * <b>Author:</b> Michael
	 * <br><b>Date:</b> 2017年9月6日 上午11:41:06
	 */
	BuyorderVo selectSumNumByBuyorderGoodsId(Integer buyorderGoodsId);


	BuyorderVo selectBuyorderGoodsId(Integer buyorderGoodsId);

	/**
	 * <b>Description:</b><br> 根据公司id查询采购订单中供应商id集合
	 * @param companyId
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年9月22日 下午4:17:00
	 */
	List<Integer> getBuyOrderTraderIdList(Integer companyId);
    /**
     *
     * <b>Description:</b><br> 在库商品列表
     * @param goodsId
     * @return
     * @Note
     * <b>Author:</b> scott
     * <br><b>Date:</b> 2017年10月9日 上午11:26:17
     */
	List<BuyorderVo> getBuyorderVoList(Integer goodsId);

	/**
	 * @description: 采购单付款金额
	 * @return: BigDecimal
	 * @author: Strange
	 * @date: 2020/11/2
	 **/
	BigDecimal getPaymentAmount(@Param("buyorderId") Integer buyorderId);

	/**
	 * <b>Description:</b><br> 批量查询已付款金额
	 * @param buyorderId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年10月17日 上午11:01:04
	 */
	List<BuyorderVo> batchPaymentAmountList(@Param("buyorderVos") List<BuyorderVo> buyorderVos);

	/**
	 * <b>Description:</b><br> 应付账期额
	 * @param buyorderId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年10月17日 上午11:01:08
	 */
	BigDecimal getLackAccountPeriodAmount(@Param("buyorderId") Integer buyorderId);

	/**
	 * <b>Description:</b><br> 已收票总额
	 * @param buyorderId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年10月17日 上午11:01:15
	 */
	BigDecimal getInvoiceAmount(@Param("buyorderId") Integer buyorderId);

	/**
	 * <b>Description:</b><br> 订单金额（总额-退款额）
	 * @param buyorderId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年10月24日 上午9:16:22
	 */
	BigDecimal getRealAmount(@Param("buyorderId") Integer buyorderId);

	/**
	 * <b>Description:</b><br> 订单账期支付金额
	 * @param buyorderId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年10月24日 上午9:16:37
	 */
	BigDecimal getPeriodAmount(@Param("buyorderId") Integer buyorderId);

	/**
	 * <b>Description:</b><br> 批量查询订单账期支付金额
	 * @param buyorderId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年10月24日 上午9:16:37
	 */
	List<BuyorderVo> batchPeriodAmountList(@Param("buyorderVos") List<BuyorderVo> buyorderVos);

	/**
	 * <b>Description:</b><br> 获取采购产品已收票数量(实际收票数量)
	 * @param buyorderGoodsId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年11月15日 下午6:03:03
	 */
	BigDecimal getHaveInvoiceNums(Integer buyorderGoodsId);
	/**
	 *
	 * <b>Description:</b><br> 获取采购产品已收票数量(批量)
	 * @param bgvList
	 * @return
	 * @Note
	 * <b>Author:</b> Cooper
	 * <br><b>Date:</b> 2018年7月2日 下午2:16:19
	 */
	List<BuyorderGoodsVo> getHaveInvoiceNumsByList(@Param("bgvList") List<BuyorderGoodsVo> bgvList);

	/**
	 * <b>Description:</b><br> 获取采购产品已收票数量
	 * @param buyorderGoodsId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年11月15日 下午6:03:03
	 */
	List<BuyorderGoodsVo> getHaveInvoiceNumsByParam(@Param("list") List<BuyorderGoodsVo> list);

	/**
	 * <b>Description:</b><br> getGoodsOccupyAmount
	 * @param goodsId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年11月23日 下午6:20:20
	 */
	BigDecimal getGoodsOccupyAmount(@Param("goodsId") Integer goodsId);

	/**
	 * <b>Description:</b><br> 平均到货时间信息：最近的生效采购订单生效时间往前三个月，已付款生效采购订单中，商品入库时间-商品所在采购订单生效时间
	 * @param goodsId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年11月24日 上午9:37:32
	 */
	List<BuyorderVo> getAverageArrivalList(@Param("goodsId") Integer goodsId);

	/**
	 * <b>Description:</b><br> 批量查询平均到货时间信息
	 * @param goodsIds
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年11月30日 下午5:41:21
	 */
	List<BuyorderVo> getAverageArrivalListByGoodsIds(@Param("goodsIds") List<Integer> goodsIds);
    /**
     *
     * <b>Description:</b><br> 打印采购单
     * @param buyorder
     * @return
     * @Note
     * <b>Author:</b> scott
     * <br><b>Date:</b> 2017年12月14日 上午10:57:56
     */
	BuyorderVo getBuyOrderPrintInfo(Buyorder buyorder);

	/**
	 * <b>Description:</b><br> 获取待同步采购订单数据
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2017年12月26日 下午2:58:16
	 */
	List<Buyorder> getWaitSyncBuyorderList();

	/**
	 * <b>Description:</b><br>
	 * @param buyorderIds
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年1月18日 上午9:47:31
	 */
	List<Buyorder> getBuyorderByBuyorderIdList(@Param("buyorderIds") List<Integer> buyorderIds);
	/**
	 * <b>Description:</b><br> 根据销售订单产品ID集合获取采购归属人id
	 * @param saleorderGoodIds
	 * @return
	 * @Note
	 * <b>Author:</b> Michael
	 * <br><b>Date:</b> 2018年1月18日 上午9:47:31
	 */
	List<Integer> getBuyorderUserBySaleorderGoodsIds(@Param("saleorderGoodIds") List<Integer> saleorderGoodIds);

	/**
	 * <b>Description:</b><br> 订单预付款金额（预付款总额-退款额）
	 * @param buyorderId
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2018年2月1日 下午8:18:47
	 */
	BigDecimal getRealPreAmount(Integer buyorderId);

	Buyorder getByuorderByBuyorderGoodsId(@Param("buyorderGoodsId") Integer buyorderGoodsId);

	BigDecimal getPeriodOrderAmount(@Param("traderId") Integer traderId);

	/**
	 * <b>Description:</b><br> 根据销售订单详情id获取对应的销售订单
	 * @param saleorderGoodsId
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2018年2月11日 下午1:39:13
	 */
	List<Buyorder> getBuyorderListBySaleorderGoodsId(@Param("saleorderGoodsId") Integer saleorderGoodsId);

	/**
	 * <b>Description:</b><br> 根据销售订单产品id获取对应的生效采购订单的数量
	 * @param saleorderGoodsId
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2018年2月11日 下午1:39:13
	 */
	Integer getBuyorderGoodsSumBySaleorderGoodsId(@Param("saleorderGoodsId") Integer saleorderGoodsId);

	/**
	 * <b>Description:</b><br> 未还帐期大于0的采购订单
	 * @param companyId
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年3月22日 下午2:55:23
	 */
	List<Integer> getLackAccountPeriodAmountBuyorderIds(@Param("companyId") Integer companyId);

	/**
	 * <b>Description:</b><br> 查询售后产品关联的采购订单
	 * @param saleorderGoodsId
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2018年2月11日 下午1:39:13
	 */
	List<Buyorder> getBuyorderListByAftersale(@Param("afterSalesGoodsVos") List<AfterSalesGoodsVo> afterSalesGoodsVos);
    /**
     *
     * <b>Description:</b><br> 查询采购单已入库的商品总数
     * @param buyorderGoodsId
     * @return
     * @Note
     * <b>Author:</b> scott
     * <br><b>Date:</b> 2018年4月17日 下午5:43:40
     */
	BuyorderVo getIsInNum(Integer buyorderGoodsId);
    /**
     *
     * <b>Description:</b><br> 将订单下的特殊商品都改为已到货
     * @param br
     * @return
     * @Note
     * <b>Author:</b> scott
     * <br><b>Date:</b> 2018年4月17日 下午6:01:35
     */
	int updateTsGoodsDh(Buyorder br);

	BigDecimal getPaymentAndPeriodAmount(@Param("buyorderId") Integer buyorderId);
    /**
     *
     * <b>Description:</b><br> 查询采购单信息
     * @param buyorderGoodsId
     * @return
     * @Note
     * <b>Author:</b> scott
     * <br><b>Date:</b> 2018年5月30日 下午7:13:14
     */
	BuyorderVo selectBuyorderInfo(Integer buyorderGoodsId);

	/**
	 * <b>Description:</b><br> 已付款总额（总计）
	 * @param map
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2018年5月30日 上午9:43:08
	 */
	BigDecimal getPaymentTotalAmount(Map<String, Object> map);

	/**
	 * <b>Description:</b><br> 收票总额（总计）
	 * @param map
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2018年5月30日 上午9:44:01
	 */
	BigDecimal getInvoiceTotalAmount(Map<String, Object> map);

	/**
	 * <b>Description:</b><br> 获取采购申请修改的详情
	 * @param buyorder
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年6月12日 上午11:44:09
	 */
	BuyorderVo getApplyBuyorderDetail(Buyorder buyorder);

	/**
	 * <b>Description:</b><br> 根据采购订单id查询关联销售订单的数量
	 * @param buyorderId
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年7月13日 下午5:56:23
	 */
	List<Integer> getSaleorderIdListByBuyorderId(@Param("buyorderId") Integer buyorderId);


	/**
	 * <b>Description:</b><br> 根据采购订单id查询关联销售订单的数量（排除备货单）
	 * @param buyorderId
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年7月13日 下午5:56:23
	 */
	List<Integer> getSaleorderIdListByBuyorderIdNoBH(@Param("buyorderId") Integer buyorderId);


	/**
	 * <b>Description:</b><br> 根据采购订单id查询关联销售订单商品的直发普发状态
	 * @param buyorderId
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年7月13日 下午5:56:23
	 */
	List<Integer> getSaleorderGoodsDeliveryDirectByBuyorderId(@Param("buyorderId") Integer buyorderId);
    /**
    /**
     *
     * <b>Description:</b><br> 销售单出库对应的采购单
     * @param saleorderGoodsId
     * @return
     * @Note
     * <b>Author:</b> scott
     * <br><b>Date:</b> 2018年7月6日 上午10:42:42
     */
	List<Buyorder> getBuyorderOutListBySaleorderGoodsId(Integer saleorderGoodsId);

	BigDecimal getPeriodOrderAmountNew(@Param("traderId") Integer traderId, @Param("companyId") Integer companyId);
	/**
	 * <b>Description:</b><br> 采购单申请修改生效，修改采购单信息
	 * @param buyorder
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年7月18日 下午5:12:49
	 */
	int updateBuyorderApplyValid(Buyorder buyorder);

	/**
	 * <b>Description:</b><br> 获取订单支付的账期金额（账期支付-账期还款-退还账期）
	 * @param id
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2018年8月1日 下午1:05:49
	 */
	BuyorderVo getBuyorderPeriodAmount(@Param("buyorderId") Integer buyorderId);

	Integer updateDataTimeByOrderId(Integer orderId);

	Integer updateDataTimeByDetailId(Integer orderDetailId);
	/**
	*获取采购单关联的销售详情id
	* @Author:strange
	* @Date:10:31 2020-04-10
	*/
	List<Integer> getSaleorderGooodsIdByBuyorderId(Integer orderId);

	/**
	 * 获取所有采购单id
	 * <AUTHOR>
	 * @Date 10:52 上午 2020/5/22
	 * @Param
	 * @return
	 **/
    List<Integer> getAllBuyorderId();

	/**
	 * 获取所有进行中的采购单
	 * @return
	 */
	List<Integer> getAllProcessingBuyOrderList();

	/**
	 * @description: 采购单退款退货流水总金额
	 * @return: BigDecimal
	 * @author: Strange
	 * @date: 2020/11/2
	 **/
	BigDecimal getAfterReturnAmount(Integer orderId);

	/**
	 * @description: 获取采购单支付金额信息
	 * @return: BuyorderAmount
	 * @author: Strange
	 * @date: 2020/11/2
	 **/
    BuyorderAmount getNoPaybackAmount(Integer orderId);

	/**
	 * @description: 更新采购单金额信息
	 * @return: int
	 * @author: Strange
	 * @date: 2020/11/2
	 **/
	int updateBuyorderAmount(Buyorder updateOrder);

	/**
	 * @description: 获取距今几天的采购单号
	 * @return: List<Integer>
	 * @author: Strange
	 * @date: 2020/11/3
	 **/
	List<Integer> getRecentDayOrder(@Param("day") Integer day);

	/**
	 * @description: 根据单号获取订单信息
	 * @param buyOrderNo
	 * @return {@link Buyorder}
	 * @throws
	 * <AUTHOR>
	 * @date 2020/11/27 13:11
	 */
	Buyorder getBuyOrderByOrderNo(String buyOrderNo);

	/**
	 * 获取采购订单中商品涉及到的产品归属
	 * @param buyorderId 采购单id
	 * @return 产品归属
	 */
	List<CoreSpu> getAssignListOfSkuInBuyorder(Integer buyorderId);

	/**
	 * @description: 查询风控订单
	 * @return: List<Buyorder>
	 * @author: Strange
	 * @date: 2021/1/6
	 **/
	List<Buyorder> getFinishOrderStatus(Buyorder searchBuyorder);

    void clearBuyOrderAuditInfo(@Param("buyOrderId") Integer buyOrderId);

	/**
	 * 保存采购单的录票状态
	 *
	 * @param buyorderId
	 * @param invoiceStatus
	 * @return
	 */
	Integer saveInvoiceStatus(@Param("buyorderId") Integer buyorderId, @Param("invoiceStatus") Integer invoiceStatus);

    /**
     * 获取采购单商品已收票金额
     *
     * @param buyorderGoodsId
     * @return
     */
	BigDecimal getHaveInvoiceTotalAmount(Integer buyorderGoodsId);

	/**
	 * 获取采购订单商品实际数量（除去售后）
	 *
	 * @param buyorderGoodsId
	 * @return
	 */
	BigDecimal getGoodsTotalNum(Integer buyorderGoodsId);


	/**
	 *  获取销售id
	 * @param buyorderGoodIds
	 * @return
	 */
	List<Integer> getSaleOrderIdByBuyOrderGoodIds(@Param("saleorderGoodIds") List<Integer> buyorderGoodIds);
	/**
	 * @Description 订单绑定VB单
	 * <AUTHOR>
	 * @Date 14:41 2021/4/14
	 * @Param [saleorderGoodId, num, buyorderGoodsId]
	 * @return int
	 **/
	boolean bindVB(@Param("saleorderGoodId")Integer saleorderGoodId, @Param("num")Integer num, @Param("buyorderGoodsId")Integer buyorderGoodsId);

	/**
	 * 根据订单找销售
	 * @param orderId
	 * @return
	 */
    List<Integer> selectBuyOrderUserIdListByPrimaryKey(@Param("buyOrderId") Integer orderId);

    List<Integer> getrecentDayOrder(Integer day);

    /**
     * 获得预生成催货任务的sku
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/5/18 16:17.
     * @author: Randy.Xu.
     * @param
     * @return: java.util.List<com.vedeng.order.model.BuyorderGoods>.
     * @throws:  .
     */
    List<BuyorderGoods> getPreRemindGoodsOrderList();

    /**
     * 根据采购订单修改id查询采购订单
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/5/27 10:11.
     * @author: Randy.Xu.
     * @param BuyorderModifyId
     * @return: com.vedeng.order.model.BuyorderModifyApply.
     * @throws:  .
     */
    BuyorderModifyApply getBuyModifyInfoByBuyorderModifyId(Integer buyorderModifyId);

	List<Integer> getSaleOrderUserIdsByBuyOrderGoodId(Integer buyorderGoodsId);

	List<Saleorder> getSaleorderByBuyorderGoodsId(Integer relateBusinessId);

	void updateExpeditingStatusById(BuyorderVo buyorderVoById);
	/**
	 * <b>Description:</b><br>
	 * 根据销售售后skuid查询直发采购单号
	 *
	 * @param afterSalesGoodsId
	 * @return java.util.List<com.vedeng.order.model.Buyorder>
	 * @Note <b>Author:</b> Thor <br>
	 *       <b>Date:</b> 2021/6/17 17:20
	 */
    List<Buyorder> selectSalesBuyorderList(@Param("afterSalesGoodsId")Integer afterSalesGoodsId);

	/**
	 * <b>Description:</b><br> 查询供应商的交易时间
	 * @param companyId
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年3月22日 下午2:55:23
	 */
	List<Integer> getTraderIdsByTime(TraderSupplierVo traderSupplierVo);

	/**
	 * 根据采购订单状态检索采购订单ID
	 *
	 * @param status
	 * @return
	 */
    List<Integer> getBuyOrderIdsByStatus(@Param("status") Integer status);

	/**
	 * 采购订单检索所有修改申请单ID
	 *
	 * @param buyOrderId
	 * @return
	 */
	List<Integer> getBuyOrderApplyIdsByBuyOrderId(Integer buyOrderId);

	/**
	 * 采购单关闭，晚上重新计算成本价
	 */
	void updateSaleGoodsDataTimeByBuyOrderId(@Param("buyOrderId") Integer buyOrderId);


	/**
	 * 分页获取订单数据
	 * @param pageParam
	 * @return
	 */
    List<Buyorder> getSaleorderListPage(Map<String, Object> pageParam);

    List<Buyorder> getRelateBuyorderInProgress(@Param("skuNo") String skuNo);

	/**
	 * <b>Description:</b><br> 获取采购订单票款信息
	 * @param buyorderIds
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2017年10月23日 下午4:16:53
	 */
	List<BuyorderData> getBuyorderDatas(@Param("buyorderIds")List<Integer> buyorderIds);

	/**
	 * <b>Description:</b><br> 采购订单实际金额（订单总额-退款金额）
	 * @param buyOrderIdList
	 * @param companyId
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2018年9月14日 下午2:27:21
	 */
	List<ResultAssist> getNewRealAmount(@Param("buyOrderIdList")List<Integer> buyOrderIdList, @Param("companyId")Integer companyId);

	/**
	 * 查询采购单合同回传数量
	 * @param buyorderId
	 * @return
	 */
    Integer selectAttachmentCountByBuyOrderId(Integer buyorderId);

	/**
	 * 更新主表审核状态
	 * @param buyorderId
	 * @param status
	 */
    void saveVerifyStatus(@Param("buyorderId")Integer buyorderId,@Param("status")Integer status);

	/**
	 * 根据skuId查询采购单商品在途数量
	 */
	int getSkuOnWayBySkuId(@Param("skuId") Integer skuId);

	/**
	 * 根据BuyorderId查询对应的goods及数量（去除运费）
	 */
	List<BuyorderGoods> getBuyOrderGoodsExceptFreight(@Param("buyOrderId") Integer buyOrderId);

	/**
	 * <b>Description:</b><br> 已付款金额
	 * @param buyorderId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年10月17日 上午11:01:04
	 */
	BigDecimal getPaymentAmountDB(@Param("buyorderId")Integer buyorderId);

	BigDecimal getAfterSaleServiceAmount(Integer buyorderId);


	/**
	 * 根据销售单id查询关联的采购单id集合
	 */
	List<Integer> getBuyorderIdListBySaleorderId(@Param("saleorderId") Integer saleorderId);


    /**
     * 更新采购单的合同url
     *
     * @param buyorderId  采购单id
     * @param contractUrl 合同url
     */
    void updateContractUrlOfBuyorder(@Param("buyorderId") Integer buyorderId, @Param("contractUrl") String contractUrl);

	/**
	 * 信息发送dto，根据采购单ID获取对应订单信息
	 * @param buyorderId
	 * @return
	 */
	SendSystemMsgDto getBuyorderInfoById(Integer buyorderId);

	/**
	 * 获取录票的符合条件的采购单商品信息
	 * @return
	 */
	List<BuyorderGoodsVo> getBuyOrderGoodsList4InvoiceSave(InvoiceSaveSearchDto searchDto);

	/**
	 * 订单ID获取订单信息
	 * @param orderIds
	 * @return
	 */
	List<BuyorderVo> getBuyOrderInfoByOrderIds(@Param("orderIds") List<Integer> orderIds);

	/**
	 * 更具关联No获取单据
	 *
	 * @param relateNo
	 * @return
	 */
	Buyorder selectBuyOrderByRalateNo(@Param("relateNo") String relateNo);

	/**
	 * 根据单据id获取单据
	 *
	 * @param orderId
	 * @return
	 */
	Buyorder selectBuyOrderByOrderId(@Param("orderId") Integer orderId);

	/**
	 * 符合条件的采购单ID集合
	 * @param searchDto
	 * @return
	 */
	List<Integer> getBuyOrderIdsWithEligible(InvoiceSaveSearchDto searchDto);

	/**
	 * 根据采购订单创建时间查询客户ID
	 * @param queryParams
	 * @return
	 */
	List<Integer> selectByAddTime(Map<String, Object> queryParams);

	/**
	 * 获取入库单  的入库记录商品 的 授权书类型
	 *
	 * @param traderId
	 * @param validTime
	 * @param skuNo
	 * @return
	 */
	String findByTraderSupplyIdAndValidStartTimeBeforeAndValidEndTimeAfter(@Param("traderId") Integer traderId, @Param("validTime") Long validTime, @Param("skuNo") String skuNo);

	/**
	 * 根据采购订单产品id列表获取销售订单编号及销售产品id
	 * @param buyOrderGoodsIds
	 * @return
	 */
	List<SaleAndBuyOrderGoodsBO> getSaleAndBuyOrderGoodsByIds(@Param(value = "list") List<Integer> buyOrderGoodsIds);


    Integer getStatusById(Integer buyorderId);

    Integer updateIsGift(@Param("buyorderId") Integer buyorderId,@Param("isGift")Integer isGift);
    Integer selectDeliveryDirect(Integer buyorderId);

    Integer countEnableReceiveNum(@Param("userId") Integer userId,@Param("beginTime") String beginTime,@Param("endTime") String endTime);

	List<Buyorder> selectBuyorderByExpressId(Integer expressId);

	void updateExpressEnableReceiveByIds(List<Buyorder> buyorderList);

	BigDecimal getPrepaidAmountByBuyorderId(@Param("buyorderId")Integer buyorderId);


}