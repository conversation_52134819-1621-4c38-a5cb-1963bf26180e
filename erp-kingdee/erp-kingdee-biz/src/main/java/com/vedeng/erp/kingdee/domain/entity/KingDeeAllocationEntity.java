package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @description: 调拨单
 * @author: yana.jiang
 * @date: 2022/11/11
 */
@Getter
@Setter
@ToString
@Table(name = "KING_DEE_ALLOCATION")
public class KingDeeAllocationEntity extends BaseEntity {
    /**
    * 单据内码
    */
    private String fId;

    /**
    * 单据编号
    */
    private String fBillNo;

    /**
    * 单据类型
    */
    private String fBillTypeId;

    /**
    * 调拨方向
    */
    private String fTransferDirect;

    /**
    * 调拨类型
    */
    private String fTransferBizType;

    /**
    * 调出库存组织
    */
    private String fStockOutOrgId;

    /**
    * fOwnerTypeOutIdHead
    */
    private String fOwnerTypeOutIdHead;

    /**
    * 调入库存组织
    */
    private String fStockOrgId;

    /**
    * 单据日期
    */
    private String fDate;

    /**
    * 借调人
    */
    private String fQzokJdr;

    /**
    * 贝登单据头ID
    */
    private String fQzokBddjtid;

    /**
    * fBillEntry
    */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fBillEntry;

    /**
    * id
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;
}