<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.UnitKingDeeMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.UnitKingDee">
    <!--@mbg.generated-->
    <!--@Table T_UNIT_KING_DEE-->
    <id column="UNIT_KING_DEE_ID" jdbcType="INTEGER" property="unitKingDeeId" />
    <result column="UNIT_KING_DEE_NO" jdbcType="VARCHAR" property="unitKingDeeNo" />
    <result column="UNIT_KING_DEE_NAME" jdbcType="VARCHAR" property="unitKingDeeName" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    UNIT_KING_DEE_ID, UNIT_KING_DEE_NO, UNIT_KING_DEE_NAME, IS_DELETE, ADD_TIME, CREATOR, 
    CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, REMARK, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_UNIT_KING_DEE
    where UNIT_KING_DEE_ID = #{unitKingDeeId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_UNIT_KING_DEE
    where UNIT_KING_DEE_ID = #{unitKingDeeId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="UNIT_KING_DEE_ID" keyProperty="unitKingDeeId" parameterType="com.vedeng.goods.domain.entity.UnitKingDee" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_UNIT_KING_DEE (UNIT_KING_DEE_NO, UNIT_KING_DEE_NAME, IS_DELETE, 
      ADD_TIME, CREATOR, CREATOR_NAME, 
      MOD_TIME, UPDATER, UPDATER_NAME, 
      REMARK, UPDATE_REMARK)
    values (#{unitKingDeeNo,jdbcType=VARCHAR}, #{unitKingDeeName,jdbcType=VARCHAR}, #{isDelete,jdbcType=BOOLEAN}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="UNIT_KING_DEE_ID" keyProperty="unitKingDeeId" parameterType="com.vedeng.goods.domain.entity.UnitKingDee" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_UNIT_KING_DEE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="unitKingDeeNo != null">
        UNIT_KING_DEE_NO,
      </if>
      <if test="unitKingDeeName != null">
        UNIT_KING_DEE_NAME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="unitKingDeeNo != null">
        #{unitKingDeeNo,jdbcType=VARCHAR},
      </if>
      <if test="unitKingDeeName != null">
        #{unitKingDeeName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.domain.entity.UnitKingDee">
    <!--@mbg.generated-->
    update T_UNIT_KING_DEE
    <set>
      <if test="unitKingDeeNo != null">
        UNIT_KING_DEE_NO = #{unitKingDeeNo,jdbcType=VARCHAR},
      </if>
      <if test="unitKingDeeName != null">
        UNIT_KING_DEE_NAME = #{unitKingDeeName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where UNIT_KING_DEE_ID = #{unitKingDeeId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.domain.entity.UnitKingDee">
    <!--@mbg.generated-->
    update T_UNIT_KING_DEE
    set UNIT_KING_DEE_NO = #{unitKingDeeNo,jdbcType=VARCHAR},
      UNIT_KING_DEE_NAME = #{unitKingDeeName,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where UNIT_KING_DEE_ID = #{unitKingDeeId,jdbcType=INTEGER}
  </update>
</mapper>