<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="订单打印" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<%--<link href='<%=basePath%>static/css/order_print/system.css'
      rel="stylesheet" type="text/css">--%>
<%--<link rel="stylesheet"
      href='<%=basePath%>static/css/order_print/ui.theme.css?rnd=${resourceVersionKey}'
      type="text/css" media="all">--%>
<%--<link type="text/css" rel="stylesheet"
      href='<%=basePath%>static/css/order_print/layer.css?rnd=${resourceVersionKey}'
      id="skinlayercss">--%>
<script type="text/javascript"
        src='<%=basePath%>static/js/jquery.PrintArea.js'></script>
<script type="text/javascript"
        src='<%=basePath%>static/js/order/saleorder/order_print.js?rnd=${resourceVersionKey}'></script>
<link type="text/css" rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}">
<link type="text/css" rel="stylesheet" href="<%=basePath%>static/css/manage.css"/>
<body>

<c:choose>
    <c:when test="${flowOrderInfo.contractFileUrl != null && flowOrderInfo.contractFileUrl!=''}">
        <div  >
            <iframe style="width: 1400px;height: 1300px"  src="${flowOrderInfo.contractFileUrl}" frameborder="0" style="width:1000px; height:600px;" scrolling="no"></iframe>
        </div>
    </c:when>

<c:otherwise>
<div id="print-contract">
    <%-- 更换字体 --%>
    <style>
        @font-face {
            font-family: 'SourceHanSansCN-Regular';
            src: url('<%=basePath%>static/hansFont/SourceHanSansCN-Regular.otf');
        }

        html, body, #print-contract {
            font-family: "SourceHanSansCN-Regular", sans-serif;
        }
        .keyWord{
            color: rgba(255,255,255,0);
            font-size: 1px;
        }
    </style>
    <div class="">
        <div class="contract-head">
            <div class="contract-number">
                <div>合同号码: ${flowOrder.flowOrderNo}</div>
                <div>制单日期:
                    <c:if test="${flowOrder.addTime == null}">
                        ${currTime}
                    </c:if>
                    <c:if test="${flowOrder.addTime != null}">
                        <date:date value="${flowOrder.addTime}" format="yyyy-MM-dd"/>
                    </c:if>
                </div>
            </div>
        </div>
        <div class="contract-head-title">采购合同</div>
        <div class="contract-print-table">
            <table>
                <tbody>
                <tr class="jiayi">
                    <td><span>甲方：</span><span>${partyA.traderName}</span><span class="keyWord">$jia$</span></td>
                    <td><span>乙方：</span><span>${partyB.traderName}</span><span class="keyWord">$yi$</span></td>
                </tr>
                <tr>
                    <td>
                        <div style="display: inline-block">
                            采购及收票人员： ${partyA.traderContactName}
                        </div>
                        <div>
                            <span>联系方式：</span>
                            <span>${partyA.traderContactPhone}</span>
                        </div>
                        <div>
                            <span>开票信息：</span>
                            <span></span>
                        </div>
                        <div class="overflow-hidden">
                            <span style="width:71px;float:left;">注册地址/电话：</span>

                            <span style="float:left;display:inline-block;max-width:-moz-calc(100% - 91px);max-width:-webkit-calc(100% - 91px);max-width:calc(100% - 91px);">${vedeng_address_phone}</span>
                        </div>
                        <div>
                            <span>开户行：</span>
                            <span>${companyInfoA.bankName}</span>
                        </div>
                        <div>
                            <span>税号：</span>
                            <span>${companyInfoA.businessLicense}</span>
                        </div>
                        <div>
                            <span>账号：</span>
                            <span>${companyInfoA.bankAccount}</span>
                        </div>
                    </td>
                    <td>
                        <div>
                            <span>销售人员：</span>
                            <span>${partyB.traderContactName}
                            </span>
                        </div>
                        <div>
                            <span>联系方式：</span>
                            <span>${partyB.traderContactPhone}</span>
                        </div>
                        <div>
                            <span>收款信息：</span>
                            <span></span>
                        </div>
                        <div>
                            <span>开户行：</span>
                            <span>${companyInfoB.bankName}</span>
                        </div>
                        <div>
                            <span>账号：</span>
                            <span>${companyInfoB.bankAccount}</span>
                        </div>
                        <div>
                            <span>税号：</span>
                            <span>${companyInfoB.businessLicense}</span>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="contract-print-title2 pl10">货物接收信息</div>
            <table>
                <tbody>
                <tr>
                    <td>
                        <div>
                            <span>收货人/联系方式：</span>
                            <span>${partyB.receiverName}/${partyB.receiverPhone}</span>
                            <div>
                                <span>收货地址：</span>
                                <span>${partyB.receiverAddress}</span>
                            </div>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="contract-tips">就甲方向乙方采购产品事宜，经友好协商，由双方于${contractDateList[0]}年${contractDateList[1]}月${contractDateList[2]}日在南京市秦淮区签订本合同，以资遵守。</div>
            <div class="contract-print-title2 pl10">一、产品信息：</div>
            <table class="print-product-table">
                <tbody>
                <tr class="font-bold tdsmall">
                    <td style="width:4%;">序号</td>
                    <td style="width:8%;">订货号</td>
                    <td style="width:26%;">产品名称</td>
                    <td style="width:6%;">品牌</td>
                    <td style="width:8%;">型号/规格</td>
                    <c:if test="${haveMedicalApparatus == 1}">
                        <td style="width:11%;">注册证编号/备案编号</td>
                        <td style="width:11%;">注册人/备案人名称</td>
                    </c:if>
                    <td style="width:4%;">数量</td>
                    <td style="width:4%;">单位</td>
                    <td style="width:6%">单价(元）</td>
                    <td style="width:6%;">金额(元）</td>
                    <td style="width:5%;">货期<br/><span style="font-size:0.75em;">(日)</span></td>
                    <td style="width:5%">安装政策</td>
                    <td style="width:5%;">质保期</td>
                    <td style="width:4%;">备注</td>
                </tr>
                <c:set var="count" value="1"></c:set>
                <c:forEach var="detail" items="${detailList}" varStatus="num">
                    <c:if test="${detail.isDelete eq 0}">
                        <tr>
                            <td class="" style="width: 4%;"> ${count}</td>
                            <c:set var="count" value="${count+1}"></c:set>
                            <td style="width: 6%;">${detail.skuNo}</td>
                            <td style="width: 26%; text-align:left;">${detail.productName}</td>
                            <td style="width: 6%;">${detail.brand}</td>
                            <td style="width: 8%;">${detail.model}</td>
                            <c:if test="${haveMedicalApparatus == 1}">
                                <td style="width: 11%;">
                                    <c:choose>
                                        <c:when test="${detail.registrationNumber != null && detail.registrationNumber != '' }">
                                            ${detail.registrationNumber}
                                        </c:when>
                                        <c:otherwise>
                                            -
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td style="width: 11%;">
                                    <c:choose>
                                        <c:when test="${detail.manufacturerName != null && detail.manufacturerName != '' }">
                                            ${detail.manufacturerName}
                                        </c:when>
                                        <c:otherwise>
                                            -
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                            </c:if>
                            <td style="width: 4%;">${detail.quantity}</td>
                            <td style="width: 4%;">${detail.unit}</td>
                            <td style="width: 10%;">
                                <c:set var="priceList" value="${detailPriceMap[detail.flowOrderDetailId]}" />
                                <c:forEach items="${priceList}" var="price">
                                    <c:if test="${price.flowNodeId eq partyB.flowNodeId}">
                                        <fmt:formatNumber value="${price.price}" pattern="#,##0.00" />
                                    </c:if>
                                </c:forEach>
                            </td>
                            <td style="width: 10%;">
                                <c:set var="priceList" value="${detailPriceMap[detail.flowOrderDetailId]}" />
                                <c:forEach items="${priceList}" var="price">
                                    <c:if test="${price.flowNodeId eq partyB.flowNodeId}">
                                        <fmt:formatNumber value="${price.price * detail.quantity}" pattern="#,##0.00" />
                                    </c:if>
                                </c:forEach>
                            </td>
                            <td style="width: 5%;"></td>

                            <td style="width: 5%;"></td>
                            <td style="width: 5%;"></td>
                            <td style="width: 4%;"></td>
                        </tr>
                    </c:if>
                </c:forEach>

                <tr>
                    <td colspan=3>合计</td>
                    <td colspan="${haveMedicalApparatus == 1 ? 8 : 6}" style="overflow: hidden;">
                        <div class="total-count f_left">（大写）${chineseNumberTotalPrice}</div>
                        <div class="total-count-num f_right">￥${totalAmount}</div>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="${haveMedicalApparatus == 1 ? 15 : 13}">&nbsp;</td>
                </tr>
                </tbody>
            </table>
            <div class="contract-print-title2">二、付款方式&发票类型：</div>
            <div class="contract-details">
                1、 付款方式：
                <c:choose>
                    <%-- PAYMENT_METHOD = 0：先款后货，预付100% --%>
                    <c:when test="${partyB.paymentMethod == 0}">
                        先款后货，预付100%，预付金额<fmt:formatNumber type="number" value="${partyB.amount}" pattern="0.00" maxFractionDigits="2"/>元。
                    </c:when>
                    <%-- PAYMENT_METHOD = 1：先货后款 --%>
                    <c:when test="${partyB.paymentMethod == 1}">
                        先货后款
                        <%-- 显示预付金额（partyB.amount就是预付金额） --%>
                        <c:if test="${partyB.amount != null && partyB.amount > 0}">
                            ，预付金额<fmt:formatNumber type="number" value="${partyB.amount}" pattern="0.00" maxFractionDigits="2"/>元
                        </c:if>
                        <%-- 显示账期支付金额（如果有） --%>
                        <c:if test="${partyB.creditPayment != null && partyB.creditPayment > 0}">
                            ，账期支付<fmt:formatNumber type="number" value="${partyB.creditPayment}" pattern="0.00" maxFractionDigits="2"/>元
                        </c:if>
                        <%-- 显示尾款（如果有） --%>
                        <c:if test="${partyB.balance != null && partyB.balance > 0}">
                            ，尾款<fmt:formatNumber type="number" value="${partyB.balance}" pattern="0.00" maxFractionDigits="2"/>元
                            <c:if test="${partyB.balanceDueDate != null && partyB.balanceDueDate > 0}">
                                ，尾款期限${partyB.balanceDueDate}月
                            </c:if>
                        </c:if>
                        。
                    </c:when>
                    <%-- 默认情况 --%>
                    <c:otherwise>
                        先款后货，预付100%，预付金额<fmt:formatNumber type="number" value="${partyB.amount}" pattern="0.00" maxFractionDigits="2"/>元。
                    </c:otherwise>
                </c:choose>
                <br/>
                2、 发票类型：
                <c:choose>
                    <c:when test="${partyB.invoiceType == 688}">0%增值税专用发票</c:when>
                    <c:when test="${partyB.invoiceType == 429}">17%增值税专用发票</c:when>
                    <c:when test="${partyB.invoiceType == 430}">17%增值税普通发票</c:when>
                    <c:when test="${partyB.invoiceType == 681}">16%增值税普通发票</c:when>
                    <c:when test="${partyB.invoiceType == 682}">16%增值税专用发票</c:when>
                    <c:when test="${partyB.invoiceType == 683}">6%增值税普通发票</c:when>
                    <c:when test="${partyB.invoiceType == 684}">6%增值税专用发票</c:when>
                    <c:when test="${partyB.invoiceType == 685}">3%增值税普通发票</c:when>
                    <c:when test="${partyB.invoiceType == 686}">3%增值税专用发票</c:when>
                    <c:when test="${partyB.invoiceType == 687}">0%增值税普通发票</c:when>
                    <c:when test="${partyB.invoiceType == 971}">13%增值税普通发票</c:when>
                    <c:when test="${partyB.invoiceType == 972}">13%增值税专用发票</c:when>
                    <c:when test="${partyB.invoiceType == 1758}">1%增值税专用发票</c:when>
                    <c:when test="${partyB.invoiceType == 4071}">1%增值税普通发票</c:when>
                    <c:otherwise>未指定</c:otherwise>
                </c:choose>
                。乙方开具的发票必须注明双方签订合同时的订单号，否则会影响后续合作和付款。
                <br/>
                3、 开票时间：乙方应在发货后七个工作日内向甲方开具满足上述发票类型的合规票据并于开票之日将发票寄送给甲方；若本合同分批次发货的，乙方应在每批次发货后的七个工作日完成开票并于开票之日将发票寄送给甲方。
            </div>
            <div class="contract-print-title2">三、产品交付：</div>
            <div class="contract-details">
                1、 关于货期：货期⾃⼄⽅收到甲⽅预付款之日起开始计算。<br/>
                2、 当收到甲方对产品与订单不符，或质量不合格声明时，乙方负责无条件退货。甲方由此而产生的所有费用，应由乙方承担，由此造成交货期延误的，按本条第3款约定执行。<br/>
                3、 交货期：如因乙方原因造成货物不能在订单规定的日期完成交货，乙方需承担延迟交付的责任，乙方每天赔偿合同总金额的0.3%作为违约赔偿金，直到收到货物为止。如到合同规定的交期，乙方仍不能交货，甲方有权解除合同，乙方必须全额退还甲方的订货预付款，且乙方向甲方支付合同总金额30%的违约金，并承担对甲方造成的损失。<br/>
                4、 针对存在有效期的产品（效期产品），包括但不限于耗材、试剂产品，乙方应保证送达甲方指定收货地点时效期产品剩余有效期的天数大于效期产品总有效期天数的2/3。否则甲方有权直接拒收，由此产生的甲方协助乙方退回货物的运费、乙方再次发货给甲方的运费、拒收货物损毁灭失风险等全部由乙方承担。由此造成交货期延误的，按本条第3款约定执行。<br/>
                5、 合同第一条所约定的合同价款包括但不限于货款、税金、包装、运输、装卸、保险、报关（如系进口商品）、安装、调试、技术指导、培训、咨询以及售后服务等其他各项有关费用。即：除合同第一条所约定的合同价款外，甲方无须再向乙方支付其他任何费用。<br/>
                6、 乙方应保证产品到货时随货提供产品质量合格证明材料（包括但不限于质量检验报告、合格证书、合格标签等）。产品若为医疗器械，乙方还应提供加盖乙方出库章的随货同行单；有冷链储运管理要求的，乙方还应提供合规的冷链交接单、在途温湿度记录；产品若为进口产品，乙方应保证产品已按照中国的进口法律、法规和政策办理全部进口手续，应向甲方提供真实有效的进口报关单及出入境检验检疫证书（如适用）。<br/>
                7、 产品经甲方验收合格后，视为乙方完成产品的交付，产品灭失、损毁的风险自此转移至甲方。<br/>
                8、 未经甲方书面同意，乙方不得提前交货，否则甲方有权拒收，乙方应承担因此发生的仓储费、保管费、货损或其他费用。
            </div>
            <div class="contract-print-title2">四、质量保证：</div>
            <div class="contract-details">
                1、 乙方保证提供的产品为原厂原包装正品，产品未经拆封、未经更换标签、及未开机使用。乙方应保证产品内外包装上标识产品唯一性质的编码内容完全一致，且可以在厂家官方可查验渠道上进行验证查询。<br/>
                2、 乙方提供的产品必须是符合国家质量标准的合格品，产品的包装、标签和说明书符合国家和行业的有关规定，其包装能确保商品质量和货物运输要求。<br/>
                3、 乙方应当采取有效措施，确保产品运输、贮存过程符合产品说明书或者标签标示要求，尤其是需要特殊温度储运的产品。<br/>
                4、 若乙方提供的产品因质量问题（含包装质量）验收不合格，乙方应积极协助甲方退换货，退换货产生的运输费用由乙方承担。若因质量问题（含包装质量）而造成甲方损失的，乙方也应承担甲方的损失。
            </div>
            <div class="contract-print-title2">五、售后服务：</div>
            <div class="contract-details">
                1、 根据甲方及产品特性，乙方给甲方提供售后服务，服务内容包含提供技术支持、对质量或故障医疗器械进行维修，产品培训等。服务的方式包含直接上门提供维修服务，电话方式进行专业指导、联系生产企业对接解决问题。<br/>
                2、 对于甲方自身设立售后服务人员，可由乙方对其下游客户或使用者提供安装、维修、技术培训的服务，必要时可联系生产企业技术人员提供售后服务。<br/>
            </div>
            <div class="contract-print-title2">六、反商业贿赂：</div>
            <div class="contract-details">
                1、 双方应按照所有适用的法律法规，包括但不限于所有适用的反贿赂和反不正当法律法规，履行其在本合同项下的各项义务。乙方确认知晓国家法律、法规和甲方相关制度关于反商业贿赂事项的各项规定。乙方已获甲方正式告知，甲方对于任何形式商业贿赂均持坚决的反对态度，亦不会授权任何员工要求、指示、暗示乙方实施、参与任何形式商业贿赂行为，以获得交易机会或其他经济利益。甲方员工如有实施商业贿赂行为，乙方将第一时间告知甲方。双方在开展业务发展活动过程中，将严格遵守国家法律、法规，不从事、参与任何形式的商业贿赂及不正当竞争行为，以自身行动维护双方良好的合作关系。一旦发现，甲方有权单方解除本合同，并将乙方纳入供应商黑名单，同时，乙方应向甲方支付等同于已发生业务货款金额二十倍的违约金，并承担由于该违约行为给甲方造成的一切损失，甲方可直接从应付乙方的货款中扣除，不足部分甲方保留向乙方追偿的权利。
            </div>
            <div class="contract-print-title2">七、知识产权：</div>
            <div class="contract-details">
                1、 未经甲⽅事先书⾯同意，乙⽅不得在任何国家或地区，并通过任何形式，使⽤甲⽅及其关联⽅的商标、商号或将与甲⽅商标、商号相同或类似的内容注册为乙⽅的商标、商号、公司域名等。<br/>
                2、 未经甲⽅事先书⾯同意，乙⽅不得对甲⽅及其关联⽅的企业名称、商标、商号、服务标志或标识进⾏任何商业使⽤和擅⾃作出任何变更，包括但不限于在⼴告、宣传资料、办公地点等使⽤。<br/>
                3、 乙方承诺对于乙方所销售的产品未侵害任何第三方的知识产权，若甲方因此受到任何第三方关于该产品知识产权的责任追究都由乙⽅⾃⾏承担，由此给甲⽅造成损失的，乙⽅还应向甲⽅承担相应的赔偿责任。
            </div>
            <div class="contract-print-title2">八、保密条款：</div>
            <div class="contract-details">
                甲方向乙方提供的与此订单有关的所有信息，包括但不限于关于产品、商业机密、图纸、文件、商标，乙方必须保密，且未经甲方事先书面同意，乙方不得公开或以其他方式向第三方透露，否则应当承担给甲方造成的损失。<br/>
            </div>
            <div class="contract-print-title2">九、违约责任：</div>
            <div class="contract-details">
                合同生效后，乙方不得单方面撤销合同。若乙方单方面撤销合同或乙方未按照本合同各条的要求履行义务而产生违约的，乙方应承担合同总金额30%的违约金（各条有单独约定违约金的适用各条约定的违约金），并承担给甲方造成的损失。
            </div>
            <div class="contract-print-title2"> 十、合同争议处理：</div>
            <div class="contract-details">
                对于合同争议，双方需本着友好精神协商解决，协商不成，可将争议提请甲方所在地人民法院诉讼解决。
            </div>
            <div class="contract-print-title2"> 十一、合同效力：</div>
            <div class="contract-details">
                本合同自双方盖章之日起生效，传真件、扫描件与原件具有同等法律效力。
            </div>
            <div class="contract-print-title2"> 十二、 其他约定：</div>
            <div class="contract-details">
                若货物由乙方按照甲方指示直接发送甲方客户的，乙方还应遵守如下约定：<br>
                1、乙方按照甲方要求发货。<br>
                2、乙方发货时，不能夹带乙方任何联系方式，如名片、便签、含价格单据等。<br>
                3、乙方发货时，不能夹带乙方开具给甲方的发票。<br>
                4、甲方的客户，甲方有权自主维护。若甲方客户有意越过甲方与乙方直接合作，乙方应拒绝为甲方客户报价，并将情况及时告知甲方。<br>
                5、上述条款中，乙方若有任何一条违约情况发生，应承担合同总金额30%的违约金。<br>
            </div>
            <div class="contract-print-title2">十三、补充条款：</div>
            <c:if test="${!empty additionalClause}">
                <div class="contract-details">${additionalClause}</div>
            </c:if>
            <c:if test="${empty additionalClause}">
                <div class="contract-details">无。</div>
            </c:if>
            <div class="contract-print-sign">
                <ul>
                    <li style="margin-left: 50px;padding:32px 45px 0px 45px;">
                        <div class="sign-name">甲方：${partyA.traderName}<span class="keyWord">$jia$</span></div>
                        <div class="sign-place">
                        </div>
                    </li>
                    <li style="float: right; margin-right: 50px;overflow:visible;position: relative; padding:32px 0 28px 0;">
                        <div class="sign-name">乙方：${partyB.traderName}<span class="keyWord">$yi$</span></div>
                        <div class="sign-place">
                        </div>
                    </li>
                    <div class='clear'></div>
                </ul>
            </div>
        </div>
    </div>
</div>
</c:otherwise>
</c:choose>
<input type="hidden" name="flowOrderInfoId" id="flowOrderInfoId" value="${flowOrderInfo.flowOrderInfoId}">
<%@ include file="../../common/footer.jsp" %>