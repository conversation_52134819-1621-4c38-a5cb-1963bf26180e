package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.enums.OtherTypeConst;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.domain.entity.LogDataKingDee;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInResultDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageOutDto;
import com.vedeng.erp.kingdee.repository.mappers.LogDataKingDeeMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 样品入库单历史数据
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchSampleInHistoryProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto> {
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private LogDataKingDeeMapper logDataKingDeeMapper;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Override
    public KingDeeStorageInDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        KingDeeStorageInDto dto = new KingDeeStorageInDto();
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        if (!OtherTypeConst.UPDATE_REMARK_ORTHER_TYPE.equals(batchWarehouseGoodsOutInDto.getUpdateRemark())){
            return null;
        }
        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(dto);
        if(old){
            log.info("样品入库单,数据已存在:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }

        log.info("样品入库单,BatchReceiveProcessorService.process:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));

        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo()));
        dto.setFId("0");
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        dto.setFDate(DateUtil.formatDate(batchWarehouseGoodsOutInDto.getOutInTime()));
        dto.setFStockDirect("GENERAL");

        List<KingDeeStorageInDetailDto> detailDtoList = new ArrayList<>();
        List<BatchWarehouseGoodsOutInItemDto> byOutInNo = batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo());
        if (CollUtil.isEmpty(byOutInNo)) {
            log.error("未能查到入库单子单信息,batchWarehouseGoodsOutInDto:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }
        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(byOutInNo);
        batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos().forEach(l -> {
            KingDeeStorageInDetailDto detailDto = new KingDeeStorageInDetailDto();
            detailDto.setFMaterialId("V" + l.getGoodsId());
            detailDto.setFStockId("CK9999");
            BigDecimal num = l.getNum().abs();
            detailDto.setFQty(num.toString());

            List<LogDataKingDee> logDataKingDee = logDataKingDeeMapper.findByOutInNoAndGoodsIdAndOrderType(l.getOutInNo(), l.getGoodsId(), "样品入库");
            if (CollUtil.isEmpty(logDataKingDee)) {
                log.error("样品入库未能查到金额信息,BatchWarehouseGoodsOutInItemDto:{}", JSON.toJSONString(l));
                return;
            }
            BigDecimal realPrice = logDataKingDee.get(0).getRealPrice().abs();
            detailDto.setFPrice(realPrice.toString());
            detailDto.setFAmount(num.multiply(realPrice).setScale(2,RoundingMode.HALF_UP).toString());

            detailDto.setFQzokYsddh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokGsywdh(batchWarehouseGoodsOutInDto.getRelateNo());
            detailDto.setFQzokYwlx("样品入库");
            detailDto.setFQzokPch(l.getBatchNumber());
            detailDto.setFQzokXlh(l.getBarcodeFactory());
            detailDto.setFQzokSfzf(OtherTypeConst.ERP.equals(batchWarehouseGoodsOutInDto.getSource()) ? "true" : "false");

            detailDtoList.add(detailDto);
        });
        dto.setFEntity(detailDtoList);
        return dto;
    }
}
