$(function(){
	$("#close-layer").click(function(){
		 return false;
	});
	$("#disableForm").submit(function(){
		var dr=$("#enableComment").val();
		if(dr==""){
			warnTips("enableComment","禁用原因不允许为空");
			return false;
		}else if(dr.length>200){
			warnTips("enableComment","禁用原因不能超过200个字符");
			return false;
		}
		$.ajax({
			url:page_url+'/warehouse/storageAreaMag/upDisableStorageArea.do',
			data:$('#disableForm').serialize(),
			type:"POST",
			dataType : "json",
			success:function(data)
			{
				parent.location.reload();
			},
			error: function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}else
				    layer.alert("操作失败")
				}
		});
		return false;
	})
	$("#start").click(function() {
		if($("#st_is_enable").val()==0){
			layer.alert("上级单位已被禁用 ，无法启用该单位！");
			return false;
		}
		var storageAreaId = $("#storage_AreaId").val();
		var isEnable = $("#is_Enable").val();
		$.ajax({
			url : page_url + '/warehouse/storageAreaMag/upDisableStorageArea.do',
			data : {
				"storageAreaId" : storageAreaId,
				"isEnable" : isEnable
			},
			type : "POST",
			dataType : "json",
			success : function(data) {
				location.reload();
			},
			error: function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}else
				    layer.alert("操作失败")
				}
		});
	});
})

