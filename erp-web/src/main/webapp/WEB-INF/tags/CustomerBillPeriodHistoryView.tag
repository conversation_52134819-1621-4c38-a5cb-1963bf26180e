<%@ tag language="java" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<%@attribute name="saleHistory" type="java.util.List" required="true" %>
<%@attribute name="totalHistory" type="com.vedeng.trader.dto.CustomerBillPeriodCreditHistoryVo" required="true" %>

<table class="table table-bordered table-striped table-condensed table-centered">
    <thead>
    <tr>
        <th class="table-smallest">账期类型</th>
        <th class="table-smallest">订单实际金额</th>
        <th class="table-smallest">未还账期金额</th>
        <th class="table-smallest">逾期未还金额</th>
        <th class="table-smallest">逾期订单数</th>
        <th class="table-smallest">逾期天数</th>
        <th class="table-smallest">订单平均逾期天数</th>
        <th class="table-smallest">账期现有额度</th>
        <th class="table-smallest">账期剩余额度</th>
        <th class="table-smallest">历史逾期次数</th>
        <th class="table-smallest">账期历史使用次数</th>
        <th class="table-smallest">账期次数逾期率</th>
        <th class="table-smallest">历史账期订单总金额</th>
        <th class="table-smallest">历史账期订单逾期总额</th>
        <th class="table-smallest">账期金额逾期率</th>
        <th class="table-smallest">历史逾期订单数</th>
        <th class="table-smallest">历史逾期天数</th>
        <th class="table-smallest">历史订单平均逾期天数</th>
    </tr>
    </thead>

    <tbody>
    <c:if test="${not empty saleHistory}">
        <c:forEach items="${saleHistory}" var="sale">
            <tr>
                <td>
                    <c:choose>
                        <c:when test="${sale.billPeriodType == 1}">正式账期</c:when>
                        <c:when test="${sale.billPeriodType == 2}">临时账期</c:when>
                        <c:when test="${sale.billPeriodType == 3}">订单账期</c:when>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${empty sale.orderListAmount}">-</c:when>
                        <c:otherwise>
                            <fmt:formatNumber type="number" value="${sale.orderListAmount}" pattern="0.00" maxFractionDigits="2" />
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${empty sale.unreturnedAmount}">-</c:when>
                        <c:otherwise>
                            <fmt:formatNumber type="number" value="${sale.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${empty sale.overdueAmount}">-</c:when>
                        <c:otherwise>
                            <fmt:formatNumber type="number" value="${sale.overdueAmount}" pattern="0.00" maxFractionDigits="2" />
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="empty sale.orderCountOfOverdue">-</c:when>
                        <c:otherwise>
                            ${sale.orderCountOfOverdue}
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${empty sale.daysOfOverdue}">-</c:when>
                        <c:otherwise>
                            ${sale.daysOfOverdue}
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${(empty sale.avgOverdueDaysByOrder || sale.avgOverdueDaysByOrder eq 0) && (empty
                                sale.orderCountOfOverdue || empty sale.daysOfOverdue || sale.orderCountOfOverdue eq 0 ||
                                sale.daysOfOverdue eq 0)}">
                            -
                        </c:when>
                        <c:when test="${(empty sale.avgOverdueDaysByOrder || sale.avgOverdueDaysByOrder eq 0) && (not empty
                                sale.orderCountOfOverdue && not empty sale.daysOfOverdue && sale.orderCountOfOverdue ne 0 &&
                                sale.daysOfOverdue ne 0) }">
                            <fmt:formatNumber type="number" value="${(sale.daysOfOverdue/sale.orderCountOfOverdue) }"
                                              maxFractionDigits="2" />
                        </c:when>
                        <c:otherwise>
                            ${sale.avgOverdueDaysByOrder}
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${empty sale.applyAmount}">-</c:when>
                        <c:otherwise>
                            <fmt:formatNumber type="number" value="${sale.applyAmount}" pattern="0.00" maxFractionDigits="2" />
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${empty sale.availableAmount}">-</c:when>
                        <c:otherwise>
                            <fmt:formatNumber type="number" value="${sale.availableAmount}" pattern="0.00" maxFractionDigits="2" />
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${empty sale.historyCountOfOverdue}">-</c:when>
                        <c:otherwise>
                            ${sale.historyCountOfOverdue}
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${empty sale.historyUsedCount}">-</c:when>
                        <c:otherwise>
                            ${sale.historyUsedCount}
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${empty sale.historyPercentOverdueByUsedCount || sale.historyPercentOverdueByUsedCount eq 0 }">0%
                        </c:when>
                        <c:otherwise>
                            <fmt:formatNumber type="number" value="${(sale.historyPercentOverdueByUsedCount)*100}"  maxFractionDigits="2" />%
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${empty sale.historyOrderListAmount}">-</c:when>
                        <c:otherwise>
                            <fmt:formatNumber type="number" value="${sale.historyOrderListAmount}" pattern="0.00" maxFractionDigits="2" />
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${empty sale.historyAmountOfOverdue}">-</c:when>
                        <c:otherwise>
                            <fmt:formatNumber type="number" value="${sale.historyAmountOfOverdue}" pattern="0.00" maxFractionDigits="2" />
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${empty sale.historyOrderListAmount || sale.historyOrderListAmount.unscaledValue() == 0 || empty(sale.historyAmountOfOverdue)}">
                            0%
                        </c:when>
                        <c:otherwise>
                            <fmt:formatNumber type="number" value="${(sale.historyAmountOfOverdue*100/sale.historyOrderListAmount)}" maxFractionDigits="2" />%
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${empty sale.historyOrderCountOfOverdue}">-</c:when>
                        <c:otherwise>
                            ${sale.historyOrderCountOfOverdue}
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${empty sale.historyDaysOfOverdue}">-</c:when>
                        <c:otherwise>
                            ${sale.historyDaysOfOverdue}
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${empty sale.historyAvgDaysOfOverdueByOrder}">-</c:when>
                        <c:otherwise>
                            ${sale.historyAvgDaysOfOverdueByOrder}
                        </c:otherwise>
                    </c:choose>
                </td>
            </tr>
        </c:forEach>

        <tr>
            <td>合计</td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.orderListAmount}">-</c:when>
                    <c:otherwise>
                        <fmt:formatNumber type="number" value="${totalHistory.orderListAmount}" pattern="0.00" maxFractionDigits="2" />
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.unreturnedAmount}">-</c:when>
                    <c:otherwise>
                        <fmt:formatNumber type="number" value="${totalHistory.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.overdueAmount}">-</c:when>
                    <c:otherwise>
                        <fmt:formatNumber type="number" value="${totalHistory.overdueAmount}" pattern="0.00" maxFractionDigits="2" />
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.orderCountOfOverdue}">-</c:when>
                    <c:otherwise>
                        ${totalHistory.orderCountOfOverdue}
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.daysOfOverdue}">-</c:when>
                    <c:otherwise>
                        ${totalHistory.daysOfOverdue}
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.daysOfOverdue || empty totalHistory.orderCountOfOverdue || totalHistory.orderCountOfOverdue == 0}">-</c:when>
                    <c:otherwise>
                        <fmt:formatNumber type="number" value="${(totalHistory.daysOfOverdue/totalHistory.orderCountOfOverdue)}"  maxFractionDigits="2" />
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.applyAmount}">-</c:when>
                    <c:otherwise>
                        <fmt:formatNumber type="number" value="${totalHistory.applyAmount}" pattern="0.00" maxFractionDigits="2" />
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.availableAmount}">-</c:when>
                    <c:otherwise>
                        <fmt:formatNumber type="number" value="${totalHistory.availableAmount}" pattern="0.00" maxFractionDigits="2" />
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.historyCountOfOverdue}">-</c:when>
                    <c:otherwise>
                        ${totalHistory.historyCountOfOverdue}
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.historyUsedCount}">-</c:when>
                    <c:otherwise>
                        ${totalHistory.historyUsedCount}
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.historyUsedCount || empty totalHistory.historyCountOfOverdue || totalHistory.historyUsedCount == 0}">-</c:when>
                    <c:otherwise>
                        <fmt:formatNumber type="number" value="${(totalHistory.historyCountOfOverdue/totalHistory.historyUsedCount * 100)}" maxFractionDigits="2" />%
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.historyOrderListAmount}">-</c:when>
                    <c:otherwise>
                        <fmt:formatNumber type="number" value="${totalHistory.historyOrderListAmount}" pattern="0.00" maxFractionDigits="2" />
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.historyAmountOfOverdue}">-</c:when>
                    <c:otherwise>
                        <fmt:formatNumber type="number" value="${totalHistory.historyAmountOfOverdue}" pattern="0.00" maxFractionDigits="2" />
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.historyAmountOfOverdue || empty totalHistory.historyOrderListAmount || totalHistory.historyOrderListAmount.unscaledValue() == 0}">-</c:when>
                    <c:otherwise>
                        <fmt:formatNumber type="number" value="${(totalHistory.historyAmountOfOverdue * 100 / totalHistory.historyOrderListAmount)}" maxFractionDigits="2" />%
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.historyOrderCountOfOverdue}">-</c:when>
                    <c:otherwise>
                        ${totalHistory.historyOrderCountOfOverdue}
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.historyDaysOfOverdue}">-</c:when>
                    <c:otherwise>
                        ${totalHistory.historyDaysOfOverdue}
                    </c:otherwise>
                </c:choose>
            </td>
            <td>
                <c:choose>
                    <c:when test="${empty totalHistory.historyDaysOfOverdue || empty totalHistory.historyOrderCountOfOverdue || totalHistory.historyOrderCountOfOverdue == 0}">-</c:when>
                    <c:otherwise>
                        <fmt:formatNumber type="number" value="${(totalHistory.historyDaysOfOverdue/totalHistory.historyOrderCountOfOverdue)}"  maxFractionDigits="2" />
                    </c:otherwise>
                </c:choose>
            </td>
        </tr>
    </c:if>
    </tbody>
</table>