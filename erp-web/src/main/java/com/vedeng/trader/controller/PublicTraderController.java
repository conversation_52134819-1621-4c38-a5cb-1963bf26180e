package com.vedeng.trader.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.newtask.service.TraderGroupService;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.coupon.service.BaseCouponService;
import com.vedeng.erp.trader.constant.InvalidReasonEnum;
import com.vedeng.erp.trader.domain.dto.PublicTraderLockUpdateDto;
import com.vedeng.erp.trader.mapper.PublicTraderMapper;
import com.vedeng.erp.trader.service.PublicTraderService;
import com.vedeng.market.api.dto.request.QueryCouponCenterRequest;
import com.vedeng.market.api.dto.response.mycoupon.MyCouponDto;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.RegionService;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.group.model.TraderGroup;
import com.vedeng.trader.model.RTraderGroupJTrader;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.service.TraderCustomerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 公私海项目-接口
 *
 * <AUTHOR>
 * @create 2022/2/15 16:26
 */
@Controller
@RequestMapping(value = "/trader/public")
public class PublicTraderController extends BaseController {

    @Autowired
    private PublicTraderService publicTraderService;

    @Autowired
    @Qualifier("regionService")
    private RegionService regionService;

    @Resource
    private TraderCustomerService traderCustomerService;

    @Resource
    private UserService userService;

    @Autowired
    protected OrgService orgService;

    @Autowired
    private BaseCouponService baseCouponService;

    @Autowired
    private TraderGroupService traderGroupService;

    /**
     * 公海虚拟账户配置人员ID
     */
    @Value("${VIRTUAL_PUBLIC_CUSTOMER_USER_ID}")
    private Integer virtualPublicCustomerUserId;


    @Value("#{'${publicListIgnoreAuthUserId}'.split(',')}")
    private List<Integer> publicListIgnoreAuthUserId;

    @Resource
    private PublicTraderMapper publicTraderMapper;

    /**
     * 公海批量撤销按钮显示人员
     */
    @Value("#{'${publicBatchRemoveShowUser}'.split(',')}")
    private List<Integer> userIds;

    @Value("${traderCustomerLevelGrade}")
    private String traderCustomerLevelGradeList;

    /**
     * 公海列表页
     *
     * @param mv               modelAndView
     * @param traderCustomerVo traderCustomerVo
     * @return 页面视图
     */
    @RequestMapping(value = "/index")
    @NoNeedAccessAuthorization
    public ModelAndView index(ModelAndView mv,
                              TraderCustomerVo traderCustomerVo,
                              @RequestParam(defaultValue = "1", required = false) Integer pageNo,
                              @RequestParam(defaultValue = "10", required = false) Integer pageSize) {

        log.info("公海列表页url[/index] -- {}", traderCustomerVo);



        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
//        if(StringUtils.isEmpty(request.getParameter("search"))){ VDERP-17897 Aadi将默认条件去掉
//            //traderCustomerVo.setEffectiveness(1);
//            traderCustomerVo.setQueryGongHaiLevelList(Arrays.asList(2,3,4,5));// 默认：优质客户、重点关注客户、潜力客户、次要客户； 去掉了一个无效客户
//        }
        User currentLoginUser = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        Page page = getPageTag(request, pageNo, pageSize);

        // 页面日期转化
        if (ObjectUtils.notEmpty(traderCustomerVo.getQueryStartTime())) {
            traderCustomerVo.setStartTime(DateUtil.convertLong(traderCustomerVo.getQueryStartTime(), "yyyy-MM-dd"));
        }
        if (ObjectUtils.notEmpty(traderCustomerVo.getQueryEndTime())) {
            traderCustomerVo.setEndTime(DateUtil.convertLong(traderCustomerVo.getQueryEndTime() + " 23:59:59", "yyyy-MM-dd HH:mm:ss"));
        }


        if (StrUtil.isNotEmpty(traderCustomerLevelGradeList)) {
            List<String> split = StrUtil.split(traderCustomerLevelGradeList, ",");
            mv.addObject("traderCustomerLevelGradeList", split);
        }
        // 处理页面地区搜索展示
        regionSelectOptionHandle(mv, traderCustomerVo);

        // 归属销售入参
        List<Integer> userIdList = new LinkedList<>();
        if (ObjectUtils.notEmpty(traderCustomerVo.getHomePurchasing())) {
            userIdList.add(traderCustomerVo.getHomePurchasing());
        }
        if (CollectionUtil.isEmpty(userIdList)) {
            userIdList = null;
        }
        traderCustomerVo.setUserIdList(userIdList);

        // 归属销售筛选项(公海虚拟账户配置人员)
        User user = userService.getUserById(virtualPublicCustomerUserId);
        List<User> userList = new ArrayList<>();
        if (user != null) {
            userList.add(user);
        }
        mv.addObject("userList", userList);

        // 原归属销售
        List<User> originUsers = traderCustomerService.getOriginUsers();
        if (!CollectionUtils.isEmpty(userIdList)) {
            originUsers.addAll(userList);
        }
        // 效率比 java8 stream 高
        Set<Integer> userSet = new HashSet<>();
        // 删除重复的元素
        Iterator<User> iterator = originUsers.iterator();
        while (iterator.hasNext()) {
            User orgUser = iterator.next();
            if (orgUser != null && orgUser.getUserId() != null) {
                if (userSet.contains(orgUser.getUserId())) {
                    iterator.remove();
                } else {
                    userSet.add(orgUser.getUserId());
                }
            }

        }
        if (!CollectionUtils.isEmpty(originUsers)) {
            originUsers = originUsers.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }

        mv.addObject("originUsers", originUsers);
        
        // 公海列表(客户列表额外约束)
        publicTraderConstraint(traderCustomerVo, currentLoginUser);

        // 是否显示批量撤销按钮
        mv.addObject("isShow", showBatchRemove(currentLoginUser.getUserId()));

        // 客户分群信息 & 列表信息
        Map<String, Object> map = traderCustomerService.getPublicTraderCustomerVoPage(traderCustomerVo, page, userList);
        List<TraderCustomerVo> list = null;
        if (Objects.nonNull(map)) {
            list = (List<TraderCustomerVo>) map.get("list");
            page = (Page) map.get("page");
        }
        setCouponFlag(list);
        List<Integer> traderGroupIdList = new ArrayList<>();
        List<TraderGroup> traderGroupList = traderGroupService.getTraderGroupInfo(traderGroupIdList);
        mv.addObject("traderGroupList", traderGroupList);
        list = traderGroupService.setTraderGroupname(list, traderGroupList);
        mv.addObject("list", list);


        // 客户等级
        List<SysOptionDefinition> customerLevers = getSysOptionDefinitionList(SysOptionConstant.ID_11);
        mv.addObject("customerLevers", customerLevers);
        
        mv.addObject("invalidReasonList", InvalidReasonEnum.getReasonList());

        // 客户分群信息
        List<Integer> traderIds = list.stream().map(TraderCustomer::getTraderId).collect(Collectors.toList());
        Map<Integer, RTraderGroupJTrader> traderGroupMap = getTraderGroup(traderIds);
        mv.addObject("traderGroupMap", traderGroupMap);

        // 查询参数
        mv.addObject("traderCustomerVo", traderCustomerVo);
        mv.addObject("page", page);

        mv.setViewName("trader/public/index");
        return mv;
    }

    /**
     * 判断是否显示批量删除按钮
     * @parapanserId
     * @return
     */
    private boolean showBatchRemove(Integer userId){

        boolean result = false;
        if (!CollectionUtils.isEmpty(userIds)) {
            List<Integer> collect = userIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)) {
                if (collect.contains(userId)) {
                    result = true;
                }
            }
        }
        return result;
    }


    /**
     * 锁定公海列表
     *
     * @param publicTraderLockUpdateDto publicTraderLockUpdateDto
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/lockPublicTrader", method = RequestMethod.POST)
    public ResultInfo lockPublicTrader(@RequestBody PublicTraderLockUpdateDto publicTraderLockUpdateDto) throws Exception {

        ResultInfo resultInfo = null;
        try {
            resultInfo = publicTraderService.lockPublicTrader(publicTraderLockUpdateDto.getPublicCustomerRecordId());
        } catch (Exception e) {
            logger.warn("锁定客户失败，异常信息,",e);
            return new ResultInfo(-1,e.getMessage());
        }
        return resultInfo;
    }

    /**
     * 取消锁定公海列表
     *
     * @param publicTraderLockUpdateDto publicTraderLockUpdateDto
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/cancelLockPublicTrader", method = RequestMethod.POST)
    public ResultInfo cancelLockPublicTrader(@RequestBody PublicTraderLockUpdateDto publicTraderLockUpdateDto) throws Exception {

        ResultInfo resultInfo = null;
        try {
            resultInfo = publicTraderService.cancelLockPublicTrader(publicTraderLockUpdateDto.getPublicCustomerRecordId());
        } catch (Exception e) {
            logger.warn("锁定客户失败，异常信息,",e);
            return new ResultInfo(-1,e.getMessage());
        }
        return resultInfo;
    }

    /**
     * 公海额外约束
     *
     * @param traderCustomerVo traderCustomerVo traderCustomerVo
     * @param currentLoginUser currentLoginUser currentLoginUser
     */
    private void publicTraderConstraint(TraderCustomerVo traderCustomerVo, User currentLoginUser) {
        if (publicListIgnoreAuthUserId.contains(currentLoginUser.getUserId())){
            logger.info("当前用户无视约束：{}", currentLoginUser.getUsername());
            return;
        }

        // 本人可查看【自己】和【下级】和【下级的下级】的所在同省（见区域规则维护）客户数据
        // 1 查询自己及下级及下级的下级
        List<Integer> userIdList = new ArrayList<>();
        userIdList.add(currentLoginUser.getUserId());
        List<Integer> allChildrenIds = userService.getAllChildrenByParentUser(currentLoginUser.getUserId());
        userIdList.addAll(allChildrenIds);

        // 2 公海规则下，查询所有user的负责同省区域的所有销售对应客户
//        List<Integer> publicTraderServiceAllTraderInRegionsByUserIdList = publicTraderService.getAllTraderCustomerInRegionsByUserIdList(userIdList);

        //2、公海区域规则下，userList配置下同省下的所有用户集合
        List<Integer> userListByRegionRule = publicTraderService.getAllUserListByRegionRule(userIdList);

        if (allChildrenIds.size() > 0) {
            userListByRegionRule.addAll(userIdList);
        }

        userListByRegionRule = userListByRegionRule.stream().distinct().collect(Collectors.toList());
        //VDERP-10974 fix bug
        if (userListByRegionRule.size() == 0) {
            userListByRegionRule.add(-1);
        }
        traderCustomerVo.setOriginUserList(userListByRegionRule);

        // 未维护区域规则的销售客户信息，可以被上级以及上级的上级查看
//        if (allChildrenIds.size() > 0) {
//            List<Integer> noRegionUserIdList = publicTraderMapper.selectTraderIdListBySalesUserIdList(allChildrenIds);
//            publicTraderServiceAllTraderInRegionsByUserIdList.addAll(noRegionUserIdList);
//        }
//        traderCustomerVo.setPublicTraderCustomerIdList(publicTraderServiceAllTraderInRegionsByUserIdList.stream().distinct().collect(Collectors.toList()));

    }


    /**
     * 处理页面地区搜索展示
     *
     * @param mv               mv
     * @param traderCustomerVo traderCustomerVo
     */
    private void regionSelectOptionHandle(ModelAndView mv, TraderCustomerVo traderCustomerVo) {

        // 省
        List<Region> provinceList = regionService.getRegionByParentId(1);
        mv.addObject("provinceList", provinceList);

        // 市区
        if (ObjectUtils.notEmpty(traderCustomerVo.getZone())) {
            traderCustomerVo.setAreaId(traderCustomerVo.getZone());
            List<Region> list = regionService.getRegionByParentId(traderCustomerVo.getCity());
            mv.addObject("zoneList", list);
            List<Region> cys = regionService.getRegionByParentId(traderCustomerVo.getProvince());
            mv.addObject("cityList", cys);
        } else if (ObjectUtils.notEmpty(traderCustomerVo.getProvince()) && ObjectUtils.notEmpty(traderCustomerVo.getCity()) && ObjectUtils.isEmpty(traderCustomerVo.getZone())) {
            traderCustomerVo.setAreaId(traderCustomerVo.getCity());
            List<Region> list = regionService.getRegionByParentId(traderCustomerVo.getCity());
            mv.addObject("zoneList", list);
            List<Region> cys = regionService.getRegionByParentId(traderCustomerVo.getProvince());
            mv.addObject("cityList", cys);
        } else if (ObjectUtils.notEmpty(traderCustomerVo.getProvince()) && ObjectUtils.isEmpty(traderCustomerVo.getCity()) && ObjectUtils.isEmpty(traderCustomerVo.getZone())) {
            traderCustomerVo.setAreaId(traderCustomerVo.getProvince());
            List<Region> cys = regionService.getRegionByParentId(traderCustomerVo.getProvince());
            mv.addObject("cityList", cys);
        } else {
            traderCustomerVo.setAreaId(null);
        }
    }

    /**
     * copy by [com.vedeng.trader.controller.TraderCustomerController#setCouponFlag(java.util.List)]
     *
     * @param list list
     */
    private void setCouponFlag(List<TraderCustomerVo> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            for (TraderCustomerVo traderCustomerVo : list) {
                QueryCouponCenterRequest queryCouponCenterRequest = new QueryCouponCenterRequest();
                queryCouponCenterRequest.setPlatformId(traderCustomerVo.getBelongPlatform());
                queryCouponCenterRequest.setTraderId(traderCustomerVo.getTraderId());
                MyCouponDto myCouponDto = baseCouponService.getCouponByTraderId(queryCouponCenterRequest);
                if (myCouponDto == null) {
                    continue;
                }
                if (!CollectionUtils.isEmpty(myCouponDto.getAvailableCouponsList())
                        || !CollectionUtils.isEmpty(myCouponDto.getExpiredCouponsList())
                        || !CollectionUtils.isEmpty(myCouponDto.getUsedCouponsList())) {
                    traderCustomerVo.setCouponFlag(1);
                }
            }
        } catch (Exception e) {
            logger.error("setCouponFlag error", e);
        }
    }
    /**
     * @desc 撤销公海
     * <AUTHOR>
     * @param publicCustomerRecordId publicTraderLockUpdateDto
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/cancelPublicTraderJudge", method = RequestMethod.POST)
    public ResultInfo cancelPublicTraderJudge(Integer publicCustomerRecordId ,Integer dayInput,HttpServletRequest request) throws Exception {
        ResultInfo resultInfo = null;
        User currentUser = getSessionUser(request);
        try {
            resultInfo = publicTraderService.cancelPublicTraderJudge(publicCustomerRecordId,dayInput,currentUser);
        } catch (Exception e) {
            log.error("【cancelPublicTraderJudge】处理异常",e);
            return new ResultInfo(-1,e.getMessage());
        }
        return resultInfo;
    }

    /**
     * 批量撤销公海
     * @param publicCustomerRecordIds
     * @param dayInput
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/batchCancelPublicTraderJudge", method = RequestMethod.POST)
    public ResultInfo batchCancelPublicTraderJudge(Integer[] publicCustomerRecordIds ,Integer dayInput,HttpServletRequest request) {
        ResultInfo resultInfo = null;
        User currentUser = getSessionUser(request);
        try {
            resultInfo = publicTraderService.banchCancelPublicTraderJudge(publicCustomerRecordIds,dayInput,currentUser);
        } catch (Exception e) {
            log.error("【batchCancelPublicTraderJudge】处理异常",e);
            return new ResultInfo(-1,e.getMessage());
        }
        return resultInfo;
    }
}
