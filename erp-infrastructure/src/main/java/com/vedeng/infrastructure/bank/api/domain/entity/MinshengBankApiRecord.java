package com.vedeng.infrastructure.bank.api.domain.entity;

import lombok.Data;

import java.util.Date;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2024-08-01
 * @Description: TODO
 * @Version: 1.0
 */
@Data
public class MinshengBankApiRecord {
    /**
     * 主键
     */
    private Long recordId;

    /**
     * 请求报文
     */
    private String requestBody;

    /**
     * 响应报文
     */
    private String responseBody;

    /**
     * 客户端技术流水号
     */
    private String trnId;

    /**
     * 客户业务流水号
     */
    private String insId;

    /**
     * 接口类型
     */
    private Integer apiType;

    /**
     * 是否删除
     */
    private Boolean isDelete;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 添加人ID
     */
    private Integer creator;

    /**
     * 添加人名称
     */
    private String creatorName;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 更新人ID
     */
    private Integer updater;

    /**
     * 更新人名称
     */
    private String updaterName;
}
