package com.vedeng.erp.saleorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.erp.saleorder.dao.OrderGoodsLowerPriceMapper;
import com.vedeng.erp.saleorder.dao.QuoteInfoMapper;
import com.vedeng.erp.saleorder.domain.entity.OrderGoodsLowerPrice;
import com.vedeng.erp.saleorder.dto.QuoteGoodsInfoDto;
import com.vedeng.erp.saleorder.enums.LowPriceOrderTypeEnum;
import com.vedeng.erp.saleorder.service.NewSaleOrderService;
import com.vedeng.erp.saleorder.service.OrderGoodsLowerPriceApiService;
import com.vedeng.erp.system.common.enums.ChangeRelatedTableEnums;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.system.dao.VerifiesInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
public class OrderGoodsLowerPriceApiImpl implements OrderGoodsLowerPriceApiService {

    @Autowired
    OrderGoodsLowerPriceMapper orderGoodsLowerPriceMapper;

    @Autowired
    VerifiesInfoMapper verifiesInfoMapper;

    @Autowired
    QuoteInfoMapper quoteInfoMapper;

    @Autowired
    NewSaleOrderService newSaleOrderService;

    @Autowired
    SaleorderGoodsMapper saleorderGoodsMapper;

    @Autowired
    SaleorderMapper saleorderMapper;

    @Override
    public BigDecimal getCheckPrice(Integer orderId,Integer orderGoodsId,LowPriceOrderTypeEnum lowPriceOrderTypeEnum) {
        Integer orderType = lowPriceOrderTypeEnum.getOrderType();
        if (LowPriceOrderTypeEnum.QUOTATION.getOrderType().equals(orderType)){
            Integer status = verifiesInfoMapper.getOrderVerifyInfo(ChangeRelatedTableEnums.T_QUOTEORDER.getRealtedTable(),612,orderId);
            // 0审核中1审核通过2审核不通过',
            if (null == status || !ErpConst.ONE.equals(status)){
                log.info("{}单据id:{},产品ID:{}，查询审核流审核状态为：{}",lowPriceOrderTypeEnum.getDesc(),orderId,orderGoodsId,status);
                return null;
            }
        }
        //提交审核后被驳回或撤销的订单不展示tip
        List<OrderGoodsLowerPrice> lowerPriceGoodsList = orderGoodsLowerPriceMapper.getLowerPriceGoodsList(orderId,orderType,orderGoodsId);
        if (CollUtil.isEmpty(lowerPriceGoodsList)){
            log.info("{}单据id:{}，产品ID:{}，查询改低价列表结果为空",lowPriceOrderTypeEnum.getDesc(),orderId,orderGoodsId);
            return null;
        }
        BigDecimal checkPrice = Optional.ofNullable(lowerPriceGoodsList.get(0)).map(OrderGoodsLowerPrice::getCheckPrice).orElse(null);
        log.info("{}单据id:{},产品ID:{}，查询改低价产品价格：{}",lowPriceOrderTypeEnum.getDesc(),orderId,orderGoodsId,checkPrice);
        return checkPrice;
    }

    @Override
    public Integer addHandleOpinion(Integer goodsLowerPriceId, String handleOpinion) {
        return orderGoodsLowerPriceMapper.addHandleOpinion(goodsLowerPriceId,handleOpinion);
    }

    @Override
    public boolean isLowerPriceOrder(Integer saleorderId) {
        List<OrderGoodsLowerPrice> lowerPriceGoodsList = orderGoodsLowerPriceMapper.getLowerPriceGoodsList(saleorderId,LowPriceOrderTypeEnum.SALE_ORDER.getOrderType(), null);
        log.info("查询是否为低价订单，saleorderId:{},查询到的低价商品list:{}",saleorderId, JSONUtil.toJsonStr(lowerPriceGoodsList));
        return CollUtil.isNotEmpty(lowerPriceGoodsList);
    }

    @Override
    public void delOrderGoods(Integer saleorderId) {
        orderGoodsLowerPriceMapper.delByOrderIdAndType(saleorderId,LowPriceOrderTypeEnum.SALE_ORDER.getOrderType());
    }

    @Override
    public void add(Integer saleorderGoodsId, BigDecimal priceOrderPrice) {
        SaleorderGoods saleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(saleorderGoodsId);
        if (ObjectUtil.isNull(saleorderGoods)){
            log.error("T_ORDER_GOODS_LOWER_PRICE insert,saleorderGoods is null,saleorderGoodsId:{}",saleorderGoodsId);
            return;
        }
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleorderGoods.getSaleorderId());
        if (ObjectUtil.isNull(saleorder)){
            log.error("T_ORDER_GOODS_LOWER_PRICE insert,saleorder is null,saleordeId:{}",saleorderGoods.getSaleorderId());
            return;
        }
        OrderGoodsLowerPrice lowerPrice = new OrderGoodsLowerPrice();
        lowerPrice.setOrderType(LowPriceOrderTypeEnum.SALE_ORDER.getOrderType());
        lowerPrice.setOrderId(saleorder.getSaleorderId());
        lowerPrice.setOrderNo(saleorder.getSaleorderNo());
        lowerPrice.setOrderGoodsId(saleorderGoods.getSaleorderGoodsId());
        lowerPrice.setSku(saleorderGoods.getSku());
        lowerPrice.setNum(saleorderGoods.getNum());
        lowerPrice.setPrice(saleorderGoods.getPrice());
        lowerPrice.setCheckPrice(priceOrderPrice);
        lowerPrice.setOrderAddTime(new Date(saleorder.getAddTime()));
        lowerPrice.setIsDelete(0);
        lowerPrice.setAddTime(new Date());
        lowerPrice.setModTime(new Date());
        log.info("T_ORDER_GOODS_LOWER_PRICE insert,SaleorderGoodsLowerPrice:{}", JSONUtil.toJsonStr(lowerPrice));
        orderGoodsLowerPriceMapper.insertSelective(lowerPrice);
    }

    @Override
    public void addComment(Integer saleorderId, String comment) {
        orderGoodsLowerPriceMapper.addComment(saleorderId,comment);
    }

    @Override
    public BigDecimal getCheckPrice(Integer saleorderId, Integer saleorderGoodsId) {
        //提交审核后被驳回或撤销的订单不展示tip
        Integer status = verifiesInfoMapper.getSaleorderVerifyInfo(saleorderId);
        if (null == status || ErpConst.TWO.equals(status)){
            return null;
        }
        return orderGoodsLowerPriceMapper.getCheckPriceBySaleorderGoodsId(saleorderGoodsId);
    }


    /**
     * 尝试保存改低价
     * @param lowPriceOrderTypeEnum
     * @param orderId
     */
    @Override
    @Transactional
    public void tryInsertLowPrice(LowPriceOrderTypeEnum lowPriceOrderTypeEnum, Integer orderId) {
        log.info("tryInsertLowPrice{},orderId:{}",lowPriceOrderTypeEnum.getDesc(),orderId);
        if (LowPriceOrderTypeEnum.QUOTATION.getOrderType().equals(lowPriceOrderTypeEnum.getOrderType())){
            List<QuoteGoodsInfoDto> quoteGoodsInfoList = quoteInfoMapper.queryTraderInfoByQuoteId(orderId);
            orderGoodsLowerPriceMapper.delByOrderIdAndType(orderId,lowPriceOrderTypeEnum.getOrderType());
            for (QuoteGoodsInfoDto quoteGoodsInfoDto : quoteGoodsInfoList){
                BigDecimal price = (Objects.isNull(quoteGoodsInfoDto.getPrice())) ? BigDecimal.ZERO : quoteGoodsInfoDto.getPrice();

                Map<String, String> saleOrderGoodsPrice = newSaleOrderService.getPriceCenterBySKU(quoteGoodsInfoDto.getTraderId(), quoteGoodsInfoDto.getSku(), null);
                // 核价-销售价
                String salePriceStr = saleOrderGoodsPrice.get("salePrice");
                log.info("判断订单是否改低价，从价格中心获取价格：{},price:{}, sku:{}, orderId:{},traderId:{}", salePriceStr,quoteGoodsInfoDto.getPrice(),quoteGoodsInfoDto.getSku(),orderId,quoteGoodsInfoDto.getTraderId());
                if (StringUtils.isBlank(salePriceStr)){
                    log.info("报价单{}改低价SKU{}未获取到价格",orderId,quoteGoodsInfoDto.getSku());
                    continue;
                }
                BigDecimal salePrice = new BigDecimal(salePriceStr);
                if (ObjectUtil.isNotNull(salePrice) && BigDecimal.ZERO.compareTo(price) < 0 && price.compareTo(salePrice) < 0){
                    OrderGoodsLowerPrice lowerPrice = new  OrderGoodsLowerPrice();
                    lowerPrice.setOrderId(orderId)
                            .setOrderType(lowPriceOrderTypeEnum.getOrderType())
                            .setOrderNo(quoteGoodsInfoDto.getQuoteorderNo())
                            .setOrderGoodsId(quoteGoodsInfoDto.getQuoteorderGoodsId())
                            .setSku(quoteGoodsInfoDto.getSku())
                            .setNum(quoteGoodsInfoDto.getNum())
                            .setPrice(quoteGoodsInfoDto.getPrice())
                            .setCheckPrice(salePrice)
                            .setOrderAddTime(new Date())
                            .setIsDelete(0)
                            .setAddTime(new Date())
                            .setModTime(new Date());
                    orderGoodsLowerPriceMapper.insertSelective(lowerPrice);
                }
            }
        }
    }
}
