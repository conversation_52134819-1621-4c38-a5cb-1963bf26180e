package com.vedeng.flash.dto.vo;

import lombok.*;

import java.util.List;

/**
 * @program: erp.vedeng.com
 * @description: 移除定品参数
 * @author: Pusan
 * @create: 2021-05-26 18:43
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RemoveOrderingPoolOperateVO {

    /** 移除定品原因 */
    private String causeContent;

    /** 移除定品id集合 */
    private List<String> skuNoList;

}
