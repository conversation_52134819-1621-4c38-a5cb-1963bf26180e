package com.vedeng.erp.aftersale.service.impl;

import com.vedeng.orderstream.aftersales.dao.AfterSalesExpenditureRecordMapper;
import com.vedeng.orderstream.aftersales.model.AfterSalesExpenditureRecord;
import com.vedeng.erp.aftersale.service.AfterSalesExpenditureRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> simgo.wang
 * @date : 2021/10/13 20:29
 * @desc :
 */
@Service
public class AfterSalesExpenditureRecordServiceImpl implements AfterSalesExpenditureRecordService {

    @Qualifier("afterSalesExpenditureRecordMapper")
    @Autowired
    private AfterSalesExpenditureRecordMapper afterSalesExpenditureRecordMapper;

    @Override
    public int insert(AfterSalesExpenditureRecord afterSalesExpenditureRecord) {

        return afterSalesExpenditureRecordMapper.insert(afterSalesExpenditureRecord);

    }

    @Override
    public List<AfterSalesExpenditureRecord> selectByAfterSalesId(Integer afterSalesId) {

        return afterSalesExpenditureRecordMapper.selectByAfterSalesId(afterSalesId);

    }

}
