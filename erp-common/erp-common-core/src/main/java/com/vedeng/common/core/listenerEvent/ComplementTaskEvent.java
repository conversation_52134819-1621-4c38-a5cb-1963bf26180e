package com.vedeng.common.core.listenerEvent;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComplementTaskEvent<T> {
    private HttpServletRequest request;
    private String taskId;
    private String comment;
    private String assignee;
    private Map<String, Object> variables;
    private T Data;

}
