package com.vedeng.erp.aftersale.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

/**
 * @description 销售单和采购费用售后单退货数量关系表（细粒度到商品）
 * <AUTHOR>
 * @date 2023/1/5 13:56
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RExpenseAfterSalesJSaleorderDto extends BaseDto {
    /**
    * 主键
    */
    private Integer tRExpenseAfterSalesJSaleorderId;

    /**
    * 销售单表id
    */
    private Integer saleorderId;

    /**
    * 销售单明细表id
    */
    private Integer saleorderGoodsId;

    /**
    * 采购费用单id
    */
    private Long expenseAfterSalesId;

    /**
    * 采购费用单明细id
    */
    private Long expenseAfterSalesItemId;

    /**
    * skuId
    */
    private Integer skuId;

    /**
    * sku
    */
    private String skuNo;

    /**
    * 关联退货数量
    */
    private Integer afterSalesNum;

    /**
     * @组合对象@ 费用单id
     */
    private Integer buyorderExpenseId;

    /**
     * @组合对象@ 费用单商品id
     */
    private Integer buyorderExpenseItemId;
}