package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 销售售后处理调整单（逆向）
 * @author: Suqin
 * @date: 2023/2/24 16:18
 **/
@Service
@Slf4j
public class BatchAfterSaleSettlementAdjustmentProcessor implements ItemProcessor<BatchAfterSalesDto, BatchSaleSettlementAdjustmentAndItemDto> {

    @Autowired
    BatchSaleorderGoodsDtoMapper batchSaleorderGoodsDtoMapper;

    @Autowired
    BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;

    @Autowired
    BatchSaleorderDtoMapper batchSaleorderDtoMapper;

    @Autowired
    BatchSaleSettlementAdjustmentDtoMapper batchSaleSettlementAdjustmentDtoMapper;

    @Autowired
    BatchAfterSalesGoodsDtoMapper batchAfterSalesGoodsDtoMapper;

    @Autowired
    BatchSaleSettlementAdjustmentItemDtoMapper batchSaleSettlementAdjustmentItemDtoMapper;

    @Override
    public BatchSaleSettlementAdjustmentAndItemDto process(BatchAfterSalesDto batchAfterSalesDto) throws Exception {
        List<BatchSaleSettlementAdjustmentDto> dtoList = batchSaleSettlementAdjustmentDtoMapper.findByAfterSaleId(batchAfterSalesDto.getAfterSalesId());
        if (CollUtil.isNotEmpty(dtoList)) {
            log.info("售后单已生成调整单{}", JSON.toJSONString(dtoList));
            return null;
        }
        // 售后订单对应的销售订单
        BatchSaleorderDto saleorder = batchSaleorderDtoMapper.selectByPrimaryKey(batchAfterSalesDto.getOrderId());
        // 对应的出库单
        BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto = new BatchWarehouseGoodsOutInDto();
        batchWarehouseGoodsOutInDto.setRelateNo(saleorder.getSaleorderNo());
        batchWarehouseGoodsOutInDto.setOutInType(2);
        batchWarehouseGoodsOutInDto.setIsDelete(0);
        batchWarehouseGoodsOutInDto.setIsVirtual(0);
        List<BatchWarehouseGoodsOutInDto> outOrders = batchWarehouseGoodsOutInDtoMapper.findByRelateNo(batchWarehouseGoodsOutInDto);
        if (CollUtil.isEmpty(outOrders)) {
            log.info("售后单生成逆向调整单，未找到出库单，{}", JSON.toJSONString(batchAfterSalesDto));
            return null;
        }
        // 上次的售后调整单或首次销售调整单
        BatchSaleSettlementAdjustmentDto adjustmentDto = batchSaleSettlementAdjustmentDtoMapper.selectBySaleorderIdLatest(saleorder.getSaleorderId());
        if (Objects.isNull(adjustmentDto)) {
            // 销售订单是否有对应的销售调整单
            BatchSaleSettlementAdjustmentDto queryAdjustment = new BatchSaleSettlementAdjustmentDto();
            queryAdjustment.setSaleorderId(saleorder.getSaleorderId());
            queryAdjustment.setAdjustmentType(1);
            List<BatchSaleSettlementAdjustmentDto> saleOrderAdjustment = batchSaleSettlementAdjustmentDtoMapper.findByItem(queryAdjustment);
            if (CollUtil.isEmpty(saleOrderAdjustment)) {
                log.error("售后单生成逆向调整单，销售订单已出库，但未产生调整单：{}", saleorder);
                throw new KingDeeException("售后单生成逆向调整单，销售订单已出库，但未产生调整单");
            }
            adjustmentDto = CollUtil.getFirst(saleOrderAdjustment);
        }
        // 上次调整单明细
        List<BatchSaleSettlementAdjustmentItemDto> adjustmentItems = batchSaleSettlementAdjustmentItemDtoMapper.findByAdjustmentId(adjustmentDto.getSaleSettlementAdjustmentId());
        // 售后商品
        List<BatchAfterSalesGoodsDto> afterSaleGoodsList = batchAfterSalesGoodsDtoMapper.findByAfterSalesId(batchAfterSalesDto.getAfterSalesId());
        // 售后商品过滤虚拟商品
        afterSaleGoodsList = afterSaleGoodsList.stream().filter(a -> {
            Integer result = batchSaleorderGoodsDtoMapper.selectIsVirtualGoods(a.getGoodsId());
            return Objects.isNull(result) || KingDeeConstant.ZERO.equals(result);
        }).collect(Collectors.toList());
        //过滤掉不生成调整单的6个固定sku
        List<String> virtulSkus = Arrays.asList("V127063", "V251526", "V256675", "V253620", "V251462", "V140633");
        afterSaleGoodsList = afterSaleGoodsList.stream().filter(goods -> !virtulSkus.contains(goods.getSku())).collect(Collectors.toList());
        if (CollUtil.isEmpty(afterSaleGoodsList)) {
            log.info("售后单生成逆向调整单，售后单id={}无实物商品，不生成调整单", batchAfterSalesDto.getAfterSalesId());
            return null;
        }
        // 售后调整单
        BatchSaleSettlementAdjustmentDto adjustment = new BatchSaleSettlementAdjustmentDto();
        adjustment.setSaleorderId(saleorder.getSaleorderId());
        adjustment.setSaleorderNo(saleorder.getSaleorderNo());
        adjustment.setAfterSaleId(batchAfterSalesDto.getAfterSalesId());
        adjustment.setAfterSaleNo(batchAfterSalesDto.getAfterSalesNo());
        adjustment.setAdjustmentType(2);
        // 待落表的调整单明细
        List<BatchSaleSettlementAdjustmentItemDto> newAdjustmentItems = new ArrayList<>();
        // 新含税应收单价合计
        BigDecimal adjustmentAmountTotal = BigDecimal.ZERO;
        // 新含税销售单价合计
        BigDecimal receivedAmount = BigDecimal.ZERO;
        // 新分摊后含税单价合计
        BigDecimal apportionAmountTotal = BigDecimal.ZERO;
        Map<Integer, BatchAfterSalesGoodsDto> afterSalesGoodsDtoMap = afterSaleGoodsList.stream().collect(Collectors.toMap(BatchAfterSalesGoodsDto::getOrderDetailId, a -> a, (k1, k2) -> k1));
        // 依据上次调整单获取到对应的调整金额
        for (BatchSaleSettlementAdjustmentItemDto saleAdjustmentItem : adjustmentItems) {
            BatchSaleSettlementAdjustmentItemDto afterSalesAdjustmentItem = new BatchSaleSettlementAdjustmentItemDto();
            afterSalesAdjustmentItem.setBatchNumber(saleAdjustmentItem.getBatchNumber());
            afterSalesAdjustmentItem.setBarcodeFactory(saleAdjustmentItem.getBarcodeFactory());
            afterSalesAdjustmentItem.setSkuId(saleAdjustmentItem.getSkuId());
            afterSalesAdjustmentItem.setSku(saleAdjustmentItem.getSku());
            afterSalesAdjustmentItem.setSkuName(saleAdjustmentItem.getSkuName());
            afterSalesAdjustmentItem.setDifferenceAmount(saleAdjustmentItem.getDifferenceAmount());
            afterSalesAdjustmentItem.setActualAmount(saleAdjustmentItem.getActualAmount());
            afterSalesAdjustmentItem.setSaleorderGoodsId(saleAdjustmentItem.getSaleorderGoodsId());

            BatchAfterSalesGoodsDto batchAfterSalesGoodsDto = afterSalesGoodsDtoMap.get(saleAdjustmentItem.getSaleorderGoodsId());
            if (Objects.nonNull(batchAfterSalesGoodsDto)) {
                afterSalesAdjustmentItem.setNum(saleAdjustmentItem.getNum() - batchAfterSalesGoodsDto.getNum());
            } else {
                afterSalesAdjustmentItem.setNum(saleAdjustmentItem.getNum());
            }
            if (afterSalesAdjustmentItem.getNum() <= 0) {
                continue;
            }
            BigDecimal totalDifferenceAmount = afterSalesAdjustmentItem.getDifferenceAmount().multiply(BigDecimal.valueOf(afterSalesAdjustmentItem.getNum()));
            BigDecimal totalActualAmount = afterSalesAdjustmentItem.getActualAmount().multiply(BigDecimal.valueOf(afterSalesAdjustmentItem.getNum()));
            adjustmentAmountTotal = adjustmentAmountTotal.add(totalDifferenceAmount);
            receivedAmount = receivedAmount.add(totalActualAmount);
            newAdjustmentItems.add(afterSalesAdjustmentItem);
        }
        if (CollUtil.isEmpty(newAdjustmentItems)) {
            log.info("售后单生成逆向调整单，售后单id={}，该订单已全部退货，不生成调整单", batchAfterSalesDto.getAfterSalesId());
            return null;
        }
        Map<Integer, BatchSaleSettlementAdjustmentItemDto> adjustmentItemDtoMap = adjustmentItems.stream().collect(Collectors.toMap(BatchSaleSettlementAdjustmentItemDto::getSaleorderGoodsId, a -> a, (k1, k2) -> k1));
        for (BatchSaleSettlementAdjustmentItemDto item : newAdjustmentItems) {
            // 分摊后含税价（分摊价格）
            BigDecimal apportionAmount = item.getDifferenceAmount().multiply(receivedAmount).divide(adjustmentAmountTotal, 2, RoundingMode.HALF_UP);
            // 上一个调整单中对应商品的分摊价格
            BigDecimal beforeApportionAmount = adjustmentItemDtoMap.get(item.getSaleorderGoodsId()).getApportionAmount();
            // 需调整含税价（调整金额）= 本次分摊金额 - 上次分摊金额
            BigDecimal adjustmentAmount = apportionAmount.subtract(beforeApportionAmount);
            item.setApportionAmount(apportionAmount);
            item.setAdjustmentAmount(adjustmentAmount);
            apportionAmountTotal = apportionAmountTotal.add(apportionAmount.multiply(BigDecimal.valueOf(item.getNum())).setScale(2, RoundingMode.HALF_UP));
        }
        // 尾差金额
        BigDecimal differenceAmount = receivedAmount.subtract(apportionAmountTotal);
        adjustment.setTotalAmount(adjustmentAmountTotal);
        adjustment.setReceivedAmount(receivedAmount);
        adjustment.setDifferenceAmount(differenceAmount);

        BatchSaleSettlementAdjustmentAndItemDto batchSaleSettlementAdjustmentAndItemDto = new BatchSaleSettlementAdjustmentAndItemDto();
        batchSaleSettlementAdjustmentAndItemDto.setBatchSaleSettlementAdjustmentDto(adjustment);
        batchSaleSettlementAdjustmentAndItemDto.setBatchSaleSettlementAdjustmentItemDtoList(newAdjustmentItems);
        return batchSaleSettlementAdjustmentAndItemDto;
    }

}

