package com.vedeng.aftersales.model;

import lombok.Data;

import java.util.Date;

@Data
public class TraderAssociatedLogDo {


    private Long id;

    /**
     * ERP注册用户唯一编号
     */
    private Integer erpAccountId;

    /**
     * 记录日志时注册用户归属平台
     *
     * @see com.vedeng.common.constant.BelongPlatformEnum
     */
    private Integer registeredAccountPlatform;

    private Integer traderId;

    /**
     * 操作类型，1:绑定日志，2:解除绑定日志
     */
    private Integer operationType;

    private String operationDescription;

    private String reason;

    private String remark;

    private Integer operatorId;

    private String operatorName;

    private String operatorOrganization;

    private Date operateTime;
}