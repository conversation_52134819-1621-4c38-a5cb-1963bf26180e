package com.vedeng.common.orderstrategy;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.vedeng.common.constant.Constants;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.erp.saleorder.api.SaleorderSyncService;
import com.vedeng.erp.saleorder.enums.PCOrderStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleorderSyncEnum;
import com.vedeng.goods.dao.CoreOperateInfoGenerateExtendMapper;
import com.vedeng.goods.model.GoodsAttachment;
import com.vedeng.logistics.dao.LogisticsMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.Logistics;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.order.dao.SaleorderGenerateMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGenerate;
import com.vedeng.order.model.vo.OrderData;
import com.vedeng.order.model.vo.OrderGoodsData;
import com.vedeng.order.model.vo.OrderLogisticsData;
import com.vedeng.order.model.vo.OrderLogisticsGoodsData;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.ordergoods.dao.SaleorderGoodsGenerateMapper;
import com.vedeng.ordergoods.model.SaleorderGoodsGenerate;
import com.vedeng.ordergoods.model.SaleorderGoodsGenerateExample;
import com.vedeng.system.dao.TraderAddressGenerateMapper;
import com.vedeng.trader.dao.TraderAddressMapper;
import com.vedeng.trader.dao.TraderContactGenerateMapper;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.trader.model.WebAccount;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class OrderCheckedPushPCStrategy extends OrderStrategy {

    private static Logger logger = LoggerFactory.getLogger(OrderCheckedPushPCStrategy.class);

    @Autowired
    SaleorderGenerateMapper saleorderGenerateMapper;
    @Autowired
    private SaleorderGoodsGenerateMapper saleorderGoodsGenerateMapper;

    @Autowired
    TraderContactGenerateMapper traderContactGenerateMapper;
    @Autowired
    TraderAddressGenerateMapper traderAddressGenerateMapper;
    @Autowired
    private TraderAddressMapper traderAddressMapper;
    @Autowired
    private CoreOperateInfoGenerateExtendMapper coreOperateInfoGenerateExtendMapper;
    @Autowired
    private WebAccountMapper webAccountMapper;
    @Value("${mjx_url}")
    protected String mjxUrl;
    @Autowired
    @Qualifier("expressService")
    private ExpressService expressService;

    @Autowired
    @Qualifier("logisticsMapper")
    private LogisticsMapper logisticsMapper;
    @Autowired
    SaleorderService saleorderService;
    @Resource
    private SaleorderSyncService saleorderSyncService;

//    @Value("${jd.upload.tradeId}")
//    private Integer jdTraderId;

    @Value("${jd.upload.tradeIdList:194723}")
    private String jdTraderIds;


    @Override
    public void execute(Object object) {
        Saleorder saleorder = (Saleorder) object;
        if (saleorder == null || saleorder.getOrderType() != 0) {
            return;
        }
        //
        logger.info("pushVSOrder: 生效" + saleorder.getSaleorderNo() + "\t" + saleorder.getTraderContactMobile() + "\t" + saleorder.getSendToPc());
        WebAccount web = webAccountMapper.getWenAccountInfoByMobile(saleorder.getTraderContactMobile());
        //没有推送 或者 已经推送且已经生效且已经关联
        boolean needToSendToPc = saleorder.getSendToPc() == null && web != null ;

        String[] idsArray = jdTraderIds.split(",");
        List<Integer> traderIdList = Arrays.stream(idsArray)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        if (web==null||traderIdList.contains(saleorder.getTraderId())) {
            return;
        }
//        if(saleorder.getValidStatus()!=null&&saleorder.getValidStatus()==0){
//            return;
//        }
        OrderData orderData = new OrderData();
        orderData.setOrderNo(saleorder.getSaleorderNo());
        orderData.setCompanyId(saleorder.getTraderId());
        orderData.setCompanyName(saleorder.getTraderName());
        orderData.setOrderSrc(4);

        orderData.setAccountId(web.getWebAccountId());//待定  =用户ID
        orderData.setSsoAccountId(web.getSsoAccountId());
        orderData.setTotalMoney(saleorder.getTotalAmount());

        // TraderAddress traderAddress = traderAddressMapper.getAddressInfoById(saleorder.getTakeTraderAddressId(), 1);
        orderData.setDeliveryAddressId(saleorder.getTakeTraderAddressId());
        orderData.setDeliveryUserName(saleorder.getTakeTraderContactName());

        //  orderData.setDeliveryUserArea(saleorder.getTakeTraderArea());
        orderData.setDeliveryUserAddress(saleorder.getTakeTraderAddress());
        orderData.setDeliveryUserPhone(saleorder.getTakeTraderContactMobile());
        // orderData.setDeliveryUserTel(saleorder.getTakeTraderContactTelephone());
        orderData.setIsSendInvoice(saleorder.getIsSendInvoice());//是否寄送发票
        orderData.setInvoiceType(saleorder.getInvoiceType());

        orderData.setInvoiceUserId(saleorder.getInvoiceTraderContactId());
        orderData.setInvoiceUserName(saleorder.getInvoiceTraderContactName());
        orderData.setInvoiceUserAddress(saleorder.getInvoiceTraderAddress());
        orderData.setInvoiceUserPhone(saleorder.getInvoiceTraderContactTelephone());
        // orderData.setAdditionalClause(saleorder.getAdditionalClause());

        SaleorderGoodsGenerateExample example = new SaleorderGoodsGenerateExample();
        example.createCriteria().andSaleorderIdEqualTo(saleorder.getSaleorderId())
                .andIsDeleteEqualTo(0);
        List<SaleorderGoodsGenerate> goodsList = saleorderGoodsGenerateMapper.selectByExample(example);
//		orderData.setDeliveryAreaIds(traderAddress.getAreaIds());
        // List<SaleorderGoods> goodsList = getSaleorderGoodsById(saleorder);
        List<OrderGoodsData> goodsList2 = new ArrayList<OrderGoodsData>();
        Set<String> goodsKinds = Sets.newHashSet();
        int goodsCount = 0;
        if (CollectionUtils.isNotEmpty(goodsList)) {
            for (SaleorderGoodsGenerate s : goodsList) {
                goodsKinds.add(s.getSku());
                goodsCount += s.getNum();

                OrderGoodsData orderGoodsData = new OrderGoodsData();
                orderGoodsData.setSaleorderGoodsId(s.getSaleorderGoodsId());
                orderGoodsData.setSkuNo(s.getSku());
                orderGoodsData.setGoodsName(s.getGoodsName());
                orderGoodsData.setGoodsNum(s.getNum());
                orderGoodsData.setGoodsSku(s.getSku());
                orderGoodsData.setGoodsModel(s.getModel());
                orderGoodsData.setSalesMoney(s.getPrice());
                orderGoodsData.setSpecs(s.getModel());//vs订单暂时不区分规格型号
                // 赠品标识
                orderGoodsData.setGiftFlag(s.getIsGift() != null && Constants.ONE.equals(s.getIsGift()) ? "Y" : "N");

                try {
                    List<GoodsAttachment> attachmentList = coreOperateInfoGenerateExtendMapper.selectSkuOperateAttachmentList(s.getGoodsId(), 1001);
                    if (CollectionUtils.isNotEmpty(attachmentList)) {
                        GoodsAttachment attachment = attachmentList.get(0);
                        if (StringUtils.isNotBlank(attachment.getUri())) {
                            if(StringUtils.startsWith(attachment.getUri(),"/file/display")){
                                orderGoodsData.setGoodsUri("https://file.vedeng.com" + attachment.getUri().replace("default", "800x800"));
                            }else{
                                orderGoodsData.setGoodsUri("https://file1.vedeng.com" + attachment.getUri().replace("default", "800x800"));
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error("pushVSOrder:", e);
                }
                goodsList2.add(orderGoodsData);
            }
        }
        List<Express> expressList = getExpressList(goodsList);
        transToPcStatus(saleorder, orderData, expressList);
        //待收货，补充物流信息
        addLogisticsData(saleorder, web, orderData, goodsList2, expressList);

        orderData.setProductTypeNum(goodsKinds.size());
        orderData.setProductNum(goodsCount);
        orderData.setGoodsList(goodsList2);
        sendMessage(orderData, saleorder );
    }

    private void addLogisticsData(Saleorder saleorder, WebAccount web, OrderData orderData, List<OrderGoodsData> goodsList2, List<Express> expressList) {
        if (orderData.getOrderStatus().equals(4) && CollectionUtils.isNotEmpty(expressList)) {
            List<OrderLogisticsData> orderLogisticsList = Lists.newArrayList();
            if (CollectionUtils.isEmpty(expressList)) {
                return;
            }
            expressList.forEach(item -> {
                OrderLogisticsData orderLogisticsData = new OrderLogisticsData();
                orderLogisticsData.setAccountId(web.getWebAccountId());
                orderLogisticsData.setOrderNo(saleorder.getSaleorderNo());
                orderLogisticsData.setLogisticsNo(item.getLogisticsNo());
                //只有普发
                orderLogisticsData.setLogisticsType(2);//物流类型 1、直发 2、普法
                //orderLogisticsData.setState(item.getArrivalStatus());//状态 101待发、102出库、3签收
                if (item.getArrivalStatus() != null && item.getArrivalStatus().equals(2)) {
                    orderLogisticsData.setState(3);
                } else if (item.getArrivalStatus() != null && item.getArrivalStatus() < 2) {
                    orderLogisticsData.setState(102);
                }
                Logistics logistics = logisticsMapper.getLogisticsById(item.getLogisticsId());
                if (logistics != null) {
                    orderLogisticsData.setLogisticsCode(logisticsMapper.getLogisticsCode(logistics.getName()));
                }
                orderLogisticsData.setCheckoutTime(new Date(item.getAddTime()));//出库时间
                orderLogisticsData.setSignTime(new Date(item.getArrivalTime()));//签收时间
                if (CollectionUtils.isNotEmpty(item.getExpressDetail())) {
                    List<OrderLogisticsGoodsData> orderLogisticsGoodsDataList = Lists.newArrayList();
                    item.getExpressDetail().forEach(detailItem -> {
                        OrderLogisticsGoodsData goodsData = new OrderLogisticsGoodsData();

                        goodsData.setGoodsNum(detailItem.getNum());
                        if (CollectionUtils.isNotEmpty(goodsList2)) {
                            for (OrderGoodsData goods : goodsList2) {
                                if (StringUtils.equalsIgnoreCase(goods.getSaleorderGoodsId() + "", detailItem.getRelatedId() + "")) {
                                    goodsData.setSkuNo(goods.getSkuNo());
                                    goodsData.setGoodsName(goods.getGoodsName());
                                    goodsData.setImage(goods.getGoodsUri());
                                    goodsData.setSpecs(goods.getSpecs());
                                    orderLogisticsGoodsDataList.add(goodsData);
                                    break;
                                }
                            }
                        }
                    });
                    orderLogisticsData.setOrderGoodsLogisticsList(orderLogisticsGoodsDataList);
                }
                orderLogisticsList.add(orderLogisticsData);
            });

        }
    }

    private List<Express> getExpressList(List<SaleorderGoodsGenerate> goodsList) {
        try {
            Express express = new Express();
            express.setBusinessType(SysOptionConstant.ID_496);
            express.setCompanyId(1);
            List<Integer> relatedIds = new ArrayList<Integer>();
            if (CollectionUtils.isNotEmpty(goodsList)) {
                for (SaleorderGoodsGenerate sg : goodsList) {
                    relatedIds.add(sg.getSaleorderGoodsId());
                }
            }
            express.setRelatedIds(relatedIds);
            List<Express> expressList = expressService.getExpressList(express);
            return expressList;
        } catch (Exception e) {
            logger.error("订单审核通过之后，发送物流信息出错，获取快递列表失败 :", e);
        }
        return Collections.emptyList();
    }

    //转换成PC状态

    /**
     * 贝登前台状态	ERP状态
     * 订单状态	生效状态	其他
     * 0审核中	非已关闭	未生效	/
     * 2待付款	非已关闭	已生效	未付款
     * 7部分付款	非已关闭	已生效	部分付款
     * 3待发货	非已关闭	已生效	全部付款+无快递信息
     * 4待收货	非已关闭	已生效	有快递信息
     * 5已完成	非已关闭	已生效	全部收货
     * 6已取消 	已关闭	/	/
     * 1待确认(已经不用)
     *
     * @param saleorder
     * @param orderData
     * @param expressList
     */
    public    void transToPcStatus(Saleorder saleorder, OrderData orderData, List<Express> expressList) {
        try {
            Saleorder saleorderDb=saleorderService.getSaleOrderInfo(saleorder);
            saleorder.setPaymentStatus(saleorderDb.getPaymentStatus());
            saleorder.setStatus(saleorderDb.getStatus());
            saleorder.setArrivalStatus(saleorderDb.getArrivalStatus());

        }catch (Exception e){
            logger.error("",e);
        }
        if (saleorder.getStatus() != null && saleorder.getStatus() == 2) {
            orderData.setOrderStatus(PCOrderStatusEnum.FINISH.status());//5、已完成
        } else if (saleorder.getStatus() != null && saleorder.getStatus() == 3) {
            orderData.setOrderStatus(PCOrderStatusEnum.CANCEL.status());//6、已取消
        } else if (saleorder.getPaymentStatus() != null && saleorder.getPaymentStatus() == 1) {
            orderData.setOrderStatus(PCOrderStatusEnum.HALF_PAID.status());//7、部分付款
        } else if (saleorder.getPaymentStatus() != null && saleorder.getPaymentStatus() == 0) {
            orderData.setOrderStatus(PCOrderStatusEnum.PRE_PAY.status());
            //发货状态0未发货 1部分发货 2全部发货
        } else if (saleorder.getPaymentStatus() != null && saleorder.getPaymentStatus() == 2) {
                if (CollectionUtils.isNotEmpty(expressList)) {
                    //有快递
                    orderData.setOrderStatus(PCOrderStatusEnum.PRE_RECEIVE.status());
                } else {
                    //无快递
                    orderData.setOrderStatus(PCOrderStatusEnum.PRE_DELIVERY.status());
                }
        } else if (saleorder.getArrivalStatus() != null && saleorder.getArrivalStatus() == 2) {
            orderData.setOrderStatus(PCOrderStatusEnum.FINISH.status());
        } else {
            orderData.setOrderStatus(PCOrderStatusEnum.PRE_PAY.status());
        }
    }

    private void sendMessage(OrderData orderData, Saleorder saleorder) {
        String json = JsonUtils.convertObjectToJsonStr(orderData);
        try {
            List<OrderData> list = Lists.newArrayList();
            list.add(orderData);
            String url = mjxUrl+"/order/pushVSOrder";

            logger.info("pushVSOrder 开始推送VS订单:" + json);
            net.sf.json.JSONObject result2 = NewHttpClientUtils.httpPost(url, JSONArray.toJSONString(list));
            logger.info("pushVSOrder  推送VS订单结果:" + result2 );
                 SaleorderGenerate order = new SaleorderGenerate();
                order.setSaleorderId(saleorder.getSaleorderId());
                order.setSendToPc(1);
                order.setModTime(System.currentTimeMillis());
                saleorderGenerateMapper.updateByPrimaryKeySelective(order);

            saleorderSyncService.syncSaleorderStatus2Mjx(saleorder.getSaleorderId()
            ,PCOrderStatusEnum.get(orderData.getOrderStatus()), SaleorderSyncEnum.AFTER_PUSH);

        } catch (Exception e) {
            logger.error("pushVSOrder{}",json, e);
        }
    }
}
