package com.smallhospital.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.smallhospital.constant.HospitalConstant;
import com.smallhospital.constant.UrlConstant;
import com.smallhospital.dao.*;
import com.smallhospital.dto.*;
import com.smallhospital.model.ContractSku;
import com.smallhospital.model.ElContractSku;
import com.smallhospital.model.vo.ELContractVO;
import com.smallhospital.model.vo.ElContractSkuVO;
import com.smallhospital.service.ELContractService;
import com.smallhospital.service.ELContractSkuService;
import com.smallhospital.service.impl.remote.SynContractModifyService;
import com.smallhospital.service.impl.remote.SynContractTerminateService;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.smallhospital.controller.ElSaleContractController.SYSTEM_CREATOR_USER;

@Service
public class ELContractServiceImpl implements ELContractService {

    private Logger logger= LoggerFactory.getLogger(ELContractServiceImpl.class);

    @Value("${api_url}")
    private String apiUrl;

    @Autowired
    private ElContractSkuMapper contractSkuMapper;

    @Autowired
    private ElContractMapper contractMapper;

    @Autowired
    private ELContractModifyMapper modifyMapper;

    @Autowired
    private ELContractSkuModifyMapper skuModifyMapper;

    @Autowired
    private ElContractSkuMapper skuMapper;

    @Autowired
    private ElSkuMapper elSkuMapper;

    @Autowired
    private SynContractTerminateService synContractTerminateService;

    @Autowired
    private SynContractModifyService synContractModifyService;

    @Override
    public List<ContractSku> checkSkuIdsIsOnSale(ELContractSkuDTO elContractDTO) {
        logger.info("校验SKU是否上架入参:"+ JSON.toJSONString(elContractDTO));
        TypeReference<RestfulResult<List<ContractSku>>> typeReference = new TypeReference<RestfulResult<List<ContractSku>>>(){};
        RestfulResult<List<ContractSku>> restfulResult = HttpRestClientUtil.restPost(apiUrl + UrlConstant.CHECK_SKU_IS_ON_SALE , typeReference,null,elContractDTO);
        logger.info("校验SKU是否上架出参:" + JSON.toJSONString(restfulResult));
        if (restfulResult !=null && restfulResult.isSuccess() && CollectionUtils.isNotEmpty(restfulResult.getData())){
            return restfulResult.getData();
        }
        return new ArrayList<>();
    }

    @Override
    public List<Integer> findNotExitsIds(List<Integer> skuIds) {
        return contractMapper.findNotExitsIds(skuIds);
    }

    @Override
    public List<ELContractVO> querylistPage(ELContractVO contract, Page page) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("contract", contract);
        map.put("page", page);
        return contractMapper.querylistPage(map);
    }

    @Override
    public int saveContractInfo(ELContractVO contract) {
        if(contract == null){
            return 0;
        }
        contract.setAddTime(System.currentTimeMillis());
        contract.setUpdateTime(System.currentTimeMillis());
        this.contractMapper.insertSelective(contract);
        return contract.getElContractId();
    }

    /**
     * 校验合同的有效期
     * @param orderDto
     * @return
     */
    @Override
    public ValidatorResult validatorContractExpire(ELOrderDto orderDto) {

        //客户id
        Integer traderId = orderDto.getPurchaserId();

        //订购商品的sku列表
        List<Integer> skuIds = orderDto.getDDMX().stream().map(ELOrderItemDto::getProductId).collect(Collectors.toList());

        List<Integer> contractIds = contractMapper.findContractIdsByCusAndSkuIds(traderId,skuIds);

        ValidatorResult result = null;

        for(Integer contractId : contractIds){

            ELContractVO contractVO = this.contractMapper.selectByPrimaryKey(contractId);

            result = contractValidator(contractVO);

            if(!result.getResult()){
                break;
            }
        }

        return result;

    }

    /**
     * 校验合同是否过期
     * @param contract
     * @return
     */
    private ValidatorResult contractValidator(ELContractVO contract) {

        if(contract.getEffctiveStatus() == 0){
            return ValidatorResult.newBuild().setMessage("合同id="+contract.getElContractId()+"已经失效");
        }

        if(!(contract.getContractValidityDateStart() <= System.currentTimeMillis() && System.currentTimeMillis() <= contract.getContractValidityDateEnd())){
            return ValidatorResult.newBuild().setMessage("合同id="+contract.getElContractId()+"已经过了有效期");
        }

        return ValidatorResult.newBuild().setResult(true);
    }

    @Override
    public ELContractVO getContractModifyInfo(Integer contractId) {
        return modifyMapper.selectByContractId(contractId);
    }

    @Override
    public ELContractVO findById(Integer contractId) {
        return contractMapper.selectByPrimaryKey(contractId);
    }

    @Override
    public ResultInfo saveModifyContract(ELContractVO contract, User user,List<ElContractSku> skuList) {
        ELContractVO preModifyContract = getContractModifyInfo(contract.getElContractId());


        if (preModifyContract != null) {
            preModifyContract.setUpdator(user.getUserId());
            preModifyContract.setSignDate(DateUtil.convertLong(contract.getSignDateStr(), DateUtil.DATE_FORMAT));
            preModifyContract.setContractValidityDateStart(DateUtil.convertLong(contract.getContractValidityDateStartStr(), DateUtil.DATE_FORMAT));
            preModifyContract.setContractValidityDateEnd(DateUtil.convertLong(contract.getContractValidityDateEndStr(), DateUtil.DATE_FORMAT));
            skuModifyMapper.deleteByContractModifyId(preModifyContract.getElContractModifyId());
            modifyMapper.updateByPrimaryKeySelective(preModifyContract);
            if (CollectionUtils.isNotEmpty(skuList)) {
                for (ElContractSku s : skuList) {
                    if (s == null) {
                        continue;
                    }
                    s.setContractModifyId(preModifyContract.getElContractModifyId());
                }
                skuModifyMapper.batchAddContractSkus(skuList);
            }
        } else {
            contract.setCreator(user.getUserId());
            contract.setSignDate(DateUtil.convertLong(contract.getSignDateStr(), DateUtil.DATE_FORMAT));
            contract.setContractValidityDateStart(DateUtil.convertLong(contract.getContractValidityDateStartStr(), DateUtil.DATE_FORMAT));
            contract.setContractValidityDateEnd(DateUtil.convertLong(contract.getContractValidityDateEndStr(), DateUtil.DATE_FORMAT));
            modifyMapper.insertSelective(contract);
            if (CollectionUtils.isNotEmpty(skuList)) {
                for (ElContractSku s : skuList) {
                    if (s == null) {
                        continue;
                    }
                    s.setContractModifyId(contract.getElContractModifyId());
                }
                skuModifyMapper.batchAddContractSkus(skuList);
            }
        }
        ELContractVO elContract = new ELContractVO();
        elContract.setElContractId(contract.getElContractId());
        elContract.setModifyStatus(HospitalConstant.CONTRACT_ONMODIFY);
        elContract.setAuditStatus(HospitalConstant.CONTRACT_NOT_AUDIT);
        contractMapper.updateByPrimaryKeySelective(elContract);

        return new ResultInfo(0, "操作成功");
    }

    @Override
    public void updateContract(ELContractVO contract) {
        contractMapper.updateByPrimaryKeySelective(contract);
    }

    @Override
    public List<Integer> findOtherValidSkus(ELContractVO contractVO) {
        return contractMapper.findOtherValidSkus(contractVO);
    }

    @Override
    public List<ELContractVO> findByTradeId(Integer traderId) {
        return contractMapper.findByTradeId(traderId);
    }

    @Override
    public ResultInfo startTermination(Integer contractId) {
        ELContractVO elContract = new ELContractVO();
        elContract.setElContractId(contractId);
        elContract.setTerminateStatus(HospitalConstant.SALEMANAGER_TERMINATE_STATUS);
        contractMapper.updateByPrimaryKeySelective(elContract);
        return new ResultInfo(0,"操作成功");
    }

    @Override
    public ResultInfo checkTermination(ELContractVO elContractVO, Integer type) {
        if(elContractVO.getAuditStatus()==null){
            return new ResultInfo(-1,"终止合同状态不得为空");
        }
        ELContractVO preContract=contractMapper.selectByPrimaryKey(elContractVO.getElContractId());
        if(preContract==null){
            return new ResultInfo(-1,"该合同在系统中已不存在");
        }
        elContractVO.setTerminateCheckDesc(elContractVO.getAuditDesc());
        elContractVO.setAuditDesc(null);
        if (HospitalConstant.AUDIT_STATUS_PASSED.equals(elContractVO.getAuditStatus())) {
            //审核通过
            if (type == HospitalConstant.MANAGER_TERMINATE_CHECK_TYPE) {
                //操作类型为销售主管，总监审核
                if(!HospitalConstant.MANAGER_TERMINATE_CHECK_TYPE.equals(preContract.getTerminateStatus())){
                    return new ResultInfo(-1,"审批操作已完成");
                }
                if (HospitalConstant.CONTRACT_SYNC_SUCCESS.equals(preContract.getContractSynStatus())) {
                    synContractTerminateService.syncData(elContractVO.getElContractId());
                    elContractVO.setTerminateStatus(HospitalConstant.CUSTOMER_TERMINATE_STATUS);
                    elContractVO.setEffctiveStatus(HospitalConstant.CONTRACT_NOT_OFFECTIVE);
                } else {
                    elContractVO.setEffctiveStatus(HospitalConstant.CONTRACT_NOT_OFFECTIVE);
                    elContractVO.setTerminateStatus(HospitalConstant.TERMINATE_CHECK_PASS);
                }
            } else if(type == HospitalConstant.CUSTOMER_TERMINATE_CHECK_TYPE) {
                //操作类型为客户审核
                if(!HospitalConstant.CUSTOMER_TERMINATE_CHECK_TYPE.equals(preContract.getTerminateStatus())){
                    return new ResultInfo(-1,"审批操作已完成");
                }
                elContractVO.setEffctiveStatus(HospitalConstant.CONTRACT_NOT_OFFECTIVE);
                elContractVO.setTerminateStatus(HospitalConstant.TERMINATE_CHECK_PASS);
            }
        }else if(HospitalConstant.AUDIT_STATUS_REJECTED.equals(elContractVO.getAuditStatus())){
            //审核不通过
            elContractVO.setTerminateStatus(HospitalConstant.CONTRACT_NOT_TERMINATION);
        }
        elContractVO.setAuditStatus(null);
        contractMapper.updateByPrimaryKeySelective(elContractVO);
        return new ResultInfo(0,"操作成功");
    }

    @Override
    public ResultInfo checkModify(ELContractVO contract) {
        ELContractVO modifyContract=getContractModifyInfo(contract.getElContractId());
        if(HospitalConstant.CONTRACT_AUDIT_UNPASS.equals(contract.getAuditStatus())){
            modifyMapper.deleteByContractId(contract.getElContractId());
            if(modifyContract!=null) {
                skuModifyMapper.deleteByContractModifyId(modifyContract.getElContractModifyId());
            }
        }else if(HospitalConstant.CONTRACT_AUDIT_PASS.equals(contract.getAuditStatus())){
            ELContractVO preContract=findById(contract.getElContractId());
            a:if(preContract==null){
                return new ResultInfo(-1,"原信息内容为空");
            }
            if(modifyContract==null){
                return new ResultInfo(0,"修改合同信息为空");
            }
            BeanUtils.copyProperties(modifyContract,preContract);
            preContract.setModifyStatus(HospitalConstant.CONTRACT_UNMODIFY);
            updateContract(preContract);
            modifyMapper.deleteByContractId(contract.getElContractId());
            skuMapper.deleteByContractId(contract.getElContractId());
            List<ElContractSkuVO> skuList=skuModifyMapper.findByModifyContractId(preContract.getElContractModifyId());
            List<ElContractSku> newSkus=new ArrayList<>();
            if(CollectionUtils.isNotEmpty(skuList)){
                for(ElContractSkuVO s:skuList){
                    if(s==null){
                        continue;
                    }
                    s.setContractId(contract.getElContractId());
                    ElContractSku sku=new ElContractSku();
                    BeanUtils.copyProperties(s,sku);
                    newSkus.add(sku);
                }
                skuMapper.batchAddContractSkus(newSkus);
            }
            skuModifyMapper.deleteByContractModifyId(preContract.getElContractModifyId());
            contract.setProductSynStatus(HospitalConstant.CONTRACT_PRO_UNSYNC);
            contract.setContractSynStatus(HospitalConstant.CONTRACT_UNSYNC);
            contract.setConfirmStatus(HospitalConstant.CONTRACT_UNCONFIRM);
            synContractModifyService.syncData(contract.getElContractId());
//            contract.setEffctiveStatus(0);
        }
        contract.setAuditStatus(HospitalConstant.AUDIT_STATUS_PASSED);
        contract.setModifyStatus(HospitalConstant.CONTRACT_UNMODIFY);
        return new ResultInfo(0,"操作成功");
    }

    @Override
    public void validatorContractSku(ELOrderDto orderDto) {
        List<ELOrderItemDto> itemDtos = this.getOrderItem(orderDto);
        if (CollectionUtils.isNotEmpty(itemDtos)){
            //新增合约
            ELContractVO contract = new ELContractVO();
            contract.setSignDate(DateUtil.DateToLong(new Date()));
            contract.setContractValidityDateStart(DateUtil.gainNowDate());
            contract.setContractValidityDateEnd(DateUtil.gainNowDate());
            contract.setOwner(SYSTEM_CREATOR_USER);
            contract.setCreator(SYSTEM_CREATOR_USER);
            contract.setUpdator(SYSTEM_CREATOR_USER);
            contract.setStatus(1);
            contract.setTraderId(orderDto.getPurchaserId());
            contract.setAuditStatus(2);
            contract.setEffctiveStatus(1);
            contract.setContractSynStatus(1);
            contract.setConfirmStatus(1);
            contract.setProductSynStatus(1);
            Integer contractId = this.saveContract(contract);
            //添加合约产品
            List<ElContractSku> skuList = new ArrayList<ElContractSku>();
            itemDtos.stream().forEach(item -> {
                ElContractSku contractSku = new ElContractSku();
                contractSku.setContractId(contractId);
                contractSku.setSkuId(item.getProductId());
                contractSku.setPrice(item.getPurPrice());
                contractSku.setContractPrice(item.getPurPrice());
                contractSku.setCreator(SYSTEM_CREATOR_USER);
                contractSku.setUpdator(SYSTEM_CREATOR_USER);
                contractSku.setAddTime(DateUtil.gainNowDate());
                contractSku.setUpdateTime(DateUtil.gainNowDate());
                skuList.add(contractSku);
            });
            contractSkuMapper.batchAddContractSkus(skuList);
        }
    }

    @Override
    public List<ElSkuInfo> validatorSkuPrice(ELOrderDto orderDto) {
        List<ELOrderItemDto> itemDtos = this.getOrderItem(orderDto);
        List<ElSkuInfo> list = new ArrayList<>();
        itemDtos.stream().forEach(data ->{
            ELSkuDetailInfo skuDetailInfo = elSkuMapper.getDetailSkuInfo(data.getProductId());
            if (!skuDetailInfo.getTerminalPrice().equals(data.getPurPrice().toString())){
                ElSkuInfo info = new ElSkuInfo();
                info.setProductId(data.getProductId());
                info.setProductCode(skuDetailInfo.getProductCode());
                info.setPrice(skuDetailInfo.getTerminalPrice());
                list.add(info);
            }
        });
        return list;
    }

    public int saveContract(ELContractVO contract) {
        if(contract == null){
            return 0;
        }
        contract.setAddTime(System.currentTimeMillis());
        contract.setUpdateTime(System.currentTimeMillis());
        this.contractMapper.insertSelect(contract);
        return contract.getElContractId();
    }

    private List<ELOrderItemDto> getOrderItem(ELOrderDto orderDto){
        //客户id
        Integer traderId = orderDto.getPurchaserId();

        //订购商品的sku列表
        List<Integer> skuIds = orderDto.getDDMX().stream().map(ELOrderItemDto::getProductId).collect(Collectors.toList());

        List<Integer> skus = contractMapper.findExistContractByCusAndSkuId(traderId,skuIds);

        List<ELOrderItemDto> itemDtos = orderDto.getDDMX();
        itemDtos.stream().map(v -> {
            if (skus.contains(v.getProductId())){
                itemDtos.remove(v);
            }
            return v;
        });
        return itemDtos;
    }
}
