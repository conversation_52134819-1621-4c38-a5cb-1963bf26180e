package com.vedeng.flash.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 催票/跟进任务Dto
 * @date 2021/5/21 16:23
 */
@Data
public class EarlyWarningTicksDto {
    private Integer earlyWarningTaskId;
    private String traderName;
    private String saleorderNo;
    private String skuNo;
    private String skuName;
    private String skuUnit;
    private BigDecimal price;
    private Integer num;
    private String addtime;
    private Integer urgingTicketNum;
    private BigDecimal urgingTicketAmount;
    private BigDecimal alreadyInputNum;
    private BigDecimal alreadyInputAmount;
    private int followUpStatus;
    private String followUpTime;
    private String followUpResult;
    private BigDecimal canTicketAmount;
    private String canTicketTime;
    private String followUpComment;
    private String taskDealerName;

    private BigDecimal urgingTicketNumFollowUp;

    private BigDecimal urgingTicketAmountFollowUp;
}
