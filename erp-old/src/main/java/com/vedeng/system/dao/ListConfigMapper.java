package com.vedeng.system.dao;

import com.vedeng.system.model.ListConfig;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

/**
 * <AUTHOR>
 * @date created in 2020/5/30 16:56
 */
@Named("listConfigMapper")
public interface ListConfigMapper {

    ListConfig getListConfigByUserAndListName(@Param("userId") Integer userId, @Param("listName") String listName);


    void deleteListConfigByUserAndListName(@Param("userId") Integer userId, @Param("listName") String listName);

    void saveListConfig(@Param("listConfig") ListConfig listConfig);

    void updateListConfigByUserAndListName(@Param("listConfig") ListConfig listConfig);
}
