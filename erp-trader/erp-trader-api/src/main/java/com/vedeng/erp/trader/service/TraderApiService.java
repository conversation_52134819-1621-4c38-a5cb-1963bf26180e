package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.BizTraderApiDto;
import com.vedeng.erp.trader.dto.TraderDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 客户表操作
 * @date 2023/8/11 13:46
 **/
public interface TraderApiService {


    /**
     * 保存客户主表信息
     *
     * @param traderDto 客户信息
     */
    void add(TraderDto traderDto);


    /**
     * 更新客户信息
     *
     * @param newTraderDto 客户信息
     */
    void update(TraderDto newTraderDto);

    /**
     * 获取商机关联客户
     *
     * @param businessChanceIds
     * @return
     */
    List<BizTraderApiDto> getBizTrader(List<Integer> businessChanceIds, Integer bizType);


    /**
     * 获取客户详情链接
     */
    void getTraderLink(BizTraderApiDto bizTraderApiDto);

    /**
     * 根据已有的客户ID获取客户列表
     * @param traderIdList
     * @return
     */
    List<TraderDto> getTraderList(List<Integer> traderIdList);
}
