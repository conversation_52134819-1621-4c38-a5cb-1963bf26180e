$(function() {
    $("#search").click(function(){
        checkLogin();
        $("#myform1").submit();
    });
    $("#reset").click(function(){
        $("#buyOrderId").val('');
    });
});
function addVB(saleorderGoodInfos, buyorderGoodsId){
    checkLogin();
    debugger;
    layer.confirm("关联成功后，此销售单将从待采购列表消失，并自动通知销售已采购，确认关联？",
    { btn: ['确定','取消']},
    function(){
        $.ajax({
            async:false,
            url:'./saveBindVB.do',
            data:{"saleorderGoodInfos":saleorderGoodInfos,"buyorderGoodsId":buyorderGoodsId},
            type:"POST",
            dataType : "json",
            success:function(data){
                if(data.code==0){
                    layer.alert(data.message,
                        { icon: 1 },
                        function () {
                            parent.location.reload();
                        }
                    );
                }else{
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    });
}

