package com.vedeng.api.standard.approval;

import java.util.Date;

/**
 * 审核步骤详情
 * 记录每个审核步骤的详细信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-26
 */
public class ApprovalStepDetail {
    
    private String taskId;
    private String taskName;
    private String approverUsername;
    private String approverRealName;
    private String comment;
    private int stepNumber;
    private Date executeTime;
    private boolean success;
    private String errorMessage;
    
    public ApprovalStepDetail() {
        this.executeTime = new Date();
    }
    
    public ApprovalStepDetail(String taskId, String taskName, String approverUsername, 
                             String approverRealName, String comment, int stepNumber) {
        this();
        this.taskId = taskId;
        this.taskName = taskName;
        this.approverUsername = approverUsername;
        this.approverRealName = approverRealName;
        this.comment = comment;
        this.stepNumber = stepNumber;
        this.success = true;
    }
    
    // Getters and Setters
    public String getTaskId() { return taskId; }
    public void setTaskId(String taskId) { this.taskId = taskId; }
    
    public String getTaskName() { return taskName; }
    public void setTaskName(String taskName) { this.taskName = taskName; }
    
    public String getApproverUsername() { return approverUsername; }
    public void setApproverUsername(String approverUsername) { this.approverUsername = approverUsername; }
    
    public String getApproverRealName() { return approverRealName; }
    public void setApproverRealName(String approverRealName) { this.approverRealName = approverRealName; }
    
    public String getComment() { return comment; }
    public void setComment(String comment) { this.comment = comment; }
    
    public int getStepNumber() { return stepNumber; }
    public void setStepNumber(int stepNumber) { this.stepNumber = stepNumber; }
    
    public Date getExecuteTime() { return executeTime; }
    public void setExecuteTime(Date executeTime) { this.executeTime = executeTime; }
    
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    
    @Override
    public String toString() {
        return "ApprovalStepDetail{" +
                "taskId='" + taskId + '\'' +
                ", taskName='" + taskName + '\'' +
                ", approverUsername='" + approverUsername + '\'' +
                ", approverRealName='" + approverRealName + '\'' +
                ", comment='" + comment + '\'' +
                ", stepNumber=" + stepNumber +
                ", executeTime=" + executeTime +
                ", success=" + success +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
}
