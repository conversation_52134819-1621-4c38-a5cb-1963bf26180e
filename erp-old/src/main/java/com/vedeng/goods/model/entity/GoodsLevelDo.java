package com.vedeng.goods.model.entity;


import com.vedeng.goods.supprot.BaseGoodsDo;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = true)
@Data
public class GoodsLevelDo extends BaseGoodsDo {

    private String uniqueIdentifier;

    private String levelName;

    private String description;

    private Integer allowSyncFrontend;

    private String spuValidationRules;
    private String skuValidationRules;

}