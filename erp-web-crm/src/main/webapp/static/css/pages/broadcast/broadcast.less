
.page-wrap {
    padding-top: 0;
    // min-width: 1900px;
}

.rank-container {
    position: relative;
    min-height: 100vh;
    background: url('../../../image/broadcast/banner1.jpg') no-repeat #AD0D27;
    background-position: top center;
    padding: 320px 20px 30px;

    .fixed-nav {
        display: none;
    }

    .rank-wrap {
        margin: auto;
        padding: 15px;
        border-radius: 10px;
        background: #970012;
        width: 1727px;

        .rank-card-wrap {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: -15px;

            .rank-card {
                width: 413px;
                flex-shrink: 0;
                border-radius: 5px;
                background: #fff;
                margin-right: 15px;
                margin-bottom: 15px;

                &:nth-child(4n) {
                    margin-right: 0;
                }
            }
        }
    }

}


@media screen and (max-width: 1800px) {
    .rank-container {
        .rank-wrap {
            width: 871px;
            
            .rank-card-wrap .rank-card:nth-child(2n) {
                margin-right: 0;
            }
        }
    }
}

@media screen and (max-width: 768px) {
    .page-wrap {
        min-width: auto;
    }

    .rank-container {
        min-width: auto;
        min-height: 100vh;
        // background: #fff;
        background: url('../../../image/broadcast/banner2.jpg') no-repeat #AD0D27;
        background-size: contain;
        background-position: top center;
        padding: 22.47vh 0 0;

        .rank-wrap {
            width: 100%;
            padding: 10px;
            background: none;

            .rank-card-wrap {
                display: block;
                margin-bottom: 0;

                .rank-card {
                    margin-right: 0;
                    margin-bottom: 10px;
                    width: 100%;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }
        }

        .fixed-nav {
            height: 41px;
            display: block;

            .m-nav {
                position: relative;

                &.fixed {
                    position: fixed;
                    top: 0;
                    z-index: 9;
                }

                .scroll-bar {
                    position: relative;
                    width: 100vw;
                    display: flex;
                    white-space: nowrap;
                    overflow: auto;
                    background: #911100;

                    &::-webkit-scrollbar {
                        display: none;
                    }

                    .nav-list {
                        width: 710px;
                        display: flex;
                        padding: 0 15px;

                        .nav-item {
                            position: relative;
                            padding: 10px 20px 10px 0;
                            font-size: 14px;
                            color: rgba(255, 255, 255, 0.6);
                            white-space: nowrap;

                            &:last-child {
                                padding-right: 0;
                            }

                            &.active {
                                color: #fff;
                                font-weight: 700;
    
                                &::after {
                                    content: "";
                                    width: 25px;
                                    height: 4px;
                                    border-radius: 2px;
                                    background: #fff;
                                    position: absolute;
                                    left: 50%;
                                    transform: translateX(-50%);
                                    bottom: 0;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
