package com.vedeng.activiti.taskassign;

import com.pricecenter.constant.VerifyStatusEnum;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.system.service.UserService;
import com.wms.service.WMSLendOutService;
import com.wms.service.WMSScrappedOutService;
import com.wms.service.WMSReceiveOutService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

public class ReceiveOutAuditFinishExecutionListenr implements ExecutionListener {

    // 运行时注入service
    public static WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();

    private UserService userService = (UserService) context.getBean("userService");

    private WMSReceiveOutService receiveOutService;

    private WMSScrappedOutService scrappedOutService;

    public ReceiveOutAuditFinishExecutionListenr(){
        receiveOutService = (WMSReceiveOutService) context.getBean(WMSReceiveOutService.class);
        scrappedOutService = (WMSScrappedOutService) context.getBean(WMSScrappedOutService.class);
    }

    @Override
    public void notify(DelegateExecution execution) throws Exception {
        User user = null;

        if(RequestContextHolder.getRequestAttributes() == null){
            user = userService.getByUsername("njadmin",1);
        }else{
            ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = ra.getRequest();
            user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            if(user == null){
                user = userService.getByUsername("njadmin",1);
            }
        }

        String preAssignee = execution.getVariable("currentAssinee").toString();
        User assigneeInfo = userService.getByUsernameEnable(preAssignee, user.getCompanyId());

        String businessKey = execution.getVariable("businessKey").toString();
        Long receiveOutOrderId =  Long.valueOf(businessKey.split("_")[1]);
        boolean pass = Boolean.valueOf(execution.getVariable("pass").toString());

        //更新审核状态
        receiveOutService.updateReceiveOutAuditStatus(receiveOutOrderId,pass ? VerifyStatusEnum.Approved.getValue() : VerifyStatusEnum.Reject.getValue());

        //领用单审核通过，将单据同步到WMS
        if(pass){
            scrappedOutService.addWmsLogicalOrderGodos(assigneeInfo,receiveOutOrderId.intValue());
            scrappedOutService.putScrappedOutOrder(receiveOutOrderId.intValue());
        }
    }
}
