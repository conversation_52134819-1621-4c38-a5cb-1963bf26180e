package com.vedeng.erp.system.web.controller;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.system.domain.entity.SysCostCategoryKingDee;
import com.vedeng.erp.system.service.CostCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.system.web.api
 * @Date 2022/8/12 15:33
 */
@ExceptionController
@Controller
@RequestMapping("/costCategory")
@Slf4j
public class CostCategoryController {

    @Autowired
    CostCategoryService costCategoryService;

    /**
     * 新增/编辑费用类别页面
     * @param costCategoryId 费用类别ID
     * @return ModelAndView
     */
    @RequestMapping(value = "/addView")
    public ModelAndView edit(@RequestParam(required = false, defaultValue = "0") Integer costCategoryId) {
        ModelAndView mv = new ModelAndView("system/costCategory/addCostCategory");
        mv.addObject("costCategoryId", costCategoryId);
        return mv;
    }


    /**
     * 通过类别或编号查找金蝶费用信息
     * @param category 费用名称或ID
     * @return R
     */
    @RequestMapping(value = "/getKingCostCategory", method = RequestMethod.POST)
    @ResponseBody
    public R<?> getKingCostCategory(String category){
        List<SysCostCategoryKingDee> list =  costCategoryService.selectKingDeeCategoryByNameOrId(category);
        return R.success(list);
    }

    /**
     * 通过编号获取单条金蝶费用信息
     * @param categoryNo
     * @return
     */
    @RequestMapping(value = "/getKingDeeCostCategory",method = RequestMethod.POST)
    @ResponseBody
    public R<?> getKingDeeCostCategory(String categoryNo){
        return R.success(costCategoryService.selectByKingDeeNo(categoryNo));
    }

    /**
     * 维护金蝶费用页面
     * @return
     */
    @RequestMapping(value = "/editKingDeeView")
    public ModelAndView editKingDee(){
        ModelAndView mv = new ModelAndView("system/costCategory/addKingDeeCostCategory");
        List<SysCostCategoryKingDee> kingDeeCategory = costCategoryService.getKingDeeCategory();
        mv.addObject("kingDeeCategory",kingDeeCategory);
        return mv;
    }

    /**
     * 添加金蝶费用信息
     * @param kingDeeCategoryList
     * @return
     */
    @RequestMapping(value = "/addKingDeeCategory" ,method = RequestMethod.POST)
    @ResponseBody
    public R<?> addKingDeeCategory(@RequestParam("list")String kingDeeCategoryList){
        List<SysCostCategoryKingDee> sysCostCategoryKingDeeList = JSON.parseArray(kingDeeCategoryList, SysCostCategoryKingDee.class);
        costCategoryService.checkKingDeeRepeat(sysCostCategoryKingDeeList);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        for (SysCostCategoryKingDee sysCostCategoryKingDee : sysCostCategoryKingDeeList) {
            sysCostCategoryKingDee.setCreator(currentUser.getId());
            sysCostCategoryKingDee.setCreatorName(currentUser.getUsername());
            sysCostCategoryKingDee.setUpdater(currentUser.getId());
            sysCostCategoryKingDee.setUpdaterName(currentUser.getUsername());
        }
        costCategoryService.insertBatchKingDeeCategory(sysCostCategoryKingDeeList);
        return R.success();
    }
}
