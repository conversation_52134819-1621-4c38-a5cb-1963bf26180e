package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 刷新历史订单MAXSkuAmount值
 *
 * <AUTHOR>
 * @date $
 */
@Component
@JobHandler(value="SaleoderMaxSkuAmountTask")
public class SaleoderMaxSkuAmountTask extends AbstractJobHandler {

    @Autowired
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("XXL-JOB, Hello World.");
        updateSaleoderMaxSkuAmount();
        return SUCCESS;
    }

    private void updateSaleoderMaxSkuAmount() {
        List<Integer> saleGoodsidList = saleorderGoodsMapper.getSaleorderGoodsIdByMaxSkuAmount();
        int i = saleorderGoodsMapper.updateMaxSkuAmountBySaleGoodsId(saleGoodsidList);
        XxlJobLogger.log("更新"+i+"条");
    }
}
