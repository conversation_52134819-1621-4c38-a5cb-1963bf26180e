package com.vedeng.billsync.task.model.entity.generate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import lombok.Data;

/**
 * TMP_ALIPAY_BILL_DATA
 * <AUTHOR>
@Data
public class TmpAlipayBillDataDo implements Serializable {
    /**
     * 支付宝账单临时表ID
     */
    private Integer id;

    /**
     * 财务流水号
     */
    private String financialTurnover;

    /**
     * 业务流水号
     */
    private String businessTurnover;

    /**
     * 商户订单号
     */
    private String orderNo;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 发生时间
     */
    private Date occurrenceTime;

    /**
     * 对方账号
     */
    private String otherAccount;

    /**
     * 收入金额
     */
    private BigDecimal income;

    /**
     * 支出金额
     */
    private BigDecimal outlay;

    /**
     * 账户余额
     */
    private BigDecimal balance;

    /**
     * 交易渠道
     */
    private String tradingChannel;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 同步时间
     */
    private Date syncTime;

    /**
     * 更新时间
     */
    private Timestamp updateTime;

    /**
     * 增加时间
     */
    private Timestamp addTime;

    private static final long serialVersionUID = 1L;
}