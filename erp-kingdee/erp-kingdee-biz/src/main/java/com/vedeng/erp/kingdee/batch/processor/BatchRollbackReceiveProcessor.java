package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchRollbackInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchRollbackInvoiceProcessDto;
import com.vedeng.erp.kingdee.batch.repository.BatchRollbackInvoiceDtoMapper;
import com.vedeng.erp.kingdee.dto.result.KingDeeReceiveQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeReceiveCommonService;
import com.vedeng.erp.kingdee.service.KingDeeReceiveFeeService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import com.vedeng.infrastructure.kingdee.domain.command.OperateExtCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 销售线-回滚蓝字作废关联蓝字有效应收单处理器
 * <AUTHOR>
 * @create 2023−01-13 下午2:04
 */
@Slf4j
@Service
public class BatchRollbackReceiveProcessor extends BaseProcessor<BatchRollbackInvoiceDto, List<BatchRollbackInvoiceProcessDto>> {

    @Autowired
    BatchRollbackInvoiceDtoMapper batchRollbackInvoiceDtoMapper;
    @Autowired
    private KingDeeReceiveCommonService kingDeeReceiveCommonService;

    @Autowired
    private KingDeeReceiveFeeService kingDeeReceiveFeeService;

    @Override
    public List<BatchRollbackInvoiceProcessDto> doProcess(BatchRollbackInvoiceDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("销售线蓝字作废票关联的蓝字有效应收单回滚批处理 BatchRollbackInvoiceDto:{}", JSON.toJSONString(dto));
        List<BatchRollbackInvoiceProcessDto> resultList = new ArrayList<>();
        //蓝字有效票可能有对应的费用应收单和标准应收单

        //标准应收单
        List<KingDeeReceiveQueryResultDto> bzResult = kingDeeReceiveCommonService.getKingDeeReceiveCommon(dto.getInvoiceId().toString());
        //费用应收单
        List<KingDeeReceiveQueryResultDto> fyResult = kingDeeReceiveFeeService.getKingDeeReceiveFee(dto.getInvoiceId().toString());
        if (CollUtil.size(bzResult) > 1 || CollUtil.size(fyResult) > 1){
            log.info("销售售后回滚应收单，对应金蝶应收单数据异常，有多条数据 BatchRollbackInvoiceDto:{}",JSON.toJSONString(dto));
        }
        if (CollUtil.isEmpty(bzResult) && CollUtil.isEmpty(fyResult)){
            log.warn("销售线蓝字作废票关联的蓝字有效应收单回滚批处理,未查询到标准应收单或费用应收单记录,BatchRollbackInvoiceDto:{}", JSON.toJSONString(dto));
            throw new KingDeeException("销售线蓝字作废票关联的蓝字有效应收单回滚批处理,未查询到标准应收单或费用应收单记录");
        }
        if (CollUtil.isNotEmpty(bzResult)){
            resultList.add(BatchRollbackInvoiceProcessDto.builder().invoiceId(dto.getInvoiceId()).formId(KingDeeFormConstant.RECEIVE_EXPENSES).ids(bzResult.get(0).getFID()).isPay(Boolean.TRUE).orgId(KingDeeConstant.ORG_ID.toString()).build());
        }
        if (CollUtil.isNotEmpty(fyResult)){
            resultList.add(BatchRollbackInvoiceProcessDto.builder().invoiceId(dto.getInvoiceId()).formId(KingDeeFormConstant.RECEIVE_EXPENSES).ids(fyResult.get(0).getFID()).isPay(Boolean.TRUE).orgId(KingDeeConstant.ORG_ID.toString()).build());
        }
        return resultList;
    }
}
