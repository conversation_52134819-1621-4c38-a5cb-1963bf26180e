<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="来电|销售" scope="application" />
<%@ include file="./common/header.jsp"%>

<div class="layer-content call-layer-content">
	<!-- 标题 -->
	<div class="callcenter-title">
		<%@ include file="./common/call_in.jsp"%>

		<div class="right-title">
			<c:if test="${isClick}">	<span
					onclick="addComm('${phone}',${traderCustomer.traderId},${callOut.traderType },0,0,0,0,'${callOut.coid }');">新增联系</span>
				<span
					onclick="showEnquiry('${phone}');">查看商机</span>

				<a class="addtitle" style="color: #ffffff; border: solid 1px #ccc;" href="javascript:void(0);"
				   tabTitle='{"num":"addbusinessChance${traderCustomer.traderId}",
										"link":"${lxcrmUrl}/crm/businessChance/profile/add?traderId=${traderCustomer.traderId}&traderContactId=${traderCustomer.traderContactId}&phone=${phone}&traderName=${traderName}",
										"title":"新增商机"}'>
					新增商机</a>

			</c:if><i class="iconclosetitle" onclick="window.parent.closeScreenAll();"></i>
		</div>

	</div>
	<!-- 表格信息 -->
	<div class="content-box">
		<div class="content-colum content-colum2">

			<c:choose>
				<c:when test="${isClick}">
					<%@ include file="./common/customer_info.jsp"%>
					<%@ include file="./common/communicate.jsp"%>
					<%@ include file="./common/bussiness_chance.jsp"%>
					<%@ include file="./common/quoteorder.jsp"%>
					<%@ include file="./common/saleorder.jsp"%>
				</c:when>
				<c:otherwise>
					<%@ include file="./common/customer_info_nolink.jsp"%>
					<%@ include file="./common/communicate.jsp"%>
					<%@ include file="./common/bussiness_chance_nolink.jsp"%>
					<%@ include file="./common/quoteorder_nolink.jsp"%>
					<%@ include file="./common/saleorder_nolink.jsp"%>

				</c:otherwise>
			</c:choose>




		</div>
		<div class="clear"></div>
	</div>
</div>

<%@ include file="../common/footer.jsp"%>
