<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.logistics.dao.WarehouseGoodsOperateLogVirtualMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.WarehouseGoodsOperateLogVirtual" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 14:44:16 CST 2022.
    -->
    <id column="WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID" property="warehouseGoodsOperateLogVirtualId" jdbcType="INTEGER" />
    <result column="BARCODE_ID" property="barcodeId" jdbcType="INTEGER" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="OPERATE_TYPE" property="operateType" jdbcType="TINYINT" />
    <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
    <result column="WAREHOUSE_PICKING_DETAIL_ID" property="warehousePickingDetailId" jdbcType="INTEGER" />
    <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
    <result column="BARCODE_FACTORY" property="barcodeFactory" jdbcType="VARCHAR" />
    <result column="NUM" property="num" jdbcType="INTEGER" />
    <result column="WAREHOUSE_ID" property="warehouseId" jdbcType="INTEGER" />
    <result column="STORAGE_ROOM_ID" property="storageRoomId" jdbcType="INTEGER" />
    <result column="STORAGE_AREA_ID" property="storageAreaId" jdbcType="INTEGER" />
    <result column="STORAGE_LOCATION_ID" property="storageLocationId" jdbcType="INTEGER" />
    <result column="STORAGE_RACK_ID" property="storageRackId" jdbcType="INTEGER" />
    <result column="BATCH_NUMBER" property="batchNumber" jdbcType="VARCHAR" />
    <result column="EXPIRATION_DATE" property="expirationDate" jdbcType="BIGINT" />
    <result column="CHECK_STATUS" property="checkStatus" jdbcType="BIT" />
    <result column="CHECK_STATUS_USER" property="checkStatusUser" jdbcType="INTEGER" />
    <result column="CHECK_STATUS_TIME" property="checkStatusTime" jdbcType="BIGINT" />
    <result column="RECHECK_STATUS" property="recheckStatus" jdbcType="BIT" />
    <result column="RECHECK_STATUS_USER" property="recheckStatusUser" jdbcType="INTEGER" />
    <result column="RECHECK_STATUS_TIME" property="recheckStatusTime" jdbcType="BIGINT" />
    <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="IS_EXPRESS" property="isExpress" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="IS_PROBLEM" property="isProblem" jdbcType="TINYINT" />
    <result column="PROBLEM_REMARK" property="problemRemark" jdbcType="VARCHAR" />
    <result column="PRODUCT_DATE" property="productDate" jdbcType="BIGINT" />
    <result column="COST_PRICE" property="costPrice" jdbcType="DECIMAL" />
    <result column="IS_USE" property="isUse" jdbcType="BIT" />
    <result column="LOGICAL_WAREHOUSE_ID" property="logicalWarehouseId" jdbcType="INTEGER" />
    <result column="VEDENG_BATCH_NUMER" property="vedengBatchNumer" jdbcType="VARCHAR" />
    <result column="LAST_STOCK_NUM" property="lastStockNum" jdbcType="INTEGER" />
    <result column="STERILZATION_BATCH_NUMBER" property="sterilizationBatchNo" jdbcType="VARCHAR" />
    <result column="LOG_TYPE" property="logType" jdbcType="BIT" />
    <result column="NEW_COST_PRICE" property="newCostPrice" jdbcType="DECIMAL" />
    <result column="TAG_SOURCES" property="tagSources" jdbcType="VARCHAR" />
    <result column="DEDICATED_BUYORDER_NO" property="dedicatedBuyorderNo" jdbcType="VARCHAR" />
    <result column="FIRST_ENGAGE_ID" property="fitstEngageId" />
    <result column="REGISTRATION_NUMBER_ID" property="registrationNumberId" />
    <result column="REGISTRATION_NUMBER" property="registrationNo" />
  </resultMap>
  <resultMap id="WarehouseLogGroupResultMap"
             type="com.vedeng.logistics.model.WarehouseLog">
    <id column="WAREHOUSE_GOODS_OPERATE_LOG_ID"
        property="warehouseGoodsOperateLogId" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="VARCHAR" />

  </resultMap>

  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 14:44:16 CST 2022.
    -->
    WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID, BARCODE_ID, COMPANY_ID, OPERATE_TYPE, RELATED_ID, 
    WAREHOUSE_PICKING_DETAIL_ID, GOODS_ID, BARCODE_FACTORY, NUM, WAREHOUSE_ID, STORAGE_ROOM_ID, 
    STORAGE_AREA_ID, STORAGE_LOCATION_ID, STORAGE_RACK_ID, BATCH_NUMBER, EXPIRATION_DATE, 
    CHECK_STATUS, CHECK_STATUS_USER, CHECK_STATUS_TIME, RECHECK_STATUS, RECHECK_STATUS_USER, 
    RECHECK_STATUS_TIME, COMMENTS, IS_ENABLE, IS_EXPRESS, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER, IS_PROBLEM, PROBLEM_REMARK, PRODUCT_DATE, COST_PRICE, IS_USE, LOGICAL_WAREHOUSE_ID, 
    VEDENG_BATCH_NUMER, LAST_STOCK_NUM, STERILZATION_BATCH_NUMBER, LOG_TYPE, NEW_COST_PRICE, 
    TAG_SOURCES, DEDICATED_BUYORDER_NO
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 14:44:16 CST 2022.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL
    where WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID = #{warehouseGoodsOperateLogVirtualId,jdbcType=INTEGER}
  </select>

  <insert id="insert" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLogVirtual" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 14:44:16 CST 2022.
    -->
    <selectKey resultType="java.lang.Integer" keyProperty="warehouseGoodsOperateLogVirtualId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL (BARCODE_ID, COMPANY_ID, OPERATE_TYPE,
      RELATED_ID, WAREHOUSE_PICKING_DETAIL_ID, GOODS_ID,
      BARCODE_FACTORY, NUM, WAREHOUSE_ID,
      STORAGE_ROOM_ID, STORAGE_AREA_ID, STORAGE_LOCATION_ID,
      STORAGE_RACK_ID, BATCH_NUMBER, EXPIRATION_DATE,
      CHECK_STATUS, CHECK_STATUS_USER, CHECK_STATUS_TIME,
      RECHECK_STATUS, RECHECK_STATUS_USER, RECHECK_STATUS_TIME,
      COMMENTS, IS_ENABLE, IS_EXPRESS,
      ADD_TIME, CREATOR, MOD_TIME,
      UPDATER, IS_PROBLEM, PROBLEM_REMARK,
      PRODUCT_DATE, COST_PRICE, IS_USE,
      LOGICAL_WAREHOUSE_ID, VEDENG_BATCH_NUMER, LAST_STOCK_NUM,
      STERILZATION_BATCH_NUMBER, LOG_TYPE, NEW_COST_PRICE,
      TAG_SOURCES, DEDICATED_BUYORDER_NO)
    values (#{barcodeId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, #{operateType,jdbcType=TINYINT},
      #{relatedId,jdbcType=INTEGER}, #{warehousePickingDetailId,jdbcType=INTEGER}, #{goodsId,jdbcType=INTEGER},
      #{barcodeFactory,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER}, #{warehouseId,jdbcType=INTEGER},
      #{storageRoomId,jdbcType=INTEGER}, #{storageAreaId,jdbcType=INTEGER}, #{storageLocationId,jdbcType=INTEGER},
      #{storageRackId,jdbcType=INTEGER}, #{batchNumber,jdbcType=VARCHAR}, #{expirationDate,jdbcType=BIGINT},
      #{checkStatus,jdbcType=BIT}, #{checkStatusUser,jdbcType=INTEGER}, #{checkStatusTime,jdbcType=BIGINT},
      #{recheckStatus,jdbcType=BIT}, #{recheckStatusUser,jdbcType=INTEGER}, #{recheckStatusTime,jdbcType=BIGINT},
      #{comments,jdbcType=VARCHAR}, #{isEnable,jdbcType=BIT}, #{isExpress,jdbcType=BIT},
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT},
      #{updater,jdbcType=INTEGER}, #{isProblem,jdbcType=TINYINT}, #{problemRemark,jdbcType=VARCHAR},
      #{productDate,jdbcType=BIGINT}, #{costPrice,jdbcType=DECIMAL}, #{isUse,jdbcType=BIT},
      #{logicalWarehouseId,jdbcType=INTEGER}, #{vedengBatchNumer,jdbcType=VARCHAR}, #{lastStockNum,jdbcType=INTEGER},
      #{sterilzationBatchNumber,jdbcType=VARCHAR}, #{logType,jdbcType=BIT}, #{newCostPrice,jdbcType=DECIMAL},
      #{tagSources,jdbcType=VARCHAR}, #{dedicatedBuyorderNo,jdbcType=VARCHAR})
  </insert>

  <insert id="insertSelective" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLogVirtual" useGeneratedKeys="true"
          keyProperty="warehouseGoodsOperateLogVirtualId" >
    <!--          -->
    insert into T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="barcodeId != null" >
        BARCODE_ID,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="operateType != null" >
        OPERATE_TYPE,
      </if>
      <if test="relatedId != null" >
        RELATED_ID,
      </if>
      <if test="warehousePickingDetailId != null" >
        WAREHOUSE_PICKING_DETAIL_ID,
      </if>
      <if test="goodsId != null" >
        GOODS_ID,
      </if>
      <if test="barcodeFactory != null" >
        BARCODE_FACTORY,
      </if>
      <if test="num != null" >
        NUM,
      </if>
      <if test="warehouseId != null" >
        WAREHOUSE_ID,
      </if>
      <if test="storageRoomId != null" >
        STORAGE_ROOM_ID,
      </if>
      <if test="storageAreaId != null" >
        STORAGE_AREA_ID,
      </if>
      <if test="storageLocationId != null" >
        STORAGE_LOCATION_ID,
      </if>
      <if test="storageRackId != null" >
        STORAGE_RACK_ID,
      </if>
      <if test="batchNumber != null" >
        BATCH_NUMBER,
      </if>
      <if test="expirationDate != null" >
        EXPIRATION_DATE,
      </if>
      <if test="checkStatus != null" >
        CHECK_STATUS,
      </if>
      <if test="checkStatusUser != null" >
        CHECK_STATUS_USER,
      </if>
      <if test="checkStatusTime != null" >
        CHECK_STATUS_TIME,
      </if>
      <if test="recheckStatus != null" >
        RECHECK_STATUS,
      </if>
      <if test="recheckStatusUser != null" >
        RECHECK_STATUS_USER,
      </if>
      <if test="recheckStatusTime != null" >
        RECHECK_STATUS_TIME,
      </if>
      <if test="comments != null" >
        COMMENTS,
      </if>
      <if test="isEnable != null" >
        IS_ENABLE,
      </if>
      <if test="isExpress != null" >
        IS_EXPRESS,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="isProblem != null" >
        IS_PROBLEM,
      </if>
      <if test="problemRemark != null" >
        PROBLEM_REMARK,
      </if>
      <if test="productDate != null" >
        PRODUCT_DATE,
      </if>
      <if test="costPrice != null" >
        COST_PRICE,
      </if>
      <if test="isUse != null" >
        IS_USE,
      </if>
      <if test="logicalWarehouseId != null" >
        LOGICAL_WAREHOUSE_ID,
      </if>
      <if test="vedengBatchNumer != null" >
        VEDENG_BATCH_NUMER,
      </if>
      <if test="lastStockNum != null" >
        LAST_STOCK_NUM,
      </if>
      <if test="sterilzationBatchNumber != null" >
        STERILZATION_BATCH_NUMBER,
      </if>
      <if test="logType != null" >
        LOG_TYPE,
      </if>
      <if test="newCostPrice != null" >
        NEW_COST_PRICE,
      </if>
      <if test="tagSources != null" >
        TAG_SOURCES,
      </if>
      <if test="dedicatedBuyorderNo != null" >
        DEDICATED_BUYORDER_NO,
      </if>
      <if test="createTime != null" >
        CREATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="barcodeId != null" >
        #{barcodeId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="operateType != null" >
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null" >
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="warehousePickingDetailId != null" >
        #{warehousePickingDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null" >
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="barcodeFactory != null" >
        #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="num != null" >
        #{num,jdbcType=INTEGER},
      </if>
      <if test="warehouseId != null" >
        #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="storageRoomId != null" >
        #{storageRoomId,jdbcType=INTEGER},
      </if>
      <if test="storageAreaId != null" >
        #{storageAreaId,jdbcType=INTEGER},
      </if>
      <if test="storageLocationId != null" >
        #{storageLocationId,jdbcType=INTEGER},
      </if>
      <if test="storageRackId != null" >
        #{storageRackId,jdbcType=INTEGER},
      </if>
      <if test="batchNumber != null" >
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null" >
        #{expirationDate,jdbcType=BIGINT},
      </if>
      <if test="checkStatus != null" >
        #{checkStatus,jdbcType=BIT},
      </if>
      <if test="checkStatusUser != null" >
        #{checkStatusUser,jdbcType=INTEGER},
      </if>
      <if test="checkStatusTime != null" >
        #{checkStatusTime,jdbcType=BIGINT},
      </if>
      <if test="recheckStatus != null" >
        #{recheckStatus,jdbcType=BIT},
      </if>
      <if test="recheckStatusUser != null" >
        #{recheckStatusUser,jdbcType=INTEGER},
      </if>
      <if test="recheckStatusTime != null" >
        #{recheckStatusTime,jdbcType=BIGINT},
      </if>
      <if test="comments != null" >
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=BIT},
      </if>
      <if test="isExpress != null" >
        #{isExpress,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isProblem != null" >
        #{isProblem,jdbcType=TINYINT},
      </if>
      <if test="problemRemark != null" >
        #{problemRemark,jdbcType=VARCHAR},
      </if>
      <if test="productDate != null" >
        #{productDate,jdbcType=BIGINT},
      </if>
      <if test="costPrice != null" >
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="isUse != null" >
        #{isUse,jdbcType=BIT},
      </if>
      <if test="logicalWarehouseId != null" >
        #{logicalWarehouseId,jdbcType=INTEGER},
      </if>
      <if test="vedengBatchNumer != null" >
        #{vedengBatchNumer,jdbcType=VARCHAR},
      </if>
      <if test="lastStockNum != null" >
        #{lastStockNum,jdbcType=INTEGER},
      </if>
      <if test="sterilzationBatchNumber != null" >
        #{sterilzationBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="logType != null" >
        #{logType,jdbcType=BIT},
      </if>
      <if test="newCostPrice != null" >
        #{newCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="tagSources != null" >
        #{tagSources,jdbcType=VARCHAR},
      </if>
      <if test="dedicatedBuyorderNo != null" >
        #{dedicatedBuyorderNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>


  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLogVirtual" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 13 14:44:16 CST 2022.
    -->
    update T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL
    <set >
      <if test="barcodeId != null" >
        BARCODE_ID = #{barcodeId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="operateType != null" >
        OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="relatedId != null" >
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="warehousePickingDetailId != null" >
        WAREHOUSE_PICKING_DETAIL_ID = #{warehousePickingDetailId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null" >
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="barcodeFactory != null" >
        BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="num != null" >
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="warehouseId != null" >
        WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER},
      </if>
      <if test="storageRoomId != null" >
        STORAGE_ROOM_ID = #{storageRoomId,jdbcType=INTEGER},
      </if>
      <if test="storageAreaId != null" >
        STORAGE_AREA_ID = #{storageAreaId,jdbcType=INTEGER},
      </if>
      <if test="storageLocationId != null" >
        STORAGE_LOCATION_ID = #{storageLocationId,jdbcType=INTEGER},
      </if>
      <if test="storageRackId != null" >
        STORAGE_RACK_ID = #{storageRackId,jdbcType=INTEGER},
      </if>
      <if test="batchNumber != null" >
        BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null" >
        EXPIRATION_DATE = #{expirationDate,jdbcType=BIGINT},
      </if>
      <if test="checkStatus != null" >
        CHECK_STATUS = #{checkStatus,jdbcType=BIT},
      </if>
      <if test="checkStatusUser != null" >
        CHECK_STATUS_USER = #{checkStatusUser,jdbcType=INTEGER},
      </if>
      <if test="checkStatusTime != null" >
        CHECK_STATUS_TIME = #{checkStatusTime,jdbcType=BIGINT},
      </if>
      <if test="recheckStatus != null" >
        RECHECK_STATUS = #{recheckStatus,jdbcType=BIT},
      </if>
      <if test="recheckStatusUser != null" >
        RECHECK_STATUS_USER = #{recheckStatusUser,jdbcType=INTEGER},
      </if>
      <if test="recheckStatusTime != null" >
        RECHECK_STATUS_TIME = #{recheckStatusTime,jdbcType=BIGINT},
      </if>
      <if test="comments != null" >
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        IS_ENABLE = #{isEnable,jdbcType=BIT},
      </if>
      <if test="isExpress != null" >
        IS_EXPRESS = #{isExpress,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isProblem != null" >
        IS_PROBLEM = #{isProblem,jdbcType=TINYINT},
      </if>
      <if test="problemRemark != null" >
        PROBLEM_REMARK = #{problemRemark,jdbcType=VARCHAR},
      </if>
      <if test="productDate != null" >
        PRODUCT_DATE = #{productDate,jdbcType=BIGINT},
      </if>
      <if test="costPrice != null" >
        COST_PRICE = #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="isUse != null" >
        IS_USE = #{isUse,jdbcType=BIT},
      </if>
      <if test="logicalWarehouseId != null" >
        LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER},
      </if>
      <if test="vedengBatchNumer != null" >
        VEDENG_BATCH_NUMER = #{vedengBatchNumer,jdbcType=VARCHAR},
      </if>
      <if test="lastStockNum != null" >
        LAST_STOCK_NUM = #{lastStockNum,jdbcType=INTEGER},
      </if>
      <if test="sterilzationBatchNumber != null" >
        STERILZATION_BATCH_NUMBER = #{sterilzationBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="logType != null" >
        LOG_TYPE = #{logType,jdbcType=BIT},
      </if>
      <if test="newCostPrice != null" >
        NEW_COST_PRICE = #{newCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="tagSources != null" >
        TAG_SOURCES = #{tagSources,jdbcType=VARCHAR},
      </if>
      <if test="dedicatedBuyorderNo != null" >
        DEDICATED_BUYORDER_NO = #{dedicatedBuyorderNo,jdbcType=VARCHAR},
      </if>
    </set>
    where WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID = #{warehouseGoodsOperateLogVirtualId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLogVirtual" >
    <!--          -->
    update T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL
    set BARCODE_ID = #{barcodeId,jdbcType=INTEGER},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      WAREHOUSE_PICKING_DETAIL_ID = #{warehousePickingDetailId,jdbcType=INTEGER},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=INTEGER},
      WAREHOUSE_ID = #{warehouseId,jdbcType=INTEGER},
      STORAGE_ROOM_ID = #{storageRoomId,jdbcType=INTEGER},
      STORAGE_AREA_ID = #{storageAreaId,jdbcType=INTEGER},
      STORAGE_LOCATION_ID = #{storageLocationId,jdbcType=INTEGER},
      STORAGE_RACK_ID = #{storageRackId,jdbcType=INTEGER},
      BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      EXPIRATION_DATE = #{expirationDate,jdbcType=BIGINT},
      CHECK_STATUS = #{checkStatus,jdbcType=BIT},
      CHECK_STATUS_USER = #{checkStatusUser,jdbcType=INTEGER},
      CHECK_STATUS_TIME = #{checkStatusTime,jdbcType=BIGINT},
      RECHECK_STATUS = #{recheckStatus,jdbcType=BIT},
      RECHECK_STATUS_USER = #{recheckStatusUser,jdbcType=INTEGER},
      RECHECK_STATUS_TIME = #{recheckStatusTime,jdbcType=BIGINT},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      IS_ENABLE = #{isEnable,jdbcType=BIT},
      IS_EXPRESS = #{isExpress,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_PROBLEM = #{isProblem,jdbcType=TINYINT},
      PROBLEM_REMARK = #{problemRemark,jdbcType=VARCHAR},
      PRODUCT_DATE = #{productDate,jdbcType=BIGINT},
      COST_PRICE = #{costPrice,jdbcType=DECIMAL},
      IS_USE = #{isUse,jdbcType=BIT},
      LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER},
      VEDENG_BATCH_NUMER = #{vedengBatchNumer,jdbcType=VARCHAR},
      LAST_STOCK_NUM = #{lastStockNum,jdbcType=INTEGER},
      STERILZATION_BATCH_NUMBER = #{sterilzationBatchNumber,jdbcType=VARCHAR},
      LOG_TYPE = #{logType,jdbcType=BIT},
      NEW_COST_PRICE = #{newCostPrice,jdbcType=DECIMAL},
      TAG_SOURCES = #{tagSources,jdbcType=VARCHAR},
      DEDICATED_BUYORDER_NO = #{dedicatedBuyorderNo,jdbcType=VARCHAR}
    where WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID = #{warehouseGoodsOperateLogVirtualId,jdbcType=INTEGER}
  </update>

  <select id="getAvailableLogicalGoods" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLogVirtual">
    SELECT
    *
    FROM
    T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL
    <where>
      1 = 1
      <if test="logicalWarehouseId != null">
        AND LOGICAL_WAREHOUSE_ID = #{logicalWarehouseId,jdbcType=INTEGER}
      </if>
      <if test="relatedId != null">
        AND RELATED_ID = #{relatedId,jdbcType=INTEGER}
      </if>
      <if test="dedicatedBuyorderNo != null">
        AND DEDICATED_BUYORDER_NO = #{dedicatedBuyorderNo,jdbcType=VARCHAR}
      </if>
      AND VEDENG_BATCH_NUMER = #{vedengBatchNumer,jdbcType=VARCHAR}
      AND GOODS_ID = #{goodsId,jdbcType=INTEGER}
      AND LOG_TYPE = 0
      AND IS_ENABLE = 1
      AND IS_USE = 0
      AND LAST_STOCK_NUM>0
    </where>
  </select>

  <update id="updateOutIsUseAndLastStockNumById">
    UPDATE
      T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL
    SET
      LAST_STOCK_NUM = LAST_STOCK_NUM + #{num,jdbcType=INTEGER} ,
      IS_USE = (CASE LAST_STOCK_NUM WHEN 0 THEN 1 ELSE 0 END ),
      MOD_TIME = NOW()
    WHERE
        WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID = #{warehouseGoodsOperateLogVirtualId,jdbcType=INTEGER}
      AND LOG_TYPE =0
      AND IS_ENABLE = 1
  </update>

  <select id="getWarehouseIdByExpressDetail" parameterType="com.vedeng.logistics.model.ExpressDetail" resultMap="BaseResultMap">
    SELECT
    C.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID,ABS(C.NUM)
    FROM
    T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL C
    WHERE
    C.RELATED_ID = #{relatedId,jdbcType=INTEGER}
    AND C.OPERATE_TYPE=2
    AND C.IS_ENABLE=1
    <if test="wmsOrderNo != null">
      AND C.COMMENTS = #{wmsOrderNo,jdbcType=VARCHAR}
    </if>
    AND	C.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID NOT IN (
    SELECT
    B.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID
    FROM
    T_EXPRESS_DETAIL A
    JOIN T_EXPRESS D ON A.EXPRESS_ID=D.EXPRESS_ID
    JOIN V_E_W_EXPRESS_WAREHOUSE B ON A.EXPRESS_DETAIL_ID = B.EXPRESS_DETAIL_ID
    WHERE
    A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
    AND A.BUSINESS_TYPE=496
    AND D.IS_ENABLE=1
    )
    <if test="wmsOrderNo == null">
      limit #{num}
    </if>
  </select>

  <select id="getWarehouseOutList" resultMap="BaseResultMap"
          parameterType="com.vedeng.order.model.Saleorder">
    select
    <if test="bussinessType == 2">
      b.GOODS_NAME,
      b.MODEL,
      b.BRAND_NAME,
      b.UNIT_NAME,
    </if>
    <if test="bussinessType != 2">
      f.GOODS_NAME,
      g.BRAND_NAME,
      f.MODEL,
      h.UNIT_NAME,
    </if>
    d.BARCODE,
    c.MATERIAL_CODE,
    k.SKU_NO as SKU,
    a.NUM ,
    a.ADD_TIME,
    a.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID,
    a.IS_ENABLE,
    a.COMMENTS,
    a.CREATOR,
    a.GOODS_ID,
    a.BARCODE_FACTORY,
    a.BATCH_NUMBER,
    ABS(a.NUM) as REAL_GOODS_NUM,
    a.VEDENG_BATCH_NUMER,
    a.STERILZATION_BATCH_NUMBER,
    n.REGISTRATION_NUMBER_ID,
    n.REGISTRATION_NUMBER ,
    a.EXPIRATION_DATE,
    a.CHECK_STATUS_TIME,
    a.PRODUCT_DATE,
    z.FIRST_ENGAGE_ID,
    a.RELATED_ID
    from T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL a
    LEFT JOIN
    <if test="bussinessType == 2">
      T_SALEORDER_GOODS b on b.SALEORDER_GOODS_ID = a.RELATED_ID
      AND b.IS_DELETE =0
    </if>
    <if test="bussinessType != 2">
      T_AFTER_SALES_GOODS b on b.AFTER_SALES_GOODS_ID =
      a.RELATED_ID
      AND b.GOODS_TYPE =0
      INNER JOIN V_CORE_SKU f ON b.GOODS_ID =
      f.SKU_ID
      INNER JOIN T_BRAND g ON f.BRAND_ID = g.BRAND_ID
      INNER JOIN
      T_UNIT h ON f.UNIT_ID = h.UNIT_ID
    </if>
    LEFT JOIN V_CORE_SKU c on b.GOODS_ID = c.SKU_ID
    LEFT JOIN T_BARCODE d ON
    a.BARCODE_ID = d.BARCODE_ID
    AND d.IS_ENABLE = 1
    left join V_CORE_SKU k on a.GOODS_ID=k.SKU_ID
    left join V_CORE_SPU p on k.SPU_ID=p.SPU_ID
    left join T_FIRST_ENGAGE z on p.FIRST_ENGAGE_ID=z.FIRST_ENGAGE_ID and z.IS_DELETED=0
    left join T_REGISTRATION_NUMBER n on z.REGISTRATION_NUMBER_ID=n.REGISTRATION_NUMBER_ID
    where 1=1
    <if test="bussinessType != 2">
      and b.AFTER_SALES_ID = #{bussinessId,jdbcType=INTEGER}
    </if>
    <if test="bussinessType == 2">
      and b.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    </if>
    and a.OPERATE_TYPE =#{bussinessType,jdbcType=INTEGER}
    and a.IS_ENABLE=1
    <if test="outGoodsTime != null and outGoodsTime != ''">
      AND a.ADD_TIME in (${outGoodsTime})
    </if>
    <if test="batchNoComments != null and batchNoComments != ''">
      AND a.COMMENTS like CONCAT('%',#{batchNoComments})
    </if>
    ORDER BY a.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID DESC
  </select>

  <select id="queryWarehouseLogIdBySaleorderId" resultType="java.lang.Integer">
    SELECT
      C.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID
    FROM
      T_SALEORDER A
        LEFT JOIN T_SALEORDER_GOODS B ON A.SALEORDER_ID = B.SALEORDER_ID AND B.IS_DELETE=0
        LEFT JOIN T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL C ON B.SALEORDER_GOODS_ID = C.RELATED_ID
        AND C.OPERATE_TYPE = 2  AND C.IS_ENABLE = 1
    WHERE A.SALEORDER_ID=#{saleorderId,jdbcType=INTEGER};
  </select>

  <select id="getOperateTypeById" resultType="java.lang.Integer">
    SELECT OPERATE_TYPE FROM T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL WHERE WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID = #{logId,jdbcType=INTEGER}
  </select>

  <!-- 根据id查询出库信息 -->
  <select id="getwlById" resultType="com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
    SELECT
    SUM(a.NUM) NUM,
    a.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID,
    a.OPERATE_TYPE,
    a.RELATED_ID,
    a.ADD_TIME,
    a.CREATOR,
    a.EXPIRATION_DATE,
    a.PRODUCT_DATE,
    a.BATCH_NUMBER,
    a.BARCODE_FACTORY,
    n.SKU_NO SKU,
    n.MATERIAL_CODE,
    MM.PRODUCT_COMPANY_LICENCE productionLicenseNumber,
    MM.RECORD_CERTIFICATE_LICENCE recordCertificateLicence,
    CONCAT(
    e.WAREHOUSE_NAME,
    ' ',
    IFNULL(f.STORAGE_ROOM_NAME, ''),
    ' ',
    IFNULL(g.STORAGE_AREA_NAME, ''),
    ' ',
    IFNULL(h.STORAGE_RACK_NAME, ''),
    ' ',
    IFNULL(i.STORAGE_LOCATION_NAME, '')
    ) AS warehouseArea,
    RR.REGISTRATION_NUMBER `RECORD_NUMBER` ,
    RR.MANAGE_CATEGORY_LEVEL manageCategoryLevel,
    <!-- b.`MANUFACTURER`, -->
    j.TEMPERATURE ,
    j.REGISTRATION_NUMBER_ID,
    j.`CONDITION_ONE` ,
    k.`TITLE` ,
    n.`STORAGE_CONDITION_ONE`,
    p.`PRICE` ,
    p.REAL_PRICE,
    p.MAX_SKU_REFUND_AMOUNT,
    p.NUM AS SALESNUM,
    <choose>
      <when test="operateType != null and operateType== 4">
        q.IS_ACTION_GOODS,
      </when>
      <otherwise>
        p.IS_ACTION_GOODS,
      </otherwise>
    </choose>
    n.STORAGE_CONDITION_ONE_LOWER_VALUE,
    n.STORAGE_CONDITION_ONE_UPPER_VALUE,
    n.STORAGE_CONDITION_ONE,
    a.GOODS_ID,
    o.SPU_TYPE,
    n.SUPPLY_MODEL,
    <!--  q.`ORDER_TYPE` -->
    b.PRODUCT_CHINESE_NAME,
    b.GOODS_NAME,
    b.BRAND_NAME,
    b.MODEL,
    b.SPEC,
    b.UNIT_NAME,b.REGISTRATION_NUMBER,
    b.REGISTRATION_NUMBER AS registrationNo,
    b.MANUFACTURER_NAME MANUFACTURER,
    b.PRODUCT_COMPANY_LICENCE
    FROM
    T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL a
    LEFT JOIN T_SALEORDER_GOODS b ON a.RELATED_ID = b.SALEORDER_GOODS_ID and a.OPERATE_TYPE = 2
    LEFT JOIN T_WAREHOUSE e ON e.WAREHOUSE_ID = a.WAREHOUSE_ID
    LEFT JOIN T_STORAGE_ROOM f ON f.STORAGE_ROOM_ID = a.STORAGE_ROOM_ID
    LEFT JOIN T_STORAGE_AREA g ON g.STORAGE_AREA_ID = a.STORAGE_AREA_ID
    LEFT JOIN T_STORAGE_RACK h ON h.STORAGE_RACK_ID = a.STORAGE_RACK_ID
    LEFT JOIN T_STORAGE_LOCATION i ON i.STORAGE_LOCATION_ID = a.STORAGE_LOCATION_ID
    LEFT JOIN `V_CORE_SKU` n ON a.GOODS_ID = n.`SKU_ID`
    LEFT JOIN `V_CORE_SPU` o ON n.`SPU_ID` = o.`SPU_ID`
    LEFT JOIN T_UNIT c ON n.BASE_UNIT_ID   = c.UNIT_ID
    LEFT JOIN T_BRAND d ON o.BRAND_ID = d.BRAND_ID
    LEFT JOIN T_FIRST_ENGAGE j ON o.`FIRST_ENGAGE_ID` = j.`FIRST_ENGAGE_ID`
    LEFT JOIN T_REGISTRATION_NUMBER RR ON RR.REGISTRATION_NUMBER_ID=j.REGISTRATION_NUMBER_ID
    LEFT JOIN T_MANUFACTURER MM ON MM.MANUFACTURER_ID=RR.MANUFACTURER_ID
    LEFT JOIN T_SYS_OPTION_DEFINITION k ON RR.`MANAGE_CATEGORY_LEVEL` = 	k.`SYS_OPTION_DEFINITION_ID`
    LEFT JOIN `T_SALEORDER_GOODS` p ON  p.`SALEORDER_GOODS_ID`=a.`RELATED_ID`
    <if test="operateType != null and operateType== 4">
      LEFT JOIN T_AFTER_SALES_GOODS q ON q.AFTER_SALES_GOODS_ID = a.RELATED_ID
    </if>
    WHERE 1=1
    <if test="goodsFlag != null and goodsFlag== 1">
      o.SPU_TYPE = 316
    </if>
    <if test="goodsFlag != null and goodsFlag== 2">
      o.SPU_TYPE <![CDATA[ <> ]]> 316
    </if>
    AND
    a.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID IN
    <foreach item="wId" index="index" collection="idList" open="("
             separator="," close=")">
      #{wId}
    </foreach>
    <choose>
      <when test="operateType != null and operateType== 4">
        GROUP BY q.IS_ACTION_GOODS
      </when>
      <otherwise>
        GROUP BY p.IS_ACTION_GOODS
      </otherwise>
    </choose>

    <if test="ywType ==0">
      , n.SKU_NO
    </if>
    <if test="ywType ==1">
      , a.EXPIRATION_DATE,n.SKU_NO,a.BATCH_NUMBER,a.PRODUCT_DATE,a.BARCODE_FACTORY
    </if>
    <if test="ywType ==2">
      , a.EXPIRATION_DATE,n.SKU_NO,a.BATCH_NUMBER,a.PRODUCT_DATE,a.BARCODE_FACTORY
    </if>
    <if test="ywType ==3">
      , a.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID
    </if>
    <!-- spu类型为设备的会被排到前面 -->
    ORDER BY o.SPU_TYPE = 316
  </select>

  <!-- 根据出入库id获取当前采购单价或销售单�?-->
  <select id="getPrintPriceById" resultType="com.vedeng.logistics.model.vo.WarehouseGoodsOperateLogVo" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
    SELECT
    <if test="operateType != null">
      b.PRICE,
    </if>
    a.*
    FROM
    T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL a
    <!-- 入库 -->
    <if test="operateType == 1">
      LEFT JOIN T_BUYORDER_GOODS b ON b.BUYORDER_GOODS_ID =
      a.RELATED_ID
    </if>
    <!-- 出库 -->
    <if test="operateType == 2">
      LEFT JOIN T_SALEORDER_GOODS b ON b.SALEORDER_GOODS_ID =
      a.RELATED_ID
    </if>
    <!-- 销售业务入�?3�?5退 销售业务出�?4�?-->
    <if test="operateType == 3 or operateType == 4 or operateType == 5 ">
      LEFT JOIN T_AFTER_SALES_GOODS c ON c.AFTER_SALES_GOODS_ID =
      a.RELATED_ID
      LEFT JOIN T_SALEORDER_GOODS b ON b.SALEORDER_GOODS_ID =
      c.ORDER_DETAIL_ID
    </if>
    <!-- 采购业务出库 6退/7�?采购业务入库 8�?-->
    <if test="operateType == 6 or operateType == 7 or operateType == 8 ">
      LEFT JOIN T_AFTER_SALES_GOODS c ON c.AFTER_SALES_GOODS_ID =
      a.RELATED_ID
      LEFT JOIN T_BUYORDER_GOODS b ON b.BUYORDER_GOODS_ID =
      c.ORDER_DETAIL_ID
    </if>
    WHERE
    1 = 1
    AND a.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID =
    #{warehouseGoodsOperateLogId,jdbcType=INTEGER}
  </select>

  <select id="getLastOutTime" resultType="long">
    SELECT
    MAX(a.ADD_TIME)
    FROM
    T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL a
    WHERE
    a.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID IN
    <foreach item="wId" index="index" collection="idList" open="("
             separator="," close=")">
      #{wId}
    </foreach>
  </select>

  <select id="getWGOlog" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLogVirtual" parameterType="com.vedeng.logistics.model.WarehouseGoodsOperateLog">
    <if
            test="operateType != 3  and operateType != 5 and operateType != 8 and operateType!=9">
      select
      a.*,b.PRICE,b.GOODS_NAME,b.MODEL,b.BRAND_NAME,d.MATERIAL_CODE,b.UNIT_NAME,b.SKU,b.PRICE,j.BARCODE,
    </if>
    <if test="operateType == 3 or operateType == 5 or operateType == 8">
      select a.*,d.GOODS_NAME,
      d.SKU,d.GOODS_ID,l.BRAND_NAME,d.MODEL,d.MATERIAL_CODE,m.UNIT_NAME,j.BARCODE,
    </if>
    <if test="operateType==9">
      SELECT
      a.*,
      d.GOODS_NAME,
      d.MODEL,
      br.BRAND_NAME,
      d.MATERIAL_CODE,
      un.UNIT_NAME,
      d.SKU,
      j.BARCODE,
    </if>
    k.PRICE XSPRICE,
    k.PURCHASING_PRICE,
    <if test="operateType == 5">
      SUM(ABS(IFNULL(r.NUM,0))) NUMS,
    </if>
    <if test="operateType != 5">
      ABS(IFNULL(r.NUM,0)) NUMS,
    </if>
    concat(e.WAREHOUSE_NAME,
    ' ',
    IFNULL(f.STORAGE_ROOM_NAME, ''),
    ' ',
    IFNULL(g.STORAGE_AREA_NAME, ''),
    ' ',
    IFNULL(h.STORAGE_RACK_NAME, ''),
    '
    ',
    IFNULL(i.STORAGE_LOCATION_NAME, '')
    ) AS STORAGEADDRESS
    <if test="operateType == 1">
      ,c.BUYORDER_NO,c.TRADER_NAME as
      BUYTRADERNAME,c.BUYORDER_ID,   num.REGISTRATION_NUMBER, z.FIRST_ENGAGE_ID as firstEngageId
    </if>
    <if test="operateType == 2">
      ,c.SALEORDER_NO,c.TAKE_TRADER_NAME as
      SALETRADERNAME,c.SALEORDER_ID
    </if>
    <if test="operateType == 3 or operateType == 5 or operateType == 8">
      ,c.AFTER_SALES_NO,c.AFTER_SALES_ID , num.REGISTRATION_NUMBER, z.FIRST_ENGAGE_ID as firstEngageId
    </if>
    <if test="operateType == 9">
      ,le.`LEND_OUT_ID`,
      le.`LEND_OUT_NO`,
      le.`TRADER_NAME` AS SALETRADERNAME , num.REGISTRATION_NUMBER, z.FIRST_ENGAGE_ID as firstEngageId
    </if>
    from T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL a
    <if test="operateType == 1">
      LEFT JOIN T_BUYORDER_GOODS b on a.RELATED_ID =
      b.BUYORDER_GOODS_ID
      AND b.IS_DELETE =0
      LEFT JOIN T_BUYORDER c on
      b.BUYORDER_ID = c.BUYORDER_ID
      AND c.VALID_STATUS =1
      LEFT JOIN T_GOODS d
      on b.GOODS_ID = d.GOODS_ID
      left join V_CORE_SKU sku on b.GOODS_ID=sku.SKU_ID
      left join V_CORE_SPU spu on sku.SPU_ID=spu.SPU_ID
      left join T_FIRST_ENGAGE z on spu.FIRST_ENGAGE_ID=z.FIRST_ENGAGE_ID and z.IS_DELETED=0
      left join T_REGISTRATION_NUMBER num on z.REGISTRATION_NUMBER_ID=num.REGISTRATION_NUMBER_ID
    </if>
    <if test="operateType == 2">
      LEFT JOIN T_SALEORDER_GOODS b on a.RELATED_ID =
      b.SALEORDER_GOODS_ID
      AND b.IS_DELETE =0
      LEFT JOIN T_SALEORDER c on
      b.SALEORDER_ID = c.SALEORDER_ID
      AND c.VALID_STATUS =1
      LEFT JOIN T_GOODS
      d on b.GOODS_ID = d.GOODS_ID
    </if>
    <if test="operateType == 3 or operateType == 5 or operateType == 8">
      LEFT JOIN T_AFTER_SALES_GOODS b on a.RELATED_ID =
      b.AFTER_SALES_GOODS_ID
      AND b.GOODS_TYPE =0
      LEFT JOIN T_AFTER_SALES c on
      b.AFTER_SALES_ID = c.AFTER_SALES_ID
      AND c.VALID_STATUS =1
      LEFT JOIN
      T_GOODS d on b.GOODS_ID = d.GOODS_ID
      INNER JOIN T_BRAND l ON
      l.BRAND_ID = d.BRAND_ID
      INNER JOIN T_UNIT m ON m.UNIT_ID = d.UNIT_ID
      left join V_CORE_SKU sku on b.GOODS_ID=sku.SKU_ID
      left join V_CORE_SPU spu on sku.SPU_ID=spu.SPU_ID
      left join T_FIRST_ENGAGE z on spu.FIRST_ENGAGE_ID=z.FIRST_ENGAGE_ID and z.IS_DELETED=0
      left join T_REGISTRATION_NUMBER num on z.REGISTRATION_NUMBER_ID=num.REGISTRATION_NUMBER_ID
    </if>
    <if test="operateType == 9">
      LEFT JOIN `T_LEND_OUT` le
      ON a.`RELATED_ID` = le.`LEND_OUT_ID`
      LEFT JOIN `T_GOODS` d
      ON le.`GOODS_ID` = d.`GOODS_ID`
      LEFT JOIN `T_BRAND` br
      ON d.`BRAND_ID` = br.`BRAND_ID`
      LEFT JOIN `T_UNIT` un
      left join V_CORE_SKU sku on b.GOODS_ID=sku.SKU_ID
      left join V_CORE_SPU spu on sku.SPU_ID=spu.SPU_ID
      left join T_FIRST_ENGAGE z on spu.FIRST_ENGAGE_ID=z.FIRST_ENGAGE_ID and z.IS_DELETED=0
      left join T_REGISTRATION_NUMBER num on z.REGISTRATION_NUMBER_ID=num.REGISTRATION_NUMBER_ID
    </if>
    LEFT JOIN T_WAREHOUSE e ON a.WAREHOUSE_ID = e.WAREHOUSE_ID
    LEFT JOIN
    T_STORAGE_ROOM f ON a.STORAGE_ROOM_ID = f.STORAGE_ROOM_ID
    LEFT JOIN
    T_STORAGE_AREA g ON a.STORAGE_AREA_ID = g.STORAGE_AREA_ID
    LEFT JOIN
    T_STORAGE_RACK h ON a.STORAGE_RACK_ID = h.STORAGE_RACK_ID
    LEFT JOIN
    T_STORAGE_LOCATION i ON a.STORAGE_LOCATION_ID = i.STORAGE_LOCATION_ID
    LEFT JOIN T_BARCODE j ON a.BARCODE_ID = j.BARCODE_ID AND j.IS_ENABLE
    =1
    LEFT JOIN T_SALEORDER_GOODS k ON a.RELATED_ID = k.SALEORDER_GOODS_ID
    AND k.IS_DELETE =0
    LEFT JOIN T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL r
    ON
    a.BARCODE_ID = r.BARCODE_ID AND r.IS_ENABLE = 1 AND r.OPERATE_TYPE IN
    (2,4,6,7,10)
    where 1=1
    <if test="operateType == 3 or operateType == 5 or operateType == 8">
      <if test="ywType!=null">
        AND c.TYPE = #{ywType,jdbcType=INTEGER}
      </if>
    </if>
    <if test="operateType == 0">
      and a.OPERATE_TYPE in (3,5,8)
    </if>
    <if test="warehouseGoodsOperateLogId!=null">
      and
      a.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID=#{warehouseGoodsOperateLogId,jdbcType=INTEGER}
    </if>
    <if test="barcodeId!=null">
      and a.BARCODE_ID=#{barcodeId,jdbcType=INTEGER}
    </if>
    <if test="companyId!=null">
      and a.COMPANY_ID=#{companyId,jdbcType=INTEGER}
    </if>
    <if test="creator!=null and creator!=''">
      and a.CREATOR=#{creator,jdbcType=INTEGER}
    </if>
    <if test="operateType!=null and operateType != 0">
      and a.OPERATE_TYPE=#{operateType,jdbcType=INTEGER}
    </if>
    <if test="relatedId!=null">
      and a.RELATED_ID=#{relatedId,jdbcType=INTEGER}
    </if>
    <if test="goodsId!=null">
      and a.GOODS_ID=#{goodsId,jdbcType=INTEGER}
    </if>
    <if test="barcodeFactory!=null">
      and a.BARCODE_FACTORY like
      CONCAT('%',#{barcodeFactory},'%' )
    </if>
    <if test="num!=null">
      and a.NUM=#{num,jdbcType=INTEGER}
    </if>
    <if test="warehouseId!=null">
      and a.WAREHOUSE_ID=#{warehouseId,jdbcType=INTEGER}
    </if>
    <if test="storageRoomId!=null">
      and a.STORAGE_ROOM_ID=#{storageRoomId,jdbcType=INTEGER}
    </if>
    <if test="storageAreaId!=null">
      and a.STORAGE_AREA_ID=#{storageAreaId,jdbcType=INTEGER}
    </if>
    <if test="storageLocationId!=null">
      and
      a.STORAGE_LOCATION_ID=#{storageLocationId,jdbcType=INTEGER}
    </if>
    <if test="storageRackId!=null">
      and a.STORAGE_RACK_ID=#{storageRackId,jdbcType=INTEGER}
    </if>
    <if test="batchNumber!=null">
      and a.BATCH_NUMBER like CONCAT('%',#{batchNumber},'%' )
    </if>
    <if test="expirationDate!=null">
      and a.EXPIRATION_DATE=#{expirationDate,jdbcType=INTEGER}
    </if>
    <if test="checkStatus!=null and checkStatus!='-1'">
      and a.checkStatus=#{checkStatus,jdbcType=INTEGER}
    </if>
    <if test="checkStatusUser!=null and checkStatus!='-1'">
      and
      a.CHECK_STATUS_USER=#{checkStatusUser,jdbcType=INTEGER}
    </if>
    <if test="recheckStatus!=null and recheckStatus!='-1'">
      and a.RECHECK_STATUS=#{recheckStatus,jdbcType=INTEGER}
    </if>
    <if test="recheckStatusUser!=null and recheckStatusUser!='-1'">
      and
      a.RECHECK_STATUS_USER=#{recheckStatusUser,jdbcType=INTEGER}
    </if>
    <if test="isEnable != null and isEnable != ''">
      and a.IS_ENABLE=#{isEnable,jdbcType=BIT}
    </if>
    <if test="beginTime != null and beginTime != ''">
      AND a.ADD_TIME >= #{beginTime,jdbcType=INTEGER}
    </if>
    <if test="endTime != null and endTime != ''">
      AND a.ADD_TIME <![CDATA[ <= ]]>
      #{endTime,jdbcType=INTEGER}
    </if>

    <if
            test="operateType == 1 and buyorderNo != null and buyorderNo != ''">
      and c.BUYORDER_NO like CONCAT('%',#{buyorderNo},'%' )
    </if>
    <if
            test="operateType == 1 and buytraderName != null and buytraderName != ''">
      and c.TRADER_NAME like CONCAT('%',#{buytraderName},'%' )
    </if>

    <if
            test="operateType == 2 and saleorderNo != null and saleorderNo != ''">
      and c.SALEORDER_NO like CONCAT('%',#{saleorderNo},'%' )
    </if>
    <if
            test="operateType == 2 and saletraderName != null and saletraderName != ''">
      and c.TRADER_NAME like CONCAT('%',#{saletraderName},'%' )
    </if>
    <if test="operateType == 3 or operateType == 5 or operateType == 8">
      <if test="afterSalesId !=null">
        and c.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
      </if>
    </if>
    <if test="goodsName != null and goodsName != ''">
      and b.GOODS_NAME like CONCAT('%',#{goodsName},'%' )
    </if>
    <if test="brandName != null and brandName != ''">
      and b.BRAND_NAME like CONCAT('%',#{brandName},'%' )
    </if>
    <if test="model != null and model != ''">
      and b.MODEL like CONCAT('%',#{model},'%' )
    </if>
    <if test="materialCode != null and materialCode != ''">
      and d.MATERIAL_CODE like CONCAT('%',#{materialCode},'%' )
    </if>
    <if test="sku != null and sku != ''">
      and b.SKU like CONCAT('%',#{sku},'%' )
    </if>
    <if test="barcode != null and barcode != ''">
      and j.BARCODE like CONCAT('%',#{barcode},'%' )
    </if>
    <if test="operateType == 2">
      and c.ORDER_TYPE =0
    </if>
    <if test="isBarcode == 1">
      AND a.OPERATE_TYPE IN (1,3,5,8,9)
    </if>
    <if test="operateType == 5">
      GROUP BY a.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID
    </if>
    order by a.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID desc,d.SKU asc
  </select>

  <select id="querySnBySaleorderGoodsId" resultType="java.lang.String">
    SELECT DISTINCT BARCODE_FACTORY
    FROM T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL
    WHERE RELATED_ID = #{saleorderGoodsId,jdbcType=INTEGER} AND OPERATE_TYPE = 2
      AND IS_ENABLE = 1 AND BARCODE_FACTORY IS NOT NULL
  </select>
  <select id="getWGOlistByComments" resultType="com.vedeng.logistics.model.WarehouseGoodsOperateLogVirtual">
    SELECT a.ADD_TIME
    FROM T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL a
    WHERE COMMENTS like CONCAT('%',#{batChNo},'%' ) AND OPERATE_TYPE = 2
      AND IS_ENABLE = 1 AND BARCODE_FACTORY IS NOT NULL
  </select>


  <select id="getWarehouseLogDirectById" resultMap="WarehouseLogGroupResultMap">
  SELECT
    WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID AS "WAREHOUSE_GOODS_OPERATE_LOG_ID",
    LEFT(FROM_UNIXTIME(ADD_TIME/1000),19) AS ADD_TIME
    FROM
    T_WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL a
    WHERE a.WAREHOUSE_GOODS_OPERATE_LOG_VIRTUAL_ID IN
    <foreach item="id" index="index" collection="idList" open="("
             separator="," close=")">
      #{id}
    </foreach>


  </select>

</mapper>