package com.vedeng.erp.trader.mapper;

import com.vedeng.erp.trader.domain.dto.SkuSupplyAuthDetail;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface SkuSupplyAuthDetailMapper {
    int deleteByPrimaryKey(Integer skuSupplyAuthDetailId);

    int insert(SkuSupplyAuthDetail record);

    int insertSelective(SkuSupplyAuthDetail record);

    SkuSupplyAuthDetail selectByPrimaryKey(Integer skuSupplyAuthDetailId);

    int updateByPrimaryKeySelective(SkuSupplyAuthDetail record);

    int updateByPrimaryKey(SkuSupplyAuthDetail record);

    /**
     * <AUTHOR>
     * @desc 根据主表id作废明细信息
     * @param skuSupplyAuthDetail
     */
    void delSkuSupplyAuthByAuthId(SkuSupplyAuthDetail skuSupplyAuthDetail);

    /**
     * 批量插入
     * @param paramSkuList sku信息集合
     * @return 影响行数
     */
    int insertBatch(@Param("paramSkuList") List<SkuSupplyAuthDetail> paramSkuList);

    /**
     * 根据授权书id查询所有未删除的关联sku
     * @param skuSupplyAuthId 权书id
     * @return List<SkuSupplyAuthDetail>
     */
    List<SkuSupplyAuthDetail> selectRelatedSkuByAuthId(@Param("skuSupplyAuthId") Integer skuSupplyAuthId);

    /**
     *
     * @param skuSupplyAuthDetailIdList
     * @param updater
     * @param modTime
     * @return
     */
    int saveDeleteRelatedSku(@Param("skuSupplyAuthDetailIdList") List<Integer> skuSupplyAuthDetailIdList, @Param("updater") Integer updater, @Param("modTime") Date modTime);
}