package com.vedeng.temporal.mapper;

import com.vedeng.temporal.domain.entity.FlowOrderInfoEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务流程汇总信息数据访问接口
 * 
 * 设计特点：
 * - 基于 XML 配置的 SQL 映射
 * - 支持动态 SQL 更新
 * - 提供批量操作支持
 * - 遵循现有的 Mapper 命名规范
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-14
 */
public interface TemporalFlowOrderInfoMapper {

    /**
     * 根据主键查询
     *
     * @param flowOrderInfoId 主键ID
     * @return 汇总信息实体，不存在时返回null
     */
    FlowOrderInfoEntity selectByPrimaryKey(Long flowOrderInfoId);

    /**
     * 根据节点ID查询
     *
     * @param flowNodeId 流程节点ID
     * @return 汇总信息实体，不存在时返回null
     */
    FlowOrderInfoEntity selectByFlowNodeId(Long flowNodeId);

    /**
     * 根据节点ID查询所有记录
     *
     * @param flowNodeId 流程节点ID
     * @return 汇总信息实体列表
     */
    List<FlowOrderInfoEntity> selectListByFlowNodeId(Long flowNodeId);

    /**
     * 根据节点ID和业务类型查询记录列表
     *
     * @param flowNodeId 流程节点ID
     * @param businessType 业务类型
     * @return 汇总信息实体列表
     */
    List<FlowOrderInfoEntity> selectListByFlowNodeIdAndType(@Param("flowNodeId") Long flowNodeId, 
                                                            @Param("businessType") Integer businessType);

    /**
     * 根据业务编号查询
     *
     * @param businessNo 业务编号
     * @return 汇总信息实体，不存在时返回null
     */
    FlowOrderInfoEntity selectByBusinessNo(String businessNo);

    /**
     * 根据流转单编号查询所有相关的FlowOrderInfo记录
     * 通过联表查询：T_FLOW_ORDER_INFO JOIN T_FLOW_NODE JOIN T_FLOW_ORDER
     *
     * @param flowOrderNo 流转单编号
     * @return FlowOrderInfo记录列表，不存在时返回空列表
     */
    List<FlowOrderInfoEntity> selectByFlowOrderNo(@Param("flowOrderNo") String flowOrderNo);

    /**
     * 根据业务类型查询列表
     *
     * @param businessType 业务类型：0-采购，1-销售
     * @return 汇总信息实体列表
     */
    List<FlowOrderInfoEntity> selectByBusinessType(Integer businessType);

    /**
     * 插入新记录（选择性插入，只插入非空字段）
     *
     * @param entity 汇总信息实体
     * @return 影响行数
     */
    int insertSelective(FlowOrderInfoEntity entity);

    /**
     * 根据业务类型查询列表
     * @param businessType
     * @param flowNodeId
     * @return
     */
    List<FlowOrderInfoEntity> selectByBusinessTypeAndFlowNodeId(@Param("businessType") Integer businessType,@Param("flowNodeId") Long flowNodeId);


    /**
     * 插入新记录
     *
     * @param entity 汇总信息实体
     * @return 影响行数
     */
    int insert(FlowOrderInfoEntity entity);

    /**
     * 根据主键更新
     *
     * @param entity 汇总信息实体
     * @return 影响行数
     */
    int updateByPrimaryKey(FlowOrderInfoEntity entity);


    int updateByPrimaryKeySelective(FlowOrderInfoEntity entity);


    /**
     * 根据节点ID动态更新
     * 只更新非空字段
     *
     * @param entity 汇总信息实体
     * @return 影响行数
     */
    int updateByFlowNodeIdSelective(FlowOrderInfoEntity entity);

    /**
     * 根据节点ID和业务类型动态更新
     * 只更新非空字段
     *
     * @param entity 汇总信息实体
     * @return 影响行数
     */
    int updateByFlowNodeIdAndTypeSelective(FlowOrderInfoEntity entity);

    /**
     * 根据主键删除（物理删除）
     *
     * @param flowOrderInfoId 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long flowOrderInfoId);

    /**
     * 根据节点ID逻辑删除
     *
     * @param flowNodeId 流程节点ID
     * @return 影响行数
     */
    int logicalDeleteByFlowNodeId(Long flowNodeId);

    /**
     * 批量插入
     *
     * @param entities 汇总信息实体列表
     * @return 影响行数
     */
    int batchInsert(@Param("entities") List<FlowOrderInfoEntity> entities);


    /**
     * 根据条件查询列表
     *
     * @param flowNodeId    流程节点ID（可选）
     * @param businessType  业务类型（可选）
     * @param businessNo    业务编号（可选）
     * @param includeDeleted 是否包含已删除记录
     * @return 汇总信息实体列表
     */
    List<FlowOrderInfoEntity> selectByConditions(
            @Param("flowNodeId") Long flowNodeId,
            @Param("businessType") Integer businessType,
            @Param("businessNo") String businessNo,
            @Param("includeDeleted") Boolean includeDeleted
    );

    /**
     * 统计记录数
     *
     * @param businessType   业务类型（可选）
     * @param includeDeleted 是否包含已删除记录
     * @return 记录总数
     */
    int countByConditions(
            @Param("businessType") Integer businessType,
            @Param("includeDeleted") Boolean includeDeleted
    );

    /**
     * 查询需要数据一致性检查的记录
     * 用于定时任务进行数据修复
     *
     * @return 可能不一致的记录列表
     */
    List<FlowOrderInfoEntity> selectInconsistentRecords();

    /**
     * 查询所有处理中且可能需要标记为已完结的记录
     * 条件：ORDER_STATUS = 0 且所有业务状态（PAYMENT_STATUS、STORAGE_STATUS、INVOICE_STATUS）都为2
     * 
     * @return 待完结的记录列表
     */
    List<FlowOrderInfoEntity> selectPendingCompletionRecords();

    /**
     * 根据节点ID列表查询
     *
     * @param flowNodeIds 节点ID列表
     * @return 汇总信息实体列表
     */
    List<FlowOrderInfoEntity> selectByFlowNodeIds(@Param("flowNodeIds") List<Long> flowNodeIds);

    /**
     * 更新订单状态
     *
     * @param flowNodeId  节点ID
     * @param orderStatus 订单状态
     * @param updater     更新人ID
     * @param updaterName 更新人姓名
     * @return 影响行数
     */
    int updateOrderStatus(
            @Param("flowNodeId") Long flowNodeId,
            @Param("orderStatus") Integer orderStatus,
            @Param("updater") Integer updater,
            @Param("updaterName") String updaterName
    );

    /**
     * 更新款项状态
     *
     * @param flowNodeId    节点ID
     * @param paymentStatus 款项状态
     * @param updater       更新人ID
     * @param updaterName   更新人姓名
     * @return 影响行数
     */
    int updatePaymentStatus(
            @Param("flowNodeId") Long flowNodeId,
            @Param("paymentStatus") Integer paymentStatus,
            @Param("updater") Integer updater,
            @Param("updaterName") String updaterName
    );

    /**
     * 更新入库状态
     *
     * @param flowNodeId    节点ID
     * @param storageStatus 入库状态
     * @param updater       更新人ID
     * @param updaterName   更新人姓名
     * @return 影响行数
     */
    int updateStorageStatus(
            @Param("flowNodeId") Long flowNodeId,
            @Param("storageStatus") Integer storageStatus,
            @Param("updater") Integer updater,
            @Param("updaterName") String updaterName
    );

    /**
     * 更新票据状态
     *
     * @param flowNodeId    节点ID
     * @param invoiceStatus 票据状态
     * @param updater       更新人ID
     * @param updaterName   更新人姓名
     * @return 影响行数
     */
    int updateInvoiceStatus(
            @Param("flowNodeId") Long flowNodeId,
            @Param("invoiceStatus") Integer invoiceStatus,
            @Param("updater") Integer updater,
            @Param("updaterName") String updaterName
    );
}
