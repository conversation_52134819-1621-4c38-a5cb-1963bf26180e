package com.vedeng.temporal.mapper;

import com.vedeng.temporal.domain.entity.FlowOrderEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Temporal流转单数据访问接口
 * 用于 Temporal 模块的流程配置查询
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
public interface TemporalFlowOrderMapper {

    /**
     * 查询有效的流程配置数据
     * 用于 Temporal 工作流批量启动
     *
     * @return 有效的流转单列表
     */
    List<FlowOrderEntity> selectValidFlowOrders();

    /**
     * 查询ERP方向的流转单
     * 用于FlowOrderInfo生成任务
     *
     * @return ERP方向的流转单列表
     */
    List<FlowOrderEntity> selectErpDirectionFlowOrders();

    /**
     * 根据流转单编号查询流转单
     * 用于单个流程启动
     *
     * @param flowOrderNo 流转单编号
     * @return 流转单实体，如果不存在则返回null
     */
    FlowOrderEntity selectByFlowOrderNo(@Param("flowOrderNo") String flowOrderNo);

    /**
     * 根据公司简称和流转单ID查询销售单ID
     * 通过多表关联查询：T_BASE_COMPANY_INFO -> T_FLOW_NODE -> T_FLOW_ORDER_INFO
     *
     * @param companyShortName 公司简称
     * @param flowOrderId 流转单ID
     * @param flowOrderInfoType 业务类型，0:采购, 1:销售              
     * @return 销售单ID，如果不存在则返回null
     */
    String selectSaleOrderIdByCompanyAndFlowOrder(@Param("companyShortName") String companyShortName, 
                                                  @Param("flowOrderId") Long flowOrderId,
                                                  @Param("flowOrderInfoType") Integer flowOrderInfoType);

    String selectBusinessNoByCompanyAndFlowOrder(@Param("companyShortName") String companyShortName,
                                                  @Param("flowOrderId") Long flowOrderId);

    // ========== 扩展查询方法 ==========

    /**
     * 查询完整的业务流转单信息
     *
     * @param flowOrderId 流转单ID
     * @return 流转单完整信息
     */
    Map<String, Object> selectFlowOrderCompleteInfo(@Param("flowOrderId") Long flowOrderId);

    /**
     * 查询流转单商品明细列表
     *
     * @param flowOrderId 流转单ID
     * @return 商品明细列表
     */
    List<Map<String, Object>> selectFlowOrderDetails(@Param("flowOrderId") Long flowOrderId);

    /**
     * 查询指定节点的商品价格信息
     *
     * @param flowOrderId 流转单ID
     * @param companyCode 公司代码
     * @return 价格信息列表
     */
    List<Map<String, Object>> selectFlowNodePrices(@Param("flowOrderId") Long flowOrderId,
                                                    @Param("companyCode") String companyCode);

    /**
     * 查询0-1采购单的商品信息
     * 通过业务流转单的BASE_ORDER_ID从T_BUYORDER_GOODS表查询
     *
     * @param baseOrderId 0-1采购单ID（来自业务流转单的BASE_ORDER_ID）
     * @return 采购商品信息列表
     */
    List<Map<String, Object>> selectBuyorderGoodsByOrderId(@Param("baseOrderId") Long baseOrderId);

    /**
     * 根据流转单ID和公司代码查询流程节点的完整信息
     * 包含供应商基本信息、联系人信息、地址信息等
     *
     * @param flowOrderId 流转单ID
     * @param companyCode 公司代码
     * @return 流程节点完整信息
     */
    Map<String, Object> selectFlowNodeCompleteInfo(@Param("flowOrderId") Long flowOrderId,
                                                    @Param("companyCode") String companyCode);

    /**
     * 根据流转单ID查询流转单实体
     * 用于业务流转单信息查询
     *
     * @param flowOrderId 流转单ID
     * @return 流转单实体，如果不存在则返回null
     */
    FlowOrderEntity selectByFlowOrderId(@Param("flowOrderId") Long flowOrderId);
}
