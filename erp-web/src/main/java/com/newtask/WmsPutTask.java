package com.newtask;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.buyorder.service.NewBuyorderPeerListService;
import com.vedeng.flash.enums.WmsSendOrderTypeEnum;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.system.service.UserService;
import com.wms.constant.*;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dao.WmsSendOrderMapper;
import com.wms.dto.*;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.*;
import com.wms.service.chain.Build.StepBuildFactory;
import com.wms.service.chain.HandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.service.chain.step.InventoryOutWmsRequestHandler;
import com.wms.service.chain.step.UnitConversionOrderInWmsRequestHandler;
import com.wms.service.chain.step.UnitConversionOrderOutWmsRequestHandler;
import com.wms.service.listenner.PurchaseExgAuditFinishListener;
import com.wms.service.listenner.PurchaseReturnAuditFinishListenner;
import com.wms.service.listenner.PutAfterReturnAuditFinishLister;
import com.wms.service.other.DeliveryDirectSaleorderChooseServiceImpl;
import com.wms.service.other.DoPutDeliveryDirectPeerListServiceImpl;
import com.wms.unitconversionorder.dao.WmsUnitConversionOrderMapper;
import com.wms.unitconversionorder.model.entity.WmsUnitConversionOrder;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName WmsPutTask.java
 * @Description TODO  WMS下传任务重试
 * @createTime 2020年08月13日 17:03:00
 */
@Component
@JobHandler(value="WmsPutTask")
public class WmsPutTask  extends AbstractJobHandler {

    private Logger logger = LoggerFactory.getLogger(WmsPutTask.class);

    @Resource
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Autowired
    private LogicalSaleorderChooseService logicalSaleorderChooseService;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Autowired
    private LogicalAfterorderChooseService logicalAfterorderChooseService;

    @Resource
    private AfterSalesService afterSalesOrderService;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Autowired
    private PutAfterReturnAuditFinishLister putAfterReturnAuditFinishLister;

    @Autowired
    private PurchaseReturnAuditFinishListenner purchaseReturnAuditFinishListenner;

    @Autowired
    private PurchaseExgAuditFinishListener purchaseExgAuditFinishListener;

    @Resource
    private ExpressMapper expressMapper;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Value("${erp_url}")
    protected String erpUrl;

    @Autowired
    private WmsSendOrderMapper wmsSendOrderMapper;

    @Autowired
    private PurcjaseOrderService purcjaseOrderService;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private BuyorderService buyorderService;

    @Resource
    private NewBuyorderPeerListService newBuyorderPeerListService;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Autowired
    private DoPutService doPutService;

    @Autowired
    private DeliveryDirectSaleorderChooseServiceImpl deliveryDirectSaleorderChooseService;

    @Autowired
    private DoPutDeliveryDirectPeerListServiceImpl doPutDeliveryDirectPeerListService;

    @Autowired
    private InventoryOutWmsRequestHandler inventoryOutWmsRequestHandler;

    @Autowired
    private UnitConversionOrderOutWmsRequestHandler unitConversionOrderOutWmsRequestHandler;

    @Autowired
    private UnitConversionOrderInWmsRequestHandler unitConversionOrderInWmsRequestHandler;

    @Autowired
    private WmsUnitConversionOrderMapper wmsUnitConversionOrderMapper;


    @Autowired
    private StepBuildFactory stepBuildFactory;

    @Autowired
    private WmsOutputOrderMapper outputOrderMapper;


    @Autowired
    private WmsInventoryOutService wmsInventoryOutService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        putWMS(param);
        return SUCCESS;
    }

    private void putWMS(String param) throws Exception {
        if(StringUtil.isBlank(param)){
            List<WmsSendOrder> putSendFailSalorderlist = wmsSendOrderMapper.getSendFailSaleorder(WmsSendOrderTypeEnum.PUTSALEORDER.getCode());
            for (WmsSendOrder wmsSendOrder : putSendFailSalorderlist) {
                String saleorderNo = wmsSendOrder.getOrderNo();
                putSaleorderByNo(saleorderNo);
            }
            //下发采购单
            retrySendFailBuyOrderModify();
            retrySendFailSaleorderModify();
            //下发售后单
            retrySendFailAfterSalesModify();
            //下发盘亏出库单
            retrySendInventoryOutOrder();
            // 库存转换异常下发
            retrySendUnitConversionOrder();
            // 外借
            retrySendLendOutOrder();
        }else {
            if (param.contains("noZhiFa")) {
                //下发直发采购单入库
                retrySendFailDeliveryDirectBuyorder();
                //下发直发销售单出库
                retrySendFailDeliveryDirectSaleorder();
            } else {
                String[] split = param.split(":");
                switch (split[0]) {
                    case WMSContant.PUT_ORIGINAL_SALESORDER:
                        putSaleorder(param);
                        break;
                    case WMSContant.PUT_EXG_DATA:
                        putExgorder(param);
                        break;
                    case WMSContant.CANCEL_ORGGINCAL_SALESORDER:
                        cancelOut(param);
                        break;
                    case WmsInterfaceOrderType.INPUT_SALE_RETURN:
                        putSaleReturn(param);
                        break;
                    case WmsInterfaceOrderType.OUT_PURCHASE_RETURN:
                        putBuyorderReturn(param);
                        break;
                    case WMSContant.CANCEL_PO:
                        cancelin(param);
                        break;
                    case "buyExg":
                        purchaseExgAuditFinish(param);
                        break;
                    case "path":
                        putpath(param);
                        break;
                    case "buyorder":
                        putBuyorder(param);
                        break;
                    case "zhibuy":
                        putZhibuy(param);
                        break;
                    case "zhisale":
                        putZhisale(param);
                        break;
                    case "pankuiOut":
                        putPankuiOut(param);
                        break;
                    case "unitConversionOut":
                        putUnitOut(param);
                        break;
                    case "unitConversionIn":
                        putUnitIn(param);
                    case "lendOut":
                        lendOutParam(param);
                }
            }
        }
    }

    private void lendOutParam(String param) throws Exception {
        String[] split = param.split(":");
        String orderNos = split[1].trim();
        String[] nolists = orderNos.split(",");
        for (String orderNo : nolists) {
            WmsOutputOrder wmsOutputOrder = outputOrderMapper.selectByOrderNo(orderNo);
            if (Objects.nonNull(wmsOutputOrder)) {
                lendOutPut(wmsOutputOrder.getOrderNo(), wmsOutputOrder.getId());
            } else {
                XxlJobLogger.log("未查到此单信息："+orderNo);
            }
        }
    }

    private void retrySendLendOutOrder() throws Exception {
        List<WmsSendOrder> outData = wmsSendOrderMapper.getSendFailSaleorder(WmsSendOrderTypeEnum.LEND_OUT.getCode());
        for (WmsSendOrder data : outData) {
            Integer orderId = data.getOrderId();
            String orderNo = data.getOrderNo();
            lendOutPut(orderNo, Long.valueOf(orderId));
        }
    }

    private void putUnitIn(String param) throws Exception {
        String[] split = param.split(":");
        String orderNos = split[1].trim();
        String[] nolists = orderNos.split(",");
        for (String orderNo : nolists) {
            WmsUnitConversionOrder wmsUnitConversionOrder = wmsUnitConversionOrderMapper.selectByWmsUnitConversionOrderNo(orderNo);
            if (Objects.nonNull(wmsUnitConversionOrder)) {
                unitConversionIn(wmsUnitConversionOrder.getWmsUnitConversionOrderId());
            } else {
                XxlJobLogger.log("未查到此单信息："+orderNo);
            }
        }
    }

    private void putUnitOut(String param) throws Exception {
        String[] split = param.split(":");
        String orderNos = split[1].trim();
        String[] nolists = orderNos.split(",");
        for (String orderNo : nolists) {
            WmsUnitConversionOrder wmsUnitConversionOrder = wmsUnitConversionOrderMapper.selectByWmsUnitConversionOrderNo(orderNo);
            if (Objects.nonNull(wmsUnitConversionOrder)) {
                unitConversionOut(wmsUnitConversionOrder.getWmsUnitConversionOrderNo(), wmsUnitConversionOrder.getWmsUnitConversionOrderId());
            } else {
                XxlJobLogger.log("未查到此单信息："+orderNo);
            }
        }
    }

    private void retrySendUnitConversionOrder() throws Exception {
        List<WmsSendOrder> outData = wmsSendOrderMapper.getSendFailSaleorder(WmsSendOrderTypeEnum.UNIT_CONVERSION_OUT.getCode());
        List<WmsSendOrder> inData = wmsSendOrderMapper.getSendFailSaleorder(WmsSendOrderTypeEnum.UNIT_CONVERSION_IN.getCode());

        for (WmsSendOrder wmsSendOrder : outData) {
            Integer orderId = wmsSendOrder.getOrderId();
            String orderNo = wmsSendOrder.getOrderNo();
            unitConversionOut(orderNo, orderId);
        }

        for (WmsSendOrder wmsSendOrder : inData) {
            Integer orderId = wmsSendOrder.getOrderId();
            unitConversionIn(orderId);
        }

    }

    private void unitConversionIn(Integer orderId) throws Exception {
        HandlerStepContext handlerStepContext = new HandlerStepContext();
        handlerStepContext.put("wmsUnitConversionOrderId", orderId);
        unitConversionOrderInWmsRequestHandler.dealWith(handlerStepContext);
    }

    private void unitConversionOut(String orderNo, Integer orderId) throws Exception {
        HandlerStepContext handlerStepContext = new HandlerStepContext();
        handlerStepContext.put("wmsUnitConversionOrderId", orderId);
        List<WmsLogicalOrdergoods> wmsLogicalOrdergoodsList = wmsLogicalOrdergoodsMapper.selectUnitConversionData(orderId, WmsLogicalOperateTypeEnum.UNIT_CONVERSION_OUT.getOperateTypeCode());
        if (CollUtil.isEmpty(wmsLogicalOrdergoodsList)) {
            XxlJobLogger.log("未查到此单逻辑库存分配信息信息："+ orderNo);
            return;
        }
        handlerStepContext.put("wmsLogicalOrdergoodsList",wmsLogicalOrdergoodsList);
        unitConversionOrderOutWmsRequestHandler.dealWith(handlerStepContext);
    }


    private void lendOutPut(String orderNo, Long orderId) throws Exception {
        XxlJobLogger.log("定时任务重试外借单：" + orderNo + "id:" + orderId);
        HandlerStepContext handlerStepContext = new HandlerStepContext();
        handlerStepContext.put("lendOutOrderId",orderId);
        HandlerStep handlerStep = stepBuildFactory.buildLendOutStep();
        handlerStep.dealWith(handlerStepContext);
    }

    private void putPankuiOut(String param) throws Exception {
        String[] split = param.split(":");
        String orderNos = split[1].trim();
        String[] nolists = orderNos.split(",");
        for (String orderNo : nolists) {
            WmsOutputOrder inventoryOutByOrderNo = wmsInventoryOutService.getInventoryOutByOrderNo(orderNo);
            if (Objects.nonNull(inventoryOutByOrderNo)) {
                putInventoryOutOrder(Math.toIntExact(inventoryOutByOrderNo.getId()));
            } else {
                XxlJobLogger.log("未查到此单信息："+orderNo);
            }
        }
    }

    private void retrySendInventoryOutOrder() throws Exception{
        List<WmsSendOrder> data = wmsSendOrderMapper.getSendFailSaleorder(WmsSendOrderTypeEnum.INVENTORY_OUT.getCode());
        for (WmsSendOrder wmsSendOrder : data) {
            putInventoryOutOrder(wmsSendOrder.getOrderId());
        }
    }




    private void putBuyorderReturn(String param) {
        User user = new User();
        user.setUsername("njadmin");
        user.setUserId(ErpConst.TWO);
        String[] split = param.split(":");
        String buyorderNos = split[1].trim();
        String[] nolists = buyorderNos.split(",");
        WmsSendOrder wmsSendOrder = new WmsSendOrder();
        for (String no : nolists) {
            AfterSales afterSalesByNo = afterSalesMapper.getAfterSalesByNo(no);
            wmsSendOrder.setOrderId(afterSalesByNo.getAfterSalesId());
            wmsSendOrder.setOrderNo(no);
            putBuyOrderReturnModifyById(user,wmsSendOrder);
        }

    }

    private void retrySendFailAfterSalesModify(){
        try{

            User user = new User();
            user.setUsername("njadmin");
            user.setUserId(ErpConst.TWO);

            //销售售后退货-查询订单list
            List<WmsSendOrder> modifySendFailSalesOrderReturnlist = wmsSendOrderMapper.getSendFailSaleorder(WmsSendOrderTypeEnum.PUTSALESORDERRETURN.getCode());
            for (WmsSendOrder wmsSendSalesOrderReturn : modifySendFailSalesOrderReturnlist) {
                putSalesOrderReturnModifyById(user,wmsSendSalesOrderReturn);
            }
            //采购售后退货-查询订单list
            List<WmsSendOrder> modifySendFailBuyOrderReturnlist = wmsSendOrderMapper.getSendFailSaleorder(WmsSendOrderTypeEnum.PUTBUYORDERRETURN.getCode());
            for (WmsSendOrder wmsSendBuyOrderReturn : modifySendFailBuyOrderReturnlist) {
                putBuyOrderReturnModifyById(user,wmsSendBuyOrderReturn);
            }
            //销售售后换货-查询订单list
            List<WmsSendOrder> modifySendFailSalesOrderExchangelist = wmsSendOrderMapper.getSendFailSaleorder(WmsSendOrderTypeEnum.PUTSALESORDEREXCHANGE.getCode());
            for (WmsSendOrder wmsSendSalesOrderExchange : modifySendFailSalesOrderExchangelist) {
                putSalesOrderExchangeModifyById(user,wmsSendSalesOrderExchange);
            }
            //销售售后换货-查询订单list
            List<WmsSendOrder> modifySendFailBuyOrderExchangelist = wmsSendOrderMapper.getSendFailSaleorder(WmsSendOrderTypeEnum.PUTBUYORDEREXCHANGE.getCode());
            for (WmsSendOrder wmsSendBuyOrderExchange : modifySendFailBuyOrderExchangelist) {
                putBuyOrderExchangeModifyById(user,wmsSendBuyOrderExchange);
            }

        }catch (Exception e){
            logger.error("调用WMS修改采购接口重试失败,exception: {}",e);
        }
    }

    /**
     * 下发采购单-查询订单list
     */
    private void retrySendFailBuyOrderModify(){
        try{
            List<WmsSendOrder> modifySendFailSaleorderlist = wmsSendOrderMapper.getSendFailSaleorder(WmsSendOrderTypeEnum.PUTBUYORDER.getCode());
            for (WmsSendOrder wmsSendOrder : modifySendFailSaleorderlist) {
                putBuyorderModifyById(wmsSendOrder);
            }
        }catch (Exception e){
            logger.error("调用WMS修改采购接口重试失败,exception: {}",e);
        }
    }

    /**
     * 采购售后退货
     * @param user
     * @param wmsSendOrder
     */
    private void putBuyOrderReturnModifyById(User user,WmsSendOrder wmsSendOrder) {

        try{
            AfterSalesVo afterSales = new AfterSalesVo();
            afterSales.setAfterSalesId(Integer.valueOf(wmsSendOrder.getOrderId()));
            afterSales.setTraderType(2);

            AfterSalesVo afterSalesVo = afterSalesOrderService.getAfterSalesVoDetail(afterSales);
            purchaseReturnAuditFinishListenner.onActionHappen(afterSalesVo,true,user);
        } catch (Exception e){
            logger.error("采购售后退货下发wms失败 单号:"+ wmsSendOrder.getOrderNo(),e);
        }

    }

    /**
     * 销售售后退货
     * @param user
     * @param wmsSendOrder
     */
    private void putSalesOrderReturnModifyById(User user,WmsSendOrder wmsSendOrder) {

        try{
            AfterSalesVo afterSalesVo = new AfterSalesVo();
            afterSalesVo.setAfterSalesId(wmsSendOrder.getOrderId());
            putAfterReturnAuditFinishLister.onActionHappen(afterSalesVo,true,user);
        } catch (Exception e){
            logger.error("销售售后退货下发wms失败 单号:"+ wmsSendOrder.getOrderNo(),e);
        }

    }

    /**
     * 销售售后换货
     * @param user
     * @param wmsSendOrder
     */
    private void putSalesOrderExchangeModifyById(User user,WmsSendOrder wmsSendOrder) {

        try{
            AfterSales afterSalesByNo = afterSalesMapper.getAfterSalesByNo(wmsSendOrder.getOrderNo());
            List<WmsLogicalOrdergoods> list = wmsLogicalOrdergoodsMapper.getAfterorderLogicalChooseInfoByNo(wmsSendOrder.getOrderNo());
            logicalAfterorderChooseService.putExchangeWMS(afterSalesByNo,list,user);
        } catch (Exception e){
            logger.error("销售售后换货单下发wms失败 单号:{}"+ wmsSendOrder.getOrderNo(),e);
        }

    }

    /**
     * 采购售后换货
     * @param user
     * @param wmsSendOrder
     */
    private void putBuyOrderExchangeModifyById(User user,WmsSendOrder wmsSendOrder) {

        try{
            AfterSalesVo afterSales = new AfterSalesVo();
            afterSales.setAfterSalesId(Integer.valueOf(wmsSendOrder.getOrderId()));
            afterSales.setTraderType(2);
            AfterSalesVo afterSalesVo =  afterSalesOrderService.getAfterSalesVoDetail(afterSales);
            purchaseExgAuditFinishListener.onActionHappen(afterSalesVo,true,user);
        } catch (Exception e){
            logger.error("采购售后换货单下发wms失败 单号:{}"+ wmsSendOrder.getOrderNo(),e);
        }

    }

    /**
     * 采购单下发
     * @param wmsSendOrder
     */
    private void putBuyorderModifyById(WmsSendOrder wmsSendOrder){

        User user = new User();
        user.setUsername("njadmin");
        user.setUserId(ErpConst.TWO);

        //采购单审核通过 下发wms接口
        Buyorder buyorder = buyorderService.getBuyOrderByOrderId(wmsSendOrder.getOrderId());

        logger.info("采购单:"+buyorder.getBuyorderNo()+",审核通过下发WMS start=============================");


        try {

            WmsResponse wmsResponse = doPutService.doPutPurchaseOrderMethod(buyorder.getBuyorderId(),buyorder.getLogisticsComments());
            logger.info("采购入库单下发wms响应:" +buyorder.getBuyorderNo()+","+ JSON.toJSONString(wmsResponse));
        }catch (Exception e){
            logger.error("采购入库单下发wms失败 单号:{}"+ buyorder.getBuyorderNo(),e);
        }
    }


    private void retrySendFailDeliveryDirectBuyorder(){
        User user = new User();
        user.setUsername("njadmin");
        user.setUserId(ErpConst.TWO);
        try {
            List<WmsSendOrder> deliveryDirectBuyorderSendList = wmsSendOrderMapper.getSendFailSaleorder(WmsSendOrderTypeEnum.PUT_DELIVERY_DIRECT_BUYORDER.getCode());
            for (WmsSendOrder item : deliveryDirectBuyorderSendList){
                List<PurchaseDeliveryDirectBatchDetailDto> batchDetailDtos = newBuyorderPeerListService.getExOrInPurchaseDeliveryDirectBatchDetails(item.getOrderId(),false);
                doPutDeliveryDirectPeerListService.doPutPurchaseOrderMethod(batchDetailDtos,user);
            }
        } catch (Exception e){
            logger.error("调用wms接口，重试下发直发采购单失败，e：",e);
        }
    }


    private void retrySendFailDeliveryDirectSaleorder(){
        User user = new User();
        user.setUsername("njadmin");
        user.setUserId(ErpConst.TWO);
        try {
            List<WmsSendOrder> deliveryDirectSaleorderSendList = wmsSendOrderMapper.getSendFailSaleorder(WmsSendOrderTypeEnum.PUT_DELIVERY_DIRECT_SALEORDER.getCode());
            for (WmsSendOrder item : deliveryDirectSaleorderSendList){
                // 查寻采购单关联的所有销售单
                List<Integer> saleorderIdListByBuyorderId = newBuyorderPeerListService.getSaleorderIdListByBuyorderId(item.getOrderId());
                if (!CollectionUtils.isEmpty(saleorderIdListByBuyorderId)) {
                    // 查询所有采购单
                    List<Integer> buyorderIdListBySaleorderIds = newBuyorderPeerListService.getBuyorderIdListBySaleorderIds(saleorderIdListByBuyorderId);
                    if (!CollectionUtils.isEmpty(buyorderIdListBySaleorderIds)) {
                        buyorderIdListBySaleorderIds.add(item.getOrderId());
                        // 去重
                        List<Integer> collect = buyorderIdListBySaleorderIds.stream().distinct().collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(collect) && collect.size() == 1) {
                            List<PurchaseDeliveryDirectBatchDetailDto> purchaseDeliveryDirectBatchDetails = newBuyorderPeerListService.getExOrInPurchaseDeliveryDirectBatchDetails(item.getOrderId(), true);
                            logger.info("直发采购单出库下发wms信息:{}", JSON.toJSONString(purchaseDeliveryDirectBatchDetails));
                            deliveryDirectSaleorderChooseService.putSaleOrderOutput(purchaseDeliveryDirectBatchDetails, user);
                        } else if (!CollectionUtils.isEmpty(collect) && collect.size() > 1) {
                            List<PurchaseDeliveryDirectBatchDetailDto> result = new ArrayList<>();
                            logger.info("直发采购单关联销售有多个采购单：{}", JSON.toJSONString(collect));
                            for (Integer buyId : collect) {
                                List<PurchaseDeliveryDirectBatchDetailDto> purchaseDeliveryDirectBatchDetails = newBuyorderPeerListService.getExOrInPurchaseDeliveryDirectBatchDetails(buyId, true);
                                if (!CollectionUtils.isEmpty(purchaseDeliveryDirectBatchDetails)) {
                                    result.addAll(purchaseDeliveryDirectBatchDetails);
                                }
                            }
                            logger.info("直发采购单关联销售有多个采购单触发采购单{} 直发采购单出库下发wms信息:{}", item.getOrderId(), JSON.toJSONString(result));
                            deliveryDirectSaleorderChooseService.putSaleOrderOutput(result, user);
                        }
                    }
                }

            }
        } catch (Exception e){
            logger.error("调用wms接口，重试下发直发销售单失败，e：",e);
        }
    }



    private void retrySendFailSaleorderModify() {
        try{
            List<WmsSendOrder> modifySendFailSaleorderlist = wmsSendOrderMapper.getSendFailSaleorder(WmsSendOrderTypeEnum.PUTSALEORDERMODIFY.getCode());
            for (WmsSendOrder wmsSendOrder : modifySendFailSaleorderlist) {
                putSaleorderModifyById(wmsSendOrder.getOrderId());
            }
        }catch (Exception e){
            logger.error("调用WMS修改接口重试失败,exception: {}",e);
        }
    }

    private void putSaleorderModifyById(Integer orderId) {
        User operater = new User();
        //系统自动将操作人置为njadmin
        operater.setUserId(ErpConst.TWO);
        logicalSaleorderChooseService.modifyOrderOutput(orderId,null, operater);
    }

    private void putpath(String param) {
        String[] split = param.split(":");
        String orderS = split[1].trim();
        String[] split1 = orderS.split(",");
        Saleorder saleorder = new Saleorder();
        for (String orderNo : split1) {
            Express search = new Express();
            search.setLogisticsNo(orderNo);
            search.setLogisticsComments("虚拟快递单");
            saleorder.setSaleorderNo(orderNo);
            Saleorder saleOrderId = saleorderMapper.getSaleorderBySaleorderNo(saleorder);
            List<Express> expressList = expressMapper.getExpressInfoByLogAndComments(search);
            for (Express express : expressList) {
                String printOutOrderUrl = getPrintOutOrderUrl(saleOrderId, express);
                putPrintOutOrder(express,printOutOrderUrl);
            }

        }
    }
    private String getPrintOutOrderUrl(Saleorder saleorder, Express expressDto) {
        StringBuffer path = new StringBuffer(erpUrl)
                .append("/warehouse/warehousesout/printOutOrder.do?")
                .append("expressId=" + expressDto.getExpressId())
                .append("&orderId=" + saleorder.getSaleorderId())
                .append("&bussinessType=496&bussinessNo=" + saleorder.getSaleorderNo())
                .append("&type_f=");
        String type = warehouseStockService.getPrintOutType(saleorder);

        if (StringUtil.isBlank(type)) {
            return "";
        }
        StringBuffer result = path.append(type);
        path.append("&expressType=0");
        return result.toString();
    }

    private void putPrintOutOrder(Express express, String printOutOrderUrl) {
        if (StringUtil.isEmpty(printOutOrderUrl)) {
            return;
        }
        String wmsOrderNo = express.getWmsOrderNo();
        String[] split = wmsOrderNo.split("/");
        PutPathDto putPathDto = new PutPathDto();
        putPathDto.setSOReference1(split[0]);
        putPathDto.setOrderNo(split[1]);
        List<PutPathDetailDto> detailDtoList = new ArrayList<>();
        PutPathDetailDto putPathDetailDto = new PutPathDetailDto();
        putPathDetailDto.setOrderNo(split[1]);
        putPathDetailDto.setSOReference1(split[0]);
        putPathDetailDto.setPicture_type(PictureTypeEnum.GOODS_INVOICE.getCode());
        putPathDetailDto.setPicture_path(printOutOrderUrl);
        detailDtoList.add(putPathDetailDto);
        putPathDto.setDetails(detailDtoList);
        try {
            logger.info("随货同行单至WMS接口的请求: putPathDto:{}", JSON.toJSONString(putPathDto));
            XxlJobLogger.log("随货同行单至WMS接口的请求: putPathDto:{}", JSON.toJSONString(putPathDto));
            WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_PATH_DATA);
            WmsResponse response = wmsInterface.request(putPathDto);
            logger.info("随货同行单至WMS接口的返回: putPathDto:{},response:{}", JSON.toJSONString(putPathDto), JSON.toJSONString(response));
            XxlJobLogger.log("随货同行单至WMS接口的返回: putPathDto:{},response:{}", JSON.toJSONString(putPathDto), JSON.toJSONString(response));
        } catch (Exception e) {
            logger.error("随货同行单至WMS error putPrintOutOrder:{}" + putPathDto.toString(), e);
        }
    }
    private void purchaseExgAuditFinish(String param) {
        String[] split = param.split(":");
        String afterSaleNo = split[1].trim();

        AfterSales afterSalesByNo = afterSalesMapper.getAfterSalesByNo(afterSaleNo);
        AfterSalesVo afterSaleVoById = afterSalesMapper.getAfterSaleVoById(afterSalesByNo.getAfterSalesId());

        User user = new User();
        user.setUsername("njadmin");
        user.setUserId(2);

        try {
            purchaseExgAuditFinishListener.onActionHappen(afterSaleVoById,true,user);
        } catch (Exception e) {
            XxlJobLogger.log("采购换货审核完成报错", e);
            logger.error("采购换货审核完成报错", e);
        }
    }

    private void cancelin(String param) {
        String[] split = param.split(":");
        String type = split[1].trim();
        String afterSaleNo = split[2].trim();
        try{
            //取消订单参数对象
            CancelPoDto cancelPoDto = new CancelPoDto();
            cancelPoDto.setDocNo(afterSaleNo);
            cancelPoDto.setPoType(type);
            cancelPoDto.setErpCancelReason("取消");
            logger.info("ERP取消入库单至WMS的请求: cancelPoDto:{}" + JSON.toJSONString(cancelPoDto));
            //撤销入库单
            WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.CANCEL_PO);
            WmsResponse wmsResponse = wmsInterface.request(cancelPoDto);
            logger.info("ERP取消入库单至WMS的响应:" + JSON.toJSONString(wmsResponse));
        }catch (Exception e){
            logger.error("【cancelin】处理异常",e);
        }
    }


    private void putSaleReturn(String param) {
        String[] split = param.split(":");
        String no = split[1].trim();
        AfterSales afterSalesByNo = afterSalesMapper.getAfterSalesByNo(no);
        AfterSalesVo afterSalesVo = new AfterSalesVo();
        afterSalesVo.setAfterSalesId(afterSalesByNo.getAfterSalesId());
        User user = new User();
        user.setUsername("njadmin");
        user.setUserId(2);
         try {
             putAfterReturnAuditFinishLister.onActionHappen(afterSalesVo,true,user);
         } catch (Exception e) {
             XxlJobLogger.log("ERP下发退货单至WMS的请求接口报错", e);
         }
    }

    private void cancelOut(String param) {
        String[] split = param.split(":");
        String nolist = split[1].trim();
        String[] nolists = nolist.split(",");
        for (String no : nolists) {
            cancelTypeService.cancelOutSaleOutMethod(no, CancelReasonConstant.SEND_ORDER);
        }
    }


    private void putExgorder(String param) throws Exception {
        String[] split = param.split(":");
        String afterNo = split[1].trim();
        User user = new User();
        user.setUsername("njadmin");
        user.setUserId(2);
        AfterSales afterSalesByNo = afterSalesMapper.getAfterSalesByNo(afterNo);
        List<WmsLogicalOrdergoods> list = wmsLogicalOrdergoodsMapper.getAfterorderLogicalChooseInfoByNo(afterNo);
        logicalAfterorderChooseService.putExchangeWMS(afterSalesByNo,list,user);
    }

    private void putSaleorder(String param) throws Exception {
        String[] split = param.split(":");
        String saleorderNos = split[1].trim();
        String[] nolists = saleorderNos.split(",");
        for (String saleorderNo : nolists) {
//            WmsResponse response = logicalSaleorderChooseService.cancelOutByNo(saleorderNo);
//            if("1".equals(response.getReturnFlag()) || CancelCodeConstant.OUTNO_EXIST_CODE.equals(response.getReturnCode())){
                putSaleorderByNo(saleorderNo);
//            }
        }
    }

    /**
     * 重新下发wms
     * @param id 盘亏出库单id
     * @throws Exception
     */
    private void putInventoryOutOrder(Integer id) throws Exception{

        HandlerStepContext param = new HandlerStepContext();
        param.put("inventoryOutOrderId",Long.valueOf(id));
        inventoryOutWmsRequestHandler.dealWith(param);

    }
    private void putSaleorderByNo(String saleorderNo) throws Exception {
        Saleorder order = new Saleorder();
        order.setSaleorderNo(saleorderNo);
        User user = new User();
        user.setUsername("njadmin");
        user.setUserId(2);
        order = saleorderMapper.getSaleorderBySaleorderNo(order);
        List<WmsLogicalOrdergoods> saleorderLogicalChooseInfoByNo = wmsLogicalOrdergoodsMapper.getSaleorderLogicalChooseInfoByNo(saleorderNo);
        List<WmsLogicalOrdergoods> list = new ArrayList<>();
        for (WmsLogicalOrdergoods wmsLogicalOrdergoods : saleorderLogicalChooseInfoByNo) {
            if(wmsLogicalOrdergoods.getOccupyNum() > 0){
                wmsLogicalOrdergoods.setNum(wmsLogicalOrdergoods.getOccupyNum());
                list.add(wmsLogicalOrdergoods);
            }
        }
        if(order == null){
            return;
        }
        if (!CollectionUtils.isEmpty(list)){
            logicalSaleorderChooseService.chooseLogicalSaleorder(order, user);
        }else{
//            logicalSaleorderChooseService.cancelOutByNo(saleorderNo,CancelReasonConstant.CLOSE_ORDER);
            cancelTypeService.cancelOutSaleOutMethod(saleorderNo, CancelReasonConstant.CLOSE_ORDER);
            WmsSendOrder wmsSendOrder = new WmsSendOrder();
            wmsSendOrder.setOrderNo(saleorderNo);
            wmsSendOrder.setOrderId(order.getOrderId());
            wmsSendOrder.setOrderType(0);
            wmsSendOrder = wmsSendOrderMapper.getWmsSendOrderInfo(wmsSendOrder);
            if(wmsSendOrder != null){
                WmsSendOrder updateWmsSendorder = new WmsSendOrder();
                updateWmsSendorder.setWmsSendOrderId(wmsSendOrder.getWmsSendOrderId());
                updateWmsSendorder.setSendStatus(ErpConst.ONE);
                wmsSendOrderMapper.updateByPrimaryKeySelective(updateWmsSendorder);
            }
        }
    }

    private void putBuyorder(String param) throws Exception {
        String[] split = param.split(":");
        String buyorderNos = split[1].trim();
        String[] nolists = buyorderNos.split(",");
        for (String no : nolists) {
            Buyorder buyOrderByOrderNo = buyorderMapper.getBuyOrderByOrderNo(no);
            purcjaseOrderService.dealWithSingleBuyOrder(buyOrderByOrderNo.getBuyorderId());
        }
    }

    private void putZhisale(String param) {
        User user = new User();
        user.setUsername("njadmin");
        user.setUserId(ErpConst.TWO);
        try {
            String[] split = param.split(":");
            String buyorderNos = split[1].trim();
            String[] nolists = buyorderNos.split(",");
            for (String no : nolists) {
                Buyorder buyorder = buyorderMapper.getBuyOrderByOrderNo(no);

                // 查寻采购单关联的所有销售单
                List<Integer> saleorderIdListByBuyorderId = newBuyorderPeerListService.getSaleorderIdListByBuyorderId(buyorder.getBuyorderId());
                if (!CollectionUtils.isEmpty(saleorderIdListByBuyorderId)) {
                    // 查询所有采购单
                    List<Integer> buyorderIdListBySaleorderIds = newBuyorderPeerListService.getBuyorderIdListBySaleorderIds(saleorderIdListByBuyorderId);
                    if (!CollectionUtils.isEmpty(buyorderIdListBySaleorderIds)) {
                        buyorderIdListBySaleorderIds.add(buyorder.getBuyorderId());
                        // 去重
                        List<Integer> collect = buyorderIdListBySaleorderIds.stream().distinct().collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(collect) && collect.size() == 1) {
                            List<PurchaseDeliveryDirectBatchDetailDto> purchaseDeliveryDirectBatchDetails = newBuyorderPeerListService.getExOrInPurchaseDeliveryDirectBatchDetails(buyorder.getBuyorderId(), true);
                            logger.info("直发采购单出库下发wms信息:{}", JSON.toJSONString(purchaseDeliveryDirectBatchDetails));
                            deliveryDirectSaleorderChooseService.putSaleOrderOutput(purchaseDeliveryDirectBatchDetails, user);
                        } else if (!CollectionUtils.isEmpty(collect) && collect.size() > 1) {
                            List<PurchaseDeliveryDirectBatchDetailDto> result = new ArrayList<>();
                            logger.info("直发采购单关联销售有多个采购单：{}", JSON.toJSONString(collect));
                            for (Integer buyId : collect) {
                                List<PurchaseDeliveryDirectBatchDetailDto> purchaseDeliveryDirectBatchDetails = newBuyorderPeerListService.getExOrInPurchaseDeliveryDirectBatchDetails(buyId, true);
                                if (!CollectionUtils.isEmpty(purchaseDeliveryDirectBatchDetails)) {
                                    result.addAll(purchaseDeliveryDirectBatchDetails);
                                }
                            }
                            logger.info("直发采购单关联销售有多个采购单触发采购单{} 直发采购单出库下发wms信息:{}", buyorder.getBuyorderId(), JSON.toJSONString(result));
                            deliveryDirectSaleorderChooseService.putSaleOrderOutput(result, user);
                        }
                    }
                }

//                List<PurchaseDeliveryDirectBatchDetailDto> batchDetailDtos = newBuyorderPeerListService.getExOrInPurchaseDeliveryDirectBatchDetails(buyorder.getBuyorderId(),true);
//                deliveryDirectSaleorderChooseService.putSaleOrderOutput(batchDetailDtos,user);
            }
        } catch (Exception e){
            logger.error("调用wms接口，重试下发直发销售单失败，e：",e);
        }
    }

    private void putZhibuy(String param) {
        User user = new User();
        user.setUsername("njadmin");
        user.setUserId(ErpConst.TWO);
        try {
            String[] split = param.split(":");
            String buyorderNos = split[1].trim();
            String[] nolists = buyorderNos.split(",");
            for (String no : nolists) {
                Buyorder buyorder = buyorderMapper.getBuyOrderByOrderNo(no);
                List<PurchaseDeliveryDirectBatchDetailDto> batchDetailDtos = newBuyorderPeerListService.getExOrInPurchaseDeliveryDirectBatchDetails(buyorder.getBuyorderId(),false);
                doPutDeliveryDirectPeerListService.doPutPurchaseOrderMethod(batchDetailDtos,user);
            }

        } catch (Exception e){
            logger.error("调用wms接口，重试下发直发采购单失败，e：",e);
        }
    }
    /*private PutPurchaseOrderDto convertBuyorderToPurcharse(Buyorder buyorder) {

        PutPurchaseOrderDto putPurchaseOrderDto = new PutPurchaseOrderDto();
        putPurchaseOrderDto.setPoType(WmsInterfaceOrderType.INPUT_PURCHASE);
        putPurchaseOrderDto.setDocNo(buyorder.getBuyorderNo());
        putPurchaseOrderDto.setPoCreationTime(DateUtil.convertString(buyorder.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        putPurchaseOrderDto.setExpectedArriveTime1(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
        putPurchaseOrderDto.setExpectedArriveTime2(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));

        putPurchaseOrderDto.setSupplierId(buyorder.getTraderId());
        putPurchaseOrderDto.setSupplierName(buyorder.getTraderName());

        User user = userService.getUserById(buyorder.getCreator());
        putPurchaseOrderDto.setPoReferenceA(user.getPositionName());
        putPurchaseOrderDto.setPoReferenceB(user.getUsername());
        putPurchaseOrderDto.setNotes(buyorder.getLogisticsComments());

        List<PutPurchaseOrderGoodsDto> details = new ArrayList<>();


        buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyorder.getBuyorderId())
                .stream()
                .forEach(buyOrderGood -> {
                    if (GoodsConstants.VIRTUAL_GOODS.contains(buyOrderGood.getGoodsId())){
                        return;
                    }
                    PutPurchaseOrderGoodsDto detailItem = new PutPurchaseOrderGoodsDto();
                    detailItem.setSku(buyOrderGood.getSku());
                    detailItem.setOrderedQty(buyOrderGood.getNum());
                    detailItem.setDedi04(buyOrderGood.getBuyorderGoodsId() + StringUtils.EMPTY);


                    if (SpecialDeliveryEnum.NEED_SPECIAL_DELIVERY.getCode().equals(
                            buyorderGoodsMapper.getSpecialDeliveryByRelatedId(buyOrderGood.getBuyorderGoodsId()))){
                        detailItem.setLotAtt07(buyorder.getBuyorderNo());
                    }

                    if(detailItem.getOrderedQty() > 0){
                        details.add(detailItem);
                    }
                });

        putPurchaseOrderDto.setDetails(details);

        return putPurchaseOrderDto;
    }*/

}
