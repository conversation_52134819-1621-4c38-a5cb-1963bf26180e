package com.vedeng.activiti.taskassign;

import java.math.BigDecimal;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.xml.ws.WebServiceContext;

import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.finance.model.TraderAccountPeriodApply;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderModifyApply;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.soap.service.VedengSoapService;

public class EditEarlyBuyorderVerifyExecutionListener implements ExecutionListener {
	private static final org.slf4j.Logger LOG = LoggerFactory.getLogger(EditEarlyBuyorderVerifyExecutionListener.class);
    // 运行时注入service
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private SaleorderService saleorderService = (SaleorderService) context.getBean("saleorderService");
    private ActionProcdefService actionProcdefService = (ActionProcdefService) context.getBean("actionProcdefService");
	private SaleorderMapper saleorderMapper = (SaleorderMapper) context.getBean("saleorderMapper");
	private SaleorderGoodsMapper saleorderGoodsMapper =(SaleorderGoodsMapper)context.getBean("saleorderGoodsMapper");
	@Resource
    private WebServiceContext webServiceContext;
    //提前采购审核触发器
    //根据穿参通用回写主表中状态
    public void notify(DelegateExecution execution) throws Exception {
		ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request =  ra.getRequest();
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		Saleorder saleorderInfo = (Saleorder) execution.getVariable("saleorderInfo");
		if(execution.getCurrentActivityName().equals("审核完成")){
		    actionProcdefService.updateInfo("T_SALEORDER", "SALEORDER_ID", saleorderInfo.getSaleorderId(), "LOCKED_STATUS", 0,2);
			saleorderService.updateUnlockSaleOrderWarning(saleorderInfo.getSaleorderId());
		    ResultInfo<?> res = saleorderService.saveApplyPurchase(saleorderInfo, user);
		}else{
            	    actionProcdefService.updateInfo("T_SALEORDER", "SALEORDER_ID", saleorderInfo.getSaleorderId(), "ADVANCE_PURCHASE_STATUS", 3,2);
		}
    }
}
/**
 * 
 * <AUTHOR>
 *
 */
