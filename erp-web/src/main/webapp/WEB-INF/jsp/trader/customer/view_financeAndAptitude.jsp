<%@page import="java.util.Date"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="客户财务与资质信息" scope="application" />
<%@ include file="../../common/common.jsp"%>

	<%@ include file="customer_tag.jsp"%>
	<div class="content">
        <div class="parts">
            <div class="title-container">
                <div class="table-title">资质信息</div>
                <c:if test="${(isCustomeroperation || isbelong) &&(traderCustomer.aptitudeStatus==null||traderCustomer.aptitudeStatus==3||traderCustomer.aptitudeStatus==2)}">
                <!-- 	<div class="title-click  pop-new-data" layerParams='{"width":"1200px","height":"500px","title":"编辑","link":"./editAptitude.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}"}'>编辑</div> -->
					<!-- 判断traderCustomer.isEnable 如果为0，直接给提示 -->
					<c:choose>
						<c:when test="${traderCustomer.isEnable==0}">
							<a class="title-click" onclick="alert('当前客户为禁用状态，请启用后再进行资质调整！')">编辑</a>
						</c:when>
						<c:otherwise>
							<div class="title-click addtitle"
								 tabTitle='{"num":"customer${traderCustomer.traderId}","link":"./trader/customer/editAptitude.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}","title":"编辑资质"}'>编辑</div>
						</c:otherwise>
					</c:choose>
					<!-- 判断traderCustomer.isEnable 如果为0，直接给提示 -->

                </c:if>
				<c:if test="${traderCustomer.aptitudeStatus!=null and traderCustomer.aptitudeStatus==0 and (taskInfo.assignee == curr_user.username or candidateUserMap['belong'])}">

					<div class="title-click" onclick="checkVerifyStatus(${traderCustomer.traderCustomerId})">资质复审</div>
					<div id="checkAptitudePage" style="display: none" class="addtitle"
						 tabTitle='{"num":"customer${traderCustomer.traderId}","link":"./trader/customer/editAptitude.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}","title":"编辑资质"}'></div>
				</c:if>
				<c:if test="${traderCustomer.aptitudeStatus!=null and traderCustomer.aptitudeStatus==4 and (taskInfo.assignee == curr_user.username or candidateUserMap['belong'])}">

					<div class="title-click" onclick="checkVerifyStatus(${traderCustomer.traderCustomerId})">资质初审</div>
					<div id="checkAptitudePage" style="display: none" class="addtitle"
						 tabTitle='{"num":"customer${traderCustomer.traderId}","link":"./trader/customer/editAptitude.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}","title":"编辑资质"}'></div>
				</c:if>
				<c:if test="${traderCustomer.aptitudeStatus!=null and traderCustomer.aptitudeStatus==5 and (taskInfo.assignee == curr_user.username or candidateUserMap['belong'])}">

					<div class="title-click" onclick="checkVerifyStatus(${traderCustomer.traderCustomerId})">公章审核</div>
					<div id="checkAptitudePage" style="display: none" class="addtitle"
						 tabTitle='{"num":"customer${traderCustomer.traderId}","link":"./trader/customer/editAptitude.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}","title":"编辑资质"}'></div>
				</c:if>
				<%--<c:if test="${isbelong and traderCustomer.aptitudeStatus==1}">--%>
					<%--<div class="title-click addtitle"--%>
						 <%--tabTitle='{"num":"customer${traderCustomer.traderId}","link":"./trader/customer/editAptitude.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}","title":"编辑资质"}'>修改资质</div>--%>
				<%--</c:if>--%>
            </div>
           <% Date date=new Date();long now = date.getTime(); request.setAttribute("now", now); %>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <tbody>
                <c:if test="${traderCustomer.customerNature eq 466}">
                    <tr>
                        <td class="table-smallest">是否为营利机构</td>
                        <td style="text-align: left;">
                            <c:if test="${traderCustomer.isProfit ne 1}">是</c:if>
                            <c:if test="${traderCustomer.isProfit eq 1}">否</c:if>
                        </td>
                    </tr>
                </c:if>
				<tr>
                    <c:forEach items="${bussinessList }" var="bus" varStatus="st">
                        <c:if test="${st.index == 0}">
                            <c:set var="threeInOne" value="${bus.threeInOne}"></c:set>
                        </c:if>
                    </c:forEach>
					<td class="table-smallest">三证合一</td>
					<td style="text-align: left;">
                        <c:if test="${threeInOne eq 1}">是</c:if>
                        <c:if test="${threeInOne ne 1}">否</c:if>
                    </td>
				</tr>
                    <tr>
                        <td class="table-smallest">营业执照</td>
                        <td style="text-align: left;">
                        	<%--<c:choose> --%>
							   <%--<c:when test="${not empty business && not empty business.uri }">--%>
							   		 <%--<a href="http://${business.domain}${business.uri}" target="_blank">营业执照</a>--%>
							   <%--</c:when>--%>
							   <%--<c:otherwise>--%>
							   		<%--营业执照--%>
							   <%--</c:otherwise>--%>
							<%--</c:choose> &nbsp;&nbsp;&nbsp;&nbsp;--%>
                        	<%--有效期： <date:date value ="${business.begintime}" format="yyyy-MM-dd"/>--%>
		                        	<%--<c:if test="${business ne null && business.endtime eq null}">-无限期</c:if>--%>
			                        <%--<c:if test="${business.endtime ne null}">-<date:date value ="${business.endtime}" format="yyyy-MM-dd"/></c:if>&nbsp;&nbsp;&nbsp;&nbsp;--%>
			                        <%--<c:if test="${business.endtime ne null && business.endtime ne 0 && business.endtime lt now }"><span style="color: red">（已过期）</span></c:if> --%>
		                        	<%--<c:if test="${business.isMedical eq 1}">含有医疗器械</c:if>--%>
								<c:choose>
									<c:when test="${bussinessList ne null }">
										<c:forEach items="${bussinessList }" var="bus" varStatus="st">
											<c:if test="${st.index == 0}">
												<c:set var="begintime" value="${bus.begintime}"></c:set>
												<c:set var="endtime" value="${bus.endtime}"></c:set>
											</c:if>
											<c:if test="${bus.uri ne null && not empty bus.uri}">
												<a href="http://${bus.domain}${bus.uri}" target="_blank">营业执照- ${st.index + 1}</a>&nbsp;&nbsp;
											</c:if>
										</c:forEach>
									</c:when>
									<c:otherwise>
										营业执照&nbsp;
									</c:otherwise>
								</c:choose>&nbsp;&nbsp;&nbsp;
								有效期：<date:date value ="${begintime} " format="yyyy-MM-dd"/>
								<c:if test="${bussinessList ne null && endtime eq null && not empty bussinessList}">-无限期</c:if>
								<c:if test="${endtime ne null}">
									-<date:date value ="${endtime} " format="yyyy-MM-dd"/>
								</c:if>
								<%--&nbsp;&nbsp;&nbsp;&nbsp; 许可证编号：${sn}--%>
								<c:if test="${endtime ne null && endtime ne 0 && endtime lt now }"><span style="color: red">（已过期）</span></c:if>

						</td>
                    </tr>
                    <tr>
                        <td class="table-smallest">税务登记证</td>
                        <td style="text-align: left;">
                        	<c:choose>
							   <c:when test="${not empty tax && not empty tax.uri}">
							   		 <a href="http://${tax.domain}${tax.uri}" target="_blank">税务登记证</a>
							   </c:when>
							   <c:otherwise>
							   		税务登记证
							   </c:otherwise>
							</c:choose> &nbsp;&nbsp;&nbsp;&nbsp;
                        	有效期：  <date:date value ="${tax.begintime}" format="yyyy-MM-dd"/>
		                        	<c:if test="${tax ne null && tax.endtime eq null}">-无限期</c:if>
			                        <c:if test="${tax.endtime ne null}">-<date:date value ="${tax.endtime}" format="yyyy-MM-dd"/></c:if>
			                        <c:if test="${tax.endtime ne null && tax.endtime ne 0 && tax.endtime lt now }"><span style="color: red">（已过期）</span></c:if>
                        </td>
                    </tr>
                    <tr>
                        <td class="table-smallest">组织机构代码证</td>
                        <td style="text-align: left;">
                        	<c:choose>
							   <c:when test="${not empty orga && not empty orga.uri}">
							   		 <a href="http://${orga.domain}${orga.uri}" target="_blank">组织机构代码证</a>
							   </c:when>
							   <c:otherwise>
							   		组织机构代码证
							   </c:otherwise>
							</c:choose> &nbsp;&nbsp;&nbsp;&nbsp;
                       		有效期：<date:date value ="${orga.begintime}" format="yyyy-MM-dd"/>
	                        		<c:if test="${orga ne null && orga.endtime eq null}">-无限期</c:if>
		                        	<c:if test="${orga.endtime ne null}">-<date:date value ="${orga.endtime}" format="yyyy-MM-dd"/></c:if>
		                        	<c:if test="${orga.endtime ne null && orga.endtime ne 0 && orga.endtime lt now }"><span style="color: red">（已过期）</span></c:if>

                        </td>
                    </tr>
                    <c:if test="${traderCustomer.customerNature eq 465}">
	                    <tr>
	                        <td class="table-smallest">二类医疗资质</td>
	                        <td style="text-align: left;">
	                        	<c:choose>
							   <c:when test="${twoMedicalList ne null }">
							   <c:forEach items="${twoMedicalList }" var="twoMedical" varStatus="st">
								   <c:if test="${st.index == 0}">
								   	<c:set var="beginTime2" value="${twoMedical.begintime}"></c:set>
								   	<c:set var="endTime2" value="${twoMedical.endtime}"></c:set>
								   	<c:set var="sn2" value="${twoMedical.sn}"></c:set>
								   </c:if>
									   	<c:if test="${twoMedical.uri ne null && not empty twoMedical.uri}">
									   		 <a href="http://${twoMedical.domain}${twoMedical.uri}" target="_blank">二类医疗资质- ${st.index + 1}</a>&nbsp;&nbsp;
									   	</c:if>
							   </c:forEach>
							   </c:when>
							   <c:otherwise>
							   		二类医疗资质&nbsp;
							   </c:otherwise>
							</c:choose>&nbsp;&nbsp;&nbsp;
                        	有效期：<date:date value ="${beginTime2} " format="yyyy-MM-dd"/>
                        			<c:if test="${twoMedicalList ne null && endTime2 eq null && not empty twoMedicalList}">-无限期</c:if>
                        			<c:if test="${endTime2 ne null}">
                        				-<date:date value ="${endTime2} " format="yyyy-MM-dd"/>
                        			</c:if>&nbsp;&nbsp;&nbsp;&nbsp; 许可证编号：${sn2}
                        			 <c:if test="${endTime2 ne null && endTime2 ne 0 && endTime2 lt now }"><span style="color: red">（已过期）</span></c:if>

	                        </td>
	                    </tr>
	                    <tr>
	                        <td class="table-smallest">二类医疗资质(旧国标)</td>
	                        <td style="text-align: left;">
	                        	<c:if test="${not empty two }">
					               <c:forEach items="${two }" var="mc">
					               	${mc.title}&nbsp;&nbsp;
					               </c:forEach>
					            </c:if>
	                        </td>

	                    </tr>
						<tr>
	                        <td class="table-smallest">二类医疗资质(新国标)</td>
	                        <td style="text-align: left;">
	                        	<c:if test="${not empty newTwo }">
					               <c:forEach items="${newTwo}" var="mc">
					               	${mc.title}&nbsp;&nbsp;
					               </c:forEach>
					            </c:if>
	                        </td>

	                    </tr>
	                    <tr>
	                        <td class="table-smallest">三类医疗资质</td>
	                        <td style="text-align: left;">
	                        	<c:choose>

									<c:when test="${threeMedical ne null }">
										<c:forEach items="${threeMedical}" var="three" varStatus="st">
											<c:if test="${st.index == 0}">
												<c:set var="beginTime3" value="${three.begintime}"></c:set>
												<c:set var="endTime3" value="${three.endtime}"></c:set>
												<c:set var="sn3" value="${three.sn}"></c:set>
											</c:if>
											<c:if test="${three.uri ne null && not empty three.uri}">
												<a href="http://${three.domain}${three.uri}" target="_blank">三类医疗资质- ${st.index + 1}</a>&nbsp;&nbsp;
											</c:if>
										</c:forEach>
									</c:when>
								   <%--<c:when test="${not empty threeMedical && not empty threeMedical.uri}">--%>
								   		 <%--<a href="http://${threeMedical.domain}${threeMedical.uri}" target="_blank">三类医疗资质</a>--%>
								   <%--</c:when>--%>
								   <c:otherwise>
								   		三类医疗资质
								   </c:otherwise>
								</c:choose>&nbsp;&nbsp;&nbsp;&nbsp;
	                        	有效期:
								<date:date value ="${beginTime3} " format="yyyy-MM-dd"/>
	                        			<c:if test="${beginTime3 ne null && endTime3 eq null}">-无限期</c:if>
	                        			<c:if test="${endTime3 ne null}">
	                        				-<date:date value ="${endTime3} " format="yyyy-MM-dd"/>
	                        			</c:if>&nbsp;&nbsp;&nbsp;&nbsp;许可证编号：${sn3}
	                        			 <c:if test="${endTime3 ne null && endTime3 ne 0 && endTime3 lt now }"><span style="color: red">（已过期）</span></c:if>
	                        </td>
	                    </tr>
	                    <tr>
	                        <td class="table-smallest">三类医疗资质(旧国标)</td>
	                        <td style="text-align: left;">
	                        	<c:if test="${not empty three }">
					               <c:forEach items="${three }" var="mc">
					               	${mc.title}&nbsp;&nbsp;
					               </c:forEach>
					            </c:if>
	                        </td>
	                    </tr>
						<tr>
	                        <td class="table-smallest">三类医疗资质(新国标)</td>
	                        <td style="text-align: left;">
	                        	<c:if test="${not empty newThree }">
					               <c:forEach items="${newThree}" var="mc">
					               	${mc.title}&nbsp;&nbsp;
					               </c:forEach>
					            </c:if>
	                        </td>
	                    </tr>
						<tr>
							<td class="table-smallest">备注</td>
							<td style="text-align: left;">
                                ${comment}
							</td>
						</tr>
                    </c:if>

                    <c:if test="${traderCustomer.customerNature eq 466}">
                    	<tr>
	                        <td class="table-smallest">医疗机构执业许可证</td>
	                        <td style="text-align: left;">
		                        <c:choose>
								   <c:when test="${practiceList ne null }">
								   <c:forEach items="${practiceList }" var="practice" varStatus="st">
								   <c:if test="${st.index == 0}">
								   	<c:set var="beginTime2" value="${practice.begintime}"></c:set>
								   	<c:set var="endTime2" value="${practice.endtime}"></c:set>
								   	<c:set var="sn2" value="${practice.sn}"></c:set>
								   </c:if>
								   	<c:if test="${practice.uri ne null && not empty practice.uri}">
								   		 <a href="http://${practice.domain}${practice.uri}" target="_blank">医疗机构执业许可证 - ${st.index + 1}</a>&nbsp;&nbsp;
								   	</c:if>
								   </c:forEach>
								   </c:when>
								   <c:otherwise>
								   		医疗机构执业许可证&nbsp;
								   </c:otherwise>
								</c:choose>&nbsp;&nbsp;&nbsp;
	                        	有效期：<date:date value ="${beginTime2} " format="yyyy-MM-dd"/>
	                        			<c:if test="${practiceList ne null  && endTime2 eq null && not empty practiceList}">-无限期</c:if>
	                        			<c:if test="${endTime2 ne null}">
	                        				-<date:date value ="${endTime2} " format="yyyy-MM-dd"/>
	                        			</c:if>&nbsp;&nbsp;&nbsp;&nbsp; 许可证编号：${sn2}
	                        			 <c:if test="${endTime2 ne null && endTime2 ne 0 && endTime2 lt now }"><span style="color: red">（已过期）</span></c:if>
	                        </td>
	                    </tr>

						<tr>
							<td class="table-smallest">中医诊所备案证</td>
							<td style="text-align: left;">
								<c:choose>
									<c:when test="${not empty clinic && not empty clinic.uri}">
										<a href="http://${clinic.domain}${clinic.uri}" target="_blank">中医诊所备案证</a>
									</c:when>
									<c:otherwise>
										中医诊所备案证
									</c:otherwise>
								</c:choose> &nbsp;&nbsp;&nbsp;&nbsp;
								有效期：  <date:date value ="${clinic.begintime}" format="yyyy-MM-dd"/>
								<c:if test="${clinic ne null && clinic.endtime eq null}">-无限期</c:if>
								<c:if test="${clinic.endtime ne null}">-<date:date value ="${clinic.endtime}" format="yyyy-MM-dd"/></c:if>
								<c:if test="${clinic.endtime ne null && clinic.endtime ne 0 && clinic.endtime lt now }"><span style="color: red">（已过期）</span></c:if>
								许可证编号：${clinic.sn}
							</td>
						</tr>
						<tr>
							<td class="table-smallest">动物诊疗许可证</td>
							<td style="text-align: left;">
								<c:choose>
									<c:when test="${not empty animal && not empty animal.uri}">
										<a href="http://${animal.domain}${animal.uri}" target="_blank">动物诊疗许可证</a>
									</c:when>
									<c:otherwise>
										动物诊疗许可证
									</c:otherwise>
								</c:choose> &nbsp;&nbsp;&nbsp;&nbsp;
								有效期：  <date:date value ="${animal.begintime}" format="yyyy-MM-dd"/>
								<c:if test="${animal ne null && animal.endtime eq null}">-无限期</c:if>
								<c:if test="${animal.endtime ne null}">-<date:date value ="${animal.endtime}" format="yyyy-MM-dd"/></c:if>
								<c:if test="${animal.endtime ne null && animal.endtime ne 0 && animal.endtime lt now }"><span style="color: red">（已过期）</span></c:if>
								许可证编号：${animal.sn}
							</td>
						</tr>
						<tr>
							<td class="table-smallest">其他</td>
							<td style="text-align: left;">
								<c:choose>
									<c:when test="${others ne null }">
										<c:forEach items="${others }" var="other" varStatus="st">

											<c:if test="${st.index == 0}">
												<c:set var="otherBeginTime" value="${other.begintime}"></c:set>
												<c:set var="otherEndTime" value="${other.endtime}"></c:set>
												<c:set var="otherSn" value="${other.sn}"></c:set>
											</c:if>
											<c:if test="${other.uri ne null && not empty other.uri}">
												<a href="http://${other.domain}${other.uri}" target="_blank">其他 - ${st.index + 1}</a>&nbsp;&nbsp;&nbsp;
											</c:if>
											有效期：<date:date value ="${other.begintime} " format="yyyy-MM-dd"/>
											<c:if test="${others ne null  && other.endtime eq null && not empty others && other.begintime ne null}">-无限期</c:if>
											<c:if test="${other.endtime ne null  && other.begintime ne null}">
												-<date:date value ="${other.endtime} " format="yyyy-MM-dd"/>
											</c:if>
											<c:if test="${other.endtime ne null && other.endtime ne 0 && other.endtime lt now  && other.begintime ne null}"><span style="color: red">（已过期）</span></c:if>&nbsp;&nbsp;
											许可证编号：${other.sn}
											<c:if test="${st.last == false}">
												</br>
											</c:if>
										</c:forEach>
									</c:when>
									<c:otherwise>
										其他
									</c:otherwise>
								</c:choose>&nbsp;&nbsp;&nbsp;

							</td>
						</tr>
                    </c:if>
                    <!-- add by fralin.wu for[耗材商城的客户管理-代付款证明] at 2018/11/22 begin -->
                    <tr>
                        <td class="table-smallest">代付款证明</td>
                        <td style="text-align: left;">
						   <c:forEach items="${pofPayList}" var="porPay" varStatus="st">
							   	<c:if test="${not empty porPay.uri && not empty porPay.domain}">
							   		 <a href="http://${porPay.domain}${porPay.uri}" target="_blank">代付款证明- ${st.index + 1}</a>&nbsp;&nbsp;
							   	</c:if>
						   </c:forEach>
                        </td>
                    </tr>
                    <!-- add by fralin.wu for[耗材商城的客户管理-代付款证明] at 2018/11/22 end -->

                </tbody>
            </table>
        </div>
		<div class="tcenter mb15 mt-5">

			<c:choose>
				<c:when test="${(isCustomeroperation or isbelong or isBelongBdOperator) and (traderCustomer.aptitudeStatus==1 or traderCustomer.aptitudeStatus==0 or traderCustomer.aptitudeStatus==4 or traderCustomer.aptitudeStatus==5)}">
					<!-- 判断traderCustomer.isEnable 如果为0，直接给提示 -->
					<c:choose>
						<c:when test="${traderCustomer.isEnable==0}">
							<button class="bt-bg-style bg-light-orange bt-small mr10" onclick="alert('当前客户为禁用状态，请启用后再进行资质调整！')">修改资质</button>
						</c:when>
						<c:otherwise>
							<button class="bt-bg-style bg-light-orange bt-small mr10" onclick="changeAptitude(${traderCustomer.traderCustomerId},${traderCustomer.aptitudeStatus},${taskInfo.id})">修改资质</button>
						</c:otherwise>
					</c:choose>
					<!-- 判断traderCustomer.isEnable 如果为0，直接给提示 -->


					<button id="changeAptitudeTitle" style="display: none;" type="button" class="addtitle" tabTitle='{"num":"customer${traderCustomer.traderId}","link":"./trader/customer/editAptitude.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}","title":"编辑资质"}'>修改资质</button>
				</c:when>
				<c:when test="${traderCustomer.aptitudeStatus!=null and (traderCustomer.aptitudeStatus==0 or traderCustomer.aptitudeStatus==4)}">
					<button type="button" class="bt-bg-style bt-small bg-light-greybe mr10">已申请审核</button>
				</c:when>
			</c:choose>
		</div>
		<div class="parts">
			<div class="title-container">
				<div class="table-title">资质审核状态</div>
			</div>
			<table class="table table-bordered table-striped table-condensed table-centered">
				<tbody>
				<tr>
					<td >资质审核状态</td>
					<td >
						<c:if test="${empty traderCustomer.aptitudeStatus || traderCustomer.aptitudeStatus eq 3}">待审核</c:if>
						<c:if test="${traderCustomer.aptitudeStatus eq 4}">初审中</c:if>
						<c:if test="${traderCustomer.aptitudeStatus eq 5}">公章审核</c:if>
						<c:if test="${traderCustomer.aptitudeStatus eq 0}">复审中</c:if>
						<c:if test="${traderCustomer.aptitudeStatus eq 1}">审核通过</c:if>
						<c:if test="${traderCustomer.aptitudeStatus eq 2}">审核不通过</c:if>
					</td>
				</tr>
				</tbody>
			</table>
		</div>
        <div class="parts">
            <div class="title-container">
                <div class="table-title">财务信息</div>
                <c:if
						test="${(not empty trader)&&(finance ==null || finance.checkStatus==null || finance.checkStatus == 0
						||finance.checkStatus==3||finance.checkStatus==2) && customerInfoByTraderCustomer.isEnable == 1 && ((customerInfoByTraderCustomer.verifyStatus != null && customerInfoByTraderCustomer.verifyStatus != 0 )|| customerInfoByTraderCustomer.verifyStatus == null) }">
				<div class="title-click  pop-new-data" layerParams='{"width":"600px","height":"380px","title":"编辑","link":"./toEditFinancePage.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&source=${trader.source}"}'>编辑</div>
			    </c:if>
				<c:if test="${(trader.source eq 1) &&isFinanceBelong &&(finance ==null || finance.checkStatus==null||finance.checkStatus==3||finance.checkStatus==2)}">

					<div class="title-click  pop-new-data" layerParams='{"width":"600px","height":"380px","title":"编辑","link":"./editYxgFinancePage.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&source=${trader.source}"}'>编辑</div>
				</c:if>
				<c:if test="${finance.checkStatus!=null and finance.checkStatus==0 and (financeTaskInfo.assignee == curr_user.username or financeCandidateUserMap['belong'])}">
					<div class="title-click  pop-new-data" layerParams='{"width":"600px","height":"380px","title":"审核","link":"./editYxgFinancePage.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&source=${trader.source}"}'>财务审核</div>
				</c:if>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <tbody>
                    <tr>
                        <td >注册地址</td>
                        <td >${finance.regAddress}</td>
                        <td >注册电话</td>
                        <td >${finance.regTel}</td>
                    </tr>
                    <tr>
                        <td>税务登记号</td>
                        <td>${finance.taxNum}</td>
                        <td>一般纳税人资质</td>
                        <td>
                        	<c:choose>
							   <c:when test="${finance ne null && not empty finance.averageTaxpayerUri }">
							   		 <a href="http://${finance.averageTaxpayerDomain}${finance.averageTaxpayerUri}" target="_blank">查看资质</a>
							   </c:when>
							   <c:otherwise>

							   </c:otherwise>
							</c:choose>
                        </td>
                    </tr>
                    <tr>
                        <td>开户银行</td>
                        <td>${finance.bank}</td>
                        <td>开户行支付联行号</td>
                        <td>${finance.bankCode}</td>
                    </tr>
                    <tr>
                        <td>银行帐号</td>
                        <td colspan="3">${finance.bankAccount}</td>
                    </tr>
                    <tr>
                        <td>账户余额</td>
                        <td><fmt:formatNumber type="number" value="${finance.amount}" pattern="0.00" maxFractionDigits="2" /></td>
                        <td>信用评估</td>
                        <td>${finance.creditRating}</td>
                    </tr>
                </tbody>
            </table>
        </div>

		<div class="tcenter mb15 mt-5">

			<c:choose>
				<%--当前登录用户是该客户归属销售领导或者归属销售 and 来源耗材商城 and 客户资历审核通过--%>
				<c:when test="${isFinanceBelong && trader.source eq 1  and finance.checkStatus==1}">
					<span class=" pop-new-data bt-bg-style bg-light-orange bt-small"  layerParams='{"width":"600px","height":"380px","title":"编辑","link":"./editYxgFinancePage.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&source=${trader.source}"}'>修改财务信息</span>
				</c:when>
				<%--来源ERP and 客户资历审核通过--%>
				<c:when test="${ trader.source eq 0  and finance.checkStatus==1}">
					<span class=" pop-new-data bt-bg-style bg-light-orange bt-small"  layerParams='{"width":"600px","height":"380px","title":"编辑","link":"./toEditFinancePage.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&source=${trader.source}"}'>修改财务信息</span>
				</c:when>
				<c:when test="${finance.checkStatus!=null and finance.checkStatus==0}">
					<button type="button" class="bt-bg-style bt-small bg-light-greybe mr10">已申请审核</button>
				</c:when>
			</c:choose>
		</div>
		<div class="parts">
			<div class="title-container">
				<div class="table-title nobor">
					资质审核记录
				</div>
			</div>
			<table class="table table-bordered table-striped table-condensed table-centered">
				<thead>
				<tr>
					<td>操作人</td>
					<td>操作时间</td>
					<td>操作事项</td>
					<td>备注</td>
				</tr>
				</thead>
				<tbody>
				<c:if test="${null!=historicActivityInstance}">
					<c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
						<c:if test="${not empty  hi.activityName}">
							<tr>
								<td>
									<c:choose>
										<c:when test="${hi.activityType == 'startEvent'}">
											${startUser}
										</c:when>
										<c:when test="${hi.activityType == 'intermediateThrowEvent'}">
										</c:when>
										<c:otherwise>
											<c:if test="${historicActivityInstance.size() == status.count}">
												${verifyUsers}
											</c:if>
											<c:if test="${historicActivityInstance.size() != status.count}">
												<c:forEach items="${assigneeVos}" var="assigneeVo">
													<c:if test="${assigneeVo.assignee eq hi.assignee}">
														${assigneeVo.realName}
													</c:if>
												</c:forEach>
											<%--	${hi.assignee}--%>
											</c:if>
										</c:otherwise>
									</c:choose>


								</td>
								<td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
								<td>
									<c:choose>
										<c:when test="${hi.activityType == 'startEvent'}">
											开始
										</c:when>
										<c:when test="${hi.activityType == 'intermediateThrowEvent'}">
											结束
										</c:when>
										<c:otherwise>
											${hi.activityName}
										</c:otherwise>
									</c:choose>
								</td>
								<td class="font-red">${commentMap[hi.taskId]}</td>
							</tr>
						</c:if>
					</c:forEach>
				</c:if>
				<c:if test="${empty historicActivityInstance}">
					<!-- 查询无结果弹出 -->
					<tr>
						<td colspan="4">暂无审核记录。</td>
					</tr>
				</c:if>
				</tbody>
			</table>

			<div class="clear"></div>
		</div>
		<div class="parts">
			<div class="title-container">
				<div class="table-title nobor">
					财务审核记录
				</div>
			</div>
			<table class="table table-bordered table-striped table-condensed table-centered">
				<thead>
				<tr>
					<td>操作人</td>
					<td>操作时间</td>
					<td>操作事项</td>
					<td>备注</td>
				</tr>
				</thead>
				<tbody>
				<c:if test="${null!=financeHistoricActivityInstance}">
					<c:forEach var="hi" items="${financeHistoricActivityInstance}" varStatus="status">
						<c:if test="${not empty  hi.activityName}">
							<tr>
								<td>
									<c:choose>
										<c:when test="${hi.activityType == 'startEvent'}">
											${financeStartUser}
										</c:when>
										<c:when test="${hi.activityType == 'intermediateThrowEvent'}">
										</c:when>
										<c:otherwise>
											<c:if test="${financeHistoricActivityInstance.size() == status.count}">
												${verifyUsersFinance}
											</c:if>
											<c:if test="${financeHistoricActivityInstance.size() != status.count}">
												${hi.assignee}
											</c:if>
										</c:otherwise>
									</c:choose>


								</td>
								<td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
								<td>
									<c:choose>
										<c:when test="${hi.activityType == 'startEvent'}">
											开始
										</c:when>
										<c:when test="${hi.activityType == 'intermediateThrowEvent'}">
											结束
										</c:when>
										<c:otherwise>
											${hi.activityName}
										</c:otherwise>
									</c:choose>
								</td>
								<td class="font-red">${financeCommentMap[hi.taskId]}</td>
							</tr>
						</c:if>
					</c:forEach>
				</c:if>
				<c:if test="${empty financeHistoricActivityInstance}">
					<!-- 查询无结果弹出 -->
					<tr>
						<td colspan="4">暂无审核记录。</td>
					</tr>
				</c:if>
				</tbody>
			</table>

			<div class="clear"></div>
		</div>
		<div class="parts">
			<div class="title-container">
				<div class="table-title nobor">账期信息</div>
			</div>
			<div class="title-container">
				<div class="table-title nobor">客户信用：</div>
				<div>${finance.creditRating}</div>
			</div>
			<table class="table table-bordered table-striped table-condensed table-centered">
				<thead>
				<tr>
					<th class="table-smallest">
						<div class="customernamec pos_rel">
							<span >账期类型</span>
							<i class="iconbluemouth"></i>
							<div class="pos_abs customernameshow" style="display: none;">
								<label>1）正式账期：开放给正式合作的客户的可支持长期使用的信用额，一个客户只有一个正式账期，只能对此账期进行额度调整、有效期调整。可适用于多个订单。
									2）临时账期：针对某个客户在短期时间内可临时使用的一笔信用额，可适用于多个订单。一个客户可以有多个临时账期，账期之间有效期不可交叉出现。
									3）订单账期：对某个初次或极少合作的客户针对某个特定订单开放的账期额度，该账期只能适用于该订单。
								</label>
							</div>
						</div>
					</th>
					<th class="table-smallest">
						<div class="customernamec pos_rel">
							<span>账期总额度</span>
							<i class="iconbluemouth"></i>
							<div class="pos_abs customernameshow" style="display: none;">
								<label>此客户下所有已申请审核通过的对应类型的账期总额度</label>
							</div>
						</div>
					</th>
					<th class="table-smallest">
						<div class="customernamec pos_rel">
							<span>账期有效期</span>
							<i class="iconbluemouth"></i>
							<div class="pos_abs customernameshow" style="display: none;">
								<label>当前可用账期的有效期</label>
							</div>
						</div>
					</th>
					<th class="table-smallest">最近一次修改日期</th>
					<th class="table-smallest">
						<div class="customernamec pos_rel">
							<span>有效账期数</span>
							<i class="iconbluemouth"></i>
							<div class="pos_abs customernameshow" style="display: none;">
								<label>同时满足（申请审核通过）&（目前处于有效期内）的账期个数</label>
							</div>
						</div>
					</th>
					<th class="table-smallest">
						<div class="customernamec pos_rel">
							<span>当前可用总额度</span>
							<i class="iconbluemouth"></i>
							<div class="pos_abs customernameshow" style="display: none;">
								<label>有效期内的账期可用于支付新订单的额度<br>【账期额度-已冻结额度（订单生效未实际支付的账期）-已占用额度（已用于支付的账期）+已归还的账期（售后退货或结款偿还的账期）】总和</label>
							</div>
						</div>
					</th>
					<th class="table-smallest">未归还账期总额</th>
					<th class="table-smallest">逾期未还金额</th>
					<th class="table-smallest">使用次数</th>
					<th class="table-smallest">逾期次数</th>
					<th class="table-smallest">操作</th>
				</tr>
				</thead>

				<tbody>
					<tr>
						<td>订单账期</td>
						<c:choose>
							<c:when test="${empty billInfoListOrder}">
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
							</c:when>
							<c:otherwise>
								<td><c:if test="${empty billInfoListOrder.totalAmount}">0</c:if>
									<fmt:formatNumber type="number" value="${billInfoListOrder.totalAmount}" pattern="0.00" maxFractionDigits="2" />
								</td>
								<%--<td><c:if test="${empty billInfoListOrder.availableAmount && empty billInfoListOrder.unreturnedAmount}"></c:if>${billInfoListOrder.availableAmount+billInfoListOrder.unreturnedAmount}</td>--%>
								<td>-</td>
								<td><c:if test="${empty billInfoListOrder.lastModTime || billInfoListOrder.lastModTime == 0}">-</c:if><date:date value="${billInfoListOrder.lastModTime}" /></td>
								<td><c:if test="${empty billInfoListOrder.countOfValid}">-</c:if>${billInfoListOrder.countOfValid}</td>
								<td><c:if test="${empty billInfoListOrder.availableAmount}">-</c:if>
									<fmt:formatNumber type="number" value="${billInfoListOrder.availableAmount}" pattern="0.00" maxFractionDigits="2" />
								</td>
								<td><c:if test="${empty billInfoListOrder.unreturnedAmount}">-</c:if>
									<fmt:formatNumber type="number" value="${billInfoListOrder.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
								</td>
								<td><c:if test="${empty billInfoListOrder.overDueAmount}">-</c:if>
										<fmt:formatNumber type="number" value="${billInfoListOrder.overDueAmount}" pattern="0.00" maxFractionDigits="2" />
								</td>
								<td><c:if test="${empty billInfoListOrder.countOfUsed}">-</c:if>${billInfoListOrder.countOfUsed}</td>
								<td><c:if test="${empty billInfoListOrder.countOfOverDue}">-</c:if>${billInfoListOrder.countOfOverDue}</td>
							</c:otherwise>
						</c:choose>
						<td>
							<%--入参：客户名称（traderId），账期类型（3）--%>
							<div class="title-click addtitle" tabTitle='{"num":"accountListOrder${traderCustomer.traderId}","link":"./finance/accountperiod/getCustomerAccountListPage.do?traderId=${traderCustomer.traderId}&billType=3","title":"使用明细"}'>使用明细</div>
							<div class="title-click pop-new-data1" layerParams='{"width":"850px","height":"650px","title":"账期申请",
							"link":"./accountOtherapply.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&billPeriodType=3"}'>账期申请</div>

						</td>

					</tr>
					<tr>
						<td>临时账期</td>
						<c:choose>
							<c:when test="${empty  billInfoListTemporary}">
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
							</c:when>
							<c:otherwise>
								<%--<td><c:if test="${empty billInfoListTemporary.availableAmount && empty billInfoListTemporary.unreturnedAmount}"></c:if>${billInfoListTemporary.availableAmount+billInfoListTemporary.unreturnedAmount}</td>--%>
								<td><c:if test="${empty billInfoListTemporary.totalAmount}">0</c:if>
									<fmt:formatNumber type="number" value="${billInfoListTemporary.totalAmount}" pattern="0.00" maxFractionDigits="2" />
								</td>
								<td>-</td>
								<td><c:if test="${empty billInfoListTemporary.lastModTime || billInfoListTemporary.lastModTime == 0}">-</c:if><date:date value="${billInfoListTemporary.lastModTime}" /></td>
								<td><c:if test="${empty billInfoListTemporary.countOfValid}">-</c:if>${billInfoListTemporary.countOfValid}</td>
								<td><c:if test="${empty billInfoListTemporary.availableAmount}">-</c:if>
									<fmt:formatNumber type="number" value="${billInfoListTemporary.availableAmount}" pattern="0.00" maxFractionDigits="2" />
								</td>
								<td><c:if test="${empty billInfoListTemporary.unreturnedAmount}">-</c:if>
									<fmt:formatNumber type="number" value="${billInfoListTemporary.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
								</td>
								<td><c:if test="${empty billInfoListTemporary.overDueAmount}">-</c:if>
									<fmt:formatNumber type="number" value="${billInfoListTemporary.overDueAmount}" pattern="0.00" maxFractionDigits="2" />
								</td>
								<td><c:if test="${empty billInfoListTemporary.countOfUsed}">-</c:if>${billInfoListTemporary.countOfUsed}</td>
								<td><c:if test="${empty billInfoListTemporary.countOfOverDue}">-</c:if>${billInfoListTemporary.countOfOverDue}</td>
							</c:otherwise>
						</c:choose>
						<td>
							<%--入参：客户名称（traderId），账期类型（2）--%>
							<div class="title-click addtitle" tabTitle='{"num":"accountListTemporary${traderCustomer.traderId}","link":"./finance/accountperiod/getCustomerAccountListPage.do?traderId=${traderCustomer.traderId}&billType=2","title":"使用明细"}'>使用明细</div>
							<div class="title-click pop-new-data1" layerParams='{"width":"900px","height":"700px","title":"账期申请",
	               				"link":"./accountOtherapply.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&billPeriodType=2"}'>账期申请</div>

						</td>
					</tr>

					<tr>
						<td>正式账期</td>
						<c:choose>
							<c:when test="${empty  billInfoListFormal}">
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
							</c:when>
							<c:otherwise>
								<%--<td><c:if test="${empty billInfoListFormal.availableAmount && empty billInfoListFormal.unreturnedAmount}"></c:if>${billInfoListFormal.availableAmount+billInfoListFormal.unreturnedAmount}</td>--%>
								<td>
									<c:if test="${empty billInfoListFormal.totalAmount}">0</c:if>
									<fmt:formatNumber type="number" value="${billInfoListFormal.totalAmount}" pattern="0.00" maxFractionDigits="2" />
								</td>
								<td><c:if test="${empty billInfoListFormal.billPeriodEnd}">-</c:if><date:date value="${billInfoListFormal.billPeriodEnd}" format="yyyy-MM-dd" /></td>
								<td><c:if test="${empty billInfoListFormal.lastModTime || billInfoListFormal.lastModTime == 0}">-</c:if><date:date value="${billInfoListFormal.lastModTime}" /></td>
								<td><c:if test="${empty billInfoListFormal.countOfValid}">-</c:if>${billInfoListFormal.countOfValid}</td>
								<td>
									<c:if test="${empty billInfoListFormal.availableAmount}">-</c:if>
									<fmt:formatNumber type="number" value="${billInfoListFormal.availableAmount}" pattern="0.00" maxFractionDigits="2" />
								</td>
								<td>
									<c:if test="${empty billInfoListFormal.unreturnedAmount}">-</c:if>
									<fmt:formatNumber type="number" value="${billInfoListFormal.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
								</td>
								<td>
									<c:if test="${empty billInfoListFormal.overDueAmount}">-</c:if>
									<fmt:formatNumber type="number" value="${billInfoListFormal.overDueAmount}" pattern="0.00" maxFractionDigits="2" />
								</td>
								<td><c:if test="${empty billInfoListFormal.countOfUsed}">-</c:if>${billInfoListFormal.countOfUsed}</td>
								<td><c:if test="${empty billInfoListFormal.countOfOverDue}">-</c:if>${billInfoListFormal.countOfOverDue}</td>
							</c:otherwise>
						</c:choose>
						<td>
							<%--入参：客户名称（traderId），账期类型（1）--%>
							<div class="title-click addtitle" tabTitle='{"num":"accountListFormal${traderCustomer.traderId}","link":"./finance/accountperiod/getCustomerAccountListPage.do?traderId=${traderCustomer.traderId}&billType=1","title":"使用明细"}'>使用明细</div>
							<div class="title-click  pop-new-data1" layerParams='{"width":"850px","height":"650px","title":"账期申请",
	               				"link":"./accountFormalapply.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}"}'>账期申请</div>

						</td>
					</tr>
				</tbody>
			</table>
			<iframe src="./getAmountBillPageApply.do?traderCustomerId=${traderCustomer.traderCustomerId}&traderType=1&isEnable=${customerInfoByTraderCustomer.isEnable}" frameborder="0" style="width: 100%;border: 0; " onload="setIframeHeight(this)" scrolling="no"></iframe>
		</div>
	<!--
        <iframe src="./getAmountBillPage.do?traderId=${customerInfoByTraderCustomer.traderId}&traderType=1&isEnable=${customerInfoByTraderCustomer.isEnable}" frameborder="0" style="width: 100%;border: 0; " onload="setIframeHeight(this)" scrolling="no"></iframe>

        <span style="display:none;">

			<div class="title-click nobor  pop-new-data" id="popBill"></div>
		</span> -->
        <iframe src="./getCapitalBillPage.do?traderId=${customerInfoByTraderCustomer.traderId}&traderType=1" frameborder="0" style="width: 100%;border: 0; " onload="setIframeHeight(this)" scrolling="no">
         </iframe>
    </div>
</body>
<script type="text/javascript"
		src='<%= basePath %>static/js/customer/finance_and_aptitude.js?rnd=${resourceVersionKey}'></script>
</html>
