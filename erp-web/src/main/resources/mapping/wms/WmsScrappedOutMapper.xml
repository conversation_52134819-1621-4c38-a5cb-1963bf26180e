<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wms.dao.WmsScrappedOutMapper" >
    <resultMap id="BaseResultMap" type="com.wms.model.po.WMSScrappedOutOrder" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="type" property="type" jdbcType="INTEGER" />
        <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
        <result column="verify_status" property="verifyStatus" jdbcType="INTEGER" />
        <result column="out_status" property="outStatus" jdbcType="INTEGER" />
        <result column="return_status" property="returnStatus" jdbcType="INTEGER" />
        <result column="borrow_reason" property="borrowReason" jdbcType="VARCHAR" />
        <result column="borrow_trader_name" property="borrowTraderName" jdbcType="BIGINT" />
        <result column="logistic_commnet" property="logisticCommnet" jdbcType="VARCHAR" />
        <result column="logistic_commnet" property="logisticCommnet" jdbcType="VARCHAR" />
        <result column="belong_department" property="belongDepartment" jdbcType="VARCHAR" />
        <result column="scrap_type" property="scrapType" jdbcType="INTEGER" />
        <result column="scrap_level" property="scrapLevel" jdbcType="INTEGER" />
        <result column="scrap_deal_type" property="scrapDealType" jdbcType="INTEGER" />
        <result column="applyer" property="applyer" jdbcType="VARCHAR" />
        <result column="applyer_department" property="applyerDepartment" jdbcType="VARCHAR" />
        <result column="recipient_applyer" property="recipientApplyer" jdbcType="VARCHAR" />
        <result column="recipient_department" property="recipientDepartment" jdbcType="VARCHAR" />
        <result column="recipient_type" property="recipientType" jdbcType="INTEGER" />
        <result column="apple_out_date" property="appleOutDate" jdbcType="VARCHAR" />
        <result column="use_nature" property="useNature" jdbcType="VARCHAR" />
        <result column="use_name" property="useName" jdbcType="VARCHAR" />
        <result column="receiver" property="receiver" jdbcType="VARCHAR" />
        <result column="receiver_address" property="receiverAddress" jdbcType="VARCHAR" />
        <result column="receiver_telphone" property="receiverTelphone" jdbcType="VARCHAR" />
        <result column="receiver_phone" property="receiverPhone" jdbcType="VARCHAR" />
        <result column="detail_address" property="detailAddress" jdbcType="VARCHAR" />
        <result column="apply_reason" property="applyReason" jdbcType="VARCHAR" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="real_output_time" property="realOutputTime" jdbcType="VARCHAR" />
        <result column="customer_receive_time" property="customerReceiveTime" jdbcType="VARCHAR" />
        <result column="approval_time" property="approvalTime" jdbcType="VARCHAR" />
        <result column="add_time" property="addTime" jdbcType="VARCHAR" />
        <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
        <result column="creator" property="creator" jdbcType="VARCHAR" />
        <result column="updator" property="updator" jdbcType="VARCHAR" />
        <result column="is_delete" property="isDelete" jdbcType="INTEGER" />
        <result column="output_num " property="outputNum" jdbcType="INTEGER" />
        <result column="sku_no" property="skuNo" jdbcType="VARCHAR" />
        <result column="model" property="model" jdbcType="VARCHAR" />
        <result column="unit_name" property="unitName" jdbcType="VARCHAR" />
        <result column="brand_name" property="brandName" jdbcType="VARCHAR" />
        <result column="applyer_id " property="applyerId" jdbcType="INTEGER" />
        <result column="applyer_department_id " property="applyerDepartmentId" jdbcType="INTEGER" />
    </resultMap>
    <sql id="Base_Column_List" >
        id, type, order_no, verify_status,out_status,return_status,borrow_reason, borrow_trader_id,borrow_trader_name, logistic_commnet,
        belong_department, scrap_type, scrap_level, scrap_deal_type, applyer, applyer_department,
        recipient_applyer, recipient_department, recipient_type, apple_out_date, use_nature,
        use_name, receiver, receiver_address, receiver_telphone,receiver_phone, detail_address, apply_reason,
        remark, real_output_time, customer_receive_time,approval_time, add_time, update_time, creator,
        updator, is_delete,applyer_id,applyer_department_id
    </sql>
    <select id="queryScrapedOutlistPage"  parameterType="Map" resultType="com.wms.model.po.WMSScrappedOutOrder">
        SELECT
        T.ID,
        T.ORDER_NO,
        T.APPLYER_DEPARTMENT,
        T.APPLYER,
        T.APPLE_OUT_DATE,
        T.SCRAP_DEAL_TYPE,
        T.SCRAP_LEVEL,
        T.REAL_OUTPUT_TIME,
        T.SCRAP_TYPE,
        T.OUT_STATUS,
        GOOD.OUTPUT_NUM,
        SKU.SKU_NAME,
        GOOD.SKU_NO,
        SKU.MODEL,
        U.UNIT_NAME,
        BR.BRAND_NAME,
        COUNT(GOOD.id)COUNTGOODS,
        DEA.TITLE scrapDealTypeStr,
        LE.TITLE scrapLevelStr,
        TY.TITLE scrapTypeStr
        FROM
        T_WMS_OUTPUT_ORDER T
        LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS GOOD ON GOOD.WMS_OUTPUT_ORDER_ID = T.ID
        LEFT JOIN V_CORE_SKU SKU ON SKU.SKU_NO = GOOD.SKU_NO
        LEFT JOIN V_CORE_SPU SPU ON SPU.SPU_ID = SKU.SPU_ID
        LEFT JOIN T_UNIT U ON SKU.UNIT_ID=U.UNIT_ID
        LEFT JOIN T_BRAND BR ON BR.BRAND_ID = SPU.BRAND_ID
        LEFT JOIN T_SYS_OPTION_DEFINITION DEA ON DEA.SYS_OPTION_DEFINITION_ID=T.scrap_deal_type
        LEFT JOIN T_SYS_OPTION_DEFINITION LE ON LE.SYS_OPTION_DEFINITION_ID=T.scrap_level
        LEFT JOIN T_SYS_OPTION_DEFINITION TY ON TY.SYS_OPTION_DEFINITION_ID = T.scrap_type
        WHERE
        T.TYPE IN (2,3) AND T.is_delete=0
            <if test="scrappedOutQueryDto.orderNo != null and scrappedOutQueryDto.orderNo != ''">

                AND ORDER_NO LIKE
                CONCAT('%',#{scrappedOutQueryDto.orderNo,jdbcType=VARCHAR},'%' )

            </if>
            <if test="scrappedOutQueryDto.applyer != null and scrappedOutQueryDto.applyer != ''">

                AND APPLYER LIKE
                CONCAT('%',#{scrappedOutQueryDto.applyer,jdbcType=VARCHAR},'%' )

            </if>
            <if test="scrappedOutQueryDto.applyerDepartment != null and scrappedOutQueryDto.applyerDepartment != ''">

                AND APPLYER_DEPARTMENT LIKE
                CONCAT('%',#{scrappedOutQueryDto.applyerDepartment,jdbcType=INTEGER},'%' )

            </if>
            <if test="scrappedOutQueryDto.scrapType != null and scrappedOutQueryDto.scrapType != ''">

                AND SCRAP_TYPE = #{scrappedOutQueryDto.scrapType,jdbcType=INTEGER}

            </if>
            <if test="scrappedOutQueryDto.scrapLevel != null and scrappedOutQueryDto.scrapLevel != ''">

                AND SCRAP_LEVEL = #{scrappedOutQueryDto.scrapLevel,jdbcType=INTEGER}

            </if>
            <if test="scrappedOutQueryDto.skuName != null and scrappedOutQueryDto.skuName != ''">

                AND SKU.SKU_NAME LIKE
                CONCAT('%',#{scrappedOutQueryDto.skuName,jdbcType=VARCHAR},'%' )

            </if>
            <if test="scrappedOutQueryDto.skuNo != null and scrappedOutQueryDto.skuNo != ''">

                AND SKU.SKU_NO LIKE
                CONCAT('%',#{scrappedOutQueryDto.skuNo,jdbcType=VARCHAR},'%' )

            </if>
            <if test="scrappedOutQueryDto.brandName != null and scrappedOutQueryDto.brandName != ''">

                AND BRAND_NAME LIKE
                CONCAT('%',#{scrappedOutQueryDto.brandName,jdbcType=VARCHAR},'%' )

            </if>
            <if test="scrappedOutQueryDto.addStartTime != null and scrappedOutQueryDto.addStartTime != ''">

                and APPLE_OUT_DATE <![CDATA[>=]]> CONCAT(#{scrappedOutQueryDto.addStartTime,jdbcType=VARCHAR},' 00:00:00')

            </if>
            <if test="scrappedOutQueryDto.addEndTime != null and scrappedOutQueryDto.addEndTime != ''">

                and APPLE_OUT_DATE <![CDATA[<=]]> CONCAT(#{scrappedOutQueryDto.addEndTime,jdbcType=VARCHAR},' 23:59:59')

            </if>
            <if test="scrappedOutQueryDto.scrapDealType != null and scrappedOutQueryDto.scrapDealType != ''">

                AND SCRAP_DEAL_TYPE = #{scrappedOutQueryDto.scrapDealType,jdbcType=INTEGER}

            </if>
            <if test="scrappedOutQueryDto.model != null and scrappedOutQueryDto.model != ''">

                AND  SKU.MODEL LIKE
                CONCAT('%',#{scrappedOutQueryDto.model,jdbcType=VARCHAR},'%' )

            </if>
            <if test="scrappedOutQueryDto.outputNum != null and scrappedOutQueryDto.outputNum != ''">

                AND  T.OUT_STATUS = #{scrappedOutQueryDto.outStatus,jdbcType=INTEGER}

            </if>
            GROUP BY T.ID
            ORDER BY T.ID ASC
    </select>


    <select id="queryOutputGoodsByScrapedOutId" resultType="com.wms.model.dto.WmsOutputOrderGoodsDto" parameterType="java.lang.Long" >
    SELECT
       goods.*,
       sku.SKU_NAME,
       sku.MODEL,
       BR.BRAND_NAME
    FROM T_WMS_OUTPUT_ORDER_GOODS goods
    LEFT JOIN V_CORE_SKU sku ON goods.sku_no = sku.SKU_NO
    LEFT JOIN V_CORE_SPU spu ON spu.SPU_ID = sku.SPU_ID
    LEFT JOIN T_BRAND BR ON BR.BRAND_ID = spu.BRAND_ID
    where wms_output_order_id = #{scrappedOutId,jdbcType=BIGINT}
  </select>
    <select id="getScrappedOrderByNo" resultType="com.wms.model.po.WmsOutputOrder">
    SELECT
       *
    FROM T_WMS_OUTPUT_ORDER T
    where T.ORDER_NO = #{orderNo,jdbcType=VARCHAR} AND T.TYPE IN (2,3) AND T.is_delete=0
    </select>
    <select id="selectById" resultType="com.wms.model.po.WMSScrappedOutOrder">
    SELECT
    T.*,
    (CASE T.OUT_STATUS
	  WHEN 2 THEN
		 '全部出库'
		WHEN 1 THEN
		'部分出库'
	  ELSE '未出库'   END ) outStatusStr,
    DEA.TITLE scrapDealTypeStr,
    LE.TITLE scrapLevelStr,
    TY.TITLE scrapTypeStr
    FROM T_WMS_OUTPUT_ORDER T
    LEFT JOIN T_SYS_OPTION_DEFINITION DEA ON DEA.SYS_OPTION_DEFINITION_ID=T.scrap_deal_type
    LEFT JOIN T_SYS_OPTION_DEFINITION LE ON LE.SYS_OPTION_DEFINITION_ID=T.scrap_level
    LEFT JOIN T_SYS_OPTION_DEFINITION TY ON TY.SYS_OPTION_DEFINITION_ID = T.scrap_type
    where T.ID = #{scrappedOutId,jdbcType=VARCHAR}
    </select>

</mapper>