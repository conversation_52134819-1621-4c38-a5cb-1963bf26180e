$(function(){
	
	$("#myform").submit(function(){
		checkLogin();
		
		if($("#comments").val()==''){
			warnTips("comments","备注不允许为空");
			return  false;
		}else{
			delWarnTips("comments");
		}
		if($('#comments').val().length>128){
			warnTips("comments","备注不能大于128字符");
			return false;
		}else{
			delWarnTips("comments");
		}
		$.ajax({
			url:page_url+'/order/bussinesschance/saveSalesBussnessChanceComments.do',
			data:$('#myform').serialize(),
			type:"POST",
			dataType : "json",
			async: false,
			success:function(data)
			{

				if(data.code==0){
					window.parent.location.reload();
				}else{
					layer.alert("操作失败！")
				}

				
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
		return false;
	})
});

