package com.vedeng.activiti;

import com.vedeng.activiti.constants.ProcessConstants;
import com.vedeng.activiti.model.HistoryVerfiyRecord;
import com.vedeng.authorization.model.User;
import com.vedeng.system.service.VerifiesRecordService;
import org.activiti.engine.*;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Objects;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [<EMAIL>]
 */
public abstract class ProcessSupport {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    private final ProcessKeyGenerator processKeyGenerator = ProcessKeyGenerator.createWithPrefix(getProcessDefinitionPrefixKey());

    private final ProcessEngine processEngine;

    private final VerifiesRecordService verifiesRecordService;

    public ProcessSupport(ProcessEngine processEngine, VerifiesRecordService verifiesRecordService) {
        Objects.requireNonNull(processEngine, "processEngine is null");
        Objects.requireNonNull(verifiesRecordService, "verifiesRecordService is null");

        this.processEngine = processEngine;
        this.verifiesRecordService = verifiesRecordService;
    }

    /**
     * Retrieves the prefix of business definition.
     *
     * @return
     * @see ProcessConstants
     */
    protected abstract String getProcessDefinitionPrefixKey();


    protected TaskService getTaskService() {
        return processEngine.getTaskService();
    }


    protected List<Comment> getProcessInstanceComments(String processInstanceId){
        return getTaskService().getProcessInstanceComments(processInstanceId);
    };


    protected List<IdentityLink> getIdentityLinksForTaskByTaskId(String taskId){
        return getTaskService().getIdentityLinksForTask(taskId);
    };

    protected IdentityService getIdentityService() {
        return processEngine.getIdentityService();
    }


    protected RuntimeService getRuntimeService() {
        return processEngine.getRuntimeService();
    }

    protected HistoryService getHistoryService() {
        return processEngine.getHistoryService();
    }

    protected List<HistoricProcessInstance> getHistoricProcessInstanceListByBusinessKey(String businessKey){
        Objects.requireNonNull(businessKey, "businessKey is null");
        return getHistoryService().createHistoricProcessInstanceQuery().processInstanceBusinessKey(businessKey).orderByProcessInstanceStartTime().asc().list();
    }


    protected List<HistoricActivityInstance> getHistoricActivityInstanceListByProInstanceId(String processInstanceId){
        Objects.requireNonNull(processInstanceId, "processInstanceId is null");
        return getHistoryService().createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).orderByHistoricActivityInstanceStartTime().asc().list();
    }

    protected String generateBusinessKey(Object suffix) {
        Objects.requireNonNull(suffix, "suffix is null");

        String suffixStr = suffix.toString();
        if (suffixStr.length() == 0) {
            throw new IllegalArgumentException("suffix is empty");
        }

        String businessKey = processKeyGenerator.generate(suffixStr);
        if (businessKey.length() > ProcessConstants.DEFAULT_BIZ_KEY_LIMITED_LENGTH) {
            throw new IllegalArgumentException("Exceed the limited length " + ProcessConstants.DEFAULT_BIZ_KEY_LIMITED_LENGTH);
        }

        return businessKey;
    }


    protected String generateBusinessKey(Object suffix,String type) {
        Objects.requireNonNull(suffix, "suffix is null");
        Objects.requireNonNull(type, "type is null");

        String suffixStr = suffix.toString();
        if (suffixStr.length() == 0) {
            throw new IllegalArgumentException("suffix is empty");
        }
        if (type.length() == 0) {
            throw new IllegalArgumentException("type is empty");
        }

        String businessKey = processKeyGenerator.generate(suffixStr,type);
        if (businessKey.length() > ProcessConstants.DEFAULT_BIZ_KEY_LIMITED_LENGTH) {
            throw new IllegalArgumentException("Exceed the limited length " + ProcessConstants.DEFAULT_BIZ_KEY_LIMITED_LENGTH);
        }

        return businessKey;
    }

    protected String getTenantId(ProcessInstanceContext context) {
        return context.getTenantId() != null ? context.getTenantId() : ProcessConstants.DEFAULT_TENANT_ID;
    }


    protected Task getTaskByBusinessKey(String businessKey) {
        Objects.requireNonNull(businessKey, "businessKey is null");
        return getTaskService().createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
    }

    protected Task getTaskByTaskId(String taskId) {
        Objects.requireNonNull(taskId, "taskId is null");
        return getTaskService().createTaskQuery().taskId(taskId).singleResult();
    }


    protected void startProcessInstance(ProcessInstanceContext processInstanceContext) {
        Objects.requireNonNull(processInstanceContext.getAssignee(), "assignee is null.");
        Objects.requireNonNull(processInstanceContext.getProcessDefinitionKey(), "processDefinitionKey is null.");
        Objects.requireNonNull(processInstanceContext.getBusinessKey(), "businessKey is null.");

        try {
            getRuntimeService()
                    .startProcessInstanceByKeyAndTenantId(processInstanceContext.getProcessDefinitionKey(), processInstanceContext.getBusinessKey(),
                            processInstanceContext.getAttributes(), getTenantId(processInstanceContext));
        } catch (Exception e) {
            logger.error("启动流程时发生错误 - info:{}", processInstanceContext.toString());
            throw new IllegalStateException(String.format("Started process [%s] error", getProcessDefinitionPrefixKey()), e);
        }
    }


    protected void doCompleteTask(Task task, String assignee, String comment, Map<String, Object> attributes, Integer checkStatus) {
        Objects.requireNonNull(task, "task is null");
        Objects.requireNonNull(assignee, "assignee is null");
        Objects.requireNonNull(checkStatus, "checkStatus is null");

        if (StringUtils.isNotBlank(comment)) {
            getTaskService().addComment(task.getId(), task.getProcessInstanceId(), comment);
        }

        getTaskService().setAssignee(task.getId(), assignee);
        try {
            getIdentityService().setAuthenticatedUserId(assignee);
            getTaskService().complete(task.getId(), attributes);
        } catch (Exception e) {
            logger.error("完成流程时发生错误 - taskId: {}", task.getId(), e);
            throw new IllegalStateException(e.getMessage(), e);
        } finally {
            getIdentityService().setAuthenticatedUserId(null);
        }

        //如果未结束添加审核对应主表的审核状态
        Task newTaskInfo = getTaskService().createTaskQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
        if (newTaskInfo != null) {
            verifiesRecordService.saveVerifiesInfo(task.getId(), checkStatus);
        }
    }


    protected List<HistoryVerfiyRecord> getHistoricByBusinessKey(String businessKey, User user){
        if (Objects.isNull(user)){
            user=new User();
            user.setCompanyId(1);
            user.setUserId(2);
            user.setUsername("njadmin");
        }
        List<HistoricProcessInstance>  historicProcessInstance = getHistoricProcessInstanceListByBusinessKey(businessKey);

        Task taskByBusinessKey = getTaskByBusinessKey(businessKey);

        List<HistoricActivityInstance> historicActivityInstanceList = new ArrayList<>();

        Map<String, Object> commentMap = new HashMap<String, Object>();

        if (CollectionUtils.isNotEmpty(historicProcessInstance)) {
            String processInstanceId;
            for (HistoricProcessInstance hi : historicProcessInstance) {
                List<HistoricActivityInstance> hia = getHistoricActivityInstanceListByProInstanceId(hi.getId());
                processInstanceId = hi.getId();
                List<Comment> commentList = getProcessInstanceComments(processInstanceId);
                for (Comment c : commentList) {
                    commentMap.put(c.getTaskId(), c.getFullMessage());
                }

                historicActivityInstanceList.addAll(hia);
            }
        }

        List<HistoryVerfiyRecord> historyVerfiyRecords = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(historicActivityInstanceList)){
            int i= 1;
            for(HistoricActivityInstance activityInstance:historicActivityInstanceList){
                HistoryVerfiyRecord historyVerfiyRecord = new HistoryVerfiyRecord();
                if (!"startEvent".equals(activityInstance.getActivityType()) && !"endEvent".equals(activityInstance.getActivityType())){
                    if (historicActivityInstanceList.size() != i){
                        historyVerfiyRecord.setOperaterName(activityInstance.getAssignee());
                    }
                    if (historicActivityInstanceList.size() == i && !Objects.isNull(taskByBusinessKey)){
                        List<IdentityLink> identityLinksForTaskByTaskId = getIdentityLinksForTaskByTaskId(taskByBusinessKey.getId());
                        if (CollectionUtils.isNotEmpty(identityLinksForTaskByTaskId)){
                            List<String> collect = identityLinksForTaskByTaskId.stream().map(IdentityLink::getUserId).collect(Collectors.toList());
                            historyVerfiyRecord.setOperaterName(String.join(",",collect));
                        }
                    }
                }
                i++;
                historyVerfiyRecord.setOperateTime(activityInstance.getEndTime());
                if ("startEvent".equals(activityInstance.getActivityType())){
                    historyVerfiyRecord.setOperate("开始");
                }
                if ("intermediateThrowEvent".equals(activityInstance.getActivityType())){
                    historyVerfiyRecord.setOperate("结束");
                }
                if (!"startEvent".equals(activityInstance.getActivityType()) && !"intermediateThrowEvent".equals(activityInstance.getActivityType())){
                    historyVerfiyRecord.setOperate(activityInstance.getActivityName());
                }
                historyVerfiyRecord.setComment((String) commentMap.get(activityInstance.getTaskId()));
                historyVerfiyRecords.add(historyVerfiyRecord);
            }

        }
        return historyVerfiyRecords;
    }


}
