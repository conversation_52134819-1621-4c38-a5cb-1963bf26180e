package com.wms.inventorytransfer.dao;

import com.wms.inventorytransfer.model.dto.InventoryTransferDto;
import com.wms.inventorytransfer.model.po.InventoryTransferDetailPO;
import com.wms.inventorytransfer.model.po.InventoryTransferPO;
import com.wms.inventorytransfer.model.vo.InventoryTransferDetailVO;
import com.wms.inventorytransfer.model.vo.InventoryTransferVO;

import javax.inject.Named;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @describe 库存转移单
 * @date 2020/7/10 14:22:10
 */
@Named("inventoryTransferMapper")
public interface InventoryTransferMapper {

    /**
     * 获取库存转移单信息
     *
     * @param map
     * @return
     * <AUTHOR>
     * @date 2020/7/10 14:10:15
     */
    List<InventoryTransferVO> getInventoryTransferVosListPage(Map<String, Object> map);


    /**
     * @return
     * @describe 获取库存转移单详情信息
     * <AUTHOR>
     * @date 2020/7/10 15:27:15
     */
    List<InventoryTransferDetailVO> getInventoryTransferDetailById(Integer inventoryTransferId);

    /**
     * @param inventoryTransferId
     * @return
     * @describe 通过库存转移单ID查询信息
     * <AUTHOR>
     * @date 2020/7/13 9:45:22
     */
    InventoryTransferVO getInventoryTransferById(Integer inventoryTransferId);

    /**
     * 保存库存转移单
     *
     * @param inventoryTransferPO
     * @return
     * @auhor hugo
     * @date 2020/7/15 16:53:15
     */
    int insertInventoryTransfer(InventoryTransferPO inventoryTransferPO);

    /**
     * 保存库存转移单详情信息
     *
     * @param inventoryTransferDetailPO
     * @return
     * @auhor hugo
     * @date 2020/7/15 16:53:15
     */
    int insertInventoryTransferDetail(InventoryTransferDetailPO inventoryTransferDetailPO);

    /**
     * 根据活动查询相关库存转移单信息
     *
     * @param actionId 活动ID
     * @return
     * <AUTHOR>
     * @date 2020/7/20 9:14:18
     */
    List<InventoryTransferDto> getInventoryTransferInfoByActionId(Integer actionId);

    /**
     * 更新库存转移单信息
     * @param inventoryTransferPO
     * @return
     */
    int updateInventoryTransfer(InventoryTransferPO inventoryTransferPO);

    /**
     * 更新库存转移单详情信息
     * @param inventoryTransferDetailPO
     * @return
     */
    int updateInventoryTransferDetail(InventoryTransferDetailPO inventoryTransferDetailPO);

    /**
     * @param inventoryTransferNo
     * @return
     * @describe 通过库存转移单单号查询信息
     * <AUTHOR>
     * @date 2020/7/13 9:45:22
     */
    InventoryTransferPO getInventoryTransferByNo(String inventoryTransferNo);
}
