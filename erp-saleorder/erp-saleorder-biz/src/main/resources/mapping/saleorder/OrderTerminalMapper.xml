<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.saleorder.dao.OrderTerminalMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.saleorder.domain.entity.OrderTerminalEntity">
    <id column="ORDER_TERMINAL_ID" jdbcType="INTEGER" property="orderTerminalId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="BUSINESS_ID" jdbcType="INTEGER" property="businessId" />
    <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType" />
    <result column="BUSINESS_NO" jdbcType="VARCHAR" property="businessNo" />
    <result column="TERMINAL_NAME" jdbcType="VARCHAR" property="terminalName" />
    <result column="DWH_TERMINAL_ID" jdbcType="VARCHAR" property="dwhTerminalId" />
    <result column="UNIFIED_SOCIAL_CREDIT_IDENTIFIER" jdbcType="VARCHAR" property="unifiedSocialCreditIdentifier" />
    <result column="ORGANIZATION_CODE" jdbcType="VARCHAR" property="organizationCode" />
    <result column="IS_DELETED" jdbcType="INTEGER" property="isDeleted" />
    <result column="PROVINCE_ID" jdbcType="INTEGER" property="provinceId" />
    <result column="CITY_ID" jdbcType="INTEGER" property="cityId" />
    <result column="AREA_ID" jdbcType="INTEGER" property="areaId" />
    <result column="PROVINCE_NAME" jdbcType="VARCHAR" property="provinceName" />
    <result column="CITY_NAME" jdbcType="VARCHAR" property="cityName" />
    <result column="AREA_NAME" jdbcType="VARCHAR" property="areaName" />
    <result column="TERMINAL_TRADER_NATURE" jdbcType="INTEGER" property="terminalTraderNature" />
    <result column="DETAIL_ADDRESS" jdbcType="VARCHAR" property="detailAddress" />
    <result column="NATURE_TYPE_NAME" jdbcType="VARCHAR" property="natureTypeName" />
    <result column="CONTRACT_USER" jdbcType="VARCHAR" property="contractUser" />
    <result column="CONTRACT_MOBILE" jdbcType="VARCHAR" property="contractMobile" />
    <result column="BEFORE_TERMINAL_NAME" jdbcType="VARCHAR" property="beforeTerminalName" />
  </resultMap>
  <sql id="Base_Column_List">
    ORDER_TERMINAL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    BUSINESS_ID, BUSINESS_TYPE, BUSINESS_NO, TERMINAL_NAME, DWH_TERMINAL_ID, UNIFIED_SOCIAL_CREDIT_IDENTIFIER, 
    ORGANIZATION_CODE, IS_DELETED, PROVINCE_ID, CITY_ID, AREA_ID, PROVINCE_NAME, CITY_NAME,
    AREA_NAME,
    TERMINAL_TRADER_NATURE, DETAIL_ADDRESS, NATURE_TYPE_NAME, CONTRACT_USER, CONTRACT_MOBILE, BEFORE_TERMINAL_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_ORDER_TERMINAL
    where ORDER_TERMINAL_ID = #{orderTerminalId,jdbcType=INTEGER}
  </select>
  <select id="getByBusinessIdAndBusinessType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_ORDER_TERMINAL
    where BUSINESS_ID = #{businessId,jdbcType=INTEGER}
    and BUSINESS_TYPE = #{businessType,jdbcType=INTEGER}
    and IS_DELETED = 0
    limit 1
  </select>
  <select id="getByBusinessIdNameAndBusinessType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_ORDER_TERMINAL
    where BUSINESS_ID = #{businessId,jdbcType=INTEGER}
    and BUSINESS_TYPE = #{businessType,jdbcType=INTEGER}
    AND TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR}
    and IS_DELETED = 0
    limit 1
  </select>

  <select id="findBySaleOrderId" resultType="com.vedeng.erp.saleorder.domain.entity.OrderTerminalEntity">
    select
    <include refid="Base_Column_List" />
    from T_ORDER_TERMINAL
    where BUSINESS_ID = #{businessId,jdbcType=INTEGER}
    and BUSINESS_TYPE = #{businessType,jdbcType=INTEGER}
    and IS_DELETED = 0
    ORDER BY ORDER_TERMINAL_ID ASC
  </select>



  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_ORDER_TERMINAL
    where ORDER_TERMINAL_ID = #{orderTerminalId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.saleorder.domain.entity.OrderTerminalEntity">
    insert into T_ORDER_TERMINAL (ADD_TIME, MOD_TIME, CREATOR,
                                  UPDATER, CREATOR_NAME, UPDATER_NAME,
                                  BUSINESS_ID, BUSINESS_TYPE, BUSINESS_NO,
                                  TERMINAL_NAME, DWH_TERMINAL_ID, UNIFIED_SOCIAL_CREDIT_IDENTIFIER,
                                  ORGANIZATION_CODE, IS_DELETED, PROVINCE_ID,
                                  CITY_ID, AREA_ID, PROVINCE_NAME,
                                  CITY_NAME, AREA_NAME,
    TERMINAL_TRADER_NATURE, DETAIL_ADDRESS, NATURE_TYPE_NAME, CONTRACT_USER, CONTRACT_MOBILE, BEFORE_TERMINAL_NAME)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
            #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR},
            #{businessId,jdbcType=INTEGER}, #{businessType,jdbcType=INTEGER}, #{businessNo,jdbcType=VARCHAR},
            #{terminalName,jdbcType=VARCHAR}, #{dwhTerminalId,jdbcType=VARCHAR}, #{unifiedSocialCreditIdentifier,jdbcType=VARCHAR},
            #{organizationCode,jdbcType=VARCHAR}, #{isDeleted,jdbcType=INTEGER}, #{provinceId,jdbcType=INTEGER},
            #{cityId,jdbcType=INTEGER}, #{areaId,jdbcType=INTEGER}, #{provinceName,jdbcType=VARCHAR},
            #{cityName,jdbcType=VARCHAR}, #{areaName,jdbcType=VARCHAR},
    #{terminalTraderNature,jdbcType=INTEGER}, #{detailAddress,jdbcType=VARCHAR}, #{natureTypeName,jdbcType=VARCHAR},
    #{contractUser,jdbcType=VARCHAR}, #{contractMobile,jdbcType=VARCHAR}, #{beforeTerminalName,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective"  keyColumn="ORDER_TERMINAL_ID" keyProperty="orderTerminalId"  parameterType="com.vedeng.erp.saleorder.domain.entity.OrderTerminalEntity" useGeneratedKeys="true">
    insert into T_ORDER_TERMINAL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="businessId != null">
        BUSINESS_ID,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="businessNo != null">
        BUSINESS_NO,
      </if>
      <if test="terminalName != null">
        TERMINAL_NAME,
      </if>
      <if test="dwhTerminalId != null">
        DWH_TERMINAL_ID,
      </if>
      <if test="unifiedSocialCreditIdentifier != null">
        UNIFIED_SOCIAL_CREDIT_IDENTIFIER,
      </if>
      <if test="organizationCode != null">
        ORGANIZATION_CODE,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
      <if test="provinceId != null">
        PROVINCE_ID,
      </if>
      <if test="cityId != null">
        CITY_ID,
      </if>
      <if test="areaId != null">
        AREA_ID,
      </if>
      <if test="provinceName != null">
        PROVINCE_NAME,
      </if>
      <if test="cityName != null">
        CITY_NAME,
      </if>
      <if test="areaName != null">
        AREA_NAME,
      </if>
      <if test="terminalTraderNature != null">
        TERMINAL_TRADER_NATURE,
      </if>
      <if test="detailAddress != null">
        DETAIL_ADDRESS,
      </if>
      <if test="natureTypeName != null">
        NATURE_TYPE_NAME,
      </if>
      <if test="contractUser != null">
        CONTRACT_USER,
      </if>
      <if test="contractMobile != null">
        CONTRACT_MOBILE,
      </if>
      <if test="beforeTerminalName != null">
        BEFORE_TERMINAL_NAME,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="businessNo != null">
        #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="terminalName != null">
        #{terminalName,jdbcType=VARCHAR},
      </if>
      <if test="dwhTerminalId != null">
        #{dwhTerminalId,jdbcType=VARCHAR},
      </if>
      <if test="unifiedSocialCreditIdentifier != null">
        #{unifiedSocialCreditIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="organizationCode != null">
        #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="provinceId != null">
        #{provinceId,jdbcType=INTEGER},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=INTEGER},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderNature != null">
        #{terminalTraderNature,jdbcType=INTEGER},
      </if>
      <if test="detailAddress != null">
        #{detailAddress,jdbcType=VARCHAR},
      </if>
      <if test="natureTypeName != null">
        #{natureTypeName,jdbcType=VARCHAR},
      </if>
      <if test="contractUser != null">
        #{contractUser,jdbcType=VARCHAR},
      </if>
      <if test="contractMobile != null">
        #{contractMobile,jdbcType=VARCHAR},
      </if>
      <if test="beforeTerminalName != null">
        #{beforeTerminalName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.saleorder.domain.entity.OrderTerminalEntity">
    update T_ORDER_TERMINAL
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        BUSINESS_ID = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="businessNo != null">
        BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="terminalName != null">
        TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
      </if>
      <if test="dwhTerminalId != null">
        DWH_TERMINAL_ID = #{dwhTerminalId,jdbcType=VARCHAR},
      </if>
      <if test="unifiedSocialCreditIdentifier != null">
        UNIFIED_SOCIAL_CREDIT_IDENTIFIER = #{unifiedSocialCreditIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="organizationCode != null">
        ORGANIZATION_CODE = #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="provinceId != null">
        PROVINCE_ID = #{provinceId,jdbcType=INTEGER},
      </if>
      <if test="cityId != null">
        CITY_ID = #{cityId,jdbcType=INTEGER},
      </if>
      <if test="areaId != null">
        AREA_ID = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="provinceName != null">
        PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        CITY_NAME = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="areaName != null">
        AREA_NAME = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderNature != null">
        TERMINAL_TRADER_NATURE = #{terminalTraderNature,jdbcType=INTEGER},
      </if>
      <if test="detailAddress != null">
        DETAIL_ADDRESS = #{detailAddress,jdbcType=VARCHAR},
      </if>
      <if test="natureTypeName != null">
        NATURE_TYPE_NAME = #{natureTypeName,jdbcType=VARCHAR},
      </if>
      <if test="contractUser != null">
        CONTRACT_USER = #{contractUser,jdbcType=VARCHAR},
      </if>
      <if test="contractMobile != null">
        CONTRACT_MOBILE = #{contractMobile,jdbcType=VARCHAR},
      </if>
      <if test="beforeTerminalName != null">
        BEFORE_TERMINAL_NAME = #{beforeTerminalName,jdbcType=VARCHAR},
      </if>
    </set>
    where ORDER_TERMINAL_ID = #{orderTerminalId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.saleorder.domain.entity.OrderTerminalEntity">
    update T_ORDER_TERMINAL
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
        CREATOR = #{creator,jdbcType=INTEGER},
        UPDATER = #{updater,jdbcType=INTEGER},
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
        BUSINESS_ID = #{businessId,jdbcType=INTEGER},
        BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
        BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
        TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
        DWH_TERMINAL_ID = #{dwhTerminalId,jdbcType=VARCHAR},
        UNIFIED_SOCIAL_CREDIT_IDENTIFIER = #{unifiedSocialCreditIdentifier,jdbcType=VARCHAR},
        ORGANIZATION_CODE = #{organizationCode,jdbcType=VARCHAR},
        IS_DELETED = #{isDeleted,jdbcType=INTEGER},
        PROVINCE_ID = #{provinceId,jdbcType=INTEGER},
        CITY_ID = #{cityId,jdbcType=INTEGER},
        AREA_ID = #{areaId,jdbcType=INTEGER},
        PROVINCE_NAME = #{provinceName,jdbcType=VARCHAR},
        CITY_NAME = #{cityName,jdbcType=VARCHAR},
        AREA_NAME = #{areaName,jdbcType=VARCHAR},
        TERMINAL_TRADER_NATURE = #{terminalTraderNature,jdbcType=INTEGER},
        DETAIL_ADDRESS= #{detailAddress,jdbcType=VARCHAR},
        NATURE_TYPE_NAME = #{natureTypeName,jdbcType=VARCHAR},
        CONTRACT_USER = #{contractUser,jdbcType=VARCHAR},
        CONTRACT_MOBILE = #{contractMobile,jdbcType=VARCHAR},
        BEFORE_TERMINAL_NAME = #{beforeTerminalName,jdbcType=VARCHAR}
    where ORDER_TERMINAL_ID = #{orderTerminalId,jdbcType=INTEGER}
  </update>
  <update id="logicalDelete">
    update T_ORDER_TERMINAL
    set IS_DELETED = 1
    where BUSINESS_ID = #{businessId,jdbcType=INTEGER}
      and BUSINESS_TYPE = #{businessType,jdbcType=INTEGER}
  </update>
</mapper>