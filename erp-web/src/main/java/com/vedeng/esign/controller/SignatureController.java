package com.vedeng.esign.controller;

import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultJSON;
import com.vedeng.esign.service.SignatureService;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2021/12/10 13:53
 **/
@Controller
@RequestMapping("/e/goods")
@Slf4j
public class SignatureController {
    @Autowired
    private SignatureService signatureService;

    @ResponseBody
    @RequestMapping(value = "/manufacturer/submitCheck")
    public ResultJSON submitCheckManufacturer(HttpServletRequest request, Manufacturer manufacturer) {
        try {
            User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            signatureService.checkManufacture(manufacturer, sessUser);

        } catch (Exception e) {
            log.error("提交生产企业电子签章错误：msg:",e);
            return ResultJSON.failed().message("提交生产企业电子签章故障,请联系开发!");
        }
        return ResultJSON.success().message("操作成功");
    }

    @ResponseBody
    @RequestMapping(value = "/firstengage/check")
    public ResultJSON checkFirstengage(HttpServletRequest request, FirstEngage firstEngage) {
        try {
            User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
            signatureService.checkFirstengage(firstEngage, sessUser);
        } catch (Exception e) {
            log.error("首营信息电子签章错误：",e);
            return ResultJSON.failed().message("首营信息电子签章故障,请联系开发");
        }
        return ResultJSON.success().message("操作成功");
    }
}
