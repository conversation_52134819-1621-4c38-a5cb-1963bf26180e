<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>B2B商机列表</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="b2bbussinesschance" datasource="erp-reportdatasource" fix_number="1" append_column_url="" append_row_url="" empty_show="-" fixednumber="2" firstcol="numbers">
  <div id="appendHead">
   <style>
a{color:green}
</style>
  </div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab">
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline ">
     <label class="layui-form-label">商机编号</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="BUSSINESS_CHANCE_NO" placeholder="" style="" alias="a" jdbctype="">
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">商机状态</label>
     <div class="layui-input-inline">
      <select class=" layui-input list-search-item " type="search" name="STATUS" placeholder="" style="" alias="a"
             data="[{&quot;V&quot;:&quot;未处理&quot;,&quot;K&quot;:&quot;0&quot;}
,{&quot;V&quot;:&quot;报价中&quot;,&quot;K&quot;:&quot;1&quot;}
,{&quot;V&quot;:&quot;已报价&quot;,&quot;K&quot;:&quot;2&quot;}
,{&quot;V&quot;:&quot;已订单&quot;,&quot;K&quot;:&quot;3&quot;}
,{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;4&quot;}
,{&quot;V&quot;:&quot;未分配&quot;,&quot;K&quot;:&quot;5&quot;}
,{&quot;V&quot;:&quot;处理中&quot;,&quot;K&quot;:&quot;6&quot;}
,{&quot;V&quot;:&quot;已成单&quot;,&quot;K&quot;:&quot;7&quot;}
]" datatype="JSON"jdbctype=""></select>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">商机类型</label>
     <div class="layui-input-inline">
      <select class=" layui-input list-search-item " type="search" name="TYPE" placeholder=""
              style="" alias="a" jdbctype="" data="SELECT SYS_OPTION_DEFINITION_ID K,TITLE V FROM T_SYS_OPTION_DEFINITION WHERE  PARENT_ID=390 AND STATUS=1" datatype="KVSQLCACHE" oper="">
      </select>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">客户性质</label>
     <div class="layui-input-inline">
      <select class=" layui-input list-search-item " type="search" name="CUSTOMER_NATURE" placeholder=""
              style="" alias="g" jdbctype=""  datatype="JSON" data='[{"V":"分销","K":"465"},{"V":"终端","K":"466"}]' oper="">
      </select>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">客户名称</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="客户名称" placeholder="" style="" alias="IF(a.TRADER_NAME IS NULL OR a.TRADER_NAME = '', a.CHECK_TRADER_NAME, a.TRADER_NAME)" jdbctype="" oper="like" >
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">询价产品</label>
     <div class="layui-input-inline">
         <object class=" layui-input list-search-item " alias="" type="union" name="CONTENT,PRODUCT_COMMENTS_SALE,PRODUCT_COMMENTS" placeholder="" style=""  jdbctype=""  oper="like" ></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">询价产品</label>
     <div class="layui-input-inline">
         <object class=" layui-input list-search-item " alias="" type="hidden" name="CONTENT" placeholder="" style=""  jdbctype=""  oper="like" ></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">产品备注(销售)</label>
     <div class="layui-input-inline">
         <object class=" layui-input list-search-item " alias="" type="hidden" name="PRODUCT_COMMENTS_SALE" placeholder="" style=""  jdbctype=""  oper="like" ></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">产品备注(总机)</label>
     <div class="layui-input-inline">
         <object class=" layui-input list-search-item " alias="" type="hidden" name="PRODUCT_COMMENTS" placeholder="" style=""  jdbctype=""  oper="like" ></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">手机号</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="手机号" placeholder="" style=""  jdbctype="" oper="like">
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">归属销售</label>
     <div class="layui-input-inline">
         <select class=" layui-input list-search-item " type="search" name="USER_ID" placeholder="" style="" alias="a" jdbctype="" data="select user_id K,username V from T_USER ORDER BY USERNAME" datatype="KVSQLCACHE" oper=""></select>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">报价单号</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="QUOTEORDER_NO" placeholder="" style="" alias="c" jdbctype=""   oper="">
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">分配时间</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="daterange" name="ASSIGN_TIME" placeholder="" style="" alias="from_unixtime(a.ASSIGN_TIME/1000 ,'%Y-%m-%d %h:%i:%s')" jdbctype="" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">部门</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="orgb2b" name="ORG_ID" placeholder="" style="" alias="a" jdbctype="" data="" datatype="" oper="IN"></object>
     </div>
    </div>
   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton">
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
       <th item_name="BUSSINESS_CHANCE_NO" name="BUSSINESS_CHANCE_NO" body="td-link" order="" url="/order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${BUSSINESS_CHANCE_ID}" opentype="PARENT" windowname="商机" datatype="" data style head="th">商机编号</th>
         <th item_name="商机状态" name="商机状态" body="td-text" order="" url="" opentype="" windowname=""   style head="th">商机状态</th>
         <th item_name="商机类型" name="商机类型" body="td-text" order="" url="" opentype="" windowname=""  style head="th">商机类型</th>
         <th item_name="渠道类型" name="渠道类型" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style head="th">渠道类型</th>
         <th item_name="询价产品" name="询价产品" body="td-text" order="" url="" opentype="" windowname=""  style="max-width:300px" head="th">询价产品</th>
         <th item_name="产品备注" name="产品备注" body="td-text" order="" url="" opentype="" windowname="" style="max-width:300px" head="th">产品备注</th>
         <th item_name="ORG_NAME" name="ORG_NAME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style head="th">部门</th>
         <th item_name="USER_ID" name="USER_ID" body="td-select" datatype="KVSQLCACHE" data="SELECT  USER_ID K,USERNAME V FROM T_USER  " order="" url="" opentype="" windowname=""   style head="th">归属销售</th>
         <th item_name="报价金额" name="报价金额" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style head="th" jdbctype="NUMBER">报价金额</th>
         <th item_name="客户名称" name="客户名称" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style head="th">客户名称</th>
         <th item_name="CUSTOMER_NATURE" name="CUSTOMER_NATURE" body="td-select" order="" url="" opentype="" windowname="" datatype="JSON" data='[{"V":"分销","K":"465"},{"V":"终端","K":"466"}]' style head="th">客户性质</th>
         <th item_name="手机号" name="手机号" body="td-text" order="" url="" opentype="" windowname=""   style head="th">联系方式</th>
      <th item_name="报价单号" name="报价单号" body="td-link" order="" url="/order/quote/getQuoteDetail.do?quoteorderId=${QUOTEORDER_ID}&viewType=2" opentype="PARENT" windowname="报价单" head="th">报价单号</th>
      <th item_name="订单号" name="订单号" body="td-link" order="" url="/orderstream/saleorder/detail.do?saleOrderId=${SALEORDER_ID}&scene=0" opentype="PARENT" windowname="订单号" datatype="" data="" style head="th">订单号</th>
      <th item_name="分配时间" name="分配时间" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style head="th">分配时间</th>
      <th item_name="初次查看时间" name="初次查看时间" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style head="th">初次查看时间</th>
      <th item_name="关闭原因" name="关闭原因" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style head="th">关闭原因</th>
     </tr>
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="ORDER BY BUSSINESS_CHANCE_ID DESC" groupby="">StringBuilder sql=new StringBuilder("
        SELECT a.BUSSINESS_CHANCE_ID,
       a.BUSSINESS_CHANCE_NO,
       b.TITLE                                                                             商机类型,
       d.TITLE                                                                             渠道类型,
       c.TOTAL_AMOUNT                                                                      报价金额,
       IF(a.TRADER_NAME IS NULL OR a.TRADER_NAME = '', a.CHECK_TRADER_NAME, a.TRADER_NAME) 客户名称,
       a.CHECK_TRADER_NAME,

       a.MOBILE                                                                            手机,
       a.CONTENT                                                                        询价产品,
       CONCAT(IF(a.PRODUCT_COMMENTS_SALE IS NULL, '', a.PRODUCT_COMMENTS_SALE), IF(a.COMMENTS IS NULL, '', a.COMMENTS),
              IF(a.PRODUCT_COMMENTS IS NULL, '', a.PRODUCT_COMMENTS))                      产品备注,
a.PRODUCT_COMMENTS_SALE,a.PRODUCT_COMMENTS,
          a.STATUS,a.TYPE,
       CASE
           WHEN a.`STATUS` = 0 THEN '未处理'
           WHEN a.`STATUS` = 1 THEN '报价中'
           WHEN a.`STATUS` = 2 THEN '已报价'
           WHEN a.`STATUS` = 3 THEN '已订单'
           WHEN a.`STATUS` = 4 THEN '已关闭'
           WHEN a.`STATUS` = 5 THEN '未分配'
           WHEN a.`STATUS` = 6 THEN '处理中'
           WHEN a.`STATUS` = 7 THEN '已成单' END AS                                           商机状态,
       c.QUOTEORDER_NO                                                                     报价单号,
          c.QUOTEORDER_ID,
       i.SALEORDER_NO                                                                      订单号,
          i.SALEORDER_ID,
       FROM_UNIXTIME(a.ASSIGN_TIME / 1000,'%Y-%m-%d %H:%i:%s')                                                 分配时间,
       FROM_UNIXTIME(a.FIRST_VIEW_TIME / 1000,'%Y-%m-%d %H:%i:%s')                                             初次查看时间,
       CONCAT(a.CLOSED_COMMENTS, j.TITLE)                                                  关闭原因,
       O.ORG_NAME,a.ORG_ID,
       a.USER_ID,g.CUSTOMER_NATURE
FROM `T_BUSSINESS_CHANCE` a
         LEFT JOIN T_SYS_OPTION_DEFINITION b ON a.TYPE = b.SYS_OPTION_DEFINITION_ID
         LEFT JOIN T_QUOTEORDER c ON c.BUSSINESS_CHANCE_ID = a.BUSSINESS_CHANCE_ID
         LEFT JOIN T_SYS_OPTION_DEFINITION d ON a.SOURCE = d.SYS_OPTION_DEFINITION_ID

         LEFT JOIN T_TRADER_CUSTOMER g ON g.TRADER_ID = a.TRADER_ID

         LEFT JOIN T_SALEORDER i ON i.QUOTEORDER_ID = c.QUOTEORDER_ID
         LEFT JOIN T_SYS_OPTION_DEFINITION j on a.STATUS_COMMENTS=j.SYS_OPTION_DEFINITION_ID
         LEFT JOIN T_ORGANIZATION O ON a.ORG_ID = O.ORG_ID where 1=1
         ");

       sql.append ( " and (1=2  " );

       if ( isNotBlank("EZ_SESSION_ORG_IDS_LIST_KEY","session")) {
           sql.append ( " or  a.ORG_ID  in (" );
           sql.append ( $$("EZ_SESSION_ORG_IDS_LIST_KEY") );
           sql.append (  ") " );
       }

       if ( isNotBlank("EZ_SESSION_MY_USER_KEY","session")) {
           sql.append ( " or a.USER_ID in (" );
           sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
           sql.append (  ") " );
       }

       if ( !isNotBlank("EZ_SESSION_MY_USER_KEY","session") &amp;&amp; !isNotBlank("EZ_SESSION_ORG_IDS_LIST_KEY","session")) {
          sql.append ( " or 1=1" );
       }
       sql.append (  ") " );

       list=search(sql );
       return list;</pre> count: <pre id="count" class="layui-code">select 10000 </pre></td>
     </tr> 
    </tbody> 
   </table> 
  </div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <div id="appendFoot">

  </div> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>