<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="订单助理绑定" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/css/order/buyorder/bind_orderassistan_sub.css?rnd=${resourceVersionKey}">
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/common/global.css">

<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>

<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/fonts/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
<div>
    <div class="erp-wrap" style="min-width: auto; padding-top:20px" >
        <div class="erp-block base-form search-wrap J-search-wrap">
            <div >
                <div class="layui-row" style="text-align:center">
                <div class="item-label" style="display: inline-block ">产品经理：</div>
                <div class="item-fields" style="display: inline-block">
                    <select class="J-select" id="productManager" name="productManager" style="min-width: 200px">
                        <option value="">请选择</option>
                        <c:forEach var="managerlist" items="${productManagerList}">
                            <option value="${managerlist.userId}">${managerlist.username}</option>
                        </c:forEach>
                    </select>
                </div>
                </div>
                <div class="layui-row" style="padding-top: 20px;text-align:center">
                <div class="item-label" style="display: inline-block">产品助理：</div>
                <div class="item-fields" style="display: inline-block">
                    <select class="J-select" id="productAssistant" name="productAssistant" style="min-width: 200px">
                        <option value="">请选择</option>
                        <c:forEach var="asslist" items="${productAssistantList}">
                            <option value="${asslist.userId}">${asslist.username}</option>
                        </c:forEach>
                    </select>
                </div>
                </div>
            </div >
            <div class="search-btns" style="margin-right: 300px;padding-top: 20px">
                <div class="btn btn-small btn-blue-bd " id='cancel' onclick="cancel()">取消</div>
                <div class="btn btn-small btn-blue-bd " id='save' onclick="save()">保存</div>
            </div>
        </div>
    </div>
</div>
<input type="hidden" value="${orderAssistantId}" id="orderAssistantId">
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/order/buyorder/bind_orderassistant_sub.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp" %>