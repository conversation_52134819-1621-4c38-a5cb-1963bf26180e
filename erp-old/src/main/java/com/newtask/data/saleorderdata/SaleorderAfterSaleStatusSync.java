package com.newtask.data.saleorderdata;

import com.newtask.data.AbstractDataSync;
import com.newtask.data.dao.SaleorderDataMapper;
import com.newtask.data.dto.AfterSalesStatusDto;
import com.newtask.data.dto.SaleoderDataDto;
import com.vedeng.finance.model.SaleorderData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/26 14:02
 * @describe
 */
@Component
public class SaleorderAfterSaleStatusSync extends AbstractDataSync {

    @Resource
    SaleorderDataMapper saleorderDataMapper;

    @Override
    public List<Integer> loadBizId(Long startTime, Long endTime) {
        List<Integer> saleorderAfterSalesStatusIdsList = saleorderDataMapper.getSaleorderAfterSalesStatusIdsList(startTime, endTime);

        return saleorderAfterSalesStatusIdsList;
    }

    @Override
    public List<Map<String, Object>> loadBizData(List<Integer> bizIds) {
        List<Map<String, Object>> afterSalesStatus = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(bizIds)) {
            List<AfterSalesStatusDto> afterSalesStatusList = saleorderDataMapper.findAfterSalesStatusBySaleorderId(bizIds);
            Map<String, List<AfterSalesStatusDto>> listMap = afterSalesStatusList.stream().collect(Collectors.groupingBy(AfterSalesStatusDto::getSaleorderId));
            listMap.forEach((key, value) -> {
                //一个订单有多个售后单
                //有一个进行中的代表进行中 否则有个完结的代表已完结 否则 有一个关闭的代表已关闭
                List<String> collect = value.stream().map(AfterSalesStatusDto::getAfterSalesStatus).collect(Collectors.toList());
                if (collect.contains("1")){
                    Map<String, Object> map = new HashMap<>();
                    map.put("afterSalesStatus","1");
                    map.put("saleorderId",key);
                    afterSalesStatus.add(map);
                    return;
                }
                if (collect.contains("2")){
                    Map<String, Object> map = new HashMap<>();
                    map.put("afterSalesStatus","2");
                    map.put("saleorderId",key);
                    afterSalesStatus.add(map);
                    return;
                }
                if (collect.contains("3")){
                    Map<String, Object> map = new HashMap<>();
                    map.put("afterSalesStatus","0");
                    map.put("saleorderId",key);
                    afterSalesStatus.add(map);
                    return;
                }
            });
        }

        return afterSalesStatus;
    }



    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        List<SaleoderDataDto> saleoderDataDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataList)) {
            long timeMillis = System.currentTimeMillis();
            dataList.forEach(item->{
                SaleoderDataDto saleoderDataDto = new SaleoderDataDto();
                Integer saleorderId = NumberUtils.toInt(item.get("saleorderId").toString());
                saleoderDataDto.setSaleorderId(saleorderId);
                saleoderDataDto.setAfterSalesStatus(NumberUtils.toLong(item.get("afterSalesStatus")+""));

                // 判断saleorderId是否存在
                makeExist(saleorderId, timeMillis);
                saleoderDataDtos.add(saleoderDataDto);
            });
            // 根据id修改afterSalesStatus
            saleorderDataMapper.updateAfterSalesStatusCheck(saleoderDataDtos, timeMillis);
        }
    }

    @Override
    public void makeExist(Integer id, Long nowTime) {
        Long count = saleorderDataMapper.getSaleorderById(id);
        if (count <= 0) {
            saleorderDataMapper.insertSaleOrder(id, nowTime);
        }
    }
}
