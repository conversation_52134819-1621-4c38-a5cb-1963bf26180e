package com.vedeng.crm.xxl;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.crm.task.service.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description 企微   商机督导	超时提醒	主管	当前时间=截止时间
 * 超时预警：商机编号+任务类型（含二级）任务已经超时，请您关注；
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/8/10
 */
@Component
@JobHandler(value="BussinessLeadDeadLineCheckJob")
@Slf4j
public class BussinessLeadDeadLineCheckJob  extends AbstractJobHandler {

    @Autowired
    private TaskService taskService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("=================商机督导 超时提醒 job开始====================");
        // 超时提醒
        taskService.alertBussinessChanceTask();
        XxlJobLogger.log("=================商机督导 超时提醒 job结束====================");
        return SUCCESS;
    }
}
