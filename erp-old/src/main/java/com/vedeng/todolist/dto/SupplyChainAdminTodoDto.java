package com.vedeng.todolist.dto;

import com.vedeng.authorization.model.Organization;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
public class SupplyChainAdminTodoDto {

    private Map<Organization, RiskSkuTodoDto> skuTodoMap = new LinkedHashMap();

    private Map<Organization, RiskTraderTodoDto> traderTodoMap = new LinkedHashMap();

    private PurchaseInfoTotoDto purchaseInfoTotoDto;

    private DailyManagementTodoDto dailyManagementTodoDto;

    private MessageTodoDto messageTodoDto;

    private List<RankingDto> rickSkuRankingDto;

    private List<RankingDto> purchaseRankingDto;

    private EarlyWarningGoodsTaskToDoDto earlyWarningGoodsTaskToDoDto;

    private EarlyWarningTicksTaskTodoDto earlyWarningTicksTaskTodoDto;

    /**
     * 备货任务统计
     */
    private PrepareStockTaskToDoDto prepareStockTaskToDoDto;

    private ReviewTaskToDoDto reviewTaskToDoDto;

    /**
     * 直发可确认收货待办项
     */
    private EnableReceiveToDoDto enableReceiveToDoDto;
}
