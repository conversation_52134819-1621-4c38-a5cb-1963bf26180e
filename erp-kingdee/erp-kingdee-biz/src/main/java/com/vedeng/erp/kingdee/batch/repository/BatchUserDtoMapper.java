package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchUserDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BatchUserDtoMapper {
    int deleteByPrimaryKey(Integer userId);

    int insert(BatchUserDto record);

    int insertOrUpdate(BatchUserDto record);

    int insertOrUpdateSelective(BatchUserDto record);

    int insertSelective(BatchUserDto record);

    BatchUserDto selectByPrimaryKey(Integer userId);

    int updateByPrimaryKeySelective(BatchUserDto record);

    int updateByPrimaryKey(BatchUserDto record);

    int updateBatch(List<BatchUserDto> list);

    int updateBatchSelective(List<BatchUserDto> list);

    int batchInsert(@Param("list") List<BatchUserDto> list);

    String findUsernameByUserId(@Param("userId")Integer userId);

    /**
     * <b>Description:</b><br> 获取客户/供应商归属人信息（orgId，userId）
     * @param traderId
     * @param traderType 1客户 2供应商
     * @return
     * @Note
     * <b>Author:</b> Jerry
     * <br><b>Date:</b> 2018年1月18日 上午9:14:22
     */
    BatchUserDto getUserInfoByTraderId(@Param("traderId")Integer traderId,@Param("traderType")Integer traderType);

}