// 其他联系方式
Vue.component('ui-other-contact-dialog', {
    template: `
        <crm-slide-dialog ref="slideDialog" title="添加联系方式" :refresh="true" @hidn="handlerCancel">
            <div class="other-contact-panel">
                <ui-tip type="info" styles="2">最多可添加 5 个联系方式</ui-tip>
                <div class="other-contact-list" @click.stop v-if="contactList.length">
                    <div 
                        class="other-contact-item" 
                        v-for="(item, index) in contactList" :key="index"
                        @click.stop="chooseThis(item)"
                    >
                        <div class="select">
                            <i v-if="item.value == selected.value" class="vd-ui_icon icon-radio3"></i>
                            <i v-else class="vd-ui_icon icon-radio1"></i>
                        </div>
                        <div class="label">{{ item.label }}</div>
                        <div class="input-">
                            <ui-input 
                                v-if="item.value == 3 && selected.value == 3" 
                                v-model="otherContactName"
                                maxlength="7"
                                placeholder="请输入联系方式名称，最多7个字"
                            ></ui-input>
                        </div>
                    </div>
                </div>
                <div class="slide-dialog-default-footer">
                    <div class="btn-cancel-flex">
                        <vd-ui-button @click="handlerCancel">取消</vd-ui-button>
                    </div>
                    <div class="btn-confirm-flex">
                        <vd-ui-button type="primary" @click="handlerConfirm">确定</vd-ui-button>
                    </div>
                </div>
            </div>
        </crm-slide-dialog>
    `,
    data() {
        return {
            contactList: [
                { label: '微信', value: 1 },
                { label: 'QQ', value: 2 },
                { label: '其他联系方式', value: 3 }
            ],
            selected: {},
            otherContactName: '', // 其他方式Label
        }
    },
    mounted() {
    },
    methods: {
        show() {
            this.$refs.slideDialog.show();
        },

        // 选择已建档客户
        async chooseThis (item) {
            this.selected = item;
        },

        // 确定
        handlerConfirm () {
            if (!this.selected && this.selected.value) {
                this.$message({
                    type: 'warn',
                    message: '请选择要添加的联系方式'
                })
                return;
            }
            else if (this.selected.value == 3 && !this.otherContactName) {
                this.$message({
                    type: 'warn',
                    message: '请输入联系方式名称'
                })
                return;
            }

            let emitLabel = '';
            if (this.selected.value == 3) {
                emitLabel = this.otherContactName;
            } else {
                emitLabel = this.selected.label;
            }

            this.$emit('change', emitLabel);

            setTimeout(()=> {
                this.handlerCancel();
            }, 30);
        },
        // 取消
        handlerCancel () {
            this.$refs.slideDialog.hide();

            this.selected = {},
            this.otherContactName = '';
        }
    }
})