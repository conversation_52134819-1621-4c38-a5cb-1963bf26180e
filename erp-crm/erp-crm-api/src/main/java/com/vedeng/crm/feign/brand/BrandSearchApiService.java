package com.vedeng.crm.feign.brand;

import com.vedeng.common.core.base.R;
import com.vedeng.common.feign.annotations.FeignApi;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/9/29
 */


@FeignApi(serverName = "erpServer")
public interface BrandSearchApiService {
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /firstengage/brand/brandNameForApi.do")
    R searchForApi(@RequestBody String brandName);
}
