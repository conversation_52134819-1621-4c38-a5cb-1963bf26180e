package com.vedeng.api.standard.processor;

import com.vedeng.api.standard.core.ApiRequest;
import com.vedeng.api.standard.core.ServiceAdapter;
import com.vedeng.api.standard.factory.ServiceAdapterFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 请求处理器实现类
 * 提供标准的请求预处理功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
@Component
public class RequestProcessorImpl implements RequestProcessor {

    private static final Logger logger = LoggerFactory.getLogger(RequestProcessorImpl.class);

    @Autowired
    private ServiceAdapterFactory serviceAdapterFactory;
    
    @Override
    public void preProcess(ApiRequest apiRequest) {
        // 1. 验证请求参数
        validateRequest(apiRequest);
        
        // 2. 记录请求日志
        logRequest(apiRequest);
        
        // 3. 可以在这里添加其他预处理逻辑
        // 例如：请求限流、参数转换、权限预检等
    }
    
    @Override
    public Map<String, Object> extractParametersFromRequest(HttpServletRequest httpRequest) {
        Map<String, Object> parameters = new HashMap<>();
        
        // 提取请求参数
        Enumeration<String> parameterNames = httpRequest.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String paramName = parameterNames.nextElement();
            String[] paramValues = httpRequest.getParameterValues(paramName);
            
            if (paramValues != null) {
                if (paramValues.length == 1) {
                    parameters.put(paramName, paramValues[0]);
                } else {
                    parameters.put(paramName, paramValues);
                }
            }
        }
        
        return parameters;
    }
    
    @Override
    public Map<String, String> extractHeadersFromRequest(HttpServletRequest httpRequest) {
        Map<String, String> headers = new HashMap<>();
        
        // 提取请求头
        Enumeration<String> headerNames = httpRequest.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = httpRequest.getHeader(headerName);
            headers.put(headerName, headerValue);
        }
        
        return headers;
    }
    
    @Override
    public void validateRequest(ApiRequest apiRequest) {
        if (apiRequest == null) {
            throw new IllegalArgumentException("API请求对象不能为空");
        }

        // 验证模块名称
        if (apiRequest.getModule() == null || apiRequest.getModule().trim().isEmpty()) {
            throw new IllegalArgumentException("模块名称不能为空");
        }

        // 验证操作名称
        if (apiRequest.getAction() == null || apiRequest.getAction().trim().isEmpty()) {
            throw new IllegalArgumentException("操作名称不能为空");
        }

        // 检查是否为免认证操作
        boolean isNoAuthAction = isNoAuthAction(apiRequest.getModule(), apiRequest.getAction());

        // 对于需要认证的操作，验证用户信息
        if (!isNoAuthAction && apiRequest.getCurrentUser() == null) {
            throw new IllegalArgumentException("当前用户不能为空");
        }

        // 对于免认证操作，使用更宽松的验证逻辑
        if (!isNoAuthAction && !apiRequest.isValid()) {
            throw new IllegalArgumentException("API请求对象无效: " + apiRequest.toString());
        } else if (isNoAuthAction) {
            // 免认证操作只验证基本字段，不验证用户信息
            if (apiRequest.getModule() == null || apiRequest.getModule().trim().isEmpty() ||
                apiRequest.getAction() == null || apiRequest.getAction().trim().isEmpty()) {
                throw new IllegalArgumentException("API请求对象无效: " + apiRequest.toString());
            }
        }

        // 可以在这里添加更多的验证逻辑
        validateModuleSpecificRules(apiRequest);
    }
    
    @Override
    public void logRequest(ApiRequest apiRequest) {
        try {
            logger.info("API请求详情: requestId={}, module={}, action={}, user={}, clientIp={}", 
                apiRequest.getRequestId(),
                apiRequest.getModule(),
                apiRequest.getAction(),
                apiRequest.getCurrentUser() != null ? apiRequest.getCurrentUser().getUsername() : "unknown",
                apiRequest.getClientIp());
            
            // 记录请求数据大小（不记录具体内容，避免日志过大）
            if (apiRequest.getData() != null) {
                logger.debug("请求数据大小: {} 个参数", apiRequest.getData().size());
            }
            
        } catch (Exception e) {
            logger.warn("记录请求日志失败", e);
        }
    }

    /**
     * 检查指定的模块和操作是否为免认证操作
     *
     * @param module 模块名称
     * @param action 操作名称
     * @return true表示免认证操作，false表示需要认证
     */
    private boolean isNoAuthAction(String module, String action) {
        try {
            ServiceAdapter serviceAdapter = serviceAdapterFactory.getServiceAdapter(module);
            if (serviceAdapter != null) {
                String[] noAuthActions = serviceAdapter.getNoAuthActions();
                boolean isNoAuth = Arrays.asList(noAuthActions).contains(action);

                logger.debug("免认证检查: module={}, action={}, noAuthActions={}, isNoAuth={}",
                    module, action, Arrays.toString(noAuthActions), isNoAuth);

                return isNoAuth;
            }
        } catch (Exception e) {
            logger.warn("检查免认证操作时发生异常: module={}, action={}", module, action, e);
        }
        return false; // 默认需要认证（安全优先）
    }

    /**
     * 验证模块特定的规则
     * 子类可以重写此方法来实现特定模块的验证逻辑
     *
     * @param apiRequest API请求对象
     */
    protected void validateModuleSpecificRules(ApiRequest apiRequest) {
        String module = apiRequest.getModule().toLowerCase();
        
        switch (module) {
            case "buyorder":
                validateBuyorderRequest(apiRequest);
                break;
            case "saleorder":
                validateSaleorderRequest(apiRequest);
                break;
            case "goods":
                validateGoodsRequest(apiRequest);
                break;
            default:
                // 默认验证逻辑
                break;
        }
    }
    
    /**
     * 验证采购单请求
     */
    private void validateBuyorderRequest(ApiRequest apiRequest) {
        String action = apiRequest.getAction().toLowerCase();
        Map<String, Object> data = apiRequest.getData();
        
        if ("create".equals(action)) {
            // 验证创建采购单的必填参数
            if (data == null || data.isEmpty()) {
                throw new IllegalArgumentException("创建采购单时请求数据不能为空");
            }
            
            // 可以添加更多具体的验证逻辑
        }
    }
    
    /**
     * 验证销售单请求
     */
    private void validateSaleorderRequest(ApiRequest apiRequest) {
        // 销售单特定的验证逻辑
        // 可以根据需要实现
    }
    
    /**
     * 验证商品请求
     */
    private void validateGoodsRequest(ApiRequest apiRequest) {
        // 商品特定的验证逻辑
        // 可以根据需要实现
    }
}
