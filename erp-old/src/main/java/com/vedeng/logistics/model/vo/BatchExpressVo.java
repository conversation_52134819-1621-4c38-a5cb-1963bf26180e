package com.vedeng.logistics.model.vo;

import lombok.Data;

import java.util.List;

@Data
public class BatchExpressVo {
    //订货号
    private String sku;

    //产品名称
    private String goodsName;

    //品牌
    private String brand;

    //型号
    private String model;

    //实际签收数量
    private Integer num;

    //单位
    private String unitName;

    //物流单号
    private String logisticsOrderNo;

    //方便拼接物流单号的属性
    private List<String> logisticsOrderNoUse;
}
