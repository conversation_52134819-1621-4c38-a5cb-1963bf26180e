<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.system.dao.ListConfigMapper" >
    <insert id="saveListConfig">
        INSERT INTO T_LIST_CONFIG(USER_ID,LIST_NAME,COLUMN_LIST,SEARCH_LIST) VALUE (
            #{listConfig.userId},#{listConfig.listName},#{listConfig.columnList},#{listConfig.searchList}
            )
    </insert>
    <update id="updateListConfigByUserAndListName">
        UPDATE T_LIST_CONFIG SET SEARCH_LIST = #{listConfig.searchList}, COLUMN_LIST = #{listConfig.columnList} WHERE
        USER_ID = #{listConfig.userId} AND LIST_NAME = #{listConfig.listName}
    </update>
    <delete id="deleteListConfigByUserAndListName">
        DELETE FROM T_LIST_CONFIG WHERE USER_ID = #{userId} AND LIST_NAME = #{listName}
    </delete>

    <select id="getListConfigByUserAndListName" resultType="com.vedeng.system.model.ListConfig">
        SELECT * FROM T_LIST_CONFIG WHERE USER_ID = #{userId} AND LIST_NAME = #{listName}
    </select>
</mapper>