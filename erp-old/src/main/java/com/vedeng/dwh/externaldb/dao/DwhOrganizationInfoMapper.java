package com.vedeng.dwh.externaldb.dao;

import com.vedeng.dwh.externaldb.dao.generate.DwdErpOrganizationNjDfMapper;
import com.vedeng.dwh.model.dto.DwhErpOrganizationDto;
import com.vedeng.dwh.model.dto.DwhErpSubDeptDto;
import com.vedeng.dwh.model.dto.DwhQueryParamsDto;

import java.util.List;

public interface DwhOrganizationInfoMapper extends DwdErpOrganizationNjDfMapper {

    /**
     * 获取组织架构基本信息.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/4 4:50 下午.
     * @author: Tomcat.Hui.
     * @param params: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhErpOrganizationDto>.
     * @throw: .
     */
    List<DwhErpOrganizationDto> getOrgBaseInfo(DwhQueryParamsDto params);

    /**
     * 获取指定时间所有部门快照信息.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/2 5:31 下午.
     * @author: Tomcat.Hui.
     * @param params: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhErpOrganizationDto>.
     * @throw: .
     */
    List<DwhErpOrganizationDto> getAllErpDepartments(DwhQueryParamsDto params);


    /**
     * 获取所有直接下级部门&归属员工.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/3 11:05 上午.
     * @author: Tomcat.Hui.
     * @param params: .
     * @return: java.util.List<com.vedeng.dwh.model.dto.DwhErpSubDept>.
     * @throw: .
     */
    List<DwhErpSubDeptDto> getSubDeptsAndUsers(DwhQueryParamsDto params);
}