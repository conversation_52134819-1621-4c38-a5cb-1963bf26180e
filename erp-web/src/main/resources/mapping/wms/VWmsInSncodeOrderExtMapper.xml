<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wms.dao.VWmsInSncodeOrderExtMapper">
    <resultMap id="BaseResultMap" type="com.wms.model.ddi.VWmsInSncodeOrderExtDto">
        <id column="IN_SNCODE_ORDER_ID" jdbcType="INTEGER" property="inSncodeOrderId"/>
        <result column="ORDER_ID" jdbcType="INTEGER" property="orderId"/>
        <result column="ORDER_GOODS_ID" jdbcType="INTEGER" property="orderGoodsId"/>
        <result column="OPERATE_TYPE" jdbcType="TINYINT" property="operateType"/>
        <result column="WMS_ORDER_NO" jdbcType="VARCHAR" property="wmsOrderNo"/>
        <result column="SKU" jdbcType="VARCHAR" property="sku"/>
        <result column="SKU_ID" jdbcType="INTEGER" property="skuId"/>
        <result column="SN_CODE" jdbcType="VARCHAR" property="snCode"/>
        <result column="SERIAL_NO" jdbcType="VARCHAR" property="serialNo"/>
        <result column="IN_TIME" jdbcType="TIMESTAMP" property="inTime"/>
        <result column="PRODUCT_DATE" jdbcType="TIMESTAMP" property="productDate"/>
        <result column="EXPIRATION_DATE" jdbcType="TIMESTAMP" property="expirationDate"/>
        <result column="BATCH_NUMBER" jdbcType="VARCHAR" property="batchNumber"/>
        <result column="VEDENG_BATCH_NUMER" jdbcType="VARCHAR" property="vedengBatchNumer"/>
        <result column="COMMENTS" jdbcType="VARCHAR" property="comments"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime"/>
        <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
    </resultMap>
    <sql id="Base_Column_List">
    IN_SNCODE_ORDER_ID, ORDER_ID, ORDER_GOODS_ID, OPERATE_TYPE, WMS_ORDER_NO, SKU, SKU_ID, 
    SN_CODE, SERIAL_NO, IN_TIME, PRODUCT_DATE, EXPIRATION_DATE, BATCH_NUMBER, VEDENG_BATCH_NUMER, 
    COMMENTS, ADD_TIME, MODE_TIME, IS_DELETE, CREATOR, UPDATER
  </sql>


    <select id="getAlltodayOrder"  resultMap="BaseResultMap">
        SELECT *
        FROM V_WMS_IN_SNCODE_ORDER
        WHERE
        IN_TIME <![CDATA[>=]]> #{startTime,jdbcType = TIMESTAMP}
        AND IN_TIME <![CDATA[<=]]> #{endTime,jdbcType = TIMESTAMP}
        AND SKU IN
        <foreach collection="skuList" item="sku" index="index" separator="," open="(" close=")">
            #{sku,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getSingleOrder" resultType="com.wms.model.ddi.DdiBuyorderExtDto">
        SELECT
        "贝登" AS distributor,
        <if test="operateType == 0">
            bg.GE_CONTRACT_NO AS quoteId,
            bg.GE_SALE_CONTRACT_NO AS saleorderNo,
            bg.ARRIVAL_NUM AS num,
            bg.UNIT_NAME AS unit,
            b.TRADER_NAME AS supplierName,
            b.TRADER_ID AS supplierId,
            b.BUYORDER_NO AS inOrderNo,
        </if>
        <if test="operateType == 1">
            coalesce(bg.GE_CONTRACT_NO,bg2.GE_CONTRACT_NO) AS quoteId,
            coalesce(bg.GE_SALE_CONTRACT_NO,bg2.GE_SALE_CONTRACT_NO) AS saleorderNo,
            asg.ARRIVAL_NUM AS num,
            coalesce(bg.UNIT_NAME,bg2.UNIT_NAME) AS unit,
            coalesce(b.TRADER_NAME,b2.TRADER_NAME) AS supplierName,
            coalesce(b.TRADER_ID,b2.TRADER_ID) AS supplierId,
            ass.AFTER_SALES_NO AS inOrderNo,
        </if>

        <if test="operateType == 2">
            bg.GE_CONTRACT_NO AS quoteId,
            bg.GE_SALE_CONTRACT_NO AS saleorderNo,
            asg.ARRIVAL_NUM AS num,
            bg.UNIT_NAME AS unit,
            b.TRADER_NAME AS supplierName,
            b.TRADER_ID AS supplierId,
            ass.AFTER_SALES_NO AS inOrderNo,
        </if>
        <if test="operateType == 3">
            coalesce(bg.GE_CONTRACT_NO,bg2.GE_CONTRACT_NO) AS quoteId,
            coalesce(bg.GE_SALE_CONTRACT_NO,bg2.GE_SALE_CONTRACT_NO) AS saleorderNo,
            wiog.ARRIVAL_NUM AS num,
            coalesce(bg.UNIT_NAME,bg2.UNIT_NAME) AS unit,
            coalesce(b.TRADER_NAME,b2.TRADER_NAME) AS supplierName,
            coalesce(b.TRADER_ID,b2.TRADER_ID) AS supplierId,
            wio.ORDER_NO AS inOrderNo,
        </if>
        <if test="operateType == 4">
            coalesce(bg.GE_CONTRACT_NO,bg2.GE_CONTRACT_NO) AS quoteId,
            coalesce(bg.GE_SALE_CONTRACT_NO,bg2.GE_SALE_CONTRACT_NO) AS saleorderNo,
            woog.already_input_num AS num,
            coalesce(bg.UNIT_NAME,bg2.UNIT_NAME) AS unit,
            coalesce(b.TRADER_NAME,b2.TRADER_NAME) AS supplierName,
            coalesce(b.TRADER_ID,b2.TRADER_ID) AS supplierId,
            woo.ORDER_NO AS inOrderNo,
        </if>
        <if test="operateType == 5">

        </if>
        a.IN_TIME AS storageTime,
        sod.title AS goodsType,
        g.GOODS_NAME AS goodsName,
        g.MODEL AS goodsModel,
        g.REGISTRATION_NUMBER AS registrationNumber,
        a.SN_CODE AS serialNumber,
        a.EXPIRATION_DATE AS effectiveDays,
        pc.PRODUCT_COMPANY_CHINESE_NAME AS manufacturer,
        (CASE a.OPERATE_TYPE
        WHEN 0 THEN '采购入库'
        WHEN 1 THEN '销售售后入库'
        WHEN 2 THEN '采购售后入库'
        WHEN 3 THEN '盘盈入库'
        WHEN 4 THEN '借贷归还'
        WHEN 5 THEN '库存初始化入库'
        ELSE '其他' END ) AS purchaseType

        FROM
        V_WMS_IN_SNCODE_ORDER a
        LEFT JOIN V_CORE_SKU sku ON a.SKU_ID = sku.SKU_ID
        LEFT JOIN T_GOODS g ON a.SKU = g.SKU

        <if test="operateType == 0">
            LEFT JOIN T_BUYORDER b ON a.ORDER_ID = b.BUYORDER_ID
            LEFT JOIN T_BUYORDER_GOODS bg ON a.ORDER_GOODS_ID = bg.BUYORDER_GOODS_ID
        </if>

        <if test="operateType == 1">
            LEFT JOIN T_AFTER_SALES_GOODS asg ON a.ORDER_GOODS_ID = asg.AFTER_SALES_GOODS_ID
            LEFT JOIN T_AFTER_SALES ass ON asg.AFTER_SALES_ID = ass.AFTER_SALES_ID
            LEFT JOIN T_SALEORDER_GOODS sg ON asg.ORDER_DETAIL_ID = sg.SALEORDER_GOODS_ID

            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso ON wiso.SN_CODE = a.SN_CODE
            AND wiso.OPERATE_TYPE = 0
            AND wiso.SN_CODE != ''
            LEFT JOIN T_BUYORDER_GOODS bg ON wiso.ORDER_GOODS_ID = bg.BUYORDER_GOODS_ID
            LEFT JOIN T_BUYORDER b ON bg.BUYORDER_ID = b.BUYORDER_ID
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso2 ON wiso2.SN_CODE = a.SN_CODE
            AND wiso2.SN_CODE != ''
            AND wiso.OPERATE_TYPE = 2 AND wiso.ORDER_GOODS_ID is null
            LEFT JOIN T_AFTER_SALES_GOODS asg2 ON wiso2.ORDER_GOODS_ID = asg2.AFTER_SALES_GOODS_ID
            AND wiso.ORDER_GOODS_ID is null
            LEFT JOIN T_BUYORDER_GOODS bg2 ON asg2.ORDER_DETAIL_ID = bg2.BUYORDER_GOODS_ID
            AND wiso.ORDER_GOODS_ID is null
            LEFT JOIN T_BUYORDER b2 ON bg2.BUYORDER_ID = b.BUYORDER_ID
            AND wiso.ORDER_GOODS_ID is null
        </if>
        <if test="operateType == 2">
            LEFT JOIN T_AFTER_SALES_GOODS asg ON a.ORDER_GOODS_ID = asg.AFTER_SALES_GOODS_ID
            LEFT JOIN T_AFTER_SALES ass ON asg.AFTER_SALES_ID = ass.AFTER_SALES_ID
            LEFT JOIN T_BUYORDER_GOODS bg ON asg.ORDER_DETAIL_ID = bg.BUYORDER_GOODS_ID
            LEFT JOIN T_BUYORDER b ON bg.BUYORDER_ID = b.BUYORDER_ID
        </if>

        <if test="operateType == 3">
            LEFT JOIN T_WMS_INPUT_ORDER_GOODS wiog ON a.ORDER_GOODS_ID = wiog.WMS_INPUT_ORDER_GOODS_ID
            LEFT JOIN T_WMS_INPUT_ORDER wio ON wiog.WMS_INPUT_ORDER_ID = wio.WMS_INPUT_ORDER_ID
            AND wio.IS_DELETE = 0 AND wio.ORDER_TYPE = 1
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso ON wiso.SN_CODE = a.SN_CODE
            AND wiso.SN_CODE != ''
            AND wiso.OPERATE_TYPE = 0
            LEFT JOIN T_BUYORDER_GOODS bg ON wiso.ORDER_GOODS_ID = bg.BUYORDER_GOODS_ID
            LEFT JOIN T_BUYORDER b ON bg.BUYORDER_ID = b.BUYORDER_ID
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso2 ON wiso2.SN_CODE = a.SN_CODE
            AND wiso2.SN_CODE !=''
            AND wiso.OPERATE_TYPE = 2 AND wiso.ORDER_GOODS_ID is null
            LEFT JOIN T_AFTER_SALES_GOODS asg2 ON wiso2.ORDER_GOODS_ID = asg2.AFTER_SALES_GOODS_ID
            AND wiso.ORDER_GOODS_ID is null
            LEFT JOIN T_BUYORDER_GOODS bg2 ON asg2.ORDER_DETAIL_ID = bg2.BUYORDER_GOODS_ID
            AND wiso.ORDER_GOODS_ID is null
            LEFT JOIN T_BUYORDER b2 ON bg2.BUYORDER_ID = b.BUYORDER_ID
            AND wiso.ORDER_GOODS_ID is null
        </if>

        <if test="operateType == 4">
            LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS woog ON a.ORDER_GOODS_ID = woog.id
            LEFT JOIN T_WMS_OUTPUT_ORDER woo ON woog.wms_output_order_id = woo.id

            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso ON wiso.SN_CODE = a.SN_CODE
            AND wiso.SN_CODE != ''
            AND wiso.OPERATE_TYPE = 0
            LEFT JOIN T_BUYORDER_GOODS bg ON wiso.ORDER_GOODS_ID = bg.BUYORDER_GOODS_ID
            LEFT JOIN T_BUYORDER b ON bg.BUYORDER_ID = b.BUYORDER_ID
            LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso2 ON wiso2.SN_CODE = a.SN_CODE
            AND wiso2.SN_CODE != ''
            AND wiso.OPERATE_TYPE = 2 AND wiso.ORDER_GOODS_ID is null
            LEFT JOIN T_AFTER_SALES_GOODS asg2 ON wiso2.ORDER_GOODS_ID = asg2.AFTER_SALES_GOODS_ID
            AND wiso.ORDER_GOODS_ID is null
            LEFT JOIN T_BUYORDER_GOODS bg2 ON asg2.ORDER_DETAIL_ID = bg2.BUYORDER_GOODS_ID
            AND wiso.ORDER_GOODS_ID is null
            LEFT JOIN T_BUYORDER b2 ON bg2.BUYORDER_ID = b.BUYORDER_ID
            AND wiso.ORDER_GOODS_ID is null
        </if>
        LEFT JOIN T_SYS_OPTION_DEFINITION sod ON g.GOODS_TYPE = sod.SYS_OPTION_DEFINITION_ID
        LEFT JOIN V_CORE_SPU spu ON sku.SPU_ID = spu.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE fe ON spu.FIRST_ENGAGE_ID = fe.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER rn ON fe.REGISTRATION_NUMBER_ID = rn.REGISTRATION_NUMBER_ID
        LEFT JOIN T_PRODUCT_COMPANY pc ON rn.PRODUCT_COMPANY_ID = pc.PRODUCT_COMPANY_ID
        WHERE
        a.IN_SNCODE_ORDER_ID = #{inSncodeOrderId,jdbcType = INTEGER}
    </select>
</mapper>