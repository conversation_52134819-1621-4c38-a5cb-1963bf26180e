package com.vedeng.erp.kingdee.batch.handle;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.dto.AfterSaleGoodsIdQueryReqDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 发票和出入库关系绑定处理类
 * @date 2023/1/31 15:11
 */
@Service
@Slf4j
public class InvoiceWarehouseOutInHandle {

    @Autowired
    private BatchRInvoiceDetailJOperateLogDtoMapper batchRInvoiceDetailJOperateLogDtoMapper;
    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;
    @Autowired
    private BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;
    @Autowired
    private BatchRInvoiceDetailJOperateLogDtoMapper batchInvoiceDetailOperateLogDtoMapper;
    @Autowired
    private BatchBuyorderGoodsDtoMapper batchBuyorderGoodsDtoMapper;

    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;

    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;

    @Autowired
    private BatchRInvoiceJInvoiceDtoMapper batchRInvoiceJInvoiceDtoMapper;

    @Autowired
    private BuyOrderVirtualInvoiceHandle buyOrderVirtualInvoiceHandle;

    /**
     * 绑定销售退货入库和红票关系
     *
     * @param batchInvoiceDtoList 需要绑定的发票
     * @return 是否成功
     */
    public void bindSaleOrderRedInvoiceAndWarehouseInRelation(List<BatchInvoiceDto> batchInvoiceDtoList) {
        log.info("发票保存关联入库记录:{}", JSON.toJSONString(batchInvoiceDtoList));
        if (CollUtil.isEmpty(batchInvoiceDtoList)) {
            return;
        }
        // 实物商品明细
        for (BatchInvoiceDto invoiceDto : batchInvoiceDtoList) {
            List<BatchInvoiceDetailDto> saleOrderInvoiceDetailList = batchInvoiceDetailDtoMapper.getSaleOrderInvoiceDetailList(invoiceDto.getInvoiceId())
                    .stream().filter(detailDto -> !Convert.toBool(detailDto.getIsVirtureSku(), false)).collect(Collectors.toList());
            // 获取售后单
            BatchAfterSalesDto afterSales = batchAfterSalesDtoMapper.findByAfterSalesId(invoiceDto.getAfterSalesId());
            if(afterSales==null){
                log.error("发票中售后单无法查询:{}",invoiceDto.getAfterSalesId());
                continue;
            }
            for (BatchInvoiceDetailDto detailDto : saleOrderInvoiceDetailList) {
                // 根据发票中的销售id获取售后明细id
                Integer afterGoodsId = batchRInvoiceDetailJOperateLogDtoMapper.getAfterGoodsIdByCondition(detailDto.getDetailgoodsId(), invoiceDto.getAfterSalesId());
                // 根据售后明细id和关联表查询出，还可以剩余关联的对象信息
                List<BatchRInvoiceDetailJOperateLogDto> realWarehouse = batchRInvoiceDetailJOperateLogDtoMapper
                        .getValidWarehousingLogByRelatedId(afterGoodsId, afterSales.getAfterSalesNo(), 5, 0);
                // 剩余虚拟出库单
                List<BatchRInvoiceDetailJOperateLogDto> surplusWarehouse = batchRInvoiceDetailJOperateLogDtoMapper
                        .getValidWarehousingLogByRelatedId(detailDto.getDetailgoodsId(), afterSales.getAfterSalesNo(), 5, 1);
                List<BatchRInvoiceDetailJOperateLogDto> surplusCanRelevanceInfoList = CollUtil.unionAll(realWarehouse, surplusWarehouse);
                this.relationInvoiceDetailOperateLog(surplusCanRelevanceInfoList, detailDto, 5);
            }
        }
    }

    /**
     * 绑定销售出库和蓝票关系
     *
     * @param batchInvoiceDtoList 需要绑定的发票
     */
    public void bindSaleOrderBlueInvoiceAndWarehouseOutRelation(List<BatchInvoiceDto> batchInvoiceDtoList) {
        log.info("销售蓝票关联出库记录开始:{}", JSON.toJSONString(batchInvoiceDtoList));
        if (CollUtil.isEmpty(batchInvoiceDtoList)) {
            return;
        }
        for (BatchInvoiceDto invoiceDto : batchInvoiceDtoList) {
            if (StrUtil.isBlank(invoiceDto.getInvoiceNo()) || StrUtil.isBlank(invoiceDto.getInvoiceCode())) {
                log.warn("当前蓝票无发票号或者发票代码{}", JSON.toJSONString(invoiceDto));
                continue;
            }

            // 排除蓝字作废
            List<BatchInvoiceDto> blueDisEnableByInvoiceCodeAndInvoiceNo = batchInvoiceDtoMapper.findBlueDisEnableByInvoiceCodeAndInvoiceNo(invoiceDto.getInvoiceCode(), invoiceDto.getInvoiceNo(), 505);
            if (CollUtil.isNotEmpty(blueDisEnableByInvoiceCodeAndInvoiceNo)) {
                log.info("当前蓝票存在蓝字作废{}", JSON.toJSONString(invoiceDto));
                continue;
            }

            // 查询当前销售单的所有erp出库单
            List<BatchWarehouseGoodsOutInDto> warehouseGoodsOutInDtoList = batchWarehouseGoodsOutInDtoMapper.getByRelateNo(invoiceDto.getOrderNo(), 2);
            // 查询当前发票的所有实物商品明细行
            List<BatchInvoiceDetailDto> invoiceDetailList = batchInvoiceDetailDtoMapper.getSaleOrderInvoiceDetailList(invoiceDto.getInvoiceId())
                    .stream().filter(invoiceDetailDto -> invoiceDetailDto.getIsVirtureSku() == null || invoiceDetailDto.getIsVirtureSku() == 0).collect(Collectors.toList());
            for (BatchInvoiceDetailDto detailDto : invoiceDetailList) {
                // 已经绑定的数量
                BigDecimal detailOccupyNum = batchInvoiceDetailOperateLogDtoMapper.getInvoiceDetailOccupyNum(detailDto.getInvoiceDetailId());
                // 还需要分摊的发票明细数量
                BigDecimal needRecordNum = detailDto.getNum().abs().subtract(detailOccupyNum);
                // 只有当 发票明细中的数量减去 已经绑定的数量后，依然大于0，才需要继续绑定
                if (BigDecimal.ZERO.compareTo(needRecordNum) < 0) {
                    // 根据saleorderGoodsId查询出invoice detail里面商品的录票信息对应的 出库信息
                    List<BatchWarehouseGoodsOutInItemDto> collect = warehouseGoodsOutInDtoList.stream().map(outInDto -> outInDto.getBatchWarehouseGoodsOutInItemDtos()
                            .stream().filter(item -> item.getRelatedId().equals(detailDto.getDetailgoodsId())).map(batchWarehouseGoodsOutInItemDto -> {
                                BatchWarehouseGoodsOutInItemDto temp = new BatchWarehouseGoodsOutInItemDto();
                                temp.setOutInNo(outInDto.getOutInNo());
                                temp.setNum(batchWarehouseGoodsOutInItemDto.getNum());
                                temp.setWarehouseGoodsOutInDetailId(batchWarehouseGoodsOutInItemDto.getWarehouseGoodsOutInDetailId());
                                temp.setGoodsId(batchWarehouseGoodsOutInItemDto.getGoodsId());
                                temp.setRelatedId(batchWarehouseGoodsOutInItemDto.getRelatedId());
                                return temp;
                            }).collect(Collectors.toList())).filter(CollUtil::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());

                    List<BatchRInvoiceDetailJOperateLogDto> surplusCanRelevanceInfoList = new ArrayList<>();
                    // 去关系表 T_R_INVOICE_DETAIL_J_OPERATE_LOG 减去已经分摊过的 出库记录
                    List<BatchWarehouseGoodsOutInItemDto> accessWarehouseOutList = collect.stream().peek(item -> {
                        BigDecimal occupyNum = batchInvoiceDetailOperateLogDtoMapper.getSumNumByOperateLogId(item.getWarehouseGoodsOutInDetailId().intValue());
                        item.setNum(item.getNum().abs().subtract(occupyNum));
                    }).filter(dto -> dto.getNum().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());

                    // 遍历可用的出库记录明细，将本次的发票明细中的数量分摊进出库明细中
                    for (BatchWarehouseGoodsOutInItemDto outInItemDto : accessWarehouseOutList) {
                        BatchRInvoiceDetailJOperateLogDto dto = new BatchRInvoiceDetailJOperateLogDto();
                        dto.setRelatedId(outInItemDto.getRelatedId());
                        dto.setOperateLogId(Convert.toInt(outInItemDto.getWarehouseGoodsOutInDetailId()));
                        dto.setGoodsId(outInItemDto.getGoodsId());
                        surplusCanRelevanceInfoList.add(dto);
                        if (outInItemDto.getNum().compareTo(needRecordNum) >= 0) {
                            // 若当前出库明细的可用数量大于等于发票明细的 需录数量
                            dto.setCanRelationNum(needRecordNum);
                            break;
                        }
                        dto.setCanRelationNum(outInItemDto.getNum());
                        // 减去本次出库明细分摊掉的 发票明细数量
                        needRecordNum = needRecordNum.subtract(outInItemDto.getNum());
                    }
                    this.relationInvoiceDetailOperateLog(surplusCanRelevanceInfoList, detailDto, 2);
                }
            }
        }
    }


    /**
     * 绑定采购入库和蓝票关系
     *
     * @param batchInvoiceDtoList 需要绑定的发票
     * @return 是否成功
     */
    public void bindBuyOrderBlueInvoiceAndBuyOrderWarehouseInRelation(List<BatchInvoiceDto> batchInvoiceDtoList) {
        log.info("绑定采购入库和蓝票关系:{}", JSON.toJSONString(batchInvoiceDtoList));
        if (CollUtil.isEmpty(batchInvoiceDtoList)) {
            return;
        }


        for (BatchInvoiceDto id : batchInvoiceDtoList) {
            // 排除采购蓝字作废
            List<BatchInvoiceDto> blueDisEnableByInvoiceCodeAndInvoiceNo = batchInvoiceDtoMapper.findBlueDisEnableByInvoiceCodeAndInvoiceNo(id.getInvoiceCode(), id.getInvoiceNo(), 503);
            if (CollUtil.isNotEmpty(blueDisEnableByInvoiceCodeAndInvoiceNo)) {
                log.info("当前采购蓝票存在蓝字作废,{}", JSON.toJSONString(blueDisEnableByInvoiceCodeAndInvoiceNo));
                continue;
            }

            // 排除存在冲销票的


            List<BatchInvoiceDto> reversalInvoice = batchRInvoiceJInvoiceDtoMapper.findReversalInvoice(id.getInvoiceId());

            if (CollUtil.isNotEmpty(reversalInvoice)) {
                log.info("当前票存在冲销票,{}", JSON.toJSONString(reversalInvoice));
                continue;
            }


            List<BatchInvoiceDetailDto> allValidInvoiceDetailList = batchInvoiceDetailDtoMapper.getAllValidInvoiceDetailByInvoiceIds(Collections.singletonList(id.getInvoiceId()));

            allValidInvoiceDetailList = buyOrderVirtualInvoiceHandle.checkAndProcessIfHaveVirtualInvoice(allValidInvoiceDetailList);
            if (CollectionUtil.isEmpty(allValidInvoiceDetailList)) {
                log.info("无满足条件的发票保存关联入库记录发票详情");
                continue;
            }

            for (BatchInvoiceDetailDto a : allValidInvoiceDetailList) {
                List<BatchRInvoiceDetailJOperateLogDto> validWarehousingLogList = batchRInvoiceDetailJOperateLogDtoMapper.getValidWarehousingLogByRelatedIds(Collections.singletonList(a.getDetailgoodsId()), ErpConstant.ONE);
                validWarehousingLogList = buyOrderVirtualInvoiceHandle.checkAndProcessVirtualOperateLog(validWarehousingLogList);
                if (CollectionUtil.isEmpty(validWarehousingLogList)) {
                    log.error("当前发票商品无可匹配入库记录 InvoiceId:{}", JSON.toJSONString(id.getInvoiceId()));
                    continue;
                }
                this.relationInvoiceDetailOperateLog(validWarehousingLogList, a, ErpConstant.ONE);
            }


        }

    }


    /**
     * 绑定采购出库和红票关系
     *
     * @param batchInvoiceDtoList 需要绑定的发票
     * @return 是否成功
     */
    public void bindBuyOrderRedInvoiceAndBuyOrderWarehouseOutRelation(List<BatchInvoiceDto> batchInvoiceDtoList) {
        log.info("绑定采购出库和红票关系:{}", JSON.toJSONString(batchInvoiceDtoList));
        if (CollUtil.isEmpty(batchInvoiceDtoList)) {
            return;
        }

        for (BatchInvoiceDto invoiceDto : batchInvoiceDtoList) {
            List<BatchInvoiceDetailDto> invoiceDetailDtos = batchInvoiceDetailDtoMapper.getAllValidInvoiceDetailByInvoiceIds(Collections.singletonList(invoiceDto.getInvoiceId()));

            invoiceDetailDtos = buyOrderVirtualInvoiceHandle.checkAndProcessIfHaveVirtualInvoice(invoiceDetailDtos);

            if (CollUtil.isEmpty(invoiceDetailDtos)) {
                log.info("当前发票下可录无详情数据:{}", invoiceDto.getInvoiceId());
                continue;
            }

            for (BatchInvoiceDetailDto detailDto : invoiceDetailDtos) {
                // 根据发票中的采购id获取售后明细id
                AfterSaleGoodsIdQueryReqDto afterSaleGoodsIdQueryReq = AfterSaleGoodsIdQueryReqDto.builder()
                        .buyOrderGoodsId(detailDto.getDetailgoodsId()).afterSalesId(invoiceDto.getAfterSalesId()).build();
                log.info("获取售后详情id的参数,{}", JSON.toJSONString(afterSaleGoodsIdQueryReq));
                Integer afterGoodsId = batchBuyorderGoodsDtoMapper.getAfterGoodsIdByCondition(afterSaleGoodsIdQueryReq);
                // 获取售后单
                BatchAfterSalesDto afterSales = batchAfterSalesDtoMapper.findByAfterSalesId(invoiceDto.getAfterSalesId());
                // 根据售后明细id和关联表查询出，还可以剩余关联的对象信息
                List<BatchRInvoiceDetailJOperateLogDto> surplusCanRelevanceInfoList = batchRInvoiceDetailJOperateLogDtoMapper
                        .getValidWarehousingLogByRelatedId(afterGoodsId, afterSales.getAfterSalesNo() ,ErpConstant.SIX,0);

                surplusCanRelevanceInfoList = buyOrderVirtualInvoiceHandle.checkAndProcessVirtualOperateLog(surplusCanRelevanceInfoList);

                this.relationInvoiceDetailOperateLog(surplusCanRelevanceInfoList, detailDto, ErpConstant.SIX);
            }
        }
    }


    /**
     * 发票详情关联有效出入库记录
     *
     * @param surplusCanRelevanceInfoList
     * @param invoiceDetailDto
     */
    private void relationInvoiceDetailOperateLog(List<BatchRInvoiceDetailJOperateLogDto> surplusCanRelevanceInfoList,
                                                 BatchInvoiceDetailDto invoiceDetailDto, Integer operateLog) {
        log.info("发票详情关联有效出入库记录:{}{}{}", JSON.toJSONString(surplusCanRelevanceInfoList), JSON.toJSONString(invoiceDetailDto), operateLog);
        for (BatchRInvoiceDetailJOperateLogDto validDetailGoodsItem : surplusCanRelevanceInfoList) {
            if (validDetailGoodsItem.getCanRelationNum().compareTo(BigDecimal.ZERO) < 1) {
                continue;
            }

            log.info("开始绑定发票与出入库记录:{}", JSON.toJSONString(validDetailGoodsItem));
            BigDecimal thisRelationNum = validDetailGoodsItem.getCanRelationNum().compareTo(invoiceDetailDto.getNum()) > -1 ?
                    invoiceDetailDto.getNum() : validDetailGoodsItem.getCanRelationNum();

            BatchRInvoiceDetailJOperateLogDto rInvoiceDetailJOperateLogEntity = BatchRInvoiceDetailJOperateLogDto.builder()
                    .invoiceDetailId(invoiceDetailDto.getInvoiceDetailId())
                    .operateLogId(validDetailGoodsItem.getOperateLogId())
                    .invoiceId(invoiceDetailDto.getInvoiceId())
                    .detailGoodsId(invoiceDetailDto.getDetailgoodsId())
                    .goodsId(validDetailGoodsItem.getGoodsId())
                    .sku("V" + validDetailGoodsItem.getGoodsId())
                    .num(thisRelationNum)
                    .operateType(operateLog)
                    .isDelete(ErpConstant.DEFAULT_ID)
                    .build();
            rInvoiceDetailJOperateLogEntity.setAddTime(new Date());
            rInvoiceDetailJOperateLogEntity.setModTime(new Date());
            rInvoiceDetailJOperateLogEntity.setCreator(ErpConstant.DEFAULT_USER_ID);
            rInvoiceDetailJOperateLogEntity.setUpdater(ErpConstant.DEFAULT_USER_ID);
            log.info("发票与出入库关联关系生成 rInvoiceDetailJOperateLogEntity:{}", JSON.toJSONString(rInvoiceDetailJOperateLogEntity));
            batchRInvoiceDetailJOperateLogDtoMapper.insertSelective(rInvoiceDetailJOperateLogEntity);

            validDetailGoodsItem.setCanRelationNum(validDetailGoodsItem.getCanRelationNum().subtract(thisRelationNum).stripTrailingZeros());
            invoiceDetailDto.setNum(invoiceDetailDto.getNum().subtract(thisRelationNum).stripTrailingZeros());
            if (invoiceDetailDto.getNum().compareTo(BigDecimal.ZERO) < 1) {
                break;
            }
        }
    }
}
