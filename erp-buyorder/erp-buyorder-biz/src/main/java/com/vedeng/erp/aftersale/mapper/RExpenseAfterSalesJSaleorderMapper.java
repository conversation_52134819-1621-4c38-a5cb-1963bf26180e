package com.vedeng.erp.aftersale.mapper;

import com.vedeng.erp.aftersale.domain.entity.RExpenseAfterSalesJSaleorderEntity;
import java.util.List;

import com.vedeng.erp.aftersale.dto.RExpenseAfterSalesJSaleorderDto;
import org.apache.ibatis.annotations.Param;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/1/5 13:56
 **/
public interface RExpenseAfterSalesJSaleorderMapper {
    /**
     * delete by primary key
     * @param tRExpenseAfterSalesJSaleorderId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer tRExpenseAfterSalesJSaleorderId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(RExpenseAfterSalesJSaleorderEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(RExpenseAfterSalesJSaleorderEntity record);

    /**
     * select by primary key
     * @param tRExpenseAfterSalesJSaleorderId primary key
     * @return object by primary key
     */
    RExpenseAfterSalesJSaleorderEntity selectByPrimaryKey(Integer tRExpenseAfterSalesJSaleorderId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(RExpenseAfterSalesJSaleorderEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(RExpenseAfterSalesJSaleorderEntity record);


    /**
     * 根据费用单id 查询售后关联关系
     * @param buyorderExpenseId
     * @return 售后关系
     */
    List<RExpenseAfterSalesJSaleorderDto> selectByBuyorderExpenseId(Integer buyorderExpenseId);

    /**
     * 根据费用单id 查询采购费用售后退货数量
     * @param buyorderExpenseId
     * @return
     */
    List<RExpenseAfterSalesJSaleorderDto> getReturnNumByBuyorderExpenseId(Integer buyorderExpenseId);

    /**
     * 删除售后的关系
     * @param expenseAfterSalesId 售后单id
     * @return
     */
    int deleteByExpenseAfterSalesId(@Param("expenseAfterSalesId")Long expenseAfterSalesId);

    RExpenseAfterSalesJSaleorderDto getReturnNumByExpenseAfterSalesId(RExpenseAfterSalesJSaleorderDto rExpenseAfterSalesJSaleorderDto);
}