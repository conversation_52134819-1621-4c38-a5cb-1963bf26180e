<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>CRM</title>
    <style>
        /* 给整个页面添加蓝色背景 */
        body {
            background-color: #3384f0;
            color: #fff;
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        /* 添加加载效果的样式 */
        #loading {
            display: none;
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            font-size: 20px;
            color: #fff;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        /* 转圈效果 */
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #fff;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 10px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 成功消息的样式 */
        #success-message {
            display: none;
            position: fixed;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            font-size: 20px;
            color: #fff;
            background: url('success-background.jpg') no-repeat center center;
            background-size: cover;
            padding: 50px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<div id="loading">
    <div class="spinner"></div>
    正在加载，请稍候...
</div>
<input type="hidden" id="murl" name="murl" value="${requestScope.murl}"/>
<input type="hidden" id="pcurl" name="pcurl" value="${requestScope.pcurl}"/>

<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/common/jquery.min.js'></script>
<script src="//res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>

<script type="text/javascript">
    function isWeixinBrowser() {
        var ua = navigator.userAgent.toLowerCase();
        return ua.indexOf("micromessenger") != -1;
    }
    function isPC() {
        let userAgentInfo = navigator.userAgent;
        let Agents = new Array("Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod");
        let flag = true;
        for (let v = 0; v < Agents.length; v++) {
            if (userAgentInfo.indexOf(Agents[v]) > 0) {
                flag = false;
                break;
            }
        }
        return flag;
    }


    $(function(){
        // debugger
        if (!isWeixinBrowser()) {
            var targetUrl = "${requestScope.pcurl}";
            window.location.href=targetUrl;
        } else {
            if(isPC()){
                // 显示加载效果
                $('#loading').show();
                var appUrl = ""
                $.ajax({
                    url: "/crm/wx/getWeiXinPermissionsValidationConfig",
                    data: {
                        url: window.location.href.split("#")[0]
                    },
                    type: "get",
                    success: function (res) {
                        wx.config({
                            beta: true,// 必须这么写，否则wx.invoke调用形式的jsapi会有问题
                            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                            appId: res.data.corpid, // 必填，企业微信的corpid，必须与当前登录的企业一致
                            timestamp: res.data.timestamp, // 必填，生成签名的时间戳
                            nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
                            signature: res.data.signature,// 必填，签名，见附录-JS-SDK使用权限签名算法
                            jsApiList: ['openDefaultBrowser'] //必填，传入需要使用的接口名称
                        });

                        appUrl = res.data.appUrl

                        wx.ready(function () {
                            openDefaultBrowser();
                        });

                        wx.error(function (res) {
                            // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
                            console.log(res);
                            // 隐藏加载效果
                            $('#loading').hide();
                        });
                    },
                    error: function () {
                        // 请求失败时隐藏加载效果
                        $('#loading').hide();
                    }
                });
                //openDefaultBrowser();
            }else{
                window.location.href = "${requestScope.murl}";
            }
        }


        function openDefaultBrowser() {
            var targetUrl = "${requestScope.pcurl}";
            wx.invoke('openDefaultBrowser',
                {'url': targetUrl},
                function(res){
                    if(res.err_msg == "openDefaultBrowser:ok"){
                        $('#loading').hide();
                        window.close();
                    } else {
                        $('#loading').hide();
                        window.close();
                    }
                });
        }


    });
</script>
</body>
</html>
