package com.wms.dto;

import java.util.List;

/**
 * <AUTHOR>
 * @description:入库SN码dto
 * @date 2020/11/169:56
 */
public class InputSnDto extends AbstractCommonDto{

    /**
     * WMS单据编号
     */
    private String AsnNo;

    /**
     * ERP单据编号
     */
    private String AsnReference1;

    /**
     * 订单类型
     */
    private String AsnType;

    private String ReceivedTime;

    private List<InputSnDetailDto> details;

    public String getAsnNo() {
        return AsnNo;
    }

    public void setAsnNo(String asnNo) {
        AsnNo = asnNo;
    }

    public String getAsnReference1() {
        return AsnReference1;
    }

    public void setAsnReference1(String asnReference1) {
        AsnReference1 = asnReference1;
    }

    public String getAsnType() {
        return AsnType;
    }

    public void setAsnType(String asnType) {
        AsnType = asnType;
    }

    public String getReceivedTime() {
        return ReceivedTime;
    }

    public void setReceivedTime(String receivedTime) {
        ReceivedTime = receivedTime;
    }

    public List<InputSnDetailDto> getDetails() {
        return details;
    }

    public void setDetails(List<InputSnDetailDto> details) {
        this.details = details;
    }

    @Override
    public String toString() {
        return "InputSnDto{" +
                "AsnNo='" + AsnNo + '\'' +
                ", AsnReference1='" + AsnReference1 + '\'' +
                ", AsnType='" + AsnType + '\'' +
                ", ReceivedTime='" + ReceivedTime + '\'' +
                ", details=" + details +
                '}';
    }
}
