package com.newtask.filemove;

import com.newtask.filemove.dto.CommandProcessorResult;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 文件迁移的定时任务
 */
@Component
@JobHandler(value = "fileMove2OssHandler")
public class FileMoveTimeTask extends AbstractJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileMoveTimeTask.class);

    @Autowired
    private CommandProcessorFactory commandProcessorFactory;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        if(StringUtils.isEmpty(param)){
            LOGGER.info("参数不能为空");
            return ReturnT.FAIL;
        }

        try{

            //命令接口的规范 命令类型|业务类型|具体的参数
            // 例如(单条命令处理): single|trader|1,3
            // 例如(批处理命令处理): batch|trader|all
            CommandProcessorResult commandProcessorResult = commandProcessorFactory.getCommandProcessor(param);

            if(commandProcessorResult == null || commandProcessorResult.getFileMoveInterface() == null){
                throw new Exception("文件处理器为空");
            }

            commandProcessorResult.fileMove();

        }catch (Exception e){
            LOGGER.info("文件迁移失败 :" + e.getMessage());
            return ReturnT.FAIL;
        }

        return SUCCESS;

    }

}
