package com.vedeng.temporal.service;

import com.vedeng.temporal.domain.dto.FlowOrderInfoUpdateRequest;
import com.vedeng.temporal.domain.dto.FlowOrderInfoQueryRequest;
import com.vedeng.temporal.domain.dto.FlowOrderInfoResponse;
import com.vedeng.temporal.domain.entity.FlowOrderInfoEntity;

import java.util.List;

/**
 * 业务流程汇总信息服务接口
 * 
 * 职责说明：
 * - 封装汇总信息的业务逻辑
 * - 处理状态转换和验证
 * - 支持批量操作
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-14
 */
public interface FlowOrderInfoService {

    /**
     * 更新流程汇总信息
     * 
     * 业务逻辑：
     * - 验证请求参数
     * - 检查状态转换有效性
     * - 执行数据更新
     *
     * @param request 更新请求
     * @throws RuntimeException 当更新失败时抛出异常
     */
    void updateFlowOrderInfo(FlowOrderInfoUpdateRequest request);


    /**
     * 查询流程汇总信息
     *
     * @param request 查询请求
     * @return 汇总信息响应对象，不存在时返回null
     */
    FlowOrderInfoResponse queryFlowOrderInfo(FlowOrderInfoQueryRequest request);

    /**
     * 根据节点ID查询汇总信息
     * 注意：一个FlowNodeId可能对应多条不同类型的记录，建议使用 getByFlowNodeIdAndType 方法
     *
     * @param flowNodeId 流程节点ID
     * @return 汇总信息实体，不存在时返回null
     */
    FlowOrderInfoEntity getByFlowNodeId(Long flowNodeId);

    /**
     * 根据节点ID和业务类型查询汇总信息
     * 推荐使用此方法，因为一个FlowNodeId可能对应多条不同类型的记录
     *
     * @param flowNodeId 流程节点ID
     * @param flowOrderInfoType 业务类型：0-采购，1-销售
     * @return 汇总信息实体，不存在时返回null
     */
    FlowOrderInfoEntity getByFlowNodeIdAndType(Long flowNodeId, Integer flowOrderInfoType);

    /**
     * 检查汇总信息是否存在
     * 注意：一个FlowNodeId可能对应多条不同类型的记录，建议使用 existsByFlowNodeIdAndType 方法
     *
     * @param flowNodeId 流程节点ID
     * @return true-存在，false-不存在
     */
    boolean existsByFlowNodeId(Long flowNodeId);

    /**
     * 检查指定节点ID和业务类型的汇总信息是否存在
     * 推荐使用此方法，因为一个FlowNodeId可能对应多条不同类型的记录
     *
     * @param flowNodeId 流程节点ID
     * @param flowOrderInfoType 业务类型：0-采购，1-销售
     * @return true-存在，false-不存在
     */
    boolean existsByFlowNodeIdAndType(Long flowNodeId, Integer flowOrderInfoType);

    /**
     * 删除流程汇总信息
     * 
     * 逻辑删除：
     * - 设置 IS_DELETE = 1
     * - 保留历史数据
     *
     * @param flowNodeId 流程节点ID
     * @throws RuntimeException 当删除失败时抛出异常
     */
    void deleteFlowOrderInfo(Long flowNodeId);

    /**
     * 创建流程汇总信息
     * 
     * 创建逻辑：
     * - 验证节点ID唯一性
     * - 设置默认状态值
     * - 记录创建信息
     *
     * @param request 创建请求
     * @return 创建的汇总信息ID
     * @throws RuntimeException 当创建失败时抛出异常
     */
    Long createFlowOrderInfo(FlowOrderInfoUpdateRequest request);

    /**
     * 查找数据不一致的记录
     * 
     * 一致性检查：
     * - 检查业务单据状态与汇总信息是否一致
     * - 用于数据修复
     *
     * @return 不一致的记录列表
     */
    List<FlowOrderInfoEntity> findInconsistentRecords();

    /**
     * 修复数据不一致
     * 
     * 修复策略：
     * - 以业务单据状态为准
     * - 重新计算汇总信息
     * - 记录修复日志
     *
     * @param entity 需要修复的记录
     */
    void repairInconsistentData(FlowOrderInfoEntity entity);
}
