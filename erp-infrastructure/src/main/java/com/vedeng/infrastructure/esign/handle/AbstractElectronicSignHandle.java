package com.vedeng.infrastructure.esign.handle;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.vedeng.base.api.dto.EsignReqDTO;
import com.vedeng.base.api.dto.EsignResDTO;
import com.vedeng.base.api.dto.EsignSignInfo;
import com.vedeng.base.api.dto.reqParam.*;
import com.vedeng.base.api.dto.resParam.*;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.PdfUtil;
import com.vedeng.infrastructure.esign.api.ElectronicSignApi;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.domain.dto.ElectronicSignRecordDto;
import com.vedeng.infrastructure.esign.domain.entity.ElectronicSignRecordEntity;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import com.vedeng.infrastructure.esign.service.ElectronicSignRecordService;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 电子签章抽象类
 * @date 2021/12/4 16:54
 */
@Slf4j
public abstract class AbstractElectronicSignHandle {

    /**
     * 合同章的名字-固定，不能修改
     */
    public static final String STATIC_SEAL_NAME_HETONG = "合同专用章";

    /**
     * 合同中的隐藏字-甲方-固定，不能修改
     */
    public static final String STATIC_HIDE_NAME_JIA = "$jia$";

    /**
     * 合同中的隐藏字-乙方-固定，不能修改
     */
    public static final String STATIC_HIDE_NAME_YI = "$yi$";



    @Autowired
    private ElectronicSignApi electronicSignApi;
    @Autowired
    private OssUtilsService ossUtilsService;
    @Autowired
    public ElectronicSignRecordService electronicSignRecordService;

    @Value("${oss_http}")
    public String ossHttp;
    /**
     * OSS地址
     */
    @Value("${oss_url}")
    public String ossUrl;
    /**
     * oss文档路径
     */
    @Value("${oss_file_path}")
    public String ossFilePath;

    /**
     * 贝登盖章关键字
     */
    private static final String VEDENG_SIGN_KEYWORD = "$yi$";
    /**
     * 客户盖章关键字
     */
    private static final String CUSTOMER_SIGN_KEYWORD = "$jia$";
    /**
     * 贝登章位置
     */
    private static final Pos VEDENG_STAMP = new Pos((float) 442.22, (float) 416.66);
    /**
     * 贝登骑缝章位置
     */
    private static final XYPageNo VEDENG_RIDE_STITCH = new XYPageNo("all", CollUtil.newArrayList(new Pos(534, 366)));
    /**
     * 客户骑缝章位置
     */
    private static final XYPageNo CUSTOMER_RIDE_STITCH = new XYPageNo("all", CollUtil.newArrayList(new Pos(534, 503)));

    @PostConstruct
    private void initTLS() {
        // 解决异常：SSL peer shut down incorrectly
        System.setProperty("https.protocols", "TLSv1,TLSv1.1,TLSv1.2,SSLv3");
    }

    private static final int CORE_POOL_SIZE = 10;
    private static final int MAX_POOL_SIZE = CORE_POOL_SIZE * 5;
    private static final long KEEP_ALIVE_TIME = 60L;
    /**
     * 最大重试次数
     */
    public static final int MAX_RETRY_COUNT = 3;

    /**
     * 声明异步线程池
     */
    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(CORE_POOL_SIZE,
            MAX_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(MAX_POOL_SIZE * 4, true),
            new ThreadFactoryBuilder().setNameFormat("electronicSign-pool-%d").build(),
            new ThreadPoolExecutor.AbortPolicy());

    /**
     * 电子签章异步任务
     */
    public final void electronicSign(ElectronicSignParam electronicSignParam) {
        CompletableFuture<Void> completableFuture = CompletableFuture.runAsync(() -> this.asyncTaskElectronicSign(electronicSignParam), executor);
        completableFuture.thenApply(result -> {
            log.info("电子签章:异步执行签章任务成功");

            ElectronicSignRecordDto dto = ElectronicSignRecordDto
                    .builder()
                    .businessId(electronicSignParam.getBusinessId())
                    .businessType(electronicSignParam.getElectronicSignBusinessEnums().getType())
                    .build();
            electronicSignRecordService.sendSuccess(dto);

            this.electronicSignSuccessCompensate(electronicSignParam);
            return null;
        });
        completableFuture.exceptionally(e -> {
            log.error("电子签章:异步执行签章任务失败", e);
            AtomicInteger retryCount = electronicSignParam.getRetryCount();
            retryCount.incrementAndGet();
            if (retryCount.get() <= MAX_RETRY_COUNT) {
                log.warn("电子签章:重试异步执行签章任务,重试次数{},编号{}", retryCount.get(), electronicSignParam.getBusinessInfo().getOrderNo());
                electronicSign(electronicSignParam);
                return null;
            } else {
                List<ElectronicSignRecordEntity> electronicSignRecordList = electronicSignRecordService.getElectronicSignRecord(
                        electronicSignParam.getAuthorizationApplyId().toString(),
                        electronicSignParam.getElectronicSignBusinessEnums().getType());

                if (CollUtil.isNotEmpty(electronicSignRecordList)) {
                    electronicSignRecordList.forEach(electronicSignRecordEntity -> {
                        // 重试次数+1
                        electronicSignRecordService.msgRetryNumAdd(electronicSignRecordEntity);
                    });
                } else {
                    ElectronicSignRecordDto dto = ElectronicSignRecordDto
                            .builder()
                            .businessId(electronicSignParam.getBusinessId())
                            .businessType(electronicSignParam.getElectronicSignBusinessEnums().getType())
                            .errorMsg(e.getMessage())
                            .build();
                    electronicSignRecordService.sendFail(dto);
                }
                this.electronicSignFailCompensate(e.getMessage(), electronicSignParam);

                log.error("电子签章:内部自动重试{}次,异步执行签章任务失败", MAX_RETRY_COUNT, e);
            }
            return null;
        });
    }


    /**
     * 电子签章调用流程
     * 1. 子类实现 PDF 的转换，并上传到 OSS 返回文件实体
     * 2. 抽象类完成上传到e签宝
     * 3. 调用公共服务e签宝 获取坐标 （坐标已知，可忽略）
     * 4. 调用公共服务e签宝 一步签
     *
     * @param electronicSignParam 电子签章参数
     */
    private void asyncTaskElectronicSign(ElectronicSignParam electronicSignParam) {
        FileInfo fileInfo;
        try {
            fileInfo = this.toPdfGetFile(electronicSignParam);
        } catch (Exception e) {
            log.error("电子签章:获取PDF文件异常", e);
            throw new ServiceException("电子签章:获取PDF文件异常");
        }
        UploadDto uploadDto = new UploadDto();
        uploadDto.setFileInfo(fileInfo);
        BusinessInfo businessInfo = electronicSignParam.getBusinessInfo();
        businessInfo.setThirdNo(IdUtil.simpleUUID());
        businessInfo.setPlatfromId(32);
        businessInfo.setPort(4);
        uploadDto.setBusinessInfo(businessInfo);
        uploadDto.setConvert2Pdf(false);
        this.uploadFile(fileInfo, uploadDto);

        fileInfo.setBusinessInfo(businessInfo);
        List<SignerDto> signers = new ArrayList<>();
        log.info("electronicSignParam detail:{}", JSONObject.toJSONString(electronicSignParam));
        // 双方盖章
        if (electronicSignParam.getFlowType() == 1) {
            fileInfo.setKeywords(VEDENG_SIGN_KEYWORD);
            PositionInfo vdPositionInfo = this.searchKeywordsPosition(fileInfo);
            this.createSignerDto(signers, true, electronicSignParam, fileInfo, vdPositionInfo);
            fileInfo.setKeywords(CUSTOMER_SIGN_KEYWORD);
            PositionInfo customerPositionInfo = this.searchKeywordsPosition(fileInfo);
            this.createSignerDto(signers, false, electronicSignParam, fileInfo, customerPositionInfo);
        }
        // 仅盖贝登章
        if (electronicSignParam.getFlowType() == 2) {
            switch (electronicSignParam.getElectronicSignBusinessEnums()) {
                case VENDOR:
                    this.createSignerDto(signers, true, electronicSignParam, fileInfo, null);
                    break;
                case SALE_ORDER:
                case BUY_ORDER:
                case BUY_ORDER_EXPENSE:
                    fileInfo.setKeywords(VEDENG_SIGN_KEYWORD);
                    PositionInfo vdPositionInfo = this.searchKeywordsPosition(fileInfo);
                    this.createSignerDto(signers, true, electronicSignParam, fileInfo, vdPositionInfo);
                    break;
                case SALE_AUTHORIZATION:
                    PositionInfo positionInfo;
                    try {
                        fileInfo.setKeywords("南京贝登医疗股份有限公司");
                        positionInfo = this.searchKeywordsPosition(fileInfo);
                    } catch (Exception e) {
                        log.warn("电子签章:根据 [南京贝登医疗股份有限公司] 获取关键字位置信息异常,按照[南京⻉登医疗股份有限公司]再次获取,");
                        fileInfo.setKeywords("南京⻉登医疗股份有限公司");
                        positionInfo = this.searchKeywordsPosition(fileInfo);
                    }
                    KeywordInfo keywordInfo = CollUtil.getLast(positionInfo.getKeywordInfoList());
                    XYPageNo last = CollUtil.getLast(keywordInfo.getXYPageNoInfoList());
                    Pos lastPos = CollUtil.getFirst(last.getPosList());
                    last.setPosList(CollUtil.newArrayList(lastPos));
                    keywordInfo.setXYPageNoInfoList(CollUtil.newArrayList(last));
                    positionInfo.setKeywordInfoList(CollUtil.newArrayList(keywordInfo));
                    this.createSignerDto(signers, true, electronicSignParam, fileInfo, positionInfo);
                    break;
                default:
                    break;
            }
        }
        List<SignCompanyInfo> signCompanyInfoList = new ArrayList<>();
        if(electronicSignParam.getFlowType() == 3) {
            switch (electronicSignParam.getElectronicSignBusinessEnums()) {
                case MULT_SALEORDER_SEAL:
                    this.getSignCompanyInfoList(electronicSignParam, signCompanyInfoList);
                    break;
                case MULT_BUYORDER_SEAL:
                case CROSS_SHOUQUANSHU:
                    this.getSignCompanyInfoList(electronicSignParam,signCompanyInfoList);
                    break;
                case MULT_LIUZHUANG_SEAL:
                    this.getSignCompanyInfoList(electronicSignParam,signCompanyInfoList);
                default:
                    break;
            }
        }
        DoubleSignFlowDto flowDto = new DoubleSignFlowDto();
        flowDto.setOnlySignLast(electronicSignParam.getOnlySignLast());//只盖最后一个关键字出现的位置
        flowDto.setBusinessInfo(businessInfo);
        flowDto.setNoticeType("1");
        flowDto.setSignerDtos(signers);
        if(electronicSignParam.getFlowType() == 3) {
            flowDto.setFileId(fileInfo.getFileId());
            flowDto.setSignCompanyInfoList(signCompanyInfoList);
            this.createFlowOneStepForDoubleCompany(flowDto);
        }else{
            this.createFlowOneStep(flowDto);
        }

    }


    /**
     * 转换 PDF 得到文件
     * 同时重新封装了BusinessInfo里的orderNo
     *
     * @param electronicSignParam 电子签章参数实体
     * @return 文件链接
     */
    protected abstract FileInfo toPdfGetFile(ElectronicSignParam electronicSignParam);

    protected void getSignCompanyInfoList(ElectronicSignParam electronicSignParam,List<SignCompanyInfo> signCompanyInfoList) {
        //do nothing 由子实现类去做
    }

    /**
     * 组装请求参数-20250627 修改销售订单-支持双章
     * @param businessId 业务单据的ID
     * @param businessInfo
     * @return
     */
    public abstract  ElectronicSignParam buildElectronicSignParam(Integer businessId, BusinessInfo businessInfo);





    /**
     * 签章成功补偿措施
     *
     * @param electronicSignParam 入参
     */
    protected abstract void electronicSignSuccessCompensate(ElectronicSignParam electronicSignParam);


    /**
     * 签章失败补偿措施
     *
     * @param errorMsg            异常信息
     * @param electronicSignParam 入参
     */
    protected abstract void electronicSignFailCompensate(String errorMsg, ElectronicSignParam electronicSignParam);

    /**
     * mq消息前置处理器
     *
     * @param signCallbackDto 电子签章MQ回传消息
     */
    protected abstract void preMqProcessors(SignCallbackDto signCallbackDto);

    /**
     * mq消息业务处理器
     *
     * @param signCallbackDto 电子签章MQ回传消息
     */
    protected abstract void mqProcessors(SignCallbackDto signCallbackDto);


    /**
     * mq消息后置处理器
     *
     * @param signCallbackDto 电子签章MQ回传消息
     */
    protected abstract void postMqProcessors(SignCallbackDto signCallbackDto);

    /**
     * mq消费失败补偿
     *
     * @param errorMsg        异常信息
     * @param signCallbackDto 电子签章MQ回传消息
     */
    protected abstract void dealWithByMqException(String errorMsg, SignCallbackDto signCallbackDto);


    /**
     * 电子签章解析MQ消息流程
     * 1. 下载电子签章文件上传oss
     * 2. 根据类型处理不同业务
     * - a. 厂商资质获取pdf：需要对pdf进行转图片处理
     * - b. 采购和销售订单：半程签贝登章 更新合同url
     * - c。销售订单： 全程签章 新增合同回传记录
     */
    public final void parseElectronicSignMq(SignCallbackDto signCallbackDto) {
        CallbackDto callbackDto = new CallbackDto();
        callbackDto.setAccountId(signCallbackDto.getAccountId());
        callbackDto.setFlowId(signCallbackDto.getFlowId());
        try {
            this.preMqProcessors(signCallbackDto);
            this.mqProcessors(signCallbackDto);
            this.postMqProcessors(signCallbackDto);
            electronicSignRecordService.signSuccess(signCallbackDto.getOrderNo(), Integer.parseInt(signCallbackDto.getBusinessName()));
            callbackDto.setSuccess(ErpConstant.F);
            this.mqCallback(callbackDto);
        } catch (Exception e) {
            log.error("电子签章MQ消息解析:根据类型处理不同业务异常", e);
            this.dealWithByMqException(e.getMessage(), signCallbackDto);
            callbackDto.setSuccess(ErpConstant.T);
            this.mqCallback(callbackDto);
            electronicSignRecordService.signFail(signCallbackDto.getOrderNo(),  Integer.parseInt(signCallbackDto.getBusinessName()), e.getMessage());
            throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_MQ_ERROR, e);
        }
    }


    /**
     * 创建签署方 和 签章位置
     *
     * @param signers             签署集合
     * @param isPlatform          是否是贝登平台
     * @param electronicSignParam 电子签章公共参数
     * @param fileInfo            文件
     * @param positionInfo        位置
     */
    private void createSignerDto(List<SignerDto> signers, boolean isPlatform, ElectronicSignParam electronicSignParam, FileInfo fileInfo, PositionInfo positionInfo) {
        SignerDto signer = new SignerDto();
        signer.setPlatform(isPlatform);
        signer.setAccount(!isPlatform ? electronicSignParam.getAccount() : null);
        signer.setOrg(!isPlatform ? electronicSignParam.getOrg() : null);
        FilePosition filePosition = new FilePosition();
        filePosition.setFileId(fileInfo.getFileId());
        List<XYPageNo> xyPageNoList = this.createXyPageNoInfo(electronicSignParam.getElectronicSignBusinessEnums(), isPlatform, fileInfo, positionInfo);
        filePosition.setXYPageNoInfoList(xyPageNoList);
        signer.setFile(filePosition);
        signers.add(signer);
    }

    /**
     * 根据盖章流程类型,构建签章位置
     * 1. 半程厂商贝登章
     * 2. 半程采购单贝登章
     * 3. 半程销售单贝登章
     * 4. 全程销售单贝登章
     * 每次仅传一个位置,所以 positionInfo.getKeywordInfoList() 仅有一个或者没有
     */
    private List<XYPageNo> createXyPageNoInfo(ElectronicSignBusinessEnums electronicSignBusinessEnums, boolean isPlatform,
                                              FileInfo fileInfo, PositionInfo positionInfo) {
        List<XYPageNo> xyPageNoList = positionInfo != null ? CollUtil.getFirst(positionInfo.getKeywordInfoList()).getXYPageNoInfoList() : new ArrayList<>();
        switch (electronicSignBusinessEnums) {
            case SALE_ORDER:
            case BUY_ORDER:
            case BUY_ORDER_EXPENSE:
            case SALE_AUTHORIZATION:
                if (fileInfo.getTotalPage() > 1) {
                    // 骑缝章
                    if (isPlatform) {
                        xyPageNoList.add(VEDENG_RIDE_STITCH);
                    } else {
                        xyPageNoList.add(CUSTOMER_RIDE_STITCH);
                    }
                }
                break;
            case VENDOR:
                if (fileInfo.getTotalPage() > 1) {
                    XYPageNo firstXyPageNo = new XYPageNo("1", CollUtil.newArrayList(VEDENG_STAMP));
                    xyPageNoList.add(firstXyPageNo);
                    XYPageNo lastXyPageNo = new XYPageNo(fileInfo.getTotalPage().toString(), CollUtil.newArrayList(VEDENG_STAMP));
                    xyPageNoList.add(lastXyPageNo);
                    xyPageNoList.add(VEDENG_RIDE_STITCH);
                } else {
                    XYPageNo firstXyPageNo = new XYPageNo("1", CollUtil.newArrayList(VEDENG_STAMP));
                    xyPageNoList.add(firstXyPageNo);
                }
                break;
            default:
                break;
        }
        return xyPageNoList;
    }


    /**
     * 获取发起签章业务的所有签署地址
     */
    public final List<EsignSignInfo> getSignUrls(EsignReqDTO esignReqDTO) {
        RestfulResult<EsignResDTO> flowDtoResult;
        try {
            esignReqDTO.setThirdNo(IdUtil.simpleUUID());
            log.info("电子签章: 获取签署地址记录,入参[{}]", JSON.toJSONString(esignReqDTO));
            flowDtoResult = electronicSignApi.querySignUrls(esignReqDTO);
            if (!flowDtoResult.isSuccess()) {
                log.error("电子签章: 获取签署地址记录,调用异常[{}]", flowDtoResult.getMessage());
                throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_ERROR);
            }
            log.info("电子签章: 获取签署地址记录,返回值[{}]", JSON.toJSONString(flowDtoResult));
        } catch (Exception e) {
            log.error("电子签章: 获取签署地址记录,异常信息", e);
            throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_ERROR);
        }
        EsignResDTO esignResDTO = Optional.ofNullable(flowDtoResult.getData()).orElse(new EsignResDTO());
        return Optional.ofNullable(esignResDTO.getSignUrlList()).orElse(new ArrayList<>());
    }

    /**
     * 根据手机号获取e签宝注册姓名
     *
     * @param phone 手机号
     */
    public String getSignUsername(String phone) {
        RestfulResult<EAccount> restfulResult;
        try {
            EAccount eAccount = new EAccount();
            eAccount.setPhoneNumber(phone);
            log.info("电子签章: 根据手机号获取e签宝注册姓名,入参[{}]", JSON.toJSONString(phone));
            restfulResult = electronicSignApi.queryEsignAccount(eAccount);
            if (!restfulResult.isSuccess()) {
                log.error("电子签章: 根据手机号获取e签宝注册姓名,调用异常[{}]", restfulResult.getMessage());
                throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_ERROR);
            }
            log.info("电子签章: 根据手机号获取e签宝注册姓名,返回值[{}]", JSON.toJSONString(eAccount));
        } catch (Exception e) {
            log.error("电子签章: 根据手机号获取e签宝注册姓名,异常信息", e);
            throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_ERROR);
        }
        return restfulResult.getData().getRealName();
    }

    /**
     * 上传文件
     *
     * @param fileInfo  文件
     * @param uploadDto 上传dto
     */
    private void uploadFile(FileInfo fileInfo, UploadDto uploadDto) {
        try {
            log.info("电子签章: 上传文件,入参[{}]", JSON.toJSONString(uploadDto));
            RestfulResult<FileInfo> resultFileInfo = electronicSignApi.uploadFile(uploadDto);
            if (!resultFileInfo.isSuccess()) {
                log.error("电子签章: 上传文件,调用异常[{}]", resultFileInfo.getMessage());
                throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_ERROR);
            }
            log.info("电子签章: 上传文件,返回值[{}]", JSON.toJSONString(resultFileInfo));
            fileInfo.setFileId(resultFileInfo.getData().getFileId());
            fileInfo.setTotalPage(resultFileInfo.getData().getTotalPage());
        } catch (Exception e) {
            log.error("电子签章: 上传文件,异常信息", e);
            throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_ERROR);
        }
    }

    /**
     * 获取关键字坐标
     *
     * @param fileInfo 文件
     * @return 位置信息
     */
    private PositionInfo searchKeywordsPosition(FileInfo fileInfo) {
        PositionInfo positionInfo;
        try {
            log.info("电子签章: 获取文件坐标位置,入参[{}]", JSON.toJSONString(fileInfo));
            RestfulResult<PositionInfo> positionInfoResult = electronicSignApi.searchKeywordsPosition(fileInfo);
            if (!positionInfoResult.isSuccess()) {
                log.error("电子签章: 获取文件坐标位置,调用异常[{}]", positionInfoResult.getMessage());
                throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_ERROR);
            }
            log.info("电子签章: 获取文件坐标位置,返回值[{}]", JSON.toJSONString(positionInfoResult));
            positionInfo = positionInfoResult.getData();
        } catch (Exception e) {
            log.error("电子签章: 获取文件坐标位置,异常信息", e);
            throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_ERROR);
        }
        return positionInfo;
    }


    /**
     * 发起一步签
     *
     * @param flowDto 流程dto
     */
    private void createFlowOneStep(FlowDto  flowDto) {
        try {
            log.info("电子签章: 发起一步签,入参[{}]", JSON.toJSONString(flowDto));
            RestfulResult<FlowDto> flowDtoResult = electronicSignApi.createFlowOneStep(flowDto);
            if (!flowDtoResult.isSuccess()) {
                log.error("电子签章: 发起一步签,调用异常[{}]", flowDtoResult.getMessage());
                throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_ERROR);
            }
            log.info("电子签章: 发起一步签,返回值[{}]", JSON.toJSONString(flowDtoResult));
        } catch (Exception e) {
            log.error("电子签章: 发起一步签,异常信息", e);
            throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_ERROR);
        }
    }

    /**
     * 发起一步签
     *
     * @param doubleSignFlowDto 流程dto
     */
    private void createFlowOneStepForDoubleCompany(DoubleSignFlowDto  doubleSignFlowDto) {
        try {
            log.info("电子签章: 发起一步签,入参[{}]" , JSON.toJSONString(doubleSignFlowDto));
            RestfulResult<FlowDto> flowDtoResult = electronicSignApi.createFlowOneStepForDoubleCompany(doubleSignFlowDto);
            if (!flowDtoResult.isSuccess()) {
                log.error("电子签章: 发起一步签,调用异常[{}]" , flowDtoResult.getMessage());
                throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_ERROR);
            }
            log.info("电子签章: 发起一步签,返回值[{}]" , JSON.toJSONString(flowDtoResult));
        } catch (Exception e) {
            log.error("电子签章: 发起一步签,异常信息" , e);
            throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_ERROR);
        }
    }

    /**
     * 签署流程撤销 仅撤销全程
     *
     * @param businessInfo 流程dto
     */
    public void revokeSignFlow(BusinessInfo businessInfo) {
        CompletableFuture.runAsync(() -> {
            try {
                businessInfo.setThirdNo(IdUtil.simpleUUID());
                businessInfo.setPlatfromId(32);
                businessInfo.setPort(4);
                log.info("电子签章: 签署流程撤销,入参[{}]", JSON.toJSONString(businessInfo));
                RestfulResult<Boolean> result = electronicSignApi.revokeSignFlow(businessInfo);
                if (!result.isSuccess()) {
                    log.error("电子签章: 签署流程撤销,调用异常[{}]", result.getMessage());
                    throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_ERROR);
                }
                log.info("电子签章: 签署流程撤销,返回值[{}]", JSON.toJSONString(result));
            } catch (Exception e) {
                log.error("电子签章: 签署流程撤销,异常信息", e);
            }
        }, executor);
    }

    /**
     * MQ消费回执，修改本地推送表的消费字段为 消费成功0 或 消费失败1
     *
     * @param callbackDto 回执dto
     */
    public void mqCallback(CallbackDto callbackDto) {
        try {
            log.info("电子签章: MQ消费回执,入参[{}]", JSON.toJSONString(callbackDto));
            RestfulResult<Integer> result = electronicSignApi.mqCallback(callbackDto);
            if (!result.isSuccess()) {
                log.error("电子签章: MQ消费回执,调用异常[{}]", result.getMessage());
                throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_ERROR);
            }
            log.info("电子签章: MQ消费回执,返回值[{}]", JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("电子签章: MQ消费回执,异常信息", e);
            throw new ServiceException(BaseResponseCode.ELECTRONIC_SIGN_ERROR);
        }
    }

    /**
     * e签宝文件存储
     *
     * @param signCallbackDto 消息对象
     */
    public void saveFile2Oss(SignCallbackDto signCallbackDto, AtomicInteger retryCount, String fileName) {
        String fileUrl = signCallbackDto.getFileUrl();
        if (StringUtils.isEmpty(fileUrl)) {
            log.error("电子签章: 消费消息，回传信息中无下载地址！消息:{}", JSON.toJSONString(signCallbackDto));
            throw new ServiceException("电子签章: 消费消息，回传信息中无下载地址！");
        }
        long begin = System.currentTimeMillis();
        HttpURLConnection urlConnection = null;
        InputStream inputStream = null;
        try {
            URL url = new URL(fileUrl);
            urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.setConnectTimeout(15000);
            urlConnection.setReadTimeout(60000);
            urlConnection.connect();
            inputStream = urlConnection.getInputStream();
            String filePath = ossUtilsService.upload2OssForInputStream("pdf", fileName, inputStream);
            long end = System.currentTimeMillis();
            log.info("消费-》oss保存文件地址：{},耗时：{}", filePath, end - begin);
            signCallbackDto.setFileUrl(filePath);
        } catch (Exception e) {
            log.warn("电子签章: 消费消息，保存盖章文件异常：", e);
            if (retryCount.get() < MAX_RETRY_COUNT) {
                log.warn("电子签章:消费保存签章文件,重试次数{},消息{}", retryCount.get(), JSON.toJSONString(signCallbackDto));
                retryCount.incrementAndGet();
                this.saveFile2Oss(signCallbackDto, retryCount, fileName);
            } else {
                log.error("电子签章:消费保存签章文件:{}重试超过3次,异常:{}", JSON.toJSONString(signCallbackDto), e);
                throw new ServiceException("电子签章:消费保存签章文件重试超过3次");
            }
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (urlConnection != null) {
                    urlConnection.disconnect();
                }
            } catch (Exception e) {
                log.error("关闭连接失败", e);
                throw new ServiceException(e.getMessage());
            }
        }

    }

    /**
     * 去除合同空白页
     *
     * @param ssoUrl     合同地址
     * @param retryCount 重试次数
     */
    public String removeBlankPdfPagesAndSaveFile2Oss(String ssoUrl, AtomicInteger retryCount, String fileName) {
        if (StrUtil.isEmpty(ssoUrl)) {
            log.error("返回的空白合同无网络地址！");
            throw new ServiceException("返回的空白合同无网络地址！");
        }
        HttpURLConnection urlConnection = null;
        InputStream inputStream = null;
        String filePath = "";
        try {
            URL url = new URL(ssoUrl);
            urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.setConnectTimeout(15000);
            urlConnection.setReadTimeout(60000);
            urlConnection.connect();
            inputStream = urlConnection.getInputStream();

            // 去除空白页
            ByteArrayOutputStream fileOutputStream = new ByteArrayOutputStream();
            PdfUtil.removeBlankPdfPages(inputStream, fileOutputStream);

            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(fileOutputStream.toByteArray());

            if (StrUtil.isEmpty(fileName)) {
                fileName = UUID.randomUUID().toString();
            }
            filePath = ossUtilsService.upload2OssForInputStream("pdf", fileName, ossHttp + ossUrl + ossFilePath, byteArrayInputStream);
            log.info("消费-》oss保存文件地址：{}", filePath);
            fileOutputStream.close();
            byteArrayInputStream.close();
            return filePath;
        } catch (Exception e) {
            log.warn("电子签章: 空白合同{}，去除空白页异常：{}", ssoUrl, e);
            if (retryCount.get() < MAX_RETRY_COUNT) {
                log.warn("电子签章: 重试次数{},空白合同{}", retryCount.get(), ssoUrl);
                retryCount.incrementAndGet();
                return this.removeBlankPdfPagesAndSaveFile2Oss(ssoUrl, retryCount, null);
            } else {
                log.error("电子签章:空白合同去除空白页:{}重试超过3次,异常:{}", ssoUrl, e);
                throw new ServiceException("电子签章:空白合同去除空白页重试超过3次");
            }
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (urlConnection != null) {
                    urlConnection.disconnect();
                }
            } catch (Exception e) {
                log.error("关闭连接失败", e);
                throw new ServiceException(e.getMessage());
            }
        }
    }
}
