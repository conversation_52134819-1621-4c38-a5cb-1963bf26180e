package com.vedeng.common.validator.goods;

import com.baidu.unbiz.fluentvalidator.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.vedeng.common.constant.goods.GoodsCheckStatusEnum;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.constant.goods.SpuLevelEnum;
import com.vedeng.common.validator.*;
import com.vedeng.goods.command.SkuAddCommand;
import com.vedeng.goods.command.SpuAddCommand;
import com.vedeng.goods.command.SpuSearchCommand;
import com.vedeng.goods.manager.validator.model.SkuValidObject;
import com.vedeng.goods.manager.validator.model.SpuValidObject;
import com.vedeng.goods.manager.validator.SkuBasicValidator;
import com.vedeng.goods.manager.validator.SpuBasicValidator;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.vo.GoodsStorageConditionVo;
import com.vedeng.goods.utils.GoodsParameterUtils;
import com.vedeng.goods.utils.GoodsStorageConditionUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

public class SpuValidate {
	public static Result check(SpuAddCommand spuCommand,GoodsStorageConditionVo goodsStorageConditionVo) {
        Result ret = FluentValidator.checkAll().result(ResultCollectors.toSimple());
		hasEditAuth(spuCommand, ret);

		if(ret.isSuccess()&& GoodsCheckStatusEnum.isPre(spuCommand.getCheckStatus())||GoodsCheckStatusEnum.isApprove(spuCommand.getCheckStatus()) ){
			ret.setIsSuccess(false);
			ret.getErrors().add("审核通过或已经提交审核的商品不能编辑");
            spuCommand.setErrors(ret.getErrors());
            return ret;
		}

		SpuValidObject spuValidObject = new SpuValidObject();
        spuValidObject.setSpuId(spuCommand.getSpuId());
		spuValidObject.setCategoryId(spuCommand.getCategoryId());
		spuValidObject.setBrandId(spuCommand.getBrandId());
		spuValidObject.setSpuType(spuCommand.getSpuType());
		spuValidObject.setShowName(spuCommand.getShowName());
		spuValidObject.setSpuName(spuCommand.getSpuName());
		spuValidObject.setSpecsModel(spuCommand.getSpecsModel());
		spuValidObject.setGoodsLevelNo(spuCommand.getGoodsLevelNo());
		spuValidObject.setGoodsPositionNo(spuCommand.getGoodsPositionNo());

		spuValidObject.setHasRegistrationCert(spuCommand.getHasRegistrationCert());
		spuValidObject.setMedicalInstrumentCatalogIncluded(spuCommand.getMedicalInstrumentCatalogIncluded());

        spuValidObject.setBaseAttributeIds(spuCommand.getBaseAttributeIds()!=null?spuCommand.getBaseAttributeIds():new Integer[0]);

        GoodsStorageConditionUtils.populateStorageCondition(spuValidObject, goodsStorageConditionVo);
		if (spuCommand.getSpuId()==null) {
            spuValidObject.setAssignmentManagerId(spuCommand.getAssignmentManagerId());
            spuValidObject.setAssignmentAssistantId(spuCommand.getAssignmentAssistantId());
		}

		spuValidObject.setWikiHref(spuCommand.getWikiHref());

		Result resultValid = SpuBasicValidator.getInstance().validate(spuValidObject);
        spuCommand.setErrors(resultValid.getErrors());
		return resultValid;
	}

	private static void hasEditAuth(SpuAddCommand spuCommand, Result ret) {
		if(CollectionUtils.isEmpty(ret.getErrors()) ){
			ret.setErrors(Lists.newArrayList());
		}

		if (ret.isSuccess()&&SpuLevelEnum.isTempSpu(spuCommand.getSpuLevel()) && !spuCommand.isHasEditTempAuth()) {
			ret.setIsSuccess(false);
			ret.getErrors().add("权限不足");
		}



		if (ret.isSuccess()&&!SpuLevelEnum.isTempSpu(spuCommand.getSpuLevel()) && !spuCommand.isHasEditAuth()) {
			ret.setIsSuccess(false);
			ret.getErrors().add("权限不足");
		}

	}

	public static Result checkDeleteSpu(SpuSearchCommand spuCommand) {
		Result ret = FluentValidator.checkAll().on(spuCommand.getSpuId(), new NumberValidate("请选择Spu"))
				.on(spuCommand.getDeleteReason(), new MinLengthValidate(10, "最少需要10个字符"))

				.on(spuCommand.getDeleteReason(), new MaxLengthValidate(300, "不能超过300个字符")).doValidate()
				.result(ResultCollectors.toSimple());
		spuCommand.setErrors(ret.getErrors());
		return ret;
	}

	public static Result checkDeleteSku(SpuSearchCommand spuCommand) {
		Result ret = FluentValidator.checkAll().on(spuCommand.getSkuId(), new NumberValidate("请选择Sku"))
				.on(spuCommand.getDeleteReason(), new MinLengthValidate(10, "最少需要10个字符"))
				.on(spuCommand.getDeleteReason(), new MaxLengthValidate(300, "不能超过300个字符")).doValidate()
				.result(ResultCollectors.toSimple());
		spuCommand.setErrors(ret.getErrors());
		return ret;
	}

	public static Result checkCopySku(SkuAddCommand spuCommand) {
		Result ret = FluentValidator.checkAll().on(spuCommand.getSkuId(), new NumberValidate("请选择Sku")).doValidate()
				.result(ResultCollectors.toSimple());
		spuCommand.setErrors(ret.getErrors());
		return ret;
	}

    public static Result checkSku(SkuAddCommand command, CoreSkuGenerate skuGenerate, GoodsStorageConditionVo goodsStorageConditionVo) {
		SkuValidObject skuValidObject = SkuValidObject.createCoreSkuGenerate(skuGenerate);
		skuValidObject.setSkuType(command.getSkuType());
		GoodsStorageConditionUtils.populateStorageCondition(skuValidObject, goodsStorageConditionVo);

		//SKU属于器械时
		if (GoodsConstants.SKU_TYPE_INSTRUMENT == command.getSkuType()) {
			String technicalParameter = GoodsParameterUtils.paramArrayToString(command.getParamsName1(), command.getParamsValue1());
			skuValidObject.setTechnicalParameter(technicalParameter);
		}

		skuValidObject.setBaseAttributeValueId(command.getBaseAttributeValueId()!=null ? command.getBaseAttributeValueId():new Integer[0]);
        Result resultValid = SkuBasicValidator.getInstance().validate(skuValidObject);
        command.setErrors(resultValid.getErrors());
		return resultValid;
    }

	public static Result checkCheckSpu(SpuAddCommand spuCommand) {
		FluentValidator validate=FluentValidator.checkAll();
		validate.on(spuCommand.getSpuId(), new NumberValidate("SpuId不存在"))
				.on(spuCommand.getLastCheckReason(),new MinLengthValidate(10,"最少10个字符"))
				.when(GoodsCheckStatusEnum.isReject(spuCommand.getCheckStatus()) )
				.on(spuCommand.getLastCheckReason(),new MaxLengthValidate(300,"最多300个字符"))
				.when(GoodsCheckStatusEnum.isReject(spuCommand.getCheckStatus())) ;
		Result ret = validate.doValidate().result(ResultCollectors.toSimple());
		if(ret.isSuccess()&&GoodsCheckStatusEnum.isNew(spuCommand.getCheckStatus())||GoodsCheckStatusEnum.isReject(spuCommand.getCheckStatus())){
			ret.setIsSuccess(false);
			ret.getErrors().add("待完善或者审核不通过的商品不能审核");
		}
		spuCommand.setErrors(ret.getErrors());
		return ret;
	}

	public static Result checkTempSku(SkuAddCommand spuCommand ) {
		FluentValidator validate=FluentValidator.checkAll();
		validate.on(spuCommand.getSpuId(), new NumberValidate("SpuId不存在")
				 ).on(spuCommand.getSkuInfo(),new NullValidate("制造商型号或者规格为必填"))
		;
		Result ret = validate.doValidate().result(ResultCollectors.toSimple());

		spuCommand.setErrors(ret.getErrors());
		return ret;
	}

	public static Result checkCheckSku(SkuAddCommand command) {
		FluentValidator validate=FluentValidator.checkAll();
		validate.on(command.getSkuId(), new NumberValidate("SkuId不存在")
		) ;
		Result ret = validate.doValidate().result(ResultCollectors.toSimple());
		command.setErrors(ret.getErrors());
		return ret;
	}

    public static Result checkBackupSku(SkuAddCommand command) {
		FluentValidator validate=FluentValidator.checkAll();
		validate
				.on(command.getSkuIds(), new NullValidate("SkuId不存在"))
				.on(command.getHasBackupMachine(), new NullValidate("备货状态不能为空")) ;
		Result ret = validate.doValidate().result(ResultCollectors.toSimple());
		command.setErrors(ret.getErrors());
		return ret;
    }
}
