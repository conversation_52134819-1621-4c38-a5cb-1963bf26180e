package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeInstallServiceRecordDto;
import com.vedeng.erp.kingdee.service.KingDeeInstallServiceRecordApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 售后安调单
 * @date 2023/02/20 15:55
 */
@Service
@Slf4j
public class BatchInstallServiceRecordWriter extends BaseWriter<KingDeeInstallServiceRecordDto> {

    @Autowired
    private KingDeeInstallServiceRecordApiService kingDeeInstallServiceRecordApiService;

    @Override
    public void doWrite(KingDeeInstallServiceRecordDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("BatchInstallServiceRecordWriter write:{}", JSON.toJSONString(dto));
        // 改造优化
        dto.setKingDeeBizEnums(KingDeeBizEnums.saveInstallServiceRecord);
        kingDeeInstallServiceRecordApiService.register(dto,true);
    }


}
