package com.vedeng.erp.broadcast.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.broadcast.domain.dto.BroadcastContentConfigDto;
import com.vedeng.erp.broadcast.domain.entity.BroadcastContentConfigEntity;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * 播报内容配置转换器
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BroadcastContentConfigConvertor extends BaseMapStruct<BroadcastContentConfigEntity, BroadcastContentConfigDto> {
}
