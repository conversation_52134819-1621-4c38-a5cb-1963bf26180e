package com.vedeng.goods.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CoreSpuSearchGenerateExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public CoreSpuSearchGenerateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSpuIdIsNull() {
            addCriterion("SPU_ID is null");
            return (Criteria) this;
        }

        public Criteria andSpuIdIsNotNull() {
            addCriterion("SPU_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSpuIdEqualTo(Integer value) {
            addCriterion("SPU_ID =", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotEqualTo(Integer value) {
            addCriterion("SPU_ID <>", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThan(Integer value) {
            addCriterion("SPU_ID >", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SPU_ID >=", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThan(Integer value) {
            addCriterion("SPU_ID <", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThanOrEqualTo(Integer value) {
            addCriterion("SPU_ID <=", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdIn(List<Integer> values) {
            addCriterion("SPU_ID in", values, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotIn(List<Integer> values) {
            addCriterion("SPU_ID not in", values, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdBetween(Integer value1, Integer value2) {
            addCriterion("SPU_ID between", value1, value2, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SPU_ID not between", value1, value2, "spuId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("CATEGORY_ID is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("CATEGORY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Integer value) {
            addCriterion("CATEGORY_ID =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Integer value) {
            addCriterion("CATEGORY_ID <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Integer value) {
            addCriterion("CATEGORY_ID >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("CATEGORY_ID >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Integer value) {
            addCriterion("CATEGORY_ID <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("CATEGORY_ID <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Integer> values) {
            addCriterion("CATEGORY_ID in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Integer> values) {
            addCriterion("CATEGORY_ID not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("CATEGORY_ID between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("CATEGORY_ID not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andBrandIdIsNull() {
            addCriterion("BRAND_ID is null");
            return (Criteria) this;
        }

        public Criteria andBrandIdIsNotNull() {
            addCriterion("BRAND_ID is not null");
            return (Criteria) this;
        }

        public Criteria andBrandIdEqualTo(Integer value) {
            addCriterion("BRAND_ID =", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdNotEqualTo(Integer value) {
            addCriterion("BRAND_ID <>", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdGreaterThan(Integer value) {
            addCriterion("BRAND_ID >", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("BRAND_ID >=", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdLessThan(Integer value) {
            addCriterion("BRAND_ID <", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdLessThanOrEqualTo(Integer value) {
            addCriterion("BRAND_ID <=", value, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdIn(List<Integer> values) {
            addCriterion("BRAND_ID in", values, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdNotIn(List<Integer> values) {
            addCriterion("BRAND_ID not in", values, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdBetween(Integer value1, Integer value2) {
            addCriterion("BRAND_ID between", value1, value2, "brandId");
            return (Criteria) this;
        }

        public Criteria andBrandIdNotBetween(Integer value1, Integer value2) {
            addCriterion("BRAND_ID not between", value1, value2, "brandId");
            return (Criteria) this;
        }

        public Criteria andSpuNoIsNull() {
            addCriterion("SPU_NO is null");
            return (Criteria) this;
        }

        public Criteria andSpuNoIsNotNull() {
            addCriterion("SPU_NO is not null");
            return (Criteria) this;
        }

        public Criteria andSpuNoEqualTo(String value) {
            addCriterion("SPU_NO =", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoNotEqualTo(String value) {
            addCriterion("SPU_NO <>", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoGreaterThan(String value) {
            addCriterion("SPU_NO >", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoGreaterThanOrEqualTo(String value) {
            addCriterion("SPU_NO >=", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoLessThan(String value) {
            addCriterion("SPU_NO <", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoLessThanOrEqualTo(String value) {
            addCriterion("SPU_NO <=", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoLike(String value) {
            addCriterion("SPU_NO like", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoNotLike(String value) {
            addCriterion("SPU_NO not like", value, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoIn(List<String> values) {
            addCriterion("SPU_NO in", values, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoNotIn(List<String> values) {
            addCriterion("SPU_NO not in", values, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoBetween(String value1, String value2) {
            addCriterion("SPU_NO between", value1, value2, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNoNotBetween(String value1, String value2) {
            addCriterion("SPU_NO not between", value1, value2, "spuNo");
            return (Criteria) this;
        }

        public Criteria andSpuNameIsNull() {
            addCriterion("SPU_NAME is null");
            return (Criteria) this;
        }

        public Criteria andSpuNameIsNotNull() {
            addCriterion("SPU_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andSpuNameEqualTo(String value) {
            addCriterion("SPU_NAME =", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameNotEqualTo(String value) {
            addCriterion("SPU_NAME <>", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameGreaterThan(String value) {
            addCriterion("SPU_NAME >", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameGreaterThanOrEqualTo(String value) {
            addCriterion("SPU_NAME >=", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameLessThan(String value) {
            addCriterion("SPU_NAME <", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameLessThanOrEqualTo(String value) {
            addCriterion("SPU_NAME <=", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameLike(String value) {
            addCriterion("SPU_NAME like", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameNotLike(String value) {
            addCriterion("SPU_NAME not like", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameIn(List<String> values) {
            addCriterion("SPU_NAME in", values, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameNotIn(List<String> values) {
            addCriterion("SPU_NAME not in", values, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameBetween(String value1, String value2) {
            addCriterion("SPU_NAME between", value1, value2, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameNotBetween(String value1, String value2) {
            addCriterion("SPU_NAME not between", value1, value2, "spuName");
            return (Criteria) this;
        }

        public Criteria andShowNameIsNull() {
            addCriterion("SHOW_NAME is null");
            return (Criteria) this;
        }

        public Criteria andShowNameIsNotNull() {
            addCriterion("SHOW_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andShowNameEqualTo(String value) {
            addCriterion("SHOW_NAME =", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameNotEqualTo(String value) {
            addCriterion("SHOW_NAME <>", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameGreaterThan(String value) {
            addCriterion("SHOW_NAME >", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameGreaterThanOrEqualTo(String value) {
            addCriterion("SHOW_NAME >=", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameLessThan(String value) {
            addCriterion("SHOW_NAME <", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameLessThanOrEqualTo(String value) {
            addCriterion("SHOW_NAME <=", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameLike(String value) {
            addCriterion("SHOW_NAME like", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameNotLike(String value) {
            addCriterion("SHOW_NAME not like", value, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameIn(List<String> values) {
            addCriterion("SHOW_NAME in", values, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameNotIn(List<String> values) {
            addCriterion("SHOW_NAME not in", values, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameBetween(String value1, String value2) {
            addCriterion("SHOW_NAME between", value1, value2, "showName");
            return (Criteria) this;
        }

        public Criteria andShowNameNotBetween(String value1, String value2) {
            addCriterion("SHOW_NAME not between", value1, value2, "showName");
            return (Criteria) this;
        }

        public Criteria andSpuLevelIsNull() {
            addCriterion("SPU_LEVEL is null");
            return (Criteria) this;
        }

        public Criteria andSpuLevelIsNotNull() {
            addCriterion("SPU_LEVEL is not null");
            return (Criteria) this;
        }

        public Criteria andSpuLevelEqualTo(Integer value) {
            addCriterion("SPU_LEVEL =", value, "spuLevel");
            return (Criteria) this;
        }

        public Criteria andSpuLevelNotEqualTo(Integer value) {
            addCriterion("SPU_LEVEL <>", value, "spuLevel");
            return (Criteria) this;
        }

        public Criteria andSpuLevelGreaterThan(Integer value) {
            addCriterion("SPU_LEVEL >", value, "spuLevel");
            return (Criteria) this;
        }

        public Criteria andSpuLevelGreaterThanOrEqualTo(Integer value) {
            addCriterion("SPU_LEVEL >=", value, "spuLevel");
            return (Criteria) this;
        }

        public Criteria andSpuLevelLessThan(Integer value) {
            addCriterion("SPU_LEVEL <", value, "spuLevel");
            return (Criteria) this;
        }

        public Criteria andSpuLevelLessThanOrEqualTo(Integer value) {
            addCriterion("SPU_LEVEL <=", value, "spuLevel");
            return (Criteria) this;
        }

        public Criteria andSpuLevelIn(List<Integer> values) {
            addCriterion("SPU_LEVEL in", values, "spuLevel");
            return (Criteria) this;
        }

        public Criteria andSpuLevelNotIn(List<Integer> values) {
            addCriterion("SPU_LEVEL not in", values, "spuLevel");
            return (Criteria) this;
        }

        public Criteria andSpuLevelBetween(Integer value1, Integer value2) {
            addCriterion("SPU_LEVEL between", value1, value2, "spuLevel");
            return (Criteria) this;
        }

        public Criteria andSpuLevelNotBetween(Integer value1, Integer value2) {
            addCriterion("SPU_LEVEL not between", value1, value2, "spuLevel");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`STATUS` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`STATUS` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`STATUS` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`STATUS` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`STATUS` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`STATUS` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`STATUS` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`STATUS` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`STATUS` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`STATUS` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`STATUS` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`STATUS` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andSpuTypeIsNull() {
            addCriterion("SPU_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andSpuTypeIsNotNull() {
            addCriterion("SPU_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andSpuTypeEqualTo(Integer value) {
            addCriterion("SPU_TYPE =", value, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeNotEqualTo(Integer value) {
            addCriterion("SPU_TYPE <>", value, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeGreaterThan(Integer value) {
            addCriterion("SPU_TYPE >", value, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("SPU_TYPE >=", value, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeLessThan(Integer value) {
            addCriterion("SPU_TYPE <", value, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeLessThanOrEqualTo(Integer value) {
            addCriterion("SPU_TYPE <=", value, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeIn(List<Integer> values) {
            addCriterion("SPU_TYPE in", values, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeNotIn(List<Integer> values) {
            addCriterion("SPU_TYPE not in", values, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeBetween(Integer value1, Integer value2) {
            addCriterion("SPU_TYPE between", value1, value2, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("SPU_TYPE not between", value1, value2, "spuType");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdIsNull() {
            addCriterion("FIRST_ENGAGE_ID is null");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdIsNotNull() {
            addCriterion("FIRST_ENGAGE_ID is not null");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdEqualTo(Integer value) {
            addCriterion("FIRST_ENGAGE_ID =", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdNotEqualTo(Integer value) {
            addCriterion("FIRST_ENGAGE_ID <>", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdGreaterThan(Integer value) {
            addCriterion("FIRST_ENGAGE_ID >", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("FIRST_ENGAGE_ID >=", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdLessThan(Integer value) {
            addCriterion("FIRST_ENGAGE_ID <", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdLessThanOrEqualTo(Integer value) {
            addCriterion("FIRST_ENGAGE_ID <=", value, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdIn(List<Integer> values) {
            addCriterion("FIRST_ENGAGE_ID in", values, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdNotIn(List<Integer> values) {
            addCriterion("FIRST_ENGAGE_ID not in", values, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdBetween(Integer value1, Integer value2) {
            addCriterion("FIRST_ENGAGE_ID between", value1, value2, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andFirstEngageIdNotBetween(Integer value1, Integer value2) {
            addCriterion("FIRST_ENGAGE_ID not between", value1, value2, "firstEngageId");
            return (Criteria) this;
        }

        public Criteria andRegistrationIconIsNull() {
            addCriterion("REGISTRATION_ICON is null");
            return (Criteria) this;
        }

        public Criteria andRegistrationIconIsNotNull() {
            addCriterion("REGISTRATION_ICON is not null");
            return (Criteria) this;
        }

        public Criteria andRegistrationIconEqualTo(String value) {
            addCriterion("REGISTRATION_ICON =", value, "registrationIcon");
            return (Criteria) this;
        }

        public Criteria andRegistrationIconNotEqualTo(String value) {
            addCriterion("REGISTRATION_ICON <>", value, "registrationIcon");
            return (Criteria) this;
        }

        public Criteria andRegistrationIconGreaterThan(String value) {
            addCriterion("REGISTRATION_ICON >", value, "registrationIcon");
            return (Criteria) this;
        }

        public Criteria andRegistrationIconGreaterThanOrEqualTo(String value) {
            addCriterion("REGISTRATION_ICON >=", value, "registrationIcon");
            return (Criteria) this;
        }

        public Criteria andRegistrationIconLessThan(String value) {
            addCriterion("REGISTRATION_ICON <", value, "registrationIcon");
            return (Criteria) this;
        }

        public Criteria andRegistrationIconLessThanOrEqualTo(String value) {
            addCriterion("REGISTRATION_ICON <=", value, "registrationIcon");
            return (Criteria) this;
        }

        public Criteria andRegistrationIconLike(String value) {
            addCriterion("REGISTRATION_ICON like", value, "registrationIcon");
            return (Criteria) this;
        }

        public Criteria andRegistrationIconNotLike(String value) {
            addCriterion("REGISTRATION_ICON not like", value, "registrationIcon");
            return (Criteria) this;
        }

        public Criteria andRegistrationIconIn(List<String> values) {
            addCriterion("REGISTRATION_ICON in", values, "registrationIcon");
            return (Criteria) this;
        }

        public Criteria andRegistrationIconNotIn(List<String> values) {
            addCriterion("REGISTRATION_ICON not in", values, "registrationIcon");
            return (Criteria) this;
        }

        public Criteria andRegistrationIconBetween(String value1, String value2) {
            addCriterion("REGISTRATION_ICON between", value1, value2, "registrationIcon");
            return (Criteria) this;
        }

        public Criteria andRegistrationIconNotBetween(String value1, String value2) {
            addCriterion("REGISTRATION_ICON not between", value1, value2, "registrationIcon");
            return (Criteria) this;
        }

        public Criteria andWikiHrefIsNull() {
            addCriterion("WIKI_HREF is null");
            return (Criteria) this;
        }

        public Criteria andWikiHrefIsNotNull() {
            addCriterion("WIKI_HREF is not null");
            return (Criteria) this;
        }

        public Criteria andWikiHrefEqualTo(String value) {
            addCriterion("WIKI_HREF =", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefNotEqualTo(String value) {
            addCriterion("WIKI_HREF <>", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefGreaterThan(String value) {
            addCriterion("WIKI_HREF >", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefGreaterThanOrEqualTo(String value) {
            addCriterion("WIKI_HREF >=", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefLessThan(String value) {
            addCriterion("WIKI_HREF <", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefLessThanOrEqualTo(String value) {
            addCriterion("WIKI_HREF <=", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefLike(String value) {
            addCriterion("WIKI_HREF like", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefNotLike(String value) {
            addCriterion("WIKI_HREF not like", value, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefIn(List<String> values) {
            addCriterion("WIKI_HREF in", values, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefNotIn(List<String> values) {
            addCriterion("WIKI_HREF not in", values, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefBetween(String value1, String value2) {
            addCriterion("WIKI_HREF between", value1, value2, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andWikiHrefNotBetween(String value1, String value2) {
            addCriterion("WIKI_HREF not between", value1, value2, "wikiHref");
            return (Criteria) this;
        }

        public Criteria andOperateInfoFlagIsNull() {
            addCriterion("OPERATE_INFO_FLAG is null");
            return (Criteria) this;
        }

        public Criteria andOperateInfoFlagIsNotNull() {
            addCriterion("OPERATE_INFO_FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andOperateInfoFlagEqualTo(Integer value) {
            addCriterion("OPERATE_INFO_FLAG =", value, "operateInfoFlag");
            return (Criteria) this;
        }

        public Criteria andOperateInfoFlagNotEqualTo(Integer value) {
            addCriterion("OPERATE_INFO_FLAG <>", value, "operateInfoFlag");
            return (Criteria) this;
        }

        public Criteria andOperateInfoFlagGreaterThan(Integer value) {
            addCriterion("OPERATE_INFO_FLAG >", value, "operateInfoFlag");
            return (Criteria) this;
        }

        public Criteria andOperateInfoFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("OPERATE_INFO_FLAG >=", value, "operateInfoFlag");
            return (Criteria) this;
        }

        public Criteria andOperateInfoFlagLessThan(Integer value) {
            addCriterion("OPERATE_INFO_FLAG <", value, "operateInfoFlag");
            return (Criteria) this;
        }

        public Criteria andOperateInfoFlagLessThanOrEqualTo(Integer value) {
            addCriterion("OPERATE_INFO_FLAG <=", value, "operateInfoFlag");
            return (Criteria) this;
        }

        public Criteria andOperateInfoFlagIn(List<Integer> values) {
            addCriterion("OPERATE_INFO_FLAG in", values, "operateInfoFlag");
            return (Criteria) this;
        }

        public Criteria andOperateInfoFlagNotIn(List<Integer> values) {
            addCriterion("OPERATE_INFO_FLAG not in", values, "operateInfoFlag");
            return (Criteria) this;
        }

        public Criteria andOperateInfoFlagBetween(Integer value1, Integer value2) {
            addCriterion("OPERATE_INFO_FLAG between", value1, value2, "operateInfoFlag");
            return (Criteria) this;
        }

        public Criteria andOperateInfoFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("OPERATE_INFO_FLAG not between", value1, value2, "operateInfoFlag");
            return (Criteria) this;
        }

        public Criteria andCheckStatusIsNull() {
            addCriterion("CHECK_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andCheckStatusIsNotNull() {
            addCriterion("CHECK_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andCheckStatusEqualTo(Integer value) {
            addCriterion("CHECK_STATUS =", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusNotEqualTo(Integer value) {
            addCriterion("CHECK_STATUS <>", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusGreaterThan(Integer value) {
            addCriterion("CHECK_STATUS >", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("CHECK_STATUS >=", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusLessThan(Integer value) {
            addCriterion("CHECK_STATUS <", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusLessThanOrEqualTo(Integer value) {
            addCriterion("CHECK_STATUS <=", value, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusIn(List<Integer> values) {
            addCriterion("CHECK_STATUS in", values, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusNotIn(List<Integer> values) {
            addCriterion("CHECK_STATUS not in", values, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusBetween(Integer value1, Integer value2) {
            addCriterion("CHECK_STATUS between", value1, value2, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andCheckStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("CHECK_STATUS not between", value1, value2, "checkStatus");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdIsNull() {
            addCriterion("OPERATE_INFO_ID is null");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdIsNotNull() {
            addCriterion("OPERATE_INFO_ID is not null");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdEqualTo(Integer value) {
            addCriterion("OPERATE_INFO_ID =", value, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdNotEqualTo(Integer value) {
            addCriterion("OPERATE_INFO_ID <>", value, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdGreaterThan(Integer value) {
            addCriterion("OPERATE_INFO_ID >", value, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("OPERATE_INFO_ID >=", value, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdLessThan(Integer value) {
            addCriterion("OPERATE_INFO_ID <", value, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdLessThanOrEqualTo(Integer value) {
            addCriterion("OPERATE_INFO_ID <=", value, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdIn(List<Integer> values) {
            addCriterion("OPERATE_INFO_ID in", values, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdNotIn(List<Integer> values) {
            addCriterion("OPERATE_INFO_ID not in", values, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdBetween(Integer value1, Integer value2) {
            addCriterion("OPERATE_INFO_ID between", value1, value2, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andOperateInfoIdNotBetween(Integer value1, Integer value2) {
            addCriterion("OPERATE_INFO_ID not between", value1, value2, "operateInfoId");
            return (Criteria) this;
        }

        public Criteria andHospitalTagsIsNull() {
            addCriterion("HOSPITAL_TAGS is null");
            return (Criteria) this;
        }

        public Criteria andHospitalTagsIsNotNull() {
            addCriterion("HOSPITAL_TAGS is not null");
            return (Criteria) this;
        }

        public Criteria andHospitalTagsEqualTo(String value) {
            addCriterion("HOSPITAL_TAGS =", value, "hospitalTags");
            return (Criteria) this;
        }

        public Criteria andHospitalTagsNotEqualTo(String value) {
            addCriterion("HOSPITAL_TAGS <>", value, "hospitalTags");
            return (Criteria) this;
        }

        public Criteria andHospitalTagsGreaterThan(String value) {
            addCriterion("HOSPITAL_TAGS >", value, "hospitalTags");
            return (Criteria) this;
        }

        public Criteria andHospitalTagsGreaterThanOrEqualTo(String value) {
            addCriterion("HOSPITAL_TAGS >=", value, "hospitalTags");
            return (Criteria) this;
        }

        public Criteria andHospitalTagsLessThan(String value) {
            addCriterion("HOSPITAL_TAGS <", value, "hospitalTags");
            return (Criteria) this;
        }

        public Criteria andHospitalTagsLessThanOrEqualTo(String value) {
            addCriterion("HOSPITAL_TAGS <=", value, "hospitalTags");
            return (Criteria) this;
        }

        public Criteria andHospitalTagsLike(String value) {
            addCriterion("HOSPITAL_TAGS like", value, "hospitalTags");
            return (Criteria) this;
        }

        public Criteria andHospitalTagsNotLike(String value) {
            addCriterion("HOSPITAL_TAGS not like", value, "hospitalTags");
            return (Criteria) this;
        }

        public Criteria andHospitalTagsIn(List<String> values) {
            addCriterion("HOSPITAL_TAGS in", values, "hospitalTags");
            return (Criteria) this;
        }

        public Criteria andHospitalTagsNotIn(List<String> values) {
            addCriterion("HOSPITAL_TAGS not in", values, "hospitalTags");
            return (Criteria) this;
        }

        public Criteria andHospitalTagsBetween(String value1, String value2) {
            addCriterion("HOSPITAL_TAGS between", value1, value2, "hospitalTags");
            return (Criteria) this;
        }

        public Criteria andHospitalTagsNotBetween(String value1, String value2) {
            addCriterion("HOSPITAL_TAGS not between", value1, value2, "hospitalTags");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Date value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Date value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Date value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Date value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Date> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Date> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andCheckTimeIsNull() {
            addCriterion("CHECK_TIME is null");
            return (Criteria) this;
        }

        public Criteria andCheckTimeIsNotNull() {
            addCriterion("CHECK_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andCheckTimeEqualTo(Date value) {
            addCriterion("CHECK_TIME =", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeNotEqualTo(Date value) {
            addCriterion("CHECK_TIME <>", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeGreaterThan(Date value) {
            addCriterion("CHECK_TIME >", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("CHECK_TIME >=", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeLessThan(Date value) {
            addCriterion("CHECK_TIME <", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeLessThanOrEqualTo(Date value) {
            addCriterion("CHECK_TIME <=", value, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeIn(List<Date> values) {
            addCriterion("CHECK_TIME in", values, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeNotIn(List<Date> values) {
            addCriterion("CHECK_TIME not in", values, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeBetween(Date value1, Date value2) {
            addCriterion("CHECK_TIME between", value1, value2, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckTimeNotBetween(Date value1, Date value2) {
            addCriterion("CHECK_TIME not between", value1, value2, "checkTime");
            return (Criteria) this;
        }

        public Criteria andCheckerIsNull() {
            addCriterion("CHECKER is null");
            return (Criteria) this;
        }

        public Criteria andCheckerIsNotNull() {
            addCriterion("CHECKER is not null");
            return (Criteria) this;
        }

        public Criteria andCheckerEqualTo(Integer value) {
            addCriterion("CHECKER =", value, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerNotEqualTo(Integer value) {
            addCriterion("CHECKER <>", value, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerGreaterThan(Integer value) {
            addCriterion("CHECKER >", value, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerGreaterThanOrEqualTo(Integer value) {
            addCriterion("CHECKER >=", value, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerLessThan(Integer value) {
            addCriterion("CHECKER <", value, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerLessThanOrEqualTo(Integer value) {
            addCriterion("CHECKER <=", value, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerIn(List<Integer> values) {
            addCriterion("CHECKER in", values, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerNotIn(List<Integer> values) {
            addCriterion("CHECKER not in", values, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerBetween(Integer value1, Integer value2) {
            addCriterion("CHECKER between", value1, value2, "checker");
            return (Criteria) this;
        }

        public Criteria andCheckerNotBetween(Integer value1, Integer value2) {
            addCriterion("CHECKER not between", value1, value2, "checker");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonIsNull() {
            addCriterion("DELETE_REASON is null");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonIsNotNull() {
            addCriterion("DELETE_REASON is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonEqualTo(String value) {
            addCriterion("DELETE_REASON =", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonNotEqualTo(String value) {
            addCriterion("DELETE_REASON <>", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonGreaterThan(String value) {
            addCriterion("DELETE_REASON >", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonGreaterThanOrEqualTo(String value) {
            addCriterion("DELETE_REASON >=", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonLessThan(String value) {
            addCriterion("DELETE_REASON <", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonLessThanOrEqualTo(String value) {
            addCriterion("DELETE_REASON <=", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonLike(String value) {
            addCriterion("DELETE_REASON like", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonNotLike(String value) {
            addCriterion("DELETE_REASON not like", value, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonIn(List<String> values) {
            addCriterion("DELETE_REASON in", values, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonNotIn(List<String> values) {
            addCriterion("DELETE_REASON not in", values, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonBetween(String value1, String value2) {
            addCriterion("DELETE_REASON between", value1, value2, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andDeleteReasonNotBetween(String value1, String value2) {
            addCriterion("DELETE_REASON not between", value1, value2, "deleteReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonIsNull() {
            addCriterion("LAST_CHECK_REASON is null");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonIsNotNull() {
            addCriterion("LAST_CHECK_REASON is not null");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonEqualTo(String value) {
            addCriterion("LAST_CHECK_REASON =", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonNotEqualTo(String value) {
            addCriterion("LAST_CHECK_REASON <>", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonGreaterThan(String value) {
            addCriterion("LAST_CHECK_REASON >", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonGreaterThanOrEqualTo(String value) {
            addCriterion("LAST_CHECK_REASON >=", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonLessThan(String value) {
            addCriterion("LAST_CHECK_REASON <", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonLessThanOrEqualTo(String value) {
            addCriterion("LAST_CHECK_REASON <=", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonLike(String value) {
            addCriterion("LAST_CHECK_REASON like", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonNotLike(String value) {
            addCriterion("LAST_CHECK_REASON not like", value, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonIn(List<String> values) {
            addCriterion("LAST_CHECK_REASON in", values, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonNotIn(List<String> values) {
            addCriterion("LAST_CHECK_REASON not in", values, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonBetween(String value1, String value2) {
            addCriterion("LAST_CHECK_REASON between", value1, value2, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andLastCheckReasonNotBetween(String value1, String value2) {
            addCriterion("LAST_CHECK_REASON not between", value1, value2, "lastCheckReason");
            return (Criteria) this;
        }

        public Criteria andAssignmentManagerIdIsNull() {
            addCriterion("ASSIGNMENT_MANAGER_ID is null");
            return (Criteria) this;
        }

        public Criteria andAssignmentManagerIdIsNotNull() {
            addCriterion("ASSIGNMENT_MANAGER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andAssignmentManagerIdEqualTo(Integer value) {
            addCriterion("ASSIGNMENT_MANAGER_ID =", value, "assignmentManagerId");
            return (Criteria) this;
        }

        public Criteria andAssignmentManagerIdNotEqualTo(Integer value) {
            addCriterion("ASSIGNMENT_MANAGER_ID <>", value, "assignmentManagerId");
            return (Criteria) this;
        }

        public Criteria andAssignmentManagerIdGreaterThan(Integer value) {
            addCriterion("ASSIGNMENT_MANAGER_ID >", value, "assignmentManagerId");
            return (Criteria) this;
        }

        public Criteria andAssignmentManagerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ASSIGNMENT_MANAGER_ID >=", value, "assignmentManagerId");
            return (Criteria) this;
        }

        public Criteria andAssignmentManagerIdLessThan(Integer value) {
            addCriterion("ASSIGNMENT_MANAGER_ID <", value, "assignmentManagerId");
            return (Criteria) this;
        }

        public Criteria andAssignmentManagerIdLessThanOrEqualTo(Integer value) {
            addCriterion("ASSIGNMENT_MANAGER_ID <=", value, "assignmentManagerId");
            return (Criteria) this;
        }

        public Criteria andAssignmentManagerIdIn(List<Integer> values) {
            addCriterion("ASSIGNMENT_MANAGER_ID in", values, "assignmentManagerId");
            return (Criteria) this;
        }

        public Criteria andAssignmentManagerIdNotIn(List<Integer> values) {
            addCriterion("ASSIGNMENT_MANAGER_ID not in", values, "assignmentManagerId");
            return (Criteria) this;
        }

        public Criteria andAssignmentManagerIdBetween(Integer value1, Integer value2) {
            addCriterion("ASSIGNMENT_MANAGER_ID between", value1, value2, "assignmentManagerId");
            return (Criteria) this;
        }

        public Criteria andAssignmentManagerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ASSIGNMENT_MANAGER_ID not between", value1, value2, "assignmentManagerId");
            return (Criteria) this;
        }

        public Criteria andAssignmentAssistantIdIsNull() {
            addCriterion("ASSIGNMENT_ASSISTANT_ID is null");
            return (Criteria) this;
        }

        public Criteria andAssignmentAssistantIdIsNotNull() {
            addCriterion("ASSIGNMENT_ASSISTANT_ID is not null");
            return (Criteria) this;
        }

        public Criteria andAssignmentAssistantIdEqualTo(Integer value) {
            addCriterion("ASSIGNMENT_ASSISTANT_ID =", value, "assignmentAssistantId");
            return (Criteria) this;
        }

        public Criteria andAssignmentAssistantIdNotEqualTo(Integer value) {
            addCriterion("ASSIGNMENT_ASSISTANT_ID <>", value, "assignmentAssistantId");
            return (Criteria) this;
        }

        public Criteria andAssignmentAssistantIdGreaterThan(Integer value) {
            addCriterion("ASSIGNMENT_ASSISTANT_ID >", value, "assignmentAssistantId");
            return (Criteria) this;
        }

        public Criteria andAssignmentAssistantIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ASSIGNMENT_ASSISTANT_ID >=", value, "assignmentAssistantId");
            return (Criteria) this;
        }

        public Criteria andAssignmentAssistantIdLessThan(Integer value) {
            addCriterion("ASSIGNMENT_ASSISTANT_ID <", value, "assignmentAssistantId");
            return (Criteria) this;
        }

        public Criteria andAssignmentAssistantIdLessThanOrEqualTo(Integer value) {
            addCriterion("ASSIGNMENT_ASSISTANT_ID <=", value, "assignmentAssistantId");
            return (Criteria) this;
        }

        public Criteria andAssignmentAssistantIdIn(List<Integer> values) {
            addCriterion("ASSIGNMENT_ASSISTANT_ID in", values, "assignmentAssistantId");
            return (Criteria) this;
        }

        public Criteria andAssignmentAssistantIdNotIn(List<Integer> values) {
            addCriterion("ASSIGNMENT_ASSISTANT_ID not in", values, "assignmentAssistantId");
            return (Criteria) this;
        }

        public Criteria andAssignmentAssistantIdBetween(Integer value1, Integer value2) {
            addCriterion("ASSIGNMENT_ASSISTANT_ID between", value1, value2, "assignmentAssistantId");
            return (Criteria) this;
        }

        public Criteria andAssignmentAssistantIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ASSIGNMENT_ASSISTANT_ID not between", value1, value2, "assignmentAssistantId");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated do_not_delete_during_merge Thu Jun 27 19:06:50 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table V_CORE_SPU_SEARCH
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}