<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newtask.ddi.dao.generate.DdiInstallstionMapper">
  <resultMap id="BaseResultMap" type="com.wms.model.ddi.generate.DdiInstallstion">
    <id column="T_DDI_INSTALLSTION_ID" jdbcType="INTEGER" property="tDdiInstallstionId" />
    <result column="DISTRIBUTOR" jdbcType="VARCHAR" property="distributor" />
    <result column="QUOTE_ID" jdbcType="VARCHAR" property="quoteId" />
    <result column="SALEORDER_NO" jdbcType="VARCHAR" property="saleorderNo" />
    <result column="INSTALLSTION_TIME" jdbcType="TIMESTAMP" property="installstionTime" />
    <result column="GOODS_TYPE" jdbcType="VARCHAR" property="goodsType" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="GOODS_MODEL" jdbcType="VARCHAR" property="goodsModel" />
    <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="SERIAL_NUMBER" jdbcType="VARCHAR" property="serialNumber" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="INSTALLSTION_AERA" jdbcType="VARCHAR" property="installstionAera" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="WARRANTY_START_TIME" jdbcType="TIMESTAMP" property="warrantyStartTime" />
    <result column="WARRANTY_PERIOD" jdbcType="VARCHAR" property="warrantyPeriod" />
    <result column="WARRANTY_END_TIME" jdbcType="TIMESTAMP" property="warrantyEndTime" />
    <result column="ENGINEER_NAME" jdbcType="VARCHAR" property="engineerName" />
    <result column="ENGINEER_PHONE" jdbcType="VARCHAR" property="engineerPhone" />
    <result column="SALER_NAME" jdbcType="VARCHAR" property="salerName" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    T_DDI_INSTALLSTION_ID, DISTRIBUTOR, QUOTE_ID, SALEORDER_NO, INSTALLSTION_TIME, GOODS_TYPE, 
    GOODS_NAME, GOODS_MODEL, REGISTRATION_NUMBER, SERIAL_NUMBER, TRADER_NAME, INSTALLSTION_AERA, 
    NUM, WARRANTY_START_TIME, WARRANTY_PERIOD, WARRANTY_END_TIME, ENGINEER_NAME, ENGINEER_PHONE, 
    SALER_NAME, ADD_TIME, MODE_TIME, IS_DELETE, CREATOR, UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_DDI_INSTALLSTION
    where T_DDI_INSTALLSTION_ID = #{tDdiInstallstionId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_DDI_INSTALLSTION
    where T_DDI_INSTALLSTION_ID = #{tDdiInstallstionId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="T_DDI_INSTALLSTION_ID" keyProperty="tDdiInstallstionId" parameterType="com.wms.model.ddi.DdiInstallstionExtDto" useGeneratedKeys="true">
    insert into T_DDI_INSTALLSTION (DISTRIBUTOR, QUOTE_ID, SALEORDER_NO, 
      INSTALLSTION_TIME, GOODS_TYPE, GOODS_NAME, 
      GOODS_MODEL, REGISTRATION_NUMBER, SERIAL_NUMBER, 
      TRADER_NAME, INSTALLSTION_AERA, NUM, 
      WARRANTY_START_TIME, WARRANTY_PERIOD, WARRANTY_END_TIME, 
      ENGINEER_NAME, ENGINEER_PHONE, SALER_NAME, 
      ADD_TIME, MODE_TIME, IS_DELETE, 
      CREATOR, UPDATER)
    values (#{distributor,jdbcType=VARCHAR}, #{quoteId,jdbcType=VARCHAR}, #{saleorderNo,jdbcType=VARCHAR}, 
      #{installstionTime,jdbcType=TIMESTAMP}, #{goodsType,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, 
      #{goodsModel,jdbcType=VARCHAR}, #{registrationNumber,jdbcType=VARCHAR}, #{serialNumber,jdbcType=VARCHAR}, 
      #{traderName,jdbcType=VARCHAR}, #{installstionAera,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER}, 
      #{warrantyStartTime,jdbcType=TIMESTAMP}, #{warrantyPeriod,jdbcType=VARCHAR}, #{warrantyEndTime,jdbcType=TIMESTAMP}, 
      #{engineerName,jdbcType=VARCHAR}, #{engineerPhone,jdbcType=VARCHAR}, #{salerName,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{modeTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=BOOLEAN}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="T_DDI_INSTALLSTION_ID" keyProperty="tDdiInstallstionId" parameterType="com.wms.model.ddi.DdiInstallstionExtDto" useGeneratedKeys="true">
    insert into T_DDI_INSTALLSTION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="distributor != null">
        DISTRIBUTOR,
      </if>
      <if test="quoteId != null">
        QUOTE_ID,
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO,
      </if>
      <if test="installstionTime != null">
        INSTALLSTION_TIME,
      </if>
      <if test="goodsType != null">
        GOODS_TYPE,
      </if>
      <if test="goodsName != null">
        GOODS_NAME,
      </if>
      <if test="goodsModel != null">
        GOODS_MODEL,
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER,
      </if>
      <if test="serialNumber != null">
        SERIAL_NUMBER,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="installstionAera != null">
        INSTALLSTION_AERA,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="warrantyStartTime != null">
        WARRANTY_START_TIME,
      </if>
      <if test="warrantyPeriod != null">
        WARRANTY_PERIOD,
      </if>
      <if test="warrantyEndTime != null">
        WARRANTY_END_TIME,
      </if>
      <if test="engineerName != null">
        ENGINEER_NAME,
      </if>
      <if test="engineerPhone != null">
        ENGINEER_PHONE,
      </if>
      <if test="salerName != null">
        SALER_NAME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modeTime != null">
        MODE_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="distributor != null">
        #{distributor,jdbcType=VARCHAR},
      </if>
      <if test="quoteId != null">
        #{quoteId,jdbcType=VARCHAR},
      </if>
      <if test="saleorderNo != null">
        #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="installstionTime != null">
        #{installstionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="goodsType != null">
        #{goodsType,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsModel != null">
        #{goodsModel,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="installstionAera != null">
        #{installstionAera,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="warrantyStartTime != null">
        #{warrantyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="warrantyPeriod != null">
        #{warrantyPeriod,jdbcType=VARCHAR},
      </if>
      <if test="warrantyEndTime != null">
        #{warrantyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="engineerName != null">
        #{engineerName,jdbcType=VARCHAR},
      </if>
      <if test="engineerPhone != null">
        #{engineerPhone,jdbcType=VARCHAR},
      </if>
      <if test="salerName != null">
        #{salerName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wms.model.ddi.DdiInstallstionExtDto">
    update T_DDI_INSTALLSTION
    <set>
      <if test="distributor != null">
        DISTRIBUTOR = #{distributor,jdbcType=VARCHAR},
      </if>
      <if test="quoteId != null">
        QUOTE_ID = #{quoteId,jdbcType=VARCHAR},
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="installstionTime != null">
        INSTALLSTION_TIME = #{installstionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="goodsType != null">
        GOODS_TYPE = #{goodsType,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="goodsModel != null">
        GOODS_MODEL = #{goodsModel,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="installstionAera != null">
        INSTALLSTION_AERA = #{installstionAera,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="warrantyStartTime != null">
        WARRANTY_START_TIME = #{warrantyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="warrantyPeriod != null">
        WARRANTY_PERIOD = #{warrantyPeriod,jdbcType=VARCHAR},
      </if>
      <if test="warrantyEndTime != null">
        WARRANTY_END_TIME = #{warrantyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="engineerName != null">
        ENGINEER_NAME = #{engineerName,jdbcType=VARCHAR},
      </if>
      <if test="engineerPhone != null">
        ENGINEER_PHONE = #{engineerPhone,jdbcType=VARCHAR},
      </if>
      <if test="salerName != null">
        SALER_NAME = #{salerName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where T_DDI_INSTALLSTION_ID = #{tDdiInstallstionId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wms.model.ddi.DdiInstallstionExtDto">
    update T_DDI_INSTALLSTION
    set DISTRIBUTOR = #{distributor,jdbcType=VARCHAR},
      QUOTE_ID = #{quoteId,jdbcType=VARCHAR},
      SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      INSTALLSTION_TIME = #{installstionTime,jdbcType=TIMESTAMP},
      GOODS_TYPE = #{goodsType,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      GOODS_MODEL = #{goodsModel,jdbcType=VARCHAR},
      REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      INSTALLSTION_AERA = #{installstionAera,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=INTEGER},
      WARRANTY_START_TIME = #{warrantyStartTime,jdbcType=TIMESTAMP},
      WARRANTY_PERIOD = #{warrantyPeriod,jdbcType=VARCHAR},
      WARRANTY_END_TIME = #{warrantyEndTime,jdbcType=TIMESTAMP},
      ENGINEER_NAME = #{engineerName,jdbcType=VARCHAR},
      ENGINEER_PHONE = #{engineerPhone,jdbcType=VARCHAR},
      SALER_NAME = #{salerName,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER}
    where T_DDI_INSTALLSTION_ID = #{tDdiInstallstionId,jdbcType=INTEGER}
  </update>
</mapper>