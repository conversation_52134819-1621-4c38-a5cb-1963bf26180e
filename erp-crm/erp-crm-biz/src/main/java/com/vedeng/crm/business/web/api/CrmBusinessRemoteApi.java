package com.vedeng.crm.business.web.api;

import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.crm.business.quote.domain.dto.QuoteSyncInfoDto;
import com.vedeng.crm.business.quote.facade.QuoteFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@ExceptionController
@RestController
@RequestMapping("/api/business/")
@Slf4j
public class CrmBusinessRemoteApi {

    @Autowired
    private QuoteFacade quoteFacade;

    @RequestMapping(value = "/queryQuoteDetail", method = RequestMethod.GET)
    public R<QuoteSyncInfoDto> getUserInfo(Integer quoteorderId){
        QuoteSyncInfoDto quoteSyncInfoDto = quoteFacade.queryQuoteDetail(quoteorderId);
        return R.success(quoteSyncInfoDto);
    }

    @RequestMapping(value = "/close", method = RequestMethod.GET)
    public R<QuoteSyncInfoDto> close(Integer quoteorderId){
       quoteFacade.close(quoteorderId);
        return R.success();
    }
}
