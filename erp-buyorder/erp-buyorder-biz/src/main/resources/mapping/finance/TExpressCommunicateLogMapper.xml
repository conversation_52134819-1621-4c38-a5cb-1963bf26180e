<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.TExpressCommunicateLogMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.TExpressCommunicateLog">
    <!--@mbg.generated-->
    <!--@Table T_EXPRESS_COMMUNICATE_LOG-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="ADD_USER_NAME" jdbcType="VARCHAR" property="addUserName" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ADD_USER_NAME, ADD_TIME, MOD_TIME, CREATOR, UPDATER, EXPRESS_ID, CONTENT
  </sql>

  <select id="selectByExpressId" parameterType="java.lang.Integer" resultType="com.vedeng.erp.finance.domain.entity.TExpressCommunicateLog">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_EXPRESS_COMMUNICATE_LOG
    where EXPRESS_ID = #{expressId,jdbcType=INTEGER}
    ORDER BY ID DESC
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_EXPRESS_COMMUNICATE_LOG
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_EXPRESS_COMMUNICATE_LOG
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.finance.domain.entity.TExpressCommunicateLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS_COMMUNICATE_LOG (ADD_USER_NAME, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, EXPRESS_ID, 
      CONTENT)
    values (#{addUserName,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{expressId,jdbcType=INTEGER}, 
      #{content,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.finance.domain.entity.TExpressCommunicateLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS_COMMUNICATE_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addUserName != null">
        ADD_USER_NAME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      <if test="content != null">
        CONTENT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addUserName != null">
        #{addUserName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.TExpressCommunicateLog">
    <!--@mbg.generated-->
    update T_EXPRESS_COMMUNICATE_LOG
    <set>
      <if test="addUserName != null">
        ADD_USER_NAME = #{addUserName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.TExpressCommunicateLog">
    <!--@mbg.generated-->
    update T_EXPRESS_COMMUNICATE_LOG
    set ADD_USER_NAME = #{addUserName,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      CONTENT = #{content,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
</mapper>