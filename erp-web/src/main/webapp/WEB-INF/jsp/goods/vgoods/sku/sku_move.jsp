<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>
        迁移SKU
    </title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/suggestSelect.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/sku/sku_edit.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/sku/chosen.min.css?rnd=${resourceVersionKey}">
</head>

<body>
    <form action="${pageContext.request.contextPath}/goods/vgoods/skuMoveSubmit.do" id="form_submit" class="J-form" method="POST">

        <div class=" ">
            <div class="form-container base-form form-span-7">
                <div class="form-title">
                    SKU迁移
                </div>

                <%--基本信息--%>
                <div class="form-block">

                        <div class="form-item">
                            <div class="form-label">订货号(逗号分隔)：</div>
                            <div class="form-fields">
                                <input type="text"  class="input-text  "  name="SkuIds"  >

                            </div>
                        </div>
                    <div class="form-item">
                        <div class="form-label">迁移SPU ID：</div>
                        <div class="form-fields">
                            <input type="text"   class="input-text  " name="spuId"  >

                        </div>
                    </div>

                </div>

                <div class="form-btn">
                    <div class="form-fields">
                        <button type="button" class="btn btn-large close-spu-edit">取消</button>
                        <button type="submit" class="btn btn-blue btn-large" style="margin-left: 10px">提交</button>
                    </div>
                </div>
        </div>
        </div>
    </form>

    <script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/upload.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/pages/goods/vgoods/sku_edit.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/js/Sortable.min.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/js/chosen.jquery.js"></script>

<%@ include file="../../../common/footer.jsp"%>