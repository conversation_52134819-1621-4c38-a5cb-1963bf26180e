package com.vedeng.erp.saleorder.model.dto;

import lombok.Data;

import java.util.Objects;

/**
 * 客户信息
 *
 * <AUTHOR>
 */
@Data
public class CustomerInvoiceDto {

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 税务登记号
     */
    private String taxRegistrationNumber;

    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 电话号码
     */
    private String mobile;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 开户行支付联行号
     */
    private String bankTaxNumber;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 是否含有资质
     */
    private Integer isHaveQualifications;

    /**
     * 客户ID
     */
    private Integer invoiceTraderId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CustomerInvoiceDto that = (CustomerInvoiceDto) o;
        return Objects.equals(customerName, that.customerName) && Objects.equals(taxRegistrationNumber, that.taxRegistrationNumber) && Objects.equals(companyAddress, that.companyAddress) && Objects.equals(mobile, that.mobile) && Objects.equals(bankName, that.bankName) && Objects.equals(bankTaxNumber, that.bankTaxNumber) && Objects.equals(bankAccount, that.bankAccount) && Objects.equals(isHaveQualifications, that.isHaveQualifications) && Objects.equals(invoiceTraderId, that.invoiceTraderId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(customerName, taxRegistrationNumber, companyAddress, mobile, bankName, bankTaxNumber, bankAccount, isHaveQualifications, invoiceTraderId);
    }
}
