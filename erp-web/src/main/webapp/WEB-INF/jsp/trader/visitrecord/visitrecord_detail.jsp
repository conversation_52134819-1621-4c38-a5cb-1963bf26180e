<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="拜访记录" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script src="${pageContext.request.contextPath}/static/vue/element-ui/js/core.js?rnd=${resourceVersionKey}"></script>
<style type="text/css">
    table tr{
        border:0 !important;
    }
    .table-smallest{
        text-align: right !important;
        padding-right: 10px !important;
        border: 0 !important;
    }
    .table-middle{
        text-align: left !important;
        padding-left: 10px !important;
        border: 0 !important;
    }
    .table-title-second{
        font-size: 12px !important;
        padding-left: 40px;

    }
    .table-title{
        font-size: 14px !important;
        font-weight: bolder !important;
    }
    table{
        border: 0 !important;
    }
</style>
<div class="content" style="padding-bottom: 20px;">
    <div class="parts" style="margin-top: 10px;">
        <div class="title-container">
            <div class="table-title">拜访计划</div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smallest">计划拜访时间</td>
                <td class="table-middle"> <fmt:formatDate value="${requestScope.visitDto.planVisitDate}" pattern="yyyy-MM-dd" /></td>
            </tr>

            <tr>
                <td class="table-smallest">拜访目标</td>
                <td class="table-middle">${requestScope.visitDto.visitTargetNames}</td>
            </tr>

            <tr>
                <td class="table-smallest">拜访人</td>
                <td class="table-middle">${requestScope.visitDto.visitorName}</td>
            </tr>
            <tr>
                <td class="table-smallest">拜访单位名称</td>
                <td class="table-middle">${requestScope.visitDto.customerName}</td>
            </tr>
<%--
            <c:if test="${not empty requestScope.visitDto.customerNature }">
--%>
            <tr>
                <td class="table-smallest">客户类型</td>
                <td class="table-middle">
                    <c:if test="${requestScope.visitDto.customerNature eq 465}">
                        渠道商
                    </c:if>
                    <c:if test="${requestScope.visitDto.customerNature eq 466}">
                        终端
                    </c:if>
                </td>
            </tr>


             <tr>
                <td class="table-smallest">同舟会员</td>
                <td class="table-middle">
                    <c:choose>
                        <c:when test="${requestScope.visitDto.customerTz eq 1}">
                            是
                        </c:when>
                        <c:otherwise>
                            否
                        </c:otherwise>
                    </c:choose>

                </td>
            </tr>
             <c:if test="${not empty requestScope.visitDto.customerLevel }">
                <tr>
                    <td class="table-smallest">会员等级</td>
                    <td class="table-middle">
                        <c:if test="${requestScope.visitDto.customerLevel eq 1}">
                            金牌会员
                        </c:if>
                        <c:if test="${requestScope.visitDto.customerLevel eq 2}">
                            银牌会员
                        </c:if>
                    </td>
                </tr>
            </c:if>

            <tr>
                <td class="table-smallest">客户所在地区</td>
                <td class="table-middle">${requestScope.visitDto.provinceName}&nbsp;&nbsp;${requestScope.visitDto.cityName}&nbsp;&nbsp;${requestScope.visitDto.areaName}</td>
            </tr>
            <tr>
                <td class="table-smallest">商机编号</td>
                <td class="table-middle">
                    <c:if test="${ not empty requestScope.visitDto.bussinessChanceNo}">
                        <a class="addtitle" href="javascript:void(0);"
                           tabTitle='{"num":"viewbusinessChance${requestScope.visitDto.bussinessChanceId}",
											"link":"/businessChance/details.do?id=${requestScope.visitDto.bussinessChanceId}",
											"title":"商机详情"}'>${requestScope.visitDto.bussinessChanceNo}
                        </a>
                    </c:if>
                    </td>
            </tr>
            <tr>
                <td class="table-smallest">商机状态</td>
                <td class="table-middle">${requestScope.visitDto.statusName}</td>
            </tr>

            </tbody>
        </table>
    </div>

    <c:if test="${requestScope.visitDto.cardOff eq 'Y'}">
    <hr />
    <div class="parts" style="margin-top: 10px;">
        <div class="title-container">
            <div class="table-title">打卡记录</div>
            <div class="title-click" style="font-size:14px;font-weight: bolder;color:green;margin-right: 50px;"   >
                已打卡
            </div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smallest">客户名称</td>
                <td class="table-middle">
                    <c:if test="${ not empty requestScope.visitDto.customerName}">
                        <c:choose>
                            <c:when test="${requestScope.visitDto.traderViewAble}">
                                <a class="addtitle" href="javascript:void(0);"
                                   tabTitle='{"num":"viewCustomerId${requestScope.visitDto.traderId}",
                                                "link":"/trader/customer/baseinfo.do?traderId=${requestScope.visitDto.traderId}",
                                                "title":"客户详情"}'>${requestScope.visitDto.customerName}
                                </a>
                            </c:when>
                            <c:otherwise>
                                ${requestScope.visitDto.customerName}
                            </c:otherwise>
                        </c:choose>
                    </c:if>
                </td>
            </tr>
            <tr>
                <td class="table-smallest">拍照打卡</td>
                <td class="table-middle">

                    <c:forEach var="url" items="${fn:split(requestScope.visitDto.pictureList, ',')}">
                        <img src="${url}" title="单击可打开" alt="Image" width="90" height="90" onclick="window.open(this.src);" style="cursor: pointer;"/>
                        &nbsp;&nbsp;
                    </c:forEach>

                </td>
            </tr>

            <tr>
                <td class="table-smallest">打卡时间</td>
                <td class="table-middle">${requestScope.visitDto.cardTime}</td>
            </tr>


            </tbody>
        </table>
    </div>
    </c:if>

<c:if test="${requestScope.visitDto.cardOff eq 'Y'}">
    <hr />
    <div class="parts" style="margin-top: 10px;">
        <div class="title-container">
            <div class="table-title">拜访记录</div>

            <c:if test="${requestScope.visitDto.visitSuccess eq 'Y'}">
            <div class="title-click" style="color:green;font-size:14px;font-weight: bolder;margin-right: 50px;"   >
                拜访成功
            </div>
            </c:if>
            <c:if test="${requestScope.visitDto.visitSuccess eq 'N'}">
                <div class="title-click" style="color:red;font-size:14px;font-weight: bolder;margin-right: 50px;"  >
                    拜访事项缺失
                </div>
            </c:if>
        </div>

        <div class="title-container" style="background-color: #f5f5f5;">
            <div class="table-title table-title-second" style="font-size: 12px !important;">联系人信息</div>
        </div>

        <table class="table table-bordered table-striped table-condensed table-centered" style="margin-bottom: 0px !important;">
            <tbody>
            <tr>
                <td class="table-smallest">联系人姓名</td>
                <td class="table-middle">${requestScope.visitDto.contactName}</td>
            </tr>
            <tr>
                <td class="table-smallest">手机</td>
                <td class="table-middle">${requestScope.visitDto.contactMobile}</td>
            </tr>
            <tr>
                <td class="table-smallest">电话</td>
                <td class="table-middle">${requestScope.visitDto.contactTele}</td>
            </tr>

            <tr>
                <td class="table-smallest">联系人职位</td>
                <td class="table-middle">${requestScope.visitDto.contactPosition}</td>
            </tr>


            </tbody>
        </table>

        <div class="title-container" style="background-color:#f5f5f5;margin-top: 10px;">
            <div class="table-title table-title-second" style="font-size: 12px !important;">沟通事项</div>
        </div>

        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smallest">讲解PPT</td>
                <td class="table-middle">
                    <c:choose  >
                        <c:when test="${requestScope.visitDto.showPpt eq 'Y'}">
                            已完成
                        </c:when>
                        <c:otherwise>
                            未完成
                        </c:otherwise>
                    </c:choose>
                </td>
            </tr>
            <tr>
                <td class="table-smallest">商机编号</td>
                <td class="table-middle">
                    <c:if test="${ not empty requestScope.visitDto.bussinessChanceNo}">
                    <a class="addtitle" href="javascript:void(0);"
                       tabTitle='{"num":"viewbusinessChance${requestScope.visitDto.bussinessChanceId}",
											"link":"/businessChance/details.do?id=${requestScope.visitDto.bussinessChanceId}",
											"title":"商机详情"}'>${requestScope.visitDto.bussinessChanceNo}
                    </a>
                    </c:if>
<%--
                    <a href="javascript:openTab('','businessChance/details.do?id=${requestScope.visitDto.bussinessChanceNo}')">${requestScope.visitDto.bussinessChanceNo}</a>
--%>
                </td>
            </tr>

            <tr>
                <td class="table-smallest">邀请客户注册</td>
                <td class="table-middle">
                    <c:choose  >
                        <c:when test="${requestScope.visitDto.inviteReg eq 'Y'}">
                            已完成
                        </c:when>
                        <c:otherwise>
                            未完成
                        </c:otherwise>
                    </c:choose>
                </td>
            </tr>
            <tr>
                <td class="table-smallest">注册账号</td>
                <td class="table-middle">${requestScope.visitDto.regMobile}</td>
            </tr>
            <tr>
                <td class="table-smallest">沟通备忘录</td>
                <td class="table-middle">${requestScope.visitDto.commucateContent}</td>
            </tr>
            <tr>
                <td class="table-smallest">预计下次拜访时间</td>
                <td class="table-middle"><c:choose  >
                    <c:when test="${requestScope.visitDto.nextVisitDate ==null }">
                        近期不做拜访
                    </c:when>
                    <c:otherwise>
                        <fmt:formatDate value="${requestScope.visitDto.nextVisitDate}" pattern="yyyy-MM-dd" />
                    </c:otherwise>
                    </c:choose>
                </td>
            </tr>


            </tbody>
        </table>
    </div>
</c:if>
	<%--<div class="pb50 font-grey9">
		友情提醒： <br />
		1、如果该客户同时也是供应商，则显示供应商相关数据，否则供应信息为空；采购次数和采购金额指我司向其采购订单的次数和金额。
	</div>--%>
</div>
<%@ include file="../../common/footer.jsp" %>
