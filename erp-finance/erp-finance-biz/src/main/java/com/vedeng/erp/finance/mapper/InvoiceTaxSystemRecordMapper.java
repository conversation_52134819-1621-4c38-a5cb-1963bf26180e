package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.dto.InvoiceTaxSystemRecordDto;
import org.apache.ibatis.annotations.Param;

import com.vedeng.erp.finance.domain.entity.InvoiceTaxSystemRecordEntity;

import java.util.List;
import java.util.Set;

public interface InvoiceTaxSystemRecordMapper {
    /**
     * delete by primary key
     *
     * @param invoiceTaxSystemErrorId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long invoiceTaxSystemErrorId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(InvoiceTaxSystemRecordEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(InvoiceTaxSystemRecordEntity record);

    /**
     * select by primary key
     *
     * @param invoiceTaxSystemErrorId primary key
     * @return object by primary key
     */
    InvoiceTaxSystemRecordEntity selectByPrimaryKey(Long invoiceTaxSystemErrorId);

    /**
     * 根据条件查找
     *
     * @param invoiceTaxSystemRecordEntity invoiceTaxSystemRecordEntity
     * @return InvoiceTaxSystemRecordEntity
     */
    List<InvoiceTaxSystemRecordEntity> selectByAll(InvoiceTaxSystemRecordEntity invoiceTaxSystemRecordEntity);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(InvoiceTaxSystemRecordEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(InvoiceTaxSystemRecordEntity record);

    /**
     * insertOrUpdateSelective
     *
     * @param record record
     * @return count
     */
    int insertOrUpdateSelective(InvoiceTaxSystemRecordEntity record);

    InvoiceTaxSystemRecordEntity findByBusinessIdAndInterfaceType(@Param("businessId") String businessId,
                                                                  @Param("interfaceType") String interfaceType);

    List<InvoiceTaxSystemRecordDto> queryDownPdfError();


    List<InvoiceTaxSystemRecordDto> queryDownXmlNeedDown();

    /**
     * 更新并将重试次数+1
     *
     * @param record record
     * @return 更新后重试次数+
     */
    int updateAndRetryNumAdd(InvoiceTaxSystemRecordEntity record);


    List<String> getPdfUrlsByInvoiceNos(Set<String> invoiceNos);

    List<String> getXmlUrlsByInvoiceNos(Set<String> invoiceNos);

    InvoiceTaxSystemRecordEntity getByInvoiceNo(@Param("invoiceNo") String invoiceNo, @Param("interfaceType")String interfaceType);
}