package com.vedeng.erp.saleorder.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.erp.saleorder.domain.entity.ActivityPreOrderEntity;
import com.vedeng.erp.saleorder.dto.ActivityPreOrderDto;
import lombok.Getter;
import lombok.Setter;

import java.io.IOException;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 活动预处理订单。后续可转真实订单
 * @date 2022/12/22 13:48
 */
public interface ActivityPreOrderService {

    /**
     * 批量关闭活动商机
     *
     * @param ids
     */
    void closeActivityPreOrder(String[] ids) throws IOException;


    /**
     * 根据编号获取
     *
     * @param orderNo orderNo
     * @return ActivityPreOrderEntity
     */
    ActivityPreOrderEntity getActivityPreOrderByOrderNo(String orderNo);

    /**
     * 根据订单id获取
     *
     * @param saleorderId saleorderId
     * @return ActivityPreOrderEntity
     */
    ActivityPreOrderDto getActivityPreOrderBySaleorderId(Integer saleorderId);

    /**
     * 根据id查询活动商机
     *
     * @param activityPreOrderId
     * @return
     */
    ActivityPreOrderEntity queryById(Integer activityPreOrderId);

    /**
     *  查询某个客户在某个活动下已经抢购了多少商品
     * @param actionId
     * @param traderId
     * @param skuId
     * @return
     */
      Integer findExistNumByTraderIdAndActionId(Integer actionId,Integer traderId,Integer skuId);




    /**
     * 根据id查询活动商机
     *
     * @param activityPreOrderId
     * @return
     */
    ActivityPreOrderDto getActivityPreOrderDtoById(Integer activityPreOrderId);

    /**
     * 修改
     *
     * @param dto
     */
    void update(ActivityPreOrderDto dto);

}