<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="是否确认处理完毕" scope="application"/>
<div class="formpublic" style="height: 100%">
    <ul>
        <li>
            <div class="infor_name form-tips7" style="font-size: 16px">
                <lable>备注：</lable>
            </div>
            <div class="f_left">
                <div>
                    <textarea  class="textarea-larger" name="handleComments" id="handleComments"></textarea>
                    <div id="handleCommentsError"></div>
                </div>
            </div>
        </li>

    </ul>
    <div class="add-tijiao tcenter">
        <input type="hidden" id="afterSalesId" value="${afterSalesId}">
        <input type="hidden" id="afterSalesInvoiceId" value="${afterSalesInvoiceId}">
        <input type="hidden" id="formToken" value="${formToken}"/>
        <button class="dele" id="close-layer" type="button" onclick="selfClose()"
                style="background-color: #f5f7fa;border-color: #ced2d9;color: black;">取消
        </button>
        <button type="submit" id="submit" style="background-color: #3384ef;border-color: #fff;">提交</button>
    </div>
</div>


<script>
    $(function () {
        $("#submit").click(function () {
            checkLogin();
            $(".warning").remove();
            $("input").removeClass("errorbor");

            if ($('#handleComments').val().length > 200) {
                warnTips("handleCommentsError", " 详情说明最多输入200字符，请检查后提交");
                return false;
            }

            var afterSalesId = $('#afterSalesId').val();
            var afterSalesInvoiceId = $('#afterSalesInvoiceId').val();
            var handleComments = $("#handleComments").val();
            var formToken = $('#formToken').val();

            $.ajax({
                type: "POST",
                url: page_url + "/order/afterSalesCommon/invoiceHandleInfoSave.do",
                data: {
                    "afterSalesId": afterSalesId,
                    "afterSalesInvoiceId": afterSalesInvoiceId,
                    "formToken": formToken,
                    "handleStatus": 1,
                    "handleComments": handleComments
                },
                dataType: 'json',
                success: function (data) {
                    if (data.code == 0) {
                        window.location.reload();
                    } else {
                        layer.alert(data.message, {icon: 2},
                            function (index) {
                                layer.close(index);
                                return false;
                            }
                        );
                    }

                }, error: function (data) {
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });

        });
    });

    function selfClose() {
        closeChildrenView();
    }
</script>