$(function(){
	if($("#currentMonthInvoice").val()!=1){
		var returnNum = "",returnId = "",amount = 0, indx = "";
		$("#afterGoodsListId").find("input[type='hidden'][name='hideReturnNum']").each(function(i){//循环售后退货(票)数量
			returnNum = $(this).val();returnId = $(this).attr("id");
			var indx = $(this).attr("id");
			var price = Number($("#price_"+indx).val());
			amount = (Number(amount) + Number(returnNum*price)).toFixed(2);
		})
		//$("#afterReturnInvoiceAmount").html(amount);
	}else{
		var returnNum = "",returnId = "",amount = 0, indx = "";
		$("#afterGoodsListId").find("input[name='hideReturnInvoiceAmount']").each(function(i){//循环售后退货(票)数量
			amount = (Number(amount) + Number($(this).val())).toFixed(2);
		})
		//$("#afterReturnInvoiceAmount").html(amount);
		
	}
	changeRetuenInvoice();
})


function chooseAfterFullElectronicInvoice() {
	var checkboxValue = $("input[type='checkbox'][name='fullElectronicInvoice'].mt5").prop("checked");
	if (checkboxValue) {
		$("#invoiceTypeText").text("红字有效");
		$("#invoiceTypeText").css("color", "red");
		$("#colorType").val(1);
		$("#isEnable").val(1);
		$("#invoiceProperty").val(3);

		$("#in_invoiceCode").val("0000000000");
		$("#in_invoiceCode").attr("readonly","readonly");
		$("#in_invoiceCode").css("background-color","#c5c2c2");

		$("#in_invoiceNo").val("");
		$("#in_invoiceNo").removeAttr("readonly");
		$("#in_invoiceNo").css("background-color","#FFFFFF");
	} else {
		// 取消选中时，恢复原来的逻辑
		var currentMonthInvoice = $("#currentMonthInvoice").val();
		if (currentMonthInvoice == 1) {
			$("#invoiceTypeText").text("蓝字作废");
			$("#invoiceTypeText").css("color", "blue");
			$("#colorType").val(2);
			$("#isEnable").val(0);
			$("#invoiceProperty").val(1);

			$("#in_invoiceCode").val($('#originInvoiceCode').val());
			$("#in_invoiceNo").val($('#originInvoiceNo').val());
			$("#in_invoiceNo").attr("readonly","readonly");
			$("#in_invoiceNo").css("background-color","#c5c2c2");
		}
		if (currentMonthInvoice == 0) {
			$("#invoiceTypeText").text("红字有效");
			$("#invoiceTypeText").css("color", "red");
			$("#colorType").val(1);
			$("#isEnable").val(1);
			$("#invoiceProperty").val(1);

			$("#in_invoiceCode").val("");
			$("#in_invoiceCode").removeAttr("readonly");
			$("#in_invoiceCode").css("background-color","#FFFFFF");
		}
	}
}




function checkReturnInvoice(obj,index){
	if(index == 0){
		if($(obj).is(":checked")){//选中
			var returnNum = "",returnId = "",amount = 0, indx = "";
			$("#afterGoodsListId").find("input[type='hidden'][name='hideReturnNum']").each(function(i){//循环售后退货(票)数量
				returnNum = $(this).val();returnId = $(this).attr("id");
				$("#afterGoodsListId").find("input[type='hidden'][name='hideInvoiceNum']").each(function(j){//循环财务已开发票
					if(i == j){
						if(Number(returnNum) <= Number($(this).val())){ //退货(票)数量小于等于已开票数量
							$("#spanInvoiceNum"+$(this).attr("id")).html(Number(returnNum).toFixed(2));
						}else{
							$("#spanInvoiceNum"+$(this).attr("id")).html(Number($(this).val()).toFixed(2));
						}
					}
				})
					var indx = $(this).attr("id");
					var price = Number($("#price_"+indx).val());
					amount = (Number(amount) + Number(returnNum*price)).toFixed(2);
			})
			
			//$("#afterReturnInvoiceAmount").html(amount);
		}
	}else{
		if($(obj).is(":checked")){//选中
			var hiddenAmount = $("#afterReturnInvoiceAmountHidden").val();
			$("#afterGoodsListId").find("input[type='hidden'][name='hideInvoiceNum']").each(function(j){//循环财务已开发票
				$("#spanInvoiceNum"+$(this).attr("id")).html(Number($(this).val()).toFixed(2));
			})
			//$("#afterReturnInvoiceAmount").html(Number(hiddenAmount).toFixed(2));
		}
	}
	changeRetuenInvoice();
}

function vailInvoiceCode(obj){
	clear2ErroeMes();
	var invoiceCode = $(obj).val().trim();
	if(invoiceCode.length == 0){
		warn2Tips("invoiceCode","发票代码不允许为空");
		return false;
	} else {
		var reg = /^[0-9]*$/;
		if (!reg.test(invoiceCode)) {
			warn2Tips("invoiceCode","请输入数字");
			return false;
		}
	}
}
function vailInvoiceNo(obj){
	clear2ErroeMes();
	var invoiceNo = $(obj).val().trim();
	if(invoiceNo.length == 0){
		warn2Tips("in_invoiceNo","发票号不允许为空");
		return false;
	}else{
		var checkboxValue = $("input[type='checkbox'][name='fullElectronicInvoice'].mt5").prop("checked");
		if (checkboxValue) {
			// 数电发票no为20位
			var reg = /^\d{20}$/;
			if(!reg.test(invoiceNo)){
				warn2Tips("in_invoiceNo","请输入正确的20位数字发票号");
				return false;
			}
		} else {
			// 原有逻辑为8
			var reg = /^\d{8}$/;
			if(!reg.test(invoiceNo)){
				warn2Tips("in_invoiceNo","请输入正确的8位数字发票号");
				return false;
			}
		}
	}
}

// 重复点击 置灰
function lockClickInvoice(){
	$('#addAfterReturnInvoiceButton').attr('disabled',true)
}

function addAfterReturnInvoice(){

	lockClickInvoice()

	checkLogin();
	var type = $('#type').val();
	var checkboxValue = $("input[type='checkbox'][name='fullElectronicInvoice'].mt5").prop("checked");
	if($("#currentMonthInvoice").val() == "0" || checkboxValue){//非当月发票 || 当前选中数电发票
		clear2ErroeMes();
		var invoiceCode = $("#addAfterCapitalBillForm #in_invoiceCode").val().trim();
		if(invoiceCode.length == 0){
			warn2Tips("invoiceCode","发票代码不允许为空");
			return false;
		} else {
			var reg = /^[0-9]*$/;
			if (!reg.test(invoiceCode)) {
				warn2Tips("invoiceCode","请输入数字");
				return false;
			}
		}
		var invoiceNo = $("#addAfterCapitalBillForm #in_invoiceNo").val().trim();
		if(invoiceNo.length == 0){
			warn2Tips("in_invoiceNo","发票号不允许为空");
			return false;
		}else{
			var checkboxValue = $("input[type='checkbox'][name='fullElectronicInvoice'].mt5").prop("checked");
			if (checkboxValue) {
				// 数电发票no为20位
				var reg = /^\d{20}$/;
				if(!reg.test(invoiceNo)){
					warn2Tips("in_invoiceNo","请输入正确的20位数字发票号");
					return false;
				}
			} else {
				// 原有逻辑为8
				var reg = /^\d{8}$/;
				if(!reg.test(invoiceNo)){
					warn2Tips("in_invoiceNo","请输入正确的8位数字发票号");
					return false;
				}
			}
		}
		$("#invoiceNo").val(invoiceNo);
		$("#invoiceCode").val(invoiceCode);
		$("#returnInvoiceForm").find("#dynamicParameter").html("");//清空参数，防止提交失败后再次提交参数重复
		var invoiceNumArr = [];
		var invoiceAmountArr = [];
		var invoicePriceArr = [];
		var detailGoodsIdArr = [];
		//此次退票数量(红字发票退票数量改成退货数量) 2018-9-27
		var returnInvoiceAll = $("input[type='radio'][name='returnInvoiceCheck']:checked").val();
		if(returnInvoiceAll ==1){
			$("#afterGoodsListId").find("input[name='hideInvoiceNum']").each(function(i){
					invoiceNumArr.push($(this).val().trim());
			});
			$("#afterGoodsListId").find("input[name='hideReturnInvoiceAmount']").each(function(i){
					invoiceAmountArr.push($(this).val().trim());
			});
			//退票产品单价
			$("#addAfterCapitalBillForm").find("input[name='orderPrice']").each(function(n){
					invoicePriceArr.push($(this).val().trim());
			});
			//售后单-销售单产品详细表ID
			$("#addAfterCapitalBillForm").find("input[name='detailGoodsId']").each(function(j){
					detailGoodsIdArr.push($(this).val().trim());
			});

		}else{
			$("#afterGoodsListId").find("input[name='hideReturnNum']").each(function(i){
				if($("#afterGoodsListId").find("input[name='hideReturnNum'][id='"+i+"']").val() != 0){
					invoiceNumArr.push($(this).val().trim());
				}
			});
			var invoicePriceArr = [];
			//退票产品单价
			$("#addAfterCapitalBillForm").find("input[name='orderPrice']").each(function(n){
				if($("#afterGoodsListId").find("input[name='hideReturnNum'][id='"+n+"']").val() != 0){
					invoicePriceArr.push($(this).val().trim());
				}
			});
			var detailGoodsIdArr = [];
			//售后单-销售单产品详细表ID
			$("#addAfterCapitalBillForm").find("input[name='detailGoodsId']").each(function(j){
				if($("#afterGoodsListId").find("input[name='hideReturnNum'][id='"+j+"']").val() != 0){
					detailGoodsIdArr.push($(this).val().trim());
				}
			});
			//采购部分退票的微调
			if (type == 503){
                $("#afterGoodsListId").find("input[name='hideReturnInvoiceAmount']").each(function(i){
                    invoiceAmountArr.push($(this).val().trim());
                });
            }
		}
		
		if(invoiceNumArr.length != invoicePriceArr.length || invoicePriceArr.length != detailGoodsIdArr.length){
			
			layer.alert("参数获取错误，请刷新当前页重试或联系管理员！", {
				icon : 2
			}, function(lay) {
				layer.close(lay);
			});
		}
		
		$("#returnInvoiceForm").find("#dynamicParameter").html(
				
				"<input name='invoiceNumArr' type='hidden' value='"+invoiceNumArr+"'/>" +
						"<input name='invoicePriceArr' type='hidden' value='"+invoicePriceArr+"'/>" +
						((returnInvoiceAll == 1 && type == 505) || type == 503 ? ("<input name='invoiceAmountArr' type='hidden' value='"+invoiceAmountArr+"'/>"):"")+
								"<input name='detailGoodsIdArr' type='hidden' value='"+detailGoodsIdArr+"'/>");
	}

	if (checkReturnInvoiceAmount() == false){
	    return;
    }

	// $("#returnInvoiceForm").submit();
	$.ajax({
		async:false,
		url:'./saveAfterReturnInvoice.do',
		data:$("#returnInvoiceForm").serialize(),
		type:"POST",
		dataType : "json",
		success:function(data){
			// layer.confirm(data.message,{
			// 	btn: ['确定']
			// }, function () {
			// 	refreshNowPageList(data);
			// });
			layer.alert(data.message);
		},error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	})
}

function changeRetuenInvoice() {
	var returnInvoiceCheck = $("input[name='returnInvoiceCheck']:checked").val();
	var returnInvoiceAmounts = $("[id^='hideReturnInvoiceAmount_']");
	for (var index = 0; index < returnInvoiceAmounts.length; index++) {
		if (returnInvoiceCheck == 1){
			$('#' + returnInvoiceAmounts[index].id).attr('disabled', true);
			var returnAmount = Number($('#price_' + index).val() * $('#allReturnNum_' + index).val()).toFixed(2);
			console.log('全部退票商品退票金额:' + returnAmount)
			$('#' + returnInvoiceAmounts[index].id).val(returnAmount);
			$('#hideReturnInvoiceAmount_' + index).attr('valueFlag', returnAmount);
		}else {
			console.log($('#spanInvoiceNum' + returnInvoiceAmounts[index].getAttribute('indexFlag')).html());
			if ($('#spanInvoiceNum' + returnInvoiceAmounts[index].getAttribute('indexFlag')).html() > 0){
				$('#' + returnInvoiceAmounts[index].id).attr('disabled', false);
			} else {
				$('#' + returnInvoiceAmounts[index].id).attr('disabled', true);
			}
			var returnAmount = Number($('#price_' + index).val() * $('#realReturnNum_' + index).val()).toFixed(2);
			console.log('部分退票商品退票金额:' + returnAmount)
			$('#' + returnInvoiceAmounts[index].id).val(returnAmount);
			$('#hideReturnInvoiceAmount_' + index).attr('valueFlag', returnAmount);
		}
	}
}

/**
 * 修改退票金额
 */
function checkReturnInvoiceAmount() {
    //全部退票跳过校验
    var returnInvoiceCheck = $("input[name='returnInvoiceCheck']:checked").val();
    if (returnInvoiceCheck == 1){
        return true;
    }
	var  totalAmount = 0.00;
	var returnInvoiceAmounts = $("[id^='hideReturnInvoiceAmount_']");
	reg = /^-?\d+\.?\d{0,2}$/
	for (var index = 0; index < returnInvoiceAmounts.length; index++) {
		var valueFlag = parseFloat(returnInvoiceAmounts[index].getAttribute('valueFlag'));
		var selfValue = parseFloat(returnInvoiceAmounts[index].value);

		if (valueFlag + 1 < selfValue || valueFlag - 1 > selfValue){
			layer.alert('退票金额不得超过“退票数量*单价”的正负1元范围', function () {
				layer.closeAll();
				return false;
			});
            return false;
		}
		console.log('两位小数校验:' + reg.test(selfValue));

		var canRecordAmount = parseFloat(returnInvoiceAmounts[index].getAttribute('canRecordAmount'));
		console.log('canRecordAmount' + canRecordAmount);
		if (selfValue > canRecordAmount) {
			layer.alert('退票金额不得超过该发票该sku的剩余收票金额', function () {
				layer.closeAll();
				return false;
			});
            return false;
		}

		if (! reg.test(selfValue)){
			layer.alert('只能填写2位小数', function () {
				layer.closeAll();
				return false;
			});
            return false;
		}
		totalAmount += selfValue;
	}

	if (totalAmount == 0.00){
		layer.alert('退票金额合计不得为0', function () {
			layer.closeAll();
			return false;
		})
		return true;
	}
	return true;
}


/**
 * 控制只能输入小数点后2位
 * @param obj
 */
function clearNoNum(obj) {
	obj.value = obj.value.replace(/[^\d.]/g, "");
	obj.value = obj.value.replace(/\.{2,}/g, ".");
	obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
	obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
	if (obj.value.indexOf(".") < 0 && obj.value != "") {
		 obj.value = parseFloat(obj.value);
	}
}