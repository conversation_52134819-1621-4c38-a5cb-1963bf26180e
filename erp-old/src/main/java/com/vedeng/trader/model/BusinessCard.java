package com.vedeng.trader.model;

public class BusinessCard {

    /**
     * 相对路径
     */
    private String relativePath;

    /**
     * 显示名称
     */
    private String displayName;

    private Integer traderCertificateId;

    public String getRelativePath() {
        return relativePath;
    }

    public void setRelativePath(String relativePath) {
        this.relativePath = relativePath;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public Integer getTraderCertificateId() {
        return traderCertificateId;
    }

    public void setTraderCertificateId(Integer traderCertificateId) {
        this.traderCertificateId = traderCertificateId;
    }
}
