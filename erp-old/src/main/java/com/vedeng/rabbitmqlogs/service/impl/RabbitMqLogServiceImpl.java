package com.vedeng.rabbitmqlogs.service.impl;

import com.vedeng.rabbitmqlogs.dao.RabbitmqLogsMapper;
import com.vedeng.rabbitmqlogs.model.RabbitmqLogs;
import com.vedeng.rabbitmqlogs.service.RabbitMqLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Service
public class RabbitMqLogServiceImpl implements RabbitMqLogService {

    @Resource
    private RabbitmqLogsMapper rabbitmqLogsMapper;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public int saveRabbitmq(String paramStr, String error, String orderNo) {
        RabbitmqLogs logs = rabbitmqLogsMapper.selectByPrimaryKey(orderNo);
        if (logs != null) {
            RabbitmqLogs rabbitmqLogs = new RabbitmqLogs();
            rabbitmqLogs.setOrderNo(orderNo);
            rabbitmqLogs.setUpdateTime(new Date());
            rabbitmqLogs.setNumberRetries(logs.getNumberRetries() + 1);
            int i = rabbitmqLogsMapper.updateByPrimaryKeySelective(rabbitmqLogs);
            if (i > 0) {
                return logs.getNumberRetries() + 1;
            } else {
                return 0;
            }
        } else {
            RabbitmqLogs rabbitmqLogs = new RabbitmqLogs();
            rabbitmqLogs.setOrderNo(orderNo);
            rabbitmqLogs.setAddTime(new Date());
            rabbitmqLogs.setErrorMessage(error);
            rabbitmqLogs.setOrderInformation(paramStr);
            rabbitmqLogs.setNumberRetries(1);
            int i = rabbitmqLogsMapper.insertSelective(rabbitmqLogs);
            if (i > 0) {
                return i;
            } else {
                return 0;
            }
        }
    }
}
