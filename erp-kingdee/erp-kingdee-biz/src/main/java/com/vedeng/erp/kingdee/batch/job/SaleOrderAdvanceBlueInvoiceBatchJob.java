package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.processor.BatchSaleOrderBlueInvoiceProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchSaleOrderReceiveProcessor;
import com.vedeng.erp.kingdee.batch.tasklet.SaleOrderAdvanceBlueInvoiceWarehouseOutTasklet;
import com.vedeng.erp.kingdee.batch.writer.BatchSaleOrderBlueInvoiceWriter;
import com.vedeng.erp.kingdee.batch.writer.BatchSaleOrderReceiveWriter;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDto;
import com.vedeng.erp.kingdee.dto.KingDeeSalesVatPlainAndSpecialInvoiceDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * 销售蓝票提前开票处理job
 * @version 1.0
 * @date 2023/7/5 9:25
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class SaleOrderAdvanceBlueInvoiceBatchJob extends BaseJob {

    @Autowired
    private SaleOrderAdvanceBlueInvoiceWarehouseOutTasklet saleOrderAdvanceBlueInvoiceWarehouseOutTasklet;

    @Autowired
    private BatchSaleOrderReceiveProcessor batchSaleOrderReceiveProcessor;

    @Autowired
    private BatchSaleOrderReceiveWriter batchSaleOrderReceiveWriter;

    @Autowired
    private BatchSaleOrderBlueInvoiceProcessor batchSaleOrderBlueInvoiceProcessor;

    @Autowired
    private BatchSaleOrderBlueInvoiceWriter batchSaleOrderBlueInvoiceWriter;

    public Job advanceSaleBlueInvoice() {
        return jobBuilderFactory.get("销售提前开票应收单及蓝票")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(bindBlueInvoiceAndWarehouseInRelation())
                .next(receivableOrderPush())
                .next(blueInvoicePush())
                .build();
    }

    /**
     * 销售蓝票和出库单关系绑定
     */
    private Step bindBlueInvoiceAndWarehouseInRelation() {
        return stepBuilderFactory.get("销售提前开票蓝票和出库单关系绑定")
                .tasklet(saleOrderAdvanceBlueInvoiceWarehouseOutTasklet)
                .build();
    }

    /**
     * 标准应收单推送
     *
     * @return Step
     */
    private Step receivableOrderPush() {
        return stepBuilderFactory.get("销售标准应收单")
                .<BatchInvoiceDto, KingDeeReceiveCommonDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleOrderAdvanceBlueInvoiceReader(null, null))
                .processor(batchSaleOrderReceiveProcessor)
                .writer(batchSaleOrderReceiveWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 蓝票推送（包含普票和专票）
     *
     * @return Step
     */
    private Step blueInvoicePush() {
        return stepBuilderFactory.get("销售蓝票")
                .<BatchInvoiceDto, KingDeeSalesVatPlainAndSpecialInvoiceDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleOrderAdvanceBlueInvoiceReader(null, null))
                .processor(batchSaleOrderBlueInvoiceProcessor)
                .writer(batchSaleOrderBlueInvoiceWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> saleOrderAdvanceBlueInvoiceReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto invoiceDto = BatchInvoiceDto
                .builder()
                // 通过时间是当天的
                .beginTime(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "findAdvanceSaleOrderBlueInvoiceBatch", invoiceDto);
    }

}