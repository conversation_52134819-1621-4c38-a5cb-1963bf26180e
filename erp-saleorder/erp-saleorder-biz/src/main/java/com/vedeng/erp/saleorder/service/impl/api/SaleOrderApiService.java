package com.vedeng.erp.saleorder.service.impl.api;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.service.BankBillApiService;
import com.vedeng.erp.saleorder.api.SaleOrderApi;
import com.vedeng.erp.saleorder.dao.SaleOrderMapper;
import com.vedeng.erp.saleorder.domain.entity.ActivityPreOrderEntity;
import com.vedeng.erp.saleorder.dto.CapitalFlowDto;
import com.vedeng.erp.saleorder.service.ActivityPreOrderService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleorderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 获取流水金额
 * @date 2022/12/23 10:53
 */
@Controller
public class SaleOrderApiService implements SaleOrderApi {

    @Autowired
    SaleorderService saleorderService;
    @Autowired
    ActivityPreOrderService activityPreOrderService;
    @Autowired
    BankBillApiService bankBillApiService;

    @Override
    @ResponseBody
    public R<CapitalFlowDto> getFlowAmount(@RequestBody CapitalFlowDto capitalFlowDto) {
        String companyName = null;
        if (StrUtil.isNotBlank(capitalFlowDto.getSaleOrderNo())) {
            Saleorder saleorder = saleorderService.getBySaleOrderNo(capitalFlowDto.getSaleOrderNo());
            if (saleorder != null) {
                companyName = saleorder.getTraderName();
            }
        }

        if (StrUtil.isNotBlank(capitalFlowDto.getBusinessChangeNo())) {
            ActivityPreOrderEntity activityPreOrder = activityPreOrderService.getActivityPreOrderByOrderNo(capitalFlowDto.getBusinessChangeNo());
            if (activityPreOrder.getSaleorderId() != null && activityPreOrder.getSaleorderId() != 0) {
                Saleorder saleorder = saleorderService.getsaleorderbySaleorderId(activityPreOrder.getSaleorderId());
                if (saleorder != null) {
                    companyName = saleorder.getTraderName();
                }
            }
        }
        if (companyName == null) {
            return R.error("未找到对应客户信息");
        }
        List<BankBillDto> bankBillDtos = bankBillApiService.getBankBillByDateAndCompany(DateUtil.offsetMonth(new Date(), -1), new Date(), companyName);
        BigDecimal amount = bankBillDtos.stream().map(BankBillDto::getSurplusAmount).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        capitalFlowDto.setAmount(amount);
        return R.success(capitalFlowDto);
    }

    
}
