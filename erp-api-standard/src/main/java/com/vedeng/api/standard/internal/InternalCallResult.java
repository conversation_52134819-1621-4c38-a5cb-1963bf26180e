package com.vedeng.api.standard.internal;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 内部调用结果封装类
 * 用于封装内部HTTP调用的结果信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
public class InternalCallResult implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 调用是否成功
     */
    private boolean success;
    
    /**
     * 返回数据
     */
    private Object data;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * HTTP状态码
     */
    private int httpStatus;
    
    /**
     * 响应头
     */
    private Map<String, String> responseHeaders;
    
    /**
     * 执行时间（毫秒）
     */
    private long executionTime;
    
    /**
     * 调用开始时间
     */
    private long startTime;
    
    /**
     * 调用结束时间
     */
    private long endTime;
    
    /**
     * 异常信息
     */
    private Throwable exception;
    
    /**
     * 业务错误码（用于存储响应中的业务错误代码）
     */
    private String businessCode;
    
    /**
     * 扩展属性（用于存储特定业务的额外信息）
     */
    private Map<String, Object> attributes;
    
    /**
     * 默认构造函数
     */
    public InternalCallResult() {
        this.success = false;
        this.httpStatus = 200;
        this.responseHeaders = new HashMap<>();
        this.attributes = new HashMap<>();
        this.startTime = System.currentTimeMillis();
    }
    
    /**
     * 静态工厂方法 - 创建成功结果
     * 
     * @param data 返回数据
     * @return 成功结果
     */
    public static InternalCallResult success(Object data) {
        InternalCallResult result = new InternalCallResult();
        result.setSuccess(true);
        result.setData(data);
        result.markCompleted();
        return result;
    }
    
    /**
     * 静态工厂方法 - 创建失败结果
     * 
     * @param errorMessage 错误消息
     * @return 失败结果
     */
    public static InternalCallResult failure(String errorMessage) {
        InternalCallResult result = new InternalCallResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        result.markCompleted();
        return result;
    }
    
    /**
     * 静态工厂方法 - 创建异常结果
     * 
     * @param exception 异常对象
     * @return 异常结果
     */
    public static InternalCallResult exception(Throwable exception) {
        InternalCallResult result = new InternalCallResult();
        result.setSuccess(false);
        result.setException(exception);
        result.setErrorMessage(exception.getMessage());
        result.markCompleted();
        return result;
    }
    
    /**
     * 静态工厂方法 - 创建业务失败结果
     * 
     * @param businessCode 业务错误码
     * @param errorMessage 错误消息
     * @param originalData 原始响应数据
     * @return 业务失败结果
     */
    public static InternalCallResult businessFailure(String businessCode, String errorMessage, Object originalData) {
        InternalCallResult result = new InternalCallResult();
        result.setSuccess(false);
        result.setBusinessCode(businessCode);
        result.setErrorMessage(errorMessage);
        result.setData(originalData);
        result.markCompleted();
        return result;
    }
    
    /**
     * 静态工厂方法 - 从解析结果创建
     * 
     * @param parseResult 响应解析结果
     * @return InternalCallResult
     */
    public static InternalCallResult fromParseResult(ResponseParser.ParseResult parseResult) {
        InternalCallResult result = new InternalCallResult();
        result.setSuccess(parseResult.isSuccess());
        result.setData(parseResult.getData());
        
        if (!parseResult.isSuccess()) {
            result.setBusinessCode(parseResult.getBusinessCode());
            result.setErrorMessage(parseResult.getErrorMessage());
        }
        
        result.markCompleted();
        return result;
    }
    
    /**
     * 标记调用完成
     */
    public void markCompleted() {
        this.endTime = System.currentTimeMillis();
        this.executionTime = this.endTime - this.startTime;
    }
    
    /**
     * 添加扩展属性
     * 
     * @param key 属性名
     * @param value 属性值
     * @return 当前对象（支持链式调用）
     */
    public InternalCallResult addAttribute(String key, Object value) {
        this.attributes.put(key, value);
        return this;
    }
    
    /**
     * 获取扩展属性
     * 
     * @param key 属性名
     * @return 属性值
     */
    public Object getAttribute(String key) {
        return this.attributes.get(key);
    }
    
    /**
     * 获取扩展属性（带类型转换）
     * 
     * @param key 属性名
     * @param type 目标类型
     * @param <T> 类型泛型
     * @return 属性值
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key, Class<T> type) {
        Object value = this.attributes.get(key);
        if (value != null && type.isAssignableFrom(value.getClass())) {
            return (T) value;
        }
        return null;
    }
    
    /**
     * 添加响应头
     * 
     * @param key 头名称
     * @param value 头值
     * @return 当前对象（支持链式调用）
     */
    public InternalCallResult addResponseHeader(String key, String value) {
        this.responseHeaders.put(key, value);
        return this;
    }
    
    /**
     * 判断是否有异常
     * 
     * @return 是否有异常
     */
    public boolean hasException() {
        return this.exception != null;
    }
    
    /**
     * 判断是否有业务错误码
     * 
     * @return 是否有业务错误码
     */
    public boolean hasBusinessCode() {
        return businessCode != null && !businessCode.trim().isEmpty();
    }
    
    /**
     * 获取简要的错误信息
     * 
     * @return 错误信息摘要
     */
    public String getErrorSummary() {
        if (success) {
            return "Success";
        }
        
        if (errorMessage != null && !errorMessage.trim().isEmpty()) {
            String summary = errorMessage;
            if (hasBusinessCode()) {
                summary = "[" + businessCode + "] " + summary;
            }
            return summary;
        }
        
        if (exception != null) {
            return exception.getClass().getSimpleName() + ": " + exception.getMessage();
        }
        
        if (hasBusinessCode()) {
            return "Business error: " + businessCode;
        }
        
        return "Unknown error";
    }
    
    // Getter and Setter methods
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public Object getData() {
        return data;
    }
    
    public void setData(Object data) {
        this.data = data;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public int getHttpStatus() {
        return httpStatus;
    }
    
    public void setHttpStatus(int httpStatus) {
        this.httpStatus = httpStatus;
    }
    
    public Map<String, String> getResponseHeaders() {
        return responseHeaders;
    }
    
    public void setResponseHeaders(Map<String, String> responseHeaders) {
        this.responseHeaders = responseHeaders;
    }
    
    public long getExecutionTime() {
        return executionTime;
    }
    
    public void setExecutionTime(long executionTime) {
        this.executionTime = executionTime;
    }
    
    public long getStartTime() {
        return startTime;
    }
    
    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }
    
    public long getEndTime() {
        return endTime;
    }
    
    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }
    
    public Throwable getException() {
        return exception;
    }
    
    public void setException(Throwable exception) {
        this.exception = exception;
    }
    
    public Map<String, Object> getAttributes() {
        return attributes;
    }
    
    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }
    
    public String getBusinessCode() {
        return businessCode;
    }
    
    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }
    
    @Override
    public String toString() {
        return "InternalCallResult{" +
                "success=" + success +
                ", httpStatus=" + httpStatus +
                ", executionTime=" + executionTime + "ms" +
                ", businessCode='" + businessCode + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", hasData=" + (data != null) +
                ", hasException=" + hasException() +
                ", attributesCount=" + (attributes != null ? attributes.size() : 0) +
                '}';
    }
}
