package com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.afterProcessor.ext;

import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.orderstrategy.BuyorderStrategyContext;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.erp.buyorder.bizProcessor.dto.BizDto;
import com.vedeng.erp.buyorder.bizProcessor.processor.overBuyorderReturnSales.afterProcessor.OverBuyorderAfterSaleReturnOrderAfterProcessor;
import com.vedeng.erp.buyorder.service.BuyorderInfoSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
public class DealBuyorderForBuyReturnAfterProcessor extends OverBuyorderAfterSaleReturnOrderAfterProcessor {
    Logger logger = LoggerFactory.getLogger(DealBuyorderForBuyReturnAfterProcessor.class);
    @Autowired
    BuyorderInfoSyncService buyorderInfoSyncService;

    @Autowired
    AfterSalesMapper afterSalesMapper;

    @Autowired
    private BuyorderStrategyContext buyorderStrategyContext;

    /**
     * 影响范围:
     * /order/newBuyorder/editConfirmComplete  售后订单确认完成
     *
     * @param bizDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doProcess(BizDto bizDto) {
        logger.info("开始处理：{}", JsonUtils.convertObjectToJsonStr(bizDto.getBizData()));
        AfterSalesVo afterSalesVo = (AfterSalesVo) bizDto.getBizData();

        buyorderInfoSyncService.updateBuyorderForBuyorderReturnOver(afterSalesVo.getAfterSalesId(),afterSalesVo.getOrderId());
        buyorderStrategyContext.executeAll(buyorderStrategyContext.add(BuyorderStrategyContext.BUYORDER_AMOUNT_STRATEGY, OrderDataUpdateConstant.BUY_ORDER_AFTERORDER_OPY),afterSalesVo.getOrderId());

        buyorderInfoSyncService.syncDeliveryStatus(afterSalesVo.getOrderId(), 3);
        buyorderInfoSyncService.closeBuyorderOnCondition (afterSalesVo.getOrderId());
        buyorderInfoSyncService.backBuyorderStatus(afterSalesVo.getOrderId());
        logger.info("结束处理：{}", JsonUtils.convertObjectToJsonStr(bizDto.getBizData()));
    }
}
