package com.newtask.ddi;


import com.newtask.ddi.dao.DdiInstallstionExtMapper;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.order.model.GeSaleOrder;
import com.wms.dao.VWmsOutSncodeOrderExtMapper;
import com.wms.model.ddi.DdiInstallstionExtDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;


@SuppressWarnings("Duplicates")
@Component
@JobHandler(value = "DdiInstallstionTask")
public class DdiInstallstionTask extends AbstractJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(DdiInstallstionTask.class);

    @Autowired
    private DdiInstallstionExtMapper ddiInstallstionExtMapper;

    @Autowired
    private VWmsOutSncodeOrderExtMapper vWmsOutSncodeOrderExtMapper;

    @Value("${GE_TRADER_SKU}")
    private String geTraderSku;

    @Transactional
    public ReturnT<String> doExecute(String param) throws Exception {


        LOGGER.info("DDI安装订单数据采集定时任务开始"+param);
        XxlJobLogger.log("DDI安装订单数据采集定时任务开始执行" + param);


        //默认取开始时间位昨天的零点到23：59
        Date yesterdayDate = DateUtil.getPreviousDayByDateTime(new Date());
        Timestamp startTime = DateUtil.getDayStartTime(yesterdayDate);
        Timestamp endTime = DateUtil.getDayEndTime(yesterdayDate);

        //取参数时间
        if(!StringUtils.isBlank(param)){
            String[] params = param.split(",");
            String startTimeStr = params[0];
            String endTimeStr = params[1];

            long startTimeLong = DateUtil.convertLong(startTimeStr, DateUtil.DATE_FORMAT);
            long endTimeLong = DateUtil.convertLong(endTimeStr, DateUtil.DATE_FORMAT);

            startTime = DateUtil.getDayStartTime(new Date(startTimeLong));
            endTime = DateUtil.getDayEndTime(new Date(endTimeLong));
        }

        GeSaleOrder geSaleOrder = JsonUtils.readValue(geTraderSku, GeSaleOrder.class);

        //获取geSKU
        List<String> skuList = geSaleOrder.getSkuList();

        if(CollectionUtils.isEmpty(skuList)){
            return FAIL;
        }

        //获取当日录入的安装GesKU的入库单
        //获取当日时间
        Date now = new Date();

        long yesterdayTime = DateUtil.getPreviousDayByDateTime(now).getTime();
        long todayTime = now.getTime();

        try {
            //获取销售订单安调
            List<DdiInstallstionExtDto> DdiInstallstionExtDtoListforSale = vWmsOutSncodeOrderExtMapper.getAllinstallationOrderSaleOrder(skuList,startTime,endTime);



            if(!CollectionUtils.isEmpty(DdiInstallstionExtDtoListforSale)){
                DdiInstallstionExtDtoListforSale.stream().forEach(x->{
                    if(x.getInstallstionTimeLong() != null){
                        Date date = new Date(x.getInstallstionTimeLong());
                        x.setInstallstionTime(date);
                    }
                    // GE产品数量不用去订单里面取，取1
                    x.setNum(1);
                    ddiInstallstionExtMapper.insertSingle(x);
                });

            }

            //获取销售售后安调
            List<DdiInstallstionExtDto> DdiInstallstionExtDtoListforAfterSale = vWmsOutSncodeOrderExtMapper.getAllinstallationOrderAfterSaleOrder(skuList,startTime,endTime);
            if(!CollectionUtils.isEmpty(DdiInstallstionExtDtoListforAfterSale)){
                DdiInstallstionExtDtoListforAfterSale.stream().forEach(e->{
                    if(e.getInstallstionTimeLong() != null){
                        Date date = new Date(e.getInstallstionTimeLong());
                        e.setInstallstionTime(date);
                    }
                    e.setNum(1);
                    ddiInstallstionExtMapper.insertSingle(e);
                });

            }
        } catch (Exception e) {
            LOGGER.error("DDI安装订单数据采集定时任务失败,错误原因：{}",e);
            return FAIL;
        }


        LOGGER.info("DDI安装订单数据采集定时任务结束" + param);
        XxlJobLogger.log("DDI安装订单数据采集定时任务结束 " + param);
        return SUCCESS;
    }
}
