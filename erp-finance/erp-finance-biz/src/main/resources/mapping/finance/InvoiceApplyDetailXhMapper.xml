<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.InvoiceApplyDetailXhMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.InvoiceApplyDetailXhEntity">
    <!--@mbg.generated-->
    <!--@Table T_INVOICE_APPLY_DETAIL_XH-->
    <id column="INVOICE_APPLY_DETAIL_XH_ID" jdbcType="BIGINT" property="invoiceApplyDetailXhId" />
    <result column="INVOICE_APPLY_ID" jdbcType="INTEGER" property="invoiceApplyId" />
    <result column="INVOICE_APPLY_DETAIL_ID" jdbcType="INTEGER" property="invoiceApplyDetailId" />
    <result column="XH" jdbcType="INTEGER" property="xh" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    INVOICE_APPLY_DETAIL_XH_ID, INVOICE_APPLY_ID, INVOICE_APPLY_DETAIL_ID, XH, IS_DELETE,
    ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_INVOICE_APPLY_DETAIL_XH
    where INVOICE_APPLY_DETAIL_XH_ID = #{invoiceApplyDetailXhId,jdbcType=BIGINT}
  </select>
  <select id="selectByApplyId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_INVOICE_APPLY_DETAIL_XH
    where IS_DELETE = 0 and INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_INVOICE_APPLY_DETAIL_XH
    where INVOICE_APPLY_DETAIL_XH_ID = #{invoiceApplyDetailXhId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="INVOICE_APPLY_DETAIL_XH_ID" keyProperty="invoiceApplyDetailXhId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyDetailXhEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_APPLY_DETAIL_XH (INVOICE_APPLY_ID, INVOICE_APPLY_DETAIL_ID,
    XH, IS_DELETE, ADD_TIME,
    MOD_TIME, CREATOR, CREATOR_NAME,
    UPDATER, UPDATER_NAME, UPDATE_REMARK
    )
    values (#{invoiceApplyId,jdbcType=INTEGER}, #{invoiceApplyDetailId,jdbcType=INTEGER},
    #{xh,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
    #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR},
    #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" keyColumn="INVOICE_APPLY_DETAIL_XH_ID" keyProperty="invoiceApplyDetailXhId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyDetailXhEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_APPLY_DETAIL_XH
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceApplyId != null">
        INVOICE_APPLY_ID,
      </if>
      <if test="invoiceApplyDetailId != null">
        INVOICE_APPLY_DETAIL_ID,
      </if>
      <if test="xh != null">
        XH,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="invoiceApplyId != null">
        #{invoiceApplyId,jdbcType=INTEGER},
      </if>
      <if test="invoiceApplyDetailId != null">
        #{invoiceApplyDetailId,jdbcType=INTEGER},
      </if>
      <if test="xh != null">
        #{xh,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyDetailXhEntity">
    <!--@mbg.generated-->
    update T_INVOICE_APPLY_DETAIL_XH
    <set>
      <if test="invoiceApplyId != null">
        INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
      </if>
      <if test="invoiceApplyDetailId != null">
        INVOICE_APPLY_DETAIL_ID = #{invoiceApplyDetailId,jdbcType=INTEGER},
      </if>
      <if test="xh != null">
        XH = #{xh,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where INVOICE_APPLY_DETAIL_XH_ID = #{invoiceApplyDetailXhId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyDetailXhEntity">
    <!--@mbg.generated-->
    update T_INVOICE_APPLY_DETAIL_XH
    set INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
    INVOICE_APPLY_DETAIL_ID = #{invoiceApplyDetailId,jdbcType=INTEGER},
    XH = #{xh,jdbcType=INTEGER},
    IS_DELETE = #{isDelete,jdbcType=INTEGER},
    ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
    MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    CREATOR = #{creator,jdbcType=INTEGER},
    CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
    UPDATER = #{updater,jdbcType=INTEGER},
    UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where INVOICE_APPLY_DETAIL_XH_ID = #{invoiceApplyDetailXhId,jdbcType=BIGINT}
  </update>
</mapper>
