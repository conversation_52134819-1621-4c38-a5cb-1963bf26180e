package com.vedeng.erp.buyorder.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.buyorder.dto
 * @Date 2022/1/11 17:42
 */
@Data
public class BuyOrderDto {

    private Integer buyorderId;

    /**
     * 采购单号
     */
    private String buyorderNo;
    /**
     * 在途数量
     */
    private Integer onWayNum;

    /**
     * 预计到货时间
     */
    private Long estimateArrivalTime;
    /**
     * 供应商名称
     */
    private String traderName;
    /**
     * 供应商id
     */
    private Integer traderId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 创建人id
     */
    private Integer creator;

    /**
     * 部门id
     */
    private String orgId;

    /**
     * 部门
     */
    private String orgName;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 是否直发
     */
    private Integer deliveryDirect;
    /**
     * 总额
     */
    private BigDecimal totalAmount;
    /**
     * 供应商登记
     */
    private Integer grade;
    /**
     * 生效时间
     */
    private Long validTime;
    /**
     * 采购单状态
     */
    private Integer status;
}
