package com.vedeng.erp.settlement.service.impl.api;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.util.StringUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.finance.domain.entity.RPayApplyJCapitalBill;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.mapper.RPayApplyJCapitalBillMapper;
import com.vedeng.erp.settlement.domain.entity.CapitalBillDetailEntity;
import com.vedeng.erp.settlement.domain.entity.CapitalBillEntity;
import com.vedeng.erp.finance.dto.CapitalBillDetailDto;
import com.vedeng.erp.finance.dto.CapitalBillDto;
import com.vedeng.erp.settlement.enums.CapitalBusinessTypeEnum;
import com.vedeng.erp.settlement.enums.CapitalOrderTypeEnum;
import com.vedeng.erp.settlement.enums.CapitalTraderModeEnum;
import com.vedeng.erp.settlement.mapper.BankBillMapper;
import com.vedeng.erp.settlement.mapper.CapitalBillDetailMapper;
import com.vedeng.erp.settlement.mapper.CapitalBillMapper;
import com.vedeng.erp.settlement.mapstruct.CapitalBillConvertor;
import com.vedeng.erp.settlement.mapstruct.CapitalBillDetailConvertor;
import com.vedeng.erp.finance.service.CapitalBillApiService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 接口实现类
 * @date 2022/8/27 13:29
 **/
@Service
@Slf4j
public class CapitalBillApiServiceImpl implements CapitalBillApiService {

    @Autowired
    private CapitalBillMapper capitalBillMapper;

    @Autowired
    private BankBillMapper bankBillMapper;

    @Autowired
    private CapitalBillDetailMapper capitalBillDetailMapper;

    @Autowired
    private CapitalBillConvertor capitalBillConvertor;

    @Autowired
    private CapitalBillDetailConvertor capitalBillDetailConvertor;

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private RPayApplyJCapitalBillMapper rPayApplyJCapitalBillMapper;

    @Override
    public List<CapitalBillDto> getCapitalBillData(CapitalBillDetailDto capitalBillDetailDto, String operationType) {

        List<CapitalBillDto> result = capitalBillMapper.getCapitalBillData(capitalBillDetailDto);

        if ("finance_sale_detail".equals(operationType) || "finance_buy_detail".equals(operationType)||"finance_buy_expense_detail".equals(operationType)) {
            // 查询对应售后的退款记录
            List<CapitalBillDto> afterList = capitalBillMapper.getAfterReturnCapitalBillData(capitalBillDetailDto,operationType);
            if (result != null && CollectionUtil.isNotEmpty(afterList)) {
                result.addAll(afterList);
            }
        }

        List<Integer> bankBillIds = Objects.requireNonNull(result).stream().map(CapitalBillDto::getBankBillId).collect(Collectors.toList());
        Map<Integer, BankBillDto> bankBillMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(bankBillIds)){
            List<BankBillDto> bankBillDtos = bankBillMapper.queryBankBill(bankBillIds);
            bankBillMap = bankBillDtos.stream().collect(Collectors.toMap(BankBillDto::getBankBillId, Function.identity(), (v1, v2) -> v1));
            if (MapUtil.isEmpty(bankBillMap)){
                bankBillMap = new HashMap<>();
            }
        }
        for (CapitalBillDto e : result) {
            BankBillDto bankBillDto = bankBillMap.get(e.getBankBillId());
            String s = Optional.ofNullable(bankBillDto).map(BankBillDto::getReceiptUrl).orElse("");
            e.setReceiptUrl(s);
        }
        return result;
    }

    @Override
    public List<CapitalBillDto> getCapitalBillByBankBillId(Integer bankBillId) {
        return capitalBillMapper.findByBankBillId(bankBillId);
    }

    /**
     * 订单付款金额 + 不含账期偿还
     * @param buyOrderExpenseId
     * @return
     */
    @Override
    public BigDecimal getExpensePayMoney(Integer buyOrderExpenseId) {
        CapitalBillDetailDto capitalBillDetailDto = new CapitalBillDetailDto();
        // 订单付款
        capitalBillDetailDto.setBussinessType(CapitalBusinessTypeEnum.ORDER_PAYMENT.getCode());
        // 采购费用单
        capitalBillDetailDto.setOrderType(CapitalOrderTypeEnum.BUY_ORDER_EXPENSE.getCode());
        capitalBillDetailDto.setRelatedId(buyOrderExpenseId);
        List<CapitalBillDto> capitalBillData = getCapitalBillData(capitalBillDetailDto, null);
        BigDecimal payMoney = capitalBillData.stream().map(CapitalBillDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("查询采购费用支付金额：{}，入参：{}，出参：{}",payMoney, JSON.toJSONString(capitalBillDetailDto),JSON.toJSONString(capitalBillData));
        return payMoney;
    }

    /**
     * 信用支付金额 = 信用支付 - 信用还款
     * @param buyOrderExpenseId
     * @return
     */
    @Override
    public BigDecimal getExpenseDebtPeriodMoney(Integer buyOrderExpenseId) {
        CapitalBillDetailDto capitalBillDetailDto = new CapitalBillDetailDto();
        // 订单付款
        capitalBillDetailDto.setBussinessType(CapitalBusinessTypeEnum.ORDER_PAYMENT.getCode());
        // 采购费用单
        capitalBillDetailDto.setOrderType(CapitalOrderTypeEnum.BUY_ORDER_EXPENSE.getCode());
        capitalBillDetailDto.setRelatedId(buyOrderExpenseId);
        // 包含售后
        List<CapitalBillDto> capitalBillData = getCapitalBillData(capitalBillDetailDto, "finance_buy_expense_detail");
        // 信用支付
        BigDecimal creditPay = capitalBillData.stream().filter(e-> CapitalTraderModeEnum.CREDIT_PAY.getCode().equals(e.getTraderMode())).map(CapitalBillDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).abs();

        // 信用还款
        BigDecimal creditReturn = capitalBillData.stream().filter(e-> CapitalBusinessTypeEnum.CREDIT_PAYMENT.getCode().equals(e.getCapitalBillDetailDto().getBussinessType())).map(CapitalBillDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add).abs();

        return creditPay.subtract(creditReturn);
    }



    @Override
    public void insertCapitalBill(CapitalBillDto capitalBillDto) {
        CapitalBillEntity entity = capitalBillConvertor.toEntity(capitalBillDto);
        log.info("保存流水入参：{}",JSON.toJSONString(entity));
        int insertCount = capitalBillMapper.insert(entity);
        if (insertCount == 0){
            log.info("保存流水失败 capitalBillDto:{}",JSON.toJSONString(capitalBillDto));
            throw new ServiceException("保存流水失败");
        }
        capitalBillDto.setCapitalBillId(entity.getCapitalBillId());
    }

    @Override
    public void insertCapitalBillDetail(List<CapitalBillDetailDto> capitalBillDetailDto) {
        List<CapitalBillDetailEntity> capitalBillDetailEntities = capitalBillDetailConvertor.toEntity(capitalBillDetailDto);
        log.info("保存流水明细入参：{}",JSON.toJSONString(capitalBillDetailEntities));
        capitalBillDetailMapper.batchInsert(capitalBillDetailEntities);
    }

    @Override
    public void updateCapitalBillNo(CapitalBillDto capitalBillDto) {
        if (StringUtil.isEmpty(capitalBillDto.getCapitalBillNo())){
            throw new ServiceException("流水单号不能为空");
        }
        if (Objects.isNull(capitalBillDto.getCapitalBillId())){
            throw new ServiceException("流水ID不能为空");
        }
        CapitalBillEntity entity = capitalBillConvertor.toEntity(capitalBillDto);
        capitalBillMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void saveBillPeriodCapitalBill(Integer buyorderExpenseId) {
        BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.getOrderMainData(buyorderExpenseId);
        if(buyorderExpenseDto != null) {
            BigDecimal havePayAmount = getExpensePayMoney(buyorderExpenseDto.getBuyorderExpenseId());
            if (!ErpConstant.TWO.equals(buyorderExpenseDto.getPaymentStatus())
                    && buyorderExpenseDto.getBuyorderExpenseDetailDto().getAccountPeriodAmount().compareTo(new BigDecimal(0)) > 0
                    && buyorderExpenseDto.getBuyorderExpenseDetailDto().getPrepaidAmount().compareTo(havePayAmount) <= 0) {
                //非全部付款状态且满足预付款的情况下下才需要添加流水
                CapitalBillEntity capitalBill = new CapitalBillEntity();
                capitalBill.setCompanyId(ErpConstant.ONE);
                capitalBill.setTraderTime(DateUtil.current());
                capitalBill.setTraderSubject(ErpConstant.ONE);
                capitalBill.setTraderType(ErpConstant.THREE);
                capitalBill.setTraderMode(CapitalTraderModeEnum.CREDIT_PAY.getCode());
                capitalBill.setAmount(buyorderExpenseDto.getBuyorderExpenseDetailDto().getAccountPeriodAmount());
                capitalBill.setPayer("南京贝登医疗股份有限公司");
                capitalBill.setPayee(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderName());
                capitalBill.setAddTime(DateUtil.current());
                //创建人是管理员
                capitalBill.setCreator(buyorderExpenseDto.getCreator());
                capitalBillMapper.insertSelective(capitalBill);
                //流水编号
                BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.CAPITAL_NO, NoGeneratorBean.builder().dateFormat(ErpConstant.YYYYMMDDHHMMSS).id(capitalBill.getCapitalBillId()).numberOfDigits(9).build());
                String capitalBillNo = new BillNumGenerator().distribution(billGeneratorBean);
                capitalBill.setCapitalBillNo(capitalBillNo);
                capitalBillMapper.updateByPrimaryKeySelective(capitalBill);
                CapitalBillDetailEntity capitalBillDetail = new CapitalBillDetailEntity();
                capitalBillDetail.setCapitalBillId(capitalBill.getCapitalBillId());
                capitalBillDetail.setBussinessType(CapitalBusinessTypeEnum.ORDER_PAYMENT.getCode());
                capitalBillDetail.setOrderType(CapitalOrderTypeEnum.BUY_ORDER_EXPENSE.getCode());
                capitalBillDetail.setOrderNo(buyorderExpenseDto.getBuyorderExpenseNo());
                capitalBillDetail.setRelatedId(buyorderExpenseDto.getBuyorderExpenseId());
                capitalBillDetail.setAmount(buyorderExpenseDto.getBuyorderExpenseDetailDto().getAccountPeriodAmount());
                capitalBillDetail.setTraderId(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderId());
                capitalBillDetail.setTraderType(ErpConstant.TWO);
                UserDto userDto = userApiService.getUserById(buyorderExpenseDto.getCreator());
                capitalBillDetail.setUserId(userDto.getUserId());
                capitalBillDetail.setOrgId(userDto.getOrgId());
                capitalBillDetail.setOrgName(userDto.getOrgName());
                capitalBillDetailMapper.insertSelective(capitalBillDetail);
            } else {
                log.info("采购费用单{},付款状态为{},", buyorderExpenseDto.getBuyorderExpenseNo(),buyorderExpenseDto.getPaymentStatus());
            }
        }else{
            log.info("采购费用单id{},无法查询到采购费用单信息");
        }
    }

    @Override
    public BigDecimal getAfterSalesFlByBuyOrderId(Integer buyOrderId) {
        BigDecimal fl = capitalBillDetailMapper.getAfterSalesFlByBuyOrderId(buyOrderId);
        return fl;
    }

    /**
     * 计算采购单已付款金额（不含信用支付、返利）
     * @param buyOrderId
     * @return
     */
    public BigDecimal getBuyOrderPayMoney(Integer buyOrderId) {
        BigDecimal payMoney = capitalBillDetailMapper.getBuyOrderHavePayMoney(buyOrderId);
        BigDecimal afterSalesPayMoney = capitalBillDetailMapper.getBuyOrderAfterSalesHavePayMoney(buyOrderId);
        log.info("已付款金额为{},退款金额为{}",payMoney,afterSalesPayMoney);
        BigDecimal pay = payMoney.subtract(afterSalesPayMoney);
        if (pay.compareTo(BigDecimal.ZERO) < 0){
            log.error("页面展示采购单已付款金额（不含信用支付、返利）时，查询为负数，检查一下数据：采购单ID:{}",buyOrderId);
            return BigDecimal.ZERO;
        }
        return pay;
    }

    @Override
    public BigDecimal getSaleOrderPublicIncome(Integer saleorderId, String traderName) {
        return capitalBillMapper.getSaleOrderPublicIncome(saleorderId,traderName);
    }

    @Override
    public BigDecimal getSaleOrderTotalExpenditure(Integer saleorderId) {
        return capitalBillMapper.getSaleOrderTotalExpenditure(saleorderId);
    }

    @Override
    public void saveRPayApplyJCapitalBill(Integer payApplyId, Integer capitalBillId) {
        log.info("保存付款申请与资金流水关系表,payApplyId:{},capitalBillId:{}", payApplyId, capitalBillId);
        rPayApplyJCapitalBillMapper.insertSelective(RPayApplyJCapitalBill.builder().payApplyId(payApplyId).capitalBillId(capitalBillId).build());
    }

    public List<CapitalBillDto> getReceivedAmountBySaleOrderId(Integer orderId) {
        return capitalBillMapper.getReceivedAmountBySaleOrderId(orderId);
    }
}
