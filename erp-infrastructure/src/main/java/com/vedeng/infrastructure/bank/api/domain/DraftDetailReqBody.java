package com.vedeng.infrastructure.bank.api.domain;

import cn.hutool.core.util.StrUtil;
import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import lombok.Data;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2024-09-09
 * @Description: 票据详细信息查询(B2eNbsDraftDetail)请求体
 * @Version: 1.0
 */
@Data
public class DraftDetailReqBody {
    /**
     * 客户基数请求流水号
     */
    private String trnId;

    /**
     * 签约账号
     */
    private String custAccount;

    /**
     * 票号
     */
    private String billNo;

    /**
     * 票子票区间起始
     */
    private String billRangeStart;

    /**
     * 子票区间截止
     */
    private String billRangeEnd;

    /**
     * 展示票面备注
     * 0：展示
     * 1：不展示
     * 不输不展示
     */
    private String showBillRemark;


    /**
     * 校验必填参数
     *
     * @return true 如果必填参数有效，false 如果缺失
     * @param response
     */
    public boolean validate(BankResponse response) {
        if (StrUtil.isBlank(trnId)) {
            //参数校验失败，通用返回
            response.checkParamResponseHeader(response,"trnId不能为空");
            return false;
        }
        if (StrUtil.isBlank(custAccount)) {
            //参数校验失败，通用返回
            response.checkParamResponseHeader(response,"custAccount不能为空");
            return false;
        }
        if (StrUtil.isBlank(billNo)) {
            //参数校验失败，通用返回
            response.checkParamResponseHeader(response,"billNo不能为空");
            return false;
        }
        if (StrUtil.isBlank(billRangeStart)) {
            //参数校验失败，通用返回
            response.checkParamResponseHeader(response,"billRangeStart不能为空");
            return false;
        }
        if (StrUtil.isBlank(billRangeEnd)) {
            //参数校验失败，通用返回
            response.checkParamResponseHeader(response,"billRangeEnd不能为空");
            return false;
        }
        return true;
    }
}
