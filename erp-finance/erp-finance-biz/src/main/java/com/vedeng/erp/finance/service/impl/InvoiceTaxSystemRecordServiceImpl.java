package com.vedeng.erp.finance.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.erp.finance.service.InvoiceTaxSystemRecordApiService;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.finance.constants.FinanceConstant;
import com.vedeng.erp.finance.domain.entity.InvoiceTaxSystemRecordEntity;
import com.vedeng.erp.finance.dto.InvoiceTaxSystemRecordDto;
import com.vedeng.erp.finance.enums.TaxesMappingEnum;
import com.vedeng.erp.finance.mapper.InvoiceTaxSystemRecordMapper;
import com.vedeng.erp.finance.mapstruct.InvoiceTaxSystemRecordConvertor;
import com.vedeng.erp.finance.service.InvoiceTaxSystemRecordService;
import com.vedeng.erp.finance.domain.dto.TaxesReturnInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class InvoiceTaxSystemRecordServiceImpl implements InvoiceTaxSystemRecordService, InvoiceTaxSystemRecordApiService {

    @Resource
    private InvoiceTaxSystemRecordMapper invoiceTaxSystemRecordMapper;

    @Autowired
    private InvoiceTaxSystemRecordConvertor invoiceTaxSystemRecordConvertor;



    @Override
    public InvoiceTaxSystemRecordDto isExist(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto) {
        return invoiceTaxSystemRecordConvertor.toDto(invoiceTaxSystemRecordMapper.findByBusinessIdAndInterfaceType(invoiceTaxSystemRecordDto.getBusinessId(),
                invoiceTaxSystemRecordDto.getInterfaceType()));
    }

    @Override
    public InvoiceTaxSystemRecordDto queryById(Long invoiceTaxSystemRecordId) {
        return invoiceTaxSystemRecordConvertor.toDto(invoiceTaxSystemRecordMapper.selectByPrimaryKey(invoiceTaxSystemRecordId));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void init(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto) {
        invoiceTaxSystemRecordMapper.insertSelective(invoiceTaxSystemRecordConvertor.toEntity(invoiceTaxSystemRecordDto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void success(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto) {
        invoiceTaxSystemRecordDto.setRunningStatus(FinanceConstant.SUCCESS);
        invoiceTaxSystemRecordMapper.insertOrUpdateSelective(invoiceTaxSystemRecordConvertor.toEntity(invoiceTaxSystemRecordDto));
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void fail(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto) {
        invoiceTaxSystemRecordDto.setRunningStatus(FinanceConstant.FAIL);
        InvoiceTaxSystemRecordEntity invoiceTaxSystemRecordEntity = invoiceTaxSystemRecordConvertor.toEntity(invoiceTaxSystemRecordDto);
        if (invoiceTaxSystemRecordEntity.getInvoiceTaxSystemRecordId() == null) {
            invoiceTaxSystemRecordMapper.insertSelective(invoiceTaxSystemRecordEntity);
        } else {
            invoiceTaxSystemRecordMapper.updateAndRetryNumAdd(invoiceTaxSystemRecordEntity);
        }
        invoiceTaxSystemRecordDto.setRetryNum(invoiceTaxSystemRecordEntity.getRetryNum());
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public InvoiceTaxSystemRecordDto fromTaxesApiInfoInitInvoiceTaxSystemRecord(ITaxesParam iTaxesParam,
                                                                                TaxesReturnInfo taxesReturnInfo) {
        InvoiceTaxSystemRecordDto dto = new InvoiceTaxSystemRecordDto();
        dto.setInterfaceType(TaxesMappingEnum.getInterfaceType(taxesReturnInfo.getClass()));
        dto.setIsDelete(ErpConstant.F);
        dto.setRunningStatus(taxesReturnInfo.getIsSuccess() ? FinanceConstant.SUCCESS : FinanceConstant.FAIL);
        dto.setRetryNum(ErpConstant.ZERO);
        dto.setBodyOrg(JSON.toJSONString(iTaxesParam));
        dto.setResult(JSON.toJSONString(taxesReturnInfo));
        return dto;
    }

    @Override
    public List<InvoiceTaxSystemRecordDto> findInvoiceTaxSystemRecordDto(InvoiceTaxSystemRecordDto invoiceTaxSystemRecordDto) {
        List<InvoiceTaxSystemRecordEntity> invoiceTaxSystemRecordEntities = invoiceTaxSystemRecordMapper
                .selectByAll(invoiceTaxSystemRecordConvertor.toEntity(invoiceTaxSystemRecordDto));
        return invoiceTaxSystemRecordConvertor.toDto(invoiceTaxSystemRecordEntities);
    }

    @Override
    public PageInfo<InvoiceTaxSystemRecordDto> queryDownPdfError(Page page) {
        return PageHelper.startPage(page.getPageNum(), page.getPageSize()).doSelectPageInfo(() -> invoiceTaxSystemRecordMapper.queryDownPdfError());
    }

    @Override
    public PageInfo<InvoiceTaxSystemRecordDto> queryDownXmlNeedDown(Page page) {
        return PageHelper.startPage(page.getPageNum(), page.getPageSize()).doSelectPageInfo(() -> invoiceTaxSystemRecordMapper.queryDownXmlNeedDown());
    }

    @Override
    public List<String> getPdfUrlsByInvoiceNos(Set<String> invoiceNos) {
        return invoiceTaxSystemRecordMapper.getPdfUrlsByInvoiceNos(invoiceNos);
    }

    @Override
    public List<String> getXmlUrlsByInvoiceNos(Set<String> invoiceNos) {
        return invoiceTaxSystemRecordMapper.getXmlUrlsByInvoiceNos(invoiceNos);
    }

    @Override
    public InvoiceTaxSystemRecordDto getByInvoiceNo(String invoiceNo, String interfaceType) {
        return invoiceTaxSystemRecordConvertor.toDto(invoiceTaxSystemRecordMapper.getByInvoiceNo(invoiceNo, interfaceType));
    }
}
