<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="出库详情" scope="application" />
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript"></script>
<div class="content">
    <div class="formtitle"></div>
    <div  id="desc_div">
        <form method="post" id="viewForm">
            <ul class="payplan">
                <div class="parts">
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">
                            基本信息
                        </div>
                    </div>
                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th style="width:80px">出库单号</th>
                            <th style="width:60px">出库状态</th>
                            <th style="width:60px">实际出库时间</th>
                            <th style="width:80px">申请类型</th>

                            <th style="width:80px">申请人</th>
                            <th style="width:80px">申请人部门</th>
                            <th style="width:80px">申请出库日期</th>
                        </tr>
                        </thead>
                        <tbody class="company">
                                <tr>
                                    <td> ${scrappedOut.orderNo} </td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${scrappedOut.outStatus eq 0}">
                                                未出库
                                            </c:when>
                                            <c:when test="${scrappedOut.outStatus eq 1}">
                                                部分出库
                                            </c:when>
                                            <c:when test="${scrappedOut.outStatus eq 2}">
                                                全部出库
                                            </c:when>
                                        </c:choose>
                                    </td>
                                    <td> <date:date value ="${realOutputTime}" format="yyyy-MM-dd HH:mm:ss"/></td>

                                    <td>
                                        <c:forEach items="${applyTypeList}" var="applyType">
                                            <c:if test="${applyType.sysOptionDefinitionId eq scrappedOut.wmsOutputOrderExtra.applyType}">
                                                ${applyType.title}
                                            </c:if>
                                        </c:forEach>
                                    </td>

                                    <td> ${scrappedOut.creator} </td>
                                    <td> ${scrappedOut.belongDepartment} </td>
                                    <td> ${scrappedOut.appleOutDate}  </td>
                                </tr>
                        </tbody>
                    </table>
                </div>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">产品信息</div>
                        </div>
                        <table class="table">
                            <thead>
                            <tr>
                                <th style="width:80px">序号</th>
                                <th style="width:80px">订货号</th>
                                <th style="width:80px">产品名称</th>
                                <th style="width:80px">品牌</th>
                                <th style="width:80px">型号</th>
                                <th style="width:80px">需出库数量</th>
                                <th style="width:80px">出库数量</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tbody id="thisTimeUpdateTbody">
                            <c:forEach var="ordergoods" items="${scrappedOutGoodList}" varStatus="staut">
                                <tr>
                                    <td>${staut.count}</td>
                                    <td>${ordergoods.skuNo}</td>
                                    <td>${ordergoods.showName}</td>
                                    <td>${ordergoods.brandName}</td>
                                    <td>${ordergoods.model}</td>
                                    <td>${ordergoods.outputNum}</td>
                                    <td>${ordergoods.alreadyOutputNum}</td>
                                </tr>
                            </c:forEach>
                            </tbody>
                            </tbody>
                        </table>

                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor ">出库记录</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
<%--                                <th style="width:80px">出库单据</th>--%>
                                <th class="wid5">序号</th>
                                <th style="width:80px">订货号</th>
                                <th style="width:80px">产品名称</th>
                                <th style="width:50px">品牌</th>
                                <th style="width:50px">型号</th>
                                <th style="width:50px">实际出库数量</th>
                                <th style="width:30px">单位</th>
                                <th style="width:80px">贝登批次码</th>
                                <th style="width:80px">生产日期</th>
                                <th style="width:80px">有效期至</th>
                                <th style="width:80px">出库时间</th>
                                <th style="width:80px">入库日期</th>
                                <th style="width:80px">产品生产批次</th>
                                <th style="width:80px">SN码</th>
                                <th style="width:80px">灭菌批号</th>
                                <th style="width:80px">注册证号</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="wlist" items="${wlogList}" varStatus="num">
                                <tr>
<%--                                    <td>--%>
<%--                                    <a class="addtitle"--%>
<%--                                       href="javascript:void(0);"--%>
<%--                                       tabTitle='{ "link":"./warehouse/warehousesout/detail.do?outInNo=${wlist.outInNo}&outInType=${wlist.operateType}","title":"出库单详情页"}'>${wlist.outInNo}</a>--%>
<%--                                    </td>--%>
                                    <td>${num.count}</td>
                                    <td>${wlist.sku}</td>
                                    <td class="text-left">
                                        <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${wlist.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${wlist.goodsId}","title":"产品信息"}'>${wlist.goodsName}</a>
                                    </td>
                                    <td>${wlist.brandName}</td>
                                    <td>${wlist.model}</td>
                                        <%--实际收货数量--%>
                                    <td>${-wlist.num}</td>
                                    <td>${wlist.unitName}</td>
                                    <td>${wlist.vedengBatchNumer}</td>
                                        <%--生产日期--%>
                                    <td><date:date value ="${wlist.productDate}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                    <td><date:date value ="${wlist.expirationDate}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                        <%--出库日期--%>
                                    <td><date:date value ="${wlist.addTime}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                        <%--入库日期--%>
                                    <td><date:date value ="${wlist.checkStatusTime}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                        <%--产品生产批次--%>
                                    <td>${wlist.batchNumber}</td>
                                        <%--SN码--%>
                                    <td>${wlist.barcodeFactory}</td>
                                    <td>${wlist.sterilizationBatchNo}</td>
                                        <%--注册证号--%>
                                    <td>${wlist.registrationNumber}</td>
                                </tr>
                            </c:forEach>
                            <!-- 查询无结果弹出 -->
                            <c:if test="${empty wlogList}">
                                <tr>
                                    <td colspan="16">暂无出库记录。</td>
                                </tr>
                            </c:if>
                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor ">备注信息</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:80px">备注</th>
                            </tr>
                            </thead>
                            <tbody>
                                <c:choose>
                                    <c:when test="${scrappedOut.remark != null && scrappedOut.remark !=''}">
                                        <tr>
                                            <td colspan="1">${scrappedOut.remark}</td>
                                        </tr>
                                    </c:when>
                                    <c:otherwise>
                                        <tr>
                                            <td colspan="1">暂无备注信息。</td>
                                        </tr>
                                    </c:otherwise>
                                </c:choose>

                            </tbody>
                        </table>
                    </div>
                </li>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor ">物流信息</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">快递公司</th>
                                <th style="width:80px">快递单号</th>
                                <th style="width:80px">发货时间</th>
                                <th style="width:80px">商品</th>
                                <th style="width:50px">快递状态</th>
                                <th style="width:80px">运费总额</th>
                                <th style="width:80px">商品总数</th>
                                <th style="width:80px">已发货总数</th>
                                <th style="width:80px">待发货数量</th>
                                <th style="width:80px">操作</th>
                            </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="10">暂无物流信息。</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor ">收货信息</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td colspan="10">暂无收货信息。</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor ">其他信息</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <tbody>
                            <tr>
                                <td colspan="10">暂无其他信息。</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
            </ul>
        </form>
    </div>
</div>
<%@ include file="../../common/footer.jsp"%>