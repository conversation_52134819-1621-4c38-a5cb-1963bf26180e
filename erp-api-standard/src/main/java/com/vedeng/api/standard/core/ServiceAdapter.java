package com.vedeng.api.standard.core;

import com.vedeng.api.standard.internal.InternalCallResult;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 服务适配器接口
 * 定义统一的业务服务适配器规范
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
public interface ServiceAdapter {
    
    /**
     * 执行业务操作
     * 
     * @param action 操作名称（如：create、update、delete、query等）
     * @param request 统一请求对象
     * @return 业务执行结果
     * @throws Exception 业务执行异常
     */
    Object execute(String action, ApiRequest request) throws Exception;
    
    /**
     * 获取适配器支持的模块名称
     * 
     * @return 模块名称
     */
    default String getModuleName() {
        // 默认从类名中提取模块名称
        String className = this.getClass().getSimpleName();
        if (className.endsWith("ServiceAdapter")) {
            return className.substring(0, className.length() - "ServiceAdapter".length()).toLowerCase();
        }
        return className.toLowerCase();
    }
    
    /**
     * 获取适配器支持的操作列表
     *
     * 注意：使用 AbstractServiceAdapter 的子类会自动实现此方法
     *
     * @return 支持的操作名称数组
     */
    String[] getSupportedActions();

    /**
     * 获取不需要身份认证的操作列表
     *
     * 默认情况下，所有操作都需要身份认证（安全优先原则）
     * 各个业务模块可以根据实际需要覆盖此方法，声明哪些操作不需要认证
     *
     * 典型的不需要认证的操作：
     * - query: 查询操作
     * - detail: 详情查看
     * - search: 搜索操作
     *
     * @return 不需要认证的操作名称数组，默认为空数组（所有操作都需要认证）
     */
    default String[] getNoAuthActions() {
        return new String[0];
    }
    
    /**
     * 检查是否支持指定的操作
     *
     * 注意：使用 AbstractServiceAdapter 的子类会自动实现此方法
     *
     * @param action 操作名称
     * @return 是否支持
     */
    boolean supportsAction(String action);
    

    
    /**
     * 预处理请求
     * 子类可以重写此方法来实现特定的预处理逻辑
     * 
     * @param action 操作名称
     * @param request 请求对象
     */
    default void preProcess(String action, ApiRequest request) {
        // 默认实现为空，子类可以重写
    }
    
    ///**
    // * 后处理响应
    // * 子类可以重写此方法来实现特定的后处理逻辑
    // *
    // * @param action 操作名称
    // * @param request 请求对象
    // * @param result 执行结果
    // * @return 处理后的结果
    // */
    //default Object postProcess(String action, ApiRequest request, Object result) {
    //    // 直接透传结果，不进行任何处理
    //    // InternalCallResult 将由 ResponseProcessor 统一处理
    //    return result;
    //}


    /**
     * 后处理响应
     * 子类可以重写此方法来实现特定的后处理逻辑
     *
     * @param action 操作名称
     * @param request 请求对象
     * @param result 执行结果
     * @return 处理后的结果
     */
    default Object postProcess(String action, ApiRequest request, Object result) {
        // 处理 InternalCallResult（兼容旧版本或特殊场景）
        if (result instanceof InternalCallResult) {
            return processInternalCallResult((InternalCallResult) result, action, request);
        }

        // 如果不是 InternalCallResult，直接返回
        return result;
    }

    /**
     * 处理 InternalCallResult 的通用逻辑
     * 子类通常不需要重写此方法
     *
     * @param callResult InternalCallResult 对象
     * @param action 操作名称
     * @param request 请求对象
     * @return 处理后的结果
     */
    default Object processInternalCallResult(InternalCallResult callResult, String action, ApiRequest request) {
        if (!callResult.isSuccess()) {
            // 调用失败时抛出异常
            String errorMessage = callResult.getErrorMessage();
            if (errorMessage == null || errorMessage.trim().isEmpty()) {
                errorMessage = "内部调用失败";
            }
            throw new RuntimeException(errorMessage);
        }

        Object businessData = callResult.getData();
        if (businessData == null) {
            return null;
        }

        // 直接返回业务数据，无论什么类型
        return businessData;
    }


}
