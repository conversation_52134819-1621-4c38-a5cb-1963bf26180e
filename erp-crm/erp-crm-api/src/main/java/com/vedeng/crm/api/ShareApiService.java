package com.vedeng.crm.api;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.common.dto.RSalesJBusinessOrderDto;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/1/10
 */
public interface ShareApiService {
    /**
     * 分享
     * @param rSalesJBusinessOrderDto
     */
    void shareBusiness(RSalesJBusinessOrderDto rSalesJBusinessOrderDto, CurrentUser currentUser);

    void shareAutoBusiness(RSalesJBusinessOrderDto rSalesJBusinessOrderDto, CurrentUser currentUser);

    /**
     * 取消分享
     * @param bussinessId
     */
    void cancelShare(Integer bussinessId,CurrentUser currentUser);


    /**
     * 查询分享人列表
     * @param businessId
     * @param businessType
     * @return
     */
    List<RSalesJBusinessOrderDto> getShareListByBusinessId(Integer businessId, Integer businessType);


    /**
     * 是否存在某条分享记录
     * @param businessId
     * @param businessType
     * @param saleUserId
     * @return
     */
    RSalesJBusinessOrderDto getShareListByBusinessIdAndUserId(Integer businessId,Integer businessType,Integer saleUserId);

}
