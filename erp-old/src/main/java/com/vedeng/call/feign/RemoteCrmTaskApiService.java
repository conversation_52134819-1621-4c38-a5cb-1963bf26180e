package com.vedeng.call.feign;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import com.vedeng.doc.api.docsupplier.command.SupplierDocCommand;
import com.vedeng.doc.api.dto.DataResult;
import feign.Headers;
import feign.RequestLine;
import lombok.*;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: crm任务接口
 * @date 2024/9/30 10:51
 */
@FeignApi(serverName = ServerConstants.CRM_SERVER)
public interface RemoteCrmTaskApiService {


    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /crm/task/profile/save")
    R<Void> saveTask(@RequestBody TaskDto taskDto);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /crm/task/profile/autoHandle")
    R<Void> autoHandle(@RequestBody TaskDto taskDto);

    /**
     * 关闭商机时自动关闭商机下的所有任务，赋三个值，bizId,apiUserId,apiUserName
     * @param taskDto
     * @return
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /crm/task/profile/closeTaskForBusinessChance")
    R<Void> closeTaskForBusinessChance(@RequestBody TaskDto taskDto);

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class TaskDto extends BaseDto {
        /**
         * 主键
         */
        private Long taskId;

        /**
         * 任务内容
         */
        private String taskContent;

        /**
         * 业务类型，1：商机 2：线索 3：报价
         */
        private Integer bizType;

        /**
         * 业务ID
         */
        private Integer bizId;

        /**
         * 任务类型
         * 1.初步产品方案
         * 2.单品询价
         * 3.综合询价
         * 4.商机督导
         * 5.再次跟进
         */
        private Integer mainTaskType;

        /**
         * 商机督导-督导类型
         */
        private String subTaskType;

        /**
         * 处理状态：0处理中 1已处理 2关闭
         */
        private Integer doneStatus;

        /**
         * 提交时间
         */
        private Date commitTime;

        /**
         * 截止时间
         */
        private Date deadline;

        /**
         * 待办人
         */
        private List<Integer> todoUserList;

        /**
         * 业务编号
         */
        private String bizNo;

        /**
         * 通过API接口传过去的用户信息-userId
         */
        private Integer apiUserId;
        /**
         * 通过API接口传过去的用户信息-userName
         */
        private String apiUserName;
    }
}
