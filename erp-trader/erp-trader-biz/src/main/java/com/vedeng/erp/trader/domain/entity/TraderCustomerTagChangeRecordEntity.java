package com.vedeng.erp.trader.domain.entity;

import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * @description 客户标签更新记录
 * <AUTHOR>
 * @date 2023/8/17 15:00
 **/

@Getter
@Setter
@NoArgsConstructor
public class TraderCustomerTagChangeRecordEntity extends BaseEntity {

    /**
     * 主键
     */
    private Integer traderCustomerTagChangeId;

    /**
     * 交易者id
     */
    private Integer traderId;

    /**
     * 客户id
     */
    private Integer traderCustomerId;

    /**
     * 数据来源（0:用户  1:系统）
     */
    private Integer source;

    /**
     * 操作时间
     */
    private Date operTime;

    /**
     * 模块操作类型
     * 0 新增
     * 1 修改
     * 2 删除
     */
    private Integer operType;

    /**
     * 标签模块名称
     */
    private String tagModelName;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 原始标签记录值 （逗号分割）
     */
    private String oldTagLabel;

    /**
     * 新标签记录值 （逗号分割）
     */
    private String newTagLabel;

    /**
     * 记录变化日志内容
     */
    private String tagChangeLog;
}