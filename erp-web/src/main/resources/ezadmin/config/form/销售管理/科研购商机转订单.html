<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>科研购创建订单</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="createOrder" datasource="erp-datasourcetarget" success_url=""> 
  <div id="APPEND_HEAD" class="append"></div> 
  <div class="layui-container"> 
   <form id="inputForm" method="post" class="layui-form"> 
    <div class="layui-card" group_name="EZ_DEFAULT_GROUP"> 
     <div class="layui-card-header">
       EZ_DEFAULT_GROUP 
     </div> 
     <div class="layui-card-body"> 
      <div class="layui-form-item ">
       <label class="layui-form-label">客户名称</label>
       <div class="layui-input-block form-group">
        <button class="form-item" name="traderId" url="/ezadmin/list/list-traderCustomerSearch" type="single" value="" item_name="traderId" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="required" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload=""></button>
       </div>
      </div>
      <div class="layui-form-item "><label class="layui-form-label">联系人</label>
       <div class="layui-input-block form-group"><button class="form-item" name="traderContactId" url="/ezadmin/list/list-traderContactSearch" type="single" value="" item_name="traderContactId" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload=""></button>
       </div>
      </div>
      <div class="layui-form-item ">
       <label class="layui-form-label">地址</label>
       <div class="layui-input-block form-group">
        <button class="form-item" name="traderAddressId" url="/ezadmin/list/list-traderAddressSearch" type="single" value="" item_name="traderAddressId" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload=""></button>
       </div>
      </div>
      <div class="layui-form-item ">
       <label class="layui-form-label">活动ID</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="activityPreOrderId" type="span" value="" item_name="activityPreOrderId" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
     </div> 
    </div> 
   </form> 
   <form id="configForm" class="layui-form"> 
    <div class="layui-card" group_name="表单数据配置"> 
     <div class="layui-card-header">
       表单数据配置 
     </div> 
     <div class="layui-card-body"> 
     </div> 
    </div> 
    <div class="layui-form-item "><label class="layui-form-label">初始表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">1</pre> 
     </div> 
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">提交表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">1</pre> 
     </div> 
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">删除表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type=""></pre> 
     </div> 
    </div>
    <div class="layui-form-item "><label class="layui-form-label">分组表达式</label> 
     <div class="layui-input-block form-group"> 
      <pre class="form-item layui-code" name="GROUP_DATA" type=""></pre> 
     </div> 
    </div>
   </form> 
  </div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
  <div id="APPEND_FOOT" class="append">
   <script>
        var btn1,btn2;
        var url1 ;
        var url2 ;
        $(function(){
            btn1=$("button[item_name=traderContactId]");
            btn2=$("button[item_name=traderAddressId]");
            url1=btn1.attr("item_url");
           url2=btn2.attr("item_url");
           btn1.hide();
           btn2.hide();
           $("#formSubmitUrl").val("/orderstream/saleorder/saveSaleorderInfo.do");
           $("#inputForm").attr("action","/orderstream/saleorder/saveSaleorderInfo.do")
           $("#inputForm").validate().destroy();
        })

         function traderSearchCallBack(ids,lines){
           // alert(ids+""+lines)
          var ja=JSON.parse(lines);
          if(ja.length<=0){
              return;
          }
           var json=JSON.parse(lines)[0];

           $("[item_name=span-traderId]").text(json.TRADER_NAME);
           $("input[name=traderId]").val(json.TRADER_ID);

            btn1.attr("item_url",url1+"?TRADER_ID="+json.TRADER_ID);
            btn2.attr("item_url",url2+"?TRADER_ID="+json.TRADER_ID);
          btn1.show();
          btn2.show();
            layer.closeAll();
         }
         function traderContactCallBack(ids,lines){
           var ja=JSON.parse(lines);
           if(ja.length<=0){
            return;
           }
           var json=JSON.parse(lines)[0];


          $("[item_name=span-traderContactId]").text(json.NAME+"/"+json.MOBILE);
          $("input[name=traderContactId]").val(json.ID);
          layer.closeAll();

         }

        function traderAddressCallBack(ids,lines){
         var ja=JSON.parse(lines);
         if(ja.length<=0){
          return;
         }
         var json=JSON.parse(lines)[0];


         $("[item_name=span-traderAddressId]").text(json.REGION+"/"+json.ADDRESS);
         $("input[name=traderAddressId]").val(json.ID);
         layer.closeAll();

        }

       </script>
  </div> 
 </body>
</html>