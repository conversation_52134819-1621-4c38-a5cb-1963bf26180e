<%--
  Created by IntelliJ IDEA.
  User: ckt
  Date: 2023/11/13
  Time: 09:49
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<html>
<head>
    <title>售后订单开票申请</title>
</head>

<style>
    .el-input.is-disabled .el-input__inner {
        color: #696B6F;
    }
    .el-textarea.is-disabled .el-textarea__inner {
        color: #696B6F;
    }
</style>
<body>
<div id="app">
    <div>申请开票</div>
    <div class="invoice_body">
        <el-radio v-model="invoiceInfoType" label="0">标准开票信息</el-radio>
        <el-radio v-model="invoiceInfoType" label="1">
            自定开票信息
            <el-tooltip class="item" effect="dark" placement="right-end" >
                <template v-slot:content>
                    <div>可修改项目：产品名称，规格型号，开票备注</div>
                    <div>1. 产品名称、规格型号仅可按合同或注册证进行修改</div>
                    <div>2. 开票备注即票面备注信息，可自行修改</div>
                </template>
                <i class="el-icon-warning-outline"></i>
            </el-tooltip>
        </el-radio>
    </div>
    <div class="invoice_table" style="position: fixed;overflow: auto;height: 80%;width: 99%;">
        <el-table
                :data="afterSaleApplyInfoDto.afterSaleApplyInfoGoodsDtos"
                border
                empty-text="暂无信息"
                fit
                @selection-change="handleSelectionChange"
                :summary-method="getSummaries"
                show-summary
                :header-cell-style="{background: '#E3ECD9FF'}">
            <el-table-column type="selection" align="center" width="60"></el-table-column>
            <el-table-column align="center" prop="goodsName">
                <template slot="header" slot-scope="scope">
                    <span style="color: red;">*</span> 产品名称
                </template>
                <template slot-scope="scope">
                    <el-input size="small" v-model="scope.row.goodsName" placeholder="请输入产品名称" :disabled="invoiceInfoType == 0 || !scope.row.selected" maxlength="100"></el-input>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="spec" label="规格型号" width="150">

                <template slot="header" slot-scope="scope">
                    规格型号
                </template>
                <template slot-scope="scope">
                    <el-input size="small" v-model="scope.row.spec" placeholder="请输入规格型号" :disabled="invoiceInfoType == 0 || !scope.row.selected" maxlength="40"></el-input>
                </template>

            </el-table-column>
            <el-table-column align="center" prop="unitName" label="单位" width="60"></el-table-column>
            <el-table-column align="center" prop="price" label="单价" width="70"></el-table-column>
            <el-table-column align="center" prop="appliedNum" label="已开票数量" width="70"></el-table-column>
            <el-table-column align="center" prop="TNNum" width="100">
                <template slot="header" slot-scope="scope">
                    <span>收货T+N未开票数量</span>
                    <el-tooltip placement="right">
                        <div slot="content">七天无理由商品：T+8<br/>其他商品：T+4</div>
                        <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="TNDateStr" label="满足收货T+N日期" width="100"></el-table-column>
            <el-table-column align="center" prop="maxCanApplyNum" label="剩余未开票数量" width="80"></el-table-column>
            <el-table-column align="center" prop="applyNum" label="申请数量" width="152"></el-table-column>
            <el-table-column align="center" prop="applyAmount" label="申请金额" width="60"></el-table-column>
        </el-table>
        <el-divider></el-divider>

        <div class="comments">
            <span style="padding-right: 10px">票面备注</span> <el-button type="text" :disabled="invoiceInfoType == 0" @click="editComments = true">编辑</el-button>
            <el-input v-model="comments"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入开票备注"
                      style="padding-top: 5px"
                      :disabled="true"
                      maxlength="200" show-word-limit>
            </el-input>
        </div>
        <div class="invoiceMessage">
            <span style="padding-right: 10px">开票留言</span>
            <el-input v-model="invoiceMessage"
                      type="textarea"
                      :rows="3"
                      placeholder="告知财务开票的注意事项"
                      style="padding-top: 5px"
                      maxlength="200" show-word-limit>
            </el-input>
        </div>
    </div>
    <div style="position: fixed;bottom: 10px;right: 30px">
        <el-button type="success" plain @click="submit()" :disabled="submitDisabled">申请</el-button>
        <el-button type="success" plain @click="preview()">预览</el-button>
        <el-button type="primary" plain @click="cancel()">取消</el-button>
    </div>

    <%--提前开票申请--%>
    <el-dialog
            title="提前申请"
            class="advance_class"
            :visible.sync="advancedVisible"
            :before-close="advancedVisibleClose"
            :show-close="false"
            :fullscreen="true">
        <div style="height:20px;background-color: #E3ECD9FF">申请检查未通过，如需继续申请，请填写以下未通过项目说明</div>
        <div v-for="detailDto in invoiceCheckResultDetailDtoList" style="padding-top: 20px">
            <span style="color: red">*</span>{{detailDto.ruleName}} <span class="tipClass">{{detailDto.ruleContent}}</span>
            </br>{{detailDto.promptText}}
            <el-input v-model="detailDto.applyReason"
                      type="textarea"
                      :rows="2"
                      style="padding-top: 5px"
                      maxlength="200" show-word-limit>
            </el-input>
        </div>
        <div style="padding-top: 20px">
            <span style="color: red">*</span>提前开票原因
            <el-input v-model="advanceValidReason"
                      type="textarea"
                      :rows="2"
                      style="padding-top: 5px"
                      maxlength="200" show-word-limit>
            </el-input>
        </div>
        <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="submitAdvanceApply()" :disabled="applyDisabled">申请</el-button>
    <el-button @click="cancelAdvanceApply">取消</el-button>
  </span>
    </el-dialog>
    <el-dialog
            title="预览页面"
            :show-close="false"
            :fullscreen="true"
            :visible.sync="previewVisible"
            width="50%"
    >
        <!-- 这里显示接口返回的内容 -->
        <div v-html="dialogContent"></div>
        <el-button
                size="medium"
                type="primary"
                plain
                @click="cancelDialogContent"
                style="position: fixed; bottom: 20px; right: 20px;"
        >
            退 出
        </el-button>
    </el-dialog>
    <%--编辑票面备注dialog--%>
    <el-dialog
            title="编辑票面备注"
            :visible.sync="editComments"
            width="45%"
            :before-close="beforeClose">
        <p style="background-color: rgb(233, 233, 235);margin-top: -15px;line-height: 2.4">
            请注意，<span style="color: #2c99ae;">票面备注</span>将在票据上直接显示，用于提供有关票据的额外信息或特殊要求。
            请确保您填写的备注已与客户进行确认。如果只是告知财务开票的注意事项请填写在<span style="color: #2c99ae;">开票留言</span>中，不要填写在<span style="color: #2c99ae;">票面备注</span>中。
        </p>
        <span style="padding-right: 20px">{{oldComments}}</span> <el-checkbox v-model="fixedComment">固定备注</el-checkbox></br>
        <div style="padding-top: 10px;padding-bottom: 8px"><span style="color: red">*</span>票面备注</div>
        <el-input v-model="appendComment"
                  type="textarea"
                  :rows="3"
                  placeholder="请填写票面备注"
                  style="padding-top: 5px"
                  :disabled="false"
                  maxlength="150" show-word-limit>
        </el-input>
        <span slot="footer" class="dialog-footer">
                <el-button size="medium" type="success" plain @click="submitComment">确 定</el-button>
                <el-button size="medium" type="primary" plain @click="cancelComment">取 消</el-button>
            </span>
    </el-dialog>

    <el-dialog
            title="预览页面"
            :show-close="false"
            :fullscreen="true"
            :visible.sync="previewVisible"
            width="50%">
        <!-- 这里显示接口返回的内容 -->
        <div v-html="dialogContent"></div>
        <el-button
                size="medium"
                type="primary"
                plain
                @click="cancelDialogContent"
                style="position: fixed; bottom: 20px; right: 20px;"
        >
            退 出
        </el-button>
    </el-dialog>
</div>
</body>

<script src="${pageContext.request.contextPath}/static/api/invoiceApply/invoice_apply_aftersale.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">

    let afterSalesId = ${afterSalesId};

    let vm = new Vue({
        el: '#app',
        data() {
            return {
                afterSaleApplyInfoDto: {},
                invoiceInfoType: '0',
                comments: '',
                oldComments: '',
                submitDisabled: false,
                totalAmount: 0,
                editComments: false,
                fixedComment: true,
                appendComment: '',
                invoiceMessage: '',
                appliedNumTotal: 0,
                TNNumTotal: 0,
                appliedNum: 1,
                maxCanApplyNumTotal: 0,
                applyNum: 0,
                applyTotal: 0.00,
                advancedVisible: false,
                applyDisabled: false,
                /*财务资质*/
                financeCertification: {
                    pass: true, //财务资质检查是否通过
                    specialInvoice: false, //是否专票

                },
                /*申请校验不通过list*/
                invoiceCheckResultDetailDtoList: null,
                advanceValidReason: '',
                previewVisible: false,
                dialogContent: ''
            }
        },

        mounted() {
            loadingApp();
            this.initData(afterSalesId)
        },


        methods: {
            cancelDialogContent(){
                this.previewVisible = false;
            },
            preview(){
                let filteredArr = this.afterSaleApplyInfoDto.afterSaleApplyInfoGoodsDtos.filter(obj => obj.selected);
                if (filteredArr.length == 0) {
                    this.$message.error("请至少勾选一条数据");
                    return false;
                }
                this.previewVisible = true;
                let previewInvoiceVo = {
                    comments: this.comments,
                    afterSalesId: this.afterSaleApplyInfoDto.afterSalesId,
                    goodsList: filteredArr
                };

                $.ajax({
                    async: false,
                    url: page_url + "/finance/invoice/previewInvoice.do",
                    data: JSON.stringify(previewInvoiceVo),  // 将对象转为JSON字符串
                    type: "POST",
                    dataType: "html",
                    contentType: "application/json",  // 指定发送的数据是JSON格式
                    success: (data) => {
                        this.dialogContent = data;

                    },
                    error: function (data) {
                        if (data.status == 1001) {
                            layer.alert("当前操作无权限");
                        }
                    }
                });
            },
            advancedVisibleClose(){

            },
            /*提前申请确认*/
            submitAdvanceApply(){
                this.applyDisabled = true;
                var saveValid = true;
                this.invoiceCheckResultDetailDtoList.some(d => {
                    if (!d.applyReason){
                        this.$message.error('请填写'+d.ruleName+'申请内容')
                        saveValid = false;
                        this.applyDisabled = false;
                        return true;
                    }
                })
                if (saveValid && !this.advanceValidReason){
                    this.$message.error('请填写提前开票原因')
                    this.applyDisabled = false;
                    saveValid = false;
                }
                if (saveValid){
                    this.saveInvoice();
                    setTimeout(() => {
                        this.applyDisabled = false;
                    }, 1500);
                }
            },
            /*提前申请取消*/
            cancelAdvanceApply(){
                this.submitDisabled = false;
                this.advancedVisible = false;
            },
            calTotalAmount(){
                this.applyTotal = 0.00;
                this.applyNum = 0;
                this.afterSaleApplyInfoDto.afterSaleApplyInfoGoodsDtos.forEach(g =>{
                    if (g.selected) {
                        this.applyNum = 1;
                        this.applyTotal = g.applyAmount;
                    }
                })
            },
            getSummaries(param){
                const sums = [];
                param.columns.forEach((column,index) => {
                    if(index === 0){
                        sums[index] = '合计';
                        return;
                    }
                    sums[5] = this.appliedNumTotal;
                    sums[6] = this.TNNumTotal;
                    sums[8] = 1;
                    sums[9] = this.applyNum;
                    sums[10] = this.applyTotal;
                })
                return sums;
            },
            cancelComment(){
                this.editComments = false
            },
            submitComment(){
                if (this.appendComment.trim() == ''){
                    this.$message.error("请填写票面备注");
                    return;
                }
                this.comments = this.fixedComment ? (this.oldComments + '\n' + this.appendComment) : this.appendComment
                this.editComments = false
            },
            beforeClose(done){
                this.fixedComment = true
                done();
            },
            handleSelectionChange(selectedRows) {
                this.afterSaleApplyInfoDto.afterSaleApplyInfoGoodsDtos.forEach(row => {
                    row.selected = selectedRows.includes(row);
                });
                this.calTotalAmount()
            },
            // 页面初始化数据加载
            initData(afterSalesId) {

                afterSaleApplyInfo({"afterSalesId": afterSalesId})
                    .then(res => {
                        if (res.data.code !== 0) {
                            this.$message.error("数据异常：" + res.data.message);
                            return;
                        }

                        this.afterSaleApplyInfoDto = res.data.data;
                        this.comments = this.afterSaleApplyInfoDto.invoiceComments;
                        this.oldComments = this.afterSaleApplyInfoDto.invoiceComments;
                        this.afterSaleApplyInfoDto.afterSaleApplyInfoGoodsDtos.forEach(g => {
                            g.oldGoodsName = g.goodsName
                            g.spec = "";
                            g.oldSpec = "";
                            g.appliedNum = 0;
                            g.TNNum = 0;
                            g.maxCanApplyNum = 1;
                            this.totalAmount += g.applyAmount;
                        });

                    })
                    .catch(err => {
                        this.$message.error("系统异常：" + err.message);
                    });

            },

            cancel() {
                layer.closeAll();
                parent.layer.closeAll();
            },

            async submit() {
                //校验
                //校验
                var goodsCheck = 0;
                var selectedNum = 0; //勾选条数
                this.afterSaleApplyInfoDto.afterSaleApplyInfoGoodsDtos.forEach(g => {
                    if (g.selected){
                        selectedNum += 1;
                    }
                    if (g.selected && g.applyNum == 0){
                        this.$message.error('存在选则数据数量为0，请检查');
                        goodsCheck = 1;
                    }
                    if (!g.goodsName || g.goodsName.trim() == ''){
                        this.$message.error('产品名称不能为空');
                        goodsCheck = 1;
                    }
                })
                if (selectedNum == 0){
                    this.submitDisabled = false;
                    this.$message.error('请至少选择一行数据')
                    return;
                }
                if (goodsCheck == 1){
                    this.submitDisabled = false;
                    return;
                }

                var continueSubmit = false;
                //是否编辑了票面备注
                if(this.oldComments != this.comments) {
                    await this.$confirm('你修改了票面备注，请注意' +
                        '</br></br>' +
                        '1  <span style="color: green">票面备注</span>将在票据上直接显示，用于提供有关票据的额外信息或特殊要求。请确保您填写' +
                        '的备注已与客户进行确认。如果只是告知财务开票的注意事项请填写在<span style="color: green">开票留言</span>中，不要填写在<span style="color: green">票面备注</span>中。' +
                        '</br></br>' +
                        '2  固定备注内容对财务十分重要，除非客户坚决不同意在备注栏中显示我公司单号信息，否则请不要取消勾选。' +
                        '</br></br>' +
                        '请确认是否继续提交？', '注意', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'info',
                        dangerouslyUseHTMLString: true
                    }).then(() => {
                        continueSubmit = true
                    }).catch(() => {

                    })
                }
                if (this.oldComments != this.comments && !continueSubmit){
                    this.submitDisabled = false;
                    return
                }

                //校验并发策略
                var enableOpenInvoice = true;
                var errorMsg = '';
                $.ajax({
                    async:false,
                    url:page_url + "/invoice/api/enableOpenInvoice.do",
                    data:{
                        "orderId": this.afterSaleApplyInfoDto.afterSalesId,
                        "orderType": 2,
                    },
                    type:"POST",
                    dataType : "json",
                    success:(data) =>{
                        if (!data.data.pass){
                            enableOpenInvoice = false;
                            errorMsg = data.data.errorMsg;
                        }
                    },error:function(data){
                        if(data.status ==1001){
                            layer.alert("当前操作无权限")
                        }
                    }
                })
                if (!enableOpenInvoice){
                    this.$message.error(errorMsg);
                    this.submitDisabled = false;
                    return;
                }
                //是否提前申请
                var advancedApply = true;
                //组装参数
                var detailList =  [];
                this.afterSaleApplyInfoDto.afterSaleApplyInfoGoodsDtos.forEach(g => {
                    let detail = {};
                    detail.detailGoodsId = g.afterSalesGoodsId;
                    detail.num = g.applyNum;
                    detail.totalAmount = (g.applyAmount).toFixed(2);
                    detail.price = g.price;
                    detail.productName = g.goodsName;
                    detail.specModel = g.spec;
                    detail.unit = g.unitName;
                    if (detail.num > 0){
                        detailList.push(detail);
                    }
                })
                $.ajax({
                    async:false,
                    url:page_url + "/invoice/api/advanceApply.do",
                    data:JSON.stringify({
                        "relatedId": this.afterSaleApplyInfoDto.afterSalesId,
                        "type": 504,
                        "invoiceInfoType": this.invoiceInfoType,
                        "invoiceMessage": this.invoiceMessage,
                        "detailList": detailList
                    }),
                    type:"POST",
                    dataType : "json",
                    contentType:'application/json',
                    success:(data) =>{
                        if (data.data.success){
                            advancedApply = false;
                        }else {
                            this.invoiceCheckResultDetailDtoList = data.data.invoiceCheckResultDetailDtoList
                        }
                    },error:function(data){
                        if(data.status ==1001){
                            layer.alert("当前操作无权限")
                        }
                    }
                })
                if (advancedApply){
                    this.advancedVisible = true;
                }else {
                    this.saveInvoice();
                }
            },

            saveInvoice(){
                //组装参数
                let afterSaleApplyDto = this.afterSaleApplyInfoDto;
                afterSaleApplyDto.totalAmount = this.totalAmount;
                afterSaleApplyDto.invoiceInfoType = this.invoiceInfoType;
                afterSaleApplyDto.invoiceComments = this.comments;
                afterSaleApplyDto.invoiceCheckResultDetailDtoList = this.invoiceCheckResultDetailDtoList;
                afterSaleApplyDto.advanceValidReason = this.advanceValidReason
                afterSaleApplyDto.invoiceMessage = this.invoiceMessage
                //发送请求
                this.submitDisabled = true;
                afterSaleApply(afterSaleApplyDto)
                    .then(res => {
                        if (res.data.code !== 0) {
                            this.$message.error("数据异常：" + res.data.message);
                            this.submitDisabled = false;
                            return;
                        }
                        this.$message.success("提交成功");
                        // 设置延时关闭弹窗和刷新页面
                        setTimeout(() => {
                            this.submitDisabled = false;
                            parent.layer.closeAll();
                            parent.window.location.reload();
                        }, 2000); // 3000 毫秒后执行
                    })
                    .catch(err => {
                        this.submitDisabled = false;
                        this.$message.error("系统异常：" + err.message);
                    });
            }
        },
        watch: {
            fixedComment(checked){
                if (checked){
                    // this.appendComment = ''
                }
                if (!checked){
                    this.$confirm('固定备注内容对财务十分重要，除非客户坚决不同意在备注栏中显示我公司单号信息，否则请不要取消勾选。', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.fixedComment = false
                    }).catch(() => {
                        this.fixedComment = true
                    })
                }
            },
            invoiceInfoType(newVal,oldVal){
                if (newVal == 0){
                    this.afterSaleApplyInfoDto.afterSaleApplyInfoGoodsDtos.forEach(g => {
                        g.goodsName = g.oldGoodsName;
                        g.spec = g.oldSpec;
                    });
                    this.comments = this.oldComments;
                    this.fixedComment = true
                    this.appendComment = ''
                }
            },
        },
    });
</script>
<style>
    #app {
        font-size: 14px;
        font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
        color: #606266;
    }
    .invoice_header {
        border: 1px solid #EBEEF5;
        height: 60px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }
    .invoice_body,.invoice_table,.is_advance,.comments{
        margin-top: 20px;
    }
    .advance_class .el-dialog__header .el-dialog__title {
        font-size: 14px;
        font-weight: bold;
    }
</style>
</html>
