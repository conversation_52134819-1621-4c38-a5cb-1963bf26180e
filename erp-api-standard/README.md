# ERP API标准化模块技术操作手册

## 📚 目录

- [1. 项目概述](#1-项目概述)
- [2. 技术架构](#2-技术架构)
- [3. 环境要求与依赖](#3-环境要求与依赖)
- [4. 安装与集成指南](#4-安装与集成指南)
- [5. 核心组件详解](#5-核心组件详解)
  - [5.0 业务开发框架](#50-业务开发框架)
    - [5.0.1 业务模板框架](#501-业务模板框架businesstemplate)
    - [5.0.2 参数配置详解](#502-参数配置详解parameterconfig)
    - [5.0.3 通用响应转换框架](#503-通用响应转换框架responseconverter)
    - [5.0.4 验证上下文数据传递](#504-验证上下文数据传递)
  - [5.1 通用审核框架](#51-通用审核框架approvalframework)
  - [5.2 统一API控制器](#52-统一api控制器unifiedapicontroller)
  - [5.3 服务适配器](#53-服务适配器serviceadapter)
  - [5.4 内部调用框架](#54-内部调用框架internalcallhelper)
  - [5.5 请求响应处理器](#55-请求响应处理器)
- [6. 开发指南](#6-开发指南)
- [7. 配置参考](#7-配置参考)
- [8. 故障排除与最佳实践](#8-故障排除与最佳实践)
- [9. 附录](#9-附录)

---

## 1. 项目概述

### 1.1 项目背景

`erp-api-standard` 是ERP系统接口标准化改造的统一模块，旨在解决现有ERP系统中API接口不统一、维护困难、扩展性差等问题。通过引入标准化的API框架，实现接口的统一管理和规范化处理。

### 1.2 核心目标

| 目标 | 描述 | 技术价值 |
|------|------|----------|
| **接口统一** | 统一API请求响应格式，规范接口调用方式 | 降低集成复杂度，提高开发效率 |
| **架构解耦** | 通过适配器模式实现业务逻辑与接口层分离 | 提高系统可维护性和扩展性 |
| **兼容性保证** | 与现有Spring 4.1.9技术栈完全兼容 | 无需升级现有系统，平滑集成 |
| **开发效率** | 提供统一的开发框架和工具类 | 减少重复代码，加快开发速度 |

### 1.3 核心特性

- **🏗️ 高内聚设计**：所有接口标准化相关代码集中在一个模块中
- **🔗 低耦合架构**：通过清晰的包结构和接口设计分离不同职责
- **🔐 纯动态用户注入**：专注于动态真实用户注入，移除系统用户概念
- **🔧 易于维护**：统一的代码位置和标准化的开发模式
- **♻️ 高度复用**：其他项目可以直接依赖和扩展此模块
- **🧪 测试友好**：完整的测试结构，支持单元测试和集成测试
- **⚡ 性能监控**：内置性能监控和统计机制
- **🤖 XXL-Job集成**：完美支持定时任务的纯动态真实用户API调用
- **🔄 验证数据传递**：支持验证规则中查询的数据传递给业务逻辑，避免重复查询

---

## 2. 技术架构

### 2.1 整体架构设计

```mermaid
graph TB
    A[客户端请求] --> B[UnifiedApiController]
    B --> C[RequestProcessor]
    C --> D[ServiceAdapterFactory]
    D --> E[ServiceAdapter]
    E --> F[InternalCallHelper]
    F --> G[现有Controller]
    G --> H[业务逻辑层]
    H --> I[数据访问层]
    I --> J[数据库]

    E --> K[ResponseProcessor]
    K --> L[ApiResponse]
    L --> M[客户端响应]

    style B fill:#e1f5fe
    style E fill:#f3e5f5
    style F fill:#e8f5e8
```

### 2.2 核心设计原则

| 设计原则 | 实现方式 | 技术优势 |
|----------|----------|----------|
| **单一职责** | 每个组件只负责特定功能 | 提高代码可维护性 |
| **开闭原则** | 通过接口和适配器模式扩展 | 支持功能扩展而不修改现有代码 |
| **依赖倒置** | 面向接口编程，依赖注入 | 降低组件间耦合度 |
| **适配器模式** | ServiceAdapter统一接口调用 | 兼容现有系统，平滑迁移 |

### 2.3 模块结构

```
erp-api-standard/
├── src/main/java/com/vedeng/api/standard/
│   ├── auth/                          # 纯动态用户注入框架
│   │   └── UserLookupService.java    # 纯动态用户查询服务
│   ├── core/                          # 核心框架
│   │   ├── ApiRequest.java            # 统一请求格式
│   │   ├── ApiResponse.java           # 统一响应格式
│   │   ├── BaseResponseCode.java      # 响应码枚举
│   │   ├── ServiceAdapter.java        # 服务适配器接口
│   │   └── exception/                 # 异常类
│   ├── internal/                      # 内部调用框架
│   │   ├── InternalCallRequest.java   # 内部调用请求
│   │   ├── InternalCallResult.java    # 内部调用结果
│   │   ├── InternalHttpCallService.java # HTTP调用服务
│   │   └── InternalCallHelper.java    # 调用助手类
│   ├── processor/                     # 请求响应处理器
│   ├── controller/                    # 统一API控制器
│   ├── factory/                       # 工厂类
│   ├── interceptor/                   # 拦截器
│   └── adapter/                       # 业务适配器
│       └── buyorder/                  # 采购单适配器示例
└── src/main/resources/
    ├── META-INF/spring/
    │   └── api-standard-context.xml   # Spring配置文件
    └── api-auth.properties             # 纯动态用户注入配置文件
```

### 2.4 请求处理流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant UC as UnifiedApiController
    participant RP as RequestProcessor
    participant SA as ServiceAdapter
    participant ICH as InternalCallHelper
    participant EC as 现有Controller

    C->>UC: POST /api/v1/{module}/{action}
    UC->>RP: 预处理请求
    RP->>UC: 返回ApiRequest
    UC->>SA: execute(action, request)
    SA->>ICH: call(controller, method, params)
    ICH->>EC: 模拟HTTP调用
    EC->>ICH: 返回业务结果
    ICH->>SA: InternalCallResult
    SA->>UC: 标准化响应数据
    UC->>C: ApiResponse
```

---

## 3. 环境要求与依赖

### 3.1 系统要求

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| **JDK** | 1.8+ | 支持Java 8及以上版本 |
| **Spring Framework** | 4.1.9 | 与现有ERP系统保持一致 |
| **Spring MVC** | 4.1.9 | Web层框架 |
| **Jackson** | 2.6.x | JSON序列化/反序列化 |
| **Servlet API** | 3.1+ | Web容器支持 |

### 3.2 技术栈兼容性

| 现有ERP组件 | 版本 | 兼容性 | 集成方式 |
|-------------|------|--------|----------|
| **Spring** | 4.1.9 | ✅ 完全兼容 | 直接集成 |
| **MySQL** | 5.7+ | ✅ 完全兼容 | 共享数据源 |
| **RabbitMQ** | 3.6+ | ✅ 完全兼容 | 可选集成 |
| **XXL-Job** | 2.x | ✅ 完全兼容 | 独立运行 |

### 3.3 Maven依赖配置

#### 3.3.1 基础依赖

```xml
<dependency>
    <groupId>com.vedeng</groupId>
    <artifactId>erp-api-standard</artifactId>
    <version>1.0-SNAPSHOT</version>
</dependency>
```

#### 3.3.2 依赖版本管理

```xml
<properties>
    <spring.version>4.1.9.RELEASE</spring.version>
    <jackson.version>2.6.7</jackson.version>
    <servlet.version>3.1.0</servlet.version>
</properties>

<dependencies>
    <!-- Spring框架依赖 -->
    <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-webmvc</artifactId>
        <version>${spring.version}</version>
    </dependency>

    <!-- JSON处理依赖 -->
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson.version}</version>
    </dependency>

    <!-- Servlet API -->
    <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <version>${servlet.version}</version>
        <scope>provided</scope>
    </dependency>
</dependencies>
```

---

## 4. 安装与集成指南

### 4.1 快速集成步骤

#### 步骤1：添加模块依赖

在需要使用API标准化功能的模块的 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>com.vedeng</groupId>
    <artifactId>erp-api-standard</artifactId>
    <version>1.0-SNAPSHOT</version>
</dependency>
```

#### 步骤2：导入Spring配置

在主Spring配置文件中导入API标准化模块配置：

```xml
<!-- 引入API标准化模块配置 -->
<import resource="classpath:META-INF/spring/api-standard-context.xml" />
```

#### 步骤3：配置Web.xml（如果需要）

如果项目使用独立的web.xml配置，需要确保Spring MVC配置正确：

```xml
<!-- Spring MVC DispatcherServlet配置 -->
<servlet>
    <servlet-name>springmvc</servlet-name>
    <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
    <init-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>classpath:applicationContext.xml</param-value>
    </init-param>
    <load-on-startup>1</load-on-startup>
</servlet>

<servlet-mapping>
    <servlet-name>springmvc</servlet-name>
    <url-pattern>/api/*</url-pattern>
</servlet-mapping>
```

#### 步骤4：验证集成

启动应用后，可以通过以下方式验证集成是否成功：

1. **检查Spring容器启动日志**
```
INFO: Loading XML bean definitions from class path resource [META-INF/spring/api-standard-context.xml]
INFO: Mapped "{[/api/v1/{module}/{action}],methods=[POST]}" onto public com.vedeng.api.standard.core.ApiResponse<java.lang.Object>
```

2. **测试API接口可访问性**
```bash
curl -X POST http://localhost:8080/api/v1/test/ping \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 4.2 统一API接口规范

#### 4.2.1 接口路径规范

所有标准化API接口都通过统一入口访问：

| 请求方式 | 路径格式 | 说明 |
|----------|----------|------|
| `POST` | `/api/v1/{module}/{action}` | 数据操作接口 |
| `GET` | `/api/v1/{module}/{action}` | 数据查询接口 |

#### 4.2.2 接口示例

| 接口路径 | 功能描述 | 请求方式 |
|----------|----------|----------|
| `/api/v1/buyorder/create` | 创建采购单 | POST |
| `/api/v1/buyorder/detail` | 获取采购单详情 | GET |
| `/api/v1/buyorder/submit` | 提交采购单审核 | POST |
| `/api/v1/buyorder/approve` | 审核通过采购单 | POST |
| `/api/v1/buyorder/reject` | 审核拒绝采购单 | POST |
| `/api/v1/buyorder/cancel` | 取消采购单审核 | POST |

#### 4.2.3 请求格式规范

**标准请求格式：**
```json
{
    "参数名": "参数值",
    "参数名2": 参数值2
}
```

**创建采购单请求示例：**
```json
{
    "deliveryDirect": 1,
    "saleorderGoodsIds": "1001,1002,1003",
    "traderId": 2001,
    "remark": "测试采购单"
}
```

**审核操作请求示例：**
```json
{
    "businessId": 12345,
    "auditComment": "审核通过，符合采购要求"
}
```

#### 4.2.4 响应格式规范

**标准响应格式：**
```json
{
    "code": 0,
    "message": "操作成功",
    "data": {},
    "requestId": "uuid-string",
    "timestamp": 1640995200000
}
```

**成功响应示例：**
```json
{
    "code": 0,
    "message": "操作成功",
    "data": {
        "buyOrderId": 12345,
        "buyOrderNo": "BO12345"
    },
    "requestId": "req-************",
    "timestamp": 1640995200000
}
```

**错误响应示例：**
```json
{
    "code": 400,
    "message": "参数错误：采购单ID不能为空",
    "data": null,
    "requestId": "req-************",
    "timestamp": 1640995200000
}
```

---

## 5. 核心组件详解

### 5.0 业务开发框架

#### 5.0.1 业务模板框架（BusinessTemplate）

**核心价值**：消除重复代码，提供统一的业务执行模式

**使用方式**：

**Controller 调用示例**：
```java
// 调用 Controller 方法（包含 HttpServletRequest 参数）
public BuyOrderCreateResponse createBuyOrder(ApiRequest request) throws Exception {
    return businessTemplate.<BuyOrderCreateRequest, BuyOrderCreateResponse>executeCreate(request)
        .requestType(BuyOrderCreateRequest.class)
        .responseType(BuyOrderCreateResponse.class)
        .validationRules(SaleOrderExistsWithDataRule.class)  // 带数据传递的验证规则
        .duplicateRules(SaleOrderDuplicateRule.class)
        .controller("newBuyorderController", "saveAddBuyorder")
        .withHttpParameters(  // 用于 Controller 调用，自动添加 HttpServletRequest
                ParameterConfig.of(BuyorderVo.class, buyorderVo),
                // 从验证上下文获取数据，避免重复查询
                ParameterConfig.fromValidationContext(ValidationContextKeys.SALE_ORDER_INFO, SaleOrderInfo.class),
                ParameterConfig.fromValidationContext(ValidationContextKeys.CUSTOMER_INFO, CustomerInfo.class)
        )
        .responseConfig(responseConfig)
        .execute();
}

// 调用包含 request.getParameter() 的 Controller 方法
public PayApplyResponse saveApplyPayment(ApiRequest request) throws Exception {
    return businessTemplate.<PayApplyCreateRequest, PayApplyResponse>executeCreate(request)
        .requestType(PayApplyCreateRequest.class)
        .responseType(PayApplyResponse.class)
        .controller("buyorderController", "saveApplyPayment")
        .withHttpParameters(
                ParameterConfig.of(PayApply.class, payApply),
                ParameterConfig.requestParam("buyorderGoodsIdArr", JSON.toJSONString(buyorderGoodsIdList)),
                ParameterConfig.requestParam("priceArr", priceArr),
                ParameterConfig.requestParam("numArr", numArr),
                ParameterConfig.requestParam("totalAmountArr", totalAmountArr),
                ParameterConfig.of(Integer.class, payApplyType)
        )
        .responseConfig(responseConfig)
        .execute();
}
```

**Service 调用示例**：
```java
// 调用 Service 方法（不包含 HttpServletRequest 参数）
public SaleorderInfoDto getSaleOrder(Integer saleOrderId) throws Exception {
    return businessTemplate.<Object, SaleorderInfoDto>executeQuery(request)
        .controller("saleOrderServiceImpl", "getBySaleOrderId")  // 调用 Service Bean
        .withoutHttpParameters(  // 用于 Service 调用，不包含 HttpServletRequest
                ParameterConfig.of(Integer.class, saleOrderId)
        )
        .responseConfig(responseConfig)
        .execute();
}
```

**预设模板**：
- `executeCreate()` - 创建操作（启用验证+防重检查）
- `executeUpdate()` - 更新操作（启用验证，跳过防重检查）
- `executeQuery()` - 查询操作（最简单，通常只需验证）
- `executeDelete()` - 删除操作（启用验证，跳过防重检查）
- `executeCustom()` - 自定义操作（完全可控）

#### 5.0.2 方法调用参数配置

**两种调用方式**：

| 方法 | 适用场景 | HttpServletRequest | 示例方法签名 |
|------|----------|-------------------|-------------|
| `.parameters()` | 包含 HttpServletRequest 的方法 | ✅ 自动添加 | `saveAddBuyorder(HttpServletRequest, BuyorderVo)` |
| `.serviceParameters()` | 不包含 HttpServletRequest 的方法 | ❌ 不添加 | `getBySaleOrderId(Integer)` |

**包含 HttpServletRequest 的方法调用**：
```java
// Controller 方法：public ResultInfo saveAddBuyorder(HttpServletRequest request, BuyorderVo buyorderVo)
.controller("newBuyorderController", "saveAddBuyorder")
.parameters(  // 自动添加 HttpServletRequest 作为第一个参数
        ParameterConfig.of(BuyorderVo.class, buyorderVo)
)

// 历史 Service 方法：public ResultInfo processData(HttpServletRequest request, String data, Integer id)
.controller("legacyServiceImpl", "processData")
.parameters(  // 自动添加 HttpServletRequest 作为第一个参数
        ParameterConfig.of(String.class, data),
        ParameterConfig.of(Integer.class, id)
)
```

**不包含 HttpServletRequest 的方法调用**：
```java
// 现代 Service 方法：public SaleorderInfoDto getBySaleOrderId(Integer saleOrderId)
.controller("saleOrderServiceImpl", "getBySaleOrderId")
.serviceParameters(  // 不添加 HttpServletRequest 参数
        ParameterConfig.of(Integer.class, saleOrderId)
)
```

**选择原则**：
- **方法签名包含 HttpServletRequest** → 使用 `.withHttpParameters()`
- **方法签名不包含 HttpServletRequest** → 使用 `.withoutHttpParameters()`

#### 5.0.2 参数配置详解（ParameterConfig）

**核心价值**：提供灵活的参数配置方式，支持多种参数传递场景

**支持的参数类型**：

1. **固定值参数**：
```java
ParameterConfig.of(BuyorderVo.class, buyorderVo)
ParameterConfig.of(Integer.class, 123)
ParameterConfig.of(String.class, "test")
```

2. **请求参数**（用于 `request.getParameter()`）：
```java
ParameterConfig.requestParam("buyorderGoodsIdArr", JSON.toJSONString(idList))
ParameterConfig.requestParam("priceArr", priceArrayString)
ParameterConfig.requestParam("numArr", numArrayString)
```

3. **业务对象占位符**：
```java
ParameterConfig.businessObject(BuyorderVo.class)  // 运行时自动注入
```

4. **从调用参数获取**：
```java
ParameterConfig.fromParams(String.class, "paramKey")
ParameterConfig.stringParam("paramKey")
ParameterConfig.integerParam("paramKey")
```

5. **从验证上下文获取**：
```java
ParameterConfig.fromValidationContext(ValidationContextKeys.SALE_ORDER_INFO, SaleOrderInfo.class)
ParameterConfig.fromValidationContext(ValidationContextKeys.CUSTOMER_INFO, CustomerInfo.class)
ParameterConfig.fromValidationContext(ValidationContextKeys.GOODS_INFO, GoodsInfo.class)
```

**便捷方法**：
```java
ParameterConfig.string("test")      // 等同于 of(String.class, "test")
ParameterConfig.integer(123)        // 等同于 of(Integer.class, 123)
ParameterConfig.bool(true)          // 等同于 of(Boolean.class, true)
```

**使用场景**：

- **Controller 方法调用**：混合使用固定值参数和请求参数
- **Service 方法调用**：主要使用固定值参数
- **历史方法兼容**：支持复杂的参数组合
- **验证数据传递**：使用验证阶段查询的数据，避免重复查询

#### 5.0.3 通用响应转换框架（ResponseConverter）

**核心价值**：统一响应转换逻辑，提供标准化的响应格式处理

**ResponseConfig 配置**：

提供简洁的一行式配置方式：

```java
ResponseMappingConfig config = ResponseConfig.create("采购单创建成功", "buyOrderId", "buyorderId");
```

**ResponseConfig API 使用**：
```java
// 创建操作（带ID字段）
ResponseMappingConfig config = ResponseConfig.create("采购单创建成功", "buyOrderId", "buyorderId");

// 更新操作
ResponseMappingConfig config = ResponseConfig.update("采购单更新成功");

// 删除操作
ResponseMappingConfig config = ResponseConfig.delete("采购单删除成功");

// 查询操作
ResponseMappingConfig config = ResponseConfig.query("查询成功");

// 提交操作
ResponseMappingConfig config = ResponseConfig.submit("提交审核成功");

// 审核操作
ResponseMappingConfig config = ResponseConfig.approval("审核通过成功");

// 通用操作
ResponseMappingConfig config = ResponseConfig.success("操作成功");
```

**链式API（高级用法）**：
```java
// 基础链式调用
ResponseMappingConfig config = ResponseConfig.operation("采购单创建成功")
    .withIds("buyOrderId", "buyorderId");

// 复杂自定义配置
ResponseMappingConfig config = ResponseConfig.operation("复杂操作成功")
    .custom()
    .successViewNames("custom/success")
    .numberFieldNames("orderNumber")
    .build();
```

**配置化转换（兼容模式）**：
```java
ResponseMappingConfig config = ResponseMappingConfig.builder()
    .successViewNames("common/success", "buyorder")
    .idFieldNames("buyOrderId", "buyorderId")
    .successMessage("采购单创建成功")
    .build();

BuyOrderResponse response = responseConverter.convertToResponse(
    callResult, BuyOrderResponse.class, config);
```

**实际应用示例（BuyorderServiceAdapter）**：

```java
// 创建操作示例
private Object executeCreateOperation(ApiRequest request) throws Exception {
    ResponseMappingConfig responseConfig = ResponseConfig.create("采购单创建成功", "buyOrderId", "buyorderId");

    return businessTemplate.<BuyOrderRequest, BuyOrderResponse>executeCreate(request)
            .responseConfig(responseConfig)
            .execute();
}

// 其他操作示例
private Object executeUpdateOperation(ApiRequest request) throws Exception {
    ResponseMappingConfig responseConfig = ResponseConfig.update("采购单更新成功");
    // ... 业务逻辑
}

private Object executeDeleteOperation(ApiRequest request) throws Exception {
    ResponseMappingConfig responseConfig = ResponseConfig.delete("采购单删除成功");
    // ... 业务逻辑
}
```

**自定义构建器**：
```java
BuyOrderResponse response = responseConverter.convertToResponse(callResult,
    (result, dataMap) -> {
        if (result.isSuccess()) {
            Integer id = ResponseUtils.extractInteger(dataMap, "buyOrderId", "buyorderId");
            BuyOrderResponse response = new BuyOrderResponse(true, "创建成功");
            response.setBuyOrderId(id);
            return response;
        } else {
            return new BuyOrderResponse(false, result.getErrorMessage());
        }
    }
);
```

#### 5.0.3 验证和防重检查框架

**参数验证**：
```java
// 支持JSR-303注解 + 自定义业务规则
standardValidator.validateAndThrow(request,
    SaleOrderExistsRule.class,
    SaleOrderStatusRule.class,
    UserPermissionRule.class
);
```

**防重检查**：
```java
// 支持多种处理策略：REJECT、ALLOW_WITH_WARNING、WARN、IGNORE
duplicateCheckManager.checkAndThrow(request,
    SaleOrderDuplicateRule.class,
    CustomerDuplicateRule.class,
    TimeDuplicateRule.class
);
```

**自定义规则开发**：
```java
@Component
public class CustomValidationRule implements ValidationRule<YourRequest> {
    @Override
    public ValidationResult validate(YourRequest request, Map<String, Object> context) {
        // 实现验证逻辑
        if (/* 验证失败条件 */) {
            return ValidationResult.failure(getRuleName(), "验证失败消息");
        }
        return ValidationResult.success(getRuleName());
    }
}
```

#### 5.0.4 验证上下文数据传递

**核心价值**：允许验证规则在验证过程中查询到的数据传递给后续的业务逻辑使用，避免重复查询，提高性能并确保数据一致性。

**使用场景**：
- 验证销售单存在性时，将销售单信息传递给采购单创建逻辑
- 验证客户权限时，将客户信息传递给业务处理
- 验证商品库存时，将库存信息传递给订单处理

**实现步骤**：

**1. 验证规则中存储数据**：
```java
@Component
public class SaleOrderExistsWithDataRule implements ValidationRule<BuyOrderCreateRequest> {

    @Override
    public ValidationResult validate(BuyOrderCreateRequest request, Map<String, Object> context) {
        // 查询销售单信息
        SaleOrderInfo saleOrderInfo = saleOrderService.getById(request.getSaleorderId());
        if (saleOrderInfo == null) {
            return ValidationResult.failure(getRuleName(), "销售单不存在");
        }

        // 查询客户信息
        CustomerInfo customerInfo = customerService.getById(saleOrderInfo.getCustomerId());

        // 存储到验证上下文中供后续使用
        context.put(ValidationContextKeys.SALE_ORDER_INFO, saleOrderInfo);
        context.put(ValidationContextKeys.CUSTOMER_INFO, customerInfo);

        return ValidationResult.success();
    }
}
```

**2. 业务模板中使用验证上下文数据**：
```java
public BuyOrderCreateResponse createBuyOrder(ApiRequest request) throws Exception {
    return businessTemplate.<BuyOrderCreateRequest, BuyOrderCreateResponse>executeCreate(request)
        .requestType(BuyOrderCreateRequest.class)
        .responseType(BuyOrderCreateResponse.class)
        .validationRules(SaleOrderExistsWithDataRule.class)
        .controller("newBuyorderController", "saveAddBuyorder")
        .withHttpParameters(
            ParameterConfig.of(BuyorderVo.class, buyorderVo),
            // 从验证上下文获取数据，避免重复查询
            ParameterConfig.fromValidationContext(ValidationContextKeys.SALE_ORDER_INFO, SaleOrderInfo.class),
            ParameterConfig.fromValidationContext(ValidationContextKeys.CUSTOMER_INFO, CustomerInfo.class)
        )
        .execute();
}
```

**3. Controller方法接收参数**：
```java
@Controller
public class NewBuyorderController {

    public ResultInfo saveAddBuyorder(HttpServletRequest request,
                                     BuyorderVo buyorderVo,
                                     SaleOrderInfo saleOrderInfo,  // 来自验证上下文
                                     CustomerInfo customerInfo) {   // 来自验证上下文

        // 直接使用验证阶段查询的数据，无需重复查询
        logger.info("使用验证阶段查询的销售单信息: {}", saleOrderInfo);
        logger.info("使用验证阶段查询的客户信息: {}", customerInfo);

        // 业务逻辑处理...
        return ResultInfo.success();
    }
}
```

**标准化键名**：
```java
// 使用预定义的标准键名
context.put(ValidationContextKeys.SALE_ORDER_INFO, saleOrderInfo);
context.put(ValidationContextKeys.CUSTOMER_INFO, customerInfo);
context.put(ValidationContextKeys.GOODS_INFO, goodsInfo);

// 支持的标准键名包括：
// - SALE_ORDER_INFO, BUY_ORDER_INFO
// - CUSTOMER_INFO, SUPPLIER_INFO
// - GOODS_INFO, STOCK_INFO
// - CURRENT_USER, USER_PERMISSIONS
// - WORKFLOW_INSTANCE_ID, COMPANY_SEQUENCE
// 等等...
```

**最佳实践**：
- 优先使用 `ValidationContextKeys` 中定义的标准键名
- 验证规则中即使查询结果为空也要存储到上下文中
- 在Controller方法中添加适当的空值检查
- 使用明确的类型定义，避免使用Object类型

---

### 5.1 通用审核框架（ApprovalFramework）

#### 5.1.1 框架概述

通用审核框架提供了统一的多步审核执行能力，支持各种业务模块的审核需求。通过配置化的方式，可以轻松地在不同的 ServiceAdapter 中实现审核功能。

#### 5.1.2 核心组件

**ApprovalRequest 接口**

所有需要审核的请求类都应该实现此接口：

```java
public interface ApprovalRequest {
    String getTaskId();
    void setTaskId(String taskId);
    String getComment();
    void setComment(String comment);
}
```

**ApprovalConfig 配置类**

封装审核执行所需的所有配置信息：

```java
ApprovalConfig<RequestType, ResponseType> config = ApprovalConfig
    .<RequestType, ResponseType>builder()
    .requestType(RequestType.class)
    .responseType(ResponseType.class)
    .controller("controllerBeanName", "methodName")
    .validationRules(ValidationRule1.class, ValidationRule2.class)
    .responseConfig(ResponseConfig.create("成功消息", "dataKey"))
    .build();
```

**ApprovalExecutor 执行器**

通用审核执行器，提供统一的审核执行逻辑：

```java
@Autowired
private ApprovalExecutor approvalExecutor;

// 循环审核：自动执行所有审核步骤直到流程完成
public ApprovalResult executeApproval(ApiRequest request, SomeRequest someRequest) {
    return approvalExecutor.executeOneStepApproval(request, someRequest, config);
}
```

#### 5.1.3 使用步骤

**步骤1：让请求类实现 ApprovalRequest 接口**

```java
@Data
public class BuyOrderRequest implements Serializable, ApprovalRequest {
    private String taskId;
    private String comment;
    // ... 其他字段

    // ApprovalRequest 接口方法会由 Lombok @Data 自动生成
}
```

**步骤2：在 ServiceAdapter 中注入 ApprovalExecutor**

```java
@Component
public class SomeServiceAdapter extends AbstractServiceAdapter {

    @Autowired
    private ApprovalExecutor approvalExecutor;

    // ... 其他依赖
}
```

**步骤3：创建审核配置并执行**

```java
private ApprovalResult executeApproveOperation(ApiRequest request) throws Exception {
    // 转换请求数据
    SomeRequest someRequest = dataConverter.convert(request.getData(), SomeRequest.class);

    // 创建审核配置
    ApprovalConfig<SomeRequest, SomeResponse> config = ApprovalConfig
            .<SomeRequest, SomeResponse>builder()
            .requestType(SomeRequest.class)
            .responseType(SomeResponse.class)
            .controller("someController", "complementTaskForSome")
            .validationRules(SomeExistsRule.class, SomeStatusRule.class)
            .responseConfig(ResponseConfig.create("审核成功", "result"))
            .build();

    // 执行循环审核（推荐）
    return approvalExecutor.executeMultiStepApproval(request, someRequest, config);
}
```

#### 5.1.4 循环审核 vs 单步审核

**循环审核（executeMultiStepApproval）**：
- ✅ **推荐使用**：自动执行所有审核步骤直到流程完成
- ✅ **智能循环**：自动获取下一个任务并切换审核人
- ✅ **流程完整性**：确保整个审核流程完成
- ✅ **防无限循环**：最多执行10步，防止异常情况
- ✅ **详细日志**：记录每个审核步骤的详细信息
- ✅ **错误处理**：单步失败立即终止循环，避免无效执行

**单步审核（executeOneStepApproval）**：
- ⚠️ **已废弃**：标记为 @Deprecated，建议迁移到循环审核
- ⚠️ **手动控制**：只执行一个审核步骤，需要外部循环控制
- ⚠️ **有限场景**：仅在需要步骤间插入特殊逻辑时使用

**选择建议**：
- 新功能开发：使用 `executeMultiStepApproval`
- 现有功能迁移：逐步从 `executeOneStepApproval` 迁移到 `executeMultiStepApproval`
- 特殊需求：需要在审核步骤间插入额外逻辑时使用 `executeOneStepApproval`

#### 5.1.5 完整示例

**采购单审核示例**

```java
@Component("buyorderServiceAdapter")
public class BuyorderServiceAdapter extends AbstractServiceAdapter {

    @Autowired
    private ApprovalExecutor approvalExecutor;

    @Autowired
    private BuyorderDataConverter dataConverter;

    /**
     * 执行采购单审核（使用通用框架）
     */
    private ApprovalResult executeApproveOperation(ApiRequest request) throws Exception {
        // 转换请求数据
        BuyOrderRequest buyOrderRequest = dataConverter.convert(request.getData(), BuyOrderRequest.class);

        // 创建审核配置
        ApprovalConfig<BuyOrderRequest, BuyOrderResponse> config = ApprovalConfig
                .<BuyOrderRequest, BuyOrderResponse>builder()
                .requestType(BuyOrderRequest.class)
                .responseType(BuyOrderResponse.class)
                .controller("buyorderController", "complementTaskForBuyOrder")
                .validationRules(BuyOrderExistsRule.class, BuyOrderStatusRule.class)
                .responseConfig(ResponseConfig.create("审核步骤成功", "stepResult"))
                .build();

        // 执行循环审核（推荐）
        return approvalExecutor.executeMultiStepApproval(request, buyOrderRequest, config);
    }
}
```

**参数配置示例**：

```java
// 示例1：包含HttpServletRequest参数的Controller方法
// 方法签名：complementTaskForBuyOrder(HttpServletRequest request, String taskId, String comment, Boolean pass, Integer buyorderId, HttpSession session)
ApprovalConfig<BuyOrderRequest, BuyOrderResponse> configWithHttp = ApprovalConfig
    .<BuyOrderRequest, BuyOrderResponse>builder()
    .requestType(BuyOrderRequest.class)
    .responseType(BuyOrderResponse.class)
    .controller("buyorderController", "complementTaskForBuyOrder")
    .withHttpParameters(approvalReq -> new ParameterConfig[]{
        ParameterConfig.of(String.class, approvalReq.getTaskId()),      // taskId
        ParameterConfig.of(String.class, approvalReq.getComment()),    // comment
        ParameterConfig.of(Boolean.class, approvalReq.getPass()),      // pass
        ParameterConfig.of(Integer.class, approvalReq.getBuyOrderId()) // buyorderId
        // HttpServletRequest 和 HttpSession 会自动注入
    })
    .build();

// 示例2：不包含HttpServletRequest参数的Service方法
// 方法签名：processApproval(String taskId, Boolean pass, Integer buyorderId)
ApprovalConfig<BuyOrderRequest, BuyOrderResponse> configWithoutHttp = ApprovalConfig
    .<BuyOrderRequest, BuyOrderResponse>builder()
    .requestType(BuyOrderRequest.class)
    .responseType(BuyOrderResponse.class)
    .controller("buyorderService", "processApproval")
    .withoutHttpParameters(approvalReq -> new ParameterConfig[]{
        ParameterConfig.of(String.class, approvalReq.getTaskId()),      // taskId
        ParameterConfig.of(Boolean.class, approvalReq.getPass()),       // pass
        ParameterConfig.of(Integer.class, approvalReq.getBuyOrderId())  // buyorderId
    })
    .build();
```

#### 5.1.5 技术特点

**优势**

1. **代码复用**：统一的审核逻辑，避免重复代码
2. **配置化**：通过配置适应不同的业务场景
3. **类型安全**：泛型支持，编译时类型检查
4. **用户身份管理**：自动处理审核人身份切换和恢复
5. **详细日志**：完整的审核步骤记录和日志
6. **异常安全**：确保用户身份一定会被恢复

**核心功能**

1. **自动用户身份切换**：
   - 更新 ApiRequest 的 currentUser
   - 设置 Activiti 认证用户
   - 异常安全的身份恢复

2. **灵活的配置支持**：
   - 支持不同的 Controller 和方法
   - 支持自定义验证规则
   - 支持自定义响应配置

3. **完整的审核记录**：
   - 记录审核人信息
   - 记录审核时间和结果
   - 支持审核备注

#### 5.1.6 用户身份切换实现

**技术原理**

用户身份切换通过以下机制实现：

1. **ApiRequest用户更新**：
   - 调用 `request.setCurrentUser(approver)` 更新请求中的用户信息
   - BusinessTemplate 会将此用户信息传递给 InternalHttpCallService
   - InternalHttpCallService 创建 MockHttpSession 并设置 `ErpConst.CURR_USER`
   - Controller 方法通过 `session.getAttribute(ErpConst.CURR_USER)` 获取审核人信息

2. **Activiti用户设置**：
   - 调用 `Authentication.setAuthenticatedUserId(approver.getUsername())`
   - 设置工作流引擎的当前用户，确保审核操作记录正确的执行人

3. **异常安全处理**：
   - 使用 try-finally 确保用户身份一定会被恢复
   - restoreUserContext 中不抛异常，避免影响主流程
   - 包含清理逻辑，防止用户身份泄露

**调用链路**

**循环审核调用链路**：
```
executeMultiStepApproval()
├── while (true) {
│   ├── 获取当前任务 → TaskService.createTaskQuery()
│   ├── switchUserContext() → 更新 ApiRequest.currentUser + Activiti用户
│   ├── try {
│   │   ├── executeApprovalStep()
│   │   │   └── BusinessTemplate.executeUpdate()
│   │   │       └── InternalHttpCallService.execute()
│   │   │           └── MockHttpSession.setAttribute(ErpConst.CURR_USER, approver)
│   │   │               └── Controller.complementTaskForBuyOrder()
│   │   │                   └── session.getAttribute(ErpConst.CURR_USER) → 获取审核人
│   │   ├── recordApprovalStep(success=true) → 记录成功详情
│   │   ├── isWorkflowCompleted() → 检查流程是否完成
│   │   ├── getNextTaskId() → 获取下一个任务ID
│   │   └── 更新 approvalRequest.taskId
│   │   } catch (Exception) {
│   │       ├── recordApprovalStep(success=false) → 记录失败详情
│   │       └── return ApprovalResult.failure() → 立即终止循环
│   │   }
│   }
└── restoreUserContext() → 恢复原始用户身份
```

**单步审核调用链路**：
```
executeOneStepApproval()
├── switchUserContext() → 更新 ApiRequest.currentUser + Activiti用户
├── executeApprovalStep()
│   └── BusinessTemplate.executeUpdate()
│       └── InternalHttpCallService.execute()
│           └── MockHttpSession.setAttribute(ErpConst.CURR_USER, approver)
│               └── Controller.complementTaskForBuyOrder()
│                   └── session.getAttribute(ErpConst.CURR_USER) → 获取审核人
└── restoreUserContext() → 恢复原始用户身份
```

#### 5.1.7 迁移指南

**从现有实现迁移**

1. **保持兼容性**：现有的审核方法可以逐步迁移
2. **逐步迁移**：新功能使用通用框架，现有功能逐步迁移
3. **测试验证**：确保迁移后功能正常

**推荐做法**

1. 新的 ServiceAdapter 直接使用通用框架
2. 现有的 ServiceAdapter 可以同时提供两种实现
3. 逐步将现有实现迁移到通用框架

**注意事项**

1. **请求类约束**：必须实现 ApprovalRequest 接口
2. **Controller 方法**：确保 Controller 方法存在且参数匹配
3. **验证规则**：确保验证规则类存在且配置正确
4. **异常处理**：框架会自动处理用户身份恢复，但业务异常需要自行处理

---

### 5.2 统一API控制器（UnifiedApiController）

#### 5.2.1 组件职责

UnifiedApiController是整个API标准化框架的入口点，负责：

- 接收所有标准化API请求
- 统一请求格式验证和预处理
- 路由请求到对应的服务适配器
- 统一响应格式封装和异常处理

#### 5.1.2 核心实现机制

```java
@RestController
@RequestMapping("/api/v1")
public class UnifiedApiController {

    // 统一处理POST请求
    @PostMapping("/{module}/{action}")
    public ApiResponse<Object> handlePost(
        @PathVariable String module,
        @PathVariable String action,
        @RequestBody Map<String, Object> requestData) {

        // 1. 构建统一请求对象
        ApiRequest apiRequest = buildApiRequest(module, action, requestData);

        // 2. 获取对应的服务适配器
        ServiceAdapter adapter = serviceAdapterFactory.getServiceAdapter(module);

        // 3. 执行业务逻辑
        Object result = adapter.execute(action, apiRequest);

        // 4. 封装标准响应
        return ApiResponse.success(result);
    }
}
```

### 5.3 服务适配器（ServiceAdapter）

#### 5.3.1 设计模式

ServiceAdapter采用适配器模式，将标准化的API请求适配到现有的业务Controller，实现新旧系统的无缝对接。

#### 5.3.2 接口定义

```java
public interface ServiceAdapter {
    /**
     * 执行业务操作
     */
    Object execute(String action, ApiRequest request) throws Exception;

    /**
     * 获取模块名称
     */
    String getModuleName();

    /**
     * 获取支持的操作列表
     */
    default String[] getSupportedActions() {
        return new String[]{"create", "update", "delete", "query"};
    }
}
```

#### 5.3.3 实现示例

```java
@Component("buyorderServiceAdapter")
public class BuyorderServiceAdapter extends AbstractServiceAdapter {

    @Override
    public String getModuleName() {
        return "buyorder";
    }

    @Override
    protected void registerOperationHandlers() {
        // 使用声明式注册替代 switch 语句
        registerThrowingHandler("create", this::executeCreateOperation);
        registerThrowingHandler("update", this::executeUpdateOperation);
        registerThrowingHandler("query", this::executeQueryOperation);
        registerThrowingHandler("detail", this::executeDetailOperation);
        registerThrowingHandler("submit", this::executeSubmitOperation);
        registerThrowingHandler("approve", this::executeApproveOperation);

        logger.info("采购单服务适配器注册完成，支持{}个操作", getHandlerCount());
    }

    private Object executeCreateOperation(ApiRequest request) throws Exception {
        // 业务逻辑实现
        return businessTemplate.<BuyOrderRequest, BuyOrderResponse>executeCreate(request)
                .requestType(BuyOrderRequest.class)
                .responseType(BuyOrderResponse.class)
                .controller("newBuyorderController", "saveAddBuyorder")
                .responseConfig(ResponseConfig.create("采购单创建成功", "buyOrderId"))
                .execute();
    }
}
```

#### 5.3.4 ServiceAdapter 架构设计

**核心价值**：提供统一的操作分发机制，简化业务适配器开发

**设计理念**：
ServiceAdapter 采用抽象基类 + 函数映射的设计模式，具有以下特点：
- **统一的操作分发** - 所有 ServiceAdapter 共享相同的分发机制
- **声明式注册** - 通过简单的方法注册实现操作映射
- **易于扩展** - 新增操作只需添加一行注册代码

**AbstractServiceAdapter 核心特性**：
```java
public abstract class AbstractServiceAdapter implements ServiceAdapter {

    // 操作处理器映射
    private final Map<String, Function<ApiRequest, Object>> operationHandlers = new HashMap<>();

    // 子类实现此方法注册操作处理器
    protected abstract void registerOperationHandlers();

    // 注册普通操作处理器
    protected final void registerHandler(String action, Function<ApiRequest, Object> handler);

    // 注册支持异常的操作处理器
    protected final void registerThrowingHandler(String action, ThrowingFunction<ApiRequest, Object> handler);

    // 统一的操作执行入口
    @Override
    public final Object execute(String action, ApiRequest request) throws Exception;
}
```

**技术特性**：

| 特性 | 实现方式 | 技术优势 |
|------|----------|----------|
| **代码简洁** | 声明式注册 | 6行代码完成操作映射 |
| **易于扩展** | 函数映射机制 | 新增操作只需一行注册 |
| **统一管理** | 抽象基类设计 | 操作分发逻辑集中管理 |
| **类型安全** | 泛型支持 | 编译时类型检查 |

**使用示例**：
```java
@Component("buyorderServiceAdapter")
public class BuyorderServiceAdapter extends AbstractServiceAdapter {

    @Override
    protected void registerOperationHandlers() {
        // 声明式注册，替代 switch 语句
        registerThrowingHandler("create", this::executeCreateOperation);
        registerThrowingHandler("update", this::executeUpdateOperation);
        registerThrowingHandler("query", this::executeQueryOperation);
        registerThrowingHandler("detail", this::executeDetailOperation);
        registerThrowingHandler("submit", this::executeSubmitOperation);
        registerThrowingHandler("approve", this::executeApproveOperation);

        logger.info("采购单服务适配器注册完成，支持{}个操作", getHandlerCount());
    }

    // 业务方法保持不变
    private Object executeCreateOperation(ApiRequest request) throws Exception {
        // 原有业务逻辑
    }
}
```

**技术优势**：
- **单一职责** - 抽象基类专注于操作分发，子类专注于业务逻辑
- **开闭原则** - 对扩展开放，对修改关闭
- **代码复用** - 操作分发逻辑在所有子类中复用
- **高效执行** - Map 查找比 switch 语句更高效
- **测试友好** - 抽象基类可独立测试

**实现指南**：
创建新的 ServiceAdapter 的标准步骤：
```java
// 实现步骤：
// 1. 继承 AbstractServiceAdapter
// 2. 实现 registerOperationHandlers() 方法
// 3. 使用 registerThrowingHandler() 注册操作
// 4. 实现具体的业务方法

@Component("saleorderServiceAdapter")
public class SaleorderServiceAdapter extends AbstractServiceAdapter {

    @Override
    protected void registerOperationHandlers() {
        registerThrowingHandler("create", this::executeCreateOperation);
        registerThrowingHandler("update", this::executeUpdateOperation);
        // ... 其他操作
    }

    @Override
    public String getModuleName() {
        return "saleorder";
    }
}
```

### 5.4 内部调用框架（InternalCallHelper）

#### 5.4.1 核心功能

InternalCallHelper提供了一套完整的内部Controller调用机制，支持：

- 模拟HTTP请求环境
- 自动参数转换和用户会话管理
- 统一的错误处理和结果封装
- 支持同步和异步调用模式

#### 5.4.2 调用方式

**基础调用：**
```java
InternalCallResult result = internalCallHelper.call(
    "controllerBeanName",    // Controller Bean名称
    "methodName",            // 方法名
    parameters,              // 参数Map
    currentUser              // 当前用户
);
```

**Builder模式调用：**
```java
InternalCallResult result = internalCallHelper
    .builder("controllerBeanName", "methodName")
    .post()
    .param("key1", "value1")
    .user(currentUser)
    .timeout(30000)
    .execute();
```

### 5.5 请求响应处理器

#### 5.5.1 RequestProcessor

负责请求的预处理，包括参数验证、格式转换、权限检查等：

```java
@Component
public class RequestProcessor {

    public void preProcess(ApiRequest request) {
        // 1. 参数验证
        validateRequest(request);

        // 2. 用户权限检查
        checkUserPermission(request);

        // 3. 请求日志记录
        logRequest(request);
    }
}
```

#### 5.5.2 ResponseProcessor

负责响应的后处理，包括数据脱敏、格式统一、性能统计等：

```java
@Component
public class ResponseProcessor {

    public ApiResponse<?> postProcess(ApiResponse<?> response, ApiRequest request) {
        // 1. 数据脱敏处理
        sanitizeData(response);

        // 2. 性能统计
        recordPerformance(request, response);

        // 3. 响应日志记录
        logResponse(request, response);

        return response;
    }
}
```

---

## 6. 开发指南

### 6.1 创建新的服务适配器

#### 6.1.1 基础适配器模板

```java
@Component("yourModuleServiceAdapter")
public class YourModuleServiceAdapter implements ServiceAdapter {

    @Autowired
    private InternalCallHelper internalCallHelper;

    @Autowired
    private YourModuleDataConverter dataConverter;

    @Override
    public Object execute(String action, ApiRequest request) throws Exception {
        switch (action.toLowerCase()) {
            case "create":
                return createEntity(request);
            case "query":
                return queryEntity(request);
            default:
                throw new UnsupportedOperationException("不支持的操作: " + action);
        }
    }

    @Override
    public String getModuleName() {
        return "yourmodule";
    }

    private Object createEntity(ApiRequest request) {
        // 实现创建逻辑
        return internalCallHelper.call(
            "yourController",
            "createMethod",
            convertParams(request.getData()),
            request.getCurrentUser());
    }
}
```

#### 6.1.2 DTO类设计

**请求DTO示例：**
```java
public class YourEntityCreateRequest {
    private String name;
    private Integer type;
    private String description;

    // 参数验证
    public void validate() {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("名称不能为空");
        }
    }

    // getters and setters...
}
```

**响应DTO示例：**
```java
public class YourEntityCreateResponse {
    private Boolean success;
    private String message;
    private Integer entityId;
    private String entityNo;

    public static YourEntityCreateResponse success(Integer entityId) {
        YourEntityCreateResponse response = new YourEntityCreateResponse();
        response.setSuccess(true);
        response.setMessage("创建成功");
        response.setEntityId(entityId);
        return response;
    }

    // getters and setters...
}
```

### 6.2 数据转换器开发

#### 6.2.1 转换器模板

```java
@Component
public class YourModuleDataConverter {

    /**
     * 通用数据转换方法
     */
    public <T> T convert(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }

        try {
            // 使用Jackson进行转换
            ObjectMapper mapper = new ObjectMapper();
            String json = mapper.writeValueAsString(source);
            return mapper.readValue(json, targetClass);
        } catch (Exception e) {
            throw new RuntimeException("数据转换失败", e);
        }
    }

    /**
     * 专用转换方法
     */
    public Map<String, Object> convertToControllerParams(YourEntityCreateRequest request) {
        Map<String, Object> params = new HashMap<>();
        params.put("name", request.getName());
        params.put("type", request.getType().toString());
        return params;
    }
}
```

### 6.3 异常处理

#### 6.3.1 标准异常类

```java
// 业务异常
public class BusinessException extends RuntimeException {
    private Integer code;

    public BusinessException(String message) {
        super(message);
        this.code = BaseResponseCode.BUSINESS_ERROR.getCode();
    }

    public BusinessException(BaseResponseCode responseCode) {
        super(responseCode.getMessage());
        this.code = responseCode.getCode();
    }
}

// 参数异常
public class ParameterException extends BusinessException {
    public ParameterException(String message) {
        super(message);
        this.code = BaseResponseCode.PARAMETER_ERROR.getCode();
    }
}
```

#### 6.3.2 异常处理最佳实践

```java
public Object execute(String action, ApiRequest request) throws Exception {
    try {
        // 1. 参数验证
        validateRequest(request);

        // 2. 业务逻辑执行
        return executeBusinessLogic(action, request);

    } catch (ParameterException e) {
        logger.warn("参数错误: {}", e.getMessage());
        throw e;
    } catch (BusinessException e) {
        logger.error("业务异常: {}", e.getMessage());
        throw e;
    } catch (Exception e) {
        logger.error("系统异常", e);
        throw new BusinessException("系统异常，请稍后重试");
    }
}
```

### 6.4 开发框架集成示例

#### 6.4.1 完整的ServiceAdapter示例

```java
@Component("buyorderServiceAdapter")
public class BuyorderServiceAdapter implements ServiceAdapter {

    @Autowired
    private BusinessTemplate businessTemplate;

    @Autowired
    private StandardValidator standardValidator;

    @Autowired
    private DuplicateCheckManager duplicateCheckManager;

    @Autowired
    private StandardResponseConverter responseConverter;

    public BuyOrderResponse createBuyOrder(ApiRequest request) throws Exception {
        // 使用业务模板框架 - 一行代码完成所有流程
        return businessTemplate.<BuyOrderRequest, BuyOrderResponse>executeCreate(request)
            .requestType(BuyOrderRequest.class)
            .responseType(BuyOrderResponse.class)
            .validationRules(SaleOrderExistsRule.class)
            .duplicateRules(SaleOrderDuplicateRule.class)
            .converter(this::convertToBuyorderVo)
            .targetUrl("/buyorder/create.do")
            .responseConfig(createResponseConfig())
            .execute();
    }

    public BuyOrderUpdateResponse updateBuyOrder(ApiRequest request) throws Exception {
        // 更新操作默认跳过防重检查
        return businessTemplate.<BuyOrderUpdateRequest, BuyOrderUpdateResponse>executeUpdate(request)
            .requestType(BuyOrderUpdateRequest.class)
            .responseType(BuyOrderUpdateResponse.class)
            .validationRules(BuyOrderExistsRule.class)
            .converter(this::convertToUpdateVo)
            .targetUrl("/buyorder/update.do")
            .responseConfig(updateResponseConfig())
            .execute();
    }

    private ResponseMappingConfig createResponseConfig() {
        return ResponseMappingConfig.builder()
            .successViewNames("common/success", "buyorder")
            .idFieldNames("buyOrderId", "buyorderId")
            .successMessage("采购单创建成功")
            .build();
    }
}
```

#### 6.4.2 单元测试模板

```java
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:test-context.xml"})
public class YourModuleServiceAdapterTest {

    @Autowired
    private YourModuleServiceAdapter adapter;

    @Test
    public void testCreateEntity() {
        // 准备测试数据
        ApiRequest request = new ApiRequest();
        request.setModule("yourmodule");
        request.setAction("create");

        Map<String, Object> data = new HashMap<>();
        data.put("name", "测试实体");
        request.setData(data);

        // 执行测试
        Object result = adapter.execute("create", request);

        // 验证结果
        assertNotNull(result);
    }
}
```

---

## 7. 配置参考

### 7.1 Spring配置详解

#### 7.1.1 核心配置文件（api-standard-context.xml）

```xml
<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="
           http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-4.1.xsd
           http://www.springframework.org/schema/mvc
           http://www.springframework.org/schema/mvc/spring-mvc-4.1.xsd">

    <!-- 组件扫描配置 -->
    <context:component-scan base-package="com.vedeng.api.standard">
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Component"/>
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Service"/>
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
        <context:include-filter type="annotation" expression="org.springframework.web.bind.annotation.RestController"/>
    </context:component-scan>

    <!-- MVC注解驱动配置 -->
    <mvc:annotation-driven>
        <mvc:message-converters>
            <bean class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
                <property name="objectMapper">
                    <bean class="com.fasterxml.jackson.databind.ObjectMapper">
                        <property name="dateFormat">
                            <bean class="java.text.SimpleDateFormat">
                                <constructor-arg value="yyyy-MM-dd HH:mm:ss"/>
                            </bean>
                        </property>
                    </bean>
                </property>
            </bean>
        </mvc:message-converters>
    </mvc:annotation-driven>

    <!-- 异常处理器 -->
    <bean class="com.vedeng.api.standard.controller.exception.UnifiedApiExceptionHandler"/>

    <!-- 拦截器配置 -->
    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/api/**"/>
            <bean class="com.vedeng.api.standard.interceptor.ApiRequestLogInterceptor"/>
        </mvc:interceptor>
    </mvc:interceptors>

</beans>
```

#### 7.1.2 自定义配置选项

**性能调优参数：**

| 参数名 | 默认值 | 说明 | 配置方式 |
|--------|--------|------|----------|
| `api.timeout` | 30000ms | API调用超时时间 | application.properties |
| `api.maxThreads` | 200 | 最大并发线程数 | Tomcat配置 |
| `api.logLevel` | INFO | 日志级别 | logback.xml |

**安全配置参数：**

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| `api.auth.enabled` | true | 是否启用身份验证 |
| `api.permission.check` | true | 是否启用权限检查 |
| `api.rate.limit` | 1000/min | 接口调用频率限制 |

### 7.2 自定义配置

#### 7.2.1 扩展异常处理器

```java
@Component
public class CustomApiExceptionHandler extends UnifiedApiExceptionHandler {

    @Override
    protected ApiResponse<?> handleBusinessException(BusinessException e) {
        // 自定义业务异常处理逻辑
        logger.error("业务异常: {}", e.getMessage());

        return ApiResponse.error(e.getCode(), e.getMessage());
    }

    @Override
    protected ApiResponse<?> handleSystemException(Exception e) {
        // 自定义系统异常处理逻辑
        logger.error("系统异常", e);

        // 发送告警通知
        sendAlertNotification(e);

        return ApiResponse.error("系统繁忙，请稍后重试");
    }
}
```

#### 7.2.2 自定义拦截器

```java
@Component
public class CustomApiInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request,
                           HttpServletResponse response,
                           Object handler) throws Exception {

        // 1. 请求频率限制
        if (!checkRateLimit(request)) {
            response.setStatus(429);
            return false;
        }

        // 2. IP白名单检查
        if (!checkIpWhitelist(request)) {
            response.setStatus(403);
            return false;
        }

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request,
                          HttpServletResponse response,
                          Object handler,
                          ModelAndView modelAndView) throws Exception {
        // 响应后处理
    }
}
```

#### 7.2.3 自定义处理器

```java
@Component
public class CustomRequestProcessor implements RequestProcessor {

    @Override
    public void preProcess(ApiRequest request) {
        // 1. 自定义参数验证
        validateCustomParams(request);

        // 2. 数据脱敏处理
        sanitizeRequestData(request);

        // 3. 审计日志记录
        recordAuditLog(request);
    }
}
```

---

## 8. 故障排除与最佳实践

### 8.1 常见问题及解决方案

#### 8.1.1 Bean未找到异常

**问题现象：**
```
NoSuchBeanDefinitionException: No bean named 'xxxServiceAdapter' available
```

**解决方案：**

| 检查项 | 解决方法 |
|--------|----------|
| Spring配置导入 | 确认 `api-standard-context.xml` 已正确导入 |
| 组件扫描路径 | 检查 `<context:component-scan>` 包含相关包路径 |
| 注解配置 | 确认适配器类使用了 `@Component` 注解 |
| Bean名称 | 检查Bean名称是否与工厂类中的查找名称一致 |

**验证方法：**
```java
// 在启动时检查Bean是否存在
ApplicationContext context = ...;
ServiceAdapter adapter = context.getBean("buyorderServiceAdapter", ServiceAdapter.class);
```

#### 8.1.2 内部调用失败

**问题现象：**
```
InternalCallException: Controller method invocation failed
```

**解决方案：**

| 问题类型 | 检查项 | 解决方法 |
|----------|--------|----------|
| Controller不存在 | Bean名称 | 确认Controller Bean名称正确 |
| 方法不存在 | 方法签名 | 检查方法名和参数类型是否匹配 |
| 参数错误 | 参数转换 | 验证参数格式和类型转换 |
| 权限问题 | 用户会话 | 确认用户会话正确设置 |

**调试技巧：**
```java
// 启用详细日志
logger.debug("调用Controller: {}, 方法: {}, 参数: {}",
    controllerName, methodName, parameters);

// 检查调用结果
if (!result.isSuccess()) {
    logger.error("调用失败: {}", result.getErrorMessage());
}
```

#### 8.1.3 响应格式错误

**问题现象：**
```json
{
    "code": 500,
    "message": "响应格式转换失败"
}
```

**解决方案：**

1. **检查数据转换器**
```java
@Component
public class ResponseConverter {
    public Object convertResponse(InternalCallResult result) {
        // 添加空值检查
        if (result == null || result.getData() == null) {
            return Collections.emptyMap();
        }

        // 类型安全转换
        Object data = result.getData();
        if (data instanceof Map) {
            return (Map<String, Object>) data;
        }

        return data;
    }
}
```

2. **验证JSON序列化**
```java
// 测试JSON序列化
ObjectMapper mapper = new ObjectMapper();
try {
    String json = mapper.writeValueAsString(responseData);
    logger.debug("响应JSON: {}", json);
} catch (JsonProcessingException e) {
    logger.error("JSON序列化失败", e);
}
```

### 8.2 性能调优建议

#### 8.2.1 调用超时配置

```java
// 设置合理的超时时间
InternalCallResult result = internalCallHelper
    .builder("controller", "method")
    .timeout(10000)  // 10秒超时
    .execute();

// 异步调用支持
@Async
public CompletableFuture<Object> asyncCall(ApiRequest request) {
    return CompletableFuture.supplyAsync(() -> {
        return executeBusinessLogic(request);
    });
}
```

#### 8.2.2 缓存策略

```java
@Component
public class CacheableServiceAdapter implements ServiceAdapter {

    @Cacheable(value = "apiCache", key = "#request.module + '_' + #request.action")
    public Object execute(String action, ApiRequest request) {
        // 缓存查询结果
        return executeQuery(action, request);
    }

    @CacheEvict(value = "apiCache", allEntries = true)
    public Object executeUpdate(String action, ApiRequest request) {
        // 更新操作清除缓存
        return executeBusinessLogic(action, request);
    }
}
```

#### 8.2.3 连接池配置

```xml
<!-- 数据库连接池配置 -->
<bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource">
    <property name="initialSize" value="10"/>
    <property name="maxActive" value="100"/>
    <property name="maxWait" value="60000"/>
    <property name="validationQuery" value="SELECT 1"/>
</bean>
```

### 8.3 安全最佳实践

#### 8.3.1 权限验证

```java
@Component
public class SecurityValidator {

    public void validateUserPermission(ApiRequest request) {
        User currentUser = request.getCurrentUser();

        // 1. 用户登录状态检查
        if (currentUser == null) {
            throw new AuthenticationException("用户未登录");
        }

        // 2. 用户状态检查
        if (!currentUser.isActive()) {
            throw new AuthenticationException("用户账户已被禁用");
        }

        // 3. 模块权限检查
        String module = request.getModule();
        if (!hasModulePermission(currentUser, module)) {
            throw new PermissionDeniedException("无权限访问模块: " + module);
        }

        // 4. 操作权限检查
        String action = request.getAction();
        if (!hasActionPermission(currentUser, module, action)) {
            throw new PermissionDeniedException("无权限执行操作: " + action);
        }
    }
}
```

#### 8.3.2 数据脱敏

```java
@Component
public class DataSanitizer {

    public void sanitizeResponse(ApiResponse<?> response) {
        Object data = response.getData();
        if (data instanceof Map) {
            sanitizeMap((Map<String, Object>) data);
        }
    }

    private void sanitizeMap(Map<String, Object> data) {
        // 敏感字段脱敏
        String[] sensitiveFields = {"password", "phone", "idCard", "bankAccount"};

        for (String field : sensitiveFields) {
            if (data.containsKey(field)) {
                data.put(field, maskSensitiveData(data.get(field).toString()));
            }
        }
    }

    private String maskSensitiveData(String data) {
        if (data == null || data.length() <= 4) {
            return "****";
        }
        return data.substring(0, 2) + "****" + data.substring(data.length() - 2);
    }
}
```

---

## 9. 附录

### 9.1 API响应码参考

| 响应码 | 含义 | 说明 |
|--------|------|------|
| 0 | 成功 | 操作成功完成 |
| 400 | 参数错误 | 请求参数格式或内容错误 |
| 401 | 未授权 | 用户未登录或token无效 |
| 403 | 权限不足 | 用户无权限执行此操作 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 500 | 系统错误 | 服务器内部错误 |
| 700 | 业务异常 | 业务逻辑处理异常 |

### 9.2 配置模板

#### 9.2.1 完整的Spring配置模板

```xml
<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="
           http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-4.1.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-4.1.xsd
           http://www.springframework.org/schema/mvc
           http://www.springframework.org/schema/mvc/spring-mvc-4.1.xsd">

    <!-- 导入API标准化模块 -->
    <import resource="classpath:META-INF/spring/api-standard-context.xml" />

    <!-- 扫描业务适配器 -->
    <context:component-scan base-package="com.yourcompany.erp.adapter" />

    <!-- 自定义配置 -->
    <bean id="customApiInterceptor" class="com.yourcompany.erp.interceptor.CustomApiInterceptor"/>

</beans>
```

#### 9.2.2 Maven依赖模板

```xml
<dependencies>
    <!-- ERP API标准化模块 -->
    <dependency>
        <groupId>com.vedeng</groupId>
        <artifactId>erp-api-standard</artifactId>
        <version>1.0-SNAPSHOT</version>
    </dependency>

    <!-- Spring框架 -->
    <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-webmvc</artifactId>
        <version>4.1.9.RELEASE</version>
    </dependency>

    <!-- JSON处理 -->
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>2.6.7</version>
    </dependency>

    <!-- 测试依赖 -->
    <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-test</artifactId>
        <version>4.1.9.RELEASE</version>
        <scope>test</scope>
    </dependency>
</dependencies>
```

### 9.3 快速参考

#### 9.3.1 常用API调用示例

```bash
# 创建采购单
curl -X POST http://localhost:8080/api/v1/buyorder/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "deliveryDirect": 1,
    "saleorderGoodsIds": "1001,1002",
    "traderId": 2001,
    "remark": "测试采购单"
  }'

# 查询采购单详情
curl -X GET "http://localhost:8080/api/v1/buyorder/detail?businessId=12345" \
  -H "Authorization: Bearer your-token"

# 提交审核
curl -X POST http://localhost:8080/api/v1/buyorder/submit \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "businessId": 12345,
    "auditComment": "提交审核"
  }'
```

#### 9.3.2 开发检查清单

- [ ] 添加Maven依赖
- [ ] 导入Spring配置
- [ ] 创建服务适配器
- [ ] 实现数据转换器
- [ ] 编写单元测试
- [ ] 配置日志记录
- [ ] 验证API接口
- [ ] 性能测试
- [ ] 安全检查



---


