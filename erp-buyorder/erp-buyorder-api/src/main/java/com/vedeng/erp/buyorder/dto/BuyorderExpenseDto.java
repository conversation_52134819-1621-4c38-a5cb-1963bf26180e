package com.vedeng.erp.buyorder.dto;

import com.common.dto.StepsNodeDto;
import com.vedeng.common.core.base.BaseDto;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description 采购费用订单
 * <AUTHOR>
 * @date 2022/8/22 13:36
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BuyorderExpenseDto extends BaseDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer buyorderExpenseId;

    /**
     * 采购费用单号
     */
    private String buyorderExpenseNo;
    /**
     * 采购单号
     */
    private String buyorderNo;

    /**
     * 采购ID
     */
    private Integer buyorderId;

    /**
     * 费用单类型0直属采购费用单1非直属采购费用单
     */
    private Integer orderType;

    /**
     * 生效状态 0否 1是
     */
    private Integer validStatus;

    /**
     * 生效时间
     */
    private Date validTime;

    /**
     * 订单状态：0待确认（默认）、1进行中、2已完结、3已关闭
     */
    private Integer status;

    /**
     * 锁定状态0未锁定 1已锁定
     */
    private Integer lockedStatus;

    /**
     * 收票状态0未收票 1部分收票 2全部收票
     */
    private Integer invoiceStatus;

    /**
     * 付款状态 0未付款 1部分付款 2全部付款
     */
    private Integer paymentStatus;

    /**
     * 发货状态0未发货 1部分发货 2全部发货
     */
    private Integer deliveryStatus;

    /**
     * 收货状态0未收货 1部分收货 2全部收货
     */
    private Integer arrivalStatus;

    /**
     * 售后状态 0未售后 1售后中 2售后完成 3售后关闭
     */
    private Integer serviceStatus;

    /**
     * 订单审核状态：0待审核、1审核中、2审核通过、3审核不通过
     */
    private Integer auditStatus;

    /**
     * 收票时间
     */
    private Date invoiceTime;

    /**
     * 付款时间
     */
    private Date paymentTime;

    /**
     * 付款时间
     */
    private Date auditTime;

    /**
     * 是否删除0否1是
     */
    private Integer isDelete;

    /**
     * 开户行
     */
    private String bank;
    /**
     * 账号
     */
    private String bankAccount;
    /**
     * 税号
     */
    private String taxNum;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * @组合对象@ 本来在 detail中 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 发票备注
     */
    private String invoiceComments;

    /**
     * 费用单收票金额
     */
    private BigDecimal invoiceAmount;


    /**
     * 业务类型：1.采购 2.采购售后
     */
    private Integer businessType;

    /**
     * 售后单号
     */
    private String afterSalesNo;

    /**
     * 售后ID
     */
    private Integer afterSalesId;

    /**
     * 采购单付款状态
     */
    private Integer buyorderPaymentStatus;

    /**
     * 是否退货预警 0否1是
     */
    private Integer isReturnEarlyWarn;

    /**
     * 是否是自动转单创建 0 否 1 是
     */
    private Integer isAuto;

    /**
     * @组合对象@ 采购单详情信息
     */
    private BuyorderExpenseDetailDto buyorderExpenseDetailDto;

    /**
     * 采购费用订单商品 包含商品各种状态和 商品主体信息 @see 组装对象
     */
    List<BuyorderExpenseItemDto> buyorderExpenseItemDtos;


    /**
     * @ 组装对象 @
     * 付款计划封装类
     */
    List<PaymentPlanDto> paymentPlans;

    /**
     * @部门@
     */
    private String orgName;

    /**
     * @采购单信息@
     */
    private BuyOrderDto buyOrderDto;

    /**
     * @详情页 订单主状态 节点@
     */
    private List<StepsNodeDto> stepsNodes;

    /**
     * @组合对象自动转单用@ 原费用单id
     */
    private Integer originalBuyorderExpenseId;
    /**
     * @组合对象自动转单用@ 原费用单no
     */
    private String originalBuyorderExpenseNo;

    /**
     * @组合对象自动转单用@ 销售售后单id
     */
    private Integer autoAfterSaleId;



}
