package com.vedeng.erp.trader.common.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/17 16:00
 **/
public enum TagChangeRecordOperTypeEnum {


    ADD(0, "新增"),
    UPDATE(1, "修改"),
    DELETE(2, "删除"),
    ;


    /**
     * 操作类型
     */
    private final Integer type;

    /**
     * 描述
     */
    private final String desc;


    TagChangeRecordOperTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
