package com.vedeng.erp.buyorder.dto;

import com.vedeng.erp.buyorder.domain.entity.Buyorder;
import com.vedeng.erp.trader.dto.TraderSupplierDto;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
public class SettlementBillCheckResult {

    /**
     * 采购单
     */
    private Buyorder buyorder;

    /**
     * 供应商
     */
    private TraderSupplierDto traderSupplierDto;

    /**
     * 采购单返利总金额
     */
    private BigDecimal totalRebateAmount;

}
