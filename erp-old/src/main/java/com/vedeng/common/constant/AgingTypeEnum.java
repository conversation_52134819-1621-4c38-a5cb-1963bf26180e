package com.vedeng.common.constant;

import com.vedeng.common.util.StringUtil;

/**
 * @Description:
 * @Author:       davis
 * @Date:         2021/4/16 下午4:43
 * @Version:      1.0
 */
public enum AgingTypeEnum {
    NORMAL(0 , "正常"),
    APPROACH(1, "临期"),
    OVERDUE(2, "逾期");

    AgingTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;

    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据编码获取编码信息
     * @param code
     * @return
     */
    public static String codeToMessage(Integer code){
        if(code == null){
            return "";
        }
        for(AgingTypeEnum agingTypeEnum : AgingTypeEnum.values()){
            if(code.equals(agingTypeEnum.getCode())){
                return agingTypeEnum.getMsg();
            }
        }
        return "";
    }

}
