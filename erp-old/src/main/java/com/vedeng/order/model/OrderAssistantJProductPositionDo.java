package com.vedeng.order.model;

import java.io.Serializable;
import lombok.Data;

/**
 * T_R_ORDER_ASSISTANT_J_PRODUCT_POSITION
 * <AUTHOR>
@Data
public class OrderAssistantJProductPositionDo implements Serializable {


    public static final Integer PRODUCT_MANAGER_TYPE = 1;

    public static final Integer PRODUCT_ASSITANT_TYPE = 2;

    /**
     * 主键ID
     */
    private Integer orderAssistantJProductPositionId;

    /**
     * 订单助理用户ID
     */
    private Integer orderAsssistantUserId;

    /**
     * 产品职位（产品助理/产品经理）用户ID
     */
    private Integer productPositoinUserId;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 更新时间
     */
    private Long updataTime;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新者
     */
    private Integer updater;

    /**
     * 职位类型(1：产品经理，2：产品助理)
     */
    private Integer positionLevel;




    private static final long serialVersionUID = 1L;
}