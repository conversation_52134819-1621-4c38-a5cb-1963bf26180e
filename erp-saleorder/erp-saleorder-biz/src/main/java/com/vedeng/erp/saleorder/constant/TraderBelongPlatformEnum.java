package com.vedeng.erp.saleorder.constant;

import com.vedeng.orderstream.aftersales.constant.AfterSalesProcessEnum;

/**
 *客户注册平台枚举类
 */
public enum TraderBelongPlatformEnum {
    BDYL("贝登医疗",1),
    YXG("医械购",2),
    KYG("科研购",3),
    JTYWB("集团业务部",4),
    QT("其他",5),
    JC("集采",6),
    DEFAULT_CONSTANT("默认",0);
    private String belongPlatform;

    private Integer code;

    TraderBelongPlatformEnum(String belongPlatform,Integer code){
        this.belongPlatform = belongPlatform;
        this.code = code;
    }

    public static TraderBelongPlatformEnum getInstance(Integer code) {
        for (TraderBelongPlatformEnum v : values()) {
            if (v.code.equals(code)) {
                return v;
            }
        }
        return DEFAULT_CONSTANT;
    }

    public String getBelongPlatform() {
        return belongPlatform;
    }

    public Integer getCode() {
        return code;
    }
}
