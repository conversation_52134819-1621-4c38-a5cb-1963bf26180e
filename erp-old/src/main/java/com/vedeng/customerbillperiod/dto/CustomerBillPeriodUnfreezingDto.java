package com.vedeng.customerbillperiod.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: daniel
 * @Date: 2021/7/26 11 37
 * @Description: 客户账期解冻类
 */
@Data
public class CustomerBillPeriodUnfreezingDto {

    private Integer companyId;

    private Long customerId;

    /**
     * 使用账期的订单ID
     */
    private Long orderId;

    /**
     * 客户账期解冻类型，@CustomerBillPeriodUseTypeEnum
     */
    private Integer unfreezingType;

    /**
     * 关联的ID，售后单id/流水id
     */
    private Long relatedId;

    /**
     * 解冻金额
     */
    private BigDecimal unfreezingAmount;

    private Long addTime;
}
