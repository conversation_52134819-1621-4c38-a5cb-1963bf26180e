package com.newtask;


import com.alibaba.fastjson.JSON;
import com.newtask.dto.TraderDto;
import com.vedeng.authorization.model.Region;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.system.service.RegionService;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.Trader;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.List;

/**
 * 客户仓库地址 导入任务
 */
@Component
@JobHandler(value="traderWarehouseAddressImportTask")
public class TraderWarehouseAddressImportTask extends AbstractJobHandler {

    private Logger logger = LoggerFactory.getLogger(TraderWarehouseAddressImportTask.class);

    @Resource
    private TraderMapper traderMapper;

    @Autowired
    @Qualifier("regionService")
    private RegionService regionService;

    @Override
    public ReturnT<String> doExecute(String requestParam) throws Exception {

        XxlJobLogger.log("traderWarehouseAddressImportTask start.......");

        if(StringUtils.isEmpty(requestParam)){
            XxlJobLogger.log("查询参数不能为空");
            return SUCCESS;
        }

        String traderName = "";
        String province = "";
        String city = "";
        String zone = "";
        String detailAdress = "";
        String[] line = null;
        List<Trader> traderList = null;
        Region zoneRegion = null;
        String fullRegionIdStr = null;

        int successCount = 0;
        int errorCount = 0;

        BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(new FileInputStream(new File(requestParam)),"UTF-8"));

        String lineTxt = null;

        try {
            while((lineTxt = bufferedReader.readLine()) != null){
                try {
                    line = lineTxt.split(",");
                    traderName = line[0];
                    province = line[1];
                    city = line[2];
                    zone = line[3];
                    detailAdress = line[4];
                    fullRegionIdStr = "";
                    zoneRegion = null;

                    traderList = this.traderMapper.getTraderByTraderName(traderName);
                    if(CollectionUtils.isEmpty(traderList)){
                        XxlJobLogger.log("客户仓库地址导入错误，无对应的客户:"+lineTxt);
                        errorCount++;
                        continue;
                    }

                    if(StringUtils.isBlank(zone)){
                        detailAdress = province + city + detailAdress;
                        if(StringUtils.isBlank(detailAdress)){
                            XxlJobLogger.log("客户仓库地址导入错误,只有区的情况下，详细地址为空:"+lineTxt);
                            errorCount++;
                            continue;
                        }
                    }else {

                        try{
                            zoneRegion = regionService.getRegionByReginFullName(zone,3,null);
                        }catch (Exception e){
                            Region cityRegion = regionService.getRegionByReginFullName(city,2,null);
                            zoneRegion = regionService.getRegionByReginFullName(zone,3,cityRegion.getRegionId());
                        }

                        if(zoneRegion == null){
                            XxlJobLogger.log("客户仓库地址导入错误，找不到对应的区:"+lineTxt);
                            errorCount++;
                            continue;
                        }

                        fullRegionIdStr = regionService.getRegionIdStringByMinRegionId(zoneRegion.getRegionId());

                    }

                    for(Trader trader : traderList){
                        TraderDto traderDto = new TraderDto();
                        traderDto.setTraderId(trader.getTraderId());
                        traderDto.setWarehouseAreaId(zoneRegion == null ? null: zoneRegion.getRegionId());
                        traderDto.setWarehouseAreaIds(fullRegionIdStr);
                        traderDto.setWarehouseDetailAddress(detailAdress);

                        XxlJobLogger.log("更新客户的仓库地址:" + JSON.toJSONString(traderDto));
                        traderMapper.updateTraderWarehouseAdressById(traderDto);
                    }

                    successCount++;

                }catch (Exception e){
                    errorCount++;
                    XxlJobLogger.log("客户仓库地址导入错误:"+lineTxt +":"+e.getMessage());
                }
            }
        }finally {
            bufferedReader.close();
        }

        XxlJobLogger.log("成功的数据条数:" + successCount);
        XxlJobLogger.log("失败的数据条数:" + errorCount);

        XxlJobLogger.log("traderWarehouseAddressImportTask end.......");

        return SUCCESS;
    }

}
