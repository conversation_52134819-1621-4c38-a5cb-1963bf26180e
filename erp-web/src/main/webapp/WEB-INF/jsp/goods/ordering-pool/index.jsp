<%--
  Created by IntelliJ IDEA.
  User: admin
  Date: 2021/5/18
  Time: 14:17
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="定品池" scope="application" />
<%@ include file="../../common/common.jsp"%>
<html>
<head>
</head>
<body>
    <div class="content">
        <div class="searchfunc">
            <form action="${pageContext.request.contextPath}/ordering-pool/goods/index.do" method="post" id="search">
                <br />
                <ul>
                    <li>
                        <label class="infor_name">订货号</label>
                        <input type="text" class="input-middle" name="skuNo" id="skuNo" value="${orderingPoolQueryVO.skuNo}" placeholder="请输入订货号">
                    </li>
                    <li>
                        <label class="infor_name">商品名称</label>
                        <input type="text" class="input-middle" name="goodsName" id="goodsName" value="${orderingPoolQueryVO.goodsName}" placeholder="请输入商品名称">
                    </li>
                    <li>
                        <label class="infor_name">品牌</label>
                        <input type="text" class="input-middle J-suggest-input" placeholder="请输入品牌" data-url="/firstengage/brand/brandName.do" name="brandName" value="${orderingPoolQueryVO.brandName}">
                    </li>
                    <li>
                        <label class="infor_name">商品类型</label>
                        <select class="input-middle f_left" name="goodsType">
                            <option value="-1">全部</option>
                            <c:forEach var="list" items="${goodsTypes}">
                                <option value="${list.sysOptionDefinitionId}" <c:if test="${orderingPoolQueryVO.goodsType == list.sysOptionDefinitionId}">selected="selected"</c:if> >${list.title}</option>
                            </c:forEach>
                        </select>
                    </li>
                    <li>
                        <label class="infor_name">商品分类</label>
                        <span id="category_div">
                            <select id='categoryOpt0' name='categoryOpt0' onchange="getChildrenCategory(this)" style = "width:100px;">
                                <option value="-1" id="1">请选择</option>
                                <c:forEach var="list" items="${categoryOptList}" varStatus="status">
                                    <option value="${list.categoryId}" <c:if test="${list.categoryId == orderingPoolQueryVO.categoryOpt0}">selected</c:if>>${list.categoryName}</option>
                                </c:forEach>
                            </select> &nbsp;
                            <select id='categoryOpt1' name='categoryOpt1' onchange="getChildrenCategory(this)" style = "width:100px;">
                                <option value="-1" id="1">请选择</option>
                                <c:forEach var="list0" items="${categoryOptList}" varStatus="status0">
                                    <c:if test="${list0.categoryId == orderingPoolQueryVO.categoryOpt0}">
                                        <c:forEach var="list" items="${list0.childrenCategoryList}" varStatus="status">
                                            <option value="${list.categoryId}" <c:if test="${list.categoryId == orderingPoolQueryVO.categoryOpt1}">selected</c:if>>${list.categoryName}</option>
                                        </c:forEach>
                                    </c:if>
                                </c:forEach>
                            </select> &nbsp;
                            <select id='categoryOpt2' name='categoryOpt2' onchange="getChildrenCategory(this)" style = "width:100px;">
                                <option value="-1" id="1">请选择</option>
                                <c:forEach var="list0" items="${categoryOptList}" varStatus="status0">
                                    <c:if test="${list0.categoryId == orderingPoolQueryVO.categoryOpt0}">
                                        <c:forEach var="list1" items="${list0.childrenCategoryList}" varStatus="status1">
                                            <c:if test="${list1.categoryId == orderingPoolQueryVO.categoryOpt1}">
                                                <c:forEach var="list2" items="${list1.childrenCategoryList}" varStatus="status2">
                                                    <option value="${list2.categoryId}" <c:if test="${list2.categoryId == orderingPoolQueryVO.categoryOpt2}">selected</c:if>>${list2.categoryName}</option>
                                                </c:forEach>
                                            </c:if>
                                        </c:forEach>
                                    </c:if>
                                </c:forEach>
                            </select>
                        </span>
                        <input type="hidden" class="input-middle" name="categoryId" id="categoryId" value="${orderingPoolQueryVO.categoryId}">
                    </li>
                    <li>
                        <label class="infor_name">归属人</label>
                        <select class="input-middle f_left" name="goodsUserId">
                            <option value="-1">全部</option>
                            <c:if test="${not empty goodsManagers }">
                                <c:forEach items="${goodsManagers }" var="item">
                                    <option value="${item.userId }"
                                            <c:if test="${orderingPoolQueryVO.goodsUserId eq item.userId }">selected="selected"</c:if>>${item.username }</option>
                                </c:forEach>
                            </c:if>
                        </select>
                    </li>
                    <li>
                        <label class="infor_name">定品状态</label>
                        <select class="input-middle f_left" name="skuStatus" onchange="showOrHiddenBatchDelete();search();">
                            <option value="0" <c:if test="${orderingPoolQueryVO.skuStatus == 0}">selected="selected"</c:if>>启&nbsp;用</option>
                            <option value="1" <c:if test="${orderingPoolQueryVO.skuStatus == 1}">selected="selected"</c:if>>已删除</option>
                        </select>
                    </li>
                    <input type="hidden" class="input-middle" name="pageNo" id="pageNo" value="${page.pageNo}">
                    <input type="hidden" class="input-middle" name="pageSize" id="pageSize" value="${page.pageSize}">
                </ul>
                <div class="tcenter">
                    <span class="bg-light-blue bt-bg-style bt-middle" onclick="search();" id="searchSpan">搜索</span>
                    <span class="bg-light-blue bt-bg-style bt-middle" onclick="reset();showOrHiddenBatchDelete();$('#categoryId').val('');">重置</span>

                </div>
            </form>
        </div>
        <br />
        <div class="btnlist">
            &nbsp;&nbsp;
            <span class="bg-light-orange bt-bg-style bt-largest"
                  style="padding-left: 24px; padding-right: 24px" id="recommendOrdering">推荐定品</span>
            <script>
                $('#recommendOrdering').on('click', function(){
                    layer.open({
                        type: 2,
                        title:'推荐定品',
                        closeBtn: 1,
                        btn: ['返回定品池', '加入定品'],
                        anim: 2,
                        offset: '20px',
                        skin: 'demo-class',
                        area: ['97%', '800px'],
                        shadeClose: false,
                        content: './recommendOrdering.do',
                        btnAlign: 'c',
                        fixed: false,
                        yes:function (index, layero){
                            // console.log(index,layero);
                            layer.close(index);
                        },
                        btn2:function (index, layero) {
                            //获得弹出层iframe
                            let iframe = window["layui-layer-iframe" + index].callbackdata();
                            // console.log(iframe)
                            return false;
                        }

                    });

                });
            </script>

            <span class="bg-light-blue bt-bg-style bt-largest" style="padding-left: 24px; padding-right: 24px" id="newAddOrdering">新增定品</span>
            <script>
                $('#newAddOrdering').on('click', function(){
                    layer.open({
                        type: 2,
                        title:'新增定品',
                        closeBtn: 1,
                        btn: ['返回定品池', '加入定品'],
                        anim: 2,
                        offset: '20px',
                        skin: 'demo-class',
                        area: ['97%', '800px'],
                        shadeClose: false,
                        content: './newAddOrdering.do',
                        btnAlign: 'c',
                        fixed: false,
                        yes:function (index, layero){
                            // console.log(index,layero);
                            layer.close(index);
                        },
                        btn2:function (index, layero) {
                            //获得弹出层iframe
                            let iframe = window["layui-layer-iframe" + index].callbackdata1();
                            // console.log(iframe)
                            return false;
                        }

                    });

                });
            </script>

            <span class="bg-light-blue bt-bg-style bt-largest" style="padding-left: 24px; padding-right: 24px"  onclick="batchAddOrdering();">导入定品</span>
            <script>
                function batchAddOrdering() {
                    let open = layer.open({
                        type: 1,
                        title: '批量导入定品',
                        shadeClose: false,
                        area : ['600px', '240px'],
                        content: '<iframe style="width: calc(100% - 10px);height: calc(100% - 10px); border: 0;" src="/ordering-pool/goods/batchAddOrdering.do"></iframe>',
                        success: function(layero, index) {

                        }
                    });
                }
            </script>

            <span class="bg-light-blue bt-bg-style bt-largest" style="padding-left: 24px; padding-right: 24px"  onclick="exportOrderingList();">导出定品列表</span>
            <script>
                function exportOrderingList() {
                    layer.open({
                        type: 2,
                        title:'导出定品列表',
                        closeBtn: 1,
                        btn: ['取消', '导出'],
                        anim: 2,
                        offset: '20px',
                        area: ['50%', '60%'],
                        skin: 'demo-class',
                        shadeClose: false,
                        content: './initExportOrderingListPage.do',
                        btnAlign: 'c',
                        fixed: true,
                        yes:function (index, layero){
                            // console.log(index,layero);
                            layer.close(index);
                        },
                        btn2:function (index, layero) {
                            //获得弹出层iframe
                            let iframe = window["layui-layer-iframe" + index].callbackdata4();
                            return false;
                        }});
                }
            </script>

        </div>
        <br />
        <div class="">
            <div class="superdiv" style="width:100%;">
                <table class="table table-bordered table-striped table-condensed table-centered">
                    <thead>
                        <tr>
                            <th rowspan="2" class="wid1" >
                                <div class="input-checkbox">
                                    <label class="input-wrap">
                                        <input type="checkbox" class="J-select-list-all">
                                        <span class="input-ctnr"></span>
                                    </label>
                                </div>
                            </th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">订货号</span></th>
                            <th rowspan="2" class="wid8"><span style="font-weight: bold">商品名称</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">品牌</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">商品类型</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">二级分类</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">规格</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">商品单位</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">成本价(元)</span></th>
                            <th colspan="2" class="wid5"><span style="font-weight: bold">去年销售量</span></th>
                            <th colspan="2" class="wid5"><span style="font-weight: bold">近三个月销售量</span></th>
                            <th colspan="2" class="wid5"><span style="font-weight: bold">近一个月销售量</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">归属人</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">最新一次加入定品池日期</span></th>
                            <th rowspan="2" class="wid4"><span style="font-weight: bold">操作项</span></th>
                        </tr>
                        <tr style="color: #00a0e9;">
                            <th><span style="font-weight: bold">总</span></th>
                            <th><span style="font-weight: bold">医械购</span></th>
                            <th><span style="font-weight: bold">总</span></th>
                            <th><span style="font-weight: bold">医械购</span></th>
                            <th><span style="font-weight: bold">总</span></th>
                            <th><span style="font-weight: bold">医械购</span></th>
                        </tr>
                    </thead>

                    <tbody>
                        <c:if test="${not empty orderingPoolListPage}">
                            <c:forEach items="${orderingPoolListPage}" var="item" varStatus="status">
                                <tr>
                                    <td>
                                        <div class="tr-item">
                                            <div class="input-checkbox">
                                                <label class="input-wrap">
                                                    <input type="checkbox" class="J-select-sku" name="skuId" value="${item.skuId}">
                                                </label>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value" name="skuNo">${item.skuNo}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value">${item.goodsName}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value">${item.brandName}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value">${item.goodsTypeName}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value">${item.categoryNameTwo}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value">${item.spec}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value">${item.unitName}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value">${item.costPrice}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value" id="lastYearSum${item.skuId}">${item.lastYearSum}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value" id="lastYearPart${item.skuId}">${item.lastYearPart}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value" id="threeMonthSum${item.skuId}">${item.threeMonthSum}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value" id="threeMonthPart${item.skuId}">${item.threeMonthPart}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value" id="oneMonthSum${item.skuId}">${item.oneMonthSum}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value" id="oneMonthPart${item.skuId}">${item.oneMonthPart}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value">${item.goodsUserName}<br>${item.goodsAssistantName}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value"><date:date value="${item.latestJoinDate}" format="yyyy-MM-dd" /></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="tr-item">
                                            <span class="item-value">
                                                <c:if test="${item.skuStatus == 0}">
                                                    <a href="javascript:void(0);" onclick="removePool(['${item.skuNo}'])">删除</a>
                                                </c:if>
                                                <c:if test="${item.skuStatus == 1}">
                                                    <a href="javascript:void(0);" onclick="removePoolCause(${item.skuId})">删除原因</a>
                                                    &nbsp;&nbsp;
                                                    <a href="javascript:void(0);" onclick="joinPool([${item.skuId}])">加入定品</a>
                                                </c:if>
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                            </c:forEach>
                        </c:if>

                    </tbody>
                </table>
            </div>
            <c:if test="${empty orderingPoolListPage}">
                <!-- 查询无结果弹出 -->
                <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
            </c:if>
        </div>
        <br />
        <div class="parts" style="height: 30px;">
            <tags:page page="${page}" />
        </div>
    </div>

    <div class="del-div" align="center">
            <span class="bg-light-blue bt-bg-style bt-largest" style="padding-left: 24px; padding-right: 24px" id="batchDelete"  onclick="batchRemovePool()"
                  <c:if test="${orderingPoolQueryVO.skuStatus == 1}">hidden="hidden"</c:if>>批量删除</span>
    </div>
    <%@ include file="../../common/footer.jsp"%>
</body>
</html>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/inputSuggest.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/goods/ordering-pool/ordering-pool.js?rnd=${resourceVersionKey}"></script>
<style>

    .suggest-list-wrap {
        position: absolute;
        background: #fff;
        padding: 10px 0;
        box-shadow: 2px 2px 3px rgba(0, 33, 66, 0.1);
        overflow: unset;
        z-index: 11;
        border: 1px solid #ced2d9;
        width: 100%;
        display: none;
        max-height: 275px;
        overflow-y: auto;
        left: 100px;
    }

    .suggest-wrap {
        position: relative;
    }

    .suggest-list-wrap .suggest-item {
        padding: 5px 10px;
        color: #666;
        display: block;
        text-decoration: none;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        cursor: pointer;
    }

    .suggest-list-wrap .suggest-item strong {
        color: #f60;
        font-size: 12px;
    }

    .suggest-list-wrap .suggest-item.disabled {
        color: #c2c2c2;
        cursor: default;
    }

    .suggest-list-wrap .suggest-item.disabled strong {
        color: #c2c2c2;
        cursor: default;
        font-weight: normal;
    }

     body .demo-class .layui-layer-btn0{
         background: white;
         color: #1b76ed;
         border: 1px solid #1b76ed;
     }
    body .demo-class .layui-layer-btn1{
        background: #0175e2;
        color: #fff;
    }
</style>