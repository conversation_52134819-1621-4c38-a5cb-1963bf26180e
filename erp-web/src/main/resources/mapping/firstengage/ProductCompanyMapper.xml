<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.firstengage.dao.ProductCompanyMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.firstengage.model.ProductCompany">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:36:27 CST 2019.
    -->
    <id column="PRODUCT_COMPANY_ID" jdbcType="INTEGER" property="productCompanyId" />
    <result column="PRODUCT_COMPANY_CHINESE_NAME" jdbcType="VARCHAR" property="productCompanyChineseName" />
    <result column="PRODUCT_COMPANY_ENGLISH_NAME" jdbcType="VARCHAR" property="productCompanyEnglishName" />
    <result column="PRODUCT_COMPANY_ADDRESS" jdbcType="VARCHAR" property="productCompanyAddress" />
    <result column="PRODUCT_COMPANY_LICENCE" jdbcType="VARCHAR" property="productCompanyLicence" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:36:27 CST 2019.
    -->
    PRODUCT_COMPANY_ID, PRODUCT_COMPANY_CHINESE_NAME, PRODUCT_COMPANY_ENGLISH_NAME, PRODUCT_COMPANY_ADDRESS, PRODUCT_COMPANY_LICENCE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:36:27 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_PRODUCT_COMPANY
    where PRODUCT_COMPANY_ID = #{productCompanyId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:36:27 CST 2019.
    -->
    delete from T_PRODUCT_COMPANY
    where PRODUCT_COMPANY_ID = #{productCompanyId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.firstengage.model.ProductCompany">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:36:27 CST 2019.
    -->
    insert into T_PRODUCT_COMPANY (PRODUCT_COMPANY_ID, PRODUCT_COMPANY_CHINESE_NAME, 
      PRODUCT_COMPANY_ENGLISH_NAME, PRODUCT_COMPANY_ADDRESS
      )
    values (#{productCompanyId,jdbcType=INTEGER}, #{productCompanyChineseName,jdbcType=VARCHAR}, 
      #{productCompanyEnglishName,jdbcType=VARCHAR}, #{productCompanyAddress,jdbcType=VARCHAR}
      )
  </insert>
  
  <insert id="insertSelective" parameterType="com.vedeng.firstengage.model.ProductCompany" useGeneratedKeys="true" keyProperty="productCompanyId" >
    insert into T_PRODUCT_COMPANY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="productCompanyId != null">
        PRODUCT_COMPANY_ID,
      </if>
      <if test="productCompanyChineseName != null">
        PRODUCT_COMPANY_CHINESE_NAME,
      </if>
      <if test="productCompanyEnglishName != null">
        PRODUCT_COMPANY_ENGLISH_NAME,
      </if>
      <if test="productCompanyAddress != null">
        PRODUCT_COMPANY_ADDRESS,
      </if>
      <if test="productCompanyLicence != null">
        PRODUCT_COMPANY_LICENCE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="productCompanyId != null">
        #{productCompanyId,jdbcType=INTEGER},
      </if>
      <if test="productCompanyChineseName != null">
        #{productCompanyChineseName,jdbcType=VARCHAR},
      </if>
      <if test="productCompanyEnglishName != null">
        #{productCompanyEnglishName,jdbcType=VARCHAR},
      </if>
      <if test="productCompanyAddress != null">
        #{productCompanyAddress,jdbcType=VARCHAR},
      </if>
      <if test="productCompanyLicence != null">
        #{productCompanyLicence,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.firstengage.model.ProductCompany">
    update T_PRODUCT_COMPANY
    <set>
      <if test="productCompanyChineseName != null">
        PRODUCT_COMPANY_CHINESE_NAME = #{productCompanyChineseName,jdbcType=VARCHAR},
      </if>
      <if test="productCompanyEnglishName != null">
        PRODUCT_COMPANY_ENGLISH_NAME = #{productCompanyEnglishName,jdbcType=VARCHAR},
      </if>
      <if test="productCompanyAddress != null">
        PRODUCT_COMPANY_ADDRESS = #{productCompanyAddress,jdbcType=VARCHAR},
      </if>
      <if test="productCompanyLicence != null">
        PRODUCT_COMPANY_LICENCE = #{productCompanyLicence,jdbcType=VARCHAR},
      </if>
    </set>
    where PRODUCT_COMPANY_ID = #{productCompanyId,jdbcType=INTEGER}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.vedeng.firstengage.model.ProductCompany">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:36:27 CST 2019.
    -->
    update T_PRODUCT_COMPANY
    set PRODUCT_COMPANY_CHINESE_NAME = #{productCompanyChineseName,jdbcType=VARCHAR},
      PRODUCT_COMPANY_ENGLISH_NAME = #{productCompanyEnglishName,jdbcType=VARCHAR},
      PRODUCT_COMPANY_ADDRESS = #{productCompanyAddress,jdbcType=VARCHAR}
    where PRODUCT_COMPANY_ID = #{productCompanyId,jdbcType=INTEGER}
  </update>

  <insert id="insertList">
    insert into T_PRODUCT_COMPANY (PRODUCT_COMPANY_CHINESE_NAME,
    PRODUCT_COMPANY_ENGLISH_NAME, PRODUCT_COMPANY_ADDRESS
    )
    values
    <foreach collection="list" item="reg" separator=",">
      (#{reg.productCompany.productCompanyChineseName,jdbcType=VARCHAR},
      #{reg.productCompany.productCompanyEnglishName,jdbcType=VARCHAR},
      #{reg.productCompany.productCompanyAddress,jdbcType=VARCHAR})
    </foreach>

  </insert>

  <select id="getallcompany" resultType="java.util.Map">
      SELECT
      a.PRODUCT_COMPANY_CHINESE_NAME AS label,
      a.PRODUCT_COMPANY_ID AS value
      FROM
      T_PRODUCT_COMPANY a
      where
      1=1
    <if test="productCompanyName !=null and  productCompanyName !='' ">
        and   a.PRODUCT_COMPANY_CHINESE_NAME LIKE CONCAT(CONCAT('%', #{productCompanyName, jdbcType=VARCHAR}),'%')
      </if>
      GROUP BY
      a.PRODUCT_COMPANY_CHINESE_NAME
      limit 0,5
  </select>
  <select id="getByProductCompanyName" resultType="int" resultMap="BaseResultMap">
    SELECT
      <include refid="Base_Column_List"/>
    FROM
    T_PRODUCT_COMPANY
    where
      PRODUCT_COMPANY_CHINESE_NAME = #{productCompanyName, jdbcType=VARCHAR}
    limit 1
  </select>
</mapper>