/* 天眼查详情 */
Vue.component('ui-tyc-detail', {
    template: `
        <crm-slide-dialog ref="tycDetailDialog" title="天眼查公司信息">
            <div class="tyc-detail-panel">
                <template v-if="tycInfo && Object.keys(tycInfo).length">
                    <div class="tyc-detail-top">
                        <div class="tyc-name">{{ tycInfo.name }}</div>
                        <div class="tyc-tags">
                            <span class="tag" v-for="(tag, tind) in tycInfo.tags" :key="tind">{{ tag }}</span>
                        </div>
                    </div>
                    <div class="tyc-detail-bottom">
                        <div class="tyc-attr">
                            <ui-form-item label="地区">
                                <template v-if="tycInfo.base">{{ tycInfo.base }}</template>
                                <template v-if="tycInfo.base && tycInfo.city"> / </template>
                                <template v-if="tycInfo.city">{{ tycInfo.city }}</template>
                                <template v-if="tycInfo.city && tycInfo.district"> / </template>
                                <template v-if="tycInfo.district">{{ tycInfo.district }}</template>
                            </ui-form-item>
                            <ui-form-item label="注册地址">{{ tycInfo.regLocation || '-' }}</ui-form-item>
                            <ui-form-item label="曾用名">{{ tycInfo.historyNames || '-' }}</ui-form-item>
                            <ui-form-item label="法人">{{ tycInfo.legalPersonName || '-' }}</ui-form-item>
                            <ui-form-item label="注册资本">{{ tycInfo.regCapital || '-' }}</ui-form-item>
                            <ui-form-item label="纳税人识别号">{{ tycInfo.taxNumber || '-' }}</ui-form-item>
                            <ui-form-item label="企业联系方式">{{ tycInfo.phoneNumber || '-' }}</ui-form-item>
                            <ui-form-item label="行业">{{ tycInfo.industry || '-' }}</ui-form-item>
                            <ui-form-item label="国民经济行业分\n类">{{ category_Big_Middle_Small || '-' }}</ui-form-item>
                            <ui-form-item label="成立日期">{{ tycInfo.estiblishTime | filterDetailDateTime }}</ui-form-item>
                            <ui-form-item label="网址">{{ tycInfo.websiteList || '-' }}</ui-form-item>
                            <ui-form-item label="经营范围">{{ tycInfo.businessScope || '-' }}</ui-form-item>
                        </div>
                    </div>
                </template>
                <div class="erp-load-empty" v-else>
                    <img src="/mstatic/image/empty/search-empty.svg"/>
                    <p>抱歉，未能匹配到天眼查公司数据，</p>
                    <p>建议您检查关键词重新撞索</p>
                </div>
            </div>
        </crm-slide-dialog>
    `,
    data() {
        return {
            traderName: '', // 客户名称
            tycInfo: {}, // 天眼查公司信息
        };
    },
    mounted() {
        
    },
    filters: {
        filterDetailDateTime (val) {
            if (/\d{10,}/.test(val)) {
                return moment(val).format('YYYY-MM-DD');
            } else {
                return '-'
            }
        },
    },
    computed: {
        category_Big_Middle_Small () {
            let arr = [];
            this.tycInfo.category && arr.push(this.tycInfo.category);
            this.tycInfo.categoryBig && arr.push(this.tycInfo.categoryBig);
            this.tycInfo.categoryMiddle && arr.push(this.tycInfo.categoryMiddle);
            this.tycInfo.categorySmall && arr.push(this.tycInfo.categorySmall);
            return arr.join(' / ')
        }
    },
    methods: {
        async show (traderName, tycInfo) {
            this.traderName = traderName || '';
            this.tycInfo = tycInfo || {};
            if (!(this.tycInfo && Object.keys(this.tycInfo).length)) {
                let fetxhDetail = await this.getDetail(this.traderName);
                if (fetxhDetail.success) {
                    this.tycInfo = fetxhDetail.data || {};
                }
            }
            this.$refs.tycDetailDialog.show();
        },

        // 获取详情信息
        getDetail (name) {
            return new Promise((resolve, reject) => {
                this.$axios.post(`/crm/trader/profile/queryTycDetail?traderName=${name}`).then(({data}) => {
                    resolve(data || {});
                }).catch(errr=> {
                    reject(errr || {});
                    console.log('fetch error', errr);
                })
            })
        },
    }
})