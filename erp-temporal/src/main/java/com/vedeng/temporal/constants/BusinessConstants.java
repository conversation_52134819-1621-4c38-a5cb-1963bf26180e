package com.vedeng.temporal.constants;

/**
 * 多公司业务常量定义
 * 统一管理业务操作类型、错误代码等常量
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
public final class BusinessConstants {

    private BusinessConstants() {
        // 工具类，禁止实例化
    }

    /**
     * 业务操作类型常量
     */
    public static final class BusinessOperationType {
        public static final String SALES_ORDER = "SALES_ORDER";
        public static final String PURCHASE_ORDER = "PURCHASE_ORDER";
        public static final String INVENTORY = "INVENTORY";
        public static final String INVOICE = "INVOICE";
        public static final String PAYMENT = "PAYMENT";
        // 新增：三链条架构的业务操作类型
        public static final String SALES_INVOICE = "SALES_INVOICE";
        public static final String INVOICE_ENTRY = "INVOICE_ENTRY";

        private BusinessOperationType() {}
    }

    /**
     * 业务类型常量
     */
    public static final class BusinessType {
        public static final String PURCHASE_BUSINESS = "PURCHASE_BUSINESS";
        public static final String PAYMENT_BUSINESS = "PAYMENT_BUSINESS";
        public static final String PURCHASE_SALES_INVENTORY_INVOICE = "PURCHASE_SALES_INVENTORY_INVOICE";
        public static final String PAYMENT_TRANSFER = "PAYMENT_TRANSFER";

        private BusinessType() {}
    }

    /**
     * 业务步骤常量
     */
    public static final class BusinessStep {
        public static final String STEP_1 = "STEP_1";  // 生成销售单
        public static final String STEP_2 = "STEP_2";  // 生成采购单
        public static final String STEP_3 = "STEP_3";  // 生成入库单
        public static final String STEP_4 = "STEP_4";  // 生成发票
        public static final String STEP_PAYMENT = "STEP_PAYMENT";  // 生成付款单

        private BusinessStep() {}
    }

    /**
     * 错误代码常量
     */
    public static final class ErrorCode {
        // API相关错误
        public static final String API_CONFIG_NOT_FOUND = "API_CONFIG_NOT_FOUND";
        public static final String API_CALL_FAILED = "API_CALL_FAILED";
        public static final String API_TIMEOUT = "API_TIMEOUT";
        public static final String API_RESPONSE_PARSE_ERROR = "API_RESPONSE_PARSE_ERROR";

        // 业务数据相关错误
        public static final String TARGET_COMPANY_EMPTY = "TARGET_COMPANY_EMPTY";
        public static final String BUSINESS_DATA_EMPTY = "BUSINESS_DATA_EMPTY";
        public static final String BUSINESS_DATA_FORMAT_ERROR = "BUSINESS_DATA_FORMAT_ERROR";
        public static final String TARGET_COMPANY_API_NOT_CONFIGURED = "TARGET_COMPANY_API_NOT_CONFIGURED";

        // 工作流相关错误
        public static final String WORKFLOW_START_ERROR = "WORKFLOW_START_ERROR";
        public static final String WORKFLOW_EXECUTION_ERROR = "WORKFLOW_EXECUTION_ERROR";

        // 系统相关错误
        public static final String SYSTEM_ERROR = "SYSTEM_ERROR";
        public static final String OPERATION_ERROR = "OPERATION_ERROR";
        public static final String RETRY_EXHAUSTED = "RETRY_EXHAUSTED";
        public static final String NOTIFICATION_ERROR = "NOTIFICATION_ERROR";
        public static final String LOG_ERROR = "LOG_ERROR";
        public static final String EXCEPTION_HANDLER_ERROR = "EXCEPTION_HANDLER_ERROR";

        // 业务处理器相关错误
        public static final String UNSUPPORTED_OPERATION_TYPE = "UNSUPPORTED_OPERATION_TYPE";
        public static final String BUSINESS_OPERATION_ERROR = "BUSINESS_OPERATION_ERROR";
        public static final String PROCESSOR_NOT_FOUND = "PROCESSOR_NOT_FOUND";
        public static final String SALES_ORDER_ERROR = "SALES_ORDER_ERROR";
        public static final String PURCHASE_ORDER_ERROR = "PURCHASE_ORDER_ERROR";
        public static final String INVENTORY_ERROR = "INVENTORY_ERROR";
        public static final String INVOICE_ERROR = "INVOICE_ERROR";
        public static final String SALES_INVOICE_ERROR = "SALES_INVOICE_ERROR";
        public static final String PAYMENT_ERROR = "PAYMENT_ERROR";

        // 工作流管理相关错误
        public static final String WORKFLOW_QUERY_ERROR = "WORKFLOW_QUERY_ERROR";
        public static final String WORKFLOW_STOP_ERROR = "WORKFLOW_STOP_ERROR";
        public static final String WORKFLOW_HISTORY_ERROR = "WORKFLOW_HISTORY_ERROR";

        private ErrorCode() {}
    }

    /**
     * HTTP相关常量
     */
    public static final class HttpConstants {
        public static final String METHOD_POST = "POST";
        public static final String METHOD_GET = "GET";
        public static final String USER_AGENT = "ERP-Temporal-Client/1.0";
        public static final String CONTENT_TYPE_JSON = "application/json";

        private HttpConstants() {}
    }

    /**
     * 工作流相关常量
     */
    public static final class WorkflowConstants {
        public static final String WORKFLOW_ID_PREFIX = "multi-company-";
        public static final String DEFAULT_TASK_QUEUE = "erp-multi-company-queue";
        public static final int DEFAULT_WORKFLOW_TIMEOUT_HOURS = 2;
        public static final int DEFAULT_WORKFLOW_TASK_TIMEOUT_MINUTES = 10; // 增加到10分钟

        private WorkflowConstants() {}
    }

    /**
     * 缓存相关常量
     */
    public static final class CacheConstants {
        public static final int DEFAULT_CACHE_SIZE = 1000;
        public static final int DEFAULT_CACHE_EXPIRE_MINUTES = 10;
        public static final String API_CONFIG_CACHE_NAME = "apiConfigCache";

        private CacheConstants() {}
    }

    /**
     * 重试相关常量
     */
    public static final class RetryConstants {
        public static final int MAX_RETRY_ATTEMPTS = 3;
        public static final long INITIAL_RETRY_DELAY_MS = 1000L;
        public static final double RETRY_BACKOFF_MULTIPLIER = 2.0;

        private RetryConstants() {}
    }

    /**
     * 日志相关常量
     */
    public static final class LogConstants {
        public static final String BUSINESS_OPERATION_START = "开始执行业务操作：{}，目标公司：{}";
        public static final String BUSINESS_OPERATION_SUCCESS = "{}操作成功，目标公司：{}，单据ID：{}";
        public static final String BUSINESS_OPERATION_FAILED = "{}操作失败，目标公司：{}";
        public static final String API_CALL_START = "调用公司API，URL：{}，Method：{}";
        public static final String WORKFLOW_START = "启动多公司业务工作流，业务ID：{}";
        public static final String WORKFLOW_START_SUCCESS = "多公司业务工作流启动成功，工作流ID：{}";

        private LogConstants() {}
    }
}
