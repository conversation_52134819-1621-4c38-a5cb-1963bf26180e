<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    
    <!-- 控制台输出 -->
    <appender name="consoleLog" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>
                %d{yyyy-MM-dd HH:mm:ss.SSS} [%level] [temporal] [%X{traceId}] [%thread] [s]%logger[%L][e]-%msg %n
            </pattern>
        </layout>
    </appender>

    <!-- 文件输出 -->
    <appender name="fileInfoLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <pattern>
                %d{yyyy-MM-dd HH:mm:ss.SSS} [%level] [temporal] [%X{traceId}] [%thread] [s]%logger[%L][e]-%msg %n
            </pattern>
        </encoder>
        <!-- 滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 路径 -->
            <fileNamePattern>/app/logs/temporal/temporal.%d.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 抑制 Temporal 心跳相关的错误日志 -->
    <logger name="io.temporal.internal.activity.HeartbeatContextImpl" level="ERROR" additivity="false">
        <!-- 只输出 ERROR 级别以上的日志，过滤掉 WARN 级别的心跳失败日志 -->
        <appender-ref ref="consoleLog"/>
        <appender-ref ref="fileInfoLog"/>
    </logger>
    
    <!-- 抑制 gRPC 连接相关的错误日志 -->
    <logger name="io.grpc" level="ERROR" additivity="false">
        <appender-ref ref="consoleLog"/>
        <appender-ref ref="fileInfoLog"/>
    </logger>
    
    <!-- 抑制 Netty 连接相关的错误日志 -->
    <logger name="io.grpc.netty" level="ERROR" additivity="false">
        <appender-ref ref="consoleLog"/>
        <appender-ref ref="fileInfoLog"/>
    </logger>

    <!-- Temporal 相关日志配置 -->
    <logger name="com.vedeng.temporal" level="INFO" additivity="false">
        <appender-ref ref="consoleLog"/>
        <appender-ref ref="fileInfoLog"/>
    </logger>

    <!-- 根日志配置 -->
    <root level="INFO">
        <appender-ref ref="consoleLog"/>
        <appender-ref ref="fileInfoLog"/>
    </root>

</configuration>
