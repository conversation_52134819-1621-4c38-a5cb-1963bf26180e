package com.vedeng.goods.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SpuAttrMappingGenerateExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public SpuAttrMappingGenerateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSpuAttrIdIsNull() {
            addCriterion("SPU_ATTR_ID is null");
            return (Criteria) this;
        }

        public Criteria andSpuAttrIdIsNotNull() {
            addCriterion("SPU_ATTR_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSpuAttrIdEqualTo(Integer value) {
            addCriterion("SPU_ATTR_ID =", value, "spuAttrId");
            return (Criteria) this;
        }

        public Criteria andSpuAttrIdNotEqualTo(Integer value) {
            addCriterion("SPU_ATTR_ID <>", value, "spuAttrId");
            return (Criteria) this;
        }

        public Criteria andSpuAttrIdGreaterThan(Integer value) {
            addCriterion("SPU_ATTR_ID >", value, "spuAttrId");
            return (Criteria) this;
        }

        public Criteria andSpuAttrIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SPU_ATTR_ID >=", value, "spuAttrId");
            return (Criteria) this;
        }

        public Criteria andSpuAttrIdLessThan(Integer value) {
            addCriterion("SPU_ATTR_ID <", value, "spuAttrId");
            return (Criteria) this;
        }

        public Criteria andSpuAttrIdLessThanOrEqualTo(Integer value) {
            addCriterion("SPU_ATTR_ID <=", value, "spuAttrId");
            return (Criteria) this;
        }

        public Criteria andSpuAttrIdIn(List<Integer> values) {
            addCriterion("SPU_ATTR_ID in", values, "spuAttrId");
            return (Criteria) this;
        }

        public Criteria andSpuAttrIdNotIn(List<Integer> values) {
            addCriterion("SPU_ATTR_ID not in", values, "spuAttrId");
            return (Criteria) this;
        }

        public Criteria andSpuAttrIdBetween(Integer value1, Integer value2) {
            addCriterion("SPU_ATTR_ID between", value1, value2, "spuAttrId");
            return (Criteria) this;
        }

        public Criteria andSpuAttrIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SPU_ATTR_ID not between", value1, value2, "spuAttrId");
            return (Criteria) this;
        }

        public Criteria andSpuIdIsNull() {
            addCriterion("SPU_ID is null");
            return (Criteria) this;
        }

        public Criteria andSpuIdIsNotNull() {
            addCriterion("SPU_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSpuIdEqualTo(Integer value) {
            addCriterion("SPU_ID =", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotEqualTo(Integer value) {
            addCriterion("SPU_ID <>", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThan(Integer value) {
            addCriterion("SPU_ID >", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SPU_ID >=", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThan(Integer value) {
            addCriterion("SPU_ID <", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThanOrEqualTo(Integer value) {
            addCriterion("SPU_ID <=", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdIn(List<Integer> values) {
            addCriterion("SPU_ID in", values, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotIn(List<Integer> values) {
            addCriterion("SPU_ID not in", values, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdBetween(Integer value1, Integer value2) {
            addCriterion("SPU_ID between", value1, value2, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SPU_ID not between", value1, value2, "spuId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeIdIsNull() {
            addCriterion("BASE_ATTRIBUTE_ID is null");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeIdIsNotNull() {
            addCriterion("BASE_ATTRIBUTE_ID is not null");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeIdEqualTo(Integer value) {
            addCriterion("BASE_ATTRIBUTE_ID =", value, "baseAttributeId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeIdNotEqualTo(Integer value) {
            addCriterion("BASE_ATTRIBUTE_ID <>", value, "baseAttributeId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeIdGreaterThan(Integer value) {
            addCriterion("BASE_ATTRIBUTE_ID >", value, "baseAttributeId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("BASE_ATTRIBUTE_ID >=", value, "baseAttributeId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeIdLessThan(Integer value) {
            addCriterion("BASE_ATTRIBUTE_ID <", value, "baseAttributeId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeIdLessThanOrEqualTo(Integer value) {
            addCriterion("BASE_ATTRIBUTE_ID <=", value, "baseAttributeId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeIdIn(List<Integer> values) {
            addCriterion("BASE_ATTRIBUTE_ID in", values, "baseAttributeId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeIdNotIn(List<Integer> values) {
            addCriterion("BASE_ATTRIBUTE_ID not in", values, "baseAttributeId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeIdBetween(Integer value1, Integer value2) {
            addCriterion("BASE_ATTRIBUTE_ID between", value1, value2, "baseAttributeId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("BASE_ATTRIBUTE_ID not between", value1, value2, "baseAttributeId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`STATUS` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`STATUS` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`STATUS` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`STATUS` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`STATUS` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`STATUS` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`STATUS` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`STATUS` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`STATUS` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`STATUS` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`STATUS` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`STATUS` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andIsPrimaryIsNull() {
            addCriterion("`IS_PRIMARY` is null");
            return (Criteria) this;
        }

        public Criteria andIsPrimaryIsNotNull() {
            addCriterion("`IS_PRIMARY` is not null");
            return (Criteria) this;
        }

        public Criteria andIsPrimaryEqualTo(Integer value) {
            addCriterion("`IS_PRIMARY` =", value, "isPrimary");
            return (Criteria) this;
        }

        public Criteria andIsPrimaryNotEqualTo(Integer value) {
            addCriterion("`IS_PRIMARY` <>", value, "isPrimary");
            return (Criteria) this;
        }

        public Criteria andIsPrimaryGreaterThan(Integer value) {
            addCriterion("`IS_PRIMARY` >", value, "isPrimary");
            return (Criteria) this;
        }

        public Criteria andIsPrimaryGreaterThanOrEqualTo(Integer value) {
            addCriterion("`IS_PRIMARY` >=", value, "isPrimary");
            return (Criteria) this;
        }

        public Criteria andIsPrimaryLessThan(Integer value) {
            addCriterion("`IS_PRIMARY` <", value, "isPrimary");
            return (Criteria) this;
        }

        public Criteria andIsPrimaryLessThanOrEqualTo(Integer value) {
            addCriterion("`IS_PRIMARY` <=", value, "isPrimary");
            return (Criteria) this;
        }

        public Criteria andIsPrimaryIn(List<Integer> values) {
            addCriterion("`IS_PRIMARY` in", values, "isPrimary");
            return (Criteria) this;
        }

        public Criteria andIsPrimaryNotIn(List<Integer> values) {
            addCriterion("`IS_PRIMARY` not in", values, "isPrimary");
            return (Criteria) this;
        }

        public Criteria andIsPrimaryBetween(Integer value1, Integer value2) {
            addCriterion("`IS_PRIMARY` between", value1, value2, "isPrimary");
            return (Criteria) this;
        }

        public Criteria andIsPrimaryNotBetween(Integer value1, Integer value2) {
            addCriterion("`IS_PRIMARY` not between", value1, value2, "isPrimary");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Date value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Date value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Date value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Date value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Date> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Date> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeValueIdIsNull() {
            addCriterion("BASE_ATTRIBUTE_VALUE_ID is null");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeValueIdIsNotNull() {
            addCriterion("BASE_ATTRIBUTE_VALUE_ID is not null");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeValueIdEqualTo(Integer value) {
            addCriterion("BASE_ATTRIBUTE_VALUE_ID =", value, "baseAttributeValueId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeValueIdNotEqualTo(Integer value) {
            addCriterion("BASE_ATTRIBUTE_VALUE_ID <>", value, "baseAttributeValueId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeValueIdGreaterThan(Integer value) {
            addCriterion("BASE_ATTRIBUTE_VALUE_ID >", value, "baseAttributeValueId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeValueIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("BASE_ATTRIBUTE_VALUE_ID >=", value, "baseAttributeValueId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeValueIdLessThan(Integer value) {
            addCriterion("BASE_ATTRIBUTE_VALUE_ID <", value, "baseAttributeValueId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeValueIdLessThanOrEqualTo(Integer value) {
            addCriterion("BASE_ATTRIBUTE_VALUE_ID <=", value, "baseAttributeValueId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeValueIdIn(List<Integer> values) {
            addCriterion("BASE_ATTRIBUTE_VALUE_ID in", values, "baseAttributeValueId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeValueIdNotIn(List<Integer> values) {
            addCriterion("BASE_ATTRIBUTE_VALUE_ID not in", values, "baseAttributeValueId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeValueIdBetween(Integer value1, Integer value2) {
            addCriterion("BASE_ATTRIBUTE_VALUE_ID between", value1, value2, "baseAttributeValueId");
            return (Criteria) this;
        }

        public Criteria andBaseAttributeValueIdNotBetween(Integer value1, Integer value2) {
            addCriterion("BASE_ATTRIBUTE_VALUE_ID not between", value1, value2, "baseAttributeValueId");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated do_not_delete_during_merge Thu Jun 27 19:06:50 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table V_SPU_ATTR_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}