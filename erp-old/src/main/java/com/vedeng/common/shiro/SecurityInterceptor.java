package com.vedeng.common.shiro;

import com.ctrip.framework.apollo.ConfigService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.model.ResultJSON;
import com.vedeng.common.redis.RedisKeyUtils;
import com.vedeng.common.shiro.cas.CasClientHelper;
import com.vedeng.common.shiro.constant.SecurityConstants;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.RequestUtils;
import net.sf.json.JSONArray;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import org.springframework.web.util.UrlPathHelper;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <b>Description:</b><br>
 * 权限拦截器
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.common.shiro <br>
 * <b>ClassName:</b> SecurityInterceptor <br>
 * <b>Date:</b> 2017年8月31日 下午1:02:18
 */
public class SecurityInterceptor extends HandlerInterceptorAdapter {

    private final static String EXCLUDE = ".*(\\.(js|css|jpg|png|txt|gif|jpeg|ico|bmp|json));.*(/static/|/getCategoryListByParentId|/getregion).*";
    private final static List<Pattern> EXCLUDE_PATTERNS;

    private static Logger logger= LoggerFactory.getLogger(SecurityInterceptor.class);

    @Value("${shiro.publicUrlList}")
    private String publicUrlList;

    static {
        EXCLUDE_PATTERNS = Collections.unmodifiableList(
                Arrays.stream(EXCLUDE.split(";")).map(Pattern::compile).collect(Collectors.toList())
        );
    }

    private boolean isStaticUrl(String originatingUrl) {
        return  match(EXCLUDE_PATTERNS, originatingUrl);
    }
    private static boolean match(List<Pattern> list, String s) {
        if (s == null || list == null || list.size() == 0) {
            return false;
        } else {
            for (int i = 0; i < list.size(); i++) {
                Pattern pInclude = list.get(i);
                Matcher matcher = pInclude.matcher(s);
                if (matcher.matches()) {
                    return true;
                }
            }
            return false;
        }
    }
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        if (true){
            return true;
        }
        
        UrlPathHelper urlPathHelper = new UrlPathHelper();
        String originatingUrl = urlPathHelper.getOriginatingRequestUri(request);
        //给op用 或者静态资源
        if (request.getRequestURI().contains("getgoodslistextrainfo") || isStaticUrl(originatingUrl)) {
            return true;
        }

        //spu迁移时需要，临时跳转到无访问权限页面
        if (request.getRequestURI().contains("/goods/vgoods/deleteSpu.do") || request.getRequestURI().contains("/goods/vgoods/deleteSku.do")) {
            request.getRequestDispatcher(SecurityConstants.NO_UNAUTHORIZED_PATH)
                    .forward(request, response);
            return false;
        }

        //接口被注解标记不需要鉴权
        if (allowAccess(handler)) {
            return true;
        }

        /**
        * 用户密码过于简单需要重置
        * 禁止用户使用功能
        */
        Boolean isNeedReset = (Boolean) request.getSession().getAttribute("is_need_reset");
        if (Boolean.TRUE.equals(isNeedReset)) {
            request.getRequestDispatcher("/WEB-INF/jsp/system/user/reset_password.jsp")
                    .forward(request, response);
            return false;
        }

        User user = (User) request.getSession().getAttribute(SecurityConstants.CURRENT_USER_ATTRIBUTE_NAME);

        //开启SSO时校验普通用户选择部门
        if (CasClientHelper.enableSingleSignOn() && user != null && !CasClientHelper.isAdminUser(user.getIsAdmin())) {
            boolean valid = checkUserPositionIfUnique(user, response);
            if (!valid) {
                return false;
            }
        }

        if (user != null && !SecurityConstants.SUPER_ADMIN_NO.equals(user.getIsAdmin())) {
            String requestUri = request.getRequestURI();
            if(checkUrlIsPublic(requestUri)){
                return true;
            }

            final String uriToCheck = requestUri.contains("erp") ? requestUri.substring(4) : requestUri;

            if (!checkPermission(uriToCheck, user.getUsername())) {
                logger.info("{} 没有权限访问 {}", user.getUsername(), requestUri);
                //判断是否是ajax提交方式
                if (RequestUtils.isAjaxRequest(request)) {
                    // 没有权限
                    // 向http头添加 状态 sessionstatus
                    response.setHeader("permissionstatus", "nopermission");
                    // 没有权限的状态码
                    response.setStatus(1001);
                    // 向http头添加登录的url
                    response.getWriter().write(JsonUtils.convertObjectToJsonStr(ResultJSON.failed().message("nopermission")));
                    if (Boolean.parseBoolean(ConfigService.getAppConfig().getProperty("openflush", "false"))) {
                        response.flushBuffer();
                    }

                } else {
                    String pop = request.getQueryString();
                    if (pop != null && pop.contains("pop")) {
                        request.setAttribute("noPowerContract",noPowerContract);
                        request.getRequestDispatcher("/WEB-INF/jsp/common/nopower_popup.jsp").forward(request,
                                response);
                    } else {
                        request.getRequestDispatcher("/nopower.do").forward(request, response);
                    }
                }

                return false;
            }
        }

        return true;
    }

    private boolean checkUrlIsPublic(String requestUri){
        if(StringUtils.isEmpty(publicUrlList)){
            return false;
        }
        String[] publicUrls = publicUrlList.split(",");
        for(String publicUrl : publicUrls){
            if(requestUri.equals(publicUrl)){
                return true;
            }
        }
        return false;
    }

    @Value("${noPowerContract:Aadi}")
    private String noPowerContract;

    private boolean checkPermission(String uri, String userName) {
        if (CasClientHelper.enableSingleSignOn()) {
            return SecurityUtils.getSubject().isPermitted(uri);
        } else {
            JSONArray jsonArray = JSONArray.fromObject(JedisUtils.get(RedisKeyUtils.createPermissionKey(userName)));
            @SuppressWarnings("unchecked")
            List<String> permission = (List<String>) JSONArray.toCollection(jsonArray, String.class);
            List<String> permissionList = new LinkedList<>(permission);
            String uriToUse = StringUtils.trimLeadingCharacter(uri, '/');
            uriToUse = "/" + uriToUse;
            return permissionList.contains(uriToUse);
        }
    }


    private boolean checkUserPositionIfUnique(User user, HttpServletResponse response)
            throws IOException {

        if (user.getPositions() == null || user.getPositions().isEmpty()) {
            response.sendRedirect("/notAssignPosition.do");
            return false;
        } else if (user.getPositions().size() > 1) {
            response.sendRedirect(SecurityConstants.SELECT_ORG_URL);
            return false;
        }

        return true;
    }


    /**
     * 检查接口时否存在注解如果存在不需要鉴权
     *
     * @param handler
     * @return
     */
    private boolean allowAccess(Object handler) {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            return handlerMethod.getMethodAnnotation(NoNeedAccessAuthorization.class) != null
                    || handlerMethod.getMethodAnnotation(ExcludeAuthorization.class)  != null;
        }
        return false;
    }

}
