<%--
  Created by IntelliJ IDEA.
  User: ckt
  Date: 2023/11/13
  Time: 09:49
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<html>
<head>
    <title>销售订单开票申请</title>
</head>
<style>
    .el-input.is-disabled .el-input__inner {
        color: #696B6F;
    }
    .el-textarea.is-disabled .el-textarea__inner {
        color: #696B6F;
    }
</style>
<body>
<div id="app">
    <div style="font-size: 14px;font-weight: bold;padding-bottom: 5px">申请开票</div>
    <div class="invoice_body">
        <el-radio v-model="invoiceInfoType" label="0">标准开票信息</el-radio>
        <el-radio v-model="invoiceInfoType" label="1">
            自定开票信息
            <el-tooltip class="item" effect="dark" placement="right-end" >
                <template v-slot:content>
                    <div>可修改项目：产品名称，规格型号，开票备注</div>
                    <div>1. 产品名称、规格型号仅可按合同或注册证进行修改</div>
                    <div>2. 开票备注即票面备注信息，可自行修改</div>
                </template>
                <i class="el-icon-warning-outline"></i>
            </el-tooltip>
        </el-radio>
    </div>
    <div class="invoice_table" style="position: fixed;overflow: auto;height: 80%;width: 99%;">
        <el-table
                :data="saleorder.goodsList"
                border
                empty-text="暂无信息"
                fit
                @selection-change="handleSelectionChange"
                :summary-method="getSummaries"
                show-summary
                :header-cell-style="{background: '#E3ECD9FF'}">
            <el-table-column type="selection" align="center" width="60"></el-table-column>
            <el-table-column align="center" prop="goodsName">
                <template slot="header" slot-scope="scope">
                    <span style="color: red;">*</span> 产品名称
                </template>
                <template slot-scope="scope">
                    <el-input size="small" v-model="scope.row.goodsName" placeholder="请输入产品名称" :disabled="invoiceInfoType == 0 || !scope.row.selected" maxlength="100"></el-input>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="spec" width="150">
                <template slot="header" slot-scope="scope">
                    规格型号
                </template>
                <template slot-scope="scope">
                    <el-input size="small" v-model="scope.row.spec" placeholder="请输入规格型号" :disabled="invoiceInfoType == 0 || !scope.row.selected" maxlength="40"></el-input>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="unitName" label="单位" width="60"></el-table-column>
            <el-table-column align="center" prop="price" label="单价" width="70"></el-table-column>
            <el-table-column align="center" prop="invoicedNum" label="已开票数量" width="70"></el-table-column>
            <el-table-column align="center" prop="TNNum" width="100">
                <template slot="header" slot-scope="scope">
                    <span>收货T+N未开票数量</span>
                    <el-tooltip placement="right">
                        <div slot="content">七天无理由商品：T+8<br/>其他商品：T+4</div>
                        <i class="el-icon-warning-outline"></i>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="TNDateStr" label="满足收货T+N日期" width="100"></el-table-column>
            <el-table-column align="center" prop="maxCanApplyNum" label="剩余未开票数量" width="80"></el-table-column>
            <el-table-column align="center" prop="applyNum" width="152" label="申请数量">
                <template slot-scope="scope">
                    <el-input-number
                            size="small"
                            v-model="scope.row.applyNum"
                            placeholder="请输入申请数量" :min="0" :max="scope.row.maxCanApplyNum"
                            @change="changeApplyNum(scope.row)"
                            :precision="2"
                            :disabled="!scope.row.selected"
                            :controls="true">
                    </el-input-number>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="applyAmount" label="申请金额" width="60">
                <template slot-scope="scope">
                    <div>{{ calculateAmount(scope.row) }}</div>
                </template>
            </el-table-column>
        </el-table>

<%--        <div class="invoice_sum">--%>
<%--            <span>合计: 已申请开票金额: {{total_alreadyAppliedAmount}} 未申请开票金额: {{realAmount}} 本次开票申请数量: {{totalNum.toFixed(2)}} 本次开票申请金额: {{totalApplyAmount.toFixed(2)}}</span>--%>
<%--        </div>--%>
<%--        <el-checkbox v-model="allInvoiceCheck" style="padding-top: 5px">选择全部剩余待开票商品</el-checkbox>--%>
        <el-divider></el-divider>

        <div class="comments">
            <span style="padding-right: 10px">票面备注</span> <el-button type="text" :disabled="invoiceInfoType == 0" @click="editComments = true">编辑</el-button>
            <el-input v-model="comments"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入开票备注"
                      style="padding-top: 5px"
                      :disabled="true"
                      maxlength="200" show-word-limit>
            </el-input>
        </div>
        <br>
        <div class="invoiceMessage">
            <span style="padding-right: 10px">开票留言</span>
            <el-input v-model="invoiceMessage"
                      type="textarea"
                      :rows="3"
                      placeholder="告知财务开票的注意事项"
                      style="padding-top: 5px"
                      maxlength="200" show-word-limit>
            </el-input>
        </div>
    </div>

    <div style="position: fixed;bottom: 100px;right: 30px">
        <el-button type="success" plain @click="submit()" :disabled="submitDisabled">申请</el-button>
        <el-button type="success" plain @click="preview()">预览</el-button>
        <el-button type="primary" plain @click="cancel()">取消</el-button>
    </div>


    <%--提前开票申请--%>
    <el-dialog
            title="提前申请"
            class="advance_class"
            :visible.sync="advancedVisible"
            :before-close="advancedVisibleClose"
            :show-close="false"
            :fullscreen="true">
        <div style="height:20px;background-color: #E3ECD9FF">申请检查未通过，如需继续申请，请填写以下未通过项目说明</div>
        <div v-for="detailDto in invoiceCheckResultDetailDtoList" style="padding-top: 20px">
            <span style="color: red">*</span>{{detailDto.ruleName}} <span class="tipClass">{{detailDto.ruleContent}}</span>
            </br>{{detailDto.promptText}}
            <el-input v-model="detailDto.applyReason"
                      type="textarea"
                      :rows="2"
                      style="padding-top: 5px"
                      maxlength="200" show-word-limit>
            </el-input>
        </div>
        <div style="padding-top: 20px">
            <span style="color: red">*</span>提前开票原因
            <el-input v-model="advanceValidReason"
                      type="textarea"
                      :rows="2"
                      style="padding-top: 5px"
                      maxlength="200" show-word-limit>
            </el-input>
        </div>
  <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="submitAdvanceApply()" :disabled="applyDisabled">申请</el-button>
    <el-button @click="cancelAdvanceApply">取消</el-button>
  </span>
    </el-dialog>

    <%--编辑票面备注dialog--%>
    <el-dialog
            title="编辑票面备注"
            :visible.sync="editComments"
            width="45%"
            :before-close="beforeClose">
        <p style="background-color: rgb(233, 233, 235);margin-top: -15px;line-height: 2.4">
            请注意，<span style="color: #2c99ae;">票面备注</span>将在票据上直接显示，用于提供有关票据的额外信息或特殊要求。
            请确保您填写的备注已与客户进行确认。如果只是告知财务开票的注意事项请填写在<span style="color: #2c99ae;">开票留言</span>中，不要填写在<span style="color: #2c99ae;">票面备注</span>中。
        </p>
        <span style="padding-right: 20px">{{oldComments}}</span> <el-checkbox v-model="fixedComment">固定备注</el-checkbox></br>
        <div style="padding-top: 10px;padding-bottom: 8px"><span style="color: red">*</span>票面备注</div>
        <el-input v-model="appendComment"
                  type="textarea"
                  :rows="3"
                  placeholder="请填写票面备注"
                  style="padding-top: 5px"
                  :disabled="false"
                  maxlength="150" show-word-limit>
        </el-input>
        <span slot="footer" class="dialog-footer">
                <el-button size="medium" type="success" plain @click="submitComment">确 定</el-button>
                <el-button size="medium" type="primary" plain @click="cancelComment">取 消</el-button>
            </span>
    </el-dialog>

    <el-dialog
            title="预览页面"
            :show-close="false"
            :fullscreen="true"
            :visible.sync="previewVisible"
            width="50%">
        <!-- 这里显示接口返回的内容 -->
        <div v-html="dialogContent"></div>
        <el-button
                size="medium"
                type="primary"
                plain
                @click="cancelDialogContent"
                style="position: fixed; bottom: 20px; right: 20px;"
        >
            退 出
        </el-button>
    </el-dialog>
</div>



</body>
<script type="text/javascript">
    let invoiceApply = ${invoiceApplyJson};
    let order = ${saleOrderJson};
    let invoiceTypeStr = '${invoiceTypeStr}';
    let realAmount = ${realAmount};
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                invoiceType: invoiceTypeStr,
                invoiceMethod: 0,
                invoiceInfoType: '0',
                saleorderId: 0,
                saleorder: {
                    goodsList: [{
                        goodsName: '',
                        oldGoodsName: '',
                        spec: '',
                        oldSpec: '',
                        unitName: '',
                        price: null,
                        canInvoicedNum: 0,
                        maxCanApplyNum: 0,
                        applyNum: 0,
                        applyAmount: 0
                    }],

                },
                allInvoiceCheck: false,
                advanceValidReason: '',
                comments: '',
                oldComments: '',
                total_alreadyAppliedAmount: 0.00,
                totalNum: 0,
                totalApplyAmount: 0,
                isAdvance: null,
                invoiceApply: {},
                realAmount: 0,
                submitDisabled: false,
                editComments: false,
                fixedComment: true,
                appendComment: '',
                invoiceMessage: '',
                appliedNumTotal: 0,
                invoicedNumTotal: 0,
                TNNumTotal: 0,
                maxCanApplyNumTotal: 0,
                applyTotal: 0.00,
                advancedVisible: false,
                applyDisabled: false,
                /*财务资质*/
                financeCertification: {
                    pass: true, //财务资质检查是否通过
                    specialInvoice: false, //是否专票

                },
                /*申请校验不通过list*/
                invoiceCheckResultDetailDtoList: {},
                //预览显隐控制
                previewVisible: false,
                dialogContent: ''
            }
        },
        methods: {
            cancelDialogContent(){
                this.previewVisible = false;
            },
            /*预览*/
            preview(){
                let filteredArr = this.saleorder.goodsList.filter(obj => obj.selected&&obj.applyNum>0);
                if (filteredArr.length == 0) {
                    this.$message.error("请至少勾选一条数据，并填写数量");
                    return false;
                }

                this.previewVisible = true;
                let previewInvoiceVo = {
                    comments: this.comments,
                    saleorderId: this.saleorderId,
                    goodsList: filteredArr,
                };


                $.ajax({
                    async: false,
                    url: page_url + "/finance/invoice/previewInvoice.do",
                    data: JSON.stringify(previewInvoiceVo),  // 将对象转为JSON字符串
                    type: "POST",
                    dataType: "html",
                    contentType: "application/json",  // 指定发送的数据是JSON格式
                    success: (data) => {
                        this.dialogContent = data;

                    },
                    error: function (data) {
                        if (data.status == 1001) {
                            layer.alert("当前操作无权限");
                        }
                    }
                });
            },
            /*提前申请确认*/
            submitAdvanceApply(){
                this.applyDisabled = true;
                var saveValid = true;
                this.invoiceCheckResultDetailDtoList.some(d => {
                    if (!d.applyReason){
                        this.$message.error('请填写'+d.ruleName+'申请内容')
                        saveValid = false;
                        this.applyDisabled = false;
                        return true;
                    }
                })
                if (saveValid && !this.advanceValidReason){
                    this.$message.error('请填写提前开票原因')
                    this.applyDisabled = false;
                    saveValid = false;
                }
                if (saveValid){
                    this.saveInvoice();
                    setTimeout(() => {
                        this.applyDisabled = false;
                    }, 1500);
                }
            },
            /*提前申请取消*/
            cancelAdvanceApply(){
                this.submitDisabled = false;
                this.advancedVisible = false;
            },
            advancedVisibleClose(){

            },
            calTotalAmount(){
                let total  = 0;
                this.saleorder.goodsList.forEach(g => {
                    if (g.selected){
                        let product = g.applyNum * g.price; // 计算乘积
                        let productFixed = Number(product.toFixed(2)); // 格式化乘积并转换为数字
                        // 累加到总和中
                        total += productFixed;

                    }
                });
                // 如果你需要，可以将最终的和四舍五入到两位小数
                this.applyTotal = total.toFixed(2);
            },
            getSummaries(param){
              const sums = [];
              param.columns.forEach((column,index) => {
                  if(index === 0){
                      sums[index] = '合计';
                      return;
                  }
                  sums[5] = this.invoicedNumTotal;
                  sums[6] = this.TNNumTotal;
                  sums[8] = this.maxCanApplyNumTotal;
                  sums[9] = this.totalNum;
                  sums[10] = this.applyTotal;
              })
                return sums;
            },
            cancelComment(){
                this.editComments = false
            },
            submitComment(){
                if (this.appendComment.trim() == ''){
                    this.$message.error("请填写票面备注");
                    return;
                }
                this.comments =  this.fixedComment ? (this.oldComments + '\n' + this.appendComment) : this.appendComment
                this.editComments = false
            },
            beforeClose(done){
                this.fixedComment = true
                done();
            },
            handleSelectionChange(selectedRows) {
                this.saleorder.goodsList.forEach(row => {
                    row.selected = selectedRows.includes(row);
                });
                this.calTotalAmount()
            },
            calculateAmount(row) {
                if (!row.applyNum || row.applyNum == 0 || !row.selected){
                    return 0;
                }
                return (row.applyNum * row.price).toFixed(2);
            },
            changeApplyNum(row) {
                this.totalNum = 0;
                this.totalApplyAmount = 0;
                this.saleorder.goodsList.forEach(g => {
                    g.applyAmount = g.price * g.applyNum;
                    this.totalNum += g.applyNum;
                })
                this.calTotalAmount();
                if (row.applyNum > row.TNNum){
                    this.$notify({
                        title: '警告',
                        dangerouslyUseHTMLString: true,
                        message: '申请数量大于允许开票的<span style="color: green;">收货T+N未开票数量，</span>请与财务沟通确认后再申请',
                        type: 'warning',
                        duration: 2000
                    });
                }
            },
            getSpec(spuType,model,spec){
                if (spuType == 316 || spuType == 1008) {
                    return model;
                } else if (spuType == 317 || spuType == 318) {
                    return spec;
                } else {
                    if (model != null && model != '') {
                        return model;
                    } else {
                        return spec;
                    }
                }
            },
            cancel() {
                layer.closeAll();
                parent.layer.closeAll();
            },
            async submit() {
                this.submitDisabled = true;
                //校验
                var goodsCheck = 0;
                var selectedNum = 0; //勾选条数
                this.saleorder.goodsList.some(g => {
                    if (g.selected){
                        selectedNum += 1;
                    }
                    if (g.selected && g.applyNum == 0){
                        this.$message.error('存在选则数据数量为0，请检查');
                        goodsCheck = 1;
                        return true;
                    }
                    if (!g.goodsName || g.goodsName.trim() == ''){
                        this.$message.error('产品名称不能为空');
                        goodsCheck = 1;
                        return true;
                    }
                })
                if (selectedNum == 0){
                    this.submitDisabled = false;
                    this.$message.error('请至少选择一行数据')
                    return;
                }
                if (goodsCheck == 1){
                    this.submitDisabled = false;
                    return;
                }
                var continueSubmit = false;
                //是否编辑了票面备注
                if(this.oldComments != this.comments) {
                    await this.$confirm('你修改了票面备注，请注意' +
                        '</br></br>' +
                        '1  <span style="color: green">票面备注</span>将在票据上直接显示，用于提供有关票据的额外信息或特殊要求。请确保您填写' +
                        '的备注已与客户进行确认。如果只是告知财务开票的注意事项请填写在<span style="color: green">开票留言</span>中，不要填写在<span style="color: green">票面备注</span>中。' +
                        '</br></br>' +
                        '2  固定备注内容对财务十分重要，除非客户坚决不同意在备注栏中显示我公司单号信息，否则请不要取消勾选。' +
                        '</br></br>' +
                        '请确认是否继续提交？', '注意', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'info',
                        dangerouslyUseHTMLString: true
                    }).then(() => {
                        continueSubmit = true
                    }).catch(() => {

                    })
                }
                if (this.oldComments != this.comments && !continueSubmit){
                    this.submitDisabled = false;
                    return
                }
                //校验并发策略
                var enableOpenInvoice = true;
                var errorMsg = '';
                $.ajax({
                    async:false,
                    url:page_url + "/invoice/api/enableOpenInvoice.do",
                    data:{
                        "orderId": this.saleorderId,
                        "orderType": 1,
                    },
                    type:"POST",
                    dataType : "json",
                    success:(data) =>{
                        if (!data.data.pass){
                            enableOpenInvoice = false;
                            errorMsg = data.data.errorMsg;
                        }
                    },error:function(data){
                        if(data.status ==1001){
                            layer.alert("当前操作无权限")
                        }
                    }
                })
                if (!enableOpenInvoice){
                    this.$message.error(errorMsg);
                    this.submitDisabled = false;
                    return;
                }
                //是否提前申请
                var advancedApply = true;
                //组装参数
                var detailList =  [];
                this.saleorder.goodsList.forEach(g => {
                    if (g.selected){
                        let detail = {};
                        detail.detailGoodsId = g.saleorderGoodsId;
                        detail.num = g.applyNum;
                        detail.totalAmount = (g.applyNum*g.price).toFixed(2);
                        detail.price = g.price;
                        detail.productName = g.goodsName;
                        detail.specModel = g.spec;
                        detail.unit = g.unitName;
                        if (detail.num > 0){
                            detailList.push(detail);
                        }
                    }
                })
                $.ajax({
                    async:false,
                    url:page_url + "/invoice/api/advanceApply.do",
                    data:JSON.stringify({
                        "relatedId": this.saleorderId,
                        "type": 505,
                        "invoiceInfoType": this.invoiceInfoType,
                        "invoiceMessage": this.invoiceMessage,
                        "detailList": detailList
                    }),
                    type:"POST",
                    dataType : "json",
                    contentType:'application/json',
                    success:(data) =>{
                        if (data.data.success){
                            advancedApply = false;
                        }else {
                            this.invoiceCheckResultDetailDtoList = data.data.invoiceCheckResultDetailDtoList
                        }
                    },error:function(data){
                        if(data.status ==1001){
                            layer.alert("当前操作无权限")
                        }
                    }
                })
                if (advancedApply){
                    this.advancedVisible = true;
                }else {
                    this.saveInvoice();
                }
            },
            saveInvoice(){
                //组装参数
                var invoiceApplyDetails =  [];
                this.saleorder.goodsList.forEach(g => {
                    if (g.selected){
                        let detail = {};
                        detail.detailgoodsId = g.saleorderGoodsId;
                        detail.num = g.applyNum;
                        detail.totalAmount = (g.applyNum*g.price).toFixed(2);
                        detail.price = g.price;
                        detail.productName = g.goodsName;
                        detail.specModel = g.spec;
                        detail.unit = g.unitName;
                        if (detail.num > 0){
                            invoiceApplyDetails.push(detail);
                        }
                    }
                });
                let invoiceCheckResultDetailDtoStr;
                if (this.invoiceCheckResultDetailDtoList && this.invoiceCheckResultDetailDtoList.length > 0) {
                    invoiceCheckResultDetailDtoStr = JSON.stringify(this.invoiceCheckResultDetailDtoList);
                } else {
                    invoiceCheckResultDetailDtoStr = "";
                }
                //保存发票
                $.ajax({
                    async:false,
                    url:page_url + "/finance/invoice/saveOpenInvoceApply.do",
                    data:{
                        "relatedId": this.saleorderId,
                        "isAuto": this.invoiceMethod,
                        "comments": this.comments,
                        "invoiceMessage": this.invoiceMessage,
                        "advanceValidReason": this.advanceValidReason,
                        "invoiceType" : this.saleorder.invoiceType,
                        "invoiceInfoType" : this.invoiceInfoType,
                        "isAdvance": this.invoiceApply.isAdvance,
                        "detailString":JSON.stringify(invoiceApplyDetails),
                        "invoiceCheckResultDetailDtoStr": invoiceCheckResultDetailDtoStr //提前申请原因
                    },
                    type:"POST",
                    dataType : "json",
                    success:(data) =>{
                        if(data.code==0){
                            this.$message.success("提交成功");
                            // 设置延时关闭弹窗和刷新页面
                            setTimeout(() => {
                                this.submitDisabled = false;
                                parent.layer.closeAll();
                                parent.window.location.reload();
                            }, 1500);
                        }else{
                            this.submitDisabled = false;
                            layer.alert(data.message,{icon:2});
                            warn2Tips("errorTitle",data.message);//文本框ID和提示用语
                        }
                    },error:function(data){
                        if(data.status ==1001){
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                })
            }
        },
        watch: {
            fixedComment(checked){
                if (checked){
                    // this.appendComment = ''
                }
                if (!checked){
                    this.$confirm('固定备注内容对财务十分重要，除非客户坚决不同意在备注栏中显示我公司单号信息，否则请不要取消勾选。', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.fixedComment = false
                    }).catch(() => {
                        this.fixedComment = true
                    })
                }
            },
            invoiceInfoType(newVal,oldVal){
                if (newVal == 0){
                    this.saleorder.goodsList.forEach(g => {
                        g.goodsName = g.oldGoodsName;
                        g.spec = g.oldSpec;
                    });
                    this.comments=this.oldComments;
                    this.fixedComment = true
                    this.appendComment = ''
                }
            },
            allInvoiceCheck(newVal,oldVal){
                if (newVal) {
                    this.totalNum = 0;
                    this.totalApplyAmount = 0;
                    this.saleorder.goodsList.forEach(g => {
                        g.applyNum = g.maxCanApplyNum
                        this.totalNum += g.applyNum;
                        this.totalApplyAmount += Number(g.applyNum * g.price);
                    })
                }
            },
            applyNum(newVal,oldVal){
                this.saleorder.goodsList.forEach(g => {
                    this.totalNum += g.applyNum;
                    this.totalApplyAmount += g.applyAmount
                })
            }
        },
        created() {
            var oldGoodsList = order.goodsList;
            this.saleorderId = order.saleorderId;
            this.saleorder.meetInvoiceConditions = order.meetInvoiceConditions;
            this.invoiceMethod = order.invoiceMethod;
            this.saleorder.goodsList = [];
            this.invoiceApply = invoiceApply;
            this.comments = order.saleorderNo;
            this.oldComments = order.saleorderNo;
            debugger
            oldGoodsList.forEach(g => {
                g.oldGoodsName = g.goodsName;
                g.oldSpec = this.getSpec(g.spuType,g.model,g.spec);
                g.spec = this.getSpec(g.spuType,g.model,g.spec);
                g.appliedNum = 0;
                g.applyNum = g.TNNum;
                g.applyAmount = 0;
                g.maxCanApplyNum = g.num - g.afterReturnNum  - g.appliedNum - g.invoicedNum
                if (!g.canInvoicedNum){
                    g.canInvoicedNum = 0;
                }
                if (g.maxCanApplyNum > 0 && g.realTotalAmount > 0){
                    this.totalNum += g.applyNum;
                    this.total_alreadyAppliedAmount += g.appliedAmount + g.invoicedAmount;
                    this.realAmount = realAmount - this.total_alreadyAppliedAmount;
                    this.invoicedNumTotal += g.invoicedNum;
                    this.TNNumTotal += g.TNNum;
                    if (g.realTotalAmount > 0){
                        this.maxCanApplyNumTotal += g.maxCanApplyNum;
                    }
                    this.saleorder.goodsList.push(g)
                }
            });
        },
    });
</script>
<style>
    #app {
        font-size: 14px;
        font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
        color: #606266;
    }
    .invoice_header {
        border: 1px solid #EBEEF5;
        height: 60px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .invoice_body,.invoice_table,.is_advance,.comments{
        margin-top: 20px;
    }
    .invoice_sum {
        padding-top: 5px;
    }
    .tipClass {
        display: inline-block;
        padding-left: 20px;
        color: #aaacaf;
    }
    .advance_class .el-dialog__header .el-dialog__title {
        font-size: 14px;
        font-weight: bold;
    }

</style>
</html>
