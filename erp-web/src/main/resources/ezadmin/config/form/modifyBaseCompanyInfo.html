<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>集团ERP主体信息编辑</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="modifyBaseCompanyInfo" datasource="erp-datasourcetarget" success_url="reload">
  <div id="APPEND_HEAD" class="append">
      <style>
          .layui-quote-nm{display: none;}
          small {
              display: none;
          }
      </style>
  </div>
  <div class="layui-container">
   <form id="inputForm" method="post" class="layui-form">
       <input type="hidden" name="ID" value="${ID}">

       <div class="layui-form-item ">
           <label class="layui-form-label">公司名称</label>
           <div class="layui-input-inline form-group">
               <input lay_verify="required" datatype="varchar" jdbctype="VARCHAR"
                      type="text" class="layui-input form-item"
                      value="" name="COMPANY_NAME" item_desc="公司名称" maxlength="255">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">公司简称</label>
           <div class="layui-input-inline form-group">
               <input datatype="varchar" jdbctype="VARCHAR"
                      type="text" class="layui-input form-item"
                      value="" name="COMPANY_SHORT_NAME" item_desc="公司简称" maxlength="100">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">前端标识序号</label>
           <div class="layui-input-inline form-group">
               <input datatype="int" jdbctype="INTEGER"
                      type="number" class="layui-input form-item"
                      value="" name="FRONT_END_SEQ" item_desc="前端标识序号">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">金蝶账套编码</label>
           <div class="layui-input-inline form-group">
               <input lay_verify="required" datatype="varchar" jdbctype="VARCHAR"
                      type="text" class="layui-input form-item"
                      value="" name="KINGDEE_ACCOUNT_CODE" item_desc="金蝶账套编码" maxlength="50">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">统一社会信用代码</label>
           <div class="layui-input-inline form-group">
               <input datatype="varchar" jdbctype="VARCHAR"
                      type="text" class="layui-input form-item"
                      value="" name="BUSINESS_LICENSE" item_desc="统一社会信用代码" maxlength="50">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">注册地址</label>
           <div class="layui-input-inline form-group">
               <input datatype="varchar" jdbctype="VARCHAR"
                      type="text" class="layui-input form-item"
                      value="" name="COMPANY_ADDRESS" item_desc="注册地址" maxlength="255">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">注册电话</label>
           <div class="layui-input-inline form-group">
               <input datatype="varchar" jdbctype="VARCHAR"
                      type="text" class="layui-input form-item"
                      value="" name="CONTACT_PHONE" item_desc="注册电话" maxlength="20">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">开户银行</label>
           <div class="layui-input-inline form-group">
               <input datatype="varchar" jdbctype="VARCHAR"
                      type="text" class="layui-input form-item"
                      value="" name="BANK_NAME" item_desc="开户银行" maxlength="100">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">银行账号</label>
           <div class="layui-input-inline form-group">
               <input datatype="varchar" jdbctype="VARCHAR"
                      type="text" class="layui-input form-item"
                      value="" name="BANK_ACCOUNT" item_desc="银行账号" maxlength="50">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">合同签约地址</label>
           <div class="layui-input-inline form-group">
               <input datatype="varchar" jdbctype="VARCHAR"
                      type="text" class="layui-input form-item"
                      value="" name="CONTRACT_ADDRESS" item_desc="合同签约地址" maxlength="255">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">客户TRADER_ID</label>
           <div class="layui-input-inline form-group">
               <input datatype="int" jdbctype="INTEGER"
                      type="number" class="layui-input form-item"
                      value="" name="CUSTOMER_TRADER_ID" item_desc="客户TRADER_ID">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">供应商TRADER_ID</label>
           <div class="layui-input-inline form-group">
               <input datatype="int" jdbctype="INTEGER"
                      type="number" class="layui-input form-item"
                      value="" name="SUPPLIER_TRADER_ID" item_desc="供应商TRADER_ID">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">是否删除</label>
           <div class="layui-input-inline form-group">
               <select class="form-item" name="IS_DELETED" type=""
                       value="" item_name="IS_DELETED" placeholder=""
                       style="" alias="" jdbctype="TINYINT"
                       data='[{"V":"否","K":"0"},{"V":"是","K":"1"}]'
                       datatype="JSON" lay_verify="required" item_desc="是否删除"></select>
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">ERP的地址</label>
           <div class="layui-input-inline form-group">
               <input datatype="varchar" jdbctype="VARCHAR"
                      type="text" class="layui-input form-item"
                      value="" name="ERP_DOMAIN" item_desc="ERP的地址" maxlength="100">
           </div>
       </div>
       <div class="layui-form-item ">
           <label class="layui-form-label">主体详细信息字段</label>
           <div class="layui-input-inline form-group">
               <textarea datatype="varchar" jdbctype="VARCHAR"
                         class="layui-input form-item"
                         name="DETAIL_JSON" item_desc="主体详细信息字段" maxlength="5000"></textarea>
           </div>
       </div>
   </form>
   <form id="configForm" class="layui-form">
    <div class="layui-card" >
     <div class="layui-card-header">
       表单数据配置
     </div>
     <div class="layui-card-body">
     </div>
    </div>
    <div class="layui-form-item "><label class="layui-form-label">初始表达式</label>
     <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">list=select("
      SELECT
                ID,
                COMPANY_NAME,
                COMPANY_SHORT_NAME,
                FRONT_END_SEQ,
                KINGDEE_ACCOUNT_CODE,
                BUSINESS_LICENSE,
                COMPANY_ADDRESS,
                CONTACT_PHONE,
                BANK_NAME,
                BANK_ACCOUNT,
                CONTRACT_ADDRESS,
                CUSTOMER_TRADER_ID,
                SUPPLIER_TRADER_ID,
                CREATE_TIME,
                UPDATE_TIME,
                CREATE_USER,
                UPDATE_USER,
                IS_DELETED,
                ERP_DOMAIN,
                DETAIL_JSON
            FROM
                T_BASE_COMPANY_INFO
WHERE
	ID = ${ID}");
return list.get(0);</pre>
     </div>
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">提交表达式</label>
     <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">if(null==ID||"0".equals(ID)){
          sessionUserId=$$("EZ_SESSION_USER_ID_KEY");
          insert("INSERT INTO T_BASE_COMPANY_INFO
            (COMPANY_NAME, COMPANY_SHORT_NAME, FRONT_END_SEQ, KINGDEE_ACCOUNT_CODE, BUSINESS_LICENSE, COMPANY_ADDRESS, CONTACT_PHONE, BANK_NAME, BANK_ACCOUNT, CONTRACT_ADDRESS, CUSTOMER_TRADER_ID, SUPPLIER_TRADER_ID, CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, IS_DELETED, ERP_DOMAIN, DETAIL_JSON) VALUES
          (#{COMPANY_NAME}, #{COMPANY_SHORT_NAME}, #{FRONT_END_SEQ}, #{KINGDEE_ACCOUNT_CODE}, #{BUSINESS_LICENSE}, #{COMPANY_ADDRESS}, #{CONTACT_PHONE}, #{BANK_NAME}, #{BANK_ACCOUNT}, #{CONTRACT_ADDRESS}, #{CUSTOMER_TRADER_ID}, #{SUPPLIER_TRADER_ID}, now(), now(), #{EZ_SESSION_USER_ID_KEY}, #{EZ_SESSION_USER_ID_KEY}, #{IS_DELETED}, #{ERP_DOMAIN}, #{DETAIL_JSON})");
     return 0;
}else{
    update("UPDATE T_BASE_COMPANY_INFO
          SET
          COMPANY_NAME=#{COMPANY_NAME},
          COMPANY_SHORT_NAME=#{COMPANY_SHORT_NAME},
          FRONT_END_SEQ=#{FRONT_END_SEQ},
          KINGDEE_ACCOUNT_CODE=#{KINGDEE_ACCOUNT_CODE},
          BUSINESS_LICENSE=#{BUSINESS_LICENSE},
          COMPANY_ADDRESS=#{COMPANY_ADDRESS},
          CONTACT_PHONE=#{CONTACT_PHONE},
          BANK_NAME=#{BANK_NAME},
          BANK_ACCOUNT=#{BANK_ACCOUNT},
          CONTRACT_ADDRESS=#{CONTRACT_ADDRESS},
          CUSTOMER_TRADER_ID=#{CUSTOMER_TRADER_ID},
          SUPPLIER_TRADER_ID=#{SUPPLIER_TRADER_ID},
          UPDATE_TIME=now(),
          UPDATE_USER=#{EZ_SESSION_USER_ID_KEY},
          IS_DELETED=#{IS_DELETED},
          ERP_DOMAIN=#{ERP_DOMAIN},
          DETAIL_JSON=#{DETAIL_JSON}
          WHERE ID = #{ID}");
return ID;
}</pre>
     </div>
    </div>
    <div class="layui-form-item "> <label class="layui-form-label">删除表达式</label>
     <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type="">
          update("UPDATE T_BASE_COMPANY_INFO SET IS_DELETED = 1, UPDATE_TIME = NOW(), UPDATE_USER = #{EZ_SESSION_USER_ID_KEY} WHERE ID = #{ID}");
          return ID;
      </pre>
     </div>
    </div>
   </form>
  </div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
  <div id="APPEND_FOOT" class="append">
   <script>
    </script>
  </div>
 </body>
</html> 