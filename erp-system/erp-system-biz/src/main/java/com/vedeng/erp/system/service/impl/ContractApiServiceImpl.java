package com.vedeng.erp.system.service.impl;

import com.vedeng.erp.system.service.ContractApiService;
import com.vedeng.erp.system.service.ContractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 合同服务API实现类
 * 委托给 ContractService 接口的实现
 *
 * <AUTHOR>
 * @date 2023-07-01
 */
@Slf4j
@Service
public class ContractApiServiceImpl implements ContractApiService {

    @Autowired
    private ContractService contractService;

    @Override
    public boolean signContract(Long orderId, Integer type) {
        return contractService.signContract(orderId, type);
    }


}
