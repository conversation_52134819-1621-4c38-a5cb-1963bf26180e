<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="分类迁移记录列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/new/js/pages/goodsdribute/category_migration_list.js?rnd=${resourceVersionKey}'></script>

	<div class="main-container">
		<div class="list-pages-search">
			<form method="post" id="search" action="<%=basePath%>category/base/categoryMigretionIndex.do">
				<ul>
					<li>
						<label class="infor_name">分类名称</label>
						<input type="text" class="input-middle" name="categoryName" id="" value="${categoryMigrationVo.categoryName}" />
					</li>
					<li>
						<label class="infor_name">分类ID</label>
						<input type="text" class="input-middle" name="categoryId" id="" value="${categoryMigrationVo.categoryId}" />
					</li>
					<li>
						<label class="infor_name">操作人</label>
						<select class="input-middle" name="userId" id="">
							<option value="">全部</option>
							<%--<option <c:if test="${invoiceApply.isCollect eq 0}">selected</c:if> value="0">否</option>
							<option <c:if test="${invoiceApply.isCollect eq 1}">selected</c:if> value="1">是</option>--%>
						</select>
					</li>
					<li>
						<label class="infor_name">审核状态</label>
						<select class="input-middle" name="categoryStatus" id="">
							<option value="">全部</option>
							<option <c:if test="${categoryMigrationVo.categoryStatus eq 0}">selected</c:if> value="0">待审核</option>
							<option <c:if test="${categoryMigrationVo.categoryStatus eq 1}">selected</c:if> value="1">审核通过</option>
							<option <c:if test="${categoryMigrationVo.categoryStatus eq 2}">selected</c:if> value="2">审核不通过</option>
						</select>
					</li>

				</ul>
				<div class="tcenter">
                    <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="categoryMigrationpass();" id="">审核通过</span>
                    <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="resetPage();">驳回</span>
					<span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="">查询</span>
					<span class="bt-small bg-light-blue bt-bg-style mr20" onclick="resetPage();">重置</span>
		</div>
			</form>
		</div>
		<div class="list-page">
			<div class="fixdiv">
				<div class=''>
					<table class="table table-bordered table-centered" id="invoice_apply_list_tab">
						<thead>
							<tr>
								<th>
									<span style="vertical-align:middle;">全选&nbsp;</span>
                                    <input type="checkbox" name="checkAllOpt" style="vertical-align:middle;" onchange="checkAllOpt(this);">
								</th>
								<th>分类ID</th>
								<th>分类名称</th>
								<th>原始路径</th>
								<th>目标路劲</th>
								<th>原始分类ID</th>
								<th>目标父级ID</th>
								<th>迁移原因</th>
								<th>审核状态</th>
								<th>操作时间</th>
							</tr>
						</thead>
						<tbody>
							<c:forEach var="categoryMigration" items="${categoryMigrationList}" varStatus="num">
								<tr>
                                    <td><input type="checkbox" name="checkName" value="${list.invoiceApplyId}" isSign="${list.isSign}" isAuto="${list.isAuto}" onchange="checkedOnly(this)" amount="${list.totalAmount}"/></td>
									<td>
										${categoryMigration.categoryId}
									</td>
									<td>
											${categoryMigration.categoryName}
									</td>
									<td>
											${categoryMigration.originPath}
									</td>
									<td>
											${categoryMigration.targetPath}
									</td>
									<td>
											${categoryMigration.originParentId}
									</td>
									<td>
											${categoryMigration.targetParentId}
									</td>
									<td>
											${categoryMigration.migrationReason}
									</td>
									<td>
											${categoryMigration.categoryId}
									</td>


									<td><date:date value="${categoryMigration.updateTime}" /></td>


								</tr>
							</c:forEach>
							<c:if test="${empty categoryMigrationList}">
								<tr>
									<td colspan="22">
										<!-- 查询无结果弹出 --> 查询无结果！请尝试使用其他搜索条件。
									</td>
								</tr>
							</c:if>
						</tbody>
					</table>
				</div>
			</div>
			<div>

				<tags:page page="${page}" />

			</div>
		</div>
	</div>

<%@ include file="../../common/footer.jsp"%>
