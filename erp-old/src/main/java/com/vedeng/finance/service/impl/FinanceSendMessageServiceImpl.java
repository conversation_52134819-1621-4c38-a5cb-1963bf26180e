package com.vedeng.finance.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.system.service.UserWorkApiService;
import com.vedeng.finance.model.vo.SettlementMessageVo;
import com.vedeng.finance.service.FinanceSendMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Service
@Slf4j
public class FinanceSendMessageServiceImpl implements FinanceSendMessageService {

    @Resource
    private UserWorkApiService userWorkApiService;

    public static final String SETTLEMENT_TIPS = "<font color=\"warning\">你的订单有新的结款信息</font>\n销售单号：{}\n客户名称：{}\n订单实际金额：{}\n本次结款金额：{}\n剩余结款金额：{}";

    @Override
    @Async
    public void sendMessage(SettlementMessageVo settlementMessageVo) {
        try {
            if (StringUtils.isEmpty(settlementMessageVo.getUserId())){
                log.info("发送微信消息失败，userId为空");
                return;
            }
            String message = StrUtil.format(SETTLEMENT_TIPS,
                    settlementMessageVo.getSaleOrderNo(),
                    settlementMessageVo.getTraderName(),
                    settlementMessageVo.getActualAmount(),
                    settlementMessageVo.getCurrentAmount(),
                    settlementMessageVo.getRemainAmount()
            );
            log.info("发送微信消息：{}", JSONObject.toJSON(settlementMessageVo));
            userWorkApiService.sendMsg(settlementMessageVo.getUserId(),message);
        }catch (Exception e){
            log.info("发送微信消息失败",e);
        }
       
    }
}
