package com.vedeng.common.page;

import net.sf.jsqlparser.expression.Alias;

import org.apache.commons.jxpath.JXPathContext;
import org.apache.commons.jxpath.JXPathNotFoundException;

import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.MappedStatement.Builder;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.scripting.defaults.DefaultParameterHandler;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;


@Intercepts({@Signature(type = Executor.class,method = "query",args =  {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})})
public class PageInterceptor implements Interceptor {
	
	private static String dialect = "";	//数据库方言
	private static String pageSqlId = ""; //mapper.xml中需要拦截的ID

    private static final Alias TABLE_ALIAS;

    static {
        TABLE_ALIAS = new Alias("table_count");
        TABLE_ALIAS.setUseAs(false);
    }
	
    @Override
	public Object intercept(Invocation invocation) throws Throwable {
    	//当前环境 MappedStatement，BoundSql，及sql取得  
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs()[1];
        BoundSql boundSql = mappedStatement.getBoundSql(parameter);
        String originalSql = boundSql.getSql().trim();
        Object parameterObject = boundSql.getParameterObject();

        //Page对象获取，“信使”到达拦截器！  
        Page page = null;

        if (null != searchPageWithXpath(boundSql.getParameterObject(), ".", "page", "*/page")) {
            page = searchPageWithXpath(boundSql.getParameterObject(), ".", "page", "*/page");
        }

        if (page != null && (mappedStatement.getId().toLowerCase()).endsWith(pageSqlId)) {
        	if(page.getTotalRecord()==null || page.getTotalRecord()==0){//程序中给出查询记录总数，此处不再计算
        		//Page对象存在的场合，开始分页处理  
        		String countSql = getSimpleCountSql(originalSql);
        		Connection connection = mappedStatement.getConfiguration().getEnvironment().getDataSource().getConnection();
        		PreparedStatement countStmt = connection.prepareStatement(countSql);
        		BoundSql countBS = copyFromBoundSql(mappedStatement, boundSql,countSql);
        		DefaultParameterHandler parameterHandler = new DefaultParameterHandler(mappedStatement,parameterObject, countBS);
        		parameterHandler.setParameters(countStmt);
        		
        		ResultSet rs = countStmt.executeQuery();
        		int totpage = 0;
        		
        		if (rs.next()) {
        			totpage = rs.getInt(1);
        		}
        		
        		rs.close();
        		countStmt.close();
        		connection.close();
        		
        		//分页计算
        		page.setTotalRecord(totpage);
        	}

            //对原始Sql追加limit  
            int offset = (page.getPageNo() - 1) * page.getPageSize();

            BoundSql newBoundSql = null;
            StringBuffer sb = new StringBuffer();
            sb.append(originalSql).append(" limit ").append(offset).append(",").append(page.getPageSize());
            newBoundSql = copyFromBoundSql(mappedStatement, boundSql, sb.toString());

            MappedStatement newMs = copyFromMappedStatement(mappedStatement, new BoundSqlSqlSource(newBoundSql));
            invocation.getArgs()[0] = newMs;
        }

        return invocation.proceed();
    }

    /**
     * 根据给定的xpath查询Page对象
     */
    private Page searchPageWithXpath(Object o, String... xpaths) {
        JXPathContext context = JXPathContext.newContext(o);
        Object result;

        for (String xpath : xpaths) {
            try {
                result = context.selectNodes(xpath);
            } catch (JXPathNotFoundException e) {
                continue;
            }

            if (result instanceof List && (((List) result).size() > 0)) {
                if (((List) result).get(0) instanceof Page) {
                    return (Page) (((List) result).get(0));
                }
            }
        }

        return null;
    }

    /**
     * 复制MappedStatement对象 
     */
    private MappedStatement copyFromMappedStatement(MappedStatement ms,
        SqlSource newSqlSource) {
        Builder builder = new Builder(ms.getConfiguration(), ms.getId(), newSqlSource, ms.getSqlCommandType());

        builder.resource(ms.getResource());
        builder.fetchSize(ms.getFetchSize());
        builder.statementType(ms.getStatementType());
        builder.keyGenerator(ms.getKeyGenerator());

        if (null != ms.getKeyProperties()) {
            if (ms.getKeyProperties().length > 0) {
                builder.keyProperty(ms.getKeyProperties()[0]);
            }
        }

        builder.timeout(ms.getTimeout());
        builder.parameterMap(ms.getParameterMap());
        builder.resultMaps(ms.getResultMaps());
        builder.resultSetType(ms.getResultSetType());
        builder.cache(ms.getCache());
        builder.flushCacheRequired(ms.isFlushCacheRequired());
        builder.useCache(ms.isUseCache());

        return builder.build();
    }

    /**
     * 复制BoundSql对象
     */
    private BoundSql copyFromBoundSql(MappedStatement ms, BoundSql boundSql,
        String sql) {
        BoundSql newBoundSql = new BoundSql(ms.getConfiguration(), sql, boundSql.getParameterMappings(), boundSql.getParameterObject());

        for (ParameterMapping mapping : boundSql.getParameterMappings()) {
            String prop = mapping.getProperty();

            if (boundSql.hasAdditionalParameter(prop)) {
                newBoundSql.setAdditionalParameter(prop, boundSql.getAdditionalParameter(prop));
            }
        }
        return newBoundSql;
    }

    /**
     * 根据原Sql语句获取对应的查询总记录数的Sql语句
     */
    private String getSimpleCountSql(String sql) {
        return "SELECT COUNT(*) FROM (" + sql + ") aliasForPage";
    }

    @Override
	public Object plugin(Object arg0) {
        return Plugin.wrap(arg0, this);
    }

    @Override
	public void setProperties(Properties arg0) {
    	pageSqlId = arg0.getProperty("pageSqlId");
    	dialect = arg0.getProperty("dialect");
    }

    public class BoundSqlSqlSource implements SqlSource {
        BoundSql boundSql;

        public BoundSqlSqlSource(BoundSql boundSql) {
            this.boundSql = boundSql;
        }

        @Override
		public BoundSql getBoundSql(Object parameterObject) {
            return boundSql;
        }
    }

//
//    /**
//     * 处理selectBody去除Order by
//     *
//     * @param selectBody
//     */
//    public void processSelectBody(SelectBody selectBody) {
//        if (selectBody instanceof PlainSelect) {
//            processPlainSelect((PlainSelect) selectBody);
//        } else if (selectBody instanceof WithItem) {
//            WithItem withItem = (WithItem) selectBody;
//            if (withItem.getSubSelect().getSelectBody() != null) {
//                processSelectBody(withItem.getSubSelect().getSelectBody());
//            }
//        } else {
//            SetOperationList operationList = (SetOperationList) selectBody;
//            if (operationList.getSelects() != null && operationList.getSelects().size() > 0) {
//                List<SelectBody> plainSelects = operationList.getSelects();
//                for (SelectBody plainSelect : plainSelects) {
//                    processSelectBody(plainSelect);
//                }
//            }
//            if (!orderByHashParameters(operationList.getOrderByElements())) {
//                operationList.setOrderByElements(null);
//            }
//        }
//    }
//
//
//    /**
//     * 处理PlainSelect类型的selectBody
//     *
//     * @param plainSelect
//     */
//    public void processPlainSelect(PlainSelect plainSelect) {
//        if (!orderByHashParameters(plainSelect.getOrderByElements())) {
//            plainSelect.setOrderByElements(null);
//        }
//        if (plainSelect.getFromItem() != null) {
//            processFromItem(plainSelect.getFromItem());
//        }
//        if (plainSelect.getJoins() != null && plainSelect.getJoins().size() > 0) {
//            List<Join> joins = plainSelect.getJoins();
//            for (Join join : joins) {
//                if (join.getRightItem() != null) {
//                    processFromItem(join.getRightItem());
//                }
//            }
//        }
//    }

//
//    /**
//     * 判断Orderby是否包含参数，有参数的不能去
//     *
//     * @param orderByElements
//     * @return
//     */
//    public boolean orderByHashParameters(List<OrderByElement> orderByElements) {
//        if (orderByElements == null) {
//            return false;
//        }
//        for (OrderByElement orderByElement : orderByElements) {
//            if (orderByElement.toString().contains("?")) {
//                return true;
//            }
//        }
//        return false;
//    }
//
//    /**
//     * 处理子查询
//     *
//     * @param fromItem
//     */
//    public void processFromItem(FromItem fromItem) {
//        if (fromItem instanceof SubJoin) {
//            SubJoin subJoin = (SubJoin) fromItem;
//            if (subJoin.getJoinList().getJoin() != null) {
//                if (subJoin.getJoin().getRightItem() != null) {
//                    processFromItem(subJoin.getJoin().getRightItem());
//                }
//            }
//            if (subJoin.getLeft() != null) {
//                processFromItem(subJoin.getLeft());
//            }
//        } else if (fromItem instanceof SubSelect) {
//            SubSelect subSelect = (SubSelect) fromItem;
//            if (subSelect.getSelectBody() != null) {
//                processSelectBody(subSelect.getSelectBody());
//            }
//        } else if (fromItem instanceof ValuesList) {
//
//        } else if (fromItem instanceof LateralSubSelect) {
//            LateralSubSelect lateralSubSelect = (LateralSubSelect) fromItem;
//            if (lateralSubSelect.getSubSelect() != null) {
//                SubSelect subSelect = lateralSubSelect.getSubSelect();
//                if (subSelect.getSelectBody() != null) {
//                    processSelectBody(subSelect.getSelectBody());
//                }
//            }
//        }
//        //Table时不用处理
//    }
//
//    /**
//     * 是否可以用简单的count查询方式
//     *
//     * @param select
//     * @return
//     */
//    public boolean isSimpleCount(PlainSelect select) {
//        //包含group by的时候不可以
//        if (select.getGroupByColumnReferences() != null) {
//            return false;
//        }
//        //包含distinct的时候不可以
//        if (select.getDistinct() != null) {
//            return false;
//        }
//        for (SelectItem item : select.getSelectItems()) {
//            //select列中包含参数的时候不可以，否则会引起参数个数错误
//            if (item.toString().contains("?")) {
//                return false;
//            }
//            //如果查询列中包含函数，也不可以，函数可能会聚合列
//            if (item instanceof SelectExpressionItem) {
//                if (((SelectExpressionItem) item).getExpression() instanceof Function) {
//                    return false;
//                }
//            }
//        }
//        return true;
//    }
}
