package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.Data;

import java.util.Date;

/**
 * KING_DEE_PAY_REFUND_BILL
 *
 * <AUTHOR>
@Data
@Table(name = "KING_DEE_PAY_REFUND_BILL")
public class KingDeePayRefundBillEntity extends BaseEntity {
    /**
     * ID
     */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;

    /**
     * 单据内码
     */
    private String fId;

    /**
     * 单据编号
     */
    private String fBillNo;

    /**
     * 单据类型id
     */
    private String fBillTypeId;

    /**
     * 单据日期
     */
    private String fDate;

    /**
     * 往来单位类型
     */
    private String fContactUnitType;

    /**
     * 往来单位
     */
    private String fContactUnit;

    /**
     * 业务类型
     */
    private String fBusinessType;

    /**
     * 付款单位类型
     */
    private String fPayUnitType;

    /**
     * 结算组织
     */
    private String fSettleOrgId;

    /**
     * 采购组织
     */
    private String fPurchaseOrgId;

    /**
     * 付款组织
     */
    private String fPayOrgId;

    /**
     * 是否转款
     */
    private String fQzokSfzk;

    /**
     * 银行流水号
     */
    private String fQzokLsh;

    /**
     * 贝登单据头ID
     */
    private String fQzokBddjtid;

    /**
     * 明细
     */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fRefundBillEntry;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 创建者id
     */
    private Integer creator;

    /**
     * 修改者id
     */
    private Integer updater;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 修改者名称
     */
    private String updaterName;

}