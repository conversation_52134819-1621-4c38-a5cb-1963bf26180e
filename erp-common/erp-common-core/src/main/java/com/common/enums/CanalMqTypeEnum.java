package com.common.enums;

/**
 * @Author: daniel
 * @Date: 2021/3/16 17 03
 * @Description: canal消息操作类型枚举类
 */
public enum CanalMqTypeEnum {

    /**
     * 新增
     */
    INSERT("INSERT"),

    /**
     * 更新
     */
    UPDATE("UPDATE"),

    /**
     * 删除
     */
    DELETE("DELETE"),

    /**
     * 数据表字段变更
     */
    ALTER("ALTER"),

    /**
     * 增加索引
     */
    ADD_INDEX("CINDEX"),

    /**
     * 默认枚举
     */
    DEFAULT_TYPE("")
    ;

    public static CanalMqTypeEnum getInstance(String operateType) {
        for (CanalMqTypeEnum v : values()) {
            if (v.operateType.equalsIgnoreCase(operateType)) {
                return v;
            }
        }
        return DEFAULT_TYPE;
    }

    private String operateType;

    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }

    CanalMqTypeEnum(String operateType) {
        this.operateType = operateType;
    }
}
