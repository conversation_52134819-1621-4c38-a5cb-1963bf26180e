package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.*;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchFlowNodeOrderDetailPriceDto extends BatchBaseDto {
    /**
     * 主键
     */
    private Long flowNodeOrderPriceId;

    /**
     * 关联业务流转单明细ID
     */
    private Long flowOrderDetailId;

    /**
     * 节点ID
     */
    private Long flowNodeId;

    /**
     * 节点级别
     */
    private Integer nodeLevel;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 加价率（%），与上一节点的价格计算相关
     */
    private BigDecimal markupRate;

    /**
     * 毛利率（%）
     */
    private BigDecimal grossProfitRate;

    /**
     * 是否删除
     */
    private Integer isDelete;

}
