package com.vedeng.crm.business.quote.domain.enums;

/**
 * 报备结果0无需报备 1报备中 2报备成功 3报备失败
 */
public enum ReportStatusEnum {

    REPORT_NOT_NEED(0,"无需报备"),
    REPORT_ING(1,"报备中"),
    REPORT_SUCCESS(2,"报备成功"),
    REPORT_FAIL(3,"报备失败");

    private Integer status;
    private String desc;

    ReportStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByStatus(Integer status) {
        if (status == null){
            return "未报备";
        }
        for (ReportStatusEnum reportStatusEnum : ReportStatusEnum.values()) {
            if (reportStatusEnum.getStatus().equals(status)) {
                return reportStatusEnum.getDesc();
            }
        }
        return null;
    }
}
