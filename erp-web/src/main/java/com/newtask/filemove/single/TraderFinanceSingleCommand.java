package com.newtask.filemove.single;

import com.google.common.collect.Lists;
import com.newtask.filemove.AbstractFileMoveInterface;
import com.newtask.filemove.dto.CommonConstant;

import com.newtask.filemove.dto.FileMoveDto;
import com.newtask.filemove.theadpool.BrandTask;
import com.newtask.filemove.theadpool.TraderFinanceTask;
import com.vedeng.filemove.dao.FileMoveDao;
import com.vedeng.goods.model.Brand;
import com.vedeng.trader.model.TraderFinance;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class TraderFinanceSingleCommand extends AbstractFileMoveInterface {
    @Resource
    private FileMoveDao fileMoveDao;

    @Override
    protected String getCommandType() {
        return CommonConstant.SINGLE;
    }

    @Override
    protected List<FileMoveDto> getPendingList(String requsetParam) {

        String[] params = requsetParam.split(",");

        List<TraderFinance> brandList = fileMoveDao.getTraderFinanceByParam(Integer.valueOf(params[0]),Integer.valueOf(params[1]));

        return Lists.transform(brandList, (entity) -> {
                                                FileMoveDto fileMoveDto = new FileMoveDto();
                                                fileMoveDto.setId(entity.getTraderFinanceId());
                                                fileMoveDto.setDomain(entity.getAverageTaxpayerDomain());
                                                fileMoveDto.setUri(entity.getAverageTaxpayerUri());
                                                return fileMoveDto;
                                            });
    }

    protected Runnable getFileMoveTask(List<FileMoveDto> fileMoveDtoList){
        return new TraderFinanceTask(fileMoveDtoList);
    }
}
