<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="saleorder" type="com.vedeng.order.model.Saleorder" required="true" %>
<%@attribute name="saleorderAttachmentList" type="java.util.List" required="true" %>
<%@attribute name="commentMapContractReturn" type="java.util.Map" required="true" %>
<%@attribute name="taskInfoContractReturn" type="java.lang.Object" required="true" %>
<%@attribute name="historicActivityInstanceContractReturn" type="java.util.List" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>

<div class="parts content1" liname="销售声明" id="销售声明">
    <div class="title-container">
        <div class="table-title nobor">
            销售声明
        </div>
        <c:if test="${saleorder.status != 3}">
            <div class="title-click nobor pop-new-data"
                 layerParams='{"width":"520px","height":"200px","title":"销售声明","link":"/order/saleorder/salesStatementInit.do?saleorderId=${saleorder.saleorderId}"}'>上传</div>
        </c:if>
    </div>
    <table class="table table-bordered table-striped table-condensed table-centered">
        <thead>
        <tr>
            <th>销售声明</th>
            <th class="table-small">操作人</th>
            <th class="table-small">时间</th>
            <th class="table-smallest5">操作</th>
        </tr>
        </thead>
        <tbody>
        <c:set var="salesStatementNum" value="0"></c:set>
        <c:forEach items="${saleorderAttachmentList}" var="list" varStatus="status">
            <c:if test="${list.attachmentFunction == 1201}">
                <c:set var="salesStatementNum" value="1"></c:set>
                <tr>
                    <td class="font-blue"><a href="http://${list.domain}${list.uri}" target="_blank">${list.name}</a></td>
                    <td>${list.username}</td>
                    <td><date:date value ="${list.addTime}"/></td>
                    <td>
                        <div class="caozuo">
                        </div>
                    </td>
                </tr>
            </c:if>
        </c:forEach>
        <c:if test="${salesStatementNum == 0}">
            <tr>
                <td colspan="4">暂无销售声明。</td>
            </tr>
        </c:if>
        </tbody>

    </table>
</div>

<%--合同回传--%>
<div class="parts content1" liname="合同回传" id="合同回传">
    <div class="title-container">
        <div class="table-title nobor">
            合同回传
        </div>
        <c:set var="sizeCount" value="0" />
        <c:forEach items="${saleorderAttachmentList}" var="list" varStatus="status">
            <c:if test="${list.attachmentFunction == 492 }">
                <c:set var="sizeCount" value="${sizeCount+1}"></c:set>
            </c:if>
        </c:forEach>
        <c:if test="${(saleorder.contractStatus == null || saleorder.contractStatus == 2)}">
            <div class="title-click nobor pop-new-data"
                 layerParams='{"width":"50%","height":"50%","title":"合同回传","link":"/orderstream/saleorder/batchContractFile.do?saleorderId=${saleorder.saleorderId}"}'>上传</div>
        </c:if>
        <input type="hidden" id="alertCount" value="${saleorder.contractStatus == 2?0:1}">
        <c:if test="${sizeCount>0 && (saleorder.contractStatus == null || saleorder.contractStatus == 2) && saleorder.validStatus == 1}">
            <div class="title-click nobor" onclick="requestCheck(${saleorder.saleorderId},${taskInfoContractReturn.id == null ?0: taskInfoContractReturn.id})">申请审核</div>
        </c:if>
    </div>
    <table class="table table-bordered table-striped table-condensed table-centered">
        <thead>
        <tr>
            <th>合同回传</th>
            <th>操作人</th>
            <th>时间</th>
            <th>审核状态</th>
            <th>备注</th>
            <th>上传备注</th>
            <th class="table-smallest2"> 操作</th>
        </tr>
        </thead>
        <tbody>
        <c:set var="contractReturnNum" value="0" />
        <c:set var="statusCount" value="0" />
        <c:set var="contractReturnRemark" value="" />
        <c:if test="${null!=historicActivityInstanceContractReturn}">
            <c:forEach var="hio" items="${historicActivityInstanceContractReturn}" varStatus="status">
                <c:if test="${not empty  hio.activityName}">
                    <c:if test="${hio.activityType != 'endEvent'}">
                        <c:set var="contractReturnRemark" value="${commentMapContractReturn[hio.taskId]}" />
                    </c:if>

                </c:if>
            </c:forEach>
        </c:if>
        <c:forEach items="${saleorderAttachmentList}" var="list" varStatus="status">
            <c:if test="${list.attachmentFunction == 492 }">
                <c:set var="contractReturnNum" value="1"></c:set>
                <c:set var="statusCount" value="${statusCount+1}"></c:set>
                <tr>
                    <td class="font-blue"><a href="http://${list.domain}${list.uri}" target="_blank">${list.name}</a></td>
                    <td>
                        <c:choose>
                            <c:when test="${not empty list.alt && list.alt eq '电子签章'}">
                               客户
                            </c:when>
                            <c:otherwise>
                                ${list.username}
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td><date:date value ="${list.addTime}"/></td>
                    <c:if test="${statusCount==1}">
                        <td rowspan="${sizeCount}">
                            <c:if test="${saleorder.contractStatus == null}">
                                待审核
                            </c:if>
                            <c:if test="${saleorder.contractStatus == 0}">
                                审核中
                            </c:if>
                            <c:if test="${saleorder.contractStatus == 1}">
                                审核通过
                            </c:if>
                            <c:if test="${saleorder.contractStatus == 2}">
                                <span class="font-red">审核不通过</span>
                            </c:if>
                        </td>
                        <td rowspan="${sizeCount}">
                            <c:choose>
                                <c:when test="${not empty list.alt && list.alt eq '电子签章'}">
                                    ${list.alt}
                                </c:when>
                                <c:otherwise>
                                    ${contractReturnRemark}
                                </c:otherwise>
                            </c:choose>
                        </td>

                    </c:if>
                    <td>
                        ${list.alt}
                    </td>
                    <td>
                        <div class="caozuo">
                            <c:if test="${(saleorder.contractStatus == null || saleorder.contractStatus == 2)}">
                                <span class="caozuo-red" onclick="contractReturnDelNew(${list.attachmentId})">删除</span>
                            </c:if>
                        </div>
                    </td>

                </tr>
            </c:if>
        </c:forEach>
        <c:if test="${contractReturnNum == 0}">
            <tr>
                <td colspan="7">暂无合同回传。</td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>

<%--送货单--%>
<c:if test="${saleorder.validStatus eq 1}">
    <div class="parts content1" liname="送货单" id="送货单">
    <div class="title-container">
        <div class="table-title nobor">
            送货单回传
        </div>
        <c:if test="${saleorder.status != 3}">
            <div class="title-click nobor pop-new-data"
                 layerParams='{"width":"520px","height":"200px","title":"送货单回传","link":"/order/saleorder/deliveryOrderReturnInit.do?saleorderId=${saleorder.saleorderId}"}'>上传</div>
        </c:if>
    </div>
    <table class="table table-bordered table-striped table-condensed table-centered">
        <thead>
        <tr>
            <th>送货单</th>
            <th class="table-small">操作人</th>
            <th class="table-small">时间</th>
            <th class="table-smallest5">操作</th>
        </tr>
        </thead>
        <tbody>
        <c:set var="deliveryOrderReturnNum" value="0"></c:set>
        <c:forEach items="${saleorderAttachmentList}" var="list" varStatus="status">
            <c:if test="${list.attachmentFunction == 493}">
                <c:set var="deliveryOrderReturnNum" value="1"></c:set>
                <tr>
                    <td class="font-blue"><a href="http://${list.domain}${list.uri}" target="_blank">${list.name}</a></td>
                    <td>${list.username}</td>
                    <td><date:date value ="${list.addTime}"/></td>
                    <td>
                        <div class="caozuo">
                            <c:if test="${saleorder.status != 3}">
                                <span class="caozuo-red" onclick="deliveryOrderReturnDel(${list.attachmentId})">删除</span>
                            </c:if>
                        </div>
                    </td>
                </tr>
            </c:if>
        </c:forEach>
        <c:if test="${deliveryOrderReturnNum == 0}">
            <tr>
                <td colspan="4">暂无送货单回传。</td>
            </tr>
        </c:if>
        </tbody>

    </table>
</div>
</c:if>

