<%@ page import="lombok.experimental.var" %><%--
  Created by IntelliJ IDEA.
  User: ckt
  Date: 2023/11/21
  Time: 10:11
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<html>
<head>
    <title>Title</title>
</head>
<script>
    function downloadExe(){
        var a = document.createElement('a');
        // a.href = "http://file.ivedeng.com/file/download?resourceId=133243a2ee65439e81609552f81dcc25";
        a.href = "https://file.vedeng.com/file/download?resourceId=12f64312de674c9399c1427f06c666a7";
        a.download = 'myExe.exe';
        a.click();
    }

    function downloadPdf(){
        // 创建包含文本内容的 Blob 对象
        var text = '';
        ${pdfUrls}.forEach(t => {
            text += t+'\n';
        })
        var blob = new Blob([text], {type: 'text/plain'});
        // 创建指向这个 Blob 对象的 URL
        var url = URL.createObjectURL(blob);
        // 在新窗口中打开这个 URL
        var a = document.createElement('a');
        a.href = url;
        a.download = 'myPDF.txt';
        a.click();
    }
    function downloadXml(){
        var text = '';
        ${xmlUrls}.forEach(t => {
            text += t+'\n';
        })
        // 创建包含文本内容的 Blob 对象
        var blob = new Blob([text], {type: 'text/plain'});
        // 创建指向这个 Blob 对象的 URL
        var url = URL.createObjectURL(blob);
        // 在新窗口中打开这个 URL
        var a = document.createElement('a');
        a.href = url;
        a.download = 'myXML.txt';
        a.click();
    }
</script>
<body>
<div id="app">
    <div class="padding-class">
        <span style="font-size: 17px">说明：</span><br/>
        1.将根据查询结果下载配置文件，请先确认查询条件 <br/>
        2.配置文件下载后，请打开下载软件载入配置文件进行下载 <br/>
        3.只下载<span style="color: red">数电发票</span>，纸质发票与电子发票请单独处理 <br/>
        4.单次最大下载5000个文件
    </div>
    <span class="padding-class" style="font-size: 17px">下载:</span>

    <div class="img-parent">
        <div class="img-class">
            <img src="/static/images/icon/exe.svg" onclick="downloadExe()"/>
            <span>下载软件</span>
        </div>
        <div class="img-class">
            <img src="/static/images/icon/pdf.svg" onclick="downloadPdf()"/>
            <span>PDF文件</span>
        </div>
        <div class="img-class">
            <img src="/static/images/icon/xml.svg" onclick="downloadXml()"/>
            <span>XML文件</span>
        </div>
    </div>
</div>

</body>
<style>
    .img-parent {
        display: flex;
        justify-content: space-between;
    }

    .img-class {
        text-align: center;
        cursor: pointer;
    }

    .img-parent img {
        width: 100px;
        height: 100px;
        padding: 0px 20px 0px 20px;
    }

    .img-parent span {
        display: block;
    }
    #app {
        font-size: 14px;
        font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
        color: #606266;
    }
    .padding-class {
        display: block;
        padding-top: 16px;
        padding-left: 20px;
        line-height: 2.5;
    }
</style>
</html>
