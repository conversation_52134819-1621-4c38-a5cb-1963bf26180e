package com.wms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.activiti.dto.ActivitiNoticeInfoEntity;
import com.vedeng.common.activiti.service.ActivitiNoticeService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.OrderNoDict;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.logistics.dao.WmsOutputOrderGoodsExtraMapper;
import com.vedeng.logistics.dao.ext.GoodsAcceptanceReportExtMapper;
import com.vedeng.logistics.model.GoodsAcceptanceReport;
import com.vedeng.logistics.model.WmsOutputOrderGoodsExtra;
import com.vedeng.logistics.model.vo.GoodsAcceptanceReportDto;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.model.dto.ProductManageAndAsistDto;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.AttachmentService;
import com.vedeng.system.service.UserService;
import com.vedeng.uac.api.dto.BatchMessageSendDto;
import com.wms.constant.ReturnStatusConstant;
import com.wms.constant.VerifyStatusEnum;
import com.wms.constant.WmsOutputOrderTypeConstant;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.model.dto.AddLendOutDto;
import com.wms.model.dto.WMSLendOutQueryDto;
import com.wms.model.po.WmsLendOutOrder;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WMSLendOutService;
import com.wms.service.chain.Build.StepBuildFactory;
import com.wms.service.chain.HandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.service.stockallocation.StockAllocationStrategy;
import com.wms.service.stockcalculate.StockinfoCaculateInterface;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
@Service
public class
WMSLendOutServiceImpl implements WMSLendOutService {

    private static final Logger LOGGER = LoggerFactory.getLogger(WMSLendOutServiceImpl.class);

    @Autowired
    private WmsOutputOrderMapper outputOrderMapper;

    @Autowired
    private WmsOutputOrderGoodsMapper outputOrderGoodsMapper;

    @Autowired
    private WMSInterfaceFactory WMSInterfaceFactory;

    @Resource
    private GoodsAcceptanceReportExtMapper goodsAcceptanceReportExtMapper;

    @Resource
    private AttachmentService attachmentService;

    @Value("${oss_url}")
    protected String domain;

    @Autowired
    @Qualifier("lendAuditPassCaculateImpl")
    private StockinfoCaculateInterface stockinfoCaculateInterface;

    @Resource
    private UserMapper userMapper;

    @Autowired
    @Qualifier("saleOrderStockAllocationStrategy")
    private StockAllocationStrategy stockAllocationStrategy;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    private StepBuildFactory stepBuildFactory;

    @Resource
    private OrderNoDict orderNoDict;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Autowired
    private WmsOutputOrderGoodsExtraMapper wmsOutputOrderGoodsExtraMapper;

    @Autowired
    private UserService userService;
    @Autowired
    private UacWxUserInfoApiService uacWxUserInfoApiService ;

    @Override
    public List<WmsLendOutOrder> querylistPage(WMSLendOutQueryDto lendOutQueryDto, Page page) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("lendOutQueryDto", lendOutQueryDto);
        map.put("page", page);
        return outputOrderMapper.querylistPage(map);
    }

    /**
     * 新增外借出库单
     * @param addLendOutDto
     */
    @Override
    @Transactional
    public Long addLendOutOrder(AddLendOutDto addLendOutDto, User user) throws Exception{

        WmsOutputOrder lendOutOrder = convertTolendOutBean(addLendOutDto,user);
        outputOrderMapper.insertSelective(lendOutOrder);

        updateOrderNo(lendOutOrder);

        List<WmsOutputOrderGoods> lendOutOrderGoods = convertTolendOutGoodsBean(lendOutOrder,addLendOutDto);
        outputOrderGoodsMapper.batchInsert(lendOutOrderGoods);

        //关联出入库商品额外信息表,维护商品的产品经理和产品助理
        setProductBelongInfo(lendOutOrderGoods);

        return lendOutOrder.getId();
    }

    /**
     * 商品关联产品经理及助理
     * @param lendOutOrderGoods
     */
    private void setProductBelongInfo(List<WmsOutputOrderGoods> lendOutOrderGoods) {

        if (CollectionUtils.isEmpty(lendOutOrderGoods)) {
            return;
        }

        lendOutOrderGoods.stream().forEach(good -> {

            ProductManageAndAsistDto productManageAndAsistDto = this.coreSkuMapper.queryProductManageAndAsist(good.getSkuNo());
            WmsOutputOrderGoodsExtra wmsOutputOrderGoodsExtra=new WmsOutputOrderGoodsExtra();
            wmsOutputOrderGoodsExtra.setWmsOutputOrderId(good.getWmsOutputOrderId());
            wmsOutputOrderGoodsExtra.setSkuNo(good.getSkuNo());
            wmsOutputOrderGoodsExtra.setProductBelongIdInfo(productManageAndAsistDto.getProductAssitUserId() + "," + productManageAndAsistDto.getProductManageUserId());
            wmsOutputOrderGoodsExtra.setProductBelongNameInfo(productManageAndAsistDto.getProductAssitName() + "," + productManageAndAsistDto.getProductManageName());

            this.wmsOutputOrderGoodsExtraMapper.insertSelective(wmsOutputOrderGoodsExtra);

        });
    }

    private void updateOrderNo(WmsOutputOrder lendOutOrder) {
        WmsOutputOrder updateOrder = new WmsOutputOrder();
        updateOrder.setId(lendOutOrder.getId());
        updateOrder.setOrderNo(orderNoDict.getOrderNum(lendOutOrder.getId().intValue(), OrderNoDict.LEND_OUT_NEW_TYPE));
        outputOrderMapper.updateByPrimaryKeySelective(updateOrder);
    }

    /**
     * 外接单审核通过
     * @param lendOutOrderId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lendOutOrderAuditPass(Long lendOutOrderId) throws Exception {

        HandlerStepContext handlerStepContext = new HandlerStepContext();
        handlerStepContext.put("lendOutOrderId",lendOutOrderId);

        HandlerStep handlerStep = stepBuildFactory.buildLendOutStep();
        handlerStep.dealWith(handlerStepContext);
    }

    /**
     * 更新wms状态
     * @param lendOutOrderId
     * @param auditStatusValue
     */
    @Override
    public void updateLendOutAuditStatus(Long lendOutOrderId, int auditStatusValue) {

        WmsOutputOrder wmsOutputOrder = new WmsOutputOrder();
        wmsOutputOrder.setId(lendOutOrderId);
        wmsOutputOrder.setVerifyStatus(auditStatusValue);
        wmsOutputOrder.setUpdateTime(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));

        outputOrderMapper.updateByPrimaryKeySelective(wmsOutputOrder);
    }

    @Override
    public WmsOutputOrder findLendOutById(Long lendOutId) {
        return outputOrderMapper.selectByPrimaryKey(lendOutId);
    }

    @Override
    public List<WmsOutputOrderGoodsExtra> findGoodsAuditList(WmsOutputOrderGoodsExtra wmsOutputOrderGoodsExtra) {
        return wmsOutputOrderGoodsExtraMapper.selectWmsExtraByLendOutId(wmsOutputOrderGoodsExtra);
    }

    @Override
    public GoodsAcceptanceReport getGooodsAcceptanceReportById(Long goodsAcceptanceReportId) {
        return goodsAcceptanceReportExtMapper.selectByPrimaryKey(goodsAcceptanceReportId);
    }

    @Override
    public void addReportByOrderNo(GoodsAcceptanceReport report) {
        goodsAcceptanceReportExtMapper.insertSelective(report);
    }

    @Override
    public void addAttachmentByGoodsAcceptanceReportId(List<String> nameList, List<String> uriList, Long goodsAcceptanceReportId, User user) {
        if(CollectionUtils.isEmpty(nameList) || CollectionUtils.isEmpty(uriList) || Objects.isNull(goodsAcceptanceReportId)){
            return;
        }
        for (int i=0;i<nameList.size();i++) {
            Attachment attachment = new Attachment();
            attachment.setAttachmentType(SysOptionConstant.ID_461);
            attachment.setAttachmentFunction(SysOptionConstant.ID_4211);
            if (Objects.nonNull(user)) {
                attachment.setCreator(user.getUserId());
                attachment.setCreatorName(user.getUsername());
                attachment.setAddTime(DateUtil.sysTimeMillis());
            }
            attachment.setRelatedId(Integer.valueOf(goodsAcceptanceReportId.toString()));
            attachment.setDomain(domain);
            attachment.setUri(uriList.get(i));
            attachment.setName(nameList.get(i));
            attachment.setIsDeleted(0);
            attachmentService.saveOrUpdateAttachment(attachment);
        }
    }

    @Override
    @Transactional
    public void addReport(GoodsAcceptanceReport report, List<String> nameList, List<String> uriList, User user) {
        //新增报告
        report.setAddTime(new Date());
        report.setCreator(user.getUserId());
        report.setCreatorName(user.getUsername());
        report.setIsDelete(0);
        report.setModTime(new Date());
        report.setRemark("");
        report.setSku("V"+report.getGoodsId());
        report.setType(1);
        report.setUpdater(user.getUserId());
        report.setUpdateRemark("");
        report.setUpdaterName(user.getUsername());
        addReportByOrderNo(report);
        //新增附件
        addAttachmentByGoodsAcceptanceReportId(nameList,uriList,report.getGoodsAcceptanceReportId(),user);
    }

    @Override
    public void updateReport(GoodsAcceptanceReport report, List<String> nameList, List<String> uriList, User user) {
        //更新验收报告单
        report.setIsDelete(0);
        report.setModTime(new Date());
        report.setRemark("");
        report.setSku("V"+report.getGoodsId());
        report.setType(1);
        report.setUpdater(user.getUserId());
        report.setUpdateRemark("");
        report.setUpdaterName(user.getUsername());
        goodsAcceptanceReportExtMapper.updateByPrimaryKeySelective(report);
        //删除验收报告单附件
        Attachment attachment = new Attachment();
        attachment.setRelatedId(Integer.valueOf(report.getGoodsAcceptanceReportId().toString()));
        attachment.setAttachmentType(SysOptionConstant.ID_461);
        attachment.setAttachmentFunction(SysOptionConstant.ID_4211);
        attachmentService.delAttachment(attachment);
        //新增验收报告单附件
        addAttachmentByGoodsAcceptanceReportId(nameList,uriList,report.getGoodsAcceptanceReportId(),user);
    }

    @Override
    public List<GoodsAcceptanceReportDto> getAcceptanceReportList(String orderNo) {
        return goodsAcceptanceReportExtMapper.getAcceptanceReportList(orderNo,SysOptionConstant.ID_461,SysOptionConstant.ID_4211);
    }

    @Override
    public List<Attachment> getReportAttachmentList(Long goodsAcceptanceReportId) {
        Attachment attachment = new Attachment();
        attachment.setRelatedId(Integer.valueOf(goodsAcceptanceReportId.toString()));
        attachment.setAttachmentType(SysOptionConstant.ID_461);
        attachment.setAttachmentFunction(SysOptionConstant.ID_4211);
        return attachmentService.queryAttachmentList(attachment);
    }

    @Value("${erp_url}")
    private String erp_url; //erp_url	http://erp.ivedeng.com/ 此为apollo里的值

    public static final String NOTICE_MESSAGE_TEMPLATE_FOR_WEIXIN = "**外借单抄送通知**\n>    外借单号：**{lendOutNum}**  \n>    发起时间：{createTime}  \n>    借用原因：{reason}  \n>    借用企业：{factoryName}  \n>    产品信息：{skuInfo1}   \n>    [查看详情]({jumpUrl})";

    @Override
    public void sendWeixinNotice(Long lendOutId,  Collection<String> userNameList) {
        log.info("外借单开始消息推送:{},{}",lendOutId,userNameList);
        try{
            WmsOutputOrder wmsOutputOrder = outputOrderMapper.selectByPrimaryKey(lendOutId);
            List<WmsOutputOrderGoods> goodsExtraList = outputOrderGoodsMapper.getWmsOutPutOrderGoods(lendOutId);
            String message = new String(NOTICE_MESSAGE_TEMPLATE_FOR_WEIXIN);
            String lendOutNum = wmsOutputOrder.getOrderNo();//外借单号
            message = message.replace("{lendOutNum}",lendOutNum);

            String createTime =wmsOutputOrder.getAddTime();// 创建时间
            message = message.replace("{createTime}",createTime);

            String reason = wmsOutputOrder.getBorrowReason();//借用原因
            message = message.replace("{reason}",reason);

            String factoryName = wmsOutputOrder.getBorrowTraderName();//借用的企业
            message = message.replace("{factoryName}",factoryName);

            String skuNo = goodsExtraList.get(0).getSkuNo();
            Integer fisrtNum = goodsExtraList.get(0).getOutputNum();
            CoreSku coreSku = coreSkuMapper.getSkuInfoByNo(skuNo);
            String skuInfo1 =  coreSku.getSkuName() +"*" + fisrtNum;

            Integer skuNum = CollectionUtils.size(goodsExtraList);

            Integer skuSum = goodsExtraList.stream().mapToInt(WmsOutputOrderGoods::getOutputNum).sum();
            if(CollectionUtils.size(goodsExtraList)>1){
                skuInfo1 = skuInfo1+"等 （总数量"+skuSum+"个，总计"+skuNum+"种）";
            }
            message = message.replace("{skuInfo1}",skuInfo1);


            String targetUrl = URLEncoder.encode("/wms/commodityLendOut/detail.do?lendOutId="+lendOutId.intValue()+"&title=外借单详情","UTF-8");
            String jumpUrl = erp_url+"index.do?target="+targetUrl;
            message = message.replace("{jumpUrl}",jumpUrl);


            List<User> userList =  userService.getUserByUserIds(userNameList);

            BatchMessageSendDto batchMessageSendDto = new BatchMessageSendDto();
            batchMessageSendDto.setUserIdList(userList.stream().map(User::getUserId).collect(Collectors.toList()));
            batchMessageSendDto.setMsgContent(message);
            RestfulResult result = uacWxUserInfoApiService.sendBatchMsg(batchMessageSendDto);
            if("success".equals(result.getCode())){
                return;
            }else{
                log.warn("外借单推送消息可能含有失败的内容:{}", JSONObject.toJSONString(result));
            }
        }catch (Exception e){
            log.error("外借单推送消息失败",e);
        }
    }

    public static final String NOTICE_MESSAGE_TEMPLATE_FOR_ACTIVITI = "**外借单审批提醒**\n>    **{lendOutNum}**审批到您已经30分钟了，请及时处理。  \n>    [查看详情]({jumpUrl})";

    @Autowired
    private ActivitiNoticeService activitiNoticeService;

    @Override
    public void sendWeixinNoticeForActiviti(Long lendOutId, String businessKey,String taskId, String userName) {
        try{
            WmsOutputOrder wmsOutputOrder = outputOrderMapper.selectByPrimaryKey(lendOutId);
            String message = new String(NOTICE_MESSAGE_TEMPLATE_FOR_ACTIVITI);
            String lendOutNum = wmsOutputOrder.getOrderNo();//外借单号
            message = message.replace("{lendOutNum}",lendOutNum);

            String targetUrl = URLEncoder.encode("/wms/commodityLendOut/detail.do?lendOutId="+lendOutId.intValue()+"&title=外借单详情","UTF-8");
            String jumpUrl = erp_url+"index.do?target="+targetUrl;
            message = message.replace("{jumpUrl}",jumpUrl);
            List<User> userList =  userService.getUserByUserIds(Arrays.asList(userName));


            ActivitiNoticeInfoEntity activitiNoticeInfoEntity = new ActivitiNoticeInfoEntity();
            activitiNoticeInfoEntity.setTaskId(taskId);
            activitiNoticeInfoEntity.setBusinessKey(businessKey);
            activitiNoticeInfoEntity.setIsClick(0);
            activitiNoticeInfoEntity.setClickTime(null);
            activitiNoticeInfoEntity.setAcceptUserName(userName);
            activitiNoticeInfoEntity.setAcceptUserId(userList.stream().map(User::getUserId).collect(Collectors.toList()).get(0));
            activitiNoticeInfoEntity.setNoticeTime(new Date());
            activitiNoticeService.saveActivitiTaskNoticeInfo(activitiNoticeInfoEntity);

            BatchMessageSendDto batchMessageSendDto = new BatchMessageSendDto();
            batchMessageSendDto.setUserIdList(userList.stream().map(User::getUserId).collect(Collectors.toList()));
            batchMessageSendDto.setMsgContent(message);
            RestfulResult result = uacWxUserInfoApiService.sendBatchMsg(batchMessageSendDto);
            if("success".equals(result.getCode())){
                return;
            }else{
                log.warn("外借单推送消息可能含有失败的内容:{}", JSONObject.toJSONString(result));
            }
        }catch (Exception e){
            log.error("外借单审批提醒失败",e);
        }



    }

    public static void main(String[] args) {
        String message = new String(NOTICE_MESSAGE_TEMPLATE_FOR_WEIXIN);

        message = message.replace("{lendOutNum}","1");
        System.out.println(message);
    }

    @Override
    public void updateWmsOutputOrderGoodsExtra(WmsOutputOrderGoodsExtra goodsExtra) {
        wmsOutputOrderGoodsExtraMapper.updateByPrimaryKeySelective(goodsExtra);
    }

    //新增出库单的Bean
    private WmsOutputOrder convertTolendOutBean(AddLendOutDto addLendOutDto,User user) {

        WmsOutputOrder wmsOutputOrder = new WmsOutputOrder();
        wmsOutputOrder.setType(WmsOutputOrderTypeConstant.LENDOUT);
        wmsOutputOrder.setVerifyStatus(VerifyStatusEnum.Reviewing.getValue());
        wmsOutputOrder.setReturnStatus(ReturnStatusConstant.UN_RETURN);

        wmsOutputOrder.setBorrowReason(addLendOutDto.getBorrowReason());
        wmsOutputOrder.setBorrowTraderId(addLendOutDto.getBorrowTraderId());
        wmsOutputOrder.setBorrowTraderName(addLendOutDto.getBorrowTraderName());
        wmsOutputOrder.setLogisticCommnet(addLendOutDto.getLogisticCommnet());


        String[] contactInfo = addLendOutDto.getTraderContact().split("/");

        wmsOutputOrder.setReceiver(contactInfo[0]);
        wmsOutputOrder.setReceiverPhone(contactInfo[1]);
        wmsOutputOrder.setReceiverTelphone(contactInfo[2]);

        String[] addressInfo = addLendOutDto.getTraderAddress().split("/");
        wmsOutputOrder.setReceiverAddress(addressInfo[0]);
        wmsOutputOrder.setDetailAddress(addressInfo[1]);

        wmsOutputOrder.setBelongDepartment(user.getOrgName());

        wmsOutputOrder.setCreator(user.getUsername());
        wmsOutputOrder.setUpdator(user.getUsername());

        String addTime = DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss");
        wmsOutputOrder.setAddTime(addTime);
        wmsOutputOrder.setUpdateTime(addTime);
        return wmsOutputOrder;
    }

    //新增出库单商品的Bean
    private List<WmsOutputOrderGoods> convertTolendOutGoodsBean(WmsOutputOrder lendOutOrder, AddLendOutDto addLendOutDto) {

        if(addLendOutDto.getSkuNo() == null || addLendOutDto.getSkuNo().length == 0){
            return null;
        }

        List<WmsOutputOrderGoods> outputOrderGoodsList = new ArrayList<>();

        for(int i = 0;i < addLendOutDto.getSkuNo().length;i++){
            WmsOutputOrderGoods wmsOutputOrderGoods = new WmsOutputOrderGoods();
            outputOrderGoodsList.add(wmsOutputOrderGoods);

            wmsOutputOrderGoods.setWmsOutputOrderId(lendOutOrder.getId());
            wmsOutputOrderGoods.setSkuNo(addLendOutDto.getSkuNo()[i]);
            wmsOutputOrderGoods.setOutputNum(addLendOutDto.getOutputNum()[i].intValue());
            wmsOutputOrderGoods.setExpectReturnedTime(addLendOutDto.getExpectReturnedTime()[i]);
            wmsOutputOrderGoods.setAddTime(lendOutOrder.getAddTime());
            wmsOutputOrderGoods.setUpdateTime(lendOutOrder.getAddTime());
        }

        return outputOrderGoodsList;
    }
}
