package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseReceiptCommand;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseReceiptDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseReceiptDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * @description:
 * @author: yana.jiang
 * @date: 2022/11/10
 */
@Mapper(componentModel = "spring")
public interface KingDeePurchaseReceiptCommandConvertor extends BaseCommandMapStruct<KingDeePurchaseReceiptCommand, KingDeePurchaseReceiptDto> {

    @Mapping(target = "FId", source = "FId")
    @Mapping(target = "FBillTypeId.FNumber", source = "FBillTypeId")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtId")
    @Mapping(target = "FDate", source = "FDate")
    @Mapping(target = "FStockOrgId.FNumber", source = "FStockOrgId")
    @Mapping(target = "FSupplierId.FNumber", source = "FSupplierId")
    @Override
    KingDeePurchaseReceiptCommand toCommand(KingDeePurchaseReceiptDto dto);

    @Mapping(target = "FMaterialId.FNumber", source = "FMaterialId")
    @Mapping(target = "FRealQty", source = "FRealQty")
    @Mapping(target = "FTaxPrice", source = "FTaxPrice")
    @Mapping(target = "FEntryTaxRate", source = "FEntryTaxRate")
    @Mapping(target = "FStockId.FNumber", source = "FStockId")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "f_QZOK_PCH", source = "FQzokPch")
    @Mapping(target = "f_QZOK_XLH", source = "FQzokXlh")
    @Mapping(target = "f_QZOK_SQLX", source = "FQzokSqlx")
    @Mapping(target = "f_QZOK_SFZF", source = "FQzokSfzf")
    @Mapping(target = "f_QZOK_BDDJHID", source = "FQzokBddjhId")
    KingDeePurchaseReceiptCommand.KingDeePurchaseReceiptDetailCommand toCommand(KingDeePurchaseReceiptDetailDto dto);
}
