package com.vedeng.crm.business.quote.domain.entity;

import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
    * 报价产品
    */
@Data
public class CrmQuoteorderGoodsNoSku extends  CrmQuoteorderGoods{

    /**
     * 品牌ID
     */
    private Integer brandId;

    /**
     * 产品图片
     */
    private String imgUrl;

    /**
     * 产品参数
     */
    private String paramContent;
}
