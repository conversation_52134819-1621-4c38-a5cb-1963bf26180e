package com.vedeng.flash.service.warningtask;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.aftersale.service.BuyorderExpenseItemApiService;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDetailDto;
import com.vedeng.flash.dao.EarlyWarningTaskMapper;
import com.vedeng.flash.dto.*;
import com.vedeng.flash.enums.EarlyWarningTaskStatusEnum;
import com.vedeng.flash.enums.EarlyWarningTaskTypeEnum;
import com.vedeng.flash.model.EarlyWarningTask;
import com.vedeng.flash.service.message.ExpeditingTicketsCreateMessageSender;
import com.vedeng.flash.service.message.ExpeditingTicketsOver48HoursMessageSender;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderGoods;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 催票任务
 */
@Service("expeditingTicketsService")
@Slf4j
public class ExpeditingTicketsService extends AbstractWarningtaskService{

    @Autowired
    private ExpeditingTicketsCreateMessageSender expeditingTicketsCreateMessageSender;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private EarlyWarningTaskMapper earlyWarningTaskMapper;

    @Resource
    private ExpeditingTicketsOver48HoursMessageSender expeditingTicketsOver48HoursMessageSender;

    @Resource
    private BuyorderExpenseItemApiService buyorderExpenseItemApiService;

    @Override
    protected void customPropertySet(EarlyWarningTask earlyWarningTask,EarlyWarningTaskDto earlyWarningTaskDto) {
        if(earlyWarningTaskDto.getNotice() == 1){
            BuyorderGoods buyorderGoods = this.buyorderGoodsMapper.selectByPrimaryKey(earlyWarningTaskDto.getRelateBusinessId());
            Buyorder buyorder = this.buyorderMapper.selectByPrimaryKey(buyorderGoods.getBuyorderId());
            //设置预警任务类型
            earlyWarningTask.setEarlyWarningType(EarlyWarningTaskTypeEnum.TICKET_TASK.getCode());
            earlyWarningTask.setRelateBusinessId(earlyWarningTaskDto.getRelateBusinessId());
            earlyWarningTask.setBusinessExtra1(buyorder.getBuyorderNo());
            earlyWarningTask.setBusinessExtra2(buyorder.getTraderId().longValue());
            earlyWarningTask.setUrgingTicketNum(earlyWarningTaskDto.getUrgingTicketNum());
            earlyWarningTask.setUrgingTicketAmount(
                    buyorderGoods.getPrice().multiply(BigDecimal.valueOf(earlyWarningTaskDto.getUrgingTicketNum())).setScale(2));
            earlyWarningTask.setTaskStatus(EarlyWarningTaskStatusEnum.INIT.getCode());
            earlyWarningTask.setTaskDealer(buyorder.getCreator().toString());
            earlyWarningTask.setSendInitMessage(false);
            earlyWarningTask.setSendOver48hoursMessage(false);
            earlyWarningTask.setIsDeleted(0);
            earlyWarningTask.setAddTime(DateUtil.getNowDate(DateUtil.TIME_FORMAT));
            earlyWarningTask.setUpdateTime(DateUtil.getNowDate(DateUtil.TIME_FORMAT));
            earlyWarningTask.setCreator(buyorder.getCreator());
            earlyWarningTask.setUpdator(buyorder.getCreator());
        }else {
            //获取采购费用单商品详情
            BuyorderExpenseItemDetailDto buyorderExpenseDetails = buyorderExpenseItemApiService.getBuyorderExpenseDetails(earlyWarningTaskDto.getRelateBusinessId());
            //获取采购费用单
            BuyorderExpenseDto buyOrderExpense = buyorderExpenseItemApiService.getBuyOrderExpense(buyorderExpenseDetails.getBuyorderExpenseItemId());
            //设置预警任务类型
            earlyWarningTask.setEarlyWarningType(EarlyWarningTaskTypeEnum.TICKET_TASK.getCode());
            earlyWarningTask.setRelateBusinessId(earlyWarningTaskDto.getRelateBusinessId());
            earlyWarningTask.setBusinessExtra1(buyOrderExpense.getBuyorderExpenseNo());
            earlyWarningTask.setBusinessExtra2(buyOrderExpense.getTraderId().longValue());
            earlyWarningTask.setUrgingTicketNum(earlyWarningTaskDto.getUrgingTicketNum());
            earlyWarningTask.setUrgingTicketAmount(
                    buyorderExpenseDetails.getPrice().multiply(BigDecimal.valueOf(earlyWarningTaskDto.getUrgingTicketNum())).setScale(2));
            earlyWarningTask.setTaskStatus(EarlyWarningTaskStatusEnum.INIT.getCode());
            earlyWarningTask.setTaskDealer(buyOrderExpense.getCreator().toString());
            earlyWarningTask.setSendInitMessage(false);
            earlyWarningTask.setSendOver48hoursMessage(false);
            earlyWarningTask.setIsDeleted(0);
            earlyWarningTask.setAddTime(DateUtil.getNowDate(DateUtil.TIME_FORMAT));
            earlyWarningTask.setUpdateTime(DateUtil.getNowDate(DateUtil.TIME_FORMAT));
            earlyWarningTask.setCreator(buyOrderExpense.getCreator());
            earlyWarningTask.setUpdator(buyOrderExpense.getCreator());
        }

    }

    @Override
    protected void sendMessageForWarningtaskCreate(EarlyWarningTask earlyWarningTask) {
        SendMessageDto sendMessageDto = new SendMessageDto();
        sendMessageDto.setEarlyWarningTask(earlyWarningTask);
        expeditingTicketsCreateMessageSender.sendMesssage(sendMessageDto);
        earlyWarningTask.setSendInitMessage(true);
        earlyWarningTaskMapper.updateByPrimaryKeySelective(earlyWarningTask);
    }
    public  List<EarlyWarningTicksDto> getEarlyWarningTicksByUserId(List<Integer> userIds, EarlyWarningTicksSearchDto earlyWarningTicksSearchDto,Page page){
        Map<String, Object> map = new HashMap<>();
        map.put("userIds", userIds);
        map.put("earlyWarningTicksSearchDto", earlyWarningTicksSearchDto);
        map.put("page", page);
        return earlyWarningTaskMapper.getEarlyWarningTicksDtoByUserIdsListPage(map);
    }
    public  List<EarlyWarningTicksDto> getTicksFollowUpRecoredByUserId(List<Integer> userIds,EarlyWarningTicksSearchDto earlyWarningTicksSearchDto,Page page){
        Map<String, Object> map = new HashMap<>();
        map.put("userIds", userIds);
        map.put("earlyWarningTicksSearchDto", earlyWarningTicksSearchDto);
        map.put("page", page);
        return earlyWarningTaskMapper.getTicksFollowUpRecoredByUserIdListPage(map);
    }
    public List<EarlyWarningTicksDto> getEarlyWarningTicksById(String earlyWarningTaskIds){
        List<String> earlyWarningTaskIdsString= Arrays.asList(earlyWarningTaskIds.split(",").clone());
        List<Integer> earlyWarningTaskIdsList=earlyWarningTaskIdsString.stream().map(Integer::parseInt).collect(Collectors.toList());
        return earlyWarningTaskMapper.getEarlyWarningTicksDtoByIds(earlyWarningTaskIdsList);
    }
    public ResultInfo<?> SaveFollowUp(FollowUpDto followUpDto){
        for(String earlyWarningTaskFollowUpInfoString:
                followUpDto.getEarlyWarningTaskId_canTicketAmountInput_urgingTicketNumFollowUp_urgingTicketAmountFollowUp().split(",")){
            EarlyWarningTask earlyWarningTask = new EarlyWarningTask();
            earlyWarningTask.setEarlyWarningTaskId(Long.valueOf(earlyWarningTaskFollowUpInfoString.split("_")[0]));
            earlyWarningTask.setCanTicketAmount(new BigDecimal(earlyWarningTaskFollowUpInfoString.split("_")[1]));
            earlyWarningTask.setUrgingTicketNumFollowUp(new BigDecimal(earlyWarningTaskFollowUpInfoString.split("_")[2]));
            earlyWarningTask.setUrgingTicketAmountFollowUp(new BigDecimal(earlyWarningTaskFollowUpInfoString.split("_")[3]));
            earlyWarningTask.setCanTicketTime(followUpDto.getCanTicketTime());
            earlyWarningTask.setFollowUpResult(followUpDto.getFollowUpStatus());
            earlyWarningTask.setFollowUpComment(followUpDto.getReson());
            earlyWarningTask.setFollowUpNum(
                    earlyWarningTaskMapper.getFollowNumByPrimaryKey(Long.valueOf(earlyWarningTaskFollowUpInfoString.split("_")[0]))+1);
            earlyWarningTask.setFollowUpPerson(followUpDto.getFollowUpPerson().toString());
            earlyWarningTask.setFollowUpTime(followUpDto.getFollowUpTime());
            earlyWarningTask.setUpdateTime(followUpDto.getFollowUpTime());
            earlyWarningTask.setUpdator(followUpDto.getFollowUpPerson());
            earlyWarningTaskMapper.updateByPrimaryKeySelective(earlyWarningTask);
        }
        return new ResultInfo<>(0,"跟进成功");
    }

    public List<TaskDealerDto> getAllDealerByUserIds(List<Integer> userIds) {
       return earlyWarningTaskMapper.getAllDealerByUserIds(userIds);
    }

    public void renovateExpeditingTicketStatus() throws ParseException {
        List<EarlyWarningTask> earlyWarningTaskList = earlyWarningTaskMapper.getAllNotDeleteAndNotSendOver48HoursMessageTask();
        for(EarlyWarningTask earlyWarningTask : earlyWarningTaskList){

            if (earlyWarningTask.getBusinessExtra1().contains("CGFY")) {
                continue;
            }
            //超过48小时发消息，改变任务状态
            if(new BigDecimal(48).compareTo(DateUtil.getHoursNumFromTimeString(earlyWarningTask.getAddTime()))<=0){
                //改变任务状态为超过48小时
                earlyWarningTask.setTaskStatus(EarlyWarningTaskStatusEnum.OVER_MATURE_48HOURS.getCode());
                try {
                    sendMessageForWarningtaskOver48Hours(earlyWarningTask);
                } catch (Exception e) {
                    log.error("sendMessageForWarningtaskOver48Hours error:",e);
                }
            }
        }
    }
    private void sendMessageForWarningtaskOver48Hours(EarlyWarningTask earlyWarningTask){
        SendMessageDto sendMessageDto = new SendMessageDto();
        sendMessageDto.setEarlyWarningTask(earlyWarningTask);
        expeditingTicketsOver48HoursMessageSender.sendMesssage(sendMessageDto);
        earlyWarningTask.setSendOver48hoursMessage(true);
        earlyWarningTaskMapper.updateByPrimaryKeySelective(earlyWarningTask);
    }
    public void updateEarlyWarningTicketTask(BigDecimal thisInvoiceTicketCount,Integer buyorderGoodsId) {
        List <EarlyWarningTask> earlyWarningTaskList =
                earlyWarningTaskMapper.getEarlyWarningTicketTaskByBuyorderGoodId(buyorderGoodsId);

        BuyorderGoods buyorderGoods = this.buyorderGoodsMapper.selectByPrimaryKey(buyorderGoodsId);
        for(EarlyWarningTask earlyWarningTask : earlyWarningTaskList){
            if(thisInvoiceTicketCount.compareTo(BigDecimal.ZERO) == 1){
                earlyWarningTask.setUpdateTime(DateUtil.getNowDate(DateUtil.TIME_FORMAT));
                if((thisInvoiceTicketCount.subtract(BigDecimal.valueOf(earlyWarningTask.getUrgingTicketNum())).add(earlyWarningTask.getAlreadyInputNum())).compareTo(BigDecimal.ZERO) >= 0){
                    thisInvoiceTicketCount = thisInvoiceTicketCount.subtract(BigDecimal.valueOf(earlyWarningTask.getUrgingTicketNum())).add(earlyWarningTask.getAlreadyInputNum());
                    earlyWarningTask.setAlreadyInputNum(BigDecimal.valueOf(earlyWarningTask.getUrgingTicketNum()));
                    earlyWarningTask.setAlreadyInputAmout(earlyWarningTask.getUrgingTicketAmount());
                    earlyWarningTask.setIsDeleted(1);
                    earlyWarningTaskMapper.updateByPrimaryKeySelective(earlyWarningTask);
                }else {
                    earlyWarningTask.setAlreadyInputNum(earlyWarningTask.getAlreadyInputNum().add(thisInvoiceTicketCount));
                    /**
                     * 根据关联业务单号和订单商品id 查询商品价格
                     */
                    BigDecimal price = buyorderExpenseItemApiService.getBuyOrderExpenseByOrderNoAndBuyorderExpenseItemId(earlyWarningTask.getBusinessExtra1(),buyorderGoodsId);
                    if(Objects.nonNull(price)){
                        //采购费用单
                        earlyWarningTask.setAlreadyInputAmout(price.multiply(earlyWarningTask.getAlreadyInputNum()));
                    }else {
                        //采购单
                        earlyWarningTask.setAlreadyInputAmout(buyorderGoods.getPrice().multiply(earlyWarningTask.getAlreadyInputNum()));
                    }
                    earlyWarningTaskMapper.updateByPrimaryKeySelective(earlyWarningTask);
                    break;
                }
            }else{
                break;
            }
        }
    }
}
