<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.dwh.externaldb.dao.DwhErpUserInfoMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.dwh.model.dto.DwhErpUserDto">
        <id column="VD_ODS_PK" jdbcType="BIGINT" property="vdOdsPk"/>
        <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="END_TIME" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="USER_ID" jdbcType="INTEGER" property="userId"/>
        <result column="ORG_ID" jdbcType="INTEGER" property="orgId"/>
        <result column="USERNAME" jdbcType="VARCHAR" property="username"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="POSITION_ID" jdbcType="INTEGER" property="positionId"/>
        <result column="POSITION_NAME" jdbcType="VARCHAR" property="positionName"/>
        <result column="POSITION_TYPE" jdbcType="INTEGER" property="positionType"/>
        <result column="POSITION_LEVEL" jdbcType="INTEGER" property="positionLevel"/>
        <result column="IS_ADMIN" jdbcType="INTEGER" property="isAdmin"/>
        <result column="IS_DISABLED" jdbcType="INTEGER" property="isDisabled"/>
        <result column="NUMBER" jdbcType="INTEGER" property="number"/>
        <result column="SYSTEM" jdbcType="VARCHAR" property="system"/>
        <result column="PARENT_ID" jdbcType="INTEGER" property="parentId"/>
        <result column="SEX" jdbcType="INTEGER" property="sex"/>
        <result column="MOBILE" jdbcType="VARCHAR" property="mobile"/>
        <result column="REAL_NAME" jdbcType="VARCHAR" property="realName"/>
        <result column="EMAIL" jdbcType="VARCHAR" property="email"/>
        <result column="TELEPHONE" jdbcType="VARCHAR" property="telephone"/>
        <result column="L1_ID" jdbcType="INTEGER" property="l1Id"/>
        <result column="L1_NAME" jdbcType="VARCHAR" property="l1Name"/>
        <result column="L2_ID" jdbcType="INTEGER" property="l2Id"/>
        <result column="L2_NAME" jdbcType="VARCHAR" property="l2Name"/>
        <result column="L3_ID" jdbcType="INTEGER" property="l3Id"/>
        <result column="L3_NAME" jdbcType="VARCHAR" property="l3Name"/>
    </resultMap>
    <sql id="Base_Column_List">
    VD_ODS_PK, START_TIME, END_TIME, USER_ID, ORG_ID, USERNAME, COMPANY_ID, POSITION_ID,
    POSITION_NAME, POSITION_TYPE, POSITION_LEVEL, IS_ADMIN, IS_DISABLED, `NUMBER`, `SYSTEM`,
    PARENT_ID, SEX, MOBILE, REAL_NAME, EMAIL, TELEPHONE, L1_ID, L1_NAME, L2_ID, L2_NAME,
    L3_ID, L3_NAME
  </sql>

    <select id="getErpUserSnapshotInfo" resultType="com.vedeng.dwh.model.dto.DwhErpUserDto">
        select u.USER_ID   as userId,
           u.USERNAME      as username,
           u.ORG_ID        as orgId,
           u.L1_ID         as l1Id,
           u.L1_NAME       as l1Name,
           u.L2_ID         as l2Id,
           u.L2_NAME       as l2Name,
           u.L3_ID         as l3Id,
           u.L3_NAME       as l3Name,
           u.POSITION_ID   as positionId,
           u.POSITION_NAME as positionName,
           u.PARENT_ID     as parentName,
           u.IS_ADMIN      as isAdmin
        from DWM_NJ_USER_ORG_DF u
        where u.START_TIME &lt;= #{startTime,jdbcType=TIMESTAMP}
        and u.END_TIME &gt; #{endTime,jdbcType=TIMESTAMP}
        and u.IS_DISABLED = 0
        and u.COMPANY_ID = 1
        and u.USER_ID = #{userId,jdbcType=INTEGER}
        limit 1
    </select>

    <!-- 获取全量用户信息 -->
    <select id="getErpUserSnapshotInfoList" resultType="com.vedeng.dwh.model.dto.DwhErpUserDto">
       select u.USER_ID    as userId,
           u.USERNAME      as username,
           u.ORG_ID        as orgId,
           u.L1_ID         as l1Id,
           u.L1_NAME       as l1Name,
           u.L2_ID         as l2Id,
           u.L2_NAME       as l2Name,
           u.L3_ID         as l3Id,
           u.L3_NAME       as l3Name,
           u.POSITION_ID   as positionId,
           u.POSITION_NAME as positionName,
           u.PARENT_ID     as parentName,
           u.IS_ADMIN      as isAdmin
        from DWM_NJ_USER_ORG_DF u
        where u.START_TIME &lt;= #{startTime,jdbcType=TIMESTAMP}
        and u.END_TIME &gt; #{endTime,jdbcType=TIMESTAMP}
        and u.IS_DISABLED = 0
        and u.COMPANY_ID = 1
        <if test="orgIds != null">
            and u.ORG_ID in
            <foreach collection="orgIds" item="orgIds" open="(" close=")" index="index" separator=",">
                #{orgIds,jdbcType=INTEGER}
            </foreach>
        </if>
    </select>
</mapper>