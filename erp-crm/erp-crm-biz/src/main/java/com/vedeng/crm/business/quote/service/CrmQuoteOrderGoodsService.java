package com.vedeng.crm.business.quote.service;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.crm.business.quote.domain.dto.*;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoodsNoSku;

import java.util.List;

public interface CrmQuoteOrderGoodsService {

   void deleteByPrimaryKey(Integer quoteorderId, CurrentUser currentUser);

   CrmQuoteorderGoods insertSelectiveDto(QuoteGoodsInsertRequestDto record, CurrentUser currentUser);

   CrmQuoteorderGoods insertSelective(CrmQuoteorderGoods crmQuoteorderGoods, CurrentUser currentUser);



   /**
    * 新增报价自定义商品
    * @param crmQuoteorderGoods
    * @param currentUser
    * @return
    */
   public CrmQuoteorderGoods insertQuoteOrderGoodsNoSku(CrmQuoteorderGoodsNoSku crmQuoteorderGoods, CurrentUser currentUser);

   /**
    * 更新报价自定义商品
    * @param crmQuoteorderGoods
    * @param currentUser
    * @return
    */
   public CrmQuoteorderGoods updateQuoteOrderGoodsNoSku(CrmQuoteorderGoodsNoSku crmQuoteorderGoods, CurrentUser currentUser);

   void updateSelective(CrmQuoteorderGoods crmQuoteorderGoods, CurrentUser currentUser);
   int updateDeliveryCycle(QuoteGoodsUpdateLockDto updateRequestDto, CurrentUser currentUser);
   int updatePrice(QuoteGoodsUpdateLockDto updateRequestDto, CurrentUser currentUser);

   int updateNum(QuoteGoodsUpdateLockDto updateRequestDto, CurrentUser currentUser);

   int updateReportStatus(QuoteGoodsUpdateLockDto updateRequestDto, CurrentUser currentUser);

   List<CrmQuoteorderGoods> selectByQuoteorderId(Integer quoteorderId);

   List<CrmQuoteorderGoods> selectByQuoteNeedsIdList(List<Long> quoteorderNeedsIdList);

   void batchUpdateConsultationReport(ConsultationReportDto consultationReportDto,CurrentUser currentUser);

   CrmQuoteorderGoods selectById(Integer quoteorderGoodsId);

   void cleanConsultationContent(CleanConsultationContentDto cleanConsultationContentDto);
}
