$(function(){

	$("#searchSpan").click(function(){
		checkLogin();
		$("#search").submit();
	})

});

function deleteContractPrice(id){
	checkLogin();
	layer.confirm("确认删除当前客户协议价格？", {
		btn: ['确定','取消'] //按钮
	}, function(){
		$.ajax({
			url:page_url+"/price/contract/deleteContractPrice.do",
			data:{'id':id},
			type:"POST",
			dataType : "json",
			async: false,
			success:function(data)
			{
				if(data.code==0){
					layer.alert("删除成功", {
						icon : 1
					}, function() {
						window.location.reload();
					});
				}else{
					layer.alert(message);
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});

	}, function(){
	});

	return false;
}
