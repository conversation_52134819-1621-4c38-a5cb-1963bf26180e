package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.aftersale.dto.BuyOrderAfterSalesDto;
import com.vedeng.erp.aftersale.service.BuyorderAfterSalesApiService;
import com.vedeng.erp.finance.dto.PayVedengBankDto;
import com.vedeng.erp.finance.service.PayVedengBankApiService;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto;
import com.vedeng.erp.kingdee.batch.repository.BatchBankBillDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchCapitalBillDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeePayRefundBillDto;
import com.vedeng.erp.kingdee.dto.KingDeePayRefundEntryDto;
import com.vedeng.erp.kingdee.enums.KingDeeBankTagEnum;
import com.vedeng.erp.kingdee.enums.KingDeePayBillUseTypeEnums;
import com.vedeng.erp.trader.dto.TraderSupplierDto;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 已结算 付款退款单 构造器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/24 16:59
 */
@Service
@Slf4j
public class SettledPayRefundBillProcessor extends BaseProcessor<BatchBankBillDto, KingDeePayRefundBillDto> {

    @Autowired
    private BuyorderAfterSalesApiService buyorderAfterSalesApiService;

    @Autowired
    private TraderSupplierApiService traderSupplierApiService;

    @Autowired
    private PayVedengBankApiService payVedengBankApiService;

    @Autowired
    private BatchBankBillDtoMapper batchBankBillDtoMapper;

    @Autowired
    private BatchCapitalBillDtoMapper batchCapitalBillDtoMapper;

    @Override
    public KingDeePayRefundBillDto doProcess(BatchBankBillDto input, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("开始处理已结算付款退款单,{}", JSONObject.toJSONString(input));
        Optional<TraderSupplierDto> supplier = Optional.ofNullable(traderSupplierApiService.getTraderSupplierByTraderId(input.getTraderId()));
        return (KingDeeConstant.FIVE.equals(input.getOrderType()) || this.judgeType(input)) && supplier.isPresent() ?
                supplier.map(s -> this.constructResult(input, s))
                        .orElseGet(() -> {
                            log.info("当前流水不满足条件：{}", JSONObject.toJSONString(input));
                            return null;
                        }) : null;
    }

    /**
     * 判断是否时采购的售后流水
     *
     * @param input BatchBankBillDto
     * @return true：是BD_Supplier； false：不是
     */
    private boolean judgeType(BatchBankBillDto input) {
        return KingDeeConstant.THREE.equals(input.getOrderType())
                && Optional.ofNullable(buyorderAfterSalesApiService.getByAfterSalesId(input.getRelatedId()))
                .map(BuyOrderAfterSalesDto::getSubjectType)
                .filter(KingDeeConstant.PURCHASE::equals)
                .isPresent();
    }


    /**
     * 构造返回数据
     *
     * @param input       BatchBankBillDto
     * @param supplierDto TraderSupplierDto
     * @return KingDeePayRefundBillDto
     */
    private KingDeePayRefundBillDto constructResult(BatchBankBillDto input, TraderSupplierDto supplierDto) {
        KingDeePayRefundBillDto result = new KingDeePayRefundBillDto();
        result.setFContactUnit(supplierDto.getTraderSupplierId().toString());
        result.setFId("0");
        result.setFBillNo(KingDeeConstant.SETTLE_ACCOUNT + input.getBankBillId().toString());
        result.setFContactUnitType(KingDeeConstant.BD_SUPPLIER);
//        result.setFDate(DateUtil.formatDate(input.getTranDate()));
        result.setFDate(DateUtil.formatDateTime(input.getRealTrandatetime()));//VDERP-16088  款对接金蝶业务日期规则调整-精确到时分秒
        result.setFPayUnitType(KingDeeConstant.BD_SUPPLIER);
        result.setFQzokLsh(input.getTranFlow());
        result.setFQzokPzgsywdh(input.getOrderNo());//VDERP-16088  款对接金蝶业务日期规则调整-精确到时分秒

        PayVedengBankDto payVedengBankDto = payVedengBankApiService.queryBankInfo(input.getBankTag());
        if (Objects.isNull(payVedengBankDto)) {
            log.info("未查询到已结算流水的银行信息：{}", JSONObject.toJSONString(input));
            return null;
        }

        // 查询银行流水关联的所有资金流水
        List<BatchCapitalBillDto> capitalBillDtoList = batchCapitalBillDtoMapper.queryByTraderTypeAndBankBillId(KingDeeConstant.ONE, input.getBankBillId());
        if (CollUtil.isEmpty(capitalBillDtoList)) {
            log.error("已结算付款退款单银行流水推送金蝶processor执行失败，银行流水关联的资金流水不存在：{}", JSON.toJSONString(input));
            return null;
        }

        List<KingDeePayRefundEntryDto> kingDeePayRefundEntryDtos = new ArrayList<>();

        for (BatchCapitalBillDto batchCapitalBillDto : capitalBillDtoList) {
            KingDeePayRefundEntryDto entryDto = new KingDeePayRefundEntryDto();
            entryDto.setFsettletypeid(KingDeeBankTagEnum.matchFSettleTypeIdByCode(input.getBankTag()));
            entryDto.setFpurposeid(KingDeePayBillUseTypeEnums.BUY_PAY.getCode());
            entryDto.setFrefundamountfor(batchCapitalBillDto.getAmount());
            entryDto.setFaccountid(payVedengBankDto.getPayBankNo());
            entryDto.setFQzokGsywdh(batchCapitalBillDto.getOrderNo());
            entryDto.setFQzokYwlx(KingDeeConstant.PURCHASE_AFTER_SALES_PAYMENT);
            entryDto.setFhandlingchargefor(BigDecimal.ZERO);

            kingDeePayRefundEntryDtos.add(entryDto);
        }

        result.setFrefundbillentry(kingDeePayRefundEntryDtos);
        log.info("已结算付款退款单构造完成：{}", JSONObject.toJSONString(result));
        return result;
    }
}