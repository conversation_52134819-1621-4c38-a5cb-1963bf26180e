package com.vedeng.flash.enums;

public enum WmsSendOrderTypeEnum {
    PUTSALEORDER(0 , "下发销售单"),
    PUTBUYORDER(1, "下发采购单"),
    PUTSALEORDERMODIFY(2, "下发销售单修改"),
    PUTSALESORDERRETURN(3,"销售售后退货"),
    PUTBUYORDERRETURN(4,"采购售后退货"),
    PUTSALESORDEREXCHANGE(5,"销售售后换货"),
    PUTBUYORDEREXCHANGE(6,"采购售后换货"),
    PUTSALEORDERMODIFYCHOOSE(7,"销售单修改重新下发"),
    PUT_DELIVERY_DIRECT_SALEORDER(8,"下发直发销售单"),
    PUT_DELIVERY_DIRECT_BUYORDER(9,"下发直发采购单"),
    INVENTORY_OUT(10,"下发盘亏出库单"),
    UNIT_CONVERSION_OUT(11,"单位转换单出库"),
    UNIT_CONVERSION_IN(12,"单位转换单入库"),
    SAMPLE_OUT(13,"样品单出库"),
    LEND_OUT(14,"外借单出库")
    ;

    WmsSendOrderTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;

    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据编码获取编码信息
     * @param code
     * @return
     */
    public static String codeToMessage(Integer code){
        if(code == null){
            return "";
        }
        for(DealTypeEnum enums : DealTypeEnum.values()){
            if(code.equals(enums.getCode())){
                return enums.getMsg();
            }
        }
        return "";
    }
}
