package com.vedeng.activiti.taskassign;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.xml.ws.WebServiceContext;

import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDetailDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDetailDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import com.vedeng.infrastructure.esign.handle.AbstractElectronicSignHandle;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.finance.model.TraderAccountPeriodApply;
import com.vedeng.order.model.BuyorderModifyApply;
import com.vedeng.order.model.SaleorderModifyApply;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.order.service.SaleorderService;

public class EditBuyExecutionListener implements ExecutionListener {

	private static final Logger LOGGER = LoggerFactory.getLogger(TaskValidExecutionListener.class);
    // 运行时注入service
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private BuyorderService buyorderService = (BuyorderService) context.getBean("buyorderService");
    private BuyorderGoodsMapper buyorderGoodsMapper = (BuyorderGoodsMapper) context.getBean("buyorderGoodsMapper");

    private ActionProcdefService actionProcdefService = (ActionProcdefService) context.getBean("actionProcdefService");

    private BuyorderExpenseApiService buyorderExpenseApiService = (BuyorderExpenseApiService)context.getBean("buyorderExpenseServiceImpl");

	/**
	 * 采购订单电子签章实习类
	 */
	private final AbstractElectronicSignHandle buyOrderElectronicSignHandle = (AbstractElectronicSignHandle) context.getBean("buyOrderElectronicSignHandle");
    @Resource
    private WebServiceContext webServiceContext;
    //修改订单审核触发器
    //根据穿参通用回写主表中状态
    public void notify(DelegateExecution execution) throws Exception {
		ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request =  ra.getRequest();
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		BuyorderModifyApply buyorderModifyApply = (BuyorderModifyApply) execution.getVariable("buyorderModifyApplyInfo");
		String a = execution.getCurrentActivityName();

		String processDefinitionKey = (String) execution.getVariable("processDefinitionKey");


		if(execution.getCurrentActivityName().equals("审核完成")){
		    buyorderModifyApply.setValidStatus(1);
		    buyorderModifyApply.setValidTime(DateUtil.sysTimeMillis());
		    ResultInfo<?> res = buyorderService.saveApplyBuyorderModfiyValidStatus(buyorderModifyApply);
		    actionProcdefService.updateInfo("T_BUYORDER", "BUYORDER_ID", buyorderModifyApply.getBuyorderId(), "LOCKED_STATUS", 0,2);
		    // db不能发站内信 所以回传采购商品集合
		    if(res != null && res.getCode().equals(Integer.valueOf(0))){
				Map<String,Object> sendMsgMap = (Map<String,Object>)res.getData();
				if((Integer)sendMsgMap.get("res") > 0) {
					List<Integer> buyOrderGoodsIdList = (List<Integer>)sendMsgMap.get("buyOrderGoodsIdList");
					for (Integer buyOrderGoodsIdKey : buyOrderGoodsIdList) {
						// 获取当前采购商品对应采购单信息
						Buyorder buyOrderDetail = buyorderGoodsMapper.selectBuyorderByBuyOrderGoodsId(buyOrderGoodsIdKey);
						if (buyOrderDetail == null) {
							continue;
						}
						// 过滤 非VP、 对应销售单商品已经全部发货
						List<Saleorder> validSaleOrderNos = buyorderGoodsMapper.selectValidSaleOrderByBuyOrderGoodsId(Integer.valueOf(buyOrderGoodsIdKey));
						if (validSaleOrderNos == null || validSaleOrderNos.size() == 0) {
							continue;
						}

						Map<String, String> mapParams = new HashMap<>();
						for (Saleorder saleOrderItem : validSaleOrderNos) {
							// 根据销售单编码查找对应销售
							List<Integer> saleOrderTraderIds = buyorderGoodsMapper.selectSaleOrderTraderIdsBySaleOrderId(saleOrderItem.getSaleorderId());
							saleOrderTraderIds = saleOrderTraderIds.stream().filter(ObjectUtils::notEmpty).collect(Collectors.toList());
							if(!ObjectUtils.allNotNull(saleOrderItem)){
								continue;
							}

							mapParams.put("saleOrderNo", saleOrderItem.getSaleorderNo());
							mapParams.put("buyOrderNo", buyOrderDetail.getBuyorderNo());
							MessageUtil.sendMessage2(170,saleOrderTraderIds,mapParams,"./order/saleorder/view.do?saleorderId="+saleOrderItem.getSaleorderId().intValue(),user.getUsername());
						}
					}
				}

				// 更新直属费用单的相关信息
				BuyorderExpenseDetailDto buyorderExpenseDetailDto = new BuyorderExpenseDetailDto();
				buyorderExpenseDetailDto.setInvoiceType(buyorderModifyApply.getInvoiceType());
				buyorderExpenseDetailDto.setInvoiceComments(buyorderModifyApply.getInvoiceComments());
				buyorderExpenseApiService.updateDirectExpenseInfo(buyorderModifyApply.getBuyorderId(), buyorderModifyApply.getBuyorderModifyApplyId(), buyorderExpenseDetailDto);

			}
		}else{
			actionProcdefService.updateInfo("T_BUYORDER", "BUYORDER_ID", buyorderModifyApply.getBuyorderId(), "LOCKED_STATUS", 0,2);
		}

		// 采购单审核通过或者修改采购单审核通过后，调用电子签章
		if ("editBuyorderVerify".equals(processDefinitionKey)) {
			int buyOrderId = 0;
			try {
				BuyorderModifyApply buyorderModifyApplyInfo = (BuyorderModifyApply) execution.getVariable("buyorderModifyApplyInfo");
				buyOrderId = buyorderModifyApplyInfo.getBuyorderId();
				buyorderService.udpateContractUrlByBuyOrderId(buyOrderId,null);

				// 调用电子签章
				BusinessInfo businessInfo = new BusinessInfo();
				businessInfo.setOperator("系统");
				ElectronicSignParam electronicSignParam = buyOrderElectronicSignHandle.buildElectronicSignParam(buyOrderId,businessInfo);
				buyOrderElectronicSignHandle.electronicSign(electronicSignParam);
//				ElectronicSignParam electronicSignParam = ElectronicSignParam
//						.builder()
//						.flowType(2)
//						.buyOrderId(buyOrderId)
//						.businessInfo(businessInfo)
//						.electronicSignBusinessEnums(ElectronicSignBusinessEnums.BUY_ORDER)
//						.build();
//				buyOrderElectronicSignHandle.electronicSign(electronicSignParam);
			} catch (Exception e) {
				LOGGER.error("调用电子签章异常，订单id：{}，错误：", buyOrderId, e);
			}
		}
    }
}
/**
 * 
 * <AUTHOR>
 *
 */
