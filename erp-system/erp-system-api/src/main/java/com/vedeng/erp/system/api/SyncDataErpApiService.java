package com.vedeng.erp.system.api;

import com.vedeng.erp.system.dto.SyncDataErpDto;

import java.util.List;

public interface SyncDataErpApiService {

    boolean checkBuyOrderFromStandard(String buyOrderNo);
    
    /**
     * 根据ID删除同步数据
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入同步数据
     */
    int insert(SyncDataErpDto record);

    /**
     * 选择性插入同步数据
     */
    int insertSelective(SyncDataErpDto record);

    /**
     * 根据ID查询同步数据
     */
    SyncDataErpDto selectByPrimaryKey(Integer id);

    /**
     * 选择性更新同步数据
     */
    int updateByPrimaryKeySelective(SyncDataErpDto record);

    /**
     * 更新同步数据
     */
    int updateByPrimaryKey(SyncDataErpDto record);

    /**
     * 根据处理状态查询同步数据
     */
    List<SyncDataErpDto> selectByProcessStatus(Integer processStatus,String businessType);

    /**
     * 根据业务单号查询同步数据
     */
    List<SyncDataErpDto> selectByBusinessNo(String businessNo);
} 