package com.vedeng.order.model;

import java.io.Serializable;
import lombok.Data;

/**
 * 发货通知单
 * T_SALEORDER_DELIVERY_NOTICE
 * <AUTHOR>
public class SaleorderDeliveryNotice implements Serializable {

    /**
     * 发货通知单表主键
     */
    private Integer deliveryNoticeId;

    /**
     * 发货通知单-单号
     */
    private String deliveryNoticeNo;

    /**
     * 关联销售订单ID
     */
    private Integer orderId;

    /**
     * 关联销售订单-单号NO
     */
    private String orderNo;

    /**
     * 发货状态
     */
    private Integer deliveryStatus;
    /**
     * 发货通知单状态: 0待确认（默认）、1进行中、2已完结、3已关闭
     */
    private Integer status;

    /**
     * 发货通知单审核状态: 0待审核（默认）、1审核中、2审核通过、3审核不通过
     */
    private Integer auditStatus;
    /**
     * 审核通过的时间
     */
    private Long validTime;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建人ID
     */
    private Integer creator;
    private String creatorName;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最后一次编辑人ID
     */
    private Integer updater;

    /**
     * 交易者名称
     */
    private String traderName;

    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 联系人
     */
    private String traderContactName;

    /**
     * 手机
     */
    private String traderContactMobile;

    /**
     * 电话
     */
    private String traderContactTelephone;

    /**
     * 联系地址ID
     */
    private Integer traderAddressId;

    /**
     * 客户地区最小级ID
     */
    private Integer traderAreaId;

    /**
     * 客户地区
     */
    private String traderArea;

    /**
     * 联系详细地址(含省市区)
     */
    private String traderAddress;

    /**
     * 客户信息备注
            
     */
    private String traderComments;

    /**
     * 收货公司ID
     */
    private Integer takeTraderId;

    /**
     * 收货公司名称
     */
    private String takeTraderName;

    /**
     * 收货联系人ID
     */
    private Integer takeTraderContactId;

    /**
     * 收货联系人名称
     */
    private String takeTraderContactName;

    /**
     * 收货联系人手机
     */
    private String takeTraderContactMobile;

    /**
     * 收货联系人电话
     */
    private String takeTraderContactTelephone;

    /**
     * 收货地址ID
     */
    private Integer takeTraderAddressId;

    /**
     * 收货地区最小级ID
     */
    private Integer takeTraderAreaId;

    /**
     * 收货地区
     */
    private String takeTraderArea;

    /**
     * 收货地址
     */
    private String takeTraderAddress;

    /**
     * 运费说明 字典表
     */
    private Integer freightDescription;

    /**
     * 发货方式 字典表
     */
    private Integer deliveryType;

    /**
     * 物流公司ID
     */
    private Integer logisticsId;

    /**
     * 物流备注
     */
    private String logisticsComments;

    /**
     * 打印随货出库单  0不打印   1打印带价格出库单   2打印不带价格出库单
     */
    private Integer isPrintout;

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public Long getValidTime() {
        return validTime;
    }

    public void setValidTime(Long validTime) {
        this.validTime = validTime;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    private static final long serialVersionUID = 1L;

    public Integer getDeliveryNoticeId() {
        return deliveryNoticeId;
    }

    public void setDeliveryNoticeId(Integer deliveryNoticeId) {
        this.deliveryNoticeId = deliveryNoticeId;
    }

    public String getDeliveryNoticeNo() {
        return deliveryNoticeNo;
    }

    public void setDeliveryNoticeNo(String deliveryNoticeNo) {
        this.deliveryNoticeNo = deliveryNoticeNo;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public Integer getTraderContactId() {
        return traderContactId;
    }

    public void setTraderContactId(Integer traderContactId) {
        this.traderContactId = traderContactId;
    }

    public String getTraderContactName() {
        return traderContactName;
    }

    public void setTraderContactName(String traderContactName) {
        this.traderContactName = traderContactName;
    }

    public String getTraderContactMobile() {
        return traderContactMobile;
    }

    public void setTraderContactMobile(String traderContactMobile) {
        this.traderContactMobile = traderContactMobile;
    }

    public String getTraderContactTelephone() {
        return traderContactTelephone;
    }

    public void setTraderContactTelephone(String traderContactTelephone) {
        this.traderContactTelephone = traderContactTelephone;
    }

    public Integer getTraderAddressId() {
        return traderAddressId;
    }

    public void setTraderAddressId(Integer traderAddressId) {
        this.traderAddressId = traderAddressId;
    }

    public Integer getTraderAreaId() {
        return traderAreaId;
    }

    public void setTraderAreaId(Integer traderAreaId) {
        this.traderAreaId = traderAreaId;
    }

    public String getTraderArea() {
        return traderArea;
    }

    public void setTraderArea(String traderArea) {
        this.traderArea = traderArea;
    }

    public String getTraderAddress() {
        return traderAddress;
    }

    public void setTraderAddress(String traderAddress) {
        this.traderAddress = traderAddress;
    }

    public String getTraderComments() {
        return traderComments;
    }

    public void setTraderComments(String traderComments) {
        this.traderComments = traderComments;
    }

    public Integer getTakeTraderId() {
        return takeTraderId;
    }

    public void setTakeTraderId(Integer takeTraderId) {
        this.takeTraderId = takeTraderId;
    }

    public String getTakeTraderName() {
        return takeTraderName;
    }

    public void setTakeTraderName(String takeTraderName) {
        this.takeTraderName = takeTraderName;
    }

    public Integer getTakeTraderContactId() {
        return takeTraderContactId;
    }

    public void setTakeTraderContactId(Integer takeTraderContactId) {
        this.takeTraderContactId = takeTraderContactId;
    }

    public String getTakeTraderContactName() {
        return takeTraderContactName;
    }

    public void setTakeTraderContactName(String takeTraderContactName) {
        this.takeTraderContactName = takeTraderContactName;
    }

    public String getTakeTraderContactMobile() {
        return takeTraderContactMobile;
    }

    public void setTakeTraderContactMobile(String takeTraderContactMobile) {
        this.takeTraderContactMobile = takeTraderContactMobile;
    }

    public String getTakeTraderContactTelephone() {
        return takeTraderContactTelephone;
    }

    public void setTakeTraderContactTelephone(String takeTraderContactTelephone) {
        this.takeTraderContactTelephone = takeTraderContactTelephone;
    }

    public Integer getTakeTraderAddressId() {
        return takeTraderAddressId;
    }

    public void setTakeTraderAddressId(Integer takeTraderAddressId) {
        this.takeTraderAddressId = takeTraderAddressId;
    }

    public Integer getTakeTraderAreaId() {
        return takeTraderAreaId;
    }

    public void setTakeTraderAreaId(Integer takeTraderAreaId) {
        this.takeTraderAreaId = takeTraderAreaId;
    }

    public String getTakeTraderArea() {
        return takeTraderArea;
    }

    public void setTakeTraderArea(String takeTraderArea) {
        this.takeTraderArea = takeTraderArea;
    }

    public String getTakeTraderAddress() {
        return takeTraderAddress;
    }

    public void setTakeTraderAddress(String takeTraderAddress) {
        this.takeTraderAddress = takeTraderAddress;
    }

    public Integer getFreightDescription() {
        return freightDescription;
    }

    public void setFreightDescription(Integer freightDescription) {
        this.freightDescription = freightDescription;
    }

    public Integer getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(Integer deliveryType) {
        this.deliveryType = deliveryType;
    }

    public Integer getLogisticsId() {
        return logisticsId;
    }

    public void setLogisticsId(Integer logisticsId) {
        this.logisticsId = logisticsId;
    }

    public String getLogisticsComments() {
        return logisticsComments;
    }

    public void setLogisticsComments(String logisticsComments) {
        this.logisticsComments = logisticsComments;
    }

    public Integer getIsPrintout() {
        return isPrintout;
    }

    public void setIsPrintout(Integer isPrintout) {
        this.isPrintout = isPrintout;
    }
}