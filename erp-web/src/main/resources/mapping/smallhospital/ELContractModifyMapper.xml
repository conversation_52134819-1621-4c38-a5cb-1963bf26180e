<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.smallhospital.dao.ELContractModifyMapper">

    <resultMap id="BaseResultMap" type="com.smallhospital.model.ElContract">
        <id column="EL_CONTRACT_MODIFY_ID" jdbcType="INTEGER" property="elContractModifyId" />
        <result column="EL_CONTRACT_ID" jdbcType="INTEGER" property="elContractId" />
        <result column="CONTRACT_NUMBER" jdbcType="VARCHAR" property="contractNumber" />
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
        <result column="SIGN_DATE" jdbcType="BIGINT" property="signDate" />
        <result column="CONTRACT_VALIDITY_DATE_START" jdbcType="BIGINT" property="contractValidityDateStart" />
        <result column="CONTRACT_VALIDITY_DATE_END" jdbcType="BIGINT" property="contractValidityDateEnd" />
        <result column="CONTRACT_PIC" jdbcType="VARCHAR" property="contractPic" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />

        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
        <result column="UPDATE_TIME" jdbcType="BIGINT" property="updateTime" />
        <result column="CREATOR" jdbcType="INTEGER" property="creator" />
        <result column="UPDATOR" jdbcType="INTEGER" property="updator" />
        <result column="OWNER" jdbcType="INTEGER" property="owner" />
        <result column="STATUS" jdbcType="INTEGER" property="status" />
        <result column="EFFCTIVE_STATUS" jdbcType="INTEGER" property="effctiveStatus" />

        <result column="PRODUCT_SYN_STATUS" jdbcType="INTEGER" property="productSynStatus" />
        <result column="CONTRACT_SYN_STATUS" jdbcType="INTEGER" property="contractSynStatus" />

        <result column="CONFIRM_STATUS" jdbcType="INTEGER" property="confirmStatus" />

        <result column="AUDIT_STATUS" jdbcType="INTEGER" property="auditStatus" />
        <result column="AUDITER" jdbcType="INTEGER" property="auditer" />
        <result column="AUDIT_TIME" jdbcType="INTEGER" property="auditTime" />
        <result column="AUDIT_DESC" jdbcType="INTEGER" property="auditDesc" />
        <result column="TERMINATE_STATUS" jdbcType="INTEGER" property="terminateStatus" />
        <result column="MODIFY_STATUS" jdbcType="INTEGER" property="modifyStatus" />

    </resultMap>


    <sql id="Base_Column_List">
    EL_CONTRACT_MODIFY_ID, EL_CONTRACT_ID, CONTRACT_NUMBER, TRADER_ID, SIGN_DATE, CONTRACT_VALIDITY_DATE_START,
    CONTRACT_VALIDITY_DATE_END, CONTRACT_PIC, REMARK, ADD_TIME, UPDATE_TIME, CREATOR, UPDATOR ,OWNER
  </sql>



    <select id="selectByContractId" parameterType="java.lang.Integer" resultType="com.smallhospital.model.vo.ELContractVO">
        select
        <include refid="Base_Column_List" />
        from T_EL_CONTRACT_MODIFY
        where
        IS_ENABLE = 1 AND
        EL_CONTRACT_ID = #{contractId,jdbcType=INTEGER}
    </select>

    <delete id="deleteByContractId">
    update T_EL_CONTRACT_MODIFY SET IS_ENABLE=0
    where EL_CONTRACT_ID = #{contractId,jdbcType=INTEGER}
  </delete>

    <insert id="insert" keyColumn="EL_CONTRACT_MODIFY_ID" keyProperty="elContractModifyId" parameterType="com.smallhospital.model.ElContract" useGeneratedKeys="true">
    insert into T_EL_CONTRACT_MODIFY (CONTRACT_NUMBER, TRADER_ID, SIGN_DATE,
      CONTRACT_VALIDITY_DATE_START, CONTRACT_VALIDITY_DATE_END,
      CONTRACT_PIC, REMARK,
      ADD_TIME, UPDATE_TIME,CREATOR, UPDATOR,OWNER,STATUS
      )
    values (#{contractNumber,jdbcType=VARCHAR}, #{traderId,jdbcType=INTEGER}, #{signDate,jdbcType=BIGINT},
      #{contractValidityDateStart,jdbcType=BIGINT}, #{contractValidityDateEnd,jdbcType=BIGINT},
      #{contractPic,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
      #{addTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT},#{creator,jdbcType=INTEGER}, #{updator,jdbcType=INTEGER},#{owner,jdbcType=INTEGER},#{status,jdbcType=INTEGER}
      )
  </insert>
    <insert id="insertSelective" keyColumn="EL_CONTRACT_MODIFY_ID" keyProperty="elContractModifyId" parameterType="com.smallhospital.model.ElContract" useGeneratedKeys="true">
        insert into T_EL_CONTRACT_MODIFY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="elContractId != null">
                EL_CONTRACT_ID,
            </if>
            <if test="contractNumber != null">
                CONTRACT_NUMBER,
            </if>
            <if test="traderId != null">
                TRADER_ID,
            </if>
            <if test="signDate != null">
                SIGN_DATE,
            </if>
            <if test="contractValidityDateStart != null">
                CONTRACT_VALIDITY_DATE_START,
            </if>
            <if test="contractValidityDateEnd != null">
                CONTRACT_VALIDITY_DATE_END,
            </if>
            <if test="contractPic != null">
                CONTRACT_PIC,
            </if>
            <if test="remark != null">
                REMARK,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updator != null">
                UPDATOR,
            </if>
            <if test="owner != null">
                OWNER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="elContractId != null">
                #{elContractId,jdbcType=INTEGER},
            </if>
            <if test="contractNumber != null">
                #{contractNumber,jdbcType=VARCHAR},
            </if>
            <if test="traderId != null">
                #{traderId,jdbcType=INTEGER},
            </if>
            <if test="signDate != null">
                #{signDate,jdbcType=BIGINT},
            </if>
            <if test="contractValidityDateStart != null">
                #{contractValidityDateStart,jdbcType=BIGINT},
            </if>
            <if test="contractValidityDateEnd != null">
                #{contractValidityDateEnd,jdbcType=BIGINT},
            </if>
            <if test="contractPic != null">
                #{contractPic,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updator != null">
                #{updator,jdbcType=INTEGER},
            </if>
            <if test="owner != null">
                #{owner,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.smallhospital.model.vo.ELContractVO">
        update T_EL_CONTRACT_MODIFY
        <set>
            <if test="contractNumber != null">
                CONTRACT_NUMBER = #{contractNumber,jdbcType=VARCHAR},
            </if>
            <if test="traderId != null">
                TRADER_ID = #{traderId,jdbcType=INTEGER},
            </if>
            <if test="signDate != null">
                SIGN_DATE = #{signDate,jdbcType=BIGINT},
            </if>
            <if test="contractValidityDateStart != null">
                CONTRACT_VALIDITY_DATE_START = #{contractValidityDateStart,jdbcType=BIGINT},
            </if>
            <if test="contractValidityDateEnd != null">
                CONTRACT_VALIDITY_DATE_END = #{contractValidityDateEnd,jdbcType=BIGINT},
            </if>
            <if test="contractPic != null">
                CONTRACT_PIC = #{contractPic,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="owner != null">
                OWNER = #{owner,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updator != null">
                UPDATOR = #{updator,jdbcType=INTEGER},
            </if>
        </set>
        where EL_CONTRACT_MODIFY_ID = #{elContractModifyId,jdbcType=INTEGER}
    </update>
</mapper>