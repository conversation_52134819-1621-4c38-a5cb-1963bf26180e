/* 线索/商机 卡片 */

Vue.component('ui-business-card', {
    template: `
        <div class="business-card">
            <!-- 线索 -->
            <div class="business-leads" v-if="type == 1">
                <div class="business-top">
                    <div class="row">
                        <div class="business-trader">
                            <div class="trader-name">{{ businessInfo.traderName }}</div>
                            <i v-if="businessInfo.tycFlag == 'Y'" @click.stop="openTyc" class="vd-ui_icon icon-tianyancha"></i>
                        </div>
                        <div class="business-status">
                            <div class="leads-status" :class="'s'+businessInfo.followStatus">{{ businessInfo.followStatusStr }}</div>
                        </div>
                    </div>
                    <div class="row mt">
                        <div class="business-no">{{ businessInfo.leadsNo }}</div>
                        <div class="business-time">{{ businessInfo.createTime }}</div>
                    </div>
                </div>
                <div class="business-bottom">
                    <div class="business-attrs">
                        <div class="item-attr">
                            <span class="label">归属销售：</span>
                            <span class="value" v-if="businessInfo.belonger || businessInfo.belongerPic">
                                <ui-user 
                                    :name="businessInfo.belonger" :avatar="businessInfo.belongerPic" 
                                    @click="openChat(businessInfo.belongerId)"
                                ></ui-user>
                            </span>
                            <span class="value" v-else>-</span>
                        </div>
                        <div class="item-attr">
                            <span class="label">来源：</span>
                            <span class="value">{{ businessInfo.clueTypeName || '-' }}</span>
                        </div>
                    </div>
                    <div class="trader-check" v-if="traderCheck && traderName !== businessInfo.traderName">
                        <ui-tip>线索客户与当前拜访客户不一致</ui-tip>
                    </div>
                </div>
            </div>

            <!-- 商机 -->
            <div class="business-change" v-else-if="type == 2">
                <div class="business-top">
                    <div class="row">
                        <div class="business-trader">
                            <div class="trader-name">{{ businessInfo.traderName }}</div>
                            <i v-if="businessInfo.tycFlag == 'Y'" @click.stop="openTyc" class="vd-ui_icon icon-tianyancha"></i>
                        </div>
                        <div class="business-status">
                            <div class="change-status" :class="'s'+businessInfo.stage">{{ businessInfo.stageStr }}</div>
                        </div>
                    </div>
                    <div class="row mt">
                        <div class="business-no">{{ businessInfo.bussinessChanceNo }}</div>
                        <div class="business-time">{{ businessInfo.createTime }}</div>
                    </div>
                </div>
                <div class="business-bottom">
                    <div class="business-attrs">
                        <div class="item-attr">
                            <span class="label">归属销售：</span>
                            <span class="value" v-if="businessInfo.belonger || businessInfo.belongPic">
                                <ui-user 
                                    :name="businessInfo.belonger" :avatar="businessInfo.belongPic"
                                    @click="openChat(businessInfo.belongerId)"
                                ></ui-user>
                            </span>
                            <span class="value" v-else>-</span>
                        </div>
                        <div class="item-attr">
                            <span class="label">业务类型：</span>
                            <span class="value">{{ businessInfo.businessTypeName || '-' }}</span>
                        </div>
                        <div class="item-attr">
                            <span class="label">预计金额：</span>
                            <span class="value" :class="{'price-light': businessInfo.amount}">{{ businessInfo.amount || '-' }}</span>
                        </div>
                        <div class="item-attr">
                            <span class="label">预计成单：</span>
                            <span  class="value">{{ businessInfo.orderTime || '-' }}</span>
                        </div>
                    </div>
                    <div class="pro-name" v-if="businessInfo.productCommentsSale">{{ businessInfo.productCommentsSale }}</div>
                    <div class="trader-check" v-if="traderCheck && traderName !== businessInfo.traderName">
                        <ui-tip>商机客户与当前拜访客户不一致</ui-tip>
                    </div>
                </div>
            </div>
            
            <ui-tyc-detail ref="tycDetail"></ui-tyc-detail>
        </div>
    `,
    props: {
        businessInfo: {
            type: Object
        },
        type: {
            type: String,
            default: '1' // 1:线索 2:商机
        },
        // 验证客户是否一致
        traderCheck: {
            type: Boolean,
            default: false,
        },
        traderName: String,
    },
    data() {
        return {
           
        }
    },
    created() {
    },
    methods: {
        openTyc () {
            this.$refs.tycDetail.show(this.businessInfo.traderName);
        },
        /* 打开企微聊天框 */
        openChat (userid, qwUserid) {
            if (qwUserid) {
                GLOBAL.openQwChat(this, qwUserid, true);
            } else {
                GLOBAL.openQwChat(this, userid, false);
            }
        },
    }
})