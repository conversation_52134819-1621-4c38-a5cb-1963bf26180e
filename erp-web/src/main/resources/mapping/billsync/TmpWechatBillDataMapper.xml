<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.billsync.dao.TmpWechatBillDataMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.billsync.task.model.entity.generate.TmpWechatBillDataDo">
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="OCCURRENCE_TIME" jdbcType="TIMESTAMP" property="occurrenceTime" />
    <result column="PUBLIC_ID" jdbcType="VARCHAR" property="publicId" />
    <result column="MERCHANT_NO" jdbcType="VARCHAR" property="merchantNo" />
    <result column="SPECIAL_MERCHANT_NO" jdbcType="VARCHAR" property="specialMerchantNo" />
    <result column="DEVICE_NO" jdbcType="VARCHAR" property="deviceNo" />
    <result column="WX_ORDER_NO" jdbcType="VARCHAR" property="wxOrderNo" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="BUSINESS_TYPE" jdbcType="VARCHAR" property="businessType" />
    <result column="BUSINESS_STATUS" jdbcType="VARCHAR" property="businessStatus" />
    <result column="BANK" jdbcType="VARCHAR" property="bank" />
    <result column="CURRENCY" jdbcType="VARCHAR" property="currency" />
    <result column="ORDER_AMOUNT_DUE" jdbcType="DECIMAL" property="orderAmountDue" />
    <result column="COUPON_AMOUNT" jdbcType="DECIMAL" property="couponAmount" />
    <result column="WX_REFUND_NO" jdbcType="VARCHAR" property="wxRefundNo" />
    <result column="REFUND_NO" jdbcType="VARCHAR" property="refundNo" />
    <result column="REFUND_AMOUNT" jdbcType="DECIMAL" property="refundAmount" />
    <result column="RECHARGE_REFUND_AMOUNT" jdbcType="DECIMAL" property="rechargeRefundAmount" />
    <result column="REFUND_TYPE" jdbcType="VARCHAR" property="refundType" />
    <result column="REFUND_STATUS" jdbcType="VARCHAR" property="refundStatus" />
    <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName" />
    <result column="DATA_PACK" jdbcType="VARCHAR" property="dataPack" />
    <result column="HANDLING_FEE" jdbcType="DECIMAL" property="handlingFee" />
    <result column="RATE" jdbcType="VARCHAR" property="rate" />
    <result column="ORDER_AMOUNT" jdbcType="DECIMAL" property="orderAmount" />
    <result column="REQ_REFUND_AMOUNT" jdbcType="DECIMAL" property="reqRefundAmount" />
    <result column="SYNC_TIME" jdbcType="DATE" property="syncTime" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, OCCURRENCE_TIME, PUBLIC_ID, MERCHANT_NO, SPECIAL_MERCHANT_NO, DEVICE_NO, WX_ORDER_NO, 
    ORDER_NO, USER_ID, BUSINESS_TYPE, BUSINESS_STATUS, BANK, CURRENCY, ORDER_AMOUNT_DUE, 
    COUPON_AMOUNT, WX_REFUND_NO, REFUND_NO, REFUND_AMOUNT, RECHARGE_REFUND_AMOUNT, REFUND_TYPE, 
    REFUND_STATUS, SKU_NAME, DATA_PACK, HANDLING_FEE, RATE, ORDER_AMOUNT, REQ_REFUND_AMOUNT, 
    SYNC_TIME, ADD_TIME, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from TMP_WECHAT_BILL_DATA
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from TMP_WECHAT_BILL_DATA
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.billsync.task.model.entity.generate.TmpWechatBillDataDo" useGeneratedKeys="true">
    insert into TMP_WECHAT_BILL_DATA (OCCURRENCE_TIME, PUBLIC_ID, MERCHANT_NO, 
      SPECIAL_MERCHANT_NO, DEVICE_NO, WX_ORDER_NO, 
      ORDER_NO, USER_ID, BUSINESS_TYPE, 
      BUSINESS_STATUS, BANK, CURRENCY, 
      ORDER_AMOUNT_DUE, COUPON_AMOUNT, WX_REFUND_NO, 
      REFUND_NO, REFUND_AMOUNT, RECHARGE_REFUND_AMOUNT, 
      REFUND_TYPE, REFUND_STATUS, SKU_NAME, 
      DATA_PACK, HANDLING_FEE, RATE, 
      ORDER_AMOUNT, REQ_REFUND_AMOUNT, SYNC_TIME, 
      ADD_TIME)
    values (#{occurrenceTime,jdbcType=TIMESTAMP}, #{publicId,jdbcType=VARCHAR}, #{merchantNo,jdbcType=VARCHAR}, 
      #{specialMerchantNo,jdbcType=VARCHAR}, #{deviceNo,jdbcType=VARCHAR}, #{wxOrderNo,jdbcType=VARCHAR}, 
      #{orderNo,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{businessType,jdbcType=VARCHAR}, 
      #{businessStatus,jdbcType=VARCHAR}, #{bank,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, 
      #{orderAmountDue,jdbcType=DECIMAL}, #{couponAmount,jdbcType=DECIMAL}, #{wxRefundNo,jdbcType=VARCHAR}, 
      #{refundNo,jdbcType=VARCHAR}, #{refundAmount,jdbcType=DECIMAL}, #{rechargeRefundAmount,jdbcType=DECIMAL}, 
      #{refundType,jdbcType=VARCHAR}, #{refundStatus,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, 
      #{dataPack,jdbcType=VARCHAR}, #{handlingFee,jdbcType=DECIMAL}, #{rate,jdbcType=VARCHAR}, 
      #{orderAmount,jdbcType=DECIMAL}, #{reqRefundAmount,jdbcType=DECIMAL}, #{syncTime,jdbcType=DATE}, 
      #{addTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.billsync.task.model.entity.generate.TmpWechatBillDataDo" useGeneratedKeys="true">
    insert into TMP_WECHAT_BILL_DATA
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="occurrenceTime != null">
        OCCURRENCE_TIME,
      </if>
      <if test="publicId != null">
        PUBLIC_ID,
      </if>
      <if test="merchantNo != null">
        MERCHANT_NO,
      </if>
      <if test="specialMerchantNo != null">
        SPECIAL_MERCHANT_NO,
      </if>
      <if test="deviceNo != null">
        DEVICE_NO,
      </if>
      <if test="wxOrderNo != null">
        WX_ORDER_NO,
      </if>
      <if test="orderNo != null">
        ORDER_NO,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="businessStatus != null">
        BUSINESS_STATUS,
      </if>
      <if test="bank != null">
        BANK,
      </if>
      <if test="currency != null">
        CURRENCY,
      </if>
      <if test="orderAmountDue != null">
        ORDER_AMOUNT_DUE,
      </if>
      <if test="couponAmount != null">
        COUPON_AMOUNT,
      </if>
      <if test="wxRefundNo != null">
        WX_REFUND_NO,
      </if>
      <if test="refundNo != null">
        REFUND_NO,
      </if>
      <if test="refundAmount != null">
        REFUND_AMOUNT,
      </if>
      <if test="rechargeRefundAmount != null">
        RECHARGE_REFUND_AMOUNT,
      </if>
      <if test="refundType != null">
        REFUND_TYPE,
      </if>
      <if test="refundStatus != null">
        REFUND_STATUS,
      </if>
      <if test="skuName != null">
        SKU_NAME,
      </if>
      <if test="dataPack != null">
        DATA_PACK,
      </if>
      <if test="handlingFee != null">
        HANDLING_FEE,
      </if>
      <if test="rate != null">
        RATE,
      </if>
      <if test="orderAmount != null">
        ORDER_AMOUNT,
      </if>
      <if test="reqRefundAmount != null">
        REQ_REFUND_AMOUNT,
      </if>
      <if test="syncTime != null">
        SYNC_TIME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="occurrenceTime != null">
        #{occurrenceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="publicId != null">
        #{publicId,jdbcType=VARCHAR},
      </if>
      <if test="merchantNo != null">
        #{merchantNo,jdbcType=VARCHAR},
      </if>
      <if test="specialMerchantNo != null">
        #{specialMerchantNo,jdbcType=VARCHAR},
      </if>
      <if test="deviceNo != null">
        #{deviceNo,jdbcType=VARCHAR},
      </if>
      <if test="wxOrderNo != null">
        #{wxOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="businessStatus != null">
        #{businessStatus,jdbcType=VARCHAR},
      </if>
      <if test="bank != null">
        #{bank,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="orderAmountDue != null">
        #{orderAmountDue,jdbcType=DECIMAL},
      </if>
      <if test="couponAmount != null">
        #{couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="wxRefundNo != null">
        #{wxRefundNo,jdbcType=VARCHAR},
      </if>
      <if test="refundNo != null">
        #{refundNo,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="rechargeRefundAmount != null">
        #{rechargeRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundType != null">
        #{refundType,jdbcType=VARCHAR},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="dataPack != null">
        #{dataPack,jdbcType=VARCHAR},
      </if>
      <if test="handlingFee != null">
        #{handlingFee,jdbcType=DECIMAL},
      </if>
      <if test="rate != null">
        #{rate,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="reqRefundAmount != null">
        #{reqRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="syncTime != null">
        #{syncTime,jdbcType=DATE},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.billsync.task.model.entity.generate.TmpWechatBillDataDo">
    update TMP_WECHAT_BILL_DATA
    <set>
      <if test="occurrenceTime != null">
        OCCURRENCE_TIME = #{occurrenceTime,jdbcType=TIMESTAMP},
      </if>
      <if test="publicId != null">
        PUBLIC_ID = #{publicId,jdbcType=VARCHAR},
      </if>
      <if test="merchantNo != null">
        MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR},
      </if>
      <if test="specialMerchantNo != null">
        SPECIAL_MERCHANT_NO = #{specialMerchantNo,jdbcType=VARCHAR},
      </if>
      <if test="deviceNo != null">
        DEVICE_NO = #{deviceNo,jdbcType=VARCHAR},
      </if>
      <if test="wxOrderNo != null">
        WX_ORDER_NO = #{wxOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="businessStatus != null">
        BUSINESS_STATUS = #{businessStatus,jdbcType=VARCHAR},
      </if>
      <if test="bank != null">
        BANK = #{bank,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        CURRENCY = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="orderAmountDue != null">
        ORDER_AMOUNT_DUE = #{orderAmountDue,jdbcType=DECIMAL},
      </if>
      <if test="couponAmount != null">
        COUPON_AMOUNT = #{couponAmount,jdbcType=DECIMAL},
      </if>
      <if test="wxRefundNo != null">
        WX_REFUND_NO = #{wxRefundNo,jdbcType=VARCHAR},
      </if>
      <if test="refundNo != null">
        REFUND_NO = #{refundNo,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        REFUND_AMOUNT = #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="rechargeRefundAmount != null">
        RECHARGE_REFUND_AMOUNT = #{rechargeRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundType != null">
        REFUND_TYPE = #{refundType,jdbcType=VARCHAR},
      </if>
      <if test="refundStatus != null">
        REFUND_STATUS = #{refundStatus,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="dataPack != null">
        DATA_PACK = #{dataPack,jdbcType=VARCHAR},
      </if>
      <if test="handlingFee != null">
        HANDLING_FEE = #{handlingFee,jdbcType=DECIMAL},
      </if>
      <if test="rate != null">
        RATE = #{rate,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null">
        ORDER_AMOUNT = #{orderAmount,jdbcType=DECIMAL},
      </if>
      <if test="reqRefundAmount != null">
        REQ_REFUND_AMOUNT = #{reqRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="syncTime != null">
        SYNC_TIME = #{syncTime,jdbcType=DATE},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.billsync.task.model.entity.generate.TmpWechatBillDataDo">
    update TMP_WECHAT_BILL_DATA
    set OCCURRENCE_TIME = #{occurrenceTime,jdbcType=TIMESTAMP},
      PUBLIC_ID = #{publicId,jdbcType=VARCHAR},
      MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR},
      SPECIAL_MERCHANT_NO = #{specialMerchantNo,jdbcType=VARCHAR},
      DEVICE_NO = #{deviceNo,jdbcType=VARCHAR},
      WX_ORDER_NO = #{wxOrderNo,jdbcType=VARCHAR},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=VARCHAR},
      BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      BUSINESS_STATUS = #{businessStatus,jdbcType=VARCHAR},
      BANK = #{bank,jdbcType=VARCHAR},
      CURRENCY = #{currency,jdbcType=VARCHAR},
      ORDER_AMOUNT_DUE = #{orderAmountDue,jdbcType=DECIMAL},
      COUPON_AMOUNT = #{couponAmount,jdbcType=DECIMAL},
      WX_REFUND_NO = #{wxRefundNo,jdbcType=VARCHAR},
      REFUND_NO = #{refundNo,jdbcType=VARCHAR},
      REFUND_AMOUNT = #{refundAmount,jdbcType=DECIMAL},
      RECHARGE_REFUND_AMOUNT = #{rechargeRefundAmount,jdbcType=DECIMAL},
      REFUND_TYPE = #{refundType,jdbcType=VARCHAR},
      REFUND_STATUS = #{refundStatus,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      DATA_PACK = #{dataPack,jdbcType=VARCHAR},
      HANDLING_FEE = #{handlingFee,jdbcType=DECIMAL},
      RATE = #{rate,jdbcType=VARCHAR},
      ORDER_AMOUNT = #{orderAmount,jdbcType=DECIMAL},
      REQ_REFUND_AMOUNT = #{reqRefundAmount,jdbcType=DECIMAL},
      SYNC_TIME = #{syncTime,jdbcType=DATE},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=BIGINT}
  </update>
</mapper>