package com.vedeng.erp.trader.domain.dto;

import com.vedeng.erp.trader.dto.*;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 客户360
 *
 * <AUTHOR>
 */
@Data
public class TraderCustomerPortraitDto {

    /**
     * 客户ID
     */
    private Integer traderCustomerId;

    /**
     * 交易者信息ID
     */
    private Integer traderId;

    /**
     * 审核状态
     * 待审核 = 3
     * 审核中 = 0
     * 审核通过 = 1
     * 审核不通过 = 2
     */
    private Integer auditStatus;

    /**
     * traderId的父级
     */
    private Integer parentId;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 创建时间
     */
    private String addTime;

    /**
     * 注册地址
     */
    private String registrationAddress;

    /**
     * 经销商是否有效性，和isEnable 不是同一个东西
     */
    private Integer effectiveness;

    /**
     * 客户类型分类ID
     */
    private Integer traderCustomerCategoryId;

    /**
     * 客户类型(展示字符串)
     */
    private String traderCustomerCategoryName;

    /**
     * 客户类型
     */
    private Integer customerType;

    /**
     * 客户性质
     */
    private Integer customerNature;

    /**
     * 地区最小级ID
     */
    private Integer areaId;

    /**
     * 经营产品类型
     */
    private String businessProductsType;

    /**
     * 经营客户类型
     */
    private String businessCustomerType;

    /**
     * 员工人数 字典项
     */
    private Integer employees;

    /**
     * 员工人数 展示
     */
    private String employeesStr;

    /**
     * 年销售额 字典项
     */
    private Integer annualSales;

    /**
     * 年销售额 展示
     */
    private String annualSalesStr;

    /**
     * 决策标签-生命周期
     */
    private String lifeCycle;

    /**
     * 决策标签-客户等级
     */
    private String customerGrade;

    /**
     * 交易类标签-累计下单
     */
    private String historyTransactionNum;

    /**
     * 交易类标签-历史交易总额
     */
    private String historyTransactionAmount;

    /**
     * 交易类标签-首次下单时间
     */
    private String firstOrderTime;

    /**
     * 交易类标签-最近下单时间
     */
    private String lastOrderTime;

    /**
     * 交易类标签-近一年平均购买周期
     */
    private String lastYearAveragePurchasePeriod;

    /**
     * 交易类标签-历史累计客单价
     */
    private String historyCumulativeUnitPrice;

    /**
     * 交易类标签-订单覆盖科室
     */
    private String departmentCover;

    /**
     * 交易类标签-订单收货地区
     */
    private String orderCoverArea;

    /**
     * 交易类标签-订单装机地区
     */
    private String installedArea;

    /**
     * 封装交易类标签 各个时间段内的分类和品牌信息
     */
    private TraderCategoryBrandDto traderTimeTagsMap = new TraderCategoryBrandDto();

    /**
     * 是否有关联客户信息
     */
    private boolean hasRelationCustomer = false;

    /**
     * 机构性质
     */
    private String institutionNatureName;

    /**
     * 机构等级
     */
    private String institutionLevelName;

    /**
     * 机构类型
     */
    private String institutionTypeName;

    /**
     * 经营状态
     */
    private String managementForms;

    /**
     * 成立时间
     */
    private Date registeredDate;

    /**
     * 成立时间
     */
    private String registeredDateStr;

    /**
     * 注册资本
     */
    private String registeredCapital;

    /**
     * 注册地址
     */
    private String regLocation;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 终端大类
     */
    private String traderCustomerMarketingTypeName;

    /**
     * 是否展示AI客户助理的“NEW”标签
     */
    private Boolean showNewSummary = false;

    /**
     * 仓库地址id
     */
    private Integer warehouseAreaId;

    /**
     * 仓库地址
     */
    private String warehouseArea;

    /**
     * 经营区域
     */
    private String traderCustomerBusinessArea;


    /**
     * 联系人
     */
    private List<TraderContactDto> traderContactDtoList = new ArrayList<>();

    /**
     * 联系地址
     */
    private List<TraderAddressDto> traderAddressDtoList = new ArrayList<>();

    /**
     * 沟通记录
     */
    private List<CommunicateRecordDto> communicateRecordDtoList = new ArrayList<>();

    /**
     * 主营属性
     */
    private TraderCustomerMarketingPrincipalDto traderCustomerMarketingPrincipalDto;

    /**
     * 终端属性
     */
    private List<TraderCustomerMarketingTerminalDto> marketingTerminalDtoList = new ArrayList<>();

    /**
     * 归属销售
     */
    private TraderUserDto traderUserDto;
    
    private String invalidReason;
    
    private String otherReason;
    
    private String showInvalidReason;
}
