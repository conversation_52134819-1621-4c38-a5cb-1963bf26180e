package com.vedeng.erp.trader.domain.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * @description 客户经营品牌
 * <AUTHOR>
 * @date 2023/8/11 13:43
 **/

@Getter
@Setter
@ToString
@NoArgsConstructor
public class TraderCustomerBussinessBrandEntity {

    private Integer traderCustomerBussinessBrandId;

    /**
    * 客户ID
    */
    private Integer traderCustomerId;

    /**
    * 品牌ID
    */
    private Integer brandId;
}