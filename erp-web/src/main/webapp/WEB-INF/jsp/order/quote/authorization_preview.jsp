<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
<c:set var="title" value="授权书预览" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='<%=basePath%>static/js/jquery.PrintArea.js'></script>
<link rel="stylesheet" href="<%=basePath%>static/css/authorization_print_view.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="<%=basePath%>static/new/css/common/font/font.css" />
<link rel="stylesheet" href="<%=basePath%>static/new/css/common/lib/pikaday.css?rnd=${resourceVersionKey}" />

<form method="post" action="<%= basePath %>/order/quote/authorizationApply.do" id="tjform">
    <div class="wrapper">
        <tags:authorization_common authorizationApply="${authorizationApply}" />
    </div>
</form>

<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/pikaday.2.1.0.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/order/quote/authorization_view.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/tag.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp" %>
