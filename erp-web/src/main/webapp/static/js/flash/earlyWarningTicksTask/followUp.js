function computeTotalCanTicketAmount(){
    var totalAmount=0;
    $.each($("input[name='canTicketAmountInput']"),function(i,n){
        if(isNaN($(this).val())){
            layer.alert("请输入数字！");
            $(this).val($(this).siblings("input[name='nowUrgingTicketAmount']").val());
        } else if(Number($(this).val())>Number($(this).siblings("input[name='nowUrgingTicketAmount']").val())){
            layer.alert("可开金额大于催票金额，请重新输入");
            $(this).val($(this).siblings("input[name='nowUrgingTicketAmount']").val());
        }
        totalAmount += Number($(this).val());
    });
    $("#canTicketAmount").val(totalAmount);
}
function changeCanTicket(){
    if($("#followUpStatus").val()==0){//不可开票
        $("#cantTicketReson").show();
        $("#canTicketTimeLi").hide();
        $.each($("input[name='canTicketAmountInput']"),function(i,n){
           $(this).val(0);
           $(this).attr("readonly","readonly");
        });
        $("#canTicketAmount").val(0);
        $("#canTicketTime").val("");
    }else{//可开票
        $("#cantTicketReson").hide();
        $("#canTicketTimeLi").show();
        $("#reson").val("");
        $.each($("input[name='canTicketAmountInput']"),function(i,n){
            $(this).attr("readonly",false);
        });
    }
}
$(function(){
    computeTotalCanTicketAmount();

    changeCanTicket();

    $("#saveFollowUp").click(function(){
        checkLogin();
        if($("#followUpStatus").val()==0 && ($("#reson").val().length<1||$("#reson").val().length>100)){//不可开票
            layer.alert("不可开原因不符合规范!");
            return;
        }
        if($("#followUpStatus").val()==1 && ($("#canTicketTime").val()==0)){//可开票
            layer.alert("请输入可开时间!");
            return;
        }
        var earlyWarningTaskId_canTicketAmountInput_urgingTicketNumFollowUp_urgingTicketAmountFollowUp="";
        $.each($("input[name='canTicketAmountInput']"),function(i,n){
            earlyWarningTaskId_canTicketAmountInput_urgingTicketNumFollowUp_urgingTicketAmountFollowUp+=
                $(this).siblings("input[name='earlyWarningTaskId']").val()+'_'
                +$(this).val()+ '_'
                +$(this).siblings("input[name='nowUrgingTicketNum']").val()+ '_'
                +$(this).siblings("input[name='nowUrgingTicketAmount']").val()+ ',';
        });
        $.ajax({
            async:false,
            url:'./saveFollowUp.do',
            data:{
                "earlyWarningTaskId_canTicketAmountInput_urgingTicketNumFollowUp_urgingTicketAmountFollowUp":
                earlyWarningTaskId_canTicketAmountInput_urgingTicketNumFollowUp_urgingTicketAmountFollowUp,
                "followUpStatus":$("#followUpStatus").val(),
                "canTicketTime":$("#canTicketTime").val(),
                "reson":$("#reson").val()
            },
            type:"POST",
            dataType : "json",
            success:function(data){

                if(data.code==0){
                    layer.alert(data.message,
                        { icon: 1 },
                        function () {
                            parent.location.reload();
                        }
                    );
                }else{
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    })
})
