package com.vedeng.crm.feign.category;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/17
 */
import com.github.pagehelper.PageInfo;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.base.R;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.goods.dto.CategoryFrontDto;
import com.vedeng.goods.dto.CategoryQueryDto;
import com.vedeng.goods.dto.CategoryResultDto;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 *  三级分类的服务-调用ERP接口
 *
 */
@FeignApi(serverName = "erpServer")
public interface CategoryThreeApiService {

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/goods/findThreeCategory.do")
    RestfulResult<PageInfo<CategoryResultDto>> findThreeCategory(@RequestBody CategoryQueryDto categoryQueryDto);

    /**
     * 查询分类详情
     * @return
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /goodsCategory/getAllCategory.do")
    R<List<CategoryFrontDto>> findLevelCategory();

    @RequestLine("GET /goodsCategory/getCategoryIdByFullPath.do?fullCategoryPath={fullCategoryPath}")
    R<Integer> getCategoryIdByFullPath(@Param("fullCategoryPath") String fullCategoryPath);

    /**
     * 根据分类ID获取完整路径名称
     *
     * @param categoryId 分类ID
     * @return 完整路径名称
     */
    @RequestLine("GET /goodsCategory/getFullPathNameById.do?categoryId={categoryId}")
    R<String> getFullPathNameById(@Param("categoryId") Integer categoryId);
    
}
