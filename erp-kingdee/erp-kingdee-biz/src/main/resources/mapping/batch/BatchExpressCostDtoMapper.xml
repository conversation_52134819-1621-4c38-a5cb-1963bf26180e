<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchExpressCostDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchExpressCostDto">
    <!--@mbg.generated-->
    <!--@Table T_EXPRESS_COST-->
    <id column="EXPRESS_COST_ID" jdbcType="INTEGER" property="expressCostId" />
    <result column="UNIQUE_KEY" jdbcType="VARCHAR" property="uniqueKey" />
    <result column="EXPRESS_DETAIL_ID" jdbcType="INTEGER" property="expressDetailId" />
    <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId" />
    <result column="SALEORDER_NO" jdbcType="VARCHAR" property="saleorderNo" />
    <result column="OUT_IN_NO" jdbcType="VARCHAR" property="outInNo" />
    <result column="LOGISTICS" jdbcType="VARCHAR" property="logistics" />
    <result column="TAKE_TRADER_ADDRESS" jdbcType="VARCHAR" property="takeTraderAddress" />
    <result column="TAKE_TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="takeTraderContactName" />
    <result column="TAKE_TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="takeTraderContactTelephone" />
    <result column="LOGISTICS_NO" jdbcType="VARCHAR" property="logisticsNo" />
    <result column="DATA_TYPE" jdbcType="INTEGER" property="dataType" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="BATCH_NUMBER" jdbcType="VARCHAR" property="batchNumber" />
    <result column="BARCODE_FACTORY" jdbcType="VARCHAR" property="barcodeFactory" />
    <result column="SHARE_NUM" jdbcType="INTEGER" property="shareNum" />
    <result column="FAIR_PRICE" jdbcType="DECIMAL" property="fairPrice" />
    <result column="SALEORDER_GOODS_ID" jdbcType="INTEGER" property="saleorderGoodsId" />
    <result column="IS_GIFT" jdbcType="BOOLEAN" property="isGift" />
    <result column="TOTAL_EXPRESS_AMOUNT" jdbcType="DECIMAL" property="totalExpressAmount" />
    <result column="SHARE_EXPRESS_AMOUNT" jdbcType="DECIMAL" property="shareExpressAmount" />
    <result column="SHARE_EXPRESS_AMOUNT_NO_TAX" jdbcType="DECIMAL" property="shareExpressAmountNoTax" />
    <result column="COST_ACT_FLAG" jdbcType="INTEGER" property="costActFlag" />
    <result column="ETL_TIME" jdbcType="VARCHAR" property="etlTime" />
    <result column="PUSH_STATUS" jdbcType="BOOLEAN" property="pushStatus" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    EXPRESS_COST_ID, UNIQUE_KEY, EXPRESS_DETAIL_ID, EXPRESS_ID, SALEORDER_NO, OUT_IN_NO,
    LOGISTICS, TAKE_TRADER_ADDRESS, TAKE_TRADER_CONTACT_NAME, TAKE_TRADER_CONTACT_TELEPHONE, 
    LOGISTICS_NO, DATA_TYPE, SKU, BATCH_NUMBER, BARCODE_FACTORY, SHARE_NUM, FAIR_PRICE, 
    SALEORDER_GOODS_ID, IS_GIFT, TOTAL_EXPRESS_AMOUNT, SHARE_EXPRESS_AMOUNT, SHARE_EXPRESS_AMOUNT_NO_TAX, 
    COST_ACT_FLAG, ETL_TIME, PUSH_STATUS, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, UPDATER, 
    CREATOR_NAME, UPDATER_NAME, UPDATE_REMARK
  </sql>

  <sql id="Kd_Column_List">
    <!--@mbg.generated-->
    EXPRESS_COST_ID, UNIQUE_KEY, EXPRESS_DETAIL_ID, EXPRESS_ID, SALEORDER_NO, OUT_IN_NO,
    LOGISTICS, TAKE_TRADER_ADDRESS, TAKE_TRADER_CONTACT_NAME, TAKE_TRADER_CONTACT_TELEPHONE,
    LOGISTICS_NO, DATA_TYPE, SKU, BATCH_NUMBER, BARCODE_FACTORY, SHARE_NUM, FAIR_PRICE,
    SALEORDER_GOODS_ID, IS_GIFT, TOTAL_EXPRESS_AMOUNT, SHARE_EXPRESS_AMOUNT, SHARE_EXPRESS_AMOUNT_NO_TAX,
    COST_ACT_FLAG, ETL_TIME, PUSH_STATUS, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, UPDATER,
    CREATOR_NAME, UPDATER_NAME, UPDATE_REMARK
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_EXPRESS_COST
    where EXPRESS_COST_ID = #{expressCostId,jdbcType=INTEGER}
  </select>
  <select id="findExpressCostByAll" resultType="com.vedeng.erp.kingdee.batch.dto.BatchExpressCostDto">
    <!--@mbg.generated-->
    select
    <include refid="Kd_Column_List" />
    from T_EXPRESS_COST
    where
    IS_DELETE = 0 and COST_ACT_FLAG in
    <foreach collection="costActFlags" item="item" close=")" open="(" separator=",">
      #{item}
    </foreach>
    <if test="beginTime != null">
      and ADD_TIME <![CDATA[>=]]> #{beginTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and ADD_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
    </if>
    order by EXPRESS_COST_ID asc
    limit #{_pagesize} OFFSET #{_skiprows}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_EXPRESS_COST
    where EXPRESS_COST_ID = #{expressCostId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="EXPRESS_COST_ID" keyProperty="expressCostId" parameterType="com.vedeng.erp.kingdee.domain.entity.ExpressCostEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS_COST (UNIQUE_KEY, EXPRESS_DETAIL_ID, EXPRESS_ID, 
      SALEORDER_NO, OUT_IN_NO, LOGISTICS, 
      TAKE_TRADER_ADDRESS, TAKE_TRADER_CONTACT_NAME, 
      TAKE_TRADER_CONTACT_TELEPHONE, LOGISTICS_NO, 
      DATA_TYPE, SKU, BATCH_NUMBER, 
      BARCODE_FACTORY, SHARE_NUM, FAIR_PRICE, 
      SALEORDER_GOODS_ID, IS_GIFT, TOTAL_EXPRESS_AMOUNT, 
      SHARE_EXPRESS_AMOUNT, SHARE_EXPRESS_AMOUNT_NO_TAX, 
      COST_ACT_FLAG, ETL_TIME, PUSH_STATUS, 
      IS_DELETE, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, CREATOR_NAME, 
      UPDATER_NAME, UPDATE_REMARK)
    values (#{uniqueKey,jdbcType=VARCHAR}, #{expressDetailId,jdbcType=INTEGER}, #{expressId,jdbcType=INTEGER}, 
      #{saleorderNo,jdbcType=VARCHAR}, #{outInNo,jdbcType=VARCHAR}, #{logistics,jdbcType=VARCHAR}, 
      #{takeTraderAddress,jdbcType=VARCHAR}, #{takeTraderContactName,jdbcType=VARCHAR}, 
      #{takeTraderContactTelephone,jdbcType=VARCHAR}, #{logisticsNo,jdbcType=VARCHAR}, 
      #{dataType,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{batchNumber,jdbcType=VARCHAR}, 
      #{barcodeFactory,jdbcType=VARCHAR}, #{shareNum,jdbcType=INTEGER}, #{fairPrice,jdbcType=DECIMAL}, 
      #{saleorderGoodsId,jdbcType=INTEGER}, #{isGift,jdbcType=BOOLEAN}, #{totalExpressAmount,jdbcType=DECIMAL}, 
      #{shareExpressAmount,jdbcType=DECIMAL}, #{shareExpressAmountNoTax,jdbcType=DECIMAL}, 
      #{costActFlag,jdbcType=INTEGER}, #{etlTime,jdbcType=VARCHAR}, #{pushStatus,jdbcType=BOOLEAN}, 
      #{isDelete,jdbcType=BOOLEAN}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="EXPRESS_COST_ID" keyProperty="expressCostId" parameterType="com.vedeng.erp.kingdee.domain.entity.ExpressCostEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS_COST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uniqueKey != null">
        UNIQUE_KEY,
      </if>
      <if test="expressDetailId != null">
        EXPRESS_DETAIL_ID,
      </if>
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO,
      </if>
      <if test="outInNo != null">
        OUT_IN_NO,
      </if>
      <if test="logistics != null">
        LOGISTICS,
      </if>
      <if test="takeTraderAddress != null">
        TAKE_TRADER_ADDRESS,
      </if>
      <if test="takeTraderContactName != null">
        TAKE_TRADER_CONTACT_NAME,
      </if>
      <if test="takeTraderContactTelephone != null">
        TAKE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="logisticsNo != null">
        LOGISTICS_NO,
      </if>
      <if test="dataType != null">
        DATA_TYPE,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="batchNumber != null">
        BATCH_NUMBER,
      </if>
      <if test="barcodeFactory != null">
        BARCODE_FACTORY,
      </if>
      <if test="shareNum != null">
        SHARE_NUM,
      </if>
      <if test="fairPrice != null">
        FAIR_PRICE,
      </if>
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID,
      </if>
      <if test="isGift != null">
        IS_GIFT,
      </if>
      <if test="totalExpressAmount != null">
        TOTAL_EXPRESS_AMOUNT,
      </if>
      <if test="shareExpressAmount != null">
        SHARE_EXPRESS_AMOUNT,
      </if>
      <if test="shareExpressAmountNoTax != null">
        SHARE_EXPRESS_AMOUNT_NO_TAX,
      </if>
      <if test="costActFlag != null">
        COST_ACT_FLAG,
      </if>
      <if test="etlTime != null">
        ETL_TIME,
      </if>
      <if test="pushStatus != null">
        PUSH_STATUS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uniqueKey != null">
        #{uniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="expressDetailId != null">
        #{expressDetailId,jdbcType=INTEGER},
      </if>
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      <if test="saleorderNo != null">
        #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="outInNo != null">
        #{outInNo,jdbcType=VARCHAR},
      </if>
      <if test="logistics != null">
        #{logistics,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddress != null">
        #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactName != null">
        #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null">
        #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="logisticsNo != null">
        #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="barcodeFactory != null">
        #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="shareNum != null">
        #{shareNum,jdbcType=INTEGER},
      </if>
      <if test="fairPrice != null">
        #{fairPrice,jdbcType=DECIMAL},
      </if>
      <if test="saleorderGoodsId != null">
        #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="isGift != null">
        #{isGift,jdbcType=BOOLEAN},
      </if>
      <if test="totalExpressAmount != null">
        #{totalExpressAmount,jdbcType=DECIMAL},
      </if>
      <if test="shareExpressAmount != null">
        #{shareExpressAmount,jdbcType=DECIMAL},
      </if>
      <if test="shareExpressAmountNoTax != null">
        #{shareExpressAmountNoTax,jdbcType=DECIMAL},
      </if>
      <if test="costActFlag != null">
        #{costActFlag,jdbcType=INTEGER},
      </if>
      <if test="etlTime != null">
        #{etlTime,jdbcType=VARCHAR},
      </if>
      <if test="pushStatus != null">
        #{pushStatus,jdbcType=BOOLEAN},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.ExpressCostEntity">
    <!--@mbg.generated-->
    update T_EXPRESS_COST
    <set>
      <if test="uniqueKey != null">
        UNIQUE_KEY = #{uniqueKey,jdbcType=VARCHAR},
      </if>
      <if test="expressDetailId != null">
        EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER},
      </if>
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="outInNo != null">
        OUT_IN_NO = #{outInNo,jdbcType=VARCHAR},
      </if>
      <if test="logistics != null">
        LOGISTICS = #{logistics,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddress != null">
        TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactName != null">
        TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null">
        TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="logisticsNo != null">
        LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        DATA_TYPE = #{dataType,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="barcodeFactory != null">
        BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      </if>
      <if test="shareNum != null">
        SHARE_NUM = #{shareNum,jdbcType=INTEGER},
      </if>
      <if test="fairPrice != null">
        FAIR_PRICE = #{fairPrice,jdbcType=DECIMAL},
      </if>
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="isGift != null">
        IS_GIFT = #{isGift,jdbcType=BOOLEAN},
      </if>
      <if test="totalExpressAmount != null">
        TOTAL_EXPRESS_AMOUNT = #{totalExpressAmount,jdbcType=DECIMAL},
      </if>
      <if test="shareExpressAmount != null">
        SHARE_EXPRESS_AMOUNT = #{shareExpressAmount,jdbcType=DECIMAL},
      </if>
      <if test="shareExpressAmountNoTax != null">
        SHARE_EXPRESS_AMOUNT_NO_TAX = #{shareExpressAmountNoTax,jdbcType=DECIMAL},
      </if>
      <if test="costActFlag != null">
        COST_ACT_FLAG = #{costActFlag,jdbcType=INTEGER},
      </if>
      <if test="etlTime != null">
        ETL_TIME = #{etlTime,jdbcType=VARCHAR},
      </if>
      <if test="pushStatus != null">
        PUSH_STATUS = #{pushStatus,jdbcType=BOOLEAN},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where EXPRESS_COST_ID = #{expressCostId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.ExpressCostEntity">
    <!--@mbg.generated-->
    update T_EXPRESS_COST
    set UNIQUE_KEY = #{uniqueKey,jdbcType=VARCHAR},
      EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER},
      EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      OUT_IN_NO = #{outInNo,jdbcType=VARCHAR},
      LOGISTICS = #{logistics,jdbcType=VARCHAR},
      TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      DATA_TYPE = #{dataType,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      BATCH_NUMBER = #{batchNumber,jdbcType=VARCHAR},
      BARCODE_FACTORY = #{barcodeFactory,jdbcType=VARCHAR},
      SHARE_NUM = #{shareNum,jdbcType=INTEGER},
      FAIR_PRICE = #{fairPrice,jdbcType=DECIMAL},
      SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      IS_GIFT = #{isGift,jdbcType=BOOLEAN},
      TOTAL_EXPRESS_AMOUNT = #{totalExpressAmount,jdbcType=DECIMAL},
      SHARE_EXPRESS_AMOUNT = #{shareExpressAmount,jdbcType=DECIMAL},
      SHARE_EXPRESS_AMOUNT_NO_TAX = #{shareExpressAmountNoTax,jdbcType=DECIMAL},
      COST_ACT_FLAG = #{costActFlag,jdbcType=INTEGER},
      ETL_TIME = #{etlTime,jdbcType=VARCHAR},
      PUSH_STATUS = #{pushStatus,jdbcType=BOOLEAN},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where EXPRESS_COST_ID = #{expressCostId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="EXPRESS_COST_ID" keyProperty="expressCostId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS_COST
    (UNIQUE_KEY, EXPRESS_DETAIL_ID, EXPRESS_ID, SALEORDER_NO, OUT_IN_NO, LOGISTICS, TAKE_TRADER_ADDRESS, 
      TAKE_TRADER_CONTACT_NAME, TAKE_TRADER_CONTACT_TELEPHONE, LOGISTICS_NO, DATA_TYPE, 
      SKU, BATCH_NUMBER, BARCODE_FACTORY, SHARE_NUM, FAIR_PRICE, SALEORDER_GOODS_ID, 
      IS_GIFT, TOTAL_EXPRESS_AMOUNT, SHARE_EXPRESS_AMOUNT, SHARE_EXPRESS_AMOUNT_NO_TAX, 
      COST_ACT_FLAG, ETL_TIME, PUSH_STATUS, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, UPDATER, 
      CREATOR_NAME, UPDATER_NAME, UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.uniqueKey,jdbcType=VARCHAR}, #{item.expressDetailId,jdbcType=INTEGER}, #{item.expressId,jdbcType=INTEGER}, 
        #{item.saleorderNo,jdbcType=VARCHAR}, #{item.outInNo,jdbcType=VARCHAR}, #{item.logistics,jdbcType=VARCHAR}, 
        #{item.takeTraderAddress,jdbcType=VARCHAR}, #{item.takeTraderContactName,jdbcType=VARCHAR}, 
        #{item.takeTraderContactTelephone,jdbcType=VARCHAR}, #{item.logisticsNo,jdbcType=VARCHAR}, 
        #{item.dataType,jdbcType=INTEGER}, #{item.sku,jdbcType=VARCHAR}, #{item.batchNumber,jdbcType=VARCHAR}, 
        #{item.barcodeFactory,jdbcType=VARCHAR}, #{item.shareNum,jdbcType=INTEGER}, #{item.fairPrice,jdbcType=DECIMAL}, 
        #{item.saleorderGoodsId,jdbcType=INTEGER}, #{item.isGift,jdbcType=BOOLEAN}, #{item.totalExpressAmount,jdbcType=DECIMAL}, 
        #{item.shareExpressAmount,jdbcType=DECIMAL}, #{item.shareExpressAmountNoTax,jdbcType=DECIMAL}, 
        #{item.costActFlag,jdbcType=INTEGER}, #{item.etlTime,jdbcType=VARCHAR}, #{item.pushStatus,jdbcType=BOOLEAN}, 
        #{item.isDelete,jdbcType=BOOLEAN}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.updaterName,jdbcType=VARCHAR}, #{item.updateRemark,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>