package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveFeeEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface KingDeeReceiveFeeMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeReceiveFeeEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeReceiveFeeEntity record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    KingDeeReceiveFeeEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeReceiveFeeEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeReceiveFeeEntity record);

    int updateBatchSelective(List<KingDeeReceiveFeeEntity> list);

    int batchInsert(@Param("list") List<KingDeeReceiveFeeEntity> list);

}