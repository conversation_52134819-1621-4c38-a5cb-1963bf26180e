<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCustomerTerminalMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderCustomerTerminalEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_CUSTOMER_TERMINAL-->
    <id column="TRADER_CUSTOMER_TERMINAL_ID" jdbcType="INTEGER" property="traderCustomerTerminalId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
    <result column="TERMINAL_NAME" jdbcType="VARCHAR" property="terminalName" />
    <result column="DWH_TERMINAL_ID" jdbcType="VARCHAR" property="dwhTerminalId" />
    <result column="AUDIT_STATUS" jdbcType="INTEGER" property="auditStatus" />
    <result column="COMMUNICATE_RECORD_ID" jdbcType="INTEGER" property="communicateRecordId" />
    <result column="PIC_URL" jdbcType="VARCHAR" property="picUrl" />
    <result column="IS_DELETED" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_CUSTOMER_TERMINAL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, TRADER_ID, TRADER_CUSTOMER_ID, TERMINAL_NAME, DWH_TERMINAL_ID, AUDIT_STATUS,
    COMMUNICATE_RECORD_ID, PIC_URL, IS_DELETED
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_TRADER_CUSTOMER_TERMINAL
    where TRADER_CUSTOMER_TERMINAL_ID = #{traderCustomerTerminalId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER_CUSTOMER_TERMINAL
    where TRADER_CUSTOMER_TERMINAL_ID = #{traderCustomerTerminalId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_CUSTOMER_TERMINAL_ID" keyProperty="traderCustomerTerminalId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerTerminalEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_TERMINAL (ADD_TIME, MOD_TIME, CREATOR,
    UPDATER, CREATOR_NAME, UPDATER_NAME,
    TRADER_ID, TRADER_CUSTOMER_ID, TERMINAL_NAME,
    DWH_TERMINAL_ID, AUDIT_STATUS, COMMUNICATE_RECORD_ID,
    PIC_URL, IS_DELETED)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
    #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR},
    #{traderId,jdbcType=INTEGER}, #{traderCustomerId,jdbcType=INTEGER}, #{terminalName,jdbcType=VARCHAR},
    #{dwhTerminalId,jdbcType=VARCHAR}, #{auditStatus,jdbcType=INTEGER}, #{communicateRecordId,jdbcType=INTEGER},
    #{picUrl,jdbcType=VARCHAR}, #{isDeleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_CUSTOMER_TERMINAL_ID" keyProperty="traderCustomerTerminalId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerTerminalEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_TERMINAL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID,
      </if>
      <if test="terminalName != null">
        TERMINAL_NAME,
      </if>
      <if test="dwhTerminalId != null">
        DWH_TERMINAL_ID,
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS,
      </if>
      <if test="communicateRecordId != null">
        COMMUNICATE_RECORD_ID,
      </if>
      <if test="picUrl != null">
        PIC_URL,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="terminalName != null">
        #{terminalName,jdbcType=VARCHAR},
      </if>
      <if test="dwhTerminalId != null">
        #{dwhTerminalId,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="communicateRecordId != null">
        #{communicateRecordId,jdbcType=INTEGER},
      </if>
      <if test="picUrl != null">
        #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerTerminalEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_TERMINAL
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderCustomerId != null">
        TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="terminalName != null">
        TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
      </if>
      <if test="dwhTerminalId != null">
        DWH_TERMINAL_ID = #{dwhTerminalId,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="communicateRecordId != null">
        COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER},
      </if>
      <if test="picUrl != null">
        PIC_URL = #{picUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=INTEGER},
      </if>
    </set>
    where TRADER_CUSTOMER_TERMINAL_ID = #{traderCustomerTerminalId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerTerminalEntity">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_TERMINAL
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
    MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
    CREATOR = #{creator,jdbcType=INTEGER},
    UPDATER = #{updater,jdbcType=INTEGER},
    CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
    UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    TRADER_ID = #{traderId,jdbcType=INTEGER},
    TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER},
    TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
    DWH_TERMINAL_ID = #{dwhTerminalId,jdbcType=VARCHAR},
    AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER},
    COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER},
    PIC_URL = #{picUrl,jdbcType=VARCHAR},
    IS_DELETED = #{isDeleted,jdbcType=INTEGER}
    where TRADER_CUSTOMER_TERMINAL_ID = #{traderCustomerTerminalId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="TRADER_CUSTOMER_TERMINAL_ID" keyProperty="traderCustomerTerminalId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_TERMINAL
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, TRADER_ID, TRADER_CUSTOMER_ID,
    TERMINAL_NAME, DWH_TERMINAL_ID, AUDIT_STATUS, COMMUNICATE_RECORD_ID, PIC_URL, IS_DELETED
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
      #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR},
      #{item.traderId,jdbcType=INTEGER}, #{item.traderCustomerId,jdbcType=INTEGER}, #{item.terminalName,jdbcType=VARCHAR},
      #{item.dwhTerminalId,jdbcType=VARCHAR}, #{item.auditStatus,jdbcType=INTEGER}, #{item.communicateRecordId,jdbcType=INTEGER},
      #{item.picUrl,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=INTEGER})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-09-05-->
  <select id="selectByTraderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_TRADER_CUSTOMER_TERMINAL
    where TRADER_ID=#{traderId,jdbcType=INTEGER}
    and  IS_DELETED = 0
  </select>

  <select id="findByAll" resultType="com.vedeng.erp.trader.dto.TraderCustomerTerminalResult">
        select
            c.TRADER_ID,c.TRADER_CUSTOMER_ID,b.TRADER_NAME,d.USER_ID,d.USERNAME,MAX(c.MOD_TIME) arrivalTime
        from
        T_R_TRADER_J_USER a left join T_TRADER b on b.TRADER_ID = a.TRADER_ID and a.TRADER_TYPE = 1
        left join T_TRADER_CUSTOMER_TERMINAL c on c.TRADER_ID = a.TRADER_ID
          left join T_USER d on a.USER_ID = d.USER_ID
        where c.IS_DELETED = 0 and c.AUDIT_STATUS =0
        <if test="userIdList != null and userIdList.size() != 0">
          and a.USER_ID in
          <foreach item="i" collection="userIdList" close=")" separator="," open="(">
            #{i,jdbcType=INTEGER}
          </foreach>
        </if>
        <if test="traderName != null and traderName != ''">
          and b.TRADER_NAME like concat('%',#{traderName,jdbcType=VARCHAR},'%')
        </if>
        <if test="beginTime != null">
          and c.MOD_TIME <![CDATA[>=]]>  #{beginTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
          and c.MOD_TIME <![CDATA[<=]]>  #{endTime,jdbcType=TIMESTAMP}
        </if>
        group by  c.TRADER_ID,
            c.TRADER_CUSTOMER_ID,
            b.TRADER_NAME,
            d.USER_ID,
            d.USERNAME
        ORDER BY
            arrivalTime DESC
  </select>

<!--auto generated by MybatisCodeHelper on 2023-09-06-->
  <select id="selectByTraderIdAndDwhTerminalId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_TRADER_CUSTOMER_TERMINAL
    where TRADER_ID=#{traderId,jdbcType=INTEGER} and DWH_TERMINAL_ID=#{dwhTerminalId,jdbcType=VARCHAR}
    and IS_DELETED = 0
  </select>

<!--auto generated by MybatisCodeHelper on 2023-09-06-->
  <select id="selectByTraderIdAndTraderCustomerIdAndAuditStatus" resultType="com.vedeng.erp.trader.dto.TraderCustomerTerminalDto">
    select
    a.ADD_TIME, a.MOD_TIME, a.CREATOR, a.UPDATER, a.CREATOR_NAME, a.UPDATER_NAME, a.TRADER_CUSTOMER_TERMINAL_ID, a.TRADER_ID,
    a.TRADER_CUSTOMER_ID, a.TERMINAL_NAME, a.DWH_TERMINAL_ID, a.AUDIT_STATUS, a.COMMUNICATE_RECORD_ID, a.PIC_URL, a.IS_DELETED,b.TRADER_NAME
    ,c.COID_URI,e.USERNAME belongSalesUsername
    from T_TRADER_CUSTOMER_TERMINAL a left join T_TRADER b on b.TRADER_ID = a.TRADER_ID
    left join T_R_TRADER_J_USER d on d.TRADER_ID = a.TRADER_ID and d.TRADER_TYPE = 1
    left join T_USER e on e.USER_ID = d.USER_ID
    left join T_COMMUNICATE_RECORD c on c.COMMUNICATE_RECORD_ID = a.COMMUNICATE_RECORD_ID
    where a.TRADER_ID=#{traderId,jdbcType=INTEGER} and
    a.AUDIT_STATUS=#{auditStatus,jdbcType=INTEGER}
    and a.IS_DELETED = 0
    order by a.TRADER_CUSTOMER_TERMINAL_ID asc
  </select>
</mapper>