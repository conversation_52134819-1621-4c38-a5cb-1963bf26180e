<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<div id="app" style="display: none">
    <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix">
            <span></span>
        </div>

        <template>
            <el-table :data="fairValueDto.historyDataDtoList" border :header-cell-style="{textAlign: 'center'}" style="width: 100%">
                <el-table-column align="center" label="生成年月">
                    <template slot-scope="scope">
                        <span>{{parseTime(scope.row.generateDate,'{y}-{m}')}}</span>
                    </template>
                </el-table-column>

                <el-table-column align="center" label="历史公允价">
                    <template slot-scope="scope">
                        <span>{{formatNum(scope.row.fairValue)}}</span>
                    </template>
                </el-table-column>
            </el-table>
        </template>
    </el-card>
    <el-row style="text-align: center; margin-bottom: 20px;margin-top: 20px">
        <el-button type="primary" size="small" @click="closeLayer()" style="background-color: #d58c0f">关闭</el-button>
    </el-row>
</div>

<script type="text/javascript">
    const fairValueId = '${fairValueId}';
    const sendThis = (_this) => {
        vm = _this;
    };

    new Vue({
        el: '#app',
        data() {
            return {
                fairValueDto:{
                    historyDataDtoList: []
                }
            };
        },
        mounted(){
            loadingApp();
        },
        created() {
            this.initData();
        },
        methods: {
            closeThis(){
                parent.layer.close(index);
            },
            closeLayer(){
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            },
            formatNum(data) {
                if (data == null) {
                    data = 0;
                }
                return data.toFixed(2);
            },
            async initData() {
                if (fairValueId != null && fairValueId != "") {
                    await viewData(fairValueId).then(res => {
                        this.fairValueDto = res.data.data;
                    });
                }
            }
        }
    });

    /**
     * @param data
     */
    function viewData(data){
        return axios({
            url: '/goods/finance/getFairPriceInfoDto.do',
            method: 'post',
            params:{fairValueId:data}
        })
    }
</script>