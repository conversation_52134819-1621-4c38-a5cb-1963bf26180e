<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>拜访管理</title>
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,viewport-fit=cover">
    <link rel="stylesheet" href="/mstatic/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/mstatic/mui/mui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/mstatic/css/pages/visitRecord.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>
<jsp:include page="./head_import_im.jsp"></jsp:include>
    <div class="page-wrap" id="page-container">
        <crm-list-container ref="searchContainer" title="全部拜访" v-model="searchParams" :filter-sort-list="filterSortList" list-url="/crm/visitrecord/m/page" add-link="/crm/visitRecord/m/add" list-key="03">
            <template v-slot:filter>
                <crm-m-search-item title="客户名称" type="vertical" vkey="customerName" :lock="true">
                    <ui-input placeholder="请输入" v-model="searchParams.customerName" maxlength="50"></ui-input>
                </crm-m-search-item>
                <crm-m-search-item title="拜访编号" type="vertical" vkey="visitRecordNo">
                    <ui-input placeholder="请输入" maxlength="50" v-model="searchParams.visitRecordNo"></ui-input>
                </crm-m-search-item>
                <crm-m-search-item title="拜访状态" type="vertical" vkey="visitRecordStatusList">
                    <ui-checkbox-group type="label" v-model="searchParams.visitRecordStatusList" :list="statusList"></ui-checkbox-group>
                </crm-m-search-item>
                <crm-m-search-item title="拜访人" type="toggle" vkey="visitorIdList" >
                    <ui-select 
                        title="拜访人" 
                        :list="visitorList"
                        :line="2" 
                        v-model="searchParams.visitorIdList" 
                        :filter="true" 
                        :multi="true"
                        :avatar="true"
                    >
                    </ui-select>
                </crm-m-search-item>
                <crm-m-search-item title="同行人" type="toggle" vkey="tongxingIdList">
                    <ui-select 
                        title="同行人" 
                        :line="2" 
                        :list="tongxingList"
                        v-model="searchParams.tongxingIdList" 
                        :filter="true" 
                        :multi="true"
                        :avatar="true"
                    >
                    </ui-select>
                </crm-m-search-item>
                <crm-m-search-item title="创建人" type="toggle" vkey="creatorIdList">
                    <ui-select 
                        title="创建人" 
                        :line="2" 
                        :list="creatorList"
                        v-model="searchParams.creatorIdList" 
                        :filter="true" 
                        :multi="true"
                        :avatar="true"
                    >
                    </ui-select>
                </crm-m-search-item>
                <crm-m-search-item title="计划拜访时间" vkey="planVisitDate">
                    <ui-form-date-picker type="range" title="计划拜访时间" v-model="searchParams.planVisitDate" @change="handlerDateChange('planVisitDate', $event)"></ui-form-date-picker>
                </crm-m-search-item>
                <crm-m-search-item title="拜访完成时间" vkey="completeDate">
                    <ui-form-date-picker type="range" title="拜访完成时间" v-model="searchParams.completeDate" @change="handlerDateChange('completeDate', $event)"></ui-form-date-picker>
                </crm-m-search-item>
                <crm-m-search-item title="创建时间" vkey="addTime">
                    <ui-form-date-picker type="range" title="创建时间" v-model="searchParams.addTime" @change="handlerDateChange('addTime', $event)"></ui-form-date-picker>
                </crm-m-search-item>
                <crm-m-search-item title="客户类型" type="vertical" vkey="customerNature">
                    <ui-radio-group type="label" :clearable="true" v-model="searchParams.customerNature" :list="customerNatureList"></ui-radio-group>
                </crm-m-search-item>
                <crm-m-search-item title="联系人姓名" type="vertical" vkey="contactName">
                    <ui-input placeholder="请输入" v-model="searchParams.contactName" maxlength="50"></ui-input>
                </crm-m-search-item>
                <crm-m-search-item title="联系人手机" type="vertical" vkey="contactMobile">
                    <ui-input placeholder="请输入" v-model="searchParams.contactMobile" maxlength="50"></ui-input>
                </crm-m-search-item>
                <crm-m-search-item title="固话" type="vertical" vkey="contactTele">
                    <ui-input placeholder="请输入" v-model="searchParams.contactTele" maxlength="50"></ui-input>
                </crm-m-search-item>
                <crm-m-search-item title="拜访目标" type="vertical" vkey="visitTargetList">
                    <ui-checkbox-group type="label" v-model="searchParams.visitTargetList" :list="visitTargetList"></ui-checkbox-group>
                </crm-m-search-item>
                <crm-m-search-item title="线索/商机编号" type="vertical" vkey="bussinessChanceNo">
                    <ui-input placeholder="请输入" v-model="searchParams.bussinessChanceNo" maxlength="50"></ui-input>
                </crm-m-search-item>
                <crm-m-search-item title="沟通记录" type="vertical" vkey="commucateContent">
                    <ui-input placeholder="请输入" v-model="searchParams.commucateContent" maxlength="50"></ui-input>
                </crm-m-search-item>
            </template>
            <template v-slot:list-item="{ item }">
                <div class="visit-list-item" @click="gotoDetail(item.id)">
                    <div class="visit-title">
                        <div class="title-icon lv-S" v-if="item.visitCustomerVo && item.visitCustomerVo.customerGrade" :class="'lv-' + (item.visitCustomerVo ? item.visitCustomerVo.customerGrade : '')"></div>
                        <div class="title-txt text-line-1">{{ item.customerName }}</div>
                        <div class="title-status status-1" :class="'status-' + item.visitRecordStatus">{{ {1: '待拜访', 2: '拜访中', 3: '已拜访', 4: '已关闭', }[item.visitRecordStatus] }}</div>
                    </div>
                    <div class="visit-tip">
                        <div class="visit-no">{{ item.visitRecordNo }}</div>
                        <div class="visit-time">{{ item.addTime }}</div>
                    </div>
                    <div class="visit-info">
                        <div class="info-item">
                            <div class="info-label">拜访人：</div>
                            <div class="info-txt">
                                <ui-user :avatar="item.visitorPic" :name="item.visitorName" color="#000"></ui-user>
                            </div>
                        </div>
                        <div class="info-item" v-if="item.tongXingUserNames">
                            <div class="info-label">同行人：</div>
                            <div class="info-txt">{{ item.tongXingUserNames }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">拜访目标：</div>
                            <div class="info-txt">{{ item.visitTargetStr || '-' }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">拜访地区：</div>
                            <div class="info-txt">{{ getAddressStr(item) }}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">计划时间：</div>
                            <div class="info-txt">{{ item.planVisitDate }}</div>
                        </div>
                        <div class="info-item" v-if="item.completeDatetime">
                            <div class="info-label">完成时间：</div>
                            <div class="info-txt">{{ item.completeDatetime }}</div>
                        </div>
                    </div>
                </div>
            </template>
        </crm-list-container>
    </div>
    <script src="https://wwcdn.weixin.qq.com/node/open/js/wecom-jssdk-2.0.2.js"></script>
    <script src="/mstatic/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/vuedraggable.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/mui/mui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/pages/visitRecord/list.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
