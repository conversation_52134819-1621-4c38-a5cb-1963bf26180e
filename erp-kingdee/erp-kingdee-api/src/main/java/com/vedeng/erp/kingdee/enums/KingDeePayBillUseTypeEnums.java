package com.vedeng.erp.kingdee.enums;

/**
 * <AUTHOR>
 * @desc 金蝶付款单付款用途类型
 */
public enum KingDeePayBillUseTypeEnums {
    BUY_PAY("SFKYT08_SYS","采购付款"),
    PRE_PAY("SFKYT09_SYS","预付款"),
    EXPENSE_CLAIM_PAY("SFKYT10_SYS","费用报销"),
    BORROW_PAY("SFKYT11_SYS","个人借款"),
    OTHER_PAY("SFKYT12_SYS","其他支出"),
    BANK_PUNISH_PAY("SFKYT13_SYS","银行罚息"),
    BANK_EXPENSE_PAY("SFKYT14_SYS","银行手续费"),
    PUNISH_PAY("SFKYT15_SYS","罚款支出"),
    TAX_PAY("SFKYT16_SYS","缴纳税费"),
    BILL_PAY("SFKYT17_SYS","购买发票"),
    DONATE_PAY("SFKYT18_SYS","捐赠支出"),
    SALARY_PAY("SFKYT19_SYS","工资发放"),
    BEHALF_PAY("SFKYT20_SYS","代付款项"),
    FUND_UP_PAY("SFKYT21_SYS","资金上划"),
    EXPENSE_BORROW_PAY("SFKYT23_SYS","费用借款"),
    CREDIT_PAY("SFKYT25_SYS","信贷付款"),
    INTEREST_PAY("SFKYT26_SYS","利息支出"),
    FUND_TURN_PAY("SFKYT28_SYS","资金调拨付款"),
    REAL_PAY("SFKYT29_SYS","实报实付"),
    EXPENSE_PRE_PAY("SFKYT42_SYS","费用预支"),
    ENSURE_PAY("SFKYT43_SYS","保证金支出"),
    ALI_PAY("JSFS32_SYS","支付宝"),
    WE_CHAT_PAY("JSFS31_SYS","微信"),
    WIRE_TRANSFER("JSFS04_SYS","电汇"),
    HIRE_PAY("SFKYT44_SYS","支付租金");
    /**
     * 编码
     */
    private final String code;

    /**
     * 名称
     */
    private final String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    KingDeePayBillUseTypeEnums(String code,String name){
        this.code = code;
        this.name = name;
    }
}
