<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.QuoteorderConsultDetailMapper">

    <insert id="insert">
        insert into T_QUOTEORDER_CONSULT_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="quoteorderId != null">
                QUOTEORDER_ID,
            </if>
            <if test="quoteorderConsultId != null">
                QUOTEORDER_CONSULT_ID,
            </if>
            <if test="skuNo != null">
                SKU_NO,
            </if>
            <if test="otherContent != null">
                OTHER_CONTENT,
            </if>
            <if test="quoteConsultType != null">
                QUOTE_CONSULT_TYPE,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="quoteorderId != null">
                #{quoteorderId,jdbcType=INTEGER},
            </if>
            <if test="quoteorderConsultId != null">
                #{quoteorderConsultId,jdbcType=INTEGER},
            </if>
            <if test="skuNo != null">
                #{skuNo,jdbcType=VARCHAR},
            </if>
            <if test="otherContent != null">
                #{otherContent,jdbcType=VARCHAR},
            </if>
            <if test="quoteConsultType != null">
                #{quoteConsultType,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert" parameterType="java.util.List">
        insert into T_QUOTEORDER_CONSULT_DETAIL
            (
                QUOTEORDER_ID,
                QUOTEORDER_CONSULT_ID,
                SKU_NO,
                OTHER_CONTENT,
                QUOTE_CONSULT_TYPE,
                CREATOR,
                ADD_TIME
            )
        VALUES
        <foreach collection ="list" item="quoteorderConsultDetail" index="index" separator =",">
            (
                #{quoteorderConsultDetail.quoteorderId,jdbcType=INTEGER},
                #{quoteorderConsultDetail.quoteorderConsultId,jdbcType=INTEGER},
                #{quoteorderConsultDetail.skuNo,jdbcType=VARCHAR},
                #{quoteorderConsultDetail.otherContent,jdbcType=VARCHAR},
                #{quoteorderConsultDetail.quoteConsultType,jdbcType=INTEGER},
                #{quoteorderConsultDetail.creator,jdbcType=INTEGER},
                #{quoteorderConsultDetail.addTime,jdbcType=BIGINT}
            )
        </foreach >
    </insert>
    <delete id="deleteAll">
        DELETE FROM T_QUOTEORDER_CONSULT_DETAIL
    </delete>


</mapper>