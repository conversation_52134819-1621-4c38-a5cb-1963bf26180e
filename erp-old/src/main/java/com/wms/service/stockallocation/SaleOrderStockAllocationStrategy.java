package com.wms.service.stockallocation;

import com.alibaba.fastjson.JSON;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.service.WarehouseStockService;
import com.wms.constant.LogicalEnum;
import com.wms.dto.StockAllocationRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 销售单相关的库存分配策略
 */
@Service
public class SaleOrderStockAllocationStrategy extends AbstractStockAllocationStrategy {

    private static final Logger LOGGER = LoggerFactory.getLogger(SaleOrderStockAllocationStrategy.class);

    @Autowired
    private WarehouseStockService warehouseStockService;

    private List<Integer> stockAllocationOrder = Arrays.asList(
            LogicalEnum.JXQ.getLogicalWarehouseId(),
            LogicalEnum.HG.getLogicalWarehouseId()
    );

    @Override
    protected List<Integer> getStockAllocationOrder() {
        return stockAllocationOrder;
    }

    @Override
    protected Map<Integer, WarehouseStock> getLogicalStockMapInfo(StockAllocationRequest stockAllocationRequest) {

        List<String> skuNoList = Arrays.asList(stockAllocationRequest.getSku());

        Map<Integer, WarehouseStock> result = new HashMap<>();

        List<WarehouseStock> warehouseStockList = warehouseStockService.getLogicalStockInfo(skuNoList).get(stockAllocationRequest.getSku());

        LOGGER.info("从库存服务获取到的库存信息:" + JSON.toJSONString(warehouseStockList));


        stockAllocationOrder.stream().forEach(logicalWarehouseId ->{

            WarehouseStock warehouseStock = new WarehouseStock();
            warehouseStock.setLogicalWarehouseId(logicalWarehouseId);
            warehouseStock.setSku(stockAllocationRequest.getSku());

            WarehouseStock stock = warehouseStockList
                    .stream()
                    .filter(wc -> {
                        return wc.getLogicalWarehouseId().equals(logicalWarehouseId);
                    })
                    .findFirst()
                    .orElse(null);

            warehouseStock.setAvailableStockNum(stock == null ? 0 : stock.getAvailableStockNum());
            result.put(logicalWarehouseId,warehouseStock);
        });

        return result;
    }

}
