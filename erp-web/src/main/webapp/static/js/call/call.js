
// 获取当前脚本的URL
var scripts = document.getElementsByTagName('script');
var currentScriptUrl = scripts[scripts.length - 1].src;

// 从URL中提取版本号
var rndMatch = currentScriptUrl.match(/rnd=([^&]+)/);
var rnd = rndMatch ? rndMatch[1] : Math.random();

var new_element=document.createElement("script");
new_element.setAttribute("type","text/javascript");
new_element.setAttribute("src","/static/js/call/call1.js?rnd=" + rnd);
document.getElementsByTagName('HEAD').item(0).appendChild(new_element);

var nccSwitch = 1;

$(document).ready(function(){
	if (navigator.userAgent.toLowerCase().indexOf('firefox') > 0){
		nccSwitch = 0;
	}
})

/**
 * 呼出电话前数据处理
 * @param phone 被叫号码
 * @param traderId 交易者id
 * @param traderType 交易者类型 1客户2供应商
 * @param callType 呼出订单类型 1商机2销售订单3报价4售后5采购订单//没有就传 0
 * @param orderId 订单id 没有就传 0
 * @param traderContactId 联系人ID 没有就传 0
 * @returns
 */
function ezcall(phone){
	checkLogin();
	calloutWS(phone,0,0,0,0,0);
}
function callout(phone,traderId,traderType,callType,orderId,traderContactId){
	checkLogin();
	traderId=default0(traderId);
	traderType=default0(traderType);
	callType=default0(callType);
	orderId=default0(orderId);
	traderContactId=default0(traderContactId);
	if(nccSwitch){
		calloutWS(phone,traderId,traderType,callType,orderId,traderContactId);
		return;
	}
	calloutOCX(phone,traderId,traderType,callType,orderId,traderContactId);
}

function default0(param){
	if(param === '' || param === null){
		param = 0;
	}
	return param;
}

function calloutOCX(phone,traderId,traderType,callType,orderId,traderContactId){
	console.log('calloutOCX phone:' + phone);
	if(self.parent.document.getElementById("callcenter") != null){
		var obj = self.parent.document.getElementById("callcenter").contentWindow;
		$.ajax({
			type:'post',
			url:page_url+'/system/call/getphoneinfo.do',
			data:{phone:phone},
			dataType:'json',
			success:function(data){
				if(data.code ==403){
					layer.alert("登录已经过期，您没有操作权限，申请开通权限请联系研发部Aadi")
					return;
				}
				self.parent.document.getElementById("call_traderId").value=traderId;
				self.parent.document.getElementById("call_traderType").value=traderType;
				self.parent.document.getElementById("call_callType").value=callType;
				self.parent.document.getElementById("call_orderId").value=orderId;
				$(window.parent.document).find("#callcenter").contents().find("input[name='orderTypeCallFlag']").val(callType);
				$(window.parent.document).find("#callcenter").contents().find("input[name='orderIdCallFlag']").val(orderId);
				self.parent.document.getElementById("call_traderContactId").value=traderContactId;
				console.log('callout ----'+data.data)
				//获取主叫 设置主叫
				var pp='';
				console.log("current select:"+$(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val())
				if($(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val()==1){
					//  Trace("主叫号码：号码池内号码");
					pp = obj.document.getElementById("csSoftPhone").getRollingANI();
					console.log('getRollingANI  号码池内号码 ' + pp)
					$(window.parent.document).find("#callcenter").contents().find("#ttNumber").val('');
					$(window.parent.document).find("#callcenter").contents().find("#ttNumberCount").val(0);
					signPpNumber(pp);
				}
				if($(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val()==2){
					if( $(window.parent.document).find("#callcenter").contents().find("#ttNumber").val()!=''){
						pp=$(window.parent.document).find("#callcenter").contents().find("#ttNumber").val();
						console.log('getRollingANI from erp ' + pp)
					}else{
						alert("无主叫号码，有弹窗")
						//pp = obj.document.getElementById("csSoftPhone").getRollingANI();
						console.log('getRollingANI 号码池内号码 ' + pp)
					}
					signPpNumber(pp);
				}
				//$.get('/getRollingANI_from_erp_'+$(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val()+"_"  + pp+"_"+data.data)

				$(window.parent.document).find("#callcenter").contents().find("#currentTtNumber").val(pp);
				if($(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val()==1){
					$(window.parent.document).find("#callcenter").contents().find("#phonepool").html(pp);
				}
				obj.document.getElementById("csSoftPhone").setCoInfo("DIALPREPARE_ANI", pp);

				console.log("DIALPREPARE_ANI"+pp);

				obj.document.getElementById("csSoftPhone").Dial(data.data,"");

			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}
}


/**
 * 呼出电话前数据处理(无弹窗)
 * @param phone 被叫号码
 * @param traderId 交易者id
 * @param traderType 交易者类型 1客户2供应商
 * @param callType 呼出订单类型 1商机2销售订单3报价4售后5采购订单//没有就传 0
 * @param orderId 订单id 没有就传 0
 * @param traderContactId 联系人ID 没有就传 0
 * @returns
 */

//TODO 暂时不做升级，因为是内部通话快捷方式
function calloutNoScreen(phone,traderId,traderType,callType,orderId,traderContactId){
	if (nccSwitch){
		calloutNoScreenWS(phone,traderId,traderType,callType,orderId,traderContactId);
		return ;
	}
	checkLogin();
	phone = phone.replace(/^025/,"");
	if(traderId == '' || traderId == null){
		traderId = 0;
	}
	if(traderType == '' || traderType == null){
		traderType = 0;
	}
	if(callType == '' || callType == null){
		callType = 0;
	}
	if(orderId == '' || orderId == null){
		orderId = 0;
	}
	if(traderContactId == '' || traderContactId == null){
		traderContactId = 0;
	}
	console.log("不打开窗口"+call_screen)
	$.get("/static/js/call/test.js?calloutNoScreenWS="+$('#agentID').val()+Math.random());
	if(self.parent.document.getElementById("callcenter") != null){
		var obj = self.parent.document.getElementById("callcenter").contentWindow;
		$.ajax({
			type:'post',
			url:page_url+'/system/call/getphoneinfo.do',
			data:{phone:phone},
			dataType:'json',
			success:function(data){
				self.parent.document.getElementById("call_traderId").value=traderId;
				self.parent.document.getElementById("call_traderType").value=traderType;
				self.parent.document.getElementById("call_callType").value=callType;
				self.parent.document.getElementById("call_orderId").value=orderId;
				self.parent.document.getElementById("call_traderContactId").value=traderContactId;
				self.parent.document.getElementById("call_screen").value='0';
				console.log('calloutNoScreen'+data.data)


				//获取主叫 设置主叫
				var pp='';
				console.log("calloutNoScreen current select:"+$(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val())
				if($(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val()==1){
					//  Trace("主叫号码：号码池内号码");
					pp = obj.document.getElementById("csSoftPhone").getRollingANI();
					console.log('calloutNoScreen getRollingANI  号码池内号码 ' + pp)
					$(window.parent.document).find("#callcenter").contents().find("#ttNumber").val('');
					$(window.parent.document).find("#callcenter").contents().find("#ttNumberCount").val(0);
				}
				if($(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val()==2){
					if( $(window.parent.document).find("#callcenter").contents().find("#ttNumber").val()!=''){
						pp=$(window.parent.document).find("#callcenter").contents().find("#ttNumber").val();
						console.log('calloutNoScreen getRollingANI from erp ' + pp)
					}else{
						alert("无主叫号码，无弹窗")
						//pp = obj.document.getElementById("csSoftPhone").getRollingANI();
						console.log('calloutNoScreen getRollingANI 号码池内号码 ' + pp)
					}
				}
				console.log('calloutNoScreen getRollingANI from erp2 '  + pp)
				//$.get('/getRollingANI_from_erp_'+pp)

				$(window.parent.document).find("#callcenter").contents().find("#currentTtNumber").val(pp);

				if($(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val()==1){
					$(window.parent.document).find("#callcenter").contents().find("#phonepool").html(pp);
				}

				obj.document.getElementById("csSoftPhone").setCoInfo("DIALPREPARE_ANI", pp);


				obj.document.getElementById("csSoftPhone").Dial(data.data,"");
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}
}
/**
 * 呼出电话弹屏
 * @param phone 被叫号码
 * @param coid 录音coid
 * @returns
 */
//TODO 新版本 迁移至 wssp.js/csSoftPhone_evtDialBegin ，因为此js只在 call/index.jsp中用到
function calloutScreen(phone,coid){
	if (nccSwitch){
		calloutScreenWS(phone,coid);
		return ;
	}
	checkLogin();
	var obj = self.parent.document.getElementById("callcenter").contentWindow;
	
	var coid = obj.document.getElementById("csSoftPhone").getCOInfo("COID");
	var call_traderId = self.parent.document.getElementById("call_traderId").value != undefined ? self.parent.document.getElementById("call_traderId").value : 0;
	var call_traderType = self.parent.document.getElementById("call_traderType").value != undefined ? self.parent.document.getElementById("call_traderType").value: 0;
	var call_callType = self.parent.document.getElementById("call_callType").value != undefined ? self.parent.document.getElementById("call_callType").value: 0;
	var call_orderId = self.parent.document.getElementById("call_orderId").value != undefined ? self.parent.document.getElementById("call_orderId").value: 0;
	var call_traderContactId = self.parent.document.getElementById("call_traderContactId").val != undefined ? self.parent.document.getElementById("call_traderContactId").value: 0;
	var call_screen = self.parent.document.getElementById("call_screen").value != undefined ? self.parent.document.getElementById("call_screen").value: 0;
	
	self.parent.document.getElementById("call_traderId").value=0;
	self.parent.document.getElementById("call_traderType").value=0;
	self.parent.document.getElementById("call_callType").value=0;
	self.parent.document.getElementById("call_orderId").value=0;
	self.parent.document.getElementById("call_traderContactId").value=0;
	self.parent.document.getElementById("call_screen").value = 1;
	// if(phone.startsWith('09')){
	// 	$(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val(1)
	// }else{
	// 	$(window.parent.document).find("#callcenter").contents().find("select[name='mycall']").val(0)
	// }
	console.log("打开窗口"+call_screen+"\t"+self.parent.document.getElementById("call_screen").val+"\t"+self.parent.document.getElementById("call_screen").value)
	if(call_screen == 1){
		self.parent.callIndex = self.parent.layer.myopen({
			type: 2,
			shadeClose: false, //点击遮罩关闭
			closeBtn: false,
			area: ['880px','531px'],
			title: false,
			content: ['./system/call/getcallout.do?traderId='+call_traderId+'&traderType='+call_traderType+'&callType='+call_callType+'&orderId='+call_orderId+'&coid='+coid+'&traderContactId='+call_traderContactId+'&phone='+phone,'no'],
			success: function(layero, index) {
				//layer.iframeAuto(index);
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}else{
		$.ajax({
			type:'get',
			url:page_url+'/system/call/getcallout.do?traderId='+call_traderId+'&traderType='+call_traderType+'&callType='+call_callType+'&orderId='+call_orderId+'&coid='+coid+'&traderContactId='+call_traderContactId+'&phone='+phone,
			dataType:'json',
			success:function(data){
				//
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}
}

/**
 * 来电弹屏
 * @param phone
 * @returns
 */
function callin(phone){
	if (nccSwitch){
		callinWS(phone);
		return ;
	}
	checkLogin();
	var obj = self.parent.document.getElementById("callcenter").contentWindow
	var coid = obj.document.getElementById("csSoftPhone").getCOInfo("COID");
	self.parent.callIndex = self.parent.layer.myopen({
        type: 2,
        shadeClose: false, //点击遮罩关闭
        closeBtn: false,
        area: ['880px','531px'],
        title: false,
        content: ['./system/call/getcallin.do?callType=1&callFrom=1&coid='+coid+'&phone='+phone,'no'],
        success: function(layero, index) {
            //layer.iframeAuto(index);
        },
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	
    });
}

/**
 * 新增联系
 * @param phone 被叫号码
 * @param traderId 交易者id
 * @param traderType 交易者类型
 * @param callType 呼出订单类型
 * @param orderId 订单id
 * @param traderContactId 联系人ID
 * @param callFrom 沟通方式 0呼入1呼出
 * @returns
 */
function addComm(phone,traderId,traderType,callType,orderId,traderContactId,callFrom,coid){
	if (nccSwitch){
		addCommWS(phone,traderId,traderType,callType,orderId,traderContactId,callFrom,coid);
		return ;
	}
	checkLogin();
	if(traderId == '' || traderId == null){
		traderId = 0;
	}
	if(traderType == '' || traderType == null){
		traderType = 0;
	}
	if(callType == '' || callType == null){
		callType = 0;
	}
	if(orderId == '' || orderId == null){
		orderId = 0;
	}
	if(traderContactId == '' || traderContactId == null){
		traderContactId = 0;
	}
	if(callFrom == '' || callFrom == null){
		callFrom = 0;
	}
	var obj = self.parent.document.getElementById("callcenter").contentWindow
	self.parent.callComm = self.parent.layer.myopen({
        type: 2,
        shadeClose: false, //点击遮罩关闭
        closeBtn: false,
        area: ['880px','531px'],
        title: false,
        content: ['./system/call/getaddcomm.do?traderId='+traderId+'&traderType='+traderType+'&callType='+callType+'&orderId='+orderId+'&coid='+coid+'&traderContactId='+traderContactId+'&callFrom='+callFrom+'&phone='+phone],
        success: function(layero, index) {
            //layer.iframeAuto(index);
        },
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
    });
}

/**
 * 售后新增商机
 * @param phone
 * @param traderId
 * @returns
 */
function addbussinesschance(phone,traderId){
	checkLogin();
	if(traderId == '' || traderId == null){
		traderId = 0;
	}
	var obj = self.parent.document.getElementById("callcenter").contentWindow
	self.parent.callComm = self.parent.layer.myopen({
        type: 2,
        shadeClose: false, //点击遮罩关闭
        closeBtn: false,
        area: ['880px','531px'],
        title: false,
        content: ['./system/call/getaddbussinesschance.do?traderId='+traderId+'&phone='+phone],
        success: function(layero, index) {
            //layer.iframeAuto(index);
        },
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
    });
}

/**
 * 查看客户商机
 * @param phone
 * @returns
 */
function showEnquiry(phone){
	checkLogin();
	var obj = self.parent.document.getElementById("callcenter").contentWindow
	self.parent.callBussincessChance = self.parent.layer.myopen({
        type: 2,
        shadeClose: false, //点击遮罩关闭
        closeBtn: false,
        area: ['880px','531px'],
        title: false,
        content: ['./system/call/getbussincesschance.do?phone='+phone],
        success: function(layero, index) {
            //layer.iframeAuto(index);
        },
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
    });
}

/**
 * 录音播放
 * @param url
 * @returns
 */
function playrecord(url){
	checkLogin();
	url = encodeURIComponent(url);
	if(url != ''){
		self.parent.layer.myopen({
			type: 2,
			shadeClose: false, //点击遮罩关闭
			area: ['420px','80px'],
			title: false,
			content: ['./system/call/getrecordpaly.do?url='+url],
			success: function(layero, index) {
				//layer.iframeAuto(index);
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}
	
}

/**
 * 添加沟通记录
 *
 */
function addComrecord() {
	if (nccSwitch){
		addComrecordWS();
		return ;
	}
	var orderTypeCallFlag = $('#orderTypeCallFlag').val();
	var orderIdCallFlag = $('#orderIdCallFlag').val();
	var phone = csSoftPhone.getCOInfo("DNIS");
	var coid = csSoftPhone.getCOInfo("COID");
	var communicateTime = $('#communicateTime').val();
	var positType = $('#positType').val();//313 物流
	console.log("start phone："+phone)
	phone = phone.replace("#", "");
	// var tempphone = phone.replace(/\b(0+)/gi, "");
	//
	// var reg = /^1[3456789]{1}\d{9}$/;
	// console.log("end phone："+tempphone)
	// if (!reg.test(tempphone)) {
	// 	//phone = phone;
	// //} else {
	// 	if (tempphone.length > 8) {
	// 		tempphone = '0' + tempphone;
	// 	}
	// }
	console.log('csSoftPhone_evtDialBegin' + phone);

	//是否可关闭沟通记录弹窗
	var canCloseCommunicate = $("#canCloseCommunicate").val();
	console.log('canCloseCommunicate:' + canCloseCommunicate);
	var canCloseCommunicateFlag = canCloseCommunicate == true || canCloseCommunicate == 'true';

	$.ajax({
		type:'post',
		url:page_url+'/system/call/getTraderIdByPhone.do',
		data:{phone:phone,coid:coid,traderType:1},
		dataType:'json',
		success:function(data){
			console.log('返回结果:' + data);
			console.log('listData:' + data.listData );
			console.log('data:' + data.data );
			if(positType && positType == "313"){//如果是物流打电话的情况
				console.log('物流拨号逻辑');
				self.parent.callIndex = self.parent.layer.myopen({
					type: 2,
					shadeClose: false,
					closeBtn: canCloseCommunicateFlag,
					area: ['880px','531px'],
					title: false,
					content: ['/saleCall/callWithExpress.do?phone='+phone+'&coid='+coid+'&callType='+
					orderTypeCallFlag+'&orderId='+orderIdCallFlag+'&communicateTime='+communicateTime],//物流弹框架的地址
					success: function(layero, index) {
						//layer.iframeAuto(index);
					},
					error:function(data){
						if(data.status ==1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
						}
					}
				});
				return;
			}

			if (data.listData == null || data.listData == '' || data.listData .size == 0){
				//电话类型是商机并且不存在联系人的情况
				if (orderTypeCallFlag != undefined && orderTypeCallFlag == 1){
					self.parent.callIndex = self.parent.layer.myopen({
						type: 2,
						shadeClose: false,
						closeBtn: canCloseCommunicateFlag,
						area: ['880px','531px'],
						title: false,
						content: ['/system/call/bussinessCommunicate.do?phone='+phone+'&coid='+coid+'&callType='+
                        orderTypeCallFlag+'&orderId='+orderIdCallFlag+'&communicateTime='+communicateTime],
						success: function(layero, index) {
							//layer.iframeAuto(index);
						},
						error:function(data){
							if(data.status ==1001){
								layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
							}
						}
					});
					return;
				}
				return;
			}
			self.parent.callIndex = self.parent.layer.myopen({
				type: 2,
				shadeClose: false, //点击遮罩关闭
				closeBtn: canCloseCommunicateFlag,
				area: ['880px','531px'],
				title: false,
				content: ['/system/call/addCommunicate.do?phone='+phone+'&coid='+coid+'&callType='+orderTypeCallFlag+
				'&orderId='+orderIdCallFlag+'&communicateTime='+communicateTime],
				success: function(layero, index) {
					//layer.iframeAuto(index);
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			});
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
	$('#orderTypeCallFlag').val('');
	$('#orderIdCallFlag').val('');
}

/**
 * 标记号码池以及主叫号码
 *
 * @param ppNumber
 */
function signPpNumber(ppNumber) {
	try{
		console.log('标记号码池以及主叫号码' + ppNumber);
		$.get("/system/call/static/signPpNumber.do?ppNumber="+ppNumber,function(data){
			console.log('标记号码池 ppNumber:' + ppNumber);
		});
	}catch(e){}
}