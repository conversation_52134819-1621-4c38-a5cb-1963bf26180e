/* 拜访目标 */
Vue.component('ui-form-checkbox', {
    template: `
        <div class="ui-form-checkbox-wrap">
            <div class="form-checkbox-inner" @click="openSlideDialog">
                <ui-placeholder v-if="!labelsShow">{{ placeholder }}</ui-placeholder>
                <div v-else class="form-checkbox-show">
                    <div class="value">{{ labelsShow }}</div>
                </div>
            </div>

            <crm-slide-dialog ref="slideDialog" :title="title">
                <div class="form-checkbox-panel">
                    <ui-checkbox-group
                        :list="list"
                        v-model="formValue"
                        :single-row="true"
                        @change="handlerChange"
                    ></ui-checkbox-group>
                    <div class="slide-dialog-default-footer">
                        <div class="btn-cancel-flex">
                            <vd-ui-button @click="handlerCancel">取消</vd-ui-button>
                        </div>
                        <div class="btn-confirm-flex">
                            <vd-ui-button type="primary" @click="handlerConfirm">确定</vd-ui-button>
                        </div>
                    </div>
                </div>
            </crm-slide-dialog>
        </div>
    `,
    props: {
        // 外层确定的数据，仅展示及初始化
        value: {
            type: Array,
            default: () => ([])
        },
        // 
        list: Array,
        placeholder: {
            type: String
        },

        // slide query
        title: String
    },
    data () {
        return {
            formValue: [], // 组件内value
        }
    },
    watch: {
        // value: {
        //     handler (newV) {
        //         this.formValue = newV;
        //     },
        //     deep: true,
        // },
    },
    computed: {
        labelsShow () {
            let labels = [];
            this.list.forEach(item => {
                if (this.value.includes(item.value)) {
                    labels.push(item.label);
                }
            })
            return labels.join('、');
        }
    },
    mounted() {
       
    },
    methods: {
        openSlideDialog () {
            this.formValue = JSON.parse(JSON.stringify(this.value));
            this.$refs.slideDialog.show();
        },

        handlerChange (data) {
            console.log('formValue:', this.formValue);
        },

        // 确定
        handlerConfirm () {
            // if (!this.formValue.length) {
            //     this.$message({
            //         type: 'error',
            //         message: '请选择地址',
            //     })
            //     return;
            // }
            
            let emitData = Object.assign([], this.formData);
            this.$emit("input", this.formValue); // 修改外层v-model值
            this.$emit('change', emitData);
            setTimeout(()=> {
                this.handlerCancel();
            }, 30);
        },
        // 取消
        handlerCancel () {
            this.$refs.slideDialog.hide();
        },
    }
})