package com.vedeng.infrastructure.kingdee.domain.dto;

import com.vedeng.infrastructure.kingdee.common.sdk.entity.JsonBase;
import lombok.Data;

/**
 * 付款单回单流水自定义查询接口
 * <AUTHOR>
 */
@Data
public class KingDeeSysReportParam extends JsonBase {
    private String formId;
    private String fieldKeys;
    private String schemeId;
    private Integer startRow;
    private Integer limit;
    private boolean IsVerifyBaseDataField = true;
    private QueryModel model;

    @Data
    public static class QueryModel{
        private String FPayBillNo;
    }
}
