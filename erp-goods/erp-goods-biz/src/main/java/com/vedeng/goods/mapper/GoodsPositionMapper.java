package com.vedeng.goods.mapper;

import com.vedeng.goods.domain.entity.GoodsPositionEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository("newGoodsPositionMapper")
public interface GoodsPositionMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(GoodsPositionEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(GoodsPositionEntity record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    GoodsPositionEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(GoodsPositionEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(GoodsPositionEntity record);

    int updateBatchSelective(List<GoodsPositionEntity> list);

    int batchInsert(@Param("list") List<GoodsPositionEntity> list);

    List<GoodsPositionEntity> findAll();


}