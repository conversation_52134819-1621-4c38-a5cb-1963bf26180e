package com.vedeng.erp.kingdee.enums;

public enum KingDeeBankBillFormIdAndContactUnitTypeEnum {
    RECIEVE_BILL_CUSTOMER("AR_RECEIVEBILL","BD_Customer","收款单"),
    RECIEVE_BILL_SUPPLIER("AR_REFUNDBILL","BD_Supplier","收款退款单"),
    PAY_BILL_CUSTOMER("AP_PAYBILL","BD_Supplier","付款单"),
    PAY_BILL_SUPPLIER("AP_REFUNDBILL","BD_Customer","付款退款单"),
    ;
    private String formId;
    private String contactUnitType;
    private String desc;

    KingDeeBankBillFormIdAndContactUnitTypeEnum(String formId, String contactUnitType, String desc) {
        this.formId = formId;
        this.contactUnitType = contactUnitType;
        this.desc=desc;
    }

    public String getFormId() {
        return formId;
    }

    public String getContactUnitType() {
        return contactUnitType;
    }

    public String getDesc() {
        return desc;
    }
}
