package com.vedeng.order.model.query;

import com.vedeng.goods.model.Goods;
import lombok.Data;

/**
 * 报价咨询列表查询条件实体
 *
 * <AUTHOR> [<EMAIL>]
 */
@Data
public class QuoteConsultQuery {

    private final static Integer DEFAULT_PAGE_NO = 1;

    /**
     * 分页查询页码，默认值: {@value #DEFAULT_PAGE_NO}
     */
    private Integer pageNo = DEFAULT_PAGE_NO;

    private Integer pageSize;

    /**
     * 报价单号
     */
    private String quoteorderNo;

    private String sourceQuae;

    /**
     * 1销售咨询  2采购回复
     */
    private Integer type;


    private Integer consultStatus;

    /**
     * 跟单状态：0跟单中 1成单 2失单
     */
    private Integer trackOrderState;

    /**
     * 报价预警
     */
    private Integer purchaserAlarmLevel;

    /**
     * 产品信息
     */
    private Goods goods;

    /**
     * 搜索开始时间
     */
    private String searchBeginTime;

    /**
     * 搜索结束时间
     */
    private String searchEndTime;

}

