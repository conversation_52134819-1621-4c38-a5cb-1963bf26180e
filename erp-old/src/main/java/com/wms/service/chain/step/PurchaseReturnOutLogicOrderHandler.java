package com.wms.service.chain.step;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.constant.ErpConst;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dto.PutSaleOrderGoodsDto;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 采购退货出库 逻辑单处理
 */
@Service
public class PurchaseReturnOutLogicOrderHandler extends AbstractHandlerStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseReturnOutLogicOrderHandler.class);

    @Resource
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        List<PutSaleOrderGoodsDto> outputOrderGoodsList = context.get("outputOrderGoodsList");

        AfterSalesVo afterSaleInfo = context.get("afterSalesInfo");

        List<AfterSalesGoodsVo> afterSalesGoodList =
                this.afterSalesGoodsMapper.getAfterSalesGoodList(afterSaleInfo.getAfterSalesId());

        outputOrderGoodsList.stream().forEach(outputOrder -> {

            WmsLogicalOrdergoods wmsLogicalOrdergoods = new WmsLogicalOrdergoods();

            int realateId = afterSalesGoodList
                    .stream()
                    .filter(afterSalesGood -> {
                        return afterSalesGood.getSku().equals(outputOrder.getSku()) && ErpConst.ZERO.equals(afterSalesGood.getDeliveryDirect());
                    }).map(afterSalesGood -> afterSalesGood.getAfterSalesGoodsId())
                    .findFirst()
                    .orElse(0);

            wmsLogicalOrdergoods.setRelatedId(realateId);

            //采购退货出库
            wmsLogicalOrdergoods.setOperateType(WmsLogicalOperateTypeEnum.PURCHASE_RETURN.getOperateTypeCode());

            wmsLogicalOrdergoods.setSku(outputOrder.getSku());
            wmsLogicalOrdergoods.setGoodsId(Integer.valueOf(outputOrder.getSku().substring(1)));
            wmsLogicalOrdergoods.setNum(Integer.valueOf(outputOrder.getQtyOrdered()));
            wmsLogicalOrdergoods.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(outputOrder.getLotAtt08()));
            wmsLogicalOrdergoods.setOccupyNum(outputOrder.getQtyOrdered());
            wmsLogicalOrdergoods.setAddTime(new Date());
            wmsLogicalOrdergoods.setModeTime(new Date());

            LOGGER.info("采购单退货出库新增逻辑订单========================:" + JSON.toJSONString(wmsLogicalOrdergoods));

            wmsLogicalOrdergoodsMapper.insertSelective(wmsLogicalOrdergoods);

        });
    }

}
