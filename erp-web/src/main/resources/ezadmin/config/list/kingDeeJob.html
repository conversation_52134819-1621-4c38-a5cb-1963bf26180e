<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>金蝶任务</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="kingDeeJob" datasource="erp-datasourcetarget" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
<div id="appendHead">

</div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">作业名称</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="JOB_NAME" placeholder="请输入" style="" alias="bji" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">作业标识</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="JOB_KEY" placeholder="请输入" style="" alias="bji" jdbctype="VARCHAR" data="" datatype="" oper="EQ" ></object>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">执行器创建时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="CREATE_TIME" placeholder="" style="" alias="bje" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">执行器开始执行时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="START_TIME" placeholder="" style="" alias="bje" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">执行器结束时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="END_TIME" placeholder="" style="" alias="bje" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">执行器状态</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="STATUS" placeholder="请输入" style="" alias="bje" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">执行器退出编码</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="EXIT_CODE" placeholder="请输入" style="" alias="bje" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object>
            </div>
        </div>

    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
    </div>

    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th item_name="JOB_NAME" name="JOB_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">作业名称</th>
            <th item_name="JOB_KEY" name="JOB_KEY" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">作业标识</th>
            <th item_name="CREATE_TIME" name="CREATE_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">作业执行器创建时间</th>
            <th item_name="START_TIME" name="START_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">作业执行器开始执行时间</th>
            <th item_name="END_TIME" name="END_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">作业执行器结束时间</th>
            <th item_name="STATUS" name="STATUS" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">作业执行器状态</th>
            <th item_name="EXIT_CODE" name="EXIT_CODE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">作业执行器退出编码</th>
            <th item_name="EXIT_MESSAGE" name="EXIT_MESSAGE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">作业执行器异常描述</th>
            <th item_name="JOB_CONFIGURATION_LOCATION" name="JOB_CONFIGURATION_LOCATION" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">Job本地配置信息</th>

        </tr>
        </thead>
        <tbody>
        <tr>
            <td> express:</td>
            <td colspan="100"><pre id="express" class="layui-code" orderby="order by bji.JOB_INSTANCE_ID desc" groupby="">StringBuilder sql=new StringBuilder("
		  SELECT
	bji.JOB_NAME ,
	bji.JOB_KEY ,
	bje.CREATE_TIME,
	bje.START_TIME,
	bje.END_TIME,
	bje.STATUS,
	bje.EXIT_CODE,
	bje.EXIT_MESSAGE,
	bje.JOB_CONFIGURATION_LOCATION
from
	BATCH_JOB_INSTANCE bji
left join BATCH_JOB_EXECUTION bje on
	bji.JOB_INSTANCE_ID = bje.JOB_INSTANCE_ID
                where 1=1
");



list=search(sql);
return list;</pre></td>
        </tr>
        <tr>
            <td> count:</td>
            <td colspan="100"><pre id="count" class="layui-code"></pre></td>
        </tr>

        </tbody>
    </table>
</div>
<div id="appendFoot">

</div>

<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    });
</script>
</body>
</html>