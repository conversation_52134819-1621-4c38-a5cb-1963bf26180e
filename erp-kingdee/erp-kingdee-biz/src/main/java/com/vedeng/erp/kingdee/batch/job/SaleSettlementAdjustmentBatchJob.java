package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesDto;
import com.vedeng.erp.kingdee.batch.dto.BatchSaleSettlementAdjustmentAndItemDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.processor.BatchAfterSaleSettlementAdjustmentProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchAfterSaleSettlementAdjustmentPushProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchSaleSettlementAdjustmentProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchSaleSettlementAdjustmentPushProcessor;
import com.vedeng.erp.kingdee.batch.writer.BatchSaleSettlementAdjustmentPushWriter;
import com.vedeng.erp.kingdee.batch.writer.BatchSaleSettlementAdjustmentWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Date;

/**
 * @description: 销售正逆向调整单推送金蝶job
 * @author: Suqin
 * @date: 2023/2/17 17:33
 **/
@Configuration
@Slf4j
@SuppressWarnings("all")
public class SaleSettlementAdjustmentBatchJob extends BaseJob {

    @Autowired
    private BatchSaleSettlementAdjustmentProcessor batchSaleOrderAdjustmentProcessor;

    @Autowired
    private BatchSaleSettlementAdjustmentWriter saleSettlementAdjustmentWriter;

    @Autowired
    private BatchSaleSettlementAdjustmentPushProcessor batchSaleSettlementAdjustmentPushProcessor;

    @Autowired
    private BatchSaleSettlementAdjustmentPushWriter batchSaleSettlementAdjustmentPushWriter;

    @Autowired
    private BatchAfterSaleSettlementAdjustmentProcessor batchAfterSaleSettlementAdjustmentProcessor;

    @Autowired
    private BatchAfterSaleSettlementAdjustmentPushProcessor batchAfterSaleSettlementAdjustmentPushProcessor;


    @Autowired
    @Qualifier("sqlSessionFactoryKingDee")
    private SqlSessionFactory sqlSessionFactory;

    public Job saleAdjustmentFlowJob() {
        return jobBuilderFactory.get("saleAdjustmentFlowJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(saleAdjustPrice())
                .next(saleAdjustmentPush())
                .next(afterSaleAdjustPrice())
                .next(afterSaleAdjustmentPush())
                .build();
    }

    /**
     * 销售生成erp调整单入库（正向）
     * @return
     */
    private Step saleAdjustPrice(){
        return stepBuilderFactory.get("正向销售调整单生成")
                .<BatchWarehouseGoodsOutInDto, BatchSaleSettlementAdjustmentAndItemDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleAdjustPriceReader(null, null))
                .listener(baseReadListener)
                .processor(batchSaleOrderAdjustmentProcessor)
                .writer(saleSettlementAdjustmentWriter)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 正向调整单推送金蝶
     * @return
     */
    private Step saleAdjustmentPush(){
        return stepBuilderFactory.get("正向销售调整单推送")
                .<BatchWarehouseGoodsOutInDto, BatchSaleSettlementAdjustmentAndItemDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleAdjustmentPushReader(null, null))
                .listener(baseReadListener)
                .processor(batchSaleSettlementAdjustmentPushProcessor)
                .writer(batchSaleSettlementAdjustmentPushWriter)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 销售售后生成调整单对接金蝶（逆向）
     * @return
     */
    private Step afterSaleAdjustPrice(){
        return stepBuilderFactory.get("逆向销售调整单生成")
                .<BatchAfterSalesDto,BatchSaleSettlementAdjustmentAndItemDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(afterSaleAdjustPriceReader(null, null))
                .listener(baseReadListener)
                .processor(batchAfterSaleSettlementAdjustmentProcessor)
                .writer(saleSettlementAdjustmentWriter)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 销售售后调整单推送金蝶（逆向）
     * @return
     */
    private Step afterSaleAdjustmentPush(){
        return stepBuilderFactory.get("逆向销售调整单推送")
                .<BatchWarehouseGoodsOutInDto,BatchSaleSettlementAdjustmentAndItemDto>chunk(1)
                .faultTolerant()
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(afterSaleAdjustmentPushReader(null, null))
                .listener(baseReadListener)
                .processor(batchAfterSaleSettlementAdjustmentPushProcessor)
                .writer(batchSaleSettlementAdjustmentPushWriter)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> saleAdjustPriceReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                      @Value("#{jobParameters['endTime']}") String endTime) {
        BatchWarehouseGoodsOutInDto queryOutOrder = BatchWarehouseGoodsOutInDto
                .builder()
                .beginTime(StringUtils.isBlank(beginTime) ? null : DateUtil.parseDateTime(beginTime))
                .endTime(StringUtils.isBlank(endTime) ? null : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), "querySaleAdjustPriceNew", queryOutOrder);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> saleAdjustmentPushReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                         @Value("#{jobParameters['endTime']}") String endTime) {
        BatchWarehouseGoodsOutInDto queryOutOrder = BatchWarehouseGoodsOutInDto
                .builder()
                .beginTime(StringUtils.isBlank(beginTime) ? null : DateUtil.parseDateTime(beginTime))
                .endTime(StringUtils.isBlank(endTime) ? null : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), "querySaleAdjustmentPushNew", queryOutOrder);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> afterSaleAdjustmentPushReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                              @Value("#{jobParameters['endTime']}") String endTime) {
        BatchWarehouseGoodsOutInDto queryOutOrder = BatchWarehouseGoodsOutInDto
                .builder()
                .beginTime(StringUtils.isBlank(beginTime) ? null : DateUtil.parseDateTime(beginTime))
                .endTime(StringUtils.isBlank(endTime) ? null : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), "queryAfterSaleAdjustmentPushNew", queryOutOrder);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchAfterSalesDto> afterSaleAdjustPriceReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                  @Value("#{jobParameters['endTime']}") String endTime) {
        BatchAfterSalesDto queryAdjustment = BatchAfterSalesDto
                .builder()
                .beginTime(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchAfterSalesDto.class.getSimpleName(), "queryAfterSaleAdjustPriceNew", queryAdjustment);
    }
}
