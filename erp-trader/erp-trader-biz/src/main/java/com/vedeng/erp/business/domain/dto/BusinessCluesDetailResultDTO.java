package com.vedeng.erp.business.domain.dto;

import com.vedeng.crm.admin.api.dto.tuoke.ZhongbiaoGoods;
import lombok.Data;

import java.util.List;

/**
 * @Author: daniel
 * @Date: 2022/3/14 17 17
 * @Description: 线索详情
 */
@Data
public class BusinessCluesDetailResultDTO {

    /**
     * 标书ID
     */
    private String infoId;

    /**
     * 招标单位
     */
    private String zhaobiaoUnit;


    /**
     * 招标地区
     */
    private String zhaobiaoArea;

    /**
     * 招标性质
     */
    private String zhaobiaoXingzhi;

    /**
     * 单位等级
     */
    private String zhaobiaoLevel;

    /**
     * 单位类型
     */
    private String zhaobiaoType;


    /**
     * 中标商品
     *
     * 品牌
     */
    private String zhongbiaoBrand;

    /**
     * 中标商品
     */
    private String zhongbiaoGoods;

    /**
     * 型号
     */
    private String zhongbiaoModel;

    /**
     * 其余商品信息
     */
    List<BusinessCluesDetailResultDTO> otherGoodsInfo;


}
