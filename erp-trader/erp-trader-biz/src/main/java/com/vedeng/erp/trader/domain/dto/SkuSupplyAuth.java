package com.vedeng.erp.trader.domain.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * T_SKU_SUPPLY_AUTH
 * <AUTHOR>
public class SkuSupplyAuth implements Serializable {
    /**
     * 主键id
     */
    private Integer skuSupplyAuthId;

    /**
     * 供应商ID
     */
    private Integer traderSupplyId;

    /**
     * 品牌ids
     */
    private String brandIds;

    /**
     * 有效期开始时间
     */
    private Date validStartTime;

    /**
     * 有效期结束时间
     */
    private Date validEndTime;

    /**
     * 授权类型
     */
    private Integer authType;

    /**
     * 是否删除0否1是
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date modTime;

    /**
     * 更新者
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;

    public Integer getSkuSupplyAuthId() {
        return skuSupplyAuthId;
    }

    public void setSkuSupplyAuthId(Integer skuSupplyAuthId) {
        this.skuSupplyAuthId = skuSupplyAuthId;
    }

    public Integer getTraderSupplyId() {
        return traderSupplyId;
    }

    public void setTraderSupplyId(Integer traderSupplyId) {
        this.traderSupplyId = traderSupplyId;
    }

    public String getBrandIds() {
        return brandIds;
    }

    public void setBrandIds(String brandIds) {
        this.brandIds = brandIds;
    }

    public Date getValidStartTime() {
        return validStartTime;
    }

    public void setValidStartTime(Date validStartTime) {
        this.validStartTime = validStartTime;
    }

    public Date getValidEndTime() {
        return validEndTime;
    }

    public void setValidEndTime(Date validEndTime) {
        this.validEndTime = validEndTime;
    }

    public Integer getAuthType() {
        return authType;
    }

    public void setAuthType(Integer authType) {
        this.authType = authType;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Date getModTime() {
        return modTime;
    }

    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }
}