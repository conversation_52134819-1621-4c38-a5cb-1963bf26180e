package com.wms.service.processor.other;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.dao.GoodsMapper;
import com.vedeng.logistics.dao.BarcodeMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.Barcode;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.service.WarehouseGoodsOperateLogService;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.InventoryTransferStatusEnum;
import com.wms.constant.InventoryTransferTypeEnum;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dto.WmsInventoryTransferDetailDto;
import com.wms.dto.WmsInventoryTransferDto;
import com.wms.inventorytransfer.dao.InventoryTransferMapper;
import com.wms.inventorytransfer.model.dto.InventoryTransferDetailDto;
import com.wms.inventorytransfer.model.dto.InventoryTransferDto;
import com.wms.inventorytransfer.model.po.InventoryTransferDetailPO;
import com.wms.inventorytransfer.model.po.InventoryTransferPO;
import com.wms.inventorytransfer.model.vo.InventoryTransferDetailVO;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.service.util.WmsCommonUtil;
import com.wms.service.processor.AbstractWMSCalllBackProcessor;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 库存转移单处理器
 *
 * <AUTHOR>
 */
@Service
public class InventoryTransferProcessor extends AbstractWMSCalllBackProcessor<WmsInventoryTransferDto> {
    private static final Logger logger = LoggerFactory.getLogger(InventoryTransferProcessor.class);

    @Autowired
    private InventoryTransferMapper inventoryTransferMapper;

    @Resource
    private GoodsMapper goodsMapper;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Autowired
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Resource
    private WarehouseGoodsOperateLogService warehouseGoodsOperateLogService;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Resource
    private BarcodeMapper barcodeMapper;


    @Value("${stock_url}")
    protected String stockUrl;

    @Override
    protected boolean needIdempotentValidator() {
        return true;
    }

    @Override
    protected String getBusinessKey(WmsInventoryTransferDto requestBean) {
        return requestBean.getTDOCNo();
    }

    @Override
    protected void commonValidator(WmsInventoryTransferDto wmsInventoryTransferDto) throws Exception {
        if (wmsInventoryTransferDto == null || CollectionUtils.isEmpty(wmsInventoryTransferDto.getDetails())) {
            logger.error("库存转移单回传信息异常 单号:{}, wmsInventoryTransferDto:{}" ,wmsInventoryTransferDto.getTDOCNo(), JSON.toJSONString(wmsInventoryTransferDto));
            throw new Exception("库存转移单回传信息异常 wmsInventoryTransferDto:{}" + JSON.toJSONString(wmsInventoryTransferDto));
        }
        if (StringUtil.isBlank(wmsInventoryTransferDto.getUserDefine1())) {
            for (WmsInventoryTransferDetailDto detail : wmsInventoryTransferDto.getDetails()) {
                if (detail.getLotAtt08().equals(LogicalEnum.HDC.getLogicalWarehouseCode()) ||
                        detail.getToLotAtt08().equals(LogicalEnum.HDC.getLogicalWarehouseCode())) {
                    logger.error("WMS主动发起的库存转移单暂不支持活动仓库的相关转移  单号:{},detail:{}",wmsInventoryTransferDto.getTDOCNo() ,JSON.toJSONString(detail));
                    throw new Exception("WMS主动发起的库存转移单暂不支持活动仓库的相关转移 detail:{}" + JSON.toJSONString(detail));
                }
            }
        }
    }

    @Override
    protected void doDealWithRequest(WmsInventoryTransferDto wmsInventoryTransferDto) throws Exception {

        logger.info("完成库存转移单 单号:{},wmsInventoryTransferDto:{}",wmsInventoryTransferDto.getTDOCNo(), JSON.toJSONString(wmsInventoryTransferDto));
        if (wmsInventoryTransferDto != null && StringUtil.isNotBlank(wmsInventoryTransferDto.getUserDefine1())) {
            InventoryTransferPO oldInventoryTransfer =
                    inventoryTransferMapper.getInventoryTransferByNo(WmsCommonUtil.getOriginalOrderNo(wmsInventoryTransferDto.getUserDefine1()));

            //1.更新转移单详情信息
            List<InventoryTransferDetailVO> inventoryTransferDetailVOS = inventoryTransferMapper.getInventoryTransferDetailById(oldInventoryTransfer.getInventoryTransferId());
            if (CollectionUtils.isEmpty(inventoryTransferDetailVOS) || CollectionUtils.isEmpty(wmsInventoryTransferDto.getDetails())) {
                logger.error("库存转移单信息异常 单号:{},inventoryTransferDetailVOS:{},wmsInventoryTransferDetailDto:{}"
                        ,wmsInventoryTransferDto.getTDOCNo(), JSON.toJSONString(inventoryTransferDetailVOS),JSON.toJSONString(wmsInventoryTransferDto.getDetails()));
                throw new Exception("库存转移单信息异常 inventoryTransferDetailVOS:{}" + JSON.toJSONString(inventoryTransferDetailVOS) +
                        "||wmsInventoryTransferDetailDto:{}" + JSON.toJSONString(wmsInventoryTransferDto.getDetails()));
            }

            ArrayList<InventoryTransferDetailDto> inventoryTransferDetailDtos = new ArrayList<>();
            for (WmsInventoryTransferDetailDto wmsInventoryTransferDetailDto : wmsInventoryTransferDto.getDetails()) {
                for (InventoryTransferDetailVO inventoryTransferDetailVO : inventoryTransferDetailVOS) {
                    if (inventoryTransferDetailVO.getInventoryTransferDetailId().equals(Integer.parseInt(wmsInventoryTransferDetailDto.getUserDefine1()))) {
                        InventoryTransferDetailPO inventoryTransferDetailPO = new InventoryTransferDetailPO();
                        inventoryTransferDetailPO.setInventoryTransferDetailId(inventoryTransferDetailVO.getInventoryTransferDetailId());
                        inventoryTransferDetailPO.setTransferNum(wmsInventoryTransferDetailDto.getTOQty().intValue());
                        logger.info("更新库存转移单详情  单号:{},inventoryTransferDetailPO:{}" ,wmsInventoryTransferDto.getTDOCNo(), JSON.toJSONString(inventoryTransferDetailPO));
                        inventoryTransferMapper.updateInventoryTransferDetail(inventoryTransferDetailPO);

                        //2.更新ERP库存占用信息
                        WmsLogicalOrdergoods wmsLogicalOrdergoods = new WmsLogicalOrdergoods();
                        wmsLogicalOrdergoods.setRelatedId(inventoryTransferDetailVO.getInventoryTransferDetailId());
                        wmsLogicalOrdergoods.setOccupyNum(-wmsInventoryTransferDetailDto.getTOQty().intValue());
                        wmsLogicalOrdergoods.setOperateType(WmsLogicalOperateTypeEnum.INVENTORY_TRANSFER_TYPE.getOperateTypeCode());
                        wmsLogicalOrdergoods.setUpdater(1);
                        wmsLogicalOrdergoods.setModeTime(new Date());
                        logger.info("更新ERP库存占用数量 单号:{}, wmsLogicalOrdergoods:{}",wmsInventoryTransferDto.getTDOCNo(), JSON.toJSONString(wmsLogicalOrdergoods));
                        wmsLogicalOrdergoodsMapper.updateOccupyNum(wmsLogicalOrdergoods);

                        //3.更新出入库日志信息
                        setWarehouseGoodsOperateLogInfo(wmsInventoryTransferDetailDto, inventoryTransferDetailVO, wmsInventoryTransferDto.getTDOCNo());
                    }
                }

                //4.添加库存库存回传信息
                addInventoryTransferDetail(inventoryTransferDetailDtos, wmsInventoryTransferDetailDto);
            }


            //5.更新库存转移单信息
            List<InventoryTransferDetailVO> newInventoryTransferDetail =
                    inventoryTransferMapper.getInventoryTransferDetailById(oldInventoryTransfer.getInventoryTransferId());
            Integer status = InventoryTransferStatusEnum.FINISHED.getStatus();
            for (InventoryTransferDetailVO inventoryTransferDetailVO : newInventoryTransferDetail) {
                if (inventoryTransferDetailVO.getNum() - inventoryTransferDetailVO.getTransferNum() > 0) {
                    status = InventoryTransferStatusEnum.ON_GOING.getStatus();
                    break;
                }
            }
            InventoryTransferPO inventoryTransfer = new InventoryTransferPO();
            inventoryTransfer.setInventoryTransferId(oldInventoryTransfer.getInventoryTransferId());
            inventoryTransfer.setStatus(status);
            inventoryTransferMapper.updateInventoryTransfer(inventoryTransfer);

            //6.下传转移信息到Stock
            InventoryTransferDto inventoryTransferDto = new InventoryTransferDto();
            inventoryTransferDto.setActionId(oldInventoryTransfer.getActionId());
            inventoryTransferDto.setStatus(status);
            inventoryTransferDto.setType(oldInventoryTransfer.getType());
            inventoryTransferDto.setInventoryTransferDetailDtos(inventoryTransferDetailDtos);

            Boolean isActionTransfer = false;
            if (oldInventoryTransfer.getType() != null &&
                    oldInventoryTransfer.getType().equals(InventoryTransferTypeEnum.SECKILL_INVENTORY_TRANSFER)) {
                isActionTransfer = true;
            }
            pushStock(inventoryTransferDto, wmsInventoryTransferDto.getUserDefine1(), isActionTransfer,
                    oldInventoryTransfer.getActionId(), false);
        } else {
            if (wmsInventoryTransferDto == null || CollectionUtils.isEmpty(wmsInventoryTransferDto.getDetails())) {
                logger.error("WMS主动发起库存转移单信息不全 单号:{} ,wmsInventoryTransferDto:{}",wmsInventoryTransferDto.getTDOCNo(), JSON.toJSONString(wmsInventoryTransferDto));
                throw new Exception("WMS主动发起库存转移单信息不全 + wmsInventoryTransferDto:{}" + JSON.toJSONString(wmsInventoryTransferDto));
            }
            logger.info("WMS主动发起库存转移单 单号:{},wmsInventoryTransferDto:{}" ,wmsInventoryTransferDto.getTDOCNo(),JSON.toJSONString(wmsInventoryTransferDto));
            //1.WMS主动发起的库存转移
            InventoryTransferPO inventoryTransfer = saveInventoryTransfer(wmsInventoryTransferDto);
            List<InventoryTransferDetailDto> inventoryTransferDetailDtos = saveInventoryDetail(wmsInventoryTransferDto, inventoryTransfer);

            //2.下传转移信息到Stock
            InventoryTransferDto inventoryTransferDto = new InventoryTransferDto();
            inventoryTransferDto.setStatus(InventoryTransferStatusEnum.FINISHED.getStatus());
            inventoryTransferDto.setInventoryTransferDetailDtos(inventoryTransferDetailDtos);
            pushStock(inventoryTransferDto, wmsInventoryTransferDto.getTDOCNo(), false, null, true);
        }

    }

    /**
     * 设置ERP库存信息
     *
     * @param wmsInventoryTransferDetailDto
     * @param inventoryTransferDetailVO
     */
    private void setWarehouseGoodsOperateLogInfo(WmsInventoryTransferDetailDto wmsInventoryTransferDetailDto,
                                                 InventoryTransferDetailVO inventoryTransferDetailVO, String TDOCNo) throws Exception {
        logger.info("设置ERP库存信息 start TDOCNo:{}", TDOCNo);
        if(StringUtil.isBlank(wmsInventoryTransferDetailDto.getToLotAtt11())){
            throw new RuntimeException(TDOCNo+"贝登批次码为空");
        }
        WarehouseGoodsOperateLog warehouseGoodsOperateLogForQuery = new WarehouseGoodsOperateLog();
        warehouseGoodsOperateLogForQuery.setLogicalWarehouseId(inventoryTransferDetailVO.getFromWarehouseId());
        warehouseGoodsOperateLogForQuery.setGoodsId(goodsMapper.getGoodsIdBySku(inventoryTransferDetailVO.getSkuNo()));
        warehouseGoodsOperateLogForQuery.setVedengBatchNumer(wmsInventoryTransferDetailDto.getToLotAtt11());
        List<WarehouseGoodsOperateLog> availableLogicalGoods = warehouseGoodsOperateLogMapper.getAvailableLogicalGoods(warehouseGoodsOperateLogForQuery);

        if (CollectionUtils.isEmpty(availableLogicalGoods)) {
            logger.error("活动转移来源仓库存数据为空 单号" + TDOCNo + "批次码" + wmsInventoryTransferDetailDto.getLotAtt11());
            throw new ShowErrorMsgException("活动转移来源仓库存数据为空 单号" + TDOCNo + "批次码" + wmsInventoryTransferDetailDto.getLotAtt11());
        }
        logger.info("ERP可用库存信息 单号:{},availableLogicalGoods:{}" ,TDOCNo, JSON.toJSONString(availableLogicalGoods));

        Integer transferNum = wmsInventoryTransferDetailDto.getTOQty().intValue();
        for (WarehouseGoodsOperateLog availableLogicalGood : availableLogicalGoods) {
            if (transferNum <= 0) {
                break;
            }
            int updateNum = transferNum > availableLogicalGood.getLastStockNum() ? availableLogicalGood.getLastStockNum() : transferNum;
            WarehouseGoodsOperateLog updatelog = new WarehouseGoodsOperateLog();
            updatelog.setWarehouseGoodsOperateLogId(availableLogicalGood.getWarehouseGoodsOperateLogId());
            updatelog.setNum(availableLogicalGood.getNum() - updateNum);
            updatelog.setLastStockNum(availableLogicalGood.getLastStockNum() - updateNum);
            updatelog.setIsUse(updatelog.getLastStockNum() == 0 ? 1 : 0);
            updatelog.setIsEnable(updatelog.getNum() == 0 ? 0 : 1);
            updatelog.setModTime(System.currentTimeMillis());
            updatelog.setUpdater(2);
            updatelog.setComments("活动转移来源仓" + TDOCNo);
            updatelog.setTagSources(StringUtil.isBlank(wmsInventoryTransferDetailDto.getLotAtt10()) ?
                    null : wmsInventoryTransferDetailDto.getLotAtt10());
            logger.info("更新活动转移来源仓 单号:{},updateNum:{},updatelog:{}",
                    TDOCNo, updateNum, JSON.toJSONString(updatelog));
            warehouseGoodsOperateLogMapper.updateAvailableLogicalGood(updatelog);

            //目标仓处理
            Integer barcodeId = availableLogicalGood.getBarcodeId();
            if(barcodeId == null){
                //新增贝登条码
                Barcode barcode = new Barcode();
                barcode.setBarcode(wmsInventoryTransferDetailDto.getToLotAtt11());
                //采购
                barcode.setType(3);
                barcode.setDetailGoodsId(inventoryTransferDetailVO.getInventoryTransferDetailId());
                barcode.setGoodsId(goodsMapper.getGoodsIdBySku(wmsInventoryTransferDetailDto.getSKU()));
                //有效
                barcode.setIsEnable(1);
                barcode.setAddTime(System.currentTimeMillis());
                barcode.setModTime(System.currentTimeMillis());
                barcodeMapper.insertSelective(barcode);
                barcodeId = barcode.getBarcodeId();
            }
            availableLogicalGood.setBarcodeId(barcodeId);
            availableLogicalGood.setNum(updateNum);
            availableLogicalGood.setLastStockNum(updateNum);
            availableLogicalGood.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(wmsInventoryTransferDetailDto.getToLotAtt08()));
            availableLogicalGood.setComments("活动转移目标仓" + TDOCNo);
            availableLogicalGood.setModTime(System.currentTimeMillis());
            availableLogicalGood.setTagSources(StringUtil.isBlank(wmsInventoryTransferDetailDto.getLotAtt10()) ?
                    null : wmsInventoryTransferDetailDto.getLotAtt10());

            // VDERP-6942 专项发货SKU回传需关联VP单号，默认传*，WMS通过LotAtt07字段回传
            logger.info("专项发货SKU回传需关联VP单号：{}",wmsInventoryTransferDetailDto.getLotAtt07());
            availableLogicalGood.setDedicatedBuyorderNo(wmsInventoryTransferDetailDto.getLotAtt07());

            logger.info("更新活动转移目标仓 单号:{},updateNum:{},availableLogicalGood:{}" ,
                    TDOCNo, updateNum, JSON.toJSONString(availableLogicalGood));
            warehouseGoodsOperateLogService.insertSelective(availableLogicalGood);
            transferNum -= updateNum;

        }
        if (transferNum > 0){
            logger.error("ERP来源仓库存不足 skuId:{},needNum:{}", warehouseGoodsOperateLogForQuery.getGoodsId(), transferNum);
            throw new ShowErrorMsgException("ERP来源仓库存不足 skuId:{}"+ warehouseGoodsOperateLogForQuery.getGoodsId() + ",needNum:{}" +  transferNum);
        }
    }

    /**
     * 推送转移信息至库存服务
     *
     * @param inventoryTransferDto
     * @param transferNo           转移单号
     * @param isActionTransfer     是否活动移仓单
     * @param actionId             活动ID
     * @param isWmsLaunch          是否为WMS主动发起
     */
    private void pushStock(InventoryTransferDto inventoryTransferDto, String transferNo,
                           Boolean isActionTransfer, Integer actionId, Boolean isWmsLaunch) throws Exception {
        logger.info("completeInventoryTransfer 单号:{},inventoryTransferDto:{}",transferNo, JSON.toJSONString(inventoryTransferDto));
        ArrayList<WarehouseDto> warehouseDtos = new ArrayList<>();
        inventoryTransferDto.getInventoryTransferDetailDtos().stream().forEach(inventoryTransferDetailDto -> {
            if (inventoryTransferDetailDto.getTransferNum() > 0) {
                //1. 释放来源仓库存占用以及来源仓库存转移
                WarehouseDto warehouseDtoFrom = new WarehouseDto();
                warehouseDtoFrom.setSku(inventoryTransferDetailDto.getSkuNo());
                warehouseDtoFrom.setLogicalWarehouseId(inventoryTransferDetailDto.getFromWarehouseId());
                warehouseDtoFrom.setStockNum(-inventoryTransferDetailDto.getTransferNum());
                if (!isWmsLaunch) {
                    warehouseDtoFrom.setOccupyNum(-inventoryTransferDetailDto.getTransferNum());
                }
                warehouseDtoFrom.setActionId(inventoryTransferDto.getActionId());
                warehouseDtos.add(warehouseDtoFrom);

                //2. 进行目标仓库存转移
                WarehouseDto warehouseDtoTo = new WarehouseDto();
                warehouseDtoTo.setSku(inventoryTransferDetailDto.getSkuNo());
                warehouseDtoTo.setLogicalWarehouseId(inventoryTransferDetailDto.getToWarehouseId());
                warehouseDtoTo.setStockNum(inventoryTransferDetailDto.getTransferNum());
                warehouseDtoTo.setActionId(inventoryTransferDto.getActionId());
                warehouseDtos.add(warehouseDtoTo);
            }
        });
        StockInfoDto stockInfoDto = new StockInfoDto();
        stockInfoDto.setWarehouseStockList(warehouseDtos);
        stockInfoDto.setRelatedNo(transferNo);
        if (isActionTransfer) {
            stockInfoDto.setStockType(StockOperateTypeConst.INVENTORY_TRANSFER_ACTION);
            stockInfoDto.setRelatedId(actionId);
        }
        logger.info("库存调整stock库存修改 START 单号:{},stockInfoDto:{}",transferNo ,JSON.toJSONString(stockInfoDto));
        warehouseStockService.updateStockInfo(stockInfoDto);
        logger.info("库存调整stock库存修改 END  SUCCESS 单号:{}",transferNo);
    }


    /**
     * 保存库存转移详情信息
     *
     * @param wmsInventoryTransferDto
     * @param inventoryTransferPO
     * @return
     */
    private List<InventoryTransferDetailDto> saveInventoryDetail(WmsInventoryTransferDto wmsInventoryTransferDto,
                                                                 InventoryTransferPO inventoryTransferPO) throws Exception {
        ArrayList<InventoryTransferDetailDto> inventoryTransferDetailDtos = new ArrayList<>();
        wmsInventoryTransferDto.getDetails().stream().forEach(wmsInventoryTransferDetailDto -> {
            //1.添加库存转移详情信息
            InventoryTransferDetailPO inventoryTransferDetailPO = insertInventoryTransferDetail(inventoryTransferPO, wmsInventoryTransferDetailDto);

            //2.添加ERP库存占用信息
            insertWmsLogicalOrderGoods(wmsInventoryTransferDetailDto, inventoryTransferDetailPO);

            //3.维护ERP库存信息
            try {
                InventoryTransferDetailVO inventoryTransferDetailVO = new InventoryTransferDetailVO();
                inventoryTransferDetailVO.setFromWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(wmsInventoryTransferDetailDto.getLotAtt08()));
                inventoryTransferDetailVO.setSkuNo(wmsInventoryTransferDetailDto.getSKU());
                setWarehouseGoodsOperateLogInfo(wmsInventoryTransferDetailDto, inventoryTransferDetailVO, wmsInventoryTransferDto.getTDOCNo());
            } catch (Exception e) {
                logger.error("WMS主动发起库存转移维护ERP库存信息 ERROR", e);
                throw new RuntimeException("WMS主动发起库存转移维护ERP库存信息 ERROR 单号:" + wmsInventoryTransferDto.getTDOCNo());
            }

            //4.添加Stock更新的详情信息
            addInventoryTransferDetail(inventoryTransferDetailDtos, wmsInventoryTransferDetailDto);
        });
        return inventoryTransferDetailDtos;
    }

    /**
     * 封装库存服务请求信息
     *
     * @param inventoryTransferDetailDtos
     * @param wmsInventoryTransferDetailDto
     */
    private void addInventoryTransferDetail(ArrayList<InventoryTransferDetailDto> inventoryTransferDetailDtos,
                                            WmsInventoryTransferDetailDto wmsInventoryTransferDetailDto) {
        logger.info("封装库存服务请求信息  wmsInventoryTransferDetailDto：{}" , JSON.toJSONString(wmsInventoryTransferDetailDto));
        InventoryTransferDetailDto inventoryTransferDetailDto = new InventoryTransferDetailDto();
        inventoryTransferDetailDto.setTransferNum(wmsInventoryTransferDetailDto.getTOQty().intValue());
        inventoryTransferDetailDto.setSkuNo(wmsInventoryTransferDetailDto.getSKU());
        inventoryTransferDetailDto.setFromWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(wmsInventoryTransferDetailDto.getLotAtt08()));
        inventoryTransferDetailDto.setToWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(wmsInventoryTransferDetailDto.getToLotAtt08()));
        inventoryTransferDetailDtos.add(inventoryTransferDetailDto);
    }


    /**
     * 保存库存转移单信息
     *
     * @param wmsInventoryTransferDto
     * @return
     */
    private InventoryTransferPO saveInventoryTransfer(WmsInventoryTransferDto wmsInventoryTransferDto) {
        logger.info("保存库存转移单信息 start 单号:{},wmsInventoryTransferDto:{}",wmsInventoryTransferDto.getTDOCNo(),JSON.toJSONString(wmsInventoryTransferDto));
        InventoryTransferPO inventoryTransfer = new InventoryTransferPO();
        inventoryTransfer.setInventoryTransferNo(wmsInventoryTransferDto.getTDOCNo());
        inventoryTransfer.setType(InventoryTransferTypeEnum.getTypeByCode(wmsInventoryTransferDto.getTDOCType()));
        inventoryTransfer.setStatus(InventoryTransferStatusEnum.FINISHED.getStatus());
        inventoryTransfer.setToWarehouseId(wmsInventoryTransferDto != null &&
                CollectionUtils.isNotEmpty(wmsInventoryTransferDto.getDetails()) ?
                LogicalEnum.getLogicalWarehouseIdByCode(wmsInventoryTransferDto.getDetails().get(0).getToLotAtt08()) : 0);
        inventoryTransfer.setReasons(wmsInventoryTransferDto.getReason());
        inventoryTransfer.setAddTime(System.currentTimeMillis());
        inventoryTransfer.setCreator(1);
        inventoryTransferMapper.insertInventoryTransfer(inventoryTransfer);
        logger.info("保存库存转移单信息 end 单号:{}",wmsInventoryTransferDto.getTDOCNo());
        return inventoryTransfer;
    }

    /**
     * 保存ERP库存占用信息
     *
     * @param wmsInventoryTransferDetailDto
     * @param inventoryTransferDetailPO
     */
    private void insertWmsLogicalOrderGoods(WmsInventoryTransferDetailDto wmsInventoryTransferDetailDto, InventoryTransferDetailPO inventoryTransferDetailPO) {
        logger.info("保存ERP库存占用信息 start  inventoryTransferDetailPO:" + JSON.toJSONString(inventoryTransferDetailPO));
        WmsLogicalOrdergoods wmsLogicalOrdergoods = new WmsLogicalOrdergoods();
        wmsLogicalOrdergoods.setRelatedId(inventoryTransferDetailPO.getInventoryTransferDetailId());
        wmsLogicalOrdergoods.setOperateType(WmsLogicalOperateTypeEnum.INVENTORY_TRANSFER_TYPE.getOperateTypeCode());
        wmsLogicalOrdergoods.setSku(wmsInventoryTransferDetailDto.getSKU());
        wmsLogicalOrdergoods.setGoodsId(goodsMapper.getGoodsIdBySku(wmsInventoryTransferDetailDto.getSKU()));
        wmsLogicalOrdergoods.setNum(wmsInventoryTransferDetailDto.getTOQty().intValue());
        wmsLogicalOrdergoods.setOccupyNum(0);
        wmsLogicalOrdergoods.setDeliveryNum(0);
        wmsLogicalOrdergoods.setArrivalNum(0);
        wmsLogicalOrdergoods.setIsDelete(0);
        wmsLogicalOrdergoods.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(wmsInventoryTransferDetailDto.getLotAtt08()));
        wmsLogicalOrdergoods.setAddTime(new Date());
        wmsLogicalOrdergoods.setCreator(1);
        wmsLogicalOrdergoodsMapper.insertSelective(wmsLogicalOrdergoods);
    }

    /**
     * 保存库存转移单详情信息
     *
     * @param inventoryTransferPO
     * @param wmsInventoryTransferDetailDto
     * @return
     */
    private InventoryTransferDetailPO insertInventoryTransferDetail(InventoryTransferPO inventoryTransferPO, WmsInventoryTransferDetailDto wmsInventoryTransferDetailDto) {
        logger.info("保存库存转移单详情信息 start inventoryTransferPO:{}" , JSON.toJSONString(inventoryTransferPO));
        InventoryTransferDetailPO inventoryTransferDetailPO = new InventoryTransferDetailPO();
        inventoryTransferDetailPO.setInventoryTransferId(inventoryTransferPO.getInventoryTransferId());
        inventoryTransferDetailPO.setSkuNo(wmsInventoryTransferDetailDto.getSKU());
        inventoryTransferDetailPO.setGoodsName(goodsMapper.getGoodNameBySkuNo(wmsInventoryTransferDetailDto.getSKU()));
        inventoryTransferDetailPO.setNum(wmsInventoryTransferDetailDto.getTOQty().intValue());
        inventoryTransferDetailPO.setTransferNum(wmsInventoryTransferDetailDto.getTOQty().intValue());
        inventoryTransferDetailPO.setFromWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(wmsInventoryTransferDetailDto.getLotAtt08()));
        inventoryTransferDetailPO.setToWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(wmsInventoryTransferDetailDto.getToLotAtt08()));
        inventoryTransferDetailPO.setAddTime(System.currentTimeMillis());
        inventoryTransferMapper.insertInventoryTransferDetail(inventoryTransferDetailPO);
        return inventoryTransferDetailPO;
    }
}
