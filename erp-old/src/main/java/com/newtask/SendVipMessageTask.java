package com.newtask;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.trader.model.WebAccount;
import com.vedeng.trader.service.WebAccountService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: erp
 * @description: 会员开通消息提醒
 * @author: addis
 * @create: 2020-03-16 13:29
 **/
@Component
@JobHandler(value = "SendVipMessageTask")
public class SendVipMessageTask extends AbstractJobHandler {


    @Autowired
    WebAccountService webAccountService;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        if(StringUtil.isNotBlank(param)){
            WebAccount webAccount=new WebAccount();
            webAccount.setMobile(param.trim());
            webAccount.setIsSendMessage(0);//未发送消息
            webAccount.setStatus(1);//资质审核通过
            webAccount.setCustomerNature(ErpConst.CUSTOME_RNATURE);//分销
            List<WebAccount> list= webAccountService.SendVipMessageMethod(webAccount);
            XxlJobLogger.log("发送消息用户数："+(list==null?0:list.size()));
        }else{
            WebAccount webAccount=new WebAccount();
            webAccount.setIsSendMessage(0);//未发送消息
            webAccount.setStatus(1);//资质审核通过
            webAccount.setCustomerNature(ErpConst.CUSTOME_RNATURE);//分销
            List<WebAccount> list= webAccountService.SendVipMessageMethod(webAccount);//调用会员提醒发送
            XxlJobLogger.log("发送消息用户数："+(list==null?0:list.size()));
        }
        return SUCCESS;
    }
}
