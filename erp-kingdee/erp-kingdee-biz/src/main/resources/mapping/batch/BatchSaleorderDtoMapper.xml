<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchSaleorderDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderDto">
    <!--@mbg.generated-->
    <!--@Table T_SALEORDER-->
    <id column="SALEORDER_ID" jdbcType="INTEGER" property="saleorderId" />
    <result column="QUOTEORDER_ID" jdbcType="INTEGER" property="quoteorderId" />
    <result column="PARENT_ID" jdbcType="INTEGER" property="parentId" />
    <result column="SALEORDER_NO" jdbcType="VARCHAR" property="saleorderNo" />
    <result column="M_SALEORDER_NO" jdbcType="VARCHAR" property="mSaleorderNo" />
    <result column="ORDER_TYPE" jdbcType="BOOLEAN" property="orderType" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="SOURCE" jdbcType="BOOLEAN" property="source" />
    <result column="CREATOR_ORG_ID" jdbcType="INTEGER" property="creatorOrgId" />
    <result column="CREATOR_ORG_NAME" jdbcType="VARCHAR" property="creatorOrgName" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="VALID_ORG_ID" jdbcType="INTEGER" property="validOrgId" />
    <result column="VALID_ORG_NAME" jdbcType="VARCHAR" property="validOrgName" />
    <result column="VALID_USER_ID" jdbcType="INTEGER" property="validUserId" />
    <result column="VALID_STATUS" jdbcType="BOOLEAN" property="validStatus" />
    <result column="VALID_TIME" jdbcType="BIGINT" property="validTime" />
    <result column="END_TIME" jdbcType="BIGINT" property="endTime" />
    <result column="STATUS" jdbcType="BOOLEAN" property="status" />
    <result column="PURCHASE_STATUS" jdbcType="BOOLEAN" property="purchaseStatus" />
    <result column="LOCKED_STATUS" jdbcType="BOOLEAN" property="lockedStatus" />
    <result column="INVOICE_STATUS" jdbcType="BOOLEAN" property="invoiceStatus" />
    <result column="INVOICE_TIME" jdbcType="BIGINT" property="invoiceTime" />
    <result column="PAYMENT_STATUS" jdbcType="INTEGER" property="paymentStatus" />
    <result column="PAYMENT_TIME" jdbcType="BIGINT" property="paymentTime" />
    <result column="DELIVERY_STATUS" jdbcType="BOOLEAN" property="deliveryStatus" />
    <result column="DELIVERY_TIME" jdbcType="BIGINT" property="deliveryTime" />
    <result column="IS_CUSTOMER_ARRIVAL" jdbcType="BOOLEAN" property="isCustomerArrival" />
    <result column="ARRIVAL_STATUS" jdbcType="BOOLEAN" property="arrivalStatus" />
    <result column="ARRIVAL_TIME" jdbcType="BIGINT" property="arrivalTime" />
    <result column="SERVICE_STATUS" jdbcType="BOOLEAN" property="serviceStatus" />
    <result column="HAVE_ACCOUNT_PERIOD" jdbcType="BOOLEAN" property="haveAccountPeriod" />
    <result column="IS_PAYMENT" jdbcType="BOOLEAN" property="isPayment" />
    <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="CUSTOMER_TYPE" jdbcType="INTEGER" property="customerType" />
    <result column="CUSTOMER_NATURE" jdbcType="INTEGER" property="customerNature" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName" />
    <result column="TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="traderContactMobile" />
    <result column="TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="traderContactTelephone" />
    <result column="TRADER_ADDRESS_ID" jdbcType="INTEGER" property="traderAddressId" />
    <result column="TRADER_AREA_ID" jdbcType="INTEGER" property="traderAreaId" />
    <result column="TRADER_AREA" jdbcType="VARCHAR" property="traderArea" />
    <result column="TRADER_ADDRESS" jdbcType="VARCHAR" property="traderAddress" />
    <result column="TRADER_COMMENTS" jdbcType="VARCHAR" property="traderComments" />
    <result column="TAKE_TRADER_ID" jdbcType="INTEGER" property="takeTraderId" />
    <result column="TAKE_TRADER_NAME" jdbcType="VARCHAR" property="takeTraderName" />
    <result column="TAKE_TRADER_CONTACT_ID" jdbcType="INTEGER" property="takeTraderContactId" />
    <result column="TAKE_TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="takeTraderContactName" />
    <result column="TAKE_TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="takeTraderContactMobile" />
    <result column="TAKE_TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="takeTraderContactTelephone" />
    <result column="TAKE_TRADER_ADDRESS_ID" jdbcType="INTEGER" property="takeTraderAddressId" />
    <result column="TAKE_TRADER_AREA_ID" jdbcType="INTEGER" property="takeTraderAreaId" />
    <result column="TAKE_TRADER_AREA" jdbcType="VARCHAR" property="takeTraderArea" />
    <result column="TAKE_TRADER_ADDRESS" jdbcType="VARCHAR" property="takeTraderAddress" />
    <result column="IS_SEND_INVOICE" jdbcType="BOOLEAN" property="isSendInvoice" />
    <result column="INVOICE_TRADER_ID" jdbcType="INTEGER" property="invoiceTraderId" />
    <result column="INVOICE_TRADER_NAME" jdbcType="VARCHAR" property="invoiceTraderName" />
    <result column="INVOICE_TRADER_CONTACT_ID" jdbcType="INTEGER" property="invoiceTraderContactId" />
    <result column="INVOICE_TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="invoiceTraderContactName" />
    <result column="INVOICE_TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="invoiceTraderContactMobile" />
    <result column="INVOICE_TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="invoiceTraderContactTelephone" />
    <result column="INVOICE_TRADER_ADDRESS_ID" jdbcType="INTEGER" property="invoiceTraderAddressId" />
    <result column="INVOICE_TRADER_AREA_ID" jdbcType="INTEGER" property="invoiceTraderAreaId" />
    <result column="INVOICE_TRADER_AREA" jdbcType="VARCHAR" property="invoiceTraderArea" />
    <result column="INVOICE_TRADER_ADDRESS" jdbcType="VARCHAR" property="invoiceTraderAddress" />
    <result column="SALES_AREA_ID" jdbcType="INTEGER" property="salesAreaId" />
    <result column="SALES_AREA" jdbcType="VARCHAR" property="salesArea" />
    <result column="TERMINAL_TRADER_ID" jdbcType="INTEGER" property="terminalTraderId" />
    <result column="TERMINAL_TRADER_NAME" jdbcType="VARCHAR" property="terminalTraderName" />
    <result column="TERMINAL_TRADER_TYPE" jdbcType="INTEGER" property="terminalTraderType" />
    <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType" />
    <result column="FREIGHT_DESCRIPTION" jdbcType="INTEGER" property="freightDescription" />
    <result column="DELIVERY_TYPE" jdbcType="INTEGER" property="deliveryType" />
    <result column="DELIVERY_METHOD" jdbcType="INTEGER" property="deliveryMethod" />
    <result column="LOGISTICS_ID" jdbcType="INTEGER" property="logisticsId" />
    <result column="PAYMENT_TYPE" jdbcType="INTEGER" property="paymentType" />
    <result column="PREPAID_AMOUNT" jdbcType="DECIMAL" property="prepaidAmount" />
    <result column="ACCOUNT_PERIOD_AMOUNT" jdbcType="DECIMAL" property="accountPeriodAmount" />
    <result column="PERIOD_DAY" jdbcType="INTEGER" property="periodDay" />
    <result column="LOGISTICS_COLLECTION" jdbcType="BOOLEAN" property="logisticsCollection" />
    <result column="RETAINAGE_AMOUNT" jdbcType="DECIMAL" property="retainageAmount" />
    <result column="RETAINAGE_AMOUNT_MONTH" jdbcType="INTEGER" property="retainageAmountMonth" />
    <result column="PAYMENT_COMMENTS" jdbcType="VARCHAR" property="paymentComments" />
    <result column="ADDITIONAL_CLAUSE" jdbcType="VARCHAR" property="additionalClause" />
    <result column="LOGISTICS_COMMENTS" jdbcType="VARCHAR" property="logisticsComments" />
    <result column="FINANCE_COMMENTS" jdbcType="VARCHAR" property="financeComments" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="INVOICE_COMMENTS" jdbcType="VARCHAR" property="invoiceComments" />
    <result column="DELIVERY_DIRECT" jdbcType="BOOLEAN" property="deliveryDirect" />
    <result column="SUPPLIER_CLAUSE" jdbcType="VARCHAR" property="supplierClause" />
    <result column="HAVE_ADVANCE_PURCHASE" jdbcType="BOOLEAN" property="haveAdvancePurchase" />
    <result column="ADVANCE_PURCHASE_STATUS" jdbcType="BOOLEAN" property="advancePurchaseStatus" />
    <result column="ADVANCE_PURCHASE_COMMENTS" jdbcType="VARCHAR" property="advancePurchaseComments" />
    <result column="ADVANCE_PURCHASE_TIME" jdbcType="BIGINT" property="advancePurchaseTime" />
    <result column="IS_URGENT" jdbcType="BOOLEAN" property="isUrgent" />
    <result column="URGENT_AMOUNT" jdbcType="DECIMAL" property="urgentAmount" />
    <result column="HAVE_COMMUNICATE" jdbcType="BOOLEAN" property="haveCommunicate" />
    <result column="PREPARE_COMMENTS" jdbcType="VARCHAR" property="prepareComments" />
    <result column="MARKETING_PLAN" jdbcType="VARCHAR" property="marketingPlan" />
    <result column="STATUS_COMMENTS" jdbcType="INTEGER" property="statusComments" />
    <result column="SYNC_STATUS" jdbcType="BOOLEAN" property="syncStatus" />
    <result column="LOGISTICS_API_SYNC" jdbcType="BOOLEAN" property="logisticsApiSync" />
    <result column="LOGISTICS_WXSEND_SYNC" jdbcType="BOOLEAN" property="logisticsWxsendSync" />
    <result column="SATISFY_INVOICE_TIME" jdbcType="BIGINT" property="satisfyInvoiceTime" />
    <result column="SATISFY_DELIVERY_TIME" jdbcType="BIGINT" property="satisfyDeliveryTime" />
    <result column="IS_SALES_PERFORMANCE" jdbcType="BOOLEAN" property="isSalesPerformance" />
    <result column="SALES_PERFORMANCE_TIME" jdbcType="BIGINT" property="salesPerformanceTime" />
    <result column="SALES_PERFORMANCE_MOD_TIME" jdbcType="BIGINT" property="salesPerformanceModTime" />
    <result column="IS_DELAY_INVOICE" jdbcType="BOOLEAN" property="isDelayInvoice" />
    <result column="INVOICE_METHOD" jdbcType="BOOLEAN" property="invoiceMethod" />
    <result column="LOCKED_REASON" jdbcType="VARCHAR" property="lockedReason" />
    <result column="COST_USER_IDS" jdbcType="VARCHAR" property="costUserIds" />
    <result column="OWNER_USER_ID" jdbcType="INTEGER" property="ownerUserId" />
    <result column="INVOICE_EMAIL" jdbcType="VARCHAR" property="invoiceEmail" />
    <result column="PAYMENT_MODE" jdbcType="BOOLEAN" property="paymentMode" />
    <result column="PAY_TYPE" jdbcType="BOOLEAN" property="payType" />
    <result column="IS_APPLY_INVOICE" jdbcType="BOOLEAN" property="isApplyInvoice" />
    <result column="APPLY_INVOICE_TIME" jdbcType="BIGINT" property="applyInvoiceTime" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="ADK_SALEORDER_NO" jdbcType="VARCHAR" property="adkSaleorderNo" />
    <result column="CREATE_MOBILE" jdbcType="VARCHAR" property="createMobile" />
    <result column="BDTRADER_COMMENTS" jdbcType="VARCHAR" property="bdtraderComments" />
    <result column="CLOSE_COMMENTS" jdbcType="VARCHAR" property="closeComments" />
    <result column="BD_MOBILE_TIME" jdbcType="BIGINT" property="bdMobileTime" />
    <result column="WEB_TAKE_DELIVERY_TIME" jdbcType="BIGINT" property="webTakeDeliveryTime" />
    <result column="ACTION_ID" jdbcType="INTEGER" property="actionId" />
    <result column="IS_COUPONS" jdbcType="BOOLEAN" property="isCoupons" />
    <result column="COUPONMONEY" jdbcType="DECIMAL" property="couponmoney" />
    <result column="ORIGINAL_AMOUNT" jdbcType="DECIMAL" property="originalAmount" />
    <result column="EL_SALEORDRE_NO" jdbcType="VARCHAR" property="elSaleordreNo" />
    <result column="IS_PRINTOUT" jdbcType="BOOLEAN" property="isPrintout" />
    <result column="OUT_IS_FLAG" jdbcType="BOOLEAN" property="outIsFlag" />
    <result column="UPDATE_DATA_TIME" jdbcType="TIMESTAMP" property="updateDataTime" />
    <result column="REAL_PAY_AMOUNT" jdbcType="DECIMAL" property="realPayAmount" />
    <result column="REAL_RETURN_AMOUNT" jdbcType="DECIMAL" property="realReturnAmount" />
    <result column="REAL_TOTAL_AMOUNT" jdbcType="DECIMAL" property="realTotalAmount" />
    <result column="SEND_TO_PC" jdbcType="BOOLEAN" property="sendToPc" />
    <result column="RETENTION_MONEY" jdbcType="DECIMAL" property="retentionMoney" />
    <result column="RETENTION_MONEY_DAY" jdbcType="INTEGER" property="retentionMoneyDay" />
    <result column="IS_SAME_ADDRESS" jdbcType="BOOLEAN" property="isSameAddress" />
    <result column="INVOICE_SEND_NODE" jdbcType="BOOLEAN" property="invoiceSendNode" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="NOPAYBACK_AMOUNT" jdbcType="DECIMAL" property="nopaybackAmount" />
    <result column="CONTRACT_URL" jdbcType="VARCHAR" property="contractUrl" />
    <result column="AUTO_AUDIT" jdbcType="INTEGER" property="autoAudit" />
    <result column="IS_RISK" jdbcType="BOOLEAN" property="isRisk" />
    <result column="RISK_COMMENTS" jdbcType="VARCHAR" property="riskComments" />
    <result column="RISK_TIME" jdbcType="BIGINT" property="riskTime" />
    <result column="GROUP_CUSTOMER_ID" jdbcType="INTEGER" property="groupCustomerId" />
    <result column="IS_CONTRACT_RETURN" jdbcType="TINYINT" property="isContractReturn" />
    <result column="IS_DELIVERYORDER_RETURN" jdbcType="TINYINT" property="isDeliveryorderReturn" />
    <result column="DELIVERY_CLAIM" jdbcType="BOOLEAN" property="deliveryClaim" />
    <result column="DELIVERY_DELAY_TIME" jdbcType="BIGINT" property="deliveryDelayTime" />
    <result column="BILL_PERIOD_SETTLEMENT_TYPE" jdbcType="TINYINT" property="billPeriodSettlementType" />
    <result column="IS_NEW" jdbcType="TINYINT" property="isNew" />
    <result column="CONFIRM_STATUS" jdbcType="INTEGER" property="confirmStatus" />
    <result column="CONFIRM_TIME" jdbcType="TIMESTAMP" property="confirmTime" />
    <result column="CONTRACT_NO_STAMP_URL" jdbcType="VARCHAR" property="contractNoStampUrl" />
    <result column="ONLINE_RECEIPT_STATUS" jdbcType="BOOLEAN" property="onlineReceiptStatus" />
    <result column="CONFIRMATION_FORM_UPLOAD" jdbcType="BOOLEAN" property="confirmationFormUpload" />
    <result column="CONFIRMATION_FORM_AUDIT" jdbcType="BOOLEAN" property="confirmationFormAudit" />
    <result column="CONFIRMATION_SUBMIT_TIME" jdbcType="BIGINT" property="confirmationSubmitTime" />
    <result column="PREPARE_REASEON_TYPE" jdbcType="INTEGER" property="prepareReaseonType" />
    <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SALEORDER_ID, QUOTEORDER_ID, PARENT_ID, SALEORDER_NO, M_SALEORDER_NO, ORDER_TYPE, 
    COMPANY_ID, `SOURCE`, CREATOR_ORG_ID, CREATOR_ORG_NAME, ORG_ID, ORG_NAME, USER_ID, 
    VALID_ORG_ID, VALID_ORG_NAME, VALID_USER_ID, VALID_STATUS, VALID_TIME, END_TIME, 
    `STATUS`, PURCHASE_STATUS, LOCKED_STATUS, INVOICE_STATUS, INVOICE_TIME, PAYMENT_STATUS, 
    PAYMENT_TIME, DELIVERY_STATUS, DELIVERY_TIME, IS_CUSTOMER_ARRIVAL, ARRIVAL_STATUS, 
    ARRIVAL_TIME, SERVICE_STATUS, HAVE_ACCOUNT_PERIOD, IS_PAYMENT, TOTAL_AMOUNT, TRADER_ID, 
    CUSTOMER_TYPE, CUSTOMER_NATURE, TRADER_NAME, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, 
    TRADER_CONTACT_MOBILE, TRADER_CONTACT_TELEPHONE, TRADER_ADDRESS_ID, TRADER_AREA_ID, 
    TRADER_AREA, TRADER_ADDRESS, TRADER_COMMENTS, TAKE_TRADER_ID, TAKE_TRADER_NAME, TAKE_TRADER_CONTACT_ID, 
    TAKE_TRADER_CONTACT_NAME, TAKE_TRADER_CONTACT_MOBILE, TAKE_TRADER_CONTACT_TELEPHONE, 
    TAKE_TRADER_ADDRESS_ID, TAKE_TRADER_AREA_ID, TAKE_TRADER_AREA, TAKE_TRADER_ADDRESS, 
    IS_SEND_INVOICE, INVOICE_TRADER_ID, INVOICE_TRADER_NAME, INVOICE_TRADER_CONTACT_ID, 
    INVOICE_TRADER_CONTACT_NAME, INVOICE_TRADER_CONTACT_MOBILE, INVOICE_TRADER_CONTACT_TELEPHONE, 
    INVOICE_TRADER_ADDRESS_ID, INVOICE_TRADER_AREA_ID, INVOICE_TRADER_AREA, INVOICE_TRADER_ADDRESS, 
    SALES_AREA_ID, SALES_AREA, TERMINAL_TRADER_ID, TERMINAL_TRADER_NAME, TERMINAL_TRADER_TYPE, 
    INVOICE_TYPE, FREIGHT_DESCRIPTION, DELIVERY_TYPE, DELIVERY_METHOD, LOGISTICS_ID, 
    PAYMENT_TYPE, PREPAID_AMOUNT, ACCOUNT_PERIOD_AMOUNT, PERIOD_DAY, LOGISTICS_COLLECTION, 
    RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH, PAYMENT_COMMENTS, ADDITIONAL_CLAUSE, LOGISTICS_COMMENTS, 
    FINANCE_COMMENTS, COMMENTS, INVOICE_COMMENTS, DELIVERY_DIRECT, SUPPLIER_CLAUSE, HAVE_ADVANCE_PURCHASE, 
    ADVANCE_PURCHASE_STATUS, ADVANCE_PURCHASE_COMMENTS, ADVANCE_PURCHASE_TIME, IS_URGENT, 
    URGENT_AMOUNT, HAVE_COMMUNICATE, PREPARE_COMMENTS, MARKETING_PLAN, STATUS_COMMENTS, 
    SYNC_STATUS, LOGISTICS_API_SYNC, LOGISTICS_WXSEND_SYNC, SATISFY_INVOICE_TIME, SATISFY_DELIVERY_TIME, 
    IS_SALES_PERFORMANCE, SALES_PERFORMANCE_TIME, SALES_PERFORMANCE_MOD_TIME, IS_DELAY_INVOICE, 
    INVOICE_METHOD, LOCKED_REASON, COST_USER_IDS, OWNER_USER_ID, INVOICE_EMAIL, PAYMENT_MODE, 
    PAY_TYPE, IS_APPLY_INVOICE, APPLY_INVOICE_TIME, ADD_TIME, CREATOR, MOD_TIME, UPDATER, 
    ADK_SALEORDER_NO, CREATE_MOBILE, BDTRADER_COMMENTS, CLOSE_COMMENTS, BD_MOBILE_TIME, 
    WEB_TAKE_DELIVERY_TIME, ACTION_ID, IS_COUPONS, COUPONMONEY, ORIGINAL_AMOUNT, EL_SALEORDRE_NO, 
    IS_PRINTOUT, OUT_IS_FLAG, UPDATE_DATA_TIME, REAL_PAY_AMOUNT, REAL_RETURN_AMOUNT, 
    REAL_TOTAL_AMOUNT, SEND_TO_PC, RETENTION_MONEY, RETENTION_MONEY_DAY, IS_SAME_ADDRESS, 
    INVOICE_SEND_NODE, IS_DELETE, NOPAYBACK_AMOUNT, CONTRACT_URL, AUTO_AUDIT, IS_RISK, 
    RISK_COMMENTS, RISK_TIME, GROUP_CUSTOMER_ID, IS_CONTRACT_RETURN, IS_DELIVERYORDER_RETURN, 
    DELIVERY_CLAIM, DELIVERY_DELAY_TIME, BILL_PERIOD_SETTLEMENT_TYPE, IS_NEW, CONFIRM_STATUS, 
    CONFIRM_TIME, CONTRACT_NO_STAMP_URL, ONLINE_RECEIPT_STATUS, CONFIRMATION_FORM_UPLOAD, 
    CONFIRMATION_FORM_AUDIT, CONFIRMATION_SUBMIT_TIME, PREPARE_REASEON_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_SALEORDER
    where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_SALEORDER
    where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="SALEORDER_ID" keyProperty="saleorderId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SALEORDER (QUOTEORDER_ID, PARENT_ID, SALEORDER_NO, 
      M_SALEORDER_NO, ORDER_TYPE, COMPANY_ID, 
      `SOURCE`, CREATOR_ORG_ID, CREATOR_ORG_NAME, 
      ORG_ID, ORG_NAME, USER_ID, 
      VALID_ORG_ID, VALID_ORG_NAME, VALID_USER_ID, 
      VALID_STATUS, VALID_TIME, END_TIME, 
      `STATUS`, PURCHASE_STATUS, LOCKED_STATUS, 
      INVOICE_STATUS, INVOICE_TIME, PAYMENT_STATUS, 
      PAYMENT_TIME, DELIVERY_STATUS, DELIVERY_TIME, 
      IS_CUSTOMER_ARRIVAL, ARRIVAL_STATUS, ARRIVAL_TIME, 
      SERVICE_STATUS, HAVE_ACCOUNT_PERIOD, IS_PAYMENT, 
      TOTAL_AMOUNT, TRADER_ID, CUSTOMER_TYPE, 
      CUSTOMER_NATURE, TRADER_NAME, TRADER_CONTACT_ID, 
      TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, 
      TRADER_CONTACT_TELEPHONE, TRADER_ADDRESS_ID, 
      TRADER_AREA_ID, TRADER_AREA, TRADER_ADDRESS, 
      TRADER_COMMENTS, TAKE_TRADER_ID, TAKE_TRADER_NAME, 
      TAKE_TRADER_CONTACT_ID, TAKE_TRADER_CONTACT_NAME, 
      TAKE_TRADER_CONTACT_MOBILE, TAKE_TRADER_CONTACT_TELEPHONE, 
      TAKE_TRADER_ADDRESS_ID, TAKE_TRADER_AREA_ID, 
      TAKE_TRADER_AREA, TAKE_TRADER_ADDRESS, IS_SEND_INVOICE, 
      INVOICE_TRADER_ID, INVOICE_TRADER_NAME, INVOICE_TRADER_CONTACT_ID, 
      INVOICE_TRADER_CONTACT_NAME, INVOICE_TRADER_CONTACT_MOBILE, 
      INVOICE_TRADER_CONTACT_TELEPHONE, INVOICE_TRADER_ADDRESS_ID, 
      INVOICE_TRADER_AREA_ID, INVOICE_TRADER_AREA, 
      INVOICE_TRADER_ADDRESS, SALES_AREA_ID, SALES_AREA, 
      TERMINAL_TRADER_ID, TERMINAL_TRADER_NAME, TERMINAL_TRADER_TYPE, 
      INVOICE_TYPE, FREIGHT_DESCRIPTION, DELIVERY_TYPE, 
      DELIVERY_METHOD, LOGISTICS_ID, PAYMENT_TYPE, 
      PREPAID_AMOUNT, ACCOUNT_PERIOD_AMOUNT, PERIOD_DAY, 
      LOGISTICS_COLLECTION, RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH, 
      PAYMENT_COMMENTS, ADDITIONAL_CLAUSE, LOGISTICS_COMMENTS, 
      FINANCE_COMMENTS, COMMENTS, INVOICE_COMMENTS, 
      DELIVERY_DIRECT, SUPPLIER_CLAUSE, HAVE_ADVANCE_PURCHASE, 
      ADVANCE_PURCHASE_STATUS, ADVANCE_PURCHASE_COMMENTS, 
      ADVANCE_PURCHASE_TIME, IS_URGENT, URGENT_AMOUNT, 
      HAVE_COMMUNICATE, PREPARE_COMMENTS, MARKETING_PLAN, 
      STATUS_COMMENTS, SYNC_STATUS, LOGISTICS_API_SYNC, 
      LOGISTICS_WXSEND_SYNC, SATISFY_INVOICE_TIME, 
      SATISFY_DELIVERY_TIME, IS_SALES_PERFORMANCE, 
      SALES_PERFORMANCE_TIME, SALES_PERFORMANCE_MOD_TIME, 
      IS_DELAY_INVOICE, INVOICE_METHOD, LOCKED_REASON, 
      COST_USER_IDS, OWNER_USER_ID, INVOICE_EMAIL, 
      PAYMENT_MODE, PAY_TYPE, IS_APPLY_INVOICE, 
      APPLY_INVOICE_TIME, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, ADK_SALEORDER_NO, 
      CREATE_MOBILE, BDTRADER_COMMENTS, CLOSE_COMMENTS, 
      BD_MOBILE_TIME, WEB_TAKE_DELIVERY_TIME, ACTION_ID, 
      IS_COUPONS, COUPONMONEY, ORIGINAL_AMOUNT, 
      EL_SALEORDRE_NO, IS_PRINTOUT, OUT_IS_FLAG, 
      UPDATE_DATA_TIME, REAL_PAY_AMOUNT, REAL_RETURN_AMOUNT, 
      REAL_TOTAL_AMOUNT, SEND_TO_PC, RETENTION_MONEY, 
      RETENTION_MONEY_DAY, IS_SAME_ADDRESS, INVOICE_SEND_NODE, 
      IS_DELETE, NOPAYBACK_AMOUNT, CONTRACT_URL, 
      AUTO_AUDIT, IS_RISK, RISK_COMMENTS, 
      RISK_TIME, GROUP_CUSTOMER_ID, IS_CONTRACT_RETURN, 
      IS_DELIVERYORDER_RETURN, DELIVERY_CLAIM, DELIVERY_DELAY_TIME, 
      BILL_PERIOD_SETTLEMENT_TYPE, IS_NEW, CONFIRM_STATUS, 
      CONFIRM_TIME, CONTRACT_NO_STAMP_URL, ONLINE_RECEIPT_STATUS, 
      CONFIRMATION_FORM_UPLOAD, CONFIRMATION_FORM_AUDIT, 
      CONFIRMATION_SUBMIT_TIME, PREPARE_REASEON_TYPE
      )
    values (#{quoteorderId,jdbcType=INTEGER}, #{parentId,jdbcType=INTEGER}, #{saleorderNo,jdbcType=VARCHAR}, 
      #{mSaleorderNo,jdbcType=VARCHAR}, #{orderType,jdbcType=BOOLEAN}, #{companyId,jdbcType=INTEGER}, 
      #{source,jdbcType=BOOLEAN}, #{creatorOrgId,jdbcType=INTEGER}, #{creatorOrgName,jdbcType=VARCHAR}, 
      #{orgId,jdbcType=INTEGER}, #{orgName,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER}, 
      #{validOrgId,jdbcType=INTEGER}, #{validOrgName,jdbcType=VARCHAR}, #{validUserId,jdbcType=INTEGER}, 
      #{validStatus,jdbcType=BOOLEAN}, #{validTime,jdbcType=BIGINT}, #{endTime,jdbcType=BIGINT}, 
      #{status,jdbcType=BOOLEAN}, #{purchaseStatus,jdbcType=BOOLEAN}, #{lockedStatus,jdbcType=BOOLEAN}, 
      #{invoiceStatus,jdbcType=BOOLEAN}, #{invoiceTime,jdbcType=BIGINT}, #{paymentStatus,jdbcType=INTEGER},
      #{paymentTime,jdbcType=BIGINT}, #{deliveryStatus,jdbcType=BOOLEAN}, #{deliveryTime,jdbcType=BIGINT}, 
      #{isCustomerArrival,jdbcType=BOOLEAN}, #{arrivalStatus,jdbcType=BOOLEAN}, #{arrivalTime,jdbcType=BIGINT}, 
      #{serviceStatus,jdbcType=BOOLEAN}, #{haveAccountPeriod,jdbcType=BOOLEAN}, #{isPayment,jdbcType=BOOLEAN}, 
      #{totalAmount,jdbcType=DECIMAL}, #{traderId,jdbcType=INTEGER}, #{customerType,jdbcType=INTEGER}, 
      #{customerNature,jdbcType=INTEGER}, #{traderName,jdbcType=VARCHAR}, #{traderContactId,jdbcType=INTEGER}, 
      #{traderContactName,jdbcType=VARCHAR}, #{traderContactMobile,jdbcType=VARCHAR}, 
      #{traderContactTelephone,jdbcType=VARCHAR}, #{traderAddressId,jdbcType=INTEGER}, 
      #{traderAreaId,jdbcType=INTEGER}, #{traderArea,jdbcType=VARCHAR}, #{traderAddress,jdbcType=VARCHAR}, 
      #{traderComments,jdbcType=VARCHAR}, #{takeTraderId,jdbcType=INTEGER}, #{takeTraderName,jdbcType=VARCHAR}, 
      #{takeTraderContactId,jdbcType=INTEGER}, #{takeTraderContactName,jdbcType=VARCHAR}, 
      #{takeTraderContactMobile,jdbcType=VARCHAR}, #{takeTraderContactTelephone,jdbcType=VARCHAR}, 
      #{takeTraderAddressId,jdbcType=INTEGER}, #{takeTraderAreaId,jdbcType=INTEGER}, 
      #{takeTraderArea,jdbcType=VARCHAR}, #{takeTraderAddress,jdbcType=VARCHAR}, #{isSendInvoice,jdbcType=BOOLEAN}, 
      #{invoiceTraderId,jdbcType=INTEGER}, #{invoiceTraderName,jdbcType=VARCHAR}, #{invoiceTraderContactId,jdbcType=INTEGER}, 
      #{invoiceTraderContactName,jdbcType=VARCHAR}, #{invoiceTraderContactMobile,jdbcType=VARCHAR}, 
      #{invoiceTraderContactTelephone,jdbcType=VARCHAR}, #{invoiceTraderAddressId,jdbcType=INTEGER}, 
      #{invoiceTraderAreaId,jdbcType=INTEGER}, #{invoiceTraderArea,jdbcType=VARCHAR}, 
      #{invoiceTraderAddress,jdbcType=VARCHAR}, #{salesAreaId,jdbcType=INTEGER}, #{salesArea,jdbcType=VARCHAR}, 
      #{terminalTraderId,jdbcType=INTEGER}, #{terminalTraderName,jdbcType=VARCHAR}, #{terminalTraderType,jdbcType=INTEGER}, 
      #{invoiceType,jdbcType=INTEGER}, #{freightDescription,jdbcType=INTEGER}, #{deliveryType,jdbcType=INTEGER}, 
      #{deliveryMethod,jdbcType=INTEGER}, #{logisticsId,jdbcType=INTEGER}, #{paymentType,jdbcType=INTEGER}, 
      #{prepaidAmount,jdbcType=DECIMAL}, #{accountPeriodAmount,jdbcType=DECIMAL}, #{periodDay,jdbcType=INTEGER}, 
      #{logisticsCollection,jdbcType=BOOLEAN}, #{retainageAmount,jdbcType=DECIMAL}, #{retainageAmountMonth,jdbcType=INTEGER}, 
      #{paymentComments,jdbcType=VARCHAR}, #{additionalClause,jdbcType=VARCHAR}, #{logisticsComments,jdbcType=VARCHAR}, 
      #{financeComments,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, #{invoiceComments,jdbcType=VARCHAR}, 
      #{deliveryDirect,jdbcType=BOOLEAN}, #{supplierClause,jdbcType=VARCHAR}, #{haveAdvancePurchase,jdbcType=BOOLEAN}, 
      #{advancePurchaseStatus,jdbcType=BOOLEAN}, #{advancePurchaseComments,jdbcType=VARCHAR}, 
      #{advancePurchaseTime,jdbcType=BIGINT}, #{isUrgent,jdbcType=BOOLEAN}, #{urgentAmount,jdbcType=DECIMAL}, 
      #{haveCommunicate,jdbcType=BOOLEAN}, #{prepareComments,jdbcType=VARCHAR}, #{marketingPlan,jdbcType=VARCHAR}, 
      #{statusComments,jdbcType=INTEGER}, #{syncStatus,jdbcType=BOOLEAN}, #{logisticsApiSync,jdbcType=BOOLEAN}, 
      #{logisticsWxsendSync,jdbcType=BOOLEAN}, #{satisfyInvoiceTime,jdbcType=BIGINT}, 
      #{satisfyDeliveryTime,jdbcType=BIGINT}, #{isSalesPerformance,jdbcType=BOOLEAN}, 
      #{salesPerformanceTime,jdbcType=BIGINT}, #{salesPerformanceModTime,jdbcType=BIGINT}, 
      #{isDelayInvoice,jdbcType=BOOLEAN}, #{invoiceMethod,jdbcType=BOOLEAN}, #{lockedReason,jdbcType=VARCHAR}, 
      #{costUserIds,jdbcType=VARCHAR}, #{ownerUserId,jdbcType=INTEGER}, #{invoiceEmail,jdbcType=VARCHAR}, 
      #{paymentMode,jdbcType=BOOLEAN}, #{payType,jdbcType=BOOLEAN}, #{isApplyInvoice,jdbcType=BOOLEAN}, 
      #{applyInvoiceTime,jdbcType=BIGINT}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{adkSaleorderNo,jdbcType=VARCHAR}, 
      #{createMobile,jdbcType=VARCHAR}, #{bdtraderComments,jdbcType=VARCHAR}, #{closeComments,jdbcType=VARCHAR}, 
      #{bdMobileTime,jdbcType=BIGINT}, #{webTakeDeliveryTime,jdbcType=BIGINT}, #{actionId,jdbcType=INTEGER}, 
      #{isCoupons,jdbcType=BOOLEAN}, #{couponmoney,jdbcType=DECIMAL}, #{originalAmount,jdbcType=DECIMAL}, 
      #{elSaleordreNo,jdbcType=VARCHAR}, #{isPrintout,jdbcType=BOOLEAN}, #{outIsFlag,jdbcType=BOOLEAN}, 
      #{updateDataTime,jdbcType=TIMESTAMP}, #{realPayAmount,jdbcType=DECIMAL}, #{realReturnAmount,jdbcType=DECIMAL}, 
      #{realTotalAmount,jdbcType=DECIMAL}, #{sendToPc,jdbcType=BOOLEAN}, #{retentionMoney,jdbcType=DECIMAL}, 
      #{retentionMoneyDay,jdbcType=INTEGER}, #{isSameAddress,jdbcType=BOOLEAN}, #{invoiceSendNode,jdbcType=BOOLEAN}, 
      #{isDelete,jdbcType=TINYINT}, #{nopaybackAmount,jdbcType=DECIMAL}, #{contractUrl,jdbcType=VARCHAR}, 
      #{autoAudit,jdbcType=INTEGER}, #{isRisk,jdbcType=BOOLEAN}, #{riskComments,jdbcType=VARCHAR}, 
      #{riskTime,jdbcType=BIGINT}, #{groupCustomerId,jdbcType=INTEGER}, #{isContractReturn,jdbcType=TINYINT}, 
      #{isDeliveryorderReturn,jdbcType=TINYINT}, #{deliveryClaim,jdbcType=BOOLEAN}, #{deliveryDelayTime,jdbcType=BIGINT}, 
      #{billPeriodSettlementType,jdbcType=TINYINT}, #{isNew,jdbcType=TINYINT}, #{confirmStatus,jdbcType=INTEGER}, 
      #{confirmTime,jdbcType=TIMESTAMP}, #{contractNoStampUrl,jdbcType=VARCHAR}, #{onlineReceiptStatus,jdbcType=BOOLEAN}, 
      #{confirmationFormUpload,jdbcType=BOOLEAN}, #{confirmationFormAudit,jdbcType=BOOLEAN}, 
      #{confirmationSubmitTime,jdbcType=BIGINT}, #{prepareReaseonType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="SALEORDER_ID" keyProperty="saleorderId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SALEORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="quoteorderId != null">
        QUOTEORDER_ID,
      </if>
      <if test="parentId != null">
        PARENT_ID,
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO,
      </if>
      <if test="mSaleorderNo != null">
        M_SALEORDER_NO,
      </if>
      <if test="orderType != null">
        ORDER_TYPE,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="creatorOrgId != null">
        CREATOR_ORG_ID,
      </if>
      <if test="creatorOrgName != null">
        CREATOR_ORG_NAME,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="orgName != null">
        ORG_NAME,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="validOrgId != null">
        VALID_ORG_ID,
      </if>
      <if test="validOrgName != null">
        VALID_ORG_NAME,
      </if>
      <if test="validUserId != null">
        VALID_USER_ID,
      </if>
      <if test="validStatus != null">
        VALID_STATUS,
      </if>
      <if test="validTime != null">
        VALID_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="purchaseStatus != null">
        PURCHASE_STATUS,
      </if>
      <if test="lockedStatus != null">
        LOCKED_STATUS,
      </if>
      <if test="invoiceStatus != null">
        INVOICE_STATUS,
      </if>
      <if test="invoiceTime != null">
        INVOICE_TIME,
      </if>
      <if test="paymentStatus != null">
        PAYMENT_STATUS,
      </if>
      <if test="paymentTime != null">
        PAYMENT_TIME,
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS,
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME,
      </if>
      <if test="isCustomerArrival != null">
        IS_CUSTOMER_ARRIVAL,
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="serviceStatus != null">
        SERVICE_STATUS,
      </if>
      <if test="haveAccountPeriod != null">
        HAVE_ACCOUNT_PERIOD,
      </if>
      <if test="isPayment != null">
        IS_PAYMENT,
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="customerType != null">
        CUSTOMER_TYPE,
      </if>
      <if test="customerNature != null">
        CUSTOMER_NATURE,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME,
      </if>
      <if test="traderContactMobile != null">
        TRADER_CONTACT_MOBILE,
      </if>
      <if test="traderContactTelephone != null">
        TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID,
      </if>
      <if test="traderAreaId != null">
        TRADER_AREA_ID,
      </if>
      <if test="traderArea != null">
        TRADER_AREA,
      </if>
      <if test="traderAddress != null">
        TRADER_ADDRESS,
      </if>
      <if test="traderComments != null">
        TRADER_COMMENTS,
      </if>
      <if test="takeTraderId != null">
        TAKE_TRADER_ID,
      </if>
      <if test="takeTraderName != null">
        TAKE_TRADER_NAME,
      </if>
      <if test="takeTraderContactId != null">
        TAKE_TRADER_CONTACT_ID,
      </if>
      <if test="takeTraderContactName != null">
        TAKE_TRADER_CONTACT_NAME,
      </if>
      <if test="takeTraderContactMobile != null">
        TAKE_TRADER_CONTACT_MOBILE,
      </if>
      <if test="takeTraderContactTelephone != null">
        TAKE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="takeTraderAddressId != null">
        TAKE_TRADER_ADDRESS_ID,
      </if>
      <if test="takeTraderAreaId != null">
        TAKE_TRADER_AREA_ID,
      </if>
      <if test="takeTraderArea != null">
        TAKE_TRADER_AREA,
      </if>
      <if test="takeTraderAddress != null">
        TAKE_TRADER_ADDRESS,
      </if>
      <if test="isSendInvoice != null">
        IS_SEND_INVOICE,
      </if>
      <if test="invoiceTraderId != null">
        INVOICE_TRADER_ID,
      </if>
      <if test="invoiceTraderName != null">
        INVOICE_TRADER_NAME,
      </if>
      <if test="invoiceTraderContactId != null">
        INVOICE_TRADER_CONTACT_ID,
      </if>
      <if test="invoiceTraderContactName != null">
        INVOICE_TRADER_CONTACT_NAME,
      </if>
      <if test="invoiceTraderContactMobile != null">
        INVOICE_TRADER_CONTACT_MOBILE,
      </if>
      <if test="invoiceTraderContactTelephone != null">
        INVOICE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="invoiceTraderAddressId != null">
        INVOICE_TRADER_ADDRESS_ID,
      </if>
      <if test="invoiceTraderAreaId != null">
        INVOICE_TRADER_AREA_ID,
      </if>
      <if test="invoiceTraderArea != null">
        INVOICE_TRADER_AREA,
      </if>
      <if test="invoiceTraderAddress != null">
        INVOICE_TRADER_ADDRESS,
      </if>
      <if test="salesAreaId != null">
        SALES_AREA_ID,
      </if>
      <if test="salesArea != null">
        SALES_AREA,
      </if>
      <if test="terminalTraderId != null">
        TERMINAL_TRADER_ID,
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME,
      </if>
      <if test="terminalTraderType != null">
        TERMINAL_TRADER_TYPE,
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE,
      </if>
      <if test="freightDescription != null">
        FREIGHT_DESCRIPTION,
      </if>
      <if test="deliveryType != null">
        DELIVERY_TYPE,
      </if>
      <if test="deliveryMethod != null">
        DELIVERY_METHOD,
      </if>
      <if test="logisticsId != null">
        LOGISTICS_ID,
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE,
      </if>
      <if test="prepaidAmount != null">
        PREPAID_AMOUNT,
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT,
      </if>
      <if test="periodDay != null">
        PERIOD_DAY,
      </if>
      <if test="logisticsCollection != null">
        LOGISTICS_COLLECTION,
      </if>
      <if test="retainageAmount != null">
        RETAINAGE_AMOUNT,
      </if>
      <if test="retainageAmountMonth != null">
        RETAINAGE_AMOUNT_MONTH,
      </if>
      <if test="paymentComments != null">
        PAYMENT_COMMENTS,
      </if>
      <if test="additionalClause != null">
        ADDITIONAL_CLAUSE,
      </if>
      <if test="logisticsComments != null">
        LOGISTICS_COMMENTS,
      </if>
      <if test="financeComments != null">
        FINANCE_COMMENTS,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="invoiceComments != null">
        INVOICE_COMMENTS,
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT,
      </if>
      <if test="supplierClause != null">
        SUPPLIER_CLAUSE,
      </if>
      <if test="haveAdvancePurchase != null">
        HAVE_ADVANCE_PURCHASE,
      </if>
      <if test="advancePurchaseStatus != null">
        ADVANCE_PURCHASE_STATUS,
      </if>
      <if test="advancePurchaseComments != null">
        ADVANCE_PURCHASE_COMMENTS,
      </if>
      <if test="advancePurchaseTime != null">
        ADVANCE_PURCHASE_TIME,
      </if>
      <if test="isUrgent != null">
        IS_URGENT,
      </if>
      <if test="urgentAmount != null">
        URGENT_AMOUNT,
      </if>
      <if test="haveCommunicate != null">
        HAVE_COMMUNICATE,
      </if>
      <if test="prepareComments != null">
        PREPARE_COMMENTS,
      </if>
      <if test="marketingPlan != null">
        MARKETING_PLAN,
      </if>
      <if test="statusComments != null">
        STATUS_COMMENTS,
      </if>
      <if test="syncStatus != null">
        SYNC_STATUS,
      </if>
      <if test="logisticsApiSync != null">
        LOGISTICS_API_SYNC,
      </if>
      <if test="logisticsWxsendSync != null">
        LOGISTICS_WXSEND_SYNC,
      </if>
      <if test="satisfyInvoiceTime != null">
        SATISFY_INVOICE_TIME,
      </if>
      <if test="satisfyDeliveryTime != null">
        SATISFY_DELIVERY_TIME,
      </if>
      <if test="isSalesPerformance != null">
        IS_SALES_PERFORMANCE,
      </if>
      <if test="salesPerformanceTime != null">
        SALES_PERFORMANCE_TIME,
      </if>
      <if test="salesPerformanceModTime != null">
        SALES_PERFORMANCE_MOD_TIME,
      </if>
      <if test="isDelayInvoice != null">
        IS_DELAY_INVOICE,
      </if>
      <if test="invoiceMethod != null">
        INVOICE_METHOD,
      </if>
      <if test="lockedReason != null">
        LOCKED_REASON,
      </if>
      <if test="costUserIds != null">
        COST_USER_IDS,
      </if>
      <if test="ownerUserId != null">
        OWNER_USER_ID,
      </if>
      <if test="invoiceEmail != null">
        INVOICE_EMAIL,
      </if>
      <if test="paymentMode != null">
        PAYMENT_MODE,
      </if>
      <if test="payType != null">
        PAY_TYPE,
      </if>
      <if test="isApplyInvoice != null">
        IS_APPLY_INVOICE,
      </if>
      <if test="applyInvoiceTime != null">
        APPLY_INVOICE_TIME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="adkSaleorderNo != null">
        ADK_SALEORDER_NO,
      </if>
      <if test="createMobile != null">
        CREATE_MOBILE,
      </if>
      <if test="bdtraderComments != null">
        BDTRADER_COMMENTS,
      </if>
      <if test="closeComments != null">
        CLOSE_COMMENTS,
      </if>
      <if test="bdMobileTime != null">
        BD_MOBILE_TIME,
      </if>
      <if test="webTakeDeliveryTime != null">
        WEB_TAKE_DELIVERY_TIME,
      </if>
      <if test="actionId != null">
        ACTION_ID,
      </if>
      <if test="isCoupons != null">
        IS_COUPONS,
      </if>
      <if test="couponmoney != null">
        COUPONMONEY,
      </if>
      <if test="originalAmount != null">
        ORIGINAL_AMOUNT,
      </if>
      <if test="elSaleordreNo != null">
        EL_SALEORDRE_NO,
      </if>
      <if test="isPrintout != null">
        IS_PRINTOUT,
      </if>
      <if test="outIsFlag != null">
        OUT_IS_FLAG,
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME,
      </if>
      <if test="realPayAmount != null">
        REAL_PAY_AMOUNT,
      </if>
      <if test="realReturnAmount != null">
        REAL_RETURN_AMOUNT,
      </if>
      <if test="realTotalAmount != null">
        REAL_TOTAL_AMOUNT,
      </if>
      <if test="sendToPc != null">
        SEND_TO_PC,
      </if>
      <if test="retentionMoney != null">
        RETENTION_MONEY,
      </if>
      <if test="retentionMoneyDay != null">
        RETENTION_MONEY_DAY,
      </if>
      <if test="isSameAddress != null">
        IS_SAME_ADDRESS,
      </if>
      <if test="invoiceSendNode != null">
        INVOICE_SEND_NODE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="nopaybackAmount != null">
        NOPAYBACK_AMOUNT,
      </if>
      <if test="contractUrl != null">
        CONTRACT_URL,
      </if>
      <if test="autoAudit != null">
        AUTO_AUDIT,
      </if>
      <if test="isRisk != null">
        IS_RISK,
      </if>
      <if test="riskComments != null">
        RISK_COMMENTS,
      </if>
      <if test="riskTime != null">
        RISK_TIME,
      </if>
      <if test="groupCustomerId != null">
        GROUP_CUSTOMER_ID,
      </if>
      <if test="isContractReturn != null">
        IS_CONTRACT_RETURN,
      </if>
      <if test="isDeliveryorderReturn != null">
        IS_DELIVERYORDER_RETURN,
      </if>
      <if test="deliveryClaim != null">
        DELIVERY_CLAIM,
      </if>
      <if test="deliveryDelayTime != null">
        DELIVERY_DELAY_TIME,
      </if>
      <if test="billPeriodSettlementType != null">
        BILL_PERIOD_SETTLEMENT_TYPE,
      </if>
      <if test="isNew != null">
        IS_NEW,
      </if>
      <if test="confirmStatus != null">
        CONFIRM_STATUS,
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME,
      </if>
      <if test="contractNoStampUrl != null">
        CONTRACT_NO_STAMP_URL,
      </if>
      <if test="onlineReceiptStatus != null">
        ONLINE_RECEIPT_STATUS,
      </if>
      <if test="confirmationFormUpload != null">
        CONFIRMATION_FORM_UPLOAD,
      </if>
      <if test="confirmationFormAudit != null">
        CONFIRMATION_FORM_AUDIT,
      </if>
      <if test="confirmationSubmitTime != null">
        CONFIRMATION_SUBMIT_TIME,
      </if>
      <if test="prepareReaseonType != null">
        PREPARE_REASEON_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="quoteorderId != null">
        #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="saleorderNo != null">
        #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="mSaleorderNo != null">
        #{mSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=BOOLEAN},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=BOOLEAN},
      </if>
      <if test="creatorOrgId != null">
        #{creatorOrgId,jdbcType=INTEGER},
      </if>
      <if test="creatorOrgName != null">
        #{creatorOrgName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="validOrgId != null">
        #{validOrgId,jdbcType=INTEGER},
      </if>
      <if test="validOrgName != null">
        #{validOrgName,jdbcType=VARCHAR},
      </if>
      <if test="validUserId != null">
        #{validUserId,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        #{validStatus,jdbcType=BOOLEAN},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=BOOLEAN},
      </if>
      <if test="purchaseStatus != null">
        #{purchaseStatus,jdbcType=BOOLEAN},
      </if>
      <if test="lockedStatus != null">
        #{lockedStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceStatus != null">
        #{invoiceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceTime != null">
        #{invoiceTime,jdbcType=BIGINT},
      </if>
      <if test="paymentStatus != null">
        #{paymentStatus,jdbcType=INTEGER},
      </if>
      <if test="paymentTime != null">
        #{paymentTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="isCustomerArrival != null">
        #{isCustomerArrival,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalStatus != null">
        #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="serviceStatus != null">
        #{serviceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="haveAccountPeriod != null">
        #{haveAccountPeriod,jdbcType=BOOLEAN},
      </if>
      <if test="isPayment != null">
        #{isPayment,jdbcType=BOOLEAN},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="customerType != null">
        #{customerType,jdbcType=INTEGER},
      </if>
      <if test="customerNature != null">
        #{customerNature,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null">
        #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null">
        #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null">
        #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="traderAreaId != null">
        #{traderAreaId,jdbcType=INTEGER},
      </if>
      <if test="traderArea != null">
        #{traderArea,jdbcType=VARCHAR},
      </if>
      <if test="traderAddress != null">
        #{traderAddress,jdbcType=VARCHAR},
      </if>
      <if test="traderComments != null">
        #{traderComments,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderId != null">
        #{takeTraderId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderName != null">
        #{takeTraderName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactId != null">
        #{takeTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderContactName != null">
        #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactMobile != null">
        #{takeTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null">
        #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddressId != null">
        #{takeTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderAreaId != null">
        #{takeTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderArea != null">
        #{takeTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddress != null">
        #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="isSendInvoice != null">
        #{isSendInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceTraderId != null">
        #{invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderName != null">
        #{invoiceTraderName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactId != null">
        #{invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderContactName != null">
        #{invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactMobile != null">
        #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactTelephone != null">
        #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddressId != null">
        #{invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderAreaId != null">
        #{invoiceTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderArea != null">
        #{invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddress != null">
        #{invoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="salesAreaId != null">
        #{salesAreaId,jdbcType=INTEGER},
      </if>
      <if test="salesArea != null">
        #{salesArea,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null">
        #{terminalTraderId,jdbcType=INTEGER},
      </if>
      <if test="terminalTraderName != null">
        #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderType != null">
        #{terminalTraderType,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="freightDescription != null">
        #{freightDescription,jdbcType=INTEGER},
      </if>
      <if test="deliveryType != null">
        #{deliveryType,jdbcType=INTEGER},
      </if>
      <if test="deliveryMethod != null">
        #{deliveryMethod,jdbcType=INTEGER},
      </if>
      <if test="logisticsId != null">
        #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="prepaidAmount != null">
        #{prepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodAmount != null">
        #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null">
        #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="logisticsCollection != null">
        #{logisticsCollection,jdbcType=BOOLEAN},
      </if>
      <if test="retainageAmount != null">
        #{retainageAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmountMonth != null">
        #{retainageAmountMonth,jdbcType=INTEGER},
      </if>
      <if test="paymentComments != null">
        #{paymentComments,jdbcType=VARCHAR},
      </if>
      <if test="additionalClause != null">
        #{additionalClause,jdbcType=VARCHAR},
      </if>
      <if test="logisticsComments != null">
        #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="financeComments != null">
        #{financeComments,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="invoiceComments != null">
        #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null">
        #{deliveryDirect,jdbcType=BOOLEAN},
      </if>
      <if test="supplierClause != null">
        #{supplierClause,jdbcType=VARCHAR},
      </if>
      <if test="haveAdvancePurchase != null">
        #{haveAdvancePurchase,jdbcType=BOOLEAN},
      </if>
      <if test="advancePurchaseStatus != null">
        #{advancePurchaseStatus,jdbcType=BOOLEAN},
      </if>
      <if test="advancePurchaseComments != null">
        #{advancePurchaseComments,jdbcType=VARCHAR},
      </if>
      <if test="advancePurchaseTime != null">
        #{advancePurchaseTime,jdbcType=BIGINT},
      </if>
      <if test="isUrgent != null">
        #{isUrgent,jdbcType=BOOLEAN},
      </if>
      <if test="urgentAmount != null">
        #{urgentAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveCommunicate != null">
        #{haveCommunicate,jdbcType=BOOLEAN},
      </if>
      <if test="prepareComments != null">
        #{prepareComments,jdbcType=VARCHAR},
      </if>
      <if test="marketingPlan != null">
        #{marketingPlan,jdbcType=VARCHAR},
      </if>
      <if test="statusComments != null">
        #{statusComments,jdbcType=INTEGER},
      </if>
      <if test="syncStatus != null">
        #{syncStatus,jdbcType=BOOLEAN},
      </if>
      <if test="logisticsApiSync != null">
        #{logisticsApiSync,jdbcType=BOOLEAN},
      </if>
      <if test="logisticsWxsendSync != null">
        #{logisticsWxsendSync,jdbcType=BOOLEAN},
      </if>
      <if test="satisfyInvoiceTime != null">
        #{satisfyInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="satisfyDeliveryTime != null">
        #{satisfyDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="isSalesPerformance != null">
        #{isSalesPerformance,jdbcType=BOOLEAN},
      </if>
      <if test="salesPerformanceTime != null">
        #{salesPerformanceTime,jdbcType=BIGINT},
      </if>
      <if test="salesPerformanceModTime != null">
        #{salesPerformanceModTime,jdbcType=BIGINT},
      </if>
      <if test="isDelayInvoice != null">
        #{isDelayInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceMethod != null">
        #{invoiceMethod,jdbcType=BOOLEAN},
      </if>
      <if test="lockedReason != null">
        #{lockedReason,jdbcType=VARCHAR},
      </if>
      <if test="costUserIds != null">
        #{costUserIds,jdbcType=VARCHAR},
      </if>
      <if test="ownerUserId != null">
        #{ownerUserId,jdbcType=INTEGER},
      </if>
      <if test="invoiceEmail != null">
        #{invoiceEmail,jdbcType=VARCHAR},
      </if>
      <if test="paymentMode != null">
        #{paymentMode,jdbcType=BOOLEAN},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=BOOLEAN},
      </if>
      <if test="isApplyInvoice != null">
        #{isApplyInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="applyInvoiceTime != null">
        #{applyInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="adkSaleorderNo != null">
        #{adkSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null">
        #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="bdtraderComments != null">
        #{bdtraderComments,jdbcType=VARCHAR},
      </if>
      <if test="closeComments != null">
        #{closeComments,jdbcType=VARCHAR},
      </if>
      <if test="bdMobileTime != null">
        #{bdMobileTime,jdbcType=BIGINT},
      </if>
      <if test="webTakeDeliveryTime != null">
        #{webTakeDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="actionId != null">
        #{actionId,jdbcType=INTEGER},
      </if>
      <if test="isCoupons != null">
        #{isCoupons,jdbcType=BOOLEAN},
      </if>
      <if test="couponmoney != null">
        #{couponmoney,jdbcType=DECIMAL},
      </if>
      <if test="originalAmount != null">
        #{originalAmount,jdbcType=DECIMAL},
      </if>
      <if test="elSaleordreNo != null">
        #{elSaleordreNo,jdbcType=VARCHAR},
      </if>
      <if test="isPrintout != null">
        #{isPrintout,jdbcType=BOOLEAN},
      </if>
      <if test="outIsFlag != null">
        #{outIsFlag,jdbcType=BOOLEAN},
      </if>
      <if test="updateDataTime != null">
        #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realPayAmount != null">
        #{realPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="realReturnAmount != null">
        #{realReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="realTotalAmount != null">
        #{realTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="sendToPc != null">
        #{sendToPc,jdbcType=BOOLEAN},
      </if>
      <if test="retentionMoney != null">
        #{retentionMoney,jdbcType=DECIMAL},
      </if>
      <if test="retentionMoneyDay != null">
        #{retentionMoneyDay,jdbcType=INTEGER},
      </if>
      <if test="isSameAddress != null">
        #{isSameAddress,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceSendNode != null">
        #{invoiceSendNode,jdbcType=BOOLEAN},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="nopaybackAmount != null">
        #{nopaybackAmount,jdbcType=DECIMAL},
      </if>
      <if test="contractUrl != null">
        #{contractUrl,jdbcType=VARCHAR},
      </if>
      <if test="autoAudit != null">
        #{autoAudit,jdbcType=INTEGER},
      </if>
      <if test="isRisk != null">
        #{isRisk,jdbcType=BOOLEAN},
      </if>
      <if test="riskComments != null">
        #{riskComments,jdbcType=VARCHAR},
      </if>
      <if test="riskTime != null">
        #{riskTime,jdbcType=BIGINT},
      </if>
      <if test="groupCustomerId != null">
        #{groupCustomerId,jdbcType=INTEGER},
      </if>
      <if test="isContractReturn != null">
        #{isContractReturn,jdbcType=TINYINT},
      </if>
      <if test="isDeliveryorderReturn != null">
        #{isDeliveryorderReturn,jdbcType=TINYINT},
      </if>
      <if test="deliveryClaim != null">
        #{deliveryClaim,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryDelayTime != null">
        #{deliveryDelayTime,jdbcType=BIGINT},
      </if>
      <if test="billPeriodSettlementType != null">
        #{billPeriodSettlementType,jdbcType=TINYINT},
      </if>
      <if test="isNew != null">
        #{isNew,jdbcType=TINYINT},
      </if>
      <if test="confirmStatus != null">
        #{confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractNoStampUrl != null">
        #{contractNoStampUrl,jdbcType=VARCHAR},
      </if>
      <if test="onlineReceiptStatus != null">
        #{onlineReceiptStatus,jdbcType=BOOLEAN},
      </if>
      <if test="confirmationFormUpload != null">
        #{confirmationFormUpload,jdbcType=BOOLEAN},
      </if>
      <if test="confirmationFormAudit != null">
        #{confirmationFormAudit,jdbcType=BOOLEAN},
      </if>
      <if test="confirmationSubmitTime != null">
        #{confirmationSubmitTime,jdbcType=BIGINT},
      </if>
      <if test="prepareReaseonType != null">
        #{prepareReaseonType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderDto">
    <!--@mbg.generated-->
    update T_SALEORDER
    <set>
      <if test="quoteorderId != null">
        QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        PARENT_ID = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="mSaleorderNo != null">
        M_SALEORDER_NO = #{mSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        ORDER_TYPE = #{orderType,jdbcType=BOOLEAN},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=BOOLEAN},
      </if>
      <if test="creatorOrgId != null">
        CREATOR_ORG_ID = #{creatorOrgId,jdbcType=INTEGER},
      </if>
      <if test="creatorOrgName != null">
        CREATOR_ORG_NAME = #{creatorOrgName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        ORG_NAME = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="validOrgId != null">
        VALID_ORG_ID = #{validOrgId,jdbcType=INTEGER},
      </if>
      <if test="validOrgName != null">
        VALID_ORG_NAME = #{validOrgName,jdbcType=VARCHAR},
      </if>
      <if test="validUserId != null">
        VALID_USER_ID = #{validUserId,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
      </if>
      <if test="validTime != null">
        VALID_TIME = #{validTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=BOOLEAN},
      </if>
      <if test="purchaseStatus != null">
        PURCHASE_STATUS = #{purchaseStatus,jdbcType=BOOLEAN},
      </if>
      <if test="lockedStatus != null">
        LOCKED_STATUS = #{lockedStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceStatus != null">
        INVOICE_STATUS = #{invoiceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceTime != null">
        INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
      </if>
      <if test="paymentStatus != null">
        PAYMENT_STATUS = #{paymentStatus,jdbcType=INTEGER},
      </if>
      <if test="paymentTime != null">
        PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS = #{deliveryStatus,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="isCustomerArrival != null">
        IS_CUSTOMER_ARRIVAL = #{isCustomerArrival,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="serviceStatus != null">
        SERVICE_STATUS = #{serviceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="haveAccountPeriod != null">
        HAVE_ACCOUNT_PERIOD = #{haveAccountPeriod,jdbcType=BOOLEAN},
      </if>
      <if test="isPayment != null">
        IS_PAYMENT = #{isPayment,jdbcType=BOOLEAN},
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="customerType != null">
        CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
      </if>
      <if test="customerNature != null">
        CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null">
        TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null">
        TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="traderAreaId != null">
        TRADER_AREA_ID = #{traderAreaId,jdbcType=INTEGER},
      </if>
      <if test="traderArea != null">
        TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
      </if>
      <if test="traderAddress != null">
        TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
      </if>
      <if test="traderComments != null">
        TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderId != null">
        TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderName != null">
        TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactId != null">
        TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderContactName != null">
        TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactMobile != null">
        TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null">
        TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddressId != null">
        TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderAreaId != null">
        TAKE_TRADER_AREA_ID = #{takeTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderArea != null">
        TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddress != null">
        TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="isSendInvoice != null">
        IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceTraderId != null">
        INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderName != null">
        INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactId != null">
        INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderContactName != null">
        INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactMobile != null">
        INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactTelephone != null">
        INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddressId != null">
        INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderAreaId != null">
        INVOICE_TRADER_AREA_ID = #{invoiceTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderArea != null">
        INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddress != null">
        INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="salesAreaId != null">
        SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER},
      </if>
      <if test="salesArea != null">
        SALES_AREA = #{salesArea,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null">
        TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER},
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderType != null">
        TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="freightDescription != null">
        FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
      </if>
      <if test="deliveryType != null">
        DELIVERY_TYPE = #{deliveryType,jdbcType=INTEGER},
      </if>
      <if test="deliveryMethod != null">
        DELIVERY_METHOD = #{deliveryMethod,jdbcType=INTEGER},
      </if>
      <if test="logisticsId != null">
        LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="prepaidAmount != null">
        PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null">
        PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="logisticsCollection != null">
        LOGISTICS_COLLECTION = #{logisticsCollection,jdbcType=BOOLEAN},
      </if>
      <if test="retainageAmount != null">
        RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmountMonth != null">
        RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
      </if>
      <if test="paymentComments != null">
        PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
      </if>
      <if test="additionalClause != null">
        ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
      </if>
      <if test="logisticsComments != null">
        LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="financeComments != null">
        FINANCE_COMMENTS = #{financeComments,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="invoiceComments != null">
        INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BOOLEAN},
      </if>
      <if test="supplierClause != null">
        SUPPLIER_CLAUSE = #{supplierClause,jdbcType=VARCHAR},
      </if>
      <if test="haveAdvancePurchase != null">
        HAVE_ADVANCE_PURCHASE = #{haveAdvancePurchase,jdbcType=BOOLEAN},
      </if>
      <if test="advancePurchaseStatus != null">
        ADVANCE_PURCHASE_STATUS = #{advancePurchaseStatus,jdbcType=BOOLEAN},
      </if>
      <if test="advancePurchaseComments != null">
        ADVANCE_PURCHASE_COMMENTS = #{advancePurchaseComments,jdbcType=VARCHAR},
      </if>
      <if test="advancePurchaseTime != null">
        ADVANCE_PURCHASE_TIME = #{advancePurchaseTime,jdbcType=BIGINT},
      </if>
      <if test="isUrgent != null">
        IS_URGENT = #{isUrgent,jdbcType=BOOLEAN},
      </if>
      <if test="urgentAmount != null">
        URGENT_AMOUNT = #{urgentAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveCommunicate != null">
        HAVE_COMMUNICATE = #{haveCommunicate,jdbcType=BOOLEAN},
      </if>
      <if test="prepareComments != null">
        PREPARE_COMMENTS = #{prepareComments,jdbcType=VARCHAR},
      </if>
      <if test="marketingPlan != null">
        MARKETING_PLAN = #{marketingPlan,jdbcType=VARCHAR},
      </if>
      <if test="statusComments != null">
        STATUS_COMMENTS = #{statusComments,jdbcType=INTEGER},
      </if>
      <if test="syncStatus != null">
        SYNC_STATUS = #{syncStatus,jdbcType=BOOLEAN},
      </if>
      <if test="logisticsApiSync != null">
        LOGISTICS_API_SYNC = #{logisticsApiSync,jdbcType=BOOLEAN},
      </if>
      <if test="logisticsWxsendSync != null">
        LOGISTICS_WXSEND_SYNC = #{logisticsWxsendSync,jdbcType=BOOLEAN},
      </if>
      <if test="satisfyInvoiceTime != null">
        SATISFY_INVOICE_TIME = #{satisfyInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="satisfyDeliveryTime != null">
        SATISFY_DELIVERY_TIME = #{satisfyDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="isSalesPerformance != null">
        IS_SALES_PERFORMANCE = #{isSalesPerformance,jdbcType=BOOLEAN},
      </if>
      <if test="salesPerformanceTime != null">
        SALES_PERFORMANCE_TIME = #{salesPerformanceTime,jdbcType=BIGINT},
      </if>
      <if test="salesPerformanceModTime != null">
        SALES_PERFORMANCE_MOD_TIME = #{salesPerformanceModTime,jdbcType=BIGINT},
      </if>
      <if test="isDelayInvoice != null">
        IS_DELAY_INVOICE = #{isDelayInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceMethod != null">
        INVOICE_METHOD = #{invoiceMethod,jdbcType=BOOLEAN},
      </if>
      <if test="lockedReason != null">
        LOCKED_REASON = #{lockedReason,jdbcType=VARCHAR},
      </if>
      <if test="costUserIds != null">
        COST_USER_IDS = #{costUserIds,jdbcType=VARCHAR},
      </if>
      <if test="ownerUserId != null">
        OWNER_USER_ID = #{ownerUserId,jdbcType=INTEGER},
      </if>
      <if test="invoiceEmail != null">
        INVOICE_EMAIL = #{invoiceEmail,jdbcType=VARCHAR},
      </if>
      <if test="paymentMode != null">
        PAYMENT_MODE = #{paymentMode,jdbcType=BOOLEAN},
      </if>
      <if test="payType != null">
        PAY_TYPE = #{payType,jdbcType=BOOLEAN},
      </if>
      <if test="isApplyInvoice != null">
        IS_APPLY_INVOICE = #{isApplyInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="applyInvoiceTime != null">
        APPLY_INVOICE_TIME = #{applyInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="adkSaleorderNo != null">
        ADK_SALEORDER_NO = #{adkSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null">
        CREATE_MOBILE = #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="bdtraderComments != null">
        BDTRADER_COMMENTS = #{bdtraderComments,jdbcType=VARCHAR},
      </if>
      <if test="closeComments != null">
        CLOSE_COMMENTS = #{closeComments,jdbcType=VARCHAR},
      </if>
      <if test="bdMobileTime != null">
        BD_MOBILE_TIME = #{bdMobileTime,jdbcType=BIGINT},
      </if>
      <if test="webTakeDeliveryTime != null">
        WEB_TAKE_DELIVERY_TIME = #{webTakeDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="actionId != null">
        ACTION_ID = #{actionId,jdbcType=INTEGER},
      </if>
      <if test="isCoupons != null">
        IS_COUPONS = #{isCoupons,jdbcType=BOOLEAN},
      </if>
      <if test="couponmoney != null">
        COUPONMONEY = #{couponmoney,jdbcType=DECIMAL},
      </if>
      <if test="originalAmount != null">
        ORIGINAL_AMOUNT = #{originalAmount,jdbcType=DECIMAL},
      </if>
      <if test="elSaleordreNo != null">
        EL_SALEORDRE_NO = #{elSaleordreNo,jdbcType=VARCHAR},
      </if>
      <if test="isPrintout != null">
        IS_PRINTOUT = #{isPrintout,jdbcType=BOOLEAN},
      </if>
      <if test="outIsFlag != null">
        OUT_IS_FLAG = #{outIsFlag,jdbcType=BOOLEAN},
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realPayAmount != null">
        REAL_PAY_AMOUNT = #{realPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="realReturnAmount != null">
        REAL_RETURN_AMOUNT = #{realReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="realTotalAmount != null">
        REAL_TOTAL_AMOUNT = #{realTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="sendToPc != null">
        SEND_TO_PC = #{sendToPc,jdbcType=BOOLEAN},
      </if>
      <if test="retentionMoney != null">
        RETENTION_MONEY = #{retentionMoney,jdbcType=DECIMAL},
      </if>
      <if test="retentionMoneyDay != null">
        RETENTION_MONEY_DAY = #{retentionMoneyDay,jdbcType=INTEGER},
      </if>
      <if test="isSameAddress != null">
        IS_SAME_ADDRESS = #{isSameAddress,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceSendNode != null">
        INVOICE_SEND_NODE = #{invoiceSendNode,jdbcType=BOOLEAN},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="nopaybackAmount != null">
        NOPAYBACK_AMOUNT = #{nopaybackAmount,jdbcType=DECIMAL},
      </if>
      <if test="contractUrl != null">
        CONTRACT_URL = #{contractUrl,jdbcType=VARCHAR},
      </if>
      <if test="autoAudit != null">
        AUTO_AUDIT = #{autoAudit,jdbcType=INTEGER},
      </if>
      <if test="isRisk != null">
        IS_RISK = #{isRisk,jdbcType=BOOLEAN},
      </if>
      <if test="riskComments != null">
        RISK_COMMENTS = #{riskComments,jdbcType=VARCHAR},
      </if>
      <if test="riskTime != null">
        RISK_TIME = #{riskTime,jdbcType=BIGINT},
      </if>
      <if test="groupCustomerId != null">
        GROUP_CUSTOMER_ID = #{groupCustomerId,jdbcType=INTEGER},
      </if>
      <if test="isContractReturn != null">
        IS_CONTRACT_RETURN = #{isContractReturn,jdbcType=TINYINT},
      </if>
      <if test="isDeliveryorderReturn != null">
        IS_DELIVERYORDER_RETURN = #{isDeliveryorderReturn,jdbcType=TINYINT},
      </if>
      <if test="deliveryClaim != null">
        DELIVERY_CLAIM = #{deliveryClaim,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryDelayTime != null">
        DELIVERY_DELAY_TIME = #{deliveryDelayTime,jdbcType=BIGINT},
      </if>
      <if test="billPeriodSettlementType != null">
        BILL_PERIOD_SETTLEMENT_TYPE = #{billPeriodSettlementType,jdbcType=TINYINT},
      </if>
      <if test="isNew != null">
        IS_NEW = #{isNew,jdbcType=TINYINT},
      </if>
      <if test="confirmStatus != null">
        CONFIRM_STATUS = #{confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractNoStampUrl != null">
        CONTRACT_NO_STAMP_URL = #{contractNoStampUrl,jdbcType=VARCHAR},
      </if>
      <if test="onlineReceiptStatus != null">
        ONLINE_RECEIPT_STATUS = #{onlineReceiptStatus,jdbcType=BOOLEAN},
      </if>
      <if test="confirmationFormUpload != null">
        CONFIRMATION_FORM_UPLOAD = #{confirmationFormUpload,jdbcType=BOOLEAN},
      </if>
      <if test="confirmationFormAudit != null">
        CONFIRMATION_FORM_AUDIT = #{confirmationFormAudit,jdbcType=BOOLEAN},
      </if>
      <if test="confirmationSubmitTime != null">
        CONFIRMATION_SUBMIT_TIME = #{confirmationSubmitTime,jdbcType=BIGINT},
      </if>
      <if test="prepareReaseonType != null">
        PREPARE_REASEON_TYPE = #{prepareReaseonType,jdbcType=INTEGER},
      </if>
    </set>
    where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderDto">
    <!--@mbg.generated-->
    update T_SALEORDER
    set QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      PARENT_ID = #{parentId,jdbcType=INTEGER},
      SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      M_SALEORDER_NO = #{mSaleorderNo,jdbcType=VARCHAR},
      ORDER_TYPE = #{orderType,jdbcType=BOOLEAN},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      `SOURCE` = #{source,jdbcType=BOOLEAN},
      CREATOR_ORG_ID = #{creatorOrgId,jdbcType=INTEGER},
      CREATOR_ORG_NAME = #{creatorOrgName,jdbcType=VARCHAR},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      ORG_NAME = #{orgName,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=INTEGER},
      VALID_ORG_ID = #{validOrgId,jdbcType=INTEGER},
      VALID_ORG_NAME = #{validOrgName,jdbcType=VARCHAR},
      VALID_USER_ID = #{validUserId,jdbcType=INTEGER},
      VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
      VALID_TIME = #{validTime,jdbcType=BIGINT},
      END_TIME = #{endTime,jdbcType=BIGINT},
      `STATUS` = #{status,jdbcType=BOOLEAN},
      PURCHASE_STATUS = #{purchaseStatus,jdbcType=BOOLEAN},
      LOCKED_STATUS = #{lockedStatus,jdbcType=BOOLEAN},
      INVOICE_STATUS = #{invoiceStatus,jdbcType=BOOLEAN},
      INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
      PAYMENT_STATUS = #{paymentStatus,jdbcType=INTEGER},
      PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
      DELIVERY_STATUS = #{deliveryStatus,jdbcType=BOOLEAN},
      DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      IS_CUSTOMER_ARRIVAL = #{isCustomerArrival,jdbcType=BOOLEAN},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      SERVICE_STATUS = #{serviceStatus,jdbcType=BOOLEAN},
      HAVE_ACCOUNT_PERIOD = #{haveAccountPeriod,jdbcType=BOOLEAN},
      IS_PAYMENT = #{isPayment,jdbcType=BOOLEAN},
      TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
      CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      TRADER_AREA_ID = #{traderAreaId,jdbcType=INTEGER},
      TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
      TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
      TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
      TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
      TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
      TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
      TAKE_TRADER_AREA_ID = #{takeTraderAreaId,jdbcType=INTEGER},
      TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
      TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BOOLEAN},
      INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
      INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
      INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
      INVOICE_TRADER_AREA_ID = #{invoiceTraderAreaId,jdbcType=INTEGER},
      INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
      INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
      SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER},
      SALES_AREA = #{salesArea,jdbcType=VARCHAR},
      TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER},
      TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER},
      INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
      DELIVERY_TYPE = #{deliveryType,jdbcType=INTEGER},
      DELIVERY_METHOD = #{deliveryMethod,jdbcType=INTEGER},
      LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
      ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      LOGISTICS_COLLECTION = #{logisticsCollection,jdbcType=BOOLEAN},
      RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
      RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
      PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
      ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
      LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      FINANCE_COMMENTS = #{financeComments,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BOOLEAN},
      SUPPLIER_CLAUSE = #{supplierClause,jdbcType=VARCHAR},
      HAVE_ADVANCE_PURCHASE = #{haveAdvancePurchase,jdbcType=BOOLEAN},
      ADVANCE_PURCHASE_STATUS = #{advancePurchaseStatus,jdbcType=BOOLEAN},
      ADVANCE_PURCHASE_COMMENTS = #{advancePurchaseComments,jdbcType=VARCHAR},
      ADVANCE_PURCHASE_TIME = #{advancePurchaseTime,jdbcType=BIGINT},
      IS_URGENT = #{isUrgent,jdbcType=BOOLEAN},
      URGENT_AMOUNT = #{urgentAmount,jdbcType=DECIMAL},
      HAVE_COMMUNICATE = #{haveCommunicate,jdbcType=BOOLEAN},
      PREPARE_COMMENTS = #{prepareComments,jdbcType=VARCHAR},
      MARKETING_PLAN = #{marketingPlan,jdbcType=VARCHAR},
      STATUS_COMMENTS = #{statusComments,jdbcType=INTEGER},
      SYNC_STATUS = #{syncStatus,jdbcType=BOOLEAN},
      LOGISTICS_API_SYNC = #{logisticsApiSync,jdbcType=BOOLEAN},
      LOGISTICS_WXSEND_SYNC = #{logisticsWxsendSync,jdbcType=BOOLEAN},
      SATISFY_INVOICE_TIME = #{satisfyInvoiceTime,jdbcType=BIGINT},
      SATISFY_DELIVERY_TIME = #{satisfyDeliveryTime,jdbcType=BIGINT},
      IS_SALES_PERFORMANCE = #{isSalesPerformance,jdbcType=BOOLEAN},
      SALES_PERFORMANCE_TIME = #{salesPerformanceTime,jdbcType=BIGINT},
      SALES_PERFORMANCE_MOD_TIME = #{salesPerformanceModTime,jdbcType=BIGINT},
      IS_DELAY_INVOICE = #{isDelayInvoice,jdbcType=BOOLEAN},
      INVOICE_METHOD = #{invoiceMethod,jdbcType=BOOLEAN},
      LOCKED_REASON = #{lockedReason,jdbcType=VARCHAR},
      COST_USER_IDS = #{costUserIds,jdbcType=VARCHAR},
      OWNER_USER_ID = #{ownerUserId,jdbcType=INTEGER},
      INVOICE_EMAIL = #{invoiceEmail,jdbcType=VARCHAR},
      PAYMENT_MODE = #{paymentMode,jdbcType=BOOLEAN},
      PAY_TYPE = #{payType,jdbcType=BOOLEAN},
      IS_APPLY_INVOICE = #{isApplyInvoice,jdbcType=BOOLEAN},
      APPLY_INVOICE_TIME = #{applyInvoiceTime,jdbcType=BIGINT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      ADK_SALEORDER_NO = #{adkSaleorderNo,jdbcType=VARCHAR},
      CREATE_MOBILE = #{createMobile,jdbcType=VARCHAR},
      BDTRADER_COMMENTS = #{bdtraderComments,jdbcType=VARCHAR},
      CLOSE_COMMENTS = #{closeComments,jdbcType=VARCHAR},
      BD_MOBILE_TIME = #{bdMobileTime,jdbcType=BIGINT},
      WEB_TAKE_DELIVERY_TIME = #{webTakeDeliveryTime,jdbcType=BIGINT},
      ACTION_ID = #{actionId,jdbcType=INTEGER},
      IS_COUPONS = #{isCoupons,jdbcType=BOOLEAN},
      COUPONMONEY = #{couponmoney,jdbcType=DECIMAL},
      ORIGINAL_AMOUNT = #{originalAmount,jdbcType=DECIMAL},
      EL_SALEORDRE_NO = #{elSaleordreNo,jdbcType=VARCHAR},
      IS_PRINTOUT = #{isPrintout,jdbcType=BOOLEAN},
      OUT_IS_FLAG = #{outIsFlag,jdbcType=BOOLEAN},
      UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      REAL_PAY_AMOUNT = #{realPayAmount,jdbcType=DECIMAL},
      REAL_RETURN_AMOUNT = #{realReturnAmount,jdbcType=DECIMAL},
      REAL_TOTAL_AMOUNT = #{realTotalAmount,jdbcType=DECIMAL},
      SEND_TO_PC = #{sendToPc,jdbcType=BOOLEAN},
      RETENTION_MONEY = #{retentionMoney,jdbcType=DECIMAL},
      RETENTION_MONEY_DAY = #{retentionMoneyDay,jdbcType=INTEGER},
      IS_SAME_ADDRESS = #{isSameAddress,jdbcType=BOOLEAN},
      INVOICE_SEND_NODE = #{invoiceSendNode,jdbcType=BOOLEAN},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      NOPAYBACK_AMOUNT = #{nopaybackAmount,jdbcType=DECIMAL},
      CONTRACT_URL = #{contractUrl,jdbcType=VARCHAR},
      AUTO_AUDIT = #{autoAudit,jdbcType=INTEGER},
      IS_RISK = #{isRisk,jdbcType=BOOLEAN},
      RISK_COMMENTS = #{riskComments,jdbcType=VARCHAR},
      RISK_TIME = #{riskTime,jdbcType=BIGINT},
      GROUP_CUSTOMER_ID = #{groupCustomerId,jdbcType=INTEGER},
      IS_CONTRACT_RETURN = #{isContractReturn,jdbcType=TINYINT},
      IS_DELIVERYORDER_RETURN = #{isDeliveryorderReturn,jdbcType=TINYINT},
      DELIVERY_CLAIM = #{deliveryClaim,jdbcType=BOOLEAN},
      DELIVERY_DELAY_TIME = #{deliveryDelayTime,jdbcType=BIGINT},
      BILL_PERIOD_SETTLEMENT_TYPE = #{billPeriodSettlementType,jdbcType=TINYINT},
      IS_NEW = #{isNew,jdbcType=TINYINT},
      CONFIRM_STATUS = #{confirmStatus,jdbcType=INTEGER},
      CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
      CONTRACT_NO_STAMP_URL = #{contractNoStampUrl,jdbcType=VARCHAR},
      ONLINE_RECEIPT_STATUS = #{onlineReceiptStatus,jdbcType=BOOLEAN},
      CONFIRMATION_FORM_UPLOAD = #{confirmationFormUpload,jdbcType=BOOLEAN},
      CONFIRMATION_FORM_AUDIT = #{confirmationFormAudit,jdbcType=BOOLEAN},
      CONFIRMATION_SUBMIT_TIME = #{confirmationSubmitTime,jdbcType=BIGINT},
      PREPARE_REASEON_TYPE = #{prepareReaseonType,jdbcType=INTEGER}
    where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_SALEORDER
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="QUOTEORDER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.quoteorderId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PARENT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.parentId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SALEORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.saleorderNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="M_SALEORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.mSaleorderNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ORDER_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.orderType,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="COMPANY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.companyId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`SOURCE` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.source,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="CREATOR_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.creatorOrgId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_ORG_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.creatorOrgName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.orgId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ORG_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.orgName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.userId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="VALID_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.validOrgId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="VALID_ORG_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.validOrgName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="VALID_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.validUserId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="VALID_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.validStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="VALID_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.validTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="END_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.endTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`STATUS` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.status,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="PURCHASE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.purchaseStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="LOCKED_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.lockedStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="INVOICE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="PAYMENT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.paymentStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PAYMENT_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.paymentTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.deliveryStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.deliveryTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="IS_CUSTOMER_ARRIVAL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isCustomerArrival,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.arrivalStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.arrivalTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="SERVICE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.serviceStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="HAVE_ACCOUNT_PERIOD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.haveAccountPeriod,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_PAYMENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isPayment,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="TOTAL_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.totalAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CUSTOMER_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.customerType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CUSTOMER_NATURE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.customerNature,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TRADER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderContactId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderContactName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_MOBILE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderContactMobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_TELEPHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderContactTelephone,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TRADER_ADDRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderAddressId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TRADER_AREA_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderAreaId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TRADER_AREA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderArea,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TRADER_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderAddress,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TRADER_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_CONTACT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderContactId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_CONTACT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderContactName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_CONTACT_MOBILE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderContactMobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_CONTACT_TELEPHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderContactTelephone,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_ADDRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderAddressId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_AREA_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderAreaId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_AREA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderArea,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderAddress,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_SEND_INVOICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isSendInvoice,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderContactId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderContactName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_MOBILE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderContactMobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_TELEPHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderContactTelephone,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_ADDRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderAddressId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_AREA_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderAreaId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_AREA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderArea,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderAddress,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SALES_AREA_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.salesAreaId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SALES_AREA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.salesArea,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TERMINAL_TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.terminalTraderId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TERMINAL_TRADER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.terminalTraderName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TERMINAL_TRADER_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.terminalTraderType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="FREIGHT_DESCRIPTION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.freightDescription,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.deliveryType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_METHOD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.deliveryMethod,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.logisticsId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PAYMENT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.paymentType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PREPAID_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.prepaidAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="ACCOUNT_PERIOD_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.accountPeriodAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="PERIOD_DAY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.periodDay,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_COLLECTION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.logisticsCollection,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="RETAINAGE_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.retainageAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="RETAINAGE_AMOUNT_MONTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.retainageAmountMonth,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="PAYMENT_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.paymentComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ADDITIONAL_CLAUSE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.additionalClause,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.logisticsComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="FINANCE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.financeComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.comments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_DIRECT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.deliveryDirect,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="SUPPLIER_CLAUSE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.supplierClause,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="HAVE_ADVANCE_PURCHASE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.haveAdvancePurchase,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="ADVANCE_PURCHASE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.advancePurchaseStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="ADVANCE_PURCHASE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.advancePurchaseComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ADVANCE_PURCHASE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.advancePurchaseTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="IS_URGENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isUrgent,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="URGENT_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.urgentAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="HAVE_COMMUNICATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.haveCommunicate,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="PREPARE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.prepareComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MARKETING_PLAN = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.marketingPlan,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="STATUS_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.statusComments,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SYNC_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.syncStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_API_SYNC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.logisticsApiSync,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_WXSEND_SYNC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.logisticsWxsendSync,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="SATISFY_INVOICE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.satisfyInvoiceTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="SATISFY_DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.satisfyDeliveryTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="IS_SALES_PERFORMANCE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isSalesPerformance,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="SALES_PERFORMANCE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.salesPerformanceTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="SALES_PERFORMANCE_MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.salesPerformanceModTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="IS_DELAY_INVOICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isDelayInvoice,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="INVOICE_METHOD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceMethod,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="LOCKED_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.lockedReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="COST_USER_IDS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.costUserIds,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="OWNER_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.ownerUserId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INVOICE_EMAIL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceEmail,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PAYMENT_MODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.paymentMode,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="PAY_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.payType,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_APPLY_INVOICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isApplyInvoice,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="APPLY_INVOICE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.applyInvoiceTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADK_SALEORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.adkSaleorderNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="CREATE_MOBILE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.createMobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BDTRADER_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.bdtraderComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="CLOSE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.closeComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BD_MOBILE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.bdMobileTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="WEB_TAKE_DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.webTakeDeliveryTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="ACTION_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.actionId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_COUPONS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isCoupons,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="COUPONMONEY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.couponmoney,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="ORIGINAL_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.originalAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="EL_SALEORDRE_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.elSaleordreNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_PRINTOUT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isPrintout,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="OUT_IS_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.outIsFlag,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="UPDATE_DATA_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.updateDataTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="REAL_PAY_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.realPayAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="REAL_RETURN_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.realReturnAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="REAL_TOTAL_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.realTotalAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="SEND_TO_PC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.sendToPc,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="RETENTION_MONEY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.retentionMoney,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="RETENTION_MONEY_DAY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.retentionMoneyDay,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_SAME_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isSameAddress,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="INVOICE_SEND_NODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceSendNode,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="NOPAYBACK_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.nopaybackAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="CONTRACT_URL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.contractUrl,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="AUTO_AUDIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.autoAudit,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_RISK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isRisk,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="RISK_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.riskComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="RISK_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.riskTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="GROUP_CUSTOMER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.groupCustomerId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_CONTRACT_RETURN = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isContractReturn,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="IS_DELIVERYORDER_RETURN = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isDeliveryorderReturn,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_CLAIM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.deliveryClaim,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="DELIVERY_DELAY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.deliveryDelayTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="BILL_PERIOD_SETTLEMENT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.billPeriodSettlementType,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="IS_NEW = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isNew,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="CONFIRM_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.confirmStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CONFIRM_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.confirmTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CONTRACT_NO_STAMP_URL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.contractNoStampUrl,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ONLINE_RECEIPT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.onlineReceiptStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="CONFIRMATION_FORM_UPLOAD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.confirmationFormUpload,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="CONFIRMATION_FORM_AUDIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.confirmationFormAudit,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="CONFIRMATION_SUBMIT_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.confirmationSubmitTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="PREPARE_REASEON_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.prepareReaseonType,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where SALEORDER_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.saleorderId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_SALEORDER
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="QUOTEORDER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.quoteorderId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.quoteorderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PARENT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.parentId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.parentId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SALEORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.saleorderNo != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.saleorderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="M_SALEORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mSaleorderNo != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.mSaleorderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORDER_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderType != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.orderType,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="COMPANY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.companyId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.companyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`SOURCE` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.source != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.source,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorOrgId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.creatorOrgId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_ORG_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorOrgName != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.creatorOrgName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orgId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.orgId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORG_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orgName != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.orgName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.userId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="VALID_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.validOrgId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.validOrgId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="VALID_ORG_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.validOrgName != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.validOrgName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="VALID_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.validUserId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.validUserId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="VALID_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.validStatus != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.validStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="VALID_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.validTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.validTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="END_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.endTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.endTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`STATUS` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.status,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="PURCHASE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.purchaseStatus != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.purchaseStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="LOCKED_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.lockedStatus != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.lockedStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceStatus != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAYMENT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.paymentStatus != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.paymentStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAYMENT_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.paymentTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.paymentTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryStatus != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.deliveryStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.deliveryTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_CUSTOMER_ARRIVAL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isCustomerArrival != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isCustomerArrival,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalStatus != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.arrivalStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ARRIVAL_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.arrivalTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.arrivalTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="SERVICE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.serviceStatus != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.serviceStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="HAVE_ACCOUNT_PERIOD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.haveAccountPeriod != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.haveAccountPeriod,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_PAYMENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isPayment != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isPayment,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="TOTAL_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.totalAmount != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.totalAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CUSTOMER_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.customerType != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.customerType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CUSTOMER_NATURE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.customerNature != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.customerNature,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderName != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderContactId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderContactId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderContactName != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderContactName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_MOBILE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderContactMobile != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderContactMobile,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_TELEPHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderContactTelephone != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderContactTelephone,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_ADDRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderAddressId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderAddressId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_AREA_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderAreaId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderAreaId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_AREA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderArea != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderArea,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderAddress != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderAddress,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderComments != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.traderComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.takeTraderId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.takeTraderName != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_CONTACT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.takeTraderContactId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderContactId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_CONTACT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.takeTraderContactName != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderContactName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_CONTACT_MOBILE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.takeTraderContactMobile != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderContactMobile,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_CONTACT_TELEPHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.takeTraderContactTelephone != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderContactTelephone,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_ADDRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.takeTraderAddressId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderAddressId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_AREA_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.takeTraderAreaId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderAreaId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_AREA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.takeTraderArea != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderArea,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TAKE_TRADER_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.takeTraderAddress != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.takeTraderAddress,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_SEND_INVOICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isSendInvoice != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isSendInvoice,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderName != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderContactId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderContactId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderContactName != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderContactName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_MOBILE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderContactMobile != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderContactMobile,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_TELEPHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderContactTelephone != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderContactTelephone,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_ADDRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderAddressId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderAddressId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_AREA_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderAreaId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderAreaId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_AREA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderArea != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderArea,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderAddress != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceTraderAddress,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SALES_AREA_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.salesAreaId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.salesAreaId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SALES_AREA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.salesArea != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.salesArea,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TERMINAL_TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.terminalTraderId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.terminalTraderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TERMINAL_TRADER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.terminalTraderName != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.terminalTraderName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TERMINAL_TRADER_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.terminalTraderType != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.terminalTraderType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceType != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="FREIGHT_DESCRIPTION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.freightDescription != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.freightDescription,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryType != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.deliveryType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_METHOD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryMethod != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.deliveryMethod,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logisticsId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.logisticsId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAYMENT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.paymentType != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.paymentType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PREPAID_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.prepaidAmount != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.prepaidAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="ACCOUNT_PERIOD_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.accountPeriodAmount != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.accountPeriodAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="PERIOD_DAY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.periodDay != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.periodDay,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_COLLECTION = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logisticsCollection != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.logisticsCollection,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="RETAINAGE_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.retainageAmount != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.retainageAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="RETAINAGE_AMOUNT_MONTH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.retainageAmountMonth != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.retainageAmountMonth,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAYMENT_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.paymentComments != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.paymentComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADDITIONAL_CLAUSE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.additionalClause != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.additionalClause,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logisticsComments != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.logisticsComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FINANCE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.financeComments != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.financeComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.comments != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.comments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceComments != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_DIRECT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryDirect != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.deliveryDirect,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="SUPPLIER_CLAUSE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.supplierClause != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.supplierClause,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="HAVE_ADVANCE_PURCHASE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.haveAdvancePurchase != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.haveAdvancePurchase,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADVANCE_PURCHASE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.advancePurchaseStatus != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.advancePurchaseStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADVANCE_PURCHASE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.advancePurchaseComments != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.advancePurchaseComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADVANCE_PURCHASE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.advancePurchaseTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.advancePurchaseTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_URGENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isUrgent != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isUrgent,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="URGENT_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.urgentAmount != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.urgentAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="HAVE_COMMUNICATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.haveCommunicate != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.haveCommunicate,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="PREPARE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.prepareComments != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.prepareComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MARKETING_PLAN = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.marketingPlan != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.marketingPlan,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="STATUS_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.statusComments != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.statusComments,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SYNC_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.syncStatus != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.syncStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_API_SYNC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logisticsApiSync != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.logisticsApiSync,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="LOGISTICS_WXSEND_SYNC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logisticsWxsendSync != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.logisticsWxsendSync,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="SATISFY_INVOICE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.satisfyInvoiceTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.satisfyInvoiceTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="SATISFY_DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.satisfyDeliveryTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.satisfyDeliveryTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_SALES_PERFORMANCE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isSalesPerformance != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isSalesPerformance,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="SALES_PERFORMANCE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.salesPerformanceTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.salesPerformanceTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="SALES_PERFORMANCE_MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.salesPerformanceModTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.salesPerformanceModTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELAY_INVOICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelayInvoice != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isDelayInvoice,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_METHOD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceMethod != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceMethod,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="LOCKED_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.lockedReason != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.lockedReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="COST_USER_IDS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.costUserIds != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.costUserIds,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="OWNER_USER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ownerUserId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.ownerUserId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_EMAIL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceEmail != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceEmail,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAYMENT_MODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.paymentMode != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.paymentMode,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAY_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.payType != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.payType,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_APPLY_INVOICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isApplyInvoice != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isApplyInvoice,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="APPLY_INVOICE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.applyInvoiceTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.applyInvoiceTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.modTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADK_SALEORDER_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.adkSaleorderNo != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.adkSaleorderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATE_MOBILE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createMobile != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.createMobile,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BDTRADER_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bdtraderComments != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.bdtraderComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CLOSE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.closeComments != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.closeComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BD_MOBILE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bdMobileTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.bdMobileTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="WEB_TAKE_DELIVERY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.webTakeDeliveryTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.webTakeDeliveryTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="ACTION_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.actionId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.actionId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_COUPONS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isCoupons != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isCoupons,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="COUPONMONEY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.couponmoney != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.couponmoney,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="ORIGINAL_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.originalAmount != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.originalAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="EL_SALEORDRE_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.elSaleordreNo != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.elSaleordreNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_PRINTOUT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isPrintout != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isPrintout,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="OUT_IS_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.outIsFlag != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.outIsFlag,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_DATA_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateDataTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.updateDataTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="REAL_PAY_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.realPayAmount != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.realPayAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="REAL_RETURN_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.realReturnAmount != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.realReturnAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="REAL_TOTAL_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.realTotalAmount != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.realTotalAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="SEND_TO_PC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sendToPc != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.sendToPc,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="RETENTION_MONEY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.retentionMoney != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.retentionMoney,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="RETENTION_MONEY_DAY = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.retentionMoneyDay != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.retentionMoneyDay,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_SAME_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isSameAddress != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isSameAddress,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_SEND_NODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceSendNode != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.invoiceSendNode,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="NOPAYBACK_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.nopaybackAmount != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.nopaybackAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONTRACT_URL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.contractUrl != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.contractUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="AUTO_AUDIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.autoAudit != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.autoAudit,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_RISK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isRisk != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isRisk,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="RISK_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.riskComments != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.riskComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="RISK_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.riskTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.riskTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="GROUP_CUSTOMER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.groupCustomerId != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.groupCustomerId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_CONTRACT_RETURN = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isContractReturn != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isContractReturn,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELIVERYORDER_RETURN = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeliveryorderReturn != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isDeliveryorderReturn,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_CLAIM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryClaim != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.deliveryClaim,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELIVERY_DELAY_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deliveryDelayTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.deliveryDelayTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="BILL_PERIOD_SETTLEMENT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.billPeriodSettlementType != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.billPeriodSettlementType,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_NEW = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isNew != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.isNew,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONFIRM_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.confirmStatus != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.confirmStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONFIRM_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.confirmTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.confirmTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONTRACT_NO_STAMP_URL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.contractNoStampUrl != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.contractNoStampUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ONLINE_RECEIPT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.onlineReceiptStatus != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.onlineReceiptStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONFIRMATION_FORM_UPLOAD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.confirmationFormUpload != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.confirmationFormUpload,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONFIRMATION_FORM_AUDIT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.confirmationFormAudit != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.confirmationFormAudit,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="CONFIRMATION_SUBMIT_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.confirmationSubmitTime != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.confirmationSubmitTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="PREPARE_REASEON_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.prepareReaseonType != null">
            when SALEORDER_ID = #{item.saleorderId,jdbcType=INTEGER} then #{item.prepareReaseonType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where SALEORDER_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.saleorderId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="SALEORDER_ID" keyProperty="saleorderId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SALEORDER
    (QUOTEORDER_ID, PARENT_ID, SALEORDER_NO, M_SALEORDER_NO, ORDER_TYPE, COMPANY_ID, 
      `SOURCE`, CREATOR_ORG_ID, CREATOR_ORG_NAME, ORG_ID, ORG_NAME, USER_ID, VALID_ORG_ID, 
      VALID_ORG_NAME, VALID_USER_ID, VALID_STATUS, VALID_TIME, END_TIME, `STATUS`, PURCHASE_STATUS, 
      LOCKED_STATUS, INVOICE_STATUS, INVOICE_TIME, PAYMENT_STATUS, PAYMENT_TIME, DELIVERY_STATUS, 
      DELIVERY_TIME, IS_CUSTOMER_ARRIVAL, ARRIVAL_STATUS, ARRIVAL_TIME, SERVICE_STATUS, 
      HAVE_ACCOUNT_PERIOD, IS_PAYMENT, TOTAL_AMOUNT, TRADER_ID, CUSTOMER_TYPE, CUSTOMER_NATURE, 
      TRADER_NAME, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, TRADER_CONTACT_TELEPHONE, 
      TRADER_ADDRESS_ID, TRADER_AREA_ID, TRADER_AREA, TRADER_ADDRESS, TRADER_COMMENTS, 
      TAKE_TRADER_ID, TAKE_TRADER_NAME, TAKE_TRADER_CONTACT_ID, TAKE_TRADER_CONTACT_NAME, 
      TAKE_TRADER_CONTACT_MOBILE, TAKE_TRADER_CONTACT_TELEPHONE, TAKE_TRADER_ADDRESS_ID, 
      TAKE_TRADER_AREA_ID, TAKE_TRADER_AREA, TAKE_TRADER_ADDRESS, IS_SEND_INVOICE, INVOICE_TRADER_ID, 
      INVOICE_TRADER_NAME, INVOICE_TRADER_CONTACT_ID, INVOICE_TRADER_CONTACT_NAME, INVOICE_TRADER_CONTACT_MOBILE, 
      INVOICE_TRADER_CONTACT_TELEPHONE, INVOICE_TRADER_ADDRESS_ID, INVOICE_TRADER_AREA_ID, 
      INVOICE_TRADER_AREA, INVOICE_TRADER_ADDRESS, SALES_AREA_ID, SALES_AREA, TERMINAL_TRADER_ID, 
      TERMINAL_TRADER_NAME, TERMINAL_TRADER_TYPE, INVOICE_TYPE, FREIGHT_DESCRIPTION, 
      DELIVERY_TYPE, DELIVERY_METHOD, LOGISTICS_ID, PAYMENT_TYPE, PREPAID_AMOUNT, ACCOUNT_PERIOD_AMOUNT, 
      PERIOD_DAY, LOGISTICS_COLLECTION, RETAINAGE_AMOUNT, RETAINAGE_AMOUNT_MONTH, PAYMENT_COMMENTS, 
      ADDITIONAL_CLAUSE, LOGISTICS_COMMENTS, FINANCE_COMMENTS, COMMENTS, INVOICE_COMMENTS, 
      DELIVERY_DIRECT, SUPPLIER_CLAUSE, HAVE_ADVANCE_PURCHASE, ADVANCE_PURCHASE_STATUS, 
      ADVANCE_PURCHASE_COMMENTS, ADVANCE_PURCHASE_TIME, IS_URGENT, URGENT_AMOUNT, HAVE_COMMUNICATE, 
      PREPARE_COMMENTS, MARKETING_PLAN, STATUS_COMMENTS, SYNC_STATUS, LOGISTICS_API_SYNC, 
      LOGISTICS_WXSEND_SYNC, SATISFY_INVOICE_TIME, SATISFY_DELIVERY_TIME, IS_SALES_PERFORMANCE, 
      SALES_PERFORMANCE_TIME, SALES_PERFORMANCE_MOD_TIME, IS_DELAY_INVOICE, INVOICE_METHOD, 
      LOCKED_REASON, COST_USER_IDS, OWNER_USER_ID, INVOICE_EMAIL, PAYMENT_MODE, PAY_TYPE, 
      IS_APPLY_INVOICE, APPLY_INVOICE_TIME, ADD_TIME, CREATOR, MOD_TIME, UPDATER, ADK_SALEORDER_NO, 
      CREATE_MOBILE, BDTRADER_COMMENTS, CLOSE_COMMENTS, BD_MOBILE_TIME, WEB_TAKE_DELIVERY_TIME, 
      ACTION_ID, IS_COUPONS, COUPONMONEY, ORIGINAL_AMOUNT, EL_SALEORDRE_NO, IS_PRINTOUT, 
      OUT_IS_FLAG, UPDATE_DATA_TIME, REAL_PAY_AMOUNT, REAL_RETURN_AMOUNT, REAL_TOTAL_AMOUNT, 
      SEND_TO_PC, RETENTION_MONEY, RETENTION_MONEY_DAY, IS_SAME_ADDRESS, INVOICE_SEND_NODE, 
      IS_DELETE, NOPAYBACK_AMOUNT, CONTRACT_URL, AUTO_AUDIT, IS_RISK, RISK_COMMENTS, 
      RISK_TIME, GROUP_CUSTOMER_ID, IS_CONTRACT_RETURN, IS_DELIVERYORDER_RETURN, DELIVERY_CLAIM, 
      DELIVERY_DELAY_TIME, BILL_PERIOD_SETTLEMENT_TYPE, IS_NEW, CONFIRM_STATUS, CONFIRM_TIME, 
      CONTRACT_NO_STAMP_URL, ONLINE_RECEIPT_STATUS, CONFIRMATION_FORM_UPLOAD, CONFIRMATION_FORM_AUDIT, 
      CONFIRMATION_SUBMIT_TIME, PREPARE_REASEON_TYPE)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.quoteorderId,jdbcType=INTEGER}, #{item.parentId,jdbcType=INTEGER}, #{item.saleorderNo,jdbcType=VARCHAR}, 
        #{item.mSaleorderNo,jdbcType=VARCHAR}, #{item.orderType,jdbcType=BOOLEAN}, #{item.companyId,jdbcType=INTEGER}, 
        #{item.source,jdbcType=BOOLEAN}, #{item.creatorOrgId,jdbcType=INTEGER}, #{item.creatorOrgName,jdbcType=VARCHAR}, 
        #{item.orgId,jdbcType=INTEGER}, #{item.orgName,jdbcType=VARCHAR}, #{item.userId,jdbcType=INTEGER}, 
        #{item.validOrgId,jdbcType=INTEGER}, #{item.validOrgName,jdbcType=VARCHAR}, #{item.validUserId,jdbcType=INTEGER}, 
        #{item.validStatus,jdbcType=BOOLEAN}, #{item.validTime,jdbcType=BIGINT}, #{item.endTime,jdbcType=BIGINT}, 
        #{item.status,jdbcType=BOOLEAN}, #{item.purchaseStatus,jdbcType=BOOLEAN}, #{item.lockedStatus,jdbcType=BOOLEAN}, 
        #{item.invoiceStatus,jdbcType=BOOLEAN}, #{item.invoiceTime,jdbcType=BIGINT}, #{item.paymentStatus,jdbcType=INTEGER},
        #{item.paymentTime,jdbcType=BIGINT}, #{item.deliveryStatus,jdbcType=BOOLEAN}, #{item.deliveryTime,jdbcType=BIGINT}, 
        #{item.isCustomerArrival,jdbcType=BOOLEAN}, #{item.arrivalStatus,jdbcType=BOOLEAN}, 
        #{item.arrivalTime,jdbcType=BIGINT}, #{item.serviceStatus,jdbcType=BOOLEAN}, #{item.haveAccountPeriod,jdbcType=BOOLEAN}, 
        #{item.isPayment,jdbcType=BOOLEAN}, #{item.totalAmount,jdbcType=DECIMAL}, #{item.traderId,jdbcType=INTEGER}, 
        #{item.customerType,jdbcType=INTEGER}, #{item.customerNature,jdbcType=INTEGER}, 
        #{item.traderName,jdbcType=VARCHAR}, #{item.traderContactId,jdbcType=INTEGER}, 
        #{item.traderContactName,jdbcType=VARCHAR}, #{item.traderContactMobile,jdbcType=VARCHAR}, 
        #{item.traderContactTelephone,jdbcType=VARCHAR}, #{item.traderAddressId,jdbcType=INTEGER}, 
        #{item.traderAreaId,jdbcType=INTEGER}, #{item.traderArea,jdbcType=VARCHAR}, #{item.traderAddress,jdbcType=VARCHAR}, 
        #{item.traderComments,jdbcType=VARCHAR}, #{item.takeTraderId,jdbcType=INTEGER}, 
        #{item.takeTraderName,jdbcType=VARCHAR}, #{item.takeTraderContactId,jdbcType=INTEGER}, 
        #{item.takeTraderContactName,jdbcType=VARCHAR}, #{item.takeTraderContactMobile,jdbcType=VARCHAR}, 
        #{item.takeTraderContactTelephone,jdbcType=VARCHAR}, #{item.takeTraderAddressId,jdbcType=INTEGER}, 
        #{item.takeTraderAreaId,jdbcType=INTEGER}, #{item.takeTraderArea,jdbcType=VARCHAR}, 
        #{item.takeTraderAddress,jdbcType=VARCHAR}, #{item.isSendInvoice,jdbcType=BOOLEAN}, 
        #{item.invoiceTraderId,jdbcType=INTEGER}, #{item.invoiceTraderName,jdbcType=VARCHAR}, 
        #{item.invoiceTraderContactId,jdbcType=INTEGER}, #{item.invoiceTraderContactName,jdbcType=VARCHAR}, 
        #{item.invoiceTraderContactMobile,jdbcType=VARCHAR}, #{item.invoiceTraderContactTelephone,jdbcType=VARCHAR}, 
        #{item.invoiceTraderAddressId,jdbcType=INTEGER}, #{item.invoiceTraderAreaId,jdbcType=INTEGER}, 
        #{item.invoiceTraderArea,jdbcType=VARCHAR}, #{item.invoiceTraderAddress,jdbcType=VARCHAR}, 
        #{item.salesAreaId,jdbcType=INTEGER}, #{item.salesArea,jdbcType=VARCHAR}, #{item.terminalTraderId,jdbcType=INTEGER}, 
        #{item.terminalTraderName,jdbcType=VARCHAR}, #{item.terminalTraderType,jdbcType=INTEGER}, 
        #{item.invoiceType,jdbcType=INTEGER}, #{item.freightDescription,jdbcType=INTEGER}, 
        #{item.deliveryType,jdbcType=INTEGER}, #{item.deliveryMethod,jdbcType=INTEGER}, 
        #{item.logisticsId,jdbcType=INTEGER}, #{item.paymentType,jdbcType=INTEGER}, #{item.prepaidAmount,jdbcType=DECIMAL}, 
        #{item.accountPeriodAmount,jdbcType=DECIMAL}, #{item.periodDay,jdbcType=INTEGER}, 
        #{item.logisticsCollection,jdbcType=BOOLEAN}, #{item.retainageAmount,jdbcType=DECIMAL}, 
        #{item.retainageAmountMonth,jdbcType=INTEGER}, #{item.paymentComments,jdbcType=VARCHAR}, 
        #{item.additionalClause,jdbcType=VARCHAR}, #{item.logisticsComments,jdbcType=VARCHAR}, 
        #{item.financeComments,jdbcType=VARCHAR}, #{item.comments,jdbcType=VARCHAR}, #{item.invoiceComments,jdbcType=VARCHAR}, 
        #{item.deliveryDirect,jdbcType=BOOLEAN}, #{item.supplierClause,jdbcType=VARCHAR}, 
        #{item.haveAdvancePurchase,jdbcType=BOOLEAN}, #{item.advancePurchaseStatus,jdbcType=BOOLEAN}, 
        #{item.advancePurchaseComments,jdbcType=VARCHAR}, #{item.advancePurchaseTime,jdbcType=BIGINT}, 
        #{item.isUrgent,jdbcType=BOOLEAN}, #{item.urgentAmount,jdbcType=DECIMAL}, #{item.haveCommunicate,jdbcType=BOOLEAN}, 
        #{item.prepareComments,jdbcType=VARCHAR}, #{item.marketingPlan,jdbcType=VARCHAR}, 
        #{item.statusComments,jdbcType=INTEGER}, #{item.syncStatus,jdbcType=BOOLEAN}, #{item.logisticsApiSync,jdbcType=BOOLEAN}, 
        #{item.logisticsWxsendSync,jdbcType=BOOLEAN}, #{item.satisfyInvoiceTime,jdbcType=BIGINT}, 
        #{item.satisfyDeliveryTime,jdbcType=BIGINT}, #{item.isSalesPerformance,jdbcType=BOOLEAN}, 
        #{item.salesPerformanceTime,jdbcType=BIGINT}, #{item.salesPerformanceModTime,jdbcType=BIGINT}, 
        #{item.isDelayInvoice,jdbcType=BOOLEAN}, #{item.invoiceMethod,jdbcType=BOOLEAN}, 
        #{item.lockedReason,jdbcType=VARCHAR}, #{item.costUserIds,jdbcType=VARCHAR}, #{item.ownerUserId,jdbcType=INTEGER}, 
        #{item.invoiceEmail,jdbcType=VARCHAR}, #{item.paymentMode,jdbcType=BOOLEAN}, #{item.payType,jdbcType=BOOLEAN}, 
        #{item.isApplyInvoice,jdbcType=BOOLEAN}, #{item.applyInvoiceTime,jdbcType=BIGINT}, 
        #{item.addTime,jdbcType=BIGINT}, #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=BIGINT}, 
        #{item.updater,jdbcType=INTEGER}, #{item.adkSaleorderNo,jdbcType=VARCHAR}, #{item.createMobile,jdbcType=VARCHAR}, 
        #{item.bdtraderComments,jdbcType=VARCHAR}, #{item.closeComments,jdbcType=VARCHAR}, 
        #{item.bdMobileTime,jdbcType=BIGINT}, #{item.webTakeDeliveryTime,jdbcType=BIGINT}, 
        #{item.actionId,jdbcType=INTEGER}, #{item.isCoupons,jdbcType=BOOLEAN}, #{item.couponmoney,jdbcType=DECIMAL}, 
        #{item.originalAmount,jdbcType=DECIMAL}, #{item.elSaleordreNo,jdbcType=VARCHAR}, 
        #{item.isPrintout,jdbcType=BOOLEAN}, #{item.outIsFlag,jdbcType=BOOLEAN}, #{item.updateDataTime,jdbcType=TIMESTAMP}, 
        #{item.realPayAmount,jdbcType=DECIMAL}, #{item.realReturnAmount,jdbcType=DECIMAL}, 
        #{item.realTotalAmount,jdbcType=DECIMAL}, #{item.sendToPc,jdbcType=BOOLEAN}, #{item.retentionMoney,jdbcType=DECIMAL}, 
        #{item.retentionMoneyDay,jdbcType=INTEGER}, #{item.isSameAddress,jdbcType=BOOLEAN}, 
        #{item.invoiceSendNode,jdbcType=BOOLEAN}, #{item.isDelete,jdbcType=TINYINT}, #{item.nopaybackAmount,jdbcType=DECIMAL}, 
        #{item.contractUrl,jdbcType=VARCHAR}, #{item.autoAudit,jdbcType=INTEGER}, #{item.isRisk,jdbcType=BOOLEAN}, 
        #{item.riskComments,jdbcType=VARCHAR}, #{item.riskTime,jdbcType=BIGINT}, #{item.groupCustomerId,jdbcType=INTEGER}, 
        #{item.isContractReturn,jdbcType=TINYINT}, #{item.isDeliveryorderReturn,jdbcType=TINYINT}, 
        #{item.deliveryClaim,jdbcType=BOOLEAN}, #{item.deliveryDelayTime,jdbcType=BIGINT}, 
        #{item.billPeriodSettlementType,jdbcType=TINYINT}, #{item.isNew,jdbcType=TINYINT}, 
        #{item.confirmStatus,jdbcType=INTEGER}, #{item.confirmTime,jdbcType=TIMESTAMP}, 
        #{item.contractNoStampUrl,jdbcType=VARCHAR}, #{item.onlineReceiptStatus,jdbcType=BOOLEAN}, 
        #{item.confirmationFormUpload,jdbcType=BOOLEAN}, #{item.confirmationFormAudit,jdbcType=BOOLEAN}, 
        #{item.confirmationSubmitTime,jdbcType=BIGINT}, #{item.prepareReaseonType,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="SALEORDER_ID" keyProperty="saleorderId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SALEORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        SALEORDER_ID,
      </if>
      QUOTEORDER_ID,
      PARENT_ID,
      SALEORDER_NO,
      M_SALEORDER_NO,
      ORDER_TYPE,
      COMPANY_ID,
      `SOURCE`,
      CREATOR_ORG_ID,
      CREATOR_ORG_NAME,
      ORG_ID,
      ORG_NAME,
      USER_ID,
      VALID_ORG_ID,
      VALID_ORG_NAME,
      VALID_USER_ID,
      VALID_STATUS,
      VALID_TIME,
      END_TIME,
      `STATUS`,
      PURCHASE_STATUS,
      LOCKED_STATUS,
      INVOICE_STATUS,
      INVOICE_TIME,
      PAYMENT_STATUS,
      PAYMENT_TIME,
      DELIVERY_STATUS,
      DELIVERY_TIME,
      IS_CUSTOMER_ARRIVAL,
      ARRIVAL_STATUS,
      ARRIVAL_TIME,
      SERVICE_STATUS,
      HAVE_ACCOUNT_PERIOD,
      IS_PAYMENT,
      TOTAL_AMOUNT,
      TRADER_ID,
      CUSTOMER_TYPE,
      CUSTOMER_NATURE,
      TRADER_NAME,
      TRADER_CONTACT_ID,
      TRADER_CONTACT_NAME,
      TRADER_CONTACT_MOBILE,
      TRADER_CONTACT_TELEPHONE,
      TRADER_ADDRESS_ID,
      TRADER_AREA_ID,
      TRADER_AREA,
      TRADER_ADDRESS,
      TRADER_COMMENTS,
      TAKE_TRADER_ID,
      TAKE_TRADER_NAME,
      TAKE_TRADER_CONTACT_ID,
      TAKE_TRADER_CONTACT_NAME,
      TAKE_TRADER_CONTACT_MOBILE,
      TAKE_TRADER_CONTACT_TELEPHONE,
      TAKE_TRADER_ADDRESS_ID,
      TAKE_TRADER_AREA_ID,
      TAKE_TRADER_AREA,
      TAKE_TRADER_ADDRESS,
      IS_SEND_INVOICE,
      INVOICE_TRADER_ID,
      INVOICE_TRADER_NAME,
      INVOICE_TRADER_CONTACT_ID,
      INVOICE_TRADER_CONTACT_NAME,
      INVOICE_TRADER_CONTACT_MOBILE,
      INVOICE_TRADER_CONTACT_TELEPHONE,
      INVOICE_TRADER_ADDRESS_ID,
      INVOICE_TRADER_AREA_ID,
      INVOICE_TRADER_AREA,
      INVOICE_TRADER_ADDRESS,
      SALES_AREA_ID,
      SALES_AREA,
      TERMINAL_TRADER_ID,
      TERMINAL_TRADER_NAME,
      TERMINAL_TRADER_TYPE,
      INVOICE_TYPE,
      FREIGHT_DESCRIPTION,
      DELIVERY_TYPE,
      DELIVERY_METHOD,
      LOGISTICS_ID,
      PAYMENT_TYPE,
      PREPAID_AMOUNT,
      ACCOUNT_PERIOD_AMOUNT,
      PERIOD_DAY,
      LOGISTICS_COLLECTION,
      RETAINAGE_AMOUNT,
      RETAINAGE_AMOUNT_MONTH,
      PAYMENT_COMMENTS,
      ADDITIONAL_CLAUSE,
      LOGISTICS_COMMENTS,
      FINANCE_COMMENTS,
      COMMENTS,
      INVOICE_COMMENTS,
      DELIVERY_DIRECT,
      SUPPLIER_CLAUSE,
      HAVE_ADVANCE_PURCHASE,
      ADVANCE_PURCHASE_STATUS,
      ADVANCE_PURCHASE_COMMENTS,
      ADVANCE_PURCHASE_TIME,
      IS_URGENT,
      URGENT_AMOUNT,
      HAVE_COMMUNICATE,
      PREPARE_COMMENTS,
      MARKETING_PLAN,
      STATUS_COMMENTS,
      SYNC_STATUS,
      LOGISTICS_API_SYNC,
      LOGISTICS_WXSEND_SYNC,
      SATISFY_INVOICE_TIME,
      SATISFY_DELIVERY_TIME,
      IS_SALES_PERFORMANCE,
      SALES_PERFORMANCE_TIME,
      SALES_PERFORMANCE_MOD_TIME,
      IS_DELAY_INVOICE,
      INVOICE_METHOD,
      LOCKED_REASON,
      COST_USER_IDS,
      OWNER_USER_ID,
      INVOICE_EMAIL,
      PAYMENT_MODE,
      PAY_TYPE,
      IS_APPLY_INVOICE,
      APPLY_INVOICE_TIME,
      ADD_TIME,
      CREATOR,
      MOD_TIME,
      UPDATER,
      ADK_SALEORDER_NO,
      CREATE_MOBILE,
      BDTRADER_COMMENTS,
      CLOSE_COMMENTS,
      BD_MOBILE_TIME,
      WEB_TAKE_DELIVERY_TIME,
      ACTION_ID,
      IS_COUPONS,
      COUPONMONEY,
      ORIGINAL_AMOUNT,
      EL_SALEORDRE_NO,
      IS_PRINTOUT,
      OUT_IS_FLAG,
      UPDATE_DATA_TIME,
      REAL_PAY_AMOUNT,
      REAL_RETURN_AMOUNT,
      REAL_TOTAL_AMOUNT,
      SEND_TO_PC,
      RETENTION_MONEY,
      RETENTION_MONEY_DAY,
      IS_SAME_ADDRESS,
      INVOICE_SEND_NODE,
      IS_DELETE,
      NOPAYBACK_AMOUNT,
      CONTRACT_URL,
      AUTO_AUDIT,
      IS_RISK,
      RISK_COMMENTS,
      RISK_TIME,
      GROUP_CUSTOMER_ID,
      IS_CONTRACT_RETURN,
      IS_DELIVERYORDER_RETURN,
      DELIVERY_CLAIM,
      DELIVERY_DELAY_TIME,
      BILL_PERIOD_SETTLEMENT_TYPE,
      IS_NEW,
      CONFIRM_STATUS,
      CONFIRM_TIME,
      CONTRACT_NO_STAMP_URL,
      ONLINE_RECEIPT_STATUS,
      CONFIRMATION_FORM_UPLOAD,
      CONFIRMATION_FORM_AUDIT,
      CONFIRMATION_SUBMIT_TIME,
      PREPARE_REASEON_TYPE,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        #{saleorderId,jdbcType=INTEGER},
      </if>
      #{quoteorderId,jdbcType=INTEGER},
      #{parentId,jdbcType=INTEGER},
      #{saleorderNo,jdbcType=VARCHAR},
      #{mSaleorderNo,jdbcType=VARCHAR},
      #{orderType,jdbcType=BOOLEAN},
      #{companyId,jdbcType=INTEGER},
      #{source,jdbcType=BOOLEAN},
      #{creatorOrgId,jdbcType=INTEGER},
      #{creatorOrgName,jdbcType=VARCHAR},
      #{orgId,jdbcType=INTEGER},
      #{orgName,jdbcType=VARCHAR},
      #{userId,jdbcType=INTEGER},
      #{validOrgId,jdbcType=INTEGER},
      #{validOrgName,jdbcType=VARCHAR},
      #{validUserId,jdbcType=INTEGER},
      #{validStatus,jdbcType=BOOLEAN},
      #{validTime,jdbcType=BIGINT},
      #{endTime,jdbcType=BIGINT},
      #{status,jdbcType=BOOLEAN},
      #{purchaseStatus,jdbcType=BOOLEAN},
      #{lockedStatus,jdbcType=BOOLEAN},
      #{invoiceStatus,jdbcType=BOOLEAN},
      #{invoiceTime,jdbcType=BIGINT},
      #{paymentStatus,jdbcType=INTEGER},
      #{paymentTime,jdbcType=BIGINT},
      #{deliveryStatus,jdbcType=BOOLEAN},
      #{deliveryTime,jdbcType=BIGINT},
      #{isCustomerArrival,jdbcType=BOOLEAN},
      #{arrivalStatus,jdbcType=BOOLEAN},
      #{arrivalTime,jdbcType=BIGINT},
      #{serviceStatus,jdbcType=BOOLEAN},
      #{haveAccountPeriod,jdbcType=BOOLEAN},
      #{isPayment,jdbcType=BOOLEAN},
      #{totalAmount,jdbcType=DECIMAL},
      #{traderId,jdbcType=INTEGER},
      #{customerType,jdbcType=INTEGER},
      #{customerNature,jdbcType=INTEGER},
      #{traderName,jdbcType=VARCHAR},
      #{traderContactId,jdbcType=INTEGER},
      #{traderContactName,jdbcType=VARCHAR},
      #{traderContactMobile,jdbcType=VARCHAR},
      #{traderContactTelephone,jdbcType=VARCHAR},
      #{traderAddressId,jdbcType=INTEGER},
      #{traderAreaId,jdbcType=INTEGER},
      #{traderArea,jdbcType=VARCHAR},
      #{traderAddress,jdbcType=VARCHAR},
      #{traderComments,jdbcType=VARCHAR},
      #{takeTraderId,jdbcType=INTEGER},
      #{takeTraderName,jdbcType=VARCHAR},
      #{takeTraderContactId,jdbcType=INTEGER},
      #{takeTraderContactName,jdbcType=VARCHAR},
      #{takeTraderContactMobile,jdbcType=VARCHAR},
      #{takeTraderContactTelephone,jdbcType=VARCHAR},
      #{takeTraderAddressId,jdbcType=INTEGER},
      #{takeTraderAreaId,jdbcType=INTEGER},
      #{takeTraderArea,jdbcType=VARCHAR},
      #{takeTraderAddress,jdbcType=VARCHAR},
      #{isSendInvoice,jdbcType=BOOLEAN},
      #{invoiceTraderId,jdbcType=INTEGER},
      #{invoiceTraderName,jdbcType=VARCHAR},
      #{invoiceTraderContactId,jdbcType=INTEGER},
      #{invoiceTraderContactName,jdbcType=VARCHAR},
      #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      #{invoiceTraderAddressId,jdbcType=INTEGER},
      #{invoiceTraderAreaId,jdbcType=INTEGER},
      #{invoiceTraderArea,jdbcType=VARCHAR},
      #{invoiceTraderAddress,jdbcType=VARCHAR},
      #{salesAreaId,jdbcType=INTEGER},
      #{salesArea,jdbcType=VARCHAR},
      #{terminalTraderId,jdbcType=INTEGER},
      #{terminalTraderName,jdbcType=VARCHAR},
      #{terminalTraderType,jdbcType=INTEGER},
      #{invoiceType,jdbcType=INTEGER},
      #{freightDescription,jdbcType=INTEGER},
      #{deliveryType,jdbcType=INTEGER},
      #{deliveryMethod,jdbcType=INTEGER},
      #{logisticsId,jdbcType=INTEGER},
      #{paymentType,jdbcType=INTEGER},
      #{prepaidAmount,jdbcType=DECIMAL},
      #{accountPeriodAmount,jdbcType=DECIMAL},
      #{periodDay,jdbcType=INTEGER},
      #{logisticsCollection,jdbcType=BOOLEAN},
      #{retainageAmount,jdbcType=DECIMAL},
      #{retainageAmountMonth,jdbcType=INTEGER},
      #{paymentComments,jdbcType=VARCHAR},
      #{additionalClause,jdbcType=VARCHAR},
      #{logisticsComments,jdbcType=VARCHAR},
      #{financeComments,jdbcType=VARCHAR},
      #{comments,jdbcType=VARCHAR},
      #{invoiceComments,jdbcType=VARCHAR},
      #{deliveryDirect,jdbcType=BOOLEAN},
      #{supplierClause,jdbcType=VARCHAR},
      #{haveAdvancePurchase,jdbcType=BOOLEAN},
      #{advancePurchaseStatus,jdbcType=BOOLEAN},
      #{advancePurchaseComments,jdbcType=VARCHAR},
      #{advancePurchaseTime,jdbcType=BIGINT},
      #{isUrgent,jdbcType=BOOLEAN},
      #{urgentAmount,jdbcType=DECIMAL},
      #{haveCommunicate,jdbcType=BOOLEAN},
      #{prepareComments,jdbcType=VARCHAR},
      #{marketingPlan,jdbcType=VARCHAR},
      #{statusComments,jdbcType=INTEGER},
      #{syncStatus,jdbcType=BOOLEAN},
      #{logisticsApiSync,jdbcType=BOOLEAN},
      #{logisticsWxsendSync,jdbcType=BOOLEAN},
      #{satisfyInvoiceTime,jdbcType=BIGINT},
      #{satisfyDeliveryTime,jdbcType=BIGINT},
      #{isSalesPerformance,jdbcType=BOOLEAN},
      #{salesPerformanceTime,jdbcType=BIGINT},
      #{salesPerformanceModTime,jdbcType=BIGINT},
      #{isDelayInvoice,jdbcType=BOOLEAN},
      #{invoiceMethod,jdbcType=BOOLEAN},
      #{lockedReason,jdbcType=VARCHAR},
      #{costUserIds,jdbcType=VARCHAR},
      #{ownerUserId,jdbcType=INTEGER},
      #{invoiceEmail,jdbcType=VARCHAR},
      #{paymentMode,jdbcType=BOOLEAN},
      #{payType,jdbcType=BOOLEAN},
      #{isApplyInvoice,jdbcType=BOOLEAN},
      #{applyInvoiceTime,jdbcType=BIGINT},
      #{addTime,jdbcType=BIGINT},
      #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=BIGINT},
      #{updater,jdbcType=INTEGER},
      #{adkSaleorderNo,jdbcType=VARCHAR},
      #{createMobile,jdbcType=VARCHAR},
      #{bdtraderComments,jdbcType=VARCHAR},
      #{closeComments,jdbcType=VARCHAR},
      #{bdMobileTime,jdbcType=BIGINT},
      #{webTakeDeliveryTime,jdbcType=BIGINT},
      #{actionId,jdbcType=INTEGER},
      #{isCoupons,jdbcType=BOOLEAN},
      #{couponmoney,jdbcType=DECIMAL},
      #{originalAmount,jdbcType=DECIMAL},
      #{elSaleordreNo,jdbcType=VARCHAR},
      #{isPrintout,jdbcType=BOOLEAN},
      #{outIsFlag,jdbcType=BOOLEAN},
      #{updateDataTime,jdbcType=TIMESTAMP},
      #{realPayAmount,jdbcType=DECIMAL},
      #{realReturnAmount,jdbcType=DECIMAL},
      #{realTotalAmount,jdbcType=DECIMAL},
      #{sendToPc,jdbcType=BOOLEAN},
      #{retentionMoney,jdbcType=DECIMAL},
      #{retentionMoneyDay,jdbcType=INTEGER},
      #{isSameAddress,jdbcType=BOOLEAN},
      #{invoiceSendNode,jdbcType=BOOLEAN},
      #{isDelete,jdbcType=TINYINT},
      #{nopaybackAmount,jdbcType=DECIMAL},
      #{contractUrl,jdbcType=VARCHAR},
      #{autoAudit,jdbcType=INTEGER},
      #{isRisk,jdbcType=BOOLEAN},
      #{riskComments,jdbcType=VARCHAR},
      #{riskTime,jdbcType=BIGINT},
      #{groupCustomerId,jdbcType=INTEGER},
      #{isContractReturn,jdbcType=TINYINT},
      #{isDeliveryorderReturn,jdbcType=TINYINT},
      #{deliveryClaim,jdbcType=BOOLEAN},
      #{deliveryDelayTime,jdbcType=BIGINT},
      #{billPeriodSettlementType,jdbcType=TINYINT},
      #{isNew,jdbcType=TINYINT},
      #{confirmStatus,jdbcType=INTEGER},
      #{confirmTime,jdbcType=TIMESTAMP},
      #{contractNoStampUrl,jdbcType=VARCHAR},
      #{onlineReceiptStatus,jdbcType=BOOLEAN},
      #{confirmationFormUpload,jdbcType=BOOLEAN},
      #{confirmationFormAudit,jdbcType=BOOLEAN},
      #{confirmationSubmitTime,jdbcType=BIGINT},
      #{prepareReaseonType,jdbcType=INTEGER},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="saleorderId != null">
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      </if>
      QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      PARENT_ID = #{parentId,jdbcType=INTEGER},
      SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      M_SALEORDER_NO = #{mSaleorderNo,jdbcType=VARCHAR},
      ORDER_TYPE = #{orderType,jdbcType=BOOLEAN},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      `SOURCE` = #{source,jdbcType=BOOLEAN},
      CREATOR_ORG_ID = #{creatorOrgId,jdbcType=INTEGER},
      CREATOR_ORG_NAME = #{creatorOrgName,jdbcType=VARCHAR},
      ORG_ID = #{orgId,jdbcType=INTEGER},
      ORG_NAME = #{orgName,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=INTEGER},
      VALID_ORG_ID = #{validOrgId,jdbcType=INTEGER},
      VALID_ORG_NAME = #{validOrgName,jdbcType=VARCHAR},
      VALID_USER_ID = #{validUserId,jdbcType=INTEGER},
      VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
      VALID_TIME = #{validTime,jdbcType=BIGINT},
      END_TIME = #{endTime,jdbcType=BIGINT},
      `STATUS` = #{status,jdbcType=BOOLEAN},
      PURCHASE_STATUS = #{purchaseStatus,jdbcType=BOOLEAN},
      LOCKED_STATUS = #{lockedStatus,jdbcType=BOOLEAN},
      INVOICE_STATUS = #{invoiceStatus,jdbcType=BOOLEAN},
      INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
      PAYMENT_STATUS = #{paymentStatus,jdbcType=INTEGER},
      PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
      DELIVERY_STATUS = #{deliveryStatus,jdbcType=BOOLEAN},
      DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      IS_CUSTOMER_ARRIVAL = #{isCustomerArrival,jdbcType=BOOLEAN},
      ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      SERVICE_STATUS = #{serviceStatus,jdbcType=BOOLEAN},
      HAVE_ACCOUNT_PERIOD = #{haveAccountPeriod,jdbcType=BOOLEAN},
      IS_PAYMENT = #{isPayment,jdbcType=BOOLEAN},
      TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
      CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      TRADER_AREA_ID = #{traderAreaId,jdbcType=INTEGER},
      TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
      TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
      TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
      TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
      TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
      TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
      TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
      TAKE_TRADER_AREA_ID = #{takeTraderAreaId,jdbcType=INTEGER},
      TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
      TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BOOLEAN},
      INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
      INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
      INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
      INVOICE_TRADER_AREA_ID = #{invoiceTraderAreaId,jdbcType=INTEGER},
      INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
      INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
      SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER},
      SALES_AREA = #{salesArea,jdbcType=VARCHAR},
      TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER},
      TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER},
      INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
      DELIVERY_TYPE = #{deliveryType,jdbcType=INTEGER},
      DELIVERY_METHOD = #{deliveryMethod,jdbcType=INTEGER},
      LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
      ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      LOGISTICS_COLLECTION = #{logisticsCollection,jdbcType=BOOLEAN},
      RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
      RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
      PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
      ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
      LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      FINANCE_COMMENTS = #{financeComments,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BOOLEAN},
      SUPPLIER_CLAUSE = #{supplierClause,jdbcType=VARCHAR},
      HAVE_ADVANCE_PURCHASE = #{haveAdvancePurchase,jdbcType=BOOLEAN},
      ADVANCE_PURCHASE_STATUS = #{advancePurchaseStatus,jdbcType=BOOLEAN},
      ADVANCE_PURCHASE_COMMENTS = #{advancePurchaseComments,jdbcType=VARCHAR},
      ADVANCE_PURCHASE_TIME = #{advancePurchaseTime,jdbcType=BIGINT},
      IS_URGENT = #{isUrgent,jdbcType=BOOLEAN},
      URGENT_AMOUNT = #{urgentAmount,jdbcType=DECIMAL},
      HAVE_COMMUNICATE = #{haveCommunicate,jdbcType=BOOLEAN},
      PREPARE_COMMENTS = #{prepareComments,jdbcType=VARCHAR},
      MARKETING_PLAN = #{marketingPlan,jdbcType=VARCHAR},
      STATUS_COMMENTS = #{statusComments,jdbcType=INTEGER},
      SYNC_STATUS = #{syncStatus,jdbcType=BOOLEAN},
      LOGISTICS_API_SYNC = #{logisticsApiSync,jdbcType=BOOLEAN},
      LOGISTICS_WXSEND_SYNC = #{logisticsWxsendSync,jdbcType=BOOLEAN},
      SATISFY_INVOICE_TIME = #{satisfyInvoiceTime,jdbcType=BIGINT},
      SATISFY_DELIVERY_TIME = #{satisfyDeliveryTime,jdbcType=BIGINT},
      IS_SALES_PERFORMANCE = #{isSalesPerformance,jdbcType=BOOLEAN},
      SALES_PERFORMANCE_TIME = #{salesPerformanceTime,jdbcType=BIGINT},
      SALES_PERFORMANCE_MOD_TIME = #{salesPerformanceModTime,jdbcType=BIGINT},
      IS_DELAY_INVOICE = #{isDelayInvoice,jdbcType=BOOLEAN},
      INVOICE_METHOD = #{invoiceMethod,jdbcType=BOOLEAN},
      LOCKED_REASON = #{lockedReason,jdbcType=VARCHAR},
      COST_USER_IDS = #{costUserIds,jdbcType=VARCHAR},
      OWNER_USER_ID = #{ownerUserId,jdbcType=INTEGER},
      INVOICE_EMAIL = #{invoiceEmail,jdbcType=VARCHAR},
      PAYMENT_MODE = #{paymentMode,jdbcType=BOOLEAN},
      PAY_TYPE = #{payType,jdbcType=BOOLEAN},
      IS_APPLY_INVOICE = #{isApplyInvoice,jdbcType=BOOLEAN},
      APPLY_INVOICE_TIME = #{applyInvoiceTime,jdbcType=BIGINT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      ADK_SALEORDER_NO = #{adkSaleorderNo,jdbcType=VARCHAR},
      CREATE_MOBILE = #{createMobile,jdbcType=VARCHAR},
      BDTRADER_COMMENTS = #{bdtraderComments,jdbcType=VARCHAR},
      CLOSE_COMMENTS = #{closeComments,jdbcType=VARCHAR},
      BD_MOBILE_TIME = #{bdMobileTime,jdbcType=BIGINT},
      WEB_TAKE_DELIVERY_TIME = #{webTakeDeliveryTime,jdbcType=BIGINT},
      ACTION_ID = #{actionId,jdbcType=INTEGER},
      IS_COUPONS = #{isCoupons,jdbcType=BOOLEAN},
      COUPONMONEY = #{couponmoney,jdbcType=DECIMAL},
      ORIGINAL_AMOUNT = #{originalAmount,jdbcType=DECIMAL},
      EL_SALEORDRE_NO = #{elSaleordreNo,jdbcType=VARCHAR},
      IS_PRINTOUT = #{isPrintout,jdbcType=BOOLEAN},
      OUT_IS_FLAG = #{outIsFlag,jdbcType=BOOLEAN},
      UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      REAL_PAY_AMOUNT = #{realPayAmount,jdbcType=DECIMAL},
      REAL_RETURN_AMOUNT = #{realReturnAmount,jdbcType=DECIMAL},
      REAL_TOTAL_AMOUNT = #{realTotalAmount,jdbcType=DECIMAL},
      SEND_TO_PC = #{sendToPc,jdbcType=BOOLEAN},
      RETENTION_MONEY = #{retentionMoney,jdbcType=DECIMAL},
      RETENTION_MONEY_DAY = #{retentionMoneyDay,jdbcType=INTEGER},
      IS_SAME_ADDRESS = #{isSameAddress,jdbcType=BOOLEAN},
      INVOICE_SEND_NODE = #{invoiceSendNode,jdbcType=BOOLEAN},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      NOPAYBACK_AMOUNT = #{nopaybackAmount,jdbcType=DECIMAL},
      CONTRACT_URL = #{contractUrl,jdbcType=VARCHAR},
      AUTO_AUDIT = #{autoAudit,jdbcType=INTEGER},
      IS_RISK = #{isRisk,jdbcType=BOOLEAN},
      RISK_COMMENTS = #{riskComments,jdbcType=VARCHAR},
      RISK_TIME = #{riskTime,jdbcType=BIGINT},
      GROUP_CUSTOMER_ID = #{groupCustomerId,jdbcType=INTEGER},
      IS_CONTRACT_RETURN = #{isContractReturn,jdbcType=TINYINT},
      IS_DELIVERYORDER_RETURN = #{isDeliveryorderReturn,jdbcType=TINYINT},
      DELIVERY_CLAIM = #{deliveryClaim,jdbcType=BOOLEAN},
      DELIVERY_DELAY_TIME = #{deliveryDelayTime,jdbcType=BIGINT},
      BILL_PERIOD_SETTLEMENT_TYPE = #{billPeriodSettlementType,jdbcType=TINYINT},
      IS_NEW = #{isNew,jdbcType=TINYINT},
      CONFIRM_STATUS = #{confirmStatus,jdbcType=INTEGER},
      CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
      CONTRACT_NO_STAMP_URL = #{contractNoStampUrl,jdbcType=VARCHAR},
      ONLINE_RECEIPT_STATUS = #{onlineReceiptStatus,jdbcType=BOOLEAN},
      CONFIRMATION_FORM_UPLOAD = #{confirmationFormUpload,jdbcType=BOOLEAN},
      CONFIRMATION_FORM_AUDIT = #{confirmationFormAudit,jdbcType=BOOLEAN},
      CONFIRMATION_SUBMIT_TIME = #{confirmationSubmitTime,jdbcType=BIGINT},
      PREPARE_REASEON_TYPE = #{prepareReaseonType,jdbcType=INTEGER},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="SALEORDER_ID" keyProperty="saleorderId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSaleorderDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SALEORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        SALEORDER_ID,
      </if>
      <if test="quoteorderId != null">
        QUOTEORDER_ID,
      </if>
      <if test="parentId != null">
        PARENT_ID,
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO,
      </if>
      <if test="mSaleorderNo != null">
        M_SALEORDER_NO,
      </if>
      <if test="orderType != null">
        ORDER_TYPE,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="creatorOrgId != null">
        CREATOR_ORG_ID,
      </if>
      <if test="creatorOrgName != null">
        CREATOR_ORG_NAME,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="orgName != null">
        ORG_NAME,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="validOrgId != null">
        VALID_ORG_ID,
      </if>
      <if test="validOrgName != null">
        VALID_ORG_NAME,
      </if>
      <if test="validUserId != null">
        VALID_USER_ID,
      </if>
      <if test="validStatus != null">
        VALID_STATUS,
      </if>
      <if test="validTime != null">
        VALID_TIME,
      </if>
      <if test="endTime != null">
        END_TIME,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="purchaseStatus != null">
        PURCHASE_STATUS,
      </if>
      <if test="lockedStatus != null">
        LOCKED_STATUS,
      </if>
      <if test="invoiceStatus != null">
        INVOICE_STATUS,
      </if>
      <if test="invoiceTime != null">
        INVOICE_TIME,
      </if>
      <if test="paymentStatus != null">
        PAYMENT_STATUS,
      </if>
      <if test="paymentTime != null">
        PAYMENT_TIME,
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS,
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME,
      </if>
      <if test="isCustomerArrival != null">
        IS_CUSTOMER_ARRIVAL,
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS,
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME,
      </if>
      <if test="serviceStatus != null">
        SERVICE_STATUS,
      </if>
      <if test="haveAccountPeriod != null">
        HAVE_ACCOUNT_PERIOD,
      </if>
      <if test="isPayment != null">
        IS_PAYMENT,
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="customerType != null">
        CUSTOMER_TYPE,
      </if>
      <if test="customerNature != null">
        CUSTOMER_NATURE,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME,
      </if>
      <if test="traderContactMobile != null">
        TRADER_CONTACT_MOBILE,
      </if>
      <if test="traderContactTelephone != null">
        TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID,
      </if>
      <if test="traderAreaId != null">
        TRADER_AREA_ID,
      </if>
      <if test="traderArea != null">
        TRADER_AREA,
      </if>
      <if test="traderAddress != null">
        TRADER_ADDRESS,
      </if>
      <if test="traderComments != null">
        TRADER_COMMENTS,
      </if>
      <if test="takeTraderId != null">
        TAKE_TRADER_ID,
      </if>
      <if test="takeTraderName != null">
        TAKE_TRADER_NAME,
      </if>
      <if test="takeTraderContactId != null">
        TAKE_TRADER_CONTACT_ID,
      </if>
      <if test="takeTraderContactName != null">
        TAKE_TRADER_CONTACT_NAME,
      </if>
      <if test="takeTraderContactMobile != null">
        TAKE_TRADER_CONTACT_MOBILE,
      </if>
      <if test="takeTraderContactTelephone != null">
        TAKE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="takeTraderAddressId != null">
        TAKE_TRADER_ADDRESS_ID,
      </if>
      <if test="takeTraderAreaId != null">
        TAKE_TRADER_AREA_ID,
      </if>
      <if test="takeTraderArea != null">
        TAKE_TRADER_AREA,
      </if>
      <if test="takeTraderAddress != null">
        TAKE_TRADER_ADDRESS,
      </if>
      <if test="isSendInvoice != null">
        IS_SEND_INVOICE,
      </if>
      <if test="invoiceTraderId != null">
        INVOICE_TRADER_ID,
      </if>
      <if test="invoiceTraderName != null">
        INVOICE_TRADER_NAME,
      </if>
      <if test="invoiceTraderContactId != null">
        INVOICE_TRADER_CONTACT_ID,
      </if>
      <if test="invoiceTraderContactName != null">
        INVOICE_TRADER_CONTACT_NAME,
      </if>
      <if test="invoiceTraderContactMobile != null">
        INVOICE_TRADER_CONTACT_MOBILE,
      </if>
      <if test="invoiceTraderContactTelephone != null">
        INVOICE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="invoiceTraderAddressId != null">
        INVOICE_TRADER_ADDRESS_ID,
      </if>
      <if test="invoiceTraderAreaId != null">
        INVOICE_TRADER_AREA_ID,
      </if>
      <if test="invoiceTraderArea != null">
        INVOICE_TRADER_AREA,
      </if>
      <if test="invoiceTraderAddress != null">
        INVOICE_TRADER_ADDRESS,
      </if>
      <if test="salesAreaId != null">
        SALES_AREA_ID,
      </if>
      <if test="salesArea != null">
        SALES_AREA,
      </if>
      <if test="terminalTraderId != null">
        TERMINAL_TRADER_ID,
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME,
      </if>
      <if test="terminalTraderType != null">
        TERMINAL_TRADER_TYPE,
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE,
      </if>
      <if test="freightDescription != null">
        FREIGHT_DESCRIPTION,
      </if>
      <if test="deliveryType != null">
        DELIVERY_TYPE,
      </if>
      <if test="deliveryMethod != null">
        DELIVERY_METHOD,
      </if>
      <if test="logisticsId != null">
        LOGISTICS_ID,
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE,
      </if>
      <if test="prepaidAmount != null">
        PREPAID_AMOUNT,
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT,
      </if>
      <if test="periodDay != null">
        PERIOD_DAY,
      </if>
      <if test="logisticsCollection != null">
        LOGISTICS_COLLECTION,
      </if>
      <if test="retainageAmount != null">
        RETAINAGE_AMOUNT,
      </if>
      <if test="retainageAmountMonth != null">
        RETAINAGE_AMOUNT_MONTH,
      </if>
      <if test="paymentComments != null">
        PAYMENT_COMMENTS,
      </if>
      <if test="additionalClause != null">
        ADDITIONAL_CLAUSE,
      </if>
      <if test="logisticsComments != null">
        LOGISTICS_COMMENTS,
      </if>
      <if test="financeComments != null">
        FINANCE_COMMENTS,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="invoiceComments != null">
        INVOICE_COMMENTS,
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT,
      </if>
      <if test="supplierClause != null">
        SUPPLIER_CLAUSE,
      </if>
      <if test="haveAdvancePurchase != null">
        HAVE_ADVANCE_PURCHASE,
      </if>
      <if test="advancePurchaseStatus != null">
        ADVANCE_PURCHASE_STATUS,
      </if>
      <if test="advancePurchaseComments != null">
        ADVANCE_PURCHASE_COMMENTS,
      </if>
      <if test="advancePurchaseTime != null">
        ADVANCE_PURCHASE_TIME,
      </if>
      <if test="isUrgent != null">
        IS_URGENT,
      </if>
      <if test="urgentAmount != null">
        URGENT_AMOUNT,
      </if>
      <if test="haveCommunicate != null">
        HAVE_COMMUNICATE,
      </if>
      <if test="prepareComments != null">
        PREPARE_COMMENTS,
      </if>
      <if test="marketingPlan != null">
        MARKETING_PLAN,
      </if>
      <if test="statusComments != null">
        STATUS_COMMENTS,
      </if>
      <if test="syncStatus != null">
        SYNC_STATUS,
      </if>
      <if test="logisticsApiSync != null">
        LOGISTICS_API_SYNC,
      </if>
      <if test="logisticsWxsendSync != null">
        LOGISTICS_WXSEND_SYNC,
      </if>
      <if test="satisfyInvoiceTime != null">
        SATISFY_INVOICE_TIME,
      </if>
      <if test="satisfyDeliveryTime != null">
        SATISFY_DELIVERY_TIME,
      </if>
      <if test="isSalesPerformance != null">
        IS_SALES_PERFORMANCE,
      </if>
      <if test="salesPerformanceTime != null">
        SALES_PERFORMANCE_TIME,
      </if>
      <if test="salesPerformanceModTime != null">
        SALES_PERFORMANCE_MOD_TIME,
      </if>
      <if test="isDelayInvoice != null">
        IS_DELAY_INVOICE,
      </if>
      <if test="invoiceMethod != null">
        INVOICE_METHOD,
      </if>
      <if test="lockedReason != null">
        LOCKED_REASON,
      </if>
      <if test="costUserIds != null">
        COST_USER_IDS,
      </if>
      <if test="ownerUserId != null">
        OWNER_USER_ID,
      </if>
      <if test="invoiceEmail != null">
        INVOICE_EMAIL,
      </if>
      <if test="paymentMode != null">
        PAYMENT_MODE,
      </if>
      <if test="payType != null">
        PAY_TYPE,
      </if>
      <if test="isApplyInvoice != null">
        IS_APPLY_INVOICE,
      </if>
      <if test="applyInvoiceTime != null">
        APPLY_INVOICE_TIME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="adkSaleorderNo != null">
        ADK_SALEORDER_NO,
      </if>
      <if test="createMobile != null">
        CREATE_MOBILE,
      </if>
      <if test="bdtraderComments != null">
        BDTRADER_COMMENTS,
      </if>
      <if test="closeComments != null">
        CLOSE_COMMENTS,
      </if>
      <if test="bdMobileTime != null">
        BD_MOBILE_TIME,
      </if>
      <if test="webTakeDeliveryTime != null">
        WEB_TAKE_DELIVERY_TIME,
      </if>
      <if test="actionId != null">
        ACTION_ID,
      </if>
      <if test="isCoupons != null">
        IS_COUPONS,
      </if>
      <if test="couponmoney != null">
        COUPONMONEY,
      </if>
      <if test="originalAmount != null">
        ORIGINAL_AMOUNT,
      </if>
      <if test="elSaleordreNo != null">
        EL_SALEORDRE_NO,
      </if>
      <if test="isPrintout != null">
        IS_PRINTOUT,
      </if>
      <if test="outIsFlag != null">
        OUT_IS_FLAG,
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME,
      </if>
      <if test="realPayAmount != null">
        REAL_PAY_AMOUNT,
      </if>
      <if test="realReturnAmount != null">
        REAL_RETURN_AMOUNT,
      </if>
      <if test="realTotalAmount != null">
        REAL_TOTAL_AMOUNT,
      </if>
      <if test="sendToPc != null">
        SEND_TO_PC,
      </if>
      <if test="retentionMoney != null">
        RETENTION_MONEY,
      </if>
      <if test="retentionMoneyDay != null">
        RETENTION_MONEY_DAY,
      </if>
      <if test="isSameAddress != null">
        IS_SAME_ADDRESS,
      </if>
      <if test="invoiceSendNode != null">
        INVOICE_SEND_NODE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="nopaybackAmount != null">
        NOPAYBACK_AMOUNT,
      </if>
      <if test="contractUrl != null">
        CONTRACT_URL,
      </if>
      <if test="autoAudit != null">
        AUTO_AUDIT,
      </if>
      <if test="isRisk != null">
        IS_RISK,
      </if>
      <if test="riskComments != null">
        RISK_COMMENTS,
      </if>
      <if test="riskTime != null">
        RISK_TIME,
      </if>
      <if test="groupCustomerId != null">
        GROUP_CUSTOMER_ID,
      </if>
      <if test="isContractReturn != null">
        IS_CONTRACT_RETURN,
      </if>
      <if test="isDeliveryorderReturn != null">
        IS_DELIVERYORDER_RETURN,
      </if>
      <if test="deliveryClaim != null">
        DELIVERY_CLAIM,
      </if>
      <if test="deliveryDelayTime != null">
        DELIVERY_DELAY_TIME,
      </if>
      <if test="billPeriodSettlementType != null">
        BILL_PERIOD_SETTLEMENT_TYPE,
      </if>
      <if test="isNew != null">
        IS_NEW,
      </if>
      <if test="confirmStatus != null">
        CONFIRM_STATUS,
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME,
      </if>
      <if test="contractNoStampUrl != null">
        CONTRACT_NO_STAMP_URL,
      </if>
      <if test="onlineReceiptStatus != null">
        ONLINE_RECEIPT_STATUS,
      </if>
      <if test="confirmationFormUpload != null">
        CONFIRMATION_FORM_UPLOAD,
      </if>
      <if test="confirmationFormAudit != null">
        CONFIRMATION_FORM_AUDIT,
      </if>
      <if test="confirmationSubmitTime != null">
        CONFIRMATION_SUBMIT_TIME,
      </if>
      <if test="prepareReaseonType != null">
        PREPARE_REASEON_TYPE,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleorderId != null">
        #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="quoteorderId != null">
        #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="saleorderNo != null">
        #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="mSaleorderNo != null">
        #{mSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=BOOLEAN},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=BOOLEAN},
      </if>
      <if test="creatorOrgId != null">
        #{creatorOrgId,jdbcType=INTEGER},
      </if>
      <if test="creatorOrgName != null">
        #{creatorOrgName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="validOrgId != null">
        #{validOrgId,jdbcType=INTEGER},
      </if>
      <if test="validOrgName != null">
        #{validOrgName,jdbcType=VARCHAR},
      </if>
      <if test="validUserId != null">
        #{validUserId,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        #{validStatus,jdbcType=BOOLEAN},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=BOOLEAN},
      </if>
      <if test="purchaseStatus != null">
        #{purchaseStatus,jdbcType=BOOLEAN},
      </if>
      <if test="lockedStatus != null">
        #{lockedStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceStatus != null">
        #{invoiceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceTime != null">
        #{invoiceTime,jdbcType=BIGINT},
      </if>
      <if test="paymentStatus != null">
        #{paymentStatus,jdbcType=INTEGER},
      </if>
      <if test="paymentTime != null">
        #{paymentTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="isCustomerArrival != null">
        #{isCustomerArrival,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalStatus != null">
        #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="serviceStatus != null">
        #{serviceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="haveAccountPeriod != null">
        #{haveAccountPeriod,jdbcType=BOOLEAN},
      </if>
      <if test="isPayment != null">
        #{isPayment,jdbcType=BOOLEAN},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="customerType != null">
        #{customerType,jdbcType=INTEGER},
      </if>
      <if test="customerNature != null">
        #{customerNature,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null">
        #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null">
        #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null">
        #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="traderAreaId != null">
        #{traderAreaId,jdbcType=INTEGER},
      </if>
      <if test="traderArea != null">
        #{traderArea,jdbcType=VARCHAR},
      </if>
      <if test="traderAddress != null">
        #{traderAddress,jdbcType=VARCHAR},
      </if>
      <if test="traderComments != null">
        #{traderComments,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderId != null">
        #{takeTraderId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderName != null">
        #{takeTraderName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactId != null">
        #{takeTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderContactName != null">
        #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactMobile != null">
        #{takeTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null">
        #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddressId != null">
        #{takeTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderAreaId != null">
        #{takeTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderArea != null">
        #{takeTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddress != null">
        #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="isSendInvoice != null">
        #{isSendInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceTraderId != null">
        #{invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderName != null">
        #{invoiceTraderName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactId != null">
        #{invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderContactName != null">
        #{invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactMobile != null">
        #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactTelephone != null">
        #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddressId != null">
        #{invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderAreaId != null">
        #{invoiceTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderArea != null">
        #{invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddress != null">
        #{invoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="salesAreaId != null">
        #{salesAreaId,jdbcType=INTEGER},
      </if>
      <if test="salesArea != null">
        #{salesArea,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null">
        #{terminalTraderId,jdbcType=INTEGER},
      </if>
      <if test="terminalTraderName != null">
        #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderType != null">
        #{terminalTraderType,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="freightDescription != null">
        #{freightDescription,jdbcType=INTEGER},
      </if>
      <if test="deliveryType != null">
        #{deliveryType,jdbcType=INTEGER},
      </if>
      <if test="deliveryMethod != null">
        #{deliveryMethod,jdbcType=INTEGER},
      </if>
      <if test="logisticsId != null">
        #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="prepaidAmount != null">
        #{prepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodAmount != null">
        #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null">
        #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="logisticsCollection != null">
        #{logisticsCollection,jdbcType=BOOLEAN},
      </if>
      <if test="retainageAmount != null">
        #{retainageAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmountMonth != null">
        #{retainageAmountMonth,jdbcType=INTEGER},
      </if>
      <if test="paymentComments != null">
        #{paymentComments,jdbcType=VARCHAR},
      </if>
      <if test="additionalClause != null">
        #{additionalClause,jdbcType=VARCHAR},
      </if>
      <if test="logisticsComments != null">
        #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="financeComments != null">
        #{financeComments,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="invoiceComments != null">
        #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null">
        #{deliveryDirect,jdbcType=BOOLEAN},
      </if>
      <if test="supplierClause != null">
        #{supplierClause,jdbcType=VARCHAR},
      </if>
      <if test="haveAdvancePurchase != null">
        #{haveAdvancePurchase,jdbcType=BOOLEAN},
      </if>
      <if test="advancePurchaseStatus != null">
        #{advancePurchaseStatus,jdbcType=BOOLEAN},
      </if>
      <if test="advancePurchaseComments != null">
        #{advancePurchaseComments,jdbcType=VARCHAR},
      </if>
      <if test="advancePurchaseTime != null">
        #{advancePurchaseTime,jdbcType=BIGINT},
      </if>
      <if test="isUrgent != null">
        #{isUrgent,jdbcType=BOOLEAN},
      </if>
      <if test="urgentAmount != null">
        #{urgentAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveCommunicate != null">
        #{haveCommunicate,jdbcType=BOOLEAN},
      </if>
      <if test="prepareComments != null">
        #{prepareComments,jdbcType=VARCHAR},
      </if>
      <if test="marketingPlan != null">
        #{marketingPlan,jdbcType=VARCHAR},
      </if>
      <if test="statusComments != null">
        #{statusComments,jdbcType=INTEGER},
      </if>
      <if test="syncStatus != null">
        #{syncStatus,jdbcType=BOOLEAN},
      </if>
      <if test="logisticsApiSync != null">
        #{logisticsApiSync,jdbcType=BOOLEAN},
      </if>
      <if test="logisticsWxsendSync != null">
        #{logisticsWxsendSync,jdbcType=BOOLEAN},
      </if>
      <if test="satisfyInvoiceTime != null">
        #{satisfyInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="satisfyDeliveryTime != null">
        #{satisfyDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="isSalesPerformance != null">
        #{isSalesPerformance,jdbcType=BOOLEAN},
      </if>
      <if test="salesPerformanceTime != null">
        #{salesPerformanceTime,jdbcType=BIGINT},
      </if>
      <if test="salesPerformanceModTime != null">
        #{salesPerformanceModTime,jdbcType=BIGINT},
      </if>
      <if test="isDelayInvoice != null">
        #{isDelayInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceMethod != null">
        #{invoiceMethod,jdbcType=BOOLEAN},
      </if>
      <if test="lockedReason != null">
        #{lockedReason,jdbcType=VARCHAR},
      </if>
      <if test="costUserIds != null">
        #{costUserIds,jdbcType=VARCHAR},
      </if>
      <if test="ownerUserId != null">
        #{ownerUserId,jdbcType=INTEGER},
      </if>
      <if test="invoiceEmail != null">
        #{invoiceEmail,jdbcType=VARCHAR},
      </if>
      <if test="paymentMode != null">
        #{paymentMode,jdbcType=BOOLEAN},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=BOOLEAN},
      </if>
      <if test="isApplyInvoice != null">
        #{isApplyInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="applyInvoiceTime != null">
        #{applyInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="adkSaleorderNo != null">
        #{adkSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null">
        #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="bdtraderComments != null">
        #{bdtraderComments,jdbcType=VARCHAR},
      </if>
      <if test="closeComments != null">
        #{closeComments,jdbcType=VARCHAR},
      </if>
      <if test="bdMobileTime != null">
        #{bdMobileTime,jdbcType=BIGINT},
      </if>
      <if test="webTakeDeliveryTime != null">
        #{webTakeDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="actionId != null">
        #{actionId,jdbcType=INTEGER},
      </if>
      <if test="isCoupons != null">
        #{isCoupons,jdbcType=BOOLEAN},
      </if>
      <if test="couponmoney != null">
        #{couponmoney,jdbcType=DECIMAL},
      </if>
      <if test="originalAmount != null">
        #{originalAmount,jdbcType=DECIMAL},
      </if>
      <if test="elSaleordreNo != null">
        #{elSaleordreNo,jdbcType=VARCHAR},
      </if>
      <if test="isPrintout != null">
        #{isPrintout,jdbcType=BOOLEAN},
      </if>
      <if test="outIsFlag != null">
        #{outIsFlag,jdbcType=BOOLEAN},
      </if>
      <if test="updateDataTime != null">
        #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realPayAmount != null">
        #{realPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="realReturnAmount != null">
        #{realReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="realTotalAmount != null">
        #{realTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="sendToPc != null">
        #{sendToPc,jdbcType=BOOLEAN},
      </if>
      <if test="retentionMoney != null">
        #{retentionMoney,jdbcType=DECIMAL},
      </if>
      <if test="retentionMoneyDay != null">
        #{retentionMoneyDay,jdbcType=INTEGER},
      </if>
      <if test="isSameAddress != null">
        #{isSameAddress,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceSendNode != null">
        #{invoiceSendNode,jdbcType=BOOLEAN},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="nopaybackAmount != null">
        #{nopaybackAmount,jdbcType=DECIMAL},
      </if>
      <if test="contractUrl != null">
        #{contractUrl,jdbcType=VARCHAR},
      </if>
      <if test="autoAudit != null">
        #{autoAudit,jdbcType=INTEGER},
      </if>
      <if test="isRisk != null">
        #{isRisk,jdbcType=BOOLEAN},
      </if>
      <if test="riskComments != null">
        #{riskComments,jdbcType=VARCHAR},
      </if>
      <if test="riskTime != null">
        #{riskTime,jdbcType=BIGINT},
      </if>
      <if test="groupCustomerId != null">
        #{groupCustomerId,jdbcType=INTEGER},
      </if>
      <if test="isContractReturn != null">
        #{isContractReturn,jdbcType=TINYINT},
      </if>
      <if test="isDeliveryorderReturn != null">
        #{isDeliveryorderReturn,jdbcType=TINYINT},
      </if>
      <if test="deliveryClaim != null">
        #{deliveryClaim,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryDelayTime != null">
        #{deliveryDelayTime,jdbcType=BIGINT},
      </if>
      <if test="billPeriodSettlementType != null">
        #{billPeriodSettlementType,jdbcType=TINYINT},
      </if>
      <if test="isNew != null">
        #{isNew,jdbcType=TINYINT},
      </if>
      <if test="confirmStatus != null">
        #{confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractNoStampUrl != null">
        #{contractNoStampUrl,jdbcType=VARCHAR},
      </if>
      <if test="onlineReceiptStatus != null">
        #{onlineReceiptStatus,jdbcType=BOOLEAN},
      </if>
      <if test="confirmationFormUpload != null">
        #{confirmationFormUpload,jdbcType=BOOLEAN},
      </if>
      <if test="confirmationFormAudit != null">
        #{confirmationFormAudit,jdbcType=BOOLEAN},
      </if>
      <if test="confirmationSubmitTime != null">
        #{confirmationSubmitTime,jdbcType=BIGINT},
      </if>
      <if test="prepareReaseonType != null">
        #{prepareReaseonType,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="saleorderId != null">
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="quoteorderId != null">
        QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        PARENT_ID = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="saleorderNo != null">
        SALEORDER_NO = #{saleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="mSaleorderNo != null">
        M_SALEORDER_NO = #{mSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        ORDER_TYPE = #{orderType,jdbcType=BOOLEAN},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=BOOLEAN},
      </if>
      <if test="creatorOrgId != null">
        CREATOR_ORG_ID = #{creatorOrgId,jdbcType=INTEGER},
      </if>
      <if test="creatorOrgName != null">
        CREATOR_ORG_NAME = #{creatorOrgName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=INTEGER},
      </if>
      <if test="orgName != null">
        ORG_NAME = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="validOrgId != null">
        VALID_ORG_ID = #{validOrgId,jdbcType=INTEGER},
      </if>
      <if test="validOrgName != null">
        VALID_ORG_NAME = #{validOrgName,jdbcType=VARCHAR},
      </if>
      <if test="validUserId != null">
        VALID_USER_ID = #{validUserId,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        VALID_STATUS = #{validStatus,jdbcType=BOOLEAN},
      </if>
      <if test="validTime != null">
        VALID_TIME = #{validTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        END_TIME = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=BOOLEAN},
      </if>
      <if test="purchaseStatus != null">
        PURCHASE_STATUS = #{purchaseStatus,jdbcType=BOOLEAN},
      </if>
      <if test="lockedStatus != null">
        LOCKED_STATUS = #{lockedStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceStatus != null">
        INVOICE_STATUS = #{invoiceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceTime != null">
        INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
      </if>
      <if test="paymentStatus != null">
        PAYMENT_STATUS = #{paymentStatus,jdbcType=INTEGER},
      </if>
      <if test="paymentTime != null">
        PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS = #{deliveryStatus,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryTime != null">
        DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
      </if>
      <if test="isCustomerArrival != null">
        IS_CUSTOMER_ARRIVAL = #{isCustomerArrival,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalStatus != null">
        ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BOOLEAN},
      </if>
      <if test="arrivalTime != null">
        ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
      </if>
      <if test="serviceStatus != null">
        SERVICE_STATUS = #{serviceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="haveAccountPeriod != null">
        HAVE_ACCOUNT_PERIOD = #{haveAccountPeriod,jdbcType=BOOLEAN},
      </if>
      <if test="isPayment != null">
        IS_PAYMENT = #{isPayment,jdbcType=BOOLEAN},
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="customerType != null">
        CUSTOMER_TYPE = #{customerType,jdbcType=INTEGER},
      </if>
      <if test="customerNature != null">
        CUSTOMER_NATURE = #{customerNature,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null">
        TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null">
        TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="traderAreaId != null">
        TRADER_AREA_ID = #{traderAreaId,jdbcType=INTEGER},
      </if>
      <if test="traderArea != null">
        TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
      </if>
      <if test="traderAddress != null">
        TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
      </if>
      <if test="traderComments != null">
        TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderId != null">
        TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderName != null">
        TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactId != null">
        TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderContactName != null">
        TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactMobile != null">
        TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderContactTelephone != null">
        TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddressId != null">
        TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderAreaId != null">
        TAKE_TRADER_AREA_ID = #{takeTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="takeTraderArea != null">
        TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="takeTraderAddress != null">
        TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="isSendInvoice != null">
        IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceTraderId != null">
        INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderName != null">
        INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactId != null">
        INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderContactName != null">
        INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactMobile != null">
        INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactTelephone != null">
        INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddressId != null">
        INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderAreaId != null">
        INVOICE_TRADER_AREA_ID = #{invoiceTraderAreaId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderArea != null">
        INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddress != null">
        INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="salesAreaId != null">
        SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER},
      </if>
      <if test="salesArea != null">
        SALES_AREA = #{salesArea,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderId != null">
        TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER},
      </if>
      <if test="terminalTraderName != null">
        TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR},
      </if>
      <if test="terminalTraderType != null">
        TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER},
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="freightDescription != null">
        FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
      </if>
      <if test="deliveryType != null">
        DELIVERY_TYPE = #{deliveryType,jdbcType=INTEGER},
      </if>
      <if test="deliveryMethod != null">
        DELIVERY_METHOD = #{deliveryMethod,jdbcType=INTEGER},
      </if>
      <if test="logisticsId != null">
        LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="paymentType != null">
        PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="prepaidAmount != null">
        PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="periodDay != null">
        PERIOD_DAY = #{periodDay,jdbcType=INTEGER},
      </if>
      <if test="logisticsCollection != null">
        LOGISTICS_COLLECTION = #{logisticsCollection,jdbcType=BOOLEAN},
      </if>
      <if test="retainageAmount != null">
        RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
      </if>
      <if test="retainageAmountMonth != null">
        RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
      </if>
      <if test="paymentComments != null">
        PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
      </if>
      <if test="additionalClause != null">
        ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
      </if>
      <if test="logisticsComments != null">
        LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
      </if>
      <if test="financeComments != null">
        FINANCE_COMMENTS = #{financeComments,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="invoiceComments != null">
        INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BOOLEAN},
      </if>
      <if test="supplierClause != null">
        SUPPLIER_CLAUSE = #{supplierClause,jdbcType=VARCHAR},
      </if>
      <if test="haveAdvancePurchase != null">
        HAVE_ADVANCE_PURCHASE = #{haveAdvancePurchase,jdbcType=BOOLEAN},
      </if>
      <if test="advancePurchaseStatus != null">
        ADVANCE_PURCHASE_STATUS = #{advancePurchaseStatus,jdbcType=BOOLEAN},
      </if>
      <if test="advancePurchaseComments != null">
        ADVANCE_PURCHASE_COMMENTS = #{advancePurchaseComments,jdbcType=VARCHAR},
      </if>
      <if test="advancePurchaseTime != null">
        ADVANCE_PURCHASE_TIME = #{advancePurchaseTime,jdbcType=BIGINT},
      </if>
      <if test="isUrgent != null">
        IS_URGENT = #{isUrgent,jdbcType=BOOLEAN},
      </if>
      <if test="urgentAmount != null">
        URGENT_AMOUNT = #{urgentAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveCommunicate != null">
        HAVE_COMMUNICATE = #{haveCommunicate,jdbcType=BOOLEAN},
      </if>
      <if test="prepareComments != null">
        PREPARE_COMMENTS = #{prepareComments,jdbcType=VARCHAR},
      </if>
      <if test="marketingPlan != null">
        MARKETING_PLAN = #{marketingPlan,jdbcType=VARCHAR},
      </if>
      <if test="statusComments != null">
        STATUS_COMMENTS = #{statusComments,jdbcType=INTEGER},
      </if>
      <if test="syncStatus != null">
        SYNC_STATUS = #{syncStatus,jdbcType=BOOLEAN},
      </if>
      <if test="logisticsApiSync != null">
        LOGISTICS_API_SYNC = #{logisticsApiSync,jdbcType=BOOLEAN},
      </if>
      <if test="logisticsWxsendSync != null">
        LOGISTICS_WXSEND_SYNC = #{logisticsWxsendSync,jdbcType=BOOLEAN},
      </if>
      <if test="satisfyInvoiceTime != null">
        SATISFY_INVOICE_TIME = #{satisfyInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="satisfyDeliveryTime != null">
        SATISFY_DELIVERY_TIME = #{satisfyDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="isSalesPerformance != null">
        IS_SALES_PERFORMANCE = #{isSalesPerformance,jdbcType=BOOLEAN},
      </if>
      <if test="salesPerformanceTime != null">
        SALES_PERFORMANCE_TIME = #{salesPerformanceTime,jdbcType=BIGINT},
      </if>
      <if test="salesPerformanceModTime != null">
        SALES_PERFORMANCE_MOD_TIME = #{salesPerformanceModTime,jdbcType=BIGINT},
      </if>
      <if test="isDelayInvoice != null">
        IS_DELAY_INVOICE = #{isDelayInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceMethod != null">
        INVOICE_METHOD = #{invoiceMethod,jdbcType=BOOLEAN},
      </if>
      <if test="lockedReason != null">
        LOCKED_REASON = #{lockedReason,jdbcType=VARCHAR},
      </if>
      <if test="costUserIds != null">
        COST_USER_IDS = #{costUserIds,jdbcType=VARCHAR},
      </if>
      <if test="ownerUserId != null">
        OWNER_USER_ID = #{ownerUserId,jdbcType=INTEGER},
      </if>
      <if test="invoiceEmail != null">
        INVOICE_EMAIL = #{invoiceEmail,jdbcType=VARCHAR},
      </if>
      <if test="paymentMode != null">
        PAYMENT_MODE = #{paymentMode,jdbcType=BOOLEAN},
      </if>
      <if test="payType != null">
        PAY_TYPE = #{payType,jdbcType=BOOLEAN},
      </if>
      <if test="isApplyInvoice != null">
        IS_APPLY_INVOICE = #{isApplyInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="applyInvoiceTime != null">
        APPLY_INVOICE_TIME = #{applyInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="adkSaleorderNo != null">
        ADK_SALEORDER_NO = #{adkSaleorderNo,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null">
        CREATE_MOBILE = #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="bdtraderComments != null">
        BDTRADER_COMMENTS = #{bdtraderComments,jdbcType=VARCHAR},
      </if>
      <if test="closeComments != null">
        CLOSE_COMMENTS = #{closeComments,jdbcType=VARCHAR},
      </if>
      <if test="bdMobileTime != null">
        BD_MOBILE_TIME = #{bdMobileTime,jdbcType=BIGINT},
      </if>
      <if test="webTakeDeliveryTime != null">
        WEB_TAKE_DELIVERY_TIME = #{webTakeDeliveryTime,jdbcType=BIGINT},
      </if>
      <if test="actionId != null">
        ACTION_ID = #{actionId,jdbcType=INTEGER},
      </if>
      <if test="isCoupons != null">
        IS_COUPONS = #{isCoupons,jdbcType=BOOLEAN},
      </if>
      <if test="couponmoney != null">
        COUPONMONEY = #{couponmoney,jdbcType=DECIMAL},
      </if>
      <if test="originalAmount != null">
        ORIGINAL_AMOUNT = #{originalAmount,jdbcType=DECIMAL},
      </if>
      <if test="elSaleordreNo != null">
        EL_SALEORDRE_NO = #{elSaleordreNo,jdbcType=VARCHAR},
      </if>
      <if test="isPrintout != null">
        IS_PRINTOUT = #{isPrintout,jdbcType=BOOLEAN},
      </if>
      <if test="outIsFlag != null">
        OUT_IS_FLAG = #{outIsFlag,jdbcType=BOOLEAN},
      </if>
      <if test="updateDataTime != null">
        UPDATE_DATA_TIME = #{updateDataTime,jdbcType=TIMESTAMP},
      </if>
      <if test="realPayAmount != null">
        REAL_PAY_AMOUNT = #{realPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="realReturnAmount != null">
        REAL_RETURN_AMOUNT = #{realReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="realTotalAmount != null">
        REAL_TOTAL_AMOUNT = #{realTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="sendToPc != null">
        SEND_TO_PC = #{sendToPc,jdbcType=BOOLEAN},
      </if>
      <if test="retentionMoney != null">
        RETENTION_MONEY = #{retentionMoney,jdbcType=DECIMAL},
      </if>
      <if test="retentionMoneyDay != null">
        RETENTION_MONEY_DAY = #{retentionMoneyDay,jdbcType=INTEGER},
      </if>
      <if test="isSameAddress != null">
        IS_SAME_ADDRESS = #{isSameAddress,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceSendNode != null">
        INVOICE_SEND_NODE = #{invoiceSendNode,jdbcType=BOOLEAN},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="nopaybackAmount != null">
        NOPAYBACK_AMOUNT = #{nopaybackAmount,jdbcType=DECIMAL},
      </if>
      <if test="contractUrl != null">
        CONTRACT_URL = #{contractUrl,jdbcType=VARCHAR},
      </if>
      <if test="autoAudit != null">
        AUTO_AUDIT = #{autoAudit,jdbcType=INTEGER},
      </if>
      <if test="isRisk != null">
        IS_RISK = #{isRisk,jdbcType=BOOLEAN},
      </if>
      <if test="riskComments != null">
        RISK_COMMENTS = #{riskComments,jdbcType=VARCHAR},
      </if>
      <if test="riskTime != null">
        RISK_TIME = #{riskTime,jdbcType=BIGINT},
      </if>
      <if test="groupCustomerId != null">
        GROUP_CUSTOMER_ID = #{groupCustomerId,jdbcType=INTEGER},
      </if>
      <if test="isContractReturn != null">
        IS_CONTRACT_RETURN = #{isContractReturn,jdbcType=TINYINT},
      </if>
      <if test="isDeliveryorderReturn != null">
        IS_DELIVERYORDER_RETURN = #{isDeliveryorderReturn,jdbcType=TINYINT},
      </if>
      <if test="deliveryClaim != null">
        DELIVERY_CLAIM = #{deliveryClaim,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryDelayTime != null">
        DELIVERY_DELAY_TIME = #{deliveryDelayTime,jdbcType=BIGINT},
      </if>
      <if test="billPeriodSettlementType != null">
        BILL_PERIOD_SETTLEMENT_TYPE = #{billPeriodSettlementType,jdbcType=TINYINT},
      </if>
      <if test="isNew != null">
        IS_NEW = #{isNew,jdbcType=TINYINT},
      </if>
      <if test="confirmStatus != null">
        CONFIRM_STATUS = #{confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="confirmTime != null">
        CONFIRM_TIME = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractNoStampUrl != null">
        CONTRACT_NO_STAMP_URL = #{contractNoStampUrl,jdbcType=VARCHAR},
      </if>
      <if test="onlineReceiptStatus != null">
        ONLINE_RECEIPT_STATUS = #{onlineReceiptStatus,jdbcType=BOOLEAN},
      </if>
      <if test="confirmationFormUpload != null">
        CONFIRMATION_FORM_UPLOAD = #{confirmationFormUpload,jdbcType=BOOLEAN},
      </if>
      <if test="confirmationFormAudit != null">
        CONFIRMATION_FORM_AUDIT = #{confirmationFormAudit,jdbcType=BOOLEAN},
      </if>
      <if test="confirmationSubmitTime != null">
        CONFIRMATION_SUBMIT_TIME = #{confirmationSubmitTime,jdbcType=BIGINT},
      </if>
      <if test="prepareReaseonType != null">
        PREPARE_REASEON_TYPE = #{prepareReaseonType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-01-09-->
  <select id="selectBySaleorderNo" resultMap="BaseResultMap">
    select
    TS.*,
    TTC.TRADER_CUSTOMER_ID
    from T_SALEORDER TS
    LEFT JOIN T_TRADER_CUSTOMER TTC on TS.TRADER_ID = TTC.TRADER_ID
    where SALEORDER_NO=#{saleorderNo,jdbcType=VARCHAR}
  </select>

  <select id="getTaxRateByInvoiceType" resultType="java.lang.String">
    SELECT
        COMMENTS
    FROM
        T_SYS_OPTION_DEFINITION
    WHERE
        PARENT_ID = 428
    AND STATUS = 1
    AND SYS_OPTION_DEFINITION_ID = #{InvoiceType,jdbcType=INTEGER}
  </select>

  <select id="getSaleOrderNoById" resultType="java.lang.String">

    SELECT
      ts.SALEORDER_NO
    from
      T_SALEORDER ts
    WHERE
      ts.SALEORDER_ID = #{salesOrderId,jdbcType=INTEGER}
  </select>
</mapper>