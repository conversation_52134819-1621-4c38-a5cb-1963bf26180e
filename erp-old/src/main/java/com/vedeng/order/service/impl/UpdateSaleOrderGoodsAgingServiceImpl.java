package com.vedeng.order.service.impl;

import com.rabbitmq.SaleOrderWarningQueue;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.AgingTypeEnum;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.WarnLevelEnum;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.order.dao.SaleOrderWarningMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.model.SaleOrderWarnVo;
import com.vedeng.order.model.SaleOrderWarning;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.service.UpdateSaleOrderGoodsAgingService;
import com.vedeng.system.model.MessageTemplate;
import com.vedeng.system.service.MessageTemplateService;
import com.vedeng.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Description:  更新待采购订单列表，产品时效状态和预警等级业务
 * @Author:       davis
 * @Date:         2021/4/19 下午10:11
 * @Version:      1.0
 */
@Service("updateSaleOrderGoodsAgingService")
@Slf4j
public class UpdateSaleOrderGoodsAgingServiceImpl extends BaseServiceimpl implements UpdateSaleOrderGoodsAgingService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateSaleOrderGoodsAgingServiceImpl.class);

    @Autowired
    private SaleOrderWarningMapper saleOrderWarningMapper;

    @Autowired
    JavaMailSender javaMailSender;

    @Autowired
    SimpleMailMessage simpleMailMessage;

    @Autowired
    UserService userService;

    @Autowired
    MessageTemplateService messageTemplateService;

    @Autowired
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Override
    public void dealSaleOrderGoodsAging(SaleOrderWarnVo saleOrderWarnVo) {
        LOGGER.info("处理队列中的产品明细");
        long nowTime = DateUtil.gainNowDate();
        Integer warnStatus = saleOrderWarnVo.getWarnStatus();
        // 正常-->临期
        if (ErpConst.ONE == warnStatus) {
            send(saleOrderWarnVo, nowTime, WarnLevelEnum.THREE_L.getCode(), AgingTypeEnum.APPROACH.getCode(), 173);
        } else if (ErpConst.TWO == warnStatus) {
            // 临期-->逾期
            send(saleOrderWarnVo, nowTime, WarnLevelEnum.TWO_L.getCode(), AgingTypeEnum.OVERDUE.getCode(), 174);
        } else if (ErpConst.THREE == warnStatus) {
            // 逾期超过12小时，每过12个小时发送告警，更新采购订单历史数据
            send(saleOrderWarnVo, nowTime, WarnLevelEnum.ONE_L.getCode(), AgingTypeEnum.OVERDUE.getCode(), 174);
        }
    }

    @Override
    public void sendSaleOrder(SaleOrderWarnVo saleOrderWarnVo) {
        try {
            LOGGER.info("采购预警订单详情：{}", saleOrderWarnVo.getSaleorderNo());
            SaleOrderWarningQueue.getSaleOrderQueue().produce(saleOrderWarnVo);
        } catch (InterruptedException e) {
            LOGGER.error("待采购订单入列失败", e);
            log.error("【sendSaleOrder】处理异常",e);
        }
    }

    public void send(SaleOrderWarnVo saleOrderWarnVo, long nowTime, int warnLevel, int aging, int templateId) {
        saleOrderWarnVo.setWarnLevel(warnLevel);
        saleOrderWarnVo.setAging(aging);
        saleOrderWarnVo.setIsWarn(ErpConst.ONE);
        Integer userId = saleOrderWarnVo.getUserId();
        int isSendMessageSize = 0;
        List<SaleorderGoodsVo> sgvList = saleOrderWarnVo.getSaleorderGoodsVoList();
        if (CollectionUtils.isEmpty(sgvList)) {
            return;
        }
        // 再次校验是否是否需要重复发送消息
        for (SaleorderGoodsVo sgv : sgvList) {
            sgv.setUserId(userId);
            SaleOrderWarning saleOrderWarning = null;
            List<SaleOrderWarning> saleOrderWarningList = this.getHistorySaleOrderWarn(sgv);
            if (!CollectionUtils.isEmpty(saleOrderWarningList)) {
                saleOrderWarning = saleOrderWarningList.get(0);
            }
            if (saleOrderWarning != null && saleOrderWarning.getWarnLevel() == warnLevel && saleOrderWarning.getAging() == aging) {
                isSendMessageSize++;
            }
        }
        // 所有产品都已发送过消息，不需要再次发送
        if (isSendMessageSize == sgvList.size()) {
            return;
        }
        Map<String, String> map = new HashMap<>();
        map.put("saleOrderNo", saleOrderWarnVo.getSaleorderNo());
        List<Integer> userIds = new ArrayList<>();
        userIds.add(userId);
        map.put("userName", saleOrderWarnVo.getUsername());
        MessageUtil.sendMessage(templateId, userIds, map, "./order/buyorder/indexPendingPurchase.do?tabFlag=0&urlSaleorderNo="+saleOrderWarnVo.getSaleorderNo(),"njadmin","2");
        User user = userService.getUserById(userId);
        if (user != null ) {
            if (StringUtil.isNotEmpty(user.getEmail())) {
                sendEmail(user.getEmail(), "order/buyorder/indexPendingPurchase.do?tabFlag=0&urlSaleorderNo="+saleOrderWarnVo.getSaleorderNo(), map, 174);
            }
        }
        insertSaleOrderWarning(saleOrderWarnVo, userId, nowTime);
    }

    public void insertSaleOrderWarning(SaleOrderWarnVo saleOrderWarnVo, Integer userId, long nowTime) {
        List<SaleorderGoodsVo> saleOrderGoodsVoList = saleOrderWarnVo.getSaleorderGoodsVoList();
        if (CollectionUtils.isEmpty(saleOrderGoodsVoList)) {
            return;
        }
        for (SaleorderGoodsVo saleOrderGoodsVo : saleOrderGoodsVoList) {
            // saleOrderWarningMapper.deleteSaleOrderWarning(saleOrderGoodsVo);
            SaleOrderWarning saleOrderWarning = new SaleOrderWarning();
            saleOrderWarning.setSaleorderId(saleOrderGoodsVo.getSaleorderId());
            saleOrderWarning.setSaleorderGoodsId(saleOrderGoodsVo.getSaleorderGoodsId());
            saleOrderWarning.setUserId(userId);
            saleOrderWarning.setAging(saleOrderWarnVo.getAging());
            saleOrderWarning.setWarnLevel(saleOrderWarnVo.getWarnLevel());
            saleOrderWarning.setOverDue(saleOrderGoodsVo.getOverDue());
            saleOrderWarning.setWarnTime(nowTime);
            int num = saleOrderWarningMapper.updateSaleOrderWarning(saleOrderWarning);
            if (num == 0) {
                saleOrderWarningMapper.insertSaleOrderWarning(saleOrderWarning);
            }
            if (saleOrderGoodsVo.getOverDue() <= 2) {
                saleOrderGoodsVo.setAgingTime(null);
                saleOrderGoodsVo.setWarnLevel(saleOrderWarnVo.getWarnLevel());
                saleOrderGoodsVo.setAging(saleOrderWarnVo.getAging());
                saleOrderGoodsVo.setIsWarn(1);
                saleorderGoodsMapper.updateWarnStatusSelective(saleOrderGoodsVo);
            }
        }
    }

    @Override
    public List<SaleOrderWarning> getHistorySaleOrderWarn(SaleorderGoodsVo saleorderGoodsVo) {
        List<SaleOrderWarning> saleOrderWarningList = saleOrderWarningMapper.getHistorySaleOrderWarn(saleorderGoodsVo);
        return saleOrderWarningList;
    }

    @Override
    public List<User> getOrderAssistant(SaleorderGoodsVo sgv) {
        return saleOrderWarningMapper.getOrderAssistant(sgv);
    }

    public String sendEmail(String email, String url, Map<String, String> paramsMap , Integer messageTemplateId){
        MessageTemplate template = new MessageTemplate();
        template.setMessageTemplateId(messageTemplateId);
        MessageTemplate messageTemplate = messageTemplateService.getMessageTemplate(template);
        String title = "";
        String content;
        String returnMsg = "";
        if (null != messageTemplate) {
            title = messageTemplate.getTitle();
            content = messageTemplate.getContent();
            for (Map.Entry<String, String> entry : paramsMap.entrySet()) {
                String key = "\\{\\$" + entry.getKey() + "\\}";
                String value = entry.getValue();
                if (value == null) {
                    title = title.replaceAll(key, value);
                    content = content.replaceAll(key, "");
                } else {
                    title = title.replaceAll(key, value);
                    content = content.replaceAll(key, value);
                }
            }
            returnMsg = title + "：" + content;
        }
        try{
            if (StringUtil.isNotEmpty(saleOrderWarnEmail)) {
                email = saleOrderWarnEmail;
            }
            simpleMailMessage.setTo(new String[]{
                    email
            }) ;
            simpleMailMessage.setSubject(title);
            simpleMailMessage.setSentDate(new Date());
            String text = returnMsg + "\n查看地址: " + erpUrl + url;
            simpleMailMessage.setText(text);
            javaMailSender.send(simpleMailMessage);
        }catch (Exception e){
            LOGGER.error("",e);
        }
        return "邮件已发送";
    }
}
