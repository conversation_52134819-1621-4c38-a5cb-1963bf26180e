<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeReceiveBillMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveBillEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_RECEIVE_BILL-->
    <id column="KING_DEE_RECEIVE_BILL_ID" jdbcType="INTEGER" property="kingDeeReceiveBillId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="F_ID" jdbcType="VARCHAR" property="fId" />
    <result column="F_BILL_NO" jdbcType="VARCHAR" property="fBillNo" />
    <result column="F_BILL_TYPE_ID" jdbcType="VARCHAR" property="fBillTypeId" />
    <result column="F_PAY_ORG_ID" jdbcType="VARCHAR" property="fPayOrgId" />
    <result column="F_DATE" jdbcType="VARCHAR" property="fDate" />
    <result column="F_QZOK_LSH" jdbcType="VARCHAR" property="fQzokLsh" />
    <result column="F_CONTACT_UNIT_TYPE" jdbcType="VARCHAR" property="fContactUnitType" />
    <result column="F_CONTACT_UNIT" jdbcType="VARCHAR" property="fContactUnit" />
    <result column="F_QZOK_JYLX" jdbcType="VARCHAR" property="fQzokJylx" />
    <result column="F_QZOK_JYZT" jdbcType="VARCHAR" property="fQzokJyzt" />
    <result column="F_QZOK_JYFS" jdbcType="VARCHAR" property="fQzokJyfs" />
    <result column="FILE_IS_PUSH" jdbcType="INTEGER" property="fileIsPush" />
    <result column="ERP_BANK_BILL_ID" jdbcType="VARCHAR" property="erpBankBillId" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="F_RECEIVE_BILL_ENTRY"
            javaType="com.vedeng.erp.kingdee.dto.KingDeeReceiveBillEntryDto"
            property="fReceiveBillEntry"
            typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    KING_DEE_RECEIVE_BILL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    F_ID, F_BILL_NO, F_BILL_TYPE_ID, F_PAY_ORG_ID, F_DATE, F_QZOK_LSH, F_CONTACT_UNIT_TYPE, 
    F_CONTACT_UNIT, F_QZOK_JYLX, F_QZOK_JYZT, F_QZOK_JYFS, FILE_IS_PUSH, ERP_BANK_BILL_ID, 
    IS_DELETE, F_RECEIVE_BILL_ENTRY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_RECEIVE_BILL
    where KING_DEE_RECEIVE_BILL_ID = #{kingDeeReceiveBillId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_RECEIVE_BILL
    where KING_DEE_RECEIVE_BILL_ID = #{kingDeeReceiveBillId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="KING_DEE_RECEIVE_BILL_ID" keyProperty="kingDeeReceiveBillId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveBillEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_RECEIVE_BILL (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      F_ID, F_BILL_NO, F_BILL_TYPE_ID, 
      F_PAY_ORG_ID, F_DATE, F_QZOK_LSH, 
      F_CONTACT_UNIT_TYPE, F_CONTACT_UNIT, F_QZOK_JYLX, 
      F_QZOK_JYZT, F_QZOK_JYFS, FILE_IS_PUSH, 
      ERP_BANK_BILL_ID, IS_DELETE, F_RECEIVE_BILL_ENTRY
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{fId,jdbcType=VARCHAR}, #{fBillNo,jdbcType=VARCHAR}, #{fBillTypeId,jdbcType=VARCHAR}, 
      #{fPayOrgId,jdbcType=VARCHAR}, #{fDate,jdbcType=VARCHAR}, #{fQzokLsh,jdbcType=VARCHAR}, 
      #{fContactUnitType,jdbcType=VARCHAR}, #{fContactUnit,jdbcType=VARCHAR}, #{fQzokJylx,jdbcType=VARCHAR}, 
      #{fQzokJyzt,jdbcType=VARCHAR}, #{fQzokJyfs,jdbcType=VARCHAR}, #{fileIsPush,jdbcType=INTEGER}, 
      #{erpBankBillId,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}, #{fReceiveBillEntry,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="KING_DEE_RECEIVE_BILL_ID" keyProperty="kingDeeReceiveBillId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveBillEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_RECEIVE_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="fId != null and fId != ''">
        F_ID,
      </if>
      <if test="fBillNo != null and fBillNo != ''">
        F_BILL_NO,
      </if>
      <if test="fBillTypeId != null and fBillTypeId != ''">
        F_BILL_TYPE_ID,
      </if>
      <if test="fPayOrgId != null and fPayOrgId != ''">
        F_PAY_ORG_ID,
      </if>
      <if test="fDate != null and fDate != ''">
        F_DATE,
      </if>
      <if test="fQzokLsh != null and fQzokLsh != ''">
        F_QZOK_LSH,
      </if>
      <if test="fContactUnitType != null and fContactUnitType != ''">
        F_CONTACT_UNIT_TYPE,
      </if>
      <if test="fContactUnit != null and fContactUnit != ''">
        F_CONTACT_UNIT,
      </if>
      <if test="fQzokJylx != null and fQzokJylx != ''">
        F_QZOK_JYLX,
      </if>
      <if test="fQzokJyzt != null and fQzokJyzt != ''">
        F_QZOK_JYZT,
      </if>
      <if test="fQzokJyfs != null and fQzokJyfs != ''">
        F_QZOK_JYFS,
      </if>
      <if test="fileIsPush != null">
        FILE_IS_PUSH,
      </if>
      <if test="erpBankBillId != null and erpBankBillId != ''">
        ERP_BANK_BILL_ID,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="fReceiveBillEntry != null and fReceiveBillEntry != ''">
        F_RECEIVE_BILL_ENTRY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fId != null and fId != ''">
        #{fId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null and fBillNo != ''">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null and fBillTypeId != ''">
        #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fPayOrgId != null and fPayOrgId != ''">
        #{fPayOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null and fDate != ''">
        #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokLsh != null and fQzokLsh != ''">
        #{fQzokLsh,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnitType != null and fContactUnitType != ''">
        #{fContactUnitType,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnit != null and fContactUnit != ''">
        #{fContactUnit,jdbcType=VARCHAR},
      </if>
      <if test="fQzokJylx != null and fQzokJylx != ''">
        #{fQzokJylx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokJyzt != null and fQzokJyzt != ''">
        #{fQzokJyzt,jdbcType=VARCHAR},
      </if>
      <if test="fQzokJyfs != null and fQzokJyfs != ''">
        #{fQzokJyfs,jdbcType=VARCHAR},
      </if>
      <if test="fileIsPush != null">
        #{fileIsPush,jdbcType=INTEGER},
      </if>
      <if test="erpBankBillId != null and erpBankBillId != ''">
        #{erpBankBillId,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="fReceiveBillEntry != null and fReceiveBillEntry != ''">
        #{fReceiveBillEntry,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveBillEntity">
    <!--@mbg.generated-->
    update KING_DEE_RECEIVE_BILL
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fId != null and fId != ''">
        F_ID = #{fId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null and fBillNo != ''">
        F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null and fBillTypeId != ''">
        F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fPayOrgId != null and fPayOrgId != ''">
        F_PAY_ORG_ID = #{fPayOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null and fDate != ''">
        F_DATE = #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fQzokLsh != null and fQzokLsh != ''">
        F_QZOK_LSH = #{fQzokLsh,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnitType != null and fContactUnitType != ''">
        F_CONTACT_UNIT_TYPE = #{fContactUnitType,jdbcType=VARCHAR},
      </if>
      <if test="fContactUnit != null and fContactUnit != ''">
        F_CONTACT_UNIT = #{fContactUnit,jdbcType=VARCHAR},
      </if>
      <if test="fQzokJylx != null and fQzokJylx != ''">
        F_QZOK_JYLX = #{fQzokJylx,jdbcType=VARCHAR},
      </if>
      <if test="fQzokJyzt != null and fQzokJyzt != ''">
        F_QZOK_JYZT = #{fQzokJyzt,jdbcType=VARCHAR},
      </if>
      <if test="fQzokJyfs != null and fQzokJyfs != ''">
        F_QZOK_JYFS = #{fQzokJyfs,jdbcType=VARCHAR},
      </if>
      <if test="fileIsPush != null">
        FILE_IS_PUSH = #{fileIsPush,jdbcType=INTEGER},
      </if>
      <if test="erpBankBillId != null and erpBankBillId != ''">
        ERP_BANK_BILL_ID = #{erpBankBillId,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="fReceiveBillEntry != null and fReceiveBillEntry != ''">
        F_RECEIVE_BILL_ENTRY = #{fReceiveBillEntry,jdbcType=VARCHAR},
      </if>
    </set>
    where KING_DEE_RECEIVE_BILL_ID = #{kingDeeReceiveBillId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveBillEntity">
    <!--@mbg.generated-->
    update KING_DEE_RECEIVE_BILL
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      F_ID = #{fId,jdbcType=VARCHAR},
      F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      F_PAY_ORG_ID = #{fPayOrgId,jdbcType=VARCHAR},
      F_DATE = #{fDate,jdbcType=VARCHAR},
      F_QZOK_LSH = #{fQzokLsh,jdbcType=VARCHAR},
      F_CONTACT_UNIT_TYPE = #{fContactUnitType,jdbcType=VARCHAR},
      F_CONTACT_UNIT = #{fContactUnit,jdbcType=VARCHAR},
      F_QZOK_JYLX = #{fQzokJylx,jdbcType=VARCHAR},
      F_QZOK_JYZT = #{fQzokJyzt,jdbcType=VARCHAR},
      F_QZOK_JYFS = #{fQzokJyfs,jdbcType=VARCHAR},
      FILE_IS_PUSH = #{fileIsPush,jdbcType=INTEGER},
      ERP_BANK_BILL_ID = #{erpBankBillId,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      F_RECEIVE_BILL_ENTRY = #{fReceiveBillEntry,jdbcType=VARCHAR}
    where KING_DEE_RECEIVE_BILL_ID = #{kingDeeReceiveBillId,jdbcType=INTEGER}
  </update>

  <select id="queryErpBankBillIdByType" resultType="com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveBillEntity">
    SELECT ERP_BANK_BILL_ID,F_ID,F_BILL_NO,KING_DEE_RECEIVE_BILL_ID
        FROM KING_DEE_RECEIVE_BILL
    WHERE FILE_IS_PUSH = #{fileIsPush,jdbcType=INTEGER}
  </select>

  <update id="updatePushStatus">
    UPDATE KING_DEE_RECEIVE_BILL
    SET FILE_IS_PUSH = 1
    where KING_DEE_RECEIVE_BILL_ID = #{kingDeeReceiveBillId,jdbcType=INTEGER}
  </update>
</mapper>