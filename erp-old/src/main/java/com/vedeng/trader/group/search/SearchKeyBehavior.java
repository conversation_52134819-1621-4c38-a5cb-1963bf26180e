package com.vedeng.trader.group.search;

import com.vedeng.track.api.dto.business.req.BusinessReqDto;
import com.vedeng.trader.group.BaseBehavior;
import com.vedeng.trader.group.visit.TrackBaseBehvior;

import java.util.ArrayList;
import java.util.BitSet;
import java.util.List;

/**
 * <b>Description:</b>搜索关键词<br>
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/5/25
 */
public class SearchKeyBehavior extends TrackBaseBehvior {
    private String key;
    @Override
    public BitSet calculate() {
        BusinessReqDto reqDto=new BusinessReqDto();
        reqDto.setDayType(getPeriod());
        List<String> keys=new ArrayList<>();
        keys.add(getKey());
        reqDto.setSearchKeyWordsList(keys);
        BitSet middleResult=getSearchData(reqDto);
        BitSet result=getBaseTraderSet();
        result.and(middleResult);
        return result;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
