package com.vedeng.erp.trader.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.trader.domain.entity.CustomerBankAccountEntity;
import com.vedeng.erp.trader.dto.CustomerBankAccountApiDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CustomerBankAccountApiConvertor extends BaseMapStruct<CustomerBankAccountEntity, CustomerBankAccountApiDto> {
}
