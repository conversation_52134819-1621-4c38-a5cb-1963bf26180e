<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>商品费用类别列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="costCategoryList" datasource="erp-datasourcetarget" empty_show="" fixednumber="0" firstcol="numbers">
<div id="appendHead">
</div>
<div class="layui-fluid">
    <form class="layui-form" id="search">
        <div class="layui-inline ">
            <label>费用类别名称</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入费用类别"
                       oper="like" name="SEARCH_CREATOR_NAME" empty_show="-" style="" alias="A">
            </div>
        </div>
        <div class="layui-inline ">
            <label>金蝶科目ID</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="EQ"
                       name="COST_CATEGORY_KING_DEE_ID" empty_show="-" style="" alias="A">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">操作人</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="UPDATER_NAME" placeholder="请选择" style=""
                        alias="A" jdbctype="" data="SELECT DISTINCT u.USER_ID,u.USERNAME K, lower(u.USERNAME) V
                                                                                                                                                                    FROM T_USER u
                                                                                                                                                                             LEFT JOIN T_R_USER_POSIT up ON u.USER_ID = up.USER_ID
                                                                                                                                                                             LEFT JOIN T_POSITION p on up.POSITION_ID = p.POSITION_ID
                                                                                                                                                                             LEFT JOIN T_ORGANIZATION o ON p.ORG_ID = o.ORG_ID
                                                                                                                                                                    WHERE o.TYPE = 311   order by u.USERNAME"
                        datatype="KVSQLCACHE" oper="EQ"></object>
            </div>
        </div>
        <div class="layui-inline ">
            <label>操作时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="MOD_TIME" placeholder="" style=""
                        alias="A" jdbctype="DATE" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">是否需采购</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="IS_NEED_PURCHASE" placeholder="" style="" alias="A" jdbctype="" data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;否&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;是&quot;}]" datatype="JSON" oper=""></object>
            </div>
        </div>

    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
        <button type="table" class="layui-btn" url="/costCategory/addView.do" windowname="新增费用类别" opentype="MODEL"
                area="800px,500px"> 新增
        </button>
        <button type="table" class="layui-btn" url="/costCategory/editKingDeeView.do" windowname="维护金蝶费用"
                opentype="MODEL" area="800px,500px"> 维护金蝶费用
        </button>
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th name="COST_CATEGORY_ID">ERP类别ID</th>
            <th name="CATEGORY_NAME">ERP类别名称</th>
            <th name="COST_CATEGORY_KING_DEE_ID">金蝶类别ID</th>
            <th name="COST_CATEGORY_KING_DEE_NAME">金蝶类别名称</th>
            <th name="IS_NEED_PURCHASE">是否需采购</th>
            <th name="UPDATER_NAME">操作人</th>
            <th name="MOD_TIME">操作时间</th>
            <th name="OLD_CATEGORY_NAME">修改内容</th>
            <th type="rowbutton" id="rowbutton">
                <button class="layui-btn list-row-button" type="single" opentype="MODEL"
                        url="/costCategory/addView.do?costCategoryId=${COST_CATEGORY_ID}"
                        windowname="编辑费用类别" name="MODEL" area="800px,500px">编辑
                </button>
            </th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="order by COST_CATEGORY_ID desc" groupby="">
select
	*
from
	(
	select
		COST_CATEGORY_ID,
		DATE_FORMAT(A.MOD_TIME, '%Y-%m-%d %H:%i') MOD_TIME,
		A.UPDATER_NAME,
		CATEGORY_NAME,
		CONCAT(if(OLD_CATEGORY_NAME != '', CONCAT('ERP从', OLD_CATEGORY_NAME, '修改至', CATEGORY_NAME), '新增ERP类别'), if(OLD_UNIT_KING_DEE_NAME != '', CONCAT(';金蝶类别从', OLD_UNIT_KING_DEE_NAME, '修改至', COST_CATEGORY_KING_DEE_NO, '-', COST_CATEGORY_KING_DEE_NAME), if(UNIT_KING_DEE_NO != '', ';关联金蝶类别', ''))) OLD_CATEGORY_NAME,
		if(B.COST_CATEGORY_KING_DEE_NO is null,
		'-',
		B.COST_CATEGORY_KING_DEE_NO) COST_CATEGORY_KING_DEE_ID,
		if(B.COST_CATEGORY_KING_DEE_NAME is null,
		'-',
		B.COST_CATEGORY_KING_DEE_NAME) COST_CATEGORY_KING_DEE_NAME,
		A.IS_DEL,
		B.IS_DELETE,
		A.UPDATER,
		concat(CATEGORY_NAME, ',',
              if(COST_CATEGORY_KING_DEE_NAME is null, '', COST_CATEGORY_KING_DEE_NAME)) SEARCH_CREATOR_NAME,
		if(A.IS_NEED_PURCHASE = 1,
		'是',
		'否') IS_NEED_PURCHASE
	FROM
		T_SYS_COST_CATEGORY A
	left join T_SYS_COST_CATEGORY_KING_DEE B
                         on
		A.UNIT_KING_DEE_NO = B.COST_CATEGORY_KING_DEE_NO
		and A.IS_DEL = 0
		and B.IS_DELETE = 0) A
where
	1 = 1
     </pre>
                <pre id="count" class="layui-code">

     </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <script>

        console.log('hello world!')
    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>