<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.department.dao.DepartmentFeeItemsMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.department.model.DepartmentFeeItems">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 16:31:49 CST 2019.
    -->
    <id column="DEPARTMENT_FEE_ITEMS_ID" jdbcType="INTEGER" property="departmentFeeItemsId" />
    <result column="PARENT_ID" jdbcType="INTEGER" property="parentId" />
    <result column="FEE_ITEMS_NUMBER" jdbcType="INTEGER" property="feeItemsNumber" />
    <result column="FEE_ITEMS_NAME" jdbcType="VARCHAR" property="feeItemsName" />
    <result column="EVENT_MEANING" jdbcType="VARCHAR" property="eventMeaning" />
    <result column="EXTRA_CONTENT" jdbcType="VARCHAR" property="extraContent" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 16:31:49 CST 2019.
    -->
    DEPARTMENT_FEE_ITEMS_ID, PARENT_ID, FEE_ITEMS_NUMBER, FEE_ITEMS_NAME, EVENT_MEANING, 
    EXTRA_CONTENT, DESCRIPTION
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 16:31:49 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_DEPARTMENT_FEE_ITEMS
    where DEPARTMENT_FEE_ITEMS_ID = #{departmentFeeItemsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 16:31:49 CST 2019.
    -->
    delete from T_DEPARTMENT_FEE_ITEMS
    where DEPARTMENT_FEE_ITEMS_ID = #{departmentFeeItemsId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.department.model.DepartmentFeeItems">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 16:31:49 CST 2019.
    -->
    insert into T_DEPARTMENT_FEE_ITEMS (DEPARTMENT_FEE_ITEMS_ID, PARENT_ID, FEE_ITEMS_NUMBER, 
      FEE_ITEMS_NAME, EVENT_MEANING, EXTRA_CONTENT, 
      DESCRIPTION)
    values (#{departmentFeeItemsId,jdbcType=INTEGER}, #{parentId,jdbcType=INTEGER}, #{feeItemsNumber,jdbcType=INTEGER}, 
      #{feeItemsName,jdbcType=VARCHAR}, #{eventMeaning,jdbcType=VARCHAR}, #{extraContent,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.department.model.DepartmentFeeItems">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 16:31:49 CST 2019.
    -->
    insert into T_DEPARTMENT_FEE_ITEMS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="departmentFeeItemsId != null">
        DEPARTMENT_FEE_ITEMS_ID,
      </if>
      <if test="parentId != null">
        PARENT_ID,
      </if>
      <if test="feeItemsNumber != null">
        FEE_ITEMS_NUMBER,
      </if>
      <if test="feeItemsName != null">
        FEE_ITEMS_NAME,
      </if>
      <if test="eventMeaning != null">
        EVENT_MEANING,
      </if>
      <if test="extraContent != null">
        EXTRA_CONTENT,
      </if>
      <if test="description != null">
        DESCRIPTION,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="departmentFeeItemsId != null">
        #{departmentFeeItemsId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="feeItemsNumber != null">
        #{feeItemsNumber,jdbcType=INTEGER},
      </if>
      <if test="feeItemsName != null">
        #{feeItemsName,jdbcType=VARCHAR},
      </if>
      <if test="eventMeaning != null">
        #{eventMeaning,jdbcType=VARCHAR},
      </if>
      <if test="extraContent != null">
        #{extraContent,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.department.model.DepartmentFeeItems">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Apr 09 16:31:49 CST 2019.
    -->
    update T_DEPARTMENT_FEE_ITEMS
    <set>
      <if test="parentId != null">
        PARENT_ID = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="feeItemsNumber != null">
        FEE_ITEMS_NUMBER = #{feeItemsNumber,jdbcType=INTEGER},
      </if>
      <if test="feeItemsName != null">
        FEE_ITEMS_NAME = #{feeItemsName,jdbcType=VARCHAR},
      </if>
      <if test="eventMeaning != null">
        EVENT_MEANING = #{eventMeaning,jdbcType=VARCHAR},
      </if>
      <if test="extraContent != null">
        EXTRA_CONTENT = #{extraContent,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
    </set>
    where DEPARTMENT_FEE_ITEMS_ID = #{departmentFeeItemsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.department.model.DepartmentFeeItems">
    update T_DEPARTMENT_FEE_ITEMS
    set PARENT_ID = #{parentId,jdbcType=INTEGER},
      FEE_ITEMS_NUMBER = #{feeItemsNumber,jdbcType=INTEGER},
      FEE_ITEMS_NAME = #{feeItemsName,jdbcType=VARCHAR},
      EVENT_MEANING = #{eventMeaning,jdbcType=VARCHAR},
      EXTRA_CONTENT = #{extraContent,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=VARCHAR}
    where DEPARTMENT_FEE_ITEMS_ID = #{departmentFeeItemsId,jdbcType=INTEGER}
  </update>
  
  <select id="getMinFeesIds" resultType="java.lang.Integer">
    SELECT
        COALESCE(c.DEPARTMENT_FEE_ITEMS_ID, b.DEPARTMENT_FEE_ITEMS_ID, a.DEPARTMENT_FEE_ITEMS_ID) AS aaa
    FROM
        T_DEPARTMENT_FEE_ITEMS a
    LEFT JOIN T_DEPARTMENT_FEE_ITEMS b ON a.DEPARTMENT_FEE_ITEMS_ID = b.PARENT_ID
    LEFT JOIN T_DEPARTMENT_FEE_ITEMS c ON b.DEPARTMENT_FEE_ITEMS_ID = c.PARENT_ID
    WHERE
        a.DEPARTMENT_FEE_ITEMS_ID IN
        <foreach collection="collection" item="feeId" open="(" close=")" separator=",">
          #{feeId, jdbcType=INTEGER}
        </foreach>
    GROUP BY aaa
    ORDER BY
        a.DEPARTMENT_FEE_ITEMS_ID DESC,
        b.DEPARTMENT_FEE_ITEMS_ID DESC,
        c.DEPARTMENT_FEE_ITEMS_ID DESC
  </select>


  <!-- 一级收费项目 -->
  <resultMap id="BaseResultMap1" type="com.vedeng.department.model.DepartmentFeeItems">
    <result column="FEE_ID_ONE" property="departmentFeeItemsId"/>
    <result column="FEE_NAME_ONE" property="feeItemsName"/>
    <!-- 二级收费项目 -->
    <collection property="departmentFeeItemsList" ofType="com.vedeng.department.model.DepartmentFeeItems">
      <result column="FEE_ID_TWO" property="departmentFeeItemsId"/>
      <result column="FEE_NAME_TWO" property="feeItemsName"/>
      <result column="FEE_NAME_THREE" property="feeItemsNameThree"/>
    </collection>
  </resultMap>

  <select id="getFeesByIds" resultMap="BaseResultMap1">
    SELECT
      IF(e.DEPARTMENT_FEE_ITEMS_ID IS NULL, IF(d.DEPARTMENT_FEE_ITEMS_ID IS NULL,
        c.DEPARTMENT_FEE_ITEMS_ID, d.DEPARTMENT_FEE_ITEMS_ID), e.DEPARTMENT_FEE_ITEMS_ID) AS FEE_ID_ONE,

      IF(e.DEPARTMENT_FEE_ITEMS_ID IS NULL, IF(d.DEPARTMENT_FEE_ITEMS_ID IS NULL,
        c.FEE_ITEMS_NAME, d.FEE_ITEMS_NAME), e.FEE_ITEMS_NAME) AS FEE_NAME_ONE,

      IF(e.DEPARTMENT_FEE_ITEMS_ID IS NULL, IF(d.DEPARTMENT_FEE_ITEMS_ID IS NULL, NULL, c.DEPARTMENT_FEE_ITEMS_ID),
        IF(d.DEPARTMENT_FEE_ITEMS_ID IS NULL, c.DEPARTMENT_FEE_ITEMS_ID, d.DEPARTMENT_FEE_ITEMS_ID)) AS FEE_ID_TWO,

      IF(e.DEPARTMENT_FEE_ITEMS_ID IS NULL, IF(d.DEPARTMENT_FEE_ITEMS_ID IS NULL, NULL, c.FEE_ITEMS_NAME),
        IF(d.DEPARTMENT_FEE_ITEMS_ID IS NULL, c.FEE_ITEMS_NAME, d.FEE_ITEMS_NAME)) AS FEE_NAME_TWO,

      REPLACE(GROUP_CONCAT(IF(e.DEPARTMENT_FEE_ITEMS_ID IS NULL, NULL, IF(d.DEPARTMENT_FEE_ITEMS_ID IS NULL, NULL, c.DEPARTMENT_FEE_ITEMS_ID))
    ORDER BY IF(e.DEPARTMENT_FEE_ITEMS_ID IS NULL, NULL, IF(d.DEPARTMENT_FEE_ITEMS_ID IS NULL, NULL, c.DEPARTMENT_FEE_ITEMS_ID)) DESC), ",", "@") AS FEE_NAME_THREE

    FROM
      T_DEPARTMENT_FEE_ITEMS c
    LEFT JOIN T_DEPARTMENT_FEE_ITEMS d ON c.PARENT_ID = d.DEPARTMENT_FEE_ITEMS_ID
    LEFT JOIN T_DEPARTMENT_FEE_ITEMS e ON d.PARENT_ID = e.DEPARTMENT_FEE_ITEMS_ID
    WHERE
      c.DEPARTMENT_FEE_ITEMS_ID IN
      <foreach collection="collection" item="feeId" open="(" close=")" separator=",">
        #{feeId, jdbcType=INTEGER}
      </foreach>
    GROUP BY
      FEE_ID_TWO
    ORDER BY
      FEE_ID_ONE DESC, FEE_ID_TWO DESC
  </select>

</mapper>