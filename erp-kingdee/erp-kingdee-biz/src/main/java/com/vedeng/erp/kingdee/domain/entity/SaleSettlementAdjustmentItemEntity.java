package com.vedeng.erp.kingdee.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售售后调整明细单
 * @date 2023/2/16 12:53
 */
@Getter
@Setter
public class SaleSettlementAdjustmentItemEntity extends BaseEntity {

    /**
     * 销售售后调整单明细id
     */
    private Integer saleSettlementAdjustmentItemId;

    /**
     * 销售售后调整单明细id
     */
    private Integer saleSettlementAdjustmentId;

    /**
     * 分摊价格
     */
    private BigDecimal apportionAmount;

    /**
     * 调整金额
     */
    private BigDecimal adjustmentAmount;

    /**
     * 原始售价
     */
    private BigDecimal differenceAmount;

    /**
     * 实际售价
     */
    private BigDecimal adjustmentType;

    /**
     * skuId
     */
    private Integer skuId;

    /**
     * sku
     */
    private String sku;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 实际销售数量
     */
    private Integer num;

}
