<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.dwh.externaldb.dao.DwhWorkbenchMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.dwh.model.generate.DwsBussinessChanceWorkbenchDfDo">
    <id column="BUSSINESS_CHANCE_ID" jdbcType="INTEGER" property="bussinessChanceId" />
    <result column="BUSSINESS_CHANCE_NO" jdbcType="VARCHAR" property="bussinessChanceNo" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="ORG_ID" jdbcType="INTEGER" property="orgId" />
    <result column="USER_NAME" jdbcType="VARCHAR" property="userName" />
    <result column="L1_ID" jdbcType="INTEGER" property="l1Id" />
    <result column="L1_NAME" jdbcType="VARCHAR" property="l1Name" />
    <result column="L2_ID" jdbcType="INTEGER" property="l2Id" />
    <result column="L2_NAME" jdbcType="VARCHAR" property="l2Name" />
    <result column="L3_ID" jdbcType="INTEGER" property="l3Id" />
    <result column="L3_NAME" jdbcType="VARCHAR" property="l3Name" />
    <result column="POSITION_ID" jdbcType="INTEGER" property="positionId" />
    <result column="POSITION_LEVEL" jdbcType="INTEGER" property="positionLevel" />
    <result column="POSITION_NAME" jdbcType="VARCHAR" property="positionName" />
    <result column="POSITION_TYPE" jdbcType="INTEGER" property="positionType" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="TYPE" jdbcType="INTEGER" property="type" />
    <result column="RECEIVE_TIME" jdbcType="BIGINT" property="receiveTime" />
    <result column="SOURCE" jdbcType="INTEGER" property="source" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="COMMUNICATION" jdbcType="INTEGER" property="communication" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="GOODS_CATEGORY" jdbcType="INTEGER" property="goodsCategory" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="GOODS_BRAND" jdbcType="VARCHAR" property="goodsBrand" />
    <result column="ASSIGN_TIME" jdbcType="BIGINT" property="assignTime" />
    <result column="FIRST_VIEW_TIME" jdbcType="BIGINT" property="firstViewTime" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
    <result column="BUSSINESS_LEVEL" jdbcType="INTEGER" property="bussinessLevel" />
    <result column="BUSSINESS_STAGE" jdbcType="INTEGER" property="bussinessStage" />
    <result column="ENQUIRY_TYPE" jdbcType="INTEGER" property="enquiryType" />
    <result column="ORDER_RATE" jdbcType="INTEGER" property="orderRate" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="QUOTE_AMOUNT" jdbcType="DECIMAL" property="quoteAmount" />
    <result column="SALEORDER_AMOUNT" jdbcType="DECIMAL" property="saleorderAmount" />
    <result column="ORDER_TIME" jdbcType="BIGINT" property="orderTime" />
    <result column="IS_NEW" jdbcType="INTEGER" property="isNew" />
    <result column="MERGE_STATUS" jdbcType="INTEGER" property="mergeStatus" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="IS_BIG_PROJECT" jdbcType="INTEGER" property="isBigProject" />
    <result column="HAS_CORE_SPU" jdbcType="INTEGER" property="hasCoreSpu" />
    <result column="IS_BIG_CHANCE" jdbcType="INTEGER" property="isBigChance" />
    <result column="BUSSINESS_AMOUNT" jdbcType="DECIMAL" property="bussinessAmount" />
    <result column="QUOTEORDER_ID" jdbcType="INTEGER" property="quoteorderId" />
    <result column="QUOTEORDER_NO" jdbcType="VARCHAR" property="quoteorderNo" />
    <result column="SALEORDER_ID" jdbcType="INTEGER" property="saleorderId" />
    <result column="RELATED_SALEORDER_NO" jdbcType="VARCHAR" property="relatedSaleorderNo" />
    <result column="PURCHASING_TIME" jdbcType="BIGINT" property="purchasingTime" />
    <result column="PURCHASING_TYPE" jdbcType="INTEGER" property="purchasingType" />
    <result column="IS_POLICYMAKER" jdbcType="INTEGER" property="isPolicymaker" />
    <result column="CHECK_TRADER_NAME" jdbcType="VARCHAR" property="checkTraderName" />
    <result column="CHECK_TRADER_AREA" jdbcType="VARCHAR" property="checkTraderArea" />
    <result column="CHECK_TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="checkTraderContactName" />
    <result column="CHECK_TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="checkTraderContactMobile" />
    <result column="CHECK_TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="checkTraderContactTelephone" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName" />
    <result column="TELEPHONE" jdbcType="VARCHAR" property="telephone" />
  </resultMap>
  <sql id="Base_Column_List">
    BUSSINESS_CHANCE_ID, BUSSINESS_CHANCE_NO, COMPANY_ID, USER_ID, ORG_ID, USER_NAME,
    L1_ID, L1_NAME, L2_ID, L2_NAME, L3_ID, L3_NAME, POSITION_ID, POSITION_LEVEL, POSITION_NAME,
    POSITION_TYPE, TRADER_ID, TRADER_NAME, `TYPE`, RECEIVE_TIME, `SOURCE`, MOBILE, COMMUNICATION,
    CONTENT, GOODS_CATEGORY, GOODS_NAME, GOODS_BRAND, ASSIGN_TIME, FIRST_VIEW_TIME, `STATUS`,
    BUSSINESS_LEVEL, BUSSINESS_STAGE, ENQUIRY_TYPE, ORDER_RATE, AMOUNT, QUOTE_AMOUNT,
    SALEORDER_AMOUNT, ORDER_TIME, IS_NEW, MERGE_STATUS, ADD_TIME, CREATOR, IS_BIG_PROJECT,
    HAS_CORE_SPU, IS_BIG_CHANCE, BUSSINESS_AMOUNT, QUOTEORDER_ID, QUOTEORDER_NO, SALEORDER_ID,
    RELATED_SALEORDER_NO, PURCHASING_TIME, PURCHASING_TYPE, IS_POLICYMAKER, CHECK_TRADER_NAME,
    CHECK_TRADER_AREA, CHECK_TRADER_CONTACT_NAME, CHECK_TRADER_CONTACT_MOBILE, CHECK_TRADER_CONTACT_TELEPHONE,
    TRADER_CONTACT_ID, TRADER_CONTACT_NAME, TELEPHONE
  </sql>

  <select id="getUserBussinessChanceOverview" resultType="com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto">
    SELECT SUM(IFNULL(BUSSINESS_AMOUNT,0)) AS totalBussinessChanceAmount,
           SUM(IFNULL(AMOUNT,0))           AS totalExpectAmount,
           COUNT(1)                        AS totalBussinessChanceNum
    FROM DWS_BUSSINESS_CHANCE_WORKBENCH_DF
    WHERE
    <if test="overviewType != null">
      <choose>
        <when test=" overviewType == 1">
          ADD_TIME BETWEEN #{startTimeMillions,jdbcType=BIGINT} AND #{endTimeMillions,jdbcType=BIGINT}
        </when>
        <when test=" overviewType == 2">
          ORDER_TIME BETWEEN #{startTimeMillions,jdbcType=BIGINT} AND #{endTimeMillions,jdbcType=BIGINT}
        </when>
      </choose>
    </if>
    <if test="status != null">
      AND STATUS IN
      <foreach collection="status" item="status" open="(" close=")" index="index" separator=",">
        #{status,jdbcType=INTEGER}
      </foreach>
    </if>
    AND USER_ID = #{userId,jdbcType=INTEGER}
    AND MERGE_STATUS IN (0,2)
    AND COMPANY_ID = 1
  </select>

  <select id="getLeaderBussinessChanceOverview" resultType="com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto">
    SELECT
    SUM(IFNULL(BUSSINESS_AMOUNT,0)) AS totalBussinessChanceAmount,
    SUM(IFNULL(AMOUNT,0))           AS totalExpectAmount,
    COUNT(1)                        AS totalBussinessChanceNum,
    <if test=" userType != null">
      <choose>
        <when test=" userType == 1">
          L1_ID
        </when>
        <when test=" userType == 2">
          L2_ID
        </when>
        <when test=" userType == 3">
          L3_ID
        </when>
      </choose>
    </if>                            AS orgId
    FROM DWS_BUSSINESS_CHANCE_WORKBENCH_DF
    WHERE
    <if test="overviewType != null">
      <choose>
        <when test=" overviewType == 1">
          ADD_TIME BETWEEN #{startTimeMillions,jdbcType=BIGINT} AND #{endTimeMillions,jdbcType=BIGINT}
        </when>
        <when test=" overviewType == 2">
          ORDER_TIME BETWEEN #{startTimeMillions,jdbcType=BIGINT} AND #{endTimeMillions,jdbcType=BIGINT}
        </when>
      </choose>
    </if>
    AND COMPANY_ID = 1
    <if test="status != null">
      AND STATUS IN
      <foreach collection="status" item="status" open="(" close=")" index="index" separator=",">
        #{status,jdbcType=INTEGER}
      </foreach>
    </if>
    AND MERGE_STATUS IN (0,2)
    <if test=" userType != null">
      <choose>
        <when test=" userType == 1">
          AND L1_ID IN
          <foreach collection="orgIds" item="orgIds" open="(" close=")" index="index" separator=",">
            #{orgIds,jdbcType=INTEGER}
          </foreach>
            GROUP BY L1_ID
        </when>
        <when test=" userType == 2">
          AND L2_ID IN
          <foreach collection="orgIds" item="orgIds" open="(" close=")" index="index" separator=",">
            #{orgIds,jdbcType=INTEGER}
          </foreach>
            GROUP BY L2_ID
        </when>
        <when test=" userType == 3">
          AND L3_ID IN
          <foreach collection="orgIds" item="orgIds" open="(" close=")" index="index" separator=",">
            #{orgIds,jdbcType=INTEGER}
          </foreach>
          GROUP BY L3_ID
        </when>
      </choose>
    </if>

  </select>

  <select id="getBussinessStatus" resultType="com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto">
    SELECT STATUS                          AS status,
           SUM(IFNULL(BUSSINESS_AMOUNT,0)) AS totalBussinessChanceAmount,
           COUNT(1)                        AS totalBussinessChanceNum
    FROM DWS_BUSSINESS_CHANCE_WORKBENCH_DF
    WHERE ADD_TIME BETWEEN #{startTimeMillions,jdbcType=BIGINT} AND #{endTimeMillions,jdbcType=BIGINT}
    AND COMPANY_ID = 1
    <if test="status != null">
      AND STATUS IN
      <foreach collection="status" item="status" open="(" close=")" index="index" separator=",">
        #{status,jdbcType=INTEGER}
      </foreach>
    </if>
    AND USER_ID = #{userId,jdbcType=INTEGER}
    AND MERGE_STATUS IN (0,2)
    GROUP BY STATUS
  </select>


  <select id="getLeaderBussinessStatus" resultType="com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto">
    SELECT STATUS                          AS status,
    <if test=" userType != null">
      <choose>
        <when test=" userType == 1">
          L1_ID
        </when>
        <when test=" userType == 2">
          L2_ID
        </when>
        <when test=" userType == 3">
          L3_ID
        </when>
      </choose>
    </if>                           AS orgId,
    SUM(IFNULL(BUSSINESS_AMOUNT,0)) AS totalBussinessChanceAmount,
    COUNT(1)                        AS totalBussinessChanceNum
    FROM DWS_BUSSINESS_CHANCE_WORKBENCH_DF
    WHERE ADD_TIME BETWEEN #{startTimeMillions,jdbcType=BIGINT} AND #{endTimeMillions,jdbcType=BIGINT}
    AND COMPANY_ID = 1
    <if test="status != null">
      AND STATUS IN
      <foreach collection="status" item="status" open="(" close=")" index="index" separator=",">
        #{status,jdbcType=INTEGER}
      </foreach>
    </if>
    AND MERGE_STATUS IN (0,2)
    <if test=" userType != null">
      <choose>
        <when test=" userType == 1">
          AND L1_ID IN
          <foreach collection="orgIds" item="orgIds" open="(" close=")" index="index" separator=",">
            #{orgIds,jdbcType=INTEGER}
          </foreach>
          GROUP BY L1_ID
        </when>
        <when test=" userType == 2">
          AND L2_ID IN
          <foreach collection="orgIds" item="orgIds" open="(" close=")" index="index" separator=",">
            #{orgIds,jdbcType=INTEGER}
          </foreach>
          GROUP BY L2_ID
        </when>
        <when test=" userType == 3">
          AND L3_ID IN
          <foreach collection="orgIds" item="orgIds" open="(" close=")" index="index" separator=",">
            #{orgIds,jdbcType=INTEGER}
          </foreach>
          GROUP BY L3_ID
        </when>
      </choose>
    </if>
    ,STATUS
  </select>

  <select id="getImportBussinessChance" resultType="com.vedeng.dwh.model.dto.DwhWorkbenchDto">
    select BUSSINESS_CHANCE_ID as bussinessChanceId,
     BUSSINESS_CHANCE_NO as bussinessChanceNo,
     TRADER_ID as traderId,
     TRADER_NAME as traderName,
     GOODS_NAME as goodsName,
     BUSSINESS_LEVEL as bussinessLevel,
     COALESCE (BUSSINESS_AMOUNT,0)AS bussinessAmount,
     CONTENT as content,
     GOODS_NAME as goodsName,
     USER_ID as userId,
     USER_NAME as userName,
     CHECK_TRADER_CONTACT_MOBILE as checkTraderContactMobile,
     MOBILE as mobile,
     TELEPHONE as telephone,
     CHECK_TRADER_CONTACT_NAME as chechTraderContactName,
     TRADER_CONTACT_NAME as traderContactName,
     coalesce (CHECK_TRADER_CONTACT_MOBILE,MOBILE) as displayMobile,
     coalesce (CHECK_TRADER_NAME,TRADER_NAME) as displayTraderName
    from DWS_BUSSINESS_CHANCE_WORKBENCH_DF
    WHERE ADD_TIME BETWEEN #{startTimeMillions,jdbcType=BIGINT} AND #{endTimeMillions,jdbcType=BIGINT}
    AND COMPANY_ID = 1
      <if test="status != null">
        AND STATUS IN
        <foreach collection="status" item="status" open="(" close=")" index="index" separator=",">
          #{status,jdbcType=INTEGER}
        </foreach>
      </if>
      AND USER_ID = #{userId,jdbcType=INTEGER}
      AND MERGE_STATUS IN (0,2)
      AND BUSSINESS_LEVEL in (939, 940)
  </select>

  <!--change by Randy.Xu 2020/11/8  3:46 .Desc:. begin -->


  <select id="getLeaderImportBussinessChance" resultType="com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto">
    select STATUS as status,
    <if test=" userType != null">
      <choose>
        <when test=" userType == 1">
          L1_ID
        </when>
        <when test=" userType == 2">
          L2_ID
        </when>
        <when test=" userType == 3">
          L3_ID
        </when>
      </choose>
    </if> as orgId,
    <if test=" userType != null">
      <choose>
        <when test=" userType == 1">
          L1_Name
        </when>
        <when test=" userType == 2">
          L2_Name
        </when>
        <when test=" userType == 3">
          L3_Name
        </when>
      </choose>
    </if> as orgName,
      sum(if(BUSSINESS_LEVEL = 939,IFNULL(BUSSINESS_AMOUNT,0),0)) as ssTotalAmount,
      sum(if(BUSSINESS_LEVEL = 939,1,0)) as ssTotalNum,
      sum(if(BUSSINESS_LEVEL = 940,IFNULL(BUSSINESS_AMOUNT,0),0)) as aaTotalAmount,
      sum(if(BUSSINESS_LEVEL = 940,1,0)) as  aaTotalNum
    from DWS_BUSSINESS_CHANCE_WORKBENCH_DF
    WHERE ADD_TIME BETWEEN #{startTimeMillions,jdbcType=BIGINT} AND #{endTimeMillions,jdbcType=BIGINT}
    AND COMPANY_ID = 1
    <if test="status != null">
      AND STATUS IN
      <foreach collection="status" item="status" open="(" close=")" index="index" separator=",">
        #{status,jdbcType=INTEGER}
      </foreach>
    </if>
    AND MERGE_STATUS IN (0,2)
    <if test=" userType != null">
      <choose>
        <when test=" userType == 1">
          AND L1_ID IN
          <foreach collection="orgIds" item="orgIds" open="(" close=")" index="index" separator=",">
            #{orgIds,jdbcType=INTEGER}
          </foreach>
          GROUP BY L1_ID
        </when>
        <when test=" userType == 2">
          AND L2_ID IN
          <foreach collection="orgIds" item="orgIds" open="(" close=")" index="index" separator=",">
            #{orgIds,jdbcType=INTEGER}
          </foreach>
          GROUP BY L2_ID
        </when>
        <when test=" userType == 3">
          AND L3_ID IN
          <foreach collection="orgIds" item="orgIds" open="(" close=")" index="index" separator=",">
            #{orgIds,jdbcType=INTEGER}
          </foreach>
          GROUP BY L3_ID
        </when>
      </choose>
    </if>
  </select>
    <!--change by Randy.Xu 2020/11/8  3:46 .Desc:. end   -->

</mapper>