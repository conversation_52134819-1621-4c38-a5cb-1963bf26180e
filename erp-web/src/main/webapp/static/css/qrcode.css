.qr-code-box {
        border: 1px solid #FFF;
        width: 310px;
        border-radius: 5px;
        font-family: "黑体", Arial, Helvetica, sans-serif;
        font-size: 16px;
		margin-left: 10px;
    }
    
    .qr-code-box * {
        box-sizing: border-box;
    }
    
    .qr-code-box .img {
        padding: 5px;
        padding-left: 0;
        float: right;
        width: 35%;
        text-align: center;
    }
    
    .qr-code-box .img img {
        width: 90%;
    }
    
    .qr-code-box .img .img-num {
        text-align: center;
    }
    
    .qr-code-box .message {
        padding: 6px;
        padding-right: 0;
        float: left;
        width: 65%;
        vertical-align: top;
    }
    
    .qr-code-box .message .item {
        display: block;
        line-height: 1.3;
        text-align: left;
        font-size: 16px;
        white-space: nowrap;
    }
    
    .qr-code-box .message .item.product_name {
        white-space: normal;
        word-wrap: break-word;
        max-height: 85px;
        overflow: hidden;
    }
    
    .clear {
        clear: both;
    }