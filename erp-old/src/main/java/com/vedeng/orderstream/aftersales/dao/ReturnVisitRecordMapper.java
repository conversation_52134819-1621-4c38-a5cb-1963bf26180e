package com.vedeng.orderstream.aftersales.dao;

import com.vedeng.orderstream.aftersales.model.ReturnVisitRecord;
import com.vedeng.orderstream.aftersales.model.dto.ReturnVisitRecordDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReturnVisitRecordMapper {

    int deleteByPrimaryKey(Integer returnVisitRecordId);

    int insert(ReturnVisitRecord record);

    int insertSelective(ReturnVisitRecord record);

    ReturnVisitRecord selectByPrimaryKey(Integer returnVisitRecordId);

    int updateByPrimaryKeySelective(ReturnVisitRecord record);

    int updateByPrimaryKey(ReturnVisitRecord record);

    /**
     * 根据售后单id查询回访记录
     */
    List<ReturnVisitRecordDto> getReturnVisitRecordListByAfterSaleId(@Param("afterSaleId") Integer afterSaleId);
}