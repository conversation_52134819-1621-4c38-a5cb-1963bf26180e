package com.vedeng.erp.trader.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VisitInputTraderDto {


    /**
     * 客户来源方式 (1erp 2终端库 3天眼查)
     */
    private Integer customerFrom;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 客户ID
     */
    private Integer traderCustomerId;

    /**
     * 客户所在地区-省CODE
     */
    private Integer provinceCode;

    /**
     * 客户所在地区-省名称
     */
    private String provinceName;

    /**
     * 客户所在地区-市CODE
     */
    private Integer cityCode;

    /**
     * 客户所在地区-市名称
     */
    private String cityName;

    /**
     * 拜访人
     */
    private Integer visitorId;

    /**
     * 客户类型 (ERP客户时自动带入且不能修改，其他必填。465分销466终端)
     */
    private Integer customerNature;


}
