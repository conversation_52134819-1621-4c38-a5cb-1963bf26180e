package com.vedeng.system.model;

import lombok.Data;

import java.util.Date;

/**
 * <b>Description:</b><br>
 * 类解释
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.system.model <br>
 * <b>ClassName:</b> WechatUser <br>
 * <b>Date:</b> 2021/4/1 14:14 <br>
 */
@Data
public class WechatUser {
    /**
     * 主键
     */
    private Integer accountId;

    /**
     * 是否删除：0否，1是
     */
    private Integer deleted;

    /**
     * 创建者Id
     */
    private Integer creatorId;

    /**
     * 更新者Id
     */
    private Integer updaterId;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 上次更新时间
     */
    private Date updatedTime;

    private String realName;

    private String displayName;

    private Integer sex;

    private String jobNumber;

    private String mobileNo;

    private String telephone;

    private String email;

    private String ccNumber;

    private Integer enable;

    private Integer workingStatus;

    /**
     * 员工头像
     */
    private String aliasHeadPicture;


    private String password;

    private String salt;

}
