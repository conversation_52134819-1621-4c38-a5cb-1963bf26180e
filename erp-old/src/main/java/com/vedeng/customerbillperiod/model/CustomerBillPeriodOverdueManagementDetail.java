package com.vedeng.customerbillperiod.model;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL
 * <AUTHOR>
@Data
public class CustomerBillPeriodOverdueManagementDetail implements Serializable {
    private Long billPeriodOverdueManagementDetailId;

    /**
     * 账期逾期编码
     */
    private String billPeriodOverdueManagementCode;

    /**
     * 客户所属公司ID
     */
    private Integer companyId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户账期使用明细ID
     */
    private Long billPeriodUseDetailId;

    /**
     * 客户账期逾期管理金额
     */
    private BigDecimal amount;

    /**
     * 账期逾期管理明细待还金额
     */
    private BigDecimal unreturnedAmount;

    /**
     * 账期逾期管理明细类型，@CustomerBillPeriodOverdueManageDetailTypeEnum
     */
    private Integer type;

    /**
     * 账期逾期管理关联表的ID，与TYPE字段对应：物流ID，发票ID，售后退货单ID，售后退票单ID，支付流水ID
     */
    private Long relatedId;

    /**
     * 当售后退货、退票、还款时产生的明细，对应的订单发货、开票明细ID
     */
    private Long parentManagementDetailId;

    /**
     * 账期结算周期的，单位：天
     */
    private Integer settlementPeriod;

    /**
     * 客户账期逾期管理明细添加时间
     */
    private Long addTime;

    /**
     * 逾期天数
     */
    private Integer overdueDays;

    /**
     * 逾期金额
     */
    private BigDecimal overdueAmount;

    /**
     * 账期逾期管理明细更新商机
     */
    private Long modTime;

    private static final long serialVersionUID = 1L;
}