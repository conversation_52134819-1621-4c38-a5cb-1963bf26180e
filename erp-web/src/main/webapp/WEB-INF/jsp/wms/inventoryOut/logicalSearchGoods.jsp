<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="添加产品" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript"
	src='<%=basePath%>/static/js/wms/inventoryOut/logicalSearchGoods.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript"
		src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<div class="formpublic formpublic1">
	<div>
		<!-- ------------产品数据列表--------start------- -->
		<div class="controlled" id="goodsListDiv">
			<!-- 搜索表格出来 -->
			<ul class="searchTable">
				<li>
				<!-- 商品外借出库搜素 -->
					<form method="post" id="search" action="<%=basePath%>/wms/inventoryOut/logicalSearchGoods.do">
						<input type="hidden" id ="callbackFuntion" name="callbackFuntion" value="${callbackFuntion}"/>
						<input type="hidden" name="logicalId" value="${logicalId}"/>
							<div class="infor_name ">
								<span>*</span> <label>产品名称</label>
							</div>
							<div class="f_left table-larger">
								<div class="mb10">
									<input type="text" class="input-larger mr5 J-searchContent" 
										placeholder="请输入产品名称/订货号/品牌/型号等关键词" id="searchContent"
										name="searchContent" value="${searchContent}">
									<input type="hidden" name="searchType" id="searchType" value="">
									<span class="bt-bg-style bt-small bg-light-blue" onclick="search();" id="errorMes">搜索</span>
									<%--<span class="bt-bg-style bt-small bg-light-blue" onclick="searchAll();" id="errorMes2">批量搜索</span>--%>
									<div class="warning J-error" style="display: none;">查询条件不允许为空</div>

								</div>
							</div>
						</form>
						<div>
							<table
								class="table table-bordered table-striped table-condensed table-centered mb10">
								<thead>
									<th class="table-smallest8">
										<input type="checkbox" id="table-smallest8-all" name="c_checkAllValue"/>
									</th>
									<th class="table-smallest8">订货号</th>
									<th>产品名称</th>
									<th class="table-smallest">品牌</th>
									<th class="table-smallest8">型号</th>
									<th class="table-smallest8">单位</th>
									<th class="table-smallest8">逻辑仓</th>
									<th class="table-smallest8">库存量</th>
									<%--<th class="table-smallest6">选择</th>--%>
								</thead>
								<tbody id="smallest8List">
									<c:forEach var="list" items="${goodsList}" varStatus="status">
										<c:forEach var="logicalMap" items="${list.availableStockMap}">
											<tr>
												<td>
													<c:if test="${logicalMap.value > 0}">
														<input type="checkbox" name="check_value" value="${list.sku}%@;${list.goodsName}%@;${list.brandName}%@;${list.model}%@;${list.unitName}%@;${logicalMap.value}%@;${logicalMap.key}" />
													</c:if>
												</td>
												<td>${list.sku}</td>
												<td>${list.goodsName}</td>
												<td>${list.brandName}</td>
												<td>${list.model}</td>
												<td>${list.unitName}</td>
												<td>${logicalMap.key}</td>
												<td>${logicalMap.value}</td>
													<%--<td>
                                                    <c:if test="${list.stockNum > 0}">
                                                        <a href="javascript:void(0);" onclick="selectGoods('${callbackFuntion}','${list.goodsId}','${list.sku}','${list.goodsName}','${list.brandName}','${list.model}','${list.unitName}','${list.stockNum}');">选择</a>
                                                    </c:if>
                                                    </td>--%>
											</tr>
										</c:forEach>

									</c:forEach>
									<c:if test="${empty goodsList}">
										<tr>
											<td colspan="7">查询无结果！请尝试使用其他搜索条件。</td>
										</tr>
									</c:if>
								</tbody>
							</table>
						</div></li>
				<tags:page page="${page}"/>
				<div class="clear"></div>
				<div class="tcenter">
					<span id="small8-submit" class="bt-small bg-light-blue bt-bg-style">确定</span>
					<span id="close_window" class="bt-small bg-light-orange bt-bg-style">取消</span>
				</div>
			</ul>
		</div>
	</div>
</div>
<%@ include file="../../common/footer.jsp"%>
