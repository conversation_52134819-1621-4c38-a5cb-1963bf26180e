package com.vedeng.order.model;

public class QuoteLinkBdLog {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_QUOTE_LINK_BD_LOG.QUOTE_LINK_BD_LOG_ID
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    private Integer quoteLinkBdLogId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_QUOTE_LINK_BD_LOG.QUOTE_ID
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    private Integer quoteId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_QUOTE_LINK_BD_LOG.BD_ORDER_ID
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    private Integer bdOrderId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_QUOTE_LINK_BD_LOG.IS_ENABLE
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    private Integer isEnable;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_QUOTE_LINK_BD_LOG.ADD_TIME
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    private Long addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_QUOTE_LINK_BD_LOG.UPDATE_TIME
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    private Long updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_QUOTE_LINK_BD_LOG.CREATOR
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_QUOTE_LINK_BD_LOG.UPDATOR
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    private Integer updator;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_QUOTE_LINK_BD_LOG.QUOTE_LINK_BD_LOG_ID
     *
     * @return the value of T_QUOTE_LINK_BD_LOG.QUOTE_LINK_BD_LOG_ID
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public Integer getQuoteLinkBdLogId() {
        return quoteLinkBdLogId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_QUOTE_LINK_BD_LOG.QUOTE_LINK_BD_LOG_ID
     *
     * @param quoteLinkBdLogId the value for T_QUOTE_LINK_BD_LOG.QUOTE_LINK_BD_LOG_ID
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public void setQuoteLinkBdLogId(Integer quoteLinkBdLogId) {
        this.quoteLinkBdLogId = quoteLinkBdLogId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_QUOTE_LINK_BD_LOG.QUOTE_ID
     *
     * @return the value of T_QUOTE_LINK_BD_LOG.QUOTE_ID
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public Integer getQuoteId() {
        return quoteId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_QUOTE_LINK_BD_LOG.QUOTE_ID
     *
     * @param quoteId the value for T_QUOTE_LINK_BD_LOG.QUOTE_ID
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public void setQuoteId(Integer quoteId) {
        this.quoteId = quoteId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_QUOTE_LINK_BD_LOG.BD_ORDER_ID
     *
     * @return the value of T_QUOTE_LINK_BD_LOG.BD_ORDER_ID
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public Integer getBdOrderId() {
        return bdOrderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_QUOTE_LINK_BD_LOG.BD_ORDER_ID
     *
     * @param bdOrderId the value for T_QUOTE_LINK_BD_LOG.BD_ORDER_ID
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public void setBdOrderId(Integer bdOrderId) {
        this.bdOrderId = bdOrderId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_QUOTE_LINK_BD_LOG.IS_ENABLE
     *
     * @return the value of T_QUOTE_LINK_BD_LOG.IS_ENABLE
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public Integer getIsEnable() {
        return isEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_QUOTE_LINK_BD_LOG.IS_ENABLE
     *
     * @param isEnable the value for T_QUOTE_LINK_BD_LOG.IS_ENABLE
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public void setIsEnable(Integer isEnable) {
        this.isEnable = isEnable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_QUOTE_LINK_BD_LOG.ADD_TIME
     *
     * @return the value of T_QUOTE_LINK_BD_LOG.ADD_TIME
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_QUOTE_LINK_BD_LOG.ADD_TIME
     *
     * @param addTime the value for T_QUOTE_LINK_BD_LOG.ADD_TIME
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_QUOTE_LINK_BD_LOG.UPDATE_TIME
     *
     * @return the value of T_QUOTE_LINK_BD_LOG.UPDATE_TIME
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_QUOTE_LINK_BD_LOG.UPDATE_TIME
     *
     * @param updateTime the value for T_QUOTE_LINK_BD_LOG.UPDATE_TIME
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_QUOTE_LINK_BD_LOG.CREATOR
     *
     * @return the value of T_QUOTE_LINK_BD_LOG.CREATOR
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_QUOTE_LINK_BD_LOG.CREATOR
     *
     * @param creator the value for T_QUOTE_LINK_BD_LOG.CREATOR
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_QUOTE_LINK_BD_LOG.UPDATOR
     *
     * @return the value of T_QUOTE_LINK_BD_LOG.UPDATOR
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public Integer getUpdator() {
        return updator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_QUOTE_LINK_BD_LOG.UPDATOR
     *
     * @param updator the value for T_QUOTE_LINK_BD_LOG.UPDATOR
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    public void setUpdator(Integer updator) {
        this.updator = updator;
    }
}