package com.vedeng.api.standard.duplicate.mapper;

import com.vedeng.api.standard.duplicate.entity.IdempotencyRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 幂等性记录数据访问接口
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-15
 */
public interface IdempotencyRecordMapper {
    
    /**
     * 根据幂等性键查询记录
     * 
     * @param idempotencyKey 幂等性键
     * @return 幂等性记录
     */
    IdempotencyRecord selectByIdempotencyKey(@Param("idempotencyKey") String idempotencyKey);
    
    /**
     * 插入幂等性记录
     * 
     * @param record 幂等性记录
     * @return 影响行数
     */
    int insert(IdempotencyRecord record);
    
    /**
     * 根据幂等性键更新记录
     * 
     * @param record 幂等性记录（包含要更新的字段）
     * @return 影响行数
     */
    int updateByIdempotencyKey(IdempotencyRecord record);
    
    /**
     * 根据主键删除记录
     * 
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据幂等性键删除记录
     * 
     * @param idempotencyKey 幂等性键
     * @return 影响行数
     */
    int deleteByIdempotencyKey(@Param("idempotencyKey") String idempotencyKey);
    
    /**
     * 根据幂等性键逻辑删除记录
     * 
     * @param idempotencyKey 幂等性键
     * @return 影响行数
     */
    int logicalDeleteByIdempotencyKey(@Param("idempotencyKey") String idempotencyKey);
    
    /**
     * 批量删除过期记录
     *
     * @param beforeDate 截止时间
     * @param batchSize 批次大小
     * @return 影响行数
     */
    int deleteExpiredRecords(@Param("beforeDate") Date beforeDate, @Param("batchSize") int batchSize);
    
    /**
     * 根据业务类型和时间删除记录
     * 
     * @param businessType 业务类型
     * @param beforeDate 时间界限
     * @return 影响行数
     */
    int deleteByBusinessTypeAndDate(@Param("businessType") String businessType, 
                                   @Param("beforeDate") Date beforeDate);
    
    /**
     * 删除失败状态的记录
     * 
     * @param beforeDate 时间界限
     * @return 影响行数
     */
    int deleteFailedRecords(@Param("beforeDate") Date beforeDate);
    
    /**
     * 查询长时间处理中的记录
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 长时间处理中的记录列表
     */
    List<IdempotencyRecord> selectLongProcessingRecords(@Param("timeoutMinutes") int timeoutMinutes);
    
    /**
     * 根据流程订单ID和公司代码查询记录
     * 
     * @param flowOrderId 流程订单ID
     * @param companyCode 公司代码
     * @param businessType 业务类型
     * @return 幂等性记录列表
     */
    List<IdempotencyRecord> selectByFlowOrderAndCompany(@Param("flowOrderId") String flowOrderId,
                                                       @Param("companyCode") String companyCode,
                                                       @Param("businessType") String businessType);
    
    /**
     * 根据业务文档ID查询记录
     * 
     * @param businessDocumentId 业务文档ID
     * @param businessType 业务类型
     * @return 幂等性记录
     */
    IdempotencyRecord selectByBusinessDocumentId(@Param("businessDocumentId") String businessDocumentId,
                                                @Param("businessType") String businessType);
    
    /**
     * 统计指定时间范围内的记录数
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param businessType 业务类型（可选）
     * @param status 状态（可选）
     * @return 记录数
     */
    int countRecords(@Param("startTime") Date startTime,
                    @Param("endTime") Date endTime,
                    @Param("businessType") String businessType,
                    @Param("status") Integer status);
    
    /**
     * 分页查询记录
     * 
     * @param offset 偏移量
     * @param limit 限制数量
     * @param businessType 业务类型（可选）
     * @param status 状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 记录列表
     */
    List<IdempotencyRecord> selectRecordsWithPaging(@Param("offset") int offset,
                                                   @Param("limit") int limit,
                                                   @Param("businessType") String businessType,
                                                   @Param("status") Integer status,
                                                   @Param("startTime") Date startTime,
                                                   @Param("endTime") Date endTime);
}
