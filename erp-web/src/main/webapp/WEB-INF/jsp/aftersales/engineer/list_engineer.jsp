<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="售后服务公司列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="searchfunc">
	<form method="post"
		action="${pageContext.request.contextPath}/aftersales/engineer/list.do"
		id="search">
		<ul>
			<li><label class="infor_name">公司名称</label> <input type="text"
															  class="input-middle" name="name" id="name"
															  value="${engineer.name }" /></li>
			<li><label class="infor_name">是否禁用</label>
				<select name="isEnable">
					<option value="-1">默认全部</option>
					<option value="0" <c:if test="${ not empty engineer &&  engineer.isEnable == 0 }">selected="selected"</c:if> >是</option>
					<option value="1" <c:if test="${ not empty engineer &&  engineer.isEnable == 1 }">selected="selected"</c:if> >否</option>
				</select>
			</li>
		</ul>
		<div class="tcenter">
			<span class="bt-small bg-light-blue bt-bg-style mr20 addtitle"
				  tabtitle='{"num":"addEngineer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"./aftersales/engineer/add.do","title":"新增售后服务公司"}'>新增</span>
			<span class="bt-small bg-light-blue bt-bg-style mr20 "
				  onclick="search();" id="searchSpan">查询</span> <span
				class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>
<%--			<span class="bg-light-blue bt-bg-style bt-small" onclick="exportList()">导出列表</span>--%>
		</div>
	</form>
</div>
<div class="content">
	<div class="list-page normal-list-page">
		<table
				class="table table-bordered table-striped table-condensed table-centered">
			<thead>
			<tr>
				<th>公司名称</th>
				<th>服务次数</th>
				<th>开户银行</th>
				<th>银行账号</th>
				<th>联行号</th>
				<th>是否禁用</th>
				<th class="wid10">操作</th>
			</tr>
			</thead>
			<tbody>
			<c:if test="${not empty list}">
				<c:forEach items="${list }" var="engineer" varStatus="status">
					<tr>
						<td><a class="addtitle" href="javascript:void(0);"
							   tabTitle='{"num":"viewengineer${engineer.engineerId}","link":"./aftersales/engineer/view.do?engineerId=${engineer.engineerId}","title":"查看售后服务公司"}'>${engineer.name }</a></td>
						<td>${engineer.serviceTimes }</td>
						<td>${engineer.bank}</td>
						<td>${engineer.bankAccount }</td>
						<td>${engineer.bankCode }</td>
						<td>
							<c:choose>
								<c:when test="${engineer.isEnable eq 0}">是</c:when>
								<c:when test="${engineer.isEnable eq 1}">否</c:when>
							</c:choose>
						</td>
						<td>
							<c:choose>
								<c:when test="${engineer.isEnable eq 0 }">
									<span class="font-blue cursor-pointer"
										 onclick="setEnable(${engineer.engineerId});">启用</span>
								</c:when>
								<c:otherwise>
									<span class="font-red  pop-new-data"
										 layerParams='{"width":"600px","height":"200px","title":"禁用售后服务公司","link":"${pageContext.request.contextPath}/aftersales/engineer/changeenable.do?engineerId=${engineer.engineerId}"}'>禁用</span>
								</c:otherwise>
							</c:choose>
							<span class="font-blue addtitle"
								  tabtitle='{"num":"editEngineer${engineer.engineerId }","link":"./aftersales/engineer/edit.do?engineerId=${engineer.engineerId }","title":"编辑售后服务公司"}'>编辑</span>
						</td>
					</tr>
				</c:forEach>
			</c:if>
			</tbody>
		</table>
		<c:if test="${empty list }">
			<!-- 查询无结果弹出 -->
			<div class="noresult">查询无结果！</div>
		</c:if>
	</div>
	<div>
		<tags:page page="${page}" />
	</div>
</div>
<script type="text/javascript"
		src="${pageContext.request.contextPath}/static/js/aftersales/engineer/list_engineer.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
		src="${pageContext.request.contextPath}/static/js/region/index.js?rnd=${resourceVersionKey}"></script>

<%@ include file="../../common/footer.jsp"%>
