<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.temporal.mapper.FlowNodeCompanyMapper">
    <!-- 结果映射 -->
    <resultMap id="CompanySequenceInfoResultMap" type="com.vedeng.temporal.domain.dto.CompanySequenceInfo">
        <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="COMPANY_SHORT_NAME" property="companyShortName" jdbcType="VARCHAR"/>
        <result column="KINGDEE_ACCOUNT_CODE" property="kingdeeAccountCode" jdbcType="VARCHAR"/>
        <result column="NODE_LEVEL" property="nodeLevel" jdbcType="INTEGER"/>
        <result column="TRADER_ID" property="traderId" jdbcType="INTEGER"/>
        <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR"/>
        <result column="FLOW_NODE_ID" property="flowNodeId" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础查询列 -->
    <sql id="Base_Company_Sequence_Columns">
        bci.ID as COMPANY_ID,
        bci.COMPANY_NAME,
        bci.COMPANY_SHORT_NAME,
        bci.KINGDEE_ACCOUNT_CODE,
        fn.NODE_LEVEL,
        fn.TRADER_ID,
        fn.FLOW_NODE_ID,
        t.TRADER_NAME
    </sql>

    <!-- 根据流转单ID获取公司执行顺序（联合查询） -->
    <select id="selectCompanySequenceByFlowOrderId" resultMap="CompanySequenceInfoResultMap">
        <!-- 第一部分：查询0级公司（起始采购公司，来自T_FLOW_ORDER.SOURCE_ERP） -->
        SELECT
        bci.ID as COMPANY_ID,
        bci.COMPANY_NAME,
        bci.COMPANY_SHORT_NAME,
        bci.KINGDEE_ACCOUNT_CODE,
        0 as NODE_LEVEL,
        NULL as TRADER_ID,
        NULL as FLOW_NODE_ID,
        bci.COMPANY_SHORT_NAME as TRADER_NAME
        FROM T_FLOW_ORDER fo
        INNER JOIN T_BASE_COMPANY_INFO bci ON fo.SOURCE_ERP = bci.COMPANY_SHORT_NAME
        WHERE fo.FLOW_ORDER_ID = #{flowOrderId}
        AND fo.IS_DELETE = 0
        AND bci.IS_DELETED = 0

        UNION ALL

        <!-- 第二部分：查询1级以上公司（来自T_FLOW_NODE） -->
        SELECT
        <include refid="Base_Company_Sequence_Columns"/>
        FROM T_FLOW_NODE fn
        INNER JOIN T_TRADER t ON fn.TRADER_ID = t.TRADER_ID
        INNER JOIN T_TRADER_SUPPLIER ts ON t.TRADER_ID = ts.TRADER_ID
        INNER JOIN T_BASE_COMPANY_INFO bci ON ts.TRADER_SUPPLIER_ID = bci.SUPPLIER_TRADER_ID
        WHERE fn.FLOW_ORDER_ID = #{flowOrderId}
        AND fn.IS_DELETE = 0
        AND t.IS_ENABLE = 1
        AND bci.IS_DELETED = 0

        ORDER BY NODE_LEVEL ASC
    </select>


    <!-- 根据流转单ID和业务类型获取公司执行顺序 -->
    <select id="selectCompanySequenceByBusinessType" resultMap="CompanySequenceInfoResultMap">
        <!-- 第一部分：查询0级公司（起始采购公司，来自T_FLOW_ORDER.SOURCE_ERP） -->
        SELECT
        bci.ID as COMPANY_ID,
        bci.COMPANY_NAME,
        bci.COMPANY_SHORT_NAME,
        bci.KINGDEE_ACCOUNT_CODE,
        0 as NODE_LEVEL,
        NULL as TRADER_ID,
        NULL as FLOW_NODE_ID,
        bci.COMPANY_SHORT_NAME as TRADER_NAME
        FROM T_FLOW_ORDER fo
        INNER JOIN T_BASE_COMPANY_INFO bci ON fo.SOURCE_ERP = bci.COMPANY_SHORT_NAME
        WHERE fo.FLOW_ORDER_ID = #{flowOrderId}
        AND fo.IS_DELETE = 0
        AND bci.IS_DELETED = 0

        UNION ALL

        <!-- 第二部分：查询1级以上公司（来自T_FLOW_NODE） -->
        SELECT
        <include refid="Base_Company_Sequence_Columns"/>
        FROM T_FLOW_NODE fn
        INNER JOIN T_TRADER t ON fn.TRADER_ID = t.TRADER_ID
        <!-- 采购业务统一使用供应商关联 -->
        INNER JOIN T_TRADER_SUPPLIER ts ON t.TRADER_ID = ts.TRADER_ID
        INNER JOIN T_BASE_COMPANY_INFO bci ON ts.TRADER_SUPPLIER_ID = bci.SUPPLIER_TRADER_ID
        WHERE fn.FLOW_ORDER_ID = #{flowOrderId}
        AND fn.IS_DELETE = 0
        AND t.IS_ENABLE = 1

        ORDER BY NODE_LEVEL ASC
    </select>


    <!-- 仅从流程节点获取公司执行顺序（不包含来自T_FLOW_ORDER的0级公司） -->
    <select id="selectCompanySequenceFromFlowNodeOnly" resultMap="CompanySequenceInfoResultMap">
        SELECT
        bci.ID as COMPANY_ID,
        COALESCE(bci.COMPANY_NAME, t.TRADER_NAME) as COMPANY_NAME,
        COALESCE(bci.COMPANY_SHORT_NAME, t.TRADER_NAME) as COMPANY_SHORT_NAME,
        bci.KINGDEE_ACCOUNT_CODE,
        fn.NODE_LEVEL,
        fn.TRADER_ID,
        fn.FLOW_NODE_ID,
        t.TRADER_NAME
        FROM T_FLOW_NODE fn
        INNER JOIN T_TRADER t ON fn.TRADER_ID = t.TRADER_ID
        LEFT JOIN T_TRADER_SUPPLIER ts ON t.TRADER_ID = ts.TRADER_ID
        LEFT JOIN T_BASE_COMPANY_INFO bci ON ts.TRADER_SUPPLIER_ID = bci.SUPPLIER_TRADER_ID AND bci.IS_DELETED = 0
        WHERE fn.FLOW_ORDER_ID = #{flowOrderId}
        AND fn.IS_DELETE = 0
        AND t.IS_ENABLE = 1

        ORDER BY NODE_LEVEL ASC
    </select>

    <!-- 根据流转单ID和公司代码获取流程节点ID -->
    <select id="getFlowNodeIdByFlowOrderAndCompany" resultType="java.lang.Long">
        SELECT fn.FLOW_NODE_ID
        FROM T_FLOW_NODE fn
                 INNER JOIN T_TRADER t ON fn.TRADER_ID = t.TRADER_ID
                 INNER JOIN T_TRADER_SUPPLIER ts ON t.TRADER_ID = ts.TRADER_ID
                 INNER JOIN T_BASE_COMPANY_INFO bci ON ts.TRADER_SUPPLIER_ID = bci.SUPPLIER_TRADER_ID
        WHERE fn.FLOW_ORDER_ID = #{flowOrderId}
          AND bci.COMPANY_SHORT_NAME = #{companyCode}
          AND fn.IS_DELETE = 0
          AND bci.IS_DELETED = 0
        LIMIT 1
    </select>

    <!-- 根据流程节点ID获取公司代码 -->
    <select id="getCompanyCodeByFlowNodeId" resultType="java.lang.String">
        SELECT bci.COMPANY_SHORT_NAME
        FROM T_FLOW_NODE fn
                 INNER JOIN T_TRADER t ON fn.TRADER_ID = t.TRADER_ID
                 INNER JOIN T_TRADER_SUPPLIER ts ON t.TRADER_ID = ts.TRADER_ID
                 INNER JOIN T_BASE_COMPANY_INFO bci ON ts.TRADER_SUPPLIER_ID = bci.SUPPLIER_TRADER_ID
        WHERE fn.FLOW_NODE_ID = #{flowNodeId}
          AND fn.IS_DELETE = 0
          AND bci.IS_DELETED = 0
        LIMIT 1
    </select>
</mapper>
