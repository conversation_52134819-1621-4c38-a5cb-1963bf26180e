package com.vedeng.aftersales.constant;


public enum ServiceChangesGoodIdEnum {
    RETURNS_SALEORDER_SERVICE_CHANGE_GOOD_ID(539,253620,"销售订单退货goodid"),

    EXCHANGES_SALEORDER_SERVICE_CHANGE_GOOD_ID(540,253620,"销售订单换货goodid"),

    INSTALL_COMMISSION_SALEORDER_SERVICE_CHANGE_GOOD_ID(4091,251462,"销售订单安调手续费goodid"),

    INSTALL_CONTRACT_SALEORDER_SERVICE_CHANGE_GOOD_ID(4090,251462,"销售订单安调手续费goodid"),

    MAINTENANCE_SALEORDER_SERVICE_CHANGE_GOOD_ID(584,140633,"销售订单维修手续费goodid"),

    NULL_SERVICE_CHANGE_GOOD_ID(0,0,"异常数据"),
    ;

    private Integer typeCode;

    private Integer goodCode;

    private String name;

    public Integer getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(Integer typeCode) {
        this.typeCode = typeCode;
    }

    public Integer getGoodCode() {
        return goodCode;
    }

    public void setGoodCode(Integer goodCode) {
        this.goodCode = goodCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    ServiceChangesGoodIdEnum(Integer typeCode, Integer goodCode, String name) {
        this.typeCode = typeCode;
        this.goodCode = goodCode;
        this.name = name;
    }

    public static ServiceChangesGoodIdEnum getEnumByTypeCode(Integer typeCode){
        for (ServiceChangesGoodIdEnum item : ServiceChangesGoodIdEnum.values()){
            if (item.getTypeCode().equals(typeCode)){
                return item;
            }
        }
        return null;
    }
}
