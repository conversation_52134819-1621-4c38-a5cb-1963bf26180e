<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDtoMapper">
    <sql id="Base_Column_List">
        INVOICE_ID,
        COMPANY_ID,
        INVOICE_CODE,
        INVOICE_FLOW,
        INVOICE_PROPERTY,
        INVOICE_HREF,
        `TYPE`,
        TAG,
        RELATED_ID,
        AFTER_SALES_ID,
        INVOICE_NO,
        INVOICE_TYPE,
        RATIO,
        COLOR_TYPE,
        AMOUNT,
        IS_ENABLE,
        VALID_USERID,
        VALID_STATUS,
        VALID_TIME,
        VALID_COMMENTS,
        INVOICE_PRINT_STATUS,
        INVOICE_CANCEL_STATUS,
        EXPRESS_ID,
        IS_AUTH,
        IS_MONTH_AUTH,
        AUTH_TIME,
        ADD_TIME,
        CREATOR,
        MOD_TIME,
        UPDATER,
        INVOICE_APPLY_ID,
        SEND_TIME,
        AFTER_EXPRESS_ID,
        OSS_FILE_URL,
        RESOURCE_ID,
        INVOICE_FROM,
        HX_INVOICE_ID,
        AUTH_MODE,
        AUTH_FAIL_REASON,
        IS_AUTHING,
        COLOR_COMPLEMENT_TYPE,
        IS_AFTER_BUYORDER_ONLY,
        invoiceTypeName,
        TRADER_SUPPLIER_ID,
        TRADER_CUSTOMER_ID
    </sql>
    <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto">
        <!--@mbg.generated-->
        <!--@Table T_INVOICE-->
        <id column="INVOICE_ID" jdbcType="INTEGER" property="invoiceId"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="INVOICE_CODE" jdbcType="VARCHAR" property="invoiceCode"/>
        <result column="INVOICE_FLOW" jdbcType="VARCHAR" property="invoiceFlow"/>
        <result column="INVOICE_PROPERTY" jdbcType="INTEGER" property="invoiceProperty"/>
        <result column="INVOICE_HREF" jdbcType="VARCHAR" property="invoiceHref"/>
        <result column="TYPE" jdbcType="INTEGER" property="type"/>
        <result column="TAG" jdbcType="INTEGER" property="tag"/>
        <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId"/>
        <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId"/>
        <result column="INVOICE_NO" jdbcType="VARCHAR" property="invoiceNo"/>
        <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType"/>
        <result column="RATIO" jdbcType="DECIMAL" property="ratio"/>
        <result column="COLOR_TYPE" jdbcType="INTEGER" property="colorType"/>
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount"/>
        <result column="IS_ENABLE" jdbcType="INTEGER" property="isEnable"/>
        <result column="VALID_USERID" jdbcType="INTEGER" property="validUserid"/>
        <result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus"/>
        <result column="VALID_TIME" jdbcType="BIGINT" property="validTime"/>
        <result column="VALID_COMMENTS" jdbcType="VARCHAR" property="validComments"/>
        <result column="INVOICE_PRINT_STATUS" jdbcType="INTEGER" property="invoicePrintStatus"/>
        <result column="INVOICE_CANCEL_STATUS" jdbcType="INTEGER" property="invoiceCancelStatus"/>
        <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId"/>
        <result column="IS_AUTH" jdbcType="INTEGER" property="isAuth"/>
        <result column="IS_MONTH_AUTH" jdbcType="INTEGER" property="isMonthAuth"/>
        <result column="AUTH_TIME" jdbcType="BIGINT" property="authTime"/>
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="INVOICE_APPLY_ID" jdbcType="INTEGER" property="invoiceApplyId"/>
        <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime"/>
        <result column="AFTER_EXPRESS_ID" jdbcType="INTEGER" property="afterExpressId"/>
        <result column="OSS_FILE_URL" jdbcType="VARCHAR" property="ossFileUrl"/>
        <result column="RESOURCE_ID" jdbcType="VARCHAR" property="resourceId"/>
        <result column="INVOICE_FROM" jdbcType="TINYINT" property="invoiceFrom"/>
        <result column="HX_INVOICE_ID" jdbcType="INTEGER" property="hxInvoiceId"/>
        <result column="AUTH_MODE" jdbcType="INTEGER" property="authMode"/>
        <result column="AUTH_FAIL_REASON" jdbcType="VARCHAR" property="authFailReason"/>
        <result column="IS_AUTHING" jdbcType="INTEGER" property="isAuthing"/>
        <result column="COLOR_COMPLEMENT_TYPE" jdbcType="INTEGER" property="colorComplementType"/>
        <result column="IS_AFTER_BUYORDER_ONLY" jdbcType="INTEGER" property="isAfterBuyorderOnly"/>
        <result column="invoiceTypeName" jdbcType="VARCHAR" property="invoiceTypeName"/>
        <result column="TRADER_SUPPLIER_ID" jdbcType="INTEGER" property="traderSupplierId"/>
        <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId"/>
        <result column="orderNo" jdbcType="VARCHAR" property="orderNo"/>
    </resultMap>
    <resultMap id="SaleOrderNoAndMinAddTimeResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto">
        <!--@mbg.generated-->
        <!--@Table T_INVOICE-->
        <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId"/>
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
        <result column="TRADER_CUSTOMER_ID" jdbcType="INTEGER" property="traderCustomerId"/>
    </resultMap>


    <select id="findByAll" resultMap="BaseResultMap">
        <!-- 税率 取 字典表信息 兼容发票表有的额没存税率 排除采后售后重新录入的蓝票-->
        select TI.INVOICE_ID,
        TI.COMPANY_ID,
        INVOICE_CODE,
        INVOICE_FLOW,
        INVOICE_PROPERTY,
        INVOICE_HREF,
        TYPE,
        TAG,
        TI.RELATED_ID,
        AFTER_SALES_ID,
        INVOICE_NO,
        TI.INVOICE_TYPE,
        COLOR_TYPE,
        TI.AMOUNT,
        TI.IS_ENABLE,
        VALID_USERID,
        TI.VALID_STATUS,
        TI.VALID_TIME,
        VALID_COMMENTS,
        INVOICE_PRINT_STATUS,
        INVOICE_CANCEL_STATUS,
        EXPRESS_ID,
        IS_AUTH,
        IS_MONTH_AUTH,
        AUTH_TIME,
        TI.ADD_TIME,
        TI.CREATOR,
        TI.MOD_TIME,
        TI.UPDATER,
        TI.IS_AFTER_BUYORDER_ONLY,
        INVOICE_APPLY_ID,
        SEND_TIME,
        AFTER_EXPRESS_ID,
        OSS_FILE_URL,
        RESOURCE_ID,
        INVOICE_FROM,
        HX_INVOICE_ID,
        AUTH_MODE,
        AUTH_FAIL_REASON,
        IS_AUTHING,
        COLOR_COMPLEMENT_TYPE,
        TRS.TRADER_SUPPLIER_ID,
        TSOD.COMMENTS ratio,
        TSOD.TITLE invoiceTypeName
        from T_INVOICE TI
        LEFT JOIN T_INVOICE_VOUCHER TIV ON TIV.INVOICE_ID = TI.INVOICE_ID and TIV.IS_DELETE = 0
        LEFT JOIN T_BUYORDER_EXPENSE_DETAIL TBED on TBED.BUYORDER_EXPENSE_ID = TI.RELATED_ID
        LEFT JOIN T_TRADER_SUPPLIER TRS on TRS.TRADER_ID = TBED.TRADER_ID
        LEFT JOIN T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE
        <where>
            <if test="invoiceId != null">
                and TI.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
            </if>
            <if test="companyId != null">
                and TI.COMPANY_ID = #{companyId,jdbcType=INTEGER}
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                and TI.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFlow != null and invoiceFlow != ''">
                and TI.INVOICE_FLOW = #{invoiceFlow,jdbcType=VARCHAR}
            </if>
            <if test="invoiceProperty != null">
                and TI.INVOICE_PROPERTY = #{invoiceProperty,jdbcType=INTEGER}
            </if>
            <if test="invoiceHref != null and invoiceHref != ''">
                and TI.INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and TI.`TYPE` = #{type,jdbcType=INTEGER}
            </if>
            <if test="tag != null">
                and TI.TAG = #{tag,jdbcType=INTEGER}
            </if>
            <if test="relatedId != null">
                and TI.RELATED_ID = #{relatedId,jdbcType=INTEGER}
            </if>
            <if test="afterSalesId != null">
                and TI.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                and TI.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
            </if>
            <if test="invoiceType != null">
                and TI.INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER}
            </if>
            <if test="ratio != null">
                and TI.RATIO = #{ratio,jdbcType=DECIMAL}
            </if>
            <if test="colorType != null">
                and TI.COLOR_TYPE = #{colorType,jdbcType=INTEGER}
            </if>
            <if test="amount != null">
                and TI.AMOUNT = #{amount,jdbcType=DECIMAL}
            </if>
            <if test="isEnable != null">
                and TI.IS_ENABLE = #{isEnable,jdbcType=INTEGER}
            </if>
            <if test="validUserid != null">
                and TI.VALID_USERID = #{validUserid,jdbcType=INTEGER}
            </if>
            <if test="validStatus != null">
                and TI.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
            </if>
            <if test="validTime != null">
                and TI.VALID_TIME = #{validTime,jdbcType=BIGINT}
            </if>
            <if test="validComments != null and validComments != ''">
                and TI.VALID_COMMENTS = #{validComments,jdbcType=VARCHAR}
            </if>
            <if test="invoicePrintStatus != null">
                and TI.INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=INTEGER}
            </if>
            <if test="invoiceCancelStatus != null">
                and TI.INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=INTEGER}
            </if>
            <if test="expressId != null">
                and TI.EXPRESS_ID = #{expressId,jdbcType=INTEGER}
            </if>
            <if test="isAuth != null">
                and TI.IS_AUTH = #{isAuth,jdbcType=INTEGER}
            </if>
            <if test="isMonthAuth != null">
                and TI.IS_MONTH_AUTH = #{isMonthAuth,jdbcType=INTEGER}
            </if>
            <if test="authTime != null">
                and TI.AUTH_TIME = #{authTime,jdbcType=BIGINT}
            </if>
            <if test="addTime != null">
                and TI.ADD_TIME = #{addTime,jdbcType=BIGINT}
            </if>
            <if test="creator != null">
                and TI.CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="modTime != null">
                and TI.MOD_TIME = #{modTime,jdbcType=BIGINT}
            </if>
            <if test="updater != null">
                and TI.UPDATER = #{updater,jdbcType=INTEGER}
            </if>
            <if test="invoiceApplyId != null">
                and TI.INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
            </if>
            <if test="sendTime != null">
                and TI.SEND_TIME = #{sendTime,jdbcType=TIMESTAMP}
            </if>
            <if test="afterExpressId != null">
                and TI.AFTER_EXPRESS_ID = #{afterExpressId,jdbcType=INTEGER}
            </if>
            <if test="ossFileUrl != null and ossFileUrl != ''">
                and TI.OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR}
            </if>
            <if test="resourceId != null and resourceId != ''">
                and TI.RESOURCE_ID = #{resourceId,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFrom != null">
                and TI.INVOICE_FROM = #{invoiceFrom,jdbcType=TINYINT}
            </if>
            <if test="hxInvoiceId != null">
                and TI.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
            </if>
            <if test="authMode != null">
                and TI.AUTH_MODE = #{authMode,jdbcType=INTEGER}
            </if>
            <if test="isAfterBuyorderOnly != null">
                and TI.IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly}
            </if>
            <if test="authFailReason != null and authFailReason != ''">
                and TI.AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR}
            </if>
            <if test="isAuthing != null">
                and TI.IS_AUTHING = #{isAuthing,jdbcType=INTEGER}
            </if>
            <if test="colorComplementType != null">
                and TI.COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null and endTime != null">
                and ((TI.VALID_TIME <![CDATA[>=]]> #{beginTime} and TI.VALID_TIME <![CDATA[<=]]> #{endTime})
                or (TI.AUTH_TIME <![CDATA[>=]]> #{beginTime} and TI.AUTH_TIME <![CDATA[<=]]> #{endTime})
                or ( TI.MOD_TIME <![CDATA[>=]]> #{beginTime} and TI.MOD_TIME <![CDATA[<=]]> #{endTime})
                )
            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="buyorderBlueEnableInvoicefindByAll" resultMap="BaseResultMap">
        <!--@mbg.generated 税率 取 字典表信息 兼容发票表有的额没存税率 排除采后售后重新录入的蓝票-->
        select TI.INVOICE_ID,
               TI.COMPANY_ID,
               INVOICE_CODE,
               INVOICE_FLOW,
               INVOICE_PROPERTY,
               INVOICE_HREF,
               TYPE,
               TAG,
               TI.RELATED_ID,
               AFTER_SALES_ID,
               INVOICE_NO,
               TI.INVOICE_TYPE,
               COLOR_TYPE,
               TI.AMOUNT,
               TI.IS_ENABLE,
               VALID_USERID,
               TI.VALID_STATUS,
               TI.VALID_TIME,
               VALID_COMMENTS,
               INVOICE_PRINT_STATUS,
               INVOICE_CANCEL_STATUS,
               EXPRESS_ID,
               IS_AUTH,
               IS_MONTH_AUTH,
               AUTH_TIME,
               TI.ADD_TIME,
               TI.CREATOR,
               TI.MOD_TIME,
               TI.UPDATER,
               TI.IS_AFTER_BUYORDER_ONLY,
               INVOICE_APPLY_ID,
               SEND_TIME,
               AFTER_EXPRESS_ID,
               OSS_FILE_URL,
               RESOURCE_ID,
               INVOICE_FROM,
               HX_INVOICE_ID,
               AUTH_MODE,
               AUTH_FAIL_REASON,
               IS_AUTHING,
               COLOR_COMPLEMENT_TYPE,
               TRS.TRADER_SUPPLIER_ID,
               TSOD.COMMENTS  ratio,
               TSOD.TITLE     invoiceTypeName,
               TB.BUYORDER_NO orderNo
        from T_INVOICE TI
                 LEFT JOIN T_INVOICE_VOUCHER TIV ON TIV.INVOICE_ID = TI.INVOICE_ID and TIV.IS_DELETE = 0
                 LEFT JOIN T_BUYORDER TB on TB.BUYORDER_ID = TI.RELATED_ID
                 LEFT JOIN T_TRADER_SUPPLIER TRS on TRS.TRADER_ID = TB.TRADER_ID
                 LEFT JOIN T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE
                 LEFT JOIN T_R_OLD_INVOICE_J_NEW_INVOICE TROIJNI on TROIJNI.NEW_INVOICE_ID = TI.INVOICE_ID
        <where>
            and TROIJNI.R_OLD_INVOICE_J_NEW_INVOICE_ID is null
            <if test="invoiceId != null">
                and TI.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
            </if>
            <if test="companyId != null">
                and TI.COMPANY_ID = #{companyId,jdbcType=INTEGER}
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                and TI.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFlow != null and invoiceFlow != ''">
                and TI.INVOICE_FLOW = #{invoiceFlow,jdbcType=VARCHAR}
            </if>
            <if test="invoiceProperty != null">
                and TI.INVOICE_PROPERTY = #{invoiceProperty,jdbcType=INTEGER}
            </if>
            <if test="invoiceHref != null and invoiceHref != ''">
                and TI.INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and TI.`TYPE` = #{type,jdbcType=INTEGER}
            </if>
            <if test="tag != null">
                and TI.TAG = #{tag,jdbcType=INTEGER}
            </if>
            <if test="relatedId != null">
                and TI.RELATED_ID = #{relatedId,jdbcType=INTEGER}
            </if>
            <if test="afterSalesId != null">
                and TI.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                and TI.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
            </if>
            <if test="invoiceType != null">
                and TI.INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER}
            </if>
            <if test="ratio != null">
                and TI.RATIO = #{ratio,jdbcType=DECIMAL}
            </if>
            <if test="colorType != null">
                and TI.COLOR_TYPE = #{colorType,jdbcType=INTEGER}
            </if>
            <if test="amount != null">
                and TI.AMOUNT = #{amount,jdbcType=DECIMAL}
            </if>
            <if test="isEnable != null">
                and TI.IS_ENABLE = #{isEnable,jdbcType=INTEGER}
            </if>
            <if test="validUserid != null">
                and TI.VALID_USERID = #{validUserid,jdbcType=INTEGER}
            </if>
            <if test="validStatus != null">
                and TI.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
            </if>
            <if test="validTime != null">
                and TI.VALID_TIME = #{validTime,jdbcType=BIGINT}
            </if>
            <if test="validComments != null and validComments != ''">
                and TI.VALID_COMMENTS = #{validComments,jdbcType=VARCHAR}
            </if>
            <if test="invoicePrintStatus != null">
                and TI.INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=INTEGER}
            </if>
            <if test="invoiceCancelStatus != null">
                and TI.INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=INTEGER}
            </if>
            <if test="expressId != null">
                and TI.EXPRESS_ID = #{expressId,jdbcType=INTEGER}
            </if>
            <if test="isAuth != null">
                and TI.IS_AUTH = #{isAuth,jdbcType=INTEGER}
            </if>
            <if test="isMonthAuth != null">
                and TI.IS_MONTH_AUTH = #{isMonthAuth,jdbcType=INTEGER}
            </if>
            <if test="authTime != null">
                and TI.AUTH_TIME = #{authTime,jdbcType=BIGINT}
            </if>
            <if test="addTime != null">
                and TI.ADD_TIME = #{addTime,jdbcType=BIGINT}
            </if>
            <if test="creator != null">
                and TI.CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="modTime != null">
                and TI.MOD_TIME = #{modTime,jdbcType=BIGINT}
            </if>
            <if test="updater != null">
                and TI.UPDATER = #{updater,jdbcType=INTEGER}
            </if>
            <if test="invoiceApplyId != null">
                and TI.INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
            </if>
            <if test="sendTime != null">
                and TI.SEND_TIME = #{sendTime,jdbcType=TIMESTAMP}
            </if>
            <if test="afterExpressId != null">
                and TI.AFTER_EXPRESS_ID = #{afterExpressId,jdbcType=INTEGER}
            </if>
            <if test="ossFileUrl != null and ossFileUrl != ''">
                and TI.OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR}
            </if>
            <if test="resourceId != null and resourceId != ''">
                and TI.RESOURCE_ID = #{resourceId,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFrom != null">
                and TI.INVOICE_FROM = #{invoiceFrom,jdbcType=TINYINT}
            </if>
            <if test="hxInvoiceId != null">
                and TI.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
            </if>
            <if test="authMode != null">
                and TI.AUTH_MODE = #{authMode,jdbcType=INTEGER}
            </if>
            <if test="isAfterBuyorderOnly != null">
                and TI.IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly}
            </if>
            <if test="authFailReason != null and authFailReason != ''">
                and TI.AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR}
            </if>
            <if test="isAuthing != null">
                and TI.IS_AUTHING = #{isAuthing,jdbcType=INTEGER}
            </if>
            <if test="colorComplementType != null">
                and TI.COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null and endTime != null">
                and ((TI.VALID_TIME <![CDATA[>=]]> #{beginTime} and TI.VALID_TIME <![CDATA[<=]]> #{endTime})
                    or (TI.AUTH_TIME <![CDATA[>=]]> #{beginTime} and TI.AUTH_TIME <![CDATA[<=]]> #{endTime})
                    or ( TI.MOD_TIME <![CDATA[>=]]> #{beginTime} and TI.MOD_TIME <![CDATA[<=]]> #{endTime})
                )
            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="findBlueDisEnableByInvoiceCodeAndInvoiceNo" resultMap="BaseResultMap">
        select INVOICE_ID,
               COMPANY_ID,
               INVOICE_CODE,
               INVOICE_FLOW,
               INVOICE_PROPERTY,
               INVOICE_HREF,
               TYPE,
               TAG,
               RELATED_ID,
               AFTER_SALES_ID,
               INVOICE_NO,
               INVOICE_TYPE,
               RATIO,
               COLOR_TYPE,
               AMOUNT,
               IS_ENABLE,
               VALID_USERID,
               VALID_STATUS,
               VALID_TIME,
               VALID_COMMENTS,
               INVOICE_PRINT_STATUS,
               INVOICE_CANCEL_STATUS,
               EXPRESS_ID,
               IS_AUTH,
               IS_MONTH_AUTH,
               AUTH_TIME,
               ADD_TIME,
               CREATOR,
               MOD_TIME,
               UPDATER,
               INVOICE_APPLY_ID,
               SEND_TIME,
               AFTER_EXPRESS_ID,
               OSS_FILE_URL,
               RESOURCE_ID,
               INVOICE_FROM,
               HX_INVOICE_ID,
               AUTH_MODE,
               AUTH_FAIL_REASON,
               IS_AUTHING,
               COLOR_COMPLEMENT_TYPE,
               IS_AFTER_BUYORDER_ONLY
        from T_INVOICE
        where INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
          and INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
          and COMPANY_ID = 1
          and TYPE = #{type,jdbcType=INTEGER}
          and COLOR_TYPE = 2
          and IS_ENABLE = 0
    </select>

    <select id="buyorderRedEnableInvoicefindByAll" resultMap="BaseResultMap">
        <!--@mbg.generated 税率 取 字典表信息 兼容发票表有的额没存税率 -->
        select TI.INVOICE_ID,
               TI.COMPANY_ID,
               INVOICE_CODE,
               INVOICE_FLOW,
               INVOICE_PROPERTY,
               INVOICE_HREF,
               TYPE,
               TAG,
               TI.RELATED_ID,
               AFTER_SALES_ID,
               INVOICE_NO,
               TI.INVOICE_TYPE,
               TI.IS_AFTER_BUYORDER_ONLY,
               TI.COLOR_TYPE,
               TI.AMOUNT,
               TI.IS_ENABLE,
               VALID_USERID,
               TI.VALID_STATUS,
               TI.VALID_TIME,
               VALID_COMMENTS,
               INVOICE_PRINT_STATUS,
               INVOICE_CANCEL_STATUS,
               EXPRESS_ID,
               IS_AUTH,
               IS_MONTH_AUTH,
               AUTH_TIME,
               TI.ADD_TIME,
               TI.CREATOR,
               TI.MOD_TIME,
               TI.UPDATER,
               INVOICE_APPLY_ID,
               SEND_TIME,
               AFTER_EXPRESS_ID,
               OSS_FILE_URL,
               RESOURCE_ID,
               INVOICE_FROM,
               HX_INVOICE_ID,
               AUTH_MODE,
               AUTH_FAIL_REASON,
               IS_AUTHING,
               COLOR_COMPLEMENT_TYPE,
               TRS.TRADER_SUPPLIER_ID,
               TSOD.COMMENTS ratio,
               TSOD.TITLE    invoiceTypeName,
               TB.BUYORDER_NO orderNo
        from T_INVOICE TI
                 LEFT JOIN T_INVOICE_VOUCHER TIV ON TIV.INVOICE_ID = TI.INVOICE_ID and TIV.IS_DELETE = 0
                 LEFT JOIN T_BUYORDER TB on TB.BUYORDER_ID = TI.RELATED_ID
                 LEFT JOIN T_TRADER_SUPPLIER TRS on TRS.TRADER_ID = TB.TRADER_ID
                 LEFT JOIN T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE
        <where>
            <if test="invoiceId != null">
                and TI.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
            </if>
            <if test="companyId != null">
                and TI.COMPANY_ID = #{companyId,jdbcType=INTEGER}
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                and TI.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFlow != null and invoiceFlow != ''">
                and TI.INVOICE_FLOW = #{invoiceFlow,jdbcType=VARCHAR}
            </if>
            <if test="invoiceProperty != null">
                and TI.INVOICE_PROPERTY = #{invoiceProperty,jdbcType=INTEGER}
            </if>
            <if test="invoiceHref != null and invoiceHref != ''">
                and TI.INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and TI.`TYPE` = #{type,jdbcType=INTEGER}
            </if>
            <if test="tag != null">
                and TI.TAG = #{tag,jdbcType=INTEGER}
            </if>
            <if test="relatedId != null">
                and TI.RELATED_ID = #{relatedId,jdbcType=INTEGER}
            </if>
            <if test="afterSalesId != null">
                and TI.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                and TI.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
            </if>
            <if test="invoiceType != null">
                and TI.INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER}
            </if>
            <if test="ratio != null">
                and TI.RATIO = #{ratio,jdbcType=DECIMAL}
            </if>
            <if test="colorType != null">
                and TI.COLOR_TYPE = #{colorType,jdbcType=INTEGER}
            </if>
            <if test="amount != null">
                and TI.AMOUNT = #{amount,jdbcType=DECIMAL}
            </if>
            <if test="isAfterBuyorderOnly != null">
                and TI.IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly}
            </if>
            <if test="isEnable != null">
                and TI.IS_ENABLE = #{isEnable,jdbcType=INTEGER}
            </if>
            <if test="validUserid != null">
                and TI.VALID_USERID = #{validUserid,jdbcType=INTEGER}
            </if>
            <if test="validStatus != null">
                and TI.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
            </if>
            <if test="validTime != null">
                and TI.VALID_TIME = #{validTime,jdbcType=BIGINT}
            </if>
            <if test="validComments != null and validComments != ''">
                and TI.VALID_COMMENTS = #{validComments,jdbcType=VARCHAR}
            </if>
            <if test="invoicePrintStatus != null">
                and TI.INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=INTEGER}
            </if>
            <if test="invoiceCancelStatus != null">
                and TI.INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=INTEGER}
            </if>
            <if test="expressId != null">
                and TI.EXPRESS_ID = #{expressId,jdbcType=INTEGER}
            </if>
            <if test="isAuth != null">
                and TI.IS_AUTH = #{isAuth,jdbcType=INTEGER}
            </if>
            <if test="isMonthAuth != null">
                and TI.IS_MONTH_AUTH = #{isMonthAuth,jdbcType=INTEGER}
            </if>
            <if test="authTime != null">
                and TI.AUTH_TIME = #{authTime,jdbcType=BIGINT}
            </if>
            <if test="addTime != null">
                and TI.ADD_TIME = #{addTime,jdbcType=BIGINT}
            </if>
            <if test="creator != null">
                and TI.CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="modTime != null">
                and TI.MOD_TIME = #{modTime,jdbcType=BIGINT}
            </if>
            <if test="updater != null">
                and TI.UPDATER = #{updater,jdbcType=INTEGER}
            </if>
            <if test="invoiceApplyId != null">
                and TI.INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
            </if>
            <if test="sendTime != null">
                and TI.SEND_TIME = #{sendTime,jdbcType=TIMESTAMP}
            </if>
            <if test="afterExpressId != null">
                and TI.AFTER_EXPRESS_ID = #{afterExpressId,jdbcType=INTEGER}
            </if>
            <if test="ossFileUrl != null and ossFileUrl != ''">
                and TI.OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR}
            </if>
            <if test="resourceId != null and resourceId != ''">
                and TI.RESOURCE_ID = #{resourceId,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFrom != null">
                and TI.INVOICE_FROM = #{invoiceFrom,jdbcType=TINYINT}
            </if>
            <if test="hxInvoiceId != null">
                and TI.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
            </if>
            <if test="authMode != null">
                and TI.AUTH_MODE = #{authMode,jdbcType=INTEGER}
            </if>
            <if test="authFailReason != null and authFailReason != ''">
                and TI.AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR}
            </if>
            <if test="isAuthing != null">
                and TI.IS_AUTHING = #{isAuthing,jdbcType=INTEGER}
            </if>
            <if test="colorComplementType != null">
                and TI.COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null and endTime != null">
                and ((TI.VALID_TIME <![CDATA[>=]]> #{beginTime} and TI.VALID_TIME <![CDATA[<=]]> #{endTime})
                    or (TI.AUTH_TIME <![CDATA[>=]]> #{beginTime} and TI.AUTH_TIME <![CDATA[<=]]> #{endTime})
                    or ( TI.MOD_TIME <![CDATA[>=]]> #{beginTime} and TI.MOD_TIME <![CDATA[<=]]> #{endTime})
                )
            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="buyorderNewBlueEnableInvoicefindByAll" resultMap="BaseResultMap">
        <!--@mbg.generated 税率 取 字典表信息 兼容发票表有的额没存税率 排除采后售后重新录入的蓝票-->
        select TI.INVOICE_ID,
               TI.COMPANY_ID,
               INVOICE_CODE,
               INVOICE_FLOW,
               INVOICE_PROPERTY,
               INVOICE_HREF,
               TYPE,
               TAG,
               TI.RELATED_ID,
               AFTER_SALES_ID,
               INVOICE_NO,
               TI.INVOICE_TYPE,
               COLOR_TYPE,
               TI.AMOUNT,
               TI.IS_ENABLE,
               VALID_USERID,
               TI.VALID_STATUS,
               TI.VALID_TIME,
               VALID_COMMENTS,
               INVOICE_PRINT_STATUS,
               INVOICE_CANCEL_STATUS,
               EXPRESS_ID,
               IS_AUTH,
               IS_MONTH_AUTH,
               AUTH_TIME,
               TI.ADD_TIME,
               TI.CREATOR,
               TI.MOD_TIME,
               TI.UPDATER,
               TI.IS_AFTER_BUYORDER_ONLY,
               INVOICE_APPLY_ID,
               SEND_TIME,
               AFTER_EXPRESS_ID,
               OSS_FILE_URL,
               RESOURCE_ID,
               INVOICE_FROM,
               HX_INVOICE_ID,
               AUTH_MODE,
               AUTH_FAIL_REASON,
               IS_AUTHING,
               COLOR_COMPLEMENT_TYPE,
               TRS.TRADER_SUPPLIER_ID,
               TSOD.COMMENTS ratio,
               TSOD.TITLE    invoiceTypeName,
               TB.BUYORDER_NO orderNo
        from T_INVOICE TI
                 LEFT JOIN T_INVOICE_VOUCHER TIV ON TIV.INVOICE_ID = TI.INVOICE_ID and TIV.IS_DELETE = 0
                 LEFT JOIN T_BUYORDER TB on TB.BUYORDER_ID = TI.RELATED_ID
                 LEFT JOIN T_TRADER_SUPPLIER TRS on TRS.TRADER_ID = TB.TRADER_ID
                 LEFT JOIN T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE
                 LEFT JOIN T_R_OLD_INVOICE_J_NEW_INVOICE TROIJNI on TROIJNI.NEW_INVOICE_ID = TI.INVOICE_ID
        <where>
            and TROIJNI.R_OLD_INVOICE_J_NEW_INVOICE_ID is not null
            <if test="invoiceId != null">
                and TI.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
            </if>
            <if test="companyId != null">
                and TI.COMPANY_ID = #{companyId,jdbcType=INTEGER}
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                and TI.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFlow != null and invoiceFlow != ''">
                and TI.INVOICE_FLOW = #{invoiceFlow,jdbcType=VARCHAR}
            </if>
            <if test="invoiceProperty != null">
                and TI.INVOICE_PROPERTY = #{invoiceProperty,jdbcType=INTEGER}
            </if>
            <if test="invoiceHref != null and invoiceHref != ''">
                and TI.INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and TI.`TYPE` = #{type,jdbcType=INTEGER}
            </if>
            <if test="tag != null">
                and TI.TAG = #{tag,jdbcType=INTEGER}
            </if>
            <if test="relatedId != null">
                and TI.RELATED_ID = #{relatedId,jdbcType=INTEGER}
            </if>
            <if test="afterSalesId != null">
                and TI.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                and TI.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
            </if>
            <if test="invoiceType != null">
                and TI.INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER}
            </if>
            <if test="ratio != null">
                and TI.RATIO = #{ratio,jdbcType=DECIMAL}
            </if>
            <if test="colorType != null">
                and TI.COLOR_TYPE = #{colorType,jdbcType=INTEGER}
            </if>
            <if test="amount != null">
                and TI.AMOUNT = #{amount,jdbcType=DECIMAL}
            </if>
            <if test="isEnable != null">
                and TI.IS_ENABLE = #{isEnable,jdbcType=INTEGER}
            </if>
            <if test="validUserid != null">
                and TI.VALID_USERID = #{validUserid,jdbcType=INTEGER}
            </if>
            <if test="validStatus != null">
                and TI.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
            </if>
            <if test="validTime != null">
                and TI.VALID_TIME = #{validTime,jdbcType=BIGINT}
            </if>
            <if test="validComments != null and validComments != ''">
                and TI.VALID_COMMENTS = #{validComments,jdbcType=VARCHAR}
            </if>
            <if test="invoicePrintStatus != null">
                and TI.INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=INTEGER}
            </if>
            <if test="invoiceCancelStatus != null">
                and TI.INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=INTEGER}
            </if>
            <if test="expressId != null">
                and TI.EXPRESS_ID = #{expressId,jdbcType=INTEGER}
            </if>
            <if test="isAuth != null">
                and TI.IS_AUTH = #{isAuth,jdbcType=INTEGER}
            </if>
            <if test="isMonthAuth != null">
                and TI.IS_MONTH_AUTH = #{isMonthAuth,jdbcType=INTEGER}
            </if>
            <if test="authTime != null">
                and TI.AUTH_TIME = #{authTime,jdbcType=BIGINT}
            </if>
            <if test="addTime != null">
                and TI.ADD_TIME = #{addTime,jdbcType=BIGINT}
            </if>
            <if test="creator != null">
                and TI.CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="modTime != null">
                and TI.MOD_TIME = #{modTime,jdbcType=BIGINT}
            </if>
            <if test="updater != null">
                and TI.UPDATER = #{updater,jdbcType=INTEGER}
            </if>
            <if test="invoiceApplyId != null">
                and TI.INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
            </if>
            <if test="sendTime != null">
                and TI.SEND_TIME = #{sendTime,jdbcType=TIMESTAMP}
            </if>
            <if test="afterExpressId != null">
                and TI.AFTER_EXPRESS_ID = #{afterExpressId,jdbcType=INTEGER}
            </if>
            <if test="ossFileUrl != null and ossFileUrl != ''">
                and TI.OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR}
            </if>
            <if test="resourceId != null and resourceId != ''">
                and TI.RESOURCE_ID = #{resourceId,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFrom != null">
                and TI.INVOICE_FROM = #{invoiceFrom,jdbcType=TINYINT}
            </if>
            <if test="hxInvoiceId != null">
                and TI.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
            </if>
            <if test="authMode != null">
                and TI.AUTH_MODE = #{authMode,jdbcType=INTEGER}
            </if>
            <if test="isAfterBuyorderOnly != null">
                and TI.IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly}
            </if>
            <if test="authFailReason != null and authFailReason != ''">
                and TI.AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR}
            </if>
            <if test="isAuthing != null">
                and TI.IS_AUTHING = #{isAuthing,jdbcType=INTEGER}
            </if>
            <if test="colorComplementType != null">
                and TI.COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null and endTime != null">
                and ((TI.VALID_TIME <![CDATA[>=]]> #{beginTime} and TI.VALID_TIME <![CDATA[<=]]> #{endTime} )
                or ( TI.AUTH_TIME <![CDATA[>=]]> #{beginTime} and TI.AUTH_TIME <![CDATA[<=]]> #{endTime})
                or ( TI.MOD_TIME <![CDATA[>=]]> #{beginTime} and TI.MOD_TIME <![CDATA[<=]]> #{endTime})
                )
            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>


    <select id="saleOrderAfterSaleReceiveFeeInvoice" resultMap="BaseResultMap">
        select TI.INVOICE_ID,
               TI.COMPANY_ID,
               INVOICE_CODE,
               INVOICE_FLOW,
               INVOICE_PROPERTY,
               INVOICE_HREF,
               TI.TYPE,
               TAG,
               TI.RELATED_ID,
               TAS.AFTER_SALES_ID,
               INVOICE_NO,
               TI.INVOICE_TYPE,
               COLOR_TYPE,
               TI.AMOUNT,
               TI.IS_ENABLE,
               VALID_USERID,
               TI.VALID_STATUS,
               TI.VALID_TIME,
               VALID_COMMENTS,
               INVOICE_PRINT_STATUS,
               INVOICE_CANCEL_STATUS,
               EXPRESS_ID,
               IS_AUTH,
               IS_MONTH_AUTH,
               AUTH_TIME,
               TI.ADD_TIME,
               TI.CREATOR,
               TI.MOD_TIME,
               TI.UPDATER,
               TI.IS_AFTER_BUYORDER_ONLY,
               INVOICE_APPLY_ID,
               SEND_TIME,
               AFTER_EXPRESS_ID,
               OSS_FILE_URL,
               RESOURCE_ID,
               INVOICE_FROM,
               HX_INVOICE_ID,
               AUTH_MODE,
               AUTH_FAIL_REASON,
               IS_AUTHING,
               COLOR_COMPLEMENT_TYPE,
               TRS.TRADER_CUSTOMER_ID,
               TSOD.COMMENTS ratio,
               TSOD.TITLE    invoiceTypeName
        from T_INVOICE TI
                 LEFT JOIN T_INVOICE_VOUCHER TIV ON TIV.INVOICE_ID = TI.INVOICE_ID and TIV.IS_DELETE = 0
                 LEFT JOIN T_AFTER_SALES TAS on TAS.AFTER_SALES_ID = TI.RELATED_ID
                 LEFT JOIN T_SALEORDER TS on TAS.ORDER_ID = TS.SALEORDER_ID
                 LEFT JOIN T_TRADER_CUSTOMER TRS on TRS.TRADER_ID = TS.TRADER_ID
                 LEFT JOIN T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE
        <where>
            TAS.SUBJECT_TYPE in (535, 537)
              and TI.TYPE = 504
            <if test="invoiceId != null">
                and TI.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
            </if>
            <if test="companyId != null">
                and TI.COMPANY_ID = #{companyId,jdbcType=INTEGER}
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                and TI.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFlow != null and invoiceFlow != ''">
                and TI.INVOICE_FLOW = #{invoiceFlow,jdbcType=VARCHAR}
            </if>
            <if test="invoiceProperty != null">
                and TI.INVOICE_PROPERTY = #{invoiceProperty,jdbcType=INTEGER}
            </if>
            <if test="invoiceHref != null and invoiceHref != ''">
                and TI.INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and TI.`TYPE` = #{type,jdbcType=INTEGER}
            </if>
            <if test="tag != null">
                and TI.TAG = #{tag,jdbcType=INTEGER}
            </if>
            <if test="relatedId != null">
                and TI.RELATED_ID = #{relatedId,jdbcType=INTEGER}
            </if>
            <if test="afterSalesId != null">
                and TI.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                and TI.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
            </if>
            <if test="invoiceType != null">
                and TI.INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER}
            </if>
            <if test="ratio != null">
                and TI.RATIO = #{ratio,jdbcType=DECIMAL}
            </if>
            <if test="colorType != null">
                and TI.COLOR_TYPE = #{colorType,jdbcType=INTEGER}
            </if>
            <if test="amount != null">
                and TI.AMOUNT = #{amount,jdbcType=DECIMAL}
            </if>
            <if test="isEnable != null">
                and TI.IS_ENABLE = #{isEnable,jdbcType=INTEGER}
            </if>
            <if test="validUserid != null">
                and TI.VALID_USERID = #{validUserid,jdbcType=INTEGER}
            </if>
            <if test="validStatus != null">
                and TI.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
            </if>
            <if test="validTime != null">
                and TI.VALID_TIME = #{validTime,jdbcType=BIGINT}
            </if>
            <if test="validComments != null and validComments != ''">
                and TI.VALID_COMMENTS = #{validComments,jdbcType=VARCHAR}
            </if>
            <if test="invoicePrintStatus != null">
                and TI.INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=INTEGER}
            </if>
            <if test="invoiceCancelStatus != null">
                and TI.INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=INTEGER}
            </if>
            <if test="expressId != null">
                and TI.EXPRESS_ID = #{expressId,jdbcType=INTEGER}
            </if>
            <if test="isAuth != null">
                and TI.IS_AUTH = #{isAuth,jdbcType=INTEGER}
            </if>
            <if test="isMonthAuth != null">
                and TI.IS_MONTH_AUTH = #{isMonthAuth,jdbcType=INTEGER}
            </if>
            <if test="authTime != null">
                and TI.AUTH_TIME = #{authTime,jdbcType=BIGINT}
            </if>
            <if test="addTime != null">
                and TI.ADD_TIME = #{addTime,jdbcType=BIGINT}
            </if>
            <if test="creator != null">
                and TI.CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="modTime != null">
                and TI.MOD_TIME = #{modTime,jdbcType=BIGINT}
            </if>
            <if test="updater != null">
                and TI.UPDATER = #{updater,jdbcType=INTEGER}
            </if>
            <if test="invoiceApplyId != null">
                and TI.INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
            </if>
            <if test="sendTime != null">
                and TI.SEND_TIME = #{sendTime,jdbcType=TIMESTAMP}
            </if>
            <if test="afterExpressId != null">
                and TI.AFTER_EXPRESS_ID = #{afterExpressId,jdbcType=INTEGER}
            </if>
            <if test="ossFileUrl != null and ossFileUrl != ''">
                and TI.OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR}
            </if>
            <if test="resourceId != null and resourceId != ''">
                and TI.RESOURCE_ID = #{resourceId,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFrom != null">
                and TI.INVOICE_FROM = #{invoiceFrom,jdbcType=TINYINT}
            </if>
            <if test="hxInvoiceId != null">
                and TI.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
            </if>
            <if test="authMode != null">
                and TI.AUTH_MODE = #{authMode,jdbcType=INTEGER}
            </if>
            <if test="isAfterBuyorderOnly != null">
                and TI.IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly}
            </if>
            <if test="authFailReason != null and authFailReason != ''">
                and TI.AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR}
            </if>
            <if test="isAuthing != null">
                and TI.IS_AUTHING = #{isAuthing,jdbcType=INTEGER}
            </if>
            <if test="colorComplementType != null">
                and TI.COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null">
                and TI.ADD_TIME <![CDATA[>=]]> #{beginTime}
            </if>
            <if test="endTime != null">
                and TI.ADD_TIME <![CDATA[<=]]> #{endTime}
            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="saleOrderAfterSalePayExpenseReader" resultMap="BaseResultMap">
        select TI.INVOICE_ID,
               TI.COMPANY_ID,
               INVOICE_CODE,
               INVOICE_FLOW,
               INVOICE_PROPERTY,
               INVOICE_HREF,
               TI.TYPE,
               TAG,
               TI.RELATED_ID,
               TAS.AFTER_SALES_ID,
               INVOICE_NO,
               TI.INVOICE_TYPE,
               COLOR_TYPE,
               TI.AMOUNT,
               TI.IS_ENABLE,
               VALID_USERID,
               TI.VALID_STATUS,
               TI.VALID_TIME,
               VALID_COMMENTS,
               INVOICE_PRINT_STATUS,
               INVOICE_CANCEL_STATUS,
               EXPRESS_ID,
               IS_AUTH,
               IS_MONTH_AUTH,
               AUTH_TIME,
               TI.ADD_TIME,
               TI.CREATOR,
               TI.MOD_TIME,
               TI.UPDATER,
               TI.IS_AFTER_BUYORDER_ONLY,
               INVOICE_APPLY_ID,
               SEND_TIME,
               AFTER_EXPRESS_ID,
               OSS_FILE_URL,
               RESOURCE_ID,
               INVOICE_FROM,
               HX_INVOICE_ID,
               AUTH_MODE,
               AUTH_FAIL_REASON,
               IS_AUTHING,
               COLOR_COMPLEMENT_TYPE,
               TTS.TRADER_SUPPLIER_ID,
               TSOD.COMMENTS ratio,
               TSOD.TITLE    invoiceTypeName
        from T_INVOICE TI
                 LEFT JOIN T_INVOICE_VOUCHER TIV ON TIV.INVOICE_ID = TI.INVOICE_ID and TIV.IS_DELETE = 0
                 LEFT JOIN T_AFTER_SALES TAS on TAS.AFTER_SALES_ID = TI.RELATED_ID
                 LEFT JOIN T_SALEORDER TS on TAS.ORDER_ID = TS.SALEORDER_ID
                 LEFT JOIN T_TRADER_SUPPLIER TTS on TTS.TRADER_ID = TS.TRADER_ID
                 LEFT JOIN T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE
        <where>
            TAS.SUBJECT_TYPE in (535, 537)
              and TI.TYPE = 504
            <if test="invoiceId != null">
                and TI.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
            </if>
            <if test="companyId != null">
                and TI.COMPANY_ID = #{companyId,jdbcType=INTEGER}
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                and TI.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFlow != null and invoiceFlow != ''">
                and TI.INVOICE_FLOW = #{invoiceFlow,jdbcType=VARCHAR}
            </if>
            <if test="invoiceProperty != null">
                and TI.INVOICE_PROPERTY = #{invoiceProperty,jdbcType=INTEGER}
            </if>
            <if test="invoiceHref != null and invoiceHref != ''">
                and TI.INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and TI.`TYPE` = #{type,jdbcType=INTEGER}
            </if>
            <if test="tag != null">
                and TI.TAG = #{tag,jdbcType=INTEGER}
            </if>
            <if test="relatedId != null">
                and TI.RELATED_ID = #{relatedId,jdbcType=INTEGER}
            </if>
            <if test="afterSalesId != null">
                and TI.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                and TI.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
            </if>
            <if test="invoiceType != null">
                and TI.INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER}
            </if>
            <if test="ratio != null">
                and TI.RATIO = #{ratio,jdbcType=DECIMAL}
            </if>
            <if test="colorType != null">
                and TI.COLOR_TYPE = #{colorType,jdbcType=INTEGER}
            </if>
            <if test="amount != null">
                and TI.AMOUNT = #{amount,jdbcType=DECIMAL}
            </if>
            <if test="isEnable != null">
                and TI.IS_ENABLE = #{isEnable,jdbcType=INTEGER}
            </if>
            <if test="validUserid != null">
                and TI.VALID_USERID = #{validUserid,jdbcType=INTEGER}
            </if>
            <if test="validStatus != null">
                and TI.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
            </if>
            <if test="validTime != null">
                and TI.VALID_TIME = #{validTime,jdbcType=BIGINT}
            </if>
            <if test="validComments != null and validComments != ''">
                and TI.VALID_COMMENTS = #{validComments,jdbcType=VARCHAR}
            </if>
            <if test="invoicePrintStatus != null">
                and TI.INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=INTEGER}
            </if>
            <if test="invoiceCancelStatus != null">
                and TI.INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=INTEGER}
            </if>
            <if test="expressId != null">
                and TI.EXPRESS_ID = #{expressId,jdbcType=INTEGER}
            </if>
            <if test="isAuth != null">
                and TI.IS_AUTH = #{isAuth,jdbcType=INTEGER}
            </if>
            <if test="isMonthAuth != null">
                and TI.IS_MONTH_AUTH = #{isMonthAuth,jdbcType=INTEGER}
            </if>
            <if test="authTime != null">
                and TI.AUTH_TIME = #{authTime,jdbcType=BIGINT}
            </if>
            <if test="addTime != null">
                and TI.ADD_TIME = #{addTime,jdbcType=BIGINT}
            </if>
            <if test="creator != null">
                and TI.CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="modTime != null">
                and TI.MOD_TIME = #{modTime,jdbcType=BIGINT}
            </if>
            <if test="updater != null">
                and TI.UPDATER = #{updater,jdbcType=INTEGER}
            </if>
            <if test="invoiceApplyId != null">
                and TI.INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
            </if>
            <if test="sendTime != null">
                and TI.SEND_TIME = #{sendTime,jdbcType=TIMESTAMP}
            </if>
            <if test="afterExpressId != null">
                and TI.AFTER_EXPRESS_ID = #{afterExpressId,jdbcType=INTEGER}
            </if>
            <if test="ossFileUrl != null and ossFileUrl != ''">
                and TI.OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR}
            </if>
            <if test="resourceId != null and resourceId != ''">
                and TI.RESOURCE_ID = #{resourceId,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFrom != null">
                and TI.INVOICE_FROM = #{invoiceFrom,jdbcType=TINYINT}
            </if>
            <if test="hxInvoiceId != null">
                and TI.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
            </if>
            <if test="authMode != null">
                and TI.AUTH_MODE = #{authMode,jdbcType=INTEGER}
            </if>
            <if test="isAfterBuyorderOnly != null">
                and TI.IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly}
            </if>
            <if test="authFailReason != null and authFailReason != ''">
                and TI.AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR}
            </if>
            <if test="isAuthing != null">
                and TI.IS_AUTHING = #{isAuthing,jdbcType=INTEGER}
            </if>
            <if test="colorComplementType != null">
                and TI.COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null and endTime != null">
                and (
                (TI.VALID_TIME <![CDATA[>=]]> #{beginTime} and TI.VALID_TIME <![CDATA[<=]]> #{endTime})
                or (TI.ADD_TIME <![CDATA[>=]]> #{beginTime} and TI.ADD_TIME <![CDATA[<=]]> #{endTime})
                or (TI.MOD_TIME <![CDATA[>=]]> #{beginTime} and TI.MOD_TIME <![CDATA[<=]]> #{endTime})
                )
            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="saleOrderAfterSaleOutPutFeeInvoice" resultMap="BaseResultMap">
        select TI.INVOICE_ID,
               TI.COMPANY_ID,
               INVOICE_CODE,
               INVOICE_FLOW,
               INVOICE_PROPERTY,
               INVOICE_HREF,
               TI.TYPE,
               TAG,
               TI.RELATED_ID,
               TAS.AFTER_SALES_ID,
               INVOICE_NO,
               TI.INVOICE_TYPE,
               COLOR_TYPE,
               TI.AMOUNT,
               TI.IS_ENABLE,
               VALID_USERID,
               TI.VALID_STATUS,
               TI.VALID_TIME,
               VALID_COMMENTS,
               INVOICE_PRINT_STATUS,
               INVOICE_CANCEL_STATUS,
               EXPRESS_ID,
               IS_AUTH,
               IS_MONTH_AUTH,
               AUTH_TIME,
               TI.ADD_TIME,
               TI.CREATOR,
               TI.MOD_TIME,
               TI.UPDATER,
               TI.IS_AFTER_BUYORDER_ONLY,
               INVOICE_APPLY_ID,
               SEND_TIME,
               AFTER_EXPRESS_ID,
               OSS_FILE_URL,
               RESOURCE_ID,
               INVOICE_FROM,
               HX_INVOICE_ID,
               AUTH_MODE,
               AUTH_FAIL_REASON,
               IS_AUTHING,
               COLOR_COMPLEMENT_TYPE,
               TRS.TRADER_CUSTOMER_ID,
               TSOD.COMMENTS ratio,
               TSOD.TITLE    invoiceTypeName
        from T_INVOICE TI
                 LEFT JOIN T_AFTER_SALES TAS on TAS.AFTER_SALES_ID = TI.RELATED_ID
                 LEFT JOIN T_SALEORDER TS on TAS.ORDER_ID = TS.SALEORDER_ID
                 LEFT JOIN T_TRADER_CUSTOMER TRS on TRS.TRADER_ID = TS.TRADER_ID
                 LEFT JOIN T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE
        <where>
            and TAS.SUBJECT_TYPE in (535, 537)
                  and TI.TYPE = 504
            <if test="invoiceId != null">
                and TI.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
            </if>
            <if test="companyId != null">
                and TI.COMPANY_ID = #{companyId,jdbcType=INTEGER}
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                and TI.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFlow != null and invoiceFlow != ''">
                and TI.INVOICE_FLOW = #{invoiceFlow,jdbcType=VARCHAR}
            </if>
            <if test="invoiceProperty != null">
                and TI.INVOICE_PROPERTY = #{invoiceProperty,jdbcType=INTEGER}
            </if>
            <if test="invoiceHref != null and invoiceHref != ''">
                and TI.INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and TI.`TYPE` = #{type,jdbcType=INTEGER}
            </if>
            <if test="tag != null">
                and TI.TAG = #{tag,jdbcType=INTEGER}
            </if>
            <if test="relatedId != null">
                and TI.RELATED_ID = #{relatedId,jdbcType=INTEGER}
            </if>
            <if test="afterSalesId != null">
                and TI.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                and TI.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
            </if>
            <if test="invoiceType != null">
                and TI.INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER}
            </if>
            <if test="ratio != null">
                and TI.RATIO = #{ratio,jdbcType=DECIMAL}
            </if>
            <if test="colorType != null">
                and TI.COLOR_TYPE = #{colorType,jdbcType=INTEGER}
            </if>
            <if test="amount != null">
                and TI.AMOUNT = #{amount,jdbcType=DECIMAL}
            </if>
            <if test="isEnable != null">
                and TI.IS_ENABLE = #{isEnable,jdbcType=INTEGER}
            </if>
            <if test="validUserid != null">
                and TI.VALID_USERID = #{validUserid,jdbcType=INTEGER}
            </if>
            <if test="validStatus != null">
                and TI.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
            </if>
            <if test="validTime != null">
                and TI.VALID_TIME = #{validTime,jdbcType=BIGINT}
            </if>
            <if test="validComments != null and validComments != ''">
                and TI.VALID_COMMENTS = #{validComments,jdbcType=VARCHAR}
            </if>
            <if test="invoicePrintStatus != null">
                and TI.INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=INTEGER}
            </if>
            <if test="invoiceCancelStatus != null">
                and TI.INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=INTEGER}
            </if>
            <if test="expressId != null">
                and TI.EXPRESS_ID = #{expressId,jdbcType=INTEGER}
            </if>
            <if test="isAuth != null">
                and TI.IS_AUTH = #{isAuth,jdbcType=INTEGER}
            </if>
            <if test="isMonthAuth != null">
                and TI.IS_MONTH_AUTH = #{isMonthAuth,jdbcType=INTEGER}
            </if>
            <if test="authTime != null">
                and TI.AUTH_TIME = #{authTime,jdbcType=BIGINT}
            </if>
            <if test="addTime != null">
                and TI.ADD_TIME = #{addTime,jdbcType=BIGINT}
            </if>
            <if test="creator != null">
                and TI.CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="modTime != null">
                and TI.MOD_TIME = #{modTime,jdbcType=BIGINT}
            </if>
            <if test="updater != null">
                and TI.UPDATER = #{updater,jdbcType=INTEGER}
            </if>
            <if test="invoiceApplyId != null">
                and TI.INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
            </if>
            <if test="sendTime != null">
                and TI.SEND_TIME = #{sendTime,jdbcType=TIMESTAMP}
            </if>
            <if test="afterExpressId != null">
                and TI.AFTER_EXPRESS_ID = #{afterExpressId,jdbcType=INTEGER}
            </if>
            <if test="ossFileUrl != null and ossFileUrl != ''">
                and TI.OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR}
            </if>
            <if test="resourceId != null and resourceId != ''">
                and TI.RESOURCE_ID = #{resourceId,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFrom != null">
                and TI.INVOICE_FROM = #{invoiceFrom,jdbcType=TINYINT}
            </if>
            <if test="hxInvoiceId != null">
                and TI.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
            </if>
            <if test="authMode != null">
                and TI.AUTH_MODE = #{authMode,jdbcType=INTEGER}
            </if>
            <if test="isAfterBuyorderOnly != null">
                and TI.IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly}
            </if>
            <if test="authFailReason != null and authFailReason != ''">
                and TI.AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR}
            </if>
            <if test="isAuthing != null">
                and TI.IS_AUTHING = #{isAuthing,jdbcType=INTEGER}
            </if>
            <if test="colorComplementType != null">
                and TI.COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null">
                and TI.ADD_TIME <![CDATA[>=]]> #{beginTime}
            </if>
            <if test="endTime != null">
                and TI.ADD_TIME <![CDATA[<=]]> #{endTime}
            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>


    <select id="findSaleorderNoByRelatedId" resultType="java.lang.String">
        select TAS.ORDER_NO
        from T_INVOICE TI
                 left join T_AFTER_SALES TAS on TI.RELATED_ID = TAS.AFTER_SALES_ID
        where TI.RELATED_ID = #{relatedId,jdbcType=INTEGER}
        group by TAS.ORDER_NO
    </select>

    <select id="findAfterSaleOrderNoByAfterSaleId" resultType="java.lang.String">
        select TAS.AFTER_SALES_NO
        from T_INVOICE TI
                 left join T_AFTER_SALES TAS on TI.RELATED_ID = TAS.AFTER_SALES_ID
        where TI.RELATED_ID = #{afterSalesId,jdbcType=INTEGER}
        group by TAS.AFTER_SALES_NO
    </select>

    <select id="findSaleOrderBlueInvoiceBatch" resultMap="BaseResultMap">
        select TI.INVOICE_ID,
               TI.COMPANY_ID,
               TI.INVOICE_CODE,
               TI.INVOICE_FLOW,
               TI.INVOICE_PROPERTY,
               TI.INVOICE_HREF,
               TI.TYPE,
               TI.TAG,
               TI.RELATED_ID,
               TI.AFTER_SALES_ID,
               TI.INVOICE_NO,
               TI.INVOICE_TYPE,
               TI.COLOR_TYPE,
               TI.AMOUNT,
               TI.IS_ENABLE,
               TI.VALID_USERID,
               TI.VALID_STATUS,
               TI.VALID_TIME,
               TI.VALID_COMMENTS,
               TI.INVOICE_PRINT_STATUS,
               TI.INVOICE_CANCEL_STATUS,
               TI.EXPRESS_ID,
               TI.IS_AUTH,
               TI.IS_MONTH_AUTH,
               TI.AUTH_TIME,
               TI.ADD_TIME,
               TI.CREATOR,
               TI.MOD_TIME,
               TI.UPDATER,
               TI.IS_AFTER_BUYORDER_ONLY,
               TI.INVOICE_APPLY_ID,
               TI.SEND_TIME,
               TI.AFTER_EXPRESS_ID,
               TI.OSS_FILE_URL,
               TI.RESOURCE_ID,
               TI.INVOICE_FROM,
               TI.HX_INVOICE_ID,
               TI.AUTH_MODE,
               TI.AUTH_FAIL_REASON,
               TI.IS_AUTHING,
               TI.COLOR_COMPLEMENT_TYPE,
               TRC.TRADER_CUSTOMER_ID,
               TSOD.COMMENTS   AS ratio,
               TSOD.TITLE      AS invoiceTypeName,
               TS.SALEORDER_NO AS orderNo
        from T_INVOICE TI
                 LEFT JOIN T_SALEORDER TS on TS.SALEORDER_ID = TI.RELATED_ID
                 LEFT JOIN T_TRADER_CUSTOMER TRC on TRC.TRADER_ID = TS.TRADER_ID
                 LEFT JOIN T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE

        WHERE
            TI.COLOR_TYPE = 2
            and TI.IS_ENABLE = 1
            and TI.COMPANY_ID = 1
            and (
                 (TI.ADD_TIME <![CDATA[>=]]> #{beginTime} and TI.ADD_TIME <![CDATA[<=]]> #{endTime})
                or (TI.MOD_TIME <![CDATA[>=]]> #{beginTime} and TI.MOD_TIME <![CDATA[<=]]> #{endTime})
            )
            and TI.`TYPE` = #{type,jdbcType=INTEGER}
            and TI.RELATED_ID != 0
            GROUP BY TI.INVOICE_ID

        limit #{_pagesize} OFFSET #{_skiprows}

    </select>

    <select id="saleorderRedInvoiceFindByAll" resultMap="BaseResultMap">
        <!--@mbg.generated 税率 取 字典表信息 兼容发票表有的额没存税率 -->
        select TI.INVOICE_ID,
        TI.COMPANY_ID,
        INVOICE_CODE,
        INVOICE_FLOW,
        INVOICE_PROPERTY,
        INVOICE_HREF,
        TYPE,
        TAG,
        TI.RELATED_ID,
        AFTER_SALES_ID,
        INVOICE_NO,
        TI.INVOICE_TYPE,
        TI.IS_AFTER_BUYORDER_ONLY,
        TI.COLOR_TYPE,
        TI.AMOUNT,
        TI.IS_ENABLE,
        VALID_USERID,
        TI.VALID_STATUS,
        TI.VALID_TIME,
        VALID_COMMENTS,
        INVOICE_PRINT_STATUS,
        INVOICE_CANCEL_STATUS,
        EXPRESS_ID,
        IS_AUTH,
        IS_MONTH_AUTH,
        AUTH_TIME,
        TI.ADD_TIME,
        TI.CREATOR,
        TI.MOD_TIME,
        TI.UPDATER,
        INVOICE_APPLY_ID,
        SEND_TIME,
        AFTER_EXPRESS_ID,
        OSS_FILE_URL,
        RESOURCE_ID,
        INVOICE_FROM,
        HX_INVOICE_ID,
        AUTH_MODE,
        AUTH_FAIL_REASON,
        IS_AUTHING,
        COLOR_COMPLEMENT_TYPE,
        TTC.TRADER_CUSTOMER_ID,
        TSOD.COMMENTS ratio,
        TSOD.TITLE    invoiceTypeName
        from T_INVOICE TI
        LEFT JOIN T_SALEORDER TS on TS.SALEORDER_ID = TI.RELATED_ID
        LEFT JOIN T_TRADER_CUSTOMER TTC on TTC.TRADER_ID = TS.TRADER_ID
        LEFT JOIN T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE
        <where>
            <if test="invoiceId != null">
                and TI.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
            </if>
            <if test="companyId != null">
                and TI.COMPANY_ID = #{companyId,jdbcType=INTEGER}
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                and TI.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFlow != null and invoiceFlow != ''">
                and TI.INVOICE_FLOW = #{invoiceFlow,jdbcType=VARCHAR}
            </if>
            <if test="invoiceProperty != null">
                and TI.INVOICE_PROPERTY = #{invoiceProperty,jdbcType=INTEGER}
            </if>
            <if test="invoiceHref != null and invoiceHref != ''">
                and TI.INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and TI.`TYPE` = #{type,jdbcType=INTEGER}
            </if>
            <if test="tag != null">
                and TI.TAG = #{tag,jdbcType=INTEGER}
            </if>
            <if test="relatedId != null">
                and TI.RELATED_ID = #{relatedId,jdbcType=INTEGER}
            </if>
            <if test="afterSalesId != null">
                and TI.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                and TI.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
            </if>
            <if test="invoiceType != null">
                and TI.INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER}
            </if>
            <if test="ratio != null">
                and TI.RATIO = #{ratio,jdbcType=DECIMAL}
            </if>
            <if test="colorType != null">
                and TI.COLOR_TYPE = #{colorType,jdbcType=INTEGER}
            </if>
            <if test="amount != null">
                and TI.AMOUNT = #{amount,jdbcType=DECIMAL}
            </if>
            <if test="isAfterBuyorderOnly != null">
                and TI.IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly}
            </if>
            <if test="isEnable != null">
                and TI.IS_ENABLE = #{isEnable,jdbcType=INTEGER}
            </if>
            <if test="validUserid != null">
                and TI.VALID_USERID = #{validUserid,jdbcType=INTEGER}
            </if>
            <if test="validStatus != null">
                and TI.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
            </if>
            <if test="validTime != null">
                and TI.VALID_TIME = #{validTime,jdbcType=BIGINT}
            </if>
            <if test="validComments != null and validComments != ''">
                and TI.VALID_COMMENTS = #{validComments,jdbcType=VARCHAR}
            </if>
            <if test="invoicePrintStatus != null">
                and TI.INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=INTEGER}
            </if>
            <if test="invoiceCancelStatus != null">
                and TI.INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=INTEGER}
            </if>
            <if test="expressId != null">
                and TI.EXPRESS_ID = #{expressId,jdbcType=INTEGER}
            </if>
            <if test="isAuth != null">
                and TI.IS_AUTH = #{isAuth,jdbcType=INTEGER}
            </if>
            <if test="isMonthAuth != null">
                and TI.IS_MONTH_AUTH = #{isMonthAuth,jdbcType=INTEGER}
            </if>
            <if test="authTime != null">
                and TI.AUTH_TIME = #{authTime,jdbcType=BIGINT}
            </if>
            <if test="addTime != null">
                and TI.ADD_TIME = #{addTime,jdbcType=BIGINT}
            </if>
            <if test="creator != null">
                and TI.CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="modTime != null">
                and TI.MOD_TIME = #{modTime,jdbcType=BIGINT}
            </if>
            <if test="updater != null">
                and TI.UPDATER = #{updater,jdbcType=INTEGER}
            </if>
            <if test="invoiceApplyId != null">
                and TI.INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
            </if>
            <if test="sendTime != null">
                and TI.SEND_TIME = #{sendTime,jdbcType=TIMESTAMP}
            </if>
            <if test="afterExpressId != null">
                and TI.AFTER_EXPRESS_ID = #{afterExpressId,jdbcType=INTEGER}
            </if>
            <if test="ossFileUrl != null and ossFileUrl != ''">
                and TI.OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR}
            </if>
            <if test="resourceId != null and resourceId != ''">
                and TI.RESOURCE_ID = #{resourceId,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFrom != null">
                and TI.INVOICE_FROM = #{invoiceFrom,jdbcType=TINYINT}
            </if>
            <if test="hxInvoiceId != null">
                and TI.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
            </if>
            <if test="authMode != null">
                and TI.AUTH_MODE = #{authMode,jdbcType=INTEGER}
            </if>
            <if test="authFailReason != null and authFailReason != ''">
                and TI.AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR}
            </if>
            <if test="isAuthing != null">
                and TI.IS_AUTHING = #{isAuthing,jdbcType=INTEGER}
            </if>
            <if test="colorComplementType != null">
                and TI.COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null and endTime != null">
                and ((TI.VALID_TIME <![CDATA[>=]]> #{beginTime} and TI.VALID_TIME <![CDATA[<=]]> #{endTime} )
                or ( TI.AUTH_TIME <![CDATA[>=]]> #{beginTime} and TI.AUTH_TIME <![CDATA[<=]]> #{endTime})
                or ( TI.ADD_TIME <![CDATA[>=]]> #{beginTime} and TI.ADD_TIME <![CDATA[<=]]> #{endTime})
                or ( TI.MOD_TIME <![CDATA[>=]]> #{beginTime} and TI.MOD_TIME <![CDATA[<=]]> #{endTime})
                )

            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>


    <select id="findDeprecatedBlueInvoice" resultType="com.vedeng.erp.finance.dto.InvoiceDto">
        select TI2.*
        from T_INVOICE TI
                 left join T_INVOICE TI2 on TI.INVOICE_NO = TI2.INVOICE_NO
            and TI.INVOICE_CODE = TI2.INVOICE_CODE
            and TI.IS_ENABLE = 1
            and TI2.IS_ENABLE = 0
        where TI.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
          and TI2.INVOICE_ID is not null
    </select>


    <select id="getSaleOrderNoBySaleOrderId" resultType="java.lang.String">
        SELECT SALEORDER_NO
        FROM T_SALEORDER
        WHERE SALEORDER_ID = #{saleOrderId,jdbcType=INTEGER}
    </select>

    <select id="getAfterSalesNoByAfterSalesId" resultType="java.lang.String">
        SELECT AFTER_SALES_NO
        FROM T_AFTER_SALES
        WHERE AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    </select>

    <select id="queryAllNeedVirturalInvoice" resultMap="BaseResultMap">
        SELECT
        A.INVOICE_ID,A.INVOICE_NO,A.INVOICE_CODE,A.INVOICE_CODE,A.AFTER_SALES_ID,A.ADD_TIME
        FROM
        T_INVOICE A
        LEFT JOIN T_R_INVOICE_DETAIL_J_OPERATE_LOG B ON A.INVOICE_ID = B.INVOICE_ID AND B.SOURCE_TYPE = 0
        WHERE
        A.TYPE = 505
        AND A.COLOR_TYPE = #{colorType,jdbcType=INTEGER}
        AND A.TAG = #{tag,jdbcType=INTEGER}
        AND A.IS_ENABLE = #{isEnable,jdbcType=INTEGER}
        AND B.R_INVOICE_DETAIL_J_OPERATE_LOG_ID IS NULL
        <if test="beginTime != null">
            and A.ADD_TIME <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime != null">
            and A.ADD_TIME <![CDATA[<=]]> #{endTime}
        </if>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="getAfterSalesGoodsByInvoiceDetialId"
            resultType="com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesGoodsDto">
        select TID.* from T_AFTER_SALES_GOODS TASG left join T_INVOICE_DETAIL TID on TASG.AFTER_SALES_GOODS_ID = TID.DETAILGOODS_ID
        where TID.INVOICE_DETAIL_ID = #{invoiceDetailId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-03-27-->
    <select id="findByRelatedIdAndTypeAndColorType" resultType="java.lang.Integer">
        select
        INVOICE_ID
        from T_INVOICE
        where RELATED_ID=#{relatedId,jdbcType=INTEGER} and `TYPE`=#{type,jdbcType=INTEGER} and
        COLOR_TYPE=#{colorType,jdbcType=INTEGER} and IS_ENABLE = #{isEnable,jdbcType=INTEGER}
        and VALID_STATUS  = 1
        and COMPANY_ID = 1
    </select>

    <select id="saleOrderAfterSaleInputFeeReader" resultMap="BaseResultMap">
        select TI.INVOICE_ID,
               TI.COMPANY_ID,
               INVOICE_CODE,
               INVOICE_FLOW,
               INVOICE_PROPERTY,
               INVOICE_HREF,
               TI.TYPE,
               TAG,
               TI.RELATED_ID,
               TAS.AFTER_SALES_ID,
               INVOICE_NO,
               TI.INVOICE_TYPE,
               COLOR_TYPE,
               TI.AMOUNT,
               TI.IS_ENABLE,
               VALID_USERID,
               TI.VALID_STATUS,
               TI.VALID_TIME,
               VALID_COMMENTS,
               INVOICE_PRINT_STATUS,
               INVOICE_CANCEL_STATUS,
               EXPRESS_ID,
               IS_AUTH,
               IS_MONTH_AUTH,
               AUTH_TIME,
               TI.ADD_TIME,
               TI.CREATOR,
               TI.MOD_TIME,
               TI.UPDATER,
               TI.IS_AFTER_BUYORDER_ONLY,
               INVOICE_APPLY_ID,
               SEND_TIME,
               AFTER_EXPRESS_ID,
               OSS_FILE_URL,
               RESOURCE_ID,
               INVOICE_FROM,
               HX_INVOICE_ID,
               AUTH_MODE,
               AUTH_FAIL_REASON,
               IS_AUTHING,
               COLOR_COMPLEMENT_TYPE,
               TTS.TRADER_SUPPLIER_ID,
               TSOD.COMMENTS ratio,
               TSOD.TITLE    invoiceTypeName
        from T_INVOICE TI
                 LEFT JOIN T_AFTER_SALES TAS on TAS.AFTER_SALES_ID = TI.RELATED_ID
                 LEFT JOIN T_SALEORDER TS on TAS.ORDER_ID = TS.SALEORDER_ID
                 LEFT JOIN T_TRADER_SUPPLIER TTS on TTS.TRADER_ID = TS.TRADER_ID
                 LEFT JOIN T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE
        <where>
            and TAS.SUBJECT_TYPE in (535, 537)
                  and TI.TYPE = 504
            <if test="invoiceId != null">
                and TI.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
            </if>
            <if test="companyId != null">
                and TI.COMPANY_ID = #{companyId,jdbcType=INTEGER}
            </if>
            <if test="invoiceCode != null and invoiceCode != ''">
                and TI.INVOICE_CODE = #{invoiceCode,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFlow != null and invoiceFlow != ''">
                and TI.INVOICE_FLOW = #{invoiceFlow,jdbcType=VARCHAR}
            </if>
            <if test="invoiceProperty != null">
                and TI.INVOICE_PROPERTY = #{invoiceProperty,jdbcType=INTEGER}
            </if>
            <if test="invoiceHref != null and invoiceHref != ''">
                and TI.INVOICE_HREF = #{invoiceHref,jdbcType=VARCHAR}
            </if>
            <if test="type != null">
                and TI.`TYPE` = #{type,jdbcType=INTEGER}
            </if>
            <if test="tag != null">
                and TI.TAG = #{tag,jdbcType=INTEGER}
            </if>
            <if test="relatedId != null">
                and TI.RELATED_ID = #{relatedId,jdbcType=INTEGER}
            </if>
            <if test="afterSalesId != null">
                and TI.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
            </if>
            <if test="invoiceNo != null and invoiceNo != ''">
                and TI.INVOICE_NO = #{invoiceNo,jdbcType=VARCHAR}
            </if>
            <if test="invoiceType != null">
                and TI.INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER}
            </if>
            <if test="ratio != null">
                and TI.RATIO = #{ratio,jdbcType=DECIMAL}
            </if>
            <if test="colorType != null">
                and TI.COLOR_TYPE = #{colorType,jdbcType=INTEGER}
            </if>
            <if test="amount != null">
                and TI.AMOUNT = #{amount,jdbcType=DECIMAL}
            </if>
            <if test="isEnable != null">
                and TI.IS_ENABLE = #{isEnable,jdbcType=INTEGER}
            </if>
            <if test="validUserid != null">
                and TI.VALID_USERID = #{validUserid,jdbcType=INTEGER}
            </if>
            <if test="validStatus != null">
                and TI.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
            </if>
            <if test="validTime != null">
                and TI.VALID_TIME = #{validTime,jdbcType=BIGINT}
            </if>
            <if test="validComments != null and validComments != ''">
                and TI.VALID_COMMENTS = #{validComments,jdbcType=VARCHAR}
            </if>
            <if test="invoicePrintStatus != null">
                and TI.INVOICE_PRINT_STATUS = #{invoicePrintStatus,jdbcType=INTEGER}
            </if>
            <if test="invoiceCancelStatus != null">
                and TI.INVOICE_CANCEL_STATUS = #{invoiceCancelStatus,jdbcType=INTEGER}
            </if>
            <if test="expressId != null">
                and TI.EXPRESS_ID = #{expressId,jdbcType=INTEGER}
            </if>
            <if test="isAuth != null">
                and TI.IS_AUTH = #{isAuth,jdbcType=INTEGER}
            </if>
            <if test="isMonthAuth != null">
                and TI.IS_MONTH_AUTH = #{isMonthAuth,jdbcType=INTEGER}
            </if>
            <if test="authTime != null">
                and TI.AUTH_TIME = #{authTime,jdbcType=BIGINT}
            </if>
            <if test="addTime != null">
                and TI.ADD_TIME = #{addTime,jdbcType=BIGINT}
            </if>
            <if test="creator != null">
                and TI.CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="modTime != null">
                and TI.MOD_TIME = #{modTime,jdbcType=BIGINT}
            </if>
            <if test="updater != null">
                and TI.UPDATER = #{updater,jdbcType=INTEGER}
            </if>
            <if test="invoiceApplyId != null">
                and TI.INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
            </if>
            <if test="sendTime != null">
                and TI.SEND_TIME = #{sendTime,jdbcType=TIMESTAMP}
            </if>
            <if test="afterExpressId != null">
                and TI.AFTER_EXPRESS_ID = #{afterExpressId,jdbcType=INTEGER}
            </if>
            <if test="ossFileUrl != null and ossFileUrl != ''">
                and TI.OSS_FILE_URL = #{ossFileUrl,jdbcType=VARCHAR}
            </if>
            <if test="resourceId != null and resourceId != ''">
                and TI.RESOURCE_ID = #{resourceId,jdbcType=VARCHAR}
            </if>
            <if test="invoiceFrom != null">
                and TI.INVOICE_FROM = #{invoiceFrom,jdbcType=TINYINT}
            </if>
            <if test="hxInvoiceId != null">
                and TI.HX_INVOICE_ID = #{hxInvoiceId,jdbcType=INTEGER}
            </if>
            <if test="authMode != null">
                and TI.AUTH_MODE = #{authMode,jdbcType=INTEGER}
            </if>
            <if test="isAfterBuyorderOnly != null">
                and TI.IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly}
            </if>
            <if test="authFailReason != null and authFailReason != ''">
                and TI.AUTH_FAIL_REASON = #{authFailReason,jdbcType=VARCHAR}
            </if>
            <if test="isAuthing != null">
                and TI.IS_AUTHING = #{isAuthing,jdbcType=INTEGER}
            </if>
            <if test="colorComplementType != null">
                and TI.COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=INTEGER}
            </if>
            <if test="beginTime != null and endTime != null">
                and (
                (TI.VALID_TIME <![CDATA[>=]]> #{beginTime} and TI.VALID_TIME <![CDATA[<=]]> #{endTime})
                or (TI.ADD_TIME <![CDATA[>=]]> #{beginTime} and TI.ADD_TIME <![CDATA[<=]]> #{endTime})
                or (TI.MOD_TIME <![CDATA[>=]]> #{beginTime} and TI.MOD_TIME <![CDATA[<=]]> #{endTime})
                )
            </if>
        </where>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>


    <select id="findAdvanceSaleOrderReceivableBatch" resultMap="BaseResultMap">
        select TI.INVOICE_ID,
               TI.COMPANY_ID,
               TI.INVOICE_CODE,
               TI.INVOICE_FLOW,
               TI.INVOICE_PROPERTY,
               TI.INVOICE_HREF,
               TI.TYPE,
               TI.TAG,
               TI.RELATED_ID,
               TI.AFTER_SALES_ID,
               TI.INVOICE_NO,
               TI.INVOICE_TYPE,
               TI.COLOR_TYPE,
               TI.AMOUNT,
               TI.IS_ENABLE,
               TI.VALID_USERID,
               TI.VALID_STATUS,
               TI.VALID_TIME,
               TI.VALID_COMMENTS,
               TI.INVOICE_PRINT_STATUS,
               TI.INVOICE_CANCEL_STATUS,
               TI.EXPRESS_ID,
               TI.IS_AUTH,
               TI.IS_MONTH_AUTH,
               TI.AUTH_TIME,
               TI.ADD_TIME,
               TI.CREATOR,
               TI.MOD_TIME,
               TI.UPDATER,
               TI.IS_AFTER_BUYORDER_ONLY,
               TI.INVOICE_APPLY_ID,
               TI.SEND_TIME,
               TI.AFTER_EXPRESS_ID,
               TI.OSS_FILE_URL,
               TI.RESOURCE_ID,
               TI.INVOICE_FROM,
               TI.HX_INVOICE_ID,
               TI.AUTH_MODE,
               TI.AUTH_FAIL_REASON,
               TI.IS_AUTHING,
               TI.COLOR_COMPLEMENT_TYPE,
               TRC.TRADER_CUSTOMER_ID,
               TSOD.COMMENTS   AS ratio,
               TSOD.TITLE      AS invoiceTypeName,
               TS.SALEORDER_NO AS orderNo
        from T_INVOICE_APPLY TIA
                 LEFT JOIN T_INVOICE TI ON TIA.INVOICE_APPLY_ID = TI.INVOICE_APPLY_ID
                 LEFT JOIN T_SALEORDER TS on TS.SALEORDER_ID = TI.RELATED_ID
                 LEFT JOIN T_TRADER_CUSTOMER TRC on TRC.TRADER_ID = TS.TRADER_ID
                 LEFT JOIN T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE
                 LEFT JOIN KING_DEE_RECEIVE_COMMON KC ON KC.F_QZOK_BDDJT_ID = cast(TI.INVOICE_ID AS CHAR)
        WHERE TI.COLOR_TYPE = 2
          and TI.IS_ENABLE = 1
          and TI.COMPANY_ID = 1
          AND TIA.IS_ADVANCE = 1
        <if test="beginTime != null">
            and TI.ADD_TIME <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime != null">
            and TI.ADD_TIME <![CDATA[<=]]> #{endTime}
        </if>
        and TI.`TYPE` = 505
        and TI.RELATED_ID != 0
        and TIA.TYPE = 505
        and TIA.VALID_STATUS = 1
        and TIA.ADVANCE_VALID_STATUS = 1
        AND KC.F_QZOK_BDDJT_ID IS NULL
        and TI.ADD_TIME > 1609430400000
        GROUP BY TI.INVOICE_ID

        limit #{_pagesize} OFFSET #{_skiprows}
    </select>


    <select id="findAdvanceSaleOrderBlueInvoiceBatch" resultMap="BaseResultMap">
        select TI.INVOICE_ID,
               TI.COMPANY_ID,
               TI.INVOICE_CODE,
               TI.INVOICE_FLOW,
               TI.INVOICE_PROPERTY,
               TI.INVOICE_HREF,
               TI.TYPE,
               TI.TAG,
               TI.RELATED_ID,
               TI.AFTER_SALES_ID,
               TI.INVOICE_NO,
               TI.INVOICE_TYPE,
               TI.COLOR_TYPE,
               TI.AMOUNT,
               TI.IS_ENABLE,
               TI.VALID_USERID,
               TI.VALID_STATUS,
               TI.VALID_TIME,
               TI.VALID_COMMENTS,
               TI.INVOICE_PRINT_STATUS,
               TI.INVOICE_CANCEL_STATUS,
               TI.EXPRESS_ID,
               TI.IS_AUTH,
               TI.IS_MONTH_AUTH,
               TI.AUTH_TIME,
               TI.ADD_TIME,
               TI.CREATOR,
               TI.MOD_TIME,
               TI.UPDATER,
               TI.IS_AFTER_BUYORDER_ONLY,
               TI.INVOICE_APPLY_ID,
               TI.SEND_TIME,
               TI.AFTER_EXPRESS_ID,
               TI.OSS_FILE_URL,
               TI.RESOURCE_ID,
               TI.INVOICE_FROM,
               TI.HX_INVOICE_ID,
               TI.AUTH_MODE,
               TI.AUTH_FAIL_REASON,
               TI.IS_AUTHING,
               TI.COLOR_COMPLEMENT_TYPE,
               TRC.TRADER_CUSTOMER_ID,
               TSOD.COMMENTS   AS ratio,
               TSOD.TITLE      AS invoiceTypeName,
               TS.SALEORDER_NO AS orderNo
        from T_INVOICE_APPLY TIA
                 LEFT JOIN T_INVOICE TI ON TIA.INVOICE_APPLY_ID = TI.INVOICE_APPLY_ID
                 LEFT JOIN T_SALEORDER TS on TS.SALEORDER_ID = TI.RELATED_ID
                 LEFT JOIN T_TRADER_CUSTOMER TRC on TRC.TRADER_ID = TS.TRADER_ID
                 LEFT JOIN T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE
                 LEFT JOIN
             KING_DEE_SALES_VAT_PLAIN_INVOICE KDSVPI
             ON KDSVPI.F_QZOK_BDDJTID = cast(TI.INVOICE_ID AS CHAR)
                 LEFT JOIN
             KING_DEE_SALES_VAT_SPECIAL_INVOICE KDSVSI
             ON KDSVSI.F_QZOK_BDDJTID = cast(TI.INVOICE_ID AS CHAR)
        WHERE TI.COLOR_TYPE = 2
          and TI.IS_ENABLE = 1
          and TI.COMPANY_ID = 1
          AND TIA.IS_ADVANCE = 1
        <if test="beginTime != null">
            and TI.ADD_TIME <![CDATA[>=]]> #{beginTime}
        </if>
        <if test="endTime != null">
            and TI.ADD_TIME <![CDATA[<=]]> #{endTime}
        </if>
        and TI.`TYPE` = 505
        and TI.RELATED_ID != 0
        and TIA.TYPE = 505
        and TIA.VALID_STATUS = 1
        and TIA.ADVANCE_VALID_STATUS = 1
        AND KDSVPI.F_QZOK_BDDJTID IS NULL
        AND KDSVSI.F_QZOK_BDDJTID IS NULL
        and TI.ADD_TIME > 1609430400000
        GROUP BY TI.INVOICE_ID

        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="findBySaleOrderNoAndMinAddTime" resultMap="SaleOrderNoAndMinAddTimeResultMap">
        select b.ADD_TIME,b.RELATED_ID,c.TRADER_CUSTOMER_ID from T_SALEORDER a left join T_INVOICE b on a.SALEORDER_ID = b.RELATED_ID
        left join T_TRADER_CUSTOMER c on  a.TRADER_ID  = c.TRADER_ID
        where a.SALEORDER_NO = #{saleOrderNo,jdbcType=VARCHAR}
        and b.COLOR_TYPE = 2 and b.IS_ENABLE = 1 and b.COMPANY_ID =1 and a.ADD_TIME >0
        order by a.ADD_TIME limit 1
    </select>

    <select id="findBySaleOrderNoAndMinAddTimeByAfterSaleNo" resultMap="BaseResultMap">
        select b.ADD_TIME,b.RELATED_ID,c.TRADER_CUSTOMER_ID from T_AFTER_SALES d
        left join T_SALEORDER a on d.ORDER_ID = a.SALEORDER_ID
        left join T_INVOICE b on a.SALEORDER_ID = b.RELATED_ID
        left join T_TRADER_CUSTOMER c on  a.TRADER_ID  = c.TRADER_ID
        where d.AFTER_SALES_NO = #{afterSaleNo,jdbcType=VARCHAR}
        and b.COLOR_TYPE = 2 and b.IS_ENABLE = 1 and b.COMPANY_ID =1 and a.ADD_TIME >0
        order by a.ADD_TIME limit 1
    </select>

<!--auto generated by MybatisCodeHelper on 2023-11-29-->
    <select id="selectBuyOrderBlueInvoiceByRelatedId" resultMap="BaseResultMap">
        select INVOICE_ID,
               COMPANY_ID,
               INVOICE_CODE,
               INVOICE_FLOW,
               INVOICE_PROPERTY,
               INVOICE_HREF,
               `TYPE`,
               TAG,
               RELATED_ID,
               AFTER_SALES_ID,
               INVOICE_NO,
               INVOICE_TYPE,
               RATIO,
               COLOR_TYPE,
               AMOUNT,
               IS_ENABLE,
               VALID_USERID,
               VALID_STATUS,
               VALID_TIME,
               VALID_COMMENTS,
               INVOICE_PRINT_STATUS,
               INVOICE_CANCEL_STATUS,
               EXPRESS_ID,
               IS_AUTH,
               IS_MONTH_AUTH,
               AUTH_TIME,
               ADD_TIME,
               CREATOR,
               MOD_TIME,
               UPDATER,
               INVOICE_APPLY_ID,
               SEND_TIME,
               AFTER_EXPRESS_ID,
               OSS_FILE_URL,
               RESOURCE_ID,
               INVOICE_FROM,
               HX_INVOICE_ID,
               AUTH_MODE,
               AUTH_FAIL_REASON,
               IS_AUTHING,
               COLOR_COMPLEMENT_TYPE,
               IS_AFTER_BUYORDER_ONLY
        from T_INVOICE
        where RELATED_ID=#{relatedId,jdbcType=INTEGER}
        and TYPE = 503 and COLOR_TYPE = 2 and COMPANY_ID=1
    </select>

    <select id="selectBuyOrderRedInvoiceByAfterSaleId" resultMap="BaseResultMap">
        select
        INVOICE_ID,
        COMPANY_ID,
        INVOICE_CODE,
        INVOICE_FLOW,
        INVOICE_PROPERTY,
        INVOICE_HREF,
        `TYPE`,
        TAG,
        RELATED_ID,
        AFTER_SALES_ID,
        INVOICE_NO,
        INVOICE_TYPE,
        RATIO,
        COLOR_TYPE,
        AMOUNT,
        IS_ENABLE,
        VALID_USERID,
        VALID_STATUS,
        VALID_TIME,
        VALID_COMMENTS,
        INVOICE_PRINT_STATUS,
        INVOICE_CANCEL_STATUS,
        EXPRESS_ID,
        IS_AUTH,
        IS_MONTH_AUTH,
        AUTH_TIME,
        ADD_TIME,
        CREATOR,
        MOD_TIME,
        UPDATER,
        INVOICE_APPLY_ID,
        SEND_TIME,
        AFTER_EXPRESS_ID,
        OSS_FILE_URL,
        RESOURCE_ID,
        INVOICE_FROM,
        HX_INVOICE_ID,
        AUTH_MODE,
        AUTH_FAIL_REASON,
        IS_AUTHING,
        COLOR_COMPLEMENT_TYPE,
        IS_AFTER_BUYORDER_ONLY
        from T_INVOICE
        where AFTER_SALES_ID=#{afterSaleId,jdbcType=INTEGER}
        and TYPE = 503 and COLOR_TYPE = 1 and IS_ENABLE = 1 and IS_AFTER_BUYORDER_ONLY = 0 and COLOR_COMPLEMENT_TYPE = 0 and COMPANY_ID=1 and VALID_STATUS =1
    </select>

<!--auto generated by MybatisCodeHelper on 2023-12-01-->
    <select id="findByInvoiceId" resultMap="BaseResultMap">
        select
        INVOICE_ID,
        COMPANY_ID,
        INVOICE_CODE,
        INVOICE_FLOW,
        INVOICE_PROPERTY,
        INVOICE_HREF,
        `TYPE`,
        TAG,
        RELATED_ID,
        AFTER_SALES_ID,
        INVOICE_NO,
        INVOICE_TYPE,
        RATIO,
        COLOR_TYPE,
        AMOUNT,
        IS_ENABLE,
        VALID_USERID,
        VALID_STATUS,
        VALID_TIME,
        VALID_COMMENTS,
        INVOICE_PRINT_STATUS,
        INVOICE_CANCEL_STATUS,
        EXPRESS_ID,
        IS_AUTH,
        IS_MONTH_AUTH,
        AUTH_TIME,
        ADD_TIME,
        CREATOR,
        MOD_TIME,
        UPDATER,
        INVOICE_APPLY_ID,
        SEND_TIME,
        AFTER_EXPRESS_ID,
        OSS_FILE_URL,
        RESOURCE_ID,
        INVOICE_FROM,
        HX_INVOICE_ID,
        AUTH_MODE,
        AUTH_FAIL_REASON,
        IS_AUTHING,
        COLOR_COMPLEMENT_TYPE,
        IS_AFTER_BUYORDER_ONLY
        from T_INVOICE
        where INVOICE_ID=#{invoiceId,jdbcType=INTEGER}
    </select>
    <select id="commonInvoicePdfFileReader" resultType="com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto">
        SELECT
            TI.INVOICE_ID,
            TI.INVOICE_NO,
            TI.OSS_FILE_URL,
            TI.INVOICE_PROPERTY,
            TSOD.TITLE AS invoiceTypeName,
            TI.INVOICE_APPLY_ID
        FROM
            T_INVOICE TI
              LEFT JOIN KING_DEE_FILE_DATA KDFD ON KDFD.URL = TI.OSS_FILE_URL AND TI.INVOICE_ID = KDFD.ERP_ID AND KDFD.FORM_ID in
                <foreach item="formId" index="index" collection="formIdList" separator="," open="(" close=")">
                    #{formId,jdbcType=VARCHAR}
                </foreach>
              LEFT JOIN T_SYS_OPTION_DEFINITION TSOD on TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE
        WHERE
            KDFD.ID IS NULL
            AND TI.COMPANY_ID = 1
            <if test="colorType != null">
                AND TI.COLOR_TYPE = #{colorType,jdbcType=INTEGER}
            </if>
            <if test="tag != null">
                and TI.TAG = #{tag,jdbcType=INTEGER}
            </if>
            <if test="isEnable != null">
                AND TI.IS_ENABLE = #{isEnable,jdbcType=INTEGER}
            </if>
            <if test="type != null">
                AND TI.TYPE = #{type,jdbcType=INTEGER}
            </if>
            <if test="validStatus != null">
                AND TI.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
            </if>
            <if test="isAfterBuyorderOnly != null">
                AND TI.IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly,jdbcType=INTEGER}
            </if>
            <if test="colorComplementType != null">
                AND TI.COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=INTEGER}
            </if>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>


    <select id="commonInvoiceXmlFileReader" resultType="com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto">
        SELECT
            titsr.URL_XML AS OSS_FILE_URL,
            TI.INVOICE_ID,
            TI.INVOICE_NO,
            TI.INVOICE_PROPERTY,
            TSOD.TITLE AS invoiceTypeName
        FROM
            T_INVOICE_TAX_SYSTEM_RECORD titsr
                LEFT JOIN T_INVOICE TI ON titsr.INVOICE_NO = TI.INVOICE_NO AND titsr.INTERFACE_TYPE = 'SALE_DOWN'
                LEFT JOIN KING_DEE_FILE_DATA KDFD ON KDFD.URL = titsr.URL_XML
                LEFT JOIN T_SYS_OPTION_DEFINITION TSOD on  TSOD.SYS_OPTION_DEFINITION_ID = TI.INVOICE_TYPE
        WHERE
            KDFD.ID IS NULL
            AND TI.COMPANY_ID = 1
            <if test="colorType != null">
                AND TI.COLOR_TYPE = #{colorType,jdbcType=INTEGER}
            </if>
            <if test="tag != null">
                and TI.TAG = #{tag,jdbcType=INTEGER}
            </if>
            <if test="isEnable != null">
                AND TI.IS_ENABLE = #{isEnable,jdbcType=INTEGER}
            </if>
            <if test="type != null">
                AND TI.TYPE = #{type,jdbcType=INTEGER}
            </if>
            <if test="validStatus != null">
                AND TI.VALID_STATUS = #{validStatus,jdbcType=INTEGER}
            </if>
            <if test="isAfterBuyorderOnly != null">
                AND TI.IS_AFTER_BUYORDER_ONLY = #{isAfterBuyorderOnly,jdbcType=INTEGER}
            </if>
            <if test="colorComplementType != null">
                AND TI.COLOR_COMPLEMENT_TYPE = #{colorComplementType,jdbcType=INTEGER}
            </if>
            limit #{_pagesize} OFFSET #{_skiprows}
    </select>
</mapper>