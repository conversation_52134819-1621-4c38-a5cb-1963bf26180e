package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * 流转单合同DTO
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchFlowOrderContractDto extends BatchBaseDto {
    /**
     * 流转单ID
     */
    private Long flowOrderId;
    
    /**
     * 流转单编号
     */
    private String flowOrderNo;
    
    /**
     * 流转单信息ID
     */
    private Long flowOrderInfoId;

    /**
     * 流转单信息no
     */
    private String flowOrderInfoNo;

    /**
     * 基础业务类型 1.采购 2.销售
     */
    private Integer baseBusinessType;
    
    /**
     * 基础业务订单ID
     */
    private Integer baseOrderId;
    
    /**
     * 基础业务订单编号
     */
    private String baseOrderNo;
    
    /**
     * 附件名称
     */
    private String name;
    
    /**
     * 合同文件URL
     */
    private String contractFileUrl;
    
    /**
     * 合同文件名称
     */
    private String contractFileName;
    
    /**
     * 附件回写表id
     */
    private Integer dataId;
    
    /**
     * oss http协议前缀
     */
    private String ossPre;
    
    /**
     * 附件domain
     */
    private String domain;
    
    /**
     * 附件uri
     */
    private String uri;
    
    /**
     * 文件后缀
     */
    private String suffix;
    
    /**
     * 有效时间开始
     */
    private Long validTimeBegin;
    
    /**
     * 有效时间结束
     */
    private Long validTimeEnd;
}
