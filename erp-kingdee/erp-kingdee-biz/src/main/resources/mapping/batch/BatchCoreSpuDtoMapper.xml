<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchCoreSpuDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchCoreSpuDto">
    <!--@mbg.generated-->
    <!--@Table V_CORE_SPU-->
    <id column="SPU_ID" jdbcType="INTEGER" property="spuId" />
    <result column="CATEGORY_ID" jdbcType="INTEGER" property="categoryId" />
    <result column="BRAND_ID" jdbcType="INTEGER" property="brandId" />
    <result column="SPU_NO" jdbcType="VARCHAR" property="spuNo" />
    <result column="SPU_NAME" jdbcType="VARCHAR" property="spuName" />
    <result column="SHOW_NAME" jdbcType="VARCHAR" property="showName" />
    <result column="SPU_LEVEL" jdbcType="BOOLEAN" property="spuLevel" />
    <result column="STATUS" jdbcType="BOOLEAN" property="status" />
    <result column="SPU_TYPE" jdbcType="INTEGER" property="spuType" />
    <result column="FIRST_ENGAGE_ID" jdbcType="INTEGER" property="firstEngageId" />
    <result column="REGISTRATION_ICON" jdbcType="VARCHAR" property="registrationIcon" />
    <result column="WIKI_HREF" jdbcType="VARCHAR" property="wikiHref" />
    <result column="OPERATE_INFO_FLAG" jdbcType="BOOLEAN" property="operateInfoFlag" />
    <result column="CHECK_STATUS" jdbcType="BOOLEAN" property="checkStatus" />
    <result column="OPERATE_INFO_ID" jdbcType="INTEGER" property="operateInfoId" />
    <result column="HOSPITAL_TAGS" jdbcType="VARCHAR" property="hospitalTags" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CHECK_TIME" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="CHECKER" jdbcType="INTEGER" property="checker" />
    <result column="DELETE_REASON" jdbcType="VARCHAR" property="deleteReason" />
    <result column="LAST_CHECK_REASON" jdbcType="VARCHAR" property="lastCheckReason" />
    <result column="ASSIGNMENT_MANAGER_ID" jdbcType="INTEGER" property="assignmentManagerId" />
    <result column="ASSIGNMENT_ASSISTANT_ID" jdbcType="INTEGER" property="assignmentAssistantId" />
    <result column="APPARATUS_TYPE" jdbcType="TINYINT" property="apparatusType" />
    <result column="STORAGE_CONDITION_TEMPERATURE" jdbcType="BOOLEAN" property="storageConditionTemperature" />
    <result column="STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE" jdbcType="FLOAT" property="storageConditionTemperatureLowerValue" />
    <result column="STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE" jdbcType="FLOAT" property="storageConditionTemperatureUpperValue" />
    <result column="STORAGE_CONDITION_HUMIDITY_LOWER_VALUE" jdbcType="FLOAT" property="storageConditionHumidityLowerValue" />
    <result column="STORAGE_CONDITION_HUMIDITY_UPPER_VALUE" jdbcType="FLOAT" property="storageConditionHumidityUpperValue" />
    <result column="STORAGE_CONDITION_OTHERS" jdbcType="VARCHAR" property="storageConditionOthers" />
    <result column="TECHNICAL_PARAMETER_NAMES" jdbcType="VARCHAR" property="technicalParameterNames" />
    <result column="SECOND_LEVEL_SPU_TYPE" jdbcType="INTEGER" property="secondLevelSpuType" />
    <result column="SPECS_MODEL" jdbcType="VARCHAR" property="specsModel" />
    <result column="MEDICAL_INSTRUMENT_CATALOG_INCLUDED" jdbcType="BOOLEAN" property="medicalInstrumentCatalogIncluded" />
    <result column="GOODS_LEVEL_NO" jdbcType="INTEGER" property="goodsLevelNo" />
    <result column="GOODS_POSITION_NO" jdbcType="INTEGER" property="goodsPositionNo" />
    <result column="DISABLED_REASON" jdbcType="VARCHAR" property="disabledReason" />
    <result column="NO_MEDICAL_FIRST_TYPE" jdbcType="INTEGER" property="noMedicalFirstType" />
    <result column="NO_MEDICAL_SECOND_TYPE" jdbcType="INTEGER" property="noMedicalSecondType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SPU_ID, CATEGORY_ID, BRAND_ID, SPU_NO, SPU_NAME, SHOW_NAME, SPU_LEVEL, `STATUS`, 
    SPU_TYPE, FIRST_ENGAGE_ID, REGISTRATION_ICON, WIKI_HREF, OPERATE_INFO_FLAG, CHECK_STATUS, 
    OPERATE_INFO_ID, HOSPITAL_TAGS, ADD_TIME, CREATOR, MOD_TIME, UPDATER, CHECK_TIME, 
    CHECKER, DELETE_REASON, LAST_CHECK_REASON, ASSIGNMENT_MANAGER_ID, ASSIGNMENT_ASSISTANT_ID, 
    APPARATUS_TYPE, STORAGE_CONDITION_TEMPERATURE, STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE, 
    STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE, STORAGE_CONDITION_HUMIDITY_LOWER_VALUE, 
    STORAGE_CONDITION_HUMIDITY_UPPER_VALUE, STORAGE_CONDITION_OTHERS, TECHNICAL_PARAMETER_NAMES, 
    SECOND_LEVEL_SPU_TYPE, SPECS_MODEL, MEDICAL_INSTRUMENT_CATALOG_INCLUDED, GOODS_LEVEL_NO, 
    GOODS_POSITION_NO, DISABLED_REASON, NO_MEDICAL_FIRST_TYPE, NO_MEDICAL_SECOND_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from V_CORE_SPU
    where SPU_ID = #{spuId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from V_CORE_SPU
    where SPU_ID = #{spuId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="SPU_ID" keyProperty="spuId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchCoreSpuDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_CORE_SPU (CATEGORY_ID, BRAND_ID, SPU_NO, 
      SPU_NAME, SHOW_NAME, SPU_LEVEL, 
      `STATUS`, SPU_TYPE, FIRST_ENGAGE_ID, 
      REGISTRATION_ICON, WIKI_HREF, OPERATE_INFO_FLAG, 
      CHECK_STATUS, OPERATE_INFO_ID, HOSPITAL_TAGS, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, CHECK_TIME, CHECKER, 
      DELETE_REASON, LAST_CHECK_REASON, ASSIGNMENT_MANAGER_ID, 
      ASSIGNMENT_ASSISTANT_ID, APPARATUS_TYPE, STORAGE_CONDITION_TEMPERATURE, 
      STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE, STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE, 
      STORAGE_CONDITION_HUMIDITY_LOWER_VALUE, STORAGE_CONDITION_HUMIDITY_UPPER_VALUE, 
      STORAGE_CONDITION_OTHERS, TECHNICAL_PARAMETER_NAMES, 
      SECOND_LEVEL_SPU_TYPE, SPECS_MODEL, MEDICAL_INSTRUMENT_CATALOG_INCLUDED, 
      GOODS_LEVEL_NO, GOODS_POSITION_NO, DISABLED_REASON, 
      NO_MEDICAL_FIRST_TYPE, NO_MEDICAL_SECOND_TYPE
      )
    values (#{categoryId,jdbcType=INTEGER}, #{brandId,jdbcType=INTEGER}, #{spuNo,jdbcType=VARCHAR}, 
      #{spuName,jdbcType=VARCHAR}, #{showName,jdbcType=VARCHAR}, #{spuLevel,jdbcType=BOOLEAN}, 
      #{status,jdbcType=BOOLEAN}, #{spuType,jdbcType=INTEGER}, #{firstEngageId,jdbcType=INTEGER}, 
      #{registrationIcon,jdbcType=VARCHAR}, #{wikiHref,jdbcType=VARCHAR}, #{operateInfoFlag,jdbcType=BOOLEAN}, 
      #{checkStatus,jdbcType=BOOLEAN}, #{operateInfoId,jdbcType=INTEGER}, #{hospitalTags,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER}, #{checkTime,jdbcType=TIMESTAMP}, #{checker,jdbcType=INTEGER}, 
      #{deleteReason,jdbcType=VARCHAR}, #{lastCheckReason,jdbcType=VARCHAR}, #{assignmentManagerId,jdbcType=INTEGER}, 
      #{assignmentAssistantId,jdbcType=INTEGER}, #{apparatusType,jdbcType=TINYINT}, #{storageConditionTemperature,jdbcType=BOOLEAN}, 
      #{storageConditionTemperatureLowerValue,jdbcType=FLOAT}, #{storageConditionTemperatureUpperValue,jdbcType=FLOAT}, 
      #{storageConditionHumidityLowerValue,jdbcType=FLOAT}, #{storageConditionHumidityUpperValue,jdbcType=FLOAT}, 
      #{storageConditionOthers,jdbcType=VARCHAR}, #{technicalParameterNames,jdbcType=VARCHAR}, 
      #{secondLevelSpuType,jdbcType=INTEGER}, #{specsModel,jdbcType=VARCHAR}, #{medicalInstrumentCatalogIncluded,jdbcType=BOOLEAN}, 
      #{goodsLevelNo,jdbcType=INTEGER}, #{goodsPositionNo,jdbcType=INTEGER}, #{disabledReason,jdbcType=VARCHAR}, 
      #{noMedicalFirstType,jdbcType=INTEGER}, #{noMedicalSecondType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="SPU_ID" keyProperty="spuId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchCoreSpuDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_CORE_SPU
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="categoryId != null">
        CATEGORY_ID,
      </if>
      <if test="brandId != null">
        BRAND_ID,
      </if>
      <if test="spuNo != null and spuNo != ''">
        SPU_NO,
      </if>
      <if test="spuName != null and spuName != ''">
        SPU_NAME,
      </if>
      <if test="showName != null and showName != ''">
        SHOW_NAME,
      </if>
      <if test="spuLevel != null">
        SPU_LEVEL,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="spuType != null">
        SPU_TYPE,
      </if>
      <if test="firstEngageId != null">
        FIRST_ENGAGE_ID,
      </if>
      <if test="registrationIcon != null and registrationIcon != ''">
        REGISTRATION_ICON,
      </if>
      <if test="wikiHref != null and wikiHref != ''">
        WIKI_HREF,
      </if>
      <if test="operateInfoFlag != null">
        OPERATE_INFO_FLAG,
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS,
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID,
      </if>
      <if test="hospitalTags != null and hospitalTags != ''">
        HOSPITAL_TAGS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="checkTime != null">
        CHECK_TIME,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="deleteReason != null and deleteReason != ''">
        DELETE_REASON,
      </if>
      <if test="lastCheckReason != null and lastCheckReason != ''">
        LAST_CHECK_REASON,
      </if>
      <if test="assignmentManagerId != null">
        ASSIGNMENT_MANAGER_ID,
      </if>
      <if test="assignmentAssistantId != null">
        ASSIGNMENT_ASSISTANT_ID,
      </if>
      <if test="apparatusType != null">
        APPARATUS_TYPE,
      </if>
      <if test="storageConditionTemperature != null">
        STORAGE_CONDITION_TEMPERATURE,
      </if>
      <if test="storageConditionTemperatureLowerValue != null">
        STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE,
      </if>
      <if test="storageConditionTemperatureUpperValue != null">
        STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE,
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE,
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,
      </if>
      <if test="storageConditionOthers != null and storageConditionOthers != ''">
        STORAGE_CONDITION_OTHERS,
      </if>
      <if test="technicalParameterNames != null and technicalParameterNames != ''">
        TECHNICAL_PARAMETER_NAMES,
      </if>
      <if test="secondLevelSpuType != null">
        SECOND_LEVEL_SPU_TYPE,
      </if>
      <if test="specsModel != null and specsModel != ''">
        SPECS_MODEL,
      </if>
      <if test="medicalInstrumentCatalogIncluded != null">
        MEDICAL_INSTRUMENT_CATALOG_INCLUDED,
      </if>
      <if test="goodsLevelNo != null">
        GOODS_LEVEL_NO,
      </if>
      <if test="goodsPositionNo != null">
        GOODS_POSITION_NO,
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        DISABLED_REASON,
      </if>
      <if test="noMedicalFirstType != null">
        NO_MEDICAL_FIRST_TYPE,
      </if>
      <if test="noMedicalSecondType != null">
        NO_MEDICAL_SECOND_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="categoryId != null">
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=INTEGER},
      </if>
      <if test="spuNo != null and spuNo != ''">
        #{spuNo,jdbcType=VARCHAR},
      </if>
      <if test="spuName != null and spuName != ''">
        #{spuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null and showName != ''">
        #{showName,jdbcType=VARCHAR},
      </if>
      <if test="spuLevel != null">
        #{spuLevel,jdbcType=BOOLEAN},
      </if>
      <if test="status != null">
        #{status,jdbcType=BOOLEAN},
      </if>
      <if test="spuType != null">
        #{spuType,jdbcType=INTEGER},
      </if>
      <if test="firstEngageId != null">
        #{firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="registrationIcon != null and registrationIcon != ''">
        #{registrationIcon,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null and wikiHref != ''">
        #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="operateInfoFlag != null">
        #{operateInfoFlag,jdbcType=BOOLEAN},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=BOOLEAN},
      </if>
      <if test="operateInfoId != null">
        #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="hospitalTags != null and hospitalTags != ''">
        #{hospitalTags,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null and deleteReason != ''">
        #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null and lastCheckReason != ''">
        #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="assignmentManagerId != null">
        #{assignmentManagerId,jdbcType=INTEGER},
      </if>
      <if test="assignmentAssistantId != null">
        #{assignmentAssistantId,jdbcType=INTEGER},
      </if>
      <if test="apparatusType != null">
        #{apparatusType,jdbcType=TINYINT},
      </if>
      <if test="storageConditionTemperature != null">
        #{storageConditionTemperature,jdbcType=BOOLEAN},
      </if>
      <if test="storageConditionTemperatureLowerValue != null">
        #{storageConditionTemperatureLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionTemperatureUpperValue != null">
        #{storageConditionTemperatureUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionOthers != null and storageConditionOthers != ''">
        #{storageConditionOthers,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameterNames != null and technicalParameterNames != ''">
        #{technicalParameterNames,jdbcType=VARCHAR},
      </if>
      <if test="secondLevelSpuType != null">
        #{secondLevelSpuType,jdbcType=INTEGER},
      </if>
      <if test="specsModel != null and specsModel != ''">
        #{specsModel,jdbcType=VARCHAR},
      </if>
      <if test="medicalInstrumentCatalogIncluded != null">
        #{medicalInstrumentCatalogIncluded,jdbcType=BOOLEAN},
      </if>
      <if test="goodsLevelNo != null">
        #{goodsLevelNo,jdbcType=INTEGER},
      </if>
      <if test="goodsPositionNo != null">
        #{goodsPositionNo,jdbcType=INTEGER},
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        #{disabledReason,jdbcType=VARCHAR},
      </if>
      <if test="noMedicalFirstType != null">
        #{noMedicalFirstType,jdbcType=INTEGER},
      </if>
      <if test="noMedicalSecondType != null">
        #{noMedicalSecondType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchCoreSpuDto">
    <!--@mbg.generated-->
    update V_CORE_SPU
    <set>
      <if test="categoryId != null">
        CATEGORY_ID = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        BRAND_ID = #{brandId,jdbcType=INTEGER},
      </if>
      <if test="spuNo != null and spuNo != ''">
        SPU_NO = #{spuNo,jdbcType=VARCHAR},
      </if>
      <if test="spuName != null and spuName != ''">
        SPU_NAME = #{spuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null and showName != ''">
        SHOW_NAME = #{showName,jdbcType=VARCHAR},
      </if>
      <if test="spuLevel != null">
        SPU_LEVEL = #{spuLevel,jdbcType=BOOLEAN},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=BOOLEAN},
      </if>
      <if test="spuType != null">
        SPU_TYPE = #{spuType,jdbcType=INTEGER},
      </if>
      <if test="firstEngageId != null">
        FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="registrationIcon != null and registrationIcon != ''">
        REGISTRATION_ICON = #{registrationIcon,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null and wikiHref != ''">
        WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="operateInfoFlag != null">
        OPERATE_INFO_FLAG = #{operateInfoFlag,jdbcType=BOOLEAN},
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS = #{checkStatus,jdbcType=BOOLEAN},
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="hospitalTags != null and hospitalTags != ''">
        HOSPITAL_TAGS = #{hospitalTags,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        CHECKER = #{checker,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null and deleteReason != ''">
        DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null and lastCheckReason != ''">
        LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="assignmentManagerId != null">
        ASSIGNMENT_MANAGER_ID = #{assignmentManagerId,jdbcType=INTEGER},
      </if>
      <if test="assignmentAssistantId != null">
        ASSIGNMENT_ASSISTANT_ID = #{assignmentAssistantId,jdbcType=INTEGER},
      </if>
      <if test="apparatusType != null">
        APPARATUS_TYPE = #{apparatusType,jdbcType=TINYINT},
      </if>
      <if test="storageConditionTemperature != null">
        STORAGE_CONDITION_TEMPERATURE = #{storageConditionTemperature,jdbcType=BOOLEAN},
      </if>
      <if test="storageConditionTemperatureLowerValue != null">
        STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE = #{storageConditionTemperatureLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionTemperatureUpperValue != null">
        STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE = #{storageConditionTemperatureUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionOthers != null and storageConditionOthers != ''">
        STORAGE_CONDITION_OTHERS = #{storageConditionOthers,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameterNames != null and technicalParameterNames != ''">
        TECHNICAL_PARAMETER_NAMES = #{technicalParameterNames,jdbcType=VARCHAR},
      </if>
      <if test="secondLevelSpuType != null">
        SECOND_LEVEL_SPU_TYPE = #{secondLevelSpuType,jdbcType=INTEGER},
      </if>
      <if test="specsModel != null and specsModel != ''">
        SPECS_MODEL = #{specsModel,jdbcType=VARCHAR},
      </if>
      <if test="medicalInstrumentCatalogIncluded != null">
        MEDICAL_INSTRUMENT_CATALOG_INCLUDED = #{medicalInstrumentCatalogIncluded,jdbcType=BOOLEAN},
      </if>
      <if test="goodsLevelNo != null">
        GOODS_LEVEL_NO = #{goodsLevelNo,jdbcType=INTEGER},
      </if>
      <if test="goodsPositionNo != null">
        GOODS_POSITION_NO = #{goodsPositionNo,jdbcType=INTEGER},
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        DISABLED_REASON = #{disabledReason,jdbcType=VARCHAR},
      </if>
      <if test="noMedicalFirstType != null">
        NO_MEDICAL_FIRST_TYPE = #{noMedicalFirstType,jdbcType=INTEGER},
      </if>
      <if test="noMedicalSecondType != null">
        NO_MEDICAL_SECOND_TYPE = #{noMedicalSecondType,jdbcType=INTEGER},
      </if>
    </set>
    where SPU_ID = #{spuId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchCoreSpuDto">
    <!--@mbg.generated-->
    update V_CORE_SPU
    set CATEGORY_ID = #{categoryId,jdbcType=INTEGER},
      BRAND_ID = #{brandId,jdbcType=INTEGER},
      SPU_NO = #{spuNo,jdbcType=VARCHAR},
      SPU_NAME = #{spuName,jdbcType=VARCHAR},
      SHOW_NAME = #{showName,jdbcType=VARCHAR},
      SPU_LEVEL = #{spuLevel,jdbcType=BOOLEAN},
      `STATUS` = #{status,jdbcType=BOOLEAN},
      SPU_TYPE = #{spuType,jdbcType=INTEGER},
      FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER},
      REGISTRATION_ICON = #{registrationIcon,jdbcType=VARCHAR},
      WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      OPERATE_INFO_FLAG = #{operateInfoFlag,jdbcType=BOOLEAN},
      CHECK_STATUS = #{checkStatus,jdbcType=BOOLEAN},
      OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      HOSPITAL_TAGS = #{hospitalTags,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      CHECKER = #{checker,jdbcType=INTEGER},
      DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      ASSIGNMENT_MANAGER_ID = #{assignmentManagerId,jdbcType=INTEGER},
      ASSIGNMENT_ASSISTANT_ID = #{assignmentAssistantId,jdbcType=INTEGER},
      APPARATUS_TYPE = #{apparatusType,jdbcType=TINYINT},
      STORAGE_CONDITION_TEMPERATURE = #{storageConditionTemperature,jdbcType=BOOLEAN},
      STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE = #{storageConditionTemperatureLowerValue,jdbcType=FLOAT},
      STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE = #{storageConditionTemperatureUpperValue,jdbcType=FLOAT},
      STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      STORAGE_CONDITION_OTHERS = #{storageConditionOthers,jdbcType=VARCHAR},
      TECHNICAL_PARAMETER_NAMES = #{technicalParameterNames,jdbcType=VARCHAR},
      SECOND_LEVEL_SPU_TYPE = #{secondLevelSpuType,jdbcType=INTEGER},
      SPECS_MODEL = #{specsModel,jdbcType=VARCHAR},
      MEDICAL_INSTRUMENT_CATALOG_INCLUDED = #{medicalInstrumentCatalogIncluded,jdbcType=BOOLEAN},
      GOODS_LEVEL_NO = #{goodsLevelNo,jdbcType=INTEGER},
      GOODS_POSITION_NO = #{goodsPositionNo,jdbcType=INTEGER},
      DISABLED_REASON = #{disabledReason,jdbcType=VARCHAR},
      NO_MEDICAL_FIRST_TYPE = #{noMedicalFirstType,jdbcType=INTEGER},
      NO_MEDICAL_SECOND_TYPE = #{noMedicalSecondType,jdbcType=INTEGER}
    where SPU_ID = #{spuId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update V_CORE_SPU
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="CATEGORY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.categoryId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="BRAND_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.brandId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SPU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.spuNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SPU_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.spuName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SHOW_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.showName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SPU_LEVEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.spuLevel,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="`STATUS` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.status,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="SPU_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.spuType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="FIRST_ENGAGE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.firstEngageId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="REGISTRATION_ICON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.registrationIcon,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="WIKI_HREF = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.wikiHref,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="OPERATE_INFO_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.operateInfoFlag,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="CHECK_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.checkStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="OPERATE_INFO_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.operateInfoId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="HOSPITAL_TAGS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.hospitalTags,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CHECK_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.checkTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CHECKER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.checker,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DELETE_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.deleteReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="LAST_CHECK_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.lastCheckReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ASSIGNMENT_MANAGER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.assignmentManagerId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ASSIGNMENT_ASSISTANT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.assignmentAssistantId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="APPARATUS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.apparatusType,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_TEMPERATURE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.storageConditionTemperature,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.storageConditionTemperatureLowerValue,jdbcType=FLOAT}
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.storageConditionTemperatureUpperValue,jdbcType=FLOAT}
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.storageConditionHumidityLowerValue,jdbcType=FLOAT}
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.storageConditionHumidityUpperValue,jdbcType=FLOAT}
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_OTHERS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.storageConditionOthers,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TECHNICAL_PARAMETER_NAMES = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.technicalParameterNames,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SECOND_LEVEL_SPU_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.secondLevelSpuType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SPECS_MODEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.specsModel,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MEDICAL_INSTRUMENT_CATALOG_INCLUDED = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.medicalInstrumentCatalogIncluded,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="GOODS_LEVEL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.goodsLevelNo,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="GOODS_POSITION_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.goodsPositionNo,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="DISABLED_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.disabledReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="NO_MEDICAL_FIRST_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.noMedicalFirstType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="NO_MEDICAL_SECOND_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.noMedicalSecondType,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where SPU_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.spuId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update V_CORE_SPU
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="CATEGORY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.categoryId != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.categoryId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BRAND_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.brandId != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.brandId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SPU_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.spuNo != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.spuNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SPU_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.spuName != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.spuName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SHOW_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.showName != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.showName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SPU_LEVEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.spuLevel != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.spuLevel,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="`STATUS` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.status,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="SPU_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.spuType != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.spuType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="FIRST_ENGAGE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.firstEngageId != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.firstEngageId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="REGISTRATION_ICON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.registrationIcon != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.registrationIcon,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="WIKI_HREF = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.wikiHref != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.wikiHref,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="OPERATE_INFO_FLAG = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operateInfoFlag != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.operateInfoFlag,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="CHECK_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.checkStatus != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.checkStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="OPERATE_INFO_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operateInfoId != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.operateInfoId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="HOSPITAL_TAGS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hospitalTags != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.hospitalTags,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CHECK_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.checkTime != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.checkTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CHECKER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.checker != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.checker,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DELETE_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deleteReason != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.deleteReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="LAST_CHECK_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.lastCheckReason != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.lastCheckReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ASSIGNMENT_MANAGER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.assignmentManagerId != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.assignmentManagerId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ASSIGNMENT_ASSISTANT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.assignmentAssistantId != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.assignmentAssistantId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="APPARATUS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.apparatusType != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.apparatusType,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_TEMPERATURE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storageConditionTemperature != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.storageConditionTemperature,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storageConditionTemperatureLowerValue != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.storageConditionTemperatureLowerValue,jdbcType=FLOAT}
          </if>
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storageConditionTemperatureUpperValue != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.storageConditionTemperatureUpperValue,jdbcType=FLOAT}
          </if>
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storageConditionHumidityLowerValue != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.storageConditionHumidityLowerValue,jdbcType=FLOAT}
          </if>
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storageConditionHumidityUpperValue != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.storageConditionHumidityUpperValue,jdbcType=FLOAT}
          </if>
        </foreach>
      </trim>
      <trim prefix="STORAGE_CONDITION_OTHERS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storageConditionOthers != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.storageConditionOthers,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TECHNICAL_PARAMETER_NAMES = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.technicalParameterNames != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.technicalParameterNames,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SECOND_LEVEL_SPU_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.secondLevelSpuType != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.secondLevelSpuType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SPECS_MODEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.specsModel != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.specsModel,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="MEDICAL_INSTRUMENT_CATALOG_INCLUDED = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.medicalInstrumentCatalogIncluded != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.medicalInstrumentCatalogIncluded,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_LEVEL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsLevelNo != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.goodsLevelNo,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="GOODS_POSITION_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsPositionNo != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.goodsPositionNo,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="DISABLED_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.disabledReason != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.disabledReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="NO_MEDICAL_FIRST_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.noMedicalFirstType != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.noMedicalFirstType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="NO_MEDICAL_SECOND_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.noMedicalSecondType != null">
            when SPU_ID = #{item.spuId,jdbcType=INTEGER} then #{item.noMedicalSecondType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where SPU_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.spuId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="SPU_ID" keyProperty="spuId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_CORE_SPU
    (CATEGORY_ID, BRAND_ID, SPU_NO, SPU_NAME, SHOW_NAME, SPU_LEVEL, `STATUS`, SPU_TYPE, 
      FIRST_ENGAGE_ID, REGISTRATION_ICON, WIKI_HREF, OPERATE_INFO_FLAG, CHECK_STATUS, 
      OPERATE_INFO_ID, HOSPITAL_TAGS, ADD_TIME, CREATOR, MOD_TIME, UPDATER, CHECK_TIME, 
      CHECKER, DELETE_REASON, LAST_CHECK_REASON, ASSIGNMENT_MANAGER_ID, ASSIGNMENT_ASSISTANT_ID, 
      APPARATUS_TYPE, STORAGE_CONDITION_TEMPERATURE, STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE, 
      STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE, STORAGE_CONDITION_HUMIDITY_LOWER_VALUE, 
      STORAGE_CONDITION_HUMIDITY_UPPER_VALUE, STORAGE_CONDITION_OTHERS, TECHNICAL_PARAMETER_NAMES, 
      SECOND_LEVEL_SPU_TYPE, SPECS_MODEL, MEDICAL_INSTRUMENT_CATALOG_INCLUDED, GOODS_LEVEL_NO, 
      GOODS_POSITION_NO, DISABLED_REASON, NO_MEDICAL_FIRST_TYPE, NO_MEDICAL_SECOND_TYPE
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.categoryId,jdbcType=INTEGER}, #{item.brandId,jdbcType=INTEGER}, #{item.spuNo,jdbcType=VARCHAR}, 
        #{item.spuName,jdbcType=VARCHAR}, #{item.showName,jdbcType=VARCHAR}, #{item.spuLevel,jdbcType=BOOLEAN}, 
        #{item.status,jdbcType=BOOLEAN}, #{item.spuType,jdbcType=INTEGER}, #{item.firstEngageId,jdbcType=INTEGER}, 
        #{item.registrationIcon,jdbcType=VARCHAR}, #{item.wikiHref,jdbcType=VARCHAR}, #{item.operateInfoFlag,jdbcType=BOOLEAN}, 
        #{item.checkStatus,jdbcType=BOOLEAN}, #{item.operateInfoId,jdbcType=INTEGER}, #{item.hospitalTags,jdbcType=VARCHAR}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=TIMESTAMP}, 
        #{item.updater,jdbcType=INTEGER}, #{item.checkTime,jdbcType=TIMESTAMP}, #{item.checker,jdbcType=INTEGER}, 
        #{item.deleteReason,jdbcType=VARCHAR}, #{item.lastCheckReason,jdbcType=VARCHAR}, 
        #{item.assignmentManagerId,jdbcType=INTEGER}, #{item.assignmentAssistantId,jdbcType=INTEGER}, 
        #{item.apparatusType,jdbcType=TINYINT}, #{item.storageConditionTemperature,jdbcType=BOOLEAN}, 
        #{item.storageConditionTemperatureLowerValue,jdbcType=FLOAT}, #{item.storageConditionTemperatureUpperValue,jdbcType=FLOAT}, 
        #{item.storageConditionHumidityLowerValue,jdbcType=FLOAT}, #{item.storageConditionHumidityUpperValue,jdbcType=FLOAT}, 
        #{item.storageConditionOthers,jdbcType=VARCHAR}, #{item.technicalParameterNames,jdbcType=VARCHAR}, 
        #{item.secondLevelSpuType,jdbcType=INTEGER}, #{item.specsModel,jdbcType=VARCHAR}, 
        #{item.medicalInstrumentCatalogIncluded,jdbcType=BOOLEAN}, #{item.goodsLevelNo,jdbcType=INTEGER}, 
        #{item.goodsPositionNo,jdbcType=INTEGER}, #{item.disabledReason,jdbcType=VARCHAR}, 
        #{item.noMedicalFirstType,jdbcType=INTEGER}, #{item.noMedicalSecondType,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="SPU_ID" keyProperty="spuId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchCoreSpuDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_CORE_SPU
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="spuId != null">
        SPU_ID,
      </if>
      CATEGORY_ID,
      BRAND_ID,
      SPU_NO,
      SPU_NAME,
      SHOW_NAME,
      SPU_LEVEL,
      `STATUS`,
      SPU_TYPE,
      FIRST_ENGAGE_ID,
      REGISTRATION_ICON,
      WIKI_HREF,
      OPERATE_INFO_FLAG,
      CHECK_STATUS,
      OPERATE_INFO_ID,
      HOSPITAL_TAGS,
      ADD_TIME,
      CREATOR,
      MOD_TIME,
      UPDATER,
      CHECK_TIME,
      CHECKER,
      DELETE_REASON,
      LAST_CHECK_REASON,
      ASSIGNMENT_MANAGER_ID,
      ASSIGNMENT_ASSISTANT_ID,
      APPARATUS_TYPE,
      STORAGE_CONDITION_TEMPERATURE,
      STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE,
      STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE,
      STORAGE_CONDITION_HUMIDITY_LOWER_VALUE,
      STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,
      STORAGE_CONDITION_OTHERS,
      TECHNICAL_PARAMETER_NAMES,
      SECOND_LEVEL_SPU_TYPE,
      SPECS_MODEL,
      MEDICAL_INSTRUMENT_CATALOG_INCLUDED,
      GOODS_LEVEL_NO,
      GOODS_POSITION_NO,
      DISABLED_REASON,
      NO_MEDICAL_FIRST_TYPE,
      NO_MEDICAL_SECOND_TYPE,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="spuId != null">
        #{spuId,jdbcType=INTEGER},
      </if>
      #{categoryId,jdbcType=INTEGER},
      #{brandId,jdbcType=INTEGER},
      #{spuNo,jdbcType=VARCHAR},
      #{spuName,jdbcType=VARCHAR},
      #{showName,jdbcType=VARCHAR},
      #{spuLevel,jdbcType=BOOLEAN},
      #{status,jdbcType=BOOLEAN},
      #{spuType,jdbcType=INTEGER},
      #{firstEngageId,jdbcType=INTEGER},
      #{registrationIcon,jdbcType=VARCHAR},
      #{wikiHref,jdbcType=VARCHAR},
      #{operateInfoFlag,jdbcType=BOOLEAN},
      #{checkStatus,jdbcType=BOOLEAN},
      #{operateInfoId,jdbcType=INTEGER},
      #{hospitalTags,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{modTime,jdbcType=TIMESTAMP},
      #{updater,jdbcType=INTEGER},
      #{checkTime,jdbcType=TIMESTAMP},
      #{checker,jdbcType=INTEGER},
      #{deleteReason,jdbcType=VARCHAR},
      #{lastCheckReason,jdbcType=VARCHAR},
      #{assignmentManagerId,jdbcType=INTEGER},
      #{assignmentAssistantId,jdbcType=INTEGER},
      #{apparatusType,jdbcType=TINYINT},
      #{storageConditionTemperature,jdbcType=BOOLEAN},
      #{storageConditionTemperatureLowerValue,jdbcType=FLOAT},
      #{storageConditionTemperatureUpperValue,jdbcType=FLOAT},
      #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      #{storageConditionOthers,jdbcType=VARCHAR},
      #{technicalParameterNames,jdbcType=VARCHAR},
      #{secondLevelSpuType,jdbcType=INTEGER},
      #{specsModel,jdbcType=VARCHAR},
      #{medicalInstrumentCatalogIncluded,jdbcType=BOOLEAN},
      #{goodsLevelNo,jdbcType=INTEGER},
      #{goodsPositionNo,jdbcType=INTEGER},
      #{disabledReason,jdbcType=VARCHAR},
      #{noMedicalFirstType,jdbcType=INTEGER},
      #{noMedicalSecondType,jdbcType=INTEGER},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="spuId != null">
        SPU_ID = #{spuId,jdbcType=INTEGER},
      </if>
      CATEGORY_ID = #{categoryId,jdbcType=INTEGER},
      BRAND_ID = #{brandId,jdbcType=INTEGER},
      SPU_NO = #{spuNo,jdbcType=VARCHAR},
      SPU_NAME = #{spuName,jdbcType=VARCHAR},
      SHOW_NAME = #{showName,jdbcType=VARCHAR},
      SPU_LEVEL = #{spuLevel,jdbcType=BOOLEAN},
      `STATUS` = #{status,jdbcType=BOOLEAN},
      SPU_TYPE = #{spuType,jdbcType=INTEGER},
      FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER},
      REGISTRATION_ICON = #{registrationIcon,jdbcType=VARCHAR},
      WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      OPERATE_INFO_FLAG = #{operateInfoFlag,jdbcType=BOOLEAN},
      CHECK_STATUS = #{checkStatus,jdbcType=BOOLEAN},
      OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      HOSPITAL_TAGS = #{hospitalTags,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      CHECKER = #{checker,jdbcType=INTEGER},
      DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      ASSIGNMENT_MANAGER_ID = #{assignmentManagerId,jdbcType=INTEGER},
      ASSIGNMENT_ASSISTANT_ID = #{assignmentAssistantId,jdbcType=INTEGER},
      APPARATUS_TYPE = #{apparatusType,jdbcType=TINYINT},
      STORAGE_CONDITION_TEMPERATURE = #{storageConditionTemperature,jdbcType=BOOLEAN},
      STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE = #{storageConditionTemperatureLowerValue,jdbcType=FLOAT},
      STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE = #{storageConditionTemperatureUpperValue,jdbcType=FLOAT},
      STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      STORAGE_CONDITION_OTHERS = #{storageConditionOthers,jdbcType=VARCHAR},
      TECHNICAL_PARAMETER_NAMES = #{technicalParameterNames,jdbcType=VARCHAR},
      SECOND_LEVEL_SPU_TYPE = #{secondLevelSpuType,jdbcType=INTEGER},
      SPECS_MODEL = #{specsModel,jdbcType=VARCHAR},
      MEDICAL_INSTRUMENT_CATALOG_INCLUDED = #{medicalInstrumentCatalogIncluded,jdbcType=BOOLEAN},
      GOODS_LEVEL_NO = #{goodsLevelNo,jdbcType=INTEGER},
      GOODS_POSITION_NO = #{goodsPositionNo,jdbcType=INTEGER},
      DISABLED_REASON = #{disabledReason,jdbcType=VARCHAR},
      NO_MEDICAL_FIRST_TYPE = #{noMedicalFirstType,jdbcType=INTEGER},
      NO_MEDICAL_SECOND_TYPE = #{noMedicalSecondType,jdbcType=INTEGER},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="SPU_ID" keyProperty="spuId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchCoreSpuDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into V_CORE_SPU
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="spuId != null">
        SPU_ID,
      </if>
      <if test="categoryId != null">
        CATEGORY_ID,
      </if>
      <if test="brandId != null">
        BRAND_ID,
      </if>
      <if test="spuNo != null and spuNo != ''">
        SPU_NO,
      </if>
      <if test="spuName != null and spuName != ''">
        SPU_NAME,
      </if>
      <if test="showName != null and showName != ''">
        SHOW_NAME,
      </if>
      <if test="spuLevel != null">
        SPU_LEVEL,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
      <if test="spuType != null">
        SPU_TYPE,
      </if>
      <if test="firstEngageId != null">
        FIRST_ENGAGE_ID,
      </if>
      <if test="registrationIcon != null and registrationIcon != ''">
        REGISTRATION_ICON,
      </if>
      <if test="wikiHref != null and wikiHref != ''">
        WIKI_HREF,
      </if>
      <if test="operateInfoFlag != null">
        OPERATE_INFO_FLAG,
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS,
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID,
      </if>
      <if test="hospitalTags != null and hospitalTags != ''">
        HOSPITAL_TAGS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="checkTime != null">
        CHECK_TIME,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="deleteReason != null and deleteReason != ''">
        DELETE_REASON,
      </if>
      <if test="lastCheckReason != null and lastCheckReason != ''">
        LAST_CHECK_REASON,
      </if>
      <if test="assignmentManagerId != null">
        ASSIGNMENT_MANAGER_ID,
      </if>
      <if test="assignmentAssistantId != null">
        ASSIGNMENT_ASSISTANT_ID,
      </if>
      <if test="apparatusType != null">
        APPARATUS_TYPE,
      </if>
      <if test="storageConditionTemperature != null">
        STORAGE_CONDITION_TEMPERATURE,
      </if>
      <if test="storageConditionTemperatureLowerValue != null">
        STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE,
      </if>
      <if test="storageConditionTemperatureUpperValue != null">
        STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE,
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE,
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,
      </if>
      <if test="storageConditionOthers != null and storageConditionOthers != ''">
        STORAGE_CONDITION_OTHERS,
      </if>
      <if test="technicalParameterNames != null and technicalParameterNames != ''">
        TECHNICAL_PARAMETER_NAMES,
      </if>
      <if test="secondLevelSpuType != null">
        SECOND_LEVEL_SPU_TYPE,
      </if>
      <if test="specsModel != null and specsModel != ''">
        SPECS_MODEL,
      </if>
      <if test="medicalInstrumentCatalogIncluded != null">
        MEDICAL_INSTRUMENT_CATALOG_INCLUDED,
      </if>
      <if test="goodsLevelNo != null">
        GOODS_LEVEL_NO,
      </if>
      <if test="goodsPositionNo != null">
        GOODS_POSITION_NO,
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        DISABLED_REASON,
      </if>
      <if test="noMedicalFirstType != null">
        NO_MEDICAL_FIRST_TYPE,
      </if>
      <if test="noMedicalSecondType != null">
        NO_MEDICAL_SECOND_TYPE,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="spuId != null">
        #{spuId,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=INTEGER},
      </if>
      <if test="spuNo != null and spuNo != ''">
        #{spuNo,jdbcType=VARCHAR},
      </if>
      <if test="spuName != null and spuName != ''">
        #{spuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null and showName != ''">
        #{showName,jdbcType=VARCHAR},
      </if>
      <if test="spuLevel != null">
        #{spuLevel,jdbcType=BOOLEAN},
      </if>
      <if test="status != null">
        #{status,jdbcType=BOOLEAN},
      </if>
      <if test="spuType != null">
        #{spuType,jdbcType=INTEGER},
      </if>
      <if test="firstEngageId != null">
        #{firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="registrationIcon != null and registrationIcon != ''">
        #{registrationIcon,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null and wikiHref != ''">
        #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="operateInfoFlag != null">
        #{operateInfoFlag,jdbcType=BOOLEAN},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=BOOLEAN},
      </if>
      <if test="operateInfoId != null">
        #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="hospitalTags != null and hospitalTags != ''">
        #{hospitalTags,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null and deleteReason != ''">
        #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null and lastCheckReason != ''">
        #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="assignmentManagerId != null">
        #{assignmentManagerId,jdbcType=INTEGER},
      </if>
      <if test="assignmentAssistantId != null">
        #{assignmentAssistantId,jdbcType=INTEGER},
      </if>
      <if test="apparatusType != null">
        #{apparatusType,jdbcType=TINYINT},
      </if>
      <if test="storageConditionTemperature != null">
        #{storageConditionTemperature,jdbcType=BOOLEAN},
      </if>
      <if test="storageConditionTemperatureLowerValue != null">
        #{storageConditionTemperatureLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionTemperatureUpperValue != null">
        #{storageConditionTemperatureUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionOthers != null and storageConditionOthers != ''">
        #{storageConditionOthers,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameterNames != null and technicalParameterNames != ''">
        #{technicalParameterNames,jdbcType=VARCHAR},
      </if>
      <if test="secondLevelSpuType != null">
        #{secondLevelSpuType,jdbcType=INTEGER},
      </if>
      <if test="specsModel != null and specsModel != ''">
        #{specsModel,jdbcType=VARCHAR},
      </if>
      <if test="medicalInstrumentCatalogIncluded != null">
        #{medicalInstrumentCatalogIncluded,jdbcType=BOOLEAN},
      </if>
      <if test="goodsLevelNo != null">
        #{goodsLevelNo,jdbcType=INTEGER},
      </if>
      <if test="goodsPositionNo != null">
        #{goodsPositionNo,jdbcType=INTEGER},
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        #{disabledReason,jdbcType=VARCHAR},
      </if>
      <if test="noMedicalFirstType != null">
        #{noMedicalFirstType,jdbcType=INTEGER},
      </if>
      <if test="noMedicalSecondType != null">
        #{noMedicalSecondType,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="spuId != null">
        SPU_ID = #{spuId,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null">
        CATEGORY_ID = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="brandId != null">
        BRAND_ID = #{brandId,jdbcType=INTEGER},
      </if>
      <if test="spuNo != null and spuNo != ''">
        SPU_NO = #{spuNo,jdbcType=VARCHAR},
      </if>
      <if test="spuName != null and spuName != ''">
        SPU_NAME = #{spuName,jdbcType=VARCHAR},
      </if>
      <if test="showName != null and showName != ''">
        SHOW_NAME = #{showName,jdbcType=VARCHAR},
      </if>
      <if test="spuLevel != null">
        SPU_LEVEL = #{spuLevel,jdbcType=BOOLEAN},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=BOOLEAN},
      </if>
      <if test="spuType != null">
        SPU_TYPE = #{spuType,jdbcType=INTEGER},
      </if>
      <if test="firstEngageId != null">
        FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="registrationIcon != null and registrationIcon != ''">
        REGISTRATION_ICON = #{registrationIcon,jdbcType=VARCHAR},
      </if>
      <if test="wikiHref != null and wikiHref != ''">
        WIKI_HREF = #{wikiHref,jdbcType=VARCHAR},
      </if>
      <if test="operateInfoFlag != null">
        OPERATE_INFO_FLAG = #{operateInfoFlag,jdbcType=BOOLEAN},
      </if>
      <if test="checkStatus != null">
        CHECK_STATUS = #{checkStatus,jdbcType=BOOLEAN},
      </if>
      <if test="operateInfoId != null">
        OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER},
      </if>
      <if test="hospitalTags != null and hospitalTags != ''">
        HOSPITAL_TAGS = #{hospitalTags,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        CHECK_TIME = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checker != null">
        CHECKER = #{checker,jdbcType=INTEGER},
      </if>
      <if test="deleteReason != null and deleteReason != ''">
        DELETE_REASON = #{deleteReason,jdbcType=VARCHAR},
      </if>
      <if test="lastCheckReason != null and lastCheckReason != ''">
        LAST_CHECK_REASON = #{lastCheckReason,jdbcType=VARCHAR},
      </if>
      <if test="assignmentManagerId != null">
        ASSIGNMENT_MANAGER_ID = #{assignmentManagerId,jdbcType=INTEGER},
      </if>
      <if test="assignmentAssistantId != null">
        ASSIGNMENT_ASSISTANT_ID = #{assignmentAssistantId,jdbcType=INTEGER},
      </if>
      <if test="apparatusType != null">
        APPARATUS_TYPE = #{apparatusType,jdbcType=TINYINT},
      </if>
      <if test="storageConditionTemperature != null">
        STORAGE_CONDITION_TEMPERATURE = #{storageConditionTemperature,jdbcType=BOOLEAN},
      </if>
      <if test="storageConditionTemperatureLowerValue != null">
        STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE = #{storageConditionTemperatureLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionTemperatureUpperValue != null">
        STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE = #{storageConditionTemperatureUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityLowerValue != null">
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE = #{storageConditionHumidityLowerValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionHumidityUpperValue != null">
        STORAGE_CONDITION_HUMIDITY_UPPER_VALUE = #{storageConditionHumidityUpperValue,jdbcType=FLOAT},
      </if>
      <if test="storageConditionOthers != null and storageConditionOthers != ''">
        STORAGE_CONDITION_OTHERS = #{storageConditionOthers,jdbcType=VARCHAR},
      </if>
      <if test="technicalParameterNames != null and technicalParameterNames != ''">
        TECHNICAL_PARAMETER_NAMES = #{technicalParameterNames,jdbcType=VARCHAR},
      </if>
      <if test="secondLevelSpuType != null">
        SECOND_LEVEL_SPU_TYPE = #{secondLevelSpuType,jdbcType=INTEGER},
      </if>
      <if test="specsModel != null and specsModel != ''">
        SPECS_MODEL = #{specsModel,jdbcType=VARCHAR},
      </if>
      <if test="medicalInstrumentCatalogIncluded != null">
        MEDICAL_INSTRUMENT_CATALOG_INCLUDED = #{medicalInstrumentCatalogIncluded,jdbcType=BOOLEAN},
      </if>
      <if test="goodsLevelNo != null">
        GOODS_LEVEL_NO = #{goodsLevelNo,jdbcType=INTEGER},
      </if>
      <if test="goodsPositionNo != null">
        GOODS_POSITION_NO = #{goodsPositionNo,jdbcType=INTEGER},
      </if>
      <if test="disabledReason != null and disabledReason != ''">
        DISABLED_REASON = #{disabledReason,jdbcType=VARCHAR},
      </if>
      <if test="noMedicalFirstType != null">
        NO_MEDICAL_FIRST_TYPE = #{noMedicalFirstType,jdbcType=INTEGER},
      </if>
      <if test="noMedicalSecondType != null">
        NO_MEDICAL_SECOND_TYPE = #{noMedicalSecondType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
</mapper>