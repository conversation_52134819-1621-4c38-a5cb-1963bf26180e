package com.vedeng.order.dao;

import com.vedeng.common.activiti.entity.ActivitiTaskUnDoEntity;
import com.vedeng.order.model.AuthorizationApply;
import com.vedeng.order.model.AuthorizationApplyDto;
import com.vedeng.order.model.vo.AuthorizationApplyVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface AuthorizationMapper {
    /**
     * 判断授权书申请表的授权产品数量
     * @param quoteorderId
     * @return
     */
    int getAuthorizationSum(@Param(value = "quoteorderId") Integer quoteorderId,@Param(value = "applyStatus") Integer applyStatus);

    AuthorizationApply getAuthorizationApplyInfoBySkuIdAndQuoteId(@Param(value = "quoteorderId") Integer quoteorderId, @Param(value = "skuId") Integer skuId, @Param(value = "authorizationApplyId") Integer authorizationApplyId);

    List<String> getSqNumByYearAndMonth(@Param(value = "yearAndMonth") String yearAndMonth);

    void insertAuthorizationApplyInfo(AuthorizationApply authorizationApply);

    void updateAuthorizationApplyInfo(AuthorizationApply authorizationApply);

    AuthorizationApply getAutnorizationApplyByNum(@Param(value = "authorizationApplyNum") String authorizationApplyNum);

    List<AuthorizationApply> getAuthorizationApplyByQuoteId(@Param(value = "quoteorderId") Integer quoteorderId);

    AuthorizationApply getAuthorizationIsRepeat(AuthorizationApply authorizationApply);

    AuthorizationApply getAuthorizationApplyByKeyId(Integer authorizationApplyId);

    void updateAuthorizationApplyModTimeAndStatus(@Param(value = "authorizationApplyId")Integer authorizationApplyId,@Param(value = "userId") Integer userId, @Param (value = "time")Long time, @Param(value = "applyStatus") Integer authorizationPass);

    List<AuthorizationApply> getAuthorizationApplyListByQuoteId(@Param(value = "quoteorderId") Integer quoteorderId);

    List<AuthorizationApplyDto> getAuthorizationApplylistpage(Map<String, Object> map);

    AuthorizationApply getAuthorizationApplyByNum(String authorizationApplyNum);

    void updateAuthorizationApplyReviewer(@Param(value = "authorizationApplyId") Integer authorizationApplyId, @Param(value = "verifyUsers") String verifyUsers);

    void updateAuthorizationApplyComments(@Param(value = "authorizationApplyId") Integer authorizationApplyId, @Param(value = "comments") String comments);

    List<String> getApplyPreson();

    List<AuthorizationApply> getAuthorizationApplyListByQuoteIdAndPass(@Param(value = "quoteorderId") Integer quoteorderId);

    int getAuthorizationMaxId();

    List<ActivitiTaskUnDoEntity> selectTimeoutTaskForNotice();
}
