<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="客户账期申请记录" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/finance/accountPeriod/list_accountPeriod.js?rnd=${resourceVersionKey}'></script>
<link rel="stylesheet" href="<%= basePath %>static/css/select2.css" />
<script type="text/javascript" src='<%= basePath %>static/js/select2.js'></script>
<script type="text/javascript" src='<%= basePath %>static/js/select2_locale_zh-CN.js'></script>
<div class="main-container">
    <div class="list-pages-search">
        <form method="post" id="search" action="<%=basePath%>finance/accountperiod/accountPeriodApplyListNew.do">
            <ul>
                <li>
                    <label class="infor_name">客户名称</label>
                    <input type="text" class="input-middle" name="traderName" id="traderName" value="${tapa.traderName}"/>
                </li>
                <li>
                    <label class="infor_name">审核状态</label>
                    <select class="input-middle" name="status" id="status">
                        <option value="-1">全部</option>
                        <option <c:if test="${tapa.status eq 0}">selected</c:if> value="0">审核中</option>
                        <option <c:if test="${tapa.status eq 1}">selected</c:if> value="1">审核通过</option>
                        <option <c:if test="${tapa.status eq 2}">selected</c:if> value="2">审核不通过</option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">归属人员</label>
                    <select class="input-middle" name="creator" id="creator">
                        <option value="">全部</option>
                        <c:forEach items="${userList}" var="list" varStatus="status">
                            <option value="${list.userId}" <c:if test="${tapa.creator eq list.userId}">selected</c:if>>${list.username}</option>
                        </c:forEach>
                    </select>
                </li>
                <li>
                    <label class="infor_name">日期</label>
                    <input class="Wdate f_left input-smaller96 mr5" type="text" placeholder="请选择日期" autocomplete="off"  onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endTime\')}'})" name="startTime" id="startTime" value="${startTime}">
                    <div class="gang">-</div>
                    <input class="Wdate f_left input-smaller96" type="text"	placeholder="请选择日期" autocomplete="off" onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startTime\')}'})" name="endTime" id="endTime" value="${endTime}">
                </li>
                <li>
                    <label class="infor_name">账期类型</label>
                    <select class="input-middle" name="billPeriodType" id="billPeriodType">
                        <option value="">全部</option>
                        <option value="1" <c:if test="${tapa.billPeriodType eq 1}">selected</c:if>>正式账期</option>
                        <option value="2" <c:if test="${tapa.billPeriodType eq 2}">selected</c:if>>临时账期</option>
                        <option value="3" <c:if test="${tapa.billPeriodType eq 3}">selected</c:if>>订单账期</option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">归属部门</label>
                    <input type="hidden" id="departmentInfoList" value="${departmentInfoList}"/>
                    <select class="" style="width: 178px;" multiple="multiple" name="departmentInfo" id="departmentInfo">
                        <option value=""></option>
                        <c:forEach items="${orgList}" var="org">
                            <option value="${org.orgId}">${org.orgName}</option>
                        </c:forEach>
                    </select>
                </li>
                <li>
                    <label class="infor_name">待我审核</label>
                    <select class="input-middle" name="needUserCheck" id="needUserCheck">
                        <option value="">全部</option>
                        <option value="0" <c:if test="${tapa.needUserCheck eq 0}">selected</c:if>>否</option>
                        <option value="1" <c:if test="${tapa.needUserCheck eq 1}">selected</c:if>>是</option>
                    </select>
                </li>
            </ul>
            <input type="hidden" value="${ezDomain}" id="ezDomain">
            <input type="hidden" value="${customerExport[1]}" id="ezId">
            <div class="tcenter">
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="search()">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="exportAccountPeriodList();">导出</span>
            </div>
        </form>
    </div>
    <div class="list-page">
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid3">序号</th>
                <th class="wid16">客户名称</th>
                <th class="wid5">客户身份</th>
                <th class="wid5">客户性质</th>
                <th class="wid5">客户类型</th>
                <th class="wid5">申请人</th>
                <th class="wid5">申请日期</th>
                <th class="wid5">账期类型</th>
                <th class="wid5">申请类型</th>
                <th class="wid5">账期原额度</th>
                <th class="wid5">账期可用额</th>
                <th class="wid5">逾期次数</th>
                <th class="wid5">逾期未还金额</th>
                <th class="wid5">原有效期</th>
                <%--<th class="wid5">原结算标准</th>--%>
                <th class="wid5">本次额度申请（元）</th>
                <th class="wid5">本次结算周期（天）</th>
                <th class="wid5">本次申请有效期</th>
                <th class="wid5">审核状态</th>
                <th class="wid4">操作</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach items="${customerApplyList}" var="list" varStatus="status">

                <tr>
                    <td>${status.index+1}</td>
                    <td>
                        <div class="font-blue text-ellipsis">
                            <a class="addtitle" href="javascript:void(0);" tabtitle='{"num":"view_quote_customer${list.traderId}",
													"link":"/trader/customer/baseinfo.do?traderId=${list.traderId}","title":"客户信息"}'>${list.customerName}</a>
                            <%--<c:choose>
                                <c:when test="${list.traderType eq 1}">
                                    <a class="addtitle" href="javascript:void(0);" tabtitle='{"num":"view_quote_customer${list.traderId}",
													"link":"/trader/customer/baseinfo.do?traderId=${list.traderId}","title":"客户信息"}'>${list.customerName}</a>
                                </c:when>
                                <c:otherwise>
                                    <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewsupplier${list.traderId}",
													"link":"/trader/supplier/baseinfo.do?traderId=${list.traderId}","title":"供应商信息"}'>${list.customerName}</a>
                                </c:otherwise>
                            </c:choose>--%>
                        </div>
                    </td>
                    <td>
                        客户
                    </td>
                    <td>
                        <c:if test="${empty list.customerNature}">-</c:if>${list.customerNature}
                    </td>
                    <td>
                        <c:if test="${empty list.customerType}">-</c:if>${list.customerType}
                    </td>
                    <td>
                        <c:if test="${empty list.userName}">-</c:if>${list.userName}
                    </td>
                    <td>
                        <c:if test="${empty list.addTime}">-</c:if>
                        <date:date value="${list.addTime}" format="yyyy-MM-dd" />
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${empty list.billPeriodType}">-</c:when>
                            <c:when test="${list.billPeriodType==1}">正式账期</c:when>
                            <c:when test="${list.billPeriodType==2}">临时账期</c:when>
                            <c:when test="${list.billPeriodType==3}">订单账期</c:when>
                        </c:choose>
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${empty list.operateType}">-</c:when>
                            <c:when test="${list.operateType==1}">新增</c:when>
                            <c:when test="${list.operateType==2}">调整</c:when>
                        </c:choose>
                    </td>
                    <td>
                        <c:if test="${empty list.beforeApplyAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.beforeApplyAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.creditUsableAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.creditUsableAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.countOfOverDue}">-</c:if>${list.countOfOverDue}
                    </td>
                    <td>
                        <c:if test="${empty list.unReturnedOverDueAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.unReturnedOverDueAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${empty list.beforeBillPeriodEnd}">-</c:when>
                            <c:when test="${not empty list.billPeriodType && list.billPeriodType == 3}">-</c:when>
                            <c:otherwise> <date:date value="${list.beforeBillPeriodEnd}" format="yyyy-MM-dd" /></c:otherwise>
                        </c:choose>
                    </td>
                    <%--<td>
                        <c:choose>
                            <c:when test="${empty list.settlementType}">产品开票</c:when>
                            <c:when test="${list.settlementType == 1}">产品发货</c:when>
                            <c:otherwise>产品开票</c:otherwise>
                        </c:choose>
                    </td>--%>
                    <td>
                        <c:if test="${empty list.applyAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${list.applyAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>
                        <c:if test="${empty list.settlementPeriod}">-</c:if>${list.settlementPeriod}
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${empty list.billPeriodEnd}">-</c:when>
                            <c:when test="${not empty list.billPeriodType && list.billPeriodType == 3}">-</c:when>
                            <c:otherwise><date:date value="${list.billPeriodEnd}" format="yyyy-MM-dd" /></c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${list.checkStatus eq 0}">审核中</c:when>
                            <c:when test="${list.checkStatus eq 1}">审核通过</c:when>
                            <c:when test="${list.checkStatus eq 2}">审核不通过</c:when>
                            <c:otherwise>待审核</c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <a href="javascript:void(0);"
                           class="addtitle" tabtitle='{"num":"finance_accountperiod_applyAudit_${list.billPeriodApplyId}",
										   "link":"/finance/accountperiod/getAccountPeriodApply.do?billPeriodApplyId=${list.billPeriodApplyId}&traderId=${list.traderId}&traderCustomerId=${list.customerId}&billPeriodType=${list.billPeriodType}&traderAccountPeriodApplyId=${list.billPeriodApplyId}&traderType=1",
										   "title":"账期申请审核"}'>查看</a>
                        <%--<div class="font-blue"><a href="javascript:void(0);" class="addtitle" tabtitle='{"num":"finance_accountperiod_applyAudit_${list.traderAccountPeriodApplyId}","link":"./finance/accountperiod/getAccountPeriodApply.do?traderAccountPeriodApplyId=${list.traderAccountPeriodApplyId}","title":"账期申请审核"}'>查看</a></div>--%>
                    </td>
                </tr>
            </c:forEach>
            <c:if test="${empty customerApplyList}">
                <tr>
                    <td colspan="19">
                        <!-- 查询无结果弹出 -->
                        查询无结果！请尝试使用其他搜索条件。
                    </td>
                </tr>
            </c:if>
            </tbody>
        </table>
        <tags:page page="${page}"/>
    </div>
    <%@ include file="../../common/footer.jsp"%>
