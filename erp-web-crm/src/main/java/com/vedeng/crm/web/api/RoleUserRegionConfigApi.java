package com.vedeng.crm.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.MenuDesc;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.system.dto.RoleUserRegionConfigDto;
import com.vedeng.erp.system.service.RoleUserRegionConfigApiService;
import com.vedeng.erp.system.vo.RoleUserRegionConfigQueryVO;
import com.vedeng.erp.system.vo.RoleUserRegionConfigVO;
import com.vedeng.erp.system.vo.BusinessUserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 人员与产线区域配置api
 * @menu 人员与产线区域配置
 */
@ExceptionController
@RestController
@RequestMapping("/crm/role/profile")
@Slf4j
public class RoleUserRegionConfigApi {


    @Autowired
    private RoleUserRegionConfigApiService roleUserRegionConfigApiService;

    /**
     * 分页查询人员与产线区域配置
     */
    @MenuDesc(menuValue = "C05", menuDesc = "销售关系配置")
    @RequestMapping("/role-user-region-config/page")
    public R<PageInfo<RoleUserRegionConfigVO>> page(@RequestBody PageParam<RoleUserRegionConfigQueryVO> pageParam) {
        return R.success(roleUserRegionConfigApiService.pageRoleUserRegionConfig(pageParam));
    }

    /**
     * 获取所有业务人员列表（线上/线下销售、产线人员）
     */
    @MenuDesc(menuValue = "C05", menuDesc = "销售关系配置")
    @RequestMapping("/role-user-region-config/business-users")
    public R<List<BusinessUserVO>> listAllBusinessUsers(@RequestParam(value = "userName", required = false) String userName) {
        return R.success(roleUserRegionConfigApiService.listAllBusinessUsers(userName));
    }

    /**
     * 新增人员与产线区域配置
     */
    @MenuDesc(menuValue = "C0501", menuDesc = "新建")
    @RequestMapping("/role-user-region-config/add")
    public R<Void> add(@RequestBody RoleUserRegionConfigDto dto) {
        roleUserRegionConfigApiService.saveRoleUserRegionConfig(dto);
        return R.success();
    }


    /**
     * 批量删除人员与产线区域配置
     */
    @MenuDesc(menuValue = "C0502", menuDesc = "删除")
    @RequestMapping("/role-user-region-config/batch-delete")
    public R<Void> batchRemove(@RequestBody List<Long> ids) {
        roleUserRegionConfigApiService.batchDeleteRoleUserRegionConfig(ids);
        return R.success();
    }


    /**
     * 导入数据
     */
    @MenuDesc(menuValue = "C0503", menuDesc = "导入")
    @RequestMapping("/role-user-region-config/import")
    public R<String> importData(@RequestParam("file") MultipartFile file) {
        return R.success(roleUserRegionConfigApiService.importData(file));
    }

    /**
     * 下载导入模板
     */
    @MenuDesc(menuValue = "C0503", menuDesc = "导入")
    @RequestMapping("/role-user-region-config/download-template")
    public R<String> downloadTemplate(HttpServletResponse response) {
        return R.success(roleUserRegionConfigApiService.downloadTemplate(response));
    }

    /**
     *  导出数据
     */
    @MenuDesc(menuValue = "C0504", menuDesc = "导出")
    @RequestMapping("/role-user-region-config/export")
    public R<String> exportData(HttpServletResponse response) {
        return R.success(roleUserRegionConfigApiService.exportData(null, response));
    }


}