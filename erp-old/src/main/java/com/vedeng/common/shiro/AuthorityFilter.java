package com.vedeng.common.shiro;

import com.vedeng.authorization.model.Action;
import com.vedeng.authorization.model.Position;
import com.vedeng.authorization.model.Role;
import com.vedeng.authorization.model.User;
import com.vedeng.common.redis.RedisKeyUtils;
import com.vedeng.common.shiro.cas.CasClientHelper;
import com.vedeng.common.shiro.constant.SecurityConstants;
import com.vedeng.common.util.JsonUtils;

import com.vedeng.system.service.ActionService;
import com.vedeng.system.service.PositService;
import com.vedeng.system.service.RoleService;
import com.vedeng.system.service.UserService;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.UrlPathHelper;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Deprecated
public class AuthorityFilter extends OncePerRequestFilter {

    private final static Logger LOGGER = LoggerFactory.getLogger(AuthorityFilter.class);

    private static final String STATIC_RESOURCE_REGEX = "\".*(\\\\.(js|css|jpg|png|txt|gif|jpeg|ico|bmp|json))";

    private static final Pattern STATIC_RESOURCE_PATTERN = Pattern.compile(STATIC_RESOURCE_REGEX);

    private final List<Pattern> excludePattern = new LinkedList<>();

    private static final String DOMAIN = "ivedeng.com";

    @Override
    public void initFilterBean() {
        String exclusivePath = "/batSyncToPhp.html;";
        exclusivePath += "/nopower.do;";
        exclusivePath += "/checkpreload.html;";
        exclusivePath += "/(yxg/|mjx/).*;";
        exclusivePath += "/dologin.do;";
        exclusivePath += "/login.do;";
        exclusivePath += "/logout.do;";
        exclusivePath += "/code.do;";
        exclusivePath += "/checkSession.do;";
        exclusivePath += "/selectorg.do;";
        exclusivePath += "/changeorg.do;";
        exclusivePath += "/savechangeorg.do;";
        exclusivePath += "/saveselectorg.do;";
        exclusivePath += "/fileUpload/ajaxFileUpload.do;";
        exclusivePath += "/fileUpload/uploadFile2Oss.do;";
        exclusivePath += "/fileUpload/ueditFileUpload.do;";
        exclusivePath += "/order/adkorder/add.do;";
        exclusivePath += ".*(/static/).*;";
        exclusivePath += "(/system/message/).*;";
        exclusivePath += "/phoneticTranscription/phonetic/viewContent.do;";
        exclusivePath += "/phoneticTranscription/phonetic/addComments.do;";
        exclusivePath += "/tradermsg/sendMsg/sendTraderMsg.do;";
        exclusivePath += "/tradermsg/sendMsg/sendWebaccountCertificateMsg.do;";
        exclusivePath += "/order/saleorder/saveBDAddSaleorder.do;";
        exclusivePath += "/order/saleorder/saveYgyxBuyorderToErpSaleOrder.do;";
        exclusivePath += "/system/call/pushVoiceWxMp3.do;";
        exclusivePath += "/order/saleorder/deleteBdOrder.do;";
        exclusivePath += "/order/saleorder/updateBDSaleStatus.do;";
        exclusivePath += "/goods/vgoods/viewSku.do;";
        exclusivePath += "/goods/vgoods/viewSpu.do;";
        exclusivePath += "/goods/static/vgoods/skuTip.do;";
        exclusivePath += "/vgoods/operate/handleOldData.do;";
        exclusivePath += "/trader/customer/saveMjxContactAdders.do;";
        exclusivePath += "/warehouse/warehousesout/updateWarehouseProblem.do;";
        exclusivePath += "/goods/vgoods/static/getCostPrice.do;";
        exclusivePath += "/system/role/static/savefeedback.do";

        excludePattern.addAll( loadPattern(exclusivePath).keySet());
    }

    private static Map<Pattern, String> loadPattern(String conf) {
        if (conf == null || "".equals(conf)) {
            return Collections.emptyMap();
        }
        Map<Pattern, String> map = new HashMap<Pattern, String>();
        String[] includes = conf.split(";");
        for (int i = 0; i < includes.length; i++) {
            if (conf == null || "".equals(conf)) {
                continue;
            }
            String[] include = includes[i].split(":");
            Pattern pInclude = Pattern.compile(include[0]);
            if (include.length == 2) {
                map.put(pInclude, include[1]);
            } else {
                map.put(pInclude, "1");
            }
        }
        return map;
    }
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        //sso跳过此过滤器
        return CasClientHelper.enableSingleSignOn();
    }

    @Override
    protected void doFilterInternal(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
                                    FilterChain filterChain) throws ServletException, IOException {

        UrlPathHelper urlPathHelper = new UrlPathHelper();
        String originatingUrl = urlPathHelper.getOriginatingRequestUri(httpServletRequest);

        //其他请求不走此filter
        if (isStaticUrl(originatingUrl)&&!originatingUrl.contains("getTtNumber")) {
            filterChain.doFilter(httpServletRequest, httpServletResponse);
            return;
        }

        User sessionUser = SpringContextHolder.getSessionUser(httpServletRequest.getSession());
        if (sessionUser == null) {
            httpServletResponse.sendRedirect("/login.do");
            return;
        } else {
            httpServletRequest.getSession().setAttribute("EZ_SESSION_USER_ID_KEY",sessionUser.getUserId());
        }

        filterChain.doFilter(httpServletRequest, httpServletResponse);
    }

    private boolean isStaticUrl(String originatingUrl) {
        return STATIC_RESOURCE_PATTERN.matcher(originatingUrl).matches()
                || match(excludePattern, originatingUrl);
    }

    private static boolean match(List<Pattern> list, String s) {
        if (s == null || list == null || list.size() == 0) {
            return false;
        } else {
            for (int i = 0; i < list.size(); i++) {
                Pattern pInclude = list.get(i);
                Matcher matcher = pInclude.matcher(s);
                if (matcher.matches()) {
                    return true;
                }
            }
            return false;
        }
    }

}
