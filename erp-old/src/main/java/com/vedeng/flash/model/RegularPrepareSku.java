package com.vedeng.flash.model;

import lombok.*;

import lombok.Data;

import java.math.BigInteger;

/**
 * @Description:  定品/备货实体
 * @Author:       davis
 * @Date:         2021/5/18 下午2:49
 * @Version:      1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RegularPrepareSku {

    /**
     * 定品/备货主键ID
     */
    private Integer regularId;

    /**
     * skuId
     */
    private Integer skuId;

    /**
     * 定品状态 0：启用，1：删除
     */
    private Integer skuStatus;

    /**
     * 安全库存
     */
    private Integer safeStock;

    /**
     * 调整安全库存
     */
    private Integer originSafeStock;

    /**
     * 安全库存系数
     */
    private Integer safeRatio;

    /**
     * 安全库存配置方式 0：手动输入，1：系统配置
     */
    private Integer deployType;

    /**
     * 是否调整安全库存 0：否，1：是
     */
    private Integer isAdjustStock;

    /**
     * 首次断货时间
     */
    private Long firstOutStockTime;

    /**
     * 预警等级 0：安全，1：二级预警，2：一级预警
     */
    private Integer warnLevel;

    /**
     * 是否备货 0：否，1：是
     */
    private Integer isPrepare;

    /**
     * 最后一次加入定品池时间
     */
    private Long lastAddTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 更新人
     */
    private Integer updator;

    /**
     * 更新时间
     */
    private Long updateTime;


}
