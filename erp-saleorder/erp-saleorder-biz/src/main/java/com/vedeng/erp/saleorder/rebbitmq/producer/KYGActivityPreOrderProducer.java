package com.vedeng.erp.saleorder.rebbitmq.producer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.saleorder.domain.dto.KYGActivityPreOrderProducerDto;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeEventMsgDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 科研购活动mq
 * @date 2022/12/24 14:33
 */
@Slf4j
@Component
public class KYGActivityPreOrderProducer {

    @Autowired
    @Qualifier(value = "erpRabbitTemplate")
    private RabbitTemplate erpRabbitTemplate;

    /**
     * 科研购活动异步事件队列
     */
    public final static String KYG_ACTIVITY_PRE_ORDER_QUEUE = "kygActivityPreOrderQueue";
    /**
     * 科研购活动异步事件交换机
     */
    public final static String KYG_ACTIVITY_PRE_ORDER_EXCHANGE = "kygActivityPreOrderQueueExchange";
    /**
     * 科研购活动异步事件路由key
     */
    public final static String KYG_ACTIVITY_PRE_ORDER_KEY = "kygActivityPreOrderQueueRoutingKey";


    public void senMsg(KYGActivityPreOrderProducerDto dto) {
        log.info("科研购活动mq信息：{}", JSON.toJSONString(dto));
        erpRabbitTemplate.convertAndSend(KYG_ACTIVITY_PRE_ORDER_EXCHANGE,
                KYG_ACTIVITY_PRE_ORDER_KEY, JSON.toJSONString(dto));

    }
}
