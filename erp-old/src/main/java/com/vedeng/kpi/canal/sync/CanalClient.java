package com.vedeng.kpi.canal.sync;

import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.client.CanalConnector;
import com.alibaba.otter.canal.client.CanalConnectors;
import com.alibaba.otter.canal.protocol.Message;
import net.sf.json.JSONObject;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.net.InetSocketAddress;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @date created in 2020/6/9 11:16
 */
@Component
public class CanalClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(CanalClient.class);

    @Value("${canal.server}")
//    private String canalServer = "127.0.0.1";
    private String canalServer;

    @Value("${canal.port}")
//    private Integer canalPort = 11111;
    private Integer canalPort;

    @Value("${canal.destination}")
//    private String canalDestination = "kpi-dev";
    private String canalDestination;

    @Value("${canal.subscribe}")
//    private String canalSubscribe = "QA_NEW_ERP_VEDENG_COM.T_SALEORDER,QA_NEW_ERP_VEDENG_COM.T_SALEORDER_GOODS,QA_NEW_ERP_VEDENG_COM.T_CAPITAL_BILL,QA_NEW_ERP_VEDENG_COM.T_CAPITAL_BILL_DETAIL,QA_NEW_ERP_VEDENG_COM.T_AFTER_SALES,QA_NEW_ERP_VEDENG_COM.T_AFTER_SALES_GOODS,QA_NEW_ERP_VEDENG_COM.T_AFTER_SALES_DETAIL,QA_NEW_ERP_VEDENG_COM.T_SALES_PERFORMANCE_DEPT,QA_NEW_ERP_VEDENG_COM.T_SALES_PERFORMANCE_DEPT_USER";
    private String canalSubscribe;

    /**
     * 连接重试时间间隔。默认5秒
     */
    private Integer retryInterval = 5000;

    @Resource
    private SyncDatabase syncDatabase;

//    @PostConstruct
    public void init(){

//        Executors.newSingleThreadExecutor().execute(() -> {
//
//            CanalConnector connector = CanalConnectors.newSingleConnector(new InetSocketAddress(canalServer,canalPort),canalDestination,"","");
//
//            try {
//                connector.connect();
//                connector.subscribe(canalSubscribe);
//                connector.rollback();
//                while (true) {
//                    Message message = connector.getWithoutAck(1); // 获取指定数量的数据
//                    long batchId = message.getId();
//                    int size = message.getEntries().size();
//                    if (batchId == -1 || size == 0) {
//                        try {
//                            Thread.sleep(5000);
//                        } catch (InterruptedException e) {
//                            LOGGER.error("InterruptedException : ",e);
//                        }
//                    } else {
//                        try {
//                            syncDatabase.receiveCanalEntries(message);
//                            connector.ack(batchId); // 提交确认
//                        } catch (Exception e){
//                            LOGGER.error("canal同步数据失败，数据：{}，错误：",message.toString(),e);
//                            connector.rollback(batchId);
//                        }
//                    }
//
//                }
//            } catch (Exception e){
//                LOGGER.error("canal连接异常：",e);
//            }
//            finally {
//                connector.disconnect();
//            }
//        });

    }
}
