<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>产品货期NEW</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="HBWEl499hrQ" datasource="erp-reportdatasource" fixednumber="2" fixednumberright="0" append_column_url="" append_row_url="" empty_show="" firstcol="">
  <div id="appendHead"></div>
  <div class="layui-fluid">
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
    </ul>
   </div>
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">省份</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="PROVINCE_NAME" placeholder="" style="" alias="RE.REGION_NAME" jdbctype="" data="" datatype="" validate_rules="" validate_messages=""></input>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">城市</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="CITY_NAME" placeholder="" style="" alias="R.REGION_NAME" jdbctype="" data="" datatype="" validate_rules="" validate_messages=""></input>
     </div>
    </div>
   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton"> <button item_name="导出所有" name="导出所有" url="/ezadmin/list/export-HBWEl499hrQ" opentype="APPEND_PARAM" windowname="导出所有" style type="table">导出所有</button>
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
      <th type="rowbutton" id="rowbutton"><button item_name="editButton" name="editButton" url="/ezadmin/form/form-RWUZeh75EEs?ID=${DELIVER_TIME_ID}" opentype="MODEL" windowname="编辑" style area type="single">编辑</button></th>
      <th item_name="PROVINCE_NAME" name="PROVINCE_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">省级行政区</th>
      <th item_name="CITY_NAME" name="CITY_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">地级行政区</th>
      <th item_name="DELIVER_TIME" name="DELIVER_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">发货到货时长（工作日）</th>
      <th item_name="MOD_TIME" name="MOD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">发货到货时长最近更新时间</th>
     </tr>
    </thead>
    <tbody>
     <tr>
      <td> express:</td>
      <td colspan="100"> <pre id="express" class="layui-code" orderby="" groupby="">StringBuilder sql=new StringBuilder(" 	SELECT
		D.DELIVER_TIME_ID,
		D.AREA_ID CITY_ID,
		R.REGION_NAME CITY_NAME,
		R.PARENT_ID PROVINCE_ID,
		RE.REGION_NAME PROVINCE_NAME,
		D.AREA_LEVEL,
		D.DELIVER_TIME,
    DATE_FORMAT(D.MOD_TIME,'%Y-%m-%d %H:%i:%s') MOD_TIME,
		R.PARENT_ID
	FROM
		T_DELIVER_TIME D
		LEFT JOIN T_REGION R ON D.AREA_ID = R.REGION_ID
		left join T_REGION RE ON R.PARENT_ID = RE.REGION_ID

WHERE 1=1  ");

 if(isNotBlank("_CHECKD_IDS")){
	sql.append(" and RES.DELIVER_TIME_ID  IN (" +$("_CHECKD_IDS") +" )");
 }

 list=search(sql);

 return list;</pre> </td>
     </tr>
     <tr>
      <td> count:</td>
      <td colspan="100"> <pre id="count" class="layui-code"></pre> </td>
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>