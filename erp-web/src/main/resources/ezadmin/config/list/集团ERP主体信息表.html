<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>集团ERP主体信息表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="basecompanyinfo" datasource="erp-datasourcetarget" empty_show="仅支持在贝登ERP维护此信息" fixednumber="0"   firstcol="checkbox">
<div id="appendHead">

        <style>
            .rowButtons{
                min-width: 160px;
            }
            li{
                position: relative;
                float: left;
                padding: 10px 0;
                display: list-item;
            }
            li>a{    color: #333;
                border-right: 1px solid #ddd;
                padding: 0 10px;}
            ul{    overflow: hidden;
                list-style: none outside none;}
            /*.layui-elip{*/
            /*    white-space: normal !important;*/
            /*}*/
            .ezcall{
                color: #01AAED;
                font-size: 16px !important;
                cursor: pointer;
            }
            .utext i{
                font-size: 16px !important;
            }

        </style>

</div>

<div class="layui-fluid">
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">公司名称</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="COMPANY_NAME" style="" alias="" data="" jdbctype="BODY" oper="like">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">公司简称</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="COMPANY_SHORT_NAME" style="" alias="" data="" jdbctype="BODY" oper="like">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">金蝶账套编码</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="KINGDEE_ACCOUNT_CODE" style="" alias="" data="" jdbctype="BODY" oper="=">
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">

    <table id="table" class="layui-table" style=" width:100%">
        <thead>

        <tr id="column">
            <th type="rowbutton" id="rowbutton">
                <button  type="single" url="/ezadmin/form/form-modifyBaseCompanyInfo?ID=${ID}" area="800px,500px"
                         windowname="编辑" name="playrecordBtn"  >编辑
                </button>
                <button type="table" url="/ezadmin/form/doDelete-modifyBaseCompanyInfo?ID=${ID}"
                        opentype="CONFIRM_AJAX" windowname="删除"  >删除
                </button>
            </th>
            <th name="ID" style="max-width:80px;width: 50px;">主键</th>
            <th name="COMPANY_NAME">公司名称</th>
            <th name="COMPANY_SHORT_NAME">公司简称</th>
            <th name="FRONT_END_SEQ">前端标识序号</th>
            <th name="KINGDEE_ACCOUNT_CODE">金蝶账套编码</th>
            <th name="BUSINESS_LICENSE">统一社会信用代码</th>
            <th name="COMPANY_ADDRESS">注册地址</th>
            <th name="CONTACT_PHONE">注册电话</th>
            <th name="BANK_NAME">开户银行</th>
            <th name="BANK_ACCOUNT">银行账号</th>
            <th name="CONTRACT_ADDRESS">合同签约地址</th>
            <th name="CUSTOMER_TRADER_ID">客户TRADER_ID</th>
            <th name="SUPPLIER_TRADER_ID">供应商TRADER_ID</th>
            <th name="CREATE_TIME">创建时间</th>
            <th name="UPDATE_TIME">更新时间</th>
            <th name="CREATE_USER">创建人</th>
            <th name="UPDATE_USER">更新人</th>
            <th name="IS_DELETED">是否删除</th>
            <th name="ERP_DOMAIN">ERP的地址</th>
            <th name="DETAIL_JSON">主体详细信息字段</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="" groupby="">
                    StringBuilder sql=new StringBuilder("
select ID,
       COMPANY_NAME,
       COMPANY_SHORT_NAME,
       FRONT_END_SEQ,
       KINGDEE_ACCOUNT_CODE,
       BUSINESS_LICENSE,
       COMPANY_ADDRESS,
       CONTACT_PHONE,
       BANK_NAME,
       BANK_ACCOUNT,
       CONTRACT_ADDRESS,
       CUSTOMER_TRADER_ID,
       SUPPLIER_TRADER_ID,
       CREATE_TIME,
       UPDATE_TIME,
       CREATE_USER,
       UPDATE_USER,
       IS_DELETED,
       ERP_DOMAIN,
       DETAIL_JSON
from T_BASE_COMPANY_INFO
where IS_DELETED = 0");
                    if(isNotBlank("COMPANY_NAME","request")){
                        sql.append(" and COMPANY_NAME like '%");
                        sql.append($("COMPANY_NAME"));
                        sql.append("%'");
                    }
                    if(isNotBlank("COMPANY_SHORT_NAME","request")){
                        sql.append(" and COMPANY_SHORT_NAME like '%");
                        sql.append($("COMPANY_SHORT_NAME"));
                        sql.append("%'");
                    }
                    if(isNotBlank("KINGDEE_ACCOUNT_CODE","request")){
                        sql.append(" and KINGDEE_ACCOUNT_CODE = '");
                        sql.append($("KINGDEE_ACCOUNT_CODE"));
                        sql.append("'");
                    }
                    sql.append(" order by ID ASC");
                    list= search(sql);
                    return list;
                 </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
</body>

<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
</script>
<script>
    layui.use(function () {

    })
</script>
</html>
