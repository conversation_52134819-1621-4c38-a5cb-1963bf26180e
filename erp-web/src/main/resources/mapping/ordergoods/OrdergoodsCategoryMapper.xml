<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.ordergoods.dao.OrdergoodsCategoryMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.ordergoods.model.OrdergoodsCategory" >
    <id column="ORDERGOODS_CATEGORY_ID" property="ordergoodsCategoryId" jdbcType="INTEGER" />
    <result column="ORDERGOODS_STORE_ID" property="ordergoodsStoreId" jdbcType="INTEGER" />
    <result column="PARENT_ID" property="parentId" jdbcType="INTEGER" />
    <result column="CATEGORY_NAME" property="categoryName" jdbcType="VARCHAR" />
    <result column="TREENODES" property="treenodes" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="BIT" />
    <result column="LEVEL" property="level" jdbcType="BIT" />
    <result column="MIN_AMOUNT" property="minAmount" jdbcType="DECIMAL" />
    <result column="SORT" property="sort" jdbcType="INTEGER" />
    <result column="ICON_DOMAIN" property="iconDomain" jdbcType="VARCHAR" />
    <result column="ICON_URI" property="iconUri" jdbcType="VARCHAR" />
    <result column="DESCRIPTION" property="description" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    ORDERGOODS_CATEGORY_ID, ORDERGOODS_STORE_ID, PARENT_ID, CATEGORY_NAME, TREENODES, 
    STATUS, LEVEL, MIN_AMOUNT, SORT, ICON_DOMAIN, ICON_URI, DESCRIPTION, ADD_TIME, CREATOR, 
    MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_ORDERGOODS_CATEGORY
    where ORDERGOODS_CATEGORY_ID = #{ordergoodsCategoryId,jdbcType=INTEGER}
  </select>
  <insert id="insert" parameterType="com.vedeng.ordergoods.model.OrdergoodsCategory" >
    insert into T_ORDERGOODS_CATEGORY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ordergoodsStoreId != null" >
        ORDERGOODS_STORE_ID,
      </if>
      <if test="parentId != null" >
        PARENT_ID,
      </if>
      <if test="categoryName != null" >
        CATEGORY_NAME,
      </if>
      <if test="treenodes != null" >
        TREENODES,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="level != null" >
        LEVEL,
      </if>
      <if test="minAmount != null" >
        MIN_AMOUNT,
      </if>
      <if test="sort != null" >
        SORT,
      </if>
      <if test="iconDomain != null" >
        ICON_DOMAIN,
      </if>
      <if test="iconUri != null" >
        ICON_URI,
      </if>
      <if test="description != null" >
        DESCRIPTION,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ordergoodsStoreId != null" >
        #{ordergoodsStoreId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null" >
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="treenodes != null" >
        #{treenodes,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=BIT},
      </if>
      <if test="level != null" >
        #{level,jdbcType=BIT},
      </if>
      <if test="minAmount != null" >
        #{minAmount,jdbcType=DECIMAL},
      </if>
      <if test="sort != null" >
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="iconDomain != null" >
        #{iconDomain,jdbcType=VARCHAR},
      </if>
      <if test="iconUri != null" >
        #{iconUri,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
    
    <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="ordergoodsCategoryId">
		SELECT LAST_INSERT_ID() AS ordergoodsCategoryId
    </selectKey>
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.ordergoods.model.OrdergoodsCategory" >
    update T_ORDERGOODS_CATEGORY
    <set >
      <if test="ordergoodsStoreId != null" >
        ORDERGOODS_STORE_ID = #{ordergoodsStoreId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null" >
        PARENT_ID = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null" >
        CATEGORY_NAME = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="treenodes != null" >
        TREENODES = #{treenodes,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=BIT},
      </if>
      <if test="level != null" >
        LEVEL = #{level,jdbcType=BIT},
      </if>
      <if test="minAmount != null" >
        MIN_AMOUNT = #{minAmount,jdbcType=DECIMAL},
      </if>
      <if test="sort != null" >
        SORT = #{sort,jdbcType=INTEGER},
      </if>
      <if test="iconDomain != null" >
        ICON_DOMAIN = #{iconDomain,jdbcType=VARCHAR},
      </if>
      <if test="iconUri != null" >
        ICON_URI = #{iconUri,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where ORDERGOODS_CATEGORY_ID = #{ordergoodsCategoryId,jdbcType=INTEGER}
  </update>
  
    <select id="getRootOrdergoodsCategory" resultMap="BaseResultMap">
  	select
  	<include refid="Base_Column_List" />
  	from
  	T_ORDERGOODS_CATEGORY
  	where
  		1=1
  		and PARENT_ID = 0
  		<if test="ordergoodsStoreId != null and ordergoodsStoreId > 0">
  		and ORDERGOODS_STORE_ID = #{ordergoodsStoreId,jdbcType=INTEGER}
  		</if>
  </select>
  
  <select id="getOrdergoodsCategory" resultMap="BaseResultMap">
  	select
  	<include refid="Base_Column_List" />
  	from
  	T_ORDERGOODS_CATEGORY
  	where
  		1=1
  		<if test="parentId != null and parentId > 0">
  		and PARENT_ID = #{parentId,jdbcType=INTEGER} 
  		</if>
  		<if test="ordergoodsStoreId != null and ordergoodsStoreId > 0">
  		and ORDERGOODS_STORE_ID = #{ordergoodsStoreId,jdbcType=INTEGER}
  		</if>
  </select>
  
  <select id="getOrdergoodsCategoryByCate" parameterType="com.vedeng.ordergoods.model.OrdergoodsCategory" resultMap="BaseResultMap">
  	select
  	<include refid="Base_Column_List" />
  	from
  	T_ORDERGOODS_CATEGORY
  	where
  		1=1
  		<if test="ordergoodsStoreId != null and ordergoodsStoreId > 0">
  		and ORDERGOODS_STORE_ID = #{ordergoodsStoreId,jdbcType=INTEGER}
  		</if>
  		<if test="categoryName != null">
  		and CATEGORY_NAME = #{categoryName,jdbcType=VARCHAR}
  		</if>
  		<if test="level != null" >
        and LEVEL = #{level,jdbcType=BIT}
      </if>
  </select>
  <select id="getCategoryNamesByIds" resultType="java.lang.String">
  	select
  	group_concat(CATEGORY_NAME)
  	from
  	T_ORDERGOODS_CATEGORY
  	where
  	ORDERGOODS_CATEGORY_ID in
  	<foreach collection="categoryIds" item="categoryId" open="(" close=")" separator=",">
		#{categoryId,jdbcType=INTEGER}
	</foreach>
  </select>
</mapper>