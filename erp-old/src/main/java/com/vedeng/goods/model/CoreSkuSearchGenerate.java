package com.vedeng.goods.model;

import java.math.BigDecimal;
import java.util.Date;

public class CoreSkuSearchGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.SKU_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer skuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.SPU_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer spuId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.CHECK_STATUS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer checkStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.MODEL
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String model;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.SPEC
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String spec;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.SKU_NO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String skuNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.SKU_NAME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String skuName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.SHOW_NAME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String showName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.MATERIAL_CODE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String materialCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.SUPPLY_MODEL
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String supplyModel;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.IS_STOCKUP
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String isStockup;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.WIKI_HREF
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String wikiHref;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.TECHNICAL_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String technicalParameter;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.PERFORMANCE_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String performanceParameter;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.SPEC_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String specParameter;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.BASE_UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer baseUnitId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.MIN_ORDER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal minOrder;

    /**
     * 中包装数量
     */
    private Integer midPackageNum;
    /**
     * 箱包装数量
     */
    private Integer boxPackageNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.GOODS_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal goodsLength;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.GOODS_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal goodsWidth;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.GOODS_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal goodsHeight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.PACKAGE_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal packageLength;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.PACKAGE_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal packageWidth;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.PACKAGE_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal packageHeight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.NET_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal netWeight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.GROSS_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal grossWeight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer unitId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.CHANGE_NUM
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Long changeNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.PACKING_LIST
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String packingList;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.AFTER_SALE_CONTENT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String afterSaleContent;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.QA_YEARS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String qaYears;

    /**
     * 存储条件（温度)
     *
     * @since ERP_SV_2020_61
     * @see com.vedeng.goods.enums.GoodsStorageConditionTemperatureEnum
     */
    private Integer storageConditionOne;

    /**
     * 存储条件（温度,单位：摄氏度），其他温度范围值存储较小值
     *
     * @since ERP_SV_2020_61
     */
    private Float storageConditionOneLowerValue;

    /**
     * 存储条件（温度，单位：摄氏度），其他温度范围值存储较大值
     *
     * @since ERP_LV_2020_67
     */
    private Float storageConditionOneUpperValue;

    /**
     * 存储条件（湿度，单位：%）：范围值存储较小的值
     *
     * @since ERP_LV_2020_67
     */
    private Float storageConditionHumidityLowerValue;

    /**
     * 存储条件（湿度，单位：%）：范围值存储较大的
     *
     * @since ERP_LV_2020_67
     */
    private Float storageConditionHumidityUpperValue;

    /**
     * 存储条件（其他）
     *
     * @since ERP_LV_2020_67
     * @see com.vedeng.goods.enums.GoodsStorageConditionOthersEnum
     */
    private String storageConditionTwo;


    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.EFFECTIVE_DAY_UNIT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer effectiveDayUnit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.EFFECTIVE_DAYS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String effectiveDays;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.QA_RULE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String qaRule;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.QA_OUT_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal qaOutPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.QA_RESPONSE_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Long qaResponseTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.HAS_BACKUP_MACHINE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String hasBackupMachine;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.SUPPLIER_EXTEND_GUARANTEE_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal supplierExtendGuaranteePrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.CORE_PARTS_PRICE_FID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer corePartsPriceFid;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.RETURN_GOODS_CONDITIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer returnGoodsConditions;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.FREIGHT_INTRODUCTIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String freightIntroductions;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.EXCHANGE_GOODS_CONDITIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String exchangeGoodsConditions;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.EXCHANGE_GOODS_METHOD
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String exchangeGoodsMethod;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.GOODS_COMMENTS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String goodsComments;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.STATUS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.ADD_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Date addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.CREATOR
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.MOD_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Date modTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.UPDATER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.CHECK_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Date checkTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.CHECKER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer checker;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.OPERATE_INFO_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer operateInfoId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.DELETE_REASON
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String deleteReason;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.LAST_CHECK_REASON
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String lastCheckReason;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.TAX_CATEGORY_NO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private String taxCategoryNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.JX_MARKET_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal jxMarketPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.JX_SALE_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private BigDecimal jxSalePrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.JX_FLAG
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer jxFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column V_CORE_SKU_SEARCH.SOURCE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    private Integer source;

    /**
     * 商品级别
     *
     * @since ERP_LV_2020_105
     */
    private Integer goodsLevelNo;

    /**
     * 商品档位
     *
     * @since  ERP_LV_2020_105
     */
    private Integer goodsPositionNo;

    /**
     *  归属组织Id集合
     */
    private String orgIdList;

    /**
     *  是否可售
     */
    private Integer isAvailableSale;

    /**
     *  已推送的区域商城id集合
     */
    private String pushedOrgIdList;

    /**
     * 配置清单
     */
    private String configurationList;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.SKU_ID
     *
     * @return the value of V_CORE_SKU_SEARCH.SKU_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getSkuId() {
        return skuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.SKU_ID
     *
     * @param skuId the value for V_CORE_SKU_SEARCH.SKU_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.SPU_ID
     *
     * @return the value of V_CORE_SKU_SEARCH.SPU_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getSpuId() {
        return spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.SPU_ID
     *
     * @param spuId the value for V_CORE_SKU_SEARCH.SPU_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSpuId(Integer spuId) {
        this.spuId = spuId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.CHECK_STATUS
     *
     * @return the value of V_CORE_SKU_SEARCH.CHECK_STATUS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getCheckStatus() {
        return checkStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.CHECK_STATUS
     *
     * @param checkStatus the value for V_CORE_SKU_SEARCH.CHECK_STATUS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setCheckStatus(Integer checkStatus) {
        this.checkStatus = checkStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.MODEL
     *
     * @return the value of V_CORE_SKU_SEARCH.MODEL
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getModel() {
        return model;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.MODEL
     *
     * @param model the value for V_CORE_SKU_SEARCH.MODEL
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setModel(String model) {
        this.model = model;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.SPEC
     *
     * @return the value of V_CORE_SKU_SEARCH.SPEC
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getSpec() {
        return spec;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.SPEC
     *
     * @param spec the value for V_CORE_SKU_SEARCH.SPEC
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSpec(String spec) {
        this.spec = spec;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.SKU_NO
     *
     * @return the value of V_CORE_SKU_SEARCH.SKU_NO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getSkuNo() {
        return skuNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.SKU_NO
     *
     * @param skuNo the value for V_CORE_SKU_SEARCH.SKU_NO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.SKU_NAME
     *
     * @return the value of V_CORE_SKU_SEARCH.SKU_NAME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getSkuName() {
        return skuName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.SKU_NAME
     *
     * @param skuName the value for V_CORE_SKU_SEARCH.SKU_NAME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.SHOW_NAME
     *
     * @return the value of V_CORE_SKU_SEARCH.SHOW_NAME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getShowName() {
        return showName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.SHOW_NAME
     *
     * @param showName the value for V_CORE_SKU_SEARCH.SHOW_NAME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setShowName(String showName) {
        this.showName = showName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.MATERIAL_CODE
     *
     * @return the value of V_CORE_SKU_SEARCH.MATERIAL_CODE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getMaterialCode() {
        return materialCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.MATERIAL_CODE
     *
     * @param materialCode the value for V_CORE_SKU_SEARCH.MATERIAL_CODE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.SUPPLY_MODEL
     *
     * @return the value of V_CORE_SKU_SEARCH.SUPPLY_MODEL
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getSupplyModel() {
        return supplyModel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.SUPPLY_MODEL
     *
     * @param supplyModel the value for V_CORE_SKU_SEARCH.SUPPLY_MODEL
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSupplyModel(String supplyModel) {
        this.supplyModel = supplyModel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.IS_STOCKUP
     *
     * @return the value of V_CORE_SKU_SEARCH.IS_STOCKUP
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getIsStockup() {
        return isStockup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.IS_STOCKUP
     *
     * @param isStockup the value for V_CORE_SKU_SEARCH.IS_STOCKUP
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setIsStockup(String isStockup) {
        this.isStockup = isStockup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.WIKI_HREF
     *
     * @return the value of V_CORE_SKU_SEARCH.WIKI_HREF
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getWikiHref() {
        return wikiHref;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.WIKI_HREF
     *
     * @param wikiHref the value for V_CORE_SKU_SEARCH.WIKI_HREF
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setWikiHref(String wikiHref) {
        this.wikiHref = wikiHref;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.TECHNICAL_PARAMETER
     *
     * @return the value of V_CORE_SKU_SEARCH.TECHNICAL_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getTechnicalParameter() {
        return technicalParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.TECHNICAL_PARAMETER
     *
     * @param technicalParameter the value for V_CORE_SKU_SEARCH.TECHNICAL_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setTechnicalParameter(String technicalParameter) {
        this.technicalParameter = technicalParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.PERFORMANCE_PARAMETER
     *
     * @return the value of V_CORE_SKU_SEARCH.PERFORMANCE_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getPerformanceParameter() {
        return performanceParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.PERFORMANCE_PARAMETER
     *
     * @param performanceParameter the value for V_CORE_SKU_SEARCH.PERFORMANCE_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setPerformanceParameter(String performanceParameter) {
        this.performanceParameter = performanceParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.SPEC_PARAMETER
     *
     * @return the value of V_CORE_SKU_SEARCH.SPEC_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getSpecParameter() {
        return specParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.SPEC_PARAMETER
     *
     * @param specParameter the value for V_CORE_SKU_SEARCH.SPEC_PARAMETER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSpecParameter(String specParameter) {
        this.specParameter = specParameter;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.BASE_UNIT_ID
     *
     * @return the value of V_CORE_SKU_SEARCH.BASE_UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getBaseUnitId() {
        return baseUnitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.BASE_UNIT_ID
     *
     * @param baseUnitId the value for V_CORE_SKU_SEARCH.BASE_UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setBaseUnitId(Integer baseUnitId) {
        this.baseUnitId = baseUnitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.MIN_ORDER
     *
     * @return the value of V_CORE_SKU_SEARCH.MIN_ORDER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getMinOrder() {
        return minOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.MIN_ORDER
     *
     * @param minOrder the value for V_CORE_SKU_SEARCH.MIN_ORDER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setMinOrder(BigDecimal minOrder) {
        this.minOrder = minOrder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.GOODS_LENGTH
     *
     * @return the value of V_CORE_SKU_SEARCH.GOODS_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getGoodsLength() {
        return goodsLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.GOODS_LENGTH
     *
     * @param goodsLength the value for V_CORE_SKU_SEARCH.GOODS_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setGoodsLength(BigDecimal goodsLength) {
        this.goodsLength = goodsLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.GOODS_WIDTH
     *
     * @return the value of V_CORE_SKU_SEARCH.GOODS_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getGoodsWidth() {
        return goodsWidth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.GOODS_WIDTH
     *
     * @param goodsWidth the value for V_CORE_SKU_SEARCH.GOODS_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setGoodsWidth(BigDecimal goodsWidth) {
        this.goodsWidth = goodsWidth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.GOODS_HEIGHT
     *
     * @return the value of V_CORE_SKU_SEARCH.GOODS_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getGoodsHeight() {
        return goodsHeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.GOODS_HEIGHT
     *
     * @param goodsHeight the value for V_CORE_SKU_SEARCH.GOODS_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setGoodsHeight(BigDecimal goodsHeight) {
        this.goodsHeight = goodsHeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.PACKAGE_LENGTH
     *
     * @return the value of V_CORE_SKU_SEARCH.PACKAGE_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getPackageLength() {
        return packageLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.PACKAGE_LENGTH
     *
     * @param packageLength the value for V_CORE_SKU_SEARCH.PACKAGE_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setPackageLength(BigDecimal packageLength) {
        this.packageLength = packageLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.PACKAGE_WIDTH
     *
     * @return the value of V_CORE_SKU_SEARCH.PACKAGE_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getPackageWidth() {
        return packageWidth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.PACKAGE_WIDTH
     *
     * @param packageWidth the value for V_CORE_SKU_SEARCH.PACKAGE_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setPackageWidth(BigDecimal packageWidth) {
        this.packageWidth = packageWidth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.PACKAGE_HEIGHT
     *
     * @return the value of V_CORE_SKU_SEARCH.PACKAGE_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getPackageHeight() {
        return packageHeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.PACKAGE_HEIGHT
     *
     * @param packageHeight the value for V_CORE_SKU_SEARCH.PACKAGE_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setPackageHeight(BigDecimal packageHeight) {
        this.packageHeight = packageHeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.NET_WEIGHT
     *
     * @return the value of V_CORE_SKU_SEARCH.NET_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getNetWeight() {
        return netWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.NET_WEIGHT
     *
     * @param netWeight the value for V_CORE_SKU_SEARCH.NET_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.GROSS_WEIGHT
     *
     * @return the value of V_CORE_SKU_SEARCH.GROSS_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.GROSS_WEIGHT
     *
     * @param grossWeight the value for V_CORE_SKU_SEARCH.GROSS_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.UNIT_ID
     *
     * @return the value of V_CORE_SKU_SEARCH.UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getUnitId() {
        return unitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.UNIT_ID
     *
     * @param unitId the value for V_CORE_SKU_SEARCH.UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.CHANGE_NUM
     *
     * @return the value of V_CORE_SKU_SEARCH.CHANGE_NUM
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Long getChangeNum() {
        return changeNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.CHANGE_NUM
     *
     * @param changeNum the value for V_CORE_SKU_SEARCH.CHANGE_NUM
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setChangeNum(Long changeNum) {
        this.changeNum = changeNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.PACKING_LIST
     *
     * @return the value of V_CORE_SKU_SEARCH.PACKING_LIST
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getPackingList() {
        return packingList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.PACKING_LIST
     *
     * @param packingList the value for V_CORE_SKU_SEARCH.PACKING_LIST
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setPackingList(String packingList) {
        this.packingList = packingList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.AFTER_SALE_CONTENT
     *
     * @return the value of V_CORE_SKU_SEARCH.AFTER_SALE_CONTENT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getAfterSaleContent() {
        return afterSaleContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.AFTER_SALE_CONTENT
     *
     * @param afterSaleContent the value for V_CORE_SKU_SEARCH.AFTER_SALE_CONTENT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setAfterSaleContent(String afterSaleContent) {
        this.afterSaleContent = afterSaleContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.QA_YEARS
     *
     * @return the value of V_CORE_SKU_SEARCH.QA_YEARS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getQaYears() {
        return qaYears;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.QA_YEARS
     *
     * @param qaYears the value for V_CORE_SKU_SEARCH.QA_YEARS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setQaYears(String qaYears) {
        this.qaYears = qaYears;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.STORAGE_CONDITION_ONE
     *
     * @return the value of V_CORE_SKU_SEARCH.STORAGE_CONDITION_ONE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getStorageConditionOne() {
        return storageConditionOne;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.STORAGE_CONDITION_ONE
     *
     * @param storageConditionOne the value for V_CORE_SKU_SEARCH.STORAGE_CONDITION_ONE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setStorageConditionOne(Integer storageConditionOne) {
        this.storageConditionOne = storageConditionOne;
    }

    public Float getStorageConditionOneLowerValue() {
        return storageConditionOneLowerValue;
    }

    public void setStorageConditionOneLowerValue(Float storageConditionOneLowerValue) {
        this.storageConditionOneLowerValue = storageConditionOneLowerValue;
    }

    public Float getStorageConditionOneUpperValue() {
        return storageConditionOneUpperValue;
    }

    public void setStorageConditionOneUpperValue(Float storageConditionOneUpperValue) {
        this.storageConditionOneUpperValue = storageConditionOneUpperValue;
    }

    public Float getStorageConditionHumidityLowerValue() {
        return storageConditionHumidityLowerValue;
    }

    public void setStorageConditionHumidityLowerValue(Float storageConditionHumidityLowerValue) {
        this.storageConditionHumidityLowerValue = storageConditionHumidityLowerValue;
    }

    public Float getStorageConditionHumidityUpperValue() {
        return storageConditionHumidityUpperValue;
    }

    public void setStorageConditionHumidityUpperValue(Float storageConditionHumidityUpperValue) {
        this.storageConditionHumidityUpperValue = storageConditionHumidityUpperValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.STORAGE_CONDITION_TWO
     *
     * @return the value of V_CORE_SKU_SEARCH.STORAGE_CONDITION_TWO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getStorageConditionTwo() {
        return storageConditionTwo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.STORAGE_CONDITION_TWO
     *
     * @param storageConditionTwo the value for V_CORE_SKU_SEARCH.STORAGE_CONDITION_TWO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setStorageConditionTwo(String storageConditionTwo) {
        this.storageConditionTwo = storageConditionTwo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.EFFECTIVE_DAY_UNIT
     *
     * @return the value of V_CORE_SKU_SEARCH.EFFECTIVE_DAY_UNIT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getEffectiveDayUnit() {
        return effectiveDayUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.EFFECTIVE_DAY_UNIT
     *
     * @param effectiveDayUnit the value for V_CORE_SKU_SEARCH.EFFECTIVE_DAY_UNIT
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setEffectiveDayUnit(Integer effectiveDayUnit) {
        this.effectiveDayUnit = effectiveDayUnit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.EFFECTIVE_DAYS
     *
     * @return the value of V_CORE_SKU_SEARCH.EFFECTIVE_DAYS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getEffectiveDays() {
        return effectiveDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.EFFECTIVE_DAYS
     *
     * @param effectiveDays the value for V_CORE_SKU_SEARCH.EFFECTIVE_DAYS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setEffectiveDays(String effectiveDays) {
        this.effectiveDays = effectiveDays;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.QA_RULE
     *
     * @return the value of V_CORE_SKU_SEARCH.QA_RULE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getQaRule() {
        return qaRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.QA_RULE
     *
     * @param qaRule the value for V_CORE_SKU_SEARCH.QA_RULE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setQaRule(String qaRule) {
        this.qaRule = qaRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.QA_OUT_PRICE
     *
     * @return the value of V_CORE_SKU_SEARCH.QA_OUT_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getQaOutPrice() {
        return qaOutPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.QA_OUT_PRICE
     *
     * @param qaOutPrice the value for V_CORE_SKU_SEARCH.QA_OUT_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setQaOutPrice(BigDecimal qaOutPrice) {
        this.qaOutPrice = qaOutPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.QA_RESPONSE_TIME
     *
     * @return the value of V_CORE_SKU_SEARCH.QA_RESPONSE_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Long getQaResponseTime() {
        return qaResponseTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.QA_RESPONSE_TIME
     *
     * @param qaResponseTime the value for V_CORE_SKU_SEARCH.QA_RESPONSE_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setQaResponseTime(Long qaResponseTime) {
        this.qaResponseTime = qaResponseTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.HAS_BACKUP_MACHINE
     *
     * @return the value of V_CORE_SKU_SEARCH.HAS_BACKUP_MACHINE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getHasBackupMachine() {
        return hasBackupMachine;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.HAS_BACKUP_MACHINE
     *
     * @param hasBackupMachine the value for V_CORE_SKU_SEARCH.HAS_BACKUP_MACHINE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setHasBackupMachine(String hasBackupMachine) {
        this.hasBackupMachine = hasBackupMachine;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.SUPPLIER_EXTEND_GUARANTEE_PRICE
     *
     * @return the value of V_CORE_SKU_SEARCH.SUPPLIER_EXTEND_GUARANTEE_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getSupplierExtendGuaranteePrice() {
        return supplierExtendGuaranteePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.SUPPLIER_EXTEND_GUARANTEE_PRICE
     *
     * @param supplierExtendGuaranteePrice the value for V_CORE_SKU_SEARCH.SUPPLIER_EXTEND_GUARANTEE_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSupplierExtendGuaranteePrice(BigDecimal supplierExtendGuaranteePrice) {
        this.supplierExtendGuaranteePrice = supplierExtendGuaranteePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.CORE_PARTS_PRICE_FID
     *
     * @return the value of V_CORE_SKU_SEARCH.CORE_PARTS_PRICE_FID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getCorePartsPriceFid() {
        return corePartsPriceFid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.CORE_PARTS_PRICE_FID
     *
     * @param corePartsPriceFid the value for V_CORE_SKU_SEARCH.CORE_PARTS_PRICE_FID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setCorePartsPriceFid(Integer corePartsPriceFid) {
        this.corePartsPriceFid = corePartsPriceFid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.RETURN_GOODS_CONDITIONS
     *
     * @return the value of V_CORE_SKU_SEARCH.RETURN_GOODS_CONDITIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getReturnGoodsConditions() {
        return returnGoodsConditions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.RETURN_GOODS_CONDITIONS
     *
     * @param returnGoodsConditions the value for V_CORE_SKU_SEARCH.RETURN_GOODS_CONDITIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setReturnGoodsConditions(Integer returnGoodsConditions) {
        this.returnGoodsConditions = returnGoodsConditions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.FREIGHT_INTRODUCTIONS
     *
     * @return the value of V_CORE_SKU_SEARCH.FREIGHT_INTRODUCTIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getFreightIntroductions() {
        return freightIntroductions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.FREIGHT_INTRODUCTIONS
     *
     * @param freightIntroductions the value for V_CORE_SKU_SEARCH.FREIGHT_INTRODUCTIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setFreightIntroductions(String freightIntroductions) {
        this.freightIntroductions = freightIntroductions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.EXCHANGE_GOODS_CONDITIONS
     *
     * @return the value of V_CORE_SKU_SEARCH.EXCHANGE_GOODS_CONDITIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getExchangeGoodsConditions() {
        return exchangeGoodsConditions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.EXCHANGE_GOODS_CONDITIONS
     *
     * @param exchangeGoodsConditions the value for V_CORE_SKU_SEARCH.EXCHANGE_GOODS_CONDITIONS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setExchangeGoodsConditions(String exchangeGoodsConditions) {
        this.exchangeGoodsConditions = exchangeGoodsConditions;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.EXCHANGE_GOODS_METHOD
     *
     * @return the value of V_CORE_SKU_SEARCH.EXCHANGE_GOODS_METHOD
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getExchangeGoodsMethod() {
        return exchangeGoodsMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.EXCHANGE_GOODS_METHOD
     *
     * @param exchangeGoodsMethod the value for V_CORE_SKU_SEARCH.EXCHANGE_GOODS_METHOD
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setExchangeGoodsMethod(String exchangeGoodsMethod) {
        this.exchangeGoodsMethod = exchangeGoodsMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.GOODS_COMMENTS
     *
     * @return the value of V_CORE_SKU_SEARCH.GOODS_COMMENTS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getGoodsComments() {
        return goodsComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.GOODS_COMMENTS
     *
     * @param goodsComments the value for V_CORE_SKU_SEARCH.GOODS_COMMENTS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setGoodsComments(String goodsComments) {
        this.goodsComments = goodsComments;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.STATUS
     *
     * @return the value of V_CORE_SKU_SEARCH.STATUS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.STATUS
     *
     * @param status the value for V_CORE_SKU_SEARCH.STATUS
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.ADD_TIME
     *
     * @return the value of V_CORE_SKU_SEARCH.ADD_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.ADD_TIME
     *
     * @param addTime the value for V_CORE_SKU_SEARCH.ADD_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.CREATOR
     *
     * @return the value of V_CORE_SKU_SEARCH.CREATOR
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.CREATOR
     *
     * @param creator the value for V_CORE_SKU_SEARCH.CREATOR
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.MOD_TIME
     *
     * @return the value of V_CORE_SKU_SEARCH.MOD_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Date getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.MOD_TIME
     *
     * @param modTime the value for V_CORE_SKU_SEARCH.MOD_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.UPDATER
     *
     * @return the value of V_CORE_SKU_SEARCH.UPDATER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.UPDATER
     *
     * @param updater the value for V_CORE_SKU_SEARCH.UPDATER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.CHECK_TIME
     *
     * @return the value of V_CORE_SKU_SEARCH.CHECK_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Date getCheckTime() {
        return checkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.CHECK_TIME
     *
     * @param checkTime the value for V_CORE_SKU_SEARCH.CHECK_TIME
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.CHECKER
     *
     * @return the value of V_CORE_SKU_SEARCH.CHECKER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getChecker() {
        return checker;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.CHECKER
     *
     * @param checker the value for V_CORE_SKU_SEARCH.CHECKER
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setChecker(Integer checker) {
        this.checker = checker;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.OPERATE_INFO_ID
     *
     * @return the value of V_CORE_SKU_SEARCH.OPERATE_INFO_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getOperateInfoId() {
        return operateInfoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.OPERATE_INFO_ID
     *
     * @param operateInfoId the value for V_CORE_SKU_SEARCH.OPERATE_INFO_ID
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setOperateInfoId(Integer operateInfoId) {
        this.operateInfoId = operateInfoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.DELETE_REASON
     *
     * @return the value of V_CORE_SKU_SEARCH.DELETE_REASON
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getDeleteReason() {
        return deleteReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.DELETE_REASON
     *
     * @param deleteReason the value for V_CORE_SKU_SEARCH.DELETE_REASON
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setDeleteReason(String deleteReason) {
        this.deleteReason = deleteReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.LAST_CHECK_REASON
     *
     * @return the value of V_CORE_SKU_SEARCH.LAST_CHECK_REASON
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getLastCheckReason() {
        return lastCheckReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.LAST_CHECK_REASON
     *
     * @param lastCheckReason the value for V_CORE_SKU_SEARCH.LAST_CHECK_REASON
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setLastCheckReason(String lastCheckReason) {
        this.lastCheckReason = lastCheckReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.TAX_CATEGORY_NO
     *
     * @return the value of V_CORE_SKU_SEARCH.TAX_CATEGORY_NO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public String getTaxCategoryNo() {
        return taxCategoryNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.TAX_CATEGORY_NO
     *
     * @param taxCategoryNo the value for V_CORE_SKU_SEARCH.TAX_CATEGORY_NO
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setTaxCategoryNo(String taxCategoryNo) {
        this.taxCategoryNo = taxCategoryNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.JX_MARKET_PRICE
     *
     * @return the value of V_CORE_SKU_SEARCH.JX_MARKET_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getJxMarketPrice() {
        return jxMarketPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.JX_MARKET_PRICE
     *
     * @param jxMarketPrice the value for V_CORE_SKU_SEARCH.JX_MARKET_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setJxMarketPrice(BigDecimal jxMarketPrice) {
        this.jxMarketPrice = jxMarketPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.JX_SALE_PRICE
     *
     * @return the value of V_CORE_SKU_SEARCH.JX_SALE_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public BigDecimal getJxSalePrice() {
        return jxSalePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.JX_SALE_PRICE
     *
     * @param jxSalePrice the value for V_CORE_SKU_SEARCH.JX_SALE_PRICE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setJxSalePrice(BigDecimal jxSalePrice) {
        this.jxSalePrice = jxSalePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.JX_FLAG
     *
     * @return the value of V_CORE_SKU_SEARCH.JX_FLAG
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getJxFlag() {
        return jxFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.JX_FLAG
     *
     * @param jxFlag the value for V_CORE_SKU_SEARCH.JX_FLAG
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setJxFlag(Integer jxFlag) {
        this.jxFlag = jxFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column V_CORE_SKU_SEARCH.SOURCE
     *
     * @return the value of V_CORE_SKU_SEARCH.SOURCE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public Integer getSource() {
        return source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column V_CORE_SKU_SEARCH.SOURCE
     *
     * @param source the value for V_CORE_SKU_SEARCH.SOURCE
     *
     * @mbggenerated Sat Aug 10 17:13:52 CST 2019
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getMidPackageNum() {
        return midPackageNum;
    }

    public void setMidPackageNum(Integer midPackageNum) {
        this.midPackageNum = midPackageNum;
    }

    public Integer getBoxPackageNum() {
        return boxPackageNum;
    }

    public void setBoxPackageNum(Integer boxPackageNum) {
        this.boxPackageNum = boxPackageNum;
    }


    public Integer getGoodsLevelNo() {
        return goodsLevelNo;
    }

    public void setGoodsLevelNo(Integer goodsLevelNo) {
        this.goodsLevelNo = goodsLevelNo;
    }

    public Integer getGoodsPositionNo() {
        return goodsPositionNo;
    }

    public void setGoodsPositionNo(Integer goodsPositionNo) {
        this.goodsPositionNo = goodsPositionNo;
    }

    public String getOrgIdList() {
        return orgIdList;
    }

    public void setOrgIdList(String orgIdList) {
        this.orgIdList = orgIdList;
    }

    public Integer getIsAvailableSale() {
        return isAvailableSale;
    }

    public void setIsAvailableSale(Integer isAvailableSale) {
        this.isAvailableSale = isAvailableSale;
    }

    public String getPushedOrgIdList() {
        return pushedOrgIdList;
    }

    public void setPushedOrgIdList(String pushedOrgIdList) {
        this.pushedOrgIdList = pushedOrgIdList;
    }

    public String getConfigurationList() {
        return configurationList;
    }

    public void setConfigurationList(String configurationList) {
        this.configurationList = configurationList;
    }
}
