<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.doc.mapper.DocBuzTagMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.doc.domain.entity.DocBuzTag">
            <id property="id" column="ID" jdbcType="INTEGER"/>
            <result property="buzType" column="BUZ_TYPE" jdbcType="INTEGER"/>
            <result property="buzTagName" column="BUZ_TAG_NAME" jdbcType="VARCHAR"/>
            <result property="required" column="REQUIRED" jdbcType="BOOLEAN"/>
            <result property="maxCount" column="MAX_COUNT" jdbcType="INTEGER"/>
            <result property="maintainValidPeriod" column="MAINTAIN_VALID_PERIOD" jdbcType="BOOLEAN"/>
            <result property="saveMaintainLog" column="SAVE_MAINTAIN_LOG" jdbcType="BOOLEAN"/>
            <result property="needHasStamp" column="NEED_HAS_STAMP" jdbcType="TINYINT"/>
            <result property="source" column="SOURCE" jdbcType="BOOLEAN"/>
            <result property="externalUrl" column="EXTERNAL_URL" jdbcType="VARCHAR"/>
            <result property="externalFunctionId" column="EXTERNAL_FUNCTION_ID" jdbcType="VARCHAR"/>
            <result property="hidden" column="HIDDEN" jdbcType="BOOLEAN"/>
            <result property="isDelete" column="IS_DELETE" jdbcType="BOOLEAN"/>
            <result property="addTime" column="ADD_TIME" jdbcType="BIGINT"/>
            <result property="creator" column="CREATOR" jdbcType="INTEGER"/>
            <result property="modTime" column="MOD_TIME" jdbcType="BIGINT"/>
            <result property="updater" column="UPDATER" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,BUZ_TYPE,BUZ_TAG_NAME,
        REQUIRED,MAX_COUNT,MAINTAIN_VALID_PERIOD,
        SAVE_MAINTAIN_LOG,NEED_HAS_STAMP,SOURCE,
        EXTERNAL_URL,EXTERNAL_FUNCTION_ID,HIDDEN,
        IS_DELETE,ADD_TIME,
        CREATOR,MOD_TIME,UPDATER
    </sql>
    <select id="selectListByBuzType" resultType="com.vedeng.doc.domain.entity.DocBuzTag">
        SELECT
            <include refid="Base_Column_List" />
        FROM T_DOC_BUZ_TAG
        WHERE BUZ_TYPE = #{buzType, jdbcType=INTEGER}
            AND IS_DELETE = 0
            AND HIDDEN = 0
    </select>
</mapper>
