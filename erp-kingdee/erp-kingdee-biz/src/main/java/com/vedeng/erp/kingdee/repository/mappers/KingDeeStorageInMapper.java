package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeStorageInEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface KingDeeStorageInMapper {
    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeStorageInEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeStorageInEntity record);

    int batchInsert(@Param("list") List<KingDeeStorageInEntity> list);
}