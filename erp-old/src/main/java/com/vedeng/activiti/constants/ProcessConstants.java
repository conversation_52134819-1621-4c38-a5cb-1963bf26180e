package com.vedeng.activiti.constants;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class ProcessConstants {

    /**
     * 流程定义前缀
     */
    public static final String APPLY_ACCOUNT_PERIOD_PROC_KEY = "applyCustomerAccountPeriod";

    public static final String MODIFY_BUYORDER_APPLY_PROC_KEY = "editBuyorderVerify";

    public static final String BUYORDER_EXPENSE_KEY = "buyorderExpenseVerify";

    public static int VERIFY_TYPE_CUSTOMER_ACCOUNT_PERIOD = 616;

    public static final String APPLY_DISABLE_GOODS_PROC_KEY = "spuOrSkuDisabledStatusVerify";

    public static int VERIFY_TYPE_DISABLE_GOODS = ********;

    public static final int DEFAULT_BIZ_KEY_LIMITED_LENGTH = 64;

    public static final String DEFAULT_TENANT_ID = "erp:company:1";


    /**
     * 数据表T_VERIFIES_INFO，审核状态枚举
     */
    public enum CheckStatus {
        CHECKING(0), CHECKED(1), REJECTED(2);

        private int status;

        CheckStatus(int status) {
            this.status = status;
        }

        public int getStatus() {
            return status;
        }
    }


    public static final String DEFAULT_AUDITED_ACTIVITY_NAME = "审核完成";
    public static final String DEFAULT_REJECTED_ACTIVITY_NAME = "驳回";

}