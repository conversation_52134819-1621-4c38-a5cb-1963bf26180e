package com.wms.service.processor.input;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsInputOrderGoodsMapper;
import com.wms.dao.WmsInputOrderMapper;
import com.wms.dto.InputOrderDto;
import com.wms.dto.InputOrderGoodsDto;
import com.wms.dto.StockCalculateDto;
import com.wms.model.po.WmsInputOrder;
import com.wms.model.po.WmsInputOrderGoods;
import com.wms.service.util.WmsCommonUtil;
import com.wms.service.WmsSurplusinService;
import com.wms.service.processor.AbstractInputOrderProcessor;
import com.wms.service.stockcalculate.StockinfoCaculateInterface;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName WmsSurplusInOrderProcessor.java
 * @Description TODO  盘盈入库
 * @createTime 2020年09月22日 17:49:00
 */
@Service
public class WmsSurplusInOrderProcessor extends AbstractInputOrderProcessor {

    private static final Logger logger = LoggerFactory.getLogger(WmsSurplusInOrderProcessor.class);

    @Autowired
    @Qualifier("inputOrderAuditPassCaculateImpl")
    private StockinfoCaculateInterface stockinfoCaculateInterface;

    @Autowired
    private WmsSurplusinService wmsSurplusinService;

    @Resource
    private WmsInputOrderGoodsMapper wmsInputOrderGoodsMapper;

    @Resource
    private WmsInputOrderMapper wmsInputOrderMapper;

    @Override
    protected void commonValidator(InputOrderDto requestBean) throws Exception {
        if(!WmsInterfaceOrderType.INPUT_CHECK_MORE.equals(requestBean.getASNType())){
            throw new Exception("入库单:"+requestBean.getASNReference1()+"的类型错误!");
        }
    }
    @Override
    protected void updateOrderData(InputOrderDto inputOrderDto) throws Exception {
        String surplusNo = WmsCommonUtil.getOriginalOrderNo(inputOrderDto.getASNReference1());
        logger.info("盘盈入库单  单号:{},入参:{}",surplusNo, JSON.toJSONString(inputOrderDto));
        WmsInputOrder wmsInputOrder =  wmsSurplusinService.getSurplusInorderByNo(surplusNo);

        Map<Integer,WmsInputOrderGoods> wmsInputOrderGoodsMap = wmsSurplusinService.getWmsInputOrderGoods(wmsInputOrder.getWmsInputOrderId())
                .stream()
                .collect(Collectors.toMap(WmsInputOrderGoods::getWmsInputOrderGoodsId, item -> item));
        List<InputOrderGoodsDto> details = inputOrderDto.getDetails();

        Map<Integer,Integer> inNumMap = new HashMap<>();
        for (InputOrderGoodsDto inputOrderGoodsDto : details) {
            Integer detailId = Integer.valueOf(inputOrderGoodsDto.getUserDefine1());
            Integer inNum = inNumMap.get(detailId);
            if(inNum == null){
                inNum = 0;
            }
            inNumMap.put(detailId,inputOrderGoodsDto.getReceivedQty().intValue() + inNum);
        }

        for (Integer detailId : inNumMap.keySet()) {
            WmsInputOrderGoods wmsInputOrderGoods = wmsInputOrderGoodsMap.get(detailId);
            Integer inNum = inNumMap.get(detailId);
            WmsInputOrderGoods updateGoods = new WmsInputOrderGoods();
            updateGoods.setWmsInputOrderGoodsId(wmsInputOrderGoods.getWmsInputOrderGoodsId());
            updateGoods.setArrivalNum(wmsInputOrderGoods.getArrivalNum() + inNum);
            updateGoods.setArrivalStatus(wmsInputOrderGoods.getInputNum() <= updateGoods.getArrivalNum() ? 2 : 1);
            wmsInputOrderGoodsMapper.updateByPrimaryKeySelective(updateGoods);
        }
        List<WmsInputOrderGoods> wmsInputOrderGoods = wmsSurplusinService.getWmsInputOrderGoods(wmsInputOrder.getWmsInputOrderId());
        WmsInputOrder updateorder = new WmsInputOrder();
        updateorder.setWmsInputOrderId(wmsInputOrder.getWmsInputOrderId());
        updateorder.setArrivalStatus(2);
        for (WmsInputOrderGoods wmsInputOrderGood : wmsInputOrderGoods) {
           if(!wmsInputOrderGood.getArrivalStatus().equals(2)){
               updateorder.setArrivalStatus(1);
               break;
           }
        }
        wmsInputOrderMapper.updateByPrimaryKeySelective(updateorder);
    }


    @Override
    protected int getWmsLogicalOperateType(InputOrderDto orderDto) {
        return WmsLogicalOperateTypeEnum.SURPLUS_IN.getOperateTypeCode();
    }

    @Override
    protected int getRelateId(InputOrderDto orderDto, InputOrderGoodsDto goodsDto) {
        return Integer.valueOf(goodsDto.getUserDefine1());
    }

    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return  stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);
    }

    @Override
    protected int getBarcodeType(InputOrderDto orderDto, InputOrderGoodsDto goodsDto) {
        return 1;
    }

    @Override
    protected int getOperateType(InputOrderDto orderDto) {
        return StockOperateTypeConst.SURPLUS_WAREHOUSE_IN;
    }


}
