package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.*;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class

BatchSaleorderContractDto extends BatchBaseDto {
    private Integer saleorderId;
    private String saleorderNo;
    private BigDecimal totalAmount;
    private BigDecimal rate;
    //附件名称
    private String name;
    private String url;
    private Long validTimeBegin;
    private Long validTimeEnd;
    private Long validTime;
    private Integer invoiceType;
    //附件回写表id
    private Integer dataId;
    //oss http协议前缀
    private String ossPre;
    //附件id
    private Integer attachmentId;
    //金蝶回写表销售单号
    private Integer kingdeeDDH;
    //确认单名称
    private String confirmationName;
    //文件后缀
    private String suffix;

}
