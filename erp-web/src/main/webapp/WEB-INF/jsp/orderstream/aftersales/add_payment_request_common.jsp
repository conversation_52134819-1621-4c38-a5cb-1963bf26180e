<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<div class="parts">
    <table class="table">
        <thead>
        <tr>
            <th class="wid6">申请金额</th>
            <th class="wid8">申请时间</th>
            <th class="wid12">申请人</th>
            <th class="wid8">支付对象</th>
            <th class="wid15">身份证号</th>
            <th class="wid10">手机号</th>
            <th class="wid10">银行帐号</th>
            <th class="wid15">开户行及联行号</th>
            <th class="wid10">付款备注</th>
            <th class="wid5">审核状态</th>
            <th class="wid5">查看审核记录</th>
        </tr>
        </thead>
        <tbody>
        <c:if test="${not empty afterSalesVo.afterPayApplyList}">
            <c:forEach items="${afterSalesVo.afterPayApplyList}" var="apal" varStatus="num_index">
                <tr>
                    <td><fmt:formatNumber type="number" value="${apal.amount}" pattern="0.00" maxFractionDigits="2" /></td>
                    <td><date:date value="${apal.addTime}" format="yyyy.MM.dd HH:mm:ss"/></td>
                    <td>${apal.creatorName}</td>
                    <td>${apal.traderName}</td>
                    <td><c:if test="${empty apal.card }">-</c:if> ${apal.card}</td>
                    <td><c:if test="${empty apal.mobile }">-</c:if> ${apal.mobile}</td>
                    <td>${apal.bankAccount}</td>
                    <td>
                            ${apal.bank}<br/>${apal.bankCode}
                    </td>
                    <td>${apal.comments}</td>
                    <td>
                        <c:choose>
                            <c:when test="${apal.validStatus eq 0}">审核中</c:when>
                            <c:when test="${apal.validStatus eq 1}">审核通过</c:when>
                            <c:when test="${apal.validStatus eq 2 || apal.validStatus eq 3}">审核不通过</c:when>
                            <c:otherwise>--</c:otherwise>
                        </c:choose>
                    </td>
                    <td>
                        <c:if test="${isSupply ne 1}">
                            <div class="caozuo">
                                <span class="caozuo-blue pop-new-data" layerparams='{"width":"50%","height":"50%","title":"付款申请审核信息","link":"<%=basePath%>finance/after/paymentVerify.do?payApplyId=${apal.payApplyId}"}'>查看</span>
                            </div>
                        </c:if>
                    </td>
                </tr>
            </c:forEach>
        </c:if>
        <c:if test="${empty afterSalesVo.afterPayApplyList}">
            <tr>
                <td colspan="11">暂无记录</td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>