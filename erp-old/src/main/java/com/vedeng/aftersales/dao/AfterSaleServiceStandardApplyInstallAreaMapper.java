package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.model.AfterSaleServiceStandardApplyInstallArea;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AfterSaleServiceStandardApplyInstallAreaMapper {

    int deleteByPrimaryKey(Long installAreaId);

    int insertSelective(AfterSaleServiceStandardApplyInstallArea record);

    AfterSaleServiceStandardApplyInstallArea selectByPrimaryKey(Long installAreaId);

    int updateByPrimaryKeySelective(AfterSaleServiceStandardApplyInstallArea record);

    AfterSaleServiceStandardApplyInstallArea queryInstallAreaByApplyId(@Param("serviceStandardApplyId") Long serviceStandardApplyId);

}