package com.vedeng.common.core.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 一对多模型中用于在同一个方法中做集合数据的增删改工具类
 * @date 2022/8/31 10:22
 */
@Slf4j
public class DiffUtils {

    public static <K, V, O> void doDiff(List<ContentValue<K, V>> t1, List<ContentValue<K, V>> t2,
                                        Map<DiffUtils.Type, Consumer<List<V>>> actionMap) {
        Map<K, ContentValue<K, V>> map = compareMerge(t1, t2);

        List<V> batchInsert = new ArrayList<>(6);
        List<V> batchUpdate = new ArrayList<>(4);
        List<V> batchDelete = new ArrayList<>(4);
        for (Map.Entry<K, ContentValue<K, V>> entry : map.entrySet()) {
            if (DiffUtils.Type.INSERT.equals(entry.getValue().getType())) {
                batchInsert.add(entry.getValue().getValue());
            }
            if (DiffUtils.Type.UPDATE.equals(entry.getValue().getType())) {
                batchUpdate.add(entry.getValue().getValue());
            }
            if (DiffUtils.Type.DELETE.equals(entry.getValue().getType())) {
                batchDelete.add(entry.getValue().getValue());
            }
        }
        Optional.ofNullable(actionMap.get(Type.INSERT))
                .filter(consumer -> !batchInsert.isEmpty())
                .ifPresent(consumer -> consumer.accept(batchInsert));

        Optional.ofNullable(actionMap.get(Type.UPDATE))
                .filter(consumer -> !batchUpdate.isEmpty())
                .ifPresent(consumer -> consumer.accept(batchUpdate));

        Optional.ofNullable(actionMap.get(Type.DELETE))
                .filter(consumer -> !batchDelete.isEmpty())
                .ifPresent(consumer -> consumer.accept(batchDelete));
    }


    /**
     * 比较并归并
     * @param dbT 历史数据
     * @param newT 新数据
     * @param <K> 比较主键
     * @param <V> 值
     * @return Map
     */
    public static <K, V> Map<K, ContentValue<K, V>> compareMerge(List<ContentValue<K, V>> dbT, List<ContentValue<K, V>> newT) {
        Map<K, ContentValue<K, V>> map = new HashMap<>(dbT.size() + newT.size());
        // db数据集 和 new数据集 比对
        // 当db中有 new没有，表示需要删除的数据
        // 当db中有，new中也有表示修改的数据
        // 当db中有，new中没有表示需要新增的数据
        List<K> dbList = dbT.stream().map(ContentValue::getKey).collect(Collectors.toList());
        List<K> newList = newT.stream().map(ContentValue::getKey).collect(Collectors.toList());

        Collection<K> deleteListK = CollUtil.subtract(dbList, newList);
        List<ContentValue<K, V>> deleteList = dbT.stream().filter(t -> deleteListK.contains(t.getKey()))
                .collect(Collectors.toList());
        deleteList.forEach(t -> map.put(t.getKey(), ContentValue.of(t.getValue(), Type.DELETE)));
        log.info("需要删除的数据{}", JSON.toJSONString(deleteList));

        List<ContentValue<K, V>> insertList = newT.stream().filter(t -> t.key == null).collect(Collectors.toList());
        insertList.forEach(t -> map.put((K) IdUtil.simpleUUID(), ContentValue.of(t.getValue(), Type.INSERT)));
        log.info("需要新增的数据{}", JSON.toJSONString(insertList));

        Collection<K> updateListK = CollUtil.intersectionDistinct(dbList, newList);
        List<ContentValue<K, V>> updateList = newT.stream().filter(t -> updateListK.contains(t.getKey()))
                .collect(Collectors.toList());
        log.info("需要修改的数据{}", JSON.toJSONString(updateListK));
        updateList.forEach(t -> map.put(t.getKey(), ContentValue.of(t.getValue(), Type.UPDATE)));

        return map;
    }

    public static class ContentValue<K, V> {
        /**
         * 用于对比的主键
         */
        private final K key;
        /**
         * 对比的值
         */
        private final V value;
        /**
         * 操作
         */
        private final Type type;

        ContentValue(K key, V value, Type type) {
            this.key = key;
            this.value = value;
            this.type = type;
        }

        public static <K, V> ContentValue<K, V> of(K k, V v) {
            return new ContentValue<>(k, v, null);
        }

        public static <K, V> ContentValue<K, V> of(V v, Type type) {
            return new ContentValue<>(null, v, type);
        }

        public K getKey() {
            return key;
        }

        public V getValue() {
            return value;
        }

        public Type getType() {
            return type;
        }
    }

    /**
     * 操作类型
     */
    public enum Type {
        SAME,
        INSERT,
        UPDATE,
        DELETE,
        IGNORE
    }


    public static void main(String[] args) {

    }
}