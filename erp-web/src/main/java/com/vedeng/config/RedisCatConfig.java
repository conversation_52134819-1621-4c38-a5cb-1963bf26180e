package com.vedeng.config;

import com.alibaba.druid.support.spring.stat.BeanTypeAutoProxyCreator;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.config.plugin.JedisInterceptor;
import org.aopalliance.aop.Advice;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class RedisCatConfig {

    //创建Advice或Advisor
    @Bean
    public Advice jedisInterceptor(){
        return new JedisInterceptor();
    }

    @Bean
    public BeanTypeAutoProxyCreator beanTypeAutoProxyCreator(){
        BeanTypeAutoProxyCreator beanTypeAutoProxyCreator=new BeanTypeAutoProxyCreator();
        //设置要创建代理的那些Bean的名字
        beanTypeAutoProxyCreator.setTargetBeanType(RedisUtils.class);
        //设置拦截链名字(这些拦截器是有先后顺序的)
        beanTypeAutoProxyCreator.setInterceptorNames("jedisInterceptor");
        return beanTypeAutoProxyCreator;
    }
}

