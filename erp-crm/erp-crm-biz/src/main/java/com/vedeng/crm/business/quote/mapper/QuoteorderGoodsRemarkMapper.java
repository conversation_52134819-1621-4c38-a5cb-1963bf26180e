package com.vedeng.crm.business.quote.mapper;

import com.vedeng.crm.business.quote.domain.entity.QuoteorderGoodsRemarkEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QuoteorderGoodsRemarkMapper {
    int insertSelective(QuoteorderGoodsRemarkEntity record);

    QuoteorderGoodsRemarkEntity selectByPrimaryKey(Long quoteorderGoodsRemarkId);

    List<QuoteorderGoodsRemarkEntity> selectByQuoteGoodsId(@Param("quoteorderGoodsId") Integer quoteorderGoodsId);
    List<QuoteorderGoodsRemarkEntity> selectByQuoteId(@Param("quoteorderId") Integer quoteorderId);

    int updateByPrimaryKeySelective(QuoteorderGoodsRemarkEntity record);
}
