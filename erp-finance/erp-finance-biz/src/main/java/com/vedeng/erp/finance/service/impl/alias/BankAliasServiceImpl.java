package com.vedeng.erp.finance.service.impl.alias;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.vedeng.erp.finance.domain.dto.BankAliasDto;
import com.vedeng.erp.finance.domain.entity.BankAliasEntity;
import com.vedeng.erp.finance.mapper.BankAliasMapper;
import com.vedeng.erp.finance.mapstruct.BankAliasConvertor;
import com.vedeng.erp.finance.service.BankAliasService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BankAliasServiceImpl implements BankAliasService {

    @Autowired
    private BankAliasMapper bankAliasMapper;

    @Autowired
    private BankAliasConvertor bankAliasConvertor;

    @Override
    public List<BankAliasDto> getBankAlias(List<String> aliasList) {
        if (CollUtil.isEmpty(aliasList)){
            return Lists.newArrayList();
        }
        List<BankAliasEntity> bankAliasEntities = bankAliasMapper.batchSelectByBankName(aliasList);
        List<BankAliasDto> dto = bankAliasConvertor.toDto(bankAliasEntities);
        return dto;
    }

    @Override
    public void createOrUpdateBankAlias(List<String> receiptBankNameList,Integer bankId) {
        List<BankAliasEntity> bankAliasEntities = bankAliasMapper.batchSelectByBankName(receiptBankNameList);
        log.info("查询到银行别名，bankAliasEntities:{}", JSON.toJSONString(bankAliasEntities));
        Map<String, BankAliasEntity> bankAliasEntityMap = bankAliasEntities.stream().collect(Collectors.toMap(BankAliasEntity::getAlias, Function.identity()));
        if (MapUtil.isEmpty(bankAliasEntityMap)){
            bankAliasEntityMap = Maps.newHashMap();
        }
        for (String bankName : receiptBankNameList) {
            if (bankAliasEntityMap.containsKey(bankName)){
                // 更新
                BankAliasEntity bankAliasEntity = bankAliasEntityMap.get(bankName);
                BankAliasEntity update = new BankAliasEntity();
                update.setBankId(bankId);
                update.setBankReceiptAliasId(bankAliasEntity.getBankReceiptAliasId());
                log.info("更新银行别名：{}", JSON.toJSON(bankAliasEntity));
                bankAliasMapper.updateByPrimaryKeySelective(update);
            }else {
                BankAliasEntity bankAliasEntity = new BankAliasEntity();
                bankAliasEntity.setAlias(bankName);
                bankAliasEntity.setBankId(bankId);
                bankAliasEntity.setAddTime(new Date());
                log.info("新增银行别名：{}", JSON.toJSON(bankAliasEntity));
                bankAliasMapper.insertSelective(bankAliasEntity);
            }
        }
    }
}
