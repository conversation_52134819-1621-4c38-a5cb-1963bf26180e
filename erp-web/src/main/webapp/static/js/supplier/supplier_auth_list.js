function addAttament(item,domain,uri){
    var parent = $(item).parents(".p")
    var skuSupplyAuthId = $(parent).find(".sku-supply-auth-id").val();
    var traderId = $("#traderId").val();
    var id=0;
    $.ajax({
        type: "POST",
        url: page_url+"/trader/tradersupplier/authPictureAdd.do",
        data: {'traderId':traderId,"skuSupplyAuthId":skuSupplyAuthId,"domain":domain,"uri":uri},
        dataType:'json',
        async:false,
        success: function(data){
            if(data.code==0){
                id= data.data.attachmentId
            }else{
                layer.alert(data.message,{ icon: 2 });
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
    return id;
}
function delAttach(item,id){
    var parent=$(item).parents(".p");
    var skuSupplyAuthId=$(parent).find(".sku-supply-auth-id").val();
    var traderId=$("#traderId").val();
    $.ajax({
        type: "POST",
        url: page_url+"/trader/tradersupplier/authPictureDel.do",
        data: {'traderId':traderId,"skuSupplyAuthId":skuSupplyAuthId,"attachmentId":id},
        dataType:'json',
        success: function(data){
            if(data.code==0){
            }else{
                layer.alert(data.message,{ icon: 2 });
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}

$(function () {
    $('.J-upload').each(function (i) {
        var _this = this;
        new Upload({
            limit: 5,
            url: GLOBAL.IMGUPLOADURL,
            wrapper: $(this),
            uploadName: 'upload' + i,
            list: JSON.parse($(this).siblings('.J-upload-data').val() || '[]'),
            onchange: function () {
                $(_this).find('.J-upload-item').each(function (ii) {
                    var data = $(this).data('item');
                    $(this).find('.J-item-name').remove();
                    if(!data.id){
                        var id=addAttament(this,data.domain,data.filePath);
                        data.id=id;
                        $(this).data('item',JSON.stringify(data));
                        $(this).data('id', id);
                    }else {
                        $(this).data('id', data.id);
                        $(this).data("w",1);
                    }
                })

            },
            filters: {
                mime_types: [
                    { title: "Image files", extensions: "jpg,jpeg,png,bmp" }
                ],
                max_file_size: '5MB'
            },
            onError: function (error) {
                var errorMsg = {
                    TYPE: '上传图片格式为：JPG、PNG、JPEG、BMP格式',
                    SIZE: '图片大小不超过5M'
                }
                if (error) {
                    var $error = $(_this).siblings('.J-upload-error');
                    $error.show().find('label').html(errorMsg[error]).show();
                    timeout && clearTimeout(timeout);
                    timeout = setTimeout(function () {
                        $error.hide();
                    }, 3000)
                }
            },
            delCallback: function (item) {
                var id=item.data('id');
                delAttach(item,id);
            }
        });
    })

    var canEdit=$("#canEdit").val();
    if("0"==canEdit){
        $(".J-upload-btn-wrap").each(function () {
            $(this).remove()
        });
        $(".J-upload-del").each(function () {
            $(this).remove()
        })
    }
})

/**
 * 删除授权书信息
 */
function delSkuSupplyAuth(num){
    var skuSupplyAuthId = $('#skuSupplyAuth_' + num).val();
    var url = '';
    url =  '/trader/tradersupplier/authRemove.do'
    layer.confirm('确定删除授权书？',{btn: ['确定','取消'],title: "提示"},function (){
        $.ajax({
            type: 'POST',
            url: url,
            data: {"skuSupplyAuthId": skuSupplyAuthId},
            dataType : "json",
            success: function(data) {
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }else if(data.code == 0){
                    layer.alert("删除成功");
                    window.location.reload();
                }else {
                    layer.alert("删除失败");
                }
            }
        });
    })
}

function show(count){
    $("#hideSkuInfo"+count).show();
    $("#showMoreSpan"+count).hide();
    $("#hideMoreSpan"+count).show();
}

function hide(count){
    $("#hideSkuInfo"+count).hide();
    $("#showMoreSpan"+count).show();
    $("#hideMoreSpan"+count).hide();
}