<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseItemDetailMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemDetailEntity">
    <!--@mbg.generated-->
    <!--@Table T_BUYORDER_EXPENSE_ITEM_DETAIL-->
    <id column="BUYORDER_EXPENSE_ITEM_DETAIL_ID" jdbcType="INTEGER" property="buyorderExpenseItemDetailId" />
    <result column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="CURRENCY_UNIT_ID" jdbcType="INTEGER" property="currencyUnitId" />
    <result column="EXPENSE_CATEGORY_ID" jdbcType="INTEGER" property="expenseCategoryId" />
    <result column="EXPENSE_CATEGORY_NAME" jdbcType="VARCHAR" property="expenseCategoryName" />
    <result column="HAVE_STOCK_MANAGE" jdbcType="INTEGER" property="haveStockManage" />
    <result column="INSIDE_COMMENTS" jdbcType="VARCHAR" property="insideComments" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BUYORDER_EXPENSE_ITEM_DETAIL_ID, BUYORDER_EXPENSE_ITEM_ID, SKU, GOODS_NAME, BRAND_NAME, 
    MODEL, UNIT_NAME, PRICE, CURRENCY_UNIT_ID, EXPENSE_CATEGORY_ID, EXPENSE_CATEGORY_NAME, 
    HAVE_STOCK_MANAGE, INSIDE_COMMENTS, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, 
    UPDATER, UPDATER_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BUYORDER_EXPENSE_ITEM_DETAIL
    where BUYORDER_EXPENSE_ITEM_DETAIL_ID = #{buyorderExpenseItemDetailId,jdbcType=INTEGER}
  </select>
  <select id="getAllByItemIdList" resultType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemDetailEntity">
    select
    <include refid="Base_Column_List" />
    from T_BUYORDER_EXPENSE_ITEM_DETAIL
    where BUYORDER_EXPENSE_ITEM_ID in
    <foreach collection="itemIdList" item="itemId" open="(" close=")" separator=",">
      #{itemId,jdbcType=INTEGER}
    </foreach>
    and IS_DELETE = 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_BUYORDER_EXPENSE_ITEM_DETAIL
    where BUYORDER_EXPENSE_ITEM_DETAIL_ID = #{buyorderExpenseItemDetailId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="BUYORDER_EXPENSE_ITEM_DETAIL_ID" keyProperty="buyorderExpenseItemDetailId" parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUYORDER_EXPENSE_ITEM_DETAIL (BUYORDER_EXPENSE_ITEM_ID, SKU, GOODS_NAME, 
      BRAND_NAME, MODEL, UNIT_NAME, 
      PRICE, CURRENCY_UNIT_ID, EXPENSE_CATEGORY_ID, 
      EXPENSE_CATEGORY_NAME, HAVE_STOCK_MANAGE, INSIDE_COMMENTS, 
      IS_DELETE, ADD_TIME, CREATOR, 
      CREATOR_NAME, MOD_TIME, UPDATER, 
      UPDATER_NAME)
    values (#{buyorderExpenseItemId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, 
      #{brandName,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{unitName,jdbcType=VARCHAR}, 
      #{price,jdbcType=DECIMAL}, #{currencyUnitId,jdbcType=INTEGER}, #{expenseCategoryId,jdbcType=INTEGER}, 
      #{expenseCategoryName,jdbcType=VARCHAR}, #{haveStockManage,jdbcType=INTEGER}, #{insideComments,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, 
      #{updaterName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="BUYORDER_EXPENSE_ITEM_DETAIL_ID" keyProperty="buyorderExpenseItemDetailId" parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUYORDER_EXPENSE_ITEM_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="buyorderExpenseItemId != null">
        BUYORDER_EXPENSE_ITEM_ID,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="goodsName != null">
        GOODS_NAME,
      </if>
      <if test="brandName != null">
        BRAND_NAME,
      </if>
      <if test="model != null">
        MODEL,
      </if>
      <if test="unitName != null">
        UNIT_NAME,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID,
      </if>
      <if test="expenseCategoryId != null">
        EXPENSE_CATEGORY_ID,
      </if>
      <if test="expenseCategoryName != null">
        EXPENSE_CATEGORY_NAME,
      </if>
      <if test="haveStockManage != null">
        HAVE_STOCK_MANAGE,
      </if>
      <if test="insideComments != null">
        INSIDE_COMMENTS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="buyorderExpenseItemId != null">
        #{buyorderExpenseItemId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null">
        #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="expenseCategoryId != null">
        #{expenseCategoryId,jdbcType=INTEGER},
      </if>
      <if test="expenseCategoryName != null">
        #{expenseCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="haveStockManage != null">
        #{haveStockManage,jdbcType=INTEGER},
      </if>
      <if test="insideComments != null">
        #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemDetailEntity">
    <!--@mbg.generated-->
    update T_BUYORDER_EXPENSE_ITEM_DETAIL
    <set>
      <if test="buyorderExpenseItemId != null">
        BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="expenseCategoryId != null">
        EXPENSE_CATEGORY_ID = #{expenseCategoryId,jdbcType=INTEGER},
      </if>
      <if test="expenseCategoryName != null">
        EXPENSE_CATEGORY_NAME = #{expenseCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="haveStockManage != null">
        HAVE_STOCK_MANAGE = #{haveStockManage,jdbcType=INTEGER},
      </if>
      <if test="insideComments != null">
        INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where BUYORDER_EXPENSE_ITEM_DETAIL_ID = #{buyorderExpenseItemDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemDetailEntity">
    <!--@mbg.generated-->
    update T_BUYORDER_EXPENSE_ITEM_DETAIL
    set BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      PRICE = #{price,jdbcType=DECIMAL},
      CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      EXPENSE_CATEGORY_ID = #{expenseCategoryId,jdbcType=INTEGER},
      EXPENSE_CATEGORY_NAME = #{expenseCategoryName,jdbcType=VARCHAR},
      HAVE_STOCK_MANAGE = #{haveStockManage,jdbcType=INTEGER},
      INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where BUYORDER_EXPENSE_ITEM_DETAIL_ID = #{buyorderExpenseItemDetailId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="BUYORDER_EXPENSE_ITEM_DETAIL_ID" keyProperty="buyorderExpenseItemDetailId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUYORDER_EXPENSE_ITEM_DETAIL
    (BUYORDER_EXPENSE_ITEM_ID, SKU, GOODS_NAME, BRAND_NAME, MODEL, UNIT_NAME, PRICE, 
      CURRENCY_UNIT_ID, EXPENSE_CATEGORY_ID, EXPENSE_CATEGORY_NAME, HAVE_STOCK_MANAGE, 
      INSIDE_COMMENTS, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, 
      UPDATER_NAME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.buyorderExpenseItemId,jdbcType=INTEGER}, #{item.sku,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR}, 
        #{item.brandName,jdbcType=VARCHAR}, #{item.model,jdbcType=VARCHAR}, #{item.unitName,jdbcType=VARCHAR}, 
        #{item.price,jdbcType=DECIMAL}, #{item.currencyUnitId,jdbcType=INTEGER}, #{item.expenseCategoryId,jdbcType=INTEGER}, 
        #{item.expenseCategoryName,jdbcType=VARCHAR}, #{item.haveStockManage,jdbcType=INTEGER}, 
        #{item.insideComments,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.modTime,jdbcType=TIMESTAMP}, 
        #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <update id="updateByBuyorderExpenseItemIdSelective">
    update T_BUYORDER_EXPENSE_ITEM_DETAIL
    <set>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="expenseCategoryId != null">
        EXPENSE_CATEGORY_ID = #{expenseCategoryId,jdbcType=INTEGER},
      </if>
      <if test="expenseCategoryName != null">
        EXPENSE_CATEGORY_NAME = #{expenseCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="haveStockManage != null">
        HAVE_STOCK_MANAGE = #{haveStockManage,jdbcType=INTEGER},
      </if>
      <if test="insideComments != null">
        INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER}
  </update>
  <update id="batchUpdate">
      <foreach collection="list" item="item" index="index" separator=";">
        update T_BUYORDER_EXPENSE_ITEM_DETAIL
        set
          BUYORDER_EXPENSE_ITEM_ID = #{item.buyorderExpenseItemId,jdbcType=INTEGER},
          SKU = #{item.sku,jdbcType=VARCHAR},
          GOODS_NAME = #{item.goodsName,jdbcType=VARCHAR},
          BRAND_NAME = #{item.brandName,jdbcType=VARCHAR},
          MODEL = #{item.model,jdbcType=VARCHAR},
          UNIT_NAME = #{item.unitName,jdbcType=VARCHAR},
          PRICE = #{item.price,jdbcType=DECIMAL},
          CURRENCY_UNIT_ID = #{item.currencyUnitId,jdbcType=INTEGER},
          EXPENSE_CATEGORY_ID = #{item.expenseCategoryId,jdbcType=INTEGER},
          EXPENSE_CATEGORY_NAME = #{item.expenseCategoryName,jdbcType=VARCHAR},
          HAVE_STOCK_MANAGE = #{item.haveStockManage,jdbcType=INTEGER},
          INSIDE_COMMENTS = #{item.insideComments,jdbcType=VARCHAR},
          IS_DELETE = #{item.isDelete,jdbcType=INTEGER}
        where BUYORDER_EXPENSE_ITEM_DETAIL_ID = #{item.buyorderExpenseItemDetailId,jdbcType=INTEGER}
      </foreach>
  </update>
</mapper>