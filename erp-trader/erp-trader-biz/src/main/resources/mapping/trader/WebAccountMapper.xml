<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.WebAccountMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.WebAccountEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <id column="ERP_ACCOUNT_ID" jdbcType="INTEGER" property="erpAccountId" />
    <result column="WEB_ACCOUNT_ID" jdbcType="INTEGER" property="webAccountId" />
    <result column="SSO_ACCOUNT_ID" jdbcType="INTEGER" property="ssoAccountId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_ADDRESS_ID" jdbcType="INTEGER" property="traderAddressId" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="IS_ENABLE" jdbcType="BIT" property="isEnable" />
    <result column="FROM" jdbcType="TINYINT" property="from" />
    <result column="COMPANY_STATUS" jdbcType="BIT" property="companyStatus" />
    <result column="INDENTITY_STATUS" jdbcType="BIT" property="indentityStatus" />
    <result column="IS_OPEN_STORE" jdbcType="BIT" property="isOpenStore" />
    <result column="IS_VEDENG_JX" jdbcType="BIT" property="isVedengJx" />
    <result column="ACCOUNT" jdbcType="VARCHAR" property="account" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="COMPANY_NAME" jdbcType="VARCHAR" property="companyName" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="SEX" jdbcType="BIT" property="sex" />
    <result column="WEIXIN_OPENID" jdbcType="VARCHAR" property="weixinOpenid" />
    <result column="UUID" jdbcType="VARCHAR" property="uuid" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="LAST_LOGIN_TIME" jdbcType="BIGINT" property="lastLoginTime" />
    <result column="IS_VEDENG_JOIN" jdbcType="TINYINT" property="isVedengJoin" />
    <result column="MOD_TIME_JOIN" jdbcType="BIGINT" property="modTimeJoin" />
    <result column="IS_SEND_MESSAGE" jdbcType="BIT" property="isSendMessage" />
    <result column="REGISTER_PLATFORM" jdbcType="INTEGER" property="registerPlatform" />
    <result column="BELONG_PLATFORM" jdbcType="INTEGER" property="belongPlatform" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="IS_VEDENG_MEMBER" jdbcType="BIT" property="isVedengMember" />
    <result column="VEDENG_MEMBER_TIME" jdbcType="TIMESTAMP" property="vedengMemberTime" />
    <result column="IS_REGIONAL_MALL" jdbcType="INTEGER" property="isRegionalMall" />
    <result column="REGISTER_REGIONAL_MALL" jdbcType="INTEGER" property="registerRegionalMall" />
    <result column="AUTH_TYPE" jdbcType="INTEGER" property="authType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    ERP_ACCOUNT_ID, WEB_ACCOUNT_ID, SSO_ACCOUNT_ID, TRADER_ID, TRADER_CONTACT_ID, TRADER_ADDRESS_ID, 
    USER_ID, IS_ENABLE, `FROM`, COMPANY_STATUS, INDENTITY_STATUS, IS_OPEN_STORE, IS_VEDENG_JX, 
    ACCOUNT, EMAIL, MOBILE, COMPANY_NAME, `NAME`, SEX, WEIXIN_OPENID, UUID, ADD_TIME, 
    LAST_LOGIN_TIME, IS_VEDENG_JOIN, MOD_TIME_JOIN, IS_SEND_MESSAGE, REGISTER_PLATFORM, 
    BELONG_PLATFORM, MOD_TIME, IS_VEDENG_MEMBER, VEDENG_MEMBER_TIME, IS_REGIONAL_MALL, 
    REGISTER_REGIONAL_MALL, AUTH_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    select 
    <include refid="Base_Column_List" />
    from T_WEB_ACCOUNT
    where ERP_ACCOUNT_ID = #{erpAccountId,jdbcType=INTEGER}
  </select>
  <select id="getWebAccountListBytraderId" resultType="com.vedeng.erp.trader.domain.entity.WebAccountEntity">
     SELECT
    CASE WHEN contact.POSITION IS NOT NULL THEN contact.POSITION
         WHEN tca.POSITION IS NOT NULL THEN tca.POSITION
         ELSE tcb.POSITION END AS position,
    tca.IS_ON_JOB AS isOnJob,
    a.MOBILE,
    a.ADD_TIME,
    a.VEDENG_MEMBER_TIME,
    CASE WHEN contact.NAME IS NOT NULL THEN contact.NAME
         WHEN tca.NAME IS NOT NULL THEN tca.NAME
         ELSE tcb.NAME END AS NAME
FROM
    T_WEB_ACCOUNT a
LEFT JOIN T_TRADER_CUSTOMER tc ON a.TRADER_ID = tc.TRADER_ID
LEFT JOIN T_TRADER_CONTACT tca ON a.TRADER_CONTACT_ID = tca.TRADER_CONTACT_ID
LEFT JOIN (
    SELECT T1.MOBILE, T2.NAME, T2.POSITION
    FROM T_WEB_ACCOUNT T1
    JOIN T_TRADER_CONTACT T2 ON T1.MOBILE = T2.MOBILE OR T1.MOBILE = T2.MOBILE2
    WHERE T1.TRADER_ID = #{traderId}
      AND T2.TRADER_ID = #{traderId}
      AND T2.TRADER_CONTACT_ID = (
          SELECT MIN(TRADER_CONTACT_ID)
          FROM T_TRADER_CONTACT
          WHERE (MOBILE = T1.MOBILE OR MOBILE2 = T1.MOBILE)
            AND TRADER_ID = #{traderId}
      )
) tcb ON a.MOBILE = tcb.MOBILE
LEFT JOIN (
    SELECT T1.MOBILE, T1.POSITION, NAME, TRADER_CONTACT_ID, T1.MAX_ID
    FROM (
        SELECT T1.MOBILE, T2.POSITION, T2.NAME, T2.TRADER_CONTACT_ID, MAX(ts.SALEORDER_ID) AS MAX_ID
        FROM T_WEB_ACCOUNT T1
        JOIN T_TRADER_CONTACT T2 ON T1.MOBILE = T2.MOBILE OR T1.MOBILE = T2.MOBILE2
        JOIN T_SALEORDER ts ON T2.TRADER_CONTACT_ID = ts.TAKE_TRADER_CONTACT_ID
        WHERE T1.TRADER_ID = #{traderId}
          AND T2.TRADER_ID = #{traderId}
          AND ts.TRADER_ID = #{traderId}
          AND T2.IS_ENABLE = 1
        GROUP BY T1.MOBILE, T2.TRADER_CONTACT_ID
    ) T1
    JOIN (
        SELECT T1.MOBILE, MAX(ts.SALEORDER_ID) AS MAX_ID
        FROM T_WEB_ACCOUNT T1
        JOIN T_TRADER_CONTACT T2 ON T1.MOBILE = T2.MOBILE OR T1.MOBILE = T2.MOBILE2
        JOIN T_SALEORDER ts ON T2.TRADER_CONTACT_ID = ts.TAKE_TRADER_CONTACT_ID
        WHERE T1.TRADER_ID = #{traderId}
          AND T2.TRADER_ID = #{traderId}
          AND ts.TRADER_ID = #{traderId}
          AND T2.IS_ENABLE = 1
        GROUP BY T1.MOBILE
    ) T2 ON T1.MOBILE = T2.MOBILE AND T1.MAX_ID = T2.MAX_ID
) contact ON a.MOBILE = contact.MOBILE
WHERE
    a.TRADER_ID = #{traderId}
ORDER BY
    a.ERP_ACCOUNT_ID DESC


  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    delete from T_WEB_ACCOUNT
    where ERP_ACCOUNT_ID = #{erpAccountId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.trader.domain.entity.WebAccountEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    insert into T_WEB_ACCOUNT (ERP_ACCOUNT_ID, WEB_ACCOUNT_ID, SSO_ACCOUNT_ID, 
      TRADER_ID, TRADER_CONTACT_ID, TRADER_ADDRESS_ID, 
      USER_ID, IS_ENABLE, `FROM`, 
      COMPANY_STATUS, INDENTITY_STATUS, IS_OPEN_STORE, 
      IS_VEDENG_JX, ACCOUNT, EMAIL, 
      MOBILE, COMPANY_NAME, `NAME`, 
      SEX, WEIXIN_OPENID, UUID, 
      ADD_TIME, LAST_LOGIN_TIME, IS_VEDENG_JOIN, 
      MOD_TIME_JOIN, IS_SEND_MESSAGE, REGISTER_PLATFORM, 
      BELONG_PLATFORM, MOD_TIME, IS_VEDENG_MEMBER, 
      VEDENG_MEMBER_TIME, IS_REGIONAL_MALL, REGISTER_REGIONAL_MALL, 
      AUTH_TYPE)
    values (#{erpAccountId,jdbcType=INTEGER}, #{webAccountId,jdbcType=INTEGER}, #{ssoAccountId,jdbcType=INTEGER}, 
      #{traderId,jdbcType=INTEGER}, #{traderContactId,jdbcType=INTEGER}, #{traderAddressId,jdbcType=INTEGER}, 
      #{userId,jdbcType=INTEGER}, #{isEnable,jdbcType=BIT}, #{from,jdbcType=TINYINT}, 
      #{companyStatus,jdbcType=BIT}, #{indentityStatus,jdbcType=BIT}, #{isOpenStore,jdbcType=BIT}, 
      #{isVedengJx,jdbcType=BIT}, #{account,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 
      #{mobile,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{sex,jdbcType=BIT}, #{weixinOpenid,jdbcType=VARCHAR}, #{uuid,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=BIGINT}, #{lastLoginTime,jdbcType=BIGINT}, #{isVedengJoin,jdbcType=TINYINT}, 
      #{modTimeJoin,jdbcType=BIGINT}, #{isSendMessage,jdbcType=BIT}, #{registerPlatform,jdbcType=INTEGER}, 
      #{belongPlatform,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, #{isVedengMember,jdbcType=BIT}, 
      #{vedengMemberTime,jdbcType=TIMESTAMP}, #{isRegionalMall,jdbcType=INTEGER}, #{registerRegionalMall,jdbcType=INTEGER}, 
      #{authType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.trader.domain.entity.WebAccountEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    insert into T_WEB_ACCOUNT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="erpAccountId != null">
        ERP_ACCOUNT_ID,
      </if>
      <if test="webAccountId != null">
        WEB_ACCOUNT_ID,
      </if>
      <if test="ssoAccountId != null">
        SSO_ACCOUNT_ID,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="from != null">
        `FROM`,
      </if>
      <if test="companyStatus != null">
        COMPANY_STATUS,
      </if>
      <if test="indentityStatus != null">
        INDENTITY_STATUS,
      </if>
      <if test="isOpenStore != null">
        IS_OPEN_STORE,
      </if>
      <if test="isVedengJx != null">
        IS_VEDENG_JX,
      </if>
      <if test="account != null">
        ACCOUNT,
      </if>
      <if test="email != null">
        EMAIL,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="companyName != null">
        COMPANY_NAME,
      </if>
      <if test="name != null">
        `NAME`,
      </if>
      <if test="sex != null">
        SEX,
      </if>
      <if test="weixinOpenid != null">
        WEIXIN_OPENID,
      </if>
      <if test="uuid != null">
        UUID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="lastLoginTime != null">
        LAST_LOGIN_TIME,
      </if>
      <if test="isVedengJoin != null">
        IS_VEDENG_JOIN,
      </if>
      <if test="modTimeJoin != null">
        MOD_TIME_JOIN,
      </if>
      <if test="isSendMessage != null">
        IS_SEND_MESSAGE,
      </if>
      <if test="registerPlatform != null">
        REGISTER_PLATFORM,
      </if>
      <if test="belongPlatform != null">
        BELONG_PLATFORM,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="isVedengMember != null">
        IS_VEDENG_MEMBER,
      </if>
      <if test="vedengMemberTime != null">
        VEDENG_MEMBER_TIME,
      </if>
      <if test="isRegionalMall != null">
        IS_REGIONAL_MALL,
      </if>
      <if test="registerRegionalMall != null">
        REGISTER_REGIONAL_MALL,
      </if>
      <if test="authType != null">
        AUTH_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="erpAccountId != null">
        #{erpAccountId,jdbcType=INTEGER},
      </if>
      <if test="webAccountId != null">
        #{webAccountId,jdbcType=INTEGER},
      </if>
      <if test="ssoAccountId != null">
        #{ssoAccountId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderAddressId != null">
        #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=BIT},
      </if>
      <if test="from != null">
        #{from,jdbcType=TINYINT},
      </if>
      <if test="companyStatus != null">
        #{companyStatus,jdbcType=BIT},
      </if>
      <if test="indentityStatus != null">
        #{indentityStatus,jdbcType=BIT},
      </if>
      <if test="isOpenStore != null">
        #{isOpenStore,jdbcType=BIT},
      </if>
      <if test="isVedengJx != null">
        #{isVedengJx,jdbcType=BIT},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=BIT},
      </if>
      <if test="weixinOpenid != null">
        #{weixinOpenid,jdbcType=VARCHAR},
      </if>
      <if test="uuid != null">
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="lastLoginTime != null">
        #{lastLoginTime,jdbcType=BIGINT},
      </if>
      <if test="isVedengJoin != null">
        #{isVedengJoin,jdbcType=TINYINT},
      </if>
      <if test="modTimeJoin != null">
        #{modTimeJoin,jdbcType=BIGINT},
      </if>
      <if test="isSendMessage != null">
        #{isSendMessage,jdbcType=BIT},
      </if>
      <if test="registerPlatform != null">
        #{registerPlatform,jdbcType=INTEGER},
      </if>
      <if test="belongPlatform != null">
        #{belongPlatform,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isVedengMember != null">
        #{isVedengMember,jdbcType=BIT},
      </if>
      <if test="vedengMemberTime != null">
        #{vedengMemberTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isRegionalMall != null">
        #{isRegionalMall,jdbcType=INTEGER},
      </if>
      <if test="registerRegionalMall != null">
        #{registerRegionalMall,jdbcType=INTEGER},
      </if>
      <if test="authType != null">
        #{authType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.WebAccountEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update T_WEB_ACCOUNT
    <set>
      <if test="webAccountId != null">
        WEB_ACCOUNT_ID = #{webAccountId,jdbcType=INTEGER},
      </if>
      <if test="ssoAccountId != null">
        SSO_ACCOUNT_ID = #{ssoAccountId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=BIT},
      </if>
      <if test="from != null">
        `FROM` = #{from,jdbcType=TINYINT},
      </if>
      <if test="companyStatus != null">
        COMPANY_STATUS = #{companyStatus,jdbcType=BIT},
      </if>
      <if test="indentityStatus != null">
        INDENTITY_STATUS = #{indentityStatus,jdbcType=BIT},
      </if>
      <if test="isOpenStore != null">
        IS_OPEN_STORE = #{isOpenStore,jdbcType=BIT},
      </if>
      <if test="isVedengJx != null">
        IS_VEDENG_JX = #{isVedengJx,jdbcType=BIT},
      </if>
      <if test="account != null">
        ACCOUNT = #{account,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        EMAIL = #{email,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `NAME` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        SEX = #{sex,jdbcType=BIT},
      </if>
      <if test="weixinOpenid != null">
        WEIXIN_OPENID = #{weixinOpenid,jdbcType=VARCHAR},
      </if>
      <if test="uuid != null">
        UUID = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="lastLoginTime != null">
        LAST_LOGIN_TIME = #{lastLoginTime,jdbcType=BIGINT},
      </if>
      <if test="isVedengJoin != null">
        IS_VEDENG_JOIN = #{isVedengJoin,jdbcType=TINYINT},
      </if>
      <if test="modTimeJoin != null">
        MOD_TIME_JOIN = #{modTimeJoin,jdbcType=BIGINT},
      </if>
      <if test="isSendMessage != null">
        IS_SEND_MESSAGE = #{isSendMessage,jdbcType=BIT},
      </if>
      <if test="registerPlatform != null">
        REGISTER_PLATFORM = #{registerPlatform,jdbcType=INTEGER},
      </if>
      <if test="belongPlatform != null">
        BELONG_PLATFORM = #{belongPlatform,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isVedengMember != null">
        IS_VEDENG_MEMBER = #{isVedengMember,jdbcType=BIT},
      </if>
      <if test="vedengMemberTime != null">
        VEDENG_MEMBER_TIME = #{vedengMemberTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isRegionalMall != null">
        IS_REGIONAL_MALL = #{isRegionalMall,jdbcType=INTEGER},
      </if>
      <if test="registerRegionalMall != null">
        REGISTER_REGIONAL_MALL = #{registerRegionalMall,jdbcType=INTEGER},
      </if>
      <if test="authType != null">
        AUTH_TYPE = #{authType,jdbcType=INTEGER},
      </if>
    </set>
    where ERP_ACCOUNT_ID = #{erpAccountId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.WebAccountEntity">
    <!-- @mbg.generated: generated automatically, do not modify! -->
    update T_WEB_ACCOUNT
    set WEB_ACCOUNT_ID = #{webAccountId,jdbcType=INTEGER},
      SSO_ACCOUNT_ID = #{ssoAccountId,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      USER_ID = #{userId,jdbcType=INTEGER},
      IS_ENABLE = #{isEnable,jdbcType=BIT},
      `FROM` = #{from,jdbcType=TINYINT},
      COMPANY_STATUS = #{companyStatus,jdbcType=BIT},
      INDENTITY_STATUS = #{indentityStatus,jdbcType=BIT},
      IS_OPEN_STORE = #{isOpenStore,jdbcType=BIT},
      IS_VEDENG_JX = #{isVedengJx,jdbcType=BIT},
      ACCOUNT = #{account,jdbcType=VARCHAR},
      EMAIL = #{email,jdbcType=VARCHAR},
      MOBILE = #{mobile,jdbcType=VARCHAR},
      COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
      `NAME` = #{name,jdbcType=VARCHAR},
      SEX = #{sex,jdbcType=BIT},
      WEIXIN_OPENID = #{weixinOpenid,jdbcType=VARCHAR},
      UUID = #{uuid,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      LAST_LOGIN_TIME = #{lastLoginTime,jdbcType=BIGINT},
      IS_VEDENG_JOIN = #{isVedengJoin,jdbcType=TINYINT},
      MOD_TIME_JOIN = #{modTimeJoin,jdbcType=BIGINT},
      IS_SEND_MESSAGE = #{isSendMessage,jdbcType=BIT},
      REGISTER_PLATFORM = #{registerPlatform,jdbcType=INTEGER},
      BELONG_PLATFORM = #{belongPlatform,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      IS_VEDENG_MEMBER = #{isVedengMember,jdbcType=BIT},
      VEDENG_MEMBER_TIME = #{vedengMemberTime,jdbcType=TIMESTAMP},
      IS_REGIONAL_MALL = #{isRegionalMall,jdbcType=INTEGER},
      REGISTER_REGIONAL_MALL = #{registerRegionalMall,jdbcType=INTEGER},
      AUTH_TYPE = #{authType,jdbcType=INTEGER}
    where ERP_ACCOUNT_ID = #{erpAccountId,jdbcType=INTEGER}
  </update>

  <!-- 检查手机号是否注册 -->
  <select id="checkMobileExists" resultType="java.lang.Integer">
    SELECT COUNT(1) FROM T_WEB_ACCOUNT WHERE MOBILE = #{mobile}
  </select>
</mapper>