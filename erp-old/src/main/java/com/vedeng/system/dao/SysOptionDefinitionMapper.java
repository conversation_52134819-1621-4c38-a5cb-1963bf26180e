package com.vedeng.system.dao;
import java.util.Collection;

import java.util.List;

import com.vedeng.trader.model.TraderMedicalCategory;
import org.apache.ibatis.annotations.Param;

import com.vedeng.system.model.SysOptionDefinition;

import javax.inject.Named;

@Named("sysOptionDefinitionMapper")
public interface SysOptionDefinitionMapper {
	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_SYS_OPTION_DEFINITION
	 *
	 * @mbg.generated Mon Mar 25 13:59:53 CST 2019
	 */
	int deleteByPrimaryKey(Integer sysOptionDefinitionId);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_SYS_OPTION_DEFINITION
	 *
	 * @mbg.generated Mon Mar 25 13:59:53 CST 2019
	 */
	int insert(SysOptionDefinition record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_SYS_OPTION_DEFINITION
	 *
	 * @mbg.generated Mon Mar 25 13:59:53 CST 2019
	 */
	int insertSelective(SysOptionDefinition record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_SYS_OPTION_DEFINITION
	 *
	 * @mbg.generated Mon Mar 25 13:59:53 CST 2019
	 */
	SysOptionDefinition selectByPrimaryKey(Integer sysOptionDefinitionId);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_SYS_OPTION_DEFINITION
	 *
	 * @mbg.generated Mon Mar 25 13:59:53 CST 2019
	 */
	int updateByPrimaryKeySelective(SysOptionDefinition record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_SYS_OPTION_DEFINITION
	 *
	 * @mbg.generated Mon Mar 25 13:59:53 CST 2019
	 */
	int updateByPrimaryKey(SysOptionDefinition record);

	/**
	 * 根据parentId查询字典库信息
	 * <p>
	 * Title: getSysOptionDefinitionByParam
	 * </p>
	 * <p>
	 * Description:
	 * </p>
	 * 
	 * @param paramMap
	 * @return
	 * <AUTHOR>
	 * @date 2019年3月25日
	 */
	List<SysOptionDefinition> getSysOptionDefinitionByParam(List<Integer> scopeList);

	List<SysOptionDefinition> getSysOptionDefinitionByOptionType(@Param("optionType") String optionType);

	Integer getSysOptionDefinitionIdByTitleAndParentTitle(@Param("title") String title, @Param("parentTitle") String parentTitle);

	List<SysOptionDefinition> getSysOptionDefinitionByParentTitle(String title);

	/**
	 * @describe 根据Title获取字典库信息
	 * @param title
	 * @return
	 * <AUTHOR>
	 * @date 2020/6/3 11:03:05
	 */
	SysOptionDefinition getSysOptionDefinitionByTitle(String title);

	/**
	 * 获取字典表父子关系拼接字符串
	 * @param closeReasonId
	 * @return
	 */
    String getCloseReasonInfo(Integer closeReasonId);
	/**
	 * <b>Description:</b><br>
	 * 根据国标号查询所有符合的器械id
	 *
	 * @param chnMedical 旧国标中医医疗器械类号
	 * @return java.util.List<com.vedeng.trader.model.TraderMedicalCategory>
	 * @Note <b>Author:</b> Thor <br>
	 *       <b>Date:</b> 2021/4/19 15:06
	 */
    List<TraderMedicalCategory> getCategoryByChnMedical(Integer chnMedical);

	/**
	 * 父级查询字典信息
	 *
	 * @param parentId
	 * @return
	 */
	List<SysOptionDefinition> getDictionaryByParentId(Integer parentId);

	/**
	 * <b>Description:</b><br> 查询字典
	 * @param
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年5月16日 上午11:13:49
	 */
	List<SysOptionDefinition> getList(@Param("customerId")Integer customerId,@Param("attributeId")Integer attributeId);


	/**
	 * 根据多个字典表id查询
	 * @param sysOptionDefinitionIdCollection 字典表
	 * @return List<SysOptionDefinition>
	 */
	List<SysOptionDefinition> findBySysOptionDefinitionIdIn(@Param("sysOptionDefinitionIdCollection")Collection<Integer> sysOptionDefinitionIdCollection);


	List<SysOptionDefinition> getSysOptionDefinitionByRelatedTable(String bussInquiry);

	List<SysOptionDefinition> getSysOptionDefinitionByParentsId(@Param("parentsId") List<Integer> parentsId);


	List<SysOptionDefinition> getSysOptionDefinitionByTitleAndParentId(@Param("title") String title, @Param("parentId") Integer parentId);

}
