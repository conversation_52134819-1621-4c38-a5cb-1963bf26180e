package com.vedeng.erp.trader.service.api;

import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.utils.DateUtilGlobal;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.business.dto.PublicCustomerRecordDto;
import com.vedeng.erp.trader.domain.PublicCustomerRecord;
import com.vedeng.erp.trader.dto.TraderCustomerDto;
import com.vedeng.erp.trader.mapper.PublicCustomerRecordMapper;
import com.vedeng.erp.trader.mapper.TraderCustomerBaseMapper;
import com.vedeng.erp.trader.service.PublicCustomerRecordApiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class PublicCustomerRecordApiServiceImpl implements PublicCustomerRecordApiService {
    public static Logger logger = LoggerFactory.getLogger(PublicCustomerRecordApiServiceImpl.class);

    @Resource
    private TraderCustomerBaseMapper TraderCustomerBaseMapper;

    @Resource
    private PublicCustomerRecordMapper publicCustomerRecordMapper;

    @Override
    public void unLockTrader(Integer traderId, Integer relatedId, Integer relatedType, Integer updater) {
        TraderCustomerDto traderCustomer = TraderCustomerBaseMapper.getTraderCustomerInfoByTraderId(traderId);
        if (traderCustomer == null) {
            return;
        }
        //获取客户进入公海最新记录
        PublicCustomerRecord publicCustomerRecord = publicCustomerRecordMapper.queryInfoByCustId(traderCustomer.getTraderCustomerId());
        if(publicCustomerRecord == null){
            logger.info("客户id{},用户id{},未查询到对应进入公海的记录，无需解锁",traderId,updater);
            return;
        }
        if(!ErpConstant.ONE.equals(publicCustomerRecord.getIsPrivatized()) || publicCustomerRecord.getIsUnlock() > 0){
            //非锁定或已解锁状态下的记录无需解锁
            logger.info("客户id{},用户id{},对应进入公海的记录{},私有状态{},解锁状态：{},非锁定状态",traderId,updater,publicCustomerRecord.getPublicCustomerRecordId(),
                    publicCustomerRecord.getIsPrivatized(),publicCustomerRecord.getIsUnlock());
            return;
        }
        //根据时间判断是否可对公海记录进行解锁
        //当前时间
        Long nowDateLong = DateUtilGlobal.sysTimeMillis();
        //锁定时间+24小时
        Date locktime = DateUtilGlobal.StringToDate(DateUtilGlobal.convertString(publicCustomerRecord.getPrivatizedTime(),"yyyy-MM-dd HH:mm:ss.SSS"),"yyyy-MM-dd HH:mm:ss.SSS");
        Long lockTimeLong = DateUtilGlobal.getDateAfter(locktime,1);
        if(lockTimeLong < nowDateLong){
            //当前时间>锁定后24小时
            logger.info("客户id{},用户id{},对应进入公海的记录{},新增商机/沟通记录时间超出锁定后24h",traderId,updater,publicCustomerRecord.getPublicCustomerRecordId());
            return;
        }

        if (traderCustomer.getAssociatedCustomerGroup() > 0) {
            List<Integer> associatedCustomerList = TraderCustomerBaseMapper.getTraderCustomerListByAssociatedCustomerGroup(traderCustomer.getAssociatedCustomerGroup());
            publicCustomerRecordMapper.unlockPublicCustomer(associatedCustomerList,publicCustomerRecord.getIsUnlock(),relatedId);
        }
    }

    @Override
    public PublicCustomerRecordDto getPublicCustomerRecordDto(Integer traderCustomerId) {
        PublicCustomerRecordDto publicCustomerRecordDto = publicCustomerRecordMapper.getByTraderCustomerId(traderCustomerId,null,null);
        return publicCustomerRecordDto;
    }

    @Override
    public Integer getPublicNum(Integer userId, Integer areaId) {
        Integer publicNum = publicCustomerRecordMapper.getPublicCustomerNum(userId,areaId);
        return publicNum;
    }

    @Override
    public List<PublicCustomerRecordDto> getTopOne(List<Integer> traderIdList) {
        List<PublicCustomerRecordDto> dto = publicCustomerRecordMapper.getMaxRecordList(traderIdList);
        return dto;
    }

    @Override
    public void updateOrgUserId(Long publicCustomerRecordId, Integer originUserId) {
        publicCustomerRecordMapper.updateOrgUserId(Collections.singletonList(publicCustomerRecordId),originUserId);
    }
}
