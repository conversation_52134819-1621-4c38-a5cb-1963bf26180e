package com.vedeng.erp.buyorder.service;

import com.vedeng.erp.buyorder.dto.*;

import java.util.List;

public interface RBuyorderExpenseJSaleorderService {
    /**
     * 建立销售单和采购费用单关联关系
     *
     * @param buyorderExpenseDto * @param saleorderGoodsIds
     */
    void addExpenseJ(BuyorderExpenseDto buyorderExpenseDto);

    /**
     * 获取销售单和采购费用单关联关系
     *
     * @param buyorderExpenseItemDtoList
     * @return
     */
    List<RBuyorderExpenseJSaleorderDto> getRelatedDetail(List<BuyorderExpenseItemDto> buyorderExpenseItemDtoList);

    /**
     * 更新销售单和采购费用单关联数量
     *
     * @param buyorderExpenseItemDtos
     */
    void updateBuyNum(List<BuyorderExpenseItemDto> buyorderExpenseItemDtos);

    /**
     * 销售页面虚拟商品需替换的采购状态
     * @param ids
     * @return
     */
    List<ExpenseBuyForSaleDetail> replaceExpenseBuyDetails(List<Integer> ids);

    /**
     * 根据虚拟费用单Id查询关联的销售单id
     * @param buyorderExpenseId
     * @return
     */
    List<Integer> findSaleOrderIds(Integer buyorderExpenseId);
}
