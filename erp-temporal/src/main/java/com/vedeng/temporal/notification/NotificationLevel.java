package com.vedeng.temporal.notification;

/**
 * 通知级别枚举（简化版）
 * 集成了消息模板，简化整体架构
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 2.0
 * @since 2025-01-31
 */
public enum NotificationLevel {
    
    /**
     * 业务级别错误
     * 如：业务规则验证失败、前置条件不满足等
     */
    BUSINESS("业务错误", 
        "## <font color=\"orange\">ERP工作流业务失败</font>\n" +
        "**业务ID**: {}\n" +
        "**业务类型**: {}\n" +
        "**目标公司**: {}\n" +
        "**失败操作**: {}\n" +
        "**失败原因**: {}\n" +
        "**发生时间**: {}\n" +
        "**执行耗时**: {}ms\n" +
        "> 请检查业务数据和规则配置"),
    
    /**
     * 技术级别错误
     * 如：API调用失败、数据库连接异常等
     */
    TECHNICAL("技术错误",
        "## <font color=\"red\">ERP工作流技术异常</font>\n" +
        "**业务ID**: {}\n" +
        "**异常类型**: {}\n" +
        "**异常信息**: {}\n" +
        "**失败操作**: {}\n" +
        "**发生时间**: {}\n" +
        "**执行耗时**: {}ms\n" +
        "> 请检查系统状态和网络连接"),
    
    /**
     * 工作流级别错误
     * 如：工作流引擎异常、配置错误等
     */
    WORKFLOW("工作流错误",
        "## <font color=\"red\">ERP多公司工作流失败</font>\n" +
        "**工作流ID**: {}\n" +
        "**业务类型**: {}\n" +
        "**公司序列**: {}\n" +
        "**失败公司**: {}\n" +
        "**失败原因**: {}\n" +
        "**发生时间**: {}\n" +
        "**影响范围**: {}\n" +
        "> 请立即检查工作流状态和数据一致性");
    
    private final String description;
    private final String template;
    
    NotificationLevel(String description, String template) {
        this.description = description;
        this.template = template;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getTemplate() {
        return template;
    }
}
