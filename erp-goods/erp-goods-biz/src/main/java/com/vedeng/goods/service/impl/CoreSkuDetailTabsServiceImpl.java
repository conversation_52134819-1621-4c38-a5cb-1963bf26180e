package com.vedeng.goods.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.goods.common.constant.ErpGoodsConstant;
import com.vedeng.goods.domain.entity.CoreSku;
import com.vedeng.goods.dto.CoreSkuDetailTabsQueryAllDto;
import com.vedeng.goods.query.CoreSkuDetailTabsQuery;
import com.vedeng.goods.feign.op.RemoteOpApiService;
import com.vedeng.goods.mapper.CoreSkuMapper;
import com.vedeng.goods.service.CoreSkuDetailTabsService;
import com.vedeng.goods.common.util.ByteUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CoreSkuDetailTabsServiceImpl implements CoreSkuDetailTabsService {

    @Autowired
    private CoreSkuMapper coreSkuMapper;

    @Autowired
    private RemoteOpApiService remoteOpApiService;

    /**
     * 掌上小贝Node域名
     */
    @Value("${hand_node_prefix_url:}")
    private String handNodePrefixUrl;

    /**
     * 商城展示Url
     */
    @Value("${market_show_prefix_url:}")
    private String marketShowPrefixUrl;


    @Override
    public List<CoreSkuDetailTabsQueryAllDto> queryAllTabs(CoreSkuDetailTabsQuery coreSkuDetailTabsQuery) {

        List<CoreSkuDetailTabsQueryAllDto> coreSkuDetailTabsQueryAllDtoList = new ArrayList<>();

        // 默认包含 [基本信息] 和 [资料下载]
        String baseInfoUrl = handNodePrefixUrl + ErpGoodsConstant.LinkUrls.NODE_BASE_INFO_PREFIX_URL + '?' + "skuId=" + coreSkuDetailTabsQuery.getSkuId();
        CoreSkuDetailTabsQueryAllDto baseInfoTabDto = new CoreSkuDetailTabsQueryAllDto("基本信息", baseInfoUrl);
        String docDownloadUrl = handNodePrefixUrl + ErpGoodsConstant.LinkUrls.DOC_DOWNLOAD_PREFIX_URL;
        CoreSkuDetailTabsQueryAllDto docDownloadTabDto = new CoreSkuDetailTabsQueryAllDto("资料下载", docDownloadUrl);

        coreSkuDetailTabsQueryAllDtoList.add(baseInfoTabDto);
        coreSkuDetailTabsQueryAllDtoList.add(docDownloadTabDto);

        // 根据 [上下架] 判断是否添加 [商城展示]
        boolean isOnSale = queryOnSale(coreSkuDetailTabsQuery.getSkuId());
        if (!isOnSale) {
            return coreSkuDetailTabsQueryAllDtoList;
        }
        String marketShowUrl = (StringUtils.isEmpty(marketShowPrefixUrl) ? "/" : marketShowPrefixUrl) + 'V' + coreSkuDetailTabsQuery.getSkuId() + ".html";
        CoreSkuDetailTabsQueryAllDto marketShowTabDto = new CoreSkuDetailTabsQueryAllDto("商城展示", marketShowUrl);
        coreSkuDetailTabsQueryAllDtoList.add(marketShowTabDto);

        return coreSkuDetailTabsQueryAllDtoList;
    }

    /**
     * OP是否上下架
     *
     * @param skuId
     * @return
     */
    private boolean queryOnSale(Integer skuId) {

        log.info("掌上小贝Tabs页：获取商品上下架状态，入参[{}]", skuId);

        RestfulResult<List<String>> responseDtoRestfulResult = remoteOpApiService.getAllSkuNos(ErpGoodsConstant.NUMBER_ONE);
        if (Objects.isNull(responseDtoRestfulResult)) {
            log.error("掌上小贝Tabs页：获取商品上下架状态,调用Op异常");
            return false;
        }
        List<String> data = responseDtoRestfulResult.getData();
        if (CollectionUtil.isEmpty(data)) {
            log.error("掌上小贝Tabs页：获取商品上下架状态,调用Op失败,未获取有效SKU_NO集合");
            return false;
        }

        CoreSku coreSku = coreSkuMapper.findBySkuId(skuId);
        if (Objects.isNull(coreSku) || Objects.isNull(coreSku.getSkuNo())) {
            return false;
        }

        // 该SKU不在上架列表里
        if (!data.contains(coreSku.getSkuNo())) {
            return false;
        }
        return true;
    }

    /**
     * ERP是否上下架
     *
     * @param skuId skuId
     * @return Boolean
     */
    public Boolean isOrNotOnSale(Integer skuId) {

        CoreSku coreSku = coreSkuMapper.findBySkuId(skuId);
        if (Objects.isNull(coreSku)) {
            throw new ServiceException("未搜索到该商品！");
        }
        if (Objects.isNull(coreSku.getPushStatus()) || Objects.isNull(coreSku.getOnSale())) {
            return Boolean.FALSE;
        }
        List<Byte> onSaleStatusByteArray = getOnSaleStatusByteArray(coreSku.getPushStatus(), coreSku.getOnSale());
        log.info("skuId {} 推送状态 {} 上下架状态 {} 上架状态：{}", skuId, coreSku.getPushStatus(), coreSku.getOnSale(), onSaleStatusByteArray);
        if (CollectionUtil.isEmpty(onSaleStatusByteArray)) {
            return Boolean.FALSE;
        }
        int onSaleCount = onSaleStatusByteArray.stream().mapToInt(Byte::intValue).reduce(Integer::sum).orElse(0);
        return onSaleCount > 0;
    }

    /**
     * 在推送状态下，【上下架状态】字节码数组
     *
     * @param pushStatus 推送状态
     * @param onSale     上下架状态
     * @return 空集合：未上架；集合Item：1-上架 0-下架
     */
    private List<Byte> getOnSaleStatusByteArray(Integer pushStatus, Integer onSale) {

        int[] pushStatusArray = ByteUtil.getBitArrayByIntNumber(pushStatus);
        int[] onSaleArray = ByteUtil.getBitArrayByIntNumber(onSale);

        List<Byte> resultByteList = new ArrayList<>();
        for (int i = ErpGoodsConstant.CommonNumbers.ZERO; i < ErpGoodsConstant.CommonNumbers.THREE; i++) {
            Byte mid = null;
            if (pushStatusArray[i] == ErpGoodsConstant.PushStatus.ON) {
                if (onSaleArray[i] == ErpGoodsConstant.OnSaleStatus.ON) {
                    mid = ErpGoodsConstant.OnSaleStatus.ON;
                }
                if (onSaleArray[i] == ErpGoodsConstant.OnSaleStatus.OFF) {
                    mid = ErpGoodsConstant.OnSaleStatus.OFF;
                }
                if (Objects.isNull(mid)) {
                    continue;
                }
                resultByteList.add(mid);
            }
        }
        return resultByteList;
    }
}
