package com.vedeng.erp.system.service.impl;

import com.vedeng.common.core.enums.InvoiceApplyCheckRuleEnum;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.mapper.SysOptionDefinitionMapper;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 字典表
 * @date 2022/7/15 14:55
 */
@Service
@Slf4j
public class SysOptionDefinitionApiServiceImpl implements SysOptionDefinitionApiService {

    @Autowired
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;

    @Override
    public List<SysOptionDefinitionDto> getOptionDefinitionListByOptType(String optType) {
        return sysOptionDefinitionMapper.getOptionDefinitionByOptType(optType);
    }

    @Override
    public List<SysOptionDefinitionDto> getOptionDefinitionListByParentCode(String parentCode) {
        SysOptionDefinitionDto parent = sysOptionDefinitionMapper.getOptionDefinitionByCode(parentCode);
        return parent == null ? null : sysOptionDefinitionMapper.getOptionDefinitionListByParentId(parent.getSysOptionDefinitionId());
    }

    @Override
    public SysOptionDefinitionDto getOptionDefinitionById(Integer id) {
        SysOptionDefinitionDto definitionDto = sysOptionDefinitionMapper.getById(id);
        return definitionDto == null ? new SysOptionDefinitionDto() : definitionDto;
    }

    @Override
    public SysOptionDefinitionDto getOptionDefinition(String code) {
        return sysOptionDefinitionMapper.getOptionDefinitionByCode(code);
    }

    @Override
    public List<SysOptionDefinitionDto> getByIds(List<Integer> ids) {
        return sysOptionDefinitionMapper.getByIds(ids);
    }

    @Override
    public List<SysOptionDefinitionDto> getByParentIdList(List<Integer> parentIds) {
        return sysOptionDefinitionMapper.getByParentIdList(parentIds);
    }

    @Override
    public int updateInvoiceSwitch(SysOptionDefinitionDto updateDto) {
        Integer oldStatus = Integer.valueOf(1).equals(updateDto.getStatus())?Integer.valueOf(0):Integer.valueOf(1);
        return sysOptionDefinitionMapper.updateInvoiceSwitch(updateDto.getSysOptionDefinitionId(),updateDto.getStatus(),oldStatus);
    }

    @Override
    public Boolean getInvoiceApplyButtonShow(InvoiceApplyCheckRuleEnum invoiceApplyCheckRuleEnum) {
        return Optional.ofNullable(sysOptionDefinitionMapper.getById(invoiceApplyCheckRuleEnum.getCode())).map(SysOptionDefinitionDto::getStatus).orElse(0).equals(1);

    }
}