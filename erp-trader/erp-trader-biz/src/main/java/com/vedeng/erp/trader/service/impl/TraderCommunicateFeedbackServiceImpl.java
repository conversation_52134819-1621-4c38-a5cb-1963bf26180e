package com.vedeng.erp.trader.service.impl;

import com.vedeng.erp.trader.domain.entity.TraderCommunicateFeedbackEntity;
import com.vedeng.erp.trader.mapper.TraderCommunicateFeedbackMapper;
import com.vedeng.erp.trader.service.TraderCommunicateFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TraderCommunicateFeedbackServiceImpl implements TraderCommunicateFeedbackService {

    @Autowired
    private TraderCommunicateFeedbackMapper traderCommunicateFeedbackMapper;

    @Override
    public void add(TraderCommunicateFeedbackEntity traderCommunicateFeedbackEntity) {
        traderCommunicateFeedbackMapper.insertSelective(traderCommunicateFeedbackEntity);
    }
}
