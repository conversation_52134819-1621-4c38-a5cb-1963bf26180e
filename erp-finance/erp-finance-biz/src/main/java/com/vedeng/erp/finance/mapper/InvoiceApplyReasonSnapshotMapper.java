package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.InvoiceApplyReasonSnapshotEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface InvoiceApplyReasonSnapshotMapper {
    int deleteByPrimaryKey(Long invoiceApplyReasonId);

    int insert(InvoiceApplyReasonSnapshotEntity record);

    int insertOrUpdate(InvoiceApplyReasonSnapshotEntity record);

    int insertOrUpdateSelective(InvoiceApplyReasonSnapshotEntity record);

    int insertSelective(InvoiceApplyReasonSnapshotEntity record);

    InvoiceApplyReasonSnapshotEntity selectByPrimaryKey(Long invoiceApplyReasonId);

    int updateByPrimaryKeySelective(InvoiceApplyReasonSnapshotEntity record);

    int updateByPrimaryKey(InvoiceApplyReasonSnapshotEntity record);

    int updateBatch(List<InvoiceApplyReasonSnapshotEntity> list);

    int updateBatchSelective(List<InvoiceApplyReasonSnapshotEntity> list);

    int batchInsert(@Param("list") List<InvoiceApplyReasonSnapshotEntity> list);
}