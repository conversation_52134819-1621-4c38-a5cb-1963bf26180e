<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<div id="app" style="display: none;">

    <el-form :rules="rules" label-width="100px" ref="form" :model="form">

        <el-row>
            <el-col :span="24">
                <el-form-item label="支行名称：" prop="bankName">
                    <el-input v-model="form.bankName" style="width: 80%;" ></el-input>
                </el-form-item>

                <el-form-item label="联行号：" prop="bankNo" >
                    <el-input v-model="form.bankNo"  style="width: 80%;" :readonly="isReadonly"></el-input>
                </el-form-item>

            </el-col>
        </el-row>

        <el-form-item>
            <el-button @click="closeThis">取消</el-button>
            <el-button type="primary" @click="onSubmit('form')" :loading="onSubmitLoading">保存</el-button>
        </el-form-item>

    </el-form>


</div>

<script src="${pageContext.request.contextPath}/static/api/system/bank.js?rnd=${resourceVersionKey}"></script>


<script type="text/javascript">

    const ID = '${bankId}'

    new Vue({
        el: '#app',
        data() {
            return {
                onSubmitLoading:false,
                //表单校验
                rules: {
                    bankName: [
                        {required: true, message: '请填写支行名称', trigger: 'blur'}
                    ],
                    bankNo: [
                        {required: true, message: '请填写联行号', trigger: 'blur'}
                    ],
                },
                //表单
                form: {
                    bankId: ID,
                    bankName: null,
                    bankNo: null,
                },
                isReadonly:false
            };
        },
        mounted() {
            loadingApp()
        },
        created() {
            this.initForm();
        },
        methods: {
            closeThis() {
                parent.layer.close(index);
            },
            onSubmit(form) {
                this.onSubmitLoading = true
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        if (this.form.bankId === '') {
                            addBank(this.form).then(res => {
                                if (res.data.code === 0) {
                                    window.parent.location.reload();
                                    parent.layer.close(index);
                                } else {
                                    this.$message({
                                        message: res.data.message,
                                        duration: 1000,
                                        showClose:true,
                                        type: 'error',
                                    });
                                }
                                this.onSubmitLoading = false
                            });
                        } else {
                            let update = {
                                bankId: this.form.bankId,
                                bankName: this.form.bankName,
                                bankNo: this.form.bankNo,
                            }
                            updateBank(update).then(res => {
                                if (res.data.code === 0) {
                                    window.parent.location.reload();
                                    parent.layer.close(index);
                                } else {
                                    this.$message({
                                        message: res.data.message,
                                        duration: 1000,
                                        showClose:true,
                                        type: 'error',
                                    });
                                }
                                this.onSubmitLoading = false
                            });
                        }
                    }
                    this.onSubmitLoading = false
                })
            },

            initForm() {
                if (this.form.bankId !== '') {
                    this.isReadonly = true
                    getBank({bankId:this.form.bankId}).then(res => {
                        if (res.data.code === 0) {
                            this.form = res.data.data
                        }
                    });
                }
            }

        }
    });


</script>

<style>


</style>
