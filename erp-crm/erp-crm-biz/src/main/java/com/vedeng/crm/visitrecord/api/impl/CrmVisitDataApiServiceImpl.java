package com.vedeng.crm.visitrecord.api.impl;

import com.vedeng.crm.visitrecord.api.CrmVisitDataApiService;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;
import com.vedeng.crm.visitrecord.mapper.CrmVisitRecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/14
 */
@Service
public class CrmVisitDataApiServiceImpl implements CrmVisitDataApiService {
    @Autowired
    private CrmVisitRecordMapper visitRecordMapper;
    @Override
    public List<VisitRecordVo> selectVisitRecordByRelateId(Integer relateId, Integer relateType) {
        return visitRecordMapper.selectVisitRecordByRelateId(relateId,relateType);
    }
}
