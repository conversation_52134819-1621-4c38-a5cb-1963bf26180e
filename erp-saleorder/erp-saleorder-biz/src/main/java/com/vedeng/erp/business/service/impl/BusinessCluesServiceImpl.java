package com.vedeng.erp.business.service.impl;

import com.vedeng.authorization.dao.RegionMapper;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.crm.admin.api.dto.tuoke.BusinessCluesDetailDTO;
import com.vedeng.erp.business.dao.BusinessCluesMapper;
import com.vedeng.erp.business.domain.entity.BusinessClues;
import com.vedeng.erp.business.domain.vo.BusinessCluesVo;
import com.vedeng.erp.business.service.BusinessCluesService;
import com.vedeng.erp.businessclues.api.BusinessCluesApi;
import com.vedeng.erp.businessclues.dto.BusinessCluesDto;
import com.vedeng.trader.dao.TraderContactGenerateMapper;
import com.vedeng.trader.model.vo.TraderContactVo;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/3/10 17:10
 **/
@Service
public class BusinessCluesServiceImpl implements BusinessCluesService, BusinessCluesApi {

    @Resource
    private BusinessCluesMapper businessCluesMapper;

    @Resource
    private RegionMapper regionMapper;
    
    @Resource
    private TraderContactGenerateMapper traderContactGenerateMapper;

    @Override
    public Map<String, Object> getBusinessCluesListPage(Map<String, Object> queryParam) {
        List<BusinessCluesVo> businessCluesListPage = businessCluesMapper.getBusinessClueslistpage(queryParam);
        // 处理客户地区
        businessCluesListPage.forEach(businessCluesVo -> {
            if (!StringUtils.isEmpty(businessCluesVo.getAreaIds())) {
                String[] areIds = businessCluesVo.getAreaIds().split(",");
                StringBuilder traderArea = new StringBuilder();
                for (String areId : areIds) {
                    traderArea.append(regionMapper.getRegionById(Integer.valueOf(areId)).getRegionName()).append(" ");
                }
                businessCluesVo.setTraderArea(traderArea.toString());
            }
        });

        Map<String, Object> result = new HashMap<>();
        result.put("businessCluesVoList", businessCluesListPage);
        result.put("page", queryParam.get("page"));
        return result;
    }

    @Override
    public int changeTopStatus(BusinessCluesVo businessCluesVo) {
        if (businessCluesVo.getTop().equals(ErpConst.ONE)) {
            businessCluesVo.setTopTime(System.currentTimeMillis());
        } else {
            businessCluesVo.setTopTime(0L);
        }
        return businessCluesMapper.updateByPrimaryKeySelective(businessCluesVo);
    }

    @Override
    public void updateBusinessCluesChanceId(BusinessCluesDto businessCluesDto) {
        BusinessClues businessClues = new BusinessClues();
        businessClues.setBusinessCluesId(businessCluesDto.getBusinessCluesId());
        businessClues.setBusinessChanceId(businessCluesDto.getBusinessChanceId());
        businessCluesMapper.updateByPrimaryKeySelective(businessClues);
    }

    @Override
    public BusinessCluesDto selectBusinessChanceByCluesId(Integer businessCluesId) {
        BusinessClues businessClues = businessCluesMapper.selectByPrimaryKey(businessCluesId);
        return BusinessCluesDto.builder().businessChanceId(businessClues.getBusinessChanceId()).build();
    }

    @Override
    public int saveCluesWorth(BusinessCluesVo businessCluesVo) {
        return businessCluesMapper.updateByPrimaryKeySelective(businessCluesVo);
    }

    @Override
    public BusinessClues getCluesInfo(Integer businessCluesId) {
        return businessCluesMapper.selectByPrimaryKey(businessCluesId);
    }

    @Override
    public List<TraderContactVo> getTraderContactList(Integer traderId, List<BusinessCluesDetailDTO> cluesDetailDTOS) {
        // step1 根据traderId查出所有的联系人信息
        List<TraderContactVo> traderContactList = traderContactGenerateMapper.getTraderContactList(traderId);

        // step2 根据联系人名称与CRM传来的中标信息中的联系人信息进行匹配，获取电话号码和手机号
        Set<String> contactNameList = new HashSet<>();
        cluesDetailDTOS.forEach(cluesDetail -> {
            cluesDetail.getZhongbiaoGoodsList().forEach(item -> {
                contactNameList.add(item.getZhongRelationName());
            });
        });

        traderContactList.removeIf(traderContactVo -> !contactNameList.contains(traderContactVo.getName()));
        return traderContactList;
    }
}
