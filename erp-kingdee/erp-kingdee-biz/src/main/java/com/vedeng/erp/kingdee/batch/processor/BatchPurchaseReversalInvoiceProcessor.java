package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayCommonEntity;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseVatPlainInvoiceEntity;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseVatSpecialInvoiceEntity;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePayCommonMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseVatPlainInvoiceMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseVatSpecialInvoiceMapper;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购票的应付单 以及 票的数据推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchPurchaseReversalInvoiceProcessor implements ItemProcessor<BatchInvoiceDto, KingDeeInvoiceRollBackDto> {


    @Autowired
    private KingDeePayCommonMapper kingDeePayCommonMapper;

    @Autowired
    private KingDeePurchaseVatSpecialInvoiceMapper kingDeePurchaseVatSpecialInvoiceMapper;

    @Autowired
    private BatchROldInvoiceJNewInvoiceDtoMapper batchROldInvoiceJNewInvoiceDtoMapper;

    @Autowired
    private KingDeePurchaseVatPlainInvoiceMapper kingDeePurchaseVatPlainInvoiceMapper;

    @Autowired
    private BatchRInvoiceJInvoiceDtoMapper batchRInvoiceJInvoiceDtoMapper;


    @Override
    public KingDeeInvoiceRollBackDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {

        log.info("处理采购冲销票{}" , JSON.toJSONString(batchInvoiceDto));
        // 查询冲销票关联的
        List<BatchInvoiceDto> byRelateInvoiceId = batchRInvoiceJInvoiceDtoMapper.findOriginalBlueEnable(batchInvoiceDto.getInvoiceId());
        if (CollUtil.isEmpty(byRelateInvoiceId)) {
            log.error("冲销票id:{},未能查到原始蓝票",JSON.toJSONString(batchInvoiceDto.getInvoiceId()));
            throw new KingDeeException("冲销票id:"+JSON.toJSONString(batchInvoiceDto.getInvoiceId())+",未能查到原始蓝票");
        }

        List<KingDeeInvoiceRollBackDto.KingDeeInvoiceAndId> result = new ArrayList<>();
        KingDeeInvoiceRollBackDto kingDeeInvoiceRollBackDto = new KingDeeInvoiceRollBackDto();
        kingDeeInvoiceRollBackDto.setOriginalInvoice(result);
        kingDeeInvoiceRollBackDto.setInvoiceId(batchInvoiceDto.getInvoiceId());
        List<String> invoiceIds = byRelateInvoiceId.stream().map(c -> c.getInvoiceId().toString()).collect(Collectors.toList());
        // 所有的原始蓝票 + 新蓝票
        List<Integer> invoiceIdSs = byRelateInvoiceId.stream().map(BatchInvoiceDto::getInvoiceId).collect(Collectors.toList());
        List<Integer> byNewInvoiceIds = batchROldInvoiceJNewInvoiceDtoMapper.findByNewInvoiceIds(invoiceIdSs);
        if (CollUtil.isNotEmpty(byNewInvoiceIds)) {
            invoiceIdSs.addAll(byNewInvoiceIds);
        }
        List<String> strings = invoiceIds.stream().map(String::toString).collect(Collectors.toList());
        List<KingDeePayCommonEntity> payIds = kingDeePayCommonMapper.findByFQzokBddjtId(strings);
        log.info("invoice:{}查询到的原采购应付单集合:{}",batchInvoiceDto.getInvoiceId(),JSON.toJSONString(payIds));

        // 专票
        List<KingDeePurchaseVatSpecialInvoiceEntity> specialIds = kingDeePurchaseVatSpecialInvoiceMapper.findByFQzokBddjtid(invoiceIds);
        log.info("invoice:{}查询到的推送到金蝶的专票:{}",batchInvoiceDto.getInvoiceId(),JSON.toJSONString(specialIds));
        if (CollUtil.isNotEmpty(specialIds)) {
            specialIds.forEach(x -> {
                KingDeeInvoiceRollBackDto.KingDeeInvoiceAndId data = new KingDeeInvoiceRollBackDto.KingDeeInvoiceAndId();
                data.setFid(x.getFid());
                data.setSpecial(Boolean.TRUE);
                data.setId(Integer.valueOf(x.getFQzokBddjtid()));
                result.add(data);
            });
        }


        // 普票
        List<KingDeePurchaseVatPlainInvoiceEntity> plainIds = kingDeePurchaseVatPlainInvoiceMapper.findByFQzokBddjtid(invoiceIds);
        log.info("invoice:{}查询到的推送到金蝶的普票:{}",batchInvoiceDto.getInvoiceId(),JSON.toJSONString(plainIds));
        if (CollUtil.isNotEmpty(plainIds)) {
            plainIds.forEach(x -> {
                KingDeeInvoiceRollBackDto.KingDeeInvoiceAndId data = new KingDeeInvoiceRollBackDto.KingDeeInvoiceAndId();
                data.setFid(x.getFid());
                data.setId(Integer.valueOf(x.getFQzokBddjtid()));
                result.add(data);
            });
        }

        // 应付单
        if (CollUtil.isNotEmpty(payIds)) {
            payIds.forEach(x -> {
                Optional<KingDeeInvoiceRollBackDto.KingDeeInvoiceAndId> first = result.stream().filter(a -> a.getId().toString().equals(x.getFQzokBddjtId())).findFirst();
                first.ifPresent(kingDeeInvoiceAndId -> kingDeeInvoiceAndId.setPayId(x.getFId()));
            });
        }
        return kingDeeInvoiceRollBackDto;
    }

}
