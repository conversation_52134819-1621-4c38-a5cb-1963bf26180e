package com.newtask;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.order.chain.model.BuyorderRiskModelVo;
import com.vedeng.order.chain.model.SaleorderRiskModelVo;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.RiskCheckService;
import com.vedeng.todolist.constant.RiskCheckTodoListBuzPropertyEnum;
import com.vedeng.todolist.model.TodoList;
import com.vedeng.todolist.service.RiskCheckLogService;
import com.vedeng.todolist.service.TodoListService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @ClassName TodoFinishTask.java
 * @Description TODO 待办事项task
 * @createTime 2020年12月19日 16:07:00
 */
@Component
@JobHandler(value="TodoFinishTask")
public class TodoFinishTask extends AbstractJobHandler {

    private Logger logger = LoggerFactory.getLogger(TodoFinishTask.class);

    @Value("${erp_url}")
    private String erpUrl;

    @Value("${risk_key}")
    protected String riskKey;

    @Autowired
    private RiskCheckService riskCheckService;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Autowired
    private RiskCheckLogService riskCheckLogService;

    @Autowired
    private TodoListService todoListService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        executeSaleorder();

        executeBuyorder();

        executeFinishOrder();

        return SUCCESS;
    }

    private void executeFinishOrder() {
        executeFinishBuyorder();

        executeFinishSaleorder();

    }

    private void executeFinishSaleorder() {
        Saleorder searchSaleorder = new Saleorder();
        searchSaleorder.setIsRisk(1);
        searchSaleorder.setStatus(1);
        List<Saleorder> saleorderList = saleorderMapper.getFinishOrderStatus(searchSaleorder);
        Saleorder updateSaleorder = new Saleorder();
        updateSaleorder.setIsRisk(3);
        for (Saleorder saleorder : saleorderList) {
            updateSaleorder.setSaleorderId(saleorder.getSaleorderId());
            List<TodoList>  saleorderTodoList = todoListService.getTodoListByExtraProperty(saleorder.getSaleorderNo(),RiskCheckTodoListBuzPropertyEnum.RISK_CHECK_SALEORDER.getValue());
            if(CollectionUtils.isEmpty(saleorderTodoList)){
                return;
            }
            Optional<TodoList> firstSale = saleorderTodoList.stream().filter(item -> item.getStatus().equals(0)).findFirst();
            if(!firstSale.isPresent()){
                saleorderMapper.updateByPrimaryKeySelective(updateSaleorder);
                XxlJobLogger.log("TodoFinishTask executeFinishSaleorder no:{}",saleorder.getSaleorderNo());
            }
        }
    }

    private void executeFinishBuyorder() {
        Buyorder searchBuyorder = new Buyorder();
        searchBuyorder.setIsRisk(1);
        searchBuyorder.setStatus(1);
        List<Buyorder> buyorderList = buyorderMapper.getFinishOrderStatus(searchBuyorder);
        Buyorder updateBuyorder = new Buyorder();
        updateBuyorder.setIsRisk(3);
        for (Buyorder buyorder : buyorderList) {
            updateBuyorder.setBuyorderId(buyorder.getBuyorderId());
            List<TodoList>  buyorderTodoList = todoListService.getTodoListByExtraProperty(buyorder.getBuyorderNo(),RiskCheckTodoListBuzPropertyEnum.RISK_CHECK_BUYORDER.getValue());
            if(CollectionUtils.isEmpty(buyorderTodoList)){
                return;
            }
            Optional<TodoList> firstBuyorder = buyorderTodoList.stream().filter(item -> item.getStatus().equals(0)).findFirst();
            if(!firstBuyorder.isPresent()){
                buyorderMapper.updateByPrimaryKeySelective(updateBuyorder);
                XxlJobLogger.log("TodoFinishTask executeFinishBuyorder no:{}",buyorder.getBuyorderNo());
            }
        }
    }

    private void executeBuyorder() throws Exception {
        String url = erpUrl +"order/buyorder/editApplyValidBuyorder.do";
        List<String> buyorderNoList = riskCheckLogService.getUnTriggeredOrderOfRickCheck(RiskCheckTodoListBuzPropertyEnum.RISK_CHECK_BUYORDER.getValue());
        Buyorder update = new Buyorder();
        update.setIsRisk(3);

        for (String buyorderNo : buyorderNoList) {
            logger.info("TodoFinishTask executeBuyorder start no:{}",buyorderNo);
            XxlJobLogger.log("TodoFinishTask executeBuyorder start no:{}",buyorderNo);
            Buyorder buyorder = buyorderMapper.getBuyOrderByOrderNo(buyorderNo);
            update.setBuyorderId(buyorder.getBuyorderId());
            if(buyorder == null || !buyorder.getStatus().equals(0)){
                riskCheckLogService.autoFinishTriggerOfRiskCheck(buyorderNo);
                buyorderMapper.updateByPrimaryKeySelective(update);
                logger.info("TodoFinishTask executeBuyorder 状态不可审核 no:{}",buyorderNo);
                XxlJobLogger.log("TodoFinishTask executeBuyorder 状态不可审核 no:{}",buyorderNo);
                continue;
            }
            BuyorderRiskModelVo buyorderRiskModelVo = riskCheckService.riskCheckBuyOrder(buyorder);
            logger.info("TodoFinishTask executeBuyorder no:{},isRisk:{}",buyorderNo,buyorderRiskModelVo.getIsRisk());
            XxlJobLogger.log("TodoFinishTask executeBuyorder no:{},isRisk:{}",buyorderNo,buyorderRiskModelVo.getIsRisk());
            if(buyorderRiskModelVo.getIsRisk()){
                Map<String,String> map = new HashMap<>();
                map.put("buyorderId",buyorder.getBuyorderId().toString());
                map.put("riskKey",riskKey);
                map.put("taskId","0");

                ResultInfo resultInfo = NewHttpClientUtils.doGet(url, map);
                logger.info("TodoFinishTask executeBuyorder no:{},result:{}",buyorderNo, JSON.toJSONString(resultInfo));
                XxlJobLogger.log("TodoFinishTask executeBuyorder no:{},result:{}",buyorderNo, JSON.toJSONString(resultInfo));
                if(resultInfo != null && resultInfo.getData() != null) {
                    ResultInfo data = JsonUtils.readValue(resultInfo.getData().toString(), ResultInfo.class);
                    if(isUpdate(data)){
                        buyorderMapper.updateByPrimaryKeySelective(update);
                        riskCheckLogService.autoFinishTriggerOfRiskCheck(buyorderNo);
                    }
                }
            }
        }
    }

    private void executeSaleorder() throws Exception {
        String url = erpUrl + "order/saleorder/editApplyValidSaleorder.do";
        List<String> saleorderNoList = riskCheckLogService.getUnTriggeredOrderOfRickCheck(RiskCheckTodoListBuzPropertyEnum.RISK_CHECK_SALEORDER.getValue());
        Saleorder update  = new Saleorder();
        update.setIsRisk(3);
        Saleorder search = new Saleorder();
        for (String saleorderNo : saleorderNoList) {
            logger.info("TodoFinishTask executeSaleorder start no:{}",saleorderNo);
            XxlJobLogger.log("TodoFinishTask executeSaleorder start no:{}",saleorderNo);
            search.setSaleorderNo(saleorderNo);
            Saleorder saleOrderInfo = saleorderMapper.getSaleorderBySaleorderNo(search);
            update.setSaleorderId(saleOrderInfo.getSaleorderId());
            if(saleOrderInfo == null || !saleOrderInfo.getStatus().equals(0)){
                riskCheckLogService.autoFinishTriggerOfRiskCheck(saleorderNo);
                saleorderMapper.updateByPrimaryKeySelective(update);
                logger.info("TodoFinishTask executeSaleorder 状态不可审核 no:{}",saleorderNo);
                XxlJobLogger.log("TodoFinishTask executeSaleorder 状态不可审核 no:{}",saleorderNo);
                continue;
            }
            SaleorderRiskModelVo saleorderRiskModelVo = riskCheckService.riskCheckSaleOrder(saleOrderInfo);
            logger.info("TodoFinishTask executeSaleorder no:{},isRisk:{}",saleorderNo,saleorderRiskModelVo.getIsRisk());
            XxlJobLogger.log("TodoFinishTask executeSaleorder no:{},isRisk:{}",saleorderNo,saleorderRiskModelVo.getIsRisk());
            if(saleorderRiskModelVo.getIsRisk()){
                Map<String,String> map = new HashMap<>();
                map.put("saleorderId",saleOrderInfo.getSaleorderId().toString());
                map.put("riskKey",riskKey);
                map.put("taskId","0");

                ResultInfo resultInfo = NewHttpClientUtils.doGet(url, map);
                logger.info("TodoFinishTask executeSaleorder no:{},result:{}",saleorderNo, JSON.toJSONString(resultInfo));
                XxlJobLogger.log("TodoFinishTask executeSaleorder no:{},result:{}",saleorderNo, JSON.toJSONString(resultInfo));
                if(resultInfo != null && resultInfo.getData() != null) {
                    ResultInfo data = JsonUtils.readValue(resultInfo.getData().toString(), ResultInfo.class);
                    if(isUpdate(data)){
                        saleorderMapper.updateByPrimaryKeySelective(update);
                        riskCheckLogService.autoFinishTriggerOfRiskCheck(saleorderNo);
                    }
                }
            }
        }
    }

    private boolean isUpdate(ResultInfo data) {
        if(data == null){
            return false;
        }
        if(data.getCode().equals(0)){
            return true;
        }

        return  data.getCode().equals(-1) && ErpConst.APPLY_VALID_ERROR_MESSAGE.equals(data.getMessage());
    }
}
