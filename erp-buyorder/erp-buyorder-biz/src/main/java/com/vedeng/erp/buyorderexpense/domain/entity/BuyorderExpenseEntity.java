package com.vedeng.erp.buyorderexpense.domain.entity;

import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

/**
 * @description 采购费用单
 * <AUTHOR>
 * @date 2022/11/18 9:38
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class BuyorderExpenseEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer buyorderExpenseId;

    /**
     * 采购费用单号
     */
    private String buyorderExpenseNo;

    /**
     * 采购单号
     */
    private String buyorderNo;

    /**
     * 采购ID
     */
    private Integer buyorderId;

    /**
     * 费用单类型0直属采购费用单1非直属采购费用单
     */
    private Integer orderType;

    /**
     * 生效状态 0否 1是
     */
    private Integer validStatus;

    /**
     * 生效时间
     */
    private Date validTime;

    /**
     * 订单状态：0待确认（默认）、1进行中、2已完结、3已关闭
     */
    private Integer status;

    /**
     * 锁定状态0未锁定 1已锁定
     */
    private Integer lockedStatus;

    /**
     * 收票状态0未收票 1部分收票 2全部收票
     */
    private Integer invoiceStatus;

    /**
     * 付款状态 0未付款 1部分付款 2全部付款
     */
    private Integer paymentStatus;

    /**
     * 发货状态0未发货 1部分发货 2全部发货
     */
    private Integer deliveryStatus;

    /**
     * 收货状态0未收货 1部分收货 2全部收货
     */
    private Integer arrivalStatus;

    /**
     * 售后状态 0未售后 1售后中 2售后完成 3售后关闭
     */
    private Integer serviceStatus;

    /**
     * 订单审核状态：0待审核、1审核中、2审核通过、3审核不通过
     */
    private Integer auditStatus;

    /**
     * 收票时间
     */
    private Date invoiceTime;

    /**
     * 付款时间
     */
    private Date paymentTime;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 是否删除0否1是
     */
    private Integer isDelete;

    /**
     * 业务类型：1.采购 2.采购售后
     */
    private Integer businessType;

    /**
     * 售后单号
     */
    private String afterSalesNo;

    /**
     * 售后ID
     */
    private Integer afterSalesId;

    /**
     * 是否退货预警 0否1是
     */
    private Integer isReturnEarlyWarn;

    /**
     * 是否是自动转单创建 0 否 1 是
     */
    private Integer isAuto;
}