$(function () {

    //展开收起
    $('.J-toggle-show').click(function () {
        var $optionalWrap = $(this).siblings().find('.J-optional-more');
        var isShow = $optionalWrap.hasClass('show');
        var $less = $(this).find('.J-less');
        var $more = $(this).find('.J-more');

        if (isShow) {
            $optionalWrap.removeClass('show').slideUp(200);
            $less.hide();
            $more.show();
        } else {
            $optionalWrap.addClass('show').slideDown(200);
            $less.show();
            $more.hide();
        }
    })

    //删除
    var delItem = function (id) {
        var dialog = new artDialog({
            content: $('.J-del-firstengage-tmpl').html(),
            init: function () {
                $('.J-del-firstengage-form').validate({
                    errorWrap: true,
                    rules: {
                        content: {
                            required: true,
                            minlength: 10,
                            maxlength: 300
                        }
                    },
                    messages: {
                        content: {
                            required: '请填写删除原因，10-300个字',
                            minlength: '请填写删除原因，10-300个字',
                            maxlength: '请填写删除原因，10-300个字'
                        }
                    }
                })
            },
            width: 420,
            button: [{
                name: '确定',
                highlight: true,
                callback: function () {
                    if ($('.J-del-firstengage-form').valid()) {
                        $.ajax({
                            url: page_url + '/firstengage/baseinfo/deleteFirstEngage.do',
                            data: {
                                ids: id,
                                comment: $('.J-del-firstengage-form [name="content"]').val()
                            },
                            type: 'post',
                            dataType: 'json',
                            traditional: true,
                            success: function (res) {
                                if (res.code === 0) {
                                    pagesContrlpages(true);
                                } else {
                                    var dia = artDialog.alert(res.message || '操作异常', null, {
                                        fn: function () {
                                            dia.close();
                                        }, text: '我知道了'
                                    }, { type: "warn" });
                                }
                            }
                        })
                    }
                    return false;
                }
            }, {
                name: '取消'
            }],
        })
    };

    $('.J-del').click(function () {
        var id = $(this).data('id');

        delItem([id]);
    })

    //过期处理
    $('.J-overday').click(function () {
        var id = $(this).data('id');

        var dialog = artDialog.confirm('确认已处理注册证过期内容？', '', {
            fn: function () {
                $.ajax({
                    url: page_url + '/firstengage/baseinfo/dealstatus.do',
                    data: {
                        registrationNumberId: id,
                    },
                    type: 'post',
                    dataType: 'json',
                    traditional: true,
                    success: function (res) {
                        if (res.code === 0) {
                            window.localStorage.setItem('needShowTip', 'true');
                            window.location.reload();
                        } else {
                            var dia = artDialog.alert(res.message || '操作异常', null, {
                                fn: function () {
                                    dia.close();
                                }, text: '我知道了'
                            }, { type: "warn" });
                        }
                    }
                })
            }, text: '提交'
        }, {
                fn: function () {
                    dialog.close();
                }, text: '取消'
            });
    })

    //审核操作
    var tmpl = template($('.J-audit-tmpl').html());
    $('.J-audit').click(function () {
        var _this = this;
        var type = $(this).data('type');
        var auditTip = $.trim($(this).html());

        var auditReq = function (data) {
            $.ajax({
                url: page_url + '/firstengage/baseinfo/check.do',
                data: {
                    firstEngageId: $(_this).data('id'),
                    status: type,
                    signature:data,
                    manufacturerId: manufacturerId,
                    reason: $('.J-audit-form [name="content"]').val()
                },
                type: 'post',
                dataType: 'json',
                success: function (res) {
                    if (res.code === 0) {
                        window.localStorage.setItem('needShowTip', 'true');
                        window.location.reload();
                    }else {
                        layer.alert(res.message)
                    }
                }
            })
        };

        var dialog = new artDialog({
            content: tmpl({ auditTip: auditTip, type: type }),
            init: function () {
                if (type != 3) {
                    $('.J-audit-form').validate({
                        errorWrap: true,
                        rules: {
                            content: {
                                required: true,
                                maxlength: 64
                            }
                        },
                        messages: {
                            content: {
                                required: '请填写原因，最多64个字',
                                maxlength: '请填写原因，最多64个字'
                            }
                        }
                    })
                }
            },
            width: 420,
            button: [{
                name: '确定',
                highlight: true,
                callback: function () {
                    if (type == 3) {
                        auditReq(null);
                    } else {
                        if ($('.J-audit-form').valid()) {
                            auditReq(null);
                        }
                    }

                    return false;
                }
            }, {
                name: '取消'
            }],
        })
    })

    $('.J-audit2').click(function () {
        var _this = this;
        var type = $(this).data('type');
        var auditTip = $.trim($(this).html());

        var auditReq = function (data) {
            $.ajax({
                url: page_url + '/firstengage/baseinfo/check.do',
                data: {
                    firstEngageId: $(_this).data('id'),
                    status: type,
                    signature:data,
                    manufacturerId: manufacturerId,
                    reason: $('.J-audit-form [name="content"]').val()
                },
                type: 'post',
                dataType: 'json',
                success: function (res) {
                    if (res.code === 0) {
                        window.localStorage.setItem('needShowTip', 'true');
                        window.location.reload();
                    }else {
                        window.localStorage.setItem('needShowTip', 'true');
                        window.location.reload();
                    }
                }
            })
        };

        var dialog = new artDialog({
            content: tmpl({ auditTip: auditTip, type: type }),
            init: function () {
                if (type !== 3) {
                    $('.J-audit-form').validate({
                        errorWrap: true,
                        rules: {
                            content: {
                                required: true,
                                maxlength: 64
                            }
                        },
                        messages: {
                            content: {
                                required: '请填写原因，最多64个字',
                                maxlength: '请填写原因，最多64个字'
                            }
                        }
                    })
                }
            },
            width: 420,
            button: [{
                name: '审核通过',
                highlight: true,
                callback: function () {
                    if (type == 3) {
                        auditReq(0);
                    } else {
                        if ($('.J-audit-form').valid()) {
                            auditReq(0);
                        }
                    }

                    return false;
                }
            }, {
                name: '审核通过并进行电子签章',
                highlight: true,
                callback: function () {
                    if (type == 3) {
                        auditReq(1);
                    } else {
                        if ($('.J-audit-form').valid()) {
                            auditReq(0);
                        }
                    }

                    return false;
                }
            },
                {
                    name:'取消'
                }
            ],
        })
    })


    $('.J-audit3').click(function () {
        var _this = this;
        var type = $(this).data('type');
        var auditTip = $.trim($(this).html());

        var auditReq = function (data) {
            $.ajax({
                url: page_url + '/e/goods/firstengage/check.do',
                data: {
                    firstEngageId: $(_this).data('id'),
                    status: type,
                    signature:data,
                    manufacturerId: manufacturerId,
                    reason: $('.J-audit-form [name="content"]').val()
                },
                type: 'post',
                dataType: 'json',
                success: function (res) {
                    if (res.code === 0) {
                        window.localStorage.setItem('needShowTip', 'true');
                        var dialog = new artDialog({
                            content: '已发起电子签章流程。可稍后查看，无需频繁发起。',
                            init: function () {
                            },
                            width: 420,
                            button: [{
                                name: '确定',
                                highlight: true,
                                callback: function () {
                                    window.location.reload();
                                }
                            }],
                        })
                        // window.location.reload();
                    }else {
                        window.localStorage.setItem('needShowTip', 'true');
                        var dia = artDialog.alert(res.message || '操作异常', null, {
                            fn: function () {
                                dia.close();
                                window.location.reload();
                            }, text: '我知道了'
                        }, { type: "warn" });
                    }
                },
                fail: function (res){
                    var dia = artDialog.alert(res.message || '操作异常', null, {
                        fn: function () {
                            dia.close();
                            window.location.reload();
                        }, text: '我知道了'
                    }, { type: "warn" });
                }
            })
        };
        let v = $(_this).data('t');
        // alert(v)
        if (v == 0) {
            auditReq(1);
            $(_this).data('t',1);
            $(_this).attr('disabled',true)
        }

    })


    $('.J-submitCheck').click(function () {
        var _this = this;

        let manufacturerId = $("input[name='registration.manufacturerId']").val();
        $.ajax({
            url: page_url + '/firstengage/baseinfo/submitCheck.do',
            data: {
                firstEngageId: $(_this).data('id'),
                manufacturerId: manufacturerId,
                status: $(this).data('type')
            },
            type: 'post',
            dataType: 'json',
            success: function (res) {
                if (res.code === 0) {
                    window.localStorage.setItem('needShowTip', 'true');
                    window.location.reload();
                }else {
                    layer.alert(res.message)
                }
            }
        })
    })

    $('.J-submitCheck-e').click(function () {
        var _this = this;

        let manufacturerId = $("input[name='registration.manufacturerId']").val();
        $.ajax({
            url: page_url + '/firstengage/baseinfo/submitCheck.do',
            data: {
                firstEngageId: $(_this).data('id'),
                manufacturerId: manufacturerId,
                status: $(this).data('type'),
                signature: $(_this).data('signature')
            },
            type: 'post',
            dataType: 'json',
            success: function (res) {
                if (res.code === 0) {
                    window.localStorage.setItem('needShowTip', 'true');
                    window.location.reload();
                }else {
                    layer.alert(res.message)
                }
            }
        })
    })

    //查看大图
     GLOBAL.showLargePic('.J-show-big');

    $("body").on("click",".printAtta",function(){
        $(this).hide();
        $(this).prev().printArea();
        $(this).show()
    });

    $(".tip-wrap").on({
        mouseenter:function(){
            var that = this;
            tips =layer.tips("<span style='color:#000;'>电子签章失败，请手动发起</span>",that,{tips:[2,'#fff'],time:0,area: 'auto',maxWidth:500});
        },
        mouseleave:function(){
            layer.close(tips);
        }
    })


    //操作提示
    GLOBAL.showGlobalTip('操作成功', null, 'needShowTip');
    GLOBAL.showGlobalTip('保存成功', null, 'addsuccess');

})