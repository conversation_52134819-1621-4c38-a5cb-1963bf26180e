package com.vedeng.erp.kingdee.batch.common.handle;

import com.ctrip.framework.apollo.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.batch.core.step.skip.SkipLimitExceededException;
import org.springframework.batch.core.step.skip.SkipPolicy;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义异常跳过策略
 * @date 2023/4/12 13:29
 */
@Slf4j
public class CustomSkipPolicy implements SkipPolicy {

    private static final int MAX_SKIP_COUNT = Integer.parseInt(ConfigService.getAppConfig().getProperty("kingdee.error.skip.count", ""));


    @Override
    public boolean shouldSkip(Throwable throwable, int skipCount) throws SkipLimitExceededException {
        StepExecution stepExecution = StepSynchronizationManager.getContext().getStepExecution();
        log.error("执行步骤id：{},执行步骤名称：{},自定义异常跳过策略,跳过次数{}", stepExecution.getId(), stepExecution.getStepName(), skipCount, throwable);
        return throwable instanceof Exception && skipCount < MAX_SKIP_COUNT;
    }

}
