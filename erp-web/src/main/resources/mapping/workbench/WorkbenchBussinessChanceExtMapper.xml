<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.workbench.dao.WorkbenchBussinessChanceExtMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.workbench.model.dto.WorkbenchBussinessChanceExtDto">
        <id column="BUSSINESS_CHANCE_ID" jdbcType="INTEGER" property="bussinessChanceId"/>
        <result column="WEB_BUSSINESS_CHANCE_ID" jdbcType="INTEGER" property="webBussinessChanceId"/>
        <result column="BUSSINESS_CHANCE_NO" jdbcType="VARCHAR" property="bussinessChanceNo"/>
        <result column="WEB_ACCOUNT_ID" jdbcType="INTEGER" property="webAccountId"/>
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId"/>
        <result column="ORG_ID" jdbcType="INTEGER" property="orgId"/>
        <result column="USER_ID" jdbcType="INTEGER" property="userId"/>
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId"/>
        <result column="CHECK_TRADER_NAME" jdbcType="VARCHAR" property="checkTraderName"/>
        <result column="CHECK_TRADER_AREA" jdbcType="VARCHAR" property="checkTraderArea"/>
        <result column="CHECK_TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="checkTraderContactName"/>
        <result column="CHECK_TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="checkTraderContactMobile"/>
        <result column="CHECK_TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="checkTraderContactTelephone"/>
        <result column="TYPE" jdbcType="INTEGER" property="type"/>
        <result column="RECEIVE_TIME" jdbcType="BIGINT" property="receiveTime"/>
        <result column="SOURCE" jdbcType="INTEGER" property="source"/>
        <result column="COMMUNICATION" jdbcType="INTEGER" property="communication"/>
        <result column="CONTENT" jdbcType="VARCHAR" property="content"/>
        <result column="GOODS_CATEGORY" jdbcType="INTEGER" property="goodsCategory"/>
        <result column="GOODS_BRAND" jdbcType="VARCHAR" property="goodsBrand"/>
        <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName"/>
        <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName"/>
        <result column="AREA_ID" jdbcType="INTEGER" property="areaId"/>
        <result column="AREA_IDS" jdbcType="VARCHAR" property="areaIds"/>
        <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId"/>
        <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName"/>
        <result column="MOBILE" jdbcType="VARCHAR" property="mobile"/>
        <result column="TELEPHONE" jdbcType="VARCHAR" property="telephone"/>
        <result column="OTHER_CONTACT" jdbcType="VARCHAR" property="otherContact"/>
        <result column="COMMENTS" jdbcType="VARCHAR" property="comments"/>
        <result column="ASSIGN_TIME" jdbcType="BIGINT" property="assignTime"/>
        <result column="FIRST_VIEW_TIME" jdbcType="BIGINT" property="firstViewTime"/>
        <result column="STATUS" jdbcType="TINYINT" property="status"/>
        <result column="STATUS_COMMENTS" jdbcType="INTEGER" property="statusComments"/>
        <result column="CLOSED_COMMENTS" jdbcType="VARCHAR" property="closedComments"/>
        <result column="WENXIN_OPEN_ID" jdbcType="VARCHAR" property="wenxinOpenId"/>
        <result column="BUSSINESS_LEVEL" jdbcType="INTEGER" property="bussinessLevel"/>
        <result column="BUSSINESS_STAGE" jdbcType="INTEGER" property="bussinessStage"/>
        <result column="ENQUIRY_TYPE" jdbcType="INTEGER" property="enquiryType"/>
        <result column="ORDER_RATE" jdbcType="INTEGER" property="orderRate"/>
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount"/>
        <result column="ORDER_TIME" jdbcType="BIGINT" property="orderTime"/>
        <result column="CANCEL_REASON" jdbcType="INTEGER" property="cancelReason"/>
        <result column="OTHER_REASON" jdbcType="VARCHAR" property="otherReason"/>
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="ENTRANCES" jdbcType="INTEGER" property="entrances"/>
        <result column="FUNCTIONS" jdbcType="INTEGER" property="functions"/>
        <result column="IS_NEW" jdbcType="BOOLEAN" property="isNew"/>
        <result column="PRODUCT_COMMENTS" jdbcType="VARCHAR" property="productComments"/>
        <result column="MERGE_STATUS" jdbcType="TINYINT" property="mergeStatus"/>
        <result column="OLD_CHANCE_NO" jdbcType="VARCHAR" property="oldChanceNo"/>
        <result column="BUSSINESS_PARENT_ID" jdbcType="INTEGER" property="bussinessParentId"/>
        <result column="PRODUCT_COMMENTS_SALE" jdbcType="VARCHAR" property="productCommentsSale"/>
        <result column="CLOSE_CHECK_STATUS" jdbcType="BOOLEAN" property="closeCheckStatus"/>
        <result column="BNC_LINK" jdbcType="VARCHAR" property="bncLink"/>
        <result column="IS_LINK_BD" jdbcType="BOOLEAN" property="isLinkBd"/>
        <result column="CONTACT_NUM" jdbcType="VARCHAR" property="contactNum"/>
		<result column="BUSSINESS_AMOUNT" jdbcType="DECIMAL" property="bussinessAmount"/>
		<result column="DISPLAY_TRADER_NAME" jdbcType="VARCHAR" property="displayTraderName"/>
    </resultMap>
    <sql id="Base_Column_List">
    BUSSINESS_CHANCE_ID, WEB_BUSSINESS_CHANCE_ID, BUSSINESS_CHANCE_NO, WEB_ACCOUNT_ID,
    COMPANY_ID, ORG_ID, USER_ID, TRADER_ID, CHECK_TRADER_NAME, CHECK_TRADER_AREA, CHECK_TRADER_CONTACT_NAME,
    CHECK_TRADER_CONTACT_MOBILE, CHECK_TRADER_CONTACT_TELEPHONE, `TYPE`, RECEIVE_TIME,
    `SOURCE`, COMMUNICATION, CONTENT, GOODS_CATEGORY, GOODS_BRAND, GOODS_NAME, TRADER_NAME,
    AREA_ID, AREA_IDS, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, MOBILE, TELEPHONE, OTHER_CONTACT,
    COMMENTS, ASSIGN_TIME, FIRST_VIEW_TIME, `STATUS`, STATUS_COMMENTS, CLOSED_COMMENTS,
    WENXIN_OPEN_ID, BUSSINESS_LEVEL, BUSSINESS_STAGE, ENQUIRY_TYPE, ORDER_RATE, AMOUNT,
    ORDER_TIME, CANCEL_REASON, OTHER_REASON, ADD_TIME, CREATOR, MOD_TIME, UPDATER, ENTRANCES,
    FUNCTIONS, IS_NEW, PRODUCT_COMMENTS, MERGE_STATUS, OLD_CHANCE_NO, BUSSINESS_PARENT_ID,
    PRODUCT_COMMENTS_SALE, CLOSE_CHECK_STATUS, BNC_LINK, IS_LINK_BD
  </sql>


    <resultMap id="getTodayToCommunicateBussinessChanceResult"
               type="com.vedeng.workbench.model.dto.WorkbenchBussinessChanceExtDto" extends="BaseResultMap">
        <collection property="bussinessCommunicateRecordDtoList"
                    ofType="com.vedeng.workbench.model.dto.BussinessCommunicateRecordDto"
                    select="getCommunicateRecordByBussinessChanceId" column="BUSSINESS_CHANCE_ID">
            <id column="COMMUNICATE_RECORD_ID" jdbcType="INTEGER" property="communicateRecordId"/>
            <result column="ENDTIME" jdbcType="BIGINT" property="endTime"/>
            <result column="NEXT_CONTACT_CONTENT" jdbcType="VARCHAR" property="nextContactContent"/>
            <result column="NEXT_CONTACT_DATE" jdbcType="DATE" property="nextContactDate"/>
        </collection>
    </resultMap>

    <!--获得每条待沟通商机的沟通记录-->
    <select id="getCommunicateRecordByBussinessChanceId"
            resultType="com.vedeng.workbench.model.dto.BussinessCommunicateRecordDto">
		SELECT
		COMMUNICATE_RECORD_ID,ENDTIME,NEXT_CONTACT_CONTENT
		FROM
		T_COMMUNICATE_RECORD
		WHERE
		COMMUNICATE_TYPE =244
		AND
		RELATED_ID=#{bussinessChanceId}

	</select>


    <!--查询今日待沟通商机数-->
    <select id="getTodayToCommunicateBussinessChanceById" resultMap="getTodayToCommunicateBussinessChanceResult">
		SELECT
			a.BUSSINESS_CHANCE_ID,a.BUSSINESS_CHANCE_NO,a.USER_ID,a.GOODS_NAME,a.CONTENT,a.Type,IFNULL(a.AMOUNT,0) AS AMOUNT,
			cr.NEXT_CONTACT_CONTENT,a.`STATUS`,a.TRADER_CONTACT_NAME,a.CHECK_TRADER_CONTACT_MOBILE,a.MOBILE,a.TELEPHONE,a.TRADER_ID,
			COALESCE(b.TRADER_NAME,a.CHECK_TRADER_NAME,a.TRADER_NAME) AS DISPLAY_TRADER_NAME,
			IFNULL(COALESCE(c.TOTAL_AMOUNT,a.AMOUNT),0) AS BUSSINESS_AMOUNT,
			COALESCE(a.CHECK_TRADER_CONTACT_MOBILE,a.MOBILE,a.TELEPHONE) AS CONTACT_NUM
		FROM
			T_BUSSINESS_CHANCE a
		LEFT JOIN
			T_TRADER b
			on a.TRADER_ID = b.TRADER_ID
		LEFT JOIN
			T_QUOTEORDER c
			on	a.BUSSINESS_CHANCE_ID=c.BUSSINESS_CHANCE_ID
		LEFT JOIN
			T_COMMUNICATE_RECORD cr
			ON a.BUSSINESS_CHANCE_ID = cr.RELATED_ID
			AND cr.COMMUNICATE_TYPE = 244
		WHERE
			a.COMPANY_ID = 1
		AND
		  	a.MERGE_STATUS IN (0,2)
		AND
			a.`STATUS` IN (0,6,1,2,3)
		AND
			a.USER_ID=#{userId}
		AND
			cr.NEXT_CONTACT_DATE = #{nextContactDate,jdbcType=DATE}
		GROUP BY
			a.BUSSINESS_CHANCE_ID
	</select>

    <!--获取预计本周成单商机-->
    <select id="getThisWeekExpectOrderChanceListById"
            parameterType="com.vedeng.workbench.model.dto.WorkbenchDataQueryDto"
            resultMap="BaseResultMap">
		SELECT
			a.BUSSINESS_CHANCE_ID,a.BUSSINESS_CHANCE_NO,a.USER_ID,a.GOODS_NAME,a.CONTENT,a.TRADER_ID,
			a.`STATUS`,IFNULL(a.AMOUNT,0) AS AMOUNT,a.ORDER_TIME,
			COALESCE(b.TRADER_NAME,a.CHECK_TRADER_NAME,a.TRADER_NAME) AS DISPLAY_TRADER_NAME
		FROM
			T_BUSSINESS_CHANCE a
		LEFT JOIN
			T_TRADER b
		on a.TRADER_ID = b.TRADER_ID
		WHERE
		 	a.COMPANY_ID = 1
		AND
		  	a.MERGE_STATUS IN (0,2)
		AND
			a.ORDER_TIME <![CDATA[>=]]> #{startTime}
		AND
			a.ORDER_TIME <![CDATA[<=]]> #{endTime}
		AND
			a.`STATUS` IN (0,6,1,2,3)
		AND
			a.USER_ID=#{userId}
		GROUP BY
			a.BUSSINESS_CHANCE_ID
    </select>

    <!--获得销售所有商机-->
    <select id="getAllBussinessChanceList"
			parameterType="com.vedeng.workbench.model.dto.WorkbenchDataQueryDto"
            resultMap="BaseResultMap">
		SELECT
			a.BUSSINESS_CHANCE_ID,a.BUSSINESS_CHANCE_NO,a.USER_ID,a.GOODS_NAME,a.CONTENT,IFNULL(a.AMOUNT,0),
			a.TRADER_CONTACT_NAME,a.BUSSINESS_LEVEL,a.CHECK_TRADER_CONTACT_MOBILE,a.MOBILE,a.TELEPHONE,a.TRADER_ID,
			COALESCE(b.TRADER_NAME,a.CHECK_TRADER_NAME,a.TRADER_NAME) AS DISPLAY_TRADER_NAME,
			IFNULL(COALESCE(b.TOTAL_AMOUNT,a.AMOUNT),0) AS BUSSINESS_AMOUNT,
			COALESCE(a.CHECK_TRADER_CONTACT_MOBILE,a.MOBILE,a.TELEPHONE) AS CONTACT_NUM
		FROM
			T_BUSSINESS_CHANCE a
		LEFT JOIN
			T_QUOTEORDER b
		on
			a.BUSSINESS_CHANCE_ID=b.BUSSINESS_CHANCE_ID
		WHERE
			a.COMPANY_ID = 1
		AND
		  	a.MERGE_STATUS IN (0,2)
		AND
			a.`STATUS` IN (0,1,2,3,4,6,7)
		AND
			a.ADD_TIME <![CDATA[>=]]> #{startTime}
		AND
			a.ADD_TIME <![CDATA[<=]]> #{endTime}
		AND
			a.USER_ID=#{userId}
		GROUP BY
			a.BUSSINESS_CHANCE_ID
	</select>

    <!--查询今日新增商机-->
    <select id="getTodayNewAddBussinessChnaceListById"
			parameterType="com.vedeng.workbench.model.dto.WorkbenchDataQueryDto"
            resultMap="BaseResultMap">
		SELECT
			a.BUSSINESS_CHANCE_ID,a.BUSSINESS_CHANCE_NO,a.USER_ID,a.TYPE,a.SOURCE,a.GOODS_NAME,a.CONTENT,
			a.TRADER_CONTACT_NAME,a.ADD_TIME,a.CHECK_TRADER_CONTACT_MOBILE,a.MOBILE,a.TELEPHONE,IFNULL(a.AMOUNT,0) AS AMOUNT,
			a.`STATUS`,a.TRADER_ID,
			COALESCE(a.CHECK_TRADER_CONTACT_MOBILE,a.MOBILE,a.TELEPHONE) AS CONTACT_NUM
		FROM
			T_BUSSINESS_CHANCE a
		WHERE
				a.COMPANY_ID = 1
			AND
				a.MERGE_STATUS IN (0,2)
			AND
				a.`STATUS` IN (0,1,2,3,4,6,7)
			AND
				a.ADD_TIME <![CDATA[>=]]> #{startTime}
			AND
				a.ADD_TIME <![CDATA[<=]]> #{endTime}
			AND
				a.USER_ID=#{userId}
	</select>

    <select id="getExpectOrderBussinessChanceListByIds"
			parameterType="com.vedeng.workbench.model.dto.WorkbenchDataQueryDto"
            resultMap="BaseResultMap">
		SELECT
			a.BUSSINESS_CHANCE_ID,a.BUSSINESS_CHANCE_NO,
			a.`STATUS`,a.USER_ID,IFNULL(a.AMOUNT,0) AS AMOUNT,a.ORDER_TIME,a.TRADER_ID,
			IFNULL(COALESCE(b.TOTAL_AMOUNT,a.AMOUNT),0) AS BUSSINESS_AMOUNT
		FROM
			T_BUSSINESS_CHANCE a
		LEFT JOIN
			T_QUOTEORDER b
		on a.BUSSINESS_CHANCE_ID = b.BUSSINESS_CHANCE_ID
		WHERE
			a.COMPANY_ID = 1
		AND
			a.MERGE_STATUS IN (0,2)
		AND
			a.`STATUS` IN (0,1,2,3,4,6,7)
		AND
			a.ORDER_TIME <![CDATA[>=]]> #{startTime}
		AND
			a.ORDER_TIME <![CDATA[<=]]> #{endTime}
		AND
			a.USER_ID in
		<foreach collection="userIds" item="userId" open="(" close=")" index="index" separator=",">
			#{userId,jdbcType=INTEGER}
		</foreach>
		GROUP BY
			a.BUSSINESS_CHANCE_ID
</select>

    <select id="getThisWeekNewAddImportChanceListByIds"
			parameterType="com.vedeng.workbench.model.dto.WorkbenchDataQueryDto"
			resultMap="BaseResultMap">
		SELECT
			a.BUSSINESS_CHANCE_ID,a.BUSSINESS_CHANCE_NO,a.BUSSINESS_LEVEL,a.`STATUS`,a.USER_ID,IFNULL(a.AMOUNT,0) AS AMOUNT,a.TRADER_ID,
			IFNULL(COALESCE(b.TOTAL_AMOUNT,a.AMOUNT),0) AS BUSSINESS_AMOUNT
		FROM
			T_BUSSINESS_CHANCE a
		LEFT JOIN
			T_QUOTEORDER b
		on a.BUSSINESS_CHANCE_ID = b.BUSSINESS_CHANCE_ID
		WHERE
			a.COMPANY_ID = 1
		AND
			a.MERGE_STATUS IN (0,2)
		AND
			a.`STATUS` IN (0,1,2,3,4,6,7)
        AND
            a.ADD_TIME <![CDATA[>=]]> #{startTime}
        AND
            a.ADD_TIME <![CDATA[<=]]> #{endTime}
		AND
			IFNULL(COALESCE(b.TOTAL_AMOUNT,a.AMOUNT),0)  <![CDATA[>=]]> 200000
		AND
			a.USER_ID in
			<foreach collection="userIds" item="userId" open="(" close=")" index="index" separator=",">
				#{userId,jdbcType=INTEGER}
			</foreach>
		GROUP BY
			a.BUSSINESS_CHANCE_ID

	</select>

    <select id="getTwoDayUncontactChanceListByIds"
			parameterType="com.vedeng.workbench.model.dto.WorkbenchDataQueryDto"
			resultMap="getTodayToCommunicateBussinessChanceResult">
 		SELECT
			a.BUSSINESS_CHANCE_NO,a.BUSSINESS_CHANCE_ID,a.`STATUS`,a.BUSSINESS_LEVEL,a.USER_ID,IFNULL(a.AMOUNT,0) AS AMOUNT,a.TRADER_ID,
			IFNULL(COALESCE(b.TOTAL_AMOUNT,a.AMOUNT),0) AS BUSSINESS_AMOUNT,a.ADD_TIME
		FROM
			T_BUSSINESS_CHANCE a
		LEFT JOIN
			T_QUOTEORDER b
		on a.BUSSINESS_CHANCE_ID = b.BUSSINESS_CHANCE_ID
		LEFT JOIN
			T_COMMUNICATE_RECORD cr
			ON a.BUSSINESS_CHANCE_ID = cr.RELATED_ID
			AND cr.COMMUNICATE_TYPE = 244
		WHERE
			a.COMPANY_ID = 1
		AND
			a.MERGE_STATUS IN (0,2)
		AND
			a.`STATUS` IN (0)
		AND
			a.USER_ID in
		<foreach collection="userIds" item="userId" open="(" close=")" index="index" separator=",">
			#{userId,jdbcType=INTEGER}
		</foreach>
		AND
			((a.ADD_TIME <![CDATA[>=]]> #{startTime}
				AND
				a.ADD_TIME <![CDATA[<=]]> #{endTime})
			or cr.NEXT_CONTACT_DATE = #{twoDaysBeforeDate,jdbcType=DATE})
		GROUP BY
			a.BUSSINESS_CHANCE_ID
 	</select>

	<select id="getThisWeekCoreFailChanceListByIds"
			parameterType="com.vedeng.workbench.model.dto.WorkbenchDataQueryDto"
			resultMap="BaseResultMap">
		SELECT
			a.BUSSINESS_CHANCE_ID,a.BUSSINESS_CHANCE_NO,
			IFNULL(COALESCE(q.TOTAL_AMOUNT,a.AMOUNT),0) AS BUSSINESS_AMOUNT,a.USER_ID,IFNULL(a.AMOUNT,0) AS AMOUNT,
			a.MOD_TIME,a.BUSSINESS_LEVEL,a.`STATUS`,a.TRADER_ID
		FROM
			T_BUSSINESS_CHANCE a
		LEFT JOIN
			T_QUOTEORDER q
		    ON a.BUSSINESS_CHANCE_ID = q.BUSSINESS_CHANCE_ID
        LEFT JOIN
            T_QUOTEORDER_GOODS qg
            ON qg.QUOTEORDER_ID = q.QUOTEORDER_ID
        LEFT JOIN
            V_CORE_SKU sku
            ON qg.GOODS_ID = sku.SKU_ID
        LEFT JOIN
            V_CORE_SPU spu
            ON spu.SPU_ID = sku.SPU_ID
		WHERE
			a.COMPANY_ID = 1
		AND
			a.MERGE_STATUS IN (0,2)
		AND
			a.`STATUS` = 4
		AND
			a.MOD_TIME <![CDATA[>=]]> #{startTime}
		AND
			a.MOD_TIME <![CDATA[<=]]> #{endTime}
		AND
			a.USER_ID in
		<foreach collection="userIds" item="userId" open="(" close=")" index="index" separator=",">
			#{userId,jdbcType=INTEGER}
		</foreach>
		AND
		    spu.SPU_LEVEL = 1
		GROUP BY
			a.BUSSINESS_CHANCE_ID

	</select>

	<select id="getThisWeekExpectFailChanceListByIds"
			parameterType="com.vedeng.workbench.model.dto.WorkbenchDataQueryDto"
			resultMap="BaseResultMap">

		SELECT
			a.BUSSINESS_CHANCE_ID,a.BUSSINESS_CHANCE_NO,a.BUSSINESS_LEVEL,a.`STATUS`,a.ORDER_TIME,a.USER_ID,a.TRADER_ID,
			IFNULL(COALESCE(b.TOTAL_AMOUNT,a.AMOUNT),0) AS BUSSINESS_AMOUNT,IFNULL(a.AMOUNT,0) AS AMOUNT
		FROM
			T_BUSSINESS_CHANCE a
		LEFT JOIN
			T_QUOTEORDER b
		on a.BUSSINESS_CHANCE_ID = b.BUSSINESS_CHANCE_ID
		WHERE
			a.COMPANY_ID = 1
		AND
			a.MERGE_STATUS IN (0,2)
		AND
			a.`STATUS` IN (0,1,2,3,4,6)
		AND
			a.ORDER_TIME <![CDATA[>=]]> #{startTime}
		AND
			a.ORDER_TIME <![CDATA[<=]]> #{endTime}
		AND
			a.USER_ID in
		<foreach collection="userIds" item="userId" open="(" close=")" index="index" separator=",">
			#{userId,jdbcType=INTEGER}
		</foreach>
		GROUP BY
			a.BUSSINESS_CHANCE_ID

	</select>
	<select id="getPlanCommunicateChanceListByIds"
			parameterType="com.vedeng.workbench.model.dto.WorkbenchDataQueryDto"
			resultMap="getTodayToCommunicateBussinessChanceResult">
		SELECT
			a.BUSSINESS_CHANCE_ID,a.BUSSINESS_CHANCE_NO,a.STATUS,a.USER_ID,IFNULL(a.AMOUNT,0) AS AMOUNT,a.TRADER_ID
		FROM
			T_BUSSINESS_CHANCE a
		LEFT JOIN
			T_COMMUNICATE_RECORD cr
			ON a.BUSSINESS_CHANCE_ID = cr.RELATED_ID
			AND cr.COMMUNICATE_TYPE = 244
	  	WHERE
			a.COMPANY_ID = 1
		AND
			a.MERGE_STATUS IN (0,2)
	  	AND
	  		cr.NEXT_CONTACT_DATE = #{nextContactDate,jdbcType=DATE}
	  	AND
			a.USER_ID in
		<foreach collection="userIds" item="userId" open="(" close=")" index="index" separator=",">
			#{userId,jdbcType=INTEGER}
		</foreach>
		GROUP BY
			a.BUSSINESS_CHANCE_ID

	</select>

    <select id="getYesterdayContactChanceListByIds"
            parameterType="com.vedeng.workbench.model.dto.WorkbenchDataQueryDto"
            resultMap="getTodayToCommunicateBussinessChanceResult">
        SELECT
			a.BUSSINESS_CHANCE_ID,a.BUSSINESS_CHANCE_NO,a.STATUS,a.USER_ID,IFNULL(a.AMOUNT,0) AS AMOUNT,a.TRADER_ID
		FROM
			T_BUSSINESS_CHANCE a
		LEFT JOIN
			T_COMMUNICATE_RECORD cr
			ON a.BUSSINESS_CHANCE_ID = cr.RELATED_ID
			AND cr.COMMUNICATE_TYPE = 244
	  	WHERE
			a.COMPANY_ID = 1
		AND
			a.MERGE_STATUS IN (0,2)
	  	AND
			cr.NEXT_CONTACT_DATE != #{nextContactDate,jdbcType=DATE}
	  	AND
			a.USER_ID IN
		<foreach collection="userIds" item="userId" open="(" close=")" index="index" separator=",">
			#{userId,jdbcType=INTEGER}
		</foreach>
		GROUP BY
			a.BUSSINESS_CHANCE_ID

    </select>

	<select id="getThisWeekNewAddChanceListByIds"
			parameterType="com.vedeng.workbench.model.dto.WorkbenchDataQueryDto"
			resultMap="BaseResultMap">
		SELECT
            a.BUSSINESS_CHANCE_ID,a.BUSSINESS_CHANCE_NO,a.BUSSINESS_LEVEL,a.`STATUS`,a.USER_ID,a.TRADER_ID,
            IFNULL(COALESCE(b.TOTAL_AMOUNT,a.AMOUNT),0) AS BUSSINESS_AMOUNT,IFNULL(a.AMOUNT,0) AS AMOUNT
		FROM
		  T_BUSSINESS_CHANCE a
		LEFT JOIN
		  T_QUOTEORDER b
		  on a.BUSSINESS_CHANCE_ID = b.BUSSINESS_CHANCE_ID
		WHERE
		    a.COMPANY_ID = 1
		AND
		    a.MERGE_STATUS IN (0,2)
		AND
		    a.`STATUS` IN (0,1,2,3,4,6,7)
		AND
		    a.ADD_TIME <![CDATA[>=]]> #{startTime}
		AND
		    a.ADD_TIME <![CDATA[<=]]> #{endTime}
		AND
		    a.USER_ID in
		<foreach collection="userIds" item="userId" open="(" close=")" index="index" separator=",">
			#{userId,jdbcType=INTEGER}
		</foreach>
		GROUP BY
			a.BUSSINESS_CHANCE_ID

	</select>

</mapper>