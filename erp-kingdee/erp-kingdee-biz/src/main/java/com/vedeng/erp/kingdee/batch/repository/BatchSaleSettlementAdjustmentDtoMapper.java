package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchSaleSettlementAdjustmentDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BatchSaleSettlementAdjustmentDtoMapper {
    int deleteByPrimaryKey(Integer saleSettlementAdjustmentId);

    int insert(BatchSaleSettlementAdjustmentDto record);

    int insertOrUpdate(BatchSaleSettlementAdjustmentDto record);

    int insertOrUpdateSelective(BatchSaleSettlementAdjustmentDto record);

    int insertSelective(BatchSaleSettlementAdjustmentDto record);

    BatchSaleSettlementAdjustmentDto selectByPrimaryKey(Integer saleSettlementAdjustmentId);

    int updateByPrimaryKeySelective(BatchSaleSettlementAdjustmentDto record);

    int updateByPrimaryKey(BatchSaleSettlementAdjustmentDto record);

    int updateBatch(List<BatchSaleSettlementAdjustmentDto> list);

    int updateBatchSelective(List<BatchSaleSettlementAdjustmentDto> list);

    int batchInsert(@Param("list") List<BatchSaleSettlementAdjustmentDto> list);

    List<BatchSaleSettlementAdjustmentDto> findByAll(BatchSaleSettlementAdjustmentDto batchSaleSettlementAdjustmentDto);

    /**
     * 根据销售单id查找最近产生的售后调整单
     * @param saleorderId
     * @return
     */
    BatchSaleSettlementAdjustmentDto selectBySaleorderIdLatest(Integer saleorderId);

    /**
     * 按条件查找调整单
     * @param batchSaleSettlementAdjustmentDto
     * @return
     */
    List<BatchSaleSettlementAdjustmentDto> findByItem(BatchSaleSettlementAdjustmentDto batchSaleSettlementAdjustmentDto);

    /**
     * 根据售后单查询调整单
     *
     * @param afterSaleId
     * @return
     */
    List<BatchSaleSettlementAdjustmentDto> findByAfterSaleId(@Param("afterSaleId")Integer afterSaleId);

    List<BatchSaleSettlementAdjustmentDto> findBySaleorderId(@Param("saleorderId")Integer saleorderId);




}