package com.vedeng.erp.trader.mapper;
import com.vedeng.erp.trader.dto.DwhCustomerTagCpmAddDfDto;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Date;

import com.vedeng.erp.trader.domain.entity.DwhCustomerTagCpmAddDfEntity;


/**
 * @description ${end}
 * <AUTHOR>
 * @date 2024/4/1 8:50
 **/
public interface DwhCustomerTagCpmAddDfMapper {
    int insert(DwhCustomerTagCpmAddDfEntity record);

    int insertSelective(DwhCustomerTagCpmAddDfEntity record);

    List<DwhCustomerTagCpmAddDfDto> findByAll(DwhCustomerTagCpmAddDfDto dwhCustomerTagCpmAddDfEntity);


}