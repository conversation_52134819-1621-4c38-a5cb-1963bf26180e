package com.vedeng.goods.utils;

import com.google.common.collect.Range;
import com.vedeng.common.constant.PushPlatformEnum;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.goods.command.EffectDayResult;
import com.vedeng.goods.enums.GoodsTypeEnum;

import java.util.*;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class GoodsUtils {

    /**
     * 耗材(spu的商品类别为以下时)
     */
    private static final List<Integer> CONSUMABLES_OPTION_TYPES =
            Collections.unmodifiableList(Arrays.asList(GoodsTypeEnum.CONSUMABLES.getId(), GoodsTypeEnum.REAGENT.getId(),
                    GoodsTypeEnum.CONSUMABLES_PLUS.getId())
            );


    /**
     * 根据商品类型决定sku的类型
     *
     * @param goodsTypeId
     * @return
     */
    public static Integer determineSkuTye(Integer goodsTypeId) {
        //根据SPU类型决定SKU类型
        return CONSUMABLES_OPTION_TYPES.contains(goodsTypeId) ? GoodsConstants.SKU_TYPE_CONSUMABLES
                : GoodsConstants.SKU_TYPE_INSTRUMENT;
    }


    /**
     * 根据数据字典的商品类型的Id判断sku是否为耗材
     *
     * @param goodsType
     * @return
     */
    public static boolean isConsumables(Integer goodsType) {
        return CONSUMABLES_OPTION_TYPES.contains(goodsType);
    }

    /**
     * 根据整数类型获取各平台的字符串集合
     * 1贝登 2医械购 4科研购 8非公集采 16区域商城 32 科研特麦帮
     * @param var 平台二进制集合的整数值
     * @param split 字符串分隔符
     * @param placeholder 空结果替换符
     * @return 平台字符串
     */
    public static String getPlatformStrByInteger(Integer var, String split, String placeholder){
        List<String> pushPlatformNameList = PushPlatformEnum.getAllName();
        List<String> platformStrList = new ArrayList<>();
        String binaryStr= new StringBuilder(Integer.toBinaryString(var)).reverse().toString();
        String[] binaryStrArray = binaryStr.split("");
        for (int i = 0; i < binaryStrArray.length; i++) {
            if (Integer.parseInt(binaryStrArray[i]) == 1){
                platformStrList.add(pushPlatformNameList.get(i));
            }
        }
        if (platformStrList.size() > 0){
            return String.join(split,platformStrList);
        } else {
            return placeholder;
        }
    }


    /**
     * 根据推送状态和上架状态，来获取已推送平台的上下架信息
     * @param pushStatus 推送状态
     * @param onSale 上架状态
     * @param split 字符串分隔符
     * @param placeholder 空结果替换字符串
     * @return 上下架信息
     */
    public static String getOnSaleStrByPushStatus(Integer pushStatus, Integer onSale, String split, String placeholder){
        String pushStatusStr = new StringBuilder(Integer.toBinaryString(pushStatus)).reverse().toString();
        String[] pushStatusArray = pushStatusStr.split("");
        String onSaleStr = new StringBuilder(Integer.toBinaryString(onSale)).reverse().toString();
        String[] onSaleArray = onSaleStr.split("");
        List<String> onSaleInfo = new ArrayList<>();
        for (int i = 0; i < pushStatusArray.length; i++) {
            if (Integer.parseInt(pushStatusArray[i]) == 1){
                if (onSaleArray.length > i && Integer.parseInt(onSaleArray[i]) == 1){
                    onSaleInfo.add("上架");
                } else {
                    onSaleInfo.add("下架");
                }
            }
        }
        if (onSaleInfo.size() > 0){
            return String.join(split,onSaleInfo);
        } else {
            return placeholder;
        }
    }

    /**
     * 根据平台获取该平台已推送的所有推送状态值
     * -1全部推送 1贝登 2医械购 4科研购 8集采
     * @param platform 平台值
     * @return 推送状态值集合
     */
    public static List<Integer> getAllPushStatusValueByPlatform(Integer platform){
        Integer sitCount = PushPlatformEnum.getCount();
        if (platform == -1){
            return Collections.singletonList(PushPlatformEnum.getValueOfPushedAllPlatform());
        }
        if (platform == 0) {
            return Collections.singletonList(0);
        }
        List<Integer> allPushStatus = new ArrayList<>();

        for (int i = platform; i < Math.pow(2,sitCount); i++) {
            if ((i & platform) > 0){
                allPushStatus.add(i);
            }
        }
        return allPushStatus;
    }


    private final static String GOODS_NO_PREFIX = "V";

    public static String createGoodsNo(Integer goodsId) {
        Objects.requireNonNull(goodsId, "goods id is null");
        return GOODS_NO_PREFIX + goodsId;
    }
    /**
     * 计算效期与超近效期
     * @param effectiveDays
     * @return
     */
    public static EffectDayResult generateNearTermWarnDays(int effectiveDays){
        Map<Range, EffectDayResult> map =  new HashMap<>();
        map.put(Range.atLeast(1825),new EffectDayResult(910,730 ));
        map.put(Range.closedOpen(1460,1825),new EffectDayResult(720,540));
        map.put(Range.closedOpen(1095,1460),new EffectDayResult(540,365));
        map.put(Range.closedOpen(730,1095),new EffectDayResult(360,245));
        map.put(Range.closedOpen(365,730),new EffectDayResult(270,180));
        map.put(Range.closedOpen(180,365),new EffectDayResult(120,60));
        map.put(Range.closedOpen(120,180),new EffectDayResult(60,40));
        map.put(Range.closedOpen(60,120),new EffectDayResult(40,20));

        map.put(Range.closedOpen(0,60),new EffectDayResult(20,10));
        return map.entrySet().stream().filter(e -> e.getKey().contains(effectiveDays))
                .map(e -> e.getValue())
                .findFirst()
                .orElse(null);
    }
}
