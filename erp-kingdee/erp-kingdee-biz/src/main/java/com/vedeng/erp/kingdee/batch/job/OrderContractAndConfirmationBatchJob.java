package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchBuyOrderContractDto;
import com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderContractDto;
import com.vedeng.erp.kingdee.batch.dto.BatchSaleorderContractDto;
import com.vedeng.erp.kingdee.batch.processor.*;
import com.vedeng.erp.kingdee.batch.writer.BuyOrderContractWriter;
import com.vedeng.erp.kingdee.batch.writer.CommonFileDataWriter;
import com.vedeng.erp.kingdee.batch.writer.SaleorderContractWriter;
import com.vedeng.erp.kingdee.dto.KingDeeBuyOrderContractDto;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleorderContractDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: ckt
 * @Date: 2023/11/11
 * @Description: 销售订单合同推送金蝶
 * 销售订单确认单推送金蝶
 * 采购订单合同推送金蝶
 * 流转单合同推送金蝶
 */

@Slf4j
@Configuration
@SuppressWarnings("all")
public class OrderContractAndConfirmationBatchJob extends BaseJob {
    @Autowired
    private CommonFileDataWriter commonFileDataWriter;
    @Autowired
    private SaleorderContractProcessor saleorderContractProcessor;
    @Autowired
    private BuyOrderContractProcessor buyOrderContractProcessor;
    @Autowired
    private SaleorderContractWriter saleorderContractWriter;
    @Autowired
    private BuyOrderContractWriter buyOrderContractWriter;
    @Autowired
    private SaleorderContractFileProcessor saleorderContractFileProcessor;
    @Autowired
    private BuyOrderContractFileProcessor buyOrderContractFileProcessor;
    @Autowired
    private FlowOrderContractFileProcessor flowOrderContractFileProcessor;
    @Autowired
    private ConfirmationFileProcessor confirmationFileProcessor;
    @Value("${oss_http}")
    private String ossHttp;

    public Job orderContractAndConfirmationBatchJob() {
        return jobBuilderFactory.get("orderContractAndConfirmationBatchJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(saleorderContract())
                .next(saleorderContractFile())
                .next(buyorderContract())
                .next(buyorderContractFile())
                .next(flowOrderBuyContractFile())
                .next(flowOrderSaleContractFile())
                .next(confirmation())
                .next(confirmationFile())
                .next(onlineConfirmation())
                .next(onlineConfirmationFile())
                .build();
    }

    private Step saleorderContract() {
        return stepBuilderFactory.get("销售订单合同")
                .<BatchSaleorderContractDto, KingDeeSaleorderContractDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleorderContractReader(null, null))
                .processor(saleorderContractProcessor)
                .writer(saleorderContractWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step buyorderContract() {
        return stepBuilderFactory.get("采购订单合同")
                .<BatchBuyOrderContractDto, KingDeeBuyOrderContractDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(buyOrderContractReader(null, null))
                .processor(buyOrderContractProcessor)
                .writer(buyOrderContractWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    private Step confirmation() {
        return stepBuilderFactory.get("确认单")
                .<BatchSaleorderContractDto, KingDeeSaleorderContractDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(confirmationReader(null, null))
                .processor(saleorderContractProcessor)
                .writer(saleorderContractWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step onlineConfirmation() {
        return stepBuilderFactory.get("在线确认单")
                .<BatchSaleorderContractDto, KingDeeSaleorderContractDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(onlineConfirmationReader(null, null))
                .processor(saleorderContractProcessor)
                .writer(saleorderContractWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    private Step saleorderContractFile() {
        return stepBuilderFactory.get("销售订单合同附件")
                .<BatchSaleorderContractDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleorderContractFileReader(null, null))
                .processor(saleorderContractFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step buyorderContractFile() {
        return stepBuilderFactory.get("采购订单合同附件")
                .<BatchBuyOrderContractDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(buyOrderContractFileReader(null, null))
                .processor(buyOrderContractFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step flowOrderBuyContractFile() {
        return stepBuilderFactory.get("采购流转单合同附件")
                .<BatchFlowOrderContractDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(flowOrderBuyContractFileReader(null, null))
                .processor(flowOrderContractFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step flowOrderSaleContractFile() {
        return stepBuilderFactory.get("销售流转单合同附件")
                .<BatchFlowOrderContractDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(flowOrderSaleContractFileReader(null, null))
                .processor(flowOrderContractFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step confirmationFile() {
        return stepBuilderFactory.get("确认单附件")
                .<BatchSaleorderContractDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(confirmationFileReader(null, null))
                .processor(confirmationFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step onlineConfirmationFile() {
        return stepBuilderFactory.get("在线确认单附件")
                .<BatchSaleorderContractDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(onlineConfirmationFileReader(null, null))
                .processor(confirmationFileProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 销售订单Reader
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchSaleorderContractDto> saleorderContractReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                      @Value("#{jobParameters['endTime']}") String endTime) {
        BatchSaleorderContractDto batchSaleorderContractDto = BatchSaleorderContractDto
                .builder()
                .validTimeBegin(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime())
                .validTimeEnd(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime())
                .ossPre(ossHttp)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchSaleorderContractDto.class.getSimpleName(), "querySaleorderContract", batchSaleorderContractDto);
    }


    /**
     * 采购订单Reader
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBuyOrderContractDto> buyOrderContractReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                    @Value("#{jobParameters['endTime']}") String endTime) {
        BatchBuyOrderContractDto batchBuyOrderContractDto = BatchBuyOrderContractDto
                .builder()
                .validTimeBegin(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime())
                .validTimeEnd(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchBuyOrderContractDto.class.getSimpleName(), "queryBuyOrderContract", batchBuyOrderContractDto);
    }

    /**
     * 确认单Reader
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchSaleorderContractDto> confirmationReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                 @Value("#{jobParameters['endTime']}") String endTime) {
        BatchSaleorderContractDto batchSaleorderContractDto = BatchSaleorderContractDto
                .builder()
                .validTimeBegin(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime())
                .validTimeEnd(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchSaleorderContractDto.class.getSimpleName(), "queryConfirmation", batchSaleorderContractDto);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchSaleorderContractDto> onlineConfirmationReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                       @Value("#{jobParameters['endTime']}") String endTime) {
        BatchSaleorderContractDto batchSaleorderContractDto = BatchSaleorderContractDto
                .builder()
                .validTimeBegin(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime())
                .validTimeEnd(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchSaleorderContractDto.class.getSimpleName(), "onlineQueryConfirmation", batchSaleorderContractDto);
    }

    /**
     * 销售订单附件Reader
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchSaleorderContractDto> saleorderContractFileReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                          @Value("#{jobParameters['endTime']}") String endTime) {
        BatchSaleorderContractDto batchSaleorderContractDto = BatchSaleorderContractDto
                .builder()
                .validTimeBegin(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime())
                .validTimeEnd(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime())
                .ossPre(ossHttp)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchSaleorderContractDto.class.getSimpleName(), "querySaleorderContractFile", batchSaleorderContractDto);
    }

    /**
     * 采购订单附件Reader
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchBuyOrderContractDto> buyOrderContractFileReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                        @Value("#{jobParameters['endTime']}") String endTime) {
        BatchBuyOrderContractDto batchBuyOrderContractDto = BatchBuyOrderContractDto
                .builder()
                .validTimeBegin(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime())
                .validTimeEnd(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime())
                .ossPre(ossHttp)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchBuyOrderContractDto.class.getSimpleName(), "queryBuyOrderContractFile", batchBuyOrderContractDto);
    }

    /**
     * 确认单附件Reader
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchSaleorderContractDto> confirmationFileReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                     @Value("#{jobParameters['endTime']}") String endTime) {
        BatchSaleorderContractDto batchSaleorderContractDto = BatchSaleorderContractDto
                .builder()
                .validTimeBegin(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime())
                .validTimeEnd(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchSaleorderContractDto.class.getSimpleName(), "queryConfirmationFile", batchSaleorderContractDto);
    }


    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchSaleorderContractDto> onlineConfirmationFileReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                           @Value("#{jobParameters['endTime']}") String endTime) {
        BatchSaleorderContractDto batchSaleorderContractDto = BatchSaleorderContractDto
                .builder()
                .validTimeBegin(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime())
                .validTimeEnd(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchSaleorderContractDto.class.getSimpleName(), "queryOnlineConfirmationFile", batchSaleorderContractDto);
    }


    /**
     * 采购流转单合同附件Reader
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchFlowOrderContractDto> flowOrderBuyContractFileReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                             @Value("#{jobParameters['endTime']}") String endTime) {
        BatchFlowOrderContractDto batchFlowOrderContractDto = BatchFlowOrderContractDto
                .builder()
                .validTimeBegin(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime())
                .validTimeEnd(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime())
                .baseBusinessType(1) // 采购
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchFlowOrderContractDto.class.getSimpleName(), "queryFlowOrderBuyContractFile", batchFlowOrderContractDto);
    }

    /**
     * 销售流转单合同附件Reader
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchFlowOrderContractDto> flowOrderSaleContractFileReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                              @Value("#{jobParameters['endTime']}") String endTime) {
        BatchFlowOrderContractDto batchFlowOrderContractDto = BatchFlowOrderContractDto
                .builder()
                .validTimeBegin(beginTime == null ? null : DateUtil.parseDateTime(beginTime).getTime())
                .validTimeEnd(endTime == null ? null : DateUtil.parseDateTime(endTime).getTime())
                .baseBusinessType(2) // 销售
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchFlowOrderContractDto.class.getSimpleName(), "queryFlowOrderSaleContractFile", batchFlowOrderContractDto);
    }

}
