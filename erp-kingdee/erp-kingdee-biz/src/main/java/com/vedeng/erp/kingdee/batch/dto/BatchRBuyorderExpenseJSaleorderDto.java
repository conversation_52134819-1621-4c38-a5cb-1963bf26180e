package com.vedeng.erp.kingdee.batch.dto;

import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @description 销售单和采购费用单采购数量关系表（细粒度到商品）
 * <AUTHOR>
 * @date 2023/3/21 10:01
 **/
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class BatchRBuyorderExpenseJSaleorderDto {
    /**
     * 主键
     */
    private Integer tRBuyorderExpenseJSaleorderId;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 创建者id
     */
    private Integer creator;

    /**
     * 修改者id
     */
    private Integer updater;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 修改者名称
     */
    private String updaterName;

    /**
     * 销售单表id
     */
    private Integer saleorderId;

    /**
     * @组合对象@
     */
    private String saleorderNo;

    /**
     * 销售单明细表id
     */
    private Integer saleorderGoodsId;

    /**
     * 采购费用单id
     */
    private Integer buyorderExpenseId;

    /**
     * 采购费用单明细id
     */
    private Integer buyorderExpenseItemId;

    /**
     * skuId
     */
    private Integer skuId;

    /**
     * sku
     */
    private String skuNo;

    /**
     * 关联采购数量
     */
    private Integer num;


    /**
     * 关联采购数量@组合对象@
     */
    private BigDecimal preciseNum;
}