package com.vedeng.dwh.model.dto;

import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * 下级/用户用户.
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2020/11/3 8:59 上午.
 * @author: Tomcat.Hui.
 */
@Data
public class DwhErpSubDeptDto {

    public DwhErpSubDeptDto(){}

    public DwhErpSubDeptDto(Integer departId, String departName) {
        this.departId = departId;
        this.departName = departName;
    }

    public DwhErpSubDeptDto(Integer departId, String departName, List<DwhErpUserDto> subUsers) {
        this.departId = departId;
        this.departName = departName;
        this.subUsers = subUsers;
    }

    private Integer level;

    private Integer departId;

    private String departName;

//    /**
//     * 一级部门ID
//     */
//    private Integer l1Id;
//
//    /**
//     * 一级部门名称
//     */
//    private String l1Name;
//
//    /**
//     * 二级部门ID
//     */
//    private Integer l2Id;
//
//    /**
//     * 二级部门名称
//     */
//    private String l2Name;
//
//    /**
//     * 三级部门ID
//     */
//    private Integer l3Id;
//
//    /**
//     * 三级部门名称
//     */
//    private String l3Name;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DwhErpSubDeptDto that = (DwhErpSubDeptDto) o;
        return Objects.equals(departId, that.departId) &&
                Objects.equals(departName, that.departName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(departId, departName);
    }

    /**
     * 下属销售
     */
    private List<DwhErpUserDto> subUsers;

}
