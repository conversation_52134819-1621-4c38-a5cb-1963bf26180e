package com.vedeng.erp.saleorder.service.impl;

import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.base.api.dto.EsignReqDTO;
import com.vedeng.base.api.dto.EsignSignInfo;
import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.base.api.dto.reqParam.EAccount;
import com.vedeng.base.api.dto.reqParam.Org;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.saleorder.manager.esign.SaleOrderElectronicSignHandle;
import com.vedeng.erp.saleorder.service.ElectronicSignatureService;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.dao.ZxfOrderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.dao.TraderContactGenerateMapper;
import com.vedeng.trader.model.vo.TraderContactVo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/9 11:09
 */
@Service
public class ElectronicSignatureServiceImpl implements ElectronicSignatureService {

    private static Logger logger = LoggerFactory.getLogger(ElectronicSignatureServiceImpl.class);

    @Resource
    private SaleOrderElectronicSignHandle saleOrderElectronicSignHandle;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private TraderContactGenerateMapper traderContactGenerateMapper;

    @Resource
    private ZxfOrderMapper zxfOrderMapper;

    @Autowired
    @Qualifier("actionProcdefService")
    protected ActionProcdefService actionProcdefService;

    @Autowired
    @Qualifier("verifiesRecordService")
    protected VerifiesRecordService verifiesRecordService;

    @Autowired
    private UserMapper userMapper;

    @Override
    public void signature(User user, Integer saleorderId) {
        // 调用电子签章
        BusinessInfo businessInfo = new BusinessInfo();
        businessInfo.setOperator(user.getUsername());
//        ElectronicSignParam electronicSignParam = ElectronicSignParam
//                .builder()
//                .flowType(2)
//                .saleOrderId(saleorderId)
//                .businessInfo(businessInfo)
//                .electronicSignBusinessEnums(ElectronicSignBusinessEnums.SALE_ORDER)
//                .build();
//        saleOrderElectronicSignHandle.electronicSign(electronicSignParam);


        ElectronicSignParam electronicSignParam = saleOrderElectronicSignHandle.buildElectronicSignParam(saleorderId,businessInfo);
        saleOrderElectronicSignHandle.electronicSign(electronicSignParam);
    }

    @Override
    public void electronicSignature(HttpServletRequest request,Saleorder saleOrder, ModelAndView mv) {
        Saleorder saleOrderInfo = saleorderMapper.getSaleOrderById(saleOrder.getSaleorderId());
        if (null == saleOrderInfo){
            logger.info("ElectronicSignatureServiceImpl->electronicSignature,saleOrderInfo is null,saleOrderId:{}",saleOrder.getSaleorderId());
            throw new NullPointerException();
        }
        mv.addObject("saleOrder", saleOrderInfo);
        //订单号
        String orderNo = saleOrderInfo.getSaleorderNo();
        EsignReqDTO esignReqDTO = new EsignReqDTO();
        esignReqDTO.setBusinessName(ElectronicSignBusinessEnums.SALE_ORDER.getType().toString());
        esignReqDTO.setBusinessType(1);
        esignReqDTO.setOrderNo(orderNo);
        User user = this.getSessionUser(request);
        esignReqDTO.setOperator(user.getUsername());
        List<EsignSignInfo> list = saleOrderElectronicSignHandle.getSignUrls(esignReqDTO);
        if (CollectionUtils.isNotEmpty(list)){
            list = list.stream().filter(Objects::nonNull).sorted(Comparator.comparing(EsignSignInfo::getAddTime)).collect(Collectors.toList());
        }
        mv.addObject("electronicSignatureList", list);
    }

    @Override
    public void addElectronicSignature(Saleorder saleOrder, ModelAndView mv) {
        mv.addObject("phoneNumber",saleOrder.getTraderContactMobile());
        String frontDeskRealName = saleOrderElectronicSignHandle.getSignUsername(saleOrder.getTraderContactMobile());
        mv.addObject("realName", frontDeskRealName);
        if (ObjectUtils.isEmpty(frontDeskRealName)){
            List<TraderContactVo> list = traderContactGenerateMapper.getTraderContactList(saleOrder.getTraderId());
            if (CollectionUtils.isNotEmpty(list)){
                List<TraderContactVo> voList = list.stream().filter(Objects::nonNull).filter(s->saleOrder.getTraderContactMobile().equals(s.getMobile())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(voList)){
                    String name = voList.get(0).getName();
                    mv.addObject("realName", name);
                    if (ObjectUtils.isEmpty(voList.get(0).getIsDefault()) ){
                        Integer isDefault = 1;
                        List<TraderContactVo> contactVoList = voList.stream().filter(s->isDefault.equals(s.getIsDefault())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(contactVoList)){
                            mv.addObject("realName", contactVoList.get(0).getName());
                        }
                    }
                }
            }
        }
    }

    @Override
    public Integer saveElectronicSignature(HttpServletRequest request,Saleorder saleOrder) {
        if (ObjectUtils.isEmpty(saleOrder.getSaleorderId())){
            logger.info("ElectronicSignatureServiceImpl->saveElectronicSignature,fail,saleOrderId is null");
            throw new ServiceException("保存电子签章,销售订单ID为空");
        }
        //订单状态异常(订单状态为“已关闭”或“待确认”)，toast提示“订单状态异常，请返回查看订单状态”；
        Saleorder saleOrderInfo = zxfOrderMapper.getBaseSaleorderInfo(saleOrder.getSaleorderId());
        //此处增加JCO状态
        if (ObjectUtils.isEmpty(saleOrderInfo.getStatus()) || ErpConst.THREE.equals(saleOrderInfo.getStatus()) || ErpConst.FOUR.equals(saleOrderInfo.getStatus())){
            logger.info("ElectronicSignatureServiceImpl->saveElectronicSignature,fail,status :{},saleOrderId:{}",saleOrderInfo.getStatus(),saleOrder.getSaleorderId());
            return ErpConst.ONE;
        }
        //3)	当前订单的合同审核状态为“审核中”或“审核通过”，toast提示“该订单合同在审核中或已审核通过，无法生成链接”
        if (ErpConst.ZERO.equals(saleOrderInfo.getContractStatus()) || ErpConst.ONE.equals(saleOrderInfo.getContractStatus())){
            logger.info("ElectronicSignatureServiceImpl->saveElectronicSignature,fail,contractStatus :{},saleOrderId:{}",saleOrderInfo.getContractStatus(),saleOrder.getSaleorderId());
            return ErpConst.TWO;
        }
        //4)	订单状态/合同审核状态无异常，则获取e签宝签署链接；如果获取失败（例如账号创建失败、接口调用失败等），则toast提示“操作失败，请稍后重试”；
        // 调用电子签章
        User user = this.getSessionUser(request);



        BusinessInfo businessInfo = new BusinessInfo();
        String realName = this.getRealNameByUserName(user.getUsername());
        businessInfo.setOperator(realName);
        EAccount eAccount = new EAccount();
        eAccount.setPhoneNumber(request.getParameter("phoneNumber"));
        eAccount.setRealName(request.getParameter("realName"));
        Org org = new Org();
        org.setOrgId(saleOrderInfo.getTraderId());
        org.setOrgName(saleOrderInfo.getTraderName());
        ElectronicSignParam electronicSignParam = ElectronicSignParam
                .builder()
                .saleOrderId(saleOrder.getSaleorderId())
                .businessInfo(businessInfo)
                .flowType(1)
                .account(eAccount)
                .org(org)
                .electronicSignBusinessEnums(ElectronicSignBusinessEnums.SALE_ORDER)
                .build();
        saleOrderElectronicSignHandle.electronicSign(electronicSignParam);
        //5)	成功获取到e签宝链接后，更新到页面中，页面自动刷新。
        return ErpConst.ZERO;
    }

    private String getRealNameByUserName(String userName) {
        if (StringUtil.isBlank(userName)){
            return userName;
        }

        //管理员
        StringBuffer realName = new StringBuffer();
        if ("admin".equals(userName) || "njadmin".equals(userName)){
            return realName.append(userName).append(" (").append("管理员").append(")").toString();
        }
        //普通用户
        String realNameStr = userMapper.getRealNameByUserName(userName);
        if (StringUtil.isBlank(realNameStr)){
            return userName;
        }
        return realName.append(userName).append("(").append(realNameStr).append(")").toString();
    }

    /**
     * 是否线上订单
     * @param saleOrder 订单
     * @return 是否线上
     */
    private Integer isOnlineOrder(Saleorder saleOrder){
        //判断当前订单类型,线上1-1,5,7
        Boolean result = ObjectUtils.notEmpty(saleOrder.getOrderType()) && ( SysOptionConstant.ID_1.equals(saleOrder.getOrderType())
                || SysOptionConstant.ID_5.equals(saleOrder.getOrderType())
                || SysOptionConstant.ID_7.equals(saleOrder.getOrderType()));

        if ((Boolean.TRUE.equals(result))){
            return ErpConst.ONE;
        }else{
            return ErpConst.TWO;
        }
    }

    /**
     *
     * <b>Description: 获取session中用户信息</b><br>
     * @param request request
     * @return <b>Author: Franlin</b> <br>
     *         <b>Date: 2018年6月5日 上午11:49:13 </b>
     */
    private User getSessionUser(HttpServletRequest request) {
        return (User) request.getSession().getAttribute(ErpConst.CURR_USER);
    }
}
