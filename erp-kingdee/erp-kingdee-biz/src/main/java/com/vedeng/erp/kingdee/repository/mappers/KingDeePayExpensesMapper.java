package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeePayExpensesEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface KingDeePayExpensesMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeePayExpensesEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeePayExpensesEntity record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    KingDeePayExpensesEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeePayExpensesEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeePayExpensesEntity record);

    int updateBatchSelective(List<KingDeePayExpensesEntity> list);

    int batchInsert(@Param("list") List<KingDeePayExpensesEntity> list);

    /**
     * 根据invoiceid 查应付单
     * @param list
     * @return
     */
    List<KingDeePayExpensesEntity> findByFQzokBddjtIds(@Param("list") List<String> list);
}