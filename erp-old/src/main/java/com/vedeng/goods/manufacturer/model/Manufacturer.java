package com.vedeng.goods.manufacturer.model;

import com.vedeng.common.controller.BaseCommand;
import com.vedeng.system.model.Attachment;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class Manufacturer extends BaseCommand {

    /**
     * 企业id
     */
    private Integer manufacturerId;

    /**
     * 企业名称
     */
    private String manufacturerName;

    /**
     * 是否上传贝登公章 0没上传 1部分上传 2全部上传
     */
    private Integer isUpload;

    /**
     * 营业执照发证日期
     */
    private Date bcIssueDate;

    /**
     * 营业执照有效日期 开始时间
     */
    private Date bcStartTime;

    /**
     * 营业执照有效日期 结束时间
     */
    private Date bcEndTime;

    /**
     * 生产企业许可证 开始时间
     */
    private Date peStartTime;

    /**
     * 生产企业许可证 结束时间
     */
    private Date peEndTime;

    /**
     * 生产企业生产产品登记表 开始时间
     */
    private Date pepStartTime;

    /**
     * 生产企业生产产品登记表 结束时间
     */
    private Date pepEndTime;

    /**
     * 生产企业许可证编号
     */
    private String productCompanyLicence;

    /**
     * 生产企业备案凭证编号 开始时间
     */
    private Date rcStartTime;
    /**
     * 生产企业备案凭证编号 结束时间
     */
    private Date rcEndTime;

    /**
     * 生产企业备案凭证编号
     */
    private String recordCertificateLicence;

    /**
     * 对应供应商id
     */
    private Integer relateId;

    /**
     * 审核状态 5 待提交审核 1待审核 2审核不通过 3审核通过
     */
    private Integer status;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 审核不通过原因
     */
    private String reason;

    /**
     * 产品经理id
     */
    private Integer assignmentManagerId;

    /**
     * 产品助理id
     */
    private Integer assignmentAssistantId;

    /**
     * 归属产品经理
     */
    private String userName;

    private Integer attachmentFunction;

    /**
     * 营业执照（新）1307
     */
    private List<Attachment> yzAttachments;

    /**
     * 生产企业生产许可证（新）1306
     */
    private List<Attachment> scAttachments;

    /**
     * 注册登记表附件 1305
     */
    private List<Attachment> djbAttachments;

    /**
     * 生产备案证 1308
     */
    private List<Attachment> rcAttachments;

    /**
     * 营业执照（贝）1302
     */
    private List<Attachment> yzBAttachments;

    /**
     * 生产企业生产许可证（贝）1303
     */
    private List<Attachment> scBAttachments;

    /**
     * 生产企业生产产品登记表（贝）即注册登记表附件 1304
     */
    private List<Attachment> djbBAttachments;

    /**
     * 生产备案证  1308
     */
    private List<Attachment> rcBAttachments;


    /**
     * 是否有效 0有效 1无效
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 创建人id
     */
    private Integer addNo;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人id
     */
    private Integer updateNo;

    /**
     * 是否同时发起电子签章
     */
    private Integer signature;

    @Override
    public String toString() {
        return "Manufacturer{" +
                "manufacturerId=" + manufacturerId +
                ", manufacturerName='" + manufacturerName + '\'' +
                ", isUpload=" + isUpload +
                ", bcIssueDate=" + bcIssueDate +
                ", bcStartTime=" + bcStartTime +
                ", bcEndTime=" + bcEndTime +
                ", peStartTime=" + peStartTime +
                ", peEndTime=" + peEndTime +
                ", pepStartTime=" + pepStartTime +
                ", pepEndTime=" + pepEndTime +
                ", productCompanyLicence='" + productCompanyLicence + '\'' +
                ", rcStartTime=" + rcStartTime +
                ", rcEndTime=" + rcEndTime +
                ", recordCertificateLicence='" + recordCertificateLicence + '\'' +
                ", relateId=" + relateId +
                ", status=" + status +
                ", auditTime=" + auditTime +
                ", reason='" + reason + '\'' +
                ", assignmentManagerId=" + assignmentManagerId +
                ", assignmentAssistantId=" + assignmentAssistantId +
                ", userName='" + userName + '\'' +
                ", attachmentFunction=" + attachmentFunction +
                ", yzAttachments=" + yzAttachments +
                ", scAttachments=" + scAttachments +
                ", djbAttachments=" + djbAttachments +
                ", rcAttachments=" + rcAttachments +
                ", yzBAttachments=" + yzBAttachments +
                ", scBAttachments=" + scBAttachments +
                ", djbBAttachments=" + djbBAttachments +
                ", rcBAttachments=" + rcBAttachments +
                ", isDelete=" + isDelete +
                ", addTime=" + addTime +
                ", addNo=" + addNo +
                ", updateTime=" + updateTime +
                ", updateNo=" + updateNo +
                ", signature=" + signature +
                '}';
    }
}