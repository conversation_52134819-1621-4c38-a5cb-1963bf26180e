package com.vedeng.finance.dto;

import com.vedeng.authorization.model.User;
import com.vedeng.common.page.Page;
import com.vedeng.finance.model.PayApply;
import com.vedeng.system.model.SysOptionDefinition;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/9/11
 */
@Setter
@Getter
@Data
public class PayApplyForPageDto implements Serializable {

    private List<PayApply> payApplyList;

    private Page page;

    private PayApply payApply;


}
