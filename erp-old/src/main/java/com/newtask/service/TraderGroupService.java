package com.newtask.service;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.crm.api.dto.customergroup.TraderGroupEXTDto;
import com.vedeng.trader.group.model.TraderGroup;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.vo.TraderCustomerVo;

import java.util.List;
import java.util.Map;

public interface TraderGroupService {

    /**
     * <b>Description:</b>处理客户分群集合计算<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/26
     */
    ResultInfo handleTraderGroup(List<TraderGroupEXTDto> traderGroupList,List<Integer> organizations);

    /**
     * <b>Description:</b>处理单个客户分群计算<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/26
     */
    ResultInfo handleOneGroup(TraderGroupEXTDto group,List<Integer> organizations);

    /**
     * 获取客户分组信息
     * <AUTHOR>
     * @Date 11:47 上午 2020/5/30
     * @Param
     * @return
     **/
    List<TraderGroup> getTraderGroupInfo(List<Integer> traderGroupIdList);

    /**
     * 将客户分组信息list转为map
     * <AUTHOR>
     * @Date 11:07 上午 2020/6/1
     * @Param
     * @return
     **/
    Map<Integer, TraderGroup> getTraderGroupMap(List<TraderGroup> traderGroupList);

    /**
     * 设置客户分组名称
     * <AUTHOR>
     * @Date 11:45 上午 2020/6/1
     * @Param
     * @return
     **/
    List<TraderCustomerVo> setTraderGroupname(List<TraderCustomerVo> list, List<TraderGroup> traderGroupList);

    List<Trader> queryNoPublicGroupList();
}
