package com.wms.dao;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Date;


import com.wms.dto.WmsInOutPersonDto;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/12/11 10:40
 **/
public interface WmsInOutPersonMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(WmsInOutPersonDto record);

    int insertSelective(WmsInOutPersonDto record);

    WmsInOutPersonDto selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(WmsInOutPersonDto record);

    int updateByPrimaryKey(WmsInOutPersonDto record);

    List<WmsInOutPersonDto> selectByTypeAndDate(@Param("type")Integer type,@Param("date")String date);

    List<WmsInOutPersonDto> selectByType(@Param("type")Integer type);




}