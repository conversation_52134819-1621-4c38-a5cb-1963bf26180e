package com.wms.inventoryadjustment.model.dto;

import java.io.Serializable;

/**
 * 库存调整单检索传输对象
 *
 * @author: hugo
 * @date: 2020/7:/29 11:36:22
 */
public class InventoryAdjustmentSearchDto implements Serializable {
    /**
     * 库存调整单单号
     */
    private String inventoryAdjustmentNo;

    /**
     * 库存调整单类型
     */
    private Integer type;

    /**
     * 搜索开始时间
     */
    private String searchBeginTime;

    /**
     * 搜索结束时间
     */
    private String searchEndTime;

    /**
     * 搜索开始时间戳
     */
    private Long startTime;

    /**
     * 搜索结束时间戳
     */
    private Long endTime;

    public String getInventoryAdjustmentNo() {
        return inventoryAdjustmentNo;
    }

    public void setInventoryAdjustmentNo(String inventoryAdjustmentNo) {
        this.inventoryAdjustmentNo = inventoryAdjustmentNo;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSearchBeginTime() {
        return searchBeginTime;
    }

    public void setSearchBeginTime(String searchBeginTime) {
        this.searchBeginTime = searchBeginTime;
    }

    public String getSearchEndTime() {
        return searchEndTime;
    }

    public void setSearchEndTime(String searchEndTime) {
        this.searchEndTime = searchEndTime;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }
}
