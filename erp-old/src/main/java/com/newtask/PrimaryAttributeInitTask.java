package com.newtask;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.goods.dao.CoreSpuGenerateExtendMapper;
import com.vedeng.goods.dao.SpuAttrMappingGenerateMapper;
import com.vedeng.goods.model.SpuAttrMappingGenerate;
import com.vedeng.goods.model.vo.BaseAttributeVo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: spu商品类型为耗材和试剂的初始化主属性
 * @date 2021/6/8 17:06
 */
@Component
@JobHandler(value="primaryAttributeInitTask")
public class PrimaryAttributeInitTask extends AbstractJobHandler {

    @Resource
    private CoreSpuGenerateExtendMapper coreSpuGenerateExtendMapper;

    @Resource
    private SpuAttrMappingGenerateMapper spuAttrMappingGenerateMapper;

    private static final Logger LOGGER= LoggerFactory.getLogger(PrimaryAttributeInitTask.class);

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        List<Integer> spuTypes = Arrays.asList(317,318);
        List<BaseAttributeVo> baseAttributeVos = coreSpuGenerateExtendMapper.selectAllAttributeBySpuType(spuTypes);

        LOGGER.info("spu初始化主属性开始:" + JSON.toJSONString(baseAttributeVos));
        if (CollectionUtils.isNotEmpty(baseAttributeVos)){
            Map<Integer,List<BaseAttributeVo>> baseMap = baseAttributeVos.stream().collect(Collectors.groupingBy(BaseAttributeVo::getSpuId));
            for (Integer key : baseMap.keySet()) {
                List<BaseAttributeVo> attributeVos = baseMap.get(key);
                List<BaseAttributeVo> baseAttributeVoList = attributeVos.stream().filter(item -> ErpConst.ONE.equals(item.getIsPrimary())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(baseAttributeVoList)){
                    continue;
                }
                SpuAttrMappingGenerate baseAttribute = new SpuAttrMappingGenerate();
                Integer spuAttrId = attributeVos.get(0).getSpuAttrId();
                baseAttribute.setSpuAttrId(spuAttrId);
                baseAttribute.setIsPrimary(1);
                spuAttrMappingGenerateMapper.updateByPrimaryKeySelective(baseAttribute);
            }
        }
        return SUCCESS;
    }
}
