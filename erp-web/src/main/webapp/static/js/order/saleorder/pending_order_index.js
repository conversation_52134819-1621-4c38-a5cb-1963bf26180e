//全选
function selectall(obj, name) {
    console.log($(obj).val())
    console.log(name)
    checkLogin();
    if ($(obj).is(":checked")) {
        $("input[name='" + $(obj).val() + "']").not(':disabled').prop("checked", true);
        if (name != "") {
            $("input[id='" + name + "']").not(':disabled').prop("checked", true);
        }
    } else {
        $("input[name='" + $(obj).val() + "']").not(':disabled').prop('checked', false);
        if (name != "") {
            $("input[id='" + name + "']").not(':disabled').prop('checked', false);
        }
    }
}

//复制链接
function copyOrderIds(traderId,traderContactId) {

    if (traderId == null || traderId == "") {
        layer.alert("客户名称为空，不能复制")
        return;
    }
    if (traderContactId == null || traderContactId == "") {
        layer.alert("订单联系人ID为空，不能复制")
        return;
    }

    var Url2=document.getElementById("copyUrl");
    Url2.style.display="block"
    Url2.select(); // 选择对象
    document.execCommand("Copy"); // 执行浏览器复制命令
    Url2.style.display="none"
    // $.ajax({
    //     url: page_url + '/orderstream/saleorder/copyOrderLink.do?traderId='+traderId+'&traderContactId='+traderContactId,
    //     type: 'GET',
    //     dataType: 'html',
    //     success: function (data) {
    //         alert("复制成功")
    //     }, error: function (data) {
    //         alert("复制失败")
    //     }
    // })

}

//发送短信
function sendSms() {
    var saleorderList;
    var saleorderReq = $("input[name='saleorderReq']").val();


    var saleorderChecks = [];
    $.each($("input[name='checknox']:checked"), function () {
        var saleorder = {'traderId':$(this).attr('data-traderId'),
            'traderContactMobile':$(this).attr('data-traderContactMobile'),
            'saleorderGoodsId':$(this).attr('data-saleorderGoodsId'),
            'goodsName':$(this).attr('data-goodsName')};
        saleorderChecks.push(saleorder);
    });


    if (saleorderChecks == null ||saleorderChecks.length <= 0) {
        saleorderChecks.length = 0;
        saleorderChecks.push(JSON.parse(saleorderReq))
        saleorderList = saleorderChecks;
    } else {
        saleorderList = saleorderChecks;
    }
    sendSmsData(saleorderList);
}

function  sendSmsData(saleorderList) {
    $.ajax({
        url: page_url + '/orderstream/saleorder/sendSms.do',
        type: 'POST',
        data: JSON.stringify(saleorderList),
        dataType: 'json',
        contentType: "application/json",
        success: function (data) {

        }, error: function (data) {

        }
    })
}


function checkInfo(e) {
    var checked = $(e)[0].checked;
    console.log(checked)

    var $logistics = $(e).parents('table:first').find('.J-check-item:checked');
    var _traderName;
    var _traderContactName;

    var flag = false;
    $logistics.each( (i, item)=> {
        if($(item).attr('data-traderName') != _traderName ||
            $(item).attr('data-traderContactName') != _traderContactName){
            flag = true
            return;
        }
    });
}