function getAllSubUserList() {
    return axios({
        url: '/user/getAllSubUserList.do',
        method: 'get'
    })
}

// 当前用户登录的顶级部门下及下属部门所有的销售
function getSaleUserList() {
    return axios({
        url: '/user/getUserList.do',
        method: 'get'
    })
}

function getAllNotDisabledUserList() {
    return axios({
        url: '/user/getAllNotDisabledUserList.do',
        method: 'get'
    })
}

function getSysOptionDefinitionByParentId(param) {
    return axios({
        url: '/sysOptionDefinition/get/parentId.do',
        method: 'post',
        params: {"parentId": param},
    })
}