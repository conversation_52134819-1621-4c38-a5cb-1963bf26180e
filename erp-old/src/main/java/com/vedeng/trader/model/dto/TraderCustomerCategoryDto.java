package com.vedeng.trader.model.dto;

import com.vedeng.trader.model.TraderCustomerCategory;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户类型分类dto
 * @date 2022/4/22 11:33
 */
@Data
public class TraderCustomerCategoryDto {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 父级ID
     */
    private Integer parentId;

    /**
     * 客户分类名称
     */
    private String customerCategoryName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 分类子类
     */
    private List<TraderCustomerCategoryDto> children;

    public TraderCustomerCategoryDto entityToDto(TraderCustomerCategory traderCustomerCategory) {
        TraderCustomerCategoryDto traderCustomerCategoryDto = new TraderCustomerCategoryDto();
        traderCustomerCategoryDto.setId(traderCustomerCategory.getTraderCustomerCategoryId());
        traderCustomerCategoryDto.setParentId(traderCustomerCategory.getParentId());
        traderCustomerCategoryDto.setSort(traderCustomerCategory.getSort());
        traderCustomerCategoryDto.setCustomerCategoryName(traderCustomerCategory.getCustomerCategoryName());
        return traderCustomerCategoryDto;
    }


}
