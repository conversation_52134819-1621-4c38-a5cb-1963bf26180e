<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="确认撤回" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/jquery.min.js"></script>
 <div class="formpublic">
            <form method="post" action="<%= basePath %>/order/quote/authorizationWithdrow.do" id="zzform">
                <ul>

                    <li>
                       <span style="color: red">是否确认撤回授权书申请，此操作不可逆！请谨慎操作！</span>
                    </li>

                </ul>
                <textarea name="comments" id="comment" style="margin-left: 85px;width: 300px"></textarea>
                <div class="add-tijiao tcenter">
                	<input type="hidden" name="formToken" value="${formToken}"/>
                	<input type="hidden" value="${authorizationApplyId}" name="authorizationApplyId">
                    <input type="hidden" value="${quoteorderId}" name="quoteorderId">
                    <button type="button" class="bg-light-green" onclick="chehui()">确定</button>

                </div>
           </form>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/order/quote/authorization_ch.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>