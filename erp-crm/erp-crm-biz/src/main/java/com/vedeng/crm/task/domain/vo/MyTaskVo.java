package com.vedeng.crm.task.domain.vo;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.crm.task.domain.dto.TaskUserDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 我的任务记录 前端展示
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class MyTaskVo {

    /**
     * 任务主键
     */
    private Long taskId;

    /**
     * 我的任务主键
     */
    private Long taskItemId;

    /**
     * 任务内容
     */
    private String taskContent;

    /**
     * 业务id
     */
    private Integer bizId;

    /**
     * 业务编号
     */
    private String bizNo;

    /**
     * 1：商机 2：线索 3：报价
     */
    private Integer bizType;

    /**
     * 任务类型
     * 1.初步产品方案
     * 2.单品询价
     * 3.综合询价
     * 4.商机督导
     * 5.再次跟进
     */
    private Integer mainTaskType;

    /**
     * 主任务类型名称
     */
    private String mainTaskTypeLabel;

    /**
     * 商机督导-督导类型 逗号分割
     */
    private String subTaskType;

    /**
     * 详情中需展示全任务类型，包含二级
     */
    private String taskType;


    /**
     * 处理状态：0待处理 1已处理 2关闭
     */
    private Integer doneStatus;

    /**
     * 是否超时
     */
    private Integer isOverTime;

    /**
     * 创建人
     */
    private Integer creatorId;

    /**
     * 发起人
     */
    private TaskUserDto applyUser;

    /**
     * 发起人id
     */
    private Integer applyUserId;

    /**
     * 发起人用户名
     */
    private String applyUserName;

    /**
     * 发起人头像
     */
    private String applyUserAliasHeadPicture;

    /**
     * 待办人id
     */
    private Integer taskUserId;

    /**
     * 待办人
     */
    private TaskUserDto todoUser;

    /**
     * 待办人id
     */
    private Integer todoUserId;

    /**
     * 待办人用户名
     */
    private String todoUserName;

    /**
     * 待办人头像
     */
    private String todoUserAliasHeadPicture;

    /**
     * 截止时间
     */
    private Date deadline;

    /**
     * 提交时间
     */
    private Date commitTime;

    /**
     * 处理人
     */
    private TaskUserDto doneUser;

    /**
     * 处理人id
     */
    private Integer doneUserId;

    /**
     * 处理人用户名
     */
    private String doneUserName;

    /**
     * 处理人头像
     */
    private String doneUserAliasHeadPicture;

    /**
     * 处理时间
     */
    private Date doneTime;

    /**
     * 处理结果
     */
    private String doneRemark;

    /**
     * 子任务列表
     */
    List<MyTaskVo> subTaskList;

    /**
     * 子任务中的待办人
     */
    List<TaskUserDto> subTodoUserList;

    /**
     * 是否可处理
     */
    private Integer canHandle = ErpConstant.F;

    /**
     * 客户id
     */
    private Integer traderCustomerId;

    /**
     * 客户名称
     */
    private String traderCustomerName;

    /**
     * 客户详情连接
     */
    private String traderNameLink;

    /**
     * 客户详内部连接
     */
    private String traderNameInnerLink;




    public TaskUserDto getApplyUser() {
        if (this.applyUser != null) {
            this.applyUserId = this.applyUser.getUserId();
            this.applyUserName = this.applyUser.getUserName();
            this.applyUserAliasHeadPicture = this.applyUser.getAliasHeadPicture();

        }
        return applyUser;
    }

    public TaskUserDto getTodoUser() {
        if (this.todoUser != null) {
            this.todoUserId = this.todoUser.getUserId();
            this.todoUserName = this.todoUser.getUserName();
            this.todoUserAliasHeadPicture = this.todoUser.getAliasHeadPicture();
        }
        return todoUser;
    }

    public TaskUserDto getDoneUser() {
        if (this.doneUser != null) {
            this.doneUserId = this.doneUser.getUserId();
            this.doneUserName = this.doneUser.getUserName();
            this.doneUserAliasHeadPicture = this.doneUser.getAliasHeadPicture();
        }
        return doneUser;
    }

    public List<TaskUserDto> getSubTodoUserList() {
        if (CollUtil.isNotEmpty(this.subTaskList)) {
            this.subTodoUserList = this.subTaskList.stream().map(MyTaskVo::getTodoUser).collect(Collectors.toList());
        }
        return subTodoUserList;
    }
}