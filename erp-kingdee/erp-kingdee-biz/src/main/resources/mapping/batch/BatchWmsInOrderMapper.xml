<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchWmsInOrderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchWmsInOrderDto">
    <!--@mbg.generated-->
    <!--@Table T_WMS_IN_ORDER-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="WMS_NO" jdbcType="VARCHAR" property="wmsNo" />
    <result column="ERP_NO" jdbcType="VARCHAR" property="erpNo" />
    <result column="CONSIGNEE" jdbcType="VARCHAR" property="consignee" />
    <result column="INSPECTOR" jdbcType="VARCHAR" property="inspector" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, WMS_NO, ERP_NO, CONSIGNEE, INSPECTOR, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, 
    UPDATER_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_WMS_IN_ORDER
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_WMS_IN_ORDER
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWmsInOrderDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_IN_ORDER (WMS_NO, ERP_NO, CONSIGNEE, 
      INSPECTOR, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, CREATOR_NAME, 
      UPDATER_NAME)
    values (#{wmsNo,jdbcType=VARCHAR}, #{erpNo,jdbcType=VARCHAR}, #{consignee,jdbcType=VARCHAR}, 
      #{inspector,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updaterName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWmsInOrderDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_IN_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="wmsNo != null">
        WMS_NO,
      </if>
      <if test="erpNo != null">
        ERP_NO,
      </if>
      <if test="consignee != null">
        CONSIGNEE,
      </if>
      <if test="inspector != null">
        INSPECTOR,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="wmsNo != null">
        #{wmsNo,jdbcType=VARCHAR},
      </if>
      <if test="erpNo != null">
        #{erpNo,jdbcType=VARCHAR},
      </if>
      <if test="consignee != null">
        #{consignee,jdbcType=VARCHAR},
      </if>
      <if test="inspector != null">
        #{inspector,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWmsInOrderDto">
    <!--@mbg.generated-->
    update T_WMS_IN_ORDER
    <set>
      <if test="wmsNo != null">
        WMS_NO = #{wmsNo,jdbcType=VARCHAR},
      </if>
      <if test="erpNo != null">
        ERP_NO = #{erpNo,jdbcType=VARCHAR},
      </if>
      <if test="consignee != null">
        CONSIGNEE = #{consignee,jdbcType=VARCHAR},
      </if>
      <if test="inspector != null">
        INSPECTOR = #{inspector,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWmsInOrderDto">
    <!--@mbg.generated-->
    update T_WMS_IN_ORDER
    set WMS_NO = #{wmsNo,jdbcType=VARCHAR},
      ERP_NO = #{erpNo,jdbcType=VARCHAR},
      CONSIGNEE = #{consignee,jdbcType=VARCHAR},
      INSPECTOR = #{inspector,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-12-11-->
  <select id="selectByWmsNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_WMS_IN_ORDER
    where WMS_NO=#{wmsNo,jdbcType=VARCHAR}
  </select>
</mapper>