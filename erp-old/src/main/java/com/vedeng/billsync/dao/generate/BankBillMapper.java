package com.vedeng.billsync.dao.generate;

import com.vedeng.billsync.task.model.entity.generate.BankBillDo;
import com.vedeng.finance.model.BankBill;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface BankBillMapper {
    int deleteByPrimaryKey(Integer bankBillId);

    int insert(BankBillDo record);

    int insertSelective(BankBillDo record);

    BankBillDo selectByPrimaryKey(Integer bankBillId);

    int updateByPrimaryKeySelective(BankBillDo record);

    int updateByPrimaryKey(BankBillDo record);

    int updateByPrimaryKeySelectiveNew(BankBill bankBill);
}