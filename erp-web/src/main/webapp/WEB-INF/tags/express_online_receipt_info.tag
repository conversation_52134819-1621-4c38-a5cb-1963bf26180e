<%@ tag language="java" pageEncoding="UTF-8" %>
<%@attribute name="expressOnlineReceiptList" type="java.util.List" required="true" %>
<%@attribute name="traderContactName" type="java.lang.String" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>


<div class="parts" liname="在线确认" id="在线确认">
    <div class="title-container">
        <div class="table-title nobor">
            客户在线确认记录
        </div>
    </div>
    <table class="table">
        <thead>
        <tr>
            <th class="wid14">快递单号</th>
            <th class="wid12">快递公司</th>
            <th class="wid18">客户名称</th>
            <th class="wid12">客户ID</th>
            <th class="wid16">在线确认者注册手机</th>
            <th class="wid18">在线确认者及用户ID</th>
            <th class="wid20">在线确认时间</th>
            <th class="wid18">备注</th>
        </tr>
        </thead>
        <tbody id="onliensignatureInfo">
        <c:forEach var="expressOnlineReceipt" items="${expressOnlineReceiptList}">
            <tr>

                <td>${expressOnlineReceipt.logisticsNo}</td>
                <td>${expressOnlineReceipt.logisticsName}</td>
                <td>${expressOnlineReceipt.traderName}</td>
                <td>${expressOnlineReceipt.traderId}</td>
                <td>${expressOnlineReceipt.mobile}</td>
                <td>${traderContactName}/${expressOnlineReceipt.userId}</td>
                <td><date:date value="${expressOnlineReceipt.signTime}"/></td>
                <td>${expressOnlineReceipt.comments}</td>
            </tr>
        </c:forEach>
        <c:if test="${empty expressOnlineReceiptList}">
            <tr>
                <td colspan="8">暂无客户在线确认记录！</td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>