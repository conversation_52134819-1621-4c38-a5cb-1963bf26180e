package com.vedeng.flash.service.oreding.impl;

import com.pricecenter.dto.SkuPriceChangeApplyDto;
import com.vedeng.authorization.dao.RegionMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.jasper.IreportExport;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.flash.dao.*;
import com.vedeng.flash.dto.PrepareStockCommand;
import com.vedeng.flash.dto.PrepareStockDto;
import com.vedeng.flash.dto.po.NewAddOrderingQueryPO;
import com.vedeng.flash.dto.po.OrderingPoolQueryPO;
import com.vedeng.flash.dto.po.RecommendOrderingQueryPO;
import com.vedeng.flash.dto.temp.JoinOrderingPoolOperateTemp;
import com.vedeng.flash.dto.temp.OneThreeMonthLastYearSaleNumTemp;
import com.vedeng.flash.dto.temp.SumaryRepeatCountTemp;
import com.vedeng.flash.dto.vo.*;
import com.vedeng.flash.enums.AdjustReasonEnum;
import com.vedeng.flash.enums.DealTypeEnum;
import com.vedeng.flash.model.PrepareStockLog;
import com.vedeng.flash.model.RegularOperateLog;
import com.vedeng.flash.model.RegularPrepareSku;
import com.vedeng.flash.model.RegularSnapshot;
import com.vedeng.flash.service.oreding.OrderingPoolService;
import com.vedeng.flash.service.prepare.impl.PrepareStockServiceImpl;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.logistics.service.WarehouseStockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @program: erp.vedeng.com
 * @description: 定品池服务实现类
 * @author: Pusan
 * @create: 2021-05-18 20:31
 **/
@Service
@Slf4j
public class OrderingPoolServiceImpl implements OrderingPoolService {

    @Resource
    private RegularPrepareSkuMapper regularPrepareSkuMapper;

    @Resource
    private PrepareStockLogMapper prepareStockLogMapper;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Resource
    private RegionMapper regionMapper;

    @Autowired
    private PrepareStockServiceImpl prepareStockService;

    @Resource
    private RegularOperateLogMapper regularOperateLogMapper;

    @Resource
    private RegularSnapshotMapper regularSnapshotMapper;

    @Resource
    private EarlyWarningTaskMapper earlyWarningTaskMapper;



    @Override
    public List<OrderingPoolQueryPO> getOrderingPoolListPage(HttpServletRequest httpServletRequest, Page page, OrderingPoolQueryVO orderingPoolQueryVO) {

        // 操作用户 (预留)
        User user =(User)httpServletRequest.getSession().getAttribute(ErpConst.CURR_USER);

        // 传参方式
        Map<String, Object> mapParamValue = new HashMap<String, Object>();
        mapParamValue.put("page", page);
        mapParamValue.put("orderingPoolQueryVO", orderingPoolQueryVO);
        return regularPrepareSkuMapper.selectOrderingPoollistpage(mapParamValue);
    }

    @Override
    public List<RecommendOrderingQueryPO> getRecommendOrderingListPage(HttpServletRequest httpServletRequest, Page page, RecommendOrderingQueryVO recommendOrderingQueryVO) {

        // 操作用户 (预留)
        User user =(User)httpServletRequest.getSession().getAttribute(ErpConst.CURR_USER);

        // 传参方式
        Map<String, Object> mapParamValue = new HashMap<String, Object>();
        mapParamValue.put("page", page);
        mapParamValue.put("recommendOrderingQueryVO", recommendOrderingQueryVO);
        return regularPrepareSkuMapper.selectRecommendOrderinglistpage(mapParamValue);
    }

    @Override
    public List<OneThreeMonthLastYearSaleNumTemp> getOneThreeMonthLastYearSaleNum(List<Integer> skuIdList) {

        List<OneThreeMonthLastYearSaleNumTemp> returnOneThreeMonthLastYearSaleNumTemps = new ArrayList<>();

        List<OneThreeMonthLastYearSaleNumTemp> lastYearSaleNum = regularPrepareSkuMapper.selectLastYearSumSaleNum(skuIdList);
        List<OneThreeMonthLastYearSaleNumTemp> lastYearPartSaleNum = regularPrepareSkuMapper.selectLastYearPartSaleNum(skuIdList);
        List<OneThreeMonthLastYearSaleNumTemp> threeMonthSumSaleNum = regularPrepareSkuMapper.selectThreeMonthSumSaleNum(skuIdList);
        List<OneThreeMonthLastYearSaleNumTemp> threeMonthPartSaleNum = regularPrepareSkuMapper.selectThreeMonthPartSaleNum(skuIdList);
        List<OneThreeMonthLastYearSaleNumTemp> oneMonthSumSaleNum = regularPrepareSkuMapper.selectOneMonthSumSaleNum(skuIdList);
        List<OneThreeMonthLastYearSaleNumTemp> oneMonthPartSaleNum = regularPrepareSkuMapper.selectOneMonthPartSaleNum(skuIdList);

        // 转换
        Map<Integer, BigDecimal> lastYearSumSaleNumMap = new HashMap<>();
        Map<Integer, BigDecimal> lastYearPartSaleNumMap = new HashMap<>();
        Map<Integer, BigDecimal> threeMonthSumSaleNumMap = new HashMap<>();
        Map<Integer, BigDecimal> threeMonthPartSaleNumMap = new HashMap<>();
        Map<Integer, BigDecimal> oneMonthSumSaleNumMap = new HashMap<>();
        Map<Integer, BigDecimal> oneMonthPartSaleNumMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(lastYearSaleNum)){
         lastYearSumSaleNumMap = lastYearSaleNum.stream()
                .collect(Collectors.toMap(k -> k.getSkuId(), v -> v.getLastYearSum(), (oldValue, newValue) -> newValue));}
        if(!CollectionUtils.isEmpty(lastYearPartSaleNum)){
         lastYearPartSaleNumMap = lastYearPartSaleNum.stream()
                .collect(Collectors.toMap(k -> k.getSkuId(), v -> v.getLastYearPart(), (oldValue, newValue) -> newValue));}
        if(!CollectionUtils.isEmpty(threeMonthSumSaleNum)){
         threeMonthSumSaleNumMap = threeMonthSumSaleNum.stream()
                .collect(Collectors.toMap(k -> k.getSkuId(), v -> v.getThreeMonthSum(), (oldValue, newValue) -> newValue));}
        if(!CollectionUtils.isEmpty(threeMonthPartSaleNum)){
         threeMonthPartSaleNumMap = threeMonthPartSaleNum.stream()
                .collect(Collectors.toMap(k -> k.getSkuId(), v -> v.getThreeMonthPart(), (oldValue, newValue) -> newValue));}
        if(!CollectionUtils.isEmpty(oneMonthSumSaleNum)){
         oneMonthSumSaleNumMap = oneMonthSumSaleNum.stream()
                .collect(Collectors.toMap(k -> k.getSkuId(), v -> v.getOneMonthSum(), (oldValue, newValue) -> newValue));}
        if(!CollectionUtils.isEmpty(oneMonthPartSaleNum)){
         oneMonthPartSaleNumMap = oneMonthPartSaleNum.stream()
                .collect(Collectors.toMap(k -> k.getSkuId(), v -> v.getOneMonthPart(), (oldValue, newValue) -> newValue));}

        // 组装数据
        for(Integer skuId:skuIdList){
            returnOneThreeMonthLastYearSaleNumTemps.add(
                    OneThreeMonthLastYearSaleNumTemp
                            .builder()
                            .skuId(skuId)
                            .lastYearSum(lastYearSumSaleNumMap.get(skuId)==null?BigDecimal.ZERO:lastYearSumSaleNumMap.get(skuId))
                            .lastYearPart(lastYearPartSaleNumMap.get(skuId)==null?BigDecimal.ZERO:lastYearPartSaleNumMap.get(skuId))
                            .threeMonthSum(threeMonthSumSaleNumMap.get(skuId)==null?BigDecimal.ZERO:threeMonthSumSaleNumMap.get(skuId))
                            .threeMonthPart(threeMonthPartSaleNumMap.get(skuId)==null?BigDecimal.ZERO:threeMonthPartSaleNumMap.get(skuId))
                            .oneMonthSum(oneMonthSumSaleNumMap.get(skuId)==null?BigDecimal.ZERO:oneMonthSumSaleNumMap.get(skuId))
                            .oneMonthPart(oneMonthPartSaleNumMap.get(skuId)==null?BigDecimal.ZERO:oneMonthPartSaleNumMap.get(skuId))
                            .build()
            );
        }
    log.info("returnOneThreeMonthLastYearSaleNumTemps -- {}",returnOneThreeMonthLastYearSaleNumTemps);
        return returnOneThreeMonthLastYearSaleNumTemps;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void joinOrderingPoolBySkuId(List<JoinOrderingPoolOperateTemp> joinOrderingPoolOperateTemps, SumaryRepeatCountTemp sumaryRepeatCountTemp) {

        // 统计重复数量
        AtomicInteger repeatCount = new AtomicInteger(sumaryRepeatCountTemp.getRepeatCount());

        // 获取初始化安全库存(进三月日均)
        List<Integer> toCalculateSkuIdList = joinOrderingPoolOperateTemps.stream().map(item -> item.getSkuId()).collect(Collectors.toList());
        List<PrepareStockDto> recentSkuSaleNumListModify = regularPrepareSkuMapper.getRecentSkuSaleNumListModify(toCalculateSkuIdList);
        log.info("获取初始化安全库存(进三月日均) recentSkuSaleNumListModify -- {}",recentSkuSaleNumListModify);

        // 获取全局安全库存系数
        Integer safeRatioTemp = regularPrepareSkuMapper.getSafeRatio();
        if(safeRatioTemp == null){
            safeRatioTemp = Integer.valueOf(45);
        }
        Integer finalSafeRatioTemp = safeRatioTemp;

        // 加入前判断是否已经存在
        joinOrderingPoolOperateTemps.stream().forEach(item -> {
            // 指定日均安全库存
            Optional<PrepareStockDto> safeStockAvgSum = recentSkuSaleNumListModify
                    .stream()
                    .filter(tmp -> tmp.getSkuId().equals(item.getSkuId()))
                    .findFirst();

            // 安全库存 初始化
            Integer safeStock = safeStockAvgSum.isPresent() ?
                    safeStockAvgSum.get().getThreeMonthDaysSaleNum().intValue() * finalSafeRatioTemp.intValue() :
                    Integer.valueOf(0);
            // 优先前端传来的
            safeStock = (item.getSafeStock() == null)?safeStock:item.getSafeStock();

            // 查询该Sku对应定品状态
            RegularPrepareSku regularPrepareSku = regularPrepareSkuMapper.selectBySkuId(item.getSkuId());
            if(regularPrepareSku != null){
                // 删除状态
                if(Integer.valueOf(1).equals(regularPrepareSku.getSkuStatus())){
                    // 删除状态 -- 更新定品状态(不物理删除)
                    RegularPrepareSku regularPrepareSkuRecord = RegularPrepareSku
                            .builder()
                            .regularId(regularPrepareSku.getRegularId())
                            .skuId(item.getSkuId())
                            .skuStatus(Integer.valueOf(0))
                            .safeStock(safeStock)
                            .safeRatio(finalSafeRatioTemp)
                            .deployType(Integer.valueOf(0))
                            .isAdjustStock(Integer.valueOf(0))
                            .warnLevel(Integer.valueOf(0))
                            .isPrepare(Integer.valueOf(0))
                            .lastAddTime(DateUtil.sysTimeMillis())
                            .creator(sumaryRepeatCountTemp.getCurrentUser().getUserId())
                            .addTime(DateUtil.sysTimeMillis())
                            .updator(sumaryRepeatCountTemp.getCurrentUser().getUserId())
                            .updateTime(DateUtil.sysTimeMillis())
                            .build();
                    regularPrepareSkuMapper.updateByPrimaryKey(regularPrepareSkuRecord);
                }
                // 启用状态
                if(Integer.valueOf(0).equals(regularPrepareSku.getSkuStatus())){
                    // 统计重复数量
                    repeatCount.getAndIncrement();

                    // 安全库存调整 && 记录安全库存调整日志(导入时才会添加)
                    if(item.getSafeStock() != null) {
                        // 安全库存调整
                        RegularPrepareSku regularPrepareSkuRecord = RegularPrepareSku
                                .builder()
                                .regularId(regularPrepareSku.getRegularId())
                                .safeStock(safeStock)
                                .originSafeStock(regularPrepareSku.getSafeStock())
                                .isAdjustStock(Integer.valueOf(1))
                                .updator(sumaryRepeatCountTemp.getCurrentUser().getUserId())
                                .updateTime(DateUtil.sysTimeMillis())
                                .build();
                        regularPrepareSkuMapper.updateByPrimaryKeySelective(regularPrepareSkuRecord);
                        // 库存日志添加
                        PrepareStockLog prepareStockLog = new PrepareStockLog();
                        prepareStockLog.setPrepareId(regularPrepareSku.getRegularId());
                        prepareStockLog.setSkuId(regularPrepareSku.getSkuId());
                        prepareStockLog.setSafeStock(item.getSafeStock());
                        prepareStockLog.setOriginSafeStock(regularPrepareSku.getSafeStock());
                        prepareStockLog.setOperateType(AdjustReasonEnum.REASON_FOUR.getCode());
                        prepareStockLog.setOperateUserId(sumaryRepeatCountTemp.getCurrentUser().getUserId());
                        prepareStockLog.setOperateUserName(sumaryRepeatCountTemp.getCurrentUser().getUsername());
                        prepareStockLog.setOperateTime(DateUtil.gainNowDate());
                        prepareStockLog.setOperateReason("批量导入");

                        prepareStockLogMapper.insert(prepareStockLog);

                    }
                }
            }else{
                // 加入定品 joinPool
                RegularPrepareSku regularPrepareSkuRecord = RegularPrepareSku
                        .builder()
                        .skuId(item.getSkuId())
                        .skuStatus(Integer.valueOf(0))
                        .safeStock(safeStock)
                        .safeRatio(finalSafeRatioTemp)
                        .deployType(Integer.valueOf(0))
                        .isAdjustStock(Integer.valueOf(0))
                        .warnLevel(Integer.valueOf(0))
                        .isPrepare(Integer.valueOf(0))
                        .lastAddTime(DateUtil.sysTimeMillis())
                        .creator(sumaryRepeatCountTemp.getCurrentUser().getUserId())
                        .addTime(DateUtil.sysTimeMillis())
                        .updator(sumaryRepeatCountTemp.getCurrentUser().getUserId())
                        .updateTime(DateUtil.sysTimeMillis())
                        .build();
                regularPrepareSkuMapper.insert(regularPrepareSkuRecord);
            }
            // 获取重复数量
            sumaryRepeatCountTemp.setRepeatCount(repeatCount.get());
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultInfo joinOrderingPoolBySkuNo(List<JoinOrderingPoolOperateTemp> joinOrderingPoolOperateTemps, SumaryRepeatCountTemp sumaryRepeatCountTemp) {

        int rowNum = 1;
        for(JoinOrderingPoolOperateTemp item:joinOrderingPoolOperateTemps) {
            CoreSku coreSku = coreSkuMapper.selectBySkuNo(item.getSkuNo());
            if (coreSku != null) {
                item.setSkuId(coreSku.getSkuId());
            } else {
                return new ResultInfo(-1,"第"+(rowNum)+"行 订货号"+item.getSkuNo()+"不存在");
            }
            rowNum++;
        }

        // SkuNo -> SkuId
        joinOrderingPoolBySkuId(joinOrderingPoolOperateTemps, sumaryRepeatCountTemp);
        return new ResultInfo(0,"OK");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultInfo<Object> batchSaveOrdering(FileInfo fileInfo, User user) throws IOException, InvalidFormatException {

        ResultInfo<Object> resultInfo = new ResultInfo<>();
        if (fileInfo.getCode() != 0){
            resultInfo.setMessage("临时文件上传失败!");
            return resultInfo;
        }

        // 解析数据
        // 1.校验上传的文件类型为Excel
        String suffix = fileInfo.getFilePath().substring(fileInfo.getFilePath().lastIndexOf(".") + 1);
        if (! "xlsx".equalsIgnoreCase(suffix)){
            resultInfo.setCode(-1);
            resultInfo.setMessage("上传文件格式校验失败!");
            return resultInfo;
        }
        Workbook workbook = null;
        FileInputStream fileInputStream = new FileInputStream(new File(fileInfo.getFilePath()));
        try {
            workbook = WorkbookFactory.create(fileInputStream);
            Sheet sheet = workbook.getSheetAt(0);

            int startRowNum = sheet.getFirstRowNum() + 1;
            int realRows =  sheet.getLastRowNum();

            // 2.验证模板是否为标准模板
            Row firstRow = sheet.getRow(0);
            if (2 != firstRow.getLastCellNum()){
                resultInfo.setCode(-1);
                resultInfo.setMessage("模板不正确，请重新下载模板文件");
                return resultInfo;
            }

            if (0 == realRows){
                resultInfo.setCode(-1);
                resultInfo.setMessage("第1行，第1列不可为空!");
                return resultInfo;
            }

            // 创建一个用于保存Excel数据的对象
            ArrayList<BatchSaveOrderingOperateVO> batchSaveOrderingOperateVOS = new ArrayList<>();
            // 最小击级别区域用于校验
            ArrayList<Integer> zoneList = new ArrayList<>();

            for (int rowNum = startRowNum; rowNum <= realRows; rowNum++) {
                Row row = sheet.getRow(rowNum);
                int startCellNum = row.getFirstCellNum();
                int endCellNum = row.getLastCellNum() - 1;
                BatchSaveOrderingOperateVO batchSaveOrderingOperateVO = new BatchSaveOrderingOperateVO();

                // 获取excel的值
                for (int cellNum = startCellNum; cellNum <= endCellNum; cellNum++) {
                    Cell cell = row.getCell(cellNum);
                    if (cellNum == 0) {
                        //设置单元格类型防止类型转换错误
                        row.getCell(cellNum).setCellType(CellType.STRING);
                        String skuNo = cell.getStringCellValue().trim();
                        if (StringUtil.isBlank(skuNo)){
                            resultInfo.setCode(-1);
                            resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列数据订货号不能为空，请修改后重新导入");
                            return resultInfo;
                        }
                        batchSaveOrderingOperateVO.setSkuNo(skuNo);
                    }
                    if (cellNum == 1) {
                        //设置单元格类型防止类型转换错误
                        row.getCell(cellNum).setCellType(CellType.STRING);
                        Integer safeStock = Integer.valueOf(0);
                        try{
                            safeStock = Integer.valueOf(cell.getStringCellValue());
                            if(safeStock == null){
                                safeStock = Integer.valueOf(0);
                            }
                        }catch (Exception e){
                            log.error("【batchSaveOrdering】处理异常",e);
                        }
                        batchSaveOrderingOperateVO.setSafeStock(safeStock);
                    }
                }
                if(batchSaveOrderingOperateVO.getSafeStock() == null){batchSaveOrderingOperateVO.setSafeStock(Integer.valueOf(0));}
                if(batchSaveOrderingOperateVO.getSkuNo() == null){
                    if (StringUtil.isBlank(batchSaveOrderingOperateVO.getSkuNo())){
                        resultInfo.setCode(-1);
                        resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (1) + "列数据订货号不能为空，请修改后重新导入");
                        return resultInfo;
                    }
                }
                batchSaveOrderingOperateVOS.add(batchSaveOrderingOperateVO);
            }
            log.info("解析到集合数据 batchSaveOrderingOperateVOS -- {}",batchSaveOrderingOperateVOS);
            int totalSize = batchSaveOrderingOperateVOS.size();

            if (CollectionUtils.isEmpty(batchSaveOrderingOperateVOS)){
                resultInfo.setCode(-1);
                resultInfo.setMessage("表格上传至少需要一条有效数据，请检查重新上传!");
                return resultInfo;
            }

            // 统计上传模板种重复订货号的行数
            int rowNum = 2;
            Map<String,String> repeatRowMap = new HashMap<>();
            for(BatchSaveOrderingOperateVO item:batchSaveOrderingOperateVOS){
                String skuNo = item.getSkuNo();
                if(repeatRowMap.get(skuNo) == null){repeatRowMap.put(skuNo,"");}
                repeatRowMap.put(skuNo,repeatRowMap.get(skuNo)+rowNum+"]");
                rowNum++;
            }

            StringBuffer msgTips = new StringBuffer();
            msgTips.append("订货号");
            int repeatFlag = 0;
            Set<Map.Entry<String, String>> entries = repeatRowMap.entrySet();
            for(Map.Entry<String,String> repeatRow:entries){
                String[] splits = repeatRow.getValue().split("]");
                StringBuffer tempMsgTips = new StringBuffer();
                if(splits.length>1){
                    // 统计第几行重复
                    /*for(String split:splits) {
                        tempMsgTips.append("第"+split+"行,");
                    }*/
                    msgTips.append("["+ repeatRow.getKey() +"]" );
                    repeatFlag = 1;
                }
            }
            msgTips.append("重复,请核对后重新上传");
            if(repeatFlag>0){
                return new ResultInfo(-1,msgTips.toString());
            }


            // 清空重复统计
            int repeatCount = 0;
            // 落地操作
            SumaryRepeatCountTemp sumaryRepeatCountTemp = SumaryRepeatCountTemp
                    .builder()
                    .repeatCount(repeatCount)
                    .currentUser(user)
                    .build();

            List<JoinOrderingPoolOperateTemp> joinOrderingPoolOperateTempList = new ArrayList<>();
            for(BatchSaveOrderingOperateVO item:batchSaveOrderingOperateVOS){
                joinOrderingPoolOperateTempList.add(
                        JoinOrderingPoolOperateTemp
                                .builder()
                                .skuNo(item.getSkuNo())
                                .safeStock(item.getSafeStock())
                                .build()
                );
            }

            resultInfo = joinOrderingPoolBySkuNo(joinOrderingPoolOperateTempList, sumaryRepeatCountTemp);
            if(resultInfo.getCode() == -1){
                return resultInfo;
            }

            HashMap<String, Integer> stringIntegerHashMap = new HashMap<>();
            stringIntegerHashMap.put("totalCount",totalSize);
            stringIntegerHashMap.put("repeatCount",sumaryRepeatCountTemp.getRepeatCount());

            // 返回结果状态
            resultInfo.setCode(0);
            resultInfo.setData(stringIntegerHashMap);
        } finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (Exception e) {
                    log.error("【batchSaveOrdering】处理异常",e);
                }
            }

            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {
                    log.error("【batchSaveOrdering】处理异常",e);
                }
            }

        }
        return resultInfo;

    }

    @Override
    public List<NewAddOrderingQueryPO> getNewAddOrderingListPage(HttpServletRequest httpServletRequest, Page page, NewAddOrderingQueryVO newAddOrderingQueryVO) {

        // 操作用户 (预留)
        User user =(User)httpServletRequest.getSession().getAttribute(ErpConst.CURR_USER);

        // 传参方式
        Map<String, Object> mapParamValue = new HashMap<String, Object>();
        mapParamValue.put("page", page);
        mapParamValue.put("newAddOrderingQueryVO", newAddOrderingQueryVO);
        return regularPrepareSkuMapper.selectNewAddOrderinglistpage(mapParamValue);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrderingPool(RemoveOrderingPoolOperateVO removeOrderingPoolOperateVO, HttpServletRequest httpServletRequest) {

        // 操作用户 (预留)
        User user =(User)httpServletRequest.getSession().getAttribute(ErpConst.CURR_USER);

        List<String> skuNoList = removeOrderingPoolOperateVO.getSkuNoList()
                .stream()
                .filter(item -> StringUtil.isNotBlank(item))
                .collect(Collectors.toList());
        skuNoList = filterDeletedSku(skuNoList);
        if(CollectionUtils.isEmpty(skuNoList)){
            return;
        }

        // 更新定品状态
        // 预先获取SkuId 根据Sku
        List<CoreSku> skuList = coreSkuMapper.selectSkuBySkuNoList(skuNoList);
        List<Integer> skuIdList = skuList.stream().map(item -> item.getSkuId()).collect(Collectors.toList());
        regularPrepareSkuMapper.updatePrepareRegularBySkuId(skuIdList,user);

        // 查询商品名称&品牌&商品类型等直接可查数据，在途库存等后植入
        List<RegularOperateLog> regularOperateLogBySkuNoList = regularPrepareSkuMapper.selectRegularOperateLogBySkuNoList(skuNoList);
        if (CollectionUtils.isEmpty(regularOperateLogBySkuNoList)) {
            return;
        }

        // 获取近一月 近三月 去年销量 & 设置
        List<OneThreeMonthLastYearSaleNumTemp> oneThreeMonthLastYearSaleNumTemps = getOneThreeMonthLastYearSaleNum(skuIdList);
        if (!CollectionUtils.isEmpty(oneThreeMonthLastYearSaleNumTemps)) {
            regularOperateLogBySkuNoList.stream().forEach(regularOperateLog -> {
                oneThreeMonthLastYearSaleNumTemps.stream().forEach(oneThreeMonthLastYearSaleNumTemp -> {
                    if (oneThreeMonthLastYearSaleNumTemp.getSkuId() != null && regularOperateLog.getSkuId() != null
                            && regularOperateLog.getSkuId().equals(oneThreeMonthLastYearSaleNumTemp.getSkuId())) {
                        regularOperateLog.setOneMonthAmount(oneThreeMonthLastYearSaleNumTemp.getOneMonthSum());
                        regularOperateLog.setYxgOneMonthAmount(oneThreeMonthLastYearSaleNumTemp.getOneMonthPart());
                        regularOperateLog.setThreeMonthAmount(oneThreeMonthLastYearSaleNumTemp.getThreeMonthSum());
                        regularOperateLog.setYxgThreeMonthAmount(oneThreeMonthLastYearSaleNumTemp.getThreeMonthPart());
                        regularOperateLog.setAmount(oneThreeMonthLastYearSaleNumTemp.getLastYearSum());
                        regularOperateLog.setYxgAmount(oneThreeMonthLastYearSaleNumTemp.getLastYearPart());
                    }
                });
            });
        }

        // 获取价格中心采购成本价 & 设置采购成本价
        List<SkuPriceChangeApplyDto> skuPriceApplyList = prepareStockService.querySkuCostFromPriceCenter(skuNoList);
        if (!CollectionUtils.isEmpty(skuPriceApplyList)) {
            regularOperateLogBySkuNoList.stream().forEach(regularOperateLog -> {
                skuPriceApplyList.stream().forEach(skuPrice -> {
                    if (StringUtil.isNotEmpty(skuPrice.getSkuNo()) && StringUtil.isNotEmpty(regularOperateLog.getSkuNo())
                            && regularOperateLog.getSkuNo().equals(skuPrice.getSkuNo())) {
                        regularOperateLog.setCost(skuPrice.getMiddlePrice());
                    }
                });
            });
        }

        // 获取在途数量 & 设置在途数量
        List<PrepareStockDto> inTransitSkuList = regularPrepareSkuMapper.getInTransitSkuNumList(skuNoList);
        if (!CollectionUtils.isEmpty(inTransitSkuList)) {
            regularOperateLogBySkuNoList.stream().forEach(regularOperateLog -> {
                inTransitSkuList.stream().forEach(inTransitSku -> {
                    if (StringUtil.isNotEmpty(inTransitSku.getSkuNo()) && StringUtil.isNotEmpty(regularOperateLog.getSkuNo())
                            && regularOperateLog.getSkuNo().equals(inTransitSku.getSkuNo())) {
                        regularOperateLog.setIntransitStock(inTransitSku.getIntransitStock());
                    }
                });
            });
        }

        // 获取进三月均销量recentSkuSaleNumListModify & 全局安全库存系数globalSafeRatio -> 预测安全库存
        List<PrepareStockDto> recentSkuSaleNumListModify = regularPrepareSkuMapper.getRecentSkuSaleNumListModify(skuIdList);
        Integer globalSafeRatio = regularPrepareSkuMapper.getSafeRatio();
        if(!CollectionUtils.isEmpty(recentSkuSaleNumListModify) && globalSafeRatio != null){
            recentSkuSaleNumListModify.stream().forEach(
                    item->{
                        Integer threeMonthDaysSaleNum = item.getThreeMonthDaysSaleNum();
                        if(threeMonthDaysSaleNum == null){
                            item.setThreeMonthDaysSaleNum(Integer.valueOf(0));
                        }else{
                            item.setThreeMonthDaysSaleNum(item.getThreeMonthDaysSaleNum()*globalSafeRatio);
                        }
                    });
            regularOperateLogBySkuNoList.stream().forEach(regularOperateLog -> {
                recentSkuSaleNumListModify.stream().forEach(recentSkuSaleNum->{
                    if (regularOperateLog.getSkuId() != null && recentSkuSaleNum.getSkuId() != null
                            && regularOperateLog.getSkuId().equals(recentSkuSaleNum.getSkuId())) {
                        regularOperateLog.setForecastSafeStock(recentSkuSaleNum.getThreeMonthDaysSaleNum());
                    }
                });
            });
        }

        // 获取库存数量
        regularOperateLogBySkuNoList.stream().forEach(item->{
            PrepareStockDto stockDto = new PrepareStockDto();
            stockDto.setSkuNo(item.getSkuNo());
            stockDto.setSkuId(item.getSkuId());
            if(item.getSafeStock() == null){
                item.setSafeStock(item.getForecastSafeStock());
            }
            stockDto.setSafeStock(item.getSafeStock());
            stockDto.setIntransitStock(item.getIntransitStock());
            PrepareStockCommand prepareStockCommand = new PrepareStockCommand();
            prepareStockCommand.setStartDate(DateUtil.getStartDateMonths(-3));
            prepareStockCommand.setEndDate(DateUtil.getEndDateMonths(-3));
            // 处理库存数量
            prepareStockService.dealWithStock(stockDto);
            // 平均断货时间
            prepareStockService.dealWidthReceiveTimes(prepareStockCommand,stockDto);
            // 安全库存 - 现有库存 - 在途数量 + 占有数量
            stockDto.setSupplementStock(stockDto.getSafeStock()-stockDto.getStock()-stockDto.getIntransitStock()+stockDto.getOrderStock());

            // 刷新要存储的信息
            item.setStock(stockDto.getStock());
            item.setOrderStock(stockDto.getOrderStock());
            item.setSupplementStock(stockDto.getSupplementStock());
            item.setReceiveTimes(stockDto.getReceiveTimes());
        });

        // 操作人等
        regularOperateLogBySkuNoList.stream().forEach(item->{

            item.setLogType(Integer.valueOf(0));

            item.setOperateType(DealTypeEnum.PREPARE_DELETE.getCode());
            item.setOperateUserId(user.getUserId());
            item.setOperateUserName(user.getUsername());
            item.setOperateTime(DateUtil.gainNowDate());
            item.setOperateReason(removeOrderingPoolOperateVO.getCauseContent());
        });


        // 保存日志完整信息
        regularOperateLogBySkuNoList.stream().forEach(item->{
            regularOperateLogMapper.insertSelective(item);

            // 更新备货计划预警状态
            PrepareStockDto prepareStockDto = new PrepareStockDto();
            prepareStockDto.setSkuId(item.getSkuId());
            earlyWarningTaskMapper.deletePrepareStockBySkuId(prepareStockDto);
        });

    }

    @Override
    public List<RegularOperateLog> removePoolCause(Integer skuId) {
        return regularOperateLogMapper.selectBySkuId(skuId);
    }

    @Override
    public Map<String, Object> exportOrderingList(String exportMonthStr) {

        Map<String, Object> exportMap = new HashMap<>();
        // 判断是否为当前月
        boolean isCurrentMonthFlag = isCurrentMonthFlag(exportMonthStr);
        // 1. orderingPoolList -- 定品池列表
        // 导出当前月份定品
        List<RegularSnapshot> regularSnapshotList = new ArrayList<>();
        if (isCurrentMonthFlag) {
            regularSnapshotList = syncOrderingPoolSkuSnapshot();
        } else {
            PrepareStockCommand prepareStockCommand = new PrepareStockCommand();
            prepareStockCommand.setExportMonth(exportMonthStr);
            regularSnapshotList = regularSnapshotMapper.selectSnapshotListByMonth(prepareStockCommand);
        }
        exportMap.put("orderingPoolList",regularSnapshotList);

        // orderingPoolOperateLogList -- 定品池操作日志
        // 2. 查询操作日志
        long startDate = DateUtil.convertLong(exportMonthStr + "-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        long endDate = DateUtil.convertLong(DateUtil.getLastDayOfMonth(exportMonthStr) + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
        PrepareStockCommand command = new PrepareStockCommand(); command.setStartDate(startDate); command.setEndDate(endDate);
        // 定品池操作日志 （不需要控制）
        List<RegularOperateLog> operateLogList = regularOperateLogMapper.getRegularOperateLogList(command);

        List<RegularOperateLog> regularOperateLogList = new ArrayList<>();
        RegularOperateLog emptyLine = new RegularOperateLog();
        if (!CollectionUtils.isEmpty(operateLogList)) {
            for (RegularOperateLog operateLog : operateLogList) {
                if (operateLog == null) {
                    continue;
                }
                operateLog.setOperateTypeStr(DealTypeEnum.codeToMessage(operateLog.getOperateType()));
                regularOperateLogList.add(operateLog);
                if (operateLog.getOperateType() == DealTypeEnum.PREPARE_REPLACE.getCode()
                        || operateLog.getOperateType() == DealTypeEnum.PREPARE_CONVERT.getCode()) {
                    RegularOperateLog regularOperateLog = regularOperateLogMapper.getChileRegularSku(operateLog.getLogId());
                    if (regularOperateLog != null) {
                        regularOperateLogList.add(regularOperateLog);
                    }
                }
                // 插入一个空行
                regularOperateLogList.add(emptyLine);
            }
        }
        exportMap.put("orderingPoolOperateLogList",regularOperateLogList);
        log.info("exportOrderingListDetail -- {}",exportMap);

        return exportMap;
    }

    private List<RegularSnapshot> syncOrderingPoolSkuSnapshot() {

        // 查询不需要统计的信息
        List<RegularSnapshot> orderingPoolSkuSnapshot = regularPrepareSkuMapper.selectSnapshotListByExportMonth();
        if(CollectionUtils.isEmpty(orderingPoolSkuSnapshot)){
            return new ArrayList<>();
        }
        List<Integer> skuIdList = orderingPoolSkuSnapshot.stream().map(RegularSnapshot::getSkuId).collect(Collectors.toList());
        List<String> skuNoList = orderingPoolSkuSnapshot.stream().map(RegularSnapshot::getSkuNo).collect(Collectors.toList());

        // 近xx 销售额
        List<OneThreeMonthLastYearSaleNumTemp> oneThreeMonthLastYearSaleNumTemps = getOneThreeMonthLastYearSaleNum(skuIdList);
        if (!CollectionUtils.isEmpty(oneThreeMonthLastYearSaleNumTemps)) {
            orderingPoolSkuSnapshot.stream().forEach(snapshot -> {
                oneThreeMonthLastYearSaleNumTemps.stream().forEach(saleNumTemp -> {
                    if(saleNumTemp.getSkuId() == null || snapshot.getSkuId() == null){
                        return;
                    }
                    if (saleNumTemp.getSkuId().intValue() == snapshot.getSkuId().intValue()) {
                        snapshot.setAmount(saleNumTemp.getLastYearSum());
                        snapshot.setThreeMonthAmount(saleNumTemp.getThreeMonthSum());
                        snapshot.setOneMonthAmount(saleNumTemp.getOneMonthSum());
                        snapshot.setYxgAmount(saleNumTemp.getLastYearPart());
                        snapshot.setYxgThreeMonthAmount(saleNumTemp.getThreeMonthPart());
                        snapshot.setYxgOneMonthAmount(saleNumTemp.getOneMonthPart());
                    }
                });
            });
        }

        // 获取价格中心采购成本价
        List<SkuPriceChangeApplyDto> skuPriceApplyList = prepareStockService.querySkuCostFromPriceCenter(skuNoList);
        if (!CollectionUtils.isEmpty(skuPriceApplyList)) {
            orderingPoolSkuSnapshot.stream().forEach(snapshot -> {
                skuPriceApplyList.stream().forEach(skuPrice -> {
                    if (StringUtil.isNotEmpty(skuPrice.getSkuNo()) && StringUtil.isNotEmpty(snapshot.getSkuNo())
                            && snapshot.getSkuNo().equals(skuPrice.getSkuNo())) {
                        snapshot.setCost(skuPrice.getMiddlePrice());
                    }
                });
            });
        }

        return orderingPoolSkuSnapshot;
    }

    private boolean isCurrentMonthFlag(String exportMonthStr) {

        // 获取当前时间月份字符串
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String nowMonthStr = sdf.format(new Date());
        // 导出当前月份定品
        boolean isCurrentMonthFlag = false;
        if (nowMonthStr.equals(exportMonthStr)) {
            isCurrentMonthFlag = true;
        }
        return isCurrentMonthFlag;
    }

    /**
     * 过滤已删除状态定品
     * @param skuNoList
     * @return
     */
    private List<String> filterDeletedSku(List<String> skuNoList) {

        List<String> notDeleteSkuNoList = coreSkuMapper.selectNotDeleteBySkuNoList(skuNoList);
        return notDeleteSkuNoList;
    }

}
