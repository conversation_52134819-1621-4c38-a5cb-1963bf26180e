package com.vedeng.goods.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GoodsRemovalRecordQueryDto extends BaseQueryDto {

    private Integer spuId;

    private String spuName;

    private String operatorName;

    private String operateBeginTime;

    private String operateEndTime;

    private Long operateBeginTimeMills;

    private Long operateEndTimeMills;

}
