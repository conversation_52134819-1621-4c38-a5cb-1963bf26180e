<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>采购费用订单列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="8Mwa3zjYDa" datasource="erp-datasourcetarget" fixednumber="0" append_column_url="" append_row_url=""
      empty_show="" firstcol>
<div id="appendHead">
</div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">采购费用单号</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="BUYORDER_EXPENSE_NO" placeholder="请输入"
                        style="" alias="T1" jdbctype="" data="" datatype="" oper="LIKE"></object>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">订单状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="STATUS" placeholder="默认全部" style=""
                        alias="T1" jdbctype="VARCHAR"
                        data="[{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;进行中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;3&quot;}]"
                        datatype="JSON" oper="EQ"></object>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">付款状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="PAYMENT_STATUS" placeholder="默认全部" style=""
                        alias="T1" jdbctype="VARCHAR"
                        data="[{&quot;V&quot;:&quot;未付款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分付款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部付款&quot;,&quot;K&quot;:&quot;2&quot;}]"
                        datatype="JSON" oper=""></object>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">收票状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="INVOICE_STATUS" placeholder="默认全部" style=""
                        alias="T1" jdbctype="VARCHAR"
                        data="[{&quot;V&quot;:&quot;未收票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收票&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收票&quot;,&quot;K&quot;:&quot;2&quot;}]"
                        datatype="JSON" oper=""></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">订货号</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="SKU" placeholder="请输入" style=""
                        alias="BEID" jdbctype="" data="" datatype="" oper="LIKE"></object>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">产品名称</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="GOODS_NAME" placeholder="请输入" style=""
                        alias="BEID" jdbctype="" data="" datatype="" oper="LIKE"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">产品归属</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="BELONG" placeholder="默认全部" style=""
                        alias="CONCAT_WS('_',T6.ASSIGNMENT_MANAGER_ID,T6.ASSIGNMENT_ASSISTANT_ID)" jdbctype="" data="SELECT DISTINCT tu.USER_ID K, lower(USERNAME) V
FROM T_USER tu
         INNER JOIN T_R_USER_ROLE tru ON tu.USER_ID = tru.USER_ID
         INNER JOIN T_ROLE tr ON tr.ROLE_ID = tru.ROLE_ID
WHERE (tr.ROLE_NAME = '产品专员'
   or tr.ROLE_NAME = '产品主管'
   or tr.ROLE_NAME = '产品总监')       order by USERNAME" datatype="KVSQLCACHE" oper="LIKE"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">是否可库存管理</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="HAVE_STOCK_MANAGE" placeholder="默认全部" style=""
                        alias="BEID" jdbctype=""
                        data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]"
                        datatype="JSON" oper=""></object>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">创建人</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="CREATOR" placeholder="默认全部" style="" alias="T1" jdbctype="" data="SELECT DISTINCT u.USER_ID K, lower(u.USERNAME) V
FROM T_USER u
         LEFT JOIN T_R_USER_POSIT up ON u.USER_ID = up.USER_ID
         LEFT JOIN T_POSITION p on up.POSITION_ID = p.POSITION_ID
         LEFT JOIN T_ORGANIZATION o ON p.ORG_ID = o.ORG_ID
WHERE o.TYPE = 311   order by u.USERNAME" datatype="KVSQLCACHE" oper=""></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">创建人部门</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="search-orggyl" name="ORG_ID" placeholder="默认全部" style="" alias="BED" jdbctype=""   oper="IN"></object>
            </div>
        </div>



        <div class=" layui-inline "><label class="layui-form-label">合并日期搜索</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="search-date" name="ADD_TIME,VALID_TIME,PAYMENT_TIME,INVOICE_TIME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper="between"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">创建时间</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="ADD_TIME" placeholder="请选择日期" style="" alias="T1" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">生效时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="hidden" name="VALID_TIME" placeholder="" style="" alias="T1" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">付款时间</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="PAYMENT_TIME" placeholder="" style="" alias="T1" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">收票时间</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="INVOICE_TIME" placeholder="" style="" alias="T1" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">采购单号</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="19" name="BUYORDER_NO" placeholder="请输入"
                        style="" alias="T1" jdbctype="" data="" datatype="" oper="LIKE"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">退货预警</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" name="IS_RETURN_EARLY_WARN" placeholder="默认全部" style=""
                        alias="T1" jdbctype="VARCHAR"
                        data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]"
                        datatype="JSON" oper=""></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">是否特麦帮</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="IS_SPECIAL" placeholder="" style="" alias="if(TSS.SPECIAL_SALES_ID is null, '否', '是')" jdbctype="" data="[{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;是&quot;},{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;否&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>

    </form>
    <hr class="layui-border-blue">
        <div class="btn-group   bd-highlight" id="tableButton">
            <button type="table" class="layui-btn" url="/buyorderExpense/edit.do" opentype="PARENT" windowname="新增采购费用订单">新增采购费用订单</button>
        </div>

    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th item_name="IS_RETURN_EARLY_WARN" name="IS_RETURN_EARLY_WARN" body="td-spanlink" url="" windowname="" datatype="" data="" style="text-align: center" head="18"></th>
            <th item_name="BUYORDER_EXPENSE_NO" name="BUYORDER_EXPENSE_NO" body="td-spanlink" url="/buyorderExpense/details.do?buyorderExpenseId=${BUYORDER_EXPENSE_ID}" opentype="PARENT" windowname="费用订单详情" datatype="" data="" style="min-width:200px;
word-break: break-all;position: sticky;" head="18">采购费用单号</th>

            <th item_name="BUYORDER_NO" name="BUYORDER_NO" body="td-spanlink" url="/order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=${BUYORDER_ID}" opentype="PARENT" windowname="采购订单详情" datatype="" data="" style="min-width:200px;
word-break: break-all;position: sticky;" head="18">采购单号</th>

            <th item_name="TOTAL_AMOUNT" name="TOTAL_AMOUNT" body="td-text" url="" opentype="MODEL" windowname="" datatype=""
                data="" style="text-align:right" head="18" jdbctype="NUMBER">总额
            </th>
            <th item_name="PAYMENT_AMOUNT" name="PAYMENT_AMOUNT" body="td-text" url="" opentype="MODEL" windowname=""
                datatype="" data="" style="text-align:right" head="18" jdbctype="NUMBER">已付款金额
            </th>
            <th item_name="PAYMENT_STATUS_STR" name="PAYMENT_STATUS_STR" body="td-text" order="0" url="" opentype="MODEL"
                windowname="" datatype="" data="" style="" head="18">付款状态
            </th>
            <th item_name="INVOICE_STATUS_STR" name="INVOICE_STATUS_STR" body="td-text" order="0" url="" opentype="MODEL"
                windowname="" datatype="" data="" style="" head="18">收票状态
            </th>
            <th item_name="STATUS_STR" name="STATUS_STR" body="td-text" order="0" url="" opentype="MODEL" windowname=""
                datatype="" data="" style="" head="18">订单状态
            </th>
            <th item_name="CREATOR_NAME" name="CREATOR_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname=""
                datatype="" data="" style="" head="18">创建人
            </th>
            <th item_name="ORG_NAME" name="ORG_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname=""
                datatype="" data="" style="" head="18">部门
            </th>
            <th item_name="INVOICE_TYPE_STR" name="INVOICE_TYPE_STR" body="td-text" order="0" url="" opentype="MODEL"
                windowname="" datatype="" data="" style="" head="18">票种
            </th>
            <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname=""
                datatype="" data="" style="" head="18">创建时间
            </th>
            <th item_name="VALID_TIME" name="VALID_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname=""
                datatype="" data="" style="" head="18">生效时间
            </th>
            <th item_name="IS_SPECIAL" name="IS_SPECIAL" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">是否特麦帮</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100"> express:
                <pre id="express" class="layui-code" orderby="order by T1.ADD_TIME DESC"
                     groupby="GROUP BY T1.BUYORDER_EXPENSE_ID">StringBuilder sql=new StringBuilder("
SELECT
	T1.BUYORDER_EXPENSE_ID,
	T1.BUYORDER_EXPENSE_NO,
	T1.BUYORDER_NO,
    T1.BUYORDER_ID,
	BED.TOTAL_AMOUNT,
	IFNULL( T4.PAYMENT_AMOUNT, 0 ) PAYMENT_AMOUNT,
CASE
		T1.PAYMENT_STATUS
		WHEN 0 THEN
		'未付款'
		WHEN 1 THEN
		'部分付款' ELSE '全部付款'
	END PAYMENT_STATUS_STR,
CASE
		T1.INVOICE_STATUS
		WHEN 0 THEN
		'未收票'
		WHEN 1 THEN
		'部分收票' ELSE '全部收票'
	END INVOICE_STATUS_STR,
CASE
		T1.`STATUS`
		WHEN 0 THEN
		'待确认'
		WHEN '1' THEN
		'进行中'
		WHEN '2' THEN
		'已完结'
		WHEN '3' THEN
		'已关闭' ELSE ''
	END STATUS_STR,
	T1.CREATOR_NAME,
	T2.ORG_NAME,
	T3.TITLE INVOICE_TYPE_STR,
	DATE_FORMAT( T1.ADD_TIME, '%Y-%c-%d %H:%i:%s' ) ADD_TIME,
	DATE_FORMAT( T1.VALID_TIME, '%Y-%c-%d %H:%i:%s' ) VALID_TIME,
    if(TSS.SPECIAL_SALES_ID is null, '否', '是') as IS_SPECIAL,
    T1.IS_RETURN_EARLY_WARN
FROM
	T_BUYORDER_EXPENSE T1
	LEFT JOIN T_BUYORDER_EXPENSE_DETAIL BED ON T1.BUYORDER_EXPENSE_ID = BED.BUYORDER_EXPENSE_ID
	AND BED.IS_DELETE = 0
    LEFT JOIN T_SPECIAL_SALES TSS ON TSS.RELATE_ID = T1.BUYORDER_EXPENSE_ID AND TSS.RELATE_TYPE = 5 AND TSS.IS_DELETE = 0
	LEFT JOIN T_ORGANIZATION T2 ON BED.ORG_ID = T2.ORG_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION T3 ON BED.INVOICE_TYPE = T3.SYS_OPTION_DEFINITION_ID
	LEFT JOIN (
	SELECT
		B.RELATED_ID,
		COALESCE ( SUM( ABS( IFNULL( A.AMOUNT, 0 ))), 0 ) PAYMENT_AMOUNT
	FROM
		T_CAPITAL_BILL A
		LEFT JOIN T_CAPITAL_BILL_DETAIL B ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
	WHERE
		A.TRADER_TYPE IN ( 2, 5 )
		AND B.ORDER_TYPE = 4
	GROUP BY
		B.RELATED_ID
	) T4 ON T1.BUYORDER_EXPENSE_ID = T4.RELATED_ID
       ");

if ( isNotBlank("SKU") || isNotBlank("GOODS_NAME") || isNotBlank("HAVE_STOCK_MANAGE") ||  isNotBlank("BELONG")) {
      sql.append ( " LEFT JOIN T_BUYORDER_EXPENSE_ITEM BEI ON T1.BUYORDER_EXPENSE_ID = BEI.BUYORDER_EXPENSE_ID
	AND BEI.IS_DELETE = 0
	LEFT JOIN T_BUYORDER_EXPENSE_ITEM_DETAIL BEID ON BEI.BUYORDER_EXPENSE_ITEM_ID = BEID.BUYORDER_EXPENSE_ITEM_ID
	AND BEID.IS_DELETE = 0 " );
      }

if ( isNotBlank("BELONG")) {
	sql.append ( " LEFT JOIN V_CORE_SKU T5 ON BEI.GOODS_ID = T5.SKU_ID
	LEFT JOIN V_CORE_SPU T6 ON T5.SPU_ID = T6.SPU_ID  " );
}

          sql.append ( " WHERE T1.IS_DELETE = 0 and T1.BUSINESS_TYPE = 1 " );
          list =search(sql);
return list;</pre>
                count:
                <pre id="count" class="layui-code"></pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <script>

        $(".dropdown-menu").each(function () {
            var $tr = $(this).parents("tr:first");
            var bdStatus = $tr.find('td').eq(11);
            var businessChanceStatus = $tr.find('td').eq(13);
            if ($.trim(bdStatus.html()) == "可跟进" && $.trim(businessChanceStatus.html()) == '跟进中') {
            } else {
                $(this).find('.dropdown-item').eq(1).hide();
            }
        })
        function afterAllDataLoad() {
            $("td[item_name=IS_RETURN_EARLY_WARN]").each(function (e) {
                if($(this).text().indexOf("1") >= 0){
                    $(this).html("<img src='/static/images/return.png' style='width: 20%;height: auto;'>")
                } else{
                    $(this).html("")
                }
            })
        }
    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>