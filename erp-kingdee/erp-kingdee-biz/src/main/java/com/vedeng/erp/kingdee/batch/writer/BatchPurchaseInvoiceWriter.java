package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeePayCommonAndInvoiceDto;
import com.vedeng.erp.kingdee.dto.PurchaseVatPlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.PurchaseVatSpecialInvoiceDto;
import com.vedeng.erp.kingdee.service.KingDeePurchaseInvoicePlainApiService;
import com.vedeng.erp.kingdee.service.KingDeePurchaseInvoiceSpecialApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购应付单推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchPurchaseInvoiceWriter extends BaseWriter<KingDeePayCommonAndInvoiceDto> {

    @Autowired
    private KingDeePurchaseInvoiceSpecialApiService kingDeePurchaseInvoiceSpecialApiService;

    @Autowired
    private KingDeePurchaseInvoicePlainApiService kingDeePurchaseInvoicePlainApiService;


    @Override
    public void doWrite(KingDeePayCommonAndInvoiceDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("采购发票票发票推送：{}", JSON.toJSONString(item));

        if (Objects.nonNull(item.getPurchaseVatSpecialInvoiceDto())) {
            specialWrite(item.getPurchaseVatSpecialInvoiceDto());
            return;
        }
        if (Objects.nonNull(item.getPurchaseVatPlainInvoiceDto())) {
            plainWrite(item.getPurchaseVatPlainInvoiceDto());
        }


    }

    /**
     * 专票
     * @param dto
     */
    private void specialWrite(PurchaseVatSpecialInvoiceDto dto) {
        log.info("采购专票入参-->"+JSON.toJSON(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.savePurchaseInvoiceSpecial);
        kingDeePurchaseInvoiceSpecialApiService.register(dto,true);
    }


    /**
     * 普票
     * @param dto
     */
    private void plainWrite(PurchaseVatPlainInvoiceDto dto) {
        log.info("采购普票入参-->"+JSON.toJSON(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.savePurchaseInvoicePlain);
        kingDeePurchaseInvoicePlainApiService.register(dto,true);
    }
}
