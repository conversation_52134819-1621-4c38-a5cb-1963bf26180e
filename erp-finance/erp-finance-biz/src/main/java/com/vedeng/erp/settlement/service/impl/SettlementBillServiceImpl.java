package com.vedeng.erp.settlement.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.enums.BusinessSourceTypeEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.DiffUtils;
import com.vedeng.erp.finance.cmd.SettlementBillCreateCmd;
import com.vedeng.erp.finance.cmd.SettlementBillDeleteCmd;
import com.vedeng.erp.finance.cmd.SettlementBillQueryCmd;
import com.vedeng.erp.finance.cmd.SettlementBillSettleCmd;
import com.vedeng.erp.settlement.domain.dto.SettlementBillDto;
import com.vedeng.erp.settlement.domain.dto.SettlementBillItemDto;
import com.vedeng.erp.settlement.domain.entity.SettlementBillEntity;
import com.vedeng.erp.settlement.domain.entity.SettlementBillItemEntity;
import com.vedeng.erp.settlement.manager.AbstractSettlementHandle;
import com.vedeng.erp.settlement.mapper.SettlementBillItemMapper;
import com.vedeng.erp.settlement.mapper.SettlementBillMapper;
import com.vedeng.erp.settlement.mapstruct.SettlementBillConvertor;
import com.vedeng.erp.settlement.mapstruct.SettlementBillItemConvertor;
import com.vedeng.erp.settlement.service.SettlementBillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 结算单
 * @date 2023/11/23 14:32
 */
@Service
@Slf4j
public class SettlementBillServiceImpl implements SettlementBillService {

    @Autowired
    SettlementBillConvertor settlementBillConvertor;
    @Autowired
    SettlementBillItemConvertor settlementBillItemConvertor;
    @Autowired
    SettlementBillMapper settlementBillMapper;
    @Autowired
    SettlementBillItemMapper settlementBillItemMapper;

    @Autowired
    @Qualifier("buyOrderAfterSaleSettlementHandle")
    AbstractSettlementHandle buyOrderAfterSaleSettlementHandle;
    @Autowired
    @Qualifier("buyOrderRebateChargeSettlementHandle")
    AbstractSettlementHandle buyOrderRebateChargeSettlementHandle;
    @Autowired
    @Qualifier("buyOrderSettlementHandle")
    AbstractSettlementHandle buyOrderSettlementHandle;


    private final EnumMap<BusinessSourceTypeEnum, Consumer<SettlementBillSettleCmd>> settlementDispatcherMapSettle
            = new EnumMap<>(BusinessSourceTypeEnum.class);
    private final EnumMap<BusinessSourceTypeEnum, Function<SettlementBillCreateCmd, Integer>> settlementDispatcherMapCreate
            = new EnumMap<>(BusinessSourceTypeEnum.class);

    /**
     * 初始化结算逻辑调用处理 map
     */
    @PostConstruct
    private void settlementDispatcherMapSettlement() {
        settlementDispatcherMapCreate.put(BusinessSourceTypeEnum.buyOrder, cmd -> buyOrderSettlementHandle.create(cmd));
        settlementDispatcherMapCreate.put(BusinessSourceTypeEnum.buyOrderAfterSale, cmd -> buyOrderAfterSaleSettlementHandle.create(cmd));
        settlementDispatcherMapCreate.put(BusinessSourceTypeEnum.buyOrderRebateChargeApply, cmd -> buyOrderRebateChargeSettlementHandle.create(cmd));

        settlementDispatcherMapSettle.put(BusinessSourceTypeEnum.buyOrder, cmd -> buyOrderSettlementHandle.settle(cmd));
        settlementDispatcherMapSettle.put(BusinessSourceTypeEnum.buyOrderAfterSale, cmd -> buyOrderAfterSaleSettlementHandle.settle(cmd));
        settlementDispatcherMapSettle.put(BusinessSourceTypeEnum.buyOrderRebateChargeApply, cmd -> buyOrderRebateChargeSettlementHandle.settle(cmd));

    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Integer create(SettlementBillCreateCmd settlementBillCreateCmd) {
        Integer id;
        try {
            Function<SettlementBillCreateCmd, Integer> function = settlementDispatcherMapCreate.get(settlementBillCreateCmd.getSourceTypeEnum());
            id = function.apply(settlementBillCreateCmd);
        } catch (Exception e) {
            log.error("创建结算单失败", e);
            throw new ServiceException("创建结算单失败");
        }
        return id;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void createAlsoSettlement(SettlementBillCreateCmd settlementBillCreateCmd) {
        Integer settlementBillId = this.create(settlementBillCreateCmd);
        if (settlementBillId == null) {
            log.info("结算单创建并生成无数据:{}", JSON.toJSONString(settlementBillCreateCmd));
            return;
        }
        SettlementBillSettleCmd build = SettlementBillSettleCmd
                .builder()
                .sourceTypeEnum(settlementBillCreateCmd.getSourceTypeEnum())
                .settlementBillId(settlementBillId)
                .settlementTypeEnum(settlementBillCreateCmd.getSettlementTypeEnum())
                .build();
        this.settlement(build);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void settlement(SettlementBillSettleCmd settlementBillSettleCmd) {
        try {
            Consumer<SettlementBillSettleCmd> consumer = settlementDispatcherMapSettle.get(settlementBillSettleCmd.getSourceTypeEnum());
            consumer.accept(settlementBillSettleCmd);
        } catch (Exception e) {
            log.error("结算失败", e);
            throw new ServiceException("结算失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void modify(SettlementBillDto settlementBillDto) {
        log.info("结算单:{}", JSON.toJSONString(settlementBillDto));
        // 修改结算单
        settlementBillMapper.updateByPrimaryKeySelective(settlementBillConvertor.toEntity(settlementBillDto));

        // 修改结算明细
        List<SettlementBillItemEntity> settlementBillItemListFromDb = settlementBillItemMapper.findBySettleBillId(settlementBillDto.getSettleBillId());
        log.info("修改之前数据库中存在的结算明细数据{}", JSON.toJSONString(settlementBillItemListFromDb));

        List<SettlementBillItemEntity> settlementBillItemList = settlementBillItemConvertor.toEntity(settlementBillDto.getSettlementBillItemDtoList());
        settlementBillItemList.forEach(item -> item.setSettleBillId(settlementBillDto.getSettleBillId()));
        log.info("修改时传入的结算明细数据{}", JSON.toJSONString(settlementBillItemList));

        List<DiffUtils.ContentValue<Integer, SettlementBillItemEntity>> settlementBillItemDtoFromDb =
                settlementBillItemListFromDb.stream().map(item -> DiffUtils.ContentValue.of(item.getSettleItemBillId(), item)).collect(Collectors.toList());
        List<DiffUtils.ContentValue<Integer, SettlementBillItemEntity>> settlementBillItemDtoFromParams =
                settlementBillItemList.stream().map(item -> DiffUtils.ContentValue.of(item.getSettleItemBillId(), item)).collect(Collectors.toList());

        Map<DiffUtils.Type, Consumer<List<SettlementBillItemEntity>>> actionMap = new EnumMap<>(DiffUtils.Type.class);
        actionMap.put(DiffUtils.Type.INSERT, (k) -> settlementBillItemMapper.batchInsert(k));
        actionMap.put(DiffUtils.Type.UPDATE, (k) -> settlementBillItemMapper.updateBatchSelective(k));
        actionMap.put(DiffUtils.Type.DELETE, this::deleteSettlementBillItem);
        DiffUtils.doDiff(settlementBillItemDtoFromDb, settlementBillItemDtoFromParams, actionMap);
    }

    /**
     * 删除结算单明细
     *
     * @param settlementBillItemEntityList 结算单明细
     */
    private void deleteSettlementBillItem(List<SettlementBillItemEntity> settlementBillItemEntityList) {
        List<Integer> ids = settlementBillItemEntityList.stream().map(SettlementBillItemEntity::getSettleItemBillId).collect(Collectors.toList());
        settlementBillItemMapper.deleteBySettleItemBillIdIn(ids);
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void delete(SettlementBillDeleteCmd cmd) {
        settlementBillMapper.updateIsDeleteBySettleBillId(ErpConstant.T, cmd.getSettlementBillId());
    }


    @Override
    public Integer getSettlementIdByBusiness(SettlementBillQueryCmd settlementBillQueryCmd) {
        SettlementBillEntity settleBill = settlementBillMapper.findByBusinessSourceTypeIdAndSourceType(settlementBillQueryCmd.getBusinessSourceId(),
                settlementBillQueryCmd.getBusinessSourceTypeEnum().getCode());
        if (settleBill == null) {
            return null;
        }
        return settleBill.getSettleBillId();
    }

    @Override
    public SettlementBillDto getSettlementByBusiness(SettlementBillQueryCmd settlementBillQueryCmd) {
        SettlementBillEntity settleBill = settlementBillMapper.findByBusinessSourceTypeIdAndSourceType(settlementBillQueryCmd.getBusinessSourceId(),
                settlementBillQueryCmd.getBusinessSourceTypeEnum().getCode());
        if (settleBill == null) {
            return null;
        }
        SettlementBillDto settlementBillDto = settlementBillConvertor.toDto(settleBill);
        List<SettlementBillItemEntity> bySettleBillId = settlementBillItemMapper.findBySettleBillId(settleBill.getSettleBillId());
        List<SettlementBillItemDto> settlementBillItemDtos = settlementBillItemConvertor.toDto(bySettleBillId);
        settlementBillDto.setSettlementBillItemDtoList(settlementBillItemDtos);
        return settlementBillDto;
    }

}
