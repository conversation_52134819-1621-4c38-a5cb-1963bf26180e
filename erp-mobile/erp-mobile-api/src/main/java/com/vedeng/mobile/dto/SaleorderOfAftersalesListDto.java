package com.vedeng.mobile.dto;

import lombok.Data;

import java.util.List;


/**
 * 销售订单对应的售后订单列表查询类
 */
@Data
public class SaleorderOfAftersalesListDto {

    /**
     * 售后id
     */
    private Integer afterSalesId;
    /**
     * 售后单号
     */
    private String afterSalesNo;
    /**
     * 售后类型
     */
    private Integer type;
    /**
     * 售后类型名称
     */
    private String typeName;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单id
     */
    private Integer orderId;
    /**
     * 售后状态0待确认1进行中2已完结3已关闭
     */
    private Integer afterSalesStatus;
    /**
     * 创建时间
     */
    private String addTime;
    /**
     * 售后原因
     */
    private Integer reason;
    /**
     * 售后原因名称
     */
    private String reasonName;

    /**
     * 售后商品集合
     */
    private List<SaleorderOfAftersalesGoodsListDto> afterSalesGoodsList;

}
