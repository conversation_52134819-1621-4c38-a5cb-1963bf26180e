package com.vedeng.goods.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Getter
public enum GoodsSignContractModeEnum {

    NONE(0, "无", "无",""),

    NOT_SIGN(1, "未签约", "未签约","签约模式=无签约"),

    SELL_BY_AGENT(2, "代销", "签约模式为代销","签约模式=代销"),

    DISTRIBUTE(3, "经销", "签约模式为经销，无授权区域","签约模式=经销"),

    AGENT(4, "代理", "签约模式为代理，有一定的授权区域","签约模式=代理"),

    UNIQUE(5, "独家代理", "签约模式为代理，有一定的授权区域，且仅贝登可以销售","签约模式=独家代理"),
    LOWCLASS(6,"低等级商品","前台做简单展示","")
    ;

    private Integer code;
    private String name;
    private String description;
    private String rule;

    GoodsSignContractModeEnum(Integer code, String name, String description, String rule) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.rule = rule;
    }

    public static GoodsSignContractModeEnum getByCode(Integer code) {
        return Arrays.stream(values()).filter(item -> item.getCode().equals(code)).findFirst().orElse(NONE);
    }
}
