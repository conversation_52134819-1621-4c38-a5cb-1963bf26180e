package com.vedeng.order.enums;

import lombok.Getter;

import java.util.*;

/**
 * 报价预警模式
 *
 * <AUTHOR> [<EMAIL>]
 * @since ERP_LV_2020_38
 */
@Getter
public enum QuotedAlarmModeEnum {

    /**
     * 接触报价预警
     */
    OFF(0, "无预警", new LinkedList<>()) {
        @Override
        public boolean allowLevel(Integer level) {
            return false;
        }
    },

    /**
     * 销售端触发的报价预警
     */
    SALESMAN_MODE(1, "销售端触发预警模式", new LinkedList<>(Arrays.asList(QuotedAlarmLevelEnum.NONE, QuotedAlarmLevelEnum.LEVEL_TWO, QuotedAlarmLevelEnum.LEVEL_ONE))),

    /**
     * 供应链端触发的报价预警
     */
    PURCHASER_MODE(2, "供应链端触发预警模式", new LinkedList<>((Arrays.asList(QuotedAlarmLevelEnum.NONE, QuotedAlarmLevelEnum.LEVEL_THREE, QuotedAlarmLevelEnum.LEVEL_TWO, QuotedAlarmLevelEnum.LEVEL_ONE))));

    private Integer mode;
    private String message;
    private LinkedList<QuotedAlarmLevelEnum> allowedLevels;

    /**
     * Constructor with mode and levelList.
     *
     * @param mode
     * @param levelList
     */
    QuotedAlarmModeEnum(Integer mode, String message, LinkedList<QuotedAlarmLevelEnum> levelList) {
        this.mode = mode;
        this.message = message;
        this.allowedLevels = levelList;
    }


    public static QuotedAlarmModeEnum getOrDefault(Integer mode) {
        for (QuotedAlarmModeEnum modeEnum : values()) {
            if (Objects.equals(mode, modeEnum.getMode())) {
                return modeEnum;
            }
        }
        return OFF;
    }




    /**
     * Checks  level.
     *
     * @param level
     */
    public boolean allowLevel(Integer level) {
        return allowedLevels.stream().anyMatch(item -> Objects.equals(level, item.getLevel()));
    }
}
