package com.vedeng.erp.buyorder.common.constant;

public class BuyOrderModifyStatus {

    public final static String WAIT_VALID = "待审核";
    public final static String VALIDATING = "审核中";
    public final static String COMPLETED = "已完成";


    public final static String START_AUDIT = "开始审核";
    public final static String APPLICANT = "申请人";
    public final static String SUPERVISOR_AUDIT = "主管审核";
    public final static String FINISHED = "审核完成";
    public final static String REJECT = "驳回";

    public enum Son {

        /**
         * 开始
         */
        NODE_START("开始"),
         /**
         * 申请人
         */
        NODE_APPLY("申请人"),
         /**
         * 主管审核
         */
        NODE_MANAGE("主管审核"),
         /**
         * 驳回
         */
        NODE_REJECT("驳回"),
         /**
         * 审核完成
         */
        NODE_OVER("审核完成");


        private String nodeName;

        Son(String name) {
            this.nodeName = name;
        }

        public String getNodeName() {
            return nodeName;
        }

        public void setNodeName(String nodeName) {
            this.nodeName = nodeName;
        }
    }

}
