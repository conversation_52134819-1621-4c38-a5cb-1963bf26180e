package com.wms.model.po;

import java.util.Date;

public class WmsLogicalOrdergoods {
    /** 主键id  LOGICAL_ORDER_GOODS_ID **/
    private Integer logicalOrderGoodsId;

    /** 关联订单商品表id  RELATED_ID **/
    private Integer relatedId;

    /** 业务类型 业务类型 0销售单 1售后单 2库存转移单 3.采购入库 4.库存调整单 5.采购退货单 6.采购换货单 7外借单 8盘盈入库单 9报废出库单  OPERATE_TYPE **/
    private Integer operateType;

    /**
     * SKU
     */
    private String sku;

    /** 商品id  GOODS_ID **/
    private Integer goodsId;

    /** 数量  NUM **/
    private Integer num;

    /** 逻辑仓id  LOGICAL_WAREHOUSE_ID **/
    private Integer logicalWarehouseId;

    /** 占用数量  OCCUPY_NUM **/
    private Integer occupyNum;

    /** 发货数量  DELIVERY_NUM **/
    private Integer deliveryNum;

    /** 收货数量  ARRIVAL_NUM **/
    private Integer arrivalNum;

    /** 创建时间  ADD_TIME **/
    private Date addTime;

    /** 更新时间  MODE_TIME **/
    private Date modeTime;

    /** 是否删除 0否 1是  IS_DELETE **/
    private Integer isDelete;

    /** 创建人  CREATOR **/
    private Integer creator;

    /** 更新人  UPDATER **/
    private Integer updater;

    /**
     * 是否专项发货
     */
    private Integer specialDelivery;

    /**   主键id  LOGICAL_ORDER_GOODS_ID   **/
    public Integer getLogicalOrderGoodsId() {
        return logicalOrderGoodsId;
    }

    /**   主键id  LOGICAL_ORDER_GOODS_ID   **/
    public void setLogicalOrderGoodsId(Integer logicalOrderGoodsId) {
        this.logicalOrderGoodsId = logicalOrderGoodsId;
    }

    /**   关联订单商品表id  RELATED_ID   **/
    public Integer getRelatedId() {
        return relatedId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    /**   关联订单商品表id  RELATED_ID   **/
    public void setRelatedId(Integer relatedId) {
        this.relatedId = relatedId;
    }

    /**   业务类型 0销售单 1售后单 2库存转移单  OPERATE_TYPE   **/
    public Integer getOperateType() {
        return operateType;
    }

    /**   业务类型 0销售单 1售后单 2库存转移单  OPERATE_TYPE   **/
    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    /**   商品id  GOODS_ID   **/
    public Integer getGoodsId() {
        return goodsId;
    }

    /**   商品id  GOODS_ID   **/
    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    /**   数量  NUM   **/
    public Integer getNum() {
        return num;
    }

    /**   数量  NUM   **/
    public void setNum(Integer num) {
        this.num = num;
    }

    /**   逻辑仓id  LOGICAL_WAREHOUSE_ID   **/
    public Integer getLogicalWarehouseId() {
        return logicalWarehouseId;
    }

    /**   逻辑仓id  LOGICAL_WAREHOUSE_ID   **/
    public void setLogicalWarehouseId(Integer logicalWarehouseId) {
        this.logicalWarehouseId = logicalWarehouseId;
    }

    /**   占用数量  OCCUPY_NUM   **/
    public Integer getOccupyNum() {
        return occupyNum;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**   占用数量  OCCUPY_NUM   **/
    public void setOccupyNum(Integer occupyNum) {
        this.occupyNum = occupyNum;
    }

    /**   发货数量  DELIVERY_NUM   **/
    public Integer getDeliveryNum() {
        return deliveryNum;
    }

    /**   发货数量  DELIVERY_NUM   **/
    public void setDeliveryNum(Integer deliveryNum) {
        this.deliveryNum = deliveryNum;
    }

    /**   收货数量  ARRIVAL_NUM   **/
    public Integer getArrivalNum() {
        return arrivalNum;
    }

    /**   收货数量  ARRIVAL_NUM   **/
    public void setArrivalNum(Integer arrivalNum) {
        this.arrivalNum = arrivalNum;
    }

    /**   创建时间  ADD_TIME   **/
    public Date getAddTime() {
        return addTime;
    }

    /**   创建时间  ADD_TIME   **/
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**   更新时间  MODE_TIME   **/
    public Date getModeTime() {
        return modeTime;
    }

    /**   更新时间  MODE_TIME   **/
    public void setModeTime(Date modeTime) {
        this.modeTime = modeTime;
    }

    /**   创建人  CREATOR   **/
    public Integer getCreator() {
        return creator;
    }

    /**   创建人  CREATOR   **/
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**   更新人  UPDATER   **/
    public Integer getUpdater() {
        return updater;
    }

    /**   更新人  UPDATER   **/
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public Integer getSpecialDelivery() {
        return specialDelivery;
    }

    public void setSpecialDelivery(Integer specialDelivery) {
        this.specialDelivery = specialDelivery;
    }

    @Override
    public String toString() {
        return "WmsLogicalOrdergoods{" +
                "relatedId=" + relatedId +
                ", operateType=" + operateType +
                ", goodsId=" + goodsId +
                ", sku='" + sku + '\'' +
                ", num=" + num +
                ", logicalWarehouseId=" + logicalWarehouseId +
                ", occupyNum=" + occupyNum +
                ", deliveryNum=" + deliveryNum +
                ", arrivalNum=" + arrivalNum +
                ", isDelete=" + isDelete +
                '}';
    }
}
