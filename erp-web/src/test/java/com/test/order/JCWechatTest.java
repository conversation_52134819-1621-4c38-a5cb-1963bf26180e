package com.test.order;

import com.test.stock.SpeicalWarehouseProcessor;
import com.vedeng.aftersales.service.impl.WebAccountServiceImpl;
import com.vedeng.invi.api.dto.OrgDTO;
import com.vedeng.wechat.JcWeChatEnum;
import com.vedeng.wechat.model.JcWeChatModel;
import com.vedeng.wechat.service.impl.JcWeChatArrServiceImpl;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName JCWechatTest.java
 * @Description TODO
 * @createTime 2021年03月08日 10:25:00
 */
public class JCWechatTest  extends SpeicalWarehouseProcessor {

    @Autowired
    private JcWeChatArrServiceImpl jcWeChatArrService;

    @Autowired
    private WebAccountServiceImpl webAccountService;

    @Test
    public void test(){
        JcWeChatModel jcWeChatModel = new JcWeChatModel(108616);
        jcWeChatModel.setExpressId(84395);
        jcWeChatArrService.sendTemplateMsgJcorder(jcWeChatModel, JcWeChatEnum.SAVE_ORDER,false);
    }

    @Test
    public void test1(){
        List<OrgDTO> registerRegionalMall = webAccountService.getRegisterRegionalMall();
        System.out.println(registerRegionalMall);
    }
}
