<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.RegionMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.RegionEntity">
    <!--@mbg.generated-->
    <!--@Table T_REGION-->
    <id column="REGION_ID" jdbcType="BIGINT" property="regionId" />
    <result column="PARENT_ID" jdbcType="BIGINT" property="parentId" />
    <result column="REGION_NAME" jdbcType="VARCHAR" property="regionName" />
    <result column="REGION_TYPE" jdbcType="INTEGER" property="regionType" />
    <result column="AGENCY_ID" jdbcType="INTEGER" property="agencyId" />
    <result column="REGION_FULL_NAME" jdbcType="VARCHAR" property="regionFullName" />
    <result column="REGION_CODE" jdbcType="VARCHAR" property="regionCode" />
    <result column="IS_DELETED" jdbcType="BOOLEAN" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    REGION_ID, PARENT_ID, REGION_NAME, REGION_TYPE, AGENCY_ID,
    REGION_FULL_NAME, REGION_CODE, IS_DELETED
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_REGION
    where REGION_ID = #{regionId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_REGION
    where REGION_ID = #{regionId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.system.domain.entity.RegionEntity">
    <!--@mbg.generated-->
    insert into T_REGION (REGION_ID, PARENT_ID, REGION_NAME, 
      REGION_TYPE, AGENCY_ID, CREATE_TIME, 
      UPDATE_TIME, REGION_FULL_NAME, REGION_CODE,
      IS_DELETED)
    values (#{regionId,jdbcType=BIGINT}, #{parentId,jdbcType=BIGINT}, #{regionName,jdbcType=VARCHAR}, 
      #{regionType,jdbcType=INTEGER}, #{agencyId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{regionFullName,jdbcType=VARCHAR}, #{regionCode,jdbcType=VARCHAR},
      #{isDeleted,jdbcType=BOOLEAN})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.system.domain.entity.RegionEntity">
    <!--@mbg.generated-->
    insert into T_REGION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="regionId != null">
        REGION_ID,
      </if>
      <if test="parentId != null">
        PARENT_ID,
      </if>
      <if test="regionName != null">
        REGION_NAME,
      </if>
      <if test="regionType != null">
        REGION_TYPE,
      </if>
      <if test="agencyId != null">
        AGENCY_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="regionFullName != null">
        REGION_FULL_NAME,
      </if>
      <if test="regionCode != null">
        REGION_CODE,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="regionId != null">
        #{regionId,jdbcType=BIGINT},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="regionName != null">
        #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="regionType != null">
        #{regionType,jdbcType=BOOLEAN},
      </if>
      <if test="agencyId != null">
        #{agencyId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="regionFullName != null">
        #{regionFullName,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.RegionEntity">
    <!--@mbg.generated-->
    update T_REGION
    <set>
      <if test="parentId != null">
        PARENT_ID = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="regionName != null">
        REGION_NAME = #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="regionType != null">
        REGION_TYPE = #{regionType,jdbcType=INTEGER},
      </if>
      <if test="agencyId != null">
        AGENCY_ID = #{agencyId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="regionFullName != null">
        REGION_FULL_NAME = #{regionFullName,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        REGION_CODE = #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=BOOLEAN},
      </if>
    </set>
    where REGION_ID = #{regionId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.RegionEntity">
    <!--@mbg.generated-->
    update T_REGION
    set PARENT_ID = #{parentId,jdbcType=BIGINT},
      REGION_NAME = #{regionName,jdbcType=VARCHAR},
      REGION_TYPE = #{regionType,jdbcType=INTEGER},
      AGENCY_ID = #{agencyId,jdbcType=INTEGER},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      REGION_FULL_NAME = #{regionFullName,jdbcType=VARCHAR},
      REGION_CODE = #{regionCode,jdbcType=VARCHAR},
      IS_DELETED = #{isDeleted,jdbcType=BOOLEAN}
    where REGION_ID = #{regionId,jdbcType=BIGINT}
  </update>

  <select id="selectByRegionId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_REGION
    where REGION_ID=#{regionId,jdbcType=BIGINT}
  </select>

<!--auto generated by MybatisCodeHelper on 2022-07-13-->
  <select id="selectByParentIdAndNotDeleted" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_REGION
    where PARENT_ID=#{parentId,jdbcType=BIGINT} and IS_DELETED=0
  </select>

  <select id="getRegion" resultType="java.lang.String">
    SELECT CONCAT(t3.REGION_NAME, ' ', t2.REGION_NAME, ' ', t1.REGION_NAME) AS region

    FROM T_REGION AS t1
           JOIN
         T_REGION AS t2 ON t1.parent_id = t2.REGION_ID
           JOIN
         T_REGION AS t3 ON t2.parent_id = t3.REGION_ID
    WHERE t1.parent_id IS NOT NULL
      and t1.REGION_ID > 100000
      AND t2.parent_id IS NOT NULL
      AND t1.REGION_ID = #{regionId,jdbcType=INTEGER};
  </select>

  <select id="getAllRegion" resultType="com.vedeng.erp.system.dto.RegionDto">
    select * from T_REGION
    where IS_DELETED = 0 OR IS_DELETED IS NULL
  </select>


  <select id="getRegionProvinceByCityName" resultType="java.lang.String">
    SELECT t1.REGION_NAME

    FROM T_REGION AS t1
           JOIN
         T_REGION AS t2 ON t2.parent_id = t1.REGION_ID
    WHERE
      t2.REGION_ID > 100000
      AND t2.parent_id IS NOT NULL
      AND t2.REGION_NAME = #{name,jdbcType=VARCHAR}
    LIMIT 1
  </select>

  <select id="getProvinceIdByName" resultType="java.lang.Integer">
    SELECT REGION_ID
    FROM T_REGION
    WHERE REGION_NAME = #{provinceName,jdbcType=VARCHAR}
    AND REGION_ID > 100000
    AND REGION_TYPE = 1
    AND (IS_DELETED = 0 OR IS_DELETED IS NULL)
    LIMIT 1
  </select>

  <select id="getCityIdByNameAndProvince" resultType="java.lang.Integer">
    SELECT t1.REGION_ID
    FROM T_REGION t1
    JOIN T_REGION t2 ON t1.PARENT_ID = t2.REGION_ID
    WHERE t1.REGION_NAME = #{cityName,jdbcType=VARCHAR}
    AND t2.REGION_NAME = #{provinceName,jdbcType=VARCHAR}
    AND t2.REGION_ID > 100000
    AND t1.REGION_TYPE = 2
    AND (t1.IS_DELETED = 0 OR t1.IS_DELETED IS NULL)
    AND (t2.IS_DELETED = 0 OR t2.IS_DELETED IS NULL)
    LIMIT 1
  </select>

</mapper>