package com.vedeng.erp.settlement.service.impl.api;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.finance.domain.entity.CapitalBillSettlementTypeEntity;
import com.vedeng.erp.finance.dto.CapitalBillSettlementTypeDto;
import com.vedeng.erp.finance.mapper.CapitalBillSettlementTypeMapper;
import com.vedeng.erp.finance.service.CapitalBillSettlementTypeApiService;
import com.vedeng.erp.settlement.mapstruct.CapitalBillSettlementTypeConvertor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/8 13:56
 **/
@Service
@Slf4j
public class CapitalBillSettlementTypeApiServiceImpl implements CapitalBillSettlementTypeApiService {

    @Autowired
    private CapitalBillSettlementTypeConvertor capitalBillSettlementTypeConvertor;

    @Autowired
    private CapitalBillSettlementTypeMapper capitalBillSettlementTypeMapper;


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void add(CapitalBillSettlementTypeDto dto) {

        log.info("add 入参：{}", JSON.toJSONString(dto));
        if (Objects.isNull(dto.getCapitalBillId())) {
            throw new ServiceException("交易流水id不可为空");
        }

        CapitalBillSettlementTypeEntity capitalBillSettlementTypeEntity = capitalBillSettlementTypeConvertor.toEntity(dto);
        capitalBillSettlementTypeMapper.insertSelective(capitalBillSettlementTypeEntity);

    }
}
