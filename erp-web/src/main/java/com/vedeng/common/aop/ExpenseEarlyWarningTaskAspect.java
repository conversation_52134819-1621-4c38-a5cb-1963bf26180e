package com.vedeng.common.aop;

import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.buyorderexpense.web.api.BuyorderExpenseApi;
import com.vedeng.flash.dto.EarlyWarningTaskDto;
import com.vedeng.flash.service.warningtask.ExpeditingTicketsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description @see EarlyWarningTaskPoint
 * @date 2023/3/15 13:53
 **/
@Aspect
@Component
@Slf4j
public class ExpenseEarlyWarningTaskAspect {

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired
    private ExpeditingTicketsService expeditingTicketsService;


    @Pointcut("execution(* com.vedeng.erp.buyorderexpense.service.impl.BuyorderExpenseServiceImpl.doArrivalStatus(..))")
    public void expensePointcut() {
    }

    @Before("expensePointcut()")
    public void expenseEarlyWarning(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {

            Integer buyorderExpenseId = (Integer) args[0];

            if (Objects.nonNull(buyorderExpenseId) && buyorderExpenseId != 0) {

                BuyorderExpenseDto orderMainData = buyorderExpenseApiService.getOrderMainData(buyorderExpenseId);
                List<BuyorderExpenseItemDto> buyorderExpenseItemDtos = orderMainData.getBuyorderExpenseItemDtos();

                Integer arrivalAll = 2;
                if (!arrivalAll.equals(orderMainData.getArrivalStatus())) {
                    if (!CollectionUtils.isEmpty(buyorderExpenseItemDtos)) {
                        //遍历采购费用单 虚拟商品 商品明细
                        buyorderExpenseItemDtos.forEach(buyorderExpenseItem -> {
                            //虚拟商品  生成催票信息
                            EarlyWarningTaskDto earlyWarningTaskDto = new EarlyWarningTaskDto();
                            earlyWarningTaskDto.setRelateBusinessId(buyorderExpenseItem.getBuyorderExpenseItemId());
                            earlyWarningTaskDto.setUrgingTicketNum(buyorderExpenseItem.getNum());
                            earlyWarningTaskDto.setNotice(0);
                            log.info("采购费用单的虚拟商品  生成催票信息 BuyorderExpenseItemId:{},num:{}", buyorderExpenseId, buyorderExpenseItem.getNum());
                            expeditingTicketsService.create(earlyWarningTaskDto);
                        });
                    }
                }
            }

        }

    }
}
