package com.vedeng.aftersales.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Preconditions;
import com.rabbitmq.ErpMsgProducer;
import com.rabbitmq.MsgProducer;
import com.rabbitmq.OpMsgProducer;
import com.rabbitmq.RabbitConfig;
import com.rest.traderMsg.controller.WebAccountCertificateMsg;
import com.vedeng.aftersales.model.BDTraderCertificate;
import com.vedeng.aftersales.model.dto.JcAccountQueryDto;
import com.vedeng.aftersales.model.dto.TraderAssociatedLogDto;
import com.vedeng.aftersales.model.dto.TraderUnboundDto;
import com.vedeng.aftersales.model.vo.TraderAssociatedLogVo;
import com.vedeng.aftersales.service.WebAccountService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.*;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.common.util.*;
import com.vedeng.crm.api.enums.BelongPlatform;
import com.vedeng.invi.api.dto.OrgDTO;
import com.vedeng.invi.api.dto.OrgListDTO;
import com.vedeng.jc.api.dto.AccountResponse;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.model.VerifiesInfo;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.dao.*;
import com.vedeng.trader.enums.TraderAssociatedLogEnum;
import com.vedeng.trader.enums.TraderUnboundReasonEnum;
import com.vedeng.trader.model.*;
import com.vedeng.trader.model.vo.TraderAddressVo;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.WebAccountVo;
import com.vedeng.trader.service.TraderAssociatedLogService;
import com.vedeng.trader.service.TraderContactService;
import com.vedeng.trader.service.TraderCustomerService;
import net.sf.json.JSONObject;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service("webAccountService")
public class WebAccountServiceImpl extends BaseServiceimpl implements WebAccountService {
	public static Logger logger = LoggerFactory.getLogger(WebAccountServiceImpl.class);

	@Autowired
	@Qualifier("webAccountMapper")
	private WebAccountMapper webAccountMapper;
	
	@Autowired
	@Qualifier("userMapper")
	private UserMapper userMapper;

	@Autowired
	@Qualifier("traderCustomerMapper")
	private TraderCustomerMapper traderCustomerMapper;
	@Resource
	private TraderContactService traderContactService;

	@Autowired
	@Qualifier("userService")
	private UserService userService;

	@Autowired
	@Qualifier("traderMapper")
	private TraderMapper traderMapper;

	@Lazy
	@Autowired
	@Qualifier("saleorderService")
	private SaleorderService saleorderService;

	@Autowired
	private TraderCertificateMapper traderCertificateMapper;

	@Autowired
	private TraderCustomerService traderCustomerService;
	@Resource
	private TraderAssociatedLogService traderAssociatedLogService;

	@Autowired
	private WebAccountCertificateMapper certificateMapper;

	@Autowired
	private RTraderJUserMapper rTraderJUserMapper;

	@Autowired
	private RTraderJUserModifyRecordMapper rTraderJUserModifyRecordMapper;

	@Autowired
	private RestTemplate restTemplate;

	@Value("${invi_url}")
	private String inviUrl;

	@Value("${jcServiceDomain}")
	private String jcServiceDomain;

	@Autowired // 自动装载
	private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

	@Autowired
	private MsgProducer msgProducer;

	@Autowired
	ErpMsgProducer erpMsgProducer;

	@Value("${api_http}")
	protected String api_http;
	
	@Autowired
	private TrackStrategyFactory trackStrategyFactory;


	@Override
	public Map<String, Object> getWebAccount(WebAccount webAccount,Integer used) {
		Map<String, Object> returnMap = new HashMap<>();
		WebAccountVo webAccountVo = webAccountMapper.getWebAccount(webAccount);

		TraderCustomer customer = traderCustomerMapper.getTraderCustomerByTraderId(webAccountVo.getTraderId());

		Trader traderQuery = traderMapper.getTraderByTraderId(webAccountVo.getTraderId());

		TraderCustomerVo customerVo = null;
		List<TraderAddressVo> addressList = new ArrayList<>();
		List<TraderContact> contactlist = new ArrayList<>();
		if(null != webAccountVo && webAccountVo.getTraderId()!=null && webAccountVo.getTraderId()>0){
			try{
				TraderCustomerVo traderCustomerVo = new TraderCustomerVo();
				traderCustomerVo.setTraderAddressId(webAccountVo.getTraderAddressId());
				traderCustomerVo.setTraderContactId(webAccountVo.getTraderContactId());
				traderCustomerVo.setTraderId(webAccountVo.getTraderId());
				// 定义反序列化 数据格式
				final TypeReference<ResultInfo<Map<String, Object>>> TypeRef = new TypeReference<ResultInfo<Map<String, Object>>>() {};
				String url=httpUrl + "tradercustomer/geteditordergoodsstoreaccountinfo.htm";
				ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, traderCustomerVo,clientId,clientKey, TypeRef);
				Map<String, Object> map = (Map<String, Object>) result.getData();
				if(map.containsKey("customer")){
					net.sf.json.JSONObject json = net.sf.json.JSONObject.fromObject(map.get("customer"));
					customerVo = (TraderCustomerVo) json.toBean(json, TraderCustomerVo.class);
					if(null != customerVo){
						webAccountVo.setCompanyName(customerVo.getTraderName());
					}
				}
				if(map.containsKey("contact")){
					net.sf.json.JSONArray json = net.sf.json.JSONArray.fromObject(map.get("contact"));
					contactlist = (List<TraderContact>) json.toCollection(json, TraderContact.class);
				}
								
				if(map.containsKey("address")){
					net.sf.json.JSONArray json = net.sf.json.JSONArray.fromObject(map.get("address"));
					List<TraderAddress> traderAddressList = (List<TraderAddress>) json.toCollection(json, TraderAddress.class);
					if(null != traderAddressList && traderAddressList.size() > 0){
						for(TraderAddress address : traderAddressList){
							TraderAddressVo tav = new TraderAddressVo();
							tav.setTraderAddress(address);
							tav.setArea(getAddressByAreaId(address.getAreaId()));
							addressList.add(tav);
						}
					}
				}
			}catch (Exception e) {
				logger.error(Contant.ERROR_MSG, e);
			}
			
		}

		//获取关联客户公司日志
		List<TraderAssociatedLogVo> traderAssociatedLogList = traderAssociatedLogService.listByWebAccountId(webAccount.getErpAccountId());

		if(null != webAccountVo && null != webAccountVo.getMobile() && used == 1){
			//可能归属
			try {
				// 接口调用（优先查客户，客户没有查供应商库）
				String url = httpUrl + "tradercustomer/getcustomerinfobyphone.htm";
				TraderCustomerVo traderCustomer = new TraderCustomerVo();
				traderCustomer.setPhone(webAccountVo.getMobile());
				// 定义反序列化 数据格式
				final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
				};
				ResultInfo<?> result2;
				result2 = (ResultInfo<?>) HttpClientUtils.post(url, traderCustomer, clientId, clientKey, TypeRef2);
				
				JSONObject json = JSONObject.fromObject(result2.getData());
				// 对象包含另一个对象集合时用以下方法转化
				TraderCustomerVo res = JsonUtils.readValue(json.toString(), TraderCustomerVo.class);
				
				if (null != res) {// 有客户
					// 客户归属人
					User sale = userMapper.getUserByTraderId(res.getTraderId(), ErpConst.ONE);
					if (null != sale) {
						webAccountVo.setMaybeSaler(sale.getUsername());
					}
				}
			} catch (IOException e) {
				logger.error(Contant.ERROR_MSG, e);
			}
		}
		
		returnMap.put("webAccountVo", webAccountVo);

		returnMap.put("traderInfo", traderQuery);
		returnMap.put("traderCustomer", customer);
		returnMap.put("customer", customerVo);
		returnMap.put("contact", contactlist);
		returnMap.put("address", addressList);
		returnMap.put("traderAssociatedLogList", traderAssociatedLogList);

		return returnMap;
	}

	@Override
	public Map<String, Object> getWebAccountListPage(WebAccountVo webAccountVo, Page page, HttpSession session) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("webAccountVo", webAccountVo);
		map.put("page", page);
		
		List<WebAccountVo> list = webAccountMapper.getWebAccountListPage(map);
		
		if(null != list && list.size() > 0){
			//归属
			List<Integer> userIds = new ArrayList<>();
//			List<Integer> contactids = new ArrayList<>();
			List<OrgDTO> registerRegionalMall = getRegisterRegionalMall();
			for(WebAccountVo accountVo : list){
				if(null != accountVo.getUserId() && accountVo.getUserId() > 0){
					userIds.add(accountVo.getUserId());
				}
				
//				if(null != accountVo.getTraderContactId() && accountVo.getTraderContactId() > 0){
//					contactids.add(accountVo.getTraderContactId());
//				}

				//    VDERP-6719 【S2B2C】区域商城注册信息打通
				StringBuffer sbBelong = new StringBuffer();
				if(BelongPlatformEnum.BD.getBelong().equals(accountVo.getBelongPlatform())){
					sbBelong.append(BelongPlatformEnum.BD.getName());
				}
				if(BelongPlatformEnum.YXG.getBelong().equals(accountVo.getBelongPlatform())){
					sbBelong.append(BelongPlatformEnum.YXG.getName());
				}
				if(BelongPlatformEnum.KY.getBelong().equals(accountVo.getBelongPlatform())){
					sbBelong.append(BelongPlatformEnum.KY.getName());
				}
				if(BelongPlatformEnum.JT.getBelong().equals(accountVo.getBelongPlatform())){
					sbBelong.append(BelongPlatformEnum.JT.getName());
				}
				if(BelongPlatformEnum.OTHER.getBelong().equals(accountVo.getBelongPlatform())){
					sbBelong.append(BelongPlatformEnum.OTHER.getName());
				}
				if(BelongPlatformEnum.JC.getBelong().equals(accountVo.getBelongPlatform())){
					sbBelong.append(BelongPlatformEnum.JC.getName());
				}
				if(BelongPlatformEnum.QY.getBelong().equals(accountVo.getBelongPlatform())){
					sbBelong.append(BelongPlatformEnum.QY.getName());
				}
				if(BelongPlatformEnum.TMH.getBelong().equals(accountVo.getBelongPlatform())){
					sbBelong.append(BelongPlatformEnum.TMH.getName());
				}
				accountVo.setBelongPlatformStr(sbBelong.toString());


				StringBuffer sbRegister = new StringBuffer();
				if(RegisterPlatformEnum.BD.getRegistNo().equals(accountVo.getRegisterPlatform())){
					sbRegister.append(RegisterPlatformEnum.BD.getName());
				}
				if(RegisterPlatformEnum.YXG.getRegistNo().equals(accountVo.getRegisterPlatform())){
					sbRegister.append(RegisterPlatformEnum.YXG.getName());
				}
				if(RegisterPlatformEnum.KY.getRegistNo().equals(accountVo.getRegisterPlatform())){
					sbRegister.append(RegisterPlatformEnum.KY.getName());
				}
				if(RegisterPlatformEnum.JT.getRegistNo().equals(accountVo.getRegisterPlatform())){
					sbRegister.append(RegisterPlatformEnum.JT.getName());
				}
				if(RegisterPlatformEnum.OTHER.getRegistNo().equals(accountVo.getRegisterPlatform())){
					sbRegister.append(RegisterPlatformEnum.OTHER.getName());
				}
				if(RegisterPlatformEnum.JC.getRegistNo().equals(accountVo.getRegisterPlatform())){
					sbRegister.append(RegisterPlatformEnum.JC.getName());
				}
				if(RegisterPlatformEnum.QY.getRegistNo().equals(accountVo.getRegisterPlatform())){
					sbRegister.append(RegisterPlatformEnum.QY.getName());
					String registerRegionalMallName = getRegisterRegionalMallName(registerRegionalMall,accountVo.getRegisterRegionalMall());
					if(registerRegionalMallName != null){
						sbRegister.append("-");
						sbRegister.append(registerRegionalMallName);
					}
				}
				if(RegisterPlatformEnum.TMH.getRegistNo().equals(accountVo.getRegisterPlatform())){
					sbRegister.append(RegisterPlatformEnum.TMH.getName());
				}
				accountVo.setRegisterPlatformStr(sbRegister.toString());
			}
			
			if (userIds.size() > 0) {
				List<User> userList = userMapper.getUserByUserIds(userIds.stream().distinct().collect(Collectors.toList()));
				
				for(WebAccountVo accountVo : list){
					if(null != accountVo.getUserId() && accountVo.getUserId() > 0){
						for(User user : userList){
							if(accountVo.getUserId().equals(user.getUserId())){
								accountVo.setOwner(user.getUsername());
							}
						}
					}
				}
			}
			
			//关联公司
//			if(contactids.size() > 0){
//				try{
//					// 定义反序列化 数据格式
//					final TypeReference<ResultInfo<List<TraderCustomerVo>>> TypeRef = new TypeReference<ResultInfo<List<TraderCustomerVo>>>() {};
//					String url=httpUrl + "tradercustomer/getdhtraderinfo.htm";
//					ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, contactids,clientId,clientKey, TypeRef);
//					List<TraderCustomerVo> customerList = (List<TraderCustomerVo>) result.getData();
//					if(null != customerList && customerList.size() > 0){
//						for(TraderCustomerVo customer : customerList){
//							for(WebAccountVo accountVo :list){
//								if(customer.getTraderContactId().equals(accountVo.getTraderContactId())){
//									accountVo.setRelateComapnyName(customer.getTraderName());
//								}
//							}
//						}
//					}
//				}catch (Exception e) {
//					logger.error(Contant.ERROR_MSG, e);
//				}
//			}
			
		}
		
		Map<String, Object> returnMap = new HashMap<String, Object>();
		returnMap.put("list", list);
		returnMap.put("page", page);
		return returnMap;
	}

	private String getRegisterRegionalMallName(List<OrgDTO> registerRegionalMall, Integer registerRegionalMallId) {

		if(registerRegionalMallId == null || CollectionUtils.isEmpty(registerRegionalMall)){
			return null;
		}
		Optional<OrgDTO> first = registerRegionalMall.stream().filter(e -> String.valueOf(registerRegionalMallId).equals(e.getOrgId())).findFirst();
		if(first.isPresent()){
			return first.get().getOrgName();
		}
		return null;
	}

	public List<OrgDTO> getRegisterRegionalMall(){
		String url = inviUrl + "/org/queryOrg";
		try{
			ResponseEntity<OrgListDTO> orgListDTOResponseEntity = restTemplate.postForEntity(url, null, OrgListDTO.class);
			if(orgListDTOResponseEntity.getStatusCode().equals(HttpStatus.OK)){
				return orgListDTOResponseEntity.getBody().getOrgList();
			} else {
				return null;
			}
		} catch (Exception e){
			logger.error("getRegisterRegionalMall请求集采失败，e：",e);
			return null;
		}

	}


	@Override
	@Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
	public Integer saveEdit(WebAccountVo webAccountVo, HttpSession session,HttpServletRequest request) throws Exception {
		Integer saleorderId = Integer.valueOf(EmptyUtils.isBlank(request.getParameter("saleorderId")) ? "0" : request.getParameter("saleorderId"));
		//VDERP-2426： 当账号关联的客户没有分配归属销售时，同步修改客户的归属销售（客户归属销售与账号的归属销售一致）
		Trader trader = traderMapper.getTraderByTraderId(webAccountVo.getTraderId());
		// 销售修改客户(当前登录人员非客户归属销售)
		Integer belongPaltform=null;
		if ("saleWebConfirm".equals(webAccountVo.getOptType())) {
			User user = userMapper.getUserByTraderCustomerId(webAccountVo.getTraderCustomerId(), CommonConstants.ONE);
			// 修改ERP归属销售
			webAccountVo.setUserId(user.getUserId());
		}
		if(webAccountVo.getUserId()!=null&&webAccountVo.getUserId()>0){
			//VDERP-5598 如果客户目前归属平台是集采，那么变更归属销售时，不同步更新其归属平台
			if (trader != null && BelongPlatformEnum.JC.getBelong().equals(trader.getBelongPlatform())){
				belongPaltform = BelongPlatformEnum.JC.getBelong();
			} else {
				belongPaltform=userService.getBelongPlatformOfUser(webAccountVo.getUserId(),1);
			}
			webAccountVo.setBelongPlatform(belongPaltform);
		}
		User user = (User) session.getAttribute(ErpConst.CURR_USER);
		WebAccount webAccount = webAccountMapper.getWebAccontAuthous(webAccountVo);
		//换绑后给前台发送消息
		sendMsgLinkChange(webAccountVo);
		//更新BD订单里交易者信息
		sendMsgIfFirstChangeTrader(webAccountVo,true);
		//医械购一个注册用户只可关联一个客户，一个客户也只可被关联一个注册用户
		if (trader != null && BelongPlatform.YXG.getBelongPlatform().equals(belongPaltform)) {
			WebAccount condition = new WebAccount();
			condition.setTraders(Collections.singletonList(trader.getTraderId()));
			List<WebAccount> webAccountTraderList = webAccountMapper.getWebAccontTrader(condition);
			if(webAccountTraderList.stream().anyMatch(account -> !Objects.equals(account.getErpAccountId(), webAccountVo.getErpAccountId()))){
				logger.info("【注册用户关联客户】客户已关联了注册用户 - traderId:{},erpAccountId:{}, 销售归属平台:{}", trader.getTraderId(),
						webAccountVo.getErpAccountId(),belongPaltform);
				throw new IllegalStateException(String.format("%s已有关联用户，请重新选择", trader.getTraderName()));
			}
		}
		Integer k = saleorderService.updateBDChangeErp(webAccountVo, webAccountVo.getTraderId(), saleorderId);
		Integer i = webAccountMapper.update(webAccountVo);


		if (trader != null) {
			if (rTraderJUserMapper.getUserByTraderId(trader.getTraderId()) == null) {
				RTraderJUser var = new RTraderJUser();
				var.setTraderId(webAccountVo.getTraderId());
				var.setUserId(webAccountVo.getUserId());
				var.setTraderType(trader.getTraderType());
				//更新客户的归属销售
				rTraderJUserMapper.insert(var);

				//新增交易者归属变更记录
				logger.info("【新增交易者归属变更记录】新增了交易者归属绑定关系 - traderId:{},fromUser:{}, toUser:{}", var.getTraderId(),"无",var.getUserId());
				insertRTraderJUserModifyRecordWhenInsertrTraderJUser(var);

				//更新客户的归属平台
//				if (belongPaltform != null) {
//					Trader toUpdate = new Trader();
//					toUpdate.setTraderId(trader.getTraderId());
//					toUpdate.setBelongPlatform(belongPaltform);
//					traderMapper.updatePartBySelective(toUpdate);
//					sendMsgIfTraderBelongPlatformChange(trader.getTraderId(),belongPaltform,ErpConst.ZERO);
//				}
			}



			//记录客户关联客户信息
			if (!Objects.equals(webAccount.getTraderId(), webAccountVo.getTraderId())) {
				TraderAssociatedLogDto traderAssociatedLogDto = new TraderAssociatedLogDto();
				traderAssociatedLogDto.setErpAccountId(webAccountVo.getErpAccountId());
				traderAssociatedLogDto.setWebAccountToBelongPlatformNo(belongPaltform);
				traderAssociatedLogDto.setTraderId(trader.getTraderId());
				traderAssociatedLogDto.setOperatorId(user.getUserId());
				traderAssociatedLogDto.setOperationType(TraderAssociatedLogEnum.BIND.getType());
				StringBuilder nameJoiner = new StringBuilder(user.getUsername());
				if (user.getUserDetail()!=null && StringUtils.isNotBlank(user.getUserDetail().getRealName())) {
					nameJoiner.append("(")
							.append(user.getUserDetail().getRealName())
							.append(")");
				}
				traderAssociatedLogDto.setRemark("关联客户："+ trader.getTraderName());
				traderAssociatedLogDto.setOperatorName(nameJoiner.toString());
				traderAssociatedLogService.recordTraderAssociatedLog(traderAssociatedLogDto);
			}
		}
		WebAccount webAccount2 = webAccountMapper.getWebAccontAuthous(webAccountVo);
		logger.info("更新注册用户信息 用户id:{},更新前:{},更新后:{}",webAccount.getErpAccountId(),webAccount.getUserId(),webAccount2.getUserId());
		List<Saleorder> saleorderList=new ArrayList<>();
		Saleorder saleorder = new Saleorder();
		saleorder.setCreateMobile(webAccount.getMobile());
		if(null!=saleorder&& StringUtil.isNotBlank(saleorder.getCreateMobile())){
			 saleorderList = saleorderService.selectSaleorderNo(saleorder);
		}
		List<Integer> userd = new ArrayList<>();
		Map<String, String> hashmap = new HashMap<>();
		if (null != webAccount && null != webAccount2) {
			userd.add(webAccount2.getUserId());
			if (null == webAccount.getUserId()) {
				if (null != webAccount2.getUserId()) {
					if (null != saleorderList && saleorderList.size() > 0) {
						for (Saleorder saleorder1 : saleorderList) {
							hashmap.put("saleorderNo", saleorder1.getSaleorderNo());
							MessageUtil.sendMessage2(96, userd, hashmap, "./order/saleorder/view.do?saleorderId=" + saleorder1.getSaleorderId());
						}
					}
				}
			}
			else if (webAccount.getUserId() != webAccount2.getUserId() || webAccount.getTraderId() != webAccount2.getTraderId()) {
				if (null != saleorderList && saleorderList.size() > 0) {
					for (Saleorder saleorder1 : saleorderList) {
						hashmap.put("saleorderNo", saleorder1.getSaleorderNo());
						MessageUtil.sendMessage2(96, userd, hashmap, "./order/saleorder/view.do?saleorderId=" + saleorder1.getSaleorderId());
					}
				}
			}
			logger.info("发送注册用户信息 用户id:{},更新前:{},更新后:{}",webAccount.getErpAccountId(),webAccount.getUserId(),webAccount2.getUserId());
			if((webAccount.getUserId() == null && webAccount2.getUserId() != null ) ||
					(webAccount.getUserId() != null && !webAccount.getUserId().equals(webAccount2.getUserId()))){
				logger.info("发送注册用户信息  用户id:{},收到用户:{}",webAccount.getErpAccountId(),userd.toString());
				hashmap.put("mobile",webAccount2.getMobile());
				MessageUtil.sendMessage2(112, userd, hashmap,"trader/accountweb/view.do?erpAccountId="+webAccountVo.getErpAccountId() ,user.getUsername());
			}
		}
			//更新贝登会员
			traderCustomerService.updateVedengMember();
			return i + k;

	}
	/**
	 * @Description 新增交易者归属信息更改记录
	 * <AUTHOR>
	 * @Date 17:09 2021/8/4
	 * @Param [rTrader]
	 * @return void
	 **/
	private void insertRTraderJUserModifyRecordWhenInsertrTraderJUser(RTraderJUser rTrader) {
		RTraderJUserModifyRecord rTraderJUserModifyRecord = new RTraderJUserModifyRecord();
		rTraderJUserModifyRecord.setTraderType(rTrader.getTraderType());
		rTraderJUserModifyRecord.setUserId(rTrader.getUserId());
		rTraderJUserModifyRecord.setCreator(rTrader.getUserId());
		rTraderJUserModifyRecord.setStartTime(DateUtil.gainNowDate());
		rTraderJUserModifyRecord.setTraderId(rTrader.getTraderId());
		rTraderJUserModifyRecordMapper.insert(rTraderJUserModifyRecord);
	}



	private void sendMsgLinkChange(WebAccountVo webAccountVo){
		if(webAccountVo.getTraderId()==null||webAccountVo.getTraderId()==0){
			return;
		}
		int status;
		Trader trader=traderMapper.getTraderByTraderId(webAccountVo.getTraderId());
		TraderCustomer traderCustomer=traderCustomerMapper.getBaseCustomerByTraderId(webAccountVo.getTraderId());
		Integer verify_status = traderCustomerMapper.getTraderCertificateCheckStatusByTraderId(traderCustomer.getTraderId());
		if (ErpConst.ZERO.equals(verify_status) || ErpConst.ONE.equals(verify_status) || ErpConst.FIVE.equals(verify_status)) {
			status = 1;
		}else {
			status = 0;
		}
		com.alibaba.fastjson.JSONObject json=new com.alibaba.fastjson.JSONObject();
		json.put("traderId",trader.getTraderId());
		json.put("status",status);
		json.put("mobile",webAccountVo.getMobile());
		json.put("belongPlatform",trader.getBelongPlatform());
		msgProducer.sendMsg(RabbitConfig.CUSTOMER_LINK_ACCOUNT_EXCHANGE,null,json.toJSONString());
	}

	/**
	 * 客户的归属平台发生变更推送消息
	 */
	private void sendMsgIfTraderBelongPlatformChange(Integer traderId,Integer nowPlatform,Integer prePlatform){
		if(prePlatform.equals(nowPlatform)){
			return;
		}
		com.alibaba.fastjson.JSONObject json=new com.alibaba.fastjson.JSONObject();
		json.put("traderId",traderId);
		json.put("nowPlatform",nowPlatform);
		json.put("prePlatform",prePlatform);
		Timer time=new Timer();
		time.schedule(new TimerTask() {
			@Override
			public void run() {
				opMsgProducer.sendMsg(RabbitConfig.TRADER_BELONG_PLATFORM_EXCHANGE,null,json.toJSONString());
			}
		},5000);

	}

	@Autowired
	private WebAccountInvitationLogMapper invitationLogMapper;

	@Autowired
	private OpMsgProducer opMsgProducer;

	@Override
	public  void sendMsgIfFirstChangeTrader(WebAccountVo webAccountVo,boolean isErpLink){
		if(webAccountVo.getTraderId()==null||webAccountVo.getTraderId()==0){
			return;
		}
		WebAccount webAccount=webAccountMapper.getWebAccontAuthous(webAccountVo);
		int firstLink=1;
		if(isErpLink && webAccount.getTraderId()!=null
				&& webAccount.getTraderId().equals(webAccountVo.getTraderId())){
			return;
		}
		if(isErpLink) {
			if (webAccount.getTraderId() != null && webAccount.getTraderId() > 0) {
				firstLink=0;
			}
		}
        Integer hasInvited=invitationLogMapper.countInvited(webAccount.getMobile());
		if(hasInvited==null||hasInvited==0){
			return;
		}
		JSONObject json=new JSONObject();
		Integer status;
		Long addTime=0L;
		Trader trader=traderMapper.getTraderByTraderId(webAccountVo.getTraderId());
		TraderCustomer traderCustomer=traderCustomerMapper.getBaseCustomerByTraderId(webAccountVo.getTraderId());
		VerifiesInfo verifiesInfo=traderCustomerService.getCustomerAptitudeVerifiesInfo(traderCustomer.getTraderCustomerId());
		if(verifiesInfo==null){
			status=-1;
		}else{
			status=verifiesInfo.getStatus();
			if(status==1){
				addTime=verifiesInfo.getModTime();
			}
		}
		json.put("traderId",trader.getTraderId());
		json.put("traderName",trader.getTraderName());
		json.put("customerNature",traderCustomer.getCustomerNature());
		json.put("status",status);
		json.put("checkPassTime",addTime);
		json.put("belongPlatform",trader.getBelongPlatform());
		json.put("firstLink",firstLink);
		json.put("mobile",webAccount.getMobile());

		// 判断客户是否已经提交过资质审核，已经提交待迁移、或者已经发起审核
		boolean hasPostCertification = verifiesInfo != null;
		if (!hasPostCertification){
			hasPostCertification = certificateMapper.getCountByWebAccountId(webAccount.getWebAccountId()) > 0;
		}
		json.put("hasPostCertification",hasPostCertification);

		//存在MQ已经消费，但是从库还没同步的情况。所以这儿使用延时操作来暂时解决这个问题
		Timer time=new Timer();
		time.schedule(new TimerTask() {
			@Override
			public void run() {
				opMsgProducer.sendMsg(RabbitConfig.TRADER_LINK_ACCOUNT_EXCHANGE,null,json.toString());
			}
		},5000);

	}

    @Override
    public AccountResponse getJcAccountInfo(String mobile) {
		if (StringUtils.isBlank(mobile)){
			return null;
		}
		String url = jcServiceDomain + ErpConst.JC_ACCOUNT_INFO_URL;
		com.alibaba.fastjson.TypeReference<RestfulResult<AccountResponse>> typeReference =
				new com.alibaba.fastjson.TypeReference<RestfulResult<AccountResponse>>(){};
		try {
			JcAccountQueryDto queryDto = new JcAccountQueryDto();
			queryDto.setMobile(mobile);
			RestfulResult<?> result = HttpRestClientUtil.restPost(url,typeReference,null,queryDto);
			if(result == null){
				logger.error("获取集采用户信息失败，请求参数：{}",queryDto.toString());
				return null;
			}
			if(result.isSuccess()){
				return (AccountResponse) result.getData();
			}
			return null;
		} catch (Exception e) {
			logger.error("获取集采用户信息失败，请求参数：{}，错误信息：",mobile,e);
			return null;
		}
    }

    @Override
	public List<WebAccount> getWebAccountByTraderContactId(WebAccount webAccount){
		return webAccountMapper.getWebAccountListByParam(webAccount);
	}

    @Override
    public ResultInfo vailTraderUser(WebAccountVo webAccountVo) {
        if("sale".equals(webAccountVo.getOwner())){
            User user = userMapper.getUserByTraderCustomerId(webAccountVo.getTraderCustomerId(), CommonConstants.ONE);
            if(user != null && webAccountVo.getUserId().equals(user.getUserId())){
                return new ResultInfo(0,"操作成功");
            }
            return new ResultInfo(-1,"该关联客户不在您名下，关联后该注册用户及相应订单将转移到其他销售名下");
        } else {
            User user = userMapper.getUserByTraderCustomerId(webAccountVo.getTraderCustomerId(), CommonConstants.ONE);
			if (user != null && !webAccountVo.getUserId().equals(user.getUserId())) {
				return new ResultInfo(-1,"分配的归属销售与关联公司的归属销售不一致，无法分配。");
			} else {
				return new ResultInfo(0,"操作成功");
			}
        }
    }

    @Transactional(rollbackFor = Exception.class)
	@Override
	public void unbindAssociationWithTrader(TraderUnboundDto traderUnboundDto, User operator) {
		Preconditions.checkArgument(traderUnboundDto.getTraderId() != null, "客户编号为空");
		Preconditions.checkArgument(traderUnboundDto.getErpAccountId()!= null, "注册用户编号为空");

		TraderUnboundReasonEnum traderUnboundReasonToUse = TraderUnboundReasonEnum.getByType(traderUnboundDto.getReasonType());
		if (traderUnboundReasonToUse == null) {
			throw new IllegalArgumentException("注册用户解除关联公司时，缺少必要解除原因");
		}
		//解除原因为其他时，备注原因不得为空，且不能超过30字
		if (traderUnboundReasonToUse == TraderUnboundReasonEnum.OTHERS) {
            if (StringUtils.isEmpty(traderUnboundDto.getRemark()) || traderUnboundDto.getRemark().length() > TraderAssociatedLogService.REMARK_LIMITED_SIZE) {
                throw new IllegalArgumentException("备注原因不得为空，且不能超过30字");
            }
        }

		WebAccount webAccountQuery = webAccountMapper.getWebAccountInfo(traderUnboundDto.getErpAccountId());
		if(webAccountQuery == null){
			logger.info("注册用户解除关联公司时查询注册用户失败 - erpAccountId:{}", traderUnboundDto.getErpAccountId());
			throw new IllegalStateException();
		}

		if(webAccountQuery.getTraderId() == 0) {
			logger.info("注册用户没有关联公司无需解除客户操作 - erpAccountId:{}", traderUnboundDto.getErpAccountId());
			throw new IllegalStateException();
		}

		//若客户信息-联系人中创建过该注册用户的信息，则将该注册用户置为禁用状态
		TraderContactGenerate contactToUpdate = null;
		if (webAccountQuery.getTraderContactId()!=null && webAccountQuery.getTraderContactId()>0) {
		    contactToUpdate = traderContactService.selectByPrimaryKey(webAccountQuery.getTraderContactId());
		}
		if (contactToUpdate == null) {
			contactToUpdate = traderContactService.getByTraderIdAndMobileNo(traderUnboundDto.getTraderId(), webAccountQuery.getMobile());
		}

		if(contactToUpdate!=null && !Objects.equals(contactToUpdate.getIsEnable(), CommonConstants.DISABLE)){
			//if found one, then to update.
			contactToUpdate.setTraderId(traderUnboundDto.getTraderId());
			contactToUpdate.setIsEnable(CommonConstants.DISABLE);
			contactToUpdate.setUpdater(operator.getUserId());
			contactToUpdate.setModTime(System.currentTimeMillis());
			traderContactService.updateByPrimaryKeySelective(contactToUpdate);
		}

		webAccountQuery.setTraderId(ErpConst.ZERO);
		webAccountQuery.setTraderContactId(ErpConst.ZERO);
		webAccountQuery.setTraderAddressId(ErpConst.ZERO);
		//解除注册用户关联客户信息，并贝登会员置为"非会员"
		webAccountQuery.setIsVedengMember(CommonConstants.STATUS_0);
		webAccountQuery.setModTime(new Date());
		webAccountMapper.updateisVedengJoin(webAccountQuery);
		//VDERP-4748 清空贝登会员开通时间
		webAccountMapper.updateClearVedengMemberTime(webAccountQuery);
        //记录注册用户解除客户信息日志
        TraderAssociatedLogDto traderAssociatedLogDto = new TraderAssociatedLogDto();
        traderAssociatedLogDto.setErpAccountId(traderUnboundDto.getErpAccountId());
        traderAssociatedLogDto.setWebAccountToBelongPlatformNo(webAccountQuery.getBelongPlatform());
        traderAssociatedLogDto.setTraderId(traderUnboundDto.getTraderId());
        traderAssociatedLogDto.setOperationType(TraderAssociatedLogEnum.UNBIND.getType());
        traderAssociatedLogDto.setOperatorId(operator.getUserId());
        StringBuilder operatorNameBuilder = new StringBuilder(operator.getUsername());
        if (operator.getUserDetail()!=null&&StringUtils.isNotEmpty(operator.getUserDetail().getRealName())) {
            operatorNameBuilder.append("(")
                    .append(operator.getUserDetail().getRealName())
                    .append(")");
        }
        traderAssociatedLogDto.setOperatorName(operatorNameBuilder.toString());
        traderAssociatedLogDto.setReason(traderUnboundReasonToUse.getMessage());
        if (traderUnboundReasonToUse==TraderUnboundReasonEnum.OTHERS) {
			traderAssociatedLogDto.setRemark(traderUnboundDto.getRemark());
		}
        traderAssociatedLogService.recordTraderAssociatedLog(traderAssociatedLogDto);

        //发送消息给前台 账号名（注册手机号码）
		String traderReceptionStr = com.alibaba.fastjson.JSONObject.toJSONString(webAccountQuery.getMobile(), SerializerFeature.WriteMapNullValue);
		try {
			logger.info("解绑发送消息到前台 start=======" + JSON.toJSONString(traderReceptionStr));
			erpMsgProducer.sendMsg(RabbitConfig.JC_ERP_ORG_EXCHANGE,RabbitConfig.JCERPUNBOUNDROUTINGKEY,traderReceptionStr);
		}catch (Exception e){
			logger.error("解绑发送消息到前台失败" + JSON.toJSONString(traderReceptionStr));

		}

	}

	@Override
	public Integer getWebAccountIdByMobile(String traderContactTelephone) {
		return webAccountMapper.getWebAccountIdByMobile(traderContactTelephone);
	}

	@Override
	public WebAccount getWebAccontAuthous(WebAccount webAccount) {
		return webAccountMapper.getWebAccontAuthous(webAccount);
	}

	@Override
	public Map<String, Object> getCertificateByCategory(Integer webAccountId) {
		WebAccountCertificate queryCertificate = new WebAccountCertificate();
		queryCertificate.setWebAccountId(webAccountId);
		List<WebAccountCertificate> certificates = certificateMapper.getCertificateList(queryCertificate);
		Map<String, Object> result = new HashMap<>();
		if (CollectionUtils.isNotEmpty(certificates)) {
			for (WebAccountCertificate c : certificates) {
				if (c != null && c.getType() != null) {
					switch (c.getType()) {
						//营业执照
						case TraderConstants.TYPE_BUSINESS_CERTIFICATE:
							if (result.get(TraderConstants.ACCOUNT_BUSINESS_CERTIFICATE) == null) {
								result.put(TraderConstants.ACCOUNT_BUSINESS_CERTIFICATE, new ArrayList<>());
							}
							List businessCertificates = (ArrayList) result.get(TraderConstants.ACCOUNT_BUSINESS_CERTIFICATE);
							businessCertificates.add(c);
							break;
						//二类资质
						case TraderConstants.TYPE_SECOND_MEDICAL:
							if (result.get(TraderConstants.ACCOUNT_SECOND_CERTIFICATE) == null) {
								result.put(TraderConstants.ACCOUNT_SECOND_CERTIFICATE, new ArrayList<>());
							}
							List secondCertificates = (ArrayList) result.get(TraderConstants.ACCOUNT_SECOND_CERTIFICATE);
							secondCertificates.add(c);
							break;
						//三类资质
						case TraderConstants.TYPE_THIRD_MEDICAL:
							if (result.get(TraderConstants.ACCOUNT_THIRD_CERTIFICATE) == null) {
								result.put(TraderConstants.ACCOUNT_THIRD_CERTIFICATE, new ArrayList<>());
							}
							List thirdCertificates = (ArrayList) result.get(TraderConstants.ACCOUNT_THIRD_CERTIFICATE);
							thirdCertificates.add(c);
							break;
						//个人名片
						case TraderConstants.BUSINESS_CARDS:
							if (result.get(TraderConstants.ACCOUNT_BUSINESSCARD_CERTIFICATE) == null) {
								result.put(TraderConstants.ACCOUNT_BUSINESSCARD_CERTIFICATE, new ArrayList<>());
							}
							List businessCards = (ArrayList) result.get(TraderConstants.ACCOUNT_BUSINESSCARD_CERTIFICATE);
							businessCards.add(c);
							break;
						default:
							break;
					}
				}
			}
		}
		return result;
	}

	/**
	 * <b>Description:</b>转移客户资质<br>
	 *
	 * @param
	 * @return
	 * @Note <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/3/3
	 */
	@Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public ResultInfo transferCertificate(Integer erpAccountId, Integer traderId, int type, User user) {
		WebAccount webAccount=webAccountMapper.getWebAccountInfo(erpAccountId);
		TraderCustomer traderCustomer=traderCustomerMapper.getCustomerInfo(traderId);
		if(traderCustomer==null||traderCustomer.getTraderCustomerId()==null){
			return new ResultInfo(-1,"该客户不存在");
		}
		if (webAccount != null && webAccount.getBelongPlatform() != null
				&& webAccount.getBelongPlatform() == TraderConstants.PLATFORM_BEIDENG) {
			if (traderCustomer != null && traderCustomer.getCustomerNature() != null
					&& !traderCustomer.getCustomerNature().equals(SysOptionConstant.ID_465)) {
				return new ResultInfo(-1, "客户性质为终端，无法转移。");
			}
		}
		WebAccountCertificate query = new WebAccountCertificate();
		if (type == TraderConstants.TYPE_BUSINESS_CERTIFICATE) {
			//只转移营业执照
			query.setWebAccountId(erpAccountId);
			query.setType(TraderConstants.TYPE_BUSINESS_CERTIFICATE);
			List<WebAccountCertificate> businessCertificate = certificateMapper.getCertificateList(query);
			if (CollectionUtils.isNotEmpty(businessCertificate)) {
				tranferCertificateReal(businessCertificate, traderId, SysOptionConstant.ID_25, user);
			}
			setCustomerAptitudeUncheck(traderCustomer.getTraderCustomerId());
		} else if (type == TraderConstants.TYPE_THIRD_MEDICAL) {
			//只转移三类医疗资质
			query.setWebAccountId(erpAccountId);
			query.setType(TraderConstants.TYPE_THIRD_MEDICAL);
			List<WebAccountCertificate> thirdCertificate = certificateMapper.getCertificateList(query);
			if (CollectionUtils.isNotEmpty(thirdCertificate)) {
				tranferCertificateReal(thirdCertificate, traderId, SysOptionConstant.ID_29, user);
			}
			changeTraderMedicalQualication(traderId);
			setCustomerAptitudeUncheck(traderCustomer.getTraderCustomerId());
		} else if (type == TraderConstants.TYPE_SECOND_MEDICAL) {
			//只转移二类医疗资质
			query.setWebAccountId(erpAccountId);
			query.setType(TraderConstants.TYPE_SECOND_MEDICAL);
			List<WebAccountCertificate> secondCertificate = certificateMapper.getCertificateList(query);
			if (CollectionUtils.isNotEmpty(secondCertificate)) {
				transferSecondCertificateReal(secondCertificate,traderId,user);
			}
			changeTraderMedicalQualication(traderId);
			setCustomerAptitudeUncheck(traderCustomer.getTraderCustomerId());
		} else if (type == TraderConstants.TRANSFER_ALL_CERTIFICATES) {
			//批量转移
			Map<String, Object> map = getCertificateByCategory(erpAccountId);
			if (!map.containsKey(TraderConstants.ACCOUNT_BUSINESS_CERTIFICATE)
					&& !map.containsKey(TraderConstants.ACCOUNT_SECOND_CERTIFICATE)
					&& !map.containsKey(TraderConstants.ACCOUNT_THIRD_CERTIFICATE)) {
				return new ResultInfo(-1, "资质图片为空，无法操作。");
			}
			if (map.get(TraderConstants.ACCOUNT_BUSINESS_CERTIFICATE) != null) {
				List<WebAccountCertificate> businessCertificate = (ArrayList) map.get(TraderConstants.ACCOUNT_BUSINESS_CERTIFICATE);
				tranferCertificateReal(businessCertificate, traderId, SysOptionConstant.ID_25, user);
			}
			if (map.get(TraderConstants.ACCOUNT_SECOND_CERTIFICATE) != null) {
				List certificates = (ArrayList) map.get(TraderConstants.ACCOUNT_SECOND_CERTIFICATE);
				if (CollectionUtils.isNotEmpty(certificates)) {
					transferSecondCertificateReal(certificates,traderId,user);
				}
			}
			if (map.get(TraderConstants.ACCOUNT_THIRD_CERTIFICATE) != null) {
				List<WebAccountCertificate> thirdCertificate = (ArrayList) map.get(TraderConstants.ACCOUNT_THIRD_CERTIFICATE);
				tranferCertificateReal(thirdCertificate, traderId, SysOptionConstant.ID_29, user);
			}
			if (map.containsKey(TraderConstants.ACCOUNT_SECOND_CERTIFICATE) || map.containsKey(TraderConstants.ACCOUNT_THIRD_CERTIFICATE)) {
				changeTraderMedicalQualication(traderId);
			}
			setCustomerAptitudeUncheck(traderCustomer.getTraderCustomerId());
		}
		return new ResultInfo(0, "操作成功");
	}

	@Override
	public Integer getBelongPlatformOfAccount(Integer userId, Integer traderId, Integer registerPlatform) {
		if (traderId != null) {
			//若已关联公司，显示关联的公司的归属平台
			return traderMapper.getTraderInfoByTraderId(traderId).getBelongPlatform();
		} else if (userId != null){
			//若未关联公司但有归属销售，显示归属销售的所属平台；
			return userService.getBelongPlatformOfUser(userId,1);
		} else {
			//若未关联公司且无归属销售，显示注册账号的注册平台；
			return registerPlatform;
		}
	}


	/**
	 * <b>Description:</b>设置客户资质为待审核状态<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/3/4
	 */
	private void setCustomerAptitudeUncheck(Integer traderCustomerId){
        traderCustomerService.setCustomerAptitudeUncheck(traderCustomerId);
	}
	/**
	 * <b>Description:</b>修改客户医疗资质合一为否<br>
	 *
	 * @param traderId 客户标识
	 * @return
	 * @Note <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/3/3
	 */
	private void changeTraderMedicalQualication(Integer traderId) {
		Trader trader = new Trader();
		trader.setTraderId(traderId);
		trader.setMedicalQualification(0);
		traderMapper.updatePartBySelective(trader);
	}

	/**
	 * <b>Description:</b>转移营业执照信息<br>
	 *
	 * @param traderId    客户Id
	 * @param type        资质类型
	 * @return
	 * @Note <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/3/3
	 */
	private void tranferCertificateReal(List<WebAccountCertificate> certificates, Integer traderId, Integer type, User user) {
		if (CollectionUtils.isNotEmpty(certificates)) {
			traderCertificateMapper.delTraderCertificateAndByTypeId(traderId, type);
			for (WebAccountCertificate o : certificates) {
				if (o != null) {
					TraderCertificate traderCertificate = initTraderCertificate(o, traderId, type, user);
					certificateMapper.deleteByPrimaryKey(o.getWebAccountCertificateId());
					traderCertificateMapper.insertSelective(traderCertificate);
				}
			}
		}
	}

	/**
	 * <b>Description:</b>转移二类资质<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/3/4
	 */
	private void transferSecondCertificateReal(List certificates,Integer traderId,User user){
		if (CollectionUtils.isNotEmpty(certificates)) {
			traderCertificateMapper.delTraderCertificateAndByTypeId(traderId, SysOptionConstant.ID_28);
			for (Object o : certificates) {
				if (o != null) {
					WebAccountCertificate c = (WebAccountCertificate) o;
					TraderCertificate traderCertificate = initTraderCertificate(c, traderId, SysOptionConstant.ID_28, user);
					certificateMapper.deleteByPrimaryKey(c.getWebAccountCertificateId());
					traderCertificateMapper.insertSelective(traderCertificate);
				}
			}
		}
	}
	/**
	 * <b>Description:</b>生成客户资质信息<br>
	 *
	 * @param certificate 资质信息
	 * @param traderId    客户Id
	 * @param type        资质类型
	 * @return
	 * @Note <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/3/3
	 */
	private TraderCertificate initTraderCertificate(WebAccountCertificate certificate, Integer traderId, Integer type, User user) {
		TraderCertificate traderCertificate = new TraderCertificate();
		traderCertificate.setSysOptionDefinitionId(type);
		traderCertificate.setAddTime(System.currentTimeMillis());
		traderCertificate.setCreator(user.getUserId());
		traderCertificate.setBegintime(0L);
		traderCertificate.setEndtime(0L);
		traderCertificate.setDomain(certificate.getDomain());
		traderCertificate.setUri(certificate.getUri());
		traderCertificate.setOssResourceId(certificate.getOssId());
		traderCertificate.setTraderId(traderId);
		if(type.equals(SysOptionConstant.ID_25)){
			traderCertificate.setName(TraderConstants.CERTIFICATE_BUSINESS_NAME);
		}else if(type.equals(SysOptionConstant.ID_28)){
			traderCertificate.setName(TraderConstants.CERTIFICATE_SECOND_NAME);
		}else if(type.equals(SysOptionConstant.ID_29)){
			traderCertificate.setName(TraderConstants.CERTIFICATE_THIRD_NAME);
		}
		return traderCertificate;
	}

	@Override
	public ResultInfo updateBDTraderCertificate(BDTraderCertificate bdTraderCertificate) {
		String url = httpUrl + ErpConst.BD_CETIFICATE_UPDATE_URL;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo> TypeRef2 = new TypeReference<ResultInfo>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, bdTraderCertificate, clientId, clientKey, TypeRef2);
			if(result == null){
				result=new ResultInfo<>(1100,"操作失败");
			}
			if(result.getCode()==0){
                sendMessage(bdTraderCertificate.getVdUserTel(),TraderConstants.UPADATE_WEBACCOUNT_CERTIFICATE);
            }
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo(1100,"操作失败");
		}
	}

	@Override
	public ResultInfo addBDTraderCertificate(BDTraderCertificate bdTraderCertificate) {
		String url = httpUrl + ErpConst.BD_CETIFICATE_ADD_URL;
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo> TypeRef2 = new TypeReference<ResultInfo>() {
		};
		try {
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, bdTraderCertificate, clientId, clientKey, TypeRef2);
			if(result == null){
				result=new ResultInfo<>(1100,"操作失败");
			}
            if(result.getCode()==0){
            	WebAccount webAccount=webAccountMapper.getWenAccountInfoByMobile(bdTraderCertificate.getVdUserTel());
            	WebAccountVo webAccountVo=new WebAccountVo();
            	webAccountVo.setTraderId(webAccount.getTraderId());
            	webAccountVo.setErpAccountId(webAccount.getErpAccountId());
            	sendMsgIfFirstChangeTrader(webAccountVo,false);
                sendMessage(bdTraderCertificate.getVdUserTel(),TraderConstants.ADD_WEBACCOUNT_CERTIFICATE);
                
                //VDERP-17057  【客户档案】ERP客户档案时间轴 贝登客户新增
            	addBdTraderTrack(result);
            }
			return result;
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo(1100,"操作失败");
		}
	}

	private void addBdTraderTrack(ResultInfo<?> result) {
		try {
			TrackParamsData trackParamsData = new TrackParamsData();
			Map<String, Object> trackParams = new HashMap<>();
			trackParams.put("traderId",JSON.parseObject((String) result.getData()).getInteger("traderId"));
			TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(EventTrackingEnum.BASE_INFO_NEW_CUSTOMER_FROM_FRONT);
			trackParamsData.setEventTrackingEnum(EventTrackingEnum.BASE_INFO_NEW_CUSTOMER_FROM_FRONT);
			trackParamsData.setTrackParams(trackParams);
			trackParamsData.setTrackResult(ResultInfo.success());
			trackStrategy.track(trackParamsData);
		}catch(Exception e) {
			logger.error("埋点：{}，失败，不影响正常业务",EventTrackingEnum.BASE_INFO_NEW_CUSTOMER_FROM_FRONT.getArchivedName(),e);
		}
	}



	private ResultInfo sendWebaccountCertificateMsg( WebAccountCertificateMsg msg){
        try {
            String[] str = {"njadmin", "2"};
            Map<String, String> map = new HashMap<>();
            map.put("mobile", msg.getMobile());
            MessageUtil.sendMessage(msg.getMessageTemplateId(), msg.getUserIds(), map, msg.getUrl(), str);
            return new ResultInfo(0,"操作成功");
        }catch (Exception ex){
            return new ResultInfo(-1,"操作失败");
        }
    }

    public void sendMessage(String moblie,int type) {
        WebAccount account=webAccountMapper.getWenAccountInfoByMobile(moblie);
        WebAccountCertificateMsg webAccountCertificateMsg = new WebAccountCertificateMsg();
        webAccountCertificateMsg.setMobile(account.getMobile());
        webAccountCertificateMsg.setUrl(TraderConstants.WEBACCOUNT_DETAIL_URL + account.getErpAccountId());
        List<Integer> userIds = new ArrayList<>();
        RTraderJUser rTraderJUser = rTraderJUserMapper.getUserByTraderId(account.getTraderId());
        if (rTraderJUser != null && rTraderJUser.getUserId() != null && rTraderJUser.getUserId() > 0) {
            userIds.add(rTraderJUser.getUserId());
        }else{
            return;
        }
        webAccountCertificateMsg.setUserIds(userIds);

        try {
            if (type == TraderConstants.ADD_WEBACCOUNT_CERTIFICATE) {
                webAccountCertificateMsg.setMessageTemplateId(100);
            } else if (type == TraderConstants.UPADATE_WEBACCOUNT_CERTIFICATE) {
                webAccountCertificateMsg.setMessageTemplateId(101);
            }
            sendWebaccountCertificateMsg(webAccountCertificateMsg);
        } catch (Exception ex) {
            logger.error("注册用户资质提交，消息提醒失败,erp_webaccount:" + account.getErpAccountId(), ex);
        }
    }

	/**
	 *   根据手机 获取6月16日 0 点以后的订单
	 * @param mobile
	 * @return
	 */
	@Override
	public List<Saleorder> selectLatestSaleOrderByMobile(String mobile) {
		return saleorderService.selectLatestSaleOrderByMobile(mobile);
	}

	@Override
	public WebAccount getByMobileNo(String mobileNo) {
		if (StringUtils.isEmpty(mobileNo)) {
			return null;
		}
		return webAccountMapper.getByMobileNo(mobileNo);
	}

	@Override
	public List<Map<String, Object>> getBusinessCard(List<WebAccountCertificate> certificates) {
		List<Map<String, Object>> maps = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(certificates)) {
			for (WebAccountCertificate certificate : certificates) {
				Map<String, Object> traderCertificatesMap = new HashMap<>();
				traderCertificatesMap.put("message", "操作成功");
				traderCertificatesMap.put("httpUrl", api_http + certificate.getDomain());
				// uri
				String uri = certificate.getUri();
				if(EmptyUtils.isEmpty(uri)){
					continue;
				}
				String[] uriArray = uri.split("/");
				String fileName = uriArray[uriArray.length-1];
				String fileNameTemp = "/" + fileName;
				// 文件后缀
				String[] prefixArray = fileNameTemp.split("\\.");
				String prefix = prefixArray[prefixArray.length-1];
				// 去除路径名
				String filePath = uri.replaceAll(fileNameTemp, "");
				traderCertificatesMap.put("fileName", fileName);
				traderCertificatesMap.put("filePath", uri);
				traderCertificatesMap.put("prefix", prefix);
				traderCertificatesMap.put("domain",certificate.getDomain());
				traderCertificatesMap.put("suffix",prefix);
				maps.add(traderCertificatesMap);
			}
		}
		return maps;
	}
}
