let vm = null
const sendThis = (_this) => {
    vm = _this;
}


function getSysOptionDefinitionList(param) {
    return axios({
        url: '/sysOptionDefinition/getByParentCode.do',
        method: 'post',
        params: param
    })
}

function updateSelectOneData(value) {
    return axios({
        url: '/businessChance/selectOne.do',
        method: 'post',
        params: {bussinessChanceId: value}
    })
}

function leadsConverData(value) {
    return axios({
        url: '/businessLeads/getLeadsToChance.do',
        method: 'post',
        params: {id: value}
    })
}

/**
 * 获取渠道名称 原来的老接口 销售下的
 * @returns {*}
 */
function getCommunication(id) {
    return axios({
        url: '/order/bussinesschance/getRadioDataById.do',
        method: 'post',
        params: {"parentId":id},
        // headers: {
        //     'Content-Type': 'application/json;charset=utf-8'  //如果写成contentType会报错,如果不写这条也报错
        // }
    })
}

function getBusinesChanceTagList(type) {
    return axios({
        url: '/customTag/getUserTags.do',
        method: 'post',
        params: type
    })
}


function getBusinessTags() {
    return axios({
        url: '/customTag/getUserTags.do',
        method: 'post',
        params: {type: 2}
    })
}

function page(data) {
    return axios({
        url: '/businessChance/page.do',
        method: 'post',
        data: data
    })
}

function getTraderRemoteList(data) {
    return axios({
        url: '/traderCustomerBase/query.do',
        method: 'post',
        params: data
    })
}

function add(data) {
    return axios({
        url: '/businessChance/add.do',
        method: 'post',
        data: data
    })
}

function update(data) {
    return axios({
        url: '/businessChance/update.do',
        method: 'post',
        data: data
    })
}

function calc(data) {
    return axios({
        url: '/businessChance/calc.do',
        method: 'post',
        data: data
    });
}

function getTraderConcatDat(traderId) {
    var data = {"param": {"traderId": traderId}, "orderBy": 'TRADER_CONTACT_ID desc', "pageSize": 1000}
    return axios({
        url: '/traderContact/page.do',
        method: 'post',
        data: data,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

function getTraderCustomerInfo(traderId) {
    return axios({
        url: '/traderCustomerBase/getTraderCustomerInfo.do',
        method: 'post',
        params: {traderId:traderId}
    })
}

function selectGoods2(skuid) {
    if (window.parent && window.parent.selectGoods) {
        window.parent.selectGoods(skuid);
    }
}

function selectGoods2(goodsName, brandName, model, unitName) {
    layer.closeAll();
    var goods = {
        goodsName: goodsName,
        sku:"",
        goodsId:0,
        price:null,
        brandName: brandName,
        model: model,
        unitName: unitName,
        stockNum: 0,
        referenceDeliveryCycle: "",
        settlementPrice: "",
        channelPrice: "",
        deliveryDirect: false,
        haveInstallation: false
    };
    vm.businessChanceDto.businessChanceGoodsDtos.push(goods);
}

function getCommunicationTag() {
    return axios({
        url:"/tag/getTypeTags.do",
        method:"post",
        params:{isRecommend:true,tagType:32,companyId:1}
    })
}

/**
 * 区分页面哪个调用的新增联系人
 */
function changeNewConcatFlag(vaule) {
    vm.concatFlag = vaule;

}

// 老页面 联系人 回调方法
function updateTrader() {
    let traderId;
    if (vm.concatFlag == 2) {
        traderId = vm.businessChanceDetail.traderId;
    } else {
        traderId = vm.businessChanceDto.traderId;
    }

    getTraderConcatDat(traderId).then(res => {
        vm.traderConcatDatas = res.data.data.list;
        if (vm.concatFlag == 0) {

            debugger
            vm.businessChanceDto.traderContactName = vm.traderConcatDatas[0].name;
            vm.businessChanceDto.traderContactId = vm.traderConcatDatas[0].traderContactId;
            vm.businessChanceDto.mobile = vm.traderConcatDatas[0].mobile;
            vm.businessChanceDto.telephone = vm.traderConcatDatas[0].telephone;
            vm.businessChanceDto.traderContactNameShow = vm.traderConcatDatas[0].name+" "+vm.traderConcatDatas[0].mobile;
            vm.businessChanceDto.communicateRecordDto.traderContactNameView = vm.traderConcatDatas[0].name + " " + vm.traderConcatDatas[0].mobile + " " + vm.traderConcatDatas[0].telephone;
            vm.businessChanceDto.communicateRecordDto.traderContactId = vm.traderConcatDatas[0].traderContactId;
        } else if (vm.concatFlag == 1) {
            vm.concatFlag = 0;
            vm.businessChanceDto.communicateRecordDto.traderContactNameView = vm.traderConcatDatas[0].name + " " + vm.traderConcatDatas[0].mobile + " " + vm.traderConcatDatas[0].telephone;
            vm.businessChanceDto.communicateRecordDto.traderContactId = vm.traderConcatDatas[0].traderContactId;
        } else if (vm.concatFlag == 2) {
            vm.concatFlag = 0;
            debugger
            vm.communicateRecordDto.traderContactNameView = vm.traderConcatDatas[0].name + " " + vm.traderConcatDatas[0].mobile + " " + vm.traderConcatDatas[0].telephone;
            vm.communicateRecordDto.traderContactId = vm.traderConcatDatas[0].traderContactId;
        }
    });
}

function selectGoods(skuid, traderId) {
    layer.closeAll();
    console.log(skuid, parseInt(traderId));
    $.ajax({
        url: '/goods/goods/getSkuInfo.do',
        data: {
            skuIds: skuid,
            traderId: parseInt(traderId)
        },
        dataType: 'json',
        success: function (res) {

            // var prodTmpl = template($('.J-prod-tmpl').html());
            if (res.code == 0) if (checkSku(skuid)) {
                layer.alert('已选过该产品');
            } else {
                // 默认
                res.data[0].haveInstallation = false;
                if (res.data[0].isDirect == 1) {
                    res.data[0].deliveryDirect = true;
                } else {
                    res.data[0].deliveryDirect = false;
                }

                //res.data[0].price=null;
                vm.businessChanceDto.businessChanceGoodsDtos.push(res.data[0]);
                console.log(vm.businessChanceDto.businessChanceGoodsDtos)
                // checkProdList();
            } else {
                layer.alert(res.message);
            }
        }
    });
}

function checkSku(sku) {
    var flag = false;
    vm.businessChanceDto.businessChanceGoodsDtos.forEach(item => {
        if (item.goodsId == sku) {
            flag = true;
        }
    })
    return flag;
}



function getBusinessChanceDetail(param) {
    return axios({
        url: '/businessChance/getDetail.do',
        method: 'post',
        params: param
    })
}

function getCommunicationRecords(data) {
    return axios({
        url: '/communicateRecord/page.do',
        method: 'post',
        data: data
    })
}

function getSupportRecordList(data){
    return axios({
        url: '/support/getSupportRecordList.do',
        method: 'post',
        data: data
    })
}

function pageAddCommunication(data) {
    return axios({
        url: '/communicateRecord/add.do',
        method: 'post',
        data: data
    })
}

function pageUpdateCommunication(data) {
    return axios({
        url: '/communicateRecord/update.do',
        method: 'post',
        data: data
    })
}

function addSupport(data) {
    return axios({
        url: '/support/add.do',
        method: 'post',
        data: data
    })
}

function addRequest(data) {
    return axios({
        url: '/support/addRequest.do',
        method: 'post',
        data: data
    })
}

function getSupportListApi() {
    return axios({
        url: '/support/getSupportList.do',
        method: 'get',
    })
}

function getCommunicationData(data) {
    return axios({
        url: '/communicateRecord/getOne.do',
        method: 'post',
        data: data
    })
}

function getCloseVerifyInfo(data) {
    return axios({
        url: '/businessChance/getCloseVerifyInfo.do',
        method: 'post',
        params: data
    })
}
// 三级地区
function getCascaderRegionOptions() {
    return axios({
        url: '/region/getCascaderRegionOptions.do',
        method: 'get'
    })
}
function addTraderContract() {
    layer.config({
        extend: 'vedeng.com/style.css', //加载您的扩展样式
        skin: 'vedeng.com'
    });
    var layerParams = $("#concat").attr('layerParams');
    if (typeof(layerParams) == 'undefined') {
        alert('参数错误');
    } else {
        layerParams = $.parseJSON(layerParams);
    }
    var link = layerParams.link;
    if (link.indexOf("?") > 0 && (link.indexOf("?") + 1 == link.length)) {
        link += "pop=pop";
    } else if (link.indexOf("?") < 0) {
        link += "?pop=pop";
    } else if (link.indexOf("?") > 0 && (link.indexOf("?") + 1 != link.length)) {
        link += "&pop=pop";
    }
    var index = layer.open({
        type: 2,
        shadeClose: false, //点击遮罩关闭
        //area: 'auto',
        area: [layerParams.width, layerParams.height],
        title: layerParams.title,
        content: layerParams.noEncodeURI ? encodeURI(link): encodeURI(encodeURI(link)),
        success: function(layero, index) {
        }
    });
}


function getMergedChances(param) {
    return axios({
        url: '/businessChance/getMergeChance.do',
        method: 'post',
        params: param
    })
}

function getBidInfoByTraderId(param) {
    return axios({
        url: '/businessChance/getBidInfo.do',
        method: 'post',
        params: param
    })
}

function getTraderInfoByTraderId(param) {
    return axios({
        url: '/businessChance/getTraderInfoByTraderId.do',
        method: 'post',
        params: param
    })
}

function updateSingleData(param) {
    return axios({
        url: '/businessChance/updateSingleData.do',
        method: 'post',
        data: param
    })
}
function toTop(data) {
    return axios({
        url: '/businessChance/top.do',
        method: 'post',
        params: data
    })
}

function toUnTop(data) {
    return axios({
        url: '/businessChance/unTop.do',
        method: 'post',
        params: data
    })
}

function toAttention(data) {
    return axios({
        url: '/businessChance/attention.do',
        method: 'post',
        data: data
    })
}

function toCancelAttention(data) {
    return axios({
        url: '/businessChance/cancelAttention.do',
        method: 'post',
        params: data
    })
}

function getCommunicateRecord(data) {
    return axios({
        url: '/communicateRecord/page.do',
        method: 'post',
        data: data,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

function bussinessChanceCloseComplementTask(data) {
    return axios({
        url: '/businessChance/closeBusinessAudit.do',
        method: 'POST',
        data: data
    })
}

function getTraderContactDat(data) {
    return axios({
        url: '/traderContact/page.do',
        method: 'post',
        data: data,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

function getAccuracyEnum(data) {
    return axios({
        url: '/businessChance/getAccuracyEnum.do',
        method: 'post',
        data: data,
        headers: {
            'Content-Type': 'application/json'
        }
    })
}


function addCommunicateRecord(data) {
    return axios({
        url: '/communicateRecord/add.do',
        method: 'post',
        data: data
    })
}

function updateChanceStatus(id) {
    return axios({
        url: '/businessChance/updateBusinessChanceStatusByComm.do',
        method: 'post',
        params: id
    })
}

function getTerminalInfo(data) {
    return axios({
        url: '/order/terminal/businessId.do',
        method: 'post',
        params: data
    })
}

function shareBusinessChance(data) {
    return axios({
        url: '/businessChance/shareBusinessChance.do',
        method: 'post',
        data: data
    })
}

function getShareBusinessChance(data) {
    return axios({
        url: '/businessChance/getShareBusinessChance.do',
        method: 'post',
        params: data
    })
}

function cancelShareBusinessChance(data) {
    return axios({
        url: '/businessChance/cancelShareBusinessChance.do',
        method: 'post',
        params: data
    })
}

function getBusinessTerminalCategoriesAndTypes(){
    return axios({
        url: '/trader/customer/getBusinessTerminal.do',
        method: 'get'
    })
}

function getAreaSearchList(){
    return axios({
        url: '/traderCustomerBase/terminal/areaSearchList.do',
        method: 'get'
    })
}

function getAllAreaSearchList(){
    return axios({
        url: '/system/region/static/getAllRegion.do?parentId=100000&level=3',
        method: 'get'
    })
}

function getBusinessSupportWorkbench(data) {
    return axios.post('/businessChance/getBusinessSupportWorkbench.do', data);
}

function getConsultationPendingInfo(data) {
    return axios.post('/businessChance/getConsultationPendingInfo.do', data);
}

function processOrCloseSeekHelp(data) {
    return axios.post('/businessChance/processOrCloseSeekHelp.do', data);
}