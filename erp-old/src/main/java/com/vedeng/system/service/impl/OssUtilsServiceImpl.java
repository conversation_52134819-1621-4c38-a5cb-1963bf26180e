package com.vedeng.system.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.OssInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.common.util.UrlUtils;
import com.vedeng.file.api.constants.TerminationType;
import com.vedeng.file.api.request.UploadConstantsMapKey;
import com.vedeng.file.api.util.SignUtil;
import com.vedeng.system.model.vo.UrlToPdfParam;
import com.vedeng.system.service.OssUtilsService;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.Random;

/**
 * <AUTHOR>
 * @date created in 2020/3/5 11:49
 */
@Service
public class OssUtilsServiceImpl implements OssUtilsService {

    Logger logger= LoggerFactory.getLogger(OssUtilsServiceImpl.class);

    @Autowired
    private RestTemplate restTemplate;

    @Value("${oss_http}")
    private String ossHttp;

    /**
     * OSS地址
     */
    @Value("${oss_url}")
    private String ossUrl;
    /**
     * oss秘钥
     */
    @Value("${oss_key}")
    private String ossKey;
    /**
     * oss应用码
     */
    @Value("${oss_app_code}")
    private String ossAppCode;
    /**
     * oss文档路径
     */
    @Value("${oss_file_path}")
    private String ossFilePath;
    @Override
    public FileInfo upload2Oss(HttpServletRequest request, MultipartFile upfile) {
        String fileName = upfile.getOriginalFilename();
        // 文件后缀名称
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        try {
            return uploadFile2Oss(request,upfile,suffix,false);
        }catch (Exception ex){
            logger.error("图片上传OSS失败，请重新上传",ex);
            return new FileInfo(-1,"上传失败，请重新上传");
        }
    }

    /**
     *
     * @param request
     * @param upfile
     * @param compress 是否压缩
     * @return
     */
    @Override
    public FileInfo upload2Oss(HttpServletRequest request, MultipartFile upfile,boolean compress) {
        String fileName = upfile.getOriginalFilename();
        // 文件后缀名称
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        try {
            return uploadFile2Oss(request,upfile,suffix,true);
        }catch (Exception ex){
            logger.error("图片上传OSS失败，请重新上传",ex);
            return new FileInfo(-1,"上传失败，请重新上传");
        }
    }


    private FileInfo uploadFile2Oss(HttpServletRequest request, MultipartFile oldfile, String suffix,boolean compress) throws IOException {

        String time = DateUtil.gainNowDate() + "";
        Random random = new Random();
        time = time + "_" + String.format("%04d", random.nextInt(10000));// 随机数4位，不足4位，补位
        String newFileName=time + "." + suffix;

        String[] splitFileName = oldfile.getOriginalFilename().split("." + suffix);
        MultipartFile file;
        if(splitFileName != null && splitFileName.length > 0){
            String orgfileName = splitFileName[0];
            file = getNewFile(orgfileName, oldfile);
        }else{
            file = oldfile;
        }

        long timestamp = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        String authorization = SignUtil.sign(ossKey,(ossAppCode+timestamp).getBytes());
        TypeReference<ResultInfo> resultType = new TypeReference<ResultInfo>() {};
        String reqUrl = ossHttp + ossUrl + ossFilePath;
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        builder.setCharset(StandardCharsets.UTF_8);
        builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
        //封装请求body
        builder.addBinaryBody("file",file.getInputStream(), ContentType.create("multipart/form-data","utf-8"),file.getOriginalFilename());
        // 类似浏览器表单提交，对应input的name和value
        builder.addTextBody(UploadConstantsMapKey.appCode,ossAppCode);
        builder.addTextBody(UploadConstantsMapKey.Authorization, authorization);
        builder.addTextBody(UploadConstantsMapKey.timestamp, String.valueOf(timestamp));
        builder.addTextBody(UploadConstantsMapKey.deviceInfo,request.getHeader("User-Agent"));
        builder.addTextBody(UploadConstantsMapKey.suffix,suffix);
        builder.addTextBody(UploadConstantsMapKey.previlege, "0");
        builder.addTextBody(UploadConstantsMapKey.termination, TerminationType.PC.getCode());
        //是否需要压缩，如果是，file会压缩默认的尺寸
        builder.addTextBody(UploadConstantsMapKey.isNeedCompress, compress+"");

        //发送请求
        org.apache.http.HttpEntity entity = builder.build();
        HttpPost httpPost = new HttpPost(reqUrl);
        httpPost.setEntity(entity);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpResponse response = httpClient.execute(httpPost);

        //解析返回的结果，获取ossUrl和resourceId
        JSONObject jsonObject = JSONObject.fromObject(EntityUtils.toString(response.getEntity()));
        logger.info("pic upload:"+jsonObject==null?"":jsonObject.toString());
        boolean result = Boolean.parseBoolean(jsonObject.getString("success"));

        if (result) {

            JSONObject data = JSONObject.fromObject(jsonObject.get("data"));
            String ossFileUrl = data.getString("url");
            String resourceId = data.getString("resourceId");
            // VDERP-7467 保存文件后缀到redis,新增attachment时，需要保存suffix
            JedisUtils.set(ErpConst.KEY_PREFIX_ATTACHMENT + resourceId, suffix, 86400);
            String[] domainAndUri= UrlUtils.getDomainAndUriFromUrl(ossFileUrl);
            if(domainAndUri!=null&& StringUtil.isNotBlank(domainAndUri[0])&&StringUtil.isNotBlank(domainAndUri[1])) {
                FileInfo fileInfo = new FileInfo(0, "上传成功",newFileName, domainAndUri[1],
                        domainAndUri[0], resourceId, null);
                fileInfo.setPrefix(suffix);
                //size按照KB计算
//                fileInfo.setFileSize(new BigDecimal((oldfile.getSize()/1000)+"").setScale(2,BigDecimal.ROUND_HALF_UP));
//                JedisUtils.set(ErpConst.KEY_PREFIX_ATTACHMENT_SIZE + resourceId, fileInfo.getFileSize()+"", 86400);
                //TODO 如果是图片，设置 宽高 或者定时任务计算
                try{

                }catch (Exception e){
                }
                return fileInfo;
            }

        }
        logger.error("pic upload:"+jsonObject==null?"":jsonObject.toString());
        return new FileInfo(-1,"上传失败，文件服务异常："+jsonObject.getString("message"));
    }


    @Override
    public String uploadFileStream2Oss(InputStream inputStream, String suffixOfFile){

        long timestamp = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        String authorization = SignUtil.sign(ossKey,(ossAppCode+timestamp).getBytes());
        String reqUrl = ossHttp + ossUrl + ossFilePath;
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        //封装请求body
        String originFileName = RandomStringUtils.random(50,"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789");
        builder.addBinaryBody("file",inputStream, ContentType.MULTIPART_FORM_DATA,originFileName);
        // 类似浏览器表单提交，对应input的name和value
        builder.addTextBody(UploadConstantsMapKey.appCode,ossAppCode);
        builder.addTextBody(UploadConstantsMapKey.Authorization, authorization);
        builder.addTextBody(UploadConstantsMapKey.timestamp, String.valueOf(timestamp));
        builder.addTextBody(UploadConstantsMapKey.deviceInfo,"pc");
        builder.addTextBody(UploadConstantsMapKey.suffix,suffixOfFile);
        builder.addTextBody(UploadConstantsMapKey.previlege, "0");
        builder.addTextBody(UploadConstantsMapKey.termination, TerminationType.PC.getCode());
        //发送请求
        org.apache.http.HttpEntity entity = builder.build();
        HttpPost httpPost = new HttpPost(reqUrl);
        httpPost.setEntity(entity);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            HttpResponse response = httpClient.execute(httpPost);
            //解析返回的结果，获取ossUrl和resourceId
            JSONObject jsonObject = JSONObject.fromObject(EntityUtils.toString(response.getEntity()));

            boolean result = Boolean.parseBoolean(jsonObject.getString("success"));
            if (result) {
                JSONObject data = JSONObject.fromObject(jsonObject.get("data"));
                return data.getString("url");
            }
        } catch (IOException e) {
            logger.error("pic upload: e", e);
        }
        return null;
    }


    @Override
    public String upload2OssForInputStream(String suffix, String newFileName, String ossTargetUrl, InputStream inputStream) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
        long timestamp = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        queryParams.add("appCode",ossAppCode);
        String authorization = SignUtil.sign(ossKey,(ossAppCode+timestamp).getBytes());
        queryParams.add("Authorization",authorization);
        queryParams.add("timestamp",String.valueOf(timestamp));
        queryParams.add("deviceInfo","pc");
        queryParams.add("sourceId","erp");
        queryParams.add("suffix",suffix);
        queryParams.add("previlege","0");
        queryParams.add("termination", TerminationType.PC.getCode());
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder
                .fromHttpUrl(ossTargetUrl)
                .queryParams(queryParams);

        MultiValueMap<String,Object> bodyMap = new LinkedMultiValueMap<>();
        Resource resource = new InputStreamResource(inputStream){

            @Override
            public String getFilename(){
                return newFileName;
            }

            @Override
            public long contentLength() throws IOException {
                return inputStream.available();
            }
        };
        bodyMap.add("file",resource);
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(bodyMap,headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(uriComponentsBuilder.build().encode().toUri(),httpEntity,String.class);

        //解析返回的结果，获取ossUrl和resourceId
        JSONObject jsonObject = JSONObject.fromObject(responseEntity.getBody());
        boolean result = Boolean.parseBoolean(jsonObject.getString("success"));

        if (!result){
            return null;
        }
        JSONObject data = JSONObject.fromObject(jsonObject.get("data"));
        return data.getString("url");
    }

    @Override
    public String migrateFile2Oss(String originSourceUrl, String suffix, String fileName, UrlToPdfParam urlToPdfParam) {
        RequestCallback requestCallback = request -> {
            request.getHeaders().setContentType(MediaType.APPLICATION_JSON);
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.writeValue(request.getBody(),urlToPdfParam);
        };

        ResponseExtractor<String> responseExtractor = response -> {
            //发送到oss
            return upload2OssForInputStream("pdf",fileName,ossHttp+ossUrl+ossFilePath,response.getBody());
        } ;
        try {
            return restTemplate.execute(originSourceUrl, HttpMethod.POST, requestCallback, responseExtractor);
        } catch (RestClientException ex){
            logger.warn("请求访问URL：{}，文件：{}，异常：",originSourceUrl,fileName,ex);
            return null;
        } catch (Exception e){
            logger.error("请求访问URL：{}，文件：{}，异常：",originSourceUrl,fileName,e);
            return null;
        }
    }

    private MultipartFile getNewFile(String fileName, MultipartFile currentFile){
        return new MultipartFile() {
            @Override
            public String getName() {
                return currentFile.getName();
            }

            @Override
            public String getOriginalFilename() {
                return fileName;
            }

            @Override
            public String getContentType() {
                return currentFile.getContentType();
            }

            @Override
            public boolean isEmpty() {
                return currentFile.isEmpty();
            }

            @Override
            public long getSize() {
                return currentFile.getSize();
            }

            @Override
            public byte[] getBytes() throws IOException {
                return currentFile.getBytes();
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return currentFile.getInputStream();
            }

            @Override
            public void transferTo(File file) throws IOException, IllegalStateException {

            }
        };
    }



    /**
     * @description: fileSourceUrl 获取文件后缀  inputStream 文件字节上传oss  compress是否压缩
     * @return: OssInfo
     * @author: Strange
     * @date: 2021/1/8
     **/
    public OssInfo sendFile2Oss(String fileSourceUrl, InputStream inputStream, boolean compress){
        OssInfo ossInfo = new OssInfo();
        if(inputStream == null){
            logger.info("sendFile2Oss inputStream为空 path:{}",fileSourceUrl);
            return ossInfo;
        }
        String ossTargetUrl = ossHttp + ossUrl + ossFilePath;
        Instant start = Instant.now();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
        String[] urlArray = fileSourceUrl.split("\\.");
        String suffix = urlArray[urlArray.length-1];
        long timestamp = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        queryParams.add(UploadConstantsMapKey.appCode,ossAppCode);
        String authorization = SignUtil.sign(ossKey,(ossAppCode+timestamp).getBytes());
        queryParams.add(UploadConstantsMapKey.Authorization,authorization);
        queryParams.add(UploadConstantsMapKey.timestamp,String.valueOf(timestamp));
        queryParams.add(UploadConstantsMapKey.deviceInfo,"pc");
        queryParams.add("sourceId","erp");
        queryParams.add(UploadConstantsMapKey.suffix,suffix);
        queryParams.add(UploadConstantsMapKey.previlege,"0");
        queryParams.add(UploadConstantsMapKey.termination, TerminationType.PC.getCode());
        //是否需要压缩，如果是，file会压缩默认的尺寸
        queryParams.add(UploadConstantsMapKey.isNeedCompress, compress+"");

        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder
                .fromHttpUrl(ossTargetUrl)
                .queryParams(queryParams);

        //文件名
        String fileName = fileSourceUrl.substring( fileSourceUrl.lastIndexOf("/", fileSourceUrl.length())+1, fileSourceUrl.length());

        MultiValueMap<String,Object> bodyMap = new LinkedMultiValueMap<>();
        Resource resource = new InputStreamResource(inputStream){

            @Override
            public String getFilename(){
                return fileName;
            }

            @Override
            public long contentLength() throws IOException {
                return inputStream.available();
            }
        };
        bodyMap.add("file",resource);
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(bodyMap,headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(uriComponentsBuilder.build().encode().toUri(),httpEntity,String.class);

        logger.info("上传文件到OSS，响应结果：{}",responseEntity.getBody());

        //解析返回的结果，获取ossUrl和resourceId
        JSONObject jsonObject = JSONObject.fromObject(responseEntity.getBody());
        boolean result = Boolean.parseBoolean(jsonObject.getString("success"));

        if (result){
            logger.info("成功上传文件{}到OSS，耗时:{}，响应结果：{}",fileSourceUrl, ChronoUnit.MILLIS.between(start, Instant.now()),responseEntity.getBody());
            JSONObject data = JSONObject.fromObject(jsonObject.get("data"));
            String ossFileUrl = data.getString("url");
            String resourceId = data.getString("resourceId");
            // VDERP-7467 保存文件后缀到redis,新增attachment时，需要保存suffix
            JedisUtils.set(ErpConst.KEY_PREFIX_ATTACHMENT + resourceId, suffix, 86400);
//            try {
//                JedisUtils.set(ErpConst.KEY_PREFIX_ATTACHMENT_SIZE + resourceId, ""+(resource.contentLength() / 1000), 86400);
//            }catch (Exception e){}
            if (!ossFileUrl.startsWith(ossHttp)){
                logger.error("上传文件：{}，OSS响应的结果有误：{}",fileSourceUrl,ossFileUrl);
                return ossInfo;
            }
            String domainAndUri = ossFileUrl.split(ossHttp)[1];
            int domainIndex = domainAndUri.indexOf("/");
            String domain = domainAndUri.substring(0,domainIndex);
            String uri = domainAndUri.substring(domainIndex);
            ossInfo.setCode(0);
            ossInfo.setOssFileUrl(ossFileUrl);
            ossInfo.setResourceId(resourceId);
            ossInfo.setFileSourceUrl(fileSourceUrl);
            ossInfo.setDomain(domain);
            ossInfo.setUri(uri);
        }
        return ossInfo;
    }
}
