package com.wms.service.stockcalculate;

import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.dto.StockCalculateDto;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PurchaseExgInputCaculateImpl extends AbstractStockinfoCaculateInterace{

    @Override
    protected void calculateStockNum(List<StockCalculateDto> details, List<WarehouseDto> warehouseStockList) {
        /*details.stream().forEach(stockCalculateDto->{

            WarehouseDto warehouseStock = super.getWarehouseStockBySkuAndLogcic(warehouseStockList,
                    stockCalculateDto.getSku(),
                    stockCalculateDto.getLogicalWarehouseId());

            //增加总数量
            warehouseStock.setStockNum(Math.abs(stockCalculateDto.getStockNum()));
        });*/
        super.calculateStockOrOccupyNumBase(details, warehouseStockList, false,true);
    }

    @Override
    protected void calculateOcupyNum(List<StockCalculateDto> details, List<WarehouseDto> warehouseStockList) {


    }

}
