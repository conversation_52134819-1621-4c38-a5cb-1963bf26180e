<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.quote.mapper.NewQuoteMapper">


    <update id="updateQuoteTerminalInfo" parameterType="com.vedeng.erp.quote.domain.UpdateTerminalInfoDto">
      update T_QUOTEORDER
      set
        <if test="traderName != null">
            TRADER_NAME = #{traderName},
        </if>
        <if test="areaStr != null">
            AREA = #{areaStr}
        </if>
      where
        QUOTEORDER_ID = ${quoteorderId}
    </update>
    <update id="updateOrderTerminalInfo" parameterType="com.vedeng.erp.quote.domain.UpdateTerminalInfoDto">
      update T_SALEORDER
      set
        <if test="areaStr != null">
            SALES_AREA = #{areaStr},
        </if>
        <if test="areaId != null">
            SALES_AREA_ID = #{areaId},
        </if>
        <if test="terminalTraderNature != null">
            TERMINAL_TRADER_NATURE = #{terminalTraderNature,jdbcType=INTEGER},
        </if>
        TERMINAL_TRADER_NAME = #{traderName}
      where
        SALEORDER_ID = ${saleorderId}
    </update>

    <select id="getOrderTraderInfoByQuoteId" resultType="com.vedeng.erp.quote.domain.UpdateTerminalInfoDto"
            parameterType="java.lang.Integer">

        SELECT
            s.QUOTEORDER_ID,
            t.TRADER_NAME,
            t.AREA_ID ,
            tc.CUSTOMER_TYPE
        FROM
            T_QUOTEORDER s
            LEFT JOIN T_TRADER t ON s.TRADER_ID = t.TRADER_ID
            LEFT JOIN T_TRADER_CUSTOMER tc ON tc.TRADER_ID = t.TRADER_ID
        WHERE
            s.QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}

    </select>
    <select id="getOrderTraderInfoBySaleorderId" resultType="com.vedeng.erp.quote.domain.UpdateTerminalInfoDto"
            parameterType="java.lang.Integer">

         SELECT
            s.SALEORDER_ID,
            t.TRADER_NAME,
            t.AREA_ID ,
            tc.CUSTOMER_TYPE
        FROM
            T_SALEORDER s
            LEFT JOIN T_TRADER t ON s.TRADER_ID = t.TRADER_ID
            LEFT JOIN T_TRADER_CUSTOMER tc ON tc.TRADER_ID = t.TRADER_ID
        WHERE
            s.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}

    </select>

</mapper>