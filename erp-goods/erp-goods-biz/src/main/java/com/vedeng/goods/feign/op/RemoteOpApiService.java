package com.vedeng.goods.feign.op;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import com.vedeng.op.api.dto.category.QueryCategoryRequest;
import com.vedeng.op.api.dto.hotword.HotWordListDTO;
import feign.Headers;
import feign.Param;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/1/18 16:06
 */
@FeignApi(serverName = ServerConstants.OP_SERVER)
public interface RemoteOpApiService {

    @RequestLine("GET /op/getAllSkuNos?platformId={platformId}")
    RestfulResult<List<String>> getAllSkuNos(@Param("platformId") Integer platformId);

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /hotword/queryHotWordByCategoryId")
    RestfulResult<List<HotWordListDTO>> queryHotWordListByCategoryId(@RequestBody QueryCategoryRequest var1);
}
