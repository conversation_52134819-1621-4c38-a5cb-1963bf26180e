<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.CategoryAttrValueMappingGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.CategoryAttrValueMappingGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <id column="CATEGORY_ATTR_VALUE_MAPPING_ID" jdbcType="INTEGER" property="categoryAttrValueMappingId" />
    <result column="BASE_CATEGORY_ID" jdbcType="INTEGER" property="baseCategoryId" />
    <result column="BASE_ATTRIBUTE_ID" jdbcType="INTEGER" property="baseAttributeId" />
    <result column="BASE_ATTRIBUTE_VALUE_ID" jdbcType="INTEGER" property="baseAttributeValueId" />
    <result column="IS_DELETED" jdbcType="TINYINT" property="isDeleted" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    CATEGORY_ATTR_VALUE_MAPPING_ID, BASE_CATEGORY_ID, BASE_ATTRIBUTE_ID, BASE_ATTRIBUTE_VALUE_ID, 
    IS_DELETED, CREATOR, UPDATER, MOD_TIME, ADD_TIME
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.goods.model.CategoryAttrValueMappingGenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from V_CATEGORY_ATTR_VALUE_MAPPING
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from V_CATEGORY_ATTR_VALUE_MAPPING
    where CATEGORY_ATTR_VALUE_MAPPING_ID = #{categoryAttrValueMappingId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from V_CATEGORY_ATTR_VALUE_MAPPING
    where CATEGORY_ATTR_VALUE_MAPPING_ID = #{categoryAttrValueMappingId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.goods.model.CategoryAttrValueMappingGenerateExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from V_CATEGORY_ATTR_VALUE_MAPPING
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.CategoryAttrValueMappingGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="categoryAttrValueMappingId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into V_CATEGORY_ATTR_VALUE_MAPPING (BASE_CATEGORY_ID, BASE_ATTRIBUTE_ID, 
      BASE_ATTRIBUTE_VALUE_ID, IS_DELETED, CREATOR, 
      UPDATER, MOD_TIME, ADD_TIME
      )
    values (#{baseCategoryId,jdbcType=INTEGER}, #{baseAttributeId,jdbcType=INTEGER}, 
      #{baseAttributeValueId,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, #{addTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.CategoryAttrValueMappingGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="categoryAttrValueMappingId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into V_CATEGORY_ATTR_VALUE_MAPPING
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="baseCategoryId != null">
        BASE_CATEGORY_ID,
      </if>
      <if test="baseAttributeId != null">
        BASE_ATTRIBUTE_ID,
      </if>
      <if test="baseAttributeValueId != null">
        BASE_ATTRIBUTE_VALUE_ID,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="baseCategoryId != null">
        #{baseCategoryId,jdbcType=INTEGER},
      </if>
      <if test="baseAttributeId != null">
        #{baseAttributeId,jdbcType=INTEGER},
      </if>
      <if test="baseAttributeValueId != null">
        #{baseAttributeValueId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.goods.model.CategoryAttrValueMappingGenerateExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select count(*) from V_CATEGORY_ATTR_VALUE_MAPPING
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update V_CATEGORY_ATTR_VALUE_MAPPING
    <set>
      <if test="record.categoryAttrValueMappingId != null">
        CATEGORY_ATTR_VALUE_MAPPING_ID = #{record.categoryAttrValueMappingId,jdbcType=INTEGER},
      </if>
      <if test="record.baseCategoryId != null">
        BASE_CATEGORY_ID = #{record.baseCategoryId,jdbcType=INTEGER},
      </if>
      <if test="record.baseAttributeId != null">
        BASE_ATTRIBUTE_ID = #{record.baseAttributeId,jdbcType=INTEGER},
      </if>
      <if test="record.baseAttributeValueId != null">
        BASE_ATTRIBUTE_VALUE_ID = #{record.baseAttributeValueId,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        IS_DELETED = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update V_CATEGORY_ATTR_VALUE_MAPPING
    set CATEGORY_ATTR_VALUE_MAPPING_ID = #{record.categoryAttrValueMappingId,jdbcType=INTEGER},
      BASE_CATEGORY_ID = #{record.baseCategoryId,jdbcType=INTEGER},
      BASE_ATTRIBUTE_ID = #{record.baseAttributeId,jdbcType=INTEGER},
      BASE_ATTRIBUTE_VALUE_ID = #{record.baseAttributeValueId,jdbcType=INTEGER},
      IS_DELETED = #{record.isDeleted,jdbcType=TINYINT},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.CategoryAttrValueMappingGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update V_CATEGORY_ATTR_VALUE_MAPPING
    <set>
      <if test="baseCategoryId != null">
        BASE_CATEGORY_ID = #{baseCategoryId,jdbcType=INTEGER},
      </if>
      <if test="baseAttributeId != null">
        BASE_ATTRIBUTE_ID = #{baseAttributeId,jdbcType=INTEGER},
      </if>
      <if test="baseAttributeValueId != null">
        BASE_ATTRIBUTE_VALUE_ID = #{baseAttributeValueId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where CATEGORY_ATTR_VALUE_MAPPING_ID = #{categoryAttrValueMappingId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.CategoryAttrValueMappingGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update V_CATEGORY_ATTR_VALUE_MAPPING
    set BASE_CATEGORY_ID = #{baseCategoryId,jdbcType=INTEGER},
      BASE_ATTRIBUTE_ID = #{baseAttributeId,jdbcType=INTEGER},
      BASE_ATTRIBUTE_VALUE_ID = #{baseAttributeValueId,jdbcType=INTEGER},
      IS_DELETED = #{isDeleted,jdbcType=TINYINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
    where CATEGORY_ATTR_VALUE_MAPPING_ID = #{categoryAttrValueMappingId,jdbcType=INTEGER}
  </update>
</mapper>