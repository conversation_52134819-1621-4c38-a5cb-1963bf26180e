package com.vedeng.temporal.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import com.vedeng.temporal.domain.entity.FlowOrderInfoEntity;
import com.vedeng.temporal.mapper.TemporalFlowOrderInfoMapper;
import com.vedeng.temporal.util.SystemApiClient;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 流转单信息状态更新服务
 * 专注于FlowOrderInfo记录的状态更新，不再负责记录创建
 * 记录创建已迁移到SalesOrderStep和PurchaseOrderStep中
 *
 * <AUTHOR> 4.0 sonnet
 * @since 2025-01-29
 */
@Service
@Slf4j
public class FlowOrderInfoGenerationService {

    // 业务类型常量
    private static final int PURCHASE_TYPE = 0; // 采购
    private static final int SALES_TYPE = 1; // 销售

    @Autowired
    private TemporalFlowOrderInfoMapper temporalFlowOrderInfoMapper;

    @Autowired
    private FlowNodeBasedCompanyService flowNodeBasedCompanyService;

    @Autowired
    private SystemApiClient systemApiClient;

    /**
     * 更新所有ERP方向FlowOrderInfo记录的状态
     * 不再创建记录，只更新已存在记录的状态信息
     *
     * @return 处理结果统计
     */
    @Transactional(rollbackFor = Exception.class)
    public ProcessResult updateAllErpFlowOrderInfoStatus() {
        log.info("开始更新所有ERP方向FlowOrderInfo记录的状态");

        ProcessResult result = new ProcessResult();

        try {
            // 1. 查询所有需要更新状态的FlowOrderInfo记录
            List<FlowOrderInfoEntity> flowOrderInfos = temporalFlowOrderInfoMapper.selectInconsistentRecords();

            if (CollectionUtils.isEmpty(flowOrderInfos)) {
                log.info("未找到需要更新状态的FlowOrderInfo记录");
                return result;
            }

            result.setTotalCount(flowOrderInfos.size());
            log.info("找到 {} 个需要更新状态的FlowOrderInfo记录", flowOrderInfos.size());

            // 2. 遍历更新每个记录的状态
            for (FlowOrderInfoEntity flowOrderInfo : flowOrderInfos) {
                try {
                    boolean success = updateFlowOrderInfoStatusInternal(flowOrderInfo);
                    if (success) {
                        result.incrementSuccess();
                        log.info("FlowOrderInfo状态更新成功: {}", flowOrderInfo.getFlowOrderInfoNo());
                    } else {
                        result.incrementFailure();
                        log.warn("FlowOrderInfo状态更新失败: {}", flowOrderInfo.getFlowOrderInfoNo());
                    }
                } catch (Exception e) {
                    result.incrementFailure();
                    log.error("更新FlowOrderInfo状态异常: {}", flowOrderInfo.getFlowOrderInfoNo(), e);
                }
            }

            log.info("所有FlowOrderInfo状态更新完成 - 总数: {}, 成功: {}, 失败: {}",
                    result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());

            return result;

        } catch (Exception e) {
            log.error("更新所有FlowOrderInfo状态异常", e);
            throw e;
        }
    }

    /**
     * 更新指定流转单的所有FlowOrderInfo记录的状态
     *
     * @param flowOrderNo 流转单编号
     * @return 处理结果统计
     */
    @Transactional(rollbackFor = Exception.class)
    public ProcessResult updateSingleFlowOrderInfoStatus(String flowOrderNo) {
        log.info("开始更新流转单的所有FlowOrderInfo状态: {}", flowOrderNo);

        ProcessResult result = new ProcessResult();

        try {
            // 1. 根据流转单编号查询所有相关的FlowOrderInfo记录
            List<FlowOrderInfoEntity> flowOrderInfos = temporalFlowOrderInfoMapper.selectByFlowOrderNo(flowOrderNo);

            if (CollectionUtils.isEmpty(flowOrderInfos)) {
                log.warn("未找到流转单相关的FlowOrderInfo记录: {}", flowOrderNo);
                return result;
            }

            result.setTotalCount(flowOrderInfos.size());
            log.info("找到流转单 {} 相关的 {} 个FlowOrderInfo记录", flowOrderNo, flowOrderInfos.size());

            // 2. 遍历更新每个记录的状态
            for (FlowOrderInfoEntity flowOrderInfo : flowOrderInfos) {
                try {
                    boolean success = updateFlowOrderInfoStatusInternal(flowOrderInfo);
                    if (success) {
                        result.incrementSuccess();
                        log.info("FlowOrderInfo状态更新成功: {} (流转单: {})", 
                                flowOrderInfo.getFlowOrderInfoNo(), flowOrderNo);
                    } else {
                        result.incrementFailure();
                        log.warn("FlowOrderInfo状态更新失败: {} (流转单: {})", 
                                flowOrderInfo.getFlowOrderInfoNo(), flowOrderNo);
                    }
                } catch (Exception e) {
                    result.incrementFailure();
                    log.error("更新FlowOrderInfo状态异常: {} (流转单: {})", 
                            flowOrderInfo.getFlowOrderInfoNo(), flowOrderNo, e);
                }
            }

            log.info("流转单 {} 的FlowOrderInfo状态更新完成 - 总数: {}, 成功: {}, 失败: {}",
                    flowOrderNo, result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());

            return result;

        } catch (Exception e) {
            log.error("更新流转单FlowOrderInfo状态异常: {}", flowOrderNo, e);
            // 如果查询出错，返回失败结果
            result.setTotalCount(1);
            result.incrementFailure();
            return result;
        }
    }

    /**
     * 更新FlowOrderInfo记录状态的内部逻辑
     *
     * @param flowOrderInfo FlowOrderInfo实体
     * @return 是否更新成功
     */
    private boolean updateFlowOrderInfoStatusInternal(FlowOrderInfoEntity flowOrderInfo) {
        String flowOrderInfoNo = flowOrderInfo.getFlowOrderInfoNo();
        Long flowNodeId = flowOrderInfo.getFlowNodeId();
        Integer businessType = flowOrderInfo.getFlowOrderInfoType();

        log.info("开始更新FlowOrderInfo状态: {} - {} - {}", flowOrderInfoNo, flowNodeId, businessType);

        try {
            // 获取关联的公司代码
            String companyCode = getCompanyCodeByFlowNodeId(flowNodeId);
            if (!StringUtils.hasText(companyCode)) {
                log.warn("无法获取公司代码，flowNodeId: {}", flowNodeId);
                return false;
            }

            // 调用API更新订单状态信息
            boolean updateSuccess = updateOrderStatusFromApi(flowOrderInfo, companyCode);
            
            if (updateSuccess) {
                log.info("FlowOrderInfo状态更新成功: {}", flowOrderInfoNo);
                return true;
            } else {
                log.warn("FlowOrderInfo状态更新失败: {}", flowOrderInfoNo);
                return false;
            }

        } catch (Exception e) {
            log.error("更新FlowOrderInfo状态异常: {}", flowOrderInfoNo, e);
            return false;
        }
    }

    /**
     * 根据flowNodeId获取公司代码
     *
     * @param flowNodeId 流程节点ID
     * @return 公司代码
     */
    private String getCompanyCodeByFlowNodeId(Long flowNodeId) {
        try {
            return flowNodeBasedCompanyService.getCompanyCodeByFlowNodeId(flowNodeId);
        } catch (Exception e) {
            log.error("根据flowNodeId获取公司代码异常，flowNodeId: {}", flowNodeId, e);
            return null;
        }
    }

    /**
     * 从API更新订单状态信息
     * 根据业务类型调用相应的查询接口获取最新状态
     *
     * @param flowOrderInfo FlowOrderInfo实体
     * @param companyCode   公司代码
     * @return 是否更新成功
     */
    private boolean updateOrderStatusFromApi(FlowOrderInfoEntity flowOrderInfo, String companyCode) {
        String flowOrderInfoNo = flowOrderInfo.getFlowOrderInfoNo();
        Long flowNodeId = flowOrderInfo.getFlowNodeId();
        Integer businessType = flowOrderInfo.getFlowOrderInfoType();

        log.info("开始从API更新订单状态: {} - {} - {}", flowOrderInfoNo, flowNodeId, companyCode);

        try {
            // 构建查询请求
            Map<String, Object> queryRequest = buildStatusQueryRequest(flowOrderInfo);

            String apiResult = null;
            String operationName = null;

            // 根据业务类型调用不同的API
            if (PURCHASE_TYPE == businessType) {
                // 调用采购查询接口
                apiResult = systemApiClient
                        .withCompany(companyCode)
                        .withUser("system")
                        .postToSystemApi("/api/v1/buyorder/query.do", queryRequest);
                operationName = "采购状态查询";
            } else if (SALES_TYPE == businessType) {
                // 调用销售查询接口
                apiResult = systemApiClient
                        .withCompany(companyCode)
                        .withUser("system")
                        .postToSystemApi("/api/v1/saleorder/query.do", queryRequest);
                operationName = "销售状态查询";
            } else {
                log.warn("未知的业务类型: {}", businessType);
                return false;
            }

            // 解析API结果并更新状态
            boolean success = updateFlowOrderInfoFromApiResult(
                    flowNodeId, businessType, apiResult, operationName);

            if (success) {
                log.info("从API更新订单状态成功: {} - {}", flowOrderInfoNo, operationName);
            } else {
                log.warn("从API更新订单状态失败: {} - {}", flowOrderInfoNo, operationName);
            }

            return success;

        } catch (Exception e) {
            log.error("从API更新订单状态异常: {} - {}", flowOrderInfoNo, companyCode, e);
            return false;
        }
    }

    /**
     * 构建状态查询请求
     *
     * @param flowOrderInfo FlowOrderInfo实体
     * @return 查询请求参数
     */
    private Map<String, Object> buildStatusQueryRequest(FlowOrderInfoEntity flowOrderInfo) {
        Map<String, Object> queryRequest = new HashMap<>();

        Integer businessType = flowOrderInfo.getFlowOrderInfoType();
        String orderNo = flowOrderInfo.getFlowOrderInfoNo();

        // 根据业务类型设置不同的参数名
        if (PURCHASE_TYPE == businessType) {
            // 采购类型使用 buyorderNo
            queryRequest.put("buyorderNo", orderNo);
        } else if (SALES_TYPE == businessType) {
            // 销售类型使用 saleOrderNo
            queryRequest.put("saleOrderNo", orderNo);
        } else {
            // 未知类型，使用通用参数名
            queryRequest.put("orderNo", orderNo);
        }

        queryRequest.put("flowNodeId", flowOrderInfo.getFlowNodeId());

        return queryRequest;
    }

    /**
     * 根据API返回结果更新FlowOrderInfo状态
     * 使用ObjectMapper解析标准ApiResponse格式
     *
     * @param flowNodeId    流程节点ID
     * @param businessType  业务类型
     * @param apiResult     API返回结果
     * @param operationName 操作名称（用于日志）
     * @return 是否更新成功
     */
    private boolean updateFlowOrderInfoFromApiResult(Long flowNodeId,
                                                     Integer businessType,
                                                     String apiResult,
                                                     String operationName) {
        log.info("开始解析{}API结果: {} - {}", operationName, flowNodeId, businessType);

        try {
            if (!StringUtils.hasText(apiResult)) {
                log.warn("{}API返回结果为空: {} - {}", operationName, flowNodeId, businessType);
                return false;
            }

            // 通过ErpSpringBeanUtil获取ObjectMapper
            ObjectMapper objectMapper = ErpSpringBeanUtil.getBean(ObjectMapper.class);
            
            if (objectMapper == null) {
                log.error("无法获取ObjectMapper，解析API返回结果失败");
                return false;
            }

            // 解析响应为Map对象
            Map<String, Object> apiResponseMap = objectMapper.readValue(apiResult, Map.class);

            // 检查API调用是否成功 (基于ApiResponse标准格式：code=0表示成功)
            Integer code = (Integer) apiResponseMap.get("code");
            String message = (String) apiResponseMap.get("message");
            Object data = apiResponseMap.get("data");

            if (code == null || !code.equals(0)) {
                String errorMsg = String.format("%s失败: %s (错误码: %d)",
                        operationName, message, code != null ? code : -1);
                log.warn("{}, flowNodeId: {}, businessType: {}", errorMsg, flowNodeId, businessType);
                return false;
            }

            // 从data中提取状态信息
            if (!(data instanceof Map)) {
                log.warn("{}API返回数据格式错误: {} - {}", operationName, flowNodeId, businessType);
                return false;
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> dataMap = (Map<String, Object>) data;

            // 构建更新实体
            FlowOrderInfoEntity updateEntity = new FlowOrderInfoEntity();
            updateEntity.setFlowNodeId(flowNodeId);
            updateEntity.setFlowOrderInfoType(businessType);

            // 根据返回数据更新状态
            boolean hasUpdates = false;

            
            if (dataMap.containsKey("paymentStatus")) {
                Object paymentStatusObj = dataMap.get("paymentStatus");
                if (paymentStatusObj instanceof Number) {
                    updateEntity.setPaymentStatus(((Number) paymentStatusObj).intValue());
                    hasUpdates = true;
                }
            }
            
            if (dataMap.containsKey("deliveryStatus")) {
                Object storageStatusObj = dataMap.get("deliveryStatus");
                if (storageStatusObj instanceof Number) {
                    updateEntity.setStorageStatus(((Number) storageStatusObj).intValue());
                    hasUpdates = true;
                }
            }
            
            if (dataMap.containsKey("invoiceStatus")) {
                Object invoiceStatusObj = dataMap.get("invoiceStatus");
                if (invoiceStatusObj instanceof Number) {
                    updateEntity.setInvoiceStatus(((Number) invoiceStatusObj).intValue());
                    hasUpdates = true;
                }
            }

            // 更新发票信息
            if(SALES_TYPE == businessType){
                Object saleOrderInvoiceDtoList = dataMap.get("saleOrderInvoiceDtoList");
                if (saleOrderInvoiceDtoList != null) {
                    boolean invoiceProcessed = processSaleOrderInvoiceInfo(updateEntity, saleOrderInvoiceDtoList, objectMapper);
                    if (invoiceProcessed) {
                        hasUpdates = true;
                    }
                }
            }else if(PURCHASE_TYPE == businessType){
                if (dataMap.containsKey("invoiceList")) {
                    Object invoiceListObj = dataMap.get("invoiceList");
                    if (invoiceListObj != null) {
                        boolean invoiceProcessed = processPurchaseOrderInvoiceInfo(updateEntity, invoiceListObj, objectMapper);
                        if (invoiceProcessed) {
                            hasUpdates = true;
                        }
                    }
                }
            }

            if (dataMap.containsKey("contractUrl")) {
                Object contractUrl = dataMap.get("contractUrl");
                if (contractUrl != null) {
                    updateEntity.setContractFileName("合同");
                    updateEntity.setContractFileUrl(contractUrl.toString());
                    hasUpdates = true;
                }
            }

            if (!hasUpdates) {
                log.info("{}API返回数据中无可更新的状态字段: {} - {}", operationName, flowNodeId, businessType);
                return true; // 虽然没有更新，但不算失败
            }

            // 设置更新信息
            updateEntity.setModTime(new Date());
            updateEntity.setUpdater(1); // 系统用户
            updateEntity.setUpdaterName("system");

            // 执行更新
            int updateCount = temporalFlowOrderInfoMapper.updateByFlowNodeIdAndTypeSelective(updateEntity);

            if (updateCount > 0) {
                log.info("{}结果更新成功: {} - {} - 更新{}条记录",
                        operationName, flowNodeId, businessType, updateCount);
                
                // 更新成功后，检查是否需要将订单状态标记为已完结
                checkAndUpdateCompletionStatusAfterUpdate(flowNodeId, businessType);
                
                return true;
            } else {
                log.warn("{}结果更新失败: {} - {} - 无记录更新",
                        operationName, flowNodeId, businessType);
                return false;
            }

        } catch (Exception e) {
            log.error("解析{}API结果异常: {} - {}", operationName, flowNodeId, businessType, e);
            return false;
        }
    }

    /**
     * 处理销售订单发票信息
     * 将 saleOrderInvoiceDtoList 转换为 JSON 格式并更新到实体中
     *
     * @param updateEntity FlowOrderInfo更新实体
     * @param saleOrderInvoiceDtoList 销售订单发票信息列表
     * @param objectMapper JSON转换器
     * @return 是否有更新
     */
    private boolean processSaleOrderInvoiceInfo(FlowOrderInfoEntity updateEntity, 
                                               Object saleOrderInvoiceDtoList, 
                                               ObjectMapper objectMapper) {
        if (saleOrderInvoiceDtoList == null) {
            log.debug("销售订单发票信息为空");
            return false;
        }

        try {
            // 检查是否为List类型
            if (!(saleOrderInvoiceDtoList instanceof List)) {
                log.warn("销售订单发票信息格式错误，期望List类型，实际类型: {}", 
                        saleOrderInvoiceDtoList.getClass().getSimpleName());
                return false;
            }

            @SuppressWarnings("unchecked")
            List<Object> invoiceList = (List<Object>) saleOrderInvoiceDtoList;

            if (invoiceList.isEmpty()) {
                log.debug("销售订单发票信息列表为空");
                return false;
            }

            // 处理发票信息列表
            List<Map<String, Object>> processedInvoices = new ArrayList<>();
            
            for (Object invoiceObj : invoiceList) {
                if (!(invoiceObj instanceof Map)) {
                    log.warn("发票信息格式错误，期望Map类型，实际类型: {}", 
                            invoiceObj.getClass().getSimpleName());
                    continue;
                }

                @SuppressWarnings("unchecked")
                Map<String, Object> invoiceMap = (Map<String, Object>) invoiceObj;
                
                // 创建简化的发票信息Map
                Map<String, Object> simplifiedInvoice = new HashMap<>();
                simplifiedInvoice.put("invoiceNo", invoiceMap.get("invoiceNo"));
                simplifiedInvoice.put("invoiceUrl", invoiceMap.get("ossFileUrl"));

                processedInvoices.add(simplifiedInvoice);
            }

            if (processedInvoices.isEmpty()) {
                log.warn("处理后的销售订单发票信息列表为空");
                return false;
            }

            // 转换为JSON字符串
            String invoiceInfoJson = objectMapper.writeValueAsString(processedInvoices);
            updateEntity.setInvoiceInfo(invoiceInfoJson);

            log.info("成功处理销售订单发票信息，发票数量: {}, JSON长度: {}", 
                    processedInvoices.size(), invoiceInfoJson.length());
            
            return true;

        } catch (Exception e) {
            log.error("处理销售订单发票信息异常", e);
            return false;
        }
    }

    /**
     * 处理采购订单发票信息
     *
     * @param updateEntity FlowOrderInfo更新实体
     * @param purchaseOrderInvoiceList 采购订单发票信息列表
     * @param objectMapper JSON转换器
     * @return 是否有更新
     */
    private boolean processPurchaseOrderInvoiceInfo(FlowOrderInfoEntity updateEntity, 
                                                   Object purchaseOrderInvoiceList, 
                                                   ObjectMapper objectMapper) {
        if (purchaseOrderInvoiceList == null) {
            log.debug("采购订单发票信息为空");
            return false;
        }

        try {
            // 检查是否为List类型
            if (!(purchaseOrderInvoiceList instanceof List)) {
                log.warn("采购订单发票信息格式错误，期望List类型，实际类型: {}", 
                        purchaseOrderInvoiceList.getClass().getSimpleName());
                return false;
            }

            @SuppressWarnings("unchecked")
            List<Object> invoiceList = (List<Object>) purchaseOrderInvoiceList;

            if (invoiceList.isEmpty()) {
                log.debug("采购订单发票信息列表为空");
                return false;
            }

            // 处理发票信息列表
            List<Map<String, Object>> processedInvoices = new ArrayList<>();
            
            for (Object invoiceObj : invoiceList) {
                if (!(invoiceObj instanceof Map)) {
                    log.warn("采购订单发票信息格式错误，期望Map类型，实际类型: {}", 
                            invoiceObj.getClass().getSimpleName());
                    continue;
                }

                @SuppressWarnings("unchecked")
                Map<String, Object> invoiceMap = (Map<String, Object>) invoiceObj;
                
                // 创建简化的发票信息Map，对应BuyOrderInvoiceDto结构
                Map<String, Object> simplifiedInvoice = new HashMap<>();
                simplifiedInvoice.put("invoiceNo", invoiceMap.get("invoiceNo"));
                simplifiedInvoice.put("invoiceUrl", invoiceMap.get("invoiceHref"));
                
                processedInvoices.add(simplifiedInvoice);
            }

            if (processedInvoices.isEmpty()) {
                log.warn("处理后的采购订单发票信息列表为空");
                return false;
            }

            // 转换为JSON字符串
            String invoiceInfoJson = objectMapper.writeValueAsString(processedInvoices);
            updateEntity.setInvoiceInfo(invoiceInfoJson);

            log.info("成功处理采购订单发票信息，发票数量: {}, JSON长度: {}", 
                    processedInvoices.size(), invoiceInfoJson.length());
            
            return true;

        } catch (Exception e) {
            log.error("处理采购订单发票信息异常", e);
            return false;
        }
    }

    /**
     * 批量检查并更新完成状态
     * 遍历所有处理中的记录，检查是否达到完结条件，如果是则更新为已完结
     * 
     * @return 处理结果统计
     */
    @Transactional(rollbackFor = Exception.class)
    public ProcessResult batchUpdateCompletedStatus() {
        log.info("开始批量检查并更新FlowOrderInfo完成状态");
        
        ProcessResult result = new ProcessResult();
        
        try {
            // 查询所有处理中且可能需要更新为已完结的记录
            List<FlowOrderInfoEntity> pendingRecords = temporalFlowOrderInfoMapper.selectPendingCompletionRecords();
            
            if (CollectionUtils.isEmpty(pendingRecords)) {
                log.info("未找到需要检查完成状态的记录");
                return result;
            }
            
            result.setTotalCount(pendingRecords.size());
            log.info("找到 {} 个需要检查完成状态的记录", pendingRecords.size());
            
            // 遍历检查每个记录
            for (FlowOrderInfoEntity record : pendingRecords) {
                try {
                    boolean updated = checkAndUpdateSingleRecordStatus(record);
                    if (updated) {
                        result.incrementSuccess();
                        log.info("FlowOrderInfo状态更新为已完结: {}", record.getFlowOrderInfoNo());
                    } else {
                        // 不需要更新也算成功
                        result.incrementSuccess();
                    }
                } catch (Exception e) {
                    result.incrementFailure();
                    log.error("检查FlowOrderInfo完成状态异常: {}", record.getFlowOrderInfoNo(), e);
                }
            }
            
            log.info("批量检查完成状态完成 - 总数: {}, 成功: {}, 失败: {}",
                    result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());
            
            return result;
            
        } catch (Exception e) {
            log.error("批量检查完成状态异常", e);
            throw e;
        }
    }
    
    /**
     * 检查单条记录是否应标记为已完结，如果是则更新
     * 
     * @param record FlowOrderInfo记录
     * @return 是否进行了更新
     */
    private boolean checkAndUpdateSingleRecordStatus(FlowOrderInfoEntity record) {
        if (record == null) {
            return false;
        }
        
        // 检查是否应该标记为已完结
        if (!record.shouldMarkAsCompleted()) {
            log.debug("FlowOrderInfo无需更新为已完结: {} - 当前状态: 订单={}, 款项={}, 入库={}, 票据={}, 发票信息={}, 合同文件={}",
                    record.getFlowOrderInfoNo(), record.getOrderStatus(), 
                    record.getPaymentStatus(), record.getStorageStatus(), record.getInvoiceStatus(),
                    record.hasValidInvoiceInfo() ? "有效" : "无效", 
                    record.hasValidContractFile() ? "有效" : "无效");
            return false;
        }
        
        log.info("FlowOrderInfo满足完结条件，准备更新: {} - 款项={}, 入库={}, 票据={}, 发票信息={}, 合同文件={}",
                record.getFlowOrderInfoNo(), record.getPaymentStatus(), 
                record.getStorageStatus(), record.getInvoiceStatus(),
                record.hasValidInvoiceInfo() ? "有效" : "无效", 
                record.hasValidContractFile() ? "有效" : "无效");
        
        try {
            // 构建更新实体
            FlowOrderInfoEntity updateEntity = new FlowOrderInfoEntity();
            updateEntity.setFlowNodeId(record.getFlowNodeId());
            updateEntity.setFlowOrderInfoType(record.getFlowOrderInfoType());
            updateEntity.setOrderStatus(1); // 标记为已完结
            updateEntity.setModTime(new Date());
            updateEntity.setUpdater(1); // 系统用户
            updateEntity.setUpdaterName("system");
            
            // 执行更新
            int updateCount = temporalFlowOrderInfoMapper.updateByFlowNodeIdAndTypeSelective(updateEntity);
            
            if (updateCount > 0) {
                log.info("FlowOrderInfo状态更新为已完结成功: {} - 更新{}条记录",
                        record.getFlowOrderInfoNo(), updateCount);
                return true;
            } else {
                log.warn("FlowOrderInfo状态更新为已完结失败: {} - 无记录更新",
                        record.getFlowOrderInfoNo());
                return false;
            }
            
        } catch (Exception e) {
            log.error("更新FlowOrderInfo状态为已完结异常: {}", record.getFlowOrderInfoNo(), e);
            return false;
        }
    }

    /**
     * 在更新后检查并更新订单完成状态
     * 当单条记录的业务状态更新后，检查是否达到完结条件
     * 
     * @param flowNodeId 流程节点ID
     * @param businessType 业务类型
     */
    private void checkAndUpdateCompletionStatusAfterUpdate(Long flowNodeId, Integer businessType) {
        try {
            // 查询刚刚更新的记录
            List<FlowOrderInfoEntity> records = temporalFlowOrderInfoMapper
                    .selectByBusinessTypeAndFlowNodeId(businessType, flowNodeId);
            
            if (!CollectionUtils.isEmpty(records)) {
                FlowOrderInfoEntity updatedRecord = records.get(0);
                // 检查并更新完成状态
                boolean updated = checkAndUpdateSingleRecordStatus(updatedRecord);
                if (updated) {
                    log.info("更新后自动检查完成状态: {} 已标记为已完结", 
                            updatedRecord.getFlowOrderInfoNo());
                }
            } else {
                log.warn("无法找到刚刚更新的FlowOrderInfo记录: flowNodeId={}, businessType={}", 
                        flowNodeId, businessType);
            }
        } catch (Exception e) {
            log.error("检查更新后完成状态异常: flowNodeId={}, businessType={}", 
                    flowNodeId, businessType, e);
        }
    }

    /**
     * 处理结果统计类
     */
    @Data
    public static class ProcessResult {
        private int totalCount = 0;
        private int successCount = 0;
        private int failureCount = 0;

        public void incrementSuccess() {
            this.successCount++;
        }

        public void incrementFailure() {
            this.failureCount++;
        }
    }
}