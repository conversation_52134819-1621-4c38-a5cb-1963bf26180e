package com.vedeng.erp.kingdee.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseReceiptCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseReceiptEntity;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseReceiptDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseReceiptQueryResultDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseReceiptCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseReceiptConvertor;
import com.vedeng.erp.kingdee.repository.KingDeePurchaseReceiptRepository;
import com.vedeng.erp.kingdee.service.KingDeePurchaseReceiptApiService;
import com.vedeng.erp.kingdee.service.KingDeePurchaseReceiptService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/3/7 16:11
 **/
@Service
@Slf4j
public class KingDeePurchaseReceiptServiceImpl extends KingDeeBaseServiceImpl<
        KingDeePurchaseReceiptEntity,
        KingDeePurchaseReceiptDto,
        KingDeePurchaseReceiptCommand,
        KingDeePurchaseReceiptRepository,
        KingDeePurchaseReceiptConvertor,
        KingDeePurchaseReceiptCommandConvertor> implements KingDeePurchaseReceiptService, KingDeePurchaseReceiptApiService {


    @Override
    public List<KingDeePurchaseReceiptQueryResultDto> queryByOutInNo(String outInNo) {
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.STK_INSTOCK);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBillNo").value(outInNo).build());
        queryParam.setFilterString(queryFilterDtos);
        log.info("金蝶采购入库单查询入参：{}", JSON.toJSONString(queryParam));
        return kingDeeBaseApi.query(queryParam, KingDeePurchaseReceiptQueryResultDto.class);
    }
}
