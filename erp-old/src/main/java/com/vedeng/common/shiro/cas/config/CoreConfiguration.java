package com.vedeng.common.shiro.cas.config;

import com.ctrip.framework.apollo.build.ApolloInjector;
import com.ctrip.framework.apollo.core.enums.Env;
import com.ctrip.framework.apollo.util.ConfigUtil;
import com.vedeng.common.shiro.cas.CasClientConstants;
import com.vedeng.common.shiro.cas.properties.CasClientProperties;
import com.vedeng.common.shiro.constant.SecurityConstants;
import org.pac4j.cas.client.CasClient;
import org.pac4j.cas.config.CasConfiguration;
import org.pac4j.cas.config.CasProtocol;
import org.pac4j.core.context.J2EContext;
import org.pac4j.core.context.WebContext;
import org.pac4j.core.http.UrlResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class CoreConfiguration {
    @Autowired
    private CasClientProperties casClientProperties;


    @Bean
    public CasClient casClient() {
        CasClient casClient = new CasClient();
        casClient.setConfiguration(createCasConfiguration());
        casClient.setCallbackUrl(casClientProperties.getCallbackUrl());
        casClient.setName(CasClientConstants.DEFAULT_CAS_CLIENT_NAME);

        //处理生产环境兼容newerp路径跳转问题
        if (isProdEnv()) {
            casClient.setUrlResolver(AdaptiveUrlResolver.INSTANCE);
        }
        return casClient;
    }


    private CasConfiguration createCasConfiguration() {
        CasConfiguration casConfiguration = new CasConfiguration(casClientProperties.getCasLoginUrl());
        casConfiguration.setProtocol(CasProtocol.CAS30);
        casConfiguration.setPrefixUrl(casClientProperties.getPrefixUrl());
        casConfiguration.setLoginUrl(casClientProperties.getCasLoginUrl() + "?platformNo=" + SecurityConstants.ERP_PLATFORM_NO);

        //处理生产环境兼容newerp路径跳转问题
        if (isProdEnv()) {
            casConfiguration.setUrlResolver(AdaptiveUrlResolver.INSTANCE);
        }

        return casConfiguration;
    }


    private boolean isProdEnv() {
        ConfigUtil configUtil = ApolloInjector.getInstance(ConfigUtil.class);
        Env apolloEnv = configUtil.getApolloEnv();
        return Env.PRO == apolloEnv;
    }


    private static class AdaptiveUrlResolver implements UrlResolver {

        private static final String NEW_ERP_DOMAIN = "http://newerp.vedeng.com";

        public static final UrlResolver INSTANCE = new AdaptiveUrlResolver();

        @Override
        public String compute(String url, WebContext context) {
            if (!url.startsWith("http://erp.ivedeng.com")) {
                return url;
            }

            if (context instanceof J2EContext) {
                StringBuffer requestURL = ((J2EContext) context).getRequest().getRequestURL();
                if (requestURL.toString().startsWith(NEW_ERP_DOMAIN)) {
                    url = url.replace("http://erp.ivedeng.com", NEW_ERP_DOMAIN);
                }
            }
            return url;
        }
    }
}
