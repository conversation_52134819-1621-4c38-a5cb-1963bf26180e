<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/vd-ui/common.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/vd-ui/ui.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/scene/detail.css">


<div class="page-wrap" id="page-container">
    <div class="page-detail-container">
        <div class="page-detail-header">
            <div class="header-title">场景方案详情</div>
            <div class="header-options">
                <ui-button @click="gotoEdit">编辑</ui-button>
                <ui-button @click="exportScene">导出</ui-button>
                <ui-button @click="updateScene(1)" v-if="sceneInfo.status === 0">下架</ui-button>
                <ui-button @click="updateScene(0)" v-else>上架</ui-button>
                <ui-button @click="copyScene">复制</ui-button>
            </div>
        </div>
        <div class="page-detail-content" v-if="!isloading">
            <div class="card-block">
                <div class="card-title">基础信息</div>
                <div class="detail-info-wrap">
                    <div class="info-item">
                        <div class="info-label">场景名称：</div>
                        <div class="info-content">{{ sceneInfo.name }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">场景说明：</div>
                        <div class="info-content">{{ sceneInfo.description || '-' }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">状态：</div>
                        <div class="info-content" :class="sceneInfo.status === 0 ? 'green' : 'red'">{{ ['已上架', '已下架'][sceneInfo.status] }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">编辑时间：</div>
                        <div class="info-content">{{ sceneInfo.modTime }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">编辑人：</div>
                        <div class="info-content">{{ sceneInfo.updaterName }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">创建时间：</div>
                        <div class="info-content">{{ sceneInfo.addTime }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">创建人：</div>
                        <div class="info-content">{{ sceneInfo.creatorName }}</div>
                    </div>
                </div>
            </div>
            <div class="card-block">
                <div class="card-title">产品信息</div>
                <div class="detail-prod-tabs-placeholder" ref="tabWrap" :style="tabHeight ? 'height:' + tabHeight + 'px' : ''">
                    <div class="detail-prod-tabs" :class="{fixed: tabFixed}" ref="tabList">
                        <div class="tabs-list">
                            <div class="tab-item" @click="changeTab(index)" :class="{active: index === currentTabIndex}" v-for="(item, index) in tabInfo">{{ item.name }}</div>
                        </div>
                    </div>
                </div>
                <div class="detail-prod-list-wrap">
                    <ui-table :headers="prodListHeader" :list="currentProdList" ref="tableList" :left-fixed="false" :fixed-top="tableFixedTop">
                        <template v-slot:tablenum="{ row }">{{ row.listUiIndex + 1 }}</template>
                        <template v-slot:baseinfo="{ row }">
                            <div class="td-cnt-wrap">
                                <div class="prod-info-wrap">
                                    <div class="prod-img">
                                        <img :src="row.basicInfo.imageUrl || VD_UI_GLOBAL.defaultImg" alt="">
                                    </div>
                                    <div class="prod-info">
                                        <div class="prod-sku" @click="gotoGoodsDetail(row.basicInfo.skuNo)">{{ row.basicInfo.skuNo }}</div>
                                        <div class="prod-name text-line-2" :title="row.basicInfo.skuName">{{ row.basicInfo.skuName }}</div>
                                        <div class="prod-detail">
                                            <div class="detail-label">品牌：</div>
                                            <div class="detail-txt">{{ row.basicInfo.brandName || '-' }}</div>
                                        </div>
                                        <div class="prod-detail">
                                            <div class="detail-label">型号/规格：</div>
                                            <div class="detail-txt">{{ row.basicInfo.modelOrSpec || '-' }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template v-slot:mainparam="{ row }">
                            <template v-if="row.mainParameters.mainParam && row.mainParameters.mainParam.length">
                                <div class="td-cnt-wrap" v-html="row.mainParameters.mainParam.join('<br>')"></div>
                            </template>
                            <template v-else>-</template>
                        </template>
                        <template v-slot:priceinfo="{ row }">
                            <div class="td-cnt-wrap">
                                <div class="prod-detail">
                                    <div class="detail-label">分销价：</div>
                                    <template v-if="row.priceInfo.priceStatus === 0">
                                        <div class="detail-txt">未核价</div>
                                    </template>
                                    <template v-else>
                                        <div class="detail-txt red" v-if="row.priceInfo.distributionPrice || row.priceInfo.distributionPrice === 0">{{ row.priceInfo.distributionPrice.toFixed(2) }}</div>
                                        <div class="detail-txt" v-else>-</div>
                                    </template>
                                </div>
                                <div class="prod-detail">
                                    <div class="detail-label">终端价：</div>
                                    <template v-if="row.priceInfo.priceStatus === 0">
                                        <div class="detail-txt">未核价</div>
                                    </template>
                                    <template v-else>
                                        <div class="detail-txt red" v-if="row.priceInfo.terminalPrice || row.priceInfo.terminalPrice === 0">{{ row.priceInfo.terminalPrice.toFixed(2) }}</div>
                                        <div class="detail-txt" v-else>-</div>
                                    </template>
                                </div>
                            </div>
                        </template>
                        <template v-slot:saleinfo="{ row }">
                            <div class="td-cnt-wrap">
                                <div class="prod-detail">
                                    <div class="detail-label">使用年限：</div>
                                    <div class="detail-txt">{{ row.afterSaleInfo.useLife || '-' }}</div>
                                </div>
                                <div class="prod-detail">
                                    <div class="detail-label">质保：</div>
                                    <div class="detail-txt">{{ row.afterSaleInfo.warrantyInfo || '-' }}</div>
                                </div>
                                <div class="prod-detail">
                                    <div class="detail-txt">{{ row.afterSaleInfo.isInstall == 1 ? '免费安装（偏远地区除外）' : '' }}</div>
                                </div>
                            </div>
                        </template>
                        <template v-slot:prodstatus="{ row }">
                            <div class="td-cnt-wrap">
                                <div class="prod-detail">
                                    <div class="detail-label">审核状态：</div>
                                    <div class="detail-txt red" :class="{orange: row.productStatus.auditStatus == 1, green: row.productStatus.auditStatus == 3}">{{ ['待完善', '审核中', '审核不通过', '审核通过', '删除', '待提交审核'][row.productStatus.auditStatus] }}</div>
                                </div>
                                <div class="prod-detail">
                                    <div class="detail-label">商品等级：</div>
                                    <div class="detail-txt" :class="{orange: row.productStatus.goodsLevelNo == 5}">{{ row.productStatus.goodsLevelNoLabel }}</div>
                                </div>
                                <div class="prod-detail">
                                    <div class="detail-label">商品挡位：</div>
                                    <div class="detail-txt">{{ row.productStatus.goodsPositionNoLabel }}</div>
                                </div>
                            </div>
                        </template>
                        <template v-slot:manager="{ row }">
                            <div class="td-cnt-wrap">
                                <div class="prod-detail">
                                    <div class="detail-label">产品经理：</div>
                                    <div class="detail-txt">
                                        <div class="user-wrap">
                                            <div class="user-avatar">
                                                <img :src="row.productManager.aliasHeadPicture || VD_UI_GLOBAL.defaultImg" alt="">
                                            </div>
                                            <div class="user-name text-line-1">{{ row.productManager.username }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="prod-detail">
                                    <div class="detail-label">产品助理：</div>
                                    <div class="detail-txt">
                                        <div class="user-wrap">
                                            <div class="user-avatar">
                                                <img :src="row.productAssistant.aliasHeadPicture || VD_UI_GLOBAL.defaultImg" alt="">
                                            </div>
                                            <div class="user-name text-line-1">{{ row.productAssistant.username }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </ui-table>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="${pageContext.request.contextPath}/static/vue/vd-ui/ui.js"></script>
<script src="${pageContext.request.contextPath}/static/js/scene/detail.js"></script>