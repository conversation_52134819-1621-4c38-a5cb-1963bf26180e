package com.vedeng.infrastructure.bank.api.service;

import com.vedeng.infrastructure.bank.api.domain.*;
import com.vedeng.infrastructure.bank.api.domain.dto.AcceptanceBillCreateDto;

/**
 * <AUTHOR>
 */
public interface AcceptanceBillApiService {

    /**
     * 创建承兑汇票
     *
     * 1.获取文件token
     * 2.上传文件
     * 3.融资预申请
     * 4.融资申请提交
     *
     * @param body
     * @return
     */
    B2eDraftApplyRes b2eNbsAcceptanceBillCreate(AcceptanceBillCreateDto body);


    /**
     * 票据查询结果(B2eQueryBasicDraftResult)
     */
    B2eQueryBasicDraftResultRes queryDraftResult(B2eQueryBasicDraftResultReq queryReq);

    /**
     * 持有票据查询(B2eNbsDraftHoldingBillsQry)
     * @param body
     * @return
     */
    DraftHoldingBillsQryResponse b2eNbsDraftHoldingBillsQry(DraftHoldingBillsQryBody body);

    /**
     * 票据详细信息查询(B2eNbsDraftDetail)
     * @param body
     * @return
     */
    DraftDetailResponse b2eNbsDraftDetail(DraftDetailReqBody body);


    /**
     * 票据签收
     * step1:可通用签收票据列表查询(B2eNbsQryStaySignUpDrafts)
     * step2:通用签收(B2eNbsDraftSignUp)
     * @param body
     */
    B2eNbsDraftSignUpResponse b2eNbsDraftSignUp(B2eNbsQryStaySignUpDraftsReqBody body);


    /**
     * 接口名称：贴现申请(B2eNbsDraftDiscount)<br/>
     * 接口说明：http://wiki.ivedeng.com/pages/viewpage.action?pageId=251756571<br/>
     * 1. 本接口用于对票据做贴现申请<br/>
     * 2. 交易后使用《票据交易状态查询(B2eNbsQryDraftTransStatus)》查询交易结果向我行贴现<br/>
     * 3. 向我行贴现,目前需线下与我行签署贴现协议，具体流程请联系客户经理；向他行贴现，请向他行咨询具体流程。<br/>
     * @param b2eNbsDraftDiscountReqBody
     * @return
     */
    B2eNbsDraftDiscountResponse b2eNbsDraftDiscount(B2eNbsDraftDiscountReqBody b2eNbsDraftDiscountReqBody);


    /**
     * 接口名称：票据交易状态查询(B2eNbsQryDraftTransStatus)<br/>
     * 接口说明：<br/>
     * 1. 本接口用于根据签约账户、操作类型等参数查询各个交易的交易状态。<br/>
     * 2. 若想查询撤销交易的状态或者结果，操作类型请选择“申请”，客户业务请求流水号 （insid）也请使用对应申请的客户业务请求流水号。<br/>
     * 例如：背书转让申请后，在背书转 让待签收前，进行背书转让撤销，撤销后，若需查询撤销结果，需使用背书申请的 insid，操作类型选择“申请”，业务类型选择“02背书转出”。<br/>
     * @param body
     * @return
     */
    B2eNbsQryDraftTransStatusResponse b2eNbsQryDraftTransStatus(B2eNbsQryDraftTransStatusReqBody body,boolean isShou);


    /**
     * 接口名称：银承中收减免审批单号查询(B2eQueryTaskNo)
     * @param b2eQueryTaskNoReq
     * @return
     */
    B2eQueryTaskNoRes b2eQueryTaskNo(B2eQueryTaskNoReq b2eQueryTaskNoReq);

}
