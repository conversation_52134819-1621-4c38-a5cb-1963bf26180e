package com.vedeng.erp.system.service.impl;

import com.vedeng.erp.system.dto.CommonSearchDto;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import com.vedeng.erp.system.domain.entity.CommonSearchEntity;
import com.vedeng.erp.system.mapper.CommonSearchMapper;
import com.vedeng.erp.system.service.CommonSearchService;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
public class CommonSearchServiceImpl implements CommonSearchService{

    @Autowired
    private CommonSearchMapper commonSearchMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return commonSearchMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(CommonSearchEntity record) {
        return commonSearchMapper.insert(record);
    }

    @Override
    public int insertSelective(CommonSearchEntity record) {
        return commonSearchMapper.insertSelective(record);
    }

    @Override
    public CommonSearchEntity selectByPrimaryKey(Long id) {
        return commonSearchMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(CommonSearchEntity record) {
        return commonSearchMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(CommonSearchEntity record) {
        return commonSearchMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<CommonSearchEntity> selectListByUserIdAndSearchFrom(CommonSearchDto commonSearchDto) {
        return commonSearchMapper.selectListByUserIdAndSearchFrom(commonSearchDto);
    }

    @Override
    public int saveOrUpdate(CommonSearchDto commonSearchDto) {
        CommonSearchEntity record = new CommonSearchEntity();
        BeanUtils.copyProperties(commonSearchDto,record);
        record.setAddTime(new Date());
        record.setModTime(new Date());
        record.setCreator(commonSearchDto.getUserId());
        record.setUpdater(commonSearchDto.getUserId());
        if(commonSearchDto.getId() == null){
            commonSearchMapper.insertSelective(record);
            return record.getId().intValue();
        }else{
            commonSearchMapper.updateByPrimaryKeySelective(record);
            return record.getId().intValue();
        }
    }


}
