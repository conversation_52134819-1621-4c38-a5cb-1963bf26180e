<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="进项票（供应链）" scope="application"/>
<%@ include file="../../../common/common.jsp" %>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
<script src="${pageContext.request.contextPath}/static/js/finance/invoice/hx_invoice/supply_hx_invoice_wait_return.js?rnd=${resourceVersionKey}"
        charset="utf-8"></script>
<script src="${pageContext.request.contextPath}/static/js/finance/invoice/hx_invoice/hx_invoice_common.js?rnd=${resourceVersionKey}"
        charset="utf-8"></script>
<div class="layui-tab layui-tab-brief" lay-filter="test">
    <ul class="layui-tab-title">
        <li onclick="changeTab(0)">待录票</li>
        <li onclick="changeTab(1)">审核中</li>
        <li onclick="changeTab(2)">已审核</li>
        <li onclick="changeTab(3)" class="layui-this">待退票</li>
        <li onclick="changeTab(4)">无效票</li>
    </ul>
</div>
<div class="layui-tab-item layui-show">
    <div class="searchfunc ">
        <form method="post" id="search" action="/supplyChain/invoice/hx_invoice_wait.do?idFlag=3">
            <ul>
                <li>
                    <label class="infor_name">关键词</label>
                    <input type="text" class="input-middle" name="keyword" id="keyword"
                           value="${invoiceSearch.keyword}"/>
                </li>

                <li>
                    <label class="infor_name">归属采购</label>
                    <select class="input-middle" name="userId" id="userId">
                        <option value="0">全部</option>
                        <c:forEach items="${userList}" var="user">
                            <option value="${user.userId}"
                                    <c:if test="${invoiceSearch.userId eq user.userId}">selected="selected"</c:if>>${user.username}</option>
                        </c:forEach>
                    </select>
                </li>

                <li>
                    <label class="infor_name">处理状态</label>
                    <select class="input-middle" name="invoiceRefundStatus" id="invoiceRefundStatus">
                        <option value="-1"
                                <c:if test="${invoiceSearch.invoiceTaxRate == -1}">selected</c:if> >全部
                        </option>
                        <option value="0" <c:if test="${invoiceSearch.invoiceRefundStatus == 0}">selected</c:if>>未处理
                        </option>
                        <option value="1" <c:if test="${invoiceSearch.invoiceRefundStatus == 1}">selected</c:if>>已处理
                        </option>
                    </select>
                </li>

                <li>
                    <div class="infor_name">
                        开票时间
                    </div>
                    <input class="Wdate f_left input-smaller96 mr5" type="text" placeholder="请选择日期"
                           onClick="WdatePicker()" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endAddDateStr\')}'})"
                           name="startAddDateStr" id="startAddDateStr"
                           value='<date:date value ="${startAddDateStr}" format="yyyy-MM-dd"/>'>
                    <div class="gang">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" onClick="WdatePicker()" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startAddDateStr\')}'})"
                           name="endAddDateStr" id="endAddDateStr"
                           value='<date:date value ="${endAddDateStr}" format="yyyy-MM-dd"/>'>
                </li>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();"
                      id="searchSpan">查询</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="searchReset();">重置</span>
            </div>
        </form>
        <br>
        <div class="content">
            <div class="">
                <div style="width:1752px;" class='superdiv'>
                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th class="wid6">发票号码</th>
                            <th class="wid6">发票代码</th>
                            <th class="wid12">供应商名称</th>
                            <th class="wid6">归属采购</th>
                            <th class="wid4">发票总额</th>
                            <th class="wid8">票种</th>
                            <th class="wid10">开票时间</th>
                            <th class="wid4">处理状态</th>
                            <th class="wid8">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:set var="lzyxAmount" value="0"></c:set><!-- 蓝字有效总额 -->
                        <c:set var="lzzfAmount" value="0"></c:set><!-- 蓝字作废总额 -->
                        <c:set var="hzyxAmount" value="0"></c:set><!-- 红字有效总额 -->
                        <c:forEach var="hxInvoiceVo" items="${list}">
                            <c:set var="pageNum" value="${pageNum + 1}"></c:set>
                            <tr>
                                <td>
                                        ${hxInvoiceVo.invoiceNum}
                                </td>
                                <td>
                                        ${hxInvoiceVo.invoiceCode}
                                </td>
                                <td>
                                        ${hxInvoiceVo.salerName}
                                            <c:choose>
                                                <c:when test="${hxInvoiceVo.colorType == 1}">
                                                    <c:set var="lzyxAmount" value="${lzyxAmount + hxInvoiceVo.amount}"></c:set>
                                                </c:when>
                                                <c:when test="${hxInvoiceVo.colorType == 2}">
                                                    <c:set var="hzyxAmount" value="${hzyxAmount + hxInvoiceVo.amount}"></c:set>
                                                </c:when>
                                                <c:when test="${hxInvoiceVo.colorType == 3}">
                                                    <c:set var="lzzfAmount" value="${lzzfAmount + hxInvoiceVo.amount}"></c:set>
                                                </c:when>
                                            </c:choose>
                                </td>

                                <td>
                                    <c:forEach items="${userList}" var="user">
                                        <c:if test="${user.userId == hxInvoiceVo.orderUserId}">${user.username}</c:if>
                                    </c:forEach>
                                </td>
                                <td>
                                    <fmt:formatNumber type="number" value="${hxInvoiceVo.amount}" pattern="0.00"
                                                      maxFractionDigits="2"/>
                                </td>
                                <td>
                                    <c:if test="${hxInvoiceVo.taxRate != null && hxInvoiceVo.invoiceCategory != null}">
                                        ${hxInvoiceVo.taxRate}%增值税<c:if test="${hxInvoiceVo.invoiceCategory == '0'}">普通</c:if><c:if test="${hxInvoiceVo.invoiceCategory == '1'}">专用</c:if>发票
                                    </c:if>
                                </td>
                                <td>
                                    <date:date value="${hxInvoiceVo.createTime}"/>
                                </td>
                                <td>
                                    <c:if test="${hxInvoiceVo.invoiceRefundStatus == 0}">
                                        未处理
                                    </c:if>
                                    <c:if test="${hxInvoiceVo.invoiceRefundStatus == 1}">
                                        已处理
                                    </c:if>
                                </td>
                                <td>
                                    <a onclick="viewAndDownloadHxInvoiceHref('${hxInvoiceVo.invoiceCode}','${hxInvoiceVo.invoiceNum}','${hxInvoiceVo.hxInvoiceId}',0)">
                                        <font style="color: #438DEF"
                                              id="viewInvocieFont${hxInvoiceVo.hxInvoiceId}">查看发票</font>
                                    </a>
                                    <c:if test="${hxInvoiceVo.invoiceRefundStatus == 0}">
                                        <a onclick="updateInvoiceRefundStatus(${hxInvoiceVo.hxInvoiceId},1)">
                                            <font style="color: #438DEF">标记为已处理</font>
                                        </a>
                                    </c:if>
                                    <c:if test="${hxInvoiceVo.invoiceRefundStatus == 1}">
                                        <a onclick="updateInvoiceRefundStatus(${hxInvoiceVo.hxInvoiceId},0)">
                                            <font style="color: #438DEF">标记为未处理</font>
                                        </a>
                                    </c:if>
                                </td>
                            </tr>
                            <tr style="display: none" id="imgTr${hxInvoiceVo.hxInvoiceId}">
                                <td colspan="9" height="350px" id="invoiceImg${hxInvoiceVo.hxInvoiceId}">
                                </td>
                            </tr>
                            <input id="imgFlag${hxInvoiceVo.hxInvoiceId}" value="0" type="hidden">
                            <input id="imgSrc${hxInvoiceVo.hxInvoiceId}" value="${hxInvoiceVo.attachment}"
                                   type="hidden">
                        </c:forEach>
                        </tbody>
                    </table>

                    <c:if test="${empty list}">
                        <!-- 查询无结果弹出 -->
                        <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
                    </c:if>
                </div>
            </div>
            <div>
                <tags:page page="${page}"/>
                <div class="clear"></div>
                <div class="fixtablelastline" style="height: initial;">
                    【全部结果 条目：${invoice.invoiceCount}
                    总金额：<fmt:formatNumber type="number" value="${invoice.amountCount == null ? 0 : invoice.amountCount}" pattern="0.00" maxFractionDigits="2" /> 】 【本页统计 条目：${pageNum}
                    总金额：<fmt:formatNumber type="number" value="${lzyxAmount - lzzfAmount - hzyxAmount}" pattern="0.00" maxFractionDigits="2" />】
                </div>
            </div>
        </div>
    </div>
</div>