package com.vedeng.erp.saleorder.dao;


import com.vedeng.erp.saleorder.domain.entity.OrderTerminalEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderTerminalMapper {
    int deleteByPrimaryKey(Integer orderTerminalId);

    int insert(OrderTerminalEntity record);

    int insertSelective(OrderTerminalEntity record);

    OrderTerminalEntity selectByPrimaryKey(Integer orderTerminalId);

    int updateByPrimaryKeySelective(OrderTerminalEntity record);

    int updateByPrimaryKey(OrderTerminalEntity record);

    /**
     * 根据业务单据id和业务类型查询终端信息
     *
     * @param businessId   业务单据id
     * @param businessType 业务类型
     * @return OrderTerminalEntity
     */
    OrderTerminalEntity getByBusinessIdAndBusinessType(@Param("businessId") Integer businessId, @Param("businessType") Integer businessType);

    List<OrderTerminalEntity> findBySaleOrderId(@Param("businessId") Integer businessId, @Param("businessType") Integer businessType);


    OrderTerminalEntity getByBusinessIdNameAndBusinessType(@Param("businessId") Integer businessId,@Param("terminalName") String terminalName, @Param("businessType") Integer businessType);

    /**
     * 根据业务单据id和业务类型逻辑删除
     *
     * @param businessId   业务单据id
     * @param businessType 业务类型
     */
    void logicalDelete(@Param("businessId") Integer businessId, @Param("businessType") Integer businessType);
}