package com.vedeng.infrastructure.esign.enums;


import com.vedeng.common.core.exception.ServiceException;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 具体传入e签宝的业务类型枚举
 * @date 2021/12/7 9:59
 */
public enum ElectronicSignBusinessEnums {

    /**
     * 业务类型
     */
    VENDOR(1),

    SALE_ORDER(2),

    BUY_ORDER(3),

    BUY_ORDER_EXPENSE(4),

    /**
     * 销售授权书
     */
    SALE_AUTHORIZATION(5),

    MULT_LIUZHUANG_SEAL(97),//业务流转单多主体该章  需要使用企业微信这个应用，这个在base中做了配置

    CROSS_SHOUQUANSHU(96), //授权书替子主体盖章 需要使用企业微信这个应用，这个在base中做了配置

    MULT_BUYORDER_SEAL(98),  //采购单多主体盖章 需要使用企业微信这个应用，这个在base中做了配置

    MULT_SALEORDER_SEAL(100)  //采购单多主体盖章 需要使用企业微信这个应用，这个在base中做了配置
    ;


    private final Integer type;


    ElectronicSignBusinessEnums(Integer type) {
        this.type = type;
    }

    public Integer getType() {
        return type;
    }

    /**
     * 获取电子签章业务枚举
     *
     * @param type 业务类型
     * @return ElectronicSignBusinessEnums
     */
    public static ElectronicSignBusinessEnums getEnum(Integer type) {
        return Arrays.stream(ElectronicSignBusinessEnums.values())
                .filter(enums -> enums.getType().equals(type))
                .findFirst().orElseThrow(() -> new ServiceException("无法获取对应的电子签章枚举"));
    }
}
