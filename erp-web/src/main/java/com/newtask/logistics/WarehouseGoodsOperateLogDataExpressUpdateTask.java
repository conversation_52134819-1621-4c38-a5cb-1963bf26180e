package com.newtask.logistics;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.ftpclient.client.FTPClientHelper;
import com.vedeng.logistics.dao.ext.WmsWarehouseGoodsOperateLogDataExtMapper;
import com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogData;
import com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogDataExample;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.OssUtilsService;
import com.vedeng.system.service.UserService;
import com.wms.service.WmsImagesService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.Date;
import java.util.List;

@Component
@JobHandler(value = "warehouseGoodsOperateLogDataExpressUpdateTask")
@Slf4j
public class WarehouseGoodsOperateLogDataExpressUpdateTask extends AbstractJobHandler {

    @Resource
    WmsWarehouseGoodsOperateLogDataExtMapper wmsWarehouseGoodsOperateLogDataExtMapper;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        //10分钟
        Long preTime=System.currentTimeMillis()-10*60*1000L;
        List<WmsWarehouseGoodsOperateLogData> logs= wmsWarehouseGoodsOperateLogDataExtMapper.selectLatestIdByExpressModTime(preTime);
        if(CollectionUtils.isEmpty(logs)){
            XxlJobLogger.log("没有找到快递信息");
            return ReturnT.SUCCESS;
        }
        //通过relateid+opertype查找log
        logs.forEach(item->{
            //开始更新log
            log.info("开始更新出库快递 ", JsonUtils.convertObjectToJsonStr(item));
            XxlJobLogger.log("开始更新出库快递 ", JsonUtils.convertObjectToJsonStr(item));
            WmsWarehouseGoodsOperateLogDataExample example2 = new WmsWarehouseGoodsOperateLogDataExample();
            example2.createCriteria().andOperateTypeEqualTo(item.getOperateType()).andRelatedIdEqualTo(item.getRelatedId());
            WmsWarehouseGoodsOperateLogData update=new WmsWarehouseGoodsOperateLogData();
            update.setModeTime(new Date());
            update.setUpdater(0);
            update.setExpressNos(item.getExpressNos());
            wmsWarehouseGoodsOperateLogDataExtMapper.updateByExampleSelective(update,example2);
        });
        return ReturnT.SUCCESS;
    }
}
