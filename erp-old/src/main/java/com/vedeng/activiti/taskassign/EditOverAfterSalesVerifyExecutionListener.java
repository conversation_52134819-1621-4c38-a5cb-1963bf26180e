package com.vedeng.activiti.taskassign;

import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.service.BaseService;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.saleorder.api.OrderInfoSyncService;
import com.vedeng.erp.saleorder.api.SaleorderSyncService;
import com.vedeng.erp.saleorder.enums.PCOrderStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleorderSyncEnum;
import com.vedeng.logistics.service.ConfirmationFormRecodeService;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.orderstream.aftersales.constant.AfterSalesProcessEnum;
import com.wms.service.LogicalSaleorderChooseService;
import com.wms.service.listenner.PutSaleAfterReturnFinshLister;
import com.wms.service.util.GlobalThreadPool;
import net.sf.json.JSONObject;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.xml.ws.WebServiceContext;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class EditOverAfterSalesVerifyExecutionListener implements ExecutionListener {
    // 运行时注入service
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private AfterSalesService afterSalesOrderService = (AfterSalesService) context.getBean("afterSalesOrderService");
    private BaseService baseService = (BaseService) context.getBean("baseService");
	private LogicalSaleorderChooseService logicalSaleorderChooseService = (LogicalSaleorderChooseService) context.getBean("logicalSaleorderChooseService");
	private OrderInfoSyncService orderInfoSyncService = (OrderInfoSyncService) context.getBean("orderInfoSyncService");
	private PutSaleAfterReturnFinshLister putSaleAfterReturnFinshLister = (PutSaleAfterReturnFinshLister) context.getBean("putSaleAfterReturnFinshLister");
	private AfterSalesMapper afterSalesMapper = (AfterSalesMapper) context.getBean("afterSalesMapper");
	private SaleorderMapper saleorderMapper = (SaleorderMapper) context.getBean("saleorderMapper");
	private final SaleorderSyncService saleorderSyncService = (SaleorderSyncService) context.getBean("saleorderSyncService");

	private static final ExecutorService OVERAFTER_LISTENER_THREAD_POOL = Executors.newFixedThreadPool(1);

	public static Logger logger = LoggerFactory.getLogger(EditOverAfterSalesVerifyExecutionListener.class);

    @Resource
    private WebServiceContext webServiceContext;
    //修改售后单结束触发器
    //根据穿参通用回写主表中状态
    public void notify(DelegateExecution execution) throws Exception {
		ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request =  ra.getRequest();
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		AfterSalesVo afterSalesInfo = (AfterSalesVo) execution.getVariable("afterSalesVo");

		logger.info("售后单审核监听执行开始 afterSalesInfo:{}", JSON.toJSONString(afterSalesInfo));

		if(afterSalesInfo.getVerifiesType() != null && afterSalesInfo.getVerifiesType() == 0){
		    //售后单关闭的触发器
		    user.setUserId(afterSalesInfo.getAfterSalesStatusUser());
		    ResultInfo<?> res = afterSalesOrderService.saveCloseAfterSales(afterSalesInfo, user);

//			GlobalThreadPool.submitMessage(new Runnable() {
//				@Override
//				public void run() {
					try{
						//销售退货单关闭重新下传销售单
						logicalSaleorderChooseService.closeAfterPutSaleorder(afterSalesInfo,user);
					}catch (Exception e){
						logger.error("销售退货单关闭重新下传销售单执行报错：",e);
					}
//				}
//			});


			/****** 2018-12-12 Barry 审核流程-审核完成-判断是耗材订单，推送售后金额给耗材数据库 ******/
			afterSalesInfo.setTraderType(1);
			AfterSalesVo afterSales = afterSalesOrderService.getAfterSalesVoDetail(afterSalesInfo);

			//判断是否是耗材订单
			if(afterSales.getSource() == 1){

				BigDecimal total = new BigDecimal(0);
				if (afterSales.getAfterSalesGoodsList().size()>0){
					List<AfterSalesGoodsVo> list =  afterSales.getAfterSalesGoodsList();
					for (AfterSalesGoodsVo goods : list){
						//判断是否是商品
						if (goods.getGoodsType() == 0){
							total = total.add((goods.getSaleorderPrice().multiply(new BigDecimal(goods.getNum()))));
						}
					}
				}

				Map<String, Object> param = new HashMap<String, Object>();
				//查询当前订单的售后列表
				List<AfterSalesVo> afterSalesList = afterSalesOrderService.getAfterSalesVoListByOrderId(afterSales);

				if (afterSalesList.size()>0){
					int num = 0;
					for (AfterSalesVo asv : afterSalesList){
						//如果有待确认和进行中的订单
						if (asv.getAtferSalesStatus() == 0 || asv.getAtferSalesStatus() == 1){
							num++;
						}
					}

					if (num>0){
						param.put("isRefund",1);
					}else {
						param.put("isRefund",0);
					}
				}else {
					param.put("isRefund",0);
				}

				// 请求头
				Map<String, String> header = new HashMap<String, String>();
				header.put("version", "v1");

				param.put("backMoney",total);
				param.put("orderNo",afterSales.getOrderNo());
				JSONObject jsonObject = JSONObject.fromObject(param);
				// 定义反序列化 数据格式
				String url = baseService.getApiUri() + "/order/after/colseBackMoney";

				try {
					final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
					ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.put(url, jsonObject.toString(), header, TypeRef);

				}catch (Exception e){
					logger.error(Contant.ERROR_MSG, e);
				}
			}

			try {
				//完结售后单同步数据
				logger.info("关闭售后单同步数据 afterSalesId:{}", afterSales.getAfterSalesId());
				orderInfoSyncService.unlockSaleOrderWhenAfterSalesClosedOrFinished(afterSales.getAfterSalesId(),ErpConst.ONE);
			} catch (Exception e) {
				logger.error("关闭售后单同步数据 error", e);
			}
		}else if(afterSalesInfo.getVerifiesType() != null && afterSalesInfo.getVerifiesType() == 1){
		    //售后单完成的触发器
		    AfterSalesVo afterSales = (AfterSalesVo) execution.getVariable("afterSales");
		    ResultInfo<?> res = afterSalesOrderService.editApplyAudit(afterSales);
			GlobalThreadPool.submitMessage(new Runnable() {
				@Override
				public void run() {
					try{
						String uuid = UUID.randomUUID().toString().replace("-", "");
						logger.info("开始执行onActionHappen方法，uuid：{}",uuid);
						//售后完成销售单下发wms监听器
						putSaleAfterReturnFinshLister.onActionHappen(afterSales,user);
						logger.info("结束执行onActionHappen方法，uuid：{}",uuid);
					}catch (Exception e){
						logger.error("售后完成销售单下发wms监听器执行报错：",e);
					}
				}
			});


			/****** 2018-12-12 Barry 审核流程-审核完成-判断是耗材订单，推送售后金额给耗材数据库 ******/
			afterSalesInfo.setTraderType(1);
			AfterSalesVo afterSalesVo = afterSalesOrderService.getAfterSalesVoDetail(afterSalesInfo);
			//判断是否是耗材订单
			if(afterSalesVo.getSource() == 1){
				Map<String, Object> param = new HashMap<String, Object>();
				//查询当前订单的售后列表
				List<AfterSalesVo> afterSalesList = afterSalesOrderService.getAfterSalesVoListByOrderId(afterSalesVo);

				if (afterSalesList.size()>0){
					int num = 0;
					for (AfterSalesVo asv : afterSalesList){
						//如果有待确认和进行中的订单
						if (asv.getAtferSalesStatus() == 0 || asv.getAtferSalesStatus() == 1){
							num++;
						}
					}

					if (num>0){
						param.put("isRefund",1);
					}else {
						param.put("isRefund",0);
					}
				}else {
					param.put("isRefund",0);
				}

				// 请求头
				Map<String, String> header = new HashMap<String, String>();
				header.put("version", "v1");

				param.put("orderNo",afterSalesVo.getOrderNo());
				JSONObject jsonObject = JSONObject.fromObject(param);
				// 定义反序列化 数据格式
				String url = baseService.getApiUri() + "/order/after/completeBackMoney";
				try {
					final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
					ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.put(url, jsonObject.toString(), header, TypeRef);

				}catch (Exception e){
					logger.error(Contant.ERROR_MSG, e);
				}
			}

			try {
				//完结售后单同步数据
				logger.info("完结售后单同步数据 afterSalesId:{}", afterSales.getAfterSalesId());
				orderInfoSyncService.unlockSaleOrderWhenAfterSalesClosedOrFinished(afterSales.getAfterSalesId(),ErpConst.TWO);

				// 【VDERP-12500】售后单审核后，订单状态同步到前台
				switch (AfterSalesProcessEnum.getInstance(afterSalesVo.getType())){
					case AFTERSALES_TH:
						    //判断订单是否是 全部发货
						    try {
								Saleorder saleorder= saleorderMapper.selectBySaleOrderId(afterSales.getOrderId());
								logger.info("完结销售售后退货单对应的销售单saleOrder:{}", JSON.toJSONString(saleorder));
								// VDERP-14103 售后退货完结时，触发确认单审核
								if (ErpConst.TWO.equals(saleorder.getDeliveryStatus())) {
									logger.info("完结销售售后退货单对应的销售单触发确认单审核:{}", saleorder.getSaleorderNo());
									ConfirmationFormRecodeService confirmationFormRecodeService = ErpSpringBeanUtil.getBean(ConfirmationFormRecodeService.class);
									if (Objects.nonNull(confirmationFormRecodeService)) {
										logger.info("完结销售售后退货单,获取到确认单审核处理类,异步触发审核:{}", saleorder.getSaleorderNo());
										CompletableFuture
												.supplyAsync(() ->
																confirmationFormRecodeService.confirmationOrderAudit(request, System.currentTimeMillis(), saleorder.getSaleorderId()),
														OVERAFTER_LISTENER_THREAD_POOL);
									}
								}
								if(ErpConst.TWO.equals(saleorder.getArrivalStatus())){
									saleorderSyncService.syncSaleorderStatus2Mjx(afterSales.getOrderId(), PCOrderStatusEnum.FINISH, SaleorderSyncEnum.AFTER_LISTENER);
								}
							}catch (Exception e){
								logger.error("",e);
							}
							break;
					default:
						;
				}
				//更新modtime为最新完结时间
				AfterSales afterSale = new AfterSales();
				afterSale.setAfterSalesId(afterSales.getAfterSalesId());
				afterSale.setModTime(DateUtil.sysTimeMillis());
				afterSalesMapper.updateByPrimaryKeySelective(afterSale);
			} catch (Exception e) {
				logger.error("完结售后单同步数据 error", e);
			}
		}
    }
}
