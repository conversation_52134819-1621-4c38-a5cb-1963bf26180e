package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeOutPutFeePlainAndSpecialInvoiceDto;
import com.vedeng.erp.kingdee.dto.OutPutFeePlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.OutPutFeeSpecialInvoiceDto;
import com.vedeng.erp.kingdee.service.KingDeeOutPutFeePlainInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeeOutPutFeeSpecialInvoiceApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/12 14:28
 */
@Service
@Slf4j
public class BatchExpenseBlueInvoiceWriter extends BaseWriter<KingDeeOutPutFeePlainAndSpecialInvoiceDto> {

    @Autowired
    private KingDeeOutPutFeeSpecialInvoiceApiService kingDeeOutPutFeeSpecialInvoiceApiService;

    @Autowired
    private KingDeeOutPutFeePlainInvoiceApiService kingDeeOutPutFeePlainInvoiceApiService;

    @Override
    public void doWrite(KingDeeOutPutFeePlainAndSpecialInvoiceDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        if (item.getSpecial()) {
            // 费用专票
            OutPutFeeSpecialInvoiceDto outPutFeeSpecialInvoiceDto = item.getOutPutFeeSpecialInvoiceDto();
            log.info("销售费用专票：{}", JSON.toJSONString(outPutFeeSpecialInvoiceDto));
            outPutFeeSpecialInvoiceDto.setKingDeeBizEnums(KingDeeBizEnums.saveExpenseSpecialInvoice);
            kingDeeOutPutFeeSpecialInvoiceApiService.register(outPutFeeSpecialInvoiceDto, true);
        } else {
            // 费用普票
            OutPutFeePlainInvoiceDto outPutFeePlainInvoiceDto = item.getOutPutFeePlainInvoiceDto();
            log.info("销售费用普票：{}", JSON.toJSONString(outPutFeePlainInvoiceDto));
            outPutFeePlainInvoiceDto.setKingDeeBizEnums(KingDeeBizEnums.saveExpensePlainInvoice);
            kingDeeOutPutFeePlainInvoiceApiService.register(outPutFeePlainInvoiceDto, true);
        }
    }
}