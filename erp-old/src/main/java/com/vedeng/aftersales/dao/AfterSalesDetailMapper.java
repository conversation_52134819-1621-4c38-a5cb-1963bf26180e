package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.aftersales.model.vo.AfterSalesDetailVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;


public interface AfterSalesDetailMapper {
    int deleteByPrimaryKey(Integer afterSalesDetailId);

    int insert(AfterSalesDetail record);

    int insertSelective(AfterSalesDetail record);

    AfterSalesDetail selectByPrimaryKey(Integer afterSalesDetailId);

    int updateByPrimaryKeySelective(AfterSalesDetail record);

    int updateByPrimaryKey(AfterSalesDetail record);
    
    /**
     * <b>Description:</b><br> 查询退款信息
     * @param afterSalesDetail
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月18日 下午2:00:32
     */
    AfterSalesDetailVo getAfterSalesDetailVo(AfterSalesDetail afterSalesDetail);
    
    /**
     * <b>Description:</b><br> 根据afterSalesId删除关联的详情
     * @param afterSalesId
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月20日 下午1:24:44
     */
    int delAfterSalesDetailByafterSalesId(Integer afterSalesId);
    
    /**
     * <b>Description:</b><br> 主键查询
     * @param afterSalesDetailId
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2018年4月16日 下午1:39:33
     */
    AfterSalesDetailVo getAfterSalesDetailVoById(Integer afterSalesDetailId);
    /**
     * 
     * <b>Description:</b><br> 查询售后详情
     * @param afterSales
     * @return
     * @Note
     * <b>Author:</b> scott
     * <br><b>Date:</b> 2018年5月25日 下午12:20:08
     */
	AfterSalesDetail selectadtbyid(AfterSales afterSales);
    //添加是否闪电发货字段
    void addIsLightning(@Param("afterSalesId") Integer afterSalesId, @Param("isLightning") Integer isLightning);
    /**
     * <b>Description:</b><br>
     * 根据采购售后单明细id查询采购单号（定向发货传值）
     *
     * @param afterSalesGoodsId
     * @return java.lang.String
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/6/15 16:53
     */
    String selectBuyConBuyOrderNo(@Param("afterSalesGoodsId")Integer afterSalesGoodsId);

    AfterSalesDetailVo getAfterSalesDetailByAfterSalesId(@Param("afterSalesId") Integer afterSalesId);

    /**
     * 根据售后id查询售后信息，可支持拓展，缺什么补什么，不需要另写
     * @param afterSalesId
     * @return
     */
    AfterSalesDetailVo getAfterSalesInfoByAfterSalesId(@Param("afterSalesId") Integer afterSalesId);

    int updateByPrimaryKeyInfo(AfterSalesVo record);

    int updateByPrimaryKeyDetailInfo(AfterSalesDetail record);

    /**
     * 刷新采购单的最终退款金额
     *
     * @param afterSalesDetailId
     * @param finalRefundableAmount
     * @return
     */
    int updateFinalRefundableAmountByDetalId(@Param("afterSalesDetailId") Integer afterSalesDetailId,
                                             @Param("finalRefundableAmount") BigDecimal finalRefundableAmount);
}