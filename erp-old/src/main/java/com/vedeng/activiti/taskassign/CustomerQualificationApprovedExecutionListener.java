package com.vedeng.activiti.taskassign;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.service.KingDeeCustomerApiService;
import com.vedeng.erp.trader.service.TraderCustomerApiService;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.service.TraderCustomerService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

/**
 * 客户资质审核通过 事件监听器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/6 9:29
 */
@Slf4j
public class CustomerQualificationApprovedExecutionListener implements ExecutionListener {

    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();

    private TraderCustomerApiService traderCustomerApiService = (TraderCustomerApiService)context.getBean("traderCustomerBaseServiceImpl");

    private KingDeeCustomerApiService kingDeeCustomerApiService= (KingDeeCustomerApiService) context.getBean("kingDeeCustomerServiceImpl");


    private TraderCustomerService traderCustomerService =  (TraderCustomerService) context.getBean("traderCustomerService");
    @Override
    public void notify(DelegateExecution delegateExecution) throws Exception {




        // 获取 客户id
        Integer traderCustomerId = (Integer) delegateExecution.getVariable("relateTableKey");
        KingDeeCustomerDto kingDeeCustomerInfo = traderCustomerApiService.getKingDeeCustomerInfo(traderCustomerId);
        if (kingDeeCustomerInfo == null) {
            log.error("客户资质审核通过推送客户信息至金蝶，客户id：{}，未找到客户信息", traderCustomerId);
            return;
        }

        try{
            Integer traderId = traderCustomerService.getTraderIdByTraderCustomId(traderCustomerId);
            Trader trader = traderCustomerService.getBaseTraderByTraderId(traderId);
            // 客户资质状态信息-解决医械购客户资质审核的问题
            Integer traderStatus = trader.getTraderStatus();
            // 认证状态：0 未审核 1 待审核 2 审核通过 3 审核不通过 只有审核通过，才可申请审核成功
            if (!ErpConst.TWO.equals(traderStatus) && ErpConst.TWO.equals(trader.getBelongPlatform())) {
                traderCustomerService.updateYxgTraderStatusComplete(traderId);
//                return new ResultInfo<>(-1, "当前订单的客户资质状态" + traderStatusDesc + ", 请联系耗材运营人员对该客户信息进行审核");
            }
        }catch ( Exception e){
            log.warn("客户资质审核通过时，更新医械购客户资质失败",e);
        }

        kingDeeCustomerApiService.register(kingDeeCustomerInfo);
        log.info("客户资质审核通过推送客户信息至金蝶，客户id：{}", traderCustomerId);
    }
}