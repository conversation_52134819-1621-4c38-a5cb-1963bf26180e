<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.kpi.dao.KpiDailyCountMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.kpi.model.DTO.KpiDailyCountExtDto">
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="TEAM_ID" jdbcType="INTEGER" property="teamId" />
    <result column="GROUP_ID" jdbcType="INTEGER" property="groupId" />
    <result column="KPI_AMOUNT" jdbcType="DECIMAL" property="kpiAmount" />
    <result column="KPI_AMOUNT_PROGRESS" jdbcType="DECIMAL" property="kpiAmountProgress" />
    <result column="KPI_AMOUNT_SCORE" jdbcType="DECIMAL" property="kpiAmountScore" />
    <result column="CO_CUSTOMER_NUM" jdbcType="INTEGER" property="coCustomerNum" />
    <result column="LOST_CUSTOMER_NUM" jdbcType="INTEGER" property="lostCustomerNum" />
    <result column="CUSTOMER_SCORE" jdbcType="DECIMAL" property="customerScore" />
    <result column="BD_NEW_CUSTOMER_NUM" jdbcType="INTEGER" property="bdNewCustomerNum" />
    <result column="BD_LOST_CUSTOMER_NUM" jdbcType="INTEGER" property="bdLostCustomerNum" />
    <result column="BD_CUSTOMER_SCORE" jdbcType="DECIMAL" property="bdCustomerScore" />
    <result column="CHANCE_SUCCESS_NUM" jdbcType="INTEGER" property="chanceSuccessNum" />
    <result column="CHANCE_FAIL_NUM" jdbcType="INTEGER" property="chanceFailNum" />
    <result column="CHANCE_TRANS_PROPORTION" jdbcType="DECIMAL" property="chanceTransProportion" />
    <result column="CHANCE_RANGE_CUSTOMER_NUM" jdbcType="INTEGER" property="chanceRangeCustomerNum" />
    <result column="CHANCE_TRANS_SCORE" jdbcType="DECIMAL" property="chanceTransScore" />
    <result column="KPI_DATE" jdbcType="DATE" property="kpiDate" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, USER_ID, COMPANY_ID, TEAM_ID, GROUP_ID, KPI_AMOUNT, KPI_AMOUNT_PROGRESS, KPI_AMOUNT_SCORE, 
    CO_CUSTOMER_NUM, LOST_CUSTOMER_NUM, CUSTOMER_SCORE, BD_NEW_CUSTOMER_NUM, BD_LOST_CUSTOMER_NUM, 
    BD_CUSTOMER_SCORE, CHANCE_SUCCESS_NUM, CHANCE_FAIL_NUM, CHANCE_TRANS_PROPORTION, 
    CHANCE_RANGE_CUSTOMER_NUM, CHANCE_TRANS_SCORE, KPI_DATE, ADD_TIME, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_KPI_DAILY_COUNT
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_KPI_DAILY_COUNT
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.kpi.model.DO.KpiDailyCountDo" useGeneratedKeys="true">
    insert into T_KPI_DAILY_COUNT (USER_ID, COMPANY_ID, TEAM_ID, 
      GROUP_ID, KPI_AMOUNT, KPI_AMOUNT_PROGRESS, 
      KPI_AMOUNT_SCORE, CO_CUSTOMER_NUM, LOST_CUSTOMER_NUM, 
      CUSTOMER_SCORE, BD_NEW_CUSTOMER_NUM, BD_LOST_CUSTOMER_NUM, 
      BD_CUSTOMER_SCORE, CHANCE_SUCCESS_NUM, CHANCE_FAIL_NUM, 
      CHANCE_TRANS_PROPORTION, CHANCE_RANGE_CUSTOMER_NUM, 
      CHANCE_TRANS_SCORE, KPI_DATE, ADD_TIME, 
      UPDATE_TIME)
    values (#{userId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, #{teamId,jdbcType=INTEGER}, 
      #{groupId,jdbcType=INTEGER}, #{kpiAmount,jdbcType=DECIMAL}, #{kpiAmountProgress,jdbcType=DECIMAL}, 
      #{kpiAmountScore,jdbcType=DECIMAL}, #{coCustomerNum,jdbcType=INTEGER}, #{lostCustomerNum,jdbcType=INTEGER}, 
      #{customerScore,jdbcType=DECIMAL}, #{bdNewCustomerNum,jdbcType=INTEGER}, #{bdLostCustomerNum,jdbcType=INTEGER}, 
      #{bdCustomerScore,jdbcType=DECIMAL}, #{chanceSuccessNum,jdbcType=INTEGER}, #{chanceFailNum,jdbcType=INTEGER}, 
      #{chanceTransProportion,jdbcType=DECIMAL}, #{chanceRangeCustomerNum,jdbcType=INTEGER}, 
      #{chanceTransScore,jdbcType=DECIMAL}, #{kpiDate,jdbcType=DATE}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.kpi.model.DO.KpiDailyCountDo" useGeneratedKeys="true">
    insert into T_KPI_DAILY_COUNT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="teamId != null">
        TEAM_ID,
      </if>
      <if test="groupId != null">
        GROUP_ID,
      </if>
      <if test="kpiAmount != null">
        KPI_AMOUNT,
      </if>
      <if test="kpiAmountProgress != null">
        KPI_AMOUNT_PROGRESS,
      </if>
      <if test="kpiAmountScore != null">
        KPI_AMOUNT_SCORE,
      </if>
      <if test="coCustomerNum != null">
        CO_CUSTOMER_NUM,
      </if>
      <if test="lostCustomerNum != null">
        LOST_CUSTOMER_NUM,
      </if>
      <if test="customerScore != null">
        CUSTOMER_SCORE,
      </if>
      <if test="bdNewCustomerNum != null">
        BD_NEW_CUSTOMER_NUM,
      </if>
      <if test="bdLostCustomerNum != null">
        BD_LOST_CUSTOMER_NUM,
      </if>
      <if test="bdCustomerScore != null">
        BD_CUSTOMER_SCORE,
      </if>
      <if test="chanceSuccessNum != null">
        CHANCE_SUCCESS_NUM,
      </if>
      <if test="chanceFailNum != null">
        CHANCE_FAIL_NUM,
      </if>
      <if test="chanceTransProportion != null">
        CHANCE_TRANS_PROPORTION,
      </if>
      <if test="chanceRangeCustomerNum != null">
        CHANCE_RANGE_CUSTOMER_NUM,
      </if>
      <if test="chanceTransScore != null">
        CHANCE_TRANS_SCORE,
      </if>
      <if test="kpiDate != null">
        KPI_DATE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="kpiAmount != null">
        #{kpiAmount,jdbcType=DECIMAL},
      </if>
      <if test="kpiAmountProgress != null">
        #{kpiAmountProgress,jdbcType=DECIMAL},
      </if>
      <if test="kpiAmountScore != null">
        #{kpiAmountScore,jdbcType=DECIMAL},
      </if>
      <if test="coCustomerNum != null">
        #{coCustomerNum,jdbcType=INTEGER},
      </if>
      <if test="lostCustomerNum != null">
        #{lostCustomerNum,jdbcType=INTEGER},
      </if>
      <if test="customerScore != null">
        #{customerScore,jdbcType=DECIMAL},
      </if>
      <if test="bdNewCustomerNum != null">
        #{bdNewCustomerNum,jdbcType=INTEGER},
      </if>
      <if test="bdLostCustomerNum != null">
        #{bdLostCustomerNum,jdbcType=INTEGER},
      </if>
      <if test="bdCustomerScore != null">
        #{bdCustomerScore,jdbcType=DECIMAL},
      </if>
      <if test="chanceSuccessNum != null">
        #{chanceSuccessNum,jdbcType=INTEGER},
      </if>
      <if test="chanceFailNum != null">
        #{chanceFailNum,jdbcType=INTEGER},
      </if>
      <if test="chanceTransProportion != null">
        #{chanceTransProportion,jdbcType=DECIMAL},
      </if>
      <if test="chanceRangeCustomerNum != null">
        #{chanceRangeCustomerNum,jdbcType=INTEGER},
      </if>
      <if test="chanceTransScore != null">
        #{chanceTransScore,jdbcType=DECIMAL},
      </if>
      <if test="kpiDate != null">
        #{kpiDate,jdbcType=DATE},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.kpi.model.DO.KpiDailyCountDo">
    update T_KPI_DAILY_COUNT
    <set>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="teamId != null">
        TEAM_ID = #{teamId,jdbcType=INTEGER},
      </if>
      <if test="groupId != null">
        GROUP_ID = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="kpiAmount != null">
        KPI_AMOUNT = #{kpiAmount,jdbcType=DECIMAL},
      </if>
      <if test="kpiAmountProgress != null">
        KPI_AMOUNT_PROGRESS = #{kpiAmountProgress,jdbcType=DECIMAL},
      </if>
      <if test="kpiAmountScore != null">
        KPI_AMOUNT_SCORE = #{kpiAmountScore,jdbcType=DECIMAL},
      </if>
      <if test="coCustomerNum != null">
        CO_CUSTOMER_NUM = #{coCustomerNum,jdbcType=INTEGER},
      </if>
      <if test="lostCustomerNum != null">
        LOST_CUSTOMER_NUM = #{lostCustomerNum,jdbcType=INTEGER},
      </if>
      <if test="customerScore != null">
        CUSTOMER_SCORE = #{customerScore,jdbcType=DECIMAL},
      </if>
      <if test="bdNewCustomerNum != null">
        BD_NEW_CUSTOMER_NUM = #{bdNewCustomerNum,jdbcType=INTEGER},
      </if>
      <if test="bdLostCustomerNum != null">
        BD_LOST_CUSTOMER_NUM = #{bdLostCustomerNum,jdbcType=INTEGER},
      </if>
      <if test="bdCustomerScore != null">
        BD_CUSTOMER_SCORE = #{bdCustomerScore,jdbcType=DECIMAL},
      </if>
      <if test="chanceSuccessNum != null">
        CHANCE_SUCCESS_NUM = #{chanceSuccessNum,jdbcType=INTEGER},
      </if>
      <if test="chanceFailNum != null">
        CHANCE_FAIL_NUM = #{chanceFailNum,jdbcType=INTEGER},
      </if>
      <if test="chanceTransProportion != null">
        CHANCE_TRANS_PROPORTION = #{chanceTransProportion,jdbcType=DECIMAL},
      </if>
      <if test="chanceRangeCustomerNum != null">
        CHANCE_RANGE_CUSTOMER_NUM = #{chanceRangeCustomerNum,jdbcType=INTEGER},
      </if>
      <if test="chanceTransScore != null">
        CHANCE_TRANS_SCORE = #{chanceTransScore,jdbcType=DECIMAL},
      </if>
      <if test="kpiDate != null">
        KPI_DATE = #{kpiDate,jdbcType=DATE},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.kpi.model.DO.KpiDailyCountDo">
    update T_KPI_DAILY_COUNT
    set USER_ID = #{userId,jdbcType=INTEGER},
      COMPANY_ID = #{companyId,jdbcType=INTEGER},
      TEAM_ID = #{teamId,jdbcType=INTEGER},
      GROUP_ID = #{groupId,jdbcType=INTEGER},
      KPI_AMOUNT = #{kpiAmount,jdbcType=DECIMAL},
      KPI_AMOUNT_PROGRESS = #{kpiAmountProgress,jdbcType=DECIMAL},
      KPI_AMOUNT_SCORE = #{kpiAmountScore,jdbcType=DECIMAL},
      CO_CUSTOMER_NUM = #{coCustomerNum,jdbcType=INTEGER},
      LOST_CUSTOMER_NUM = #{lostCustomerNum,jdbcType=INTEGER},
      CUSTOMER_SCORE = #{customerScore,jdbcType=DECIMAL},
      BD_NEW_CUSTOMER_NUM = #{bdNewCustomerNum,jdbcType=INTEGER},
      BD_LOST_CUSTOMER_NUM = #{bdLostCustomerNum,jdbcType=INTEGER},
      BD_CUSTOMER_SCORE = #{bdCustomerScore,jdbcType=DECIMAL},
      CHANCE_SUCCESS_NUM = #{chanceSuccessNum,jdbcType=INTEGER},
      CHANCE_FAIL_NUM = #{chanceFailNum,jdbcType=INTEGER},
      CHANCE_TRANS_PROPORTION = #{chanceTransProportion,jdbcType=DECIMAL},
      CHANCE_RANGE_CUSTOMER_NUM = #{chanceRangeCustomerNum,jdbcType=INTEGER},
      CHANCE_TRANS_SCORE = #{chanceTransScore,jdbcType=DECIMAL},
      KPI_DATE = #{kpiDate,jdbcType=DATE},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByUserDay" parameterType="com.vedeng.kpi.model.DTO.KpiDailyCountExtDto" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_KPI_DAILY_COUNT
    where USER_ID = #{userId,jdbcType=INTEGER}
    and KPI_DATE = #{kpiDate,jdbcType=DATE}
  </select>

  <!-- 查询团队指定时间业绩(在代码里分组取最后一条) -->
  <select id="selectByGroupDate" parameterType="com.vedeng.kpi.model.DTO.KpiDailyCountExtDto" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_KPI_DAILY_COUNT
    where USER_ID in
    <foreach collection="userIds" item="item" open="(" close=")" index="index" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
    and KPI_DATE between #{kpiDateStart,jdbcType=DATE} and #{kpiDateEnd,jdbcType=DATE}
  </select>

  <!-- 查询个多用户定时间业绩 -->
  <select id="selectByBatchUser" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_KPI_DAILY_COUNT
    where USER_ID in
    <foreach collection="userIds" item="item" open="(" close=")" index="index" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
    and KPI_DATE &lt;= #{kpiDateEnd,jdbcType=DATE}
    <if test="kpiDateStart != null">
    and  KPI_DATE &gt;= #{kpiDateStart,jdbcType=DATE}
    </if>
  </select>

  <select id="selectAll" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_KPI_DAILY_COUNT
    where KPI_DATE &lt;= #{kpiDateEnd,jdbcType=DATE}
    <if test="kpiDateStart != null">
    and KPI_DATE &gt;= #{kpiDateStart,jdbcType=DATE}
    </if>
    <if test="userIds != null">
    and USER_ID in
      <foreach collection="userIds" item="item" open="(" close=")" index="index" separator=",">
        #{item,jdbcType=INTEGER}
      </foreach>
    </if>
  </select>
</mapper>