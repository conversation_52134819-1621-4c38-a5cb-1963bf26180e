a.btn,
a.btn:hover {
    text-decoration: none;
}

button {
    outline: none;
}

.btn {
    height: 34px;
    line-height: 32px;
    font-size: 12px;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    color: #333;
    border: solid 1px #ced2d9;
    background-color: #f5f7fa;
    padding: 0 15px;
    border-radius: 3px;
    cursor: pointer;
    box-sizing: border-box;
}

.btn:hover {
    background-color: #e6ecf2;
    color: #333;
}

.btn.btn-disabled,
.btn.btn-disabled:hover,
.btn.btn[disabled],
.btn.btn[disabled]:hover {
    color: #999;
    border: solid 1px #e6ebf2;
    background-color: #f5f7fa;
    cursor: not-allowed;
}

.btn.btn-blue {
    color: #fff;
    border: solid 1px #2e8ae6;
    background-color: #2e8ae6;
}

.btn.btn-blue:hover {
    border: solid 1px #217dd9;
    background-color: #217dd9;
}

.btn.btn-blue.btn-disabled,
.btn.btn-blue.btn-disabled:hover,
.btn.btn-blue.btn[disabled],
.btn.btn-blue.btn[disabled]:hover {
    border: solid 1px #99ccff;
    background-color: #99ccff;
    color: #fff;
}

.btn.btn-blue-bd {
    color: #2e8ae6;
    border: solid 1px #2e8ae6;
    background-color: #fff;
}

.btn.btn-blue-bd:hover {
    background-color: #e5f2ff;
}

.btn.btn-blue-bd.btn-disabled,
.btn.btn-blue-bd.btn-disabled:hover,
.btn.btn-blue-bd.btn[disabled],
.btn.btn-blue-bd.btn[disabled]:hover {
    border: solid 1px #3d97f2;
    background-color: #3d97f2;
    color: #3d97f2;
}

.btn.btn-red {
    color: #fff;
    border: solid 1px #e64545;
    background-color: #e64545;
}

.btn.btn-red:hover {
    border: solid 1px #d93636;
    background-color: #d93636;
}

.btn.btn-red.btn-disabled,
.btn.btn-red.btn-disabled:hover,
.btn.btn-red.btn[disabled],
.btn.btn-red.btn[disabled]:hover {
    border: solid 1px #ff9999;
    background-color: #ff9999;
    color: #fff;
}

.btn.btn-red-bd {
    color: #e64545;
    border: solid 1px #e64545;
    background-color: #fff;
}

.btn.btn-red-bd:hover {
    background-color: #ffe5e5;
}


.vd-tip {
    border: 1px solid #CCE5FF;
    background: #E5F2FF;
    padding: 5px 10px 5px 31px;
    position: relative;
    border-radius: 3px;
    margin-bottom: 20px;
}

.vd-tip .vd-tip-icon {
    color: #2E8AE6;
    position: absolute;
    left: 10px;
    top: 3px;
}

.vd-tip .vd-tip-cnt {
    color: #666;
    font-size: 12px;
}

.vd-tip.tip-orange {
    border-color: #FFEACC;
    background: #FFF4E5;
}

.vd-tip.tip-orange .vd-tip-icon {
    color: #FF9500;
}

.vd-tip.tip-green {
    border-color: #C2F2C2;
    background: #E1FAE1;
}

.vd-tip.tip-green .vd-tip-icon {
    color: #FF9500;
}

.vd-tip.tip-red {
    border-color: #FFCCCC;
    background: #FFE5E5;
}

.vd-tip.tip-red .vd-tip-icon {
    color: #E64545;
}

.erp-wrap {
    max-width: 1600px;
    min-width: 1100px;
    margin: 0 auto;
    padding: 0 20px;
}

.erp-wrap .erp-title {
    margin: 20px 0 10px 0;
}

.erp-wrap .erp-title .erp-title-txt {
    font-size: 18px;
    font-weight: 700;
    display: inline-block;
}

.erp-wrap .erp-title .erp-title-label {
    color: #666;
}

.erp-wrap .erp-top-option {
    position: relative;
}

.erp-wrap .erp-top-option .option-btn-wrap {
    position: absolute;
    right: 0;
    bottom: 20px;
}

.erp-wrap .erp-block {
    background: #fff;
    padding: 20px;
    border: 1px solid #EDF0F2;
    margin-bottom: 10px;
}

.erp-wrap .erp-block:last-child {
    margin-bottom: 0;
}

.erp-wrap .erp-block.erp-block-list {
    padding: 20px 0;
}

.erp-wrap .erp-block.erp-block-list .input-checkbox .input-wrap {
    margin-top: 4px;
}

.erp-wrap {
    padding-bottom: 50px;
}

.erp-wrap .search-wrap {
    position: relative;
}

.erp-wrap .search-list {
    display: flex;
    flex-wrap: wrap;
    margin-right: -20px;
}

.erp-wrap .search-item {
    display: flex;
    align-items: center;
    padding-right: 20px;
    margin-bottom: 10px;
    width: 33.33%;
    box-sizing: border-box;
}

.erp-wrap .search-item .item-fields {
    flex: 1;
    display: flex;
}

.erp-wrap .search-item .item-label {
    white-space: nowrap;
    min-width: 90px;
    text-align: right;
}

.erp-wrap .search-item .item-input {
    flex: 1;
}

.erp-wrap .search-item .select {
    flex: 1;
}

.erp-wrap .search-item input {
    width: 100%;
}

.erp-wrap .search-item .btn {
    margin-right: 10px;
}

.erp-wrap .search-item .btn:last-child {
    margin-right: 0;
}

.erp-wrap .item-search-select .select {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    margin-right: -1px;
    position: relative;
    width: 63px;
}

.erp-wrap .item-search-select .select:hover {
    z-index: 2;
}

.erp-wrap .item-search-select .select.focus {
    z-index: 3;
}

.erp-wrap .item-search-select .input-text {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    position: relative;
}

.erp-wrap .item-search-select .input-text:hover {
    z-index: 2;
}

.erp-wrap .item-search-select .input-text:focus {
    z-index: 3;
}

.erp-wrap .item-search-select .item-label {
    min-width: 0;
    text-align: left;
}

.erp-wrap .search-item-gap {
    line-height: 30px;
    padding: 0 5px;
}

.erp-wrap .search-btns {
    text-align: center;
    width: 100%;
}

.erp-wrap .search-btns .btn {
    margin-right: 7px;
}

.erp-wrap .search-more {
    text-align: center;
    width: 100%;
    color: #2E8AE6;
    border-top: 1px solid #edf0f2;
    line-height: 38px;
    margin: 20px -20px -20px -20px;
    cursor: pointer;
}

.erp-wrap .search-more:hover {
    color: #f60;
}

.erp-wrap .search-more .vd-icon {
    margin-left: 5px;
    vertical-align: -2px;
}

.erp-wrap .pager-wrap {
    text-align: center;
    margin-top: 20px;
}

.table .no-data {
    padding: 60px 0;
    text-align: center;
}

.table .no-data:hover {
    background: #fff !important;
}

.table .no-data .icon-caution1 {
    color: #2E8AE6;
    font-size: 32px;
    margin-bottom: 10px;
}


.select {
    min-width: auto;
}


.table-th {
    display: flex;
    border-bottom: 1px solid #EDF0F2;
    color: #999;
    padding: 0 0 10px 20px;
}

.table-te {
    display: flex;
    color: #999;
    padding: 5px 0 10px 20px;
}

.table-tr .tr-lv1 {
    border-bottom: 1px solid #EDF0F2;
    padding-left: 20px;
}

.table-tr .tr-lv1 .tr-list {
    display: flex;
    margin-bottom: -1px;
}

.table-tr .tr-lv1 .tr-item:nth-child(1) {
    box-sizing: border-box;
    padding: 0;
    border-bottom: 0;
}

.table-tr .tr-lv1.hidden .tr-lv2 {
    display: none;
}

.table-tr .tr-lv1.hidden > .tr-list .tr-name-txt .icon-down {
    transform: rotate(-90deg);
}


.table-tr .tr-lv3 .tr-item:nth-child(1) {
    padding-left: 66px;
}

.table-tr .tr-name-txt {
    padding: 15px 0;
    border-bottom: 1px solid #EDF0F2;
    height: 100%;
    box-sizing: border-box;
    position: relative;
    padding-left: 32px;
}

.table-tr .tr-name-txt .icon-down {
    vertical-align: -2px;
    display: inline-block;
    margin: -7px 2px -7px 0;
    height: 32px;
    line-height: 32px;
    cursor: pointer;
    color: #555;
    padding-left: 5px;
    padding-right: 5px;
    transition: transform .2s ease;
    position: absolute;
    left: 0;
}

.table-tr .tr-name-txt .icon-down:hover {
    color: #f60;
}

.table-tr.no-data {
    padding: 60px 0;
    text-align: center;
}

.table-tr.no-data:hover {
    background: #fff !important;
}

.table-tr.no-data .icon-caution1 {
    color: #2E8AE6;
    font-size: 32px;
    margin-bottom: 10px;
}

.tr-item {
    border-bottom: 1px solid #EDF0F2;
    padding: 15px 0;
}

.tr-item,
.th {
    box-sizing: border-box;
    padding-right: 20px;
}

.tr-item:nth-child(1),
.th:nth-child(1) {
    width: 20%;
}

.tr-item:nth-child(2),
.th:nth-child(2) {
    width: 20%;
}

.tr-item:nth-child(3),
.th:nth-child(3) {
    width: 8%;
}

.tr-item:nth-child(4),
.th:nth-child(4) {
    width: 8%;
}

.tr-item:nth-child(5),
.th:nth-child(5) {
    width: 8%;
}

.tr-item:nth-child(6),
.th:nth-child(6) {
    width: 8%;
}

.tr-item:nth-child(7),
.th:nth-child(7) {
    width: 8%;
}
