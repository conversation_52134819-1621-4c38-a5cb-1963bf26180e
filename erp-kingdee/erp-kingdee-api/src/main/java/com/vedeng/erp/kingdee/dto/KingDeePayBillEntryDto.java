package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/9/6 9:11
 */
@Getter
@Setter
public class KingDeePayBillEntryDto {

    /**
     * 结算方式
     */
    private String fsettletypeid;
    /**
     * 付款用途
     */
    private String fpurposeid;
    /**
     * 应付金额
     */
    private BigDecimal fpaytotalamountfor;
    /**
     * 长短款
     */
    private BigDecimal fovershortagefor;
    /**
     * 手续费
     */
    private BigDecimal fhandlingchargefor;
    /**
     * 费用项目
     */
    private String fcostid;
    /**
     * 费用承担部门
     */
    private String fexpensedeptidE;
    /**
     * 我方银行账号
     */
    private String faccountid;
    /**
     * 结算号
     */
    private String fsettleno;
    /**
     * 备注
     */
    private String fcomment;
    /**
     * 登账日期
     */
    private String fpostdate;
    /**
     * 收款方类型
     */
    private String fRecType;
    /**
     * 入账类型
     */
    private String fRuZhangType;
    /**
     * 支付类型
     */
    private String fPayType;
    /**
     * 收款账户账号
     */
    private String fOppositeBankAccount;

    /**
     * 收款账户名称
     */
    private String fOppositeCcountName;

    /**
     * 收款单位开户行
     */
    private String fOppositeBankName;

    /**
     * 收款单位联行号
     */
    private String fCnaps;
    /**
     * 原始订单号
     */
    private String fQzokYsddh;

    /**
     * 归属业务单号
     */
    private String fQzokGsywdh;

    /**
     * 业务类型
     */
    private String fQzokYwlx;

    /**
     * 自动提交银行
     */
    private String FQzokZdtjyh;

    public KingDeePayBillEntryDto(){
        this.fovershortagefor = new BigDecimal(0.0);
        this.fhandlingchargefor = new BigDecimal(0.0);
        this.fPayType = "A";
    }
}
