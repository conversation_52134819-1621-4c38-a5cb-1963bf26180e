
.vd-ui-imgView-container {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100000;

    .vd-ui-black-bg {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background-color: rgba(0, 0, 0, .6);
    }

    // 通用按钮
    .vd-ui-btn {
        position: absolute;
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        opacity: .8;
        cursor: pointer;
        box-sizing: border-box;
        user-select: none;
    }

    // 关闭按钮
    .vd-ui-close {
        width: 40px;
        height: 40px;
        top: 20px;
        right: 20px;
        font-size: 20px;
        color: #fff;
        background-color: rgba(0, 0, 0, .5);
        &:hover {
            background-color: rgba(0, 0, 0, .7);
        }
        &:active {
            background-color: rgba(0, 0, 0, .9);
        }
    }

    // 左按钮
    .vd-ui-arrow-left {
        left: 40px;
        top: 50%;
        transform: translateY(-50%);
        width: 40px;
        height: 40px;
        font-size: 20px;
        color: #fff;
        border-color: #fff;
        background-color: rgba(0, 0, 0, .5);
        &:hover {
            background-color: rgba(0, 0, 0, .7);
        }
        &:active {
            background-color: rgba(0, 0, 0, .9);
        }
    }

    // 右按钮
    .vd-ui-arrow-right {
        right: 40px;
        top: 50%;
        transform: translateY(-50%);
        width: 40px;
        height: 40px;
        font-size: 20px;
        color: #fff;
        border-color: #fff;
        background-color: rgba(0, 0, 0, .5);
        &:hover {
            background-color: rgba(0, 0, 0, .7);
        }
        &:active {
            background-color: rgba(0, 0, 0, .9);
        }
    }

    // 功能栏
    .vd-ui-feature {
        bottom: 40px;
        left: 50%;
        transform: translateX(-50%);
        padding: 0 15px;
        border-radius: 20px;
        overflow: hidden;
        background-color: rgba(0, 0, 0, .5);
        &:hover {
            background-color: rgba(0, 0, 0, .7);
        }
        .feature-btn {
            width: 30px;
            height: 35px;
            display: flex;
            justify-content: center;
            align-items: center;
            i {
                font-size: 20px;
                color: rgba(#fff, .6);
            }
            &:nth-child(3) {
                margin: 0 10px;
            }
            &:hover i {
                color: #fff;
            }
        }
    }

    .vd-ui-img-view {
        position: relative;
        z-index: 2;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
        .img-viewer {
            max-width: 100%;
            max-height: 100%;
        }
    }

    .vd-ui-cover-img {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        .loading {
            i {
                display: inline-block;
                font-size: 48px;
                color: #fff;
                margin-bottom: 10px;
                animation: rotation 1.8s linear infinite;
            }
            p {
                font-size: 14px;
                color: #fff;
            }
        }
        .error {
            i {
                display: inline-block;
                margin-bottom: 10px;
                font-size: 40px;
                color: #fff;
            }
            p {
                font-size: 14px;
                color: #fff;
            }
        }
    }
    
    @keyframes rotation {
        from {
            transform : rotate(0deg);
        }
      
        to {
            transform : rotate(360deg);
        }
    }
}

.ui-img-preview-list {
    display: flex;
    align-items: center;

    .ui-img-item {
        width: 80px;
        height: 80px;
        border-radius: 3px;
        margin-right: 8px;
        overflow: hidden;
        position: relative;

        &:last-child {
            margin-right: 0;
        }
        
        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .ui-img-preview-handler {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background: rgba(0, 0, 0, .6);
            color: #fff;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            cursor: pointer;
            display: none;
        }

        &:hover {
            .ui-img-preview-handler {
                display: flex;
            }
        }
    }
}