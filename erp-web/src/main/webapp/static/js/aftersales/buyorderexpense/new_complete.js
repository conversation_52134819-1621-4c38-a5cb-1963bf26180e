//采购订单审核
function expenseAfterSaleAudit(){

	$.ajax({
		type: "POST",
		url: "/old/buyorderExpenseAfterSales/complementAfterSaleTask.do",
		data: $('#complement').serialize(),
		dataType:'json',
		success: function(data){
			if(data.code == 0){
				layer.close(index);
				window.parent.location.href = page_url + '/buyorderExpense/aftersale/detail.do?expenseAfterSalesId='+data.data;
			}else{
				layer.confirm(data.message,{btn: ['确认']},function () {
					window.parent.location.reload();
				});

			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});
}



function complementTask(){
	checkLogin();
	var comment = $("input[name='comment']").val()
	var taskId = $("input[name='taskId']").val()
	var pass = $("input[name='pass']").val()
	var type = $("input[name='type']").val()
	var expenseAfterSalesId = $("input[name='expenseAfterSalesId']").val();

	if(pass =="false" && comment == ""){
		warnTips("comment","请填写备注");
		return false;
	}
	if(comment.length > 256){
		warnTips("comment","备注内容不允许超过256个字符");
		return false;
	}

	//采购费用售后订单的操作
	expenseAfterSaleAudit();

		

}