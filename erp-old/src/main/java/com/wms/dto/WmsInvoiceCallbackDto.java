package com.wms.dto;

/**
 * WMS发起开票请求运输类
 *
 * <AUTHOR>
 * @date 2020/8/18 13:51:12
 */
public class WmsInvoiceCallbackDto extends AbstractCommonDto {

    /**
     * ERP单据编号（Y）
     */
    private String SOReference1;

    /**
     * WMS单据编号（Y）
     */
    private String OrderNo;

    /**
     * 开票申请ID（Y）
     */
    private String INVOICEID;

    /**
     * 预留字段
     */
    private String UserDefine1;

    /**
     * 预留字段
     */
    private String UserDefine2;

    /**
     * 预留字段
     */
    private String UserDefine3;

    /**
     * 预留字段
     */
    private String UserDefine4;

    /**
     * 预留字段
     */
    private String UserDefine5;

    /**
     * 备注
     */
    private String Notes;

    public String getSOReference1() {
        return SOReference1;
    }

    public void setSOReference1(String SOReference1) {
        this.SOReference1 = SOReference1;
    }

    public String getOrderNo() {
        return OrderNo;
    }

    public void setOrderNo(String orderNo) {
        OrderNo = orderNo;
    }

    public String getINVOICEID() {
        return INVOICEID;
    }

    public void setINVOICEID(String INVOICEID) {
        this.INVOICEID = INVOICEID;
    }

    public String getUserDefine1() {
        return UserDefine1;
    }

    public void setUserDefine1(String userDefine1) {
        UserDefine1 = userDefine1;
    }

    public String getUserDefine2() {
        return UserDefine2;
    }

    public void setUserDefine2(String userDefine2) {
        UserDefine2 = userDefine2;
    }

    public String getUserDefine3() {
        return UserDefine3;
    }

    public void setUserDefine3(String userDefine3) {
        UserDefine3 = userDefine3;
    }

    public String getUserDefine4() {
        return UserDefine4;
    }

    public void setUserDefine4(String userDefine4) {
        UserDefine4 = userDefine4;
    }

    public String getUserDefine5() {
        return UserDefine5;
    }

    public void setUserDefine5(String userDefine5) {
        UserDefine5 = userDefine5;
    }

    public String getNotes() {
        return Notes;
    }

    public void setNotes(String notes) {
        Notes = notes;
    }

    @Override
    public String toString() {
        return "WmsInvoiceCallbackDto{" +
                "SOReference1='" + SOReference1 + '\'' +
                ", OrderNo='" + OrderNo + '\'' +
                ", INVOICEID='" + INVOICEID + '\'' +
                ", UserDefine1='" + UserDefine1 + '\'' +
                ", UserDefine2='" + UserDefine2 + '\'' +
                ", UserDefine3='" + UserDefine3 + '\'' +
                ", UserDefine4='" + UserDefine4 + '\'' +
                ", UserDefine5='" + UserDefine5 + '\'' +
                ", Notes='" + Notes + '\'' +
                '}';
    }
}
