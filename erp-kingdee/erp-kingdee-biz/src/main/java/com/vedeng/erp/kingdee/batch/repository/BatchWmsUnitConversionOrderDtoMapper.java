package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchWmsUnitConversionOrderDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/2/22 9:43
 **/
public interface BatchWmsUnitConversionOrderDtoMapper {
    /**
     * delete by primary key
     * @param wmsUnitConversionOrderId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer wmsUnitConversionOrderId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(BatchWmsUnitConversionOrderDto record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(BatchWmsUnitConversionOrderDto record);

    /**
     * select by primary key
     * @param wmsUnitConversionOrderId primary key
     * @return object by primary key
     */
    BatchWmsUnitConversionOrderDto selectByPrimaryKey(Integer wmsUnitConversionOrderId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BatchWmsUnitConversionOrderDto record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BatchWmsUnitConversionOrderDto record);

    int batchInsert(@Param("list") List<BatchWmsUnitConversionOrderDto> list);


}