package com.vedeng.goods.mapper;
import com.vedeng.goods.dto.BrandFrontDto;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.vedeng.goods.domain.entity.Brand;

public interface BrandMapper {
    int deleteByPrimaryKey(Integer brandId);

    int insert(Brand record);

    int insertSelective(Brand record);

    Brand selectByPrimaryKey(Integer brandId);

    int updateByPrimaryKeySelective(Brand record);

    int updateByPrimaryKeyWithBLOBs(Brand record);

    int updateByPrimaryKey(Brand record);

    /**
     * <AUTHOR>
     * @desc 根据品牌id查询品牌名称
     * @param brandId
     * @return
     */
    String queryBrandNameById(Integer brandId);

    /**
     *查询所有品牌
     */
    List<BrandFrontDto> queryAllBrand();


    /**
     * 根据 品牌名模糊查询
     * @param likeBrandName
     * @return
     */
    List<BrandFrontDto> selectByBrandNameLike(@Param("likeBrandName")String likeBrandName, @Param("limit") Integer limit);

    /**
     * 根据品牌id集合查询品牌信息
     *
     * @param brandIdList 品牌id集合
     * @return List<BrandFrontDto>
     */
    List<BrandFrontDto> getByBrandIdList(@Param("brandIdList") List<Integer> brandIdList);
    /**
     * 品牌
     * @param brandIds
     * @return
     */
    List<BrandFrontDto> selectByBrandIds(@Param("lists")List<Integer> brandIds);


}