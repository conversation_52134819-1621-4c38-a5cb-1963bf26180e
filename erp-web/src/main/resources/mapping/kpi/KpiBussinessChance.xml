<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.kpi.dao.KpiChanceMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.kpi.model.DO.KpiOrderLogDo">
        <id column="ID" jdbcType="INTEGER" property="id" />
        <result column="USER_ID" jdbcType="INTEGER" property="userId" />
        <result column="TEAM_ID" jdbcType="INTEGER" property="teamId" />
        <result column="GROUP_ID" jdbcType="INTEGER" property="groupId" />
        <result column="OPERATION" jdbcType="INTEGER" property="operation" />
        <result column="KPI_DATE" jdbcType="DATE" property="kpiDate" />
        <result column="ORDER_ID" jdbcType="INTEGER" property="orderId" />
        <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
        <result column="ORDER_TYPE" jdbcType="INTEGER" property="orderType" />
        <result column="PAYMENT_TYPE" jdbcType="INTEGER" property="paymentType" />
        <result column="PAYMENT_COMMENTS" jdbcType="VARCHAR" property="paymentComments" />
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
        <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
        <result column="TRADER_TYPE" jdbcType="INTEGER" property="traderType" />
        <result column="TRADER_SUBJECT" jdbcType="INTEGER" property="traderSubject" />
        <result column="ROSS_MARGIN" jdbcType="FLOAT" property="rossMargin" />
        <result column="IS_SPEC_ORDER" jdbcType="INTEGER" property="isSpecOrder" />
        <result column="AFTER_SALE_ID" jdbcType="INTEGER" property="afterSaleId" />
        <result column="AFTER_SALE_NO" jdbcType="VARCHAR" property="afterSaleNo" />
        <result column="AFTER_SALE_COMMENT" jdbcType="VARCHAR" property="afterSaleComment" />
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
        <result column="KPI_AMOUNT" jdbcType="DECIMAL" property="kpiAmount" />
        <result column="ORDER_SUM_AMOUNT" jdbcType="DECIMAL" property="orderSumAmount" />
        <result column="CHANCE_NO" jdbcType="VARCHAR" property="chanceNo" />
        <result column="CHANCE_ID" jdbcType="INTEGER" property="chanceId" />
        <result column="CHANCE_TYPE" jdbcType="INTEGER" property="chanceType" />
        <result column="CHANCE_SOURCE" jdbcType="INTEGER" property="chanceSource" />
        <result column="CHANCE_TRANS_FLAG" jdbcType="INTEGER" property="chanceTransFlag" />
        <result column="CHANCE_TRANS_DAYS" jdbcType="INTEGER" property="chanceTransDays" />
        <result column="COMMENT" jdbcType="VARCHAR" property="comment" />
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <resultMap id="QueryResultMap" type="com.vedeng.kpi.model.DTO.KpiDataQueryDto">
        <result column="USER_ID" jdbcType="INTEGER" property="userId" />
        <result column="CHANCE_ID" jdbcType="INTEGER" property="chanceId" />
        <result column="KPI_DATE_START" jdbcType="INTEGER" property="kpiDateStart" />
        <result column="KPI_DATE_START" jdbcType="INTEGER" property="kpiDateEnd" />
        <result column="CO_CUSTOMERS" jdbcType="INTEGER" property="coCustomers" />
        <result column="LOST_CUSTOMERS" jdbcType="INTEGER" property="lostCustomers" />
        <result column="CO_ORDER_NUM" jdbcType="INTEGER" property="coOrderNum" />
        <result column="IS_ALL_RETURN" jdbcType="INTEGER" property="isAllReturn" />
    </resultMap>

    <!-- 获取团队用户的商机(并关联客户) 以IS_KPI的值是否大于1判断是否有计入过业绩的订单(老客户). -->
    <select id="getGroupRangeBussinessChances" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultType="com.vedeng.kpi.model.DTO.KpiDataQueryDto">
        select chance.USER_ID,
        chance.BUSSINESS_CHANCE_ID as CHANCE_ID,
        sum(if (s.VALID_STATUS = 1 and s.IS_SALES_PERFORMANCE = 1 and
            (s.SALES_PERFORMANCE_TIME is null or s.SALES_PERFORMANCE_TIME  &lt; chance.ADD_TIME),1,0)
        ) as IS_KPI
        from T_BUSSINESS_CHANCE chance
        left join
        T_TRADER_CONTACT contact
        on contact.MOBILE = chance.MOBILE
        left join
        T_SALEORDER s
        on s.TRADER_ID = contact.TRADER_ID
        where chance.ADD_TIME between #{startMillisecond} and #{endMillisecond}
        and chance.COMPANY_ID = 1
        and chance.MOBILE is not null
        and chance.MOBILE != ''
        and chance.TYPE in (
        391, 394
        )
        and chance.MERGE_STATUS in (0,2)
        and chance.USER_ID in
        <foreach collection="userIds" item="item" open="(" close=")" index="index" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
        group by chance.USER_ID,
        chance.BUSSINESS_CHANCE_ID
        having IS_KPI = 0
        union all
        select chance.USER_ID,
        chance.BUSSINESS_CHANCE_ID as CHANCE_ID,
        0
        from T_BUSSINESS_CHANCE chance
        where chance.ADD_TIME between #{startMillisecond} and #{endMillisecond}
        and chance.COMPANY_ID = 1
        and (chance.MOBILE is null or chance.MOBILE = '')
        and chance.TYPE in (
        391, 394
        )
        and chance.MERGE_STATUS in (0,2)
        and chance.USER_ID in
        <foreach collection="userIds" item="item" open="(" close=")" index="index" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
        group by chance.USER_ID,
        chance.BUSSINESS_CHANCE_ID
    </select>

    <!-- 获取用户分配到的总机商机. -->
    <select id="getReceivedZjBussinessChances" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultType="com.vedeng.kpi.model.DTO.KpiDataQueryDto">
        select
        a.BUSSINESS_CHANCE_ID as CHANCE_ID,
        a.USER_ID,
        a.ORG_ID as GROUP_ID,
        a.TRADER_ID,
        a.TYPE as CHANCE_TYPE,
        a.SOURCE as CHANCE_SOURCE
        from
        T_BUSSINESS_CHANCE a
        where
        a.COMPANY_ID = #{companyId}
        and a.STATUS != 4
        and a.TYPE in (
        391, 394
        )
        and a.MERGE_STATUS in (0,2)
        and a.USER_ID = #{userId,jdbcType=INTEGER}
        and a.ADD_TIME between #{startMillisecond} and #{endMillisecond}
    </select>

    <!-- 查询该客户是否已被转化过 -->
    <select id="getChanceExistsNo" parameterType="com.vedeng.kpi.model.DTO.KpiDataQueryDto" resultMap="QueryResultMap">
    select BUSSINESS_CHANCE_NO
    from T_BUSSINESS_CHANCE
    where TRADER_ID = #{traderId,jdbcType=INTEGER}
    and STATUS = 3
  </select>

</mapper>