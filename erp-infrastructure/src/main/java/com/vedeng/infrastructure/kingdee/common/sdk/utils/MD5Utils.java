package com.vedeng.infrastructure.kingdee.common.sdk.utils;


import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

/* loaded from: k3cloud-webapi-sdk7.9.2.jar:com/kingdee/bos/webapi/utils/MD5Utils.class */
@Slf4j
public class MD5Utils {
    public static String encrypt(String dataStr) {
        try {
            MessageDigest m = MessageDigest.getInstance("MD5");
            m.update(dataStr.getBytes("UTF8"));
            byte[] s = m.digest();
            String result = "";
            for (int i = 0; i < s.length; i++) {
                result = result + Integer.toHexString((255 & s[i]) | -256).substring(6);
            }
            return result;
        } catch (Exception e) {
            log.error("【encrypt】处理异常",e);
            return "";
        }
    }

    public static String hashMAC(String data, String secret) {
        try {
            Mac kdmac = Mac.getInstance("HmacSHA256");
            kdmac.init(new SecretKeySpec(secret.getBytes(), "HmacSHA256"));
            return Base64Utils.encodingToBase64(bytesToHex(kdmac.doFinal(data.getBytes())).getBytes());
        } catch (Exception e) {
            log.error("【hashMAC】处理异常",e);
            return null;
        }
    }

    private static String bytesToHex(byte[] hashInBytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : hashInBytes) {
            String hex = Integer.toHexString(b & 255);
            if (hex.length() < 2) {
                hex = "0" + hex;
            }
            sb.append(hex);
        }
        return sb.toString();
    }
}
