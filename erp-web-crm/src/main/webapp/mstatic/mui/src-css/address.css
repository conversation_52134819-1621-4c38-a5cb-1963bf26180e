.vd-ui-address .vd-ui-address-panel {
  height: calc(100% - 53px);
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap {
  height: 100%;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .vd-ui-address-nav {
  padding: 0 10px;
  border-bottom: solid 1px #EBEFF2;
  display: flex;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .vd-ui-address-nav .nav-item {
  position: relative;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #999;
  margin-right: 30px;
  max-width: 90px;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .vd-ui-address-nav .nav-item.active {
  font-weight: 700;
  color: #09f;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .vd-ui-address-nav .nav-item.active::after {
  content: "";
  display: block;
  border-bottom: solid 3px #09f;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .address-choose-panel {
  max-height: calc(100% - 41px);
  overflow-y: auto;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .address-choose-panel .ap-list .ap-item {
  padding: 10px;
  font-size: 12px;
  color: #000;
  display: flex;
  align-items: center;
  border-bottom: solid 1px #F5F7FA;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .address-choose-panel .ap-list .ap-item:last-child {
  border: none;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .address-choose-panel .ap-list .ap-item .selected {
  height: 16px;
  line-height: 16px;
  margin-right: 5px;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .address-choose-panel .ap-list .ap-item .selected > i {
  font-size: 16px;
  color: #ccc;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .address-choose-panel .ap-list .ap-item.active {
  background: #E0F3FF;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .address-choose-panel .ap-list .ap-item.active .selected i {
  color: #09f;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap {
  height: 100%;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list {
  max-height: 100%;
  overflow-y: auto;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list .filter-item {
  padding: 0 10px;
  height: 33px;
  line-height: 33px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list .filter-item .filter-selected {
  width: 16px;
  height: 16px;
  line-height: 16px;
  margin-right: 5px;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list .filter-item .filter-selected > i {
  height: 16px;
  font-size: 16px;
  color: #ccc;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list .filter-item .filter-selected > i.icon-radio3 {
  color: #09f;
  display: none;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list .filter-item.active {
  background: #E0F3FF;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list .filter-item.active .filter-selected > i.icon-radio3 {
  display: block;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list .filter-item.active .filter-selected > i.icon-radio1 {
  display: none;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .no-filter {
  padding: 5px 0;
  color: #999;
  text-align: center;
}
.ui-form-address-wrap .form-address-view {
  padding-left: 10px;
}
.ui-form-address-wrap .form-address-view .form-address-show {
  display: flex;
  align-items: center;
}
.ui-form-address-wrap .form-address-view .form-address-show .value {
  flex: 1;
  min-width: 0;
}
.ui-form-address-wrap .form-address-view .form-address-show .icon-error2 {
  width: 36px;
  height: 38px;
  flex-shrink: 0;
  font-size: 16px;
  color: #666;
  display: flex;
  justify-content: center;
  align-items: center;
}
.form-address-panel {
  height: calc(100vh - 45px - 53px);
  height: calc(100vh - 98px - constant(safe-area-inset-bottom));
  height: calc(100vh - 98px - env(safe-area-inset-bottom));
}
.form-address-panel .panel-inner {
  height: calc(100% - 53px);
}
