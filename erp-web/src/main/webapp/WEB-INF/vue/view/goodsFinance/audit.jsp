<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<div id="app" style="display: none;">

    <template>
        <el-form ref="form" :model="form" label-width="130px" :rules="rules">
            <el-form-item label="订货号">
                <span>{{goodsFinanceInfoDto.skuNo}}</span>
            </el-form-item>

            <el-form-item label="商品名称">
                <span>{{goodsFinanceInfoDto.showName}}</span>
            </el-form-item>

            <el-form-item label="审核状态">
                <span v-if="goodsFinanceInfoDto.auditStatus == 0" style="color: #909399;">待提交</span>
                <span v-if="goodsFinanceInfoDto.auditStatus == 1" style="color: #409EFF;">审核中</span>
                <span v-if="goodsFinanceInfoDto.auditStatus == 2" style="color: #67C23A;">审核通过</span>
                <span v-if="goodsFinanceInfoDto.auditStatus == 3" style="color: #F56C6C;">审核驳回</span>
            </el-form-item>

            <el-form-item label="支持安调" prop="auditInstallation">
                <el-radio-group v-model="form.auditInstallation" :disabled="isDisabled">
                    <el-radio :label="0">否</el-radio>
                    <el-radio :label="1">是</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="审批意见" prop="auditRemark" v-if="goodsFinanceInfoDto.auditStatus==1">
                <el-input type="textarea"
                          v-model="form.auditRemark"
                          placeholder="请输入审批意见"
                          maxlength="500"
                          show-word-limit
                          :rows="3"
                          :style="{ width: '300px' }"></el-input>
            </el-form-item>

        </el-form>
    </template>


    <template>
        <el-table
                :data="goodsFinanceInfoDto.auditInfoDtoList"
                border
                style="width: 100%"
                v-if="goodsFinanceInfoDto.auditStatus!=0"
                align="center"
        >
            <el-table-column
                    prop="user"
                    label="操作人"
                    align="center"
                    width="180">
            </el-table-column>
            <el-table-column
                    prop="time"
                    label="操作时间"
                    align="center"
                    width="180">
                <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.time)}}</span>
                </template>
            </el-table-column>
            <el-table-column
                    prop="oper"
                    align="center"
                    label="操作事项">
            </el-table-column>
            <el-table-column
                    prop="remark"
                    align="center"
                    label="备注">
            </el-table-column>
        </el-table>
    </template>

    <template>
        <div style="text-align: center;margin-top: 20px; ">
            <el-button type="primary" @click="onSubmit('1')"
                       :loading="onSubmitLoading"
                       v-if="goodsFinanceInfoDto.auditStatus!=1 && goodsFinanceInfoDto.auditButton">提交审核
            </el-button>
            <el-button type="primary" @click="onSubmit('2')"
                       :loading="onSubmitLoading"
                       v-if="goodsFinanceInfoDto.auditStatus==1 && goodsFinanceInfoDto.auditButton">通过
            </el-button>
            <el-button type="primary" @click="onSubmit('3')"
                       :loading="onSubmitLoading"
                       v-if="goodsFinanceInfoDto.auditStatus==1 && goodsFinanceInfoDto.auditButton">驳回
            </el-button>
        </div>
    </template>

</div>

<script src="${pageContext.request.contextPath}/static/api/goods/finance/goodsFinance.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    const goodsFinanceInfo = {
        goodsFinanceId: '${goodsFinanceId}'
    };

    new Vue({
        el: '#app',
        data() {
            return {
                onSubmitLoading: false,
                form: {
                    goodsFinanceId: goodsFinanceInfo.goodsFinanceId,
                    auditInstallation: 0,
                    auditRemark: ''
                },
                isDisabled: false,
                goodsFinanceInfoDto: {
                    showName: '',
                    skuNo: '',
                    auditStatus: '',
                },
                rules: {
                    auditRemark: [
                        {required: true, message: '审核备注必填', trigger: 'blur'},
                        {max: 500, message: '内容超过500个字，请检查', trigger: 'blur'}
                    ]
                }
            }
        },
        created() {
            this.initData();
        },
        watch: {
            'goodsFinanceInfoDto.auditStatus'(newValue) {
                this.isDisabled = newValue === 1;
            }
        },
        mounted() {
            loadingApp()
        },
        methods: {
            async initData() {
                if (goodsFinanceInfo.goodsFinanceId != null && goodsFinanceInfo.goodsFinanceId != "") {
                    await viewData(goodsFinanceInfo).then(res => {
                        this.goodsFinanceInfoDto = res.data.data;
                        this.form.auditInstallation = this.goodsFinanceInfoDto.auditInstallation;

                    })
                }
            },


            onSubmit(status) {
                this.onSubmitLoading = true;
                this.$refs['form'].validate((valid) => {
                    if (valid) {
                        this.form.auditStatus = status
                        audit(this.form).then(res => {
                            if (res.data.code == 0) {
                                debugger
                                this.$message({
                                    message: '审核成功!',
                                    type: 'success',
                                    onClose: () => {
                                        this.closeThis()
                                    }
                                });
                                window.parent.location.reload();
                            } else {
                                this.$message({
                                    message: res.data.message,
                                    type: 'error',
                                });
                                this.onSubmitLoading = false;
                            }
                        }).catch((err) => {
                            console.log(err);
                        })
                        this.onSubmitLoading = false
                    }
                    this.onSubmitLoading = false
                })
            }
        }

    })
</script>




