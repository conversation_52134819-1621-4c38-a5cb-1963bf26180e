<%@ tag language="java" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<%@attribute name="list" type="java.lang.Object" required="true" %>
<%@attribute name="terminalTypes" type="java.util.List" required="true" %>
<%@attribute name="regions" type="java.util.List" required="true" %>

<div class="detail-info-field width2">
    <div class="detail-info-label">是否需要报备：</div>
    <div class="detail-info-txt">
        <c:choose>
            <c:when test="${list.isNeedReport == 1}">
                是
                <div class="goods-tip-wrap width12">
                    <span class="vd-icon icon-info1"></span>
                    <div class="goods-tip-cnt">
                        <div class="table-item item-col">
                            <div class="table-td">
                                是否获得授权：
                                <c:if test="${list.isAuthorized eq 1}">
                                    有授权
                                </c:if>
                                <c:if test="${list.isAuthorized eq 0}">
                                    无授权
                                </c:if>
                            </div>
                        </div>
                        <c:if test="${list.isAuthorized eq 1}">
                            <div class="table-item item-col">
                                <div class="table-td">
                                    授权范围：
                                    <c:forEach items="${list.skuAuthorizationVo.skuAuthorizationItemVoList}" var="skuAuthorizationItem">
                                        <c:choose>
                                            <c:when test="${fn:length(skuAuthorizationItem.regionIds) == fn:length(regions)}">
                                                “全国”
                                            </c:when>
                                            <c:otherwise>
                                                <c:forEach items="${skuAuthorizationItem.regionIds}" var="regionId"  varStatus="regionStatus">
                                                    <c:if test="${regionStatus.first}">
                                                        "
                                                    </c:if>
                                                    <c:forEach items="${regions}" var="region">
                                                        <c:if test="${region.regionId eq regionId}">
                                                            ${region.regionName}
                                                        </c:if>
                                                    </c:forEach>
                                                    <c:if test="${!regionStatus.last}">
                                                        、
                                                    </c:if>
                                                    <c:if test="${regionStatus.last}">
                                                        "
                                                    </c:if>
                                                </c:forEach>
                                            </c:otherwise>
                                        </c:choose>
                                        的
                                        <c:choose>
                                            <c:when test="${fn:length(skuAuthorizationItem.terminalTypeIds) == fn:length(terminalTypes)}">
                                                “全部终端”
                                            </c:when>
                                            <c:otherwise>
                                                <c:forEach items="${skuAuthorizationItem.terminalTypeIds}" var="terminalTypeId" varStatus="terminalTypeStatus">
                                                    <c:if test="${terminalTypeStatus.first}">
                                                        "
                                                    </c:if>
                                                    <c:forEach items="${terminalTypes}" var="terminalType">
                                                        <c:if test="${terminalType.sysOptionDefinitionId eq terminalTypeId}">
                                                            ${terminalType.title}
                                                        </c:if>
                                                    </c:forEach>
                                                    <c:if test="${!terminalTypeStatus.last}">
                                                        、
                                                    </c:if>
                                                    <c:if test="${terminalTypeStatus.last}">
                                                        "
                                                    </c:if>
                                                </c:forEach>
                                            </c:otherwise>
                                        </c:choose>
                                        已获得授权
                                        </br>
                                    </c:forEach>
                                    <label style="color: red;font-weight: bold">注：除以上范围，需要报备。</label>
                                </div>
                            </div>
                        </c:if>
                    </div>
                </div>
            </c:when>
            <c:when test="${list.isNeedReport == 0}">
                否
            </c:when>
            <c:otherwise>
                -
            </c:otherwise>
        </c:choose>
    </div>
</div>


