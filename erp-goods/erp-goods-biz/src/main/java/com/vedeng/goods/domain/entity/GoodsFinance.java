package com.vedeng.goods.domain.entity;

import java.io.Serializable;
import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

/**
 * 商品列表（财务专用）
 */
@Data
public class GoodsFinance extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    private Long goodsFinanceId;

    /**
     * skuID
     */
    private Integer skuId;

    /**
     * 订货号
     */
    private String skuNo;

    /**
     * 是否医疗器械 1是 0否
     */
    private Integer isMedicalEquipment;

    /**
     * 医疗器械细分类
     */
    private String medicalEquipmentType;

    /**
     * 医疗器械用途
     */
    private String medicalEquipmentUse;

    /**
     * 医疗器械产线
     */
    private String medicalEquipmentLine;

    /**
     * 是否推送金蝶 0否 1是
     */
    private Integer isPush;

    /**
     * 推送时间
     */
    private Date pushTime;

    /**
     * 编辑提交前V_CORE_SKU的ADD_TIME
     */
    private Date skuAddTime;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新备注
     */
    private String updateRemark;


    /**
     * 审核状态:0待提交1审核中2审核通过3已驳回
     */
    private Integer auditStatus;

    /**
     * 是否安调 0否 1是
     */
    private Integer isInstallation;

    /**
     * 审核是否安调 0否 1是
     */
    private Integer auditInstallation;

    /**
     * 审批发起时间
     */
    private Date applyTime;

    /**
     * 发起人ID
     */
    private Integer applyUserId;

    /**
     * 发起人名称
     */
    private String applyUsername;

    /**
     * 审批时间
     */
    private Date auditTime;

    /**
     * 审批人ID
     */
    private Integer auditUserId;

    /**
     * 审批人名称
     */
    private String auditUsername;

    /**
     * 审批备注
     */
    private String auditRemark;


    private static final long serialVersionUID = 1L;
}