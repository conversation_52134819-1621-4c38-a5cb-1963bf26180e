package com.vedeng.order.service;

import com.vedeng.order.model.RemarkComponentTree;
import com.vedeng.order.model.query.LabelQuery;

import java.util.List;
import java.util.Map;

/**
 * @Description:  备注组件业务逻辑接口
 * @Author:       davis
 * @Date:         2021/4/14 下午5:27
 * @Version:      1.0
 */
public interface RemarkComponentService {

    /**
     * 获取所有备注组件
     * @param labelQuery 查询菜蔬
     * @return 备注组件集合
     */
    List<RemarkComponentTree> getInitComponent(LabelQuery labelQuery);

    /**
     * 获取备注标签HTML
     * @param labelQuery
     * @return
     */
    String getComponentHtml(LabelQuery labelQuery);

    List<Map<String, Object>> findComponentList(Map map);
}
