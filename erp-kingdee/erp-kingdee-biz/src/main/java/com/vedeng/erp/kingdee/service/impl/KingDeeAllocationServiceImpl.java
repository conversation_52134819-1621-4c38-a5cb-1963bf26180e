package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeAllocationCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeAllocationEntity;
import com.vedeng.erp.kingdee.dto.KingDeeAllocationDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeAllocationCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeAllocationConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeAllocationRepository;
import com.vedeng.erp.kingdee.service.KingDeeAllocationApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2023/11/18 13:57
 **/
@Service
@Slf4j
public class KingDeeAllocationServiceImpl extends KingDeeBaseServiceImpl<
        King<PERSON><PERSON>A<PERSON>cationEntity,
        King<PERSON><PERSON>A<PERSON>cationDto,
        KingDeeA<PERSON>cationCommand,
        KingDeeAllocationRepository,
        KingDeeAllocationConvertor,
        KingDeeAllocationCommandConvertor> implements KingDeeAllocationApiService {
}
