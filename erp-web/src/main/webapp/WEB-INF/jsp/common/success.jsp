<!DOCTYPE html>
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>    
<html lang="en">
<head>
    <meta charset="UTF-8">
    <!-- <title>操作成功</title> -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/content.css" />
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/general.css?rnd=${resourceVersionKey}" />
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/manage.css" />
    <c:if test="${not empty url }">
    <meta http-equiv="refresh" content ="1;url=${url}">
    </c:if>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/jquery.min.js"></script>
    <script type="text/javascript" charset="UTF-8" src="${pageContext.request.contextPath}/static/js/form.js?rnd=${resourceVersionKey}"></script>
	<script type="text/javascript">
		var flag = false;
		//是否关闭自己--是否刷新自己--是否刷新父级页面
		var closeSelf = false,freshSelf = false,freshFrontPage = false;
		$(document).ready(function(){
			var refresh = ('${refresh}');//控制器传入参数
            var reloadParent = '${reloadParent}';//是否刷新父页面
            var url = $("#url").val();
            if(url != undefined && url.startsWith("/trader/customer/toEditContactPage")){
                var parentUrl = window.parent.location.href;
                debugger;
                // 判断父页面的地址是否包含指定的字符串
                if (parentUrl.includes('/ezadmin/list/list-traderContractList')) {
                    // 触发父页面中 id 为 submitBtn 的按钮的点击事件
                    window.parent.document.getElementById('submitBtn').click();
                }
            }

            if(refresh != undefined && refresh != ""){
				var arr = refresh.split("_");
				if(arr.length==3){
					setTimeout(function(){
						flag = true;
						closeSelf = (arr[0]=="false"?false:true);
						freshSelf = (arr[1]=="false"?false:true);
						freshFrontPage = (arr[2]=="false"?false:true);
						pagesContrlpages(closeSelf,freshSelf,freshFrontPage);
					}, 500);
				}
			}

            if("Y"==reloadParent){
                setTimeout(function () {
                    parentWindow[0].location.reload(true);
                },3000);
            }

		});
		
		function jumpUrl(url){
			if(flag){
				pagesContrlpages(closeSelf,freshSelf,freshFrontPage);
			}
			self.location=url;
		}
	</script>
</head>

<body>
    <div class="operate">
        <div class="success">
        	操作成功！<br/>
            <c:if test="${reloadParent=='Y'}">
                3秒后自动刷新页面，如果浏览器没有自动刷新，请手工关闭此页面。
            </c:if>

        	<c:if test="${not empty url }">
        	<span class="jump">如果浏览器没有跳转，请点击<a href="javaScript:void();" onclick="jumpUrl('${url}');">跳转链接</a></span>
                <input type="hidden" name="url" id="url" value="${url}" />
        	</c:if>
        </div>
        <div class="success-img"><img src="${pageContext.request.contextPath}/static/images/success404.jpg"/></div>
    </div>
</body>

</html>
