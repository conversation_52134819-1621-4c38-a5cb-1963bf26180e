<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>拜访计划列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">

</head>
<body id="visitrecordlist" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show=""  firstcol="numbers">
<div id="appendHead">
    <style type="text/css">
        .edit-table-head{
            display: none;
        }
        button.ezopenbutton{
            display: none !important;
        }
        .layui-laydate-hint{
            display: none !important;
        }
    </style>
    <script type="text/javascript">
        $(document).ready(function() {
            var objVisitorId1 = $('#itemName-VISITOR_ID');
            // var xmel = $(objVisitorId)[0];
            // //var initdata=objVisitorId.attr("itemsJson");
            var initvalue1=objVisitorId1.attr("value");
            // 创建隐藏域
            var input = $('<input>').attr({
                type: 'hidden',
                id:'VISITOR_ID_HIDDEN',
                name: 'VISITOR_ID',
                value: initvalue1
            });

            // 将隐藏域添加到form中
            $('#searchForm').append(input);
        });
    </script>

</div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
            <!--        <li class="layui-this"><a href="/ezadmin/list/list-R8XBgDSwsUc">订单列表</a></li>-->
            <!--        <li><a href="/ezadmin/list/list-Jb8cQU_twaQ">售后列表</a></li>-->

        </ul>
    </div>
    <form class="layui-form" id="search">

        <div class=" layui-inline ">
            <label class="layui-form-label">客户名称</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="CUSTOMER_NAME" placeholder="请输入" style="" alias="A" jdbctype="" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <!--<div class=" layui-inline ">
            <label class="layui-form-label">归属销售</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select" name="USER_ID" placeholder="" style="" alias="R" jdbctype="INTEGER" data="${session.EZ_SESSION_MY_USER_MAP_KEY}" datatype="KVSQLCACHE" oper="IN"></object>
            </div>
        </div>-->
        <div class=" layui-inline ">
            <label class="layui-form-label">拜访人</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select-share" name="VISITOR_ID" placeholder="" style="" radio="true" alias="A" jdbctype="INTEGER" data="${session.EZ_SESSION_MY_USER_MAP_KEY}" datatype="KVSQLCACHE" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">计划拜访时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="PLAN_VISIT_DATE" placeholder="" style="" alias="A.PLAN_VISIT_DATE" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">实际拜访时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="CARD_TIME" placeholder="" style="" alias="A.CARD_TIME" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">客户类型</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="select" name="CUSTOMER_NATURE" alias ="A.CUSTOMER_NATURE" jdbctype="INTEGER" placeholder="全部"
                        data='[{"K":"465","V":"渠道商"},{"K":"466","V":"终端"}]'
                        datatype="json"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">同舟会员</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="TZ_CUSTOMER"   placeholder="全部"  jdbctype="BODY"
                        data='[{"K":"1","V":"是"},{"K":"2","V":"否"}]'
                        datatype="json"></select>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">同舟会员等级</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="TZ_CUSTOMER_LEVEL" alias = "C.TZ_CUSTOMER_LEVEL" jdbctype="INTEGER" placeholder="全部"
                        data='[{"K":"1","V":"金牌会员"},{"K":"2","V":"银牌会员"}]'
                        datatype="json"></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">贝登会员</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="VD_CUSTOMER"  placeholder="全部" jdbctype="BODY"
                        data='[{"K":"1","V":"是"},{"K":"2","V":"否"}]'
                        datatype="json"></select>
            </div>
        </div>



        <div class=" layui-inline ">
            <label class="layui-form-label">沟通备忘录</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " maxlength="20" type="text" name="COMMUCATE_CONTENT" placeholder="请输入关键词" style="" alias="A.COMMUCATE_CONTENT" jdbctype="" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">拜访结果</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="select" name="VISIT_SUCCESS" placeholder="" style="" alias="plan" jdbctype="BODY"
                                                    data='[{"K":"1","V":"未打卡"},{"K":"2","V":"仅打卡"},{"K":"3","V":"拜访事项缺失"},{"K":"4","V":"拜访成功"}]'
                                                    datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">商机编号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="BUSSINESS_CHANCE_NO" placeholder="请输入" style="" alias="A.BUSSINESS_CHANCE_NO" jdbctype="" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">商机状态</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="BC_STATUS" alias = "BC.STATUS" jdbctype="INTEGER" placeholder="全部"
                        data='[{"K":"0","V":"未处理"}, {"K":"1","V":"报价中"}, {"K":"2","V":"已报价"}, {"K":"3","V":"已订单"}, {"K":"4","V":"已关闭"},{"K":"6","V":"处理中"}, {"K":"7","V":"已成单"}]'
                        datatype="json"></select>
            </div>
        </div>
        <!--<div class=" layui-inline ">
            <label class="layui-form-label">注册地区</label>
            <div class="layui-input-inline">
                <object   class=" layui-input list-search-item " type="search-region" name="REGION"  multiple="false"  placeholder="" style="" alias = "TRADER.AREA_ID"   data="" datatype="" oper="in" ></object>
            </div>
        </div>-->




    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
        <!--<button item_name="修改拜访人" name="修改拜访人" type="table">修改拜访人</button>-->
        <!--<button item_name="批量创建拜访计划" name="batchCreate" type="table" >批量创建拜访计划</button>-->
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th item_name="CUSTOMER_NAME" name="CUSTOMER_NAME" body="td-linktext" url="${TRADER_URL}" opentype="PARENT" windowname="${CUSTOMER_NAME}" datatype="" data="" style="min-width:80px;width: 140px;max-width: 300px;
word-break: break-all;" head="18">客户名称</th>
            <th item_name="BELONG_USER_NAME" name="BELONG_USER_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 60px;" head="18" >归属销售</th>
            <th item_name="VISITOR_USER_NAME" name="VISITOR_USER_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 60px;" head="18" >拜访人</th>
            <th item_name="PLAN_VISIT_DATE" name="PLAN_VISIT_DATE" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 80px;" head="18">计划拜访时间</th>
            <th item_name="CARD_TIME" name=CARD_TIME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 40px;min-width: 60px;" head="18">实际拜访时间</th>
            <th item_name="VISIT_RESULT" name="VISIT_RESULT" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">拜访结果</th>
            <th item_name="BUSSINESS_CHANCE_NO" name="BUSSINESS_CHANCE_NO"body="td-linktext" url="/businessChance/details.do?id=${BUSSINESS_CHANCE_ID}"   opentype="PARENT" windowname="" datatype="" data="" style="" head="18">商机编号</th>
            <th item_name="STATUS_NAME" name="STATUS_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">商机状态</th>
            <th item_name="COMMUCATE_CONTENT" name="COMMUCATE_CONTENT" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:LEFT;width: 150px;min-width: 150px;max-width: 200px;" head="18" >沟通备忘录</th>
            <th type="rowbutton" id="rowbutton" style="min-width: 60px">
                <button   type="single" url="/visitrecord/profile/getVisitRecordDetail.do?visitId=${ID}"
                          windowname="查看" name="playrecordBtn"  opentype="PARENT">查看
                </button>
                <button type="single" opentype="CONFIRM_AJAX" style="display: none;" item_Style="display:none;"
                        url="/ezadmin/form/doDelete-deleteVisitRecord?ID=${ID}"
                        windowname="删除" name="MODEL" >删除
                </button>
                <!--<button item_name="b" name="viewBtn"
                        url="/visitrecord/profile/getVisitRecordDetail.do?visitId=${ID}"
                        opentype="PARENT" windowname="${CUSTOMER_NAME}" style  >查看
                </button>-->
            </th>

        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by A.PLAN_VISIT_DATE DESC,A.CARD_TIME DESC" groupby="GROUP BY A.ID">
    StringBuilder sql=new StringBuilder("

       SELECT
                case  when A.CUSTOMER_FROM =1
                    then CONCAT('/trader/customer/baseinfo.do?traderId=',A.TRADER_ID )
                when A.CUSTOMER_FROM =2
                    then CONCAT('/trader/customer/new/terminalPortrait.do?searchName=',A.CUSTOMER_NAME )
                ELSE ''  END AS TRADER_URL,
                 case  when A.TRADER_ID is not null
                    then 'color:'
                ELSE 'td-text'  END AS TDTYPE,
                A.ID,
                A.CUSTOMER_NAME as CUSTOMER_NAME,
                A.TRADER_ID,
  		A.VISITOR_NAME ,
  		A.PLAN_VISIT_DATE AS PLAN_VISIT_DATE,
  		A.CARD_TIME AS CARD_TIME ,
  		CASE WHEN A.CARD_OFF ='N' OR A.CARD_OFF IS NULL then '未打卡'
  		when A.CARD_OFF = 'Y' and A.VISIT_SUCCESS = 'N' AND A.CONTACT_NAME IS NULL then '仅打卡'
  		when A.CARD_OFF = 'Y'  AND A.VISIT_SUCCESS = 'N'  AND A.CONTACT_NAME IS NOT NULL then '拜访事项缺失'
  		when A.VISIT_SUCCESS = 'Y' then '拜访成功'
  		else ''
  		END AS VISIT_RESULT,
  		BC.BUSSINESS_CHANCE_NO AS BUSSINESS_CHANCE_NO,
        BC.BUSSINESS_CHANCE_ID AS BUSSINESS_CHANCE_ID,
        C.TZ_CUSTOMER_LEVEL AS  customerLevel,BC.STATUS,
        A.COMMUCATE_CONTENT AS COMMUCATE_CONTENT,
    	CASE BC.STATUS
	        WHEN 0 THEN '未处理'
	        WHEN 1 THEN '报价中'
	        WHEN 2 THEN '已报价'
	        WHEN 3 THEN '已订单'
	        WHEN 4 THEN '已关闭'
	        WHEN 5 THEN '未分配'
	        WHEN 6 THEN '处理中'
	        WHEN 7 THEN '已成单'
	        ELSE '' END AS STATUS_NAME,
        case
                when CUSER.USERNAME is null then '-'
                else CUSER.USERNAME
                end  AS BELONG_USER_NAME,
        U.USERNAME  AS VISITOR_USER_NAME
        FROM T_VISIT_RECORD A
    LEFT JOIN T_USER U ON A.VISITOR_ID = U.USER_ID
    LEFT JOIN T_TRADER_CUSTOMER B ON A.TRADER_CUSTOMER_ID = B.TRADER_CUSTOMER_ID AND A.TRADER_ID=B.TRADER_ID
    LEFT JOIN T_TRADER TRADER ON B.TRADER_ID=TRADER.TRADER_ID
    LEFT JOIN T_R_TRADER_J_USER R ON A.TRADER_ID = R.TRADER_ID AND R.TRADER_TYPE = 1
    LEFT JOIN T_USER CUSER ON R.USER_ID  = CUSER.USER_ID
    LEFT JOIN T_BUSSINESS_CHANCE BC ON A.BUSSINESS_CHANCE_NO = BC.BUSSINESS_CHANCE_NO AND BC.MERGE_STATUS IN (0,2)
    LEFT JOIN DWH_TRADER_LIST_FILTER_ERP C ON A.TRADER_CUSTOMER_ID = C.TRADER_CUSTOMER_ID AND A.TRADER_ID=C.TRADER_ID

    WHERE A.IS_DELETE = 0");
if ( isNotBlank("EZ_SESSION_MY_USER_KEY","session")) {
    sql.append ( " AND (A.ADD_USER_ID in (" );
    sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
    sql.append (  ") " );
    sql.append ( " or A.VISITOR_ID in (" );
    sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
    sql.append (  ") OR A.ID IN (" );
    sql.append("SELECT A1.ID FROM T_VISIT_RECORD A1  LEFT JOIN T_R_TRADER_J_USER R1 ON A1.TRADER_ID = R1.TRADER_ID AND R1.TRADER_TYPE = 1  LEFT JOIN T_R_SALES_J_TRADER  SHARE1  ON A1.TRADER_CUSTOMER_ID = SHARE1.TRADER_CUSTOMER_ID AND A1.TRADER_ID=SHARE1.TRADER_ID       WHERE  SHARE1.SALE_USER_ID IN (");
    sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
    sql.append( ") OR R1.USER_ID IN (");
    sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
    sql.append (  "))) " );
}
visitSuccess = $("VISIT_SUCCESS");
if ( isNotBlank("VISIT_SUCCESS","request")&amp;&amp; visitSuccess.toString().equals("1")) {
     sql.append (" AND (A.CARD_OFF ='N' OR A.CARD_OFF IS NULL)");
}
if ( isNotBlank("VISIT_SUCCESS","request") &amp;&amp; visitSuccess.toString().equals("2")) {
    sql.append ("AND A.CARD_OFF = 'Y' and A.VISIT_SUCCESS = 'N' AND A.CONTACT_NAME IS NULL");
}
if ( isNotBlank("VISIT_SUCCESS","request") &amp;&amp; visitSuccess.toString().equals("3")) {
    sql.append ("AND A.CARD_OFF = 'Y'  AND A.VISIT_SUCCESS = 'N'  AND A.CONTACT_NAME IS NOT NULL");
}
if ( isNotBlank("VISIT_SUCCESS","request") &amp;&amp; visitSuccess.toString().equals("4")) {
    sql.append ("AND A.VISIT_SUCCESS = 'Y'");
}
vdCustomer = $("VD_CUSTOMER");
if ( isNotBlank("VD_CUSTOMER","request")&amp;&amp; vdCustomer.toString().equals("1")) {
     sql.append (" AND A.TRADER_ID IN (select distinct TRADER_ID FROM T_WEB_ACCOUNT where TRADER_ID is not null)");
}
if ( isNotBlank("VD_CUSTOMER","request")&amp;&amp; vdCustomer.toString().equals("2")) {
     sql.append (" AND A.TRADER_ID NOT IN (select distinct TRADER_ID FROM T_WEB_ACCOUNT where TRADER_ID is not null)");
}
tzCustomer = $("TZ_CUSTOMER");
if ( isNotBlank("TZ_CUSTOMER","request")&amp;&amp; tzCustomer.toString().equals("1")) {
     sql.append (" AND C.TZ_CUSTOMER IS NOT NULL");
}
if ( isNotBlank("TZ_CUSTOMER","request")&amp;&amp; tzCustomer.toString().equals("2")) {
     sql.append (" AND C.TZ_CUSTOMER IS NULL");
}







list=search(sql);


return list;</pre> count:
                <pre id="count" class="layui-code">

                </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">

    <script>
        function getCheckIdsUrl() {
            var goodsIdArr="-1";
            $("input[name='list-body-checkbox']:not(:disabled)").each(function () {
                if (this.checked) {
                    goodsIdArr+=','+$(this).attr("_CHECK_ID_VALUE");
                }
            })
            if(goodsIdArr=="-1"){
                return "";
            }
            return "ID=0&_CHECKD_IDS=" + encodeURI(goodsIdArr)+"&USERLIST=";
        }
        $(function(){


            var visitorList = eval($("[name='VISITOR_ID']").attr("itemsjson"));
            //console.log($("[name='VISITOR_ID']").attr("value").replace(/\[+|\]+/g, ''));
            $("button[name='playrecordBtn']").attr('style', 'display: inline-block !important;margin-bottom:0px');
            if(visitorList.length>1){//大于1个人的时候
                // $("button[item_open_title='批量创建拜访计划']").attr('style', 'display: inline-block !important;margin-bottom:0px');
                $("button[name='MODEL']").attr('style', 'display: inline-block !important;margin-bottom:0px');
                // $("button[item_open_title='批量创建拜访计划']").click(function(){
                //     // if(getCheckIdsUrl()==""){
                //     //     layer.alert("请选择需要修改拜访人的拜访记录");
                //     //     return false;
                //     // }
                //     // ///$(this).removeClass("ezopenbutton");
                //     // var s = getCheckIdsUrl();
                //     // console.log(s);
                //     openModel("/distributionLink/distribution/link.do?create=Y","批量创建拜访计划");
                //     return false;
                // })
            }else{
                // $("button[name='修改拜访人']").hide();
            }

            $("input[name='list-body-checkbox']").each(function () {
                if ($(this).parent().find('input[name="row_data_hidden_VISIT_RESULT"]').val() !='未打卡') {
                    $(this).attr("disabled","disabled");
                    $(this).parent().parent().find('button[item_id="MODEL"]').hide();
                }
            });

            //拜访人需要单独加逻辑

            $.ajax({
                url: '/visitrecord/profile/queryShardUserListByBelongUser.do',
                type:"GET",
                dataType : "json",
                success:function(res){
                    if(res.code==0){
                        var userList = res.data;
                        userList.forEach(function(obj2){
                            var isDuplicate = visitorList.some(function(obj1) {
                                return obj1.K === obj2.K;
                            });
                            if (!isDuplicate) {
                                visitorList.push(obj2);
                            }
                        });
                        var objVisitorId = $('#itemName-VISITOR_ID');
                        // var xmel = $(objVisitorId)[0];
                        // //var initdata=objVisitorId.attr("itemsJson");
                        var initvalue=objVisitorId.attr("value").replace(/\[+|\]+/g, '');
                        initvalue = "["+initvalue+"]";
                        var itemName=objVisitorId.attr("name");
                        var itemPlaceholder=objVisitorId.attr("itemPlaceholder");
                        // var divElement = document.getElementById('itemName-VISITOR_ID');
                        // var initvalue = divElement.getAttribute('value');
                        // var itemName = divElement.getAttribute('name');
                        // var itemPlaceholder = divElement.getAttribute('itemPlaceholder');
                        console.log(2);
                        $("#VISITOR_ID_HIDDEN").remove();
                        var demo1 = xmSelect.render({
                            el: objVisitorId[0],
                            language: 'zn',
                            filterable: true,
                            filterMethod: function (val, item, index, prop) {//重写搜索方法。
                                if(val == item.K){//把value相同的搜索出来
                                    return true;
                                }
                                if(item.V.indexOf(val) != -1){//名称中包含的搜索出来
                                    return true;
                                }
                                return !ezpingyin(val, item.V, item.K);
                            },
                            style: {
                                height: '26px'  ,
                            },
                            prop: {
                                name: 'V',
                                value: 'K',
                            },
                            name: itemName,
                            tips: itemPlaceholder,
                            data:  visitorList,
                            initValue:  JSON.parse(initvalue)
                        });
                    }

                }
            });
          })


    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>

    layui.use(function () {

    })
</script>
</body>
</html>