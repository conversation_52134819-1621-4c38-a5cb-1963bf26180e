package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayBillEntity;
import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillEntryDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillSrcEntryDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * dto转entity
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface KingDeePayBillConvertor extends BaseMapStruct<KingDeePayBillEntity, KingDeePayBillDto> {

    /**
     * DTO转Entity
     *
     * @param dto
     * @return
     */
    @Mapping(target = "FPayBillEntry", source = "FPayBillEntry", qualifiedByName = "entryListToJsonArray")
    @Mapping(target = "FPayBillSrcEntry", source = "FPayBillSrcEntry", qualifiedByName = "srcEntryListToJsonArray")
    @Override
    KingDeePayBillEntity toEntity(KingDeePayBillDto dto);

    /**
     * Entity转DTO
     *
     * @param entity
     * @return
     */
    @Mapping(target = "FPayBillEntry", source = "FPayBillEntry", qualifiedByName = "entryJsonArrayToList")
    @Mapping(target = "FPayBillSrcEntry", source = "FPayBillSrcEntry", qualifiedByName = "srcEntryJsonArrayToList")
    @Override
    KingDeePayBillDto toDto(KingDeePayBillEntity entity);


    /**
     * entity 中JSONArray 转 原对象
     *
     * @param payBillEntryDtos JSONArray
     * @return List<KingDeePayBillDto> dto中的对象
     */
    @Named("entryJsonArrayToList")
    default List<KingDeePayBillEntryDto> entryJsonArrayToList(JSONArray payBillEntryDtos) {
        if (CollUtil.isEmpty(payBillEntryDtos)) {
            return Collections.emptyList();
        }
        return payBillEntryDtos.toJavaList(KingDeePayBillEntryDto.class);
    }

    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("entryListToJsonArray")
    default JSONArray entryListToJsonArray(List<KingDeePayBillEntryDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }


    /**
     * entity 中JSONArray 转 原对象
     *
     * @param payBillEntryDtos JSONArray
     * @return List<KingDeePayBillDto> dto中的对象
     */
    @Named("srcEntryJsonArrayToList")
    default List<KingDeePayBillSrcEntryDto> srcEntryJsonArrayToList(JSONArray payBillEntryDtos) {
        if (CollUtil.isEmpty(payBillEntryDtos)) {
            return Collections.emptyList();
        }
        return payBillEntryDtos.toJavaList(KingDeePayBillSrcEntryDto.class);
    }


    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("srcEntryListToJsonArray")
    default JSONArray srcEntryListToJsonArray(List<KingDeePayBillSrcEntryDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
}
