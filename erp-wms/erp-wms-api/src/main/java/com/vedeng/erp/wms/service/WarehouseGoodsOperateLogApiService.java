package com.vedeng.erp.wms.service;

import com.vedeng.erp.wms.dto.AfterSaleBuyorderDirectOutLogDto;
import com.vedeng.erp.wms.dto.WarehouseGoodsOperateLogDto;

import java.util.List;

public interface WarehouseGoodsOperateLogApiService {

    /**
     * 出库单查询
     * @param warehouseGoodsOperateLogDto
     * @return
     */
    List<WarehouseGoodsOperateLogDto> findWarehouseByAll(WarehouseGoodsOperateLogDto warehouseGoodsOperateLogDto);
    
    /**
     * 采购售后直发出库单查询
     * @param afterSaleBuyorderDirectOutLogDto
     * @return
     */
    List<AfterSaleBuyorderDirectOutLogDto> findBuyOrderAfterSaleDirectByAll(AfterSaleBuyorderDirectOutLogDto afterSaleBuyorderDirectOutLogDto);
}
