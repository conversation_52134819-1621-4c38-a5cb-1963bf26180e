<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ include file="../../common/common.jsp" %>
<c:set var="title" value="抓取发票" scope="application"/>

<div class="formpublic">
    <form method="post" id="editOrderRatioEdit">
        <ul>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>发票类型</lable>
                </div>

                <div class="f_left">
                    <div class="form-blanks">
                        <input type="radio" name="invoiceType" checked value="1">
                        <label>进项发票</label>
                        &nbsp; &nbsp; &nbsp;
                        <input type="radio" name="invoiceType"  value="0">
                        <label>销项发票</label>
                    </div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>发票时间</lable>
                </div>

                <div class="f_left">
                    <div class="form-blanks">
                        <input class="Wdate f_left input-smaller96 mr5" type="text" placeholder="请选择日期" autocomplete="off"
                               onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'2020-11-01',maxDate:'#F{$dp.$D(\'endDateStr\')}'})"
                               name="startDateStr" id="startDateStr">
                        <div class="gang">-</div>
                        <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off"
                               onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startDateStr\')}'})"
                               name="endDateStr" id="endDateStr">
                        &nbsp;&nbsp;
                        <label style="color: red;font-weight: bold">提示: 时间区间切勿选择过大！</label>
                    </div>
                </div>
            </li>
        </ul>
        <br>
        <div class="add-tijiao tcenter">
            <button type="button" class="bg-light-blue"
                    onclick="doCaptureInvoice()">确定
            </button>
            <button class="dele" type="button" onclick="closeMyLayer()">取消</button>
        </div>
    </form>
</div>
<script>
    $(function () {
        //实例日期
        var now = new Date();
        var nowMonth = String((now.getMonth()+1));
        var nowDay = String(now.getDate());
        if(nowMonth.length==1) nowMonth = "0"+nowMonth;
        if(nowDay.length==1) nowDay = "0"+nowDay;
        var nowDate =  now.getFullYear()+"-"+nowMonth+"-"+nowDay;
        //初始加载
        $('#startDateStr').val(nowDate);
        $('#endDateStr').val(nowDate);
    })

    /**
     * 拉取发票操作
     */
    function doCaptureInvoice() {
        var invoiceType = $("input[name='invoiceType']:checked").val();
        if (invoiceType == null || invoiceType == '' || invoiceType == undefined){
            layer.alert('发票类型不能为空！');
            return;
        }
        var startDateStr = $('#startDateStr').val();
        if (startDateStr == null || startDateStr == '' || startDateStr == undefined) {
            layer.alert('开始时间不能为空!');
            return;
        }
        var endDateStr = $('#endDateStr').val();
        if (endDateStr == null || endDateStr == '' || endDateStr == undefined) {
            layer.alert('结束时间不能为空!');
            return;
        }
        $.ajax({
            url: '/finance/invoice/doCaptureInvoice.do',
            data: {
                invoiceType: invoiceType,
                startDateStr: startDateStr,
                endDateStr: endDateStr
            },
            type: 'post',
            dataType: 'json',
            success: function (res) {
                if (res.code == 0) {
                    layer.alert('后台开始抓取发票成功，请稍后刷新列表页！', function () {
                        window.parent.location.reload();
                    });
                } else {
                    layer.alert(res.message, function () {
                        window.parent.location.reload();
                    });
                }
            }
        })
    }
    function closeMyLayer() {
        layer.closeAll();
        parent.layer.closeAll();
    }
</script>
<%@ include file="../../common/footer.jsp" %>