package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.model.entities.AfterSaleServiceStandardLongdistanceFeePo;
import com.vedeng.aftersales.model.vo.AfterSaleServiceStandardLongdistanceFeeVo;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;
import java.util.Map;

/**
 * @describe 售后服务标准长途费Mapper
 * <AUTHOR>
 * @date 2020/12/11
 */
@Named("afterSalesLongDistanceFeeMapper")
public interface AfterSalesLongDistanceFeeMapper {
    /**
     * 添加长途费标准
     *
     * @param longdistanceFeePo
     * @return
     */
    int insertLongDistanceFee (AfterSaleServiceStandardLongdistanceFeePo longdistanceFeePo);

    /**
     * 检索售后标准长途费
     *
     * @return
     */
    List<AfterSaleServiceStandardLongdistanceFeeVo> getLongdisranceFeeVoListPage(Map<String, Object> map);

    /**
     * 作废所有售后标准服务费记录
     *
     * @param userId
     * @return
     */
    int disableAllLongDistanceFee(Integer userId);
    /**
     * <b>Description:</b><br>
     * 根据省份id查询下面所有的长途费的区间
     *
     * @param provinceId
     * @return java.lang.String
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/7/6 9:47
     */
    String selectCarriageByProvince(@Param("provinceId") Integer provinceId);
    /**
     * <b>Description:</b><br>
     * 根据城市id查询下面所有的长途费的区间
     *
     * @param city
     * @return java.lang.String
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/7/6 9:47
     */
    String selectCarriageByCity(@Param("city") Integer city);/**
     * <b>Description:</b><br>
     * 根据县id查询长途费
     *
     * @param regionId
     * @return java.lang.String
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/7/6 9:47
     */
    String selectCarriageByRegionId(@Param("regionId") Integer regionId);
}