package com.vedeng.infrastructure.bank.api.domain.base;

import cn.hutool.core.date.DateUtil;
import com.ctrip.framework.apollo.ConfigService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author: jez
 * @CreateTime: 2024-07-30
 * @Description: 银行请求头
 * @Version: 1.0
 */
@Data
@XmlRootElement(name = "RequestHeader")
@XmlAccessorType(XmlAccessType.FIELD)
public class RequestHeader {

    @XmlElement(name = "dtClient")
    private String dtClient;

    @XmlElement(name = "clientId")
    private String clientId;
    @XmlElement(name = "userId")
    private String userId;
    @XmlElement(name = "userPswd")
    private String userPswd;

    @XmlElement(name = "language")
    private String language = "utf-8";
    @XmlElement(name = "appId")
    private String appId = "nsbdes";
    @XmlElement(name = "appVer")
    private String appVer = "201";


    public RequestHeader() {
        this.dtClient = DateUtil.formatDateTime(DateUtil.date());
        this.clientId = ConfigService.getAppConfig().getProperty("bank.api.clientId", "**********");
        this.userId = ConfigService.getAppConfig().getProperty("bank.api.userId", "*************");
        this.userPswd = ConfigService.getAppConfig().getProperty("bank.api.userPswd", "1qqc49");
    }

}
