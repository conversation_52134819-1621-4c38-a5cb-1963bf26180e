package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description ${end}
 * @date 2022/12/3 9:56
 **/
public interface BatchInvoiceDetailDtoMapper {

    /**
     * 根据明细id集合查询
     *
     * @param ids
     * @return
     */
    List<BatchInvoiceDetailDto> selectByDetailIds(@Param("ids") List<Integer> ids);

    /**
     * 根据invoiceId 查询
     *
     * @param invoiceId
     * @return
     */
    List<BatchInvoiceDetailDto> findByInvoiceId(@Param("invoiceId") Integer invoiceId);

    /**
     * 票明细
     *
     * @param invoiceId
     * @return
     */
    List<BatchInvoiceDetailDto> findByInvoiceIdAndBuyorderGoodsId(@Param("invoiceId") Integer invoiceId);

    /**
     * 根据销售订单详情Id查询金蝶费用项目编码
     *
     * @param invoiceId
     * @return
     */
    String findCostCategoryKingDeeIdBySaleOrderGoodsId(Integer invoiceId);

    /**
     * 根据invoiceId 查询
     *
     * @param invoiceIds
     * @return
     */
    List<BatchInvoiceDetailDto> querySaleInvoiceByInvoiceIds(@Param("invoiceIds") List<Integer> invoiceIds);

    /**
     * 根据invoiceId 查询
     *
     * @param afterSalesId
     */
    List<Integer> findByAfterSalesId(@Param("afterSalesId") Integer afterSalesId);


    /**
     * 所有有效的发票详情内容
     *
     * @param invoiceIds
     * @return
     */
    List<BatchInvoiceDetailDto> getAllValidInvoiceDetailByInvoiceIds(@Param("invoiceIds") List<Integer> invoiceIds);

    /**
     * 查询销售单商品的 发票明细信息(包含了费用票和实物票)
     *
     * @param invoiceId 发票号
     * @return List<BatchInvoiceDetailDto>
     */
    List<BatchInvoiceDetailDto> getSaleOrderInvoiceDetailList(@Param("invoiceId") Integer invoiceId);

    /**
     * 批量查询发票明细集合
     *
     * @param invoiceIds invoiceIds
     * @return List<BatchInvoiceDetailDto>
     */
    List<BatchInvoiceDetailDto> getInvoiceDetailByInvoiceIdList(@Param("invoiceIds") List<Integer> invoiceIds);

    BigDecimal getRedInvoiceTotalNumByCondition(@Param("afterSalesId") Integer afterSalesId, @Param("detailGoodsId") Integer detailGoodsId);


    ///**
    // * 根据invoiceId 查询
    // *
    // * @param invoiceIds
    // * @return
    // */
    //List<BatchInvoiceDetailDto> querySaleInvoiceByInvoiceIds(@Param("invoiceIds") List<Integer> invoiceIds);


    List<Integer> findByAfterSalesIdGetInvoiceId(@Param("afterSalesId") Integer afterSalesId);

}