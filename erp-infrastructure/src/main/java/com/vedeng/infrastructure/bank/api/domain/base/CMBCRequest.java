package com.vedeng.infrastructure.bank.api.domain.base;

import lombok.Data;

import javax.xml.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: cmbc
 * @date 2024/10/11 14:06
 */
@Data
@XmlRootElement(name = "CMBC")
@XmlAccessorType(XmlAccessType.FIELD)
public abstract class CMBCRequest<T> {

    @XmlAttribute(name = "header")
    private String header = "100";

    @XmlAttribute(name = "version")
    private String version = "100";

    @XmlAttribute(name = "security")
    private String security = "none";

    @XmlAttribute(name = "lang")
    private String lang = "utf-8";

    @XmlAttribute(name = "trnCode")
    private String trnCode;

    @XmlElement(name = "requestHeader")
    private RequestHeader requestHeader = new RequestHeader();

    @XmlElement(name = "xDataBody")
    private T xDataBody;


    /**
     * 初始化 apiCode
     */
    protected abstract void initTrnCode();

}
