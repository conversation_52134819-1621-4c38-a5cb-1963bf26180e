package com.vedeng.goods.manager.validator.InvalidatorChain.spu.Invalid;

import com.google.common.base.CharMatcher;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.*;

/**
 * (4)	所属的所有SKU（没有待确认、进行中的VB，VP采购单，销售售后退货单，且单据中该SKU处于未收货，或部分收货状态）
 *
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/1 17:32.
 * @author: Randy.Xu.
 */
@Service
public class SpuNoRelatedOrderForConditionFourInvalidator extends GoodsInvalidatorChain {

    @Autowired
    private BuyorderMapper buyorderMapper;

    @Autowired
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Override
    public void doInvalid() {
        String spuNo = getSpuNo();
        List<String> skuNoList = getSkuNoList();
        if(CollectionUtils.isEmpty(skuNoList)){
            return ;
        }
        StringBuffer skus = new StringBuffer();
        StringBuffer orders = new StringBuffer();
        for (String skuNo : skuNoList) {
            List<Integer> goodsIds = coreSkuMapper.getGoodsIdBySkuNo(skuNo);
            List<Buyorder> relatedBuyorder = buyorderMapper.getRelateBuyorderInProgress(skuNo);
            List<AfterSales> relatedAftersales = afterSalesMapper.getRelateAftersalesConditionFour(goodsIds);
            if (CollectionUtils.isNotEmpty(relatedBuyorder) || CollectionUtils.isNotEmpty(relatedAftersales)) {
                skus.append(skuNo);
                orders.append(",");
                if (CollectionUtils.isNotEmpty(relatedBuyorder)) {
                    String collect = relatedBuyorder.stream().map(e -> e.getBuyorderNo()).collect(Collectors.joining(","));
                    orders.append(collect);
                    orders.append(",");
                }
                if (CollectionUtils.isNotEmpty(relatedAftersales)) {
                    String collect = relatedAftersales.stream().map(e -> e.getAfterSalesNo()).collect(Collectors.joining(","));
                    orders.append(collect);
                    orders.append(",");
                }
            }
        }
        if (skus.length() > 0 && orders.length() > 0) {
            String tempSkusList = SpuNoRelatedOrderForConditionFourInvalidatorError.getMessage().replace("skus", skus.substring(0,skus.length()-1));
            String skusList = tempSkusList.replace("orderNo", orders.substring(0,orders.length()-1));
            throw new InvalidatorChainException(SpuNoRelatedOrderForConditionFourInvalidatorError.getCode(), skusList);
        }
    }
}
