package com.vedeng.mobile.config.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.support.ErrorPageFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: web配置
 * @date 2022/1/7 17:40
 */
@Configuration
@Slf4j
public class WebConfig {

    @Bean
    public FilterRegistrationBean testFilterRegistration() {
        log.info("ErpMobileFilter生成");
        FilterRegistrationBean registration = new FilterRegistrationBean(erpMobileFilter());
        registration.addUrlPatterns("/*");
        registration.setName("erpMobileFilter");
        return registration;
    }

    @Bean
    public ErpMobileFilter erpMobileFilter() {
        return new ErpMobileFilter();
    }
    //
    //
    //@Bean
    //public ErrorPageFilter errorPageFilter() {
    //    return new ErrorPageFilter();
    //}
    //
    //@Bean
    //public FilterRegistrationBean disableSpringBootErrorFilter(ErrorPageFilter filter) {
    //    FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean();
    //    filterRegistrationBean.setFilter(filter);
    //    filterRegistrationBean.setEnabled(false);
    //    return filterRegistrationBean;
    //}
}