package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeSaleSettlementAdjustmentEntity;


public interface KingDeeSaleSettlementAdjustmentMapper {
    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insert(KingDeeSaleSettlementAdjustmentEntity row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int insertSelective(KingDeeSaleSettlementAdjustmentEntity row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    KingDeeSaleSettlementAdjustmentEntity selectByPrimaryKey(Integer id);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKeySelective(KingDeeSaleSettlementAdjustmentEntity row);

    /**
     * @mbg.generated generated automatically, do not modify!
     */
    int updateByPrimaryKey(KingDeeSaleSettlementAdjustmentEntity row);
}