
<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>搜索-级联下拉</title>
</head>
<body id="search-region"  type="search"  >
<style>.el-input__inner{height:26px ;line-height:26px}</style>
<div  th:style="${'z-index:101;'+custom_style}" th:ITEM_NAME="${itemName}"  th:ITEM_ID="${itemName}"
      class=" "  >
    <label  class="layui-form-label" th:for="${'itemName-' + itemName}"
            th:text="${itemLabel}"   th:title="${itemLabel}"></label>
    <div  th:class="${#strings.isEmpty(itemParamValue)?'layui-input-inline':'layui-input-inline layui-border-blue' } "   >
        <input  th:value="${#strings.isEmpty(itemParamValue)?'':itemParamValue}" th:name="${itemName}" th:id="${'search-itemName-' + itemName}"
                class="ez-laycascader"
                ez_url="/ezadmin/api/region.html"
                ez_value="regionId" ez_label="regionName"
                th:multi="${multi}"
                ez_children="child"
                th:collapseTags="${collapsetags}"
                th:showAllLevels="${showalllevels}"
                th:placeholder="${placeholder}"
                autocomplete="off"
        >
    </div>
    <div class="layui-form-mid" style="padding:0 !important;line-height: 30px;margin-right:0;display:none">
        <i class="tableSearch layui-icon layui-icon-ok-circle" th:tableSearch_item_name="${item_name}"  style="color:#1E9FFF;cursor: pointer"></i></div>
</div>

</body>
</html>