<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.SaleorderCouponMapper" >

  <resultMap id="BaseResultMap" type="com.vedeng.order.model.SaleorderCoupon" >
    <id column="SALEORDER_COUPON_ID" property="saleorderCouponId" jdbcType="INTEGER" />
    <result column="SALEORDER_ID" property="saleorderId" jdbcType="INTEGER" />
    <result column="COUPON_ID" property="couponId" jdbcType="INTEGER" />
    <result column="COUPON_CODE" property="couponCode" jdbcType="VARCHAR" />
    <result column="COUPON_TYPE" property="couponType" jdbcType="INTEGER" />
    <result column="DENOMINATION" property="denomination" jdbcType="DECIMAL" />
    <result column="USE_THRESHOLD" property="useThreshold" jdbcType="DECIMAL" />
    <result column="LIMIT_TYPESTR" property="limitTypeStr" jdbcType="VARCHAR" />
    <result column="EFFEVTIVE_START_TIME" property="effevtiveStartTime" jdbcType="VARCHAR" />
    <result column="EFFEVTIVE_END_TIME" property="effevtiveEndTime" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    SALEORDER_COUPON_ID, SALEORDER_ID, COUPON_ID, COUPON_CODE, COUPON_TYPE, DENOMINATION,
    USE_THRESHOLD, LIMIT_TYPESTR, EFFEVTIVE_START_TIME, EFFEVTIVE_END_TIME
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from T_SALEORDER_COUPON
    where SALEORDER_COUPON_ID = #{saleorderCouponId,jdbcType=INTEGER}
  </select>

  <select id="selectBySaleOrderId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from T_SALEORDER_COUPON
    where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
  </select>

  <select id="selectByCouponCode" resultType="com.vedeng.order.model.Saleorder" parameterType="java.lang.Long" >
    SELECT t.SALEORDER_ID,t.SALEORDER_NO
    FROM T_SALEORDER t
    LEFT JOIN T_SALEORDER_COUPON g ON t.SALEORDER_ID = g.SALEORDER_ID
    where g.COUPON_CODE = #{couponCode,jdbcType=BIGINT}
    limit 1
  </select>

  <delete id="deleteBySaleOrderId" parameterType="java.lang.Integer">
    delete from T_SALEORDER_COUPON
    where SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
  </delete>

  <insert id="insertSelective" parameterType="com.vedeng.order.model.SaleorderCoupon" useGeneratedKeys="true" keyProperty="saleorderCouponId">
    insert into T_SALEORDER_COUPON
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="saleorderId != null" >
        SALEORDER_ID,
      </if>
      <if test="couponId != null" >
        COUPON_ID,
      </if>
      <if test="couponCode != null" >
        COUPON_CODE,
      </if>
      <if test="couponType != null" >
        COUPON_TYPE,
      </if>
      <if test="denomination != null" >
        DENOMINATION,
      </if>
      <if test="useThreshold != null" >
        USE_THRESHOLD,
      </if>
      <if test="limitTypeStr != null" >
        LIMIT_TYPESTR,
      </if>
      <if test="effevtiveStartTime != null" >
        EFFEVTIVE_START_TIME,
      </if>
      <if test="effevtiveEndTime != null" >
        EFFEVTIVE_END_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="saleorderId != null" >
        #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="couponId != null" >
        #{couponId,jdbcType=INTEGER},
      </if>
      <if test="couponCode != null" >
        #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponType != null" >
        #{couponType,jdbcType=INTEGER},
      </if>
      <if test="denomination != null" >
        #{denomination,jdbcType=DECIMAL},
      </if>
      <if test="useThreshold != null" >
        #{useThreshold,jdbcType=DECIMAL},
      </if>
      <if test="limitTypeStr != null" >
        #{limitTypeStr,jdbcType=VARCHAR},
      </if>
      <if test="effevtiveStartTime != null" >
        #{effevtiveStartTime,jdbcType=VARCHAR},
      </if>
      <if test="effevtiveEndTime != null" >
        #{effevtiveEndTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.SaleorderCoupon" >
    update T_SALEORDER_COUPON
    <set >
      <if test="saleorderId != null" >
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="couponId != null" >
        COUPON_ID = #{couponId,jdbcType=INTEGER},
      </if>
      <if test="couponCode != null" >
        COUPON_CODE = #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponType != null" >
        COUPON_TYPE = #{couponType,jdbcType=INTEGER},
      </if>
      <if test="denomination != null" >
        DENOMINATION = #{denomination,jdbcType=DECIMAL},
      </if>
      <if test="useThreshold != null" >
        USE_THRESHOLD = #{useThreshold,jdbcType=DECIMAL},
      </if>
      <if test="limitTypeStr != null" >
        LIMIT_TYPESTR = #{limitTypeStr,jdbcType=VARCHAR},
      </if>
      <if test="effevtiveStartTime != null" >
        EFFEVTIVE_START_TIME = #{effevtiveStartTime,jdbcType=VARCHAR},
      </if>
      <if test="effevtiveEndTime != null" >
        EFFEVTIVE_END_TIME = #{effevtiveEndTime,jdbcType=VARCHAR},
      </if>
    </set>
    where SALEORDER_COUPON_ID = #{saleorderCouponId,jdbcType=INTEGER}
  </update>

  <select id="selectBySaleorderId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_SALEORDER_COUPON
    WHERE
    SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
    LIMIT 1
  </select>


</mapper>
