package com.vedeng.erp.buyorderexpense.manager.esign;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.base.api.dto.reqParam.FileInfo;
import com.vedeng.base.api.dto.reqParam.SignCallbackDto;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseDetailEntity;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseEntity;
import com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseDetailMapper;
import com.vedeng.erp.buyorderexpense.mapper.BuyorderExpenseMapper;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.handle.AbstractElectronicSignHandle;
import com.vedeng.system.model.vo.UrlToPdfParam;
import com.vedeng.system.service.OssUtilsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @description 采购费用单e签宝实现类
 * @date 2022/8/26 9:26
 */
@Component
@Slf4j
public class BuyOrderExpenseElectronicSignHandle extends AbstractElectronicSignHandle {


    @Autowired
    private BuyorderExpenseMapper buyorderExpenseMapper;

    @Autowired
    private BuyorderExpenseDetailMapper buyorderExpenseDetailMapper;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Value("${erp.domain}")
    private String erpDomain;

    @Value("${html2Pdf.domain}")
    private String html2PdfDomain;

    @Value("${ht_remove_blank_page}")
    private Boolean isRemoveBlankPage;


    private static final String PRINT_CONTRACT_URL = "/buyorderExpense/printContract.do?buyorderExpenseId=";

    private static final String RENDER_URL = "/api/render";

    private static final String FILENAME_PREFIX = "采购费用单合同";

    private static final int FLOW_TYPE = 2;

    @Override
    protected void electronicSignFailCompensate(String error, ElectronicSignParam electronicSignParam) {
        log.error("采购单异步签章任务失败日志[{}],参数[{}]" , error, JSON.toJSONString(electronicSignParam));
        if (electronicSignParam.getFlowType() == FLOW_TYPE) {
            // 置空合同半程盖章字段
            buyorderExpenseMapper.updateContractUrlOfBuyorder(electronicSignParam.getBuyOrderExpenseId(), null);
        }
    }

    @Override
    protected FileInfo toPdfGetFile(ElectronicSignParam electronicSignParam) {
        if (electronicSignParam.getBuyOrderExpenseId() == null) {
            throw new ServiceException("电子签章:采购费单id不能为空");
        }

        BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(electronicSignParam.getBuyOrderExpenseId());

        electronicSignParam.getBusinessInfo().setOrderNo(buyorderExpenseEntity.getBuyorderExpenseNo());

        String contractTemplateUrl = erpDomain + PRINT_CONTRACT_URL + electronicSignParam.getBuyOrderExpenseId();
        String html2PdfUrl = html2PdfDomain + RENDER_URL;

        UrlToPdfParam urlToPdfParam = UrlToPdfParam.defaultUrlToPdfParam(contractTemplateUrl);
        // 上传未盖章空白合同链接
        String ossUrl = ossUtilsService.migrateFile2Oss(html2PdfUrl, "pdf" , "采购费用单合同" + buyorderExpenseEntity.getBuyorderExpenseNo(), urlToPdfParam);
        log.info("原电子签章:自动生成订单模板合同,采购费用单ID[{}]，采购费用单编号[{}]，合同url[{}]" , buyorderExpenseEntity.getBuyorderExpenseId(), buyorderExpenseEntity.getBuyorderExpenseNo(), ossUrl);

        if (isRemoveBlankPage) {
            AtomicInteger retryCount = new AtomicInteger(0);
            ossUrl = removeBlankPdfPagesAndSaveFile2Oss(ossUrl, retryCount,null);
        }

        if (StringUtils.isBlank(ossUrl)) {
            log.error("电子签章:自动生成采购费用单模板合同失败，采购费用单ID[{}]" , electronicSignParam.getBuyOrderExpenseId());
            throw new ServiceException("电子签章:自动生成采购费用单合同失败");
        }
        log.info("电子签章:自动生成订单模板合同,采购费用单ID[{}]，采购费用单编号[{}]，合同url[{}]" , buyorderExpenseEntity.getBuyorderExpenseId(), buyorderExpenseEntity.getBuyorderExpenseNo(), ossUrl);
        buyorderExpenseMapper.updateContractUrlOfBuyorder(electronicSignParam.getBuyOrderExpenseId(), ossUrl);
        FileInfo fileInfo = new FileInfo();
        fileInfo.setFileName(CharSequenceUtil.subAfter(ossUrl, "=" , true) + ".pdf");
        fileInfo.setFilePath(ossUrl);
        return fileInfo;
    }

    @Override
    public ElectronicSignParam buildElectronicSignParam(Integer buyorderId, BusinessInfo businessInfo) {
        return null;
    }

    @Override
    protected void electronicSignSuccessCompensate(ElectronicSignParam electronicSignParam) {

    }


    @Override
    protected void preMqProcessors(SignCallbackDto signCallbackDto) {
        log.info("进入采购费用订单实际消费业务类前置处理器,编号{}" , signCallbackDto.getOrderNo());
        AtomicInteger retryCount = new AtomicInteger(0);
        String fileName;
        BuyorderExpenseEntity buyorerExpenseByOrderNo = buyorderExpenseMapper.getBuyorerExpenseByOrderNo(signCallbackDto.getOrderNo());
        BuyorderExpenseDetailEntity buyorderExpenseDetailEntity = buyorderExpenseDetailMapper.selectByBuyorderExpenseId(buyorerExpenseByOrderNo.getBuyorderExpenseId());
        if (buyorderExpenseDetailEntity != null) {
            fileName = buyorerExpenseByOrderNo.getBuyorderExpenseNo() + buyorderExpenseDetailEntity.getTraderName();
        } else {
            fileName = FILENAME_PREFIX + signCallbackDto.getOrderNo();
        }
        log.info("消费-》oss保存文件名：{}" , fileName);
        this.saveFile2Oss(signCallbackDto, retryCount, fileName);
    }


    /**
     * 采购订单：半程签贝登章 更新合同url
     *
     * @param signCallbackDto 电子签章MQ回传消息
     */
    @Override
    protected void mqProcessors(SignCallbackDto signCallbackDto) {
        log.info("进入采购费用订单实际消费业务类,编号{}" , signCallbackDto.getOrderNo());
        BuyorderExpenseEntity buyorerExpenseByOrderNo = buyorderExpenseMapper.getBuyorerExpenseByOrderNo(signCallbackDto.getOrderNo());
        if (buyorerExpenseByOrderNo == null) {
            log.error("采购单不存在,编号{}" , signCallbackDto.getOrderNo());
            return;
        }
        buyorderExpenseMapper.updateContractUrlOfBuyorder(buyorerExpenseByOrderNo.getBuyorderExpenseId(), signCallbackDto.getFileUrl());
    }

    @Override
    protected void postMqProcessors(SignCallbackDto signCallbackDto) {

    }


    @Override
    protected void dealWithByMqException(String errorMsg, SignCallbackDto signCallbackDto) {
        log.error("Mq消费失败，补偿任务触发[{}]" , JSON.toJSONString(signCallbackDto));
        if (signCallbackDto.getBusinessType() == 2) {
            BuyorderExpenseEntity buyorerExpenseByOrderNo = buyorderExpenseMapper.getBuyorerExpenseByOrderNo(signCallbackDto.getOrderNo());
            // 置空合同半程盖章字段
            buyorderExpenseMapper.updateContractUrlOfBuyorder(buyorerExpenseByOrderNo.getBuyorderExpenseId(), null);
        }
    }

}
