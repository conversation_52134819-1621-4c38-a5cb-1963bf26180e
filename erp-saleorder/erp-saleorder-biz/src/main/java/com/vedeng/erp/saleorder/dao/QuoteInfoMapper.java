package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.saleorder.dto.QuoteGoodsInfoDto;
import com.vedeng.erp.saleorder.dto.QuoteInfoDto;

import java.util.List;

public interface QuoteInfoMapper {

    /**
     * @desc 根据报价单号查询报价单信息
     * <AUTHOR>
     * @param quoteorderNo
     * @return
     */
    QuoteInfoDto queryInfoByNo(String quoteorderNo);

    /**
     * @desc 根据报价单id查询报价单商品信息
     * <AUTHOR>
     * @param quoteorderId
     * @return
     */
    List<QuoteGoodsInfoDto> queryGoodsInfoById(Integer quoteorderId);

    /**
     * 查询报价商品信息
     * @param quoteorderId
     * @return
     */
    List<QuoteGoodsInfoDto> queryTraderInfoByQuoteId(Integer quoteorderId);
}
