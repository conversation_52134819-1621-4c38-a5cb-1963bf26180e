package com.wms.service.other;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SpecialDeliveryEnum;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dao.WmsSendOrderMapper;
import com.wms.dto.PutPurchaseOrderDto;
import com.wms.dto.PutPurchaseOrderGoodsDto;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.DoPutService;
import com.wms.service.PurcjaseOrderService;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.wms.service.listenner.PurchaseExgAuditFinishListener;
import com.wms.service.listenner.PurchaseReturnAuditFinishListenner;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName PurcjaseOrderServiceImpl.java
 * @Description TODO
 * @createTime 2021年09月23日 23:39:00
 */
@Service
public class PurcjaseOrderServiceImpl implements PurcjaseOrderService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PurcjaseOrderServiceImpl.class);

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private AfterSalesService afterSalesService;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Resource
    private UserMapper userMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Autowired
    private PurchaseReturnAuditFinishListenner purchaseReturnAuditFinishListenner;

    @Autowired
    private PurchaseExgAuditFinishListener purchaseExgAuditFinishListener;

    @Autowired
    private DoPutService doPutService;

    /**
     * 单个采购单
     * @param buyorderId
     * @throws Exception
     */
    @Override
    public void dealWithSingleBuyOrder(Integer buyorderId) throws Exception {
        doPutService.doPutPurchaseOrderMethod(buyorderId,null);
        /*List<PutPurchaseOrderGoodsDto> details = new ArrayList<>();

        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyorderId);

        buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyorder.getBuyorderId())
                .stream()
                .forEach(buyOrderGood -> {

                    PutPurchaseOrderGoodsDto detailItem = new PutPurchaseOrderGoodsDto();
                    detailItem.setSku(buyOrderGood.getSku());
                    detailItem.setDedi04(buyOrderGood.getBuyorderGoodsId() + StringUtils.EMPTY);

                    //采购单数量 - 采购单到货数量 - 已完结售后数量
                    detailItem.setOrderedQty(buyOrderGood.getNum()
                            - buyOrderGood.getArrivalNum()
                            - afterSalesService.getFinishAfterSaleNum(buyOrderGood.getBuyorderGoodsId()));

                    if (SpecialDeliveryEnum.NEED_SPECIAL_DELIVERY.getCode().equals(
                            buyorderGoodsMapper.getSpecialDeliveryByRelatedId(buyOrderGood.getBuyorderGoodsId()))){
                        detailItem.setLotAtt07(buyorder.getBuyorderNo());
                    }

                    if(detailItem.getOrderedQty() > 0){
                        details.add(detailItem);
                    }

                });

        //如果detail为空就不下发
        if(CollectionUtils.isEmpty(details)){
            return;
        }

        //下发采购单
        sendPurchaseOrder(buyorder,details);*/
    }

    /**
     * 单个退货单
     * @param afterSaleId
     * @throws Exception
     */
    @Override
    public void dealWithSingleBuyReturnOrder(Integer afterSaleId) throws Exception{

        AfterSalesVo afterSalesVo = this.afterSalesMapper.getAfterSaleVoById(afterSaleId);

        User user = userMapper.getUserByUserId(afterSalesVo.getCreator());

        LOGGER.info("处理采购退货单:" + afterSalesVo.getAfterSalesNo());
        purchaseReturnAuditFinishListenner.onActionHappen(afterSalesVo,true,user);
    }

    /**
     * 单个换货单
     * @param afterSaleId
     */
    @Override
    public void dealWithSinglePurchaseExchange(Integer afterSaleId) throws Exception{

        AfterSalesVo afterSalesVo = this.afterSalesMapper.getAfterSaleVoById(afterSaleId);

        User user = userMapper.getUserByUserId(afterSalesVo.getCreator());
        purchaseExgAuditFinishListener.onActionHappen(afterSalesVo,true,user);
    }


    /**
     * 下发采购入库单
     * @param buyorder
     * @return
     */
    /*private void sendPurchaseOrder(Buyorder buyorder, List<PutPurchaseOrderGoodsDto> details) throws Exception{

        PutPurchaseOrderDto putPurchaseOrderDto = new PutPurchaseOrderDto();

        putPurchaseOrderDto.setPoType(WmsInterfaceOrderType.INPUT_PURCHASE);
        putPurchaseOrderDto.setDocNo(buyorder.getBuyorderNo());
        putPurchaseOrderDto.setPoCreationTime(DateUtil.convertString(buyorder.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        putPurchaseOrderDto.setExpectedArriveTime1(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
        putPurchaseOrderDto.setExpectedArriveTime2(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));

        putPurchaseOrderDto.setSupplierId(buyorder.getTraderId());
        putPurchaseOrderDto.setSupplierName(buyorder.getTraderName());

        User user = userMapper.getUserInfoByUserId(buyorder.getCreator());
        putPurchaseOrderDto.setPoReferenceA(StringUtil.isEmpty(user.getOrgName()) ? "/" : user.getOrgName());
        putPurchaseOrderDto.setPoReferenceB(user.getUsername());
        putPurchaseOrderDto.setNotes(buyorder.getLogisticsComments());

        //入库单的详情
        putPurchaseOrderDto.setDetails(details);

        //wms采购入库单
        WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_PURCHASE_ORDER);

        XxlJobLogger.log("采购单:"+buyorder.getBuyorderNo()+",开始下发WMS,参数:" + JSON.toJSONString(putPurchaseOrderDto));
        LOGGER.info("历史采购单:"+buyorder.getBuyorderNo()+",开始下发WMS,参数:" + JSON.toJSONString(putPurchaseOrderDto));
        wmsInterface.request(putPurchaseOrderDto);
    }*/
}
