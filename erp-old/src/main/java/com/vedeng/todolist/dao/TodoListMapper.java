package com.vedeng.todolist.dao;

import com.vedeng.order.model.Buyorder;
import com.vedeng.todolist.dto.PersonalTodoDealingInfo;
import com.vedeng.todolist.model.TodoList;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface TodoListMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(TodoList record);

    int insertSelective(TodoList record);

    TodoList selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TodoList record);

    int updateByPrimaryKey(TodoList record);

    List<TodoList> selectUnHandledByBuzTypeAndBuzId(@Param("buzType") Integer buzType, @Param("buzId") Integer buzId);

    int countUnHandledItem(@Param("buzType") Integer buzType, @Param("buzId") Integer buzId);

    List<TodoList> selectUnHandledByBuzTypeAndBuzIdAndBuzExtra(@Param("buzType") Integer buzType, @Param("buzId") Integer buzId,
                                                     @Param("buzExtra") String buzExtra);

    List<TodoList> getUnHandledListByBuzType(@Param("buzType") Integer buzType);

    void updateStatusByBuzTypeAndBuzId(@Param("buzType") Integer buzType,
                                       @Param("buzId") Integer buzId,
                                       @Param("status") Integer status,
                                       @Param("updater") Integer updater,
                                       @Param("updateTime") Long updateTime);

    List<TodoList> getUnHandledRiskCheckSkuTodoListByUserList(@Param("buzType") Integer buzType, @Param("userList") List<Integer> userList);

    List<TodoList> getHandledRiskCheckSkuTodoListByUserList(@Param("buzType") Integer buzType,@Param("userList") List<Integer> userIdList,@Param("firstDayTimestamp") long firstDayTimestamp);

    List<TodoList> getUnHandledRiskCheckTraderSupplyTodoListByUserList(@Param("buzType") Integer buzType, @Param("userList") List<Integer> userList);

    List<TodoList> getHandledRiskCheckTraderSupplyTodoListByUserList(@Param("buzType") Integer buzType, @Param("userList") List<Integer> userList,@Param("firstDayTimestamp") long firstDayTimestamp);

    List<TodoList> getUnHandledMaintainTodoListCountByUserListGroupByGrade(@Param("buzType") Integer buzType,
                                                      @Param("userList") List<Integer> userList);

    Integer getUnHandledTodoListCountByBuzTypeListAndPropertyGroupByBuzExtra(@Param("buzTypeList") List<Integer> buzTypeList,
                                                               @Param("buzProperty") String buzProperty);

    List<TodoList> getUnHandledMaintainTodoListSkuByUserList(@Param("buzType") Integer buzType, @Param("userList") List<Integer> userList);

    void deleteByBuzTypeAndBuzId(@Param("buzType") Integer buzType, @Param("buzId") Integer buzId);

    List<TodoList> getUnHandledTodoListByBuzTypeListAndBuzExtra(@Param("buzTypeList") List<Integer> buzTypeList, @Param("buzExtra") String buzExtra);

    void deleteByBuzTypeAndBuzIdAndBuzExtra(@Param("buzType") Integer buzType, @Param("buzId") Integer buzId, @Param("buzExtra") String buzExtra);

    void deleteByBuzTypeAndBuzExtra(@Param("buzType") Integer buzType, @Param("buzExtra") String buzExtra);

    void finishZlbTodoList(@Param("buzType") Integer buzType, @Param("buzId") Integer buzId, @Param("checkStatus") Integer checkStatus, @Param("updateTime") Long updateTime);

    Integer getUnHandledCountByBuzType(Integer buzType);

    List<TodoList> getTodoListByExtraProperty(@Param("orderNo") String orderNo,@Param("property") String property);


    List<TodoList> getUnHandledSkuPushTodoList(@Param("sceneIdList") List<Integer> sceneIdList,@Param("userList") List<Integer> userIdList);

    List<PersonalTodoDealingInfo> getAllRiskCheckSkuTodoListGroupByAssitId(@Param("buzType") Integer buzType,@Param("firstDayTimestamp") long firstDayTimestamp);




    List<TodoList> getOrderList(@Param("buzId") Integer buzId,@Param("property") String property,@Param("status") Integer status);

    void updateStatusByBuzTypeAndBuzIdAndOrderNo(@Param("buzType") Integer buzType, @Param("buzId") Integer buzId, @Param("orderNo") String orderNo, @Param("status") Integer status,
                                                 @Param("updater") Integer updater,
                                                 @Param("updateTime") Long updateTime);
    /**
     * <b>Description:</b><br>
     * 获取供应商许可证数量
     *
     * @param searchDate, searchType
     * @return java.lang.Integer
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/7/19 16:37
     */
    Integer getSupplyLicenseNum(@Param("searchDate")Long searchDate,@Param("searchType")Integer searchType);
    /**
     * <b>Description:</b><br>
     * 获取客户许可证数量
     *
     * @param searchDate, searchType
     * @return java.lang.Integer
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/7/19 16:37
     */
    Integer getTraderLicenseNum(@Param("searchDate")Long searchDate,@Param("searchType")Integer searchType);


    /**
     * 根据条件获取同行单待办事项数量
     *
     * @param subUserIdList 用户id
     * @param peerListFilterTime 过滤时间
     * @param isUrgent 是否催办 null=全部数据， 0=未催办，1=催办
     * @param orgId 组织id
     * @return Integer 待办事项数量
     */
    Integer getReceiptRecordCountGroupByCategoryCount(@Param("subUserIdList") List<Integer> subUserIdList,
                                                      @Param("peerListFilterTime") Long peerListFilterTime,
                                                      @Param("isUrgent") Integer isUrgent,
                                                      @Param("orgId") Integer orgId);
    /**
     * 获取当前代办涉及的所有组
     * @param subUserIdList
     * @return
     */
    Set<Integer> byReceiptRecordCountGroupByCategoryGetOrgIds(@Param("subUserIdList") List<Integer> subUserIdList,
                                                              @Param("peerListFilterTime")Long peerListFilterTime);



    /**
     * 获取crm任务数量
     */
    Integer getCrmTaskCount(@Param("userId") Integer userId);

    /**
     * 获取crm待办任务数量
     */
    Integer getCrmTodoTaskCount(@Param("userId") Integer userId);
}