package com.vedeng.erp.buyorder.common.utils;

import com.vedeng.erp.buyorder.dto.StatusNode;

import java.util.ArrayList;
import java.util.List;

import static com.vedeng.erp.buyorder.common.enums.AfterSalesStatus.*;

public class ValidStatusProcessUtil {

    public static final int PAY_APPLY_TYPE = 1;

    public static final int AFTER_SALE_TYPE = 2;

    public static final int OVER_AFTER_SALE_TYPE = 3;

    public static final int OVER_AFTER_SALE_BEYOUND_TYPE = 4;

    public static List<StatusNode> initValidStatusNodeList(int type) {
        List<StatusNode> validStausNode = new ArrayList<>();
        switch (type) {
            case PAY_APPLY_TYPE:
                /**
                 * PAY_APPLY_VALID_STATER("开始审核"),
                 *     PAY_APPLY_VALID_APPLYER("申请人"),
                 *     PAY_APPLY_VALID_ORDER("财务制单"),
                 *     PAY_APPLY_VALID_VALID("财务审核"),
                 *     PAY_APPLY_VALID_OVER("审核完成"),
                 *     PAY_APPLY_VALID_REJECT("驳回"),
                 */
                validStausNode.add(new StatusNode(PAY_APPLY_VALID_STATER.getStatus(),0));
                validStausNode.add(new StatusNode(PAY_APPLY_VALID_APPLYER.getStatus(),0));
                validStausNode.add(new StatusNode(PAY_APPLY_VALID_ORDER.getStatus(),0));
                validStausNode.add(new StatusNode(PAY_APPLY_VALID_VALID.getStatus(),0));
                validStausNode.add(new StatusNode(PAY_APPLY_VALID_OVER.getStatus(),0));
                break;
            case AFTER_SALE_TYPE:
                /**
                 * AFTER_SALE_VALID_STATER("开始审核"),
                 *     AFTER_SALE_VALID_APPLYER("申请人"),
                 *     AFTER_SALE_VALID_MANAGE("产品主管审核"),
                 *     AFTER_SALE_VALID_OVER("审核完成"),
                 *     AFTER_SALE_VALID_REJECT("驳回"),
                 */
                validStausNode.add(new StatusNode(AFTER_SALE_VALID_STATER.getStatus(),0));
                validStausNode.add(new StatusNode(AFTER_SALE_VALID_APPLYER.getStatus(),0));
                validStausNode.add(new StatusNode(AFTER_SALE_VALID_MANAGE.getStatus(),0));
                validStausNode.add(new StatusNode(AFTER_SALE_VALID_OVER.getStatus(),0));
                break;
            case OVER_AFTER_SALE_TYPE:
                /**
                 * OVER_AFTER_SALE_VALID_STATER("开始审核"),
                 *     OVER_AFTER_SALE_VALID_APPLYER("申请人"),
                 *     OVER_AFTER_SALE_VALID_MANAGE("产品主管"),
                 *     OVER_AFTER_SALE_VALID_OVER("审核完成"),
                 *     OVER_AFTER_SALE_VALID_REJECT("驳回"),
                 */
                validStausNode.add(new StatusNode(OVER_AFTER_SALE_VALID_STATER.getStatus(),0));
                validStausNode.add(new StatusNode(OVER_AFTER_SALE_VALID_APPLYER.getStatus(),0));
                validStausNode.add(new StatusNode(OVER_AFTER_SALE_VALID_MANAGE.getStatus(),0));
                validStausNode.add(new StatusNode(OVER_AFTER_SALE_VALID_OVER.getStatus(),0));
                break;
        }
        return validStausNode;
    }
}
