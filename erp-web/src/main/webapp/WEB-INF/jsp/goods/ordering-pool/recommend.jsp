<%--
  Created by IntelliJ IDEA.
  User: admin
  Date: 2021/5/19
  Time: 16:19
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="推荐定品" scope="application" />
<%@ include file="../../common/common.jsp"%>
<html>
<head>
</head>
<body>
    <div class="content">
        <div class="searchfunc">
            <form action="${pageContext.request.contextPath}/ordering-pool/goods/recommendOrdering.do" method="post" id="search">
                <br />
                <ul>
                    <li>
                        <label class="infor_name">销售额:</label>
                        <select class="input-middle f_left" name="timeFrame">
                            <option value="12" <c:if test="${recommendOrderingQueryVO.timeFrame == 12}">selected="selected"</c:if>>去年</option>
                            <option value="3" <c:if test="${recommendOrderingQueryVO.timeFrame == 3}">selected="selected"</c:if>>近三个月</option>
                            <option value="1" <c:if test="${recommendOrderingQueryVO.timeFrame == 1}">selected="selected"</c:if>>近一个月</option>
                        </select>
                        <select class="input-middle f_left" name="rate">
                            <option value="80" <c:if test="${recommendOrderingQueryVO.rate == 80}">selected="selected"</c:if>>销售额占比80%的SKU</option>
                        </select>
                    </li>
                    <li>
                        <label class="infor_name">商品类型:</label>
                        <select class="input-middle f_left" name="goodsType">
                            <c:forEach var="list" items="${goodsTypes}">
                                <option value="${list.sysOptionDefinitionId}" <c:if test="${recommendOrderingQueryVO.goodsType == list.sysOptionDefinitionId}">selected="selected"</c:if> >${list.title}</option>
                            </c:forEach>
                        </select>
                    </li>
                    <li>
                        <label class="infor_name">是否定品:</label>
                        <select class="input-middle f_left" name="orderingFlag">
                            <option value="-1" <c:if test="${recommendOrderingQueryVO.orderingFlag == -1}">selected="selected"</c:if>>全部</option>
                            <option value="1" <c:if test="${recommendOrderingQueryVO.orderingFlag == 1}">selected="selected"</c:if>>是</option>
                            <option value="0" <c:if test="${recommendOrderingQueryVO.orderingFlag == 0}">selected="selected"</c:if>>否</option>
                        </select>
                    </li>
                    <li style="margin-left: 5%">
                        <span class="bg-light-blue bt-bg-style bt-middle" onclick="search();" style="padding-left: 24px; padding-right: 24px" id="searchSpan">搜索</span>
                    </li>
                    <li>
                        <span class="bg-light-blue bt-bg-style bt-middle" onclick="reset();" style="padding-left: 24px; padding-right: 24px">重置</span>
                    </li>
                    <input type="hidden" class="input-middle" name="pageNo" id="pageNo" value="${page.pageNo}">
                    <input type="hidden" class="input-middle" name="pageSize" id="pageSize" value="${page.pageSize}">

                </ul>
            </form>
        </div>
        <br />
        <div class="">
            <div class="superdiv" style="width:100%">
                <table class="table table-bordered table-striped table-condensed table-centered">
                    <thead>
                        <tr>
                            <th rowspan="2" class="wid1">
                                <div class="input-checkbox">
                                    <label class="input-wrap">
                                        <input type="checkbox" class="J-select-list-all">
                                        <span class="input-ctnr"></span>
                                    </label>
                                </div>
                            </th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">订货号</span></th>
                            <th rowspan="2" class="wid8"><span style="font-weight: bold">商品名称</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">品牌</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">商品类型</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">二级分类</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">规格</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">商品单位</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">成本价(元)</span></th>
                            <th colspan="2" class="wid5"><span style="font-weight: bold">去年销售量</span></th>
                            <th colspan="2" class="wid5"><span style="font-weight: bold">近三个月销售量</span></th>
                            <th colspan="2" class="wid5"><span style="font-weight: bold">近一个月销售量</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">归属人</span></th>
                            <th rowspan="2" class="wid3"><span style="font-weight: bold">SKU审核状态</span></th>
                            <th rowspan="2" class="wid4"><span style="font-weight: bold">操作项</span></th>
                        </tr>
                        <tr style="color: #00a0e9;">
                            <th><span style="font-weight: bold">总</span></th>
                            <th><span style="font-weight: bold">医械购</span></th>
                            <th><span style="font-weight: bold">总</span></th>
                            <th><span style="font-weight: bold">医械购</span></th>
                            <th><span style="font-weight: bold">总</span></th>
                            <th><span style="font-weight: bold">医械购</span></th>
                        </tr>
                    </thead>

                    <tbody>
                    <c:if test="${not empty recommendOrderingListPage}">
                        <c:forEach items="${recommendOrderingListPage }" var="item" varStatus="status">
                            <tr>
                                <td>
                                    <div class="tr-item">
                                        <div class="input-checkbox">
                                            <label class="input-wrap">
                                                <input type="checkbox" class="J-select-sku" name="skuId" value="${item.skuId}">
                                            </label>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value">${item.skuNo}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
<%--
                                        <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${item.skuNo}","link":"./goods/goods/viewbaseinfo.do?goodsId=${item.skuId}","title":"产品信息"}'>
--%>
                                            <span class="item-value">${item.goodsName}</span>
<%--
                                        </a>
--%>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value">${item.brandName}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value">${item.goodsTypeName}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value">${item.categoryNameTwo}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value">${item.spec}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value">${item.unitName}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value">${item.costPrice}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value" id="lastYearSum${item.skuId}">${item.lastYearSum}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value" id="lastYearPart${item.skuId}">${item.lastYearPart}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value" id="threeMonthSum${item.skuId}">${item.threeMonthSum}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value" id="threeMonthPart${item.skuId}">${item.threeMonthPart}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value" id="oneMonthSum${item.skuId}">${item.oneMonthSum}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value" id="oneMonthPart${item.skuId}">${item.oneMonthPart}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value">${item.goodsUserName}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value">${item.checkStatus}</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="tr-item">
                                        <span class="item-value">
                                            <c:if test="${item.joinPoolFlag == 1}">
                                                已加入
                                            </c:if>
                                            <c:if test="${item.joinPoolFlag == 0}">
                                                <a href="javascript:void(0);" onclick="joinPool([${item.skuId}])">加入定品</a>
                                            </c:if>
                                        </span>
                                    </div>
                                </td>
                            </tr>
                        </c:forEach>
                    </c:if>
                    </tbody>
                </table>
            </div>
            <c:if test="${empty recommendOrderingListPage}">
                <!-- 查询无结果弹出 -->
                <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
            </c:if>
            <br />
            <div class="page">
                <tags:page page="${page}"/>
            </div>
        </div>

</div>
    <%@ include file="../../common/footer.jsp"%>
</body>
</html>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/inputSuggest.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/goods/ordering-pool/recommand.js?rnd=${resourceVersionKey}"></script>
<style>

    .suggest-list-wrap {
        position: absolute;
        background: #fff;
        padding: 10px 0;
        box-shadow: 2px 2px 3px rgba(0, 33, 66, 0.1);
        overflow: unset;
        z-index: 11;
        border: 1px solid #ced2d9;
        width: 100%;
        display: none;
        max-height: 275px;
        overflow-y: auto;
        left: 100px;
    }

    .suggest-wrap {
        position: relative;
    }

    .suggest-list-wrap .suggest-item {
        padding: 5px 10px;
        color: #666;
        display: block;
        text-decoration: none;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        cursor: pointer;
    }

    .suggest-list-wrap .suggest-item strong {
        color: #f60;
        font-size: 12px;
    }

    .suggest-list-wrap .suggest-item.disabled {
        color: #c2c2c2;
        cursor: default;
    }

    .suggest-list-wrap .suggest-item.disabled strong {
        color: #c2c2c2;
        cursor: default;
        font-weight: normal;
    }
</style>
