package com.vedeng.logistics.model.vo;

import com.vedeng.logistics.model.WarehouseGoodsOutIn;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/15 14:09
 **/
@Data
public class WarehouseGoodsOutVo extends WarehouseGoodsOutIn {

    /**
     * 归属人 ID
     */
    private Integer belongUserId;

    /**
     * 归属人
     */
    private String belongUserName;

    /**
     * 归属部门
     */
    private String belongUserOrgName;

    /**
     * 订单总额
     */
    private BigDecimal orderTotalAmount;

    /**
     * 商品总数
     */
    private Integer totalGoodsNum;

    /**
     * 收货客户
     */
    private String takeTraderName;

    /**
     * 收货联系人
     */
    private String takeTraderContactName;

    /**
     * 收货联系电话
     */
    private String takeTraderContactTelephone;

    /**
     * 收货联系手机
     */
    private String takeTraderContactMobile;

    /**
     * 收货地区
     */
    private String takeArea;

    /**
     * 收货地址
     */
    private String takeAddress;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 物流备注
     */
    private String logisticsComments;

    /**
     * 关联单 ID
     */
    private Integer relatedId;

    /**
     * 出库时间 string类型
     */
    private String outInTimeStr;


    /**
     * 商品出库记录
     */
    private List<WarehouseGoodsOutLogVo> warehouseGoodsOutLogList;

    /**
     * 物流公司ID
     */
    private Integer logisticsCompanyId;

    /**
     * 运费说明ID
     */
    private Integer freightDescriptionId;


}
