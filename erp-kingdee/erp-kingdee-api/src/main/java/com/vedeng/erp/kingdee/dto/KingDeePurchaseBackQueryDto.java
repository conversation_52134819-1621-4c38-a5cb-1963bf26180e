package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.*;

import java.util.List;

/**
 * 采购退料单 dto  https://www.yuque.com/manhuo/gf1570/gs91wo
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶采购退料单 dto  由erp实际业务转换
 * @date
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class KingDeePurchaseBackQueryDto extends KingDeeMqBaseDto {

    /**
     *  0：表示新增 非0：云星空系统单据FID值，表示修改
     */
    private String fId;

    /**
     * 单据编号 填写单据编号，若为空则调用系统的编码规则生成
     */
    private String fBillNo;
    /**
     * 贝登单据头ID 贝登单据头ID号（预留）
     */
    private String fQzokBddjtId;
    /**
     * 单据日期 填单据日期，格式yyyy-MM-dd
     */
    private String fDate;
    /**
     * 退料类型 默认：B
     */
    private String fMrType;
    /**
     * 退料方式 默认：B
     */
    private String fMrMode;

    /**
     * 退料数量 填写实际的退料数量 FPURMRBENTRY_Link为此数组总计退货数量
     */
    private List<KingDeePurchaseBackDetailDto> fpurmrbentry;

    //Fnumber
    /**
     * 单据类型  填单据类型编码，默认填TLD01_SYS
     */
    private String fBillTypeId;

    /**
     * 退料组织 填写组织编码
     */
    private String fStockOrgId;

    /**
     * 供应商 填写供应商编码
     */
    private String fSupplierId;

    /**
     * 查询所有的出库单号
     */
    private List<String> fBillNos;

    @Override
    public String getFormId() {
        return KingDeeFormConstant.PURCHASE_BACK;
    }
}
