package com.newtask.goods;

/**
 * <AUTHOR> [<EMAIL>]
 */
public enum DBTableType {

    /**
     * 首营信息
     */
    FIRST_ENGAGE("FIRST_ENGAGE_ID", "T_FIRST_ENGAGE"),

    /**
     * SKU
     */
    SKU("SKU_ID", "V_CORE_SKU"),


    /**
     * V_CORE_SKU_SEARCH
     */
    SKU_SEARCH("SKU_ID", "V_CORE_SKU_SEARCH"),

    /**
     * SPU
     */
    SPU("SPU_ID", "V_CORE_SPU"),


    /**
     * T_SALEORDER_GOODS
     */
    SALEORDER_GOODS("SALEORDER_GOODS_ID", "T_SALEORDER_GOODS");

    private String primaryKeyName;
    private String tableName;


    DBTableType(String primaryKeyName, String tableName) {
        this.primaryKeyName = primaryKeyName;
        this.tableName = tableName;
    }


    public String getPrimaryKeyName() {
        return primaryKeyName;
    }

    public String getTableName() {
        return tableName;
    }
}
