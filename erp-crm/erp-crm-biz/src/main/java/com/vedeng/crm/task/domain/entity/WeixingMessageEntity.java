package com.vedeng.crm.task.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;


/**
 * 企业微信消息记录表
 */
@Getter
@Setter
public class WeixingMessageEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long id;

    /**
     * 接收人USER_ID
     */
    private Integer messageUserId;

    /**
     * 业务名称-中文
     */
    private String bizType;

    /**
     * 业务类型 01线索池 02商机库 03 报价单
     */
    private String bizTypeEnum;

    /**
     * 主-任务类型
     */
    private Integer mainTaskType;

    /**
     * 子-任务类型
     */
    private Integer subTaskType;

    /**
     * 告警的任务数量：1条或多条
     */
    private Integer messageCount;

    /**
     * 日志参数
     */
    private String messageParams;

    /**
     * 日志内容
     */
    private String messageContent;

    /**
     * 是否删除 0否 1是
     */
    private Boolean isDelete;

    /**
     * 更新备注
     */
    private String updateRemark;

}