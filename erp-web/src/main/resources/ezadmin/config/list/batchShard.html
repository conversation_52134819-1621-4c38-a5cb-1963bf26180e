<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>客户批量分享 </title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="batchShard" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="checkbox">
<div id="appendHead">
<style>
    .layui-icon-search{
        display:none !important;
    }
</style>
    <script type="text/javascript">
        $(document).ready(function() {
            var objVisitorId1 = $('#itemName-SALE_USER_ID');
            // var xmel = $(objVisitorId)[0];
            debugger;
            // //var initdata=objVisitorId.attr("itemsJson");
            var initvalue1=objVisitorId1.attr("value");
            // 创建隐藏域
            var input = $('<input>').attr({
                type: 'hidden',
                id:'VISITOR_ID_HIDDEN',
                name: 'SALE_USER_ID',
                value: initvalue1
            });

            // 将隐藏域添加到form中
            $('#searchForm').append(input);
        });
    </script>
</div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">


        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">客户名称</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="TRADER_NAME" placeholder="模糊搜索" style="" alias="A.TRADER_NAME" jdbctype="" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">接收人</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select-share" name="SALE_USER_ID" placeholder="请选择" style=""
                        alias="SHARE1.SALE_USER_ID" jdbctype="" data="${session.EZ_SESSION_MY_USER_MAP_KEY}"
                        datatype="KVSQLCACHE" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">分享时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="" style="" alias="SHARE1.ADD_TIME" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
        <button type="table" class="layui-btn" url="/ezadmin/form/form-batchAaddShard?ID=1" opentype="SELF" windowname="批量分享">批量分享</button>
        <button type="table" url="/ezadmin/form/form-batchDeleteShard?ID=" opentype="CONFIRM_AJAX" windowname="批量取消"  >批量取消</button>
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="${TRADER_NAME}" datatype="" data="" style="min-width:200px;
word-break: break-all;" head="18">客户名称</th>
            <th item_name="USERNAME" name="USERNAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:left" head="18" >归属销售</th>
            <th item_name="SALE_USER_NAME" name="SALE_USER_NAME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:left" head="18" >接收人</th>
            <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">分享时间</th>
            <th type="rowbutton" id="rowbutton">
                <button  opentype="CONFIRM_AJAX"  type="single" url="/traderCustomerBase/saveCancelShare.do?id=${ID}"
                         windowname="取消分享${TRADER_NAME}给{SALE_USER_NAME}" name="deleteBtn"  >取消分享
                </button>

            </th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by SHARE1.ID DESC" groupby="">
            StringBuilder sql=new StringBuilder("select ");
            sql.append("A.TRADER_NAME ,
                A.TRADER_ID ,
                B.TRADER_CUSTOMER_ID ,
                U1.USERNAME ,
                SHARE1.SALE_USER_NAME,
                 DATE_FORMAT(SHARE1.ADD_TIME, '%Y-%m-%d %H:%i:%s') AS ADD_TIME,
                SHARE1.ID AS ID
                FROM T_TRADER A
        JOIN T_TRADER_CUSTOMER B ON   A.TRADER_ID=B.TRADER_ID
        JOIN T_R_TRADER_J_USER R1 ON A.TRADER_ID = R1.TRADER_ID
        JOIN T_R_SALES_J_TRADER  SHARE1  ON B.TRADER_CUSTOMER_ID = SHARE1.TRADER_CUSTOMER_ID AND B.TRADER_ID=SHARE1.TRADER_ID
                LEFT JOIN T_USER U1 ON R1.USER_ID = U1.USER_ID");
        sql.append(" where R1.USER_ID=");
        sql.append($("EZ_SESSION_USER_ID_KEY"));

        list=search(sql);
        return list;
            </pre> count:
                <pre id="count" class="layui-code">

                </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <div>
        <div >



        </div>
    </div>
    <script>

        $(function(){


            $.ajax({
                url: '/traderCustomerBase/queryShardUserList.do?userId='+$("#EZ_SESSION_USER_ID_KEY").val(),
                type:"GET",
                dataType : "json",
                success:function(res){
                    if(res.code==0){
                        var userList = res.data;
                        // userList.forEach(function(obj2){
                        //     var isDuplicate = visitorList.some(function(obj1) {
                        //         return obj1.K === obj2.K;
                        //     });
                        //     if (!isDuplicate) {
                        //         visitorList.push(obj2);
                        //     }
                        // });
                        var objVisitorId = $('#itemName-SALE_USER_ID');
                        // var objVisitorId = $('#itemName-VISITOR_ID');
                        // var xmel = $(objVisitorId)[0];
                        // //var initdata=objVisitorId.attr("itemsJson");
                        var initvalue=objVisitorId.attr("value").replace(/\[+|\]+/g, '');
                        initvalue = "["+initvalue+"]";
                        var itemName=objVisitorId.attr("name");
                        var itemPlaceholder=objVisitorId.attr("itemPlaceholder");
                        // var divElement = document.getElementById('itemName-VISITOR_ID');
                        // var initvalue = divElement.getAttribute('value');
                        // var itemName = divElement.getAttribute('name');
                        // var itemPlaceholder = divElement.getAttribute('itemPlaceholder');
                        ////console.log(2);
                        $("#VISITOR_ID_HIDDEN").remove();
                        var demo1 = xmSelect.render({
                            el: objVisitorId[0],
                            language: 'zn',
                            filterable: true,
                            filterMethod: function (val, item, index, prop) {//重写搜索方法。
                                if(val == item.K){//把value相同的搜索出来
                                    return true;
                                }
                                if(item.V.indexOf(val) != -1){//名称中包含的搜索出来
                                    return true;
                                }
                                return !ezpingyin(val, item.V, item.K);
                            },
                            style: {
                                height: '26px'  ,
                            },
                            prop: {
                                name: 'V',
                                value: 'K',
                            },
                            name: itemName,
                            tips: itemPlaceholder,
                            data:  userList,
                            initValue:  JSON.parse(initvalue)
                        });
                    }

                }
            });
        })

        function getCheckIdList() {
            var goodsIdArr="-1";
            $("input[name='list-body-checkbox']:not(:disabled)").each(function () {
                if (this.checked) {
                    goodsIdArr+=','+$(this).attr("_CHECK_ID_VALUE");
                }
            })
            if(goodsIdArr=="-1"){
                return "";
            }
            return encodeURI(goodsIdArr);
        }

        $("button[name='批量取消']").click(function(){
            if(getCheckIdsUrl()==""){
                layer.alert("请选择需要取消分享的记录");
                return false;
            }
            $(this).removeClass("ezopenbutton");
            layer.confirm('确认取消分享'+(getCheckIdsUrl().split(",").length-1)+"条记录", {icon: 3, title:'提示'}, function(index){
                $.getJSON("/traderCustomerBase/saveCancelShareByIds.do?ids="+getCheckIdList(),function(data){
                    if(data.code==0){
                        //layer.alert("取消分享成功,3秒后自动刷新页面。");
                        layer.alert('取消分享成功,3秒后自动刷新页面。', function(index){
                            // 这个函数会在用户点击 "确定" 按钮时被调用
                            location.reload();
                            // 最后，你需要调用 layer.close 方法来关闭弹出层
                            layer.close(index);
                        });
                        setTimeout(function () {
                            location.reload();
                        },3000);

                    }else{
                        layer.alert("关闭失败");
                        location.reload();
                    }
                })
                layer.close(index);
            });
            return false;
        })


    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>