package com.vedeng.erp.mobile.dto;

import lombok.Data;

@Data
public class SaleOrderListResultDto {

    /**
     * 销售订单id
     */
    private Integer saleorderId;

    /**
     * 销售订单号
     */
    private String saleorderNo;

    /**
     * 销售订单状态
     */
    private Integer orderStatus;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 归属销售Id
     */
    private Integer currentUserId;

    /**
     * 归属销售名称
     */
    private String currentUserName;

    /**
     * 合同地址
     */
    private String contractUrl;

    /**
     * 开票方式:1手动纸质开票、2自动纸质开票、3自动电子发票
     */
    private Integer invoiceMethod;

    /**
     * 是否寄送发票 0否 1是
     */
    private Integer isSendInvoice;

}
