<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <title>订单助理绑定</title>
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/css/order/buyorder/bind_orderasssistan_productuser.css?rnd=${resourceVersionKey}">
    <style>
        .layui-layer-btn0{
            border-color: #1E9FFF!important;
            background-color: #fff!important;
            color: #333!important;
        }
        .layui-layer-btn1{
            border-color: #1E9FFF!important;
            background-color: #1E9FFF!important;
            color: #fff!important;
        }
        .click{
            cursor: pointer;
            color: #3384ef ;
        }
    </style>
</head>
<body>
<div class="erp-wrap">
    <div class="erp-block base-form search-wrap J-search-wrap">
        <div class="search-list">
            <div class="search-item">
                <div class="item-label">订单助理：</div>
                <div class="item-fields" style="min-width: 100px" >
                    <select class="J-select" id="orderAssistant" name="orderAssistant" >
                        <option value="">请选择</option>
                        <c:forEach var="asslist" items="${orderAssitantUserList}">
                            <option value="${asslist.userId}"
                                    <c:if test="${orderAssitantUserId == asslist.userId}">selected="selected"</c:if>>${asslist.username}</option>
                        </c:forEach>
                    </select>
                </div>
                <div class="search-btns" style="margin-right: 300px">
                    <div class="btn btn-small btn-blue-bd " id='search' style="width: 100px;margin-left: 30px">查询</div>
                </div>
            </div>
        </div>
    </div>
    <div class="erp-block erp-block-list">
        <div class="option-wrap J-fix-wrap">

                <div class="erp-title-txt" style="display: inline-block">已绑定记录</div>
                <div class="btn" style="display: inline-block;float: right" id="addNewRelation" onclick="addNewOrderAssRelation()">新增绑定</div>
                <div style="display:none;">
                    <!-- 弹框 -->
                    <div class="title-click nobor  pop-new-data" id="terminalDiv"></div>
                </div>

        </div>
        <table class="table table-base table-hover base-form J-table-wrap">
            <colgroup>
                <col width="">
                <col width="">
                <col width="">
                <col width="">
                <col width="">
            </colgroup>
            <tbody>
            <tr>
                <th>订单助理</th>
                <th>产品经理</th>
                <th>产品助理</th>
                <th>操作人</th>
                <th>添加时间</th>
                <th>操作</th>
            </tr>
            <c:if test="${empty orderAssInfoList}">
                <tr>

                    <td class="no-data" colspan="6">
                        <div><i class="vd-icon icon-caution1"></i></div>
                        没有匹配的数据
                    </td>
                </tr>
            </c:if>
            <c:if test="${not empty orderAssInfoList}">
                <c:forEach items="${orderAssInfoList}" var="list" varStatus="status">
                <!--隐藏域-->
                    <tr>
                        <td>${list.orderAssistantName}</td>
                        <td>${list.productManagerName}</td>
                        <td>${list.productAssistantName}</td>
                        <td>${list.creatorName}</td>
                        <td>${list.addTimeStr}</td>
                        <td><div data-keyId='${list.id}' onclick="UnbindRelation(this)" class = "click">解除绑定</div></td>
                    </tr>
                </c:forEach>
            </c:if>
            </tbody>
        </table>
        <c:if test="${page.totalPage >= 1}">
        <tags:pageNew page="${page}" />
        </c:if>
    </div>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/pikaday.2.1.0.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/inputSuggest.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/lv-select.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/pages/modules/list.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/order/buyorder/bind_orderasssistan_productuser.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/jquery.min.js'></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/jquery/validation/jquery-form.js"></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js'></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
<script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/form.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/closable-tab.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/common.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/call/call.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/movinghead.js?rnd=${resourceVersionKey}'></script>
</body>

</html>