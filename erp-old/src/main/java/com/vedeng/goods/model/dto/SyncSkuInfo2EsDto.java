package com.vedeng.goods.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 推送至ES_SEARCH 运输类
 *
 * <AUTHOR>
 */
@Data
public class SyncSkuInfo2EsDto {

    /**
     * 属性值名称 空格分隔
     */
    private String attrValue;

    /**
     * 属性值id 空格分隔
     */
    private String skuAttrValueIds;

    /**
     * SKU对应的室场景ID集合，使用空格分割
     */
    private String skuSceneCategoryIds;

    /**
     * erp品牌id
     */
    private Integer brandId;

    /**
     * erp品牌中文名+erp品牌英文名
     */
    private String brandName;

    /**
     * erp品牌英文名
     */
    private String brandNameEn;

    /**
     * sku一级分类名
     */
    private String catName1;

    /**
     * sku二级分类名
     */
    private String catName2;

    /**
     * sku三级分类名
     */
    private String catName3;

    /**
     * 科室1id 科室2id 科室3id ...
     */
    private String departmentIds;

    /**
     * 科室1name 科室2name 科室3name ...
     */
    private String departmentValue;

    /**
     * sku一级分类id
     */
    private Integer erpCatId1;

    /**
     * sku二级分类id
     */
    private Integer erpCatId2;

    /**
     * sku三级分类id
     */
    private Integer erpCatId3;



    /**
     * 1代表有库存 0代表无库存
     */
    private Integer hasStock;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 机构
     */
    private String mechanismIds;

    /**
     * 机构名称
     */
    private String mechanismValue;

    /**
     * 型号
     */
    private String model;

    /**
     * 规格
     */
    private String spec;

    /**
     * 传0 0代表平台是erp
     */
    private Integer platformId = 0;

    /**
     * 商品流商品所在erp 表里的主键自增id (就是V_CORE_SKU的SKU_ID)
     */
    private Integer platformSkuId;

    /**
     * 检查项目1id 检查项目2id 检查项目3id ...(V_CATEGORY_INSPECTION)
     */
    private String projectIds;

    /**
     * 检查项目1name 检查项目2name 检查项目3name...
     */
    private String projectValue;

    /**
     * 商品所属三级分类的销量
     */
    private Integer ruleCategorySaleCount = 0;

    /**
     * 档位
     */
    private Integer ruleGear;

    /**
     * 级别
     */
    private Integer ruleLevel;

    /**
     * 销量比 0 或者 ruleSkuSaleCount/ruleCategorySaleCount
     */
    private BigDecimal ruleSaleRatio = BigDecimal.ZERO;

    /**
     * 近半年sku的销量（订单付款状态大于0）
     */
    private Integer ruleSkuSaleCount = 0;

    /**
     * 近半年商品销量  同上
     */
    private Integer saleCount = 0;

    /**
     * 商品销售额
     */
    private BigDecimal saleTotalMoney = BigDecimal.ZERO;

    /**
     * skuId
     */
    private Integer skuId;

    /**
     * skuNo
     */
    private String skuNo;

    /**
     * skuNo skuId
     */
    private String skuNoWs;

    /**
     * spuId
     */
    private Integer spuId;

    /**
     * spuType
     */
    private Integer spuType;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 已核价商品价格 /0
     */
    private BigDecimal price;

    /**
     * 1，1：已核价 0：未核价
     */
    private Integer isVerifiedPrice;

    /**
     * 1：已授权 0：未授权ø
     */
    private Integer isAuthorized;

    /**
     * 1：支持安装 0：不支持安装
     */
    private Integer isSupportInstallation;

    /**
     * 1：进口 0国产
     */
    private Integer source;

    /**
     * 1 外部商品 2 内部商品
     */
    private Integer isExternal = 2;

    /**
     * 近一年销量
     */
    private Integer oneYearSaleNum;

    /**
     * 经销价
     */
    private BigDecimal distributionPrice;

    /**
     * 终端价
     */
    private BigDecimal terminalPrice;

    /**
     * 最近一年单价
     */
    private BigDecimal oneYearAvgPrice;

    /**
     * 可用库存
     */
    private Integer availableStockNum = 0;

    /**
     * 在途库存
     */
    private Integer onWayNum = 0;

    /**
     * 库存量
     */
    private Integer stockNum;

    /**
     * 版本时间戳 秒
     */
    private Long version;

    /**
     * erp 属性
     */
    private String addSearchScopeName;

    /**
     * 删除标识，为Y时表示商品需要删除掉
     */
    private String deleteFlag;
    
    /**成本价是否含运费 ,0 未维护 1 含运费 2 不含运费*/
    private Integer purchaseContainsFee;
    
    /**销售价是否含运费 ,0 未维护 1 含运费 2 不含运费*/
    private Integer saleContainsFee;
    
}
