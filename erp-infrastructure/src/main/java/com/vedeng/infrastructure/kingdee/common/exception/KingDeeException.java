package com.vedeng.infrastructure.kingdee.common.exception;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶异常类
 * @date 2022/9/20 19:45
 */
@Slf4j
public class KingDeeException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private static final String ERROR_MSG = "金蝶异常:";

    public KingDeeException(String message) {
        super(message);
        log.error(ERROR_MSG + message);
    }

    public KingDeeException(String message, Throwable cause) {
        super(message, cause);
        log.error(ERROR_MSG + message, cause);
    }
}
