package com.vedeng.customerbillperiod.dao;

import com.vedeng.customerbillperiod.dto.CustomerBillPeriodDetailManageDto;
import com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface CustomerBillPeriodUseDetailMapper {
    int deleteByPrimaryKey(Long billPeriodUseDetailId);

    int insert(CustomerBillPeriodUseDetail record);

    int insertSelective(CustomerBillPeriodUseDetail record);

    CustomerBillPeriodUseDetail selectByPrimaryKey(Long billPeriodUseDetailId);

    int updateByPrimaryKeySelective(CustomerBillPeriodUseDetail record);

    /**
     * 获取客户账期已冻结的金额
     * @param billPeriodId 账期id
     * @return 冻结的金额
     */
    BigDecimal getFreezingAmountByBillPeriodId(@Param("billPeriodId") Long billPeriodId);

    /**
     * 查询指定账期的使用记录
     * @param billPeriodId 账期id
     * @return 使用记录
     */
    List<CustomerBillPeriodUseDetail> getCustomerBillPeriodUseDetailListByBillPeriodId(@Param("billPeriodId") Long billPeriodId);

    /**
     * 查询客户订单账期使用明细
     * @param companyId 公司id
     * @param customerId 客户id
     * @param orderId 订单id
     * @return 使用明细
     */
    List<CustomerBillPeriodUseDetail> getCustomerBillPeriodUseDetailListByOrderId(@Param("companyId") Integer companyId,
                                                                                  @Param("customerId") Long customerId,
                                                                                  @Param("orderId") Long orderId);


    /**
     * 查询客户订单的未发生逾期管理的使用明细
     * @param companyId 公司id
     * @param customerId 客户id
     * @param orderId 订单id
     * @return 使用明细
     */
    List<CustomerBillPeriodUseDetail> getCustomerBillPeriodUseDetailListOfUnOverdueManagedByOrderId(@Param("companyId") Integer companyId,
                                                                                                    @Param("customerId") Long customerId,
                                                                                                    @Param("orderId") Long orderId);


    /**
     * 查询客户订单的未发生风险管理的使用明细
     * @param companyId 公司id
     * @param customerId 客户id
     * @param orderId 订单id
     * @return 使用明细
     */
    List<CustomerBillPeriodUseDetail> getCustomerBillPeriodUseDetailListOfUnRiskManagedByOrderId(@Param("companyId") Integer companyId,
                                                                                                    @Param("customerId") Long customerId,
                                                                                                    @Param("orderId") Long orderId);


    /**
     * 批量获取时间范围内指定账期集合的使用记录
     * @param companyId 公司id
     * @param billPeriodIdList 账期id集合
     * @param useDetailStart 账期使用明细添加开始时间
     * @param useDetailEnd 账期使用明细添加截止时间
     * @return 账期使用记录
     */
    List<CustomerBillPeriodUseDetail> batchGetCustomerBillPeriodUseDetailListByBillPeriodIdList(@Param("companyId") Integer companyId,
                                                                                                @Param("billPeriodIdList") List<Long> billPeriodIdList,
                                                                                                @Param("useDetailStart") Long useDetailStart,
                                                                                                @Param("useDetailEnd") Long useDetailEnd);


    /**
     * 占用已冻结的客户账期使用明细
     * @param companyId 公司id
     * @param customerId 客户id
     * @param orderId 订单id
     */
    void occupancyCustomerBillPeriodUseDetailList(@Param("companyId") Integer companyId, @Param("customerId") Long customerId, @Param("orderId") Long orderId);

    /**
     * 查询是否已经占用
     */
    List<CustomerBillPeriodUseDetail> getOccupancyCustomerBillPeriodUseDetail(@Param("companyId") Integer companyId,
                                                                         @Param("customerId") Long customerId, @Param("orderId") Long orderId);

    /**
     * 根据使用类型和关联id，查询是否已有使用记录
     * @param companyId 公司id
     * @param customerId 客户id
     * @param relatedId 关联id
     * @param useType 使用类型
     * @return 使用记录
     */
    List<CustomerBillPeriodUseDetail> getUseDetailListByRelatedIdAndType(@Param("companyId") Integer companyId, @Param("customerId") Long customerId,
                                                                         @Param("relatedId") Long relatedId, @Param("useType") Integer useType);


    /**
     * 查询指定账期的未归还金额
     * @param billPeriodIdList 账期集合
     * @return 未归还金额
     */
    List<CustomerBillPeriodUseDetail> getUnreturnedAmountByBillPeriodIdList(@Param("billPeriodIdList") List<Long> billPeriodIdList);

    /**
     * 每个账期的使用次数
     * @param billPeriodIdList 账期集合
     * @return 账期的使用次数
     */
    List<CustomerBillPeriodUseDetail> getCountUsedGroupByBillPeriod(@Param("billPeriodIdList") List<Long> billPeriodIdList);

    /**
     * <b>Description:</b><br>
     * 方法注释
     * 
     * @param billPeriodId 账期id, parentUseDetailId 售后退货、还款产生的明细对应的支付订单使用ID
     * @return java.util.List<com.vedeng.customerbillperiod.model.CustomerBillPeriodUseDetail>
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/8/5 9:53
     */
    List<CustomerBillPeriodUseDetail> getRevertBillPeriodInfo(@Param("billPeriodId")Long billPeriodId, @Param("parentUseDetailId")Long parentUseDetailId);

    /**
     * <b>Description:</b><br>
     * 获取账期使用明细
     *
     * @param parentUseDetailId 售后退货、还款产生的明细对应的支付订单使用ID
     * @return java.util.List<com.vedeng.customerbillperiod.dto.CustomerBillPeriodDetailManageDto>
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/8/5 14:41
     */
    List<CustomerBillPeriodDetailManageDto> getBillPeriodDetailManage(@Param("parentUseDetailId")Long parentUseDetailId);

    /**
     * 删除已冻结未占用的客户账期使用记录
     * @param companyId 公司id
     * @param customerId 客户id
     * @param relatedId 相关订单id
     */
    void removeCustomerBillPeriodFrozenUseDetail(@Param("companyId") Integer companyId, @Param("customerId") Long customerId, @Param("relatedId") Long relatedId);


    /**
     * 检索未全部归还账期的客户ID集合
     * @return
     */
    List<Integer> getUnReturnedBillPeriodCustomerIds();



    /**
     * 查询客户的使用明细
     * @param companyId 公司id
     * @param customerId 客户id
     * @param orderId 订单id
     * @return 使用明细
     */
    List<CustomerBillPeriodUseDetail> getCustomerBillPeriodUseDetailLisOrderId(@Param("companyId") Integer companyId,
                                                                                                    @Param("customerId") Long customerId,
                                                                                                    @Param("orderId") Long orderId);
}