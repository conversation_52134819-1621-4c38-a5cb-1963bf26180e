package com.vedeng.erp.kingdee.domain.entity;

import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import com.vedeng.common.mybatis.jbatis.annotation.Column;
import com.vedeng.common.mybatis.jbatis.annotation.GeneratedValue;
import com.vedeng.common.mybatis.jbatis.annotation.Id;
import com.vedeng.common.mybatis.jbatis.annotation.Table;
import lombok.*;

/**
    * 收款单
    */
@Getter
@Setter
@Table(name = "KING_DEE_RECEIVE_BILL")
public class KingDeeReceiveBillEntity extends BaseEntity {
    /**
    * 主键
    */
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer kingDeeReceiveBillId;

    /**
    * 云星空系统单据FID值
    */
    private String fId;

    /**
    * 单据编号
    */
    private String fBillNo;

    /**
    * 单据类型id
    */
    private String fBillTypeId;

    /**
    * 付款组织id
    */
    private String fPayOrgId;

    /**
    * 单据日期
    */
    private String fDate;

    /**
    * 银行流水号
    */
    private String fQzokLsh;

    /**
    * 往来单位类型
    */
    private String fContactUnitType;

    /**
    * 往来单位id
    */
    private String fContactUnit;

    /**
    * 交易类型(对公/对私)
    */
    private String fQzokJylx;

    /**
    * 交易主体
    */
    private String fQzokJyzt;

    /**
    * 交易方式
    */
    private String fQzokJyfs;

    /**
    * 回单是否已推送，0否1是
    */
    private Integer fileIsPush;

    /**
    * ERP银行流水id
    */
    private String erpBankBillId;

    /**
    * 是否删除0否1是
    */
    private Integer isDelete;

    /**
    * FRECEIVEBILLENTRY
    */
    @Column(jdbcType = "VARCHAR")
    private JSONArray fReceiveBillEntry;
}