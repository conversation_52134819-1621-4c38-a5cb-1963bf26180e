package com.newtask.goods.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.vedeng.goods.domain.dto.InitSkuInfo2FrontDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2022/8/9
 * @apiNote InitSkuInfo2FrontDto监听器-传入导入的对象
 */
public class InitSkuInfo2FrontListener extends AnalysisEventListener<InitSkuInfo2FrontDto> {

    /**
     * 批处理阈值
     */
    private static final int BATCH_COUNT = 100;

    List<InitSkuInfo2FrontDto> initSkuInfo2FrontDtoList = new ArrayList<>(BATCH_COUNT);

    private Function<List<InitSkuInfo2FrontDto>, Boolean> updateFunc;

    public static final Logger logger = LoggerFactory.getLogger(InitSkuInfo2FrontListener.class);

    /**
     * @Description 读取数据
     * @Param initSkuInfo2FrontDto
     * @Param context
     */
    @Override
    public void invoke(InitSkuInfo2FrontDto initSkuInfo2FrontDto, AnalysisContext context) {
        logger.info("InitSkuInfo2FrontListener解析到一条数据:{}", JSON.toJSONString(initSkuInfo2FrontDto));
        initSkuInfo2FrontDtoList.add(initSkuInfo2FrontDto);
        if (initSkuInfo2FrontDtoList.size() >= BATCH_COUNT) {
            doAfterAllAnalysed(context);
        }
    }

    /**
     * @Description 读取完成
     * @Param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        updateFunc.apply(initSkuInfo2FrontDtoList);
        initSkuInfo2FrontDtoList.clear();
        logger.info("InitSkuInfo2FrontListener一批数据解析完成");
    }

    public InitSkuInfo2FrontListener(Function<List<InitSkuInfo2FrontDto>, Boolean> updateFunc) {
        this.updateFunc = updateFunc;
    }

}
