package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.listener.BaseProcessListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseReadListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseWriteListener;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.common.listener.JobListener;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.processor.BatchBuyOrderGiftOutFormProcessor;
import com.vedeng.erp.kingdee.batch.processor.PurchaseGiftOutProcessor;
import com.vedeng.erp.kingdee.batch.writer.CommonFileDataWriter;
import com.vedeng.erp.kingdee.batch.writer.PurchaseGiftOutWriter;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 赠品采购单售后退货退票 job
 * @date 2022/10/25 16:00
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class BuyOrderGiftAfterSaleBatchJob extends BaseJob {


    @Autowired
    private PurchaseGiftOutProcessor purchaseGiftOutProcessor;

    @Autowired
    private PurchaseGiftOutWriter purchaseGiftOutWriter;

    @Autowired
    private BatchBuyOrderGiftOutFormProcessor buyOrderGiftOutAcceptanceFormProcessor;

    @Autowired
    private CommonFileDataWriter commonFileDataWriter;


    public Job giftBuyOrderAfterSale() {
        return jobBuilderFactory.get("giftBuyOrderAfterSale")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(giftBuyOrderAfterSaleOut())
                .next(acceptanceForm())
                .build();
    }

    /**
     * 采购赠品售后出库
     */
    private Step giftBuyOrderAfterSaleOut() {
        return stepBuilderFactory.get("采购赠品售后出库")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(giftBuyOrderOutItemReader(null,null))
                .processor(purchaseGiftOutProcessor)
                .writer(purchaseGiftOutWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step acceptanceForm() {
        return stepBuilderFactory.get("采购赠品售后出库附件推送")
                .<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(giftBuyOrderOutItemReader(null, null))
                .processor(buyOrderGiftOutAcceptanceFormProcessor)
                .writer(commonFileDataWriter)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> giftBuyOrderOutItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,6);
    }

    private CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(
            String beginTime, String endTime, Integer outInType) {
        BatchWarehouseGoodsOutInDto warehouseGoodsOutInDto = BatchWarehouseGoodsOutInDto
                .builder()
                .outInType(outInType)
                .isDelete(0)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) :DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(),"giftBuyOrderOutFindByAll", warehouseGoodsOutInDto);
    }


}

