<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags" %>

<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>商品分类管理</title>
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/pages/goods/basecategory/index.css?rnd=${resourceVersionKey}">
</head>

<body>
<div class="erp-wrap">
    <div class="erp-title" style="text-align: center">
        <div class="erp-title-txt">添加产品</div>
    </div>
    <div class="erp-block base-form search-wrap J-search-wrap">
        <form action="/order/bussinesschance/addBussinessChanceProduct.do">
            <input type="text" name="baseCategoryName" maxlength="64" autocomplete="off" class="input-text"
                   placeholder="请输入产品名称" id="baseCategoryName" value="${baseCategoryVo.baseCategoryName}">
            <input class="btn btn-small btn-blue-bd" type="submit" value="搜索">
            <div style="float: right">
                <input class="btn large btn-blue" type="button" onclick="editBussinessChanceProduct()" value="手动填写">
            </div>
        </form>
    </div>
    <div class="erp-block erp-block-list">
        <div class="list-table">
            <div class="table-th">
                <div class="th" style="width: 50%">产品名称</div>
                <div class="th" style="width: 20%">产品数量</div>
                <div class="th" style="width: 30%">操作</div>
            </div>

            <c:if test="${empty baseCategoryVos}">
                <div class="table-tr no-data">
                    <div><i class="vd-icon icon-caution1"></i></div>
                    未能搜索到与"
                    <font style="color: #55a532">
                            ${baseCategoryVo.baseCategoryName}
                    </font>
                    "相关的产品，请尝试其他关键词搜索，
                    </br>
                    该产品可能不存在，请尝试
                    <a href="/order/bussinesschance/editBussinessChanceProduct.do">
                        手动填写
                    </a>
                </div>
            </c:if>
            <c:if test="${not empty baseCategoryVos}">
                <c:forEach var="baseCategoryVo" items="${baseCategoryVos}">
                    <div class="table-tr">
                        <div class="tr-lv1 J-item-wrap hidden">
                            <div class="tr-list">
                                <div class="tr-item" style="width: 50%">
                                    <div class="tr-name-txt">
                                            ${baseCategoryVo.baseCategoryName}
                                    </div>
                                </div>
                                <div class="tr-item" style="width: 20%">
                                        ${baseCategoryVo.totalProductNum}
                                </div>
                                <div class="tr-item" style="width: 30%">
                                    <a style="cursor: pointer" onclick="choiceProduct('${baseCategoryVo.baseCategoryName}')">
                                        选择
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </c:forEach>
                <c:if test="${page.totalPage > 1}">
                    <tags:pageNew page="${page}"/>
                </c:if>
            </c:if>
        </div>
    </div>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/pages/modules/list.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/order/bussinesschance/add_bussinessChance_product.js?rnd=${resourceVersionKey}"></script>
</body>