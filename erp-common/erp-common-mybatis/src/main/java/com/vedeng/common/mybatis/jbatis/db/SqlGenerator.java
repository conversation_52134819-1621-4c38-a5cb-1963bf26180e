package com.vedeng.common.mybatis.jbatis.db;


import cn.hutool.core.util.StrUtil;
import com.vedeng.common.mybatis.jbatis.constant.OperateConstant;
import com.vedeng.common.mybatis.jbatis.utils.EntityUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * <p>Description: 动态sql拼写工具</p>
 *
 * <AUTHOR>
 * @version V1.0
 */
@Slf4j
public class SqlGenerator {


    public static String operateSqlByType(String sqlType, EntityUtil.EntityTable entityObject) {
        StringBuffer sql = new StringBuffer("<script> ");
        if (OperateConstant.SQLID_INSERT.equals(sqlType)) {
            insertTable(sql, entityObject.getName());
            if (OperateConstant.SEQ_TYPE_JDBC.equals(entityObject.getEntityClassPKColumn().getGenerator())) {
                insert(sql, entityObject.getEntityClassExceptPkColumns(), false);
            } else {
                insert(sql, entityObject.getEntityClassColumns(), false);
            }

        }
        if (OperateConstant.SQLID_INSERT_BATCH.equals(sqlType)) {
            insertTable(sql, entityObject.getName());
            if (OperateConstant.SEQ_TYPE_JDBC.equals(entityObject.getEntityClassPKColumn().getGenerator())) {
                insert(sql, entityObject.getEntityClassExceptPkColumns(), true);
            } else {
                insert(sql, entityObject.getEntityClassColumns(), true);
            }
        }
        if (OperateConstant.SQLID_UPDATE.equals(sqlType)) {
            updateTable(sql, entityObject.getName());
            update(sql, entityObject.getEntityClassExceptPkColumns());
            commonWherePk(sql, entityObject.getEntityClassPKColumn());
        }
        if (OperateConstant.SQLID_UPDATE_BATCH.equals(sqlType)) {
            updateBatchTalbe(sql, entityObject.getName());
            updateBatchTableColumns(sql, entityObject.getEntityClassColumns());
            commonListWithComma(sql, entityObject.getEntityClassColumns());
        }
        if (OperateConstant.SQLID_DELETE.equals(sqlType)) {
            deleteTable(sql, entityObject.getName());
            commonWhere(sql, entityObject.getEntityClassColumns());
        }
        if (OperateConstant.SQLID_DELETE_BATCH.equals(sqlType)) {
            deleteTable(sql, entityObject.getName());
            commonList(sql, entityObject.getEntityClassColumns());
        }
        if (OperateConstant.SQLID_COUNT_BATCH.equals(sqlType)) {
            countTable(sql, entityObject.getName());
            commonWhere(sql, entityObject.getEntityClassColumns());
        }
        if (OperateConstant.SQLID_SELECT.equals(sqlType)) {
            selectColumn(sql, entityObject.getEntityClassColumns(), entityObject.getName());
            select(sql, entityObject.getEntityClassColumns(), false, false);
        }
        if (OperateConstant.SQLID_SELECT_BATCH.equals(sqlType)) {
            selectColumn(sql, entityObject.getEntityClassColumns(), entityObject.getName());
            select(sql, entityObject.getEntityClassColumns(), true, false);
        }
        if (OperateConstant.SQLID_SELECT_CRITERIA.equals(sqlType)) {
            selectColumn(sql, entityObject.getEntityClassColumns(), entityObject.getName());
            select(sql, entityObject.getEntityClassColumns(), false, true);
        }
        if (OperateConstant.SQLID_SELECT_BATCH_CRITERIA.equals(sqlType)) {
            selectColumn(sql, entityObject.getEntityClassColumns(), entityObject.getName());
            select(sql, entityObject.getEntityClassColumns(), true, true);
        }
        if (OperateConstant.SQLID_SELECT_ONEBYID.equals(sqlType)) {
            selectColumn(sql, entityObject.getEntityClassColumns(), entityObject.getName());
            commonWherePk(sql, entityObject.getEntityClassPKColumn());
        }
        if (OperateConstant.SQLID_DELETE_ONEBYID.equals(sqlType)) {
            deleteTable(sql, entityObject.getName());
            commonWherePk(sql, entityObject.getEntityClassPKColumn());
        }
        sql.append(" </script>");
        log.info("动态sql生成:{}", sql);
        return sql.toString();
    }


    private static void insertTable(StringBuffer sql, String table) {
        sql.append("INSERT INTO `").append(table).append("` ");
    }

    private static void updateTable(StringBuffer sql, String table) {
        sql.append("UPDATE `").append(table).append("` ");
    }

    private static void deleteTable(StringBuffer sql, String table) {
        sql.append("DELETE FROM `").append(table).append("` ");
    }

    private static void updateBatchTalbe(StringBuffer sql, String table) {
        sql.append("REPLACE INTO `").append(table).append("` ");
    }

    private static void countTable(StringBuffer sql, String table) {
        sql.append("SELECT count(*) from `").append(table).append("` ");
    }


    private static void insert(StringBuffer sql, Set<EntityUtil.EntityColumn> entityClassColumns, boolean list) {
        sql.append("<trim prefix=\"(\" suffix=\")\" suffixOverrides=\",\">");
        if (entityClassColumns != null && entityClassColumns.size() != 0) {
            for (EntityUtil.EntityColumn column : entityClassColumns) {
                sql.append(StrUtil.format("<if test=\"{} != null\">{},</if>", column.getProperty(), column.getColumn()));
            }

            sql.append("</trim>");

            sql.append("<trim prefix=\"values (\" suffix=\")\" suffixOverrides=\",\">");

            if (list) {
                commonListWithComma(sql, entityClassColumns);
            } else {
                for (EntityUtil.EntityColumn column : entityClassColumns) {
                    if (StrUtil.isNotBlank(column.getJdbcType())) {
                        String jdbcProperty = StrUtil.format(" #{{},jdbcType={}}", column.getProperty(), column.getJdbcType());
                        sql.append(StrUtil.format("<if test=\"{} != null\">{},</if>", column.getProperty(), jdbcProperty));
                    } else {
                        String property = StrUtil.format(" #{{}}", column.getProperty());
                        sql.append(StrUtil.format("<if test=\"{} != null\">{},</if>", column.getProperty(), property));
                    }
                }
                sql.append("</trim>");
            }

        }
    }

    private static void selectColumn(StringBuffer sql, Set<EntityUtil.EntityColumn> entityClassColumns, String table) {
        if (entityClassColumns != null && entityClassColumns.size() != 0) {
            sql.append("SELECT ");
            for (EntityUtil.EntityColumn column : entityClassColumns) {
                sql.append(column.getColumn()).append(",");
            }
            sql.deleteCharAt(sql.length() - 1);
            sql.append(" FROM `").append(table).append("`");
        }
    }

    private static void select(StringBuffer sql, Set<EntityUtil.EntityColumn> entityClassColumns, boolean list, boolean criteria) {
        if (entityClassColumns != null && entityClassColumns.size() != 0) {

            if (list) {
                commonList(sql, entityClassColumns);
            } else {
                sql.append("<if test=\"object !=null\"><where>");
                for (EntityUtil.EntityColumn column : entityClassColumns) {
                    sql.append("<if test=\"object.");
                    if (column.getJavaType() == String.class) {
                        sql.append(column.getProperty()).append(" != null and object. ")
                                .append(column.getProperty()).append(" != ''\"> and ");
                    } else {
                        sql.append(column.getProperty()).append(" != null\"> and ");
                    }
                    sql.append(column.getColumn())
                            .append(" = #{object.")
                            .append(column.getProperty()).append("} </if>");
                }
                sql.append("</where></if>");
            }

            if (criteria) {
                sql.append("<if test=\"orderby != null\"> order by  ${orderby}</if>");
                sql.append("<if test=\"offset !=null and limit !=null\">limit #{offset},#{limit} </if>");
            }
        }
    }

    private static void update(StringBuffer sql, Set<EntityUtil.EntityColumn> entityClassColumns) {
        final String updateTpl = "<if test=\"{} != null\"> {} = #{ {},jdbcType={} },</if>" ;
        final String updateTpl1 = "<if test=\"{} != null\"> {} = #{ {} },</if>" ;
        sql.append("<set> ");
        for (EntityUtil.EntityColumn column : entityClassColumns) {
            if (StrUtil.isNotBlank(column.getJdbcType())) {
                sql.append(StrUtil.format(updateTpl, column.getProperty(), column.getColumn(),column.getProperty(), column.getJdbcType()));
            } else {
                sql.append(StrUtil.format(updateTpl1, column.getProperty(), column.getColumn(), column.getProperty()));
            }
        }
        sql.append("</set>");
    }

    private static void updateBatchTableColumns(StringBuffer sql, Set<EntityUtil.EntityColumn> entityClassColumns) {
        sql.append("(");
        if (entityClassColumns != null && entityClassColumns.size() != 0) {
            for (EntityUtil.EntityColumn column : entityClassColumns) {
                sql.append(column.getColumn()).append(",");
            }

            sql.deleteCharAt(sql.length() - 1);
            sql.append(") values");
        }
    }

    private static void commonWhere(StringBuffer sql, Set<EntityUtil.EntityColumn> entityClassColumns) {
        sql.append("<where> 1=1 ");
        if (entityClassColumns != null && entityClassColumns.size() != 0) {
            for (EntityUtil.EntityColumn column : entityClassColumns) {
                sql.append("<if test=\"").append(column.getProperty()).append(" != null\"> and ").append(column.getColumn()).append(" = #{").append(column.getProperty()).append("} </if>");
            }
        }
        sql.append(" </where>");
    }

    private static void commonWherePk(StringBuffer sql, EntityUtil.EntityColumn entityClassPKColumn) {
        sql.append("where ");
        sql.append(entityClassPKColumn.getColumn()).append(" = #{").append(entityClassPKColumn.getProperty()).append("}");
    }

    private static void commonList(StringBuffer sql, Set<EntityUtil.EntityColumn> entityClassColumns) {
        if (entityClassColumns != null && entityClassColumns.size() != 0) {
            sql.append("<if test=\"list !=null\"><where><foreach collection=\"list\" item=\"item\" index=\"index\" separator=\"or\">");
            sql.append("(<trim suffix=\"\" prefixOverrides=\"AND\" >");
            for (EntityUtil.EntityColumn column : entityClassColumns) {
                sql.append("<if test=\"item.").append(column.getProperty()).append(" != null\"> and ").append(column.getColumn()).append(" = #{item.").append(column.getProperty()).append("} </if>");
            }
            sql.append("</trim>)</foreach></where></if>");
        }
    }

    private static void commonListWithComma(StringBuffer sql, Set<EntityUtil.EntityColumn> entityClassColumns) {
        sql.append(" <foreach collection=\"list\" item=\"item\" index=\"index\" separator=\",\">(");
        for (EntityUtil.EntityColumn column : entityClassColumns) {
            sql.append("#{item.").append(column.getProperty()).append("},");
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(") </foreach>");
    }

}
