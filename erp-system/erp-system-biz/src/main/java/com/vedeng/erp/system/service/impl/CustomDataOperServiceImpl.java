package com.vedeng.erp.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.system.dto.CustomDataOperDto;
import com.vedeng.erp.system.mapper.CustomDataOperMapper;
import com.vedeng.erp.system.mapstruct.CustomDataOperConvertor;
import com.vedeng.erp.system.service.CustomDataOperApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 自定义操作service
 * @date 2022/7/15 12:51
 */
@Service
@Slf4j
public class CustomDataOperServiceImpl implements CustomDataOperApiService {


    @Autowired
    private CustomDataOperMapper customDataOperMapper;

    @Autowired
    private CustomDataOperConvertor customDataOperConvertor;


    @Override
    public void save(CustomDataOperDto customDataOperDto) {
        CustomDataOperDto dto = new CustomDataOperDto();
        dto.setOperType(customDataOperDto.getOperType());
        dto.setBelongerId(customDataOperDto.getBelongerId());
        dto.setBizType(customDataOperDto.getBizType());
        dto.setRelatedId(customDataOperDto.getRelatedId());
        List<CustomDataOperDto> byAll = customDataOperMapper.findByAll(dto);
        if (CollUtil.isEmpty(byAll)) {
            customDataOperDto.setAddTime(new Date());
            customDataOperDto.setModTime(new Date());
            customDataOperMapper.insert(customDataOperConvertor.toEntity(customDataOperDto));
        }
    }

    @Override
    public List<CustomDataOperDto> query(CustomDataOperDto customDataOperDto) {
        CustomDataOperDto dto = new CustomDataOperDto();
        dto.setOperType(customDataOperDto.getOperType());
        dto.setBelongerId(customDataOperDto.getBelongerId());
        dto.setBizType(customDataOperDto.getBizType());
        dto.setRelatedId(customDataOperDto.getRelatedId());
        List<CustomDataOperDto> list = customDataOperMapper.findByAll(dto);
        return list;
    }

    @Override
    public void delete(CustomDataOperDto customDataOperDto) {
        List<CustomDataOperDto> byAll = customDataOperMapper.findByAll(customDataOperDto);
        if (CollUtil.isNotEmpty(byAll)) {
            byAll.forEach(dto -> customDataOperMapper.deleteByPrimaryKey(dto.getId()));
        }
    }
}
