package com.vedeng.goods.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 业务单据与商品分类的关联
 */
@Getter
@Setter
public class BusinessOrderCategoryEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商机/线索ID
     */
    private Integer businessId;

    /**
     * 业务类型 0线索 1商机
     */
    private Integer businessType;

    /**
     * ai 分析关键词
     */
    private String keywords;

    /**
     * 商品分类ID
     */
    private Integer categoryId;
}