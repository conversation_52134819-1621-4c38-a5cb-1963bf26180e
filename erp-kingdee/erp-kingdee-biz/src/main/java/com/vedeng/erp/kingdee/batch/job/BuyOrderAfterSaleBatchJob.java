package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchVirtualInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.processor.*;
import com.vedeng.erp.kingdee.batch.writer.*;
import com.vedeng.erp.kingdee.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.item.ItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购单售后退货退票 job
 * @date 2022/10/25 16:00
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class BuyOrderAfterSaleBatchJob extends BaseJob {

    @Autowired
    private PurchaseBackProcessor purchaseBackProcessor;
    @Autowired
    private PurchaseBackWriter purchaseBackWriter;
    @Autowired
    private BatchPurchaseBackInvoiceWriter batchPurchaseBackInvoiceWriter;
    @Autowired
    private BatchPurchaseReversalInvoiceProcessor batchPurchaseReversalInvoiceProcessor;
    @Autowired
    private BatchPurchaseReversalInvoiceWriter batchPurchaseReversalInvoiceWriter;
    @Autowired
    private BatchPurchaseBackInvoiceVatProcessor batchPurchaseBackInvoiceVatProcessor;
    @Autowired
    private BatchPurchaseBackInvoicePayCommonProcessor batchPurchaseBackInvoicePayCommonProcessor;
    @Autowired
    private BatchPurchaseInvoicePayCommonWriter batchPurchaseInvoicePayCommonWriter;
    @Autowired
    private BatchBuyOrderBackAcceptanceFormProcessor batchBuyOrderBackAcceptanceFormProcessor;
    @Autowired
    private CommonFileDataWriter commonFileDataWriter;

    @Autowired
    private BatchBuyOrderBackVirtualInvoicePayCommonProcessor batchBuyOrderBackVirtualInvoicePayCommonProcessor;

    @Autowired
    private BatchBuyOrderBackVirtualInvoiceVatProcessor batchBuyOrderBackVirtualInvoiceVatProcessor;


    public Job buyOrderAfterSaleFlowJob() {
        return jobBuilderFactory.get("buyOrderAfterSaleFlowJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(purchaseBack())
                .next(acceptanceOutForm())
                .next(redInvoicePayCommon())
                .next(redInvoice())
                .next(reversalInvoice())
                .next(redInvoicePayCommon())
                .next(redVirtualInvoicePayCommon())
                .next(redVirtualInvoice())
                .build();
    }

    public Job buyOrderAfterOnlyOutJob() {
        return jobBuilderFactory.get("采购仅推送出库")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(purchaseBack())
                .build();
    }

    public Job buyOrderAfterOneReversalInvoiceJob() {
        return jobBuilderFactory.get("采购第一次执行冲销")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(reversalInvoice())
                .build();
    }


    /**
     * 附件
     * @return
     */
    private Step acceptanceOutForm() {
        return stepBuilderFactory.get("采购售后退货出库验收报告")
                .<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchWarehouseGoodsOutInDtoItemReader(null, null))
                .processor(batchBuyOrderBackAcceptanceFormProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 采购售后退货出库
     * @return
     */
    private Step purchaseBack() {
        return stepBuilderFactory.get("采购售后退货出库")
                .<BatchWarehouseGoodsOutInDto, KingDeePurchaseBackDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchWarehouseGoodsOutInDtoItemReader(null,null))
                .processor(purchaseBackProcessor)
                .writer(purchaseBackWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 采购退货退票红票应付单
     * @return
     */
    private Step redInvoicePayCommon() {
        return stepBuilderFactory.get("采购售后退货红票应付单")
                .<BatchInvoiceDto, KingDeePayCommonDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchBatchBackRedInvoiceDtoItemReader(null,null))
                .processor(batchPurchaseBackInvoicePayCommonProcessor)
                .writer(batchPurchaseInvoicePayCommonWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step redVirtualInvoicePayCommon() {
        return stepBuilderFactory.get("采购售后退货虚拟红票应付单")
                .<BatchVirtualInvoiceDto, KingDeePayCommonDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(virtualInvoiceAfterRedBindReader(null,null))
                .processor(batchBuyOrderBackVirtualInvoicePayCommonProcessor)
                .writer(batchPurchaseInvoicePayCommonWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 采购退货红票
     */
    private Step redInvoice() {
        return stepBuilderFactory.get("采购售后退货红票")
                .<BatchInvoiceDto, KingDeePayCommonAndInvoiceDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchBatchBackRedInvoiceDtoItemReader(null,null))
                .processor(batchPurchaseBackInvoiceVatProcessor)
                .writer(batchPurchaseBackInvoiceWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step redVirtualInvoice() {
        return stepBuilderFactory.get("采购售后退货虚拟红票")
                .<BatchVirtualInvoiceDto, KingDeePayCommonAndInvoiceDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(virtualInvoiceAfterRedBindReader(null,null))
                .processor(batchBuyOrderBackVirtualInvoiceVatProcessor)
                .writer(batchPurchaseBackInvoiceWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 采购退货退票冲销票
     */
    private Step reversalInvoice() {
        return stepBuilderFactory.get("采购售后退货冲销票")
                .<BatchInvoiceDto, KingDeeInvoiceRollBackDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchBatchReversalInvoiceDtoItemReader(null,null))
                .processor(batchPurchaseReversalInvoiceProcessor)
                .writer(batchPurchaseReversalInvoiceWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

        /**
         * 出库
         */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchVirtualInvoiceDto> virtualInvoiceAfterRedBindReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime
    ) {
        BatchVirtualInvoiceDto query = BatchVirtualInvoiceDto.builder()
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .colorType(1)
                .businessType(2)
//                .virtualInvoiceId(82)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchVirtualInvoiceDto.class.getSimpleName(),"findRedByAll", query);
    }


    /**
     * 出库
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchWarehouseGoodsOutInDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        BatchWarehouseGoodsOutInDto warehouseGoodsOutInDto = BatchWarehouseGoodsOutInDto
                .builder()
                //6采购退货出库
                .outInTypeList(CollUtil.newArrayList(6))
                .isDelete(0)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()): DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()): DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), "buyOrderOutFindByAll",warehouseGoodsOutInDto);
    }

    /**
     * 退票
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> batchBatchBackRedInvoiceDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 红字有效
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 采购开票
                .type(503)
                // 采购退货 的财务确认退票
                .isAfterBuyorderOnly(0)
                // 红票退票
                .colorComplementType(0)
                // 通过时间是当天的
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime(): DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime(): DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(),"buyorderRedEnableInvoicefindByAll", batchInvoiceDto);
    }

    /**
     * 冲销票
     */
    @Bean
    @StepScope
    public ItemReader<? extends BatchInvoiceDto> batchBatchReversalInvoiceDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {

        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 红字有效
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 采购开票
                .type(503)
                // 红票冲销票
                .colorComplementType(1)
                // 通过时间是当天的
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime(): DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime(): DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(),"buyorderRedEnableInvoicefindByAll", batchInvoiceDto);

    }

}

