package com.vedeng.trader.model.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 账期列表详情
 */
@Data
public class CustomerBillPeriodItemDto {
    /**
     * 客户账期ID
     */
    private Long billPeriodId;

    /**
     * 客户账期使用明细ID
     */
    private Long billPeriodUseDetailId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 账期类型，@CustomerBillPeriodTypeEnum
     */
    private Integer billPeriodType;

    /**
     * 账期额度
     */
    private BigDecimal creditTotalAmount;

    /**
     * 账期可用额(针对账期)
     */
    private BigDecimal creditUsableAmount;

    /**
     * 账期使用额度(针对使用记录)
     */
    private BigDecimal creditUseAmount;

    /**
     * 账期占用额(针对使用记录)
     */
    private BigDecimal occupyAmount;

    /**
     * 冻结金额(针对使用记录)
     */
    private BigDecimal freezeAmount;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 未还账期金额
     */
    private BigDecimal unReturnedAmount;

    /**
     * 逾期金额
     */
    /*private BigDecimal overDueAmount;*/
    private BigDecimal unReturnedOverDueAmount;

    /**
     * 逾期次数
     */
    private Integer countOfOverDue;

    /**
     * 逾期天数
     */
    private Integer daysOfOverdue;

    /**
     * 已归还账期额
     */
    private BigDecimal returnedAmount;

    /**
     * 未监管账期额
     */
    private BigDecimal unSuperViseAmount;

    /**
     * 逾期状态 @OverdueStateEnum
     */
    private Integer overdueState;

    /**
     * 账期有效期截止时间
     */
    private Long billPeriodEnd;
}
