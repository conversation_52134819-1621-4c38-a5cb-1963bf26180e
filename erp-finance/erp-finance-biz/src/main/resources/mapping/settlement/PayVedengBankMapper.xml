<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.settlement.mapper.PayVedengBankMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.settlement.domain.entity.PayVedengBankEntity">
    <!--@mbg.generated-->
    <!--@Table T_PAY_VEDENG_BANK-->
    <id column="PAY_VEDENG_BANK_ID" jdbcType="INTEGER" property="payVedengBankId" />
    <result column="PAY_BANK_NAME" jdbcType="VARCHAR" property="payBankName" />
    <result column="PAY_BANK_NO" jdbcType="VARCHAR" property="payBankNo" />
    <result column="KING_DEE_BANK_CODE" jdbcType="VARCHAR" property="kingDeeBankCode" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PAY_VEDENG_BANK_ID, PAY_BANK_NAME, PAY_BANK_NO, IS_DELETE,KING_DEE_BANK_CODE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_PAY_VEDENG_BANK
    where PAY_VEDENG_BANK_ID = #{payVedengBankId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_PAY_VEDENG_BANK
    where PAY_VEDENG_BANK_ID = #{payVedengBankId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="PAY_VEDENG_BANK_ID" keyProperty="payVedengBankId" parameterType="com.vedeng.erp.settlement.domain.entity.PayVedengBankEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_PAY_VEDENG_BANK (PAY_BANK_NAME, PAY_BANK_NO, IS_DELETE,KING_DEE_BANK_CODE
      )
    values (#{payBankName,jdbcType=VARCHAR}, #{payBankNo,jdbcType=VARCHAR}, #{isDelete,jdbcType=TINYINT},#{kingDeeBankCode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="PAY_VEDENG_BANK_ID" keyProperty="payVedengBankId" parameterType="com.vedeng.erp.settlement.domain.entity.PayVedengBankEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_PAY_VEDENG_BANK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="payBankName != null">
        PAY_BANK_NAME,
      </if>
      <if test="payBankNo != null">
        PAY_BANK_NO,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="kingDeeBankCode != null">
        KING_DEE_BANK_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="payBankName != null">
        #{payBankName,jdbcType=VARCHAR},
      </if>
      <if test="payBankNo != null">
        #{payBankNo,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="kingDeeBankCode != null">
        #{kingDeeBankCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.settlement.domain.entity.PayVedengBankEntity">
    <!--@mbg.generated-->
    update T_PAY_VEDENG_BANK
    <set>
      <if test="payBankName != null">
        PAY_BANK_NAME = #{payBankName,jdbcType=VARCHAR},
      </if>
      <if test="payBankNo != null">
        PAY_BANK_NO = #{payBankNo,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="kingDeeBankCode != null">
        KING_DEE_BANK_CODE = #{kingDeeBankCode,jdbcType=VARCHAR}
      </if>
    </set>
    where PAY_VEDENG_BANK_ID = #{payVedengBankId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.settlement.domain.entity.PayVedengBankEntity">
    <!--@mbg.generated-->
    update T_PAY_VEDENG_BANK
    set PAY_BANK_NAME = #{payBankName,jdbcType=VARCHAR},
      PAY_BANK_NO = #{payBankNo,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=TINYINT}
    where PAY_VEDENG_BANK_ID = #{payVedengBankId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="PAY_VEDENG_BANK_ID" keyProperty="payVedengBankId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_PAY_VEDENG_BANK
    (PAY_BANK_NAME, PAY_BANK_NO, IS_DELETE,KING_DEE_BANK_CODE)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.payBankName,jdbcType=VARCHAR}, #{item.payBankNo,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=TINYINT},#{item.kingDeeBankCode,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <select id="queryInfoByPayVedengBankId" resultType="com.vedeng.erp.finance.dto.PayVedengBankDto">
    SELECT * FROM T_PAY_VEDENG_BANK
    WHERE PAY_VEDENG_BANK_ID = #{payVedengBankId,jdbcType=INTEGER}
  </select>

  <select id="queryInfoByPayBankNo" resultType="com.vedeng.erp.finance.dto.PayVedengBankDto">
    SELECT * FROM T_PAY_VEDENG_BANK
    WHERE PAY_BANK_NO = #{payBankNo,jdbcType=VARCHAR} limit 1
  </select>

  <select id="getKingDeeBankCodeByBankNo" resultType="java.lang.String">
    select KING_DEE_BANK_CODE from T_PAY_VEDENG_BANK where PAY_BANK_NO = #{payBankNo,jdbcType=VARCHAR}
    and IS_DELETE = 0
    </select>

  <select id="queryPayBankByBuyOrder" resultType="com.vedeng.erp.finance.dto.PayApplyPayBankResultDto">
      select a.PAY_APPLY_ID,b.BUYORDER_ID,b.BUYORDER_NO,c.PAY_VEDENG_BANK_ID,c.PAY_BANK_NAME,c.PAY_BANK_NO,c.KING_DEE_BANK_CODE
      from T_PAY_APPLY a
             left join T_BUYORDER b on a.RELATED_ID = b.BUYORDER_ID and a.PAY_TYPE = 517
             left join T_PAY_VEDENG_BANK c on a.PAY_BANKTYPE_ID = c.PAY_VEDENG_BANK_ID
      where a.VALID_STATUS = 1
        and c.IS_DELETE = 0
        and b.BUYORDER_NO = #{buyOrderNo,jdbcType=VARCHAR}
      order by PAY_BANKTYPE_ID desc
  </select>
</mapper>