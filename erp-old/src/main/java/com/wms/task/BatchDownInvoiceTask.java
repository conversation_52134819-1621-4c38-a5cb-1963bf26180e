package com.wms.task;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.putHCutil.service.HcSaleorderService;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.model.InvoiceApply;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.soap.service.VedengSoapService;
import com.wms.constant.PictureTypeEnum;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInvoiceFlagEnum;
import com.wms.dto.*;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.wms.service.util.WmsCommonUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量下载并拉去发票信息
 *
 * <AUTHOR>
 * @date 2020/8/19 17:33:20
 */


@Component
@JobHandler(value = "batchDownInvoiceTask")
public class BatchDownInvoiceTask extends AbstractJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(BatchDownInvoiceTask.class);

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private HcSaleorderService hcSaleorderService;

    @Autowired
    private VedengSoapService vedengSoapService;

    @Autowired
    private SaleorderService saleorderService;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Value("${invoice_goods_orderType}")
    private String invoiceGoodsOrderType;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("BatchDownInvoiceTask-JOB, START");
        batchDownInvoice();
        return SUCCESS;
    }

    public void batchDownInvoice() {
        logger.info("--- 下载电子发票 START --");

        Invoice invoice = new Invoice();
        invoice.setCompanyId(1);
        invoice.setType(SysOptionConstant.ID_505);
        invoice.setInvoiceGoodsPeerFlag(1);
        invoice.setInvoiceGoodsOrderTypes(JSON.parseArray(invoiceGoodsOrderType, Integer.class));
        ResultInfo<?> resultInfo = invoiceService.batchDownEInvoice(invoice);
        logger.info("检索票货同行需要下载的发票信息 resultInfo:{}" + resultInfo.toString());
        if (resultInfo != null && resultInfo.getData() != null) {
            List<Integer> invoiceIdList = (List<Integer>) resultInfo.getData();
            if (CollectionUtils.isNotEmpty(invoiceIdList)) {
                logger.info("开始检查符合票货同行的发票任务 invoiceIdList" + invoiceIdList.toString());
                excutePushInvoiceInfo(invoiceIdList);
            } else {
                logger.info("暂无需要处理的票货同行发票");
            }
        }
        if (resultInfo != null && resultInfo.getCode() == -1) {
            logger.info("票货同行任务检索结果: resultInfo:{}" + resultInfo.getMessage());
        }

        logger.info("--- 下载电子发票 END ---");

        // 处理本地票货同行信息 + 数电发票的 处理下数电的 需要推送 前台的
        List<WmsInvoiceInfoDto> wmsInvoiceInfoDtos = invoiceService.getWmsInvoiceInfoDtos(Collections.singletonList(Integer.valueOf(5)));
        if (CollUtil.isNotEmpty(wmsInvoiceInfoDtos)) {
            List<Integer> collect = wmsInvoiceInfoDtos.stream()
                    .map(WmsInvoiceInfoDto::getInvoiceList)
                    .filter(CollUtil::isNotEmpty)
                    .map(x -> x.stream().map(Invoice::getInvoiceId).collect(Collectors.toList()))
                    .flatMap(List::stream).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                excutePushInvoiceInfo(collect);
            }
        }
        dealLocalInvoiceInfo();
    }


    /**
     * 异步推送发票信息给前台
     *
     * @param invoiceIdList
     */
    @Async
    protected void excutePushInvoiceInfo(List<Integer> invoiceIdList) {
        logger.info("开始处理票货同行发票信息任务 invoiceIdList:{}" + JSON.toJSONString(invoiceIdList));
        // 根据发票ID查询订单列表，确定是否是耗材商城的销售订单
        List<Saleorder> saleorderList = saleorderService.getHcSaleorderInfoByInvoiceIds(invoiceIdList);
        if (CollectionUtils.isNotEmpty(saleorderList)) {
            saleorderList.stream().forEach(saleorder -> {
                // 获取该订单下的发票列表
                List<Invoice> invoiceList = invoiceService.getInvoiceInfoByRelatedId(saleorder.getSaleorderId(), SysOptionConstant.ID_505);
                // 实际要推送的发票列表
                ArrayList<Invoice> invoiceLastList = new ArrayList<>();
                invoiceList.stream().forEach(invoice -> {
                    // 电子发票下载连接不为空
                    if (StringUtil.isNotBlank(invoice.getResourceId())) {
                        invoiceLastList.add(invoice);
                    }
                });
                logger.info("本地订单下载开票信息的结果 invoiceLastList:{}" + JSON.toJSONString(invoiceLastList));

                //以开票申请作为一个维度检索是否开票完毕
                HashMap<Integer, ArrayList<Invoice>> invoiceApplyMap = new HashMap<>(16);
                HashMap<Integer, InvoiceApply> invoiceApplyBaseInfoMap = new HashMap<>(16);
                if (CollectionUtils.isEmpty(invoiceList)) {
                    logger.error("订单中的发票信息异常 saleorderId" + saleorder.getSaleorderId());
                    return;
                }
                invoiceList.stream().forEach(invoice -> {
                    InvoiceApply invoiceApply = invoiceService.getInvoiceApplyInfoByInvoiceId(invoice.getInvoiceId());
                    invoiceApplyBaseInfoMap.put(invoiceApply.getInvoiceApplyId(), invoiceApply);
                    if (invoiceApplyMap.containsKey(invoiceApply.getInvoiceApplyId())) {
                        invoiceApplyMap.get(invoiceApply.getInvoiceApplyId()).add(invoice);
                    } else {
                        ArrayList<Invoice> invoices = new ArrayList<>();
                        invoices.add(invoice);
                        invoiceApplyMap.put(invoiceApply.getInvoiceApplyId(), invoices);
                    }
                });

                for (Map.Entry<Integer, ArrayList<Invoice>> integerArrayListEntry : invoiceApplyMap.entrySet()) {
                    if (invoiceApplyBaseInfoMap.get(integerArrayListEntry.getKey()).getIsSendWms() == 1) {
                        continue;
                    }
                    Boolean isComplete = true;
                    for (Invoice invoice : integerArrayListEntry.getValue()) {
                        if (StringUtil.isBlank(invoice.getResourceId())) {
                            isComplete = false;
                            break;
                        }
                    }
                    //INVO/PATH 返回开票结果&&下载路径 || INVO 返回正在开票信息给WMS
                    if (isComplete) {
                        logger.info("票货同行全部开票成功 准备推送发票图片至WMS invoiceApplyId:{}" + integerArrayListEntry.getKey().toString());
                        Boolean isWmsHandleSuccess = putPathInfo2Wms(invoiceApplyBaseInfoMap, integerArrayListEntry, true);

                        logger.info("票货同行全部开票成功 准备推送至WMS invoiceApplyId:{}" + integerArrayListEntry.getKey().toString());
                        isWmsHandleSuccess = putInvoiceInfo2Wms(invoiceApplyBaseInfoMap, integerArrayListEntry, WmsInvoiceFlagEnum.ALL_COMPLETE, "开票成功", isWmsHandleSuccess);
                        if (isWmsHandleSuccess) {
                            invoiceService.signIsSendWmsFlag(integerArrayListEntry.getKey());
                        }
                    } else {
                        logger.info("票货同行存在正在开票中 准备推送至WMS invoiceApplyId:{}" + integerArrayListEntry.getKey().toString());
                        putInvoiceInfo2Wms(invoiceApplyBaseInfoMap, integerArrayListEntry, WmsInvoiceFlagEnum.OPENING_INVOICE, "正在开票中", true);
                    }
                }


                //发票信息推送给医械购前台
                if (CollectionUtils.isNotEmpty(invoiceLastList)) {
                    HashMap<String, Object> map = new HashMap<>(16);
                    map.put("saleOrder", saleorder);
                    map.put("invoiceList", invoiceList);
                    hcSaleorderService.putInvoicetoHC(map);
                }
            });
        }
        logger.info("---发票推送前台 end---");
    }

    /**
     * PATH 向WMS推送发票图片路径信息
     *
     * @param invoiceApplyBaseInfoMap
     * @param integerArrayListEntry
     * @param isWmsHandleSuccess
     */
    private Boolean putPathInfo2Wms(HashMap<Integer, InvoiceApply> invoiceApplyBaseInfoMap,
                                    Map.Entry<Integer, ArrayList<Invoice>> integerArrayListEntry, Boolean isWmsHandleSuccess) {
        PutPathDto putPathDto = new PutPathDto();
        putPathDto.setSOReference1(WmsCommonUtil.addTimestampForOrderNo(invoiceApplyBaseInfoMap.get(integerArrayListEntry.getKey()).getErpOrderNo()));
        putPathDto.setOrderNo(invoiceApplyBaseInfoMap.get(integerArrayListEntry.getKey()).getWmsOrderNo());
        ArrayList<PutPathDetailDto> details = new ArrayList<>();
        integerArrayListEntry.getValue().stream().forEach(invoice -> {
            PutPathDetailDto putPathDetailDto = new PutPathDetailDto();
            putPathDetailDto.setSOReference1(WmsCommonUtil.addTimestampForOrderNo(invoiceApplyBaseInfoMap.get(integerArrayListEntry.getKey()).getErpOrderNo()));
            putPathDetailDto.setOrderNo(invoiceApplyBaseInfoMap.get(integerArrayListEntry.getKey()).getWmsOrderNo());
            putPathDetailDto.setOrderlineNo(invoice.getInvoiceId().toString());
            putPathDetailDto.setPicture_path(WmsCommonUtil.addInvoiceFileUrl(invoice.getResourceId()));
            putPathDetailDto.setPicture_type(PictureTypeEnum.INVOICE_TYPE.getCode());
            details.add(putPathDetailDto);
        });
        putPathDto.setDetails(details);
        WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_PATH_DATA);

        if (StringUtil.isBlank(putPathDto.getSOReference1()) || StringUtil.isBlank(putPathDto.getOrderNo())){
            logger.info("发票为非出库回传开取的发票 无需下传 putPathDto:", JSON.toJSONString(putPathDto));
            return false;
        }
        try {
            logger.info("向WMS推送发票图片路径信息:{}" + JSON.toJSONString(putPathDto));
            WmsResponse response = wmsInterface.request(putPathDto);
            if (response.getReturnFlag().equals(0)) {
                isWmsHandleSuccess = false;
                logger.error("向WMS推送发票图片路径信息失败 orderNo:{}" + putPathDto.getSOReference1());
            }
        } catch (Exception e) {
            isWmsHandleSuccess = false;
            logger.error("下传发票至WMS error putPathDto:{}" + putPathDto.toString(), e);
        }
        return isWmsHandleSuccess;
    }

    /**
     * INVO 向WMS推送开票结果信息
     *
     * @param invoiceApplyBaseInfoMap
     * @param integerArrayListEntry
     * @param wmsInvoiceFlagEnum
     * @param isWmsHandleSuccess
     * @param result
     */
    private Boolean putInvoiceInfo2Wms(HashMap<Integer, InvoiceApply> invoiceApplyBaseInfoMap, Map.Entry<Integer, ArrayList<Invoice>> integerArrayListEntry,
                                       WmsInvoiceFlagEnum wmsInvoiceFlagEnum, String result, Boolean isWmsHandleSuccess) {
        WmsInvoiceDto wmsInvoiceDto = new WmsInvoiceDto();
        wmsInvoiceDto.setSOReference1(WmsCommonUtil.addTimestampForOrderNo(invoiceApplyBaseInfoMap.get(integerArrayListEntry.getKey()).getErpOrderNo()));
        wmsInvoiceDto.setOrderNo(invoiceApplyBaseInfoMap.get(integerArrayListEntry.getKey()).getWmsOrderNo());
        wmsInvoiceDto.setInvoice_Flag(wmsInvoiceFlagEnum.getCode());
        wmsInvoiceDto.setUserDefine1(integerArrayListEntry.getKey().toString());
        wmsInvoiceDto.setUserDefine2(result);

        if (StringUtil.isBlank(wmsInvoiceDto.getSOReference1()) || StringUtil.isBlank(wmsInvoiceDto.getOrderNo())){
            logger.info("发票申请为非出库回传而开取的发票 无需下传 invoiceApplyId:", wmsInvoiceDto.getUserDefine2());
            return false;
        }
        try {
            logger.info("ERP下传是否开发票接口开始  wmsInvoiceDto:{}" + JSON.toJSONString(wmsInvoiceDto));
            WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_INVOICE_DATA);
            WmsResponse response = wmsInterface.request(wmsInvoiceDto);
            if (response.getReturnFlag().equals(0)) {
                isWmsHandleSuccess = false;
                logger.error("ERP下传是否开发票接口失败,orderNo:{}" + wmsInvoiceDto.getSOReference1() + ",失败原因:{}" + response.getReturnDesc());
            }
        } catch (Exception e) {
            logger.error("下传开票信息error invoiceApplyId:{}" + integerArrayListEntry.getKey().toString(), e);
        }
        return isWmsHandleSuccess;
    }

    /**
     * 处理本地已经下载但未推送的票货同行发票
     */
    private void dealLocalInvoiceInfo() {
        logger.info("-- 检索已经下载本地未推送的票货同行发票 START --");
        List<WmsInvoiceInfoDto> wmsInvoiceInfoDtos = invoiceService.getWmsInvoiceInfoDtos(
                JSON.parseArray(invoiceGoodsOrderType, Integer.class)
        );
        logger.info("本地未推送的票货同行数据 wmsInvoiceInfoDtos:{}" + JSON.toJSONString(wmsInvoiceInfoDtos));
        if (CollectionUtils.isEmpty(wmsInvoiceInfoDtos)) {
            logger.info("本次无满足票货同行并且未推送的发票");
            return;
        }
        wmsInvoiceInfoDtos.stream().forEach(wmsInvoiceInfoDto -> {
            if (CollectionUtils.isEmpty(wmsInvoiceInfoDto.getInvoiceList())) {
                logger.info("发票申请ID下无发票信息 applyId:{}" + wmsInvoiceInfoDto.getInvoiceApplyId());
                return;
            }

            Boolean isAllCpmplete = true;
            Boolean isWmsHandleSuccess = true;
            ArrayList<PutPathDetailDto> details = new ArrayList<>();

            for (Invoice invoiceInfo : wmsInvoiceInfoDto.getInvoiceList()) {
                if (StringUtil.isBlank(invoiceInfo.getResourceId())) {
                    isAllCpmplete = false;
                    break;
                }
                PutPathDetailDto putPathDetailDto = new PutPathDetailDto();
                putPathDetailDto.setSOReference1(WmsCommonUtil.addTimestampForOrderNo(wmsInvoiceInfoDto.getErpOrderNo()));
                putPathDetailDto.setOrderNo(wmsInvoiceInfoDto.getWmsOrderNo());
                putPathDetailDto.setOrderlineNo(invoiceInfo.getInvoiceId().toString());
                putPathDetailDto.setPicture_type(PictureTypeEnum.INVOICE_TYPE.getCode());
                putPathDetailDto.setPicture_path(WmsCommonUtil.addInvoiceFileUrl(invoiceInfo.getResourceId()));
                details.add(putPathDetailDto);
            }

            if (isAllCpmplete) {
                WmsInvoiceDto wmsInvoiceDto = new WmsInvoiceDto();
                wmsInvoiceDto.setSOReference1(WmsCommonUtil.addTimestampForOrderNo(wmsInvoiceInfoDto.getErpOrderNo()));
                wmsInvoiceDto.setOrderNo(wmsInvoiceInfoDto.getWmsOrderNo());
                wmsInvoiceDto.setInvoice_Flag(WmsInvoiceFlagEnum.ALL_COMPLETE.getCode());
                wmsInvoiceDto.setUserDefine1(wmsInvoiceInfoDto.getInvoiceApplyId().toString());
                wmsInvoiceDto.setUserDefine2("");
                try {
                    logger.info("ERP下传是否开发票接口开始  wmsInvoiceDto:{}" + JSON.toJSONString(wmsInvoiceDto));
                    WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_INVOICE_DATA);
                    WmsResponse pushInvoiceResponse = wmsInterface.request(wmsInvoiceDto);
                    if (pushInvoiceResponse != null && pushInvoiceResponse.getReturnFlag().equals(0)) {
                        isWmsHandleSuccess = false;
                        logger.error("ERP下传是否开发票接口失败,invoiceApplyId:{}" + wmsInvoiceDto.getUserDefine1() +
                                ",失败原因:{}" + pushInvoiceResponse.getReturnDesc());
                    }
                } catch (Exception e) {
                    logger.error("下传开票信息error invoiceApplyId:{}" + wmsInvoiceInfoDto.getInvoiceApplyId(), e);
                }

                PutPathDto putPathDto = new PutPathDto();
                try {
                    putPathDto.setSOReference1(WmsCommonUtil.addTimestampForOrderNo(wmsInvoiceInfoDto.getErpOrderNo()));
                    putPathDto.setOrderNo(wmsInvoiceInfoDto.getWmsOrderNo());
                    putPathDto.setDetails(details);
                    WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_PATH_DATA);
                    logger.info("开始推送发票信息数据putPathDto:{}" + JSON.toJSONString(putPathDto));
                    WmsResponse pushInvoResponse = wmsInterface.request(putPathDto);
                    if (pushInvoResponse.getReturnFlag().equals(0)) {
                        isWmsHandleSuccess = false;
                        logger.error("推送发票信息数据失败,orderNo" + putPathDto.getSOReference1() +
                                ",失败原因:{}" + pushInvoResponse.getReturnDesc());
                    }
                } catch (Exception e) {
                    logger.error("下传发票至WMS error putPathDto:{}" + JSON.toJSONString(putPathDto), e);
                }
                if (isWmsHandleSuccess) {
                    logger.info("标记发票信息为已推送，发票申请ID:{}" + wmsInvoiceInfoDto.getInvoiceApplyId());
                    invoiceService.signIsSendWmsFlag(wmsInvoiceInfoDto.getInvoiceApplyId());
                }
            }
        });
        logger.info("-- 检索已经下载本地未推送的票货同行发票 END --");
    }
}
