package com.wms.model.po;

/**
 * <AUTHOR>
 * @ClassName WmsSurplusInOrder.java
 * @Description TODO
 * @createTime 2020年09月21日 16:10:00
 */
public class WmsSurplusInOrder extends WmsInputOrder{

    private String skuNo;

    private String brandName;

    private String goodsName;

    private String model;

    private Integer countSku;

    private Integer inputNum;

    private String unitName;

    private String verifyStatusStr;

    private String arrivalStatusStr;

    public Integer getCountSku() {
        return countSku;
    }

    public void setCountSku(Integer countSku) {
        this.countSku = countSku;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getVerifyStatusStr() {
        return verifyStatusStr;
    }

    public void setVerifyStatusStr(String verifyStatusStr) {
        this.verifyStatusStr = verifyStatusStr;
    }

    public String getArrivalStatusStr() {
        return arrivalStatusStr;
    }

    public void setArrivalStatusStr(String arrivalStatusStr) {
        this.arrivalStatusStr = arrivalStatusStr;
    }

    public Integer getInputNum() {
        return inputNum;
    }

    public void setInputNum(Integer inputNum) {
        this.inputNum = inputNum;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }
}
