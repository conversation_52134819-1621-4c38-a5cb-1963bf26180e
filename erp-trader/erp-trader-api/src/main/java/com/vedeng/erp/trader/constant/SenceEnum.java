package com.vedeng.erp.trader.constant;

public enum SenceEnum {
    SENCE_BUSINESS_FOLLOW("SENCE_BUSINESS_FOLLOW","商机跟进"),
    SENCE_PRODUCT_PROMOT("SENCE_PRODUCT_PROMOT","产品推广"),
    SENCE_CUSTOMER_DEVELOP("SENCE_CUSTOMER_DEVELOP","客户开发"),
    SENCE_QUOTAT_RESP("SENCE_QUOTAT_RESP","报价响应"),
    SENCE_BUSINESS_PROCESS("SENCE_BUSINESS_PROCESS","商务处理"),
    SENCE_AFTER_SALE("SENCE_AFTER_SALE","售后"),
    SENCE_OTHER("SENCE_OTHER","其他");

    private String code;
    private String name;

    SenceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
