package com.vedeng.order.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SaleorderGenerateExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public SaleorderGenerateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andSaleorderIdIsNull() {
            addCriterion("SALEORDER_ID is null");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdIsNotNull() {
            addCriterion("SALEORDER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdEqualTo(Integer value) {
            addCriterion("SALEORDER_ID =", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdNotEqualTo(Integer value) {
            addCriterion("SALEORDER_ID <>", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdGreaterThan(Integer value) {
            addCriterion("SALEORDER_ID >", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SALEORDER_ID >=", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdLessThan(Integer value) {
            addCriterion("SALEORDER_ID <", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdLessThanOrEqualTo(Integer value) {
            addCriterion("SALEORDER_ID <=", value, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdIn(List<Integer> values) {
            addCriterion("SALEORDER_ID in", values, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdNotIn(List<Integer> values) {
            addCriterion("SALEORDER_ID not in", values, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdBetween(Integer value1, Integer value2) {
            addCriterion("SALEORDER_ID between", value1, value2, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andSaleorderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SALEORDER_ID not between", value1, value2, "saleorderId");
            return (Criteria) this;
        }

        public Criteria andQuoteorderIdIsNull() {
            addCriterion("QUOTEORDER_ID is null");
            return (Criteria) this;
        }

        public Criteria andQuoteorderIdIsNotNull() {
            addCriterion("QUOTEORDER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteorderIdEqualTo(Integer value) {
            addCriterion("QUOTEORDER_ID =", value, "quoteorderId");
            return (Criteria) this;
        }

        public Criteria andQuoteorderIdNotEqualTo(Integer value) {
            addCriterion("QUOTEORDER_ID <>", value, "quoteorderId");
            return (Criteria) this;
        }

        public Criteria andQuoteorderIdGreaterThan(Integer value) {
            addCriterion("QUOTEORDER_ID >", value, "quoteorderId");
            return (Criteria) this;
        }

        public Criteria andQuoteorderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("QUOTEORDER_ID >=", value, "quoteorderId");
            return (Criteria) this;
        }

        public Criteria andQuoteorderIdLessThan(Integer value) {
            addCriterion("QUOTEORDER_ID <", value, "quoteorderId");
            return (Criteria) this;
        }

        public Criteria andQuoteorderIdLessThanOrEqualTo(Integer value) {
            addCriterion("QUOTEORDER_ID <=", value, "quoteorderId");
            return (Criteria) this;
        }

        public Criteria andQuoteorderIdIn(List<Integer> values) {
            addCriterion("QUOTEORDER_ID in", values, "quoteorderId");
            return (Criteria) this;
        }

        public Criteria andQuoteorderIdNotIn(List<Integer> values) {
            addCriterion("QUOTEORDER_ID not in", values, "quoteorderId");
            return (Criteria) this;
        }

        public Criteria andQuoteorderIdBetween(Integer value1, Integer value2) {
            addCriterion("QUOTEORDER_ID between", value1, value2, "quoteorderId");
            return (Criteria) this;
        }

        public Criteria andQuoteorderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("QUOTEORDER_ID not between", value1, value2, "quoteorderId");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNull() {
            addCriterion("PARENT_ID is null");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNotNull() {
            addCriterion("PARENT_ID is not null");
            return (Criteria) this;
        }

        public Criteria andParentIdEqualTo(Integer value) {
            addCriterion("PARENT_ID =", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotEqualTo(Integer value) {
            addCriterion("PARENT_ID <>", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThan(Integer value) {
            addCriterion("PARENT_ID >", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("PARENT_ID >=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThan(Integer value) {
            addCriterion("PARENT_ID <", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThanOrEqualTo(Integer value) {
            addCriterion("PARENT_ID <=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdIn(List<Integer> values) {
            addCriterion("PARENT_ID in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotIn(List<Integer> values) {
            addCriterion("PARENT_ID not in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdBetween(Integer value1, Integer value2) {
            addCriterion("PARENT_ID between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("PARENT_ID not between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoIsNull() {
            addCriterion("SALEORDER_NO is null");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoIsNotNull() {
            addCriterion("SALEORDER_NO is not null");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoEqualTo(String value) {
            addCriterion("SALEORDER_NO =", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoNotEqualTo(String value) {
            addCriterion("SALEORDER_NO <>", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoGreaterThan(String value) {
            addCriterion("SALEORDER_NO >", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoGreaterThanOrEqualTo(String value) {
            addCriterion("SALEORDER_NO >=", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoLessThan(String value) {
            addCriterion("SALEORDER_NO <", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoLessThanOrEqualTo(String value) {
            addCriterion("SALEORDER_NO <=", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoLike(String value) {
            addCriterion("SALEORDER_NO like", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoNotLike(String value) {
            addCriterion("SALEORDER_NO not like", value, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoIn(List<String> values) {
            addCriterion("SALEORDER_NO in", values, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoNotIn(List<String> values) {
            addCriterion("SALEORDER_NO not in", values, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoBetween(String value1, String value2) {
            addCriterion("SALEORDER_NO between", value1, value2, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andSaleorderNoNotBetween(String value1, String value2) {
            addCriterion("SALEORDER_NO not between", value1, value2, "saleorderNo");
            return (Criteria) this;
        }

        public Criteria andMSaleorderNoIsNull() {
            addCriterion("M_SALEORDER_NO is null");
            return (Criteria) this;
        }

        public Criteria andMSaleorderNoIsNotNull() {
            addCriterion("M_SALEORDER_NO is not null");
            return (Criteria) this;
        }

        public Criteria andMSaleorderNoEqualTo(String value) {
            addCriterion("M_SALEORDER_NO =", value, "mSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andMSaleorderNoNotEqualTo(String value) {
            addCriterion("M_SALEORDER_NO <>", value, "mSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andMSaleorderNoGreaterThan(String value) {
            addCriterion("M_SALEORDER_NO >", value, "mSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andMSaleorderNoGreaterThanOrEqualTo(String value) {
            addCriterion("M_SALEORDER_NO >=", value, "mSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andMSaleorderNoLessThan(String value) {
            addCriterion("M_SALEORDER_NO <", value, "mSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andMSaleorderNoLessThanOrEqualTo(String value) {
            addCriterion("M_SALEORDER_NO <=", value, "mSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andMSaleorderNoLike(String value) {
            addCriterion("M_SALEORDER_NO like", value, "mSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andMSaleorderNoNotLike(String value) {
            addCriterion("M_SALEORDER_NO not like", value, "mSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andMSaleorderNoIn(List<String> values) {
            addCriterion("M_SALEORDER_NO in", values, "mSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andMSaleorderNoNotIn(List<String> values) {
            addCriterion("M_SALEORDER_NO not in", values, "mSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andMSaleorderNoBetween(String value1, String value2) {
            addCriterion("M_SALEORDER_NO between", value1, value2, "mSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andMSaleorderNoNotBetween(String value1, String value2) {
            addCriterion("M_SALEORDER_NO not between", value1, value2, "mSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNull() {
            addCriterion("ORDER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNotNull() {
            addCriterion("ORDER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualTo(Integer value) {
            addCriterion("ORDER_TYPE =", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualTo(Integer value) {
            addCriterion("ORDER_TYPE <>", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThan(Integer value) {
            addCriterion("ORDER_TYPE >", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("ORDER_TYPE >=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThan(Integer value) {
            addCriterion("ORDER_TYPE <", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualTo(Integer value) {
            addCriterion("ORDER_TYPE <=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIn(List<Integer> values) {
            addCriterion("ORDER_TYPE in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotIn(List<Integer> values) {
            addCriterion("ORDER_TYPE not in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeBetween(Integer value1, Integer value2) {
            addCriterion("ORDER_TYPE between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("ORDER_TYPE not between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("COMPANY_ID is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("COMPANY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Integer value) {
            addCriterion("COMPANY_ID =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Integer value) {
            addCriterion("COMPANY_ID <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Integer value) {
            addCriterion("COMPANY_ID >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("COMPANY_ID >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Integer value) {
            addCriterion("COMPANY_ID <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("COMPANY_ID <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Integer> values) {
            addCriterion("COMPANY_ID in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Integer> values) {
            addCriterion("COMPANY_ID not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("COMPANY_ID between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("COMPANY_ID not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("`SOURCE` is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("`SOURCE` is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(Integer value) {
            addCriterion("`SOURCE` =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(Integer value) {
            addCriterion("`SOURCE` <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(Integer value) {
            addCriterion("`SOURCE` >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("`SOURCE` >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(Integer value) {
            addCriterion("`SOURCE` <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(Integer value) {
            addCriterion("`SOURCE` <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<Integer> values) {
            addCriterion("`SOURCE` in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<Integer> values) {
            addCriterion("`SOURCE` not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(Integer value1, Integer value2) {
            addCriterion("`SOURCE` between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("`SOURCE` not between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgIdIsNull() {
            addCriterion("CREATOR_ORG_ID is null");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgIdIsNotNull() {
            addCriterion("CREATOR_ORG_ID is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgIdEqualTo(Integer value) {
            addCriterion("CREATOR_ORG_ID =", value, "creatorOrgId");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgIdNotEqualTo(Integer value) {
            addCriterion("CREATOR_ORG_ID <>", value, "creatorOrgId");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgIdGreaterThan(Integer value) {
            addCriterion("CREATOR_ORG_ID >", value, "creatorOrgId");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR_ORG_ID >=", value, "creatorOrgId");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgIdLessThan(Integer value) {
            addCriterion("CREATOR_ORG_ID <", value, "creatorOrgId");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgIdLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR_ORG_ID <=", value, "creatorOrgId");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgIdIn(List<Integer> values) {
            addCriterion("CREATOR_ORG_ID in", values, "creatorOrgId");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgIdNotIn(List<Integer> values) {
            addCriterion("CREATOR_ORG_ID not in", values, "creatorOrgId");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgIdBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR_ORG_ID between", value1, value2, "creatorOrgId");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgIdNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR_ORG_ID not between", value1, value2, "creatorOrgId");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgNameIsNull() {
            addCriterion("CREATOR_ORG_NAME is null");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgNameIsNotNull() {
            addCriterion("CREATOR_ORG_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgNameEqualTo(String value) {
            addCriterion("CREATOR_ORG_NAME =", value, "creatorOrgName");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgNameNotEqualTo(String value) {
            addCriterion("CREATOR_ORG_NAME <>", value, "creatorOrgName");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgNameGreaterThan(String value) {
            addCriterion("CREATOR_ORG_NAME >", value, "creatorOrgName");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("CREATOR_ORG_NAME >=", value, "creatorOrgName");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgNameLessThan(String value) {
            addCriterion("CREATOR_ORG_NAME <", value, "creatorOrgName");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgNameLessThanOrEqualTo(String value) {
            addCriterion("CREATOR_ORG_NAME <=", value, "creatorOrgName");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgNameLike(String value) {
            addCriterion("CREATOR_ORG_NAME like", value, "creatorOrgName");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgNameNotLike(String value) {
            addCriterion("CREATOR_ORG_NAME not like", value, "creatorOrgName");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgNameIn(List<String> values) {
            addCriterion("CREATOR_ORG_NAME in", values, "creatorOrgName");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgNameNotIn(List<String> values) {
            addCriterion("CREATOR_ORG_NAME not in", values, "creatorOrgName");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgNameBetween(String value1, String value2) {
            addCriterion("CREATOR_ORG_NAME between", value1, value2, "creatorOrgName");
            return (Criteria) this;
        }

        public Criteria andCreatorOrgNameNotBetween(String value1, String value2) {
            addCriterion("CREATOR_ORG_NAME not between", value1, value2, "creatorOrgName");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNull() {
            addCriterion("ORG_ID is null");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNotNull() {
            addCriterion("ORG_ID is not null");
            return (Criteria) this;
        }

        public Criteria andOrgIdEqualTo(Integer value) {
            addCriterion("ORG_ID =", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotEqualTo(Integer value) {
            addCriterion("ORG_ID <>", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThan(Integer value) {
            addCriterion("ORG_ID >", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ORG_ID >=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThan(Integer value) {
            addCriterion("ORG_ID <", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThanOrEqualTo(Integer value) {
            addCriterion("ORG_ID <=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdIn(List<Integer> values) {
            addCriterion("ORG_ID in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotIn(List<Integer> values) {
            addCriterion("ORG_ID not in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdBetween(Integer value1, Integer value2) {
            addCriterion("ORG_ID between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ORG_ID not between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNull() {
            addCriterion("ORG_NAME is null");
            return (Criteria) this;
        }

        public Criteria andOrgNameIsNotNull() {
            addCriterion("ORG_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andOrgNameEqualTo(String value) {
            addCriterion("ORG_NAME =", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotEqualTo(String value) {
            addCriterion("ORG_NAME <>", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThan(String value) {
            addCriterion("ORG_NAME >", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("ORG_NAME >=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThan(String value) {
            addCriterion("ORG_NAME <", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLessThanOrEqualTo(String value) {
            addCriterion("ORG_NAME <=", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameLike(String value) {
            addCriterion("ORG_NAME like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotLike(String value) {
            addCriterion("ORG_NAME not like", value, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameIn(List<String> values) {
            addCriterion("ORG_NAME in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotIn(List<String> values) {
            addCriterion("ORG_NAME not in", values, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameBetween(String value1, String value2) {
            addCriterion("ORG_NAME between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andOrgNameNotBetween(String value1, String value2) {
            addCriterion("ORG_NAME not between", value1, value2, "orgName");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("USER_ID is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("USER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Integer value) {
            addCriterion("USER_ID =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Integer value) {
            addCriterion("USER_ID <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Integer value) {
            addCriterion("USER_ID >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("USER_ID >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Integer value) {
            addCriterion("USER_ID <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("USER_ID <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Integer> values) {
            addCriterion("USER_ID in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Integer> values) {
            addCriterion("USER_ID not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Integer value1, Integer value2) {
            addCriterion("USER_ID between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("USER_ID not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andValidOrgIdIsNull() {
            addCriterion("VALID_ORG_ID is null");
            return (Criteria) this;
        }

        public Criteria andValidOrgIdIsNotNull() {
            addCriterion("VALID_ORG_ID is not null");
            return (Criteria) this;
        }

        public Criteria andValidOrgIdEqualTo(Integer value) {
            addCriterion("VALID_ORG_ID =", value, "validOrgId");
            return (Criteria) this;
        }

        public Criteria andValidOrgIdNotEqualTo(Integer value) {
            addCriterion("VALID_ORG_ID <>", value, "validOrgId");
            return (Criteria) this;
        }

        public Criteria andValidOrgIdGreaterThan(Integer value) {
            addCriterion("VALID_ORG_ID >", value, "validOrgId");
            return (Criteria) this;
        }

        public Criteria andValidOrgIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("VALID_ORG_ID >=", value, "validOrgId");
            return (Criteria) this;
        }

        public Criteria andValidOrgIdLessThan(Integer value) {
            addCriterion("VALID_ORG_ID <", value, "validOrgId");
            return (Criteria) this;
        }

        public Criteria andValidOrgIdLessThanOrEqualTo(Integer value) {
            addCriterion("VALID_ORG_ID <=", value, "validOrgId");
            return (Criteria) this;
        }

        public Criteria andValidOrgIdIn(List<Integer> values) {
            addCriterion("VALID_ORG_ID in", values, "validOrgId");
            return (Criteria) this;
        }

        public Criteria andValidOrgIdNotIn(List<Integer> values) {
            addCriterion("VALID_ORG_ID not in", values, "validOrgId");
            return (Criteria) this;
        }

        public Criteria andValidOrgIdBetween(Integer value1, Integer value2) {
            addCriterion("VALID_ORG_ID between", value1, value2, "validOrgId");
            return (Criteria) this;
        }

        public Criteria andValidOrgIdNotBetween(Integer value1, Integer value2) {
            addCriterion("VALID_ORG_ID not between", value1, value2, "validOrgId");
            return (Criteria) this;
        }

        public Criteria andValidOrgNameIsNull() {
            addCriterion("VALID_ORG_NAME is null");
            return (Criteria) this;
        }

        public Criteria andValidOrgNameIsNotNull() {
            addCriterion("VALID_ORG_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andValidOrgNameEqualTo(String value) {
            addCriterion("VALID_ORG_NAME =", value, "validOrgName");
            return (Criteria) this;
        }

        public Criteria andValidOrgNameNotEqualTo(String value) {
            addCriterion("VALID_ORG_NAME <>", value, "validOrgName");
            return (Criteria) this;
        }

        public Criteria andValidOrgNameGreaterThan(String value) {
            addCriterion("VALID_ORG_NAME >", value, "validOrgName");
            return (Criteria) this;
        }

        public Criteria andValidOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("VALID_ORG_NAME >=", value, "validOrgName");
            return (Criteria) this;
        }

        public Criteria andValidOrgNameLessThan(String value) {
            addCriterion("VALID_ORG_NAME <", value, "validOrgName");
            return (Criteria) this;
        }

        public Criteria andValidOrgNameLessThanOrEqualTo(String value) {
            addCriterion("VALID_ORG_NAME <=", value, "validOrgName");
            return (Criteria) this;
        }

        public Criteria andValidOrgNameLike(String value) {
            addCriterion("VALID_ORG_NAME like", value, "validOrgName");
            return (Criteria) this;
        }

        public Criteria andValidOrgNameNotLike(String value) {
            addCriterion("VALID_ORG_NAME not like", value, "validOrgName");
            return (Criteria) this;
        }

        public Criteria andValidOrgNameIn(List<String> values) {
            addCriterion("VALID_ORG_NAME in", values, "validOrgName");
            return (Criteria) this;
        }

        public Criteria andValidOrgNameNotIn(List<String> values) {
            addCriterion("VALID_ORG_NAME not in", values, "validOrgName");
            return (Criteria) this;
        }

        public Criteria andValidOrgNameBetween(String value1, String value2) {
            addCriterion("VALID_ORG_NAME between", value1, value2, "validOrgName");
            return (Criteria) this;
        }

        public Criteria andValidOrgNameNotBetween(String value1, String value2) {
            addCriterion("VALID_ORG_NAME not between", value1, value2, "validOrgName");
            return (Criteria) this;
        }

        public Criteria andValidUserIdIsNull() {
            addCriterion("VALID_USER_ID is null");
            return (Criteria) this;
        }

        public Criteria andValidUserIdIsNotNull() {
            addCriterion("VALID_USER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andValidUserIdEqualTo(Integer value) {
            addCriterion("VALID_USER_ID =", value, "validUserId");
            return (Criteria) this;
        }

        public Criteria andValidUserIdNotEqualTo(Integer value) {
            addCriterion("VALID_USER_ID <>", value, "validUserId");
            return (Criteria) this;
        }

        public Criteria andValidUserIdGreaterThan(Integer value) {
            addCriterion("VALID_USER_ID >", value, "validUserId");
            return (Criteria) this;
        }

        public Criteria andValidUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("VALID_USER_ID >=", value, "validUserId");
            return (Criteria) this;
        }

        public Criteria andValidUserIdLessThan(Integer value) {
            addCriterion("VALID_USER_ID <", value, "validUserId");
            return (Criteria) this;
        }

        public Criteria andValidUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("VALID_USER_ID <=", value, "validUserId");
            return (Criteria) this;
        }

        public Criteria andValidUserIdIn(List<Integer> values) {
            addCriterion("VALID_USER_ID in", values, "validUserId");
            return (Criteria) this;
        }

        public Criteria andValidUserIdNotIn(List<Integer> values) {
            addCriterion("VALID_USER_ID not in", values, "validUserId");
            return (Criteria) this;
        }

        public Criteria andValidUserIdBetween(Integer value1, Integer value2) {
            addCriterion("VALID_USER_ID between", value1, value2, "validUserId");
            return (Criteria) this;
        }

        public Criteria andValidUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("VALID_USER_ID not between", value1, value2, "validUserId");
            return (Criteria) this;
        }

        public Criteria andValidStatusIsNull() {
            addCriterion("VALID_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andValidStatusIsNotNull() {
            addCriterion("VALID_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andValidStatusEqualTo(Byte value) {
            addCriterion("VALID_STATUS =", value, "validStatus");
            return (Criteria) this;
        }

        public Criteria andValidStatusNotEqualTo(Byte value) {
            addCriterion("VALID_STATUS <>", value, "validStatus");
            return (Criteria) this;
        }

        public Criteria andValidStatusGreaterThan(Byte value) {
            addCriterion("VALID_STATUS >", value, "validStatus");
            return (Criteria) this;
        }

        public Criteria andValidStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("VALID_STATUS >=", value, "validStatus");
            return (Criteria) this;
        }

        public Criteria andValidStatusLessThan(Byte value) {
            addCriterion("VALID_STATUS <", value, "validStatus");
            return (Criteria) this;
        }

        public Criteria andValidStatusLessThanOrEqualTo(Byte value) {
            addCriterion("VALID_STATUS <=", value, "validStatus");
            return (Criteria) this;
        }

        public Criteria andValidStatusIn(List<Byte> values) {
            addCriterion("VALID_STATUS in", values, "validStatus");
            return (Criteria) this;
        }

        public Criteria andValidStatusNotIn(List<Byte> values) {
            addCriterion("VALID_STATUS not in", values, "validStatus");
            return (Criteria) this;
        }

        public Criteria andValidStatusBetween(Byte value1, Byte value2) {
            addCriterion("VALID_STATUS between", value1, value2, "validStatus");
            return (Criteria) this;
        }

        public Criteria andValidStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("VALID_STATUS not between", value1, value2, "validStatus");
            return (Criteria) this;
        }

        public Criteria andValidTimeIsNull() {
            addCriterion("VALID_TIME is null");
            return (Criteria) this;
        }

        public Criteria andValidTimeIsNotNull() {
            addCriterion("VALID_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andValidTimeEqualTo(Long value) {
            addCriterion("VALID_TIME =", value, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeNotEqualTo(Long value) {
            addCriterion("VALID_TIME <>", value, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeGreaterThan(Long value) {
            addCriterion("VALID_TIME >", value, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("VALID_TIME >=", value, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeLessThan(Long value) {
            addCriterion("VALID_TIME <", value, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeLessThanOrEqualTo(Long value) {
            addCriterion("VALID_TIME <=", value, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeIn(List<Long> values) {
            addCriterion("VALID_TIME in", values, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeNotIn(List<Long> values) {
            addCriterion("VALID_TIME not in", values, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeBetween(Long value1, Long value2) {
            addCriterion("VALID_TIME between", value1, value2, "validTime");
            return (Criteria) this;
        }

        public Criteria andValidTimeNotBetween(Long value1, Long value2) {
            addCriterion("VALID_TIME not between", value1, value2, "validTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("END_TIME is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("END_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Long value) {
            addCriterion("END_TIME =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Long value) {
            addCriterion("END_TIME <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Long value) {
            addCriterion("END_TIME >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("END_TIME >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Long value) {
            addCriterion("END_TIME <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Long value) {
            addCriterion("END_TIME <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Long> values) {
            addCriterion("END_TIME in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Long> values) {
            addCriterion("END_TIME not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Long value1, Long value2) {
            addCriterion("END_TIME between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Long value1, Long value2) {
            addCriterion("END_TIME not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`STATUS` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`STATUS` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`STATUS` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`STATUS` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`STATUS` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`STATUS` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`STATUS` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`STATUS` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`STATUS` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`STATUS` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`STATUS` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`STATUS` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusIsNull() {
            addCriterion("PURCHASE_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusIsNotNull() {
            addCriterion("PURCHASE_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusEqualTo(Byte value) {
            addCriterion("PURCHASE_STATUS =", value, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusNotEqualTo(Byte value) {
            addCriterion("PURCHASE_STATUS <>", value, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusGreaterThan(Byte value) {
            addCriterion("PURCHASE_STATUS >", value, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("PURCHASE_STATUS >=", value, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusLessThan(Byte value) {
            addCriterion("PURCHASE_STATUS <", value, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusLessThanOrEqualTo(Byte value) {
            addCriterion("PURCHASE_STATUS <=", value, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusIn(List<Byte> values) {
            addCriterion("PURCHASE_STATUS in", values, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusNotIn(List<Byte> values) {
            addCriterion("PURCHASE_STATUS not in", values, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusBetween(Byte value1, Byte value2) {
            addCriterion("PURCHASE_STATUS between", value1, value2, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andPurchaseStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("PURCHASE_STATUS not between", value1, value2, "purchaseStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusIsNull() {
            addCriterion("LOCKED_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andLockedStatusIsNotNull() {
            addCriterion("LOCKED_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andLockedStatusEqualTo(Byte value) {
            addCriterion("LOCKED_STATUS =", value, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusNotEqualTo(Byte value) {
            addCriterion("LOCKED_STATUS <>", value, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusGreaterThan(Byte value) {
            addCriterion("LOCKED_STATUS >", value, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("LOCKED_STATUS >=", value, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusLessThan(Byte value) {
            addCriterion("LOCKED_STATUS <", value, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusLessThanOrEqualTo(Byte value) {
            addCriterion("LOCKED_STATUS <=", value, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusIn(List<Byte> values) {
            addCriterion("LOCKED_STATUS in", values, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusNotIn(List<Byte> values) {
            addCriterion("LOCKED_STATUS not in", values, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusBetween(Byte value1, Byte value2) {
            addCriterion("LOCKED_STATUS between", value1, value2, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andLockedStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("LOCKED_STATUS not between", value1, value2, "lockedStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceStatusIsNull() {
            addCriterion("INVOICE_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceStatusIsNotNull() {
            addCriterion("INVOICE_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceStatusEqualTo(Integer value) {
            addCriterion("INVOICE_STATUS =", value, "invoiceStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceStatusNotEqualTo(Integer value) {
            addCriterion("INVOICE_STATUS <>", value, "invoiceStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceStatusGreaterThan(Integer value) {
            addCriterion("INVOICE_STATUS >", value, "invoiceStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("INVOICE_STATUS >=", value, "invoiceStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceStatusLessThan(Integer value) {
            addCriterion("INVOICE_STATUS <", value, "invoiceStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceStatusLessThanOrEqualTo(Integer value) {
            addCriterion("INVOICE_STATUS <=", value, "invoiceStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceStatusIn(List<Integer> values) {
            addCriterion("INVOICE_STATUS in", values, "invoiceStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceStatusNotIn(List<Integer> values) {
            addCriterion("INVOICE_STATUS not in", values, "invoiceStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceStatusBetween(Integer value1, Integer value2) {
            addCriterion("INVOICE_STATUS between", value1, value2, "invoiceStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("INVOICE_STATUS not between", value1, value2, "invoiceStatus");
            return (Criteria) this;
        }

        public Criteria andInvoiceTimeIsNull() {
            addCriterion("INVOICE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTimeIsNotNull() {
            addCriterion("INVOICE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTimeEqualTo(Long value) {
            addCriterion("INVOICE_TIME =", value, "invoiceTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceTimeNotEqualTo(Long value) {
            addCriterion("INVOICE_TIME <>", value, "invoiceTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceTimeGreaterThan(Long value) {
            addCriterion("INVOICE_TIME >", value, "invoiceTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("INVOICE_TIME >=", value, "invoiceTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceTimeLessThan(Long value) {
            addCriterion("INVOICE_TIME <", value, "invoiceTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceTimeLessThanOrEqualTo(Long value) {
            addCriterion("INVOICE_TIME <=", value, "invoiceTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceTimeIn(List<Long> values) {
            addCriterion("INVOICE_TIME in", values, "invoiceTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceTimeNotIn(List<Long> values) {
            addCriterion("INVOICE_TIME not in", values, "invoiceTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceTimeBetween(Long value1, Long value2) {
            addCriterion("INVOICE_TIME between", value1, value2, "invoiceTime");
            return (Criteria) this;
        }

        public Criteria andInvoiceTimeNotBetween(Long value1, Long value2) {
            addCriterion("INVOICE_TIME not between", value1, value2, "invoiceTime");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusIsNull() {
            addCriterion("PAYMENT_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusIsNotNull() {
            addCriterion("PAYMENT_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusEqualTo(Integer value) {
            addCriterion("PAYMENT_STATUS =", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusNotEqualTo(Integer value) {
            addCriterion("PAYMENT_STATUS <>", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusGreaterThan(Integer value) {
            addCriterion("PAYMENT_STATUS >", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("PAYMENT_STATUS >=", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusLessThan(Integer value) {
            addCriterion("PAYMENT_STATUS <", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusLessThanOrEqualTo(Integer value) {
            addCriterion("PAYMENT_STATUS <=", value, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusIn(List<Integer> values) {
            addCriterion("PAYMENT_STATUS in", values, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusNotIn(List<Integer> values) {
            addCriterion("PAYMENT_STATUS not in", values, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusBetween(Integer value1, Integer value2) {
            addCriterion("PAYMENT_STATUS between", value1, value2, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("PAYMENT_STATUS not between", value1, value2, "paymentStatus");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeIsNull() {
            addCriterion("PAYMENT_TIME is null");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeIsNotNull() {
            addCriterion("PAYMENT_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeEqualTo(Long value) {
            addCriterion("PAYMENT_TIME =", value, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeNotEqualTo(Long value) {
            addCriterion("PAYMENT_TIME <>", value, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeGreaterThan(Long value) {
            addCriterion("PAYMENT_TIME >", value, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("PAYMENT_TIME >=", value, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeLessThan(Long value) {
            addCriterion("PAYMENT_TIME <", value, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeLessThanOrEqualTo(Long value) {
            addCriterion("PAYMENT_TIME <=", value, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeIn(List<Long> values) {
            addCriterion("PAYMENT_TIME in", values, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeNotIn(List<Long> values) {
            addCriterion("PAYMENT_TIME not in", values, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeBetween(Long value1, Long value2) {
            addCriterion("PAYMENT_TIME between", value1, value2, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andPaymentTimeNotBetween(Long value1, Long value2) {
            addCriterion("PAYMENT_TIME not between", value1, value2, "paymentTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIsNull() {
            addCriterion("DELIVERY_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIsNotNull() {
            addCriterion("DELIVERY_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusEqualTo(Integer value) {
            addCriterion("DELIVERY_STATUS =", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotEqualTo(Integer value) {
            addCriterion("DELIVERY_STATUS <>", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusGreaterThan(Integer value) {
            addCriterion("DELIVERY_STATUS >", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_STATUS >=", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusLessThan(Integer value) {
            addCriterion("DELIVERY_STATUS <", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusLessThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_STATUS <=", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIn(List<Integer> values) {
            addCriterion("DELIVERY_STATUS in", values, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotIn(List<Integer> values) {
            addCriterion("DELIVERY_STATUS not in", values, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_STATUS between", value1, value2, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_STATUS not between", value1, value2, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNull() {
            addCriterion("DELIVERY_TIME is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNotNull() {
            addCriterion("DELIVERY_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeEqualTo(Long value) {
            addCriterion("DELIVERY_TIME =", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotEqualTo(Long value) {
            addCriterion("DELIVERY_TIME <>", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThan(Long value) {
            addCriterion("DELIVERY_TIME >", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("DELIVERY_TIME >=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThan(Long value) {
            addCriterion("DELIVERY_TIME <", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThanOrEqualTo(Long value) {
            addCriterion("DELIVERY_TIME <=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIn(List<Long> values) {
            addCriterion("DELIVERY_TIME in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotIn(List<Long> values) {
            addCriterion("DELIVERY_TIME not in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeBetween(Long value1, Long value2) {
            addCriterion("DELIVERY_TIME between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotBetween(Long value1, Long value2) {
            addCriterion("DELIVERY_TIME not between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andIsCustomerArrivalIsNull() {
            addCriterion("IS_CUSTOMER_ARRIVAL is null");
            return (Criteria) this;
        }

        public Criteria andIsCustomerArrivalIsNotNull() {
            addCriterion("IS_CUSTOMER_ARRIVAL is not null");
            return (Criteria) this;
        }

        public Criteria andIsCustomerArrivalEqualTo(Byte value) {
            addCriterion("IS_CUSTOMER_ARRIVAL =", value, "isCustomerArrival");
            return (Criteria) this;
        }

        public Criteria andIsCustomerArrivalNotEqualTo(Byte value) {
            addCriterion("IS_CUSTOMER_ARRIVAL <>", value, "isCustomerArrival");
            return (Criteria) this;
        }

        public Criteria andIsCustomerArrivalGreaterThan(Byte value) {
            addCriterion("IS_CUSTOMER_ARRIVAL >", value, "isCustomerArrival");
            return (Criteria) this;
        }

        public Criteria andIsCustomerArrivalGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_CUSTOMER_ARRIVAL >=", value, "isCustomerArrival");
            return (Criteria) this;
        }

        public Criteria andIsCustomerArrivalLessThan(Byte value) {
            addCriterion("IS_CUSTOMER_ARRIVAL <", value, "isCustomerArrival");
            return (Criteria) this;
        }

        public Criteria andIsCustomerArrivalLessThanOrEqualTo(Byte value) {
            addCriterion("IS_CUSTOMER_ARRIVAL <=", value, "isCustomerArrival");
            return (Criteria) this;
        }

        public Criteria andIsCustomerArrivalIn(List<Byte> values) {
            addCriterion("IS_CUSTOMER_ARRIVAL in", values, "isCustomerArrival");
            return (Criteria) this;
        }

        public Criteria andIsCustomerArrivalNotIn(List<Byte> values) {
            addCriterion("IS_CUSTOMER_ARRIVAL not in", values, "isCustomerArrival");
            return (Criteria) this;
        }

        public Criteria andIsCustomerArrivalBetween(Byte value1, Byte value2) {
            addCriterion("IS_CUSTOMER_ARRIVAL between", value1, value2, "isCustomerArrival");
            return (Criteria) this;
        }

        public Criteria andIsCustomerArrivalNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_CUSTOMER_ARRIVAL not between", value1, value2, "isCustomerArrival");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusIsNull() {
            addCriterion("ARRIVAL_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusIsNotNull() {
            addCriterion("ARRIVAL_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusEqualTo(Byte value) {
            addCriterion("ARRIVAL_STATUS =", value, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusNotEqualTo(Byte value) {
            addCriterion("ARRIVAL_STATUS <>", value, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusGreaterThan(Byte value) {
            addCriterion("ARRIVAL_STATUS >", value, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("ARRIVAL_STATUS >=", value, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusLessThan(Byte value) {
            addCriterion("ARRIVAL_STATUS <", value, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusLessThanOrEqualTo(Byte value) {
            addCriterion("ARRIVAL_STATUS <=", value, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusIn(List<Byte> values) {
            addCriterion("ARRIVAL_STATUS in", values, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusNotIn(List<Byte> values) {
            addCriterion("ARRIVAL_STATUS not in", values, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusBetween(Byte value1, Byte value2) {
            addCriterion("ARRIVAL_STATUS between", value1, value2, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("ARRIVAL_STATUS not between", value1, value2, "arrivalStatus");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeIsNull() {
            addCriterion("ARRIVAL_TIME is null");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeIsNotNull() {
            addCriterion("ARRIVAL_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeEqualTo(Long value) {
            addCriterion("ARRIVAL_TIME =", value, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeNotEqualTo(Long value) {
            addCriterion("ARRIVAL_TIME <>", value, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeGreaterThan(Long value) {
            addCriterion("ARRIVAL_TIME >", value, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ARRIVAL_TIME >=", value, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeLessThan(Long value) {
            addCriterion("ARRIVAL_TIME <", value, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeLessThanOrEqualTo(Long value) {
            addCriterion("ARRIVAL_TIME <=", value, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeIn(List<Long> values) {
            addCriterion("ARRIVAL_TIME in", values, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeNotIn(List<Long> values) {
            addCriterion("ARRIVAL_TIME not in", values, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeBetween(Long value1, Long value2) {
            addCriterion("ARRIVAL_TIME between", value1, value2, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andArrivalTimeNotBetween(Long value1, Long value2) {
            addCriterion("ARRIVAL_TIME not between", value1, value2, "arrivalTime");
            return (Criteria) this;
        }

        public Criteria andServiceStatusIsNull() {
            addCriterion("SERVICE_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andServiceStatusIsNotNull() {
            addCriterion("SERVICE_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andServiceStatusEqualTo(Byte value) {
            addCriterion("SERVICE_STATUS =", value, "serviceStatus");
            return (Criteria) this;
        }

        public Criteria andServiceStatusNotEqualTo(Byte value) {
            addCriterion("SERVICE_STATUS <>", value, "serviceStatus");
            return (Criteria) this;
        }

        public Criteria andServiceStatusGreaterThan(Byte value) {
            addCriterion("SERVICE_STATUS >", value, "serviceStatus");
            return (Criteria) this;
        }

        public Criteria andServiceStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("SERVICE_STATUS >=", value, "serviceStatus");
            return (Criteria) this;
        }

        public Criteria andServiceStatusLessThan(Byte value) {
            addCriterion("SERVICE_STATUS <", value, "serviceStatus");
            return (Criteria) this;
        }

        public Criteria andServiceStatusLessThanOrEqualTo(Byte value) {
            addCriterion("SERVICE_STATUS <=", value, "serviceStatus");
            return (Criteria) this;
        }

        public Criteria andServiceStatusIn(List<Byte> values) {
            addCriterion("SERVICE_STATUS in", values, "serviceStatus");
            return (Criteria) this;
        }

        public Criteria andServiceStatusNotIn(List<Byte> values) {
            addCriterion("SERVICE_STATUS not in", values, "serviceStatus");
            return (Criteria) this;
        }

        public Criteria andServiceStatusBetween(Byte value1, Byte value2) {
            addCriterion("SERVICE_STATUS between", value1, value2, "serviceStatus");
            return (Criteria) this;
        }

        public Criteria andServiceStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("SERVICE_STATUS not between", value1, value2, "serviceStatus");
            return (Criteria) this;
        }

        public Criteria andHaveAccountPeriodIsNull() {
            addCriterion("HAVE_ACCOUNT_PERIOD is null");
            return (Criteria) this;
        }

        public Criteria andHaveAccountPeriodIsNotNull() {
            addCriterion("HAVE_ACCOUNT_PERIOD is not null");
            return (Criteria) this;
        }

        public Criteria andHaveAccountPeriodEqualTo(Byte value) {
            addCriterion("HAVE_ACCOUNT_PERIOD =", value, "haveAccountPeriod");
            return (Criteria) this;
        }

        public Criteria andHaveAccountPeriodNotEqualTo(Byte value) {
            addCriterion("HAVE_ACCOUNT_PERIOD <>", value, "haveAccountPeriod");
            return (Criteria) this;
        }

        public Criteria andHaveAccountPeriodGreaterThan(Byte value) {
            addCriterion("HAVE_ACCOUNT_PERIOD >", value, "haveAccountPeriod");
            return (Criteria) this;
        }

        public Criteria andHaveAccountPeriodGreaterThanOrEqualTo(Byte value) {
            addCriterion("HAVE_ACCOUNT_PERIOD >=", value, "haveAccountPeriod");
            return (Criteria) this;
        }

        public Criteria andHaveAccountPeriodLessThan(Byte value) {
            addCriterion("HAVE_ACCOUNT_PERIOD <", value, "haveAccountPeriod");
            return (Criteria) this;
        }

        public Criteria andHaveAccountPeriodLessThanOrEqualTo(Byte value) {
            addCriterion("HAVE_ACCOUNT_PERIOD <=", value, "haveAccountPeriod");
            return (Criteria) this;
        }

        public Criteria andHaveAccountPeriodIn(List<Byte> values) {
            addCriterion("HAVE_ACCOUNT_PERIOD in", values, "haveAccountPeriod");
            return (Criteria) this;
        }

        public Criteria andHaveAccountPeriodNotIn(List<Byte> values) {
            addCriterion("HAVE_ACCOUNT_PERIOD not in", values, "haveAccountPeriod");
            return (Criteria) this;
        }

        public Criteria andHaveAccountPeriodBetween(Byte value1, Byte value2) {
            addCriterion("HAVE_ACCOUNT_PERIOD between", value1, value2, "haveAccountPeriod");
            return (Criteria) this;
        }

        public Criteria andHaveAccountPeriodNotBetween(Byte value1, Byte value2) {
            addCriterion("HAVE_ACCOUNT_PERIOD not between", value1, value2, "haveAccountPeriod");
            return (Criteria) this;
        }

        public Criteria andIsPaymentIsNull() {
            addCriterion("IS_PAYMENT is null");
            return (Criteria) this;
        }

        public Criteria andIsPaymentIsNotNull() {
            addCriterion("IS_PAYMENT is not null");
            return (Criteria) this;
        }

        public Criteria andIsPaymentEqualTo(Byte value) {
            addCriterion("IS_PAYMENT =", value, "isPayment");
            return (Criteria) this;
        }

        public Criteria andIsPaymentNotEqualTo(Byte value) {
            addCriterion("IS_PAYMENT <>", value, "isPayment");
            return (Criteria) this;
        }

        public Criteria andIsPaymentGreaterThan(Byte value) {
            addCriterion("IS_PAYMENT >", value, "isPayment");
            return (Criteria) this;
        }

        public Criteria andIsPaymentGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_PAYMENT >=", value, "isPayment");
            return (Criteria) this;
        }

        public Criteria andIsPaymentLessThan(Byte value) {
            addCriterion("IS_PAYMENT <", value, "isPayment");
            return (Criteria) this;
        }

        public Criteria andIsPaymentLessThanOrEqualTo(Byte value) {
            addCriterion("IS_PAYMENT <=", value, "isPayment");
            return (Criteria) this;
        }

        public Criteria andIsPaymentIn(List<Byte> values) {
            addCriterion("IS_PAYMENT in", values, "isPayment");
            return (Criteria) this;
        }

        public Criteria andIsPaymentNotIn(List<Byte> values) {
            addCriterion("IS_PAYMENT not in", values, "isPayment");
            return (Criteria) this;
        }

        public Criteria andIsPaymentBetween(Byte value1, Byte value2) {
            addCriterion("IS_PAYMENT between", value1, value2, "isPayment");
            return (Criteria) this;
        }

        public Criteria andIsPaymentNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_PAYMENT not between", value1, value2, "isPayment");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIsNull() {
            addCriterion("TOTAL_AMOUNT is null");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIsNotNull() {
            addCriterion("TOTAL_AMOUNT is not null");
            return (Criteria) this;
        }

        public Criteria andTotalAmountEqualTo(BigDecimal value) {
            addCriterion("TOTAL_AMOUNT =", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("TOTAL_AMOUNT <>", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("TOTAL_AMOUNT >", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("TOTAL_AMOUNT >=", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLessThan(BigDecimal value) {
            addCriterion("TOTAL_AMOUNT <", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("TOTAL_AMOUNT <=", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIn(List<BigDecimal> values) {
            addCriterion("TOTAL_AMOUNT in", values, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("TOTAL_AMOUNT not in", values, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("TOTAL_AMOUNT between", value1, value2, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("TOTAL_AMOUNT not between", value1, value2, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTraderIdIsNull() {
            addCriterion("TRADER_ID is null");
            return (Criteria) this;
        }

        public Criteria andTraderIdIsNotNull() {
            addCriterion("TRADER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTraderIdEqualTo(Integer value) {
            addCriterion("TRADER_ID =", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdNotEqualTo(Integer value) {
            addCriterion("TRADER_ID <>", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdGreaterThan(Integer value) {
            addCriterion("TRADER_ID >", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_ID >=", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdLessThan(Integer value) {
            addCriterion("TRADER_ID <", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_ID <=", value, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdIn(List<Integer> values) {
            addCriterion("TRADER_ID in", values, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdNotIn(List<Integer> values) {
            addCriterion("TRADER_ID not in", values, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_ID between", value1, value2, "traderId");
            return (Criteria) this;
        }

        public Criteria andTraderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_ID not between", value1, value2, "traderId");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeIsNull() {
            addCriterion("CUSTOMER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeIsNotNull() {
            addCriterion("CUSTOMER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeEqualTo(Integer value) {
            addCriterion("CUSTOMER_TYPE =", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotEqualTo(Integer value) {
            addCriterion("CUSTOMER_TYPE <>", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeGreaterThan(Integer value) {
            addCriterion("CUSTOMER_TYPE >", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("CUSTOMER_TYPE >=", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeLessThan(Integer value) {
            addCriterion("CUSTOMER_TYPE <", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeLessThanOrEqualTo(Integer value) {
            addCriterion("CUSTOMER_TYPE <=", value, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeIn(List<Integer> values) {
            addCriterion("CUSTOMER_TYPE in", values, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotIn(List<Integer> values) {
            addCriterion("CUSTOMER_TYPE not in", values, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeBetween(Integer value1, Integer value2) {
            addCriterion("CUSTOMER_TYPE between", value1, value2, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("CUSTOMER_TYPE not between", value1, value2, "customerType");
            return (Criteria) this;
        }

        public Criteria andCustomerNatureIsNull() {
            addCriterion("CUSTOMER_NATURE is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNatureIsNotNull() {
            addCriterion("CUSTOMER_NATURE is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNatureEqualTo(Integer value) {
            addCriterion("CUSTOMER_NATURE =", value, "customerNature");
            return (Criteria) this;
        }

        public Criteria andCustomerNatureNotEqualTo(Integer value) {
            addCriterion("CUSTOMER_NATURE <>", value, "customerNature");
            return (Criteria) this;
        }

        public Criteria andCustomerNatureGreaterThan(Integer value) {
            addCriterion("CUSTOMER_NATURE >", value, "customerNature");
            return (Criteria) this;
        }

        public Criteria andCustomerNatureGreaterThanOrEqualTo(Integer value) {
            addCriterion("CUSTOMER_NATURE >=", value, "customerNature");
            return (Criteria) this;
        }

        public Criteria andCustomerNatureLessThan(Integer value) {
            addCriterion("CUSTOMER_NATURE <", value, "customerNature");
            return (Criteria) this;
        }

        public Criteria andCustomerNatureLessThanOrEqualTo(Integer value) {
            addCriterion("CUSTOMER_NATURE <=", value, "customerNature");
            return (Criteria) this;
        }

        public Criteria andCustomerNatureIn(List<Integer> values) {
            addCriterion("CUSTOMER_NATURE in", values, "customerNature");
            return (Criteria) this;
        }

        public Criteria andCustomerNatureNotIn(List<Integer> values) {
            addCriterion("CUSTOMER_NATURE not in", values, "customerNature");
            return (Criteria) this;
        }

        public Criteria andCustomerNatureBetween(Integer value1, Integer value2) {
            addCriterion("CUSTOMER_NATURE between", value1, value2, "customerNature");
            return (Criteria) this;
        }

        public Criteria andCustomerNatureNotBetween(Integer value1, Integer value2) {
            addCriterion("CUSTOMER_NATURE not between", value1, value2, "customerNature");
            return (Criteria) this;
        }

        public Criteria andTraderNameIsNull() {
            addCriterion("TRADER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andTraderNameIsNotNull() {
            addCriterion("TRADER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andTraderNameEqualTo(String value) {
            addCriterion("TRADER_NAME =", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotEqualTo(String value) {
            addCriterion("TRADER_NAME <>", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameGreaterThan(String value) {
            addCriterion("TRADER_NAME >", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_NAME >=", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameLessThan(String value) {
            addCriterion("TRADER_NAME <", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameLessThanOrEqualTo(String value) {
            addCriterion("TRADER_NAME <=", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameLike(String value) {
            addCriterion("TRADER_NAME like", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotLike(String value) {
            addCriterion("TRADER_NAME not like", value, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameIn(List<String> values) {
            addCriterion("TRADER_NAME in", values, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotIn(List<String> values) {
            addCriterion("TRADER_NAME not in", values, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameBetween(String value1, String value2) {
            addCriterion("TRADER_NAME between", value1, value2, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderNameNotBetween(String value1, String value2) {
            addCriterion("TRADER_NAME not between", value1, value2, "traderName");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdIsNull() {
            addCriterion("TRADER_CONTACT_ID is null");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdIsNotNull() {
            addCriterion("TRADER_CONTACT_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ID =", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdNotEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ID <>", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdGreaterThan(Integer value) {
            addCriterion("TRADER_CONTACT_ID >", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ID >=", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdLessThan(Integer value) {
            addCriterion("TRADER_CONTACT_ID <", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_CONTACT_ID <=", value, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdIn(List<Integer> values) {
            addCriterion("TRADER_CONTACT_ID in", values, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdNotIn(List<Integer> values) {
            addCriterion("TRADER_CONTACT_ID not in", values, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_CONTACT_ID between", value1, value2, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_CONTACT_ID not between", value1, value2, "traderContactId");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameIsNull() {
            addCriterion("TRADER_CONTACT_NAME is null");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameIsNotNull() {
            addCriterion("TRADER_CONTACT_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameEqualTo(String value) {
            addCriterion("TRADER_CONTACT_NAME =", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameNotEqualTo(String value) {
            addCriterion("TRADER_CONTACT_NAME <>", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameGreaterThan(String value) {
            addCriterion("TRADER_CONTACT_NAME >", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_NAME >=", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameLessThan(String value) {
            addCriterion("TRADER_CONTACT_NAME <", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameLessThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_NAME <=", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameLike(String value) {
            addCriterion("TRADER_CONTACT_NAME like", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameNotLike(String value) {
            addCriterion("TRADER_CONTACT_NAME not like", value, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameIn(List<String> values) {
            addCriterion("TRADER_CONTACT_NAME in", values, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameNotIn(List<String> values) {
            addCriterion("TRADER_CONTACT_NAME not in", values, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_NAME between", value1, value2, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactNameNotBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_NAME not between", value1, value2, "traderContactName");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileIsNull() {
            addCriterion("TRADER_CONTACT_MOBILE is null");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileIsNotNull() {
            addCriterion("TRADER_CONTACT_MOBILE is not null");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileEqualTo(String value) {
            addCriterion("TRADER_CONTACT_MOBILE =", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileNotEqualTo(String value) {
            addCriterion("TRADER_CONTACT_MOBILE <>", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileGreaterThan(String value) {
            addCriterion("TRADER_CONTACT_MOBILE >", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_MOBILE >=", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileLessThan(String value) {
            addCriterion("TRADER_CONTACT_MOBILE <", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileLessThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_MOBILE <=", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileLike(String value) {
            addCriterion("TRADER_CONTACT_MOBILE like", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileNotLike(String value) {
            addCriterion("TRADER_CONTACT_MOBILE not like", value, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileIn(List<String> values) {
            addCriterion("TRADER_CONTACT_MOBILE in", values, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileNotIn(List<String> values) {
            addCriterion("TRADER_CONTACT_MOBILE not in", values, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_MOBILE between", value1, value2, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactMobileNotBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_MOBILE not between", value1, value2, "traderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneIsNull() {
            addCriterion("TRADER_CONTACT_TELEPHONE is null");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneIsNotNull() {
            addCriterion("TRADER_CONTACT_TELEPHONE is not null");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneEqualTo(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE =", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneNotEqualTo(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE <>", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneGreaterThan(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE >", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE >=", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneLessThan(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE <", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneLessThanOrEqualTo(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE <=", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneLike(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE like", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneNotLike(String value) {
            addCriterion("TRADER_CONTACT_TELEPHONE not like", value, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneIn(List<String> values) {
            addCriterion("TRADER_CONTACT_TELEPHONE in", values, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneNotIn(List<String> values) {
            addCriterion("TRADER_CONTACT_TELEPHONE not in", values, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_TELEPHONE between", value1, value2, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderContactTelephoneNotBetween(String value1, String value2) {
            addCriterion("TRADER_CONTACT_TELEPHONE not between", value1, value2, "traderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdIsNull() {
            addCriterion("TRADER_ADDRESS_ID is null");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdIsNotNull() {
            addCriterion("TRADER_ADDRESS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdEqualTo(Integer value) {
            addCriterion("TRADER_ADDRESS_ID =", value, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdNotEqualTo(Integer value) {
            addCriterion("TRADER_ADDRESS_ID <>", value, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdGreaterThan(Integer value) {
            addCriterion("TRADER_ADDRESS_ID >", value, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_ADDRESS_ID >=", value, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdLessThan(Integer value) {
            addCriterion("TRADER_ADDRESS_ID <", value, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_ADDRESS_ID <=", value, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdIn(List<Integer> values) {
            addCriterion("TRADER_ADDRESS_ID in", values, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdNotIn(List<Integer> values) {
            addCriterion("TRADER_ADDRESS_ID not in", values, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_ADDRESS_ID between", value1, value2, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_ADDRESS_ID not between", value1, value2, "traderAddressId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdIsNull() {
            addCriterion("TRADER_AREA_ID is null");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdIsNotNull() {
            addCriterion("TRADER_AREA_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdEqualTo(Integer value) {
            addCriterion("TRADER_AREA_ID =", value, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdNotEqualTo(Integer value) {
            addCriterion("TRADER_AREA_ID <>", value, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdGreaterThan(Integer value) {
            addCriterion("TRADER_AREA_ID >", value, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TRADER_AREA_ID >=", value, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdLessThan(Integer value) {
            addCriterion("TRADER_AREA_ID <", value, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdLessThanOrEqualTo(Integer value) {
            addCriterion("TRADER_AREA_ID <=", value, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdIn(List<Integer> values) {
            addCriterion("TRADER_AREA_ID in", values, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdNotIn(List<Integer> values) {
            addCriterion("TRADER_AREA_ID not in", values, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_AREA_ID between", value1, value2, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TRADER_AREA_ID not between", value1, value2, "traderAreaId");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIsNull() {
            addCriterion("TRADER_AREA is null");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIsNotNull() {
            addCriterion("TRADER_AREA is not null");
            return (Criteria) this;
        }

        public Criteria andTraderAreaEqualTo(String value) {
            addCriterion("TRADER_AREA =", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaNotEqualTo(String value) {
            addCriterion("TRADER_AREA <>", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaGreaterThan(String value) {
            addCriterion("TRADER_AREA >", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_AREA >=", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaLessThan(String value) {
            addCriterion("TRADER_AREA <", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaLessThanOrEqualTo(String value) {
            addCriterion("TRADER_AREA <=", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaLike(String value) {
            addCriterion("TRADER_AREA like", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaNotLike(String value) {
            addCriterion("TRADER_AREA not like", value, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaIn(List<String> values) {
            addCriterion("TRADER_AREA in", values, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaNotIn(List<String> values) {
            addCriterion("TRADER_AREA not in", values, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaBetween(String value1, String value2) {
            addCriterion("TRADER_AREA between", value1, value2, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAreaNotBetween(String value1, String value2) {
            addCriterion("TRADER_AREA not between", value1, value2, "traderArea");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIsNull() {
            addCriterion("TRADER_ADDRESS is null");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIsNotNull() {
            addCriterion("TRADER_ADDRESS is not null");
            return (Criteria) this;
        }

        public Criteria andTraderAddressEqualTo(String value) {
            addCriterion("TRADER_ADDRESS =", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressNotEqualTo(String value) {
            addCriterion("TRADER_ADDRESS <>", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressGreaterThan(String value) {
            addCriterion("TRADER_ADDRESS >", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_ADDRESS >=", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressLessThan(String value) {
            addCriterion("TRADER_ADDRESS <", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressLessThanOrEqualTo(String value) {
            addCriterion("TRADER_ADDRESS <=", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressLike(String value) {
            addCriterion("TRADER_ADDRESS like", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressNotLike(String value) {
            addCriterion("TRADER_ADDRESS not like", value, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressIn(List<String> values) {
            addCriterion("TRADER_ADDRESS in", values, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressNotIn(List<String> values) {
            addCriterion("TRADER_ADDRESS not in", values, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressBetween(String value1, String value2) {
            addCriterion("TRADER_ADDRESS between", value1, value2, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderAddressNotBetween(String value1, String value2) {
            addCriterion("TRADER_ADDRESS not between", value1, value2, "traderAddress");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsIsNull() {
            addCriterion("TRADER_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsIsNotNull() {
            addCriterion("TRADER_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsEqualTo(String value) {
            addCriterion("TRADER_COMMENTS =", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsNotEqualTo(String value) {
            addCriterion("TRADER_COMMENTS <>", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsGreaterThan(String value) {
            addCriterion("TRADER_COMMENTS >", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("TRADER_COMMENTS >=", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsLessThan(String value) {
            addCriterion("TRADER_COMMENTS <", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsLessThanOrEqualTo(String value) {
            addCriterion("TRADER_COMMENTS <=", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsLike(String value) {
            addCriterion("TRADER_COMMENTS like", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsNotLike(String value) {
            addCriterion("TRADER_COMMENTS not like", value, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsIn(List<String> values) {
            addCriterion("TRADER_COMMENTS in", values, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsNotIn(List<String> values) {
            addCriterion("TRADER_COMMENTS not in", values, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsBetween(String value1, String value2) {
            addCriterion("TRADER_COMMENTS between", value1, value2, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTraderCommentsNotBetween(String value1, String value2) {
            addCriterion("TRADER_COMMENTS not between", value1, value2, "traderComments");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdIsNull() {
            addCriterion("TAKE_TRADER_ID is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdIsNotNull() {
            addCriterion("TAKE_TRADER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ID =", value, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdNotEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ID <>", value, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdGreaterThan(Integer value) {
            addCriterion("TAKE_TRADER_ID >", value, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ID >=", value, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdLessThan(Integer value) {
            addCriterion("TAKE_TRADER_ID <", value, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdLessThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ID <=", value, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_ID in", values, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdNotIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_ID not in", values, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_ID between", value1, value2, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_ID not between", value1, value2, "takeTraderId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameIsNull() {
            addCriterion("TAKE_TRADER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameIsNotNull() {
            addCriterion("TAKE_TRADER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameEqualTo(String value) {
            addCriterion("TAKE_TRADER_NAME =", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameNotEqualTo(String value) {
            addCriterion("TAKE_TRADER_NAME <>", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameGreaterThan(String value) {
            addCriterion("TAKE_TRADER_NAME >", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameGreaterThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_NAME >=", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameLessThan(String value) {
            addCriterion("TAKE_TRADER_NAME <", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameLessThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_NAME <=", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameLike(String value) {
            addCriterion("TAKE_TRADER_NAME like", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameNotLike(String value) {
            addCriterion("TAKE_TRADER_NAME not like", value, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameIn(List<String> values) {
            addCriterion("TAKE_TRADER_NAME in", values, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameNotIn(List<String> values) {
            addCriterion("TAKE_TRADER_NAME not in", values, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_NAME between", value1, value2, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderNameNotBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_NAME not between", value1, value2, "takeTraderName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdIsNull() {
            addCriterion("TAKE_TRADER_CONTACT_ID is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdIsNotNull() {
            addCriterion("TAKE_TRADER_CONTACT_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_CONTACT_ID =", value, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdNotEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_CONTACT_ID <>", value, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdGreaterThan(Integer value) {
            addCriterion("TAKE_TRADER_CONTACT_ID >", value, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_CONTACT_ID >=", value, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdLessThan(Integer value) {
            addCriterion("TAKE_TRADER_CONTACT_ID <", value, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdLessThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_CONTACT_ID <=", value, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_CONTACT_ID in", values, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdNotIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_CONTACT_ID not in", values, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_CONTACT_ID between", value1, value2, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_CONTACT_ID not between", value1, value2, "takeTraderContactId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameIsNull() {
            addCriterion("TAKE_TRADER_CONTACT_NAME is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameIsNotNull() {
            addCriterion("TAKE_TRADER_CONTACT_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME =", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameNotEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME <>", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameGreaterThan(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME >", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameGreaterThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME >=", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameLessThan(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME <", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameLessThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME <=", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameLike(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME like", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameNotLike(String value) {
            addCriterion("TAKE_TRADER_CONTACT_NAME not like", value, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameIn(List<String> values) {
            addCriterion("TAKE_TRADER_CONTACT_NAME in", values, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameNotIn(List<String> values) {
            addCriterion("TAKE_TRADER_CONTACT_NAME not in", values, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_CONTACT_NAME between", value1, value2, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactNameNotBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_CONTACT_NAME not between", value1, value2, "takeTraderContactName");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileIsNull() {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileIsNotNull() {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE =", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileNotEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE <>", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileGreaterThan(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE >", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileGreaterThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE >=", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileLessThan(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE <", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileLessThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE <=", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileLike(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE like", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileNotLike(String value) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE not like", value, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileIn(List<String> values) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE in", values, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileNotIn(List<String> values) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE not in", values, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE between", value1, value2, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactMobileNotBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_CONTACT_MOBILE not between", value1, value2, "takeTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneIsNull() {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneIsNotNull() {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE =", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneNotEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE <>", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneGreaterThan(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE >", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneGreaterThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE >=", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneLessThan(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE <", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneLessThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE <=", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneLike(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE like", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneNotLike(String value) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE not like", value, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneIn(List<String> values) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE in", values, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneNotIn(List<String> values) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE not in", values, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE between", value1, value2, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderContactTelephoneNotBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_CONTACT_TELEPHONE not between", value1, value2, "takeTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdIsNull() {
            addCriterion("TAKE_TRADER_ADDRESS_ID is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdIsNotNull() {
            addCriterion("TAKE_TRADER_ADDRESS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ADDRESS_ID =", value, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdNotEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ADDRESS_ID <>", value, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdGreaterThan(Integer value) {
            addCriterion("TAKE_TRADER_ADDRESS_ID >", value, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ADDRESS_ID >=", value, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdLessThan(Integer value) {
            addCriterion("TAKE_TRADER_ADDRESS_ID <", value, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdLessThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_ADDRESS_ID <=", value, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_ADDRESS_ID in", values, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdNotIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_ADDRESS_ID not in", values, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_ADDRESS_ID between", value1, value2, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_ADDRESS_ID not between", value1, value2, "takeTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdIsNull() {
            addCriterion("TAKE_TRADER_AREA_ID is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdIsNotNull() {
            addCriterion("TAKE_TRADER_AREA_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_AREA_ID =", value, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdNotEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_AREA_ID <>", value, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdGreaterThan(Integer value) {
            addCriterion("TAKE_TRADER_AREA_ID >", value, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_AREA_ID >=", value, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdLessThan(Integer value) {
            addCriterion("TAKE_TRADER_AREA_ID <", value, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdLessThanOrEqualTo(Integer value) {
            addCriterion("TAKE_TRADER_AREA_ID <=", value, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_AREA_ID in", values, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdNotIn(List<Integer> values) {
            addCriterion("TAKE_TRADER_AREA_ID not in", values, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_AREA_ID between", value1, value2, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TAKE_TRADER_AREA_ID not between", value1, value2, "takeTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIsNull() {
            addCriterion("TAKE_TRADER_AREA is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIsNotNull() {
            addCriterion("TAKE_TRADER_AREA is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaEqualTo(String value) {
            addCriterion("TAKE_TRADER_AREA =", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaNotEqualTo(String value) {
            addCriterion("TAKE_TRADER_AREA <>", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaGreaterThan(String value) {
            addCriterion("TAKE_TRADER_AREA >", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaGreaterThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_AREA >=", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaLessThan(String value) {
            addCriterion("TAKE_TRADER_AREA <", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaLessThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_AREA <=", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaLike(String value) {
            addCriterion("TAKE_TRADER_AREA like", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaNotLike(String value) {
            addCriterion("TAKE_TRADER_AREA not like", value, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaIn(List<String> values) {
            addCriterion("TAKE_TRADER_AREA in", values, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaNotIn(List<String> values) {
            addCriterion("TAKE_TRADER_AREA not in", values, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_AREA between", value1, value2, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAreaNotBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_AREA not between", value1, value2, "takeTraderArea");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIsNull() {
            addCriterion("TAKE_TRADER_ADDRESS is null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIsNotNull() {
            addCriterion("TAKE_TRADER_ADDRESS is not null");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressEqualTo(String value) {
            addCriterion("TAKE_TRADER_ADDRESS =", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressNotEqualTo(String value) {
            addCriterion("TAKE_TRADER_ADDRESS <>", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressGreaterThan(String value) {
            addCriterion("TAKE_TRADER_ADDRESS >", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressGreaterThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_ADDRESS >=", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressLessThan(String value) {
            addCriterion("TAKE_TRADER_ADDRESS <", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressLessThanOrEqualTo(String value) {
            addCriterion("TAKE_TRADER_ADDRESS <=", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressLike(String value) {
            addCriterion("TAKE_TRADER_ADDRESS like", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressNotLike(String value) {
            addCriterion("TAKE_TRADER_ADDRESS not like", value, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressIn(List<String> values) {
            addCriterion("TAKE_TRADER_ADDRESS in", values, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressNotIn(List<String> values) {
            addCriterion("TAKE_TRADER_ADDRESS not in", values, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_ADDRESS between", value1, value2, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andTakeTraderAddressNotBetween(String value1, String value2) {
            addCriterion("TAKE_TRADER_ADDRESS not between", value1, value2, "takeTraderAddress");
            return (Criteria) this;
        }

        public Criteria andIsSendInvoiceIsNull() {
            addCriterion("IS_SEND_INVOICE is null");
            return (Criteria) this;
        }

        public Criteria andIsSendInvoiceIsNotNull() {
            addCriterion("IS_SEND_INVOICE is not null");
            return (Criteria) this;
        }

        public Criteria andIsSendInvoiceEqualTo(Byte value) {
            addCriterion("IS_SEND_INVOICE =", value, "isSendInvoice");
            return (Criteria) this;
        }

        public Criteria andIsSendInvoiceNotEqualTo(Byte value) {
            addCriterion("IS_SEND_INVOICE <>", value, "isSendInvoice");
            return (Criteria) this;
        }

        public Criteria andIsSendInvoiceGreaterThan(Byte value) {
            addCriterion("IS_SEND_INVOICE >", value, "isSendInvoice");
            return (Criteria) this;
        }

        public Criteria andIsSendInvoiceGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_SEND_INVOICE >=", value, "isSendInvoice");
            return (Criteria) this;
        }

        public Criteria andIsSendInvoiceLessThan(Byte value) {
            addCriterion("IS_SEND_INVOICE <", value, "isSendInvoice");
            return (Criteria) this;
        }

        public Criteria andIsSendInvoiceLessThanOrEqualTo(Byte value) {
            addCriterion("IS_SEND_INVOICE <=", value, "isSendInvoice");
            return (Criteria) this;
        }

        public Criteria andIsSendInvoiceIn(List<Byte> values) {
            addCriterion("IS_SEND_INVOICE in", values, "isSendInvoice");
            return (Criteria) this;
        }

        public Criteria andIsSendInvoiceNotIn(List<Byte> values) {
            addCriterion("IS_SEND_INVOICE not in", values, "isSendInvoice");
            return (Criteria) this;
        }

        public Criteria andIsSendInvoiceBetween(Byte value1, Byte value2) {
            addCriterion("IS_SEND_INVOICE between", value1, value2, "isSendInvoice");
            return (Criteria) this;
        }

        public Criteria andIsSendInvoiceNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_SEND_INVOICE not between", value1, value2, "isSendInvoice");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderIdIsNull() {
            addCriterion("INVOICE_TRADER_ID is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderIdIsNotNull() {
            addCriterion("INVOICE_TRADER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderIdEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_ID =", value, "invoiceTraderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderIdNotEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_ID <>", value, "invoiceTraderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderIdGreaterThan(Integer value) {
            addCriterion("INVOICE_TRADER_ID >", value, "invoiceTraderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_ID >=", value, "invoiceTraderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderIdLessThan(Integer value) {
            addCriterion("INVOICE_TRADER_ID <", value, "invoiceTraderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderIdLessThanOrEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_ID <=", value, "invoiceTraderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderIdIn(List<Integer> values) {
            addCriterion("INVOICE_TRADER_ID in", values, "invoiceTraderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderIdNotIn(List<Integer> values) {
            addCriterion("INVOICE_TRADER_ID not in", values, "invoiceTraderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderIdBetween(Integer value1, Integer value2) {
            addCriterion("INVOICE_TRADER_ID between", value1, value2, "invoiceTraderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("INVOICE_TRADER_ID not between", value1, value2, "invoiceTraderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderNameIsNull() {
            addCriterion("INVOICE_TRADER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderNameIsNotNull() {
            addCriterion("INVOICE_TRADER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderNameEqualTo(String value) {
            addCriterion("INVOICE_TRADER_NAME =", value, "invoiceTraderName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderNameNotEqualTo(String value) {
            addCriterion("INVOICE_TRADER_NAME <>", value, "invoiceTraderName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderNameGreaterThan(String value) {
            addCriterion("INVOICE_TRADER_NAME >", value, "invoiceTraderName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderNameGreaterThanOrEqualTo(String value) {
            addCriterion("INVOICE_TRADER_NAME >=", value, "invoiceTraderName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderNameLessThan(String value) {
            addCriterion("INVOICE_TRADER_NAME <", value, "invoiceTraderName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderNameLessThanOrEqualTo(String value) {
            addCriterion("INVOICE_TRADER_NAME <=", value, "invoiceTraderName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderNameLike(String value) {
            addCriterion("INVOICE_TRADER_NAME like", value, "invoiceTraderName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderNameNotLike(String value) {
            addCriterion("INVOICE_TRADER_NAME not like", value, "invoiceTraderName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderNameIn(List<String> values) {
            addCriterion("INVOICE_TRADER_NAME in", values, "invoiceTraderName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderNameNotIn(List<String> values) {
            addCriterion("INVOICE_TRADER_NAME not in", values, "invoiceTraderName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderNameBetween(String value1, String value2) {
            addCriterion("INVOICE_TRADER_NAME between", value1, value2, "invoiceTraderName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderNameNotBetween(String value1, String value2) {
            addCriterion("INVOICE_TRADER_NAME not between", value1, value2, "invoiceTraderName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactIdIsNull() {
            addCriterion("INVOICE_TRADER_CONTACT_ID is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactIdIsNotNull() {
            addCriterion("INVOICE_TRADER_CONTACT_ID is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactIdEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_CONTACT_ID =", value, "invoiceTraderContactId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactIdNotEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_CONTACT_ID <>", value, "invoiceTraderContactId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactIdGreaterThan(Integer value) {
            addCriterion("INVOICE_TRADER_CONTACT_ID >", value, "invoiceTraderContactId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_CONTACT_ID >=", value, "invoiceTraderContactId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactIdLessThan(Integer value) {
            addCriterion("INVOICE_TRADER_CONTACT_ID <", value, "invoiceTraderContactId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactIdLessThanOrEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_CONTACT_ID <=", value, "invoiceTraderContactId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactIdIn(List<Integer> values) {
            addCriterion("INVOICE_TRADER_CONTACT_ID in", values, "invoiceTraderContactId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactIdNotIn(List<Integer> values) {
            addCriterion("INVOICE_TRADER_CONTACT_ID not in", values, "invoiceTraderContactId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactIdBetween(Integer value1, Integer value2) {
            addCriterion("INVOICE_TRADER_CONTACT_ID between", value1, value2, "invoiceTraderContactId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactIdNotBetween(Integer value1, Integer value2) {
            addCriterion("INVOICE_TRADER_CONTACT_ID not between", value1, value2, "invoiceTraderContactId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactNameIsNull() {
            addCriterion("INVOICE_TRADER_CONTACT_NAME is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactNameIsNotNull() {
            addCriterion("INVOICE_TRADER_CONTACT_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactNameEqualTo(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_NAME =", value, "invoiceTraderContactName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactNameNotEqualTo(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_NAME <>", value, "invoiceTraderContactName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactNameGreaterThan(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_NAME >", value, "invoiceTraderContactName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactNameGreaterThanOrEqualTo(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_NAME >=", value, "invoiceTraderContactName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactNameLessThan(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_NAME <", value, "invoiceTraderContactName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactNameLessThanOrEqualTo(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_NAME <=", value, "invoiceTraderContactName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactNameLike(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_NAME like", value, "invoiceTraderContactName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactNameNotLike(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_NAME not like", value, "invoiceTraderContactName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactNameIn(List<String> values) {
            addCriterion("INVOICE_TRADER_CONTACT_NAME in", values, "invoiceTraderContactName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactNameNotIn(List<String> values) {
            addCriterion("INVOICE_TRADER_CONTACT_NAME not in", values, "invoiceTraderContactName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactNameBetween(String value1, String value2) {
            addCriterion("INVOICE_TRADER_CONTACT_NAME between", value1, value2, "invoiceTraderContactName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactNameNotBetween(String value1, String value2) {
            addCriterion("INVOICE_TRADER_CONTACT_NAME not between", value1, value2, "invoiceTraderContactName");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactMobileIsNull() {
            addCriterion("INVOICE_TRADER_CONTACT_MOBILE is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactMobileIsNotNull() {
            addCriterion("INVOICE_TRADER_CONTACT_MOBILE is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactMobileEqualTo(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_MOBILE =", value, "invoiceTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactMobileNotEqualTo(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_MOBILE <>", value, "invoiceTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactMobileGreaterThan(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_MOBILE >", value, "invoiceTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactMobileGreaterThanOrEqualTo(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_MOBILE >=", value, "invoiceTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactMobileLessThan(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_MOBILE <", value, "invoiceTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactMobileLessThanOrEqualTo(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_MOBILE <=", value, "invoiceTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactMobileLike(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_MOBILE like", value, "invoiceTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactMobileNotLike(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_MOBILE not like", value, "invoiceTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactMobileIn(List<String> values) {
            addCriterion("INVOICE_TRADER_CONTACT_MOBILE in", values, "invoiceTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactMobileNotIn(List<String> values) {
            addCriterion("INVOICE_TRADER_CONTACT_MOBILE not in", values, "invoiceTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactMobileBetween(String value1, String value2) {
            addCriterion("INVOICE_TRADER_CONTACT_MOBILE between", value1, value2, "invoiceTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactMobileNotBetween(String value1, String value2) {
            addCriterion("INVOICE_TRADER_CONTACT_MOBILE not between", value1, value2, "invoiceTraderContactMobile");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactTelephoneIsNull() {
            addCriterion("INVOICE_TRADER_CONTACT_TELEPHONE is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactTelephoneIsNotNull() {
            addCriterion("INVOICE_TRADER_CONTACT_TELEPHONE is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactTelephoneEqualTo(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_TELEPHONE =", value, "invoiceTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactTelephoneNotEqualTo(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_TELEPHONE <>", value, "invoiceTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactTelephoneGreaterThan(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_TELEPHONE >", value, "invoiceTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactTelephoneGreaterThanOrEqualTo(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_TELEPHONE >=", value, "invoiceTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactTelephoneLessThan(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_TELEPHONE <", value, "invoiceTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactTelephoneLessThanOrEqualTo(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_TELEPHONE <=", value, "invoiceTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactTelephoneLike(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_TELEPHONE like", value, "invoiceTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactTelephoneNotLike(String value) {
            addCriterion("INVOICE_TRADER_CONTACT_TELEPHONE not like", value, "invoiceTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactTelephoneIn(List<String> values) {
            addCriterion("INVOICE_TRADER_CONTACT_TELEPHONE in", values, "invoiceTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactTelephoneNotIn(List<String> values) {
            addCriterion("INVOICE_TRADER_CONTACT_TELEPHONE not in", values, "invoiceTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactTelephoneBetween(String value1, String value2) {
            addCriterion("INVOICE_TRADER_CONTACT_TELEPHONE between", value1, value2, "invoiceTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderContactTelephoneNotBetween(String value1, String value2) {
            addCriterion("INVOICE_TRADER_CONTACT_TELEPHONE not between", value1, value2, "invoiceTraderContactTelephone");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressIdIsNull() {
            addCriterion("INVOICE_TRADER_ADDRESS_ID is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressIdIsNotNull() {
            addCriterion("INVOICE_TRADER_ADDRESS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressIdEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_ADDRESS_ID =", value, "invoiceTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressIdNotEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_ADDRESS_ID <>", value, "invoiceTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressIdGreaterThan(Integer value) {
            addCriterion("INVOICE_TRADER_ADDRESS_ID >", value, "invoiceTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_ADDRESS_ID >=", value, "invoiceTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressIdLessThan(Integer value) {
            addCriterion("INVOICE_TRADER_ADDRESS_ID <", value, "invoiceTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressIdLessThanOrEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_ADDRESS_ID <=", value, "invoiceTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressIdIn(List<Integer> values) {
            addCriterion("INVOICE_TRADER_ADDRESS_ID in", values, "invoiceTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressIdNotIn(List<Integer> values) {
            addCriterion("INVOICE_TRADER_ADDRESS_ID not in", values, "invoiceTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressIdBetween(Integer value1, Integer value2) {
            addCriterion("INVOICE_TRADER_ADDRESS_ID between", value1, value2, "invoiceTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressIdNotBetween(Integer value1, Integer value2) {
            addCriterion("INVOICE_TRADER_ADDRESS_ID not between", value1, value2, "invoiceTraderAddressId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaIdIsNull() {
            addCriterion("INVOICE_TRADER_AREA_ID is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaIdIsNotNull() {
            addCriterion("INVOICE_TRADER_AREA_ID is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaIdEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_AREA_ID =", value, "invoiceTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaIdNotEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_AREA_ID <>", value, "invoiceTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaIdGreaterThan(Integer value) {
            addCriterion("INVOICE_TRADER_AREA_ID >", value, "invoiceTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_AREA_ID >=", value, "invoiceTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaIdLessThan(Integer value) {
            addCriterion("INVOICE_TRADER_AREA_ID <", value, "invoiceTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaIdLessThanOrEqualTo(Integer value) {
            addCriterion("INVOICE_TRADER_AREA_ID <=", value, "invoiceTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaIdIn(List<Integer> values) {
            addCriterion("INVOICE_TRADER_AREA_ID in", values, "invoiceTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaIdNotIn(List<Integer> values) {
            addCriterion("INVOICE_TRADER_AREA_ID not in", values, "invoiceTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaIdBetween(Integer value1, Integer value2) {
            addCriterion("INVOICE_TRADER_AREA_ID between", value1, value2, "invoiceTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaIdNotBetween(Integer value1, Integer value2) {
            addCriterion("INVOICE_TRADER_AREA_ID not between", value1, value2, "invoiceTraderAreaId");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaIsNull() {
            addCriterion("INVOICE_TRADER_AREA is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaIsNotNull() {
            addCriterion("INVOICE_TRADER_AREA is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaEqualTo(String value) {
            addCriterion("INVOICE_TRADER_AREA =", value, "invoiceTraderArea");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaNotEqualTo(String value) {
            addCriterion("INVOICE_TRADER_AREA <>", value, "invoiceTraderArea");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaGreaterThan(String value) {
            addCriterion("INVOICE_TRADER_AREA >", value, "invoiceTraderArea");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaGreaterThanOrEqualTo(String value) {
            addCriterion("INVOICE_TRADER_AREA >=", value, "invoiceTraderArea");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaLessThan(String value) {
            addCriterion("INVOICE_TRADER_AREA <", value, "invoiceTraderArea");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaLessThanOrEqualTo(String value) {
            addCriterion("INVOICE_TRADER_AREA <=", value, "invoiceTraderArea");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaLike(String value) {
            addCriterion("INVOICE_TRADER_AREA like", value, "invoiceTraderArea");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaNotLike(String value) {
            addCriterion("INVOICE_TRADER_AREA not like", value, "invoiceTraderArea");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaIn(List<String> values) {
            addCriterion("INVOICE_TRADER_AREA in", values, "invoiceTraderArea");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaNotIn(List<String> values) {
            addCriterion("INVOICE_TRADER_AREA not in", values, "invoiceTraderArea");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaBetween(String value1, String value2) {
            addCriterion("INVOICE_TRADER_AREA between", value1, value2, "invoiceTraderArea");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAreaNotBetween(String value1, String value2) {
            addCriterion("INVOICE_TRADER_AREA not between", value1, value2, "invoiceTraderArea");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressIsNull() {
            addCriterion("INVOICE_TRADER_ADDRESS is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressIsNotNull() {
            addCriterion("INVOICE_TRADER_ADDRESS is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressEqualTo(String value) {
            addCriterion("INVOICE_TRADER_ADDRESS =", value, "invoiceTraderAddress");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressNotEqualTo(String value) {
            addCriterion("INVOICE_TRADER_ADDRESS <>", value, "invoiceTraderAddress");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressGreaterThan(String value) {
            addCriterion("INVOICE_TRADER_ADDRESS >", value, "invoiceTraderAddress");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressGreaterThanOrEqualTo(String value) {
            addCriterion("INVOICE_TRADER_ADDRESS >=", value, "invoiceTraderAddress");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressLessThan(String value) {
            addCriterion("INVOICE_TRADER_ADDRESS <", value, "invoiceTraderAddress");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressLessThanOrEqualTo(String value) {
            addCriterion("INVOICE_TRADER_ADDRESS <=", value, "invoiceTraderAddress");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressLike(String value) {
            addCriterion("INVOICE_TRADER_ADDRESS like", value, "invoiceTraderAddress");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressNotLike(String value) {
            addCriterion("INVOICE_TRADER_ADDRESS not like", value, "invoiceTraderAddress");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressIn(List<String> values) {
            addCriterion("INVOICE_TRADER_ADDRESS in", values, "invoiceTraderAddress");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressNotIn(List<String> values) {
            addCriterion("INVOICE_TRADER_ADDRESS not in", values, "invoiceTraderAddress");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressBetween(String value1, String value2) {
            addCriterion("INVOICE_TRADER_ADDRESS between", value1, value2, "invoiceTraderAddress");
            return (Criteria) this;
        }

        public Criteria andInvoiceTraderAddressNotBetween(String value1, String value2) {
            addCriterion("INVOICE_TRADER_ADDRESS not between", value1, value2, "invoiceTraderAddress");
            return (Criteria) this;
        }

        public Criteria andSalesAreaIdIsNull() {
            addCriterion("SALES_AREA_ID is null");
            return (Criteria) this;
        }

        public Criteria andSalesAreaIdIsNotNull() {
            addCriterion("SALES_AREA_ID is not null");
            return (Criteria) this;
        }

        public Criteria andSalesAreaIdEqualTo(Integer value) {
            addCriterion("SALES_AREA_ID =", value, "salesAreaId");
            return (Criteria) this;
        }

        public Criteria andSalesAreaIdNotEqualTo(Integer value) {
            addCriterion("SALES_AREA_ID <>", value, "salesAreaId");
            return (Criteria) this;
        }

        public Criteria andSalesAreaIdGreaterThan(Integer value) {
            addCriterion("SALES_AREA_ID >", value, "salesAreaId");
            return (Criteria) this;
        }

        public Criteria andSalesAreaIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("SALES_AREA_ID >=", value, "salesAreaId");
            return (Criteria) this;
        }

        public Criteria andSalesAreaIdLessThan(Integer value) {
            addCriterion("SALES_AREA_ID <", value, "salesAreaId");
            return (Criteria) this;
        }

        public Criteria andSalesAreaIdLessThanOrEqualTo(Integer value) {
            addCriterion("SALES_AREA_ID <=", value, "salesAreaId");
            return (Criteria) this;
        }

        public Criteria andSalesAreaIdIn(List<Integer> values) {
            addCriterion("SALES_AREA_ID in", values, "salesAreaId");
            return (Criteria) this;
        }

        public Criteria andSalesAreaIdNotIn(List<Integer> values) {
            addCriterion("SALES_AREA_ID not in", values, "salesAreaId");
            return (Criteria) this;
        }

        public Criteria andSalesAreaIdBetween(Integer value1, Integer value2) {
            addCriterion("SALES_AREA_ID between", value1, value2, "salesAreaId");
            return (Criteria) this;
        }

        public Criteria andSalesAreaIdNotBetween(Integer value1, Integer value2) {
            addCriterion("SALES_AREA_ID not between", value1, value2, "salesAreaId");
            return (Criteria) this;
        }

        public Criteria andSalesAreaIsNull() {
            addCriterion("SALES_AREA is null");
            return (Criteria) this;
        }

        public Criteria andSalesAreaIsNotNull() {
            addCriterion("SALES_AREA is not null");
            return (Criteria) this;
        }

        public Criteria andSalesAreaEqualTo(String value) {
            addCriterion("SALES_AREA =", value, "salesArea");
            return (Criteria) this;
        }

        public Criteria andSalesAreaNotEqualTo(String value) {
            addCriterion("SALES_AREA <>", value, "salesArea");
            return (Criteria) this;
        }

        public Criteria andSalesAreaGreaterThan(String value) {
            addCriterion("SALES_AREA >", value, "salesArea");
            return (Criteria) this;
        }

        public Criteria andSalesAreaGreaterThanOrEqualTo(String value) {
            addCriterion("SALES_AREA >=", value, "salesArea");
            return (Criteria) this;
        }

        public Criteria andSalesAreaLessThan(String value) {
            addCriterion("SALES_AREA <", value, "salesArea");
            return (Criteria) this;
        }

        public Criteria andSalesAreaLessThanOrEqualTo(String value) {
            addCriterion("SALES_AREA <=", value, "salesArea");
            return (Criteria) this;
        }

        public Criteria andSalesAreaLike(String value) {
            addCriterion("SALES_AREA like", value, "salesArea");
            return (Criteria) this;
        }

        public Criteria andSalesAreaNotLike(String value) {
            addCriterion("SALES_AREA not like", value, "salesArea");
            return (Criteria) this;
        }

        public Criteria andSalesAreaIn(List<String> values) {
            addCriterion("SALES_AREA in", values, "salesArea");
            return (Criteria) this;
        }

        public Criteria andSalesAreaNotIn(List<String> values) {
            addCriterion("SALES_AREA not in", values, "salesArea");
            return (Criteria) this;
        }

        public Criteria andSalesAreaBetween(String value1, String value2) {
            addCriterion("SALES_AREA between", value1, value2, "salesArea");
            return (Criteria) this;
        }

        public Criteria andSalesAreaNotBetween(String value1, String value2) {
            addCriterion("SALES_AREA not between", value1, value2, "salesArea");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderIdIsNull() {
            addCriterion("TERMINAL_TRADER_ID is null");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderIdIsNotNull() {
            addCriterion("TERMINAL_TRADER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderIdEqualTo(Integer value) {
            addCriterion("TERMINAL_TRADER_ID =", value, "terminalTraderId");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderIdNotEqualTo(Integer value) {
            addCriterion("TERMINAL_TRADER_ID <>", value, "terminalTraderId");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderIdGreaterThan(Integer value) {
            addCriterion("TERMINAL_TRADER_ID >", value, "terminalTraderId");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("TERMINAL_TRADER_ID >=", value, "terminalTraderId");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderIdLessThan(Integer value) {
            addCriterion("TERMINAL_TRADER_ID <", value, "terminalTraderId");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderIdLessThanOrEqualTo(Integer value) {
            addCriterion("TERMINAL_TRADER_ID <=", value, "terminalTraderId");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderIdIn(List<Integer> values) {
            addCriterion("TERMINAL_TRADER_ID in", values, "terminalTraderId");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderIdNotIn(List<Integer> values) {
            addCriterion("TERMINAL_TRADER_ID not in", values, "terminalTraderId");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderIdBetween(Integer value1, Integer value2) {
            addCriterion("TERMINAL_TRADER_ID between", value1, value2, "terminalTraderId");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("TERMINAL_TRADER_ID not between", value1, value2, "terminalTraderId");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameIsNull() {
            addCriterion("TERMINAL_TRADER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameIsNotNull() {
            addCriterion("TERMINAL_TRADER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameEqualTo(String value) {
            addCriterion("TERMINAL_TRADER_NAME =", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameNotEqualTo(String value) {
            addCriterion("TERMINAL_TRADER_NAME <>", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameGreaterThan(String value) {
            addCriterion("TERMINAL_TRADER_NAME >", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameGreaterThanOrEqualTo(String value) {
            addCriterion("TERMINAL_TRADER_NAME >=", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameLessThan(String value) {
            addCriterion("TERMINAL_TRADER_NAME <", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameLessThanOrEqualTo(String value) {
            addCriterion("TERMINAL_TRADER_NAME <=", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameLike(String value) {
            addCriterion("TERMINAL_TRADER_NAME like", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameNotLike(String value) {
            addCriterion("TERMINAL_TRADER_NAME not like", value, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameIn(List<String> values) {
            addCriterion("TERMINAL_TRADER_NAME in", values, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameNotIn(List<String> values) {
            addCriterion("TERMINAL_TRADER_NAME not in", values, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameBetween(String value1, String value2) {
            addCriterion("TERMINAL_TRADER_NAME between", value1, value2, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderNameNotBetween(String value1, String value2) {
            addCriterion("TERMINAL_TRADER_NAME not between", value1, value2, "terminalTraderName");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderTypeIsNull() {
            addCriterion("TERMINAL_TRADER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderTypeIsNotNull() {
            addCriterion("TERMINAL_TRADER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderTypeEqualTo(Integer value) {
            addCriterion("TERMINAL_TRADER_TYPE =", value, "terminalTraderType");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderTypeNotEqualTo(Integer value) {
            addCriterion("TERMINAL_TRADER_TYPE <>", value, "terminalTraderType");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderTypeGreaterThan(Integer value) {
            addCriterion("TERMINAL_TRADER_TYPE >", value, "terminalTraderType");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("TERMINAL_TRADER_TYPE >=", value, "terminalTraderType");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderTypeLessThan(Integer value) {
            addCriterion("TERMINAL_TRADER_TYPE <", value, "terminalTraderType");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderTypeLessThanOrEqualTo(Integer value) {
            addCriterion("TERMINAL_TRADER_TYPE <=", value, "terminalTraderType");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderTypeIn(List<Integer> values) {
            addCriterion("TERMINAL_TRADER_TYPE in", values, "terminalTraderType");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderTypeNotIn(List<Integer> values) {
            addCriterion("TERMINAL_TRADER_TYPE not in", values, "terminalTraderType");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderTypeBetween(Integer value1, Integer value2) {
            addCriterion("TERMINAL_TRADER_TYPE between", value1, value2, "terminalTraderType");
            return (Criteria) this;
        }

        public Criteria andTerminalTraderTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("TERMINAL_TRADER_TYPE not between", value1, value2, "terminalTraderType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIsNull() {
            addCriterion("INVOICE_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIsNotNull() {
            addCriterion("INVOICE_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeEqualTo(Integer value) {
            addCriterion("INVOICE_TYPE =", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotEqualTo(Integer value) {
            addCriterion("INVOICE_TYPE <>", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeGreaterThan(Integer value) {
            addCriterion("INVOICE_TYPE >", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("INVOICE_TYPE >=", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeLessThan(Integer value) {
            addCriterion("INVOICE_TYPE <", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeLessThanOrEqualTo(Integer value) {
            addCriterion("INVOICE_TYPE <=", value, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeIn(List<Integer> values) {
            addCriterion("INVOICE_TYPE in", values, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotIn(List<Integer> values) {
            addCriterion("INVOICE_TYPE not in", values, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeBetween(Integer value1, Integer value2) {
            addCriterion("INVOICE_TYPE between", value1, value2, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andInvoiceTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("INVOICE_TYPE not between", value1, value2, "invoiceType");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionIsNull() {
            addCriterion("FREIGHT_DESCRIPTION is null");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionIsNotNull() {
            addCriterion("FREIGHT_DESCRIPTION is not null");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionEqualTo(Integer value) {
            addCriterion("FREIGHT_DESCRIPTION =", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionNotEqualTo(Integer value) {
            addCriterion("FREIGHT_DESCRIPTION <>", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionGreaterThan(Integer value) {
            addCriterion("FREIGHT_DESCRIPTION >", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionGreaterThanOrEqualTo(Integer value) {
            addCriterion("FREIGHT_DESCRIPTION >=", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionLessThan(Integer value) {
            addCriterion("FREIGHT_DESCRIPTION <", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionLessThanOrEqualTo(Integer value) {
            addCriterion("FREIGHT_DESCRIPTION <=", value, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionIn(List<Integer> values) {
            addCriterion("FREIGHT_DESCRIPTION in", values, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionNotIn(List<Integer> values) {
            addCriterion("FREIGHT_DESCRIPTION not in", values, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionBetween(Integer value1, Integer value2) {
            addCriterion("FREIGHT_DESCRIPTION between", value1, value2, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andFreightDescriptionNotBetween(Integer value1, Integer value2) {
            addCriterion("FREIGHT_DESCRIPTION not between", value1, value2, "freightDescription");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeIsNull() {
            addCriterion("DELIVERY_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeIsNotNull() {
            addCriterion("DELIVERY_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeEqualTo(Integer value) {
            addCriterion("DELIVERY_TYPE =", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotEqualTo(Integer value) {
            addCriterion("DELIVERY_TYPE <>", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeGreaterThan(Integer value) {
            addCriterion("DELIVERY_TYPE >", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_TYPE >=", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeLessThan(Integer value) {
            addCriterion("DELIVERY_TYPE <", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeLessThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_TYPE <=", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeIn(List<Integer> values) {
            addCriterion("DELIVERY_TYPE in", values, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotIn(List<Integer> values) {
            addCriterion("DELIVERY_TYPE not in", values, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_TYPE between", value1, value2, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_TYPE not between", value1, value2, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdIsNull() {
            addCriterion("LOGISTICS_ID is null");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdIsNotNull() {
            addCriterion("LOGISTICS_ID is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdEqualTo(Integer value) {
            addCriterion("LOGISTICS_ID =", value, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdNotEqualTo(Integer value) {
            addCriterion("LOGISTICS_ID <>", value, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdGreaterThan(Integer value) {
            addCriterion("LOGISTICS_ID >", value, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("LOGISTICS_ID >=", value, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdLessThan(Integer value) {
            addCriterion("LOGISTICS_ID <", value, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdLessThanOrEqualTo(Integer value) {
            addCriterion("LOGISTICS_ID <=", value, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdIn(List<Integer> values) {
            addCriterion("LOGISTICS_ID in", values, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdNotIn(List<Integer> values) {
            addCriterion("LOGISTICS_ID not in", values, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdBetween(Integer value1, Integer value2) {
            addCriterion("LOGISTICS_ID between", value1, value2, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andLogisticsIdNotBetween(Integer value1, Integer value2) {
            addCriterion("LOGISTICS_ID not between", value1, value2, "logisticsId");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeIsNull() {
            addCriterion("PAYMENT_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeIsNotNull() {
            addCriterion("PAYMENT_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeEqualTo(Integer value) {
            addCriterion("PAYMENT_TYPE =", value, "paymentType");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeNotEqualTo(Integer value) {
            addCriterion("PAYMENT_TYPE <>", value, "paymentType");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeGreaterThan(Integer value) {
            addCriterion("PAYMENT_TYPE >", value, "paymentType");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("PAYMENT_TYPE >=", value, "paymentType");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeLessThan(Integer value) {
            addCriterion("PAYMENT_TYPE <", value, "paymentType");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeLessThanOrEqualTo(Integer value) {
            addCriterion("PAYMENT_TYPE <=", value, "paymentType");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeIn(List<Integer> values) {
            addCriterion("PAYMENT_TYPE in", values, "paymentType");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeNotIn(List<Integer> values) {
            addCriterion("PAYMENT_TYPE not in", values, "paymentType");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeBetween(Integer value1, Integer value2) {
            addCriterion("PAYMENT_TYPE between", value1, value2, "paymentType");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("PAYMENT_TYPE not between", value1, value2, "paymentType");
            return (Criteria) this;
        }

        public Criteria andPrepaidAmountIsNull() {
            addCriterion("PREPAID_AMOUNT is null");
            return (Criteria) this;
        }

        public Criteria andPrepaidAmountIsNotNull() {
            addCriterion("PREPAID_AMOUNT is not null");
            return (Criteria) this;
        }

        public Criteria andPrepaidAmountEqualTo(BigDecimal value) {
            addCriterion("PREPAID_AMOUNT =", value, "prepaidAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidAmountNotEqualTo(BigDecimal value) {
            addCriterion("PREPAID_AMOUNT <>", value, "prepaidAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidAmountGreaterThan(BigDecimal value) {
            addCriterion("PREPAID_AMOUNT >", value, "prepaidAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("PREPAID_AMOUNT >=", value, "prepaidAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidAmountLessThan(BigDecimal value) {
            addCriterion("PREPAID_AMOUNT <", value, "prepaidAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("PREPAID_AMOUNT <=", value, "prepaidAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidAmountIn(List<BigDecimal> values) {
            addCriterion("PREPAID_AMOUNT in", values, "prepaidAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidAmountNotIn(List<BigDecimal> values) {
            addCriterion("PREPAID_AMOUNT not in", values, "prepaidAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PREPAID_AMOUNT between", value1, value2, "prepaidAmount");
            return (Criteria) this;
        }

        public Criteria andPrepaidAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("PREPAID_AMOUNT not between", value1, value2, "prepaidAmount");
            return (Criteria) this;
        }

        public Criteria andAccountPeriodAmountIsNull() {
            addCriterion("ACCOUNT_PERIOD_AMOUNT is null");
            return (Criteria) this;
        }

        public Criteria andAccountPeriodAmountIsNotNull() {
            addCriterion("ACCOUNT_PERIOD_AMOUNT is not null");
            return (Criteria) this;
        }

        public Criteria andAccountPeriodAmountEqualTo(BigDecimal value) {
            addCriterion("ACCOUNT_PERIOD_AMOUNT =", value, "accountPeriodAmount");
            return (Criteria) this;
        }

        public Criteria andAccountPeriodAmountNotEqualTo(BigDecimal value) {
            addCriterion("ACCOUNT_PERIOD_AMOUNT <>", value, "accountPeriodAmount");
            return (Criteria) this;
        }

        public Criteria andAccountPeriodAmountGreaterThan(BigDecimal value) {
            addCriterion("ACCOUNT_PERIOD_AMOUNT >", value, "accountPeriodAmount");
            return (Criteria) this;
        }

        public Criteria andAccountPeriodAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ACCOUNT_PERIOD_AMOUNT >=", value, "accountPeriodAmount");
            return (Criteria) this;
        }

        public Criteria andAccountPeriodAmountLessThan(BigDecimal value) {
            addCriterion("ACCOUNT_PERIOD_AMOUNT <", value, "accountPeriodAmount");
            return (Criteria) this;
        }

        public Criteria andAccountPeriodAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ACCOUNT_PERIOD_AMOUNT <=", value, "accountPeriodAmount");
            return (Criteria) this;
        }

        public Criteria andAccountPeriodAmountIn(List<BigDecimal> values) {
            addCriterion("ACCOUNT_PERIOD_AMOUNT in", values, "accountPeriodAmount");
            return (Criteria) this;
        }

        public Criteria andAccountPeriodAmountNotIn(List<BigDecimal> values) {
            addCriterion("ACCOUNT_PERIOD_AMOUNT not in", values, "accountPeriodAmount");
            return (Criteria) this;
        }

        public Criteria andAccountPeriodAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ACCOUNT_PERIOD_AMOUNT between", value1, value2, "accountPeriodAmount");
            return (Criteria) this;
        }

        public Criteria andAccountPeriodAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ACCOUNT_PERIOD_AMOUNT not between", value1, value2, "accountPeriodAmount");
            return (Criteria) this;
        }

        public Criteria andPeriodDayIsNull() {
            addCriterion("PERIOD_DAY is null");
            return (Criteria) this;
        }

        public Criteria andPeriodDayIsNotNull() {
            addCriterion("PERIOD_DAY is not null");
            return (Criteria) this;
        }

        public Criteria andPeriodDayEqualTo(Integer value) {
            addCriterion("PERIOD_DAY =", value, "periodDay");
            return (Criteria) this;
        }

        public Criteria andPeriodDayNotEqualTo(Integer value) {
            addCriterion("PERIOD_DAY <>", value, "periodDay");
            return (Criteria) this;
        }

        public Criteria andPeriodDayGreaterThan(Integer value) {
            addCriterion("PERIOD_DAY >", value, "periodDay");
            return (Criteria) this;
        }

        public Criteria andPeriodDayGreaterThanOrEqualTo(Integer value) {
            addCriterion("PERIOD_DAY >=", value, "periodDay");
            return (Criteria) this;
        }

        public Criteria andPeriodDayLessThan(Integer value) {
            addCriterion("PERIOD_DAY <", value, "periodDay");
            return (Criteria) this;
        }

        public Criteria andPeriodDayLessThanOrEqualTo(Integer value) {
            addCriterion("PERIOD_DAY <=", value, "periodDay");
            return (Criteria) this;
        }

        public Criteria andPeriodDayIn(List<Integer> values) {
            addCriterion("PERIOD_DAY in", values, "periodDay");
            return (Criteria) this;
        }

        public Criteria andPeriodDayNotIn(List<Integer> values) {
            addCriterion("PERIOD_DAY not in", values, "periodDay");
            return (Criteria) this;
        }

        public Criteria andPeriodDayBetween(Integer value1, Integer value2) {
            addCriterion("PERIOD_DAY between", value1, value2, "periodDay");
            return (Criteria) this;
        }

        public Criteria andPeriodDayNotBetween(Integer value1, Integer value2) {
            addCriterion("PERIOD_DAY not between", value1, value2, "periodDay");
            return (Criteria) this;
        }

        public Criteria andLogisticsCollectionIsNull() {
            addCriterion("LOGISTICS_COLLECTION is null");
            return (Criteria) this;
        }

        public Criteria andLogisticsCollectionIsNotNull() {
            addCriterion("LOGISTICS_COLLECTION is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticsCollectionEqualTo(Byte value) {
            addCriterion("LOGISTICS_COLLECTION =", value, "logisticsCollection");
            return (Criteria) this;
        }

        public Criteria andLogisticsCollectionNotEqualTo(Byte value) {
            addCriterion("LOGISTICS_COLLECTION <>", value, "logisticsCollection");
            return (Criteria) this;
        }

        public Criteria andLogisticsCollectionGreaterThan(Byte value) {
            addCriterion("LOGISTICS_COLLECTION >", value, "logisticsCollection");
            return (Criteria) this;
        }

        public Criteria andLogisticsCollectionGreaterThanOrEqualTo(Byte value) {
            addCriterion("LOGISTICS_COLLECTION >=", value, "logisticsCollection");
            return (Criteria) this;
        }

        public Criteria andLogisticsCollectionLessThan(Byte value) {
            addCriterion("LOGISTICS_COLLECTION <", value, "logisticsCollection");
            return (Criteria) this;
        }

        public Criteria andLogisticsCollectionLessThanOrEqualTo(Byte value) {
            addCriterion("LOGISTICS_COLLECTION <=", value, "logisticsCollection");
            return (Criteria) this;
        }

        public Criteria andLogisticsCollectionIn(List<Byte> values) {
            addCriterion("LOGISTICS_COLLECTION in", values, "logisticsCollection");
            return (Criteria) this;
        }

        public Criteria andLogisticsCollectionNotIn(List<Byte> values) {
            addCriterion("LOGISTICS_COLLECTION not in", values, "logisticsCollection");
            return (Criteria) this;
        }

        public Criteria andLogisticsCollectionBetween(Byte value1, Byte value2) {
            addCriterion("LOGISTICS_COLLECTION between", value1, value2, "logisticsCollection");
            return (Criteria) this;
        }

        public Criteria andLogisticsCollectionNotBetween(Byte value1, Byte value2) {
            addCriterion("LOGISTICS_COLLECTION not between", value1, value2, "logisticsCollection");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountIsNull() {
            addCriterion("RETAINAGE_AMOUNT is null");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountIsNotNull() {
            addCriterion("RETAINAGE_AMOUNT is not null");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountEqualTo(BigDecimal value) {
            addCriterion("RETAINAGE_AMOUNT =", value, "retainageAmount");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountNotEqualTo(BigDecimal value) {
            addCriterion("RETAINAGE_AMOUNT <>", value, "retainageAmount");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountGreaterThan(BigDecimal value) {
            addCriterion("RETAINAGE_AMOUNT >", value, "retainageAmount");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("RETAINAGE_AMOUNT >=", value, "retainageAmount");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountLessThan(BigDecimal value) {
            addCriterion("RETAINAGE_AMOUNT <", value, "retainageAmount");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("RETAINAGE_AMOUNT <=", value, "retainageAmount");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountIn(List<BigDecimal> values) {
            addCriterion("RETAINAGE_AMOUNT in", values, "retainageAmount");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountNotIn(List<BigDecimal> values) {
            addCriterion("RETAINAGE_AMOUNT not in", values, "retainageAmount");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("RETAINAGE_AMOUNT between", value1, value2, "retainageAmount");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("RETAINAGE_AMOUNT not between", value1, value2, "retainageAmount");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountMonthIsNull() {
            addCriterion("RETAINAGE_AMOUNT_MONTH is null");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountMonthIsNotNull() {
            addCriterion("RETAINAGE_AMOUNT_MONTH is not null");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountMonthEqualTo(Integer value) {
            addCriterion("RETAINAGE_AMOUNT_MONTH =", value, "retainageAmountMonth");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountMonthNotEqualTo(Integer value) {
            addCriterion("RETAINAGE_AMOUNT_MONTH <>", value, "retainageAmountMonth");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountMonthGreaterThan(Integer value) {
            addCriterion("RETAINAGE_AMOUNT_MONTH >", value, "retainageAmountMonth");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountMonthGreaterThanOrEqualTo(Integer value) {
            addCriterion("RETAINAGE_AMOUNT_MONTH >=", value, "retainageAmountMonth");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountMonthLessThan(Integer value) {
            addCriterion("RETAINAGE_AMOUNT_MONTH <", value, "retainageAmountMonth");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountMonthLessThanOrEqualTo(Integer value) {
            addCriterion("RETAINAGE_AMOUNT_MONTH <=", value, "retainageAmountMonth");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountMonthIn(List<Integer> values) {
            addCriterion("RETAINAGE_AMOUNT_MONTH in", values, "retainageAmountMonth");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountMonthNotIn(List<Integer> values) {
            addCriterion("RETAINAGE_AMOUNT_MONTH not in", values, "retainageAmountMonth");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountMonthBetween(Integer value1, Integer value2) {
            addCriterion("RETAINAGE_AMOUNT_MONTH between", value1, value2, "retainageAmountMonth");
            return (Criteria) this;
        }

        public Criteria andRetainageAmountMonthNotBetween(Integer value1, Integer value2) {
            addCriterion("RETAINAGE_AMOUNT_MONTH not between", value1, value2, "retainageAmountMonth");
            return (Criteria) this;
        }

        public Criteria andPaymentCommentsIsNull() {
            addCriterion("PAYMENT_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andPaymentCommentsIsNotNull() {
            addCriterion("PAYMENT_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentCommentsEqualTo(String value) {
            addCriterion("PAYMENT_COMMENTS =", value, "paymentComments");
            return (Criteria) this;
        }

        public Criteria andPaymentCommentsNotEqualTo(String value) {
            addCriterion("PAYMENT_COMMENTS <>", value, "paymentComments");
            return (Criteria) this;
        }

        public Criteria andPaymentCommentsGreaterThan(String value) {
            addCriterion("PAYMENT_COMMENTS >", value, "paymentComments");
            return (Criteria) this;
        }

        public Criteria andPaymentCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("PAYMENT_COMMENTS >=", value, "paymentComments");
            return (Criteria) this;
        }

        public Criteria andPaymentCommentsLessThan(String value) {
            addCriterion("PAYMENT_COMMENTS <", value, "paymentComments");
            return (Criteria) this;
        }

        public Criteria andPaymentCommentsLessThanOrEqualTo(String value) {
            addCriterion("PAYMENT_COMMENTS <=", value, "paymentComments");
            return (Criteria) this;
        }

        public Criteria andPaymentCommentsLike(String value) {
            addCriterion("PAYMENT_COMMENTS like", value, "paymentComments");
            return (Criteria) this;
        }

        public Criteria andPaymentCommentsNotLike(String value) {
            addCriterion("PAYMENT_COMMENTS not like", value, "paymentComments");
            return (Criteria) this;
        }

        public Criteria andPaymentCommentsIn(List<String> values) {
            addCriterion("PAYMENT_COMMENTS in", values, "paymentComments");
            return (Criteria) this;
        }

        public Criteria andPaymentCommentsNotIn(List<String> values) {
            addCriterion("PAYMENT_COMMENTS not in", values, "paymentComments");
            return (Criteria) this;
        }

        public Criteria andPaymentCommentsBetween(String value1, String value2) {
            addCriterion("PAYMENT_COMMENTS between", value1, value2, "paymentComments");
            return (Criteria) this;
        }

        public Criteria andPaymentCommentsNotBetween(String value1, String value2) {
            addCriterion("PAYMENT_COMMENTS not between", value1, value2, "paymentComments");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseIsNull() {
            addCriterion("ADDITIONAL_CLAUSE is null");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseIsNotNull() {
            addCriterion("ADDITIONAL_CLAUSE is not null");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseEqualTo(String value) {
            addCriterion("ADDITIONAL_CLAUSE =", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNotEqualTo(String value) {
            addCriterion("ADDITIONAL_CLAUSE <>", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseGreaterThan(String value) {
            addCriterion("ADDITIONAL_CLAUSE >", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseGreaterThanOrEqualTo(String value) {
            addCriterion("ADDITIONAL_CLAUSE >=", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseLessThan(String value) {
            addCriterion("ADDITIONAL_CLAUSE <", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseLessThanOrEqualTo(String value) {
            addCriterion("ADDITIONAL_CLAUSE <=", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseLike(String value) {
            addCriterion("ADDITIONAL_CLAUSE like", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNotLike(String value) {
            addCriterion("ADDITIONAL_CLAUSE not like", value, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseIn(List<String> values) {
            addCriterion("ADDITIONAL_CLAUSE in", values, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNotIn(List<String> values) {
            addCriterion("ADDITIONAL_CLAUSE not in", values, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseBetween(String value1, String value2) {
            addCriterion("ADDITIONAL_CLAUSE between", value1, value2, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andAdditionalClauseNotBetween(String value1, String value2) {
            addCriterion("ADDITIONAL_CLAUSE not between", value1, value2, "additionalClause");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsIsNull() {
            addCriterion("LOGISTICS_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsIsNotNull() {
            addCriterion("LOGISTICS_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsEqualTo(String value) {
            addCriterion("LOGISTICS_COMMENTS =", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsNotEqualTo(String value) {
            addCriterion("LOGISTICS_COMMENTS <>", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsGreaterThan(String value) {
            addCriterion("LOGISTICS_COMMENTS >", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("LOGISTICS_COMMENTS >=", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsLessThan(String value) {
            addCriterion("LOGISTICS_COMMENTS <", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsLessThanOrEqualTo(String value) {
            addCriterion("LOGISTICS_COMMENTS <=", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsLike(String value) {
            addCriterion("LOGISTICS_COMMENTS like", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsNotLike(String value) {
            addCriterion("LOGISTICS_COMMENTS not like", value, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsIn(List<String> values) {
            addCriterion("LOGISTICS_COMMENTS in", values, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsNotIn(List<String> values) {
            addCriterion("LOGISTICS_COMMENTS not in", values, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsBetween(String value1, String value2) {
            addCriterion("LOGISTICS_COMMENTS between", value1, value2, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andLogisticsCommentsNotBetween(String value1, String value2) {
            addCriterion("LOGISTICS_COMMENTS not between", value1, value2, "logisticsComments");
            return (Criteria) this;
        }

        public Criteria andFinanceCommentsIsNull() {
            addCriterion("FINANCE_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andFinanceCommentsIsNotNull() {
            addCriterion("FINANCE_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andFinanceCommentsEqualTo(String value) {
            addCriterion("FINANCE_COMMENTS =", value, "financeComments");
            return (Criteria) this;
        }

        public Criteria andFinanceCommentsNotEqualTo(String value) {
            addCriterion("FINANCE_COMMENTS <>", value, "financeComments");
            return (Criteria) this;
        }

        public Criteria andFinanceCommentsGreaterThan(String value) {
            addCriterion("FINANCE_COMMENTS >", value, "financeComments");
            return (Criteria) this;
        }

        public Criteria andFinanceCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("FINANCE_COMMENTS >=", value, "financeComments");
            return (Criteria) this;
        }

        public Criteria andFinanceCommentsLessThan(String value) {
            addCriterion("FINANCE_COMMENTS <", value, "financeComments");
            return (Criteria) this;
        }

        public Criteria andFinanceCommentsLessThanOrEqualTo(String value) {
            addCriterion("FINANCE_COMMENTS <=", value, "financeComments");
            return (Criteria) this;
        }

        public Criteria andFinanceCommentsLike(String value) {
            addCriterion("FINANCE_COMMENTS like", value, "financeComments");
            return (Criteria) this;
        }

        public Criteria andFinanceCommentsNotLike(String value) {
            addCriterion("FINANCE_COMMENTS not like", value, "financeComments");
            return (Criteria) this;
        }

        public Criteria andFinanceCommentsIn(List<String> values) {
            addCriterion("FINANCE_COMMENTS in", values, "financeComments");
            return (Criteria) this;
        }

        public Criteria andFinanceCommentsNotIn(List<String> values) {
            addCriterion("FINANCE_COMMENTS not in", values, "financeComments");
            return (Criteria) this;
        }

        public Criteria andFinanceCommentsBetween(String value1, String value2) {
            addCriterion("FINANCE_COMMENTS between", value1, value2, "financeComments");
            return (Criteria) this;
        }

        public Criteria andFinanceCommentsNotBetween(String value1, String value2) {
            addCriterion("FINANCE_COMMENTS not between", value1, value2, "financeComments");
            return (Criteria) this;
        }

        public Criteria andCommentsIsNull() {
            addCriterion("COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andCommentsIsNotNull() {
            addCriterion("COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andCommentsEqualTo(String value) {
            addCriterion("COMMENTS =", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotEqualTo(String value) {
            addCriterion("COMMENTS <>", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsGreaterThan(String value) {
            addCriterion("COMMENTS >", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("COMMENTS >=", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsLessThan(String value) {
            addCriterion("COMMENTS <", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsLessThanOrEqualTo(String value) {
            addCriterion("COMMENTS <=", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsLike(String value) {
            addCriterion("COMMENTS like", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotLike(String value) {
            addCriterion("COMMENTS not like", value, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsIn(List<String> values) {
            addCriterion("COMMENTS in", values, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotIn(List<String> values) {
            addCriterion("COMMENTS not in", values, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsBetween(String value1, String value2) {
            addCriterion("COMMENTS between", value1, value2, "comments");
            return (Criteria) this;
        }

        public Criteria andCommentsNotBetween(String value1, String value2) {
            addCriterion("COMMENTS not between", value1, value2, "comments");
            return (Criteria) this;
        }

        public Criteria andInvoiceCommentsIsNull() {
            addCriterion("INVOICE_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceCommentsIsNotNull() {
            addCriterion("INVOICE_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceCommentsEqualTo(String value) {
            addCriterion("INVOICE_COMMENTS =", value, "invoiceComments");
            return (Criteria) this;
        }

        public Criteria andInvoiceCommentsNotEqualTo(String value) {
            addCriterion("INVOICE_COMMENTS <>", value, "invoiceComments");
            return (Criteria) this;
        }

        public Criteria andInvoiceCommentsGreaterThan(String value) {
            addCriterion("INVOICE_COMMENTS >", value, "invoiceComments");
            return (Criteria) this;
        }

        public Criteria andInvoiceCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("INVOICE_COMMENTS >=", value, "invoiceComments");
            return (Criteria) this;
        }

        public Criteria andInvoiceCommentsLessThan(String value) {
            addCriterion("INVOICE_COMMENTS <", value, "invoiceComments");
            return (Criteria) this;
        }

        public Criteria andInvoiceCommentsLessThanOrEqualTo(String value) {
            addCriterion("INVOICE_COMMENTS <=", value, "invoiceComments");
            return (Criteria) this;
        }

        public Criteria andInvoiceCommentsLike(String value) {
            addCriterion("INVOICE_COMMENTS like", value, "invoiceComments");
            return (Criteria) this;
        }

        public Criteria andInvoiceCommentsNotLike(String value) {
            addCriterion("INVOICE_COMMENTS not like", value, "invoiceComments");
            return (Criteria) this;
        }

        public Criteria andInvoiceCommentsIn(List<String> values) {
            addCriterion("INVOICE_COMMENTS in", values, "invoiceComments");
            return (Criteria) this;
        }

        public Criteria andInvoiceCommentsNotIn(List<String> values) {
            addCriterion("INVOICE_COMMENTS not in", values, "invoiceComments");
            return (Criteria) this;
        }

        public Criteria andInvoiceCommentsBetween(String value1, String value2) {
            addCriterion("INVOICE_COMMENTS between", value1, value2, "invoiceComments");
            return (Criteria) this;
        }

        public Criteria andInvoiceCommentsNotBetween(String value1, String value2) {
            addCriterion("INVOICE_COMMENTS not between", value1, value2, "invoiceComments");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectIsNull() {
            addCriterion("DELIVERY_DIRECT is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectIsNotNull() {
            addCriterion("DELIVERY_DIRECT is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectEqualTo(Integer value) {
            addCriterion("DELIVERY_DIRECT =", value, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectNotEqualTo(Integer value) {
            addCriterion("DELIVERY_DIRECT <>", value, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectGreaterThan(Integer value) {
            addCriterion("DELIVERY_DIRECT >", value, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectGreaterThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_DIRECT >=", value, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectLessThan(Integer value) {
            addCriterion("DELIVERY_DIRECT <", value, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectLessThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_DIRECT <=", value, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectIn(List<Integer> values) {
            addCriterion("DELIVERY_DIRECT in", values, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectNotIn(List<Integer> values) {
            addCriterion("DELIVERY_DIRECT not in", values, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_DIRECT between", value1, value2, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andDeliveryDirectNotBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_DIRECT not between", value1, value2, "deliveryDirect");
            return (Criteria) this;
        }

        public Criteria andSupplierClauseIsNull() {
            addCriterion("SUPPLIER_CLAUSE is null");
            return (Criteria) this;
        }

        public Criteria andSupplierClauseIsNotNull() {
            addCriterion("SUPPLIER_CLAUSE is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierClauseEqualTo(String value) {
            addCriterion("SUPPLIER_CLAUSE =", value, "supplierClause");
            return (Criteria) this;
        }

        public Criteria andSupplierClauseNotEqualTo(String value) {
            addCriterion("SUPPLIER_CLAUSE <>", value, "supplierClause");
            return (Criteria) this;
        }

        public Criteria andSupplierClauseGreaterThan(String value) {
            addCriterion("SUPPLIER_CLAUSE >", value, "supplierClause");
            return (Criteria) this;
        }

        public Criteria andSupplierClauseGreaterThanOrEqualTo(String value) {
            addCriterion("SUPPLIER_CLAUSE >=", value, "supplierClause");
            return (Criteria) this;
        }

        public Criteria andSupplierClauseLessThan(String value) {
            addCriterion("SUPPLIER_CLAUSE <", value, "supplierClause");
            return (Criteria) this;
        }

        public Criteria andSupplierClauseLessThanOrEqualTo(String value) {
            addCriterion("SUPPLIER_CLAUSE <=", value, "supplierClause");
            return (Criteria) this;
        }

        public Criteria andSupplierClauseLike(String value) {
            addCriterion("SUPPLIER_CLAUSE like", value, "supplierClause");
            return (Criteria) this;
        }

        public Criteria andSupplierClauseNotLike(String value) {
            addCriterion("SUPPLIER_CLAUSE not like", value, "supplierClause");
            return (Criteria) this;
        }

        public Criteria andSupplierClauseIn(List<String> values) {
            addCriterion("SUPPLIER_CLAUSE in", values, "supplierClause");
            return (Criteria) this;
        }

        public Criteria andSupplierClauseNotIn(List<String> values) {
            addCriterion("SUPPLIER_CLAUSE not in", values, "supplierClause");
            return (Criteria) this;
        }

        public Criteria andSupplierClauseBetween(String value1, String value2) {
            addCriterion("SUPPLIER_CLAUSE between", value1, value2, "supplierClause");
            return (Criteria) this;
        }

        public Criteria andSupplierClauseNotBetween(String value1, String value2) {
            addCriterion("SUPPLIER_CLAUSE not between", value1, value2, "supplierClause");
            return (Criteria) this;
        }

        public Criteria andHaveAdvancePurchaseIsNull() {
            addCriterion("HAVE_ADVANCE_PURCHASE is null");
            return (Criteria) this;
        }

        public Criteria andHaveAdvancePurchaseIsNotNull() {
            addCriterion("HAVE_ADVANCE_PURCHASE is not null");
            return (Criteria) this;
        }

        public Criteria andHaveAdvancePurchaseEqualTo(Byte value) {
            addCriterion("HAVE_ADVANCE_PURCHASE =", value, "haveAdvancePurchase");
            return (Criteria) this;
        }

        public Criteria andHaveAdvancePurchaseNotEqualTo(Byte value) {
            addCriterion("HAVE_ADVANCE_PURCHASE <>", value, "haveAdvancePurchase");
            return (Criteria) this;
        }

        public Criteria andHaveAdvancePurchaseGreaterThan(Byte value) {
            addCriterion("HAVE_ADVANCE_PURCHASE >", value, "haveAdvancePurchase");
            return (Criteria) this;
        }

        public Criteria andHaveAdvancePurchaseGreaterThanOrEqualTo(Byte value) {
            addCriterion("HAVE_ADVANCE_PURCHASE >=", value, "haveAdvancePurchase");
            return (Criteria) this;
        }

        public Criteria andHaveAdvancePurchaseLessThan(Byte value) {
            addCriterion("HAVE_ADVANCE_PURCHASE <", value, "haveAdvancePurchase");
            return (Criteria) this;
        }

        public Criteria andHaveAdvancePurchaseLessThanOrEqualTo(Byte value) {
            addCriterion("HAVE_ADVANCE_PURCHASE <=", value, "haveAdvancePurchase");
            return (Criteria) this;
        }

        public Criteria andHaveAdvancePurchaseIn(List<Byte> values) {
            addCriterion("HAVE_ADVANCE_PURCHASE in", values, "haveAdvancePurchase");
            return (Criteria) this;
        }

        public Criteria andHaveAdvancePurchaseNotIn(List<Byte> values) {
            addCriterion("HAVE_ADVANCE_PURCHASE not in", values, "haveAdvancePurchase");
            return (Criteria) this;
        }

        public Criteria andHaveAdvancePurchaseBetween(Byte value1, Byte value2) {
            addCriterion("HAVE_ADVANCE_PURCHASE between", value1, value2, "haveAdvancePurchase");
            return (Criteria) this;
        }

        public Criteria andHaveAdvancePurchaseNotBetween(Byte value1, Byte value2) {
            addCriterion("HAVE_ADVANCE_PURCHASE not between", value1, value2, "haveAdvancePurchase");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseStatusIsNull() {
            addCriterion("ADVANCE_PURCHASE_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseStatusIsNotNull() {
            addCriterion("ADVANCE_PURCHASE_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseStatusEqualTo(Byte value) {
            addCriterion("ADVANCE_PURCHASE_STATUS =", value, "advancePurchaseStatus");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseStatusNotEqualTo(Byte value) {
            addCriterion("ADVANCE_PURCHASE_STATUS <>", value, "advancePurchaseStatus");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseStatusGreaterThan(Byte value) {
            addCriterion("ADVANCE_PURCHASE_STATUS >", value, "advancePurchaseStatus");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("ADVANCE_PURCHASE_STATUS >=", value, "advancePurchaseStatus");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseStatusLessThan(Byte value) {
            addCriterion("ADVANCE_PURCHASE_STATUS <", value, "advancePurchaseStatus");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseStatusLessThanOrEqualTo(Byte value) {
            addCriterion("ADVANCE_PURCHASE_STATUS <=", value, "advancePurchaseStatus");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseStatusIn(List<Byte> values) {
            addCriterion("ADVANCE_PURCHASE_STATUS in", values, "advancePurchaseStatus");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseStatusNotIn(List<Byte> values) {
            addCriterion("ADVANCE_PURCHASE_STATUS not in", values, "advancePurchaseStatus");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseStatusBetween(Byte value1, Byte value2) {
            addCriterion("ADVANCE_PURCHASE_STATUS between", value1, value2, "advancePurchaseStatus");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("ADVANCE_PURCHASE_STATUS not between", value1, value2, "advancePurchaseStatus");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseCommentsIsNull() {
            addCriterion("ADVANCE_PURCHASE_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseCommentsIsNotNull() {
            addCriterion("ADVANCE_PURCHASE_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseCommentsEqualTo(String value) {
            addCriterion("ADVANCE_PURCHASE_COMMENTS =", value, "advancePurchaseComments");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseCommentsNotEqualTo(String value) {
            addCriterion("ADVANCE_PURCHASE_COMMENTS <>", value, "advancePurchaseComments");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseCommentsGreaterThan(String value) {
            addCriterion("ADVANCE_PURCHASE_COMMENTS >", value, "advancePurchaseComments");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("ADVANCE_PURCHASE_COMMENTS >=", value, "advancePurchaseComments");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseCommentsLessThan(String value) {
            addCriterion("ADVANCE_PURCHASE_COMMENTS <", value, "advancePurchaseComments");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseCommentsLessThanOrEqualTo(String value) {
            addCriterion("ADVANCE_PURCHASE_COMMENTS <=", value, "advancePurchaseComments");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseCommentsLike(String value) {
            addCriterion("ADVANCE_PURCHASE_COMMENTS like", value, "advancePurchaseComments");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseCommentsNotLike(String value) {
            addCriterion("ADVANCE_PURCHASE_COMMENTS not like", value, "advancePurchaseComments");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseCommentsIn(List<String> values) {
            addCriterion("ADVANCE_PURCHASE_COMMENTS in", values, "advancePurchaseComments");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseCommentsNotIn(List<String> values) {
            addCriterion("ADVANCE_PURCHASE_COMMENTS not in", values, "advancePurchaseComments");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseCommentsBetween(String value1, String value2) {
            addCriterion("ADVANCE_PURCHASE_COMMENTS between", value1, value2, "advancePurchaseComments");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseCommentsNotBetween(String value1, String value2) {
            addCriterion("ADVANCE_PURCHASE_COMMENTS not between", value1, value2, "advancePurchaseComments");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseTimeIsNull() {
            addCriterion("ADVANCE_PURCHASE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseTimeIsNotNull() {
            addCriterion("ADVANCE_PURCHASE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseTimeEqualTo(Long value) {
            addCriterion("ADVANCE_PURCHASE_TIME =", value, "advancePurchaseTime");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseTimeNotEqualTo(Long value) {
            addCriterion("ADVANCE_PURCHASE_TIME <>", value, "advancePurchaseTime");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseTimeGreaterThan(Long value) {
            addCriterion("ADVANCE_PURCHASE_TIME >", value, "advancePurchaseTime");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ADVANCE_PURCHASE_TIME >=", value, "advancePurchaseTime");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseTimeLessThan(Long value) {
            addCriterion("ADVANCE_PURCHASE_TIME <", value, "advancePurchaseTime");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseTimeLessThanOrEqualTo(Long value) {
            addCriterion("ADVANCE_PURCHASE_TIME <=", value, "advancePurchaseTime");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseTimeIn(List<Long> values) {
            addCriterion("ADVANCE_PURCHASE_TIME in", values, "advancePurchaseTime");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseTimeNotIn(List<Long> values) {
            addCriterion("ADVANCE_PURCHASE_TIME not in", values, "advancePurchaseTime");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseTimeBetween(Long value1, Long value2) {
            addCriterion("ADVANCE_PURCHASE_TIME between", value1, value2, "advancePurchaseTime");
            return (Criteria) this;
        }

        public Criteria andAdvancePurchaseTimeNotBetween(Long value1, Long value2) {
            addCriterion("ADVANCE_PURCHASE_TIME not between", value1, value2, "advancePurchaseTime");
            return (Criteria) this;
        }

        public Criteria andIsUrgentIsNull() {
            addCriterion("IS_URGENT is null");
            return (Criteria) this;
        }

        public Criteria andIsUrgentIsNotNull() {
            addCriterion("IS_URGENT is not null");
            return (Criteria) this;
        }

        public Criteria andIsUrgentEqualTo(Byte value) {
            addCriterion("IS_URGENT =", value, "isUrgent");
            return (Criteria) this;
        }

        public Criteria andIsUrgentNotEqualTo(Byte value) {
            addCriterion("IS_URGENT <>", value, "isUrgent");
            return (Criteria) this;
        }

        public Criteria andIsUrgentGreaterThan(Byte value) {
            addCriterion("IS_URGENT >", value, "isUrgent");
            return (Criteria) this;
        }

        public Criteria andIsUrgentGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_URGENT >=", value, "isUrgent");
            return (Criteria) this;
        }

        public Criteria andIsUrgentLessThan(Byte value) {
            addCriterion("IS_URGENT <", value, "isUrgent");
            return (Criteria) this;
        }

        public Criteria andIsUrgentLessThanOrEqualTo(Byte value) {
            addCriterion("IS_URGENT <=", value, "isUrgent");
            return (Criteria) this;
        }

        public Criteria andIsUrgentIn(List<Byte> values) {
            addCriterion("IS_URGENT in", values, "isUrgent");
            return (Criteria) this;
        }

        public Criteria andIsUrgentNotIn(List<Byte> values) {
            addCriterion("IS_URGENT not in", values, "isUrgent");
            return (Criteria) this;
        }

        public Criteria andIsUrgentBetween(Byte value1, Byte value2) {
            addCriterion("IS_URGENT between", value1, value2, "isUrgent");
            return (Criteria) this;
        }

        public Criteria andIsUrgentNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_URGENT not between", value1, value2, "isUrgent");
            return (Criteria) this;
        }

        public Criteria andUrgentAmountIsNull() {
            addCriterion("URGENT_AMOUNT is null");
            return (Criteria) this;
        }

        public Criteria andUrgentAmountIsNotNull() {
            addCriterion("URGENT_AMOUNT is not null");
            return (Criteria) this;
        }

        public Criteria andUrgentAmountEqualTo(BigDecimal value) {
            addCriterion("URGENT_AMOUNT =", value, "urgentAmount");
            return (Criteria) this;
        }

        public Criteria andUrgentAmountNotEqualTo(BigDecimal value) {
            addCriterion("URGENT_AMOUNT <>", value, "urgentAmount");
            return (Criteria) this;
        }

        public Criteria andUrgentAmountGreaterThan(BigDecimal value) {
            addCriterion("URGENT_AMOUNT >", value, "urgentAmount");
            return (Criteria) this;
        }

        public Criteria andUrgentAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("URGENT_AMOUNT >=", value, "urgentAmount");
            return (Criteria) this;
        }

        public Criteria andUrgentAmountLessThan(BigDecimal value) {
            addCriterion("URGENT_AMOUNT <", value, "urgentAmount");
            return (Criteria) this;
        }

        public Criteria andUrgentAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("URGENT_AMOUNT <=", value, "urgentAmount");
            return (Criteria) this;
        }

        public Criteria andUrgentAmountIn(List<BigDecimal> values) {
            addCriterion("URGENT_AMOUNT in", values, "urgentAmount");
            return (Criteria) this;
        }

        public Criteria andUrgentAmountNotIn(List<BigDecimal> values) {
            addCriterion("URGENT_AMOUNT not in", values, "urgentAmount");
            return (Criteria) this;
        }

        public Criteria andUrgentAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("URGENT_AMOUNT between", value1, value2, "urgentAmount");
            return (Criteria) this;
        }

        public Criteria andUrgentAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("URGENT_AMOUNT not between", value1, value2, "urgentAmount");
            return (Criteria) this;
        }

        public Criteria andHaveCommunicateIsNull() {
            addCriterion("HAVE_COMMUNICATE is null");
            return (Criteria) this;
        }

        public Criteria andHaveCommunicateIsNotNull() {
            addCriterion("HAVE_COMMUNICATE is not null");
            return (Criteria) this;
        }

        public Criteria andHaveCommunicateEqualTo(Byte value) {
            addCriterion("HAVE_COMMUNICATE =", value, "haveCommunicate");
            return (Criteria) this;
        }

        public Criteria andHaveCommunicateNotEqualTo(Byte value) {
            addCriterion("HAVE_COMMUNICATE <>", value, "haveCommunicate");
            return (Criteria) this;
        }

        public Criteria andHaveCommunicateGreaterThan(Byte value) {
            addCriterion("HAVE_COMMUNICATE >", value, "haveCommunicate");
            return (Criteria) this;
        }

        public Criteria andHaveCommunicateGreaterThanOrEqualTo(Byte value) {
            addCriterion("HAVE_COMMUNICATE >=", value, "haveCommunicate");
            return (Criteria) this;
        }

        public Criteria andHaveCommunicateLessThan(Byte value) {
            addCriterion("HAVE_COMMUNICATE <", value, "haveCommunicate");
            return (Criteria) this;
        }

        public Criteria andHaveCommunicateLessThanOrEqualTo(Byte value) {
            addCriterion("HAVE_COMMUNICATE <=", value, "haveCommunicate");
            return (Criteria) this;
        }

        public Criteria andHaveCommunicateIn(List<Byte> values) {
            addCriterion("HAVE_COMMUNICATE in", values, "haveCommunicate");
            return (Criteria) this;
        }

        public Criteria andHaveCommunicateNotIn(List<Byte> values) {
            addCriterion("HAVE_COMMUNICATE not in", values, "haveCommunicate");
            return (Criteria) this;
        }

        public Criteria andHaveCommunicateBetween(Byte value1, Byte value2) {
            addCriterion("HAVE_COMMUNICATE between", value1, value2, "haveCommunicate");
            return (Criteria) this;
        }

        public Criteria andHaveCommunicateNotBetween(Byte value1, Byte value2) {
            addCriterion("HAVE_COMMUNICATE not between", value1, value2, "haveCommunicate");
            return (Criteria) this;
        }

        public Criteria andPrepareCommentsIsNull() {
            addCriterion("PREPARE_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andPrepareCommentsIsNotNull() {
            addCriterion("PREPARE_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andPrepareCommentsEqualTo(String value) {
            addCriterion("PREPARE_COMMENTS =", value, "prepareComments");
            return (Criteria) this;
        }

        public Criteria andPrepareCommentsNotEqualTo(String value) {
            addCriterion("PREPARE_COMMENTS <>", value, "prepareComments");
            return (Criteria) this;
        }

        public Criteria andPrepareCommentsGreaterThan(String value) {
            addCriterion("PREPARE_COMMENTS >", value, "prepareComments");
            return (Criteria) this;
        }

        public Criteria andPrepareCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("PREPARE_COMMENTS >=", value, "prepareComments");
            return (Criteria) this;
        }

        public Criteria andPrepareCommentsLessThan(String value) {
            addCriterion("PREPARE_COMMENTS <", value, "prepareComments");
            return (Criteria) this;
        }

        public Criteria andPrepareCommentsLessThanOrEqualTo(String value) {
            addCriterion("PREPARE_COMMENTS <=", value, "prepareComments");
            return (Criteria) this;
        }

        public Criteria andPrepareCommentsLike(String value) {
            addCriterion("PREPARE_COMMENTS like", value, "prepareComments");
            return (Criteria) this;
        }

        public Criteria andPrepareCommentsNotLike(String value) {
            addCriterion("PREPARE_COMMENTS not like", value, "prepareComments");
            return (Criteria) this;
        }

        public Criteria andPrepareCommentsIn(List<String> values) {
            addCriterion("PREPARE_COMMENTS in", values, "prepareComments");
            return (Criteria) this;
        }

        public Criteria andPrepareCommentsNotIn(List<String> values) {
            addCriterion("PREPARE_COMMENTS not in", values, "prepareComments");
            return (Criteria) this;
        }

        public Criteria andPrepareCommentsBetween(String value1, String value2) {
            addCriterion("PREPARE_COMMENTS between", value1, value2, "prepareComments");
            return (Criteria) this;
        }

        public Criteria andPrepareCommentsNotBetween(String value1, String value2) {
            addCriterion("PREPARE_COMMENTS not between", value1, value2, "prepareComments");
            return (Criteria) this;
        }

        public Criteria andMarketingPlanIsNull() {
            addCriterion("MARKETING_PLAN is null");
            return (Criteria) this;
        }

        public Criteria andMarketingPlanIsNotNull() {
            addCriterion("MARKETING_PLAN is not null");
            return (Criteria) this;
        }

        public Criteria andMarketingPlanEqualTo(String value) {
            addCriterion("MARKETING_PLAN =", value, "marketingPlan");
            return (Criteria) this;
        }

        public Criteria andMarketingPlanNotEqualTo(String value) {
            addCriterion("MARKETING_PLAN <>", value, "marketingPlan");
            return (Criteria) this;
        }

        public Criteria andMarketingPlanGreaterThan(String value) {
            addCriterion("MARKETING_PLAN >", value, "marketingPlan");
            return (Criteria) this;
        }

        public Criteria andMarketingPlanGreaterThanOrEqualTo(String value) {
            addCriterion("MARKETING_PLAN >=", value, "marketingPlan");
            return (Criteria) this;
        }

        public Criteria andMarketingPlanLessThan(String value) {
            addCriterion("MARKETING_PLAN <", value, "marketingPlan");
            return (Criteria) this;
        }

        public Criteria andMarketingPlanLessThanOrEqualTo(String value) {
            addCriterion("MARKETING_PLAN <=", value, "marketingPlan");
            return (Criteria) this;
        }

        public Criteria andMarketingPlanLike(String value) {
            addCriterion("MARKETING_PLAN like", value, "marketingPlan");
            return (Criteria) this;
        }

        public Criteria andMarketingPlanNotLike(String value) {
            addCriterion("MARKETING_PLAN not like", value, "marketingPlan");
            return (Criteria) this;
        }

        public Criteria andMarketingPlanIn(List<String> values) {
            addCriterion("MARKETING_PLAN in", values, "marketingPlan");
            return (Criteria) this;
        }

        public Criteria andMarketingPlanNotIn(List<String> values) {
            addCriterion("MARKETING_PLAN not in", values, "marketingPlan");
            return (Criteria) this;
        }

        public Criteria andMarketingPlanBetween(String value1, String value2) {
            addCriterion("MARKETING_PLAN between", value1, value2, "marketingPlan");
            return (Criteria) this;
        }

        public Criteria andMarketingPlanNotBetween(String value1, String value2) {
            addCriterion("MARKETING_PLAN not between", value1, value2, "marketingPlan");
            return (Criteria) this;
        }

        public Criteria andStatusCommentsIsNull() {
            addCriterion("STATUS_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andStatusCommentsIsNotNull() {
            addCriterion("STATUS_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andStatusCommentsEqualTo(Integer value) {
            addCriterion("STATUS_COMMENTS =", value, "statusComments");
            return (Criteria) this;
        }

        public Criteria andStatusCommentsNotEqualTo(Integer value) {
            addCriterion("STATUS_COMMENTS <>", value, "statusComments");
            return (Criteria) this;
        }

        public Criteria andStatusCommentsGreaterThan(Integer value) {
            addCriterion("STATUS_COMMENTS >", value, "statusComments");
            return (Criteria) this;
        }

        public Criteria andStatusCommentsGreaterThanOrEqualTo(Integer value) {
            addCriterion("STATUS_COMMENTS >=", value, "statusComments");
            return (Criteria) this;
        }

        public Criteria andStatusCommentsLessThan(Integer value) {
            addCriterion("STATUS_COMMENTS <", value, "statusComments");
            return (Criteria) this;
        }

        public Criteria andStatusCommentsLessThanOrEqualTo(Integer value) {
            addCriterion("STATUS_COMMENTS <=", value, "statusComments");
            return (Criteria) this;
        }

        public Criteria andStatusCommentsIn(List<Integer> values) {
            addCriterion("STATUS_COMMENTS in", values, "statusComments");
            return (Criteria) this;
        }

        public Criteria andStatusCommentsNotIn(List<Integer> values) {
            addCriterion("STATUS_COMMENTS not in", values, "statusComments");
            return (Criteria) this;
        }

        public Criteria andStatusCommentsBetween(Integer value1, Integer value2) {
            addCriterion("STATUS_COMMENTS between", value1, value2, "statusComments");
            return (Criteria) this;
        }

        public Criteria andStatusCommentsNotBetween(Integer value1, Integer value2) {
            addCriterion("STATUS_COMMENTS not between", value1, value2, "statusComments");
            return (Criteria) this;
        }

        public Criteria andSyncStatusIsNull() {
            addCriterion("SYNC_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andSyncStatusIsNotNull() {
            addCriterion("SYNC_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andSyncStatusEqualTo(Byte value) {
            addCriterion("SYNC_STATUS =", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusNotEqualTo(Byte value) {
            addCriterion("SYNC_STATUS <>", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusGreaterThan(Byte value) {
            addCriterion("SYNC_STATUS >", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("SYNC_STATUS >=", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusLessThan(Byte value) {
            addCriterion("SYNC_STATUS <", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusLessThanOrEqualTo(Byte value) {
            addCriterion("SYNC_STATUS <=", value, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusIn(List<Byte> values) {
            addCriterion("SYNC_STATUS in", values, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusNotIn(List<Byte> values) {
            addCriterion("SYNC_STATUS not in", values, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusBetween(Byte value1, Byte value2) {
            addCriterion("SYNC_STATUS between", value1, value2, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andSyncStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("SYNC_STATUS not between", value1, value2, "syncStatus");
            return (Criteria) this;
        }

        public Criteria andLogisticsApiSyncIsNull() {
            addCriterion("LOGISTICS_API_SYNC is null");
            return (Criteria) this;
        }

        public Criteria andLogisticsApiSyncIsNotNull() {
            addCriterion("LOGISTICS_API_SYNC is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticsApiSyncEqualTo(Byte value) {
            addCriterion("LOGISTICS_API_SYNC =", value, "logisticsApiSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsApiSyncNotEqualTo(Byte value) {
            addCriterion("LOGISTICS_API_SYNC <>", value, "logisticsApiSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsApiSyncGreaterThan(Byte value) {
            addCriterion("LOGISTICS_API_SYNC >", value, "logisticsApiSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsApiSyncGreaterThanOrEqualTo(Byte value) {
            addCriterion("LOGISTICS_API_SYNC >=", value, "logisticsApiSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsApiSyncLessThan(Byte value) {
            addCriterion("LOGISTICS_API_SYNC <", value, "logisticsApiSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsApiSyncLessThanOrEqualTo(Byte value) {
            addCriterion("LOGISTICS_API_SYNC <=", value, "logisticsApiSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsApiSyncIn(List<Byte> values) {
            addCriterion("LOGISTICS_API_SYNC in", values, "logisticsApiSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsApiSyncNotIn(List<Byte> values) {
            addCriterion("LOGISTICS_API_SYNC not in", values, "logisticsApiSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsApiSyncBetween(Byte value1, Byte value2) {
            addCriterion("LOGISTICS_API_SYNC between", value1, value2, "logisticsApiSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsApiSyncNotBetween(Byte value1, Byte value2) {
            addCriterion("LOGISTICS_API_SYNC not between", value1, value2, "logisticsApiSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsWxsendSyncIsNull() {
            addCriterion("LOGISTICS_WXSEND_SYNC is null");
            return (Criteria) this;
        }

        public Criteria andLogisticsWxsendSyncIsNotNull() {
            addCriterion("LOGISTICS_WXSEND_SYNC is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticsWxsendSyncEqualTo(Byte value) {
            addCriterion("LOGISTICS_WXSEND_SYNC =", value, "logisticsWxsendSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsWxsendSyncNotEqualTo(Byte value) {
            addCriterion("LOGISTICS_WXSEND_SYNC <>", value, "logisticsWxsendSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsWxsendSyncGreaterThan(Byte value) {
            addCriterion("LOGISTICS_WXSEND_SYNC >", value, "logisticsWxsendSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsWxsendSyncGreaterThanOrEqualTo(Byte value) {
            addCriterion("LOGISTICS_WXSEND_SYNC >=", value, "logisticsWxsendSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsWxsendSyncLessThan(Byte value) {
            addCriterion("LOGISTICS_WXSEND_SYNC <", value, "logisticsWxsendSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsWxsendSyncLessThanOrEqualTo(Byte value) {
            addCriterion("LOGISTICS_WXSEND_SYNC <=", value, "logisticsWxsendSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsWxsendSyncIn(List<Byte> values) {
            addCriterion("LOGISTICS_WXSEND_SYNC in", values, "logisticsWxsendSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsWxsendSyncNotIn(List<Byte> values) {
            addCriterion("LOGISTICS_WXSEND_SYNC not in", values, "logisticsWxsendSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsWxsendSyncBetween(Byte value1, Byte value2) {
            addCriterion("LOGISTICS_WXSEND_SYNC between", value1, value2, "logisticsWxsendSync");
            return (Criteria) this;
        }

        public Criteria andLogisticsWxsendSyncNotBetween(Byte value1, Byte value2) {
            addCriterion("LOGISTICS_WXSEND_SYNC not between", value1, value2, "logisticsWxsendSync");
            return (Criteria) this;
        }

        public Criteria andSatisfyInvoiceTimeIsNull() {
            addCriterion("SATISFY_INVOICE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andSatisfyInvoiceTimeIsNotNull() {
            addCriterion("SATISFY_INVOICE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andSatisfyInvoiceTimeEqualTo(Long value) {
            addCriterion("SATISFY_INVOICE_TIME =", value, "satisfyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyInvoiceTimeNotEqualTo(Long value) {
            addCriterion("SATISFY_INVOICE_TIME <>", value, "satisfyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyInvoiceTimeGreaterThan(Long value) {
            addCriterion("SATISFY_INVOICE_TIME >", value, "satisfyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyInvoiceTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("SATISFY_INVOICE_TIME >=", value, "satisfyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyInvoiceTimeLessThan(Long value) {
            addCriterion("SATISFY_INVOICE_TIME <", value, "satisfyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyInvoiceTimeLessThanOrEqualTo(Long value) {
            addCriterion("SATISFY_INVOICE_TIME <=", value, "satisfyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyInvoiceTimeIn(List<Long> values) {
            addCriterion("SATISFY_INVOICE_TIME in", values, "satisfyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyInvoiceTimeNotIn(List<Long> values) {
            addCriterion("SATISFY_INVOICE_TIME not in", values, "satisfyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyInvoiceTimeBetween(Long value1, Long value2) {
            addCriterion("SATISFY_INVOICE_TIME between", value1, value2, "satisfyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyInvoiceTimeNotBetween(Long value1, Long value2) {
            addCriterion("SATISFY_INVOICE_TIME not between", value1, value2, "satisfyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyDeliveryTimeIsNull() {
            addCriterion("SATISFY_DELIVERY_TIME is null");
            return (Criteria) this;
        }

        public Criteria andSatisfyDeliveryTimeIsNotNull() {
            addCriterion("SATISFY_DELIVERY_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andSatisfyDeliveryTimeEqualTo(Long value) {
            addCriterion("SATISFY_DELIVERY_TIME =", value, "satisfyDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyDeliveryTimeNotEqualTo(Long value) {
            addCriterion("SATISFY_DELIVERY_TIME <>", value, "satisfyDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyDeliveryTimeGreaterThan(Long value) {
            addCriterion("SATISFY_DELIVERY_TIME >", value, "satisfyDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyDeliveryTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("SATISFY_DELIVERY_TIME >=", value, "satisfyDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyDeliveryTimeLessThan(Long value) {
            addCriterion("SATISFY_DELIVERY_TIME <", value, "satisfyDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyDeliveryTimeLessThanOrEqualTo(Long value) {
            addCriterion("SATISFY_DELIVERY_TIME <=", value, "satisfyDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyDeliveryTimeIn(List<Long> values) {
            addCriterion("SATISFY_DELIVERY_TIME in", values, "satisfyDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyDeliveryTimeNotIn(List<Long> values) {
            addCriterion("SATISFY_DELIVERY_TIME not in", values, "satisfyDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyDeliveryTimeBetween(Long value1, Long value2) {
            addCriterion("SATISFY_DELIVERY_TIME between", value1, value2, "satisfyDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andSatisfyDeliveryTimeNotBetween(Long value1, Long value2) {
            addCriterion("SATISFY_DELIVERY_TIME not between", value1, value2, "satisfyDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andIsSalesPerformanceIsNull() {
            addCriterion("IS_SALES_PERFORMANCE is null");
            return (Criteria) this;
        }

        public Criteria andIsSalesPerformanceIsNotNull() {
            addCriterion("IS_SALES_PERFORMANCE is not null");
            return (Criteria) this;
        }

        public Criteria andIsSalesPerformanceEqualTo(Byte value) {
            addCriterion("IS_SALES_PERFORMANCE =", value, "isSalesPerformance");
            return (Criteria) this;
        }

        public Criteria andIsSalesPerformanceNotEqualTo(Byte value) {
            addCriterion("IS_SALES_PERFORMANCE <>", value, "isSalesPerformance");
            return (Criteria) this;
        }

        public Criteria andIsSalesPerformanceGreaterThan(Byte value) {
            addCriterion("IS_SALES_PERFORMANCE >", value, "isSalesPerformance");
            return (Criteria) this;
        }

        public Criteria andIsSalesPerformanceGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_SALES_PERFORMANCE >=", value, "isSalesPerformance");
            return (Criteria) this;
        }

        public Criteria andIsSalesPerformanceLessThan(Byte value) {
            addCriterion("IS_SALES_PERFORMANCE <", value, "isSalesPerformance");
            return (Criteria) this;
        }

        public Criteria andIsSalesPerformanceLessThanOrEqualTo(Byte value) {
            addCriterion("IS_SALES_PERFORMANCE <=", value, "isSalesPerformance");
            return (Criteria) this;
        }

        public Criteria andIsSalesPerformanceIn(List<Byte> values) {
            addCriterion("IS_SALES_PERFORMANCE in", values, "isSalesPerformance");
            return (Criteria) this;
        }

        public Criteria andIsSalesPerformanceNotIn(List<Byte> values) {
            addCriterion("IS_SALES_PERFORMANCE not in", values, "isSalesPerformance");
            return (Criteria) this;
        }

        public Criteria andIsSalesPerformanceBetween(Byte value1, Byte value2) {
            addCriterion("IS_SALES_PERFORMANCE between", value1, value2, "isSalesPerformance");
            return (Criteria) this;
        }

        public Criteria andIsSalesPerformanceNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_SALES_PERFORMANCE not between", value1, value2, "isSalesPerformance");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceTimeIsNull() {
            addCriterion("SALES_PERFORMANCE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceTimeIsNotNull() {
            addCriterion("SALES_PERFORMANCE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceTimeEqualTo(Long value) {
            addCriterion("SALES_PERFORMANCE_TIME =", value, "salesPerformanceTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceTimeNotEqualTo(Long value) {
            addCriterion("SALES_PERFORMANCE_TIME <>", value, "salesPerformanceTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceTimeGreaterThan(Long value) {
            addCriterion("SALES_PERFORMANCE_TIME >", value, "salesPerformanceTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("SALES_PERFORMANCE_TIME >=", value, "salesPerformanceTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceTimeLessThan(Long value) {
            addCriterion("SALES_PERFORMANCE_TIME <", value, "salesPerformanceTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceTimeLessThanOrEqualTo(Long value) {
            addCriterion("SALES_PERFORMANCE_TIME <=", value, "salesPerformanceTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceTimeIn(List<Long> values) {
            addCriterion("SALES_PERFORMANCE_TIME in", values, "salesPerformanceTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceTimeNotIn(List<Long> values) {
            addCriterion("SALES_PERFORMANCE_TIME not in", values, "salesPerformanceTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceTimeBetween(Long value1, Long value2) {
            addCriterion("SALES_PERFORMANCE_TIME between", value1, value2, "salesPerformanceTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceTimeNotBetween(Long value1, Long value2) {
            addCriterion("SALES_PERFORMANCE_TIME not between", value1, value2, "salesPerformanceTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceModTimeIsNull() {
            addCriterion("SALES_PERFORMANCE_MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceModTimeIsNotNull() {
            addCriterion("SALES_PERFORMANCE_MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceModTimeEqualTo(Long value) {
            addCriterion("SALES_PERFORMANCE_MOD_TIME =", value, "salesPerformanceModTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceModTimeNotEqualTo(Long value) {
            addCriterion("SALES_PERFORMANCE_MOD_TIME <>", value, "salesPerformanceModTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceModTimeGreaterThan(Long value) {
            addCriterion("SALES_PERFORMANCE_MOD_TIME >", value, "salesPerformanceModTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceModTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("SALES_PERFORMANCE_MOD_TIME >=", value, "salesPerformanceModTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceModTimeLessThan(Long value) {
            addCriterion("SALES_PERFORMANCE_MOD_TIME <", value, "salesPerformanceModTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceModTimeLessThanOrEqualTo(Long value) {
            addCriterion("SALES_PERFORMANCE_MOD_TIME <=", value, "salesPerformanceModTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceModTimeIn(List<Long> values) {
            addCriterion("SALES_PERFORMANCE_MOD_TIME in", values, "salesPerformanceModTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceModTimeNotIn(List<Long> values) {
            addCriterion("SALES_PERFORMANCE_MOD_TIME not in", values, "salesPerformanceModTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceModTimeBetween(Long value1, Long value2) {
            addCriterion("SALES_PERFORMANCE_MOD_TIME between", value1, value2, "salesPerformanceModTime");
            return (Criteria) this;
        }

        public Criteria andSalesPerformanceModTimeNotBetween(Long value1, Long value2) {
            addCriterion("SALES_PERFORMANCE_MOD_TIME not between", value1, value2, "salesPerformanceModTime");
            return (Criteria) this;
        }

        public Criteria andIsDelayInvoiceIsNull() {
            addCriterion("IS_DELAY_INVOICE is null");
            return (Criteria) this;
        }

        public Criteria andIsDelayInvoiceIsNotNull() {
            addCriterion("IS_DELAY_INVOICE is not null");
            return (Criteria) this;
        }

        public Criteria andIsDelayInvoiceEqualTo(Byte value) {
            addCriterion("IS_DELAY_INVOICE =", value, "isDelayInvoice");
            return (Criteria) this;
        }

        public Criteria andIsDelayInvoiceNotEqualTo(Byte value) {
            addCriterion("IS_DELAY_INVOICE <>", value, "isDelayInvoice");
            return (Criteria) this;
        }

        public Criteria andIsDelayInvoiceGreaterThan(Byte value) {
            addCriterion("IS_DELAY_INVOICE >", value, "isDelayInvoice");
            return (Criteria) this;
        }

        public Criteria andIsDelayInvoiceGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_DELAY_INVOICE >=", value, "isDelayInvoice");
            return (Criteria) this;
        }

        public Criteria andIsDelayInvoiceLessThan(Byte value) {
            addCriterion("IS_DELAY_INVOICE <", value, "isDelayInvoice");
            return (Criteria) this;
        }

        public Criteria andIsDelayInvoiceLessThanOrEqualTo(Byte value) {
            addCriterion("IS_DELAY_INVOICE <=", value, "isDelayInvoice");
            return (Criteria) this;
        }

        public Criteria andIsDelayInvoiceIn(List<Byte> values) {
            addCriterion("IS_DELAY_INVOICE in", values, "isDelayInvoice");
            return (Criteria) this;
        }

        public Criteria andIsDelayInvoiceNotIn(List<Byte> values) {
            addCriterion("IS_DELAY_INVOICE not in", values, "isDelayInvoice");
            return (Criteria) this;
        }

        public Criteria andIsDelayInvoiceBetween(Byte value1, Byte value2) {
            addCriterion("IS_DELAY_INVOICE between", value1, value2, "isDelayInvoice");
            return (Criteria) this;
        }

        public Criteria andIsDelayInvoiceNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_DELAY_INVOICE not between", value1, value2, "isDelayInvoice");
            return (Criteria) this;
        }

        public Criteria andInvoiceMethodIsNull() {
            addCriterion("INVOICE_METHOD is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceMethodIsNotNull() {
            addCriterion("INVOICE_METHOD is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceMethodEqualTo(Byte value) {
            addCriterion("INVOICE_METHOD =", value, "invoiceMethod");
            return (Criteria) this;
        }

        public Criteria andInvoiceMethodNotEqualTo(Byte value) {
            addCriterion("INVOICE_METHOD <>", value, "invoiceMethod");
            return (Criteria) this;
        }

        public Criteria andInvoiceMethodGreaterThan(Byte value) {
            addCriterion("INVOICE_METHOD >", value, "invoiceMethod");
            return (Criteria) this;
        }

        public Criteria andInvoiceMethodGreaterThanOrEqualTo(Byte value) {
            addCriterion("INVOICE_METHOD >=", value, "invoiceMethod");
            return (Criteria) this;
        }

        public Criteria andInvoiceMethodLessThan(Byte value) {
            addCriterion("INVOICE_METHOD <", value, "invoiceMethod");
            return (Criteria) this;
        }

        public Criteria andInvoiceMethodLessThanOrEqualTo(Byte value) {
            addCriterion("INVOICE_METHOD <=", value, "invoiceMethod");
            return (Criteria) this;
        }

        public Criteria andInvoiceMethodIn(List<Byte> values) {
            addCriterion("INVOICE_METHOD in", values, "invoiceMethod");
            return (Criteria) this;
        }

        public Criteria andInvoiceMethodNotIn(List<Byte> values) {
            addCriterion("INVOICE_METHOD not in", values, "invoiceMethod");
            return (Criteria) this;
        }

        public Criteria andInvoiceMethodBetween(Byte value1, Byte value2) {
            addCriterion("INVOICE_METHOD between", value1, value2, "invoiceMethod");
            return (Criteria) this;
        }

        public Criteria andInvoiceMethodNotBetween(Byte value1, Byte value2) {
            addCriterion("INVOICE_METHOD not between", value1, value2, "invoiceMethod");
            return (Criteria) this;
        }

        public Criteria andLockedReasonIsNull() {
            addCriterion("LOCKED_REASON is null");
            return (Criteria) this;
        }

        public Criteria andLockedReasonIsNotNull() {
            addCriterion("LOCKED_REASON is not null");
            return (Criteria) this;
        }

        public Criteria andLockedReasonEqualTo(String value) {
            addCriterion("LOCKED_REASON =", value, "lockedReason");
            return (Criteria) this;
        }

        public Criteria andLockedReasonNotEqualTo(String value) {
            addCriterion("LOCKED_REASON <>", value, "lockedReason");
            return (Criteria) this;
        }

        public Criteria andLockedReasonGreaterThan(String value) {
            addCriterion("LOCKED_REASON >", value, "lockedReason");
            return (Criteria) this;
        }

        public Criteria andLockedReasonGreaterThanOrEqualTo(String value) {
            addCriterion("LOCKED_REASON >=", value, "lockedReason");
            return (Criteria) this;
        }

        public Criteria andLockedReasonLessThan(String value) {
            addCriterion("LOCKED_REASON <", value, "lockedReason");
            return (Criteria) this;
        }

        public Criteria andLockedReasonLessThanOrEqualTo(String value) {
            addCriterion("LOCKED_REASON <=", value, "lockedReason");
            return (Criteria) this;
        }

        public Criteria andLockedReasonLike(String value) {
            addCriterion("LOCKED_REASON like", value, "lockedReason");
            return (Criteria) this;
        }

        public Criteria andLockedReasonNotLike(String value) {
            addCriterion("LOCKED_REASON not like", value, "lockedReason");
            return (Criteria) this;
        }

        public Criteria andLockedReasonIn(List<String> values) {
            addCriterion("LOCKED_REASON in", values, "lockedReason");
            return (Criteria) this;
        }

        public Criteria andLockedReasonNotIn(List<String> values) {
            addCriterion("LOCKED_REASON not in", values, "lockedReason");
            return (Criteria) this;
        }

        public Criteria andLockedReasonBetween(String value1, String value2) {
            addCriterion("LOCKED_REASON between", value1, value2, "lockedReason");
            return (Criteria) this;
        }

        public Criteria andLockedReasonNotBetween(String value1, String value2) {
            addCriterion("LOCKED_REASON not between", value1, value2, "lockedReason");
            return (Criteria) this;
        }

        public Criteria andCostUserIdsIsNull() {
            addCriterion("COST_USER_IDS is null");
            return (Criteria) this;
        }

        public Criteria andCostUserIdsIsNotNull() {
            addCriterion("COST_USER_IDS is not null");
            return (Criteria) this;
        }

        public Criteria andCostUserIdsEqualTo(String value) {
            addCriterion("COST_USER_IDS =", value, "costUserIds");
            return (Criteria) this;
        }

        public Criteria andCostUserIdsNotEqualTo(String value) {
            addCriterion("COST_USER_IDS <>", value, "costUserIds");
            return (Criteria) this;
        }

        public Criteria andCostUserIdsGreaterThan(String value) {
            addCriterion("COST_USER_IDS >", value, "costUserIds");
            return (Criteria) this;
        }

        public Criteria andCostUserIdsGreaterThanOrEqualTo(String value) {
            addCriterion("COST_USER_IDS >=", value, "costUserIds");
            return (Criteria) this;
        }

        public Criteria andCostUserIdsLessThan(String value) {
            addCriterion("COST_USER_IDS <", value, "costUserIds");
            return (Criteria) this;
        }

        public Criteria andCostUserIdsLessThanOrEqualTo(String value) {
            addCriterion("COST_USER_IDS <=", value, "costUserIds");
            return (Criteria) this;
        }

        public Criteria andCostUserIdsLike(String value) {
            addCriterion("COST_USER_IDS like", value, "costUserIds");
            return (Criteria) this;
        }

        public Criteria andCostUserIdsNotLike(String value) {
            addCriterion("COST_USER_IDS not like", value, "costUserIds");
            return (Criteria) this;
        }

        public Criteria andCostUserIdsIn(List<String> values) {
            addCriterion("COST_USER_IDS in", values, "costUserIds");
            return (Criteria) this;
        }

        public Criteria andCostUserIdsNotIn(List<String> values) {
            addCriterion("COST_USER_IDS not in", values, "costUserIds");
            return (Criteria) this;
        }

        public Criteria andCostUserIdsBetween(String value1, String value2) {
            addCriterion("COST_USER_IDS between", value1, value2, "costUserIds");
            return (Criteria) this;
        }

        public Criteria andCostUserIdsNotBetween(String value1, String value2) {
            addCriterion("COST_USER_IDS not between", value1, value2, "costUserIds");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdIsNull() {
            addCriterion("OWNER_USER_ID is null");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdIsNotNull() {
            addCriterion("OWNER_USER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdEqualTo(Integer value) {
            addCriterion("OWNER_USER_ID =", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdNotEqualTo(Integer value) {
            addCriterion("OWNER_USER_ID <>", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdGreaterThan(Integer value) {
            addCriterion("OWNER_USER_ID >", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("OWNER_USER_ID >=", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdLessThan(Integer value) {
            addCriterion("OWNER_USER_ID <", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("OWNER_USER_ID <=", value, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdIn(List<Integer> values) {
            addCriterion("OWNER_USER_ID in", values, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdNotIn(List<Integer> values) {
            addCriterion("OWNER_USER_ID not in", values, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdBetween(Integer value1, Integer value2) {
            addCriterion("OWNER_USER_ID between", value1, value2, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andOwnerUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("OWNER_USER_ID not between", value1, value2, "ownerUserId");
            return (Criteria) this;
        }

        public Criteria andInvoiceEmailIsNull() {
            addCriterion("INVOICE_EMAIL is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceEmailIsNotNull() {
            addCriterion("INVOICE_EMAIL is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceEmailEqualTo(String value) {
            addCriterion("INVOICE_EMAIL =", value, "invoiceEmail");
            return (Criteria) this;
        }

        public Criteria andInvoiceEmailNotEqualTo(String value) {
            addCriterion("INVOICE_EMAIL <>", value, "invoiceEmail");
            return (Criteria) this;
        }

        public Criteria andInvoiceEmailGreaterThan(String value) {
            addCriterion("INVOICE_EMAIL >", value, "invoiceEmail");
            return (Criteria) this;
        }

        public Criteria andInvoiceEmailGreaterThanOrEqualTo(String value) {
            addCriterion("INVOICE_EMAIL >=", value, "invoiceEmail");
            return (Criteria) this;
        }

        public Criteria andInvoiceEmailLessThan(String value) {
            addCriterion("INVOICE_EMAIL <", value, "invoiceEmail");
            return (Criteria) this;
        }

        public Criteria andInvoiceEmailLessThanOrEqualTo(String value) {
            addCriterion("INVOICE_EMAIL <=", value, "invoiceEmail");
            return (Criteria) this;
        }

        public Criteria andInvoiceEmailLike(String value) {
            addCriterion("INVOICE_EMAIL like", value, "invoiceEmail");
            return (Criteria) this;
        }

        public Criteria andInvoiceEmailNotLike(String value) {
            addCriterion("INVOICE_EMAIL not like", value, "invoiceEmail");
            return (Criteria) this;
        }

        public Criteria andInvoiceEmailIn(List<String> values) {
            addCriterion("INVOICE_EMAIL in", values, "invoiceEmail");
            return (Criteria) this;
        }

        public Criteria andInvoiceEmailNotIn(List<String> values) {
            addCriterion("INVOICE_EMAIL not in", values, "invoiceEmail");
            return (Criteria) this;
        }

        public Criteria andInvoiceEmailBetween(String value1, String value2) {
            addCriterion("INVOICE_EMAIL between", value1, value2, "invoiceEmail");
            return (Criteria) this;
        }

        public Criteria andInvoiceEmailNotBetween(String value1, String value2) {
            addCriterion("INVOICE_EMAIL not between", value1, value2, "invoiceEmail");
            return (Criteria) this;
        }

        public Criteria andPaymentModeIsNull() {
            addCriterion("PAYMENT_MODE is null");
            return (Criteria) this;
        }

        public Criteria andPaymentModeIsNotNull() {
            addCriterion("PAYMENT_MODE is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentModeEqualTo(Byte value) {
            addCriterion("PAYMENT_MODE =", value, "paymentMode");
            return (Criteria) this;
        }

        public Criteria andPaymentModeNotEqualTo(Byte value) {
            addCriterion("PAYMENT_MODE <>", value, "paymentMode");
            return (Criteria) this;
        }

        public Criteria andPaymentModeGreaterThan(Byte value) {
            addCriterion("PAYMENT_MODE >", value, "paymentMode");
            return (Criteria) this;
        }

        public Criteria andPaymentModeGreaterThanOrEqualTo(Byte value) {
            addCriterion("PAYMENT_MODE >=", value, "paymentMode");
            return (Criteria) this;
        }

        public Criteria andPaymentModeLessThan(Byte value) {
            addCriterion("PAYMENT_MODE <", value, "paymentMode");
            return (Criteria) this;
        }

        public Criteria andPaymentModeLessThanOrEqualTo(Byte value) {
            addCriterion("PAYMENT_MODE <=", value, "paymentMode");
            return (Criteria) this;
        }

        public Criteria andPaymentModeIn(List<Byte> values) {
            addCriterion("PAYMENT_MODE in", values, "paymentMode");
            return (Criteria) this;
        }

        public Criteria andPaymentModeNotIn(List<Byte> values) {
            addCriterion("PAYMENT_MODE not in", values, "paymentMode");
            return (Criteria) this;
        }

        public Criteria andPaymentModeBetween(Byte value1, Byte value2) {
            addCriterion("PAYMENT_MODE between", value1, value2, "paymentMode");
            return (Criteria) this;
        }

        public Criteria andPaymentModeNotBetween(Byte value1, Byte value2) {
            addCriterion("PAYMENT_MODE not between", value1, value2, "paymentMode");
            return (Criteria) this;
        }

        public Criteria andPayTypeIsNull() {
            addCriterion("PAY_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andPayTypeIsNotNull() {
            addCriterion("PAY_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andPayTypeEqualTo(Byte value) {
            addCriterion("PAY_TYPE =", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeNotEqualTo(Byte value) {
            addCriterion("PAY_TYPE <>", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeGreaterThan(Byte value) {
            addCriterion("PAY_TYPE >", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("PAY_TYPE >=", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeLessThan(Byte value) {
            addCriterion("PAY_TYPE <", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeLessThanOrEqualTo(Byte value) {
            addCriterion("PAY_TYPE <=", value, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeIn(List<Byte> values) {
            addCriterion("PAY_TYPE in", values, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeNotIn(List<Byte> values) {
            addCriterion("PAY_TYPE not in", values, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeBetween(Byte value1, Byte value2) {
            addCriterion("PAY_TYPE between", value1, value2, "payType");
            return (Criteria) this;
        }

        public Criteria andPayTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("PAY_TYPE not between", value1, value2, "payType");
            return (Criteria) this;
        }

        public Criteria andIsApplyInvoiceIsNull() {
            addCriterion("IS_APPLY_INVOICE is null");
            return (Criteria) this;
        }

        public Criteria andIsApplyInvoiceIsNotNull() {
            addCriterion("IS_APPLY_INVOICE is not null");
            return (Criteria) this;
        }

        public Criteria andIsApplyInvoiceEqualTo(Byte value) {
            addCriterion("IS_APPLY_INVOICE =", value, "isApplyInvoice");
            return (Criteria) this;
        }

        public Criteria andIsApplyInvoiceNotEqualTo(Byte value) {
            addCriterion("IS_APPLY_INVOICE <>", value, "isApplyInvoice");
            return (Criteria) this;
        }

        public Criteria andIsApplyInvoiceGreaterThan(Byte value) {
            addCriterion("IS_APPLY_INVOICE >", value, "isApplyInvoice");
            return (Criteria) this;
        }

        public Criteria andIsApplyInvoiceGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_APPLY_INVOICE >=", value, "isApplyInvoice");
            return (Criteria) this;
        }

        public Criteria andIsApplyInvoiceLessThan(Byte value) {
            addCriterion("IS_APPLY_INVOICE <", value, "isApplyInvoice");
            return (Criteria) this;
        }

        public Criteria andIsApplyInvoiceLessThanOrEqualTo(Byte value) {
            addCriterion("IS_APPLY_INVOICE <=", value, "isApplyInvoice");
            return (Criteria) this;
        }

        public Criteria andIsApplyInvoiceIn(List<Byte> values) {
            addCriterion("IS_APPLY_INVOICE in", values, "isApplyInvoice");
            return (Criteria) this;
        }

        public Criteria andIsApplyInvoiceNotIn(List<Byte> values) {
            addCriterion("IS_APPLY_INVOICE not in", values, "isApplyInvoice");
            return (Criteria) this;
        }

        public Criteria andIsApplyInvoiceBetween(Byte value1, Byte value2) {
            addCriterion("IS_APPLY_INVOICE between", value1, value2, "isApplyInvoice");
            return (Criteria) this;
        }

        public Criteria andIsApplyInvoiceNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_APPLY_INVOICE not between", value1, value2, "isApplyInvoice");
            return (Criteria) this;
        }

        public Criteria andApplyInvoiceTimeIsNull() {
            addCriterion("APPLY_INVOICE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andApplyInvoiceTimeIsNotNull() {
            addCriterion("APPLY_INVOICE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andApplyInvoiceTimeEqualTo(Long value) {
            addCriterion("APPLY_INVOICE_TIME =", value, "applyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andApplyInvoiceTimeNotEqualTo(Long value) {
            addCriterion("APPLY_INVOICE_TIME <>", value, "applyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andApplyInvoiceTimeGreaterThan(Long value) {
            addCriterion("APPLY_INVOICE_TIME >", value, "applyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andApplyInvoiceTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("APPLY_INVOICE_TIME >=", value, "applyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andApplyInvoiceTimeLessThan(Long value) {
            addCriterion("APPLY_INVOICE_TIME <", value, "applyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andApplyInvoiceTimeLessThanOrEqualTo(Long value) {
            addCriterion("APPLY_INVOICE_TIME <=", value, "applyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andApplyInvoiceTimeIn(List<Long> values) {
            addCriterion("APPLY_INVOICE_TIME in", values, "applyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andApplyInvoiceTimeNotIn(List<Long> values) {
            addCriterion("APPLY_INVOICE_TIME not in", values, "applyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andApplyInvoiceTimeBetween(Long value1, Long value2) {
            addCriterion("APPLY_INVOICE_TIME between", value1, value2, "applyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andApplyInvoiceTimeNotBetween(Long value1, Long value2) {
            addCriterion("APPLY_INVOICE_TIME not between", value1, value2, "applyInvoiceTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Long value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Long value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Long value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Long value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Long> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Long> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Long value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Long value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Long value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Long value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Long> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Long> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andAdkSaleorderNoIsNull() {
            addCriterion("ADK_SALEORDER_NO is null");
            return (Criteria) this;
        }

        public Criteria andAdkSaleorderNoIsNotNull() {
            addCriterion("ADK_SALEORDER_NO is not null");
            return (Criteria) this;
        }

        public Criteria andAdkSaleorderNoEqualTo(String value) {
            addCriterion("ADK_SALEORDER_NO =", value, "adkSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andAdkSaleorderNoNotEqualTo(String value) {
            addCriterion("ADK_SALEORDER_NO <>", value, "adkSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andAdkSaleorderNoGreaterThan(String value) {
            addCriterion("ADK_SALEORDER_NO >", value, "adkSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andAdkSaleorderNoGreaterThanOrEqualTo(String value) {
            addCriterion("ADK_SALEORDER_NO >=", value, "adkSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andAdkSaleorderNoLessThan(String value) {
            addCriterion("ADK_SALEORDER_NO <", value, "adkSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andAdkSaleorderNoLessThanOrEqualTo(String value) {
            addCriterion("ADK_SALEORDER_NO <=", value, "adkSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andAdkSaleorderNoLike(String value) {
            addCriterion("ADK_SALEORDER_NO like", value, "adkSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andAdkSaleorderNoNotLike(String value) {
            addCriterion("ADK_SALEORDER_NO not like", value, "adkSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andAdkSaleorderNoIn(List<String> values) {
            addCriterion("ADK_SALEORDER_NO in", values, "adkSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andAdkSaleorderNoNotIn(List<String> values) {
            addCriterion("ADK_SALEORDER_NO not in", values, "adkSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andAdkSaleorderNoBetween(String value1, String value2) {
            addCriterion("ADK_SALEORDER_NO between", value1, value2, "adkSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andAdkSaleorderNoNotBetween(String value1, String value2) {
            addCriterion("ADK_SALEORDER_NO not between", value1, value2, "adkSaleorderNo");
            return (Criteria) this;
        }

        public Criteria andCreateMobileIsNull() {
            addCriterion("CREATE_MOBILE is null");
            return (Criteria) this;
        }

        public Criteria andCreateMobileIsNotNull() {
            addCriterion("CREATE_MOBILE is not null");
            return (Criteria) this;
        }

        public Criteria andCreateMobileEqualTo(String value) {
            addCriterion("CREATE_MOBILE =", value, "createMobile");
            return (Criteria) this;
        }

        public Criteria andCreateMobileNotEqualTo(String value) {
            addCriterion("CREATE_MOBILE <>", value, "createMobile");
            return (Criteria) this;
        }

        public Criteria andCreateMobileGreaterThan(String value) {
            addCriterion("CREATE_MOBILE >", value, "createMobile");
            return (Criteria) this;
        }

        public Criteria andCreateMobileGreaterThanOrEqualTo(String value) {
            addCriterion("CREATE_MOBILE >=", value, "createMobile");
            return (Criteria) this;
        }

        public Criteria andCreateMobileLessThan(String value) {
            addCriterion("CREATE_MOBILE <", value, "createMobile");
            return (Criteria) this;
        }

        public Criteria andCreateMobileLessThanOrEqualTo(String value) {
            addCriterion("CREATE_MOBILE <=", value, "createMobile");
            return (Criteria) this;
        }

        public Criteria andCreateMobileLike(String value) {
            addCriterion("CREATE_MOBILE like", value, "createMobile");
            return (Criteria) this;
        }

        public Criteria andCreateMobileNotLike(String value) {
            addCriterion("CREATE_MOBILE not like", value, "createMobile");
            return (Criteria) this;
        }

        public Criteria andCreateMobileIn(List<String> values) {
            addCriterion("CREATE_MOBILE in", values, "createMobile");
            return (Criteria) this;
        }

        public Criteria andCreateMobileNotIn(List<String> values) {
            addCriterion("CREATE_MOBILE not in", values, "createMobile");
            return (Criteria) this;
        }

        public Criteria andCreateMobileBetween(String value1, String value2) {
            addCriterion("CREATE_MOBILE between", value1, value2, "createMobile");
            return (Criteria) this;
        }

        public Criteria andCreateMobileNotBetween(String value1, String value2) {
            addCriterion("CREATE_MOBILE not between", value1, value2, "createMobile");
            return (Criteria) this;
        }

        public Criteria andBdtraderCommentsIsNull() {
            addCriterion("BDTRADER_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andBdtraderCommentsIsNotNull() {
            addCriterion("BDTRADER_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andBdtraderCommentsEqualTo(String value) {
            addCriterion("BDTRADER_COMMENTS =", value, "bdtraderComments");
            return (Criteria) this;
        }

        public Criteria andBdtraderCommentsNotEqualTo(String value) {
            addCriterion("BDTRADER_COMMENTS <>", value, "bdtraderComments");
            return (Criteria) this;
        }

        public Criteria andBdtraderCommentsGreaterThan(String value) {
            addCriterion("BDTRADER_COMMENTS >", value, "bdtraderComments");
            return (Criteria) this;
        }

        public Criteria andBdtraderCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("BDTRADER_COMMENTS >=", value, "bdtraderComments");
            return (Criteria) this;
        }

        public Criteria andBdtraderCommentsLessThan(String value) {
            addCriterion("BDTRADER_COMMENTS <", value, "bdtraderComments");
            return (Criteria) this;
        }

        public Criteria andBdtraderCommentsLessThanOrEqualTo(String value) {
            addCriterion("BDTRADER_COMMENTS <=", value, "bdtraderComments");
            return (Criteria) this;
        }

        public Criteria andBdtraderCommentsLike(String value) {
            addCriterion("BDTRADER_COMMENTS like", value, "bdtraderComments");
            return (Criteria) this;
        }

        public Criteria andBdtraderCommentsNotLike(String value) {
            addCriterion("BDTRADER_COMMENTS not like", value, "bdtraderComments");
            return (Criteria) this;
        }

        public Criteria andBdtraderCommentsIn(List<String> values) {
            addCriterion("BDTRADER_COMMENTS in", values, "bdtraderComments");
            return (Criteria) this;
        }

        public Criteria andBdtraderCommentsNotIn(List<String> values) {
            addCriterion("BDTRADER_COMMENTS not in", values, "bdtraderComments");
            return (Criteria) this;
        }

        public Criteria andBdtraderCommentsBetween(String value1, String value2) {
            addCriterion("BDTRADER_COMMENTS between", value1, value2, "bdtraderComments");
            return (Criteria) this;
        }

        public Criteria andBdtraderCommentsNotBetween(String value1, String value2) {
            addCriterion("BDTRADER_COMMENTS not between", value1, value2, "bdtraderComments");
            return (Criteria) this;
        }

        public Criteria andCloseCommentsIsNull() {
            addCriterion("CLOSE_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andCloseCommentsIsNotNull() {
            addCriterion("CLOSE_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andCloseCommentsEqualTo(String value) {
            addCriterion("CLOSE_COMMENTS =", value, "closeComments");
            return (Criteria) this;
        }

        public Criteria andCloseCommentsNotEqualTo(String value) {
            addCriterion("CLOSE_COMMENTS <>", value, "closeComments");
            return (Criteria) this;
        }

        public Criteria andCloseCommentsGreaterThan(String value) {
            addCriterion("CLOSE_COMMENTS >", value, "closeComments");
            return (Criteria) this;
        }

        public Criteria andCloseCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("CLOSE_COMMENTS >=", value, "closeComments");
            return (Criteria) this;
        }

        public Criteria andCloseCommentsLessThan(String value) {
            addCriterion("CLOSE_COMMENTS <", value, "closeComments");
            return (Criteria) this;
        }

        public Criteria andCloseCommentsLessThanOrEqualTo(String value) {
            addCriterion("CLOSE_COMMENTS <=", value, "closeComments");
            return (Criteria) this;
        }

        public Criteria andCloseCommentsLike(String value) {
            addCriterion("CLOSE_COMMENTS like", value, "closeComments");
            return (Criteria) this;
        }

        public Criteria andCloseCommentsNotLike(String value) {
            addCriterion("CLOSE_COMMENTS not like", value, "closeComments");
            return (Criteria) this;
        }

        public Criteria andCloseCommentsIn(List<String> values) {
            addCriterion("CLOSE_COMMENTS in", values, "closeComments");
            return (Criteria) this;
        }

        public Criteria andCloseCommentsNotIn(List<String> values) {
            addCriterion("CLOSE_COMMENTS not in", values, "closeComments");
            return (Criteria) this;
        }

        public Criteria andCloseCommentsBetween(String value1, String value2) {
            addCriterion("CLOSE_COMMENTS between", value1, value2, "closeComments");
            return (Criteria) this;
        }

        public Criteria andCloseCommentsNotBetween(String value1, String value2) {
            addCriterion("CLOSE_COMMENTS not between", value1, value2, "closeComments");
            return (Criteria) this;
        }

        public Criteria andBdMobileTimeIsNull() {
            addCriterion("BD_MOBILE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andBdMobileTimeIsNotNull() {
            addCriterion("BD_MOBILE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andBdMobileTimeEqualTo(Long value) {
            addCriterion("BD_MOBILE_TIME =", value, "bdMobileTime");
            return (Criteria) this;
        }

        public Criteria andBdMobileTimeNotEqualTo(Long value) {
            addCriterion("BD_MOBILE_TIME <>", value, "bdMobileTime");
            return (Criteria) this;
        }

        public Criteria andBdMobileTimeGreaterThan(Long value) {
            addCriterion("BD_MOBILE_TIME >", value, "bdMobileTime");
            return (Criteria) this;
        }

        public Criteria andBdMobileTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("BD_MOBILE_TIME >=", value, "bdMobileTime");
            return (Criteria) this;
        }

        public Criteria andBdMobileTimeLessThan(Long value) {
            addCriterion("BD_MOBILE_TIME <", value, "bdMobileTime");
            return (Criteria) this;
        }

        public Criteria andBdMobileTimeLessThanOrEqualTo(Long value) {
            addCriterion("BD_MOBILE_TIME <=", value, "bdMobileTime");
            return (Criteria) this;
        }

        public Criteria andBdMobileTimeIn(List<Long> values) {
            addCriterion("BD_MOBILE_TIME in", values, "bdMobileTime");
            return (Criteria) this;
        }

        public Criteria andBdMobileTimeNotIn(List<Long> values) {
            addCriterion("BD_MOBILE_TIME not in", values, "bdMobileTime");
            return (Criteria) this;
        }

        public Criteria andBdMobileTimeBetween(Long value1, Long value2) {
            addCriterion("BD_MOBILE_TIME between", value1, value2, "bdMobileTime");
            return (Criteria) this;
        }

        public Criteria andBdMobileTimeNotBetween(Long value1, Long value2) {
            addCriterion("BD_MOBILE_TIME not between", value1, value2, "bdMobileTime");
            return (Criteria) this;
        }

        public Criteria andWebTakeDeliveryTimeIsNull() {
            addCriterion("WEB_TAKE_DELIVERY_TIME is null");
            return (Criteria) this;
        }

        public Criteria andWebTakeDeliveryTimeIsNotNull() {
            addCriterion("WEB_TAKE_DELIVERY_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andWebTakeDeliveryTimeEqualTo(Long value) {
            addCriterion("WEB_TAKE_DELIVERY_TIME =", value, "webTakeDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andWebTakeDeliveryTimeNotEqualTo(Long value) {
            addCriterion("WEB_TAKE_DELIVERY_TIME <>", value, "webTakeDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andWebTakeDeliveryTimeGreaterThan(Long value) {
            addCriterion("WEB_TAKE_DELIVERY_TIME >", value, "webTakeDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andWebTakeDeliveryTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("WEB_TAKE_DELIVERY_TIME >=", value, "webTakeDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andWebTakeDeliveryTimeLessThan(Long value) {
            addCriterion("WEB_TAKE_DELIVERY_TIME <", value, "webTakeDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andWebTakeDeliveryTimeLessThanOrEqualTo(Long value) {
            addCriterion("WEB_TAKE_DELIVERY_TIME <=", value, "webTakeDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andWebTakeDeliveryTimeIn(List<Long> values) {
            addCriterion("WEB_TAKE_DELIVERY_TIME in", values, "webTakeDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andWebTakeDeliveryTimeNotIn(List<Long> values) {
            addCriterion("WEB_TAKE_DELIVERY_TIME not in", values, "webTakeDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andWebTakeDeliveryTimeBetween(Long value1, Long value2) {
            addCriterion("WEB_TAKE_DELIVERY_TIME between", value1, value2, "webTakeDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andWebTakeDeliveryTimeNotBetween(Long value1, Long value2) {
            addCriterion("WEB_TAKE_DELIVERY_TIME not between", value1, value2, "webTakeDeliveryTime");
            return (Criteria) this;
        }

        public Criteria andActionIdIsNull() {
            addCriterion("ACTION_ID is null");
            return (Criteria) this;
        }

        public Criteria andActionIdIsNotNull() {
            addCriterion("ACTION_ID is not null");
            return (Criteria) this;
        }

        public Criteria andActionIdEqualTo(Integer value) {
            addCriterion("ACTION_ID =", value, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdNotEqualTo(Integer value) {
            addCriterion("ACTION_ID <>", value, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdGreaterThan(Integer value) {
            addCriterion("ACTION_ID >", value, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ACTION_ID >=", value, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdLessThan(Integer value) {
            addCriterion("ACTION_ID <", value, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdLessThanOrEqualTo(Integer value) {
            addCriterion("ACTION_ID <=", value, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdIn(List<Integer> values) {
            addCriterion("ACTION_ID in", values, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdNotIn(List<Integer> values) {
            addCriterion("ACTION_ID not in", values, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdBetween(Integer value1, Integer value2) {
            addCriterion("ACTION_ID between", value1, value2, "actionId");
            return (Criteria) this;
        }

        public Criteria andActionIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ACTION_ID not between", value1, value2, "actionId");
            return (Criteria) this;
        }

        public Criteria andIsCouponsIsNull() {
            addCriterion("IS_COUPONS is null");
            return (Criteria) this;
        }

        public Criteria andIsCouponsIsNotNull() {
            addCriterion("IS_COUPONS is not null");
            return (Criteria) this;
        }

        public Criteria andIsCouponsEqualTo(Byte value) {
            addCriterion("IS_COUPONS =", value, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsNotEqualTo(Byte value) {
            addCriterion("IS_COUPONS <>", value, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsGreaterThan(Byte value) {
            addCriterion("IS_COUPONS >", value, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_COUPONS >=", value, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsLessThan(Byte value) {
            addCriterion("IS_COUPONS <", value, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsLessThanOrEqualTo(Byte value) {
            addCriterion("IS_COUPONS <=", value, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsIn(List<Byte> values) {
            addCriterion("IS_COUPONS in", values, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsNotIn(List<Byte> values) {
            addCriterion("IS_COUPONS not in", values, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsBetween(Byte value1, Byte value2) {
            addCriterion("IS_COUPONS between", value1, value2, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andIsCouponsNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_COUPONS not between", value1, value2, "isCoupons");
            return (Criteria) this;
        }

        public Criteria andElSaleordreNoIsNull() {
            addCriterion("EL_SALEORDRE_NO is null");
            return (Criteria) this;
        }

        public Criteria andElSaleordreNoIsNotNull() {
            addCriterion("EL_SALEORDRE_NO is not null");
            return (Criteria) this;
        }

        public Criteria andElSaleordreNoEqualTo(String value) {
            addCriterion("EL_SALEORDRE_NO =", value, "elSaleordreNo");
            return (Criteria) this;
        }

        public Criteria andElSaleordreNoNotEqualTo(String value) {
            addCriterion("EL_SALEORDRE_NO <>", value, "elSaleordreNo");
            return (Criteria) this;
        }

        public Criteria andElSaleordreNoGreaterThan(String value) {
            addCriterion("EL_SALEORDRE_NO >", value, "elSaleordreNo");
            return (Criteria) this;
        }

        public Criteria andElSaleordreNoGreaterThanOrEqualTo(String value) {
            addCriterion("EL_SALEORDRE_NO >=", value, "elSaleordreNo");
            return (Criteria) this;
        }

        public Criteria andElSaleordreNoLessThan(String value) {
            addCriterion("EL_SALEORDRE_NO <", value, "elSaleordreNo");
            return (Criteria) this;
        }

        public Criteria andElSaleordreNoLessThanOrEqualTo(String value) {
            addCriterion("EL_SALEORDRE_NO <=", value, "elSaleordreNo");
            return (Criteria) this;
        }

        public Criteria andElSaleordreNoLike(String value) {
            addCriterion("EL_SALEORDRE_NO like", value, "elSaleordreNo");
            return (Criteria) this;
        }

        public Criteria andElSaleordreNoNotLike(String value) {
            addCriterion("EL_SALEORDRE_NO not like", value, "elSaleordreNo");
            return (Criteria) this;
        }

        public Criteria andElSaleordreNoIn(List<String> values) {
            addCriterion("EL_SALEORDRE_NO in", values, "elSaleordreNo");
            return (Criteria) this;
        }

        public Criteria andElSaleordreNoNotIn(List<String> values) {
            addCriterion("EL_SALEORDRE_NO not in", values, "elSaleordreNo");
            return (Criteria) this;
        }

        public Criteria andElSaleordreNoBetween(String value1, String value2) {
            addCriterion("EL_SALEORDRE_NO between", value1, value2, "elSaleordreNo");
            return (Criteria) this;
        }

        public Criteria andElSaleordreNoNotBetween(String value1, String value2) {
            addCriterion("EL_SALEORDRE_NO not between", value1, value2, "elSaleordreNo");
            return (Criteria) this;
        }

        public Criteria andCouponmoneyIsNull() {
            addCriterion("COUPONMONEY is null");
            return (Criteria) this;
        }

        public Criteria andCouponmoneyIsNotNull() {
            addCriterion("COUPONMONEY is not null");
            return (Criteria) this;
        }

        public Criteria andCouponmoneyEqualTo(BigDecimal value) {
            addCriterion("COUPONMONEY =", value, "couponmoney");
            return (Criteria) this;
        }

        public Criteria andCouponmoneyNotEqualTo(BigDecimal value) {
            addCriterion("COUPONMONEY <>", value, "couponmoney");
            return (Criteria) this;
        }

        public Criteria andCouponmoneyGreaterThan(BigDecimal value) {
            addCriterion("COUPONMONEY >", value, "couponmoney");
            return (Criteria) this;
        }

        public Criteria andCouponmoneyGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("COUPONMONEY >=", value, "couponmoney");
            return (Criteria) this;
        }

        public Criteria andCouponmoneyLessThan(BigDecimal value) {
            addCriterion("COUPONMONEY <", value, "couponmoney");
            return (Criteria) this;
        }

        public Criteria andCouponmoneyLessThanOrEqualTo(BigDecimal value) {
            addCriterion("COUPONMONEY <=", value, "couponmoney");
            return (Criteria) this;
        }

        public Criteria andCouponmoneyIn(List<BigDecimal> values) {
            addCriterion("COUPONMONEY in", values, "couponmoney");
            return (Criteria) this;
        }

        public Criteria andCouponmoneyNotIn(List<BigDecimal> values) {
            addCriterion("COUPONMONEY not in", values, "couponmoney");
            return (Criteria) this;
        }

        public Criteria andCouponmoneyBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COUPONMONEY between", value1, value2, "couponmoney");
            return (Criteria) this;
        }

        public Criteria andCouponmoneyNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("COUPONMONEY not between", value1, value2, "couponmoney");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountIsNull() {
            addCriterion("ORIGINAL_AMOUNT is null");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountIsNotNull() {
            addCriterion("ORIGINAL_AMOUNT is not null");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountEqualTo(BigDecimal value) {
            addCriterion("ORIGINAL_AMOUNT =", value, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountNotEqualTo(BigDecimal value) {
            addCriterion("ORIGINAL_AMOUNT <>", value, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountGreaterThan(BigDecimal value) {
            addCriterion("ORIGINAL_AMOUNT >", value, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ORIGINAL_AMOUNT >=", value, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountLessThan(BigDecimal value) {
            addCriterion("ORIGINAL_AMOUNT <", value, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("ORIGINAL_AMOUNT <=", value, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountIn(List<BigDecimal> values) {
            addCriterion("ORIGINAL_AMOUNT in", values, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountNotIn(List<BigDecimal> values) {
            addCriterion("ORIGINAL_AMOUNT not in", values, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ORIGINAL_AMOUNT between", value1, value2, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andOriginalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ORIGINAL_AMOUNT not between", value1, value2, "originalAmount");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutIsNull() {
            addCriterion("IS_PRINTOUT is null");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutIsNotNull() {
            addCriterion("IS_PRINTOUT is not null");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutEqualTo(Byte value) {
            addCriterion("IS_PRINTOUT =", value, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutNotEqualTo(Byte value) {
            addCriterion("IS_PRINTOUT <>", value, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutGreaterThan(Byte value) {
            addCriterion("IS_PRINTOUT >", value, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutGreaterThanOrEqualTo(Byte value) {
            addCriterion("IS_PRINTOUT >=", value, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutLessThan(Byte value) {
            addCriterion("IS_PRINTOUT <", value, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutLessThanOrEqualTo(Byte value) {
            addCriterion("IS_PRINTOUT <=", value, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutIn(List<Byte> values) {
            addCriterion("IS_PRINTOUT in", values, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutNotIn(List<Byte> values) {
            addCriterion("IS_PRINTOUT not in", values, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutBetween(Byte value1, Byte value2) {
            addCriterion("IS_PRINTOUT between", value1, value2, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andIsPrintoutNotBetween(Byte value1, Byte value2) {
            addCriterion("IS_PRINTOUT not between", value1, value2, "isPrintout");
            return (Criteria) this;
        }

        public Criteria andOutIsFlagIsNull() {
            addCriterion("OUT_IS_FLAG is null");
            return (Criteria) this;
        }

        public Criteria andOutIsFlagIsNotNull() {
            addCriterion("OUT_IS_FLAG is not null");
            return (Criteria) this;
        }

        public Criteria andOutIsFlagEqualTo(Byte value) {
            addCriterion("OUT_IS_FLAG =", value, "outIsFlag");
            return (Criteria) this;
        }

        public Criteria andOutIsFlagNotEqualTo(Byte value) {
            addCriterion("OUT_IS_FLAG <>", value, "outIsFlag");
            return (Criteria) this;
        }

        public Criteria andOutIsFlagGreaterThan(Byte value) {
            addCriterion("OUT_IS_FLAG >", value, "outIsFlag");
            return (Criteria) this;
        }

        public Criteria andOutIsFlagGreaterThanOrEqualTo(Byte value) {
            addCriterion("OUT_IS_FLAG >=", value, "outIsFlag");
            return (Criteria) this;
        }

        public Criteria andOutIsFlagLessThan(Byte value) {
            addCriterion("OUT_IS_FLAG <", value, "outIsFlag");
            return (Criteria) this;
        }

        public Criteria andOutIsFlagLessThanOrEqualTo(Byte value) {
            addCriterion("OUT_IS_FLAG <=", value, "outIsFlag");
            return (Criteria) this;
        }

        public Criteria andOutIsFlagIn(List<Byte> values) {
            addCriterion("OUT_IS_FLAG in", values, "outIsFlag");
            return (Criteria) this;
        }

        public Criteria andOutIsFlagNotIn(List<Byte> values) {
            addCriterion("OUT_IS_FLAG not in", values, "outIsFlag");
            return (Criteria) this;
        }

        public Criteria andOutIsFlagBetween(Byte value1, Byte value2) {
            addCriterion("OUT_IS_FLAG between", value1, value2, "outIsFlag");
            return (Criteria) this;
        }

        public Criteria andOutIsFlagNotBetween(Byte value1, Byte value2) {
            addCriterion("OUT_IS_FLAG not between", value1, value2, "outIsFlag");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeIsNull() {
            addCriterion("UPDATE_DATA_TIME is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeIsNotNull() {
            addCriterion("UPDATE_DATA_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeEqualTo(Date value) {
            addCriterion("UPDATE_DATA_TIME =", value, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeNotEqualTo(Date value) {
            addCriterion("UPDATE_DATA_TIME <>", value, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeGreaterThan(Date value) {
            addCriterion("UPDATE_DATA_TIME >", value, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("UPDATE_DATA_TIME >=", value, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeLessThan(Date value) {
            addCriterion("UPDATE_DATA_TIME <", value, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeLessThanOrEqualTo(Date value) {
            addCriterion("UPDATE_DATA_TIME <=", value, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeIn(List<Date> values) {
            addCriterion("UPDATE_DATA_TIME in", values, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeNotIn(List<Date> values) {
            addCriterion("UPDATE_DATA_TIME not in", values, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeBetween(Date value1, Date value2) {
            addCriterion("UPDATE_DATA_TIME between", value1, value2, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andUpdateDataTimeNotBetween(Date value1, Date value2) {
            addCriterion("UPDATE_DATA_TIME not between", value1, value2, "updateDataTime");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountIsNull() {
            addCriterion("REAL_PAY_AMOUNT is null");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountIsNotNull() {
            addCriterion("REAL_PAY_AMOUNT is not null");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountEqualTo(BigDecimal value) {
            addCriterion("REAL_PAY_AMOUNT =", value, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountNotEqualTo(BigDecimal value) {
            addCriterion("REAL_PAY_AMOUNT <>", value, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountGreaterThan(BigDecimal value) {
            addCriterion("REAL_PAY_AMOUNT >", value, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("REAL_PAY_AMOUNT >=", value, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountLessThan(BigDecimal value) {
            addCriterion("REAL_PAY_AMOUNT <", value, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("REAL_PAY_AMOUNT <=", value, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountIn(List<BigDecimal> values) {
            addCriterion("REAL_PAY_AMOUNT in", values, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountNotIn(List<BigDecimal> values) {
            addCriterion("REAL_PAY_AMOUNT not in", values, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("REAL_PAY_AMOUNT between", value1, value2, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealPayAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("REAL_PAY_AMOUNT not between", value1, value2, "realPayAmount");
            return (Criteria) this;
        }

        public Criteria andRealReturnAmountIsNull() {
            addCriterion("REAL_RETURN_AMOUNT is null");
            return (Criteria) this;
        }

        public Criteria andRealReturnAmountIsNotNull() {
            addCriterion("REAL_RETURN_AMOUNT is not null");
            return (Criteria) this;
        }

        public Criteria andRealReturnAmountEqualTo(BigDecimal value) {
            addCriterion("REAL_RETURN_AMOUNT =", value, "realReturnAmount");
            return (Criteria) this;
        }

        public Criteria andRealReturnAmountNotEqualTo(BigDecimal value) {
            addCriterion("REAL_RETURN_AMOUNT <>", value, "realReturnAmount");
            return (Criteria) this;
        }

        public Criteria andRealReturnAmountGreaterThan(BigDecimal value) {
            addCriterion("REAL_RETURN_AMOUNT >", value, "realReturnAmount");
            return (Criteria) this;
        }

        public Criteria andRealReturnAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("REAL_RETURN_AMOUNT >=", value, "realReturnAmount");
            return (Criteria) this;
        }

        public Criteria andRealReturnAmountLessThan(BigDecimal value) {
            addCriterion("REAL_RETURN_AMOUNT <", value, "realReturnAmount");
            return (Criteria) this;
        }

        public Criteria andRealReturnAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("REAL_RETURN_AMOUNT <=", value, "realReturnAmount");
            return (Criteria) this;
        }

        public Criteria andRealReturnAmountIn(List<BigDecimal> values) {
            addCriterion("REAL_RETURN_AMOUNT in", values, "realReturnAmount");
            return (Criteria) this;
        }

        public Criteria andRealReturnAmountNotIn(List<BigDecimal> values) {
            addCriterion("REAL_RETURN_AMOUNT not in", values, "realReturnAmount");
            return (Criteria) this;
        }

        public Criteria andRealReturnAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("REAL_RETURN_AMOUNT between", value1, value2, "realReturnAmount");
            return (Criteria) this;
        }

        public Criteria andRealReturnAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("REAL_RETURN_AMOUNT not between", value1, value2, "realReturnAmount");
            return (Criteria) this;
        }

        public Criteria andRealTotalAmountIsNull() {
            addCriterion("REAL_TOTAL_AMOUNT is null");
            return (Criteria) this;
        }

        public Criteria andRealTotalAmountIsNotNull() {
            addCriterion("REAL_TOTAL_AMOUNT is not null");
            return (Criteria) this;
        }

        public Criteria andRealTotalAmountEqualTo(BigDecimal value) {
            addCriterion("REAL_TOTAL_AMOUNT =", value, "realTotalAmount");
            return (Criteria) this;
        }

        public Criteria andRealTotalAmountNotEqualTo(BigDecimal value) {
            addCriterion("REAL_TOTAL_AMOUNT <>", value, "realTotalAmount");
            return (Criteria) this;
        }

        public Criteria andRealTotalAmountGreaterThan(BigDecimal value) {
            addCriterion("REAL_TOTAL_AMOUNT >", value, "realTotalAmount");
            return (Criteria) this;
        }

        public Criteria andRealTotalAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("REAL_TOTAL_AMOUNT >=", value, "realTotalAmount");
            return (Criteria) this;
        }

        public Criteria andRealTotalAmountLessThan(BigDecimal value) {
            addCriterion("REAL_TOTAL_AMOUNT <", value, "realTotalAmount");
            return (Criteria) this;
        }

        public Criteria andRealTotalAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("REAL_TOTAL_AMOUNT <=", value, "realTotalAmount");
            return (Criteria) this;
        }

        public Criteria andRealTotalAmountIn(List<BigDecimal> values) {
            addCriterion("REAL_TOTAL_AMOUNT in", values, "realTotalAmount");
            return (Criteria) this;
        }

        public Criteria andRealTotalAmountNotIn(List<BigDecimal> values) {
            addCriterion("REAL_TOTAL_AMOUNT not in", values, "realTotalAmount");
            return (Criteria) this;
        }

        public Criteria andRealTotalAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("REAL_TOTAL_AMOUNT between", value1, value2, "realTotalAmount");
            return (Criteria) this;
        }

        public Criteria andRealTotalAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("REAL_TOTAL_AMOUNT not between", value1, value2, "realTotalAmount");
            return (Criteria) this;
        }

        public Criteria andSendToPcIsNull() {
            addCriterion("SEND_TO_PC is null");
            return (Criteria) this;
        }

        public Criteria andSendToPcIsNotNull() {
            addCriterion("SEND_TO_PC is not null");
            return (Criteria) this;
        }

        public Criteria andSendToPcEqualTo(Integer value) {
            addCriterion("SEND_TO_PC =", value, "sendToPc");
            return (Criteria) this;
        }

        public Criteria andSendToPcNotEqualTo(Integer value) {
            addCriterion("SEND_TO_PC <>", value, "sendToPc");
            return (Criteria) this;
        }

        public Criteria andSendToPcGreaterThan(Integer value) {
            addCriterion("SEND_TO_PC >", value, "sendToPc");
            return (Criteria) this;
        }

        public Criteria andSendToPcGreaterThanOrEqualTo(Integer value) {
            addCriterion("SEND_TO_PC >=", value, "sendToPc");
            return (Criteria) this;
        }

        public Criteria andSendToPcLessThan(Integer value) {
            addCriterion("SEND_TO_PC <", value, "sendToPc");
            return (Criteria) this;
        }

        public Criteria andSendToPcLessThanOrEqualTo(Integer value) {
            addCriterion("SEND_TO_PC <=", value, "sendToPc");
            return (Criteria) this;
        }

        public Criteria andSendToPcIn(List<Integer> values) {
            addCriterion("SEND_TO_PC in", values, "sendToPc");
            return (Criteria) this;
        }

        public Criteria andSendToPcNotIn(List<Integer> values) {
            addCriterion("SEND_TO_PC not in", values, "sendToPc");
            return (Criteria) this;
        }

        public Criteria andSendToPcBetween(Integer value1, Integer value2) {
            addCriterion("SEND_TO_PC between", value1, value2, "sendToPc");
            return (Criteria) this;
        }

        public Criteria andSendToPcNotBetween(Integer value1, Integer value2) {
            addCriterion("SEND_TO_PC not between", value1, value2, "sendToPc");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_SALEORDER
     *
     * @mbggenerated do_not_delete_during_merge Mon Jun 08 19:16:46 CST 2020
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_SALEORDER
     *
     * @mbggenerated Mon Jun 08 19:16:46 CST 2020
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}