package com.vedeng.goods.manager.validator.enums;

public enum GoodsInvalidatorErrorEnum {

    SkuStockInvalidatorError(2001,"库存不为0，不可禁用"),
    SkuOccupyInvalidatorError(2002,"有单据占用，不可禁用"),
    SkuBanInvalidatorError(2003,"未在全部平台下架，不可禁用"),
    SkuNoRelatedOrderForConditionFourInvalidatorError(2004,"有单据orderNo未全部收货，不可禁用"),
    SkuNoRelatedOrderForConditionFiveInvalidatorError(2005,"有单据orderNo未完成，不可禁用"),
    SkuNoRelatedValidLendOutInvalidatorError(2006,"有审核中的外借单orderNo，不可禁用"),
    SkuNoRelatedUnbackLendOutInvalidatorError(2007,"有审核通过，单归还状态为部分归还，未归还的外借单orderNo，不可禁用"),
    SkuNoRelatedInorderInvalidatorError(2008,"该SKU有审核状态为待审核，审核中，审核通过，且入库状态为未入库，部分入库的盘盈入库单orderNo"),
    SkuNoNotValidInvalidatorError(2009,"该SKU处于审核中，不可禁用"),
    SkuExistSpuInvalidatorError(2010,"该SKU没有SPU，不可禁用"),
    SkuUsingInvalidatorError(2010,"该SKU的启用状态不是已启用，不可禁用"),
    SkuUserInvalidatorError(2011,"只允许归属产品经理和归属产品助理禁用SKU，烦请确认"),
    ReasonNumNullInvalidatorError(2012,"请填写禁用原因"),
    ReasonNumLongInvalidatorError(2013,"禁用原因不可超过64字符"),
    SpuStockInvalidatorError(3001,"skus库存不为0，不可禁用"),
    SpuOccupyInvalidatorError(3002,"有单据占用skus，不可禁用"),
    SpuBanInvalidatorError(3003,"skus未在全部平台下架，不可禁用"),
    SpuNoRelatedOrderForConditionFourInvalidatorError(3004,"skus有采购单或销售售后退货单orderNo未全部收货，不可禁用"),
    SpuNoRelatedOrderForConditionFiveInvalidatorError(2005,"skus有采购售后退货单，采购售后换货单，或销售售后换货单orderNo未完成，不可禁用"),
    SpuNoRelatedValidLendOutInvalidatorError(3006,"skus有审核中的外借单orderNo，不可禁用"),
    SpuNoRelatedUnbackLendOutInvalidatorError(3007,"skus有审核通过，单归还状态为部分归还，未归还的外借单orderNo"),
    SpuNoRelatedInorderInvalidatorError(3008,"skus有审核状态为待审核，审核中，审核通过，且入库状态为未入库，部分入库的盘盈入库单orderNo，不可禁用"),
    SpuNoNotValidInvalidatorError(3009,"该SPU或所属的SKU中有处于审核中SPU或SKU"),
    SpuUsingInvalidatorError(3010,"SPU的启用状态没有处于已启用状态，无法禁用"),
    SpuUserInvalidatorError(3011,"只允许归属产品经理和归属产品助理禁用SPU，烦请确认"),


    ;
    private Integer code;

    private String message;

    GoodsInvalidatorErrorEnum(Integer code,String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
