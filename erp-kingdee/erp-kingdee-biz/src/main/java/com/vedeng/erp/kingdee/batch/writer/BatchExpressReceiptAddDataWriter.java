package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeExpressReceiptDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeExpressReceiptCommandConvertor;
import com.vedeng.erp.kingdee.service.KingDeeExpressReceiptApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 快递签收新增数据
 * @date 2023/04/14 16:00
 */
@Service
@Slf4j
public class BatchExpressReceiptAddDataWriter extends BaseWriter<KingDeeExpressReceiptDto> {

    @Autowired
    private KingDeeExpressReceiptCommandConvertor commandConvertor;
    @Autowired
    private KingDeeExpressReceiptApiService kingDeeExpressReceiptApiService;

    @Override
    public void doWrite(KingDeeExpressReceiptDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("BatchExpressReceiptAddDataWriter doWrite dto:{},调用金蝶接口入参为:{}", JSON.toJSONString(dto), JSON.toJSONString(commandConvertor.toCommand(dto)));

        dto.setKingDeeBizEnums(KingDeeBizEnums.saveExpressReceipt);
        kingDeeExpressReceiptApiService.register(dto,true);
    }


}
