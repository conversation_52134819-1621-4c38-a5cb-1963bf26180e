package com.vedeng.erp.trader.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 通话场景详细字段表
 */
@Getter
@Setter
public class VoiceFieldResultEntity extends BaseEntity {
    /**
     * 自增主键
     */
    private Integer fieldResultId;

    /**
     * 沟通记录id
     */
    private Integer communicateRecordId;

    /**
     * 场景编码
     */
    private String senceCode;

    /**
     * 分组编码
     */
    private String groupCode;

    /**
     * 字段英文名
     */
    private String fieldCode;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * gpt解析结果
     */
    private String fieldResult;

    /**
     * 处理结果（Y是N否）
     */
    private String doFlag;

    /**
     * 序号
     */
    private Integer fieldOrder;

    /**
     * 添加人
     */
    private Integer addUserId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private Integer updateUserId;
}