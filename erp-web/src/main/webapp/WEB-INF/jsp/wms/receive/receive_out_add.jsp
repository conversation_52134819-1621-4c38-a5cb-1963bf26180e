<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="新增领用单" scope="application"/>
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet" href="<%= basePath %>static/css/select2.css?rnd=${resourceVersionKey}"/>
<script type="text/javascript" src='<%= basePath %>static/js/select2.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript"
        src='<%= basePath %>static/js/select2_locale_zh-CN.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript">

    function deleteRow(row) {
        $(row).parent().parent().remove();
    }

    function addSubmit() {
        checkLogin();
        $("#no_reveice_reason").hide()
        $("#too_long_reveice_reason").hide()
        $("#no_receive_user").hide()
        $("#no_logistic_comment").hide()
        $("#too_long_logistic_comment").hide()
        $("#no_sku").hide()

        if ($("#receiveOutReason").val() == '') {
            layer.alert("请填写领用原因！");
            $("#no_reveice_reason").show()
            return;
        }
        if ($("#receiveOutReason").val().length > 500) {
            layer.alert("领用原因不允许超过500个字！");
            $("#too_long_reveice_reason").show()
            return;
        }
        if ($("#receiveOutUserId").val() == '') {
            layer.alert("请选择领用人！");
            $("#no_receive_user").show()
            return;
        }

        if ($("#logisticComment").val() == '') {
            layer.alert("请填写物流备注！");
            $("#no_logistic_comment").show()
            return false;
        }

        if ($("#logisticComment").val().length > 500) {
            layer.alert("物流备注不允许超过500个字！");
            $("#too_long_logistic_comment").show()
            return false;
        }


        var trs = $('#thisTimeUpdateTbody tr');

        if (trs.length == 0) {
            layer.alert("请选择领用产品！");
            $("#no_sku").show()
            return;
        }

        var skuNos = [];
        var re = /^[0-9]+$/;
        for (var i = 0; i < trs.length; i++) {
            var skuNo = $(trs[i]).find('td').eq(0).find('input').eq(0).val();
            var receiveOutNum = $(trs[i]).find('td').eq(4).find('input').eq(0).val();
            var stockNum = $(trs[i]).find('td').eq(5).find('input').eq(0).val();

            if (skuNos.includes(skuNo)) {
                layer.alert("sku:'" + skuNo + "'已添加，不可以添加重复数据!");
                return;
            }

            if (receiveOutNum == '') {
                layer.alert("请填写数量！");
                return;
            }
            if (receiveOutNum == 0) {
                layer.alert("数量不能为0！");
                return;
            }
            if (!re.test(receiveOutNum)) {
                layer.alert("数量必须为正整数！");
                return false;
            }

            var receiveNum = parseInt(receiveOutNum);
            var stock = parseInt(stockNum);

            if (receiveNum > stock) {
                layer.alert("数量不允许超过不合格库存量！");
                return;
            }

            skuNos.push(skuNo);
        }
        $("#addForm").submit();
        // $.ajax({
        //     async: false,
        //     url: page_url + '/wms/receiveOut/saveReceiveOutOrder.do',
        //     type: "POST",
        //     dataType:"json",
        //     data: $("#addForm").serialize(),
        //     success: function (res) {
        //         if(res.code == 0){
        //             layer.alert("保存成功");
        //             window.parent.location.reload()
        //         }else {
        //             layer.alert("保存失败: " + res.message);
        //         }
        //     },
        //     error: function (data){
        //         if(data.status ==1001){
        //             layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
        //         }
        //     }
        // })

        // $.ajax({
        //     async: true,
        //     url: page_url + '/wms/commodityLendOut/canSubmitLendOut.do',
        //     type: "POST",
        //     dataType: "json",
        //     success: function (data) {
        //         if (data.code == 0) {
        //             $("#addForm").submit();
        //         } else {
        //             layer.alert(data.message)
        //         }
        //
        //     },
        //     error: function (data) {
        //         if (data.status == 1001) {
        //             layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
        //         }
        //     }
        // });


    }

    function setGoodData(sku, goodsName, brandName, model, unitName, stockNum) {
        var tbody = $("#thisTimeUpdateTbody");
        var tr = $("<tr>\n" +
            "     <td>\n" +
            "     " + sku + "\n" +
            "       <input type=\"hidden\" name=\"skuNo\" value=\"" + sku + "\">\n" +
            "     </td>\n" +
            "     <td>" + goodsName + "</td>\n" +
            "     <td>" + brandName + "</td>\n" +
            "     <td>" + model + "</td>\n" +
            "     <td><input type=\"number\" class=\"input-middle\" style=\"width:100%;\" name=\"receiveOutNum\"></td>\n" +
            "     <td hidden=\"hidden\"><input type=\"hidden\" name=\"stockNum\" value=\"" + stockNum + "\"></td>\n" +
            "     <td><a href=\"#\" onclick=\"deleteRow(this)\">删除</a></td>\n" +
            "     </tr>");
        tbody.append(tr);
    }



    function getReceiveUserOrg() {
        checkLogin()
        $.ajax({
            async: true,
            url: page_url + '/wms/receiveOut/getReceiveUserInfo.do',
            type: "POST",
            data: {
                "userId": $("#receiveOutUserId").val()
            },
            dataType: "json",
            success: function (data) {
                if (data.code == 0) {
                    var userInfo = data.data;
                    if (typeof(userInfo["orgName"]) != "undefined"){
                        $("#receiveOutUserOrg").text(userInfo["orgName"])
                    }else{
                        $("#receiveOutUserOrg").text("")
                    }
                } else {
                    $("#receiveOutUserOrg").text("")
                }
            },
            error: function (data) {
                $("#receiveOutUserOrg").text("")
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }

    $(function () {
        $('#receiveOutUserId').select2();
    })

</script>

<div class="content">
    <div class="formtitle"></div>
    <div id="desc_div">
        <form method="post" id="addForm"
              action="${pageContext.request.contextPath}/wms/receiveOut/saveReceiveOutOrder.do">
            <input type="hidden" id="callbackFunction" name="callbackFunction" value="${callbackFunction}"/>
            <input type="hidden" name="logicalId" value="1710"/>
            <ul class="payplan">
                <div>
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">领用信息</div>
                    </div>

                    <li>
                        <div class="infor_name">
                            <span class="font-red">*</span><label>领用原因:</label>
                        </div>
                        <div class="f_left">
                            <input type="text" placeholder="请填写领用原因" class="input-middle" id="receiveOutReason"
                                   name="receiveOutReason" value=""/>
                        </div>
                    </li>
                    <li id="no_reveice_reason" hidden="hidden">
                        <labe><span class="font-red">未填写领用原因：请填写领用原因！</span></labe>
                    </li>
                    <li id="too_long_reveice_reason" hidden="hidden">
                        <label><span class="font-red">长度超过限制：领用原因不允许超过500个字！</span></label>
                    </li>
                    <li>
                        <div class="infor_name">
                            <span class="font-red">*</span><label>领用人:</label>
                        </div>
                        <div class="f_left">
                            <select class="input-middle f_left J-select" name="receiveOutUserId" id="receiveOutUserId"
                                    onchange="getReceiveUserOrg();">
                                <option selected="selected" value="">请选择</option>
                                <c:forEach items="${allUser}" var="posit">
                                    <option value="${posit.userId }">${posit.username}</option>
                                </c:forEach>
                            </select>
                        </div>
                        <div class="f_left">
                            &nbsp;<label id="receiveOutUserOrg" name="receiveOutUserOrg"></label>
                        </div>
                    </li>
                    <li id="no_receive_user" hidden="hidden">
                        <label><span class="font-red">未选择领用人：请选择领用人</span></label>
                    </li>
                    <li>
                        <div class="infor_name">
                            <span class="font-red">*</span><label>物流备注:</label>
                        </div>
                        <div class="f_left">
                            <input type="text" placeholder="请填写物流备注" class="input-middle" id="logisticComment"
                                   name="logisticComment" value=""/>
                        </div>
                    </li>
                    <li id="no_logistic_comment" hidden="hidden">
                        <label><span class="font-red">未填写物流备注：请填写物流备注！</span></label>
                    </li>
                    <li id="too_long_logistic_comment" hidden="hidden">
                        <label><span class="font-red">长度超过限制：物流备注不允许超过500个字！</span></label>
                    </li>
                </div>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">产品信息</div>
                            <div class="title-click nobor  pop-new-data"
                                 layerParams='{"width":"700px","height":"480px","title":"添加产品","link":"<%=path%>/wms/receiveOut/addReceiveOutGoods.do?callbackFunction=setGoodData"}'>
                                新增
                            </div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:80px">订货号</th>
                                <th style="width:80px">产品名称</th>
                                <th style="width:80px">品牌</th>
                                <th style="width:80px">型号</th>
                                <th style="width:80px">数量</th>
                                <th style="width:80px" hidden="hidden">库存数量</th>
                                <th style="width:80px">操作</th>
                            </tr>
                            </thead>
                            <tbody id="thisTimeUpdateTbody">
                            </tbody>
                        </table>
                    </div>
                </li>
                <li id="no_sku" hidden="hidden">
                    <label><span class="font-red">未选择产品：请选择领用产品！</span></label>
                </li>
            </ul>
            <div>
                提示:</br>
                仅不合格库商品可领用，其他逻辑仓均不可领用</br>
            </div>
            <div class="add-tijiao tcenter mt10">
                <button type="button" class="bt-bg-style bg-deep-green" onclick="addSubmit();">提交并审核</button>
            </div>
        </form>
    </div>
</div>
<%@ include file="../../common/footer.jsp" %>