package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.flash.dto.EarlyWarningTaskDto;
import com.vedeng.flash.service.prepare.PrepareStockService;
import com.vedeng.flash.service.warningtask.PrepareStockTaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.thymeleaf.util.MapUtils;

import java.util.List;
import java.util.Map;

/**
 * @Description:  备货计划预警
 * @Author:       davis
 * @Date:         2021/5/25 上午10:12
 * @Version:      1.0
 */
@Slf4j
@Component
@JobHandler(value = "prepareStockWarnTask")
public class PrepareStockWarnTask extends AbstractJobHandler {

    @Autowired
    private PrepareStockService prepareStockService;

    @Autowired
    private PrepareStockTaskService prepareStockTaskService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        log.info("备货计划预警,时间:{}",param);
        XxlJobLogger.log("备货计划预警,时间:{}",param);

        Map<String, Object> stockDtoMap = prepareStockService.prepareStockWarn();

        if (MapUtils.isEmpty(stockDtoMap)) {
            log.info("暂无可预警的备货计划");
            XxlJobLogger.log("暂无可预警的备货计划");
            return SUCCESS;
        }

        List<EarlyWarningTaskDto> insertEarlyWarningTaskList = (List<EarlyWarningTaskDto>) stockDtoMap.get("insertEarlyWarningTaskList");

        if (!CollectionUtils.isEmpty(insertEarlyWarningTaskList)) {
            prepareStockTaskService.createBatch(insertEarlyWarningTaskList);
        }

        List<EarlyWarningTaskDto> updateEarlyWarningTaskList = (List<EarlyWarningTaskDto>) stockDtoMap.get("updateEarlyWarningTaskList");
        if (!CollectionUtils.isEmpty(updateEarlyWarningTaskList)) {
            prepareStockTaskService.updateBatch(updateEarlyWarningTaskList);
        }
        log.info("备货计划预警,时间:{}",param);
        XxlJobLogger.log("备货计划预警,时间:{}",param);
        return SUCCESS;
    }
}



