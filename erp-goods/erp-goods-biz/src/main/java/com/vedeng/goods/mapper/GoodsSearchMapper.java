package com.vedeng.goods.mapper;


import com.vedeng.goods.domain.dto.GoodsSearchBaseInfoDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 商品搜索Mapper
 *
 * <AUTHOR>
 */
@Repository("goodsSearchMapper")
public interface GoodsSearchMapper {

    /**
     * sku获取商品搜索基础信息
     *
     * @param skuIds
     * @return
     */
    List<GoodsSearchBaseInfoDto> getGoodsSearchBaseInfoBySkuIds(@Param("skuIds") List<Integer> skuIds);

    /**
     * 初始化商品搜索页面
     *
     * @param paraMap
     * @return
     */
    List<GoodsSearchBaseInfoDto> defaultGoodsSearchBaseInfoListPage(Map<String, Object> paraMap);

    /**
     * 初始化页面展示商品ID
     *
     * @param paraMap
     * @return
     */
    List<Integer> defaultSkuIdsSearchListPage(Map<String, Object> paraMap);
}
