package com.vedeng.temporal.domain.dto;

import lombok.Builder;
import lombok.Data;

/**
 * 轮询完成条件配置
 * 
 * 用于定义轮询查询的完成判断条件，支持多种操作符和期望值比较
 * 
 * 支持的操作符：
 * - NOT_BLANK: 字符串不为空且不为空白
 * - NOT_NULL: 对象不为null
 * - EQUALS: 等于期望值
 * - NOT_EQUALS: 不等于期望值
 * - GREATER_THAN: 大于期望值（数值比较）
 * - LESS_THAN: 小于期望值（数值比较）
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-22
 */
@Data
@Builder
public class CompletionCondition {
    
    /**
     * 字段名称
     * 对应查询结果Map中的key
     */
    private String fieldName;
    
    /**
     * 操作符
     * 支持：NOT_BLANK, NOT_NULL, EQUALS, NOT_EQUALS, GREATER_THAN, LESS_THAN
     */
    private String operator;
    
    /**
     * 期望值（可选）
     * 当操作符为EQUALS、NOT_EQUALS、GREATER_THAN、LESS_THAN时需要提供
     */
    private Object expectedValue;
    
    /**
     * 无参构造函数（Jackson序列化需要）
     */
    public CompletionCondition() {
    }
    
    /**
     * 全参构造函数
     */
    public CompletionCondition(String fieldName, String operator, Object expectedValue) {
        this.fieldName = fieldName;
        this.operator = operator;
        this.expectedValue = expectedValue;
    }
    
    /**
     * 便捷构造函数（不需要期望值的操作符）
     */
    public CompletionCondition(String fieldName, String operator) {
        this.fieldName = fieldName;
        this.operator = operator;
        this.expectedValue = null;
    }
    
    /**
     * 操作符常量
     */
    public static class Operators {
        public static final String NOT_BLANK = "NOT_BLANK";
        public static final String NOT_NULL = "NOT_NULL";
        public static final String EQUALS = "EQUALS";
        public static final String NOT_EQUALS = "NOT_EQUALS";
        public static final String GREATER_THAN = "GREATER_THAN";
        public static final String LESS_THAN = "LESS_THAN";
    }
}