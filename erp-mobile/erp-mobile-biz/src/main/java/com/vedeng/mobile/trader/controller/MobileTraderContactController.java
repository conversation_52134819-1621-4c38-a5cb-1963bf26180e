package com.vedeng.mobile.trader.controller;

import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.dto.TraderContactDto;
import com.vedeng.erp.trader.service.TraderContactApiService;
import com.vedeng.erp.trader.service.TraderContactService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/4 15:15
 **/
@Slf4j
@RestController
@RequestMapping("/mobile/traderContact")
public class MobileTraderContactController {

    @Autowired
    private TraderContactApiService traderContactApiService;
    @Autowired
    TraderContactService traderContactService;


    /**
     * 分页查询
     *
     * @param recordDtoPageParam 筛选条件
     * @return 查询结果
     */
    @PostMapping(value = "/page")
    @ExcludeAuthorization
    public R<?> traderContactPage(@RequestBody PageParam<TraderContactDto> recordDtoPageParam) {
        return R.success(traderContactService.page(recordDtoPageParam));
    }


    /**
     * 保存客户联系人
     *
     * @param traderContactErpDto 数据
     */
    @PostMapping("/add")
    @ExcludeAuthorization
    public R<?> add(@RequestBody TraderContactDto traderContactErpDto) {
        traderContactApiService.add(traderContactErpDto);
        return R.success();
    }

    /**
     * 更新客户联系人
     */
    @PostMapping("/update")
    @ExcludeAuthorization
    public R<?> update(@RequestBody TraderContactDto traderContactErpDto) {
        traderContactApiService.edit(traderContactErpDto);
        return R.success();
    }

    @PostMapping("/enable")
    @ExcludeAuthorization
    public R<?> enable(@RequestBody TraderContactDto traderContactErpDto) {
        traderContactApiService.doEnable(traderContactErpDto);
        return R.success();
    }


    /**
     * 设为默认联系人
     * @param traderId traderId
     * @param traderContactId traderContactId
     */
    @PostMapping("/setDefault")
    @ExcludeAuthorization
    public R<?> setDefault(@RequestParam("traderId") Integer traderId, @RequestParam("traderContactId") Integer traderContactId) {
        traderContactApiService.setDefault(traderId, traderContactId);
        return R.success();
    }

    /**
     * 置顶
     * @param traderId traderId
     * @param traderContactId traderContactId
     */
    @PostMapping("/top")
    @ExcludeAuthorization
    public R<?> top(@RequestParam("traderId") Integer traderId, @RequestParam("traderContactId") Integer traderContactId, @RequestParam("top") Integer top) {
        traderContactApiService.top(traderId, traderContactId, top);
        return R.success();
    }

    /**
     * 客户职位枚举数据
     */
    @PostMapping("/position")
    @ExcludeAuthorization
    public R<?> getPosition(@RequestParam("traderId") Integer traderId) {
        return R.success(traderContactApiService.getPosition(traderId));
    }


}
