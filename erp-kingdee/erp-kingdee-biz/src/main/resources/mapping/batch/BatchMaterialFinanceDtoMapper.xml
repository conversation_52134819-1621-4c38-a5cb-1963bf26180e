<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchMaterialFinanceDtoMapper">
    <update id="updateKingDeePushStatus">
        UPDATE T_GOODS_FINANCE
        SET IS_PUSH = 1, PUSH_TIME = NOW(), MOD_TIME = MOD_TIME
        WHERE GOODS_FINANCE_ID = #{goodsFinanceId,jdbcType=BIGINT}
    </update>

    <select id="findByAll" resultType="com.vedeng.erp.kingdee.batch.dto.BatchMaterialFinanceDto">
        SELECT
        tgf.GOODS_FINANCE_ID,
        tgf.SKU_NO ,
        vcs.SKU_NAME ,
        CASE
        tgf.IS_MEDICAL_EQUIPMENT
        WHEN 1 THEN '是'
        WHEN 0 THEN '否'
        ELSE ''
        END isMedicalEquipmentText,
        tgf.MEDICAL_EQUIPMENT_TYPE ,
        tgf.MEDICAL_EQUIPMENT_USE ,
        tgf.MEDICAL_EQUIPMENT_LINE ,
        spu.SPU_TYPE,
        tu.UNIT_KING_DEE_NO,
        tgf.IS_INSTALLATION
        FROM
        T_GOODS_FINANCE tgf
        LEFT JOIN V_CORE_SKU vcs ON
        tgf.SKU_ID = vcs.SKU_ID
        LEFT JOIN V_CORE_SPU spu ON
        vcs.SPU_ID = spu.SPU_ID
        LEFT JOIN T_UNIT tu ON
        vcs.BASE_UNIT_ID = tu.UNIT_ID
        WHERE
        tgf.IS_PUSH = 0
        AND tgf.IS_DELETE = 0
        AND vcs.STATUS = 1
        and vcs.CHECK_STATUS = 3
        <if test="beginTime != null">
            AND tgf.MOD_TIME <![CDATA[>=]]> #{beginTime,jdbcType=DATE}
        </if>
        <if test="endTime != null">
            AND tgf.MOD_TIME <![CDATA[<=]]> #{endTime,jdbcType=DATE}
        </if>
    </select>
</mapper>