package com.vedeng.dwh.model.generate;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import lombok.Data;

/**
 * DWM_NJ_USER_ORG_DF
 * <AUTHOR>
@Data
public class DwmNjUserOrgDfDo implements Serializable {
    private Long vdOdsPk;

    private Timestamp startTime;

    private Timestamp endTime;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 部门ID
     */
    private Integer orgId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 归属公司
     */
    private Integer companyId;

    /**
     * 职责ID
     */
    private Integer positionId;

    /**
     * 职责名称
     */
    private String positionName;

    /**
     * 职责类型
     */
    private Integer positionType;

    /**
     * 职责等级
     */
    private Integer positionLevel;

    /**
     * 是否管理员
     */
    private Integer isAdmin;

    /**
     * 是否禁用
     */
    private Integer isDisabled;

    /**
     * 工号
     */
    private Integer number;

    /**
     * 系统
     */
    private String system;

    /**
     * 直接商机ID
     */
    private Integer parentId;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 一级部门ID
     */
    private Integer l1Id;

    /**
     * 一级部门名称
     */
    private String l1Name;

    /**
     * 二级部门ID
     */
    private Integer l2Id;

    /**
     * 二级部门名称
     */
    private String l2Name;

    /**
     * 三级部门ID
     */
    private Integer l3Id;

    /**
     * 三级部门名称
     */
    private String l3Name;

    private static final long serialVersionUID = 1L;
}