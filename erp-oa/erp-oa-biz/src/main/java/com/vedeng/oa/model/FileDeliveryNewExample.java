package com.vedeng.oa.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FileDeliveryNewExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public FileDeliveryNewExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andFileDeliveryIdIsNull() {
            addCriterion("FILE_DELIVERY_ID is null");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdIsNotNull() {
            addCriterion("FILE_DELIVERY_ID is not null");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdEqualTo(Integer value) {
            addCriterion("FILE_DELIVERY_ID =", value, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdNotEqualTo(Integer value) {
            addCriterion("FILE_DELIVERY_ID <>", value, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdGreaterThan(Integer value) {
            addCriterion("FILE_DELIVERY_ID >", value, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("FILE_DELIVERY_ID >=", value, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdLessThan(Integer value) {
            addCriterion("FILE_DELIVERY_ID <", value, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdLessThanOrEqualTo(Integer value) {
            addCriterion("FILE_DELIVERY_ID <=", value, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdIn(List<Integer> values) {
            addCriterion("FILE_DELIVERY_ID in", values, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdNotIn(List<Integer> values) {
            addCriterion("FILE_DELIVERY_ID not in", values, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdBetween(Integer value1, Integer value2) {
            addCriterion("FILE_DELIVERY_ID between", value1, value2, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("FILE_DELIVERY_ID not between", value1, value2, "fileDeliveryId");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryNoIsNull() {
            addCriterion("FILE_DELIVERY_NO is null");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryNoIsNotNull() {
            addCriterion("FILE_DELIVERY_NO is not null");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryNoEqualTo(String value) {
            addCriterion("FILE_DELIVERY_NO =", value, "fileDeliveryNo");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryNoNotEqualTo(String value) {
            addCriterion("FILE_DELIVERY_NO <>", value, "fileDeliveryNo");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryNoGreaterThan(String value) {
            addCriterion("FILE_DELIVERY_NO >", value, "fileDeliveryNo");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryNoGreaterThanOrEqualTo(String value) {
            addCriterion("FILE_DELIVERY_NO >=", value, "fileDeliveryNo");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryNoLessThan(String value) {
            addCriterion("FILE_DELIVERY_NO <", value, "fileDeliveryNo");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryNoLessThanOrEqualTo(String value) {
            addCriterion("FILE_DELIVERY_NO <=", value, "fileDeliveryNo");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryNoLike(String value) {
            addCriterion("FILE_DELIVERY_NO like", value, "fileDeliveryNo");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryNoNotLike(String value) {
            addCriterion("FILE_DELIVERY_NO not like", value, "fileDeliveryNo");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryNoIn(List<String> values) {
            addCriterion("FILE_DELIVERY_NO in", values, "fileDeliveryNo");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryNoNotIn(List<String> values) {
            addCriterion("FILE_DELIVERY_NO not in", values, "fileDeliveryNo");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryNoBetween(String value1, String value2) {
            addCriterion("FILE_DELIVERY_NO between", value1, value2, "fileDeliveryNo");
            return (Criteria) this;
        }

        public Criteria andFileDeliveryNoNotBetween(String value1, String value2) {
            addCriterion("FILE_DELIVERY_NO not between", value1, value2, "fileDeliveryNo");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdIsNull() {
            addCriterion("APPLY_USER_ID is null");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdIsNotNull() {
            addCriterion("APPLY_USER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdEqualTo(Integer value) {
            addCriterion("APPLY_USER_ID =", value, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdNotEqualTo(Integer value) {
            addCriterion("APPLY_USER_ID <>", value, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdGreaterThan(Integer value) {
            addCriterion("APPLY_USER_ID >", value, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("APPLY_USER_ID >=", value, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdLessThan(Integer value) {
            addCriterion("APPLY_USER_ID <", value, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("APPLY_USER_ID <=", value, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdIn(List<Integer> values) {
            addCriterion("APPLY_USER_ID in", values, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdNotIn(List<Integer> values) {
            addCriterion("APPLY_USER_ID not in", values, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdBetween(Integer value1, Integer value2) {
            addCriterion("APPLY_USER_ID between", value1, value2, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("APPLY_USER_ID not between", value1, value2, "applyUserId");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNameIsNull() {
            addCriterion("APPLY_ORG_NAME is null");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNameIsNotNull() {
            addCriterion("APPLY_ORG_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNameEqualTo(String value) {
            addCriterion("APPLY_ORG_NAME =", value, "applyOrgName");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNameNotEqualTo(String value) {
            addCriterion("APPLY_ORG_NAME <>", value, "applyOrgName");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNameGreaterThan(String value) {
            addCriterion("APPLY_ORG_NAME >", value, "applyOrgName");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("APPLY_ORG_NAME >=", value, "applyOrgName");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNameLessThan(String value) {
            addCriterion("APPLY_ORG_NAME <", value, "applyOrgName");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNameLessThanOrEqualTo(String value) {
            addCriterion("APPLY_ORG_NAME <=", value, "applyOrgName");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNameLike(String value) {
            addCriterion("APPLY_ORG_NAME like", value, "applyOrgName");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNameNotLike(String value) {
            addCriterion("APPLY_ORG_NAME not like", value, "applyOrgName");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNameIn(List<String> values) {
            addCriterion("APPLY_ORG_NAME in", values, "applyOrgName");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNameNotIn(List<String> values) {
            addCriterion("APPLY_ORG_NAME not in", values, "applyOrgName");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNameBetween(String value1, String value2) {
            addCriterion("APPLY_ORG_NAME between", value1, value2, "applyOrgName");
            return (Criteria) this;
        }

        public Criteria andApplyOrgNameNotBetween(String value1, String value2) {
            addCriterion("APPLY_ORG_NAME not between", value1, value2, "applyOrgName");
            return (Criteria) this;
        }

        public Criteria andApplyTimeIsNull() {
            addCriterion("APPLY_TIME is null");
            return (Criteria) this;
        }

        public Criteria andApplyTimeIsNotNull() {
            addCriterion("APPLY_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andApplyTimeEqualTo(Date value) {
            addCriterion("APPLY_TIME =", value, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeNotEqualTo(Date value) {
            addCriterion("APPLY_TIME <>", value, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeGreaterThan(Date value) {
            addCriterion("APPLY_TIME >", value, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("APPLY_TIME >=", value, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeLessThan(Date value) {
            addCriterion("APPLY_TIME <", value, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeLessThanOrEqualTo(Date value) {
            addCriterion("APPLY_TIME <=", value, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeIn(List<Date> values) {
            addCriterion("APPLY_TIME in", values, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeNotIn(List<Date> values) {
            addCriterion("APPLY_TIME not in", values, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeBetween(Date value1, Date value2) {
            addCriterion("APPLY_TIME between", value1, value2, "applyTime");
            return (Criteria) this;
        }

        public Criteria andApplyTimeNotBetween(Date value1, Date value2) {
            addCriterion("APPLY_TIME not between", value1, value2, "applyTime");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusIsNull() {
            addCriterion("VERIFY_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusIsNotNull() {
            addCriterion("VERIFY_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusEqualTo(Integer value) {
            addCriterion("VERIFY_STATUS =", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusNotEqualTo(Integer value) {
            addCriterion("VERIFY_STATUS <>", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusGreaterThan(Integer value) {
            addCriterion("VERIFY_STATUS >", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("VERIFY_STATUS >=", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusLessThan(Integer value) {
            addCriterion("VERIFY_STATUS <", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusLessThanOrEqualTo(Integer value) {
            addCriterion("VERIFY_STATUS <=", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusIn(List<Integer> values) {
            addCriterion("VERIFY_STATUS in", values, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusNotIn(List<Integer> values) {
            addCriterion("VERIFY_STATUS not in", values, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusBetween(Integer value1, Integer value2) {
            addCriterion("VERIFY_STATUS between", value1, value2, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("VERIFY_STATUS not between", value1, value2, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIsNull() {
            addCriterion("DELIVERY_STATUS is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIsNotNull() {
            addCriterion("DELIVERY_STATUS is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusEqualTo(Integer value) {
            addCriterion("DELIVERY_STATUS =", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotEqualTo(Integer value) {
            addCriterion("DELIVERY_STATUS <>", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusGreaterThan(Integer value) {
            addCriterion("DELIVERY_STATUS >", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_STATUS >=", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusLessThan(Integer value) {
            addCriterion("DELIVERY_STATUS <", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusLessThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_STATUS <=", value, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusIn(List<Integer> values) {
            addCriterion("DELIVERY_STATUS in", values, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotIn(List<Integer> values) {
            addCriterion("DELIVERY_STATUS not in", values, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_STATUS between", value1, value2, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_STATUS not between", value1, value2, "deliveryStatus");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNull() {
            addCriterion("DELIVERY_TIME is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIsNotNull() {
            addCriterion("DELIVERY_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeEqualTo(Date value) {
            addCriterion("DELIVERY_TIME =", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotEqualTo(Date value) {
            addCriterion("DELIVERY_TIME <>", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThan(Date value) {
            addCriterion("DELIVERY_TIME >", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("DELIVERY_TIME >=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThan(Date value) {
            addCriterion("DELIVERY_TIME <", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeLessThanOrEqualTo(Date value) {
            addCriterion("DELIVERY_TIME <=", value, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeIn(List<Date> values) {
            addCriterion("DELIVERY_TIME in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotIn(List<Date> values) {
            addCriterion("DELIVERY_TIME not in", values, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeBetween(Date value1, Date value2) {
            addCriterion("DELIVERY_TIME between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andDeliveryTimeNotBetween(Date value1, Date value2) {
            addCriterion("DELIVERY_TIME not between", value1, value2, "deliveryTime");
            return (Criteria) this;
        }

        public Criteria andLogisticsNameIsNull() {
            addCriterion("LOGISTICS_NAME is null");
            return (Criteria) this;
        }

        public Criteria andLogisticsNameIsNotNull() {
            addCriterion("LOGISTICS_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andLogisticsNameEqualTo(Integer value) {
            addCriterion("LOGISTICS_NAME =", value, "logisticsName");
            return (Criteria) this;
        }

        public Criteria andLogisticsNameNotEqualTo(Integer value) {
            addCriterion("LOGISTICS_NAME <>", value, "logisticsName");
            return (Criteria) this;
        }

        public Criteria andLogisticsNameGreaterThan(Integer value) {
            addCriterion("LOGISTICS_NAME >", value, "logisticsName");
            return (Criteria) this;
        }

        public Criteria andLogisticsNameGreaterThanOrEqualTo(Integer value) {
            addCriterion("LOGISTICS_NAME >=", value, "logisticsName");
            return (Criteria) this;
        }

        public Criteria andLogisticsNameLessThan(Integer value) {
            addCriterion("LOGISTICS_NAME <", value, "logisticsName");
            return (Criteria) this;
        }

        public Criteria andLogisticsNameLessThanOrEqualTo(Integer value) {
            addCriterion("LOGISTICS_NAME <=", value, "logisticsName");
            return (Criteria) this;
        }

        public Criteria andLogisticsNameIn(List<Integer> values) {
            addCriterion("LOGISTICS_NAME in", values, "logisticsName");
            return (Criteria) this;
        }

        public Criteria andLogisticsNameNotIn(List<Integer> values) {
            addCriterion("LOGISTICS_NAME not in", values, "logisticsName");
            return (Criteria) this;
        }

        public Criteria andLogisticsNameBetween(Integer value1, Integer value2) {
            addCriterion("LOGISTICS_NAME between", value1, value2, "logisticsName");
            return (Criteria) this;
        }

        public Criteria andLogisticsNameNotBetween(Integer value1, Integer value2) {
            addCriterion("LOGISTICS_NAME not between", value1, value2, "logisticsName");
            return (Criteria) this;
        }

        public Criteria andExpressNosIsNull() {
            addCriterion("EXPRESS_NOS is null");
            return (Criteria) this;
        }

        public Criteria andExpressNosIsNotNull() {
            addCriterion("EXPRESS_NOS is not null");
            return (Criteria) this;
        }

        public Criteria andExpressNosEqualTo(String value) {
            addCriterion("EXPRESS_NOS =", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosNotEqualTo(String value) {
            addCriterion("EXPRESS_NOS <>", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosGreaterThan(String value) {
            addCriterion("EXPRESS_NOS >", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosGreaterThanOrEqualTo(String value) {
            addCriterion("EXPRESS_NOS >=", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosLessThan(String value) {
            addCriterion("EXPRESS_NOS <", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosLessThanOrEqualTo(String value) {
            addCriterion("EXPRESS_NOS <=", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosLike(String value) {
            addCriterion("EXPRESS_NOS like", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosNotLike(String value) {
            addCriterion("EXPRESS_NOS not like", value, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosIn(List<String> values) {
            addCriterion("EXPRESS_NOS in", values, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosNotIn(List<String> values) {
            addCriterion("EXPRESS_NOS not in", values, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosBetween(String value1, String value2) {
            addCriterion("EXPRESS_NOS between", value1, value2, "expressNos");
            return (Criteria) this;
        }

        public Criteria andExpressNosNotBetween(String value1, String value2) {
            addCriterion("EXPRESS_NOS not between", value1, value2, "expressNos");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeIsNull() {
            addCriterion("DELIVERY_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeIsNotNull() {
            addCriterion("DELIVERY_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeEqualTo(Integer value) {
            addCriterion("DELIVERY_TYPE =", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotEqualTo(Integer value) {
            addCriterion("DELIVERY_TYPE <>", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeGreaterThan(Integer value) {
            addCriterion("DELIVERY_TYPE >", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_TYPE >=", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeLessThan(Integer value) {
            addCriterion("DELIVERY_TYPE <", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeLessThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_TYPE <=", value, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeIn(List<Integer> values) {
            addCriterion("DELIVERY_TYPE in", values, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotIn(List<Integer> values) {
            addCriterion("DELIVERY_TYPE not in", values, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_TYPE between", value1, value2, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_TYPE not between", value1, value2, "deliveryType");
            return (Criteria) this;
        }

        public Criteria andDeliveryDeptIsNull() {
            addCriterion("DELIVERY_DEPT is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDeptIsNotNull() {
            addCriterion("DELIVERY_DEPT is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryDeptEqualTo(Integer value) {
            addCriterion("DELIVERY_DEPT =", value, "deliveryDept");
            return (Criteria) this;
        }

        public Criteria andDeliveryDeptNotEqualTo(Integer value) {
            addCriterion("DELIVERY_DEPT <>", value, "deliveryDept");
            return (Criteria) this;
        }

        public Criteria andDeliveryDeptGreaterThan(Integer value) {
            addCriterion("DELIVERY_DEPT >", value, "deliveryDept");
            return (Criteria) this;
        }

        public Criteria andDeliveryDeptGreaterThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_DEPT >=", value, "deliveryDept");
            return (Criteria) this;
        }

        public Criteria andDeliveryDeptLessThan(Integer value) {
            addCriterion("DELIVERY_DEPT <", value, "deliveryDept");
            return (Criteria) this;
        }

        public Criteria andDeliveryDeptLessThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_DEPT <=", value, "deliveryDept");
            return (Criteria) this;
        }

        public Criteria andDeliveryDeptIn(List<Integer> values) {
            addCriterion("DELIVERY_DEPT in", values, "deliveryDept");
            return (Criteria) this;
        }

        public Criteria andDeliveryDeptNotIn(List<Integer> values) {
            addCriterion("DELIVERY_DEPT not in", values, "deliveryDept");
            return (Criteria) this;
        }

        public Criteria andDeliveryDeptBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_DEPT between", value1, value2, "deliveryDept");
            return (Criteria) this;
        }

        public Criteria andDeliveryDeptNotBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_DEPT not between", value1, value2, "deliveryDept");
            return (Criteria) this;
        }

        public Criteria andDeliveryTotalWeightIsNull() {
            addCriterion("DELIVERY_TOTAL_WEIGHT is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTotalWeightIsNotNull() {
            addCriterion("DELIVERY_TOTAL_WEIGHT is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryTotalWeightEqualTo(BigDecimal value) {
            addCriterion("DELIVERY_TOTAL_WEIGHT =", value, "deliveryTotalWeight");
            return (Criteria) this;
        }

        public Criteria andDeliveryTotalWeightNotEqualTo(BigDecimal value) {
            addCriterion("DELIVERY_TOTAL_WEIGHT <>", value, "deliveryTotalWeight");
            return (Criteria) this;
        }

        public Criteria andDeliveryTotalWeightGreaterThan(BigDecimal value) {
            addCriterion("DELIVERY_TOTAL_WEIGHT >", value, "deliveryTotalWeight");
            return (Criteria) this;
        }

        public Criteria andDeliveryTotalWeightGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("DELIVERY_TOTAL_WEIGHT >=", value, "deliveryTotalWeight");
            return (Criteria) this;
        }

        public Criteria andDeliveryTotalWeightLessThan(BigDecimal value) {
            addCriterion("DELIVERY_TOTAL_WEIGHT <", value, "deliveryTotalWeight");
            return (Criteria) this;
        }

        public Criteria andDeliveryTotalWeightLessThanOrEqualTo(BigDecimal value) {
            addCriterion("DELIVERY_TOTAL_WEIGHT <=", value, "deliveryTotalWeight");
            return (Criteria) this;
        }

        public Criteria andDeliveryTotalWeightIn(List<BigDecimal> values) {
            addCriterion("DELIVERY_TOTAL_WEIGHT in", values, "deliveryTotalWeight");
            return (Criteria) this;
        }

        public Criteria andDeliveryTotalWeightNotIn(List<BigDecimal> values) {
            addCriterion("DELIVERY_TOTAL_WEIGHT not in", values, "deliveryTotalWeight");
            return (Criteria) this;
        }

        public Criteria andDeliveryTotalWeightBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("DELIVERY_TOTAL_WEIGHT between", value1, value2, "deliveryTotalWeight");
            return (Criteria) this;
        }

        public Criteria andDeliveryTotalWeightNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("DELIVERY_TOTAL_WEIGHT not between", value1, value2, "deliveryTotalWeight");
            return (Criteria) this;
        }

        public Criteria andDeliveryProdNameIsNull() {
            addCriterion("DELIVERY_PROD_NAME is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryProdNameIsNotNull() {
            addCriterion("DELIVERY_PROD_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryProdNameEqualTo(String value) {
            addCriterion("DELIVERY_PROD_NAME =", value, "deliveryProdName");
            return (Criteria) this;
        }

        public Criteria andDeliveryProdNameNotEqualTo(String value) {
            addCriterion("DELIVERY_PROD_NAME <>", value, "deliveryProdName");
            return (Criteria) this;
        }

        public Criteria andDeliveryProdNameGreaterThan(String value) {
            addCriterion("DELIVERY_PROD_NAME >", value, "deliveryProdName");
            return (Criteria) this;
        }

        public Criteria andDeliveryProdNameGreaterThanOrEqualTo(String value) {
            addCriterion("DELIVERY_PROD_NAME >=", value, "deliveryProdName");
            return (Criteria) this;
        }

        public Criteria andDeliveryProdNameLessThan(String value) {
            addCriterion("DELIVERY_PROD_NAME <", value, "deliveryProdName");
            return (Criteria) this;
        }

        public Criteria andDeliveryProdNameLessThanOrEqualTo(String value) {
            addCriterion("DELIVERY_PROD_NAME <=", value, "deliveryProdName");
            return (Criteria) this;
        }

        public Criteria andDeliveryProdNameLike(String value) {
            addCriterion("DELIVERY_PROD_NAME like", value, "deliveryProdName");
            return (Criteria) this;
        }

        public Criteria andDeliveryProdNameNotLike(String value) {
            addCriterion("DELIVERY_PROD_NAME not like", value, "deliveryProdName");
            return (Criteria) this;
        }

        public Criteria andDeliveryProdNameIn(List<String> values) {
            addCriterion("DELIVERY_PROD_NAME in", values, "deliveryProdName");
            return (Criteria) this;
        }

        public Criteria andDeliveryProdNameNotIn(List<String> values) {
            addCriterion("DELIVERY_PROD_NAME not in", values, "deliveryProdName");
            return (Criteria) this;
        }

        public Criteria andDeliveryProdNameBetween(String value1, String value2) {
            addCriterion("DELIVERY_PROD_NAME between", value1, value2, "deliveryProdName");
            return (Criteria) this;
        }

        public Criteria andDeliveryProdNameNotBetween(String value1, String value2) {
            addCriterion("DELIVERY_PROD_NAME not between", value1, value2, "deliveryProdName");
            return (Criteria) this;
        }

        public Criteria andDeliveryExpressNumIsNull() {
            addCriterion("DELIVERY_EXPRESS_NUM is null");
            return (Criteria) this;
        }

        public Criteria andDeliveryExpressNumIsNotNull() {
            addCriterion("DELIVERY_EXPRESS_NUM is not null");
            return (Criteria) this;
        }

        public Criteria andDeliveryExpressNumEqualTo(Integer value) {
            addCriterion("DELIVERY_EXPRESS_NUM =", value, "deliveryExpressNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryExpressNumNotEqualTo(Integer value) {
            addCriterion("DELIVERY_EXPRESS_NUM <>", value, "deliveryExpressNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryExpressNumGreaterThan(Integer value) {
            addCriterion("DELIVERY_EXPRESS_NUM >", value, "deliveryExpressNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryExpressNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_EXPRESS_NUM >=", value, "deliveryExpressNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryExpressNumLessThan(Integer value) {
            addCriterion("DELIVERY_EXPRESS_NUM <", value, "deliveryExpressNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryExpressNumLessThanOrEqualTo(Integer value) {
            addCriterion("DELIVERY_EXPRESS_NUM <=", value, "deliveryExpressNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryExpressNumIn(List<Integer> values) {
            addCriterion("DELIVERY_EXPRESS_NUM in", values, "deliveryExpressNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryExpressNumNotIn(List<Integer> values) {
            addCriterion("DELIVERY_EXPRESS_NUM not in", values, "deliveryExpressNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryExpressNumBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_EXPRESS_NUM between", value1, value2, "deliveryExpressNum");
            return (Criteria) this;
        }

        public Criteria andDeliveryExpressNumNotBetween(Integer value1, Integer value2) {
            addCriterion("DELIVERY_EXPRESS_NUM not between", value1, value2, "deliveryExpressNum");
            return (Criteria) this;
        }

        public Criteria andContentIsNull() {
            addCriterion("CONTENT is null");
            return (Criteria) this;
        }

        public Criteria andContentIsNotNull() {
            addCriterion("CONTENT is not null");
            return (Criteria) this;
        }

        public Criteria andContentEqualTo(String value) {
            addCriterion("CONTENT =", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotEqualTo(String value) {
            addCriterion("CONTENT <>", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThan(String value) {
            addCriterion("CONTENT >", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentGreaterThanOrEqualTo(String value) {
            addCriterion("CONTENT >=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThan(String value) {
            addCriterion("CONTENT <", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLessThanOrEqualTo(String value) {
            addCriterion("CONTENT <=", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentLike(String value) {
            addCriterion("CONTENT like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotLike(String value) {
            addCriterion("CONTENT not like", value, "content");
            return (Criteria) this;
        }

        public Criteria andContentIn(List<String> values) {
            addCriterion("CONTENT in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotIn(List<String> values) {
            addCriterion("CONTENT not in", values, "content");
            return (Criteria) this;
        }

        public Criteria andContentBetween(String value1, String value2) {
            addCriterion("CONTENT between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andContentNotBetween(String value1, String value2) {
            addCriterion("CONTENT not between", value1, value2, "content");
            return (Criteria) this;
        }

        public Criteria andIsClosedIsNull() {
            addCriterion("IS_CLOSED is null");
            return (Criteria) this;
        }

        public Criteria andIsClosedIsNotNull() {
            addCriterion("IS_CLOSED is not null");
            return (Criteria) this;
        }

        public Criteria andIsClosedEqualTo(Integer value) {
            addCriterion("IS_CLOSED =", value, "isClosed");
            return (Criteria) this;
        }

        public Criteria andIsClosedNotEqualTo(Integer value) {
            addCriterion("IS_CLOSED <>", value, "isClosed");
            return (Criteria) this;
        }

        public Criteria andIsClosedGreaterThan(Integer value) {
            addCriterion("IS_CLOSED >", value, "isClosed");
            return (Criteria) this;
        }

        public Criteria andIsClosedGreaterThanOrEqualTo(Integer value) {
            addCriterion("IS_CLOSED >=", value, "isClosed");
            return (Criteria) this;
        }

        public Criteria andIsClosedLessThan(Integer value) {
            addCriterion("IS_CLOSED <", value, "isClosed");
            return (Criteria) this;
        }

        public Criteria andIsClosedLessThanOrEqualTo(Integer value) {
            addCriterion("IS_CLOSED <=", value, "isClosed");
            return (Criteria) this;
        }

        public Criteria andIsClosedIn(List<Integer> values) {
            addCriterion("IS_CLOSED in", values, "isClosed");
            return (Criteria) this;
        }

        public Criteria andIsClosedNotIn(List<Integer> values) {
            addCriterion("IS_CLOSED not in", values, "isClosed");
            return (Criteria) this;
        }

        public Criteria andIsClosedBetween(Integer value1, Integer value2) {
            addCriterion("IS_CLOSED between", value1, value2, "isClosed");
            return (Criteria) this;
        }

        public Criteria andIsClosedNotBetween(Integer value1, Integer value2) {
            addCriterion("IS_CLOSED not between", value1, value2, "isClosed");
            return (Criteria) this;
        }

        public Criteria andClosedCommentsIsNull() {
            addCriterion("CLOSED_COMMENTS is null");
            return (Criteria) this;
        }

        public Criteria andClosedCommentsIsNotNull() {
            addCriterion("CLOSED_COMMENTS is not null");
            return (Criteria) this;
        }

        public Criteria andClosedCommentsEqualTo(String value) {
            addCriterion("CLOSED_COMMENTS =", value, "closedComments");
            return (Criteria) this;
        }

        public Criteria andClosedCommentsNotEqualTo(String value) {
            addCriterion("CLOSED_COMMENTS <>", value, "closedComments");
            return (Criteria) this;
        }

        public Criteria andClosedCommentsGreaterThan(String value) {
            addCriterion("CLOSED_COMMENTS >", value, "closedComments");
            return (Criteria) this;
        }

        public Criteria andClosedCommentsGreaterThanOrEqualTo(String value) {
            addCriterion("CLOSED_COMMENTS >=", value, "closedComments");
            return (Criteria) this;
        }

        public Criteria andClosedCommentsLessThan(String value) {
            addCriterion("CLOSED_COMMENTS <", value, "closedComments");
            return (Criteria) this;
        }

        public Criteria andClosedCommentsLessThanOrEqualTo(String value) {
            addCriterion("CLOSED_COMMENTS <=", value, "closedComments");
            return (Criteria) this;
        }

        public Criteria andClosedCommentsLike(String value) {
            addCriterion("CLOSED_COMMENTS like", value, "closedComments");
            return (Criteria) this;
        }

        public Criteria andClosedCommentsNotLike(String value) {
            addCriterion("CLOSED_COMMENTS not like", value, "closedComments");
            return (Criteria) this;
        }

        public Criteria andClosedCommentsIn(List<String> values) {
            addCriterion("CLOSED_COMMENTS in", values, "closedComments");
            return (Criteria) this;
        }

        public Criteria andClosedCommentsNotIn(List<String> values) {
            addCriterion("CLOSED_COMMENTS not in", values, "closedComments");
            return (Criteria) this;
        }

        public Criteria andClosedCommentsBetween(String value1, String value2) {
            addCriterion("CLOSED_COMMENTS between", value1, value2, "closedComments");
            return (Criteria) this;
        }

        public Criteria andClosedCommentsNotBetween(String value1, String value2) {
            addCriterion("CLOSED_COMMENTS not between", value1, value2, "closedComments");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("IS_DELETED is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("IS_DELETED is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("IS_DELETED =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("IS_DELETED <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("IS_DELETED >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("IS_DELETED >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("IS_DELETED <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("IS_DELETED <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("IS_DELETED in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("IS_DELETED not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("IS_DELETED between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("IS_DELETED not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Date value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Date value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Date value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Date value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Date> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Date> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkIsNull() {
            addCriterion("UPDATE_REMARK is null");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkIsNotNull() {
            addCriterion("UPDATE_REMARK is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkEqualTo(String value) {
            addCriterion("UPDATE_REMARK =", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkNotEqualTo(String value) {
            addCriterion("UPDATE_REMARK <>", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkGreaterThan(String value) {
            addCriterion("UPDATE_REMARK >", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("UPDATE_REMARK >=", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkLessThan(String value) {
            addCriterion("UPDATE_REMARK <", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkLessThanOrEqualTo(String value) {
            addCriterion("UPDATE_REMARK <=", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkLike(String value) {
            addCriterion("UPDATE_REMARK like", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkNotLike(String value) {
            addCriterion("UPDATE_REMARK not like", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkIn(List<String> values) {
            addCriterion("UPDATE_REMARK in", values, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkNotIn(List<String> values) {
            addCriterion("UPDATE_REMARK not in", values, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkBetween(String value1, String value2) {
            addCriterion("UPDATE_REMARK between", value1, value2, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkNotBetween(String value1, String value2) {
            addCriterion("UPDATE_REMARK not between", value1, value2, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andSendUserTypeIsNull() {
            addCriterion("SEND_USER_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andSendUserTypeIsNotNull() {
            addCriterion("SEND_USER_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andSendUserTypeEqualTo(Integer value) {
            addCriterion("SEND_USER_TYPE =", value, "sendUserType");
            return (Criteria) this;
        }

        public Criteria andSendUserTypeNotEqualTo(Integer value) {
            addCriterion("SEND_USER_TYPE <>", value, "sendUserType");
            return (Criteria) this;
        }

        public Criteria andSendUserTypeGreaterThan(Integer value) {
            addCriterion("SEND_USER_TYPE >", value, "sendUserType");
            return (Criteria) this;
        }

        public Criteria andSendUserTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("SEND_USER_TYPE >=", value, "sendUserType");
            return (Criteria) this;
        }

        public Criteria andSendUserTypeLessThan(Integer value) {
            addCriterion("SEND_USER_TYPE <", value, "sendUserType");
            return (Criteria) this;
        }

        public Criteria andSendUserTypeLessThanOrEqualTo(Integer value) {
            addCriterion("SEND_USER_TYPE <=", value, "sendUserType");
            return (Criteria) this;
        }

        public Criteria andSendUserTypeIn(List<Integer> values) {
            addCriterion("SEND_USER_TYPE in", values, "sendUserType");
            return (Criteria) this;
        }

        public Criteria andSendUserTypeNotIn(List<Integer> values) {
            addCriterion("SEND_USER_TYPE not in", values, "sendUserType");
            return (Criteria) this;
        }

        public Criteria andSendUserTypeBetween(Integer value1, Integer value2) {
            addCriterion("SEND_USER_TYPE between", value1, value2, "sendUserType");
            return (Criteria) this;
        }

        public Criteria andSendUserTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("SEND_USER_TYPE not between", value1, value2, "sendUserType");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated do_not_delete_during_merge Fri Nov 22 14:17:41 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_FILE_DELIVERY_NEW
     *
     * @mbg.generated Fri Nov 22 14:17:41 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}