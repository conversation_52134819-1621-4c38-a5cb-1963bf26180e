package com.vedeng.erp.broadcast.mapdao;

import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptRErpDeptEntity;
import com.vedeng.erp.common.broadcast.bo.BroadcastDeptRErpDeptBo;
import com.vedeng.erp.common.broadcast.param.DeptInfo;

import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * ERP末级部门与通知二级部门关系Mapper
 */
public interface BroadcastDeptRErpDeptMapper {

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 删除记录数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insert(BroadcastDeptRErpDeptEntity record);

    /**
     * 选择性插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insertSelective(BroadcastDeptRErpDeptEntity record);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录
     */
    BroadcastDeptRErpDeptEntity selectByPrimaryKey(Integer id);

    /**
     * 选择性更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKeySelective(BroadcastDeptRErpDeptEntity record);

    /**
     * 根据主键更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKey(BroadcastDeptRErpDeptEntity record);

    List<BroadcastDeptRErpDeptEntity> getAllBroadcastDeptRelate();

    /**
     * 逻辑删除所有部门关系记录
     *
     * @param userId 操作人ID
     * @return 更新记录数
     */
    int deleteAllBroadcastDeptRelate(Integer userId);

    /**
     * 批量插入部门关系记录
     *
     * @param list 记录列表
     * @return 插入记录数
     */
    int insertBatch(@Param("list") List<BroadcastDeptRErpDeptEntity> list);

    
    /**
     * 根据部门ID获取获取该部门下所有的小组信息
     * @param deptId
     * @param orgId
     * @return
     */
	List<BroadcastDeptRErpDeptBo> selectAllByDeptId(@Param("deptId") Integer deptId);


	List<BroadcastDeptRErpDeptBo> selectDeptByDeptId(@Param("deptId") Integer deptId);
	
	/**
	 * 根据组织ID获取ERP末级部门与通知二级部门关系
	 * @param orgIdList
	 * @return
	 */
	List<BroadcastDeptRErpDeptEntity> selectByErpDeptIdList(@Param("orgIdList") List<Integer> orgIdList);
	
	/**
	 * 根据erp组织ID获取所在的播报部门配置
	 * @param orgId
	 * @return
	 */
	DeptInfo  selectByErpOrgId(@Param("orgId") Integer orgId);
}
