$(function() {
    //默认进入新增页面，隐藏单选
    $("#accountPeriodTable").find('th:eq(0)').hide();
    $("#accountPeriodTable tr").find('td:eq(0)').hide();
    $("#amountChange").hide();
    $("#addBtn").css("background-color","blue");
    $("#uptBtn").css("background-color","white");
    $("#uptBtn").css("color","black");
    $("#addBtn").css("color","white");

});
$("#changeMount").change(function () {
    checkLogin();
    clearErroeMes();
    $("#changeMount").removeClass("errorbor");
    var changeMount = $("#changeMount").val();
    var finalApplyAmount = $("#formerAmount").val();
    if(finalApplyAmount == "-"){
        finalApplyAmount = 0;
    }
    var changeType = $("#changeType").val();
    var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    if(changeMount.length>0 && !reg.test(changeMount)){
        warn2Tips("changeMount","调整额度输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
        return false;
    }
    if(changeType == "上调"){
        $("#applyAmount").text(Number(finalApplyAmount) + Number(changeMount));
    }else {
        var usefulAmount = $("#finalApplyAmount").val();
        var applyAmount = (Number(finalApplyAmount) - Number(changeMount)).toFixed(2);
        $("#applyAmount").text(applyAmount);
        if(applyAmount < Number(usefulAmount)){
            warn2Tips("changeMount","额度降低的范围不可高于目前可用额度！");
            $("#changeMount").addClass("errorbor");
            return false;
        }
    }
});

$("#changeType").change(function () {
    checkLogin();
    clearErroeMes();
    $("#changeMount").removeClass("errorbor");
    var changeMount = $("#changeMount").val();
    var finalApplyAmount = $("#formerAmount").val();
    if(finalApplyAmount == "-"){
        finalApplyAmount = 0;
    }
    var changeType = $("#changeType").val();
    var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    if(changeMount.length>0 && !reg.test(changeMount)){
        warn2Tips("changeMount","调整额度输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
        return false;
    }
    if(changeType == "上调"){
        $("#applyAmount").text(Number(finalApplyAmount) + Number(changeMount));
    }else {
        var usefulAmount = $("#finalApplyAmount").val();
        var applyAmount = (Number(finalApplyAmount) - Number(changeMount)).toFixed(2);
        $("#applyAmount").text(applyAmount);
        if(applyAmount < Number(usefulAmount)){
            warn2Tips("changeMount","额度降低的范围不可高于目前可用额度！");
            $("#changeMount").addClass("errorbor");
            return false;
        }
    }
});

//新增临时账期
function resetEmpty() {
    //隐藏临时账期选择按钮
    uptToAdd();
}

function addToUpt() {
    $("#accountPeriodTable").find('th:eq(0)').show();
    $("#accountPeriodTable tr").find('td:eq(0)').show();
    $("#amountChange").show();
    $("#uptBtn").css("background-color","blue");
    $("#addBtn").css("background-color","white");
    $("#addBtn").css("color","black");
    $("#uptBtn").css("color","white");
    $("#timeTips").text("--开始时间不可调整，不调整无需修改");
    $("#amountTips").text("--不调整可设百0。调低范围不可大于当前可用额");
    $("#amountLabel").text("额度调整");
    $("#amountAdd").hide()
    $("#amountChange").show();
    $("#insertApplyAmount").val(0);
    $("#operateType").val(2);
    $("#billPeriodStart").attr("disabled","disabled");
    $("#billPeriodEnd").attr("disabled","disabled");
    $("#billPeriodStart").removeAttr("onFocus");
    $("#billPeriodEnd").removeAttr("onFocus");
}
function uptToAdd() {
    $("#accountPeriodTable").find('th:eq(0)').hide();
    $("#accountPeriodTable tr").find('td:eq(0)').hide();
    $("#amountChange").hide();
    $("#addBtn").css("background-color","blue");
    $("#uptBtn").css("background-color","white");
    $("#uptBtn").css("color","black");
    $("#addBtn").css("color","white");
    $("#timeTips").text(" --设置开始及结束时间，有效期原则上不可超过60天,开始日期必须在现有账期的截止日期之后");
    $("#amountTips").text("--仅可输入正数。支持2位小数");
    $("#amountLabel").text("申请额度");
    $("#amountAdd").show()
    $("#amountChange").hide();
    $("#applyAmount").text(0);
    $("#operateType").val(1);
    $("#billPeriodStart").val('');
    $("#billPeriodEnd").val('');
    $("#settlementPeriod").val(0);
    $("#expectedMargin").val(0);
    $("#applyReason").text('');
    $("#billPeriodId").val('');
    $("#billPeriodStart").removeAttr("disabled");
    $("#billPeriodEnd").removeAttr("disabled");;
    $("#billPeriodStart").attr("onFocus","WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\\'billPeriodEnd\\')}'})");
    $("#billPeriodEnd").attr("onFocus","WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\\'billPeriodStart\\')}'})");
}
function clearInfo() {
    $("#billPeriodStart").val('');
    $("#billPeriodEnd").val('');
    $("#billPeriodEnd").val('');
    $("#applyAmount").text(0);
    $("#settlementPeriod").val('');
    $("#expectedMargin").val('');
    $("#applyReason").val('');
    $('#insertApplyAmount').val(0);
    $("#operateType").val(1);
    $("#billPeriodId").val('');
}

//提交表单校验
$('#myform').submit(function() {
    checkLogin();
    clearErroeMes();
    var operateType = $("#operateType").val();
    var insertApplyAmount = $("#insertApplyAmount").val();
    var applyAmount = $("#applyAmount").text();
    var changeMount = $("#changeMount").val();
    if(operateType == 1){
        $("#finalApplyAmount").val(Number(insertApplyAmount));
    }else {
        var changeType = $("#changeType").val();
        if(changeType == "上调"){
            $("#finalApplyAmount").val(changeMount);
        }else {
            $("#finalApplyAmount").val(-changeMount);
        }
    }
    jQuery.ajax({
        url:'./saveAccountPeriodApplyNew.do',
        type:"POST",
        dataType : "json",
        beforeSend:function(){
            var billPeriodStart = $("#billPeriodStart").val();
            var billPeriodEnd = $("#billPeriodEnd").val();
            if(billPeriodStart.length == 0){
                warn2Tips("billPeriodStart","有效期开始时间不能为空");
                return false;
            }
            if(billPeriodEnd.length == 0){
                warn2Tips("billPeriodEnd","有效期结束时间不能为空");
                return false;
            }
            if(billPeriodStart > billPeriodEnd){
                warn2Tips("billPeriodEnd","有效期开始时间不能大于结束时间");
                return false;
            }
            var re = /^[0-9]+$/;
            var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
            if(insertApplyAmount.length>0 && !reg.test(insertApplyAmount)){
                warn2Tips("insertApplyAmount","申请额度输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                return false;
            }

            var settlementPeriod = $("#settlementPeriod").val();
            if(settlementPeriod.length == 0){
                warn2Tips("settlementPeriod","结算周期不能为空");//文本框ID和提示用语
                return false;
            }
            if(settlementPeriod.length>0 && !re.test(settlementPeriod)){
                warn2Tips("settlementPeriod","结算周期输入错误！仅允许使用数字");//文本框ID和提示用语
                return false;
            }
            var expectedMargin = $("#expectedMargin").val();
            if(expectedMargin.length>0 && !reg.test(expectedMargin)){
                warn2Tips("expectedMargin","预期毛利率输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                return false;
            }
            var applyReason = $("#applyReason").val();
            if(applyReason.length == 0){
                warn2Tips("applyReason","申请原因不能为空");//文本框ID和提示用语
                return false;
            }
        },
        data:$('#myform').serialize(),
        success:function(data){
            if(data.code == 0){
                layer.alert(data.message,{
                    closeBtn: 0,
                    btn: ['确定'] //按钮
                }, function(){
                    var st=data.data.split(",");
                    var str=page_url+"/trader/customer/getFinanceAndAptitude.do?traderId="+st[0]+"&traderCustomerId="+st[1];
                    $("#finace").attr('href',str);
                    window.parent.location.reload();
                });
            }else{
                layer.alert(data.message);
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
    return false;
});