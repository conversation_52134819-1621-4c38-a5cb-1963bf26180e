package com.vedeng.erp.mobile.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.mobile.api.SaleOrderGoodsMobileApiService;
import com.vedeng.erp.mobile.dto.SaleOrderGoodsListResultDto;
import com.vedeng.erp.saleorder.dao.SaleOrderGoodsDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class SaleOrderGoodsMobileApiServiceImpl implements SaleOrderGoodsMobileApiService {

    @Autowired
    private SaleOrderGoodsDetailMapper saleOrderGoodsDetailMapper;

    @Override
    public List<SaleOrderGoodsListResultDto> getSaleOrderGoodsBySaleorderIds(List<Integer> saleorderIds) {
        if (CollUtil.isEmpty(saleorderIds)) {
            return Collections.emptyList();
        }
        return saleOrderGoodsDetailMapper.getSaleOrderGoodsBySaleorderIds(saleorderIds);
    }
}
