package com.newtask.data.trader;

import com.newtask.data.AbstractDataByTimeSync;
import com.newtask.data.AbstractDataSync;
import com.newtask.data.dao.TraderDateMapper;
import com.vedeng.common.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 更新客户审核状态
 */
@Component
public class TraderCheckStatusSync extends AbstractDataByTimeSync {
    @Autowired
    private TraderDateMapper traderDateMapper;
    @Override
    public void updateStatusProcess(String startTime) {
        super.updateStatusProcess(startTime);
    }

    /**
     * 根据时间查询 客户审核状态
     * @param startTime
     * @return
     */
    @Override
    public List<Map<String, Object>> loadBizByTimeData(Long startTime,Long endTime) {
        //当前时间戳
        List<Map<String, Object>> dataList = traderDateMapper.findCheckStatus(startTime,endTime);
            return dataList;
    }

    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if(CollectionUtils.isNotEmpty(dataList)){
            dataList.forEach(item->{
                Long nowDate = new Date().getTime();
                Integer traderId= NumberUtils.toInt(item.get("traderId")+"");
                //客户审核状态
                Integer  traderAttrCheckStatus= NumberUtils.toInt(item.get("traderAttrCheckStatus")+"");
                //判断宽表中是否存在这条记录
                makeExist(traderId,nowDate);
                traderDateMapper.updateCheckStatus(traderId,traderAttrCheckStatus,nowDate);
            });
        }


    }

    @Override
    public void makeExist(Integer traderId,Long nowDate) {
        Long count = traderDateMapper.makeExist(traderId);
        if (count <= 0) {
            //往宽表里添加数据
            traderDateMapper.insertTraderId(traderId,nowDate);
        }
    }
}
