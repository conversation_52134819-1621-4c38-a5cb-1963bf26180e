<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.CoreOperateInfoGenerateMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.goods.model.CoreOperateInfoGenerate">
        <id column="OPERATE_INFO_ID" jdbcType="INTEGER" property="operateInfoId"/>
        <result column="SPU_ID" jdbcType="INTEGER" property="spuId"/>
        <result column="SKU_ID" jdbcType="INTEGER" property="skuId"/>
        <result column="OPERATE_INFO_TYPE" jdbcType="TINYINT" property="operateInfoType"/>
        <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName"/>
        <result column="SEO_DESCRIPT" jdbcType="VARCHAR" property="seoDescript"/>
        <result column="SEO_KEYWORDS" jdbcType="VARCHAR" property="seoKeywords"/>
        <result column="SEO_TITLE" jdbcType="VARCHAR" property="seoTitle"/>
        <result column="OPERATE_INFO_SOURCE" jdbcType="TINYINT" property="operateInfoSource"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.vedeng.goods.model.CoreOperateInfoGenerate">
        <result column="OPRATE_INFO_HTML" jdbcType="LONGVARCHAR" property="oprateInfoHtml"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        OPERATE_INFO_ID, SPU_ID, SKU_ID, OPERATE_INFO_TYPE, GOODS_NAME, SEO_DESCRIPT, SEO_KEYWORDS,
        SEO_TITLE, OPERATE_INFO_SOURCE
    </sql>
    <sql id="Blob_Column_List">
        OPRATE_INFO_HTML
    </sql>
    <select id="selectByExampleWithBLOBs" parameterType="com.vedeng.goods.model.CoreOperateInfoGenerateExample" resultMap="ResultMapWithBLOBs">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from V_CORE_OPERATE_INFO
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByExample" parameterType="com.vedeng.goods.model.CoreOperateInfoGenerateExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from V_CORE_OPERATE_INFO
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from V_CORE_OPERATE_INFO
        where OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from V_CORE_OPERATE_INFO
        where OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER}
    </delete>
    <delete id="deleteByExample" parameterType="com.vedeng.goods.model.CoreOperateInfoGenerateExample">
        delete from V_CORE_OPERATE_INFO
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.vedeng.goods.model.CoreOperateInfoGenerate">
        <selectKey keyProperty="operateInfoId" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into V_CORE_OPERATE_INFO (SPU_ID, SKU_ID, OPERATE_INFO_TYPE,
        GOODS_NAME, SEO_DESCRIPT, SEO_KEYWORDS,
        SEO_TITLE, OPRATE_INFO_HTML)
        values (#{spuId,jdbcType=INTEGER}, #{skuId,jdbcType=INTEGER}, #{operateInfoType,jdbcType=TINYINT},
        #{goodsName,jdbcType=VARCHAR}, #{seoDescript,jdbcType=VARCHAR}, #{seoKeywords,jdbcType=VARCHAR},
        #{seoTitle,jdbcType=VARCHAR}, #{oprateInfoHtml,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.vedeng.goods.model.vo.CoreOperateInfoGenerateVo">
        <selectKey keyProperty="operateInfoId" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into V_CORE_OPERATE_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="spuId != null">
                SPU_ID,
            </if>
            <if test="skuId != null">
                SKU_ID,
            </if>
            <if test="operateInfoType != null">
                OPERATE_INFO_TYPE,
            </if>
            <if test="goodsName != null">
                GOODS_NAME,
            </if>
            <if test="seoDescript != null">
                SEO_DESCRIPT,
            </if>
            <if test="seoKeywords != null">
                SEO_KEYWORDS,
            </if>
            <if test="seoTitle != null">
                SEO_TITLE,
            </if>
            <if test="oprateInfoHtml != null">
                OPRATE_INFO_HTML,
            </if>
            <if test="operateInfoSource != null">
                OPERATE_INFO_SOURCE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="spuId != null">
                #{spuId,jdbcType=INTEGER},
            </if>
            <if test="skuId != null">
                #{skuId,jdbcType=INTEGER},
            </if>
            <if test="operateInfoType != null">
                #{operateInfoType,jdbcType=TINYINT},
            </if>
            <if test="goodsName != null">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="seoDescript != null">
                #{seoDescript,jdbcType=VARCHAR},
            </if>
            <if test="seoKeywords != null">
                #{seoKeywords,jdbcType=VARCHAR},
            </if>
            <if test="seoTitle != null">
                #{seoTitle,jdbcType=VARCHAR},
            </if>
            <if test="oprateInfoHtml != null">
                #{oprateInfoHtml,jdbcType=LONGVARCHAR},
            </if>
            <if test="operateInfoSource != null">
                #{operateInfoSource, jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.vedeng.goods.model.CoreOperateInfoGenerateExample" resultType="java.lang.Integer">
        select count(*) from V_CORE_OPERATE_INFO
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update V_CORE_OPERATE_INFO
        <set>
            <if test="record.operateInfoId != null">
                OPERATE_INFO_ID = #{record.operateInfoId,jdbcType=INTEGER},
            </if>
            <if test="record.spuId != null">
                SPU_ID = #{record.spuId,jdbcType=INTEGER},
            </if>
            <if test="record.skuId != null">
                SKU_ID = #{record.skuId,jdbcType=INTEGER},
            </if>
            <if test="record.operateInfoType != null">
                OPERATE_INFO_TYPE = #{record.operateInfoType,jdbcType=TINYINT},
            </if>
            <if test="record.goodsName != null">
                GOODS_NAME = #{record.goodsName,jdbcType=VARCHAR},
            </if>
            <if test="record.seoDescript != null">
                SEO_DESCRIPT = #{record.seoDescript,jdbcType=VARCHAR},
            </if>
            <if test="record.seoKeywords != null">
                SEO_KEYWORDS = #{record.seoKeywords,jdbcType=VARCHAR},
            </if>
            <if test="record.seoTitle != null">
                SEO_TITLE = #{record.seoTitle,jdbcType=VARCHAR},
            </if>
            <if test="record.oprateInfoHtml != null">
                OPRATE_INFO_HTML = #{record.oprateInfoHtml,jdbcType=LONGVARCHAR},
            </if>
            <if test="record.operateInfoSource != null">
                OPERATE_INFO_SOURCE = #{record.operateInfoSource,jdbcType=INTEGER},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExampleWithBLOBs" parameterType="map">
        update V_CORE_OPERATE_INFO
        set OPERATE_INFO_ID = #{record.operateInfoId,jdbcType=INTEGER},
        SPU_ID = #{record.spuId,jdbcType=INTEGER},
        SKU_ID = #{record.skuId,jdbcType=INTEGER},
        OPERATE_INFO_TYPE = #{record.operateInfoType,jdbcType=TINYINT},
        GOODS_NAME = #{record.goodsName,jdbcType=VARCHAR},
        SEO_DESCRIPT = #{record.seoDescript,jdbcType=VARCHAR},
        SEO_KEYWORDS = #{record.seoKeywords,jdbcType=VARCHAR},
        SEO_TITLE = #{record.seoTitle,jdbcType=VARCHAR},
        OPRATE_INFO_HTML = #{record.oprateInfoHtml,jdbcType=LONGVARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update V_CORE_OPERATE_INFO
        set OPERATE_INFO_ID = #{record.operateInfoId,jdbcType=INTEGER},
        SPU_ID = #{record.spuId,jdbcType=INTEGER},
        SKU_ID = #{record.skuId,jdbcType=INTEGER},
        OPERATE_INFO_TYPE = #{record.operateInfoType,jdbcType=TINYINT},
        GOODS_NAME = #{record.goodsName,jdbcType=VARCHAR},
        SEO_DESCRIPT = #{record.seoDescript,jdbcType=VARCHAR},
        SEO_KEYWORDS = #{record.seoKeywords,jdbcType=VARCHAR},
        SEO_TITLE = #{record.seoTitle,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.vo.CoreOperateInfoGenerateVo">
        update V_CORE_OPERATE_INFO
        <set>
            <if test="spuId != null">
                SPU_ID = #{spuId,jdbcType=INTEGER},
            </if>
            <if test="skuId != null">
                SKU_ID = #{skuId,jdbcType=INTEGER},
            </if>
            <if test="operateInfoType != null">
                OPERATE_INFO_TYPE = #{operateInfoType,jdbcType=TINYINT},
            </if>
            <if test="goodsName != null">
                GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="seoDescript != null">
                SEO_DESCRIPT = #{seoDescript,jdbcType=VARCHAR},
            </if>
            <if test="seoKeywords != null">
                SEO_KEYWORDS = #{seoKeywords,jdbcType=VARCHAR},
            </if>
            <if test="seoTitle != null">
                SEO_TITLE = #{seoTitle,jdbcType=VARCHAR},
            </if>
            <if test="oprateInfoHtml != null">
                OPRATE_INFO_HTML = #{oprateInfoHtml,jdbcType=LONGVARCHAR},
            </if>
            <if test="operateInfoSource != null">
                OPERATE_INFO_SOURCE = #{operateInfoSource, jdbcType=TINYINT},
            </if>
        </set>
        where OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vedeng.goods.model.CoreOperateInfoGenerate">
        update V_CORE_OPERATE_INFO
        set SPU_ID = #{spuId,jdbcType=INTEGER},
        SKU_ID = #{skuId,jdbcType=INTEGER},
        OPERATE_INFO_TYPE = #{operateInfoType,jdbcType=TINYINT},
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
        SEO_DESCRIPT = #{seoDescript,jdbcType=VARCHAR},
        SEO_KEYWORDS = #{seoKeywords,jdbcType=VARCHAR},
        SEO_TITLE = #{seoTitle,jdbcType=VARCHAR},
        OPRATE_INFO_HTML = #{oprateInfoHtml,jdbcType=LONGVARCHAR}
        where OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.CoreOperateInfoGenerate">
        update V_CORE_OPERATE_INFO
        set SPU_ID = #{spuId,jdbcType=INTEGER},
        SKU_ID = #{skuId,jdbcType=INTEGER},
        OPERATE_INFO_TYPE = #{operateInfoType,jdbcType=TINYINT},
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
        SEO_DESCRIPT = #{seoDescript,jdbcType=VARCHAR},
        SEO_KEYWORDS = #{seoKeywords,jdbcType=VARCHAR},
        SEO_TITLE = #{seoTitle,jdbcType=VARCHAR}
        where OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER}
    </update>

    <select id="selectCoreOperateInfo" parameterType="com.vedeng.goods.model.CoreOperateInfoGenerate" resultType="com.vedeng.goods.model.CoreOperateInfoGenerate">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from V_CORE_OPERATE_INFO
        where 1 = 1
            <if test="operateInfoId != null">
                AND OPERATE_INFO_ID = #{operateInfoId,jdbcType=INTEGER}
            </if>
            <if test="spuId != null">
                AND SPU_ID = #{spuId,jdbcType=INTEGER}
            </if>
            <if test="skuId != null">
                AND SKU_ID = #{skuId,jdbcType=INTEGER}
            </if>
            LIMIT 1
    </select>

    <update id="updateSeoKeyWords">
      update V_CORE_OPERATE_INFO
        set SEO_KEYWORDS = #{seoKeyWords,jdbcType=VARCHAR}
        where SKU_ID = #{skuId,jdbcType=INTEGER}
    </update>
</mapper>