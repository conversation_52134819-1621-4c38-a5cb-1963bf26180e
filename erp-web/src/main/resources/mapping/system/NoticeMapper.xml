<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.system.dao.NoticeMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.system.model.Notice" >
    <id column="NOTICE_ID" property="noticeId" jdbcType="INTEGER" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="NOTICE_CATEGORY" property="noticeCategory" jdbcType="INTEGER" />
    <result column="TITLE" property="title" jdbcType="VARCHAR" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="IS_TOP" property="isTop" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.vedeng.system.model.Notice" extends="BaseResultMap" >
    <result column="CONTENT" property="content" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    NOTICE_ID, COMPANY_ID, NOTICE_CATEGORY, TITLE, IS_ENABLE, IS_TOP, ADD_TIME, CREATOR, MOD_TIME, 
    UPDATER
  </sql>
  <sql id="Blob_Column_List" >
    CONTENT
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_NOTICE
    where NOTICE_ID = #{noticeId,jdbcType=INTEGER}
  </select>
 <select id="selectLastNotice" resultMap="ResultMapWithBLOBs">
 	select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
     from T_NOTICE
     WHERE IS_ENABLE=1 AND COMPANY_ID= #{companyId,jdbcType=INTEGER} AND NOTICE_CATEGORY=#{noticeCategory,jdbcType=INTEGER}
	 ORDER BY MOD_TIME DESC
	 LIMIT 1
 </select>
  <insert id="insert" parameterType="com.vedeng.system.model.Notice" >
    insert into T_NOTICE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="noticeId != null" >
        NOTICE_ID,
      </if>
      <if test="companyId != null" >
        COMPANY_ID,
      </if>
      <if test="noticeCategory != null" >
        NOTICE_CATEGORY,
      </if>
      <if test="title != null" >
        TITLE,
      </if>
      <if test="isEnable != null" >
        IS_ENABLE,
      </if>
      <if test="isTop != null" >
        IS_TOP,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="content != null" >
        CONTENT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="noticeId != null" >
        #{noticeId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null" >
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="noticeCategory != null" >
        #{noticeCategory,jdbcType=INTEGER},
      </if>
      <if test="title != null" >
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="content != null" >
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    
    <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="noticeId">
		SELECT LAST_INSERT_ID() AS noticeId
    </selectKey>
  </insert>
  <update id="update" parameterType="com.vedeng.system.model.Notice" >
    update T_NOTICE
    <set >
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="noticeCategory != null" >
        NOTICE_CATEGORY = #{noticeCategory,jdbcType=INTEGER},
      </if>
      <if test="title != null" >
        TITLE = #{title,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null" >
        IS_ENABLE = #{isEnable,jdbcType=BIT},
      </if>
      <if test="isTop != null" >
        IS_TOP = #{isTop,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="content != null" >
        CONTENT = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where NOTICE_ID = #{noticeId,jdbcType=INTEGER}
  </update>
  
  <resultMap type="com.vedeng.system.model.Notice" id="querylistPageResult" extends="BaseResultMap">
  	<result column="CREATOR_NAME" property="creatorName"/>
  </resultMap>
  <select id="querylistPage"  parameterType="Map" resultMap="querylistPageResult">
  	select
  		a.NOTICE_CATEGORY, a.NOTICE_ID, a.COMPANY_ID, a.TITLE, a.IS_ENABLE, a.IS_TOP, a.ADD_TIME, a.CREATOR, a.MOD_TIME, 
    a.UPDATER,b.USERNAME as CREATOR_NAME
  	from
  		T_NOTICE a
  	left join
  		T_USER b on a.CREATOR = b.USER_ID
  	where
  		1=1
  		<if test="notice.companyId != null" >
        and a.COMPANY_ID = #{notice.companyId,jdbcType=INTEGER}
      </if>
      <if test="notice.noticeCategory != null and notice.noticeCategory != 0" >
        and a.NOTICE_CATEGORY = #{notice.noticeCategory,jdbcType=INTEGER}
      </if>
      <if test="notice.title != null" >
       and a.TITLE like CONCAT('%',#{notice.title},'%' ) 
      </if>
      <if test="notice.isEnable != null and notice.isEnable != -1" >
        and a.IS_ENABLE = #{notice.isEnable,jdbcType=BIT}
      </if>
  	order by 
  		a.IS_TOP desc,a.ADD_TIME desc
  </select>
</mapper>