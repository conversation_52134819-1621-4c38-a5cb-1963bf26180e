package com.vedeng.goods.controller;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.EmptyUtils;
import com.vedeng.finance.dto.HxInvoiceSearchDTO;
import com.vedeng.finance.model.HxInvoice;
import com.vedeng.finance.model.InvoiceDetail;
import com.vedeng.finance.model.InvoiceEntryStash;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.finance.vo.HxInvoiceVo;
import com.vedeng.order.model.dto.BuyorderGoodsRecordDTO;
import com.vedeng.order.model.dto.BuyorderSearchDTO;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.system.model.SysOptionDefinition;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <b>Description:</b><br>
 * 供应链发票管理
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.goods.controller <br>
 * <b>ClassName:</b> SupplyChainController <br>
 * <b>Date:</b> 2020年5月19日 上午11:13:20
 */
@Controller
@RequestMapping("/supplyChain/invoice")
public class SupplyChainController extends BaseController {
    @Autowired
    private InvoiceService invoiceService;

    private final static Logger logger = LoggerFactory.getLogger(SupplyChainController.class);

    /**
     * 供应链管理查询航信推送的进项票列表
     *
     * @param request         请求
     * @param searchDTO       筛选条件
     * @param pageNo          页数
     * @param pageSize        单页显示数
     * @param startAddDateStr 查询开始时间
     * @param endAddDateStr   查询截止时间
     * @return 发票集合
     * @Note <b>Author:</b> hugo <br>
     * <b>Date:</b> 2020年5月21日 上午11:13:20
     */
    @RequestMapping("/hx_invoice_wait")
    public ModelAndView getHxInvoiceByFinance(HttpServletRequest request, HxInvoiceSearchDTO searchDTO,HttpSession session,
                                              @RequestParam(defaultValue = "0") Integer idFlag,
                                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                              @RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                              @RequestParam(required = false, value = "startAddDateStr") String startAddDateStr,
                                              @RequestParam(required = false, value = "endAddDateStr") String endAddDateStr) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        ModelAndView mv = new ModelAndView();

        Page page = getPageTag(request, pageNo, pageSize);
        timeStr2Long(searchDTO, startAddDateStr, endAddDateStr);
        Map<String, Object> map = null;
        try {
            map = invoiceService.getSupplyHxInvoiceWaitListPage(searchDTO, page, idFlag , user);
            mv.addObject("list", map.get("list"));
        } catch (Exception e) {
            logger.error("/supplyChain/invoice/hx_invoice_wait", e);
        }
        mv.addObject("page", map.get("page"));
        mv.addObject("invoiceSearch", searchDTO);
        mv.addObject("startAddDateStr", startAddDateStr);
        mv.addObject("endAddDateStr", endAddDateStr);

        switch (idFlag) {
            case 0: {
                mv.addObject("userList", map.get("userList"));
                mv.setViewName("finance/invoice/hx_invoice/supply_hx_invoice_wait");
            }
            break;
            case 1: {
                mv.addObject("entryUsers", map.get("entryUsers"));
                mv.addObject("invoice", map.get("invoice"));
                mv.setViewName("finance/invoice/hx_invoice/supply_hx_invoice_verifying");
                break;
            }
            case 2: {
                mv.addObject("entryUsers", map.get("entryUsers"));
                mv.addObject("invoice", map.get("invoice"));
                mv.setViewName("finance/invoice/hx_invoice/supply_hx_invoice_verified");
                break;
            }
            case 3: {
                mv.addObject("userList", map.get("userList"));
                mv.addObject("invoice", map.get("invoice"));
                mv.setViewName("finance/invoice/hx_invoice/supply_hx_invoice_wait_return");
                break;
            }
            case 4: {
                mv.addObject("invoice",map.get("invoice"));
                mv.addObject("page",map.get("page"));
                mv.setViewName("finance/invoice/hx_invoice/supply_hx_invoice_invalid");
                break;
            }
            default:
                break;
        }
        return mv;
    }

    /**
     * 搜索添加Long起止时间
     *
     * @param searchDTO
     * @param startAddDateStr
     * @param endAddDateStr
     */
    private void timeStr2Long(HxInvoiceSearchDTO searchDTO, String startAddDateStr, String endAddDateStr) {
        if (EmptyUtils.isNotBlank(startAddDateStr)) {
            searchDTO.setStartTime(DateUtil.convertLong(startAddDateStr + " 00:00:00", DateUtil.TIME_FORMAT));
        }
        if (EmptyUtils.isNotBlank(endAddDateStr)) {
            searchDTO.setEndTime(DateUtil.convertLong(endAddDateStr + " 23:59:59", DateUtil.TIME_FORMAT));
        }
    }

    /**
     * 保存更新航信的发票状态信息
     *
     * @param hxInvoiceId   航信发票的ID
     * @param invoiceStatus 发票的状态
     * @return
     * @Note <b>Author:</b> hugo <br>
     * <b>Date:</b> 2020年5月19日 上午11:13:20
     */
    @ResponseBody
    @RequestMapping("/saveHxInvoiceStatus")
    public ResultInfo saveHxInvoiceStatus(Integer hxInvoiceId, Integer invoiceStatus) {
        logger.info("保存航信发票状态信息 hxInvoiceId:{}" + hxInvoiceId + ",invoiceStatus:{}" + invoiceStatus);
        ResultInfo<Object> resultInfo = new ResultInfo<>();
        try {
            if (invoiceService.saveHxInvoiceStatus(hxInvoiceId, invoiceStatus) > 0) {
                resultInfo.setCode(0);
                resultInfo.setMessage("状态更新成功");
            } else {
                resultInfo.setMessage("操作失败");
            }
        } catch (Exception e) {
            logger.error("/supplyChain/invoice/saveHxInvoiceStatus", e);
        }
        return resultInfo;
    }

    /**
     * @param hxInvoiceId 发票ID
     * @return
     * @describe 供应链航信录票页面初始化
     * <AUTHOR>
     * @date 2020/5/26 15:37:56
     */
    @RequestMapping("/hx_invoice_record_init")
    public ModelAndView getHxInvoiceById(Integer hxInvoiceId) {
        Map<String, Object> result = null;
        try {
            result = invoiceService.getHxInvoiceById(hxInvoiceId);
        } catch (Exception e) {
            logger.error("/supplyChain/invoice/hx_invoice_record_init", e);
        }
        ModelAndView modelAndView = new ModelAndView("finance/invoice/hx_invoice/supply_hx_invoice_record");
        modelAndView.addObject("hxInvoiceVo", result.get("hxInvoiceVo"));
        modelAndView.addObject("hxInvoiceDetailVos", result.get("hxInvoiceDetailVos"));
        return modelAndView;
    }

    /**
     * @param invoiceEntryStashs 录票信息
     * @return
     * @describe 暂存航信录票数据
     * <AUTHOR>
     * @date 2020/5/27 15:58:38
     */
    @ResponseBody
    @RequestMapping(value = "/saveInvoiceEntryStash", consumes = "application/json;charset=utf-8")
    public ResultInfo saveInvoiceEntryStash(HttpSession session, @RequestBody List<InvoiceEntryStash> invoiceEntryStashs) {
        logger.info("航信录票暂存商品信息start invoiceEntryStashs:{}" + JSON.toJSONString(invoiceEntryStashs));
        ResultInfo<Object> resultInfo = new ResultInfo<>();
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        try {
            if (CollectionUtils.isEmpty(invoiceEntryStashs)) {
                resultInfo.setMessage("暂存商品数据不存在!");
                return resultInfo;
            }
            if (invoiceService.getHxInvocieBaseInfoById(invoiceEntryStashs.get(0).getHxInvoiceId()).getInvoiceStatus() != 1) {
                resultInfo.setMessage("暂存失败,只有待录票的发票才可以暂存");
                return resultInfo;
            }
            invoiceEntryStashs.stream().forEach(invoiceEntryStash -> {
                if (invoiceEntryStash.getInvoiceEntryStashId() != null) {
                    updateInvoiceEntryStash(user, invoiceEntryStash);
                } else {
                    saveInvoiceEntryStash(user, invoiceEntryStash);
                }
                resultInfo.setCode(0);
            });
        } catch (Exception e) {
            logger.error("saveInvoiceEntryStash", e);
            resultInfo.setMessage("保存发票暂存信息失败");
        }
        return resultInfo;
    }

    /**
     * @param user              当前登录用户
     * @param invoiceEntryStash 暂存记录
     * @describe 保存发票暂存记录
     * <AUTHOR>
     * @date 2020/6/9 16:57:12
     */
    private void saveInvoiceEntryStash(User user, InvoiceEntryStash invoiceEntryStash) {
        invoiceEntryStash.setCreator(user.getUsername());
        invoiceEntryStash.setCreateTime(System.currentTimeMillis());
        invoiceEntryStash.setIsDelete(0);
        invoiceService.saveInvoiceEntryStash(invoiceEntryStash);
    }

    /**
     * @param user
     * @param invoiceEntryStash
     * @describe 更新发票暂存记录
     * <AUTHOR>
     * @date 2020/6/9 16:57:12
     */
    private void updateInvoiceEntryStash(User user, InvoiceEntryStash invoiceEntryStash) {
        invoiceEntryStash.setUpdater(user.getUsername());
        invoiceEntryStash.setUpdateTime(System.currentTimeMillis());
        invoiceEntryStash.setIsDelete(0);
        invoiceService.updtateInvoiceEntryStash(invoiceEntryStash);
    }

    /**
     * 删除一条航信发票的录票暂存
     *
     * @param invoiceEntryStashId 录票暂存ID
     * @return
     * <AUTHOR>
     * @date 2020/5/28 9:35:20
     */
    @ResponseBody
    @RequestMapping("/deleteInvoiceEntryStash")
    public ResultInfo deleteInvoiceEntryStash(Integer invoiceEntryStashId) {
        return invoiceService.deleteInvoiceEntryStash(invoiceEntryStashId);
    }

    /**
     * @param buyorderSearchDTO
     * @param startAddDateStr
     * @param endAddDateStr
     * @return
     * @describe 初始化录票选择商品页面
     * <AUTHOR>
     * @date 2020/5/28 14:20:20
     */
    @ResponseBody
    @RequestMapping("/supply_hx_invoice_goods_choice")
    public ModelAndView choiceInvoiceDetail(BuyorderSearchDTO buyorderSearchDTO,
                                            @RequestParam(required = false, value = "startAddDateStr") String startAddDateStr,
                                            @RequestParam(required = false, value = "endAddDateStr") String endAddDateStr) {
        ModelAndView modelAndView = new ModelAndView("finance/invoice/hx_invoice/supply_hx_invoice_goods_choice");
        List<BuyorderVo> buyorderList = null;
        HxInvoiceVo hxInvoiceVo = null;
        try {
            //搜索时间处理
            if (EmptyUtils.isNotBlank(startAddDateStr)) {
                buyorderSearchDTO.setStartTime(DateUtil.convertLong(startAddDateStr + " 00:00:00", DateUtil.TIME_FORMAT));
            }
            if (EmptyUtils.isNotBlank(endAddDateStr)) {
                buyorderSearchDTO.setEndTime(DateUtil.convertLong(endAddDateStr + " 23:59:59", DateUtil.TIME_FORMAT));
            }

            //页面数据decode处理
            try {
                if (StringUtils.isNotBlank(buyorderSearchDTO.getSpecification())) {
                    buyorderSearchDTO.setSpecification(URLDecoder.decode(buyorderSearchDTO.getSpecification(), "UTF-8"));
                }
            } catch (UnsupportedEncodingException e) {
                logger.error("产品型号 decode error：", e);
            }
            try {
                if (StringUtils.isNotBlank(buyorderSearchDTO.getSalerName())) {
                    buyorderSearchDTO.setSalerName(URLDecoder.decode(buyorderSearchDTO.getSalerName(), "UTF-8"));
                }
            } catch (UnsupportedEncodingException e) {
                logger.error("供应商 decode error：", e);
            }
            //税率处理
            if (buyorderSearchDTO.getFirstSearch() != null && buyorderSearchDTO.getTaxRate() != null){
                BigDecimal taxRate = new BigDecimal(buyorderSearchDTO.getTaxRate())
                        .divide(new BigDecimal(100))
                        .setScale(2, BigDecimal.ROUND_HALF_DOWN);
                List<SysOptionDefinition> sysOptionDefinitionList = getSysOptionDefinitionList(428);
                for (SysOptionDefinition sysOptionDefinition : sysOptionDefinitionList) {
                    if (new BigDecimal(sysOptionDefinition.getComments()).compareTo(taxRate) == 0) {
                        switch (buyorderSearchDTO.getInvoiceCategory()){
                            case "0" :{
                                //普通发票
                                if (sysOptionDefinition.getTitle().contains("普通")){
                                    //发票类型（字典库）
                                    buyorderSearchDTO.setTaxRate(sysOptionDefinition.getSysOptionDefinitionId());
                                }
                                break;
                            }
                            case "1":{
                                //专用发票
                                if (sysOptionDefinition.getTitle().contains("专用")){
                                    //发票类型（字典库）
                                    buyorderSearchDTO.setTaxRate(sysOptionDefinition.getSysOptionDefinitionId());
                                }
                                break;
                            }
                            default:{
                                logger.warn("航信发票分类匹配错误 hxInvoiceId,invoiceCategory:{}",
                                        buyorderSearchDTO.getHxInvoiceId(),buyorderSearchDTO.getInvoiceCategory());
                                break;
                            }
                        }
                    }

                }

            }

            //条件检索相关订单
            if (buyorderSearchDTO != null &&
                    (StringUtils.isNotBlank(buyorderSearchDTO.getSpecification())
                            || StringUtils.isNotBlank(buyorderSearchDTO.getSalerName())
                            || StringUtils.isNotBlank(buyorderSearchDTO.getGoodsName())
                            || StringUtils.isNotBlank(buyorderSearchDTO.getBrandName())
                            || StringUtils.isNotBlank(buyorderSearchDTO.getBuyorderNo())
                            || buyorderSearchDTO.getTaxRate() != 0
                            || buyorderSearchDTO.getPrice() != null
                            || StringUtils.isNotBlank(startAddDateStr)
                            || StringUtils.isNotBlank(endAddDateStr))) {

                buyorderList = invoiceService.getInvoiceBuyorderList(buyorderSearchDTO);

                //处理采购商品已录票信息
                if (CollectionUtils.isNotEmpty(buyorderList)) {
                   List<Integer> buyorderIds = buyorderList.stream().map(BuyorderVo::getBuyorderId).distinct().collect(Collectors.toList());
                    List<InvoiceDetail> invoiceDetailList = invoiceService.getInvoiceListByRelatedId(buyorderIds);
                    buyorderList.stream().forEach(buyorderVo -> {
                        if (CollectionUtils.isNotEmpty(buyorderVo.getBuyorderGoodsVoList())) {
                            buyorderVo.getBuyorderGoodsVoList().stream().forEach(buyorderGoodsVo -> {
                                invoiceDetailList.stream().forEach(invoiceDetail -> {
                                    if (invoiceDetail.getDetailgoodsId().equals(buyorderGoodsVo.getBuyorderGoodsId())){
                                        BuyorderGoodsRecordDTO buyorderGoodsRecordDTO = new BuyorderGoodsRecordDTO();
                                        buyorderGoodsRecordDTO.setRecordedNum(invoiceDetail.getNum().intValue());
                                        buyorderGoodsRecordDTO.setRecordedAmount(invoiceDetail.getTotalAmount());
                                        buyorderGoodsVo.setBuyorderGoodsRecordDTO(buyorderGoodsRecordDTO);
                                    }
                                });
                            });
                        }
                    });
                }
            }

            hxInvoiceVo = invoiceService.getHxInvoiceInfoByHxInvocieId(buyorderSearchDTO.getHxInvoiceId());
        } catch (Exception e) {
            logger.error("supply_hx_invoice_goods_choice", e);
        }
        modelAndView.addObject("buyorderSearchDTO", buyorderSearchDTO);
        modelAndView.addObject("hxInvoiceVo", hxInvoiceVo);
        modelAndView.addObject("startAddDateStr", startAddDateStr);
        modelAndView.addObject("endAddDateStr", endAddDateStr);
        modelAndView.addObject("buyorderList", buyorderList);
        return modelAndView;
    }

    /**
     * @param invoiceEntryStashs 录票信息
     * @param session
     * @return
     * @describe 保存航信录票数据
     * <AUTHOR>
     * @date 2020/6/1 10:40:38
     */
    @ResponseBody
    @RequestMapping("/saveHxInvoiceDetail")
    public ResultInfo saveHxInvoiceDetail(HttpSession session, @RequestBody List<InvoiceEntryStash> invoiceEntryStashs) {
        logger.info("保存航信录票数据 invoiceEntryStashs:{}" + JSON.toJSONString(invoiceEntryStashs));
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        return invoiceService.saveHxInvoiceDetails(user, invoiceEntryStashs);
    }

    /**
     * @param hxInvoiceId 发票ID
     * @return
     * @describe 清空当前发票的已录票暂存信息
     * <AUTHOR>
     * @date 2020/6/1 15:31:02
     */
    @ResponseBody
    @RequestMapping("/deleteInvoiceDetails")
    public ResultInfo deleteInvoiceDetails(@RequestParam("hxInvoiceId") Integer hxInvoiceId) {
        logger.info("清空当前发票的已录票暂存信息  hxInvoiceId:{}" + hxInvoiceId);
        return invoiceService.deleteInvoiceDetails(hxInvoiceId);
    }

    /**
     * @param hxInvoice
     * @return
     * @describe 标记航信发票的退票处理状态
     * <AUTHOR>
     * @date 2020/6/9 15:50:02
     */
    @ResponseBody
    @RequestMapping("/updateInvoiceRefundStatus")
    public ResultInfo updateInvoiceRefundStatus(HxInvoice hxInvoice) {
        logger.info("标记航信发票的退票处理状态 hxInvoice:{}" + JSON.toJSONString(hxInvoice));
        ResultInfo<Object> resultInfo = new ResultInfo<>();
        try {
            invoiceService.updateInvoiceRefundStatus(hxInvoice);
            resultInfo.setCode(0);
        } catch (Exception e) {
            resultInfo.setMessage("标记航信发票的退票处理状态失败");
            logger.error("updateInvoiceRefundStatus", e);
        }
        return resultInfo;
    }

    /**
     * @param hxInvoiceIds  航信发票的ID
     * @param invoiceStatus 发票的状态
     * @return
     * @describe 批量保存更新航信的发票状态信息
     * @Note <b>Author:</b> hugo <br>
     * <b>Date:</b> 2020年6月17日 下午20:00:20
     */
    @ResponseBody
    @RequestMapping("/batchSaveHxInvoiceStatus")
    public ResultInfo batchSaveHxInvoiceStatus(String hxInvoiceIds, Integer invoiceStatus) {
        logger.info("批量保存更新航信的发票状态信息 hxInvoiceIds:{}" + hxInvoiceIds + ",invoiceStatus:{}" + invoiceStatus);
        ResultInfo<Object> resultInfo = new ResultInfo<>();
        try {
            ArrayList<Integer> hxInvoiceIdParams = new ArrayList<>();
            Arrays.stream(hxInvoiceIds.split("&")).forEach(hxInvoiceId -> {
                hxInvoiceIdParams.add(Integer.parseInt(hxInvoiceId));
            });
            if (invoiceService.batchSaveHxInvoiceStatus(hxInvoiceIdParams, invoiceStatus) <= 0) {
                resultInfo.setMessage("操作失败");
                return resultInfo;
            }
            resultInfo.setCode(0);
            resultInfo.setMessage("状态更新成功");
        } catch (Exception e) {
            logger.error("/supplyChain/invoice/batchSaveHxInvoiceStatus", e);
        }
        return resultInfo;
    }

    /**
     * @param filePath
     * @param response
     * @param isOnLine
     * @param fname
     * @throws IOException
     * @describe 查看下载pdf文件
     */
    @RequestMapping("/viewAndDownloadInvoice")
    public void viewAndDownloadInvoice(String filePath, HttpServletResponse response,
                                       boolean isOnLine, String fname) {
        try {
            File f = new File(filePath);
            if (!f.exists()) {
                response.sendError(404, "File not found!");
                return;
            }
            BufferedInputStream br = new BufferedInputStream(new FileInputStream(f));
            byte[] bs = new byte[1024];
            int len = 0;
            response.reset();
            if (isOnLine) {
                URL u = new URL("file:///" + filePath);
                String contentType = u.openConnection().getContentType();
                response.setContentType(contentType);
                response.setHeader("Content-Disposition", "inline;filename="
                        + fname);
            } else {
                response.setContentType("application/x-msdownload");
                response.setHeader("Content-Disposition", "attachment;filename="
                        + fname);
            }
            OutputStream out = response.getOutputStream();
            while ((len = br.read(bs)) > 0) {
                out.write(bs, 0, len);
            }
            out.flush();
            out.close();
            br.close();
        } catch (IOException e) {
            logger.error("viewAndDownloadInvoice", e);
        }
    }
}
