<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:apollo="http://www.ctrip.com/schema/apollo" xmlns:rabbit="http://www.springframework.org/schema/rabbit"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.ctrip.com/schema/apollo http://www.ctrip.com/schema/apollo.xsd
		http://www.springframework.org/schema/rabbit http://www.springframework.org/schema/rabbit/spring-rabbit.xsd">

    <!--rabbit连接-->
    <rabbit:connection-factory
            id="ImConnectionFactory"
            addresses="${im.rabbitmq.host}"
            username="${im.rabbitmq.username}"
            password="${im.rabbitmq.password}"
            virtual-host="${im.rabbitmq.virtualHost}"
            channel-cache-size="100"
            publisher-returns="true"
            publisher-confirms="true" />

    <!-- 定义mq管理 -->
    <rabbit:admin connection-factory="ImConnectionFactory" />

    <rabbit:queue name="erpTraderChangeQueue" durable="true" auto-delete="false" exclusive="false" />
    <rabbit:queue name="erpUserStateChangeQueue" durable="true" auto-delete="false" exclusive="false" />
    <rabbit:queue name="erpUserOrgChangeQueue" durable="true" auto-delete="false" exclusive="false" />
    <rabbit:queue name="notCreateBcReasonQueue" durable="true" auto-delete="false" exclusive="false" />
    <rabbit:queue name="IM_TRADER_LABEL_QUEUE" durable="true" auto-delete="false" exclusive="false" />

    <rabbit:template id="imRabbitTemplate"
                     connection-factory="ImConnectionFactory" message-converter="messageConverter" />
    <rabbit:direct-exchange name="ERPTraderServiceExchange" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="erpTraderChangeQueue" key="ERPTraderRoutingkey" />
            <rabbit:binding queue="erpUserStateChangeQueue" key="ERPUserRoutingkey" />
            <rabbit:binding queue="erpUserOrgChangeQueue" key="UserOrgRoutingkey" />
        </rabbit:bindings>
    </rabbit:direct-exchange>

    <rabbit:direct-exchange name="not_create_bc_reason_exchange" durable="true" auto-delete="false">
        <rabbit:bindings>
            <rabbit:binding queue="notCreateBcReasonQueue" key="notCreateBcReasonRoutingKey" />
        </rabbit:bindings>
    </rabbit:direct-exchange>
    <!--定义queue  说明：durable:是否持久化 exclusive: 仅创建者可以使用的私有队列，断开后自动删除 auto_delete: 当所有消费客户端连接断开后，是否自动删除队列-->
    <rabbit:listener-container connection-factory="ImConnectionFactory" acknowledge="manual" failed-declaration-retry-interval="${failed-declaration-retry-interval}"
                               declaration-retries="${declaration-retries}">
        <rabbit:listener  queues="IM_TRADER_LABEL_QUEUE"  ref="traderLabelConsumer"/>
        <rabbit:listener  queues="notCreateBcReasonQueue"  ref="notCreateBcReasonConsumer"/>
    </rabbit:listener-container>

</beans>