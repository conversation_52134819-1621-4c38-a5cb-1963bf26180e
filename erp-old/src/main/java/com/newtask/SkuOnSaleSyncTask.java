package com.newtask;


import com.alibaba.fastjson.JSONObject;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.op.api.dto.base.PageBaseRequest;
import com.vedeng.op.api.dto.sku.SkuOnSaleDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@JobHandler(value = "skuOnSaleSyncTask")
public class SkuOnSaleSyncTask extends AbstractJobHandler {
    private Logger logger= LoggerFactory.getLogger(SkuOnSaleSyncTask.class);
    @Value("${operate_url}")
    public String operateUrl;
    @Autowired
    private VgoodsService vgoodsService;
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        PageBaseRequest request=new PageBaseRequest();
        int currentPage= ErpConst.ONE;
        int maxPage=ErpConst.ONE;

        request.setPageSize(GoodsConstants.MAX_PAGE_SIZE);
        do{
            request.setPageNo(currentPage);
            SkuOnSaleSyncTaskPage pageInfo=getSkuOnSaleListFromOp(request);
            if(pageInfo==null){
                return ReturnT.SUCCESS;
            }
            maxPage=pageInfo.getPages();
            //pageInfo.get
            currentPage++;
            List<SkuOnSaleDto> list=pageInfo.getList();
            XxlJobLogger.log("处理第"+currentPage+"页，共计"+maxPage+"页");
            solveListOnSale(list);
        }while (currentPage<=maxPage);
            return ReturnT.SUCCESS;
    }

    private SkuOnSaleSyncTaskPage<SkuOnSaleDto> getSkuOnSaleListFromOp(PageBaseRequest request){
        logger.info("请求op的sku上架入参{}", JSONObject.toJSONString(request));
        try{
            String url = operateUrl + "/op/getGoodsOnSale";
            com.alibaba.fastjson.TypeReference<RestfulResult<SkuOnSaleSyncTaskPage<SkuOnSaleDto>>> typeReference = new com.alibaba.fastjson.TypeReference<RestfulResult<SkuOnSaleSyncTaskPage<SkuOnSaleDto>>>() {
            };
            logger.info("请求op的sku上架请求体{}", JSONObject.toJSONString(typeReference));
            RestfulResult<SkuOnSaleSyncTaskPage<SkuOnSaleDto>> restfulResult = HttpRestClientUtil.restPost(url, typeReference, null, request);
            logger.info("请求op的sku上架返回体{}", JSONObject.toJSONString(restfulResult));
            if(restfulResult.isSuccess()){
                return restfulResult.getData();
            }
        }catch (Exception ex){
            logger.error("请求op的sku上架信息出错",ex);
            XxlJobLogger.log("请求op的sku上架信息出错",ex);
        }
        return null;
    }

    private void solveListOnSale(List<SkuOnSaleDto> list){
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        for(SkuOnSaleDto sku:list){
            if(sku==null){
                continue;
            }
            vgoodsService.updateSkuOnSale(sku);
            vgoodsService.updateSkuPushStatus(sku);
        }
    }
}
