var sendFlag = false;

function submit(afterSalesId) {
    if (sendFlag) {
        return false;
    }
    sendFlag = true;
    debugger;
    if ($("input[type=checkbox]:checked").length == 0) {
        layer.alert("未选择商品");
        sendFlag = false;
        return false;
    }

    var flag = false;
    $("input[type=checkbox]:checked").each(function () {
        var dom = $(this).parent().parent().find("input[name='maxOutNum']").first();
        if (dom.val() <= 0 || dom.val() > dom.attr("data-maxNum")) {
            flag = true
        }
    });
    if (flag) {
        layer.alert("入库数量填写有误");
        sendFlag = false;
        return false;
    }
    debugger;
    var list = new Array();
    $("input[type=checkbox]:checked").each(function () {
        var logObj = {};
        var parent = $(this).parent().parent();
        logObj.afterSalesId = parent.find("input[name='afterSalesId']").first().val();
        logObj.afterSalesGoodsId = parent.find("input[name='afterSalesGoodsId']").first().val();
        logObj.sku = parent.find("input[name='sku']").first().val();
        logObj.num = parent.find("input[name='maxOutNum']").first().val();
        logObj.produceTimeStr = parent.find("input[name='produceTime']").first().val();
        logObj.validTimeStr = parent.find("input[name='validTime']").first().val();
        logObj.outTimeStr = parent.find("input[name='outTime']").first().val();
        logObj.industryBatchNumber = parent.find("input[name='industryBatchNumber']").first().val();
        logObj.sterilizationNumber = parent.find("input[name='sterilizationNumber']").first().val();
        logObj.showName = parent.find("input[name='showName']").first().val();
        logObj.goodsId = parent.find("input[name='goodsId']").first().val();
        logObj.brandName = parent.find("input[name='brandName']").first().val();
        logObj.model = parent.find("input[name='model']").first().val();
        logObj.spec = parent.find("input[name='spec']").first().val();
        logObj.unitName = parent.find("input[name='unitName']").first().val();
        logObj.firstEngageId = parent.find("input[name='firstEngageId']").first().val();
        logObj.registrationNumber = parent.find("input[name='registrationNumber']").first().val();
        logObj.vedengBatchNum = parent.find("input[name='vedengBatchNum']").first().val();
        logObj.afterSalesId = afterSalesId;
        list.push(logObj);
    });
    console.log(list);

    $.ajax({
        url: page_url + '/order/newBuyorder/saveDirectWarehouse.do',
        data: JSON.stringify(eval(list)),
        type: "POST",
        dataType: "json",
        contentType: "application/json;charset=utf-8",
        async: false,
        success: function (data) {
            if (data.code == 0) {
                window.parent.location.reload();
            } else {
                layer.alert(data.message);
            }

        },
        error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
    sendFlag = false;
    return false;

}