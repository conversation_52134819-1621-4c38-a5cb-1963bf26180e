package com.vedeng.crm.business.quote.domain.entity;

import java.math.BigDecimal;
import lombok.Data;

/**
    * 报价产品
    */
@Data
public class CrmQuoteorderGoods {

    /**
    * 主键ID
    */
    private Integer quoteorderGoodsId;

    /**
    * 报价ID
    */
    private Integer quoteorderId;

    /**
    * 是否临时产品 0否 1是
    */
    private Integer isTemp;

    /**
    * 商品ID
    */
    private Integer goodsId;

    /**
    * 唯一编码
    */
    private String sku;

    /**
    * 商品名称
    */
    private String goodsName;

    /**
    * 品牌名称
    */
    private String brandName;

    /**
    * 商品型号
    */
    private String model;

    /**
    * 中文名
    */
    private String unitName;

    /**
    * 单价
    */
    private BigDecimal price;

    /**
    * 货币单位ID
    */
    private Integer currencyUnitId;

    /**
    * 数量
    */
    private Integer num;

    /**
    * 货期
    */
    private String deliveryCycle;

    /**
    * 是否直发 0否 1是
    */
    private Integer deliveryDirect;

    /**
    * 直发备注
    */
    private String deliveryDirectComments;

    /**
    * 注册证号
    */
    private String registrationNumber;

    /**
    * 供应商名称
    */
    private String supplierName;

    /**
    * 参考成本
    */
    private BigDecimal referenceCostPrice;

    /**
    * 参考价格（产品部门报价）
    */
    private String referencePrice;

    /**
    * 参考货期（产品部门回复）
    */
    private String referenceDeliveryCycle;

    /**
    * 报备结果
     * 0不需要报备 1等待报备 2成功 3失败
    */
    private Integer reportStatus;

    /**
    * 报备原因
    */
    private String reportComments;

    /**
    * 是否包含安调 0否 1是
    */
    private Integer haveInstallation;

    /**
    * 产品备注
    */
    private String goodsComments;

    /**
    * 内部备注
    */
    private String insideComments;

    /**
    * 是否删除0否1是
    */
    private Integer isDelete;

    /**
    * 添加时间
    */
    private Long addTime;

    /**
    * 添加人
    */
    private Integer creator;

    /**
    * 最近一次编辑时间
    */
    private Long modTime;

    /**
    * 最近一次编辑人
    */
    private Integer updater;

    /**
    * 上次回复价格的用户ID
    */
    private Integer lastReferenceUser;

    /**
    * 是否需要人工回复 0否 1是
    */
    private Integer isNeedReply;

    /**
     * 是否咨询了报备：1是 0否
     */
    private Integer isConsulDeliveryCycle;

    /**
     * 是否咨询了：1是 0否
     */
    private Integer isConsulPrice;

    /**
     * 是否咨询了报备：1是 0否
     */
    private Integer isConsulReport;

    private Integer brandId;

    private String imgUrl;

    private String paramContent;


    /**
     * SKU的审核状态，取自V_CORE_SKU里的checkStatus字段
     */
    private Integer checkStatus;
}
