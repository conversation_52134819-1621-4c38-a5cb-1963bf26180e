package com.vedeng.firstengage.model;

public class FirstEngageSearchInfo {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE_SEARCH_INFO.FIRST_ENGAGE_SEARCH_INFO_ID
     *
     * @mbg.generated Wed Mar 20 18:39:49 CST 2019
     */
    private Integer firstEngageSearchInfoId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE_SEARCH_INFO.USER_ID
     *
     * @mbg.generated Wed Mar 20 18:39:49 CST 2019
     */
    private Integer userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_FIRST_ENGAGE_SEARCH_INFO.CONTENT
     *
     * @mbg.generated Wed Mar 20 18:39:49 CST 2019
     */
    private String content;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE_SEARCH_INFO.FIRST_ENGAGE_SEARCH_INFO_ID
     *
     * @return the value of T_FIRST_ENGAGE_SEARCH_INFO.FIRST_ENGAGE_SEARCH_INFO_ID
     *
     * @mbg.generated Wed Mar 20 18:39:49 CST 2019
     */
    public Integer getFirstEngageSearchInfoId() {
        return firstEngageSearchInfoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE_SEARCH_INFO.FIRST_ENGAGE_SEARCH_INFO_ID
     *
     * @param firstEngageSearchInfoId the value for T_FIRST_ENGAGE_SEARCH_INFO.FIRST_ENGAGE_SEARCH_INFO_ID
     *
     * @mbg.generated Wed Mar 20 18:39:49 CST 2019
     */
    public void setFirstEngageSearchInfoId(Integer firstEngageSearchInfoId) {
        this.firstEngageSearchInfoId = firstEngageSearchInfoId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE_SEARCH_INFO.USER_ID
     *
     * @return the value of T_FIRST_ENGAGE_SEARCH_INFO.USER_ID
     *
     * @mbg.generated Wed Mar 20 18:39:49 CST 2019
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE_SEARCH_INFO.USER_ID
     *
     * @param userId the value for T_FIRST_ENGAGE_SEARCH_INFO.USER_ID
     *
     * @mbg.generated Wed Mar 20 18:39:49 CST 2019
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_FIRST_ENGAGE_SEARCH_INFO.CONTENT
     *
     * @return the value of T_FIRST_ENGAGE_SEARCH_INFO.CONTENT
     *
     * @mbg.generated Wed Mar 20 18:39:49 CST 2019
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_FIRST_ENGAGE_SEARCH_INFO.CONTENT
     *
     * @param content the value for T_FIRST_ENGAGE_SEARCH_INFO.CONTENT
     *
     * @mbg.generated Wed Mar 20 18:39:49 CST 2019
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }
}