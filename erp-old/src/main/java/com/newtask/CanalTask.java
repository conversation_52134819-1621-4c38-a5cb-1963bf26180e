package com.newtask;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.otter.canal.client.CanalConnector;
import com.alibaba.otter.canal.client.CanalConnectors;
import com.alibaba.otter.canal.protocol.Message;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.kpi.canal.sync.SyncDatabase;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.InetSocketAddress;
import java.util.Collections;

/**
 * <AUTHOR>
 * @date created in 2020/6/9 11:16
 */
@Component
@JobHandler(value="CanalTask")
public class CanalTask extends AbstractJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(CanalTask.class);

    @Value("${canal.server}")
    private String canalServer;

    @Value("${canal.port}")
    private Integer canalPort;

    @Value("${canal.destination}")
    private String canalDestination;

    @Value("${canal.subscribe}")
    private String canalSubscribe;

    /**
     * 连接重试时间间隔。默认5秒
     */
    private Integer retryInterval = 5000;

    @Resource
    private SyncDatabase syncDatabase;


    public ReturnT<String> doExecute(String param) throws Exception {
        LOGGER.info("CanalTask start");
        long skipBatchId = 0L;
        if (StringUtils.isNotBlank(param)){
            skipBatchId = Long.parseLong(param);
        }
        CanalConnector connector = CanalConnectors.newClusterConnector(Collections.singletonList(new InetSocketAddress(canalServer,canalPort)),canalDestination,"","");
        try {
            connector.connect();
            connector.subscribe(canalSubscribe);
            LOGGER.info("canal client启动成功...");
            while (true) {
                LOGGER.info("canal client start do while...");
                connector.rollback();
                Message message = connector.getWithoutAck(1);
                long batchId = message.getId();
                int size = message.getEntries().size();
                if (batchId == -1 || size == 0) {
                    Thread.sleep(5000);
                } else {
                    try {
                        LOGGER.info("canal client receiveCanalEntries ...");
                        String result= syncDatabase.receiveCanalEntries(message);
                        LOGGER.info("canal client receiveCanalEntries ...,{}",new Object[]{result});
                        connector.ack(batchId);
                        if(StringUtils.isNotBlank(result)){
                            XxlJobLogger.log(result);
                            return ReturnT.FAIL;
                        }
                    } catch (Exception e){
                        LOGGER.error("canal同步数据失败，batchId：{}，数据：{}，错误：",batchId, JSONObject.toJSONString(message),e);
                        XxlJobLogger.log("canal同步数据失败，batchId：{}，数据：{}",batchId,JSONObject.toJSONString(message),e);
                        if (batchId == skipBatchId){
                            connector.ack(batchId);
                        } else {
                            connector.rollback(batchId);
                        }
                        return ReturnT.FAIL;
                    }
                }
            }
        } catch (Exception e){
            LOGGER.error("canal连接异常：",e);
            XxlJobLogger.log("canal连接异常",e);
            return ReturnT.FAIL;
        }
        finally {
            connector.disconnect();
        }

    }
}
