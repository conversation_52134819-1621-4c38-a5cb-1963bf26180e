package com.vedeng.mobile.query;

import lombok.Data;

import java.util.List;


/**
 * 销售订单列表查询类
 */
@Data
public class SaleorderListQuery {

    /**
     * 当前登录用户的userId
     */
    private Integer userId;

    /**
     * 搜索关键字(订单号或客户名称)
     */
    private String searchKey;

    /**
     * 订单状态筛选(0待确认、1待审核、2待收款、3待发货、4待收货、5待开票、6已完结、7已关闭)
     */
    private List<Integer> orderStatusList;

    /**
     * 页数
     */
    private Integer pageNum = 1;

    /**
     * 每页条数
     */
    private Integer pageSize = 10;

}
