<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>参考成本列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="supplier_orderlist1" datasource="erp-datasourcetarget" fixednumber="2" fixednumberright="1" append_column_url="" append_row_url="" empty_show="" firstcol="checkbox">
<div id="appendHead"></div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline ">
            <label class="layui-form-label">订单号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="SALEORDER_NO" placeholder style alias="B" jdbctype data datatype oper validate_rules validate_messages>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">订货号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="SKU" placeholder style alias="A" jdbctype data datatype oper validate_rules validate_messages>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">商品名称</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="GOODS_NAME" placeholder style="" alias="A" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages="">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">品牌</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="BRAND_ID" placeholder style alias="P" jdbctype data="SELECT BRAND_ID AS `K`,BRAND_NAME AS `V`  FROM T_BRAND WHERE COMPANY_ID=1 AND IS_DELETE=0" datatype="KVSQLCACHE" oper validate_rules validate_messages> </select>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">规格型号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="" placeholder style alias="CONCAT(A.MODEL, '/', A.SPEC)" jdbctype data datatype oper validate_rules validate_messages>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">填写成本</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="REFERENCE_COST_PRICE_FLAG" placeholder style alias="IF(A.REFERENCE_COST_PRICE>0,1, 0)" jdbctype data="yesno" datatype oper validate_rules validate_messages> </select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">单位</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="UNIT_NAME" placeholder style alias="A" jdbctype data datatype oper validate_rules validate_messages>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">产品助理</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="ASSIGNMENT_ASSISTANT_ID" placeholder style alias="P" jdbctype data="select USER_ID K,USERNAME V FROM T_USER" datatype="KVSQLCACHE" oper validate_rules validate_messages> </select>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">产品经理</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="ASSIGNMENT_MANAGER_ID" placeholder style alias="P" jdbctype data="select USER_ID K,USERNAME V FROM T_USER" datatype="KVSQLCACHE" oper validate_rules validate_messages> </select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">生效时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="VALID_TIME" placeholder style alias="FROM_UNIXTIME(B.VALID_TIME / 1000, '%Y-%m-%d %H:%i:%s')" jdbctype="DATETIME" data datatype oper="BETWEEN" validate_rules validate_messages> </object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">付款时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="PAYMENT_TIME" placeholder style alias="FROM_UNIXTIME(B.PAYMENT_TIME / 1000, '%Y-%m-%d %H:%i:%s')" jdbctype="DATETIME" data datatype oper="BETWEEN" validate_rules validate_messages> </object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">可发货时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="SATISFY_DELIVERY_TIME" placeholder style alias="FROM_UNIXTIME(B.SATISFY_DELIVERY_TIME / 1000, '%Y-%m-%d %H:%i:%s')" jdbctype="DATETIME" data datatype oper="BETWEEN" validate_rules validate_messages> </object>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th type="rowbutton" id="rowbutton"><button item_name="修改成本" name="修改成本" url="/ezadmin/form/form-update_costprice?ID=${ID}" opentype="MODEL" windowname="修改参考成本" style pre="" ez_session_user_name_key="hank.he" encrypt_list_id="supplier_orderlist" item_label="修改成本" open_type="MODEL" item_url="/ezadmin/form/form-update_costprice?ID=${ID}" window_name="修改参考成本" area="" plugin_code="button-single" ez_session_user_id_key="359" id="修改成本" type="single">修改成本</button></th>
            <th item_name="SKU" name="SKU" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">订货号</th>
            <th item_name="SALEORDER_NO" name="SALEORDER_NO" body="td-link" order="" url="/order/saleorder/view.do?saleorderId=${SALEORDER_ID}&amp;scene=0" opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th">订单号</th>
            <th item_name="GOODS_NAME" name="GOODS_NAME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">商品名称</th>
            <th item_name="BRAND_ID" name="BRAND_ID" body="td-select" order="" url="" opentype="" windowname="" datatype="KVSQLCACHE" data="SELECT BRAND_ID K,BRAND_NAME V FROM T_BRAND WHERE COMPANY_ID=1" style="min-width:100px;padding:0px 10px;" jdbctype="" head="th" edit_flag="0" edit_express="" edit_plugin="">品牌名称</th>
            <th item_name="MODELSPEC" name="MODELSPEC" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">规格型号</th>
            <th item_name="PRICE" name="PRICE" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">单价</th>
            <th item_name="REAL_PRICE" name="REAL_PRICE" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">原单价</th>
            <th item_name="REFERENCE_COST_PRICE" name="REFERENCE_COST_PRICE" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="min-width:100px;padding:0px 10px;" jdbctype="" head="th" edit_flag="1" edit_express="/ezadmin/form/doSubmit-update_costprice" edit_plugin="input-text">参考成本</th>
            <th item_name="NUM" name="NUM" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">数量</th>
            <th item_name="UNIT_NAME" name="UNIT_NAME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">单位</th>
            <th item_name="MAX_SKU_REFUND_AMOUNT" name="MAX_SKU_REFUND_AMOUNT" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">总额</th>
            <th item_name="ASSIGNMENT_ASSISTANT_ID" name="ASSIGNMENT_ASSISTANT_ID" body="td-select" order="" url="" opentype="" windowname="" data="select USER_ID K,USERNAME V FROM T_USER" datatype="KVSQLCACHE" style="" jdbctype="" head="th">产品助理</th>
            <th item_name="ASSIGNMENT_MANAGER_ID" name="ASSIGNMENT_MANAGER_ID" body="td-select" order="" url="" opentype="" windowname="" data="select USER_ID K,USERNAME V FROM T_USER" datatype="KVSQLCACHE" style="" jdbctype="" head="th">产品经理</th>
            <th item_name="VALID_TIME" name="VALID_TIME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">生效时间</th>
            <th item_name="PAYMENT_TIME" name="PAYMENT_TIME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">付款时间</th>
            <th item_name="SATISFY_DELIVERY_TIME" name="SATISFY_DELIVERY_TIME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">可发货时间</th>
            <th item_name="MAOLI" name="MAOLI" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th">毛利率</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td> express:</td>
            <td colspan="100"><pre id="express" class="layui-code" orderby="ORDER BY         B.VALID_TIME DESC" groupby="">StringBuilder sql=new StringBuilder();

sql.append("
    SELECT
        B.SALEORDER_NO,A.SALEORDER_ID,
        A.SALEORDER_GOODS_ID ID,
        A.SKU,
        A.GOODS_NAME,P.BRAND_ID,
        A.BRAND_NAME,
        CONCAT(A.MODEL,
        '/',
        A.SPEC) MODELSPEC,
        A.PRICE,
        A.REAL_PRICE,
        IFNULL(A.REFERENCE_COST_PRICE,
        0) REFERENCE_COST_PRICE,
          IF(A.REFERENCE_COST_PRICE&gt;0,1, 0) REFERENCE_COST_PRICE_FLAG,
        A.NUM,
        A.UNIT_NAME,
        A.MAX_SKU_REFUND_AMOUNT,
        P.ASSIGNMENT_ASSISTANT_ID,
        P.ASSIGNMENT_MANAGER_ID,
        FROM_UNIXTIME(B.VALID_TIME / 1000,
        '%Y-%m-%d %H:%i:%s') VALID_TIME,
        FROM_UNIXTIME(B.PAYMENT_TIME / 1000,
        '%Y-%m-%d %H:%i:%s') PAYMENT_TIME,
        FROM_UNIXTIME(B.SATISFY_DELIVERY_TIME / 1000,
        '%Y-%m-%d %H:%i:%s') SATISFY_DELIVERY_TIME,
        if(A.PRICE = 0,
        '0%',
        concat(round((A.PRICE - IFNULL(A.REFERENCE_COST_PRICE,
        0)) / A.PRICE * 100,
        2),
        '%')) MAOLI
    FROM
       T_SALEORDER B
    LEFT JOIN
        T_SALEORDER_GOODS A
            ON A.SALEORDER_ID = B.SALEORDER_ID
    LEFT JOIN
        V_CORE_SKU K
            ON K.SKU_NO = A.SKU
    LEFT JOIN
        V_CORE_SPU P
            ON P.SPU_ID = K.SPU_ID
    WHERE
        A.IS_DELETE = 0
        AND B.STATUS != 3
        and B.SALEORDER_ID &gt; 251209
        AND B.SATISFY_DELIVERY_TIME / 1000 &gt; unix_timestamp() - 60 * 24 * 60 * 60
    ");
 return search(sql);</pre></td>
        </tr>
        <tr>
            <td> count:</td>
            <td colspan="100"><pre id="count" class="layui-code"></pre></td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot"></div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>