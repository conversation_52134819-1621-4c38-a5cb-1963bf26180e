package com.vedeng.mobile.system.service;

import com.vedeng.mobile.system.dto.RegionDto;
import com.vedeng.mobile.system.dto.RegionForMobile;
import com.vedeng.mobile.system.dto.RegionInnerDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2022/1/10 17:18
 */
public interface RegionService {

    /**
     * 省市区三级联动json
     * @return 省市区三级联动json
     */
    RegionDto queryAllRegions();

    RegionInnerDto queryRegionByThreeId(Integer regionId);

    RegionForMobile queryRegionLevelTwo(@Param("parentId") Integer topRegionId);

    RegionForMobile queryRegionByParentId(@Param("parentId") Integer topRegionId);


}
