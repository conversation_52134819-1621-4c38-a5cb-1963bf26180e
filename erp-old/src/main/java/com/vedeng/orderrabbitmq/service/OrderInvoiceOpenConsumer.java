package com.vedeng.orderrabbitmq.service;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodOverdueManageDetailTypeEnum;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.finance.model.Invoice;
import com.vedeng.order.model.dto.OrderInvoiceOpenDto;
import com.vedeng.trader.dao.PeriodUseNodeRecordMapper;
import com.vedeng.trader.model.po.PeriodUseNodeRecordPo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @desc 开取发票生成账期分类编码消费者
 */
@Component
public class OrderInvoiceOpenConsumer extends AbstractMessageListener {
    private final static Logger logger = LoggerFactory.getLogger(ChannelAwareMessageListener.class);

    @Resource
    private PeriodUseNodeRecordMapper periodUseNodeRecordMapper;

    @Resource
    private InvoiceMapper invoiceMapper;

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("OrderInvoiceOpenConsumer start messageBody:{}", messageBody);

        try {
            //存在db事务还未提交，mq已经消费到消息，查询不到发票数据
            Thread.sleep(20000);
            OrderInvoiceOpenDto orderInvoiceOpenDto = JSON.parseObject(messageBody, OrderInvoiceOpenDto.class);

            PeriodUseNodeRecordPo periodUseNodeRecordPo = new PeriodUseNodeRecordPo();

            periodUseNodeRecordPo.setOrderType(CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_INVOICE.getCode());
            periodUseNodeRecordPo.setOrderId(orderInvoiceOpenDto.getSaleOrderId().longValue());
            periodUseNodeRecordPo.setRelatedId(orderInvoiceOpenDto.getRelatedId().longValue());
            periodUseNodeRecordPo.setAddTime(System.currentTimeMillis());
            periodUseNodeRecordPo.setCreator(ErpConst.ONE);
            logger.info("生成账期逾期编码为开票类型 invoiceId:{}", orderInvoiceOpenDto.getRelatedId());
            Invoice invoiceInfo = invoiceMapper.getInvoiceBaseInfoByInvoiceId(orderInvoiceOpenDto.getRelatedId());
            if (invoiceInfo == null) {
                logger.error("开票生成账期逾期编码时发票信息不存在 error invoiceId:{}", orderInvoiceOpenDto.getRelatedId());
                throw new CustomerBillPeriodException("开票生成账期逾期编码时发票信息不存在 invoiceId:" + orderInvoiceOpenDto.getRelatedId());
            }
            periodUseNodeRecordPo.setAmount(invoiceInfo.getAmount());
            periodUseNodeRecordMapper.insertRecord(periodUseNodeRecordPo);

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            logger.error("消息推送开取发票记录账期使用节点记录 error", e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }
}
