package com.vedeng.order.dao;

import java.util.List;
import java.util.Map;

//import com.meinian.model.vo.SendData;
import org.apache.ibatis.annotations.Param;

import com.vedeng.order.model.adk.AdkLog;
import com.vedeng.order.model.adk.AdkLogExample;

public interface AdkLogMapper {
	List<Integer> selectAllDelivedOrder();

	List<Map<String, Object>> selectGoodsOutBefore30Min(@Param("orderId") Integer orderId);

//	List<SendData> selectAllMeinianByNo(@Param("mNos") List<String> mNos);
//
//	List<SendData> selectAllMeinianDeliveryStatus();

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_ADK_LOG
	 *
	 * @mbg.generated Fri Apr 26 14:35:48 CST 2019
	 */
	long countByExample(AdkLogExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_ADK_LOG
	 *
	 * @mbg.generated Fri Apr 26 14:35:48 CST 2019
	 */
	int deleteByExample(AdkLogExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_ADK_LOG
	 *
	 * @mbg.generated Fri Apr 26 14:35:48 CST 2019
	 */
	int deleteByPrimaryKey(Integer adkLogId);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_ADK_LOG
	 *
	 * @mbg.generated Fri Apr 26 14:35:48 CST 2019
	 */
	int insert(AdkLog record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_ADK_LOG
	 *
	 * @mbg.generated Fri Apr 26 14:35:48 CST 2019
	 */
	int insertSelective(AdkLog record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_ADK_LOG
	 *
	 * @mbg.generated Fri Apr 26 14:35:48 CST 2019
	 */
	List<AdkLog> selectByExampleWithBLOBs(AdkLogExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_ADK_LOG
	 *
	 * @mbg.generated Fri Apr 26 14:35:48 CST 2019
	 */
	List<AdkLog> selectByExample(AdkLogExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_ADK_LOG
	 *
	 * @mbg.generated Fri Apr 26 14:35:48 CST 2019
	 */
	AdkLog selectByPrimaryKey(Integer adkLogId);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_ADK_LOG
	 *
	 * @mbg.generated Fri Apr 26 14:35:48 CST 2019
	 */
	int updateByExampleSelective(@Param("record") AdkLog record, @Param("example") AdkLogExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_ADK_LOG
	 *
	 * @mbg.generated Fri Apr 26 14:35:48 CST 2019
	 */
	int updateByExampleWithBLOBs(@Param("record") AdkLog record, @Param("example") AdkLogExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_ADK_LOG
	 *
	 * @mbg.generated Fri Apr 26 14:35:48 CST 2019
	 */
	int updateByExample(@Param("record") AdkLog record, @Param("example") AdkLogExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_ADK_LOG
	 *
	 * @mbg.generated Fri Apr 26 14:35:48 CST 2019
	 */
	int updateByPrimaryKeySelective(AdkLog record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_ADK_LOG
	 *
	 * @mbg.generated Fri Apr 26 14:35:48 CST 2019
	 */
	int updateByPrimaryKeyWithBLOBs(AdkLog record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to
	 * the database table T_ADK_LOG
	 *
	 * @mbg.generated Fri Apr 26 14:35:48 CST 2019
	 */
	int updateByPrimaryKey(AdkLog record);
}