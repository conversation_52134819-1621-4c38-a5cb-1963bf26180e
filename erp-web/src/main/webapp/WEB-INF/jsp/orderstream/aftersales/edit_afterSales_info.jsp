<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="编辑售后信息" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%=basePath%>static/js/orderstream/aftersales/edit_afterSales_info.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="<%=basePath%>/static/js/region/index.js?rnd=${resourceVersionKey}"></script>
<div class="formpublic">
    <form method="post" action="" id="editAfterSalesInfo">
        <ul>
            <li>
                <div class="infor_name">
                    <span>*</span>
                    <lable>售后原因：</lable>
                </div>
                <div class="f_left  ">
                    <select class="mr5" name="reason" id="reason">
                        <c:forEach var="list" items="${reasonList}" >
                            <option value="${list.sysOptionDefinitionId}"
                                    <c:if test="${afterSalesDetailVo.reason eq list.sysOptionDefinitionId}">selected="selected"</c:if>>${list.title}</option>
                        </c:forEach>
                    </select>
                    <div id="reasonError"></div>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <span>*</span>
                    <lable>第一责任部门：</lable>
                </div>
                <div class="f_left  ">
                    <select class="mr5" name="firstResponsibleDepartment" id="firstResponsibleDepartment">
                        <c:forEach var="org" items="${orgList}">
                            <option value="${org.orgId}"
                                    <c:if test="${afterSalesDetailVo.firstResponsibleDepartment eq org.orgId}">selected="selected"</c:if>>${org.orgName}</option>
                        </c:forEach>
                    </select>
                    <div id="firstResponsibleDepartmentError"></div>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <span>*</span>
                    <lable>详情说明：</lable>
                </div>
                <div class="f_left table-largest">
                    <div>
                                <textarea name="comments" placeholder="请输入详情内容以便后续"
                                          style="width: 338px; height: 100px" maxlength="200" id="comments">${afterSalesDetailVo.comments}</textarea>
                        <div id="commentsError"></div>
                    </div>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <span>*</span>
                    <lable>售后单报单人：</lable>
                </div>
                <div class="f_left  ">
                    <select class="mr5" name="traderContactId" id="traderContactId">
                        <c:forEach var="traderContact" items="${traderContactList}" >
                            <option value="${traderContact.traderContactId}"
                                    <c:if test="${afterSalesDetailVo.traderContactId eq traderContact.traderContactId}">selected="selected"</c:if>>
                                    ${traderContact.name}|${traderContact.mobile}|${traderContact.telephone}
                            </option>
                        </c:forEach>
                    </select>
                    <div id="traderContactIdError"></div>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <span>*</span>
                    <lable>售后联系人：</lable>
                </div>
                <div class="f_left table-largest">
                    <div>
                        <input type="text" name="afterConnectUserName" id="afterConnectUserName" placeholder="请输入售后联系人名" style="width: 205px;"value="${afterSalesDetailVo.afterConnectUserName}">
                        <div id="afterConnectUserNameError"></div>
                    </div>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <span>*</span>
                    <lable>售后联系电话：</lable>
                </div>
                <div class="f_left table-largest">
                    <div>
                        <%--<input type="text" name="afterConnectPhone" style="width: 205px;" onkeyup="this.value=this.value.replace(/[^\d]/g,'') " onafterpaste="this.value=this.value.replace(/[^\d]/g,'') " id="afterConnectPhone" placeholder="请输入售后联系人电话/手机号" value="${afterSalesDetailVo.afterConnectPhone}">--%>
                            <input type="text" name="afterConnectPhone" style="width: 205px;" id="afterConnectPhone" placeholder="请输入售后联系人电话/手机号" value="${afterSalesDetailVo.afterConnectPhone}">
                        <div id="afterConnectPhoneError"></div>
                    </div>
                </div>
            </li>
            <c:choose>
                <c:when test="${not empty afterSalesVo.type && (afterSalesVo.type eq 584 || afterSalesVo.type eq 541 || afterSalesVo.type eq 585 || afterSalesVo.type eq 550 || afterSalesVo.type eq 4090 || afterSalesVo.type eq 4091)}">
                    <li>
                        <div class="form-tips">
                            <span>*</span>
                            <lable>售后服务地区</lable>
                        </div>
                        <div class="f_left ">
                            <div class="form-blanks">
                                <ul>
                                    <li>
                                        <select class="wid9" name="province" id="province">
                                            <option value="0">全部</option>
                                            <c:if test="${not empty provinceList }">
                                                <c:forEach items="${provinceList }" var="prov">
                                                    <option value="${prov.regionId }"
                                                            <c:if test="${province eq prov.regionId }">selected="selected"</c:if>>${prov.regionName }</option>
                                                </c:forEach>
                                            </c:if>
                                        </select>
                                    </li>
                                    <li>
                                        <select class="wid9" name="city" id="city">
                                            <option value="0">全部</option>
                                            <c:if test="${not empty cityList }">
                                                <c:forEach items="${cityList }" var="cy">
                                                    <option value="${cy.regionId }"
                                                            <c:if test="${city eq cy.regionId }">selected="selected"</c:if>>${cy.regionName }</option>
                                                </c:forEach>
                                            </c:if>
                                        </select>
                                    </li>
                                    <li>
                                        <select class="wid9" name="zone" id="zone">
                                            <option value="0">全部</option>
                                            <c:if test="${not empty zoneList }">
                                                <c:forEach items="${zoneList }" var="zo">
                                                    <option value="${zo.regionId }"
                                                            <c:if test="${zone eq zo.regionId }">selected="selected"</c:if>>${zo.regionName }</option>
                                                </c:forEach>
                                            </c:if>
                                        </select>
                                    </li>
                                </ul>
                            </div>
                            <div id="areaError"></div>
                        </div>
                    </li>
                    <li>
                        <div class="form-tips">
                            <span>*</span>
                            <lable>售后服务地址</lable>
                        </div>
                        <div class="f_left ">
                            <div class="form-blanks">
                                <input type="text" class="input-largest" id="address" name="address" value="${afterSalesDetailVo.address}">
                            </div>
                            <div id="addressError"></div>
                        </div>
                    </li>
                </c:when>
                <c:when test="${not empty afterSalesVo.type && afterSalesVo.type eq 540}">
                    <li>
                        <div class="form-tips">
                            <span>*</span>
                            <lable>收货地址</lable>
                        </div>
                        <div class="f_left ">
                            <div class="form-blanks">
                                <input type="text" class="input-largest" id="address" name="address" value="${afterSalesDetailVo.address}">
                            </div>
                            <div id="addressError"></div>
                        </div>
                    </li>
                </c:when>
            </c:choose>

            <%--<li>
                <div class="infor_name">
                    <lable>添加附件</lable>
                </div>
                <input type="hidden" id="domain" name="domain" value="${domain}">
                <div class="f_left table-large">
                    <div class="form-blanks">
                        <div class="pos_rel f_left">
                            <input type="file" class="uploadErp" id='file_1' name="lwfile" onchange="uploadFile(this,1);">
                            <input type="text"  id="name_1" readonly="readonly"
                                   placeholder="上传文件" name="fileName" onclick="file_1.click();" style="width: 205px;" >
                            <input type="hidden" id="uri_1" name="fileUri" >
                        </div>
                    <span class="font-grey9">支持：png  .jpg.等图片格式文件大小不得超多20M</span>
                    </div>
                </div>
            </li>--%>
            <li>
                <div class="form-tips">
                    <lable>添加附件</lable>
                </div>
                <input type="hidden" id="domain" name="domain" value="${domain}">
                <div class="f_left ">
                    <c:if test="${empty afterSalesVo.attachmentList }">
                        <div class="form-blanks">
                            <div class="pos_rel f_left">
                                <input type="file" class="uploadErp" id='file_1' name="lwfile"
                                       onchange="uploadFile(this,1);">
                                <input type="text" class="input-largest" id="name_1" readonly="readonly"
                                       placeholder="请上传附件" name="fileName" onclick="file_1.click();">
                                <input type="hidden" id="uri_1" name="fileUri">
                            </div>
                            <label class="bt-bg-style bt-small bg-light-blue f_left" type="file" id="busUpload"
                                   onclick="return $('#file_1').click();">浏览</label>
                            <!-- 上传成功出现 -->
                            <div class="f_left">
                                <i class="iconsuccesss mt3 none" id="img_icon_1"></i>
                                <a href="" target="_blank" class="font-blue cursor-pointer  mt3 none"
                                   id="img_view_1">查看</a>
                                <span class="font-red cursor-pointer  mt3 none" onclick="del(1)"
                                      id="img_del_1">删除</span>
                            </div>
                            <div class='clear'></div>
                        </div>
                    </c:if>
                    <c:if test="${not empty afterSalesVo.attachmentList}">
                        <c:forEach items="${afterSalesVo.attachmentList}" var="att" varStatus="status">
                            <div class="form-blanks <c:if test='${status.count ne 1}'>mt10</c:if>">
                                <div class="pos_rel f_left">
                                    <input type="file" class="uploadErp" id='file_${status.count}' name="lwfile"
                                           onchange="uploadFile(this,${status.count});">
                                    <input type="text" class="input-largest" id="name_${status.count}"
                                           readonly="readonly"
                                           placeholder="请上传附件" name="fileName" onclick="file_${status.count}.click();"
                                           value="${att.name}">
                                    <input type="hidden" id="uri_${status.count}" name="fileUri" value="${att.uri}">
                                </div>
                                <label class="bt-bg-style bt-small bg-light-blue f_left" type="file" id="busUpload"
                                       onclick="return $('#file_${status.count}').click();">浏览</label>
                                <!-- 上传成功出现 -->
                                <div class="f_left">
                                    <i class="iconsuccesss mt3" id="img_icon_${status.count}"></i>
                                    <a href="http://${att.domain}${att.uri}" target="_blank"
                                       class="font-blue cursor-pointer mt3" id="img_view_${status.count}">查看</a>
                                    <span class="font-red cursor-pointer mt3" onclick="del(${status.count})"
                                          id="img_del_${status.count}">删除</span>
                                </div>
                                <div class='clear'></div>
                            </div>
                        </c:forEach>
                    </c:if>
                    <div class="mt8" id="conadd">
                        <span class="bt-border-style bt-small border-blue" onclick="conadd();">继续添加</span>
                    </div>
                    <div class="pop-friend-tips mt6">
                        <br/> 支持：png.jpg.等图片格式
                        <br/> 文件大小不得超过2M
                    </div>
                </div>
            </li>
        </ul>
        <div class="add-tijiao tcenter">
            <input type="hidden" name="formToken" value="${formToken}"/>
            <input type="hidden" name="afterSalesId" value="${afterSalesVo.afterSalesId}">
            <input type="hidden" name="afterSalesType" id="afterSalesType" value="${afterSalesVo.type}">
            <input type="hidden" name="afterSalesDetailId" value="${afterSalesDetailVo.afterSalesDetailId}">
            <button class="dele" id="close-layer" type="button"  style="background-color: #f5f7fa;border-color: #ced2d9;color: black;" onclick="closeGoBack();">取消</button>
            <button type="submit" id="submit" style="background-color: #3384ef;border-color: #fff;">确定</button>
        </div>
    </form>
</div>
<%@ include file="../../common/footer.jsp"%>