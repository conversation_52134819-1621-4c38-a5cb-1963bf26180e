package com.vedeng.mobile.config.filter;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.ServletUtils;
import com.vedeng.mobile.constant.ErpMobileCacheConstant;
import com.vedeng.mobile.system.service.UserInfoService;
import com.vedeng.mobile.utils.JwtUtil;
import com.vedeng.mobile.utils.MobileRedisUtil;
import com.vedeng.uac.api.dto.UserLoginInfo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.HandlerExecutionChain;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: erpMobileFilter 暂用过滤器做登录token认证过滤，后期优化
 * @date 2022/1/5 14:23
 */
@Slf4j
public class ErpMobileFilter implements Filter {

    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private HandlerExceptionResolver handlerExceptionResolver;


    @Value("${mobile.ignored.urls}")
    private String excludeUrls;

    @Value("${mobile.expire.Version:1.3.4,1.3.3}")
    private String expireVersion;


    @Override
    public void init(FilterConfig filterConfig) {
        log.info("初始化过滤器");
    }

    @Override
    public void destroy() {
        log.info("销毁过滤器");
    }

    @SneakyThrows
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws ServletException, IOException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpServletResponse = (HttpServletResponse) servletResponse;
        TimeInterval timer = DateUtil.timer();


        if (handleExcludeURL(httpServletRequest)) {
            this.executeFilter(servletRequest, servletResponse, filterChain, timer);
            return;
        }
        RequestMappingHandlerMapping handlerMapping = SpringUtil.getBean(RequestMappingHandlerMapping.class);
        HandlerExecutionChain handlerChain = handlerMapping.getHandler(httpServletRequest);
        HandlerMethod handler = (HandlerMethod) handlerChain.getHandler();
        ////接口被注解标记不需要鉴权
        if (allowAccess(handler)) {
            this.executeFilter(servletRequest, servletResponse, filterChain, timer);
            return;
        }

        String appVersion = httpServletRequest.getHeader("app-version");
        log.info("appVersion is:{}",appVersion);
        if(StringUtils.isNotBlank(expireVersion) && StringUtils.isNotBlank(appVersion)){
            List<String> versions = Arrays.asList(expireVersion.split(","));
            if(versions.contains(appVersion)){
                handlerExceptionResolver.resolveException(httpServletRequest, httpServletResponse, null,
                        new ServiceException(BaseResponseCode.LOW_VERSION));
                return;
            }
        }


        //从header中获取token
        String token = httpServletRequest.getHeader("token");
        if (StringUtils.isBlank(token)) {
            handlerExceptionResolver.resolveException(httpServletRequest, httpServletResponse, null,
                    new ServiceException(BaseResponseCode.USER_NOT_LOGIN));
            return;
        }
        //解析token获取用户
        String username = JwtUtil.getUsername(token);
        if (StringUtils.isBlank(username)) {
            handlerExceptionResolver.resolveException(httpServletRequest, httpServletResponse, null,
                    new ServiceException(BaseResponseCode.USER_NOT_LOGIN));
            return;
        }
        UserLoginInfo user = userInfoService.userLoginInfo(username);
        if (null == user) {
            handlerExceptionResolver.resolveException(httpServletRequest, httpServletResponse, null,
                    new ServiceException(BaseResponseCode.TOKEN_INVALID));
            return;
        }
        //校验token是否失效，自动续期
        if (!refreshToken(token, username, user.getPassWord())) {
            handlerExceptionResolver.resolveException(httpServletRequest, httpServletResponse, null,
                    new ServiceException(BaseResponseCode.TOKEN_EXPIRED));
            return;
        }

        // 使用新的当前登录用户对象,初始化当前登录用户信息
        CurrentUser currentUser = CurrentUser.builder().id(user.getUserId())
                .username(user.getUserName())
                .build();
        HttpSession session = ServletUtils.getRequest().getSession();
        session.setAttribute(ErpConstant.CURRENT_USER, currentUser);

        this.executeFilter(servletRequest, servletResponse, filterChain, timer);
    }


    /**
     * 执行下一个过滤并记录日志
     *
     * @param servletRequest  servletRequest
     * @param servletResponse servletRequest
     * @param filterChain     filterChain
     */
    private void executeFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain, TimeInterval timer) {
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpServletResponse = (HttpServletResponse) servletResponse;
        try {
            filterChain.doFilter(servletRequest, servletResponse);
        } catch (Exception e) {
            log.error("认证过滤器系统异常", e);
            handlerExceptionResolver.resolveException(httpServletRequest, httpServletResponse, null,
                    new ServiceException(BaseResponseCode.SYSTEM_BUSY));
        } finally {
            log.info("请求路径[{}]，请求参数[{}]，请求耗时[{}]", httpServletRequest.getRequestURL(), JSON.toJSONString(httpServletRequest.getParameterMap()), timer.interval());
        }
    }


    /**
     * 刷新token
     */
    public boolean refreshToken(String token, String userName, String passWord) {
        String tokenKey = ErpMobileCacheConstant.TOKEN + token;
        String cacheToken = String.valueOf(MobileRedisUtil.StringOps.get(tokenKey));
        if (StringUtils.isNotEmpty(cacheToken)) {
            // 校验token有效性，注意需要校验的是缓存中的token
            if (!JwtUtil.verify(cacheToken, userName, passWord)) {
                String newToken = JwtUtil.sign(userName, passWord);
                // 设置超时时间
                MobileRedisUtil.StringOps.set(tokenKey, newToken);
                MobileRedisUtil.KeyOps.expire(tokenKey, 30 * 60 * 2, TimeUnit.SECONDS);
            }
            return true;
        }
        return false;
    }


    /**
     * 是否需要过滤
     */
    private boolean handleExcludeURL(HttpServletRequest request) {
        // 排除链接
        List<String> excludes = new ArrayList<>();
        if (StrUtil.isNotEmpty(excludeUrls)) {
            String[] url = excludeUrls.split(",");
            excludes.addAll(Arrays.asList(url));
        }
        if (excludes.isEmpty()) {
            return false;
        }
        String url = request.getServletPath();
        if("/".equals(url)){
            return true;
        }
        for (String pattern : excludes) {
            Pattern p = Pattern.compile("^" + pattern);
            Matcher m = p.matcher(url);
            if (m.find()) {
                return true;
            }
        }

        return false;
    }


    /**
     * 检查接口时否存在注解如果存在不需要鉴权
     *
     * @param handler
     * @return
     */
    private boolean allowAccess(Object handler) {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            return  handlerMethod.getMethodAnnotation(ExcludeAuthorization.class)  != null;
        }
        return false;
    }
}
