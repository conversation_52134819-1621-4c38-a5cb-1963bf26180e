package com.vedeng.erp.statistic.mapper;

import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDay;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayExample;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayKey;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BroadcastStatisticIncomeDayMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_DAY
     *
     * @mbg.generated
     */
    long countByExample(BroadcastStatisticIncomeDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_DAY
     *
     * @mbg.generated
     */
    int deleteByExample(BroadcastStatisticIncomeDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_DAY
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(BroadcastStatisticIncomeDayKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_DAY
     *
     * @mbg.generated
     */
    int insert(BroadcastStatisticIncomeDayWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_DAY
     *
     * @mbg.generated
     */
    int insertSelective(BroadcastStatisticIncomeDayWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_DAY
     *
     * @mbg.generated
     */
    List<BroadcastStatisticIncomeDayWithBLOBs> selectByExampleWithBLOBs(BroadcastStatisticIncomeDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_DAY
     *
     * @mbg.generated
     */
    List<BroadcastStatisticIncomeDay> selectByExample(BroadcastStatisticIncomeDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_DAY
     *
     * @mbg.generated
     */
    BroadcastStatisticIncomeDayWithBLOBs selectByPrimaryKey(BroadcastStatisticIncomeDayKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_DAY
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") BroadcastStatisticIncomeDayWithBLOBs record, @Param("example") BroadcastStatisticIncomeDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_DAY
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") BroadcastStatisticIncomeDayWithBLOBs record, @Param("example") BroadcastStatisticIncomeDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_DAY
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") BroadcastStatisticIncomeDay record, @Param("example") BroadcastStatisticIncomeDayExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_DAY
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(BroadcastStatisticIncomeDayWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_DAY
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(BroadcastStatisticIncomeDayWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_DAY
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(BroadcastStatisticIncomeDay record);
    
    /**
     * 批量插入日到款金额
     * @param broadcastStatisticIncomeDayList
     */
    void batchInsert(@Param("broadcastStatisticIncomeDayList") List<BroadcastStatisticIncomeDayWithBLOBs> broadcastStatisticIncomeDayList);
}