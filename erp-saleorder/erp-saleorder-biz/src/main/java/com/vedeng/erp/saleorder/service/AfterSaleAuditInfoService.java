package com.vedeng.erp.saleorder.service;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.erp.aftersale.domain.entity.AfterSaleAuditInfoEntity;
import com.vedeng.order.model.dto.ProductManageAndAsistDto;

import java.util.List;
import java.util.Set;

public interface AfterSaleAuditInfoService {
    Set<String> getProductManageAndAsistNameList(List<ProductManageAndAsistDto> manageAndAsistDtoList);

    List<Integer> getProductManageAndAsistIdList(List<ProductManageAndAsistDto> manageAndAsistDtoList);

    void insertAfterSaleAuditInfo(List<ProductManageAndAsistDto> manageAndAsistDtoList, AfterSalesVo afterSalesInfo);

    List<AfterSaleAuditInfoEntity> findGoodsAuditList(AfterSaleAuditInfoEntity entity);

    void clearAuditInfoByAfterSaleId(Integer afterSalesId);
}
