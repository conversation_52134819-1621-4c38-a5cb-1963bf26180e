package com.vedeng.trader.group.buy;

import com.vedeng.trader.group.BaseBehavior;

import java.util.BitSet;
/**
 * <b>Description:</b>最近有购买<br>
 * @Note
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/5/25
 */
public class BuyBehavior extends BuyBaseBehavior {
    @Override
    public BitSet calculate() {
        BitSet res=getBaseTraderSet();
        BitSet buyset=getBuyInPeriod();
        res.and(buyset);
        return res;
    }
}
