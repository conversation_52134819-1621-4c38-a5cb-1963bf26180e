package com.vedeng.goods.web.controller;

import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.base.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 方案
 * @date 2024/12/3 8:47
 */
@Controller
@RequestMapping("/sku/scene")
@Slf4j
public class SkuSceneController extends BaseController {


    /**
     * 首页
     */
    @RequestMapping("/index")
    public String index(){
        return view();
    }


    /**
     * 新增
     */
    @RequestMapping("/edit")
    public String edit(){
        return view();
    }


    /**
     * 查看
     */
    @RequestMapping("/detail")
    public String detail(@RequestParam Integer id){
        return view();
    }


}
