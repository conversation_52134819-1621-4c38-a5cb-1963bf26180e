package com.vedeng.common.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.math.RoundingMode;

/**
 * @description: BigDecimal位数处理注解
 * @return:
 * @author: Strange
 * @date: 2022/8/25
 **/
@Target({ElementType.PARAMETER, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface BigScale {
    //配合BigScaleUtils使用
    /**
     * 位数
     * @return
     */
    int scale() default 2;

    /**
     * 取数规则
     */
    RoundingMode rounding() default RoundingMode.HALF_UP;
}
