package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeeStorageOutCommand;
import com.vedeng.erp.kingdee.dto.KingDeeStorageOutDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageOutDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeStorageOutMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageOutCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageOutConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * 其他出库
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeStorageOut {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeStorageOutCommandConvertor commandConvertor;

    @Autowired
    private KingDeeStorageOutConvertor kingDeeStorageOutConvertor;

    @Autowired
    private KingDeeStorageOutMapper kingDeeStorageOutMapper;


    @Test
    public void kingDeeStorageOut() {
        //一级dto
        KingDeeStorageOutDto kingDeeStorageOutDto = new KingDeeStorageOutDto();
        kingDeeStorageOutDto.setFId("0");
        kingDeeStorageOutDto.setFBillTypeId("QTCKD01_SYS");
        kingDeeStorageOutDto.setFBillNo("QTCK-0001");
        kingDeeStorageOutDto.setFQzokBddjtId("贝登erp对应的单据头ID");
        kingDeeStorageOutDto.setFStockOrgId("101");
        kingDeeStorageOutDto.setFStockDirect("GENERAL");
        kingDeeStorageOutDto.setFDate("2022-09-17 00:00:00");
        kingDeeStorageOutDto.setFCustId("KH0001");
        kingDeeStorageOutDto.setFDeptId("BM9999");
        List<KingDeeStorageOutDetailDto> kingDeeStorageOutDetailDtoList = new ArrayList<>();
        KingDeeStorageOutDetailDto kingDeeStorageOutDetailDto = new KingDeeStorageOutDetailDto();
        kingDeeStorageOutDetailDto.setFMaterialId("Sku0001");
        kingDeeStorageOutDetailDto.setFStockId("CK9999");
        kingDeeStorageOutDetailDto.setFQty("10.0");
        kingDeeStorageOutDetailDto.setFQzokYsddh("原始订单号");
        kingDeeStorageOutDetailDto.setFQzokGsywdh("归属业务单号");
        kingDeeStorageOutDetailDto.setFQzokYwlx("业务类型");
        kingDeeStorageOutDetailDto.setFQzokPch("批次号");
        kingDeeStorageOutDetailDto.setFQzokXlh("序列号");
        kingDeeStorageOutDetailDto.setFQzokSqlx("授权类型");
        kingDeeStorageOutDetailDto.setFQzokSfzf("是否直发");
        kingDeeStorageOutDetailDto.setFQzokBddjhid("贝登订单行ID");

        kingDeeStorageOutDetailDtoList.add(kingDeeStorageOutDetailDto);
        kingDeeStorageOutDto.setFEntity(kingDeeStorageOutDetailDtoList);

        KingDeeStorageOutCommand command = commandConvertor.toCommand(kingDeeStorageOutDto);

        System.out.println("入参："+JSON.toJSONString(command));
        kingDeeBaseApi.save(new SaveExtCommand<>(command, kingDeeStorageOutDto.getFormId()));

    }
}
