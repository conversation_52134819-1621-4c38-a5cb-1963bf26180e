<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderAuditSearchMapper">

    <select id="getCustomerRelateTableKey"  resultType="integer">
        select
                TRADER_CUSTOMER_ID
                from T_TRADER_CUSTOMER
        where TRADER_ID = #{traderId,jdbcType=INTEGER}
    </select>

    <select id="getSupplierRelateTableKey"  resultType="integer">
        select
            TRADER_SUPPLIER_ID
        from T_TRADER_SUPPLIER
        where TRADER_ID = #{traderId,jdbcType=INTEGER}
    </select>
</mapper>

