/.settings
.classpath
.project
.factorypath
.jsdtscope
*.prefs
*.core.xml
org.eclipse.*
/work/
.vscode

# Built application files and Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties

# Compiled class files
*.class

# Log Files
*.log

# About IntelliJ
*.iml
/.idea/
/out/

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# macOS
.DS_Store

# Package Files
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# CMake
cmake-build-debug/

# File-based project format
*.iws

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*

# rebel插件配置
rebel.xml

.vscode/
.claude/settings.local.json