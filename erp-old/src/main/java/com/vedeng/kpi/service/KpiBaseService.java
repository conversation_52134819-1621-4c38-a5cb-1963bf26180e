package com.vedeng.kpi.service;

import com.vedeng.kpi.cache.KpiLoadingCache;
import com.vedeng.kpi.dao.KpiChanceMapper;
import com.vedeng.kpi.dao.KpiOrderLogMapper;
import com.vedeng.kpi.model.DO.KpiDailyCountDo;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.model.DTO.KpiGroupConfigDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;
import com.vedeng.kpi.model.DTO.emptyEntity.NullKpiConfigItemDto;
import com.vedeng.kpi.share.KpiUtils;
import com.vedeng.kpi.threadLocal.KpiThreadParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @description: 五行service基类.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/9 10:47 上午.
 * @author: Tomcat.Hui.
 */
@Service
public abstract class KpiBaseService {

    protected static final Logger log = LoggerFactory.getLogger("kpilog");


    /**
     * 线程共享对象
     */
    private static volatile ThreadLocal<KpiThreadParams> shareParams = new ThreadLocal<>();

    @Autowired
    protected KpiLoadingCache kpiLoadingCache;

    @Resource
    protected KpiChanceMapper kpiChanceMapper;

    @Resource
    protected KpiOrderLogMapper kpiOrderLogMapper;

    @Autowired
    KpiDailyCountService kpiDailyCountService;

    @Autowired
    protected KpiParamTransService kpiParamTransService;

    /**
     * 初始化线程对象.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 一次定时任务更新多个团队时,每次切换团队需要清空一次线程对象里的共享信息.
     * @version: 1.0.
     * @date: 2020/7/11 11:01 上午.
     * @author: Tomcat.Hui.
     * @return: void.
     * @throw: .
     */
    public static void initThreadParams(KpiDataQueryDto query){
        log.info("开始清空共享对象信息");
        if (null != shareParams.get()) {
            shareParams.remove();
        }
        shareParams.set(new KpiThreadParams());
        shareParams.get().setQuery(query);

        log.info("清空共享对象信息结束 ");
    }

    /**
     * 业务处理.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version 1.0.
     * @date: 2020/7/9 9:36 上午.
     * @author: Tomcat.Hui.
     * @param userInfo: .
     * @return: void.
     * @throws Exception 异常.
     */
    public abstract void dataHandler(KpiUserInfoDto userInfo) throws Exception;

    /**
     * 插入/更新count表.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/11 11:00 上午.
     * @author: Tomcat.Hui.
     * @param kpiDailyCountDo: .
     * @return: java.lang.Boolean.
     * @throw: .
     */
    protected Boolean insertOrUpdateCount(KpiDailyCountDo kpiDailyCountDo){
        return kpiDailyCountService.insertOrUpdate(kpiDailyCountDo);
    }

    /**
     * 获取时间字符串.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 11:00 上午.
     * @author: Tomcat.Hui.
     * @param date: 日期.
     * @return: java.lang.String yyyy-MM-dd.
     * @throws: .
     */
    protected String getDateStr(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(KpiUtils.getDateStart(date));
    }

    protected String getStartDateStr(String dateStr) throws ParseException {
        // changed by Tomcat.Hui 2020/9/8 11:14 上午 .Desc: 修复销售页面绩效进度计算值的问题 .
        SimpleDateFormat sdfin = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdfOut = new SimpleDateFormat("yyyyMM");
        return sdfOut.format(KpiUtils.getMonthStart(sdfin.parse(dateStr)));
    }
    /**
     * 获取团队配置.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 11:01 上午.
     * @author: Tomcat.Hui.
     * @param groupId: 团队ID.
     * @return: com.vedeng.kpi.model.DTO.KpiGroupConfigDto.
     * @throws: .
     */
    protected KpiGroupConfigDto getGroupConfig(Integer groupId){
        return kpiLoadingCache
                .getGroupConfig(groupId);
    }

    /**
     * 获取团队权值.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/6 11:02 上午.
     * @author: Tomcat.Hui.
     * @param groupId: .
     * @param kpiType: .
     * @return: java.math.BigDecimal.
     * @throws: .
     */
    protected BigDecimal getWeight(Integer groupId,String kpiType){
        return getGroupConfig(groupId).getConfigItems().stream()
                .filter(item -> item.getItem().equals(kpiType))
                .findFirst().orElse(new NullKpiConfigItemDto()).getWeight();
    }

    /**
     * 获取ID&name.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/11 11:21 上午.
     * @author: Tomcat.Hui.
     * @param userId: .
     * @return: java.lang.String.
     * @throw: .
     */
    protected String getIdName(Integer userId){
        return userId + "/" + kpiLoadingCache.getUserConfig(userId).getUserName();
    }

    /**
     * 获取团队绩效.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: 每个团队只需要查询一次,避免重复查询.
     * @version: 1.0.
     * @date: 2020/7/11 10:59 上午.
     * @author: Tomcat.Hui.
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throw: .
     */
    protected List<KpiDataQueryDto> getGroupOrderAmountMonth(){

        if (null == shareParams.get().getGroupOrderAmountMonth()) {
            synchronized (KpiBaseService.class){
                if (null == shareParams.get().getGroupOrderAmountMonth()) {
                    log.info("开始加载团队 " + kpiLoadingCache.getGroupConfig(shareParams.get().getQuery().getGroupId())
                            .getGroupName() + " 业绩数据");
                    shareParams.get().setGroupOrderAmountMonth(kpiOrderLogMapper
                            .getGroupOrderAmountMonth(shareParams.get().getQuery()));
                }
            }
        }
        return shareParams.get().getGroupOrderAmountMonth();
    }

    /**
     * 获取团队合作客户数(90天).
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/11 11:20 上午.
     * @author: Tomcat.Hui.
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throw: .
     */
    protected List<KpiDataQueryDto> getGroupNewCoCustomersMonth() {
        if (null == shareParams.get().getGroupCoCustomersMonth()) {
            synchronized (KpiBaseService.class){
                if (null == shareParams.get().getGroupCoCustomersMonth()) {
                    log.info("开始加载团队 " + kpiLoadingCache.getGroupConfig(shareParams.get().getQuery().getGroupId())
                            .getGroupName() + " 客户数据");
                    shareParams.get().setGroupCoCustomersMonth(kpiOrderLogMapper
                            .getGroupCoCustomers(shareParams.get().getQuery()));
                }
            }
        }
        return shareParams.get().getGroupCoCustomersMonth();
    }

    /**
     * 获取团队Bd客户数据.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/11 11:26 上午.
     * @author: Tomcat.Hui.
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throw: .
     */
    protected List<KpiDataQueryDto> getGroupBdNewCoCustomers(){
        if (null == shareParams.get().getGroupBdCoCustomersMonth()) {
            synchronized (KpiBaseService.class){
                if (null == shareParams.get().getGroupBdCoCustomersMonth()) {
                    log.info("开始加载团队 " + kpiLoadingCache.getGroupConfig(shareParams.get().getQuery().getGroupId())
                            .getGroupName() + " BD新客数据");
                    shareParams.get().setGroupBdCoCustomersMonth(kpiOrderLogMapper
                            .getGroupBdNewCustomers(shareParams.get().getQuery()));
                }
            }
        }
        return shareParams.get().getGroupBdCoCustomersMonth();
    }

    /**
     * 获取团队商机数据.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/11 11:28 上午.
     * @author: Tomcat.Hui.
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throw: .
     */
    protected List<KpiDataQueryDto> getGroupChancesMonth(){
        if (null == shareParams.get().getGroupChanceMonth()) {
            synchronized (KpiBaseService.class){
                if (null == shareParams.get().getGroupChanceMonth()) {
                    log.info("开始加载团队 " + kpiLoadingCache.getGroupConfig(shareParams.get().getQuery().getGroupId())
                            .getGroupName() + " 商机数据");
                    shareParams.get().setGroupChanceMonth(kpiOrderLogMapper
                            .getGroupChanceTrans(shareParams.get().getQuery()));
                }
            }
        }
        return shareParams.get().getGroupChanceMonth();
    }

    /**
     * 获取团队区间新客数.
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/11 11:32 上午.
     * @author: Tomcat.Hui.
     * @return: java.util.List<com.vedeng.kpi.model.DTO.KpiDataQueryDto>.
     * @throw: .
     */
    protected List<KpiDataQueryDto> getGroupRangeNewChanceMonth(){
        if (null == shareParams.get().getGroupRangeNewChanceMonth()) {
            synchronized (KpiBaseService.class){
                if (null == shareParams.get().getGroupRangeNewChanceMonth()) {
                    log.info("开始加载团队 " + kpiLoadingCache.getGroupConfig(shareParams.get().getQuery().getGroupId())
                            .getGroupName() + " 区间新客数据");
                    shareParams.get().setGroupRangeNewChanceMonth(kpiChanceMapper
                            .getGroupRangeBussinessChances(shareParams.get().getQuery()));
                }
            }
        }
        return shareParams.get().getGroupRangeNewChanceMonth();
    }
}
