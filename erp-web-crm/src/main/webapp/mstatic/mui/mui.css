.vd-ui-address .vd-ui-address-panel {
  height: calc(100% - 53px);
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap {
  height: 100%;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .vd-ui-address-nav {
  padding: 0 10px;
  border-bottom: solid 1px #EBEFF2;
  display: flex;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .vd-ui-address-nav .nav-item {
  position: relative;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #999;
  margin-right: 30px;
  max-width: 90px;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .vd-ui-address-nav .nav-item.active {
  font-weight: 700;
  color: #09f;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .vd-ui-address-nav .nav-item.active::after {
  content: "";
  display: block;
  border-bottom: solid 3px #09f;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .address-choose-panel {
  max-height: calc(100% - 41px);
  overflow-y: auto;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .address-choose-panel .ap-list .ap-item {
  padding: 10px;
  font-size: 12px;
  color: #000;
  display: flex;
  align-items: center;
  border-bottom: solid 1px #F5F7FA;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .address-choose-panel .ap-list .ap-item:last-child {
  border: none;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .address-choose-panel .ap-list .ap-item .selected {
  height: 16px;
  line-height: 16px;
  margin-right: 5px;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .address-choose-panel .ap-list .ap-item .selected > i {
  font-size: 16px;
  color: #ccc;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .address-choose-panel .ap-list .ap-item.active {
  background: #E0F3FF;
}
.vd-ui-address .vd-ui-address-panel .address-choose-wrap .address-choose-panel .ap-list .ap-item.active .selected i {
  color: #09f;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap {
  height: 100%;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list {
  max-height: 100%;
  overflow-y: auto;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list .filter-item {
  padding: 0 10px;
  height: 33px;
  line-height: 33px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list .filter-item .filter-selected {
  width: 16px;
  height: 16px;
  line-height: 16px;
  margin-right: 5px;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list .filter-item .filter-selected > i {
  height: 16px;
  font-size: 16px;
  color: #ccc;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list .filter-item .filter-selected > i.icon-radio3 {
  color: #09f;
  display: none;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list .filter-item.active {
  background: #E0F3FF;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list .filter-item.active .filter-selected > i.icon-radio3 {
  display: block;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .filter-list .filter-item.active .filter-selected > i.icon-radio1 {
  display: none;
}
.vd-ui-address .vd-ui-address-panel .address-search-wrap .no-filter {
  padding: 5px 0;
  color: #999;
  text-align: center;
}
.ui-form-address-wrap .form-address-view {
  padding-left: 10px;
}
.ui-form-address-wrap .form-address-view .form-address-show {
  display: flex;
  align-items: center;
}
.ui-form-address-wrap .form-address-view .form-address-show .value {
  flex: 1;
  min-width: 0;
}
.ui-form-address-wrap .form-address-view .form-address-show .icon-error2 {
  width: 36px;
  height: 38px;
  flex-shrink: 0;
  font-size: 16px;
  color: #666;
  display: flex;
  justify-content: center;
  align-items: center;
}
.form-address-panel {
  height: calc(100vh - 45px - 53px);
  height: calc(100vh - 98px - constant(safe-area-inset-bottom));
  height: calc(100vh - 98px - env(safe-area-inset-bottom));
}
.form-address-panel .panel-inner {
  height: calc(100% - 53px);
}

.vd-ui-user-wrap {
  display: flex;
  align-items: center;
}
.vd-ui-user-wrap .vd-ui-user-avatar {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
  border-radius: 3px;
  object-fit: cover;
  margin-right: 5px;
}
.vd-ui-user-wrap .vd-ui-user-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.vd-ui-checkbox-group.is-margin- {
  margin-bottom: -10px;
}
.vd-ui-checkbox-group .vd-ui-checkbox-item {
  margin-right: 20px;
  margin-bottom: 10px;
}
.vd-ui-checkbox-group .vd-ui-checkbox-item:last-child {
  margin-right: 0;
}
.vd-ui-checkbox-group .vd-ui-input-error {
  margin-top: -5px !important;
}
.vd-ui-checkbox-group.is-label {
  margin-bottom: -5px;
}
.vd-ui-checkbox-group.is-label .vd-ui-checkbox-item {
  margin-right: 5px;
  margin-bottom: 5px;
}
.vd-ui-checkbox-group.is-label .vd-ui-checkbox-item:last-child {
  margin-right: 0;
}
.vd-ui-checkbox-item {
  display: inline-block;
  cursor: pointer;
  color: #333333;
  vertical-align: top;
}
.vd-ui-checkbox-item * {
  box-sizing: border-box;
}
.vd-ui-checkbox-item .vd-ui-checkbox-icon {
  position: relative;
  width: 16px;
  height: 16px;
  border-radius: 2px;
  border: 1px solid #969B9E;
  margin-right: 5px;
  transition: all 0.1s;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1px;
}
.vd-ui-checkbox-item .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 {
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: scale(0);
  transition: transform 0.1s;
}
.vd-ui-checkbox-item .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 .icon-selected2 {
  font-size: 12px;
  color: #ffffff;
}
.vd-ui-checkbox-item .vd-ui-checkbox-inner {
  display: flex;
}
.vd-ui-checkbox-item .vd-ui-checkbox-inner .strong {
  color: #f60;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-checked .vd-ui-checkbox-icon {
  background: #0099FF;
  border-color: #0099FF;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-checked .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 {
  transform: scale(1);
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-progress .vd-ui-checkbox-icon {
  background: #0099FF;
  border-color: #0099FF;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-progress .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 {
  transform: scale(1);
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-progress .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 .icon-selected2 {
  display: inline-block;
  width: 8px;
  height: 2px;
  background: #fff;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-progress .vd-ui-checkbox-icon .vd-ui-checkbox-icon-selected2 .icon-selected2::before {
  display: none;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-disabled {
  color: #999999;
  cursor: not-allowed;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-disabled .vd-ui-checkbox-icon {
  border-color: #D7DADE;
  background: #F5F7FA;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-disabled.vd-ui-checkbox-item-checked .vd-ui-checkbox-icon {
  border-color: #D7DADE;
  background: #D7DADE;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-labeltype .vd-ui-checkbox-icon {
  display: none;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-labeltype .vd-ui-checkbox-label {
  padding: 6px 10px;
  border-radius: 3px;
  background: #F5F7FA;
}
.vd-ui-checkbox-item.vd-ui-checkbox-item-labeltype.vd-ui-checkbox-item-checked .vd-ui-checkbox-label {
  background: #09f;
  color: #fff;
}
.vd-ui-checkbox-item.single-row {
  display: block;
  padding: 10px;
  margin: 0;
  border-bottom: solid 1px #F5F7FA;
}
.vd-ui-checkbox-item.single-row:last-child {
  border-bottom: none;
}
.vd-ui-checkbox-item.single-row.vd-ui-checkbox-item-checked {
  background: #E0F3FF;
}
.vd-ui-radio-group {
  margin-bottom: -10px;
}
.vd-ui-radio-group .vd-ui-radio-item {
  margin-right: 20px;
  margin-bottom: 10px;
}
.vd-ui-radio-group .vd-ui-radio-item:last-child {
  margin-right: 0;
}
.vd-ui-radio-group .vd-ui-input-error {
  margin-top: -5px !important;
}
.vd-ui-radio-group.is-label {
  margin-bottom: -5px;
}
.vd-ui-radio-group.is-label .vd-ui-radio-item {
  margin-right: 5px;
  margin-bottom: 5px;
}
.vd-ui-radio-group.is-label .vd-ui-radio-item:last-child {
  margin-right: 0;
}
.vd-ui-radio-item {
  display: inline-block;
  cursor: pointer;
  color: #333333;
  vertical-align: top;
}
.vd-ui-radio-item * {
  box-sizing: border-box;
}
.vd-ui-radio-item .vd-ui-radio-icon {
  flex-shrink: 0;
  position: relative;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 1px solid #969B9E;
  margin-right: 5px;
  transition: all 0.1s;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2px;
}
.vd-ui-radio-item .vd-ui-radio-icon .vd-ui-radio-icon-selected {
  width: 0;
  height: 0;
  background: #0099FF;
  transition: all 0.1s;
  border-radius: 50%;
}
.vd-ui-radio-item .vd-ui-radio-label {
  white-space: nowrap;
}
.vd-ui-radio-item .vd-ui-radio-tip {
  color: #999;
}
.vd-ui-radio-item .vd-ui-radio-inner {
  display: flex;
}
.vd-ui-radio-item.vd-ui-radio-item-checked .vd-ui-radio-icon {
  border-color: #0099FF;
}
.vd-ui-radio-item.vd-ui-radio-item-checked .vd-ui-radio-icon .vd-ui-radio-icon-selected {
  width: 6px;
  height: 6px;
}
.vd-ui-radio-item.vd-ui-radio-item-disabled {
  color: #999999;
  cursor: not-allowed;
}
.vd-ui-radio-item.vd-ui-radio-item-disabled .vd-ui-radio-icon {
  border-color: #D7DADE;
}
.vd-ui-radio-item.vd-ui-radio-item-disabled .vd-ui-radio-icon .vd-ui-radio-icon-selected {
  background: #D7DADE;
}
.vd-ui-radio-item.vd-ui-radio-item-labeltype .vd-ui-radio-icon {
  display: none;
}
.vd-ui-radio-item.vd-ui-radio-item-labeltype .vd-ui-radio-label {
  padding: 6px 10px;
  border-radius: 3px;
  background: #F5F7FA;
}
.vd-ui-radio-item.vd-ui-radio-item-labeltype.vd-ui-radio-item-checked .vd-ui-radio-label {
  background: #09f;
  color: #fff;
}

.vd-ui-button {
  height: 33px;
  text-align: center;
  width: 100%;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  line-height: 31px;
  background: #F5F7FA;
  font-size: 14px;
}
.vd-ui-button.button-primary {
  background: #0099FF;
  border-color: #0099FF;
  color: #fff;
}
.vd-ui-button.button-primary-line {
  background: #E0F3FF;
  border-color: #0099FF;
  color: #09f;
}
.vd-ui-button.button-danger {
  background: #e64545;
  border-color: #e64545;
  color: #fff;
}
.vd-ui-button.button-primary-line {
  background: #E0F3FF;
  border-color: #0099FF;
  color: #09f;
}
.vd-ui-button.button-loading {
  background: #F5F7FA;
  border-color: #D7DADE;
  color: #999;
}

.vd-ui-date * {
  margin: 0;
  padding: 0;
  list-style: none;
  box-sizing: border-box;
}
.vd-ui-date {
  position: relative;
}
.vd-ui-date .vd-ui-date-editor--year,
.vd-ui-date .vd-ui-date-editor--month,
.vd-ui-date .vd-ui-date-editor--date,
.vd-ui-date .vd-ui-date-editor--datetime,
.vd-ui-date .vd-ui-date-editor--datetimerange,
.vd-ui-date .vd-ui-date-editor--daterange {
  width: 252px;
}
.vd-ui-date .vd-ui-date-editor--time {
  width: 180px;
}
.vd-ui-date .vd-ui-date-editor /deep/ .vd-ui-input__inner {
  cursor: pointer;
}
.vd-ui-date-wrapper {
  max-width: 760px;
  min-width: 320px;
  box-sizing: border-box;
  border-radius: 3px;
  background-color: #ffffff;
  list-style-type: none;
  margin: 0 auto;
}
.vd-ui-date-wrapper.appear-up {
  transform-origin: center bottom;
  animation: appear 0.22s ease-out;
}
.vd-ui-date-wrapper.appear-down {
  transform-origin: center top;
  animation: appear 0.22s ease-out;
}
.vd-ui-panel {
  border-radius: 3px;
  overflow: hidden;
}
.vd-ui-panel .vd-ui-panel-header {
  background: #fff;
  color: #333;
  height: 46px;
  margin: 0 auto;
  padding: 0;
  border-bottom: solid 1px #ddd;
}
.vd-ui-panel .vd-ui-panel-header > ul {
  margin: 0 auto;
  height: 46px;
  width: 90%;
  display: flex;
  align-items: center;
}
.vd-ui-panel .vd-ui-panel-header > ul > li {
  cursor: pointer;
}
.vd-ui-panel .vd-ui-panel-header > ul > li.year-month {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
.vd-ui-panel .vd-ui-panel-header > ul > li.arrow {
  width: 30px;
  text-align: center;
  color: #666;
}
.vd-ui-panel .vd-ui-panel-header > ul > li.arrow i {
  display: block;
  font-size: 16px;
  color: #666;
}
.vd-ui-panel .vd-ui-panel-header > ul > li.arrow.arrow-left1 i {
  transform: rotate(-90deg);
}
.vd-ui-panel .vd-ui-panel-header > ul > li.arrow.arrow-right1 i {
  transform: rotate(90deg);
}
.vd-ui-panel .vd-ui-panel-body {
  position: relative;
  padding: 5px 15px 5px;
}
.vd-ui-panel .vd-ui-panel-body table {
  table-layout: fixed;
  width: 100%;
}
.vd-ui-panel .date-shortcuts {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  border-top: solid 1px #E1E5E8;
  padding: 9px 0;
}
.vd-ui-panel .date-shortcuts .item-sc {
  position: relative;
  font-size: 12px;
  color: #09F;
  padding: 4px 9px;
  cursor: pointer;
}
.vd-ui-panel .date-shortcuts .item-sc::after {
  content: "";
  display: block;
  width: 0;
  height: 12px;
  border-right: solid 1px #e3e3e3;
  position: absolute;
  right: 0px;
  top: 50%;
  transform: translateY(-50%);
}
.vd-ui-panel .date-shortcuts .item-sc:last-child::after {
  display: none;
}
.vd-ui-date-table {
  font-size: 12px;
  user-select: none;
  width: 100%;
}
.vd-ui-date-table td {
  width: 36px;
  height: 36px;
  box-sizing: border-box;
  text-align: center;
  cursor: pointer;
  position: relative;
}
.vd-ui-date-table td > div {
  height: 100%;
  padding: 0;
  box-sizing: border-box;
}
.vd-ui-date-table td span {
  width: 36px;
  height: 36px;
  display: block;
  margin: 0 auto;
  line-height: 34px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 3px;
}
.vd-ui-date-table td.next-month,
.vd-ui-date-table td.prev-month {
  color: #C0C4CC;
}
.vd-ui-date-table td.today {
  position: relative;
}
.vd-ui-date-table td.today span {
  color: #0099FF;
  font-weight: bold;
  border: solid 1px #0099FF;
}
.vd-ui-date-table td.today.start-date span,
.vd-ui-date-table td.today.end-date span {
  color: #FFF;
}
.vd-ui-date-table td.available:hover span {
  background-color: #F5F7FA;
}
.vd-ui-date-table td.available:active span {
  background-color: #EBEFF2;
}
.vd-ui-date-table td.in-range div {
  background-color: #e0f3ff;
}
.vd-ui-date-table td.in-range div:hover {
  background-color: #4e7a96;
}
.vd-ui-date-table td.in-range div:hover span {
  background-color: #50bcff;
}
.vd-ui-date-table td.current:not(.disabled) span {
  color: #FFF;
  background-color: #0099FF;
}
.vd-ui-date-table td.start-date div,
.vd-ui-date-table td.end-date div {
  color: #FFF;
}
.vd-ui-date-table td.start-date span,
.vd-ui-date-table td.end-date span {
  background-color: #0099FF;
}
.vd-ui-date-table td.start-date div {
  margin-left: 5px;
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
}
.vd-ui-date-table td.end-date div {
  margin-right: 5px;
  border-top-right-radius: 15px;
  border-bottom-right-radius: 15px;
}
.vd-ui-date-table td.disabled div {
  background-color: #F5F7FA;
  opacity: 1;
  cursor: not-allowed;
  color: #C0C4CC;
}
.vd-ui-date-table td.selected div {
  margin-left: 5px;
  margin-right: 5px;
  background-color: #F2F6FC;
  border-radius: 15px;
}
.vd-ui-date-table td.selected div:hover {
  background-color: #F2F6FC;
}
.vd-ui-date-table td.selected span {
  background-color: #0099FF;
  color: #FFF;
  border-radius: 15px;
}
.vd-ui-date-table th {
  position: relative;
  font-weight: 400;
  color: #606266;
  height: 36px;
  text-align: center;
}
.vd-ui-year-table {
  font-size: 12px;
  border-collapse: collapse;
}
.vd-ui-year-table .el-icon {
  color: #303133;
}
.vd-ui-year-table td {
  text-align: center;
  padding: 5px 3px;
  cursor: pointer;
}
.vd-ui-year-table td.today .cell {
  color: #000;
  font-weight: bold;
}
.vd-ui-year-table td.disabled .cell {
  background-color: #F5F7FA;
  cursor: not-allowed;
  color: #999;
}
.vd-ui-year-table td.disabled .cell:hover {
  color: #999;
}
.vd-ui-year-table td .cell {
  width: 80px;
  height: 36px;
  display: block;
  line-height: 36px;
  color: #333;
  margin: 0 auto;
}
.vd-ui-year-table td .cell:hover {
  background-color: #F5F7FA;
}
.vd-ui-year-table td.current:not(.disabled) .cell {
  color: #fff;
  background: #409eff;
  border-radius: 3px;
}
.vd-ui-month-table {
  font-size: 12px;
  border-collapse: collapse;
}
.vd-ui-month-table td {
  text-align: center;
  padding: 0px;
  cursor: pointer;
}
.vd-ui-month-table td div {
  height: 48px;
  padding: 5px 0;
  box-sizing: border-box;
}
.vd-ui-month-table td.today .cell {
  color: #000;
  font-weight: bold;
}
.vd-ui-month-table td.today.start-date .cell,
.vd-ui-month-table td.today.end-date .cell {
  color: #fff;
}
.vd-ui-month-table td.disabled .cell {
  background-color: #F5F7FA;
  cursor: not-allowed;
  color: #999;
}
.vd-ui-month-table td.disabled .cell:hover {
  color: #999;
}
.vd-ui-month-table td .cell {
  width: 58px;
  height: 36px;
  display: block;
  line-height: 36px;
  color: #333;
  margin: 0 auto;
  border-radius: 3px;
}
.vd-ui-month-table td .cell:hover {
  background: #F5F7FA;
}
.vd-ui-month-table td.in-range div {
  background-color: #F2F6FC;
}
.vd-ui-month-table td.in-range div:hover {
  background-color: #F2F6FC;
}
.vd-ui-month-table td.start-date div,
.vd-ui-month-table td.end-date div {
  color: #fff;
}
.vd-ui-month-table td.start-date .cell,
.vd-ui-month-table td.end-date .cell {
  color: #FFF;
  background-color: #409EFF;
}
.vd-ui-month-table td.start-date div {
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
}
.vd-ui-month-table td.end-date div {
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
}
.vd-ui-month-table td.current:not(.disabled) .cell {
  background: #409EFF;
  border-radius: 3px;
  color: #fff;
}
.vd-ui-time-panel {
  width: 180px;
}
.vd-ui-time-panel .vd-ui-time-panel__header {
  height: 46px;
  font-size: 14px;
  color: #333333;
  line-height: 46px;
  text-align: center;
  border-bottom: solid 1px #E1E5E8;
}
.vd-ui-time-panel .vd-ui-time-panel__content {
  border-bottom: solid 1px #E1E5E8;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list {
  font-size: 0;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item {
  display: inline-block;
  vertical-align: top;
  width: 33.32%;
  height: 264px;
  overflow: hidden;
  border-right: solid 1px #E1E5E8;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item:last-child {
  border-right: none;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul {
  list-style: none;
  margin: 0;
  padding: 0;
  height: 100%;
  overflow-y: auto;
  padding-right: 6px;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul > li {
  position: relative;
  font-size: 14px;
  text-align: center;
  height: 33px;
  line-height: 33px;
  padding-left: 6px;
  cursor: pointer;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul > li:hover {
  background: #edf0f2;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul > li.active {
  color: #09f;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul::-webkit-scrollbar-track {
  background: transparent;
  width: 0;
  height: 0;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul::-webkit-scrollbar-thumb {
  width: 0;
  height: 0;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover {
  padding-right: 0;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-time-panel .vd-ui-time-panel__content .spinner-list .spinner-item > ul:hover::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-time-panel .vd-ui-time-panel__footer {
  height: 40px;
  padding: 0 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.vd-ui-time-panel .vd-ui-time-panel__footer .time-cancel {
  border: none;
  background: none;
  font-size: 12px;
  color: #0099FF;
  cursor: pointer;
}
.vd-ui-time-panel .vd-ui-time-panel__footer .time-confirm {
  width: 44px;
  height: 26px;
  border: none;
  background: #0099FF;
  border-radius: 3px;
  font-size: 12px;
  color: #ffffff;
  cursor: pointer;
}
.vd-ui-datetime-panel {
  width: 474px;
  box-sizing: border-box;
}
.vd-ui-datetime-panel * {
  box-sizing: border-box;
}
.vd-ui-datetime-panel .datetime-content {
  display: flex;
}
.vd-ui-datetime-panel .datetime-content .datetime-date-panel {
  border-right: solid 1px #E1E5E8;
}
.vd-ui-datetime-panel .datetime-content .datetime-time-panel {
  width: 180px;
}
.vd-ui-datetime-panel .datetime-btn {
  width: calc(100% - 2px);
  height: 40px;
  padding: 0 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: solid 1px #edf0f2;
}
.vd-ui-datetime-panel .datetime-btn .time-cancel {
  border: none;
  background: none;
  font-size: 12px;
  color: #0099FF;
  cursor: pointer;
}
.vd-ui-datetime-panel .datetime-btn .time-confirm {
  width: 44px;
  height: 26px;
  border: none;
  background: #0099FF;
  border-radius: 3px;
  font-size: 12px;
  color: #ffffff;
  cursor: pointer;
}
/* 范围选择 */
.vd-ui-daterange-wrapper {
  position: relative;
  max-width: 760px;
  min-width: 320px;
  margin: 0 auto;
  background: #F5F7FA;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-input-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-input-wrap .vd-ui-daterange-input {
  flex: 1;
  min-width: 0;
  height: 33px;
  padding: 0 10px;
  border-radius: 3px;
  background: #fff;
  border: solid 1px #BABFC2;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-input-wrap .vd-ui-daterange-input.active {
  border-color: #09F;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-input-wrap .vd-ui-daterange-input > input {
  width: 100%;
  height: 100%;
  background: none;
  outline: none;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-input-wrap .vd-ui-daterange-input > input::placeholder {
  color: #999;
  font-size: 12px;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-input-wrap .vd-ui-daterange-gap {
  padding: 0 5px;
  display: flex;
  align-items: center;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel * {
  box-sizing: border-box;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper {
  position: relative;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__sidebar {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100px;
  border-right: 1px solid #e4e4e4;
  box-sizing: border-box;
  padding-top: 15px;
  background-color: #fff;
  overflow: auto;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__sidebar .vd-ui-panel__shortcut {
  display: block;
  padding-left: 12px;
  width: 100%;
  background-color: transparent;
  border: 0;
  font-size: 12px;
  color: #09F;
  text-align: left;
  margin-bottom: 15px;
  outline: none;
  cursor: pointer;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content {
  float: left;
  width: 50%;
  box-sizing: border-box;
  margin: 0;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content.is-left {
  border-right: solid 1px #edf0f2;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header {
  position: relative;
  background: #fff;
  height: 46px;
  color: #333;
  margin: 0 auto;
  padding: 0;
  border-bottom: solid 1px #ddd;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header .header-label {
  margin: 0 50px;
  text-align: center;
  line-height: 46px;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow {
  font-size: 12px;
  color: #333;
  border: 0;
  background: 0 0;
  cursor: pointer;
  outline: none;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow.arrow1 {
  left: 10px;
  transform: translateY(-50%) rotate(-90deg);
  margin-top: -1px;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow.arrow2 {
  left: 30px;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow.arrow3 {
  right: 30px;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow.arrow4 {
  right: 10px;
  transform: translateY(-50%) rotate(90deg);
  margin-top: -1px;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header > .arrow.disable {
  color: #ccc;
  cursor: not-allowed;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul {
  margin: 0 auto;
  width: 100%;
  height: 46px;
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li {
  cursor: pointer;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li.year-month {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li.arrow {
  width: 30px;
  text-align: center;
  color: #666;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li.arrow i {
  display: block;
  font-size: 16px;
  color: #666;
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li.arrow.arrow-left1 i {
  transform: rotate(-90deg);
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body .el-picker-panel__content .el-date-range-picker__header ul li.arrow.arrow-right1 i {
  transform: rotate(90deg);
}
.vd-ui-daterange-wrapper .vd-ui-daterange-panel .vd-ui-panel__body-wrapper .vd-ui-panel__body::after {
  content: "";
  display: block;
  clear: both;
}
/* 表单内 弹层选择日期 */
.vd-ui-form-date-wrapper .vd-ui-form-date-input-wrap {
  padding-left: 10px;
}
.vd-ui-form-date-wrapper .vd-ui-form-date-input-wrap .date-input-show {
  display: flex;
  align-items: center;
}
.vd-ui-form-date-wrapper .vd-ui-form-date-input-wrap .date-input-show .value {
  flex: 1;
  min-width: 0;
}
.vd-ui-form-date-wrapper .vd-ui-form-date-input-wrap .date-input-show .icon-error2 {
  width: 36px;
  height: 38px;
  flex-shrink: 0;
  font-size: 16px;
  color: #666;
  display: flex;
  justify-content: center;
  align-items: center;
}
.vd-ui-form-date-panel {
  position: relative;
  background: #F5F7FA;
  padding: 10px;
}

.dialog-fade-enter-active {
  transition: all 0.15s ease-out;
}
.dialog-fade-leave-active {
  transition: all 0.15s ease-in;
}
.dialog-fade-enter,
.dialog-fade-leave-to {
  opacity: 0;
}
.dialog-move-enter-active {
  transition: all 0.15s ease-out;
}
.dialog-move-leave-active {
  transition: all 0.15s ease-in;
}
.dialog-move-enter,
.dialog-move-leave-to {
  opacity: 0;
  transform: translate3d(0, -30px, 0);
}
.mui-custom-dialog-container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 2019;
  background-color: rgba(0, 0, 0, 0.6);
  text-align: center;
}
.mui-custom-dialog-container::after {
  content: "";
  display: inline-block;
  height: 100%;
  width: 0;
  vertical-align: middle;
}
.mui-custom-dialog-container .nui-custom-dislog-center {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.mui-custom-dialog-wrapper {
  border-radius: 12px;
  background-color: #fff;
  font-weight: 400;
  line-height: 1.5;
  overflow: hidden;
}
.mui-custom-dialog-wrapper .mui-custom-dialog-content {
  padding: 20px;
}
.mui-custom-dialog-wrapper .mui-custom-dialog-content::after {
  content: "";
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 62px;
}
.mui-custom-dialog-wrapper .mui-custom-dialog-content .mui-custom-dialog-title {
  font-size: 14px;
  color: #000;
  font-weight: 700;
  margin-bottom: 10px;
}
.mui-custom-dialog-wrapper .mui-custom-dialog-content .mui-custom-dialog-msg {
  font-size: 14px;
  color: #000;
}
.mui-custom-dialog-wrapper .mui-custom-dialog-button-choice {
  display: flex;
  border: none;
  border-top: solid 1px #E1E5E8;
}
.mui-custom-dialog-wrapper .mui-custom-dialog-button-choice .mui-cdb {
  flex: 1;
  min-width: 0;
  height: 50px;
  color: #000;
  line-height: 50px;
  background: none;
  border-right: solid 1px #E1E5E8;
  cursor: pointer;
  font-size: 14px;
}
.mui-custom-dialog-wrapper .mui-custom-dialog-button-choice .mui-cdb:last-child {
  border-right: none;
}
.mui-custom-dialog-wrapper .mui-custom-dialog-button-choice .mui-cdb.disabled {
  color: #CCC !important;
  pointer-events: none;
}
.mui-custom-dialog-wrapper .mui-custom-dialog-button-choice .mui-cdb.confirm {
  color: #09f;
}
.mui-custom-dialog-wrapper .mui-custom-dialog-button-choice .mui-cdb.delete {
  color: #E64545;
}
.mui-custom-dialog-wrapper .mui-custom-dialog-button-choice .mui-cdb.cancel {
  color: #333;
}
.msg-fork {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  cursor: pointer;
}
.msg-fork:hover {
  color: #333333;
}
.msg-fork:hover .icon-delete {
  color: #333333;
}
.msg-fork .icon-delete {
  font-size: 24px;
  color: #CCCCCC;
}
.msg-fork .icon-delete:hover {
  color: #333333;
}
.slide-dialog-input-wrap {
  height: 53px;
  padding: 10px;
}
.slide-dialog-input-wrap /deep/ .vd-ui-input-wrap .vd-ui-input {
  padding-top: 0;
  padding-bottom: 0;
}

.ui-form-checkbox-wrap .form-checkbox-inner {
  padding-left: 10px;
}
.ui-form-checkbox-wrap .form-checkbox-inner .form-checkbox-show {
  padding: 10px;
  padding-left: 0;
}
.form-checkbox-panel {
  position: relative;
}

.dialog-fade-enter-active {
  transition: all 0.15s ease-out;
}
.dialog-fade-leave-active {
  transition: all 0.15s ease-in;
}
.dialog-fade-enter,
.dialog-fade-leave-to {
  opacity: 0;
}
.dialog-move-enter-active {
  transition: all 0.15s ease-out;
}
.dialog-move-leave-active {
  transition: all 0.15s ease-in;
}
.dialog-move-enter,
.dialog-move-leave-to {
  opacity: 0;
  transform: translate3d(0, -30px, 0);
}
.mui-dialog-container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 2019;
  background-color: rgba(0, 0, 0, 0.6);
  text-align: center;
}
.mui-dialog-container::after {
  content: "";
  display: inline-block;
  height: 100%;
  width: 0;
  vertical-align: middle;
}
.mui-dialog-container .nui-custom-dislog-center {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.mui-dialog-wrapper {
  position: relative;
  border-radius: 12px;
  background-color: #fff;
  width: 300px;
  font-weight: 400;
  line-height: 1.5;
  overflow: hidden;
}
.mui-dialog-wrapper.success {
  background: linear-gradient(180deg, #dbffdb 0%, #ffffff 100%) no-repeat top #fff;
  background-size: 100% 62px;
}
.mui-dialog-wrapper.error {
  background: linear-gradient(180deg, #ffe5e5 0%, #ffffff 100%) no-repeat top #fff;
  background-size: 100% 62px;
}
.mui-dialog-wrapper.warn {
  background: linear-gradient(180deg, #ffede0 0%, #ffffff 100%) no-repeat top #fff;
  background-size: 100% 62px;
}
.mui-dialog-wrapper.info {
  background: linear-gradient(180deg, #c3e7ff 0%, #ffffff 100%) no-repeat top #fff;
  background-size: 100% 62px;
}
.mui-dialog-wrapper .mui-dialog-content {
  padding: 30px 20px;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-icon {
  margin-bottom: 20px;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-icon > i {
  font-size: 32px;
  height: 32px;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-icon > i.icon-info2 {
  color: #0099FF;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-icon > i.icon-yes2 {
  color: #13bf13;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-icon > i.icon-caution2 {
  color: #ff6600;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-icon > i.icon-error2 {
  color: #e64545;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-title {
  font-size: 16px;
  color: #000;
  font-weight: 700;
  margin-bottom: 20px;
}
.mui-dialog-wrapper .mui-dialog-content .mui-dialog-middle {
  font-size: 14px;
  color: #000;
}
.mui-dialog-wrapper .mui-dialog-button-choice {
  height: 50px;
  display: flex;
  border: none;
  border-top: solid 1px #E1E5E8;
  font-size: 14px;
}
.mui-dialog-wrapper .mui-dialog-button-choice .mui-dialog-button {
  flex: 1;
  min-width: 0;
  color: #000;
  line-height: 50px;
  border-right: solid 1px #E1E5E8;
  cursor: pointer;
}
.mui-dialog-wrapper .mui-dialog-button-choice .mui-dialog-button:last-child {
  border-right: none;
}
.mui-dialog-wrapper .mui-dialog-button-choice .mui-dialog-button.disabled {
  color: #CED2D9;
}
.mui-dialog-wrapper .mui-dialog-button-choice .mui-dialog-button.confirm {
  color: #09f;
}
.mui-dialog-wrapper .mui-dialog-button-choice .mui-dialog-button.delete {
  color: #E64545;
}
.mui-dialog-wrapper .mui-dialog-button-choice .mui-dialog-button.cancel {
  color: #333;
}
.msg-fork {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  cursor: pointer;
}
.msg-fork:hover {
  color: #333333;
}
.msg-fork:hover .icon-delete {
  color: #333333;
}
.msg-fork .icon-delete {
  font-size: 24px;
  color: #CCCCCC;
}
.msg-fork .icon-delete:hover {
  color: #333333;
}
.slide-dialog-default-footer {
  height: 53px;
  padding: 10px;
  background: #fff;
  border-top: solid 1px #EBEFF2;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 99;
}
.slide-dialog-default-footer .btn-cancel-flex {
  flex: 17;
  margin-right: 10px;
}
.slide-dialog-default-footer .btn-confirm-flex {
  flex: 25;
}

.vd-ui-input-wrap {
  position: relative;
}
.vd-ui-input-wrap .vd-ui-input {
  width: 100%;
  padding: 10px;
  border-radius: 3px;
  background: transparent;
}
.vd-ui-input-wrap .vd-ui-input::placeholder {
  color: #ccc;
  font-size: 12px;
}
.vd-ui-input-wrap .vd-ui-input.border {
  border: 1px solid #BABFC2;
  padding: 5px 9px;
  font-size: 14px;
  background: #fff;
}
.vd-ui-input-wrap .vd-ui-input.border::placeholder {
  font-size: 14px;
}
.vd-ui-input-wrap .vd-ui-input.border:focus {
  border-color: #09f;
}
.vd-ui-input-wrap .icon {
  position: absolute;
  width: 36px;
  height: calc(100% - 2px);
  background: #fff;
  right: 1px;
  top: 1px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #666;
  border-radius: 3px;
}
.vd-ui-textarea-wrap {
  position: relative;
  padding: 10px;
}
.vd-ui-textarea-wrap .vd-ui-textarea {
  width: 100%;
  border-radius: 3px;
  background: transparent;
}
.vd-ui-textarea-wrap .vd-ui-textarea::placeholder {
  color: #ccc;
  font-size: 12px;
}
.vd-ui-textarea-wrap .vd-ui-textarea.border {
  border: 1px solid #BABFC2;
  padding: 5px 9px;
  font-size: 14px;
  background: #fff;
}
.vd-ui-textarea-wrap .vd-ui-textarea.border::placeholder {
  font-size: 14px;
}
.vd-ui-textarea-wrap .vd-ui-textarea.border:focus {
  border-color: #09f;
}
.vd-ui-textarea-wrap .vd-ui-textarea-count {
  color: #999;
  position: absolute;
  top: -23px;
  right: 10px;
  text-align: right;
  white-space: nowrap;
}
.vd-ui-textarea-wrap .vd-ui-textarea-count.upper-limit {
  color: #f60;
}

/* form */
.form-wrap {
  position: relative;
}
.form-fixed-bottom {
  width: 100%;
  max-width: 768px;
  min-width: 320px;
  background: #fff;
  position: fixed;
  bottom: 0;
  padding-bottom: env(safe-area-inset-bottom);
  left: 50%;
  transform: translateX(-50%);
  z-index: 99;
  border-top: solid 1px #EBEFF2;
}
.form-fixed-bottom .submit-btn {
  height: 53px;
}
.form-fixed-bottom .submit-btn.padding {
  padding: 10px;
}
.form-fixed-bottom.z-index-top {
  z-index: 9999;
}
.form-section {
  padding: 0 10px;
  margin-bottom: 10px;
}
.form-section .form-section-title {
  position: relative;
  height: 41px;
  padding: 10px 5px;
  font-size: 14px;
  font-weight: 700;
}
.form-section .form-section-title .section-title-btn {
  height: 100%;
  color: #09f;
  display: flex;
  align-items: center;
  position: absolute;
  right: 0;
  top: 0px;
}
.form-section .form-section-title .section-title-btn > i {
  font-size: 16px;
  margin-right: 5px;
}
.form-section .form-section-title .section-title-btn > span {
  font-size: 12px;
  font-weight: 400;
}
.form-card {
  background: #fff;
  border-radius: 5px;
}
.form-card.mb10 {
  margin-bottom: 10px;
}
.form-item {
  display: flex;
  border-bottom: solid 1px #F5F7FA;
}
.form-item:last-child {
  border: none;
}
.form-item .form-label {
  width: 100px;
  flex-shrink: 0;
  color: #000;
  line-height: 18px;
  white-space: nowrap;
  padding: 10px;
  padding-right: 0px;
}
.form-item .form-label.middle {
  display: flex;
  align-items: center;
}
.form-item .form-label .must {
  color: #e64545;
}
.form-item .form-fields {
  position: relative;
  flex: 1;
  min-width: 0;
  word-break: break-all;
  padding: 10px;
}
.form-item .form-fields.no-padding {
  padding: 0;
}
.form-item .form-fields .vd-ui-input-error {
  color: #E64545;
  margin-top: 5px;
  font-size: 0;
  white-space: nowrap;
}
.form-item .form-fields .vd-ui-input-error .vd-ui-input-error--icon {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
  vertical-align: -2px;
}
.form-item .form-fields .vd-ui-input-error .vd-ui-input-error--errmsg {
  font-size: 14px;
  margin: 0px;
  display: inline-block;
}
.form-item.vertical {
  display: block;
}
.form-item.vertical .form-label {
  width: 100%;
  padding-bottom: 5px;
}
.form-item.vertical .form-fields {
  width: 100%;
  margin: 0;
}
.form-tip {
  display: flex;
  font-size: 12px;
}
.form-tip i {
  height: 16px;
  font-size: 16px;
  margin-right: 5px;
  padding-top: 2px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.form-tip.success {
  color: #13BF13;
}
.form-tip.warn {
  color: #F60;
}
.form-tip.error {
  color: #E64545;
}
.form-tip.info {
  color: #09f;
}
.form-tip.map,
.form-tip.tel {
  color: #09F;
}
.form-tip.style2 {
  padding: 10px;
}
.form-tip.style2.success {
  color: #000;
  background: #c5ffc5;
}
.form-tip.style2.success i {
  color: #13BF13;
}
.form-tip.style2.warn {
  color: #000;
  background: #ffbd91;
}
.form-tip.style2.warn i {
  color: #F60;
}
.form-tip.style2.error {
  color: #000;
  background: #FCE9E9;
}
.form-tip.style2.error i {
  color: #E64545;
}
.form-tip.style2.info {
  color: #000;
  background: #E0F3FF;
}
.form-tip.style2.info i {
  color: #09F;
}
/* 表单逻辑相关 */
.form-placeholder {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}
.form-placeholder > span {
  font-size: 12px;
  color: #ccc;
}
.form-placeholder > i {
  width: 36px;
  height: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  color: #666;
}

.ui-global-toast {
  position: fixed;
  top: 110px;
  z-index: 99999;
  width: 100%;
  display: flex;
  justify-content: center;
}
.ui-global-toast .ui-global-toast-inner {
  max-width: 80vw;
  margin: 0 auto;
  padding: 12px 20px 12px 15px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: rgba(0, 0, 0, 0.08) 0px 7px 14px;
  display: flex;
}
.ui-global-toast .ui-global-toast-inner.ui-message-success {
  background: #E3F7E3;
}
.ui-global-toast .ui-global-toast-inner.ui-message-error {
  background: #FCE9E9;
}
.ui-global-toast .ui-global-toast-inner.ui-message-warn {
  background: #FFEDE0;
}
.ui-global-toast .ui-global-toast-inner.ui-message-info {
  background: #E0F3FF;
}
.ui-global-toast .ui-global-toast-inner > i {
  height: 20px;
  position: relative;
  font-size: 20px;
  line-height: 1;
}
.ui-global-toast .ui-global-toast-inner .icon-yes2 {
  color: #13BF13;
}
.ui-global-toast .ui-global-toast-inner .icon-error2 {
  color: #E64545;
}
.ui-global-toast .ui-global-toast-inner .icon-info2 {
  color: #09f;
}
.ui-global-toast .ui-global-toast-inner .icon-caution2 {
  color: #f60;
}
.ui-global-toast .ui-global-toast-inner > span {
  font-size: 14px;
  color: #000;
  line-height: 20px;
  max-width: 320px;
  margin-left: 10px;
}
.globalToast-enter-active {
  transition: 220ms ease-out;
}
.globalToast-leave-active {
  transition: 190ms ease-in;
}
.globalToast-enter,
.globalToast-leave-to {
  opacity: 0;
  transform: translate(0, -30px);
}
.popup-fade-enter-active {
  transition: all 0.15s ease-out;
}
.popup-fade-leave-active {
  transition: all 0.15s ease-in;
}
.popup-fade-enter,
.popup-fade-leave-to {
  opacity: 0;
}
.popup-move-enter-active {
  transition: all 0.15s ease-out;
}
.popup-move-leave-active {
  transition: all 0.15s ease-in;
}
.popup-move-enter,
.popup-move-leave-to {
  opacity: 0;
  transform: translate3d(0, -30px, 0);
}
.ui-popup-message-box-wrapper {
  position: fixed;
  z-index: 2019;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  text-align: center;
}
.ui-popup-message-box-wrapper::after {
  content: "";
  display: inline-block;
  height: 100%;
  width: 0;
  vertical-align: middle;
}
.ui-popup-message-box {
  display: inline-block;
  width: 480px;
  min-height: 128px;
  background-color: #FFFFFF;
  border-radius: 5px;
  vertical-align: middle;
  text-align: left;
}
.ui-popup-message-box .msg-title {
  display: flex;
  justify-content: flex-end;
  height: 30px;
}
.ui-popup-message-box .msg-fork {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  cursor: pointer;
}
.ui-popup-message-box .msg-fork:hover {
  color: #333333;
}
.ui-popup-message-box .msg-fork:hover .icon-delete {
  color: #333333;
}
.ui-popup-message-box .msg-fork .icon-delete {
  font-size: 24px;
  color: #CCCCCC;
}
.ui-popup-message-box .msg-fork .icon-delete:hover {
  color: #333333;
}
.ui-popup-message-box .msg-content {
  display: flex;
  padding: 0 20px;
}
.ui-popup-message-box .msg-content .vd-ui_icon {
  font-size: 32px;
  line-height: 1;
}
.ui-popup-message-box .msg-content .vd-ui_icon.icon-info2 {
  color: #0099FF;
}
.ui-popup-message-box .msg-content .vd-ui_icon.icon-yes2 {
  color: #13bf13;
}
.ui-popup-message-box .msg-content .vd-ui_icon.icon-caution2 {
  color: #ff6600;
}
.ui-popup-message-box .msg-content .vd-ui_icon.icon-error2 {
  color: #e64545;
}
.ui-popup-message-box .msg-content .msg-tip-title {
  width: 398px;
  padding-top: 2px;
  padding-left: 10px;
  padding-right: 20px;
  font-size: 18px;
  font-weight: 700;
}
.ui-popup-message-box .msg-content .msg-tip-word {
  padding-top: 5px;
  padding-left: 10px;
  word-break: break-all;
}
.ui-popup-message-box .msg-button-choice {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
  padding-top: 20px;
  padding-bottom: 20px;
}
.ui-popup-message-box .msg-button-choice .vd-button {
  border: 1px solid #BABFC2;
  cursor: pointer;
  border-radius: 3px;
  padding: 5px 14px;
  background-color: #F5F7FA;
  margin-left: 10px;
}
.ui-popup-message-box .msg-button-choice .vd-button:hover {
  background-color: #EDF0F2;
}
.ui-popup-message-box .msg-button-choice .vd-button.confirm {
  border-color: #09f;
  background-color: #09f;
  color: #fff;
}
.ui-popup-message-box .msg-button-choice .vd-button.confirm:hover {
  border-color: #008ae5;
  background-color: #008ae5;
}
.ui-popup-message-box .msg-button-choice .vd-button.cannel {
  border: 1px solid #CED2D9;
}
.ui-popup-message-box .msg-button-choice .vd-button.cannel:hover {
  border: solid 1px #B6BABF;
}
.ui-popup-message-box .msg-button-choice .vd-button.delete {
  border-color: #E64545;
  background-color: #E64545;
  color: #fff;
}
.ui-popup-message-box .msg-button-choice .vd-button.delete:hover {
  border-color: #cc2929;
  background-color: #cc2929;
}

.other-contact-panel .other-contact-list .other-contact-item {
  padding: 10px;
  display: flex;
  align-items: center;
  border-bottom: solid 1px #F5F7FA;
}
.other-contact-panel .other-contact-list .other-contact-item:last-child {
  border-bottom: none;
}
.other-contact-panel .other-contact-list .other-contact-item .select {
  height: 18px;
  display: flex;
  align-items: center;
  margin-right: 5px;
}
.other-contact-panel .other-contact-list .other-contact-item .select .icon-radio3 {
  font-size: 16px;
  color: #09f;
}
.other-contact-panel .other-contact-list .other-contact-item .select .icon-radio1 {
  font-size: 16px;
  color: #ccc;
}
.other-contact-panel .other-contact-list .other-contact-item .label {
  margin-right: 15px;
}
.other-contact-panel .other-contact-list .other-contact-item .input- {
  flex: 1;
  min-width: 0;
}

.crm-m-list-layout .crm-m-list-header {
  background: #fff;
  display: flex;
  justify-content: space-between;
  border-bottom: solid 1px #EBEFF2;
  position: fixed;
  max-width: 768px;
  width: 100%;
  top: 0;
  z-index: 11;
}
.crm-m-list-layout .crm-m-list-header .crm-m-list-tab {
  position: relative;
  padding: 10px 0 10px 10px;
}
.crm-m-list-layout .crm-m-list-header .crm-m-list-tab .crm-m-list-tab-txt {
  font-size: 14px;
  display: flex;
  align-items: center;
}
.crm-m-list-layout .crm-m-list-header .crm-m-list-tab .crm-m-list-tab-txt .icon-down {
  font-size: 16px;
  margin-left: 5px;
  color: #666;
  transition: transform 0.22s ease;
}
.crm-m-list-layout .crm-m-list-header .crm-m-list-tab .crm-m-list-tab-txt.show {
  color: #09f;
}
.crm-m-list-layout .crm-m-list-header .crm-m-list-tab .crm-m-list-tab-txt.show .icon-down {
  color: #666;
  transform: rotate(180deg);
}
.crm-m-list-layout .crm-m-list-header .crm-m-list-header-options {
  display: flex;
  align-items: center;
}
.crm-m-list-layout .crm-m-list-header .crm-m-list-header-options .header-option-item {
  font-size: 20px;
  padding: 10px 0;
  line-height: 1;
  color: #666;
  position: relative;
}
.crm-m-list-layout .crm-m-list-header .crm-m-list-header-options .header-option-item.option-sort {
  padding-left: 10px;
  padding-right: 6px;
}
.crm-m-list-layout .crm-m-list-header .crm-m-list-header-options .header-option-item.option-sort.show {
  color: #09f;
}
.crm-m-list-layout .crm-m-list-header .crm-m-list-header-options .header-option-item.option-filter {
  padding-left: 6px;
  padding-right: 10px;
}
.crm-m-list-layout .crm-m-list-header .crm-m-list-header-options .header-option-item .header-option-filter-num {
  position: absolute;
  padding: 0 5px;
  height: 16px;
  border-radius: 8px;
  background: #b3e1ff;
  color: #09f;
  text-align: center;
  line-height: 16px;
  font-size: 12px;
  top: 2px;
  right: 4px;
}
.crm-m-list-layout .crm-m-list-header .crm-m-list-header-options .header-option-item .header-option-filter-num .num-txt {
  transform: scale(0.83);
}
.crm-m-list-layout .crm-m-tab-wrap {
  padding-top: 44px;
  padding-bottom: 5px;
}
.crm-m-list-layout .crm-m-tab-item {
  padding-left: 10px;
  height: 41px;
  font-size: 14px;
  border-bottom: solid 1px #F5F7FA;
  display: flex;
  align-items: center;
}
.crm-m-list-layout .crm-m-tab-item:last-child {
  border-bottom: 0;
}
.crm-m-list-layout .crm-m-tab-item .crm-m-tab-item-txt {
  flex: 1;
}
.crm-m-list-layout .crm-m-tab-item .vd-ui_icon {
  width: 36px;
  height: 41px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 16px;
}
.crm-m-list-layout .crm-m-tab-item.active .crm-m-tab-item-txt {
  color: #09f;
}
.crm-m-list-layout .crm-m-sort-wrap {
  padding-top: 44px;
  padding-bottom: 5px;
}
.crm-m-list-layout .crm-m-sort-item {
  padding: 10px;
  border-bottom: solid 1px #F5F7FA;
  font-size: 14px;
}
.crm-m-list-layout .crm-m-sort-item:last-child {
  border-bottom: 0;
}
.crm-m-list-layout .crm-m-sort-item.active {
  color: #09f;
}
.crm-m-list-layout .crm-m-list-cnt {
  padding-top: 42px;
}
.crm-m-list-layout .crm-m-list-cnt .crm-m-list-inner {
  padding: 10px;
}
.crm-m-list-layout .crm-m-list-cnt .crm-m-list-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 10px;
  color: #09f;
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.crm-m-list-layout .crm-m-list-cnt .crm-m-list-loading .icon-loading {
  font-size: 16px;
  animation: loading 2s linear infinite;
  margin-right: 5px;
}
.crm-m-list-layout .crm-m-list-cnt .crm-m-list-empty {
  padding-top: 100px;
  text-align: center;
}
.crm-m-list-layout .crm-m-list-cnt .crm-m-list-empty .crm-m-list-empty-pic {
  width: 223px;
  height: 120px;
  display: inline-block;
  margin-bottom: 15px;
}
.crm-m-list-layout .crm-m-list-cnt .crm-m-list-empty .crm-m-list-empty-pic img {
  max-width: 100%;
  max-height: 100%;
}
.crm-m-list-layout .crm-m-list-cnt .crm-m-list-empty .crm-m-list-empty-txt {
  color: #999;
}
.crm-m-list-layout .crm-list-add {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background: #09f;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  position: fixed;
  z-index: 5;
  bottom: 50px;
  right: 10px;
  box-shadow: 0 3px 5px rgba(0, 153, 255, 0.2);
}
.crm-list-filter-wrap .crm-list-filter-cnt {
  padding: 10px;
  background: #F5F7FA;
  max-height: calc(100vh - 147px);
  max-height: calc(100vh - 147px - constant(safe-area-inset-bottom));
  max-height: calc(100vh - 147px - env(safe-area-inset-bottom));
  overflow: auto;
}
.crm-list-filter-wrap .crm-list-filter-cnt .crm-m-search-item {
  background: #fff;
  border-radius: 5px;
  display: flex;
  margin-bottom: 5px;
  overflow: hidden;
}
.crm-list-filter-wrap .crm-list-filter-cnt .crm-m-search-item:last-child {
  margin-bottom: 0;
}
.crm-list-filter-wrap .crm-list-filter-cnt .crm-m-search-item .crm-m-search-item-title {
  padding: 10px;
  width: 100px;
}
.crm-list-filter-wrap .crm-list-filter-cnt .crm-m-search-item .crm-m-search-item-cnt {
  flex: 1;
}
.crm-list-filter-wrap .crm-list-filter-cnt .crm-m-search-item.vertical {
  display: block;
}
.crm-list-filter-wrap .crm-list-filter-cnt .crm-m-search-item.vertical .crm-m-search-item-title {
  padding-bottom: 5px;
}
.crm-list-filter-wrap .crm-list-filter-cnt .crm-m-search-item.vertical .vd-ui-select {
  padding-top: 5px;
}
.crm-list-filter-wrap .crm-list-filter-cnt .crm-m-search-item.vertical .vd-ui-select .vd-ui-select-trigger.multi-select {
  padding-top: 0;
}
.crm-list-filter-wrap .crm-list-filter-cnt .crm-m-search-item.vertical .vd-ui-select .vd-ui-select-trigger.multi-select .vd-ui-icon-trigger {
  margin-right: -10px;
}
.crm-list-filter-wrap .crm-list-filter-cnt .crm-m-search-item .vd-ui-checkbox-group,
.crm-list-filter-wrap .crm-list-filter-cnt .crm-m-search-item .vd-ui-radio-group {
  padding: 5px 10px 10px 10px;
}
.crm-list-filter-wrap .crm-list-filter-cnt .crm-m-search-item.active {
  background: #E0F3FF;
}
.crm-list-filter-wrap .crm-list-filter-footer {
  display: flex;
  align-items: center;
  border-top: solid 1px #EBEFF2;
}
.crm-list-filter-wrap .crm-list-filter-footer .filter-footer-option {
  flex: 1.5;
  text-align: center;
}
.crm-list-filter-wrap .crm-list-filter-footer .filter-footer-option .option-icon {
  width: 20px;
  height: 20px;
  background-size: 100% 100%;
  display: inline-block;
  vertical-align: top;
}
.crm-list-filter-wrap .crm-list-filter-footer .filter-footer-option .option-icon.option-add {
  background-image: url(/mstatic/mui/image/filter-icon-add.svg);
}
.crm-list-filter-wrap .crm-list-filter-footer .filter-footer-option .option-icon.option-reset {
  background-image: url(/mstatic/mui/image/filter-icon-reset.svg);
}
.crm-list-filter-wrap .crm-list-filter-footer .filter-footer-option.disabled {
  color: #ccc;
}
.crm-list-filter-wrap .crm-list-filter-footer .filter-footer-option.disabled .option-icon.option-add {
  background-image: url(/mstatic/mui/image/filter-icon-add-disabled.svg);
}
.crm-list-filter-wrap .crm-list-filter-footer .filter-footer-btns {
  display: flex;
  align-items: center;
  flex: 7;
  padding: 10px 0;
}
.crm-list-filter-wrap .crm-list-filter-footer .filter-footer-btns .btn-cancel-flex {
  flex: 3;
  padding-right: 10px;
}
.crm-list-filter-wrap .crm-list-filter-footer .filter-footer-btns .btn-confirm-flex {
  flex: 4;
  padding-right: 10px;
}
.crm-list-filter-setting {
  background: #F5F7FA;
}
.crm-list-filter-setting .filter-setting-list {
  padding: 10px;
  max-height: calc(100vh - 147px);
  max-height: calc(100vh - 147px - constant(safe-area-inset-bottom));
  max-height: calc(100vh - 147px - env(safe-area-inset-bottom));
  overflow: auto;
}
.crm-list-filter-setting .filter-setting-list .filter-setting-item {
  margin-top: 5px;
  border-radius: 5px;
  background: #fff;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.crm-list-filter-setting .filter-setting-list .filter-setting-item:first-child {
  margin-top: 0;
}
.crm-list-filter-setting .filter-setting-list .filter-setting-item.active {
  background: #E0F3FF;
}
.crm-list-filter-setting .filter-setting-list .filter-setting-item.placehodler {
  opacity: 0.3;
}
.crm-list-filter-setting .filter-setting-footer {
  display: flex;
  align-items: center;
  padding: 10px 0;
  background: #fff;
  border-top: solid 1px #EBEFF2;
}
.crm-list-filter-setting .filter-setting-footer .btn-cancel-flex {
  flex: 2;
  padding-left: 10px;
}
.crm-list-filter-setting .filter-setting-footer .btn-confirm-flex {
  flex: 3;
  padding: 0 10px;
}
.crm-filter-add-wrap {
  text-align: left;
}
.crm-filter-add-wrap .crm-filter-form-wrap {
  display: flex;
  align-items: center;
  border-bottom: solid 1px #E1E5E8;
}
.crm-filter-add-wrap .crm-filter-form-wrap .crm-filter-form-label {
  display: flex;
  align-items: center;
  width: 72px;
  white-space: nowrap;
}
.crm-filter-add-wrap .crm-filter-form-wrap .crm-filter-form-label .must {
  color: #e64545;
}
.crm-filter-add-wrap .crm-filter-form-tip {
  color: #999;
  margin-top: 10px;
}

/* 线索/商机卡片 */
.business-card .business-top {
  border-bottom: solid 1px #F5F7FA;
  padding: 10px;
}
.business-card .business-top .row {
  display: flex;
  justify-content: space-between;
}
.business-card .business-top .row.mt {
  margin-top: 5px;
}
.business-card .business-top .row .business-trader {
  flex: 1;
  min-width: 0;
  margin-right: 10px;
  display: flex;
  align-items: center;
}
.business-card .business-top .row .business-trader .trader-name {
  font-size: 14px;
  color: #000;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.business-card .business-top .row .business-trader .icon-tianyancha {
  width: 38px;
  flex-shrink: 0;
  font-size: 16px;
  color: #0084FF;
  display: flex;
  justify-content: center;
  align-items: center;
}
.business-card .business-top .row .business-status {
  flex-shrink: 0;
}
.business-card .business-top .row .business-status .leads-status {
  height: 22px;
  font-size: 12px;
  line-height: 22px;
  padding: 0 5px;
  border-radius: 3px;
}
.business-card .business-top .row .business-status .leads-status.s0,
.business-card .business-top .row .business-status .leads-status.s1 {
  background: #FFEDE0;
  color: #FF6600;
}
.business-card .business-top .row .business-status .leads-status.s2 {
  background: #E0F3FF;
  color: #09F;
}
.business-card .business-top .row .business-status .leads-status.s4 {
  background: #E3F7E3;
  color: #13BF13;
}
.business-card .business-top .row .business-status .leads-status.s3 {
  background: #E3EAF0;
  color: #1A4D80;
}
.business-card .business-top .row .business-status .change-status {
  height: 22px;
  font-size: 12px;
  line-height: 22px;
  padding: 0 5px;
  border-radius: 3px;
}
.business-card .business-top .row .business-status .change-status.s1,
.business-card .business-top .row .business-status .change-status.s2 {
  background: #FFEDE0;
  color: #FF6600;
}
.business-card .business-top .row .business-status .change-status.s3,
.business-card .business-top .row .business-status .change-status.s4 {
  background: #E0F3FF;
  color: #09F;
}
.business-card .business-top .row .business-status .change-status.s5 {
  background: #E3F7E3;
  color: #13BF13;
}
.business-card .business-top .row .business-status .change-status.s6 {
  background: #E3EAF0;
  color: #1A4D80;
}
.business-card .business-top .row .business-no {
  font-size: 12px;
  color: #999;
}
.business-card .business-top .row .business-time {
  font-size: 12px;
  color: #999;
  margin-left: 10px;
}
.business-card .business-bottom {
  padding: 10px;
}
.business-card .business-bottom .business-attrs {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -5px;
}
.business-card .business-bottom .business-attrs .item-attr {
  flex: 0 0 50%;
  min-width: 0;
  font-size: 12px;
  display: flex;
  margin-bottom: 5px;
}
.business-card .business-bottom .business-attrs .item-attr:nth-child(2n-1) {
  padding-right: 5px;
}
.business-card .business-bottom .business-attrs .item-attr .label {
  color: #999;
  flex-shrink: 0;
}
.business-card .business-bottom .business-attrs .item-attr .value {
  flex: 1;
  min-width: 0;
}
.business-card .business-bottom .pro-name {
  font-size: 12px;
  margin-top: 5px;
}
.business-card .business-bottom .trader-check {
  margin-top: 10px;
}

.vd-ui-phone-related .phone-related-show-wrap {
  position: relative;
  width: 100%;
  padding-left: 10px;
}
.vd-ui-phone-related .phone-related-show-wrap .phone-related-show.pb {
  padding-bottom: 10px;
}
.vd-ui-phone-related .phone-related-show-wrap .phone-related-show .phone-value {
  font-size: 12px;
  color: #000;
  height: 38px;
  line-height: 38px;
}
.vd-ui-phone-related .phone-related-show-wrap .phone-placeholder {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.vd-ui-phone-related .phone-related-show-wrap .phone-placeholder > span {
  font-size: 12px;
  color: #ccc;
}
.vd-ui-phone-related .phone-related-show-wrap .phone-placeholder > i {
  font-size: 16px;
  color: #666;
}
.phone-related-panel {
  height: calc(100vh - 98px);
  height: calc(100vh - 98px - constant(safe-area-inset-bottom));
  height: calc(100vh - 98px - env(safe-area-inset-bottom));
}
.phone-related-panel .phone-related-ul {
  height: calc(100% - 53px - 53px);
  overflow-y: auto;
}
.phone-related-panel .phone-related-ul .related-list .pr-item {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #000;
  border-bottom: solid 1px #F5F7FA;
}
.phone-related-panel .phone-related-ul .related-list .pr-item.active {
  background: #E0F3FF;
}
.phone-related-panel .phone-related-ul .related-list .pr-item .name {
  flex: 1;
  min-width: 0;
  margin-right: 10px;
}
.phone-related-panel .phone-related-ul .related-list .pr-item .mobile {
  width: 90px;
  flex-shrink: 0;
  white-space: nowrap;
  text-align: right;
}

.ui-map-iframe-wrap iframe {
  width: 100%;
  height: calc(100vh - 94px);
  border: 0;
}

/* Common */
.erp-loading {
  color: #666;
  height: 39px;
  line-height: 37px;
  padding: 0px 10px;
  overflow: hidden;
}
.erp-loading i {
  animation: loading 1.8s linear infinite;
  display: inline-block;
  position: relative;
  top: 1px;
  font-size: 16px;
  margin-right: 5px;
  color: #09F;
}
.erp-load-fail {
  height: 39px;
  line-height: 39px;
  padding: 0px 10px;
}
.erp-load-fail i {
  position: relative;
  top: 2px;
  font-size: 16px;
  color: #E64545;
  margin-right: 5px;
}
.erp-load-empty {
  text-align: center;
  padding: 100px 0;
}
.erp-load-empty > i {
  display: block;
  font-size: 32px;
  color: #09f;
  margin-bottom: 10px;
}
.erp-load-empty > img {
  width: 223px;
  height: 120px;
  margin-bottom: 14px;
}
.erp-load-empty > p {
  color: #999;
}
/* 客户名称 */
.trader-show-wrap {
  padding-left: 10px;
}
.trader-show-wrap .trader-show {
  overflow: hidden;
}
.trader-show-wrap .trader-show .input-value {
  font-size: 12px;
  color: #000;
  height: 38px;
  line-height: 38px;
  padding-right: 38px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.trader-show-wrap .trader-show .other-info {
  padding-right: 10px;
  margin-top: -5px;
  margin-bottom: 10px;
}
.trader-show-wrap .trader-show .other-info .other-item {
  color: #999;
}
.trader-show-wrap .trader-icon {
  width: 38px;
  height: 38px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 9;
}
.trader-show-wrap .trader-icon > .icon-tianyancha {
  font-size: 16px;
  color: #09f;
}
.trader-show-wrap .trader-icon > .icon-search {
  font-size: 16px;
  color: #666;
}
/* 客户名称erp查询弹层 */
.trader-erp-panel .erp-search-list {
  height: calc(100vh - 201px);
  height: calc(100vh - 201px - constant(safe-area-inset-bottom));
  height: calc(100vh - 201px - env(safe-area-inset-bottom));
}
.trader-erp-panel .erp-search-list .search-related {
  height: 100%;
}
.trader-erp-panel .erp-search-list .search-related .related-list {
  height: 100%;
  overflow-y: auto;
  border-bottom: solid 1px #EBEFF2;
}
.trader-erp-panel .erp-search-list .search-related .related-list .local-data {
  font-size: 12px;
  color: #999;
  line-height: 30px;
  padding: 0 10px;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item {
  padding: 10px;
  display: flex;
  align-items: center;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item .related-item-left {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item .related-item-left .trader-select {
  height: 16px;
  flex-shrink: 0;
  margin-right: 5px;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item .related-item-left .trader-select > i {
  font-size: 16px;
  color: #ccc;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item .related-item-left .icon {
  font-size: 16px;
  color: #0084FF;
  margin-left: 5px;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item .related-item-right {
  width: 90px;
  margin-left: 10px;
  flex-shrink: 0;
  color: #999;
  text-align: right;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item.active {
  background: #E0F3FF;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item.active .related-item-left .trader-select > i {
  color: #09f;
}
/* 客户等级 */
.ui-trader-level-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  border-radius: 3px;
}
.ui-trader-level-icon.level-S {
  background: url('/mstatic/image/traderLevel/S.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-S1 {
  background: url('/mstatic/image/traderLevel/S1.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-S2 {
  background: url('/mstatic/image/traderLevel/S2.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-A1 {
  background: url('/mstatic/image/traderLevel/A1.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-A2 {
  background: url('/mstatic/image/traderLevel/A2.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-A3 {
  background: url('/mstatic/image/traderLevel/A3.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-B1 {
  background: url('/mstatic/image/traderLevel/B1.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-B2 {
  background: url('/mstatic/image/traderLevel/B2.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-B3 {
  background: url('/mstatic/image/traderLevel/B3.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-C1 {
  background: url('/mstatic/image/traderLevel/C1.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-C2 {
  background: url('/mstatic/image/traderLevel/C2.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-C3 {
  background: url('/mstatic/image/traderLevel/C3.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-D {
  background: url('/mstatic/image/traderLevel/D.svg') no-repeat;
  background-size: cover;
}
/* 天眼查列表 */
.tyc-list-panel {
  height: calc(100vh - 45px - 50px);
}
.tyc-list-panel .tyc-list-wrap {
  background: #F5F7FA;
  padding: 10px;
  height: calc(100vh - 45px - 53px - 53px);
  overflow-y: auto;
}
.tyc-list-panel .tyc-list-wrap .tyc-list {
  position: relative;
  overflow-y: auto;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item {
  background: #fff;
  border-radius: 5px;
  padding: 10px 10px 5px 0;
  display: flex;
  margin-bottom: 5px;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item:last-child {
  margin-bottom: 0;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item .tyc-select {
  width: 36px;
  flex-shrink: 0;
  font-size: 16px;
  color: #ccc;
  text-align: center;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item.active {
  background: #E0F3FF;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item.active .tyc-select {
  color: #09f;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item .item-right {
  flex: 1;
  min-width: 0;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item .item-right .name {
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 5px;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item .item-right .attrs {
  display: flex;
  flex-wrap: wrap;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item .item-right .attrs .attr-item {
  flex: 0 0 50%;
  min-width: 0;
  font-size: 12px;
  font-weight: 400;
  color: #000;
  margin-bottom: 5px;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item .item-right .attrs .attr-item .label {
  color: #aaa;
}
/* 天眼查详情 */
.tyc-detail-panel {
  max-height: calc(100vh - 45px - 53px);
  overflow-y: auto;
}
.tyc-detail-panel .tyc-detail-top {
  background: #fff;
  padding: 10px 15px;
}
.tyc-detail-panel .tyc-detail-top .tyc-name {
  font-size: 14px;
  font-weight: 700;
}
.tyc-detail-panel .tyc-detail-top .tyc-tags {
  font-size: 0;
  margin-top: 10px;
  margin-bottom: -5px;
  display: flex;
  flex-wrap: wrap;
}
.tyc-detail-panel .tyc-detail-top .tyc-tags .tag {
  padding: 2px 5px;
  background: #E0F3FF;
  border-radius: 2px;
  font-size: 12px;
  color: #09f;
  margin-right: 5px;
  margin-bottom: 5px;
}
.tyc-detail-panel .tyc-detail-bottom {
  padding: 10px;
  background: #F5F7FA;
}
.tyc-detail-panel .tyc-detail-bottom .tyc-attr {
  background: #fff;
  border-radius: 5px;
  padding-right: 10px;
}
/* 详情·客户名称回显 */
.vd-ui-trader-name-wrap .trader-name-wrap {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.vd-ui-trader-name-wrap .trader-name-wrap .vd-ui-trader-txt {
  flex: 1;
  min-width: 0;
  margin-right: 10px;
}
.vd-ui-trader-name-wrap .trader-name-wrap .vd-ui_icon {
  width: 32px;
  flex-shrink: 0;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.vd-ui-trader-name-wrap .trader-name-wrap .vd-ui_icon.icon-tyc {
  color: #0084FF;
  padding-left: 4px;
}
.vd-ui-trader-name-wrap .trader-name-wrap .vd-ui_icon.icon-baidu {
  color: #2932E1;
  padding-right: 4px;
}

.vd-ui-select,
.vd-ui-wxuser-select {
  width: 100%;
}
.vd-ui-select .vd-ui-select-trigger,
.vd-ui-wxuser-select .vd-ui-select-trigger {
  display: flex;
  align-items: center;
  padding: 10px;
  position: relative;
}
.vd-ui-select .vd-ui-select-trigger.multi-select,
.vd-ui-wxuser-select .vd-ui-select-trigger.multi-select {
  align-items: stretch;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-trigger-txt,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-trigger-txt {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: var(--line);
  -webkit-box-orient: vertical;
  word-break: break-all;
  flex: 1;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-trigger-txt.placeholder,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-trigger-txt.placeholder {
  color: #ccc;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags {
  flex: 1;
  max-height: calc(35px * var(--line) - 5px);
  overflow: hidden;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner {
  display: flex;
  flex-wrap: wrap;
  margin-top: -5px;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 3px;
  padding-left: 10px;
  margin-right: 5px;
  margin-top: 5px;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag.tag-avatar,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag.tag-avatar {
  padding-left: 5px;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag .vd-ui-select-tag-avatar,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag .vd-ui-select-tag-avatar {
  margin-right: 5px;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag .vd-ui-select-tag-avatar img,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag .vd-ui-select-tag-avatar img {
  width: 18px;
  height: 18px;
  border-radius: 3px;
  object-fit: cover;
  overflow: hidden;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag .icon-delete,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag .icon-delete {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 16px;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag-more,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag-more {
  padding: 0 10px;
  height: 30px;
  display: flex;
  align-items: center;
  margin-top: 5px;
  background: #F5F7FA;
  border-radius: 3px;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-icon-trigger,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-icon-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-icon-trigger .icon-app-right,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-icon-trigger .icon-app-right {
  margin-left: 0;
}
.vd-ui-select .vd-ui-select-trigger .icon-app-right,
.vd-ui-wxuser-select .vd-ui-select-trigger .icon-app-right {
  font-size: 16px;
  color: #666;
  margin-left: 10px;
}
.vd-ui-wxuser-select .vd-ui-select-trigger.multi-select {
  padding: 4px 10px;
}
.vd-ui-wxuser-select .vd-ui-select-trigger.multi-select .icon-app-right {
  line-height: 30px;
}
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-trigger-txt.placeholder {
  line-height: 30px;
}
.vd-ui-select-options .vd-ui-select-options-search {
  padding: 10px;
}
.vd-ui-select-options .vd-ui-select-options-list {
  max-height: calc(100vh - 147px);
  max-height: calc(100vh - 147px - constant(safe-area-inset-bottom));
  max-height: calc(100vh - 147px - env(safe-area-inset-bottom));
  overflow: auto;
}
.vd-ui-select-options .vd-ui-select-options-list.filter {
  max-height: calc(100vh - 200px);
  height: calc(100vh - 200px);
  height: calc(100vh - 200px - constant(safe-area-inset-bottom));
  height: calc(100vh - 200px - env(safe-area-inset-bottom));
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item {
  padding: 10px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #F5F7FA;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item:last-child {
  border-bottom: 0;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item .vd-ui_icon {
  font-size: 16px;
  margin-right: 5px;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item .icon-radio1,
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item .icon-checkbox1 {
  color: #BABFC2;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item .icon-radio3,
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item .icon-checkbox2 {
  color: #09f;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item.selected {
  background: #E0F3FF;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-avatar {
  margin-right: 5px;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-avatar img {
  width: 18px;
  height: 18px;
  border-radius: 3px;
  object-fit: cover;
  overflow: hidden;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-txt {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-txt .strong {
  color: #f60;
}
.vd-ui-select-options .vd-ui-select-options-empty {
  padding: 100px 0;
  text-align: center;
  height: calc(100vh - 200px);
  height: calc(100vh - 200px - constant(safe-area-inset-bottom));
  height: calc(100vh - 200px - env(safe-area-inset-bottom));
}
.vd-ui-select-options .vd-ui-select-options-empty .empty-img {
  width: 223px;
  height: 120px;
  margin: 0 auto;
}
.vd-ui-select-options .vd-ui-select-options-empty .empty-img img {
  width: 100%;
  height: 100%;
  margin-bottom: 15px;
}
.vd-ui-select-options .vd-ui-select-options-empty .empty-txt {
  color: #666;
  font-size: 14px;
}
.vd-ui-select-options .vd-ui-select-loading {
  padding-top: 100px;
  height: calc(100vh - 200px);
  height: calc(100vh - 200px - constant(safe-area-inset-bottom));
  height: calc(100vh - 200px - env(safe-area-inset-bottom));
  text-align: center;
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.vd-ui-select-options .vd-ui-select-loading .vd-ui-select-loading-icon {
  font-size: 48px;
  color: #09f;
  text-align: center;
  margin-bottom: 10px;
}
.vd-ui-select-options .vd-ui-select-loading .vd-ui-select-loading-icon .icon-loading {
  display: inline-block;
  animation: loading 2s linear infinite;
}
.vd-ui-select-options .vd-ui-select-loading .vd-ui-select-loading-txt {
  font-size: 14px;
}
.vd-ui-select-options .vd-ui-select-options-footer {
  display: flex;
  align-items: center;
  padding: 10px 0;
  background: #fff;
  border-top: solid 1px #EBEFF2;
}
.vd-ui-select-options .vd-ui-select-options-footer .btn-cancel-flex {
  flex: 2;
  padding-left: 10px;
}
.vd-ui-select-options .vd-ui-select-options-footer .btn-confirm-flex {
  flex: 3;
  padding: 0 10px;
}

.ui-step-wrap .ui-step-list {
  display: flex;
  align-items: center;
}
.ui-step-wrap .ui-step-list .ui-step-item {
  flex: 1;
  text-align: center;
  padding: 30px 0 20px 0;
  position: relative;
}
.ui-step-wrap .ui-step-list .ui-step-item::before {
  content: "";
  width: 100%;
  border-bottom: 1px dashed #c9ced1;
  position: absolute;
  top: 38px;
  left: calc(50% + 8px);
}
.ui-step-wrap .ui-step-list .ui-step-item:last-child::before {
  display: none;
}
.ui-step-wrap .ui-step-list .ui-step-item .ui-step-item-icon {
  width: 17px;
  height: 17px;
  border-radius: 50%;
  background: #babfc2;
  display: inline-block;
  vertical-align: top;
  margin-bottom: 5px;
}
.ui-step-wrap .ui-step-list .ui-step-item .ui-step-item-txt {
  color: #999;
}
.ui-step-wrap .ui-step-list .ui-step-item.active .ui-step-item-icon {
  background-color: #09f;
}
.ui-step-wrap .ui-step-list .ui-step-item.active .ui-step-item-txt {
  color: #09f;
}
.ui-step-wrap .ui-step-list.wait .ui-step-item.active .ui-step-item-icon {
  background-color: #f60;
}
.ui-step-wrap .ui-step-list.wait .ui-step-item.active .ui-step-item-txt {
  color: #f60;
}
.ui-step-wrap .ui-step-list.finish .ui-step-item .ui-step-item-icon {
  background-color: #13BF13;
}
.ui-step-wrap .ui-step-list.finish .ui-step-item .ui-step-item-txt {
  color: #13BF13;
}
.ui-step-wrap .ui-step-list.close .ui-step-item .ui-step-item-icon {
  background-color: #1A4D80;
}
.ui-step-wrap .ui-step-list.close .ui-step-item .ui-step-item-txt {
  color: #1A4D80;
}

.ui-wx-upload-wrap {
  display: flex;
  flex-wrap: wrap;
  margin-top: -10px;
}
.ui-wx-upload-wrap .ui-wx-upload-item,
.ui-wx-upload-wrap .ui-wx-upload-btn {
  width: 90px;
  height: 90px;
  margin-right: 10px;
  border-radius: 6px;
  overflow: hidden;
  background: #F5F7FA;
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.ui-wx-upload-wrap .ui-wx-upload-item .ui-wx-upload-btn-inner,
.ui-wx-upload-wrap .ui-wx-upload-btn .ui-wx-upload-btn-inner {
  width: 32px;
  height: 32px;
  background-image: url(/mstatic/image/camera.svg);
  background-size: 100% 100%;
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.ui-wx-upload-wrap .ui-wx-upload-item .icon-loading,
.ui-wx-upload-wrap .ui-wx-upload-btn .icon-loading {
  font-size: 32px;
  color: #C2C6CC;
  animation: loading 2s linear infinite;
}
.ui-wx-upload-wrap .ui-wx-upload-item:last-child,
.ui-wx-upload-wrap .ui-wx-upload-btn:last-child {
  margin-right: 0;
}
.ui-wx-upload-wrap .ui-wx-upload-item img,
.ui-wx-upload-wrap .ui-wx-upload-btn img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.ui-wx-upload-wrap .ui-wx-upload-item .ui-wx-upload-item-del,
.ui-wx-upload-wrap .ui-wx-upload-btn .ui-wx-upload-item-del {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  width: 24px;
  height: 24px;
  border-radius: 3px;
}
.ui-wx-upload-wrap .ui-wx-upload-item .ui-wx-upload-item-del .vd-ui_icon,
.ui-wx-upload-wrap .ui-wx-upload-btn .ui-wx-upload-item-del .vd-ui_icon {
  line-height: 1;
}

.crm-slide-dialog-wrap {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  transition: all 0.22s ease;
  min-width: 360px;
}
.crm-slide-dialog-wrap .crm-slide-dialog-mask {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
  opacity: 0;
  transition: opacity 0.22s ease;
}
.crm-slide-dialog-wrap .crm-slide-dialog-cnt {
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
  z-index: 1;
  border-radius: 8px 8px 0 0;
  background: #fff;
  transform: translateY(100%);
  transition: transform 0.22s ease;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.crm-slide-dialog-wrap .crm-slide-dialog-cnt.drop-down {
  bottom: auto;
  top: 0;
  border-radius: 0 0 8px 8px;
  transform: translateY(-100%);
  padding-bottom: 0;
}
.crm-slide-dialog-wrap .crm-slide-dialog-cnt .crm-slide-dialog-header {
  position: relative;
  padding: 10px 0;
  text-align: center;
  border-bottom: solid 1px #EBEFF2;
}
.crm-slide-dialog-wrap .crm-slide-dialog-cnt .crm-slide-dialog-header .slide-dialog-header-option {
  position: absolute;
  font-size: 14px;
  height: 44px;
  line-height: 44px;
  color: #09f;
  left: 15px;
  top: 0;
}
.crm-slide-dialog-wrap .crm-slide-dialog-cnt .crm-slide-dialog-header .slide-dialog-header-title {
  font-size: 16px;
  font-weight: 700;
}
.crm-slide-dialog-wrap .crm-slide-dialog-cnt .crm-slide-dialog-header .slide-dialog-header-close {
  font-size: 20px;
  color: #999;
  position: absolute;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 0;
  right: 0;
}
.crm-slide-dialog-wrap.show {
  opacity: 1;
  transition: opacity 0.22s ease;
}
.crm-slide-dialog-wrap.show .crm-slide-dialog-mask {
  opacity: 1;
}
.crm-slide-dialog-wrap.show .crm-slide-dialog-cnt {
  transform: translateY(0);
}

.vd-ui-toggle-wrap {
  width: 36px;
  height: 18px;
  border-radius: 9px;
  background: #BABFC2;
  position: relative;
  transition: all 0.22s ease;
}
.vd-ui-toggle-wrap .vd-ui-toggle-inner {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #fff;
  position: absolute;
  left: 2px;
  top: 2px;
  transition: left 0.22s ease;
}
.vd-ui-toggle-wrap.active {
  background: #0099FF;
}
.vd-ui-toggle-wrap.active.disabled {
  background: #7FCCFF;
}
.vd-ui-toggle-wrap.active .vd-ui-toggle-inner {
  left: 20px;
}

