package com.vedeng.erp.saleorder.service.impl;

import com.vedeng.erp.buyorder.dto.RBuyorderExpenseJSaleorderDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.buyorder.service.RBuyorderExpenseJSaleorderService;
import com.vedeng.erp.saleorder.dao.SaleOrderGoodsDetailMapper;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.model.SaleorderGoods;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Putin
 * @CreateTime: 2023-01-13  11:30
 */
@Slf4j
@Service
public class SaleOrderGoodsServiceImpl implements SaleOrderGoodsApiService {
    @Resource
    private SaleOrderGoodsDetailMapper saleOrderGoodsDetailMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired
    private RBuyorderExpenseJSaleorderService rBuyorderExpenseJSaleorderService;

    @Autowired
    private SaleOrderApiService saleOrderApiService;

    public static final Integer delivery = 0; //发货

    public static final Integer receive = 1; //收货

    @Override
    public List<SaleOrderGoodsDetailDto> getRelatedDetail(List<Integer> saleorderGoodsIdList) {
        if (saleorderGoodsIdList.size() == 0) {
            return new ArrayList<>();
        }
        return saleOrderGoodsDetailMapper.getRelatedDetail(saleorderGoodsIdList);
    }


    @Override
    public List<SaleOrderGoodsDetailDto> getExpenseDetail(List<Integer> saleorderGoodsIdList) {
        return saleOrderGoodsDetailMapper.getExpenseDetail(saleorderGoodsIdList);
    }

    @Override
    public int updateExpenseGoodsDeliveryAndArrivalStatus(List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtos) {
        return saleOrderGoodsDetailMapper.updateExpenseGoodsDeliveryAndArrivalStatus(saleOrderGoodsDetailDtos);
    }


    @Override
    public void dosaleDeliveryStatus(Integer buyorderExpenseId) {
        List<RBuyorderExpenseJSaleorderDto> buyorderExpense = buyorderExpenseApiService.findByBuyorderExpenseId(buyorderExpenseId);
        if(CollectionUtils.isEmpty(buyorderExpense)){
            log.info("未查询到关联销采关系，采购费用单id:{}",buyorderExpenseId);
            return;
        }
        List<Integer> collect = buyorderExpense.stream().map(RBuyorderExpenseJSaleorderDto::getSaleorderGoodsId).collect(Collectors.toList());
        List<SaleOrderGoodsDetailDto> detailDtos = saleOrderGoodsDetailMapper.getRelatedDetail(collect);

        long logTime = System.currentTimeMillis();
        for (RBuyorderExpenseJSaleorderDto jSaleorderDto : buyorderExpense) {
            for (SaleOrderGoodsDetailDto dto : detailDtos) {
                if (jSaleorderDto.getSaleorderGoodsId().equals(dto.getSaleorderGoodsId())){
                    SaleorderGoods updateSaleOrderGood = new SaleorderGoods();
                    updateSaleOrderGood.setSaleorderGoodsId(jSaleorderDto.getSaleorderGoodsId());
                    // 发货状态 全部发货:2
                    updateSaleOrderGood.setDeliveryStatus(2);
                    // 发货时间
                    updateSaleOrderGood.setDeliveryTime(logTime);
                    //发货数量
                    updateSaleOrderGood.setDeliveryNum(dto.getNum());

                    log.info("更新直属费用单关联销售商品的发货状态，费用单id：{}，销售明细id：{}",buyorderExpenseId,jSaleorderDto.getSaleorderGoodsId());
                    saleorderGoodsMapper.updateByPrimaryKeySelective(updateSaleOrderGood);
                    break;
                }
            }
        }

    }

    @Override
    public void dosaleArrivalStatus(Integer buyorderExpenseId) {
        List<RBuyorderExpenseJSaleorderDto> buyorderExpense = buyorderExpenseApiService.findByBuyorderExpenseId(buyorderExpenseId);
        if(CollectionUtils.isEmpty(buyorderExpense)){
            log.info("未查询到关联销采关系，采购费用单id:{}",buyorderExpenseId);
            return;
        }
        Integer saleorderId = buyorderExpense.get(0).getSaleorderId();
        List<SaleOrderGoodsDetailDto> goodsDetailDtos = saleOrderGoodsDetailMapper.findGoodsDetailByOrderId(saleorderId);
        //固定不进待采购列表的五个sku
        List<String> virtulSkus = Arrays.asList("V127063", "V251526", "V256675", "V253620", "V251462");
        //销售单中所有可见的虚拟商品
        List<SaleOrderGoodsDetailDto> expenseList = goodsDetailDtos.stream()
                .filter(o -> o.getIsVirtureSku() != null && o.getIsVirtureSku() == 1 && o.getStatus() == 1 && !virtulSkus.contains(o.getSkuNo()) && o.getIsNeedPurchase()==1)
                .collect(Collectors.toList());



        int delivery = 0;
        for (SaleOrderGoodsDetailDto dto : expenseList) {
            if (dto.getDeliveryStatus() ==2){
                delivery++;
            }
        }
        //判断是否全部发货
        if (delivery == expenseList.size()){
            long logTime = System.currentTimeMillis();
            for (SaleOrderGoodsDetailDto dto : expenseList) {
                SaleorderGoods updateSaleOrderGood = new SaleorderGoods();
                updateSaleOrderGood.setSaleorderGoodsId(dto.getSaleorderGoodsId());
                //收货状态 全部收货：2
                updateSaleOrderGood.setArrivalStatus(2);
                //收货时间
                updateSaleOrderGood.setArrivalTime(logTime);

                log.info("更新直属费用单关联销售商品的收货状态，费用单id：{}，销售明细id：{}",buyorderExpenseId,dto.getSaleorderGoodsId());
                saleorderGoodsMapper.updateByPrimaryKeySelective(updateSaleOrderGood);
            }
        }


    }

    @Override
    public void doConfirmArrival(Integer buyorderExpenseId) {
        List<RBuyorderExpenseJSaleorderDto> buyorderExpense = buyorderExpenseApiService.findByBuyorderExpenseId(buyorderExpenseId);
        if(CollectionUtils.isEmpty(buyorderExpense)){
            log.info("未查询到关联销采关系，采购费用单id:{}",buyorderExpenseId);
            return;
        }
        long logTime = System.currentTimeMillis();
        for (RBuyorderExpenseJSaleorderDto jSaleorderDto : buyorderExpense) {
            SaleorderGoods updateSaleOrderGood = new SaleorderGoods();
            updateSaleOrderGood.setSaleorderGoodsId(jSaleorderDto.getSaleorderGoodsId());
            //收货状态 全部收货：2
            updateSaleOrderGood.setArrivalStatus(2);
            //收货时间
            updateSaleOrderGood.setArrivalTime(logTime);

            log.info("更新直属费用单关联销售商品的收货状态，费用单id：{}，销售明细id：{}",buyorderExpenseId,jSaleorderDto.getSaleorderGoodsId());
            saleorderGoodsMapper.updateByPrimaryKeySelective(updateSaleOrderGood);
        }
    }

    @Override
    public void pay4SaleStatus(Integer buyorderExpenseId) {
        List<RBuyorderExpenseJSaleorderDto> buyorderExpense = buyorderExpenseApiService.findByBuyorderExpenseId(buyorderExpenseId);
        if(CollectionUtils.isEmpty(buyorderExpense)){
            log.info("未查询到关联销采关系，采购费用单id:{}",buyorderExpenseId);
            return;
        }
        Integer saleorderId = buyorderExpense.get(0).getSaleorderId();
        List<SaleOrderGoodsDetailDto> goodsDetailDtos = saleOrderGoodsDetailMapper.findGoodsDetailByOrderId(saleorderId);
        long logTime = System.currentTimeMillis();
        for (RBuyorderExpenseJSaleorderDto jSaleorderDto : buyorderExpense) {
            for (SaleOrderGoodsDetailDto detailDto : goodsDetailDtos) {
                if (jSaleorderDto.getSaleorderGoodsId().equals(detailDto.getSaleorderGoodsId())){
                    SaleorderGoods updateSaleOrderGood = new SaleorderGoods();
                    updateSaleOrderGood.setSaleorderGoodsId(jSaleorderDto.getSaleorderGoodsId());

                    // 发货状态 全部发货:2
                    updateSaleOrderGood.setDeliveryStatus(2);
                    // 发货时间
                    updateSaleOrderGood.setDeliveryTime(logTime);
                    //发货数量
                    updateSaleOrderGood.setDeliveryNum(detailDto.getNum());
                    //收货状态 全部收货：2
                    updateSaleOrderGood.setArrivalStatus(2);
                    //收货时间
                    updateSaleOrderGood.setArrivalTime(logTime);

                    log.info("更新非直属费用单关联销售商品的收发货状态，费用单id：{}，销售明细id：{}",buyorderExpenseId,jSaleorderDto.getSaleorderGoodsId());
                    saleorderGoodsMapper.updateByPrimaryKeySelective(updateSaleOrderGood);
                }
            }
        }
        List<Integer> saleOrderIds = rBuyorderExpenseJSaleorderService.findSaleOrderIds(buyorderExpenseId);
        if (saleOrderIds.size()>0){
            for (Integer saleOrderId : saleOrderIds) {
                log.info("开始校验销售单收发货状态,销售单Id：{}",saleOrderId);
                saleOrderApiService.checkSaleorderDeliveryAndArrivalStatus(saleOrderId);
            }
        }
    }

    @Override
    public List<SaleOrderGoodsDetailDto> findAllVirtualGoodsBySaleorderId(Integer saleorderId) {
        List<SaleOrderGoodsDetailDto> detailList = saleOrderGoodsDetailMapper.findGoodsDetailByOrderId(saleorderId);
        List<SaleOrderGoodsDetailDto> collect = new ArrayList<>();
        if (!detailList.isEmpty()) {
            List<String> virtulSkus = Arrays.asList("V127063", "V251526", "V256675", "V253620", "V251462");
            collect = detailList.stream().filter(o -> (o.getIsVirtureSku() != null && o.getIsVirtureSku() == 1 && o.getStatus() == 1) || (virtulSkus.contains(o.getSkuNo()))).collect(Collectors.toList());
        }
        return collect;
    }

    @Override
    public void doNoSeeGoodsArrival(List<Integer> saleorderGoodsIds) {
        log.info("开始校验是否含有不可见虚拟商品：{}",saleorderGoodsIds);
        //筛选实物商品所在订单的全部商品
        List<SaleOrderGoodsDetailDto> allGoodsBySaleOrderGoods = saleOrderGoodsDetailMapper.findAllGoodsBySaleOrderGoods(saleorderGoodsIds);
        //固定不进待采购列表的五个sku
        List<String> virtulSkus = Arrays.asList("V127063", "V251526", "V256675", "V253620", "V251462");
        //不可见虚拟商品
        List<SaleOrderGoodsDetailDto> noCanSeeVsku = allGoodsBySaleOrderGoods.stream()
                .filter(o -> o.getArrivalStatus()!=2 &&((o.getIsVirtureSku() != null && o.getIsVirtureSku() == 1 && o.getStatus() == 1 && o.getIsNeedPurchase() == 0) || virtulSkus.contains(o.getSkuNo())))
                .collect(Collectors.toList());
        //不进待采购列表虚拟商品收货
        if (noCanSeeVsku.size()>0){
            long logTime = System.currentTimeMillis();
            for (SaleOrderGoodsDetailDto dto : noCanSeeVsku) {
                SaleorderGoods updateSaleOrderGood = new SaleorderGoods();
                updateSaleOrderGood.setSaleorderGoodsId(dto.getSaleorderGoodsId());
                //收货状态 全部收货：2
                updateSaleOrderGood.setArrivalStatus(2);
                //收货时间
                updateSaleOrderGood.setArrivalTime(logTime);

                log.info("更新销售单不进待采购列表商品收货状态，销售单号：{}，销售明细id：{}",dto.getSaleorderNo(),dto.getSaleorderGoodsId());
                saleorderGoodsMapper.updateByPrimaryKeySelective(updateSaleOrderGood);
            }
        }
    }

    @Override
    public void doNoSeeGoodsArrivalByBuyOrderId(List<Integer> buyorderGoodIds) {
        if (buyorderGoodIds.size() >0){
            List<Integer> saleorderGoodsIds = saleorderGoodsMapper.getAllSaleOrderGoodsIdByBuyOrderGoodsId(buyorderGoodIds);
            if (saleorderGoodsIds.size()>0){
                this.doNoSeeGoodsArrival(saleorderGoodsIds);
            }
        }
    }

    @Override
    public Integer getSaleOrderGoodsBySkuId(Integer skuId) {
        return saleorderGoodsMapper.getSaleOrderGoodsBySkuId(skuId);
    }

    @Override
    public List<SaleOrderGoodsDetailDto> getBySaleorderId(Integer saleorderId) {
        return saleorderGoodsMapper.getBySaleorderId(saleorderId);
    }

    @Override
    public SaleOrderGoodsDetailDto getBySaleOrderGoodsId(Integer saleorderGoodsId) {
        return saleorderGoodsMapper.getBySaleorderGoodsId(saleorderGoodsId);
    }

    @Override
    public List<SaleOrderGoodsDetailDto> getSalesOrderGoodsByOrderId(Integer salesOrderId) {
        return saleorderGoodsMapper.getSalesOrderGoodsByOrderId(salesOrderId);
    }
}
