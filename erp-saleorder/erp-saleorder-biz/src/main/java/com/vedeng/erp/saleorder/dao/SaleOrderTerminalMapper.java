package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.saleorder.domain.entity.SaleOrderTerminalEntity;

/**
 * <AUTHOR>
 */
public interface SaleOrderTerminalMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(SaleOrderTerminalEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(SaleOrderTerminalEntity record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    SaleOrderTerminalEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SaleOrderTerminalEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SaleOrderTerminalEntity record);

    /**
     * 根据销售单id查询已维护的终端信息
     *
     * @param saleOrderId 销售单id
     * @return SaleOrderTerminalEntity
     */
    SaleOrderTerminalEntity selectBySaleOrderId(Integer saleOrderId);
}