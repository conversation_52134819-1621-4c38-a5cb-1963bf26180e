.vd-ui-phone-related {
  position: relative;
  display: flex;
}
.vd-ui-phone-related > .vd-ui-input {
  width: 300px;
}
.vd-ui-phone-related > .vd-ui-search-related {
  max-height: 338px;
  overflow-y: auto;
  border-radius: 3px;
  background: #fff;
  border: solid 1px #BABFC2;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  position: absolute;
  top: 30px;
  z-index: 9;
}
.vd-ui-phone-related > .vd-ui-search-related::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-phone-related > .vd-ui-search-related::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-phone-related > .vd-ui-search-related::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-phone-related > .vd-ui-search-related::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-phone-related > .vd-ui-search-related::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-phone-related > .vd-ui-search-related .loading {
  color: #666;
  height: 39px;
  line-height: 37px;
  padding: 0px 10px;
  overflow: hidden;
}
.vd-ui-phone-related > .vd-ui-search-related .loading i {
  animation: loading 1.8s linear infinite;
  display: inline-block;
  position: relative;
  top: 1px;
  font-size: 16px;
  margin-right: 5px;
  color: #09F;
}
.vd-ui-phone-related > .vd-ui-search-related .failed-li {
  height: 39px;
  line-height: 39px;
  padding: 0px 10px;
}
.vd-ui-phone-related > .vd-ui-search-related .failed-li i {
  position: relative;
  top: 2px;
  font-size: 16px;
  color: #E64545;
  margin-right: 5px;
}
.vd-ui-phone-related > .vd-ui-search-related .failed-li .reload {
  color: #09f;
  cursor: pointer;
}
.vd-ui-phone-related > .vd-ui-search-related .empty-li {
  height: 39px;
  line-height: 39px;
  padding: 0px 10px;
  text-align: center;
  color: #999;
}
.vd-ui-phone-related > .vd-ui-search-related .search-list {
  padding: 5px 10px;
}
.vd-ui-phone-related > .vd-ui-search-related .search-list .sr-item {
  padding: 6px 0;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}
.vd-ui-phone-related > .vd-ui-search-related .search-list .sr-item .name {
  flex: 1;
  min-width: 0;
  margin-right: 10px;
}
.vd-ui-phone-related > .vd-ui-search-related .search-list .sr-item .mobile {
  width: 120px;
  flex-shrink: 0;
  text-align: right;
  color: #999;
}
.vd-ui-phone-related > .vd-ui-search-related .search-list .sr-item:hover .name {
  color: #f60;
}
.vd-ui-phone-related > .vd-ui-search-related .search-list .sr-item:hover .mobile {
  color: #f60;
}
