<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增虚拟商品" scope="application" />
<%@ include file="../../../common/common.jsp"%>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<style>
    .myPopover {
        background-color: #fff !important;
        color: #000 !important;
    }
</style>
<form class="layui-form" id="virtureSkuForm">
    <div class="layui-form-item">
        <lable class="layui-form-label" style="width: 200px!important;"><font style="color: red">*</font>订货号:</lable>
        <div class="layui-input-inline">
            <input type="text" readonly lay-verify="required" class="layui-input" name="skuNo" id="virtureSkuNo" />
            <input type="hidden" readonly class="input-larger" name="skuId" id="virtureSkuId" />
        </div>
        <div id="addGood" class="title-click nobor pop-new-data layui-form-mid layui-word-aux" style="color: #1E9FFF!important;"
                   layerParams='{"width":"800px","height":"650px","title":"添加产品","link":"/orderstream/saleorder/addSaleorderGoods.do?scene=8"}'>选择商品</div>
    </div>
    <div class="layui-form-item" >
        <lable class="layui-form-label" style="width: 200px!important;"><font style="color: red">*</font>商品名称：</lable>
        <div class="layui-input-inline ">
            <input type="text" readonly lay-verify="required" class="layui-input" name="skuName" id="virtureSkuName" />
        </div>
    </div>
    <div class="layui-form-item">
        <lable class="layui-form-label" style="width: 200px!important;"><font style="color: red">*</font>税收编码：</lable>

        <div class="layui-input-inline">
            <input type="text" readonly lay-verify="required" class="layui-input" name="taxCategoryNo" id="virtureTaxCategoryNo" />
        </div>
        <button id="openDialog" type="button" class="layui-btn layui-bg-blue" >税收编码库</button>
        <span id="viewDetail-Tax"  style="color: #1E9FFF!important; cursor: pointer;">详情</span>
    </div>
    <div class="layui-form-item" >
        <lable class="layui-form-label" style="width: 200px!important;"><font style="color: red">*</font>归属人：</lable>
        <div class="layui-input-inline ">
            <input type="text" readonly lay-verify="required" class="layui-input" name="proUserName" id="virtureProUserName" />
        </div>
    </div>
    <div class="layui-form-item" >
        <lable class="layui-form-label" style="width: 200px!important;"><font style="color: red">*</font>所属费用类别：</lable>
        <div class="layui-input-inline ">
            <select name="costCategoryId" id="costCategoryId" lay-verify="required">
                <option value="">请选择</option>
                <c:forEach var="costCategory" items="${costCategoryApiDtos}">
                    <option value="${costCategory.costCategoryId}">${costCategory.categoryName}</option>
                </c:forEach>
            </select>
        </div>
        <div class="layui-form-mid layui-word-aux" style="color: #31BDEC!important;">后续用于财务费用科目对应</div>
    </div>
    <div class="layui-form-item" >
        <lable class="layui-form-label" style="width: 200px!important;"><font style="color: red">*</font>是否需库存管理：</lable>
        <div class="layui-input-inline">
            <select name="haveStockManage" id="haveStockManage" lay-verify="required">
                <option value="0">否</option>
            </select>
        </div>
        <div class="layui-form-mid layui-word-aux" style="color: #31BDEC!important;">此虚拟商品若可以次数形式销售则选库存管理（例如5次清洗服务）</div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button type="button" class="layui-btn" lay-submit lay-filter="virtureForm" >立即提交</button>
        </div>
    </div>
</form>
<script>
    layui.use('form', function () {
        var form = layui.form;
        form.render();
        form.on("submit(virtureForm)", function (data) {
            $.ajax({
                url: './saveVirtureSku.do',
                type: 'post',
                data: data.field,
                dataType: "json",
                success: function (data) {
                    if (data.code == 0) {
                        var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
                        parent.layer.close(index);
                        window.parent.location.reload();
                    } else {
                        layer.alert(data.message);
                    }
                },
                error: function (data) {
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
            return false;
        })


        var index_Tax; // 弹出层的索引
        // 监听鼠标移入事件
        $('#viewDetail-Tax').click(function () {
            debugger
            let val = $("#virtureTaxCategoryNo").val();
            // 模拟从服务器获取数据
            var initdata = {
                goodsServicesNameAbbreviation: '',
                goodsServicesClassificationAbbreviation: '',
                goodsServicesName: '',
                classificationAbbreviation: '',
                keyword: '',
                description: '',
            };
            if (val != undefined && val != null && val != '') {
                var data = $.ajax({
                    url: '/taxClassification/api/findByCode.do?code=' + val,
                    method: 'GET',
                    async: false,
                    dataType: 'json'
                }).responseJSON;
            }

            if (data.data != null) {
                initdata.goodsServicesNameAbbreviation = data.data.goodsServicesNameAbbreviation
                initdata.goodsServicesClassificationAbbreviation = data.data.goodsServicesClassificationAbbreviation
                initdata.goodsServicesName = data.data.goodsServicesName
                initdata.classificationAbbreviation = data.data.classificationAbbreviation
                initdata.keyword = data.data.keyword
                initdata.description = data.data.description
            }
            // 构建Popover弹出框的HTML内容
            var html = '<div style="padding: 10px;">';
            html += '<p>汇总项名称：' + initdata.goodsServicesNameAbbreviation + '</p>';
            html += '<p>汇总项简称：' + initdata.goodsServicesClassificationAbbreviation + '</p>';
            html += '<p>货物和劳务名称：' + initdata.goodsServicesName + '</p>';
            html += '<p>商品和服务分类简称：' + initdata.classificationAbbreviation + '</p>';
            html += '<p>关键字：' + initdata.keyword + '</p>';
            html += '<p>说明：' + initdata.description + '</p>';
            html += '</div>';


            // 显示Popover弹出框
            index_Tax = layer.open( {
                type: 1, // 设置弹出框样式
                title:'税务编码详情',
                content:html,
                area: ['500px', 'auto'],
                success: function (layero) {

                }
            });
        })
        $('#openDialog').click(function () {
            $.get('/category/base/taxCodePopup.do', function (htmlContent) {
                layer.open({
                    type: 0,
                    title: '税务编码分页列表查询',
                    content: htmlContent,
                    area: ['900px', '500px'],
                    btn: ['确定', '取消'],
                    resize: true,
                    maxmin: true,
                    yes: function (index, layero) {
                        debugger
                        var table = layui.table;
                        var checkStatus = table.checkStatus('dataTable');
                        var data = checkStatus.data;
                        // 如果没有选中任何数据，返回
                        if (data.length === 0) {
                            layer.msg('请先选择一个税收编码');
                            return;
                        }

                        // 获取选中的第一条数据
                        var selectedData = data[0];

                        // 将选中的 code 赋值给税收编码的 input 框

                        $("#virtureTaxCategoryNo").val(selectedData.finalCode.trim());

                        // 关闭弹出层
                        layer.close(index);
                    }
                });
            });

        });
    });

</script>
<%@ include file="../../../common/footer.jsp"%>