<!DOCTYPE html>
<html lang="en">
<%@ page language="java" contentType="text/html; charset=UTF-8"
		 pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ include file="./common/header.jsp"%>
<style>
	#container {
		display: flex;
		/*height: 400px;*/
	}
	#voiceText {
		width: 400px;
		height: 320px;
		overflow-y: auto;
		border: 1px solid #999;
		border-radius: 10px;
		margin-left: 5px;
	}
	#playVoice {
		width: 410px;
		height: 60px;
		border: 0px solid black;
	}
	#voiceAi {
		position: relative;
		width: 300px;
		height: 380px;
		border: 0px solid black;
	}
	#voiceAi .changeSence{
		position: absolute;
		bottom: 10px;
		left: 50%;
		transform: translateX(-50%);
		width: 75px; /* 你的具体宽度 */
		height: 30px; /* 你的具体高度 */
	}

	#voiceAi .senceTitle{
		display: flex;
		justify-content: center; /* 水平居中 */
		align-items: center; /* 垂直居中 */
		height: 30px; /* 需要一个确定的高度 */
	}
	#voiceAi .senceTitle span{
		font-size: 14px;
		font-weight: bolder;
	}

	#voiceAi .backSence{
		position: absolute;
		/*bottom: 0;*/
		left: 50%;
		transform: translateX(-50%);
		width: 75px; /* 你的具体宽度 */
		height: 30px; /* 你的具体高度 */
	}


	.ai-fixed .glyphicon {
		cursor: pointer;
		position: absolute;
		top: -5px;
		left: -5px;
	}

	.ai-fixed .glyphicon:hover {
		color: #f60;
	}

	.ai-helpme{
		bottom:5px;
		display: none;
		margin-left: 35px;
		margin-top:3px;
		margin-bottom: 3px;
		width:80px;
		align-items: center;
		cursor: pointer;
		/*border:solid 1px #999;*/
		border-radius: 3px;
	}

	.ai-helpme span{
		background-color: #1d93ab;;
		color: #fff;
		width:100%;
		height: 100%;
		border-radius: 4px;
		padding: 3px;
	}

	@keyframes typing {
		from { width: 0 }
	}

	.chat-result-container {
		position: relative;
		width: 300px;
		border: 1px solid #999;
		padding: 10px;
		border-radius: 10px;
		margin-left:0px;
		margin-bottom: 10px;
		height: 300px;
		max-height:300px;
		overflow: auto;
	}
	.chat-result-container .stop{
		position: absolute;
		right: 15px;
		top: 5px;
		z-index: 100;
		display: none;
	}

	.chat-select-container {
		width: 300px;
		border: 1px solid #999;
		padding: 10px;
		border-radius: 10px;
		margin-left:0px;
		margin-bottom: 10px;
		height: 341px;
		max-height:341px;
		overflow: auto;
	}
	.message {
		position: relative;
		margin-bottom: 10px;
		display: flex;
		align-items: center;
	}
	.message .content{
		position: relative;
		width: 250px;
		border: 1px solid #999;
		padding: 5px;
		border-radius: 5px;
		margin-left:35px;
		background-color: #e7f8ff;;
		animation: typing 3s steps(40, end);

	}
	.message .content2{
		position: relative;
		width: 250px;
		border: 1px solid #999;
		padding: 5px;
		border-radius: 5px;
		margin-left:35px;
		background-color: #e7f8ff;
		height: 30px;
	}
	.message .contentTips{
		position: relative;
		width: 250px;
		border: 1px solid #999;
		padding: 5px;
		border-radius: 5px;
		margin-left:35px;
		background-color: #e7f8ff;;
	}
	.changeSenceArea {
		position: relative;
		width: 242px;
		border: 0px solid #999;
		padding: 0px;
		border-radius: 5px;
		margin-left: 35px;
	}
	.changeSenceArea button{
		margin: 5px 0px 0px 0px;
	}
	.user1 {
		justify-content: flex-start;
	}

	.message img {
		height: 30px;
		width: 30px;
		border-radius: 50%;
		margin-right: 0px;
		position: absolute;
		top: 0px;
	}
	.aibtn{
		background-color: #1d93ab;;
		color: #fff;
		padding: 3px;
		display: none;
	}

</style>
<script type="text/javascript">
	var showContent = true;//全局配置，流式输出，起到暂停效果
	var timers  = [];//全局流式输出的对象
	function stopAllTimer(){
		for(var i = 0; i < timers.length; i++){
			clearTimeout(timers[i]);
		}
		// if(timers.length > 0){
		// 	//在voiceAiContent后追加“【已停止】”
		// 	$("#voiceAiContent").append("<span style='color:red;'>【已停止】</span>");
		// }
		timers = [];
	}
	function stopAiForBtn(t) {
		stopAllTimer();
		endAi();
		$(t).hide();

	}

	function endAi(){//AI已经输出完成，完成后方可继续点击。
		$('.ai-helpme').css('pointer-events', '').attr("title","ERP贝壳助手");
		$('.ai-helpme').find("span").html('贝壳帮我分析');
		$(".aibtn").prop('disabled', false).attr("title","");
		$(".aibtn").show();
		$(".changeSence").show();
	}
	function startAi() {//开始Ai输出，不允许某些按钮操作
		$('.ai-helpme').css('pointer-events', 'none').attr("title","贝壳分析中，请稍后...");
		$('.ai-helpme').find("span").html('贝壳分析中...');
		$(".aibtn").prop('disabled', true).attr("title","贝壳分析中，请稍后...");
	}

	function showChangeSence() {
		showContent = false;
		stopAllTimer();
		endAi(0);
		$(".voiceAreaA").hide();
		$(".voiceAreaB").show();
		$(".ai-helpme").hide();

	}
	function backSence() {
		$(".voiceAreaA").show();
		$("#voiceAiContent").css("animation","none");
		$(".voiceAreaB").hide();
	}

	function changeSence(communicateRecordId,sence) {
		backSence();
		$(".changeSence").hide();
		openAi(communicateRecordId,sence);
	}
	
	function openAi(communicateRecordId,sence){
		stopAllTimer();//上一次没有结束的输出全部清除掉。
		startAi();

		$(".chat-result-container").show();
		$(".chat-select-container").hide();
		$("#voiceAiContent").hide();
		$("#loadingDiv").show();
		// $("#voiceAiContent").css("animation","none");
		// $("#voiceAiContent").css("height","30px");
		// $("#voiceAiContent").html('<img style="width: 15px;height: 15px;margin-top: 5px;" src="/static/new/img/loading.gif">');
		//setTimeout(function(){
		playVoiceAiContent(communicateRecordId,sence);
		// }, 5000);

	}

	function playVoiceAiContent(communicateRecordId,sence){
		// $("#voiceAiContent").css("animation","");
		// $("#voiceAiContent").css("height","");
		const div = document.getElementById('voiceAiContent');

		// let text="客户意图提炼如下： \n1. 客户询问上次提到的彩超预算是否批下来了。\n 2. 客户询问年后彩超的价格。\n 3. 客户询问是否有体检类的产品需求。 \n4. 客户需要稀释液溶解剂。 \n5. 客户需要溶血素和精华营养液。\n 6. 客户需要探头和探头协议。\n 7. 客户确认使用优贝康生物工程公司订购产品。\n 8. 客户询问优利特血球生化和尿沉渣产品是否常用。 \n9. 客户询问加6的价格。 \n10. 客户询问价格是否降低。 \n11. 客户确认待会收到合同。";
		let text="AI摘要分析： \n";
		$.ajax({
			url: "${pageContext.request.contextPath}/system/call/getrecordplayDetailAi.do",
			type: "post",
			timeout: 120000, // 设置超时时间为120秒，即2分钟
			data: {
				"communicateRecordId": ${communicateRecordId},
				"senceCode": sence
			},
			dataType : "json",
			success: function(data){
				$("#loadingDiv").hide();
				$("#voiceAiContent").show();
				var code = data.code;
				if(code <0 || data.data == undefined || data.data == null || data.data.length == 0){
					// $("#voiceAiContent").html("贝壳的CPU着火了，让我冷却下，请稍后再试！");//
					// endAi();
					// return;
					text = "贝壳的CPU着火了，让我冷却下，请稍后再试！";
				}
				var dataList = data.data;
				for(var i = 0; i < dataList.length; i++){
					text += (i+1)+". "+dataList[i].fieldName+"："+dataList[i].fieldResult+"\n";
				}
				div.innerHTML = '';
				//text= text.;
				showContent = true;
				for(let i = 0; i < text.length; i++) {
					var timer = setTimeout(function(){
						if(showContent){
							div.innerHTML += text.charAt(i).replace(/\n/g, "<br>");
							//console.log(i+"..."+(text.length-1));
							if(i == (text.length - 1)){
								endAi(1);
							}
						}
					}, i * 100);
					timers.push(timer);
				}

			}
		});



	}

	function showVoiceText(){
		$.ajax({
			url: "${pageContext.request.contextPath}/system/call/getrecordplayDetail.do",
			type: "post",
			timeout: 120000, // 设置超时时间为120秒，即2分钟
			data: {
				communicateRecordId: ${communicateRecordId}
			},
			dataType : "json",
			success: function(data){
				var code = data.code;
				if(code <0 || data.data == undefined || data.data == null){
					$("#errorText").show();
					$(".iconloading").hide();
					return;
				}
				//
				//* 录音状态（0初始|1上传完成（转写中）|2上传失败（需重新上传）|3讯飞转写失败|4讯飞转写成功|5本地提取可读文本失败
				// |6本地提取可读文本成功|8GPT获取标签失败)|9GPT获取标签成功|10查询讯飞转写结果报错|11查询讯飞转写结果成功）

				// private String voiceStatus;
				var voiceStatus = parseInt(data.data.voiceStatus);
				if(voiceStatus <0 ){
					$("#errorText").show();
					$(".iconloading").hide();
				}else if(voiceStatus >=0 && voiceStatus <=6){
					$("#errorText").show();
					$("#errorText").html("录音转写中，请稍后...");
					$(".iconloading").hide();
				}else{
					$("#errorText").hide();
					$(".iconloading").hide();
					var modifiedText = data.data.voiceTextParse;
					if(modifiedText.length > 0){
						modifiedText = modifiedText.replace(/对话人/g, "<br/>对话人");
						if(modifiedText.startsWith("<br/>")){
							modifiedText = modifiedText.substring(5,modifiedText.length);
						}
					}
					modifiedText= "<div style='margin:5px 5px 5px 5px;line-height: 24px;max-height: 305px;overflow: auto;background-color: aliceblue;padding-left: 11px;padding-top: 0px;'>"+modifiedText+"</div>";
					$("#voiceText").html(modifiedText);
					$(".ai-helpme").show();
				}

			}
		});

	}
</script>
<div id="container">
	<input type="hidden" id="communicateRecordId" name="communicateRecordId" value="${communicateRecordId}" />
	<div>
		<div id="voiceText">
			<div id="errorText" style="display: none;padding:20px;margin-top:150px;">
				${errorTips}
			</div>
			<div class="iconloading" style="margin-left: 200px;margin-top:150px;">
				<img src="${pageContext.request.contextPath}/static/new/img/loading.gif" onload="showVoiceText();">
			</div>

		</div>

		<div id="playVoice">
			<div style="display: flex; align-items: center; width: 400px;margin-left:5px;margin-top:5px; background-color: #f3f3f3; border: 1px solid #999;
        border-radius: 5px;
        padding: 5px;">
				<audio controls style="flex-grow: 1;height: 35px;" height="30"  width="100" >
					<source src="${url }" type="audio/mp3" />
					<source src="${url }" type="audio/ogg" />
					<embed height="30" width="100" src="${url }" />
					Your browser does not support the audio element.
				</audio>
				<%--<a href="${url}" class="download-url-button downloadVoice" download  >下载</a>--%>
				<img style="cursor: pointer;" title="下载录音" src="${pageContext.request.contextPath}/static/images/download.png" width="20px" height="20px" onclick="window.open('${url}')"/>
			</div>
		</div>
	</div>
	<div id="voiceAi">
		<div class="senceTitle">
			<span >AI意图识别</span>
		</div>
		<div class="chat-result-container voiceAreaA" style="display: none;">
			<button class="stop" style="display: none;" onclick="javascript:stopAiForBtn(this);">停止</button>
			<div class="message user1">
				<img src="${pageContext.request.contextPath}/static/images/ai-zhushou.gif" width="20px" height="20px"   >
				<div id="loadingDiv" class="content2"><img style="width: 15px;height: 15px;margin-top: 5px;" src="/static/new/img/loading.gif"></div>
				<div id="voiceAiContent" class="content"></div>
			</div>
			<%--<div class="changeSence">
				<button class="aibtn" onclick="javascript:showChangeSence();">切换场景</button>
			</div>--%>
		</div>
		<div class="changeSence voiceAreaA">
			<button class="aibtn" onclick="javascript:showChangeSence();">切换场景</button>
		</div>

		<div class="chat-select-container voiceAreaB" >
			<div class="message user1">
				<img src="${pageContext.request.contextPath}/static/images/ai-zhushou.gif" width="20px" height="20px"   >
				<div id="voiceAiTips" class="contentTips">
					<p >请告诉贝壳，该通话的场景是什么?<br/>这样我能更好的为您服务！</p>
				</div>
			</div>
			<div class="ai-helpme" onclick="javascript:openAi(${communicateRecordId},'SENCE_BUSINESS_FOLLOW');"    title="ERP贝壳助手"  >  <%--${sence}--%>
				<%--<img src="${pageContext.request.contextPath}/static/images/ai-zhushou.gif" width="20px" height="20px" style="border-radius: 50%;"/>--%>
				<span>贝壳帮我分析</span>
			</div>
			<div class="changeSenceArea">
				<button class="aibtn" onclick="changeSence(${communicateRecordId},'SENCE_PRODUCT_PROMOT')">产品推广</button>
				<button class="aibtn" onclick="changeSence(${communicateRecordId},'SENCE_BUSINESS_FOLLOW')">商机跟进</button>
				<button class="aibtn" onclick="changeSence(${communicateRecordId},'SENCE_CUSTOMER_DEVELOP')">客户开发</button>
				<button class="aibtn" onclick="changeSence(${communicateRecordId},'SENCE_QUOTAT_RESP')">报价响应</button>
				<button class="aibtn" onclick="changeSence(${communicateRecordId},'SENCE_BUSINESS_PROCESS')">商务处理</button>
				<button class="aibtn" onclick="changeSence(${communicateRecordId},'SENCE_AFTER_SALE')">售后</button>
			</div>
			<div class="backSence" style="display: none;">
				<button class="aibtn" onclick="javascript:backSence();">返回</button>
			</div>
		</div>
		<%--<div class="backSence voiceAreaB" style="display: none;">
			<button class="aibtn" onclick="javascript:backSence();">返回</button>
		</div>--%>
	</div>
</div>


</body>
</html>