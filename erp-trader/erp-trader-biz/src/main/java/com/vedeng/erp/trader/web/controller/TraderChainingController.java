package com.vedeng.erp.trader.web.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @description 客户建链
 * @date 2023/9/4 8:49
 **/
@RequestMapping("/traderChaining")
@Controller
public class TraderChainingController {


    @RequestMapping(value = "/addView")
    @NoNeedAccessAuthorization
    public ModelAndView addView(Integer traderCustomerId,Integer traderId) {
        ModelAndView mv = new ModelAndView("vue/view/tradercustomer/customer_chaining_add");
        mv.addObject("traderCustomerId",traderCustomerId);
        mv.addObject("traderId",traderId);
        return mv;
    }


    @RequestMapping(value = "/edit")
    @NoNeedAccessAuthorization
    public ModelAndView edit(Integer traderCustomerTerminalId) {
        ModelAndView mv = new ModelAndView("vue/view/tradercustomer/customer_chaining_edit");
        mv.addObject("traderCustomerTerminalId",traderCustomerTerminalId);
        return mv;
    }


    @RequestMapping(value = "/auditRecord")
    @NoNeedAccessAuthorization
    public ModelAndView auditRecord(Integer traderCustomerTerminalId,String name) {
        ModelAndView mv = new ModelAndView("vue/view/tradercustomer/customer_chaining_audit_record");
        mv.addObject("traderCustomerTerminalId",traderCustomerTerminalId);
        mv.addObject("name",name);
        return mv;
    }

    /**
     * 需要给权限
     */
    @RequestMapping(value = "/detail")
    public ModelAndView detail(Integer traderId) {
        ModelAndView mv = new ModelAndView("vue/view/tradercustomer/customer_chaining_detail");
        mv.addObject("traderId",traderId);
        return mv;
    }

    /**
     * 需要给权限
     */
    @RequestMapping(value = "/list")
    public ModelAndView list() {
        ModelAndView mv = new ModelAndView("vue/view/tradercustomer/customer_chaining_list");
        return mv;
    }

}
