package com.rabbitmq;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * @Author: daniel
 * @Date: 2021/3/16 19 28
 * @Description:
 */
@Component
public class ErpMsgProducer extends MsgProducer implements InitializingBean {

    @Autowired
    @Qualifier(value = "erpRabbitTemplate")
    private RabbitTemplate erpRabbitTemplate;

    @Override
    public void afterPropertiesSet() throws Exception {
        setRabbitTemplate(erpRabbitTemplate);
    }
}
