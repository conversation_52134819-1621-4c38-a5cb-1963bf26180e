<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ page trimDirectiveWhitespaces="true" %>
<c:if test="${not empty sessionScope.current_user}">
<input type="hidden" id="golbal_userId" value="${sessionScope.current_user.id}" />
<input type="hidden" id="golbal_userName" value="${sessionScope.current_user.username}" />
<input type="hidden" id="golbal_headPic" value="${sessionScope.current_user.headPic}" />
</c:if>
<c:if test="${empty sessionScope.current_user}">
    <input type="hidden" id="golbal_userId" value="" />
    <input type="hidden" id="golbal_userName" value="" />
    <input type="hidden" id="golbal_headPic" value="" />

</c:if>

<c:choose>
    <c:when test="${requestScope.checkCrmIsInner !=null and requestScope.checkCrmIsInner !=''}">
        <input id="golbal_innerSite" type="hidden">
        <img id="checkSiteImg" width="1" height="1" style="opacity: 0;position: absolute;z-index: -1;">
        <script>
            const NET_IMG = document.getElementById('checkSiteImg');
            const NET_INPUT = document.getElementById('golbal_innerSite');
            let NET_IMG_loaded = false;
            let NET_IMG_errored = false;

            NET_IMG.onload = function() {
                NET_IMG_loaded = true;
                console.log("innerSite");
                NET_INPUT.value = 'innerSite';
            };

            NET_IMG.onerror = function() {
                NET_IMG_errored = true;
                console.log("outSite");
                NET_INPUT.value = 'outSite';
            };

            NET_IMG.src='${requestScope.checkCrmIsInner}';

            setTimeout(function() {
                if (!NET_IMG_loaded && !NET_IMG_errored) {
                    console.log("图片加载超时或未触发事件");
                    NET_INPUT.value = 'unknown';
                }
            }, 5000); // 5秒超时
        </script>
    </c:when>
    <c:otherwise>
        <input id="golbal_innerSite" type="hidden" value="innerSite">
    </c:otherwise>
</c:choose>
<c:if test="${not empty sessionScope.current_user}">
<c:set var="setAsString" value="" /><c:forEach var="item" items="${sessionScope.current_user.permissions}" varStatus="status">
    <c:set var="setAsString" value="${setAsString}${item}" />
    <c:if test="${not status.last}"><c:set var="setAsString" value="${setAsString}," /></c:if>
</c:forEach>
<input type="hidden" id="golbal_permissions" value="${setAsString}" />
</c:if>
<c:if test="${empty sessionScope.current_user}">
    <input type="hidden" id="golbal_permissions" value="" />
</c:if>
<input type="hidden" id="erpHost" value="${erpHost}" />
<input type="hidden" id="companyConfig" value='${companyConfig}' />
