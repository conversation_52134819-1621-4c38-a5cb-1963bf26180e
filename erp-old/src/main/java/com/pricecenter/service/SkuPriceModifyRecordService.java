package com.pricecenter.service;

import com.pricecenter.dto.PriceChangeAffectOrderIndexDto;
import com.pricecenter.dto.SkuPriceChangeApplyDto;
import com.pricecenter.dto.SkuPriceModifyRecordIndexDto;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.price.dto.SkuPriceModifyRecordSearchDto;
import com.vedeng.price.model.SkuPriceModifyRecord;

import java.util.List;

public interface SkuPriceModifyRecordService {
    
    /**
     * @Description 获取调价列表分页
     * <AUTHOR>
     * @Date 11:04 2021/8/31
     * @Param [queryDto, page]
     * @return java.util.List<com.pricecenter.dto.SkuPriceModifyRecordIndexDto>
     **/
    List<SkuPriceModifyRecordIndexDto> findByPage(SkuPriceModifyRecordSearchDto queryDto, Page page);

    /**
     * 查询商品是否核价
     * @param skuId
     * @return
     */
    SkuPriceChangeApplyDto findPriceChangeApply(Integer skuId) ;


        /**
         * @Description 获取此sku14天内最近一次改价
         * <AUTHOR>
         * @Date 11:04 2021/8/31
         * @Param [skuId]
         * @return com.pricecenter.dto.SkuPriceModifyRecordIndexDto
         **/
    SkuPriceModifyRecordIndexDto getLastestPriceChangeRecord(Integer skuId);
    
    /**
     * @Description 获取昨天的改价列表
     * <AUTHOR>
     * @Date 11:06 2021/8/31
     * @Param []
     * @return java.util.List<com.vedeng.price.model.SkuPriceModifyRecord>
     **/
    List<SkuPriceModifyRecord> getYestedayPriceChangeRecordList();

    /**
     * @Description 获取当前用户持有的当前改价记录影响的订单列表
     * <AUTHOR>
     * @Date 11:07 2021/8/31
     * @Param [skuPriceModifyRecordId, currentUserId, page]
     * @return java.util.List<com.pricecenter.dto.PriceChangeAffectOrderIndexDto>
     **/
    List<PriceChangeAffectOrderIndexDto> getAffectOrderList(Integer skuPriceModifyRecordId, Integer currentUserId, Page page);

    /**
     * @Description 确认影像记录表
     * <AUTHOR>
     * @Date 11:07 2021/8/31
     * @Param [priceChangeAffectOrderId]
     * @return com.vedeng.common.model.ResultInfo<?>
     **/
    ResultInfo<?> dealAffrctOrder(Integer priceChangeAffectOrderId);

    /**
     * @Description 判断是否要弹窗提醒
     * <AUTHOR>
     * @Date 11:07 2021/8/31
     * @Param [userId]
     * @return java.lang.Integer
     **/
    Integer isNeedNotify(Integer userId,Integer positType);

    /**
     * @Description 弹窗提醒后添加记录
     * <AUTHOR>
     * @Date 11:08 2021/8/31
     * @Param [userId]
     * @return void
     **/
    void read(Integer userId);
}
