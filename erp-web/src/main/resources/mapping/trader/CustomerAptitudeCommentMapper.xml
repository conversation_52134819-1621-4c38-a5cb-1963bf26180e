<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.CustomerAptitudeCommentMapper" >
    <resultMap id="BaseResultMap" type="com.vedeng.trader.model.CustomerAptitudeComment" >
    <id column="COMMENT_ID" property="commentId" jdbcType="INTEGER" />
    <result column="TRADER_CUSTOMER_ID" property="traderCustomerId" jdbcType="INTEGER" />
    <result column="COMMENTS" property="comment" jdbcType="INTEGER" />
    </resultMap>
    <select id="selectByCustomerId" resultMap="BaseResultMap">
        select COMMENT_ID,TRADER_CUSTOMER_ID,COMMENTS from T_TRADER_CUSTOMER_APTITUDE_COMMENT
        WHERE TRADER_CUSTOMER_ID=#{traderId,jdbcType=INTEGER}
    </select>

    <insert id="insert" parameterType="com.vedeng.trader.model.CustomerAptitudeComment">
        insert into T_TRADER_CUSTOMER_APTITUDE_COMMENT(TRADER_CUSTOMER_ID,COMMENTS,ADD_TIME,CREATOR)
        VALUES (#{traderCustomerId},#{comment},#{addTime},#{creator})
    </insert>

    <update id="updateByTraderId" parameterType="com.vedeng.trader.model.CustomerAptitudeComment">
        update T_TRADER_CUSTOMER_APTITUDE_COMMENT SET COMMENTS=#{comment} where TRADER_CUSTOMER_ID=#{traderCustomerId}
    </update>
</mapper>