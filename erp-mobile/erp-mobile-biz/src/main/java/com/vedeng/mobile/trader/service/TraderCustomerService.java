package com.vedeng.mobile.trader.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.trader.domain.dto.TraderCustomerPortraitDto;
import com.vedeng.erp.trader.dto.CustomerQueryDto;
import com.vedeng.erp.trader.dto.QueryParamDto;
import com.vedeng.erp.trader.dto.TraderCustomerErpDto;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 掌上小贝客户管理
 * @date 2024/2/2 10:06
 */
public interface TraderCustomerService {

    /**
     * 分页查询
     *
     * @param pageParam pageParam
     * @return
     */
    PageInfo<TraderCustomerErpDto> page(PageParam<CustomerQueryDto> pageParam);

    /**
     * 查询选项
     *
     * @return
     */
    QueryParamDto queryParam();


    /**
     * 基本信息
     *
     * @param traderId         traderId
     * @param traderCustomerId traderCustomerId
     * @return TraderCustomerPortraitDto
     */
    TraderCustomerPortraitDto queryBaseInfo(Integer traderId, Integer traderCustomerId);
}
