<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />
<div id="app" style="display: none;">
    <el-card class="box-card J-block">
        <template>
            <el-form :inline="true" :model="queryDto" ref="form">
                <el-form-item label="商机编号" prop="businessNo" size="mini">
                    <el-input v-model="queryDto.businessNo" placeholder="请输入" style="width: 200px" size="mini"></el-input>
                </el-form-item>

                <el-form-item label="求助人" prop="creatorName" size="mini">
                    <el-input v-model="queryDto.creatorName" placeholder="请输入" style="width: 200px" size="mini"></el-input>
                </el-form-item>

                <el-form-item label="状态" size="mini">
                    <el-select v-model="queryDto.status" size="mini">
                        <el-option label="全部" :value="-1"></el-option>
                        <el-option label="未处理" value="0"></el-option>
                        <el-option label="已处理" value="1"></el-option>
                        <el-option label="关闭" value="2"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="求助时间" size="mini">
                    <el-date-picker
                            v-model="queryDto.addTime"
                            type="daterange"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            :default-time="['00:00:00', '23:59:59']"
                            size="mini">
                    </el-date-picker>
                </el-form-item>
            </el-form>

            <el-row style="margin-bottom: 20px; text-align: center">
                <el-button type="primary" @click="submitForm()" size="mini">搜索</el-button>
                <el-button type="primary" plain @click="reset()" size="mini">重置</el-button>
            </el-row>

            <el-table
                    :data="responseDto"
                    border
                    style="width: 100%;"
                    size="mini">
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="bussinessChanceNo"
                        label="商机编号">
                    <template slot-scope="scope">
                        <el-link type="primary" @click="viewChanceDetail(scope.row)">
                            {{scope.row.bussinessChanceNo || '-'}}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="addTime"
                        label="求助时间">
                    <template slot-scope="scope">
                        <span>{{scope.row.addTime ? parseTime(scope.row.addTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="creatorName"
                        label="求助人">
                    <template slot-scope="scope">
                        <span>{{scope.row.creatorName || '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="content"
                        label="求助内容">
                    <template slot-scope="scope">
                        <span>{{scope.row.content || '-'}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="status"
                        label="状态">
                    <template slot-scope="scope">
                            <span>
                              {{ scope.row.status == 0 ? '未处理' : scope.row.status == 1 ? '已处理' : '关闭' }}
                            </span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        label="操作">
                    <template slot-scope="scope">
                        <vxe-button @click="top(scope.row,1)" v-if="scope.row.status == 0" type="text" status="primary">
                            已处理
                        </vxe-button>
                        <vxe-button @click="top(scope.row,2)" v-if="scope.row.status == 0" type="text" status="primary">
                            关闭
                        </vxe-button>
                        <vxe-button v-else size="small" type="text" status="info">
                            -
                        </vxe-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPageNo"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="currentSize"
                    layout="->,total, sizes, prev, pager, next, jumper"
                    :total="totalLines"
                    size="mini">
            </el-pagination>
        </template>
    </el-card>
</div>

<script src="${pageContext.request.contextPath}/static/api/trader/businessChance.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">
    const leadsNo = '${leadsNo}';
    const from = '${from}';
    const status = '${status}';

    let consultationPendingVm = new Vue({
        el: '#app',
        data() {
            return {
                currentSize: 10,
                currentPageNo: 1,
                totalLines: 0,
                
                queryDto: {
                    businessNo: '',
                    creatorName: '',
                    status: -1,
                    addTime: []
                },
                
                responseDto: []
            }
        },

        mounted() {
            loadingApp();
        },

        created() {
            this.queryDto.businessNo = leadsNo;
            this.queryDto.status = status;
            this.doSearch();
        },

        methods: {

            submitForm() {
                this.currentPageNo = 1;
                this.doSearch();
            },
            
            reset() {
                this.queryDto.businessNo = '';
                this.queryDto.creatorName = '';
                this.queryDto.status = -1;
                this.queryDto.addTime = [];
                this.$nextTick(() => {
                    this.doSearch();
                });
            },

            doSearch() {
                var pageParam = {
                    "pageSize": this.currentSize,
                    "pageNum": this.currentPageNo,
                    "param": {
                        businessNo: this.queryDto.businessNo,
                        creatorName: this.queryDto.creatorName,
                        status: this.queryDto.status,
                        addTime: this.queryDto.addTime
                    }
                };
                console.log(pageParam);
                getConsultationPendingInfo(pageParam).then(res => {
                    console.log(res);
                    this.responseDto = res.data.data.list;
                    this.totalLines = res.data.data.total;
                })
            },

            handleSizeChange(val) {
                this.currentSize = val;
                this.currentPageNo = 1;
                this.doSearch();
            },
            
            handleCurrentChange(val) {
                this.currentPageNo = val;
                this.doSearch();
            },

            viewChanceDetail(row) {
                if(from == 'vx'){
                    openBlank('/businessChance/details.do?id=' + row.bussinessChanceId);
                    return;
                }
                openTab("商机详情", '/businessChance/details.do?id=' + row.bussinessChanceId);
            },

            top(row,status) {
                this.$confirm('是否确认关闭/已处理' + row.bussinessChanceNo + '的商机求助?', '处理确认', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消'
                }).then(() => {
                    processOrCloseSeekHelp({
                        "businessChanceSeekHelpId": row.businessChanceSeekHelpId,
                        "status":status,
                        "businessChanceId":row.bussinessChanceId})
                        .then(res => {
                        if (res.data.code === 0) {
                            this.$message({
                                message: '操作成功',
                                type: 'success'
                            });
                        }
                    })
                    location.reload();
                }).catch(() => {
                    this.$message({
                        message: '操作取消',
                        type: 'info'
                    });
                });
            },
        }
    })
</script>


<style>
    .el-card__header {
        height: 34px;
        background-color: #c5ddfb;
        box-sizing: border-box;
        padding-left: 15px;
        font-weight: 700;
        padding-top: 9px;
        padding-bottom: 30px;
        color: #303133;
    }

    .el-input--mini .el-input__icon {
        line-height: 0;
    }

    .el-pagination .btn-next .el-icon, .el-pagination .btn-prev .el-icon {
        margin-right: 5px;
    }

    .el-date-editor .el-range__icon {
        display: contents;
    }

    .el-date-editor .el-range__close-icon{
        display: contents;
    }

    .el-date-editor .el-range-separator {
        width: 10%;
    }

    .el-select .el-input .el-select__caret {
        display: none;
        width: 200px;
    }

    i {
        display: inline-block;
        height: 12px;
        background: no-repeat;
        margin-bottom: -2px;
    }
</style>