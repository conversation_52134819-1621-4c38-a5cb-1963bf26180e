package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.SaleSettlementAdjustmentBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 销售\售后调整单
 * @author: Suqin
 * @date: 2023/3/1 15:40
 **/
@JobHandler(value = "SaleSettlementAdjustmentBatchTask")
@Component
public class SaleSettlementAdjustmentBatchTask extends AbstractJobHandler {
    @Autowired
    private SaleSettlementAdjustmentBatchJob saleSettlementAdjustmentBatchJob;

    @Autowired
    private JobLauncher jobLauncher;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("====================销售、售后退货调整单对接金蝶==================");
        Job job = saleSettlementAdjustmentBatchJob.saleAdjustmentFlowJob();
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("====================销售、售后退货调整单对接金蝶==================");
        return SUCCESS;
    }
}
