package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeNeedReceiveAdjustEntity;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveDto;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveEntityDto;
import org.mapstruct.*;

import java.util.List;

/**
 * 金蝶 应收余额调整单 dto 转 entity
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, builder = @Builder(disableBuilder = true))
public interface KingDeeNeedReceiveConvertor extends BaseMapStruct<KingDeeNeedReceiveAdjustEntity, KingDeeNeedReceiveDto> {

    /**
     * DTO转Entity
     *
     * @param dto
     * @return
     */
    @Override
    @Mapping(target = "FEntity" , source = "FEntityList", qualifiedByName = "listToJsonArray")
    KingDeeNeedReceiveAdjustEntity toEntity(KingDeeNeedReceiveDto dto);

    /**
     * dto 原List 转 JSON
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("listToString")
    default String listToString(List<KingDeeNeedReceiveEntityDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSON.toJSONString(source);
    }
    @Named("listToJsonArray")
    default JSONArray listToJsonArray(List<KingDeeNeedReceiveEntityDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
}

