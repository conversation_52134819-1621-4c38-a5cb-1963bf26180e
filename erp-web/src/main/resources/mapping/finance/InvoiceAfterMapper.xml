<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.finance.dao.InvoiceAfterMapper">

    <select id="getFinanceAfterListPage" resultType="com.vedeng.finance.model.InvoiceAfter"
            parameterType="java.util.Map">
        SELECT A.AFTER_SALES_ID,
        A.AFTER_SALES_NO,
        A.SUBJECT_TYPE,
        A.TYP<PERSON>,
        A.ORDER_ID,
        A.ORDER_NO,
        A.SERVICE_USER_ID,
        A.VALID_STATUS,
        A.VALID_TIME,
        A.STATUS,
        A.ATFER_SALES_STATUS,
        C.REFUND_AMOUNT_STATUS,
        C.SERVICE_AMOUNT_STATUS,
        C.RECEIVE_PAYMENT_STATUS,
        C<PERSON>INVOICE_STATUS 'openInvoiceStatus',
        T.TRADER_NAME,
        T.TRADER_ID,
        IF(IFNULL(D.Z_NUM, 0) = IFNULL(D.N_NUM, 0), 0, IF(IFNULL(D.Z_NUM, 0) = IFNULL(D.Y_NUM, 0), 1, 2)) AS
        REFUND_INVOICE_STATUS,
        D.IS_REFUND_INVOICE,
        A.ADD_TIME,
        A.CREATOR,
        A.MOD_TIME,
        A.UPDATER,
        B.TITLE
        AS typeName
        FROM T_AFTER_SALES A
        LEFT JOIN T_SYS_OPTION_DEFINITION B
        ON A.TYPE = B.SYS_OPTION_DEFINITION_ID
        AND B.PARENT_ID IN (535, 536, 537)
        LEFT JOIN T_AFTER_SALES_DETAIL C ON A.AFTER_SALES_ID = C.AFTER_SALES_ID
        LEFT JOIN T_TRADER T ON C.TRADER_ID = T.TRADER_ID
        LEFT JOIN (SELECT COUNT(X.AFTER_SALES_INVOICE_ID) AS Z_NUM,
        SUM(IF(X.STATUS = 0, 1, 0)) AS N_NUM,
        SUM(IF(X.STATUS = 1, 1, 0)) AS Y_NUM,
        X.IS_REFUND_INVOICE, <!-- 同一个售后单，发票要么退，要么不退，不存在某个退，某个不退 -->
        X.AFTER_SALES_ID
        FROM T_AFTER_SALES_INVOICE X
        WHERE X.IS_REFUND_INVOICE = 1
        GROUP BY X.AFTER_SALES_ID) D <!-- 同个售后单可能多个需要操作的发票 -->
        ON A.AFTER_SALES_ID = D.AFTER_SALES_ID
        WHERE A.VALID_STATUS = 1
        <if test="invoiceAfter.companyId != null and invoiceAfter.companyId != 0">
            AND A.COMPANY_ID = #{invoiceAfter.companyId}
        </if>
        AND A.TYPE IN
        <foreach collection="invoiceAfter.afterTypeList" item="afterType" open="(" close=")" separator=",">
            #{afterType,jdbcType=VARCHAR}
        </foreach>
        <!-- 售后单号 -->
        <if test="invoiceAfter.afterSalesNo != null and invoiceAfter.afterSalesNo != '' ">
            AND A.AFTER_SALES_NO LIKE CONCAT('%',#{invoiceAfter.afterSalesNo,jdbcType=VARCHAR},'%' )
        </if>
        <!-- 对应订单号 -->
        <if test="invoiceAfter.orderNo != null and invoiceAfter.orderNo != ''">
            AND A.ORDER_NO LIKE CONCAT('%',#{invoiceAfter.orderNo,jdbcType=VARCHAR},'%' )
        </if>
        <!-- 对应合同编号 -->
        <if test="invoiceAfter.traderName != null and invoiceAfter.traderName != ''">
            AND T.TRADER_NAME LIKE CONCAT('%',#{invoiceAfter.traderName,jdbcType=VARCHAR},'%' )
        </if>
        <!-- 订单状态 -->
        <if test="invoiceAfter.status != null">
            AND A.STATUS = #{invoiceAfter.status,jdbcType=BIT}
        </if>
        <!-- 售后订单审核状态 -->
        <if test="invoiceAfter.atferSalesStatus != null">
            AND A.ATFER_SALES_STATUS = #{invoiceAfter.atferSalesStatus,jdbcType=BIT}
        </if>
        <!-- 业务类型 -->
        <if test="invoiceAfter.type != null">
            AND A.TYPE = #{invoiceAfter.type,jdbcType=INTEGER}
        </if>
        <!-- 售后人员 -->
        <if test="invoiceAfter.serviceUserId != null">
            AND A.SERVICE_USER_ID = #{invoiceAfter.serviceUserId,jdbcType=INTEGER}
        </if>

        <if test="invoiceAfter.invoiceSendStatus!=null">
            AND A.INVOICE_SEND_STATUS=#{invoiceAfter.invoiceSendStatus}
        </if>
        <if test="invoiceAfter.invoiceArrivalStatus!=null">
            AND A.INVOICE_ARRIVAL_STATUS=#{invoiceAfter.invoiceArrivalStatus}
        </if>
        <!-- 日期查询类型 -->
        <if test="invoiceAfter.timeType == 1">
            <if test="invoiceAfter.startTime != null and invoiceAfter.startTime != 0">
                AND A.ADD_TIME <![CDATA[ >= ]]>    #{invoiceAfter.startTime}
            </if>
            <if test="invoiceAfter.endTime != null and invoiceAfter.endTime != 0">
                AND A.ADD_TIME <![CDATA[<=]]> #{invoiceAfter.endTime}
            </if>
        </if>
        <if test="invoiceAfter.timeType == 2">
            <if test="invoiceAfter.startTime != null and invoiceAfter.startTime != 0">
                AND A.VALID_TIME <![CDATA[>=]]>    #{invoiceAfter.startTime}
            </if>
            <if test="invoiceAfter.endTime != null and invoiceAfter.endTime != 0">
                AND A.VALID_TIME <![CDATA[<=]]>    #{invoiceAfter.endTime}
            </if>
        </if>
        <!-- 开票状态 -->
        <if test="invoiceAfter.openInvoiceStatus != null">
            AND C.INVOICE_STATUS = #{invoiceAfter.openInvoiceStatus,jdbcType=INTEGER}
        </if>
        <if test="invoiceAfter.receivePaymentStatus != null">
            AND C.RECEIVE_PAYMENT_STATUS = #{invoiceAfter.receivePaymentStatus,jdbcType=INTEGER}
        </if>
        ORDER BY A.ADD_TIME DESC
    </select>

    <!-- 根据售后单号查询开票状态 -->
    <select id="getAfterOpenInvoiceStatus" parameterType="java.util.List" resultType="java.util.Map">
        SELECT A.RELATED_ID, IF(SUM(A.AMOUNT) > 0, 1, 0) AS NUM
        FROM T_INVOICE A
        WHERE A.TYPE = 504 <!-- 售后 -->
        AND A.TAG = 1
        AND A.RELATED_ID IN
        <foreach collection="afterIdList" item="afterId" open="(" close=")" separator=",">
            #{afterId,jdbcType=INTEGER}
        </foreach>
        GROUP BY A.RELATED_ID
    </select>

    <!-- 根据售后ID获取交易对象信息 -->
    <select id="getAfterCapitalBillInfo" parameterType="com.vedeng.aftersales.model.vo.AfterSalesDetailVo"
            resultType="com.vedeng.aftersales.model.vo.AfterSalesDetailVo">
        SELECT A.AFTER_SALES_DETAIL_ID,
        A.AFTER_SALES_ID,
        A.REASON,
        A.TRADER_ID,
        A.COMMENTS,
        A.TRADER_CONTACT_ID,
        A.TRADER_CONTACT_NAME,
        A.TRADER_CONTACT_MOBILE,
        A.TRADER_CONTACT_TELEPHONE,
        A.REFUND,
        A.AREA_ID,
        A.ADDRESS_ID,
        A.AREA,
        A.ADDRESS,
        A.REFUND_AMOUNT,
        A.REFUND_FEE,
        A.REAL_REFUND_AMOUNT,
        A.REFUND_AMOUNT_STATUS,
        A.TRADER_SUBJECT,
        A.SERVICE_AMOUNT,
        A.PAYEE,
        A.BANK,
        A.BANK_CODE,
        A.BANK_ACCOUNT,
        A.TRADER_MODE,
        B.AFTER_SALES_NO,
        B.SUBJECT_TYPE,
        B.ORDER_ID,
        B.ORDER_NO,
        B.COMPANY_ID,
        B.TYPE AS afterType,
        C.TRADER_NAME
        FROM T_AFTER_SALES_DETAIL A
        LEFT JOIN T_AFTER_SALES B ON A.AFTER_SALES_ID = B.AFTER_SALES_ID
        LEFT JOIN T_TRADER C ON A.TRADER_ID = C.TRADER_ID
        WHERE 1 = 1
        <if test="afterSalesId != null">
            AND A.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
        </if>
        <!-- 款项退还0无 1退到客户余额 2退给客户 -->
        <if test="refund != null">
            AND A.REFUND = #{refund,jdbcType=BIT}
        </if>
    </select>

    <resultMap id="afterOrderMultipleAmountResult" type="HashMap">
        <result property="afterSalesId" column="AFTER_SALES_ID"/>
        <result property="subjectType" column="SUBJECT_TYPE"/>
        <result property="orderId" column="ORDER_ID"/>
        <result property="haveAccountPeriod" column="HAVE_ACCOUNT_PERIOD"/>
        <result property="accountPeriodAmount" column="ACCOUNT_PERIOD_AMOUNT"/>
        <result property="totalAmount" column="TOTAL_AMOUNT"/>
        <result property="refundFee" column="REFUND_FEE"/>
        <result property="returnAmount" column="RETURN_AMOUNT"/>
    </resultMap>
    <!-- 获取售后订单多项款项金额（订单总额-退款总额-账期金额-手续费等） -->
    <select id="getAfterOrderMultipleAmount" parameterType="java.lang.Integer"
            resultMap="afterOrderMultipleAmountResult">
		SELECT A.AFTER_SALES_ID,
			   A.SUBJECT_TYPE,
			   A.ORDER_ID,
			   B.HAVE_ACCOUNT_PERIOD,
		       B.ACCOUNT_PERIOD_AMOUNT,
		       B.TOTAL_AMOUNT,
		       C.REFUND_FEE,
		       K.RETURN_AMOUNT
		FROM T_AFTER_SALES A
		     LEFT JOIN T_SALEORDER B ON A.ORDER_ID = B.SALEORDER_ID
		     LEFT JOIN T_AFTER_SALES_DETAIL C ON A.AFTER_SALES_ID = C.AFTER_SALES_ID
		     LEFT JOIN
		     (SELECT M.AFTER_SALES_ID,
		             SUM(IFNULL(N.NUM, 0) * IFNULL(N.PRICE, 0)) AS RETURN_AMOUNT
		      FROM T_AFTER_SALES M
		           LEFT JOIN T_AFTER_SALES_GOODS N
		              ON M.AFTER_SALES_ID = N.AFTER_SALES_ID
			  WHERE M.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
		      GROUP BY M.AFTER_SALES_ID) K
		        ON A.AFTER_SALES_ID = K.AFTER_SALES_ID
		WHERE A.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
	</select>
    <!-- 根据售后单号获取售后详情记录 -->
    <select id="getAfterSalesDetail" parameterType="java.lang.Integer"
            resultType="com.vedeng.aftersales.model.vo.AfterSalesDetailVo">
		SELECT A.SUBJECT_TYPE,A.TYPE AS afterType,A.ORDER_ID,A.AFTER_SALES_NO,A.COMPANY_ID, B.*
		FROM T_AFTER_SALES A
		     LEFT JOIN T_AFTER_SALES_DETAIL B ON A.AFTER_SALES_ID = B.AFTER_SALES_ID
		WHERE A.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
	</select>
    <!-- 修改售后订单手续费状态 -->
    <update id="updateAfterDetailServiceStatus" parameterType="com.vedeng.aftersales.model.vo.AfterSalesDetailVo">
        UPDATE T_AFTER_SALES_DETAIL A SET
        <!-- <if test="serviceAmountStatus != null"> -->
        A.SERVICE_AMOUNT_STATUS = #{serviceAmountStatus,jdbcType=BIT}
        <!-- </if> -->
        <if test="refundAmountStatus != null">
            ,A.REFUND_AMOUNT_STATUS = #{refundAmountStatus,jdbcType=BIT}
        </if>
        <if test="receivePaymentStatus != null">
            ,A.RECEIVE_PAYMENT_STATUS = #{receivePaymentStatus,jdbcType=BIT}
        </if>
        <if test="receivePaymentTime != null">
            ,A.RECEIVE_PAYMENT_TIME = #{receivePaymentTime,jdbcType=BIT}
        </if>
        WHERE A.AFTER_SALES_ID = #{afterSalesId,jdbcType=BIGINT}
    </update>
    <!-- 获取售后发票信息 -->
    <select id="getAfterReturnInvoiceInfo" parameterType="com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo"
            resultType="com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo">
		SELECT A.INVOICE_ID,
			   A.AFTER_SALES_INVOICE_ID,
			   A.AFTER_SALES_ID,
		       B.INVOICE_NO,
		       B.INVOICE_CODE,
		       B.AMOUNT,
		       B.RATIO,
		       B.INVOICE_TYPE,
		       B.COLOR_TYPE,
		       B.IS_ENABLE,
		       B.ADD_TIME,
               B.INVOICE_PROPERTY,
		       C.SUBJECT_TYPE,
		       C.TYPE AS afterType,
		       C.ORDER_ID AS orderId
		FROM T_AFTER_SALES_INVOICE A
		     LEFT JOIN T_INVOICE B ON A.INVOICE_ID = B.INVOICE_ID
		     LEFT JOIN T_AFTER_SALES C ON A.AFTER_SALES_ID = C.AFTER_SALES_ID
		WHERE A.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER} 
			AND A.AFTER_SALES_INVOICE_ID = #{afterSalesInvoiceId,jdbcType=INTEGER}
	</select>

    <select id="getAfterReturnInvoiceGoodsList" parameterType="java.lang.Integer"
            resultType="com.vedeng.aftersales.model.vo.AfterSalesGoodsVo">
        SELECT E.GOODS_ID,
        <if test="subjectType == '535'">
            E.SALEORDER_GOODS_ID AS orderDetailId,
        </if>
        <if test="subjectType == '536'">
            E.REBATE_PRICE,
            E.BUYORDER_GOODS_ID AS orderDetailId,
        </if>
        IF(LENGTH(F.REGISTER_NAME) > 0, F.REGISTER_NAME, E.GOODS_NAME) AS GOODS_NAME,
        E.SKU,
        F.MATERIAL_CODE,
        E.BRAND_NAME,
        E.MODEL,
        <choose>
            <when test="subjectType == '535'">
                ROUND(IF(P.ORDER_TYPE = 5, (E.MAX_SKU_REFUND_AMOUNT / E.NUM), E.PRICE), 8) AS ORDER_PRICE,
            </when>
            <when test="subjectType == '536'"><!-- 采购 -->
                ROUND(E.PRICE, 8) AS ORDER_PRICE,
            </when>
        </choose>
        E.NUM AS ORDER_NUM,
        E.UNIT_NAME,
        IFNULL(G.NUM, 0) AS NUM,
        IFNULL(G.AFTER_SALES_GOODS_ID, 0) AS AFTER_SALES_GOODS_ID,
        <!-- D.NUM AS INVOICE_NUM -->
        D.NUM - IFNULL(H.NUM, 0) AS INVOICE_NUM,
        <!-- add by Tomcat.Hui 2020/8/11 2:48 下午 .Desc: VDERP-1941 【开票软件】为解决退票金额错误问题，
      退票页面展示发票中已有的数据，并改为根据商品ID计算，而不是重新计算再生成。.  -->
        D.TOTAL_AMOUNT - IFNULL(H.TOTAL_AMOUNT,0) AS INVOICE_AMOUNT
        FROM T_AFTER_SALES A
        INNER JOIN T_AFTER_SALES_INVOICE B
        ON A.AFTER_SALES_ID = B.AFTER_SALES_ID
        <!-- LEFT JOIN T_AFTER_SALES_GOODS C ON A.AFTER_SALES_ID = C.AFTER_SALES_ID -->
        LEFT JOIN T_INVOICE_DETAIL D ON B.INVOICE_ID = D.INVOICE_ID
        <if test="subjectType == '535'"><!-- 销售 -->
            LEFT JOIN T_SALEORDER_GOODS E ON D.DETAILGOODS_ID = E.SALEORDER_GOODS_ID
            LEFT JOIN T_SALEORDER P ON E.SALEORDER_ID = P.SALEORDER_ID
        </if>
        <if test="subjectType == '536'"><!-- 采购 -->
            LEFT JOIN T_BUYORDER_GOODS E ON D.DETAILGOODS_ID = E.BUYORDER_GOODS_ID
        </if>
        LEFT JOIN T_GOODS F ON E.GOODS_ID = F.GOODS_ID
        LEFT JOIN (SELECT SUM(NUM) AS NUM, GOODS_ID,AFTER_SALES_GOODS_ID
        FROM T_AFTER_SALES_GOODS
        WHERE AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
        <if test="afterType != '542'"><!--542 销售退票 -->
            AND GOODS_TYPE = 0 <!-- 0普通产品1特殊产品 -->
        </if>
        GROUP BY GOODS_ID) G
        ON G.GOODS_ID = E.GOODS_ID
        LEFT JOIN
        <!-- add by Tomcat.Hui 2020/8/11 2:48 下午 .Desc: VDERP-1941 【开票软件】为解决退票金额错误问题，
        退票页面展示发票中已有的数据，并改为根据商品ID计算，而不是重新计算再生成。.  -->
        (SELECT SUM(ABS(Y.NUM)) AS NUM,SUM(ABS(Y.TOTAL_AMOUNT)) AS TOTAL_AMOUNT, X.RELATE_INVOICE_ID, Y.DETAILGOODS_ID
        FROM T_AFTER_SALES_INVOICE Z
        INNER JOIN T_R_INVOICE_J_INVOICE X
        ON X.RELATE_INVOICE_ID = Z.INVOICE_ID
        INNER JOIN T_INVOICE_DETAIL Y ON X.INVOICE_ID = Y.INVOICE_ID
        WHERE Z.AFTER_SALES_INVOICE_ID = 450
        GROUP BY X.RELATE_INVOICE_ID, Y.DETAILGOODS_ID) H
        ON D.INVOICE_ID = H.RELATE_INVOICE_ID
        <if test="subjectType == '535'"><!-- 销售 -->
            AND E.SALEORDER_GOODS_ID = H.DETAILGOODS_ID
        </if>
        <if test="subjectType == '536'"><!-- 采购 -->
            AND E.BUYORDER_GOODS_ID = H.DETAILGOODS_ID
        </if>

        WHERE A.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
        <if test="afterSalesInvoiceId != null">
            AND B.AFTER_SALES_INVOICE_ID = #{afterSalesInvoiceId,jdbcType=INTEGER}
        </if>
    </select>

    <update id="updaeAfterInvoiceStatus" parameterType="java.lang.Integer">
		UPDATE T_AFTER_SALES_INVOICE A
		SET A.STATUS = 1,A.IS_REFUND_INVOICE = 1
		WHERE INVOICE_ID= #{afterSalesInvoiceId,jdbcType=INTEGER}
	</update>

    <update id="updateAfterInvoiceStatus" parameterType="java.lang.Integer">
        UPDATE T_AFTER_SALES_INVOICE A
        SET A.STATUS = 1,A.IS_REFUND_INVOICE = 1,
        UPDATER = #{updater,jdbcType=INTEGER},
        MOD_TIME = #{modTime,jdbcType=BIGINT}
        WHERE A.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
        <if test="invoiceId != null and invoiceId != 0">
            AND A.INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
        </if>
    </update>
    <!-- 财务-售后-安调-付款申请记录 -->
    <select id="getAfterAtPaymentApply" parameterType="java.lang.Integer"
            resultType="com.vedeng.finance.model.PayApply">
		SELECT A.PAY_APPLY_ID,
		       A.RELATED_ID,
		       A.AMOUNT,
		       A.ADD_TIME,
		       A.CREATOR,
		       A.TRADER_NAME,
		       A.TRADER_SUBJECT,
		       A.VALID_STATUS,
		       A.VALID_TIME,
		       A.VALID_COMMENTS,
		       A.TRADER_NAME,
		       A.BANK,
		       A.BANK_CODE,
		       A.BANK_ACCOUNT,
		       A.COMMENTS,
			   TE.MOBILE,
			   TE.CARD
		FROM T_PAY_APPLY A
		LEFT JOIN (
			SELECT
				b.`NAME` AS na,
				b.MOBILE,
				b.CARD
			FROM
				T_AFTER_SALES_INSTALLSTION TA
				LEFT JOIN T_ENGINEER b ON TA.ENGINEER_ID = b.ENGINEER_ID
			WHERE
				1 = 1
				AND TA.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
			ORDER BY
				TA.SERVICE_TIME DESC
				LIMIT 1
		) TE
		ON A.TRADER_NAME = TE.na
		WHERE A.PAY_TYPE = 518 AND A.RELATED_ID = #{afterSalesId,jdbcType=INTEGER}
		order by A.ADD_TIME DESC
	</select>
    <!-- 获取售后安调开具发票信息 -->
    <select id="getAfterSalesGoodsAtInfo" parameterType="com.vedeng.aftersales.model.vo.AfterSalesGoodsVo"
            resultType="com.vedeng.aftersales.model.vo.AfterSalesGoodsVo">
        SELECT A.AFTER_SALES_GOODS_ID,
        A.AFTER_SALES_ID,
        C.INVOICE_TYPE,
        A.GOODS_ID,
        B.SKU,
        B.GOODS_NAME,
        A.NUM,
        A.PRICE,
        A.SKU_REFUND_AMOUNT
        FROM T_AFTER_SALES_GOODS A
        LEFT JOIN T_GOODS B ON A.GOODS_ID = B.GOODS_ID
        LEFT JOIN T_AFTER_SALES_DETAIL C ON A.AFTER_SALES_ID = C.AFTER_SALES_ID
        WHERE A.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER} AND A.GOODS_TYPE = 1 <!-- 特殊产品 -->
    </select>
    <!-- 查询安调已开票信息 -->
    <select id="getAfterOpenInvoiceInfoAt" parameterType="java.lang.Integer" resultType="java.util.Map">
        SELECT SUM(B.NUM) AS NUM, SUM(A.AMOUNT) AS AMOUNT, A.RELATED_ID
        FROM T_INVOICE A LEFT JOIN T_INVOICE_DETAIL B ON A.INVOICE_ID = B.INVOICE_ID
        WHERE A.RELATED_ID = #{afterSalesId,jdbcType=INTEGER} AND A.VALID_STATUS <![CDATA[ <> ]]> 2 AND A.TYPE =
        504 <!-- 售后票 -->
        AND A.TAG = 1
        GROUP BY A.RELATED_ID
    </select>

    <update id="updateAfterSalesDetail" parameterType="com.vedeng.aftersales.model.AfterSalesDetail">
        update T_AFTER_SALES_DETAIL
        <set>
            <if test="afterSalesId != null">
                AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
            </if>
            <if test="reason != null">
                REASON = #{reason,jdbcType=INTEGER},
            </if>
            <if test="comments != null">
                COMMENTS = #{comments,jdbcType=VARCHAR},
            </if>
            <if test="traderId != null">
                TRADER_ID = #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderContactId != null">
                TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
            </if>
            <if test="traderContactName != null">
                TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
            </if>
            <if test="traderContactMobile != null">
                TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="traderContactTelephone != null">
                TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
            </if>
            <if test="refund != null">
                REFUND = #{refund,jdbcType=BIT},
            </if>
            <if test="areaId != null">
                AREA_ID = #{areaId,jdbcType=INTEGER},
            </if>
            <if test="addressId != null">
                ADDRESS_ID = #{addressId,jdbcType=INTEGER},
            </if>
            <if test="area != null">
                AREA = #{area,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                ADDRESS = #{address,jdbcType=VARCHAR},
            </if>
            <if test="refundAmount != null">
                REFUND_AMOUNT = #{refundAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundFee != null">
                REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
            </if>
            <if test="realRefundAmount != null">
                REAL_REFUND_AMOUNT = #{realRefundAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundAmountStatus != null">
                REFUND_AMOUNT_STATUS = #{refundAmountStatus,jdbcType=BIT},
            </if>
            <if test="traderSubject != null">
                TRADER_SUBJECT = #{traderSubject,jdbcType=BIT},
            </if>
            <if test="payee != null">
                PAYEE = #{payee,jdbcType=VARCHAR},
            </if>
            <if test="bank != null">
                BANK = #{bank,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null">
                BANK_CODE = #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null">
                BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="serviceAmount != null">
                SERVICE_AMOUNT = #{serviceAmount,jdbcType=DECIMAL},
            </if>
            <if test="invoiceType != null">
                INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
            </if>
            <if test="isSendInvoice != null">
                IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BIT},
            </if>
        </set>
        <where>
            1 = 1
            <if test="afterSalesDetailId != null">
                AND AFTER_SALES_DETAIL_ID = #{afterSalesDetailId,jdbcType=INTEGER}
            </if>
            <!-- afterSalesDetailId和afterSalesId一一对应 -->
            <if test="afterSalesId != null">
                AND AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
            </if>
        </where>
    </update>

    <select id="getCapitalBillTotalAmount" parameterType="com.vedeng.finance.model.CapitalBill"
            resultType="java.math.BigDecimal">
        SELECT SUM(A.AMOUNT) AS AMOUNT
        FROM T_CAPITAL_BILL A
        INNER JOIN T_CAPITAL_BILL_DETAIL B
        ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
        WHERE 1 = 1
        <if test="companyId != null">
            AND A.COMPANY_ID = #{companyId,jdbcType=BIT}
        </if>
        <if test="traderSubject != null">
            AND A.TRADER_SUBJECT = #{traderSubject,jdbcType=BIT}
        </if>
        <if test="traderType != null">
            AND A.TRADER_TYPE = #{traderType,jdbcType=BIT}
        </if>
        <if test="traderMode != null">
            AND A.TRADER_MODE = #{traderMode,jdbcType=INTEGER}
        </if>
        <if test="capitalBillDetail.bussinessType != null">
            AND B.BUSSINESS_TYPE = #{capitalBillDetail.bussinessType,jdbcType=INTEGER}
        </if>
        <if test="capitalBillDetail.orderType != null">
            AND B.ORDER_TYPE = #{capitalBillDetail.orderType,jdbcType=BIT}
        </if>
        <if test="capitalBillDetail.orderNo != null">
            AND B.ORDER_NO = #{capitalBillDetail.orderNo,jdbcType=INTEGER}
        </if>
        <if test="capitalBillDetail.relatedId != null">
            AND B.RELATED_ID = #{capitalBillDetail.relatedId,jdbcType=INTEGER}
        </if>
        <if test="capitalBillDetail.traderId != null">
            AND B.TRADER_ID = #{capitalBillDetail.traderId,jdbcType=INTEGER}
        </if>
        <if test="capitalBillDetail.traderType != null">
            AND B.TRADER_TYPE = #{capitalBillDetail.traderType,jdbcType=BIT}
        </if>
    </select>

    <select id="getSaleAfterGoodsListByAfterId" parameterType="java.lang.Integer"
            resultType="com.vedeng.aftersales.model.AfterSalesGoods">
		SELECT A.* FROM T_AFTER_SALES_GOODS A WHERE A.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
	</select>

    <select id="getAfterSalesByAfterInvoiceId" parameterType="java.lang.Integer"
            resultType="com.vedeng.aftersales.model.AfterSales">
		SELECT B.*
		FROM T_AFTER_SALES_INVOICE A
		     INNER JOIN T_AFTER_SALES B ON A.AFTER_SALES_ID = B.AFTER_SALES_ID
		WHERE A.AFTER_SALES_INVOICE_ID = #{afterInvoiceId,jdbcType=INTEGER}
	</select>

    <select id="getAfterSalesByAfterId" parameterType="java.lang.Integer"
            resultType="com.vedeng.aftersales.model.AfterSales">
		SELECT A.* FROM T_AFTER_SALES A WHERE A.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
	</select>

    <update id="updateSupplyBalance">
		UPDATE T_TRADER_SUPPLIER A 
			SET A.AMOUNT = ABS(A.AMOUNT) - ABS(#{amount,jdbcType=DECIMAL})
		WHERE A.TRADER_ID = #{traderId,jdbcType=INTEGER}
	</update>
    <update id="updateAfterOpenInvoiceApply" parameterType="com.vedeng.finance.model.Invoice">
        UPDATE T_INVOICE_APPLY A
        SET
        A.VALID_STATUS = 1
        <if test="updater != null and updater != ''">
            ,A.VALID_USERID = #{updater,jdbcType=INTEGER}
            ,A.UPDATER = #{updater,jdbcType=INTEGER}
        </if>
        <if test="modTime != null and modTime != ''">
            ,A.VALID_TIME = #{modTime,jdbcType=BIGINT}
            ,A.MOD_TIME = #{modTime,jdbcType=BIGINT}
        </if>
        WHERE A.TYPE = 504
        <if test="companyId != null and companyId != 0">
            AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
        </if>
        <if test="relatedId != null and relatedId != ''">
            AND A.RELATED_ID = #{relatedId,jdbcType=INTEGER}
        </if>
        AND A.IS_ADVANCE = 0
        AND A.YY_VALID_STATUS = 1
        AND A.VALID_STATUS = 0
    </update>

    <select id="getGoodsAfterReturnNum" resultType="java.lang.Integer">
        SELECT SUM(B.NUM)
        FROM T_AFTER_SALES A
        INNER JOIN T_AFTER_SALES_GOODS B ON A.AFTER_SALES_ID = B.AFTER_SALES_ID
        WHERE A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
        <if test="afterType != null and afterType == 535">
            AND A.SUBJECT_TYPE = 535    <!-- 销售 -->
            AND A.TYPE = 539    <!-- 退货 -->
        </if>
        <if test="afterType != null and afterType == 536">
            AND A.SUBJECT_TYPE = 536    <!-- 采购 -->
            AND A.TYPE = 546    <!-- 退货 -->
        </if>
        AND B.ORDER_DETAIL_ID = #{saleorderGoodsId,jdbcType=INTEGER}
        AND B.GOODS_TYPE = 0    <!-- 普通产品 -->
        AND A.VALID_STATUS = 1
        AND A.ATFER_SALES_STATUS IN (1,2)
    </select>
    <select id="getGoodsAfterReturnNumNew" resultType="java.lang.Integer">
        SELECT SUM(B.NUM)
        FROM T_AFTER_SALES A
        INNER JOIN T_AFTER_SALES_GOODS B ON A.AFTER_SALES_ID = B.AFTER_SALES_ID
        WHERE A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
        AND A.ATFER_SALES_STATUS = 2
        <if test="afterType != null and afterType == 535">
            AND A.SUBJECT_TYPE = 535    <!-- 销售 -->
            AND A.TYPE = 539    <!-- 退货 -->
        </if>
        <if test="afterType != null and afterType == 536">
            AND A.SUBJECT_TYPE = 536    <!-- 采购 -->
            AND A.TYPE = 546    <!-- 退货 -->
        </if>
        AND B.ORDER_DETAIL_ID = #{saleorderGoodsId,jdbcType=INTEGER}
        AND B.GOODS_TYPE = 0    <!-- 普通产品 -->
        AND A.VALID_STATUS = 1
    </select>
    <select id="getGoodsAfterReturnNumByIdList" resultType="com.vedeng.aftersales.model.AfterSalesGoods">
        SELECT SUM(B.NUM) NUM,ORDER_DETAIL_ID
        FROM T_AFTER_SALES A
        INNER JOIN T_AFTER_SALES_GOODS B ON A.AFTER_SALES_ID = B.AFTER_SALES_ID
        WHERE A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
        <if test="afterType != null and afterType == 535">
            AND A.SUBJECT_TYPE = 535    <!-- 销售 -->
            AND A.TYPE = 539    <!-- 退货 -->
        </if>
        <if test="afterType != null and afterType == 536">
            AND A.SUBJECT_TYPE = 536    <!-- 采购 -->
            AND A.TYPE = 546    <!-- 退货 -->
        </if>
        <if test="goodsIds!=null and goodsIds.size()>0">
            AND B.ORDER_DETAIL_ID in
            <foreach collection="goodsIds" index="index" item="goodsId" open="(" separator="," close=")">
                #{goodsId,jdbcType=INTEGER}
            </foreach>
        </if>
        AND B.GOODS_TYPE = 0    <!-- 普通产品 -->
        AND A.VALID_STATUS = 1
        AND A.ATFER_SALES_STATUS IN (1,2)
        GROUP BY ORDER_DETAIL_ID
    </select>
    <select id="getGoodsAfterReturnNumBuyList" parameterType="map"
            resultType="com.vedeng.aftersales.model.AfterSalesGoods">
        SELECT SUM(B.NUM) NUM,ORDER_DETAIL_ID
        FROM T_AFTER_SALES A
        INNER JOIN T_AFTER_SALES_GOODS B ON A.AFTER_SALES_ID = B.AFTER_SALES_ID
        WHERE A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
        <if test="afterType != null and afterType == 535">
            AND A.SUBJECT_TYPE = 535    <!-- 销售 -->
            AND A.TYPE = 539    <!-- 退货 -->
        </if>
        <if test="afterType != null and afterType == 536">
            AND A.SUBJECT_TYPE = 536    <!-- 采购 -->
            AND A.TYPE = 546    <!-- 退货 -->
        </if>
        <if test="bgvList!=null and bgvList.size()>0">
            AND B.ORDER_DETAIL_ID IN
            <foreach item="bgv" index="index" collection="bgvList" open="(" separator="," close=")">
                #{bgv.buyorderGoodsId,jdbcType=INTEGER}
            </foreach>
        </if>
        AND B.GOODS_TYPE = 0    <!-- 普通产品 -->
        AND A.VALID_STATUS = 1
        AND A.ATFER_SALES_STATUS IN (1,2)
        GROUP BY ORDER_DETAIL_ID
    </select>
    <select id="getAfterReturnInvoiceList" resultType="com.vedeng.finance.model.Invoice">
        SELECT A.INVOICE_ID,
        A.INVOICE_NO,
        A.INVOICE_TYPE,
        A.COLOR_TYPE,
        A.IS_ENABLE,
        A.AMOUNT,
        A.ADD_TIME,
        A.CREATOR
        FROM T_INVOICE A
        WHERE A.TYPE = #{type,jdbcType=INTEGER}
        AND A.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
        <if test="orderId != null and orderId != 0">
            AND A.RELATED_ID = #{orderId,jdbcType=INTEGER}
        </if>
    </select>
    <update id="updateAfterDetailInvoiceStatus" parameterType="com.vedeng.finance.model.Invoice">
		UPDATE T_AFTER_SALES_DETAIL A
		SET A.INVOICE_STATUS = 2, A.INVOICE_TIME = UNIX_TIMESTAMP(SYSDATE()) * 1000
		WHERE     A.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
		      AND (SELECT SUM(A.AMOUNT)
		           FROM T_INVOICE A
		           WHERE     A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		                 AND A.RELATED_ID = #{afterSalesId,jdbcType=INTEGER}
		                 AND A.TYPE = 504
		                 AND A.TAG = 1) 
		           <![CDATA[ = ]]> #{serviceAmount,jdbcType=DECIMAL};
		
		UPDATE T_AFTER_SALES_DETAIL A
		SET A.INVOICE_STATUS = 1, A.INVOICE_TIME = UNIX_TIMESTAMP(SYSDATE()) * 1000
		WHERE     A.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
		      AND (SELECT SUM(A.AMOUNT)
		           FROM T_INVOICE A
		           WHERE     A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		                 AND A.RELATED_ID = #{afterSalesId,jdbcType=INTEGER}
		                 AND A.TYPE = 504
		                 AND A.TAG = 1) 
		           <![CDATA[ < ]]> #{serviceAmount,jdbcType=DECIMAL};
	</update>
    <!-- 查询红字有效的已开票总额 -->
    <select id="selectRedInvoiceAmountCount" parameterType="int" resultType="java.math.BigDecimal">
		SELECT
			SUM(ABS(b.AMOUNT)) 'amountCount'
		FROM
			T_R_INVOICE_J_INVOICE a
		LEFT JOIN 
			T_INVOICE b
		ON a.INVOICE_ID = b.INVOICE_ID
		WHERE
			a.RELATE_INVOICE_ID = #{invoiceId}
		AND b.COLOR_TYPE = 1 and b.IS_ENABLE = 1 and b.VALID_STATUS  !=  2
	</select>

    <select id="queryTpAfterSalesGoods" resultType="com.vedeng.aftersales.model.vo.AfterSalesGoodsVo">
        SELECT A.GOODS_ID,A.ORDER_DETAIL_ID,B.GOODS_NAME,B.SKU,C.MATERIAL_CODE,B.BRAND_NAME,B.MODEL,A.AFTER_INVOICE_NUM AS INVOICE_NUM,
               B.PRICE AS ORDER_PRICE,(B.NUM - B.AFTER_RETURN_NUM) AS ORDER_NUM,B.UNIT_NAME,A.AFTER_INVOICE_NUM,A.AFTER_SALES_GOODS_ID
            FROM T_AFTER_SALES_GOODS A
            LEFT JOIN T_BUYORDER_GOODS B ON A.ORDER_DETAIL_ID = B.BUYORDER_GOODS_ID
            LEFT JOIN V_CORE_SKU C ON B.GOODS_ID = C.SKU_ID
        WHERE A.GOODS_TYPE = 0 AND A.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
    </select>
</mapper>
