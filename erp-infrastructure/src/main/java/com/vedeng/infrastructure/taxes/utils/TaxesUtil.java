package com.vedeng.infrastructure.taxes.utils;

import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.infrastructure.taxes.config.TaxesConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * 税金工具类
 */
@Slf4j
@Component
public class TaxesUtil {

    public static TaxesConfig taxesConfig;

    static final String HMAC_SHA512 = "HmacSHA512";

    @Autowired
    public TaxesUtil(TaxesConfig taxesConfig) {
        TaxesUtil.taxesConfig = taxesConfig;
    }

    /**
     * 算签
     * @param dataJson
     * @return
     */
    public static String getTaxesSign(String dataJson) {
        log.info("税金算签开始");
        byte[] byteKey = taxesConfig.getAppSecret().getBytes(StandardCharsets.UTF_8);
        SecretKeySpec secretKeySpec = new SecretKeySpec(byteKey, HMAC_SHA512);
        byte[] byteData;
        try {
            Mac mac = Mac.getInstance(HMAC_SHA512);
            mac.init(secretKeySpec);
            byteData = mac.doFinal(dataJson.getBytes(StandardCharsets.UTF_8));
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new ServiceException("航信算签异常");
        }
        String dataSign = Base64Utils.encodeToString(byteData);
        log.info("税金算签结束：{}",dataSign);
        return dataSign;
    }
}
