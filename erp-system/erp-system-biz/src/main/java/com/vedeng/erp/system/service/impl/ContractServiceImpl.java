package com.vedeng.erp.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.base.api.dto.resParam.SignCompanyInfo;
import com.vedeng.erp.buyorder.dto.BuyOrderApiDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.system.domain.entity.BaseCompanyInfoEntity;
import com.vedeng.erp.system.domain.entity.FlowNodeEntity;
import com.vedeng.erp.system.domain.entity.FlowOrderEntity;
import com.vedeng.erp.system.domain.entity.FlowOrderInfoEntity;
import com.vedeng.erp.system.handler.FlowOrderElectronicSignHandler;
import com.vedeng.erp.system.mapper.FlowNodeMapper;
import com.vedeng.erp.system.mapper.FlowOrderInfoMapper;
import com.vedeng.erp.system.mapper.FlowOrderMapper;
import com.vedeng.erp.system.service.BaseCompanyInfoService;
import com.vedeng.erp.system.service.ContractService;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 合同服务实现类
 *
 * <AUTHOR>
 * @date 2023-07-01
 */
@Slf4j
@Service
public class ContractServiceImpl implements ContractService {

    @Autowired
    private FlowOrderMapper flowOrderMapper;

    @Autowired
    private FlowNodeMapper flowNodeMapper;

    @Autowired
    private FlowOrderInfoMapper flowOrderInfoMapper;

    @Autowired
    private FlowOrderElectronicSignHandler flowOrderElectronicSignHandler;

    @Autowired
    private BaseCompanyInfoService baseCompanyInfoService;

    @Autowired
    private BuyorderApiService buyorderApiService;

    @Autowired
    private SaleOrderApiService saleOrderApiService;


    @Override
    public boolean signContract(Long orderId, Integer type) {
        log.info("开始签署合同,流转单ID[{}],合同类型[{}]", orderId, type);

        try {
            // 1. 验证参数
            if (orderId == null || type == null) {
                log.error("签署合同参数错误,流转单ID[{}],合同类型[{}]", orderId, type);
                return false;
            }

            // 2. 查询流转单
            List<FlowOrderEntity> flowOrderList = flowOrderMapper.findByFlowOrderId(orderId);
            if (flowOrderList == null || flowOrderList.isEmpty()) {
                log.error("签署合同流转单不存在,流转单ID[{}]", orderId);
                return false;
            }
            FlowOrderEntity flowOrder = flowOrderList.get(0);

            // 3. 验证流转单状态
            if (flowOrder.getAuditStatus() != 1) {
                log.error("签署合同流转单未审核通过,流转单ID[{}],审核状态[{}]", orderId, flowOrder.getAuditStatus());
                return false;
            }

            // 4. 获取流转单节点
            List<FlowNodeEntity> nodeList = flowNodeMapper.findByFlowOrderId(orderId);
            if (nodeList == null || nodeList.isEmpty()) {
                log.error("签署合同流转单节点不存在,流转单ID[{}]", orderId);
                return false;
            }

            // 5. 按节点级别排序
            nodeList.sort(Comparator.comparing(FlowNodeEntity::getNodeLevel));

            // 5.1 处理0-1节点的签章
            processZeroToOneNodeSign(orderId, type, flowOrder);

            // 6. 循环处理相邻节点之间的签章
            for (int i = 0; i < nodeList.size() - 1; i++) {
                FlowNodeEntity currentNode = nodeList.get(i);
                FlowNodeEntity nextNode = nodeList.get(i + 1);

                // 检查当前节点是否已有合同链接，如果有则跳过
                List<FlowOrderInfoEntity> currentNodeInfoList = flowOrderInfoMapper.findByFlowNodeId(currentNode.getFlowNodeId());
                boolean hasContract = false;
                if (currentNodeInfoList != null && !currentNodeInfoList.isEmpty()) {
                    for (FlowOrderInfoEntity info : currentNodeInfoList) {
                        if (info != null && StrUtil.isNotEmpty(info.getContractFileUrl())) {
                            hasContract = true;
                            log.info("当前节点已有合同链接，跳过签章,流转单ID[{}],节点ID[{}],节点级别[{}]",
                                    orderId, currentNode.getFlowNodeId(), currentNode.getNodeLevel());
                            break;
                        }
                    }
                }

                if (hasContract) {
                    continue;
                }

                // 构建电子签章参数
                BusinessInfo businessInfo = new BusinessInfo();
                businessInfo.setOperator("系统");

                // 根据合同类型设置业务枚举
                ElectronicSignBusinessEnums businessEnum = ElectronicSignBusinessEnums.MULT_LIUZHUANG_SEAL;

                // 根据流转单类型确定甲方和乙方的顺序
                String partyA, partyB;
                if (type == 1) {
                    // 采购流转单：当前节点为甲方，下一节点为乙方
                    partyA = currentNode.getTraderName();
                    partyB = nextNode.getTraderName();
                } else {
                    // 销售流转单：下一节点为甲方，当前节点为乙方（与采购相反）
                    partyA = nextNode.getTraderName();
                    partyB = currentNode.getTraderName();
                }

                // 获取签章公司信息
                List<SignCompanyInfo> signCompanyInfoList = getSignCompanyInfoList(type, partyA, partyB);

                // 获取当前节点的 FlowOrderInfoEntity
                FlowOrderInfoEntity flowOrderInfo = flowOrderInfoMapper.findByFlowNodeIdAndType(currentNode.getFlowNodeId(), type == 1 ? 0 : 1);
                if (flowOrderInfo == null) {
                    log.error("签署合同失败,流转单ID[{}],节点ID[{}],合同信息不存在",
                            orderId, currentNode.getFlowNodeId());
                    continue; // 跳过当前节点，继续处理下一个节点
                }

                // 构建电子签章参数
                ElectronicSignParam electronicSignParam = ElectronicSignParam.builder()
                        .businessInfo(businessInfo)
                        .electronicSignBusinessEnums(businessEnum)
                        .businessId(flowOrderInfo.getFlowOrderInfoId().toString()) // 使用 flowOrderInfoId 而不是 flowOrderId
                        .flowType(3) // 默认使用多主体签章模式
                        .signCompanyInfoList(signCompanyInfoList)
                        .build();

                log.info("签署合同使用多主体签章模式,流转单ID[{}],合同类型[{}],甲方[{}],乙方[{}],当前节点[{}],下一节点[{}]",
                        orderId, type, partyA, partyB, currentNode.getTraderName(), nextNode.getTraderName());

                log.info("签署合同使用多主体签章模式,参数electronicSignParam{}", JSON.toJSONString(electronicSignParam));

                // 调用电子签章服务
                flowOrderElectronicSignHandler.electronicSign(electronicSignParam);

                log.info("签署合同成功,流转单ID[{}],合同类型[{}],节点级别[{}]到[{}]",
                        orderId, type, currentNode.getNodeLevel(), nextNode.getNodeLevel());
            }

            return true;
        } catch (Exception e) {
            log.error("签署合同异常,流转单ID[{}],合同类型[{}]", orderId, type, e);
            return false;
        }
    }


    private static final String SEAL_NAME = "合同专用章"; // 印章名称

    /**
     * 判断公司是否为贝登子公司
     *
     * @param companyName 公司名称
     * @return 是否为贝登子公司
     */
    public boolean isBeidengSubsidiary(String companyName) {
        if (StrUtil.isEmpty(companyName)) {
            return false;
        }

        BaseCompanyInfoEntity company = baseCompanyInfoService.selectByCompanyName(companyName);
        return company != null;
    }


    /**
     * 处理0-1节点的签章
     *
     * @param orderId   流转单ID
     * @param type      合同类型：1-采购合同，2-销售合同
     * @param flowOrder 流转单实体
     */
    private void processZeroToOneNodeSign(Long orderId, Integer type, FlowOrderEntity flowOrder) {
        log.info("开始处理0-1节点的签章,流转单ID[{}],合同类型[{}]", orderId, type);

        try {
            // 获取合同URL
            String contractUrl = null;
            Integer baseOrderId = flowOrder.getBaseOrderId();

            if (baseOrderId == null) {
                log.error("处理0-1节点签章失败,流转单ID[{}],基础订单ID为空", orderId);
                return;
            }

            // 根据合同类型获取不同的合同URL
            if (type == 1) {
                // 采购流转，先检查合同回传状态
                log.info("采购流转,检查合同回传状态,基础订单ID[{}]", baseOrderId);

                // 查询合同回传状态
                Integer contractReturnStatus = flowOrderMapper.getContractReturnStatusByBuyorderId(baseOrderId);
                if (contractReturnStatus != null && contractReturnStatus == 1) {
                    log.info("采购流转,合同已回传,跳过0-1节点签章,基础订单ID[{}]", baseOrderId);
                    return;
                }

                // 采购流转，从T_BUYORDER的CONTRACT_URL获取
                log.info("采购流转,从T_BUYORDER获取CONTRACT_URL,基础订单ID[{}]", baseOrderId);
                // 获取采购单信息
                BuyOrderApiDto buyorderByBuyorderId = buyorderApiService.getBuyorderByBuyorderId(baseOrderId);
                if (buyorderByBuyorderId != null) {
                    contractUrl = buyorderByBuyorderId.getContractUrl();
                    log.info("采购流转,获取到CONTRACT_URL[{}]", contractUrl);
                } else {
                    log.error("采购流转,未找到采购单信息,基础订单ID[{}]", baseOrderId);
                    return;
                }

            } else if (type == 2) {
                // 销售流转，从T_SALEORDER的CONTRACT_URL获取
                log.info("销售流转,从T_SALEORDER获取CONTRACT_URL,基础订单ID[{}]", baseOrderId);
                // 获取销售单信息
                SaleorderInfoDto bySaleOrderId = saleOrderApiService.getBySaleOrderId(baseOrderId);
                if (bySaleOrderId != null) {
                    contractUrl = bySaleOrderId.getContractUrl();
                    log.info("销售流转,获取到CONTRACT_URL[{}]", contractUrl);
                } else {
                    log.error("销售流转,未找到销售单信息,基础订单ID[{}]", baseOrderId);
                    return;
                }
            } else {
                log.error("处理0-1节点签章失败,流转单ID[{}],不支持的合同类型[{}]", orderId, type);
                return;
            }

            // 校验合同URL是否有值
            if (StrUtil.isEmpty(contractUrl)) {
                log.info("0-1节点合同URL为空,跳过签章,流转单ID[{}],合同类型[{}]", orderId, type);
                return;
            }

            // 获取第一个节点
            List<FlowNodeEntity> nodeList = flowNodeMapper.findByFlowOrderId(orderId);
            if (nodeList == null || nodeList.isEmpty()) {
                log.error("处理0-1节点签章失败,流转单ID[{}],节点列表为空", orderId);
                return;
            }

            // 按节点级别排序
            nodeList.sort(Comparator.comparing(FlowNodeEntity::getNodeLevel));
            FlowNodeEntity firstNode = nodeList.get(0);

            // 检查第一个节点是否已有合同链接，如果有则跳过
            List<FlowOrderInfoEntity> firstNodeInfoList = flowOrderInfoMapper.findByFlowNodeId(firstNode.getFlowNodeId());
            boolean hasContract = false;
            if (firstNodeInfoList != null && !firstNodeInfoList.isEmpty()) {
                for (FlowOrderInfoEntity info : firstNodeInfoList) {
                    if (info != null && StrUtil.isNotEmpty(info.getContractFileUrl())) {
                        hasContract = true;
                        log.info("0-1节点已有合同链接，跳过签章,流转单ID[{}],节点ID[{}],节点级别[{}]",
                                orderId, firstNode.getFlowNodeId(), firstNode.getNodeLevel());
                        break;
                    }
                }
            }

            if (hasContract) {
                return;
            }

            // 获取第一个节点的 FlowOrderInfoEntity
            FlowOrderInfoEntity flowOrderInfo = flowOrderInfoMapper.findByFlowNodeIdAndType(firstNode.getFlowNodeId(), type == 1 ? 0 : 1);
            if (flowOrderInfo == null) {
                log.error("处理0-1节点签章失败,流转单ID[{}],节点ID[{}],合同信息不存在",
                        orderId, firstNode.getFlowNodeId());
                return;
            }

            // 构建电子签章参数
            BusinessInfo businessInfo = new BusinessInfo();
            businessInfo.setOperator("系统");

            // 根据合同类型设置业务枚举
            ElectronicSignBusinessEnums businessEnum = ElectronicSignBusinessEnums.MULT_LIUZHUANG_SEAL;

            // 使用第一个节点的交易者名称作为签章方
            String partyName = firstNode.getTraderName();

            // 构建签章公司信息
            List<SignCompanyInfo> signCompanyInfoList = new ArrayList<>();
            SignCompanyInfo signCompanyInfo = new SignCompanyInfo();
            signCompanyInfo.setSignCompanyName(partyName);

            // 根据合同类型确定签章定位方式
            String hideCompanyName;
            String positionType;

            if (type == 1) {
                // 采购合同：0-1节点使用原始合同模板，没有预设关键词标记，使用公司名称定位
                hideCompanyName = partyName;
                positionType = "公司名称";
                log.info("0-1节点采购合同签章：使用公司名称定位方式，公司[{}]", partyName);
            } else {
                // 销售合同：使用关键词标记进行精确定位
                hideCompanyName = SALE_PARTY_A_SIGN_KEYWORD;
                positionType = "关键词标记";
                log.info("0-1节点销售合同签章：使用关键词标记定位方式，关键词[{}]", hideCompanyName);
            }

            signCompanyInfo.setHideCompanyName(hideCompanyName);
            signCompanyInfo.setSignAllSeal(true);
            signCompanyInfo.setSignSealName(SEAL_NAME);
            signCompanyInfoList.add(signCompanyInfo);

            log.info("0-1节点签章配置完成：公司[{}]，合同类型[{}]，定位方式[{}]，定位值[{}]",
                    partyName, type, positionType, hideCompanyName);

            // 构建电子签章参数，根据合同类型设置相应的字段
            ElectronicSignParam.ElectronicSignParamBuilder builder = ElectronicSignParam.builder()
                    .businessInfo(businessInfo)
                    .electronicSignBusinessEnums(businessEnum)
                    .businessId(flowOrder.getFlowOrderNo()) // 使用flowOrderNo作为businessId
                    .flowType(3) // 默认使用多主体签章模式
                    .signCompanyInfoList(signCompanyInfoList);

            ElectronicSignParam electronicSignParam = builder.build();

            log.info("0-1节点签署合同使用多主体签章模式,流转单ID[{}],合同类型[{}],签章方[{}]",
                    orderId, type, partyName);

            // 调用电子签章服务
            flowOrderElectronicSignHandler.electronicSign(electronicSignParam);

            log.info("0-1节点签署合同成功,流转单ID[{}],合同类型[{}],节点级别[{}]",
                    orderId, type, firstNode.getNodeLevel());
        } catch (Exception e) {
            log.error("处理0-1节点签章异常,流转单ID[{}],合同类型[{}]", orderId, type, e);
        }
    }

    // 签章关键词常量 - 与合同模板中的关键词标记保持一致
    // 销售合同关键词
    private static final String SALE_PARTY_A_SIGN_KEYWORD = "$jia$";  // 销售合同甲方签章关键词
    private static final String SALE_PARTY_B_SIGN_KEYWORD = "$yi$";   // 销售合同乙方签章关键词

    // 采购合同关键词
    private static final String BUY_PARTY_A_SIGN_KEYWORD = "$jia$";     // 采购合同甲方签章关键词
    private static final String BUY_PARTY_B_SIGN_KEYWORD = "$yi$"; // 采购合同乙方签章关键词

    /**
     * 获取签章公司信息
     *
     * @param contractType 合同类型：1-采购合同，2-销售合同
     * @param partyA       甲方名称
     * @param partyB       乙方名称
     * @return 签章公司信息列表
     */
    public List<SignCompanyInfo> getSignCompanyInfoList(Integer contractType, String partyA, String partyB) {
        List<SignCompanyInfo> signCompanyInfoList = new ArrayList<>();

        // 检查甲方和乙方是否为贝登子公司
        boolean isPartyABeideng = isBeidengSubsidiary(partyA);
        boolean isPartyBBeideng = isBeidengSubsidiary(partyB);

        log.info("签章公司信息：甲方[{}]是否贝登子公司[{}]，乙方[{}]是否贝登子公司[{}]",
                partyA, isPartyABeideng, partyB, isPartyBBeideng);

        // 根据合同类型和公司关系决定签章策略
        boolean signPartyA = false; // 默认不签章
        boolean signPartyB = false; // 默认不签章

        // 采购合同：贝登作为乙方
        if (contractType == 1) {
            // 如果乙方是贝登子公司，则必须签乙方章
            if (isPartyBBeideng) {
                signPartyB = true;
                // 如果甲方不是贝登子公司，则只签乙方章
                signPartyA = isPartyABeideng;
            } else {
                // 如果乙方不是贝登子公司，但甲方是，则只签甲方章
                signPartyA = isPartyABeideng;
            }
        }
        // 销售合同：贝登作为甲方
        else if (contractType == 2) {
            // 如果甲方是贝登子公司，则必须签甲方章
            if (isPartyABeideng) {
                signPartyA = true;
                // 如果乙方不是贝登子公司，则只签甲方章
                signPartyB = isPartyBBeideng;
            } else {
                // 如果甲方不是贝登子公司，但乙方是，则只签乙方章
                signPartyB = isPartyBBeideng;
            }
        }

        log.info("签章策略：甲方[{}]是否签章[{}]，乙方[{}]是否签章[{}]",
                partyA, signPartyA, partyB, signPartyB);

        // 根据合同类型确定关键词
        String partyAKeyword, partyBKeyword;
        if (contractType == 1) {
            // 采购合同
            partyAKeyword = BUY_PARTY_A_SIGN_KEYWORD;
            partyBKeyword = BUY_PARTY_B_SIGN_KEYWORD;
        } else {
            // 销售合同
            partyAKeyword = SALE_PARTY_A_SIGN_KEYWORD;
            partyBKeyword = SALE_PARTY_B_SIGN_KEYWORD;
        }

        // 甲方
        if (signPartyA) {
            SignCompanyInfo signCompanyInfoA = new SignCompanyInfo();
            signCompanyInfoA.setSignCompanyName(partyA);
            signCompanyInfoA.setHideCompanyName(partyAKeyword); // 使用关键词标记进行精确定位
            signCompanyInfoA.setSignAllSeal(true);
            signCompanyInfoA.setSignSealName(SEAL_NAME);
            signCompanyInfoList.add(signCompanyInfoA);
            log.info("添加甲方签章信息：公司[{}]，合同类型[{}]，关键词[{}]", partyA, contractType, partyAKeyword);
        }

        // 乙方
        if (signPartyB) {
            SignCompanyInfo signCompanyInfoB = new SignCompanyInfo();
            signCompanyInfoB.setSignCompanyName(partyB);
            signCompanyInfoB.setHideCompanyName(partyBKeyword); // 使用关键词标记进行精确定位
            signCompanyInfoB.setSignAllSeal(true);
            signCompanyInfoB.setSignSealName(SEAL_NAME);
            signCompanyInfoList.add(signCompanyInfoB);
            log.info("添加乙方签章信息：公司[{}]，合同类型[{}]，关键词[{}]", partyB, contractType, partyBKeyword);
        }

        return signCompanyInfoList;
    }

}
