<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.newtask.ddi.dao.DdiGeOrderMapper">
  <resultMap id="BaseResultMap" type="com.newtask.ddi.model.DdiGeOrder">
    <id column="T_DDI_SALEORDER_ID" jdbcType="INTEGER" property="tDdiSaleorderId" />
    <result column="DISTRIBUTOR" jdbcType="VARCHAR" property="distributor" />
    <result column="QUOTE_ID" jdbcType="VARCHAR" property="quoteId" />
    <result column="SALEORDER_NO" jdbcType="VARCHAR" property="saleorderNo" />
    <result column="DELIVERY_TIME" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="GOODS_TYPE" jdbcType="VARCHAR" property="goodsType" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="GOODS_MODEL" jdbcType="VARCHAR" property="goodsModel" />
    <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="SERIAL_NUMBER" jdbcType="VARCHAR" property="serialNumber" />
    <result column="EFFECTIVE_DAYS" jdbcType="INTEGER" property="effectiveDays" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="UNIT" jdbcType="VARCHAR" property="unit" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="MANUFACTURER" jdbcType="VARCHAR" property="manufacturer" />
    <result column="SHIPMENT_TYPE" jdbcType="INTEGER" property="shipmentType" />
    <result column="TRADER_CITY" jdbcType="VARCHAR" property="traderCity" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="WAREHOUSE_OUT_NO" jdbcType="VARCHAR" property="warehouseOutNo" />
    <result column="DISTRIBUTOR_ID" jdbcType="INTEGER" property="distributorId" />
    <result column="DISTRIBUTOR_NAME" jdbcType="VARCHAR" property="distributorName" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    T_DDI_SALEORDER_ID, DISTRIBUTOR, QUOTE_ID, SALEORDER_NO, DELIVERY_TIME, GOODS_TYPE, 
    GOODS_NAME, GOODS_MODEL, REGISTRATION_NUMBER, SERIAL_NUMBER, EFFECTIVE_DAYS, TRADER_ID, 
    TRADER_NAME, NUM, UNIT, PRICE, AMOUNT, MANUFACTURER, SHIPMENT_TYPE, TRADER_CITY, 
    REMARK, WAREHOUSE_OUT_NO, DISTRIBUTOR_ID, DISTRIBUTOR_NAME, ADD_TIME, MODE_TIME, 
    IS_DELETE, CREATOR, UPDATER
  </sql>
  <resultMap id="ResultMap" type="com.newtask.ddi.model.DdiGeOrder">
    <id column="T_DDI_SALEORDER_ID" jdbcType="INTEGER" property="tDdiSaleorderId" />
    <result column="DISTRIBUTOR" jdbcType="VARCHAR" property="distributor" />
    <result column="QUOTE_ID" jdbcType="VARCHAR" property="quoteId" />
    <result column="SALEORDER_NO" jdbcType="VARCHAR" property="saleorderNo" />
    <result column="DELIVERY_TIME" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="GOODS_TYPE" jdbcType="VARCHAR" property="goodsType" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="GOODS_MODEL" jdbcType="VARCHAR" property="goodsModel" />
    <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="SERIAL_NUMBER" jdbcType="VARCHAR" property="serialNumber" />
    <result column="EFFECTIVE_DAYS" jdbcType="INTEGER" property="effectiveDays" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="UNIT" jdbcType="VARCHAR" property="unit" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="MANUFACTURER" jdbcType="VARCHAR" property="manufacturer" />
    <result column="SHIPMENT_TYPE" jdbcType="VARCHAR" property="shipmentType" />
    <result column="TRADER_CITY" jdbcType="VARCHAR" property="traderCity" />
    <result column="TRADER_ADDRESS" jdbcType="VARCHAR" property="traderAddress" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="WAREHOUSE_OUT_NO" jdbcType="VARCHAR" property="warehouseOutNo" />
    <result column="DISTRIBUTOR_ID" jdbcType="INTEGER" property="distributorId" />
    <result column="DISTRIBUTOR_NAME" jdbcType="VARCHAR" property="distributorName" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>

  <select id="getGeOrders" parameterType="com.wms.model.WmsOutSnCodeOrder" resultMap="ResultMap">
    SELECT
    <if test="operateType == 0 ">
      COALESCE(E.GE_CONTRACT_NO, BUG.GE_CONTRACT_NO) AS QUOTE_ID,
      COALESCE(E.GE_SALE_CONTRACT_NO, BUG.GE_SALE_CONTRACT_NO) AS SALEORDER_NO,
      B.DELIVERY_NUM AS NUM,
      B.UNIT_NAME AS UNIT,
      B.REAL_PRICE AS PRICE,
      B.REAL_PRICE AS AMOUNT,
      "销售出库" AS SHIPMENT_TYPE ,
      U.SALEORDER_NO AS WAREHOUSE_OUT_NO,
      IF(U.ORDER_TYPE = 5,U.TRADER_ID,U.TERMINAL_TRADER_ID) AS TRADER_ID,
      IF(U.ORDER_TYPE = 5,U.TRADER_NAME,U.TERMINAL_TRADER_NAME ) AS TRADER_NAME,
      IF(U.ORDER_TYPE = 5,U.TRADER_AREA,U.SALES_AREA) AS TRADER_CITY,
      IF(U.ORDER_TYPE = 5,U.TRADER_ADDRESS,U.SALES_AREA) AS TRADER_ADDRESS,
    </if>
    <if test="operateType == 1 ">
      COALESCE(E.GE_CONTRACT_NO, BUG.GE_CONTRACT_NO) AS QUOTE_ID,
      COALESCE(E.GE_SALE_CONTRACT_NO, BUG.GE_SALE_CONTRACT_NO) AS SALEORDER_NO,
      B.DELIVERY_NUM AS NUM,
      SA.UNIT_NAME AS UNIT,
      SA.PRICE AS PRICE,
      SA.REAL_PRICE AS AMOUNT,
      "销售售后出库" AS SHIPMENT_TYPE,
      C.AFTER_SALES_NO AS WAREHOUSE_OUT_NO,
      IF(U.ORDER_TYPE = 5,U.TRADER_ID,U.TERMINAL_TRADER_ID) AS TRADER_ID,
      IF(U.ORDER_TYPE = 5,U.TRADER_NAME,U.TERMINAL_TRADER_NAME ) AS TRADER_NAME,
      IF(U.ORDER_TYPE = 5,U.TRADER_AREA,U.SALES_AREA) AS TRADER_CITY,
      IF(U.ORDER_TYPE = 5,U.TRADER_ADDRESS,U.SALES_AREA ) AS TRADER_ADDRESS,
    </if>
    <if test="operateType == 2 ">
      E.GE_CONTRACT_NO AS QUOTE_ID,
      E.GE_SALE_CONTRACT_NO AS SALEORDER_NO,
      E.ARRIVAL_NUM AS NUM,
      E.UNIT_NAME AS UNIT,
      E.PRICE AS PRICE,
      E.PRICE AS AMOUNT,
      "采购售后出库" AS SHIPMENT_TYPE,
      C.AFTER_SALES_NO AS WAREHOUSE_OUT_NO,
      U.TRADER_ID AS TRADER_ID,
      U.TRADER_NAME AS TRADER_NAME,
      U.TRADER_AREA AS TRADER_CITY,
      U.TRADER_ADDRESS AS TRADER_ADDRESS,
    </if>
    <if test="operateType == 3 ">
      COALESCE(E.GE_CONTRACT_NO, BUG.GE_CONTRACT_NO) AS QUOTE_ID,
      COALESCE(E.GE_SALE_CONTRACT_NO, BUG.GE_SALE_CONTRACT_NO) AS SALEORDER_NO,
      X.already_output_num AS NUM,
      coalesce(E.UNIT_NAME,BUG.UNIT_NAME) AS UNIT,
      coalesce(E.PRICE,BUG.PRICE) AS PRICE,
      coalesce(E.PRICE,BUG.PRICE) AS AMOUNT,
      "借货出库" AS SHIPMENT_TYPE,
      Y.order_no AS WAREHOUSE_OUT_NO,
      Y.borrow_trader_id AS TRADER_ID,
      Y.borrow_trader_name AS TRADER_NAME,
      Y.receiver_address AS TRADER_CITY,
      Y.detail_address AS TRADER_ADDRESS,
    </if>
    <if test="operateType == 4 ">
      COALESCE(E.GE_CONTRACT_NO, BUG.GE_CONTRACT_NO) AS QUOTE_ID,
      COALESCE(E.GE_SALE_CONTRACT_NO, BUG.GE_SALE_CONTRACT_NO) AS SALEORDER_NO,
      X.already_output_num AS NUM,
      coalesce(E.UNIT_NAME,BUG.UNIT_NAME) AS UNIT,
      coalesce(E.PRICE,BUG.PRICE) AS PRICE,
      coalesce(E.PRICE,BUG.PRICE) AS AMOUNT,
      "报废出库" AS SHIPMENT_TYPE,
      Y.order_no AS WAREHOUSE_OUT_NO,
      Y.borrow_trader_id AS TRADER_ID,
      Y.borrow_trader_name AS TRADER_NAME,
      Y.receiver_address AS TRADER_CITY,
      Y.detail_address AS TRADER_ADDRESS,
    </if>
    "贝登" AS DISTRIBUTOR,
    A.OUT_TIME AS DELIVERY_TIME,
    A.SN_CODE AS SERIAL_NUMBER,
    A.EXPIRATION_DATE AS EFFECTIVE_DAYS,
    H.TITLE AS GOODS_TYPE,
    F.SHOW_NAME AS GOODS_NAME,
    F.MODEL AS GOODS_MODEL,
    J.REGISTRATION_NUMBER AS REGISTRATION_NUMBER,
    K.PRODUCT_COMPANY_CHINESE_NAME AS MANUFACTURER
    FROM V_WMS_OUT_SNCODE_ORDER A
    <if test="operateType == 0 ">
      <!-- 销售出库 -->
      LEFT JOIN T_SALEORDER_GOODS B ON A.ORDER_GOODS_ID = B.SALEORDER_GOODS_ID AND A.OPERATE_TYPE=0
      LEFT JOIN T_SALEORDER U ON B.SALEORDER_ID = U.SALEORDER_ID
      LEFT JOIN V_WMS_IN_SNCODE_ORDER D ON A.SN_CODE = D.SN_CODE AND D.OPERATE_TYPE = 0
      LEFT JOIN T_BUYORDER_GOODS E ON D.ORDER_GOODS_ID = E.BUYORDER_GOODS_ID
      LEFT JOIN V_WMS_IN_SNCODE_ORDER D1 ON A.SN_CODE = D1.SN_CODE AND D1.OPERATE_TYPE = 2 AND D.ORDER_GOODS_ID is null
      LEFT JOIN T_AFTER_SALES_GOODS AFG ON D1.ORDER_GOODS_ID = AFG.AFTER_SALES_GOODS_ID
      LEFT JOIN T_AFTER_SALES AF ON AFG.AFTER_SALES_ID = AF.AFTER_SALES_ID AND AF.SUBJECT_TYPE = 535
      LEFT JOIN T_BUYORDER BU ON AF.ORDER_ID = BU.BUYORDER_ID
      LEFT JOIN T_BUYORDER_GOODS BUG ON BU.BUYORDER_ID = BUG.BUYORDER_ID
    </if>
    <if test="operateType == 1">
      <!-- 销售售后出库 -->
      LEFT JOIN T_AFTER_SALES_GOODS B ON A.ORDER_GOODS_ID = B.AFTER_SALES_GOODS_ID AND A.OPERATE_TYPE =1
      LEFT JOIN T_SALEORDER_GOODS SA ON B.ORDER_DETAIL_ID = SA.SALEORDER_GOODS_ID
      LEFT JOIN T_AFTER_SALES C ON B.AFTER_SALES_ID = C.AFTER_SALES_ID AND C.SUBJECT_TYPE = 535
      LEFT JOIN T_SALEORDER U ON C.ORDER_ID = U.SALEORDER_ID
      LEFT JOIN V_WMS_IN_SNCODE_ORDER D ON A.SN_CODE = D.SN_CODE AND D.OPERATE_TYPE = 0
      LEFT JOIN T_BUYORDER_GOODS E ON D.ORDER_GOODS_ID = E.BUYORDER_GOODS_ID
      LEFT JOIN V_WMS_IN_SNCODE_ORDER D1 ON A.SN_CODE = D1.SN_CODE AND D1.OPERATE_TYPE = 2 AND D.ORDER_GOODS_ID is null
      LEFT JOIN T_AFTER_SALES_GOODS AFG ON D.ORDER_GOODS_ID = AFG.AFTER_SALES_GOODS_ID
      LEFT JOIN T_AFTER_SALES AF ON AFG.AFTER_SALES_ID = AF.AFTER_SALES_ID AND AF.SUBJECT_TYPE = 535
      LEFT JOIN T_BUYORDER BU ON AF.ORDER_ID = BU.BUYORDER_ID
      LEFT JOIN T_BUYORDER_GOODS BUG ON BU.BUYORDER_ID = BUG.BUYORDER_ID
    </if>
    <if test="operateType == 2">
      <!-- 采购售后出库 -->
      LEFT JOIN T_AFTER_SALES_GOODS B ON A.ORDER_GOODS_ID = B.AFTER_SALES_GOODS_ID AND A.OPERATE_TYPE =2
      LEFT JOIN T_AFTER_SALES C ON B.AFTER_SALES_ID = C.AFTER_SALES_ID AND C.SUBJECT_TYPE = 536
      LEFT JOIN T_BUYORDER_GOODS E ON B.ORDER_DETAIL_ID = E.BUYORDER_GOODS_ID AND E.IS_DELETE = 0
      LEFT JOIN T_BUYORDER U ON E.BUYORDER_ID = U.BUYORDER_ID
    </if>
    <if test="operateType == 3">
      <!-- 借货出库 -->
      LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS X ON A.ORDER_GOODS_ID = X.ID AND A.OPERATE_TYPE = 3
      LEFT JOIN T_WMS_OUTPUT_ORDER Y ON X.wms_output_order_id = Y.id
      LEFT JOIN V_WMS_IN_SNCODE_ORDER D ON A.SN_CODE = D.SN_CODE AND D.OPERATE_TYPE = 0
      LEFT JOIN T_BUYORDER_GOODS E ON D.ORDER_GOODS_ID = E.BUYORDER_GOODS_ID
      LEFT JOIN V_WMS_IN_SNCODE_ORDER D1 ON A.SN_CODE = D1.SN_CODE AND D1.OPERATE_TYPE = 2 AND D.ORDER_GOODS_ID is null
      LEFT JOIN T_AFTER_SALES_GOODS AFG ON D.ORDER_GOODS_ID = AFG.AFTER_SALES_GOODS_ID
      LEFT JOIN T_AFTER_SALES AF ON AFG.AFTER_SALES_ID = AF.AFTER_SALES_ID AND AF.SUBJECT_TYPE = 535
      LEFT JOIN T_BUYORDER BU ON AF.ORDER_ID = BU.BUYORDER_ID
      LEFT JOIN T_BUYORDER_GOODS BUG ON BU.BUYORDER_ID = BUG.BUYORDER_ID
    </if>
    <if test="operateType == 4">
      <!-- 报废出库 -->
      LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS X ON A.ORDER_GOODS_ID = X.ID AND A.OPERATE_TYPE = 4
      LEFT JOIN T_WMS_OUTPUT_ORDER Y ON X.wms_output_order_id = Y.id
      LEFT JOIN V_WMS_IN_SNCODE_ORDER D ON A.SN_CODE = D.SN_CODE AND D.OPERATE_TYPE = 0
      LEFT JOIN T_BUYORDER_GOODS E ON D.ORDER_GOODS_ID = E.BUYORDER_GOODS_ID
      LEFT JOIN V_WMS_IN_SNCODE_ORDER D1 ON A.SN_CODE = D1.SN_CODE AND D1.OPERATE_TYPE = 2 AND D.ORDER_GOODS_ID is null
      LEFT JOIN T_AFTER_SALES_GOODS AFG ON D.ORDER_GOODS_ID = AFG.AFTER_SALES_GOODS_ID
      LEFT JOIN T_AFTER_SALES AF ON AFG.AFTER_SALES_ID = AF.AFTER_SALES_ID AND AF.SUBJECT_TYPE = 535
      LEFT JOIN T_BUYORDER BU ON AF.ORDER_ID = BU.BUYORDER_ID
      LEFT JOIN T_BUYORDER_GOODS BUG ON BU.BUYORDER_ID = BUG.BUYORDER_ID
    </if>
    LEFT JOIN V_CORE_SKU F ON A.SKU = F.SKU_NO
    LEFT JOIN V_CORE_SPU G ON F.SPU_ID = G.SPU_ID
    LEFT JOIN T_SYS_OPTION_DEFINITION H ON G.SPU_TYPE = H.SYS_OPTION_DEFINITION_ID
    LEFT JOIN T_FIRST_ENGAGE I ON G.FIRST_ENGAGE_ID = I.FIRST_ENGAGE_ID
    LEFT JOIN T_REGISTRATION_NUMBER J ON I.REGISTRATION_NUMBER_ID = J .REGISTRATION_NUMBER_ID
    LEFT JOIN T_PRODUCT_COMPANY K ON J.PRODUCT_COMPANY_ID = K.PRODUCT_COMPANY_ID
    where A.OUT_SNCODE_ORDER_ID = #{outSncodeOrderId,jdbcType=INTEGER}
  </select>


</mapper>