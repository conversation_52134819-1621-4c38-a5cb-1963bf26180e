package com.vedeng.erp.finance.service;


import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.enums.CheckChainEnum;
import com.vedeng.erp.finance.enums.CheckHandlerEnum;

import java.math.BigDecimal;
import java.util.Objects;

public abstract class AbstractCheckHandler {

    public AbstractCheckHandler nextHandler;

    public boolean hasNextHandler() {
        return Objects.nonNull(this.nextHandler);
    }

    public CheckHandlerEnum getCheckHandlerEnum() {
        return CheckHandlerEnum.getCheckHandlerEnumByClazz(this.getClass());
    }

    public void buildResult(InvoiceCheckResultDto invoiceCheckResultDto, String promptText) {
        CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
        invoiceCheckResultDto.addDetail(buildResultDetail(checkHandlerEnum, promptText));
        invoiceCheckResultDto.setFail();
    }

    public InvoiceCheckResultDto.InvoiceCheckResultDetailDto buildResultDetail(CheckHandlerEnum checkHandlerEnum, String promptText) {
        InvoiceCheckResultDto.InvoiceCheckResultDetailDto invoiceCheckResultDetailDto = new InvoiceCheckResultDto.InvoiceCheckResultDetailDto();
        invoiceCheckResultDetailDto.setRuleCode(checkHandlerEnum.getRuleCode());
        invoiceCheckResultDetailDto.setRuleName(checkHandlerEnum.getRuleName());
        invoiceCheckResultDetailDto.setRuleContent(checkHandlerEnum.getRuleContent());
        invoiceCheckResultDetailDto.setPromptText(promptText);
        return invoiceCheckResultDetailDto;
    }

    public BigDecimal getDetailTotalAmount(InvoiceCheckRequestDto invoiceCheckRequestDto) {
        return invoiceCheckRequestDto.getDetailList().stream()
                .filter(Objects::nonNull)
                .map(InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto::getTotalAmount)
                .filter(totalAmount -> Objects.nonNull(totalAmount) && totalAmount.compareTo(BigDecimal.ZERO) > 0)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public InvoiceCheckResultDto processHandler(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto){
        CheckChainEnum checkChainEnum = invoiceCheckRequestDto.getCheckChainEnum();
        if (CheckChainEnum.isSales(checkChainEnum)) {
            handleSalesCheck(invoiceCheckRequestDto, invoiceCheckResultDto);
        } else if (CheckChainEnum.isAfter(checkChainEnum)) {
            handleAfterCheck(invoiceCheckRequestDto, invoiceCheckResultDto);
        }
        return hasNextHandler() ? nextHandler.processHandler(invoiceCheckRequestDto, invoiceCheckResultDto) : invoiceCheckResultDto;
    }

    public abstract void handleSalesCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto);

    public abstract void handleAfterCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto);

}
