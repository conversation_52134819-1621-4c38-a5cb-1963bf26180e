package com.vedeng.erp.aftersale.service;

import com.vedeng.orderstream.aftersales.model.AfterSalesExpenditureRecord;

import java.util.List;

/**
 * <AUTHOR> simgo.wang
 * @date : 2021/10/13 20:21
 * @desc :
 */
public interface AfterSalesExpenditureRecordService {

    /**
     * 新增支出记录
     * @param afterSalesExpenditureRecord
     * @return
     */
    int insert(AfterSalesExpenditureRecord afterSalesExpenditureRecord);

    /**
     * 查询支出记录
     * @param afterSalesId
     * @return
     */
    List<AfterSalesExpenditureRecord> selectByAfterSalesId(Integer afterSalesId);
}
