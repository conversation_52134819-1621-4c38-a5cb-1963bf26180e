package com.wms.dao;
import org.apache.ibatis.annotations.Param;
import java.util.List;


import com.wms.dto.WmsInOrderDto;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/12/11 9:05
 **/
public interface WmsInOrderMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(WmsInOrderDto record);

    int insertSelective(WmsInOrderDto record);

    WmsInOrderDto selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(WmsInOrderDto record);

    int updateByPrimaryKey(WmsInOrderDto record);

    List<WmsInOrderDto> selectByWmsNo(@Param("wmsNo")String wmsNo);


}