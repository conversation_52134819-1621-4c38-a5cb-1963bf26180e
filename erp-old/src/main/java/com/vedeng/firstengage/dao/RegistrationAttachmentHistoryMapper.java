package com.vedeng.firstengage.dao;

import com.vedeng.firstengage.model.RegistrationAttachmentHistory;
import com.vedeng.firstengage.model.RegistrationAttachmentHistoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface RegistrationAttachmentHistoryMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    int countByExample(RegistrationAttachmentHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    int deleteByExample(RegistrationAttachmentHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    int deleteByPrimaryKey(Integer reigistrationAttachmentHistoryId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    int insert(RegistrationAttachmentHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    int insertSelective(RegistrationAttachmentHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    List<RegistrationAttachmentHistory> selectByExample(RegistrationAttachmentHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    RegistrationAttachmentHistory selectByPrimaryKey(Integer reigistrationAttachmentHistoryId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    int updateByExampleSelective(@Param("record") RegistrationAttachmentHistory record, @Param("example") RegistrationAttachmentHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    int updateByExample(@Param("record") RegistrationAttachmentHistory record, @Param("example") RegistrationAttachmentHistoryExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    int updateByPrimaryKeySelective(RegistrationAttachmentHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REIGISTRATION_ATTACHMENT_HISTORY
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    int updateByPrimaryKey(RegistrationAttachmentHistory record);

    List<RegistrationAttachmentHistory> getListByRegistrationNumberId(@Param("numberId")Integer numberId);
}