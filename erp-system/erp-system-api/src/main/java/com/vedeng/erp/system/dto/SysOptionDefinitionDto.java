package com.vedeng.erp.system.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: TODO
 * <AUTHOR>
 * @date 2022/7/15 14:46
 * @version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysOptionDefinitionDto {

    /**
     * 字典项id
     */
    private Integer sysOptionDefinitionId;

    /**
     * 启用 1启用 0禁用
     */
    private Integer status;

    /**
     * 字典项名称
     */
    private String title;

    /**
     * 父级id
     */
    private Integer parentId;
    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String comments;

    public SysOptionDefinitionDto(Integer sysOptionDefinitionId, String title, Integer parentId, Integer sort) {
        this.sysOptionDefinitionId = sysOptionDefinitionId;
        this.title = title;
        this.parentId = parentId;
        this.sort = sort;
    }
}