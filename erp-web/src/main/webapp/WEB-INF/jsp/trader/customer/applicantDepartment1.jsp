<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="title" value="申请人/部门信用记录" scope="application" />
<%@ include file="../../common/common.jsp"%>
<style>
    .bottom-c{
        bottom: 10px;
        right: 0px;
        left: 0px;
    }
    .bottom-bottom{
        position: fixed;
        bottom: 0px;
        right: 0px;
    }
</style>
<div>

    <div class="layui-layer-title">申请人/部门信用记录</div>
    <div class="form-list  form-tips8 trader-customer-accountperiodapply">
        <div>
            <form action="" method="post" id="search" action="<%=basePath%>trader/customer/applicantDepartment.do">

                <div class="infor_name" style="width: auto;">开始时间：</div>
                <input class="Wdate f_left input-smaller96 mr5" type="text" placeholder="请选择日期" autocomplete="off"
                       onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'searchEndtimeStr\')}'})" name="startTime"
                       id="searchBegintimeStr" value='<date:date value ="${startTime}" format="yyyy-MM-dd"/>' />

                <div class="infor_name">截至时间：</div>
                <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off"
                       onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'searchBegintimeStr\')}'})" name="endTime"
                       id="searchEndtimeStr" value='<date:date value ="${endTime}" format="yyyy-MM-dd"/>' />
<%--                <input type="hidden" value="${billPeriodAppliers}" id="billPeriodAppliers"/>--%>
                <%--开始时间  截至时间 搜索--%>
                <div class="f_left" style="margin: -2px 10px 0px 20px;">
                    <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="search()">搜索</span>
                </div>

            </form>
        </div>
        <div style="height: 10px;"></div>
        <div class="title-container" style="width: 100%">
            <div class="table-title nobor">归属销售名:  ${saleName}</div>
        </div>
        <tags:CustomerBillPeriodHistoryView saleHistory="${saleHistory}" totalHistory="${totalHistory}" />

        <c:forEach items="${departHistoryAndTotal}" var="depart">
            <div class="title-container">
                <div class="table-title nobor">部门名: ${depart.key}</div>
            </div>
            <tags:CustomerBillPeriodHistoryView saleHistory="${depart.value.departHistory}"
                                                totalHistory="${depart.value.departTotalHistory.get(0)}" />
        </c:forEach>

        <div style="height: 30px;"></div>

    </div>
    <div class="add-tijiao pt25 layui-layer-title bottom-bottom" style="width: 100%;">
        <button class="bt-small bt-bg-style bt-middle bg-light-blue bottom-c"  id="close-layer" type="button" onclick="closeGoBack();">确定</button>
    </div>
</div>
<script type="text/javascript">
    function closeGoBack() {
        window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'),window.parent.closableTab.resizeMove);
    }
</script>
<%@ include file="../../common/footer.jsp"%>