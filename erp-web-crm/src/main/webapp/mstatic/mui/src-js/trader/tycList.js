/* 天眼查列表 */
Vue.component('ui-tyc-list', {
    template: `
        <crm-slide-dialog ref="tycListDialog" title="客户名称" :refresh="true">
            <div class="tyc-list-panel">

                <div class="tyc-list-wrap" @click.stop>
                    <div class="tyc-list" v-if="tycList.length">
                        <div 
                            class="tyc-item" 
                            v-for="(item, index) in tycList" :key="index"
                            @click.stop="chooseThis(item)"
                            :class="{'active': item.id == chooseTyc.id }"
                        >
                            <div class="tyc-select">
                                <i v-if="item.id == chooseTyc.id" class="vd-ui_icon icon-radio3"></i>
                                <i v-else class="vd-ui_icon icon-radio1"></i>
                            </div>
                            <div class="item-right">
                                <p class="name" v-html="lightName(item)"></p>
                                <div class="attrs">
                                    <div class="attr-item">
                                        <font class="label">机构类型：</font>{{ item.companyType | filterCompanyType }}
                                    </div>
                                    <div class="attr-item">
                                        <font class="label">企业法人：</font>{{ item.legalPersonName }}
                                    </div>
                                    <div class="attr-item">
                                        <font class="label">地区省份：</font>{{ item.base }}
                                    </div>
                                    <div class="attr-item">
                                        <font class="label">成立日期：</font>{{ item.estiblishTime | filterDateTime }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="erp-load-empty" v-else>
                        <img src="/mstatic/image/empty/search-empty.svg"/>
                        <p>抱歉，未能匹配到天眼查公司数据，</p>
                        <p>建议您检查关键词重新撞索</p>
                    </div>
                </div>

                <div class="slide-dialog-default-footer">
                    <div class="btn-cancel-flex">
                        <vd-ui-button @click="handlerCancel">取消</vd-ui-button>
                    </div>
                    <div class="btn-confirm-flex">
                        <vd-ui-button type="primary" @click="handlerConfirm">确定</vd-ui-button>
                    </div>
                </div>
            </div>
        </crm-slide-dialog>
    `,
    props: {
       
    },
    data() {
        return {
            searchName: '', // 客户名称
            tycList: [], // 天眼查列表
            chooseTyc: {}, // 选择的公司
            canAjax: true,
            loading: false,
        };
    },
    
    filters: {
        filterDateTime (val) {
            if (/^\d{4}-\d{2}-\d{2}/.test(val)) {
                return val.substr(0, 10);
            } else {
                return '-'
            }
        },
        filterCompanyType (val) {
            let TYPES = {
                1: '公司',
                2: '香港企业',
                3: '社会组织',
                4: '律所',
                5: '事业单位',
                6: '基金会',
                7: '不存在法人、注册资本、统一社会信用代码、经营状态',
                8: '台湾企业',
                9: '新机构',
            }
            return TYPES[val] || ''
        }
    },
    computed: {
        // 高亮
        lightName () {
            return (item) => {
                let name = item.name;
                if (!this.searchName) return name;
                const regExp = new RegExp(this.searchName, 'g');
                name = name.replace(regExp, `<font color='#FF6600'>${this.searchName}</font>`);
                return name;
            }
        },
    },
    mounted() {
        
    },
    methods: {
        show (key) {
            this.searchName = key || '';
            this.getList();
            this.$refs.tycListDialog.show();
        },

        getList() {
            this.canAjax = false;
            this.loading = true;

            this.$axios.post(`/crm/trader/m/queryTycList?traderName=${this.searchName}`).then(({data}) => {
                if (data.success) {
                    this.canAjax = true;
                    this.tycList = data.data || [];
                } else {
                    this.$message.warn(data.message || '网络异常');
                    this.canAjax = true;
                }
                this.loading = false;
            }).catch(err=> {
                this.loading = false;
            })
        },

        // 选择天眼查公司
        async chooseThis(item) {
            this.chooseTyc = item;
        },

        // 确定
        handlerConfirm () {
            let emitData = Object.assign({}, this.chooseTyc, {
                tycFlag: 'Y',
                traderId: '',
                saleName: '',
                traderName: this.chooseTyc.name || ''
            });
            this.$emit('change', emitData);

            setTimeout(()=> {
                this.handlerCancel();
            }, 30);
        },
        // 取消
        handlerCancel () {
            this.$refs.tycListDialog.hide();
            this.tycList = [];
            this.chooseTyc = {};
        },
    }
})