package com.wms.service.other;

import com.wms.constant.WMSContant;
import com.wms.dto.PutPathDto;
import com.wms.dto.WmsRequest;
import com.wms.service.AbstractWmsInterface;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * 下传发票图片和随货出库单图片 接口
 */
@Service
public class PutPathDataInterface extends AbstractWmsInterface {

    @Override
    protected void setMethodAndMessageId(WmsRequest wmsRequest) {
        wmsRequest.setMethod(WMSContant.PUT_PATH_DATA);
        wmsRequest.setMessageId(WMSContant.PUT_PATH_DATA);
    }

    @Override
    protected JSONObject getXmlDate(Object... params) {

        if (params == null || params.length == 0) {
            return null;
        }

        JSONArray headArray = new JSONArray();

        Arrays.stream(params).forEach(paramItem -> {

            PutPathDto putPathDto = (PutPathDto) paramItem;

            JSONObject headItem = parsePutPathDtoJsonObject(putPathDto);

            headArray.add(headItem);
        });

        JSONObject headObject = new JSONObject();
        headObject.put("header", headArray);
        return headObject;
    }

    private JSONObject parsePutPathDtoJsonObject(PutPathDto putPathDto) {

        JSONObject headItem = new JSONObject();

        headItem.put("CustomerID", "VEDENG");
        headItem.put("WarehouseID", "NJ01");
        headItem.put("SOReference1", putPathDto.getSOReference1());
        headItem.put("OrderNo", putPathDto.getOrderNo());

        JSONArray detailArray = new JSONArray();
        putPathDto.getDetails().stream().forEach(putPathDetailDto -> {
            JSONObject detailItem = new JSONObject();
            detailItem.put("SOReference1", putPathDetailDto.getSOReference1());
            detailItem.put("OrderNo", putPathDetailDto.getOrderNo());
            detailItem.put("OrderlineNo", putPathDetailDto.getOrderlineNo());
            detailItem.put("Picture_type", putPathDetailDto.getPicture_type());
            detailItem.put("Picture_path", putPathDetailDto.getPicture_path());

            detailArray.add(detailItem);
        });
        headItem.put("details", detailArray);
        return headItem;
    }

    @Override
    protected <T> T parseResponseDate(JSONObject returnObject) {
        return null;
    }
}
