package com.vedeng.erp.system.api.impl;

import com.vedeng.erp.system.api.SyncOutInRelateApiService;
import com.vedeng.erp.system.dto.SyncOutInRelateDto;
import com.vedeng.erp.system.domain.entity.SyncOutInRelateEntity;
import com.vedeng.erp.system.service.SyncOutInRelateService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class SyncOutInRelateApiServiceImpl implements SyncOutInRelateApiService {

    @Autowired
    private SyncOutInRelateService syncOutInRelateService;

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return syncOutInRelateService.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(SyncOutInRelateDto record) {
        SyncOutInRelateEntity entity = new SyncOutInRelateEntity();
        BeanUtils.copyProperties(record, entity);
        return syncOutInRelateService.insert(entity);
    }

    @Override
    public int insertSelective(SyncOutInRelateDto record) {
        SyncOutInRelateEntity entity = new SyncOutInRelateEntity();
        BeanUtils.copyProperties(record, entity);
        return syncOutInRelateService.insertSelective(entity);
    }

    @Override
    public SyncOutInRelateDto selectByPrimaryKey(Integer id) {
        SyncOutInRelateEntity entity = syncOutInRelateService.selectByPrimaryKey(id);
        if (entity == null) {
            return null;
        }
        SyncOutInRelateDto dto = new SyncOutInRelateDto();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    @Override
    public int updateByPrimaryKeySelective(SyncOutInRelateDto record) {
        SyncOutInRelateEntity entity = new SyncOutInRelateEntity();
        BeanUtils.copyProperties(record, entity);
        return syncOutInRelateService.updateByPrimaryKeySelective(entity);
    }

    @Override
    public int updateByPrimaryKey(SyncOutInRelateDto record) {
        SyncOutInRelateEntity entity = new SyncOutInRelateEntity();
        BeanUtils.copyProperties(record, entity);
        return syncOutInRelateService.updateByPrimaryKey(entity);
    }

    @Override
    public List<SyncOutInRelateDto> selectByProcessStatus(Integer processStatus) {
        List<SyncOutInRelateEntity> entities = syncOutInRelateService.selectByProcessStatus(processStatus);
        return entities.stream().map(entity -> {
            SyncOutInRelateDto dto = new SyncOutInRelateDto();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SyncOutInRelateDto> selectByOutBusinessNo(String outBusinessNo) {
        List<SyncOutInRelateEntity> entities = syncOutInRelateService.selectByOutBusinessNo(outBusinessNo);
        return entities.stream().map(entity -> {
            SyncOutInRelateDto dto = new SyncOutInRelateDto();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SyncOutInRelateDto> selectByInBusinessNo(String inBusinessNo) {
        List<SyncOutInRelateEntity> entities = syncOutInRelateService.selectByInBusinessNo(inBusinessNo);
        return entities.stream().map(entity -> {
            SyncOutInRelateDto dto = new SyncOutInRelateDto();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        }).collect(Collectors.toList());
    }
} 