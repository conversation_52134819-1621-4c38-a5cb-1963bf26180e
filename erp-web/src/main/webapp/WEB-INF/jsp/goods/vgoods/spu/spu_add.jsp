<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>新增SPU</title>
    <link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
    <script src="/webjars/ezadmin/plugins/layui/layui.js" charset="utf-8"></script>
    <link rel="stylesheet"href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/steps/steps.min.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/spu/spu_edit.css?rnd=${resourceVersionKey}">
</head>

<body>
<form id="form_submit" class="J-form">
    <input type="hidden" id="spuId" name="spuId" value="${command.spuId}">
    <%--<input type="hidden" class="J-spu-level" value="${command.spuLevel}">--%>

    <div class="steps-wrap">
        <div class="form-title">
            新增SPU
            <div class="tip-wrap">
                <i class="vd-icon icon-info2">
                    <div class="tip arrow-left">
                        <div class="tip-con">
                            新增SPU需提前配置好分类、品牌、首营信息、科室&属性等信息。<br>
                            如未能找到对应选项，或者发现问题，请前往对应管理页面进行添加/编辑，或者联系商品组同事进行配置。
                        </div>
                        <span class="arrow arrow-out">
                                <span class="arrow arrow-in"></span>
                            </span>
                    </div>
                </i>
            </div>
        </div>

        <div class="vd-tip tip-red error-tips-wrapper" style="display: none">
            <i class="vd-tip-icon vd-icon icon-error2"></i>
            <div class="vd-tip-cnt error-tips"></div>
        </div>
        <div class="vd-tip tip-orange" id="first-step-tips">
            <i class="vd-tip-icon vd-icon icon-caution1"></i>
            <div class="vd-tip-cnt">SPU中的分级分档设置仅作为SKU新建时的参考，具体分级分档以SKU中设置的为准。</div>
        </div>
        <%--新建SPU steps--%>
        <div id="addSteps"></div>

        <%--新增spu时有指导教程--%>
        <div class="first-step base-form form-span-7">
            <div class="form-block">
                <div class="form-item">
                    <div class="form-label"><span class="must">*</span>归属分类：</div>
                    <div class="form-fields">
                        <div class="form-col col-6">
                            <%--todo 只有编辑时即用spu迁移需要临时禁用--%>
                            <c:choose>
                            <c:when test="${command.spuId!=null and command.categoryId != null and command.categoryId > 0}">
                            <div class="select J-category-select disabled" style="pointer-events: none;">
                                </c:when>
                                <c:otherwise>
                                <div class="select J-category-select">
                                    </c:otherwise>
                                    </c:choose>
                                    <%--<div class="select J-category-select  disabled">--%>
                                    <div class="select-title">
                                        <div class="select-selected">
                                            <span class="J-text">请选择商品分类</span>
                                        </div>
                                        <div class="select-arrow">
                                            <i class="vd-icon icon-down"></i>
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" name="categoryId" class="J-category-value">
                            </div>
                            <div class="form-fields-tip-blue" style="margin-top: 40px">
                                <div class="tip-wrap">
                                    <i class="vd-icon icon-info2"></i>
                                </div>
                                - 分类决定SKU商品的属性类型，请谨慎选择<br>&nbsp; &nbsp;&nbsp; &nbsp;- 如未找到对应分类，请前往<a
                                    tabTitle='{"num":"viewCategory","link":"/category/base/index.do","title":"分类管理", "random": "1"}'
                                    href="javascript:void(0);">分类管理</a>进行添加
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>商品等级：</div>
                        <div class="form-fields">
                            <select class="J-select-level" name="goodsLevelNo">
                            </select>
                            <div class="feedback-block" wrapfor="goodsLevelNo"></div>
                            <div class="form-fields-tip-blue"  style="display: none">
                            </div>
                        </div>
                    </div>

                    <div class="form-item">
                        <div class="form-label"><span class="must">*</span>商品档位：</div>
                        <div class="form-fields">
                            <select class="J-select-position" name="goodsPositionNo">
                            </select>
                            <div class="feedback-block" wrapfor="goodsPositionNo"></div>
                            <div class="form-fields-tip-blue" style="display: none">
                            </div>
                        </div>
                    </div>

                    <div class="form-fields">
                        <button type="button" class="btn btn-large close-spu-edit">取消</button>
                        <button type="button" class="btn btn-blue btn-large" style="margin-left: 10px" id="nextStepBtn">
                            下一步
                        </button>
                    </div>
                </div>
            </div>

        <div class="second-step base-form form-span-7" style="display: none">
            <%--基本信息--%>
            <div class="form-block">
                <div class="form-block-title">基本信息</div>
                <div class="form-item">
                    <div class="form-label"><span class="must">*</span>商品品牌：</div>
                    <div class="form-fields">
                        <div class="J-brand-wrap" data-name="${command.brandName}"></div>
                        <input type="hidden" name="brandId" class="J-brandId" value="${command.brandId}">
                        <div class="form-fields-tip">
                            - 如需<a
                                tabTitle='{"num":"viewCategory","link":"/firstengage/brand/addBrand.do","title":"新增品牌", "random": "1"}'
                                href="javascript:void(0);">新增品牌</a>，请前往品牌管理中添加；
                        </div>
                    </div>
                </div>

                <div class="form-item">
                    <div class="form-label"><span class="must">*</span>商品类型：</div>
                    <div class="form-fields">
                        <div class="input-radio">
                            <c:forEach var="spuType" items="${command.spuTypeList}" varStatus="status">
                                <label class="input-wrap">
                                    <input type="radio" class="J-prod-type" name="spuType"
                                           value="${spuType.sysOptionDefinitionId }" <c:if
                                            test="${command.spuType == spuType.sysOptionDefinitionId }">
                                           checked </c:if>>
                                    <span class="input-ctnr"></span>
                                    <c:out value="${spuType.title }"/>
                                </label>
                            </c:forEach>
                            <div class="tip-wrap" style="position: absolute; z-index: 10">
                                <i class="vd-icon icon-info2">
                                    <div class="tip arrow-left">
                                        <div class="tip-con">
                                            配件：仪器的附件<br>
                                            试剂：仪器配套的使用试剂<br>
                                            耗材：一次性使用的消耗品<br>
                                            设备：设备、仪器类
                                        </div>
                                        <span class="arrow arrow-out">
                                                <span class="arrow arrow-in"></span>
                                             </span>
                                    </div>
                                </i>
                            </div>
                        </div>
                        <div class="feedback-block" wrapfor="spuType"></div>
                    </div>
                </div>

<%--                <div class="form-item">--%>
<%--                    <div class="form-label"><span class="must">*</span>税收编码：</div>--%>
<%--                    <div class="form-fields">--%>
<%--                        <div class="form-col col-6">--%>
<%--                            <input type="text" placeholder="请手动输入或根据税收编码库选择" class="input-text" autocomplete="off" name="taxClassificationCode" valid-max="200" maxlength="19" value="${taxClassificationCode}">--%>
<%--                        </div>--%>
<%--                        <button id="openDialog"  class="btn btn-blue btn-large" type="button">税收编码库</button>--%>
<%--                    </div>--%>
<%--                    <div class="form-fields">--%>
<%--                        <div class="form-col col-6">--%>
<%--                            <input type="text" readonly placeholder="自动带出简称" class="input-text" autocomplete="off" name="taxCodeSimpleName" valid-max="200" value="${taxCodeSimpleName}">--%>
<%--                        </div>--%>
<%--                    </div>--%>
<%--                </div>--%>

                <div class="form-item">
                    <div class="form-label">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                        <span class="must">*</span>是否在《医疗器械分类目录》：
                    </div>
                    <div class="form-fields">
                        <div class="input-radio">
                            <label class="input-wrap">
                                <input type="radio" class="J-prod-type" name="medicalInstrumentCatalogIncluded"
                                       value="1" onclick="chooseNoMedicalType()"
                                <c:if test="${command.medicalInstrumentCatalogIncluded eq 1 }"> checked </c:if> >
                                <span class="input-ctnr"></span>是
                            </label>
                            <label class="input-wrap">
                                <input type="radio" class="J-prod-type" name="medicalInstrumentCatalogIncluded"
                                       value="0" onclick="chooseNoMedicalType()"
                                <c:if test="${not empty command.spuId and command.medicalInstrumentCatalogIncluded eq 0 }">
                                       checked </c:if>  >
                                <span class="input-ctnr"></span>否
                            </label>
                        </div>
                        <div class="feedback-block" wrapfor="medicalInstrumentCatalogIncluded"></div>
                    </div>
                </div>

                <div class="form-item firstType" <c:if test="${command.medicalInstrumentCatalogIncluded ne 0 }"> style="display: none"  </c:if>>
                    <div class="form-label"><span class="must">*</span>非医疗器械一级分类：</div>
                    <div class="form-fields">
                        <select class="J-select" name="noMedicalFirstType" id="noMedicalFirstType">
                            <option value="">请选择分类</option>
                            <option value="1">科研产品</option>
                            <option value="2">医疗器械附件产品</option>
                            <option value="3">其他非医疗器械</option>
                        </select>
                    </div>
                </div>

                <div class="form-item secondType" <c:if test="${command.medicalInstrumentCatalogIncluded ne 0 }"> style="display: none"  </c:if>>
                    <div class="form-label">非医疗器械二级分类：</div>
                    <div class="form-fields">
                        <select class="J-select" name="noMedicalSecondType" id="noMedicalSecondType">
                            <option value="0">请选择分类</option>
                            <option value="1">科研配件</option>
                            <option value="2">实验耗材</option>
                            <option value="3">仪器设备</option>
                        </select>
                    </div>
                </div>

                <div class="form-item">
                    <div class="form-label">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                        <span class="must">*</span>是否有注册证/备案凭证：
                    </div>
                    <div class="form-fields">
                        <div class="input-radio">
                            <label class="input-wrap">
                                <input type="radio" class="J-prod-type" name="hasRegistrationCert"
                                       onclick="registrationCertSwitch()" value="1"
                                       <c:if test="${command.hasRegistrationCert}">checked</c:if> >
                                <span class="input-ctnr"></span>有
                            </label>
                            <label class="input-wrap">
                                <input type="radio" class="J-prod-type" name="hasRegistrationCert"
                                       onclick="registrationCertSwitch()" value="0"
                                       <c:if test="${command.spuId ne null and !command.hasRegistrationCert}">checked</c:if> >
                                <span class="input-ctnr"></span>无
                            </label>
                        </div>
                        <div class="feedback-block" wrapfor="hasRegistrationCert"></div>
                    </div>
                </div>

                <div class="form-item  J-large-facility-block" <c:if
                        test="${command.spuType ne 316 or !command.hasRegistrationCert}"> style="display: none" </c:if> >
                    <div class="form-label"><span class="must">*</span>是否为大型医疗设备：</div>
                    <div class="form-fields">
                        <div class="input-radio">
                            <label class="input-wrap">
                                <input type="radio" class="J-prod-type" name="secondLevelSpuType" value="3161"
                                       <c:if test="${command.secondLevelSpuType eq 3161}">checked</c:if> >
                                <span class="input-ctnr"></span>是
                            </label>
                            <label class="input-wrap">
                                <input type="radio" class="J-prod-type" name="secondLevelSpuType" value="0" checked>
                                <span class="input-ctnr"></span>否
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-item J-first-block" <c:if
                        test="${!command.hasRegistrationCert}"> style="display: none" </c:if>  >
                    <div class="form-label"><span class="must">*</span>注册证/备案信息：</div>
                    <div class="form-fields">
                        <div class="select J-firstenage-select">
                            <div class="select-title">
                                <div class="select-selected">
                                    <span class="J-text">请选择注册证号/备案凭证号</span>
                                </div>
                                <div class="select-arrow">
                                    <i class="vd-icon icon-down"></i>
                                </div>
                            </div>
                        </div>
                        <input type="hidden"
                        <c:if test="${!command.hasRegistrationCert}"> disabled </c:if> name="firstEngageId"
                               class="J-firstenage-value"
                               value="${command.firstEngageId}">
                        <div class="form-fields-tip">
                            - 如需<a href="javascript:void(0);"
                                   tabTitle='{"num":"addProduct","link":"./firstengage/baseinfo/add.do","title":"新增注册证/备案信息", "random": "1"}'>新增注册证/备案信息</a>，请前往医疗器械注册管理中添加；
                        </div>
                        <div class="first-detail-wrap form-col col-11 J-first-detail"></div>
                    </div>
                </div>
                <div class="form-item J-registration-name">
                    <div class="form-label">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                        <span class="must">*</span>产品名称（注册证/备案凭证）：
                    </div>
                    <div class="form-fields">
                        <div class="form-col col-6">
                            <input type="text" class="input-text J-common-name" placeholder="请输入注册证/备案证上的产品名称"
                                   name="spuName" value="${command.spuName}">
                        </div>
                    </div>
                </div>
                <div class="form-item J-registration-specs">
                    <div class="form-label">
                        <img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="20px">
                        <span class="must">*</span>规格、型号（注册证/备案凭证）：
                    </div>
                    <div class="form-fields">
                        <div class="form-col col-6">
                            <input type="text" class="input-text J-common-name" placeholder="请输入注册证/备案凭证上的规格型号，以“、”区分"
                                   name="specsModel" value="${command.specsModel}">
                        </div>
                    </div>
                </div>
                <div class="form-item">
                    <div class="form-label"><span class="must">*</span>spu名称：</div>
                    <div class="form-fields">
                        <div class="form-col col-6">
                            <input type="text" class="input-text J-prod-name" placeholder="请输入SPU名称" name="showName"
                                   value="${command.showName}">
                        </div>
                        <c:if test="${command.spuLevel!=2}">
                            <div class="form-fields-tip">
                                - 对外销售的展示名，由“品牌+产品名称（注册证/备案凭证）组成”
                            </div>
                        </c:if>
                    </div>
                </div>
                <%--<c:if test="${ command.spuLevel ==2 && command.spuId ==null }">--%>
                <%--<div class="form-item J-prod-type-block" style="display: none">--%>
                <%--<div class="form-label"><span class="must">*</span><span class="J-prod-type-txt"></span>：</div>--%>
                <%--<div class="form-fields">--%>
                <%--<div class="form-col col-6">--%>
                <%--<input type="text" class="input-text" name="skuInfo" value="${command.skuInfo}" disabled>--%>
                <%--</div>--%>
                <%--</div>--%>
                <%--</div>--%>
                <%--</c:if>--%>
            </div>

            <%--非临时SPU才有属性信息--%>
            <%--<c:if test="${command.spuLevel!=2}">--%>
            <div class="form-block J-attr-wrap">
                <div class="form-block-title">属性信息</div>
                <div class="form-item">
                    <div class="form-label">选择带入分类属性：</div>
                    <div class="form-fields">
                        <div class="input-checkbox J-attr-list">
                            <c:forEach var="baseAttribute" items="${command.baseAttributes}" varStatus="status">
                                <label class="input-wrap">
                                    <input type="checkbox" name="baseAttributeIds"
                                    <c:if test="${baseAttribute.selected}"> checked </c:if>
                                           value="${baseAttribute.baseAttributeId}">
                                    <span class="input-ctnr"></span>${baseAttribute.baseAttributeName }
                                </label>
                            </c:forEach>
                        </div>
                    </div>
                </div>
                <div class="form-item J-main-attr-wrap">
                    <div class="form-label">选择主属性：</div>
                    <div class="form-fields">
                        <div class="input-checkbox J-main-attr-list">

                        </div>
                    </div>
                </div>
            </div>
            <%--</c:if>--%>

            <%--存储条件与效期--%>
            <div class="form-block">
                <div class="form-block-title">存储与效期</div>
                <%--存储条件,新增时带入所属spu的存储条件--%>
                <c:set var="goodsStorageConditionVo" value="${command.goodsStorageConditionVo}" scope="request"></c:set>
                <jsp:include page="../commons/spuStorageCondition.jsp" flush="true"></jsp:include>
            </div>

            <div class="form-block">
                <div class="form-block-title">产品资料</div>
                <div class="form-item">
                    <div class="form-label"><span class="must">*</span>Wiki链接：</div>
                    <div class="form-fields">
                        <div class="form-col col-6">
                            <input type="text" class="input-text" name="wikiHref" value="${command.wikiHref}">
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-block">
                <div class="form-block-title">产品归属</div>
                <div class="form-item">
                    <div class="form-label"><span class="must">*</span>归属产品经理：</div>
                    <div class="form-fields">
                        <select class="input-middle J-select assign" style="margin-top: 6px" name="assignmentManagerId">
                            <option value="">请选择</option>
                            <c:forEach var="list" items="${command.productOwnerUsers}">
                                <option value="${list.userId}"
                                        <c:if test="${command.assignmentManagerId == list.userId}">selected="selected"</c:if> >${list.username}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>

                <div class="form-item">
                    <div class="form-label"><span class="must">*</span>归属产品助理：</div>
                    <div class="form-fields">
                        <select class="input-middle J-select assign" style="margin-top: 6px"
                                name="assignmentAssistantId">
                            <option value="">请选择</option>
                            <c:forEach var="list" items="${command.productOwnerUsers}">
                                <option value="${list.userId}"
                                        <c:if test="${command.assignmentAssistantId == list.userId}">selected="selected"</c:if> >${list.username}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-btn">
                <div class="form-fields">
                    <button id="lastStepBtn" type="button" class="btn btn-large">上一步</button>
                    <button type="submit" class="btn btn-blue btn-large" style="margin-left: 10px">提交</button>
                </div>
            </div>
        </div>

        <div class="end-step" style="display: none">
            <div style="text-align: center">
                <img src="${pageContext.request.contextPath}/static/images/tick.png" width="100px">
            </div>
            <div style="text-align:center;font-size: 31px;">
                <b>创建成功</b>
            </div>
            <div class="first-detail-wrap form-col J-addSpu-detail" style="width:40%;margin-left:380px;">
                <div class="detail-item">
                    <div class="detail-label">SPU名称：</div>
                    <div class="detail-fields" id="viewSupName"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">所属分类：</div>
                    <div class="detail-fields" id="viewCategory"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">所属等级：</div>
                    <div class="detail-fields" id="viewLevel"></div>
                </div>
                <div class="detail-item">
                    <div class="detail-label">所属档位：</div>
                    <div class="detail-fields" id="viewPosition"></div>
                </div>
            </div>
            <div class="form-btn" style="text-align: center;margin-top: 10px">
                <div class="form-fields" >
                    <input type="hidden" id="spuIdAftSuccess">
                    <button type="button" class="btn btn-large" id="viewSpuBtn">立即查看</button>
                    <button type="button" class="btn btn-blue btn-large" style="margin-left: 10px" id="addSkuBtn">新增SKU</button>
                </div>
            </div>
        </div>

    </div>
    </div>

</form>

<div id="simulativeLink"></div>

<script type="text/tmpl" class="J-first-tmpl">
        <div class="detail-item">
            <div class="detail-label">证件有效期：</div>
            <div class="detail-fields">{{=effectStartDate}} - {{=effectEndDate}}</div>
        </div>
        <div class="detail-item">
            <div class="detail-label">管理类别：</div>
            <div class="detail-fields">{{=manageCategoryLevel || '--'}}</div>
        </div>
        <div class="detail-item">
            <div class="detail-label">生产厂商：</div>
            <div class="detail-fields">{{=productCompanyChineseName || '--'}}</div>
        </div>
        <div class="detail-item">
            <div class="detail-label">旧国标分类：</div>
            <div class="detail-fields">{{=oldStandardCategoryName || '--'}}</div>
        </div>
        {{ if(newStandardCategoryName){ }}
        <div class="detail-item">
            <div class="detail-label">新国标分类：</div>
            <div class="detail-fields">{{=newStandardCategoryName || '--'}}</div>
        </div>
        {{ } }}
        <div class="detail-item">
            <div class="detail-label">审核状态：</div>
            <div class="detail-fields">{{=checkStatus || '--'}}</div>
        </div>

</script>

<%--<script type="text/tmpl" class="J-addSuccess-tmpl">--%>
        <%--<div class="detail-item">--%>
            <%--<div class="detail-label">SPU名称：</div>--%>
            <%--<div class="detail-fields">{{=spuName || '--'}}</div>--%>
        <%--</div>--%>
        <%--<div class="detail-item">--%>
            <%--<div class="detail-label">所属分类：</div>--%>
            <%--<div class="detail-fields">{{=categoryName || '--'}}</div>--%>
        <%--</div>--%>
        <%--<div class="detail-item">--%>
            <%--<div class="detail-label">所属等级：</div>--%>
            <%--<div class="detail-fields">{{=goodsLevelName || '--'}}</div>--%>
        <%--</div>--%>
         <%--<div class="detail-item">--%>
            <%--<div class="detail-label">所属档位：</div>--%>
            <%--<div class="detail-fields">{{=goodsPositionName || '--'}}</div>--%>
        <%--</div>--%>

<%--</script>--%>
<%--<script>--%>
<%--    layui.use(['layer'], function() {--%>
<%--        var layer = layui.layer;--%>

<%--        document.getElementById('openDialog').onclick = function() {--%>
<%--            $.get('/category/base/taxCodePopup.do', function(htmlContent) {--%>
<%--                layer.open({--%>
<%--                    type: 0,--%>
<%--                    title: '税务编码分页列表查询',--%>
<%--                    content: htmlContent,--%>
<%--                    area: ['1200px', '800px'],--%>
<%--                    btn: ['确定', '取消'],--%>
<%--                    yes: function(index, layero) {--%>
<%--                        debugger--%>
<%--                        var table = layui.table;--%>
<%--                        var checkStatus = table.checkStatus('dataTable');--%>
<%--                        var data = checkStatus.data;--%>
<%--                        // 如果没有选中任何数据，返回--%>
<%--                        if (data.length === 0) {--%>
<%--                            layer.msg('请先选择一个税收编码');--%>
<%--                            return;--%>
<%--                        }--%>

<%--                        // 获取选中的第一条数据--%>
<%--                        var selectedData = data[0];--%>

<%--                        // 将选中的 code 赋值给税收编码的 input 框--%>
<%--                        document.getElementsByName('taxClassificationCode')[0].value = selectedData.finalCode.trim();--%>

<%--                        // 将选中的 simpleName 赋值给自动带出简称的 input 框--%>
<%--                        document.getElementsByName('taxCodeSimpleName')[0].value = selectedData.classificationAbbreviation;--%>

<%--                        // 关闭弹出层--%>
<%--                        layer.close(index);--%>
<%--                    }--%>
<%--                });--%>
<%--            });--%>
<%--        };--%>
<%--    });--%>
<%--</script>--%>

<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/pager.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/dialogSearch.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/dialogSelect.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/steps/steps.min.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/goods/vgoods/spu_edit.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/goods/commons/goodsCommon.js?rnd=${resourceVersionKey}"></script>
</body>
</html>