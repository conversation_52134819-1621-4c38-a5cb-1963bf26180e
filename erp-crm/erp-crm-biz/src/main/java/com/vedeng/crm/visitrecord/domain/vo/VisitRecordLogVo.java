package com.vedeng.crm.visitrecord.domain.vo;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/3
 */
import lombok.Data;
import java.util.Date;

@Data
public class VisitRecordLogVo {
    private Integer id;
    private Integer recordId;
    private Integer operationType;
    private String operationContent;
    private Date operationTime;
    private Date addTime;
    private Integer addUserId;
    private String addUserName;
}