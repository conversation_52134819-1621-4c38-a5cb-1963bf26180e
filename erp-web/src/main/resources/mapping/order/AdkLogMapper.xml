<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.order.dao.AdkLogMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.adk.AdkLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    <id column="adk_log_id" jdbcType="INTEGER" property="adkLogId" />
    <result column="adk_log_timestamp" jdbcType="BIGINT" property="adkLogTimestamp" />
    <result column="adk_log_ak" jdbcType="VARCHAR" property="adkLogAk" />
    <result column="adk_log_sig" jdbcType="VARCHAR" property="adkLogSig" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="adk_log_status" jdbcType="VARCHAR" property="adkLogStatus" />
    <result column="adk_log_msg" jdbcType="VARCHAR" property="adkLogMsg" />
    <result column="adk_log_type" jdbcType="VARCHAR" property="adkLogType" />
    <result column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="adk_order_no" jdbcType="VARCHAR" property="adkOrderNo" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.vedeng.order.model.adk.AdkLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    <result column="adk_log_param" jdbcType="LONGVARCHAR" property="adkLogParam" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    adk_log_id, adk_log_timestamp, adk_log_ak, adk_log_sig, add_time, update_time, adk_log_status, 
    adk_log_msg, adk_log_type, request_id, adk_order_no
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    adk_log_param
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.vedeng.order.model.adk.AdkLogExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_ADK_LOG
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.vedeng.order.model.adk.AdkLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_ADK_LOG
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_ADK_LOG
    where adk_log_id = #{adkLogId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    delete from T_ADK_LOG
    where adk_log_id = #{adkLogId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.order.model.adk.AdkLogExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    delete from T_ADK_LOG
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.order.model.adk.AdkLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    <selectKey keyProperty="adkLogId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_ADK_LOG (adk_log_timestamp, adk_log_ak, adk_log_sig, 
      add_time, update_time, adk_log_status, 
      adk_log_msg, adk_log_type, request_id, 
      adk_order_no, adk_log_param)
    values (#{adkLogTimestamp,jdbcType=BIGINT}, #{adkLogAk,jdbcType=VARCHAR}, #{adkLogSig,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{adkLogStatus,jdbcType=VARCHAR}, 
      #{adkLogMsg,jdbcType=VARCHAR}, #{adkLogType,jdbcType=VARCHAR}, #{requestId,jdbcType=VARCHAR}, 
      #{adkOrderNo,jdbcType=VARCHAR}, #{adkLogParam,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.order.model.adk.AdkLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    <selectKey keyProperty="adkLogId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_ADK_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adkLogTimestamp != null">
        adk_log_timestamp,
      </if>
      <if test="adkLogAk != null">
        adk_log_ak,
      </if>
      <if test="adkLogSig != null">
        adk_log_sig,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="adkLogStatus != null">
        adk_log_status,
      </if>
      <if test="adkLogMsg != null">
        adk_log_msg,
      </if>
      <if test="adkLogType != null">
        adk_log_type,
      </if>
      <if test="requestId != null">
        request_id,
      </if>
      <if test="adkOrderNo != null">
        adk_order_no,
      </if>
      <if test="adkLogParam != null">
        adk_log_param,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adkLogTimestamp != null">
        #{adkLogTimestamp,jdbcType=BIGINT},
      </if>
      <if test="adkLogAk != null">
        #{adkLogAk,jdbcType=VARCHAR},
      </if>
      <if test="adkLogSig != null">
        #{adkLogSig,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adkLogStatus != null">
        #{adkLogStatus,jdbcType=VARCHAR},
      </if>
      <if test="adkLogMsg != null">
        #{adkLogMsg,jdbcType=VARCHAR},
      </if>
      <if test="adkLogType != null">
        #{adkLogType,jdbcType=VARCHAR},
      </if>
      <if test="requestId != null">
        #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="adkOrderNo != null">
        #{adkOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="adkLogParam != null">
        #{adkLogParam,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.order.model.adk.AdkLogExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    select count(*) from T_ADK_LOG
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    update T_ADK_LOG
    <set>
      <if test="record.adkLogId != null">
        adk_log_id = #{record.adkLogId,jdbcType=INTEGER},
      </if>
      <if test="record.adkLogTimestamp != null">
        adk_log_timestamp = #{record.adkLogTimestamp,jdbcType=BIGINT},
      </if>
      <if test="record.adkLogAk != null">
        adk_log_ak = #{record.adkLogAk,jdbcType=VARCHAR},
      </if>
      <if test="record.adkLogSig != null">
        adk_log_sig = #{record.adkLogSig,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.adkLogStatus != null">
        adk_log_status = #{record.adkLogStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.adkLogMsg != null">
        adk_log_msg = #{record.adkLogMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.adkLogType != null">
        adk_log_type = #{record.adkLogType,jdbcType=VARCHAR},
      </if>
      <if test="record.requestId != null">
        request_id = #{record.requestId,jdbcType=VARCHAR},
      </if>
      <if test="record.adkOrderNo != null">
        adk_order_no = #{record.adkOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.adkLogParam != null">
        adk_log_param = #{record.adkLogParam,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    update T_ADK_LOG
    set adk_log_id = #{record.adkLogId,jdbcType=INTEGER},
      adk_log_timestamp = #{record.adkLogTimestamp,jdbcType=BIGINT},
      adk_log_ak = #{record.adkLogAk,jdbcType=VARCHAR},
      adk_log_sig = #{record.adkLogSig,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      adk_log_status = #{record.adkLogStatus,jdbcType=VARCHAR},
      adk_log_msg = #{record.adkLogMsg,jdbcType=VARCHAR},
      adk_log_type = #{record.adkLogType,jdbcType=VARCHAR},
      request_id = #{record.requestId,jdbcType=VARCHAR},
      adk_order_no = #{record.adkOrderNo,jdbcType=VARCHAR},
      adk_log_param = #{record.adkLogParam,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    update T_ADK_LOG
    set adk_log_id = #{record.adkLogId,jdbcType=INTEGER},
      adk_log_timestamp = #{record.adkLogTimestamp,jdbcType=BIGINT},
      adk_log_ak = #{record.adkLogAk,jdbcType=VARCHAR},
      adk_log_sig = #{record.adkLogSig,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      adk_log_status = #{record.adkLogStatus,jdbcType=VARCHAR},
      adk_log_msg = #{record.adkLogMsg,jdbcType=VARCHAR},
      adk_log_type = #{record.adkLogType,jdbcType=VARCHAR},
      request_id = #{record.requestId,jdbcType=VARCHAR},
      adk_order_no = #{record.adkOrderNo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.adk.AdkLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    update T_ADK_LOG
    <set>
      <if test="adkLogTimestamp != null">
        adk_log_timestamp = #{adkLogTimestamp,jdbcType=BIGINT},
      </if>
      <if test="adkLogAk != null">
        adk_log_ak = #{adkLogAk,jdbcType=VARCHAR},
      </if>
      <if test="adkLogSig != null">
        adk_log_sig = #{adkLogSig,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adkLogStatus != null">
        adk_log_status = #{adkLogStatus,jdbcType=VARCHAR},
      </if>
      <if test="adkLogMsg != null">
        adk_log_msg = #{adkLogMsg,jdbcType=VARCHAR},
      </if>
      <if test="adkLogType != null">
        adk_log_type = #{adkLogType,jdbcType=VARCHAR},
      </if>
      <if test="requestId != null">
        request_id = #{requestId,jdbcType=VARCHAR},
      </if>
      <if test="adkOrderNo != null">
        adk_order_no = #{adkOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="adkLogParam != null">
        adk_log_param = #{adkLogParam,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where adk_log_id = #{adkLogId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vedeng.order.model.adk.AdkLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    update T_ADK_LOG
    set adk_log_timestamp = #{adkLogTimestamp,jdbcType=BIGINT},
      adk_log_ak = #{adkLogAk,jdbcType=VARCHAR},
      adk_log_sig = #{adkLogSig,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      adk_log_status = #{adkLogStatus,jdbcType=VARCHAR},
      adk_log_msg = #{adkLogMsg,jdbcType=VARCHAR},
      adk_log_type = #{adkLogType,jdbcType=VARCHAR},
      request_id = #{requestId,jdbcType=VARCHAR},
      adk_order_no = #{adkOrderNo,jdbcType=VARCHAR},
      adk_log_param = #{adkLogParam,jdbcType=LONGVARCHAR}
    where adk_log_id = #{adkLogId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.order.model.adk.AdkLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sun May 05 10:38:13 CST 2019.
    -->
    update T_ADK_LOG
    set adk_log_timestamp = #{adkLogTimestamp,jdbcType=BIGINT},
      adk_log_ak = #{adkLogAk,jdbcType=VARCHAR},
      adk_log_sig = #{adkLogSig,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      adk_log_status = #{adkLogStatus,jdbcType=VARCHAR},
      adk_log_msg = #{adkLogMsg,jdbcType=VARCHAR},
      adk_log_type = #{adkLogType,jdbcType=VARCHAR},
      request_id = #{requestId,jdbcType=VARCHAR},
      adk_order_no = #{adkOrderNo,jdbcType=VARCHAR}
    where adk_log_id = #{adkLogId,jdbcType=INTEGER}
  </update>
  
  
  <select id="selectAllDelivedOrder" resultType="java.lang.Integer">
    select a.erp_order_id from T_ADK_ORDER_LOG a,T_SALEORDER b
	where b.SALEORDER_ID=a.erp_order_id
	and a.DELIVERY_STATUS='0'
	and b.DELIVERY_STATUS='2'
  </select>
  
   <select id="selectGoodsOutBefore30Min" resultType="java.util.Map">
	SELECT
	ag.ADK_GOODS_CODE,
	ag.ADK_GOODS_NAME,
	uu.UNIT_NAME,
	tsg.DELIVERY_NUM as NUM,
	tw.BATCH_NUMBER,
	from_unixtime(tw.EXPIRATION_DATE/1000,'%Y-%m-%d') as EXPIRATION_DATE,
	tg.MANUFACTURER,
	tg.PRODUCTION_LICENSE,
	tg.LICENSE_NUMBER
	, 
	tg.RECORD_NUMBER
	 
	,if(tg.STORAGE_REQUIREMENTS=1,"常温","冷藏") as STORAGE_REQUIREMENTS 
,ts.ADK_SALEORDER_NO
	,ts.TRADER_NAME

FROM
	T_EXPRESS te
LEFT JOIN T_EXPRESS_DETAIL ted ON te.EXPRESS_ID = ted.EXPRESS_ID
LEFT JOIN T_R_EXPRESS_OPERATE_LOG trl ON trl.EXPRESS_DETAIL_ID = ted.EXPRESS_DETAIL_ID
LEFT JOIN T_WAREHOUSE_GOODS_OPERATE_LOG tw ON tw.WAREHOUSE_GOODS_OPERATE_LOG_ID = trl.WAREHOUSE_GOODS_OPERATE_LOG_ID
LEFT JOIN T_SALEORDER_GOODS tsg ON ted.RELATED_ID = tsg.SALEORDER_GOODS_ID
LEFT JOIN T_SALEORDER ts ON ts.SALEORDER_ID = tsg.SALEORDER_ID
LEFT JOIN T_GOODS tg ON tg.GOODS_ID = tw.GOODS_ID
LEFT JOIN T_ADK_GOODS ag on ag.erp_goods_id=tg.GOODS_ID
left join T_UNIT uu on  uu.UNIT_ID=tg.UNIT_ID
WHERE
	tw.IS_ENABLE = 1
AND ted.BUSINESS_TYPE = 496
AND tw.OPERATE_TYPE = 2
AND te.IS_ENABLE = 1
and ts.SOURCE=3
AND ts.ADK_SALEORDER_NO is not null
 and  tw.add_TIME > unix_timestamp(now())*1000-1000*60*30 
  </select>

<!--<select id="selectAllMeinianDeliveryStatus" resultType="com.meinian.model.vo.SendData">-->
<!--  SELECT O.M_SALEORDER_NO AS orderNumber-->
<!--  ,A.price AS price-->
<!--  ,B.BATCH_NUMBER AS batchNumber-->
<!--  ,B.COMMENTS AS remark,-->
<!--  ABS(B.NUM) AS supplyNumber,-->
<!--  IF(-->
<!--  G.GOODS_TYPE = 316,-->
<!--  B.BARCODE_FACTORY,-->
<!--  ''-->
<!--  ) AS serialNumber,-->
<!--  B.EXPIRATION_DATE AS batchExpiryTime-->
<!--,G.SKU AS  supplierNumber-->
<!--  FROM-->
<!--  T_SALEORDER O LEFT JOIN-->
<!--  T_SALEORDER_GOODS A  ON O.SALEORDER_ID=A.SALEORDER_ID-->
<!--  left join-->
<!--  T_WAREHOUSE_GOODS_OPERATE_LOG B ON A.SALEORDER_GOODS_ID=B.RELATED_ID-->
<!--  left join T_GOODS G ON A.GOODS_ID=G.GOODS_ID-->

<!--  WHERE A.MOD_TIME>   unix_timestamp(now())*1000-1000*60*60*24 AND A.DELIVERY_STATUS=2-->
<!--  AND O.M_SALEORDER_NO IS NOT NULL-->
<!--  AND B.OPERATE_TYPE IN(2)-->
<!--  </select>-->
<!--  <select id="selectAllMeinianByNo" resultType="com.meinian.model.vo.SendData">-->
<!--  SELECT O.M_SALEORDER_NO AS orderNumber-->
<!--  ,A.price AS price-->
<!--  ,B.BATCH_NUMBER AS batchNumber-->
<!--  ,B.COMMENTS AS remark,-->
<!--  ABS(B.NUM) AS supplyNumber,-->
<!--  IF(-->
<!--  G.GOODS_TYPE = 316,-->
<!--  B.BARCODE_FACTORY,-->
<!--  ''-->
<!--  ) AS serialNumber,-->
<!--  B.EXPIRATION_DATE AS batchExpiryTime-->
<!--,G.SKU AS  supplierNumber-->
<!--  FROM-->
<!--  T_SALEORDER O LEFT JOIN-->
<!--  T_SALEORDER_GOODS A  ON O.SALEORDER_ID=A.SALEORDER_ID-->
<!--  left join-->
<!--  T_WAREHOUSE_GOODS_OPERATE_LOG B ON A.SALEORDER_GOODS_ID=B.RELATED_ID-->
<!--  left join T_GOODS G ON A.GOODS_ID=G.GOODS_ID-->

<!--  WHERE  A.DELIVERY_STATUS=2-->
<!--  AND O.M_SALEORDER_NO-->
<!--  in-->
<!--    <foreach collection="mNos" item="meinianno" index="index"-->
<!--             open="(" close=")" separator=",">-->
<!--      #{meinianno,jdbcType=VARCHAR}-->
<!--    </foreach>-->
<!--  AND B.OPERATE_TYPE IN(2)-->
<!--  </select>-->
</mapper>