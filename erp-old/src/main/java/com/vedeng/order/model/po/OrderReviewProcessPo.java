package com.vedeng.order.model.po;

import lombok.Data;

/**
 * 订单相关审核流记录PO
 */
@Data
public class OrderReviewProcessPo {

    /**
     *主键
     */
    private Integer id;

    /**
     * 记录类型：0销售订单，1采购订单
     */
    private Integer type;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 业务类型键
     */
    private String businessKey;

    /**
     * 是否删除0否1是
     */
    private Integer isDelete;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 创建者
     */
    private Integer creator;

    /**
     * 最近一次修改时间
     */
    private Long modTime;

    /**
     * 最近一次更新人
     */
    private Integer updater;

    public OrderReviewProcessPo() {
    }

    public OrderReviewProcessPo(Integer type, Integer orderId, String businessKey, Long addTime, Integer creator) {
        this.type = type;
        this.orderId = orderId;
        this.businessKey = businessKey;
        this.addTime = addTime;
        this.creator = creator;
    }
}
