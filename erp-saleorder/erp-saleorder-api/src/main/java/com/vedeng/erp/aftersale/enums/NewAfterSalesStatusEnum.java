package com.vedeng.erp.aftersale.enums;

/**
 * 售后单-售后单状态
 * 售后单状态 0.待确认 1.进行中 2.已完结 3.已关闭'
 */
public enum NewAfterSalesStatusEnum {
    
    CONFIRM(0,"待确认"),
    PROCESSING(1,"进行中"),
    COMPLETED(2,"已完结"),
    CLOSED(3,"已关闭"),
    ;

    private Integer code;

    private String desc;


    NewAfterSalesStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    
    public static String getDescByCode(Integer code) {
        for (NewAfterSalesStatusEnum e : NewAfterSalesStatusEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getDesc();

            }
        }
        return "";
    }
    
}
